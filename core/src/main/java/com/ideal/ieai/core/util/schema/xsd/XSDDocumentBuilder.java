package com.ideal.ieai.core.util.schema.xsd;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.FactoryConfigurationError;
import javax.xml.parsers.ParserConfigurationException;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.ideal.ieai.core.element.IEAISchema;
import com.ideal.ieai.core.util.schema.ConvertOptions;
import com.ideal.ieai.core.util.schema.ExportException;
import com.ideal.ieai.core.util.schema.IDocumentBuilder;

/**
 * <p>Title:translate schema to XSD element,produce a XSD document </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 3.0
 */

public class XSDDocumentBuilder
    implements IDocumentBuilder
{
    public XSDDocumentBuilder(IEAISchema schema)
    {
    }


    /**
     * convert a schema to a XSD document
     * @param schema
     * @param options
     * @return
     * @throws ExportException
     */
    public Object convert(IEAISchema schema, ConvertOptions options) throws ExportException
    {
        try
        {
            _doc = DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
        }
        catch (FactoryConfigurationError ex)
        {
            throw new ExportException(ExportException.Factory_CONFIG_ERROR,
                                      "builder factory configration error");
        }
        catch (ParserConfigurationException ex)
        {
            throw new ExportException(ExportException.Factory_CONFIG_ERROR,
                                      "parse configration exception");
        }
        Element rootElement = _doc.createElement(XSDHelper.SCHEMA);
        rootElement.setAttribute(XSDHelper.XMLNS_XS, XSDHelper.NS_VALUE);
        rootElement.setAttribute(XSDHelper.ELEMENT_FORM_DEFAULT,XSDHelper.QUALIFIED);
        rootElement.setAttribute(XSDHelper.ATTRIBUTE_FORM_DEFAULT,XSDHelper.UNQUALIFIED);

        SchemaRefHelper helper = new SchemaRefHelper(schema, options, _doc);
        helper.appendToRootElement(rootElement);
        _doc.appendChild(rootElement);
        return _doc;
    }


    private Document _doc;

}