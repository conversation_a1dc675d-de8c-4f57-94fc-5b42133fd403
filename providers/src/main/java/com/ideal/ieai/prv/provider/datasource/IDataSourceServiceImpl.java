package com.ideal.ieai.prv.provider.datasource;

import com.ideal.dubbo.interfaces.datasource.IDataSourceService;
import com.ideal.dubbo.models.DataSourceModel;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.ieai.prv.provider.utils.ParseJson;
import com.ideal.ieai.prv.repository.hd.datasource.DataSourceManager;
import com.ideal.ieai.prv.repository.hd.dubbo.dbaas.datachange.DataChangeManager;
import com.ideal.ieai.server.common.ycblj.StartBljGetPwd;
import com.ideal.ieai.server.engine.agent.AgentXmlRpcClient;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.proxy.execremote.PerformDataProcessService;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.agentMaintain.RpcSslType;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.log4j.Logger;
import org.apache.xmlrpc.XmlRpcClient;
import org.json.JSONObject;

import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;

@DubboService
public class IDataSourceServiceImpl implements IDataSourceService
{
    private static final Logger logger = Logger.getLogger(IDataSourceServiceImpl.class);

    @Override
    public Map<String, Object> saveDataSource ( String jsonData, String dataFrom, String jsonDataEnv ) throws Exception
    {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("success", false);
        map.put("message", "保存失败！");

        List<DataSourceModel> bsList = new ArrayList<DataSourceModel>();
        List<Map<String, Object>> databaseList = ParseJson.JSON2List(jsonData);
        List<String> nameList = new ArrayList<String>();
        DataSourceModel dbm = null;

        if (databaseList != null && !databaseList.isEmpty())
        {
            for (int i = 0; i < databaseList.size(); i++)
            {
                dbm = new DataSourceModel();
                Map<String, Object> cmdMap = databaseList.get(i);
                dbm = convertToDataSourceModel(cmdMap, dataFrom);
                if (dbm.getIID() == -1)
                {
                    nameList.add(dbm.getIDSNAME());
                }
                bsList.add(dbm);
            }
            if (!"1".equals(dataFrom))
            {
                if (nameList != null && !nameList.isEmpty())
                {
                    if (DataSourceManager.getInstance().isExistDataSourceByName(listToString(nameList)))
                    {
                        map.put("message", "数据源名重复！");
                        return map;
                    }
                }
            }
            if (DataSourceManager.getInstance().saveDataSource(bsList, dataFrom))
            {
                map.put("success", true);
                map.put("message", "保存成功！");
            }
            // Map a = null;
            // for (DataSourceModel model : bsList)
            // {
            // List<Map<String, Object>> envList = new ArrayList<Map<String, Object>>();
            // if (model.getIID() <= 0)
            // {
            // a = getDatabaseCfgList(String.valueOf(model.getIID()));
            // envList = (List) a.get("dataList");
            // }
            // saveDatasourceEnvList(envList);
            // }
        }

        return map;

    }

    @Override
    public Map listDataSource ( String page, String limit, String baseName, String dataType ) throws Exception
    {
        if ("1".equals(dataType))
        {
            return DataSourceManager.getInstance().listDataSourceDataChange(page, limit, baseName);
        } else
        {
            return DataSourceManager.getInstance().listDataSource(page, limit, baseName);
        }
    }

    @Override
    public Map deleteDataSource ( Long[] deleteIds ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        resp.put("success", true);
        resp.put("message", "删除成功!");

        if (!DataSourceManager.getInstance().deleteDataSource(deleteIds))
        {
            resp.put("success", false);
            resp.put("message", "删除失败！");
        }

        return resp;
    }

    private DataSourceModel convertToDataSourceModel ( Map<String, Object> rgMap, String dataFrom )
    {
        DataSourceModel res = new DataSourceModel();
        Object IID = rgMap.get("IID");
        if (IID == JSONObject.NULL || "".equals(String.valueOf(IID)))
        {
            res.setIID(-1l);
        } else
        {
            res.setIID(Long.parseLong(String.valueOf(rgMap.get("IID"))));
        }
        res.setIdsip(((String) rgMap.get("IDSIP")).trim());
        res.setIDSNAME(((String) rgMap.get("IDSNAME")));
        res.setIDSDRIVER(((String) rgMap.get("IDSDRIVER")).trim());
        res.setIDSPORT(((String) rgMap.get("IDSPORT")).trim());
        res.setIDSUSER(((String) rgMap.get("IDSUSER")).trim());
        res.setIDSPWD(((String) rgMap.get("IDSPWD")).trim());
        res.setIDSROLE(((String) rgMap.get("IDSROLE")).trim());
        res.setIDSINSTANCE(((String) rgMap.get("IDSINSTANCE")).trim());
        res.setIdbtype(((String) rgMap.get("IDBTYPE")).trim());
        res.setIdburl(((String) rgMap.get("IDBURL")).trim());
        if (rgMap.containsKey("ICOLLECT"))
        {
            res.setICOLLECT(rgMap.get("ICOLLECT") == null ? "" : ((String) rgMap.get("ICOLLECT")).trim());
        }
        if (rgMap.containsKey("ISYSID"))
        {
            String sysId = String.valueOf(rgMap.get("ISYSID") == null ? "" : rgMap.get("ISYSID"));
            res.setSysId(!"".equals(sysId) ? Long.parseLong(sysId) : 0);
        }
        if (rgMap.containsKey("IDBENCODE"))
        {
            String encode = String.valueOf(rgMap.get("IDBENCODE") == null ? "" : rgMap.get("IDBENCODE"));
            res.setIDBENCODE(encode);
        }
        if (rgMap.containsKey("permission"))
        {
            res.setPermisson("false".equals(String.valueOf(rgMap.get("permission"))) ? 0 : 1);
        }
        if (rgMap.containsKey("IDBIP"))
        {
            res.setIDBIP(String.valueOf(rgMap.get("IDBIP")));
        }
        if (rgMap.containsKey("suUser"))
        {
            res.setSuUser(String.valueOf(rgMap.get("suUser")));
        }
        return res;
    }

    public String listToString ( List<String> list )
    {
        StringBuilder sb = new StringBuilder();
        if (list != null && !list.isEmpty())
        {
            for (int i = 0; i < list.size(); i++)
            {
                if (i < list.size() - 1)
                {
                    sb.append("'" + list.get(i) + "'" + ",");
                } else
                {
                    sb.append("'" + list.get(i) + "'");
                }
            }
        }
        return sb.toString();
    }

    @Override
    public Map getCpIpInfoList ( String cpidStr, String ipBeginStr, String ipEndStr, String startStr, String limitStr )
            throws Exception
    {
        return DataSourceManager.getInstance().getCpIpInfoList(cpidStr, ipBeginStr, ipEndStr, startStr, limitStr);
    }

    @Override
    public Map getAllDataSourceNamesByCpid ( Long cpid ) throws Exception
    {
        return DataSourceManager.getInstance().getAllDataSourceNamesByCpid(cpid);
    }

    @Override
    public Map getDsListByCpId ( Long cpid, String switchFlag ) throws Exception
    {
        return DataSourceManager.getInstance().getDsListByCpId(cpid, switchFlag);
    }

    @Override
    public Map selectCpIpAndPort ( long dsid, long cpid ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        resp.put("success", true);
        resp.put("message", "操作成功!");

        if (!DataSourceManager.getInstance().selectCpIpAndPort(dsid, cpid))
        {
            resp.put("success", false);
            resp.put("message", "操作失败！");
        }

        return resp;
    }

    @Override
    public Map getDataSourceById ( Long id )
    {
        return DataSourceManager.getInstance().getDataSourceById(id);
    }

    @Override
    public Map chkDbResourceValid ( String id )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map retMap = new HashMap();
        Map maps = null;
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            maps = DataSourceManager.getInstance().chkDbResourceValid(Long.parseLong(id));
            String isblj = String.valueOf(maps.get("blj"));
            String driver = String.valueOf(maps.get("dbDriver"));
            String dburl = String.valueOf(maps.get("dbUrl") == null ? "" : maps.get("dbUrl"));
            String dbuser = String.valueOf(maps.get("dbuser"));
            String dbpd = String.valueOf(maps.get("dbpd"));
            String dbpwd = "";
            String sysAbb = String.valueOf(maps.get("sysAbb"));
            String agentIp = String.valueOf(maps.get("agentIp"));
            int agentPort = Integer.parseInt(String.valueOf(maps.get("agentPort")));
            String dbType = String.valueOf(maps.get("dbType"));
            String insname = String.valueOf(maps.get("insname"));
            String dbPort = String.valueOf(maps.get("dbPort"));
            String dbIp = String.valueOf(maps.get("dbIp"));
            String dbTypeIndex = "1";
            String content = "";
            if (driver.toUpperCase().indexOf("ORACLE") > -1)
            {
                content = " select * from dual a";
                dbTypeIndex = "1";
            } else if (driver.toUpperCase().indexOf("MYSQL") > -1)
            {
                content = " select @@version,''";
                dbTypeIndex = "3";
            } else if (driver.toUpperCase().indexOf("DB2") > -1)
            {
                content = " SELECT SERVICE_LEVEL,INST_NAME FROM SYSIBMADM.ENV_INST_INFO";
                dbTypeIndex = "2";
            } else if (driver.toUpperCase().indexOf("POSTGRESQL") > -1)
            {
                content = " SELECT 1 ";
                dbTypeIndex = "4";
            }
            String stderr = "状态获取成功";
            if ("1".equals(isblj))
            {
                String resIp = dbIp;
                // if (driver.toUpperCase().indexOf("REDIS") > -1 || driver.toUpperCase().indexOf("MONGODB") > -1)
                // {
                // resIp = dburl.split(":")[0];
                // } else
                // {
                // resIp = dburl.split("//")[1].split(":")[0];
                // }

                // 调用堡垒机獲取密碼
                String serverIp = ServerEnv.getInstance().getServerIP();
                String serverUser = ServerEnv.getInstance().getSysConfig(Environment.YC_APP_SERVER_TO_BLJ, "root");
                String sftpfilePath = ServerEnv.getInstance().getSysConfig(Environment.YC_SERVER_SFTP_PATH_TO_BLJ,
                    "/app/ideal/");

                dbpwd = StartBljGetPwd.getInstance().getPwds(sysAbb, serverIp, serverUser, "*_db", resIp, sftpfilePath,
                    2, dbuser, "_tr");
                // dbpwd = StartGetPwd.getInstance().getPwds(sysAbb, serverIp, serverUser, "*_db", resIp,
                // sftpfilePath, 2,
                // dbuser);
            } else
            {
                dbpwd = dbpd;
            }
            dburl = DataChangeManager.getInstance().tranckJdbcUrl(dbTypeIndex, dbIp, dbPort, insname,
                "null".equals(dburl) ? "" : dburl);
            String serverIP = null;
            int serverPort = Environment.getInstance().getIntConfig(ServerEnv.TOM_PORT, ServerEnv.TOM_PORT_DEFAULT);
            try
            {
                serverIP = Environment.getInstance().getServerIP();
            } catch (UnknownHostException e1)
            {
                logger.error(method + " : ", e1);
            }
            updateState(conn, Long.parseLong(id), -1);
            Hashtable scriptHashtable = new Hashtable();
            scriptHashtable.put("command", content);
            scriptHashtable.put("serviceName", "valid-db");
            scriptHashtable.put("servicesType", "sql");
            scriptHashtable.put("pm", "");
            Vector params = new Vector();
            params.addElement("dc_val_" + id);
            params.addElement(serverIP);
            params.addElement(serverPort);
            Hashtable table = new Hashtable();
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("serverHost", serverIP);
            table.put("serverPort", serverPort);
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", agentPort);
            table.put("adaptorDefUUID", DataChangeManager.getInstance().getAdaptorUUID("sqladaptor"));
            table.put("actId", "execsql");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "sqladaptor");// shellcmd
            table.put("actName", "sqlScriptAct"); // 默认值
            table.put("projectName", "shell_test_valid" + id);// ?
            table.put("flowName", "ExecSql");// 使用taskName可否?可以
            scriptHashtable.put("dbdriver", driver);// 使用taskName可否?可以
            scriptHashtable.put("dburl", dburl);// 使用taskName可否?可以
            scriptHashtable.put("dbuser", dbuser);// 使用taskName可否?可以
            scriptHashtable.put("dbpwd", dbpwd);// 使用taskName可否?可以
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", "dc_val_" + id);
            table.put("flowId", "-99");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            table.put("expectLastline", "");// 预期结果
            table.put("errorExpectLastline", "");// 预期异常结果
            table.put("expecttype", "");// 判断类型，1：lastLine，2：ret
            scriptHashtable.put("pm", "");
            scriptHashtable.put("expectLastline", "");// 预期结果
            scriptHashtable.put("errorExpectLastline", "");// 预期异常结果
            scriptHashtable.put("expecttype", "");// 判断类型，1：lastLine，2：ret
            scriptHashtable.put("executeUsername", "");
            scriptHashtable.put("isShutdown", false);
            scriptHashtable.put("isTest", false);
            scriptHashtable.put("scriptInstanceId", id);

            // 浦发需求 bat脚本 非administrator 需要与账号管理平台对接，自动获取密码，为了切换用户使用 -----------end
            scriptHashtable.put("annexFiles", new Object[] {});

            table.put("input", scriptHashtable);
            int ssltype= RpcSslType.getInstance().exec(agentIp, agentPort);


            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(table, agentIp, agentPort, Constants.IEAI_SCRIPT_SERVICE);
                if (pm.isProxy())
                {
                    logger.info("data.proxy- remoteIP.valid.db:" + pm.getIp() + ",remotePort:" + pm.getPort()
                            + ",agent:" + agentIp + ":" + agentPort + " proxySwitch is true ");
                }
                agentIp = pm.getIp();
                agentPort = pm.getPort();
                ssltype=pm.getSsl();
            }
            boolean dangerFlag = false;
            params.addElement(table);
            for (int i = 0; i < 5; i++)
            {
                try
                {
                    XmlRpcClient rpcClient = new AgentXmlRpcClient(agentIp, agentPort, ssltype);
                    rpcClient.execute("IEAIAgent.executeAct", params);
                    dangerFlag = true;
                    break;
                } catch (Exception e)
                {
                    if (i != 4)
                    {
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                        }
                    } else
                    {
                        logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
                        logger.error(Thread.currentThread().getStackTrace()[1].getMethodName()
                                + " : Communication Agent Error!");
                        stderr = "Communication Agent Error!";
                    }
                } finally
                {

                }
            }

            if (dangerFlag)
            {
                long state = -1;
                Map<String, String> m = new HashMap<String, String>();
                for (int i = 0; i < 10; i++)
                {
                    Thread.sleep(2000);
                    state = getState(conn, Long.parseLong(id), m);
                    if (state == 0)
                    {
                        break;
                    } else if (state == 1)
                    {
                        String msg = m.get("msg");
                        stderr = "连接数据库状态异常!" + msg;
                        break;
                    }
                }
                if (state == -1)
                {
                    stderr = "长时间未获取到数据库状态!";
                }
                retMap.put("success", true);
            } else
            {
                retMap.put("success", false);
                updateState(conn, Long.parseLong(id), 2);
            }
            retMap.put("message", stderr);

        } catch (Exception e)
        {
            logger.error(e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    private static void updateState ( Connection con, long id, int state )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            // -1 草稿，0作廢，1待审核，2审核通过，3运行中，4运行完成，5终止，6退回，7退回待修改，8执行异常，9agent连接不上,10 删除
            String sql = " UPDATE IEAI_SCRIPT_DATASOURCE SET ISTATE=? WHERE IID=?";
            ps = con.prepareStatement(sql);
            ps.setLong(1, state);
            ps.setLong(2, id);
            ps.executeUpdate();
            con.commit();
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closePSRS(rs, ps, method, logger);
        }
    }

    private static long getState ( Connection con, long id, Map<String, String> m )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        long state = -1;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            // -1 init，0 ok，1 err，2 agent err
            String sql = " SELECT ISTATE,IMSG FROM  IEAI_SCRIPT_DATASOURCE WHERE IID=?";
            ps = con.prepareStatement(sql);
            ps.setLong(1, id);
            rs = ps.executeQuery();
            while (rs.next())
            {
                state = rs.getLong("ISTATE");
                m.put("msg", rs.getString("IMSG"));
                m.put("state", rs.getString("ISTATE"));
            }
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closePSRS(rs, ps, method, logger);
        }
        return state;
    }

    /**
    * 保存环境变量 
    * <li>Description:</li> 
    * <AUTHOR>
    * 2021年2月7日 
    * @param jsonData
    * @param dataFrom
    * @return
    * @throws Exception
    * return Map<String,Object>
    */
    public Map<String, Object> saveDatasourceEnv ( String jsonData ) throws Exception
    {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("success", false);
        map.put("message", "保存失败！");

        List<DataSourceModel> bsList = new ArrayList<DataSourceModel>();
        List<Map<String, Object>> databaseList = ParseJson.JSON2List(jsonData);
        DataSourceModel dbm = null;

        if (databaseList != null && !databaseList.isEmpty())
        {
            for (int i = 0; i < databaseList.size(); i++)
            {
                dbm = new DataSourceModel();
                Map<String, Object> cmdMap = databaseList.get(i);
                String IID = String.valueOf(cmdMap.get("iid"));
                String idsId = String.valueOf(cmdMap.get("idsId"));
                String iname = String.valueOf(cmdMap.get("iname"));
                String ivalue = String.valueOf(cmdMap.get("ivalue"));
                String iorder = String.valueOf(cmdMap.get("iorder"));
                if (IID == JSONObject.NULL || "".equals(IID))
                {
                    dbm.setIID(-1l);
                } else
                {
                    dbm.setIID(Long.parseLong(String.valueOf(IID)));
                }
                dbm.setIdsId(Long.parseLong(idsId));
                dbm.setIname(iname);
                dbm.setIorder(Long.parseLong(iorder));
                dbm.setIvalue(ivalue);
                bsList.add(dbm);
            }
            if (DataSourceManager.getInstance().saveDataSourceEnv(bsList))
            {
                map.put("success", true);
                map.put("message", "保存成功！");
            }
        }
        return map;
    }

    public Map<String, Object> saveDatasourceEnvList ( List<Map<String, Object>> databaseList ) throws Exception
    {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("success", false);
        map.put("message", "保存失败！");

        List<DataSourceModel> bsList = new ArrayList<DataSourceModel>();
        DataSourceModel dbm = null;

        if (databaseList != null && !databaseList.isEmpty())
        {
            for (int i = 0; i < databaseList.size(); i++)
            {
                dbm = new DataSourceModel();
                Map<String, Object> cmdMap = databaseList.get(i);
                String IID = String.valueOf(cmdMap.get("iid"));
                String idsId = String.valueOf(cmdMap.get("idsId"));
                String iname = String.valueOf(cmdMap.get("iname"));
                String ivalue = String.valueOf(cmdMap.get("ivalue"));
                String iorder = String.valueOf(cmdMap.get("iorder"));
                if (IID == JSONObject.NULL || "".equals(IID))
                {
                    dbm.setIID(-1l);
                } else
                {
                    dbm.setIID(Long.parseLong(String.valueOf(IID)));
                }
                dbm.setIdsId(Long.parseLong(idsId));
                dbm.setIname(iname);
                dbm.setIorder(Long.parseLong(iorder));
                dbm.setIvalue(ivalue);
                bsList.add(dbm);
            }
            if (DataSourceManager.getInstance().saveDataSourceEnv(bsList))
            {
                map.put("success", true);
                map.put("message", "保存成功！");
            }
        }
        return map;
    }

    public Map getDatabaseCfgList ( String dsId ) throws Exception
    {
        return DataSourceManager.getInstance().getDatabaseCfgList(dsId);
    }

    public Map deleteDataSourceEnv ( Long[] deleteIds ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        resp.put("success", true);
        resp.put("message", "删除成功!");

        if (!DataSourceManager.getInstance().deleteDataSourceEnv(deleteIds))
        {
            resp.put("success", false);
            resp.put("message", "删除失败！");
        }

        return resp;
    }

    @Override
    public Map listDataSourceDataChangeBySysId ( long sysId ) throws Exception
    {
        return DataSourceManager.getInstance().listDataSourceDataChangeBySysId(sysId);
    }

}
