<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment" %>
<html>
<head>
    <script type="text/javascript">
        var hcStrategyHeadOfficeSwitch = <%=Environment.getInstance().getBooleanConfigNew2(Environment.HC_STRATEGY_HEAD_OFFICE_SWITCH, true)%>;
    </script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/hcstrategy/agencyTransit/agencyTransitIndex.js"></script>
<body>
<div id="agencyTransit_grid" style="width: 100%;height: 100%"></div>
</body>
</html>
