/**
 * 
 */
package com.ideal.service.jobscheduling.actmonitorinfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.jobscheduling.repository.actmonitorinfo.ActMonitorBean;
import com.ideal.ieai.server.jobscheduling.repository.actmonitorinfo.ActMonitorInfoManager;
import com.ideal.ieai.server.repository.RepositoryException;


/**
 * <ul>
 * <li>Title: ActMonitorInfoService.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020年6月10日
 */
public class ActMonitorInfoService
{
    private static final Logger _log = Logger.getLogger(ActMonitorInfoService.class);

    private static ActMonitorInfoService instance = new ActMonitorInfoService();

    public static ActMonitorInfoService getInstance ()
    {

        if (instance == null)
        {
            instance = new ActMonitorInfoService();
        }
        return instance;
    }
    
  public Map<String,Object> getActMonitorInfoMap(String prjName,String childName,String flowInsName,String status,String page,String limit) throws RepositoryException{
        
        Map<String,Object> rsMap = new HashMap<String,Object>();
        List<ActMonitorBean>  resuList = null;
        long total = 0;
        
        resuList = ActMonitorInfoManager.getInstance().getActMonitorInfoList(page, limit, prjName, childName, flowInsName,status);
        
        total = ActMonitorInfoManager.getInstance().getActMonitorInfoCount(prjName, childName, flowInsName,status);
        
        rsMap.put("dataList", resuList);
        rsMap.put("total", total);
        return rsMap;
    }
  
  public Map<String,List> getMainRroname() throws RepositoryException{
      
    return  ActMonitorInfoManager.getInstance().getMainRroname();
  }
  
  public Map<String,List> getIchildProname(String imainProname) throws RepositoryException{
      
      return ActMonitorInfoManager.getInstance().getIchildProname(imainProname);
  }
}
