package com.ideal.ieai.server.repository.groupMessage;

import java.util.List;

import com.ideal.ieai.server.ServerException;

/**
 * 集群分组操作接口
 * <AUTHOR>
 *
 */
public interface IGroupMessageManager {
	
	public List listGroupMessage () throws ServerException;
	
	public List listServerGroupMessage () throws ServerException;
	
	public List listApplyGroupMessage () throws ServerException;
	
	public List listConditionsApplyGroupMessage (String projectName) throws ServerException;
	
	public List listOneGroupMessage (Long groupId) throws ServerException;
	
	public void deleteGroupMessage (long groupId) throws ServerException;
	
	public boolean isNotDeleteGroupMessage (long groupId) throws ServerException;
	
	public boolean isSameGroupName (String groupName,String groupId) throws ServerException;
	
	public Long saveGroupMessage (String groupName,String groupDescription) throws ServerException;
	
	public void saveServerGroupMessage (Long serverId,Long groupId) throws ServerException;
	
	public void deleteServerGroupMessage (Long serverId) throws ServerException;
	
	public void saveApplyGroupMessage (Long applyId,Long groupId) throws ServerException;
	
	public Long updateGroupMessage (Long groupId,String groupName,String groupDescription) throws ServerException;
	
	public void updateNullGroupMessage (String serverIP) throws ServerException;
	
	public void createDefaultGroupMessage () throws ServerException;

}
