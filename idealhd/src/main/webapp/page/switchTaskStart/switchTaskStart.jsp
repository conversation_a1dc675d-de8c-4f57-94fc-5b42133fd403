<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<html>
<head>
<script type="text/javascript">
	<%
		SimpleDateFormat sFormat = new SimpleDateFormat("yyyyMMddHHmmss");
		String nowTime = sFormat.format(new Date());
	%>
	var nowTime='<%=nowTime%>';
	var iscib = <%= Environment.getInstance().getBankSwitchIscib()%>;
	var ispfb = <%= Environment.getInstance().getSwitchDesktopFlag()%>;
	var cibItsmFlag = <%= Environment.getInstance().getCibItsmSwtich()%>;
	var bankSwitch='<%=Environment.getInstance().getBankSwitch()%>';
	var timeTask=<%=Environment.getInstance().getBooleanConfig("eswitch.timetask.flag", false)%>;
	var nmgFlag=<%=Environment.getInstance().getBooleanConfig("eswitch.excel.nmnx.flag", false)%>;
	//是否有权限的标识true就能操作false不能操作提示没有权限
	var typeFlag = <%=request.getAttribute("typeFlag")%>;
    var psbcOrderShow = <%= Environment.getInstance().getItsmOrderNumShowSwitch()%>;
    var psbcOrderFlag=(bankSwitch==="PSBC"&&psbcOrderShow);

	
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/switchTaskStart/switchTaskStart.js"></script>
<style>
		#textfield1-inputEl{
			border-width: 0px;
		}
		#textfield2-inputEl{
			border-width: 0px;
		}
	</style>
</head>
<body>
<div id="switchTaskStart_area" style="width: 100%;height: 100%">
</div>
</body>
</html>