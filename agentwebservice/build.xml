<?xml version="1.0" ?>
<project default="WDSL2Java">
	
	<condition property="dev_root" value="./..">		
	    <not>			
		<isset property="dev_root" />
	    </not>	
	</condition>	
	
	<property name="ieai_root" location="${dev_root}" />
	<property name="ieailibs" location="${ieai_root}/lib/lib" />
	<property name="generated.dir" location="${ieai_root}/agentwebservice/src" />
	<path id="axis.classpath">
		<fileset dir="${ieailibs}" includes="**/*.jar" />
	</path>
	<taskdef resource="axis-tasks.properties" classpathref="axis.classpath" />
	<target name="clean_wsdl_generated">
		<echo message="Cleaning code genereated  by WSDL2Java..." />
		<delete dir="./src/com/ideal/ieai/clientapi/ws/*.*" includeemptydirs="true" verbose="true"/>
        <delete dir="./classes/*.*" includeemptydirs="true" verbose="true"/>
		<echo message="Cleaning done!" />
	</target>
	<!-- =================================
          target: compile_idealutils
         ================================= -->
	<target name="WDSL2Java"
	        description="Compile WDSL to Java client"
	        depends="clean_wsdl_generated">
		<!-- then , compile WSDL into Java files -->
		<axis-wsdl2java output="${generated.dir}"
		                testcase="true"
		                verbose="true"
		                url="${ieai_root}/server/AgentService.wsdl">
			<mapping namespace="http://axis.apache.org/ns/interop"
			         package="com.ideal.ieai.agentapi.ws" />
			<mapping namespace="http://agent.webservice.server.ieai.ideal.com"
			         package="com.ideal.ieai.agentapi.ws" />
			<mapping namespace="http://agent.webservice.v30.ieai.ideal.com"
			         package="com.ideal.ieai.agentapi.ws" />
		</axis-wsdl2java>
	</target>
	</project>

