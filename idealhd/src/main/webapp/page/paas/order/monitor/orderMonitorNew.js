var orderAutoRefreshTime=null;
var orderAutoRefreshCheck=null;
var orderRefreshButton=null;
var ORDER_R_TIME = 30;
Ext.onReady(function() {
	//清理主面板的各种监听时间
	destroyRubbish();
	var pagelimit = 30;
	orderAutoRefreshTime = Ext.create ('Ext.form.NumberField',
    {
        fieldLabel : '刷新时间（秒）',
        margin : '5',
        labelWidth : 110,
        width : 170,
        value : '30',
        allowDecimals : false,
        minValue : ORDER_R_TIME,
        listeners :
        {
            blur : function ()
            {
	            orderrefreshTime = this.getValue ();
	            orderrefreshTime = (orderrefreshTime == '' || orderrefreshTime == null) ? ORDER_R_TIME : orderrefreshTime;
	            try
	            {
		            orderrefreshTime = orderrefreshTime < ORDER_R_TIME ? ORDER_R_TIME : orderrefreshTime;
		            this.setValue (orderrefreshTime);
	            }
	            catch (e)
	            {
		            orderrefreshTime = ORDER_R_TIME;
		            this.setValue (orderrefreshTime);
	            }
	            if (orderAutoRefreshCheck.checked)
	            {
		            clearInterval (refreshObj);
		            refreshObj = setInterval (reload, orderrefreshTime * 1000);
	            }
            }
        }
    });

	Ext.define('orderModel', {
	    extend : 'Ext.data.Model',
	    fields : [ {
			name : 'iId',
			type : 'long'
		}, {
			name : 'cacheId',
			type : 'long'
		},{
	      name : 'templateType',
	      type : 'string'
	    }, {
	      name : 'templateName',
	      type : 'string'
	    },{
		      name : 'templateRealName',
		      type : 'string'
		 },
	    {
	    	name : 'orderName',
	    	type : 'string'
	    },{
	    	name : 'createUser',
	    	type : 'string'
	    },{
	    	name :'createTime',
	    	type:'string'
	    },{
	    	name :'status',
	    	type:'string'
	    },{
	    	name :'applyTime',
	    	type:'string'
	    },{
			name :'iTemplateid',
			type:'long'
		},{
			name :'iTemplateType',
			type:'int'
		},{
			name :'itsmNo',
			type:'string'
		},{
			name :'itsmRealtionId',
			type:'long'
		},{
			name :'itsmHisId',
			type:'long'
		},
		{
			name :'ilastexecutetime',
			type:'string'
		},{
			name :'istarttime',
			type:'string'
		},{
			name :'instanceName',
			type:'string'
		},{
			name :'applyType',
			type:'string'
		},{
			name : 'cacheUuid',
			type : 'string'
		},{
			name : 'execTime',
			type : 'string'
		},{
			name : 'bizId',
			type : 'string'
		},{
				name : 'itasksubid',
				type : 'string'
			},{
				name : 'iinsName',
				type : 'string'
			}
	    ]
	});

	Array.prototype.baoremove = function(dx) {
	    if (isNaN(dx) || dx > this.length) {
	        return false
	    }
	    this.splice(dx, 1)
	};
   var orderStore = Ext.create('Ext.data.Store',{
	  autoLoad : true,
	  autoDestroy : true,
	  groupField : 'itsmNo',
	  model : 'orderModel',
	  pageSize:pagelimit,
	  proxy : {
		  type : 'ajax',
		  url : 'getOrderMonitorListNew.do',
		  reader : {
			  type : 'json',
			  root : 'dataList'
		  }
	  }
  });

  var pagebar = Ext.create('Ext.PagingToolbar', {
		pageSize:pagelimit,
		dock: 'bottom',
		id: 'pageBarId',
		store:orderStore,
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		displayInfo: true,
		displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
		emptyMsg: "没有记录"
	});

	 // 是否自动刷新复选框
 orderAutoRefreshCheck = Ext.create ('Ext.form.Checkbox',
 {
     fieldLabel : '自动刷新',
     margin : '5',
     labelWidth : 70,
     width : 90,
     checked : true,
     listeners :
     {
         change : function ()
         {
	            if (this.checked)
	            {
		            orderrefreshTime = orderAutoRefreshTime.getValue ();
		            orderrefreshTime = orderrefreshTime == '' ? ORDER_R_TIME : orderrefreshTime;
		            try
		            {
			            orderrefreshTime = orderrefreshTime < ORDER_R_TIME ? ORDER_R_TIME : orderrefreshTime;
			            orderAutoRefreshTime.setValue (orderrefreshTime);
		            }
		            catch (e)
		            {
			            orderrefreshTime = ORDER_R_TIME;
			            orderAutoRefreshTime.setValue (orderrefreshTime);
		            }
		            clearInterval (refreshObj);
		            refreshObj = setInterval (reload, orderrefreshTime * 1000);
	            }
	            else
	            {
		            clearInterval (refreshObj);
	            }
         }
     }
 });
 //首次加载定时刷新
 if (orderAutoRefreshCheck.checked == true)
 {
     refreshObj = setInterval (reload, orderAutoRefreshTime.getValue () * 1000);
 }
 /** 刷新按钮* */
	orderRefreshButton = Ext.create ("Ext.Button",
	{
		 text : '刷新',
      cls : 'Common_Btn',
      listeners: {
          "click": function() {
         	 reload();
          }
      }
	});
	var autofreshBar=Ext.create('Ext.toolbar.Toolbar', {
		border:false,
	    items: [
	    	orderAutoRefreshTime,
	    	orderAutoRefreshCheck,
	    	orderRefreshButton,
         ]
	});

  var orderColumns = [
	  {text: '工单ID'			, dataIndex:'iId' 		,hidden:true     },
	  {text: '运行实例ID'			, dataIndex:'cacheId' 		,hidden:true     },
	  {text : '类型ID', dataIndex : 'iTemplateType', hidden:true,editor:false},
	  {text : '模板ID', dataIndex : 'iTemplateid', hidden:true,editor:false},
	  {text : '类型', dataIndex : 'templateType', width:180,editor:false},
	  {text : '名称', dataIndex : 'templateName', width:180,editor:false,hidden:bhSwitch,
		  renderer : function(value, metaData, record) {
//          metaData.tdAttr = 'data-qtip="' + value + '"';
//          return value;
//			console.log( record.data);
//	      	if(null!= record.data.bizId && record.data.bizId !=='' ){
//	      		return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"getITILDetail('" + record.get("bizId") + "','"+record.get("templateName")
//	  			+ "');\">" + "<span class='abc' style='color:#3167ff;'>" + value + "</span>" + "</a>";
//
//	      	}else{
//	      		return value;
//	      	}
			  return value;}},
	  {text : '名称', dataIndex : 'iinsName', width:520,editor:false, hidden:bhSwitch,
		renderer : function(value, metaData, record) {
//          metaData.tdAttr = 'data-qtip="' + value + '"';
//          return value;
//			console.log( record.data);
//	      	if(null!= record.data.bizId && record.data.bizId !=='' ){
//	      		return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"getITILDetail('" + record.get("bizId") + "','"+record.get("templateName")
//	  			+ "');\">" + "<span class='abc' style='color:#3167ff;'>" + value + "</span>" + "</a>";
//	  
//	      	}else{
//	      		return value;
//	      	}
			return value;
      }},
	  {text : '名称',dataIndex : 'orderName',width:250,editor:false,hidden:!bhSwitch,
		renderer : function(value, metaData, record) {
          metaData.tdAttr = 'data-qtip="' + value + '"';
          return value;
      }},
	  {text : 'ITIL单号',dataIndex : 'itsmNo', width:150,editor:false,
			 renderer : function(value, metaData, record) {
				console.log( record.data);
			    if(null!= record.data.bizId && record.data.bizId !=='' ){
		      		return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"getITILDetail('" + record.get("bizId") + "','"+record.get("templateName")
		  			+ "','"+record.data.applyType+"');\">" + "<span class='abc' style='color:#3167ff;'>" + value + "</span>" + "</a>";
		      	}else{
		      		return value;
		      	}
		  }
      },
	  {text : '创建人',dataIndex : 'createUser', width:180,editor:false},
	  {text : '创建时间',dataIndex : 'createTime', hidden:true,width:230,editor:false},
	  {text : '申请时间',dataIndex : 'applyTime',hidden:true, width:180,editor:false},
	  {text : '启动时间',dataIndex : 'execTime', width:230,editor:false},
	  {text : '状态',dataIndex : 'status', width:100,editor:false,
		  renderer: function(value,metaData,record){
			  if(value=="已创建") {
			        return "<span class='Green_color State_Color'>"+ value +"</span>";
			  }else if(value=="审批中") {
			        return "<span class='Green_color State_Color'>"+ value +"</span>";
			  }else if(value=="已审批") {
			        return "<span class='Green_color State_Color'>"+ value +"</span>";
			  }else if(value=="已退回") {
			        return "<span class='Red_color State_Color'>"+ value +"</span>";
			  }else if(value=="已归档") {
			        return "<span class='Gray_color State_Color'>"+ value +"</span>";
			  }else if(value=="运行中") {
			        return "<span class='Blue_color State_Color'>"+ value +"</span>";
			  }else if(value=="已完成") {
			        return "<span class='Green_color State_Color'>"+ value +"</span>";
			  }else if(value=="终止") {
			        return "<span class='Red_color State_Color'>"+ value +"</span>";
			  }else if(value=="异常") {
			        return "<span class='Red_color State_Color'>"+ value +"</span>";
			  }else{
				  return value;
			  }

		  }
	  },
	  {text : 'itsmRealtionId',dataIndex : 'itsmRealtionId', hidden:true,width:100,editor:false},
	  {text : 'itsmHisId',dataIndex : 'itsmHisId', hidden:true,width:100,editor:false},
	  {text : '生效时间',dataIndex : 'istarttime', width:140,editor:false,hidden :(shPaasOrderSwitch=='true'),
		  renderer:function(value, metadata, record){
			  if(record.data.applyType=='应急模式'){
				  return "";
			  }
			  return value;
		  }
	  },
	  {text : '失效时间',dataIndex : 'ilastexecutetime', width:180,editor:false,hidden :(shPaasOrderSwitch=='true'),
		  renderer:function(value, metadata, record){
			  if(record.data.applyType=='应急模式'){
				  return "";
			  }
			  return value;
		  }
	  },
	  {text : '实例名',dataIndex : 'instanceName', hidden:true,width:180,editor:false},
	  {text : 'uuid',dataIndex : 'cacheUuid',hidden:true, width:180,editor:false},
	  {text : '审核模式',dataIndex : 'applyType', width:180,editor:false},
	  {text : 'templateRealName',hidden:true, dataIndex : 'templateRealName', width:350,editor:false},
	  {text : 'bizId',hidden:true, dataIndex : 'bizId', width:90,editor:false},
	  {
			text : '操作',
			//xtype : 'actiontextcolumn',
			xtype : 'orderactiontextcolumn',
			width : 320,
			align : 'left',
			items : [{
				text : '终止',
				//iconCls : 'script_end',
				getClass : function(v, metadata, record) {
						var state =record.get('status');
						if((state == '已创建' || state == '审批中' || state == '已退回' || state == '已审批')){
	 				    	return 'x-hidden';
	 				    }
				},
				handler : function(grid, rowIndex, colIndex, item,e, record, row) {
					var orderId = record.data.iId;
			    	var templateType = record.data.iTemplateType;
					var templateName = record.data.templateName;
					var orderName = record.data.orderName;
					// 模板ID
					var templateModelId = record.data.iTemplateid;
					var itilNo = record.data.itsmNo;
					var createUser = record.data.createUser;
					var createTime = record.data.createTime;
					var applyTime = record.data.applyTime;
					var itsmRealtionId =  record.data.itsmRealtionId;
					var itsmHisId=record.data.itsmHisId;
					var cacheUuid=record.data.cacheUuid;
					var cacheid = record.data.cacheId;
					var itasksubid = record.data.itasksubid;

					if(status==5||status==6){
						Ext.Msg.alert("信息提示", "该工单已经完成或终止！");
						return;
					}
					Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
						if (btn == 'yes') {
							// if(bhSwitch){
							// 	dmKillTaskExecute(cacheid)
							// }else {
							Ext.Ajax.request({
								url : 'orderStop.do',
								method : 'POST',
								params : {
									iId : orderId,
									iTemplateType : templateType,
									templateName : templateName,
									orderName : orderName,
									iTemplateid : templateModelId,
									itsmNo : itilNo,
									createUser : createUser,
									createTime : createTime,
									applyTime : applyTime,
									itsmRealtionId : itsmRealtionId,
									itsmHisId : itsmHisId,
									cacheUuid:cacheUuid,
									itasksubid:itasksubid
								},
								success : function(response, request) {
									var success = Ext.decode(response.responseText).success;
									var message = Ext.decode(response.responseText).message;
									Ext.Msg.alert('提示', message);
									orderStore.reload();
								},
								failure : function(result, request) {
									secureFilterRs(result,"操作失败！");
								}
							});
							// }
						}
					})

				}
			},
			{
				text : '监控',
				//iconCls : 'script_monitor',
				getClass : function(v, metadata, record) {
						var state =record.get('status');
						if(state == '0' || state == '1' || state == '2' || state == '3' || state=='8'){
							return 'x-hidden';
	 				    }
				},
				handler : function(grid, rowIndex, colIndex, item,e, record, row) {
					clearInterval (refreshObj);
					var createuser = record.data.createUser;
					var operateable = true;
					if(userName != createuser){
						operateable = false;
					}
					var type = record.data.iTemplateType;

					var cacheid = record.data.cacheId;

					var orderName = record.data.orderName;
					var itempName = record.data.itempName;
					var templateName = record.data.templateName;
					var itemplateid = record.data.iTemplateid;
					var iid = record.data.cacheId;

					var uuid = record.data.uuid;
					var cacheUuid = record.data.cacheUuid;
					var ienvid = record.data.ienvid;
					var itasksubid = record.data.itasksubid;
					if (type == '27')
					{
						contentPanel.getLoader().load(
							{
								//url : "forwardDetail.do",
								url : "forwardEmRuninfoins.do",
								scripts : true,
								params : {
									itempType : type,
									orderName : orderName,
									itempName : itempName,
									orderid : iid,
									cacheid:cacheid,
									orderuuid : uuid,
									itemplateid :itemplateid,
									ienvid : ienvid,
									operateable:operateable
								}
							});
					}else if (type == '1'){
						contentPanel.setTitle("活动监控");
						contentPanel.getLoader().load(
							{
								url: 'passOrderIeaiIndex.do',
								scripts : true,
								params : {
									projectTypeId : type,
									orderName : orderName,
									itempName : itempName,
									orderid : iid,
									cacheid:cacheid,
									iuuid : cacheUuid,
									itemplateid :itemplateid,
									ienvid : ienvid,
									operateable:operateable,
									menuFrom:'monitor',
									editAble:true,
									backAble:true
								}
							});
					}else if (type == '8'){

						// if(bhSwitch){
						// 	dmShowTaskDetileRunning(cacheid,templateName)
						// }else {
							contentPanel.getLoader().load(
								{
									url: 'passOrderOnlyStandIndex.do',
									scripts : true,
									params : {
										projectTypeId : type,
										orderName : orderName,
										itempName : itempName,
										orderid : iid,
										cacheid:cacheid,
										iuuid : cacheUuid,
										itemplateid :itemplateid,
										ienvid : ienvid,
										operateable:operateable,
										menuFrom:'monitor',
										editAble:true,
										backAble:true,
										flag:false
									}
								});
						// }
					}else if(type == '-4'||type == '4'){

							contentPanel.getLoader().load({
								url : 'forwardSwitchMonitor.do',
								scripts : true,
								params : {
									isPaasOrder:'1',
									paasOrderUuid : cacheUuid,
									flag:true
								},
								callback : function(records, operation, success) {
									showImg(url);
								}
							});

//						contentPanel.getLoader().load(
//								{
//									//url : "forwardDetail.do",
//									url : "forwardDetailNew.do",
//									scripts : true,
//									params : {
//										templatetype : 4,
//										orderName : orderName,
//										itempName : itempName,
//										orderid : iid,
//										cacheid:cacheid,
//										iuuid : cacheUuid,
//										itemplateid :itemplateid,
//										ienvid : ienvid,
//				        				menuFrom:'monitor',
//										operateable:operateable
//									}
//								});		

					}else if (type == '505'){
						var itoolTypeId = 0;
						var iid = itasksubid;
						var itoolScriptType = null;
						var itoolName = null;
						Ext.Ajax.request({
							url: 'getToolsById.do',
							async: false,
							params: {
								iid: iid
							},
							success: function (response, request) {
								itoolTypeId = Ext.decode(response.responseText).itoolTypeId;
								itoolScriptType = Ext.decode(response.responseText).itoolScriptType;
								itoolName = Ext.decode(response.responseText).itoolName;
							},
							failure: function (result, request) {
								secureFilterRs(result, "请求返回失败！", request);
							}
						});
						if (itoolTypeId == 0) {
							Ext.Msg.alert("提示", "未获取到工具信息");
							return;
						}
						var url = "";
						if (itoolTypeId == '1') {
							url = "toolalarmInfo/toMessageDetailPage.do";
							openDetailnewWindow = Ext.create('Ext.window.Window', {
								id:'msDetailWindow',
								title: '工具详情',
								autoScroll: true,
								modal: true,
								buttonAlign: 'center',
								draggable: false,// 禁止拖动
								resizable: false,// 禁止缩放
								width: contentPanel.getWidth() * 0.6,
								height: contentPanel.getHeight(),
								loader: {
									url: url,
									params: {
										iid: iid
									},
									autoLoad: true,
									autoDestroy: true,
									scripts: true
								},
								listeners: {
									close: function () {
										//modelStore.reload();
									}
								},
								buttons : [ {
									xtype : 'button',
									textAlign : 'center',
									cls : 'Common_Btn',
									text : '关闭',
									handler : function(){
										var win = parent.Ext.getCmp('msDetailWindow');
										if (win) {win.close();}
									} }
								]

							}).show();
						}
						if (itoolTypeId == '3'||itoolTypeId=='2') {
							var url= 'toolboxInfo/getResultPage2.do';
							if(itoolTypeId==2){
								url = "toolboxInfo/getComHisToolProcess1.do";
								let buttonsItemsData = new Array();
								var closeBut={
									xtype : 'button',
									textAlign : 'center',
									cls : 'Common_Btn',
									text : '关闭',
									handler : function(){
										clearInterval(openExectHisDetailWindowExec);
										openExectHisDetailWindowExec.close();
										orderStore.load();
									}
								}
								buttonsItemsData.push(closeBut);
								var	width =  contentPanel.getWidth()*0.95;
								openExectHisDetailWindowExec = Ext.create('Ext.window.Window', {
									id:'detailHISWindow',
									title : '执行结果查看',
									autoScroll : false,
									modal : true,
									buttonAlign : 'right',
									draggable : false,// 禁止拖动
									resizable : false,// 禁止缩放
									width : width,
									cls:'tool_body',
									height : contentPanel.getHeight(),
									loader : {
										url : url,
										params : {
											iid : iid,
											itoolScriptType: itoolScriptType,
											itoolTypeId: itoolTypeId,
											type:"exect"
										},
										autoLoad : true,
										autoDestroy : true,
										scripts : true
									},
									buttons : buttonsItemsData,
									listeners : {
										close : function(){
											clearInterval(openExectHisDetailWindowExec);
											orderStore.load();
										}
									}
								}).show();

							}else{
								if(isTabSwitch){
									menuClick_gjx(this,unescape(itoolName)+'查看结果',url,iid,iid+5678,{
										iid : iid,
										itoolScriptType: itoolScriptType,
										itoolTypeId: itoolTypeId,
										type:"detail"
									});
								}else{
									let buttonsItemsData = new Array();
									var closeBut={
										xtype : 'button',
										textAlign : 'center',
										cls : 'Common_Btn',
										text : '关闭',
										handler : function(){
											clearInterval(openExectHisDetailWindowExec);
											openExectHisDetailWindowExec.close();
											orderStore.load();
										}
									}
									buttonsItemsData.push(closeBut);
									var	width =  contentPanel.getWidth()*0.95;
									openExectHisDetailWindowExec = Ext.create('Ext.window.Window', {
										id:'detailHISWindow',
										title : '执行结果查看',
										autoScroll : false,
										modal : true,
										buttonAlign : 'center',
										draggable : false,// 禁止拖动
										resizable : false,// 禁止缩放
										width : width,
										cls:'tool_body',
										height : contentPanel.getHeight(),
										loader : {
											url : url,
											params : {
												iid : iid,
												itoolScriptType: itoolScriptType,
												itoolTypeId: itoolTypeId,
												type:"detail"
											},
											autoLoad : true,
											autoDestroy : true,
											scripts : true
										},
										buttons : buttonsItemsData,
										listeners : {
											close : function(){
												clearInterval(openExectHisDetailWindowExec);
												orderStore.load();
											}
										}
									}).show();
								}
							}


						}

					}
				}
			},
			{
				text : '查看',
				//iconCls : 'monitor_search',
				handler : function(grid, rowIndex, colIndex, item,e, record, row) {
                	//forwardFileResult1ForExecForFlow(flowid, cataExec);
					//showCreateWin();
					clearInterval (refreshObj);
					var templateVal = record.data.iTemplateType;
					var itempType = record.data.iTemplateType;
					var orderName = record.data.orderName;
					var itempName = record.data.templateName;
					var itemplateid = record.data.iTemplateid;
					var iid = record.data.iId;
					var itasksubid = record.data.itasksubid;
					var iinsName = record.data.iinsName;
					if(templateVal=='27'){
						contentPanel.getLoader().load(
								{
									url : "forwardZuheManage.do",
									scripts : true,
									params : {
										templateName: orderName,
										templateVal : templateVal,
										templateModelId : itemplateid,
										templateTypeId : itempType,
										orderId : iid,
										iparent:2,
										fromLink:'ck'
									},
									callback : function(records, operation, success) {
//										showImg(url);
									}
								});
					}else if(templateVal=='26'){
						contentPanel.getLoader().load({
							url : "forwardCreateCloudOrderPage.do",
							scripts : true,
							params : {
								templateId : itemplateid,
								templateTypeId : itempType,
								orderId : iid,
								orderName : orderName,
								readOnly : true,
								iparent : 2
							},
							callback : function(records, operation, success) {
								showImg(url);
							}
						});
					}else if(templateVal=='24'){

						  contentPanel.getLoader().load({
								url : "orderManageMentDetail.do",
								scripts : true,
								params : {
									templateVal:'网络自动化变更',
									templateName : itempName,
									orderName : orderName,
									templateModelId : itemplateid,
									orderId : iid,
									fromLink:'ck',
									templateTypeId : templateVal
								},
								callback : function(records, operation, success) {
									showImg(url);
								}

							});
					}else if(templateVal=='1'){
						contentPanel.getLoader().load({
							url : "pass/forwardStartFlow.do",
							scripts : true,
							params : {
								templateVal:'作业调度',
								templateName : itempName,
								orderName : orderName,
								templateModelId : itemplateid,
								orderId : iid,
								fromLink:'ck',
								menuFrom:'monitor',
								templateTypeId : templateVal
							},
							callback : function(records, operation, success) {
								showImg(url);
							}

						});
					}else if (templateVal =='8'){
						contentPanel.getLoader().load(
							{
								url : "icSPDBCreateTaskPageForPass.do",
								scripts : true,
								params : {
									templateVal : "标准运维",
									templateName : itempName,
									orderName : orderName,
									templateModelId : itemplateid,
									templateTypeId : templateVal,
									orderId : iid,
									isInsert: "false",
									menuFrom:'monitor',
									fromLink:'ck',

									projectTypeId : templateVal,
									iProjectName : iinsName,
									isnomal : 0,
									isWhite:'false',
									isWhiteAndIp :false,
									viewOnly:'true',
									workitemId:itasksubid
								}
							});
					}else if(templateVal =='-4'){
						contentPanel.getLoader().load(
								{
									url : "zuheDisasterTemplate.do",
									scripts : true,
									params : {
										  templateVal : templateVal,
            							  templateModelId : itemplateid,
            							  templateTypeId : templateVal,
            							  isInsert: "true",
            							  isCreateOrder: "true",
            							  templateName : itempName,
            							  flag:true,
            							  fhFlag:2
									}
								});
					}else if (templateVal =='505'){
						var itoolTypeId = 0;
						Ext.Ajax.request({
							url: 'getToolsById.do',
							async: false,
							params: {
								iid: itasksubid
							},
							success: function (response, request) {
								itoolTypeId = Ext.decode(response.responseText).itoolTypeId;
							},
							failure: function (result, request) {
								secureFilterRs(result, "请求返回失败！", request);
							}
						});
						if (itoolTypeId == 0) {
							Ext.Msg.alert("提示", "未获取到工具信息");
							return;
						}
						var url = "";
						if (itoolTypeId == 3) {
							url = "toolalarmInfo/toJbToolsDetailPage.do";
							openDetailOrderWindow = Ext.create('Ext.window.Window', {
								title: '工具详情',
								autoScroll: true,
								modal: true,
								buttonAlign: 'center',
								draggable: false,// 禁止拖动
								resizable: false,// 禁止缩放
								width: contentPanel.getWidth() * 0.6,
								height: contentPanel.getHeight(),
								loader: {
									url: url,
									params: {
										iid: itasksubid,
										openPage: 'comtoolsDetail',
										orderId: iid
									},
									autoLoad: true,
									autoDestroy: true,
									scripts: true
								},
								listeners: {
									close: function () {
										//modelStore.reload();
									}
								}
							}).show();
						}
						if (itoolTypeId == 1) {
							url = "toolalarmInfo/toMessageDetailPage.do";
							openDetailOrderWindow = Ext.create('Ext.window.Window', {
								id: 'msDetailWindow',
								title: '工具详情',
								autoScroll: true,
								modal: true,
								buttonAlign: 'center',
								draggable: false,// 禁止拖动
								resizable: false,// 禁止缩放
								width: contentPanel.getWidth() * 0.6,
								height: contentPanel.getHeight(),
								loader: {
									url: url,
									params: {
										iid: itasksubid
									},
									autoLoad: true,
									autoDestroy: true,
									scripts: true
								},
								listeners: {
									close: function () {
										//modelStore.reload();
									}
								},
								buttons: [{
									xtype: 'button',
									textAlign: 'center',
									cls: 'Common_Btn',
									text: '关闭',
									handler: function () {
										var win = parent.Ext.getCmp('msDetailWindow');
										if (win) {
											win.close();
										}
									}
								}
								]

							}).show();
						}
						if (itoolTypeId == 2) {
							url = "toolalarmInfo/toComToolsDetailPage.do";
							openDetailOrderWindow = Ext.create('Ext.window.Window', {
								title: '组合工具详情',
								autoScroll: true,
								modal: true,
								buttonAlign: 'center',
								draggable: false,// 禁止拖动
								resizable: false,// 禁止缩放
								width: contentPanel.getWidth() * 0.9,
								height: contentPanel.getHeight(),
								loader: {
									url: url,
									params: {
										iid: itasksubid,
										openPage: 'comtoolsDetail'
									},
									autoLoad: true,
									autoDestroy: true,
									scripts: true
								},
								listeners: {
									close: function () {
										//modelStore.reload();
									}
								},
								buttons: [{
									xtype: 'button',
									textAlign: 'center',
									cls: 'Common_Btn',
									text: '关闭',
									handler: function () {
										openDetailOrderWindow.close();
									}
								}]

							}).show();
						}
					}else{
						//var url = "orderCreateDetailNew.do";
						var url = "orderMonitorDetail.do";
						contentPanel.getLoader().load({
							url : url,
							scripts : true,
							params : {
								menuFrom:'monitor',
								itempType : itempType,
								orderName : orderName,
								itempName : itempName,
								orderid : itasksubid,
								itemplateid :itemplateid
							},
							callback : function(records, operation, success) {
								showImg(url);
							}
						});
					}

				}

			}]
	    }
  ];

  var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
	    clicksToEdit: 2
  });

  function permissionExec(type,orderName,itempName,itemplateid,iid,uuid,ienvid){
	  Ext.Msg.confirm("请确认", "是否真的要立即执行工单？", function(button, text) {
	      if (button == "yes") {
	    	  Ext.Ajax.request({
	    		  url : 'operPermissionValid.do',
	    			method: 'POST',
	    			params:{
	    				iid : iid,
	    				uuid : uuid
	    			},
	    			success : function(response, text) {
	    				 var success = Ext.decode(response.responseText).success;
	    				 var message = Ext.decode(response.responseText).message;
	    				 if (success) {
	    					immedExec(type,orderName,itempName,itemplateid,iid,uuid,ienvid);
	    				 }else{
	    					Ext.Msg.show({
	    						title : '提示',
	    						msg : message,
	    						buttons : Ext.MessageBox.OK
	    					});
	    					orderStore.load();
	    				 }
	    			}

	    	  });
	      }
	    });


  }


  function immedExec(type,orderName,itempName,itemplateid,iid,uuid,ienvid){

		Ext.Ajax.request({
			url : 'immedOrderExec.do',
			params : {
				templateType :type ,
				orderName : orderName,
				templateName : itempName,
				itemplateid : itemplateid,
				iid : iid,
				uuid : uuid,
				ienvid : ienvid
			},
			method : 'post',
			success : function(response, text) {
			    var success = Ext.decode(response.responseText).success;
			    var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.show({
						title : '提示',
						msg : "执行成功",
						buttons : Ext.MessageBox.OK
					});
					orderStore.load();
				} else {
					Ext.Msg.show({
						title : '提示',
						msg : message,
						buttons : Ext.MessageBox.OK
					});
					orderStore.load();
				}
			},
			failure : function(result, request) {
				orderStore.load();
				secureFilterRs(result, "操作失败！");
			}
		});
  }

  /** 导入按钮* */
	var importButton = Ext.create ("Ext.Button",
	{
		id : 'importBtn_id',
	    cls : 'Common_Btn',
	    text : "导入",
	    handler : function(){

	    }
	});

	/** 左侧导出按钮* */
	var exportTM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '导出',
	    handler : exportOrderMonitorExcelInfo

	});

	//导出
	function exportOrderMonitorExcelInfo(){
		var record = orderGrid.getSelectionModel ().getSelection ();
		var iidStr = "";
		Ext.Array.each (record, function (recordObj)
		{
			iidStr += "," + recordObj.get ('cacheId');
		});
		/*if(iidStr.length<=0)
		{
			Ext.Msg.alert ('提示','请选择要操作的行！');
			return;
		}*/
		iidStr = iidStr.substr(1);
		window.location.href = "exportOrderMonitorExcelInfo.do?iidStr="+iidStr;
	}


	Ext.define("TemplateModel", {
        extend: "Ext.data.Model",
        fields: [{
            name: "iid", type: "string"
        }, {
            name: "iname", type: "string"
        }]
    });

	 var templateStore = Ext.create('Ext.data.Store', {
		model: "TemplateModel",
		autoLoad : true,
		autoDestroy : true,
	    data : [
	    	{"iid":"", "iname":"全部"},
	        {"iid":"1", "iname":"作业调度"},
	        {"iid":"8", "iname":"标准运维"},
	        {"iid":"4","iname":"灾备切换"},
	        {"iid":"27","iname":"组合模板"},
	        {"iid":"-4","iname":"灾切组合模板"},
			{"iid":"505","iname":"工具箱"}
	    ]
	});


	var templateTypeComBox = new Ext.form.field.ComboBox({
		displayField : 'iname',
        valueField: 'iid',
		width : '20%',
		labelWidth : 40,
	    fieldLabel: '类型',
		value:'',
		labelAlign : 'right',
		enableKeyEvents: true,
		editable : false,
	    forceSelection:true,
		//emptyText : "--请选择类型--",
		store : templateStore,
		queryMode :'local'
	});
	 //是否监控执行状态
	Ext.define("MonitorPerformStateModel", {
        extend: "Ext.data.Model",
        fields: [{
            name: "value", type: "string"
        }, {
            name: "name", type: "string"
        }]
    });

	var monitorPerformStateStore = Ext.create('Ext.data.Store', {
		model: "MonitorPerformStateModel",
		autoLoad : true,
		autoDestroy : true,
	    data : [
	        {"value":"1","name":"是"},
	        {"value":"2","name":"否"}

	    ]
	});

	var monitorPerformStateComBox = new Ext.form.field.ComboBox({
		displayField : 'name',
        valueField: 'value',
		labelAlign : 'right',
		width : '20%',
		padding : '0 0 5 0',
		fieldLabel: '监控执行状态',
		labelWidth : 95,
		enableKeyEvents: true,
	    forceSelection:true,
	    editable : false,
		//emptyText : "--请选择类型--",
		store : monitorPerformStateStore,
		queryMode :'local',
		value : '1'
	});

  var formMainPanel = Ext.create('Ext.form.Panel', {

		region : 'north',
	  	layout : 'anchor',
	  	buttonAlign : 'center',
	  	iselect : false,
	  	 bodyCls : 'x-docked-noborder-top',
		    baseCls:'customize_gray_back',
	    border: false,
	    dockedItems : [
	    	{
	  		  xtype : 'toolbar',
	  		  baseCls:'customize_gray_back',
	  		  items : [
	  		        {
	  	            	fieldLabel: '名称',
	  		            name:'itempName',
	  		            labelAlign : 'right',
	  		            width : '20%',
	  		            labelWidth : 40,
	  		            xtype: 'textfield'
	  	            },
	  	            {
	  	            	fieldLabel: 'ITIL单号',
	  		            name:'itsmno',
	  		            labelAlign : 'right',
	  		            width : '20%',
	  		            labelWidth : 70,
	  		            xtype: 'textfield'
	  	            },{
	  	            	fieldLabel: '工单名称',
	  		            name:'ordername',
	  		            labelAlign : 'right',
	  		            width : '20%',
	  		            labelWidth : 70,
	  		            xtype: 'textfield',
	  		            hidden : true
	  	            },templateTypeComBox,/*monitorPerformStateComBox,*/
	  	          {
	  		            text : '查询',
	  		            cls : 'Common_Btn',
	  		            handler : function(){
	  		            	queryBtnFun();
	  		            }
	  		      },{
	  		            text : '重置',
	  		            cls : 'Common_Btn',
	  		            handler : function(){
	  		            	resetFun();
	  		            }
	  		      },
		            exportTM
	  		  ]
	  	  }
		]
	});
	if(bhSwitch) {
		var orderGrid = Ext.create('Ext.ux.ideal.grid.Panel',{
			width: contentPanel.getWidth(),
			cls:'customize_panel_back',
			region : 'center',
			forceFit: true,
			store : orderStore,
			selModel : Ext.create('Ext.selection.CheckboxModel',{checkOnly:true}),
			plugins: [cellEditing],
			border : false,
			columnLines : true,
			columns : orderColumns,
			animCollapse : false,
			autoScroll:true,
			ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			listeners : {
				beforeedit : function(editor, e, eOpts){
					/*if(e.field == 'errcode' && e.record.data.errcode!=""){
                        return false;
                    }else{
                        return true;
                    }*/
					return true;
				}
			},
			features: [
				Ext.create('Ext.grid.feature.Grouping', {
					showGroupsText: 'ITIL单号',
					groupHeaderTpl: [
						'<div data-groupname="{name}">ITIL单号: {name:this.formatName} ({rows.length})',
						'</div>',
						{
							formatName: function(name) {
								return name;
							}
						},
					],
					// 设置初始分组是不是收起
					startCollapsed: true
				})
			]
		});
	}else{
		var orderGrid = Ext.create('Ext.ux.ideal.grid.Panel',{
			width: contentPanel.getWidth(),
			cls:'customize_panel_back',
			region : 'center',
			forceFit: true,
			store : orderStore,
			selModel : Ext.create('Ext.selection.CheckboxModel',{checkOnly:true}),
			plugins: [cellEditing],
			border : false,
			columnLines : true,
			columns : orderColumns,
			animCollapse : false,
			autoScroll:true,
			ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			listeners : {
				beforeedit : function(editor, e, eOpts){
					/*if(e.field == 'errcode' && e.record.data.errcode!=""){
                        return false;
                    }else{
                        return true;
                    }*/
					return true;
				}
			}
		});
	}

  orderStore.on('beforeload',function (store,options){
	  var itempName = formMainPanel.getForm().findField("itempName").getValue();
	  var itsmNo = formMainPanel.getForm().findField("itsmno").getValue();
	  var orderName = formMainPanel.getForm().findField("ordername").getValue();
	  var tempType = templateTypeComBox.getValue();
	  //var monitorPerformState = monitorPerformStateComBox.getValue();

	  Ext.apply(orderStore.proxy.extraParams,{
		  itempName:itempName,
		  itsmNo : itsmNo,
		  orderName : orderName,
		  iTemplateType : tempType
		 //, monitorPerformState:monitorPerformState
	  });
  });


  var mainPanel =  Ext.create('Ext.panel.Panel',{
      renderTo : "order_grid_area",
      border : false,
      layout : 'border',
      bodyPadding : 0,
      width : contentPanel.getWidth (),
	  height : contentPanel.getHeight ()-modelHeigth,
      items : [formMainPanel,orderGrid]
  });

  contentPanel.on('resize',function(){
	  mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
  })

  orderGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
	 // mainPanel.down('#delete').setDisabled(selections.length === 0);
  });


  /* 解决IE下trim问题 */
  String.prototype.trim = function() {
	  return this.replace(/(^\s*)|(\s*$)/g, "");
  };

  // 当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
	  contentPanel.getHeader().show();
	  Ext.destroy(orderGrid);
	  if(Ext.isIE){
		  CollectGarbage();
	  }
  });

  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
      return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  var beginTime = Ext.create('Go.form.field.DateTime',
	{
		fieldLabel: '预计执行时间',
		labelAlign: 'left',
		labelWidth : 100,
		columnWidth: .8,
		padding : '5',
	    emptyText : '',
	    id: 'nextstarttime1',
	    name : 'beginTime',
	    format : 'Y-m-d H:i:s',
	    width : '100%'
	});
  var dbRemarks = Ext.create ('Ext.form.field.TextArea',
	{
	  	padding : '5',
	    name:'dbRemarks',
	    columnWidth: .8,
	    labelWidth : 100,
	    fieldLabel : '发起说明',
	    width : '100%',
	    autoScroll: true
	});

  var addForm = Ext.create('Ext.form.FormPanel', {
		border: false,
      layout: 'anchor',
      collapsible : false,
		items : [
		{
          border: false,
          layout: 'column',
          margin: '5',
          items: [beginTime]
      },
      {
          border: false,
          layout: 'column',
          margin: '5',
          items: [dbRemarks]
      }]

	});
  	function queryBtnFun() {
		if (Ext.isIE) {
			CollectGarbage();
		}
		pagebar.moveFirst();
	}

  	function resetFun(){
  		 formMainPanel.getForm().findField("itempName").setValue ('');
  		 formMainPanel.getForm().findField("itsmno").setValue ('');
  		 formMainPanel.getForm().findField("ordername").setValue ('');
  		 monitorPerformStateComBox.setValue('1');
 		 templateTypeComBox.setValue('');
  	}

  	function reload(){
    	refreshTaskList();
	}
    function refreshTaskList(){
    	orderStore.reload();
    }
    refreshTaskList();

	function dmKillTaskExecute(itaskid){
		Ext.Ajax.request({
			url : 'killStandardTask.do',
			method : 'POST',
			params : {
				taskid : itaskid
			},
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					orderGrid.ipage.moveFirst();
					Ext.MessageBox.show({
						title : "提示",
						msg : "任务终止成功!",
						buttonText : {
							yes : '确定'
						},
						buttons : Ext.Msg.YES
					});
				} else {
					Ext.MessageBox.show({
						title : "提示",
						msg : "任务终止失败!",
						buttonText : {
							yes : '确定'
						},
						buttons : Ext.Msg.YES
					});
				}

			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}

});


function getITILDetail(bizId,name,applyType){
   // console.log(pmsfjnxticketLoginUrl+'?sso_token="'+fjToken+'"&bizId="'+bizId+'"');
    //pmsfjnxticketLoginUrl测试,先将pmsfjnxticketLoginUrl写死，真正上线屏蔽下行代码
   // pmsfjnxticketLoginUrl = "http://www.baidu.com";
	//window.open(pmsfjnxticketLoginUrl+'?sso_token="'+fjToken+'"&bizId="'+bizId+'"',  "_blank");

	//如果是应急模式
	if(applyType=='应急模式'){
		console.log("应急模式");
		console.log("url:"+pmsfjnxticketLoginUrl+",bizId："+bizId+",isUrgent:1");
		postOpenMonitorWindowWhitItilNo(pmsfjnxticketLoginUrl, bizId,'1', '_blank');
	}

	if(applyType=='ITIL模式'){
		console.log("ITIL模式");
		console.log("url:"+pmsfjnxticketLoginUrl+",bizId："+bizId+",isUrgent:0");
		postOpenMonitorWindowWhitItilNo(pmsfjnxticketLoginUrl, bizId,'0', '_blank');
	}

}

function postOpenMonitorWindowWhitItilNo(url, bizId,isUrgent, target){
	 var tempForm = document.createElement("form");
    tempForm.id = "tempForm";

    tempForm.method = "post";
    tempForm.action = url;
    tempForm.target = target; //打开方式
    tempForm.style.display = "none";

    var hideInput1 = document.createElement("input");
    hideInput1.type = "hidden";
    hideInput1.name = "bizId"
    hideInput1.value = bizId;

    var hideInput2 = document.createElement("input");
    hideInput2.type = "hidden";
    hideInput2.name = "appid"
 	 hideInput2.value = fjappid;



    var hideInput3 = document.createElement("input");
    hideInput3.type = "hidden";
    hideInput3.name = "sso_token"
    hideInput3.value = fjToken;


    var hideInput4 = document.createElement("input");
    hideInput4.type = "hidden";
    hideInput4.name = "isUrgent"
    hideInput4.value = isUrgent;


    var hideInput5 = document.createElement("input");
    hideInput5.type = "hidden";
    hideInput5.name = "menuId"
    hideInput5.value = manuId_fjnx_title;

    var hideInput6 = document.createElement("input");
    hideInput6.type = "hidden";
    hideInput6.name = "btn"
    hideInput6.value = btn_fjnx_title;

    tempForm.appendChild(hideInput1);
    tempForm.appendChild(hideInput2);
    tempForm.appendChild(hideInput3);
    tempForm.appendChild(hideInput4);
    tempForm.appendChild(hideInput5);
    tempForm.appendChild(hideInput6);
    document.body.appendChild(tempForm);

    tempForm.submit();

    document.body.removeChild(tempForm);
}
function dmShowTaskDetileRunning( itaskid,itaskName) {
	var	urlString = "dmShowTaskDetileRunning.do";
	var	titleString = "任务详情-【" + itaskName + "】";
	var messageWindow;
	if (messageWindow == undefined || !messageWindow.isVisible ())
	{
		messageWindow = Ext.create ('Ext.window.Window',
			{
				title :  titleString,
				modal : true,
				closeAction : 'destroy',
				constrain : true,
//		    autoScroll : true,
				autoScroll : false,
				width : contentPanel.getWidth (),
				height : contentPanel.getHeight ()+50 ,
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				layout : 'fit',
				loader :
					{
						url : urlString,
						autoLoad : true,
						scripts : true,
						params :
							{
								itaskid : itaskid,
								itaskName : itaskName,
								isendid:1,
								jsonDate:"",
								contentPanelHeight : contentPanel.getHeight(),
								windowScHeight : window.screen.height
							}
					}
			})
	}
	messageWindow.show ();

	messageWindow.on('close',function (){
		backBtnFun()
	});

	function backBtnFun()
	{
		var url='orderMonitor.do';
		destroyRubbish();
		contentPanel.getLoader().load({
			url: url,
			params: {},
			scripts: true
		});
	}

}

