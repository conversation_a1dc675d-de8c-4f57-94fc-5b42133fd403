package com.ideal.ieai.server.repository.sus.importexecl.validate;

import org.jetbrains.annotations.NotNull;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <ul>
 * <li>Title: AbsValidate.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2016-4-19
 */
public abstract class AbsValidate implements IfcValidatable
{
    protected int          cnt    = 0;
    protected StringBuffer msgBuf = new StringBuffer("");
    static int[] DAYS = { 0, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 };
    public static final boolean isGdmpYear(int year) {
        return year % 4 == 0 && (year % 100 != 0 || year % 400 == 0);
    }

    /**
     * <li>Description:判断属性是否为空</li>
     * 
     * <AUTHOR> 2016-4-19
     * @param property
     * @return return boolean
     */
    public boolean isPropertyNull ( String property )
    {
        boolean result = false;
        try
        {
            if (null == property || "".equals(property.trim()))
                result = true;
        } catch (Exception e)
        {
        }

        return result;
    }

    protected void setMsg ( String msg )
    {
        msgBuf.append(++cnt);
        msgBuf.append(msg);
    }
    
    /**
     * 判断是否为合法IP
     * @return the ip
     */
    public static boolean isboolIp(String ipAddress)
    {
        String ip = "([1-9]|[1-9]//d|1//d{2}|2[0-4]//d|25[0-5])(//.(//d|[1-9]//d|1//d{2}|2[0-4]//d|25[0-5])){3}"; 
        Pattern pattern = Pattern.compile(ip); 
        Matcher matcher = pattern.matcher(ipAddress); 
        return matcher.matches(); 
    }
    
    /**
     * <li>Description:端口校验</li> 
     * <AUTHOR>
     * 2016-4-19 
     * @param port
     * @return
     * return boolean
     */
    public static boolean isboolPort ( String port )
    {
        // 端口号验证 1 ~ 65535
        String regex = "^([1-9]|[1-9]\\d{3}|[1-6][0-5][0-5][0-3][0-5])$";
        return Pattern.matches(regex, port);
    }

    /**
     * 
     * @Title: isNumberSplit
     * @Description: TODO(判断传入的字符串是否为逗号分隔的数字)
     * @param inString
     * @return
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年5月5日 yunpeng_zhang
     */
    public static boolean isNumberSplit ( String inString )
    {
        boolean returnValue = true;
        String[] pre = inString.split(",");
        for (int i = 0; i < pre.length; i++)
        {
            try
            {
                Long.parseLong(pre[i]);
            } catch (Exception e)
            {
                returnValue = false;
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: isNumber
     * @Description: TODO(判断是否为数字)
     * @param inString
     * @return
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年8月1日 yunpeng_zhang
     */
    public static boolean isNumber ( String inString )
    {
        boolean returnValue = true;
        try
        {
            Integer.parseInt(inString);
        } catch (Exception e)
        {
            returnValue = false;
        }
        return returnValue;
    }

    /** 
    * @Description: 光大系统录入Excel时，对基础信息变更单号值进行解析校验 
    * @Param: [changeDesc] 
    * @return: boolean 
    * @Author: zuochao_wang
    * @Date: 2022/10/19 11:22
    */ 
    public  boolean isChangeFormat(String changeDesc){
        boolean returnValue = true;
        try{
            String[] arrStr = changeDesc.split("_");
            if(arrStr!=null&&arrStr.length!=5){
                returnValue = false;
            }else{
                if(isBigEnglishAndCode(arrStr[0])&&check(arrStr[3])&&check(arrStr[4])){
                    if(!"REQ".equals(arrStr[1])&&!"REQRST".equals(arrStr[1])){
                        returnValue = false;
                    }else{
                        if (arrStr[2].length() != 8)
                        {
                            returnValue = false;
                        }else{
                            if (!this.isValidDateYmd(arrStr[2]))
                            {
                                returnValue = false;
                            }
                        }
                    }
                }else{
                    returnValue = false;
                }
            }
        }catch (Exception e){
            returnValue = false;
        }

        return returnValue;
    }

    /** 
    * @Description: 增加校验年月日是否符合规定格式 
    * @Param: [date] 
    * @return: boolean 
    * @Author: zuochao_wang
    * @Date: 2022/10/19 11:21
    */ 
    public static boolean isValidDateYmd(String date) {
        try {
            int year = Integer.parseInt(date.substring(0, 4));
            if (year <= 0)
                return false;
            int month = Integer.parseInt(date.substring(4, 6));
            if (month <= 0 || month > 12)
                return false;
            int day = Integer.parseInt(date.substring(6, 8));
            if (day <= 0 || day > DAYS[month])
                return false;
            if (month == 2 && day == 29 && !isGdmpYear(year)) {
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
    * @Description: 该函数判断一个字符串是否包含标点符号（中文英文标点符号）。
     *    原理是原字符串做一次清洗，清洗掉所有标点符号。
     *    此时，如果原字符串包含标点符号，那么清洗后的长度和原字符串长度不同。返回false。
     *    如果原字符串未包含标点符号，则清洗后长度不变。返回true。
    * @Param: [s]
    * @return: boolean
    * @Author: zuochao_wang
    * @Date: 2022/10/20 8:12
    */
    public static boolean check(@NotNull String s) {

        String tmp = s;
        tmp = tmp.replaceAll("\\p{P}", "");
        if (s.length() != tmp.length()) {
            return false;
        }
        return true;
    }

    /**
    * @Description: 判断字符串是否是大写的二十六个英文字符组成
    * @Param: [str]
    * @return: boolean
    * @Author: zuochao_wang
    * @Date: 2022/10/20 8:11
    */
    public static boolean isBigEnglish(String str) {
        if (null == str) {
            return false;
        } else {
            Matcher matcher = Pattern.compile("[A-Z]*").matcher(str);
            return matcher.matches();
        }
    }

    /**
     * @Description: 判断字符串是否是英文字符和横杠组合
     * @Param: [str]
     * @return: boolean
     * @Author: zuochao_wang
     * @Date: 2022/11/15 15:30
     */
    public static boolean isBigEnglishAndCode(String str) {
        if (null == str) {
            return false;
        } else {
            Matcher matcher = Pattern.compile("[A-Z\\u4E00-]*").matcher(str);
            return matcher.matches();
        }
    }
}
