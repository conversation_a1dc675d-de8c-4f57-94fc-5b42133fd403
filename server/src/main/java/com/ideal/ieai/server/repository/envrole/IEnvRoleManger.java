package com.ideal.ieai.server.repository.envrole;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

import com.ideal.ieai.server.repository.RepositoryException;

/**
 * <ul>
 * <li>Title: EnvRoleManger.java</li>
 * <li>Description:环境_角色绑定Manager</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2015-12-28
 */
public interface IEnvRoleManger
{
  
    /**
     * @desc 绑定类型：
     * @member envs：角色与环境绑定。
     * @member users:角色与环境绑定 */
    enum bindType {envs,users};
    
    
    /**查询一个  '环境角色'*/
    public EnvRole queryEnvRoleDetail (String iid)throws RepositoryException;
    
    /**查询全部  '环境角色'*/
    public List<EnvRole> queryAllEnvRole()throws RepositoryException;
    
    /** 根据用户userid，查询该用户  角色权限下  的 ,绑定的 envid(环境id)*/
    public List<String> querySomeOneEnvs(String userid)throws RepositoryException;
    
    
    /**增加一个  '环境角色'*/
    public String addEnvRole(EnvRole eRole)throws RepositoryException;
    
    /**删除一个  '环境角色'
     * @return */
    public Map delEnvRole(String [] iids)throws RepositoryException;
    
    /**修改一个  '环境角色'*/
    public void modifyEnvRole(EnvRole eRole)throws RepositoryException;

    /**获得全部 待绑定的 选项
     * @throws RepositoryException */
    public List<EnvRoleBindBean> getAllMember(String _bindType,String envroleid) throws RepositoryException;
    /**获得 已经和envroleid 进行绑定的 选项*/
    public List<EnvRoleBindBean> getMember(String _bindType,String envroleid)throws RepositoryException;
    
    /**
     * <li>Description: 进行绑定操作</li> 
     * <AUTHOR>
     * 2015-12-28 
     * @param type 绑定的类型( envs：角色与环境绑定。 users:角色与环境绑定)
     * @param iid
     * return void
     */
    public boolean band ( bindType type, String envRoleIid, List<String> iid, Connection con )
            throws RepositoryException;
}
