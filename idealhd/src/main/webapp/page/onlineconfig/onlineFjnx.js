var creatCICDCronWin;
var extFjnxGridPanelStore;
Ext.onReady(function() {

    // 清理主面板的各种监听时间
    destroyRubbish ();
    Ext.tip.QuickTipManager.init ();
    Ext.QuickTips.init();

    var keyQuery = Ext.create('Ext.form.TextField', {
        name: 'key',
        emptyText: '请输入任务标识',
        width: '15%',
        xtype: 'textfield',
        listeners: {
            specialkey: function(field,e){
                if (e.getKey()==Ext.EventObject.ENTER){
                    panelGrid.ipage.moveFirst();
                }
            }
        }
    });

    var idesQuery = Ext.create('Ext.form.TextField', {
        name: 'key',
        emptyText: '请输入描述',
        //    labelWidth: 80,
        width: '15%',
        xtype: 'textfield',
        listeners: {
            specialkey: function(field,e){
                if (e.getKey()==Ext.EventObject.ENTER){
                    panelGrid.ipage.moveFirst();
                }
            }
        }
    });

    // 定义方案名称
    var dataModel  =  Ext.define('entegorconfig', {
        extend : 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'String'
        },{
            name: 'ikey',
            type: 'String'
        },{
            name: 'ivalue',
            type: 'String'
        },{
            name: 'ides',
            type: 'String'
        },
        {
            name: 'istatus',
            type: 'String'
        }]
    });
    /** 状态下拉选 **/
    var statusstore = Ext.create('Ext.data.Store',{
        fields:['id','name'],
        data:[{"id":"0","name":"开启"},
            {"id":"1","name":"关闭"}
        ]
    });
    extFjnxGridPanelStore = Ext.create('Ext.data.Store', {
        storeId: 'simpsonsStore',
        model:'entegorconfig',
        pageSize: 30,
        proxy: {
            type: 'ajax',
            url: 'getTimingFjnxList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    extFjnxGridPanelStore.on('beforeload', function(store, options) {
        var new_params = {
            keyPar : keyQuery.getValue().trim(),
            ides : idesQuery.getValue().trim(),
        };
        Ext.apply(store.proxy.extraParams, new_params);
    });

    extFjnxGridPanelStore.loadPage(1);
    function save() {
        var m = extFjnxGridPanelStore.getModifiedRecords();
        if (m.length == 0) {
            Ext.MessageBox.alert('提示', '没有需要保存的条目！');
            return
        } else {
            var lstAddRecord = new Array();
            var flag = true;
            Ext.each(m, function(record) {
                lstAddRecord.push(record.data);
                var ikey =  record.data.ikey.trim();
                var ides =  record.data.ides.trim();
                var ivalue =  record.data.ivalue.trim();

                if ("" == ikey || null == ikey) {
                    setMessage('开关配置项不可为空！');
                    flag = false;
                    return;
                }
                if ("" == ides || null == ides) {
                    setMessage('开关描述不可为空！');
                    flag = false;
                    return;
                }
                if(!isValidCronExpression(ivalue)){
                    setMessage(ikey+'：在线开关定时时间配置有误,请配置标准corn表达式！');
                    flag = false;
                }
            });
            if(flag)
            {
                Ext.Ajax.request({
                    url: 'saveTimingFjnx.do',
                    method: 'post',
                    params: {
                        jsonData: Ext.encode(lstAddRecord)
                    },
                    success: function(response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (true == success) {
                            QueryMessage()
                            Ext.Msg.alert('提示', '保存成功！')
                        } else {
                            Ext.Msg.alert('提示',  message);
                        }
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, '操作失败！')
                    }
                })
            }
        }
    }
    
    function QueryMessage() {
        extFjnxGridPanelStore.reload({
            params: {
                start: 0,
                limit: 30,
                keyPar: keyQuery.getValue().trim(),
                ides : idesQuery.getValue().trim()
            }
        })
        if (Ext.isIE) {
            CollectGarbage();
        }
        panelGrid.ipage.moveFirst();
    }
    var panelGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        cls:'customize_panel_back',
        cellTip : true,
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        region : 'center',
        padding : grid_space,
        store:extFjnxGridPanelStore,
        selModel : Ext.create('Ext.selection.CheckboxModel', {checkOnly: false}),
        viewConfig : {enableTextSelection:true},
        columns: [{
            text : '序号',
            width : 60,
            xtype : 'rownumberer'
        }, {
            text : 'id',
            dataIndex : 'iid',
            hidden : true
        },
        {
            text: '任务标识',
            dataIndex: 'ikey',
            width : '20%',
            editor: {
                readOnly : fjnxFlag,
                xtype: 'textfield'
            }
        },
        {
            text: '执行时间',
            dataIndex: 'ivalue',
            width : '15%',
            editor: {
                readOnly : fjnxFlag,
                xtype: 'textfield'
            }
        },
        {
            text: '默认值',
            dataIndex: 'idefault',
            width : '10%',
            hidden:fjnxFlag,
            editor: {
                xtype: 'textfield'
            }
        },
        {
            text: '描述',
            dataIndex: 'ides',
            width : '35%',
            editor: {
                xtype: 'textfield'
            }
        },{
                text: '状态',
                dataIndex: 'istatus',
                hidden:true,
                width : '5%',
                editor: new Ext.form.field.ComboBox({
                    id : 'istatuscom',
                    editable : false,
                    typeAhead:true,
                    valueField : 'id',
                    displayField : 'name',
                    queryMode : 'local' ,
                    store: statusstore,
                    lazyRender:true
                }) ,
                renderer :function(value,metadata,record){
                    var index = statusstore.find('id',value);
                    if(index!=-1){
                        return statusstore.getAt(index).data.name;
                    }
                }
            }]/*,
        listeners: {
            cellclick: function (grid, rowIndex, colIndex, cellEl, record, tr, rowIndex, e, eOpts) {
                // 检查是否点击了具有 cron-link 类的元素
                //if (e.target && e.target.className === 'cron-link') {
                    selectExecCron();
                //}
            }
        }*/
    });

    var formationConfigForm1 = Ext.create('Ext.form.Panel', {
        baseCls:'customize_gray_back',
        region : 'north',
        border : false,
        dockedItems : [
        {
            baseCls:'customize_gray_back',
            xtype: 'toolbar',
            dock: 'top',
            items: [keyQuery, idesQuery,{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: function() {
                    QueryMessage()
                }
            },{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '更改执行时间',
                handler: selectExecCron
            },{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '保存',
                handler: save
            }, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '重置',
                handler: function() {
                    keyQuery.setValue('');
                    idesQuery.setValue('');
                }
            }]
        }]
    });

    
    var mainPanel = Ext.create('Ext.panel.Panel',{
        //cls:'customize_panel_header_arrow',
        renderTo: 'lb_Ext_grid_Panel_fjnx',
       // border : false,
        layout : 'border',
        width : "100%",
        height : "100%",
        //header : false,
        //border : false,
        //bodyPadding : 0,
        height : contentPanel.getHeight() - modelHeigth,
        width : contentPanel.getWidth,
        items : [formationConfigForm1, panelGrid]
    });

    function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }

    function isValidCronExpression(cronExpression) {
        var isValid;
        Ext.Ajax.request({
            url: 'checkConfigValue.do',
            async: false,
            method: 'post',
            params: {
                ivalue: cronExpression
            },
            success: function(response, request) {
                isValid = Ext.decode(response.responseText).success;
            },
            failure: function(result, request) {
                secureFilterRs(result, '操作失败！')
            }
        })
        return isValid;
    }

    function selectExecCron()
    {
        var record = panelGrid.getSelectionModel ().getSelection ();
        if (record.length == 0)
        {
            Ext.Msg.alert ('提示', "请先选择您要操作的行!");
            return;
        }
        var jsonArray=[];
        Ext.each(record,function(item){
            if(item.data.iid!=""){
                jsonArray.push(item.data.iid);
            }
        });
        creatCICDCronWin = Ext.create('Ext.window.Window', {
            title : '周期任务参数设置',
            modal : true,
            id : 'creatCICDCronWin',
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
            width : contentPanel.getWidth() - 350,
            height : contentPanel.getHeight() - 30,
            draggable : false,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            loader : {
                url : 'cicdCronMainForSpdb.do',
                autoLoad : true,
                autoDestroy : true,
                scripts : true,
                params: {
                    onlineId : jsonArray.join(',')
                }
            }
        });
        creatCICDCronWin.show();
    }


});