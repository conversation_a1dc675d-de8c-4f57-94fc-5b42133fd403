/*
 * Copyright 2012-2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ideal.ieai.prv.provider.utils.springboot.convert;

import java.lang.annotation.*;
import java.time.Duration;

/**
 * Annotation that can be used to indicate the format to use when converting a
 * {@link Duration}.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Target({ ElementType.FIELD, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DurationFormat {

	/**
	 * The duration format style.
	 * @return the duration format style.
	 */
	DurationStyle value();

}
