var idsIn = new Array();
Ext.onReady(function() {
    Ext.QuickTips.init();
    Ext.define('MenuPermission', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'resourceGroupId',
            type : 'long'
        }, {
            name : 'resourceGroupName',
            type : 'string'
        },{
            name : 'agBsName',
            type : 'string'
        },{
            name : 'agBsTypeName',
            type : 'string'
        }, {
            name : 'permission',
            type : 'boolean'
        }]
    });

    var menuPermissionStore =Ext.create('Ext.data.TreeStore', {
        autoLoad : true,
        model : 'MenuPermission',
        proxy : {
            type : 'ajax',
            url : 'resourceGroupPermissionList.do?roleId='+roleId
        },
        // root : {
        //     expanded : true
        // },
        // listeners : {
        // 	load : function() {
        //
        // 	}
        // },
        // folderSort : true
    });


    menuPermissionStore.on('beforeload', function(store, options) {
        var new_params = {
            roleId : roleId,
            gname : groupName.getValue(), //分组名
            agBsId : agBsCombo.getValue(), //一级分类id
            agBsTypeId : agBsTypeCombo.getValue()//二级分类id
        };
        Ext.apply(menuPermissionStore.proxy.extraParams, new_params);
    });

    menuPermissionStore.on('load', function(store, options) {
        var records = Ext.getCmp('ptree').getChecked();
        Ext.Array.each(records, function(rec) {
            if (idsIn.indexOf(rec.get('resourceGroupId')) == -1){
                idsIn.push(rec.get('resourceGroupId'));
            }
        });
        // cHKBoxIdsSetChecked ();
    });

    var menuPColumns = [ {
        text : 'Agent分组编号',
        dataIndex : 'resourceGroupId',
        width : '10%',
        hidden : true
    }, {
        xtype: 'treecolumn',
        text : 'Agent分组名称',
        width : '30%',
        flex:1,
        dataIndex : 'resourceGroupName'
    },{
        text : '一级分类',
        dataIndex : 'agBsName',
        width : '30%'
    },{
        text : '二级分类',
        dataIndex : 'agBsTypeName',
        width : '30%'
    } ];
    var groupName = new Ext.form.TextField({
        name : 'groupName',
        fieldLabel : '分组名',
        emptyText : '--请输入分组名--',
        labelWidth: 58,
        labelAlign : 'right',
        width : '20%',
        listeners : {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    queryInfo();
                }
            }
        }
    });

    Ext.define('agBsModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'BSNAME', //  名称
            type : 'string'
        }, {
            name : 'IID', // 系统ID
            type : 'long'
        } ]
    });

    Ext.define('agBsTypeModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'BSTYPENAME', //  名称
            type : 'string'
        }, {
            name : 'BSTYPEID', // 系统ID
            type : 'long'
        } ]
    });

    var agBsStore = Ext.create('Ext.data.Store', {
        model :'agBsModel',
        autoLoad : true,
        proxy : {
            type : 'ajax',
            url : 'bsManager/getAgentGroupBsAll.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });

    var agBsCombo = Ext.create('Ext.form.field.ComboBox', {
        name : 'agBsName',
        queryMode : 'local',
        id : 'agBsName',
        fieldLabel : '一级分类',
        labelWidth : 65,
        //		padding : '5',
        displayField : 'BSNAME',
        valueField : 'IID',
        editable : false,
        emptyText : '--请选择一级分类--',
        store : agBsStore,
        width : '20%',
        triggerAction : "all",
        labelAlign : 'right',
        listeners : {
            change : function() {
                agBsTypeCombo.clearValue();
                agBsTypeCombo.applyEmptyText();
                agBsTypeCombo.getPicker().getSelectionModel().doMultiSelect([], false);
                if( this.value !="" && this.value != null){
                    agBsTypeStore.load({
                        params : {
                            fk : this.value
                        }
                    });
                }
            },
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    queryInfo();
                }
            }
        }
    });

    var agBsTypeStore = Ext.create('Ext.data.Store', {
        model : 'agBsTypeModel',
        autoLoad : false,
        proxy : {
            type : 'ajax',
            url : 'bsManager/getAgentGroupBsTypeByFk.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });

    agBsTypeStore.on('beforeload', function(store, options) {
        var new_params = {
            fk : 0
        };
        Ext.apply(agBsTypeStore.proxy.extraParams, new_params);
    });
    var agBsTypeCombo = Ext.create('Ext.form.field.ComboBox', {
        name : 'agBsType',
        //		padding : '5',
        id : 'agBsType',
        queryMode : 'local',
        fieldLabel : '二级分类',
        labelWidth : 65,
        //forceSelection : true, // 要求输入值必须在列表中存在
        displayField : 'BSTYPENAME',
        valueField : 'BSTYPEID',
        editable : false,
        labelAlign : 'right',
        emptyText : '--请选择二级分类--',
        store : agBsTypeStore,
        width : '20%',
        triggerAction : "all",
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    queryInfo();
                }
            }
        }
    });

    var menuPermissionGrid = Ext.create('Ext.tree.Panel', {
        width : '100%',
        height : contentPanel.getHeight() - 165,
        id:'ptree',
        store : menuPermissionStore,
        columns : menuPColumns,
        // collapsible: true,
        useArrows: true,
        rootVisible: false,
        multiSelect : true,
        singleExpand: true,
        dockedItems : [ {
            xtype : 'toolbar',border : false,
            items : [ groupName,agBsCombo,agBsTypeCombo,{
                text : '查询',
                cls : 'Common_Btn',
                handler : queryInfo
            },{
                text : '清空',
                cls : 'Common_Btn',
                handler : function(){
                    groupName.setValue();
                    agBsCombo.setValue();
                    agBsTypeStore.removeAll();
                    //agBsTypeCombo.setValue();
                }
            },'->',{
                text : '保存',
                cls : 'Common_Btn',
                handler : save
            }, {
                text : '全选',
                cls : 'Common_Btn',
                handler : function() {
                    var roonodes = Ext.getCmp('ptree').getRootNode();
                    findchildnode(roonodes);
                    function findchildnode(node) {
                        var childnodes = node.childNodes;
                        for (var i = 0; i < childnodes.length; i++) {
                            var rootnode = childnodes[i];
                            rootnode.set("checked", true);
                            if(rootnode.childNodes.length>0){
                                findchildnode(rootnode);
                            }
                        }
                    }
                    var records = Ext.getCmp('ptree').getChecked();
                    Ext.Array.each(records, function(rec) {
                        if (idsIn.indexOf(rec.get('resourceGroupId')) == -1){
                            idsIn.push(rec.get('resourceGroupId'));
                        }
                    });
                }
            }, {
                text : '全不选',
                cls : 'Common_Btn',
                handler : function() {
                    var roonodes = Ext.getCmp('ptree').getRootNode();
                    findchildnode(roonodes);
                    function findchildnode(node) {
                        var childnodes = node.childNodes;
                        for (var i = 0; i < childnodes.length; i++) {
                            var rootnode = childnodes[i];
                            var resId = rootnode.data.resourceGroupId;
                            if (idsIn.indexOf(resId) > -1){
                                idsIn.remove(resId);
                            }
                            rootnode.set("checked", false);
                            if(rootnode.childNodes.length>0){
                                findchildnode(rootnode);
                            }
                        }
                    }
                    // idsIn = new Array();
                    // idsIn.splice(0,idsIn.length);
                }
            } ]//, '->', '所属系统：', leftComboBox
        } ],
        listeners:{
            checkchange:function( node, checked, eOpts ){
                // getCHKBoxIds();
                var groId = node.data.resourceGroupId;
                if(checked){
                    if(idsIn.indexOf(groId)==-1){
                        idsIn.push(groId);
                    }
                }else{
                    if(idsIn.indexOf(groId)>-1){
                        idsIn.remove(groId);
                    }
                }
            }
        },
        renderTo : "role_permission_menu_area"
    });
    // menuPermissionStore.on('beforeload', function(store, options) {
    // 	var new_params = {
    // 		roleId : roleId
    // 	};
    // 	Ext.apply(menuPermissionStore.proxy.extraParams, new_params);
    // });
    contentPanel.on('resize', function() {
        menuPermissionGrid.setHeight(contentPanel.getHeight() - 104);
    });

    function queryInfo(){
        // getCHKBoxIds();
        menuPermissionStore.reload();
        // menuPermissionStore.on('load',function(){
        // 	cHKBoxIdsSetChecked ();
        // });
    }

    /**
     * 获得左侧树选中的复选框id
     *
     * @returns {String}
     */
    // function getCHKBoxIds() {
    // 	var records = Ext.getCmp('ptree').getChecked();
    // 	Ext.Array.each(records, function(rec) {
    // 		if (idsIn.indexOf(rec.get('resourceGroupId')) == -1){
    // 			idsIn.push(rec.get('resourceGroupId'));
    // 		}
    // 	});
    // }

    /**
     * 页面刷新复选框选中
     *
     * @param idsIn
     */
    function cHKBoxIdsSetChecked() {
        var node = Ext.getCmp('ptree').getRootNode();
        var childnodes = node.childNodes;
        for (var i = 0; i < childnodes.length; i++) {
            var rootnode = childnodes[i];
            var resId = rootnode.data.resourceGroupId;
            if (idsIn.indexOf(resId) > -1) {
                rootnode.set("checked", true);
            }else{
                rootnode.set("checked", false);
            }
        }
        // findchildnode(roonodes);
        // function findchildnode(node) {
        //     var childnodes = node.childNodes;
        //     for (var i = 0; i < childnodes.length; i++) {
        //         var rootnode = childnodes[i];
        //         var resId = rootnode.data.resourceGroupId;
        //         if (idsIn.indexOf(resId) > -1) {
        //         	rootnode.set("checked", true);
        // 		}else{
        // 			rootnode.set("checked", false);
        // 		}
        //     }
        // }
    }

    function save() {
        // var records = Ext.getCmp('ptree').getChecked();
        // Ext.Array.each(records, function(rec) {
        // 	if (idsIn.indexOf(rec.get('resourceGroupId')) == -1){
        // 		idsIn.push(rec.get('resourceGroupId'));
        // 	}
        // });
        Ext.Ajax.request({
            url : 'saveResourceGroupPermissionsById.do',
            method : 'POST',
            params : {
                jsonData : idsIn,
                roleId : roleId
            },
            success : function(response, request) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    menuPermissionStore.modified = [];
                    menuPermissionStore.reload();
                    Ext.Msg.alert('提示', '保存成功');
                } else {
                    Ext.Msg.alert('提示', '保存失败！');
                }
            },
            failure : function(result, request) {
                Ext.Msg.alert('提示', '保存失败！');
            }
        });
        // Ext.Array.each(records, function(rec) {
        // 	if (idsIn.indexOf(rec.get('resourceGroupId')) == -1){
        // 		idsIn.push(rec.get('resourceGroupId'));
        // 	}
        // });
    }

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function(obj, options, eOpts) {
            Ext.destroy(menuPermissionGrid);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });
});