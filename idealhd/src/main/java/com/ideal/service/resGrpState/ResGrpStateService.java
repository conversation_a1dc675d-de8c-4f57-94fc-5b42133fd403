package com.ideal.service.resGrpState;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ideal.controller.resourcemanage.exception.ServiceException;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.resGrpState.ResGrpStateManager;

public class ResGrpStateService
{
    
    public void resGroupStateChk (List<Map<String, String>> dbList, String username,Long userId ,int type) throws RepositoryException, ServiceException
    {
        //启动流程执行脚本
        List<Map<String,String>> flowList = startResStateCheckFlow(dbList,username,userId);
        //保存关系  
        ResGrpStateManager.getInstance().saveFlowStepRelation(flowList,type);
    }
    
    //获取全部资源的状态
    public void startChkProjectState( String username,Long userId ,String iids,int sysType) throws RepositoryException, ServiceException{
        //查询资源组信息
        List<Map<String, String>> list = ResGrpStateManager.getInstance().getResourceAgent(iids,sysType);
        List<Map<String,String>> flowList = startResStateCheckFlow(list,username,userId);
        //保存关系  
        ResGrpStateManager.getInstance().saveFlowStepRelation(flowList,sysType);
    }
    
    public List<Map<String,String>> startResStateCheckFlow(List<Map<String,String>> dbList,String userName,long userId) throws ServiceException{
        UserInfo userInfo = new UserInfo();
        userInfo.setFullName(userName);
        userInfo.setId(userId);
        long flowId = 0;
        String cmd = "";
        
        List<Map<String,String>> flowList = new ArrayList<Map<String,String>>();
        try
        {
            for(Map<String,String> map : dbList){
                int itype = Integer.parseInt(map.get("itype"));
                if(itype==0){
                    cmd = map.get("iscriptpath");
                    List args = new ArrayList();
                    args.add( map.get("iagent_ip") + ":" +  map.get("iagent_port"));
                    args.add(cmd);
                    flowId = Engine.getInstance().startFlow(userInfo,
                        "ExcelActExecModelDR", "ActExec_Check", args, new HashMap(),
                        null, map.get("iip"), null, false, null, null,Constants.IEAI_IEAI_BASIC,Constants.IEAI_EMERGENCY_SWITCH,false);
                    map.put("iflowid", String.valueOf(flowId));
                    map.put("type", "3");
                    flowList.add(map);
                }else{
                    String ip = map.get("irealip");
                    String iuser = map.get("iuser");
                    String ipasswd = map.get("ipasswd");
                    cmd = map.get("iscriptpath")+ " "+ip+" "+iuser +" "+ipasswd;
                    List args = new ArrayList();
                    args.add( map.get("iagent_ip") + ":" +  map.get("iagent_port"));
                    args.add(cmd);
                    flowId = Engine.getInstance().startFlow(userInfo,
                        "ExcelActExecModelDR", "ActExec_Check", args, new HashMap(),
                        null, map.get("iip"), null, false, null, null,Constants.IEAI_IEAI_BASIC,Constants.IEAI_EMERGENCY_SWITCH,false);
                    map.put("iflowid", String.valueOf(flowId));
                    map.put("type", "3");
                    flowList.add(map);
                }
            }
            
        } catch (Exception e)
        {
            throw new ServiceException("启动流程失败!"+e.getMessage());
        }
        return flowList;
    }
    //获取全部资源的状态
    public List<Map<String,String>> getDataCenter(int sysType) throws RepositoryException{
        return ResGrpStateManager.getInstance().getDataCenter(sysType);
    }

    public Object getProjectStateByDC ( String dcsname, int start, int limit,int sysType ) throws RepositoryException
    {
        return ResGrpStateManager.getInstance().getProjectStateByDC(dcsname, start, limit, sysType);
    }

    public Object getProjectState ( String dcsname, Long iprojectid, int sysType ) throws RepositoryException
    {
        return ResGrpStateManager.getInstance().getProjectState(dcsname, iprojectid, sysType);
    }
    public Map getResStateByDC(String dcsname,  int sysType) throws RepositoryException{
        return ResGrpStateManager.getInstance().getResStateByDC(dcsname, sysType);
    }
}
