package com.ideal.ieai.prv.provider.supperhcresult;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.apache.log4j.Logger;




/**
 * 
 * <ul>
 * <li>Title: NewHcCheckResultQueueHandler.java</li>
 * <li>Description:新巡检的巡检结果队列处理类</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020年10月16日
 */
public class SupperProviderHcCheckResultQueueHandler
{
    private static  final Logger log = Logger .getLogger(SupperProviderHcCheckResultQueueHandler.class);
    
    private BlockingQueue<SupperHcCheckResultQueueVo> supperProviderHcCheckResultQueue = null;
    
    private static SupperProviderHcCheckResultQueueHandler instance = null;

    private SupperProviderHcCheckResultQueueHandler() {
        this.supperProviderHcCheckResultQueue = new LinkedBlockingQueue<SupperHcCheckResultQueueVo>();
    }
    
    public static SupperProviderHcCheckResultQueueHandler getInstance(){
        if(null == instance){
            synchronized(SupperProviderHcCheckResultQueueHandler.class){
                if(null == instance){
                    instance = new SupperProviderHcCheckResultQueueHandler();
                }
            }
        }
        return instance;
    }
    
    
    public void putProviderSupperHcResultIntoQueue(SupperHcCheckResultQueueVo vo) {
        try {
            if (vo != null) {
                this.supperProviderHcCheckResultQueue.put(vo);
                log.info( "接收到Agent返回 巡检结果信息压入结果队列数据 ,当前队列中数据数："+ this.supperProviderHcCheckResultQueue.size());
            }
            
        } catch (Exception ex) {
            log.error("接收到Agent返回 巡检结果信息 压入任务队列数据失败", ex);
        }
    }

    public SupperHcCheckResultQueueVo getProviderSupperHcResultFromQueue() {
        try {
            SupperHcCheckResultQueueVo vo =  this.supperProviderHcCheckResultQueue.take();
            log.info("提交 巡检结果队列剩余数据 " + this.supperProviderHcCheckResultQueue.size());
            return vo;
        } catch (Exception ex) {
            log.error("提取 巡检结果队列数据失败", ex);
        }
        return null;
    }
    
    public int getProviderSupperHcResultFromQueueSize() {
        return this.supperProviderHcCheckResultQueue.size();
    }

    public boolean supperProviderHcResultQueueIsEmpty() {
        return this.supperProviderHcCheckResultQueue.isEmpty();
    }

}
