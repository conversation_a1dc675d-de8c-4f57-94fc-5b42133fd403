package com.ideal.ieai.server.repository.importexecl.validate;

import com.ideal.ieai.server.repository.importexecl.SUSExcelBaiscPacname;
import com.ideal.ieai.server.repository.importexecl.SUSExcelBaiscPro;
import com.ideal.ieai.server.repository.importexecl.SUSExcelCommon;
import com.ideal.ieai.server.repository.importexecl.SUSExcelBaisc;
import com.ideal.ieai.server.repository.importexecl.SUSExcelHostsOfEnv;

/**
 * <ul>
 * <li>Title: ValidateFactory.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2016-4-18
 */
public class ValidateFactory
{
    public static IfcValidatable getValidater ( SUSExcelCommon bean )
    {
        IfcValidatable validater = null;
        if (null != bean)
        {
            if (bean instanceof SUSExcelBaisc)
            {
                validater = new SUSExcelBaiscValidater();
            } else if (bean instanceof SUSExcelHostsOfEnv)
            {
                validater = new SUSExcelHostsOfEnvValidater();
            } else if (bean instanceof SUSExcelBaiscPacname)
            {
                validater = new SUSExcelBasicPacnameValidater();
            } else if (bean instanceof SUSExcelBaiscPro)
            {
                validater = new SUSExcelBasicProValidater();
            }

        }
        return validater;
    }
}
