	CREATE INDEX IDX_IEAI_ACTOUTPUT_01 ON IEAI_ACTOUTPUT (IACTID ASC);
	CREATE INDEX IDX_IEAI_ACTOUTPUT_02 ON IEAI_ACTOUTPUT (IACTSCOPEID ASC);
	CREATE INDEX IDX_IEAI_ACTRUNINFO_01 ON IEAI_ACTRUNINFO (IFLOWID ASC, IACTID ASC);
	CREATE INDEX IDX_IEAI_ACTRUNINFO_02 ON IEAI_ACTRUNINFO (IACTID ASC);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_01 ON IEAI_ACTRUNTIME (IACTID ASC);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_02 ON IEAI_ACTRUNTIME (IISMONITORACT ASC);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_03 ON IEAI_ACTRUNTIME (IFLOWID ASC, IACTID ASC);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_04 ON IEAI_ACTRUNTIME (IISMONITORACT ASC, ACTTYPECODE ASC, <PERSON><PERSON><PERSON><PERSON>EAR ASC, STATECODE ASC);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_05 ON IEAI_ACTRUNTIME (IACTNAME ASC);
	CREATE INDEX IDX_IEAI_ACTSTATES_01 ON IEAI_ACTSTATES (IACTID ASC);
	CREATE INDEX IDX_IEAI_ACTTIMECONFIG_01 ON IEAI_ACTTIMECONFIG (IACTID ASC);
	CREATE INDEX IDX_IEAI_ADAPTOR_01 ON IEAI_ADAPTOR (INAME ASC);
	CREATE INDEX IDX_IEAI_ADAPTOR_02 ON IEAI_ADAPTOR (IUUID ASC);
	CREATE INDEX IDX_IEAI_ATTACHMENT_01 ON IEAI_ATTACHMENT (IFLOWID ASC);
	CREATE INDEX IDX_IEAI_BRANCHINFO_01 ON IEAI_BRANCHINFO (IFLOWID ASC);
	CREATE INDEX IDX_IEAI_BRANCHINFO_02 ON IEAI_BRANCHINFO (ISTRUTINFOID ASC);
	CREATE INDEX IDX_IEAI_BRANCHSCOPE_01 ON IEAI_BRANCHSCOPE (IFLOWID ASC);
	CREATE INDEX IDX_IEAI_BRANCHSCOPE_02 ON IEAI_BRANCHSCOPE (IPARENTSCOPE ASC);
	CREATE INDEX IDX_IEAI_CALENDAR_01 ON IEAI_CALENDAR (INAME ASC);
	CREATE INDEX IDX_IEAI_ERRORTASKROLE_01 ON IEAI_ERRORTASKROLE (IERRORTASKID ASC);
	CREATE INDEX IDX_IEAI_ERRORTASKROLE_02 ON IEAI_ERRORTASKROLE (IROLEID ASC);
	CREATE INDEX IDX_IEAI_ERRORTASKUSER_01 ON IEAI_ERRORTASKUSER (IERRORTASKID ASC);
	CREATE INDEX IDX_IEAI_ERRORTASKUSER_02 ON IEAI_ERRORTASKUSER (IUSERID ASC);
	CREATE INDEX IDX_IEAI_EXECACT_01 ON IEAI_EXECACT (IFLOWID ASC, ISTATE ASC);
	CREATE INDEX IDX_IEAI_EXECACT_02 ON IEAI_EXECACT (IREXECREQUESTID ASC);
	CREATE INDEX IDX_IEAI_EXECACT_03 ON IEAI_EXECACT (ISTATE ASC, IID ASC);
	CREATE INDEX IDX_IEAI_EXECACT_04 ON IEAI_EXECACT (IFLOWID ASC, IACTID ASC, IREXECREQUESTID ASC);
	CREATE INDEX IDX_IEAI_EXECACT_05 ON IEAI_EXECACT (IACTID ASC);
	CREATE INDEX IDX_IEAI_EXECACT_06 ON IEAI_EXECACT (ISCOPEID ASC);
	CREATE INDEX IDX_IEAI_FLOWENV_01 ON IEAI_FLOWENV (IFLOWID ASC, IMARKED ASC);
	CREATE INDEX IDX_IEAI_HOLIDAY_01 ON IEAI_HOLIDAY (ICALID ASC);
	CREATE INDEX IDX_IEAI_IEXECERROR_01 ON IEAI_IEXECERROR (IEXECACTID ASC);
	CREATE INDEX IDX_IEAI_IEXECERROR_02 ON IEAI_IEXECERROR (IACTID ASC);
	CREATE INDEX IDX_IEAI_IROLEPERM_01 ON IEAI_IROLEPERM (IROLEID ASC, ITYPE ASC, IENABLED ASC);
	CREATE INDEX IDX_IEAI_NOTICE_01 ON IEAI_NOTICE (IBEGINTIME ASC);
	CREATE INDEX IDX_IEAI_NOTICE_02 ON IEAI_NOTICE (IINVALIDTIME ASC);
	CREATE INDEX IDX_IEAI_NOTICE_03 ON IEAI_NOTICE (IFLOWNAME ASC);
	CREATE INDEX IDX_IEAI_NOTICE_04 ON IEAI_NOTICE (IPROJECTNAME ASC);
	CREATE INDEX IDX_IEAI_NOTICE_05 ON IEAI_NOTICE (IVALIDTIME ASC);
	CREATE INDEX IDX_IEAI_NOTICECFMUSER_01 ON IEAI_NOTICECFMUSER (INOTICEID ASC);
	CREATE INDEX IDX_IEAI_NOTICECFMUSER_02 ON IEAI_NOTICECFMUSER (IUSERID ASC);
	CREATE INDEX IDX_IEAI_NOTICEOP_01 ON IEAI_NOTICEOP (IFLOWINSTANCEID ASC);
	CREATE INDEX IDX_IEAI_NOTICEOP_02 ON IEAI_NOTICEOP (INOTICEID ASC);
	CREATE INDEX IDX_IEAI_NOTICEOP_03 ON IEAI_NOTICEOP (ITASKNAME ASC);
	CREATE INDEX IDX_IEAI_OPERATION_01 ON IEAI_OPERATION (ITASKID ASC);
	CREATE INDEX IDX_IEAI_PROJECT_01 ON IEAI_PROJECT (INAME ASC, IUPLOADTIME DESC);
	CREATE INDEX IDX_IEAI_PROJECT_03 ON IEAI_PROJECT (IUUID ASC);
	CREATE INDEX IDX_IEAI_RECOVERYPOINT_01 ON IEAI_RECOVERYPOINT (IACTID ASC);
	CREATE INDEX IDX_IEAI_RECOVERYPOINT_02 ON IEAI_RECOVERYPOINT (IFLOWID ASC);
	CREATE INDEX IDX_IEAI_RECOVERYPOINT_03 ON IEAI_RECOVERYPOINT (ISCOPEID ASC);
	CREATE INDEX IDX_IEAI_REMOTEEXECACT_01 ON IEAI_REMOTEEXECACT (IFLOWNAME ASC);
	CREATE INDEX IDX_IEAI_REMOTEEXECACT_02 ON IEAI_REMOTEEXECACT (IACTID ASC);
	CREATE INDEX IDX_IEAI_REMOTEEXECACT_03 ON IEAI_REMOTEEXECACT (IFLOWID ASC);
	CREATE INDEX IDX_IEAI_REMOTEEXECACT_04 ON IEAI_REMOTEEXECACT (ISCOPEID ASC);
	CREATE INDEX IDX_IEAI_STRUCTINFO_01 ON IEAI_STRUCTINFO (IFLOWID ASC);
	CREATE INDEX IDX_IEAI_STRUCTINFO_02 ON IEAI_STRUCTINFO (ISCOPEID ASC, IFINISHED ASC);
	CREATE INDEX IDX_IEAI_TASKITEM_01 ON IEAI_TASKITEM (ITASKID ASC);
	CREATE INDEX IDX_IEAI_TASKPARAM_01 ON IEAI_TASKPARAM (ITASKID ASC);
	CREATE UNIQUE INDEX IDX_IEAI_USER_01 ON IEAI_USER (ILOGINNAME ASC);
	CREATE INDEX IDX_IEAI_USERPERM_01 ON IEAI_USERPERM (ITYPE ASC, IENABLED ASC, IPERMNAME ASC, IPRJADPNAME ASC);
	CREATE INDEX IDX_IEAI_WEEKLYDAY_01 ON IEAI_WEEKLYDAY (ICALID ASC);
	CREATE INDEX IDX_IEAI_WORKDAY_01 ON IEAI_WORKDAY (ICALID ASC);
	CREATE INDEX IDX_WORKFLOWINSTANCE_01 ON IEAI_WORKFLOWINSTANCE (IFLOWID ASC, ISTATUS ASC);
	CREATE INDEX IDX_WORKFLOWINSTANCE_02 ON IEAI_WORKFLOWINSTANCE (IPRJUUID ASC);
	CREATE INDEX IDX_WORKFLOWINSTANCE_03 ON IEAI_WORKFLOWINSTANCE (IPROJECTNAME ASC);
	CREATE INDEX IDX_WORKFLOWINSTANCE_04 ON IEAI_WORKFLOWINSTANCE (ISTATUS ASC);
	CREATE INDEX IDX_IEAI_WORKINGTIME_01 ON IEAI_WORKINGTIME (IPARENTID ASC);
	CREATE INDEX IDX_IEAI_WORKINGTIME_02 ON IEAI_WORKINGTIME (ITYPE ASC);
	CREATE INDEX IDX_EXECACT_DELAY_01 ON IEAI_EXECACT_DELAY (IACTID);
	CREATE INDEX IDX_EXECACT_DELAY_02 ON IEAI_EXECACT_DELAY (IFLOWID);
	CREATE UNIQUE INDEX IDX_IEAI_SYS_01 ON IEAI_SYS (SYSNAME ASC, PRJTYPE ASC);
	CREATE INDEX IDX_IEAI_COM_CHK_01 ON IEAI_COM_CHK (APPLOGO ASC);
	CREATE INDEX IDX_IEAI_COM_CHK_02 ON IEAI_COM_CHK (CPID ASC, STARTLOGO ASC);
	CREATE INDEX IDX_HD_CHECK_DATA_LAST_01 ON HD_CHECK_RESULT_DATA_LAST (CPID, COMCHKID);
	CREATE INDEX IDX_HD_CHECK_DATA_LAST_02 ON HD_CHECK_RESULT_DATA_LAST (IP, PORT);
	CREATE INDEX IDX_HD_CHECK_RESULT_DATA_CACHE_01 ON HD_CHECK_RESULT_DATA_CACHE (CPID);
	CREATE INDEX IDX_HD_CHECK_RESULT_DATA_CACHE_02 ON HD_CHECK_RESULT_DATA_CACHE (MEID, ISYSID, CIID);
	CREATE INDEX IDX_HD_CHECK_STATUS_LAST_01 ON HD_CHECK_STATUS_LAST (CPID);
	CREATE INDEX IDX_HD_CHECK_STATUS_CACHE_01 ON HD_CHECK_STATUS_CACHE(RSDID);
	CREATE INDEX IDX_HD_CHECK_STATUS_CACHE_02 ON HD_CHECK_STATUS_CACHE(CPID);
	CREATE INDEX IDX_HD_CHECK_STATUS_HIS_01 ON HD_CHECK_STATUS_HIS(RSDID);
	CREATE INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST (IP ASC);
	CREATE INDEX IDX_IEAI_COMPUTER_LIST_FLOWID ON IEAI_COMPUTER_LIST (FLOWID);
	CREATE UNIQUE INDEX IDX_IEAI_CHKITEM_01 ON IEAI_CHKITEM (ICHKITEMNAME ASC);
	CREATE INDEX IDX_IEAI_SUS_RUN_INSTANCEINFO_ITASKID ON IEAI_SUS_RUN_INSTANCEINFO (ITASKID ASC);
	CREATE INDEX IDX_IRUNINSID ON IEAI_RUNINFO_INSTANCE_HIS (IRUNINSID) ;
	CREATE INDEX IDX_HOST_PORT_FLOWID on TEMP_IEAI_REMOTEEXECACT (IAGENTHOST, IAGENTPORT, IFLOWID);
	CREATE INDEX SENDREMOTE_SEND_ID on IEAI_SENDREMOTE (SEND_ID);
	create index IDX_IACTRUNTIMEH_IACTNAME on IEAI_ACTRUNTIME_HISTORY (IACTNAME);
	create index IDX_IACTRUNTIMEH_IACTTYPE on IEAI_ACTRUNTIME_HISTORY (IACTTYPE);
	create index IDX_IACTRUNTIMEH_IBEGINEXCTIME on IEAI_ACTRUNTIME_HISTORY (IBEGINEXCTIME);
	create index IDX_IACTRUNTIMEH_IENDTIME on IEAI_ACTRUNTIME_HISTORY (IENDTIME);
	create index IDX_IACTRUNTIMEH_IFLOWID on IEAI_ACTRUNTIME_HISTORY (IFLOWID);
	create index IDX_IEAIWORKFLOWHISTORY_IFN on IEAI_WORKFLOWHISTORY (IFLOWNAME);
	create index IDX_WORKFLOW_AVGSTART on IEAI_WORKFLOW_AVGSTART (IPRJNAME, IFLOWNAME, IPRJUPPERID, IRELATIVESTIME);
	create index INX_ACTOTC_HIS_FLOWID on IEAI_ACTOUTPUTHISTORY (IFLOWID);
	CREATE INDEX INX_ACTOTC_FLOWID ON IEAI_ACTOUTPUTCICLE (IFLOWID);
	CREATE INDEX IDX_PROJECT_USER_NAME ON IEAI_PROJECT_USER (IPROJECTNAME);
	CREATE INDEX IDX_IEAI_ACTMONITOR_RUN ON IEAI_ACTMONITOR_RUN (IFLOWID,IMONITORID);
	CREATE INDEX IDX_IEAI_ACTMONITOR_RUNINFO ON IEAI_ACTMONITOR_RUNINFO (IFLOWID,IMONITORID,ISTATUS);
	CREATE INDEX INX_ACTRUNTIME_PART_ACTID ON IEAI_ACTRUNTIME_PART (IACTID); 
	CREATE INDEX INX_PART_FLOWID_ACTNAME ON IEAI_ACTRUNTIME_PART (IACTNAME, IFLOWID); 
	CREATE INDEX INX_RUNTIME_PART_FLOWID ON IEAI_ACTRUNTIME_PART (IFLOWID); 
	CREATE INDEX INX_RUNTIME_PART_FLOWID_ACTID ON IEAI_ACTRUNTIME_PART (IFLOWID, IACTID); 
	CREATE INDEX INX_ACT_RELATION_MESS ON IEAI_ACT_RELATION_MESS (IPROJECTNAME, IFLOWNAME, IACTNAME); 
	CREATE INDEX INX_ACT_TOPO_INSTANCE ON IEAI_ACT_TOPO_INSTANCE (IPROJECTNAME, IFLOWNAME, IACTNAME);
	CREATE INDEX IDX_EXCELMODEL_ACTSYSNAME_COPY ON IEAI_EXCELMODEL_COPY (IACTNAME, ISYSTEM);
	CREATE INDEX IDX_EXCELMODEL_IACTNAME_COPY ON IEAI_EXCELMODEL_COPY (IACTNAME);
	CREATE INDEX IDX_IEAI_ICLDPNAME_COPY ON IEAI_EXCELMODEL_COPY (ICHILDPRONAME);
	CREATE INDEX IDX_MODEL_ID_MAINNAME_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IMAINLINENAME);
	CREATE INDEX INDEXEXECLMODELFLAG_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IFLAG);
	CREATE INDEX INDEXEXECLMODEL_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IMAINLINENAME, IACTNAME);
	CREATE INDEX INDEXOPERIDNAME_COPY ON IEAI_EXCELMODEL_COPY (IOPERATIONID, IMAINLINENAME);
	CREATE INDEX INDEXPROACTNAME_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IACTNAME);
	CREATE INDEX INDEXUNIONALL_COPY ON IEAI_EXCELMODEL_COPY (IMAINPRONAME, IMAINLINENAME, IACTNAME, ICHILDPRONAME);
	CREATE INDEX IDX_TMP_ID_MAINNAME ON TMP_IEAI_EXCELMODEL (IOPERATIONID, IMAINLINENAME);
	CREATE INDEX IDX_TMP_OPERID ON TMP_IEAI_EXCELMODEL (IOPERATIONID);
	CREATE INDEX IDX_TMP_MAINNAME ON TMP_IEAI_EXCELMODEL (IMAINLINENAME);
	CREATE INDEX IDX_TMP_PROCHILDNAME ON TMP_IEAI_EXCELMODEL (IMAINPRONAME, ICHILDPRONAME);
	CREATE INDEX IDX_TMP_PROMAINLINENAME ON TMP_IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME);
	CREATE INDEX IDX_EXACTPRE_COPY ON IEAI_ACTPRE_COPY (IOPERATIONID);
	CREATE INDEX IDX_IEAI_ACTPRE_COPY ON IEAI_ACTPRE_COPY (IPREACTNAME, IPROJECTNAME);
	CREATE INDEX IDX_IEAI_ACTSUCC_COPY ON IEAI_ACTSUCC_COPY (ISUCCACTNAME, IPROJECTNAME);
	CREATE INDEX INDEXACTSUCC_COPY ON IEAI_ACTSUCC_COPY (IOPERATIONID);
	CREATE INDEX IDX_IEAI_FLOW_PARAM ON IEAI_FLOW_PARAM (IID);
	CREATE INDEX IDX_IEAI_SON_PROJECT ON IEAI_SON_PROJECT (IID);
	CREATE INDEX IDX_SYSTEMGROUP_GROUPID ON IEAI_SYSTEM_GROUP (ISYSGROUPNAME);
	CREATE INDEX IDX_EXCELMODEL_ACTSYSNAME ON IEAI_EXCELMODEL (IACTNAME, ISYSTEM);
	CREATE INDEX IDX_EXCELMODEL_IACTNAME ON IEAI_EXCELMODEL (IACTNAME);
	CREATE INDEX IDX_EXEXECLMODEL ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME, IACTNAME);
	CREATE INDEX IDX_EXEXECLMODELFLAG ON IEAI_EXCELMODEL (IMAINPRONAME, IFLAG);
	CREATE INDEX IDX_EXOPERIDNAME ON IEAI_EXCELMODEL (IOPERATIONID, IMAINLINENAME);
	CREATE INDEX IDX_EXPROACTNAME ON IEAI_EXCELMODEL (IMAINPRONAME, IACTNAME);
	CREATE INDEX IDX_EXUNIONALL ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME, IACTNAME, ICHILDPRONAME);
	CREATE INDEX IDX_IEAI_EXCELMODEL_ICLDPNAME ON IEAI_EXCELMODEL (ICHILDPRONAME);
	CREATE INDEX IDX_MODEL_ID_MAINNAME ON IEAI_EXCELMODEL (IMAINPRONAME, IMAINLINENAME);
	CREATE INDEX IDX_EXACTPRE ON IEAI_ACTPRE (IOPERATIONID);
	CREATE INDEX IDX_IEAI_ACTPRE_PREPN ON IEAI_ACTPRE (IPREACTNAME, IPROJECTNAME);
	CREATE INDEX IDX_EXACTSUCC ON IEAI_ACTSUCC (IOPERATIONID);
	CREATE INDEX IDX_IEAI_ACTSUCC_SUPN ON IEAI_ACTSUCC (ISUCCACTNAME, IPROJECTNAME);
	CREATE INDEX IDX_VALIDPART_IPRJNAME ON IEAI_ACTRUNTIME_VALIDPART (IPRJNAME);
	CREATE INDEX IDX_VALIDPART_NAME on IEAI_ACTRUNTIME_VALIDPART (IPRJNAME, IFLOWNAME, IACTNAME);
	CREATE INDEX ERRORINFO_PAR_IDX ON IEAI_ERRORINFO_PART (IBEGINTIME ,IPRJNAME,IFLOWNAME,IERRORTYPE,IACTTYPE);
	CREATE INDEX IDX_AVGRECORD_EVERYDAY ON IEAI_AVGRECORD_EVERYDAY(IPRJNAME, IACTNAME,IRECORDDAY);
	CREATE INDEX IDX_AVGRECORD_EVERYDAY_WEEK ON IEAI_AVGRECORD_EVERYDAY_WEEK(IPRJNAME, IACTNAME,IRECORDDAY);
	CREATE INDEX IDX_AVGRECORD_EVERYDAY_MON ON IEAI_AVGRECORD_EVERYDAY_MON(IPRJNAME, IACTNAME,IRECORDDAY);
	CREATE INDEX IDX_ACT_AVGTIME ON TMP_ACT_AVGTIME (IACTNAME);
	CREATE INDEX IDX_FLOWEND_FLAG_IACTNUM ON IEAI_FLOWEND_FLAG (IACTNUM);
	CREATE INDEX IDX_DEPRELACTION_ACTTYPE ON IEAI_ACT_DEPRELACTION (IACTTYPE);
	CREATE INDEX IDX_DEPRELACTION_CALLFLOWNAME ON IEAI_ACT_DEPRELACTION (ICALLFLOWNAME);
	CREATE INDEX IDX_DEPRELACTION_PRJFLOWACT ON IEAI_ACT_DEPRELACTION (IPROJECTNAME, IFLOWNAME, IACTNAME);
	CREATE INDEX IDX_PRE_RELACTION_PAR ON IEAI_ACT_PRE_RELACTION (IPARID);
	CREATE INDEX IDX_PRE_RELACTION_PRERE ON IEAI_ACT_PRE_RELACTION (IPRERELATION);
	CREATE INDEX IDX_SUCC_RELACTION_PAR ON IEAI_ACT_SUCC_RELACTION (IPARID);
	CREATE INDEX IDX_SUCC_RELACTION_SUCCRE ON IEAI_ACT_SUCC_RELACTION (ISUCCRELATION);
	CREATE INDEX INX_ACTRUNTIME_VAL_IFLOWID ON IEAI_ACTRUNTIME_VALIDPART(IFLOWID);
	CREATE INDEX INX_AGENTRES_AGENTNAME ON IEAI_AGENTRESOURCE (IAGENTNAME);
	CREATE INDEX INX_AGENTRES_PRJNAME ON IEAI_AGENTRESOURCE (IPRJNAME);
	CREATE INDEX IDX_VALIDDAYBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDDAY_BAK(IVALIDTIMEID);
	CREATE INDEX IDX_VALIDMONTHBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDMONTH_BAK(IVALIDTIMEID);
	CREATE INDEX IDX_VALIDWEEKBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDWEEK_BAK(IVALIDTIMEID);
	CREATE INDEX IDX_VALIDTIME_IMONITORINFOID ON IEAI_ACTMONITOR_VALIDTIME(IMONITORINFOID);
	CREATE INDEX IDX_MENU_BUTTON_IBUTTONID ON IEAI_MENU_BUTTON(IBUTTONID);
	CREATE INDEX IDX_TIMEBAK_IMONITORINFOID ON IEAI_ACTMONITOR_VALIDTIME_BAK(IMONITORINFOID);
	CREATE INDEX IDX_MONITORINFOBAK_IMONITORID ON IEAI_ACTMONITOR_INFO_BAK(IMONITORID);
	CREATE INDEX IDX_PERMISSION_IMENUBUTTONID ON IEAI_HIGHOPER_PERMISSION(IMENUBUTTONID);
	CREATE INDEX IDX_VALIDHOURBAK_IVALIDTIMEID ON IEAI_ACTMONITOR_VALIDHOUR_BAK(IVALIDTIMEID);
	CREATE INDEX IDX_ACTMONITORINFO_IMONITORID ON IEAI_ACTMONITOR_INFO(IMONITORID);
	CREATE INDEX IDX_WORKFLOWHISTORY_IFLOWID ON IEAI_WORKFLOWHISTORY(IFLOWID);
	CREATE INDEX IDX_FLOWID_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT(IFLOWID);
	CREATE INDEX IDX_ACTFINISHED_NAME ON IEAI_ACTFINISHED_FLAG (IDATADATE, IPRONAME, IFLOWNAME, IACTNAME);
	CREATE INDEX IDX_ACTLAST_FLOWID ON IEAI_ACT_LASTLINE (IFLOW_ID);
	CREATE INDEX IDX_SCHEDULERACT_STATE ON IEAI_SCHEDULER_ACT(IISSTATE);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_08 ON IEAI_ACTRUNTIME(ISTATE);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_07 ON IEAI_ACTRUNTIME(IERRORTASKID);
	CREATE INDEX IDX_IEAI_AGENT_MAINTAIN_TASK ON IEAI_AGENT_MAINTAIN_TASK (IREQUESTID);
	CREATE INDEX IDX_SCRIPT_ISSUERECORD_SJC ON IEAI_SCRIPT_ISSUERECORD (ISJC ASC);
	CREATE INDEX IDX_SCRIPT_ISSUERECORD_DATE ON IEAI_SCRIPT_ISSUERECORD (IDATE);
	CREATE INDEX IDX_ISTEPID_IEAI_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT (ISTEPID);
	CREATE INDEX IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID);
	CREATE INDEX IDX_TIMETASK_OUTPUT_IPID ON IEAI_TIMETASK_OUTPUT(IPID);
	CREATE INDEX IDX_TIMETASK_HIS_TASKID ON IEAI_TIMETASK_HIS (TASKID ASC);
	CREATE INDEX IDX_TIMETASK_IP_HIS_IP ON IEAI_TIMETASK_IP_HIS (IP ASC);
	CREATE INDEX IDX_TIMETASK_IP_HIS_TASKHISID ON IEAI_TIMETASK_IP_HIS (TASKHISID ASC);
	CREATE INDEX IDX_IEAI_INSTANCEINFO01 ON IEAI_INSTANCEINFO (IINSTANCEID);
	CREATE INDEX IDX_IEAI_SUS_ENV_INFO01 ON IEAI_SUS_ENV_INFO (IINSTANCEID);
	CREATE INDEX IDX_IEAI_ACTRUNTIME01 ON IEAI_ACTRUNTIME (ITASKID);
	CREATE INDEX IDX_IEAI_RUN_INSTANCE_HIS_01 ON IEAI_RUN_INSTANCE_HIS (ITASKID);
	CREATE INDEX IDX_IPRJUPPERID ON IEAI_ACTINFO (IPRJUPPERID);
	CREATE INDEX IDX_CMDB_IG_EQUI_RELATION_01 ON IEAI_CMDB_IG_EQUI_RELATION(RLCODE);
	CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_01 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,TOEIID);
	CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_02 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,FROMEIID);
	CREATE INDEX IDX_CMDB_IG_EQUIPMENT_01 ON IEAI_CMDB_IG_EQUIPMENT(EIID);
	CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_01 ON IEAI_CMDB_IG_ENTITY_MODEL(ESYSCODING);
	CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_02 ON IEAI_CMDB_IG_ENTITY_MODEL(ENAME);
	CREATE INDEX IDX_CMDB_IG_EQUIPMENT_02 ON IEAI_CMDB_IG_EQUIPMENT(ESYSCODING);
	CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_03 ON IEAI_CMDB_IG_FINAL_RELATION(FROMEIID,TOEIID);
	CREATE INDEX IDX_CMDB_IG_SYS_RELATION_01 ON IEAI_CMDB_IG_SYS_RELATION(IPRJUPPERID,CMDBEIID);
	CREATE INDEX IDX_CMDB_IG_EQUIPMENT_03 ON IEAI_CMDB_IG_EQUIPMENT(EIID,EIIP,EINAME);
 	CREATE INDEX IDX_IACTNAME ON IEAI_ACTINFO (IACTNAME);
 	CREATE INDEX IDX_IFLOWUPPERID ON IEAI_ACTINFO (IFLOWUPPERID);
 	CREATE INDEX IDX_ILASTID ON IEAI_ACTINFO (ILASTID);
 	CREATE INDEX IDX_IEAISUSITSMTASKIDREL_01 ON IEAI_SUS_ITSM_TASKID_REL (IITSM_TASKID);
 	CREATE INDEX IDX_IEAI_AUTOSINGLE_ACT_01 ON IEAI_AUTOSINGLE_ACT (REQID , IACTSTATE );
	CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_01 ON IEAI_ITSM_AUTO_CHILD (ISTATE , IP , ITASKID ,IINSTANCEID );
	CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_02 ON IEAI_ITSM_AUTO_CHILD ( ITASKID ,IENDTIME );
	CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_03 ON IEAI_ITSM_AUTO_CHILD ( ITASKID ,ISTARTTIME );
	CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_04 ON IEAI_ITSM_AUTO_CHILD (  ITASKID ,IINSTANCEID ,ISTATE );
	
	CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_01 ON TEMP_LOOKMONITOR_BASE (IPRJNAME);
	CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_02 ON TEMP_LOOKMONITOR_BASE (IID);
	CREATE INDEX IDX_IEAI_ACTSUCC_01 ON IEAI_ACTSUCC (IOPERATIONID,ISUCCACTNAME);
	CREATE INDEX IDX_IEAI_SCHEDULER_ACT_03 ON IEAI_SCHEDULER_ACT (IFLOWID,IACTNAME,IISSTATE);
	CREATE INDEX IDX_IEAI_SCHEDULER_ACT_04 ON IEAI_SCHEDULER_ACT (IFLOWID,IPRENUM,IISSTATE);
	CREATE INDEX IDX_IEAI_SCHEDULER_ACT_05 ON IEAI_SCHEDULER_ACT (IFLOWID,IFLOWINS,IPRENUM,IISSTATE,IOPERATIONID);
	CREATE INDEX IDX_IEAI_EXCELMODEL_01 ON IEAI_EXCELMODEL (IACTNAME,IMAINPRONAME);
	CREATE INDEX IDX_IEAI_ACTRUNTIME_09 ON IEAI_ACTRUNTIME (IFLOWID,IACTNAME,ISTATE);
	CREATE INDEX IDX_ODSACTFINISHED_FLAG_01 ON IEAI_ODSACTFINISHED_FLAG (IPRJID,IDATADATE,IPRONAME,IFLOWNAME,IACTNAME);
	CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_01 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IOPERATIONID);
	CREATE INDEX IDX_IEAI_KEYFLOW_INFO_01 ON IEAI_KEYFLOW_INFO (IFLOWRUNOWNID);
	CREATE INDEX IDX_IEAI_WORKFLOW_01 ON IEAI_WORKFLOW (IFLOWID,IPRJID,ISTATUS,ILATESTID);
	CREATE INDEX IDX_TEMP_IEAI_REMOTEEXECACT_01 ON TEMP_IEAI_REMOTEEXECACT (IFLOWID,IAGENTHOST,IAGENTPORT);
	CREATE INDEX IDX_IEAI_AGENTINFO_01 ON IEAI_AGENTINFO (IAGENT_IP,IAGENT_PORT);
	CREATE INDEX IDX_IEAI_FLOWDEF_01 ON IEAI_FLOWDEF (IFLOWNAME);
	CREATE INDEX IDX_IEAI_FLOWDEF_02 ON IEAI_FLOWDEF (IPRJID);
	CREATE INDEX IDX_IEAI_SYS_PERMISSION_01 ON IEAI_SYS_PERMISSION (IPROID,IPERMISSION,IROLEID);
	CREATE INDEX IDX_IEAI_WORKFLOW_JOBNUM_01 ON IEAI_WORKFLOW_JOBNUM (IDATADATE,IPRJNAME,IQUERY);
	CREATE INDEX IDX_IEAI_WORKFLOW_JOBNUM_02 ON IEAI_WORKFLOW_JOBNUM (IQUERY);
	CREATE INDEX IDX_IEAI_WORKFLOW_03 ON IEAI_WORKFLOW (IPRJID,ISTATUS);
	CREATE INDEX IDX_WORKFLOWINSTANCE_06 ON IEAI_WORKFLOWINSTANCE (IPROJECTNAME,IFLOWNAME,IFLOWINSNAME,IFLOWID,ISTATUS);
	
	CREATE INDEX IDX_TMP_EXECACT_NEW ON TMP_EXECACT_NEW (IUUID);
	CREATE INDEX IDX_TMP_EXEC_ACTNEXTINFO_NEW ON TMP_EXEC_ACTNEXTINFO_NEW (IUUID);
	CREATE INDEX IDX_TMP_QUERY_ACTNEXTINFO_NEW ON TMP_QUERY_ACTNEXTINFO_NEW (IUUID);
	CREATE INDEX IDX_TMP_MUTEX_INFO_NEW ON TMP_MUTEX_INFO_NEW (IUUID);
	
	CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD01 ON IEAI_ITSM_AUTO_CHILD (IINSTANCEID);
    CREATE INDEX IDX_IEAI_ACTRUNTIME ON IEAI_ACTRUNTIME (IERRORTASKID,IFLOWID,IACTNAME);
    CREATE INDEX IDX_IEAI_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT (IFLOWID,IFLOWNAME);
	CREATE INDEX IDX_IEAI_DATACOLLECT_SIZE_01 ON IEAI_DATACOLLECT_SIZE (IINSTID);
	
	CREATE INDEX IDX_IEAI_CALLWORKFLOW_INFO_02 ON IEAI_CALLWORKFLOW_INFO (ICALLFLOWID);
	CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_02 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IPRONAME,IFLOWNAME,IACTNAME);	
	CREATE INDEX IDX_IEAI_SCRIPT_FLOW ON IEAI_SCRIPT_FLOW (IWORKITEMID);
	CREATE INDEX IDX_IEAI_DOUBLECHECK_C_IWC  ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID,ICOLHEADER);
	CREATE INDEX IDX_IEAI_DOUBLECHECK_C_IWCH ON IEAI_DOUBLECHECK_COLVALUE_HIS (IWORKITEMID,ICOLHEADER);
	
	CREATE INDEX IDX_IEAI_WARNOPER_OPERTYPE ON IEAI_WARNOPER (IOPERNAME, IOPERUSER, IOPERTYPE);
    CREATE INDEX IDX_SCRIPT_FLOW_SCRIPTUUID ON IEAI_SCRIPT_FLOW (IMXSERVICEID);
    CREATE INDEX IDX_SCRIPT_ITSM_TASKUUID ON IEAI_SCRIPT_ITSMPUBLISH (TASKUUID);
    CREATE INDEX IDX_SCRIPT_ITSMHTML_TASKUUID ON IEAI_SCRIPT_CIB_HTMLFILE (ITASKUUID);
    CREATE INDEX IDX_IEAI_SCRIPT_FUJIAN_TEMP ON IEAI_SCRIPT_ATTACHMENT_TEMP (IWORKITEMID);
    CREATE INDEX IDX_SCRIPT_EXECTIME_UUID ON IEAI_SCRIPT_EXECTIME(SCRIPTUUID);
    CREATE INDEX IDX_SCRIPTDEPEND_SRCUUID ON IEAI_SCRIPT_DEPENDSCRIPT (SRCSCRIPTUUID);
	
	
	
	CREATE INDEX IDX_IEAI_ACTIVITY_PORT_ACTID ON IEAI_ACTIVITY_PORT(ACTID);
	CREATE INDEX IDX_IEAI_ACTIVITY_PORT_PORTID ON IEAI_ACTIVITY_PORT(PORTID);
	CREATE INDEX IDX_IEAI_COLLECT_SIZENEW_02 ON IEAI_DATACOLLECT_SIZENEW (IINSTID);
	CREATE INDEX IDX_IEAI_COLLECT_SIZENEW_03 ON IEAI_DATACOLLECT_SIZENEW (IDATETIME);
    CREATE INDEX IDX_SCRIPTS_COAT_WORKID ON IEAI_SCRIPTS_COAT(IWORKITEMID);

    CREATE INDEX IDX_I_SCRIPT_SCOPE_VARIABLE ON IEAI_SCRIPT_SCOPE_VARIABLE (IVARIABLE_ID, IBIND_ID);
    CREATE INDEX IDX_I_SCRIPT_SCOPE_VARIABLE_P ON IEAI_SCRIPT_SCOPE_VARIABLE_P (IVARIABLE_ID, IBIND_ID);

    CREATE INDEX IDX_IEAI_SCRIPT_SCOPE_FUNCLIB_PUB ON IEAI_SCRIPT_SCOPE_FUNCLIB_PUB (IFUNC_ID, IBIND_ID);
    CREATE INDEX IDX_IEAI_SCRIPT_SCOPE_FUNCLIB ON IEAI_SCRIPT_SCOPE_FUNCLIB (IFUNC_ID, IBIND_ID);

	CREATE INDEX IDX_MPS ON IEAI_MENU_PERMISSIONS (IMENUID, IROLEID, IPERMISSION);
	CREATE INDEX IDX_MENU_IPG ON IEAI_MENU (IID, IPARENTNAME, IGROUPMESSID);

    CREATE INDEX IDX_IEAI_MQ_CONSUMER_01 ON IEAI_MQ_CONSUMER (IFLOWID);
    CREATE INDEX IDX_IEAI_MQ_CONSUMER_02 ON IEAI_MQ_CONSUMER (IUUID);
    CREATE INDEX IDX_IEAI_MQ_CONSUMER_HIS_01 ON IEAI_MQ_CONSUMER_HIS (IFLOWID);
    CREATE INDEX IDX_IEAI_MQ_CONSUMER_HIS_02 ON IEAI_MQ_CONSUMER_HIS (IUUID);
    CREATE INDEX IDX_CITASK_VERSION ON IEAI_CITASK_VERSION (PROJ_ID,CITASKNAME);
    CREATE INDEX IEAI_CHECK_C_IINSTANCEID ON IEAI_CHECK_CHANGETYPE(IINSTANCEID);
    CREATE INDEX IEAI_CHECK_C_SDT ON IEAI_CHECK_CHANGETYPE(ISYSCODE,IDATE,ITAGONE);
	CREATE INDEX IDX_IEAI_INSTANCEINFO ON IEAI_INSTANCEINFO(IPARACHECK,ISYSTYPE);

	CREATE INDEX IDX_IEAI_INSTANCEINFO_IPS ON IEAI_INSTANCEINFO_IPS(INSTANCEINFO_ID);
	CREATE INDEX IDX_IEAI_RUNINFO_INSTANCE_IPS ON IEAI_RUNINFO_INSTANCE_IPS(INSTANCEINFO_ID);
	CREATE INDEX IDX_RUNINFO_INSTANCE_HIS_IPS ON IEAI_RUNINFO_INSTANCE_HIS_IPS(INSTANCEINFO_ID);
    CREATE INDEX IDX_IDX_IEAI_SCRIPT_SERVICES ON IEAI_SCRIPT_SERVICES(ISCRIPTUUID);
    CREATE INDEX IDX_IEAI_SCRIPT_TEST ON IEAI_SCRIPT_TEST(ISCRIPTUUID);

  CREATE INDEX IND_SUS_BASIC_INFO1 ON IEAI_SUS_BASIC_INFO(IPARAMETER);
	CREATE INDEX IND_DOUBLECHECK_WORKITEM_HIS1 ON IEAI_DOUBLECHECK_WORKITEM_HIS(ITASKID);
	CREATE INDEX IND_DOUBLECHECK_WORKITEM_HIS2 ON IEAI_DOUBLECHECK_WORKITEM_HIS(IEXECUSER);
	CREATE INDEX IND_DOUBLECHECK_WORKITEM_HIS3 ON IEAI_DOUBLECHECK_WORKITEM_HIS(IFLOWID);
    CREATE INDEX IDX_SS_BINDFUC_VAR_UID ON IEAI_SCRIPT_BIND_FUNC_VAR(ISCRIPTUUID);

	CREATE INDEX IDX_IUUID ON IEAI_TOOLS_AGENT_BIND (IUUID);
	CREATE INDEX IDX_IRESULT_ID ON IEAI_TOOLS_AGENT_BIND (IRESULT_ID);
	CREATE INDEX IDX_ISTATUS ON IEAI_TOOLS_AGENT_BIND (ISTATUS);
	CREATE INDEX IDX_AGENT_ID ON IEAI_TOOLS_AGENT_BIND (ITOOLS_AGENT_ID,IRESULT_ID);
    CREATE INDEX IDX_SCRIPT_INS_IMAINID ON IEAI_SCRIPT_INSTANCE (IMAINID);
    CREATE INDEX IDX_CR_GROUP_MAPPING_CPID ON IEAI_COMPUTER_GROUP_MAPPING(ICPID);
	CREATE INDEX IDX_IEAI_AGENTINFO_GROUP_01 ON IEAI_AGENTINFO_GROUP(INUM,INOWNUM,IGROUPID,INODEID);


    CREATE INDEX IDX_HC_ACTWARNING_01 ON IEAI_HC_ACTWARNING(CPID, IP);
    CREATE INDEX IDX_HC_ACTWARNING_02 ON IEAI_HC_ACTWARNING(IP);
    CREATE INDEX IDX_IEAI_SYS_RELATION_01 ON IEAI_SYS_RELATION(IP);
    CREATE INDEX IDX_IEAI_SYS_RELATION_02 ON IEAI_SYS_RELATION(SYSTEMID, IP);
    CREATE INDEX IDX_IEAI_SYS_PERMISSION_02 ON IEAI_SYS_PERMISSION (IROLEID);
    CREATE INDEX IDX_IEAI_SYS_PERMISSION_03 ON IEAI_SYS_PERMISSION(IPERMISSION);

	CREATE INDEX IDX_IEAI_AGENT_SDINFO_01 ON IEAI_AGENT_SDINFO(IAGENTINFOID);
	CREATE INDEX IDX_IEAI_AGENTUPDATE_01 ON IEAI_AGENTUPDATE_INFO(IAGENTUP_ID,ITRANSMISSION_ID);
	CREATE INDEX IDX_IEAI_PROXY_RELATION_01 ON IEAI_PROXY_LIST_RELATION(PROXYID);
	CREATE INDEX IDX_IEAI_COMPUTER_LIST_ID ON IEAI_COMPUTER_LIST(IAGENTINFO_ID);
	CREATE INDEX IDX_IEAI_UNIFY_SA_RELATION ON IEAI_UNIFYAGENT_SA_RELATION(ISYSID);
    CREATE INDEX IDX_IEAI_PROXY_LIST_RELATION ON IEAI_PROXY_LIST_RELATION(PROXYNEXTID,PROXYNEXTTYPE);

    CREATE INDEX IDX_TIMETASK_STATIS_INFOID_DATE ON IEAI_TIMETASK_EXEC_STATIS (CREATEDATE, TASKINFO_ID);

    CREATE INDEX IDX_ERRORTASK_01 ON IEAI_ERRORTASK (IFLOWID);
    CREATE INDEX IDX_WORKFLOWINSTANCE_07 ON IEAI_WORKFLOWINSTANCE (IPRJID, IFLOWID, IFLOWNAME, ISTATUS, ISTARTTIME);
    CREATE INDEX IDX_IEAI_WORKFLOW_IPRJUPPERID ON IEAI_WORKFLOW (IPRJUPPERID);
    CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_03 ON TEMP_LOOKMONITOR_BASE(IPRJUPPERID);
    CREATE INDEX IDX_IEAI_PROJECT_04 ON IEAI_PROJECT (PROTYPE);
    CREATE INDEX IDX_IEAI_ACT_PRIORITY ON IEAI_ACT_PRIORITY (IACTNAME, IFLOWNAME, IPRJNAME);
    CREATE INDEX IDX_WORKFLOW_SYSTEM_RECORD_01 ON IEAI_WORKFLOW_SYSTEM_RECORD (IDATADATE, IOPERTYPE, ISTAUS, ISYSTEMNAME);
    CREATE INDEX IDX_IEAI_AGENTDETAIL_01 ON IEAI_AGENT_DETAIL (AGENTIP, AGENTPORT, ICREATETIME);
    CREATE INDEX IDX_MQ_DETAIL_01 ON IEAI_MQ_DETAIL (IJOBNAME, ICHILDNAME);
    CREATE INDEX IDX_MQ_DETAIL_02 ON IEAI_MQ_DETAIL (ISTATUS);
    CREATE INDEX IDX_MQ_DATE_01 ON IEAI_MQ_DATE (IDATETYPE);
    CREATE INDEX IDX_MQ_AVGTIMEINFO_01 ON IEAI_MQ_AVGTIMEINFO (IJOBNAME,ICHILDNAME);
    CREATE INDEX IDX_MQ_AVGTIMEINFO_02 ON IEAI_MQ_AVGTIMEINFO (IDATETYPE);
    CREATE INDEX IDX_IEAI_MQ_APP_JAIL_01 ON IEAI_MQ_APP_JAIL (IJAILFLAG,IJAILTYPE,IJAILSTATUS);
    CREATE INDEX IDX_IEAI_MQ_APP_JAIL_02 ON IEAI_MQ_APP_JAIL (ICHILDNAME,IJOBNAME);
    CREATE INDEX IDX_IEAI_MQ_DETAIL_01 ON IEAI_MQ_DETAIL (ISTATUS);
    CREATE INDEX IDX_IEAI_MQ_DETAIL_02 ON IEAI_MQ_DETAIL (ISTARTTIME);
    CREATE INDEX IDX_IEAI_MQ_DETAIL_03 ON IEAI_MQ_DETAIL (ICHILDNAME,IJOBNAME);
    CREATE INDEX IDX_IEAI_MQ_CONSUMER_04 ON IEAI_MQ_CONSUMER(IJOBSTATUS);
    CREATE INDEX IDX_IEAI_ACTFINISHED_FLAG_NEW_IFLOWID ON IEAI_ACTFINISHED_FLAG_NEW (IFLOWID);
	CREATE INDEX IDX_DOUBLEC_WORKITEM_ISTATE ON IEAI_DOUBLECHECK_WORKITEM (ISTATE);
    CREATE INDEX IDX_DOUBLEC_WORK_IITEMTYPE ON IEAI_DOUBLECHECK_WORKITEM (IITEMTYPE);
    CREATE INDEX IDX_DOUBLEC_WORK_ISTARTTIME ON IEAI_DOUBLECHECK_WORKITEM (ISTARTTIME);
    CREATE INDEX IDX_DOUBLEC_WORK_FINISHTIME ON IEAI_DOUBLECHECK_WORKITEM (IFINISHEDTIME);
    CREATE INDEX IDX_DOUBLEC_WORK_AUDITYPE ON IEAI_DOUBLECHECK_WORKITEM (IAUDITYPE);
    CREATE INDEX IDX_IEAI_DOUBLEC_WORKI_IAID ON IEAI_DOUBLECHECK_WORKITEM (IRESOURCE_APPLY_ID);
	CREATE INDEX IDEX_SC_RUN_I_ATID ON IEAI_SCRIPT_RUNTIME_INFO (IEXECATID);
    CREATE INDEX IDEX_SC_RUN_I_WID ON IEAI_SCRIPT_RUNTIME_INFO (WORKFLOWID);
    CREATE INDEX IDEX_SC_RUN_INFO ON IEAI_SCRIPT_RUNTIME_INFO (ISCRIPTFLOWID);
	CREATE INDEX IDX_STD_TASK_RELA ON IEAI_STANDARD_TASK_RELATION (IWORKITEMID);
    CREATE INDEX IDX_IEAI_S_T_R_TASKID ON IEAI_STANDARD_TASK_RELATION (ITASKID);
	CREATE INDEX IDX_IEAI_TIMETASK_IP ON IEAI_TIMETASK_IP (TASKINSID, TASKIPSTATE);