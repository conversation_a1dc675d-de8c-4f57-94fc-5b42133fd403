var sysStorageStore;
var sysStorageGrid;
var checkedIid = [];
function showAlarmType(sysId,count,compositionRelationships) {
	
	Ext.define('sysStorageModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'long'
		}, {
			name : 'iname',
			type : 'string'
		},{
			name : 'ikeyword',
			type : 'string'
		}, {
			name : 'iplatform',
			type : 'string'
		}, {
			name : 'icreateuser',
			type : 'string'
		}, {
			name : 'icreatetime',
			type : 'string'
		},{name: 'checked', type:'boolean'}
		]
	});


	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',
    {
	    checkOnly : true
//		mode : 'SIMPLE',
//		listeners : {
//			beforeselect:function( me, record, index, eOpts ){
//				me.deselectAll();
//			}
//		}
    });
	sysStorageStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		//			    remoteSort: true,
		model : 'sysStorageModel',
//		pageSize : 30,
		proxy : {
			type : 'ajax',
			//url : 'infoSysCapacity/querySysStoreList.do',
			url : 'faultSelfHealing/getAlarmTypeList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	sysStorageStore.on('beforeload', function(store, options) {
		var newparams = {
				sysId : sysId,
				sname : inameQuery.getValue().trim(),
				iplatform :Ext.getCmp("selPlatform").getValue(),//获取报警平台下拉选数据
				icreateuser:icreateuserAlarmTypeQuery.getValue().trim()
		};

		Ext.apply(store.proxy.extraParams, newparams);

	});
	sysStorageStore.on ('load', function (store, options)
			{
                checkedIid = [];
				//选中ip，复选框勾选
				for (var i = 0; i < store.getCount (); i++)
				{
					var record = store.getAt (i);
					if(true==record.data.checked){
                        checkedIid.push(record.data.iid);
						selModel.select(record,true,false);
					}
				}
			});
	/** 组名查询输入框* */
	var taskNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    emptyText : '--请输入任务名称--',
	    labelWidth : 80,
	    width : '30%',
	    xtype : 'textfield'
	});
	/** 查询按钮* */
	var taskQryButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "查询",
//	    handler : query
	    handler : function()
	    {
	    	groupGrid.ipage.moveFirst();
	    }
	});
	var addButton = Ext.create("Ext.Button", {
		text : '增加',
		cls : 'Common_Btn',
		handler : function() {
			addCfg();
		}
	});
	var saveButton = Ext.create("Ext.Button", {
		text : '保存',
		cls : 'Common_Btn',
		handler : function() {
			saveCfg();
		}
	});
	var delButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		text : '移除',
		handler : function(){
			delAlarmTypeCfgs();
		} 
	});
	var bindButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		text : '绑定报警组合',
		handler : function(){
			if(parseInt(count) >0){
				Ext.MessageBox.alert("提示", "该场景已绑定自愈方案,不允许修改！");
				return;
			}
			saveSysRecords(sysId,compositionRelationships);
		} 
	});
	/** 根据报警类型名称查询**///文本框
	var inameQuery = Ext.create('Ext.form.TextField', {
		//默认文本
		emptyText : '----请输入报警类型名称----',
		width : 200,
		xtype : 'textfield',
		listeners: {  
			specialkey: function(field,e){
				if (e.getKey()==Ext.EventObject.ENTER){
					var sname = field.getValue()==null?"":field.getValue();
					sysStorageStore.load({ //请求
						params: {sname:sname.trim()}
					});
				}  
			}  
		}
	});
	
	var DStore = Ext.create('Ext.data.Store', {
		fields : [ 'text', 'value' ],
		data : [ 
		   {'text' : '巡检','value' : '巡检'
		}, {'text' : 'OVO','value' : 'OVO'
		}, {'text' : '蓝鲸报警','value' : '蓝鲸报警'
		}]
	});
	var coCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : DStore,
		forceSelection : true, // 要求输入值必须在列表中存在
		/*typeAhead : true,*/ // 允许自动选择
		displayField : 'text',
		valueField : 'value',
		triggerAction : "all"
	});
	//自定义按钮
	var queryButton = Ext.create("Ext.Button", {
		//按钮显示名称
		text : '查询',
		cls : 'Common_Btn',
		handler : function() {
			queryBtnFun();
		}
	});
	var resetButton = ("Ext.Button", {
		//按钮显示名称
		text : '重置',
		cls : 'Common_Btn',
		handler : function() {
			inameQuery.setValue('');
			Ext.getCmp("selPlatform").setValue('');
		}
	});
	/** 报警平台查询条件下拉框 **/
	var iplatformQuery = Ext.create('Ext.form.field.ComboBox', {
		//默认文本
		name : 'selPlatform',
		id:'selPlatform',
		emptyText : '----请选择报警平台----',
		width : 200,
		store: DStore,
		queryMode : 'local',
	    valueField: 'value',
	    displayField: 'text',
	    typeAhead : true,
		forceSelection:true,
		listeners: {  
			specialkey: function(field,e){
				if (e.getKey()==Ext.EventObject.ENTER){
					var iplatform = field.getValue()==null?"":field.getValue();
					sysStorageStore.load({ //请求
						sysStorageStore: {iplatform:iplatform.trim()}
					});
				}  
			}  
		}
	});
	
	/** 根据报警类型名称查询**///文本框
	var icreateuserAlarmTypeQuery = Ext.create('Ext.form.TextField', {
		//默认文本
		emptyText : '----请输入创建人----',
		width : 200,
		xtype : 'textfield',
		listeners: {  
			specialkey: function(field,e){
				if (e.getKey()==Ext.EventObject.ENTER){
					var  icreateuser = field.getValue()==null?"":field.getValue();
					sysStorageStore.load({ //请求
						params: { icreateuser: icreateuser.trim()}
					});
				}  
			}  
		}
	});
	sysStorageGrid = Ext
			.create(
					'Ext.ux.ideal.grid.Panel',
					{
						width : '100%',
						height : '100%',
						cls:'customize_panel_back',
						ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',   
						store : sysStorageStore,
						selModel : selModel,
						border : true,
						columnLines : true,
						flex : 2,
						columns : [{
							text : '序号',
							width:40,
							xtype : 'rownumberer'
						},{
							text : 'id',
							dataIndex : 'iid',
							hidden : true
						},{
							text : '报警类型',
							flex : 1,
							dataIndex : 'iname',
							//文本框
							editor: new Ext.form.TextField({ 
					 			/*regex:/^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
					 			regexText: "报警类型格式不正确"*/
				 			 })
						},{
							text : '报警关键字',
							flex : 1,
							dataIndex : 'ikeyword',
							editor: new Ext.form.TextField({ 						
								/*regex:/^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
					 			regexText: "报警关键字格式不正确"*/
					 		})
						},{
							text : '报警平台',
							flex : 1,
							dataIndex : 'iplatform',
							editor : coCombo
						},{
							text : '创建人',
							flex : 1,
							dataIndex : 'icreateuser',
						},{
							text : '创建时间',
							flex : 1,
							dataIndex : 'icreatetime',
						}],
						dockedItems : [ {
							xtype : 'toolbar',
							items : [inameQuery,iplatformQuery,icreateuserAlarmTypeQuery,queryButton,resetButton,'->', addButton,saveButton,delButton,bindButton ]
						} ],
						collapsible : false
					});
	
	//查询点击方法
	function queryBtnFun() {
		//如果是IE浏览器，释放内存。
		if (Ext.isIE) { 
			CollectGarbage();
		}
		//移动到第一页
		sysStorageGrid.ipage.moveFirst();
	}

	var win = Ext.create('widget.window', {
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		modal : true,
		title : '报警类型',
		closable : true,
		//			    closeAction : 'hide',
		closeAction : 'destroy',
		// animateTarget: this,
		width : contentPanel.getWidth ()-200,
		height : contentPanel.getHeight ()-150,
		layout : 'border',
		items : [ sysStorageGrid ]
	});
	win.show();
}

function addSysCapacityRow() {
	sysStorageStore.insert(0, new sysStorageModel());
	sysStorageGrid.getView().refresh();
}

function queryWhereSys() {
	sysStorageGrid.ipage.moveFirst();
}

function saveSysRecords(sysId,compositionRelationships) {
	var m = sysStorageGrid.getSelectionModel ().getSelection ();
    var selectnow = [];//现在页面选中的
    for (let i = 0; i < m.length; i++) {
		var id = m[i].data.iid;
        selectnow.push(id);
    }
    var inAll = [];
    for (let i = 0; i < checkedIid.length; i++) {
        if (selectnow.indexOf(checkedIid[i]) > -1) {
            inAll.push(checkedIid[i]);
        }
    }
    var shouldDelete = checkedIid;
    for (let i = 0; i < inAll.length; i++) {
        shouldDelete.remove(inAll[i])
    }
    var checkIds;
    var nocheckIds;
    selectnow = Array.from(new Set(selectnow));
    for (var i = 0, len = selectnow.length; i < len; i++) {
        if (i == 0) {
            checkIds = selectnow[0]
        } else {
            checkIds = checkIds + "," + selectnow[i]
        }
    }
    for (var i = 0, len = shouldDelete.length; i < len; i++) {
		if(i==0){
            nocheckIds = shouldDelete[i]
		}else{
            nocheckIds = nocheckIds + "," + shouldDelete[i]
		}
	}
	Ext.Ajax.request({
		url : 'faultSelfHealing/saveAlarmTypeList.do',
		method : 'post',
		params : {
			sysId : sysId,
			checkIds : checkIds,
            nocheckIds: nocheckIds,
			compositionRelationships:compositionRelationships
		},
		success : function(response, request) {
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (success) {
				queryWhereSys();
				Ext.Msg.alert("提示", message);
			} else {
                queryWhereSys();
				Ext.Msg.alert("提示", message);
			}
		},
		failure : function(result, request) {
            queryWhereSys();
			secureFilterRs(result, "操作失败！", request);

		}
	});
}
//增加点击方法
function addCfg(){
	var p = {
			iid:0,
			iname:'',
			iage:'',
			ikeyword:'',
			iplatform:'',
			icreateuser:loginName
	}
	sysStorageStore.insert(0,p);
}
//保存点击方法
function saveCfg(){
	//获取数据修改记录的集合
	var records = sysStorageStore.getModifiedRecords();
	var allrecords = sysStorageStore.getRange(0,sysStorageStore.getCount());//全部
	if(records.length==0){
		Ext.Msg.show({
		     title:'提示',
		     msg: '信息没有变更，请选择需要保存的记录',
		     buttons: Ext.Msg.OK,
		     icon: Ext.Msg.INFO 
		});
		return;
	}
	var lstAddRecord = new Array();	
	var flag = true;
	Ext.each(records, function(record) {
			var iname = record.data.iname.trim();
			var ikeyword = record.data.ikeyword.trim();
			var iplatform = record.data.iplatform;
			var j = 0;
			Ext.each(allrecords, function(r) {
				//相同用户不可以重名
				if(record.data.iname.trim()==r.data.iname.trim() )
				{
					if(r.data.icreateuser.trim() == loginName){
						j++;
					}
				}				
			});
			
			if(j>1)
			{
				Ext.Msg.alert('提示', '任务名称：【'+iname+'】重复');
				flag = false;
				return;
			}
			if ("" == iname || null == iname) {
				Ext.Msg.alert('提示', '报警类型不能为空！');
				flag = false;
                return;
            }
			if (iname.length>100) {
				Ext.Msg.alert('提示', '报警类型长度应小于一百！');
				flag = false;
                return;
            }
			if ("" == ikeyword || null == ikeyword) {
				Ext.Msg.alert('提示', '报警关键字不能为空！');
				flag = false;
                return;
            }
			if (iname.length>50) {
				Ext.Msg.alert('提示', '报警关键字长度应小于五十！');
				flag = false;
                return;
            }
			if ("" == iplatform || null == iplatform) {
				Ext.Msg.alert('提示', '报警平台不能为空！');
				flag = false;
                return;
            }
			lstAddRecord.push(record.data); 
	});
		//ajax请求
		if(flag){
			Ext.Ajax.request({
		
		    url: 'saveAlarmType.do',
			params : {
				//编码对象 Ext.JSON.encode的简写形式 得到json串
				jsonData : Ext.encode(lstAddRecord)
			},
			//200
		    success: function(response, opts) {
		    	//解码响应文本  解码（解析）JSON字符串对象
		        var message = Ext.decode(response.responseText).message;
		            Ext.MessageBox.show({ //提示框
		                title : "提示",
		                msg : message,
		                buttonText: {
		                    yes: '确定'
		                },
		                buttons: Ext.Msg.YES
		              });
		         sysStorageStore.reload();
		    },
		    //请求异常
		    failure: function(result, opts) {
		    	secureFilterRs(result,"请求返回失败！");
		    }
		  });
		}
}
//删除点击方法
function delAlarmTypeCfgs() {
		if (Ext.isIE) {//IE清理释放内存
			CollectGarbage();
		}
		var falg = true;
		var seleCount = sysStorageGrid.getSelectionModel().getSelection();
		var records = sysStorageGrid.getSelectionModel().getSelection();
		Ext.each(records,function(record){
			var icreateuser = record.data.icreateuser
			console.log(icreateuser != loginName)
			if(icreateuser != loginName){
				var iname = record.data.iname
				Ext.MessageBox.alert("提示", "没有删除报警类型【 "+iname+" 】权限，请重新选择！");
				falg = false;
				return;
			}
		});
		//一个没有选
		if (seleCount.length == 0) {
			Ext.MessageBox.alert("提示", "请选择要删除的数据!");
			return;
		}

		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		if(falg){
			Ext.Msg.confirm("确认删除", "确定删除选中的组记录", function(id) {
				if (id == 'yes')
					delalarmTypeCfg();
			});
		}
	}
//yes执行删除方法 发送 ajax请求
function delalarmTypeCfg() {	
		var records = sysStorageGrid.getSelectionModel().getSelection();
		var jsonArray=[];
		Ext.each(records,function(item){
			jsonArray.push(item.data.iid);
		});
		//ajax请求
		Ext.Ajax.request( {
			url : 'deleteAlarmType.do',
			method : 'post',
			params : {
				iids : jsonArray.join()
			},
			//200
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					//pageBar.moveFirst();
					Ext.Msg.alert('提示', message);
				} else {
					Ext.Msg.alert('提示', message);
				}
			},
			//请求异常
			failure : function(result, request) {
				secureFilterRs(result,"请求返回失败！",request);
			}
		});
	}


/**删除*/
function deleteSysRecords() {
	var data = sysStorageGrid.getView().getSelectionModel().getSelection();
	if (data.length == 0) {
		Ext.Msg.alert('提示', '请至少选择一条记录!');
		return;
	}

	Ext.Msg.confirm("请确认", "确定删除吗？", function(button, text) {
		if (button == "yes") {
			var ids = [];
			for (var i = 0; i < data.length; i++) {
				ids.push(data[i].get('iid'));
			}
			Ext.Ajax.request({
				url : 'infoSysCapacity/deleteSysCapacity.do',
				timeout : 30000,
				params : {
					deleteIds : ids
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;

					queryWhereSys();
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				},
				failure : function(result, request) {
					secureFilterRs(result, "请求返回失败！", request);
				}
			});
		}
	});
}
