/**
 * IEAIServiceServiceTestCase.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.ideal.ieai.clientapi.ws;

public class IEAIServiceServiceTestCase extends junit.framework.TestCase {
    public IEAIServiceServiceTestCase(java.lang.String name) {
        super(name);
    }

    public void testIEAIServiceWSDL() throws Exception {
        javax.xml.rpc.ServiceFactory serviceFactory = javax.xml.rpc.ServiceFactory.newInstance();
        java.net.URL url = new java.net.URL(new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIServiceAddress() + "?WSDL");
        javax.xml.rpc.Service service = serviceFactory.createService(url, new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getServiceName());
        assertTrue(service != null);
    }

    public void test1IEAIServiceGetUserInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo value = null;
        value = binding.getUserInfo(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test2IEAIServiceConnect() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.connect(new java.lang.String());
        // TBD - validate results
    }

    public void test3IEAIServiceLogin() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsClientSession value = null;
        value = binding.login(new com.ideal.ieai.clientapi.ws.WsClientSession(), new java.lang.String());
        // TBD - validate results
    }

    public void test4IEAIServiceForceLogin() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsClientSession value = null;
        value = binding.forceLogin(new com.ideal.ieai.clientapi.ws.WsClientSession(), new java.lang.String());
        // TBD - validate results
    }

    public void test5IEAIServiceLogout() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.logout(new java.lang.String());
        // TBD - validate results
    }

    public void test6IEAIServiceAutoLogout() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.autoLogout(new java.lang.String());
        // TBD - validate results
    }

    public void test7IEAIServiceIsSessionValid() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isSessionValid(new java.lang.String());
        // TBD - validate results
    }

    public void test8IEAIServiceGetAllClientSessions() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsClientSession[] value = null;
        value = binding.getAllClientSessions(new java.lang.String());
        // TBD - validate results
    }

    public void test9IEAIServiceKickoutSession() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsClientSession value = null;
        value = binding.kickoutSession(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test10IEAIServiceAddSNToDomain() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.addSNToDomain(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test11IEAIServiceFreezeServiceNode() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.freezeServiceNode(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test12IEAIServiceGetAllNodes() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ServiceNode[] value = null;
        value = binding.getAllNodes(new java.lang.String());
        // TBD - validate results
    }

    public void test13IEAIServiceGetAvailableNodes() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ServiceNode[] value = null;
        value = binding.getAvailableNodes(new java.lang.String());
        // TBD - validate results
    }

    public void test14IEAIServiceGetCurrServiceNode() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ServiceNode value = null;
        value = binding.getCurrServiceNode(new java.lang.String());
        // TBD - validate results
    }

    public void test15IEAIServiceGetIEAIGroupMembers() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.Address[] value = null;
        value = binding.getIEAIGroupMembers(new java.lang.String());
        // TBD - validate results
    }

    public void test16IEAIServiceGetServiceNode() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ServiceNode value = null;
        value = binding.getServiceNode(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test17IEAIServiceIsDBNormal() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isDBNormal(new java.lang.String());
        // TBD - validate results
    }

    public void test18IEAIServiceIsNodeStarted() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isNodeStarted(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test19IEAIServiceRemoveSNFromDomain() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.removeSNFromDomain(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test20IEAIServiceShutdownSN() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.shutdownSN(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test21IEAIServiceUnfreezeServiceNode() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.unfreezeServiceNode(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test22IEAIServiceGetDomainInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.DomainInfo value = null;
        value = binding.getDomainInfo(new java.lang.String());
        // TBD - validate results
    }

    public void test23IEAIServiceGetFlowDefinition() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSProjectDef value = null;
        value = binding.getFlowDefinition(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test24IEAIServiceGetFlowTaskNames() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        java.lang.String[] value = null;
        value = binding.getFlowTaskNames(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test25IEAIServiceGetPartInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.PartInfo value = null;
        value = binding.getPartInfo(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test26IEAIServiceGetCanStartProjects() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ProjectInfo[] value = null;
        value = binding.getCanStartProjects(new java.lang.String());
        // TBD - validate results
    }

    public void test27IEAIServiceGetRelatedProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ProjectInfo[] value = null;
        value = binding.getRelatedProject(new java.lang.String());
        // TBD - validate results
    }

    public void test28IEAIServiceGetUAProjectList() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ProjectInfo[] value = null;
        value = binding.getUAProjectList(new java.lang.String());
        // TBD - validate results
    }

    public void test29IEAIServiceDeleteProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.deleteProject(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test30IEAIServiceDownloadProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        byte[] value = null;
        value = binding.downloadProject(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test31IEAIServiceFreezeProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.freezeProject(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test32IEAIServiceUnfreezeProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.unfreezeProject(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test33IEAIServiceIsProjectExist() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isProjectExist(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test34IEAIServiceIsAdaptorExist() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isAdaptorExist(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test35IEAIServiceExistProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        int value = -3;
        value = binding.existProject(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test36IEAIServiceUpdateProject() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateProject(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test37IEAIServiceGetProjectVersionHistory() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ProjectInfo[] value = null;
        value = binding.getProjectVersionHistory(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test38IEAIServiceGetProjectLogHistory() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ProjectHistoryInfo[] value = null;
        value = binding.getProjectLogHistory(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test39IEAIServiceGetRelatedAdaptor() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.AdaptorInfo[] value = null;
        value = binding.getRelatedAdaptor(new java.lang.String());
        // TBD - validate results
    }

    public void test40IEAIServiceGetUAAdaptorList() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.AdaptorInfo[] value = null;
        value = binding.getUAAdaptorList(new java.lang.String());
        // TBD - validate results
    }

    public void test41IEAIServiceDeleteAdaptor() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.deleteAdaptor(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test42IEAIServiceDownloadAdaptor() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        byte[] value = null;
        value = binding.downloadAdaptor(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test43IEAIServiceFreezeAdaptor() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.freezeAdaptor(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test44IEAIServiceUnfreezeAdaptor() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.unfreezeAdaptor(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test45IEAIServiceUpdateAdaptor() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateAdaptor(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test46IEAIServiceGetAdaptorVersionHistory() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.AdaptorInfo[] value = null;
        value = binding.getAdaptorVersionHistory(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test47IEAIServiceGetAdaptorLogHistory() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.AdaptorHistoryInfo[] value = null;
        value = binding.getAdaptorLogHistory(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test48IEAIServiceAcquireTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.acquireTask(new java.lang.String(), 0, new java.lang.String(), true);
        // TBD - validate results
    }

    public void test49IEAIServiceApproveDelegatedTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.approveDelegatedTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test50IEAIServiceCancleTaskDelegation() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.cancleTaskDelegation(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test51IEAIServiceCompleteTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.completeTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test52IEAIServiceDelegateTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.delegateTask(new java.lang.String(), 0, 0, new java.lang.String());
        // TBD - validate results
    }

    public void test53IEAIServiceForwardTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.forwardTask(new java.lang.String(), 0, 0, new java.lang.String());
        // TBD - validate results
    }

    public void test54IEAIServiceGetFlowInfosByPendingTasks() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WorkflowInfo[] value = null;
        value = binding.getFlowInfosByPendingTasks(new java.lang.String());
        // TBD - validate results
    }

    public void test55IEAIServiceGetPendingTasks() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskInfo[] value = null;
        value = binding.getPendingTasks(new java.lang.String());
        // TBD - validate results
    }

    public void test56IEAIServiceGetTaskDeadline() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        java.util.Calendar value = null;
        value = binding.getTaskDeadline(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test57IEAIServiceGetTaskDelegateCandidates() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo[] value = null;
        value = binding.getTaskDelegateCandidates(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test58IEAIServiceGetTaskDetail() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskInfo value = null;
        value = binding.getTaskDetail(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test59IEAIServiceGetTaskForwardCandidates() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo[] value = null;
        value = binding.getTaskForwardCandidates(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test60IEAIServiceGetTasksOfJob() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskInfo[] value = null;
        value = binding.getTasksOfJob(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test61IEAIServiceGetOnlyTaskOwnerWithoutFinishedSkipped() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskInfo[] value = null;
        value = binding.getOnlyTaskOwnerWithoutFinishedSkipped(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test62IEAIServiceGetTasksToAlert() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskInfo[] value = null;
        value = binding.getTasksToAlert(new java.lang.String());
        // TBD - validate results
    }

    public void test63IEAIServiceHasTaskAlert() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.hasTaskAlert(new java.lang.String());
        // TBD - validate results
    }

    public void test64IEAIServiceIsTaskManager() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isTaskManager(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test65IEAIServicePerformActOnTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.performActOnTask(new java.lang.String(), 0, 0, new java.lang.String());
        // TBD - validate results
    }

    public void test66IEAIServiceQueryTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskQueryResult value = null;
        value = binding.queryTask(new java.lang.String(), new com.ideal.ieai.clientapi.ws.TaskQueryCriteria());
        // TBD - validate results
    }

    public void test67IEAIServiceQueryTaskHistory() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskHistory[] value = null;
        value = binding.queryTaskHistory(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsAppLogFilter());
        // TBD - validate results
    }

    public void test68IEAIServiceRejectDelegatedTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.rejectDelegatedTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test69IEAIServiceReleaseTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.releaseTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test70IEAIServiceSetPropertyValue() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setPropertyValue(new java.lang.String(), 0, new com.ideal.ieai.clientapi.ws.WSParamEnvValue());
        // TBD - validate results
    }

    public void test71IEAIServiceGetTaskId() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        long value = -3;
        value = binding.getTaskId(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test72IEAIServiceGetFlowParameters() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSParamEnvValue[] value = null;
        value = binding.getFlowParameters(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test73IEAIServiceGetAttachments() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskAttachment[] value = null;
        value = binding.getAttachments(new java.lang.String(), 0, true);
        // TBD - validate results
    }

    public void test74IEAIServiceGetAttachment() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.TaskAttachment value = null;
        value = binding.getAttachment(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test75IEAIServiceGetFullActInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsFullActInfo value = null;
        value = binding.getFullActInfo(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test76IEAIServiceAddTaskAttachment() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        long value = -3;
        value = binding.addTaskAttachment(new java.lang.String(), 0, new java.lang.String(), new byte[0]);
        // TBD - validate results
    }

    public void test77IEAIServiceSetTaskAttachment() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setTaskAttachment(new java.lang.String(), 0, 0, new java.lang.String(), new byte[0]);
        // TBD - validate results
    }

    public void test78IEAIServiceDeleteTaskAttachment() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.deleteTaskAttachment(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test79IEAIServiceForceReleaseTaskOwner() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.forceReleaseTaskOwner(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test80IEAIServiceSetTaskOwner() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setTaskOwner(new java.lang.String(), 0, 0, new java.lang.String());
        // TBD - validate results
    }

    public void test81IEAIServiceSkipTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.skipTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test82IEAIServiceUpdateFlowProperties() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateFlowProperties(new java.lang.String(), 0, new com.ideal.ieai.clientapi.ws.WSParamEnvValue[0]);
        // TBD - validate results
    }

    public void test83IEAIServiceFinishTaskItem() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.finishTaskItem(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test84IEAIServiceUpdateTaskProperties() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateTaskProperties(new java.lang.String(), 0, new com.ideal.ieai.clientapi.ws.WSTaskProperty[0]);
        // TBD - validate results
    }

    public void test85IEAIServiceCheckUserPermission() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.checkUserPermission(new java.lang.String(), (short)0, new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test86IEAIServiceCheckUserPermission() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.checkUserPermission(new java.lang.String(), 0, 0, new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test87IEAIServiceUpdateAccountInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateAccountInfo(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsUserBasicInfo());
        // TBD - validate results
    }

    public void test88IEAIServiceGetUserCalendarInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.CalendarInfo value = null;
        value = binding.getUserCalendarInfo(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test89IEAIServiceCreateRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        long value = -3;
        value = binding.createRole(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsRoleInfo());
        // TBD - validate results
    }

    public void test90IEAIServiceCreateUser() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        long value = -3;
        value = binding.createUser(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsUserBasicInfo());
        // TBD - validate results
    }

    public void test91IEAIServiceGetAllRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsRoleInfo[] value = null;
        value = binding.getAllRole(new java.lang.String());
        // TBD - validate results
    }

    public void test92IEAIServiceGetAllUser() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo[] value = null;
        value = binding.getAllUser(new java.lang.String());
        // TBD - validate results
    }

    public void test93IEAIServiceGetRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsRoleInfo value = null;
        value = binding.getRole(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test94IEAIServiceGetRolesFromUser() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsRoleInfo[] value = null;
        value = binding.getRolesFromUser(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test95IEAIServiceGetUser() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo value = null;
        value = binding.getUser(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test96IEAIServiceGetSelfInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo value = null;
        value = binding.getSelfInfo(new java.lang.String());
        // TBD - validate results
    }

    public void test97IEAIServiceGetUsersFromRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserBasicInfo[] value = null;
        value = binding.getUsersFromRole(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test98IEAIServiceIsSA() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isSA(new java.lang.String());
        // TBD - validate results
    }

    public void test99IEAIServiceIsUA() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isUA(new java.lang.String());
        // TBD - validate results
    }

    public void test100IEAIServiceIsUserInRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isUserInRole(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test101IEAIServiceRemoveRoles() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.removeRoles(new java.lang.String(), new long[0]);
        // TBD - validate results
    }

    public void test102IEAIServiceRemoveRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.removeRole(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test103IEAIServiceRemoveUsers() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.removeUsers(new java.lang.String(), new long[0]);
        // TBD - validate results
    }

    public void test104IEAIServiceRemoveUser() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.removeUser(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test105IEAIServiceSetRoleUsers() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.setRoleUsers(new java.lang.String(), new long[0], 0);
        // TBD - validate results
    }

    public void test106IEAIServiceSetUserRoles() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.setUserRoles(new java.lang.String(), new long[0], 0);
        // TBD - validate results
    }

    public void test107IEAIServiceUpdateRole() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateRole(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsRoleInfo());
        // TBD - validate results
    }

    public void test108IEAIServiceUpdateUser() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateUser(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsUserBasicInfo());
        // TBD - validate results
    }

    public void test109IEAIServiceCheckOwnPermission() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.checkOwnPermission(new java.lang.String(), (short)0, new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test110IEAIServiceGetRolePermissionSet() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.PermissionSet value = null;
        value = binding.getRolePermissionSet(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test111IEAIServiceGetUserPermissionSet() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.PermissionSet value = null;
        value = binding.getUserPermissionSet(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test112IEAIServiceSetRolePermissionSet() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setRolePermissionSet(new java.lang.String(), new com.ideal.ieai.clientapi.ws.PermissionSet(), 0);
        // TBD - validate results
    }

    public void test113IEAIServiceSetUserPermissionSet() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setUserPermissionSet(new java.lang.String(), new com.ideal.ieai.clientapi.ws.PermissionSet(), 0);
        // TBD - validate results
    }

    public void test114IEAIServiceUpdateUserRelatedInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.updateUserRelatedInfo(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsUserBasicInfo());
        // TBD - validate results
    }

    public void test115IEAIServiceSetSAPwd() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setSAPwd(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test116IEAIServiceSetUAPwd() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setUAPwd(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test117IEAIServiceSetUserSelfInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setUserSelfInfo(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsUserBasicInfo());
        // TBD - validate results
    }

    public void test118IEAIServiceSetFlowLogConfig() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.setFlowLogConfig(new java.lang.String(), new java.lang.String(), new java.lang.String(), new com.ideal.ieai.clientapi.ws.WorkflwLogConfig());
        // TBD - validate results
    }

    public void test119IEAIServiceGetFlowLogConfig() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WorkflwLogConfig value = null;
        value = binding.getFlowLogConfig(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test120IEAIServiceGetAllFlowsInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WorkflowInfo[] value = null;
        value = binding.getAllFlowsInfo(new java.lang.String());
        // TBD - validate results
    }

    public void test121IEAIServiceGetSubFlowInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.SubFlowInfo[] value = null;
        value = binding.getSubFlowInfo(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test122IEAIServiceGetActType() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        int value = -3;
        value = binding.getActType(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test123IEAIServiceGetBasicActInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.BasicActInfo value = null;
        value = binding.getBasicActInfo(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test124IEAIServiceGetFlowSnapshot() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        byte[] value = null;
        value = binding.getFlowSnapshot(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test125IEAIServiceGetActNodeInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ActNodeInfo[] value = null;
        value = binding.getActNodeInfo(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test126IEAIServiceGetFlowInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WorkflowInfo value = null;
        value = binding.getFlowInfo(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test127IEAIServiceGetFlowInfos() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WorkflowInfo[] value = null;
        value = binding.getFlowInfos(new java.lang.String(), new long[0]);
        // TBD - validate results
    }

    public void test128IEAIServiceKillFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.killFlow(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test129IEAIServiceDelWarningInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.delWarningInfo(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test130IEAIServiceQueryWarningInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WarningInfo[] value = null;
        value = binding.queryWarningInfo(new java.lang.String());
        // TBD - validate results
    }

    public void test131IEAIServiceStopFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.stopFlow(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test132IEAIServiceGetCurRunningAct() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        java.lang.String[] value = null;
        value = binding.getCurRunningAct(new java.lang.String(), new long[0]);
        // TBD - validate results
    }

    public void test133IEAIServicePauseFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.pauseFlow(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test134IEAIServiceResumeFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.resumeFlow(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test135IEAIServiceQueryFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WorkflowInfo[] value = null;
        value = binding.queryFlow(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WorkflowFilter());
        // TBD - validate results
    }

    public void test136IEAIServiceStartFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        long value = -3;
        value = binding.startFlow(new java.lang.String(), new java.lang.String(), new java.lang.String(), new com.ideal.ieai.clientapi.ws.WSParamEnvValue[0], new com.ideal.ieai.clientapi.ws.WSParamEnvValue[0], new com.ideal.ieai.clientapi.ws.WorkflwLogConfig(), new java.lang.String(), new java.lang.String(), true);
        // TBD - validate results
    }

    public void test137IEAIServiceStartFlow() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        long value = -3;
        value = binding.startFlow(new java.lang.String(), new java.lang.String(), new java.lang.String(), new com.ideal.ieai.clientapi.ws.WSParamEnvValue[0], new java.lang.String(), new java.lang.String(), true);
        // TBD - validate results
    }

    public void test138IEAIServiceGetAllActInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.ActivityRuntimeInfo[] value = null;
        value = binding.getAllActInfo(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test139IEAIServiceGetAllMonitorActCfg() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.MonitorActConfig[] value = null;
        value = binding.getAllMonitorActCfg(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test140IEAIServiceAnnounceNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult value = null;
        value = binding.announceNotice(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsNotice());
        // TBD - validate results
    }

    public void test141IEAIServiceConfirmUserNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.confirmUserNotice(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test142IEAIServiceConfirmTaskNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.confirmTaskNotice(new java.lang.String(), 0, 0, new java.lang.String());
        // TBD - validate results
    }

    public void test143IEAIServiceGetNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult value = null;
        value = binding.getNotice(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test144IEAIServiceGetTaskNotices() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult[] value = null;
        value = binding.getTaskNotices(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test145IEAIServiceGetLoginNotices() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult[] value = null;
        value = binding.getLoginNotices(new java.lang.String());
        // TBD - validate results
    }

    public void test146IEAIServiceGetTimeNotices() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult[] value = null;
        value = binding.getTimeNotices(new java.lang.String());
        // TBD - validate results
    }

    public void test147IEAIServiceInvalidateNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.invalidateNotice(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test148IEAIServiceModifyNotifyMethod() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult value = null;
        value = binding.modifyNotifyMethod(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test149IEAIServiceQueryNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeQueryResult[] value = null;
        value = binding.queryNotice(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsQueryNoticeCriteria());
        // TBD - validate results
    }

    public void test150IEAIServiceGetNoticePermission() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsNoticeAnnouncePermission value = null;
        value = binding.getNoticePermission(new java.lang.String());
        // TBD - validate results
    }

    public void test151IEAIServiceDeleteNotice() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.deleteNotice(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test152IEAIServiceQueryFlowDefination() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSFlowDefReport[] value = null;
        value = binding.queryFlowDefination(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WSFlowDefReportCriteria());
        // TBD - validate results
    }

    public void test153IEAIServiceQueryActInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSActRunInfoReport[] value = null;
        value = binding.queryActInfo(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WSActRunInfoReportCriteria());
        // TBD - validate results
    }

    public void test154IEAIServiceQueryActRunStatistic() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSActRunInfoStatReport[] value = null;
        value = binding.queryActRunStatistic(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WSActRunInfoStatReportCriteria());
        // TBD - validate results
    }

    public void test155IEAIServiceQueryActUseTimeStatistic() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSActRunUseTimeReport[] value = null;
        value = binding.queryActUseTimeStatistic(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WSActRunUseTimeReportCriteria());
        // TBD - validate results
    }

    public void test156IEAIServiceGetActValidTimeCfg() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSActValidTimeCfg[] value = null;
        value = binding.getActValidTimeCfg(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test157IEAIServiceGetUserPasswordFormat() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.UserPasswordFormat value = null;
        value = binding.getUserPasswordFormat(new java.lang.String());
        // TBD - validate results
    }

    public void test158IEAIServiceCheckUserPassword() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.checkUserPassword(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test159IEAIServiceGetSysConfig() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSSysConfig value = null;
        value = binding.getSysConfig(new java.lang.String());
        // TBD - validate results
    }

    public void test160IEAIServiceQueryRunningActForUTIndex() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsRunningActQueryResult[] value = null;
        value = binding.queryRunningActForUTIndex(new java.lang.String(), 0, new java.lang.String(), true);
        // TBD - validate results
    }

    public void test161IEAIServiceGetActivityRelatedRuntimes() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsActivityRelatedRuntimes value = null;
        value = binding.getActivityRelatedRuntimes(new java.lang.String(), 0, 0);
        // TBD - validate results
    }

    public void test162IEAIServiceGetErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.getErrorTask(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test163IEAIServiceGetCanTakeOperationsOfErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsOperationConfiguration value = null;
        value = binding.getCanTakeOperationsOfErrorTask(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test164IEAIServiceOperateOnErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.operateOnErrorTask(new java.lang.String(), new com.ideal.ieai.clientapi.ws.WsErrorTaskOperation(), 0);
        // TBD - validate results
    }

    public void test165IEAIServiceForwardErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.forwardErrorTask(new java.lang.String(), 0, new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test166IEAIServiceDelegateErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.delegateErrorTask(new java.lang.String(), 0, new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test167IEAIServiceCancelErrorTaskDelegation() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.cancelErrorTaskDelegation(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test168IEAIServiceRejectDelegateeOperationOfErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.rejectDelegateeOperationOfErrorTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test169IEAIServiceAcceptDelegatieeOperationOfErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.acceptDelegatieeOperationOfErrorTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test170IEAIServiceReleaseErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.releaseErrorTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test171IEAIServiceForceReleaseErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.forceReleaseErrorTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test172IEAIServiceAcquireErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.acquireErrorTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test173IEAIServiceForceAcquireErrorTask() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTask value = null;
        value = binding.forceAcquireErrorTask(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test174IEAIServiceGetErrorTaskPermission() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTaskPermission value = null;
        value = binding.getErrorTaskPermission(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test175IEAIServiceGetErrorTaskLog() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsErrorTaskLog[] value = null;
        value = binding.getErrorTaskLog(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test176IEAIServiceGetCanForwardOrDelegateToUsers() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WsUserInfo[][] value = null;
        value = binding.getCanForwardOrDelegateToUsers(new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test177IEAIServiceGetBae() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean[] value = null;
        value = binding.getBae(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test178IEAIServiceIsShellCmd() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        boolean value = false;
        value = binding.isShellCmd(new java.lang.String(), 0, new java.lang.String());
        // TBD - validate results
    }

    public void test179IEAIServiceStopShellCmdProcess() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.stopShellCmdProcess(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test180IEAIServiceGetShellCmdOutput() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSShellCmdOutput value = null;
        value = binding.getShellCmdOutput(new java.lang.String(), new java.lang.String(), 0, 0, 0, 0, 0);
        // TBD - validate results
    }

    public void test181IEAIServiceGetShellCmdOutputNLine() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        com.ideal.ieai.clientapi.ws.WSShellCmdOutput value = null;
        value = binding.getShellCmdOutputNLine(new java.lang.String(), new java.lang.String(), 0);
        // TBD - validate results
    }

    public void test182IEAIServiceInputForShellCmd() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.inputForShellCmd(new java.lang.String(), new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

    public void test183IEAIServiceGetMonitorSetting() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        java.util.HashMap value = null;
        value = binding.getMonitorSetting();
        // TBD - validate results
    }

    public void test184IEAIServiceSetMonitorSetting() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        java.lang.String value = null;
        value = binding.setMonitorSetting(new java.util.HashMap());
        // TBD - validate results
    }

    public void test185IEAIServiceSaveChActInfo() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.saveChActInfo(new java.lang.String(), 0, 0, 0, 0);
        // TBD - validate results
    }

    public void test186IEAIServiceMonitorApplyForWeb() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.monitorApplyForWeb(new com.ideal.ieai.server.jobscheduling.repository.heartbeatmonitor.MonitorParm());
        // TBD - validate results
    }

    public void test187IEAIServiceDbConnectTimeApplyForWeb() throws Exception {
        com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub binding;
        try {
            binding = (com.ideal.ieai.clientapi.ws.IEAIServiceSoapBindingStub)
                          new com.ideal.ieai.clientapi.ws.IEAIServiceServiceLocator().getIEAIService();
        }
        catch (javax.xml.rpc.ServiceException jre) {
            if(jre.getLinkedCause()!=null)
                jre.getLinkedCause().printStackTrace();
            throw new junit.framework.AssertionFailedError("JAX-RPC ServiceException caught: " + jre);
        }
        assertNotNull("binding is null", binding);

        // Time out after a minute
        binding.setTimeout(60000);

        // Test operation
        binding.dbConnectTimeApplyForWeb(new java.lang.String(), new java.lang.String());
        // TBD - validate results
    }

}
