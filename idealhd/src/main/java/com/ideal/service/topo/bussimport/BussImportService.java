package com.ideal.service.topo.bussimport;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.topo.dataimport.ImportExcelManager;
import com.ideal.ieai.server.repository.topo.dataimport.ImportModel;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BussImportService
{
    public static final String       Date_Format   = "^(([0-1][0-9])|2[0-3])点[0-5][0-9]分[0-5][0-9]秒$";
    public static final String       Double_Format = "^([1-9]\\d{0,13}|0)(\\.\\d{1,2})?$";

    private static BussImportService service;

    public static BussImportService getInstance ()
    {
        if (null == service)
        {
            service = new BussImportService();
        }
        return service;
    }

    private static final Logger _log = Logger.getLogger("topoLog");

    public int export ( InputStream oo, int version ) throws Exception
    {
        Connection conn1 = null;
        int retnum = 0;
        try
        {
            DBManager db = DBManager.getInstance();
            conn1 = db.getJdbcConnection(Constants.IEAI_IEAI);
            List<ImportModel> servers = new ArrayList<ImportModel>();

            HashMap<String, Integer> title = new HashMap<String, Integer>();
            Sheet hssfsheet = null;
            Row hssfrow = null;
            if (version == 2003)
            {
                HSSFWorkbook hssfworkbook = new HSSFWorkbook(oo);
                hssfsheet = hssfworkbook.getSheetAt(0);// 第一个工作表
                hssfrow = hssfsheet.getRow(0);// 第一行
                hssfsheet = hssfworkbook.getSheetAt(0);
                // xlsOrxlsx[0] = hssfsheet;
                // xlsOrxlsx[1] = hssfrow;
            } else
            {
                XSSFWorkbook hssfworkbook = new XSSFWorkbook(oo);
                hssfsheet = hssfworkbook.getSheetAt(0);// 第一个工作表
                hssfrow = hssfsheet.getRow(0);// 第一行
                hssfsheet = hssfworkbook.getSheetAt(0);
            }
            // 遍历该行所有的行,j表示行数 getPhysicalNumberOfRows行的总数
            if ((null == hssfrow) || (null == hssfrow.getCell(2)))
            {
                throw new Exception("ss表格格式不对！");
            }
            hssfrow.getCell(2).setCellType(CellType.STRING);
            /*
             * 将excel中的字段按数据库中的字段顺序排序
             */
            if (hssfrow.getPhysicalNumberOfCells() != 13)
            {
                throw new Exception("表格列数错误！");
            }
            int colNums = hssfrow.getPhysicalNumberOfCells();
            for (int t = 0; t < colNums; t++)
            {
                hssfrow.getCell(t).setCellType(CellType.STRING);
                if (hssfrow.getCell(t).getStringCellValue().trim().equals("系统名称"))
                {
                    title.put("系统名称", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("任务名称"))
                {
                    title.put("任务名称", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("任务描述"))
                {
                    title.put("任务描述", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("所属组名"))
                {
                    title.put("所属组名", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("所属组编码"))
                {
                    title.put("所属组编码", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("组内顺序"))
                {
                    title.put("组内顺序", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("所属组顺序"))
                {
                    title.put("所属组顺序", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("组描述"))
                {
                    title.put("组描述", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("是否删除"))
                {
                    title.put("是否删除", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("填表人"))
                {
                    title.put("填表人", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("上游系统名称"))
                {
                    title.put("上游系统名称", t);
                } else if (hssfrow.getCell(t).getStringCellValue().trim().equals("上游任务名称"))
                {
                    title.put("上游任务名称", t);
                }
            }
            if (hssfsheet.getPhysicalNumberOfRows() == 1)
            {
                throw new Exception("导入表格没有数据！");
            }
            for (int j = 1; j < hssfsheet.getPhysicalNumberOfRows(); j++)
            {
                ImportModel server = new ImportModel();
                hssfrow = hssfsheet.getRow(j);
                if (hssfrow != null)
                {
                    for (int n = 0; n < colNums; n++)
                    {
                        if (hssfrow.getCell(n) != null)
                        {
                            hssfrow.getCell(n).setCellType(CellType.STRING);
                        }
                    }
                    // 如果系统名称和任务名称都为空则为空行
                    if (hssfrow.getCell(title.get("系统名称")).toString().trim().equals("")
                            && hssfrow.getCell(title.get("任务名称")).toString().trim().equals(""))
                    {
                        continue;
                    }
                    if (title.get("系统名称") == null)
                    {
                        throw new Exception("ss系统名称列名错误！");
                    } else
                    {
                        if (hssfrow.getCell(title.get("系统名称")) == null)
                        {
                            throw new Exception("ss系统名称不能为空！");
                        } else
                        {
                            if (hssfrow.getCell(title.get("系统名称")).toString().trim().equals(""))
                            {
                                throw new Exception("ss系统名称不能为空！");
                            }
                            if (hssfrow.getCell(title.get("系统名称")).toString().trim().getBytes("GBK").length > 255)
                            {
                                throw new Exception("ss系统名称长度不能超过255个字节！");
                            }
                        }
                    }
                    if (title.get("任务名称") == null)
                    {
                        throw new Exception("ss任务名称列名错误！");
                    } else
                    {
                        if (hssfrow.getCell(title.get("任务名称")) == null)
                        {
                            throw new Exception("ss任务名称不能为空！");
                        } else
                        {
                            if (hssfrow.getCell(title.get("任务名称")).toString().trim().equals(""))
                            {
                                throw new Exception("ss任务名称不能为空！");
                            }
                            if (hssfrow.getCell(title.get("任务名称")).toString().trim().getBytes("GBK").length > 255)
                            {
                                throw new Exception("ss任务名称长度不能超过255个字节！");
                            }
                        }
                    }
                    if (title.get("所属组编码") == null)
                    {
                        throw new Exception("ss所属组编码列名错误！");
                    } else
                    {
                        if (hssfrow.getCell(title.get("所属组编码")) == null)
                        {
                            throw new Exception("ss所属组编码不能为空！");
                        } else
                        {
                            if (hssfrow.getCell(title.get("所属组编码")).toString().trim().equals(""))
                            {
                                throw new Exception("ss所属组编码不能为空！");
                            }
                            if (hssfrow.getCell(title.get("所属组编码")).getStringCellValue().trim()
                                    .getBytes("GBK").length > 25)
                            {
                                throw new Exception("ss所属组编码内容长度超过25个字节！");
                            }
                        }
                    }
                    if (title.get("所属组名") == null)
                    {
                        throw new Exception("ss所属组名列名错误！");
                    } else
                    {
                        if (hssfrow.getCell(title.get("所属组名")) == null)
                        {
                            throw new Exception("ss所属组名不能为空！");
                        } else
                        {
                            if (hssfrow.getCell(title.get("所属组名")).toString().trim().equals(""))
                            {
                                throw new Exception("ss所属组名不能为空！");
                            }
                            if (hssfrow.getCell(title.get("所属组名")).getStringCellValue().trim()
                                    .getBytes("GBK").length > 255)
                            {
                                throw new Exception("ss所属组名内容长度超过255个字节！");
                            }
                        }
                    }
                    if (title.get("组内顺序") == null)
                    {
                        throw new Exception("ss组内顺序列名错误！");
                    } else
                    {
                        if (hssfrow.getCell(title.get("组内顺序")) == null)
                        {
                            throw new Exception("ss组内顺序不能为空！");
                        } else
                        {
                            if (hssfrow.getCell(title.get("组内顺序")).toString().trim().equals(""))
                            {
                                throw new Exception("ss组内顺序不能为空！");
                            }
                            if (hssfrow.getCell(title.get("组内顺序")).getStringCellValue().trim()
                                    .getBytes("GBK").length > 25)
                            {
                                throw new Exception("ss组内顺序内容长度超过25个字节！");
                            }
                        }
                    }
                    if (title.get("任务描述") == null)
                    {
                        throw new Exception("ss任务描述列名错误！");
                    }
                    if (title.get("上游系统名称") == null)
                    {
                        throw new Exception("ss上游系统名称列名错误！");
                    }
                    if (title.get("上游任务名称") == null)
                    {
                        throw new Exception("ss上游任务名称列名错误！");
                    }
                    if (title.get("是否删除") == null)
                    {
                        throw new Exception("ss是否删除列名错误！");
                    }
                    if (title.get("填表人") == null)
                    {
                        throw new Exception("ss填表人列名错误！");
                    }

                    server.setSystemname(hssfrow.getCell(title.get("系统名称")).getStringCellValue().trim());
                    server.setTaskname(hssfrow.getCell(title.get("任务名称")).getStringCellValue().trim());
                    if (hssfrow.getCell(title.get("任务描述")) == null)
                    {
                        server.setTaskdes("");
                    } else
                    {
                        if (hssfrow.getCell(title.get("任务描述")).getStringCellValue().trim()
                                .getBytes("GBK").length > 2000)
                        {
                            throw new Exception("ss任务描述内容长度超过2000个字节！");
                        }
                        server.setTaskdes(hssfrow.getCell(title.get("任务描述")).getStringCellValue().trim());
                    }
                    if (hssfrow.getCell(title.get("上游系统名称")) == null)
                    {
                        server.setLastsysname("");
                    } else
                    {
                        if (hssfrow.getCell(title.get("上游系统名称")).getStringCellValue().trim()
                                .getBytes("GBK").length > 255)
                        {
                            throw new Exception("ss上游系统名称内容长度超过255个字节！");
                        }
                        server.setLastsysname(hssfrow.getCell(title.get("上游系统名称")).getStringCellValue().trim());
                    }
                    if (hssfrow.getCell(title.get("上游任务名称")) == null)
                    {
                        server.setLasttaskname("");
                    } else
                    {
                        if (hssfrow.getCell(title.get("上游任务名称")).getStringCellValue().trim()
                                .getBytes("GBK").length > 255)
                        {
                            throw new Exception("ss上游任务名称长度超过255个字节！");
                        }
                        server.setLasttaskname(hssfrow.getCell(title.get("上游任务名称")).getStringCellValue().trim());
                    }
                    if (hssfrow.getCell(title.get("是否删除")) == null)
                    {
                        server.setDelete("否");
                    } else
                    {
                        if ("是".equals(hssfrow.getCell(title.get("是否删除")).getStringCellValue().trim()))
                        {
                            server.setDelete("是");
                        } else if ("".equals(hssfrow.getCell(title.get("是否删除")).getStringCellValue().trim()))
                        {
                            server.setDelete("否");
                        } else if ("否".equals(hssfrow.getCell(title.get("是否删除")).getStringCellValue().trim()))
                        {
                            server.setDelete("否");
                        } else
                        {
                            throw new Exception("ss是否删除列填写错误！");
                        }
                    }
                    if (hssfrow.getCell(title.get("填表人")) == null)
                    {
                        server.setInformation("");
                    } else
                    {
                        if (hssfrow.getCell(title.get("填表人")).getStringCellValue().trim().getBytes("GBK").length > 255)
                        {
                            throw new Exception("ss填表人内容长度超过255个字节！");
                        }
                        server.setInformation(hssfrow.getCell(title.get("填表人")).getStringCellValue().trim());
                    }

                    server.setGroup(hssfrow.getCell(title.get("所属组名")).getStringCellValue().trim());
                    server.setPgroupNum(hssfrow.getCell(title.get("所属组编码")).getStringCellValue().trim());
                    server.setNgroupSort(hssfrow.getCell(title.get("组内顺序")).getStringCellValue().trim());
                    server.setPgroupSort(hssfrow.getCell(title.get("所属组顺序")).getStringCellValue().trim());
                    server.setPgroupDes(hssfrow.getCell(title.get("组描述")) == null ? ""
                            : hssfrow.getCell(title.get("组描述")).getStringCellValue().trim());
                    servers.add(server);
                    retnum = ImportExcelManager.getInstance().excelBussImport(servers, j, conn1, db, colNums);
                }
            }
            conn1.commit();
            return retnum;
        } catch (SQLException e)
        {
            conn1.rollback();
            throw e;
        } finally
        {
            if (conn1 != null)
            {
                conn1.close();
                conn1 = null;
            }
        }

    }

    public Map analyData ()
    {
        Map map = new HashMap();
        // 1、查詢所有数据（当前系统和上级系统），
        // 2、分别（先上级在下级）查询mess表是否存在该记录，不存在则存入mess表，全部返回主键ID，
        // 3、将ID存入map-key，value=prj__--ieai--__actname,于此建立起来父子关系存入relation表
        List<ImportModel> list = null;
        try
        {
            list = ImportExcelManager.getInstance().getAllAnalyData();
            if (null != list && !list.isEmpty())
            {
                for (ImportModel m : list)
                {
                    Long[] ids = ImportExcelManager.getInstance().getMessId(m);
                    ImportExcelManager.getInstance().saveRelation(ids);
                }
            }
            map.put("message", "解析成功");
            map.put("success", true);
        } catch (Exception e)
        {
            map.put("message", "解析失败");
            map.put("success", false);
            _log.error(e);
        }

        return map;
    }

    public Map analyDataTrnk ()
    {
        Map map = new HashMap();
        // 1、查詢所有数据（当前系统和上级系统），
        // 2、分别（先上级在下级）查询mess表是否存在该记录，不存在则存入mess表，全部返回主键ID，
        // 3、将ID存入map-key，value=prj__--ieai--__actname,于此建立起来父子关系存入relation表
        List<ImportModel> list = null;
        try
        {
            list = ImportExcelManager.getInstance().getAllAnalyTrankData();
            if (null != list && !list.isEmpty())
            {
                // for (ImportModel m : list)
                // {
                // long ids = ImportExcelManager.getInstance().getMessTrankId(m);
                ImportExcelManager.getInstance().saveGroupRelation(list);
                // }
                list = ImportExcelManager.getInstance().getAllDataForAnaly();
                // 查询所有cell，当excel中有配置上级依赖时，增加依赖关系，否则无依赖关系
                if (null != list && !list.isEmpty())
                {
                    ImportExcelManager.getInstance().saveTrankDataToMess(list);
                }
            }
            map.put("message", "解析成功");
            map.put("success", true);
        } catch (Exception e)
        {
            map.put("message", "解析失败");
            map.put("success", false);
            _log.error(e);
        }

        return map;
    }

    class MyThread extends Thread
    {
        @Override
        public void run ()
        {
            analyDataTrnk();
        }
    }

    public void threadAanalyDataTrnk ()
    {
        MyThread t = new MyThread();
        t.start();
    }

}
