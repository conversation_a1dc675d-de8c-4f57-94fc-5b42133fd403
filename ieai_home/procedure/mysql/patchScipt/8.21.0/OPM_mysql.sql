-- 4.7.18
	DE<PERSON>IMITER ;;
	DROP PROCEDURE IF <PERSON>XIS<PERSON> UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'ICREATETIME') THEN
        ALTER TABLE IEAI_USER ADD COLUMN ICREATETIME NUMERIC(19);
    END IF;
    
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.19
    	CREATE TABLE IF NOT EXISTS IEAI_COLLECT_OPERATION_STATUS(  IID DECIMAL(14) NOT NULL,  ISTATUS smallint,  CONSTRAINT  PK_COLLECT_OPER_STATUS PRIMARY KEY(IID));
    CREATE TABLE IF NOT EXISTS IEAI_AUDIT_CLOB ( AUDITID DECIMAL(19,0) NOT NULL, ICONTENT	LONGTEXT NULL, PRIMARY KEY(AUDITID));
	CREATE TABLE IF NOT EXISTS IEAI_SYNCCMDB_INFO(IID NUMERIC(19) NOT NULL,SERVICERUNFLAG	VARCHAR(4),CRONVALUE	VARCHAR(20),SYNC_IP_COUNT    VARCHAR(10),TIMINGJOBFLAG  VARCHAR(4),CMDBTHREADFLAG  VARCHAR(4),PRIMARY KEY (IID));
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ROLE' AND COLUMN_NAME = 'ICREATETIME') THEN
        ALTER TABLE IEAI_ROLE ADD COLUMN ICREATETIME NUMERIC(19);
    END IF;
    
    CREATE TABLE IF NOT EXISTS IEAI_RESGROUP_MODEL ( IID 	NUMERIC(19) NOT NULL,	IGROUPMESID  NUMERIC(19),	IISTRUE	INTEGER,	CONSTRAINT PK_IEAI_RESGROUP_MODEL PRIMARY KEY(IID));
   
		IF NOT EXISTS (SELECT * FROM IEAI_RESGROUP_MODEL WHERE  IID = -1) THEN
			INSERT INTO IEAI_RESGROUP_MODEL (IID,IGROUPMESID,IISTRUE) VALUES(-1,3,1);
			commit;
		END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ROLE' AND COLUMN_NAME = 'ICREATETIME') THEN
        ALTER TABLE IEAI_ROLE ADD COLUMN ICREATETIME NUMERIC(19);
    END IF;
	IF NOT EXISTS (SELECT * FROM IEAI_COLLECT_OPERATION_STATUS WHERE IID = 10) THEN
            INSERT INTO IEAI_COLLECT_OPERATION_STATUS(IID,ISTATUS) VALUES(10, 0);
        commit;
		END IF;
	IF  EXISTS (SELECT  *  FROM IEAI_GROUPMESSAGE WHERE  GROUPID=15) THEN
	UPDATE IEAI_GROUPMESSAGE SET IIMG ='images/info6666.png' where GROUPID=15;
	commit;
	END IF;
	
	IF  EXISTS (SELECT  *  FROM IEAI_GROUPMESSAGE WHERE  GROUPID=100) THEN
	UPDATE IEAI_GROUPMESSAGE SET IIMG ='images/info85.png' where GROUPID=100;
	commit;
	END IF;
	IF  EXISTS (SELECT  *  FROM IEAI_GROUPMESSAGE WHERE  GROUPID=10) THEN
	UPDATE IEAI_GROUPMESSAGE SET IIMG ='images/info6667.png' where GROUPID=10;
	commit;
	END IF;
		
	
    
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.20

    		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
			CREATE TABLE IF NOT EXISTS IEAI_AGENTINFO_DELETE(IAGENTINFO_ID NUMERIC(19) NOT NULL,IEQUIPMENT_OR_VM_ID NUMERIC(19),IAGENT_NAME VARCHAR(255),IAGENT_DES VARCHAR(255),IAGENT_IP VARCHAR(255) NOT NULL,IAGENT_PORT NUMERIC(19),ICLUSTER_ID NUMERIC(19),IDELETE_FLAG NUMERIC(1) DEFAULT 0,IOS_NAME VARCHAR(255),ICOM_NAME VARCHAR(255),ISTART_USER VARCHAR(255),IAGENT_STATE NUMERIC(1),IAGENT_VERSION VARCHAR(100),IAGENT_ACTIVITIES LONGTEXT,IAGENT_ACTIVITY_NUM NUMERIC(19),IAGENTUP_ID NUMERIC(19),IPALIAS NUMERIC(12),IENV_TYPE NUMERIC(2) DEFAULT 1,IAGENT_CKSTATE NUMERIC(1),IAGENT_CKTIME NUMERIC(19),IAGENT_CCHANGE NUMERIC(1),JOURNAL LONGTEXT,IAGENT_IF_SENDMSG NUMERIC(1) DEFAULT 1,IACTNUM NUMERIC(10),IACTNUMMAX NUMERIC(10),ICPUVALUE VARCHAR(255),IMEMVALUE VARCHAR(255),ICHECKTIME VARCHAR(255),ICUSTOM_CMD VARCHAR(1000),ICUSTOM_MESS LONGTEXT,ICREATETIME NUMERIC(19),ICREATEUSER NUMERIC(19),ISSUED NUMERIC(1) DEFAULT 0,IDELETEUSER VARCHAR(255),IDELETETIME NUMERIC(19),IDELETEDESC VARCHAR(255),CONSTRAINT PK_IEAI_AGENTINFO_DELETE PRIMARY KEY(IAGENTINFO_ID));

			
			
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_DOUBLECHECK_COLVALUE' AND INDEX_NAME = 'IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID') THEN
				CREATE INDEX IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID);
			END IF;	
			
			CREATE TABLE IF NOT EXISTS IEAI_EQU_GROUP_BUSINESS  (IID DECIMAL(19,0) NOT NULL,EQUGROUPID DECIMAL(19,0),BUSINESSID DECIMAL(19,0),BUSINESSNAME VARCHAR(255),PROTYPE DECIMAL(5,0) ,CONSTRAINT PK_IEAI_EQU_GROUP_BUSINESS PRIMARY KEY(IID));
			
			IF  EXISTS (SELECT * FROM IEAI_HIGHOPER WHERE IBUTTONID=145) THEN
				UPDATE IEAI_HIGHOPER SET IBUTTONURL='deleteFormationByCP.do' WHERE IBUTTONID=145;
				commit;
			END IF;
			IF  EXISTS (SELECT * FROM IEAI_HIGHOPER WHERE IBUTTONID=146) THEN
				UPDATE IEAI_HIGHOPER SET IBUTTONURL='saveFormationByCP.do' WHERE IBUTTONID=146;
				commit;
			END IF;
			
			CREATE TABLE IF NOT EXISTS IEAI_EXCEL_UT_REVIEW(IID DECIMAL(19,0) NOT NULL,IFLOWID DECIMAL(19,0),IACTID DECIMAL(19,0),IACTNAME VARCHAR(255),IACYTYPE DECIMAL(2),IUSERNAME VARCHAR(255),IUSERID DECIMAL(19,0),IREVIEWTAKEOVERTIME DECIMAL(19,0),IREVIEWTIME DECIMAL(19,0),IREVIEWFINISHTIME DECIMAL(19,0),CONSTRAINT PK_IEAI_EXCEL_UT_REVIEW PRIMARY KEY(IID));
	
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IEXPBEGINTIME') THEN
					ALTER TABLE IEAI_INSTANCEINFO ADD IEXPBEGINTIME decimal(19,0) default 0;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IEXPENDTIME') THEN
					ALTER TABLE IEAI_INSTANCEINFO ADD IEXPENDTIME decimal(19,0) default 0;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IREMARK') THEN
					ALTER TABLE IEAI_INSTANCEINFO ADD IREMARK VARCHAR(1000);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IEXPBEGINTIME') THEN
					ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPBEGINTIME decimal(19,0) default 0;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IEXPENDTIME') THEN
					ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPENDTIME decimal(19,0) default 0;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IREMARK') THEN
					ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREMARK VARCHAR(1000);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IEXPBEGINTIME') THEN
					ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPBEGINTIME decimal(19,0) default 0;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IEXPENDTIME') THEN
					ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPENDTIME decimal(19,0) default 0;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IREMARK') THEN
					ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREMARK VARCHAR(1000);
			END IF;
			
		END;;
		DELIMITER ;
		CALL UPPRDE();
-- 4.7.21
        	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
    
    
	IF  EXISTS (SELECT  *  FROM IEAI_GROUPMESSAGE WHERE  GROUPID=16) THEN
	UPDATE IEAI_GROUPMESSAGE SET IIMG ='images/info171.png' where GROUPID=16;
	commit;
	END IF;
    IF NOT EXISTS (SELECT  *  FROM IEAI_COLLECT_ITEM WHERE  IID=-100) THEN
	INSERT INTO IEAI_COLLECT_ITEM(IID, NAME, ITEM, WIN, LINUX, AIX, INFO, ISET)  VALUES(-100, '主动采集对比', '', '', '', '', '', 1);
	commit;
	END IF;
		
	IF NOT EXISTS (SELECT  *  FROM IEAI_DBSOURCE WHERE  IDBSOURCEID=20) THEN
	INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (20,20,'指令操作维护源',0);
	commit;
	END IF;
    IF NOT EXISTS (SELECT  *  FROM IEAI_PROJECT WHERE  IID=-20) THEN
	INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-20, 0, '所有指令操作业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 20, -20, -20, -20);
	commit;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'INUMBERS') THEN
		ALTER TABLE IEAI_AGENTINFO ADD INUMBERS VARCHAR(255) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO_DELETE' AND COLUMN_NAME = 'INUMBERS') THEN
		ALTER TABLE IEAI_AGENTINFO_DELETE ADD INUMBERS VARCHAR(255) ;
	END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.22
	
		
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	
  	CREATE TABLE IF NOT EXISTS IEAI_CMDB_AGENTINFO_CZ (IID NUMERIC(19) NOT NULL ,IHOSTNAME	VARCHAR(255),IIP	VARCHAR(255),IMAC	VARCHAR(255),IMEMSIZE	VARCHAR(255),IDISKSIZE	VARCHAR(255),IOSSYSTEM	VARCHAR(255),IOSARCHITECTURE	VARCHAR(255),IOSVERSION	VARCHAR(255),IOSDISTRO	VARCHAR(255),IOSRELEASE	VARCHAR(255),IINSTANCEID	VARCHAR(255),IAGENTSTATUS	VARCHAR(255),ICPUMODEL	VARCHAR(255),CONSTRAINT PK_CMDB_AGENTINFO_CZ PRIMARY KEY (IID)) ;
	CREATE TABLE IF NOT EXISTS IEAI_USER_ROLE_RELATION(IID  NUMERIC(19) NOT NULL,  IUSERID  NUMERIC(19),  IROLEID  NUMERIC(19),  IVALIDSTARTTIME NUMERIC(19),  IVALIDENDTIME   NUMERIC(19),  IOPERUSERID     NUMERIC(19),  IOPERTIME   NUMERIC(19),  CONSTRAINT PK_IEAI_USER_ROLE_RELATION PRIMARY KEY (IID));	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IREVIEWER') THEN
		ALTER TABLE IEAI_INSTANCEINFO ADD IREVIEWER VARCHAR(255) ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IREVIEWER') THEN
		ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREVIEWER VARCHAR(255) ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IREVIEWER') THEN
		ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREVIEWER VARCHAR(255) ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IBANKNAME') THEN
		ALTER TABLE IEAI_INSTANCEINFO ADD IBANKNAME VARCHAR(255) ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IBANKNAME') THEN
		ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IBANKNAME VARCHAR(255) ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IBANKNAME') THEN
		ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IBANKNAME VARCHAR(255) ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IREVIEWSTATE') THEN
		ALTER TABLE IEAI_INSTANCEINFO ADD IREVIEWSTATE INTEGER default 1 ;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IREVIEWSTATE') THEN
		ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREVIEWSTATE INTEGER default 1;
	END IF;	
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IREVIEWSTATE') THEN
		ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREVIEWSTATE INTEGER default 1;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'IPWDFORAPPLY') THEN
		ALTER TABLE IEAI_USER ADD IPWDFORAPPLY VARCHAR(255);
	END IF;

	
	
				
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.23	
		
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'IBASEPASSWORD') THEN
			ALTER TABLE IEAI_USER ADD IBASEPASSWORD VARCHAR(255);
		END IF;
	
		IF NOT EXISTS (SELECT * FROM  IEAI_PROPERTYCONFIG P WHERE  P.IPROPERTYNAME='AutoChangePublicPasswordTime') THEN
		INSERT INTO IEAI_PROPERTYCONFIG(IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) VALUES(2,'AutoChangePublicPasswordTime','30','浦发自动变更public密码署时长设置');
		END IF;
		COMMIT;
		
		
		CREATE TABLE IF NOT EXISTS IEAI_GRID_INFO (IID NUMERIC(19) NOT NULL ,IGRIDNAME VARCHAR(255) ,ICOLNAME VARCHAR(255) ,IATTRIBUTE VARCHAR(255) ,IATTRVALUE VARCHAR(255) ,CONSTRAINT PK_IEAI_GRID_INFO PRIMARY KEY (IID));
		

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_AGENTINFO_CZ' AND COLUMN_NAME = 'IAPPNAME') THEN
			ALTER TABLE IEAI_CMDB_AGENTINFO_CZ ADD IAPPNAME VARCHAR(255);
		END IF;
		COMMIT;

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_AGENTINFO_CZ' AND COLUMN_NAME = 'ISTATUS') THEN
            ALTER TABLE IEAI_CMDB_AGENTINFO_CZ ADD ISTATUS NUMERIC(3);
        END IF;
        COMMIT;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_MENU' AND COLUMN_NAME = 'IISAPP') THEN
			ALTER TABLE IEAI_MENU ADD IISAPP INTEGER DEFAULT 0;
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.24		
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
		
		
		CREATE TABLE IF NOT EXISTS IEAI_IPMPSERVICEINFO (IID DECIMAL(19,0) NOT NULL,  IRESOURCENAME VARCHAR(255),  IURL VARCHAR(1000),  IUSERNAME     VARCHAR(255),  IPASSWORD     VARCHAR(255),  ISCOMMON      DECIMAL(2) DEFAULT 0,  ICREATEUSERID DECIMAL(19,0),  IUPDATEUSERID DECIMAL(19,0),  ICREATETIME   TIMESTAMP(6),  IUPDATETIME   TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),  IDELETE       DECIMAL(2) DEFAULT 0,  CONSTRAINT PK_IEAI_IPMPSERVICEINFO PRIMARY KEY (IID));

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROXY_LIST' AND COLUMN_NAME = 'ISTATE') THEN
            ALTER TABLE IEAI_PROXY_LIST  ADD ISTATE DECIMAL (2,0) DEFAULT 0;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_SHOW_SERVERINFO (IID NUMERIC(19) NOT NULL ,ILISTID NUMERIC(19) ,ICPU VARCHAR(255) ,IMEMORY VARCHAR(255) ,IDISK VARCHAR(255) ,ITASKNUM NUMERIC(19) ,ICREATETIME TIMESTAMP(6) ,CONSTRAINT PK_IEAI_SHOW_SERVERINFO PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SHOW_SERVERLIST (IID NUMERIC(19) NOT NULL ,IIP VARCHAR(255) ,IDESC VARCHAR(255) ,ISTARTTIME NUMERIC(19) ,ICREATETIME NUMERIC(19) ,CONSTRAINT PK_IEAI_SHOW_SERVERLIST PRIMARY KEY (IID));
		
		CREATE TABLE IF NOT EXISTS IEAI_USER_PORTAL_MIDDLE (IID NUMERIC(19) NOT NULL ,USERID NUMERIC(19) NOT NULL ,PROTALID NUMERIC(19) NOT NULL ,IROW NUMERIC(2) NOT NULL ,ICOLUMN NUMERIC(3) NOT NULL ,POSITION NUMERIC(1) DEFAULT 1 ,CONSTRAINT PK_IEAI_USER_PORTAL_MIDDLE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PORTAL (IID NUMERIC(19) NOT NULL ,TITLE VARCHAR(225) NOT NULL ,NAME VARCHAR(225) NOT NULL ,DATAURL VARCHAR(100) ,FIELDURL VARCHAR(100) ,ITYPE NUMERIC(2) NOT NULL ,DES VARCHAR(225) ,CONSTRAINT PK_IEAI_PORTAL PRIMARY KEY (IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'PORTALCLO') THEN
		    ALTER TABLE IEAI_USER ADD PORTALCLO NUMERIC(1);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IEFFECT') THEN
			ALTER TABLE IEAI_AGENTINFO ADD IEFFECT NUMERIC(1) ;
		END IF;
        CREATE TABLE IF NOT EXISTS IEAI_AGENT_DETAIL  ( IID NUMERIC(19)  NOT NULL,AGENTIP    	VARCHAR(255),AGENTPORT  	NUMERIC(19),ICREATETIME	NUMERIC(19),AGENTDIR   	VARCHAR(255),DISKSIZE   	VARCHAR(255),LOGLOCATION	VARCHAR(255),CPURATE    	VARCHAR(255),MEMORYRATE 	VARCHAR(255),IOMESS     	VARCHAR(255),CONSTRAINT PK_IEAI_AGENT_DETAIL PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_AGENTSYSTEM_PERSONINFO (IID NUMERIC(19) NOT NULL ,ISYSNAME VARCHAR(255) ,IPERSONNAME VARCHAR(255) ,IEMAIL VARCHAR(255) ,IPHONE VARCHAR(255) ,CONSTRAINT PK_IEAI_AGENTSYSTEM_PERSONINFO PRIMARY KEY (IID));
		
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.25	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISDELETE') THEN
		    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISDELETE NUMERIC(1) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISDELETEINFO') THEN
		    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISDELETEINFO VARCHAR(255) ;
		END IF;

		CREATE TABLE IF NOT EXISTS IEAI_ACTIVITY_MANAGE (IID NUMERIC(19)NOT NULL,IACTNAME VARCHAR(50), IACTDES VARCHAR(1000), IACTTYPE NUMERIC(2),ISVALID NUMERIC(2) DEFAULT 0,CONSTRAINT PK_IEAI_ACTIVITY_MANAGE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PORT (IID DECIMAL(19)NOT NULL,IPORTNAME VARCHAR(50),IPORTDES VARCHAR(1000),CONSTRAINT PK_IEAI_PORT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_ACTIVITY_PORT (IID NUMERIC(19)NOT NULL,ACTID NUMERIC(19),PORTID NUMERIC(19),FOREIGN KEY(ACTID)REFERENCES IEAI_ACTIVITY_MANAGE(IID),FOREIGN KEY(PORTID)REFERENCES IEAI_PORT(IID),CONSTRAINT PK_IEAI_ACTIVITY_PORT PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_IG_ENTITY_MODEL (IID NUMERIC(19),ENAME VARCHAR(255),EID VARCHAR(255),ESYSCODING VARCHAR(255),EPARCODING VARCHAR(255),EDESC VARCHAR(255),IINSTIME NUMERIC(19),IINSUSER VARCHAR(255),IUPDATETIME NUMERIC(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_ENTITY_MODEL PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_IG_EQUIPMENT (IID NUMERIC(19),EID VARCHAR(255),EIUPDTIME VARCHAR(50),ESYSCODING VARCHAR(255),EIROOTMARK NUMERIC(19),EIVERSION NUMERIC(10),EISMALLVERSION NUMERIC(10),EIDESC VARCHAR(255),EISTATUS NUMERIC(4),EIINSDATE VARCHAR(50),EIIP VARCHAR(18),EINAME VARCHAR(255),EIID NUMERIC(19),IINSTIME NUMERIC(19),IINSUSER VARCHAR(255),IUPDATETIME NUMERIC(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUIPMENT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_IG_EQUI_RELATION (IID NUMERIC(19),RLCODE VARCHAR(255),RLCATEGORY NUMERIC(5),FROMEID VARCHAR(255),TOEID VARCHAR(255),RLDESC VARCHAR(255),IINSTIME NUMERIC(19),IINSUSER VARCHAR(255),IUPDATETIME NUMERIC(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUI_RELATION PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_IG_FINAL_RELATION (IID NUMERIC(19),RLID NUMERIC(19),RLCODE VARCHAR(255),TOEIID NUMERIC(19),RLCATEGORY NUMERIC(4),FROMEIID NUMERIC(19),UPDATETIME VARCHAR(50),EIROOMARK VARCHAR(255),IINSTIME NUMERIC(19),IINSUSER VARCHAR(255),IUPDATETIME NUMERIC(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_FINAL_RELATION PRIMARY KEY (IID));
	 	CREATE TABLE IF NOT EXISTS IEAI_CMDB_IG_SYS_RELATION (IID NUMERIC(19),CMDBEIID NUMERIC(19),IPRJUPPERID NUMERIC(19),IUPDATETIME NUMERIC(19),IOPERUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_SYS_RELATION PRIMARY KEY (IID));

		
		END;;
	DELIMITER ;
	CALL UPPRDE();

    	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCRIPT_SEGMENT' AND COLUMN_NAME = 'IFUNCTIONALTYPE') THEN
            ALTER TABLE IEAI_SCRIPT_SEGMENT  ADD IFUNCTIONALTYPE INTEGER DEFAULT 0;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_INACRIVE_NUM') THEN
			ALTER TABLE IEAI_AGENTINFO  ADD IAGENT_INACRIVE_NUM  DECIMAL(19) DEFAULT 0;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_COLLECTION_CLASSIFY (IID DECIMAL(19)NOT NULL,ICLASSNAME VARCHAR(255), DES VARCHAR(1000),CONSTRAINT PK_IEAI_COLLECTION_CLASSIFY PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECT_ITEM' AND COLUMN_NAME = 'ISPARAMETER') THEN
			ALTER TABLE IEAI_COLLECT_ITEM ADD ISPARAMETER DECIMAL(2);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECT_DICTIONARY' AND COLUMN_NAME = 'IREMARK') THEN
			ALTER TABLE IEAI_COLLECT_DICTIONARY ADD IREMARK VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECT_DICTIONARY' AND COLUMN_NAME = 'CLASSID') THEN
			ALTER TABLE IEAI_COLLECT_DICTIONARY ADD CLASSID DECIMAL(19);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ACTIVITY_MANAGE' AND COLUMN_NAME = 'IATOMACT') THEN
			ALTER TABLE IEAI_ACTIVITY_MANAGE ADD IATOMACT DECIMAL(2) DEFAULT 0;
		END IF;
	
	    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ACTIVITY_MANAGE' AND COLUMN_NAME = 'IPARAMS') THEN
			ALTER TABLE IEAI_ACTIVITY_MANAGE ADD IPARAMS VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_EQUI_RELATION' AND INDEX_NAME = 'IDX_CMDB_IG_EQUI_RELATION_01') THEN
			CREATE INDEX IDX_CMDB_IG_EQUI_RELATION_01 ON IEAI_CMDB_IG_EQUI_RELATION(RLCODE);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_FINAL_RELATION' AND INDEX_NAME = 'IDX_CMDB_IG_FINAL_RELATION_01') THEN
			CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_01 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,TOEIID);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_FINAL_RELATION' AND INDEX_NAME = 'IDX_CMDB_IG_FINAL_RELATION_02') THEN
			CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_02 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,FROMEIID);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_EQUIPMENT' AND INDEX_NAME = 'IDX_CMDB_IG_EQUIPMENT_01') THEN
			CREATE INDEX IDX_CMDB_IG_EQUIPMENT_01 ON IEAI_CMDB_IG_EQUIPMENT(EIID);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_ENTITY_MODEL' AND INDEX_NAME = 'IDX_CMDB_IG_ENTITY_MODEL_01') THEN
			CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_01 ON IEAI_CMDB_IG_ENTITY_MODEL(ESYSCODING);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_ENTITY_MODEL' AND INDEX_NAME = 'IDX_CMDB_IG_ENTITY_MODEL_02') THEN
			CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_02 ON IEAI_CMDB_IG_ENTITY_MODEL(ENAME);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_EQUIPMENT' AND INDEX_NAME = 'IDX_CMDB_IG_EQUIPMENT_02') THEN
			CREATE INDEX IDX_CMDB_IG_EQUIPMENT_02 ON IEAI_CMDB_IG_EQUIPMENT(ESYSCODING);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_FINAL_RELATION' AND INDEX_NAME = 'IDX_CMDB_IG_FINAL_RELATION_03') THEN
			CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_03 ON IEAI_CMDB_IG_FINAL_RELATION(FROMEIID,TOEIID);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_SYS_RELATION' AND INDEX_NAME = 'IDX_CMDB_IG_SYS_RELATION_01') THEN
			CREATE INDEX IDX_CMDB_IG_SYS_RELATION_01 ON IEAI_CMDB_IG_SYS_RELATION(IPRJUPPERID,CMDBEIID);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_CMDB_IG_EQUIPMENT' AND INDEX_NAME = 'IDX_CMDB_IG_EQUIPMENT_03') THEN
			CREATE INDEX IDX_CMDB_IG_EQUIPMENT_03 ON IEAI_CMDB_IG_EQUIPMENT(EIID,EIIP,EINAME);
		END IF;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.26


		DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN		
		CREATE TABLE IF NOT EXISTS IEAI_WARN_PLAN (IDETAILID NUMERIC(19) NOT NULL ,ISCENEID NUMERIC(19) NULL ,IUSERID NUMERIC(19) NULL ,IWAYTYPES VARCHAR(128) ,CONSTRAINT PK_IEAI_WARN_PLAN PRIMARY KEY (IDETAILID) );
		CREATE TABLE IF NOT EXISTS IEAI_BUSI_MODULE (IMODULEID NUMERIC(19) NOT NULL ,IMODULECODE VARCHAR(30) ,IMODULENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG NUMERIC(1) ,CONSTRAINT PK_IEAI_BUSI_MODULE PRIMARY KEY (IMODULEID) );
		CREATE TABLE IF NOT EXISTS IEAI_BUSI_TYPE (ITYPEID NUMERIC(19) NOT NULL ,ITYPECODE VARCHAR(30) ,ITYPENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG NUMERIC(1) ,CONSTRAINT PK_IEAI_BUSI_TYPE PRIMARY KEY (ITYPEID) );
		CREATE TABLE IF NOT EXISTS IEAI_BUSI_LEVEL (ILEVELID NUMERIC(19) NOT NULL ,ILEVELCODE VARCHAR(30) ,ILEVELNAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG NUMERIC(1) ,CONSTRAINT PK_IEAI_BUSI_LEVEL PRIMARY KEY (ILEVELID) );
		CREATE TABLE IF NOT EXISTS IEAI_WARN_SCENE (ISCENEID NUMERIC(19) NOT NULL ,IMODULECODE VARCHAR(30) ,ITYPECODE VARCHAR(30) ,ILEVELCODE VARCHAR(30) ,ISTATUS NUMERIC(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_WARN_SCENE PRIMARY KEY (ISCENEID) );
		CREATE TABLE IF NOT EXISTS IEAI_WARN( iwarnid NUMERIC(19) not null, imodulecode VARCHAR(30),itypecode VARCHAR(30), ilevelcode VARCHAR(30), iip VARCHAR(30), ihappentime TIMESTAMP, iwarnmsg VARCHAR(4000), createtime TIMESTAMP, CONSTRAINT PK_IEAI_WARN primary key (iwarnid));
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		DECLARE  target_database VARCHAR(100);
		SELECT DATABASE() INTO target_database;

		CREATE TABLE IF NOT EXISTS IEAI_USER_QUICK_MIDDLE (IID DECIMAL(19)NOT NULL,QUICKID DECIMAL(19), USERID DECIMAL(19),ISNODEF DECIMAL(2),ICON VARCHAR(255),CONSTRAINT PK_IEAI_USER_QUICK_MIDDLE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_NOTICE_MANAGE(IID NUMERIC(19) NOT NULL,NOTICENAME VARCHAR(225),NOTICECONTENT VARCHAR(3000),ISTATE NUMERIC(2) DEFAULT 1,TAKEEFFECTTIME TIMESTAMP,INVALIDTIME TIMESTAMP,USERID NUMERIC(19) NOT NULL,CONSTRAINT PK_IEAI_NOTICE_MANAGE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PLATFORM_NEWS(IID NUMERIC(19) NOT NULL,NEWSSOURCE NUMERIC(2),NEWCONTENT VARCHAR(2000),ICREATETIME TIMESTAMP(6),USERID NUMERIC(19) NOT NULL,IENDTIME TIMESTAMP(6),PROJECTID NUMERIC(19),CONSTRAINT PK_IEAI_PLATFORM_NEWS PRIMARY KEY (IID));
		
		CREATE TABLE IF NOT EXISTS IEAI_ORGMANAGEMENT (IID NUMERIC(19) NOT NULL ,INAME VARCHAR(255) ,IPARENTID NUMERIC(19) ,CONSTRAINT PK_IEAI_ORGMANAGEMENT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_ORGMANAGEMENT_USER (IID NUMERIC(19) NOT NULL ,MANAGEMENTID NUMERIC(19) ,USERID NUMERIC(19) ,CONSTRAINT PK_IEAI_ORGMANAGEMENT_USER PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FILECLASS (IID NUMERIC(19) NOT NULL ,ICLASSNAME VARCHAR(255) ,ICLASSDESC VARCHAR(255) ,ICREATETIME TIMESTAMP ,IPARENTID NUMERIC(19) ,CONSTRAINT PK_IEAI_FILECLASS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FILE_USER (IID NUMERIC(19) NOT NULL ,IFILEID NUMERIC(19) ,IUSERID NUMERIC(19) ,CONSTRAINT PK_IEAI_FILE_USER PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FILEINFO (IID NUMERIC(19) NOT NULL ,ICLASSIDONE NUMERIC(19) ,ICLASSIDTWO NUMERIC(19) ,IFILENAME VARCHAR(255) ,IFILETYPE VARCHAR(255) ,IFILECONTENT LONGBLOB ,IFILEVERSION NUMERIC(19) ,IFILEDESC VARCHAR(255) ,IUPLOADTIME TIMESTAMP ,IUPLOADUSERID NUMERIC(19) ,ISTATE NUMERIC(1) ,CONSTRAINT PK_IEAI_FILEINFO PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FILEINFO_HIS (IID NUMERIC(19) NOT NULL ,IMYFILEID NUMERIC(19) ,ICLASSIDONE NUMERIC(19) ,ICLASSIDTWO NUMERIC(19) ,IFILENAME VARCHAR(255) ,IFILETYPE VARCHAR(255) ,IFILECONTENT LONGBLOB ,IFILEVERSION NUMERIC(19) ,IFILEDESC VARCHAR(255) ,IUPLOADTIME TIMESTAMP ,IUPLOADUSERID NUMERIC(19) ,ISTATE NUMERIC(1) ,CONSTRAINT PK_IEAI_FILEINFO_HIS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_AUDIT_DATADICTIONARY (IID NUMERIC(19) NOT NULL,URL VARCHAR(500),MODELTYPE NUMERIC(19),KEYNAME VARCHAR(100),CNNAME VARCHAR(100),DICTIONARYDESC VARCHAR(4000),CONSTRAINT PK_IEAI_AUDIT_DATADICTIONARY PRIMARY KEY (IID));
		
    CREATE TABLE IF NOT EXISTS IEAI_PORTAL_TOP (IID NUMERIC(19) NOT NULL ,EQUIPMENTNUM NUMERIC(19) ,ONLINEUSERNUM NUMERIC(5) ,PROCESSRELEASE NUMERIC(19) ,SCRIPTNUM NUMERIC(19) ,SYSTEMNUM NUMERIC(19) ,USERLOGINNUM NUMERIC(19) ,CONSTRAINT PK_IEAI_PORTAL_TOP PRIMARY KEY (IID));
    
    CREATE TABLE IF NOT EXISTS IEAI_AGENTSHELL (IID NUMERIC(19) NOT NULL, IAGENTINFO_ID NUMERIC(19) NOT NULL, ICHECKTIME NUMERIC(19),ISTATE NUMERIC(1), IMESSAGE LONGTEXT, CONSTRAINT PK_IEAI_AGENTSHELL PRIMARY KEY (IID));
    


		
		
		IF NOT EXISTS (SELECT * FROM IEAI_DICTIONARY  WHERE DID = 998 ) THEN
			INSERT INTO IEAI_DICTIONARY VALUES(998,0,998,'消息展示天数',15);
		commit;
		END IF;

		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'DATAURL') THEN
			ALTER TABLE IEAI_PORTAL modify DATAURL VARCHAR(500)  NULL;
			END IF;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'FIELDURL') THEN
			ALTER TABLE IEAI_PORTAL modify FIELDURL VARCHAR(100)  NULL;
			END IF;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'ITYPE') THEN
			ALTER TABLE IEAI_PORTAL modify ITYPE VARCHAR(100)  NULL;
			END IF;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_ADAPTOR_BLOB' AND COLUMN_NAME = 'ICONTENT') THEN
			ALTER TABLE IEAI_ADAPTOR_BLOB modify ICONTENT LONGBLOB  NULL;
			END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'CUSTOMCLASS') THEN
			ALTER TABLE IEAI_PORTAL ADD CUSTOMCLASS VARCHAR(225);
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_STANDA_SCREEN_LIST (IID NUMERIC(19) NOT NULL ,TASKID NUMERIC(19) ,TASKNAME VARCHAR(225) ,STRATEGYTYPE NUMERIC(2) ,INAME VARCHAR(225) ,STATUS VARCHAR(225) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_LIST PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_STANDA_SCREEN_STATUS (IID NUMERIC(19) NOT NULL ,STATUS VARCHAR(50) ,TOTAL NUMERIC(10) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_STATUS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_STANDA_SCREEN_DETAILS (IID NUMERIC(19) NOT NULL ,IP VARCHAR(225) ,TASKNAME VARCHAR(225) ,TASKID NUMERIC(19) ,STATUS VARCHAR(225) ,CPNAME VARCHAR(225) ,STRATEGYTYPE NUMERIC(1) ,IFLOWID NUMERIC(19) ,ISTARTUSER VARCHAR(225) ,IEXECUSER VARCHAR(225) ,PERFORMUSER VARCHAR(225) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_DETAILS PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM IEAI_PORTAL WHERE  IID=1) THEN
			INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES ('1', '日常操作-执行中的运维监控', 'collectEnvironment', 'getDmRunningList.do', 'getColurltaskMinitor.do', '1', '日常操作-执行中的运维监控', NULL);
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_PORTAL WHERE  IID=2) THEN
			INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES ('2', '日常操作-待执行的运维监控', 'taskMonitor', 'getDmNotRunList.do', 'getTaskMontior.do', '1', '日常操作-待执行的运维监控', NULL);
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_PORTAL WHERE  IID=3) THEN
			INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES ('3', '标准运维统计', 'standardLineChart', NULL, NULL, '0', NULL, 'Ext.app.StandardLineChart');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_PORTAL WHERE  IID=4) THEN
			INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES ('4', '脚本服务化', 'scriptServerLineChart', NULL, NULL, '0', NULL, 'Ext.app.ScriptServerLineChart');
		END IF;
		COMMIT;
		

COMMIT;



		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		create PROCEDURE UPPRDE()
		BEGIN	
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_GRID_INFO' AND COLUMN_NAME = 'ILOGINNAME') THEN
			    ALTER TABLE IEAI_GRID_INFO ADD ILOGINNAME VARCHAR(255) ;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_GRID_INFO' AND COLUMN_NAME = 'ITYPE') THEN
			    ALTER TABLE IEAI_GRID_INFO ADD ITYPE NUMERIC(1) ;
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_GRID_INFO' AND COLUMN_NAME = 'IWIDTH') THEN
			    ALTER TABLE IEAI_GRID_INFO ADD IWIDTH VARCHAR(10) ;
			END IF;
			CREATE TABLE IF NOT EXISTS IEAI_COLLECT_ENVIRONMENT(  IID DECIMAL(14) NOT NULL,  INAME VARCHAR(32),  IP VARCHAR(25),  CONSTRAINT  PK_COLLECT_ENVIRONMENT PRIMARY KEY(IID));
		END;;
	DELIMITER ;
	CALL UPPRDE();

	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		create PROCEDURE UPPRDE()
		BEGIN	
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AUDIT' AND COLUMN_NAME = 'HOSTNAME') THEN
				ALTER TABLE IEAI_AUDIT ADD HOSTNAME VARCHAR(225);
			END IF;
			CREATE TABLE IF NOT EXISTS IEAI_STANDA_SCREEN_CHART (IID NUMERIC(19) NOT NULL ,TOTALFLWO NUMERIC(19) ,TORUNFLWO NUMERIC(19) ,ENDFLWO NUMERIC(19) ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,CONSTRAINT PK_IEAI_STANDA_SCREEN_CHART PRIMARY KEY (IID));

		END;;
	DELIMITER ;
	CALL UPPRDE();
-- 4.7.27
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		create PROCEDURE UPPRDE()
		BEGIN	
	CREATE TABLE IF NOT EXISTS IEAI_AGENCYTASK (IID NUMERIC(19) NOT NULL, SUM_NO VARCHAR(255),SUM_REMARK VARCHAR(255),SUM_CONTENT VARCHAR(255),MODEL_TYPE VARCHAR(255),MODEL_NAME VARCHAR(255),OPER_ID VARCHAR(255),USER_ID VARCHAR(255),CALL_ID VARCHAR(255),CALL_NAME VARCHAR(255),CALL_DEPT VARCHAR(255), CREATE_TIME VARCHAR(255), DUE_TIME VARCHAR(255),TX_TIME VARCHAR(255),TX_CODE VARCHAR(255),TRADE_CODE VARCHAR(255),CLI_SERIAL_NO VARCHAR(255),TERMI_MARK VARCHAR(255),MAC_NAME VARCHAR(255),MAC_KEY_NAME VARCHAR(255),MAC_DATA VARCHAR(255),RESERVE VARCHAR(255),AUDIT_STATE NUMERIC(2),EXEC_STATE NUMERIC(2),RECORD0_CNT VARCHAR(255),RECORD0 VARCHAR(255),RECORD1_CNT VARCHAR(255),RECORD1 VARCHAR(255),CONSTRAINT PK_IEAI_AGENCYTASK PRIMARY KEY (IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AUDIT' AND COLUMN_NAME = 'GROUPNAME') THEN
				ALTER TABLE IEAI_AUDIT ADD GROUPNAME VARCHAR(225);
			END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_STANDARD_TASK' AND COLUMN_NAME = 'INSTANCETIME') THEN
			ALTER TABLE IEAI_STANDARD_TASK ADD INSTANCETIME NUMERIC(19);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'TEMP_IEAI_DM_TASKLIST' AND COLUMN_NAME = 'INSTANCETIME') THEN
			ALTER TABLE TEMP_IEAI_DM_TASKLIST ADD INSTANCETIME NUMERIC(19);
		END IF;

		CREATE TABLE IF NOT EXISTS IEAI_CMDBINFO (IID DECIMAL(19) NOT NULL,IINSTANCEID  Varchar(255),IIP 	Varchar(255),IHOSTNAME Varchar(255),IPORT	Varchar(50),IOPERATESYSTEM	Varchar(255),ISYSTEMVERSION	Varchar(255),ICPU	Varchar(255),IARCHITECTURE	Varchar(255),IPHYSICALCORES	Varchar(10),ILOGICALCORES	Varchar(10),IMEMORYSIZE	Varchar(100),IEQUIPMENT  Varchar(255),IOUTBANDIP 	Varchar(255),IAGENTSTATUS VARCHAR(255),IEQUTYPE VARCHAR(2),CONSTRAINT PK_IEAI_CMDBINFO PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDBCLOB_LOCAL(IID DECIMAL(19) NOT NULL,ICMDBINFOID Varchar(19),IINSTANCEID  Varchar(19),ICOLUMNNAME Varchar(255), ITYPE Varchar(255),INULL SMALLINT, ICONTENT LONGTEXT,IAID DECIMAL(19), ICPID DECIMAL(19),IEQUTYPE VARCHAR(2),CONSTRAINT PK_IEAI_CMDBCLOB_LOCAL PRIMARY KEY (IID));					
		CREATE TABLE IF NOT EXISTS IEAI_CMDBCLOB  (IID DECIMAL(19) NOT NULL,ICMDBINFOID Varchar(19),IINSTANCEID  Varchar(19), ICOLUMNNAME Varchar(255), ITYPE   Varchar(255), INULL SMALLINT,ICONTENT LONGTEXT, IAID DECIMAL(19),ICPID DECIMAL(19),IEQUTYPE VARCHAR(2), CONSTRAINT PK_IEAI_CMDBCLOB PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDBCLOB_SUS  (IID DECIMAL(19) NOT NULL, INULL SMALLINT,  ICONTENT LONGTEXT , CONSTRAINT PK_IEAI_CMDBCLOB_SUS PRIMARY KEY (IID));

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WARN' AND COLUMN_NAME = 'ISYSTEMNAME') THEN
			ALTER TABLE IEAI_WARN ADD ISYSTEMNAME VARCHAR(255) ;
		END IF;
	
		CREATE TABLE IF NOT EXISTS IEAI_WARN_DETAIL (IID NUMERIC(19) NOT NULL ,ISENCEID NUMERIC(19) ,IWARNTYPE VARCHAR(255) ,ITYPE VARCHAR(255),CONSTRAINT PK_IEAI_WARN_DETAIL PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_USER_RELATION (IID NUMERIC(19) NOT NULL ,IUSERID NUMERIC(19) ,ISENCEID NUMERIC(19) ,CONSTRAINT PK_IEAI_USER_RELATION PRIMARY KEY(IID));
		
		
		COMMIT;

	
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		create PROCEDURE UPPRDE()
		BEGIN	
	CREATE TABLE IF NOT EXISTS IEAI_BATCH_UPGRADE (IID NUMERIC(19) ,ISRCPATH VARCHAR(255) ,IDESTPATH VARCHAR(255) ,IBAKPATH VARCHAR(255) ,IOSTYPE NUMERIC(1) ,CONSTRAINT PK_IEAI_BATCH_UPGRADE PRIMARY KEY(IID));
	IF NOT EXISTS (SELECT * FROM IEAI_PARAMETER_CONFIG  WHERE  IID=29) THEN
		INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(29,'batchUpgradeFtpInfo','-1');
	END IF;
	COMMIT;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'ISTEPIDENTITY') THEN
    	ALTER TABLE IEAI_INSTANCEINFO ADD ISTEPIDENTITY VARCHAR(255) ;
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();

-- 4.7.28

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER_QUICK_MIDDLE' AND COLUMN_NAME = 'IORDER') THEN
			ALTER TABLE IEAI_USER_QUICK_MIDDLE ADD IORDER NUMERIC(2) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_STANDA_SCREEN_DETAILS' AND COLUMN_NAME = 'CREATETIME') THEN
			ALTER TABLE IEAI_STANDA_SCREEN_DETAILS ADD CREATETIME TIMESTAMP(6);
		END IF;

		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AUDIT' AND COLUMN_NAME = 'IIDARR') THEN
			ALTER TABLE IEAI_AUDIT ADD IIDARR VARCHAR(255);
		END IF;	
				
		
		IF NOT EXISTS (SELECT * FROM IEAI_SYNCCMDB_INFO WHERE  IID = 1) THEN
		INSERT INTO IEAI_SYNCCMDB_INFO (IID,SERVICERUNFLAG,CRONVALUE,SYNC_IP_COUNT,TIMINGJOBFLAG,CMDBTHREADFLAG) VALUES (1,'0','0 0 0 1-31 * ?','100','0','1');
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENCYTASK' AND COLUMN_NAME = 'RECORD_NUM') THEN
			ALTER TABLE IEAI_AGENCYTASK ADD RECORD_NUM VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENCYTASK' AND COLUMN_NAME = 'RECORD_KEY') THEN
			ALTER TABLE IEAI_AGENCYTASK ADD RECORD_KEY VARCHAR(255);
		END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.29	

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENCYTASK' AND COLUMN_NAME = 'AUDIT_USER') THEN
			ALTER TABLE IEAI_AGENCYTASK ADD AUDIT_USER VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WARN_DETAIL' AND COLUMN_NAME = 'IMSGTYPE') THEN
			ALTER TABLE IEAI_WARN_DETAIL ADD IMSGTYPE VARCHAR(255) ;
		END IF;
				
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WARN' AND COLUMN_NAME = 'ICOMPARERULE') THEN
			ALTER TABLE IEAI_WARN ADD ICOMPARERULE VARCHAR(255) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WARN' AND COLUMN_NAME = 'IHOSTNAME') THEN
			ALTER TABLE IEAI_WARN ADD IHOSTNAME VARCHAR(255) ;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_WARN_USERS (IID NUMERIC(19) NOT NULL ,IWARNID NUMERIC(19) ,IUSERNAME VARCHAR(255) ,ICONNTYPE VARCHAR(255) ,CONSTRAINT PK_IEAI_WARN_USERS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_WARN_COLUMNS (IID NUMERIC(19) NOT NULL ,ICOLUMNKEY VARCHAR(255) ,ICOLUMNVALUE VARCHAR(255) ,ISCENEID NUMERIC(19) ,ITYPE VARCHAR(255) ,CONSTRAINT PK_IEAI_WARN_COLUMNS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_WARN_MESSAGE (IID NUMERIC(19) NOT NULL ,IWARNID NUMERIC(19) ,ICOLUMNKEY VARCHAR(255) ,ICOLUMNVALUE VARCHAR(255) ,ISCENEID NUMERIC(19) ,IVALUE LONGTEXT ,CONSTRAINT PK_IEAI_WARN_MESSAGE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_ANNOUNCEMENT_USER (IID NUMERIC(19) NOT NULL ,USERID NUMERIC(19) ,ITYPE VARCHAR(255)  ,CONSTRAINT PK_IEAI_ANNOUNCEMENT_USER PRIMARY KEY (IID));
		
			END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	
DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_GUARDPORT') THEN
			ALTER TABLE IEAI_AGENTINFO ADD IAGENT_GUARDPORT NUMERIC(19) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM IEAI_DBSOURCE WHERE  IDBSOURCEID=23) THEN
			INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES(23,23,'数据采集源','','','','',0);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_AZNAME') THEN
			ALTER TABLE IEAI_AGENTINFO ADD IAGENT_AZNAME VARCHAR(255) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_NETID') THEN
			ALTER TABLE IEAI_AGENTINFO ADD IAGENT_NETID VARCHAR(255) default '(无)';
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'IAGENT_NETID') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD IAGENT_NETID VARCHAR(255) default '(无)' ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'ICMDBKFCS_UPDATE_LASTTIME') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD ICMDBKFCS_UPDATE_LASTTIME VARCHAR(50) default '(无)' ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'ICMDB_UPDATE_LASTTIME') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD ICMDB_UPDATE_LASTTIME VARCHAR(50) default '(无)' ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'ICMDB_UPDATE_DESC') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD ICMDB_UPDATE_DESC VARCHAR(50) default '(无)' ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'ICMDB_DELIVER_TIME') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD ICMDB_DELIVER_TIME VARCHAR(50) default '(无)' ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'recycle_status') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD recycle_status VARCHAR(50) default '(无)' ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENT_PROJECT_CONFIG' AND COLUMN_NAME = 'ICHARGEUSERMAIL') THEN
			ALTER TABLE IEAI_AGENT_PROJECT_CONFIG ADD ICHARGEUSERMAIL VARCHAR(50) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECT_ITEM' AND COLUMN_NAME = 'CRONVALUE') THEN
			ALTER TABLE IEAI_COLLECT_ITEM ADD CRONVALUE VARCHAR(255) default '(无)' ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECT_ITEM' AND COLUMN_NAME = 'ISCRONTASK') THEN
			ALTER TABLE IEAI_COLLECT_ITEM ADD ISCRONTASK DECIMAL(2) DEFAULT 0 ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECT_RESULT_LATEST' AND COLUMN_NAME = 'SCRIPT_NAME') THEN
			ALTER TABLE IEAI_COLLECT_RESULT_LATEST ADD SCRIPT_NAME VARCHAR(255) default '(无)' ;
		END IF;




	END;;
	DELIMITER ;
	
	CALL UPPRDE();
			

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
		
		DROP INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST;
		CREATE INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST (IP ASC);
		
	END;;
	DELIMITER ;
	
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
     		
		CREATE TABLE IF NOT EXISTS  IEAI_PROXY_VMIP(IID DECIMAL(19,0) NOT NULL,IPROXYID DECIMAL(19,0) NOT NULL,IUSERID DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),ISWITCHIP VARCHAR(25),ISWITCHOUTIP VARCHAR(25),CONSTRAINT PK_IEAI_PROXY_VMIP PRIMARY KEY(IID));

		CREATE TABLE IF NOT EXISTS  IEAI_EXTJS_KEYVAL(IID DECIMAL(19,0) NOT NULL,IEXTKEY VARCHAR(4000),IEXTVAL VARCHAR(4000),CONSTRAINT PK_IEAI_EXTJS_KEYVAL PRIMARY KEY(IID));
	END;;
	DELIMITER ;
	CALL UPPRDE();

	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_MONITOR_STATE') THEN
			ALTER TABLE IEAI_AGENTINFO ADD IAGENT_MONITOR_STATE NUMERIC(1) ;
		END IF;

		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
		
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
			 CREATE TABLE IF NOT EXISTS  IEAI_SCRIPT_SQL_RESULT(IID  DECIMAL(19) NOT NULL, IFOREIGNID VARCHAR(100), IFOREIGNHEADID DECIMAL(19), IRESULT LONGTEXT, ISQLKEY VARCHAR(255), IFROM DECIMAL(1),ISQL LONGTEXT, CONSTRAINT PK_IEAI_SCRIPT_SQL_RESULT PRIMARY KEY (IID));
 			 CREATE TABLE IF NOT EXISTS  IEAI_SCRIPT_SQL_HEAD(IID DECIMAL(19) NOT NULL, IFOREIGNID VARCHAR(100), IRESULT LONGTEXT,IFROM DECIMAL(1),ISQLKEY VARCHAR(255),CONSTRAINT PK_IEAI_SCRIPT_SQL_HEAD PRIMARY KEY (IID));
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
		DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCRIPT_SQL_RESULT' AND COLUMN_NAME = 'ISQL') THEN
		ALTER TABLE IEAI_SCRIPT_SQL_RESULT ADD ISQL LONGTEXT ;
	END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	-- 20201010
	
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	DECLARE  target_database VARCHAR(100);
 	SELECT DATABASE() INTO target_database;
		
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBINFO' AND COLUMN_NAME = 'BUSINESSNAME') THEN
	    ALTER TABLE IEAI_CMDBINFO ADD BUSINESSNAME VARCHAR(225);
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBINFO' AND COLUMN_NAME = 'ENVIRONMENT') THEN
	    ALTER TABLE IEAI_CMDBINFO ADD ENVIRONMENT VARCHAR(225);
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBINFO' AND COLUMN_NAME = 'IPZX') THEN
	    ALTER TABLE IEAI_CMDBINFO ADD IPZX VARCHAR(225);
	END IF;
	CREATE TABLE IF NOT EXISTS IEAI_CMDBLABEL (IID NUMERIC(19) NOT NULL ,HOSTTYPE VARCHAR(10) ,LABELNAME VARCHAR(225) ,TYPE VARCHAR(10) ,CONSTRAINT PK_IEAI_CMDBLABEL PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_CMDB_MESSAGE (IID NUMERIC(19) NOT NULL ,IP VARCHAR(255) ,IHOSTNAME VARCHAR(255) ,ISYSTEMINFO VARCHAR(225) ,ICENTERNAME VARCHAR(255) ,ISYSADMIN VARCHAR(255) ,IAPPADMIN VARCHAR(255) ,ICOLLECTRESULT VARCHAR(255) ,IKEYNAME VARCHAR(255) ,ICMDB_UPDATE_DESC VARCHAR(50) ,ICMDB_UPDATE_LASTTIME VARCHAR(50) ,CONSTRAINT PK_IEAI_CMDB_MESSAGE PRIMARY KEY (IID));
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = TARGET_DATABASE AND TABLE_NAME = 'IEAI_CMDBCLOB_LOCAL' AND COLUMN_NAME = 'ICONTENT' AND DATA_TYPE='longtext') THEN
		ALTER TABLE IEAI_CMDBCLOB_LOCAL DROP COLUMN ICONTENT;
    END IF;
	COMMIT;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBCLOB_LOCAL' AND COLUMN_NAME = 'ICONTENT') THEN
	    ALTER TABLE IEAI_CMDBCLOB_LOCAL ADD ICONTENT VARCHAR(1000);
	END IF;
	COMMIT;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DATASYNC_TABLEINFO' AND COLUMN_NAME = 'IDMARK') THEN
			ALTER TABLE IEAI_DATASYNC_TABLEINFO ADD IDMARK VARCHAR(255) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SERVERLIST' AND COLUMN_NAME = 'IDISK') THEN
			ALTER TABLE IEAI_SERVERLIST ADD IDISK VARCHAR(20) ;
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IAGENT_MONITOR_STATE') THEN 
			ALTER TABLE IEAI_AGENTINFO ADD IAGENT_MONITOR_STATE NUMERIC (19); 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();
-- 8.4.0
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	DECLARE  target_database VARCHAR(100);
	SELECT DATABASE() INTO target_database;
	
	CREATE TABLE IF NOT EXISTS IEAI_PARAMETER_MANAGER (IID DECIMAL(19,0) NOT NULL, IPARAMNAME VARCHAR(255),IPARAMVALUE VARCHAR(255),IPARAMDESC VARCHAR(255),ISCOPE VARCHAR(255), ICREATEUSERID DECIMAL(19,0),IUPDATEUSERID DECIMAL(19,0),ICREATETIME DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),CONSTRAINT PK_IEAI_PARAMETER_MANAGER PRIMARY KEY (IID));


    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBCLOB_LOCAL' AND COLUMN_NAME = 'HOSTTYPE') THEN
        ALTER TABLE IEAI_CMDBCLOB_LOCAL ADD HOSTTYPE VARCHAR(10);
    END IF;

    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBCLOB' AND COLUMN_NAME = 'HOSTTYPE') THEN
        ALTER TABLE IEAI_CMDBCLOB ADD HOSTTYPE VARCHAR(10);
    END IF;
	
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IOSBASEVALUE') THEN 
			ALTER TABLE IEAI_AGENTINFO ADD IOSBASEVALUE VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();
	
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN

		CREATE TABLE  IF NOT EXISTS IEAI_WARN_AGENT (IID NUMERIC(19) NOT NULL,IAGENTID NUMERIC(19) ,IWARNTYPE VARCHAR(5) ,IWARNCOUNT NUMERIC(19) ,IFIRSTTIME NUMERIC(19) ,ILASTTIME NUMERIC(19) , CONSTRAINT PK_IEAI_WARN_AGENT PRIMARY KEY (IID));
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_AGENT_START_STOP (IID NUMERIC (19) ,IAGENTIP VARCHAR (255) ,IAGENTPORT NUMERIC (19) ,IOSNAME VARCHAR (255) ,ISTATE NUMERIC (19) ,IUSERNAME VARCHAR (255) ,ITIME NUMERIC (19) ,ITYPE NUMERIC (19) ,IUPDATESTATE NUMERIC (19) ,IMESSAGE VARCHAR (255) , CONSTRAINT PK_IEAI_AGENT_START_STOP PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_IPMP_SYSTEM_RELATON (IID NUMERIC (19) NOT NULL,IPROJECTID NUMERIC (19),IPMPSYSNAME VARCHAR (255), CONSTRAINT PK_IEAI_IPMP_SYSTEM_RELATON PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_LOGINRANGE_VALIDATION (IID NUMERIC (19) NOT NULL ,IIPRANGE VARCHAR (255) ,CREATORUSER VARCHAR (255) ,CREATORDATE NUMERIC (19) ,UPDATEUSER VARCHAR (255) ,UPDATEDATE NUMERIC (19) , CONSTRAINT PK_IEAI_LOGINRANGE_VALIDATION PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();


-- 8.6.0
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_BATCH_UPGRADE' AND COLUMN_NAME='IUPDATEVERSION') THEN
			ALTER TABLE IEAI_BATCH_UPGRADE ADD IUPDATEVERSION VARCHAR(255);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_BATCH_UPGRADE' AND COLUMN_NAME='IACTFINISHMAXWAIT') THEN
			ALTER TABLE IEAI_BATCH_UPGRADE ADD IACTFINISHMAXWAIT NUMERIC(19);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_BATCH_UPGRADE' AND COLUMN_NAME='IWRITEBUFFERSIZE') THEN
			ALTER TABLE IEAI_BATCH_UPGRADE ADD IWRITEBUFFERSIZE NUMERIC(19);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME='UPDATEVERSION') THEN
			ALTER TABLE IEAI_AGENTUPDATE_INFO ADD UPDATEVERSION VARCHAR(255);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME='ACTFINISHMAXWAIT') THEN
			ALTER TABLE IEAI_AGENTUPDATE_INFO ADD ACTFINISHMAXWAIT NUMERIC(19);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME='WRITEBUFFERSIZE') THEN
			ALTER TABLE IEAI_AGENTUPDATE_INFO ADD WRITEBUFFERSIZE NUMERIC(19);
		END IF;
	  END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='IUPDATEVERSION') THEN
			ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IUPDATEVERSION VARCHAR(255);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='IACTFINISHMAXWAIT') THEN
			ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IACTFINISHMAXWAIT NUMERIC(19);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='IWRITEBUFFERSIZE') THEN
			ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IWRITEBUFFERSIZE NUMERIC(19);
		END IF;
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_ENTEGOR_CONFIG (IID NUMERIC (19) ,IKEY VARCHAR (255) ,IVALUE VARCHAR (255) ,IDES VARCHAR (1500) ,ITYPE NUMERIC (19) ,ISTATUS NUMERIC (19) ,IDEFAULT VARCHAR (255) , CONSTRAINT PK_IEAI_ENTEGOR_CONFIG PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
-- 8.7.0--
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

    CREATE TABLE IF NOT EXISTS IEAI_LOGIN_KAPTCHA(IID DECIMAL(19,0) NOT NULL, IURL VARCHAR(255), USERID DECIMAL(19,0),OPERTIME DECIMAL(19,0),ISTATE INTEGER,IP VARCHAR(25),IMAC VARCHAR(25),HOSTNAME VARCHAR(255),IKAPTCHA VARCHAR(10),CONSTRAINT PK_IEAI_LOGIN_KAPTCHA PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='SYSTEMSTATUS') THEN
			ALTER TABLE IEAI_AGENTINFO ADD SYSTEMSTATUS VARCHAR(100);
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='CPNAME') THEN
			ALTER TABLE IEAI_COMPUTER_LIST MODIFY COLUMN CPNAME VARCHAR(255);
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID=2261) THEN
			INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(2261, '告警看板', 100, 'initIeaiWarnViewPage.do', 2261, '告警管理', 'images/info83.png');
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM IEAI_SYS_DICTIONARY WHERE  DID=200) THEN
			INSERT INTO IEAI_SYS_DICTIONARY(DID, DNAME, DTYPE, DINTVALUE) VALUES(200, 'LOGINCONFIG', 'INTEGER', '0');
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();
-- 8.9.0--
DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_PROXY_LIST' AND COLUMN_NAME='IVERSION') THEN
			ALTER TABLE IEAI_PROXY_LIST ADD COLUMN IVERSION VARCHAR(25);
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_PUBLIC_NOTICE_MESSAGE (IID NUMERIC (19) NOT NULL,INOTICEINFO VARCHAR (255),IEXECUCYCLE VARCHAR (255),IISSHOW NUMERIC (1), CONSTRAINT PK_IEAI_PUBLIC_NOTICE PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_DEPT (IID NUMERIC (19) ,IDEPTID NUMERIC (19) ,IDEPTNAME VARCHAR (255) ,ICREATETIME VARCHAR (255) ,IPARENTID NUMERIC (19) ,IDEPTDESC VARCHAR (255) , CONSTRAINT PK_IEAI_DEPT PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'ISYSTEMNUMBER') THEN 
			ALTER TABLE IEAI_PROJECT ADD ISYSTEMNUMBER VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_IPMP_SYSTEM_RELATON' AND COLUMN_NAME = 'ISYSCODE') THEN 
			ALTER TABLE IEAI_IPMP_SYSTEM_RELATON ADD ISYSCODE VARCHAR(255); 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();

-- 8.11.0
DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		DECLARE  target_database VARCHAR(100);
 	  SELECT DATABASE() INTO target_database;
 	  
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'MAINDEPARTMENT') THEN
             ALTER TABLE IEAI_INSTANCEINFO ADD MAINDEPARTMENT VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'BUSINESSPERSONNEL') THEN
             ALTER TABLE IEAI_INSTANCEINFO ADD BUSINESSPERSONNEL VARCHAR(500);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'ARTISAN') THEN
             ALTER TABLE IEAI_INSTANCEINFO ADD ARTISAN VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'KEYSTEPS') THEN
             ALTER TABLE IEAI_INSTANCEINFO ADD KEYSTEPS VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'PLANTIME') THEN
             ALTER TABLE IEAI_INSTANCEINFO ADD PLANTIME VARCHAR(100);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'MAINDEPARTMENT') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE ADD MAINDEPARTMENT  VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'BUSINESSPERSONNEL') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE ADD BUSINESSPERSONNEL  VARCHAR(500);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'ARTISAN') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ARTISAN  VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'KEYSTEPS') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE ADD KEYSTEPS  VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'PLANTIME') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE ADD PLANTIME  VARCHAR(100);
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME = 'MAINDEPARTMENT') THEN
             ALTER TABLE IEAI_INSTANCEINFO_HIS ADD MAINDEPARTMENT VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME = 'BUSINESSPERSONNEL') THEN
             ALTER TABLE IEAI_INSTANCEINFO_HIS ADD BUSINESSPERSONNEL VARCHAR(500);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME = 'ARTISAN') THEN
             ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ARTISAN VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME = 'KEYSTEPS') THEN
             ALTER TABLE IEAI_INSTANCEINFO_HIS ADD KEYSTEPS VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME = 'PLANTIME') THEN
             ALTER TABLE IEAI_INSTANCEINFO_HIS ADD PLANTIME VARCHAR(100);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'MAINDEPARTMENT') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD MAINDEPARTMENT  VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'BUSINESSPERSONNEL') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD BUSINESSPERSONNEL  VARCHAR(500);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'ARTISAN') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ARTISAN  VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'KEYSTEPS') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD KEYSTEPS  VARCHAR(100);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'PLANTIME') THEN
           ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD PLANTIME  VARCHAR(100);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IACTDES') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE MODIFY COLUMN IACTDES VARCHAR(1000);
		END IF;		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME='IACTDES') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA MODIFY COLUMN IACTDES VARCHAR(1000);
		END IF;		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IACTDES') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS MODIFY COLUMN IACTDES VARCHAR(1000);
		END IF;		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IACTDES') THEN
			ALTER TABLE IEAI_INSTANCEINFO MODIFY COLUMN IACTDES VARCHAR(1000);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='IACTDES') THEN
			ALTER TABLE IEAI_INSTANCEINFO_HIS MODIFY COLUMN IACTDES VARCHAR(1000);
		END IF;		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME='TMP_RUN_INSTANCE' AND COLUMN_NAME='IACTDES') THEN
			ALTER TABLE TMP_RUN_INSTANCE MODIFY COLUMN IACTDES VARCHAR(1000);
		END IF;		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_PROXY_LIST' AND COLUMN_NAME = 'PROXYIP') THEN
			ALTER TABLE IEAI_PROXY_LIST modify PROXYIP VARCHAR(200);
		END IF;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_schema = target_database AND  TABLE_NAME = 'IEAI_PROXY_LIST' AND COLUMN_NAME = 'PROXYIPOUT') THEN
			ALTER TABLE IEAI_PROXY_LIST modify PROXYIPOUT VARCHAR(200);
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();



DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'IBGTYPE') THEN 
			ALTER TABLE IEAI_INSTANCE_VERSION ADD IBGTYPE VARCHAR(255) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'ICQTASKID') THEN 
			ALTER TABLE IEAI_INSTANCE_VERSION ADD ICQTASKID VARCHAR(255) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE' AND COLUMN_NAME = 'IBGTYPE') THEN 
			ALTER TABLE IEAI_RUN_INSTANCE ADD IBGTYPE VARCHAR(255) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE' AND COLUMN_NAME = 'ICQTASKID') THEN 
			ALTER TABLE IEAI_RUN_INSTANCE ADD ICQTASKID VARCHAR(255) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME = 'IBGTYPE') THEN 
			ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IBGTYPE VARCHAR(255) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME = 'ICQTASKID') THEN 
			ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD ICQTASKID VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();


-- 8.14.0 version TOPO patch is as follows
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_SSO_APP_CONFIG (IID NUMERIC (19) ,IAPPNAME VARCHAR (50) ,IISOTHER NUMERIC (19) ,ILOGINURL VARCHAR (255) ,IAUTHURL VARCHAR (255) ,ISTATE NUMERIC (19) ,IAPPDISC VARCHAR (255) ,IUPDATEUSER NUMERIC (19) ,IUPDATETIME TIMESTAMP  ,IIMAGE VARCHAR (255) ,IENGLISHSIGN VARCHAR (50) ,IAPPTYPE NUMERIC (19) , CONSTRAINT PK_IEAI_SSO_APP_CONFIG PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
    
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WARN_USERS' AND COLUMN_NAME = 'ISENDTYPE') THEN
        ALTER TABLE IEAI_WARN_USERS ADD COLUMN ISENDTYPE VARCHAR(25);
    END IF;
    
	END;;
	DELIMITER ;
	CALL UPPRDE();

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'ISTATUS') THEN
			ALTER TABLE IEAI_USER ADD ISTATUS VARCHAR(255);
		END IF;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 邮储短信转发
	CREATE TABLE IF NOT EXISTS IEAI_SMS_SYS_CONFIG(IID NUMERIC(19,0) not null,ISYSCODE VARCHAR(128),ISYSAPP VARCHAR(56),ISYSCREATETIME NUMERIC(19,0),ISYSUPDATETIME NUMERIC(19,0),ICALLMETHOD VARCHAR(256),ISECRETKEY VARCHAR(128),ICODE VARCHAR(28),ICREATEUSER VARCHAR(56),IUPDATEUSER VARCHAR(56),CONSTRAINT PK_IEAI_SMS_SYS_CONFIG PRIMARY KEY (IID));
  	CREATE TABLE IF NOT EXISTS IEAI_SMS_APPROVE(IID NUMERIC(19,0) NOT NULL,ISYSCODE VARCHAR(128),ISYSAPP VARCHAR(56),ITASKCODE VARCHAR(128),ISMSHEADER VARCHAR(128),ISMSCONTENT   VARCHAR(2000),IAPPROVEPHONE NUMERIC(19,0),ICREATEAPPROVEPHONE NUMERIC(19,0),ISMSUSERTYPE INTEGER,ILASTSMSUSERTYPE INTEGER,IAPPROVEFORWARDSTATE INTEGER,IAPPROVEFORWARDTIME  NUMERIC(19,0),ICREATEAPPROVEFORWARDSTATE INTEGER,ICREATEAPPROVEFORWARDTIME  NUMERIC(19,0),ICREATESMSCONTENT   VARCHAR(2000),IDYNAMICCODE  VARCHAR(10),IRECEIPTSTATE   INTEGER,IRECEIPTTIME  NUMERIC(19,0),ICALLBACKCONTENT VARCHAR(2000),ICALLBACKSTATE   INTEGER,ICALLBACKTIME  NUMERIC(19,0),IAPPROVALTESULT  VARCHAR(2000),IAPPROVALCONTENT VARCHAR(2000),INOTICEFORWARDSTATE INTEGER,INOTICEFORWARDTIME  NUMERIC(19,0),ICREATETIME NUMERIC(19,0),ICODE VARCHAR(28),ICREATEUSER VARCHAR(56),CONSTRAINT PK_IEAI_IEAI_SMS_APPROVE PRIMARY KEY (IID));
	-- 采集任务表
	CREATE TABLE IF NOT EXISTS IEAI_DISTRIBUTED_JOB(IID NUMERIC(19,0),JOBNAME VARCHAR(30),  JOBCLASS VARCHAR(200),  JOBMETHOD VARCHAR(50),JOBSTATE NUMERIC(19,0),  JOBDATA VARCHAR(2000) ,   RUNSERVER VARCHAR(50)  ,CONSTRAINT PK_IEAI_DISTRIBUTED_JOB PRIMARY KEY (IID));
	
-- 8.17.0
	
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'IQYWECHAT') THEN 
			ALTER TABLE IEAI_USER ADD IQYWECHAT VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();

-- 8.18.0
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_AUDIT (IID NUMERIC (19) NOT NULL,IDATE NUMERIC (19) ,IUSERNAME VARCHAR (255) ,IHANDLE VARCHAR (255) ,IHANDLETYPE VARCHAR (255) ,INAME VARCHAR (255) ,IVERSION VARCHAR (255) ,IPROJECTID NUMERIC (19) ,ISTORENAME VARCHAR (255) , CONSTRAINT PK_IEAI_ARTIFACTS_AUDIT PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_ENV (IID NUMERIC (19) NOT NULL,INAME VARCHAR (255) ,IIP VARCHAR (255) ,IPORT VARCHAR (255) , CONSTRAINT PK_IEAI_ARTIFACTS_ENV PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_GENERIC (IID NUMERIC (19) NOT NULL,ISTOREID NUMERIC (19) ,INAME VARCHAR (255) ,ILASTMVCCID VARCHAR (255) , CONSTRAINT PK_IEAI_ARTIFACTS_GENERIC PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_GENERIC_MVCC (IID NUMERIC (19) NOT NULL,IGENERICID NUMERIC (19) ,ISIZE NUMERIC (19) ,IVERSION VARCHAR (255) ,IHASH VARCHAR (255) ,IPUSHUSER VARCHAR (255) ,IPUSHTIME VARCHAR (255) , CONSTRAINT PK_IEAI_ARTIFACTS_GENERIC_MVCC PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_PROJECT (IID NUMERIC (19) NOT NULL,INAME VARCHAR (255) ,ICODE VARCHAR (255) ,IDESC VARCHAR (255) ,ICREATEUSER VARCHAR (255) ,ICREATETIME NUMERIC (19) ,IUSERID NUMERIC (19) ,ILASTUPDATETIME NUMERIC (19) , CONSTRAINT PK_IEAI_ARTIFACTS_PROJECT PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_STORE (IID NUMERIC (19) NOT NULL,IPROJECTID NUMERIC (19) ,IADDRESS VARCHAR (255) ,IDESC VARCHAR (255) ,IVISABLE NUMERIC (19) ,ICREATEUSER VARCHAR (255) ,ICREATETIME NUMERIC (19) ,INAME VARCHAR (255) , CONSTRAINT PK_IEAI_ARTIFACTS_STORE PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_USER (IID NUMERIC (19) NOT NULL,IPROJECT NUMERIC (19) ,IUSERID NUMERIC (19) ,IUSERNAME VARCHAR (255) , CONSTRAINT PK_IEAI_ARTIFACTS_USER PRIMARY KEY (IID));

     CREATE TABLE IF NOT EXISTS IEAI_ARTIFACTS_USERGROUP (IID NUMERIC (19) NOT NULL,IPROJECTID NUMERIC (19) ,IGROUPID NUMERIC (19) ,ITYPE NUMERIC (19) ,ISTORENAME VARCHAR (255) ,IPERMISSIONTYPE VARCHAR (255) ,ISTOREID NUMERIC (19) , CONSTRAINT PK_IEAI_ARTIFACTS_USERGROUP PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();


-- 8.19.0
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYS_DICTIONARY' AND COLUMN_NAME = 'DCHECKVALUE') THEN 
			ALTER TABLE IEAI_SYS_DICTIONARY ADD DCHECKVALUE NUMERIC(19) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WARN_DETAIL' AND COLUMN_NAME = 'ITYPE') THEN
        ALTER TABLE IEAI_WARN_DETAIL MODIFY ITYPE VARCHAR(255);
    END IF;

END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'ICANSYNC') THEN 
			ALTER TABLE IEAI_PROJECT ADD ICANSYNC NUMERIC(19) DEFAULT 1; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();

-- 增加主键
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

		IF NOT EXISTS (SELECT * FROM information_schema. COLUMNS WHERE COLUMN_KEY = 'PRI' AND table_name = 'IEAI_DISTRIBUTED_JOB') THEN
            ALTER TABLE IEAI_DISTRIBUTED_JOB ADD PRIMARY KEY(IID);
        END IF;
		IF NOT EXISTS (SELECT * FROM information_schema. COLUMNS WHERE COLUMN_KEY = 'PRI' AND table_name = 'IEAI_WARNING_CODE') THEN
            ALTER TABLE IEAI_WARNING_CODE ADD PRIMARY KEY(IID);
        END IF;

END;;
DELIMITER ;
CALL UPPRDE();

-- 8.20.0
DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

	IF NOT EXISTS (SELECT * FROM IEAI_OS WHERE OSID = 9) THEN
        INSERT INTO IEAI_OS (OSID,OSNAME) VALUES (9,'UBUNTU');
    commit;
    END IF;

    IF NOT EXISTS (SELECT * FROM IEAI_OS WHERE OSID = 10) THEN
        INSERT INTO IEAI_OS (OSID,OSNAME) VALUES (10,'OTHER');
    commit;
    END IF;

	END;;
DELIMITER ;
CALL UPPRDE();

-- 首页通知
DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_HOMENOTICE_MANAGE (IID NUMERIC(19)NOT NULL,NOTICETEXT VARCHAR(255),  SPEED NUMERIC(19) DEFAULT 0,CONSTRAINT PK_IEAI_HOMENOTICE_MANAGE PRIMARY KEY (IID));
		END;;
DELIMITER ;
CALL UPPRDE();

-- 8.20.1 南京银行增加
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
create PROCEDURE UPPRDE()
BEGIN
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ORGMANAGEMENT' AND COLUMN_NAME = 'ILEVEL') THEN
	ALTER TABLE IEAI_ORGMANAGEMENT ADD ILEVEL VARCHAR(255);
END IF;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'IDEPARTMENTID') THEN
	ALTER TABLE IEAI_USER ADD IDEPARTMENTID NUMERIC(19);
END IF;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'ISTANDBY') THEN
	ALTER TABLE IEAI_USER ADD ISTANDBY VARCHAR(255);
END IF;

END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
create PROCEDURE UPPRDE()
BEGIN
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_GROUP' AND COLUMN_NAME = 'MATEIP') THEN
		ALTER TABLE IEAI_COMPUTER_GROUP ADD MATEIP VARCHAR(30);
	END IF;
END;;
DELIMITER ;
CALL UPPRDE();
-- 8.20.1 南京银行权限转移
DELIMITER ;;
       DROP PROCEDURE IF EXISTS UPPRDE;;
       CREATE PROCEDURE UPPRDE()
BEGIN
       CREATE TABLE IF NOT EXISTS  IEAI_USER_TRANSFER_PERMISSIONS (IID NUMERIC (19) NOT NULL, ILOGINID NUMERIC (19), ILOGINNAME VARCHAR(50), ITRANSFERUSER_ID  NUMERIC (19), ITRANSFERUSER VARCHAR(50),IAPPROVALUSER_ID NUMERIC (19),IAPPROVALUSER VARCHAR(50),ICREATTIME DATE,IUPDATETIME DATE,ITRANSFERDEADLINE DATE,IIS_AUDITING NUMERIC(2), IIS_TERM NUMERIC(2), CONSTRAINT IEAI_USER_TRANSFER_PERMISSIONS PRIMARY KEY(IID) );
       CREATE TABLE IF NOT EXISTS  IEAI_USER_PERMISSIONS(IID NUMERIC (19) NOT NULL, ITRANSFERID NUMERIC (19) NOT NULL, IROLEID NUMERIC (19) NOT NULL,CONSTRAINT PK_IEAI_USER_PERMISSIONS PRIMARY KEY(IID));
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
create PROCEDURE UPPRDE()
BEGIN
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_COLLECT_RESULT_LATEST' AND INDEX_NAME = 'IDX_RESLT_LATEST_EQUIPID') THEN
			CREATE INDEX IDX_RESLT_LATEST_EQUIPID ON IEAI_COLLECT_RESULT_LATEST(EQUIPID);
	END IF;

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_AGENT_PROJECT_CONFIG' AND INDEX_NAME = 'IDX_PROJECT_CONFIG_AGENT') THEN
			CREATE INDEX IDX_PROJECT_CONFIG_AGENT ON IEAI_AGENT_PROJECT_CONFIG(IAGENTINFOID);
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND INDEX_NAME = 'IDX_IEAI_AGENTINFO_002') THEN
			CREATE INDEX IDX_IEAI_AGENTINFO_002 ON IEAI_AGENTINFO(IAGENT_IP);
	END IF;
END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_AGENT_CONF_NJ (IID NUMERIC (19)  NOT NULL,AGENTID NUMERIC (19) ,DBUSER VARCHAR (50) ,DBPWD VARCHAR (64) ,DBURL VARCHAR (150) ,DBTYPE VARCHAR (20) ,DBDRIVER VARCHAR (100) ,CREATEUSER VARCHAR (50) ,CREATETIME NUMERIC (19) ,UPDATEUSER VARCHAR (50) ,UPDATETIME NUMERIC (19) , CONSTRAINT PK_IEAI_AGENT_CONF_NJ PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'DEVELOPER') THEN
			ALTER TABLE IEAI_PROJECT ADD DEVELOPER VARCHAR(255) ;
		END IF;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'DEPARTMENT') THEN
			ALTER TABLE IEAI_PROJECT ADD DEPARTMENT VARCHAR(100) ;
		END IF;
	  END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'CPOSVERSION') THEN
			ALTER TABLE IEAI_COMPUTER_LIST ADD CPOSVERSION VARCHAR(100) ;
		END IF;
	  END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
create PROCEDURE UPPRDE()
BEGIN

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ORGMANAGEMENT' AND COLUMN_NAME = 'IEXTERNALID') THEN
	ALTER TABLE IEAI_ORGMANAGEMENT ADD IEXTERNALID NUMERIC(19);
END IF;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ORGMANAGEMENT' AND COLUMN_NAME = 'IEXTERNALPID') THEN
	ALTER TABLE IEAI_ORGMANAGEMENT ADD IEXTERNALPID NUMERIC(19);
END IF;

END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
	    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'ITYPE2') THEN
			 ALTER TABLE IEAI_PORTAL ADD ITYPE2 NUMERIC(2);
		END IF;
		COMMIT;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'ITOOL_RESULT') THEN
			UPDATE IEAI_PORTAL SET ITYPE2 = ITYPE ;
		END IF;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'ITYPE') THEN
			ALTER TABLE IEAI_PORTAL DROP COLUMN ITYPE;
		END IF;
		COMMIT;
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PORTAL' AND COLUMN_NAME = 'ITYPE2') THEN
			 ALTER TABLE IEAI_PORTAL CHANGE ITYPE2 ITYPE NUMERIC(2);
		END IF;
		COMMIT;
	  END;;
DELIMITER ;
CALL UPPRDE();