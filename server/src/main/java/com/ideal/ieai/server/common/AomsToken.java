package com.ideal.ieai.server.common;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.hd.flowstart.MD5;

/**
 * 
 * <ul>
 * <li>Title: AomsToken.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2021年3月24日
 */
public class AomsToken extends Thread
{
    private static Map<String, AomsTokenBean> tokenMap           = null;
    private static Map<String, String>        tokenMapFlag       = null;
    private static AomsToken                  tokenPollingThread = null;
    private static final Logger               log                = Logger.getLogger(AomsToken.class);

    public static void init ()
    {
        if (null == tokenMap && null == tokenPollingThread)
        {
            tokenMap = new LinkedHashMap<String, AomsTokenBean>();
            tokenMapFlag = new LinkedHashMap<String, String>();
            tokenPollingThread = new AomsToken();
            tokenPollingThread.start();
        }
    }

    public static int size ()
    {
        Object obj = new Object();
        int size = -1;
        synchronized (obj)
        {
            size = tokenMap.size();
        }
        return size;
    }

    public static boolean get ( String key, int type )
    {
        Object obj = new Object();
        boolean flag = false;
        synchronized (obj)
        {
            if (tokenMap.containsKey(key))
            {
                AomsTokenBean tokenBean = tokenMap.get(key);
                if (tokenMapFlag.containsKey(tokenBean.getUserName() + "_" + type))
                {
                    long token = tokenBean.getTime();
                    if (AomsToken.tokenIsValid(token, type))
                    {
                        long now = System.currentTimeMillis();
                        AomsToken.put(tokenBean.getUserName(), now, type);
                        flag = true;
                    }
                }
            }
        }
        return flag;
    }

    public static String getUserName ( String token )
    {
        Object obj = new Object();
        String userName = null;
        synchronized (obj)
        {
            AomsTokenBean bean = tokenMap.get(token);
            userName = bean.getUserName();
        }
        return userName;
    }

    public static String put ( String user, long token, int type )
    {
        String tokens = "";
        Object obj = new Object();
        synchronized (obj)
        {
            boolean flag = true;
            String key = user + "_" + type;
            if (tokenMapFlag.containsKey(key))
            {
                String tok = tokenMapFlag.get(key);
                AomsTokenBean bean = tokenMap.get(tok);
                if(null!=bean) {
                    if (AomsToken.tokenIsValid(bean.getTime(), type))
                    {
                        bean.setTime(token);
                        tokens = bean.getToken();
                        tokenMap.put(tokens, bean);
                        flag = false;
                    } else
                    {
                        tokens = bean.getToken();
                        tokenMap.remove(tokens);
                    }
                }
            }

            if (flag)
            {
                AomsTokenBean bean = new AomsTokenBean();
                bean.setTime(token);
                bean.setUserName(user);
                tokens = MD5.md5Str(user + "_" + token);
                bean.setToken(tokens);
                bean.setType(type);
                tokenMap.put(tokens, bean);
                tokenMapFlag.put(key, tokens);
            }
        }
        return tokens;
    }

    public static void put ( String user, long userId, long token, int type )
    {
        String tokens = "";
        Object obj = new Object();
        synchronized (obj)
        {
            boolean flag = true;
            String key = user + "_" + type;
            if (tokenMapFlag.containsKey(key))
            {
                String tok = tokenMapFlag.get(key);
                AomsTokenBean bean = tokenMap.get(tok);
                if (AomsToken.tokenIsValid(bean.getTime(), type))
                {
                    bean.setTime(token);
                    tokens = bean.getToken();
                    tokenMap.put(tokens, bean);
                    flag = false;
                } else
                {
                    tokens = bean.getToken();
                    tokenMap.remove(tokens);
                }
            }

            if (flag)
            {
                AomsTokenBean bean = new AomsTokenBean();
                bean.setTime(token);
                bean.setUserName(user);
                bean.setUserId(userId);
                tokens = MD5.md5Str(user + "_" + token);
                bean.setToken(tokens);
                bean.setType(type);
                tokenMap.put(tokens, bean);
                tokenMapFlag.put(key, tokens);
            }
        }
    }

    public static void remove ( String token )
    {
        Object obj = new Object();
        synchronized (obj)
        {
            tokenMap.remove(token);
            AomsTokenBean bean = tokenMap.get(token);
            if (null != bean)
            {
                String key = bean.getUserName();
                tokenMapFlag.remove(key);
            }
        }
    }

    private static Set<String> getKeySet ()
    {
        Object obj = new Object();
        Set<String> keySet = null;
        synchronized (obj)
        {
            keySet = tokenMap.keySet();
        }
        return keySet;
    };

    /**
     * 
     * <li>Description:判断数据库中的tokenId是否有效</li> 
     * @param tokenId 数据库的tokenId
     * @return
     * return boolean true为有效，false为无效
     */
    private static boolean tokenIsValid ( long tokenTime, int type )
    {
        // token有效期，当数据库中没有数据时，赋予默认值30
        long tokenDuration = ServerEnv.getServerEnv().getAomsTokenOverTime();
        long durationMs = tokenDuration * 60 * 1000;
        long now = System.currentTimeMillis();
        if ((now - tokenTime) > durationMs)
        {
            return false;
        }
        return true;
    }

    public void run ()
    {
        while (true)
        {
            checkValid();
        }
    }

    private static void checkValid ()
    {
        try
        {
            long tikenValidTime = ServerEnv.getServerEnv().getAomsTokenOverTime() * 60 * 1000;
            List<String> deleteKeyList = new ArrayList<String>();
            for (Map.Entry<String, AomsTokenBean> entry : tokenMap.entrySet())
            {
                String key = entry.getKey();
                AomsTokenBean bean = entry.getValue();
                if (StringUtils.isNotBlank(key) && (System.currentTimeMillis() > bean.getTime() + tikenValidTime))
                {
                    deleteKeyList.add(key);
                }
            }
            if (!deleteKeyList.isEmpty())
            {
                for (String key : deleteKeyList)
                {
                    remove(key);
                }
            }

            // 每五分钟查看一下token是否示失效
        } catch (Exception e)
        {
            log.error("token overtime thread error :" + e.getMessage(), e);
        } finally
        {
            try
            {
                Thread.sleep((long) 5 * 60 * 1000);
            } catch (Exception e)
            {
                log.error(e);
            }
        }
    }

}
