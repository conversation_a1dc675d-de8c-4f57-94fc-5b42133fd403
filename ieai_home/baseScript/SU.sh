#!/bin/sh
##########################
# it must use full path
appName=$0
#runPath=`dirname ${appName}`
runPath="`pwd`/baseScript"

userName=$1
cmdFile=$@
cmdFile=`echo ${cmdFile#* }`

export SuEntegorPath="${IEAI_HOME}"
if [ -z "${SuEntegorPath}" ];then
	export SuEntegorPath=`pwd`
fi

chmod 755 $runPath/RUN.sh
if [ ! ${userName} = root ];then
	su - $userName -c "trap 'echo' 0;export IEAI_HOME=${SuEntegorPath};$runPath/RUN.sh $cmdFile"
	exit $?
else
	$runPath/RUN.sh $cmdFile
	exit $?
fi
