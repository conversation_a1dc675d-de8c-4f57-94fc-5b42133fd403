-- 4.7.17
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	 
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=16;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (16, ''应用维护'', ''应用维护模块组'', 16, ''images/info82.png'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-16;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, <PERSON><PERSON>J<PERSON><PERSON>, <PERSON><PERSON>NVE<PERSON>, IFREEZED, <PERSON>UPL<PERSON><PERSON><PERSON><PERSON>, <PERSON>COMMENT, <PERSON>UPLOADNUM, IUUID, <PERSON><PERSON><PERSON><PERSON>EUSER, IF<PERSON><PERSON><PERSON>EUSERID, I<PERSON>LOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-16, 0, ''所有应用维护业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 16, -16, -16, -16)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=16;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (16,16,''应用维护源'','''','''','''','''',0)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_APM_CODE';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_APM_CODE(IID   DECIMAL(19,0) NOT NULL, IAPMTYPE INTEGER, IAPMNAME VARCHAR(255), CONSTRAINT PK_IEAI_APM_CODE  PRIMARY KEY(IID))';
	        PREPARE	SQLA FROM LS_SQL;
	        EXECUTE SQLA;
	END IF;
	
	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	 
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_APM_CODE WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_APM_CODE(IID,IAPMTYPE,IAPMNAME) VALUES(1,0,''应用启停'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_APM_CODE WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_APM_CODE(IID,IAPMTYPE,IAPMNAME) VALUES(2,1,''数据获取'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION' AND COLNAME = 'IAPMTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN IAPMTYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE' AND COLNAME = 'IAPMTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE ADD COLUMN IAPMTYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE_HIS' AND COLNAME = 'IAPMTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD COLUMN IAPMTYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE' AND COLNAME = 'ISYSTYPE';
	IF	LI_EXISTS != 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE ALTER COLUMN  ISYSTYPE SET DATA TYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE_HIS' AND COLNAME = 'ISYSTYPE';
	IF	LI_EXISTS != 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ALTER COLUMN  ISYSTYPE SET DATA TYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
END
$

-- 4.7.18	 
   BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SUS_ITSM_TASKID_REL' AND INDNAME='IDX_IEAISUSITSMTASKIDREL_01';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE INDEX IDX_IEAISUSITSMTASKIDREL_01 ON IEAI_SUS_ITSM_TASKID_REL (IITSM_TASKID)';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
END
	 $
	
-- 4.7.23
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION' AND COLNAME = 'ISSELFHEALING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ISSELFHEALING INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION_HIS' AND COLNAME = 'ISSELFHEALING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD ISSELFHEALING INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'ISELFHEALING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ISELFHEALING INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'ISELFHEALING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ISELFHEALING INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLNAME = 'ISELFHEALING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD ISELFHEALING INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLNAME = 'ISELFHEALING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD ISELFHEALING INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
END
$

-- 4.7.24
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DOUBLECHECK_WORKITEM' AND COLNAME = 'CPFLAG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD CPFLAG INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLNAME = 'CPFLAG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD CPFLAG INTEGER DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$

-- 4.7.25
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ITSM_AUTO_CHILD' AND COLNAME = 'IERROR';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IERROR VARCHAR(800)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ITSM_AUTO' AND COLNAME = 'ORDERNUM';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_ITSM_AUTO ADD ORDERNUM VARCHAR(2000)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$


 

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_APP_STARTUP_INFO';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_APP_STARTUP_INFO (IID NUMERIC (19) NOT NULL,IAPMTYPE NUMERIC (19),IINSTANCENAME VARCHAR (255),IVERSIONALIAS VARCHAR (255),IRUNINSNAME VARCHAR (255), CONSTRAINT PK_IEAI_APP_STARTUP_INFO PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
