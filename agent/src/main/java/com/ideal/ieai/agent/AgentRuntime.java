package com.ideal.ieai.agent;

import java.rmi.RemoteException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.task.InvalidateTaskDefinitionException;
import com.ideal.ieai.commons.task.ServerTaskException;
import com.ideal.ieai.commons.task.TaskAttachmentInCore;
import com.ideal.ieai.commons.task.TaskDef;
import com.ideal.ieai.commons.task.TaskInfoInCore;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.IEAIRuntime;
import com.ideal.ieai.core.activity.ISharedResource;
import com.ideal.ieai.core.data.IDataType;
import com.ideal.ieai.core.element.IEAISchema;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Resource;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.core.expreval.ExprEvalError;
import com.ideal.ieai.core.io.UnMarshallingException;

public class AgentRuntime extends IEAIRuntime
{
    public AgentRuntime()
    {
    }

    public Project getProject ()
    {
        throw new UnsupportedOperationException();
    }

    public int getLocation ()
    {
        return IEAIRuntime.AGENT;
    }

    public String getProjectName ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        return req.projectName;
    }

    public String getWorkflowName ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        return req.flowName;
    }

    /**
     * Get current common activity
     */

    public BasicActElement getCurCommAct ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();

        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getCurCommAct(req.id);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling ActivityElement, request id"
                    + req.id);
        }
        return null;
    }

    public String getCurPrjCalendarName ( String flowName )
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();

        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        while (endtime >= System.currentTimeMillis())
        {
            try
            {
                return AgentServiceManager.getInstance().getAgentService(req)
                        .getCurPrjCalendarName(req.id, flowName);
            } catch (RemoteException remoteEx)
            {
                // sleep and retry later
                try
                {
                    Thread.sleep(retryTime);
                } catch (Exception e)
                {

                }
                // give it another chance
                continue;
            }
        }

        return null;
    }

    /**
     * @todo perform network recovery
     */

    public IEAISchema getSchemaByFullName ( final String SchemaFullName )
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();

        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getSchemaByFullName(req.id, SchemaFullName);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling schema data:" + SchemaFullName, e);
        }
        return null;
    }

    public Collection getCurPrjSchemas ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();

        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getCurPrjSchemas(req.id);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling schema data", e);
        }
        return null;
    }

    public Collection getAllSchema ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();

        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getAllSchema(req.id);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling schema data", e);
        }
        return null;

    }

    /**
     * Shared resource hold some commonly used configuration information, such
     * as database connection info. It can also be used to share ifnormation
     * among activities. For example, transaction information can be shared by
     * multiple database activities. For remote execution, resouece cannot be
     * initialized on iEAI server and be serialized to agent, because the
     * configuration cannot be valid once transfered on network. So the idea
     * here is when a resource is requested, a resource instance will be created
     * and resource config information will be retrieved and used to initialize
     * the new instance. In order to reduce redundant instance, a
     * SharedResourceManager class will be created to manage all resources
     * intialized and used on current agent. When workflow is stopped, the
     * related resource will be disposed.
     */
    /**
     * @todo perform network recovery
     */

    public ISharedResource getSharedResInsByName ( final String Name )
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        return SharedResourceManager.getInstance().getSharedResourceByName(req,
                Name);
    }

    public Map getAllSharedResIns ()
    {
        throw new UnsupportedOperationException();
    }

    public Map getSharedResInsByClass ( Class resClass )
    {
        throw new UnsupportedOperationException();
    }

    public Resource getResourceByName ( String Name )
    {
        throw new UnsupportedOperationException();
    }

    public Collection getAllResource ()
    {
        throw new UnsupportedOperationException();
    }

    public IDataType getEnvVarTypeByName ( String VarName )
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getEnvVarTypeByName(req.id, VarName);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling schema data", e);
        }
        return null;

    }

    public Map getAllEnvValue ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getAllEnvValue(req.id);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling all env var value", e);
        }
        return null;
    }

    /**
     * @todo perform network recovery
     */

    public Map getAllEnvType ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();

        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getAllEnvType(req.id);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling all env var types", e);
        }
        return null;
    }

    /**
     * @todo perform network recovery
     */

    public List getAllCommonActInfo ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
        try
        {
            while (endtime >= System.currentTimeMillis())
            {
                try
                {
                    return AgentServiceManager.getInstance().getAgentService(
                            req).getAllCommonActInfo(req.id);
                } catch (RemoteException remoteEx)
                {
                    // sleep and retry later
                    try
                    {
                        Thread.sleep(retryTime);
                    } catch (Exception e)
                    {

                    }
                    // give it another chance
                    continue;
                }
            }
        } catch (UnMarshallingException e)
        {
            _log.error("Error unmarshalling all common act info", e);
        }
        return null;
    }

    /**
     * @todo perform network recovery
     */

    public long createTask ( TaskDef taskDef )
            throws InvalidateTaskDefinitionException, ServerTaskException
    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public TaskInfoInCore getTaskInfo ( long taskId )
            throws ServerTaskException
    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public boolean isTaskEnd ( long taskId ) throws ServerTaskException
    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public void setTaskOwner ( long taskId, String userId )
            throws ServerTaskException

    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public Long addTaskAttachment ( String attachmentName, byte[] Content )
            throws ServerTaskException

    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public void deleteAttachment ( String attachmentId )
            throws ServerTaskException

    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public void setTaskAttachment ( String attachmentId, String name,
            byte[] content ) throws ServerTaskException

    {
        throw new UnsupportedOperationException();
    }

    /**
     * @todo perform network recovery
     */

    public TaskAttachmentInCore getTaskAttachment ( String attachmentId )
            throws ServerTaskException
    {
        throw new UnsupportedOperationException();
    }

    /**
     * @param attachName
     *            String null return all attachments
     * @return List
     */
    /**
     * @todo perform network recovery
     */
    public List queryAttachments ( String attachName )
            throws ServerTaskException
    {
        throw new UnsupportedOperationException();
    }

    public String getFlowInsName ()
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider
                .getActivityExecRequest();
        long retryTime = AgentEnv.getInstance().getLongConfig(
                Environment.NETWORK_RECOVERY_RETRY_TIME,
                Environment.NETWORK_RECOVERY_RETRY_TIME_DEFAULT) * 1000;
        long endtime = System.currentTimeMillis()
                + AgentEnv.getInstance().getLongConfig(
                        Environment.NETWORK_RECOVERY_WAIT_TIME,
                        Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;

        while (endtime >= System.currentTimeMillis())
        {
            try
            {
                return AgentServiceManager.getInstance().getAgentService(req)
                        .getFlowInsName(req.id);
            } catch (RemoteException remoteEx)
            {
                // sleep and retry later
                try
                {
                    Thread.sleep(retryTime);
                } catch (Exception e)
                {

                }
                // give it another chance
                continue;
            }
        }
        return null;
    }

    public Object evaluate ( String StrExpr ) throws ExprEvalError
    {
        ActivityExecRequest req = ActExecutorRuntimeProvider.getActivityExecRequest();
        try
        {
            return AgentServiceManager.getInstance().getAgentService(req).evaluate(req.id ,StrExpr);
        } catch (RemoteException e)
        {
            _log.info(e);
        } catch (UnMarshallingException e)
        {
            _log.info(e);
        }
        return null;
    }
    
    private static final Logger _log = Logger.getLogger(AgentRuntime.class);

	public String getProjectId() {
		// TODO Auto-generated method stub
		return null;
	}

    @Override
    public ISharedResource getSharedResInsByNameType ( String Name, int type )
    {
        return null;
    }
}
