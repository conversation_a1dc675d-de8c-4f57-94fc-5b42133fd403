package com.ideal.sscontroller.dubbo;

import com.alibaba.fastjson.JSON;
import com.ideal.dubbo.interfaces.IAgentService;
import com.ideal.dubbo.models.AgentModel;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

@Controller
public class ScriptServiceTryAgentConfigController
{

    @DubboReference(timeout = 60000000, retries = 0)
    IAgentService               agentService;

    @RequestMapping("configTryATryAgents.do")
    public String configTryATryAgents ()
    {
        return "dubbo/tryAgentConfig/tryAgentConfig";
    }

    @RequestMapping("getTryAgentConfig.do")
    @ResponseBody
    public Object getTryAgentConfig ( AgentModel filter, int start, int limit ) throws Exception
    {
        return agentService.getTryAgentConfig(filter, start, limit);
    }

    @RequestMapping("getTryAgentList.do")
    @ResponseBody
    public Object getTryAgentList () throws Exception
    {
        return agentService.getTryAgentList();
    }

    @RequestMapping("getTryAgentById.do")
    @ResponseBody
    public Object getTryAgentById (String iid) throws Exception
    {
        return agentService.getAgentInfo(iid);
    }

    @RequestMapping("delTryAgentInfo.do")
    @ResponseBody
    public Object delTryAgentInfo ( Long[] ids ) throws Exception
    {
        Map map = new HashMap();
        Boolean res = agentService.delTryAgentInfo(ids);
        map.put("success", res);
        return map;
    }

    @RequestMapping("addTryAgentConfig.do")
    @ResponseBody
    public Object addTryAgentConfig ( Long[] ids )
    {
        Map map = new HashMap();
        if (ids.length > 0)
        {
            Boolean res = agentService.addTryAgentConfig(ids);
            map.put("success", res);
        } else
        {
            map.put("success", false);
            map.put("message", "没有选择记录！");
        }
        return map;
    }

    @RequestMapping("saveTryAgentConfig.do")
    @ResponseBody
    public Object saveTryAgentConfig ( String customNames )
    {
        Map map = new HashMap();

        try
        {
            Map customNameMap = JSON.parseObject(customNames, Map.class);
            map = agentService.saveTryAgentConfig(customNameMap);
        } catch (Exception e)
        {
            map.put("success", false);
            map.put("message", e.getMessage());
        }

        return map;
    }
}
