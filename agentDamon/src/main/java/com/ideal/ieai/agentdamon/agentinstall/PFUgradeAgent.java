package com.ideal.ieai.agentdamon.agentinstall;

import com.alibaba.fastjson.JSON;
import com.ideal.ieai.agentdamon.util.SendUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Hashtable;

public class PFUgradeAgent {
    private static final Logger _log = Logger.getLogger(PFUgradeAgent.class);
    private static PFUgradeAgent agent = null;

    public static PFUgradeAgent getInstance(){
        agent =new PFUgradeAgent();
        return agent;
    }

    public  void  exect(Hashtable hashtable){
        String result="";
        String postString= hashtable.get("paramJson").toString();
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject
                .parseObject(postString);
        String startcmd=jsonObject.get("startcmd").toString();
        String isever=jsonObject.get("isever").toString();
        String requestId=jsonObject.get("requestId").toString();
        String methodName=jsonObject.get("methodName").toString();
        String agentip=jsonObject.get("agentip").toString();
        String iagentport=jsonObject.get("iagentport").toString();
        hashtable.remove("paramJson");
        _log.info("agentip ="+agentip);
        _log.info("iagentport ="+iagentport);
        String proxyString = null!=hashtable&&hashtable.size()>0? JSON.toJSONString(hashtable):null;
        _log.info("start cmd ="+startcmd);
        // 执行脚本 获取脚本执行结果值
        int code=0;
        try{
            result=getMemory(startcmd);
            if("rollback".equals(methodName)){
                if (result.contains("rollback success")){
                    code=300 ;
                }else {
                    code=252 ;
                }
            }else if("upgrade".equals(methodName)){
                if (result.contains("upgrade success")){
                    code=200 ;
                }else {
                    code=152 ;
                }
            }else {
                code=200;
            }
        }catch (Exception e){
            if("rollback".equals(methodName)){
                code = 252;
            }
            if("upgrade".equals(methodName)){
                code = 152;
            }
            _log.error("PFUgradeAgent Exception -> "+e);
        }
        // 发送结果
        Hashtable table = new Hashtable();
        table.put("requeId",requestId);
        table.put("code",code);
        table.put("agentip",agentip);
        table.put("iagentport",iagentport);
        table.put("result",result);
        sendWarnMsgToServer(table,isever,proxyString);
    }


    /**
     * 读取返回信息，解析内存数据
     * @param cmd 执行脚本 返回 code 值 做参考
     * @return
     */
    private String getMemory (String cmd) throws IOException {
        InputStreamReader isr = null;
        BufferedReader br = null;
        String lastLine="";
        try
        {
            Process p = Runtime.getRuntime().exec(cmd);
            isr = new InputStreamReader(p.getInputStream());
            br = new BufferedReader(isr);
            String line="";
            while ((line = br.readLine()) != null)
            {
                lastLine+=line;
            }
            _log.info("lastLine=="+lastLine);
            if (lastLine.length()>512){
                lastLine=lastLine.substring(lastLine.length()-512);
            }
        } catch (IOException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
            lastLine=e.getMessage();
        }finally {
            isr.close();
            br.close();
        }
        return lastLine;
    }

    /**
     * 发送结果到 server端
     * @param server server返回ip地址
     * @param proxy 是否走proxy
     */
    private void sendWarnMsgToServer(Hashtable table,String server,String proxy) {
        _log.info("PF ->Agent->Ugrade->start");
        try
        {
            if(StringUtils.isNotBlank(proxy)) {
                SendUtils.sendToProxy("ieai-unify-agent-pfagentupgrade", proxy, table);
            }else {
                SendUtils.sendToServer("ieai-unify-agent-pfagentupgrade", server, table);
            }
        } catch (Exception e)
        {
            _log.error("PFUgradeAgent send server request is error", e);
        }
    }
}
