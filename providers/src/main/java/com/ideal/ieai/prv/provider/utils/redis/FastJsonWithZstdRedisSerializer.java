package com.ideal.ieai.prv.provider.utils.redis;

import cn.hutool.core.io.IoUtil;
import cn.hutool.extra.compress.CompressUtil;
import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import org.apache.commons.compress.compressors.CompressorInputStream;
import org.apache.commons.compress.compressors.CompressorOutputStream;
import org.apache.commons.compress.compressors.CompressorStreamFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.springframework.data.redis.serializer.SerializationException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Objects;

/**
 * 使用zstd压缩算法进行FastJson的序列化和反序列化
 */
public class FastJsonWithZstdRedisSerializer<T> extends FastJsonRedisSerializer<T> {
    public FastJsonWithZstdRedisSerializer(Class<T> type) {
        super(type);
    }

    @Override
    public byte[] serialize(T t) throws SerializationException {
        return compress(super.serialize(t));
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        return super.deserialize(decompress(bytes));
    }


    /**
     * 压缩
     * @param content 内容
     * @return byte[]
     */
    private byte[] compress(byte[] content) {
        byte[] ret;
        ByteArrayOutputStream byteArrayOutputStream;
        CompressorOutputStream  cos = null;

        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            cos = CompressUtil.getOut(CompressorStreamFactory.ZSTANDARD, byteArrayOutputStream);
            cos.write(content);

        } catch (IOException e) {
            throw new SerializationException("Unable to compress data", e);
        } finally {
            IoUtil.close(cos);
        }
        ret = byteArrayOutputStream.toByteArray();
        if(Objects.isNull(ret)){
            throw new SerializationException("Unable to compress data");
        }
        return ret;
    }


    /**
     * 解压缩
     * @param contentBytes 内容
     * @return byte[]
     */
    private byte[] decompress(byte[] contentBytes) {
        byte[] ret;
        CompressorInputStream in = null;
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            in = CompressUtil.getIn(CompressorStreamFactory.ZSTANDARD, new ByteArrayInputStream(contentBytes));
            IOUtils.copy(in, out);
            ret = out.toByteArray();
        } catch (IOException e) {
            throw new SerializationException("Unable to decompress data", e);
        }finally {
            IoUtil.close(in);
        }
        return ret;

    }
}
