package com.ideal.ieai.server.jobscheduling.common;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;


public class ProjectInfoManager
{
    private static final String       IACTDES  = "iactdes";
    private static final String       IACTNAME = "iactname";
    private static final String       ILASTID  = "ilastid";
    private static final String       IUPPERID = "iupperid";
    private static final Logger _log = Logger.getLogger(ProjectInfoManager.class);
    private static ProjectInfoManager intance = new ProjectInfoManager();

    public static ProjectInfoManager getInstance ()
    {
        if (intance == null)
        {
            intance = new ProjectInfoManager();
        }
        return intance;
    }

    public List getProjectInfo ( Long userId )
    {
        boolean test = ServerEnv.getInstance().getBooleanConfig("testshow", false);
        List list = null;
        Connection con = null;
        CallableStatement proc = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        try
        {
            if (test)
            {
                list = new ArrayList();
                ProjectInfoModel model = new ProjectInfoModel();
                model.setUuid("1");
                model.setPrjId(Long.parseLong("1"));
                model.setPrjName("testprj");
                model.setPrjUpperId(Long.parseLong("1"));
                list.add(model);
            } else
            {
                long startTimeGetConnection = System.currentTimeMillis();
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                long endTimeGetConnection = System.currentTimeMillis();
                _log.info("获取工程下拉列表：获取connection     耗时：" + (endTimeGetConnection - startTimeGetConnection) + " 豪秒");
                
                
                list = new ArrayList();
                
                long startTimeProc = System.currentTimeMillis();
                proc = con.prepareCall("call PROC_GET_PROJECT_LIST(?,?)");
                proc.setLong(1, userId.longValue());
                proc.registerOutParameter(2, java.sql.Types.NUMERIC);
                proc.execute();
                long optID = proc.getLong(2);
                long endTimeProc = System.currentTimeMillis();
                _log.info("获取工程下拉列表：执行存储过程PROC_GET_PROJECT_LIST     耗时：" + (endTimeProc - startTimeProc) + " 豪秒");
                
                long startTimeSql = System.currentTimeMillis();
                String sql = "SELECT PRJ_NAME, UUID ,PRJ_ID,PRJ_UPPERID FROM TMP_PROJECT_LIST WHERE OPT_ID = ? ORDER BY UPPER(PRJ_NAME)";
                pres = con.prepareStatement(sql);
                pres.setLong(1, optID);
                rs = pres.executeQuery();
                long endTimeSql = System.currentTimeMillis();
                _log.info("获取工程下拉列表：执行sql     耗时：" + (endTimeSql - startTimeSql) + " 豪秒");
                
                long startTimeAddData = System.currentTimeMillis();
                boolean systemanomalyreportSwitch = PersonalityEnv.isSystemanomalyreportSwitchValue();
                while (rs.next())
                {
                    ProjectInfoModel model = new ProjectInfoModel();
                    model.setUuid(rs.getString("uuid"));
                    model.setPrjId(rs.getLong("prj_id"));
                    model.setPrjName(rs.getString("prj_name"));
                    model.setPrjUpperId(rs.getLong("PRJ_UPPERID"));
                    if (systemanomalyreportSwitch)
                    {
                        if (!"JX_一键发起流程".equals(rs.getString("prj_name")))
                        {
                            list.add(model);
                        }
                    } else
                    {
                        list.add(model);
                    }

                }
                long endTimeAddData = System.currentTimeMillis();
                _log.info("获取工程下拉列表：组装数据     耗时：" + (endTimeAddData - startTimeAddData) + " 豪秒");
                
                con.rollback();
            }
        } catch (SQLException e)
        {
            _log.error("ProjectInfoManager.getRelatedProjectForProess SQLException", e);
        } catch (DBException e)
        {
            _log.error("ProjectInfoManager.getRelatedProjectForProess DBException", e);
        } finally
        {
            DBResource.closeCallableStatement(proc, "getProjectInfo", _log);
            DBResource.closeConn(con, rs, pres, "getProjectInfo", _log);
        }
        return list;
    }

    public List getFlowInfo ( Long prjId )
    {
        boolean test = ServerEnv.getInstance().getBooleanConfig("testshow", false);
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
            if (test)
            {
                FlowInfoModel model = new FlowInfoModel();
                model.setFlowId(11l);
                model.setFlowName("testflow");
                list.add(model);
            } else
            {
                long startTimeConnection = System.currentTimeMillis();
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                long endTimeConnection = System.currentTimeMillis();
                _log.info("获取工作流下拉列表：connection    耗时：" + (endTimeConnection - startTimeConnection) + " 豪秒");
                
                long startTimeSql = System.currentTimeMillis();
                String sql = "select t.* from ieai_workflow t ,ieai_project t1 where t.iprjid=t1.ilatestid and t1.iid="
                        + prjId + " order by t.iflowname desc";
                pres = con.prepareStatement(sql);
                rs = pres.executeQuery();
                long endTimeSql = System.currentTimeMillis();
                _log.info("获取工作流下拉列表：sql    耗时：" + (endTimeSql - startTimeSql) + " 豪秒");
                
                long startTimeData = System.currentTimeMillis();
                while (rs.next())
                {
                    FlowInfoModel model = new FlowInfoModel();
                    model.setFlowId(rs.getLong("IFLOWID"));
                    model.setFlowName(rs.getString("IFLOWNAME"));
                    list.add(model);
                }
                long endTimeData = System.currentTimeMillis();
                _log.info("获取工作流下拉列表：组装数据    耗时：" + (endTimeData - startTimeData) + " 豪秒");
            }
        } catch (SQLException e)
        {
            _log.error("getFlowInfo exec error :" + e);
        } catch (DBException e)
        {
            _log.error("getFlowInfo init error :" + e);
        } finally
        {
            DBResource.closeConn(con, rs, pres, "getFlowInfo", _log);
        }
        return list;
    }

    public List getFlowInfoByNew ( String prjName )
    {
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        List list = new ArrayList();
        try
        {
                long startTimeConnection = System.currentTimeMillis();
                con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                long endTimeConnection = System.currentTimeMillis();
                _log.info("获取工作流下拉列表：connection    耗时：" + (endTimeConnection - startTimeConnection) + " 豪秒");
                
                long startTimeSql = System.currentTimeMillis();
            StringBuilder sql = new StringBuilder();
            String sqls = "";
            sql.append("select t.* from ieai_workflow t ,ieai_project t1 where t.iprjid=t1.ilatestid and t1.iname = '")
                    .append(prjName)
                    .append("' and t1.iid = (select max(t2.ilatestid) from ieai_project t2 where t2.iname = '")
                    .append(prjName).append("') order by t.iflowname");
            sqls = sql.toString();
            pres = con.prepareStatement(sqls);
                rs = pres.executeQuery();
                long endTimeSql = System.currentTimeMillis();
                _log.info("获取工作流下拉列表：sql    耗时：" + (endTimeSql - startTimeSql) + " 豪秒");
                
                long startTimeData = System.currentTimeMillis();
                while (rs.next())
                {
                    FlowInfoModel model = new FlowInfoModel();
                    model.setFlowId(rs.getLong("IFLOWID"));
                    model.setFlowName(rs.getString("IFLOWNAME"));
                    list.add(model);
                }
                long endTimeData = System.currentTimeMillis();
                _log.info("获取工作流下拉列表：组装数据    耗时：" + (endTimeData - startTimeData) + " 豪秒");
                
        } catch (SQLException e)
        {
            _log.error("getFlowInfo exec error :" + e);
        } catch (DBException e)
        {
            _log.error("getFlowInfo init error :" + e);
        } finally
        {
            DBResource.closeConn(con, rs, pres, "getFlowInfo", _log);
        }
        return list;
    }

    /**   
     * @Title: getActInfo   
     * @Description:  (这里用一句话描述这个方法的作用)   
     * @param prjName
     * @return      
     * @author: Administrator 
     * @date:   2017年9月13日 下午9:33:56   
     */
    public List getActInfo ( String prjName )
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select t.iactname, t.iupperid, t.ilastid, t.iid, t.iactdes from"
                + "  ieai_actinfo t, ieai_project t1  where t.iprjid = t1.ilatestid"
                + " and t1.iname = ?"
                + " and t1.iid = (select max(t2.ilatestid) from ieai_project t2 where t2.iname = ?)"
                + " and t.iisvalidact = 1 and (t.iacttype='1' or t.iacttype='2') order by t.iactname";
        try
        {
                long startTimeConnection = System.currentTimeMillis();
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                long endTimeConnection = System.currentTimeMillis();
                _log.info("获取活动下拉列表：connection    耗时：" + (endTimeConnection - startTimeConnection) + " 豪秒");
                
                long startTimeSql = System.currentTimeMillis();
                ps = conn.prepareStatement(sql);
                ps.setString(1, prjName);
                ps.setString(2, prjName);
                rs = ps.executeQuery();
                long endTimeSql = System.currentTimeMillis();
                _log.info("获取活动下拉列表：sql    耗时：" + (endTimeSql - startTimeSql) + " 豪秒");
                
                long startTimeData = System.currentTimeMillis();
                while (rs.next())
                {
                    ActInfoModel model = new ActInfoModel();
                    model.setActId(rs.getLong("iid"));
                    String actdes = rs.getString(IACTDES);
                    if (actdes == null || "".equals(actdes))
                    {
                        model.setActName(rs.getString(IACTNAME));
                    } else
                    {
                        model.setActName(rs.getString(IACTNAME) + "(" + actdes + ")");
                    }
                    model.setActUpperId(rs.getLong(IUPPERID));
                    model.setActLastId(rs.getLong(ILASTID));
                    list.add(model);
                } 
                long endTimeData = System.currentTimeMillis();
                _log.info("获取活动下拉列表：组装数据      耗时：" + (endTimeData - startTimeData) + " 豪秒");
        } catch (DBException e)
        {
            _log.error("ProjectInfoManager.getActInfo", e);
        } catch (SQLException e)
        {
            _log.error("ProjectInfoManager.getActInfo", e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getActInfo", _log);
        }
        return list;
    }

    /**   
     * @Title: getActInfoWithNull   
     * @Description: 添加默认第一行：请选择   
     * @param prjName
     * @return      
     * @author: Administrator 
     * @date:   2017年9月13日 下午9:33:56   
     */
    public List getActInfoWithNull ( String prjName )
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select t.iactname, t.iupperid, t.ilastid, t.iid, t.iactdes"
                + " from ieai_actinfo t, ieai_project t1" + " where t.iprjid = t1.ilatestid" + " and t1.iname = ?"
                + " and t1.iid = (select max(t2.ilatestid) from ieai_project t2 where t2.iname = ?)"
                + " and t.iisvalidact = 1 and (t.iacttype='1' or t.iacttype='2')" + " order by t.iactname";
        try
        {
                long startTimeConnection = System.currentTimeMillis();
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                long endTimeConnection = System.currentTimeMillis();
                _log.info("获取活动下拉列表：connection     耗时：" + (endTimeConnection - startTimeConnection) + " 豪秒");

                long startTimeSql = System.currentTimeMillis();
                ps = conn.prepareStatement(sql);
                ps.setString(1, prjName);
                ps.setString(2, prjName);
                rs = ps.executeQuery();
                long endTimeSql = System.currentTimeMillis();
                _log.info("获取活动下拉列表：sql     耗时：" + (endTimeSql - startTimeSql) + " 豪秒");

                long startTimeData = System.currentTimeMillis();
            // 添加第一行空行
            ActInfoModel nullModel = new ActInfoModel();
            nullModel.setActName("请选择");
            list.add(nullModel);
                while (rs.next())
                {
                    ActInfoModel model = new ActInfoModel();
                    model.setActId(rs.getLong("iid"));
                    String actdes = rs.getString(IACTDES);
                    if (actdes == null || "".equals(actdes))
                    {
                        model.setActName(rs.getString(IACTNAME));
                    } else
                    {
                        model.setActName(rs.getString(IACTNAME) + "(" + actdes + ")");
                    }
                    model.setActUpperId(rs.getLong(IUPPERID));
                    model.setActLastId(rs.getLong(ILASTID));
                    list.add(model);
                } 
            if (!rs.next())
            {
                list = getActInfoWithNullExcel(prjName);
            }
            long endTimeData = System.currentTimeMillis();
            _log.info("获取活动下拉列表：组装数据     耗时：" + (endTimeData - startTimeData) + " 豪秒");

        } catch (DBException e)
        {
            _log.error("ProjectInfoManager.getActInfoWithNull DBException", e);
        } catch (SQLException e)
        {
            _log.error("ProjectInfoManager.getActInfoWithNull SQLException", e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getActInfoWithNull", _log);
        }
        return list;
    }

    public List getActInfoWithNullExcel ( String prjName )
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "  SELECT t.* FROM  ieai_excelmodel t WHERE iflag=1 and t.ichildproname= ?  ";
        if (PersonalityEnv.isjxGetActNameSwitchValue())
        {
            sql = "  SELECT t.* FROM  ieai_excelmodel t WHERE  t.ichildproname= ?  ";
        }
        try
        {
            long startTimeConnection = System.currentTimeMillis();
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            long endTimeConnection = System.currentTimeMillis();
            _log.info("获取活动下拉列表：connection     耗时：" + (endTimeConnection - startTimeConnection) + " 豪秒");

            long startTimeSql = System.currentTimeMillis();
            ps = conn.prepareStatement(sql);
            ps.setString(1, prjName);
            rs = ps.executeQuery();
            long endTimeSql = System.currentTimeMillis();
            _log.info("获取活动下拉列表：sql     耗时：" + (endTimeSql - startTimeSql) + " 豪秒");

            long startTimeData = System.currentTimeMillis();
            // 添加第一行空行
                ActInfoModel nullModel = new ActInfoModel();
                nullModel.setActName("请选择");
                list.add(nullModel);
            while (rs.next())
            {
                ActInfoModel model = new ActInfoModel();
                model.setActId(rs.getLong("IOPERATIONID"));
                String actdes = rs.getString("IACTDESCRIPTION");
                if (actdes == null || "".equals(actdes))
                {
                    model.setActName(rs.getString(IACTNAME));
                } else
                {
                    model.setActName(rs.getString(IACTNAME) + "(" + actdes + ")");
                }
                list.add(model);
            }

            long endTimeData = System.currentTimeMillis();
            _log.info("获取活动下拉列表：组装数据     耗时：" + (endTimeData - startTimeData) + " 豪秒");
                
        } catch (DBException e)
        {
            _log.error("ProjectInfoManager.getActInfoWithNullExcel DBException", e);
        } catch (SQLException e)
        {
            _log.error("ProjectInfoManager.getActInfoWithNullExcel SQLException", e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getActInfoWithNullExcel", _log);
        }
        return list;
    }
    /**   
     * @Title: getActInfoDouble   
     * @Description:  (这里用一句话描述这个方法的作用)   
     * @param prjName
     * @return      
     * @author: xin_zhang 
     * @date:   2017年9月13日 下午9:33:56   
     */
    public List getActInfoDouble ( String prjNames )
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select t.iactname, t.iupperid, t.ilastid, t.iid, t.iactdes"
                + " from ieai_actinfo t, ieai_project t1  where t.iprjid = t1.ilatestid"
                + " and t1.iname in("+prjNames+")"
 + " and t1.iid = (select max(t2.ilatestid) from ieai_project t2 where t2.iname in("
                + prjNames + "))"
                + " and t.iisvalidact = 1 and (t.iacttype='1' or t.iacttype='2')"
                + " order by t.iactname";
        try
        {
                long startTimeConnection = System.currentTimeMillis();
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                long endTimeConnection = System.currentTimeMillis();
                _log.info("获取活动下拉列表：connection     耗时：" + (endTimeConnection - startTimeConnection) + " 豪秒");
                
                long startTimeSql = System.currentTimeMillis();
                ps = conn.prepareStatement(sql);
                rs = ps.executeQuery();
                long endTimeSql = System.currentTimeMillis();
                _log.info("获取活动下拉列表：sql     耗时：" + (endTimeSql - startTimeSql) + " 豪秒");
                
                long startTimeData = System.currentTimeMillis();
                while (rs.next())
                {
                    ActInfoModel model = new ActInfoModel();
                    model.setActId(rs.getLong("iid"));
                    String actdes = rs.getString(IACTDES);
                    if (actdes == null || "".equals(actdes))
                    {
                        model.setActName(rs.getString(IACTNAME));
                    } else
                    {
                        model.setActName(rs.getString(IACTNAME) + "(" + actdes + ")");
                    }
                    model.setActUpperId(rs.getLong(IUPPERID));
                    model.setActLastId(rs.getLong(ILASTID));
                    list.add(model);
                } 
                long endTimeData = System.currentTimeMillis();
                _log.info("获取活动下拉列表：组装数据     耗时：" + (endTimeData - startTimeData) + " 豪秒");
                
        } catch (DBException e)
        {
            _log.error("ProjectInfoManager.getActInfoDouble", e);
        } catch (SQLException e)
        {
            _log.error("ProjectInfoManager.getActInfoDouble", e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getActInfoDouble", _log);
        }
        return list;
    }
}
