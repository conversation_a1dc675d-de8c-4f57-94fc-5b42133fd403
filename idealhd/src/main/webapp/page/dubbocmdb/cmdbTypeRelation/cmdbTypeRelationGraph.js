var cmdbTypeRelationGraph;
var cmdbCodeWindow;
var cell;
Ext.onReady(function() {
	
	if(cmdbTypeRelationGraph){
		cmdbTypeRelationGraph.destroy();
	}

	//分类
	Ext.define('typeManagementmodel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'long'
		}, {
			name : 'text',
			type : 'string'
		}, {
			name : 'itype',
			type : 'long'
		}, {
			name : 'icon',
			type : 'String'
		} ]
	});
	//分类
	var cmdbTypeManagementstore = Ext.create('Ext.data.TreeStore', {
		model : 'typeManagementmodel',
		root : {
			expanded : true
		},
		proxy : {
			type : 'ajax',
			url : 'dubbo/getCMDBTypeManagementList.do'
		}
	});
	
	var cmdbTypeManagementtreepanel1 = Ext.create('Ext.tree.Panel', {
		store:cmdbTypeManagementstore,
		region : 'west',
		width : 250,
		split : true,
		border : true,
		rootVisible : false,
		title : '类型信息',
		bodyCls:'customize_tree',
		cls:'customize_panel_back panel_space_right_zb',
		viewConfig : {}
	});
	
	var cmdbTypeRelationGraphForm2 = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls : 'x-docked-noborder-top',
		border : false,
		title : '',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : ['->',{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '保存',
				handler : savecmdbTypeRelationGraphXML
			},{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '返回',
				handler : backup
			} ]
		} ]
	});
	
	var cmdbTypeRelationGraphPanel3 = Ext.create('Ext.panel.Panel', {
		region : 'center',
		border : true,
		title : ''
	});
	
	var cmdbTypeRelationGraphPanel4 = Ext.create('Ext.panel.Panel', {
		region : 'center',
		layout : 'border',
		bodyPadding : grid_margin,
//		bodyCls: 'service_platform_bodybg',
		cls:'customize_panel_back',
		border : true,
		items : [cmdbTypeRelationGraphForm2,cmdbTypeRelationGraphPanel3]
	});
	var cmdbTypeRelationGraphPanel1 = Ext.create('Ext.panel.Panel', {
		renderTo : 'dubbocmdbTypeRelationGraph_div',
		bodyCls:'service_platform_bodybg',
		border : false,
		height : contentPanel.getHeight() - modelHeigth,
		width : contentPanel.getWidth(),
		layout : 'border',
		items : [ cmdbTypeManagementtreepanel1, cmdbTypeRelationGraphPanel4 ]
	});
	contentPanel.on('resize', function() {
		cmdbTypeRelationGraphPanel1.setHeight(contentPanel.getHeight()
				- modelHeigth);
		cmdbTypeRelationGraphPanel1.setWidth(contentPanel.getWidth());
	});
	
	
	function Permission(locked, createEdges, editEdges, editVertices, cloneCells)
	{
		this.locked = (locked != null) ? locked : false;
		this.createEdges = (createEdges != null) ? createEdges : true;
		this.editEdges = (editEdges != null) ? editEdges : true;;
		this.editVertices = (editVertices != null) ? editVertices : true;;
		this.cloneCells = (cloneCells != null) ? cloneCells : true;;
	};
	
	Permission.prototype.apply = function(graph)
	{
		graph.setConnectable(this.createEdges);
		graph.setCellsLocked(this.locked);
	};
	
	// Checks if the browser is supported
	if (!mxClient.isBrowserSupported())
	{
		// Displays an error message if the browser is not supported.
		mxUtils.error('Browser is not supported!', 200, false);
	}
	else
	{
		// Defines an icon for creating new connections in the connection handler.
		// This will automatically disable the highlighting of the source vertex.
		
		var cmdbTypeRelationGraphPanel3Dom = cmdbTypeRelationGraphPanel3.getEl().dom;
	
		// Creates the div for the graph
		var container = document.createElement('div');
		container.style.position = 'absolute';
		container.style.overflow = 'auto';
		container.style.left = '00px';
		container.style.top = '00px';
		container.style.right = '0px';
		container.style.bottom = '0px';
		container.style.background = 'url("images/grid.gif")';
		
		cmdbTypeRelationGraphPanel3Dom.appendChild(container);
		
		// Workaround for Internet Explorer ignoring certain styles
		if (mxClient.IS_QUIRKS)
		{
			cmdbTypeRelationGraphPanel3Dom.style.overflow = 'hidden';
			new mxDivResizer(container);
		}
		
		// Creates the graph inside the given container
		cmdbTypeRelationGraph = new mxGraph(container);
		var styleEdge = cmdbTypeRelationGraph.getStylesheet().getDefaultEdgeStyle();
		styleEdge[mxConstants.STYLE_EDGE] = mxEdgeStyle.OrthConnector;
		
		// Enable tooltips, disables mutligraphs, enable loops
		cmdbTypeRelationGraph.setMultigraph(false);
		cmdbTypeRelationGraph.setAllowLoops(true);

		// Enables rubberband selection and key handling
		var rubberband = new mxRubberband(cmdbTypeRelationGraph);
		var keyHandler = new mxKeyHandler(cmdbTypeRelationGraph);
		
		// Assigns the delete key
		keyHandler.bindKey(46, function(evt)
		{
			if (cmdbTypeRelationGraph.isEnabled())
			{
				cmdbTypeRelationGraph.removeCells();
			}
		});
		
		// Shared variable between child function scopes
		// aka "private" variable
		var currentPermission = null;
		
		var apply = function(permission)
		{
			cmdbTypeRelationGraph.clearSelection();
			permission.apply(cmdbTypeRelationGraph);
			cmdbTypeRelationGraph.setEnabled(true);
			cmdbTypeRelationGraph.setTooltips(true);
			
			// Updates the icons on the shapes - rarely
			// needed and very slow for large graphs
			cmdbTypeRelationGraph.refresh();
			currentPermission = permission;
		};
		
		apply(new Permission());
		
		var oldDisconnectable = cmdbTypeRelationGraph.isCellDisconnectable;
		cmdbTypeRelationGraph.isCellDisconnectable = function(cell, terminal, source)
		{
			return oldDisconnectable.apply(this, arguments) &&
				currentPermission.editEdges;
		};
		
		var oldTerminalPointMovable = cmdbTypeRelationGraph.isTerminalPointMovable;
		cmdbTypeRelationGraph.isTerminalPointMovable = function(cell)
		{
			return oldTerminalPointMovable.apply(this, arguments) &&
				currentPermission.editEdges;
		};
		
		var oldBendable = cmdbTypeRelationGraph.isCellBendable;
		cmdbTypeRelationGraph.isCellBendable = function(cell)
		{
			return oldBendable.apply(this, arguments) &&
				currentPermission.editEdges;
		};
		
		var oldLabelMovable = cmdbTypeRelationGraph.isLabelMovable;
		cmdbTypeRelationGraph.isLabelMovable = function(cell)
		{
			return oldLabelMovable.apply(this, arguments) &&
				currentPermission.editEdges;
		};
		
		var oldMovable = cmdbTypeRelationGraph.isCellMovable;
		cmdbTypeRelationGraph.isCellMovable = function(cell)
		{
			return oldMovable.apply(this, arguments) &&
				currentPermission.editVertices;
		};
		
		var oldResizable = cmdbTypeRelationGraph.isCellResizable;
		cmdbTypeRelationGraph.isCellResizable = function(cell)
		{
			return oldResizable.apply(this, arguments) &&
				currentPermission.editVertices;
		};
						
		var oldEditable = cmdbTypeRelationGraph.isCellEditable;
		cmdbTypeRelationGraph.isCellEditable = function(cell)
		{
			return false;
			/*return oldEditable.apply(this, arguments) &&
				(this.getModel().isVertex(cell) &&
				currentPermission.editVertices) ||
				(this.getModel().isEdge(cell) &&
				currentPermission.editEdges);*/
		};
		
		var oldDeletable = cmdbTypeRelationGraph.isCellDeletable;
		cmdbTypeRelationGraph.isCellDeletable = function(cell)
		{
			return oldDeletable.apply(this, arguments) &&
				(this.getModel().isVertex(cell) &&
				currentPermission.editVertices) ||
				(this.getModel().isEdge(cell) &&
				currentPermission.editEdges);
		};
		
		var oldCloneable = cmdbTypeRelationGraph.isCellCloneable;
		cmdbTypeRelationGraph.isCellCloneable = function(cell)
		{
			return oldCloneable.apply(this, arguments) &&
				currentPermission.cloneCells;
		};
		
		cmdbTypeRelationGraph.addListener(mxEvent.DOUBLE_CLICK, function(sender, evt) {
			if(evt.getProperty('cell')!=null){
				cell = evt.getProperty('cell');
				if(cell.source!=null && cell.target!=null){
					var sourceitypeid = cell.source.itypeid;
					var sourcevalue = cell.source.value;
					var sourceattributecode = cell.sourceattributecode==null?-1:cell.sourceattributecode;
					var targetitypeid = cell.target.itypeid;
					var targetvalue = cell.target.value;
					var targetattributecode = cell.targetattributecode==null?-1:cell.targetattributecode;
					var relaName  = cell.value;
//					console.log(cell);
					openCmdbCode(sourceitypeid,sourcevalue,sourceattributecode,targetitypeid,targetvalue,targetattributecode,relaName);
				}
			}
		});
		
		// Gets the default parent for inserting new cells. This
		// is normally the first child of the root (ie. layer 0).
		var parent = cmdbTypeRelationGraph.getDefaultParent();
						
		// Adds cells to the model in a single step
		cmdbTypeRelationGraph.getModel().beginUpdate();
		try
		{
			var doc = mxUtils.load(encodeURI("dubbo/getcmdbTypeRelationGraphXML.do?ityperelationid="+ityperelationid+"&tmp="+new Date().getTime()));
			var dec = new mxCodec(doc);
			dec.decode(doc.getDocumentElement(),cmdbTypeRelationGraph.getModel());
		}
		finally
		{
			// Updates the display
			cmdbTypeRelationGraph.getModel().endUpdate();
		}
	}
	
	cmdbTypeManagementtreepanel1.on("itemdblclick",function(gird, record, item, index, e, eOpts){
		if(record.get("itype")==1){
			var parent = cmdbTypeRelationGraph.getDefaultParent();
			var itypeid = record.get('iid');
			var itypename = record.get('text');
			var itypeimg = record.get('icon');
			itypeimg = itypeimg.replace("small","big");
			itypeimg = itypeimg.replace("16x16","50x50");
			var vertex = cmdbTypeRelationGraph.insertVertex(parent, null, itypename, 20, 20, 50, 50,'shape=image;html=1;image='+itypeimg+';verticalLabelPosition=bottom;verticalAlign=top');
			vertex.itypeid = itypeid;
		}else{
			return false;
		}
	})
	
	function savecmdbTypeRelationGraphXML(){
		
	    var xmlDoc = mxUtils.createXmlDocument();
	    var root = xmlDoc.createElement('output');
	    xmlDoc.appendChild(root);

	    var xmlCanvas = new mxXmlCanvas2D(root);
	    var imgExport = new mxImageExport();
	    imgExport.drawState(cmdbTypeRelationGraph.getView().getState(cmdbTypeRelationGraph.model.root), xmlCanvas);

	    var bounds = cmdbTypeRelationGraph.getGraphBounds();
	    var w = Math.ceil(bounds.x + bounds.width);
	    var h = Math.ceil(bounds.y + bounds.height);

	    var imgXml = mxUtils.getXml(root);
		
		var encoder = new mxCodec();
		var node = encoder.encode(cmdbTypeRelationGraph.getModel());
		Ext.Ajax.request({
		    url: 'dubbo/updatecmdbTypeRelationGraphXML.do',
			params : {
				ityperelationid : ityperelationid,
				imgXml : imgXml,
				w : w,
				h : h,
				xml : mxUtils.getPrettyXml(node)
			},				
		    success: function(response, opts) {
		    	var success = Ext.decode(response.responseText).success;
				if (success) {
					Ext.Msg.alert('提示', '保存成功');
					cmdbTypeRelationGraph.refresh();
				} else {
					Ext.Msg.alert('提示', '保存失败！');
				}
		    },
		    failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
	}
	
	function backup(){
		contentPanel.setTitle("类型关系");
		contentPanel.getLoader().load({
			url : 'dubbo/initCmdbTypeRelationInfoPage.do',
			scripts : true
		});
	}
	
	function openCmdbCode(sourceitypeid,sourcevalue,sourceattributecode,targetitypeid,targetvalue,targetattributecode,relaName){
		cmdbCodeWindow = Ext.create('Ext.window.Window',{
			title : '关联属性',
			autoScroll : true,
			modal : true,
			resizable : false,
			closeAction : 'destroy',
			width : 800,
			height : 600,
			loader : {
				url : "dubbo/forwardCmdbCode.do",
				params : {
					sourceitypeid : sourceitypeid,
					sourcevalue:sourcevalue,
					sourceattributecode:sourceattributecode,
					targetitypeid:targetitypeid,
					targetvalue:targetvalue,
					targetattributecode:targetattributecode,
					relaName:relaName
				},
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		}).show();
	}
});