package com.ideal.ieai.server.repository.hd.infocollect.emins;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.EMConstants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.model.ProjectBean;
import com.ideal.ieai.server.repository.hd.infocollect.maintenance.IcExeclInfo;
import com.ideal.ieai.server.repository.hd.infocollect.maintenance.IcInstanceConfigBeanForQuery;
import com.ideal.ieai.server.repository.hd.infocollect.monitor.IcMonitorManage;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.importexecl.SusSysInfo;

/**
 * 
 * 名称: InstanceConfigManager.java<br>
 * 描述: 信息维护数据库操作类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2016年1月15日<br>
 * 
 * <AUTHOR>
 */
public class IcInstanceConfigManager
{
    private static final Logger _log = Logger.getLogger(IcInstanceConfigManager.class);

    private IcInstanceConfigManager()
    {

    }

    // 任务类型集合
    public static final Map<Integer, String> ACTTYPE_MAP  = new LinkedHashMap<Integer, String>();
    // 系统类型集合
    public static final Map<Integer, String> SYSTYPE_MAP  = new LinkedHashMap<Integer, String>();
    // 环境变量集合
    public static final Map<Integer, String> LOADENV_MAP  = new LinkedHashMap<Integer, String>();
    // 切换系统集合
    public static final Map<Integer, String> HDTYPE_MAP   = new LinkedHashMap<Integer, String>();
    // 是否可重做集合
    public static final Map<Integer, String> REDOABLE_MAP = new LinkedHashMap<Integer, String>();

    static
    {
        ACTTYPE_MAP.put(new Integer(1), "SHELL");
        ACTTYPE_MAP.put(new Integer(2), "提醒任务");

        SYSTYPE_MAP.put(new Integer(-1), "未选择");
        SYSTYPE_MAP.put(new Integer(1), "WINDOWS");
        SYSTYPE_MAP.put(new Integer(2), "UNIX/LINUX");

        LOADENV_MAP.put(new Integer(-1), "未选择");
        LOADENV_MAP.put(new Integer(1), "加载");
        LOADENV_MAP.put(new Integer(2), "不加载");

        HDTYPE_MAP.put(new Integer(0), "APP");
        HDTYPE_MAP.put(new Integer(1), "DB");
        HDTYPE_MAP.put(new Integer(5), "存储");
        // HDTYPE_MAP.put(new Integer(2), "WEB");
        // HDTYPE_MAP.put(new Integer(3), "NA");
        // HDTYPE_MAP.put(new Integer(4), "云平台");
        // HDTYPE_MAP.put(new Integer(5), "其他");
        REDOABLE_MAP.put(new Integer(0), "否");
        REDOABLE_MAP.put(new Integer(1), "是");

    }
    private static IcInstanceConfigManager intance = new IcInstanceConfigManager();

    public static IcInstanceConfigManager getInstance ()
    {
        if (intance == null)
        {
            intance = new IcInstanceConfigManager();
        }
        return intance;
    }

    /**
     * 
     * @Title: getInstanceNameList
     * @Description: TODO(【EM】获取子系统名列表)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月11日 fengchun_zhang
     * @变更记录 2016年1月11日 fengchun_zhang 获取 数据库连接 类型 通过service层传入。
     */
    public Map getInstanceNameList ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null; 
        String groupByString = " GROUP BY T.IINSTANCENAME,T.IUPPERID ";
        String orderString = " ORDER BY ASCII(IINSTANCENAME) \n";
        String sqlWhere = " WHERE T.IID>0   AND T.IUPPERID = PRJ.IUPPERID ";
        String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + orderString
                + ") AS RN,T.IINSTANCENAME,T.IUPPERID FROM IEAI_INSTANCE_VERSION T , IEAI_PROJECT PRJ ";
        String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
        if (3 == JudgeDB.IEAI_DB_TYPE)
        {
            // mysql
            sqlPageString = "SELECT * FROM (SELECT T.IINSTANCENAME,T.IUPPERID FROM IEAI_INSTANCE_VERSION T , IEAI_PROJECT PRJ ";
            sqlPageStringEnd = orderString + ") A limit ?, ? ";
        }
        String sqlCount = "SELECT COUNT(*) AS NUM FROM  ( SELECT IINSTANCENAME FROM IEAI_INSTANCE_VERSION T , IEAI_PROJECT PRJ  ";
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {

                    conn = DBResource.getConnection("getInstanceNameList", _log,type);

                    // 子系统名称
                    if (instanceConfigBeanForQuery.getIinstanceNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIinstanceNameForQuery()))
                    {
                        String instancenameString = instanceConfigBeanForQuery
                                .getIinstanceNameForQuery();
                        if (!"".equals(instancenameString))
                        {
                            if (instancenameString.contains("_")
                                    || instancenameString.contains("%"))
                            {
                                instancenameString = instancenameString.replaceAll("_", "/_");
                                instancenameString = instancenameString.replaceAll("%", "/%");
                            }
                            sqlWhere = sqlWhere + " AND T.IINSTANCENAME LIKE '%"
                                    + instancenameString + "%'  ESCAPE '/' ";
                        }
                    }
                    if (instanceConfigBeanForQuery.getIsysType() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.ISYSTYPE ="
                                + instanceConfigBeanForQuery.getIsysType() + "  \n";
                    }
                    
                    
                    List list = DataCenterOperationManage.getInstance().getProject(conn,
                        instanceConfigBeanForQuery.getUserId(), Constants.IEAI_INFO_COLLECT);
               /*     String projectids = "-1";
                    if(list!=null&&list.size()>0){
                        for(int n=0;n<list.size();n++){
                            ProjectBean p = (ProjectBean)list.get(n);
                            if(n==0){
                                projectids = String.valueOf(p.getLastId());
                            }else{
                                projectids = projectids + "," + String.valueOf(p.getLastId());
                            }
                        }
                    }*/
                    String projectids = "-1";
                    long allHD = Constants.IEAI_INFO_COLLECT * -1;
                    String getProjectFilterCondition = "";
                    if (list != null && list.size() > 0)
                    {
                        boolean allHDPerm = false;
                        if (list.size() == 1)
                        {
                            ProjectBean p = (ProjectBean) list.get(0);
                            if (p.getLastId() == allHD)
                            {
                                allHDPerm = true;
                            }
                        }
                        if (!allHDPerm)
                        {
                            for (int n = 0; n < list.size(); n++)
                            {
                                ProjectBean p = (ProjectBean) list.get(n);
                                if (n == 0)
                                {
                                    projectids = String.valueOf(p.getLastId());
                                } else
                                {
                                    projectids = projectids + "," + String.valueOf(p.getLastId());
                                }
                            }
                            getProjectFilterCondition = " AND T.IPROJECTID IN (" + projectids + ")";
                        }
                        sqlPageString = sqlPageString + sqlWhere +getProjectFilterCondition+ groupByString + sqlPageStringEnd;
                        sqlCount = sqlCount + sqlWhere + groupByString;

                        actStat = conn.prepareStatement(sqlPageString);
                        if (3 == JudgeDB.IEAI_DB_TYPE)
                        {
                            // mysql
                            actStat.setInt(1, instanceConfigBeanForQuery.getStart());
                            actStat.setInt(2, instanceConfigBeanForQuery.getLimit());
                        } else
                        {
                            actStat.setInt(1,
                                instanceConfigBeanForQuery.getStart() + instanceConfigBeanForQuery.getLimit());
                            actStat.setInt(2, instanceConfigBeanForQuery.getStart());
                        }

                        actRS = actStat.executeQuery();

                        while (actRS.next())
                        {
                            Map returnMap = new HashMap();
                            returnMap.put("iinstacneName", actRS.getString("IINSTANCENAME"));
                            returnMap.put("iupperId", actRS.getString("IUPPERID"));
                            res.add(returnMap);
                        }

                        actStat = conn.prepareStatement(sqlCount + ") TTT");
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            count = actRS.getInt("NUM");
                        }
                    } 
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log
                            .error("getInstanceNameList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceNameList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceNameList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }
    /**
     * 
     * @Title: getInstanceNameList
     * @Description: TODO(【EM】获取子系统名列表)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月11日 manxi_zhao.重构生成新方法(保留原方法)， 去掉分页。
     */
    public Map getInstanceNameListNoPage ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null; 
        
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            
            String groupByString = " GROUP BY T.IINSTANCENAME,T.IUPPERID ";
            String orderString = " ORDER BY ASCII(IINSTANCENAME) \n";
            String sqlWhere = " WHERE T.IID>0  " 
                            + " AND T.IUPPERID = PRJ.IUPPERID "//added by manxi_zhao.
                            ;
            String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER (" + orderString
                    + ") AS RN,T.IINSTANCENAME,T.IUPPERID FROM IEAI_INSTANCE_VERSION T"
                    + ",IEAI_PROJECT PRJ "
                    ;
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                // 支持mysql
                sqlPageString = "SELECT * FROM (SELECT T.IINSTANCENAME,T.IUPPERID FROM IEAI_INSTANCE_VERSION T"
                        + ",IEAI_PROJECT PRJ ";
            }
            String sqlPageStringEnd = ") A ";//+" WHERE A.RN  <=? AND A.RN >  ?  ";
            String sqlCount = "";
            
            try
            {
                try
                {

                    conn = DBResource.getConnection(method, _log, type);

                    // 子系统名称
                    if (instanceConfigBeanForQuery.getIinstanceNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIinstanceNameForQuery()))
                    {
                        String instancenameString = instanceConfigBeanForQuery
                                .getIinstanceNameForQuery();
                        if (!"".equals(instancenameString))
                        {
                            if (instancenameString.contains("_")
                                    || instancenameString.contains("%"))
                            {
                                instancenameString = instancenameString.replaceAll("_", "/_");
                                instancenameString = instancenameString.replaceAll("%", "/%");
                            }
                            sqlWhere = sqlWhere + " AND T.IINSTANCENAME LIKE '%"
                                    + instancenameString + "%'  ESCAPE '/' ";
                        }
                    }
                    if (instanceConfigBeanForQuery.getIsysType() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.ISYSTYPE ="
                                + instanceConfigBeanForQuery.getIsysType() + "  \n";
                    }
                    
                    
                    List list = DataCenterOperationManage.getInstance().getProject(conn,
                        instanceConfigBeanForQuery.getUserId(), Constants.IEAI_INFO_COLLECT);
                    String projectids = "-1";
                    long allHD = Constants.IEAI_INFO_COLLECT * -1;
                    String getProjectFilterCondition = "";
                    if (list != null && list.size() > 0)
                    {
                        boolean allHDPerm = false;
                        if (list.size() == 1)
                        {
                            ProjectBean p = (ProjectBean) list.get(0);
                            if (p.getLastId() == allHD)
                            {
                                allHDPerm = true;
                            }
                        }
                        if (!allHDPerm)
                        {
                            for (int n = 0; n < list.size(); n++)
                            {
                                ProjectBean p = (ProjectBean) list.get(n);
                                if (n == 0)
                                {
                                    projectids = String.valueOf(p.getLastId());
                                } else
                                {
                                    projectids = projectids + "," + String.valueOf(p.getLastId());
                                }
                            }
                            getProjectFilterCondition = " AND T.IPROJECTID IN (" + projectids + ")";
                        }
                        if (JudgeDB.IEAI_DB_TYPE == 3)
                        {
                            // 支持mysql
                            sqlPageString = sqlPageString + sqlWhere + getProjectFilterCondition + groupByString
                                    + orderString + sqlPageStringEnd;
                        } else
                        {
                            sqlPageString = sqlPageString + sqlWhere + getProjectFilterCondition + groupByString
                                    + sqlPageStringEnd;
                        }
                        sqlCount = "SELECT COUNT(*) NUM FROM (" + sqlPageString + ") CC";

                        actStat = conn.prepareStatement(sqlPageString);
                        actRS = actStat.executeQuery();
                        //查询所有未审核的 应急系统 名称
                        List<String> emList=null;
                        try
                        {
                            emList = IcMonitorManage.getInstance().getExamineedEmList(conn, 2);
                        } catch (Exception e)
                        {
                        }
                        while (actRS.next())
                        {
                            Map returnMap = new HashMap();
                            returnMap.put("iinstacneName", actRS.getString("IINSTANCENAME"));
                            returnMap.put("iupperId", actRS.getString("IUPPERID"));
                            for(String emSysName : emList){
                                if(actRS.getString("IINSTANCENAME").equals(emSysName)){
                                    res.add(returnMap);
                                    break;
                                }
                            }
                        }

                        actStat = conn.prepareStatement(sqlCount);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            count = actRS.getInt("NUM");
                        }
                    } 
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    _log.error(method + " mothod err :" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceNameList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error(method + " : " + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    public List getSwitchDirectionInfo ( String versionInfo, String instanceName )
            throws RepositoryException
    {
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        String version = "";
        String versionAlias = "";
        String[] v = versionInfo.split("##!!@@!!##");
        version = v[0];
        versionAlias = v[1];

        String sql = " select A.IID, A.ISWITCHID, B.ISWITCHDES  from IEAI_INSTANCE_VERSION A left join IEAI_SWITCH_DIR B on A.ISWITCHID=B.IID where IINSTANCENAME='"
                + instanceName
                + "' and IVERSION="
                + version
                + " and IVERSIONALIAS='"
                + versionAlias + "' and ISYSTYPE=2 ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getSwitchDirectionInfo", _log, Constants.IEAI_INFO_COLLECT);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("instanceId", actRS.getInt("IID"));
                        returnMap.put("switchId", actRS.getInt("ISWITCHID"));
                        returnMap.put("switchName", actRS.getString("ISWITCHDES"));
                        res.add(returnMap);
                    }

                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getSwitchDirectionInfo method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getSwitchDirectionInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getSwitchDirectionInfo method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return res;
    }

    public String getLastVerInfo ( String instanceName ) throws RepositoryException
    {
        String res = "";
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        String sql = " SELECT max(IVERSION) as VER FROM IEAI_INSTANCE_VERSION W WHERE W.IINSTANCENAME ='"
                + instanceName + "' AND ISYSTYPE=2 ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getConnection("getLastVerInfo", _log, Constants.IEAI_INFO_COLLECT);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        res = actRS.getString("VER");
                    }

                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log.error("getLastVerInfo method of InstanceConfigManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getLastVerInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getLastVerInfo method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return res;
    }

    /**
     * 
     * @Title: getInstanceInfoList
     * @Description: TODO(【公用】获取子系统内容)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月14日 yunpeng_zhang
     */
    public Map getInstanceInfoList ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {

                    String orderString = " ORDER BY ICONNER,ISERNER \n";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString + ") AS RN,T.* FROM IEAI_INSTANCEINFO T";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_INSTANCEINFO T ";
                    conn = DBResource.getConnection("getInstanceInfoList", _log, Constants.IEAI_INFO_COLLECT);
                    if (instanceConfigBeanForQuery.getIidForQuery() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCEID ="
                                + instanceConfigBeanForQuery.getIidForQuery() + "  \n";
                    }
                    if (instanceConfigBeanForQuery.getIiactNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIiactNameForQuery()))
                    {
                        sqlWhere = sqlWhere + " AND T.IACTNAME LIKE '%"
                                + instanceConfigBeanForQuery.getIiactNameForQuery() + "%'  \n";
                    }
                    if (instanceConfigBeanForQuery.getIswitchForQuery() > 0)
                    {
                        sqlWhere = sqlWhere
                                + " AND T.IINSTANCEID IN (SELECT T1.IID FROM IEAI_INSTANCE_VERSION T1 WHERE T1.ISWITCHID="
                                + instanceConfigBeanForQuery.getIswitchForQuery()
                                + " AND T1.ISYSTYPE=" + instanceConfigBeanForQuery.getIsysType()
                                + " )\n";
                    }

                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(1, instanceConfigBeanForQuery.getStart()
                            + instanceConfigBeanForQuery.getLimit());
                    actStat.setInt(2, instanceConfigBeanForQuery.getStart());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iserner", actRS.getDouble("ISERNER"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iprener", "-1".equals(actRS.getString("IPRENER")) ? ""
                                : actRS.getString("IPRENER"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("iactDes", actRS.getString("IACTDES"));
                        returnMap.put("iactType", ACTTYPE_MAP.get(actRS.getInt("IACTTYPE")));
                        returnMap.put("iremInfo", actRS.getString("IREMINFO"));
                        String iipString = actRS.getString("IIP");
                        if (instanceConfigBeanForQuery.getIsysType() == 1)
                        {
                            if (iipString != null && iipString.length() > 0)
                            {
                                iipString = iipString + ":" + actRS.getInt("IPORT");
                            }

                        }
                        returnMap.put("iip", iipString);
                        returnMap.put("iprot", actRS.getInt("IPORT"));
                        returnMap.put("isysType", SYSTYPE_MAP.get(actRS.getInt("ISYSTYPE")));
                        returnMap.put("iexecUser", actRS.getString("IEXECUSER"));
                        returnMap.put("ishellScript", actRS.getString("ISHELLSCRIPT"));
                        returnMap.put("iisloadenv", LOADENV_MAP.get(actRS.getInt("IISLOADENV")));
                        returnMap.put("ishellPath", actRS.getString("ISHELLPATH"));
                        returnMap.put("itimeOut", actRS.getLong("ITIMEOUT") == 0 ? "" : actRS
                                .getLong("ITIMEOUT"));
                        returnMap.put("iswitchSysType", HDTYPE_MAP.get(actRS
                                .getInt("ISWITCHSYSTYPE")));
                        returnMap.put("iparameter", actRS.getString("IPARAMETER"));
                        returnMap.put("iparaCheck", actRS.getString("IPARACHECK"));
                        returnMap.put("iparaSwitch", actRS.getString("IPARASWITCH"));
                        returnMap.put("iparaSwitchforce", actRS.getString("IPARASWITCHFORCE"));
                        returnMap.put("iexpeceInfo", actRS.getString("IEXPECEINFO"));
                        returnMap.put("iisDisable", actRS.getInt("IISDISABLE") == 1 ? true : false);
                        if (instanceConfigBeanForQuery.getIsysType() == 1)
                        {
                            returnMap.put("icenter", actRS.getString("ICENTER"));
                            returnMap.put("iconnerName", actRS.getString("ICONNERNAME"));
                        } else
                        {
                            returnMap.put("iconnerName", actRS.getString("ICONNERNAME"));
                        }
                        returnMap.put("imodeltype", actRS.getString("IMODELTYPE"));
                        returnMap.put("iretnvalexcepion", actRS.getString("IRETNVALEXCEPION"));
                        returnMap.put("iredoable", actRS.getString("IREDOABLE"));
                        returnMap.put("icenter", actRS.getString("ICENTER"));

                        res.add(returnMap);
                    }

                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceInfoList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceInfoList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: getLastInstanceInfoList
     * @Description: TODO(【公用】获取最新子系统)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月14日 yunpeng_zhang
     */
    public Map getLastInstanceInfoList ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {

                    String orderString = " ORDER BY ICONNER,ISERNER \n";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString
                            + ") AS RN,T.* FROM IEAI_INSTANCEINFO T WHERE T.IINSTANCEID IN(SELECT   T1.IID FROM IEAI_INSTANCE_VERSION T1 WHERE  T1.ISYSTYPE=? AND  T1.IINSTANCENAME=? AND  T1.IVERSION=(SELECT MAX(T2.IVERSION) FROM   IEAI_INSTANCE_VERSION T2 WHERE T2.ISYSTYPE=? AND T2.IINSTANCENAME=?))  ";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_INSTANCEINFO T WHERE T.IINSTANCEID IN(SELECT   T1.IID FROM IEAI_INSTANCE_VERSION T1 WHERE T1.ISYSTYPE=? AND  T1.IINSTANCENAME=? AND  T1.IVERSION=(SELECT MAX(T2.IVERSION) FROM   IEAI_INSTANCE_VERSION T2 WHERE T2.ISYSTYPE=? AND T2.IINSTANCENAME=?)) ";
                    String sqlWhere = "";
                    if (instanceConfigBeanForQuery.getIiactNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIiactNameForQuery()))
                    {
                        sqlWhere = sqlWhere + " AND T.IACTNAME LIKE '%"
                                + instanceConfigBeanForQuery.getIiactNameForQuery() + "%'  \n";
                    }
                    if (instanceConfigBeanForQuery.getIswitchForQuery() > 0)
                    {
                        sqlWhere = sqlWhere
                                + " AND T.IINSTANCEID IN (SELECT T3.IID FROM IEAI_INSTANCE_VERSION T3 WHERE T3.ISWITCHID="
                                + instanceConfigBeanForQuery.getIswitchForQuery()
                                + " AND T3.ISYSTYPE=" + instanceConfigBeanForQuery.getIsysType()
                                + " )\n";
                    }
                    conn = DBResource.getConnection("getInstanceInfoList", _log, Constants.IEAI_INFO_COLLECT);
                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                    int index = 0;
                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(++index, instanceConfigBeanForQuery.getIsysType());
                    actStat.setString(++index, instanceConfigBeanForQuery
                            .getIinstanceNameForQuery());
                    actStat.setInt(++index, instanceConfigBeanForQuery.getIsysType());
                    actStat.setString(++index, instanceConfigBeanForQuery
                            .getIinstanceNameForQuery());
                    actStat.setInt(++index, instanceConfigBeanForQuery.getStart()
                            + instanceConfigBeanForQuery.getLimit());
                    actStat.setInt(++index, instanceConfigBeanForQuery.getStart());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iserner", actRS.getLong("ISERNER"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iprener", "-1".equals(actRS.getString("IPRENER")) ? ""
                                : actRS.getString("IPRENER"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("iactDes", actRS.getString("IACTDES"));
                        returnMap.put("iactType", ACTTYPE_MAP.get(actRS.getInt("IACTTYPE")));
                        returnMap.put("iremInfo", actRS.getString("IREMINFO"));
                        String iipString = actRS.getString("IIP");
                        if (instanceConfigBeanForQuery.getIsysType() == 1)
                        {
                            if (iipString != null && iipString.length() > 0)
                            {
                                iipString = iipString + ":" + actRS.getInt("IPORT");
                            }

                        }
                        returnMap.put("iip", iipString);
                        returnMap.put("iprot", actRS.getInt("IPORT"));
                        returnMap.put("isysType", SYSTYPE_MAP.get(actRS.getInt("ISYSTYPE")));
                        returnMap.put("iexecUser", actRS.getString("IEXECUSER"));
                        returnMap.put("ishellScript", actRS.getString("ISHELLSCRIPT"));
                        returnMap.put("iisloadenv", LOADENV_MAP.get(actRS.getInt("IISLOADENV")));
                        returnMap.put("ishellPath", actRS.getString("ISHELLPATH"));
                        returnMap.put("itimeOut", actRS.getLong("ITIMEOUT") == 0 ? "" : actRS
                                .getLong("ITIMEOUT"));
                        returnMap.put("iswitchSysType", HDTYPE_MAP.get(actRS
                                .getInt("ISWITCHSYSTYPE")));
                        returnMap.put("iparameter", actRS.getString("IPARAMETER"));
                        returnMap.put("iparaCheck", actRS.getString("IPARACHECK"));
                        returnMap.put("iparaSwitch", actRS.getString("IPARASWITCH"));
                        returnMap.put("iparaSwitchforce", actRS.getString("IPARASWITCHFORCE"));
                        returnMap.put("iexpeceInfo", actRS.getString("IEXPECEINFO"));
                        returnMap.put("iisDisable", actRS.getInt("IISDISABLE") == 1 ? true : false);
                        if (instanceConfigBeanForQuery.getIsysType() == 1)
                        {
                            returnMap.put("icenter", actRS.getString("ICENTER"));
                            returnMap.put("iconnerName", actRS.getString("ICONNERNAME"));
                        } else
                        {
                            returnMap.put("iconnerName", actRS.getString("ICONNERNAME"));
                        }

                        returnMap.put("imodeltype", actRS.getString("IMODELTYPE"));
                        returnMap.put("iretnvalexcepion", actRS.getString("IRETNVALEXCEPION"));
                        returnMap.put("iredoable", actRS.getString("IREDOABLE"));
                        returnMap.put("icenter", actRS.getString("ICENTER"));
                        res.add(returnMap);
                    }
                    index = 0;
                    actStat = conn.prepareStatement(sqlCount + sqlWhere);
                    actStat.setInt(++index, instanceConfigBeanForQuery.getIsysType());
                    actStat.setString(++index, instanceConfigBeanForQuery
                            .getIinstanceNameForQuery());
                    actStat.setInt(++index, instanceConfigBeanForQuery.getIsysType());
                    actStat.setString(++index, instanceConfigBeanForQuery
                            .getIinstanceNameForQuery());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getLastInstanceInfoList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getLastInstanceInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getLastInstanceInfoList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: getMapKey
     * @Description: TODO(【公用】根据字符串获取int型值)
     * @param inString
     * @param inMap
     * @return
     * @return Integer 返回类型
     * @throws
     * @变更记录 2016年1月14日 yunpeng_zhang
     */
    public Integer getMapKey ( String inString, Map<Integer, String> inMap )
    {
        Iterator<Map.Entry<Integer, String>> entries = inMap.entrySet().iterator();

        while (entries.hasNext())
        {

            Map.Entry<Integer, String> entry = entries.next();
            if (inString.equals(entry.getValue()))
            {
                return entry.getKey();
            }

        }
        return 1;
    }

    /**
     * 
     * @Title: getInstanceForComBox
     * @Description: TODO(【公用】选择版本信息下拉框取值)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月14日 yunpeng_zhang
     */
    public Map getInstanceForComBox ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String orderString = " ORDER BY ITIME \n";
                    String sqlWhere = " WHERE T.IID>0  ";
//                    String sqlPageString = "SELECT * FROM  IEAI_INSTANCE_VERSION T";
                    String sqlPageString = "SELECT T.IINSTANCEID,  T.IINSTANCENAME  FROM  IEAI_EM_INSTANCEINFO T ,IEAI_EM_INSTANCE_VERSION V WHERE ";
                    conn = DBResource.getConnection("getInstanceForComBox", _log, Constants.IEAI_INFO_COLLECT);

                    // 子系统名称
                    if (instanceConfigBeanForQuery.getIinstanceNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIinstanceNameForQuery()))
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCENAME = '"
                                + instanceConfigBeanForQuery.getIinstanceNameForQuery() + "' AND V.IVERSION=0 AND T.IINSTANCEID=V.IID";
                    }
                    // 类型
                  /*  if (instanceConfigBeanForQuery.getIsysType() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.ISYSTYPE ="
                                + instanceConfigBeanForQuery.getIsysType() + "  \n";
                    }
                    // 切换方向
                    if (instanceConfigBeanForQuery.getIswitchForQuery() > 0)
                    {
                        sqlWhere = sqlWhere + " AND T.ISWITCHID ="
                                + instanceConfigBeanForQuery.getIswitchForQuery() + "  \n";
                    }*/

                    sqlPageString = sqlPageString + sqlWhere + orderString;

                    actStat = conn.prepareStatement(sqlPageString);
                    actRS = actStat.executeQuery();
                    // Map returnMapFirst = new HashMap();
                    // returnMapFirst.put("iid", -1);
                    // returnMapFirst.put("versionString", "--未选择--");
                    // res.add(returnMapFirst);
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IINSTANCEID"));
                        returnMap.put("versionString", (actRS.getString("IINSTANCENAME")));
                        res.add(returnMap);
                    }

                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceForComBox method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceForComBox", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceForComBox method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * <li>Description:</li>
     * 
     * <AUTHOR> 2016年3月14日
     * @param instanceName
     * @return
     * @throws RepositoryException return Map
     */
    public Map getVersionInfoWithSysType ( String instanceName, int sysType )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String sql = "select IVERSIONALIAS, IVERSION, MAX(ITIME) as MTIME FROM  IEAI_INSTANCE_VERSION T where T.IINSTANCENAME='"
                            + instanceName
                            + "' AND T.ISYSTYPE ="
                            + sysType
                            + " group by IVERSIONALIAS,IVERSION order by MTIME desc";
                    conn = DBResource.getConnection("getVersionInfoForHD", _log, Constants.IEAI_INFO_COLLECT);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("versionInfo", actRS.getInt("IVERSION") + "##!!@@!!##"
                                + actRS.getString("IVERSIONALIAS"));
                        returnMap.put("versionString", (actRS.getLong("MTIME") != 0 ? sdf
                                .format(new Date(actRS.getLong("MTIME"))) : "")
                                + "【" + actRS.getString("IVERSIONALIAS") + "】");
                        res.add(returnMap);
                    }

                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getVersionInfoForHD method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getVersionInfoForHD", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getVersionInfoForHD method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: getIswitchForComBox
     * @Description: TODO(【灾备切换】切换方向下拉框取值)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月14日 yunpeng_zhang
     */
    public Map getIswitchForComBox ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String orderString = " ORDER BY ISWITCHDES \n";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM  IEAI_SWITCH_DIR T";
                    conn = DBResource.getConnection("getInstanceForComBox", _log, Constants.IEAI_INFO_COLLECT);

                    sqlPageString = sqlPageString + sqlWhere + orderString;
                    actStat = conn.prepareStatement(sqlPageString);
                    actRS = actStat.executeQuery();
                    Map returnMapFirst = new HashMap();
                    returnMapFirst.put("iid", -1);
                    returnMapFirst.put("iswitchDes", "--未选择--");
                    res.add(returnMapFirst);
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iswitchDes", actRS.getString("ISWITCHDES"));
                        res.add(returnMap);
                    }

                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getIswitchForComBox method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getIswitchForComBox", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getIswitchForComBox method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: updateInstanceInfo
     * @Description: TODO(【公用】修改模板内容)
     * @param instanceInfos
     * @param userLoginName
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年1月15日 yunpeng_zhang
     */
    public boolean updateInstanceInfo ( List<Map<String, Object>> instanceInfos,
            String userLoginName , int type) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        if (null != instanceInfos && instanceInfos.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {

                try
                {

                    String sql = "UPDATE IEAI_INSTANCEINFO T SET T.ICONNER=?,T.IACTDES=?,T.IREMINFO=?,T.IPRENER=?,T.IIP=?,T.IPORT=?,T.IEXECUSER=?,T.ISHELLSCRIPT=?,T.ISHELLPATH=?,T.IEXPECEINFO=?,T.IACTTYPE=?,T.ISYSTYPE=?,T.IISLOADENV=?,T.ITIMEOUT=?,T.ISWITCHSYSTYPE=?,T.IPARACHECK=?,T.IPARASWITCH=?,T.IPARASWITCHFORCE=?,T.IISDISABLE=?,T.IMODELTYPE=?,T.IRETNVALEXCEPION=?,T.IREDOABLE=?,T.ICONNERNAME=?,T.ICENTER=?  WHERE T.IID=?";
                    conn = DBResource.getConnection("updateInstanceInfo", _log, type);
                    actStat = conn.prepareStatement(sql);
                    for (int z = 0; z < instanceInfos.size(); z++)
                    {
                        Map<String, Object> bsMap = instanceInfos.get(z);
                        Long iinstanceId = (Long) Long.parseLong(bsMap.get("iinstanceId")
                                .toString());
                        Long iserner = (Long) Long.parseLong(bsMap.get("iserner").toString());

                        Long iconner = (Long) Long.parseLong(bsMap.get("iconner").toString());
                        String iactDes = bsMap.get("iactDes").toString().trim();
                        String iremInfo = bsMap.get("iremInfo").toString().trim();
                        String iprener = bsMap.get("iprener").toString().trim();
                        String iip = bsMap.get("iip").toString().trim();
                        String[] iipAndPort = iip.split(":");
                        Integer iport = null;
                        if (iipAndPort.length > 1)
                        {
                            String portString = iipAndPort[1];
                            iport = Integer.parseInt(portString);
                            iip = iipAndPort[0];
                        } else
                        {
                            iport = Integer.parseInt(bsMap.get("iprot").toString());
                        }
                        String imodeltype = "";
                        if (null != bsMap.get("imodeltype")
                                && !"null".equals(bsMap.get("imodeltype").toString())
                                && !"".equals(bsMap.get("imodeltype").toString()))
                        {
                            imodeltype = bsMap.get("imodeltype").toString().trim();
                        }
                        String iretnvalexcepion = "";
                        if (null != bsMap.get("iretnvalexcepion")
                                && !"null".equals(bsMap.get("iretnvalexcepion").toString())
                                && !"".equals(bsMap.get("iretnvalexcepion").toString()))
                        {
                            iretnvalexcepion = bsMap.get("iretnvalexcepion").toString().trim();
                        }
                        String iredoable = "";
                        if (null != bsMap.get("iredoable")
                                && !"null".equals(bsMap.get("iredoable").toString())
                                && !"".equals(bsMap.get("iredoable").toString()))
                        {
                            iredoable = bsMap.get("iredoable").toString().trim();
                        }
                        // String imodeltype = bsMap.get("imodeltype").toString().trim();
                        // String iretnvalexcepion =
                        // bsMap.get("iretnvalexcepion").toString().trim();
                        // String iredoable = bsMap.get("iredoable").toString().trim();

                        String iexecUser = bsMap.get("iexecUser").toString().trim();
                        String ishellScript = bsMap.get("ishellScript").toString().trim();
                        String ishellPath = bsMap.get("ishellPath").toString().trim();
                        String iexpeceInfo = bsMap.get("iexpeceInfo").toString().trim();
                        String iparaCheck = bsMap.get("iparaCheck").toString().trim();
                        String iparaSwitch = bsMap.get("iparaSwitch").toString().trim();
                        String iparaSwitchforce = bsMap.get("iparaSwitchforce").toString().trim();
                        Boolean iisDisable = (Boolean) bsMap.get("iisDisable");
                        Integer iactType = this.getMapKey(bsMap.get("iactType").toString(),
                            ACTTYPE_MAP);
                        Integer isysType = this.getMapKey(bsMap.get("isysType").toString(),
                            SYSTYPE_MAP);
                        Integer iisloadenv = this.getMapKey(bsMap.get("iisloadenv").toString(),
                            LOADENV_MAP);
                        Integer iswitchsysType = this.getMapKey(bsMap.get("iswitchSysType")
                                .toString(), HDTYPE_MAP);
                        Long iid = (Long) Long.parseLong(bsMap.get("iid").toString());
                        String itimeOutString = "0";
                        if (null != bsMap.get("itimeOut")
                                && !"null".equals(bsMap.get("itimeOut").toString())
                                && !"".equals(bsMap.get("itimeOut").toString()))
                        {
                            itimeOutString = bsMap.get("itimeOut").toString();
                        }
                        Long itimeOut = (Long) Long.parseLong(itimeOutString);
                        String iconnername = "";
                        if (null != bsMap.get("iconnername")
                                && !"null".equals(bsMap.get("iconnername").toString())
                                && !"".equals(bsMap.get("iconnername").toString()))
                        {
                            iconnername = bsMap.get("iconnername").toString();
                        }
                        String icenter = "";
                        if (null != bsMap.get("icenter")
                                && !"null".equals(bsMap.get("icenter").toString())
                                && !"".equals(bsMap.get("icenter").toString()))
                        {
                            icenter = bsMap.get("icenter").toString();
                        }
                        // String iconnername = bsMap.get("iconnername").toString().trim();
                        // String icenter = bsMap.get("icenter").toString().trim();
                        boolean checkreturnValue = this.checkIprener(conn, iinstanceId, iconner,
                            iserner, iprener);
                        // 判断依赖关系是否填写规范
                        if (!checkreturnValue)
                        {
                            returnValue = false;
                            ServerError serverError = new ServerError();
                            serverError.setErrDetail("依赖关系内容填写不符合规范！");
                            RepositoryException repositoryException = new RepositoryException(
                                    serverError);
                            DBResource.rollback(conn, ServerError.ERR_DB_UPDATE,
                                repositoryException, "updateInstanceInfo", _log);
                            // TODO Auto-generated catch block
                            throw repositoryException;
                        }
                        int index = 0;
                        actStat.setLong(++index, iconner);
                        actStat.setString(++index, iactDes);
                        actStat.setString(++index, iremInfo);
                        actStat.setString(++index, "".equals(iprener) ? "-1" : iprener);
                        actStat.setString(++index, iip);
                        actStat.setInt(++index, iport);
                        actStat.setString(++index, iexecUser);
                        actStat.setString(++index, ishellScript);
                        actStat.setString(++index, ishellPath);
                        actStat.setString(++index, iexpeceInfo);
                        actStat.setInt(++index, iactType);
                        actStat.setInt(++index, isysType);
                        actStat.setInt(++index, iisloadenv);
                        actStat.setLong(++index, itimeOut);
                        actStat.setInt(++index, iswitchsysType);
                        actStat.setString(++index, iparaCheck);
                        actStat.setString(++index, iparaSwitch);
                        actStat.setString(++index, iparaSwitchforce);
                        actStat.setInt(++index, iisDisable == true ? 1 : 0);

                        actStat.setString(++index, imodeltype);
                        actStat.setString(++index, iretnvalexcepion);
                        actStat.setString(++index, iredoable);
                        actStat.setString(++index, iconnername);
                        actStat.setString(++index, icenter);
                        actStat.setLong(++index, iid);
                        actStat.addBatch();

                        _log.info("updateInstanceInfo {iid=" + iid + "} by user:" + userLoginName);
                    }
                    actStat.executeBatch();
                    conn.commit();

                } catch (SQLException e)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e, "updateInstanceInfo",
                        _log);
                    // TODO Auto-generated catch block
                    _log
                            .error("updateInstanceInfo method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                } finally
                {
                    DBResource.closePSConn(conn, actStat, "updateInstanceInfo", _log);
                }
                break;
            }
        }

        return returnValue;
    }

    /**
     * <li>Description:招商版</li>
     * 
     * <AUTHOR> 2016年3月16日
     * @param instanceInfos
     * @param userLoginName
     * @return
     * @throws RepositoryException return boolean
     */
    public boolean updateInstanceInfoForZS ( List<Map<String, Object>> instanceInfos,
            String userLoginName ) throws RepositoryException
    {return true;}

    /**
     * 
     * @Title: checkIprener
     * @Description: TODO(【公用】判断依赖关系是否正确)
     * @param conn
     * @param instanceId
     * @param conner
     * @param serner
     * @param iprener
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年1月15日 yunpeng_zhang
     */
    private boolean checkIprener ( Connection conn, Long instanceId, Long conner, Long serner,
            String iprener ) throws RepositoryException
    {
        boolean returnValue = true;
        List<Long> sernerList = new ArrayList<Long>();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        if ("".equals(iprener))
        {
            return returnValue;
        }
        String sql = "SELECT T.ISERNER FROM IEAI_INSTANCEINFO T WHERE T.IINSTANCEID=? AND T.ICONNER=? AND T.ISERNER!=?";
        try
        {
            actStat = conn.prepareStatement(sql);
            int index = 0;
            actStat.setLong(++index, instanceId);
            actStat.setLong(++index, conner);
            actStat.setLong(++index, serner);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                sernerList.add(actRS.getLong("ISERNER"));
            }
        } catch (SQLException e)
        {
            // TODO Auto-generated catch block
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, "checkIprener", _log);
        }

        String[] pre = iprener.split(",");
        for (int ii = 0; ii < pre.length; ii++)
        {
            try
            {
                long newserner = Long.parseLong(pre[ii]);
                if (newserner == -1 || sernerList.contains(newserner))
                {
                    continue;
                } else
                {
                    returnValue = false;
                    break;
                }

            } catch (Exception ee)
            {
                returnValue = false;
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: deleteInstanceForAuto
     * @Description: TODO(【EM】删除应急系统)
     * @param deleteNames
     * @param userName
     * @param isysType
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年1月18日 yunpeng_zhang
     */
    public boolean deleteInstanceForAuto ( String[] deleteNames, String userName, int isysType )
            throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        if (null != deleteNames && deleteNames.length > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        conn = DBResource.getConnection("deleteInstanceForAuto", _log,isysType);
                        // 删除详细内容
                        String sql1 = "";
                        String sql5 = "";
                        if (isysType == 2 || isysType == 3 || isysType == Constants.IEAI_INFO_COLLECT)// 变更管理=3
                        {
                            sql1 = "DELETE FROM IEAI_INSTANCEINFO WHERE IINSTANCEID IN (SELECT t1.IID FROM IEAI_INSTANCE_VERSION T1 WHERE T1.ISYSTYPE=? AND T1.IINSTANCENAME IN ("
                                    + StringUtils.join(deleteNames, ",") + "))";
                            // 删除版本信息
                            sql5 = "DELETE FROM IEAI_INSTANCE_VERSION WHERE ISYSTYPE=? AND IINSTANCENAME IN ("
                                    + StringUtils.join(deleteNames, ",") + ")";
                        }/* else if (isysType == 1)
                        {
                            sql1 = "DELETE IEAI_SUS_INSTANCEINFO T WHERE T.IINSTANCEID IN (SELECT t1.IID FROM IEAI_INSTANCE_VERSION T1 WHERE T1.ISYSTYPE=? AND T1.IINSTANCENAME IN ("
                                    + StringUtils.join(deleteNames, ",") + "))";
                            // 删除版本信息
                            sql5 = "DELETE IEAI_SUS_INSTANCE_VERSION T1  WHERE T1.ISYSTYPE=? AND T1.IINSTANCENAME IN ("
                                    + StringUtils.join(deleteNames, ",") + ")";
                        }*/

                        // _log.info(sql1);
                        actStat = conn.prepareStatement(sql1);
                        actStat.setLong(1, isysType);
                        actStat.executeUpdate();

                        // 删除业务系统表
                        String sql2 = "DELETE  FROM IEAI_PROJECT WHERE IID IN (SELECT T.IPROJECTID FROM IEAI_INSTANCE_VERSION T WHERE T.ISYSTYPE=? AND T.IINSTANCENAME IN ("
                                + StringUtils.join(deleteNames, ",") + "))";
                        // _log.info(sql2);
                        actStat = conn.prepareStatement(sql2);
                        actStat.setLong(1, isysType);
                        actStat.executeUpdate();
                        // 删除业务系统与设备关联表
                        String sql3 = "DELETE  FROM IEAI_SYS_RELATION WHERE SYSTEMID IN (SELECT T.IIEAISYSID FROM IEAI_INSTANCE_VERSION T WHERE T.ISYSTYPE=? AND T.IINSTANCENAME IN ("
                                + StringUtils.join(deleteNames, ",") + "))";
                        // _log.info(sql3);
                        actStat = conn.prepareStatement(sql3);
                        actStat.setLong(1, isysType);
                        actStat.executeUpdate();
                        // 删除业务系统与角色关联表
                        String sql4 = "DELETE  FROM IEAI_SYS_PERMISSION WHERE IPROID IN (SELECT T.IPROJECTID FROM IEAI_INSTANCE_VERSION T WHERE T.ISYSTYPE=? AND T.IINSTANCENAME IN ("
                                + StringUtils.join(deleteNames, ",") + "))";
                        // _log.info(sql4);
                        actStat = conn.prepareStatement(sql4);
                        actStat.setLong(1, isysType);
                        actStat.executeUpdate();

                        // _log.info(sql5);
                        actStat = conn.prepareStatement(sql5);
                        actStat.setLong(1, isysType);
                        actStat.executeUpdate();

                        conn.commit();
                        _log.info("deleteInstanceForAuto {IINSTANCENAME="
                                + StringUtils.join(deleteNames, ",") + "} by user:" + userName);

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_DELETE, e,
                            "deleteInstanceForAuto", _log);
                        // TODO Auto-generated catch block
                        _log
                                .error("deleteInstanceForAuto method of InstanceConfigManager.class SQLException:"
                                        + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_DELETE);
                    } finally
                    {
                        DBResource.closePSConn(conn, actStat, "deleteInstanceForAuto", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    returnValue = false;
                    _log
                            .error("deleteInstanceForAuto method of InstanceConfigManager.class RepositoryException:"
                                    + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_DELETE);
                }
            }
        }

        return returnValue;
    }


    /**
     * <li>Description:获得版本信息 列表</li>
     * 
     * <AUTHOR> 2016-2-24
     * @param instNam
     * @return
     * @throws RepositoryException return List<String>
     */
    public List<Map> getVersionNameList ( String instNam ) throws RepositoryException
    {
        StringBuffer querySQl = new StringBuffer(
                " SELECT  DISTINCT( W.IVERSIONALIAS) IAS FROM  IEAI_INSTANCE_VERSION W WHERE W.IINSTANCENAME =?");
        List<Map> list = new ArrayList<Map>();
        String ias = null;
        Map<String, String> res = null;

        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement statmt = null;
                Connection con = null;
                ResultSet rs = null;
                try
                {
                    con = DBResource.getConnection("getVersionNameList", _log, Constants.IEAI_INFO_COLLECT);
                    statmt = con.prepareStatement(querySQl.toString());
                    statmt.setString(1, instNam);
                    rs = statmt.executeQuery();
                    while (rs.next())
                    {
                        ias = rs.getString("IAS");
                        res = new LinkedHashMap<String, String>();
                        res.put("ias", ias);
                        list.add(res);
                    }
                    statmt = null;
                    rs = null;
                } catch (SQLException e)
                {
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, null, statmt, "getVersionNameList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;

    }

    /**
     * <li>Description:根据实例名和版本信息查询活动信息</li>
     * 
     * <AUTHOR> 2016-2-24
     * @param instNam 实例名
     * @param ias 版本信息
     * @return return Map<String,List<SusExeclInfo>>
     * @throws RepositoryException update by li_yang 除了versionalias外，还需要version信息，才能定位步骤
     */
    public Map<String, List<IcExeclInfo>> getExelInfo ( String instNam, String version, String ias )
            throws RepositoryException
    {

        Map<String, List<IcExeclInfo>> rsMap = new LinkedHashMap<String, List<IcExeclInfo>>();
        List<IcExeclInfo> list = new ArrayList<IcExeclInfo>();

        StringBuffer querySQl = new StringBuffer(" ");
        querySQl.append(" SELECT B.ISWITCHDES ,C.* ");
        querySQl.append("  FROM  ");
        if (null != ias && !"".equals(ias))
        {
            querySQl
                    .append("     ( SELECT  IID ,ISWITCHID FROM  IEAI_INSTANCE_VERSION W WHERE W.IINSTANCENAME =? AND W.IVERSIONALIAS='"
                            + ias + "' AND W.IVERSION=" + version + "  AND ISYSTYPE=2) A ");
        } else
        {
            querySQl
                    .append("     ( SELECT  IID ,ISWITCHID FROM  IEAI_INSTANCE_VERSION W WHERE W.IINSTANCENAME =? AND W.IVERSION="
                            + version + "  AND ISYSTYPE=2) A ");
        }
        querySQl.append("      ,IEAI_SWITCH_DIR B ,IEAI_INSTANCEINFO C  ");
        querySQl.append("  WHERE A.ISWITCHID =B.IID  ");
        querySQl.append("    AND A.IID =C.IINSTANCEID ORDER BY B.ISWITCHDES, C.ICONNER,C.ISERNER ");

        String iswitches = null;
        IcExeclInfo susExeclInfo = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement statmt = null;
                Connection con = null;
                ResultSet rs = null;
                try
                {
                    con = DBResource.getConnection("getExelInfo", _log, Constants.IEAI_INFO_COLLECT);
                    statmt = con.prepareStatement(querySQl.toString());
                    statmt.setString(1, instNam);
                    // statmt.setString(2, ias);
                    rs = statmt.executeQuery();

                    String taskType = null;
                    while (rs.next())
                    {
                        iswitches = rs.getString("ISWITCHDES");
                        list = rsMap.get(iswitches);
                        if (null == list)
                        {
                            list = new ArrayList<IcExeclInfo>();
                            rsMap.put(iswitches, list);
                        }

                        susExeclInfo = new IcExeclInfo();
                        susExeclInfo.setActNo(rs.getDouble("ISERNER"));
                        susExeclInfo.setConNo(rs.getLong("ICONNER"));
                        susExeclInfo.setIparameter(rs.getString("IPARAMETER"));
                        susExeclInfo.setActName(rs.getString("IACTNAME"));

                        susExeclInfo.setActDes(rs.getString("IACTDES"));
                        taskType = rs.getString("IACTTYPE");
                        if ("2".equals(taskType))
                        {
                            susExeclInfo.setActtype("提醒任务");
                        } else
                            susExeclInfo.setActtype("SHELL");

                        susExeclInfo.setTaskRem(rs.getString("IREMINFO"));
                        susExeclInfo.setSwitchSysType(rs.getInt("ISWITCHSYSTYPE"));

                        susExeclInfo.setIp(rs.getString("IIP"));
                        susExeclInfo.setPort(rs.getInt("IPORT"));
                        if (rs.getInt("IPORT") != 0 && rs.getInt("IPORT") != 1500)
                        {
                            susExeclInfo.setIpAndPort(rs.getString("IIP") + ":"
                                    + rs.getInt("IPORT"));
                        } else
                        {
                            susExeclInfo.setIpAndPort(rs.getString("IIP"));
                        }
                        susExeclInfo.setPreNo(rs.getString("IPRENER"));
                        susExeclInfo.setSusType(rs.getInt("ISYSTYPE"));

                        susExeclInfo.setShellscript(rs.getString("ISHELLPATH"));
                        susExeclInfo.setParaCheck(rs.getString("IPARACHECK"));
                        susExeclInfo.setParaSwitch(rs.getString("IPARASWITCH"));
                        susExeclInfo.setParaSwitchForce(rs.getString("IPARASWITCHFORCE"));

                        susExeclInfo.setExpectInfo(rs.getString("IEXPECEINFO"));
                        susExeclInfo.setTimeOutT(rs.getInt("ITIMEOUT"));

                        susExeclInfo.setModuleType(rs.getString("IMODELTYPE"));
                        susExeclInfo.setModuleType(rs.getString("IRETNVALEXCEPION"));
                        susExeclInfo.setModuleType(rs.getString("IREDOABLE"));
                        list.add(susExeclInfo);
                    }

                    statmt = null;
                    rs = null;

                } catch (SQLException e)
                {
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, null, statmt, "getExelInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return rsMap;
    }

    /**
     * 
     * @Title: getLastListForAutoExcelRxport
     * @Description: TODO(【应用自动化变更】导出excel获取最新版本数据)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return List<Map> 返回类型
     * @throws
     * @变更记录 2016年3月22日 yunpeng_zhang
     */
    public List<Map> getLastListForAutoExcelRxport (
            IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type ) throws RepositoryException
    {
        List<Map> res = new ArrayList<Map>();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {

                    String orderString = " ORDER BY ICONNER,ISERNER \n";
                    String sqlPageString = "SELECT * FROM (SELECT T.* FROM IEAI_INSTANCEINFO T "
                            + "WHERE T.IINSTANCEID IN(SELECT   T1.IID FROM IEAI_INSTANCE_VERSION T1 WHERE  T1.ISYSTYPE=? AND  T1.IINSTANCENAME=? AND  T1.iid=(SELECT MAX(T2.iid) FROM   IEAI_INSTANCE_VERSION T2 WHERE T2.ISYSTYPE=? AND T2.IINSTANCENAME=?))  ";
                    String sqlPageStringEnd = ") A  ";
                    String sqlWhere = "";
                    conn = DBResource.getConnection("getLastListForAutoExcelRxport", _log,type);
                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd +orderString;
                    int index = 0;
                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(++index, instanceConfigBeanForQuery.getIsysType());
                    actStat.setString(++index, instanceConfigBeanForQuery
                            .getIinstanceNameForQuery());
                    actStat.setInt(++index, instanceConfigBeanForQuery.getIsysType());
                    actStat.setString(++index, instanceConfigBeanForQuery
                            .getIinstanceNameForQuery());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iserner", actRS.getLong("ISERNER"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iprener", "-1".equals(actRS.getString("IPRENER")) ? ""
                                : actRS.getString("IPRENER"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("iactDes", actRS.getString("IACTDES"));
                        returnMap.put("iactType", ACTTYPE_MAP.get(actRS.getInt("IACTTYPE")));
                        returnMap.put("iremInfo", actRS.getString("IREMINFO"));
                        returnMap.put("iip", actRS.getString("IIP"));
                        returnMap.put("iprot", actRS.getInt("IPORT"));
                        returnMap.put("isysType", SYSTYPE_MAP.get(actRS.getInt("ISYSTYPE")));
                        returnMap.put("iexecUser", actRS.getString("IEXECUSER"));
                        returnMap.put("ishellScript", actRS.getString("ISHELLSCRIPT"));
                        returnMap.put("iisloadenv", (-1 == actRS.getInt("IISLOADENV")) ? ""
                                : LOADENV_MAP.get(actRS.getInt("IISLOADENV")));
                        returnMap.put("ishellPath", actRS.getString("ISHELLPATH"));
                        returnMap.put("itimeOut", actRS.getLong("ITIMEOUT") == 0 ? "" : actRS
                                .getLong("ITIMEOUT"));
                        returnMap.put("iexpeceInfo", actRS.getString("IEXPECEINFO"));
                        returnMap.put("iisDisable", actRS.getInt("IISDISABLE") == 1 ? true : false);
                        returnMap.put("iconnerName", actRS.getString("ICONNERNAME") == null ? ""
                                : actRS.getString("ICONNERNAME"));

                        returnMap.put("moduleType", actRS.getString("IMODELTYPE") == null ? ""
                                : actRS.getString("IMODELTYPE"));
                        returnMap.put("serverAlias", actRS.getString("IIPNAME") == null ? ""
                                : actRS.getString("IIPNAME"));

                        /*
                         * returnMap.put("appDevice", actRS.getString("") == null ? "" :
                         * actRS.getString(""));
                         */
                        returnMap.put("retnValWhenException",
                            actRS.getString("IEXCEPTINFO") == null ? "" : actRS
                                    .getString("IEXCEPTINFO"));
                        returnMap.put("redoable", actRS.getString("IREDOABLE") == null ? "" : actRS
                                .getString("IREDOABLE"));

                        res.add(returnMap);
                    }

                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getLastListForAutoExcelRxport method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getLastListForAutoExcelRxport",
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getLastListForAutoExcelRxport method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return res;
    }

    /**
     * 
     * @Title: getListForAutoExcelRxport
     * @Description: TODO(【应用自动化变更】导出excel获取指定版本数据)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return List<Map> 返回类型
     * @throws
     * @变更记录 2016年3月22日 yunpeng_zhang
     */
    public List<Map> getListForAutoExcelRxport (
            IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type ) throws RepositoryException
    {
        List<Map> res = new ArrayList<Map>();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String orderString = " ORDER BY ICONNER,ISERNER \n";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString + ") AS RN,T.* FROM IEAI_INSTANCEINFO T";
                    String sqlPageStringEnd = ") A   ";
                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        // mysql
                        sqlPageString = "SELECT * FROM (SELECT T.* FROM IEAI_INSTANCEINFO T";
                        sqlPageStringEnd = orderString + ") A   ";
                    }
                    conn = DBResource.getConnection("getListForAutoExcelRxport", _log, type);
                    if (instanceConfigBeanForQuery.getIidForQuery() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCEID ="
                                + instanceConfigBeanForQuery.getIidForQuery() + "  \n";
                    }
                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                    actStat = conn.prepareStatement(sqlPageString);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iserner", actRS.getLong("ISERNER"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iprener", "-1".equals(actRS.getString("IPRENER")) ? ""
                                : actRS.getString("IPRENER"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("iactDes", actRS.getString("IACTDES"));
                        returnMap.put("iactType", ACTTYPE_MAP.get(actRS.getInt("IACTTYPE")));
                        returnMap.put("iremInfo", actRS.getString("IREMINFO"));
                        returnMap.put("iip", actRS.getString("IIP"));
                        returnMap.put("iprot", actRS.getInt("IPORT"));
                        returnMap.put("isysType", SYSTYPE_MAP.get(actRS.getInt("ISYSTYPE")));
                        returnMap.put("iexecUser", actRS.getString("IEXECUSER"));
                        returnMap.put("ishellScript", actRS.getString("ISHELLSCRIPT"));
                        returnMap.put("iisloadenv", (-1 == actRS.getInt("IISLOADENV")) ? ""
                                : LOADENV_MAP.get(actRS.getInt("IISLOADENV")));
                        returnMap.put("ishellPath", actRS.getString("ISHELLPATH"));
                        returnMap.put("itimeOut", actRS.getLong("ITIMEOUT") == 0 ? "" : actRS
                                .getLong("ITIMEOUT"));
                        returnMap.put("iexpeceInfo", actRS.getString("IEXPECEINFO"));
                        returnMap.put("iisDisable", actRS.getInt("IISDISABLE") == 1 ? true : false);
                        returnMap.put("iconnerName", actRS.getString("ICONNERNAME") == null ? ""
                                : actRS.getString("ICONNERNAME"));

                        returnMap.put("moduleType", actRS.getString("IMODELTYPE") == null ? ""
                                : actRS.getString("IMODELTYPE"));
                        returnMap.put("serverAlias", actRS.getString("IIPNAME") == null ? ""
                                : actRS.getString("IIPNAME"));

                        /*
                         * returnMap.put("appDevice", actRS.getString("") == null ? "" :
                         * actRS.getString(""));
                         */
                        returnMap.put("retnValWhenException",
                            actRS.getString("IEXCEPTINFO") == null ? "" : actRS
                                    .getString("IEXCEPTINFO"));
                        returnMap.put("redoable", actRS.getString("IREDOABLE") == null ? "" : actRS
                                .getString("IREDOABLE"));

                        res.add(returnMap);
                    }

                } catch (SQLException e)
                {
                    e.printStackTrace();
                    _log.error("getListForAutoExcelRxport method of InstanceConfigManager.class SQLException:"
                            + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat,
                        "getListForAutoExcelRxport", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log.error("getListForAutoExcelRxport method of InstanceConfigManager.class RepositoryException:"
                        + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return res;
    }

    /**
     * 
     * @Title: getInstanceBasicInfoList
     * @Description: TODO(【EM】获取基础信息列表)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年4月20日 yunpeng_zhang
     */
    public Map getInstanceBasicInfoList ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {

                    String orderString = " ORDER BY IID \n";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString + ") AS RN,T.* FROM IEAI_SUS_BASIC_INFO T";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        sqlPageString = "SELECT * FROM (SELECT T.* FROM IEAI_SUS_BASIC_INFO T";
                        sqlPageStringEnd = ") A limit ?, ?  ";
                    }
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SUS_BASIC_INFO T ";
                    conn = DBResource.getConnection("getInstanceBasicInfoList", _log,type);
                    if (instanceConfigBeanForQuery.getIidForQuery() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCEID ="
                                + instanceConfigBeanForQuery.getIidForQuery() + "  \n";
                    }

                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                        sqlCount = sqlCount + sqlWhere;
                    } else
                    {
                        sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                        sqlCount = sqlCount + sqlWhere;
                    }

                    actStat = conn.prepareStatement(sqlPageString);
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        actStat.setInt(1, instanceConfigBeanForQuery.getStart());
                        actStat.setInt(2, instanceConfigBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(1,
                            instanceConfigBeanForQuery.getStart() + instanceConfigBeanForQuery.getLimit());
                        actStat.setInt(2, instanceConfigBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();

                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iparameter", actRS.getString("IPARAMETER"));
                        returnMap.put("iparaValue", actRS.getString("IPARAVALUE"));
                        returnMap.put("ides", actRS.getString("IDES"));

                        res.add(returnMap);
                    }
                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceBasicInfoList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceBasicInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceBasicInfoList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: getInstanceEnvInfoList
     * @Description: TODO(【变更管理】获取环境信息列表)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年4月20日 yunpeng_zhang
     */
    public Map getInstanceEnvInfoList ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;

        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {

                    String orderString = " ORDER BY IID \n";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString + ") AS RN,T.* FROM IEAI_SUS_ENV_INFO T";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_SUS_ENV_INFO T ";
                    conn = DBResource.getConnection("getInstanceEnvInfoList", _log, Constants.IEAI_INFO_COLLECT);
                    if (instanceConfigBeanForQuery.getIidForQuery() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCEID ="
                                + instanceConfigBeanForQuery.getIidForQuery() + "  \n";
                    }

                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(1, instanceConfigBeanForQuery.getStart()
                            + instanceConfigBeanForQuery.getLimit());
                    actStat.setInt(2, instanceConfigBeanForQuery.getStart());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iip", actRS.getString("IIP"));
                        returnMap.put("iport", actRS.getInt("IPORT"));
                        returnMap.put("iipName", actRS.getString("IIPNAME"));
                        returnMap.put("imoduleType", actRS.getString("IMODULETYPE"));
                        returnMap.put("ides", actRS.getString("IDES"));

                        res.add(returnMap);
                    }
                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceEnvInfoList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceEnvInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceEnvInfoList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: getInstanceInsInfoList
     * @Description: TODO(【变更管理】获取系统信息列表)
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年1月14日 yunpeng_zhang
     */
    public Map getInstanceInsInfoList ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Map dicMap = new HashMap();

        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {
                    long startTime = System.currentTimeMillis();
                    String orderString = " ORDER BY ICONNER,ISERNER ";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString + ") AS RN,T.* FROM IEAI_INSTANCEINFO T";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        // 支持mysql
                        sqlPageString = "SELECT * FROM (SELECT T.* FROM IEAI_INSTANCEINFO T";
                        sqlPageStringEnd = ") A limit ?, ?  ";
                    }
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_INSTANCEINFO T ";
                    conn = DBResource.getConnection("getInstanceInsInfoList", _log,type);
                    if (instanceConfigBeanForQuery.getIidForQuery() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCEID ="
                                + instanceConfigBeanForQuery.getIidForQuery() + " ";
                    }
                    if (instanceConfigBeanForQuery.getIiactNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIiactNameForQuery()))
                    {
                        
                        String iactName=instanceConfigBeanForQuery.getIiactNameForQuery();
                        if (iactName.contains("_")|| iactName.contains("%"))
                        {
                            iactName = iactName.replaceAll("_", "/_");
                            iactName = iactName.replaceAll("%", "/%");
                        }
                        sqlWhere = sqlWhere + " AND T.IACTNAME LIKE '%"
                                + iactName + "%'  ESCAPE '/' ";
                        
                        
                    }
                    if (instanceConfigBeanForQuery.getIisDisable() != null
                            && !"".equals(instanceConfigBeanForQuery.getIisDisable()))
                    {
                        sqlWhere = sqlWhere + " AND T.IISDISABLE ="
                                + instanceConfigBeanForQuery.getIisDisable() + " ";
                    }

                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        // mysql
                        sqlPageString = sqlPageString + sqlWhere + orderString + sqlPageStringEnd;
                        sqlCount = sqlCount + sqlWhere;
                    } else
                    {
                        sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                        sqlCount = sqlCount + sqlWhere;
                    }

                    actStat = conn.prepareStatement(sqlPageString);
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        // mysql
                        actStat.setInt(1, instanceConfigBeanForQuery.getStart());
                        actStat.setInt(2, instanceConfigBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(1,
                            instanceConfigBeanForQuery.getStart() + instanceConfigBeanForQuery.getLimit());
                        actStat.setInt(2, instanceConfigBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        dicMap.put(Long.toString(actRS.getLong("ISERNER")), actRS
                                .getString("IACTNAME"));
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iserner", actRS.getLong("ISERNER"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iconnerName", actRS.getString("ICONNERNAME"));
                        returnMap.put("iprener", "-1".equals(actRS.getString("IPRENER")) ? ""
                                : actRS.getString("IPRENER"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("iactDes", actRS.getString("IACTDES"));
                        returnMap.put("iactType", ACTTYPE_MAP.get(actRS.getInt("IACTTYPE")));
                        returnMap.put("iremInfo", actRS.getString("IREMINFO"));
                        returnMap.put("ipName", actRS.getString("IIPNAME"));
                        returnMap.put("imodelType", actRS.getString("IMODELTYPE"));
                        returnMap.put("iip", actRS.getString("IIP"));
                        returnMap.put("iport", actRS.getInt("IPORT"));
                        returnMap.put("isysType", SYSTYPE_MAP.get(actRS.getInt("ISYSTYPE")));
                        returnMap.put("iexecUser", actRS.getString("IEXECUSER"));
                        returnMap.put("ishellScript", actRS.getString("ISHELLSCRIPT"));
                        returnMap.put("iisloadenv", LOADENV_MAP.get(actRS.getInt("IISLOADENV")));
                        returnMap.put("ishellPath", actRS.getString("ISHELLPATH"));
                        returnMap.put("itimeOut", actRS.getLong("ITIMEOUT") == 0 ? "" : actRS
                                .getLong("ITIMEOUT"));
                        returnMap.put("iparameter", actRS.getString("IPARAMETER"));
                        returnMap.put("iexpeceInfo", actRS.getString("IEXPECEINFO"));
                        returnMap.put("iexceptinfo", actRS.getString("IEXCEPTINFO"));
                        returnMap.put("iredoable", REDOABLE_MAP.get(actRS.getInt("IREDOABLE")));
                        returnMap.put("iisDisable", actRS.getInt("IISDISABLE") == 1 ? true : false);

                        res.add(returnMap);
                    }

                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    // 将依赖步骤标识转换为自步骤名称显示
                    for (int z = 0; z < res.size(); z++)
                    {
                        String iprenerString = "";
                        Map rMap = (Map) res.get(z);
                        String iprener = (String) rMap.get("iprener");
                        if (null != iprener && !"".equals(iprener))
                        {
                            String[] iprenerItem = iprener.split(",");
                            int index = 0;//没有被禁用的个数
                            for (int x = 0; x < iprenerItem.length; x++)
                            {
                                //begin.updated by manxi_zhao.2016-06-24.
//                                if (x > 0)
//                                {
//                                    iprenerString += ",";
//                                }
//                                iprenerString += dicMap.get(iprenerItem[x]);
                                //不拼接，禁用步骤名称--修正当有禁用步骤被依赖时，依赖名称中出现null的情况(null,null,ABCD)
                                if(null!=dicMap.get(iprenerItem[x]) && !"".equals(dicMap.get(iprenerItem[x]))){
                                    if (index>0)
                                    {
                                        iprenerString += ",";
                                    }
                                    iprenerString += dicMap.get(iprenerItem[x]);
                                    index ++;
                                }
                                //end..updated by manxi_zhao.2016-06-24.
                            }
                        }
                        rMap.put("iprenerString", iprenerString);
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                    long endTime = System.currentTimeMillis();
                    Calendar c = Calendar.getInstance();
                    c.setTimeInMillis(endTime - startTime);
//                    _log.info("method getInstanceInsInfoList() 耗时: " + c.get(Calendar.MINUTE)
//                            + "分 " + c.get(Calendar.SECOND) + "秒 " + c.get(Calendar.MILLISECOND)
//                            + " 毫秒" + "\n {sql:" + sqlPageString + "}");
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceInsInfoList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceInsInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceInsInfoList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2016年5月27日 
     * @param instanceConfigBeanForQuery
     * @param type
     * @return
     * @throws RepositoryException
     * return Map
     */
    public Map getEmInstanceForComBox ( IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type )
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {
                    String orderString = " ORDER BY ITIME DESC\n";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT  T.IVERSIONALIAS,MAX(T.IID) AS IID ,MAX(T.ITIME) AS ITIME FROM  IEAI_INSTANCE_VERSION T";
                    conn = DBResource.getConnection("getSusInstanceForComBox", _log,type);

                    // 子系统名称
                    if (instanceConfigBeanForQuery.getIinstanceNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIinstanceNameForQuery()))
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCENAME = '"
                                + instanceConfigBeanForQuery.getIinstanceNameForQuery() + "'";
                    }
                    // 类型
                    if (instanceConfigBeanForQuery.getIsysType() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.ISYSTYPE ="
                                + instanceConfigBeanForQuery.getIsysType() + "  \n";
                    }

                    String sqlGroupBy=" GROUP BY T.IVERSIONALIAS ";
                    sqlPageString = sqlPageString + sqlWhere +sqlGroupBy+ orderString;

                    actStat = conn.prepareStatement(sqlPageString);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("versionString", actRS.getString("IVERSIONALIAS"));
//                        returnMap.put("versionString", (actRS.getLong("ITIME") != 0 ? sdf
//                                .format(new Date(actRS.getLong("ITIME"))) : "")
//                                + "【" + actRS.getString("IVERSIONALIAS") + "】");
                        res.add(returnMap);
                    }

                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getSusInstanceForComBox method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getSusInstanceForComBox", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getSusInstanceForComBox method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    /**
     * 
     * @Title: updateInstanceBasicInfo
     * @Description: TODO(【EM】修改基础信息)
     * @param userLoginName
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年4月20日 yunpeng_zhang
     */
    public boolean updateInstanceBasicInfo ( List<Map<String, Object>> instanceInfos ,int  type )
            throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement ps = null;
        if (null != instanceInfos && instanceInfos.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        String sql = "UPDATE IEAI_SUS_BASIC_INFO T SET T.IPARAVALUE=? WHERE T.IID=?";
                        conn = DBResource.getConnection("updateInstanceBasicInfo", _log, type);
                        ps = conn.prepareStatement(sql);
                        Long iinstanceIdFinal = 0L;
                        for (int z = 0; z < instanceInfos.size(); z++)
                        {
                            Map<String, Object> bsMap = instanceInfos.get(z);
                            Long iinstanceId = (Long) Long.parseLong(bsMap.get("iinstanceId")
                                    .toString());
                            iinstanceIdFinal = iinstanceId;
                            Long iid = (Long) Long.parseLong(bsMap.get("iid").toString());
                            String iparaValue = bsMap.get("iparaValue").toString().trim();

                            int index = 0;
                            ps.setString(++index, iparaValue);
                            ps.setLong(++index, iid);

                            ps.addBatch();

                        }
                        ps.executeBatch();
                        // 主表编辑版本号自增
                        this.updateInstanceVersion(conn, iinstanceIdFinal);
                        conn.commit();

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                            "updateInstanceBasicInfo", _log);
                        // TODO Auto-generated catch block
                        _log
                                .error("updateInstanceBasicInfo method of InstanceConfigManager.class SQLException:"
                                        + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    } finally
                    {
                        DBResource.closePSConn(conn, ps, "updateInstanceBasicInfo", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log
                            .error("updateInstanceBasicInfo method of InstanceConfigManager.class RepositoryException:"
                                    + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                }
            }
        }

        return returnValue;
    }

    /**
     * 
     * @Title: updateInstanceEvnInfo
     * @Description: TODO(【变更管理】修改环境信息)
     * @param userLoginName
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年4月20日 yunpeng_zhang
     */
    public boolean updateInstanceEvnInfo ( List<Map<String, Object>> instanceInfos )
            throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement ps = null;
        if (null != instanceInfos && instanceInfos.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {
                try
                {
                    try
                    {

                        String sql = "UPDATE IEAI_SUS_ENV_INFO T SET  T.IIP=?, T.IPORT=?, T.IIPNAME=?, T.IMODULETYPE=? WHERE T.IID=?";
                        conn = DBResource.getConnection("updateInstanceEvnInfo", _log, Constants.IEAI_INFO_COLLECT);
                        ps = conn.prepareStatement(sql);
                        Long iinstanceIdFinal = 0L;
                        for (int z = 0; z < instanceInfos.size(); z++)
                        {
                            Map<String, Object> bsMap = instanceInfos.get(z);
                            Long iid = (Long) Long.parseLong(bsMap.get("iid").toString());
                            Long iinstanceId = (Long) Long.parseLong(bsMap.get("iinstanceId")
                                    .toString());
                            iinstanceIdFinal = iinstanceId;
                            String iip = bsMap.get("iip").toString().trim();
                            String iport = bsMap.get("iport").toString().trim();
                            int iportValue = Integer.parseInt(null != iport ? iport : "0");
                            String iipName = bsMap.get("iipName").toString().trim();
                            String imoduleType = bsMap.get("imoduleType").toString().trim();

                            int index = 0;
                            ps.setString(++index, iip);
                            ps.setInt(++index, iportValue);
                            ps.setString(++index, iipName);
                            ps.setString(++index, imoduleType);
                            ps.setLong(++index, iid);

                            ps.addBatch();

                        }
                        ps.executeBatch();
                        // 主表编辑版本号自增
                        this.updateInstanceVersion(conn, iinstanceIdFinal);
                        conn.commit();

                    } catch (SQLException e)
                    {
                        returnValue = false;
                        DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                            "updateInstanceEvnInfo", _log);
                        // TODO Auto-generated catch block
                        _log
                                .error("updateInstanceEvnInfo method of InstanceConfigManager.class SQLException:"
                                        + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                    } finally
                    {
                        DBResource.closePSConn(conn, ps, "updateInstanceEvnInfo", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    _log
                            .error("updateInstanceEvnInfo method of InstanceConfigManager.class RepositoryException:"
                                    + ex.getMessage());
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                }
            }
        }

        return returnValue;
    }

    /**
     * 
     * @Title: updateInstanceInsInfo
     * @Description: TODO(【EM】修改系统信息)
     * @param userLoginName
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年4月20日 yunpeng_zhang
     */
    public boolean updateInstanceInsInfo ( List<Map<String, Object>> instanceInfos,
            String userLoginName ) throws RepositoryException
    {
        boolean returnValue = true;
        Connection conn = null;
        PreparedStatement actStat = null;
        if (null != instanceInfos && instanceInfos.size() > 0)
        {
            for (int i = 0; i < 10; i++)
            {

                try
                {

                    String sql = "UPDATE IEAI_INSTANCEINFO SET   IPRENER=?,   IACTTYPE=?, IREMINFO=?, IIPNAME=?, IMODELTYPE=?, ISYSTYPE=?, IEXECUSER=?, ISHELLSCRIPT=?, IISLOADENV=?, ISHELLPATH=?,   IEXPECEINFO=?, IEXCEPTINFO=?,IREDOABLE=?, IISDISABLE=?, IIP=?  WHERE IID=?";
                    conn = DBResource.getConnection("updateInstanceInsInfo", _log, Constants.IEAI_INFO_COLLECT);
                    actStat = conn.prepareStatement(sql);
                    Long iinstanceIdFinal = 0L;
                    for (int z = 0; z < instanceInfos.size(); z++)
                    {
                        Map<String, Object> bsMap = instanceInfos.get(z);
                        Long iid = (Long) Long.parseLong(bsMap.get("iid").toString());
                        Long iinstanceId = (Long) Long.parseLong(bsMap.get("iinstanceId")
                                .toString());
                        iinstanceIdFinal = iinstanceId;
                        Long iserner = (Long) Long.parseLong(bsMap.get("iserner").toString());

                        Long iconner = (Long) Long.parseLong(bsMap.get("iconner").toString());
                        String iprener = bsMap.get("iprener").toString().trim();
                        Integer iactType = this.getMapKey(bsMap.get("iactType").toString(),
                            ACTTYPE_MAP);
                        String iremInfo = bsMap.get("iremInfo").toString().trim();
                        String iipName = bsMap.get("ipName").toString().trim();
                        String imodeltype = bsMap.get("imodelType").toString().trim();
                        Integer isysType = this.getMapKey(bsMap.get("isysType").toString(),
                            SYSTYPE_MAP);
                        String iexecUser = bsMap.get("iexecUser").toString().trim();
                        String ishellScript = bsMap.get("ishellScript").toString().trim();
                        Integer iisloadenv = this.getMapKey(bsMap.get("iisloadenv").toString(),
                            LOADENV_MAP);
                        String ishellPath = bsMap.get("ishellPath").toString().trim();
                        String iexpeceInfo = bsMap.get("iexpeceInfo").toString().trim();
                        String iexceptinfo = bsMap.get("iexceptinfo").toString().trim();
                        String iip = bsMap.get("iip").toString().trim();
//                        System.out.println("iip: "+iip);
                        Integer iredoable = this.getMapKey(bsMap.get("iredoable").toString(),
                            REDOABLE_MAP);
                        Boolean iisDisable = (Boolean) bsMap.get("iisDisable");
                        // TODO : 需要调整
                        boolean checkreturnValue = this.checkIprenerForEM(conn, iinstanceId,
                            iconner, iserner, iprener);
                        // 判断依赖关系是否填写规范
                        if (!checkreturnValue)
                        {
                            returnValue = false;
                            ServerError serverError = new ServerError();
                            serverError.setErrDetail("依赖关系内容填写不符合规范！");
                            RepositoryException repositoryException = new RepositoryException(
                                    serverError);
                            DBResource.rollback(conn, ServerError.ERR_DB_UPDATE,
                                repositoryException, "updateInstanceInsInfo", _log);
                            // TODO Auto-generated catch block
                            throw repositoryException;
                        }
                        int index = 0;
                        actStat.setString(++index, "".equals(iprener) ? "-1" : iprener);
                        actStat.setInt(++index, iactType);
                        actStat.setString(++index, iremInfo);
                        actStat.setString(++index, iipName);
                        actStat.setString(++index, imodeltype);
                        actStat.setInt(++index, isysType);
                        actStat.setString(++index, iexecUser);
                        actStat.setString(++index, ishellScript);
                        actStat.setInt(++index, iisloadenv);
                        actStat.setString(++index, ishellPath);
                        actStat.setString(++index, iexpeceInfo);
                        actStat.setString(++index, iexceptinfo);
                        actStat.setInt(++index, iredoable);
                        actStat.setInt(++index, iisDisable == true ? 1 : 0);
                        actStat.setString(++index, iip);
                        actStat.setLong(++index, iid);
                        actStat.addBatch();
                        _log.info("updateInstanceInfo {iid=" + iid + "} by user:" + userLoginName);
                    }
                    actStat.executeBatch();
                    // 主表编辑版本号自增
//                    this.updateInstanceVersion(conn, iinstanceIdFinal);
                    conn.commit();

                } catch (SQLException e)
                {
                    returnValue = false;
                    DBResource.rollback(conn, ServerError.ERR_DB_UPDATE, e,
                        "updateInstanceInsInfo", _log);
                    // TODO Auto-generated catch block
                    _log
                            .error("updateInstanceInsInfo method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                } finally
                {
                    DBResource.closePSConn(conn, actStat, "updateInstanceInsInfo", _log);
                }
                break;
            }
        }

        return returnValue;
    }

    /**
     * 
     * @Title: updateInstanceVersion
     * @Description: TODO(【变更管理】编辑版本号自增)
     * @param conn
     * @param instanceId
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年4月22日 yunpeng_zhang
     */
    public boolean updateInstanceVersion ( Connection conn, long instanceId )
            throws RepositoryException
    {
        boolean returnValue = true;
        PreparedStatement ps = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                try
                {

                    String sql = "UPDATE IEAI_SUS_INSTANCE_VERSION SET IEDITVERSION=(IEDITVERSION+1) WHERE IID=?";
                    ps = conn.prepareStatement(sql);
                    int index = 0;
                    ps.setLong(++index, instanceId);
                    ps.execute();

                } catch (SQLException e)
                {
                    returnValue = false;
                    _log
                            .error("updateInstanceVersion method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                } finally
                {
                    DBResource.closePreparedStatement(ps, "updateInstanceVersion", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("updateInstanceVersion method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: checkIprenerForSUS
     * @Description: TODO(【变更管理】判断依赖关系是否正确)
     * @param conn
     * @param instanceId
     * @param conner
     * @param serner
     * @param iprener
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年4月21日 yunpeng_zhang
     */
    private boolean checkIprenerForEM ( Connection conn, Long instanceId, Long conner,
            Long serner, String iprener ) throws RepositoryException
    {
        boolean returnValue = true;
        List<Long> sernerList = new ArrayList<Long>();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        if ("".equals(iprener))
        {
            return returnValue;
        }
        String sql = "SELECT T.ISERNER FROM IEAI_INSTANCEINFO T WHERE T.IINSTANCEID=? AND T.ICONNER=? AND T.ISERNER!=?";
        try
        {
            actStat = conn.prepareStatement(sql);
            int index = 0;
            actStat.setLong(++index, instanceId);
            actStat.setLong(++index, conner);
            actStat.setLong(++index, serner);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                sernerList.add(actRS.getLong("ISERNER"));
            }
        } catch (SQLException e)
        {
            // TODO Auto-generated catch block
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, "checkIprenerForSUS", _log);
        }

        String[] pre = iprener.split(",");
        for (int ii = 0; ii < pre.length; ii++)
        {
            try
            {
                long newserner = Long.parseLong(pre[ii]);
                if (newserner == -1 || sernerList.contains(newserner))
                {
                    continue;
                } else
                {
                    returnValue = false;
                    break;
                }

            } catch (Exception ee)
            {
                returnValue = false;
            }
        }
        return returnValue;
    }

    public Map getSwitchPreview ( String instanceName, String versionInfo, String switchdes,
            String switchtype ) throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        String version = "";
        String versionAlias = "";
        if (!"".equals(versionInfo))
        {
            String[] v = versionInfo.split("##!!@@!!##");
            version = v[0];
            versionAlias = v[1];
        }
        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {

                    String orderString = " ORDER BY ICONNER,ISERNER \n";
                    String sqlWhere = " where A.ISWITCHID = B.IID and T.IID > 0 and T.IINSTANCEID = A.IID";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString
                            + ") AS RN,T.IID,T.ICONNER,T.ISERNER,B.ISWITCHDES,T.IACTNAME,T.ISHELLPATH"
                            + " FROM IEAI_INSTANCEINFO T,IEAI_INSTANCE_VERSION A left join IEAI_SWITCH_DIR B on A.ISWITCHID=B.IID"
                            + "";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM IEAI_INSTANCEINFO T,IEAI_INSTANCE_VERSION A left join IEAI_SWITCH_DIR B on A.ISWITCHID=B.IID ";
                    conn = DBResource.getConnection("getInstanceInfoList", _log, Constants.IEAI_INFO_COLLECT);
                    if (instanceName != null && !"".equals(instanceName))
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCENAME ='" + instanceName + "' \n";
                    }
                    if (!"".equals(versionAlias))
                    {
                        sqlWhere = sqlWhere + " AND A.IVERSIONALIAS LIKE '%" + versionAlias
                                + "%'  \n";
                    }
                    if (!"".equals(version))
                    {
                        sqlWhere = sqlWhere + " AND A.IVERSION = " + version;
                    }
                    if (switchdes != null && !"".equals(switchdes))
                    {
                        sqlWhere = sqlWhere + " AND B.ISWITCHDES = '" + switchdes + "'";
                    }

                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;
                    actStat = conn.prepareStatement(sqlPageString);
                    actStat.setInt(1, 50);
                    actStat.setInt(2, 0);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iserner", actRS.getDouble("ISERNER"));
                        returnMap.put("iswitchdes", actRS.getString("ISWITCHDES"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("ishellpath", actRS.getString("ISHELLPATH") + " "
                                + switchtype);
                        res.add(returnMap);
                    }

                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceInfoList method of InstanceConfigManager.class SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceInfoList method of InstanceConfigManager.class RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }

    public int selVersions ( String instanceName, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        String sql = "select  count( distinct IVERSION) COU from ieai_instance_version where IINSTANCENAME = ?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, instanceName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt(1);
            }
        } catch (SQLException e)
        {
            _log.error("selVersions method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            e.printStackTrace();
        } finally
        {
            try
            {
                if (rs != null)
                {
                    rs.close();
                }
                if (ps != null)
                {
                    ps.close();
                }
            } catch (Exception e2)
            {
                e2.printStackTrace();
            }
        }
        return count;
    }

    // 数据删除并插入新表
    public void delVersions ( String instanceName, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        boolean isversion = true;
        String insql = "";
        String delsql = "";

        try
        {
            insql = "insert into IEAI_INSTANCE_VERSION_HIS (select * from ieai_instance_version where iversion = (select min(IVERSION) numm from ieai_instance_version where iinstancename = ?) AND iinstancename = ?)";
            delsql = "delete from ieai_instance_version b where b.iid in (select iid from ieai_instance_version a where a.iversion = (select min(IVERSION) numm from ieai_instance_version where iinstancename = ?) AND iinstancename = ?)";
            ps = conn.prepareStatement(insql);
            ps.setString(1, instanceName);
            ps.setString(2, instanceName);
            ps1 = conn.prepareStatement(delsql);
            ps1.setString(1, instanceName);
            ps1.setString(2, instanceName);
            ps.executeUpdate();
            ps1.executeUpdate();

            ps = null;
            ps1 = null;
            insql = "insert into ieai_instanceinfo_his (select * from ieai_instanceinfo a where not exists (select * from ieai_instance_version b where a.iinstanceid = b.iid ))";
            delsql = "delete from ieai_instanceinfo c where c.iinstanceid in ( select iinstanceid from ieai_instanceinfo a where not exists (select iid from ieai_instance_version b where a.iinstanceid = b.iid ))";
            ps = conn.prepareStatement(insql);
            ps1 = conn.prepareStatement(delsql);
            ps.executeUpdate();
            ps1.executeUpdate();
        } catch (SQLException e)
        {
            isversion = false;
            _log.error("delVersions method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            try
            {
                if (ps != null)
                {
                    ps.close();
                }
                if (ps1 != null)
                {
                    ps1.close();
                }
            } catch (Exception e2)
            {
                e2.printStackTrace();
            }
        }
    }

    // 查询出数据
    public List selInstanceVersion ( String instanceName, String version, Connection conn )
            throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "";
        List listInfo = new ArrayList();
        try
        {
            sql = "select * from IEAI_INSTANCE_VERSION where IINSTANCENAME = ? AND IVERSION = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, instanceName);
            ps.setString(2, version);
            rs = ps.executeQuery();

            while (rs.next())
            {
                SusSysInfo susSysInfo = new SusSysInfo();
                susSysInfo.setIid(rs.getLong("IID"));
                susSysInfo.setInsName(rs.getString("IINSTANCENAME"));
                susSysInfo.setIversionalias(rs.getString("IVERSIONALIAS"));
                susSysInfo.setIsystype(rs.getInt("ISYSTYPE"));
                susSysInfo.setIsysid(rs.getLong("ISYSID"));
                susSysInfo.setIswitchid(rs.getLong("ISWITCHID"));
                susSysInfo.setIieaisysid(rs.getLong("IIEAISYSID"));
                listInfo.add(susSysInfo);
            }
        } catch (SQLException e)
        {
            _log.error("selInstanceVersion method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            try
            {
                if (rs != null)
                {
                    rs.close();
                }
                if (ps != null)
                {
                    ps.close();
                }
            } catch (Exception e2)
            {
                e2.printStackTrace();
            }
        }
        return listInfo;
    }

    // 插入数据
    public long insertInstanceVersion ( SusSysInfo susSysInfo, String instanceName,
            long newVersion, Connection conn, String userName ) throws RepositoryException
    {
        long mainInsId = 0;
        String sql = "";
        PreparedStatement ps = null;
        try
        {
            mainInsId = IdGenerator.createId("IEAI_INSTANCE_VERSION", conn);
            sql = "INSERT INTO IEAI_INSTANCE_VERSION(IID, IINSTANCENAME, IVERSION, IVERSIONALIAS, ISYSTYPE, IDES, ISYSID, ITIME, ISWITCHID, IIEAISYSID)  VALUES(?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8),?,?)";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, mainInsId);
            ps.setString(2, susSysInfo.getInsName());
            ps.setLong(3, newVersion);
            ps.setString(4, susSysInfo.getIversionalias());
            ps.setInt(5, susSysInfo.getIsystype());
            ps.setString(6, "");
            ps.setLong(7, susSysInfo.getIsysid());
            ps.setLong(8, susSysInfo.getIswitchid());
            ps.setLong(9, susSysInfo.getIieaisysid());
            ps.executeUpdate();
            _log.info("insertInstanceVersion {id = " + mainInsId + ",instanceName=" + instanceName
                    + ",version =" + newVersion + "} by user:" + userName);
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            _log.error("insertInstanceVersion method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            try
            {
                if (ps != null)
                {
                    ps.close();
                }
            } catch (Exception e2)
            {
                e2.printStackTrace();
            }
        }
        return mainInsId;
    }

    public void insertInstanceInfo ( long instanceId, long mainInsId, List infolist, Connection conn )
            throws RepositoryException
    {
        String sql = "";
        PreparedStatement ps = null;
        try
        {
            for (int j = 0; j < infolist.size(); j++)
            {
                IcExeclInfo susExeclInfo = (IcExeclInfo) infolist.get(j);
                sql = "INSERT INTO IEAI_INSTANCEINFO(IID, IINSTANCEID, IINSTANCENAME, ISERNER, ICONNER, IPRENER, IACTNAME, IACTDES, IACTTYPE, IREMINFO, IIP, IPORT, ISYSTYPE, ISHELLPATH, ITIMEOUT, ISWITCHSYSTYPE, IPARAMETER, IPARACHECK, IPARASWITCH, IPARASWITCHFORCE,IEXPECEINFO,IEXECUSER,ISHELLSCRIPT,IISLOADENV,IISDISABLE)"
                        + "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?)";
                ps = conn.prepareStatement(sql);
                if (instanceId == susExeclInfo.getIinstanceid())
                {
                    ps.setLong(1, IdGenerator.createId("IEAI_INSTANCEINFO", conn));
                    ps.setLong(2, mainInsId);
                    ps.setString(3, susExeclInfo.getIinstanceName());
                    ps.setDouble(4, susExeclInfo.getActNo());
                    ps.setLong(5, susExeclInfo.getConNo());
                    ps.setString(6, susExeclInfo.getPreNo());
                    ps.setString(7, susExeclInfo.getActName());
                    ps.setString(8, susExeclInfo.getActDes());
                    ps.setInt(9, susExeclInfo.getTaskType());
                    ps.setString(10, susExeclInfo.getTaskRem());
                    ps.setString(11, susExeclInfo.getIp());
                    ps.setInt(12, susExeclInfo.getPort() != 0 ? susExeclInfo.getPort() : 1500);
                    ps.setInt(13, susExeclInfo.getSusType());
                    ps.setString(14, susExeclInfo.getShellPath());
                    ps.setInt(15, susExeclInfo.getTimeOutT());
                    ps.setInt(16, susExeclInfo.getSwitchSysType());
                    ps.setString(17, susExeclInfo.getShellPara());
                    ps.setString(18, susExeclInfo.getParaCheck());
                    ps.setString(19, susExeclInfo.getParaSwitch());
                    ps.setString(20, susExeclInfo.getParaSwitchForce());
                    ps.setString(21, susExeclInfo.getExpectInfo());
                    ps.setString(22, susExeclInfo.getExecuser());
                    ps.setString(23, susExeclInfo.getShellscript());
                    ps.setInt(24, susExeclInfo.getIsLoadEnv());
                    ps.setInt(25, susExeclInfo.getIsdisable());
                    ps.executeUpdate();
                }
            }
        } catch (SQLException e)

        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            _log.error("insertInstanceInfo method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            try
            {
                if (ps != null)
                {
                    ps.close();
                }
            } catch (Exception e2)
            {
                e2.printStackTrace();
            }
        }
    }

    public List updateInstanceInfo ( String instanceName, String version,
            List<Map<String, Object>> infoData, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "";

        List list = new ArrayList();

        try
        {
            sql = "UPDATE IEAI_INSTANCEINFO T SET T.ICONNER=?,T.IACTDES=?,T.IREMINFO=?,T.IPRENER=?,T.IIP=?,T.IPORT=?,T.IEXECUSER=?,T.ISHELLSCRIPT=?,T.ISHELLPATH=?,T.IEXPECEINFO=?,T.IACTTYPE=?,T.ISYSTYPE=?,T.IISLOADENV=?,T.ITIMEOUT=?,T.ISWITCHSYSTYPE=?,T.IPARACHECK=?,T.IPARASWITCH=?,T.IPARASWITCHFORCE=?,T.IISDISABLE=?, T.ISERNER=?, T.IACTNAME=?  WHERE T.IID=?";
            ps = conn.prepareStatement(sql);
            for (int z = 0; z < infoData.size(); z++)
            {
                Map<String, Object> bsMap = infoData.get(z);
                Long iinstanceId = (Long) Long.parseLong(bsMap.get("iinstanceId").toString());
                Double iserner = (Double) Double.parseDouble(bsMap.get("iserner").toString());
                Long iconner = (Long) Long.parseLong(bsMap.get("iconner").toString());
                String iactName = bsMap.get("iactName").toString().trim();
                String iactDes = bsMap.get("iactDes").toString().trim();
                String iremInfo = bsMap.get("iremInfo").toString().trim();
                String iprener = bsMap.get("iprener").toString().trim();
                String iip = bsMap.get("iip").toString().trim();
                String[] iipAndPort = iip.split(":");
                Integer iport = null;
                if (iipAndPort.length > 1)
                {
                    String portString = iipAndPort[1];
                    iport = Integer.parseInt(portString);
                    iip = iipAndPort[0];
                } else
                {
                    iport = Integer.parseInt(bsMap.get("iprot").toString());
                }

                String iexecUser = bsMap.get("iexecUser").toString().trim();
                String ishellScript = bsMap.get("ishellScript").toString().trim();
                String ishellPath = bsMap.get("ishellPath").toString().trim();
                String iexpeceInfo = bsMap.get("iexpeceInfo").toString().trim();
                String iparaCheck = bsMap.get("iparaCheck").toString().trim();
                String iparaSwitch = bsMap.get("iparaSwitch").toString().trim();
                String iparaSwitchforce = bsMap.get("iparaSwitchforce").toString().trim();
                Boolean iisDisable = (Boolean) bsMap.get("iisDisable");
                Integer iactType = this.getMapKey(bsMap.get("iactType").toString(), ACTTYPE_MAP);
                Integer isysType = this.getMapKey(bsMap.get("isysType").toString(), SYSTYPE_MAP);
                Integer iisloadenv = this
                        .getMapKey(bsMap.get("iisloadenv").toString(), LOADENV_MAP);
                Integer iswitchsysType = this.getMapKey(bsMap.get("iswitchSysType").toString(),
                    HDTYPE_MAP);
                Long iid = (Long) Long.parseLong(bsMap.get("iid").toString());

                String itimeOutString = "0";
                if (null != bsMap.get("itimeOut")
                        && !"null".equals(bsMap.get("itimeOut").toString())
                        && !"".equals(bsMap.get("itimeOut").toString()))
                {
                    itimeOutString = bsMap.get("itimeOut").toString();
                }
                Long itimeOut = (Long) Long.parseLong(itimeOutString);

                int index = 0;
                ps.setLong(++index, iconner);
                ps.setString(++index, iactDes);
                ps.setString(++index, iremInfo);
                ps.setString(++index, "".equals(iprener) ? "-1" : iprener);
                ps.setString(++index, iip);
                ps.setInt(++index, iport);
                ps.setString(++index, iexecUser);
                ps.setString(++index, ishellScript);
                ps.setString(++index, ishellPath);
                ps.setString(++index, iexpeceInfo);
                ps.setInt(++index, iactType);
                ps.setInt(++index, isysType);
                ps.setInt(++index, iisloadenv);
                ps.setLong(++index, itimeOut);
                ps.setInt(++index, iswitchsysType);
                ps.setString(++index, iparaCheck);
                ps.setString(++index, iparaSwitch);
                ps.setString(++index, iparaSwitchforce);
                ps.setInt(++index, iisDisable == true ? 1 : 0);
                ps.setDouble(++index, iserner);
                ps.setString(++index, iactName);
                ps.setLong(++index, iid);
                ps.addBatch();
            }
            ps.executeBatch();

            ps = null;
            sql = "select * from ieai_instanceinfo where IINSTANCEID in (select iid from ieai_instance_version where IVERSION = ? AND IINSTANCENAME = ? )";
            ps = conn.prepareStatement(sql);
            ps.setString(1, version);
            ps.setString(2, instanceName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                IcExeclInfo susExeclInfo = new IcExeclInfo();
                susExeclInfo.setIinstanceid(rs.getLong("IINSTANCEID"));
                susExeclInfo.setIinstanceName(instanceName);
                susExeclInfo.setActNo(rs.getDouble("ISERNER"));
                susExeclInfo.setConNo(rs.getLong("ICONNER"));
                susExeclInfo.setPreNo(rs.getString("IPRENER"));
                susExeclInfo.setActName(rs.getString("IACTNAME"));
                susExeclInfo.setActDes(rs.getString("IACTDES"));
                susExeclInfo.setTaskType(rs.getInt("IACTTYPE"));
                susExeclInfo.setTaskRem(rs.getString("IREMINFO"));
                susExeclInfo.setIp(rs.getString("IIP"));
                susExeclInfo.setPort(rs.getInt("IPORT") != 0 ? rs.getInt("IPORT") : 1500);
                susExeclInfo.setSusType(rs.getInt("ISYSTYPE"));
                susExeclInfo.setShellPath(rs.getString("ISHELLPATH"));
                susExeclInfo.setTimeOutT(rs.getInt("ITIMEOUT"));
                susExeclInfo.setSwitchSysType(rs.getInt("ISWITCHSYSTYPE"));
                susExeclInfo.setShellPara(rs.getString("IPARAMETER"));
                susExeclInfo.setParaCheck(rs.getString("IPARACHECK"));
                susExeclInfo.setParaSwitch(rs.getString("IPARASWITCH"));
                susExeclInfo.setParaSwitchForce(rs.getString("IPARASWITCHFORCE"));
                susExeclInfo.setExpectInfo(rs.getString("IEXPECEINFO"));
                susExeclInfo.setExecuser(rs.getString("IEXECUSER"));
                susExeclInfo.setShellscript(rs.getString("ISHELLSCRIPT"));
                susExeclInfo.setIsLoadEnv(rs.getInt("IISLOADENV"));
                susExeclInfo.setIsdisable(rs.getInt("IISDISABLE"));
                list.add(susExeclInfo);
            }
            if (list.size() > 0)
            {
                conn.rollback();
            }
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            _log.error("updateInstanceInfo method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            try
            {
                if (rs != null)
                {
                    rs.close();
                }
                if (ps != null)
                {
                    ps.close();
                }
            } catch (SQLException e)
            {
                e.printStackTrace();
            }
        }
        return list;
    }

    public List selInstanceInfo ( String instanceName, String version, Connection conn )
            throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select * from ieai_instanceinfo where IINSTANCEID in (select iid from ieai_instance_version where IVERSION = ? AND IINSTANCENAME = ? )";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, version);
            ps.setString(2, instanceName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                IcExeclInfo susExeclInfo = new IcExeclInfo();
                susExeclInfo.setIinstanceid(rs.getLong("IINSTANCEID"));
                susExeclInfo.setIinstanceName(instanceName);
                susExeclInfo.setActNo(rs.getDouble("ISERNER"));
                susExeclInfo.setConNo(rs.getLong("ICONNER"));
                susExeclInfo.setPreNo(rs.getString("IPRENER"));
                susExeclInfo.setActName(rs.getString("IACTNAME"));
                susExeclInfo.setActDes(rs.getString("IACTDES"));
                susExeclInfo.setTaskType(rs.getInt("IACTTYPE"));
                susExeclInfo.setTaskRem(rs.getString("IREMINFO"));
                susExeclInfo.setIp(rs.getString("IIP"));
                susExeclInfo.setPort(rs.getInt("IPORT") != 0 ? rs.getInt("IPORT") : 1500);
                susExeclInfo.setSusType(rs.getInt("ISYSTYPE"));
                susExeclInfo.setShellPath(rs.getString("ISHELLPATH"));
                susExeclInfo.setTimeOutT(rs.getInt("ITIMEOUT"));
                susExeclInfo.setSwitchSysType(rs.getInt("ISWITCHSYSTYPE"));
                susExeclInfo.setShellPara(rs.getString("IPARAMETER"));
                susExeclInfo.setParaCheck(rs.getString("IPARACHECK"));
                susExeclInfo.setParaSwitch(rs.getString("IPARASWITCH"));
                susExeclInfo.setParaSwitchForce(rs.getString("IPARASWITCHFORCE"));
                susExeclInfo.setExpectInfo(rs.getString("IEXPECEINFO"));
                susExeclInfo.setExecuser(rs.getString("IEXECUSER"));
                susExeclInfo.setShellscript(rs.getString("ISHELLSCRIPT"));
                susExeclInfo.setIsLoadEnv(rs.getInt("IISLOADENV"));
                susExeclInfo.setIsdisable(rs.getInt("IISDISABLE"));
                list.add(susExeclInfo);
            }
        } catch (SQLException e)
        {
            e.printStackTrace();
            _log.error("selInstanceInfo method of InstanceConfigManager.class SQLException:"
                    + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            try
            {
                if (rs != null)
                {
                    rs.close();
                }
                if (ps != null)
                {
                    ps.close();
                }
            } catch (Exception e2)
            {
                e2.printStackTrace();
            }
        }
        return list;
    }
    
  //begin.added by manxi_zhao.2016-06-14.
    /**
     * <li>Description:审核任务界面--系统信息</li> 
     * <AUTHOR>
     * 2016年5月30日 
     * @param instanceConfigBeanForQuery
     * @param type
     * @return
     * return Map
     * @throws RepositoryException 
     */
    public Map getInstanceInsInfoListForEMDoubleCheck (
            IcInstanceConfigBeanForQuery instanceConfigBeanForQuery, int type ) 
            throws RepositoryException
    {
        Map map = new HashMap();
        List res = new ArrayList();
        Connection conn = null;
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        PreparedStatement selPS = null;
        ResultSet selRS = null;
        Map dicMap = new HashMap();

        for (int i = 0; i < 10; i++)
        {
            int count = 0;
            try
            {
                try
                {
                    String selSQL = "SELECT * FROM IEAI_DOUBLECHECK_IP T WHERE T.IINFOID=? AND T.IREVIEWID=?";
                    long startTime = System.currentTimeMillis();
                    String orderString = " ORDER BY ICONNER,ISERNER ";
                    String sqlWhere = " WHERE T.IID>0  ";
                    String sqlPageString = "SELECT * FROM (SELECT ROW_NUMBER() OVER ("
                            + orderString + ") AS RN,T.* FROM IEAI_INSTANCEINFO T";
                    String sqlPageStringEnd = ") A WHERE A.RN  <=? AND A.RN >  ?  ";
                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        // mysql
                        sqlPageString = "SELECT * FROM (SELECT T.* FROM IEAI_INSTANCEINFO T";
                        sqlPageStringEnd = orderString + " ) A limit ?, ? ";
                    }
                    String sqlCount = "SELECT COUNT(*) AS NUM FROM  IEAI_INSTANCEINFO T ";
                    conn = DBResource.getConnection("getInstanceInsInfoListForEMDoubleCheck", _log,type);
                    if (instanceConfigBeanForQuery.getIidForQuery() != 0)
                    {
                        sqlWhere = sqlWhere + " AND T.IINSTANCEID ="
                                + instanceConfigBeanForQuery.getIidForQuery() + " ";
                    }
                    if (instanceConfigBeanForQuery.getIiactNameForQuery() != null
                            && !"".equals(instanceConfigBeanForQuery.getIiactNameForQuery()))
                    {
                        sqlWhere = sqlWhere + " AND T.IACTNAME LIKE '%"
                                + instanceConfigBeanForQuery.getIiactNameForQuery() + "%' ";
                    }
                    if (instanceConfigBeanForQuery.getIisDisable() != null
                            && !"".equals(instanceConfigBeanForQuery.getIisDisable()))
                    {
                        sqlWhere = sqlWhere + " AND T.IISDISABLE ="
                                + instanceConfigBeanForQuery.getIisDisable() + " ";
                    }

                    sqlPageString = sqlPageString + sqlWhere + sqlPageStringEnd;
                    sqlCount = sqlCount + sqlWhere;

                    actStat = conn.prepareStatement(sqlPageString);

                    if (3 == JudgeDB.IEAI_DB_TYPE)
                    {
                        // mysql.
                        actStat.setInt(1, instanceConfigBeanForQuery.getStart());
                        actStat.setInt(2, instanceConfigBeanForQuery.getLimit());
                    } else
                    {
                        actStat.setInt(1,
                            instanceConfigBeanForQuery.getStart() + instanceConfigBeanForQuery.getLimit());
                        actStat.setInt(2, instanceConfigBeanForQuery.getStart());
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        dicMap.put(Long.toString(actRS.getLong("ISERNER")), actRS
                                .getString("IACTNAME"));
                        Map returnMap = new HashMap();
                        returnMap.put("iid", actRS.getLong("IID"));
                        returnMap.put("iinstanceId", actRS.getLong("IINSTANCEID"));
                        returnMap.put("iinstanceName", actRS.getString("IINSTANCENAME"));
                        returnMap.put("iserner", actRS.getLong("ISERNER"));
                        returnMap.put("iconner", actRS.getLong("ICONNER"));
                        returnMap.put("iconnerName", actRS.getString("ICONNERNAME"));
                        returnMap.put("iprener", "-1".equals(actRS.getString("IPRENER")) ? ""
                                : actRS.getString("IPRENER"));
                        returnMap.put("iactName", actRS.getString("IACTNAME"));
                        returnMap.put("iactDes", actRS.getString("IACTDES"));
                        returnMap.put("iactType", ACTTYPE_MAP.get(actRS.getInt("IACTTYPE")));
                        returnMap.put("iremInfo", actRS.getString("IREMINFO"));
                        returnMap.put("ipName", actRS.getString("IIPNAME"));
                        returnMap.put("imodelType", actRS.getString("IMODELTYPE"));
                        returnMap.put("iip", actRS.getString("IIP"));
                        returnMap.put("iport", actRS.getInt("IPORT"));
                        returnMap.put("isysType", SYSTYPE_MAP.get(actRS.getInt("ISYSTYPE")));
                        returnMap.put("iexecUser", actRS.getString("IEXECUSER"));
                        returnMap.put("ishellScript", actRS.getString("ISHELLSCRIPT"));
                        returnMap.put("iisloadenv", LOADENV_MAP.get(actRS.getInt("IISLOADENV")));
                        returnMap.put("ishellPath", actRS.getString("ISHELLPATH"));
                        returnMap.put("itimeOut", actRS.getLong("ITIMEOUT") == 0 ? "" : actRS
                                .getLong("ITIMEOUT"));
                        returnMap.put("iparameter", actRS.getString("IPARAMETER"));
                        returnMap.put("iexpeceInfo", actRS.getString("IEXPECEINFO"));
                        returnMap.put("iexceptinfo", actRS.getString("IEXCEPTINFO"));
                        returnMap.put("iredoable", REDOABLE_MAP.get(actRS.getInt("IREDOABLE")));
                        returnMap.put("iisDisable", actRS.getInt("IISDISABLE") == 1 ? true : false);
                        
                        //begin.
                        //查询选中的iip
                        selPS = conn.prepareStatement(selSQL);
                        selPS.setLong(1, actRS.getLong("IID"));
                        selPS.setLong(2, instanceConfigBeanForQuery.getIworkItemid());
                        selRS = selPS.executeQuery();
                        
                        String resultIPStr = "";
                        while(selRS.next()){
                            int checkedFlag = selRS.getInt("ICHECKED");
                            String iip = selRS.getString("IIP");
                            if(1==checkedFlag && null!=iip && !iip.trim().equals("")){
                                iip = "*" + iip;
                            }
                            resultIPStr += iip + EMConstants.IP_SPLIT;
                        }
                        //有ip数据,去掉最后一个分隔符
                        if(resultIPStr.length()>EMConstants.IP_SPLIT.length()){
                            resultIPStr = resultIPStr.substring(0,resultIPStr.length()-EMConstants.IP_SPLIT.length());
                        }
                        returnMap.put("iip", resultIPStr);
                        //end.
                        
                        res.add(returnMap);
                    }

                    actStat = conn.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt("NUM");
                    }
                    // 将依赖步骤标识转换为自步骤名称显示
                    for (int z = 0; z < res.size(); z++)
                    {
                        String iprenerString = "";
                        Map rMap = (Map) res.get(z);
                        String iprener = (String) rMap.get("iprener");
                        if (null != iprener && !"".equals(iprener))
                        {
                            String[] iprenerItem = iprener.split(",");
                            for (int x = 0; x < iprenerItem.length; x++)
                            {
                                if (x > 0)
                                {
                                    iprenerString += ",";
                                }
                                iprenerString += dicMap.get(iprenerItem[x]);
                            }
                        }
                        rMap.put("iprenerString", iprenerString);
                    }
                    map.put("total", count);
                    map.put("dataList", res);
                    long endTime = System.currentTimeMillis();
                    Calendar c = Calendar.getInstance();
                    c.setTimeInMillis(endTime - startTime);
//                    _log.info("method getInstanceInsInfoListForEMDoubleCheck() 耗时: " + c.get(Calendar.MINUTE)
//                            + "分 " + c.get(Calendar.SECOND) + "秒 " + c.get(Calendar.MILLISECOND)
//                            + " 毫秒" + "\n {sql:" + sqlPageString + "}");
                } catch (SQLException e)
                {
                    // TODO Auto-generated catch block
                    _log
                            .error("getInstanceInsInfoListForEMDoubleCheck method SQLException:"
                                    + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getInstanceInsInfoList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                _log
                        .error("getInstanceInsInfoListForEMDoubleCheck method RepositoryException:"
                                + ex.getMessage());
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }

        return map;
    }
  //end.added by manxi_zhao.2016-06-14.
}
