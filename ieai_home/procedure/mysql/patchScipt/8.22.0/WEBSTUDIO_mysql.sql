-- 4.7.21
CREATE TABLE IF NOT EXISTS IEAI_STRING_QUENY (IID NUMERIC(19) NOT NULL ,IUUID VARCHAR(255) NOT NULL ,ICONTENT LONGTEXT );
-- 4.7.23
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISWHITEANDIP') THEN
        ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISWHITEANDIP VARCHAR(30);
        END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISDELETE') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISDELETE NUMERIC(1) DEFAULT 0 ;
END IF;
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISDELETEINFO') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISDELETEINFO VARCHAR(255) ;
END IF;
END;;
	DELIMITER ;
	CALL UPPRDE();

CREATE TABLE IF NOT EXISTS IEAI_ACTIVITY_MANAGE (IID NUMERIC(19)NOT NULL,IACTNAME VARCHAR(50), IACTDES VARCHAR(1000), IACTTYPE NUMERIC(2),ISVALID NUMERIC(2) DEFAULT 0,CONSTRAINT PK_IEAI_ACTIVITY_MANAGE PRIMARY KEY (IID));
CREATE TABLE IF NOT EXISTS IEAI_PORT (IID DECIMAL(19)NOT NULL,IPORTNAME VARCHAR(50),IPORTDES VARCHAR(1000),CONSTRAINT PK_IEAI_PORT PRIMARY KEY (IID));
CREATE TABLE IF NOT EXISTS IEAI_ACTIVITY_PORT (IID NUMERIC(19)NOT NULL,ACTID NUMERIC(19),PORTID NUMERIC(19),FOREIGN KEY(ACTID)REFERENCES IEAI_ACTIVITY_MANAGE(IID),FOREIGN KEY(PORTID)REFERENCES IEAI_PORT(IID),CONSTRAINT PK_IEAI_ACTIVITY_PORT PRIMARY KEY(IID));
-- 4.7.27
CREATE TABLE IF NOT EXISTS IEAI_EXECACT_PROGRESS (IID NUMERIC(19) NOT NULL ,IFLOWID NUMERIC(19) DEFAULT 0 ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,IACTID NUMERIC(10) DEFAULT 0 ,BHIACTID VARCHAR(255) ,IACTNAME VARCHAR(255) ,BHIACTNAME VARCHAR(4000) ,CONSTRAINT PK_IEAI_EXECACT_PROGRESS PRIMARY KEY (IID));
ALTER  TABLE ieai_project_info_ws MODIFY COLUMN IRELEASETIME  datetime ;

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'IEDITUSER') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD  IEDITUSER NUMERIC(19,0) default -1;
    update IEAI_PROJECT_INFO_WS set IEDITUSER = ICREATEUSRID where IEDITUSER = -1;
END IF;	
END;;
	DELIMITER ;
	CALL UPPRDE();
update IEAI_PROJECT_TYPE_WS set IPROPERTYIDS='3,5' where IID =4;

delete from IEAI_PROJECT_TYPE_WS where IID in (300,400);
INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (300, 'IEAI_SUS_GROUP', '变更组合编排' , '5');
INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (400, 'IEAI_AZ_GROUP', 'Az组合编排' , '5');


DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

			
	IF NOT EXISTS (SELECT * FROM IEAI_DBSOURCE WHERE IDBSOURCEID=300) THEN
		INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (300,300,'变更组合编排源','','','','',0);
	END IF;
	
	IF NOT EXISTS (SELECT * FROM IEAI_DBSOURCE WHERE IDBSOURCEID=400) THEN
		INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (400,400,'Az组合编排源','','','','',0);
	END IF;
	
END;;
DELIMITER ;
CALL UPPRDE();
  

delete from IEAI_PROJECT where IID in (-300,-400);
INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-400, 0, '所有Az组合编排业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 400, -400, -400, -400);
INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-300, 0, '所有变更组合编排业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 300, -300, -300, -300);
delete from IEAI_GROUPMESSAGE where GROUPID in (300,400);

INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (300, '变更组合编排', '变更组合编排模块组', 300, 'images/info82.png');
INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (400, 'Az组合编排', 'Az组合编排模块组', 400, 'images/info82.png');

CREATE TABLE IF NOT EXISTS IEAI_TEMPLATE_CALL_ACT (IID DECIMAL(19,0) NOT NULL, IORDERID DECIMAL(19,0), IFLOWID DECIMAL(19,0), ISTATE  DECIMAL(19,0), ITIMESTAMP TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6), IEXECACTIID DECIMAL(19,0),IENVID DECIMAL(19,0),NODENAME VARCHAR(255),ITEMPLATETYPE SMALLINT, CONSTRAINT PK_IEAI_TEMPLATE_CALL_ACT PRIMARY KEY(IID));
CREATE TABLE IF NOT EXISTS IEAI_TEMPLATE_CALL_REL (IID DECIMAL(19,0) NOT NULL, ICALLACTID DECIMAL(19,0) NOT NULL, IORDERID DECIMAL(19,0), ITEMPLATEUUID VARCHAR(255), ISTATE  DECIMAL(19,0), ITIMESTAMP TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6), IFLOWID DECIMAL(19,0), CONSTRAINT PK_IEAI_TEMPLATE_CALL_REL PRIMARY KEY(IID));

	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WORKFLOWINSTANCE' AND COLUMN_NAME = 'PRESTART') THEN
        ALTER TABLE IEAI_WORKFLOWINSTANCE ADD PRESTART SMALLINT default 0 ;
        END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IAUDITOR') THEN
        ALTER TABLE IEAI_PROJECT ADD IAUDITOR VARCHAR(255);
        END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IRELEASESTATE') THEN
        ALTER TABLE IEAI_PROJECT ADD IRELEASESTATE NUMERIC(19,0);
        END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IBACKINFO') THEN
        ALTER TABLE IEAI_PROJECT ADD IBACKINFO VARCHAR(255);
        END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	CREATE TABLE IF NOT EXISTS IEAI_RUN_GROUP_ACTINFO (IFLOWID DECIMAL(19,0) NOT NULL,    IACTNAME VARCHAR(255),IPARAMTYPE VARCHAR(255),IPARAMNAME VARCHAR(255)  ,IPARAMVALUE VARCHAR(255), ITIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,CONSTRAINT PK_RUN_GROUP_ACTINFO PRIMARY KEY (IFLOWID,IACTNAME,IPARAMTYPE,IPARAMNAME) );
	CREATE TABLE IF NOT EXISTS IEAI_PROJECT_ACT_INFOMATION (IID DECIMAL(19,0) NOT NULL, IPROJECTID DECIMAL(19,0) NOT NULL, IPROTYPE DECIMAL(19,0), IFLOWNAME VARCHAR(255), IPRONAME VARCHAR(255),IACTNAME VARCHAR(255),IPARAMTYPE VARCHAR(255),IPARAMNAME VARCHAR(255), IACTTYPE VARCHAR(255) ,IPARAMVALUE VARCHAR(255), ITIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP, CONSTRAINT PK_IEAI_PROJECT_ACT_INFOMATION PRIMARY KEY(IID));
	
	delete from IEAI_PROJECT_PROPERTY_TYPE_DC where IID in (6,7,8,9,10);
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(6,4,'TelnetConnection');
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(7,4,'SshConnection');
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(8,4,'DBConnection');
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(9,4,'SFTPConnection');
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(10,4,'HTTPConnection');

delete from IEAI_PROJECT_PROPERTY_KEY_DC;
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(1,'funid');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(2,'isusecalendar');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(3,'flowcalendar');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(4,'inputparameter');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(5,'outputparameter');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(6,'vartype');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(7,'varvalue');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(8,'ftpserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(9,'ftpport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(10,'ftpuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(11,'ftppassword');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(12,'ftpovertime');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(13,'ftpencode');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(14,'isuseproxy');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(15,'ftpproxyserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(16,'ftpproxyport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(17,'ftpproxyuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(18,'ftpproxyowd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(19,'useagentgroup');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(20,'agentip');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(21,'agentport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(22,'connecttype');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(23,'agentgroup');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(24,'sshserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(25,'sshport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(26,'sshuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(27,'sshtimeout');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(28,'sshauthgroup');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(29,'sshkeyaddress');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(30,'sshkeypasswd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(31,'sshloginpasswd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(32,'sshdocprompt');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(33,'sshagenttype');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(34,'sshagentserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(35,'sshagentport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(36,'sshagentuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(37,'sshagentpasswd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(38,'telnetgridData');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(39,'telnetserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(40,'telnetport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(41,'telnetuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(42,'telnettimeout');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(43,'telnetpwd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(44,'telnetendSign');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(45,'httpConnectionServer')  ;
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(46,'httpConnectionPort')  ;
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(47,'httpConnectionTimeout')  ;
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(48,'httpConnectionProtocol')  ;
    DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WORKFLOWINSTANCE' AND COLUMN_NAME = 'IORDERID') THEN
    ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERID NUMERIC(19) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WORKFLOWINSTANCE' AND COLUMN_NAME = 'IORDERUUID') THEN
    ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERUUID VARCHAR(255) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IMODELID') THEN
    ALTER TABLE IEAI_PROJECT ADD IMODELID NUMERIC(19) DEFAULT -1 ;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'IISFORBIDDEN') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD IISFORBIDDEN NUMERIC(19) DEFAULT 1 ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISTATUS') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISTATUS NUMERIC(19) DEFAULT 0 ;
	END IF;
	
	
	END;;
	DELIMITER ;
	CALL UPPRDE();

	
	




-- V8.9.0
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_ACTIVITY_PORT' AND INDEX_NAME = 'IDX_IEAI_ACTIVITY_PORT_ACTID') THEN
			CREATE INDEX IDX_IEAI_ACTIVITY_PORT_ACTID ON IEAI_ACTIVITY_PORT (ACTID);
		END IF;	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_ACTIVITY_PORT' AND INDEX_NAME = 'IDX_IEAI_ACTIVITY_PORT_PORTID') THEN
			CREATE INDEX IDX_IEAI_ACTIVITY_PORT_PORTID ON IEAI_ACTIVITY_PORT (PORTID);
		END IF;
		
	END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_TEMPLATE_PARAM_MG (IID NUMERIC (19) ,INAME VARCHAR (255) ,IENMU NUMERIC (19) ,IPARAMNAMETYPE VARCHAR (255) ,IPARAMNAMECLASS NUMERIC (19) ,IINSTANCEID NUMERIC (19) ,IPARAMNAME VARCHAR (255) ,IVALUE VARCHAR (255) ,IN_OUT NUMERIC (19) DEFAULT 0,DESCRIPTION VARCHAR (4000) ,IENMUNAME VARCHAR (255) ,IORDER NUMERIC (19) DEFAULT 0,IEDITABLE NUMERIC (19) DEFAULT 0,CELLID NUMERIC (19) DEFAULT 0, CONSTRAINT PK_IEAI_TEMPLATE_PARAM_MG PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PAAS_ORDER_PARAMARE_CACHE' AND COLUMN_NAME = 'TEMPID') THEN 
			ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD TEMPID NUMERIC(19) DEFAULT 0; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PAAS_ORDER_PARAMARE_CACHE' AND COLUMN_NAME = 'TYPE') THEN 
			ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD TYPE VARCHAR(100) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PAAS_ORDER_PARAMARE_CACHE' AND COLUMN_NAME = 'TEMPNAME') THEN 
			ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD TEMPNAME VARCHAR(100) ; 
		END IF;  

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PAAS_ORDER_PARAMARE_CACHE' AND COLUMN_NAME = 'IENMU') THEN 
			ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD IENMU NUMERIC(19) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CATALOG' AND COLUMN_NAME = 'IPARENTID') THEN
		ALTER TABLE IEAI_SYNC_CATALOG ADD  IPARENTID DECIMAL(19);
		END IF; 
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CATALOG' AND COLUMN_NAME = 'ICOMPARETARGET') THEN
		ALTER TABLE IEAI_SYNC_CATALOG ADD  ICOMPARETARGET  VARCHAR(255) ;
		END IF; 	
	END;;
DELIMITER ;
CALL UPPRDE();




DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ADAPTOR_PROPERTY_KEY_WS' AND COLUMN_NAME = 'IDEFAULTVALUE') THEN
		ALTER TABLE IEAI_ADAPTOR_PROPERTY_KEY_WS ADD  IDEFAULTVALUE  VARCHAR(255) ;
		END IF; 	
	END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FUNCTIONINFO' AND COLUMN_NAME = 'TYPE') THEN
		ALTER TABLE IEAI_FUNCTIONINFO ADD  TYPE   DECIMAL(19) ;
		END IF; 
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FUNCTIONINFO' AND COLUMN_NAME = 'CLASSNAME') THEN
		ALTER TABLE IEAI_FUNCTIONINFO ADD  CLASSNAME  VARCHAR(200) ;
		END IF; 
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FUNCTIONINFO' AND COLUMN_NAME = 'FUNCTIONNAME') THEN
		ALTER TABLE IEAI_FUNCTIONINFO ADD  FUNCTIONNAME  VARCHAR(200) ;
		END IF; 
		
	END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

	IF NOT EXISTS (SELECT * FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=15) THEN
		INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(15,2,'ExternalFunction');
	END IF;
	
END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '外部函数管理' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info160.png' WHERE INAME = '外部函数管理';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '内部函数管理' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info161.png' WHERE INAME = '内部函数管理';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '接口列表' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info162.png' WHERE INAME = '接口列表';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '可恢复工程' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info163.png' WHERE INAME = '可恢复工程';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '公共参数管理' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info164.png' WHERE INAME = '公共参数管理';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '福建paas统一入口' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info165.png' WHERE INAME = '福建paas统一入口';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '组合编排启动' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info166.png' WHERE INAME = '组合编排启动';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '组合编排查询' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info167.png' WHERE INAME = '组合编排查询';
	END IF;
	
	IF  EXISTS (SELECT * FROM IEAI_MENU WHERE INAME = '组合编排' ) THEN
		UPDATE IEAI_MENU SET IIMG = 'images/info168.png' WHERE INAME = '组合编排';
	END IF;
	
	
END;;
DELIMITER ;
CALL UPPRDE();

DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_EXECACT_PROGRESS' AND COLUMN_NAME = 'BHIACTNAME') THEN
			ALTER TABLE IEAI_EXECACT_PROGRESS MODIFY BHIACTNAME VARCHAR(4000);
		END IF;
		COMMIT;
	  END;;
DELIMITER ;
CALL UPPRDE();




DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

	IF NOT EXISTS (SELECT * FROM information_schema. COLUMNS WHERE COLUMN_KEY = 'PRI' AND table_name = 'IEAI_RUN_GROUP_ACTINFO') THEN
		ALTER TABLE IEAI_RUN_GROUP_ACTINFO ADD CONSTRAINT PK_RUN_GROUP_ACTINFO PRIMARY KEY (IFLOWID,IACTNAME,IPARAMTYPE,IPARAMNAME);
	END IF;

	END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TEMPLATE_CALL_ACT' AND COLUMN_NAME = 'ITEMPLATETYPE') THEN
        ALTER TABLE IEAI_TEMPLATE_CALL_ACT ADD ITEMPLATETYPE SMALLINT ;
        END IF;
	END;;
DELIMITER ;
CALL UPPRDE();

CREATE TABLE IF NOT EXISTS IEAI_PARAM_MG  ( IID  DECIMAL(19,0) NOT NULL,INAME VARCHAR(255) ,IVALUE  VARCHAR(255) ,IPARAMNAME  VARCHAR(255) , ISCOPE  VARCHAR(255) , IDES  VARCHAR(255) , IINSTANCEID DECIMAL(19,0) , IUSERID DECIMAL(19,0) , CONSTRAINT PK_IEAI_PARAM_MG PRIMARY KEY(IID)  );



DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

     CREATE TABLE IF NOT EXISTS IEAI_WEBSTUDIO_FLOW_CLOB_DTO (IID NUMERIC(19) NOT NULL,FLOWID NUMERIC(19) NOT NULL, RUNFLOWID NUMERIC(19) NOT NULL,VERSIONSNO NUMERIC(19) NOT NULL,ICONTENT LONGTEXT, CONSTRAINT PK_IEAI_WEBSTUDIO_FLOW_CLOB_DTO PRIMARY KEY (IID));
	  END;;
DELIMITER ;
CALL UPPRDE();