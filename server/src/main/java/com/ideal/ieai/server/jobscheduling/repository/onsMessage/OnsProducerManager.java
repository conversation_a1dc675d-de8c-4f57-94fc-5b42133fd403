package com.ideal.ieai.server.jobscheduling.repository.onsMessage;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.jobscheduling.repository.workflow.WorkFlowManager;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class OnsProducerManager {
    private Logger log     = Logger.getLogger(OnsProducerManager.class);
    private Logger logMqErrorMessage     = Logger.getLogger("mqMessage");
    private static final String DELIMITER = "_@|@_";

    private static OnsProducerManager intance = null;

    public static OnsProducerManager getInstance ()
    {
        if (intance == null)
        {
            intance = new OnsProducerManager();

        }
        return intance;
    }

    public Map<String ,String > sendMq(String jobStatus,long flowId,String actName,Date time) {
        Map<String ,String > result = new HashMap();
        //脚本获取内置环境变量活动名,数据日期,结束标识
        // 作业名 flowName  子系统名 proName   实例名insName 工作流
       /* WarningManage warningInterface = new WarningManage();
        boolean Redo=false;
        if("FAIL".equals(jobStatus)&&!"".equals(actName)){
            Redo=  warningInterface.excRedoEstimate(flowId,actName);
            if(!Redo){
                result.put("success","true");
                result.put("message","当前重试告警拦截次数");
                return result;
            }
        }*/

        /** end */
        String flowName = "";
        String proName = "";
        String insName = "";
        String mainName = "";
        String mainPro = "";
        String callPrjName = "";
        String callFlowName = "";
        String callInsName = "";
        Map<String, String> sendMq = null;
        Map<String, String> callMap = null ;

        String numberMessage = Environment.getInstance().getBhMqNumberName();
        if (StringUtils.isEmpty(numberMessage)){
            numberMessage = "NumberMessage";
        }
        try {
            sendMq = EngineRepositotyJdbc.getInstance().getSendMqMessageByFlowId(flowId, Constants.IEAI_IEAI);
            if (sendMq != null  && !sendMq.isEmpty()) {
                flowName = sendMq.get("flowName");
                proName = sendMq.get("projectName");
                insName = sendMq.get("flowinsName");
                mainName = sendMq.get("mainName");
                mainPro = sendMq.get("mainPro");
            }
            if (StringUtils.isNotBlank(mainName) && StringUtils.isNotBlank(mainPro)) {
                callMap = WorkFlowManager.getInstance().queryMqProject(mainPro, mainName, insName);
            } else {
                mainName = flowName;
                callMap = WorkFlowManager.getInstance().queryMqProject(proName, flowName, insName);
            }
            if (callMap.size() != 0 && callMap != null) {
                callPrjName = callMap.get("mainPrjName");
                callFlowName = callMap.get("mainFlowName");
                callInsName = callMap.get("mainInsName");
            } else {
                callPrjName = proName;
                callFlowName = flowName;
                callInsName = insName;
            }
        } catch (RepositoryException e) {
            log.error("sendMq is error :",e);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeToStr = sdf.format(time);
        Map<String, String> map = getConsumerParameters();
        String topic = map.get("topic");
        Producer producer = ProducerBean.getInstance();
        log.info("生产者类 : "+producer);

        try {

            producer.start();


            for (int i = 0; i < 10; i++) {

                Message msg = new Message(
                        // 普通消息所属的Topic，切勿使用普通消息的Topic来收发其他类型的消息。
                        topic,
                        // Message Tag可理解为Gmail中的标签，对消息进行再归类，方便Consumer指定过滤条件在消息队列RocketMQ版的服务器过滤。
                        flowName,
                        ( numberMessage + DELIMITER +
                                callFlowName + DELIMITER +
                                callInsName + DELIMITER +
                                mainName + DELIMITER +
                                jobStatus + DELIMITER +
                                flowName+ DELIMITER +
                                callPrjName+ DELIMITER +
//                                timeToStr).getBytes("UTF-8"));
                                timeToStr+ DELIMITER +
                                String.valueOf(flowId)).getBytes("UTF-8"));

                try {
                    SendResult sendResult = producer.send(msg);
                    // 同步发送消息，只要不抛异常就是成功。
                    if(sendResult != null){
                        log.info("Message send success : "+ numberMessage + DELIMITER + callFlowName + DELIMITER + callInsName + DELIMITER + mainName + DELIMITER + jobStatus + DELIMITER + flowName + DELIMITER + callPrjName+ DELIMITER + timeToStr+ DELIMITER +flowId+";");
                        result.put("success","true");
                        result.put("message","发送成功");
                        break;
                    }
                }
                catch (Exception e) {
                    logMqErrorMessage.info("Message send Fail : "+ numberMessage + DELIMITER + callFlowName + DELIMITER + callInsName + DELIMITER + mainName + DELIMITER + jobStatus + DELIMITER + flowName + DELIMITER + callPrjName+ DELIMITER + timeToStr+DELIMITER +flowId+";");
                    // 消息发送失败，需要进行重试处理，可重新发送这条消息或持久化这条数据进行补偿处理。
                    log.info(new Date() + " Send mq message failed. Topic is:" + msg.getTopic());
                    log.error("发送Mq消息失败:"+i,e);
                    if (i==9){
                        log.error("Send end mq message failed : ",e);
                    }

                }

            }
        } catch(Exception e) {
            log.error("发送Mq消息失败:",e);
            result.put("success","false");
            result.put("message","发送失败");

        } finally {

//            if (producer != null) {
//                producer.shutdown();
//            }
        }
        return result;
    }



    public  Map<String, String> getConsumerParameters() {
        Map<String, String> map = new HashMap<>();
        Environment instance = Environment.getInstance();
        String bhGroupId = instance.getBhGroupId();
        String bhAccessKey = instance.getBhAccessKey();
        String bhSecretKey = instance.getBhSecretKey();
        String bhNamesrvAddr = instance.getBhNamesrvAddr();
        String bhTopic = instance.getBhTopic();

        map.put("GROUP_ID", bhGroupId);
        map.put("AccessKey", bhAccessKey);
        map.put("SecretKey", bhSecretKey);
        map.put("NAMESRV_ADDR", bhNamesrvAddr);
        map.put("topic", bhTopic);
        map.put("SendMsgTimeoutMillis", "30000");
        return map;
    }
}
