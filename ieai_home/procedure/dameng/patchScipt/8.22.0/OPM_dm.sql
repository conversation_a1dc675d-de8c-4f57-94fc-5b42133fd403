--4.7.17 not exists
--4.7.18
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER' AND COLUMN_NAME='ICREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_USER ADD ICREATETIME NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_HIGHOPER' AND COLUMN_NAME = 'IPERMISSIONTYPE';
     IF  LI_EXISTS = 0  THEN
         LS_SQL := 'ALTER TABLE IEAI_HIGHOPER ADD IPERMISSIONTYPE  INTEGER DEFAULT 0';
         EXECUTE IMMEDIATE LS_SQL;
     END IF;
END;
/
--4.7.19
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_COLLECT_OPERATION_STATUS' AND OBJECT_TYPE = 'TABLE';
 	IF LI_EXISTS = 1 THEN
		LS_SQL := 'drop table IEAI_COLLECT_OPERATION_STATUS';
        EXECUTE IMMEDIATE LS_SQL;
  	    END IF;
	

END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN  
  SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_RESGROUP_MODEL' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	          LS_SQL := 'CREATE TABLE IEAI_RESGROUP_MODEL  ( 
												IID     	NUMBER(19) NOT NULL,
												IGROUPMESID  NUMBER(19),
												IISTRUE	INTEGER,
												CONSTRAINT PK_IEAI_RESGROUP_MODEL PRIMARY KEY(IID)
											)';
			  EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_COLLECT_OPERATION_STATUS' AND OBJECT_TYPE = 'TABLE';
 	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE  IEAI_COLLECT_OPERATION_STATUS (IID	NUMBER(14) NOT NULL,ISTATUS  smallint,CONSTRAINT  PK_IEAI_COLLECT_OPERATION_S PRIMARY KEY(IID))';
        EXECUTE IMMEDIATE LS_SQL;
  	    END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AUDIT_CLOB' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_AUDIT_CLOB (AUDITID NUMBER(19) NOT NULL, ICONTENT CLOB NULL, CONSTRAINT PK_IEAI_AUDIT_CLOB PRIMARY KEY(AUDITID))';
      EXECUTE IMMEDIATE LS_SQL;
    END IF;

END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ROLE' AND COLUMN_NAME='ICREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ROLE ADD ICREATETIME NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
  
  SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_RESGROUP_MODEL WHERE IID = -1;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_RESGROUP_MODEL (IID,IGROUPMESID,IISTRUE) VALUES(-1,3,1)';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID = 15;
	IF LI_EXISTS = 1 THEN
		 LS_SQL := 'UPDATE IEAI_GROUPMESSAGE SET IIMG = ''images/info6666.png '' WHERE GROUPID=15';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID = 100;
	IF LI_EXISTS = 1 THEN
		 LS_SQL := 'UPDATE IEAI_GROUPMESSAGE SET IIMG = ''images/info85.png '' WHERE GROUPID=100';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID = 10;
	IF LI_EXISTS = 1 THEN
		 LS_SQL := 'UPDATE IEAI_GROUPMESSAGE SET IIMG = ''images/info6667.png '' WHERE GROUPID=10';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	SELECT
		SIGN (COUNT(*)) INTO LI_EXISTS
	FROM
		IEAI_COLLECT_OPERATION_STATUS WHERE IID = 10 ;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_COLLECT_OPERATION_STATUS(IID,ISTATUS) VALUES(10, 0)' ; 
		EXECUTE IMMEDIATE LS_SQL ;
		commit;
	END IF ;
  	
END;
/

--4.7.20
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENTINFO_DELETE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_AGENTINFO_DELETE(IAGENTINFO_ID NUMBER(19) NOT NULL,IEQUIPMENT_OR_VM_ID NUMBER(19),IAGENT_NAME VARCHAR2(255),IAGENT_DES VARCHAR2(255),IAGENT_IP VARCHAR2(255) NOT NULL,IAGENT_PORT NUMBER(19),ICLUSTER_ID NUMBER(19),IDELETE_FLAG NUMBER(1) DEFAULT 0,IOS_NAME VARCHAR2(255),ICOM_NAME VARCHAR2(255),ISTART_USER VARCHAR2(255),IAGENT_STATE NUMBER(1),IAGENT_VERSION VARCHAR2(100),IAGENT_ACTIVITIES CLOB,IAGENT_ACTIVITY_NUM NUMBER(19),IAGENTUP_ID NUMBER(19),IPALIAS NUMBER(12),IENV_TYPE NUMBER(2) DEFAULT 1,IAGENT_CKSTATE NUMBER(1),IAGENT_CKTIME NUMBER(19),IAGENT_CCHANGE NUMBER(1),JOURNAL CLOB,IAGENT_IF_SENDMSG NUMBER(1) DEFAULT 1,IACTNUM NUMBER(10),IACTNUMMAX NUMBER(10),ICPUVALUE VARCHAR2(255),IMEMVALUE VARCHAR2(255),ICHECKTIME VARCHAR2(255),ICUSTOM_CMD VARCHAR2(1000),ICUSTOM_MESS CLOB,ICREATETIME NUMBER(19),ICREATEUSER NUMBER(19),ISSUED NUMBER(1) DEFAULT 0,IDELETEUSER VARCHAR2(255),IDELETETIME NUMBER(19),IDELETEDESC VARCHAR2(255),CONSTRAINT PK_IEAI_AGENTINFO_DELETE PRIMARY KEY(IAGENTINFO_ID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_EQU_GROUP_BUSINESS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_EQU_GROUP_BUSINESS(IID NUMBER(19) NOT NULL,EQUGROUPID NUMBER(19),BUSINESSID NUMBER(19),BUSINESSNAME VARCHAR2(255),PROTYPE NUMBER(5) ,CONSTRAINT PK_IEAI_EQU_GROUP_BUSINESS PRIMARY KEY(IID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;
    
  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_IEAI_DOUBLECHECK_COLVALUE' AND OBJECT_TYPE = 'INDEX';
  IF  LI_EXISTS = 0 THEN
  	  LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLECHECK_COLVALUE ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID)';
      EXECUTE IMMEDIATE LS_SQL;
  END  IF; 
	
	COMMIT;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_PROJECT_AGENTINFO' AND COLUMN_NAME = 'IAGENTIP';
		  IF LI_EXISTS = 1 THEN
		    LS_SQL := 'ALTER TABLE IEAI_PROJECT_AGENTINFO MODIFY IAGENTIP VARCHAR2(255)';
		    EXECUTE IMMEDIATE LS_SQL;
	  END IF; 
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=145;
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'UPDATE IEAI_HIGHOPER SET IBUTTONURL=''deleteFormationByCP.do'' WHERE IBUTTONID=145';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=146;
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'UPDATE IEAI_HIGHOPER SET IBUTTONURL=''saveFormationByCP.do'' WHERE IBUTTONID=146';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	commit;
	
		  SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_EXCEL_UT_REVIEW' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_EXCEL_UT_REVIEW(IID DECIMAL(19,0) NOT NULL,IFLOWID DECIMAL(19,0),IACTID DECIMAL(19,0),IACTNAME VARCHAR(255),IACYTYPE DECIMAL(2),IUSERNAME VARCHAR(255),IUSERID DECIMAL(19,0),IREVIEWTAKEOVERTIME DECIMAL(19,0),IREVIEWTIME DECIMAL(19,0),IREVIEWFINISHTIME DECIMAL(19,0),CONSTRAINT PK_IEAI_EXCEL_UT_REVIEW PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD IEXPBEGINTIME decimal(19,0) default 0';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD IEXPENDTIME decimal(19,0)  default 0';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD IREMARK VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPBEGINTIME decimal(19,0) default 0';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPENDTIME decimal(19,0)  default 0';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREMARK VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPBEGINTIME decimal(19,0) default 0';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPENDTIME decimal(19,0)  default 0';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREMARK VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

END;
/
--4.7.21

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID = 16;
	IF LI_EXISTS = 1 THEN
		 LS_SQL := 'UPDATE IEAI_GROUPMESSAGE SET IIMG = ''images/info171.png '' WHERE GROUPID=16';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
END;
/
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 20;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (20,20,''指令操作维护源'',0)';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_COLLECT_ITEM WHERE IID = -100;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_COLLECT_ITEM(IID, NAME, ITEM, WIN, LINUX, AIX, INFO, ISET)  VALUES(-100, ''主动采集对比'', '''', '''', '''', '''', '''', 1)';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID = -20;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED,IUPLOADNUM, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-20, 0, ''所有指令操作业务系统'', 0, 0, 0, 0, 0, 0, 20, -20, -20, -20)';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='INUMBERS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD INUMBERS VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO_DELETE' AND COLUMN_NAME='INUMBERS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENTINFO_DELETE ADD INUMBERS VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

--4.7.22
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
  SELECT SIGN(COUNT(*))
    INTO LI_EXISTS
    FROM USER_OBJECTS
   WHERE OBJECT_NAME = 'IEAI_CMDB_AGENTINFO_CZ'
     AND OBJECT_TYPE = 'TABLE';
  IF LI_EXISTS = 0 THEN
		 LS_SQL := 'CREATE TABLE IEAI_CMDB_AGENTINFO_CZ (IID NUMBER(19) NOT NULL,IHOSTNAME	VARCHAR2(255),IIP	VARCHAR2(255),IMAC	VARCHAR2(255),IMEMSIZE	VARCHAR2(255),IDISKSIZE	VARCHAR2(255),IOSSYSTEM	VARCHAR2(255),IOSARCHITECTURE	VARCHAR2(255),IOSVERSION	VARCHAR2(255),IOSDISTRO	VARCHAR2(255),IOSRELEASE	VARCHAR2(255),IINSTANCEID	VARCHAR2(255),IAGENTSTATUS	VARCHAR2(255),ICPUMODEL	VARCHAR2(255),CONSTRAINT PK_CMDB_AGENTINFO_CZ PRIMARY KEY (IID)) ';
		 EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
		SELECT SIGN(COUNT(*))  INTO LI_EXISTS  FROM USER_OBJECTS   WHERE OBJECT_NAME = 'IEAI_USER_ROLE_RELATION'  AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'CREATE TABLE IEAI_USER_ROLE_RELATION(IID  NUMBER(19) NOT NULL,  IUSERID  NUMBER(19),  IROLEID  NUMBER(19),  IVALIDSTARTTIME NUMBER(19),  IVALIDENDTIME   NUMBER(19),  IOPERUSERID     NUMBER(19),  IOPERTIME   NUMBER(19),  CONSTRAINT PK_IEAI_USER_ROLE_RELATION PRIMARY KEY (IID))';
		 EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IREVIEWER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD IREVIEWER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IREVIEWER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREVIEWER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IREVIEWER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREVIEWER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IBANKNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD IBANKNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IBANKNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IBANKNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IBANKNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IBANKNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='IREVIEWSTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD IREVIEWSTATE INTEGER default 1 ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IREVIEWSTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREVIEWSTATE INTEGER default 1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IREVIEWSTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD  IREVIEWSTATE INTEGER default 1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER' AND COLUMN_NAME='IPWDFORAPPLY';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_USER ADD IPWDFORAPPLY VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/
--4.7.23
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER' AND COLUMN_NAME='IBASEPASSWORD';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_USER ADD IBASEPASSWORD VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		commit;
		END IF;

	SELECT  COUNT(IID) INTO LI_EXISTS FROM IEAI_PROPERTYCONFIG P WHERE P.IPROPERTYNAME='AutoChangePublicPasswordTime';
	IF  LI_EXISTS = 0 THEN
    LS_SQL :='INSERT INTO IEAI_PROPERTYCONFIG(IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) VALUES(2,''AutoChangePublicPasswordTime'',''30'',''浦发自动变更public密码署时长设置'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDB_AGENTINFO_CZ' AND COLUMN_NAME='IAPPNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_CMDB_AGENTINFO_CZ ADD IAPPNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDB_AGENTINFO_CZ' AND COLUMN_NAME='ISTATUS';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_CMDB_AGENTINFO_CZ ADD ISTATUS NUMBER(3)';
        EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
    END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_MENU' AND COLUMN_NAME='IISAPP';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_MENU ADD IISAPP INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_GRID_INFO' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_GRID_INFO (IID NUMBER(19) NOT NULL,IGRIDNAME VARCHAR2(255),ICOLNAME VARCHAR2(255),IATTRIBUTE VARCHAR2(255),IATTRVALUE VARCHAR2(255),CONSTRAINT PK_IEAI_GRID_INFO PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
--4.7.24
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

	
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_IPMPSERVICEINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_IPMPSERVICEINFO(
					IID           NUMBER(19) NOT NULL,  
				  IRESOURCENAME VARCHAR2(255),  
				  IURL          VARCHAR2(1000),  
				  IUSERNAME     VARCHAR2(255),  
				  IPASSWORD     VARCHAR2(255),  
				  ISCOMMON      NUMBER(2) DEFAULT 0,  
				  ICREATEUSERID NUMBER(19), 
				  IUPDATEUSERID NUMBER(19),  
				  ICREATETIME   TIMESTAMP(6),  
				  IUPDATETIME   TIMESTAMP(6) DEFAULT SYSTIMESTAMP,  
				  IDELETE       NUMBER(2) DEFAULT 0,  
				  CONSTRAINT PK_IEAI_IPMPSERVICEINFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROXY_LIST' AND COLUMN_NAME='ISTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PROXY_LIST  ADD ISTATE NUMBER (2,0) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SHOW_SERVERINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SHOW_SERVERINFO (IID NUMBER(19) NOT NULL,ILISTID NUMBER(19),ICPU VARCHAR2(255),IMEMORY VARCHAR2(255),IDISK VARCHAR2(255),ITASKNUM NUMBER(19),ICREATETIME TIMESTAMP(6),CONSTRAINT PK_IEAI_SHOW_SERVERINFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SHOW_SERVERLIST' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SHOW_SERVERLIST (IID NUMBER(19) NOT NULL,IIP VARCHAR2(255),IDESC VARCHAR2(255),ISTARTTIME NUMBER(19),ICREATETIME NUMBER(19),CONSTRAINT PK_IEAI_SHOW_SERVERLIST PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_USER_PORTAL_MIDDLE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_USER_PORTAL_MIDDLE (IID NUMBER(19) NOT NULL,USERID NUMBER(19) NOT NULL,PROTALID NUMBER(19) NOT NULL,IROW NUMBER(2) NOT NULL,ICOLUMN NUMBER(3) NOT NULL,POSITION NUMBER(1) DEFAULT 1,CONSTRAINT PK_IEAI_USER_PORTAL_MIDDLE PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PORTAL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_PORTAL (IID NUMBER(19) NOT NULL,TITLE VARCHAR(225) NOT NULL,NAME VARCHAR(225) NOT NULL,DATAURL VARCHAR(100) ,FIELDURL VARCHAR(100) ,ITYPE NUMBER(2) NOT NULL,DES VARCHAR(225),CONSTRAINT PK_IEAI_PORTAL PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER' AND COLUMN_NAME='PORTALCLO';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_USER ADD PORTALCLO NUMBER(1) DEFAULT 3';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IEFFECT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IEFFECT NUMBER(1)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENT_DETAIL' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := ' CREATE TABLE IEAI_AGENT_DETAIL  ( IID NUMBER(19) NOT NULL,AGENTIP    	VARCHAR2(255),AGENTPORT  	NUMBER(19),ICREATETIME	NUMBER(19),AGENTDIR   	VARCHAR2(255),DISKSIZE   	VARCHAR2(255),LOGLOCATION	VARCHAR2(255),CPURATE    	VARCHAR2(255),MEMORYRATE 	VARCHAR2(255),IOMESS     	VARCHAR2(255),CONSTRAINT PK_IEAI_AGENT_DETAIL PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENTSYSTEM_PERSONINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_AGENTSYSTEM_PERSONINFO (IID NUMBER(19) NOT NULL,ISYSNAME VARCHAR2(255),IPERSONNAME VARCHAR2(255),IEMAIL VARCHAR2(255),IPHONE VARCHAR2(255),CONSTRAINT PK_IEAI_AGENTSYSTEM_PERSONINFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_TIMETASK_AUDIT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_AUDIT (IID NUMBER(19) NOT NULL, IPROJECTNAME VARCHAR2(255), ITASKNAME VARCHAR2(255), ITASKRUNTIME VARCHAR2(255), ITASKCOMMAND VARCHAR2(1000), ITASKGROUP VARCHAR2(255), ITASKDESC VARCHAR2(255), IRESOURCES VARCHAR2(255), IIP VARCHAR2(1800), IENABLE INTEGER, ISERVERIP VARCHAR2(255), IGID NUMBER(19), IOLDSERVERIP VARCHAR2(255), IRECOVERYTIME TIMESTAMP(6), ISTOPINSFLAG INTEGER, ITIMEOUTMINS NUMBER(19) DEFAULT 0, ISTARTFORBIDSTATUS INTEGER DEFAULT 1, IPAUSE INTEGER DEFAULT 0, ITASKTYPE INTEGER DEFAULT -1, ISTATUS NUMBER(1) DEFAULT 0, CONSTRAINT PK_IEAI_TIMETASK_AUDIT PRIMARY KEY (IID))';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
END;
/
--4.7.25
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SEGMENT' AND COLUMN_NAME='IFUNCTIONALTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SEGMENT  ADD IFUNCTIONALTYPE INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_INACRIVE_NUM';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENTINFO  ADD IAGENT_INACRIVE_NUM  NUMBER(19) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_COLLECTION_CLASSIFY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_COLLECTION_CLASSIFY (IID NUMBER(19) NOT NULL,ICLASSNAME VARCHAR2(255), DES VARCHAR2(1000),CONSTRAINT PK_IEAI_COLLECTION_CLASSIFY PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECT_ITEM' AND COLUMN_NAME='ISPARAMETER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COLLECT_ITEM  ADD ISPARAMETER NUMBER(2) DEFAULT 1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECT_DICTIONARY' AND COLUMN_NAME='IREMARK';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COLLECT_DICTIONARY  ADD IREMARK VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECT_DICTIONARY' AND COLUMN_NAME='CLASSID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COLLECT_DICTIONARY  ADD CLASSID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_IG_ENTITY_MODEL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_CMDB_IG_ENTITY_MODEL (IID NUMBER(19) NOT NULL,ENAME VARCHAR2(255),EID VARCHAR2(255),ESYSCODING VARCHAR2(255),EPARCODING VARCHAR2(255),EDESC VARCHAR2(255),IINSTIME NUMBER(19),IINSUSER VARCHAR2(255),IUPDATETIME NUMBER(19),IUPDATEUSER VARCHAR2(255),CONSTRAINT PK_IEAI_CMDB_IG_ENTITY_MODEL PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_IG_EQUIPMENT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_CMDB_IG_EQUIPMENT (IID NUMBER(19) NOT NULL,EID VARCHAR(255),EIUPDTIME VARCHAR2(50),ESYSCODING VARCHAR2(255),EIROOTMARK NUMBER(19),EIVERSION NUMBER(10),EISMALLVERSION NUMBER(10),EIDESC VARCHAR2(255),EISTATUS NUMBER(4),EIINSDATE VARCHAR2(50),EIIP VARCHAR2(18),EINAME VARCHAR2(255),EIID NUMBER(19),IINSTIME NUMBER(19),IINSUSER VARCHAR(255),IUPDATETIME NUMBER(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUIPMENT PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_IG_EQUI_RELATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_CMDB_IG_EQUI_RELATION (IID NUMBER(19) NOT NULL,RLCODE VARCHAR2(255),RLCATEGORY NUMBER(5),FROMEID VARCHAR2(255),TOEID VARCHAR2(255),RLDESC VARCHAR2(255),IINSTIME NUMBER(19),IINSUSER VARCHAR(255),IUPDATETIME NUMBER(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUI_RELATION PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_IG_FINAL_RELATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_CMDB_IG_FINAL_RELATION (IID NUMBER(19) NOT NULL,RLID NUMBER(19),RLCODE VARCHAR2(255),TOEIID NUMBER(19),RLCATEGORY NUMBER(4),FROMEIID NUMBER(19),UPDATETIME VARCHAR2(50),EIROOMARK VARCHAR2(255),IINSTIME NUMBER(19),IINSUSER VARCHAR(255),IUPDATETIME NUMBER(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_FINAL_RELATION PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_IG_SYS_RELATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_CMDB_IG_SYS_RELATION (IID NUMBER(19) NOT NULL,CMDBEIID NUMBER(19),IPRJUPPERID NUMBER(19),IUPDATETIME NUMBER(19),IOPERUSER VARCHAR2(255),CONSTRAINT PK_IEAI_CMDB_IG_SYS_RELATION PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUI_RELATION_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUI_RELATION_01 ON IEAI_CMDB_IG_EQUI_RELATION(RLCODE)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_FINAL_RELATION_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_01 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,TOEIID)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_FINAL_RELATION_02' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_02 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,FROMEIID)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUIPMENT_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_01 ON IEAI_CMDB_IG_EQUIPMENT(EIID)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_ENTITY_MODEL_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_01 ON IEAI_CMDB_IG_ENTITY_MODEL(ESYSCODING)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_ENTITY_MODEL_02' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_02 ON IEAI_CMDB_IG_ENTITY_MODEL(ENAME)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUIPMENT_02' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_02 ON IEAI_CMDB_IG_EQUIPMENT(ESYSCODING)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_FINAL_RELATION_03' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_03 ON IEAI_CMDB_IG_FINAL_RELATION(FROMEIID,TOEIID)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_SYS_RELATION_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_SYS_RELATION_01 ON IEAI_CMDB_IG_SYS_RELATION(IPRJUPPERID,CMDBEIID)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_CMDB_IG_EQUIPMENT_03' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_03 ON IEAI_CMDB_IG_EQUIPMENT (EIID, EIIP, EINAME)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
END;
/

--4.7.26

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PORTAL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_PORTAL (IID NUMBER(19) NOT NULL,TITLE VARCHAR(225) NOT NULL,NAME VARCHAR(225) NOT NULL,DATAURL VARCHAR(100),FIELDURL VARCHAR(100),ITYPE NUMBER(2),DES VARCHAR(225),CUSTOMCLASS VARCHAR(225),CONSTRAINT PK_IEAI_PORTAL PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	ELSE
		LS_SQL := 'DROP TABLE IEAI_PORTAL';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PORTAL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_PORTAL (IID NUMBER(19) NOT NULL,TITLE VARCHAR(225) NOT NULL,NAME VARCHAR(225) NOT NULL,DATAURL VARCHAR(100),FIELDURL VARCHAR(100),ITYPE NUMBER(2),DES VARCHAR(225),CUSTOMCLASS VARCHAR(225),CONSTRAINT PK_IEAI_PORTAL PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_BUSI_MODULE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_BUSI_MODULE (IMODULEID NUMBER(19) NOT NULL,IMODULECODE VARCHAR2(30),IMODULENAME VARCHAR2(30),IREMARK VARCHAR2(300),IDELETEFLAG NUMBER(1),CONSTRAINT PK_IEAI_BUSI_MODULE PRIMARY KEY (IMODULEID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_BUSI_TYPE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_BUSI_TYPE (ITYPEID NUMBER(19) NOT NULL,ITYPECODE VARCHAR2(30),ITYPENAME VARCHAR2(30),IREMARK VARCHAR2(300),IDELETEFLAG NUMBER(1),CONSTRAINT PK_IEAI_BUSI_TYPE PRIMARY KEY (ITYPEID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_BUSI_LEVEL' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_BUSI_LEVEL (ILEVELID NUMBER(19) NOT NULL,ILEVELCODE VARCHAR2(30),ILEVELNAME VARCHAR2(30),IREMARK VARCHAR2(300),IDELETEFLAG NUMBER(1),CONSTRAINT PK_IEAI_BUSI_LEVEL PRIMARY KEY (ILEVELID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN_SCENE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_WARN_SCENE (ISCENEID NUMBER(19) NOT NULL,IMODULECODE VARCHAR2(30),ITYPECODE VARCHAR2(30),ILEVELCODE VARCHAR2(30),ISTATUS NUMBER(1) DEFAULT 0,CONSTRAINT PK_IEAI_WARN_SCENE PRIMARY KEY (ISCENEID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'create table IEAI_WARN( iwarnid NUMBER(19) not null, imodulecode VARCHAR2(30),itypecode VARCHAR2(30), ilevelcode VARCHAR2(30), iip VARCHAR2(30), ihappentime DATE, iwarnmsg VARCHAR2(4000), createtime DATE, constraint PK_IEAI_WARN primary key (iwarnid))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_NOTICE_MANAGE' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'CREATE TABLE IEAI_NOTICE_MANAGE(IID NUMBER(19) NOT NULL,NOTICENAME VARCHAR2(225),NOTICECONTENT VARCHAR2(3000),ISTATE NUMBER(2) DEFAULT 1,TAKEEFFECTTIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,INVALIDTIME TIMESTAMP,USERID NUMBER(19) NOT NULL,CONSTRAINT PK_IEAI_NOTICE_MANAGE PRIMARY KEY (IID))';
	     EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PLATFORM_NEWS' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'CREATE TABLE IEAI_PLATFORM_NEWS(IID NUMBER(19) NOT NULL,NEWSSOURCE NUMBER(2),NEWCONTENT VARCHAR2(2000),ICREATETIME TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,USERID NUMBER(19) NOT NULL,IENDTIME TIMESTAMP(6),PROJECTID NUMBER(19),CONSTRAINT PK_IEAI_PLATFORM_NEWS PRIMARY KEY (IID))';
	     EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_USER_QUICK_MIDDLE' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'CREATE TABLE IEAI_USER_QUICK_MIDDLE(IID NUMBER(19) NOT NULL,QUICKID NUMBER(19)NOT NULL,USERID NUMBER(19),ISNODEF NUMBER(2),ICON VARCHAR2(255),CONSTRAINT PK_IEAI_USER_QUICK_MIDDLE PRIMARY KEY (IID))';
	     EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ORGMANAGEMENT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_ORGMANAGEMENT (IID NUMBER(19) NOT NULL,INAME VARCHAR2(255),IPARENTID NUMBER(19),CONSTRAINT PK_IEAI_ORGMANAGEMENT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ORGMANAGEMENT_USER' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_ORGMANAGEMENT_USER (IID NUMBER(19) NOT NULL,MANAGEMENTID NUMBER(19),USERID NUMBER(19),CONSTRAINT PK_IEAI_ORGMANAGEMENT_USER PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_FILECLASS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FILECLASS (IID NUMBER(19) NOT NULL,ICLASSNAME VARCHAR2(255),ICLASSDESC VARCHAR2(255),ICREATETIME TIMESTAMP,IPARENTID NUMBER(19),CONSTRAINT PK_IEAI_FILECLASS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_FILE_USER' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FILE_USER (IID NUMBER(19) NOT NULL,IFILEID NUMBER(19),IUSERID NUMBER(19),CONSTRAINT PK_IEAI_FILE_USER PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_FILEINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FILEINFO (IID NUMBER(19) NOT NULL,ICLASSIDONE NUMBER(19),ICLASSIDTWO NUMBER(19),IFILENAME VARCHAR2(255),IFILETYPE VARCHAR2(255),IFILECONTENT BLOB,IFILEVERSION NUMBER(19),IFILEDESC VARCHAR2(255),IUPLOADTIME TIMESTAMP,IUPLOADUSERID NUMBER(19),ISTATE NUMBER(1),CONSTRAINT PK_IEAI_FILEINFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_FILEINFO_HIS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FILEINFO_HIS (IID NUMBER(19) NOT NULL,IMYFILEID NUMBER(19),ICLASSIDONE NUMBER(19),ICLASSIDTWO NUMBER(19),IFILENAME VARCHAR2(255),IFILETYPE VARCHAR2(255),IFILECONTENT BLOB,IFILEVERSION NUMBER(19),IFILEDESC VARCHAR2(255),IUPLOADTIME TIMESTAMP,IUPLOADUSERID NUMBER(19),ISTATE NUMBER(1),CONSTRAINT PK_IEAI_FILEINFO_HIS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AUDIT_DATADICTIONARY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_AUDIT_DATADICTIONARY (IID NUMBER(19) NOT NULL,URL VARCHAR2(500),MODELTYPE NUMBER(19),KEYNAME VARCHAR2(100),CNNAME VARCHAR2(100),DICTIONARYDESC VARCHAR2(4000),CONSTRAINT PK_IEAI_AUDIT_DATADICTIONARY PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	
	
	
	
	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PORTAL_TOP' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_PORTAL_TOP (IID NUMBER(19) NOT NULL,EQUIPMENTNUM NUMBER(19),ONLINEUSERNUM NUMBER(5),PROCESSRELEASE NUMBER(19),SCRIPTNUM NUMBER(19),SYSTEMNUM NUMBER(19),USERLOGINNUM NUMBER(19),CONSTRAINT PK_IEAI_PORTAL_TOP PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_DICTIONARY  WHERE DID = 998;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_DICTIONARY VALUES(998,0,998,''消息展示天数'',15)';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;

	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENTSHELL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_AGENTSHELL (IID NUMBER(19) NOT NULL, IAGENTINFO_ID NUMBER(19) NOT NULL, ICHECKTIME NUMBER(19),ISTATE NUMBER(1), IMESSAGE CLOB, CONSTRAINT PK_IEAI_AGENTSHELL PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_STANDA_SCREEN_LIST' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_STANDA_SCREEN_LIST (IID NUMBER(19) NOT NULL,TASKID NUMBER(19),TASKNAME VARCHAR(225),STRATEGYTYPE NUMBER(2),INAME VARCHAR(225),STATUS VARCHAR(225),CONSTRAINT PK_IEAI_STANDA_SCREEN_LIST PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_STANDA_SCREEN_STATUS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_STANDA_SCREEN_STATUS (IID NUMBER(19) NOT NULL,STATUS VARCHAR(50),TOTAL NUMBER(10),CONSTRAINT PK_IEAI_STANDA_SCREEN_STATUS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_STANDA_SCREEN_DETAILS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_STANDA_SCREEN_DETAILS (IID NUMBER(19) NOT NULL,IP VARCHAR(225),TASKNAME VARCHAR(225),TASKID NUMBER(19),STATUS VARCHAR(225),CPNAME VARCHAR(225),STRATEGYTYPE NUMBER(1),IFLOWID NUMBER(19),ISTARTUSER VARCHAR(225),IEXECUSER VARCHAR(225),PERFORMUSER VARCHAR(225),CONSTRAINT PK_IEAI_STANDA_SCREEN_DETAILS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_STANDA_SCREEN_DETAILS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_STANDA_SCREEN_DETAILS (IID NUMBER(19) NOT NULL,IP VARCHAR(225),TASKNAME VARCHAR(225),TASKID NUMBER(19),STATUS VARCHAR(225),CPNAME VARCHAR(225),STRATEGYTYPE NUMBER(1),IFLOWID NUMBER(19),ISTARTUSER VARCHAR(225),IEXECUSER VARCHAR(225),PERFORMUSER VARCHAR(225),CONSTRAINT PK_IEAI_STANDA_SCREEN_DETAILS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=1;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''1'', ''日常操作-执行中的运维监控'', ''collectEnvironment'', ''getDmRunningList.do'', ''getColurltaskMinitor.do'', ''1'', ''日常操作-执行中的运维监控'', NULL)';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=2;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''2'', ''日常操作-待执行的运维监控'', ''taskMonitor'', ''getDmNotRunList.do'', ''getTaskMontior.do'', ''1'', ''日常操作-待执行的运维监控'', NULL)';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=3;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''3'', ''标准运维统计'', ''standardLineChart'', NULL, NULL, ''0'', NULL, ''Ext.app.StandardLineChart'')';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=4;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''4'', ''脚本服务化'', ''scriptServerLineChart'', NULL, NULL, ''0'', NULL, ''Ext.app.ScriptServerLineChart'')';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;
	

	
END;
/

DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_GRID_INFO' AND COLUMN_NAME='ILOGINNAME';
		IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_GRID_INFO ADD ILOGINNAME VARCHAR2(255)';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_GRID_INFO' AND COLUMN_NAME='ITYPE';
		IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_GRID_INFO ADD ITYPE NUMBER(1)';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_GRID_INFO' AND COLUMN_NAME='IWIDTH';
		IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_GRID_INFO ADD IWIDTH VARCHAR2(10)';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_COLLECT_ENVIRONMENT' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'create table IEAI_COLLECT_ENVIRONMENT
			(
			  IID               NUMBER(14) not null,
			  INAME         	VARCHAR2(32),
			  IP      			VARCHAR2(25)
			)';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
END;
/

DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AUDIT' AND COLUMN_NAME='HOSTNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AUDIT ADD HOSTNAME VARCHAR(225)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_STANDA_SCREEN_CHART' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_STANDA_SCREEN_CHART (IID NUMBER(19) NOT NULL,TOTALFLWO NUMBER(19),TORUNFLWO NUMBER(19),ENDFLWO NUMBER(19),CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,CONSTRAINT PK_IEAI_STANDA_SCREEN_CHART PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;


END;
/
--4.7.27
DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENCYTASK' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_AGENCYTASK (IID NUMBER(19) NOT NULL, SUM_NO VARCHAR2(255),SUM_REMARK VARCHAR2(255),SUM_CONTENT VARCHAR2(255),MODEL_TYPE VARCHAR2(255),MODEL_NAME VARCHAR2(255),OPER_ID VARCHAR2(255),USER_ID VARCHAR2(255),CALL_ID VARCHAR2(255),CALL_NAME VARCHAR2(255),CALL_DEPT VARCHAR2(255), CREATE_TIME VARCHAR2(255), DUE_TIME VARCHAR2(255),TX_TIME VARCHAR2(255),TX_CODE VARCHAR2(255),TRADE_CODE VARCHAR2(255),CLI_SERIAL_NO VARCHAR2(255),TERMI_MARK VARCHAR2(255),MAC_NAME VARCHAR2(255),MAC_KEY_NAME VARCHAR2(255),MAC_DATA VARCHAR2(255),RESERVE VARCHAR2(255),AUDIT_STATE NUMBER(2),EXEC_STATE NUMBER(2),RECORD0_CNT VARCHAR2(255),RECORD0 VARCHAR2(255),RECORD1_CNT VARCHAR2(255),RECORD1 VARCHAR2(255),CONSTRAINT PK_IEAI_AGENCYTASK PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_BATCH_UPGRADE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_BATCH_UPGRADE (IID NUMBER(19),ISRCPATH VARCHAR2(255),IDESTPATH VARCHAR2(255),IBAKPATH VARCHAR2(255),IOSTYPE NUMBER(1),CONSTRAINT PK_IEAI_BATCH_UPGRADE PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=29;
		IF  LI_EXISTS = 0 THEN
			LS_SQL :='INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(29,''batchUpgradeFtpInfo'',''-1'')';
			EXECUTE IMMEDIATE LS_SQL;
		END  IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AUDIT' AND COLUMN_NAME='GROUPNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AUDIT ADD GROUPNAME VARCHAR(225)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_STANDARD_TASK' AND COLUMN_NAME='INSTANCETIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_STANDARD_TASK ADD INSTANCETIME NUMBER(19)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TEMP_IEAI_DM_TASKLIST' AND COLUMN_NAME='INSTANCETIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE TEMP_IEAI_DM_TASKLIST ADD INSTANCETIME NUMBER(19)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		COMMIT;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ISTEPIDENTITY';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ISTEPIDENTITY VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDBINFO' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_CMDBINFO (
										IID Number(19) NOT NULL,
										IINSTANCEID  Varchar2(255),
										IIP 	Varchar2(255),
										IHOSTNAME Varchar2(255),
										IPORT	Varchar2(50),
										IOPERATESYSTEM	Varchar2(255),
										ISYSTEMVERSION	Varchar2(255),
										ICPU	Varchar2(255),
										IARCHITECTURE	Varchar2(255),
										IPHYSICALCORES	Varchar2(10),
										ILOGICALCORES	Varchar2(10),
										IMEMORYSIZE	Varchar2(100),
										IEQUIPMENT  Varchar2(255),
										IOUTBANDIP 	Varchar2(255),
									  IAGENTSTATUS VARCHAR2(255),
									  IEQUTYPE VARCHAR2(2),
										CONSTRAINT PK_IEAI_CMDBINFO PRIMARY KEY (IID)
									)';
			EXECUTE IMMEDIATE LS_SQL;
		 END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDBCLOB_LOCAL' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_CMDBCLOB_LOCAL  (
								   IID Number(19) NOT NULL,   
								   ICMDBINFOID Varchar2(19), 
								   IINSTANCEID  Varchar2(19),  
								   ICOLUMNNAME Varchar2(255), 
								   ITYPE   Varchar2(255), 
								   INULL INTEGER,  
								   ICONTENT CLOB,  
								   IAID Number(19),  
								   ICPID Number(19),   
								   IEQUTYPE VARCHAR2(2), 
								CONSTRAINT PK_IEAI_CMDBCLOB_LOCAL PRIMARY KEY (IID)
								)';
			EXECUTE IMMEDIATE LS_SQL;
		 END IF;   	 

			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDBCLOB' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_CMDBCLOB  (
								   IID Number(19) NOT NULL,   
								   ICMDBINFOID Varchar2(19), 
								   IINSTANCEID  Varchar2(19),  
								   ICOLUMNNAME Varchar2(255), 
								   ITYPE   Varchar2(255), 
								   INULL INTEGER,  
								   ICONTENT CLOB,  
								   IAID Number(19),  
								   ICPID Number(19),   
								   IEQUTYPE VARCHAR2(2), 
								CONSTRAINT PK_IEAI_CMDBCLOB PRIMARY KEY (IID)
								)';
			EXECUTE IMMEDIATE LS_SQL;
		 END IF;  

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDBCLOB_SUS' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_CMDBCLOB_SUS  (
								   IID Number(19) NOT NULL, 
								   INULL INTEGER,  
								   ICONTENT CLOB , 
								CONSTRAINT PK_IEAI_CMDBCLOB_SUS PRIMARY KEY (IID)
								)';
			EXECUTE IMMEDIATE LS_SQL;
		 END IF;  
		 
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN_DETAIL' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_WARN_DETAIL (IID NUMBER(19) NOT NULL,ISENCEID NUMBER(19),IWARNTYPE VARCHAR2(255),ITYPE VARCHAR2(255),CONSTRAINT PK_IEAI_WARN_DETAIL PRIMARY KEY(IID))';
		 EXECUTE IMMEDIATE LS_SQL;
		 END IF;
		 
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_USER_RELATION' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_USER_RELATION (IID NUMBER(19) NOT NULL,IUSERID NUMBER(19),ISENCEID NUMBER(19),CONSTRAINT PK_IEAI_USER_RELATION PRIMARY KEY(IID))';
		 EXECUTE IMMEDIATE LS_SQL;
		 END IF;
		 
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_WARN' AND COLUMN_NAME='ISYSTEMNAME';
		 IF LI_EXISTS = 0 THEN
		 LS_SQL := 'ALTER TABLE IEAI_WARN ADD ISYSTEMNAME VARCHAR2(255)';
		 EXECUTE IMMEDIATE LS_SQL;
		 END IF;
		 
		 
		COMMIT;
END;
/

--4.7.28


DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER_QUICK_MIDDLE' AND COLUMN_NAME='IORDER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_USER_QUICK_MIDDLE ADD IORDER NUMBER(2)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AUDIT_DATADICTIONARY' AND COLUMN_NAME='SQLTEXT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AUDIT_DATADICTIONARY ADD SQLTEXT VARCHAR2(4000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AUDIT' AND COLUMN_NAME='IIDARR';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AUDIT ADD IIDARR VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_STANDA_SCREEN_DETAILS' AND COLUMN_NAME='CREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_STANDA_SCREEN_DETAILS ADD CREATETIME TIMESTAMP(6)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SYNCCMDB_INFO' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_SYNCCMDB_INFO (IID NUMBER(19) NOT NULL,SERVICERUNFLAG  VARCHAR2(4),CRONVALUE	VARCHAR2(20),SYNC_IP_COUNT  VARCHAR2(10),TIMINGJOBFLAG  VARCHAR2(4),CMDBTHREADFLAG  VARCHAR2(4),CONSTRAINT PK_IEAI_SYNCCMDB_INFO PRIMARY KEY (IID))';
      EXECUTE IMMEDIATE LS_SQL;
    END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENCYTASK' AND COLUMN_NAME='RECORD_NUM';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENCYTASK ADD RECORD_NUM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENCYTASK' AND COLUMN_NAME='RECORD_KEY';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENCYTASK ADD RECORD_KEY VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	COMMIT;
END;
/


DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SYNCCMDB_INFO;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_SYNCCMDB_INFO (IID,SERVICERUNFLAG,CRONVALUE,SYNC_IP_COUNT,TIMINGJOBFLAG,CMDBTHREADFLAG) VALUES (1,''0'',''0 0 0 1-31 * ?'',''100'',''0'',''1'')';
		 EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	COMMIT;

END;
/

--4.7.29


DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENCYTASK' AND COLUMN_NAME='AUDIT_USER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENCYTASK ADD AUDIT_USER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_WARN_DETAIL' AND COLUMN_NAME='IMSGTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_WARN_DETAIL ADD IMSGTYPE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_WARN' AND COLUMN_NAME='ICOMPARERULE';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_WARN ADD ICOMPARERULE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_WARN' AND COLUMN_NAME='IHOSTNAME';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_WARN ADD IHOSTNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN_USERS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_WARN_USERS (IID NUMBER(19) NOT NULL,IWARNID NUMBER(19),IUSERNAME VARCHAR2(255),ICONNTYPE VARCHAR2(255),CONSTRAINT PK_IEAI_WARN_USERS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN_COLUMNS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_WARN_COLUMNS (IID NUMBER(19) NOT NULL,ICOLUMNKEY VARCHAR2(255),ICOLUMNVALUE VARCHAR2(255),ISCENEID NUMBER(19),ITYPE VARCHAR2(255),CONSTRAINT PK_IEAI_WARN_COLUMNS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN_MESSAGE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_WARN_MESSAGE (IID NUMBER(19) NOT NULL,IWARNID NUMBER(19),ICOLUMNKEY VARCHAR2(255),ICOLUMNVALUE VARCHAR2(255),ISCENEID NUMBER(19),IVALUE CLOB,CONSTRAINT PK_IEAI_WARN_MESSAGE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ANNOUNCEMENT_USER' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_ANNOUNCEMENT_USER (IID NUMBER(19) NOT NULL,USERID NUMBER(19),ITYPE VARCHAR2(255),CONSTRAINT PK_IEAI_ANNOUNCEMENT_USER PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_GUARDPORT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IAGENT_GUARDPORT NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	COMMIT;
	
	END;
/
	
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN
	
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=23;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES(23,23,''数据采集源'','''','''','''','''',0)';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_AZNAME';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IAGENT_AZNAME VARCHAR2(255)';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_NETID';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IAGENT_NETID VARCHAR2(255) default ''(无)''';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='IAGENT_NETID';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_COMPUTER_LIST ADD IAGENT_NETID VARCHAR2(255) default ''(无)''';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_NETID';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IAGENT_NETID VARCHAR2(255) default ''(无)''';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;




	COMMIT;
END;
/



DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN
	
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_IEAI_COMPUTER_LIST_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 1 THEN
	LS_SQL := 'DROP INDEX IDX_IEAI_COMPUTER_LIST_01';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IDX_IEAI_COMPUTER_LIST_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST (IP ASC)';
	EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	COMMIT;
END;
/
DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
	
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PROXY_VMIP' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_PROXY_VMIP(IID DECIMAL(19,0) NOT NULL,IPROXYID DECIMAL(19,0) NOT NULL,IUSERID DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),ISWITCHIP VARCHAR2(25),ISWITCHOUTIP VARCHAR2(25),CONSTRAINT PK_IEAI_PROXY_VMIP PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_EXTJS_KEYVAL' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_EXTJS_KEYVAL(IID DECIMAL(19,0) NOT NULL,IEXTKEY VARCHAR2(4000),IEXTVAL VARCHAR2(4000),CONSTRAINT PK_IEAI_EXTJS_KEYVAL PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='ICMDB_UPDATE_LASTTIME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COMPUTER_LIST ADD ICMDB_UPDATE_LASTTIME VARCHAR2(50)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='ICMDB_UPDATE_DESC';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COMPUTER_LIST ADD ICMDB_UPDATE_DESC VARCHAR2(50)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='ICMDB_DELIVER_TIME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COMPUTER_LIST ADD ICMDB_DELIVER_TIME VARCHAR2(50)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECT_ITEM' AND COLUMN_NAME='CRONVALUE';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_COLLECT_ITEM ADD CRONVALUE VARCHAR2(50) ';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECT_RESULT_LATEST' AND COLUMN_NAME='SCRIPT_NAME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COLLECT_RESULT_LATEST ADD SCRIPT_NAME VARCHAR2(255)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COLLECT_ITEM' AND COLUMN_NAME='ISCRONTASK';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COLLECT_ITEM ADD ISCRONTASK NUMBER(2,0)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='ICMDBKFCS_UPDATE_LASTTIME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COMPUTER_LIST ADD ICMDBKFCS_UPDATE_LASTTIME VARCHAR2(50)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='RECYCLE_STATUS';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_COMPUTER_LIST ADD RECYCLE_STATUS VARCHAR2(50)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_PROJECT_CONFIG' AND COLUMN_NAME='ICHARGEUSERMAIL';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'alter table IEAI_AGENT_PROJECT_CONFIG ADD ICHARGEUSERMAIL VARCHAR2(50)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_PROPERTYCONFIG';
	IF  LI_EXISTS = 0 THEN
    LS_SQL :='INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_PROPERTYCONFIG'',1000)';
    EXECUTE IMMEDIATE LS_SQL;
	ELSE
		LS_SQL :='UPDATE IEAI_ID SET IVALUE =1000 WHERE IVALUE <1000 AND ICLASSNAME=''IEAI_PROPERTYCONFIG''' ;
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	COMMIT;
	
	END;
/
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_MONITOR_STATE';
		IF LI_EXISTS = 0 THEN
		    LS_SQL := 'alter table IEAI_AGENTINFO add IAGENT_MONITOR_STATE NUMBER(1)';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
  	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SCRIPT_SQL_RESULT' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := ' CREATE TABLE IEAI_SCRIPT_SQL_RESULT(IID  NUMBER(19) NOT NULL, IFOREIGNID VARCHAR2(100), IFOREIGNHEADID NUMBER(19), IRESULT CLOB, ISQLKEY VARCHAR2(255), IFROM NUMBER(1),ISQL CLOB, CONSTRAINT PK_IEAI_SCRIPT_SQL_RESULT PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		 	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SCRIPT_SQL_HEAD' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_SQL_HEAD( IID NUMBER(19) NOT NULL, IFOREIGNID VARCHAR2(100), IRESULT CLOB,IFROM NUMBER(1),ISQLKEY VARCHAR2(255),CONSTRAINT PK_IEAI_SCRIPT_SQL_HEAD PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPER_RESULT' AND COLUMN_NAME='ISQL';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD (ISQL CLOB)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
--20201010


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDBINFO' AND COLUMN_NAME='BUSINESSNAME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_CMDBINFO ADD BUSINESSNAME VARCHAR(225)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDBINFO' AND COLUMN_NAME='ENVIRONMENT';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_CMDBINFO ADD ENVIRONMENT VARCHAR(225)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDBINFO' AND COLUMN_NAME='IPZX';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_CMDBINFO ADD IPZX VARCHAR(225)';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDBLABEL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_CMDBLABEL (IID NUMBER(19) NOT NULL,HOSTTYPE VARCHAR(10),LABELNAME VARCHAR(225),TYPE VARCHAR(10),CONSTRAINT PK_IEAI_CMDBLABEL PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;   
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM ALL_TAB_COLS WHERE TABLE_NAME = 'IEAI_CMDBCLOB_LOCAL'  AND COLUMN_NAME='ICONTENT' AND DATA_TYPE = 'CLOB';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_CMDBCLOB_LOCAL DROP COLUMN ICONTENT';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF; 
	COMMIT;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDBCLOB_LOCAL' AND COLUMN_NAME='ICONTENT';
		IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_CMDBCLOB_LOCAL ADD ICONTENT VARCHAR(1000)';
	    EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
END;
/

----8.3.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATASYNC_TABLEINFO' AND COLUMN_NAME='IDMARK';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_DATASYNC_TABLEINFO ADD IDMARK VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVERLIST' AND COLUMN_NAME='IDISK';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVERLIST ADD IDISK VARCHAR2(20)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAGENT_MONITOR_STATE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IAGENT_MONITOR_STATE NUMBER (19)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/

----8.4.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PARAMETER_MANAGER' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_PARAMETER_MANAGER (IID NUMBER(19) NOT NULL, IPARAMNAME VARCHAR2(255),IPARAMVALUE VARCHAR2(255),IPARAMDESC VARCHAR2(255),ISCOPE VARCHAR2(255), ICREATEUSERID NUMBER(19,0),IUPDATEUSERID NUMBER(19,0),ICREATETIME DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),CONSTRAINT PK_IEAI_PARAMETER_MANAGER PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	COMMIT;


    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDBCLOB_LOCAL' AND COLUMN_NAME='HOSTTYPE';
        IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_CMDBCLOB_LOCAL ADD HOSTTYPE VARCHAR2(10)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDBCLOB' AND COLUMN_NAME='HOSTTYPE';
        IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_CMDBCLOB ADD HOSTTYPE VARCHAR2(10)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
END;
/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IOSBASEVALUE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IOSBASEVALUE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_WARN_AGENT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_WARN_AGENT (IID NUMBER (19) NOT NULL,IAGENTID NUMBER (19) ,IWARNTYPE VARCHAR2 (5) ,IWARNCOUNT NUMBER (19) ,IFIRSTTIME NUMBER (19) ,ILASTTIME NUMBER (19) , CONSTRAINT PK_IEAI_WARN_AGENT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN  
  SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_MESSAGE' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	          LS_SQL := 'CREATE TABLE IEAI_CMDB_MESSAGE  ( 
												IID     	NUMBER(19) NOT NULL,
												IP VARCHAR2(255),
												IHOSTNAME VARCHAR2(255),
												ISYSTEMINFO VARCHAR2(255),
												ICENTERNAME VARCHAR2(255),
												ISYSADMIN VARCHAR2(255),
												IAPPADMIN VARCHAR2(255),
												ICOLLECTRESULT VARCHAR2(255),
												IKEYNAME VARCHAR2(255),
												ICMDB_UPDATE_DESC VARCHAR2(50),
												ICMDB_UPDATE_LASTTIME VARCHAR2(50),  
												CONSTRAINT PK_IEAI_CMDB_MESSAGE PRIMARY KEY(IID)
											)';
			  EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/


		 
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENT_START_STOP' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_AGENT_START_STOP (IID NUMBER (19) ,IAGENTIP VARCHAR2 (255) ,IAGENTPORT NUMBER (19) ,IOSNAME VARCHAR2 (255) ,ISTATE NUMBER (19) ,IUSERNAME VARCHAR2 (255) ,ITIME NUMBER (19) ,ITYPE NUMBER (19) ,IUPDATESTATE NUMBER (19) ,IMESSAGE VARCHAR2 (255) , CONSTRAINT PK_IEAI_AGENT_START_STOP PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_IPMP_SYSTEM_RELATON' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_IPMP_SYSTEM_RELATON (IID NUMBER (19) NOT NULL,IPROJECTID NUMBER (19),IPMPSYSNAME VARCHAR2 (255), CONSTRAINT PK_IEAI_IPMP_SYSTEM_RELATON PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_LOGINRANGE_VALIDATION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_LOGINRANGE_VALIDATION (IID NUMBER (19) NOT NULL ,IIPRANGE VARCHAR2 (255) ,CREATORUSER VARCHAR2 (255) ,CREATORDATE NUMBER (19) ,UPDATEUSER VARCHAR2 (255) ,UPDATEDATE NUMBER (19) , CONSTRAINT PK_IEAI_LOGINRANGE_VALIDATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/



------8.6.0-----
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BATCH_UPGRADE' AND COLUMN_NAME='IUPDATEVERSION'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_BATCH_UPGRADE ADD IUPDATEVERSION VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BATCH_UPGRADE' AND COLUMN_NAME='IACTFINISHMAXWAIT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_BATCH_UPGRADE ADD IACTFINISHMAXWAIT NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BATCH_UPGRADE' AND COLUMN_NAME='IWRITEBUFFERSIZE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_BATCH_UPGRADE ADD IWRITEBUFFERSIZE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME='UPDATEVERSION'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTUPDATE_INFO ADD UPDATEVERSION VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME='ACTFINISHMAXWAIT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTUPDATE_INFO ADD ACTFINISHMAXWAIT NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME='WRITEBUFFERSIZE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTUPDATE_INFO ADD WRITEBUFFERSIZE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='IUPDATEVERSION'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IUPDATEVERSION VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='IACTFINISHMAXWAIT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IACTFINISHMAXWAIT NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='IWRITEBUFFERSIZE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD IWRITEBUFFERSIZE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ENTEGOR_CONFIG' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ENTEGOR_CONFIG (IID NUMBER (19) ,IKEY VARCHAR2 (255) ,IVALUE VARCHAR2 (255) ,IDES VARCHAR2 (1500) ,ITYPE NUMBER (19) ,ISTATUS NUMBER (19) ,IDEFAULT VARCHAR2 (255) , CONSTRAINT PK_IEAI_ENTEGOR_CONFIG PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
--8.7.0--
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_LOGIN_KAPTCHA' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_LOGIN_KAPTCHA(IID DECIMAL(19,0) NOT NULL, IURL VARCHAR2(255), USERID DECIMAL(19,0),OPERTIME DECIMAL(19,0),ISTATE INTEGER,IP VARCHAR2(25),IMAC VARCHAR2(25),HOSTNAME VARCHAR2(255),IKAPTCHA VARCHAR2(10),CONSTRAINT PK_IEAI_LOGIN_KAPTCHA PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 50008;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(50008, ''失效备援信息'', 100, ''lostthenhelplist.do'', 2, '''', ''images/info81.png'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;

END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='SYSTEMSTATUS'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD SYSTEMSTATUS VARCHAR2(100) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='CPNAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_COMPUTER_LIST MODIFY COLUMN CPNAME VARCHAR2(255)  '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 2261;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(2261, ''告警看板'', 100, ''initIeaiWarnViewPage.do'', 2261, ''告警管理'', ''images/info83.png'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;

END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SYS_DICTIONARY WHERE DID = 200;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_SYS_DICTIONARY(DID, DNAME, DTYPE, DINTVALUE) VALUES(200, ''LOGINCONFIG'',  ''INTEGER'',  ''0'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;

END;
/
----8.9-----

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
 	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROXY_LIST' AND COLUMN_NAME='IVERSION';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_PROXY_LIST ADD IVERSION VARCHAR2(25)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_PUBLIC_NOTICE_MESSAGE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_PUBLIC_NOTICE_MESSAGE (IID NUMBER (19) NOT NULL,INOTICEINFO VARCHAR2 (255),IEXECUCYCLE VARCHAR2 (255),IISSHOW NUMBER (1), CONSTRAINT PK_IEAI_PUBLIC_NOTICE PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_DEPT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_DEPT (IID NUMBER (19) ,IDEPTID NUMBER (19) ,IDEPTNAME VARCHAR2 (255) ,ICREATETIME VARCHAR2 (255) ,IPARENTID NUMBER (19) ,IDEPTDESC VARCHAR2 (255) , CONSTRAINT PK_IEAI_DEPT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
---V8.9
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='ISENDMAILTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE  IEAI_COMPUTER_LIST ADD ISENDMAILTIME VARCHAR2(100)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
 	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='IHOST_TYPE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE  IEAI_COMPUTER_LIST ADD IHOST_TYPE VARCHAR2(100)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SERVER_MESSAGE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := ' CREATE TABLE IEAI_SERVER_MESSAGE (IID NUMBER(19,0) NOT NULL,SERVERIP VARCHAR2(30), REPORTTIME NUMBER(19,0))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_DISTRIBUTED_JOB' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
                    LS_SQL := 'CREATE TABLE IEAI_DISTRIBUTED_JOB(IID NUMBER(19,0),JOBNAME VARCHAR2(30),  JOBCLASS VARCHAR2(200),  JOBMETHOD VARCHAR2(50),JOBSTATE NUMBER(19,0),  JOBDATA VARCHAR2(2000) ,   RUNSERVER VARCHAR(50) , CONSTRAINT PK_IEAI_DISTRIBUTED_JOB PRIMARY KEY (IID) )';
        EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='ISYSTEMNUMBER'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD ISYSTEMNUMBER VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMP_SYSTEM_RELATON' AND COLUMN_NAME='ISYSCODE';
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_IPMP_SYSTEM_RELATON ADD ISYSCODE VARCHAR2(255)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
END;
/

-- 8.11.0 version TOPO patch is as follows
DECLARE
    LS_SQL    VARCHAR2(3000);
    LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='MAINDEPARTMENT';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD MAINDEPARTMENT VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='BUSINESSPERSONNEL';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD BUSINESSPERSONNEL VARCHAR2(500)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ARTISAN';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ARTISAN VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='KEYSTEPS';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD KEYSTEPS VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='PLANTIME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD PLANTIME VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='MAINDEPARTMENT';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD MAINDEPARTMENT VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='BUSINESSPERSONNEL';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD BUSINESSPERSONNEL VARCHAR2(500)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ARTISAN';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ARTISAN VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='KEYSTEPS';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD KEYSTEPS VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='PLANTIME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD PLANTIME VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='MAINDEPARTMENT';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD MAINDEPARTMENT VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='BUSINESSPERSONNEL';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD BUSINESSPERSONNEL VARCHAR2(500)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ARTISAN';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ARTISAN VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='KEYSTEPS';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD KEYSTEPS VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='PLANTIME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD PLANTIME VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='MAINDEPARTMENT';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD MAINDEPARTMENT VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='BUSINESSPERSONNEL';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD BUSINESSPERSONNEL VARCHAR2(500)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ARTISAN';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ARTISAN VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='KEYSTEPS';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD KEYSTEPS VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='PLANTIME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD PLANTIME VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_PROXY_LIST' AND COLUMN_NAME = 'PROXYIP';
		  IF LI_EXISTS = 1 THEN
		    LS_SQL := 'ALTER TABLE IEAI_PROXY_LIST MODIFY PROXYIP VARCHAR2(200)';
		    EXECUTE IMMEDIATE LS_SQL;
	  END IF; 
	  		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_PROXY_LIST' AND COLUMN_NAME = 'PROXYIPOUT';
		  IF LI_EXISTS = 1 THEN
		    LS_SQL := 'ALTER TABLE IEAI_PROXY_LIST MODIFY PROXYIPOUT VARCHAR2(200)';
		    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
 END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IACTDES';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE MODIFY IACTDES VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME = 'IACTDES';
	IF LI_EXISTS = 1 THEN
		 LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA MODIFY IACTDES VARCHAR2(1000)';
		 EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IACTDES';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO MODIFY IACTDES VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME = 'IACTDES';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS MODIFY IACTDES VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IACTDES';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS MODIFY IACTDES VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'TMP_RUN_INSTANCE' AND COLUMN_NAME = 'IACTDES';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE TMP_RUN_INSTANCE MODIFY IACTDES VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='IBGTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD IBGTYPE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='ICQTASKID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ICQTASKID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IBGTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IBGTYPE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='ICQTASKID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD ICQTASKID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IBGTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IBGTYPE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='ICQTASKID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD ICQTASKID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/


-- 8.14.0 version TOPO patch is as follows
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SSO_APP_CONFIG' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SSO_APP_CONFIG (IID NUMBER (19) ,IAPPNAME VARCHAR2 (50) ,IISOTHER NUMBER (19) ,ILOGINURL VARCHAR2 (255) ,IAUTHURL VARCHAR2 (255) ,ISTATE NUMBER (19) ,IAPPDISC VARCHAR2 (255) ,IUPDATEUSER NUMBER (19) ,IUPDATETIME TIMESTAMP(6)  ,IIMAGE VARCHAR2 (255) ,IENGLISHSIGN VARCHAR2 (50) ,IAPPTYPE NUMBER (19) , CONSTRAINT PK_IEAI_SSO_APP_CONFIG PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_WARN_USERS' AND COLUMN_NAME = 'ISENDTYPE';
     IF  LI_EXISTS = 0  THEN
         LS_SQL := 'ALTER TABLE IEAI_WARN_USERS ADD ISENDTYPE VARCHAR2 (25)';
         EXECUTE IMMEDIATE LS_SQL;
     END IF;
END;
/

DECLARE
	LS_SQL	VARCHAR(200);
	LI_EXISTS	SMALLINT;
BEGIN
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'ISTATUS';
	IF	LI_EXISTS = 0	THEN
		LS_SQL := 'ALTER TABLE IEAI_USER ADD ISTATUS VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

-- 8.15.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SMS_SYS_CONFIG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SMS_SYS_CONFIG(IID NUMBER(19,0) not null,ISYSCODE VARCHAR2(128),ISYSAPP VARCHAR2(56),ISYSCREATETIME NUMBER(19,0),ISYSUPDATETIME NUMBER(19,0),ICALLMETHOD VARCHAR2(256),ISECRETKEY VARCHAR2(128),ICODE VARCHAR2(28),ICREATEUSER VARCHAR2(56),IUPDATEUSER VARCHAR2(56),CONSTRAINT PK_IEAI_SMS_SYS_CONFIG PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SMS_APPROVE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SMS_APPROVE(IID NUMBER(19,0) NOT NULL,ISYSCODE VARCHAR2(128),ISYSAPP VARCHAR2(56),ITASKCODE VARCHAR2(128),ISMSHEADER VARCHAR2(128),ISMSCONTENT   VARCHAR2(2000),IAPPROVEPHONE NUMBER(19,0),ICREATEAPPROVEPHONE NUMBER(19,0),ISMSUSERTYPE INTEGER,ILASTSMSUSERTYPE INTEGER,IAPPROVEFORWARDSTATE INTEGER,IAPPROVEFORWARDTIME  NUMBER(19,0),ICREATEAPPROVEFORWARDSTATE INTEGER,ICREATEAPPROVEFORWARDTIME  NUMBER(19,0),ICREATESMSCONTENT   VARCHAR2(2000),IDYNAMICCODE  VARCHAR2(10),IRECEIPTSTATE   INTEGER,IRECEIPTTIME  NUMBER(19,0),ICALLBACKCONTENT VARCHAR2(2000),ICALLBACKSTATE   INTEGER,ICALLBACKTIME  NUMBER(19,0),IAPPROVALTESULT  VARCHAR2(2000),IAPPROVALCONTENT VARCHAR2(2000),INOTICEFORWARDSTATE INTEGER,INOTICEFORWARDTIME  NUMBER(19,0),ICREATETIME NUMBER(19,0),ICODE VARCHAR2(28),ICREATEUSER VARCHAR2(56),CONSTRAINT PK_IEAI_IEAI_SMS_APPROVE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
-- 8.17.0
DECLARE
	LS_SQL	VARCHAR(200);
	LI_EXISTS	SMALLINT;
BEGIN
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_AUDIT_CLOB' AND COLUMN_NAME = 'IOLDCONTENT';
	IF	LI_EXISTS = 1	THEN
		LS_SQL := 'ALTER TABLE IEAI_AUDIT_CLOB DROP COLUMN IOLDCONTENT';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_AUDIT_CLOB' AND COLUMN_NAME = 'INAME';
	IF	LI_EXISTS = 1	THEN
		LS_SQL := 'ALTER TABLE IEAI_AUDIT_CLOB DROP COLUMN INAME';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER' AND COLUMN_NAME='IQYWECHAT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_USER ADD IQYWECHAT VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
-- 8.18.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_AUDIT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_AUDIT (IID NUMBER (19) NOT NULL,IDATE NUMBER (19) ,IUSERNAME VARCHAR2 (255) ,IHANDLE VARCHAR2 (255) ,IHANDLETYPE VARCHAR2 (255) ,INAME VARCHAR2 (255) ,IVERSION VARCHAR2 (255) ,IPROJECTID NUMBER (19) ,ISTORENAME VARCHAR2 (255) , CONSTRAINT PK_IEAI_ARTIFACTS_AUDIT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_ENV' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_ENV (IID NUMBER (19) NOT NULL,INAME VARCHAR2 (255) ,IIP VARCHAR2 (255) ,IPORT VARCHAR2 (255) , CONSTRAINT PK_IEAI_ARTIFACTS_ENV PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_GENERIC' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_GENERIC (IID NUMBER (19) NOT NULL,ISTOREID NUMBER (19) ,INAME VARCHAR2 (255) ,ILASTMVCCID VARCHAR2 (255) , ISENDNUM NUMBER(19), CONSTRAINT PK_IEAI_ARTIFACTS_GENERIC PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_GENERIC_MVCC' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_GENERIC_MVCC (IID NUMBER (19) NOT NULL,IGENERICID NUMBER (19) ,ISIZE NUMBER (19) ,IVERSION VARCHAR2 (255) ,IHASH VARCHAR2 (255) ,IPUSHUSER VARCHAR2 (255) ,IPUSHTIME VARCHAR2 (255) , CONSTRAINT PK_IEAI_ARTIFACTS_GENERIC_MVCC PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_PROJECT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_PROJECT (IID NUMBER (19) NOT NULL,INAME VARCHAR2 (255) ,ICODE VARCHAR2 (255) ,IDESC VARCHAR2 (255) ,ICREATEUSER VARCHAR2 (255) ,ICREATETIME NUMBER (19) ,IUSERID NUMBER (19) ,ILASTUPDATETIME NUMBER (19) , CONSTRAINT PK_IEAI_ARTIFACTS_PROJECT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_STORE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_STORE (IID NUMBER (19) NOT NULL,IPROJECTID NUMBER (19) ,IADDRESS VARCHAR2 (255) ,IDESC VARCHAR2 (255) ,IVISABLE NUMBER (19) ,ICREATEUSER VARCHAR2 (255) ,ICREATETIME NUMBER (19) ,INAME VARCHAR2 (255) , CONSTRAINT PK_IEAI_ARTIFACTS_STORE PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_USER' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_USER (IID NUMBER (19) NOT NULL,IPROJECT NUMBER (19) ,IUSERID NUMBER (19) ,IUSERNAME VARCHAR2 (255) , CONSTRAINT PK_IEAI_ARTIFACTS_USER PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_ARTIFACTS_USERGROUP' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_ARTIFACTS_USERGROUP (IID NUMBER (19) NOT NULL,IPROJECTID NUMBER (19) ,IGROUPID NUMBER (19) ,ITYPE NUMBER (19) ,ISTORENAME VARCHAR2 (255) ,IPERMISSIONTYPE VARCHAR2 (255) ,ISTOREID NUMBER (19) , CONSTRAINT PK_IEAI_ARTIFACTS_USERGROUP PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/



-- 8.19.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYS_DICTIONARY' AND COLUMN_NAME='DCHECKVALUE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SYS_DICTIONARY ADD DCHECKVALUE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='ICANSYNC'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD ICANSYNC NUMBER(19) DEFAULT 1'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

-- 8.20.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CLEARDATA' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_CLEARDATA ( TYPEID NUMBER(19) NOT NULL, TYPENAME VARCHAR2(255), CLEARUSERID NUMBER(19), CLEARUSER VARCHAR2(255), CLEARSTATUS SMALLINT DEFAULT 0,LASTCLEARDATE   TIMESTAMP,ISAUTOCLEAR NUMBER(19) , AUTOCLEARSAVEDAY NUMBER(19), CONSTRAINT PK_IEAI_CLEARDATA PRIMARY KEY(TYPEID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN		

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_CLEARDATA WHERE TYPEID = 5;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_CLEARDATA(TYPEID,TYPENAME,CLEARUSERID,CLEARUSER,CLEARSTATUS) VALUES (5,''定时任务'',null,null,0)';
		EXECUTE IMMEDIATE LS_SQL;
		COMMIT;
	END IF;
END;
/

DECLARE
LS_SQL VARCHAR2(2000);
    LI_EXISTS SMALLINT;
BEGIN
    SELECT SIGN(COUNT(COLUMN_NAME)) INTO LI_EXISTS FROM USER_CONS_COLUMNS CU, USER_CONSTRAINTS AU WHERE CU.CONSTRAINT_NAME = AU.CONSTRAINT_NAME AND AU.CONSTRAINT_TYPE = 'P' AND AU.TABLE_NAME = 'IEAI_DISTRIBUTED_JOB' ;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_DISTRIBUTED_JOB add CONSTRAINT PK_IEAI_DISTRIBUTED_JOB PRIMARY KEY (IID)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(COLUMN_NAME)) INTO LI_EXISTS FROM USER_CONS_COLUMNS CU, USER_CONSTRAINTS AU WHERE CU.CONSTRAINT_NAME = AU.CONSTRAINT_NAME AND AU.CONSTRAINT_TYPE = 'P' AND AU.TABLE_NAME = 'IEAI_WARNING_CODE' ;
    IF LI_EXISTS = 0 THEN
            LS_SQL := 'ALTER TABLE IEAI_WARNING_CODE add CONSTRAINT PK_IEAI_WARNING_CODE PRIMARY KEY (IID)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/

DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_OS WHERE OSID = 9;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_OS (OSID,OSNAME) VALUES (9,''UBUNTU'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_OS WHERE OSID = 10;
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'INSERT INTO IEAI_OS (OSID,OSNAME) VALUES (10,''OTHER'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;

END;
/
--首页通知表
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_HOMENOTICE_MANAGE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HOMENOTICE_MANAGE  (IID NUMBER(19) NOT NULL , NOTICETEXT VARCHAR2(255) , SPEED  NUMBER(19) ,CONSTRAINT PK_IEAI_HOMENOTICE_MANAGE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

-- 解决浦发迁移字段不一致问题
--DECLARE
  --   LS_SQL VARCHAR2(2000);
  --   LI_EXISTS SMALLINT;
--	 LI_COU_EXISTS SMALLINT;
--BEGIN

		--SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='ISENDMAILTIME' AND DATA_TYPE='VARCHAR2'; 
		--IF	LI_EXISTS = 1 THEN 
			--SELECT SIGN(COUNT(*)) INTO LI_COU_EXISTS FROM IEAI_COMPUTER_LIST WHERE ISENDMAILTIME IS NOT NULL;
			--IF LI_COU_EXISTS = 0 THEN
				--ALTER TABLE IEAI_COMPUTER_LIST MODIFY ISENDMAILTIME NUMBER(30) DEFAULT 0 ;
			--END IF; 
		--END IF; 
		--COMMIT; 
--END;
--/

-- 8.20.1 南京银行增加
DECLARE
LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_ORGMANAGEMENT' AND COLUMN_NAME = 'ILEVEL';
IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_ORGMANAGEMENT ADD ILEVEL VARCHAR2(255)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'IDEPARTMENTID';
IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_USER ADD IDEPARTMENTID NUMBER(19)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'ISTANDBY';
IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_USER ADD ISTANDBY VARCHAR2(255)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

END;
/



DECLARE
LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_COMPUTER_GROUP' AND COLUMN_NAME = 'MATEIP';
	IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_COMPUTER_GROUP ADD MATEIP VARCHAR2(30)';
	EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

-- 8.20.1 南京银行权限转移
    DECLARE
LS_SQL VARCHAR2(2000);
	     LI_EXISTS SMALLINT;
BEGIN
SELECT SIGN(COUNT(*))INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_USER_TRANSFER_PERMISSIONS' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
		 LS_SQL := 'CREATE TABLE IEAI_USER_TRANSFER_PERMISSIONS (IID DECIMAL (19) NOT NULL, ILOGINID DECIMAL (19), ILOGINNAME VARCHAR2(50), ITRANSFERUSER_ID  DECIMAL (19), ITRANSFERUSER VARCHAR2(50),IAPPROVALUSER_ID DECIMAL (19),IAPPROVALUSER VARCHAR2(50),ICREATTIME DATE,IUPDATETIME DATE,ITRANSFERDEADLINE DATE,IIS_AUDITING DECIMAL(2), IIS_TERM DECIMAL(2), CONSTRAINT IEAI_USER_TRANSFER_PERMISSIONS PRIMARY KEY(IID) )';
EXECUTE IMMEDIATE LS_SQL;
END IF;
END;
/
         DECLARE
LS_SQL VARCHAR2(2000);
	    LI_EXISTS SMALLINT;
BEGIN
SELECT SIGN(COUNT(*))  INTO LI_EXISTS  FROM USER_OBJECTS   WHERE OBJECT_NAME = 'IEAI_USER_PERMISSIONS'  AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
		 LS_SQL := 'CREATE TABLE IEAI_USER_PERMISSIONS(IID DECIMAL (19) NOT NULL, ITRANSFERID DECIMAL (19) NOT NULL, IROLEID DECIMAL (19) NOT NULL,CONSTRAINT PK_IEAI_USER_PERMISSIONS PRIMARY KEY(IID))';
EXECUTE IMMEDIATE LS_SQL;
END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_AGENT_CONF_NJ' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_AGENT_CONF_NJ (IID NUMBER (19)   NOT NULL,AGENTID NUMBER (19)  ,DBUSER VARCHAR2 (50)  ,DBPWD VARCHAR2 (64)  ,DBURL VARCHAR2 (150)  ,DBTYPE VARCHAR2 (20)  ,DBDRIVER VARCHAR2 (100)  ,CREATEUSER VARCHAR2 (50)  ,CREATETIME NUMBER (19)  ,UPDATEUSER VARCHAR2 (50)  ,UPDATETIME NUMBER (19)  , CONSTRAINT PK_IEAI_AGENT_CONF_NJ PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='DEVELOPER';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD DEVELOPER VARCHAR2(255) ';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		COMMIT;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='DEPARTMENT';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD DEPARTMENT VARCHAR2(100) ';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		COMMIT;
END;
/


DECLARE
LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_COMPUTER_LIST' AND COLUMN_NAME = 'CPOSVERSION';
IF LI_EXISTS = 0 THEN
		    LS_SQL := 'ALTER TABLE IEAI_COMPUTER_LIST ADD CPOSVERSION VARCHAR2(100)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

END;
/

DECLARE
LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_COMPUTER_GROUP' AND COLUMN_NAME = 'MATEIP';
	IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_COMPUTER_GROUP ADD MATEIP VARCHAR2(30)';
	EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/


DECLARE
LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_ORGMANAGEMENT' AND COLUMN_NAME = 'IEXTERNALID';
IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_ORGMANAGEMENT ADD IEXTERNALID NUMBER(19)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_ORGMANAGEMENT' AND COLUMN_NAME = 'IEXTERNALPID';
IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_ORGMANAGEMENT ADD IEXTERNALPID NUMBER(19)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ARTIFACTS_GENERIC' AND COLUMN_NAME='ISENDNUM';
IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ARTIFACTS_GENERIC ADD ISENDNUM NUMBER(19)';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SHELLCMD_OUTPUT' AND COLUMN_NAME='IERROUT'; 
IF LI_EXISTS = 0 THEN 
LS_SQL := 'ALTER TABLE IEAI_SHELLCMD_OUTPUT ADD IERROUT CLOB;'; 
EXECUTE IMMEDIATE LS_SQL; 
END IF; 
COMMIT; 


SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SHELLCMD_OUTPUT' AND COLUMN_NAME='ISUEOUT'; 
IF LI_EXISTS = 0 THEN 
LS_SQL := 'ALTER TABLE IEAI_SHELLCMD_OUTPUT ADD ISUEOUT CLOB'; 
EXECUTE IMMEDIATE LS_SQL; 
END IF; 
COMMIT; 

END;
/
