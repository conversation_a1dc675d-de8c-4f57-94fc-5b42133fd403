package com.ideal.ieai.agent;

import java.util.Date;
import java.util.Hashtable;

/**
 * Class that holds information of an iEAI Agent
 * <AUTHOR>
 *
 */
public class AgentInfo
{
	public String host;
	public int port;
	public boolean isSSL = false;
	public int numTotalRequests = 0;
	public int numPendingReuquests = 0;
    public int numFinishedRequests = 0;
    public int numFailedRequests = 0;
	public int numTimedoutRequests = 0;
	public Date starttime;
	public Hashtable toHashtable()
	{
		Hashtable ret = new Hashtable();
		ret.put("host", this.host);
		ret.put("port", new Integer(this.port));
		ret.put("isSSL", Boolean.valueOf(this.isSSL));
		ret.put("numTotalRequests", new Integer(this.numTotalRequests));
		ret.put("numPendingReuquests", new Integer(this.numPendingReuquests));
		ret.put("numFinishedRequests", new Integer(this.numFinishedRequests));
		ret.put("numFailedRequests", new Integer(this.numFailedRequests));
		ret.put("numTimedoutRequests", new Integer(this.numTimedoutRequests));
		ret.put("starttime", this.starttime);
		return ret;
	}
}
