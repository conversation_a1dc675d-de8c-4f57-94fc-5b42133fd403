<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
//tab页激活页码数
var activeTabNum= null==<%=request.getParameter("activeTabNum")%>?0:<%=request.getParameter("activeTabNum")%>;
var iid = null==<%=request.getParameter("iid")%>?0:<%=request.getParameter("iid")%>;
var instanceName='<%=request.getParameter("instanceName")%>';
var pahaseId='<%=request.getAttribute("pahaseId")%>';
var showOnly=<%=request.getParameter("showOnly")%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/sus/mxgraph/templateChildFlow/templateChildFlowCustomizedMain.js"></script>
</head>
<body>
<div id="templateChildFlowCustomizedMainDiv" style="width: 100%;height: 100%"></div>
</body>
</html>