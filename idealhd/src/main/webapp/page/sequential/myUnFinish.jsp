<%@ page contentType="text/html; charset=utf-8" language="java"%>
<html>
<head>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" 
   src="<%=request.getContextPath()%>/js/circleChart.min.js"></script>		
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/common/jquery.nicescroll.js"></script>
<link rel="stylesheet" type="text/css"
	href="<%=request.getContextPath()%>/css/mybacklog.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/new_blue_skin/css/Style.css" />
<script type="text/javascript">
	var path = '<%=request.getContextPath()%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/sequential/myUnFinish.js"></script>
<link rel="stylesheet" type="text/css" href="css/mybacklog.css" />
</head>
<body class="log_body" onload="load()">
<div class="upcom_top">
	<div class="upcom_wth">
    	<div class="up_logo"></div>
        <div class="length_work">
        	<span class="lwork_icon lwork_pos"></span>
            <span class="lwork_text" id="unFinished_user"></span>
            <span class="lwork_line"></span>
            <span class="lwork_icon lwork_pos2"></span>
            <span class="lwork_text" id="unFinished_hours">0h</span>
            <span class="lwork_line"></span>
            <span class="lwork_icon lwork_pos3"></span>
            <span class="lwork_text" id="unFinished_cnt">0</span>
        </div>
    </div>
</div>
<div class="upcom_tab">
	<div class="um_tab_btn group_common">
		<div class="tab_btn_normal"  onclick="window.location.href='accessmyBacklog.do'">我的关注</div>
    	<div class="tab_btn_select">我的未结任务</div>
    </div>
</div>
<div class="pending_cn">
	<span class="pending_text text_floa bold_font">待处理流程</span>
    <div class="pending_page">
    	<div class="ation_search group_common">
        	<input id="queryCriteria1" type="text" placeholder="--请输入任务名称--"/>
            <span class="lwork_icon lwork_pos4" onclick="javascript:loadFlow()"></span>
        </div>
        <div class="pagination group_common">
        	<div class="page_pre pagination_common"   id="flow_pageUp"     onclick="flow_pageUpHandler()">上一页</div>
            <div class="page_next pagination_common" id="flow_pageDown" onclick="flow_pageDownHandler()">下一页</div>
        </div>
        <select class="page_number group_common group_arrow" id="flow_num_select">
        	<option>10</option>
            <option>20</option>
            <option>30</option>
            <option selected="selected">50</option>
        </select>
    </div>
</div>
<div class="pending_table">
	<table cellpadding="0" cellspacing="0" border="0">
    	<tr class="pen_tab_tr1 bold_font">
        	<td width="3%">序号</td>
            <td width="20%"> 任务名称 </td>
            <td width="20%"> 服务名称 </td>
            <td width="15%">脚本名称</td>
            <td width="15%">开始时间</td>
            <td width="13.5%">启动用户</td>
            <td width="13.5%">状态</td>
        </tr>
      </table>
      <table cellpadding="0" cellspacing="0" border="0">
      	<tr>
      		<td>
      			<div class="pending_scroll pending_space" id ="unFinishDiv">
      		<!-- 加减号循环开始 -->
      			<div class="pending_content">
      			 
	                
		        </div>
		        <!-- 加减号循环结束 -->
		        </div>
      		</td>
      	</tr>
      </table>
</div>
 
      			<!--<div class="pending_content">
      		  
		        </div>-->
		        <!-- 加减号循环结束 -->
		        </div>
      		</td>
      	</tr>
      </table>
</div>

<script type="text/javascript">
	$(".change_table").niceScroll({
		autohidemode : true,
		cursorcolor : "#cccccc",
		cursorborder : "1px solid #cccccc"
	});
	$(".pending_scroll").niceScroll({
		autohidemode : true,
		cursorcolor : "#cccccc",
		cursorborder : "1px solid #cccccc"
	});
	
</script>
</body>
</html>
