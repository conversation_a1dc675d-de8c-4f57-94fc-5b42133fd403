package com.ideal.controller.jobscheduling.timeconfigurationupload;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.ClientSession;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.cluster.manager.ClusterManager;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ClientSessionHelper;
import com.ideal.ieai.server.jobscheduling.repository.timeconfigurationupload.DbUtilUpLoadDayStartExcel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.service.jobscheduling.timeconfiguration.TimeConfigurationService;
import com.ideal.util.UUID;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

/**   
 * @ClassName:  TimeConfigurationController   
 * @Description: (主线定时配置列表上载)   
 * @author: yue_sun 
 * @date:   2018年2月26日 下午3:09:27   
 *     
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
@Controller
public class TimeConfigurationController
{
    private static final Logger _log = Logger.getLogger(TimeConfigurationController.class);
    public static final String  UTF8 = "UTF-8";
    public static final String  STR1 = "text/html";
    public static final String  STR2 = "{success:false,message:'";
    @RequestMapping("timeConfigurationUpload.do")
    public String test ()
    {
        return "jobScheduling/timeConfigurationUpload/timeConfigurationUpload";
    }

    /**
     * 
     * @Title: taskUpload   
     * @Description:  (上传文件)   
     * @param file
     * @param request
     * @param response      
     * @author: hui_xie 
     * @date:   2017-8-9 下午04:04:49
     */
    @RequestMapping("configurationUpload.do")
    public void taskUpload ( @RequestParam("uploadFile") CommonsMultipartFile file, HttpServletRequest request,
            HttpServletResponse response )
    {
        File fileNew = null;
        FileOutputStream outer = null;
        String rsStr = "{success:true}";
        String messages = "";
        DbUtilUpLoadDayStartExcel dbUtilUpLoadExcel = DbUtilUpLoadDayStartExcel.getInstance();

        try
        {
            _log.info(file.getOriginalFilename() + "【Excel导入】开始！");
            ClientSession session = ClusterManager.getInstance().getSession(
                SessionData.getSessionData(request).getSessionId());
            String userName = session.getUserLoginName();
            UserInfo user = ClientSessionHelper.getUser(SessionData.getSessionData(request).getSessionId());
            String ffName = file.getOriginalFilename();

            long size = file.getSize();
            getStr2(size, response);
            // 上传文件临时目录
            fileNew = new File(Environment.getInstance().getIEAIHome() + File.separator + UUID.uuid());
            mkDir(fileNew);

            // 上传文件
            outer = new FileOutputStream(fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename());
            byte[] buffer = file.getBytes();
            outer.write(buffer);
            outer.close();
            // 单独excel文件上传
            if (ffName.toLowerCase().endsWith(".xls") || ffName.toLowerCase().endsWith(".xlsx") || ffName.toLowerCase().endsWith(".et"))
            {
                messages = dbUtilUpLoadExcel.taskUpload(fileNew, file, 
                    file.getOriginalFilename(), user, userName, fileNew.getAbsolutePath());
            } // 通过压缩包上传
            else if (ffName.toLowerCase().endsWith(".rar") || ffName.toLowerCase().endsWith(".zip")
                    || ffName.toLowerCase().endsWith(".7z"))
            {
                // 解压缩后返回结果集，含：1是否成功标志2失败信息3压缩包中文件名清单
                Map map = getMap(fileNew, ffName, file);

                String result = (String) map.get("result");
                // 解压缩失败直接返回
                if (!"1".equals(result))
                {
                    messages = (String) map.get("message");
                    rsStr = STR2 + messages + "'}";
                    response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                    response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                    outsClose(rsStr, response);
                } else
                {
                    List<String> list = (List) map.get("resultList");
                    StringBuilder messageBuilder = new StringBuilder("");
                    EngineRepositotyJdbc.getInstance().saveUploadExcelRar(ffName, userName, list, Constants.IEAI_IEAI);
                    for (String filename : list)
                    {
                        // 循环解析压缩中的文件
                        messages = dbUtilUpLoadExcel.taskUpload(fileNew, file,
                             filename, user, userName,
                            fileNew.getAbsolutePath());
                        taskUploadFun(messages, messageBuilder, response, filename, ffName, userName);
                    }
                }

            } else
            {
                messages = "请上传合法格式的文件！";
                rsStr = STR2 + messages + "'}";
                response.setCharacterEncoding(UTF8);
                response.setContentType(STR1);
                response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                outsClose(rsStr, response);
            }

        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            rsStr = STR2 + "上传失败 " + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        } finally
        {
            finallyClose(outer, fileNew);
        }
        _log.info(file.getOriginalFilename() + "【Excel导入】结束！");

        rsStr = STR2 + messages + "'}";
        response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
        response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
        outsClose(rsStr, response);

    }

    private Map getMap ( File fileNew, String ffName, CommonsMultipartFile file )
    {
        Map map;
        // rar格式压缩包上传
        if (ffName.toLowerCase().endsWith(".rar"))
        {
            map = TimeConfigurationService.unRarFile(
                fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(), fileNew.getAbsolutePath());
        }
        // 7z格式压缩包上传
        else if (ffName.toLowerCase().endsWith(".7z"))
        {
            map = TimeConfigurationService.un7zFile(
                fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(), fileNew.getAbsolutePath());
        }
        // zip格式压缩包上传
        else
        {
            map = TimeConfigurationService.unZipFiles(
                fileNew.getAbsolutePath() + File.separator + file.getOriginalFilename(), fileNew.getAbsolutePath());
        }
        return map;
    }
    private void taskUploadFun ( String messages, StringBuilder messageBuilder, HttpServletResponse response,
            String filename, String ffName, String userName )
            throws RepositoryException
    {
        if (!"导入成功".equals(messages))
        {
            messageBuilder = messageBuilder.append("压缩包中文件\"").append(filename).append("\"导入失败，").append(messages);
            String rsStr = STR2 + messageBuilder.toString() + "'}";
            response.setCharacterEncoding(UTF8);
            response.setContentType(STR1);
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        } else
        {
            EngineRepositotyJdbc.getInstance().updateUploadExcelRar(ffName, filename, userName, 1, Constants.IEAI_IEAI);
        }
    }
    private void finallyClose ( FileOutputStream outer, File fileNew )
    {
        try
        {
            if (null != outer)
            {
                // 关闭输出流
                outer.close();
            }
            if (null != fileNew)
            {
                // 删除临时文件
                TimeConfigurationService.deleteDir(fileNew);
            }
        } catch (Exception e2)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e2);
        }
    }

    private void outsClose ( String rsStr, HttpServletResponse response )
    {
        PrintWriter outs = null;
        try
        {
            outs = response.getWriter();
        } catch (IOException e1)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e1);
        }
        if (outs != null)
        {
            outs.write(rsStr);
            outs.close();
        }
    }

    private void getStr2 ( long size, HttpServletResponse response )
    {
        if (size >= 52428800)
        {
            String rsStr = STR2 + "上传模板文件不能大于50M" + "'}";
            response.setContentType(STR1);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            outsClose(rsStr, response);
        }
    }

    private void mkDir ( File fileNew )
    {
        if (!fileNew.exists())
        {
            fileNew.mkdir();
        }
    }
}
