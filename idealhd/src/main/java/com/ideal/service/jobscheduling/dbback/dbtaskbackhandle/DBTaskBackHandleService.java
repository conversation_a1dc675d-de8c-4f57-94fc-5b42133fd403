/**  
 * All rights Reserved, Designed By www.idealinfo.com
 * @Title:  DBTaskBackHandleService.java   
 * @Package com.ideal.service.jobScheduling.dbback.dbTaskBackHandle   
 * @Description:  备份任务手工调度 
 * @author: 理想科技     
 * @date:   2018年12月28日 上午8:53:14   
 * @version V1.0 
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
package com.ideal.service.jobscheduling.dbback.dbtaskbackhandle;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.dbback.dbtaskbackhandle.DBTaskBackHandleManager;
import com.ideal.ieai.server.jobscheduling.repository.startflow.StartFlowBean;
import com.ideal.ieai.server.repository.RepositoryException;


/**   
 * @ClassName:  DBTaskBackHandleService   
 * @Description:备份任务手工调度  
 * @author: junyu_zhang 
 * @date:   2018年12月28日 上午8:53:14   
 *     
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class DBTaskBackHandleService
{
    private static final Logger  _log       = Logger.getLogger(DBTaskBackHandleService.class);
    
    private static  final DBTaskBackHandleService _instance = new DBTaskBackHandleService();
    
    public static  DBTaskBackHandleService getInstance(){
        
        return _instance;
    }
    /**
     * 
     * @Title: getDBTaskBhStrategy   
     * @Description: 获取策略名下拉列表数据    
     * @return      
     * @author: junyu_zhang 
     * @date:   2019年1月8日 下午5:43:00
     */
    public Map<String,Object> getDBTaskBhStrategy(long type){
        
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            map = DBTaskBackHandleManager.getInstance().getDBTaskBhStrategy(type);
        } catch (RepositoryException e)
        {
            _log.error("method："+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        return map;
    }
    /**
     * 
     * @Title: getStrategyInfo   
     * @Description: 根据策略ID获取策略信息  
     * @param strategyId
     * @return      
     * @author: junyu_zhang 
     * @date:   2019年2月18日 下午3:23:13
     */
    public Map<String, Object> getStrategyInfo(long strategyId,long itype){
        
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            String scriptPath = "";
            if(itype == 0){
                
                scriptPath = ServerEnv.getServerEnv().getScriptBackAbsPath();
                
                map = DBTaskBackHandleManager.getInstance().getStrategyInfo(strategyId,scriptPath,itype);
                
            }else{
                scriptPath = ServerEnv.getServerEnv().getScriptRecoverAbsPath();
                map = DBTaskBackHandleManager.getInstance().getStrategyInfo(strategyId,scriptPath,itype);
            }
        } catch (RepositoryException e)
        {
            _log.error("method："+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }
        return map;
        
    }
    /**
     * 
     * @Title: startFlow   
     * @Description: 启动  
     * @param startFlowBean
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年2月18日 下午3:23:37
     */
    public Map startFlow (StartFlowBean startFlowBean,String strategyId,long type) throws RepositoryException
    {
        
        return DBTaskBackHandleManager.getInstance().startFlow(startFlowBean,strategyId,type);
    }
    
}
