package com.ideal.common.utils;

import org.apache.log4j.Logger;

import java.io.File;

/**
 * @ClassName: DeleteFileUtil
 * @Description:TODO(删除文件方法)
 * @author: mingh<PERSON>_shi
 * @date: 2019年11月13日 下午4:42:08
 */
public class DeleteFileUtils {
    private static final Logger logger = Logger.getLogger(DeleteFileUtils.class);

    /**
     * @param file
     * @Title: deleteFile
     * @Description: 递归删除文件夹
     * @author: minghao_shi
     * @date: 2019年11月14日 上午11:30:41
     */
    public static void deleteFile(String filePath) {
        File file = new File(filePath);
        // 判断文件不为null或文件目录存在
        if (file == null || !file.exists()) {
            logger.info("文件删除失败,请检查文件路径是否正确");
            return;
        }
        // 取得这个目录下的所有子文件对象
        File[] files = file.listFiles();
        logger.info("1files" + files);
        // 遍历该目录下的文件对象
        for (File f : files) {
            String name = file.getName();
            logger.info(name);
            // 判断子目录是否存在子目录,如果是文件则删除
            if (f.isDirectory()) {
                deleteFile(f.getPath());
            } else {
                f.delete();
                logger.info("删除文件" + f.getPath() + "完成！");
            }
        }
        // 删除空文件夹 for循环已经把上一层节点的目录清空。
        file.delete();
        logger.info("空目录删除完成！");
    }

    /**
     * @param f
     * @Title: ShowDir
     * @Description: 删除目录中的空文件夹
     * @author: minghao_shi
     * @date: 2019年11月14日 上午10:58:22
     */
    public static void DelNullDir(File f) {

        for (File f1 : f.listFiles()) {

            if (f1.isDirectory()) {

                DelNullDir(f1);
                // 一直递归到最后的目录
                if (f1.listFiles().length == 0) {
                    // 如果是文件夹里面没有文件证明是空文件，进行删除
                    f1.delete();
                }
            }
        }
    }
}