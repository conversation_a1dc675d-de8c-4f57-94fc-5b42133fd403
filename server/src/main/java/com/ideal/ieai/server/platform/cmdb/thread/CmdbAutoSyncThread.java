package com.ideal.ieai.server.platform.cmdb.thread;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.platform.cmdb.GetCmdbInfo;
import com.ideal.ieai.server.platform.cmdb.handler.CmdbSDHandler;
import org.apache.log4j.Logger;
import org.quartz.CronExpression;
import org.quartz.TriggerUtils;
import org.quartz.impl.triggers.CronTriggerImpl;

import java.util.Date;
import java.util.List;

/**
 * 山东cmdb定时同步设备信息线程
 *
 * <AUTHOR>
 * @date 2021年7月27日10:53:00
 */
public class CmdbAutoSyncThread extends Thread {
    private final Logger log = Logger.getLogger(CmdbAutoSyncThread.class);
    GetCmdbInfo getCmdbInfo = new GetCmdbInfo();

    public CmdbAutoSyncThread()
    {
        super("CMDB定时同步线程");
    }
    @Override
    public void run() {
        while (true)
        {
            CmdbSDHandler handler = new CmdbSDHandler();
            try {
                //Corn表达式获取最近一次时间
                String timeSet = getCmdbInfo.getPropertyValue(Environment.AUTO_SYNC_COMPUTER_TIMESET).trim();
                //验证Corn
                if(!CronExpression.isValidExpression(timeSet)) {
                    timeSet =   "0 15 0 * * ?";
                    log.error("Corn表达式验证未通过，默认设置为 0 15 0 * * ? ");
                }
                CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
                cronTriggerImpl.setCronExpression(timeSet);
                List<Date> dates = TriggerUtils.computeFireTimes(cronTriggerImpl, null, 8);
                long threadSleep = dates.get(0).getTime()-System.currentTimeMillis();
                Thread.sleep(threadSleep);
                log.info("================同步CMDB设备begin================");
                handler.autoSyncCmdbInfoSDAll(Constants.IEAI_OPM);
                log.info("================同步CMDB设备end================");
                log.info("================同步设备begin================");
                handler.syncComputer(Constants.IEAI_OPM);
                log.info("================同步设备end================");
            } catch (Exception e) {
                log.error("CmdbAutoSyncThread.autoSyncCmdbInfoSDAll is error ", e);
            }
        }
    }
}
