package com.ideal.ieai.core.contract.handler;

import com.ideal.ieai.core.contract.*;

/**
 * <p>Title: iEAI Core3.0</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 1.0
 */

public class TimeoutIgnoreHandler
    implements ITimeoutExceptionHandler
{
    public TimeoutIgnoreHandler()
    {

    }

    /**
     * Ignore the timeout exception,and continue
     * @throws ContractException
     */
    public void handle(ContractExecContext TheContext) throws ContractException
    {
        //Need not do anything here
    }
}