package com.ideal.ieai.studio.view.cmdui.actions;

import java.awt.event.ActionEvent;

import com.ideal.ieai.studio.view.cmdui.CmdIcons;
import com.ideal.ieai.studio.view.cmdui.CmdManager;
/**
 * <p>Title: iEAI Studio</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR>
 * @version 3.0
 */

public class SwichInfoAction
    extends StudioAction
{
    public SwichInfoAction()
    {
//        super("SwichInfoAction", CmdIcons.imageToggleMsg);
        super("", CmdIcons.imageToggleMsg);
    }

    protected void execute(ActionEvent e)
    {
         CmdManager.getInstance().setFullScreenState(false);
        //get Frame from  cmdmanger
        com.ideal.ieai.studio.MainFrame mainFrame;
        CmdManager cmdmanager = CmdManager.getInstance();
        // force change it to a MainFrame
        mainFrame=(com.ideal.ieai.studio.MainFrame)(cmdmanager.getFrame());
        //switch show scrollPanStatus or not
        mainFrame.swichShowscrollPanStatus();
    }

    protected void alterViews()
    {
            /**@todo Implement this com.ideal.ieai.studio.view.cmdui.actions.StudioAction abstract method*/
    }

}