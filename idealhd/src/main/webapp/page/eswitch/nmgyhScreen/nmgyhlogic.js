var graph;
var proStates="";
var rectUrl="url(#Gaussian_Blur)"; 
Ext.onReady(function() {
	
	mxGraph.prototype.graphToCenter = function() {
		var bounds = this.getGraphBounds();
		if((-bounds.x -(bounds.width - this.container.clientWidth)/ 2)>0){
			this.view.setTranslate(-bounds.x -(bounds.width - this.container.clientWidth)/ 2,200);
		}else{
			this.view.setTranslate(0,200);
		}
	};
	var container = document.getElementById("ciblogic_div");
	if (!mxClient.isBrowserSupported()) {
		mxUtils.error('Browser is not supported!',200, false);
	} else {
		// Enables crisp rendering of rectangles in SVG
		mxRectangleShape.prototype.crisp = true;
		// Creates the graph inside the given container
		graph = new mxGraph(container);
		graph.setEnabled(true);
		mxGraphHandler.prototype.setMoveEnabled(false);
		graph.setCellsResizable(false);		
		
		var style = graph.getStylesheet().getDefaultVertexStyle();
		style[mxConstants.STYLE_FILLCOLOR] = "#152661";
		var styleedge = graph.getStylesheet().getDefaultEdgeStyle();
		styleedge[mxConstants.STYLE_EDITABLE]=false;
		styleedge[mxConstants.STYLE_EDGE] = mxEdgeStyle.ElbowConnector;
		styleedge[mxConstants.STYLE_STROKECOLOR] = "#a6a6a6";
		style[mxConstants.STYLE_STROKECOLOR] = '#3c76ff';
		style["fontSize"] = 18;
		graph.setHtmlLabels(true);
//		 graph.setEditable(false);
		graph.setCellsEditable(false);
		graph.setTooltips(true);
		var getTooltipForCell = graph.getTooltipForCell;
		 // 开启可以拖拽建立关系
        graph.setConnectable(false);
        
        graph.addListener(mxEvent.CLICK, function(sender, evt) {
        	
        	if(evt.getProperty('cell')!=null && evt.getProperty('cell').instanceid!=null){
	  			var cell = evt.getProperty('cell');
	  			var ele = cell.value;
	  			var cibcontent = $(ele).html();
	  			cibinstanceid=cell.instanceid;
	  		}else{
	  			cibinstanceid = "";
	  		}
        });
	}
	
//	loadGraph();
	window.setInterval(function(){
		refreshCellStates(graph);
	}, 5000); 
	$("#ciblogic_div").niceScroll({
		autohidemode : true,
		cursorcolor : "#0474e2",
		cursorborder : "1px solid #0474e2"
	});
	
});


function loadGraph() {
	graph.clearSelection();
	graph.getModel().beginUpdate();
	try {
		var doc = mxUtils.load(encodeURI("queryZzbSwitchXML.do?instancename="+instancename+"&tmp="+new Date().getTime()));
		if(doc.request.responseXML!=null){
			var dec = new mxCodec(doc);
			dec.decode(doc.getDocumentElement(), graph.getModel());
			
			var layout = new mxHierarchicalLayout(graph, mxConstants.DIRECTION_NORTH);
			var cell = graph.getSelectionCell();
			setCellState(instancename);
		}
	} finally {
		setCellState(instancename);
		graph.getModel().endUpdate();
	}
	graph.graphToCenter();
//	graph.zoomIn();
//	graph.zoomIn();
//	graph.zoomIn();
	hightLightCell();
}

function refreshCellStates(graph) {
	setCellState(instancename);
}


function setCellState(instancename){
	var queryDate=new Date().getTime();
	Ext.Ajax.request({
		url : path+'/queryZzbSwitchState.do?instancename='+instancename+'&queryDate='+queryDate,
		method : 'POST',
		async : false,
		success : function(response, options) {
			proStates = Ext.decode(response.responseText).proStates;
		},
		failure : function(result, request) {
			
		}
		
	});
	var overlay;
	if(null!=proStates&&''!=proStates){
		for(var i=0;i<proStates.length;i++){
			var proState = proStates[i];
			
			var key = "vertex_"+proState.iid;
			var state = proState.istate;
			
			//0 异常 1运行 2未开始 3已完成
			
			var states = "";
			if (state=='1'){
				states = "运行中";
				picP = path+'/page/prologicSwitchMonitor/images/running_grid.png';
			}else if (state=='2'){
				states = "未执行";
				picP = path+'/page/prologicSwitchMonitor/images/ready_grid.png';
			}else if (state=='0'){
				states = "异常";
				picP = path+'/page/prologicSwitchMonitor/images/abnormal_grid.png';
			}else if (state=='3'){
				states = "完成";
				picP = path+'/page/prologicSwitchMonitor/images/finished_grid.png';
			}
			var cell = graph.getModel().getCell(key);
			if (cell != null) {
				var overlay = new mxCellOverlay(new mxImage(picP, 42, 42),states,mxConstants.ALIGN_LEFT,mxConstants.ALIGN_TOP);
				// Sets the overlay for the cell in the graph
				graph.addCellOverlay(cell, overlay);
		    }
		}
	}
}

function hightLightCell(){
	if (proStates != null && proStates != '') {
		for(var i=0;i<proStates.length;i++){
			var proState = proStates[i];
			
			var key = "vertex_"+proState.iid;
			var state = proState.istate;
			var cell = graph.getModel().getCell(key);
			
			if (cell != null) {
				var cellState = graph.view.getState(cell);
				var keygroup=key+"___"+key;
				if(cellState && cellState.shape){
					//0 异常 1运行 2未开始 3已完成
					if(state=='0'){
						var node =(cellState.shape).node;
						var rect = node.firstChild;
						var rectid=rect.getAttribute("id");
						var x=rect.getAttribute("x");
						var y=rect.getAttribute("y");
						var w=rect.getAttribute("width");
						var h=rect.getAttribute("height");
						var rx=rect.getAttribute("rx");
						var ry=rect.getAttribute("ry");
						var stroke=rect.getAttribute("stroke");
						var n = document.createElementNS( "http://www.w3.org/2000/svg", "rect" );
						n.setAttribute('id', keygroup);
						n.setAttribute('filter', rectUrl);
						n.setAttribute('stroke', "");
						n.setAttribute('x', (parseInt(x)-5));
						n.setAttribute('y', (parseInt(y)-5));
						n.setAttribute('width', (parseInt(w)+10));
						n.setAttribute('height', (parseInt(h)+10));
						if(rx&&ry){
							n.setAttribute('rx', rx);
							n.setAttribute('ry', ry);
						}
						n.setAttribute('stroke-width', 0);
						n.setAttribute('pointer-events', "all");
						n.setAttribute("fill","#fd7673");
						n.setAttribute("style","animation: twinkle_animation 1s  ease-in  infinite ;");
						node.insertBefore(n, node.firstChild);
					}else if (state=='1'){
						var node =(cellState.shape).node;
						var rect = node.firstChild;
						var rectid=rect.getAttribute("id");
						var x=rect.getAttribute("x");
						var y=rect.getAttribute("y");
						var w=rect.getAttribute("width");
						var h=rect.getAttribute("height");
						var rx=rect.getAttribute("rx");
						var ry=rect.getAttribute("ry");
						var stroke=rect.getAttribute("stroke");
						var n = document.createElementNS( "http://www.w3.org/2000/svg", "rect" );
						n.setAttribute('id', keygroup);
						n.setAttribute('filter', rectUrl);
						n.setAttribute('stroke', "");
						n.setAttribute('x', (parseInt(x)-5));
						n.setAttribute('y', (parseInt(y)-5));
						n.setAttribute('width', (parseInt(w)+10));
						n.setAttribute('height', (parseInt(h)+10));
						if(rx&&ry){
							n.setAttribute('rx', rx);
							n.setAttribute('ry', ry);
						}
						n.setAttribute('stroke-width', 0);
						n.setAttribute('pointer-events', "all");
						n.setAttribute("fill","#3c76ff");
						n.setAttribute("style","animation: twinkle_animation 1s  ease-in  infinite ;");
						node.insertBefore(n, node.firstChild);
					}else if (state=='2'){
						//n.setAttribute("fill","#b5b5b6");
					}else if (state=='3'){
						//n.setAttribute("fill","#3dcc6c");
					}
				}
				var myoutEdges = graph.getOutgoingEdges(cell);
				if(null!=myoutEdges && myoutEdges!=''){
					for (var j = 0; j < myoutEdges.length; j++) {
						var cellEdgeState = graph.view.getState(myoutEdges[j]);
						if(cellEdgeState){
							var nodeedge =(cellEdgeState.shape).node;
							var paths = nodeedge.children;
							if(paths.length==3){
								var path = paths[1];
								path.setAttribute("stroke-width","2");
								var nedge = document.createElementNS("http://www.w3.org/2000/svg","animate");
								nedge.setAttribute('attributeName', "stroke-dashoffset");
								nedge.setAttribute('from', "100");
								nedge.setAttribute('to', "0");
								nedge.setAttribute('dur', "5s");
								nedge.setAttribute('repeatCount', "indefinite");
								
								if(state=='0'||state=='1'){
									var path2 = paths[2];
									path.setAttribute("stroke-dasharray",5);
									path.setAttribute("stroke-miterlimit",10);
									path.appendChild(nedge);
								}else if(state=='2'){
									path.setAttribute("stroke-dasharray",5);
									path.setAttribute("stroke-miterlimit",10);
								}
								
							}
						}
					}
				}
			}
		}
	}
}