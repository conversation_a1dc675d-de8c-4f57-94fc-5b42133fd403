package com.ideal.ieai.prv.provider.variableBase;

import cn.hutool.core.codec.Base62;
import com.alibaba.fastjson.JSONArray;
import com.ideal.dubbo.interfaces.variableBase.VariableBase;
import com.ideal.dubbo.models.AgentModel;
import com.ideal.dubbo.models.VariableBaseBean;
import com.ideal.dubbo.models.VariableClassBean;
import com.ideal.dubbo.models.VariableParamBean;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.DesUtils;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.util.DesUtil;
import com.ideal.ieai.prv.provider.aop.VariableCache;
import com.ideal.ieai.prv.repository.hd.setScope.SetScopeManager;
import com.ideal.ieai.prv.repository.hd.variableBase.VariableBaseManager;
import com.ideal.ieai.server.repository.azstatusacquire.HttpsClientUtil;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.Agent;
import com.ideal.ieai.server.repository.hd.infoCollection.bean.HttpDeleteWithBody;
import com.ideal.util.StringUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

@DubboService
public class VariableBaseImpl implements VariableBase {

    @Autowired
    private VariableCache variableCache;

    private static final Logger _log = Logger.getLogger(VariableBaseImpl.class);
    private final String checkName = "变量名称只允许输入字母、数字、下划线，首字符不能为数字。";
    private final String SUCCESS = "success";
    private final String MSG = "message";
    private final String ENTEGOR_SERVER_HOSTNAME = "ENTEGOR_SERVER_HOSTNAME";//entegor server 主机名
    private final String ENTEGOR_SERVER_IP = "ENTEGOR_SERVER_IP";//entegor server IP
    private final String ENTEGOR_SERVER_PORT = "ENTEGOR_SERVER_PORT";//entegor server web 端口
    private final String ENTEGOR_LOCAL_HOSTNAME = "ENTEGOR_LOCAL_HOSTNAME";//执行脚本机器的主机名称
    private final String ENTEGOR_LOCAL_BUSINESS_IP = "ENTEGOR_LOCAL_BUSINESS_IP";//取自 agent 管理中业务地址字段
    private final String ENTEGOR_LOCAL_MANAGER_IP = "ENTEGOR_LOCAL_MANAGER_IP";//取自 agent 管理中 agent Ip 地 址字段
    private final String ENTEGOR_LOCAL_BUSINESS_SYS = "ENTEGOR_LOCAL_BUSINESS_SYS";//取自 agent 管理中业务系统字段
    private final AgentMaintainManager manager = AgentMaintainManager.getInstance();

    private boolean notEmpty(String str) {
        if (str != null && !str.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Object save(String userName, String newRecord, String updateRecord) {
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "保存失败");
        Connection conn = null;
        try {
            List<VariableBaseBean> newRecords = JSONArray.parseArray(newRecord, VariableBaseBean.class);
            List<VariableBaseBean> updateRecords = JSONArray.parseArray(updateRecord, VariableBaseBean.class);
            conn = DBResource.getConnection("save", _log, Constants.IEAI_SCRIPT_SERVICE);
            for (VariableBaseBean bean:newRecords) {
                bean.setIcreateuser(userName);
                bean.setImodifyuser(userName);
                // 判断变量名称 是否符合规则
                if (checkVariableName(bean.getIname())){
                    // 判断变量名称 是否已经存在
                    if (VariableBaseManager.getInstance().save(conn, bean, 1)){

                    }else {
                        conn.rollback();
                        _log.info("新增保存失败");
                        return res;
                    }
                }else {
                    conn.rollback();
                    res.put(MSG, checkName);
                    _log.info("变量名称不符合规则");
                    return res;
                }
            }

            for (VariableBaseBean bean:updateRecords){
                bean.setImodifyuser(userName);
                // 判断变量名称是否符合规则
                if (checkVariableName(bean.getIname())){
                    // 判断变量名称是否唯一
                    if (VariableBaseManager.getInstance().save(conn, bean, 0)){

                    }else {
                        conn.rollback();
                        return res;
                    }
                }else {
                    conn.rollback();
                    res.put(MSG, checkName);
                    _log.info("变量名称不符合规则");
                    return res;
                }
            }
            List<String> repeatDataNames = VariableBaseManager.getInstance().havenRepeatData(conn);
            if (repeatDataNames.size()==0){

            }else {
                conn.rollback();
                res.put(MSG, "\"" + String.join(",",repeatDataNames) + "\"变量名重复，请重新设置");
                return res;
            }
            conn.commit();
            _log.info("变量库保存数据提交");
            res.put(SUCCESS, true);
            res.put(MSG, "保存成功");
        }catch(Exception e){
            try {
                conn.rollback();
            } catch (SQLException throwables) {
                _log.error("回滚异常");
                throwables.printStackTrace();
            }
            _log.error("变量库保存string 转换 List 失败",e);
        } finally {
            DBResource.closeConnection(conn, "save", _log);
        }
        return res;
    }

    @Override
    public Object del(String iids) {
        Map res = new HashMap();
        res.put(SUCCESS,false);
        res.put(MSG, "删除失败");
        Connection conn = null;
        try {
            List<String> iidList = JSONArray.parseArray(iids, String.class);
            conn = DBResource.getConnection("del", _log, Constants.IEAI_SCRIPT_SERVICE);
            List<String> delIid = new ArrayList<>();
            List<String> updateList = new ArrayList<>();
            List<String> delPubIid = new ArrayList<>();

            for (int i = 0; i < iidList.size(); i++) {
                int status = SetScopeManager.getInstance().getStatus(conn, Long.valueOf(iidList.get(i)), "1");
                switch (status){
                    case 1:
                        delIid.add(iidList.get(i));
                        break;
                    case 2:
                        delPubIid.add(iidList.get(i));
                        break;
                    case 3:
                        updateList.add(iidList.get(i));
                        break;
                    default:
                        break;
                }
            }
            if (delIid.size() != 0){
                // 现有草稿删除
                if (VariableBaseManager.getInstance().del(conn, delIid, false)){
                    List<Long> longList = new ArrayList<>();
                    for (String strIid:delIid) {
                        longList.add(Long.valueOf(strIid));
                    }
                    // 清除作用域 IEAI_SCRIPT_SCOPE_VARIABLE
                    if (SetScopeManager.getInstance().clearScope(conn, longList.toArray(new Long[0]), "1", true, false)){

                    }else {
                        conn.rollback();
                        _log.info("清除作用域失败，回滚");
                        return res;
                    }
                }else {
                    conn.rollback();
                    _log.info("删除失败，回滚");
                    return res;
                }
            }
            if (delPubIid.size() != 0){
                // 已经发布的变量删除
                if (VariableBaseManager.getInstance().del(conn, delPubIid, true)){
                    List<Long> longList = new ArrayList<>();
                    for (String strIid:delPubIid) {
                        longList.add(Long.valueOf(strIid));
                    }
                    // 清除作用域 IEAI_SCRIPT_SCOPE_VARIABLE
                    if (SetScopeManager.getInstance().clearScope(conn, longList.toArray(new Long[0]), "1", true, false)){

                    }else {
                        conn.rollback();
                        _log.info("清除作用域失败，回滚");
                        return res;
                    }
                    // 清除发布作用域 IEAI_SCRIPT_SCOPE_VARIABLE_P
                    if (SetScopeManager.getInstance().clearPublishScope(conn, longList.toArray(new Long[0]), "1")){

                    }else {
                        conn.rollback();
                        _log.info("清除发布作用域失败，回滚");
                        return res;
                    }
                }else {
                    conn.rollback();
                    _log.info("删除失败，回滚");
                    return res;
                }
            }
            // 已发布 回退
            if (updateList.size() != 0){
                VariableBaseManager.getInstance().delPublish(conn, updateList, false);
                VariableBaseManager.getInstance().updatePublish(conn, updateList, false,1);
            }
            conn.commit();
            _log.info("删除成功，数据提交");
            res.put(SUCCESS, true);
            res.put(MSG, "删除成功");
        } catch(Exception e){
            try {
                conn.rollback();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
            _log.error("变量库删除异常",e);
        } finally {
            DBResource.closeConnection(conn, "del", _log);
        }
        return res;
    }

    @Override
    public Object getListSec(String keywordName,String name, String scope, String attribute, String type, String status, int start, int limit,long classId,String bindState,String uuid,String userName) {
        return VariableBaseManager.getInstance().getListSec(keywordName,name, scope, attribute, type, status, start, limit,classId,bindState,uuid,userName);
    }

    @Override
    public Object getList(String name, String scope, String attribute, String type, String status, int start, int limit,long classId,String classStatus) {
        return VariableBaseManager.getInstance().getList(name, scope, attribute, type, status, start, limit,classId,classStatus);
    }

    @Override
    public List<VariableBaseBean> getList(String[] iid) {
        return VariableBaseManager.getInstance().getEncodeList(iid);
    }

    @Override
    public Object getParamList(String iid) {
        return VariableBaseManager.getInstance().getParamList(null, iid);
    }

    @Override
    public Object saveParam(String newRecord, String updateRecord, String iid, String type) {
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "保存失败");
        Connection conn = null;
        try {
            int itype = Integer.valueOf(type);
            List<VariableParamBean> newRecords = JSONArray.parseArray(newRecord, VariableParamBean.class);
            List<VariableParamBean> updateRecords = JSONArray.parseArray(updateRecord, VariableParamBean.class);

            conn = DBResource.getConnection("saveParam", _log, Constants.IEAI_SCRIPT_SERVICE);
            // 调整为
            // 先更新数据，再保存新数据
            for (VariableParamBean bean:updateRecords) {
                // 数组类型判断 value 是否重复
                // 字典类型判断 key 是否重复
                if (VariableBaseManager.getInstance().saveParam(conn, bean, iid, 0)){

                }else {
                    conn.rollback();
                    _log.info("更新参数失败");
                    return res;
                }
            }
            for (VariableParamBean bean:newRecords) {
                // 数组类型判断 value 是否重复
                // 字典类型判断 key 是否重复
                if (VariableBaseManager.getInstance().saveParam(conn, bean, iid, 1)){

                }else {
                    conn.rollback();
                    return res;
                }
            }
            if (!VariableBaseManager.getInstance().isExistKeyValue(conn, iid, itype)){

                }else {
                    if (itype == 4){
                        res.put(MSG, "数组类型值不能重复");
                    }else if (itype == 5){
                        res.put(MSG, "字典类型键不能重复");
                    }
                    conn.rollback();
                    _log.info(res.get(MSG));
                    return res;
                }
            // 重新排序
            List<VariableParamBean> list = VariableBaseManager.getInstance().getParamList(conn, iid);
            for (int i = 0; i < list.size(); i++) {
                if (VariableBaseManager.getInstance().sortParamList(conn, list.get(i).getIid(), i+1)){

                }else {
                    conn.rollback();
                    res.put(MSG, "排序失败");
                    return res;
                }
            }
            if (VariableBaseManager.getInstance().updateParamCreateMd5(conn, iid)){
                _log.info("更新md5成功iid:" + iid);
            }else {
                conn.rollback();
                _log.info("更新md5失败iid:" + iid);
                res.put(MSG, "保存失败");
                return res;
            }
            int status = SetScopeManager.getInstance().getStatus(conn, Long.valueOf(iid), "1");
            if (status == 2){
                List<String> list1 = new ArrayList<>();
                list1.add(iid);
                SetScopeManager.getInstance().updateStatus(conn, list1, 3, "1");
            }
            conn.commit();
            res.put(SUCCESS, true);
            res.put(MSG, "保存成功");
        } catch(Exception e){
            try {
                conn.rollback();
            } catch (SQLException throwables) {
                _log.info("回滚异常");
                throwables.printStackTrace();
            }
            _log.error("变量库保存数组字典异常",e);
        } finally {
            DBResource.closeConnection(conn, "saveParam", _log);
        }
        return res;
    }

    /**
     * 保存4A密码数据
     * @param variableIID
     * @param iusername
     * @param ipassword
     * @param iaccount
     * @param iresourcename
     * @param ivalue
     * @return
     */
    @Override
    public Map saveVariableFourAData(String variableSetIID,String variableIID,String iusername,
                                        String ipassword, String iaccount, String iresourcename, String ivalue , String autoObtain) {
        Connection conn = null;
        Map returnMap = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_SCRIPT_SERVICE);
            returnMap = VariableBaseManager.getInstance().saveVariableFourAData(conn,variableIID, iusername, ipassword, iaccount,iresourcename,ivalue,autoObtain);
            if((boolean) returnMap.get("success")){
                conn.commit();
            }
        }catch (Exception e){
            _log.error("VariableBaseImpl.saveVariableFourAData error"+e.getMessage());
        }finally {
            DBResource.closeConnection(conn,method,_log);
        }
        return returnMap;
    }

    /**
     * 根据变量id获取4A密码类型相关数据
     * @param variableIID
     * @return
     */
    @Override
    public Map getFourADetailsData(String variableIID,boolean flag) {
        return VariableBaseManager.getInstance().getFourADetailsData(variableIID,flag);
    }

    @Override
    public Map getFourADetailsDatas(List<Long> variableIID) {
        return VariableBaseManager.getInstance().getFourADetailsDatas(variableIID);
    }

    @Override
    public List<Long> getAllNotAutoIIDS(){
        return VariableBaseManager.getInstance().getAllNotAutoIIDS();
    }

    /**
     * 调用获取4A密码接口
     * @param iusername
     * @param ipassword
     * @param iaccount
     * @param iresourcename
     * @return
     */
    @Override
    public Map getFourAPwdForInterface(boolean timetask,String variableIID,String iusername,
                                       String ipassword, String iaccount, String iresourcename,String autoObtain) {
        //定义返回map
        Map<String,Object> returnMap = new HashMap<>();
        returnMap.put("success",false);
        returnMap.put("message","密码获取失败");
        returnMap.put("password","");
        //获取的4A密码
        String password = "";
        _log.info("开始获取4A密码");
        //该方法实际调用了3个接口
        //1、访问用户登录认证  2、获取账号密码， 需要权限的支持  3、访问用户注销
        try {
            //前提：不管4A密码是否获取成功，都需要先将基本参数保存（用户名、密码、资源名、账号名）
            //VariableBaseManager.getInstance().updateValueFourA(variableIID,iusername,ipassword,iaccount,iresourcename,password,autoObtain);
            //1、访问用户登录认证（获取token）
            String stAuthToken = this.getTokenByUserNameAndPwd(iusername,ipassword);
            if(StringUtils.isEmpty(stAuthToken)){
                returnMap.put("message","获取token为空");
                return returnMap;
            }
            //2、获取账号密码
            password = this.getFourApassword(iusername,iaccount,iresourcename,stAuthToken);
            if(StringUtils.isEmpty(password)){
                returnMap.put("message","获取获取的4A密码为空");
                return returnMap;
            }
            //3、用户注销
            int logoutFlag = logoutUser(stAuthToken);
            if(204 != logoutFlag){
                returnMap.put("message","访问用户注销失败，返回值为："+logoutFlag);
                return returnMap;
            }
            //autoObtain为1，说明为自动获取类型，密码入库，直接返回
            if("1".equals(autoObtain)){
                returnMap.put("success",true);
                returnMap.put("message","密码获取成功");
                returnMap.put("password",password);
                return returnMap;
            }
            //如果为定时任务，则只更新发布表（变量主表、4A密码主表）的数据，草稿表（变量草稿表、4A密码草稿表）不动
            if(timetask){
                VariableBaseManager.getInstance().updateValueFourASec(variableIID,password);
                return returnMap;
            }
            Map get4Amap = null;
            //获取4A密码后，更新变量主表IVALUE字段
            if(StringUtils.isNotEmpty(password)){
                get4Amap = VariableBaseManager.getInstance().updateValueFourA(variableIID,iusername,ipassword,iaccount,iresourcename,password,autoObtain);
            }

            //更新变量表成功后为成功
            if(ObjectUtils.notEqual(get4Amap,null) && (boolean) (get4Amap.get("success"))){
                returnMap.put("success",true);
                returnMap.put("message","密码获取成功");
                //returnMap.put("password",password);
            }
        } catch (Exception e) {
            _log.error("VariableBaseImpl.getFourAPwdForInterface error"+e.getMessage());
            e.printStackTrace();
        }
        return returnMap;
    }


    /**
     * 用户注销
     * @param token
     * @throws IOException
     */
    @Override
    public int logoutUser(String token) {
        int returnInt = 0;
        _log.info("进入调用用户注销接口方法");
        HttpClient client = null;
        HttpDeleteWithBody httpDelete = null;
        String url =  Environment.getInstance().userFourALogout();
        if(StringUtils.isEmpty(url)){
            _log.info("用户注销接口url为空，请检查配置文件");
            return returnInt;
        }
        try {
            client = HttpsClientUtil.getNewHttpClient();
            httpDelete = new HttpDeleteWithBody(url);
            httpDelete.addHeader("Content-type","application/json;charset=utf-8");
            httpDelete.addHeader("st-auth-token",token);
            HttpResponse response = client.execute(httpDelete);
            if (204 == response.getStatusLine().getStatusCode()) {
                _log.info("调用访问用户注销接口：用户注销成功");
                returnInt = 204;
            }
            if(400 == response.getStatusLine().getStatusCode()){
                _log.info("调用访问用户注销接口：缺少token参数");
                returnInt = 400;
            }
            if(410 == response.getStatusLine().getStatusCode()){
                _log.info("调用访问用户注销接口：token不存在或已经被注销");
                returnInt = 410;
            }
            _log.info("用户注销调用完毕");
        } catch (Exception e) {
            _log.info("调用访问用户注销接口：访问失败");
        }finally {
            if(httpDelete != null){
                httpDelete.abort();
            }
        }
        return returnInt;
    }

    /**
     * 获取4A密码
     * @param iusername
     * @param iaccount
     * @param iresourcename
     * @return
     * @throws IOException
     */
    @Override
    public String getFourApassword(String iusername,String iaccount,String iresourcename,String token) throws IOException {
        _log.info("进入调用获取4A密码接口方法");
        //定义返回值
        String password = "";
        HttpGet get = null;
        InputStream in = null;
        BufferedReader br = null;
        //获取url
        String url =  Environment.getInstance().getFourAPassword();
        if(StringUtils.isEmpty(url)){
            _log.info("获取执行客户端用户一次性登陆密码接口url为空，请检查配置文件");
            return password;
        }
        //拼接url参数
        url += "?account="+iaccount+"&userName="+iusername+"&resourceName="+iresourcename;
        HttpClient httpClient = HttpsClientUtil.getNewHttpClient();
        get = new HttpGet(url);
        try {
            //执行http的get请求
            get.addHeader("st-auth-token",token);
            HttpResponse response = httpClient.execute(get);
            HttpEntity entity = response.getEntity();
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuilder strber= new StringBuilder();
            String line = null;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            String result = strber.toString();
            //获取接口内容为空
            if(StringUtils.isEmpty(result)){
                _log.info("获取指定客户端用户的一次性密码为空");
            }
            //根据要求，token以json方式返回，所以此处将返回结果转为json
            JSONObject tokenJson = JSONObject.fromObject(result);
            if(ObjectUtils.notEqual(tokenJson.get("password"),null)){
                password = tokenJson.get("password").toString();
            }else {
                _log.info("获取指定客户端用户的一次性密码为空");
            }
            _log.info("获取4A密码结束");
        }catch (Exception e){
            _log.info("根据用户名、密码获取4A登录认证失败 :" , e);
        }finally {
            if(get != null){
                get.abort();
            }
            if(br != null){
                br.close();
            }
            if(in != null){
                in.close();
            }
        }
        return password;
    }

    /**
     * 根据用户名、密码获取4A登录认证
     * @param iusername
     * @param ipassword
     * @return
     */
    @Override
    public String getTokenByUserNameAndPwd(String iusername,String ipassword) throws IOException {
        _log.info("进入调用token接口方法");
        //定义返回的token
        String tokenStr = "";
        HttpClient httpClient = null;
        HttpPost post = null;
        //获取url
        String url =  Environment.getInstance().getFourAToken();
        if(StringUtils.isEmpty(url)){
            _log.info("获取4A访问权限token接口url为空，请检查配置文件");
            return tokenStr;
        }
        httpClient = HttpsClientUtil.getNewHttpClient();
        post = new HttpPost(url);
        InputStream in = null;
        BufferedReader br = null;
        try {
            //拼接请求头
            post.setHeader("Content-Type","application/json;charset=utf-8");
            //传递过去的json参数
            JSONObject json = new JSONObject();
            json.put("username",iusername);
            json.put("password",ipassword);
            //拼接参数
            List<BasicNameValuePair> nvps = new ArrayList<BasicNameValuePair>();
            Iterator iterator = json.keys();
            while(iterator.hasNext()){
                String key = (String) iterator.next();
                String value = json.getString(key);
                nvps.add(new BasicNameValuePair(key, value));
            }
            //执行请求
            UrlEncodedFormEntity postingString = new UrlEncodedFormEntity(nvps,"utf-8");
            postingString.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, "application/json;charset=utf-8"));
            post.setEntity(new StringEntity(json.toString()));
            HttpResponse response = httpClient.execute(post);
            _log.info(response.getStatusLine().getStatusCode());
            //返回结果
            in = response.getEntity().getContent();
            br = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuilder strber= new StringBuilder();
            String line = null;
            while((line = br.readLine())!=null){
                strber.append(line+'\n');
            }
            String result = strber.toString();
            //获取接口内容为空
            if(StringUtils.isEmpty(result)){
                _log.info("获取token为空");
                return tokenStr;
            }
            //根据要求，token以json方式返回，所以此处将返回结果转为json
            JSONObject tokenJson = JSONObject.fromObject(result);
            if(ObjectUtils.notEqual(tokenJson.get("ST_AUTH_TOKEN"),null)){
                tokenStr = tokenJson.get("ST_AUTH_TOKEN").toString();
            }else {
                _log.info("获取token为空");
            }
            _log.info("调用token接口结束");
        }catch (Exception e){
            _log.info("根据用户名、密码获取4A登录认证失败"+e.getMessage());
        }finally {
            if(post != null){
                post.abort();
            }
            if(br != null){
                br.close();
            }
            if(in != null){
                in.close();
            }
        }
        return tokenStr;
    }

    /**
     * 删除字典或数组值
     * @param iids  ieai_script_variable_value的iid
     * @param variableiid   ieai_script_variable_value的variavbleiid
     * @return
     */
    @Override
    public Object delParam(String iids, String variableiid) {
        Map res = new HashMap();
        res.put(MSG, "删除失败");
        res.put(SUCCESS, false);
        Connection conn = null;
        try {
            List<String> iidList = JSONArray.parseArray(iids, String.class);
            conn = DBResource.getConnection("delParam", _log, Constants.IEAI_SCRIPT_SERVICE);
            // 删除变量类型为 数组、字典 参数值
            if (VariableBaseManager.getInstance().delParam(conn, iidList)){

            }else {
                conn.rollback();
                _log.info("删除参数失败");
                return res;
            }
            // 重新排序
            List<VariableParamBean> list = VariableBaseManager.getInstance().getParamList(conn, variableiid);
            for (int i = 0; i < list.size(); i++) {
                if (VariableBaseManager.getInstance().sortParamList(conn, list.get(i).getIid(), i+1)){

                }else {
                    conn.rollback();
                    res.put(MSG, "排序失败");
                    return res;
                }
            }
            if (VariableBaseManager.getInstance().updateParamCreateMd5(conn, variableiid)){
                _log.info("更新md5成功iid:" + variableiid);
            }else {
                conn.rollback();
                _log.info("更新md5失败iid:" + variableiid);
                res.put(MSG, "删除失败");
                return res;
            }
            int status = SetScopeManager.getInstance().getStatus(conn, Long.valueOf(variableiid), "1");
            if (status == 2){
                List<String> list1 = new ArrayList<>();
                list1.add(variableiid);
                SetScopeManager.getInstance().updateStatus(conn, list1, 3, "1");
            }
            conn.commit();
            res.put(MSG, "删除成功");
            res.put(SUCCESS, true);
        } catch (Exception e){
            try {
                conn.rollback();
            } catch (SQLException throwables) {
                _log.info("回滚异常");
                throwables.printStackTrace();
            }
            _log.error("变量库删除变量值异常",e);
        } finally {
            DBResource.closeConnection(conn, "delParam", _log);
        }
        return res;
    }

    @Override
    public Object clearParam(String iid, String type) {
        return VariableBaseManager.getInstance().clearParam(iid, type);
    }

    @Override
    public Object importData(List<VariableBaseBean> list, String userName) {
        Connection conn = null;
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "导入失败");
        try {
            // 处理数据 base62解密
            handlerData(list);
            conn = DBResource.getConnection("importData", _log, Constants.IEAI_SCRIPT_SERVICE);
            for (VariableBaseBean bean:list) {
                // 校验 变量名称是否符合规则
                if (checkVariableName(bean.getIname())){
                    // 判断变量是否已经存在
                    if (!VariableBaseManager.getInstance().isExistName(conn, bean, 1)){
                        bean.setIcreateuser(userName);
                        bean.setImodifyuser(userName);

                        if (VariableBaseManager.getInstance().importSave(conn, bean, 1)){

                        }else {
                            conn.rollback();
                            return res;
                        }
                    }else {
                        bean.setImodifyuser(userName);
                        // 如果存在比对md5 是否相同 不同更新
                        if (VariableBaseManager.getInstance().equalsMd5(conn, bean)){
                            // 比对相等 不处理
                        }else {
                            // 比对不等 更新
                            if (VariableBaseManager.getInstance().importSave(conn, bean, 0)){

                            }else {
                                conn.rollback();
                                return res;
                            }
                        }
                    }
                }else {
                    conn.rollback();
                    res.put(MSG, checkName + ",导入变量名称不符合规则");
                    return res;
                }
            }
            conn.commit();
            res.put(SUCCESS, true);
            res.put(MSG, "导入成功");
        }catch(Exception e){
            try {
                conn.rollback();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
            _log.error("变量库导入处理异常",e);
        }finally {
            DBResource.closeConnection(conn, "importData", _log);
        }
        return res;
    }

    @Override
    public Object publish(String iid) {
        Connection conn = null;
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "发布失败");
        try {
            List<String> iidList = JSONArray.parseArray(iid, String.class);
            // 创建连接
            conn = DBResource.getConnection("publish", _log, Constants.IEAI_SCRIPT_SERVICE);
            // 新增：发布时候变量是为空判断
            List<VariableBaseBean> list = VariableBaseManager.getInstance().getListForPublish(conn, iidList.toArray(new String[0]));
            for (VariableBaseBean bean:list) {
                // 内置函数不校验
                if (bean.getIattribute() == 2){
                    continue;
                }
                //type=6为4A密码类型，4A密码类型变量已经在前台校验，此处不校验4A密码类型
                if(bean.getItype() != 6){
                    if (bean.getItype() <= 3 ){
                        if (StringUtil.isBlank(bean.getIvalue())){
                            res.put(MSG, "发布的变量值不能为空");
                            return res;
                        }
                    }else {
                        if (bean.getParams().size() == 0){
                            res.put(MSG, "发布的数组或字典类型变量值不能为空");
                            return res;
                        }
                    }
                }

            }

            Long[] iidArray = new Long[iidList.size()];

            for (int i = 0; i < iidList.size(); i++) {
                iidArray[i] = Long.parseLong(iidList.get(i));
            }


            String bindMessage = SetScopeManager.getInstance().getBinds(conn, iidArray,"1");
            if(!bindMessage.isEmpty()){
                String FLAG = "flag";
                res.put(SUCCESS, true);
                res.put(MSG, bindMessage);
                res.put(FLAG,true);
                return res;
            }

            // 清除 发布表里面的数据
            VariableBaseManager.getInstance().delPublish(conn, iidList, true);
            // 清除 基础表里面 isNew = 1 and isDelete = 1 数据
            SetScopeManager.getInstance().delBaseScope(conn, 1);
            // 更新 发布状态
            SetScopeManager.getInstance().updateStatus(conn, iidList, 2, "1");
            // 将草稿表 数据 推送到 发布表
            VariableBaseManager.getInstance().updatePublish(conn, iidList, true,1);
            // 将草稿表的 数据 isNew=1 更新为0 isDelete=1删除
            VariableBaseManager.getInstance().handlerScopeVariable(conn, iidList);
            conn.commit();
            res.put(SUCCESS, true);
            res.put(MSG, "发布成功");
        }catch(Exception e){
            try {
                conn.rollback();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
            _log.error("发布变量异常",e);
        }finally {
            DBResource.closeConnection(conn, "publish", _log);
        }
        return res;
    }

    @Override
    public String getExecVariable(Connection conn,String ip, int port){
        //返回map
        Map returnMap = new HashMap();
        //Connection conn = null;
        try {
            //conn = DBResource.getConnection("getExecVariable", _log, Constants.IEAI_SCRIPT_SERVICE);
            Set<String> iids = SetScopeManager.getInstance().getIidListByScopeForExec(conn, ip, port, 1);
            if (iids.size() == 0){
                return "";
            }
            return handlerExecVariable(conn, iids, ip, port, null);
        }catch(Exception e){
            _log.error("根据agent获取可使用的发布变量异常",e);
            return null;
        }
    }

    /**
     * 重载
     * @param ip
     * @param port
     * @param userId
     * @return
     */
    @Override
    public String getExecVariable(Connection conn, String ip, int port, long userId, String scriptWorkDir){
//        Connection conn = null;
        try {
//            conn = DBResource.getConnection("getExecVariable", _log, Constants.IEAI_SCRIPT_SERVICE);
            Set<String> iids = SetScopeManager.getInstance().getIidListByScopeForExec(conn, ip, userId, port, 1);
            if (iids.size() == 0){
                return "";
            }
            return handlerExecVariable(conn, iids, ip, port, scriptWorkDir);
        }catch(Exception e){
            _log.error("根据agent获取可使用的发布变量异常",e);
            return null;
        }
    }

    public String getExecVariableByBind(Connection conn, String ip, int port, String scriptUuid, String scriptWorkDir) {
        try {
            Set<String> iids = SetScopeManager.getInstance().getIidListByBindForExec(conn, scriptUuid, 1);
            if (iids.size() == 0){
                return "";
            }
            return handlerExecVariable(conn, iids, ip, port, scriptWorkDir);
        }catch(Exception e){
            _log.error("根据agent获取可使用的发布变量异常",e);
            return null;
        }
    }

    private String handlerExecVariable(Connection conn, Set<String> iids, String ip, int port, String scriptWorkDir){
        try {
            List<VariableBaseBean> list = VariableBaseManager.getInstance().getList(conn, iids.toArray(new String[0]));
            List<String> baseList = new ArrayList<>();
            //根据变量list查询所有4A密码类型且自动获取的变量
            Map<String,Map> fourMap = VariableBaseManager.getInstance().getAllAutoVariable(list);
            Map<String,String> vaMap = new HashMap<>();
            Agent agent = null;
            //4A密码类型变量id
            List<Long> fourAPSIID = new ArrayList<>();
            //根据ip,port获取Agent信息
            agent = manager.getAgentMaintainInfoByIpPort(ip, port);
            for (VariableBaseBean bean:list) {
                //判断4A密码是否为自动获取标识
                boolean fourAflag = true;
                String str = "";
                List<String> paramList = new ArrayList<>();
                switch (bean.getItype()){
                    case 1:
                        str = bean.getIname() + "_n='" + bean.getIvalue() + "'";
                        break;
                    case 2:
                        str = bean.getIname() + "_s='" + bean.getIvalue() + "'";
                        //获取entegor server 主机名,entegor server IP 地址,entegor server 端口
                        if (bean.getIname().equals(ENTEGOR_SERVER_HOSTNAME)) {
                            String server_name = Environment.getInstance().getServerName();
                            str = ENTEGOR_SERVER_HOSTNAME + "_s='" + (StringUtil.isBlank(server_name)?"null":server_name) + "'";
                        }
                        if (bean.getIname().equals(ENTEGOR_SERVER_IP)) {
                            String server_ip = Environment.getInstance().getServerIP();
                            str = ENTEGOR_SERVER_IP + "_s='" + (StringUtil.isBlank(server_ip)?"null":server_ip) + "'";
                        }
                        if (bean.getIname().equals(ENTEGOR_SERVER_PORT)) {
                            int server_port = Environment.getInstance().getServerPort();
                            str = ENTEGOR_SERVER_PORT + "_s='" + server_port + "'";
                        }
                        //执行机器主机名
                        if (bean.getIname().equals(ENTEGOR_LOCAL_HOSTNAME)) {
                            str = ENTEGOR_LOCAL_HOSTNAME + "_s='" + (StringUtil.isBlank(agent.getIcomName())?"null":agent.getIcomName()) + "'";
                        }
                        // 执行机器业务地址
                        if(bean.getIname().equals(ENTEGOR_LOCAL_BUSINESS_IP)){
                            str = ENTEGOR_LOCAL_BUSINESS_IP + "_s='" + (StringUtil.isBlank(agent.getIbusinessip())?"null":agent.getIbusinessip()) + "'";
                        }
                        //执行机器管理地址
                        if(bean.getIname().equals(ENTEGOR_LOCAL_MANAGER_IP)){
                            str = ENTEGOR_LOCAL_MANAGER_IP + "_s='" + (StringUtil.isBlank(agent.getIagentip())?"null":agent.getIagentip()) + "'";
                        }
                        //执行机器所属业务系统名称
                        if(bean.getIname().equals(ENTEGOR_LOCAL_BUSINESS_SYS)){
                            str = ENTEGOR_LOCAL_BUSINESS_SYS + "_s='" + (StringUtil.isBlank(agent.getIbusinesssys())?"null":agent.getIbusinesssys()) + "'";
                        }
                        break;
                    case 3:
                        str = bean.getIname() + "_s='" + DesUtils.getInstance().decrypt(bean.getIvalue()) + "'";
                        break;
                    case 4:
                        for (VariableParamBean paramBean:bean.getParams()) {
                            paramList.add(paramBean.getIvalue());
                        }
                        str = bean.getIname() + "_a='" + String.join(",", paramList) + "'";
                        break;
                    case 5:
                        for (VariableParamBean paramBean:bean.getParams()) {
                            paramList.add(paramBean.getIkey() + "=" + paramBean.getIvalue());
                        }
                        str = bean.getIname() + "_d='" + String.join(",", paramList) + "'";
                        break;
                    case 6:
                        //如果fourMap中不存在当前变量，说明当前变量不是（4A类型且自动获取）
                        if(!ObjectUtils.notEqual(fourMap.get(String.valueOf(bean.getIid())),null)){
                            str = bean.getIname() + "_s='" + DesUtil.decrypt(bean.getIvalue(),"idealinfo!@#$%") + "'";
                        }else {
                            fourAPSIID.add(bean.getIid());
                            vaMap.put(String.valueOf(bean.getIid()),bean.getIname());
                            fourAflag = false;
                        }
                        break;
                    default:
                        break;
                }
                if(fourAflag){
                    baseList.add(str);
                }
            }
            //调用接口，获取密码
            for(Long setiid : fourAPSIID){
                String str = "";
                Map setMap = fourMap.get(String.valueOf(setiid));
                //拼接key标识
                String keyVal = setMap.get("IUSERNAME") + "@foura@" +
                        setMap.get("IPASSWORD") + "@foura@" +
                        setMap.get("IACCOUNT");
                String pswdFlag = variableCache.getCacheVariable(keyVal);
                String pswd = pswdFlag;
                if(StringUtils.isEmpty(pswdFlag)){
                    Map desMap = fourMap.get(String.valueOf(setiid));
                    String variableiid = String.valueOf(desMap.get("VARIABLEIID"));
                    String iusername = String.valueOf(desMap.get("IUSERNAME"));
                    String ipassword = String.valueOf(desMap.get("IPASSWORD"));
                    String iaccount = String.valueOf(desMap.get("IACCOUNT"));
                    String iresourcename = String.valueOf(desMap.get("IRESOURCENAME"));
                    Map fourAMap = this.getFourAPwdForInterface(false,variableiid,iusername,ipassword,iaccount,iresourcename,"1");

                    pswd = variableCache.saveCacheValue(keyVal,String.valueOf(fourAMap.get("password")));
                }
                str = vaMap.get(String.valueOf(setiid)) + "_s='" + pswd + "'";
                baseList.add(str);
            }

            if (StringUtils.isNotEmpty(scriptWorkDir)) {
                baseList.add("SCRIPT_WORK_DIR_s='" + scriptWorkDir + "'");
            }

            String param = "";
            if (baseList.size() ==0){

            }else {
                param = Constants.SCRIPT_ENVIRONMENT_VAL + "=" + String.join(Constants.SCRIPT_VAL_CONN, baseList);
                DesUtils desUtils = new DesUtils("SCRIPT_ENVIRONMENT_VAL");
                param = desUtils.encrypt(param);
            }
            //_log.info("ip:" + ip + ",port:" + port + ",param:" + param);
            return param;
        }catch(Exception e){
            _log.error("处理执行可用变量异常",e);
            return null;
        }/*finally {
            DBResource.closeConnection(conn, "getExecVariable", _log);
        }*/
    }

    /**
     * 代码中有2处这个方法 修改需要一并修改
     * @param conn
     * @param iids
     * @param agentMap
     * @param scriptWorkDir
     * @return
     */
    public Map<String, String> handlerExecVariable(Connection conn, Set<String> iids, Map<String, AgentModel> agentMap, String scriptWorkDir){
        Map<String, String> dataMap = new HashMap<>();
        try {
            List<VariableBaseBean> list = VariableBaseManager.getInstance().getList(conn, iids.toArray(new String[0]));
            List<String> baseList = new ArrayList<>();
            //根据变量list查询所有4A密码类型且自动获取的变量
            Map<String,Map> fourMap = VariableBaseManager.getInstance().getAllAutoVariable(list);
            Map<String,String> vaMap = new HashMap<>();
            //4A密码类型变量id
            List<Long> fourAPSIID = new ArrayList<>();
            //根据ip,port获取Agent信息
            String[] agentVariableBreak = new String[]{ENTEGOR_LOCAL_HOSTNAME, ENTEGOR_LOCAL_BUSINESS_IP, ENTEGOR_LOCAL_MANAGER_IP, ENTEGOR_LOCAL_BUSINESS_SYS};

            for (VariableBaseBean bean:list) {
                //判断4A密码是否为自动获取标识
                boolean fourAflag = true;
                String str = "";
                List<String> paramList = new ArrayList<>();
                switch (bean.getItype()){
                    case 1:
                        str = bean.getIname() + "_n='" + bean.getIvalue() + "'";
                        break;
                    case 2:
                        // Agent相关内置变量跳过拼接
                        if (ArrayUtils.contains(agentVariableBreak, bean.getIname())) {
                            continue;
                        }

                        str = bean.getIname() + "_s='" + bean.getIvalue() + "'";
                        //获取entegor server 主机名,entegor server IP 地址,entegor server 端口
                        if (bean.getIname().equals(ENTEGOR_SERVER_HOSTNAME)) {
                            String server_name = Environment.getInstance().getServerName();
                            str = ENTEGOR_SERVER_HOSTNAME + "_s='" + (StringUtil.isBlank(server_name)?"null":server_name) + "'";
                        }
                        if (bean.getIname().equals(ENTEGOR_SERVER_IP)) {
                            String server_ip = Environment.getInstance().getServerIP();
                            str = ENTEGOR_SERVER_IP + "_s='" + (StringUtil.isBlank(server_ip)?"null":server_ip) + "'";
                        }
                        if (bean.getIname().equals(ENTEGOR_SERVER_PORT)) {
                            int server_port = Environment.getInstance().getServerPort();
                            str = ENTEGOR_SERVER_PORT + "_s='" + server_port + "'";
                        }
                        break;
                    case 3:
                        str = bean.getIname() + "_s='" + DesUtils.getInstance().decrypt(bean.getIvalue()) + "'";
                        break;
                    case 4:
                        for (VariableParamBean paramBean:bean.getParams()) {
                            paramList.add(paramBean.getIvalue());
                        }
                        str = bean.getIname() + "_a='" + String.join(",", paramList) + "'";
                        break;
                    case 5:
                        for (VariableParamBean paramBean:bean.getParams()) {
                            paramList.add(paramBean.getIkey() + "=" + paramBean.getIvalue());
                        }
                        str = bean.getIname() + "_d='" + String.join(",", paramList) + "'";
                        break;
                    case 6:
                        //如果fourMap中不存在当前变量，说明当前变量不是（4A类型且自动获取）
                        if(!ObjectUtils.notEqual(fourMap.get(String.valueOf(bean.getIid())),null)){
                            str = bean.getIname() + "_s='" + DesUtil.decrypt(bean.getIvalue(),"idealinfo!@#$%") + "'";
                        }else {
                            fourAPSIID.add(bean.getIid());
                            vaMap.put(String.valueOf(bean.getIid()),bean.getIname());
                            fourAflag = false;
                        }
                        break;
                    default:
                        break;
                }
                if(fourAflag){
                    baseList.add(str);
                }
            }
            //调用接口，获取密码
            for(Long setiid : fourAPSIID){
                String str = "";
                Map setMap = fourMap.get(String.valueOf(setiid));
                //拼接key标识
                String keyVal = setMap.get("IUSERNAME") + "@foura@" +
                        setMap.get("IPASSWORD") + "@foura@" +
                        setMap.get("IACCOUNT");
                String pswdFlag = variableCache.getCacheVariable(keyVal);
                String pswd = pswdFlag;
                if(StringUtils.isEmpty(pswdFlag)){
                    Map desMap = fourMap.get(String.valueOf(setiid));
                    String variableiid = String.valueOf(desMap.get("VARIABLEIID"));
                    String iusername = String.valueOf(desMap.get("IUSERNAME"));
                    String ipassword = String.valueOf(desMap.get("IPASSWORD"));
                    String iaccount = String.valueOf(desMap.get("IACCOUNT"));
                    String iresourcename = String.valueOf(desMap.get("IRESOURCENAME"));
                    Map fourAMap = this.getFourAPwdForInterface(false,variableiid,iusername,ipassword,iaccount,iresourcename,"1");

                    pswd = variableCache.saveCacheValue(keyVal,String.valueOf(fourAMap.get("password")));
                }
                str = vaMap.get(String.valueOf(setiid)) + "_s='" + pswd + "'";
                baseList.add(str);
            }

            if (StringUtils.isNotEmpty(scriptWorkDir)) {
                baseList.add("SCRIPT_WORK_DIR_s='" + scriptWorkDir + "'");
            }

            agentMap.forEach((key,agent) -> {
                List<String> paramList = new ArrayList<>();
                paramList.addAll(baseList);
                String str = ENTEGOR_LOCAL_HOSTNAME + "_s='" + (StringUtil.isBlank(agent.getIcomName())?"":agent.getIcomName()) + "'";
                paramList.add(str);
                str = ENTEGOR_LOCAL_BUSINESS_IP + "_s='" + (StringUtil.isBlank(agent.getIbusinessip())?"":agent.getIbusinessip()) + "'";
                paramList.add(str);
                str = ENTEGOR_LOCAL_MANAGER_IP + "_s='" + (StringUtil.isBlank(agent.getAgentIp())?"":agent.getAgentIp()) + "'";
                paramList.add(str);
                str = ENTEGOR_LOCAL_BUSINESS_SYS + "_s='" + (StringUtil.isBlank(agent.getIbusinesssys())?"":agent.getIbusinesssys()) + "'";
                paramList.add(str);

                String param = "";
                if (baseList.size() ==0){

                }else {
                    param = Constants.SCRIPT_ENVIRONMENT_VAL + "=" + String.join(Constants.SCRIPT_VAL_CONN, paramList);
                    DesUtils desUtils = new DesUtils("SCRIPT_ENVIRONMENT_VAL");
                    try {
                        param = desUtils.encrypt(param);
                    } catch (Exception e) {
                        _log.error("变量库加密失败", e);
                    }
                }
                dataMap.put(agent.getAgentIp() + ":" + agent.getAgentPort(), param);
            });

            return dataMap;
        }catch(Exception e){
            _log.error("处理执行可用变量异常",e);
            return null;
        }
    }

    /**
     * 导入数据base62解密
     * @param list
     */
    private void handlerData(List<VariableBaseBean> list){
        for (VariableBaseBean bean:list) {
            bean.setIname(Base62.decodeStr(bean.getIname()));
            bean.setIvalue(null != bean.getIvalue()? Base62.decodeStr(bean.getIvalue()):"");
            bean.setIdesc(null != bean.getIvalue()? Base62.decodeStr(bean.getIdesc()):"");
            if (bean.getParams() != null){
                handlerParamData(bean.getParams());
            }
        }
    }

    /**
     * 导入数据base62 解密 字典、数组参数
     * @param list
     */
    private void handlerParamData(List<VariableParamBean> list){
        if (list.size() == 0){
            return;
        }
        for (VariableParamBean bean:list) {
            bean.setIkey(null != bean.getIkey()?Base62.decodeStr(bean.getIkey()):"");
            bean.setIvalue(null != bean.getIvalue()?Base62.decodeStr(bean.getIvalue()):"");
        }
    }

    /**
     * 规则：变量名称只允许输入字母、数字、下划线，首字符不能为数字。
     * @param name
     * @return
     */
    private boolean checkVariableName(String name){
        char[] chars = name.toUpperCase(Locale.ROOT).toCharArray();
        for (int i = 0; i < chars.length; i++) {
            int num = chars[i];
            // A-Z
            if (num >= 65 && num <= 90){
                continue;
                // _
            }else if (num == 95){
                continue;
                // 0-9
            }else if (num >= 48 && num <= 57 && i != 0){
                continue;
            }
            return false;
        }
        return true;
    }

    /**
     * 定时任务，定期执行获取4A密码的方法
     * 默认每天0点执行一次
     * 需要在entegor.config里配置执行周期   标识：ScriptVariableCycleTimeTask
     */
    @Override
    @Scheduled(cron = "${ScriptVariableCycleTimeTask:0 0 0 * * ?}")
    public void cronGetPassword() {
        boolean scriptFouraTimingTaskSwitch = Environment.getInstance().scriptFouraTimingTaskSwitch();
        if(scriptFouraTimingTaskSwitch){
            _log.info("定时任务：非自动获取的4A密码类型变量，获取4A密码");
            //获取有所非自动获取的4A密码变量的iid
            List<Long> fouraIId = this.getAllNotAutoIIDS();
            for(Long variableIID : fouraIId){
                //根据变量id获取4A密码类型变量的基本信息（登录名、密码、账号、资源）
                //如果4A密码基本信息为添加，则不能获取4A密码
                Map fourAData = this.getFourADetailsData(String.valueOf(variableIID),false);
                if(!com.ideal.ieai.server.util.ObjectUtils.isEmpty(fourAData.get("iusername"))){
                    String iusername = String.valueOf(fourAData.get("iusername"));
                    String ipassword = String.valueOf(fourAData.get("ipassword"));
                    String iaccount = String.valueOf(fourAData.get("iaccount"));
                    String iresourcename = String.valueOf(fourAData.get("iresourcename"));
                    //autoObatain参数说明：非自动获取类型，所以直接传0
                    //第一个参数timetask为  是否为定时任务的标识  true只更新主表
                    this.getFourAPwdForInterface(true,String.valueOf(variableIID),iusername, ipassword, iaccount,iresourcename,"0");
                }
            }
        }
    }
    @Override
    public List<VariableClassBean> getClassList(int classType){
        Connection conn = null;
        List<VariableClassBean> list = new ArrayList<>();
        try {
            conn = DBResource.getConnection("getClassList", _log, Constants.IEAI_SCRIPT_SERVICE);
            // 获取一级分类
            list = VariableBaseManager.getInstance().getClassListByParentId(conn, 0,classType);
            // 无限向下获取子级分类
            getNextClassList(conn, list,classType);
        }catch(Exception e){
            _log.error("[变量库]获取数据源异常",e);
        }finally {
            DBResource.closeConnection(conn, "getClassList", _log);
        }
        return list;
    }
    public void getNextClassList(Connection conn, List<VariableClassBean> list,int classType){
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setChildren(VariableBaseManager.getInstance().getClassListByParentId(conn, list.get(i).getIid(),classType));
            if (list.get(i).getChildren().size() > 0){
                getNextClassList(conn, list.get(i).getChildren(),classType);
            }
        }
    }
    @Override
    public Map saveClass(VariableClassBean bean){
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "保存失败");
        if (VariableBaseManager.getInstance().existClass(bean)){
            res.put(MSG, "分类名称不能重复");
            return res;
        }
        if (VariableBaseManager.getInstance().saveClass(bean)){
            res.put(SUCCESS, true);
            res.put(MSG, "保存成功");
        }
        return res;
    }
    @Override
    public Object getClassName(long iid,int classType){
        return VariableBaseManager.getInstance().getClassName(iid,classType);
    }

    @Override
    public Map updateClass(VariableClassBean bean){
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "修改失败");
        if (VariableBaseManager.getInstance().existClass(bean)){
            res.put(MSG, "分类名称不能重复");
            return res;
        }
        if (VariableBaseManager.getInstance().updateClass(bean)){
            res.put(SUCCESS, true);
            res.put(MSG, "修改成功");
        }
        return res;
    }
    @Override
    public Map delClass(long iid,int classType){
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "删除失败");
        if (VariableBaseManager.getInstance().haveChildren(iid,classType)) {
            res.put(MSG, "分类下含有子级分类，无法删除");
            return res;
        }
        Map map = VariableBaseManager.getInstance().delClass(iid,classType);
        if (map.get("delete").equals(true)) {
            res.put(SUCCESS, true);
            res.put(MSG, "删除成功");
        } else  {
            if (classType == 1) {
                res.put(MSG, "该分类下有变量禁止删除");
            } else {
                res.put(MSG, "该分类下有函数禁止删除");
            }

        }
        return res;
    }

    @Override
    public Map updateVariableClass(String iids, String paraid) throws Exception {
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "操作失败");
        List<String> uuidList = JSONArray.parseArray(iids, String.class);
        try {
            long classid = Long.valueOf(paraid);
            int result = VariableBaseManager.getInstance().updateVariableClass(uuidList, classid);
            if (result > 0){
                res.put(SUCCESS, true);
                res.put(MSG, "修改成功");
            }
        } catch(Exception e){
            throw new Exception("批量修改异常", e);
        }
        return res;
    }

    @Override
    public Map updateFunctionLibraryClass(String iids, String paraid,String iidsP) throws Exception {
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MSG, "操作失败");
        List<String> list = JSONArray.parseArray(iids, String.class);
        List<String> listP = JSONArray.parseArray(iidsP, String.class);
        try {
            long classid = Long.valueOf(paraid);
            int result = VariableBaseManager.getInstance().updateFunctionLibraryClass(list, classid,listP);
            if (result > 0){
                res.put(SUCCESS, true);
                res.put(MSG, "修改成功");
            }
        } catch(Exception e){
            throw new Exception("批量修改异常", e);
        }
        return res;
    }
}
