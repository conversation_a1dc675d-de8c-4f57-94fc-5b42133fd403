-- 4.7.17 version does not have patches for compare
-- 4.7.18 version does not have patches for compare
-- 4.7.19 version compare patch is as follows
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPARE_CLASS' AND COLUMN_NAME='ICLASSTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COMPARE_CLASS ADD ICLASSTYPE NUMERIC(1)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	IF LI_EXISTS = 0 THEN
		LS_SQL :='UPDATE IEAI_COMPARE_CLASS SET ICLASSTYPE=0';
		EXECUTE IMMEDIATE LS_SQL;
		COMMIT;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPARE_PRO_AUTO_START' AND COLUMN_NAME='ISTARTTIMECRON';
	IF LI_EXISTS = 0 THEN
		LS_SQL :='ALTER TABLE IEAI_COMPARE_PRO_AUTO_START ADD ISTARTTIMECRON VARCHAR2(50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COMPARE_SWITCH' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_COMPARE_SWITCH(IID  NUMBER(19),IPROGRAMMEID  NUMBER(19),ITYPE  NUMBER(1),IPROID  NUMBER(19),ISWITCHID  NUMBER(19),ICHECKNAME VARCHAR2(255),CONSTRAINT PK_IEAI_COMPARE_SWITCH PRIMARY KEY (IID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPARE_RESULT' AND COLUMN_NAME='ISWITCHSTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COMPARE_RESULT ADD ISWITCHSTATE NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	COMMIT;		
END;
/

-- 4.7.20 version does not have patches for compare
-- 4.7.21 version compare patch is as follows
DECLARE
	 LS_SQL VARCHAR2(2000);
	 LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPARE_PROGRAMME_BASE' AND COLUMN_NAME='IISDISABLED';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COMPARE_PROGRAMME_BASE ADD IISDISABLED NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_NODERESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_NODERESULT (IID NUMBER(19) NOT NULL,IRESULTID NUMBER(19),ISYSNAME VARCHAR2(255),ISOURCEIP VARCHAR2(255),ITARGETIP VARCHAR2(255),ISOURCENAME VARCHAR2(255),ITARGETNAME VARCHAR2(255),IRESULT NUMBER(1),ISTATE NUMBER(1),ISOURCEPORT NUMBER(19),ITARGETPORT NUMBER(19),CONSTRAINT PK_IEAI_SYNC_NODERESULT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_RESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_RESULT (IID NUMBER(19) NOT NULL,IPLANID NUMBER(19),ISTATE NUMBER(1),IRESULT NUMBER(1),IRUNTIME NUMBER(19),CONSTRAINT PK_IEAI_SYNC_SYNCRESULT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_DIRRESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_DIRRESULT (IID NUMBER(19) NOT NULL,INODERESULTID NUMBER(19),ITYPE NUMBER(1),IPATH VARCHAR2(255),INATCHSYNC NUMBER(1),IALLSYNC NUMBER(1),ISYNCSTRATEGY VARCHAR2(255),IRESULTSTR CLOB,IRESULT NUMBER(1),ISTATE NUMBER(1),IFLOWID NUMBER(19),ISENCRYPTION NUMBER(1),CONSTRAINT PK_IEAI_SYNC_DIRRESULT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_STRATEGYPLAN' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_STRATEGYPLAN (IID NUMBER(19) NOT NULL,IPLANNAME VARCHAR2(255),IPLANDESC VARCHAR2(255),ISOURCEID NUMBER(19),ITARGETID NUMBER(19),ICREATETIME NUMBER(19),ICREATER VARCHAR2(255),CONSTRAINT PK_IEAI_SYNC_STRATEGYPLAN PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_PLANVIEW' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_PLANVIEW (IID NUMBER(19) NOT NULL,IPLANID NUMBER(19),ISYSID NUMBER(19),CONSTRAINT PK_IEAI_SYNC_PLANVIEW PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_CRONTASK' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_CRONTASK (IID NUMBER(19) NOT NULL,IPLANID NUMBER(19),ICRON VARCHAR2(255),ISTATUS NUMBER(1),CONSTRAINT PK_IEAI_SYNC_CRONTASK PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_AGENTRELATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_AGENTRELATION (IID NUMBER(19) NOT NULL,ISYSID NUMBER(19),ISOURCEAGENTID NUMBER(19),ITARGETAGENTID NUMBER(19),ISOURCEID NUMBER(19),ITARGETID NUMBER(19),CONSTRAINT PK_IEAI_SYNC_AGENTRELATION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_SYSTEMAGENT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_SYSTEMAGENT (IID NUMBER(19) NOT NULL,ISYSID NUMBER(19),IAGENTID NUMBER(19),CONSTRAINT PK_IEAI_SYNC_SYSTEMAGENT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_CATALOG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SYNC_CATALOG (IID NUMBER(19) NOT NULL,IRELATIONID NUMBER(19),ITYPE NUMBER(1),ISTATUS NUMBER(1),IPATH VARCHAR2(255),INATCHSYNC NUMBER(1),IALLSYNC NUMBER(1),ISYNCSTRATEGY VARCHAR2(255),ISENCRYPTION NUMBER(1),CONSTRAINT PK_IEAI_SYNC_CATALOG PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IDCID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IDCID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-13;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-13, 0, ''所有一致性比对业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 13, -13, -13, -13)';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;

END;
/

-- 4.7.22 version does not have patches for compare
-- 4.7.23 version compare patch is as follows
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_CATALOG' AND COLUMN_NAME='IPATHNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SYNC_CATALOG ADD IPATHNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_CRONTASK' AND COLUMN_NAME='ICREATER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SYNC_CRONTASK ADD ICREATER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_CRONTASK' AND COLUMN_NAME='ICREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SYNC_CRONTASK ADD ICREATETIME NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_RESULT' AND COLUMN_NAME='IUSETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SYNC_RESULT ADD IUSETIME NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

-- 4.7.24 version compare patch is as follows
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPARE_PROGRAMME' AND COLUMN_NAME='ISYSID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COMPARE_PROGRAMME ADD ISYSID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

-- 4.7.25 version does not have patches for compare
-- 4.7.26 version does not have patches for compare
-- 4.7.27 version compare patch is as follows
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_WARNSTATE' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SYNC_WARNSTATE (IID NUMBER(19) NOT NULL,IRESULTID NUMBER(19),ISYSNAME VARCHAR2(255),ISTATE NUMBER(1),CONSTRAINT PK_IEAI_SYNC_WARNSTATE PRIMARY KEY (IID))';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG WHERE IID=28;
IF  LI_EXISTS = 0 THEN
	LS_SQL :='INSERT INTO IEAI_PARAMETER_CONFIG(IID,IPARAMETER,IPARAVALUE) VALUES(28,''socketRsyncScript'',''-s ##/rsync_startsocket.sh##'')';
	EXECUTE IMMEDIATE LS_SQL;
END  IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_SOCKETFLOW' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SYNC_SOCKETFLOW (IID NUMBER(10) NOT NULL,IAGENTID NUMBER(19),IFLOWID NUMBER(19),ISTATE NUMBER(1),IRESULTSTR VARCHAR2(255),IRESULT NUMBER(1),CONSTRAINT PK_SYNC_SOCKETFLOW PRIMARY KEY (IID))';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
COMMIT;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_STEP_INSTANCEINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_STEP_INSTANCEINFO (IID NUMBER(19) NOT NULL,IINSTANCEID NUMBER(19),IINSTANCENAME VARCHAR2(255),ISERNER VARCHAR2(255),ICONNER NUMBER(19),ICONNERNAME VARCHAR2(100),IPRENER VARCHAR2(255),IACTNAME VARCHAR2(255),IACTDES VARCHAR2(100),IACTTYPE INTEGER,IREMINFO VARCHAR2(255),IIPNAME VARCHAR2(100),IMODELTYPE VARCHAR2(100),IIP VARCHAR2(2000),IPORT INTEGER,ISYSTYPE INTEGER,IEXECUSER VARCHAR2(100),ISHELLSCRIPT VARCHAR2(800),IISLOADENV INTEGER,ISHELLPATH VARCHAR2(800),ITIMEOUT NUMBER(19),ISWITCHSYSTYPE INTEGER,IPARAMETER VARCHAR2(255),IPARACHECK VARCHAR2(255),IPARASWITCH VARCHAR2(255),IPARASWITCHFORCE VARCHAR2(255),IEXPECEINFO VARCHAR2(50),IEXCEPTINFO VARCHAR2(50),IREDOABLE INTEGER,IISDISABLE INTEGER,IPKGNAME VARCHAR2(255),ICENTER VARCHAR2(255),IRETNVALEXCEPION VARCHAR2(255),APPIDENTITY VARCHAR2(255),IPRESYSNAME VARCHAR2(255),IPREACTNAME VARCHAR2(1000),ITYPE INTEGER,IMODELNAME VARCHAR2(255),IMODELVERSION NUMBER(19),ISCRIPTID NUMBER(19),IMXGRAPHID NUMBER(19),ISYSTEMTYPESTEP VARCHAR2(255),IMODELTYPESTEP VARCHAR2(255),SINGLEROLLBACK VARCHAR2(255),IBRANCH VARCHAR2(255),IPAIR VARCHAR2(255),IHIGHTRISK INTEGER,IJLDISABLE INTEGER,ICONCURRENCY VARCHAR2(255),IINTERVALTIME VARCHAR2(255),ICFGFILECHANGE VARCHAR2(255),IOWNERTYPE NUMBER(1) DEFAULT -1,IOWNER VARCHAR2(255),IEXPBEGINTIME NUMBER(19) DEFAULT 0,IEXPENDTIME NUMBER(19) DEFAULT 0,IREMARK VARCHAR2(1000),IPROJECTNAME VARCHAR2(255),IREVIEWER VARCHAR2(255),IBANKNAME VARCHAR2(255),IREVIEWSTATE INTEGER DEFAULT 1,ISROLLBACK INTEGER DEFAULT 0,ISRTO NUMBER(1) DEFAULT 0,IINFORTO NUMBER(19) DEFAULT 0,ICALLINSTANCENAME VARCHAR2(255),ICALLPKGNAME VARCHAR2(255),ICALLVERSIONDES VARCHAR2(255),ICALLSTARTTIMES VARCHAR2(255),ICALLSTARTENVNAMES VARCHAR2(255),IROLLBACKSCHEME VARCHAR2(255),INSTANCECLASS NUMBER(19),ISHAREINFO NUMBER(1),ICLASSIDTWO NUMBER(19),SYSTEMNAME VARCHAR2(255),CONSTRAINT PK_IEAI_STEP_INSTANCEINFO PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCOMPARE_CONFRESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCOMPARE_CONFRESULT (IID NUMBER(19) NOT NULL,IRESULTSYSID NUMBER(19),ITYPE VARCHAR2(255),IPATH VARCHAR2(255),IALLSYNC NUMBER(1),INATCHSYNC NUMBER(1),IISPASS NUMBER(1),IISPASSHTML CLOB,IENCODE VARCHAR2(255),ISYNCSTRATEGY VARCHAR2(255),IFLOWID NUMBER(19),CONSTRAINT PK_IEAI_SCOMPARE_CONFRESULT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCOMPARE_SYSRESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCOMPARE_SYSRESULT (IID NUMBER(19) NOT NULL,IRESULTID NUMBER(19),ISYSNAME VARCHAR2(255),ISOURCEIP VARCHAR2(255),ITARGETIP VARCHAR2(255),ISOURCEPORT NUMBER(19),ITARGETPORT NUMBER(19),ISOURCENAME VARCHAR2(255),ITARGETNAME VARCHAR2(255),IISPASS NUMBER(1),CONSTRAINT PK_IEAI_SCOMPARE_SYSRESULT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCOMPARE_RESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCOMPARE_RESULT (IID NUMBER(19) NOT NULL,IRESULTNAME VARCHAR2(255),IRESULTDES VARCHAR2(255),ICOMPARETIME NUMBER(19),IISPASS NUMBER(1),IUSETIME NUMBER(19),CONSTRAINT PK_IEAI_SCOMPARE_RESULT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_CATALOG' AND COLUMN_NAME='IMODEL';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_SYNC_CATALOG ADD IMODEL NUMBER(1)';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_CATALOG' AND COLUMN_NAME='IENCODE';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_SYNC_CATALOG ADD IENCODE VARCHAR2(255)';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SYNC_STRATEGYPLAN' AND COLUMN_NAME='IMODEL';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_SYNC_STRATEGYPLAN ADD IMODEL NUMBER(1)';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCOMPARE_CRONTASK' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SCOMPARE_CRONTASK (IID NUMBER(19) NOT NULL,IPLANID NUMBER(19),ICRON VARCHAR2(255),ISTATUS NUMBER(1),ICREATER VARCHAR2(255),ICREATETIME NUMBER(19),CONSTRAINT PK_IEAI_SCOMPARE_CRONTASK PRIMARY KEY (IID))';
	EXECUTE IMMEDIATE LS_SQL;
END IF;


END;
/

--update values
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM ieai_sync_catalog where imodel is null;
IF LI_EXISTS > 0 THEN
	LS_SQL := 'update  ieai_sync_catalog set imodel=1 where imodel is null';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM ieai_sync_strategyplan where imodel is null;
IF LI_EXISTS > 0 THEN
	LS_SQL := 'update  ieai_sync_strategyplan set imodel=1 where imodel is null';
	EXECUTE IMMEDIATE LS_SQL;
END IF;

END;
/


	
--update 2020.09.29
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COMPARE_RELATION' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_COMPARE_RELATION (IID NUMBER(19) NOT NULL,ISYSID NUMBER(19),ICLASSID NUMBER(19),CONSTRAINT PK_IEAI_COMPARE_RELATION PRIMARY KEY (IID))';
	EXECUTE IMMEDIATE LS_SQL;
END IF;

END;
/

	--update 2020.10.19
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCOMPARE_CONFRESULT' AND COLUMN_NAME='IPATHNAME';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_SCOMPARE_CONFRESULT ADD IPATHNAME VARCHAR2(255)';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCOMPARE_SYSRESULT' AND COLUMN_NAME='ISOURCECOMNAME';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_SCOMPARE_SYSRESULT ADD ISOURCECOMNAME VARCHAR2(255)';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCOMPARE_SYSRESULT' AND COLUMN_NAME='ITARGETCOMNAME';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_SCOMPARE_SYSRESULT ADD ITARGETCOMNAME VARCHAR2(255)';
	EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYNC_CATELOGRELATION' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SYNC_CATELOGRELATION (IID NUMBER(19) NOT NULL,ICATELOGID NUMBER(19),IPLANVIEWID NUMBER(19),IPLANID NUMBER(19),CONSTRAINT PK_IEAI_SYNC_CATELOGRELATION PRIMARY KEY (IID))';
	EXECUTE IMMEDIATE LS_SQL;
END IF;



END;
/


--V8.3.0
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPARE_BASE' AND COLUMN_NAME='IBINDSYSTEM';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_COMPARE_BASE ADD IBINDSYSTEM NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COMPARE_PERMISSION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_COMPARE_PERMISSION (ICLASSID NUMBER (19) ,IUSERID NUMBER (19))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/	
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;  
BEGIN
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM COL WHERE TNAME = 'IEAI_SYNC_CATALOG' AND CNAME = 'ISYNCSTRATEGY';
		  IF LI_EXISTS = 1 THEN
		    LS_SQL := 'ALTER TABLE IEAI_SYNC_CATALOG MODIFY ISYNCSTRATEGY VARCHAR2(2048)';
		    EXECUTE IMMEDIATE LS_SQL;
	  END IF; 
END;
/