	-- 4.7.17 version does not have patches for datacollect
	-- 4.7.18 version does not have patches for datacollect
	-- 4.7.19 version does not have patches for datacollect
	-- 4.7.20 version does not have patches for datacollect
	-- 4.7.21 version does not have patches for datacollect
	-- 4.7.22 version does not have patches for datacollect
	-- 4.7.23 version does not have patches for datacollect
	-- 4.7.24 version does not have patches for datacollect
	-- 4.7.25 version does not have patches for datacollect
	-- 4.7.26 version datacollect patch is as follows
	DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;
	BEGIN	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_LEVEL' AND COLUMN_NAME='ILEVELDESC';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_LEVEL ADD ILEVELDESC VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_LEVEL' AND COLUMN_NAME='ILEVELTYPE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_LEVEL ADD ILEVELTYPE VARCHAR2(100)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_LEVEL' AND COLUMN_NAME='ICREATETIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_LEVEL ADD ICREATETIME NUMBER(19)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_CLASSATTR' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_CLASSATTR (IID NUMBER(19) NOT NULL,ICLASSID NUMBER(19),IATTRNAME VARCHAR2(100),IATTRCODE VARCHAR2(100),IDEFAULTVALUE VARCHAR2(255),CONSTRAINT PK_IEAI_UNIFYAGENT_CLASSATTR PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_CA_INST' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_CA_INST (IID NUMBER(19) NOT NULL,ICLASSATTRID NUMBER(19),ICOLLECTINFOID NUMBER(19),IATTRNAME VARCHAR2(100),IATTRCODE VARCHAR2(100),IATTRVALUE VARCHAR2(255),CONSTRAINT PK_IEAI_UNIFYAGENT_CA_INST PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_COLLECTINFO' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_COLLECTINFO (IID NUMBER(19) NOT NULL,ISARELATIONID NUMBER(19),ICLASSID NUMBER(19),ICOLLECTNAME VARCHAR2(100),ICREATETIME NUMBER(19),ICREATEUSER VARCHAR2(255),CONSTRAINT PK_IEAI_UNIFYAGENT_COLLECTINFO PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_SA_RELATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_SA_RELATION (IID NUMBER(19) NOT NULL,ISYSID NUMBER(19),IAGENTID NUMBER(19),CONSTRAINT PK_IEAI_UNIFYAGENT_SA_RELATION PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_CM_INST' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_CM_INST (IID NUMBER(19) NOT NULL,IMODELID NUMBER(19),ICOLLECTINFOID NUMBER(19),IMODELNAME VARCHAR2(50),ICOLLECTTYPE NUMBER(1),IPERSONAL NUMBER(1),ITOPIC VARCHAR2(255),ICREATETIME NUMBER(19),ICHECKSCRIPT VARCHAR2(255),IRUNUSER VARCHAR2(100),IOUTPUTFORMAT VARCHAR2(255),IFILESOURCE VARCHAR2(4000),IINTERVALTIME VARCHAR2(10),IREADPOSITION VARCHAR2(10),ICHARSET VARCHAR2(10),IMULTIPLE VARCHAR2(10),IJDBCURL VARCHAR2(4000),ICONNUSER VARCHAR2(255),ICONNPASSWORD VARCHAR2(255),IMODE VARCHAR2(50),IINCREMENTINGCOL VARCHAR2(4000),ITIMESTAMPCOL VARCHAR2(4000),IQUERY VARCHAR2(4000),ITIMINGTYPE NUMBER(1),ITIMINGEXPRESSION VARCHAR2(255),ISENDKFK NUMBER(1),CONSTRAINT PK_IEAI_UNIFYAGENT_CM_INST PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_COLLECTMODEL' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_COLLECTMODEL (IID NUMBER(19) NOT NULL,ICLASSID NUMBER(19),IMODELNAME VARCHAR2(50),ICOLLECTTYPE NUMBER(1),IPERSONAL NUMBER(1),ITOPIC VARCHAR2(255),ICREATETIME NUMBER(19),ICHECKSCRIPT VARCHAR2(255),IRUNUSER VARCHAR2(100),IOUTPUTFORMAT VARCHAR2(255),IFILESOURCE VARCHAR2(4000),IINTERVALTIME VARCHAR2(10),IREADPOSITION VARCHAR2(10),ICHARSET VARCHAR2(10),IMULTIPLE VARCHAR2(10),IJDBCURL VARCHAR2(4000),ICONNUSER VARCHAR2(255),ICONNPASSWORD VARCHAR2(255),IMODE VARCHAR2(50),IINCREMENTINGCOL VARCHAR2(4000),ITIMESTAMPCOL VARCHAR2(4000),IQUERY VARCHAR2(4000),ITIMINGTYPE NUMBER(1),ITIMINGEXPRESSION VARCHAR2(255),ISENDKFK NUMBER(1),CONSTRAINT PK_IEAI_UNIFYAGENT_CM PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_RUN_INST' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_RUN_INST (IID NUMBER(19) NOT NULL, ICOLLECTINFOID NUMBER(19),ICMINSTID NUMBER(19),IAGENTID NUMBER(19),IAGENTIP VARCHAR2(20),IAGENTPORT NUMBER(10),IRUNTIME NUMBER(19),IRUNSTATE NUMBER(1),IRETURNID NUMBER(19),ISCRIPTINFO CLOB,IMODELNAME VARCHAR2(50),ICOLLECTTYPE NUMBER(1),IPERSONAL NUMBER(1),ITIMINGTYPE NUMBER(1),ITIMINGEXPRESSION VARCHAR2(255),CONSTRAINT PK_IEAI_UNIFYAGENT_RUN_INST PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_RESULT' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_RESULT (IID NUMBER(19) NOT NULL,IRUNINSTID NUMBER(19),IRESULTSTATE NUMBER(1),IRESULTSTR VARCHAR2(4000),IRETURNTIME NUMBER(19),CONSTRAINT PK_IEAI_UNIFYAGENT_RESULT PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_MONITOR' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_AGENT_MONITOR (IID NUMBER(19) NOT NULL ,IAGENT_ID NUMBER(19) NULL ,IAGENT_IP VARCHAR2(20 BYTE) NULL ,ISTATE NUMBER(1) NOT NULL ,ICPU_THRESHOLD NUMBER(3) NOT NULL ,IMEMORY_THRESHOLD NUMBER(3) NOT NULL ,INET_THRESHOLD NUMBER(3) NOT NULL ,ICREATE_TIME DATE NOT NULL ,ICREATE_USER VARCHAR2(40 BYTE) NOT NULL ,IMONITOR_INTERVAL NUMBER(5) NOT NULL ,IIOREAD_THRESHOLD NUMBER(3,2),IIOWRITE_THRESHOLD NUMBER(3,2),CONSTRAINT PK_IEAI_AGENT_MONITOR PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_MONITOR_RESULT' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_AGENT_MONITOR_RESULT (IID NUMBER(19) NOT NULL ,IAGENT_ID NUMBER(19) NOT NULL ,IMONITOR_ID NUMBER(19) NOT NULL ,IMONITOR_TIME DATE NOT NULL ,IMONITOR_RESULT VARCHAR2(512 BYTE) NULL ,CONSTRAINT PK_IEAI_AGENT_MONITOR_RESULT PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_PROXY_CLUSTER' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_PROXY_CLUSTER (IID NUMBER(19) NOT NULL,INAME VARCHAR2(255),IDESC VARCHAR2(255),IZK VARCHAR2(255),ISTATUS NUMBER(1) DEFAULT 0,CONSTRAINT PK_IEAI_KAFKA_PROXY_CLUSTER PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_DATA_LINK' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_DATA_LINK (IID NUMBER(19) NOT NULL,ITYPE NUMBER(1) DEFAULT 0,IPARENTID NUMBER(19),INAME VARCHAR2(255),IDESC VARCHAR2(255),IIMG VARCHAR2(255),ICODE VARCHAR2(255),ICLUSTERID NUMBER(19) NOT NULL,CONSTRAINT PK_IEAI_KAFKA_DATA_LINK PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_AGENT_RELATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_AGENT_RELATION (IID NUMBER(19) NOT NULL,ICLUSTERID NUMBER(19),ILINKID NUMBER(19),IIP VARCHAR2(255),IPORT VARCHAR2(255),AGENTID NUMBER(19),IAGENTNAME VARCHAR2(255),IREMARK VARCHAR2(255),CONSTRAINT PK_IEAI_KAFKA_AGENT_RELATION PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_PROXY_STATUS' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_PROXY_STATUS (IID NUMBER(19) NOT NULL,IIP VARCHAR2(255),INAME VARCHAR2(255),ITOPIC VARCHAR2(255),ICONSUMENUM NUMBER(19),IMSGNUM NUMBER(19),ISUCCESSNUM NUMBER(19),IFAILNUM NUMBER(19),IRETRYNUM NUMBER(19),ITIME TIMESTAMP,CONSTRAINT PK_IEAI_KAFKA_PROXY_STATUS PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_PROXY' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_PROXY (IID NUMBER(19) NOT NULL,ICLUSTERID NUMBER(19),IINBROKER VARCHAR2(255),IGROUP VARCHAR2(255),IIP VARCHAR2(255),IPROXYNAME VARCHAR2(255),IAUTH NUMBER(1) DEFAULT 0,IINNAME VARCHAR2(50),IINPWD VARCHAR2(50),IMAXRETRY NUMBER(19),IPOLLINTERVAL NUMBER(19),IMAXBLOCK NUMBER(19),IMAXRBLOCK NUMBER(19),IMINBYTE NUMBER(19),IMAXBYTE NUMBER(19),IBROKERTIME NUMBER(19),IINTOPIC VARCHAR2(255),IOUTBROKER VARCHAR2(255),IOUTNANME VARCHAR2(50),IOUTPWD VARCHAR2(50),IOUTTOPIC VARCHAR2(255),ITOPICTYPE NUMBER(1) DEFAULT 0,ISERVERIP VARCHAR2(255),ISERVERPORT NUMBER(5),IREPORTTIME NUMBER(19),IREMARK VARCHAR2(255))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_PROXY_ALARM' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_PROXY_ALARM (IID NUMBER(19) NOT NULL,IIP VARCHAR2(255),INAME VARCHAR2(255),ILOSTNUM NUMBER(19),IALARMMSG VARCHAR2(255),ITIME TIMESTAMP,CONSTRAINT PK_IEAI_KAFKA_PROXY_ALARM PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_KAFKA_DESTINATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_KAFKA_DESTINATION (IID NUMBER(19) NOT NULL,IBROKER VARCHAR2(255),IPORT VARCHAR2(255),IAUTH NUMBER(1),INAME VARCHAR2(50),IPASSWORD VARCHAR2(50),ILINKID NUMBER(19),CONSTRAINT PK_IEAI_KAFKA_DESTINATION PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_BUSY_CONFIG' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_AGENT_BUSY_CONFIG (IID NUMBER(19) NOT NULL ,IAGENT_ID NUMBER(19) NOT NULL ,IAGENT_NAME VARCHAR2(40) NULL ,IAGENT_IP VARCHAR2(40) NULL ,ISTART_TIME VARCHAR2(10) NULL ,IEND_TIME VARCHAR2(10) NULL ,ICREATE_TIME DATE NULL  ,CONSTRAINT PK_IEAI_AGENT_BUSY_CONFIG PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFYAGENT_LIST' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFYAGENT_LIST (IID NUMBER(19) NOT NULL ,ISLIST NUMBER(1) NULL ,INSTID NUMBER(19) NULL  ,CONSTRAINT PK_IEAI_UNIFYAGENT_LIST PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATACOLLECT_SIZE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DATACOLLECT_SIZE (IID NUMBER(19) NOT NULL,IINSTID NUMBER(19),ISTARTTIME NUMBER(19),IENDTIME NUMBER(19),ISIZE NUMBER(19),CONSTRAINT PK_IEAI_DATACOLLECT_SIZE PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATACOLLECT_WARN' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DATACOLLECT_WARN (IID NUMBER(19) NOT NULL,IINSTID NUMBER(19),ITIME NUMBER(19),IMESSAGE VARCHAR2(512),CONSTRAINT PK_IEAI_DATACOLLECT_WARN PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFY_CLEAN_TASK' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFY_CLEAN_TASK (IID NUMBER(19) NOT NULL,ITABLEID NUMBER(19) NOT NULL,IKEEPTIME NUMBER(19),IREMARK VARCHAR2(4000),constraint PK_IEAI_UNIFY_CLEAN_TASK primary key (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UNIFY_CLEAN_TABLE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_UNIFY_CLEAN_TABLE (IID NUMBER(19) NOT NULL,ITABLE VARCHAR2(255),ICOLUMN VARCHAR2(255),IREMARK VARCHAR2(4000),constraint PK_IEAI_UNIFY_CLEAN_TABLE primary key (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
	END;
	/
	-- 4.7.27 version does not have patches for datacollect
	-- 4.7.28 version datacollect patch is as follows
	DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;
	BEGIN	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATACOLLECT_WARN' AND COLUMN_NAME='ISTATE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATACOLLECT_WARN ADD ISTATE NUMBER(1)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_RESULT' AND COLUMN_NAME='IRESULTSTR' AND DATA_TYPE!='CLOB';
		IF LI_EXISTS = 1 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_RESULT DROP COLUMN IRESULTSTR';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_RESULT' AND COLUMN_NAME='IRESULTSTR';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_RESULT ADD IRESULTSTR CLOB';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='ICUSTOMER';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD ICUSTOMER VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='ICUSTOMER';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD ICUSTOMER VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATACOLLECT_SIZE' AND COLUMN_NAME='ICOUNT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATACOLLECT_SIZE ADD ICOUNT NUMBER(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
	commit;
	END;
	/
	
	--4.7.29
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='IMODIFYTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD IMODIFYTIME NUMBER(19)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='IMODIFYTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD IMODIFYTIME NUMBER(19)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;


	commit;
	END;
	/
	
	
	
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN 
    
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_PARTITIONS WHERE TABLE_NAME='IEAI_DATACOLLECT_WARN'; 
		IF LI_EXISTS = 0 THEN
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATACOLLECT_WARN' AND OBJECT_TYPE = 'TABLE';
			IF LI_EXISTS != 0 THEN
				LS_SQL := 'DROP TABLE IEAI_DATACOLLECT_WARN';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			LS_SQL :='CREATE TABLE IEAI_DATACOLLECT_WARN(IID NUMBER(19) NOT NULL, IINSTID  NUMBER(19),ITIME NUMBER(19),IMESSAGE VARCHAR2(512),ISTATE NUMBER(1) ,IDATETIME DATE DEFAULT CURRENT_TIMESTAMP,CONSTRAINT PK_IEAI_DATACOLLECT_WARN PRIMARY KEY (IID))PARTITION BY RANGE (IDATETIME)  INTERVAL (NUMTODSINTERVAL(1, ''DAY''))(PARTITION PARTITION1 VALUES LESS THAN (TO_DATE(''1970/01/01'',''YYYY/MM/DD'')))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_PARTITIONS WHERE TABLE_NAME='IEAI_DATACOLLECT_SIZE'; 
			IF LI_EXISTS = 0 THEN
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATACOLLECT_SIZE' AND OBJECT_TYPE = 'TABLE';
			IF LI_EXISTS != 0 THEN
				LS_SQL := 'DROP TABLE IEAI_DATACOLLECT_SIZE';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			LS_SQL :='CREATE TABLE IEAI_DATACOLLECT_SIZE(IID NUMBER(19) NOT NULL,IINSTID NUMBER(19),ISTARTTIME NUMBER(19),IENDTIME NUMBER(19),ISIZE NUMBER(19),ICOUNT NUMBER(10),IDATETIME DATE DEFAULT CURRENT_TIMESTAMP, CONSTRAINT PK_IEAI_DATACOLLECT_SIZE PRIMARY KEY (IID))PARTITION BY RANGE (IDATETIME)  INTERVAL (NUMTODSINTERVAL(1, ''DAY''))(PARTITION PARTITION1 VALUES LESS THAN (TO_DATE(''1970/01/01'',''YYYY/MM/DD'')))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_PARTITIONS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT'; 
			IF LI_EXISTS = 0 THEN
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_MONITOR_RESULT' AND OBJECT_TYPE = 'TABLE';
			IF LI_EXISTS != 0 THEN
				LS_SQL := 'DROP TABLE IEAI_AGENT_MONITOR_RESULT';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			LS_SQL :='CREATE TABLE IEAI_AGENT_MONITOR_RESULT(IID NUMBER(19) NOT NULL,IAGENT_ID NUMBER(19) NOT NULL,IMONITOR_ID NUMBER(19) NOT NULL,IMONITOR_TIME DATE NOT NULL,IMONITOR_RESULT VARCHAR2(512) ,IDATETIME DATE DEFAULT CURRENT_TIMESTAMP,CONSTRAINT PK_IEAI_AGENT_MONITOR_RESULT PRIMARY KEY (IID))PARTITION BY RANGE (IDATETIME)  INTERVAL (NUMTODSINTERVAL(1, ''DAY''))( PARTITION PARTITION1 VALUES LESS THAN (TO_DATE(''1970/01/01'',''YYYY/MM/DD'')))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DATACOLLECT_SIZE_01' AND OBJECT_TYPE = 'INDEX';
		IF  LI_EXISTS = 0 THEN
		  LS_SQL := 'CREATE INDEX IDX_IEAI_DATACOLLECT_SIZE_01 ON IEAI_DATACOLLECT_SIZE (IINSTID)';
		  EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR' AND COLUMN_NAME='ICPU_THRESHOLDTWO';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR ADD ICPU_THRESHOLDTWO NUMBER(3)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR' AND COLUMN_NAME='IMEMORY_THRESHOLDTWO';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR ADD IMEMORY_THRESHOLDTWO NUMBER(3)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='IMAINBAKFILESIZE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD IMAINBAKFILESIZE VARCHAR(3)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='IMAINBAKFILESIZE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD IMAINBAKFILESIZE VARCHAR(3)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATACOLLECT_ALARM' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DATACOLLECT_ALARM (IID NUMBER(10) NOT NULL,IWARNMSG VARCHAR2(512),IWARNTIME NUMBER(19),CONSTRAINT PK_IEAI_DATACOLLECT_ALARM PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATACOLLECT_ALARM' AND COLUMN_NAME='IWARNTYPE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATACOLLECT_ALARM ADD IWARNTYPE VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATACOLLECT_ALARM' AND COLUMN_NAME='IWARNIP';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATACOLLECT_ALARM ADD IWARNIP VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	END;
	/
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='ICPU_VALUE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD ICPU_VALUE VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='IMEM_VALUE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD IMEM_VALUE VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='IREADKB_VALUE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD IREADKB_VALUE VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='IWRITEKB_VALUE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD IWRITEKB_VALUE VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATACOLLECT_WARN' AND COLUMN_NAME='IMESSAGE' AND DATA_TYPE!='CLOB';
		IF LI_EXISTS = 1 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATACOLLECT_WARN DROP COLUMN IMESSAGE';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATACOLLECT_WARN' AND COLUMN_NAME='IMESSAGE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATACOLLECT_WARN ADD IMESSAGE CLOB';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
	END;
	/
	
	-- 8.1.0 version  patch is as follows
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
		
	BEGIN
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATACOLLECT_TIME' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DATACOLLECT_TIME (IID NUMBER(19) NOT NULL,IINSTID NUMBER(19),MAXTIME NUMBER(19),CONSTRAINT PK_IEAI_DATACOLLECT_TIME PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_COLLECT_CHARSET' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_COLLECT_CHARSET (IID NUMBER(12) NOT NULL,INAME VARCHAR2(20),IDESC VARCHAR2(500),CONSTRAINT PK_IEAI_IEAI_COLLECT_CHARSET PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

	END;
	/
	-- 8.3.0 version  patch is as follows
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
		
	BEGIN
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='ISTRANSFERBLANK';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD ISTRANSFERBLANK NUMBER(1) default 1';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='ISTRANSFERBLANK';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD ISTRANSFERBLANK NUMBER(1) default 1';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

	END;
	/
	
	DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
		
	BEGIN
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='UUID';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD UUID varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='SN';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD SN varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='OS_TYPE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD OS_TYPE varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='LOCALIP';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD LOCALIP varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='OS_SMALLTYPE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD OS_SMALLTYPE varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IBUSINESSIP';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IBUSINESSIP varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IBUSINESSSYS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IBUSINESSSYS varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_MONITOR_RESULT_MAIN' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_AGENT_MONITOR_RESULT_MAIN
		(iid   NUMBER(19) not null,
		iagent_id       NUMBER(19) not null,
		imonitor_id     NUMBER(19) not null,
		imonitor_time   DATE not null,
		imonitor_result VARCHAR2(512),
		idatetime       DATE default CURRENT_TIMESTAMP,
		icpu_value      VARCHAR2(10),
		imem_value      VARCHAR2(10),
		ireadkb_value   VARCHAR2(10),
		iwritekb_value  VARCHAR2(10),
		flag            VARCHAR2(32)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_IINSTALLATION_RECORD' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_IINSTALLATION_RECORD
		(iid                          NUMBER(19) not null,
		 uuid                         VARCHAR2(64),
		sftp_address                 VARCHAR2(1024),
		sftp_password                VARCHAR2(64),
		sftp_user                    VARCHAR2(64),
		sftp_account_number          VARCHAR2(32),
		collection_proxy             VARCHAR2(1024),
		installation_package_address VARCHAR2(1024),
		time_out                     VARCHAR2(64),
		icreate_time                 DATE,
		icreate_user                 VARCHAR2(128),
		install_state                VARCHAR2(16),
		del_flag                     VARCHAR2(16),
		sftp_port                    NUMBER(19),
		msg                          VARCHAR2(1024),
		iagentup_id                  NUMBER(19)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_IINSTALLATION_RECORD_MAIN' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_IINSTALLATION_RECORD_MAIN
		(iid                          NUMBER(19) not null,
		uuid                         VARCHAR2(64),
		sftp_address                 VARCHAR2(1024),
		sftp_password                VARCHAR2(64),
		sftp_user                    VARCHAR2(64),
		sftp_account_number          VARCHAR2(32),
		collection_proxy             VARCHAR2(1024),
		installation_package_address VARCHAR2(1024),
		time_out                     VARCHAR2(64),
		icreate_time                 DATE,
		icreate_user                 VARCHAR2(128),
		install_state                VARCHAR2(16),
		del_flag                     VARCHAR2(16),
		sftp_port                    NUMBER(19),
		msg                          VARCHAR2(1024),
		iagentup_id                  NUMBER(19)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DAEMONS_MANAGEMENT' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_DAEMONS_MANAGEMENT
		(daemons_id     NUMBER(19) not null,
		daemons_ip     VARCHAR2(255),
		daemons_port   NUMBER(19),
		idelete_flag   NUMBER(1) default 0,
		daemons_status VARCHAR2(16),
		icreate_time   DATE,
		icreate_user   VARCHAR2(128),
		uuid           VARCHAR2(64)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DAEMONS_PROXY_RELATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_DAEMONS_PROXY_RELATION
		(iid          NUMBER(32) not null,
		daemons_uuid VARCHAR2(64),
		proxy_id     NUMBER(32),
		proxy_type   NUMBER(32),
		icreate_time DATE,
		icreate_user VARCHAR2(32)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEPLOYMENT_PLAN' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_DEPLOYMENT_PLAN
		(iid                          NUMBER(19) not null,
		uuid                         VARCHAR2(64),
		sftp_address                 VARCHAR2(1024),
		sftp_password                VARCHAR2(64),
		sftp_user                    VARCHAR2(64),
		sftp_account_number          VARCHAR2(32),
		collection_proxy             VARCHAR2(1024),
		installation_package_address VARCHAR2(1024),
		time_out                     VARCHAR2(64),
		icreate_time                 DATE,
		icreate_user                 VARCHAR2(128),
		sftp_port                    NUMBER(19)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_MONITOR_CONFIG' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_MONITOR_CONFIG
		(iid            NUMBER(19) not null,
		iagent_id      NUMBER(19),
		daemons_id     VARCHAR2(64),
		iagent_name    VARCHAR2(64),
		iagent_ip      VARCHAR2(64),
		state          VARCHAR2(16),
		time_interval  NUMBER(19),
		task_name      VARCHAR2(64),
		script_address VARCHAR2(1024),
		icreate_time   DATE,
		icreate_user   VARCHAR2(64)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='POSTSCRIPT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD  POSTSCRIPT VARCHAR(4000)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='EXCELADDRESS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD  EXCELADDRESS VARCHAR(1024)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='EXCELNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD  EXCELNAME VARCHAR(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_COLLECTMODEL' AND COLUMN_NAME='SHEETNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD  SHEETNAME VARCHAR(16)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='ISONCLOUD';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD ISONCLOUD varchar2(3)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='SCRIPTPATH';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK  ADD  SCRIPTPATH VARCHAR(1024)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='POSTSCRIPT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD POSTSCRIPT VARCHAR(4000)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='EXCELADDRESS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD EXCELADDRESS VARCHAR(1024)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='EXCELNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD EXCELNAME VARCHAR(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_UNIFYAGENT_CM_INST' AND COLUMN_NAME='SHEETNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD SHEETNAME VARCHAR(16)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UPDATE_RESULT' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_UPDATE_RESULT
		(iid           NUMBER(19) not null,
		uuid          VARCHAR2(64),
		icreate_time  DATE,
		icreate_user  VARCHAR2(128),
		install_state VARCHAR2(16),
		msg           VARCHAR2(256)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_UPDATE_RESULT_MAIN' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL :='create table IEAI_UPDATE_RESULT_MAIN
		(iid           NUMBER(19) not null,
		uuid          VARCHAR2(64),
		icreate_time  DATE,
		icreate_user  VARCHAR2(128),
		install_state VARCHAR2(16),
		msg           VARCHAR2(256)
		)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
				-- CREATE TABLE IEAI_AGENT_LIVE
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_LIVE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := ' CREATE TABLE IEAI_AGENT_LIVE(
			IID NUMBER(19) NOT NULL,
			IAGENTIID NUMBER(19),
			IAGENTIP VARCHAR2(20),
			STATUS NUMBER(2),
			UPDATETIME NUMBER(19),
			SERVERIP VARCHAR2(20),
			UUID VARCHAR2(255) ,
			CONSTRAINT PK_IEAI_AGENT_LIVE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		-- CREATE TABLE IEAI_AGENT_LIVE_HISTORY
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_LIVE_HISTORY' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := ' CREATE TABLE IEAI_AGENT_LIVE_HISTORY(
			IID NUMBER(19) NOT NULL,
			IAGENTIID NUMBER(19),
			IAGENTIP VARCHAR2(20),
			STATUS NUMBER(2),
			UPDATETIME NUMBER(19),
			SERVERIP VARCHAR2(20),
			UUID VARCHAR2(255) ,
			CONSTRAINT IEAI_AGENT_LIVE_HISTORY PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='UPGRADESCRIPTNAME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD UPGRADESCRIPTNAME  VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='UPGRADESCRIPTCONTENT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD UPGRADESCRIPTCONTENT  CLOB';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='FLAG';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD FLAG VARCHAR(32)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		

	END;
	/
	DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

  
    SELECT  COUNT(*) INTO  LI_EXISTS FROM  IEAI_PARAMETER_CONFIG WHERE IID=31;
            IF  LI_EXISTS = 0  THEN
			
     LS_SQL := 'INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(31,''startagentcmd'',''./startagent.sh'')';
     EXECUTE IMMEDIATE LS_SQL;
   COMMIT;   
	SELECT  COUNT(*) INTO  LI_EXISTS FROM  IEAI_PARAMETER_CONFIG WHERE IID=32;
            IF  LI_EXISTS = 0  THEN
			
     LS_SQL := 'INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(32,''stopagentcmd'',''./stopagent.sh'')';
     EXECUTE IMMEDIATE LS_SQL;
	 END IF;
   COMMIT;    
   
       
 SELECT  COUNT(*) INTO  LI_EXISTS FROM  IEAI_DICTIONARY WHERE DID=10011;
            IF  LI_EXISTS = 0  THEN
     LS_SQL := 'INSERT INTO IEAI_DICTIONARY (DID,PDID,DCODE,DNAME,DORDER) values(10011,0,200,''获取agent状态最大数量'',1001)';
     EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	   END  IF; 
   COMMIT;
              
			  
			  
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MAINTAIN_TASK' AND COLUMN_NAME='FILE_UPLOAD_IID';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD FILE_UPLOAD_IID NUMBER(10)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
	 COMMIT;
		
		
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FILE_UPLOAD_MONITOR' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_FILE_UPLOAD_MONITOR (IID NUMBER(19) NOT NULL, UPLOAD_PATH   VARCHAR2(512), AGENT_IP   VARCHAR2(32), AGENT_PORT   NUMBER(19), STATE  VARCHAR2(64), MSG  VARCHAR2(1024), CREATE_TIME  DATE, CREATE_NAME  VARCHAR2(32),  FILE_UPLOAD_IID NUMBER(19))';		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	COMMIT;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_FILE_UPLOAD' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_AGENT_FILE_UPLOAD(IID NUMBER(19) NOT NULL,FILE_NAME  VARCHAR2(512),UPLOAD_NAME VARCHAR2(32),UPLOAD_TIME  DATE,STORAGE_BLOB  BLOB,STORAGE_TYPE   CHAR(1), STORAGE_CONFIG    CLOB,PRE_SCRIPT_NAME    VARCHAR2(32),  THE_REAR_SCRIPT_NAME VARCHAR2(32))';		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	COMMIT;
			  
          

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='AGENT_STATE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD AGENT_STATE VARCHAR2(16)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
   COMMIT;
   
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='THREADSIZE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD THREADSIZE VARCHAR2(16)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
   COMMIT;
   
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='TASKSIZE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD TASKSIZE VARCHAR2(16)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
   COMMIT;
   
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='DISK_USAGE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD DISK_USAGE VARCHAR2(16)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
   COMMIT;
   
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENT_MONITOR_RESULT' AND COLUMN_NAME='EXCEPTIONSIZE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD EXCEPTIONSIZE VARCHAR2(16)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYSTEM_INFO' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SYSTEM_INFO(ISYSINFOID NUMBER(19) NOT NULL, ISYSID NUMBER(19),ISYSCODE VARCHAR2(255),ISYSNAME VARCHAR2(255),IENGLISHCODE VARCHAR2(255),  IOPERATIONMANEGER VARCHAR2(255),  IOPERATIONMANEGERID VARCHAR2(255),  ISYSMANAGERA VARCHAR2(255),  ISYSMANAGERAID VARCHAR2(255),  ISYSMANAGERB VARCHAR2(255),  ISYSMANAGERBID VARCHAR2(255),  IAPPMANAGERA VARCHAR2(255), IAPPMANAGERAID VARCHAR2(255),  IAPPMANAGERB VARCHAR2(255), IAPPMANAGERBID VARCHAR2(255),  IMANEGER VARCHAR2(255),  IMANAGERID VARCHAR2(255),  ISTATUS VARCHAR2(30),  IDEPARTMENT VARCHAR2(255),  IDEPARTMENTID VARCHAR2(255), ISYSSERVICELEVEL VARCHAR2(255),  ISYSLEVEL VARCHAR2(255),  ISAFELEVEL VARCHAR2(255),IAPPGROUP VARCHAR2(255),IAPPGROUPID VARCHAR2(255),ISSYSTAKE VARCHAR2(255),ISCORESYS VARCHAR2(255),ISIMPSYS VARCHAR2(255),ISKEYSYS VARCHAR2(255),INETCONNECTTYPE VARCHAR2(255),INETRECOVERTYPE VARCHAR2(255),IARCHITECTURE VARCHAR2(255),IBUSINESSNET VARCHAR2(255),ISERVERTIME VARCHAR2(255),IEAPSNAME VARCHAR2(255),ISTOREMANEGER VARCHAR2(255),ISTOREMANEGERID VARCHAR2(255),INETMANEGERA VARCHAR2(255),INETMANEGERAID VARCHAR2(255),INETMANEGERB VARCHAR2(255),INETMANEGERBID VARCHAR2(255),IDBAMANEGERA VARCHAR2(255),IDBAMANEGERAID VARCHAR2(255),IDBAMANEGERB VARCHAR2(255),IDBAMANEGERBID VARCHAR2(255),IMIDDLEMANEGERA VARCHAR2(255),IMIDDLEMANEGERAID VARCHAR2(255),IMIDDLEMANEGERB VARCHAR2(255),IMIDDLEMANEGERBID VARCHAR2(255),ISERVERSUP VARCHAR2(255),ISERVERSUPID VARCHAR2(255),ICBRCIMPSYS VARCHAR2(4000),ISYSDES VARCHAR2(4000),ISYSPRO VARCHAR2(255),IAFFECTESYS VARCHAR2(255),ICRITICALITY VARCHAR2(255),IBRANCH VARCHAR2(255),ISCOPE VARCHAR2(255),INETWORK VARCHAR2(255),ISYSNUM VARCHAR2(255),ISYSUPLINETIME VARCHAR2(255),ISYSDOWNLINETIME VARCHAR2(255),ISERVERLEVEL VARCHAR2(255),ISWITCHTYPE VARCHAR2(255),ISYSMAINTENANCE VARCHAR2(255),IBACKUPTYPE VARCHAR2(255),INETSALFWORK VARCHAR2(255),ISYSLEVELB VARCHAR2(255),ISYSNAMEB  VARCHAR2(255),CONSTRAINT PK_IEAI_SYSTEM_INFO PRIMARY KEY (ISYSINFOID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_ADMIN_CFG' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_ADMIN_CFG(IID NUMBER(19) NOT NULL,IMANAGERNMAE VARCHAR2(255), ISCREAT VARCHAR2(1),CONSTRAINT PK_IEAI_ADMIN_CFG PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AGENT_SDINFO' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
                    LS_SQL := 'CREATE TABLE  IEAI_AGENT_SDINFO  (IID  NUMBER(20) NOT NULL ,IDOMAIN  VARCHAR2(255 )  ,IPORT  NUMBER(10) NOT NULL ,IADDRESSANALYSIS  VARCHAR2(30)  , ISYSTEMNAME  VARCHAR2(30) , ISYSTEMSHORT  VARCHAR2(20 )  , ISERVICENAME  VARCHAR2(30 )  , IRECORD  VARCHAR2(30 )  , IVIEWNAME  VARCHAR2(30 )  ,IAGENTINFOID  NUMBER(19)  ,IAGENTUPID  NUMBER(19) )';
        EXECUTE IMMEDIATE LS_SQL;
        END IF;	
    COMMIT;
  
END;
/