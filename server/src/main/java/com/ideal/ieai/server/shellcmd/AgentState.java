package com.ideal.ieai.server.shellcmd;

import org.apache.commons.net.telnet.TelnetClient;

import java.io.*;

/**
 * @author: zlc
 *
 * @since: 2013.10.25
 *
 * @see: 执行脚本工具类
 *
 */
public class AgentState
{

    public int execute ( String cmd )
    {
        try
        {
            Process process;
            // 执行命令
            process = Runtime.getRuntime().exec(cmd);
            // 取得命令结果的输出流
            InputStream fis = process.getInputStream();
            // 用一个读输出流类去读
            BufferedReader br = new BufferedReader(new InputStreamReader(fis));
            // 逐行读取输出到控制台
            while ((br.readLine()) != null)
            {
            }
            try
            {
                if (process.waitFor() != 0)
                {
                    _log.error("AgentState execute () error");
                }
            } catch (InterruptedException e)
            {
                e.printStackTrace();
            }
        } catch (IOException e)
        {
            e.printStackTrace();
        }
        return 0;
    }

    private TelnetClient telnet = new TelnetClient();

    private InputStream  in;

    private PrintStream  out;

    private char         prompt = '$';               // 普通用户结束

    public AgentState(String ip, int port, String user, String password)
    {
        try
        {
            telnet.connect(ip, port);
            in = telnet.getInputStream();
            out = new PrintStream(telnet.getOutputStream());
            // 根据用户设置结束符
            this.prompt = user.equals("root") ? '#' : '>';
            login(user, password);
        } catch (Exception e)
        {
            _log.error("telnet创建连接时出错：" + e.getMessage() + " ip：" + ip);
        }
    }

    public AgentState()
    {
    }

    /**
     * 登录
     *
     * @param user
     * @param password
     */
    public void login ( String user, String password )
    {
        if (readUntil("login:"))
        {
            write(user);
            if (readUntil("Password:"))
            {
                write(password);
                readUntil(prompt + "");
            }
        }
    }

    /**
     * 读取分析结果
     *
     * @param pattern
     * @return
     */
    public boolean readUntil ( String pattern )
    {
        try
        {
            char lastChar = pattern.charAt(pattern.length() - 1);
            StringBuffer sb = new StringBuffer();
            int i = -1;
            while ((i = in.read()) > -1)
            {
                char ch = (char) i;
                sb.append(ch);
                if (ch == lastChar)
                {
                    if(pattern.equals(prompt + ""))
                    {
                        if (sb.toString().endsWith('>'+"")||sb.toString().endsWith('#'+"")||sb.toString().endsWith('$'+""))
                        {
                            return true;
                        }
                    }
                    if (sb.toString().endsWith(pattern))
                    {
                        return true;
                    }
                }
            }
        } catch (Exception e)
        {
            _log.error("telnet 在读取数据时出错：" + pattern + " 错误信息： " + e);
            disconnect();
        }
        return false;
    }

    /**
     * 写操作
     *
     * @param value
     */
    public void write ( String value )
    {
        try
        {
            out.println(value);
            out.flush();
        } catch (Exception e)
        {
            _log.error("telnet 操作在输入时 产生错误 ：" + e);
            disconnect();
        }
    }

    /**
     * 向目标发送命令字符串
     *
     * @param command
     * @return
     */
    public void sendCommand ( String command )
    {
        try
        {
            write(command);
            readUntil(prompt + "");
        } catch (Exception e)
        {
            _log.error("sendconmmand error: " + e);
        }
    }

    /**
     * 关闭连接
     */
    public void disconnect ()
    {
        try
        {
            telnet.disconnect();
        } catch (Exception e)
        {
            _log.error("关闭telnet 连接出错 error: " + e);

        }
    }

    public static void main ( String[] args )
    {
        try
        {
            AgentState she = new AgentState("192.168.0.99", 23, "root", "root");
            she.sendCommand("cd /home/<USER>");
//             she.sendCommand("nohup ./Agent_Server &");
            she.sendCommand("./Agent_Server stop");
            she.disconnect();

        } catch (Exception e)
        {

        }

    }

    static final private org.apache.log4j.Logger _log = org.apache.log4j.Logger
            .getLogger(AgentState.class);

}