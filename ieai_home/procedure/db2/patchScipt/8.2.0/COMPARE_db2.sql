	-- 4.7.17 version does not have patches for compare
	-- 4.7.18 version does not have patches for compare
	-- 4.7.19 version compare patch is as follows	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICLASSTYPE' AND TABNAME='IEAI_COMPARE_CLASS';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPARE_CLASS ADD COLUMN ICLASSTYPE NUMERIC(1)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='UPDATE IEAI_COMPARE_CLASS SET ICLASSTYPE=0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			COMMIT;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTARTTIMECRON' AND TABNAME='IEAI_COMPARE_PRO_AUTO_START';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPARE_PRO_AUTO_START ADD COLUMN ISTARTTIMECRON VARCHAR(50)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COMPARE_SWITCH';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_COMPARE_SWITCH(IID DECIMAL(19)  NOT NULL,IPROGRAMMEID DECIMAL(19),ITYPE NUMERIC(1),IPROID DECIMAL(19),ISWITCHID DECIMAL(19),ICHECKNAME VARCHAR(255),CONSTRAINT PK_IEAI_COMPARE_SWITCH PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISWITCHSTATE' AND TABNAME='IEAI_COMPARE_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPARE_RESULT ADD COLUMN ISWITCHSTATE NUMERIC(1) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;				
	END
	$
	
	-- 4.7.20 version does not have patches for compare
	-- 4.7.21 version compare patch is as follows
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISDISABLED' AND TABNAME='IEAI_COMPARE_PROGRAMME_BASE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPARE_PROGRAMME_BASE ADD COLUMN IISDISABLED DECIMAL(1) DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_NODERESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_NODERESULT(IID DECIMAL(19) NOT NULL ,IRESULTID DECIMAL(19) ,ISYSNAME VARCHAR(255),ISOURCEIP VARCHAR(255) ,ITARGETIP VARCHAR(255) ,ISOURCENAME VARCHAR(255) ,ITARGETNAME VARCHAR(255) ,IRESULT DECIMAL(1) ,ISTATE DECIMAL(1) ,ISOURCEPORT DECIMAL(19),ITARGETPORT DECIMAL(19),CONSTRAINT PK_IEAI_SYNC_NODERESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_RESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_RESULT(IID DECIMAL(19) NOT NULL ,IPLANID DECIMAL(19) ,ISTATE DECIMAL(1) ,IRESULT DECIMAL(1) ,IRUNTIME DECIMAL(19) ,CONSTRAINT PK_IEAI_SYNC_SYNCRESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_DIRRESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_DIRRESULT(IID DECIMAL(19) NOT NULL ,INODERESULTID DECIMAL(19) ,ITYPE DECIMAL(1) ,IPATH VARCHAR(255) ,INATCHSYNC DECIMAL(1) ,IALLSYNC DECIMAL(1) ,ISYNCSTRATEGY VARCHAR(255) ,IRESULTSTR CLOB ,IRESULT DECIMAL(1) ,ISTATE DECIMAL(1) ,IFLOWID DECIMAL(19) ,ISENCRYPTION DECIMAL(1) ,CONSTRAINT PK_IEAI_SYNC_DIRRESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_STRATEGYPLAN';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_STRATEGYPLAN(IID DECIMAL(19) NOT NULL ,IPLANNAME VARCHAR(255) ,IPLANDESC VARCHAR(255) ,ISOURCEID DECIMAL(19) ,ITARGETID DECIMAL(19) ,ICREATETIME DECIMAL(19) ,ICREATER VARCHAR(255) ,CONSTRAINT PK_IEAI_SYNC_STRATEGYPLAN PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_PLANVIEW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_PLANVIEW(IID DECIMAL(19) NOT NULL ,IPLANID DECIMAL(19) ,ISYSID DECIMAL(19) ,CONSTRAINT PK_IEAI_SYNC_PLANVIEW PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_CRONTASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_CRONTASK(IID DECIMAL(19) NOT NULL ,IPLANID DECIMAL(19) ,ICRON VARCHAR(255) ,ISTATUS DECIMAL(1) ,CONSTRAINT PK_IEAI_SYNC_CRONTASK PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_AGENTRELATION';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_AGENTRELATION(IID DECIMAL(19) NOT NULL ,ISYSID DECIMAL(19) ,ISOURCEAGENTID DECIMAL(19) ,ITARGETAGENTID DECIMAL(19) ,ISOURCEID DECIMAL(19) ,ITARGETID DECIMAL(19) ,CONSTRAINT PK_IEAI_SYNC_AGENTRELATION PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_SYSTEMAGENT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_SYSTEMAGENT(IID DECIMAL(19) NOT NULL ,ISYSID DECIMAL(19) ,IAGENTID DECIMAL(19) ,CONSTRAINT PK_IEAI_SYNC_SYSTEMAGENT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_CATALOG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_CATALOG(IID DECIMAL(19) NOT NULL ,IRELATIONID DECIMAL(19) ,ITYPE DECIMAL(1) ,ISTATUS DECIMAL(1) ,IPATH VARCHAR(255) ,INATCHSYNC DECIMAL(1) ,IALLSYNC DECIMAL(1) ,ISYNCSTRATEGY VARCHAR(255) ,ISENCRYPTION DECIMAL(1) ,CONSTRAINT PK_IEAI_SYNC_CATALOG PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDCID' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IDCID DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-13;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-13, 0, ''所有一致性比对业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 13, -13, -13, -13)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;

	END
	$
	
	-- 4.7.22 version does not have patches for compare
	-- 4.7.23 version compare patch is as follows
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPATHNAME' AND TABNAME='IEAI_SYNC_CATALOG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SYNC_CATALOG ADD COLUMN IPATHNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICREATER' AND TABNAME='IEAI_SYNC_CRONTASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SYNC_CRONTASK ADD COLUMN ICREATER VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICREATETIME' AND TABNAME='IEAI_SYNC_CRONTASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SYNC_CRONTASK ADD COLUMN ICREATETIME DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUSETIME' AND TABNAME='IEAI_SYNC_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SYNC_RESULT ADD COLUMN IUSETIME DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	END
	$
	
	-- 4.7.24 version compare patch is as follows
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSID' AND TABNAME='IEAI_COMPARE_PROGRAMME';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPARE_PROGRAMME ADD COLUMN ISYSID DECIMAL(19)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;

	END
	$
	
	-- 4.7.25 version does not have patches for compare
	-- 4.7.26 version does not have patches for compare
	-- 4.7.27 version compare patch is as follows
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_WARNSTATE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_WARNSTATE(IID DECIMAL(19) NOT NULL ,IRESULTID DECIMAL(19) ,ISYSNAME VARCHAR(255) ,ISTATE DECIMAL(1) ,CONSTRAINT PK_IEAI_SYNC_WARNSTATE PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG WHERE IID=28;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PARAMETER_CONFIG(IID,IPARAMETER,IPARAVALUE) VALUES(28,''socketRsyncScript'',''-s ##/rsync_startsocket.sh##'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_SOCKETFLOW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_SOCKETFLOW(IID DECIMAL(10) NOT NULL ,IAGENTID DECIMAL(19) ,IFLOWID DECIMAL(19) ,ISTATE DECIMAL(1) ,IRESULTSTR VARCHAR(255) ,IRESULT DECIMAL(1) ,CONSTRAINT PK_SYNC_SOCKETFLOW PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	COMMIT;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCOMPARE_CONFRESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCOMPARE_CONFRESULT(IID DECIMAL(19) NOT NULL ,IRESULTSYSID DECIMAL(19) ,ITYPE VARCHAR(255) ,IPATH VARCHAR(255) ,IALLSYNC DECIMAL(1) ,INATCHSYNC DECIMAL(1) ,IISPASS DECIMAL(1) ,IISPASSHTML CLOB ,IENCODE VARCHAR(255) ,ISYNCSTRATEGY VARCHAR(255) ,IFLOWID DECIMAL(19) ,CONSTRAINT PK_IEAI_SCOMPARE_CONFRESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCOMPARE_SYSRESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCOMPARE_SYSRESULT(IID DECIMAL(19) NOT NULL ,IRESULTID DECIMAL(19) ,ISYSNAME VARCHAR(255) ,ISOURCEIP VARCHAR(255) ,ITARGETIP VARCHAR(255) ,ISOURCEPORT DECIMAL(19) ,ITARGETPORT DECIMAL(19) ,ISOURCENAME VARCHAR(255) ,ITARGETNAME VARCHAR(255) ,IISPASS DECIMAL(1) ,CONSTRAINT PK_IEAI_SCOMPARE_SYSRESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCOMPARE_RESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCOMPARE_RESULT(IID DECIMAL(19) NOT NULL ,IRESULTNAME VARCHAR(255) ,IRESULTDES VARCHAR(255) ,ICOMPARETIME DECIMAL(19) ,IISPASS DECIMAL(1) ,IUSETIME DECIMAL(19) ,CONSTRAINT PK_IEAI_SCOMPARE_RESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMODEL' AND TABNAME='IEAI_SYNC_CATALOG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SYNC_CATALOG ADD COLUMN IMODEL DECIMAL(1) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IENCODE' AND TABNAME='IEAI_SYNC_CATALOG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SYNC_CATALOG ADD COLUMN IENCODE VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMODEL' AND TABNAME='IEAI_SYNC_STRATEGYPLAN';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SYNC_STRATEGYPLAN ADD COLUMN IMODEL DECIMAL(1) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCOMPARE_CRONTASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCOMPARE_CRONTASK(IID DECIMAL(19) NOT NULL ,IPLANID DECIMAL(19) ,ICRON VARCHAR(255) ,ISTATUS DECIMAL(1) ,ICREATER VARCHAR(255) ,ICREATETIME DECIMAL(19) ,CONSTRAINT PK_IEAI_SCOMPARE_CRONTASK PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	END
	$
	
	-- 4.7.27 update values
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM ieai_sync_catalog where  imodel is null;
	IF LI_EXISTS > 0 THEN
		SET LS_SQL = 'update ieai_sync_catalog set imodel= 1  where imodel is null';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM ieai_sync_strategyplan where  imodel is null;
	IF LI_EXISTS > 0 THEN
		SET LS_SQL = 'update ieai_sync_strategyplan set imodel= 1  where imodel is null';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	END
	$
	
	
		-- 8.1.0 update values
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COMPARE_RELATION';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_COMPARE_RELATION(IID DECIMAL(19) NOT NULL ,ISYSID DECIMAL(19) ,ICLASSID DECIMAL(19),CONSTRAINT PK_IEAI_COMPARE_RELATION PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
	END
	$
	
			-- 8.2.0 update values
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPATHNAME' AND TABNAME='IEAI_SCOMPARE_CONFRESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCOMPARE_CONFRESULT ADD COLUMN IPATHNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISOURCECOMNAME' AND TABNAME='IEAI_SCOMPARE_SYSRESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCOMPARE_SYSRESULT ADD COLUMN ISOURCECOMNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITARGETCOMNAME' AND TABNAME='IEAI_SCOMPARE_SYSRESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCOMPARE_SYSRESULT ADD COLUMN ITARGETCOMNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNC_CATELOGRELATION';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SYNC_CATELOGRELATION(IID DECIMAL(19) NOT NULL ,ICATELOGID DECIMAL(19) ,IPLANVIEWID DECIMAL(19) ,IPLANID DECIMAL(19) ,CONSTRAINT PK_IEAI_SYNC_CATELOGRELATION PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	END
	$