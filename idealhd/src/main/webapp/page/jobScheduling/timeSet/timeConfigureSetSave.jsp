<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/timeSet/timeConfigureSetSave.js"></script>
<script type="text/javascript">
 var optType = ""; 
 
 var filter_iid = '<%=request.getAttribute("UIID")==null?"":request.getAttribute("UIID")%>';
 var filter_PRJNAME;
 var filter_FLOWNAME;
 var filter_ACTIVITYNAME;
 var filter_TASKDEC;
 var filter_TIMESETMONTH;
 var filter_TIMESETDAY;
 var filter_TIMESETWEEK;
 var filter_TIMEOUTNUM;
 var filter_IAVGNUM;
 var filter_IPRJID;
 var filter_ITHRESHOLD;
 if(filter_iid != '' && filter_iid != null){
	 optType = "edite";
	 filter_PRJNAME = '<%=request.getAttribute("PRJNAME")==null?"":request.getAttribute("PRJNAME")%>';
	 filter_FLOWNAME = '<%=request.getAttribute("FLOWNAME")==null?"":request.getAttribute("FLOWNAME")%>';
	 filter_ACTIVITYNAME = '<%=request.getAttribute("ACTIVITYNAME")==null?"":request.getAttribute("ACTIVITYNAME")%>';
	 filter_TASKDEC = '<%=request.getAttribute("TASKDEC")==null?"":request.getAttribute("TASKDEC")%>';
	 filter_TIMESETMONTH = '<%=request.getAttribute("TIMESETMONTH")==null?"":request.getAttribute("TIMESETMONTH")%>';
	 filter_TIMESETDAY = '<%=request.getAttribute("TIMESETDAY")==null?"":request.getAttribute("TIMESETDAY")%>';
	 filter_TIMESETWEEK = '<%=request.getAttribute("TIMESETWEEK")==null?"":request.getAttribute("TIMESETWEEK")%>';
	 filter_TIMEOUTNUM = '<%=request.getAttribute("TIMEOUTNUM")==null?"":request.getAttribute("TIMEOUTNUM")%>';
	 filter_IAVGNUM = '<%=request.getAttribute("IAVGNUM")==null?"":request.getAttribute("IAVGNUM")%>';
	 filter_IPRJID = '<%=request.getAttribute("IPRJID")==null?"":request.getAttribute("IPRJID")%>';
	 filter_ITHRESHOLD = '<%=request.getAttribute("ITHRESHOLD")==null?"":request.getAttribute("ITHRESHOLD")%>';
 }else{
	 optType = "save";
 }
 
</script>
</head>
<body>
<div id="timeConfigSetConfigWindowDiv" style="width: 100%;height: 100%">
</div>
</body>
</html>