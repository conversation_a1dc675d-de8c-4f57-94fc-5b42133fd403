package com.ideal.ieai.commons.message;

import java.util.Date;
import java.io.Serializable;

public class DestinationInfo implements Serializable
{
    private String name;
    private int type;
    private boolean durable;

    private String clientId;
    private Date createTime;

    public static final int TOPIC = 0;
    public static final int QUEUE = 1;

    public boolean isDurable()
    {
        return durable;
    }

    public int getType()
    {
        return type;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public void setDurable(boolean durable)
    {
        this.durable = durable;
    }

    public void setType(int type)
    {
        this.type = type;
    }

    public void setClientId(String clientId)
    {
        this.clientId = clientId;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public String getName()
    {
        return name;
    }

    public String getClientId()
    {
        return clientId;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public DestinationInfo()
    {

    }

    public DestinationInfo(String name, int type, boolean durable, String clientId)
    {
        setName(name);
        setType(type);
        setDurable(durable);
        setClientId(clientId);
        setCreateTime(new Date());
    }
}
