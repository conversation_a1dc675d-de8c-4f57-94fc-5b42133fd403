package com.ideal.service.ic.hcstrategy.strategic;

import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.strategic.*;
import org.apache.log4j.Logger;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class StrategicService {
    public static final Logger log = Logger.getLogger(StrategicService.class);

    private StrategicService() {
    }

    private static StrategicService instance = null;

    public static StrategicService getInstance() {
        if (null == instance) {
            synchronized (StrategicService.class) {
                if (null == instance) {
                    instance = new StrategicService();
                }
            }
        }
        return instance;
    }

    /**
     * 巡检策略管理首页数据展示的查询
     *
     * @param iCenterId    中心id
     * @param iSystemId    系统ID
     * @param iName        策略名称
     * @param iStartStatus 启动状态（0未启动，1已启动）
     * @param start        分页
     * @param limit        分页大小
     * @return List<StrategicModel>
     * @throws RepositoryException e
     */
    public Map query(Long iCenterId, Long iSystemId, String iName, Integer iStartStatus, Integer start, Integer limit, Boolean flag, String templateName, String userId) throws RepositoryException {
        return StrategicManager.getInstance().query(iCenterId, iSystemId, iName, iStartStatus, start, limit, flag, templateName, userId);
    }


    /**
     * 首页策略管理下业务系统下拉框的查询
     *
     * @return List<StrategicModel>
     * @throws RepositoryException e
     */
    public List<StrategicModel> querySystemName() throws RepositoryException {
        return StrategicManager.getInstance().querySystemName();
    }


    /**
     * 查询 巡检时间维护 表
     *
     * @return
     * @throws RepositoryException
     */
    public List<StrategyTimeModel> queryTimeModel(Long iStrategyId) throws RepositoryException {
        return StrategicManager.getInstance().queryTimeModel(iStrategyId);
    }


    /**
     * 添加一个策略
     *
     * @param strategicModel model
     * @param userName       创建人
     */
    public Long insertStrategy(StrategicModel strategicModel, String userName, List<StrategyTimeModel> strategyTimeModels) throws RepositoryException {
        return StrategicManager.getInstance().insertStrategy(strategicModel, userName, strategyTimeModels);
    }

    /**
     * 巡检策略的批量删除
     *
     * @param strategicModels
     * @param userName
     */
    public void deleteStrategy(List<StrategicModel> strategicModels, String userName) throws RepositoryException {
        StrategicManager.getInstance().deleteStrategy(strategicModels, userName);
    }

    /**
     * 批量 添加策略时间维护表数据
     *
     * @param list     list
     * @param userName 创建人userName
     * @throws RepositoryException e
     */
    public void insertStrategyTime(List<StrategyTimeModel> list, String userName, Long iStrategyId) throws RepositoryException {
        StrategicManager.getInstance().insertStrategyTime(list, userName, iStrategyId);
    }


    /**
     * 批量删除 策略时间维护表数据
     *
     * @param deleteIds
     */
    public void deleteStrategyTime(String[] deleteIds) throws RepositoryException {
        StrategicManager.getInstance().deleteStrategyTime(deleteIds);
    }


    /**
     * 巡检管理下查询设备
     *
     * @param iStrategyId   巡检策略id
     * @param equipmentName 设备名称
     * @param iStartStatus  启动状态
     * @param start         分页
     * @param limit         分页
     * @return map
     */
    public Map queryEquipmentByStrategyId(Long iStrategyId, String equipmentName, Integer iStartStatus, Integer start, Integer limit) throws RepositoryException {
        return StrategicManager.getInstance().queryEquipmentByStrategyId(iStrategyId, equipmentName, iStartStatus, start, limit);
    }


    /**
     * 通过主键id查询策略维护
     *
     * @param iStrategyId
     * @param userName
     * @return
     * @throws RepositoryException
     */
    public StrategicModel queryByiStrategyId(Long iStrategyId, String userName) throws RepositoryException {
        return StrategicManager.getInstance().queryByiStrategyId(iStrategyId, userName);
    }

    /**
     * 修改策略维护
     *
     * @param model
     * @param userName
     * @param strategyTimeModels
     * @throws RepositoryException
     */
    public Map updateStrategy(StrategicModel model, String userName, List<StrategyTimeModel> strategyTimeModels) throws RepositoryException {
        return StrategicManager.getInstance().updateStrategy(model, userName, strategyTimeModels);
    }

    /**
     * @return
     * @throws RepositoryException
     */
    public Set<StrategySystemModel> queryUnStrategy(Long iCenterId, Long iSystemId, String userId) throws RepositoryException {
        return StrategicManager.getInstance().queryUnStrategy(iCenterId, iSystemId, userId);
    }

    /**
     * 巡检策略批量启动
     *
     * @param strategyIds     策略ids
     * @param userName        执行人id
     * @param executeStrategy 是否立即执行
     */
    public Map start(List<Long> strategyIds, String userName, Boolean executeStrategy, Integer timeoutNum) throws RepositoryException {
        return StrategicManager.getInstance().start(strategyIds, userName, executeStrategy, null, null, timeoutNum);
    }

    public Map startCPID(Long cpId, String userName) throws RepositoryException {
        return StrategicManager.getInstance().startCPID(cpId, userName);
    }

    /**
     * 巡检策略批量停止
     *
     * @param strategyIds
     * @param userName
     * @throws RepositoryException
     */
    public Map<String, Object> stop(List<Long> strategyIds, String userName) throws RepositoryException {
        return StrategicManager.getInstance().stop(strategyIds, userName);
    }

    /**
     * @param ICPID
     * @throws RepositoryException
     */
    public EquipmentModel queryEquipment(Long ICPID) throws RepositoryException {
        return StrategicManager.getInstance().queryEquipment(ICPID);
    }
}
