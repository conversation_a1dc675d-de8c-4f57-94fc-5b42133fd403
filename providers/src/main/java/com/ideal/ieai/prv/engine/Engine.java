package com.ideal.ieai.prv.engine;

import EDU.oswego.cs.dl.util.concurrent.PooledExecutor;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.prv.CheckDBPoolLog;
import com.ideal.ieai.prv.provider.supperhcresult.SupperProvHcCheckResultQueueHandlerThread;
import com.ideal.ieai.prv.thread.AgentCheckStateThread;
import com.ideal.ieai.prv.thread.ScriptInstanceTimeoutThread;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import org.apache.log4j.Logger;

/**
 * <li>Title: Engine.java</li> <li>Project: server</li> <li>Package: com.ideal.ieai.server.engine</li>
 * <li>Description: The class is facade of Engine. <li> <li>Copyright: Copyright (c) 2006</li> <li>
 * Company: IdealTechnologies</li> <li>Created on 06-Jun-2006, 08:06:02</li>
 * 
 * <AUTHOR> liancheng_gu
 * @version iEAI v3.5
 */
public class Engine
{
    private static final Logger   log                   = Logger.getLogger(Engine.class);

    private static Engine         inst                  = null;

    private AgentCheckStateThread agentCheckStateThread = null;
    private ScriptInstanceTimeoutThread scriptInstanceTimeoutThread   = null;


    private Thread checkDBPoolLog;

    private PooledExecutor        threadPoolLimited     = null;

    private boolean               isDBConnect           = false;

    /**
     * mkj remove jgroup
     * 
     * @throws ServerException
     */
    private Engine()
    {
        threadPoolLimited = new PooledExecutor();
        threadPoolLimited.setMaximumPoolSize(ServerEnv.getServerEnv().getSchedulerThreadNum());
        threadPoolLimited.abortWhenBlocked();
        log.info("ThreadPool initialization succeeded!");
        agentCheckStateThread = new AgentCheckStateThread();
        scriptInstanceTimeoutThread = new ScriptInstanceTimeoutThread();
        checkDBPoolLog = new CheckDBPoolLog();
    }

    /**
     * The method is used to initialize Engine.
     * 
     */
    public static synchronized void initEngine ()
    {
        Engine.inst = new Engine();
        Engine.checkThreadStart(Engine.inst.agentCheckStateThread, Environment.AGENTCHECK_POLL_FLAG,
                Environment.FALSE);
        //脚本服务化任务超时轮询线程
        Engine.checkThreadStart(Engine.inst.scriptInstanceTimeoutThread, Environment.SCRIPT_INSTANCE_TIMEOUT_SWITCH,
                Environment.FALSE);
        //数据库连接池监控线程
        Engine.checkThreadStart( Engine.inst.checkDBPoolLog, Environment.ISCHECKDBPOOLLOG, Environment.TRUE);
        log.info("agentCheckStateThread initialization succeeded!");
        
        try {
            boolean isStartQuen = Environment.getInstance().getHcSupperCheckResultPrevCacheQueueSwitch();
            
            
            if(isStartQuen) {
                int threadNum = 5;
                try {
                    String threadNumStr = Environment.getInstance().getSysConfig(Environment.HC_SUPPER_CHECKRESULT_PROVIDER_CACHE_QUEUE_THREAD_NUM,"5");
                    threadNum = Integer.parseInt(threadNumStr);
                }catch (Exception e) {
                    threadNum = 5;
                }
                log.info("SupperProvHcCheckResultQueueHandlerThread initialization num "+threadNum+"!");
                for(int i=0;i<threadNum;i++) {
                    SupperProvHcCheckResultQueueHandlerThread checkResultQueueHandlerThread = new SupperProvHcCheckResultQueueHandlerThread();
                    checkResultQueueHandlerThread.start();
                }
            }
            
        }catch (Exception e) {
            log.error("初始化队列巡检结果处理失败", e);
        }
    }

    /**
     * Get Engine instance.
     * 
     * @return
     * @throws ServerException 
     */
    public static Engine getInstance ()
    {
        if (inst == null)
        {
            inst = new Engine();
        }
        return inst;
    }

    /**
     * get a thread pool that can be configed.
     * 
     * @return
     */
    public PooledExecutor getThreadPoolLimited ()
    {
        return threadPoolLimited;
    }

    public boolean isDBConnect ()
    {
        return isDBConnect;
    }

    /**
     * 
     * @Title: checkThreadStart
     * @Description: 判断线程是否启动
     * @param inThread 线程
     * @param inname 线程开关名
     * @param defaultBoolean 默认开关布尔值字符串
     * @return void 返回类型
     * @throws @变更记录 2016年7月14日 yunpeng_zhang
     */
    public static void checkThreadStart ( Thread inThread, String inname, String defaultBoolean )
    {
        boolean stratFlag = false;
        try
        {
            stratFlag = Boolean.parseBoolean(Environment.getInstance().getSysConfig(inname, defaultBoolean));
        } catch (Exception e)
        {
            stratFlag = false;
            log.error(inname + " flag error!");
        }
        if (stratFlag)
        {
            inThread.start();
        }
    }

}
