Ext.onReady(function(){

	Ext.define('serverCPValueData', {
        extend: 'Ext.data.Model',
        fields: fieldsData
    });
	
	serverCPValue_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'serverCPValueData',
        proxy: {
            type: 'ajax',
            url: 'queryCPValue.do?cpid='+cpid,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	
    var serverCPValue_columns = columnsData;
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	})
    var serverCPValue_grid = Ext.create('Ext.grid.Panel', {
	    store:serverCPValue_store,
	    border:true,
	    height: contentPanel.getHeight ()-190,
	    columnLines : true,
	    columns:serverCPValue_columns,
 	    plugins : cellEditing,
	    listeners: {
	    	 beforeedit: function (editor, e, eOpts) {
	    		 if(e.field ==='comparerule')
    			{
    				return false;
    			}
	    		 if(e.field !='I1' ){
	    			 e.column.setEditor({
	    				 xtype : 'textareafield',
	    				 fieldCls : 'scrollStyle',
	    				 height : 90
                     });
	    		 }
	    	 }
	    }
	});
//	 serverCPValueWindow.on('resize',function(){
//		 
//		 serverCPValue_grid.setWidth(serverCPValueWindow.getWidth()-23);
//		 serverCPValue_grid.setHeight(serverCPValueWindow.getHeight()-43);
//	    });
    parent.Ext.getCmp('server_serverCPValueWindow').on('resize',function(){
		 
		 serverCPValue_grid.setWidth(parent.Ext.getCmp('server_serverCPValueWindow').getWidth());
		 serverCPValue_grid.setHeight(parent.Ext.getCmp('server_serverCPValueWindow').getHeight()-90);
	    });
    
    var mainPanel = Ext.create('Ext.panel.Panel',{
        renderTo : "serverCPValueMonitor_area",
        border : false,
        bodyPadding : 5,
        items : [serverCPValue_grid]
    });
    
    
//    serverCPValueWindow.on('beforedestroy',function(){
//    	Ext.destroy(mainPanel);
//		if(Ext.isIE){
//        	CollectGarbage(); 
//    	}
//    })

});
