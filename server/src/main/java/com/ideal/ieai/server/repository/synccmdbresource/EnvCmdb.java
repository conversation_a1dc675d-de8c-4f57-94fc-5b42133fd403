package com.ideal.ieai.server.repository.synccmdbresource;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @ClassName:  EnvCmdb   
 * @Description:环境对象-解析cmdb接口数据     
 * @author: yunpeng_zhang 
 * @date:   2019年6月12日 上午9:59:15   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
public class EnvCmdb
{
    private long               envId;                                           // 环境id
    private String             envName;                                         // 环境名
    private List<ResourceCmdb> resourceCmdbList = new ArrayList<ResourceCmdb>(); // 资源组集合

    public long getEnvId ()
    {
        return envId;
    }

    public void setEnvId ( long envId )
    {
        this.envId = envId;
    }

    public String getEnvName ()
    {
        return envName;
    }

    public void setEnvName ( String envName )
    {
        this.envName = envName;
    }

    public List<ResourceCmdb> getResourceCmdbList ()
    {
        return resourceCmdbList;
    }

    public void setResourceCmdbList ( List<ResourceCmdb> resourceCmdbList )
    {
        this.resourceCmdbList = resourceCmdbList;
    }
}
