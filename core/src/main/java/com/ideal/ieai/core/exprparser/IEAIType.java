/* Generated By:JJTree: Do not edit this line. IEAIType.java */

package com.ideal.ieai.core.exprparser;

public class IEAIType
    extends SimpleNode
{
    /**
     baseType is used during evaluation of full type and retained for the
     case where we are an array type.
     In the case where we are not an array this will be the same as type.
     */
    private Class baseType;
    /**
     If we are an array type this will be non zero and indicate the
     dimensionality of the array.  e.g. 2 for String[][];
     */
    private int arrayDims;



    public IEAIType(int id)
    {
        super(id);
    }

    public IEAIType(IEAIExprParser p, int id)
    {
        super(p, id);
    }

    /** Accept the visitor. **/
    public Object jjtAccept(IEAIExprParserVisitor visitor, Object data)
    {
        return visitor.visit(this, data);
    }

    /**
     Used by the grammar to indicate dimensions of array types
     during parsing.
     */
    public void addArrayDimension()
    {
        arrayDims++;
    }

    public int getArrayDims()
    {
        return arrayDims;
    }

    public Class getBaseType()
    {
        return baseType;
    }

}
