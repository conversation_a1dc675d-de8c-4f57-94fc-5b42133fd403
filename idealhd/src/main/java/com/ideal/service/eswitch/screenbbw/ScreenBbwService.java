package com.ideal.service.eswitch.screenbbw;

import com.ideal.controller.resourcemanage.exception.ServiceException;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.eswitch.common.EswitchException;
import com.ideal.ieai.server.eswitch.repository.screenbbw.ParamBean;
import com.ideal.ieai.server.eswitch.repository.screenbbw.ScreenBbwManage;
import com.ideal.ieai.server.eswitch.repository.screenbbw.ScreenBbwParamBean;
import com.ideal.ieai.server.eswitch.repository.screenbbw.SwitchsegmentBbwModel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;

public class ScreenBbwService {
    private static final Logger _log                   = Logger.getLogger(ScreenBbwService.class);
    private static final String IEAIRUNINSTANCEHIS     = "IEAI_RUN_INSTANCE_HIS";
    private static final String IEAIRUNINFOINSTANCEHIS = "IEAI_RUNINFO_INSTANCE_HIS";
    private static final String WORKITEMID             = "iworkitemid";
    private static final String ISONVERSIONID = "ison_version_id";
    private static final String IVERSIONID    = "iversion_id";

    private static final String THREENAME = "threeName";
    private static final String SYSSTATE  = "sysstate";


    public Map getOneKeySwitchSelectedTask( int sysType) throws EswitchException{
        Connection conn = null;
        ScreenBbwManage fcm = new ScreenBbwManage();
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String itaskids = fcm.getOneKeySwitchSelectedTask(conn);
            if(itaskids==null||"".equals(itaskids)||" ".equals(itaskids)||"null".equals(itaskids)){
                itaskids = "-100";
            }
            String iparavalue = fcm.getTaskByOneKeySwitchSelected(itaskids, conn);
            String switchName=fcm.getTaskByOneKeySwitchName(itaskids, conn);
            map.put("iswitchname", switchName);
            if(iparavalue!=null&&!"".equals(iparavalue)){
                String[] iparavalues = iparavalue.split(",");
                map.put("itaskid", iparavalues);
                map.put("itaskids", iparavalue);
            }
        } catch (Exception e)
        {
            _log.error("ScreenBbwService.getOneKeySwitchSelectedTask is error", e);
            throw new EswitchException(e);
        } finally
        {
            DBResource.closeConnection(conn, "getOneKeySwitchSelectedTask", _log);
        }
        return map;
    }


    public Map getRunningProLogicList ( int sysType ) throws ServiceException
    {
        Connection conn = null;
        Map res = new HashMap();
        List list = new ArrayList();
        ScreenBbwManage cs = new ScreenBbwManage();
        try
        {
            conn = DBResource.getConnection("getRunningProLogicList", _log, sysType);
            list = cs.getRunningProLogicList(conn);
        } catch (Exception e)
        {
            _log.error(e.getMessage());
            throw new ServiceException(e);
        } finally
        {
            DBResource.closeConnection(conn, "getRunningProLogicList", _log);
        }
        res.put("list", list);
        return res;
    }

    public Map getBbwBankInfoList (String mainStepInfo, int sysType ) throws EswitchException
    {
        Map map = new HashMap();
        ScreenBbwManage cs = new ScreenBbwManage();
        Connection con = null;

        try
        {
            con = DBManager.getInstance().getJdbcConnection(sysType);
            //获取正在运行的系统数量
            int runNum = cs.getRunNum(sysType, con);
            String runInstanceTable = IEAIRUNINSTANCEHIS;
            String runInfoInstacenTable = IEAIRUNINFOINSTANCEHIS;
            if (runNum > 0)
            {
                runInstanceTable = "IEAI_RUN_INSTANCE";
                runInfoInstacenTable = "IEAI_RUNINFO_INSTANCE";
            }
            //获取最近一次的切换原因
            String runinsname = cs.getNewSwitch(runInstanceTable, con, sysType);
            //获取中心信息
            List listCenter = cs.getCenterMessage(con);
            ParamBean bean = new ParamBean();
            bean.setRunInstanceTable(runInstanceTable);
            bean.setRunInfoInstacenTable(runInfoInstacenTable);
            bean.setRuninsname(runinsname);
            bean.setSysType(sysType);
            //查询不同中心的设备总数
            getSystemListPartOne(bean, listCenter, runNum, con);
            //获取系统的切换详情
            List listSystem = cs.getSystemList(mainStepInfo,runInstanceTable, runInfoInstacenTable,runinsname, con, sysType);
            getSystemListPartThree(mainStepInfo,runNum, runinsname, listSystem, con, sysType);
            //获取系统预计rto
//            getSystemyrto(listSystem,con);
            List listBack = new ArrayList();
            List listWorkitemid = getWorkitemidOrder(listSystem);
            if (listWorkitemid != null && !listWorkitemid.isEmpty())
            {
                for (int i = 0; i < listWorkitemid.size(); i++)
                {
                    Long iworkitemid = (Long) listWorkitemid.get(i);
                    orderSwitchMonitorSys(listBack, iworkitemid, listSystem, sysType);
                }
            }
            // 获取预计时间
            for (int i = 0; i < listBack.size(); i++)
            {
                if(((Map)listBack.get(i)).get(WORKITEMID)!=null){
                    Long iworkitemid = (Long)(((Map)listBack.get(i)).get(WORKITEMID));
                    getExpectTime(iworkitemid,(Map)listBack.get(i),con);
                }
            }
            odrbyList(listBack,map);
            // 切换步骤信息
            long workitemid = (long) Collections.max(listWorkitemid);

            Map resMap = new HashMap();
            List listSys = new ArrayList();
            try{
                List listSwitchsegment = getSwitchsegment(con, workitemid, listSystem, resMap, listSys);
                map.put("listSwitchsegment", listSwitchsegment);
            }catch (Exception e){
                _log.error("获取步骤列表失败", e);
            }


            map.put("center", listCenter);
            map.put("system", listBack);

        } catch (Exception e)
        {
            _log.error("getBbwBankInfoList is error", e);
            throw new EswitchException(e);
        }finally {
            DBResource.closeConnection(con, "getBbwBankInfoList", _log);
        }
        return map;
    }




    public List getSwitchsegment ( Connection conn, long workitemid, List listSystem, Map mapTop, List listSys )
            throws RepositoryException
    {
        boolean lastFlag = false;
        ScreenBbwManage screenManage = new ScreenBbwManage();
        List listSwitchsegmentOne = screenManage.getSwitchsegmentList(conn, 0, workitemid);
        if (listSwitchsegmentOne == null || listSwitchsegmentOne.isEmpty())
        {
            return listSwitchsegmentOne;
        }
        for (int i = 0; i < listSwitchsegmentOne.size(); i++)
        {
            ScreenBbwParamBean beanOne = new ScreenBbwParamBean();
            beanOne.setFinishNum(0);
            beanOne.setTotalNum(0);
            beanOne.setZbs(0);
            beanOne.setWcbs(0);

            SwitchsegmentBbwModel modelOne = (SwitchsegmentBbwModel) listSwitchsegmentOne.get(i);
            List listSwitchsegmentTwo = screenManage.getSwitchsegmentList(conn, modelOne.getIid(), workitemid);
            for (int j = 0; j < listSwitchsegmentTwo.size(); j++)
            {
                SwitchsegmentBbwModel modelTwo = (SwitchsegmentBbwModel) listSwitchsegmentTwo.get(j);
                ScreenBbwParamBean beanTwo = new ScreenBbwParamBean();
                beanTwo.setStarttime(0l);
                beanTwo.setEndtime(0l);
                beanTwo.setFinishedTwo(0);
                beanTwo.setRunningTwo(0);
                beanTwo.setNoBeginTwo(0);
                beanTwo.setFailedTwo(0);

                List listSwitchsegmentThree = screenManage.getSwitchsegmentList(conn, modelTwo.getIid(), workitemid);

                if ((i == listSwitchsegmentOne.size() - 1) && (j == listSwitchsegmentTwo.size() - 1)
                        && "".equals((String) mapTop.get(THREENAME)))
                {
                    lastFlag = true;
                }
                getSwitchsegmentPartThree(listSwitchsegmentThree, screenManage, conn, listSystem, beanOne, beanTwo,
                        mapTop, listSys, lastFlag);
                getSwitchsegmentPartTwo(modelTwo, beanTwo);
                // modelTwo.setSwitchsegmentTwoList(listSwitchsegmentThree);
                modelTwo.setIprogress(
                        beanTwo.getTotalNum() == 0 ? 0 : (beanTwo.getFinishNum() * 100 / beanTwo.getTotalNum()));
                modelTwo.setIprogressstep(beanTwo.getZbs() == 0 ? 0 : (beanTwo.getWcbs() * 100 / beanTwo.getZbs()));
            }
            modelOne.setSwitchsegmentTwoList(listSwitchsegmentTwo);
            modelOne.setIprogress(
                    beanOne.getTotalNum() == 0 ? 0 : (beanOne.getFinishNum() * 100 / beanOne.getTotalNum()));
            modelOne.setIprogressstep(beanOne.getZbs() == 0 ? 0 : (beanOne.getWcbs() * 100 / beanOne.getZbs()));
        }
        return listSwitchsegmentOne;
    }

    public void getSwitchsegmentPartThree ( List listSwitchsegmentThree, ScreenBbwManage screenManage,
                                            Connection conn, List listSystem, ScreenBbwParamBean beanOne, ScreenBbwParamBean beanTwo, Map mapTop,
                                            List listSys, boolean lastFlag ) throws RepositoryException
    {
        boolean flagLoadhis = false;
        try
        {
            flagLoadhis = Environment.getInstance().getBooleanConfig("czccb.screen.loadhis", false);
        } catch (Exception e)
        {
            flagLoadhis = false;
        }
        for (int k = 0; k < listSwitchsegmentThree.size(); k++)
        {
            boolean flag = false;
            SwitchsegmentBbwModel modelThree = (SwitchsegmentBbwModel) listSwitchsegmentThree.get(k);
            ScreenBbwParamBean beanThree = new ScreenBbwParamBean();

            beanThree.setFinishedThree(0);
            beanThree.setRunningThree(0);
            beanThree.setNoBeginThree(0);
            beanThree.setFailedThree(0);

            List listSwitchsegmentSys = screenManage.getSwitchsegmentSysList(conn, modelThree.getIid());
            // modelThree.setSwitchsegmentTwoList(listSwitchsegmentSys);
            getSwitchsegmentPartSys(listSwitchsegmentSys, listSystem, beanOne, beanTwo, beanThree);
            if (mapTop.get(THREENAME) != null && !"".equals((String) mapTop.get(THREENAME)))
            {
                continue;
            }
            if (beanThree.getFinishedThree() == 0 && beanThree.getRunningThree() == 0
                    /* && beanThree.getNoBeginThree() == 0 */ && beanThree.getFailedThree() == 0)
            {
                mapTop.put(THREENAME, "");
            } else if (beanThree.getFailedThree() > 0)
            {
                flag = true;
                mapTop.put(THREENAME, modelThree.getIname());
            } else if (beanThree.getRunningThree() > 0)
            {
                flag = true;
                mapTop.put(THREENAME, modelThree.getIname());
            } else if (beanThree.getFinishedThree() > 0 && beanThree.getNoBeginThree() > 0)
            {
                flag = true;
                mapTop.put(THREENAME, modelThree.getIname());
            } else
            {
                mapTop.put(THREENAME, "");
            }

//            if (beanThree.getFinishedThree() > 0 && beanThree.getNoBeginThree() == 0)
//            {
//                flag = true;
//                mapTop.put(THREENAME, modelThree.getIname());
//            } else if (beanThree.getFinishedThree() == 0 && beanThree.getNoBeginThree() > 0)

            if (lastFlag && "".equals((String) mapTop.get(THREENAME)))
            {
                flag = true;
                mapTop.put(THREENAME, modelThree.getIname());
            }

            if (flag || flagLoadhis)
            {
                getListSys(listSys, listSwitchsegmentSys, listSystem);
            }
        }
    }
    public void getListSys ( List listSys, List listSwitchsegmentSys, List listSystem )
    {
        listSys.clear();
        for (int n = 0; n < listSwitchsegmentSys.size(); n++)
        {
            SwitchsegmentBbwModel modelSys = (SwitchsegmentBbwModel) listSwitchsegmentSys.get(n);
            Map mapSys = getSysMap(modelSys, listSystem);
            if (mapSys != null)
            {
                listSys.add(mapSys);
            }

        }
    }

    public Map getSysMap ( SwitchsegmentBbwModel modelSys, List listSystem )
    {
        if (listSystem != null && !listSystem.isEmpty())
        {
            for (int i = 0; i < listSystem.size(); i++)
            {
                Map map = (Map) listSystem.get(i);
                if (modelSys.getIsysname().equals((String) map.get("isysname"))
                        && modelSys.getIswitchto().equals((String) map.get("iswitchto")))
                {
                    map.put("itiming", modelSys.getItiming());
                    return map;
                }
            }
        }
        return null;
    }

    public void getSwitchsegmentPartSys ( List listSwitchsegmentSys, List listSystem, ScreenBbwParamBean beanOne,
                                          ScreenBbwParamBean beanTwo, ScreenBbwParamBean beanThree )
    {
        for (int n = 0; n < listSwitchsegmentSys.size(); n++)
        {
            SwitchsegmentBbwModel modelSys = (SwitchsegmentBbwModel) listSwitchsegmentSys.get(n);
            Map mapSys = getSysMap(modelSys, listSystem);
            if (mapSys == null)
            {
                continue;
            }
            String sysstate = (String) mapSys.get(SYSSTATE);

            long estimatedstarttimeLong = getTimeStamp((String)mapSys.get("estimatedstarttime"));
            long estimatedendtimeLong = getTimeStamp((String)mapSys.get("estimatedendtime"));
            beanOne.setTotalNum(beanOne.getTotalNum() + 1);
            beanOne.setZbs(beanOne.getZbs() + Integer.parseInt((String) mapSys.get("sysstepnum")));
            beanOne.setWcbs(beanOne.getWcbs() + Integer.parseInt((String) mapSys.get("sysfinishednum")));

            beanTwo.setTotalNum(beanTwo.getTotalNum() + 1);
            beanTwo.setZbs(beanTwo.getZbs() + Integer.parseInt((String) mapSys.get("sysstepnum")));
            beanTwo.setWcbs(beanTwo.getWcbs() + Integer.parseInt((String) mapSys.get("sysfinishednum")));

            if ("已完成".equals(sysstate))
            {
                beanOne.setFinishNum(beanOne.getFinishNum() + 1);
                beanTwo.setFinishedTwo(beanTwo.getFinishedTwo() + 1);
                beanThree.setFinishedThree(beanThree.getFinishedThree() + 1);
            }
            if ("运行中".equals(sysstate))
            {
                beanTwo.setRunningTwo(beanTwo.getRunningTwo() + 1);
                beanThree.setRunningThree(beanThree.getRunningThree() + 1);
            }
            if ("未开始".equals(sysstate))
            {
                beanTwo.setNoBeginTwo(beanTwo.getNoBeginTwo() + 1);
                beanThree.setNoBeginThree(beanThree.getNoBeginThree() + 1);
            }
            if ("异常".equals(sysstate))
            {
                beanTwo.setFailedTwo(beanTwo.getFailedTwo() + 1);
                beanThree.setFailedThree(beanThree.getFailedThree() + 1);
            }
            getBeanTwoTime(estimatedstarttimeLong, estimatedendtimeLong, beanTwo);
        }
    }
    public long getTimeStamp(String strTime){
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(strTime);
            return date.getTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public void getBeanTwoTime ( long estimatedstarttimeLong, long estimatedendtimeLong, ScreenBbwParamBean beanTwo )
    {
        if (beanTwo.getStarttime() == 0
                || (beanTwo.getStarttime() != 0 && estimatedstarttimeLong < beanTwo.getStarttime()))
        {
            beanTwo.setStarttime(estimatedstarttimeLong);
        }
        if (estimatedendtimeLong > beanTwo.getEndtime())
        {
            beanTwo.setEndtime(estimatedendtimeLong);
        }
        if (beanTwo.getEndtime() < beanTwo.getStarttime())
        {
            beanTwo.setStarttime(beanTwo.getEndtime());
        }
    }

    public void getSwitchsegmentPartTwo ( SwitchsegmentBbwModel modelTwo, ScreenBbwParamBean beanTwo )
    {
        String isysstateTwo = "";
        if (beanTwo.getFinishedTwo() == 0 && beanTwo.getRunningTwo() == 0 && beanTwo.getNoBeginTwo() == 0
                && beanTwo.getFailedTwo() == 0)
        {
            isysstateTwo = "无";
        } else if (beanTwo.getFailedTwo() > 0)
        {
            isysstateTwo = "异常";
        } else if (beanTwo.getRunningTwo() > 0)
        {
            isysstateTwo = "运行中";
        } else if (beanTwo.getFinishedTwo() > 0 && beanTwo.getNoBeginTwo() > 0)
        {
            isysstateTwo = "运行中";
        } else if (beanTwo.getFinishedTwo() == 0 && beanTwo.getNoBeginTwo() > 0)
        {
            isysstateTwo = "未运行";
        } else if (beanTwo.getFinishedTwo() > 0 && beanTwo.getNoBeginTwo() == 0)
        {
            isysstateTwo = "已完成";
        }
        modelTwo.setIsysstate(isysstateTwo);
        modelTwo.setIruntime(beanTwo.getEndtime() - beanTwo.getStarttime());
    }


    public void getExpectTime ( long workItemId, Map reMap, Connection conn )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String getSql = "select icolheader,icolvalue from ieai_doublecheck_colvalue_his where (icolheader='iwindowStart' or icolheader='iwindowEnd') and iworkItemid=?";
        try
        {
            ps = conn.prepareStatement(getSql);
            ps.setLong(1, workItemId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                String name = rs.getString("icolheader");
                if ("iwindowStart".equals(name))
                {
                    reMap.put("expectStart", rs.getLong("icolvalue"));
                } else
                {
                    reMap.put("expectEnd", rs.getLong("icolvalue"));
                }
            }
        } catch (Exception e)
        {
            _log.error("获取预计时间发生异常");
            reMap.put("expectStart", 0);
            reMap.put("expectEnd", 0);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getExpectTime", _log);
        }
    }


    private void getSystemListPartOne ( ParamBean bean, List listCenter, int runNum,
                                        Connection con ) throws RepositoryException
    {
        ScreenBbwManage bbwManage = new ScreenBbwManage();
        if (listCenter == null || listCenter.isEmpty())
        {
            return;
        }
        String runInstanceTable = bean.getRunInstanceTable();
        String runInfoInstacenTable = bean.getRunInfoInstacenTable();
        String runinsname = bean.getRuninsname();
        int systype = bean.getSysType();

        for (int i = 0; i < listCenter.size(); i++)
        {
            Map mapCenter = (Map) listCenter.get(i);
            String centerident = (String) mapCenter.get("centerident");
            //获取中心下的系统
            List listSystemCenter = bbwManage.getCenterSystem(centerident, runInstanceTable,runInfoInstacenTable, runinsname, con, systype);
            getSystemListPartTwo(runNum, runinsname, listSystemCenter, con, centerident, systype);
            //查询agent管理，根据名称、数据中心查询设备数量
            int computerCount = 0;
            computerCount = bbwManage.getCenterComputerCount(centerident, runInstanceTable,
                    runInfoInstacenTable, runinsname, con, systype);
            if (runNum > 0)
            {
                int addCnt = bbwManage.getCenterComputerCount(centerident, IEAIRUNINSTANCEHIS,
                        IEAIRUNINFOINSTANCEHIS, runinsname, con, systype);
                if (addCnt > 0)
                {
                    computerCount = computerCount + addCnt;
                }
            }

            mapCenter.put("systemCenter", listSystemCenter);
            mapCenter.put("computerCount", computerCount);
        }
    }

    private void getSystemListPartTwo ( int runNum, String runinsname, List listSystemCenter, Connection con,
                                        String centerident, int systype ) throws RepositoryException
    {
        ScreenBbwManage bbwm = new ScreenBbwManage();
        if (runNum > 0)
        {
            List listAdd =  bbwm.getCenterSystem(centerident, IEAIRUNINSTANCEHIS, IEAIRUNINFOINSTANCEHIS, runinsname, con, systype);
            if (!listAdd.isEmpty())
            {
                for (int n = 0; n < listAdd.size(); n++)
                {
                    Map nmap = (Map) listAdd.get(n);
                    listSystemCenter.add(nmap);
                }
            }
        }
    }

    public void getSystemListPartThree (String mainStepInfo, int runNum, String runinsname, List listSystem, Connection con, int systype ) throws RepositoryException
    {
        ScreenBbwManage bbwm = new ScreenBbwManage();
        if (runNum > 0)
        {
            List listSystemAdd = bbwm.getSystemList(mainStepInfo,IEAIRUNINSTANCEHIS,
                    IEAIRUNINFOINSTANCEHIS, runinsname, con, systype);
            if (!listSystemAdd.isEmpty())
            {
                for (int m = 0; m < listSystemAdd.size(); m++)
                {
                    Map mmap = (Map) listSystemAdd.get(m);
                    listSystem.add(mmap);
                }
            }
        }
    }

    private void odrbyList ( List listBack,Map map)
    {
        List nowList=new ArrayList();
        List finiList = new ArrayList();
        List noList = new ArrayList();
        List ycList = new ArrayList();
        if(listBack!=null && !listBack.isEmpty()) {
            for(int i=0;i<listBack.size();i++) {
                Map maps = (Map) listBack.get(i);
                if("运行中".equals(maps.get("sysstate"))) {
                    nowList.add(maps);
                }
                if("已完成".equals(maps.get("sysstate"))) {
                    finiList.add(maps);
                }
                if("未开始".equals(maps.get("sysstate"))) {
                    noList.add(maps);
                }
                if("异常".equals(maps.get("sysstate"))) {
                    ycList.add(maps);
                }
            }
        }
        map.put("runningcount", nowList.size());
        nowList.addAll(finiList);
        nowList.addAll(noList);
        nowList.addAll(ycList);
        listBack.clear();
        listBack.addAll(nowList);
    }

    public Map getSwitchMonitorConnerBankScreen ( Long iruninsid, int sysType ) throws RepositoryException
    {
        Map map = new HashMap();
        List list = new ScreenBbwManage().getSwitchMonitorConnerScreen(iruninsid, sysType);
        map.put("connerStep", list);
        return map;
    }



    public List getSwitchStepBankScreen ( Long iruninsid, Long connerid,int sysType ) throws RepositoryException
    {
        return new ScreenBbwManage().getSwitchStepScreen(iruninsid, connerid, sysType);
    }

    public List getWorkitemidOrder ( List list )
    {
        List listWorkitemid = new ArrayList();
        if (!list.isEmpty())
        {
            for (int i = 0; i < list.size(); i++)
            {
                Map map = (Map) list.get(i);
                if (!listWorkitemid.contains((Long) map.get(WORKITEMID)))
                {
                    listWorkitemid.add((Long) map.get(WORKITEMID));
                }
            }
        }
        return listWorkitemid;
    }

    public void orderSwitchMonitorSys ( List listBack, Long iworkitemid, List listSource, int sysType )
            throws RepositoryException
    {
        ScreenBbwManage bbw = new ScreenBbwManage();
        List listInit = bbw.getInstanceRelation(iworkitemid, sysType);
        if (!listInit.isEmpty())
        {
            this.orderSwitchMonitorSysSon(listInit, listBack, iworkitemid, listSource);
        }

        for (int j = 0; j < listSource.size(); j++)
        {
            Map mapSource = (Map) listSource.get(j);
            if (iworkitemid.equals((Long) mapSource.get(WORKITEMID)) && checkList(listBack, mapSource))
            {
                listBack.add(mapSource);
            }
        }

    }

    public void orderSwitchMonitorSysSon ( List listInit, List listBack, Long iworkitemid, List listSource )
    {
        List listPass = new ArrayList();
        List list = new ArrayList();
        List listSon = new ArrayList();
        getFirst(listInit, list);
        while (true)
        {
            while (!list.isEmpty())
            {
                Long iversionid = (Long) list.remove(0);
                List listParent = new ArrayList();
                getParent(iversionid, listInit, listParent);
                if (isExsitAct(listParent, listPass))
                {
                    getSon(iversionid, listInit, listSon);
                    listPass.add(iversionid);
                }
            }
            for (int ii = 0; ii < listSon.size(); ii++)
            {
                Long isonversionid = (Long) listSon.get(ii);
                if (!list.contains(isonversionid))
                {
                    list.add(isonversionid);
                }
            }
            listSon.clear();
            if (list.isEmpty())
            {
                break;
            }
        }
        this.orderSwitchMonitorSysSonSplit(listPass, listBack, iworkitemid, listSource);
    }

    public void orderSwitchMonitorSysSonSplit ( List listPass, List listBack, Long iworkitemid, List listSource )
    {
        for (int i = 0; i < listPass.size(); i++)
        {
            Long iversionid = (Long) listPass.get(i);
            for (int j = 0; j < listSource.size(); j++)
            {
                Map mapSource = (Map) listSource.get(j);
                if (iworkitemid.equals((Long) mapSource.get(WORKITEMID))
                        && iversionid.equals((Long) mapSource.get("isysid")))
                {
                    listBack.add(mapSource);
                }
            }
        }
    }

    public boolean checkList ( List listBack, Map mapSource )
    {
        boolean flag = true;
        for (int i = 0; i < listBack.size(); i++)
        {
            Map mapBack = (Map) listBack.get(i);
            if (((Long) mapBack.get("iid")).equals((Long) mapSource.get("iid")))
            {
                flag = false;
            }
        }
        return flag;
    }

    public void getParent ( Long iid, List listInit, List listParent )
    {
        for (int i = 0; i < listInit.size(); i++)
        {
            Map map = (Map) listInit.get(i);
            if (iid.equals((Long) map.get(ISONVERSIONID)))
            {
                listParent.add((Long) map.get(IVERSIONID));
            }
        }
    }

    public boolean isExsitAct ( List listParent, List listPass )
    {
        boolean flag = true;
        if (!listParent.isEmpty())
        {
            for (int i = 0; i < listParent.size(); i++)
            {
                Long iid = (Long) listParent.get(i);
                if (!listPass.contains(iid))
                {
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }


    public void getFirst ( List listInit, List listD )
    {
        for (int i = 0; i < listInit.size(); i++)
        {
            boolean isFirst = true;
            Map map = (Map) listInit.get(i);
            for (int j = 0; j < listInit.size(); j++)
            {
                Map map1 = (Map) listInit.get(j);
                if (((Long) map.get(IVERSIONID)).equals((Long) map1.get(ISONVERSIONID)))
                {
                    isFirst = false;
                }
            }
            if (isFirst && !listD.contains((Long) map.get(IVERSIONID)))
            {
                listD.add((Long) map.get(IVERSIONID));
            }
        }
    }

    public void getSon ( Long iid, List listInit, List listSon )
    {
        List parent = new ArrayList();
        for (int i = 0; i < listInit.size(); i++)
        {
            Map map = (Map) listInit.get(i);
            if (iid.equals((Long) map.get(IVERSIONID)))
            {
                getParent((Long) map.get(ISONVERSIONID),listInit,parent);
                if(!CollectionUtils.containsAny(parent, listSon)) {
                    listSon.add((Long) map.get(ISONVERSIONID));
                }

            }
        }
    }









}
