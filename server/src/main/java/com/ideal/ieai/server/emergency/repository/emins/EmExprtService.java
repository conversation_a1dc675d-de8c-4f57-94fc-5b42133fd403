package com.ideal.ieai.server.emergency.repository.emins;

import java.util.List;
import java.util.Map;

import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.instanceConfig.InstanceConfigBeanForQuery;
import com.ideal.ieai.server.repository.importexecl.SUSExcelHostsOfEnv;
import com.ideal.ieai.server.repository.importexecl.export.SusExprtMgr;

/**
 * <ul>
 * <li>Title: SusExprtService.java</li>
 * <li>Description:自动部署，导出Service</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2016-4-22 上午8:00:40
 */
public class EmExprtService
{
    

    EmInstanceConfigBeanForQuery  instCfg =null;  //bean for query 
    EmsExprtMgr susExprtMgr = null;            //  mgr for query
    
    public EmExprtService(EmInstanceConfigBeanForQuery instCfg)
    {
        //init member
        if (null ==instCfg)
        {
            this.instCfg =new EmInstanceConfigBeanForQuery();
        }else{
            this.instCfg =instCfg;
        }
        susExprtMgr = new EmsExprtMgr(this);
    }
    
    
    
    //--------------------------------------
    /**
     * <li>Description:获得 “基础信息”,Sheet页数据</li> 
     * <AUTHOR>
     * 2016-4-22 上午8:05:53
     * @throws RepositoryException 
     */
    public Map<String, String> getBaisc () throws RepositoryException{
        Map<String, String> basicInfoMap =susExprtMgr.getBaisc();
        return basicInfoMap;
    }
            
    
    
    /**
     * <li>Description:获得 "环境信息" </li> 
     * <AUTHOR>
     * 2016-4-22 上午8:07:18
     * return void
     * @throws RepositoryException 
     */
    public List<SUSExcelHostsOfEnv> getHostsOfEnv () throws RepositoryException
    {
        List<SUSExcelHostsOfEnv> hostOfEnvs = susExprtMgr.getHostsOfEnv();
        return hostOfEnvs;
    }
    
    
    
    
    //------------------- by Last Version-------------------
    /**
     * <li>Description:获得"基础信息",根据最新的版本号</li> 
     * <AUTHOR>
     * 2016-4-22 上午8:16:42
     * return void
     * @throws RepositoryException 
     */
    public Map<String, String> getLastBaiscByVersn () throws RepositoryException
    {
        Map<String, String> lastBasicInfoMap = susExprtMgr.getLastBaiscByVersn();
        return lastBasicInfoMap;
    }

    
    /**
     * <li>Description:获得 "环境信息" ，根据最新的版本号 </li> 
     * <AUTHOR>
     * 2016-4-22 上午8:07:18
     * return void
     * @throws RepositoryException 
     */
    public List<SUSExcelHostsOfEnv> getLastHostsOfEnv () throws RepositoryException
    {
        List<SUSExcelHostsOfEnv> lastHostsOfEnvs = susExprtMgr.getLastHostsOfEnvBy();
        return lastHostsOfEnvs;
    }


    
    
    //------------get/set--------------
    
    
    public EmInstanceConfigBeanForQuery getInstCfg ()
    {
        return instCfg;
    }



    public void setInstCfg ( EmInstanceConfigBeanForQuery instCfg )
    {
        this.instCfg = instCfg;
    }



    public Map<String, String> getLastBaiscByVersnTwo (String iid) throws RepositoryException
    {
        Map<String, String> lastBasicInfoMap = susExprtMgr.getLastBaiscByVersnTwo(iid);
        return lastBasicInfoMap;
    }
    
}
