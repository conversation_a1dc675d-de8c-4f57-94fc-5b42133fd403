body{ 
	margin-bottom:0; 
	margin-top:0; 
	margin-left:0; 
	margin-right:0; 
	background-color:#1e2f4b;
	overflow:hidden;
	}
html,body{ height:100%;}
.body_bg{ 
	background-image:url(../images/body_bg.jpg); 
	background-position: center;
	height:100%; 
	width:100%;
	}
.hd_body_bg{ 
	width:1000px; 
	height:600px;
	margin:0 auto;
}
.hd_Login_Cn{ 
	margin:0 auto; 
	padding:170px 0 0 0;
	}
.hd_Logo{ 
	background-image:url(../images/hd_Logo.png); 
	width:1020px; 
	height:85px; 
	float:left;
	}
.login_title{
	font-size:22px; 
	font-family:"微软雅黑";
	color:#71ade1;
	display:block;
	padding:30px 0 35px 0;
}
.hd_Logozs{ 
	background-image:url(../images/hd_Logozs.png); 
	width:269px; 
	height:65px; 
	margin:auto
	}
.hd_Cn_bg{
	padding:170px 0 0 650px;
}
.hd_Content{ 
	/*background-image:url(../images/hd_Content.png); */
	width:305px; 
	height:271px;
	overflow:hidden;
	}
.hd_C_tab{width:335px;
	height:188px;
	margin:20px 0 0 440px;
	}
.User_Bg{
	background-image:url(../images/User_Bg.png); 
	width:285px;
	height:29px;
	background-position: 0px 0px;
	}
.Password_Bg{
	background-image:url(../images/User_Bg.png); 
	width:285px;
	height:29px;
	background-position: 0px -29px;
	margin:30px 0 0 0;
}

.Number_Bg{
	background-image:url(../images/User_Bg.png); 
	width:130px;
	height:29px;
	background-position: 0px -29px;
	margin:30px 0 0 5px;
}
/*
.hd_C_tab table tr{ height:78px;}
.hd_C_tab table tr td{ padding:0 0 0 10px;}
.hd_C_tab table tr td img{ padding:0 5px 0 0;}*/
.hd_text{ 
	width:252px; 
	height:29px; 
	border:0; 
	border-radius:2px; 
	color:#231815; 
	padding:0 0 0 5px;
	line-height:28px;
	background-color: #ffffff;
	margin:0 0 0 30px;
}
.hd_number{ 
	width:170px; 
	height:29px; 
	border:0; 
	border-radius:2px; 
	color:#231815; 
	padding:0 0 0 5px;
	line-height:28px;
	background-color: #ffffff;
	margin:0 0 0 30px;
}
.login_bt_tb tr .login_bt_td{
	padding:0 10px 0 0;
}
.hd_buttonL{ 
	/*background-image:url(../images/hd_bt1.png); */
	background-color: #1ca7d6;
	background-image: -webkit-linear-gradient(top, #30cbff 0%, #1ca7d6 100%);
	background-image: -moz-linear-gradient(top, #30cbff 0%, #1ca7d6 100%);
	background-image: -o-linear-gradient(top, #30cbff 0%, #1ca7d6 100%);
	background-image: linear-gradient(to bottom, #30cbff 0%, #1ca7d6 100%);
	border-radius: 2px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	width:100%;
	text-align:center;
	height:27px;
	border:0px solid #a8b3b9; 
	color:#fff; 
	font-family:"微软雅黑";
	cursor:pointer;
	font-size:12px;
	margin:25px 0 0 0;
	background-color: rgba(34,24,21,0);
	}
.hd_buttonL:hover{
	background-color: #1ca7d6;
	background-image: -webkit-linear-gradient(top, #1ca7d6 0%, #30cbff 100%);
	background-image: -moz-linear-gradient(top, #1ca7d6 0%, #30cbff 100%);
	background-image: -o-linear-gradient(top, #1ca7d6 0%, #30cbff 100%);
	background-image: linear-gradient(to bottom, #1ca7d6 0%, #30cbff 100%);
}
.hd_buttonRes{
	/*background-image:url(../images/hd_bt2.png);*/
	background-color: #f08300;
	background-image: -webkit-linear-gradient(top, #f7ab00 0%, #f08300 100%);
	background-image: -moz-linear-gradient(top, #f7ab00 0%, #f08300 100%);
	background-image: -o-linear-gradient(top, #f7ab00 0%, #f08300 100%);
	background-image: linear-gradient(to bottom, #f7ab00 0%, #f08300 100%);
	border-radius: 2px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	width:100%;
	text-align:center;
	height:27px;
	border:0px solid #a8b3b9; 
	color:#fff; 
	font-family:"微软雅黑";
	cursor:pointer;
	font-size:12px;
	margin:25px 0 0 0;
	background-color: rgba(34,24,21,0);
	}
	
.hd_number{
	/*background-image:url(../images/hd_bt2.png);*/
	background-color: #f08300;
	background-image: -webkit-linear-gradient(top, #f7ab00 0%, #f08300 100%);
	background-image: -moz-linear-gradient(top, #f7ab00 0%, #f08300 100%);
	background-image: -o-linear-gradient(top, #f7ab00 0%, #f08300 100%);
	background-image: linear-gradient(to bottom, #f7ab00 0%, #f08300 100%);
	border-radius: 2px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	width:100%;
	text-align:center;
	height:29px;
	border:0px solid #a8b3b9; 
	color:#fff; 
	font-family:"微软雅黑";
	cursor:pointer;
	font-size:12px;
	margin:25px 0 0 0;
	background-color: rgba(34,24,21,0);
	}
.hd_buttonRes:hover{
	background-color: #f7ab00;
	background-image: -webkit-linear-gradient(top, #f08300 0%, #f7ab00 100%);
	background-image: -moz-linear-gradient(top, #f08300 0%, #f7ab00 100%);
	background-image: -o-linear-gradient(top, #f08300 0%, #f7ab00 100%);
	background-image: linear-gradient(to bottom, #f08300 0%, #f7ab00 100%);
}
.login_User{ background-image:url(../images/login_user.png); width:20px; height:20px; float:left; margin:5px 5px 0 0}
.login_lock{background-image:url(../images/login_lock.png); width:20px; height:20px; float:left; margin:5px 5px 0 0}

.login_error{ font-size:12px; color:#fb4a4a;position:relative;top:15px;left:0; text-align:left; width:285px;}





.Change_body_bg{
	background-image:url(../images/Change_body_bg.jpg); 
	background-position: center;
	height:100%; 
	width:100%;
}
.uppassword{text-align:right; padding:8px 30px 0 0;}
.uppassword a{text-decoration:none;color:#0068b7; font-size:12px;font-family:"微软雅黑"}
.uppassword a:hover{ color:#231815;text-decoration:underline;}
.Uppassword_Bg{width:370px;height:370px;}
.Uppassword_Bg .Uppassword_Cn{padding: 60px 0 0 0;}
.Uppassword_Bg ul{padding:0;margin:0;font-family:"微软雅黑";list-style:none}
.Uppassword_Bg ul li{padding:0; margin:18px 0;width:291px;height:25px;}
.Uppassword_Bg ul li input{
	background-color: rgba(255,255,255,0.2);
	border:1px solid #9fa0a0;
	width:210px;
	line-height:25px; 
	color:#fff;
	height:25px;
	margin:0 0 0 10px;
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	padding:0 0 0 10px;
	}
.Upli_Span{float:left;line-height:34px;width:69px;font-size:12px;color:#dcdddd}
.Upli_Span2{float:left}
.change_Cn{padding:10px 0 0 40px;}
.Change_button01{
	background:#284459;
	background-image: -webkit-linear-gradient(top, #5ebcc5 0%, #284459 100%);
  	background-image: -moz-linear-gradient(top, #5ebcc5 0%, #284459 100%);
  	background-image: -o-linear-gradient(top, #5ebcc5 0%, #284459 100%);
  	background-image: linear-gradient(to bottom, #5ebcc5 0%, #284459 100%);
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	border:1px solid #15181b;
	box-shadow:0px 0px 1px #7dd9e1 inset;
	-webkit-box-shadow:0px 0px 1px #7dd9e1 inset;
	-moz-box-shadow:0px 0px 1px #7dd9e1 inset;
	-o-box-shadow:0px 0px 1px #7dd9e1 inset;
	width:127px;
	height:28px;
	line-height:28px; 
	text-align:center; 
	float:left;
	font-family:"微软雅黑";
	font-size:12px;
	color:#fff;
	cursor:pointer;
	}
.Change_button02{
	background:#8f2514;
	background-image: -webkit-linear-gradient(top, #f39105 0%, #8f2514 100%);
  	background-image: -moz-linear-gradient(top, #f39105 0%, #8f2514 100%);
  	background-image: -o-linear-gradient(top, #f39105 0%, #8f2514 100%);
  	background-image: linear-gradient(to bottom, #f39105 0%, #8f2514 100%);
	border-radius: 20px;
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	-o-border-radius: 20px;
	border:1px solid #15181b;
	box-shadow:0px 0px 1px #f9ad3e inset;
	-webkit-box-shadow:0px 0px 1px #f9ad3e inset;
	-moz-box-shadow:0px 0px 1px #f9ad3e inset;
	-o-box-shadow:0px 0px 1px #f9ad3e inset;
	width:129px;
	height:28px;
	line-height:28px; 
	text-align:center; 
	float:left;
	font-family:"微软雅黑";
	font-size:12px;
	color:#fff;
	cursor:pointer;
	margin:0 0 0 35px;}
.change_Result{color:#dcdddd;font-family:"微软雅黑";font-size:14px;}
.change_error{ font-size:12px; position:absolute; color:#595757; margin:5px 0 0 -140px;clear:both}
.Change_Password{padding:20px 0 0 0;}
.Change_Password a{font-size:12px; color:#54c3f1;text-decoration:underline;}

/*图片变换
#full-screen-slider { width:100%; height:100%; position:relative; clear:both}
#slides { display:block; width:100%; height:100%; list-style:none; padding:0; margin:0; position:relative}
#slides li { display:block; width:100%; height:100%; list-style:none; padding:0; margin:0; position:absolute}
#slides li a { display:block; width:100%; height:100%; text-indent:-9999px}
#pagination { display:block; list-style:none; position:absolute; left:45%; bottom:60px; z-index:9900;  padding:0 15px 0 0; margin:0}
#pagination li { display:block; list-style:none; width:30px; height:8px; float:left; margin-left:15px; background:#c9caca;}
#pagination li a { display:block; width:100%; height:100%; padding:0; margin:0;  text-indent:-9999px;}
#pagination li.current { background:#f6a600}
.body_bg1{background:url(../images/body_bg01.jpg) no-repeat center center;}
.body_bg2{background:url(../images/body_bg02.jpg) no-repeat center center;}
.body_bg3{background:url(../images/body_bg03.jpg) no-repeat center center;}
.login_tab{position:absolute; color:#FFF;z-index:9800}
.login_bg{width:1000px; height:440px;margin:0 auto; color:#FFF;}
*/
.login_bodybg{ background-image:url(../images/login_bodybg.png); height:100%; width:100%; background-position:center}
.login_bg{ background-image:url(../images/login_bg.png); width:1019px; height:609px; margin:0 auto;}
.login_copyright{ font-size:16px;font-family:"微软雅黑"; color:#ffffff; width:100%; text-align:center;} 
.login_copyright span{color:#fb4a4a;}
.login_copyright_img{background-image:url(../images/login_phone.png); width:20px;height:20px; margin:0 10px 0 0;}