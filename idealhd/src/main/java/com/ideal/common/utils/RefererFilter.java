package com.ideal.common.utils;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;

import com.ideal.service.poc.audit.AuditService;

public class RefererFilter implements Filter
{
    static Logger       log = Logger.getLogger(RefererFilter.class);

    public FilterConfig config;

    @Override
    public void destroy ()
    {
        // default implementation ignored
        this.config = null;
    }

    @Override
    public void doFilter ( ServletRequest req, ServletResponse res, FilterChain chain )
            throws IOException, ServletException
    {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;
        // 禁止缓存
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragrma", "no-cache");
        response.setDateHeader("Expires", 0);
        // 链接来源地址
        String referer = request.getHeader("referer");
        String origin = request.getHeader("Origin");

        if (referer == null || !referer.contains(request.getServerName()))
        {
            boolean needFilter = AuditService.getInstance().needFilter(request.getRequestURI());
            if (!needFilter)
            {
                log.error("referer is null or referer is error ServerName:" + request.getServerName() + ",referer:"
                        + referer);
                request.getRequestDispatcher("/page/loginFiter.jsp").forward(request, response);
            } else
            {
                chain.doFilter(request, response);
            }
        } else if (origin != null && !referer.startsWith(origin))
        {
            request.getRequestDispatcher("/page/loginFiter.jsp").forward(request, response);
        } else
        {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void init ( FilterConfig filterConfig ) throws ServletException
    {
        String allURLStr = filterConfig.getInitParameter("allow_url");
        AuditService.getInstance().init(allURLStr);
    }

}
