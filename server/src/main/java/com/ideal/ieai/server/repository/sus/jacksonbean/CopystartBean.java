package com.ideal.ieai.server.repository.sus.jacksonbean;

import java.io.Serializable;
import java.util.List;


public class CopystartBean implements Serializable
{
    public CopystartBean()
    {
    }
    private String version;
    private String instancename;
    private String relyPrj;
    private String envname;
    private String instName_input;
    private String deployStartTime;
    private String deployEndTime;
    private String startUserName;
    private int pkgNum;
    private String id_list;
    private List<CopystartPkgBean>pkgList;
    private List<CopystartStaticParamBean>staticParamList;
    private String ifrozen;
    public String getIfrozen ()
    {
        return ifrozen;
    }
    public void setIfrozen ( String ifrozen )
    {
        this.ifrozen = ifrozen;
    }
    public List<CopystartStaticParamBean> getStaticParamList ()
    {
        return staticParamList;
    }
    public void setStaticParamList ( List<CopystartStaticParamBean> staticParamList )
    {
        this.staticParamList = staticParamList;
    }
    public List<CopystartPkgBean> getPkgList ()
    {
        return pkgList;
    }
    public void setPkgList ( List<CopystartPkgBean> pkgList )
    {
        this.pkgList = pkgList;
    }
    public String getVersion ()
    {
        return version;
    }
    public void setVersion ( String version )
    {
        this.version = version;
    }
    public String getInstancename ()
    {
        return instancename;
    }
    public void setInstancename ( String instancename )
    {
        this.instancename = instancename;
    }
    public String getRelyPrj ()
    {
        return relyPrj;
    }
    public void setRelyPrj ( String relyPrj )
    {
        this.relyPrj = relyPrj;
    }
    public String getEnvname ()
    {
        return envname;
    }
    public void setEnvname ( String envname )
    {
        this.envname = envname;
    }
    public String getInstName_input ()
    {
        return instName_input;
    }
    public void setInstName_input ( String instName_input )
    {
        this.instName_input = instName_input;
    }
    public String getDeployStartTime ()
    {
        return deployStartTime;
    }
    public void setDeployStartTime ( String deployStartTime )
    {
        this.deployStartTime = deployStartTime;
    }
    public String getDeployEndTime ()
    {
        return deployEndTime;
    }
    public void setDeployEndTime ( String deployEndTime )
    {
        this.deployEndTime = deployEndTime;
    }
    public String getStartUserName ()
    {
        return startUserName;
    }
    public void setStartUserName ( String startUserName )
    {
        this.startUserName = startUserName;
    }
    public int getPkgNum ()
    {
        return pkgNum;
    }
    public void setPkgNum ( int pkgNum )
    {
        this.pkgNum = pkgNum;
    }
    public String getId_list ()
    {
        return id_list;
    }
    public void setId_list ( String id_list )
    {
        this.id_list = id_list;
    }

  
    
}
