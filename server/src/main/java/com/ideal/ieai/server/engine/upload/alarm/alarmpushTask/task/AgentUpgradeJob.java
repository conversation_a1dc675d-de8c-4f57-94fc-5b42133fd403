package com.ideal.ieai.server.engine.upload.alarm.alarmpushTask.task;

import com.ideal.ieai.server.engine.upload.alarm.alarmpushTask.AgentTimedUpgradeScheme;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AgentUpgradeJob implements Job {
    private static final Logger log = LoggerFactory.getLogger(AgentUpgradeJob.class);

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try{
            AgentTimedUpgradeScheme.getInstance().exect();
        }catch (Exception e){
            log.error(""+e);
        }
    }
}
