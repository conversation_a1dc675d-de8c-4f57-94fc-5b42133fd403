--4.7.21
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STRING_QUENY';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_STRING_QUENY(IID DECIMAL(19) NOT NULL ,IUUID VARCHAR(255) NOT NULL ,ICONTENT CLOB ,CONSTRAINT PK_IEAI_STRING_QUENY PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	select SIGN(COUNT(*)) INTO LI_EXISTS from SYSCAT.INDEXES where TABNAME='IEAI_STRING_QUENY' ;
	IF LI_EXISTS = 1 THEN
    SET LS_SQL ='alter table IEAI_STRING_QUENY drop primary key';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	select SIGN(COUNT(*)) INTO LI_EXISTS from SYSCAT.INDEXES where TABNAME='IEAI_STRING_QUENY' ;
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='alter table IEAI_STRING_QUENY add CONSTRAINT PK_IEAI_STRING_QUENY PRIMARY KEY (IID) ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	END
	$
--4.7.23
    BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISWHITEANDIP' AND TABNAME='IEAI_PROJECT_INFO_WS';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PROJECT_INFO_WS ADD COLUMN ISWHITEANDIP VARCHAR(30)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;

	
END
$
--4.7.24
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISDELETE' AND TABNAME='IEAI_PROJECT_INFO_WS';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PROJECT_INFO_WS ADD COLUMN ISDELETE DECIMAL(1) DEFAULT 0 ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISDELETEINFO' AND TABNAME='IEAI_PROJECT_INFO_WS';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PROJECT_INFO_WS ADD COLUMN ISDELETEINFO VARCHAR(255) ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ACTIVITY_MANAGE';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_ACTIVITY_MANAGE(IID DECIMAL(19)NOT NULL,IACTNAME VARCHAR(50), IACTDES VARCHAR(1000), IACTTYPE DECIMAL(2),ISVALID DECIMAL(2) DEFAULT 0,PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORT';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_PORT(IID DECIMAL(19)NOT NULL,IPORTNAME VARCHAR(50),IPORTDES VARCHAR(1000),PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ACTIVITY_PORT';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_ACTIVITY_PORT(IID DECIMAL(19)NOT NULL,ACTID DECIMAL(19),PORTID DECIMAL(19),FOREIGN KEY(ACTID)REFERENCES IEAI_ACTIVITY_MANAGE(IID),FOREIGN KEY(PORTID)REFERENCES IEAI_PORT(IID),CONSTRAINT PK_IEAI_ACTIVITY_PORT PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ACTIVITY_MANAGE';
	IF LI_EXISTS = 1 THEN
    SET LS_SQL ='ALTER TABLE IEAI_ACTIVITY_MANAGE DROP PRIMARY KEY';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ACTIVITY_MANAGE';
	IF LI_EXISTS = 1 THEN
    SET LS_SQL ='ALTER TABLE IEAI_ACTIVITY_MANAGE ADD CONSTRAINT PK_IEAI_ACTIVITY_MANAGE PRIMARY KEY(IID)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORT';
	IF LI_EXISTS = 1 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PORT DROP PRIMARY KEY';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORT';
	IF LI_EXISTS = 1 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PORT ADD CONSTRAINT PK_IEAI_PORT PRIMARY KEY(IID)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	

END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMS' AND TABNAME='IEAI_ACTIVITY_MANAGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_ACTIVITY_MANAGE ADD COLUMN IPARAMS VARCHAR(255) default '''' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IATOMACT' AND TABNAME='IEAI_ACTIVITY_MANAGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_ACTIVITY_MANAGE ADD COLUMN IATOMACT DECIMAL(2) default 0 ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	END
$
-- 4.7.27
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EXECACT_PROGRESS';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_EXECACT_PROGRESS(IID DECIMAL(19) NOT NULL ,IFLOWID DECIMAL(19) DEFAULT 0 ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,IACTID DECIMAL(10) DEFAULT 0 ,BHIACTID VARCHAR(255) ,IACTNAME VARCHAR(255) ,BHIACTNAME VARCHAR(255) ,CONSTRAINT PK_IEAI_EXECACT_PROGRESS PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;


	END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IEDITUSER' AND TABNAME='IEAI_PROJECT_INFO_WS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PROJECT_INFO_WS ADD  IEDITUSER DECIMAL(19,0) default -1';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		SET LS_SQL ='update IEAI_PROJECT_INFO_WS set IEDITUSER = ICREATEUSRID where IEDITUSER = -1';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);	
    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID=4;
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'update IEAI_PROJECT_TYPE_WS set IPROPERTYIDS=''3,5'' where IID =4';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=300;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (300, ''变更组合编排'', ''变更组合编排模块组'', 300, ''images/info82.png'')';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=400;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (400, ''Az组合编排'', ''Az组合编排模块组'', 400, ''images/info82.png'')';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-300;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-300, 0, ''所有变更组合编排业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 300, -300, -300, -300)';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-400;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-400, 0, ''所有Az组合编排业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 400, -400, -400, -400)';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=300;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (300,300,''变更组合编排源'','''','''','''','''',0)';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=400;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (400,400,''Az组合编排源'','''','''','''','''',0)';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS where IID = 300;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (300, ''IEAI_SUS_GROUP'', ''变更组合编排 '', ''5'')';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS where IID = 400;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (400, ''IEAI_AZ_GROUP'', ''Az组合编排 '', ''5'')';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TEMPLATE_CALL_ACT';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_TEMPLATE_CALL_ACT (IID DECIMAL(19,0) NOT NULL, IORDERID DECIMAL(19,0), IFLOWID DECIMAL(19,0), ISTATE  DECIMAL(19,0), ITIMESTAMP TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP, IEXECACTIID DECIMAL(19,0),IENVID DECIMAL(19,0),NODENAME VARCHAR(255),ITEMPLATETYPE SMALLINT, CONSTRAINT PK_IEAI_TEMPLATE_CALL_ACT PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TEMPLATE_CALL_REL';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_TEMPLATE_CALL_REL (FLOWIDOUTCLOBID DECIMAL(19,0) default -1,IID DECIMAL(19,0) NOT NULL, ICALLACTID DECIMAL(19,0) NOT NULL, IORDERID DECIMAL(19,0), ITEMPLATEUUID VARCHAR(255), ISTATE  DECIMAL(19,0), ITIMESTAMP TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP, IFLOWID DECIMAL(19,0), CONSTRAINT PK_IEAI_TEMPLATE_CALL_REL PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;	

	END
	$

--8.5.0
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PROJECT_ACT_INFOMATION';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_PROJECT_ACT_INFOMATION (IID DECIMAL(19,0) NOT NULL, IPROJECTID DECIMAL(19,0) NOT NULL, IPROTYPE DECIMAL(19,0), IFLOWNAME VARCHAR(255), IPRONAME VARCHAR(255),IACTNAME VARCHAR(255),IPARAMTYPE VARCHAR(255),IPARAMNAME VARCHAR(255), IACTTYPE VARCHAR(255) ,IPARAMVALUE VARCHAR(255), ITIMESTAMP TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP, CONSTRAINT PK_IEAI_PROJECT_ACT_INFOMATION PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUN_GROUP_ACTINFO';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_RUN_GROUP_ACTINFO (IFLOWID DECIMAL(19,0) NOT NULL,    IACTNAME VARCHAR(255),IPARAMTYPE VARCHAR(255),IPARAMNAME VARCHAR(255)  ,IPARAMVALUE VARCHAR(255), ITIMESTAMP TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAUDITOR' AND TABNAME='IEAI_PROJECT';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PROJECT ADD COLUMN IAUDITOR VARCHAR(255)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRELEASESTATE' AND TABNAME='IEAI_PROJECT';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PROJECT ADD COLUMN IRELEASESTATE DECIMAL(19)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IBACKINFO' AND TABNAME='IEAI_PROJECT';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_PROJECT ADD COLUMN IBACKINFO VARCHAR(255)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;
	END
	$
	
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);	
    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=6;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(6,4,''TelnetConnection'')';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	  SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=7;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(7,4,''SshConnection'')';    
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(1, ''funid'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''funid'' WHERE IID = 1';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(2, ''isusecalendar'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''isusecalendar'' WHERE IID = 2';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(3, ''flowcalendar'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''flowcalendar'' WHERE IID = 3';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(4, ''inputparameter'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''inputparameter'' WHERE IID = 4';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(5, ''outputparameter'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''outputparameter'' WHERE IID = 5';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(6, ''vartype'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''vartype'' WHERE IID = 6';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 7;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(7, ''varvalue'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''varvalue'' WHERE IID = 7';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 8;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(8, ''ftpserver'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpserver'' WHERE IID = 8';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 9;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(9, ''ftpport'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpport'' WHERE IID = 9';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 10;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(10, ''ftpuser'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpuser'' WHERE IID = 10';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 11;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(11, ''ftppassword'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftppassword'' WHERE IID = 11';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 12;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(12, ''ftpovertime'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpovertime'' WHERE IID = 12';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 13;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(13, ''ftpencode'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpencode'' WHERE IID = 13';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 14;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(14, ''isuseproxy'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''isuseproxy'' WHERE IID = 14';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 15;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(15, ''ftpproxyserver'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpproxyserver'' WHERE IID = 15';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 16;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(16, ''ftpproxyport'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpproxyport'' WHERE IID = 16';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 17;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(17, ''ftpproxyuser'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpproxyuser'' WHERE IID = 17';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 18;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(18, ''ftpproxyowd'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''ftpproxyowd'' WHERE IID = 18';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 19;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(19, ''useagentgroup'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''useagentgroup'' WHERE IID = 19';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 20;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(20, ''agentip'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''agentip'' WHERE IID = 20';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 21;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(21, ''agentport'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''agentport'' WHERE IID = 21';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 22;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(22, ''connecttype'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''connecttype'' WHERE IID = 22';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 23;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(23, ''agentgroup'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''agentgroup'' WHERE IID = 23';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 24;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(24, ''sshserver'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshserver'' WHERE IID = 24';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 25;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(25, ''sshport'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshport'' WHERE IID = 25';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 26;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(26, ''sshuser'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshuser'' WHERE IID = 26';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 27;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(27, ''sshtimeout'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshtimeout'' WHERE IID = 27';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 28;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(28, ''sshauthgroup'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshauthgroup'' WHERE IID = 28';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 29;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(29, ''sshkeyaddress'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshkeyaddress'' WHERE IID = 29';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 30;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(30, ''sshkeypasswd'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshkeypasswd'' WHERE IID = 30';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 31;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(31, ''sshloginpasswd'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshloginpasswd'' WHERE IID = 31';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 32;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(32, ''sshdocprompt'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshdocprompt'' WHERE IID = 32';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 33;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(33, ''sshagenttype'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshagenttype'' WHERE IID = 33';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 34;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(34, ''sshagentserver'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshagentserver'' WHERE IID = 34';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 35;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(35, ''sshagentport'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshagentport'' WHERE IID = 35';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 36;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(36, ''sshagentuser'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshagentuser'' WHERE IID = 36';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 37;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(37, ''sshagentpasswd'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''sshagentpasswd'' WHERE IID = 37';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 38;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(38, ''telnetgridData'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnetgridData'' WHERE IID = 38';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 39;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(39, ''telnetserver'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnetserver'' WHERE IID = 39';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 40;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(40, ''telnetport'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnetport'' WHERE IID = 40';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 41;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(41, ''telnetuser'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnetuser'' WHERE IID = 41';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 42;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(42, ''telnettimeout'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnettimeout'' WHERE IID = 42';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 43;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(43, ''telnetpwd'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnetpwd'' WHERE IID = 43';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 44;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(44, ''telnetendSign'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''telnetendSign'' WHERE IID = 44';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 45;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(45, ''httpConnectionServer'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''httpConnectionServer'' WHERE IID = 45';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 46;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(46, ''httpConnectionPort'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''httpConnectionPort'' WHERE IID = 46';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 47;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(47, ''httpConnectionTimeout'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''httpConnectionTimeout'' WHERE IID = 47';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 48;
    IF LI_EXISTS = 0 THEN
    SET    LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(48, ''httpConnectionProtocol'')';
        EXECUTE IMMEDIATE LS_SQL;
    ELSE
     SET   LS_SQL = 'UPDATE IEAI_PROJECT_PROPERTY_KEY_DC SET IKEYNAME = ''httpConnectionProtocol'' WHERE IID = 48';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    COMMIT;


	END
	$
	
	BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME = 'IEAI_PROJECT' AND COLNAME='IMODELID';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_PROJECT ADD IMODELID  DECIMAL(19,0)  DEFAULT -1';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IORDERID' AND TABNAME='IEAI_WORKFLOWINSTANCE';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERID DECIMAL(19)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IORDERUUID' AND TABNAME='IEAI_WORKFLOWINSTANCE';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERUUID VARCHAR(255)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISFORBIDDEN' AND TABNAME='IEAI_PROJECT_INFO_WS';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PROJECT_INFO_WS ADD IISFORBIDDEN DECIMAL(19) default 1 ';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATUS' AND TABNAME='IEAI_PROJECT_INFO_WS';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISTATUS DECIMAL(19) default 0';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=8;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(8, 4, ''DBConnection'', NULL)';    
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		COMMIT;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=9;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(9, 4, ''SFTPConnection'', NULL)';  
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		COMMIT;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=10;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(10, 4, ''HTTPConnection'', NULL)';  
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		COMMIT;
	
	
		END
		$

-- V8.9.0
BEGIN
DECLARE LS_SQL VARCHAR(4000);
DECLARE  LI_EXISTS  NUMERIC(2);	

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTIVITY_PORT' AND INDNAME='IDX_IEAI_ACTIVITY_PORT_ACTID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE INDEX IDX_IEAI_ACTIVITY_PORT_ACTID ON IEAI_ACTIVITY_PORT (ACTID)';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTIVITY_PORT' AND INDNAME='IDX_IEAI_ACTIVITY_PORT_PORTID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE INDEX IDX_IEAI_ACTIVITY_PORT_PORTID ON IEAI_ACTIVITY_PORT (PORTID)';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;

END
$

	
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_TEMPLATE_PARAM_MG';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_TEMPLATE_PARAM_MG (IID NUMERIC (19) NOT NULL,INAME VARCHAR (255) ,IENMU NUMERIC (19) ,IPARAMNAMETYPE VARCHAR (255) ,IPARAMNAMECLASS NUMERIC (19) ,IINSTANCEID NUMERIC (19) ,IPARAMNAME VARCHAR (255) ,IVALUE VARCHAR (255) ,IN_OUT NUMERIC (19) DEFAULT 0,DESCRIPTION VARCHAR (4000) ,IENMUNAME VARCHAR (255) ,IORDER NUMERIC (19) DEFAULT 0,IEDITABLE NUMERIC (19) DEFAULT 0,CELLID NUMERIC (19) DEFAULT 0, CONSTRAINT PK_IEAI_TEMPLATE_PARAM_MG PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE' AND  COLNAME='TEMPID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD TEMPID NUMERIC(19) DEFAULT 0'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE' AND  COLNAME='TYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD TYPE VARCHAR(100) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE' AND  COLNAME='TEMPNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD TEMPNAME VARCHAR(100) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE' AND  COLNAME='IENMU';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD IENMU NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARENTID' AND TABNAME='IEAI_SYNC_CATALOG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SYNC_CATALOG ADD IPARENTID NUMERIC(19) DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICOMPARETARGET' AND TABNAME='IEAI_SYNC_CATALOG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SYNC_CATALOG ADD ICOMPARETARGET VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;	
	END	IF;	
	
	END
$



BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID=15;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(15, 2, ''ExternalFunction'', NULL)';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TYPE' AND TABNAME='IEAI_FUNCTIONINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_FUNCTIONINFO ADD TYPE NUMERIC(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CLASSNAME' AND TABNAME='IEAI_FUNCTIONINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_FUNCTIONINFO ADD CLASSNAME VARCHAR(200)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;	
	END	IF;		
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FUNCTIONNAME' AND TABNAME='IEAI_FUNCTIONINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_FUNCTIONINFO ADD FUNCTIONNAME VARCHAR(200)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;	
	END	IF;	
	
END
$



BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '外部函数管理';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info160.png'' WHERE INAME = ''外部函数管理''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '内部函数管理';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info161.png'' WHERE INAME = ''内部函数管理''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '接口列表';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info162.png'' WHERE INAME = ''接口列表''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '可恢复工程';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info163.png'' WHERE INAME = ''可恢复工程''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '公共参数管理';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info164.png'' WHERE INAME = ''公共参数管理''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '福建paas统一入口';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info165.png'' WHERE INAME = ''福建paas统一入口''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '组合编排启动';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info166.png'' WHERE INAME = ''组合编排启动''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '组合编排查询';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info167.png'' WHERE INAME = ''组合编排查询''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE INAME = '组合编排';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'UPDATE IEAI_MENU SET IIMG = ''images/info168.png'' WHERE INAME = ''组合编排''';  
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
END
$


