package com.ideal.service.ic.monthReport;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.ic.monthReport.MonthReportManager;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class MonthReportService
{

    public static final Logger log = Logger.getLogger(MonthReportService.class);

    public List<Map<String, Object>> getReportYear() throws RepositoryException {
        MonthReportManager manager = new MonthReportManager();
        return manager.getReportYear();
    }

    public Map<String,Object> monthReportInfo (int start ,int limit, String systemId, String year) throws RepositoryException
    {
        Connection conn = null;
        Map<String, Object> resultInfo = null;
        StringBuilder sql = new StringBuilder();
        List<Object> listparam = new ArrayList<Object>();
        StringBuilder sqlWhere = new StringBuilder(" WHERE 1=1 \n");
        if (systemId != null && !"".equals(systemId) && !"-1".equals(systemId) && !"0".equals(systemId))
        {
            sqlWhere.append(" AND IMR.ISYSTEMID = ?");
            listparam.add(systemId);
        }
        if (year != null && !"".equals(year) && !"-1".equals(year) && !"0".equals(year))
        {
            sqlWhere.append(" AND IMR.IYEAR = ?");
            listparam.add(year);
        }

        sql.append(" SELECT IMR.IID, IP.INAME SYSTEMNAME, IMR.IYEAR, IMR.IMONTH, IMR.ICREATETIME, IMR.IFILENAME \n");
        sql.append(" FROM IEAI_MONTHREPORT IMR \n");
        sql.append(" LEFT JOIN IEAI_PROJECT IP ON IP.IID = IMR.ISYSTEMID \n");
        sql.append(sqlWhere);
        sql.append(" ORDER BY IMR.IYEAR, IMR.IMONTH, IMR.ICREATETIME DESC \n");

        MonthReportManager manager = new MonthReportManager();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
            resultInfo =  manager.getResultInfo(conn, sql.toString(), listparam,start, limit);

        } catch (Exception e)
        {
            log.error("monthReportInfo is error",e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConnection(conn, "monthReportInfo", log);
        }

        return resultInfo;
    }

    public Map<String,Object> startMonthReportJob () throws RepositoryException {
        MonthReportManager mananger = new MonthReportManager();
        return mananger.startMonthReportJob();
    }

    public Map<String,Object> endMonthReportJob () throws RepositoryException {
        MonthReportManager mananger = new MonthReportManager();
        return mananger.endMonthReportJob();
    }

    public Map<String,Object> doMonthReportJob () throws IOException {
        MonthReportManager mananger = new MonthReportManager();
        return mananger.doMonthReportJob();
    }

    public Blob getBlobByIid (String iid) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "SELECT IFILE FROM IEAI_MONTHREPORT WHERE IID = ?";
        Blob blob = null;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBResource.getConnection(method, log, Constants.IEAI_HEALTH_INSPECTION);
            ps = conn.prepareStatement(sql);
            ps.setString(1, iid);
            rs = ps.executeQuery();
            if (rs.next())
            {
                blob = rs.getBlob("IFILE");
            }
        } catch (Exception e)
        {
            log.error("getBlob is error",e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps,"getBlob", log);
        }

        return blob;
    }

    public int getTaskCount () throws RepositoryException{
        return new MonthReportManager().getTaskCount();
    }
}
