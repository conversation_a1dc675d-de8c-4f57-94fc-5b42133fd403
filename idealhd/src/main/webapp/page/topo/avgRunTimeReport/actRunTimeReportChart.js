Ext.onReady(function() {
	destroyRubbish();
	var myMask = new Ext.LoadMask(Ext.getBody(), {
		msg    : "读取中...",
		msgCls : 'z-index:10000;'
	});
	var colors = ['#6E548D','#94AE0A','#FF7348','#3D96AE', "#ff8809", "#ffd13e", "#a61187", "#24ad9a", "#7c7474", "#a66111"];  
	Ext.define('Ext.chart.theme.CustomBlue', {
		extend : 'Ext.chart.theme.Base',
		constructor : function(config) {
			var titleLabel = {
				font : '14px 微软雅黑',
				fill : '#b5b5b6'
			}, axisLabel = {
				fill : '#b5b5b6',
				font : '14px 微软雅黑',
				spacing : 2,
				padding : 5
			};

			this.callParent([ Ext.apply({
				colors : colors,
				axis : {
					stroke : '#15181b',
					'stroke-width' : 1
				},
				axisLabelLeft : axisLabel,
				axisLabelBottom : axisLabel,
				axisTitleLeft : titleLabel,
				axisTitleBottom : titleLabel
			}, config) ]);
		}
	});
	getData();
	function getData(){
		myMask.show();
		Ext.Ajax.request({
			url : 'avgRunTimeReportQuery.do',
			async : true,
			params : {
				prjIds : prjIds,
				actName : actName,
				actType : actType,
				countCycle : countCycle,
				startTime : startTime,
				endTime : endTime,
				isGraph : 'true',
				start : '0',
				limit : '15'
			},
			success : function(response) {
				var axesField = [];
				var seriesField = [];
				var myfields = [];
				var chartStore;
				var json = Ext.JSON.decode(response.responseText);
				if (json.fields != null && json.fields != ''
						&& json.fields != 'undefined') {
					myfields.push('timeArea');
					var fieldData = json.fields;
					for (var k = 0; k < fieldData.length; k++) {
						myfields.push(fieldData[k]);
					}
					for (var i = 0; i < myfields.length; i++) {
						if (myfields[i] == 'timeArea') {
							continue;
						}
						axesField.push(myfields[i]);
						seriesField.push({
							type : 'line',
							highlight : {
								size : 7,
								radius : 7
							},
							axis : 'left',
							xField : 'timeArea',
							yField : myfields[i],
							tips : {
								trackMouse : true,
								width : 250,
								height : 28,
								renderer : function(storeItem, item) {
									this.setTitle(item.value[1] + "(分钟)");
								}
							},
							markerConfig : {
								type : 'cross',
								size : 4,
								radius : 4,
								'stroke-width' : 0
							}
						});
					}
				}
				chartStore = Ext.create('Ext.data.Store', {
					fields : myfields,
					data : [],
					sorters : [ {
						property : 'timeArea',
						direction : 'ASC'
					} ]
				});
				if (json.avgRunTimeReportList != null
						&& json.avgRunTimeReportList != ''
						&& json.avgRunTimeReportList != 'undefined') {
					chartStore.loadData(json.avgRunTimeReportList);
				}
				var avgChart = Ext.create('Ext.chart.Chart', {
					theme : 'CustomBlue',
					animate : true,
					shadow : true,
					legend : {
						position : 'right' // 图例
					},
					store : chartStore,
					height : contentPanel.getHeight() - 175,
					width : contentPanel.getWidth(),
					axes : [ {
						type : 'Numeric',
						position : 'left',
						fields : axesField,
						title : '阶段平均耗时(分钟)',
						label : {
							font : '14px Arial',
							fill : '#b5b5b6'
						},
						grid : true,
						grid : {
							odd : {
								opacity : 1,
								fill : '#484648',
								stroke : '#454445',
								'stroke-width' : 0.5
							}
						}
					}, {
						type : 'Category',
						position : 'bottom',
						fields : [ 'timeArea' ],
						title : '时间段',
						label : {
							font : '14px Arial',
							fill : '#b5b5b6',
							rotate : {
								degrees : 375
							}
						}
					} ],
					series : seriesField,
					renderTo : 'actruntimereport_chart_area'
				});
				myMask.hide();
			},
			failure : function(request) {
				Ext.MessageBox.show({
					title : '操作提示',
					msg : "连接服务器失败",
					buttons : Ext.MessageBox.OK,
					icon : Ext.MessageBox.ERROR
				});
			},
			method : 'get'
		});
	}
	
})