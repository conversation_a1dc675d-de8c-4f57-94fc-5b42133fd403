package com.ideal.controller.jobscheduling.workflowquery;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.controller.jobscheduling.homepage.ParmBean;
import com.ideal.ieai.commons.WorkflowInfo;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.jobscheduling.repository.workflowquery.WorkflowQueryManager;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.jobscheduling.workflowquery.WorkflowQueryTrialService;

/**
 * 
 * @ClassName:  QueryWorkFlowHistoryController   
 * @Description:【作业调度】工作查询 流程信息历史展示
 * @author: junyu_zhang 
 * @date:   2018年3月5日 上午11:42:00   
 *     
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
@Controller
public class WorkFlowQueryHistoryController
{
    private static final Logger _log = Logger.getLogger(WorkFlowQueryHistoryController.class);
    public static final String  DATALIST = "dataList";

    /**   
     * @Title: showflowHistoryListForHis   
     * @Description: 【作业调度】首页,单击表格中一条记录'历史'列的查看
     * @param: @param request
     * @param: @param response
     * @param: @return
     * @param: @throws Exception      
     * @return: Object      
     * @throws RepositoryException 
     * @throws   
     * @author: tiejun_fan
     * @date:   2017-8-8 下午2:05:09   
     */
    @RequestMapping("showWorkflowHistoryListForHis.do")
    @ResponseBody
    public Object showflowHistoryListForHis ( HttpServletRequest request, HttpServletResponse response )
            throws RepositoryException
    {
        Map rsMap = new HashMap();
        String flowId = request.getParameter("flowid");
        String endTime = request.getParameter("endtime");
        String actName = request.getParameter("actname");
        String status = request.getParameter("status");
        String prjName = request.getParameter("prjName");
        String flowName = request.getParameter("flowName");
        String insDate = request.getParameter("insDate");
        String isSystem = request.getParameter("isSystem");
        String limit = request.getParameter("limit");
        String start = request.getParameter("start");

        ParmBean pb = new ParmBean();
        pb.setFlowId(flowId);
        pb.setEndTime(endTime);
        pb.setActName(actName);
        pb.setStatus(status);
        pb.setPrjName(prjName);
        pb.setFlowName(flowName);
        pb.setInsDate(insDate);
        pb.setIsSystem(isSystem);
        pb.setLimit(limit);
        pb.setStart(start);

        boolean isMain = Engine.getInstance().isMainFlow(prjName, flowName);
        List<WorkflowInfo> flowInfolist = new ArrayList();
        if (isMain)
        {
            // 如果输主线流程，则不查询记录
            WorkflowInfo[] flowInfos = new WorkflowInfo[flowInfolist.size()];
            flowInfolist.toArray(flowInfos);
        } else
        {
            rsMap = getactTryHis(pb);
            flowInfolist = (List<WorkflowInfo>) rsMap.get(DATALIST);
        }
        WorkflowQueryTrialService.getInstance().setShellExeCount(flowInfolist);
        rsMap.put(DATALIST, flowInfolist);
        return rsMap;
    }

    /**
     * 
     * @Title: getactTryHis   
     * @Description: 获取工作流历史信息
     * @param flowid
     * @param actName
     * @param status
     * @param prjName
     * @param flowName
     * @param insDate
     * @param isSystem
     * @param endtime
     * @return      
     * @author: junyu_zhang 
     * @param limit 
     * @param start 
     * @date:   2018年2月1日 上午8:08:37
     */
    private Map getactTryHis ( ParmBean pb )
    {
        String flowId = pb.getFlowId();
        String actName = pb.getActName();
        String prjName = pb.getPrjName();
        String flowName = pb.getFlowName();
        String insDate = pb.getInsDate();
        String isSystem = pb.getIsSystem();
        String start = pb.getStart();
        String limit = pb.getLimit();
        WorkflowQueryManager wfm = WorkflowQueryManager.getInstance();
        Map map =  new HashMap();
        
        Map<String,String> paramMap = new HashMap<String,String>();
        paramMap.put("flowId", flowId);
        paramMap.put("actName", actName);
        paramMap.put("prjName", prjName);
        paramMap.put("flowName", flowName);
        paramMap.put("insDate", insDate);
        paramMap.put("isSystem", isSystem);
        paramMap.put("limit", limit);
        paramMap.put("start", start);
        List<WorkflowInfo> flowInfolist = new ArrayList();
        try
        {
            map = wfm.queryactTryHisForAoms(paramMap);
            flowInfolist = (List<WorkflowInfo>) map.get(DATALIST);
        } catch (ServerException e)
        {
            _log.info("获取工作流历史信息失败:" + e);
        }
        map.put(DATALIST, flowInfolist);
        return map;
    }
}
