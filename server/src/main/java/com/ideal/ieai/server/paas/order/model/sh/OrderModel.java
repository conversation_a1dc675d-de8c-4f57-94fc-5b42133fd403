package com.ideal.ieai.server.paas.order.model.sh;

import java.util.List;
import java.util.Map;

/**
 * 
 * <ul>
 * <li>Title: OrderModel.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2022年8月8日
 */
public class OrderModel
{
    private String           orderName;    // 工单名称
    private Integer          templateType; // 模板类型
    private Long             templateId; //模板id
    private String           templateName;//模板名称
    private Integer          orderSort;    // 工单启动顺序
    private List<OrderParam> params;       // 创建工单模板所需工作流参数
    private Map<String,Object> busiparams; //工单创建所需业务参数
    private List<Computer>      computers;       // 模板所需设备信息

    /**
     * 变更环境id
     */
    private  List<Long>               envIdList;
    
    public String getOrderName ()
    {
        return orderName;
    }

    public void setOrderName ( String orderName )
    {
        this.orderName = orderName;
    }

    public Integer getTemplateType ()
    {
        return templateType;
    }

    public void setTemplateType ( Integer templateType )
    {
        this.templateType = templateType;
    }

    public Integer getOrderSort ()
    {
        return orderSort;
    }

    public void setOrderSort ( Integer orderSort )
    {
        this.orderSort = orderSort;
    }

    public List<OrderParam> getParams ()
    {
        return params;
    }

    public void setParams ( List<OrderParam> params )
    {
        this.params = params;
    }
    
    public Map<String, Object> getBusiparams ()
    {
        return busiparams;
    }

    public void setBusiparams ( Map<String, Object> busiparams )
    {
        this.busiparams = busiparams;
    }

    public List<Computer> getComputers ()
    {
        return computers;
    }

    public void setComputers ( List<Computer> computers )
    {
        this.computers = computers;
    }

    public Long getTemplateId ()
    {
        return templateId;
    }

    public void setTemplateId ( Long templateId )
    {
        this.templateId = templateId;
    }

    public String getTemplateName ()
    {
        return templateName;
    }

    public void setTemplateName ( String templateName )
    {
        this.templateName = templateName;
    }

    public List<Long> getEnvIdList() {
        return envIdList;
    }

    public void setEnvIdList(List<Long> envIdList) {
        this.envIdList = envIdList;
    }
}
