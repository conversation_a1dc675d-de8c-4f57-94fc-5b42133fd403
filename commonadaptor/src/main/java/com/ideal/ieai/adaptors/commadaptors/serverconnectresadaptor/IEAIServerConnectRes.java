package com.ideal.ieai.adaptors.commadaptors.serverconnectresadaptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

import org.apache.log4j.Logger;

import com.ideal.ieai.clientapi.APIException;
import com.ideal.ieai.clientapi.api.ConnectionAPI;
import com.ideal.ieai.clientapi.api.UserAdminAPI;
import com.ideal.ieai.commons.ClientSession;
import com.ideal.ieai.commons.RoleInfo;
import com.ideal.ieai.commons.UserBasicInfo;
import com.ideal.ieai.communication.Session;
import com.ideal.ieai.communication.commons.IConnection;
import com.ideal.ieai.communication.marshall.Response;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.activity.IConfig;
import com.ideal.ieai.core.activity.ISharedResource;

/**
 * <p>
 * Title:Server connect resource
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2004
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */

public class IEAIServerConnectRes implements ISharedResource
{
    public IEAIServerConnectRes()
    {
    }

    /**
     * according the config info ,init the necessory info
     * 
     * @param iConfig
     * @throws ActivityException
     */
    public void init ( IConfig iConfig ) throws ActivityException
    {
        if (null == iConfig || !(iConfig instanceof DefaultConfig))
        {
            throw new ActivityException();
        }
        _config = (DefaultConfig) iConfig;
        if (_config.get(KeyConstants.IP) != null)
        { // init ip
            ip = (String) _config.get(KeyConstants.IP);
        }
        if (_config.get(KeyConstants.PORT) != null)
        { // init port
            port = Integer.valueOf((String) _config.get(KeyConstants.PORT))
                    .intValue();
        }
        if (_config.get(KeyConstants.USER) != null)
        { // init user
            user = (String) _config.get(KeyConstants.USER);
        }
        if (_config.get(KeyConstants.PASSWORD) != null)
        { // init password
            password = (String) _config.get(KeyConstants.PASSWORD);
        }
        if (_config.get(KeyConstants.CONNECTTYPE) != null)
        { // init connect type
            if (_config.get(KeyConstants.CONNECTTYPE).equals(
                    res.getString("socket")))
            {
                connectType = IConnection.PLAIN_SOCKET;
            } else if (_config.get(KeyConstants.CONNECTTYPE).equals(
                    res.getString("webservice")))
            {
                /**
                 * @todo now not support web service
                 */
            }
        }
    }

    /**
     * if session is null, according config info ,create a session
     * 
     * @return _session
     */
    public Session connect ()
    {
        if (_session == null)
        { // session is null,create a session instance
            _session = connect(ip, port, user, password, connectType, null);
            return _session;
        } else
        { // session is exit ,return it
            return _session;
        }
    }

    /**
     * according the params,create a session
     * 
     * @param sip
     * @param iport
     * @param userId
     * @param pwd
     * @param type
     * @param servList
     * @return
     */
    public Session connect ( String sip, int iport, String userId, String pwd,
            int type, List servList )
    {
        Session s = null;
        try
        {
            ClientSession clientSession = new ClientSession();
            clientSession.setClient("Common Adaptor");
            clientSession.setUserLoginName(user);
            s = ConnectionAPI.login(sip, iport, clientSession, password,
                    connectType, servList, null, false);
        } catch (APIException ex)
        {
            _log.error(String.valueOf(ex.getErrorCode()) + ex.getErrorDetail());
        }
        return s;
    }

    /*
     * @return all the user info,keyset is user's id ,value is user name @throws
     * ActivityException
     */
    public Map getAllUsers () throws ActivityException
    {
        // not connect,throw exception
        Session sess = this.connect();
        if (sess == null)
        {
            throw new ActivityException("conncect failed");
        }
        sess.close();
        try
        {
            Response p = UserAdminAPI.getAllUsers(_session);
            if (p.hasError())
            { // if p has error return null
                _log.info("getUser failed");
                throw new ActivityException("getUser failed");
            }
            Map map = new HashMap();
            List lst = (List) p.getRetData();
            Iterator it = lst.iterator();
            while (it.hasNext())
            {
                UserBasicInfo userInfo = (UserBasicInfo) it.next();
                if (userInfo.isSA() || userInfo.isUA())
                { // not put sa and ua to map
                    continue;
                }
                userInfo.setLoginName(null == userInfo.getLoginName() ? ""
                        : userInfo.getLoginName());
                map.put(String.valueOf(userInfo.getId()), userInfo
                        .getLoginName());
            }
            return map;
        } catch (APIException ex)
        {
            _log.error(ex.getMessage(), ex);
            throw new ActivityException(ex.getMessage());
        }
    }

    /*
     * @return all the roles name in the list @throws ActivityException
     */
    public List getAllGroups () throws ActivityException
    {
        // not connect,throw exception
        Session sess = this.connect();
        if (sess == null)
        {
            throw new ActivityException("can't connect");
        }
        sess.close();
        try
        {
            Response p = UserAdminAPI.getAllRoles(_session);
            if (p.hasError())
            { // if p has error return null
                _log.error("getRoles failed");
                throw new ActivityException("getRoles failed");
            }
            List lst = (List) p.getRetData();
            List lRole = new ArrayList();
            Iterator it = lst.iterator();
            while (it.hasNext())
            {
                RoleInfo roleInfo = (RoleInfo) it.next();
                lRole.add(roleInfo._roleName);
            }
            return lRole;
        } catch (APIException ex)
        {
            _log.error(String.valueOf(ex.getErrorCode()) + ex.getErrorDetail());
            throw new ActivityException(ex.getMessage());
        }
    }

    /**
     * dispose all connection which connect to server
     */
    public void dispose ()
    {
        _session.close();
    }

    /**
     * close the session
     */
    public void close ()
    {
        _session.close();
    }

    private DefaultConfig         _config     = null;
    private Session               _session    = null;
    private String                ip;
    private int                   port        = -1;
    private String                user;
    private String                password;
    private int                   connectType = -1;
    private static ResourceBundle res         = ResourceBundle
                                                      .getBundle("resource.serverconnectRes");
    private static final Logger            _log        = Logger
                                                      .getLogger(IEAIServerConnectRes.class);
}
