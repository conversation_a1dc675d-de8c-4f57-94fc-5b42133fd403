/*******************************************************************************
 * 【变更管理】信息维护主页面
 ******************************************************************************/
var bsPageBar=null;
var copyConfigWindow=null;
Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	var gobleInstanceName = '';// 全局系統名
	var gobleInstanceId = -1;// 全局版本id
	var gobleVersionString = '--未选择--';// 全局版本文本
	//解决grid内容修改完后横向滚动条自动回到开始端问题
	Ext.override (Ext.view.Table,
	{
		focusRow : function (row, delay)
		{
			var me = this, row, gridCollapsed = me.ownerCt && me.ownerCt.collapsed, record;
			
			if (me.isVisible (true) && !gridCollapsed && (row = me.getNode (row, true)) && me.el)
			{
				me.scrollRowIntoView (row);
				record = me.getRecord (row);
				rowIdx = me.indexInStore (row);
				
				me.selModel.setLastFocused (record);
				
				if (!Ext.isIE)
				{ // the fix
					row.focus ();
				}
				
				me.focusedRow = row;
				me.fireEvent ('rowfocus', record, row, rowIdx);
			}
		}
	});
	/** *********************Model********************* */
	/** 树数据Model* */
	Ext.define ('instanceNameModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'iinstacneName',
		        type : 'string'
		    }
	    ]
	});
	/** 版本信息下拉框数据Model* */
	Ext.define ('instanceForComboxModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'versionString',
	                type : 'String'
	            }
	    ]
	});
	Ext.define ('instanceNameModel1',
			{
			    extend : 'Ext.data.Model',
			    fields : [
				    {
				        name : 'iinstacneName',
				        type : 'string'
				    }
			    ]
			});
	/** *********************Store********************* */
	/** 树数据源* */
	var instanceNameStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'instanceNameModel',
	    pageSize : 50,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getInstanceNameList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    },
	    listeners: {
            load: function (me, records, success, opts) {
                if (!success || !records || records.length == 0)
                    return;
 
                if(allSysCheckFlag){
                	//根据全局的选择，初始化选中的列
                    var selModel = instacneName_grid_panel.getSelectionModel();
                    Ext.Array.each(AllSelectedRecords, function () {
                        for (var i = 0; i < records.length; i++) {
                            var record = records[i];
                            console.log(record.get("iinstacneName"));
                            console.log(this.get("iinstacneName"));
//                            if (record.get("iinstacneName") == this.get("iinstacneName")){//选中record，并且保持现有的选择，不触发选中事件
//                            	selModel.select(record, true, true); 
//                            }
                            for(var j=0,len=instanceNameStore1.getCount();j<len;j++){
                            	var iinsstanceName = instanceNameStore1.getAt(j).get('iinstacneName');
                            	if (record.get("iinstacneName") == iinsstanceName){//选中record，并且保持现有的选择，不触发选中事件
                                	selModel.select(record, true, true); 
                                }
                            }
                        }
                    });
                }
            }
	    }
	});
	instanceNameStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
		    iinstanceNameForQuery : instacneNameForQuery.getValue ().trim (),
		    isysType : 3
		};
		Ext.apply (instanceNameStore.proxy.extraParams, new_params);
	});
	/** 版本信息下拉框数据源* */
	 instanceForComboxStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'instanceForComboxModel',
	    noCache : true,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getSusInstanceForComBox.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	 var instanceNameStore1 = Ext.create ('Ext.data.Store',
				{
				    autoLoad : true,
				    autoDestroy : true,
				    model : 'instanceNameModel1',
				    pageSize : 1000,
				    proxy :
				    {
				        type : 'ajax',
				        url : 'getInstanceNameList.do',
				        reader :
				        {
				            type : 'json',
				            root : 'dataList'
				        }
				    }
				});
	/**默认选中最后一项**/
	instanceForComboxStore.on ('load', function (store, options)
	{
		versionForQuery.select (this.first ());
		versionForQuery.fireEvent ('select', versionForQuery, this.first ());
		exportWithVersion.enable ();
		sysDisable.enable ();
	});
	instanceForComboxStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
		    iinstanceNameForQuery : gobleInstanceName,
		    isysType : 3
		};
		Ext.apply (instanceForComboxStore.proxy.extraParams, new_params);
	});
	instanceNameStore1.on ('beforeload', function (store, options)
			{
				var new_params =
				{
				    iinstanceNameForQuery : instacneNameForQuery.getValue ().trim (),
				    isysType : 3
				};
				Ext.apply (instanceNameStore1.proxy.extraParams, new_params);
			});
	/** 左侧树分页工具栏* */
	bsPageBar = Ext.create ('Ext.PagingToolbar',
	{
	    store : instanceNameStore,
	    dock : 'bottom',
	    displayInfo : true,
	    //baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    border : false,
	    emptyMsg : "没有记录"
	});
	
	/** *********************组件********************* */
	
	/** *********************Panel********************* */
	/** tabPanel* */
	var tabPanelForICFS = Ext.create ('Ext.tab.Panel',
	{
	    // title : '业务系统名称：无',
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : 0,
	    //	    width : '100%',
	    //	    height : contentPanel.getHeight (),
	    border : false,
	    // plain : true,
	    defaults :
	    {
		    autoScroll : true
	    },
	    items : [
	    	 /*{
	                title : '基础信息',
	                border : false,
	                hidden:false,
	                loader :
	                {
	                    url : 'accesspassSUSBasic.do',
	                    contentType : 'html',
	                    width : 300,
	                    autoLoad : false,
	                    loadMask : true,
	                    scripts : true
	                },
	                listeners :
	                {
		                activate : function (tab)
		                {
			                tab.loader.load (
			                {
				                params :
				                {
					                instanceId : gobleInstanceId
				                }
			                });
		                }
	                }
	            },*/
			    	{
		                title : '参数信息',
		                border : false,
		                loader :
		                {
		                    url : 'accesspaasParamListPageZB.do?instanceiid='+instanceiid+"&modelType="+sysType+"&istatus="+istatus,
		                    contentType : 'html',
		                    width : 300,
		                    autoLoad : false,
		                    loadMask : true,
		                    scripts : true
		                },
		                listeners :
		                {
			                activate : function (tab)
			                {
				                tab.loader.load (
				                {
					                params :
					                {
						                instanceId : gobleInstanceId,
						                insName:insName,
										 mtype:mtype,
										 itypeId:itypeId,
										 itype:itype
					                }
				                });
			                }
		                }
		            },
		            {
		                title : '系统信息',
		                border : false,
		                hidden:false,
		                loader :
		                {
		                    url : 'accesspaasPassZBStepPage.do?instanceiid='+instanceiid+"&modelType="+sysType+"&istatus="+istatus,
		                    contentType : 'html',
		                    autoLoad : false,
		                    loadMask : true,
		                    scripts : true
		                },
		                listeners :
		                {
			                activate : function (tab)
			                {
				                tab.loader.load (
				                {
					                params :
					                {
						                instanceId : gobleInstanceId,
						                instanceName:gobleInstanceName,
						                insName:insName,
										 mtype:mtype,
										 itypeId:itypeId,
										 itype:itype
					                }
				                });
			                }
		                }
		            }
/*	            ,
	            {
	                title : '环境信息',
	                border : false,
	                hidden:false,
	                loader :
	                {
	                    url : 'accesspassSUSENV.do',
	                    contentType : 'html',
	                    autoLoad : false,
	                    loadMask : true,
	                    scripts : true
	                },
	                listeners :
	                {
		                activate : function (tab)
		                {
			                tab.loader.load (
			                {
				                params :
				                {
					                instanceId : gobleInstanceId
				                }
			                });
		                }
	                }
	            }*/

	    ]
	});
	
	/** 左侧导出按钮* */
	var exportTM = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '导出',
//	    textAlign : 'center',
//	    width : 60,
//	    height : 22,
	    cls : 'Common_Btn',
	    handler : ExportTmAll,
	    hidden : pfSysDefendSwitch
	});
	/** 右侧导出按钮* */
	var exportWithVersion = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '导出',
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 60,
//	    height : 22,
	    disabled : true,
	    handler : ExportTemplate,
	    hidden : pfSysDefendSwitch
	});
	/** 右侧导出按钮* */
	var sysDisable= Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '初始化禁用',
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 60,
//	    height : 22,
	    disabled : true,
	    handler : sysDisableRule,
	    hidden : bankFlag
	});
	/** 查询按钮* */
	var queryButtonForWest = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '查询',
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 60,
//	    height : 22,
	    handler : queryWest
	});
	/** 删除按钮* */
	var deleteButtonForWest = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 60,
//	    height : 22,
	    text : '删除',
	    cls : 'Common_Btn',
	    disabled : true,
	    handler : onDeleteListener,
	    hidden : pfSysDefendSwitch
	});

	/** 左侧全选业务系统导出按钮 **/
	var allSysCheckFlag = false;
	var allSysCheckButtonLeft = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '删除Tab',
	    hidden:true,
	    cls : 'Common_Btn',
	    handler:function(){
			Ext.Msg.alert("提示","选项卡面板中会减少标签页");
			return;
	    }
	});
	/** 右侧一页纸导出按钮 **/
	var onePageExportButtonRight = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '添加Tab',
	    hidden:true,
	    cls : 'Common_Btn',
	    handler:function(){
			Ext.Msg.alert("提示","选项卡面板会新增标签页");
			return;
	    }
	});
	
	/** 左侧一页纸导出按钮 **/
	var btSave = Ext.create ("Ext.Button",
	{
	    //cls : 'Common_Btn',
	    text : '保存',
	    hidden:true,
	    cls : 'Common_Btn',
	    handler:function(){
			Ext.Msg.alert("提示","面板中的数据将被保存");
			return;
	    }
	});
	
	/** 树查询条件* */
	var instacneNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    emptyText : '--请输入系统名称--',
	    // width : 350,
//	    width : 180,
	    xtype : 'textfield',
	    padding : '0 0 0 5',
		listeners : {
			specialkey : function(field, e) {
				if (e.getKey() == Ext.EventObject.ENTER) 
				{
					queryWest();
				}
			}
		}
	});
	/** 版本信息下拉框* */
	var versionForQuery = Ext.create ('Ext.form.field.ComboBox',
	{
	    // labelWidth : 80,
	    width : '50%',
	    // margin : '5',
	    padding : '0 0 0 5',
	    // fieldLabel : '版本信息',
	    emptyText : '--请选择版本信息--',
	    queryMode : 'local',
	    triggerAction : "all",
	    queryMode : 'local',
	    store : instanceForComboxStore,
	    displayField : 'versionString',
	    anyMatch : true,
	    valueField : 'iid',
	    listConfig :
	    {
		    maxHeight : 280
	    },
	    listeners :
	    {
		    select : function (combo, records, eOpts)
		    {
		    	Ext.MessageBox.wait("数据查询中...", "进度条");
			    gobleInstanceId = this.getValue ();
			    gobleVersionString = this.getRawValue ();
			    tabPanelForICFS.setActiveTab (1);
			    tabPanelForICFS.items.getAt (1).getLoader ().load (
			    {
			        url : 'getInstanceInstanceInfoForSUS.do',
			        contentType : 'html',
			        autoLoad : false,
			        loadMask : true,
			        scripts : true,
			        params :
			        {   
			        	instanceName:gobleInstanceName,
				        instanceId : gobleInstanceId
			        }
			    });
			    setTimeout('Ext.MessageBox.hide()', 1000 );
			    //infoPageBar.moveFirst ();
		    }
	    }
	});
	/** 树列表columns* */
	var instacneNameColumns = [
	        {
	            text : '序号',
	            // align : 'center',
	            width : 65,
	            xtype : 'rownumberer'
	        },
	        {
	            text : '系统名称',
	            dataIndex : 'iinstacneName',
	            flex : 1
	        }, {
	    	    text : '操作',
	    	    dataIndex : 'iinstacneName',
	    	    hideable:false,
	    	    width : 50,
	    	    hidden : pfSysDefendSwitch,
	    	    renderer: function(value,metaData,record){
	    	      return '<a href="javascript:void(0);" onclick="openCopyWindow(\''+value+'\')">复制</a>';
	    	    }
	    	  }
	];
	var cellEditing = Ext.create ('Ext.grid.plugin.CellEditing',
	{
	    clicksToEdit : 2,
	    //解决grid内容修改完后横向滚动条自动回到开始端问题
	    onEditComplete : function (ed, value, startValue)
	    {
		    var me = this, activeColumn = me.getActiveColumn (), context = me.context, record;
		    
		    if (activeColumn)
		    {
			    record = context.record;
			    
			    me.setActiveEditor (null);
			    me.setActiveColumn (null);
			    me.setActiveRecord (null);
			    
			    context.value = value;
			    if (!me.validateEdit ())
			    {
				    return;
			    }
			    
			    if (!record.isEqual (value, startValue))
			    {
				    record.set (activeColumn.dataIndex, value);
			    }
			    
			    if (!Ext.isIE)
			    { // the fix
				    context.view.focus (false, true);
			    }
			    
			    me.fireEvent ('edit', me, context);
			    me.editing = false;
		    }
	    },
	    //解决grid内容修改完后横向滚动条自动回到开始端问题
	    cancelEdit : function ()
	    {
		    var me = this, activeEd = me.getActiveEditor ();
		    
		    me.setActiveEditor (null);
		    me.setActiveColumn (null);
		    me.setActiveRecord (null);
		    if (activeEd)
		    {
			    activeEd.cancelEdit ();
			    
			    if (!Ext.isIE)
			    {
				    me.context.view.focus ();
			    }
			    
			    me.callSuper (arguments);
			    return;
		    }
		    // If we aren't editing, return true to allow the event to bubble
		    return true;
	    }
	});

	var AllSelectedRecords = [];
	/** 树列表panel* */
	var instacneName_grid_panel = Ext.create ('Ext.grid.Panel',
	{
	    store : instanceNameStore,
	    region : 'center',
	    border : false,
	    columnLines : true,
	    flex : 2,
	    columns : instacneNameColumns,
	    //bbar : bsPageBar,
	    idisplayMsg : false,
	    //ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    {
		    checkOnly : true,
		    listeners: {
                select: function (me, record, index, opts) {
                    AllSelectedRecords.push(record);
                }
            }
	    }),
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        //cls:'customize_form_cn',
		        items : [
		                // sysNameForQueryRelation, ctForQuery, cmForQuery,
		                instacneNameForQuery, queryButtonForWest, deleteButtonForWest, exportTM
		        ]
		    },
		    {
		        xtype : 'toolbar',
		        //cls:'customize_form_cn',
		        items : [
		                //allSysCheckButtonLeft,onePageExportButtonLeft,btSave
		        ]
		    }
	    ],
	    listeners :
	    {
	    	celldblclick: function( view, td, cellIndex, record, tr, rowIndex, e, eOpts ) {
	            if(cellIndex==1||cellIndex==2)
	            	{
	            	// gobleScid = 0;
				    // gobleCheckPointId = 0;
				    // gobleCheckItemName='';
				    // gobleCheckPointName = '';
				    // gobleIp = record.get ('cpIp');
				    // gobleCpid = record.get ('cpId');
				    // cleanPanelListener (2, record.get ('cpIp'), "无");
				    gobleInstanceName = record.get ('iinstacneName');
				    gobleInstanceId = -1;
				    gobleVersionString = '--未选择--';
				    versionForQuery.setValue ('');
				    bic_east_panel.setTitle ('系统信息列表');
				    if (gobleInstanceName != '')
				    {
					    bic_east_panel.setTitle (bic_east_panel.title + "-系统名称：" + gobleInstanceName);
				    }
				    instanceForComboxStore.clearFilter ();
				    instanceForComboxStore.load ();
				    //			    instanceInfoStore.load ();
	            	}
	          }
	    	/*,
		    itemclick : function (view, record, item, index, e, eOpts)
		    {
			    // gobleScid = 0;
			    // gobleCheckPointId = 0;
			    // gobleCheckItemName='';
			    // gobleCheckPointName = '';
			    // gobleIp = record.get ('cpIp');
			    // gobleCpid = record.get ('cpId');
			    // cleanPanelListener (2, record.get ('cpIp'), "无");
			    gobleInstanceName = record.get ('iinstacneName');
			    gobleInstanceId = -1;
			    gobleVersionString = '--未选择--';
			    versionForQuery.setValue ('');
			    bic_east_panel.setTitle ('系统信息列表');
			    if (gobleInstanceName != '')
			    {
				    bic_east_panel.setTitle (bic_east_panel.title + "-系统名称：" + gobleInstanceName);
			    }
			    instanceForComboxStore.clearFilter ();
			    instanceForComboxStore.load ();
			    //			    instanceInfoStore.load ();
		    }*/
	    },
	    collapsible : false
	});
	if(pfSysDefendSwitch){
		instacneName_grid_panel = Ext.create ('Ext.grid.Panel',
				{
				    store : instanceNameStore,
				    region : 'center',
				    border : false,
				    columnLines : true,
				    flex : 2,
				    columns : instacneNameColumns,
				    //bbar : bsPageBar,
				    idisplayMsg : false,
				    //ipageBaseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
				    selModel : Ext.create ('Ext.selection.CheckboxModel',
				    {
					    checkOnly : true,
					    listeners: {
			                select: function (me, record, index, opts) {
			                    AllSelectedRecords.push(record);
			                }
			            }
				    }),
				    dockedItems : [
					    {
					        xtype : 'toolbar',
					        //cls:'customize_form_cn',
					        items : [
					 
					        ]
					    }
				    ],
				    listeners :
				    {
				    	celldblclick: function( view, td, cellIndex, record, tr, rowIndex, e, eOpts ) {
				            if(cellIndex==1||cellIndex==2)
				            	{
							    gobleInstanceName = record.get ('iinstacneName');
							    gobleInstanceId = -1;
							    gobleVersionString = '--未选择--';
							    versionForQuery.setValue ('');
							    bic_east_panel.setTitle ('系统信息列表');
							    if (gobleInstanceName != '')
							    {
								    bic_east_panel.setTitle (bic_east_panel.title + "-系统名称：" + gobleInstanceName);
							    }
							    instanceForComboxStore.clearFilter ();
							    instanceForComboxStore.load ();
				            	}
				          }
				    },
				    collapsible : false
				});
	}
	/** 判断删除按钮是否可用* */
	instacneName_grid_panel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		deleteButtonForWest.setDisabled (selections.length === 0);
	});
	// computer_grid_panel.view.loadMask = false;
	/** 右侧panel* */
	var bic_east_panel = Ext.create ('Ext.panel.Panel',
	{
	    // padding : '0 5 0 0',
	    //	    flex : 1,\
	    layout : 'border',
	/*    title : '系统信息列表',*/
	    region : 'center',
	    //cls:'customize_panel_back',
	    defaults :
	    {
	        split : false, // 是否有分割线
	        collapsible : false
	    // 是否可以折叠
	    },
	    //height : contentPanel.getHeight () - 70,
	    collapse : true,
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        //cls:'customize_form_cn',
		        items : [
		                '->',onePageExportButtonRight,allSysCheckButtonLeft,btSave
		        ]
		    }
	    ],
	    items : [
		    tabPanelForICFS
	    ]
	});
	/** 树panel* */
	var bic_west_panel = Ext.create ('Ext.panel.Panel',
	{
	    //padding : '0 5 0 0',
	    // flex : 1,
	    layout : 'border',
	    title : '应用变更系统列表',
	    //cls:'customize_panel_back panel_space_right', 
	    region : 'west',
	    defaults :
	    {
	        split : false, // 是否有分割线
	        collapsible : true
	    // 是否可以折叠
	    },
//	    height : contentPanel.getHeight () - 70,
	    // width:contentPanel.getWidth()-700,
	    width : '35%',
	    // collapse : true,
	    collapsible : true,
	    items : [
		    instacneName_grid_panel
	    ],
	    listeners :
	    {
	        collapse : function ()
	        {
		        contentPanel.fireEvent ('resize');
		        
	        },
	        expand : function ()
	        {
		        contentPanel.fireEvent ('resize');
	        }
	    }
	});
	/** 二级主panel* */
	var businessInsConfigBoderPanel = Ext.create ('Ext.panel.Panel',
	{
		renderTo : "instanceConfigForSUSMainDiv",
	    layout : 'border',
	    width : contentPanel.getWidth (),//'100%',
	    height : contentPanel.getHeight (),
	    //bodyPadding : 5,
	   // height : contentPanel.getHeight () -37,
	    border : false,
	    items : [
	            /*bic_west_panel, */bic_east_panel
	    ]
	});
	/** 主panel* *//*
	var businessInsConfig_mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "instanceConfigForSUSMainDiv",
	    width : '100%',
	    height : '100%',
	    border : false,
	    bodyPadding : 5,
	    items : [
		    businessInsConfigBoderPanel
	    ]
	});*/
	
//	$("body").off('keydown').on('keydown',function(event) {
// 	    if (event.keyCode == "13") { 
// 	    	queryWest();
// 	    }
// 	});
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
//		bic_east_panel.setHeight (contentPanel.getHeight () - 70);
//		bic_west_panel.setHeight (contentPanel.getHeight () - 70);
//		businessInsConfigBoderPanel.setHeight (contentPanel.getHeight () - 37);
//		businessInsConfigBoderPanel.setWidth ('100%');
//		businessInsConfig_mainPanel.setHeight ('100%');
//		businessInsConfig_mainPanel.setWidth ('100%');
		//		bic_east_panel.setWidth ('100%');
		//		tabPanelForICFS.setWidth ('100%');
	})
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (businessInsConfigBoderPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	/** 查询树* */
	function queryWest ()
	{
		instanceNameStore1.getProxy ().setExtraParam ("iinstanceNameForQuery", instacneNameForQuery.getValue());
		instanceNameStore1.load();
		bic_east_panel.setTitle ('系统信息列表');
		gobleInstanceName = '';// 清空全局系统名
		gobleInstanceId = -1;// 清空全局版本id
		gobleVersionString = '--未选择--';// 清空全局版本文本
		bsPageBar.moveFirst ();// 刷新左列表
		instanceForComboxStore.removeAll ();// 清空下拉框内容
		versionForQuery.clearValue ();// 清空下拉框内容
		exportWithVersion.disable ();
		sysDisable.disable ();
	}
	/** 删除系统* */
	function onDeleteListener (btn)
	{
		var record = instacneName_grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		Ext.MessageBox.confirm ('提示', "删除系统会同时删除该系统的所有版本信息内容!<br>是否确认删除选中数据!", function (btn)
		{
			if (btn == 'no')
			{
				return;
			}
			if (btn == 'yes')
			{
				
				Ext.MessageBox.wait ("数据处理中...", "进度条");
				var ids = [];
				Ext.Array.each (record, function (recordObj)
				{
					var iinstacneName = recordObj.get ('iinstacneName');
					if (fucCheckLength (iinstacneName) > 0)
					{
						ids.push ("'" + iinstacneName + "'");
					}
				});
				Ext.Ajax.request (
				{
				    url : 'deleteInstanceForAuto.do',
				    timeout : 30000,
				    params :
				    {
				        isysType : 3,
				        deleteNames : ids.join (','),
				        systemType:3
				    },
				    method : 'POST',
				    success : function (response, opts)
				    {
					    var success = Ext.decode (response.responseText).success;
					    
					    if (success)
					    {
						    queryWest ();
					    }
					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
				    },
				    failure : function(result, request) {
				    	Ext.MessageBox.hide ();
				    	secureFilterRs(result,"操作失败！");
				    }
				});
				
			}
		});
		
	}
	/**右侧导出* */
	function ExportTemplate ()
	{
		if ('' != gobleInstanceId)
		{
			window.location.href = 'exportTMforAuto.do?iinstanceNameForQuery=' + gobleInstanceName + '&iidForQuery='
			        + gobleInstanceId+'&isysType=3';
		}
		else
		{
			Ext.Msg.alert ('提示', '请选择版本信息');
		}
	}
	/**左侧导出* */
	function ExportTmAll ()
	{
		var record = instacneName_grid_panel.getSelectionModel ().getSelection ();
		var instanceNames = [];
		Ext.Array.each (record, function (recordObj)
		{
			var iinstacneName = recordObj.get ('iinstacneName');
			instanceNames.push (iinstacneName);
		});
		
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		
		if (record.length > 50)
		{
			Ext.MessageBox.confirm ('提示', "您选择了50个以上的系统，导出会耗费一定的时间，确定要导出吗？", function (btn)
			{
				if (btn == 'no')
				{
					return;
				}
				if (btn == 'yes')
				{
					window.location.href = 'exportTMAllforAuto.do?instanceNames=' + instanceNames.join (',') +'&isysType=3';
				}
			});
		}
		else
		{
			window.location.href = 'exportTMAllforAuto.do?instanceNames=' + instanceNames.join (',')+'&isysType=3';
		}
	}
	
	function openCopyRightWindow(gobleInstanceId) {
		var instanceid=versionForQuery.getValue();
		Ext.MessageBox.wait ("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'copyInstanceForRinghtSus.do',
				timeout : 30000,
				params : {
					instanceName : gobleInstanceName,
		        	instanceid:instanceid
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						Ext.Msg.alert('提示', '复制成功!');
						instanceForComboxStore.load ();
					} else {
						Ext.Msg.alert('提示',
								Ext.decode(response.responseText).message);
					}
				},
				failure : function(result, request) {
					Ext.MessageBox.hide();
					secureFilterRs(result, "操作失败！");
				}
			});
	}
	

	function sysDisableRule(gobleInstanceId) {
		var instanceid=versionForQuery.getValue();
		Ext.MessageBox.wait ("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'sysDisableRule.do',
				timeout : 30000,
				params : {
					instanceName : gobleInstanceName,
		        	instanceid:instanceid
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						Ext.Msg.alert('提示', '同步成功!');
						instanceForComboxStore.load ();
					} else {
						Ext.Msg.alert('提示',
								Ext.decode(response.responseText).message);
					}
				},
				failure : function(result, request) {
					Ext.MessageBox.hide();
					secureFilterRs(result, "操作失败！");
				}
			});
	}
	
	function allSystemChecked(){
		allSysCheckFlag = true;
		instacneName_grid_panel.getSelectionModel().selectAll();
	}
	
	function onePageExportLeft(){
		var record = instacneName_grid_panel.getSelectionModel ().getSelection ();
		var instanceNames = [];
		var codeApmTypes = [];
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		if(!allSysCheckFlag){
			Ext.Array.each (record, function (recordObj)
					{
						var iinstacneName = recordObj.get ('iinstacneName');
						instanceNames.push (iinstacneName);
					});
		}else{
			for(var j=0,len=instanceNameStore1.getCount();j<len;j++){
	        	var iinsstanceName = instanceNameStore1.getAt(j).get('iinstacneName');
	        	instanceNames.push (iinsstanceName);
	        }
		}
		//var instanceNames = [];
		allSysCheckFlag = false;
		window.location.href = 'exportOPAllforAutoSUS.do?instanceNames=' + instanceNames.join (',') + '&isysType=3';
	}
	
	function onePageExportRight(){
		window.location.href = 'exportOPforAutoSUSRight.do?iinstanceNameForQuery=' + gobleInstanceName + '&iidForQuery='
        + gobleInstanceId+'&isysType=3';
	}
});

function openCopyWindow(instanceName) {
	
	copyConfigWindow = Ext.create ('Ext.window.Window',
			{
			    title : '复制系统',
			    modal : true,
			    closeAction : 'destroy',
			    constrain : true,
			    autoScroll : true,
			    width : 500,
			    height : 110,
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
			    layout : 'fit',
			    loader :
			    {
			        url : 'instanceCopyinit.do',
			        params :
	                 {
			        	instanceName : instanceName
	                 },
			        autoLoad : true,
			        scripts : true
			    },
			    listeners: {
			    	"close":function(){
			    		bsPageBar.moveFirst ();// 刷新左列表
			    	}
			    }
			    		
			}).show ();
}
