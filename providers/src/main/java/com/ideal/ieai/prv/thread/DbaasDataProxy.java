package com.ideal.ieai.prv.thread;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.dubbo.models.QuartZModel;
import com.ideal.ieai.server.ieaikernel.ServerEnv;

import EDU.oswego.cs.dl.util.concurrent.ConcurrentHashMap;
import EDU.oswego.cs.dl.util.concurrent.PooledExecutor;

public class DbaasDataProxy
{
    private Thread _checkActListThread = new Thread(new CheckActsListThread(), "CheckActsListThread");

    public void start ()
    {
        initThreadPool();
        _actExecThreadPool.waitWhenBlocked();
        _actExecThreadPool.setKeepAliveTime(2000);
        _checkActListThread.start();
    }

    private static int getThreadPoolCfg ( String key, int defaultValue )
    {
        String cfg = ServerEnv.getInstance().getSysConfig(key, String.valueOf(defaultValue));
        try
        {
            int intValue = Integer.parseInt(cfg);
            return intValue > 0 ? intValue : defaultValue;
        } catch (NumberFormatException ex)
        {
            return defaultValue;
        }
    }

    private static PooledExecutor _actExecThreadPool = null;

    private static void initThreadPool ()
    {
        int actExecThreadPoolMax = getThreadPoolCfg("dbaas.data.result.execthread", 1500);
        _actExecThreadPool = new PooledExecutor(actExecThreadPoolMax);
    }

    private static Map _execActsList = new ConcurrentHashMap();// new

    public void addRequest ( Map model )
    {
        if (!_execActsList.containsKey(model.get("reqid")))
        {
            _execActsList.put(model.get("reqid"), model);
        }
    }

    public long getFreeThreadPool ()
    {
        return _actExecThreadPool.getMaximumPoolSize() - _actExecThreadPool.getPoolSize();
    }

    public boolean getActsMap ( String key )
    {
        boolean flag = false;
        if (_execActsList.containsKey(key))
        {
            flag = true;
        }
        return flag;
    }

    private class CheckActsListThread implements Runnable
    {
        boolean chkloop = true;

        @Override
        public void run ()
        {
            while (chkloop)
            {
                try
                {
                    try
                    {
                        Thread.sleep(2000);
                    } catch (InterruptedException e)
                    {
                        continue;
                    }
                    if (_execActsList.size() <= 0)
                        continue;
                    synchronized (_execActsList)
                    {
                        for (Iterator iter = _execActsList.values().iterator(); iter.hasNext();)
                        {
                            try
                            {
                                _log.info("_actExecThreadPool-max:" + _actExecThreadPool.getMaximumPoolSize()
                                        + ",_actExecThreadPool-use:" + _actExecThreadPool.getPoolSize());
                                if (_actExecThreadPool.getMaximumPoolSize() > _actExecThreadPool.getPoolSize())
                                {
                                    Map model = (Map) iter.next();
                                    try
                                    {
                                        String reqid = String.valueOf(model.get("reqid"));
                                        if (reqid.startsWith("thread-exe"))
                                        {
                                            long instanceIid = Long.parseLong(String.valueOf(model.get("instanceIid")));
                                            String serviceuuid = String.valueOf(model.get("serviceuuid"));
                                            long resId = Long.parseLong(String.valueOf(model.get("resId")));
                                            long flowId = Long.parseLong(String.valueOf(model.get("flowId")));
                                            long coatId = Long.parseLong(String.valueOf(model.get("coatId")));
                                            long workItemId = Long.parseLong(String.valueOf(model.get("workItemId")));
                                            boolean pollFlagAnalyze = Boolean
                                                    .parseBoolean(String.valueOf(model.get("pollFlagAnalyze")));
                                            _log.info("thread-exe:" + flowId + "-" + "" + coatId + " " + workItemId + ""
                                                    + instanceIid);
                                            QuartZModel notexis = (QuartZModel) model.get("notexis");
                                            DbaasExecThreadMonitor.addActCount(workItemId);
                                            DbaasInstanceRunExec executor = new DbaasInstanceRunExec(instanceIid,
                                                    serviceuuid, resId, workItemId, flowId, coatId, notexis, reqid,
                                                    pollFlagAnalyze);
                                            _actExecThreadPool.execute(executor);
                                        } else if (reqid.startsWith("groupexec"))
                                        {
                                            _log.info("groupexec-exe:fid." + model.get("flowId") + "-reqid."
                                                    + model.get("reqid") + " start send ");
                                            DbaasServiceAsynActivityExe executor = new DbaasServiceAsynActivityExe(
                                                    model);
                                            _actExecThreadPool.execute(executor);
                                            _log.info("groupexec-exe:fid." + model.get("flowId") + "-reqid."
                                                    + model.get("reqid") + " send succefully");
                                        } else if (reqid.startsWith("resource-exec"))
                                        {
                                            _log.info("resource-exe:fid." + model.get("flowId") + "-wkid." + ""
                                                    + model.get("workItemId") + " start send");
                                            DbaasReourceActivityExe executor = new DbaasReourceActivityExe(model);
                                            _actExecThreadPool.execute(executor);
                                            _log.info("resource-exe:fid." + model.get("flowId") + "-wkid." + ""
                                                    + model.get("workItemId") + " is send succefully");
                                        } else
                                        {
                                            List<Map<String, Object>> list1 = (List) model.get("subList");
                                            String serviceId = String.valueOf(model.get("serviceId"));
                                            String table = String.valueOf(model.get("table"));
                                            String sql = String.valueOf(model.get("sql"));
                                            long trigermodel = Long.parseLong(String.valueOf(model.get("trigermodel")));
                                            Map<Integer, String> colType = (Map) model.get("colType");
                                            Map<Integer, String> colName = (Map) model.get("colName");
                                            int count = Integer.parseInt(String.valueOf(model.get("count")));
                                            String dbid = String.valueOf(model.get("dbid"));
                                            java.sql.Timestamp sysdate = (java.sql.Timestamp) model.get("sysdate");
                                            long flowId = Long.parseLong(String.valueOf(model.get("flowId")));
                                            QuartZModel qz = (QuartZModel) model.get("qz");
                                            DbaasActivityExe executor = new DbaasActivityExe(list1, serviceId, table,
                                                    sql, trigermodel, colType, colName, count, dbid, sysdate, flowId,
                                                    qz, reqid);
                                            _actExecThreadPool.execute(executor);
                                        }

                                        iter.remove();
                                        _log.info("_actExecThreadPool model .ok");
                                    } catch (InterruptedException e)
                                    {
                                        continue;
                                    }
                                } else
                                {
                                    Thread.sleep(2000);
                                }
                            } catch (Exception e)
                            {
                                _log.error("CheckActsListThread client is error ", e);
                                throw e;
                            }
                        }
                    }
                } catch (Throwable a)
                {
                    _log.error("CheckActsListThread is error " + a);
                }
            }
        }
    }

    public DbaasDataProxy()
    {
    }

    public void run ()
    {

    }

    private static final Logger _log = Logger.getLogger(DbaasDataProxy.class);
}
