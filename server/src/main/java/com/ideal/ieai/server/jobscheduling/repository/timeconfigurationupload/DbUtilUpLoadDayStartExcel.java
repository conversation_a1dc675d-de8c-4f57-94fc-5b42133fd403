package com.ideal.ieai.server.jobscheduling.repository.timeconfigurationupload;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.cmdb.service.CronExpression;
import com.ideal.ieai.server.engine.core.ExcelActUtil;
import com.ideal.ieai.server.jobscheduling.repository.project.ExcelDayStartAct;
import com.ideal.ieai.server.jobscheduling.repository.project.ExcelDayStartFlowParams;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.UpLoadExcelManager;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.UpLoadTempExcleManager;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.ActInfoBean;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.DayStartBean;
import com.ideal.ieai.server.jobscheduling.util.taskuploadbean.MainLinePreSuccBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.TransactionStorerJDBC;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBSourceMonitor;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.engine.RepWorkflowInstance;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.repository.project.ProjectSaveUtilBean;
import com.ideal.ieai.server.repository.project.RepProject;
import com.ideal.ieai.server.repository.project.RepWorkflow;
import com.ideal.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**   
 * @ClassName:  DbUtilUpLoadDayStartExcel   
 * @Description:(主线定时配置列表上载)   
 * @author: yue_sun 
 * @date:   2018年2月26日 下午3:15:48   
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 */
public class DbUtilUpLoadDayStartExcel
{
    // 允许不同工程同时上传，将静态变量改为局部变量 Modify by yunpeng_zhang on 2015.08.18

    public static  DbUtilUpLoadDayStartExcel getInstance ()
    {
        return instGroup;
    }

    UpLoadExcelManager                             uploadExcelManager     = UpLoadExcelManager.getInstance();
    UpLoadTempExcleManager                         upLoadTempExcleManager = UpLoadTempExcleManager.getInstance();

    private static final Logger                    dbsRollbacklog         = Logger.getLogger("dbSRblog");

    private static  final DbUtilUpLoadDayStartExcel instGroup             = new DbUtilUpLoadDayStartExcel();
    
    private static final String MSG1 = "检查Excel版本是否正确及内容是否正确";
    private static final String STATUS = "status";
    private static final String XLSX = ".xlsx";
    private static final String DELETEFLAG = "deleteFlag";
    private static final String MAPLIST = "mapList";
    private static final String MESSAGE = "message";
    private static final String MESSDAYSTART = "messdaystart";
    private static final String NO_MYY = "no_myy";

    /**   
     * @Title: taskUpload   
     * @Description: 主线定时配置列表上传多写层
     * @param fileNew
     * @param file
     * @param testExcelPath
     * @param fileName
     * @param user
     * @param userName
     * @param filePath
     * @return
     * @throws ServerException
     * @throws RepositoryException      
     * @author: yue_sun 
     * @date:   2018年6月21日 下午10:20:34   
     */
    public String taskUpload ( File fileNew, CommonsMultipartFile file,  String fileName,
            UserInfo user, String userName, String filePath ) throws ServerException, RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String messages = "";

        // 创建保存工具类对象
        ProjectSaveUtilBean projectSaveUtilBean = new ProjectSaveUtilBean();
        // 获取所有可用数据源
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                .getDBsourceList(Constants.IEAI_IEAI_BASIC);
        Connection connection = null;
        int basicType = -1;
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            connection = DBResource.getConnection(method, log, (int) dBSourceMonitor.getGroupId());
            if (dBSourceMonitor.getBasic() == 1)
            {
                basicType = (int) dBSourceMonitor.getGroupId();
                // 保存基线库数据库连接至工具类对象
                projectSaveUtilBean.setBasicConnection(connection);
                projectSaveUtilBean.setBasicConnectionName(dBSourceMonitor.getDbsourceName());
            } else
            {
                // 保存除基线库数据库连接集合至工具类对象
                projectSaveUtilBean.getConnectionMap().put(dBSourceMonitor.getDbsourceName(), connection);
            }
        }

        try
        {
            Connection conn1 = null;

            conn1 = DBResource.getSchedulerConnection(method, log, Constants.IEAI_IEAI_BASIC);
            try
            {
                PreparedStatement ps = null;
                PreparedStatement ps2 = null;
                try
                {
                    projectSaveUtilBean.setFileName(fileName);
                    messages = conlockTable(fileNew.getAbsolutePath() + File.separator + fileName, user,
                        userName, filePath, projectSaveUtilBean, conn1, basicType);
                    log.info(file.getOriginalFilename() + "【Excel导入】结果：" + messages);

                } catch (Exception e)
                {
                    log.error("conlockTable method of DbUtilUpLoadExcel.class SQLException:" + e.getMessage());
                    log.error("conlockTable method of DbUtilUpLoadExcel.class：多写获取数据源失败");
                    DBResource.rollback(conn1, Constants.IEAI_IEAI_BASIC, e, method, log);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePreparedStatement(ps, method, log);
                    DBResource.closePreparedStatement(ps2, method, log);
                    DBResource.closeConnection(conn1, method, log);
                }
            } catch (RepositoryException ex)
            {
                log.error("conlockTable method of DbUtilUpLoadExcel.class RepositoryException:" + ex.getMessage());
                DBResource.throwRepositoryException(ex, ServerError.ERR_DB_QUERY);
            }
        } catch (Exception e)
        {
            if (null != projectSaveUtilBean.getBasicConnection())
            {
                try
                {
                    projectSaveUtilBean.getBasicConnection().rollback();
                } catch (SQLException e1)
                {
                    log.error("conlockTable method of DbUtilUpLoadExcel.class RepositoryException:" + e.getMessage());
                }
            }
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            // 关闭基线库连接
            DBResource.closeConnection(projectSaveUtilBean.getBasicConnection(), method, log);
            // 循环关闭数据库连接
            for (Iterator it = projectSaveUtilBean.getConnectionMap().keySet().iterator(); it.hasNext();)
            {
                DBResource.closeConnection(projectSaveUtilBean.getConnectionMap().get(it.next()), method, log);
            }
            DBResource.closeConnection(connection, method, log);
        }
        return messages;
    }

    /**
     * <AUTHOR>
     * @des:导入excel方法
     * @datea:2014-4-2
     * @param uploadFile
     * @param fileName
     * @param user
     * @throws ServerException
     * @throws RepositoryException
     * 
     *             Update by li_yang The project name is not from the excel file name, it will be
     *             got from the excel file content, second row.
     * @变更记录 Modify by yunpeng_zhang 2015.08.04 为支持压缩包和xlsx格式上传，代码部分功能有所调整
     */
    public String conlockTable ( String testExcelPath,  UserInfo user, String userName, String filePath,
            ProjectSaveUtilBean projectSaveUtilBean, Connection conn1, int basicType )
            throws ServerException, RepositoryException
    {
        String fileName = projectSaveUtilBean.getFileName();
        String messages = "";
        String fileNameFinal = fileName;
        log.info(fileNameFinal + "【Excel导入】conlockTable开始！");

        Map<String, Integer> eitRowCount = new HashMap<String, Integer>();// 执行结果影响条数

        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        try
        {
            if (!fileNameFinal.toLowerCase().endsWith(".xls") && !fileNameFinal.toLowerCase().endsWith(XLSX) && !fileNameFinal.toLowerCase().endsWith(".et"))
            {
                messages = "压缩包中文件\"" + fileNameFinal + "\"不是合法的excel文件";
                return messages;
            }

            projectSaveUtilBean.setFileName(fileName);
            String sql2 = "update IEAI_RECORD set ROCKNAME='name' where projectName=?";
            ps = conn1.prepareStatement(sql2);
            ps.setString(1, "日启动配置锁标识");
            ps.executeUpdate();
            long aa = System.currentTimeMillis();
            // 增加对xlsx格式的兼容 add by yunpeng_zhang on 2015.08.04
            // 允许不同工程同时上传，将静态变量改为局部变量 Modify by yunpeng_zhang on
            // 2015.08.18
            Map returnDayMap = this.getDayActInfoCompatible(testExcelPath);
            String mess = (String) returnDayMap.get("mess");
            if (StringUtils.isNotBlank(mess) )
            {
                return mess;
            }
            Map<String, DayStartBean> mapListDayStartBean = (Map<String, DayStartBean>) returnDayMap.get(MAPLIST);
            
            projectSaveUtilBean.getDayStartMapList().add(mapListDayStartBean);
            projectSaveUtilBean.setFilePath(filePath);
            projectSaveUtilBean.setUserName(userName);
            projectSaveUtilBean.setUserId(user.getId());
            projectSaveUtilBean.setTestExcelPath(testExcelPath);
            projectSaveUtilBean.setFileNameFinal(fileNameFinal);
            projectSaveUtilBean.setUser(user);

            Map mapListmainLinePreSuccBean = null;
            Map<String, Map<String, MainLinePreSuccBean>> listmainLinePreSuccBean = new HashMap<String, Map<String, MainLinePreSuccBean>>();
            Map<String, String> mapProject = new HashMap<String, String>();
            String validateDayStartMessges = validateExcelModelDayStart(mapListDayStartBean,
                projectSaveUtilBean.getBasicConnection());
            if (!"".equals(validateDayStartMessges))
            {
                return validateDayStartMessges;
            }

            ActInfoBean actInfoBean = new ActInfoBean();
            for (Map.Entry<String, DayStartBean> entry : mapListDayStartBean.entrySet())
            {
                DayStartBean dayStartBean = entry.getValue();

                putactInfoBean( actInfoBean, dayStartBean);
                mapProject.put(dayStartBean.getChildProName(), dayStartBean.getChildProName());
                if (!"无".equals(dayStartBean.getMainLinePosition()))
                {
                    mapListmainLinePreSuccBean = this.getMainLinePreSuccCompatible(testExcelPath, 
                        dayStartBean);
                    String messdaystart = (String) mapListmainLinePreSuccBean.get(MESSDAYSTART);
                    if (StringUtils.isNotBlank(messdaystart))
                    {
                        return messdaystart;
                    }
                    putlistmainLinePreSuccBean ( dayStartBean, mapListmainLinePreSuccBean,listmainLinePreSuccBean);
                }
            }
            String validateProPreMessges = validatePreProExcel(listmainLinePreSuccBean);
            if (!"".equals(validateProPreMessges))
            {
                return validateProPreMessges;
            } else
            {
                upLoadTempExcleManager.saveExcelModelDayStart(mapListDayStartBean, 
                    projectSaveUtilBean.getBasicConnection(), basicType);
                upLoadTempExcleManager.saveExcelModelPrePro(listmainLinePreSuccBean, 
                    projectSaveUtilBean.getBasicConnection(), basicType);
            }
            log.info("Excel导入功能耗时流水：execl数据加载：" + (System.currentTimeMillis() - aa) + " 毫秒");
            // 删除临时表中的数据
            long ab = System.currentTimeMillis();
            boolean flag = true;
            flag = saveDayStartExcel( user,
                 projectSaveUtilBean,   basicType, mapProject );

            /** add by zhangjunyu Excel 文件信息保存 */
            File file = new File(testExcelPath);
            boolean isOk = uploadExcelManager.saveExcelModelRecord(projectSaveUtilBean.getBasicConnection(),
                actInfoBean, fileNameFinal, userName, file, basicType);
            if (!isOk)
            {
                log.info("saveExcelModelRecord: 日启动上传excel文件保存到表IEAI_EXCLEMODEL_RECORD失败");
                messages = "组织工程信息入库,Excel 文件信息保存失败,请重新导入。";
                return messages;
            }

            if (!flag)
            {
                messages = "组织工程信息入库失败,请重新导入。";
                return messages;
            }
            // 工程信息组织入库完毕 一起提交

            // 保存非基线库的数据，如果上方报错，则此处不进行保存，直接回滚
            this.conlockTableDb( projectSaveUtilBean,  basicType , eitRowCount);

            long end = System.currentTimeMillis();
            log.info("Excel导入功能组织工程入库：Excel导入成功组织工程耗时为：" + (end - ab) + " 毫秒");
            messages = "导入成功";
            conn1.commit();
            long endTime = System.currentTimeMillis();
            log.info("***************上传结束*****************共用时：" + (endTime - aa) + "毫秒");
        } catch (Exception e)
        {
            DBResource.rollback(conn1, basicType, e, "conlockTable", log);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePreparedStatement(ps, "lockTable", log);
            DBResource.closePreparedStatement(ps2, "conlockTable", log);
        }

        log.info(fileNameFinal + "【Excel导入】conlockTable结束！");
        return messages;

    }

    public void putactInfoBean(ActInfoBean actInfoBean,DayStartBean dayStartBean){
        if (StringUtils.isNotBlank(  dayStartBean.getChildProName()) )
        {
            actInfoBean.setProjectName(dayStartBean.getChildProName());
        }
    }
    public void putlistmainLinePreSuccBean (DayStartBean dayStartBean,Map mapListmainLinePreSuccBean,Map<String, Map<String, MainLinePreSuccBean>> listmainLinePreSuccBean)
    {
        if ( !(mapListmainLinePreSuccBean.isEmpty()) )
        {
            listmainLinePreSuccBean.put(dayStartBean.getMainLinePosition(), mapListmainLinePreSuccBean);
        } 
    } 
    public boolean saveDayStartExcel(UserInfo user,
            ProjectSaveUtilBean projectSaveUtilBean,  int basicType,Map<String, String> mapProject ) throws RepositoryException, ServerException, SQLException{
        boolean flag = true ;
        for (Map.Entry<String, String> entryP : mapProject.entrySet())
        {
            List listProjectName = new ArrayList();
            listProjectName.add(entryP.getValue());
            flag = ExcelActUtil.getInstance().saveDayStartExcel(listProjectName, user, projectSaveUtilBean,
                basicType);
            EngineRepositotyJdbc.getInstance().updateDayStartSuccAndPreOneConnection(
                projectSaveUtilBean.getBasicConnection(), entryP.getValue());
        }
        return flag ;
    }
    public void conlockTableDb(ProjectSaveUtilBean projectSaveUtilBean, int basicType ,Map<String, Integer> eitRowCount) throws RepositoryException{
     // 保存非基线库的数据，如果上方报错，则此处不进行保存，直接回滚
        for (Iterator it = projectSaveUtilBean.getConnectionMap().keySet().iterator(); it.hasNext();)
        {
            String dBkey = it.next().toString();
            this.savePrjForTeam(projectSaveUtilBean.getConnectionMap().get(dBkey), projectSaveUtilBean, eitRowCount,
                dBkey, basicType);
        }

        // 是否回滚
        boolean isrollback = false;
        // 循环提交数据库连接
        for (Iterator it = projectSaveUtilBean.getConnectionMap().keySet().iterator(); it.hasNext();)
        {

            Object key = it.next();
            isrollback = this.commitOrRollbackConn(projectSaveUtilBean.getConnectionMap().get(key), isrollback);
            if (!isrollback )
            {
                projectSaveUtilBean.getComitConnectionMap().put(key.toString(),
                    projectSaveUtilBean.getConnectionMap().get(key));
            }

        }
        // 提交基线库连接
        isrollback = this.commitOrRollbackConn(projectSaveUtilBean.getBasicConnection(), isrollback);
        if (!isrollback )
        {
            projectSaveUtilBean.getComitConnectionMap().put(projectSaveUtilBean.getBasicConnectionName(),
                projectSaveUtilBean.getBasicConnection());
        }

        // 执行回滚方案
        if (isrollback)
        {
            for (Iterator it = projectSaveUtilBean.getComitConnectionMap().keySet().iterator(); it.hasNext();)
            {

                Object key = it.next();
                // 调用回滚方法连接
                this.rollbackPrjForTeam(projectSaveUtilBean.getComitConnectionMap().get(key), key.toString(),
                    projectSaveUtilBean, Constants.PRJ);
            }
            log.error("Excel工程上传多写失败！");

            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        }
    }

    


    /**
     * 验证日启动Excel数据合法性
     * 
     * @param mapList
     * @throws SQLException
     * @throws ServerException
     */
    public static String validatePreProExcel ( Map<String, Map<String, MainLinePreSuccBean>> mapList)
           
    {
        String messages = "";
        String mainLine = "";
        Map<String, String> mapMainLine = new HashMap<String, String>();
        boolean flag = true;
        String flowName = "";
        if (mapList.isEmpty())
        {
            flag = false;
        } else
        {
            for (Map.Entry<String, Map<String, MainLinePreSuccBean>> entry : mapList.entrySet())
            {
                Map<String, MainLinePreSuccBean> aif = entry.getValue();
                if (!(StringUtils.isEmpty( entry.getKey())  || "null".equals(entry.getKey())))
                {

                    Map<String, MainLinePreSuccBean> listM = (Map<String, MainLinePreSuccBean>) aif.get(MAPLIST);
                    for (Map.Entry<String, MainLinePreSuccBean> entryP : listM.entrySet())
                    {
                        MainLinePreSuccBean mainLineb = entryP.getValue();
                        mainLine = mapMainLine.get(mainLineb.getMainLineName());
                        if (StringUtils.isNotEmpty( mainLine))
                        {
                            messages = mainLineb.getFlowName() + "(sheet)页中的主线名称不能相同，操作序号为—— "
                                    + mainLineb.getMainLineNo();
                            return messages;
                        }
                        flowName = mainLineb.getFlowName();
                        if ("封装头".equals(mainLineb.getSEFlag()))
                        {
                            flag = false;
                        }
                        // 取出所有前继
                        List<String> listBefore = mainLineb.getBeforeActList();
                        // 取出所有后继
                        List<String> listAfter = mainLineb.getAfterActList();
                        if (listBefore != null)
                        {
                            for (int j = 0; j < listBefore.size(); j++)
                            {
                                // 如果有多个前继从第一个开始
                                String before = listBefore.get(j);
                                for (int k = 0; k < listAfter.size(); k++)
                                {
                                    String ater = listAfter.get(k);
                                    if (ater.equals(before))
                                    {
                                        messages = "前后继相同，操作序号为—— " + mainLineb.getMainLineNo();
                                        return messages;
                                    }
                                }
                                // 取得对应前继的信息
                                MainLinePreSuccBean actInforBean = listM.get(before);
                                if (null == actInforBean)
                                {
                                    messages = "操作步骤序号不存在依赖，操作序号为—— " + before;
                                    return messages;
                                } else
                                {
                                    boolean fl = false;
                                    List<String> list = actInforBean.getAfterActList();
                                    if (list.isEmpty())
                                    {
                                        list.add(mainLineb.getMainLineNo());
                                    } else
                                    {
                                        for (int k = 0; k < list.size(); k++)
                                        {
                                            String after = list.get(k);
                                            if (after.equals(mainLineb.getMainLineNo()))
                                            {
                                                fl = true;
                                                break;
                                            }
                                        }
                                        for (int b = 0; b < listBefore.size(); b++)
                                        {
                                            String bef = listBefore.get(b);
                                            if (bef.equals(mainLineb.getMainLineNo()))
                                            {
                                                messages = "前后继相同 ，操作序号为—— " + mainLineb.getMainLineNo();
                                                return messages;
                                            }
                                        }
                                        if (!fl)
                                        {
                                            list.add(mainLineb.getMainLineNo());
                                        }
                                    }
                                    actInforBean.setAfterActList(list);
                                    if ("封装头".equals(actInforBean.getSEFlag()) && !list.isEmpty())
                                    {
                                            messages = "封装流程中的封装头活动不能有依赖，操作序号为—— " + actInforBean.getMainLineNo();
                                            return messages;
                                    }
                                    if (list.size() > 1)
                                    {
                                        messages = "封装流程中的活动的依赖不能大于一个，操作序号为—— " + actInforBean.getMainLineNo();
                                        return messages;
                                    }
                                    listM.put(actInforBean.getMainLineNo(), actInforBean);
                                }
                            }

                            // 自动补全前继关系
                            if (listAfter != null)
                            {
                                for (int j = 0; j < listAfter.size(); j++)
                                {
                                    String after = listAfter.get(j);
                                    MainLinePreSuccBean actInforBean = listM.get(after);
                                    if (null == actInforBean)
                                    {
                                        messages = "操作步骤序号不存在触发，操作序号为—— " + after;
                                        return messages;
                                    } else
                                    {
                                        boolean fl = false;
                                        List<String> list = actInforBean.getBeforeActList();
                                        if (list.isEmpty())
                                        {
                                            list.add(mainLineb.getMainLineNo());
                                        } else
                                        {
                                            for (int k = 0; k < list.size(); k++)
                                            {
                                                String before = list.get(k);
                                                if (before.equals(mainLineb.getMainLineNo()))
                                                {
                                                    fl = true;
                                                    break;
                                                }
                                            }
                                            for (int b = 0; b < listAfter.size(); b++)
                                            {
                                                String bef = listAfter.get(b);
                                                if (bef.equals(mainLineb.getMainLineNo()))
                                                {
                                                    messages = "前后继相同  ，操作序号为—— " + mainLineb.getMainLineNo();
                                                    return messages;
                                                }
                                            }
                                            if (!fl )
                                            {
                                                list.add(mainLineb.getMainLineNo());
                                            }
                                        }
                                        actInforBean.setBeforeActList(list);
                                        listM.put(actInforBean.getMainLineNo(), actInforBean);
                                    }
                                }
                            }
                        }
                        mapMainLine.put(mainLineb.getMainLineName(), mainLineb.getMainLineName());
                    }
                }
            }
        }
        if (flag)
        {
            messages = flowName + "(sheet)页中的至少有一个封装头";
            return messages;
        }
        return messages;
    }

    /**
     * <AUTHOR>
     * @des:保存excel临时表的信息
     * @datea:2016-09-22
     * @param aif
     * @param mapList
     * @throws SQLException
     * @throws RepositoryException
     */
    public String validateExcelModelDayStart ( Map<String, DayStartBean> mapList, Connection conn )
            throws  RepositoryException
    {
        String messages = "";
        String mainline = "";
        String flowName = "";
        Map<String, String> mapMainLine = new HashMap<String, String>();
        Map<String, String> mapFlowname = new HashMap<String, String>();
        for (Map.Entry<String, DayStartBean> entry : mapList.entrySet())
        {
            DayStartBean aif = entry.getValue();
            int count = UpLoadTempExcleManager.getInstance().selectDayStartProjectRun(aif.getChildProName(),
                aif.getFlowName(), conn);
            if (count > 0)
            {
                messages = "工作流正在运行,不允许变更,工程名为—— " + aif.getChildProName();
            }
            mainline = mapMainLine.get(aif.getMainLineName());
            flowName = mapFlowname.get(aif.getFlowName());
            boolean dsExistSameName = nameEqualDS(aif.getChildProName(), conn);

            if (dsExistSameName){
               messages = "时间控制表的子系统名与工程表的工程名称或子系统名相同，上传失败" ;
            }


            if (StringUtils.isNotEmpty( mainline))
            {
                messages = "主线名称不能重复  ，操作序号为—— " + aif.getDayActNo();
            }
            if (StringUtils.isNotEmpty(flowName))
            {
                messages = "工作流名称不能重复  ，操作序号为—— " + aif.getDayActNo();
            }
            messages =  message1( messages, aif);

            if (StringUtils.isEmpty( aif.getMainLineRate()))
            {
                messages = "主线发起频率为必填项 ，操作序号为—— " + aif.getDayActNo();
            }
            if (StringUtils.isEmpty(aif.getMainLineTime()))
            {
                messages = "主线发起时间为必填项 ，操作序号为—— " + aif.getDayActNo();
            } else
            {
                boolean flagtime = isValidatetime(aif.getMainLineTime());
                if (flagtime)
                {
                    messages = "主线发起时间非法 ，发起时间格式必须为00:00，操作序号为—— " + aif.getDayActNo();
                }
            }
            if( Environment.getInstance().getXanxSwitch()){
                if (StringUtils.isEmpty(aif.getIsysname()))
                {
                    messages = "所属系统为必填项 ，操作序号为—— " + aif.getDayActNo();
                }
            }

            messages = messages3( messages, aif);
            mapMainLine.put(aif.getMainLineName(), aif.getMainLineName());
            mapFlowname.put(aif.getFlowName(), aif.getFlowName());
        }
        return messages;
    }

    private boolean ifExitProjectCount(String dayChildProName, String mainLineName, Connection conn) throws RepositoryException {

        boolean ifExitProject = false;
        int count = 0;
        String ichildproname = "";
        HashMap<String, Object> b = new HashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select count(1) as cou from ieai_excelmodel ie where ie.IMAINPRONAME = ? and ie.IMAINLINENAME = ?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, dayChildProName);
            ps.setString(2, mainLineName);

            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("cou");

                if (count!=0){
                    ifExitProject = true;
                }
            }


        } catch (SQLException e)
        {
            log.error("ifExitProjectCount EXEC select count(1) from ieai_excelmodel IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "ifExitProjectCount", log);
        }

        return ifExitProject;
    }

    private static boolean nameEqualDS(String name, Connection conn) throws RepositoryException {

        boolean dsExistSameprjName = false;
        boolean dsExistSamecprjName = false;
        boolean dsExistSameName = false;
        int count1 = 0;
        int count2 = 0;
        String sql1 = "select count(1) as cou from ieai_excelmodel ie where ie.IMAINPRONAME = ?";
        String sql2 = "select count(1) as cou from ieai_excelmodel ie where ie.ICHILDPRONAME = ?";
        try(PreparedStatement ps1  = conn.prepareStatement(sql1);PreparedStatement ps2  = conn.prepareStatement(sql2))
        {
            ps1.setString(1, name);
            try (ResultSet rs1 = ps1.executeQuery();){
                if (rs1.next())
                {
                    count1 = rs1.getInt("cou");
                    if (count1!=0){
                        dsExistSameprjName = true;
                    }
                }
            }
            ps2.setString(1, name);
            try (ResultSet rs2 = ps2.executeQuery();){
                if (rs2.next())
                {
                    count2 = rs2.getInt("cou");
                    if (count2!=0){
                        dsExistSamecprjName = true;
                    }
                }
            }
            if (dsExistSameprjName ||dsExistSamecprjName){
                dsExistSameName = true;
            }
        } catch (SQLException e)
        {
            log.error("nameEqualDS IS ERR" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        }

        return dsExistSameName;
    }

    private String messages3(String messages,DayStartBean aif){


        if (StringUtils.isEmpty(aif.getOffset()))
        {
            messages = "偏移量为必填项 ，操作序号为—— " + aif.getDayActNo();
        }else {
            // 偏移量 T+N、默认效验
            if (!"".equals(aif.getOffset()) && null
                    != aif.getOffset())
            {
                Boolean verificationOffset= true;
                for(String Offset:OFFSET_DICTIONARIES){
                    if (aif.getOffset().equals(Offset)){
                        verificationOffset=false;
                    }
                }
                if(verificationOffset){
                    messages =  "偏移量格式错误!正确格式为T+N、默认！,序号为："+aif.getDayActNo();
                }
            }
        }
        if (StringUtils.isEmpty( aif.getDatetype()))
        {
            messages = "跑批日期类型为必填项 ，操作序号为—— " + aif.getDayActNo();
        } else
        {
            if ("数据日期".equals(aif.getDatetype()))
            {

                if (StringUtils.isEmpty(aif.getDateAgent()))
                {
                    messages = "如果跑批日期类型为数据日期，读取日期的Agent组名为必填项 ，操作序号为—— " + aif.getDayActNo();
                }
                if (StringUtils.isEmpty( aif.getDateFilePositon()))
                {
                    messages = "如果跑批日期类型为数据日期，存储数据日期文件位置为必填项 ，操作序号为—— " + aif.getDayActNo();
                }
            }
        }
        return messages;
    } 
    private String message1(String messages,DayStartBean aif){
        if (StringUtils.isEmpty(aif.getFlag()))
        {
            messages = "操作项为必填项  ，操作序号为—— " + aif.getDayActNo();
        }
        if (StringUtils.isEmpty(aif.getDayChildProName()))
        {
            messages = "依赖关系层子系统名称为必填项  ，操作序号为—— " + aif.getDayActNo();
        }

        if (StringUtils.isEmpty( aif.getFlowName()))
        {
            messages = "工作流名称为必填项  ，操作序号为—— " + aif.getDayActNo();
        }
        if (StringUtils.isEmpty( aif.getChildProName()))
        {
            messages = "子系统名称为必填项  ，操作序号为—— " + aif.getDayActNo();
        }
        if (StringUtils.isEmpty( aif.getIsMainLine()))
        {
            messages = "是否使用主线封装流程为必填项  ，操作序号为—— " + aif.getDayActNo();
        } else
        {
            messages = messages2( messages, aif);
        }
        return messages;
    }
    
    private String messages2(String messages,DayStartBean aif){


        if ("否".equals(aif.getIsMainLine()))
        {
            if ("".equals(aif.getMainLinePosition()) || null == aif.getMainLinePosition()
                    || "无".equals(aif.getMainLinePosition()))
            {

                if ("".equals(aif.getMainLineName()) || null == aif.getMainLineName())
                {
                    messages = "如果是否使用主线封装流程为否，主线名称必填 ，操作序号为—— " + aif.getDayActNo();
                }
            } else
            {
                messages = "主线间依赖触发引用位置（sheet）为空或无 ，操作序号为—— " + aif.getDayActNo();
            }

        } else
        {
            if (StringUtils.isEmpty( aif.getMainLinePosition())
                    || "无".equals(aif.getMainLinePosition()))
            {
                messages = "主线间依赖触发引用位置（sheet）为必填项 ，操作序号为—— " + aif.getDayActNo();
            } else
            {
                if (StringUtils.isNotEmpty( aif.getMainLineName()))
                {
                    messages = "如果是否使用主线封装流程为是，主线名称必须为空 ，操作序号为—— " + aif.getDayActNo();
                }
            }

        }
        return messages;
    } 
    /**
     * public static Map<String, Object> getProjectName ( String fileName ){ Map<String, Object> res
     * = new HashMap<String, Object>(); String messge = ""; XLSFunctions xslf = new XLSFunctions();
     * Sheet sheet = null; try { sheet = xslf.getSheet(fileName); } catch (Exception ex) { messge =
     * ex.getMessage(); if (messge == null) { messge = "检查excel版本是否正确及内容是否正确"; } res.put("status",
     * false); res.put("message", messge); return res; }
     * 
     * if (sheet == null) { messge = "检查excel版本是否正确及内容是否正确"; res.put("status", false);
     * res.put("message", messge); return res; } String projectNameTip = xslf.readCell(sheet, 1, 0);
     * if("工程名称".equals(projectNameTip)){ String projectName = xslf.readCell(sheet, 1, 1);
     * res.put("status", true); res.put("projectName", projectName); return res; }else{ messge =
     * "请在Excel模板的第二行正确填写工程名称"; res.put("status", false); res.put("message", messge); return res; }
     * }
     **/
    /**
     * 导入Excel数据
     * 
     * @param fileName
     * @param projectName
     */
    /**
     * public static Map<String, ActInfoBean> getActInfo ( String fileName, String projectName,
     * UpLoadExcelManager uploadExcelManager,Connection conn ) { Map<String, ActInfoBean> mapList =
     * new HashMap<String, ActInfoBean>(); ActInfoBean avalidate = new ActInfoBean(); XLSFunctions
     * xslf = new XLSFunctions(); String messge = ""; int titlerow = -1; int totalrow = 0; Sheet
     * sheet = null; // 各标题列号 int ActNoCol = -1; // 活动序号列 int ActNameCol = -1;// 活动名称列 int ActDscCol
     * = -1;// 活动描述列 int ProjectCol = -1;// 子系统列 int BeforeCol = -1;// 依赖作业列 int AfterCol = -1;//
     * 触发作业列 int MainlineCol = -1;// 主线名称列 // int FlowCol = -1;// int SEFlagCol = -1;// 头尾作业标记列 int
     * OKFileABPathCol = -1;// ok文件绝对路径列 int OKFileFindWeek = -1;// ok文件检测周期 int InputPamCol = -1;//
     * 输入参数列 int OutputPamCol = -1;// 输出参数列 int DeleteFlagCol = -1;// 是否删除列 int AgentCol = -1;//
     * agent资源组列 int CheckAgentGroup = -1;// Agent文件检测资源组 // int ShellCrustCol = -1;// 脚本外壳列 int
     * ShellABPathCol = -1;// 脚本绝对路径列 // int LastLineCol = -1;// 成功最后一行列 // List<String[]> OtherCol
     * = null;
     * 
     * int SystemCol=-1; //所属系统类型 int RedoCol =-1; //业务异常自动重试 int weightCol = -1; //权重 int
     * priorityCol = -1; //优先级
     * 
     * //APT参数 int aptGroupName=-1;//APT组名称 int aptFileName=-1;//APT文件名称 int isDB2=-1;//是否是DB2 int
     * db2IP=-1;//DB2IP int aptResGroupname=-1; //apt资源组名称 //APT参数
     * 
     * try { sheet = xslf.getSheet(fileName); } catch (Exception ex) { messge = ex.getMessage(); if
     * (messge == null) { avalidate.setActName("检查excel版本是否正确及内容是否正确"); mapList.put("no_myy",
     * avalidate); return mapList; } }
     * 
     * if (sheet == null) { avalidate.setActName("检查excel版本是否正确及内容是否正确"); mapList.put("no_myy",
     * avalidate); return mapList; } titlerow = xslf.getCellRow(sheet, "操作步骤序号"); if (titlerow ==
     * 101) { avalidate.setActName("操作步骤序号"); mapList.put("no_my", avalidate); return mapList; }
     * totalrow = xslf.getDataRows(sheet, titlerow);
     * 
     * // 获取列号，按照模板顺序获取 ActNoCol = xslf.getCellCol(sheet, "操作步骤序号"); if (ActNoCol == -100) {
     * avalidate.setActName("操作步骤序号"); mapList.put("no_my", avalidate); return mapList; }
     * MainlineCol = xslf.getCellCol(sheet, "主线名称"); if (MainlineCol == -100) {
     * avalidate.setActName("主线名称"); mapList.put("no_my", avalidate); return mapList; } SEFlagCol =
     * xslf.getCellCol(sheet, "头尾标记"); if (SEFlagCol == -100) { avalidate.setActName("头尾标记");
     * mapList.put("no_my", avalidate); return mapList; } AgentCol = xslf.getCellCol(sheet,
     * "Agent资源组"); if (AgentCol == -100) { avalidate.setActName("Agent资源组"); mapList.put("no_my",
     * avalidate); return mapList; } CheckAgentGroup = xslf.getCellCol(sheet, "Agent文件检测资源组"); if
     * (CheckAgentGroup == -100) { avalidate.setActName("Agent文件检测资源组"); mapList.put("no_my",
     * avalidate); return mapList; } OKFileABPathCol = xslf.getCellCol(sheet, "OK文件绝对路径"); if
     * (OKFileABPathCol == -100) { avalidate.setActName("OK文件绝对路径"); mapList.put("no_my",
     * avalidate); return mapList; } OKFileFindWeek = xslf.getCellCol(sheet, "OK文件检测周期"); if
     * (OKFileFindWeek == -100) { avalidate.setActName("OK文件检测周期"); mapList.put("no_my", avalidate);
     * return mapList; } BeforeCol = xslf.getCellCol(sheet, "依赖作业"); if (BeforeCol == -100) {
     * avalidate.setActName("依赖作业"); mapList.put("no_my", avalidate); return mapList; } //
     * InputPamCol = xslf.getCellCol(sheet, "输入参数"); ProjectCol = xslf.getCellCol(sheet, "子系统名称");
     * if (ProjectCol == -100) { avalidate.setActName("子系统名称"); mapList.put("no_my", avalidate);
     * return mapList; } DeleteFlagCol = xslf.getCellCol(sheet, "是否删除"); if (DeleteFlagCol == -100)
     * { avalidate.setActName("是否删除"); mapList.put("no_my", avalidate); return mapList; } ActNameCol
     * = xslf.getCellCol(sheet, "作业名称"); if (ActNameCol == -100) { avalidate.setActName("作业名称");
     * mapList.put("no_my", avalidate); return mapList; } ActDscCol = xslf.getCellCol(sheet,
     * "作业描述"); if (ActDscCol == -100) { avalidate.setActName("作业描述"); mapList.put("no_my",
     * avalidate); return mapList; } OutputPamCol = xslf.getCellCol(sheet, "输出参数"); if (OutputPamCol
     * == -100) { avalidate.setActName("输出参数"); mapList.put("no_my", avalidate); return mapList; }
     * // FlowCol = xslf.getCellCol(sheet, ""); AfterCol = xslf.getCellCol(sheet, "触发作业"); if
     * (AfterCol == -100) { avalidate.setActName("触发作业"); mapList.put("no_my", avalidate); return
     * mapList; } // ShellCrustCol = xslf.getCellCol(sheet, "脚本外壳"); ShellABPathCol =
     * xslf.getCellCol(sheet, "脚本名称"); if (ShellABPathCol == -100) { avalidate.setActName("脚本名称");
     * mapList.put("no_my", avalidate); return mapList; } //fantiejun add SystemCol
     * =xslf.getCellCol(sheet, "所属系统"); if (SystemCol == -100) { avalidate.setActName("所属系统");
     * mapList.put("no_my", avalidate); return mapList; } RedoCol=xslf.getCellCol(sheet,
     * "业务异常自动重试"); if (RedoCol == -100) { avalidate.setActName("业务异常自动重试"); mapList.put("no_my",
     * avalidate); return mapList; } //fantiejun add
     * 
     * weightCol = xslf.getCellCol(sheet, "权重"); if(weightCol==-100){ avalidate.setActName("权重");
     * mapList.put("no_my", avalidate); return mapList; } priorityCol = xslf.getCellCol(sheet,
     * "优先级"); if(priorityCol==-100){ avalidate.setActName("优先级"); mapList.put("no_my", avalidate);
     * return mapList; } //APT参数 aptGroupName = xslf.getCellCol(sheet, "APT组名称"); if (aptGroupName
     * == -100) { avalidate.setActName("APT组名称"); mapList.put("no_my", avalidate); return mapList; }
     * aptFileName = xslf.getCellCol(sheet, "APT文件名称"); if (aptFileName == -100) {
     * avalidate.setActName("APT文件名称"); mapList.put("no_my", avalidate); return mapList; } isDB2 =
     * xslf.getCellCol(sheet, "ISDB2"); if (isDB2 == -100) { avalidate.setActName("ISDB2");
     * mapList.put("no_my", avalidate); return mapList; } db2IP = xslf.getCellCol(sheet, "DB2IP");
     * if (db2IP == -100) { avalidate.setActName("DB2IP"); mapList.put("no_my", avalidate); return
     * mapList; } // aptResGroupname = xslf.getCellCol(sheet, "APT资源组名称"); // if (aptResGroupname ==
     * -100) // { // avalidate.setActName("APT资源组名称"); // mapList.put("no_my", avalidate); // return
     * mapList; // } //APT参数
     * 
     * String shellCrust = xslf.readCell(sheet, 2, 1); //Amended by liyang, Shell info at row3,col2
     * // LastLineCol = xslf.getCellCol(sheet, "脚本执行成功最后一行打印值"); // 获取Excel信息 if (titlerow != -1 &&
     * totalrow != 0) {
     * 
     * for (int i = titlerow + 1; i < totalrow; i++) {
     * 
     * // String firstColCell = xslf.readCell(sheet, i, ActNoCol); // 操作序号为空，则停止检查 //
     * if(firstColCell==null||firstColCell.equals("")) // { // break; // }
     * 
     * ActInfoBean aibtmp = new ActInfoBean(); if (ActNoCol != -1) {
     * aibtmp.setActNo(xslf.readCell(sheet, i, ActNoCol).trim()); } if (MainlineCol != -1) {
     * aibtmp.setMainline(xslf.readCell(sheet, i, MainlineCol).trim()); } if (SEFlagCol != -1) {
     * aibtmp.setSEFlag(xslf.readCell(sheet, i, SEFlagCol).trim()); } if (AgentCol != -1) {
     * aibtmp.setAgentGropName(xslf.readCell(sheet, i, AgentCol).trim()); } if (OKFileABPathCol !=
     * -1) { aibtmp.setOKFileABPath(xslf.readCell(sheet, i, OKFileABPathCol).trim()); } if
     * (BeforeCol != -1) { String beforeActStr = xslf.readCell(sheet, i, BeforeCol).trim(); //
     * aibtmp.setBeforeAct(beforeActStr); aibtmp.setBeforeActList(changeStringtoList(beforeActStr,
     * ",")); } if (InputPamCol != -1) { aibtmp.setInputParameter(xslf.readCell(sheet, i,
     * InputPamCol).trim()); } if (ProjectCol != -1) {
     * aibtmp.setChildProjectName(xslf.readCell(sheet, i, ProjectCol).trim()); } if (DeleteFlagCol
     * != -1) { String delFlag = xslf.readCell(sheet, i, DeleteFlagCol).trim(); if
     * (delFlag.equals("是")) { aibtmp.setDeleteFlag("2"); deleteFlag = "2"; } else if
     * (delFlag.equals("否")) { aibtmp.setDeleteFlag("1"); } else { aibtmp.setDeleteFlag(delFlag); }
     * } if (ActNameCol != -1) { aibtmp.setActName(xslf.readCell(sheet, i, ActNameCol).trim()); } if
     * (ActDscCol != -1) { aibtmp.setDiscribe(xslf.readCell(sheet, i, ActDscCol).trim()); } if
     * (OutputPamCol != -1) { aibtmp.setOutputPamaeter(xslf.readCell(sheet, i,
     * OutputPamCol).trim()); } if (AfterCol != -1) { String afterActStr = xslf.readCell(sheet, i,
     * AfterCol).trim(); // aibtmp.setAfterAct(afterActStr);
     * aibtmp.setAfterActList(changeStringtoList(afterActStr, ",")); } if (!shellCrust.equals("")) {
     * aibtmp.setShellCrust(shellCrust.trim()); } if (ShellABPathCol != -1) {
     * aibtmp.setShellABPath(xslf.readCell(sheet, i, ShellABPathCol).trim()); } if (OKFileFindWeek
     * != -1) { aibtmp.setOKFileFindWeek(xslf.readCell(sheet, i, OKFileFindWeek).trim()); } if
     * (CheckAgentGroup != -1) { aibtmp.setCheckAgentGropName(xslf.readCell(sheet, i,
     * CheckAgentGroup).trim()); } if(SystemCol!= -1) { aibtmp.setSystem(xslf.readCell(sheet, i,
     * SystemCol).trim()); } if(RedoCol!= -1) { aibtmp.setRedo(xslf.readCell(sheet, i,
     * RedoCol).trim()); } int[] wp = null; try { wp =
     * uploadExcelManager.getPrevWeightAndPriority(projectName, aibtmp.getChildProjectName(),
     * aibtmp.getActName(), conn); } catch (ServerException e) { //  Auto-generated catch block
     * e.printStackTrace(); } if(weightCol!= -1){ String ww = xslf.readCell(sheet, i,
     * weightCol).trim(); if(null==ww || "".equals(ww)){ if(null==wp){ ww = "1"; }else{ ww =
     * String.valueOf(wp[0]); } } aibtmp.setWeights(ww); } if(priorityCol!= -1){ String pp =
     * xslf.readCell(sheet, i, priorityCol).trim(); if(null==pp || "".equals(pp)){ if(null==wp){ pp
     * = "1"; }else{ pp = String.valueOf(wp[1]); } } aibtmp.setPriority(pp); } //APT if
     * (aptGroupName != -1) { aibtmp.setAptGroupName(xslf.readCell(sheet, i, aptGroupName).trim());
     * } if (aptFileName != -1) { aibtmp.setAptFileName(xslf.readCell(sheet, i,
     * aptFileName).trim()); }
     * 
     * if (isDB2 != -1) { aibtmp.setIsDB2(xslf.readCell(sheet, i, isDB2).trim()); } if (db2IP != -1)
     * { aibtmp.setDb2IP(xslf.readCell(sheet, i, db2IP).trim()); } if (aptResGroupname != -1) {
     * aibtmp.setAptResGroupname(xslf.readCell(sheet, i, aptResGroupname).trim()); } //APT if (null
     * != projectName || !"".equals(projectName)) { aibtmp.setProjectName(projectName); } if
     * (!aibtmp.getActNo().equals("")) { ActInfoBean rep = mapList.get(aibtmp.getActNo()); if (rep
     * != null) { mess = "操作序号重复---" + aibtmp.getActNo(); } } if (aibtmp.getActNo().equals("") &&
     * aibtmp.getChildProjectName().equals("") && aibtmp.getActName().equals("") &&
     * aibtmp.getDiscribe().equals("") && aibtmp.getBeforeActList().size() == 0 &&
     * aibtmp.getAfterActList().size() == 0 && aibtmp.getOKFileABPath().equals("") &&
     * aibtmp.getOKFileFindWeek().equals("") && aibtmp.getShellABPath().equals("") &&
     * aibtmp.getOutputPamaeter().equals("") && aibtmp.getAgentGropName().equals("") &&
     * aibtmp.getMainline().equals("") && aibtmp.getSEFlag().equals("")) {
     * 
     * } else { mapList.put(xslf.readCell(sheet, i, ActNoCol), aibtmp); } } } return mapList;
     * 
     * }
     **/
    

    /**
     * 将以指定分隔符切割的字符串转换为无重复值的List
     * 
     * @param str
     * @param delimiter 分隔符
     * @return
     */
    private static List<String> changeStringtoList ( String str, String delimiter )
    {
        List<String> resaultList = new ArrayList<String>();
        if (str != null && (!str.equals("")))
        {
            String[] strlist = str.split(delimiter);
            HashSet<String> stringset = new HashSet<String>();
            for (int i = 0; i < strlist.length; i++)
            {
                stringset.add(strlist[i]);
            }
            Iterator<String> in = stringset.iterator();
            while (in.hasNext())
            {
                resaultList.add(in.next());
            }
        }
        return resaultList;
    }

    public static boolean isInteger ( String value )
    {
        try
        {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e)
        {
            return false;
        }
    }

    public static void deleteFileName ( File file, String fileName )
    {
        if (file.isFile() && file.exists() && file.getName().equals(fileName))
        {
            boolean s = file.delete();
                if(s){
                    dbsRollbacklog.info("文件删除成功");
                }

        }
    }

    /** 标准模板规范列名集合 **/
    private static final  String[] CELL_STR           = { "操作步骤序号", "所属系统", "子系统名称", "作业名称", "作业描述", "触发作业", "依赖作业",
            "是否删除", "OK文件绝对路径", "OK文件检测周期", "脚本名称", "输出参数", "业务异常自动重试", "Agent资源组", "Agent文件检测资源组", "优先级", "权重",
            "APT组名称", "APT文件名称", "ISDB2", "DB2IP", "主线名称", "头尾标记", "是否禁用" };
    /** 标准日启动模板规范列名集合 **/
    private static final  String[] CELL_STRDAY        = { "序号", "操作", "子系统名称", "工作流名称", "依赖关系层子系统名称", "是否使用主线封装流程",
            "主线间依赖触发引用位置（sheet）", "直接依赖主线", "主线名称", "直接触发主线", "翻牌主线名称", "调用方式", "主线发起频率", "主线发起时间", "偏移量", "引用函数组",
            "跑批日期类型", "读取日期的Agent组名", "存储数据日期文件位置"  };

    /** 标准日启动模板主线依赖触发规范列名集合 **/
    private static final  String[] CELL_STRDAYPRESUCC = { "序号", "主线名称", "封装头尾标记", "依赖主线", "触发主线", "封装头尾标记" };
    /** 偏移量效验字典**/
    private static final  String[] OFFSET_DICTIONARIES = { "默认", "T+0", "T+1", "T+2", "T+3", "T+4", "T+5", "T+6", "T+7", "T+8", "T+9" , "T+10","T+11", "T+12",
            "T+13", "T+14", "T+15", "T+16", "T+17", "T+18", "T+19", "T+20", "T+21", "T+22", "T+23", "T+24", "T+25", "T+26", "T+27", "T+28", "T+29", "T+30" , "T+31"  };
    /**
     * 
     * 
     * @Title: getProjectNameCompatible
     * @Description: ( 通过文件完整路径获取工程名(兼容xls、xlsx格式))
     * @param fileName
     * @return
     * @return Map<String,Object> 返回类型
     * @throws
     * @变更记录 2015-8-4 yunpeng_zhang
     */
    public Map<String, Object> getProjectNameCompatible ( String fileName )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        String messge = "";

        Workbook wb = null;
        InputStream stream = null;
        Sheet sheet1 = null;
        try
        {
            stream = new FileInputStream(new File(fileName));

            // Excel2003
            if (fileName.toLowerCase().endsWith(".xls"))
            {
                wb = new HSSFWorkbook(stream);
            }
            // Excel2007
            else if (fileName.toLowerCase().endsWith(XLSX))
            {
                wb = new XSSFWorkbook(stream);
            } else
            {
                messge = "请使用规范的Excel模板文件";
                res.put(STATUS, false);
                res.put(MESSAGE, messge);
                return res;
            }
            sheet1 = wb.getSheetAt(0);
            if (sheet1 == null)
            {
                messge = MSG1;
                res.put(STATUS, false);
                res.put(MESSAGE, messge);
                return res;
            }
            Row row = sheet1.getRow(1);
            if (row != null)
            {
                Cell col = row.getCell((short) 0);

                if ("工程名称".equals(getStringCellValue(col)))
                {
                    Cell col1 = row.getCell((short) 1);
                    String projectName = getStringCellValue(col1);
                    res.put(STATUS, true);
                    res.put("projectName", projectName);
                    return res;
                } else
                {
                    messge = "请在Excel模板的第二行正确填写工程名称";
                    res.put(STATUS, false);
                    res.put(MESSAGE, messge);
                    return res;
                }
            } else
            {
                messge = "请在Excel模板的第二行正确填写工程名称";
                res.put(STATUS, false);
                res.put(MESSAGE, messge);
                return res;
            }
        } catch (Exception ex)
        {
            messge = ex.getMessage();
            if (messge == null)
            {
                messge = MSG1;
            }
            res.put(STATUS, false);
            res.put(MESSAGE, messge);
            return res;
        } finally
        {
            if (null != stream)
            {
                try
                {
                    stream.close();
                } catch (Exception e)
                {
                    stream = null;
                }
            }
        }

    }

    /**
     * 
     * @Title: getActInfoCompatible
     * @Description: (获取Excel模板中的数据数据(兼容xls、xlsx格式))
     * @param fileName
     * @param projectName
     * @param uploadExcelManager
     * @param conn
     * @return
     * @return Map<String,ActInfoBean> 返回类型
     * @throws
     * @变更记录 2015-8-4 yunpeng_zhang
     */
    public Map getActInfoCompatible ( String fileName, String projectName, UpLoadExcelManager uploadExcelManager,
            Connection conn, String userName )
    {
        Map returnMap = new HashMap();
        String deleteFlaginside = "";
        String messinside = "";
        Map<String, ActInfoBean> mapList = new HashMap<String, ActInfoBean>();
        ActInfoBean avalidate = new ActInfoBean();

        Workbook wb = null;
        InputStream stream = null;
        Sheet sheet1 = null;
        String messge = "";
        String shellCrustTemp = "";
        Map cellNumMap = new HashMap();// 上传模板中的所有列名Map
        try
        {
            stream = new FileInputStream(new File(fileName));

            // Excel2003
            if (fileName.toLowerCase().endsWith(".xls"))
            {
                wb = new HSSFWorkbook(stream);
            }
            // Excel2007
            else if (fileName.toLowerCase().endsWith(XLSX))
            {
                wb = new XSSFWorkbook(stream);
            } else
            {
                avalidate.setActName(MSG1);
                mapList.put(NO_MYY, avalidate);
                returnMap.put(MAPLIST, mapList);
                returnMap.put(DELETEFLAG, deleteFlaginside);
                returnMap.put("mess", messinside);
                return returnMap;
            }
            sheet1 = wb.getSheet("作业基本信息");
            if (sheet1 != null)
            {
                int rows = sheet1.getLastRowNum() + 1 - sheet1.getFirstRowNum();
                // 获得脚本外壳
                Row rowTemp = sheet1.getRow(2);
                if (rowTemp != null)
                {
                    Cell colTempCroust = rowTemp.getCell((short) 0);
                    if ("脚本外壳".equals(getStringCellValue(colTempCroust)))
                    {
                        Cell colTemp = rowTemp.getCell((short) 1);
                        shellCrustTemp = getStringCellValue(colTemp);
                    } else
                    {
                        avalidate.setActName("脚本外壳");
                        mapList.put("no_my", avalidate);
                        returnMap.put(MAPLIST, mapList);
                        returnMap.put(DELETEFLAG, deleteFlaginside);
                        returnMap.put("mess", messinside);
                        return returnMap;
                    }

                }

                // 从第四行开始读取
                for (int i = 3; i < rows; i++)
                {

                    Row row = sheet1.getRow(i);
                    ActInfoBean aibtmp = new ActInfoBean();
                    if (row != null)
                    {
                        int cols = row.getLastCellNum(); 

                        for (int j = 0; j < cols; j++)
                        {
                            Cell col = row.getCell((short) j);
                            // 根据Excel第四行的列名判断模板格式是否正确
                            if (i == 3)
                            {
                                if (cols != 24 || null == col)
                                {
                                    avalidate.setActName(MSG1);
                                    mapList.put(NO_MYY, avalidate);
                                    returnMap.put(MAPLIST, mapList);
                                    returnMap.put(DELETEFLAG, deleteFlaginside);
                                    returnMap.put("mess", messinside);
                                    return returnMap;
                                }
                                if (col != null)
                                {
                                    // 获得模板中所有列名
                                    cellNumMap.put(j, getStringCellValue(col));

                                }

                            } else
                            {
                                // 读取Excel文件内容

                                // 根据列号获取当前列的名称
                                String cellNumName = (String) cellNumMap.get(j);
                                // excel内容转换后的字符串
                                String cellValueString = getStringCellValue(col);
                                aibtmp.setUserName(userName);
                                // 将模板中数据写入ActInfoBean对象中
                                // "操作步骤序号"
                                if (CELL_STR[0].equals(cellNumName))
                                {
                                    aibtmp.setActNo(cellValueString);
                                }
                                // "所属系统"
                                else if (CELL_STR[1].equals(cellNumName))
                                {
                                    aibtmp.setSystem(cellValueString);
                                }
                                // "子系统名称"
                                else if (CELL_STR[2].equals(cellNumName))
                                {
                                    aibtmp.setChildProjectName(cellValueString);
                                }
                                // "作业名称"
                                else if (CELL_STR[3].equals(cellNumName))
                                {
                                    aibtmp.setActName(cellValueString);
                                }
                                // "作业描述"
                                else if (CELL_STR[4].equals(cellNumName))
                                {
                                    aibtmp.setDescribe(cellValueString);
                                }
                                // "触发作业"
                                else if (CELL_STR[5].equals(cellNumName))
                                {
                                    aibtmp.setAfterActList(changeStringtoList(cellValueString, ","));
                                }
                                // "依赖作业"
                                else if (CELL_STR[6].equals(cellNumName))
                                {
                                    aibtmp.setBeforeActList(changeStringtoList(cellValueString, ","));
                                }
                                // "是否删除"
                                else if (CELL_STR[7].equals(cellNumName))
                                {
                                    if (cellValueString.equals("是"))
                                    {
                                        aibtmp.setDeleteFlag("2");
                                        deleteFlaginside = "2";
                                    } else if (cellValueString.equals("否"))
                                    {
                                        aibtmp.setDeleteFlag("1");
                                    } else
                                    {
                                        aibtmp.setDeleteFlag(cellValueString);
                                    }
                                }
                                // "OK文件绝对路径"
                                else if (CELL_STR[8].equals(cellNumName))
                                {
                                    aibtmp.setOKFileABPath(cellValueString);
                                }
                                // "OK文件检测周期"
                                else if (CELL_STR[9].equals(cellNumName))
                                {
                                    aibtmp.setOKFileFindWeek(cellValueString);
                                }
                                // "脚本名称"
                                else if (CELL_STR[10].equals(cellNumName))
                                {
                                    aibtmp.setShellABPath(cellValueString);
                                }
                                // "输出参数"
                                else if (CELL_STR[11].equals(cellNumName))
                                {
                                    aibtmp.setOutputPamaeter(cellValueString);
                                }
                                // "业务异常自动重试"
                                else if (CELL_STR[12].equals(cellNumName))
                                {
                                    aibtmp.setRedo(cellValueString);
                                }
                                // "Agent资源组"
                                else if (CELL_STR[13].equals(cellNumName))
                                {
                                    aibtmp.setAgentGropName(cellValueString);
                                }
                                // "Agent文件检测资源组"
                                else if (CELL_STR[14].equals(cellNumName))
                                {
                                    aibtmp.setCheckAgentGropName(cellValueString);
                                }
                                // "优先级"
                                else if (CELL_STR[15].equals(cellNumName))
                                {
                                    aibtmp.setPriority(cellValueString);
                                }
                                // "权重"
                                else if (CELL_STR[16].equals(cellNumName))
                                {
                                    aibtmp.setWeights(cellValueString);
                                }
                                // "APT组名称"
                                else if (CELL_STR[17].equals(cellNumName))
                                {
                                    aibtmp.setAptGroupName(cellValueString);
                                }
                                // "APT文件名称"
                                else if (CELL_STR[18].equals(cellNumName))
                                {
                                    aibtmp.setAptFileName(cellValueString);
                                }
                                // "ISDB2"
                                else if (CELL_STR[19].equals(cellNumName))
                                {
                                    aibtmp.setIsDB2(cellValueString);
                                }
                                // "DB2IP"
                                else if (CELL_STR[20].equals(cellNumName))
                                {
                                    aibtmp.setDb2IP(cellValueString);
                                }
                                // "主线名称"
                                else if (CELL_STR[21].equals(cellNumName))
                                {
                                    aibtmp.setMainline(cellValueString);
                                }
                                // "头尾标记"
                                else if (CELL_STR[22].equals(cellNumName))
                                {
                                    aibtmp.setSEFlag(cellValueString);
                                }
                                // "是否禁用"
                                else if (CELL_STR[23].equals(cellNumName))
                                {
                                    if (cellValueString.equals("是"))
                                    {
                                        aibtmp.setDisableFlag("1");
                                    } else if (cellValueString.equals("否"))
                                    {
                                        aibtmp.setDisableFlag("0");
                                    } else
                                    {
                                        aibtmp.setDisableFlag(cellValueString);
                                    }
                                }

                            }

                        }
                        // 校验模板列名是否正确
                        if (i == 3)
                        {
                            for (int ii = 0; ii < CELL_STR.length; ii++)
                            {
                                // 判断在模板中解析的列名是否为规范的列名
                                if (!cellNumMap.values().contains(CELL_STR[ii]))
                                {
                                    avalidate.setActName(CELL_STR[ii]);
                                    mapList.put("no_my", avalidate);
                                    returnMap.put(MAPLIST, mapList);
                                    returnMap.put(DELETEFLAG, deleteFlaginside);
                                    returnMap.put("mess", messinside);
                                    return returnMap;
                                }
                            }
                        } else
                        {
                            if (null != projectName || !"".equals(projectName))
                            {
                                aibtmp.setProjectName(projectName);
                            }
                            if (!aibtmp.getActNo().equals(""))
                            {
                                ActInfoBean rep = mapList.get(aibtmp.getActNo());
                                if (rep != null)
                                {
                                    messinside = "操作序号重复 ---" + aibtmp.getActNo();
                                }
                            }
                            if (!("".equals(aibtmp.getActNo()) && "".equals(aibtmp.getChildProjectName())
                                    && "".equals(aibtmp.getActName()) && "".equals(aibtmp.getDescribe())
                                    && (null != aibtmp.getBeforeActList() && aibtmp.getBeforeActList().isEmpty())
                                    && (null != aibtmp.getAfterActList() && aibtmp.getAfterActList().isEmpty())
                                    && "".equals(aibtmp.getOKFileABPath()) && "".equals(aibtmp.getOKFileFindWeek())
                                    && "".equals(aibtmp.getShellABPath()) && "".equals(aibtmp.getOutputPamaeter())
                                    && "".equals(aibtmp.getAgentGropName()) && "".equals(aibtmp.getMainline())
                                    && "".equals(aibtmp.getSEFlag())))
                           
                            {
                                int[] wp = null;
                                    wp = uploadExcelManager.getPrevWeightAndPriority(projectName,
                                        aibtmp.getChildProjectName(), aibtmp.getActName(), conn);
                                // "优先级"
                                String priorityValue = aibtmp.getPriority();
                                if (null == priorityValue || "".equals(priorityValue))
                                {
                                    if (null == wp)
                                    {
                                        priorityValue = "1";
                                    } else
                                    {
                                        priorityValue = String.valueOf(wp[1]);
                                    }
                                }
                                aibtmp.setPriority(priorityValue);

                                // "权重"
                                String weightsValue = aibtmp.getWeights();

                                if (null == weightsValue || "".equals(weightsValue))
                                {
                                    if (null == wp)
                                    {
                                        weightsValue = "1";
                                    } else
                                    {
                                        weightsValue = String.valueOf(wp[0]);
                                    }
                                }
                                aibtmp.setWeights(weightsValue);
                                // 脚本外壳
                                aibtmp.setShellCrust(shellCrustTemp);
                                if (this.checkEmptyObject(aibtmp))
                                {
                                    mapList.put(aibtmp.getActNo(), aibtmp);
                                }

                            }
                        }

                    } else if (i == 3)
                    {
                        avalidate.setActName(MSG1);
                        mapList.put(NO_MYY, avalidate);
                        returnMap.put(MAPLIST, mapList);
                        returnMap.put(DELETEFLAG, deleteFlaginside);
                        returnMap.put("mess", messinside);
                        return returnMap;
                    }

                }
            }
        } catch (Exception ex)
        {
            messge = ex.getMessage();
            if (messge == null)
            {
                avalidate.setActName(MSG1);
                mapList.put(NO_MYY, avalidate);
                returnMap.put(MAPLIST, mapList);
                returnMap.put(DELETEFLAG, deleteFlaginside);
                returnMap.put("mess", messinside);
                return returnMap;
            }
        } finally
        {
            if (null != stream)
            {
                try
                {
                    stream.close();
                } catch (Exception e)
                {
                    stream = null;
                }
            }
        }
        returnMap.put(DELETEFLAG, deleteFlaginside);
        returnMap.put("mess", messinside);
        returnMap.put(MAPLIST, mapList);
        return returnMap;
    }

    /**
     * 
     * @Title: getActInfoCompatible
     * @Description: 获取Excel模板中的数据数据(兼容xls、xlsx格式))
     * @param fileName
     * @param projectName
     * @param uploadExcelManager
     * @param conn
     * @return
     * @return Map<String,ActInfoBean> 返回类型
     * @throws
     * @变更记录 2015-8-4 yunpeng_zhang
     */
    public Map getMainLinePreSuccCompatible ( String fileName,  DayStartBean dayStartBean )
    {
        Map returnMap = new HashMap();
        String deleteFlaginside = "";
        String messinside = "";
        Map<String, MainLinePreSuccBean> mapList = new HashMap<String, MainLinePreSuccBean>();
        MainLinePreSuccBean avalidate = new MainLinePreSuccBean();

        Workbook wb = null;
        InputStream stream = null;
        Sheet sheet1 = null;
        String messge = "";
        Map cellNumMap = new HashMap();// 上传模板中的所有列名Map
        try
        {
            stream = new FileInputStream(new File(fileName));

            // Excel2003
            if (fileName.toLowerCase().endsWith(".xls"))
            {
                wb = new HSSFWorkbook(stream);
                sheet1 = wb.getSheet(dayStartBean.getMainLinePosition());
            }
            // Excel2007
            else if (fileName.toLowerCase().endsWith(XLSX))
            {
                wb = new XSSFWorkbook(stream);
                sheet1 = wb.getSheet(dayStartBean.getMainLinePosition());
            }
            int rows = sheet1.getLastRowNum() + 1 - sheet1.getFirstRowNum();
            // 从第四行开始读取
            for (int i = 4; i < rows; i++)
            {

                Row row = sheet1.getRow(i);
                MainLinePreSuccBean aibtmp = new MainLinePreSuccBean();
                messinside = setAibtmpMessinside( dayStartBean, mapList, i, aibtmp, row , messinside, cellNumMap);
            }
        } catch (Exception ex)
        {
            messge = ex.getMessage();
            dbsRollbacklog.error(ex);
            if (messge == null)
            {
                avalidate.setFlowName("导入日启动封装流程sheet出错" + ex.getMessage());
                messinside = messinside + avalidate.getFlowName();
                returnMap.put(MAPLIST, mapList);
                returnMap.put(DELETEFLAG, deleteFlaginside);
                returnMap.put(MESSDAYSTART, messinside);
                return returnMap;
            }
        } finally
        {
            if (null != stream)
            {
                try
                {
                    stream.close();
                } catch (Exception e)
                {
                    stream = null;
                }
            }
        }
        returnMap.put(DELETEFLAG, deleteFlaginside);
        returnMap.put(MESSDAYSTART, messinside);
        returnMap.put(MAPLIST, mapList);
        return returnMap;

    }

    private String setAibtmpMessinside(DayStartBean dayStartBean,Map<String, MainLinePreSuccBean> mapList,int i,MainLinePreSuccBean aibtmp,Row row ,String messinside,Map cellNumMap){
        
        if (row != null)
        {
            int cols = row.getLastCellNum();

            for (int j = 0; j < cols; j++)
            {
                Cell col = row.getCell((short) j);
                // 根据Excel第四行的列名判断模板格式是否正确
                if (i == 4)
                {
                    if (col != null)
                    {
                        // 获得模板中所有列名
                        cellNumMap.put(j, getStringCellValue(col));

                    }

                } else
                {
                    // 读取Excel文件内容

                    setAibtmp( cellNumMap, aibtmp, col, j );
                }

            }

        }
        if (StringUtils.isNotEmpty(aibtmp.getMainLineNo()))
        {
            MainLinePreSuccBean rep = mapList.get(aibtmp.getMainLineNo());
            if (rep != null)
            {
                messinside = "操作序号重复---" + aibtmp.getMainLineNo();
            }
            aibtmp.setProjectName(dayStartBean.getChildProName());
            aibtmp.setFlowName(dayStartBean.getMainLinePosition());
            aibtmp.setCallProjectName(dayStartBean.getDayChildProName());
            aibtmp.setDelFlag(dayStartBean.getFlag());
            mapList.put(aibtmp.getMainLineNo(), aibtmp);

        }
        return messinside;
    }
    private void setAibtmp(Map cellNumMap,MainLinePreSuccBean aibtmp,Cell col,int j ){
     // 读取Excel文件内容

        // 根据列号获取当前列的名称
        String cellNumName = (String) cellNumMap.get(j);
        // excel内容转换后的字符串
        String cellValueString = getStringCellValue(col);
        // 将模板中数据写入MainLinePreSuccBean对象中
        // "序号"
        if (CELL_STRDAYPRESUCC[0].equals(cellNumName))
        {
            aibtmp.setMainLineNo(cellValueString);
        }
        // "主线名称"
        else if (CELL_STRDAYPRESUCC[1].equals(cellNumName))
        {
            aibtmp.setMainLineName(cellValueString);
        }
        // "封装头尾标记"
        else if (CELL_STRDAYPRESUCC[2].equals(cellNumName))
        {
            aibtmp.setSEFlag(cellValueString);
        }
        // "触发作业"
        else if (CELL_STRDAYPRESUCC[3].equals(cellNumName))
        {
            aibtmp.setAfterActList(changeStringtoList(cellValueString, ","));
        }
        // "依赖作业"
        else if (CELL_STRDAYPRESUCC[4].equals(cellNumName))
        {
            aibtmp.setBeforeActList(changeStringtoList(cellValueString, ","));
        }
    }
    
    /**
     * 
     * @Title: getActInfoCompatible
     * @Description: (获取Excel模板中的数据数据(兼容xls、xlsx格式))
     * @param fileName
     * @param projectName
     * @param uploadExcelManager
     * @param conn
     * @return
     * @return Map<String,ActInfoBean> 返回类型
     * @throws
     * @变更记录 2015-8-4 yunpeng_zhang
     */
    public Map getDayActInfoCompatible ( String fileName )
    {
        Map returnMap = new HashMap();
        String deleteFlaginside = "";
        String messinside = "";
        Map<String, DayStartBean> mapList = new HashMap<String, DayStartBean>();
        DayStartBean avalidate = new DayStartBean();

        Workbook wb = null;
        InputStream stream = null;
        Sheet sheet1 = null;
        String messge = "";
        Map cellNumMap = new HashMap();// 上传模板中的所有列名Map
        try
        {
            stream = new FileInputStream(new File(fileName));

            // Excel2003
            if (fileName.toLowerCase().endsWith(".xls"))
            {
                wb = new HSSFWorkbook(stream);
            }
            // Excel2007
            else if (fileName.toLowerCase().endsWith(XLSX))
            {
                wb = new XSSFWorkbook(stream);
            }
            else if (fileName.toLowerCase().endsWith(".et"))
            {
                wb = WorkbookFactory.create((stream));
            } else
            {
                messinside = "检查Excel版本是否正确！";
                mapList.put(NO_MYY, avalidate);
                returnMap.put(MAPLIST, mapList);
                returnMap.put(DELETEFLAG, deleteFlaginside);
                returnMap.put("mess", messinside);
                return returnMap;
            }
            sheet1 = wb.getSheetAt(0);
            if (sheet1 != null)
            {

                int rows = sheet1.getLastRowNum() + 1 - sheet1.getFirstRowNum();

                // 从第四行开始读取
                for (int i = 4; i < rows; i++)
                {

                    Row row = sheet1.getRow(i);
                    DayStartBean aibtmp = new DayStartBean();
                    if (row != null)
                    {
                        int cols = row.getLastCellNum(); // + 1 -
                                                         // row.getFirstCellNum();

                        for (int j = 0; j < cols; j++)
                        {
                            Cell col = row.getCell((short) j);
                            // 根据Excel第四行的列名判断模板格式是否正确
                            if (i == 4)
                            {

                                if( Environment.getInstance().getXanxSwitch() || Environment.getInstance().getDgProjectParamSwitch()){
                                    if (cols != 20 || null == col)
                                    {
                                        messinside = "检查Excel格式是否正确！";
                                        mapList.put("no_myy", avalidate);
                                        returnMap.put("mapList", mapList);
                                        returnMap.put("deleteFlag", deleteFlaginside);
                                        returnMap.put("mess", messinside);
                                        return returnMap;
                                    }
                                }else {
                                    if (cols != 19 || null == col)
                                    {
                                        messinside = "检查Excel格式是否正确！";
                                        mapList.put("no_myy", avalidate);
                                        returnMap.put("mapList", mapList);
                                        returnMap.put("deleteFlag", deleteFlaginside);
                                        returnMap.put("mess", messinside);
                                        return returnMap;
                                    }
                                }
                                if (col != null)
                                {
                                    boolean isMerge = isMergedRow(sheet1, i, col.getColumnIndex());
                                    if (isMerge)
                                    {
                                        Row row5 = sheet1.getRow(5);
                                        Cell col1 = row5.getCell((short) j);
                                        cellNumMap.put(j, getStringCellValue(col1));
                                    } else
                                    {
                                        // 获得模板中所有列名
                                        cellNumMap.put(j, getStringCellValue(col));
                                    }

                                }

                            } else
                            {
                                // 读取Excel文件内容
                                if (i > 5)
                                {// 根据列号获取当前列的名称
                                    String cellNumName = (String) cellNumMap.get(j);
                                    // excel内容转换后的字符串
                                    String CellValueString = getStringCellValue(col);
                                    // 将模板中数据写入ActInfoBean对象中
                                    // "序号"
                                    if (CELL_STRDAY[0].equals(cellNumName))
                                    {
                                        aibtmp.setDayActNo(CellValueString);
                                    }
                                    // "操作"
                                    else if (CELL_STRDAY[1].equals(cellNumName))
                                    {
                                        aibtmp.setFlag(CellValueString);
                                    }
                                    // "子系统名称"
                                    else if (CELL_STRDAY[2].equals(cellNumName))
                                    {
                                        aibtmp.setChildProName(CellValueString);
                                    }
                                    // "工作流名称"
                                    else if (CELL_STRDAY[3].equals(cellNumName))
                                    {
                                        aibtmp.setFlowName(CellValueString);
                                    }
                                    // "依赖关系层子系统名称"
                                    else if (CELL_STRDAY[4].equals(cellNumName))
                                    {
                                        aibtmp.setDayChildProName(CellValueString);
                                    }
                                    // "是否使用主线封装流程"
                                    else if (CELL_STRDAY[5].equals(cellNumName))
                                    {
                                        aibtmp.setIsMainLine(CellValueString);
                                    }
                                    // "主线间依赖触发引用位置（sheet）"
                                    else if (CELL_STRDAY[6].equals(cellNumName))
                                    {
                                        if ("".equals(CellValueString) || null == CellValueString)
                                        {
                                            aibtmp.setMainLinePosition("无");
                                        } else
                                        {
                                            aibtmp.setMainLinePosition(CellValueString);
                                        }
                                    }
                                    // "直接依赖主线"
                                    else if (CELL_STRDAY[7].equals(cellNumName))
                                    {
                                        aibtmp.setBeforeMainLine(CellValueString);
                                    }
                                    // "主线名称"
                                    else if (CELL_STRDAY[8].equals(cellNumName))
                                    {
                                        aibtmp.setMainLineName(CellValueString);
                                    }
                                    // "直接触发主线"
                                    else if (CELL_STRDAY[9].equals(cellNumName))
                                    {
                                        aibtmp.setAfterMainLine(CellValueString);
                                    }
                                    // "翻牌主线名称"
                                    else if (CELL_STRDAY[10].equals(cellNumName))
                                    {
                                        aibtmp.setFpMainLineName(CellValueString);
                                    }
                                    // "调用方式"
                                    else if (CELL_STRDAY[11].equals(cellNumName))
                                    {
                                        if ("".equals(CellValueString) || null == CellValueString)
                                        {
                                            aibtmp.setCallWay("同步");
                                        } else
                                        {
                                            aibtmp.setCallWay(CellValueString);
                                        }
                                    }
                                    // "主线发起频率"
                                    else if (CELL_STRDAY[12].equals(cellNumName))
                                    {
                                        aibtmp.setMainLineRate(CellValueString);
                                    }
                                    // "主线发起时间"
                                    else if (CELL_STRDAY[13].equals(cellNumName))
                                    {
                                        aibtmp.setMainLineTime(CellValueString);
                                    }
                                    // "偏移量"
                                    else if (CELL_STRDAY[14].equals(cellNumName))
                                    {
                                        aibtmp.setOffset(CellValueString);
                                    }
                                    // "引用函数组"
                                    else if (CELL_STRDAY[15].equals(cellNumName))
                                    {
                                        if ("".equals(CellValueString) || null == CellValueString)
                                        {
                                            aibtmp.setYyFunction("默认");
                                        } else
                                        {
                                            aibtmp.setYyFunction(CellValueString);
                                        }
                                    }
                                    // "跑批日期类型"
                                    else if (CELL_STRDAY[16].equals(cellNumName))
                                    {
                                        aibtmp.setDatetype(CellValueString);
                                    }
                                    // "读取日期的Agent组名"
                                    else if (CELL_STRDAY[17].equals(cellNumName))
                                    {
                                        aibtmp.setDateAgent(CellValueString);
                                    }
                                    // "存储数据日期文件位置"
                                    else if (CELL_STRDAY[18].equals(cellNumName))
                                    {
                                        aibtmp.setDateFilePositon(CellValueString);
                                    }
                                    if( Environment.getInstance().getXanxSwitch()){
                                        if("所属系统".equals(cellNumName)){
                                            aibtmp.setIsysname(CellValueString);
                                        }
                                    }
                                    if (Environment.getInstance().getDgProjectParamSwitch()){
                                        if ("定时任务".equals(cellNumName)){
                                            String cronExpression = CellValueString;
                                            if (StringUtils.isNotBlank(CellValueString)){
                                                String[] split = CellValueString.trim().split(" ", 2);
                                                if (split.length ==2){
                                                    cronExpression = "0 "+split[1];
                                                }
                                            }
                                            aibtmp.setCronExpression(cronExpression);
                                        }
                                    }

                                }
                            }
                        }
                        if (i > 5)
                        {
                            if ("".equals(aibtmp.getDayActNo()) || null == aibtmp.getDayActNo())
                            {

                                if ("".equals(aibtmp.getFlowName()) || null == aibtmp.getFlowName())
                                {
                                } else
                                {
                                    messinside = "序号不能为空,工作流名称为：" + aibtmp.getFlowName();
                                    mapList.put("no_myy", avalidate);
                                    returnMap.put("mapList", mapList);
                                    returnMap.put("deleteFlag", deleteFlaginside);
                                    returnMap.put("mess", messinside);
                                    return returnMap;

                                }

                            } else
                            {
                                DayStartBean rep = mapList.get(aibtmp.getDayActNo());
                                if (rep != null)
                                {
                                    messinside = "操作序号重复---" + aibtmp.getDayActNo();
                                }
                                if(Environment.getInstance().isBhODSVerificationUniqueness()){//渤海ODS上传验证开关
                                    //验证，时间控制表里日启动流程名称不重复
                                    for (Map.Entry<String, DayStartBean> entry : mapList.entrySet())
                                    {
                                        DayStartBean aif = entry.getValue();
                                        if (aif.getFlowName()!=null){//子系統
                                            if(aif.getFlowName().equals(aibtmp.getFlowName())){
                                                mapList.put(NO_MYY, avalidate);
                                                returnMap.put(MAPLIST, mapList);
                                                returnMap.put(DELETEFLAG, deleteFlaginside);
                                                returnMap.put("mess", "工作流名称重复,序号为："+aibtmp.getDayActNo());
                                                return returnMap;
                                            }
                                        }
                                    }
                                }
                                mapList.put(aibtmp.getDayActNo(), aibtmp);
                                // 偏移量 T+N、默认效验
                                if (!"".equals(aibtmp.getOffset()) && null
                                        != aibtmp.getOffset())
                                {
                                    Boolean verificationOffset= true;
                                    for(String Offset:OFFSET_DICTIONARIES){
                                        if (aibtmp.getOffset().equals(Offset)){
                                            verificationOffset=false;
                                        }
                                    }
                                    if(verificationOffset){
                                        mapList.put(NO_MYY, avalidate);
                                        returnMap.put(MAPLIST, mapList);
                                        returnMap.put(DELETEFLAG, deleteFlaginside);
                                        returnMap.put("mess", "偏移量格式错误!正确格式为T+N、默认！,序号为："+aibtmp.getDayActNo());
                                        return returnMap;
                                    }
                                }
                                if(!"".equals(aibtmp.getCronExpression())&& null !=aibtmp.getCronExpression()){
                                    // 验证，定时任务表达式格式是否正确
                                    if (!CronExpression.isValidExpression(aibtmp.getCronExpression())){
                                        mapList.put(NO_MYY, avalidate);
                                        returnMap.put(MAPLIST, mapList);
                                        returnMap.put(DELETEFLAG, deleteFlaginside);
                                        returnMap.put("mess", "定时任务表达式格式错误!正确格式为：0 0 12 * *?、0 0/5 * * *?，序号为："+aibtmp.getDayActNo());
                                        return returnMap;
                                    }
                                }
                            }
                        }
                    }

                }
            
            }
        } catch (Exception ex)
        {
            messge = ex.getMessage();
            dbsRollbacklog.error(ex);
            if (messge == null)
            {
                messinside = "日启动Excel导入出现异常！" + ex.getMessage();
                mapList.put(NO_MYY, avalidate);
                returnMap.put(MAPLIST, mapList);
                returnMap.put(DELETEFLAG, deleteFlaginside);
                returnMap.put("mess", messinside);
                return returnMap;
            }
        } finally
        {
            if (null != stream)
            {
                try
                {
                    stream.close();
                } catch (Exception e)
                {
                    stream = null;
                }
            }
        }

        returnMap.put("mess", messinside);
        returnMap.put(MAPLIST, mapList);
        return returnMap;
    }

    private void cellNumMapPut(int i,int j,Cell col,Map cellNumMap,Sheet sheet1){
        if (col != null)
        {
            boolean isMerge = isMergedRow(sheet1, i, col.getColumnIndex());
            if (isMerge)
            {
                Row row5 = sheet1.getRow(5);
                Cell col1 = row5.getCell((short) j);
                cellNumMap.put(j, getStringCellValue(col1));
            } else
            {
                // 获得模板中所有列名
                cellNumMap.put(j, getStringCellValue(col));
            }

        }
    }
    
    private void readCell(int i,int j,DayStartBean aibtmp,Cell col,Map cellNumMap){
     // 读取Excel文件内容
        if (i > 5)
        {// 根据列号获取当前列的名称
            String cellNumName = (String) cellNumMap.get(j);
            // excel内容转换后的字符串
            String cellValueString = getStringCellValue(col);
            // 将模板中数据写入ActInfoBean对象中
            // "序号"
            if (CELL_STRDAY[0].equals(cellNumName))
            {
                aibtmp.setDayActNo(cellValueString);
            }
            // "操作"
            else if (CELL_STRDAY[1].equals(cellNumName))
            {
                aibtmp.setFlag(cellValueString);
            }
            // "子系统名称"
            else if (CELL_STRDAY[2].equals(cellNumName))
            {
                aibtmp.setChildProName(cellValueString);
            }
            // "工作流名称"
            else if (CELL_STRDAY[3].equals(cellNumName))
            {
                aibtmp.setFlowName(cellValueString);
            }
            // "依赖关系层子系统名称"
            else if (CELL_STRDAY[4].equals(cellNumName))
            {
                aibtmp.setDayChildProName(cellValueString);
            }
            // "是否使用主线封装流程"
            else if (CELL_STRDAY[5].equals(cellNumName))
            {
                aibtmp.setIsMainLine(cellValueString);
            }
            // "主线间依赖触发引用位置（sheet）"
            else if (CELL_STRDAY[6].equals(cellNumName))
            {
                if (StringUtils.isEmpty(  cellValueString))
                {
                    aibtmp.setMainLinePosition("无");
                } else
                {
                    aibtmp.setMainLinePosition(cellValueString);
                }
            }
            // "直接依赖主线"
            else if (CELL_STRDAY[7].equals(cellNumName))
            {
                aibtmp.setBeforeMainLine(cellValueString);
            }
            // "主线名称"
            else if (CELL_STRDAY[8].equals(cellNumName))
            {
                aibtmp.setMainLineName(cellValueString);
            }
           
            
            
            readCellSub( aibtmp, cellNumName, cellValueString);
        }
    }
    private void readCellSub(DayStartBean aibtmp,String cellNumName,String cellValueString){
        // "直接触发主线"
         if (CELL_STRDAY[9].equals(cellNumName))
        {
            aibtmp.setAfterMainLine(cellValueString);
        }
        // "翻牌主线名称"
         else if (CELL_STRDAY[10].equals(cellNumName))
        {
            aibtmp.setFpMainLineName(cellValueString);
        } 
        // "调用方式"
        else if (CELL_STRDAY[11].equals(cellNumName))
        {
            aibtmp.setCallWay(cellValueString);
            if (StringUtils.isEmpty( cellValueString))
            {
                aibtmp.setCallWay("同步");
            } 
        }
        // "主线发起频率"
        else if (CELL_STRDAY[12].equals(cellNumName))
        {
            aibtmp.setMainLineRate(cellValueString);
        }
        // "主线发起时间"
        else if (CELL_STRDAY[13].equals(cellNumName))
        {
            aibtmp.setMainLineTime(cellValueString);
        }
        // "偏移量"
        else if (CELL_STRDAY[14].equals(cellNumName))
        {
            aibtmp.setOffset(cellValueString);
        }
        // "引用函数组"
        else if (CELL_STRDAY[15].equals(cellNumName))
        {
            if (StringUtils.isEmpty(  cellValueString))
            {
                aibtmp.setYyFunction("默认");
            } else
            {
                aibtmp.setYyFunction(cellValueString);
            }
        }
        // "跑批日期类型"
        else if (CELL_STRDAY[16].equals(cellNumName))
        {
            aibtmp.setDatetype(cellValueString);
        }
        // "读取日期的Agent组名"
        else if (CELL_STRDAY[17].equals(cellNumName))
        {
            aibtmp.setDateAgent(cellValueString);
        }
        // "存储数据日期文件位置"
        else if (CELL_STRDAY[18].equals(cellNumName))
        {
            aibtmp.setDateFilePositon(cellValueString);
        }
    }
    /**
     * 
     * @Title: getStringCellValue
     * @Description: (获取单元格数据内容为字符串类型的数据)
     * @param cell Excel单元格
     * @return 单元格数据内容
     * @return String 返回类型
     * @throws
     * @变更记录 2015-8-4 yunpeng_zhang
     */
    private String getStringCellValue ( Cell cell )
    {
        String strCell = "";
        if (cell != null)
        {
            switch (cell.getCellType())
            {
                case STRING:
                    strCell = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    java.text.DecimalFormat formatter = new java.text.DecimalFormat("########");
                    strCell = formatter.format(cell.getNumericCellValue());
                    break;
                case BOOLEAN:
                    strCell = String.valueOf(cell.getBooleanCellValue());
                    break;
                case BLANK:
                    strCell = "";
                    break;
                default:
                    strCell = "";
                    break;
            }
        }
        if (strCell.equals("") ||  cell == null)
        {
            return "";
        }
        return strCell.trim();
    }

    private boolean returnValue(ActInfoBean aibtmp,boolean returnValue){
        
        // 操作步骤号
        if (StringUtils.isNotBlank(aibtmp.getActNo()))
        {
            returnValue = true;
        }
        // "所属系统"
         if (StringUtils.isNotBlank(aibtmp.getSystem()))
        {
            returnValue = true;
        }
        // "子系统名称"

         if (StringUtils.isNotBlank(aibtmp.getChildProjectName()))
        {
            returnValue = true;
        }

        // "作业名称"

         if (StringUtils.isNotBlank(aibtmp.getActName()))
        {
            returnValue = true;
        }

        // "作业描述"

         if (StringUtils.isNotBlank(aibtmp.getDescribe()))
        {
            returnValue = true;
        }
        // "触发作业"

         if (null != aibtmp.getAfterActList())
        {
            returnValue = true;
        }
        // "依赖作业"

         if (null != aibtmp.getBeforeActList())
        {
            returnValue = true;
        }
         return returnValue;
    }
    /**
     * 判断对象是否为空
     * 
     * @param aibtmp
     * @return
     */
    private boolean checkEmptyObject ( ActInfoBean aibtmp )
    {

        boolean returnValue = false;
        if (aibtmp == null)
        {
            return returnValue;
        }
        returnValue = returnValue( aibtmp, returnValue);
        
        // "OK文件绝对路径"

         if (StringUtils.isNotBlank(aibtmp.getOKFileABPath()))
        {
            returnValue = true;
        }
        // "OK文件检测周期"

         if (StringUtils.isNotBlank(aibtmp.getOKFileFindWeek()))
        {
            returnValue = true;
        }
        // "脚本名称"

         if (StringUtils.isNotBlank(aibtmp.getShellABPath()))
        {
            returnValue = true;
        }
        // "输出参数"

         if (StringUtils.isNotBlank(aibtmp.getOutputPamaeter()))
        {
            returnValue = true;
        }
        // "业务异常自动重试"

         if (StringUtils.isNotBlank(aibtmp.getRedo()))
        {
            returnValue = true;
        }
        // "Agent资源组"

         if (StringUtils.isNotBlank(aibtmp.getAgentGropName()))
        {
            returnValue = true;
        }
        // "Agent文件检测资源组"

         if (StringUtils.isNotBlank(aibtmp.getCheckAgentGropName()))
        {
            returnValue = true;
        }
        // "APT组名称"

         if (StringUtils.isNotBlank(aibtmp.getAptGroupName()))
        {
            returnValue = true;
        }
        // "APT文件名称"

         if (StringUtils.isNotBlank(aibtmp.getAptFileName()))
        {
            returnValue = true;
        }
        // "ISDB2"

         if (StringUtils.isNotBlank(aibtmp.getIsDB2()))
        {
            returnValue = true;
        }
        // "DB2IP"

         if (StringUtils.isNotBlank(aibtmp.getDb2IP()))
        {
            returnValue = true;
        }
        // "主线名称"

         if (StringUtils.isNotBlank(aibtmp.getMainline()))
        {
            returnValue = true;
        }
        // "头尾标记"

         if (StringUtils.isNotBlank(aibtmp.getSEFlag()))
        {
            returnValue = true;
        }
        // "是否禁用"
         if (StringUtils.isNotBlank(aibtmp.getDisableFlag()))
        {
            returnValue = true;
        }

        return returnValue;
    }

    /**
     * 判断合并了行
     * 
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    private static boolean isMergedRow ( Sheet sheet, int row, int column )
    {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++)
        {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if (row == firstRow && row == lastRow)
            {
                if (column >= firstColumn && column <= lastColumn)
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 校验日期格式
     * 
     */
    private static boolean isValidatetime ( String time )
    {
        boolean flag = true;
        Pattern p = Pattern.compile("^([01][0-9]|2[0-3]):([0-5][0-9])$");
        Matcher m = p.matcher(time);
        if (m.find())
        {
            flag = false;
        }

        return flag;
    }

    
    private void saveExcelModel(Connection con, ProjectSaveUtilBean projectSaveUtilBean,
              int basicType,String testExcelPath) throws RepositoryException, SQLException{
        Map<String, String> mapProject = new HashMap<String, String>();
        Map mapListmainLinePreSuccBean = null;
        Map<String, Map<String, MainLinePreSuccBean>> listmainLinePreSuccBean = new HashMap<String, Map<String, MainLinePreSuccBean>>();
        for (Map<String, DayStartBean> mapListDayStartBean : projectSaveUtilBean.getDayStartMapList())
        {
            ActInfoBean actInfoBean = new ActInfoBean();
            for (Map.Entry<String, DayStartBean> entry : mapListDayStartBean.entrySet())
            {
                DayStartBean dayStartBean = entry.getValue();

                if (StringUtils.isNotBlank( dayStartBean.getChildProName()) )
                {
                    actInfoBean.setProjectName(dayStartBean.getChildProName());
                }
                mapProject.put(dayStartBean.getChildProName(), dayStartBean.getChildProName());
                if (!"无".equals(dayStartBean.getMainLinePosition()))
                {
                    mapListmainLinePreSuccBean = this.getMainLinePreSuccCompatible(testExcelPath, 
                        dayStartBean);

                    if ( !(mapListmainLinePreSuccBean.isEmpty()) )
                    {
                        listmainLinePreSuccBean.put(dayStartBean.getMainLinePosition(),
                            mapListmainLinePreSuccBean);
                    } 
                }
            }
            upLoadTempExcleManager.saveExcelModelDayStart(mapListDayStartBean,  con,
                basicType);
            upLoadTempExcleManager.saveExcelModelPrePro(listmainLinePreSuccBean,  con,
                basicType);

            // 删除临时表中的数据
            for (Map.Entry<String, String> entryP : mapProject.entrySet())
            {
                EngineRepositotyJdbc.getInstance().updateDayStartSuccAndPreOneConnection(con,
                    entryP.getValue());
            }

            /** add by zhangjunyu Excel 文件信息保存 */
            File file = new File(testExcelPath);
            uploadExcelManager.saveExcelModelRecord(con, actInfoBean, projectSaveUtilBean.getFileNameFinal(), projectSaveUtilBean.getUserName(), file, basicType);
        }
    }
    /**
     * 
     * @Title: savePrjForTeam
     * @Description: (工程上传多写操作)
     * @param con
     * @param projectSaveUtilBean
     * @param prjOrAdp
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     * @变更记录 2018-06-28 yunpeng_zhang
     */
    public boolean savePrjForTeam ( Connection con, ProjectSaveUtilBean projectSaveUtilBean,
            Map<String, Integer> eitRowCount, String ddBkey, int basicType ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean returnValue = false;
        String updtRecord = "update IEAI_RECORD set ROCKNAME='name' where projectName=?";
        
        String sql3 = "INSERT INTO IEAI_FLOWDEF(IID, IFLOWDESC, IFLOWNAME, IPRJID) VALUES(?,?,?,?) ";
        String sql4 = "INSERT INTO IEAI_PROJECT(IID, INAME,  IMAJVER,    IMINVER,    IFREEZED,   IUPLOADUSER, IUPLOADNUM, IUUID,  IUPLOADTIME,    IUPLOADUSERID,  IGROUPID , PROTYPE, IUPPERID , ILATESTID, IPKGCONTENTID) "
                + "      VALUES ( ?, ?, 0, 0, 0, ?, ?, ?, SYSDATE, ?, ?, ?, ?, ?, ?)";
        if (JudgeDB.IEAI_DB_TYPE .equals( JudgeDB.MYSQL))
        {
            sql4 = "INSERT INTO IEAI_PROJECT(IID, INAME,  IMAJVER,    IMINVER,    IFREEZED,   IUPLOADUSER, IUPLOADNUM, IUUID,  IUPLOADTIME,    IUPLOADUSERID,  IGROUPID , PROTYPE, IUPPERID , ILATESTID, IPKGCONTENTID) "
                    + "      VALUES ( ?, ?, 0, 0, 0, ?, ?, ?, sysdate(), ?, ?, ?, ?, ?, ?)";
        }
        String sql5 = "UPDATE IEAI_PROJECT T SET T.ILATESTID=? WHERE T.INAME=?  AND T.IGROUPID=?";
        String sql6 = "INSERT INTO  IEAI_FLOW_PARAM( PID,IID,IPARAM_NAME,IPARAM_TYPE,IPARAM_DESC,IREAD_ONLY )  "
                + "VALUES(?, ?, ?, ?, ?, ?)";
        String sql7 = "INSERT INTO  IEAI_SON_PROJECT(AID, IID, IACT_ID, IACT_NAME, IACT_SUCCID, IACT_DESC, IACT_TYPE,IACT_CONDITIONS,IPROJECTNAME,IFLOWNAME)  "
                + "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        String sqlInsWs = "INSERT INTO  IEAI_WORKFLOWINSTANCE( IFLOWID, IFLOWNAME,  IPROJECTNAME,IFLOWDES, ISTARTTIME, ISTATUS,IENDTIME,IDELETEFLAG ,ISYSTEM)  "
                + "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try
        {
            PreparedStatement psUpRecord = null;
            PreparedStatement ps1 = null;
            PreparedStatement ps3 = null;
            PreparedStatement ps4 = null;
            PreparedStatement ps5 = null;
            PreparedStatement ps6 = null;
            PreparedStatement ps7 = null;
            try
            {
                int count = 0;
                int index = 0;
                
                String testExcelPath = projectSaveUtilBean.getFilePath();
                String fileNameFinal = projectSaveUtilBean.getFileNameFinal();
                
                testExcelPath = testExcelPath + File.separator + fileNameFinal;
                index = 0;
                psUpRecord = con.prepareStatement(updtRecord);
                psUpRecord.setString(++index, projectSaveUtilBean.getFileName());
                psUpRecord.executeUpdate();
                
                saveExcelModel( con,  projectSaveUtilBean,
                        basicType, testExcelPath);
                
                String sysName = "";
                String systemName = "";
                ps1 = con.prepareStatement(sqlInsWs);
                for (RepWorkflowInstance flowIns : projectSaveUtilBean.getInsertFlowInsList())
                {
                    index = 0;
                    sysName = systemName;
                    if (StringUtils.isNotBlank(flowIns.get_isystem()))
                    {
                        sysName = flowIns.get_isystem();
                    } 
                    count = ExcelActUtil.getInstance().getDayStartInsertWorkFLowInstance(sysName, flowIns.getFlowName(),
                        flowIns.getProjectName(), con);
                    if (count == 0)
                    {
                        ps1.setLong(1, flowIns.getFlowId());
                        ps1.setString(2, flowIns.getFlowName());
                        ps1.setString(3, flowIns.getProjectName());
                        ps1.setString(4, flowIns.getFlowDes());
                        ps1.setLong(5, 0);
                        ps1.setLong(6, Constants.STATE_NEW_CREATE);
                        ps1.setLong(7, 0);
                        ps1.setLong(8, 0);
                        ps1.setString(9, StringUtils.isNotBlank(flowIns.get_isystem())
                                ? flowIns.get_isystem() : systemName);
                        ps1.addBatch();
                    }
                }
                ps1.executeBatch();

                ps3 = con.prepareStatement(sql3);
                for (RepWorkflow insertDefRepWorkflow : projectSaveUtilBean.getInserFlowDefList())
                {
                    index = 0;
                    ps3.setLong(++index, insertDefRepWorkflow.getId().longValue());
                    ps3.setString(++index, insertDefRepWorkflow.getFlowDesc());
                    ps3.setString(++index, insertDefRepWorkflow.getFlowName());
                    ps3.setLong(++index, insertDefRepWorkflow.getPrjId().longValue());
                    ps3.addBatch();
                }
                ps3.executeBatch();

                ps4 = con.prepareStatement(sql4);
                for (RepProject insertRepProject : projectSaveUtilBean.getInserProjectList())
                {
                    index = 0;
                    ps4.setLong(++index, insertRepProject.getId().longValue());
                    ps4.setString(++index, insertRepProject.getName());
                    ps4.setString(++index, insertRepProject.getUploadUserFullName());
                    ps4.setLong(++index, insertRepProject.getUploadNum().longValue());
                    ps4.setString(++index, insertRepProject.getUuid());
                    ps4.setLong(++index, insertRepProject.getUploadUserId().longValue());
                    ps4.setLong(++index, insertRepProject.getGroupId().longValue());
                    ps4.setDouble(++index, insertRepProject.getProType());
                    ps4.setLong(++index, insertRepProject.getIupperId());
                    ps4.setLong(++index, insertRepProject.getIlatestId());
                    ps4.setLong(++index, insertRepProject.getPkgContentId());
                    ps4.addBatch();
                }
                ps4.executeBatch();

                ps6 = con.prepareStatement(sql6);
                for (ExcelDayStartFlowParams param : projectSaveUtilBean.getInsertDayStartFlowParams())
                {
                    index = 0;
                    ps6.setLong(++index, param.getPid());
                    ps6.setLong(++index, param.getFid());
                    ps6.setString(++index, param.getParamName());
                    ps6.setString(++index, param.getParamType());
                    ps6.setString(++index, param.getParamDesc());
                    ps6.setString(++index, param.getParamOnly());
                    ps6.addBatch();
                }
                ps6.executeBatch();

                ps7 = con.prepareStatement(sql7);
                for (ExcelDayStartAct act : projectSaveUtilBean.getInsertDayStartActList())
                {
                    index = 0;
                    ps7.setLong(++index, act.getAid());
                    ps7.setLong(++index, act.getIid());
                    ps7.setLong(++index, act.getActId());
                    ps7.setString(++index, act.getActName());
                    ps7.setLong(++index, act.getActsuccid());
                    ps7.setString(++index, act.getActDesc());
                    ps7.setString(++index, act.getActType());
                    ps7.setString(++index, act.getSuccConditions());
                    ps7.setString(++index, act.getProjectName());
                    ps7.setString(++index, act.getFlowname());
                    ps7.addBatch();
                }
                ps7.executeBatch();

                ps5 = con.prepareStatement(sql5);
                ps5Exec( ps5, projectSaveUtilBean, eitRowCount,  ddBkey);

            } catch (Exception e)
            {
                log.info(e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_INSERT);
            } finally
            {
                DBResource.closePreparedStatement(psUpRecord, method, log);
                DBResource.closePreparedStatement(ps1, method, log);
                DBResource.closePreparedStatement(ps3, method, log);
                DBResource.closePreparedStatement(ps4, method, log);
                DBResource.closePreparedStatement(ps5, method, log);
                DBResource.closePreparedStatement(ps6, method, log);
                DBResource.closePreparedStatement(ps7, method, log);
            }
        } catch (RepositoryException ex)
        {
            dbsRollbacklog.error(ex);
            throw ex;
        }
        return returnValue;
    }

    private void ps5Exec(PreparedStatement ps5,ProjectSaveUtilBean projectSaveUtilBean,Map<String, Integer> eitRowCount, String ddBkey) throws SQLException{
        for (RepProject insertRepProject : projectSaveUtilBean.getInserProjectList())
        {
            int index = 0;
            ps5.setLong(++index, insertRepProject.getId());
            ps5.setString(++index, insertRepProject.getName());
            ps5.setLong(++index, insertRepProject.getGroupId());
            int updatePrjRescont = ps5.executeUpdate();
            // 判断更新数据与基线库更新数据的数量是否一致
            if (Constants.DB_COMPARE)
            {
                LinkedHashMap<String, String> paramMap = new LinkedHashMap<String, String>();
                paramMap.put("{ILATESTID}", insertRepProject.getId().toString());
                paramMap.put("{NAME}", insertRepProject.getName());
                paramMap.put("{IGROUPID}", insertRepProject.getGroupId().toString());
                String mapKey = StringUtil.replaceTemplet(TransactionStorerJDBC.UPDATE_PROJECT_SQL, paramMap);
                int basicRescont = eitRowCount.get(mapKey);
                if (basicRescont != updatePrjRescont)
                {
                    dbsRollbacklog.error("数据源：" + ddBkey + " 'stio工程上传' " + mapKey + " 与操作基线库表中的数据量不一致！");
                    throw new SQLException("数据源：" + ddBkey + " 'stio工程上传' " + mapKey + " 与操作基线库表中的数据量不一致！");
                }
            }
        }
    }
    /**
     * 提交或者回滚
     * 
     * @Title: commitConn
     * @Description: 提交活回滚多写事务
     * @param con
     * @param rollback
     * @return
     * @return boolean 返回类型
     * @throws
     * @变更记录 2016年6月1日 yunpeng_zhang
     */
    private boolean commitOrRollbackConn ( Connection con, boolean rollback )
    {
        try
        {
            if (!rollback)
            {
                con.commit();
            } else
            {
                con.rollback();
            }

        } catch (SQLException e)
        {
            try
            {
                rollback = true;
                con.rollback();
            } catch (SQLException e1)
            {
                dbsRollbacklog.error(e1);
            }
        }
        return rollback;
    }

    /**
     * 
     * @Title: rollbackPrjForTeam
     * @Description: (工程或者接口上传多写操作失败，将已经commit的数据源数据修改为原值)
     * @param con
     * @param conName
     * @param projectSaveUtilBean
     * @param prjOrAdp
     * @return void 返回类型
     * @throws
     * @变更记录 2016年5月31日 yunpeng_zhang
     */
    public void rollbackPrjForTeam ( Connection con, String conName, ProjectSaveUtilBean projectSaveUtilBean,
            int prjOrAdp )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql1 = "DELETE IEAI_WORKFLOW WHERE IFLOWID=?";
        String sql2 = "UPDATE IEAI_WORKFLOW SET ILATESTID=?  WHERE IPRJUPPERID=? AND IFLOWNAME=?";
        String sql3 = "DELETE IEAI_FLOWDEF WHERE IID=? ";
        String sql4 = "DELETE IEAI_PROJECT WHERE IID=? ";
        String sql5 = "UPDATE IEAI_PROJECT SET ILATESTID=? WHERE INAME=?  AND IGROUPID=?";
        String sql7 = "DELETE IEAI_FLOW_PARAM WHERE IID = ?";
        String sql8 = "DELETE IEAI_SON_PROJCET WHERE IID = ?";

        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        PreparedStatement ps7 = null;
        PreparedStatement ps8 = null;
        try
        {

            int index = 0;

            ps1 = con.prepareStatement(sql1);
            for (RepWorkflow insertRepWorkflow : projectSaveUtilBean.getInsertWorkFlowList())
            {
                index = 0;
                ps1.setLong(++index, insertRepWorkflow.getId());
                ps1.addBatch();

                ps7 = con.prepareStatement(sql7);
                index = 0;
                ps7.setLong(++index, insertRepWorkflow.getId());
                ps7.addBatch();

                ps8 = con.prepareStatement(sql8);
                index = 0;
                ps8.setLong(++index, insertRepWorkflow.getId());
                ps8.addBatch();
            }
            ps1.executeBatch();
            ps7.executeUpdate();
            ps8.executeUpdate();
            ps2 = con.prepareStatement(sql2);
            for (RepWorkflow insertRepWorkflow : projectSaveUtilBean.getUpdateWorkFlowList())
            {
                index = 0;
                ps2.setLong(++index, insertRepWorkflow.get_ilastId());
                ps2.setLong(++index, insertRepWorkflow.get_iprjUpperId());
                ps2.setString(++index, insertRepWorkflow.getFlowName());
                ps2.addBatch();
            }
            ps2.executeBatch();
            ps3 = con.prepareStatement(sql3);
            for (RepWorkflow insertDefRepWorkflow : projectSaveUtilBean.getInserFlowDefList())
            {
                index = 0;
                ps3.setLong(++index, insertDefRepWorkflow.getId().longValue());
                ps3.addBatch();
            }
            ps3.executeBatch();

            ps4 = con.prepareStatement(sql4);
            for (RepProject insertRepProject : projectSaveUtilBean.getInserProjectList())
            {
                index = 0;
                ps4.setLong(++index, insertRepProject.getId().longValue());
                ps4.addBatch();
            }
            ps4.executeBatch();

            ps5 = con.prepareStatement(sql5);
            index = 0;
            ps5.setLong(++index, projectSaveUtilBean.getLatestId());
            ps5.setString(++index, projectSaveUtilBean.getPrjName());
            ps5.setLong(++index, projectSaveUtilBean.getGroupId());
            ps5.executeUpdate();

            con.commit();
        } catch (Exception e)
        {
            try
            {
                if (con != null)
                {
                    con.rollback();
                }

            } catch (SQLException e1)
            {
                dbsRollbacklog.error(e1);
            }
            if (Constants.PRJ == prjOrAdp)
            {
                dbsRollbacklog.info("数据源：" + conName + "回滚工程信息失败。工程名：" + projectSaveUtilBean.getPrjName() + " 工程ID:"
                        + projectSaveUtilBean.getPrjId());
            } else
            {
                dbsRollbacklog.info("数据源：" + conName + "回滚接口信息失败。接口名：" + projectSaveUtilBean.getPrjName() + " 接口ID:"
                        + projectSaveUtilBean.getPrjId());
            }

        } finally
        {
            DBResource.closePreparedStatement(ps1, method, log);
            DBResource.closePreparedStatement(ps2, method, log);
            DBResource.closePreparedStatement(ps3, method, log);
            DBResource.closePreparedStatement(ps4, method, log);
            DBResource.closePreparedStatement(ps5, method, log);
            DBResource.closePreparedStatement(ps6, method, log);
            DBResource.closePreparedStatement(ps7, method, log);
            DBResource.closePreparedStatement(ps8, method, log);

        }

    }

    private static final Logger log = Logger.getLogger(DbUtilUpLoadDayStartExcel.class);
}
