\set SQLTERM /
DECLARE
LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_COMPUTER_LIST' AND COLUMN_NAME='DEVICECATEGORY';
IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_COMPUTER_LIST ADD DEVICECATEGORY VARCHAR2(255) ';
EXECUTE IMMEDIATE LS_SQL;
END IF;
COMMIT;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IDISPLAYGROUPID';
IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_COMPUTER_LIST ADD REGION VARCHAR2(255) ';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT  COUNT(*) INTO  LI_EXISTS FROM IEAI_OS WHERE OSID=10001;
IF LI_EXISTS = 0 THEN
	LS_SQL := 'INSERT INTO IEAI_OS(OSID,OSNAME) VALUES (10001,''Linux'')';
EXECUTE IMMEDIATE LS_SQL;
commit;
END IF;
COMMIT;
END;
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYS_STAT_USER_TABLES WHERE RELNAME = lower('IEAI_HC_ITEM_RELATION');
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HC_ITEM_RELATION (IID NUMBER (19)   NOT NULL,TASKID NUMBER (19) NOT NULL  ,TARGETID NUMBER (19) NOT NULL ,CHKITEM_ID NUMBER (19)  ,CHKCONFPOINT_ID NUMBER (19)  , CONSTRAINT PK_IEAI_HC_ITEM_RELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYS_STAT_USER_TABLES WHERE RELNAME = lower('IEAI_HC_TARGET_RELATION');
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HC_TARGET_RELATION (IID NUMBER (19)   NOT NULL,TASKID NUMBER (19) NOT NULL  ,SYSTEMID NUMBER (19) NOT NULL  ,COMPUTERID NUMBER (19)  , CONSTRAINT PK_IEAI_HC_TARGET_RELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
