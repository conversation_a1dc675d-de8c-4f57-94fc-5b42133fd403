package com.ideal.ieai.server.repository.hd.businessTypeConfig;

import java.io.Serializable;

/**
 * 
 * 名称: BusinessTypeBeanForQuery.java<br>
 * 描述: 业务系统类型查询类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2016年1月11日<br>
 * 
 * <AUTHOR>
 */
public class BusinessTypeBeanForQuery implements Serializable
{
    public BusinessTypeBeanForQuery()
    {
    }

    private String busName;              // IBUSNAME;
    private int    start     = 0;        // 起始页
    private int    limit     = 10;       // 每页数据
    private long   iidForQuery;          // iid;
    private int    queryType = 0;        // 查询类型 1已绑定 0未绑定
    private String userName;             // 当前用户名
    private String loginName;            // 当前用户名
    private long   userId;               // 当前ID
    private String iinstanceNameForQuery; // 子系统名

    public String getIinstanceNameForQuery ()
    {
        return iinstanceNameForQuery;
    }

    public void setIinstanceNameForQuery ( String iinstanceNameForQuery )
    {
        this.iinstanceNameForQuery = iinstanceNameForQuery;
    }

    public String getLoginName ()
    {
        return loginName;
    }

    public void setLoginName ( String loginName )
    {
        this.loginName = loginName;
    }

    public String getUserName ()
    {
        return userName;
    }

    public void setUserName ( String userName )
    {
        this.userName = userName;
    }

    public long getUserId ()
    {
        return userId;
    }

    public void setUserId ( long userId )
    {
        this.userId = userId;
    }

    public long getIidForQuery ()
    {
        return iidForQuery;
    }

    public void setIidForQuery ( long iidForQuery )
    {
        this.iidForQuery = iidForQuery;
    }

    public int getQueryType ()
    {
        return queryType;
    }

    public void setQueryType ( int queryType )
    {
        this.queryType = queryType;
    }

    public int getStart ()
    {
        return start;
    }

    public void setStart ( int start )
    {
        this.start = start;
    }

    public int getLimit ()
    {
        return limit;
    }

    public void setLimit ( int limit )
    {
        this.limit = limit;
    }

    public String getBusName ()
    {
        return busName;
    }

    public void setBusName ( String busName )
    {
        this.busName = busName;
    }

}
