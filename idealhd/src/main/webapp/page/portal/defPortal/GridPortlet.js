Ext.define('Ext.app.GridPortlet', {
    extend: 'Ext.grid.Panel',
    alias: 'widget.gridportlet',

	//默认属性，属性使用方式与原生一致。
	region : 'center',
	autoScroll : true,
	border : true,
	columnLines : true,
	loadMask : {
		msg : " 数据加载中，请稍等 "
	},
	
    ipage : '', // 用于保存自定义pagingtoolbar的实例，便于外部获取。
//    ipageBaseCls : '', // 扩展自定义pagingtoolbar样式的能力
//    ipageItems : '', // 增加对pagingtoolbar扩展内容
    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    
    ipageAutoSelect : true,
    
    iqueryFun : function(){
    	console.log('虽然使用了ideapanel控件，但并没有指定iqueryFun属性，没有可以调用的查询方法。');
    },
    
    iAddListenerItems : function(myItems){
    	var me = this;
    	
    	var enter = new Ext.create('Ext.ux.ideal.util.AddEnterListener');
    	enter.add(myItems, me.iqueryFun);
    },
    
    emptyText : '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>',
    
    ipageSize : 10,
    idisplayMsg : true,
    
    colurl : "",
    dataurl : "",
    columns1 : "",
    
    initComponent: function(){
    	var me = this;
    	colurl = me.colurl;
    	dataurl = me.dataurl;
    	columns1 = me.columns1;
    	if(colurl !=""){
            var store = Ext.create('Ext.data.Store', {
                autoLoad: true,
                autoDestroy : true,
                fields: colurl,
                remoteSort: true,
                proxy: {
                    type: 'ajax',
                    url: dataurl,
                    reader: {
                        type: 'json',
                        root: 'dataList',
                        totalProperty : 'total'
                    }
                }
            });
            
            var pageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
    			name : 'bbar',
    			store : store,
    			baseCls : me.ipageBaseCls, // 扩展自定义pagingtoolbar样式的能力
    			dockedItems : me.ipageItems,
    			iAutoSelect : me.ipageAutoSelect,
    			ipageSize : me.ipageSize,
    			idisplayMsg : me.idisplayMsg
    		});
    		
    		me.ipage = pageBar;// 通过ipage自定义属性，来获取自定义的分页控件
    		
    		var items  = me.dockedItems;
    		
    		// 由于当前控件不确定是否已经配置了其他控件，所以需要对其他控件可能存在的情况做兼容。
    		items = items == undefined?(new Array()):items;
    		
    		items.push(pageBar);
    		
    		// 处理panel上面dockItems内的控件
    		if(Ext.isDefined(me.dockedItems))
    		{
    			me.iAddListenerItems(me.dockedItems);
    		}
    		
    		// 处理panel上面items的控件
    		if(Ext.isDefined(me.items))
    		{
    			me.iAddListenerItems(me.items);
    		}
            
            Ext.apply(this, {
                //height: 300,
                height: 300,
                frame : true,
                store: store,
                columns: columns1,
                dockedItems : items,
            });

            this.callParent(arguments);
    	}
    	
    },
});
