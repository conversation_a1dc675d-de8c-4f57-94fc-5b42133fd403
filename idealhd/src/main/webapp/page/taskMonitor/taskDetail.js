var createPanlAndWindow;
var flowStatePane;
var upLoadformPane;
var curFlowId = "";
var refreshTaskId=0;
var refreshObj;
var isendid;
var sendParam;
var flowIdList;
var flowIdListBatch;
Ext.Loader.setConfig({
	enabled : true,
	disableCaching : false,
	paths : {
		'Go' : 'js/ux/gooo'
	}
});
Ext.onReady(function(){
	destroyRubbish();
	// 鼠标悬浮提醒用
	Ext.QuickTips.init();
	createPanlAndWindow = function() {
	}
	var itemWidth = contentPanel.getWidth()*0.23;
	Ext.define('sysTypeModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iid',
			type : 'string'
		}, {
			name : 'ibusname',
			type : 'string'
		}]

	});

	var stateStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "0",
			"name" : "运行"
		}, {
			"id" : "2",
			"name" : "结束"
		}, {
			"id" : "4",
			"name" : "终止"
		}, {
			"id" : "25",
			"name" : "异常终止"
		}, {
			"id" : "2,4,25",
			"name" : "非运行"
		} ]
	});
	var startTime_t = Ext.create('Go.form.field.DateTime', {
		fieldLabel : '开始时间',
		format : 'Y-m-d H:i:s',
		labelWidth : 65,
		emptyText : '-请选择开始时间-',
		labelAlign : 'right',
		width:'20%',
//		margin : '10 5 0 10',
		id : 'startTime'
	});
	var endTime_t = Ext.create('Go.form.field.DateTime', {
		fieldLabel : '至',
		format : 'Y-m-d H:i:s',
		labelWidth : 20,
		labelAlign : 'right',
		emptyText : '-请选择结束时间-',
		width:'17%',
//		margin : '0 0 0 10',
		id : 'endTime'
	});
	var taskDetailForm = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		border : false,
		collapsible : false,//可收缩
		collapsed : false,//默认收缩
		dockedItems: [{
			xtype : 'toolbar',
			border : false,
			baseCls:'customize_gray_back',
			dock : 'top',	    	
			items:[
				{
					readOnly:true,
					fieldLabel: '任务名称',
					labelAlign : 'right',
					labelWidth : 65,
//					margin : '10 10 0 10',
					name: 'isysname',
					value:itaskName,
					width:'23%',
					xtype: 'textfield'
				},{
					readOnly:true,
					fieldLabel: '开始时间',
					xtype : 'textfield',
					labelAlign : 'right',
					labelWidth : 65,
//					margin : '10 20 0 10',
					width:'23%',
					name: 'istarttime',
					id: 'istarttime',
					format : 'Y-m-d H:i:s'
				},{
					readOnly:true,
					fieldLabel : '结束时间',
					xtype : 'textfield',
					labelAlign : 'right',
					labelWidth : 65,
//					margin : '10 20 0 10',
					width:'23%',
					name: 'iendtime',
					id: 'iendtime',	      
					format : 'Y-m-d H:i:s'
				},{
					readOnly:true,
					fieldLabel: '设备数量',
					labelAlign : 'right',
					labelWidth : 65,
					name: 'serverNum',
					id: 'serverNum',
					width:'18%',
					xtype: 'textfield'
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function() {
						switchrunins_grid.ipage.moveFirst();
					}
				} ]
		}]
	});

	if (cebTMDEditSwitch == true) {
		var startTime_t = Ext.create('Go.form.field.DateTime', {
			fieldLabel: '开始时间',
			format: 'Y-m-d H:i:s',
			labelWidth: 65,
			emptyText: '-请选择开始时间-',
			labelAlign: 'right',
			width: itemWidth,
//		margin : '10 5 0 10',
			id: 'startTime'
		});
		var endTime_t = Ext.create('Go.form.field.DateTime', {
			fieldLabel: '至',
			format: 'Y-m-d H:i:s',
			labelWidth: 20,
			labelAlign: 'right',
			emptyText: '-请选择结束时间-',
			width: itemWidth,
//		margin : '0 0 0 10',
			id: 'endTime'
		});
		//serverIp
		var serverIp_t = new Ext.form.TextField(
			{
				id: 'serverIP',
				name: 'serverIP',
				padding: '5 0 10 0',
				fieldLabel: 'IP',
				labelAlign: 'right',
				labelWidth: 65,
				width: itemWidth
			});

		// 运行中，结束，终止，活动异常，超时
		var statusStore = Ext.create('Ext.data.Store', {
			fields: ['id', 'name'],
			data: [{
				"id": "0",
				"name": "运行"
			}, {
				"id": "2",
				"name": "结束"
			}, {
				"id": "4",
				"name": "终止"
			}, {
				"id": "25",
				"name": "活动异常"
			}, {
				"id": "3",
				"name": "待运行"
			}, {
				"id": "5",
				"name": "超时"
			}]
		});

		/** 状态-Combo */
		var statusComboBox = Ext.create('Ext.form.ComboBox', {
			fieldLabel: '状态',
			labelAlign: 'right',
			labelWidth: 65,
			editable: false,
			store: statusStore,
			queryMode: 'local',
			width: itemWidth,
			triggerAction: 'all',
			displayField: 'name',
			valueField: 'id'
		});
		taskDetailForm = Ext.create('Ext.ux.ideal.form.Panel', {
			region: 'north',
			layout: 'anchor',
			buttonAlign: 'center',
			border: false,
			collapsible: false,//可收缩
			collapsed: false,//默认收缩
			dockedItems: [{
				xtype: 'toolbar',
				border: false,
				baseCls: 'customize_gray_back',
				dock: 'top',
				items: [
					{
						readOnly: true,
						fieldLabel: '任务名称',
						labelAlign: 'right',
						labelWidth: 65,
//					margin : '10 10 0 10',
						name: 'isysname',
						value: itaskName,
						width: itemWidth,
						xtype: 'textfield'
					}, {
						readOnly: true,
						fieldLabel: '开始时间',
						xtype: 'textfield',
						labelAlign: 'right',
						labelWidth: 65,
//					margin : '10 20 0 10',
						width: itemWidth,
						name: 'istarttime',
						id: 'istarttime',
						format: 'Y-m-d H:i:s'
					}, {
						readOnly: true,
						fieldLabel: '结束时间',
						xtype: 'textfield',
						labelAlign: 'right',
						labelWidth: 65,
//					margin : '10 20 0 10',
						width: itemWidth,
						name: 'iendtime',
						id: 'iendtime',
						format: 'Y-m-d H:i:s'
					}, {
						readOnly: true,
						fieldLabel: '设备数量',
						id: 'serverNum',
						name: 'serverNum',
						labelAlign: 'right',
						width: itemWidth,
						labelWidth: 65,
						fieldCls: 'x-form-item-label',
						xtype: 'displayfield',
					}]
			}, {
				xtype: 'toolbar',
				dock: 'top',
				border: false,
				baseCls: 'customize_gray_back',
				items: [serverIp_t, statusComboBox, {
					xtype: 'button',
					cls: 'Common_Btn',
					text: '查询',
					handler: function () {
						switchrunins_grid.ipage.moveFirst();
					}
				}]
			}]
		});
	}

	function queryWhere(){
			switchrunins_grid.ipage.moveFirst();
			upLoadformPane.hide( );
			flowStatePane.setTitle("IP【】的详细信息");
	}
	var switchrunins_form = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		border : false,
		collapsible : true,//可收缩
		collapsed : true,//默认收缩
		iqueryFun : queryWhere,
		iselect : false,
		dockedItems: [
			{
				xtype : 'toolbar',
				border : false,
				baseCls:'customize_gray_back',
				dock : 'top',
				items:[{
					fieldLabel: 'IP',
					labelAlign : 'right',
					labelWidth : 65,
					emptyText :  '-请输入IP-',
//					margin : '10 10 0 10',
					name: 'serverIP',
					id: 'serverIP',
					width:'14%',
					xtype: 'textfield'
				}, {
					xtype : 'combobox',
					id : 'status',
					fieldLabel : '状态',
					emptyText :  '-请选择-',
					displayField : 'name',
					valueField : 'id',
					store : stateStore,
					queryMode : 'local',
					editable : false,
					width : '12%',
//					margin : '5',
					labelAlign : 'right',
					labelWidth : 50
				},startTime_t, endTime_t,'->',{
					xtype : 'button',
					cls : 'Common_Btn',
					hidden : !flagbutton,
					text : '启动',
					handler : function() {
						selectStartTask();
					}
				},
					{
					xtype : 'button',
					//	columnWidth:.07,
					cls : 'Common_Btn',
					text : '查询',
					handler : function() {
						switchrunins_grid.ipage.moveFirst();
						//refreshSwitchMonitor();
						upLoadformPane.hide( );
						flowStatePane.setTitle("IP【】的详细信息");
					}
					},{
						xtype : 'button',
						//	columnWidth:.07,
						text : '清空',
						cls : 'Common_Btn',
						handler : function() {
							clearQueryWhere();
						}
						},{
							xtype : 'button',
							//	columnWidth:.07,
							text : '批量重试',
							cls : 'Common_Btn',
							hidden : true,
							handler : function() {
								batchRetryOper();
							}
						},{
							xtype : 'button',
							//	columnWidth:.07,
							text : '批量暂停',
							cls : 'Common_Btn',
							hidden:isHiddenKillBtn,
							handler : function() {
								pauseBtnFun(0);
							}
						},{
							xtype : 'button',
							//	columnWidth:.07,
							text : '批量恢复',
							cls : 'Common_Btn',
							hidden:isHiddenKillBtn,
							handler : function() {
								resumeBtnFun(0);
							}
						},{
							xtype : 'button',
							//	columnWidth:.07,
							text : '批量终止',
							cls : 'Common_Btn',
							hidden:isHiddenKillBtn,
							handler : function() {
								killBtnFun(0);
							}
					}
					]
			}],
			listeners:{
				'collapse': function(node,e){
					var th = taskDetailForm.getHeight();
					//var sh = switchrunins_form.getHeight();
					upLoadformPane.setHeight (contentPanel.getHeight()-th/*-sh*/);
				},
				'expand' : function(node,e){
					var th = taskDetailForm.getHeight();
					//var sh = switchrunins_form.getHeight();
					upLoadformPane.setHeight (contentPanel.getHeight ()-th/*-sh*/-50);
				}
			}
	});

	Ext.define('taskDetailGridModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'taskId',
			type : 'string'
		}, {
			name : 'taskType',
			type : 'string'
		}, {
			name : 'startUser',
			type : 'string'
		}, {
			name : 'insName',
			type : 'string'
		}, {
			name : 'taskName',
			type : 'string'
		}, {
			name : 'sonTaskName',
			type : 'string'
		}, {
			name : 'serverIp',
			type : 'string'
		}, {
			name : 'startTime',
			type : 'string'
		}, {
			name : 'endTime',
			type : 'string'
		}, {
			name : 'runTime',
			type : 'string'
		}, {
			name : 'status',
			type : 'string'
		},{
			name : 'butterflyVersion',
			type : 'string'
		},  {
			name : 'pic',
			type : 'string'
		},
		{
			name : 'systemCode',
			type : 'string'
		},
		{
			name : 'completePercent',
			type : 'string'
		},
		{
			name : 'flowDes',
			type : 'string'
		},
		{
			name : 'cpName',
			type : 'string'
		},
		{
			name : 'exceptionDesc',
			type : 'string'
		}
		]
	});
	var switchrunins_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize:20,
		model: 'taskDetailGridModel',
		proxy: {
			type: 'ajax',
			url: 'queryTaskDetail.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	var switchrunins_columns = [{ text: '序号', xtype:'rownumberer', width: 60 },
		{text : 'ID', sortable : true, dataIndex : 'taskId',hidden:true},
		{ text: 'IP', dataIndex: 'serverIp', flex:1,
			// 鼠标悬浮提醒
			/*renderer : function (value, metaData, record, colIndex, store, view)
			{
				var startTime = record.data.startTime;
				var endTime = record.data.endTime;
				var tipStr = "开始时间:"+startTime+"\n 结束时间:"+endTime;
				metaData.tdAttr = 'data-qtip="' + tipStr + '"';
				return value;
			}*/
		},
		{ text: '主机名', dataIndex: 'cpName', flex:1,
			// 鼠标悬浮提醒
			/*renderer : function (value, metaData, record, colIndex, store, view)
			{
				var startTime = record.data.startTime;
				var endTime = record.data.endTime;
				var tipStr = "开始时间:"+startTime+"\n 结束时间:"+endTime;
				metaData.tdAttr = 'data-qtip="' + tipStr + '"';
				return value;
			}*/
		},
		{text : '状态',  flex:1,
			sortable : true, 
			dataIndex : 'status' 
				,renderer: function(value,metaData,record){
					var startTime = record.data.startTime;
					var endTime = record.data.endTime;
					var tipStr = "开始时间:"+startTime+"\n 结束时间:"+endTime;
					metaData.tdAttr = 'data-qtip="' + tipStr + '"';
					if(value=="运行") {
						return "<span class='Green_color State_Color'>"+ value +"</span>";
					}
					if(value=="提醒") {
						return "<span class='Green_color State_Color'>"+ value +"</span>";
					}
					else if(value=="结束") {
						return "<span class='Gray_color State_Color'>"+ value +"</span>";
					} 
					else if(value=="终止") {
						return "<span class='Red_color State_Color'>"+ value +"</span>";
					}
					else if(value=="暂停") {
						return "<span class='Blue_color State_Color'>"+ value +"</span>";
					} 
					else if(value=="异常终止") {
						return "<span class='yellow_color State_Color'>"+ value +"</span>";
					}
					else if(value=="活动异常") {
						return "<span class='yellow_color State_Color'>"+ value +"</span>";
					}
					else if(value=="灾难恢复中") {
						return "<span class='purple_color State_Color'>"+ value +"</span>";
					} 
					else if(value=="灾难恢复失败") {
						return "<span class='orange_color State_Color'>"+ value +"</span>";
					}else if(value=="启动失败") {
						return "<span class='orange_color State_Color'>"+ value +"</span>";
					}else{
						return value;
					}
				}
		},
		{ text: '完成百分比',  dataIndex: 'completePercent', flex:1,
			/*renderer : function (value, metaData, record, colIndex, store, view)
			{
				var startTime = record.data.startTime;
				var endTime = record.data.endTime;
				var tipStr = "开始时间:"+startTime+"\n 结束时间:"+endTime;
				metaData.tdAttr = 'data-qtip="' + tipStr + '"';
				return value;
			}*/
		},{
				text : '开始时间',
				dataIndex : 'startTime',
				flex:2
			},{
				text : '结束时间',
				dataIndex : 'endTime',
				flex:2
			},
			{ text: '备注',  dataIndex: 'flowDes', flex:1,
				/*renderer : function (value, metaData, record, colIndex, store, view)
				{
					var startTime = record.data.startTime;
					var endTime = record.data.endTime;
					var tipStr = "开始时间:"+startTime+"\n 结束时间:"+endTime;
					metaData.tdAttr = 'data-qtip="' + tipStr + '"';
					return value;
				}*/
			}
			];

	switchrunins_store.on('beforeload', function (store, options) {
		var new_params = {
			taskId:itaskid,
			serverIp:Ext.getCmp("serverIP").getValue(),
			runState:Ext.getCmp('status').getValue(),
			startTime:Ext.getCmp('startTime').getValue(),
			endTime:Ext.getCmp('endTime').getValue()
		};
		if (cebTMDEditSwitch == true) {
			var serverIp = serverIp_t.getValue().trim();
			var status = statusComboBox.getValue();
			new_params = {
				taskId:itaskid,
				serverIp:serverIp,
				// runState:Ext.getCmp('status').getValue(),
				runState:status,
				startTime:Ext.getCmp('startTime').getValue(),
				endTime:Ext.getCmp('endTime').getValue()
			};
		}

		Ext.apply(switchrunins_store.proxy.extraParams, new_params);
	});
	switchrunins_store.on('load', function (store,records,successful,options) {
		var data = store.getProxy().getReader().jsonData;
		Ext.getCmp("istarttime").setValue(data.minStartTime);
		Ext.getCmp("iendtime").setValue(data.maxEndTime);
		Ext.getCmp("serverNum").setValue(data.serverNum);
		if(store.getCount()>0){
			var record = store.getAt(0);
			selModel.select(record);// 选中记录
			}
	});

	var pageBar = Ext.create('Ext.PagingToolbar', {
		store: switchrunins_store,
		dock: 'bottom',
		displayInfo: true
	});

	var upLoadformPaneBbar = Ext.create('Ext.toolbar.Toolbar', {
		dock: 'bottom',
		hidden: true,
		items: [
			{
				fieldLabel : '刷新时间(秒)',
				labelAlign : 'right',
				labelWidth : 90,
				name : 'refreshTime',
				id :'refreshTime',
				value : interval,
				width : 130,
				xtype : 'textfield'
			},
			{
				xtype : 'button',
				margin : '0 3 0 0',
				cls : 'Common_Btn_small',
				text : '刷新',
				//width : 70,
				handler : function() {
					if (refreshObj) {
						clearInterval(refreshObj);//
					}
					var refreshTime = Ext.getCmp("refreshTime")
					.getValue();
					interval = refreshTime;
					refreshObj = setInterval(
							refreshSwitchMonitor,
							parseInt(refreshTime) * 1000);
					//setInterval(函数，每多少秒执行一次)
					refreshSwitchMonitor();
				}
			}, {
				xtype : 'button',
				margin : '0 3 0 0',
				cls : 'Common_Btn_small',
				hidden:isHiddenKillBtn,
				text : '暂停',
				//width : 70,
				handler : function() {
					pauseBtnFun(1);
				}
			}, {
				xtype : 'button',
				margin : '0 3 0 0',
				cls : 'Common_Btn_small',
				text : '恢复',
				hidden:isHiddenKillBtn,
				//width : 70,
				handler : function() {
					resumeBtnFun(1);
				}
			}, {
				xtype : 'button',
				margin : '0 3 0 0',
				cls : 'Common_Btn_small',
				hidden:isHiddenKillBtn,
				text : '终止',
				//width : 70,
				handler : function() {
					killBtnFun(1);
				}
			}, {
				xtype : 'button',
				margin : '0 3 0 0',
				cls : 'Common_Btn_small', 
				text : '日志展示',
				//width : 90,
				handler : function() {
					getFinishSehllCmdStdoutFromFlowId();
				}
			}
		]
	});
	
	var logShow = Ext.create('Ext.toolbar.Toolbar', {
		dock: 'bottom',
		hidden: true,
		items: [
			  {
				xtype : 'button',
				margin : '0 5 0 0',
				cls : 'Common_Btn', 
				text : '日志展示',
//				width : 85,
				handler : function() {
					getFinishSehllCmdStdoutFromFlowId();
				}
			}
		]
	});
	upLoadformPane = Ext.create('Ext.form.Panel', {
		region : 'north',
		autoScroll : true,
		border : false,
		width : '100%',
		height: contentPanel.getHeight()-175,
		id : 'upLoadpanel',
		bodyStyle : 'background:#fff;',
		loader : {
			url : 'flow/showflowState.do',
			autoLoad : false,
			scripts : true
		},
		dockedItems: [
			upLoadformPaneBbar,logShow
			]
	});
	flowStatePane = Ext.create('Ext.panel.Panel', {
		region : 'center',
		title:'IP【】的详细信息',
		//autoScroll : true,
		maximizable : true,
		id : 'flowStatePane',
		cls:'customize_panel_back  panel_space_left ip_details',
		//bodyStyle : 'background:#fff;' ,
			tools:[{
				xtype: 'tool',
				handler: function(event, toolEl, owner, tool) {

					if(!owner.maximize)
					{
						//flowStatePane.setHeight(contentPanel.getHeight ()-48);
						if(refreshTaskId>0){
							iniSwitchMonitor();
							upLoadformPane.doLayout();
							upLoadformPane.setWidth(contentPanel.getWidth ()-25);
							owner.restoreSize = owner.getSize();
							owner.maximize = true;
							switchrunins_grid.hide();
						}else{
							Ext.MessageBox.alert("提示", "请选择设备！");
						}
					}else{
						switchrunins_grid.show();
						upLoadformPane.setWidth(contentPanel.getWidth ()-switchrunins_grid.getWidth () - 10);
//						refreshSwitchMonitor();
						iniSwitchMonitor();
						upLoadformPane.doLayout();
						owner.maximize = false;
						owner.setSize(owner.restoreSize);
					}

				},
				type: 'restore'
			}],
			items : [ upLoadformPane ]

	});
	
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
				if(selections.length>0){
				 var record=selections[0];
					if(record.index==0){
						flowIdList = record;
						flowIdListBatch = record;
						var taskId = record.data.taskId;
						if(taskId==""||taskId==null){
							taskId=0;
						}
						refreshTaskId = taskId;
						var serverip = record.data.serverIp;
						var status = record.data.status;
						var exceptionDesc = record.data.exceptionDesc;
						flowStatePane.setTitle("IP【"+serverip+"】的详细信息");
						if(taskId>0){
							//showFlowState(10261);
							createPanlAndWindow();
							var forword_url = 'flow/showflowState.do';
							upLoadformPane.getLoader().load({
								url : forword_url,
								params : {
									flowId : taskId,
									sysType : itype,
									interval : interval,
									pageType : '0'
		//								closeTime : closeTime
								},
								scripts : true
							});
							upLoadformPane.doLayout();
							upLoadformPane.show( );
							showHiddenButton(status);
						}else
						{
							if(exceptionDesc==null || exceptionDesc==""){
								Ext.MessageBox.alert("提示", "设备【"+serverip+"】尚未启动成功！");
							} else {
								Ext.MessageBox.alert("提示", exceptionDesc);
							}
						}
					}
			  	}
			}
		}
	});
	var switchrunins_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		//height: contentPanel.getHeight()-95,
		width : contentPanel.getWidth ()/3+305,
//		height : contentPanel.getHeight () - 35,
		region : 'west',
		layout:'fit',
		store:switchrunins_store,
		cls:'customize_panel_back',
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',  
		border:false,
		columnLines : true,
		columns:switchrunins_columns,
		//bbar: pageBar,
	    selModel : selModel,
		viewConfig:{
			getRowClass:function(record,rowIndex,rowParams,arriveStore){
				var cls = '';
				if(record.data.iisfail=='0'){
					if(record.data.istate=='0'){
						cls = 'row_Blue';
					}else if(record.data.istate=='1'){
						cls = 'row_Green';
					}else if(record.data.istate=='2'){
						cls = 'row_Gray';
					}
				}else{
					cls = 'row_Red';
				}
				return cls; 
			}
		},
		listeners :
		{
//			beforeitemclick:function (view, record, item, index, e, eOpts)
//			{
//				switchrunins_grid.getSelectionModel().select(record);
//			},
			itemclick : function (view, record, item, index, e, eOpts)
			{
				flowIdList = record;
				flowIdListBatch = record;
				var taskId = record.data.taskId;
				if(taskId==""||taskId==null){
					taskId=0;
				}
				refreshTaskId = taskId;
				var serverip = record.data.serverIp;
				var status = record.data.status;
				var exceptionDesc = record.data.exceptionDesc;
				flowStatePane.setTitle("IP【"+serverip+"】的详细信息");
				if(taskId>0){
					//showFlowState(10261);
					createPanlAndWindow();
					var forword_url = 'flow/showflowState.do';
                    console.log(upLoadformPane)
					upLoadformPane.getLoader().load({
						url : forword_url,
						params : {
							flowId : taskId,
							sysType : itype,
							interval : interval,
							pageType : '0'
//								closeTime : closeTime
						},
						scripts : true
					});
					upLoadformPane.doLayout();
					upLoadformPane.show();
                    console.log(upLoadformPane);
                    console.log(status)
					showHiddenButton(status);
				}else
				{
					if(exceptionDesc==null || exceptionDesc==""){
						Ext.MessageBox.alert("提示", "设备【"+serverip+"】尚未启动成功！");
					} else {
						Ext.MessageBox.alert("提示", exceptionDesc);
					}
				}
			}
		}
	});

	contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
	//switchrunins_form.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
	taskDetailForm.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
 
	// 界面主Panel
	var rightPanel = Ext.create ("Ext.panel.Panel",
			{
		layout : 'border',
		width : '100%',
		height : contentPanel.getHeight ()- 10,
		cls:'customize_panel_header_arrow',      
		border : false,
		items : [/*switchrunins_form,*/taskDetailForm,
			{
			layout:'border',
			xtype:'panel',
			region : 'center',
			items:[switchrunins_grid,flowStatePane]
			}
		],
		renderTo : "taskDetailDivRunning"
			});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
		{
		Ext.destroy (rightPanel);
		contentPanel.getHeader().show();
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
			});
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		rightPanel.setHeight (contentPanel.getHeight () - 2);
		rightPanel.setWidth (contentPanel.getWidth () );
	});


	function clearQueryWhere(){
		switchrunins_form.form.reset();
	}
 
	var selModel = switchrunins_grid.getSelectionModel();
	// 强行终止按钮触发事件
	function killBtnFun(flag) {
		if(flag == 0){
			var seledCnt = selModel.getCount();
			if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要终止的工作流");
				return;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认终止", "是否终止选中的工作流", function(id) {
			if (id == 'yes')
				killFlowExecute(flag);
		});
	}
	// 暂停按钮触发事件
	function pauseBtnFun(flag) {
		if(flag == 0){
			var seledCnt = selModel.getCount();
			if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要暂停的工作流");
				return;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认暂停", "是否暂停选中的工作流", function(id) {
			if (id == 'yes')
				pauseFlowExecute(flag);
		});
	}
	// 继续按钮触发事件
	function resumeBtnFun(flag) {
		if(flag == 0){
			var seledCnt = selModel.getCount();
			if (seledCnt < 1) {
			Ext.MessageBox.alert("提示", "请选择要恢复运行的工作流");
				return;
			}
		}
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm("确认继续", "是否恢复运行选中的工作流", function(id) {
			if (id == 'yes')
				resumeFlowExecute(flag);
		});
	}
	// 强行终止按钮执行方法
	function killFlowExecute(flag) {
		  
		var jsonData = getSelectedFlowId("运行,暂停,活动异常,异常终止,灾难恢复中,灾难恢复失败,启动失败",flag);
		if (jsonData.indexOf("{") == -1) {
			Ext.MessageBox.alert("提示", "工作流：" + jsonData
					+ " 的状态不符合【强行终止】操作的条件，请重新选择工作流！");
			return;
		}
		Ext.Ajax.request({
			url : 'icTaskHistory/killFlow.do',
			method : 'POST',
			params : {
				jsonData : jsonData
			},
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					switchrunins_store.load();
					Ext.MessageBox.show({
						title : "提示",
						msg : "工作流终止成功!",
						buttonText : {
							yes : '确定'
						},
						buttons : Ext.Msg.YES
					});
				} else {
					Ext.MessageBox.show({
						title : "提示",
						msg : "工作流终止失败!",
						buttonText : {
							yes : '确定'
						},
						buttons : Ext.Msg.YES
					});
				}
				//queryBtnFun();
			},
			failure : function(result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
	}

	// 暂停按钮执行方法
	function pauseFlowExecute(flag) {
		var jsonData = getSelectedFlowId("运行",flag);
		if (jsonData != undefined) {
			if (jsonData.indexOf("{") == -1) {
				Ext.MessageBox.alert("提示", "工作流：" + jsonData
						+ " 的状态不符合【暂停】操作的条件，请重新选择工作流！");
				return;
			}
			Ext.Ajax.request({
				url : 'icTaskHistory/pauseFlow.do',
				method : 'POST',
				params : {
					jsonData : jsonData
				},
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						switchrunins_store.load();
						Ext.MessageBox.show({
							title : "提示",
							msg : "工作流暂停成功!",
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					} else {
						Ext.MessageBox.show({
							title : "提示",
							msg : "工作流暂停失败!",
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					}
					//queryBtnFun();
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		} else {
			Ext.MessageBox.show({
				title : "提示",
				msg : "请选择要暂停的工作流!",
				buttonText : {
					yes : '确定'
				},
				buttons : Ext.Msg.YES
			});
		}
	}

	// 继续按钮执行方法
	function resumeFlowExecute(flag) {
		var jsonData = getSelectedFlowId("暂停",flag);
		if (jsonData != undefined) {
			if (jsonData.indexOf("{") == -1) {
				Ext.MessageBox.alert("提示", "工作流：" + jsonData
						+ " 的状态不符合【恢复】操作的条件，请重新选择工作流！");
				return;
			}
			Ext.Ajax.request({
				url : 'icTaskHistory/resumeFlow.do',
				method : 'POST',
				params : {
					jsonData : jsonData
				},
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						switchrunins_store.load();
						Ext.MessageBox.show({
							title : "提示",
							msg : "工作流恢复运行成功!",
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					} else {
						Ext.MessageBox.show({
							title : "提示",
							msg : "工作流恢复运行失败!",
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					}
					//queryBtnFun();
				},
				failure : function(result, request) {
					secureFilterRs(result, "操作失败！");
				}
			});
		} else {
			Ext.MessageBox.show({
				title : "提示",
				msg : "请选择要暂停的工作流!",
				buttonText : {
					yes : '确定'
				},
				buttons : Ext.Msg.YES
			});
		}
	}

	// 将被选中的记录的flowid组织成json串，作为参数给后台处理
	function getSelectedFlowId(flowstate,flag) {
		var isSign = 0;
		if(flag == 0){
	  		flowIdList = switchrunins_grid.getSelectionModel().getSelection();
	  		if (flowIdList.length < 1) {
				return;
			}
			for (var i = 0, len = flowIdList.length; i < len; i++) {
				if (flowstate.indexOf(parsIIDJson('status', flowIdList[i].data)) == -1) {
					isSign = parsIIDJson('taskId', flowIdList[i].data);
					break;
				}
			}
			//taskId 为"",证明 没有启动状态为待启动,直接返回 isSign
			//gang_wang
			if (isSign != 0 || isSign==="") {
				return isSign;
			}
			var jsonData = "[";
			for (var i = 0, len = flowIdList.length; i < len; i++) {
				if (i == 0) {
					jsonData = jsonData + '{"flowid":"'
					+ parsIIDJson('taskId', flowIdList[i].data) + '"}';
				} else {
					jsonData = jsonData + "," + '{"flowid":"'
					+ parsIIDJson('taskId', flowIdList[i].data) + '"}';
				}
			}
		}else{
			if (flowstate.indexOf(parsIIDJson('status', flowIdList.data)) == -1) {
				isSign = parsIIDJson('taskId', flowIdList.data);
			}
			if (isSign != 0) {
				return isSign;
			}
			var jsonData = "[";
				jsonData = jsonData + '{"flowid":"'
				+ parsIIDJson('taskId', flowIdList.data) + '"}';
		} 
		jsonData = jsonData + "]";
		return jsonData;
	}

	// 从一个json对象中，解析出key=iid的value,返回改val
	function parsIIDJson(key, jsonObj) {
		//var eValue = eval('jsonObj.' + key);
		return jsonObj['' + key + ''];
	}

	function showHiddenButton(status){
		if("结束"==status||"终止"==status||"启动失败"==status){
			upLoadformPaneBbar.hide();
			logShow.show();
		}else{
			upLoadformPaneBbar.show();
			logShow.hide();
		}
	}
	function iniSwitchMonitor() {
		createPanlAndWindow();
		var forword_url = 'flow/showflowState.do';
		if(""==refreshTaskId||null==refreshTaskId){
			refreshTaskId = 0;
			//Ext.MessageBox.alert("提示", "请选择设备！");
			return false;
		}
		upLoadformPane.getLoader().load({
			url : forword_url,
			params : {
				flowId : refreshTaskId,
				sysType : itype,
				pageType : '0',
				interval : interval
//				closeTime : closeTime
			},
			scripts : true
		});
		upLoadformPane.doLayout();
	}

	function refreshSwitchMonitor() {
		console.log('======');
		console.log(GRAPHS['viewor']);
		console.log('======');
		GRAPHS['viewor'].freshStatus();
	}
    
    /**
     * 批量重试操作
     */
    function batchRetryOper() {
    	Ext.MessageBox.confirm('提示', '确定要批量重试异常设备吗?', callBack);
    	function callBack(id) {
    		if (id == 'yes') {
    			Ext.Ajax.request({
    				url : 'batchRetryForDailyOprations.do',
    				method : 'POST',
    				params : {
    					taskId : itaskid
    					
    				},
    				success : function(response, opts) {
    					var message = Ext.decode(response.responseText).message;
						Ext.MessageBox.show({
							title : "提示",
							msg : message,
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
    				},
    				failure : function(response, opts) {
    					Ext.Msg.alert('提示', "批量操作请求未成功,请重新操作");
    				}
    			});
    		} else {
    			return;
    		}
    	}
    }
    
    /**
     * 批量略过操作
     */
    function batchSkipOper() {
    	Ext.MessageBox.confirm('提示', '确定要批量重试异常设备吗?', callBack);
    	function callBack(id) {
    		if (id == 'yes') {
    			Ext.Ajax.request({
    				url : 'batchRetryForDailyOprations.do',
    				method : 'POST',
    				params : {
    					taskId : itaskid
    					
    				},
    				success : function(response, opts) {
    					var message = Ext.decode(response.responseText).message;
						Ext.MessageBox.show({
							title : "提示",
							msg : message,
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
    				},
    				failure : function(response, opts) {
    					Ext.Msg.alert('提示', "批量操作请求未成功,请重新操作");
    				}
    			});
    		} else {
    			return;
    		}
    	}
    }
    
    var shellInfoPanel = Ext.create('Ext.form.Panel', {
    	layout : 'anchor',
    	buttonAlign : 'center',
    	border : false,
    	width : contentPanel.getWidth()-400,
    	baseCls:'customize_gray_back',
	    items: [{
	    	layout:'column',
	    	anchor:'95%',
	    	padding : '5 0 5 0',
	    	border : false,
	    	items: [
	    	  {
				fieldLabel : '活动详细输出  ',
				name : 'actStdOut',
				xtype : 'textarea',
//				padding : '5 0 5 5',
				labelAlign : 'right',
	            labelWidth : 93,
	            width : contentPanel.getWidth()-450,
	            height : contentPanel.getHeight()-200,
				anchor : '100%'
			}]
	    }]
	});   
	var prjWindow = Ext.create('Ext.Window', {
		title : '活动详细输出',
		width : contentPanel.getWidth()-400,
	    height : contentPanel.getHeight()-120,
		closeAction : 'hide',
		layout : 'form',
		resizable : false,
		modal : true,
		items : [ shellInfoPanel ]
	});
function getFinishSehllCmdStdoutFromFlowId(){
	if(typeof(flowIdList.data) == 'undefined' || flowIdList == null){
		flowIdList = flowIdListBatch;
	}
	 var taskId = flowIdList.data.taskId;
	 Ext.Ajax.request({
			url : 'getFinishSehllCmdStdoutFromFlowId.do',
			params : {
				flowId : taskId
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				shellInfoPanel.getForm().findField("actStdOut").setValue(msg.stdout);
				prjWindow.show();
			},
			failure : function(response, opts) {
			}
		});
	}
	
	//选择触发执行启动方法
	//gang_wang
	function selectStartTask () {
		var m = switchrunins_grid.getSelectionModel().getSelection();
		var flag = true;
		if(m.length == 0){
			Ext.MessageBox.alert("提示", "请选择数据！");
			return ;
		}else {
			var ips = new Array();
			Ext.Array.each (m, function (record)
			{
				var ip = record.get ('serverIp');
				var status = record.get ('status');
				if(status!="待运行"){
					flag = false;
					Ext.Msg.alert('提示', "不能重复启动!");
					return ;
				}
				if (ip!=""&&null!=ip)
				{
					ips.push ("'" + ip + "'");
				}
			});
			if(!flag){
				return;
			}
			Ext.Ajax.request (
					{
					    url : 'selectStartTask.do',
					    params :
					    {
					    	//后台启动流程之前先判断设备是否是全部是待启动
					    	ips : ips.join (','),
					    	itaskid : itaskid
					    },
					    method : 'POST',
					    success : function (response, opts)
					    {
						    var success = Ext.decode (response.responseText).success;
						    if (success)
						    {
						    	var message = Ext.decode(response.responseText).message;
						    	Ext.Msg.alert('提示', message);
						    	setTimeout(function(){
						    		switchrunins_grid.ipage.moveFirst();
						    	},1000)
						    }
						    else
						    {
						    	Ext.Msg.alert('提示', "启动任务失败!");
						    }
					    }
					});
			
		}
	}

});

function forwardruninfo(iruninsid){
	contentPanel.getLoader().load({url: "forwardswitchruninfoins_ep.do?temp="+new Date().getTime(),scripts: true,params : {iruninsid:iruninsid}});
}
