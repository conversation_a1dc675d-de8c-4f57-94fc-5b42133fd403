<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>切换监控</title>
<link rel="stylesheet" href="<%=request.getContextPath()%>/page/drOverview/css/index_1920.css" />
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/css/custom.css?_dc=<%=new Date().getTime() %>" />
<script type="text/javascript">
	var path='<%=request.getContextPath()%>';
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/jquery-3.4.1.min.js"></script>	
<script type="text/javascript"
	src="<%=request.getContextPath()%>/js/Chart.js"></script>	
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/drOverview/shindex.js"></script>
</head>
<body>
<table cellpadding="0" cellspacing="0" border="0" class="body_bg">
	<tr>
    	<td valign="top">
		<div class="overview_logo">
			<div class="overview_logo_width">
				<span class="PA_logo"></span><span>一键切换指挥平台</span>
			</div>
	    </div>
	    <div class="overview_title">
	    	<marquee direction="left" scrollamount="2" scrolldelay="1" class="marquee_w"><span id="systemMessage"></span></marquee>
	    </div>
	    <div class="overview_map">
	    <div class="overview_table">
	    	<div class="overview_tab_title"><span id="centerNameOne">异地中心</span></div>
	        <div class="overview_con">
	        	<table cellpadding="0" cellspacing="1" border="0">
	            	<tr>
	                	<td><span>系统总数</span></td>
	                    <td id="systemNumOne" align="center">0</td>
	                </tr>
	                <tr>
	                	<td><span>设备总数</span></td>
	                    <td id="stepNumOne" align="center">0</td>
	                </tr>
	                <tr>
	                	<td><span>已完成</span></td>
	                    <td align="center"><div id="finishedOne" class="overview_green">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>切换中</span></td>
	                    <td align="center"><div id="runningOne" class="overview_yellow">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>未开始</span></td>
	                    <td align="center"><div id="noBeginOne" class="overview_gray">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>异常</span></td>
	                    <td align="center"><div id="faultedOne" class="overview_red">0</div></td>
	                </tr>
	            </table>
	        </div>
	    </div>
	    <div class="img1_location">
	        <div class="animation" id="remoteCenter">
	            <div class="dot"><div class="dot_02"></div></div>
	            <div class="pulse"></div>
	        </div>
	    </div>
	    <div class="img2_location">
	        <div class="animation02" id="produceCenter">
	            <div class="dot"><div class="dot_02"></div></div>
	            <div class="pulse"></div>
	        </div>
	    </div>
	    <div class="img3_location">
	        <div class="animation03" id="cityWideCenter">
	            <div class="dot"><div class="dot_02"></div></div>
	            <div class="pulse"></div>
	        </div>
	    </div>
	   
	    <div class="triangle">
	    	<div id="proportion" class="load_value">0%</div>
	    	<span id="proportionCSS">
		    	<div class="triangle_load" style="height:0%; position:relative; top:100%">
		        </div>
	        </span>
	    </div>
	    <div class="overview_table2">
	    	<div class="overview_tab_title"><span id="centerNameTwo">生产中心</span></div>
	        <div class="overview_con2">
	        	<table cellpadding="0" cellspacing="1" border="0">
	            	<tr>
	                	<td><span>系统总数</span></td>
	                    <td id="systemNumTwo" align="center">0</td>
	                </tr>
	                <tr>
	                	<td><span>设备总数</span></td>
	                    <td id="stepNumTwo" align="center">0</td>
	                </tr>
	                <tr>
	                	<td><span>已完成</span></td>
	                    <td align="center"><div id="finishedTwo" class="overview_green">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>切换中</span></td>
	                    <td align="center"><div id="runningTwo" class="overview_yellow">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>未开始</span></td>
	                    <td align="center"><div id="noBeginTwo" class="overview_gray">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>异常</span></td>
	                    <td align="center"><div id="faultedTwo" class="overview_red">0</div></td>
	                </tr>
	            </table>
	        </div>
	    </div>
	    <div class="overview_table3">
	    	<div class="overview_tab_title"><span id="centerNameThree">同城中心</span></div>
	        <div class="overview_con2">
	        	<table cellpadding="0" cellspacing="1" border="0">
	            	<tr>
	                	<td><span>系统总数</span></td>
	                    <td id="systemNumThree" align="center">0</td>
	                </tr>
	                <tr>
	                	<td><span>设备总数</span></td>
	                    <td id="stepNumThree" align="center">0</td>
	                </tr>
	                <tr>
	                	<td><span>已完成</span></td>
	                    <td align="center"><div id="finishedThree" class="overview_green">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>切换中</span></td>
	                    <td align="center"><div id="runningThree" class="overview_yellow">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>未开始</span></td>
	                    <td align="center"><div id="noBeginThree" class="overview_gray">0</div></td>
	                </tr>
	                <tr>
	                	<td><span>异常</span></td>
	                    <td align="center"><div id="faultedThree" class="overview_red">0</div></td>
	                </tr>
	            </table>
	        </div>
	    </div>
	    
	    
	    </div>
	    <div class="overview_chart">
	    	<div class="chart_sb">
	        	<div class="chart_sb_01">
	           		 <div class="chart_list4">
	              		<span class="chart_name">异地</span>
	                  	<span id="remoteStepNum" class="chart_count">0</span>
	                   	<span class="chart_icon3 chart_icon_common"></span>
	                 </div>
	            </div>
	            <div class="chart_sb_02">
	            	<canvas id="chart1"></canvas>
	            	<div class="chart_Font" id="stepSum">
	            	</div>
	            </div>
	            <div class="chart_sb_03">
	            	<div class="chart_list3">
	                	<span class="chart_icon chart_icon_common"></span>
	                	<span class="chart_name">生产</span>
	                    <span id="produceStepNum" class="chart_count">0</span>
	                </div>
	                <div class="chart_list3">
	                	<span class="chart_icon2 chart_icon_common"></span>
	                    <span class="chart_name">同城</span>
	                    <span id="cityWideStepNum" class="chart_count">0</span>
	                </div>
	            </div>
	        </div>
	        <div class="chart_sb">
	        	<div class="chart_sb_04">
	            	<div class="chart_list1">
	                	<span class="chart_name">已完成</span>
	                    <span id="finishedNum" class="chart_count">0</span>
	                    <span class="chart_icon chart_icon_common"></span>
	                </div>
	                <div class="chart_list1">
	                	<span class="chart_name">切换中</span>
	                    <span id="runningNum" class="chart_count">0</span>
	                    <span class="chart_icon2 chart_icon_common"></span>
	                </div>
	            	
	            </div>
	            <div class="chart_sb_02_xt">
	            	<canvas id="chart2"></canvas>
	            	<div class="chart_Font" id="systemSum">
	            	</div>
	            </div>
	            <div class="chart_sb_05">
	            	<div class="chart_list1">
	                	<span class="chart_icon3 chart_icon_common"></span>
	                	<span class="chart_name">未开始</span>
	                    <span id="noBeginNum" class="chart_count">0</span>
	                </div>
	                <div class="chart_list1">
	                	<span class="chart_icon4 chart_icon_common"></span>
	                	<span class="chart_name">异常</span>
	                    <span id="faultedNum" class="chart_count">0</span>
	                </div>
	            </div>
	        </div>
	        <div class="overview_time">
	        	<div class="time_text">耗时</div>
	            <div class="time_run">
	            	<span id="hourOne" class="run_first">0</span>
	                <span id="hourTwo" class="run_two">0</span>
	                <span id="minuteOne" class="run_three">0</span>
	                <span id="minuteTwo" class="run_two">0</span>
	                <span id="secondsOne" class="run_three">0</span>
	                <span id="secondsTwo" class="run_two">0</span>
	            </div>
	        </div>
	    </div>
	
	    <div class="oview_table_info">
	        <table id="systemTable" cellpadding="0" cellspacing="0" border="0" class="oview_table_list">
	            <tr class="tab_info_tr1">
	                <td>应用系统</td>
	                <td>预计开始时间</td>
	                <td>预计结束时间</td>
	                <td>切换类型</td>
	                <td>切换方向</td>
	                <td>开始时间</td>
	                <td>结束时间</td>
	                <td>切换耗时</td>
	                <td>步骤数</td>
	                <td>状态</td>
	            </tr>
	        </table>
	    </div>
	</td>
    </tr>
</table>
<div id="systemInstanceDiv" style="position: absolute;left:0;top:0;z-index: 10000000000000;display: none;" class="screen_body">
<table cellpadding="0" cellspacing="0" border="0" height="100%" align="center" width="1100">
	<tr>
    	<td height="100%">
        	<table cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr>
                    <td class="screen_p1"></td>
                    <td class="screen_p2"></td>
                    <td class="screen_p3"></td>
                </tr>
            </table>
            <table cellpadding="0" cellspacing="0" border="0" width="100%" height="300">
                <tr>
                    <td class="screen_p4"></td>
                    <td class="screen_p5" valign="top">
                    	<div class="scr_title">
                        	<div class="scr_tl_left"><span class="scr_img1"></span><a id ='spanAId'>步骤详情-商业汇票交易管理系统</a></div>
                            <div class="scr_tl_right"><span class="scr_close" onclick = "closeWindow()"></span></div>
                        </div>
                         <div class="screen_table_info">
                         <table cellpadding="0" cellspacing="0" border="0" class="oview_table_list_tl">
                            <tr class="tab_info_tr1">
                                <td class="start_td">并发号</td>
                                <td class="start_td">步骤号</td>
                                <td class="start_td2">步骤描述</td>
                                <td class="start_td3">开始时间</td>
                                <td class="start_td3">结束时间</td>
                                <td class="start_td" style="padding: 0">耗时</td>
                                <td class="start_td">执行状态</td>
                                <td class="start_td">执行结果</td>
                            </tr>
                          </table>
                        <div class="screen_table_info2">
                        <table  id = "systemInstanceTab" cellpadding="0" cellspacing="0" border="0" class="oview_table_list2">
                        </table>
                    </div>
                    </td>
                    <td class="screen_p6"></td>
                </tr>
            </table>
            <table cellpadding="0" cellspacing="0" border="0" width="100%">
                <tr>
                    <td class="screen_p7"></td>
                    <td class="screen_p8"></td>
                    <td class="screen_p9"></td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</div>
</body>
</html>
