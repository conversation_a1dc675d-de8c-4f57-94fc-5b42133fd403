Array.prototype.baoremove = function(dx)
{
    if(isNaN(dx)||dx>this.length){return false;}
    this.splice(dx,1);
}
Ext.onReady(function() {
	// 清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	
	var upldWin = null;
	var upLoadformPane = null;
	var paramNames = '';
	var ftpParamNames = '';
	var hcitemId = 0;
	var paramName_splitStr = '@@';
	var selectValue;
	var IDX_RADIO_TIMED=1;  //
	var IDX_RADIO_CYCLE=2;  //
	/*************************************第一步"任务信息"页panel以及所需的控件*****************************************************/
	// 任务名称
	var taskName = Ext.create('Ext.form.field.Text', {
		width : '100%',
		labelWidth : 65,
		labelAlign : 'right',
		allowBlank : false,
		hidden : nomalTask,
		maxLength : 255,
		maxLengthText : '请输入255位长度的任务名称',
		enforceMaxLength : true, // 是否强制限制字符串长度，超过最大长度将不能再被输入
		name: 'handleTaskName',
		fieldLabel : '任务名称',
		value : $('#ictaskname').val() + $('#iNowDate').val()
	});
	
	/** 步骤快照按钮  **/
	var snapshotBtn = Ext.create ("Ext.Button",
			{
		cls : 'Common_Btn',
		text : "步骤快照",
		//				width : 100,
//	    disabled : true,
		handler : snapshotFunction
	});
	
	/** model* */
	Ext.define ('AuditorModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'loginName',
	                type : 'string'
	            },
	            {
	                name : 'fullName',
	                type : 'string'
	            }
	    ]
	});
	
	/** 审核人store* */
	var auditorStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    model : 'AuditorModel',
//	    extraParams : {
//	    	sysName :$("#ictaskname").val()
//	    },
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getAuditorListForIC.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	Ext.define ('nomalTaskModel',
			{
			    extend : 'Ext.data.Model',
			    fields : [
			            {
			                name : 'IID',
			                type : 'string'
			            },
			            {
			                name : 'ITASKNAME',
			                type : 'string'
			            }
			    ]
			});
	/** 常用任务store* */
	var nomalTaskStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    model : 'nomalTaskModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'icspdbflowstart/getNomalTask.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	nomalTaskStore.on('beforeload', function(store, options) {
		var new_params = {
				isystemId : projectId
		};
		Ext.apply(store.proxy.extraParams,
				new_params);
	});
	
	auditorStore.load({  
        params:{ 
        	isystemId : projectId,
        	sysName : $("#ictaskname").val()
        },
        callback : function(r, options, success) { 
/*          var record =	auditorStore.getAt(0);
          auditorComBox.setRawValue(record.data.fullName);
          auditorComBox.setValue(record.data.loginName);*/
        }
    });   
	
	/** 审核人下拉框* */
	var auditorComBox = Ext.create ('Ext.form.ComboBox',
	{
	    editable : false,
	    fieldLabel : "审核人",
	    labelWidth : 65,
	    store : auditorStore,
	    labelAlign : 'right',
	    hidden:true,
	    queryMode : 'local',
	    emptyText : '--请选择审核人--',
	    //allowBlank : false,// 不允许为空
	    width : '100%',
	    displayField : 'fullName',
	    valueField : 'loginName',// ,
	    	listeners:{
	    		afterrender:function(combox){
//	    		  var record =	auditorStore.getAt(0);
//	    		  combox.setsetRawValue(record.data.fullName);
//	    		  combox.setValue(record.data.loginName);
	    		}
	    	}
	    	
	         
	});
	
	/** 执行人store* */
	var exectorStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    model : 'AuditorModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getExectorListForIC.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	 
	exectorStore.load({  
        params:{ 
        	isystemId : projectId,
        	sysName : $("#ictaskname").val()
        }, 
        callback : function(r, options, success) {
        	if(success){
                for(var i=0;i<r.length;i++){
                    var record = r[i];
                    var v = record.data.fullName;
                    if(v == userName){
                    	executorComBox.setValue(record.data.loginName);
                    	executorComBox.setRawValue(record.data.fullName);
                    }}}

        	
        	
        	
          }
    });   
	
	var executorComBox = Ext.create ('Ext.form.ComboBox',
	{
	    editable : false,
	    fieldLabel : "执行人",
	    labelWidth : 65,
	    labelAlign : 'right',
	    //hidden:hmmedStart,
	    emptyText : '--请选择执行人--',
	    //allowBlank : false,// 不允许为空
	    store : exectorStore,
	    queryMode : 'local',
	    width : '100%',
	    displayField : 'fullName',
	    valueField : 'loginName',
	    listeners:{
	    	select:function(){
	    		//alert(this.value);
	    	}
	    }
	});
	
	var listStore = Ext.create("Ext.data.Store", {
	    fields: ["listName", "listValue"],
	    data: [
	        { listName: "日常变更", listValue: 0 },
	        { listName: "重大变更", listValue: 3 },
	        { listName: "维护", listValue: 1 },
	        { listName: "后补", listValue: 2 }
	    ]
	});
	var createModelStore = Ext.create("Ext.data.Store", {
	    fields: ["createModelName", "createModelValue"],
	    data: [
	        { createModelName: "不使用常用任务", createModelValue: 0 },
	        { createModelName: "使用常用任务", createModelValue: 1 }
	    ]
	});
	var selectModelStore = Ext.create("Ext.data.Store", {
	    fields: ["selectModelName", "selectModelValue"],
	    data: [
	        { selectModelName: "不创建常用任务", selectModelValue: 0 },
	        { selectModelName: "应用常用任务", selectModelValue: 1 },
	        { selectModelName: "创建常用任务并提交", selectModelValue: 2 }
	    ]
	});
	
	
	var listComBox = Ext.create ('Ext.form.ComboBox',
	{
	    editable : false,
	    fieldLabel : "操作类型",
	    labelWidth : 65,
	    labelAlign : 'right',
	    //hidden:hmmedStart,
	    store : listStore,
	    value : 0,
	    queryMode : 'local',
	    width : '50%',
	    displayField : 'listName',
	    valueField : 'listValue',
	    listeners:{
	    	change:function(me,newValue,oldValue){
	    		var record = me.getStore().findRecord("listValue",newValue);
	    		if(!nomalTask){
	    			if (record.data.listValue == "2") {
	    				auditorComBox.show();
	    				auditorComBox.enable();
	    				listName.hide();
	    			}else{
	    				auditorComBox.hide();
	    				auditorComBox.disable();
	    				listName.show();
	    			}
	    		}else{
	    			if (record.data.listValue == "2") {
	    				auditorComBox.show();
	    				auditorComBox.enable();
	    			}else{
	    				auditorComBox.hide();
	    				auditorComBox.disable();
	    			}
	    		}
	    		
	    	}
	    }/*,
	    listeners :
	    {
	    	select : function ()
	        {
	    		if(listComBox.getRawValue == '常规')
	    		{
	    			Ext.getCmp('listName').allowBlank = false;
	    		} else 
	    		{
	    			Ext.getCmp('listName').allowBlank = true;
	    		}
	        }
	    }*/
	});
	var createModelComBox = Ext.create ("Ext.form.field.Checkbox",
			{
				width : '20%',
				boxLabel : '应用常用任务',
				align : 'right',
				name : 'createModelComBox',
				hidden : nomalTask,
				listeners:{
					change : function(ck, checked) {
		                if(checked){
		                	selectModelComBox.setDisabled(false);
			    			Ext.getCmp('move-next').hide();
			    			Ext.getCmp('createTaskSubmit').show();
		                }else{
		                	selectModelComBox.setDisabled(true);
							Ext.getCmp('move-next').show();
			    			Ext.getCmp('createTaskSubmit').hide();
		                }
		            }
		        }
			});
	var selectModelComBox = Ext.create ('Ext.ux.ideal.form.ComboBox',
			{
			    hidden : nomalTask,
			    fieldLabel : "选择常用任务",
			    labelWidth : 90,
			    labelAlign : 'right',
			    store : nomalTaskStore,
			    value : 0,
			    forceSelection : true,
			    queryMode : 'local',
			    width : '80%',
			    displayField : 'ITASKNAME',
			    valueField : 'IID',
			    disabled : true,
			    listeners:{
			    	
			    }
			});
	
	var listName = Ext.create('Ext.form.field.Text', {
		width : '50%',
		hidden : nomalTask,
		labelWidth : 65,
		labelAlign : 'right',
		maxLength : 255,
		maxLengthText : '请输入255位长度的单号',
		name: 'listName',
		//allowBlank : false,
		fieldLabel : '单号',
		enforceMaxLength : true // 是否强制限制字符串长度，超过最大长度将不能再被输入
	});
	
	var nomalTaskName = Ext.create('Ext.form.field.Text', {
		width : '50%',
		hidden : !nomalTask,
		labelWidth : 90,
		labelAlign : 'right',
		maxLength : 255,
		maxLengthText : '请输入不超过255位长度的常用任务名称',
		name: 'nomalTaskName',
		//allowBlank : false,
		fieldLabel : '常用任务名称',
		enforceMaxLength : true // 是否强制限制字符串长度，超过最大长度将不能再被输入
	});
	
	var taskStepForm = Ext.create('Ext.form.Panel', {
		name : 'taskStepForm',
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		collapsible : false,//可收缩 
		collapsed : false,//默认收缩
		border : false,
		bodyBorder : false,
		dockedItems : [{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items : [listComBox,listName,nomalTaskName ]
			},{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items : [ createModelComBox ,selectModelComBox ]
			},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			hidden : nomalTask,
			items : [ taskName ]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ auditorComBox ]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ executorComBox ]
		},{
//			xtype : 'toolbar',
//			border : false,
//			dock : 'top',
//			items : [ listComBox,listName ]
//		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ snapshotBtn ]
		}]
	});
	
	/*************************************第二步"执行策略"页panel以及所需的控件*****************************************************/
	var timingExecRadio = Ext.create('Ext.form.field.Radio', {
		width: 100,
		name:'execRadio',
		labelAlign : 'left',
		fieldLabel: '',
	    checked: true,
		boxLabel: '定时执行',
		inputValue : 1
	});
	var cycleExecRadio = Ext.create('Ext.form.field.Radio', {
		width: 100,
		name:'execRadio',
		labelAlign : 'left',
		fieldLabel: '',
		boxLabel: '周期执行',
		inputValue : 2
	});
	
	var selectExecRadio = Ext.create('Ext.form.field.Radio', {
		width: 100,
		name:'execRadio',
		labelAlign : 'left',
		fieldLabel: '',
		hidden:true,
		boxLabel: '选择执行',
		inputValue : 3
	});
	var immediatelyExecRadio = Ext.create('Ext.form.field.Radio', {
		width: 100,
		name:'execRadio',
		labelAlign : 'left',
		fieldLabel: '',
	    boxLabel: '触发执行',
	    checked: false,
	    hidden:true,
	    inputValue : 4
	});
	var execModeGroup = Ext.create('Ext.form.RadioGroup', {
		width: '100%',
		name:'cycleModeGroup',
		labelAlign : 'left',
		layout: 'column',
		items:[immediatelyExecRadio,timingExecRadio,cycleExecRadio,selectExecRadio],
        listeners:{
            //通过change触发
            change: function(g , newValue , oldValue){
            	 if(newValue.execRadio == 1)
            	{
            		Ext.getCmp('strategyExecToolBar').hide();
            		Ext.getCmp('timingLabelToolBar').show();
            		Ext.getCmp('timingExecToolBar').show();
            		Ext.getCmp('cycleExecCronToolBar').hide();
            		Ext.getCmp('cycleLabelToolBar').hide();
            		Ext.getCmp('selectExecToolBar').hide();
            	}
            	else if(newValue.execRadio == 2)
            	{
            		Ext.getCmp('strategyExecToolBar').hide();
            		Ext.getCmp('timingLabelToolBar').hide();
            		Ext.getCmp('timingExecToolBar').hide();
            		Ext.getCmp('cycleExecCronToolBar').show();
            		Ext.getCmp('cycleLabelToolBar').show();
            		Ext.getCmp('selectExecToolBar').hide();
            	}
            	else if(newValue.execRadio == 3)
            	{
            		Ext.getCmp('strategyExecToolBar').hide();
            		Ext.getCmp('timingLabelToolBar').hide();
            		Ext.getCmp('timingExecToolBar').hide();
            		Ext.getCmp('cycleExecCronToolBar').hide();
            		Ext.getCmp('cycleLabelToolBar').hide();
            		Ext.getCmp('selectExecToolBar').show();
            	}
            }
        }
	});
	
	var strategyExecPanel = new Ext.Panel({
		region : "north",
		id : 'strategyExecPanel',
		border : false,
		items : [{
            xtype: "label",
            text: ""
        }]
	});
	var selectExecPanel = new Ext.Panel({
		region : "north",
		id : 'selectExecPanel',
		border : false,
		items : [{
            xtype: "label",
            text: "选择执行：该任务通过审核后，进入到历史页面勾选待启动设备进行启动，有效执行时间为7天"
        }]
	});
	
	var execTime = Ext.create('Go.form.field.DateTime',
	{
		fieldLabel: '执行时间:',
		id : 'execTime',
		labelAlign: 'left', 
		labelWidth : 60,
		width : 300,
	    format : 'Y-m-d H:i:s'
	});
	
	var timingExecPanel = new Ext.Panel({
		region : "north",
		id : 'timingExecPanel',
		width : '100%',
		border : false,
		items : [execTime]
	});
	
	var timingLabelPanel = new Ext.Panel({
		region : "north",
		id : 'timingLabelPanel',
		border : false,
		items : [{
            xtype: "label",
            text: "定时执行：该任务通过审核后，进入到预启动任务，由平台计算时间，到达时间后自动发起该任务"
        }]
	});
	
	var cycleLabelPanel = new Ext.Panel({
		region : "north",
		id : 'cycleLabelPanel',
		border : false,
		items : [{
            xtype: "label",
            text: "周期执行: 按照设置的周期和频率运行,不选择代表不限制,即周期性运行"
        }]
	});
	
	/********************************周期月份多选下拉框组件************************************/
	var monthVal = [
            { "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },{ "name": "5", "value": "5" },
            { "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },{ "name": "10", "value": "10" },
            { "name": "11", "value": "11" },{ "name": "12", "value": "12" }
         ];

    Ext.regModel('monthModel', {
        fields: [
                { type: 'string', name: 'name' },
                { type: 'string', name: 'value' }
            ]
    });

    var monthStore = Ext.create('Ext.data.Store', {
        model: 'monthModel',
        data: monthVal
    });
    
    var monthCombo = Ext.create('Ext.form.field.ComboBox', {
        typeAhead: true,
        fieldLabel: '月',
        id : 'monthCombo',  
        name : 'monthCombo',
        displayField: 'name',
        valueField: 'value',
        emptyText: '--请选择日期(可多选)--',
        width : '50%',
        labelWidth: 60,
        multiSelect: true,
        editable: false,
        store: monthStore,
        queryMode: 'local',
        //typeAhead: true,
        listeners: {
	         // 焦点离开时获取下拉框选中值
	         blur: function () {
	             selectValue = monthCombo.getValue();
	             document.getElementById("hiddenMonthValue").value = selectValue;
	         },
	         afterrender: function (StateCombo) {
	            var stateValue = document.getElementById("hiddenMonthValue").value;
	　　　　　　　　　 var stateValues = stateValue.split(',');
	            if (document.getElementById("hiddenMonthValue").value != "undefined" && document.getElementById("hiddenMonthValue").value != "") {
	　　　　　　　　　　　　　　　　monthCombo.setValue(stateValues);
	            }
             }
         }
    });
    
    /********************************周期日期多选下拉框组件************************************/
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var dateVal;
    
    if(month == '1' || month == '3' || month == '5' || month == '7' || month == '8' || month == '10' || month == '12' )
    {
		dateVal = [
		            { "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },{ "name": "5", "value": "5" },
		            { "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },{ "name": "10", "value": "10" },
		            { "name": "11", "value": "11" },{ "name": "12", "value": "12" },{ "name": "13", "value": "13" },{ "name": "14", "value": "14" },{ "name": "15", "value": "15" },
		            { "name": "16", "value": "16" },{ "name": "17", "value": "17" },{ "name": "18", "value": "18" },{ "name": "19", "value": "19" },{ "name": "20", "value": "20" },
		            { "name": "21", "value": "21" },{ "name": "22", "value": "22" },{ "name": "23", "value": "23" },{ "name": "24", "value": "24" },{ "name": "25", "value": "25" },
		            { "name": "26", "value": "26" },{ "name": "27", "value": "27" },{ "name": "28", "value": "28" },{ "name": "29", "value": "29" },{ "name": "30", "value": "30" },
		            { "name": "31", "value": "31" }
		         ];
    } else if(month == '2'){
    	//判断是否闰年
    	if(isLeapYear(year)){
    		dateVal = [
	            { "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },{ "name": "5", "value": "5" },
	            { "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },{ "name": "10", "value": "10" },
	            { "name": "11", "value": "11" },{ "name": "12", "value": "12" },{ "name": "13", "value": "13" },{ "name": "14", "value": "14" },{ "name": "15", "value": "15" },
	            { "name": "16", "value": "16" },{ "name": "17", "value": "17" },{ "name": "18", "value": "18" },{ "name": "19", "value": "19" },{ "name": "20", "value": "20" },
	            { "name": "21", "value": "21" },{ "name": "22", "value": "22" },{ "name": "23", "value": "23" },{ "name": "24", "value": "24" },{ "name": "25", "value": "25" },
	            { "name": "26", "value": "26" },{ "name": "27", "value": "27" },{ "name": "28", "value": "28" },{ "name": "29", "value": "29" }
	         ];
    	} else {
    		dateVal = [
	            { "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },{ "name": "5", "value": "5" },
	            { "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },{ "name": "10", "value": "10" },
	            { "name": "11", "value": "11" },{ "name": "12", "value": "12" },{ "name": "13", "value": "13" },{ "name": "14", "value": "14" },{ "name": "15", "value": "15" },
	            { "name": "16", "value": "16" },{ "name": "17", "value": "17" },{ "name": "18", "value": "18" },{ "name": "19", "value": "19" },{ "name": "20", "value": "20" },
	            { "name": "21", "value": "21" },{ "name": "22", "value": "22" },{ "name": "23", "value": "23" },{ "name": "24", "value": "24" },{ "name": "25", "value": "25" },
	            { "name": "26", "value": "26" },{ "name": "27", "value": "27" },{ "name": "28", "value": "28" }
	         ];
    	}
    	
    } else {
	    dateVal = [
	        { "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },{ "name": "5", "value": "5" },
	        { "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },{ "name": "10", "value": "10" },
	        { "name": "11", "value": "11" },{ "name": "12", "value": "12" },{ "name": "13", "value": "13" },{ "name": "14", "value": "14" },{ "name": "15", "value": "15" },
	        { "name": "16", "value": "16" },{ "name": "17", "value": "17" },{ "name": "18", "value": "18" },{ "name": "19", "value": "19" },{ "name": "20", "value": "20" },
	        { "name": "21", "value": "21" },{ "name": "22", "value": "22" },{ "name": "23", "value": "23" },{ "name": "24", "value": "24" },{ "name": "25", "value": "25" },
	        { "name": "26", "value": "26" },{ "name": "27", "value": "27" },{ "name": "28", "value": "28" },{ "name": "29", "value": "29" },{ "name": "30", "value": "30" }
	     ];
    }
    
    Ext.regModel('dateModel', {
        fields: [
                { type: 'string', name: 'name' },
                { type: 'string', name: 'value' }
            ]
    });

    var dateStore = Ext.create('Ext.data.Store', {
        model: 'dateModel',
        data: dateVal
    });
    
    var dateCombo = Ext.create('Ext.form.field.ComboBox', {
        typeAhead: true,
        fieldLabel: '日',
        id : 'dateCombo',  
        name : 'dateCombo',
        displayField: 'name',
        valueField: 'value',
        emptyText: '--请选择日期(可多选)--',
        width : '50%',
        labelWidth: 60,
        multiSelect: true,
        editable: false,
        store: dateStore,
        queryMode: 'local',
        listeners: {
	         // 焦点离开时获取下拉框选中值
	         blur: function () {
	             selectValue = dateCombo.getValue();
	             document.getElementById("hiddenDateValue").value = selectValue;
	         },
	         afterrender: function (StateCombo) {
	            var stateValue = document.getElementById("hiddenDateValue").value;
	　　　　　　　　　 var stateValues = stateValue.split(',');
	            if (document.getElementById("hiddenDateValue").value != "undefined" && document.getElementById("hiddenDateValue").value != "") {
	　　　　　　　　　　　　　　　　dateCombo.setValue(stateValues);
	            }
             }
         }
    });
    
    /********************************周期星期多选下拉框组件************************************/
    var weekVal = [
        { "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },{ "name": "5", "value": "5" },
        { "name": "6", "value": "6" },{ "name": "7", "value": "7" }
     ];

    Ext.regModel('weekModel', {
        fields: [
                { type: 'string', name: 'name' },
                { type: 'string', name: 'value' }
            ]
    });

    var weekStore = Ext.create('Ext.data.Store', {
        model: 'weekModel',
        data: weekVal
    });
    
    var weekCombo = Ext.create('Ext.form.field.ComboBox', {
        typeAhead: true,
        fieldLabel: '周',
        id : 'weekCombo',  
        name : 'weekCombo',
        displayField: 'name',
        valueField: 'value',
        emptyText: '--请选择星期(可多选)--',
        width : '50%',
        labelWidth: 60,
        multiSelect: true,
        editable: false,
        store: weekStore,
        queryMode: 'local',
        listeners: {
	         // 焦点离开时获取下拉框选中值
	         blur: function () {
	             selectValue = weekCombo.getValue();
	             document.getElementById("hiddenWeekValue").value = selectValue;
	         },
	         afterrender: function (StateCombo) {
	            var stateValue = document.getElementById("hiddenWeekValue").value;
	　　　　　　　　　 var stateValues = stateValue.split(',');
	            if (document.getElementById("hiddenWeekValue").value != "undefined" && document.getElementById("hiddenWeekValue").value != "") {
	　　　　　　　　　　　　　　　　weekCombo.setValue(stateValues);
	            }
             }
         }
    });
    
    /********************************周期小时多选下拉框组件************************************/
    var hourVal = [
            { "name": "0", "value": "0" },{ "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },
            { "name": "5", "value": "5" },{ "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },
            { "name": "10", "value": "10" },{ "name": "11", "value": "11" },{ "name": "12", "value": "12" },{ "name": "13", "value": "13" },{ "name": "14", "value": "14" },
            { "name": "15", "value": "15" },{ "name": "16", "value": "16" },{ "name": "17", "value": "17" },{ "name": "18", "value": "18" },{ "name": "19", "value": "19" },
            { "name": "20", "value": "20" },{ "name": "21", "value": "21" },{ "name": "22", "value": "22" },{ "name": "23", "value": "23" }
         ];

    Ext.regModel('hourModel', {
        fields: [
                { type: 'string', name: 'name' },
                { type: 'string', name: 'value' }
            ]
    });

    var hourStore = Ext.create('Ext.data.Store', {
        model: 'hourModel',
        data: hourVal
    });
    
    var hourCombo = Ext.create('Ext.form.field.ComboBox', {
        typeAhead: true,
        fieldLabel: '时',
        id : 'hourCombo',  
        name : 'hourCombo',
        displayField: 'name',
        valueField: 'value',
        emptyText: '--请选择小时(可多选)--',
        width : '50%',
        labelWidth: 60,
        multiSelect: true,
        editable: false,
        store: hourStore,
        queryMode: 'local',
        listeners: {
	         // 焦点离开时获取下拉框选中值
	         blur: function () {
	             selectValue = hourCombo.getValue();
	             document.getElementById("hiddenHourValue").value = selectValue;
	         },
	         afterrender: function (StateCombo) {
	            var stateValue = document.getElementById("hiddenHourValue").value;
	　　　　　　　　　 var stateValues = stateValue.split(',');
	            if (document.getElementById("hiddenHourValue").value != "undefined" && document.getElementById("hiddenHourValue").value != "") {
	　　　　　　　　　　　　　　　　hourCombo.setValue(stateValues);
	            }
             }
         }
    });
    
    /********************************周期分钟多选下拉框组件************************************/
    var minuteVal = [
            { "name": "0", "value": "0" },{ "name": "1", "value": "1" },{ "name": "2", "value": "2" },{ "name": "3", "value": "3" },{ "name": "4", "value": "4" },
            { "name": "5", "value": "5" },{ "name": "6", "value": "6" },{ "name": "7", "value": "7" },{ "name": "8", "value": "8" },{ "name": "9", "value": "9" },
            { "name": "10", "value": "10" },{ "name": "11", "value": "11" },{ "name": "12", "value": "12" },{ "name": "13", "value": "13" },{ "name": "14", "value": "14" },
            { "name": "15", "value": "15" },{ "name": "16", "value": "16" },{ "name": "17", "value": "17" },{ "name": "18", "value": "18" },{ "name": "19", "value": "19" },
            { "name": "20", "value": "20" },{ "name": "21", "value": "21" },{ "name": "22", "value": "22" },{ "name": "23", "value": "23" },{ "name": "24", "value": "24" },
            { "name": "25", "value": "25" },{ "name": "26", "value": "26" },{ "name": "27", "value": "27" },{ "name": "28", "value": "28" },{ "name": "29", "value": "29" },
            { "name": "30", "value": "30" },{ "name": "31", "value": "31" },{ "name": "32", "value": "32" },{ "name": "33", "value": "33" },{ "name": "34", "value": "34" },
            { "name": "35", "value": "35" },{ "name": "36", "value": "36" },{ "name": "37", "value": "37" },{ "name": "38", "value": "38" },{ "name": "39", "value": "39" },
            { "name": "40", "value": "40" },{ "name": "41", "value": "41" },{ "name": "42", "value": "42" },{ "name": "43", "value": "43" },{ "name": "44", "value": "44" },
            { "name": "45", "value": "45" },{ "name": "46", "value": "46" },{ "name": "47", "value": "47" },{ "name": "48", "value": "48" },{ "name": "49", "value": "49" },
            { "name": "50", "value": "50" },{ "name": "51", "value": "51" },{ "name": "52", "value": "52" },{ "name": "53", "value": "53" },{ "name": "54", "value": "54" },
            { "name": "55", "value": "55" },{ "name": "56", "value": "56" },{ "name": "57", "value": "57" },{ "name": "58", "value": "58" },{ "name": "59", "value": "59" }
         ];

    Ext.regModel('minuteModel', {
        fields: [
                { type: 'string', name: 'name' },
                { type: 'string', name: 'value' }
            ]
    });

    var minuteStore = Ext.create('Ext.data.Store', {
        model: 'minuteModel',
        data: minuteVal
    });
    
    var minuteCombo = Ext.create('Ext.form.field.ComboBox', {
        typeAhead: true,
        fieldLabel: '分',
        id : 'minuteCombo',  
        name : 'minuteCombo',
        displayField: 'name',
        valueField: 'value',
        emptyText: '--请选择分钟(可多选)--',
        width : '50%',
        labelWidth: 60,
        multiSelect: true,
        editable: false,
        store: minuteStore,
        queryMode: 'local',
        listeners: {
	         // 焦点离开时获取下拉框选中值
	         blur: function () {
	             selectValue = minuteCombo.getValue();
	             document.getElementById("hiddenMinuteValue").value = selectValue;
	         },
	         afterrender: function (StateCombo) {
	            var stateValue = document.getElementById("hiddenMinuteValue").value;
	　　　　　　　　　 var stateValues = stateValue.split(',');
	            if (document.getElementById("hiddenMinuteValue").value != "undefined" && document.getElementById("hiddenMinuteValue").value != "") {
	　　　　　　　　　　　　　　　　minuteCombo.setValue(stateValues);
	            }
             }
         }
    });
    
    var cycleExecCronText = Ext.create('Ext.form.field.Text', {
		width : '50%',
		labelWidth : 60,
		labelAlign : 'left',
		id : 'cycleExecCronText',
		name: 'cycleExecCronText',
		readOnly : true,
		//allowBlank : false,
		fieldLabel : '执行周期'
	});
    
    /** 选择生成周期表达式按钮 **/
	var selectCronButton = Ext.create ("Ext.Button",
	{
		id : 'selectCronButton_id',
	    cls : 'Common_Btn',
	    text : "选择",
	    handler : selectExecCron
	});
	
	var immediatelyExecForm = Ext.create('Ext.form.Panel', {
		name : 'immediatelyExecForm',
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		height : 120,
		collapsible : false,//可收缩 
		collapsed : false,//默认收缩
		border : true,
		bodyBorder : false,
		dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ execModeGroup ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'strategyExecToolBar',
			dock : 'top',
			items : [/* strategyExecPanel*/ ]
		},{
			xtype : 'toolbar',
			border : false,
			hidden : true,
			id : 'selectExecToolBar',
			dock : 'top',
			items : [ /*selectExecPanel*/ ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'timingExecToolBar',
			dock : 'top',
			items : [ timingExecPanel ]   //执行时间 日历控件
		},{
			xtype : 'toolbar',
			border : false,
			id : 'timingLabelToolBar',
			dock : 'top',
			items : [ /*timingLabelPanel*/ ] //执行时间 Lable控件
		},{
			xtype : 'toolbar',
			border : false,
			id : 'cycleExecCronToolBar',
			dock : 'top',
			hidden : true,
			items : [ cycleExecCronText,selectCronButton ]
		}/*,{
			xtype : 'toolbar',
			border : false,
			id : 'monthComboToolBar',
			dock : 'top',
			hidden : true,
			items : [ monthCombo ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'dateComboToolBar',
			dock : 'top',
			hidden : true,
			items : [ dateCombo ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'weekComboToolBar',
			dock : 'top',
			hidden : true,
			items : [ weekCombo ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'hourComboToolBar',
			dock : 'top',
			hidden : true,
			items : [ hourCombo ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'minuteComboToolBar',
			dock : 'top',
			hidden : true,
			items : [ minuteCombo ]
		}*/,{
			xtype : 'toolbar',
			border : false,
			id : 'cycleLabelToolBar',
			dock : 'top',
			hidden : false,
			items : [ cycleLabelPanel ]
		}]
	});
	
	// 上传文件配置的复选框
	var uploadFileConfigCheckBox = Ext.create ("Ext.form.field.Checkbox",
	{
		boxLabel : '文件下发',
		hidden:true,
		name : 'uploadFileConfigCheckBox',
		listeners:{
			change : function(ck, checked) {
                if(checked){
                	Ext.getCmp('uploadFileConfigToolBar').show();
                }else{
                	Ext.getCmp('uploadFileConfigToolBar').hide();
                }
            }
        }
	});
	
	var uploadSuccessFlagText = Ext.create('Ext.form.field.Text', {
		labelWidth : 60,
		labelAlign : 'left',
		id : 'uploadSuccessFlagText',
		name: 'uploadSuccessFlagText',
		hidden : true,
		//allowBlank : false,
		fieldLabel : '上传FTP文件是否成功标记',
		value : false
	});
	
	var uploadRootPathText =  Ext.create('Ext.form.field.Text', {
		labelWidth : 60,
		labelAlign : 'left',
		name: 'uploadRootPathText',
		hidden : true,
		//allowBlank : false,
		fieldLabel : '上传到FTP的文件的路径'
	});
	
	var myMask = new Ext.LoadMask(Ext.getCmp("icSPDBCreateTaskWin"), {
		msg    : "文件上传中，请稍后...",
		msgCls : 'z-index:10000;'
	});
	
	/** 选择生成周期表达式按钮 **/
	var uploadFtpFileButton = Ext.create ("Ext.Button",
	{
	    text : "上传",
	    handler: function() {
			var form = this.up('form').getForm();
			var upfile=form.findField("file").getValue();
			
			if(upfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			
//			var fileType = upfile.slice(upfile.lastIndexOf(".")+1).toLowerCase();  
//		    if ("zip" != fileType) {  
//		    	Ext.Msg.alert('提示',"请上传ZIP格式文件...");
//		        return false;  
//		    }  
		    
		    if (form.isValid()) {
		    	 myMask.show();
				 form.submit({
					url: 'icCreateTaskPage/uploadFtpConfigFile.do?projectId='+isystemId,
				    success: function(form, action) {
				    	myMask.hide();
				    	var success = Ext.decode(action.response.responseText).success;
                    	var msg =  Ext.decode(action.response.responseText).message;
                    	var rootPath =  Ext.decode(action.response.responseText).rootPath;
                    	// 上传FTP文件成功后，才可以进行后续步骤操作的标记
                    	uploadSuccessFlagText.setValue(success);
                    	// 记录上传FTP成功后的保存文件路径
                    	uploadRootPathText.setValue(rootPath);
                    	Ext.getCmp('uploadFileConfigFlagToolBar').show();
                    	if(success){
                    		Ext.getCmp('uploadConfigSucceseLabel').setText(msg);
                    		Ext.getCmp('uploadConfigSucceseLabel').show();
                    		Ext.getCmp('uploadConfigFailLabel').hide();
                    	} else {
                    		Ext.getCmp('uploadConfigFailLabel').setText(msg);
                    		Ext.getCmp('uploadConfigSucceseLabel').hide();
                    		Ext.getCmp('uploadConfigFailLabel').show();
                    	}
				    },
				    failure: function(form, action) {
				    	myMask.hide();
				    	var success = Ext.decode(action.response.responseText).success;
                  	    var msg = secureFilterRsForUpload(action.response.responseText);
                  		if(null == msg || msg == "" ){
                  			msg = Ext.decode(action.response.responseText).message;
                  	        msg = msg.replace(/！/g,"！<br>");
                  		}
		 	            Ext.getCmp('uploadFileConfigFlagToolBar').show();
		 	            // 上传FTP文件失败后，设置不可以正常继续操作
                    	uploadSuccessFlagText.setValue(success);
		 	            Ext.getCmp('uploadConfigFailLabel').setText(msg);
		 	            Ext.getCmp('uploadConfigSucceseLabel').hide();
		 	            Ext.getCmp('uploadConfigFailLabel').show();
				    }
				});
	         }
			
		}
	});

	//向FTP导入文件Panel
	var upLoadformForFtpPanel =Ext.create('Ext.form.Panel', {
        width:'100%',
	    frame: true,
		items: [
			{
				//id:'fileFildId',
				xtype: 'filefield',
				name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
				fieldLabel: '选择文件',
				labelWidth: 80,
				msgTarget: 'side',
				anchor: '100%',
				buttonText: '浏览...',
				width:'60%'
			},
			{
				//id:'paramNamesId',
				xtype:'hidden',
				name:'ftpParamNames',
				value:ftpParamNames
			},uploadFtpFileButton
		]
	});
    
	//向FTP导入文件Panel
	var uploadFtpFileButtonText = Ext.create ("Ext.Button",
			{
			    text : "选择",
			    handler: function() {
			    	fileSlectData(textfile);
			    }
	})
	
	var textfile = Ext.create("Ext.form.field.Text", {
        fieldLabel: '文件路径',
        labelWidth: 65,
        labelAlign: 'left',
        name: 'textfile',
        anchor: '100%',
        width: '70%'
    });
	var upLoadformForFtpPanelText =Ext.create('Ext.form.Panel', {
		  width:'100%',
		  frame: true,
		  items: [textfile,
			{
				xtype:'hidden',
				name:'ftpParamNames',
				value:ftpParamNames
			},uploadFtpFileButtonText
		]
	});
	console.log(fileselct);
	if(fileselct=='true'){
		upLoadformForFtpPanelText.show();
		upLoadformForFtpPanel.hide();
	}else{
		upLoadformForFtpPanel.show();
		upLoadformForFtpPanelText.hide();
	}
	function fileSlectData(textfile)
	{
		var fileSlectWin;
		if(null==fileSlectWin){
		Ext.define('dataModel', {
			extend : 'Ext.data.Model',
			fields : [ {
				name : 'iurl',
				type : 'string'
			}, {
				name : 'ifileName',
				type : 'string'
			}, {
				name : 'link',
				type : 'string'
			}, {
				name : 'size',
				type : 'string'
			}, {
				name : 'times',
				type : 'string'
			} ]
		});

		var columns = [
				{
					text : '目录名',
					dataIndex : 'iurl',
					width : 300
				},
				{
					text : '文件名',
					dataIndex : 'ifileName',
					width : 300
				},
				{
					text : '文件大小',
					dataIndex : 'size',
					width : 100
				},
				{
					text : '时间',
					dataIndex : 'times',
					width : 200
				} ];
		/** 系统信息列表左侧store* */
		var store = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			pageSize : 1000,
			model : 'dataModel',
			proxy : {
				type : 'ajax',
				url : 'ftpfileSlectData.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			}
		});
		store.on('beforeload', function(store, options) {
			var new_params = {};
			Ext.apply(store.proxy.extraParams,
					new_params);
		});
		var submitButton = Ext.create("Ext.Button", {
			cls : 'Common_Btn',
			text : "确认",
			handler : function(){
				var paaram="";
				var datas = grid.getView().getSelectionModel().getSelection();
				for(var i=0;i<datas.length;i++){
	        		var filename=datas[i].data.ifileName;
	        		paaram=paaram+"\""+filename+"\",";
	        	}
				if (paaram!=''){
					paaram=paaram.substring(0,(paaram.length-1));
					uploadSuccessFlagText.setValue(true);
				}
				textfile.setValue(paaram);
				fileSlectWin.close();
			}
		});
	    var selModel = Ext.create('Ext.selection.CheckboxModel', {
	        checkOnly: true
	    });
		var grid = Ext.create('Ext.grid.Panel', {
			multiSelect : true,
			region : 'center',
			selModel: selModel,
			columnLines : true,
			store : store,
			columns : columns,
			collapsible : false,
			margins : '0 0 0 5',
			dockedItems : [ {
				xtype : 'toolbar',
				height : 40,
				dock : 'top',
				items : [ submitButton ]
			} ]
		});

			fileSlectWin = Ext.create('Ext.window.Window', {
				title : '选择w文件',
				modal : true,
				closeAction : 'destroy',
				constrain : true,
				autoScroll : true,
				upperWin : fileSlectWin,
				width: contentPanel.getWidth() - 150,
				height: 870,
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				layout : 'fit',
				items: grid 
			});
	
		}
		fileSlectWin.show();
	}
	
	
	var uploadConfigLabelPanel = new Ext.Panel({
		region : "north",
		border : false,
		items : [{
            xtype: "label",
            id: "uploadConfigSucceseLabel",
            hidden : true,
            text: "文件下发成功"
        },{
            xtype: "label",
            id: "uploadConfigFailLabel",
            hidden : true,
            text: "文件下发失败"
        }]
	});
	
	var uploadFileConfigForm = Ext.create('Ext.form.Panel', {
		name : 'uploadFileConfigForm',
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		height : '65%',
		collapsible : false,//可收缩 
		collapsed : false,//默认收缩
		border : false,
		bodyBorder : false,
		hidden:true,
		dockedItems : [{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ uploadFileConfigCheckBox ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'uploadFileConfigToolBar',
			hidden : true,
			dock : 'top',
			items : [ upLoadformForFtpPanel,upLoadformForFtpPanelText ]
		},{
			xtype : 'toolbar',
			border : false,
			id : 'uploadFileConfigFlagToolBar',
			hidden : true,
			dock : 'top',
			items : [ uploadConfigLabelPanel ]
		}]
	});
	
	var immediatelyExecPanel = Ext.create('Ext.panel.Panel', 
	{
		name : 'immediatelyExecPanel',
		height:'100%',
		width:'100%',
		layout:'border',
		items:[immediatelyExecForm,uploadFileConfigForm],
		listeners:{
			beforerender:function(){
				 if(ftpConfig == "1"&&!nomalTask){
					 uploadFileConfigCheckBox.show(); 
					uploadFileConfigCheckBox.setValue(true);
				}
			}
		}
	});
	
	/*************************************第三步"运行参数"页panel以及所需的控件*****************************************************/
	// 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
	// 定义可编辑grid组件
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	
	/** 参数信息列表column* */
	var paramInfoColumns = [
        {
            text : '序号',
            width : 40,
            align:'left',
            xtype : 'rownumberer'
        },
        {
            text : 'ID',
            dataIndex : 'iid',
            hidden : true
        },
        {
            text : '参数名称',
            dataIndex : 'iparamName',
            sortable:false,
            //width : 200
            flex : 2
        },
        {
            text : '参数类型',
            dataIndex : 'iparamType',
            sortable:false,
            renderer : function(value,cellmeta,record) {
            	if (record.get('iparamDesc').indexOf("#[sequence]s#")==0)
            	 {
            		 return 'String序列';
            	 }
            	 return value;
			},
            flex : 2
        },
        {
        	text : '参数值',
            dataIndex : 'iparamValue',
            sortable:false,
            flex : 4,
            //默认设置成textfield格式
            editor :
            {
            	xtype: 'textfield',
            	allowBlank:false
            }
        },
        {
            text : '参数说明',
            dataIndex : 'iparamDesc',
            sortable:false,
            renderer : function(value) {
            	var endnum=value.indexOf("#[sequence]e#");
            	var destemp=value.substring(endnum);
            	return destemp.replace("#[sequence]e#","");
			},
            flex : 4
        }
	];
	
	/** 参数信息列表model* */
	Ext.define ('paramInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'iparamName',
	                type : 'string'
	            },
	            {
	                name : 'iparamType',
	                type : 'string'
	            },
	            {
	                name : 'iparamValue',
	                type : 'string'
	            },
	            {
	                name : 'iparamDesc',
	                type : 'string'
	            }
	    ]
	});
	
	/** 参数信息列表store* */
	var paramInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramInfoModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getSpdbParamInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	// 参数信息列表grid重新加载
	paramInfoStore.load(
	{
		params :
        {
			systemId : projectId,
        	sysName : $("#ictaskname").val()
        }
	});
	
	paramInfoStore.on('load', function(store, options) {
		paramNames = '';
		for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			//参数校验
			if(i==0){
				paramNames += record.data.iparamName;
//				paramNames += "检查规则配置";
			} else{
				paramNames += paramName_splitStr + record.data.iparamName;
			}
		}
	});
	

	
	/** 规则信息列表model* */
	Ext.define ('ruleInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'ihcName',
	                type : 'string'
	            },
	            {
	                name : 'ihcItem',
	                type : 'string'
	            },
	            {
	                name : 'ihcSeparator',
	                type : 'string'
	            },
	            {
	                name : 'ihcKeywords',
	                type : 'string'
	            },
	            {
	                name : 'ihcAlermCode',
	                type : 'string'
	            }
	    ]
	});

	/** 规则信息列表store* */
	var ruleInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
//	    pageSize : 1000,
	    model : 'ruleInfoModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getRuleInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	// 规则信息列表grid重新加载
	ruleInfoStore.load(
	{
		params :
        {
			isystemId : projectId,
			hcitemId : hcitemId
        }
	});
	
	ruleInfoStore.on('beforeload', function(store, options) {
		var new_params = 
		{
			hcitemId : hcitemId,
	        isystemId:isystemId
		};
		Ext.apply(store.proxy.extraParams, new_params);
	});
	
	/** 导入按钮* */
	var importButton = Ext.create ("Ext.Button",
	{
		id : 'importBtn_id',
	    cls : 'Common_Btn',
	    text : "导入",
	    handler : importExcel
	});
	/** 历史参数按钮* */
	var hisButton = Ext.create ("Ext.Button",
	{
		id : 'history_param',
	    cls : 'Common_Btn',
	    text : "历史参数",
	    handler : hisParamBtnHandler
	});
	

	/** 检查项配置下拉框model* */
	Ext.define ('hcitemModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'hcitemId',
	                type : 'long'
	            },
	            {
	                name : 'hcitemName',
	                type : 'string'
	            }
	    ]
	});
	/** 检查项配置下拉框store* */
	var hcitemStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    model : 'hcitemModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getHcItemCfgList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	hcitemStore.load(
	{
		params :
        {
			systemId : projectId,
        	sysName : $("#ictaskname").val()
        }
	});
	
	hcitemStore.on ('load', function(store, options) {
		for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			if(hcitemId==record.data.hcitemId){
				Ext.getCmp("hcitemComBox").setValue(record);
				break;
			}
		}
	});
	
	/** 历史参数按钮* */
	var exportButton = Ext.create ("Ext.Button",
	{
		id : 'exportBtn_id',
	    cls : 'Common_Btn',
	    text : "导出模板",
	    handler : exportParamBtnHandler
	});
	
	var stringsStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'id','value' ]
	});
	
	/**参数信息列表gridpanel* */
	var param_info_grid = Ext.create ('Ext.grid.Panel',
	{
		name :'param_info_grid_id',
		autoScroll : false,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
	    store : paramInfoStore,
	    border : true,
	    columnLines : true,
	    columns : paramInfoColumns,
	    listeners: {
            //监听函数，在点击之前进行监听
            beforeedit: function (editor, e, eOpts) {
            //进行条件判断，符合条件则设置称combobox
            	var tempdes=e.record.get('iparamDesc');
                if (tempdes.indexOf("#[sequence]s#")==0) {
                	var endnum=tempdes.indexOf("#[sequence]e#");
                	var comboDatatemp=tempdes.substring(0,endnum);
                	var comboDataArray=comboDatatemp.replace("#[sequence]s#","").split(",");
                	if(comboDataArray.length==3 && comboDataArray[2]==''){
                	var uuid = comboDataArray[0];
                	var iid =comboDataArray[1];
                	Ext.Ajax.request({
           			 url:'getYushezhi.do',  
     			       params:{  
     			        uuid:uuid,
     			        iid:iid
     			        },  
     			        success: function(resp,opts) {  
     			        	var respText = Ext.decode(resp.responseText);
     			        var data =	respText.value.split(",");
     			       var comboDatatest='[';  
     			      for(var j=0;j<data.length;j++)
         			{
     			    	 if(j!=0)
                 		{
                 			comboDatatest+=",";
                 		}
                 		comboDatatest+="{'id':'"+data[j]+"','value':'"+data[j]+"'}";	
         			}
     			     comboDatatest+=']';
     			    stringsStore.loadData(Ext.JSON.decode(comboDatatest));
     			   }});}else{
     				  var comboDatatest='[';
                  	for(var  j=0; j<comboDataArray.length; j++)
                  	{
                  		if(j!=0)
                  		{
                  			comboDatatest+=",";
                  		}
                  		comboDatatest+="{'id':'"+comboDataArray[j]+"','value':'"+comboDataArray[j]+"'}";
                  	}
                  	comboDatatest+=']';
                  	stringsStore.loadData(Ext.JSON.decode(comboDatatest));
     				   
     			   }
                    e.column.setEditor({
                        xtype: 'combobox',
                        store : stringsStore,
                		   queryMode : 'local',
                		   width : 100,
                		   displayField : 'value',
                		   valueField : 'id',
                        editable: false
                    });
                }else{
                //不符合条件的设置称textfield
                    e.column.setEditor({
                        xtype: 'textfield',
                        allowBlank:false
                    });
                }
            }
        },
	    plugins : [ cellEditing ],
	    dockedItems : [ 
	    {
	        xtype : 'toolbar',
	        items : [ /*hcitemComBox,*/ '->', hisButton, importButton,exportButton ]
	    }]
	});
	
	
	/*************************************第四步"运行参数"页panel以及所需的控件*****************************************************/
	var serverArr = [];//选中服务器
	
	/** 服务器信息列表model* */
	Ext.define('serverInfoModel', {
			extend : 'Ext.data.Model',
			fields : [ 
				{name : 'iid',type : 'string'},
				 { name: 'resourceIid', type: 'string'},
				{name: 'sysName',  type: 'string'},
				{name: 'centerName',  type: 'string'},
				{name: 'igroupid', type: 'string'},
		        {name: 'iagentid', type: 'string'},
		        {name: 'sysAdmin', type: 'string'},
		        //{name: 'appAdmin', type: 'string'},
		        {name: 'systemInfo', type: 'string'},
		        {name: 'ctype', type: 'string'},
		        {name: 'middlewareType', type: 'string'},
		        { name: 'appName',  type: 'string'},
		        { name: 'hostName', type: 'string'},
		        { name: 'osType',type: 'string'},
		        { name: 'osTypeAgentinfo',type: 'string'},
		        { name: 'agentIp', type: 'string'},
		        {name: 'agentPort',type: 'string'},
		        {name: 'envType',type: 'string'},
		        { name: 'agentState', type: 'int'},
		        {name: 'osVersion',type: 'string'},//系统版本
		        {name: 'runDays',type: 'string'},//运行天数
		        {name: 'isHa',type: 'string'},//是否双机
		        {name: 'dbType',type: 'string'},//数据库类型
		        {name: 'dbVersion',type: 'string'},//数据库版本
		        {name: 'middlewareVersion',type: 'string'},//中间件版本
		        {name: 'icreateTime',type: 'string'},//纳管时间
		        {name: 'startUser',type: 'string'}//纳管启动人
      	]
	});
	
	/** 服务器信息列表store* */
	var serverInfoStore = Ext.create ('Ext.data.Store',
	{
	    model : 'serverInfoModel',
	    data : serverArr
	});
	serverInfoStore.on('beforeload', function(store, options) {
		var new_params = 
		{
			systemId:isystemId
		};
		Ext.apply(store.proxy.extraParams, new_params);
	});
	
	/** 服务器信息列表column* */
	var serverInfoColumns = [ {
		text : '序号',
		width : 50,
		align:'left',
		xtype : 'rownumberer'
	}, {
		text : 'iid',
		dataIndex : 'iid',
		flex : 1,
		hidden : true
	},
	{ text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
    { text: 'iagentid',  dataIndex: 'iagentid',hidden:true},
    {
        text: '主机名称',
        dataIndex: 'hostName',
        width: 100
    },{
    	text: '纳管id',
    	dataIndex: 'resourceIid',
    	width: 120
    },{
        text: 'IP',
        dataIndex: 'agentIp',
        width: 120
    },{
        text: '所属区域',
        dataIndex: 'centerName',
        width: 100
    },
    {text: '系统管理员',  dataIndex: 'sysAdmin',width: 100},
    {text: '应用管理员',  dataIndex: 'appAdmin',width: 100},
    {text: '信息系统名称',  dataIndex: 'systemInfo',width: 100},
    {
        text: '运行天数',
        dataIndex: 'runDays',
        width: 100
    },
    {
        text: '是否双机',
        dataIndex: 'isHa',
        width: 100
    },
    {
        text: 'AgentInfo系统类型',
        dataIndex: 'osTypeAgentinfo',
        width: 100,
        hidden:true
    },
    {
        text: '操作系统类型',
        dataIndex: 'osType',
        width: 100
    },
     {
        text: '操作系统版本',
        dataIndex: 'osVersion',
        width: 100
    }, 
    {
        text: '数据库类型',
        dataIndex: 'dbType',
        width: 100
    },
    {
        text: '数据库版本',
        dataIndex: 'dbVersion',
        width: 100
    },
    {text: '中间件类型',  dataIndex: 'middlewareType',width: 100},
    {
        text: '中间件版本',
        dataIndex: 'middlewareVersion',
        width: 100
    },
    {
        text: '纳管时间',
        dataIndex: 'icreateTime',
        width: 100
    }, 
     {
        text: '纳管用户',
        dataIndex: 'startUser',
        width: 100
    }, 
    {text: '应用类型',  dataIndex: 'ctype',hidden:true,width: 100},
    {
        text: '状态',
        dataIndex: 'agentState',
        //flex: 1,
        width: 110,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 0) {
                backValue = "Agent正常";
            } else if (value == 1) {
                backValue = "Agent异常";
            }
            return backValue;
        }
    },
    {
        text: 'Agent端口',
        dataIndex: 'agentPort',
        width: 100,
        hidden:true
    },
    {
        text: '环境',
        dataIndex: 'envType',
        hidden: true,
        width: 100,
        renderer: function(value, p, record) {
            var backValue = "";
            if (value == 0) {
                backValue = '<font">测试</font>';
            } else if (value == 1) {
                backValue = '<font >生产</font>';
            }
            return backValue;
        }
    }];	

	/** 移除设备按钮* */
	var removeButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "移除设备",
	    //				width : 100,
	    handler : removeDeviceRelation
	});
	/** 选择设备按钮* */
	var resButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "选择设备",
	    handler : openSelectWindow
	});
	
	/**服务器信息列表gridpanel* */
	var server_info_grid = Ext.create ('Ext.grid.Panel',
	{
		id : 'server_info_grid_id',
		name:'server_info_grid_id',
		selModel : selModel,
		store : serverInfoStore,
		autoScroll : true,
		columnLines : true,
		border : false,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		columns : serverInfoColumns,
		dockedItems : [ 
   	    {
   	        xtype : 'toolbar',
   	        items : [ /*groupComBox,*/ '->', removeButton, resButton ]
   	    }]
	});
	
	/********************** 选择设备窗口相关组件 *******************************************************************/
	/** *********************Model********************* */
	Ext.define ('computerModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'name',
	                type : 'string'
	            },
	            {
	                name : 'cpId',
	                mapping: 'id',
	                type : 'long'
	            },
	            {
	                name : 'ip',
	                type : 'string'
	            },
	            {
	                name : 'agentip',
	                type : 'string'
	            },
	            {
					name : 'ctype',
					type : 'string'
				}, 
				{
					name : 'cname',
					type : 'string'
				},
				{
					name : 'os',
					type : 'string'
				}
	    ]
	});
	
	/** *********************Store********************* */
	/** 设备列表数据源* */
	var cmoputerStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
//	    autoDestroy : true,
	    model : 'computerModel',
	    pageSize : 400,
	    proxy :
	    {
	        type : 'ajax',
	        actionMethods : {  
	            read : 'POST' // Store设置请求的方法，与Ajax请求有区别  
	        }, 
	        url : 'getComputerListForIC.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	cmoputerStore.on ('beforeload', function (store, options)
	{
		//处理已经选中的ip的cpid
//		var cpidStr = "";
//		for(var i=0; i<serverArr.length; i++){
//			var iid = serverArr[i].id;
////			alert('iid='+iid);
//			if(i==0){
//				cpidStr += iid;
//			}else{
//				cpidStr += "," + iid;
//			}
//		}
////		alert("idStr="+cpidStr);
//		
//		var ipBetweenString = "";
//		var ipEndString = "";
//		if (null != ipBetween.getValue ())
//		{
//			ipBetweenString = ipBetween.getValue ().trim ();
//		}
//		if (null != ipEnd.getValue ())
//		{
//			ipEndString = ipEnd.getValue ().trim ();
//		}
//		
//		//应用类型
//		var appType = ctCombo.value;
//		var cmType = cmCombo.value;
//		var osType = osCombo.value;
//		var groupIdCode = groupCombo.value;
//		if(null==appType || ''==appType || undefined==appType)
//		{
//			appType = '';
//		}
//		if(null==cmType || ''==cmType || undefined==cmType)
//		{
//			cmType = '';
//		}
//		if(null==osType || ''==osType || undefined==osType)
//		{
//			osType = '';
//		}
//		if(null==groupIdCode || ''==groupIdCode || undefined==groupIdCode)
//		{
//			groupIdCode = '';
//		}
		
		var new_params =
		{
			pageFlag : 0 //传递参数0时，设备展示列表不带有查询列表formpanel
//		    cpidStr : cpidStr,
//			systemId : isystemId,
//		    ipBetween : ipBetweenString,
//		    ipEnd : ipEndString,
//		    appType:appType,
//		    cmType:cmType,
//		    osType:osType
//		    ,groupIdCode:groupIdCode
		};
		Ext.apply (cmoputerStore.proxy.extraParams, new_params);
	});
	/** *********************组件********************* */
	/** 起始ip* */
	var ipBetween = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 50,
	    // fieldLabel : '设备查询',
	    emptyText : '--请输入开始IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 19,
	    fieldLabel : '至',
	    emptyText : '--请输入截止IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	
	/** 应用类型条件 */
	var ctStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'ctid','ctype' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'ctList.do',
			reader : {
				type : 'json',
				root : 'ctlist'
			}
		}
	});
	var ctCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : ctStore,
		queryMode : 'local',
		emptyText : '---请选择应用类型---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'ctype',
		valueField : 'ctid',
		triggerAction : "all"
	});
	
	/** 中间件 */
	var cmStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'cmid','cname' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'cmList.do',
			reader : {
				type : 'json',
				root : 'cmlist'
			}
		}
	});
	var cmCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : cmStore,
		queryMode : 'local',
		emptyText : '---请选择中间件---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'cname',
		valueField : 'cmid',
		triggerAction : "all"
	});
	
	/** 操作系统 */
	var osStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'osid','osname' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'osList.do',
			reader : {
				type : 'json',
				root : 'oslist'
			}
		}
	});
	var osCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : osStore,
		queryMode : 'local',
		emptyText : '---请选择操作系统---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'osname',
		valueField : 'osid',
		triggerAction : "all"
	});
	
	/** 设备组 */
	var groupStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'groupId','groupName' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'groupList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var groupCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : groupStore,
		queryMode : 'local',
		hidden:!egSwitch,
		emptyText : '---请选择设备组---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'groupName',
		valueField : 'groupId',
		triggerAction : "all"
	});
	
	/** 查询按钮* */
	var queryButtonForBSMNoSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '查询',
	    handler : queryWhere
	});
	
	/** 重置按钮* */
	var resetBtn = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '重置',
	    handler : resetWhere
//	    handler : function(){
//	    	alert('重置。。。');
//	    }
	});
	
	/** 增加按钮* */
	var saveButtonForBSMNoSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    disabled : true,
	    text : '增加',
	    handler : onSaveListener
	});
	
	/** 重置 * */
	function resetWhere()
	{
		ipBetween.setValue(null);//起始IP
		ipEnd.setValue(null);//结束IP
		ctCombo.clearValue();//应用类型
		cmCombo.clearValue();//中间件
		osCombo.clearValue();//操作系统
		groupCombo.clearValue();//设备组
	}
	
	/** 查询* */
	function queryWhere ()
	{
		var ipBetweenValue = ipBetween.getValue ().trim ();
		var ipEndValue = ipEnd.getValue ().trim ();
		if(checkIsNotEmpty (ipBetweenValue) &&checkIsNotEmpty (ipEndValue) )
			{
			if (checkIsNotEmpty (ipBetweenValue) && !isYesIp (ipBetweenValue))
			{
				Ext.Msg.alert ('提示', '请输入合法开始IP进行查询！');
				return;
			}
			if (checkIsNotEmpty (ipEndValue) && !isYesIp (ipEndValue))
			{
				Ext.Msg.alert ('提示', '请输入合法结束IP进行查询！');
				return;
			}
			}
		cmoputerStore.reload();
		bsPageBar.moveFirst ();
	}
	
	function selectExecCron()
	{
		var creatCronWin = Ext.create('Ext.window.Window', {
			title : '定时任务参数设置',
			modal : true,
			id : 'creatCronWin',
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			upperWin : creatCronWin,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight() - 30,
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			loader : {
				url : 'cronMainForSpdb.do',
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		});
		creatCronWin.show();
	}
	
	/** 增加设备* */
	function onSaveListener (btn)
	{
		var record = equigrid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		//把选中ip添加到数组中
		for (var i = 0, len = record.length; i < len; i++)
		{
			var serverRec = {};
			serverRec.id = record[i].data.cpId;
			serverRec.ip = record[i].data.ip; 
			serverRec.agentip = record[i].data.agentip;
			serverArr.push(serverRec);
		}
		serverInfoStore.add(serverArr);
		server_info_grid.view.refresh();
		//“选择设备”窗口数据重新加载
		bsPageBar.moveFirst ();
	}
	
	//移除设备按钮
	function removeDeviceRelation()
	{
		var grid_panel = server_info_grid;
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		Ext.MessageBox.confirm ('提示', "是否删除选中数据!", function (btn)
		{
			if (btn == 'no')
			{
				return;
			}
			if (btn == 'yes')
			{
				serverInfoStore.remove(record);
				//处理勾选数据
				/*for (var i = 0, len = record.length; i < len; i++)
				{
					var selRec = record[i];
					var iid = selRec.data.iid;
					for (var m = 0; m < serverInfoStore.getCount(); m++) {
						var recordStore = serverInfoStore.getAt(m);
						if(iid==recordStore.get('iid')){
							serverInfoStore.remove(selRec);
							break;
						}
					}
				}*/
			}
		});
	}
	
	/** 带选择设备列表分页工具栏* */
	var bsPageBar = Ext.create ('Ext.PagingToolbar',
	{
		id : 'icSpdbEquiSelectedPageBar',
	    store : cmoputerStore,
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	
	/** *********************Panel********************* */
	/** 设备列表panel* */
	var equigrid_panel = Ext.create ('Ext.grid.Panel',
	{
		id : 'equigrid_panel',
	    width : '100%',
	    height : 435,
	    store : cmoputerStore,
	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    {
		    checkOnly : true
	    }),
	    region : 'center',
	    border : true,
	    columnLines : true,
	    flex : 2,
	    columns : [
	        {
		        text : '设备ID',
		        sortable : true,
		        hidden : true,
		        dataIndex : 'cpId',
		        flex : 1
	        },
		    {
		        text : '设备IP',
		        sortable : true,
		        dataIndex : 'ip',
		        flex : 1
		    },
			{
		        text : 'AgentIP',
		        sortable : true,
		        dataIndex : 'agentip',
		        flex : 1
		    },
		    {
				text : '应用类型',
				dataIndex : 'ctype',
				width : 90
			},
			{
				text : '中间件',
				dataIndex : 'cname',
				width : 90
			},
			{
				text : '操作系统',
				dataIndex : 'os',
				width : 90
			}
	    ],
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        border:false,
		        items : [
		                ipBetween, ipEnd
		                ,ctCombo,//应用类型
		                saveButtonForBSMNoSelected
		        ]
		    },{
		    	xtype : 'toolbar',
		    	border:false,
		        items : [
		        	cmCombo,//中间件
		        	osCombo,//操作系统
		        	groupCombo,//设备组
		        	queryButtonForBSMNoSelected, 
		        	resetBtn
		        ]
		    }
	    ],
	    collapsible : false,
	    bbar : bsPageBar
	});
	/** 判断增加按钮是否可用* */
	equigrid_panel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		saveButtonForBSMNoSelected.setDisabled (selections.length === 0);
	});
	
	/********************** 参数历史--相关组件 **********************************************************************/
	/** 参数历史列表model* */
	Ext.define ('paramHisModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'iconfig',
	                type : 'long'
	            },
	            {
	                name : 'ivalues',
	                type : 'string'
	            }
	    ]
	});
	/** 参数历史列表store* */
	var paramHisStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramHisModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getParamHisList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	paramHisStore.on('beforeload', function(store, options) {
		var new_params = 
		{
			systemId:isystemId
		};
		Ext.apply(store.proxy.extraParams, new_params);
	});
	
	/** 参数信息列表column* */
	var paramHisColumns = [
        {
            text : '序号',
            width : 40,
            locked : true,
            xtype : 'rownumberer'
        },
        {
            text : 'iid',
            dataIndex : 'iid',
            hidden : true
        },
        {
            text : '启动参数',
            dataIndex : 'ivalues',
            sortable:false,
            flex: 1
        }
	];
	
	/**参数历史列表gridpanel* */
	var param_history_grid = Ext.create ('Ext.grid.Panel',
	{
		width : 610,
		height : 265,
		autoScroll : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
	    store : paramHisStore,
	    border : false,
	    columnLines : true,
	    columns : paramHisColumns
	});
	
	param_history_grid.addListener('itemclick', rowclickFn);
	function rowclickFn( grid, record, item, index, e, eOpts ){  
		var configStr = record.data.iconfig;
		var startParamStr = record.data.ivalues;
		var paramRecs = startParamStr.split("|");
		for(var i=0; i<paramRecs.length; i++){
			var paramStr = paramRecs[i];
			var param = paramStr.split(";");
			var name = param[0];
			var value = param[1];
			var paramStore = param_info_grid.store;
			for (var ii = 0; ii < paramStore.getCount (); ii++)
			{
				var paramRecord = paramStore.getAt (ii);
				var iname = paramRecord.data.iparamName;
				if(iname==name){
					paramRecord.set('iparamValue',value);	
				}
			}
		}
		
		//处理检查规则配置
		if(!(''==configStr || null==configStr || undefined==configStr))
		{
			var configParam = configStr.split(";");
			var configValue = configParam[1];
			for (var jj = 0; jj < hcitemStore.getCount (); jj++)
			{
				var hcitemRecord = hcitemStore.getAt (jj);
				if(configValue==hcitemRecord.data.hcitemId){
					hcitemId = configValue;
					hcitemStore.reload();
					ruleInfoStore.load();
					break;
				}
			}
		}
		param_his_win.hide();
	}
	//历史参数--窗口
	var param_his_win = Ext.create('Ext.window.Window', {
		//id:'param_his_win',
		width : 630,
		height : 340,
		resizable:false,
		plain : false,
	    modal : true,
	    draggable : false,
	    closeAction: 'hide',
	    items:  [param_history_grid],
	    buttons : [ 
		    {
				text : '刷新',
				handler : function()
				{
//					alert("刷新按钮。。。");
					paramHisStore.reload();
				}
			},
			{
				text : '关闭',
				handler : function() 
				{
//					alert("关闭按钮。。。");
					param_his_win.hide();
				}
			}
		]
	});
	
	//卡片布局 可以定义多个卡片 但每次只显示一个
    //可以通过setActiveItem指定
    //常用来做向导、分步提交
    Ext.application({
        name: 'card',
        launch: function () {
            var navigate = function (panel, direction) {
                var layout = panel.getLayout();
                layout[direction]();
                if(!layout.getPrev()){
                	Ext.getCmp('move-prev').hide();
                } else {
                	Ext.getCmp('move-prev').show();
                }
                if(!layout.getNext()){
                	Ext.getCmp('move-next').hide();
                } else {
                	Ext.getCmp('move-next').show();
                }
                Ext.getCmp('move-prev').setDisabled(!layout.getPrev());
                Ext.getCmp('move-next').setDisabled(!layout.getNext());
            };
           var cardPanel = Ext.create('Ext.panel.Panel', {
            	//title: '任务信息',
                width: '100%',
                height: "100%",
                layout: 'card',
                activeItem: 0,  //默认显示的card 从0号开始   
                //x: 0,
                //y: 0,
                //bodyStyle: 'padding:15px',
                defaults: { border: false },
                //bbar 底部工具栏 tbar顶部工具栏
                bbar: [{
                    id: 'move-prev',
                    text: '上一步',
                    hidden : true,
                    cls : 'Common_Btn',
                    handler: function (btn) {/*
	            		 navigate(btn.up("panel"), "prev");
	            		 var edittitle = nomalEdit?'编辑':'创建';
	            		 var tabTitle = nomalTask?edittitle+'常用任务':'任务申请';
	            		 if(cardPanel.layout.activeItem.name=="taskStepForm")
	                 	 {
	                 		//cardPanel.setTitle("任务信息");
	            			Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-任务信息");
//	            			Ext.getCmp('createTaskSubmitModel').hide();
	                 		Ext.getCmp('createTaskSubmit').hide();
	                     } 
	             		 else if(cardPanel.layout.activeItem.name=="immediatelyExecPanel")
	                     {
	                 		//cardPanel.setTitle("执行策略");
	             			Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-执行策略");
//	             			Ext.getCmp('createTaskSubmitModel').hide();
	                 		Ext.getCmp('createTaskSubmit').hide();
	                     } 
	                     else if(cardPanel.layout.activeItem.name=="param_info_grid_id" )
	                     {
	                     	//cardPanel.setTitle("运行参数");
	                    	Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-运行参数");
//	                    	Ext.getCmp('createTaskSubmitModel').hide();
	                     	Ext.getCmp('createTaskSubmit').hide();
	                     }
	                     else if(cardPanel.layout.activeItem.name=="server_info_grid_id")
	                     {
	                     	//cardPanel.setTitle("选择设备");
	                    	 Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-选择设备");
//	                    	 Ext.getCmp('createTaskSubmitModel').show();
	                     	Ext.getCmp('createTaskSubmit').show();
	                     }
                    */},
                    disabled: true
                },
                '->',
                {
                    id: 'move-next',
                    text: '下一步',
                    hidden:true,
                    cls : 'Common_Btn',
                    handler: function (btn) {/*
                    	var flag = true;
                    	if(cardPanel.layout.activeItem.name=="taskStepForm")
                    	{
                        	//第一步"任务信息"页内容填写
                        	flag = taskInfoStep();
                        } 
                    	else if(cardPanel.layout.activeItem.name=="immediatelyExecPanel")
                        {
                    		//第二步"任务信息"页内容填写
//                    		if(ftpConfig == "1" || ftpConfig == "2"){
//                    			Ext.MessageBox.alert("提示","工程的文件下发配置不正确,请返回重新配置工程");
//                    			return;
//                    		}
                        	flag = strategyStep();
                        } 
                        else if(cardPanel.layout.activeItem.name=="param_info_grid_id")
                        {
                        	//第三部"运行参数"页内容填写
                        	flag = paramStep();
                        }
                        else if(cardPanel.layout.activeItem.name=="server_info_grid_id")
                        {
                        	//第四部"选择设备"页内容填写
                        	flag = equiStep();
                        }
                    	if(flag){
                    		navigate(btn.up("panel"), "next");
                    		var tabTitle = nomalTask?'创建常用任务':'任务申请';
                    		if(cardPanel.layout.activeItem.name=="taskStepForm")
                        	{
                        		//cardPanel.setTitle("任务信息");
                    			Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-任务信息");
//                    			Ext.getCmp('createTaskSubmitModel').hide();
                        		Ext.getCmp('createTaskSubmit').hide();
                            } 
                    		else if(cardPanel.layout.activeItem.name=="immediatelyExecPanel")
                            {
                        		//cardPanel.setTitle("执行策略");
                    			Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-执行策略");
//                    			Ext.getCmp('createTaskSubmitModel').hide();
                        		Ext.getCmp('createTaskSubmit').hide();
                            } 
                            else if(cardPanel.layout.activeItem.name=="param_info_grid_id")
                            {
                            	//cardPanel.setTitle("运行参数");
                            	Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-运行参数");
//                            	Ext.getCmp('createTaskSubmitModel').hide();
                            	Ext.getCmp('createTaskSubmit').hide();
                            }
                            else if(cardPanel.layout.activeItem.name=="server_info_grid_id")
                            {
                            	//cardPanel.setTitle("选择设备");
                            	Ext.getCmp("icSPDBCreateTaskWin").setTitle(tabTitle+"【" + $('#ictaskname').val() + "】-选择设备");
//                            	Ext.getCmp('createTaskSubmitModel').show();
                            	Ext.getCmp('createTaskSubmit').show();
                            }
                    	}
                    */}
                },{
    				xtype : 'button',
    				id : 'createTaskSubmitModel',
    				cls : 'Common_Btn',
    				hidden : true,
    				text : '生成任务模版',
    				handler : function(){
    					
    				}
    			},{
    				xtype : 'button',
    				id : 'createTaskSubmit',
    				cls : 'Common_Btn',
    				hidden : false,
    				text : '确认',
    				handler : confiremHandler
    			}],
                renderTo: "icspdbcreatetaskpage_grid_area",
                listeners:{
                	beforerender:function(cardpanel){
                		var items =null;
           			 	items =[immediatelyExecPanel];
           			 	cardpanel.add(items);
                	}
                }
            });
        }
    });
    
    // 任务信息步骤
    function taskInfoStep()
    {
    	var form_taskName_input = taskName.getValue();
    	if(nomalTask){
			var nomalTaskNameValue = nomalTaskName.getValue();
			if(trim (nomalTaskNameValue) == '' || (null == nomalTaskNameValue)){
				Ext.MessageBox.alert ("提示", "请填写常用任务名称");
				return false;
			}
		}
		if (trim (form_taskName_input).length > 255)
		{
			Ext.MessageBox.alert ("提示", "任务名称长度不能超过255个字符");
			return false;
		}
    	var auditorValue = auditorComBox.getRawValue();
    	if(null == auditorValue || "" == auditorValue)
    	{
    		Ext.Msg.alert('提示', "请选择审核人!");
    		return false;
    	}
		var executorValue = executorComBox.getRawValue();
    	if(null == executorValue || "" == executorValue)
    	{
    		Ext.Msg.alert('提示', "请选择执行人!");
    		return false;
    	}
    	var listComBoxValue = listComBox.getRawValue();
    	var listNames = listName.getValue();
    	if(("日常变更" == listComBoxValue||"重大变更" == listComBoxValue||"维护" == listComBoxValue) && (null == listNames || "" == listNames)&&!nomalTask)
    	{
    		Ext.Msg.alert('提示', "请填写单号信息!");
    		return false;
    	}else
    	{
    		if("后补" == listComBoxValue)
    		{
    		    listName.setValue('');
    		}
    	}
    	if(trim(listNames).length > 255)
    	{
    		Ext.Msg.alert('提示', "单号不能超过255字符!");
    		return false;
    	}
    	//校验常用任务名
		Ext.Ajax.request (
				{
				    url : 'nomalTaskExits.do',
				    method : 'POST',
				    sync : true,
				    params :
				    {
				        nomalTaskName : nomalTaskNameValue
				    },
				    success : function (response, options)
				    {
				    	var succ = Ext.decode (response.responseText).success;
						var message = Ext.decode (response.responseText).message;
						if(!succ){
							Ext.MessageBox.show (
									{
										width : 300,
										title : "校验常用任务名",
										msg : message,
										buttonText :
										{
											yes : '确定'
										},
										buttons : Ext.Msg.YES
									});
							return false;
						}
				    },
				    failure : function(result, request) {
						secureFilterRs(result,"校验常用任务名称失败！");
						return false;
					}
				});
    	return true;
    }
    
    // 执行策略步骤
    function strategyStep()
    {
    	var execRadioValue = immediatelyExecForm.getForm().getValues()["execRadio"];
    	var execTimeValue = Ext.getCmp('execTime').getValue();
    	if(execRadioValue == 2 && (null == execTimeValue || "" == execTimeValue ))
    	{
    		Ext.Msg.alert('提示', "请选择执行时间!");
    		return false;
    	}
    	
    	if(execRadioValue == 2){
	    	var validExecTime = Ext.Date.format(execTimeValue,"Y-m-d H:i:s");
	    	if(null == validExecTime || "" == validExecTime){
	    		Ext.Msg.alert('提示', "输入格式不正确，请按yyyy-MM-dd HH:mm:ss的格式输入！");
	    		return false;
	    	}
    	}
    	
    	
    	var cronValue = cycleExecCronText.getValue();
    	if(execRadioValue == 3 && ( null == cronValue || "" == cronValue )){ // "",null,undefined
    		Ext.Msg.alert('提示', "请选择执行周期!");
            return false;
        }
    	
    	var uploadFlag = uploadFileConfigCheckBox.getValue();
    	var uploadSuccessFlagText = Ext.getCmp('uploadSuccessFlagText').getValue();
    	if(uploadFlag){
    		if(uploadSuccessFlagText == 'false')
    		{
    			Ext.Msg.alert('提示', "请确认下发文件是否已上传成功!");
    			return false;
    		}
    	}
    	
    	paramStoreReload();
    	return true;
    }
    
    // 运行参数步骤
    function paramStep()
    {
    	//处理参数
		var store = paramInfoStore;
		for (var i = 0, lenn = store.getCount (); i < lenn; i++)
		{
			var record = store.getAt (i);
			//参数校验
			var iname = record.data.iparamName;
			var itype = record.data.iparamType;
			var ivalue = record.data.iparamValue;
			var reStr = validateParamRec(iname,itype,ivalue);
			if (!reStr[0]) {
				Ext.MessageBox.alert('信息提示', reStr[1]);
				return false;
			}
		}
    	return true;
    }
    
    // 运行参数步骤
    function equiStep()
    {
    	return true;
    }
    
    // 快照按钮
	function snapshotFunction(){
		var xmlSnapshot = Ext.create('Ext.window.Window', {
			id : 'xmlSnapshot',
			title : '任务步骤图',
			width : contentPanel.getWidth(),
			height : contentPanel.getHeight(),
			maximizable : true,
			layout : 'border',
			modal : true,
			closeAction : 'destroy',
			loader : {
				url : "page/grapheditor/webstudio/show/show.jsp?prjID="+projectId,
				autoLoad : true,
				params : {
					projectname : $('#ictaskname').val()
				},
				autoDestroy : true,
				scripts : true
			}
		});
		xmlSnapshot.show();
	}
	function submitNomalTask(btn){
		submitIns (1);
	}


	/**
	 * @desc '确认' 按钮的处理逻辑
	 * */
	function confiremHandler ()
	{		
		var val="";
		var execRadioValue = immediatelyExecForm.getForm().getValues()["execRadio"];
		
		if(null==execRadioValue){
			Ext.MessageBox.alert ("提示", "请选择执行策略");
			return false;
		}else if(IDX_RADIO_TIMED==execRadioValue){ 		 	//定时执行
			var execTimeValue = Ext.getCmp('execTime').getValue();
			var gedt = Ext.util.Format.date(execTimeValue, 'Y-m-d H:i:s');
			if(execTimeValue==null ||''==execTimeValue){
				Ext.MessageBox.alert ("提示", "执行时间不能为空");
				return false;
			}else{
				val=gedt;
			}
		}else if(IDX_RADIO_CYCLE==execRadioValue){ 			//周期执行
			var cycleCornValue = Ext.getCmp('cycleExecCronText').getValue();
			if(cycleCornValue==null ||''==cycleCornValue){
				Ext.MessageBox.alert ("提示", "执行周期不能为空");
				return false;
			}else{
				val=cycleCornValue;
			}
		}
		parent.showCtrol(execRadioValue,val);
	}
	
	//选择设备按钮
	function openSelectWindow(){
		//校验是否选择了模板
		if (isystemId == 0)
		{
			Ext.MessageBox.alert ("提示", "请选择模板名称");
			return false;
		}
		showSelCmpterWin();
	}
	
	var icSpdbEquiSelectedPagekWin;
	function showSelCmpterWin(recordiid,typeid,iprojectname)
	{
		var opType = listComBox.getValue();
    	var opTypeNum = listName.getValue();
    	console.log("===============opTypeNum=========================================="+opTypeNum);
		if (icSpdbEquiSelectedPagekWin == undefined || !icSpdbEquiSelectedPagekWin.isVisible()) {
			icSpdbEquiSelectedPagekWin = Ext.create('Ext.window.Window', {
				id : 'icSpdbEquiSelectedPagekWin',
				title : '选择设备',
				modal : true,
				closeAction : 'destroy',
				constrain : true,
				autoScroll : true,
				upperWin : icSpdbEquiSelectedPagekWin,
				width: contentPanel.getWidth() - 150,
				height:  contentPanel.getHeight(),
				//width : contentPanel.getWidth() - 350,
				//height : contentPanel.getHeight() - 200,
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				layout : 'fit',
				loader : {
					url : 'icSpdbEquiSelected.do',
					params : {
						opType :opType,
						opTypeNum :opTypeNum
					},
					autoLoad : true,
					autoDestroy : true,
					scripts : true
				}
			});
		}
		icSpdbEquiSelectedPagekWin.show();
	}
	
//	function showSelCmpterWin(){
//		var selCmpterWin = Ext.create('Ext.window.Window', {
//		    title: '选择设备',
//		    width: 800,
//		    height: 470,
//		    modal:true,
//		    closeAction: 'hide',
//		    items:  [equigrid_panel]
//		});
//		selCmpterWin.on("show",function(self, eOpts){
//			resetWhere();
//			cmoputerStore.load({  
//		        params:{ 
//		        	pageFlag : 1 //传递参数1时，弹出的设备选择页，带有查询列表formpanel
//		        }  
//		    });
//		});
//		selCmpterWin.doLayout();
//		selCmpterWin.show();
//	}
	
	//历史参数按钮
	function hisParamBtnHandler(){
		//校验是否选择了模板
		if (isystemId == 0)
		{
			Ext.MessageBox.alert ("提示", "请选择模板名称");
			return false;
		}
		//参数历史--store加载
		var grid = param_history_grid;
		grid.store.load();
		//参数历史--窗口属性设置
		//var param_his_win = Ext.getCmp("param_his_win");
		param_his_win.setTitle("历史参数--"+$('#ictaskname').val());
	    var bd = (document.body || document.documentElement);
	    var left = bd.scrollLeft + Ext.getBody().getWidth() - 4 - param_his_win.width;
	    var top = bd.scrollTop + Ext.getBody().getHeight() - 4 - param_his_win.height;
	    param_his_win.setPosition(left, top);
		param_his_win.show();
	}

	/**
	 * 计算一年是否闰年
	 */
	function isLeapYear(year) {  
		return (year % 4 == 0) && (year % 100 != 0 || year % 400 == 0);  
	}

	//校验参数方法
	function validateParamRec(iname,itype,ivalue){
		var reStr = new Array();
		reStr[0] = true;
		reStr[1] = '';
		if (trim (ivalue).length > 255)
		{
			reStr[0] = false;
			reStr[1] = '参数值: ' + iname + ' 长度不能超过255个字符！';
			return reStr;
		}
		if (itype == 0 || itype == "Integer") {
			if(ivalue==""){
				reStr[0] = false;
				reStr[1] = '参数 ' + iname + ' 请填写Integer类型数据(非负整数)！';
				return reStr;
			}
			if (!isNotNegativeInteger(ivalue)) {
				reStr[0] = false;
				reStr[1] = '参数 ' + iname + ' 请填写Integer类型数据(非负整数)！';
				return reStr;
			}
		} else if (itype == 2 || itype == "String") {
			var checkShellPath = ivalue;
			if(iname!='appName'){
				if (!checkIsNotEmpty(checkShellPath)) {
					reStr[0] = false;
					reStr[1] = '参数 ' + iname + ' 请填写String类型数据(不能为空)！';
					return reStr;
				}
				if (checkShellPath != "") {
					var errorChar = "!@#$%^&*|";
					for (var ii = 0; ii < errorChar.length; ii++) {
						var check = errorChar.substring(ii,ii+1);
						if (checkShellPath.indexOf(check) >= 0) {
							reStr[0] = false;
							reStr[1] = '参数 ' + iname + ' 项检查包含非法字符串，请确认!';
							return reStr;
						}
					}
				}
			}
		}
		return reStr;
	}

	//导入Excel参数
	function importExcel() {
		//校验是否选择了模板
		if (isystemId == 0)
		{
			Ext.MessageBox.alert ("提示", "请选择模板名称");
			return false;
		}
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        //id:'upLoadpanel',  
	        width:370,
	        height:100,
		    frame: true,
		    buttonAlign : 'rignt',
			items: [
				{
					//id:'fileFildId',
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 80,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				},
				{
					//id:'paramNamesId',
					xtype:'hidden',
					name:'paramNames',
					value:paramNames,
					width:360,
					height:130
				}
			],
			buttons: [
					{
						//id:'upldBtnId',
						text: '批量导入',
						margin:'0 0 0 5',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
							
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
			    			if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}

							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								 form.submit({
									url: 'importParamExcel.do',
								    success: function(form, action) {
								    	var msg = Ext.decode(action.response.responseText).message;
										var dataStr = Ext.decode(action.response.responseText).datastr;
										//分隔字符串
										var result = dataStr.split(";;;");
										var notFoundStr = result[0];//不存在的字符串
										var paramsStr = result[1];//匹配成功的参数
										var fmtErrStr = result[2];//存在但格式不正确的参数
										//处理不存在
										var msgStr = "";
										if(!(notFoundStr==null || notFoundStr=="" || notFoundStr==undefined)){
											msgStr += "参数名【"+notFoundStr+"】在导入模板中不存在！<br>";
										}
										
										//处理匹配成功的参数
										var params = paramsStr.split(",,,");
										for(var mm=0; mm<params.length; mm++){
											var param = params[mm];
											if(!(param==null || param=="" || param==undefined)){
												var val = param.split(":::");
												var name = val[0];
												var value = val[1];
												//给页面赋值
												var store = paramInfoStore;
												for (var i = 0; i < store.getCount (); i++)
												{
													var record = store.getAt (i);
													var iname = record.data.iparamName;
													if(iname==name){
														record.set('iparamValue',value);	
													}
												}
											}
										}
										//处理存在但格式不正确的参数
										if(!(fmtErrStr==null || fmtErrStr=="" || fmtErrStr==undefined)){
											msgStr += "<br>导入参数【"+fmtErrStr+"】格式不正确！<br>";
										}
										//提示信息
										if(!(msgStr=="" || msgStr==undefined)){
											Ext.MessageBox.alert("提示",msgStr);
										}
										else
										{
											Ext.MessageBox.alert("提示",msg);
										}
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
			//id:'upldWinId',
		    title: '参数信息批量导入',
		    width: 400,
		    height: 150,
		    modal:true,
		    closeAction: 'desdroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = upLoadformPane.getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}

	// 导出模板方法
	function exportParamBtnHandler() {
		window.location.href = 'exportSpdbParams.do';
	}

	function checkFile(fileName){
	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
	    if(!file_reg.test(fileName)){  
	    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
	        return false;
	    }
	    return true;
	}
	
	function paramStoreReload(){
		
		//处理参数
		var jsonParamArr = [];
		var paramStore = paramInfoStore;
		for (var j = 0, lenn = paramStore.getCount (); j < lenn; j++)
		{
			var paramRecord = paramStore.getAt (j);
			if(fileselct=='true'){
				if(paramRecord.data.iparamName=='patchlist'){
					paramRecord.data.iparamValue=textfile.getValue();
				}
			}
			jsonParamArr.push(paramRecord.data);
		}
		
		// 参数信息列表grid重新加载
		paramInfoStore.load(
		{
			params :
	        {
				systemId : projectId,
	        	sysName : $("#ictaskname").val(),
	        	jsonData : Ext.encode(jsonParamArr),
	        	uploadFlag : uploadFileConfigCheckBox.getValue()
	        }
	        
		});
	}
	var tabTitle = nomalTask?'创建常用任务':'任务申请';
	
	Ext.getCmp("icSPDBCreateTaskWin").setTitle("创建周期执行任务");
	if(flagDmDoubleCheck){
		listComBox.setValue(2);
		listComBox.setRawValue("后补");
		auditorComBox.setRawValue(userId);
		auditorComBox.setValue(userName);
		executorComBox.setRawValue(userId);
		executorComBox.setValue(userName);
	}
});
