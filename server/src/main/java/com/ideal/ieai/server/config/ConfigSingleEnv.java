package com.ideal.ieai.server.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;
import java.util.Properties;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.ieaikernel.ServerEnv;

public class ConfigSingleEnv
{
    private Logger                 _log         = Logger.getLogger(ConfigSingleEnv.class);
    public static Properties       properties   = new Properties();

    private static ConfigSingleEnv configReader = null;

    public static ConfigSingleEnv getInstance ()
    {
        if (null == configReader)
        {
            configReader = new ConfigSingleEnv();
        }
        return configReader;
    }

    public Properties getProperties () throws IOException
    {
        return properties;
    }

    public void init () throws IOException
    {
        InputStreamReader fio = new InputStreamReader(new FileInputStream(ServerEnv.getInstance().getMultiStepConfigFileName()), "GBK");
        properties.load(fio);
        fio.close();
    }

    public String getProperties ( String key )
    {
        return properties.getProperty(key);
    }

    public String getProperties ( String key, String defaultValue )
    {
        return properties.getProperty(key, defaultValue);
    }

    public List getAllStageNameList ( String separator )
    {
        List list = new ArrayList();
        try
        {
            if (!"".equals(separator))
            {
                Iterator<Entry<Object, Object>> it = properties.entrySet().iterator();
                while (it.hasNext())
                {
                    Entry en = (Entry) it.next();
                    String keys = (String) en.getKey();
                    String[] key = keys.split(separator);
                    if (null != key && key.length > 0)
                    {
                        for (int i = 0; i < key.length; i++)
                        {
                            list.add(key[i]);
                        }
                    }
                    break;
                }
            } else
            {
                Iterator<Entry<Object, Object>> it = properties.entrySet().iterator();
                while (it.hasNext())
                {
                    Entry en = (Entry) it.next();
                    list.add(en.getKey());
                }
            }
        } catch (Exception e)
        {
            _log.error("getAllStageNameList is error " + e.getMessage());
            list = null;
        }
        return list;
    }

    public HashMap getAllStageNameMap ( String separator )
    {
        HashMap map = new HashMap();
        try
        {
            if (!"".equals(separator))
            {
                Iterator<Entry<Object, Object>> it = properties.entrySet().iterator();
                while (it.hasNext())
                {
                    Entry en = (Entry) it.next();
                    String keys = (String) en.getKey();
                    String[] key = keys.split(",");
                    if (null != key && key.length > 0)
                    {
                        for (int i = 0; i < key.length; i++)
                        {
                            map.put(key[i], key[i]);
                        }
                    }
                    break;
                }
            } else
            {
                Iterator<Entry<Object, Object>> it = properties.entrySet().iterator();
                while (it.hasNext())
                {
                    Entry en = (Entry) it.next();
                    map.put(en.getKey(), en.getValue());
                }
            }
        } catch (Exception e)
        {
            map = null;
            _log.error("getAllStageNameMap is error " + e.getMessage());
        }
        return map;
    }
}
