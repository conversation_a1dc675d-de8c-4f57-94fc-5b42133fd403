package com.ideal.ieai.server.jobscheduling.repository.projectdrivingmode;

/**
 * 
 * @ClassName:  ProjectDrivingModeBean   
 * @Description:工程并发模式及分支判断模式bean   
 * @author: licheng_zhao 
 * @date:   2019年1月6日 上午11:06:31   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
public class ProjectDrivingModeBean
{
    private String id;
    private String systemName;
    private String projectName;
    private String flowName;
    private String flowConcurrentMode;
    private String flowBranchCondition;
    private String sort;

    public String getId ()
    {
        return id;
    }

    public void setId ( String id )
    {
        this.id = id;
    }

    public String getSystemName ()
    {
        return systemName;
    }

    public void setSystemName ( String systemName )
    {
        this.systemName = systemName;
    }

    public String getProjectName ()
    {
        return projectName;
    }

    public void setProjectName ( String projectName )
    {
        this.projectName = projectName;
    }

    public String getFlowName ()
    {
        return flowName;
    }

    public void setFlowName ( String flowName )
    {
        this.flowName = flowName;
    }

    public String getFlowConcurrentMode ()
    {
        return flowConcurrentMode;
    }

    public void setFlowConcurrentMode ( String flowConcurrentMode )
    {
        this.flowConcurrentMode = flowConcurrentMode;
    }

    public String getFlowBranchCondition ()
    {
        return flowBranchCondition;
    }

    public void setFlowBranchCondition ( String flowBranchCondition )
    {
        this.flowBranchCondition = flowBranchCondition;
    }

    public String getSort ()
    {
        return sort;
    }

    public void setSort ( String sort )
    {
        this.sort = sort;
    }

}
