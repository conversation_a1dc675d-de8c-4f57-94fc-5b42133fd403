
INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (7,'健康巡检','健康巡检模块组',7,'images/info109.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7005, '巡检看板', 7, 'inspectionPanel.do', 3, '健康巡检', 'images/info106.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7099, '配置启停', 7, '', 4, '健康巡检', 'images/info37.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7100, '监控报表', 7, '', 5, '健康巡检', 'images/info26.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7101, '历史查询', 7, '', 6, '健康巡检', 'images/info35.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7034, '新巡检配置', 7, 'hcmainconfig/initHcMainConfigPage.do', 1, '配置启停', 'images/info3.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7155, '新巡检批量启停', 7, 'supperCheckHcBatchIndex.do', 2, '配置启停', 'images/info4.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7035, '新巡检项管理', 7, 'initSupperCheckItem.do', 3, '配置启停', 'images/info7.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7037, '新巡检模板配置', 7, 'checkHctemplate/templateIndex.do', 4, '配置启停', 'images/info245.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7022, '报警联系人配置', 7, 'icalarmconfig.do', 6, '配置启停', 'images/info121.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7032, '报警码管理', 7, 'hdErrorCode.do', 7, '配置启停', 'images/info4.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7020, '系统分类', 7, 'systypeConfig.do', 8, '配置启停', 'images/info22.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7006, '巡检监控', 7, 'businessAndCpmonitor.do', 1, '监控报表', 'images/info25.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7072, '分类总览', 7, 'classOverviewSystype.do', 4, '监控报表', 'images/info4.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7019, '巡检报表', 7, 'resultReportIndexHC.do', 1, '历史查询', 'images/info17.png') ;
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES (7014, '历史报警', 7, 'queryAlarmHis.do', 2, '历史查询', 'images/info69.png') ;


INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7001, '批量处理', 'dealActWarning.do', '业务监控--铃铛图标(活动系统异常界面)--批量处理按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7031, '保存', 'saveSystypeConfig.do', '系统分类--保存按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7032, '删除', 'delSystypeConfig.do', '系统分类--删除按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7033, '保存', 'saveIcAlarmConfig.do', '健康巡检--报警联系人配置--保存按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7034, '删除', 'deleteIcAlarmConfig.do', '健康巡检--报警联系人配置-删除按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7062, '保存', 'saveAlarmErrCode.do', '健康巡检--报警码管理--保存按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7063, '删除', 'deleteHdErrorCode.do', '健康巡检--报警码管理--删除按钮') ;

INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7001, 7006, 7001, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7031, 7020, 7031, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7032, 7020, 7032, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7033, 7022, 7033, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7034, 7022, 7034, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7062, 7032, 7062, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7063, 7032, 7063, '') ;


INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7068, '保存', 'hcmainconfig/saveConfigCheckItem.do', '健康巡检--新巡检配置--检查项列表--保存按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7069, '删除', 'hcmainconfig/deleteCheckitemData.do', '健康巡检--新巡检配置--检查项列表--删除按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7070, '保存', 'hcmainconfig/saveCheckPointData.do', '健康巡检--新巡检配置--检查点列表--保存按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7071, '删除', 'hcmainconfig/deleteCheckpointData.do', '健康巡检--新巡检配置--检查点列表--删除按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7072, '启动', 'hcStart.do', '健康巡检--新巡检配置--应用服务器列表--启动按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7073, '终止', 'hcStop.do', '健康巡检--新巡检配置--应用服务器列表--终止按钮') ;

INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7068, 7034, 7068, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7069, 7034, 7069, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7070, 7034, 7070, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7071, 7034, 7071, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7072, 7034, 7072, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7073, 7034, 7073, '') ;

INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7064, '保存', 'saveSupperChkitem.do', '健康巡检--新巡检项管理--保存按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7065, '删除', 'deleteSupperChkitem.do', '健康巡检--新巡检项管理--删除按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7066, '导入', 'itemImport.do', '健康巡检--新巡检项管理--导入按钮') ;
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7067, '导出', 'itemExport.do', '健康巡检--新巡检项管理--导出按钮') ;

INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7064, 7035, 7064, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7065, 7035, 7065, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7066, 7035, 7066, '') ;
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7067, 7035, 7067, '') ;

INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7155, '业务系统维度批量启动', 'batchHcStartOfSys.do', '健康巡检--巡检批量启停--业务系统维度--批量启动按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7156, '业务系统维度批量终止', 'batchHcStopOfSys.do', '健康巡检--巡检批量启停--业务系统维度--批量终止按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7157, '巡检项维度批量启动', 'batchHcStartOfItem.do', '健康巡检--巡检批量启停--巡检项维度--批量启动按钮');
INSERT INTO IEAI_HIGHOPER (IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES (7158, '巡检项维度批量终止', 'batchHcStopOfItem.do', '健康巡检--巡检批量启停--巡检项维度--批量终止按钮');

INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7155, 7155, 7155, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7156, 7155, 7156, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7157, 7155, 7157, '');
INSERT INTO IEAI_MENU_BUTTON (IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES (7158, 7155, 7158, '');
