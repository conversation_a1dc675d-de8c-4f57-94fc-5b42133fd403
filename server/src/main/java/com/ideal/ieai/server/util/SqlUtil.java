package com.ideal.ieai.server.util;


/**   
 * @ClassName:  StrUtil   
 * @Description:拼接sql 工具类   
 * @author: zhen_sui 
 * @date:   2018年2月1日 下午4:55:46   
 *     
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class SqlUtil
{
    public static String generateInSql(String params,FieldType type){
        switch (type)
        {
            case String:
                String paramStr= params.replace(",", "','");
                return " ('"+paramStr+"') ";
            case Number:
            default:
                return " ("+params+") ";
                
        }
        
        
    }
}

