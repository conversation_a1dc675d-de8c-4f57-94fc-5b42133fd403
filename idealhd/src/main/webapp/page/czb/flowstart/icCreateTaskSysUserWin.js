Ext.define('page.czb.flowstart.icCreateTaskSysUserWin', {
    extend: 'Ext.window.Window',
    id: 'icCreateTaskSysUserWin_editId',
    title: '选择系统用户',
    autoScroll: true,
    modal: true,
    closeAction: 'destroy',
    buttonAlign: 'center',
    draggable: true,
    resizable: false,
    width: 400,
    height: 160,
    record: {},
    winRet: false,
    initComponent: function () {
        var me = this;
        // 清理主面板的各种监听时间
        destroyRubbish();
        /** 系统用户列表model* */
        Ext.define('systemUserModel',
            {
                extend: 'Ext.data.Model',
                fields: [
                    {
                        name: 'loginName',
                        type: 'string'
                    },
                    {
                        name: 'fullName',
                        type: 'string'
                    }
                ]
            });

        /** 系统用户请求* */
        var systemUserStore = Ext.create('Ext.data.Store',
            {
                autoLoad: true,
                autoDestroy: true,
                model: 'systemUserModel',
                proxy:
                    {
                        type: 'ajax',
                        url: 'getStartUserList.do',
                        reader:
                            {
                                type: 'json',
                                root: 'dataList'
                            }
                    }
            });

        var systemUserComBox = Ext.create('Ext.form.ComboBox',
            {
                editable: false,
                fieldLabel: "请选择",
                labelWidth: 65,
                store: systemUserStore,
                labelAlign: 'center',
                queryMode: 'local',
                emptyText: '--请选择系统用户--',
                //allowBlank : false,// 不允许为空
                width: '100%',
                displayField: 'fullName',
                valueField: 'loginName',// ,
            });
        /****** 页面渲染(主panel) ******/
        Ext.applyIf(me, {
            dockedItems: [{
                xtype: 'toolbar',
                border: false,
                dock: 'bottom',
                margin: '0 0 5 0',
                layout: {pack: 'center'},
                items: [{
                    xtype: 'button',
                    text: '确定',
                    cls: 'Common_Btn',
                    handler: function () {
                        var  systemUser = this.up('window').down('combobox').getValue();
                        console.log(systemUser);
                        var ret = {
                            systemUser: systemUser,
                        };
                        me.retWinBind(ret);
                    }
                },
                    {
                        xtype: 'button',
                        text: '取消',
                        cls: 'Common_Btn',
                        handler: function () {
                            this.up("window").close();
                        }
                    }]
            }],
            items: [systemUserComBox]
        });
        me.callParent(arguments);
    }
});