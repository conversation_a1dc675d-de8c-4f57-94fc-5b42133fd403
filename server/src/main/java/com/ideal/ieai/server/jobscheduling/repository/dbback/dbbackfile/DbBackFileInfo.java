/**
 * All rights Reserved, Designed By www.idealinfo.com
 * 
 * @Title: DbBackFileInfo.java
 * @Package com.ideal.ieai.server.jobScheduling.repository.dbback.dbbackfile
 * @Description: 备份路径
 * @author: 理想科技
 * @date: 2019年3月25日 上午10:39:04
 * @version V1.0
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
 * 
 */
package com.ideal.ieai.server.jobscheduling.repository.dbback.dbbackfile;

/**   
 * @ClassName:  DbBackFileInfo   
 * @Description:备份路径   
 * @author: xinglin_tian 
 * @date:   2019年3月25日 上午10:39:04   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class DbBackFileInfo
{

    private long   iid;
    private long   pathStoreId;
    private String filePath;
    private int    status;

    public long getIid ()
    {
        return iid;
    }

    public void setIid ( long iid )
    {
        this.iid = iid;
    }

    public long getPathStoreId ()
    {
        return pathStoreId;
    }

    public void setPathStoreId ( long pathStoreId )
    {
        this.pathStoreId = pathStoreId;
    }

    public String getFilePath ()
    {
        return filePath;
    }

    public void setFilePath ( String filePath )
    {
        this.filePath = filePath;
    }

    public int getStatus ()
    {
        return status;
    }

    public void setStatus ( int status )
    {
        this.status = status;
    }
}
