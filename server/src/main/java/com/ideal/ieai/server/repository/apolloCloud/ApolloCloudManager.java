package com.ideal.ieai.server.repository.apolloCloud;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.sus.fileopt.bigpkg.BigPkgBean;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ApolloCloudManager{

    private static final Logger       _log       = Logger.getLogger(ApolloCloudManager.class);

    private static ApolloCloudManager pcmIntance = new ApolloCloudManager();

    public static ApolloCloudManager getInstance ()
    {
        if (pcmIntance == null)
        {
            pcmIntance = new ApolloCloudManager();
        }
        return pcmIntance;
    }

    public Map<String, Object> getSingleSysPkgListLoop ( BigPkgBean queryBean, int type )
            throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        type);
                    res = this.getSingleSysPkgList(con, queryBean);
                    break;
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return res;
    }
    
    public Map<String, Object> getSingleSysPkgList ( Connection con, BigPkgBean queryBean ) throws SQLException
    {

        Map<String, Object> res = new HashMap<String, Object>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<Map> list = new ArrayList<Map>();
        String sql = "";
        String where = "  ";
        if (null != queryBean.getIsysname() && !"".equals(queryBean.getIsysname()))
        {
            String sysname = queryBean.getIsysname().trim();

            if (DBManager.Orcl_Faimily())
            {
                if (sysname.contains("_"))
                {
                    sysname = sysname.replaceAll("_", "\\\\_");
                    where += " and C.pipelinesetname LIKE '%" + sysname + "%' escape '\\' ";
                } else
                {
                    where += " and C.pipelinesetname LIKE '%" + sysname + "%' ";
                }
            }
            if (JudgeDB.IEAI_DB_TYPE == 2)
            {
                if (sysname.contains("_"))
                {
                    sysname = sysname.replaceAll("_", "\\\\_");
                    where += " and C.pipelinesetname LIKE '%" + sysname + "%' escape '\\' ";
                } else
                {
                    where += " and C.pipelinesetname LIKE '%" + sysname + "%'";
                }
            }
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                if (sysname.contains("_"))
                {
                    sysname = sysname.replaceAll("_", "\\\\_");
                    where += " and C.pipelinesetname LIKE '%" + sysname + "%' ";
                } else
                {
                    where += " and C.pipelinesetname LIKE '%" + sysname + "%' ";
                }
            }
        }
         
        //String sqlString = " select c.pipelinesetname ISYSNAME,a.flowinstid IDEPLOYTASKID,a.inserttime ICREATEDTIME,c.package_name INAME,a.localdownloadpath IPATCH_PATH,a.state ISATE "
                        //+" from ieai_apollocloud_version a, ieai_apollocloud_oneitem b, ieai_apollocloud_twoitem c "
                        //+" where a.iid = b.apolocloudversionid and a.iid = c.apolocloudversionid and b.apolocloudversionid=c.apolocloudversionid ";
        String sqlString = " select c.pipelinesetname ISYSNAME,a.flowinstid IDEPLOYTASKID,a.inserttime ICREATEDTIME,c.package_name INAME,a.localdownloadpath IPATCH_PATH,a.state ISATE "
                +" from ieai_apollocloud_version a, ieai_apollocloud_twoitem c "
                +" where a.iid = c.apolocloudversionid ";
        String orderString = " ORDER BY  a.inserttime DESC,a.flowinstid  ";
        String countString = "SELECT COUNT(1) AS NUM FROM (";
        int pagePara1 = 0;
        int pagePara2 = 0;
        int start = queryBean.getStart();
        int limit = queryBean.getLimit();
        if (DBManager.Orcl_Faimily())
        { // oracle
            sql = SQLUtil.getQueryPageSQLNew("oracle", sqlString + where + orderString);
            pagePara1 = start + limit;
            pagePara2 = start;
        } else if (3 == JudgeDB.IEAI_DB_TYPE)
        {
            sql = SQLUtil.getQueryPageSQLNew("mysql", sqlString + where + orderString);
            pagePara1 = start;
            pagePara2 = start + limit;
        } else
        {
            sql = SQLUtil.getQueryPageSQLNew("db2", sqlString + where + orderString);
            pagePara1 = start;
            pagePara2 = start + limit;
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(sql);
            int index = 0;
            ps.setInt(++index, pagePara1);
            ps.setInt(++index, pagePara2);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map instanceVersionMap = new HashMap();
                instanceVersionMap.put("INAME", rs.getString("INAME"));
                instanceVersionMap.put("IPATCH_PATH", rs.getString("IPATCH_PATH"));
                instanceVersionMap.put("ISYSNAME", rs.getString("ISYSNAME"));
                
                instanceVersionMap.put("IDEPLOYTASKID", rs.getString("IDEPLOYTASKID"));
                instanceVersionMap.put("ICREATEDTIME",rs.getString("ICREATEDTIME"));
                //instanceVersionMap.put("IID", rs.getLong("IID"));
                //instanceVersionMap.put("PKG_IID", rs.getLong("PKG_IID"));
                instanceVersionMap.put("ISATE", rs.getString("ISATE"));
                //instanceVersionMap.put("IZIP_PATH", rs.getString("IZIP_PATH"));
                //instanceVersionMap.put("IFLAG", rs.getString("IFLAG"));
                list.add(instanceVersionMap);
            }
            int num = 0;
            String cstring = countString + sqlString + where + ") as CountNum";
            if (DBManager.Orcl_Faimily())
            {
                // oracle
                cstring = countString + sqlString + where + ")";
            }
            ps = con.prepareStatement(cstring);
            rs = ps.executeQuery();
            while (rs.next())
            {
                num = rs.getInt("NUM");
            }
            res.put("total", num);
            res.put("dataList", list);
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw e;
        } finally
        {
            if (rs != null)
            {
                rs.close();
            }
            if (ps != null)
            {
                ps.close();
            }
        }
        return res;
    }
}
