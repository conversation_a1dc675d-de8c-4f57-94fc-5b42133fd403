-- 4.7.17 version does not have patches for HC
-- 4.7.18 version does not have patches for HC

-- 4.7.19 version HC patch is as follows 
CREATE or replace PROCEDURE PROC_HD_DATAOVERVIEW    (IN AN_USERID NUMERIC(19,0), IN AN_OBJ_ID NUMERIC(19,0), IN AI_OBJ_TYPE SMALLINT, IN AI_PAGE_ID INTEGER, IN AI_PAGE_SIZE INTEGER, IN AI_DATA_CENTER VARCHAR(255) ,OUT AN_OPT_ID NUMERIC(19,0), OUT AI_REC_AMOUNT INTEGER)
	LANGUAGE SQL
	BEGIN
		DECLARE	CN_MENU_ID			SMALLINT DEFAULT 72;
		DECLARE	V_COUNT				INTEGER;
		DECLARE	LI_PAGE_FROM		INTEGER;
		DECLARE	LI_PAGE_TO			INTEGER;
		DECLARE	LI_PAGE_COUNT		INTEGER;
		
		
		IF	AI_OBJ_TYPE < 0 OR AI_OBJ_TYPE > 4	THEN
			RETURN;
		END	IF;
		
		CALL	PROC_GET_NEXT_ID ('TMP_DATASHOW', AN_OPT_ID);
		
		IF	AN_OPT_ID IS NULL	THEN
			RETURN;
		END	IF;
		
		
		IF AI_OBJ_TYPE BETWEEN 0 AND 1 THEN
			SELECT
				COUNT(P.IID) 
			INTO V_COUNT 
			FROM
				IEAI_USERINHERIT U,
				IEAI_SYS_PERMISSION S,
				IEAI_PROJECT P 
			WHERE
				U.IUSERID = AN_USERID 
				AND S.IROLEID = U.IROLEID
				AND S.IPERMISSION = 1 
				AND S.IPROID = -7
				AND P.IID = S.IPROID 
				AND (P.PROTYPE = -7 OR P.PROTYPE = 7)
				;
		END IF;
		
		
		
		
		IF	AI_OBJ_TYPE = 0	THEN
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S, IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.IID > 0
								AND S.IID=SS.IID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND S.IID=SS.IID
								AND		S.IID >= 0
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
							  AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
					
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 1	THEN
		
			
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND     P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		S.IID >= 0
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 2	THEN
			IF	LENGTH(AI_DATA_CENTER) > 0	THEN
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							NDCNAME,
							OID,
							AN_OBJ_ID,
							OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							AMOUNT,
							CL0,
							CL1,
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	A.NDCNAME,
											A.OID,
											A.OBJ_NAME,
											NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
											SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
											SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
											SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
											SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
											SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
											SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
											SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
											A.NIP
								FROM		(
												SELECT	R.CENTERNAME,
															R.SDID AS OID,
															L.IP AS OBJ_NAME,
															FUN_GET_IP_NUMBER(L.IP) AS NIP,
															N.NDCNAME
												FROM		IEAI_SYS_RELATION R,
															IEAI_COMPUTER_LIST L,
															(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		L.CPID = R.COMPUTERID
												AND		N.CENTERNAME = R.CENTERNAME
											) A,
											(
												SELECT	R.CENTERNAME,
															R.SDID,
															D.CPSTATUS,
															COUNT(*) AS DAT_CNT
												FROM		IEAI_SYS_RELATION R,
															IEAI_COM_CHK C,
															IEAI_CHKPOINT K,
															HD_CHECK_RESULT_DATA_LAST D
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		C.CPID = R.COMPUTERID
												AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
												AND		C.STARTLOGO = 1
												AND		K.SCID = C.SCID
												AND		K.STARTLOGO = 1
												AND		D.CPID = K.CPID
												GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
											) B
								WHERE		B.SDID = A.OID
								AND		B.CENTERNAME = A.CENTERNAME
								AND A.NDCNAME = AI_DATA_CENTER
								GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP
							)
				ORDER	BY	NDCNAME, NIP;
			ELSE
			
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							NDCNAME,
							OID,
							AN_OBJ_ID,
							OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							AMOUNT,
							CL0,
							CL1,
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	A.NDCNAME,
											A.OID,
											A.OBJ_NAME,
											NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
											SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
											SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
											SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
											SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
											SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
											SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
											SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
											A.NIP
								FROM		(
												SELECT	R.CENTERNAME,
															R.SDID AS OID,
															L.IP AS OBJ_NAME,
															FUN_GET_IP_NUMBER(L.IP) AS NIP,
															N.NDCNAME
												FROM		IEAI_SYS_RELATION R,
															IEAI_COMPUTER_LIST L,
															(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		L.CPID = R.COMPUTERID
												AND		N.CENTERNAME = R.CENTERNAME
											) A,
											(
												SELECT	R.CENTERNAME,
															R.SDID,
															D.CPSTATUS,
															COUNT(*) AS DAT_CNT
												FROM		IEAI_SYS_RELATION R,
															IEAI_COM_CHK C,
															IEAI_CHKPOINT K,
															HD_CHECK_RESULT_DATA_LAST D
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		C.CPID = R.COMPUTERID
												AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
												AND		C.STARTLOGO = 1
												AND		K.SCID = C.SCID
												AND		K.STARTLOGO = 1
												AND		D.CPID = K.CPID
												GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
											) B
								WHERE		B.SDID = A.OID
								AND		B.CENTERNAME = A.CENTERNAME
								GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP
							)
				ORDER	BY	NDCNAME, NIP;
			END IF;
		ELSEIF	AI_OBJ_TYPE = 3	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						A.CENTERNAME,
						A.OID,
						AN_OBJ_ID AS POID,
						A.OBJ_NAME,
						(AI_OBJ_TYPE + 1),
						NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
						SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
						SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
						SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
						SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
						SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
						SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
						SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
						NULL,
						NULL,
						ROW_NUMBER()OVER()
			FROM		(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID AS OID,
										T.ICHKITEMNAME AS OBJ_NAME
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKITEM T
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		T.ICHKITEMID = C.CHKITEMID
						) A,
						(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID,
										D.CPSTATUS,
										COUNT(*) AS DAT_CNT
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKPOINT P,
										HD_CHECK_RESULT_DATA_LAST D
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		P.SCID = C.SCID
							AND		P.STARTLOGO = 1
							AND		D.CPID = P.CPID
							GROUP BY R.CENTERNAME, C.SCID, D.CPSTATUS
						) B
			WHERE		B.CENTERNAME = A.CENTERNAME
			AND		B.SCID = A.OID
			GROUP BY A.CENTERNAME, A.OID, A.OBJ_NAME
			ORDER	BY	A.OID;
		ELSEIF	AI_OBJ_TYPE = 4	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						NULL,
						D.RSDID,
						AN_OBJ_ID AS POID,
						D.CPTEXT,
						(AI_OBJ_TYPE + 1),
						1 AS AMOUNT,
						DECODE(D.CPSTATUS, 0, 1, 0) AS CL0,
						DECODE(D.CPSTATUS, 1, 1, 0) AS CL1,
						DECODE(D.CPSTATUS, 2, 1, 0) AS CL2,
						DECODE(D.CPSTATUS, 3, 1, 0) AS CL3,
						DECODE(D.CPSTATUS, 4, 1, 0) AS CL4,
						DECODE(D.CPSTATUS, 5, 1, 0) AS CL5,
						DECODE(D.CPSTATUS, -1, 1, 0) AS CHECKING,
						FUN_GET_TIMESTAMP(D.CPTIME, 8),
						D.CPSTATUS,
						ROW_NUMBER()OVER()
			FROM		IEAI_CHKPOINT P,
						HD_CHECK_RESULT_DATA_LAST D
			WHERE		P.SCID = AN_OBJ_ID
			AND		P.STARTLOGO = 1
			AND		D.CPID = P.CPID;
		END	IF;
		
		
		SELECT	COUNT(*)
		INTO		AI_REC_AMOUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID;
		
		
		SET	LI_PAGE_COUNT = INT(AI_REC_AMOUNT / AI_PAGE_SIZE);
		
		IF MOD(AI_REC_AMOUNT, AI_PAGE_SIZE) > 0 THEN
			SET	LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
		END	IF;
		
		IF	AI_PAGE_ID < 1 THEN
			SET	AI_PAGE_ID = 1;
		END	IF;
		
		IF	AI_PAGE_ID > LI_PAGE_COUNT	THEN
			SET	AI_PAGE_ID = LI_PAGE_COUNT;
		END	IF;
		
		SET	LI_PAGE_FROM = (AI_PAGE_ID - 1) * AI_PAGE_SIZE + 1;
		SET	LI_PAGE_TO = AI_PAGE_ID * AI_PAGE_SIZE;
		
		SELECT	SIGN(COUNT(*))
		INTO		V_COUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID
		AND		(ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		
		IF V_COUNT > 0 THEN
			DELETE	FROM	TMP_DATASHOW WHERE OPT_ID = AN_OPT_ID AND (ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		END IF;
	END
	$
	
	
-- 以由V8.5.0版本跟新修正	PROC_GET_RESULT_ALL_REPORT

		
			
		BEGIN
			DECLARE   LS_SQL VARCHAR(5000);
			DECLARE   LI_EXISTS  NUMERIC(2);
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_CONFIG_TEM';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_HC_CONFIG_TEM ( IID DECIMAL(19,0) NOT NULL,INAME VARCHAR(255) NOT NULL, IDESC VARCHAR(255),CONSTRAINT PK_IEAI_HC_CONFIG_TEM PRIMARY KEY(IID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHKPOINT_TEMP';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_CHKPOINT_TEMP (CPID DECIMAL(12, 0) NOT NULL, SCID DECIMAL(12, 0) NOT NULL, CHKPOINT VARCHAR(512),  INTLHOUR DECIMAL(12, 0),INTLMINUTE DECIMAL(12, 0), INTLLENGTH DECIMAL(12, 0), STARTLOGO SMALLINT,  CRTUSER VARCHAR(100),CRTTIME DECIMAL(19, 0), UPUSER VARCHAR(100), UPTIME DECIMAL(19, 0),SCRIPTTAG VARCHAR(8) DEFAULT '''', AUTOHC  SMALLINT DEFAULT 1, ALARMLEVEL VARCHAR(5) DEFAULT  0,  EMPRJUPID DECIMAL(12, 0), CONSTRAINT PK_IEAI_CHKPOINT_TEMP PRIMARY KEY(CPID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='HD_CHK_TIME_INTERVAL_CONF_TEMP';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE HD_CHK_TIME_INTERVAL_CONF_TEMP (CFGID DECIMAL(19,0) NOT NULL, DEVID DECIMAL(19,0) NOT NULL,  CIID DECIMAL(19,0) NOT NULL,  CPID DECIMAL(19,0) NOT NULL,  CYCLE_TYPE SMALLINT NOT NULL, START_DAY SMALLINT,   END_DAY SMALLINT,    START_TIME TIME NOT NULL,   END_TIME TIME NOT NULL,   SET_START INTEGER,  SET_END INTEGER,  ENABLED SMALLINT DEFAULT 1,  CONSTRAINT PK_CHK_TIME_INTERVAL_CFG_TEMP PRIMARY KEY(CFGID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
	
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COM_CHK_TEMP';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_COM_CHK_TEMP ( SCID DECIMAL(12, 0) NOT NULL, CPID DECIMAL(12, 0) NOT NULL, CHKITEMID DECIMAL(12, 0) NOT NULL, PERFORMUSER VARCHAR(100),  APPLOGO DECIMAL(12, 0) DEFAULT -1,  STARTLOGO SMALLINT DEFAULT 1,  CRTUSER VARCHAR(100),   CRTTIME DECIMAL(19, 0),  UPUSER VARCHAR(100),  UPTIME DECIMAL(19, 0),  IHIDDEN SMALLINT DEFAULT 0,  IFORMATIONID NUMERIC(19,0),  CONSTRAINT PK_IEAI_COM_CHK_TEMP PRIMARY KEY(SCID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
		END

		$
		
-- 4.7.20 version EM patch is as follows

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OTHERNAME' AND TABNAME='IEAI_ICALARMCONFIG';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_ICALARMCONFIG ADD COLUMN OTHERNAME VARCHAR ( 255 )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OVERTIME' AND TABNAME='IEAI_ICALARMCONFIG';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_ICALARMCONFIG ADD COLUMN OVERTIME VARCHAR ( 12 )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='MAILBOX' AND TABNAME='IEAI_ICALARMCONFIG';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_ICALARMCONFIG ADD COLUMN MAILBOX VARCHAR ( 255 )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


END
	 $
	 
	 CREATE OR REPLACE FUNCTION  FUN_GET_APPS_NAME   (AN_CHKPOINT_ID NUMERIC(19,0), AV_SYSNAME VARCHAR(256)) RETURNS VARCHAR(256)
	LANGUAGE SQL
	NOT DETERMINISTIC
	READS SQL DATA
	STATIC DISPATCH
	CALLED ON NULL INPUT
	EXTERNAL ACTION
	INHERIT SPECIAL REGISTERS
	BEGIN
		DECLARE	LV_APPS		VARCHAR(256);
		
		IF AN_CHKPOINT_ID <= 0 THEN
			SET LV_APPS = AV_SYSNAME;
		ELSE
			SET	LV_APPS = '';
			

	----FOR VCU AS SELECT	DISTINCT D.SYSNAME FROM IEAI_CHKPOINT A, IEAI_COM_CHK B, IEAI_SYS_RELATION C, IEAI_SYS D WHERE A.CPID = AN_CHKPOINT_ID AND B.SCID = A.SCID AND C.COMPUTERID = B.CPID AND D.SYSTEMID = DECODE(B.APPLOGO, -1, C.SYSTEMID, B.APPLOGO)
	FOR VCU AS SELECT	DISTINCT D.INAME FROM IEAI_CHKPOINT A, IEAI_COM_CHK B, IEAI_SYS_RELATION C, IEAI_PROJECT D WHERE A.CPID = AN_CHKPOINT_ID AND B.SCID = A.SCID AND C.COMPUTERID = B.CPID AND C.PRJTYPE=7 AND D.IID = DECODE(B.APPLOGO, -1, C.SYSTEMID, B.APPLOGO)
			DO
				IF LV_APPS = '' THEN
					SET	LV_APPS = VCU.INAME;
				ELSE
					SET	LV_APPS = LV_APPS CONCAT ',' CONCAT VCU.INAME;
				END	IF;
			END	FOR;
		END IF;

		RETURN	LV_APPS;
	END
	$
	-- TRB_IU_HC_ACTWARNING to v8.0
	
	
-- 4.7.21 version EM patch is as follows

	
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYS_HCTEMP';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_SYS_HCTEMP(IID DECIMAL(12) NOT NULL ,SYSTEMID DECIMAL(12) NOT NULL ,TEMID DECIMAL(19) NOT NULL ,SYSNAME VARCHAR(200) ,OPUSER DECIMAL(19) ,OPUSERNAME VARCHAR(255) ,OPTIME DECIMAL(19) ,EXT VARCHAR(200) ,EXT2 VARCHAR(100) ,CONSTRAINT PK_IEAI_SYS_TEMPID PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	END
$

-- 4.7.22 version EM patch is as follows
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='REMARKS' AND TABNAME='IEAI_CHKITEM'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKITEM ADD COLUMN REMARKS VARCHAR(500)';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

	END
	$


-- 以由V8.5.0版本更新修正PROC_GET_RESULT_REPORT_GRID
	

	CREATE OR REPLACE PROCEDURE PROC_GET_RESULT_REPORT_CHART  (IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_COLUMN_ID INTEGER, IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
	LANGUAGE SQL
	BEGIN
		DECLARE	LV_SQL	VARCHAR(4000);
		DECLARE	CONS_SEP VARCHAR(6);
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);
		


		
		
		SELECT	CPID
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST
		WHERE		IP = AV_IP;
		
		
		SELECT	IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;
		
		SET	CONS_SEP = '!!##!!';
		
		SET	LV_SQL = '';
		
		IF	AV_TIME_START < 0 AND AV_TIME_END < 0 THEN
			SELECT	COUNT(*)
			INTO		RECORDCOUNT
			FROM		(
							SELECT	A.CPTIME,
										LISTAGG(A.CPTEXT, CONS_SEP) WITHIN GROUP(ORDER BY 1) CPTEXT
							FROM		(
											SELECT	A.CPTIME,
														A.CPTEXT,
														B.I1
											FROM		(
															SELECT	CPTIME,
																		CPTEXT,
																		RSDID
															FROM		(
																			SELECT	A.CPTIME,
																						A.CPTEXT,
																						A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND   A.RSDID=BB.RSDID
																			AND   A.CPID=BB.CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																		) AA
														) A
														LEFT JOIN HD_CHECK_STATUS_CACHE B
														ON A.RSDID = B.RSDID
										) A
							GROUP BY A.CPTIME
						);
			
			SET	LV_SQL = 'INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT CPTIME, CPTEXT, VV FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT ROW_NUMBER() OVER(ORDER BY A.CPTIME) AS RECID, A.CPTIME, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.CPTEXT, ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) CPTEXT, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT ' , ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) VV ';
			SET	LV_SQL = LV_SQL CONCAT 'FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT '    SELECT A.CPTIME, A.CPTEXT, B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' FROM (  ';
			SET	LV_SQL = LV_SQL CONCAT '        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '          (A.ISYSID = 'CONCAT CHAR(LN_SYSID) CONCAT' OR A.ISYSID = -1) AND A.MEID = ' CONCAT CHAR(LN_CPID) CONCAT ' AND A.CIID =' CONCAT CHAR(AV_CHECK_ITEM_ID) CONCAT' ';
			SET	LV_SQL = LV_SQL CONCAT '    ) A LEFT JOIN HD_CHECK_STATUS_CACHE B ON A.RSDID = B.RSDID WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '        B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' IS NOT NULL  ';
			SET	LV_SQL = LV_SQL CONCAT ') A GROUP BY A.CPTIME ';
			SET	LV_SQL = LV_SQL CONCAT ') WHERE RECID > ' CONCAT CHAR(AV_START) CONCAT ' AND RECID <= ' CONCAT CHAR(AV_END);
		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
			SELECT	COUNT(*)
			INTO		RECORDCOUNT
			FROM		(
							SELECT	A.CPTIME,
										LISTAGG(A.CPTEXT, CONS_SEP) WITHIN GROUP(ORDER BY 1) CPTEXT
							FROM		(
											SELECT	A.CPTIME,
														A.CPTEXT,
														B.I1
											FROM		(
															SELECT	CPTIME,
																		CPTEXT,
																		RSDID
															FROM		(
																			SELECT	A.CPTIME,
																						A.CPTEXT,
																						A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND   A.RSDID=BB.RSDID
																			AND   A.CPID=BB.CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																			AND		A.CPTIME <= AV_TIME_END
																		) AA
														) A
														LEFT JOIN HD_CHECK_STATUS_CACHE B
														ON A.RSDID = B.RSDID
										) A
							GROUP BY A.CPTIME
						);
						
			SET	LV_SQL = 'INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT CPTIME, CPTEXT, VV FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT ROW_NUMBER() OVER(ORDER BY A.CPTIME) AS RECID, A.CPTIME, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.CPTEXT, ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) CPTEXT, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT ' , ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) VV ';
			SET	LV_SQL = LV_SQL CONCAT 'FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT '    SELECT A.CPTIME, A.CPTEXT, B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' FROM (  ';
			SET	LV_SQL = LV_SQL CONCAT '        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '          (A.ISYSID = 'CONCAT CHAR(LN_SYSID) CONCAT' OR A.ISYSID = -1) AND A.MEID = ' CONCAT CHAR(LN_CPID) CONCAT ' AND A.CIID =' CONCAT CHAR(AV_CHECK_ITEM_ID) CONCAT ' AND A.CPTIME <= ' CONCAT CHAR(AV_TIME_END);
			SET	LV_SQL = LV_SQL CONCAT '    ) A LEFT JOIN HD_CHECK_STATUS_CACHE B ON A.RSDID = B.RSDID WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '        B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' IS NOT NULL  ';
			SET	LV_SQL = LV_SQL CONCAT ') A GROUP BY A.CPTIME ';
			SET	LV_SQL = LV_SQL CONCAT ') WHERE RECID > ' CONCAT CHAR(AV_START) CONCAT ' AND RECID <= ' CONCAT CHAR(AV_END);
			
		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
			SELECT	COUNT(*)
			INTO		RECORDCOUNT
			FROM		(
							SELECT	A.CPTIME,
										LISTAGG(A.CPTEXT, CONS_SEP) WITHIN GROUP(ORDER BY 1) CPTEXT
							FROM		(
											SELECT	A.CPTIME,
														A.CPTEXT,
														B.I1
											FROM		(
															SELECT	CPTIME,
																		CPTEXT,
																		RSDID
															FROM		(
																			SELECT	A.CPTIME,
																						A.CPTEXT,
																						A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																			AND   A.RSDID=BB.RSDID
																			AND   A.CPID=BB.CPID
																			AND		A.CPTIME >= AV_TIME_START
																		) AA
														) A
														LEFT JOIN HD_CHECK_STATUS_CACHE B
														ON A.RSDID = B.RSDID
										) A
							GROUP BY A.CPTIME
						);
			
			SET	LV_SQL = 'INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT CPTIME, CPTEXT, VV FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT ROW_NUMBER() OVER(ORDER BY A.CPTIME) AS RECID, A.CPTIME, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.CPTEXT, ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) CPTEXT, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT ' , ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) VV ';
			SET	LV_SQL = LV_SQL CONCAT 'FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT '    SELECT A.CPTIME, A.CPTEXT, B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' FROM (  ';
			SET	LV_SQL = LV_SQL CONCAT '        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '          (A.ISYSID = 'CONCAT CHAR(LN_SYSID) CONCAT' OR A.ISYSID = -1) AND A.MEID = ' CONCAT CHAR(LN_CPID) CONCAT ' AND A.CIID =' CONCAT CHAR(AV_CHECK_ITEM_ID) CONCAT ' AND A.CPTIME >= ' CONCAT CHAR(AV_TIME_START);
			SET	LV_SQL = LV_SQL CONCAT '    ) A LEFT JOIN HD_CHECK_STATUS_CACHE B ON A.RSDID = B.RSDID WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '        B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' IS NOT NULL  ';
			SET	LV_SQL = LV_SQL CONCAT ') A GROUP BY A.CPTIME ';
			SET	LV_SQL = LV_SQL CONCAT ') WHERE RECID > ' CONCAT CHAR(AV_START) CONCAT ' AND RECID <= ' CONCAT CHAR(AV_END);
		ELSE
			SELECT	COUNT(*)
			INTO		RECORDCOUNT
			FROM		(
							SELECT	A.CPTIME,
										LISTAGG(A.CPTEXT, CONS_SEP) WITHIN GROUP(ORDER BY 1) CPTEXT
							FROM		(
											SELECT	A.CPTIME,
														A.CPTEXT,
														B.I1
											FROM		(
															SELECT	CPTIME,
																		CPTEXT,
																		RSDID
															FROM		(
																			SELECT	A.CPTIME,
																						A.CPTEXT,
																						A.RSDID
																			FROM		HD_CHECK_RESULT_DATA_CACHE A,HD_CHECK_STATUS_CACHE BB
																			WHERE		(A.ISYSID = LN_SYSID OR A.ISYSID = -1)
																			AND		A.MEID = LN_CPID
																			AND		A.CIID = AV_CHECK_ITEM_ID
																			AND   A.RSDID=BB.RSDID
																			AND   A.CPID=BB.CPID
																			AND		(A.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
																		) AA
														) A
														LEFT JOIN HD_CHECK_STATUS_CACHE B
														ON A.RSDID = B.RSDID
										) A
							GROUP BY A.CPTIME
						);
			
			SET	LV_SQL = 'INSERT INTO TMP_RESULT_REPORT_CHART (CPTIME, CPTEXT, VV) ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT CPTIME, CPTEXT, VV FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT 'SELECT ROW_NUMBER() OVER(ORDER BY A.CPTIME) AS RECID, A.CPTIME, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.CPTEXT, ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) CPTEXT, ';
			SET	LV_SQL = LV_SQL CONCAT '  LISTAGG(A.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT ' , ''' || CONS_SEP || ''') WITHIN GROUP(ORDER BY 1) VV ';
			SET	LV_SQL = LV_SQL CONCAT 'FROM ( ';
			SET	LV_SQL = LV_SQL CONCAT '    SELECT A.CPTIME, A.CPTEXT, B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' FROM (  ';
			SET	LV_SQL = LV_SQL CONCAT '        SELECT A.CPTIME, A.CPTEXT, A.RSDID  FROM HD_CHECK_RESULT_DATA_CACHE A WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '          (A.ISYSID = 'CONCAT CHAR(LN_SYSID) CONCAT' OR A.ISYSID = -1) AND A.MEID = ' CONCAT CHAR(LN_CPID) CONCAT ' AND A.CIID =' CONCAT CHAR(AV_CHECK_ITEM_ID) CONCAT ' AND A.CPTIME >= ' CONCAT CHAR(AV_TIME_START) CONCAT ' AND A.CPTIME <= ' CONCAT CHAR(AV_TIME_END);
			SET	LV_SQL = LV_SQL CONCAT '    ) A LEFT JOIN HD_CHECK_STATUS_CACHE B ON A.RSDID = B.RSDID WHERE ';
			SET	LV_SQL = LV_SQL CONCAT '        B.I' CONCAT CHAR(AV_COLUMN_ID) CONCAT' IS NOT NULL  ';
			SET	LV_SQL = LV_SQL CONCAT ') A GROUP BY A.CPTIME ';
			SET	LV_SQL = LV_SQL CONCAT ') WHERE RECID > ' CONCAT CHAR(AV_START) CONCAT ' AND RECID <= ' CONCAT CHAR(AV_END);
		END	IF;
		
		PREPARE	SQLA FROM LV_SQL;
		EXECUTE SQLA;
	END
	$

	CREATE OR REPLACE PROCEDURE PROC_GET_RESULT_CPTEXT  (IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0) ,IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0))
		LANGUAGE SQL
		BEGIN
		
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);
			
		SELECT	CPID
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST
		WHERE		IP = AV_IP;
	
		SELECT	IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;
	
		IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN
	    INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT)
			SELECT  DISTINCT a.CPTEXT
			FROM
				HD_CHECK_RESULT_DATA_CACHE a
			WHERE
				(a.ISYSID = LN_SYSID OR
				a.ISYSID=-1 ) AND
				a.MEID = LN_CPID AND
				a.CIID = AV_CHECK_ITEM_ID;
	
		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
				INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT)
				SELECT DISTINCT a.CPTEXT
				FROM
					HD_CHECK_RESULT_DATA_CACHE a 
				WHERE
					(a.ISYSID = LN_SYSID OR
					a.ISYSID=-1 ) AND
					a.MEID = LN_CPID AND
					a.CIID = AV_CHECK_ITEM_ID
					and a.CPTIME <= AV_TIME_END;
		 
		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
			INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT) 
			SELECT DISTINCT a.CPTEXT 
			FROM HD_CHECK_RESULT_DATA_CACHE a 
			WHERE
			(a.ISYSID = LN_SYSID OR
			a.ISYSID=-1 ) AND
			a.MEID = LN_CPID AND
			a.CIID = AV_CHECK_ITEM_ID 
			and a.CPTIME >= AV_TIME_START;
		ELSE
			INSERT INTO  TMP_RESULT_CPTEXT (CPTEXT) 
			SELECT DISTINCT a.CPTEXT
			FROM
			  HD_CHECK_RESULT_DATA_CACHE a
			WHERE
				(a.ISYSID = LN_SYSID OR
				a.ISYSID=-1 ) AND
				a.MEID = LN_CPID AND
				a.CIID = AV_CHECK_ITEM_ID
				and  (a.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END);
		END	IF;
		END
		$
		
-- 4.7.23 version EM patch is as follows
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EQUIPMENTTYPE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_EQUIPMENTTYPE(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) NOT NULL ,IDESCRIPTION VARCHAR(255) ,ILASTMODIFYTIME DECIMAL(19) ,ISTATE VARCHAR(1) ,CONSTRAINT PK_IEAI_EQUIPMENTTYPE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_MANUFACTORY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_MANUFACTORY(IID DECIMAL(19) NOT NULL ,IOBJECTID VARCHAR(255) ,INAME VARCHAR(255) ,IDESCRIPTION VARCHAR(255) ,ILASTMODIFYTIME DECIMAL(19) ,ISTATE VARCHAR(1) ,CONSTRAINT PK_IEAI_MANUFACTORY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;




		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EQUIPMENTMODEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_EQUIPMENTMODEL(IID DECIMAL(19) NOT NULL ,IOBJECTID VARCHAR(255) ,ITYPEID DECIMAL(19) ,IFACTORYID DECIMAL(19) ,INAME VARCHAR(255) ,IDESCRIPTION VARCHAR(255) ,ILASTMODIFYTIME DECIMAL(19) ,ISTATE VARCHAR(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_EQUIPMENTMODEL PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		-- 设备表添加两个字段
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EQTYPE' AND TABNAME='IEAI_COMPUTER_LIST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN EQTYPE DECIMAL(2) DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EQID' AND TABNAME='IEAI_COMPUTER_LIST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN EQID DECIMAL(19) DEFAULT -1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EQUIPMENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_EQUIPMENT(IID DECIMAL(19) NOT NULL ,ICPID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,IMANUFACTORY_ID DECIMAL(19) ,IEQUTYPE_ID DECIMAL(19) ,IEQUMODELNUMBER_ID DECIMAL(19) ,ICHASSIS_SERIAL_NUMBER VARCHAR(255) ,ISLOTNUM VARCHAR(255) ,ISLOT_SERIAL_NUMBER VARCHAR(255) ,IIPADDR VARCHAR(255) ,ISOFT_VERSION VARCHAR(255) ,IBANKID DECIMAL(19) ,ICHILDBANKID DECIMAL(19) ,IAREAID DECIMAL(19) ,IEQU_USE VARCHAR(255) ,IRISK_LEVEL DECIMAL(1) ,IBUILDING_NAME VARCHAR(255) ,IEQU_ROOM VARCHAR(255) ,IEQU_BOX VARCHAR(255) ,ISTART_UNIT VARCHAR(255) ,IADMIN_A VARCHAR(255) ,IADMIN_B VARCHAR(255) ,IPROTLCOL VARCHAR(255) ,ISNMP_VERSION VARCHAR(255) ,ICOMMUNITY_NAME VARCHAR(255) ,ILOWPERMIT_USER VARCHAR(255) ,ILOWPERMIT_ACSKEY VARCHAR(255) ,ILOWPERMIT_ENABLE VARCHAR(255) ,IHIGHPERMIT_USER VARCHAR(255) ,IHIGHPERMIT_ACSKEY VARCHAR(255) ,IHIGHPERMIT_ENABLE VARCHAR(255) ,IIFSYS_LOWPERMIT_PWD DECIMAL(1) ,ISYSOBJECTID VARCHAR(255) ,IONLINE_STATE DECIMAL(1) ,IBUY_TIME DECIMAL(19) ,IINSURANCE_TIME DECIMAL(19) ,IISINSURANCE DECIMAL(1) ,IHARDWARE_TYPE VARCHAR(255) ,IBACK_EQUIP VARCHAR(255) ,ISTOREROOM VARCHAR(255) ,IEQUSTATE DECIMAL(1) ,ILASTMODIFYTIME DECIMAL(19) ,ITSMID VARCHAR(255) ,ISACCESSEQU DECIMAL(19) DEFAULT 0 ,IHIGHPERMIT_USER_ALIAS VARCHAR(255) ,ILOWPERMIT_USER_ALIAS VARCHAR(255) ,CONSTRAINT PK_IEAI_EQUIPMENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHKPOINT_CONFIG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHKPOINT_CONFIG(IID DECIMAL(19) NOT NULL ,CHKITEMID DECIMAL(19) ,ILEVELID DECIMAL(12) ,POINTNAME VARCHAR(255) ,THRESHOLD VARCHAR(255) ,WARNLEVEL VARCHAR(255) ,DEFAULTWARNLEVEL VARCHAR(255) ,COMPARERULE VARCHAR(100) ,POINTTYPE DECIMAL(10) ,INTLHOUR DECIMAL(12) ,INTLMINUTE DECIMAL(12) ,INTLLENGTH DECIMAL(12) ,ICRON VARCHAR(255) ,CRTUSER VARCHAR(100) ,CRTTIME DECIMAL(19) ,UPUSER VARCHAR(100) ,UPTIME DECIMAL(19) ,CONSTRAINT PK_IEAI_CHKPOINT_CONFIG PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECK_CMD';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECK_CMD(IID DECIMAL(19) NOT NULL,CHKPOINTID DECIMAL(19) ,CMDNAME VARCHAR(255) ,EQUIPVENDOR DECIMAL(12) ,EQUIPTYPE DECIMAL(12) ,CMDINFO CLOB ,MAINTENTIME DECIMAL(19) ,CMDMETHOD DECIMAL(10) ,ISTATE DECIMAL(10) ,CONSTRAINT PK_IEAI_CHECK_CMD PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECK_CMD_EQUVERSION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECK_CMD_EQUVERSION(IID DECIMAL(19) NOT NULL ,CHKCMDID DECIMAL(19) ,EQUVERSIONID DECIMAL(19) ,CONSTRAINT PK_IEAI_CHECK_CMD_EQUVERSION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECK_SCRIPT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECK_SCRIPT(IID DECIMAL(19) NOT NULL ,CHKCMDID DECIMAL(19) ,SCRIPTNAME VARCHAR(255) ,SCRIPTTYPE VARCHAR(100) ,SCRIPTINFO CLOB ,STATUS DECIMAL(10),SCRIPTFLAG DECIMAL(2),SCRIPTSERVER VARCHAR(19),CONSTRAINT PK_IEAI_CHECK_SCRIPT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GROUPTYPE' AND TABNAME='IEAI_COMPUTER_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_GROUP ADD COLUMN GROUPTYPE DECIMAL(19) DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EQTYPE' AND TABNAME='IEAI_COMPUTER_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_GROUP ADD COLUMN EQTYPE DECIMAL(10) DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;



		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='HCSTATUS' AND TABNAME='IEAI_COMPUTER_GROUP_MAPPING';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_GROUP_MAPPING ADD COLUMN HCSTATUS SMALLINT DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHCAGENTID' AND TABNAME='IEAI_COMPUTER_GROUP_MAPPING';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_GROUP_MAPPING ADD COLUMN IHCAGENTID DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHCSTATUS' AND TABNAME='IEAI_COMPUTER_GROUP_MAPPING';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_GROUP_MAPPING ADD COLUMN IHCSTATUS SMALLINT DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;



		-- IEAI_COM_CHK增加字段IGROUPIID,IPROJECTID 开始
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IGROUPIID' AND TABNAME='IEAI_COM_CHK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COM_CHK ADD COLUMN IGROUPIID DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPROJECTID' AND TABNAME='IEAI_COM_CHK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COM_CHK ADD COLUMN IPROJECTID DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHCSTATUS' AND TABNAME='IEAI_COM_CHK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COM_CHK ADD COLUMN IHCSTATUS DECIMAL(10) DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHCAGENTID' AND TABNAME='IEAI_COM_CHK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COM_CHK ADD COLUMN IHCAGENTID DECIMAL(19) DEFAULT -1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		-- IEAI_COM_CHK增加字段IGROUPIID,IPROJECTID 结束


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COM_CHK_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_COM_CHK_GROUP(SCID DECIMAL(12) NOT NULL ,CPID DECIMAL(12) NOT NULL ,CHKITEMID DECIMAL(12) NOT NULL ,PERFORMUSER VARCHAR(100) ,APPLOGO DECIMAL(12) DEFAULT -1 ,STARTLOGO SMALLINT DEFAULT 1 ,CRTUSER VARCHAR(100) ,CRTTIME DECIMAL(19) ,UPUSER VARCHAR(100) ,UPTIME DECIMAL(19) ,IHIDDEN SMALLINT DEFAULT 0 ,IGROUPID DECIMAL(19) ,IGROUPIID DECIMAL(19) ,CONSTRAINT PK_IEAI_COM_CHK_GROUP PRIMARY KEY (SCID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		-- IEAI_CHKPOINT表增加字段开始
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICHECKPOINTID' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ICHECKPOINTID DECIMAL(19) DEFAULT -1';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICRON' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ICRON VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IGROUPIID' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN IGROUPIID DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPOINTTYPE' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN IPOINTTYPE DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICORN' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ICORN VARCHAR(600) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICHKCMDID' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ICHKCMDID DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPROJECT' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN IPROJECT DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISHELLIID' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ISHELLIID DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICOMPARERULE' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ICOMPARERULE VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDEFAULTWARNLEVEL' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN IDEFAULTWARNLEVEL VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWARNLEVEL' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN IWARNLEVEL VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITHRESHOLD' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ITHRESHOLD VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TIMESETFLAG' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN TIMESETFLAG SMALLINT  DEFAULT -1';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


	 

		-- IEAI_CHKITEM 表增加字段是否自愈
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='HEALINGSIGN' AND TABNAME='IEAI_CHKITEM';
				IF LI_EXISTS = 0 THEN
					SET LS_SQL ='ALTER TABLE IEAI_CHKITEM ADD COLUMN HEALINGSIGN DECIMAL(19) DEFAULT 0';
					PREPARE	SQLA FROM LS_SQL;
					EXECUTE SQLA;
				END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHKPOINT_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHKPOINT_GROUP(CPID DECIMAL(12) NOT NULL ,SCID DECIMAL(12) NOT NULL ,CHKPOINT VARCHAR(512) ,INTLHOUR DECIMAL(12) ,INTLMINUTE DECIMAL(12) ,INTLLENGTH DECIMAL(12) ,STARTLOGO SMALLINT ,CRTUSER VARCHAR(100) ,CRTTIME DECIMAL(19) ,UPUSER VARCHAR(100) ,UPTIME DECIMAL(19) ,ICOMPARERULE VARCHAR(255) ,IDEFAULTWARNLEVEL VARCHAR(255) ,IWARNLEVEL VARCHAR(255) ,ITHRESHOLD VARCHAR(255) ,ISHELLIID DECIMAL(19) ,ICHECKPOINTID DECIMAL(19) ,IPOINTTYPE DECIMAL(19) ,ICORN VARCHAR(255) ,ICHKCMDID DECIMAL(19) ,CONSTRAINT PK_IEAI_CHKPOINT_GROUP PRIMARY KEY (CPID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTG_BIND_CPG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AGENTG_BIND_CPG(IID DECIMAL(19) NOT NULL ,IAGENTGROUPID DECIMAL(19) ,ICPGROUPID DECIMAL(19) ,CONSTRAINT PK_IEAI_AGENTG_BIND_CPG PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;



		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COM_CHK_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_COM_CHK_SYS(SCID DECIMAL(12) NOT NULL ,CPID DECIMAL(12) NOT NULL ,CHKITEMID DECIMAL(12) NOT NULL ,PERFORMUSER VARCHAR(100) ,APPLOGO DECIMAL(12) DEFAULT -1 ,STARTLOGO INTEGER DEFAULT 1 ,CRTUSER VARCHAR(100) ,CRTTIME DECIMAL(19) ,UPUSER VARCHAR(100) ,UPTIME DECIMAL(19) ,IHIDDEN INTEGER DEFAULT 0 ,ISYSID DECIMAL(19) ,CONSTRAINT PK_IEAI_COM_CHK_SYS PRIMARY KEY (SCID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHKPOINT_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHKPOINT_SYS(CPID DECIMAL(12) NOT NULL ,SCID DECIMAL(12) NOT NULL ,CHKPOINT VARCHAR(512) ,INTLHOUR DECIMAL(12) ,INTLMINUTE DECIMAL(12) ,INTLLENGTH DECIMAL(12) ,STARTLOGO INTEGER ,CRTUSER VARCHAR(100) ,CRTTIME DECIMAL(19) ,UPUSER VARCHAR(100) ,UPTIME DECIMAL(19) ,ICOMPARERULE VARCHAR(255) ,IDEFAULTWARNLEVEL VARCHAR(255) ,IWARNLEVEL VARCHAR(255) ,ITHRESHOLD VARCHAR(255) ,ISHELLIID DECIMAL(19) ,ICHECKPOINTID DECIMAL(19) ,IPOINTTYPE DECIMAL(19) ,ICORN VARCHAR(255) ,ICHKCMDID DECIMAL(19) ,CONSTRAINT PK_IEAI_CHKPOINT_SYS PRIMARY KEY (CPID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EQUIPCHAR';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_EQUIPCHAR(IID DECIMAL(19) NOT NULL ,VENDOR DECIMAL(19) NOT NULL ,EQUIPTYPE DECIMAL(19) NOT NULL ,SUCCESSCHAR VARCHAR(4000) ,IGNORECHAR VARCHAR(4000) ,SPECIALCHAR VARCHAR(4000) ,ENDCHAR VARCHAR(4000) ,NOCONFIGCHAR VARCHAR(4000) ,NOCINFIGCHAR VARCHAR(4000) ,ERRORCHAR VARCHAR(4000) ,CONSTRAINT PK_IEAI_EQUIPCHAR PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;



		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_WEBPORT' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_WEBPORT DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FORTKEY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FORTKEY(IID DECIMAL(19) NOT NULL ,IALIAS VARCHAR(255) ,IIP VARCHAR(255) ,IUSER VARCHAR(255) ,IDES VARCHAR(255) ,CONSTRAINT PK_IEAI_FORTKEY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
 
	END
	$


	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_EQUIPMENTTYPE where iid =-1;
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='INSERT INTO IEAI_EQUIPMENTTYPE(IID,INAME,IDESCRIPTION,ILASTMODIFYTIME,ISTATE)values(-1,''服务器'',''默认描述'',1574662575836,1)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	END
	$

	CREATE OR REPLACE PROCEDURE PROC_CHECK_RESULT_DATA_LAST   (IN AV_IP VARCHAR(25),
	IN AV_CPORT NUMERIC(12,0))
		LANGUAGE SQL
	BEGIN
		DECLARE	LI_NOW			INTEGER;--
		DECLARE	LTM_NOW			TIME;--
		DECLARE	LD_TODAY			DATE;--
		DECLARE	LI_DayOfWeek	SMALLINT;--
		DECLARE	LI_DAY			SMALLINT;--
		DECLARE	LN_KEY			NUMERIC(19,0);--
		DECLARE	LI_ROWCNT		INTEGER;--
		
		SET	LTM_NOW = CURRENT TIME;--
		SET	LI_NOW = HOUR(LTM_NOW) * 60 + MINUTE(LTM_NOW);--
		
		SET	LD_TODAY = CURRENT DATE;--
		
		-- Mon 71, Tue 72, Wed 73, Thu 74, Fri 75, Sta 76, Sun 77
		SET	LI_DayOfWeek = DayOfWeek_ISO(LD_TODAY) + 70;--
		
		-- get this day
		SET	LI_DAY = DAY(LD_TODAY);--
		
		-- get the check point amount of the AV_IP
		SELECT	COUNT(C.CPID)
		INTO		LI_ROWCNT
		FROM		IEAI_COMPUTER_LIST A, 
					IEAI_COM_CHK B LEFT JOIN IEAI_SYS F ON F.SYSTEMID = B.APPLOGO AND F.SYSTEMID > 0,
					IEAI_CHKPOINT C, 
					HD_AGENT_CHECK_TIME D, 
					IEAI_CHKITEM E
		WHERE		A.IP = AV_IP
		AND		B.CPID = A.CPID
		AND		B.STARTLOGO = 1
		AND		C.SCID = B.SCID
		AND		C.STARTLOGO = 1
		AND             C.ICHECKPOINTID=-1
		AND		D.MEID = A.CPID
		AND		B.CHKITEMID = E.ICHKITEMID
		AND		MOD(D.INTERVAL_MINUTE, C.INTLLENGTH) = 0
		AND		(
						NOT EXISTS	(
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
										) OR
						EXISTS		(	-- Everyday
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 4
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	-- Every Month
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 8
											AND		(LI_DAY BETWEEN NVL(B1.START_DAY, 1) AND FUN_GET_END_DAY (LD_TODAY, B1.END_DAY))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	-- Every Week
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 16
											AND		(LI_DayOfWeek BETWEEN NVL(B1.START_DAY, 0) AND NVL(B1.END_DAY, 0))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										)
					);--
		
		-- 
		IF	LI_ROWCNT = 0 THEN
			RETURN;--
		END IF;--
		
		-- get primary key
		CALL	PROC_GET_MULT_ID ('HD_CHECK_RESULT_DATA_LAST', LI_ROWCNT, LN_KEY);--
		
		-- get primary key failure
		IF LN_KEY IS NULL THEN
			RETURN;--
		END IF;--
		
		-- get check points of the device (AV_IP)
		INSERT	INTO	TEMP_INI_RESLUT
				(
					IHIDDEN,
					COMCHID,
					ISYSID,
					SYSNAME,
					CHKITEMID,
					ICHKITEMNAME,
					IPARSERULE,
					MEID,
					IP,
					CPPORT,
					CPID,
					CHKPOINT,
					INIITEM,
					INICHKPOINT,
					RSDID,
					ITEM_TAG,
					POINT_TAG
				)
		SELECT	B.IHIDDEN,
					B.SCID AS COMCHID,
					B.APPLOGO AS ISYSID,
					F.SYSNAME,
					B.CHKITEMID,
					E.ICHKITEMNAME,
					E.IPARSERULE,
					A.CPID AS MEID,
					A.IP,
					A.CPPORT,
					C.CPID, 
					C.CHKPOINT,    
					'CHECKITEM=' CONCAT E.ISHELLNAME CONCAT '|' CONCAT E.IVERSION CONCAT '|' CONCAT NVL(B.PERFORMUSER, '') CONCAT '|' CONCAT B.IHIDDEN AS INIITEM,
					C.CHKPOINT AS INICHKPOINT,
					ROW_NUMBER()OVER() + LN_KEY - 1,
					E.SCRIPTTAG,
					C.SCRIPTTAG
		FROM		IEAI_COMPUTER_LIST A, 
					IEAI_COM_CHK B LEFT JOIN IEAI_SYS F ON F.SYSTEMID = B.APPLOGO AND F.SYSTEMID > 0,
					IEAI_CHKPOINT C, 
					HD_AGENT_CHECK_TIME D, 
					IEAI_CHKITEM E
		WHERE		A.IP = AV_IP
		AND		B.CPID = A.CPID
		AND		B.STARTLOGO = 1
		AND		C.SCID = B.SCID
		AND		C.STARTLOGO = 1
		AND             C.ICHECKPOINTID=-1
		AND		D.MEID = A.CPID
		AND		B.CHKITEMID = E.ICHKITEMID
		AND		MOD(D.INTERVAL_MINUTE, C.INTLLENGTH) = 0
		AND		(
						NOT EXISTS	(
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
										) OR
						EXISTS		(	-- Everyday
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 4
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	-- Every Month
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 8
											AND		(LI_DAY BETWEEN NVL(B1.START_DAY, 1) AND FUN_GET_END_DAY (LD_TODAY, B1.END_DAY))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	-- Every Week
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 16
											AND		(LI_DayOfWeek BETWEEN NVL(B1.START_DAY, 0) AND NVL(B1.END_DAY, 0))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										)
					)
		ORDER	BY	B.IHIDDEN DESC;--
		
		-- delete status (last) data
		DELETE	FROM	HD_CHECK_STATUS_LAST
		WHERE		EXISTS	(
									SELECT	1
									FROM		TEMP_INI_RESLUT B5
									WHERE		B5.IP = AV_IP
									AND		B5.CPID = HD_CHECK_STATUS_LAST.CPID
								);--
		
		-- delete result (last) data
		DELETE	FROM	HD_CHECK_RESULT_DATA_LAST
		WHERE		IP = AV_IP
		AND		EXISTS	(
									SELECT	1
									FROM		TEMP_INI_RESLUT B1
									WHERE		B1.IP = AV_IP
									AND		B1.CPID = HD_CHECK_RESULT_DATA_LAST.CPID
								);--
			
		-- delete all data that the check item does not exist for the device (AV_IP)
		UPDATE	HD_CHECK_RESULT_DATA_LAST
		SET		CISTATUS = 99
		WHERE		IP = AV_IP
		AND		NOT EXISTS
						(
							SELECT	1
							FROM		IEAI_COM_CHK B2,
										IEAI_CHKPOINT B3
							WHERE		B2.CPID = HD_CHECK_RESULT_DATA_LAST.MEID
							AND		B3.SCID = B2.SCID
							AND		B3.CPID = HD_CHECK_RESULT_DATA_LAST.CPID
						);--
		
		DELETE	FROM	HD_CHECK_STATUS_LAST
		WHERE		EXISTS
						(
							SELECT	1
							FROM		HD_CHECK_RESULT_DATA_LAST B1
							WHERE		B1.IP = AV_IP
							AND		B1.CISTATUS = 99
							AND		B1.CPID = HD_CHECK_STATUS_LAST.CPID
						);--
		
		DELETE	FROM	HD_CHECK_RESULT_DATA_LAST
		WHERE		IP = AV_IP
		AND		CISTATUS = 99;--
		
		-- set CISTATUS = 0 of HD_CHECK_RESULT_DATA_LAST of the device (AV_IP)
		UPDATE	HD_CHECK_RESULT_DATA_LAST
		SET		CISTATUS = 0
		WHERE		IP = AV_IP;--
		
		INSERT	INTO	HD_CHECK_RESULT_DATA_LAST
				(
					RSDID,
					ISYSID,
					ISYSNAME,
					CIID,
					CINAME,
					CIPARSRULE,
					CISTATUS,
					MEID,
					IP,
					PORT,
					CPID,
					CPTEXT,
					CPTIME,
					COMCHKID,
					CPSTATUS,
					CPTIMESTAMP
				)
		SELECT	RSDID,
					ISYSID,
					SYSNAME,
					CHKITEMID,
					ICHKITEMNAME,
					IPARSERULE,
					1,
					MEID,
					IP,
					CPPORT,
					CPID,
					CHKPOINT,
					0,
					COMCHID,
					-1,
					CURRENT TIMESTAMP
		FROM		TEMP_INI_RESLUT
		WHERE		IP = AV_IP;--
	END
	$
	
-- 4.7.24 version EM patch is as follows
		BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SUGGESTION' AND TABNAME='IEAI_HC_ACTWARNING'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_HC_ACTWARNING ADD COLUMN SUGGESTION	VARCHAR(200)';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DISPOSETYPE' AND TABNAME='IEAI_HC_ACTWARNING'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_HC_ACTWARNING ADD COLUMN DISPOSETYPE DECIMAL(4) DEFAULT 0 ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DISPOSETYPE' AND TABNAME='IEAI_HC_ACTWARNING_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_HC_ACTWARNING_HIS ADD COLUMN DISPOSETYPE DECIMAL(4) DEFAULT 0 ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENTTYPE' AND  COLNAME='INAME'  AND TYPENAME='VARCHAR' AND LENGTH<70;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENTTYPE ALTER COLUMN INAME SET DATA TYPE VARCHAR(70)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENTTYPE' AND  COLNAME='IDESCRIPTION'  AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENTTYPE ALTER COLUMN IDESCRIPTION SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_MANUFACTORY' AND  COLNAME='INAME'   AND TYPENAME='VARCHAR' AND LENGTH<70;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_MANUFACTORY ALTER COLUMN INAME SET DATA TYPE VARCHAR(70)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_MANUFACTORY' AND  COLNAME='IDESCRIPTION'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_MANUFACTORY ALTER COLUMN IDESCRIPTION SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENTMODEL' AND  COLNAME='INAME'   AND TYPENAME='VARCHAR' AND LENGTH<70;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENTMODEL ALTER COLUMN INAME SET DATA TYPE VARCHAR(70)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENTMODEL' AND  COLNAME='IDESCRIPTION'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENTMODEL ALTER COLUMN IDESCRIPTION SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='INAME'   AND TYPENAME='VARCHAR' AND LENGTH<63;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN INAME SET DATA TYPE VARCHAR(63)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='ICHASSIS_SERIAL_NUMBER'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN ICHASSIS_SERIAL_NUMBER SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IIPADDR'   AND TYPENAME='VARCHAR' AND LENGTH<15;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IIPADDR SET DATA TYPE VARCHAR(15)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='ISOFT_VERSION'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN ISOFT_VERSION SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IBUILDING_NAME'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IBUILDING_NAME SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IEQU_ROOM'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IEQU_ROOM SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IEQU_BOX'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IEQU_BOX SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IPROTLCOL'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IPROTLCOL SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='ILOWPERMIT_USER'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN ILOWPERMIT_USER SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IHIGHPERMIT_USER'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IHIGHPERMIT_USER SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='IHIGHPERMIT_USER_ALIAS'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN IHIGHPERMIT_USER_ALIAS SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPMENT' AND  COLNAME='ILOWPERMIT_USER_ALIAS'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPMENT ALTER COLUMN ILOWPERMIT_USER_ALIAS SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='SUCCESSCHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN SUCCESSCHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='IGNORECHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN IGNORECHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='SPECIALCHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN SPECIALCHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='ENDCHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN ENDCHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='NOCONFIGCHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN NOCONFIGCHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='NOCINFIGCHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN NOCINFIGCHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EQUIPCHAR' AND  COLNAME='ERRORCHAR'   AND TYPENAME='VARCHAR' AND LENGTH<3000;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EQUIPCHAR ALTER COLUMN ERRORCHAR SET DATA TYPE VARCHAR(3000)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_FORTKEY' AND  COLNAME='IALIAS'   AND TYPENAME='VARCHAR' AND LENGTH<70;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_FORTKEY ALTER COLUMN IALIAS SET DATA TYPE VARCHAR(70)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_FORTKEY' AND  COLNAME='IIP'   AND TYPENAME='VARCHAR' AND LENGTH<15;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_FORTKEY ALTER COLUMN IIP SET DATA TYPE VARCHAR(15)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_FORTKEY' AND  COLNAME='IUSER'   AND TYPENAME='VARCHAR' AND LENGTH<70;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_FORTKEY ALTER COLUMN IUSER SET DATA TYPE VARCHAR(70)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_FORTKEY' AND  COLNAME='IDES'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_FORTKEY ALTER COLUMN IDES SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_CONFIG' AND  COLNAME='POINTNAME'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_CONFIG ALTER COLUMN POINTNAME SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_CONFIG' AND  COLNAME='THRESHOLD'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_CONFIG ALTER COLUMN THRESHOLD SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_CONFIG' AND  COLNAME='WARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_CONFIG ALTER COLUMN WARNLEVEL SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_CONFIG' AND  COLNAME='DEFAULTWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<10;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_CONFIG ALTER COLUMN DEFAULTWARNLEVEL SET DATA TYPE VARCHAR(10)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_CONFIG' AND  COLNAME='COMPARERULE'   AND TYPENAME='VARCHAR' AND LENGTH<3;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_CONFIG ALTER COLUMN COMPARERULE SET DATA TYPE VARCHAR(3)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_CONFIG' AND  COLNAME='ICRON'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_CONFIG ALTER COLUMN ICRON SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_CMD' AND  COLNAME='CMDNAME'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHECK_CMD ALTER COLUMN CMDNAME SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_CMD' AND  COLNAME='EQUIPVENDOR';
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHECK_CMD ALTER COLUMN EQUIPVENDOR SET DATA TYPE DECIMAL(19)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_CMD' AND  COLNAME='EQUIPTYPE';
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHECK_CMD ALTER COLUMN EQUIPTYPE SET DATA TYPE DECIMAL(19)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_SCRIPT' AND  COLNAME='SCRIPTNAME'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHECK_SCRIPT ALTER COLUMN SCRIPTNAME SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_GROUP' AND  COLNAME='CHKPOINT'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_GROUP ALTER COLUMN CHKPOINT SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_GROUP' AND  COLNAME='ICORN'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_GROUP ALTER COLUMN ICORN SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_GROUP' AND  COLNAME='ICOMPARERULE'   AND TYPENAME='VARCHAR' AND LENGTH<3;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_GROUP ALTER COLUMN ICOMPARERULE SET DATA TYPE VARCHAR(3)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_GROUP' AND  COLNAME='IDEFAULTWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<10;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_GROUP ALTER COLUMN IDEFAULTWARNLEVEL SET DATA TYPE VARCHAR(10)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_GROUP' AND  COLNAME='IWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_GROUP ALTER COLUMN IWARNLEVEL SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_GROUP' AND  COLNAME='ITHRESHOLD'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_GROUP ALTER COLUMN ITHRESHOLD SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_SYS' AND  COLNAME='CHKPOINT'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_SYS ALTER COLUMN CHKPOINT SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_SYS' AND  COLNAME='ICORN'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_SYS ALTER COLUMN ICORN SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_SYS' AND  COLNAME='ICOMPARERULE'   AND TYPENAME='VARCHAR' AND LENGTH<3;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_SYS ALTER COLUMN ICOMPARERULE SET DATA TYPE VARCHAR(3)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_SYS' AND  COLNAME='IDEFAULTWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<10;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_SYS ALTER COLUMN IDEFAULTWARNLEVEL SET DATA TYPE VARCHAR(10)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_SYS' AND  COLNAME='IWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_SYS ALTER COLUMN IWARNLEVEL SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT_SYS' AND  COLNAME='ITHRESHOLD'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT_SYS ALTER COLUMN ITHRESHOLD SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='ICORN'   AND TYPENAME='VARCHAR' AND LENGTH<150;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ALTER COLUMN ICORN SET DATA TYPE VARCHAR(150)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='ICOMPARERULE'   AND TYPENAME='VARCHAR' AND LENGTH<3;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ALTER COLUMN ICOMPARERULE SET DATA TYPE VARCHAR(3)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='IDEFAULTWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<10;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ALTER COLUMN IDEFAULTWARNLEVEL SET DATA TYPE VARCHAR(10)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='IWARNLEVEL'   AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ALTER COLUMN IWARNLEVEL SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='ITHRESHOLD'  AND TYPENAME='VARCHAR' AND LENGTH<100;
		IF	LI_EXISTS = 1 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ALTER COLUMN ITHRESHOLD SET DATA TYPE VARCHAR(100)';
			PREPARE SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_ACTWARNING';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_HC_ACTWARNING '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_ACTWARNING_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_HC_ACTWARNING_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
		
	END
		 $
		 
	BEGIN
	  DECLARE   LS_SQL VARCHAR(4000);
	  DECLARE   LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_HD_BUSINESSMONITOR';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL = 'DROP TABLE TMP_HD_BUSINESSMONITOR';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
	END
	   $

	BEGIN
	  DECLARE   LS_SQL VARCHAR(4000);
	  DECLARE   LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_HD_BUSINESSMONITOR';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_HD_BUSINESSMONITOR  ( OPT_ID  	DECIMAL(19,0) NOT NULL,ISYSID  	DECIMAL(19,0),SYSNAME 	VARCHAR(255),CPSTATUS	DECIMAL(19,0),ALARMLEVEL DECIMAL(4)) ON COMMIT DELETE ROWS ';
	    PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
	END
	   $
	   
	CREATE OR REPLACE PROCEDURE PROC_HD_BUSINESSMONITOR (IN AN_USERID NUMERIC(19, 0), OUT AN_OPT_ID NUMERIC(19, 0))
	LANGUAGE SQL
	BEGIN
		DECLARE	ISPERM_COUNT	NUMERIC(19);
		DECLARE	LN_OPT_ID		NUMERIC(19,0);	
		DECLARE	ISPERM_ALL		NUMERIC(19);
		
		CALL	PROC_GET_NEXT_ID ('TMP_HD_BUSINESSMONITOR', LN_OPT_ID);
		SET	AN_OPT_ID = LN_OPT_ID;
		
		SELECT	COUNT(P.IID)
		INTO		ISPERM_ALL
		FROM		IEAI_SYS_PERMISSION P,
					IEAI_USERINHERIT U
	--	WHERE		P.ISYSID = -5
		WHERE		P.IPROID = -7
		AND		P.IPERMISSION = 1
		AND		U.IUSERID = AN_USERID
		AND		P.IROLEID = U.IROLEID;
		
	--	IF AN_USERID = 3 OR ISPERM_ALL > 0 THEN
		IF ISPERM_ALL > 0 THEN
			
			INSERT INTO TMP_HD_BUSINESSMONITOR
					(
						OPT_ID,
						ISYSID,
						SYSNAME,
						CPSTATUS,
						ALARMLEVEL
					)
			SELECT	LN_OPT_ID,
						D.ISYSID,
						D.SYSNAME,
						MIN(
								CASE
									WHEN D.CPSTATUS IN (-5, 1, 2, 3, 4, 5) THEN 1 
									WHEN (DECODE(D.CPSTATUS, -1, 1)) > 0 THEN 2 
									WHEN (DECODE(D.CPSTATUS, -6, 1)) > 0 THEN 3
	--                                WHEN D.CISTATUS IN (-5, 1, 2, 3, 4, 5) THEN 1 
	--								WHEN (DECODE(D.CISTATUS, -1, 1)) > 0 THEN 2 
	--								WHEN (DECODE(D.CISTATUS, -6, 1)) > 0 THEN 3
									ELSE 4
								END
							) AS CPSTATUS,
							MAX(D.CPSTATUS) AS ALARMLEVEL
			FROM		VE_SERVICE_DATA D
			GROUP BY	D.ISYSID, D.SYSNAME
			ORDER BY	D.ISYSID;
		ELSE
			INSERT INTO TMP_HD_BUSINESSMONITOR
					(
						OPT_ID,
						ISYSID,
						SYSNAME,
						CPSTATUS,
						ALARMLEVEL
					)
			SELECT	LN_OPT_ID,
						D.ISYSID,
						D.SYSNAME,
						MIN(
								CASE
									WHEN D.CPSTATUS IN (-5, 1, 2, 3, 4, 5) THEN 1 
									WHEN (DECODE(D.CPSTATUS, -1, 1)) > 0 THEN 2 
									WHEN (DECODE(D.CPSTATUS, -6, 1)) > 0 THEN 3
	--								WHEN D.CISTATUS IN (-5, 1, 2, 3, 4, 5) THEN 1
	--								WHEN (DECODE(D.CISTATUS, -1, 1)) > 0 THEN 2
	--								WHEN (DECODE(D.CISTATUS, -6, 1)) > 0 THEN 3
									ELSE 4
								END
							) AS CPSTATUS,
							MAX(D.CPSTATUS) AS ALARMLEVEL
			FROM		VE_SERVICE_DATA D,
						IEAI_USERINHERIT U,
						IEAI_SYS_PERMISSION P
			WHERE		U.IUSERID = AN_USERID
			AND		P.IROLEID = U.IROLEID
			AND		P.IPERMISSION = 1
	--		AND		D.ISYSID = P.ISYSID--IPROID
			AND		D.ISYSID = P.IPROID
			GROUP BY	D.ISYSID, D.SYSNAME
			ORDER BY	D.ISYSID;
		END IF;
	END
	$
	
	
	BEGIN
	  DECLARE   LS_SQL VARCHAR(4000);
	  DECLARE   LI_EXISTS  NUMERIC(2);
	  
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='HD_CHECK_TIME_RANGE_CONFIG';
	  IF LI_EXISTS = 0 THEN
		  SET LS_SQL ='CREATE TABLE HD_CHECK_TIME_RANGE_CONFIG(CFGID DECIMAL(19) NOT NULL ,DEVID DECIMAL(19) ,CIID DECIMAL(19) ,ICPID DECIMAL(19) ,CYCLE_TYPE INTEGER ,START_DAY INTEGER ,END_DAY INTEGER ,START_TIME TIME NOT NULL ,END_TIME TIME NOT NULL ,SET_START INTEGER ,SET_END INTEGER ,ENABLEDFLAG SMALLINT DEFAULT 1 ,CONSTRAINT PK_HD_CHECK_TIME_RANGE_CONFIG PRIMARY KEY (CFGID))';
		  PREPARE	SQLA FROM LS_SQL;
		  EXECUTE SQLA;
	  END IF;
	  
	END
	$
	
-- 4.7.25 version EM patch is as follows	
	
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_VOLUME_PERMISSIONS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_VOLUME_PERMISSIONS(IID DECIMAL(19) NOT NULL,IROLEID DECIMAL(19),IFILESTORENAME VARCHAR(255),IPERMISSION SMALLINT, CONSTRAINT PK_IEAI_VOLUME_PERMISSIONS  PRIMARY KEY(IID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILE_STORE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILE_STORE(FILE BLOB(1048576) LOGGED NOT COMPACT,FILENAME VARCHAR(255), IID DECIMAL(19,0) GENERATED  ALWAYS AS IDENTITY (START WITH 1, INCREMENT BY 1,CACHE 20,MINVALUE 1,MAXVALUE 9999999999999999999,NO CYCLE,NO ORDER) NOT NULL, INSERTTIME VARCHAR(255), TYPE VARCHAR(255),CONSTRAINT PK_IEAI_FILE_STORE PRIMARY KEY(IID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;
		
	END
	$


	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_FILE_STORE  P WHERE P.TYPE='所有交易量';  
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO ieai_file_store (TYPE,INSERTTIME) VALUES (''所有交易量'','''')';
				PREPARE SQLA FROM LS_SQL; 
				EXECUTE SQLA;
		END	IF;
	END
	$

	CREATE OR REPLACE PROCEDURE PROC_CREATE_CHECK_SNAPSHOT  (IN AN_USER_ID NUMERIC(19,0))
		AUTONOMOUS
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_SNAP_ID	NUMERIC(19,0);
			DECLARE	LI_ROWCNT	INTEGER;
			DECLARE	SQLCODE		INTEGER	DEFAULT	0;
			DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
			
			DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
			SET	RETSQLCODE = SQLCODE;
			
			CALL	PROC_GET_NEXT_ID ('IEAI_CHECK_SNAPSHOT', LN_SNAP_ID);
			
			INSERT INTO IEAI_CHECK_SNAPSHOT
						(
							SNAP_ID,
							SNAP_DATE,
							USER_ID,
							USER_NAME
						)
			SELECT	LN_SNAP_ID,
						CURRENT TIMESTAMP,
						IID,
						IFULLNAME
			FROM		IEAI_USER
			WHERE		IID = AN_USER_ID;
			
			IF	RETSQLCODE = 0 THEN
				
				SELECT	COUNT(*)
				INTO		LI_ROWCNT
				FROM		IEAI_USERINHERIT H,
							IEAI_SYS_PERMISSION P
				WHERE		H.IUSERID = AN_USER_ID
				AND		P.IROLEID = H.IROLEID
				AND		P.IPROID = -7;
			
			IF	LI_ROWCNT > 0 THEN
				INSERT INTO IEAI_CHECK_SNAPSHOT_DEVICES
							(
								SNAP_ID,
								DEV_ID,
								IP,
								DEV_STATUS
							)
				SELECT	DISTINCT
							LN_SNAP_ID,
							D.CPID,
							D.IP,
							DECODE(I.ISTATUS, 0, 1, 0)
				FROM		

							IEAI_PROJECT S,
							IEAI_SYS_RELATION R,
							IEAI_COMPUTER_LIST D LEFT JOIN IEAI_WORKFLOWINSTANCE I ON I.IFLOWID = D.FLOWID
				WHERE		S.IID >= 0
				AND		S.PROTYPE = 7
				AND		R.SYSTEMID = S.IID
				AND		D.CPID = R.COMPUTERID;
			ELSE
				INSERT INTO IEAI_CHECK_SNAPSHOT_DEVICES
							(
								SNAP_ID,
								DEV_ID,
								IP,
								DEV_STATUS
							)
				SELECT	DISTINCT
							LN_SNAP_ID,
							D.CPID,
							D.IP,
							DECODE(I.ISTATUS, 0, 1, 0)
				FROM		IEAI_USERINHERIT H,
							IEAI_SYS_PERMISSION P,
							IEAI_SYS_RELATION R,
							IEAI_COMPUTER_LIST D LEFT JOIN IEAI_WORKFLOWINSTANCE I ON I.IFLOWID = D.FLOWID
				WHERE		H.IUSERID = AN_USER_ID
				AND		P.IROLEID = H.IROLEID
				AND		P.IPROID >= 0
				AND     R.PRJTYPE = 7 
				AND		R.SYSTEMID = P.IPROID
				AND		D.CPID = R.COMPUTERID;
			END IF;
			
			IF RETSQLCODE = 0 OR RETSQLCODE = 100 THEN
				FOR CS AS SELECT A.SNAP_ID FROM (SELECT SNAP_ID, ROW_NUMBER()OVER() AS R_NO FROM (SELECT SNAP_ID FROM IEAI_CHECK_SNAPSHOT WHERE USER_ID = AN_USER_ID ORDER BY SNAP_ID DESC)) A WHERE A.R_NO > 5
				DO
					DELETE FROM IEAI_CHECK_SNAPSHOT_DEVICES WHERE SNAP_ID = CS.SNAP_ID;
					DELETE FROM IEAI_CHECK_SNAPSHOT WHERE SNAP_ID = CS.SNAP_ID;
				END FOR;
				
				IF RETSQLCODE = 0 OR RETSQLCODE = 100 THEN
					COMMIT WORK;
				ELSE
					ROLLBACK WORK;
				END IF;
			ELSE
				ROLLBACK WORK;
			END IF;
		ELSE
			ROLLBACK WORK;
		END IF;
	END
	$

-- 4.7.26 version HC patch is as follows	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SOURCETYPE' AND TABNAME='IEAI_HC_CONFIG_TEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_HC_CONFIG_TEM ADD COLUMN SOURCETYPE DECIMAL(2) DEFAULT 1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SOURCETYPE' AND TABNAME='IEAI_COM_CHK_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_COM_CHK_SYS ADD COLUMN SOURCETYPE DECIMAL(2) DEFAULT 1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SOURCETYPE' AND TABNAME='HD_CHK_TIME_INTERVAL_CONF_TEMP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE HD_CHK_TIME_INTERVAL_CONF_TEMP ADD COLUMN SOURCETYPE DECIMAL(2) DEFAULT 1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECKHC_AGENT_H2_VERSION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECKHC_AGENT_H2_VERSION(IID DECIMAL(19) NOT NULL ,PATCHVERSION VARCHAR (50) NOT NULL ,IDESC VARCHAR (150) ,CUSER VARCHAR(20) ,INSERTTIME DECIMAL(19) ,UPUSER VARCHAR(20) ,UPTIME DECIMAL(19) ,PATCHFILE BLOB ,PATCHFILENAME VARCHAR(50) ,ENABLEDTYPE DECIMAL(1) ,CONSTRAINT PK_IEAI_CHECKHC_AGENT_H2_V PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECKHC_AGENT_H2_CURRENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECKHC_AGENT_H2_CURRENT(IID DECIMAL(19) NOT NULL ,AGENTINFOID DECIMAL(19) NOT NULL ,PATCHIID DECIMAL(19) NOT NULL ,PATCHVERSION VARCHAR(50) ,UPGRADEDESC VARCHAR(150) ,PREPATCHVERSIONID DECIMAL(19) DEFAULT -100 ,PREPATCHVERSION VARCHAR(50) ,INITIALFLAG DECIMAL(1) DEFAULT 0 ,UPGRADETIME DECIMAL(19) ,UPGRADEUSER VARCHAR(20) ,CONSTRAINT PK_IEAI_CHECKHC_AGENT_H2_C PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_H2_UPGRADE_RECORD';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AGENT_H2_UPGRADE_RECORD(IID DECIMAL(19) NOT NULL ,AGENTINFOID DECIMAL(19) NOT NULL ,PATCHIID DECIMAL(19) NOT NULL ,PATCHVERSION VARCHAR(50) ,IDESC VARCHAR (150) ,UPGRADEDESC VARCHAR(150) ,UPGRADUSER VARCHAR(20) ,UPGRADETIME DECIMAL(19) ,UPGRADERESULT DECIMAL(2) ,UPGRADERESULTMESSAGE VARCHAR(2000) ,CONSTRAINT PK_IEAI_CHECKHC_AGENT_H2_R PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DYNPAR' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN DYNPAR VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DYNPAR' AND TABNAME='IEAI_CHKPOINT_CONFIG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_CONFIG ADD COLUMN DYNPAR VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DYNPAR' AND TABNAME='IEAI_CHKPOINT_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_SYS ADD COLUMN DYNPAR VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

	
		
	END
	$
	
-- 4.7.26 version HC patch is as follows
-- PROC_SET_CHK_DATA_LAST_STATUS move to v8.6.0 version change
	
	
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_TEMP_TAG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_TEMP_TAG(IID DECIMAL(19) NOT NULL ,ITEMPIID DECIMAL(19) NOT NULL ,ITAG VARCHAR(100) NOT NULL ,SOURCETYPE DECIMAL(2) DEFAULT 1 ,CONSTRAINT PK_IEAI_HC_TEMP_TAG PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='MODELFLAG' AND TABNAME='IEAI_CHECK_SCRIPT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHECK_SCRIPT ADD COLUMN MODELFLAG DECIMAL(1) DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		
	END
	$
	
	-- 4.7.28 version HC patch is as follows	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HCTASK_PERMISSIONS';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='CREATE TABLE IEAI_HCTASK_PERMISSIONS(IPERMISSION INTEGER ,TASKID DECIMAL(19) ,IROLEID DECIMAL(19) ,IID DECIMAL(19) NOT NULL ,IBUSNES_SYS DECIMAL(19),CONSTRAINT PK_IEAI_HCTASK_PERMISSIONS PRIMARY KEY (IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_INSPECT_TASK';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='CREATE TABLE IEAI_HC_INSPECT_TASK(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,TASKTYPE DECIMAL(1) DEFAULT 0 ,ICREATETIME DECIMAL(19) ,ICREATEUSER DECIMAL(19) NOT NULL ,CONSTRAINT PK_HC_INSPECT_TASK PRIMARY KEY(IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TASKID' AND TABNAME='IEAI_COM_CHK';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_COM_CHK ADD COLUMN TASKID DECIMAL(19) DEFAULT -1';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_COMPUTER_LIST' AND INDNAME='IDX_IEAI_COMPUTER_LIST_FLOWID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_IEAI_COMPUTER_LIST_FLOWID ON IEAI_COMPUTER_LIST(FLOWID)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		
	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COM_CHK';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_COM_CHK'')';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
	END
	$
	
	-- 4.7.29 version HC patch is as follows	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTTAG' AND TABNAME='IEAI_CHKPOINT_CONFIG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_CONFIG ADD COLUMN SCRIPTTAG VARCHAR(8) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTPARAM' AND TABNAME='IEAI_CHKPOINT_CONFIG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_CONFIG ADD COLUMN SCRIPTPARAM VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTPARAM' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN SCRIPTPARAM VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DYNPAR' AND TABNAME='IEAI_CHKPOINT_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_GROUP ADD COLUMN DYNPAR VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTTAG' AND TABNAME='IEAI_CHKPOINT_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_GROUP ADD COLUMN SCRIPTTAG VARCHAR(8) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTPARAM' AND TABNAME='IEAI_CHKPOINT_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_GROUP ADD COLUMN SCRIPTPARAM VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTTAG' AND TABNAME='IEAI_CHKPOINT_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_SYS ADD COLUMN SCRIPTTAG VARCHAR(8) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTPARAM' AND TABNAME='IEAI_CHKPOINT_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_SYS ADD COLUMN SCRIPTPARAM VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTARTSTATUS' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN ISTARTSTATUS DECIMAL(10) DEFAULT 0 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHCAGENTID' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN IHCAGENTID DECIMAL(19) DEFAULT -1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


	END
	$
	CREATE OR REPLACE PROCEDURE PROC_EXPORT_CHECK_DATA   (IN AN_CHKPOINT_ID NUMERIC(19,0), IN ADT_START VARCHAR(30), IN ADT_END VARCHAR(30), IN AI_PAGE_ID SMALLINT, IN AI_PAGE_SIZE INTEGER, IN AI_DAT_TYPE SMALLINT, OUT AI_AMOUNT INTEGER)
	LANGUAGE SQL
	BEGIN
		
		DECLARE	LI_CNT		INTEGER;
		DECLARE	LI_LLIMIT	INTEGER;
		DECLARE	LI_ULIMIT	INTEGER;
		
		
		INSERT INTO TMP_EXP_CHK_DATA_SWAP
			(
				CHK_OBJECT,
				CHK_DATE,
				THREADHOLD,
				CHK_DATA,
				ALARM_CODE,
				NFLAG
			)
		SELECT	C.I5,
					TIMESTAMP(C.I2),
					C.I4,
					decode( LENGTH(TRIM(TRANSLATE(TRIM(C.I3),' ','-0123456789' ))),0,TRIM(C.I3),'0') as I3,
					CAST(C.I1 AS INTEGER),
					0
		FROM		HD_CHECK_STATUS_CACHE C
		WHERE		C.CPID = AN_CHKPOINT_ID
		AND		(C.I2 BETWEEN ADT_START AND ADT_END)
		UNION
		SELECT	H.I5,
					TIMESTAMP(H.I2),
					H.I4,
					decode( LENGTH(TRIM(TRANSLATE(H.I3,' ','-0123456789' ))),0,H.I3,'0') as I3,
					CAST(H.I1 AS INTEGER),
					0
		FROM		HD_CHECK_STATUS_HIS H
		WHERE		H.CPID = AN_CHKPOINT_ID
		AND		(H.I2 BETWEEN ADT_START AND ADT_END);
		
		
		GET DIAGNOSTICS LI_CNT = ROW_COUNT;
		
		IF LI_CNT = 0 THEN
			RETURN;
		END IF;
		
		
		IF AI_PAGE_SIZE IS NULL THEN
			SET	AI_PAGE_SIZE = 50;
		END IF;
		
		IF AI_PAGE_SIZE > 65530 THEN
			SET	AI_PAGE_SIZE = 65530;
		END IF;
		
		
		IF AI_PAGE_ID IS NULL THEN
			SET	AI_PAGE_ID = 1;
		END IF;
		
		IF LI_CNT < (((AI_PAGE_ID - 1) * AI_PAGE_SIZE) + 1) THEN
			SET	AI_PAGE_ID = TRUNCATE(LI_CNT / AI_PAGE_SIZE, 0);
			IF MOD(LI_CNT, AI_PAGE_SIZE) > 0 THEN
				SET	AI_PAGE_ID = AI_PAGE_ID + 1;
			END IF;
		END IF;
		
		
		SET	LI_LLIMIT = ((AI_PAGE_ID - 1) * AI_PAGE_SIZE) + 1;
		SET	LI_ULIMIT = AI_PAGE_ID * AI_PAGE_SIZE;
		
		IF AI_DAT_TYPE = 0 THEN
			
			INSERT INTO TMP_EXPORT_CHECK_DATA
					(
						CHK_OBJECT,
						CHK_DATE,
						LOWER_LIMIT,
						UPPER_LIMIT,
						CHK_DATA,
						DATA_AVG,
						DATA_MIN,
						DATA_MAX,
						DATA_TYPE
					)
			SELECT	A.CHK_OBJECT,
						A.CHK_DATE,
						A.LOWER_LIMIT,
						A.UPPER_LIMIT,
						A.CHK_DATA,
						A.DATA_AVG,
						A.DATA_MIN,
						A.DATA_MAX,
						A.DATA_TYPE
			FROM		(
							SELECT	CHK_OBJECT,
										CHK_DATE,
										FUN_GET_LIMIT(THREADHOLD, TRUE) AS LOWER_LIMIT,
										FUN_GET_LIMIT(THREADHOLD, FALSE) AS UPPER_LIMIT,
										CHK_DATA,
										'' AS DATA_AVG,
										'' AS DATA_MIN,
										'' AS DATA_MAX,
										0 AS DATA_TYPE,
										ROW_NUMBER()OVER() AS RID
							FROM		TMP_EXP_CHK_DATA_SWAP
							ORDER	BY	CHK_DATE
						) A
			WHERE		A.RID BETWEEN LI_LLIMIT AND LI_ULIMIT;
			
			SET	AI_AMOUNT = LI_CNT;
		ELSE
			
			
			
			UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (POSSTR(CHK_DATA, ' ') > 1 OR POSSTR(CHK_DATA, '-') > 1 OR POSSTR(CHK_DATA, '+') > 1 OR LENGTH(TRIM(TRANSLATE(CHK_DATA, '', '.+-'))) = 0);
			UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (LENGTH(CHK_DATA) - LENGTH(REPLACE(CHK_DATA, '-', ''))) > 1;
			UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (LENGTH(CHK_DATA) - LENGTH(REPLACE(CHK_DATA, '+', ''))) > 1;
			UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (LENGTH(CHK_DATA) - LENGTH(REPLACE(CHK_DATA, '.', ''))) > 1;
			UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND LENGTH(TRIM(TRANSLATE(CHK_DATA, '', '0123456789+-.'))) > 0;
			
			SELECT	SIGN(SUM(NFLAG))
			INTO		LI_CNT
			FROM		TMP_EXP_CHK_DATA_SWAP;
			
			IF LI_CNT > 0 THEN
				
				SET	AI_AMOUNT = -1;
				RETURN;
			END IF;
			
			
			INSERT INTO TMP_EXPORT_CHECK_DATA
					(
						CHK_OBJECT,
						CHK_DATE,
						LOWER_LIMIT,
						UPPER_LIMIT,
						CHK_DATA,
						DATA_AVG,
						DATA_MIN,
						DATA_MAX,
						DATA_TYPE
					)
			SELECT	CHK_OBJECT,
						CHK_DATE,
						LOWER_LIMIT,
						UPPER_LIMIT,
						CHK_DATA,
						DATA_AVG,
						DATA_MIN,
						DATA_MAX,
						DATA_TYPE
			FROM	(
						SELECT	A.CHK_OBJECT,
									A.CHK_DATE,
									A.LOWER_LIMIT,
									A.UPPER_LIMIT,
									'' AS CHK_DATA,
									AVG(A.CHK_DATA) AS DATA_AVG,
									MIN(A.CHK_DATA) AS DATA_MIN,
									MAX(A.CHK_DATA) AS DATA_MAX,
									1 AS DATA_TYPE,
									ROW_NUMBER()OVER() AS RID
						FROM		(
										SELECT	CHK_OBJECT,
													DATE(CHK_DATE) AS CHK_DATE,
													FUN_GET_LIMIT(THREADHOLD, TRUE) AS LOWER_LIMIT,
													FUN_GET_LIMIT(THREADHOLD, FALSE) AS UPPER_LIMIT,
													CHK_DATA
										FROM		TMP_EXP_CHK_DATA_SWAP
										WHERE		NFLAG = 0
									) A
						GROUP BY	A.CHK_OBJECT, A.CHK_DATE, A.LOWER_LIMIT, A.UPPER_LIMIT
						ORDER BY A.CHK_DATE
					)
			WHERE	RID <= AI_PAGE_SIZE;
			
			GET DIAGNOSTICS LI_CNT = ROW_COUNT;
			SET	AI_AMOUNT = LI_CNT;
		END IF;
	END
	$

	-- V8 version HC patch is as follows	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITYPE' AND TABNAME='IEAI_LEVEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_LEVEL ADD COLUMN ITYPE VARCHAR(100) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CHKPOINT' AND INDNAME='PK_IEAI_CHKPOINT_SCID_STATUS';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX PK_IEAI_CHKPOINT_SCID_STATUS ON IEAI_CHKPOINT(SCID,ISTARTSTATUS)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_RUNALARM_RECORD';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_RUNALARM_RECORD(IID DECIMAL(19) NOT NULL ,IFLOWID DECIMAL(19) ,IIP VARCHAR(15) ,IFLOWNAME VARCHAR(50) ,IACTNAME VARCHAR(50) ,BDATE DECIMAL(19) ,FREQUENCY DECIMAL(3) DEFAULT 0 ,LDATE DECIMAL(19) ,CONSTRAINT PK_HC_RUNALARM_RECORD_ID PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		
	END
	$
	
	CREATE OR REPLACE TRIGGER TRB_IU_HC_ACTWARNING
	NO CASCADE
	BEFORE INSERT OR UPDATE
	ON IEAI_HC_ACTWARNING
	REFERENCING NEW AS N
	FOR EACH ROW
	MODE DB2SQL
	BEGIN
		DECLARE	LV_MSG		VARCHAR(128);
		DECLARE	LV_PRGNAME	VARCHAR(50);
		DECLARE	LV_SUPPORT	VARCHAR(128);
		DECLARE	LI_WTYPE		INTEGER;
		
		-- get error message, program name and support information from error code table
		SELECT	NVL(MAX(A.MSG_CN), ''),
					NVL(MAX(A.SUPPORT), '')
		INTO		LV_MSG,
					LV_SUPPORT
		FROM		IEAI_HD_ERROR_CODE_TABLE A
		WHERE		A.ERRCODE = N.ERRCODE;
		
		IF N.PRGNAME IS NULL THEN
			SET	LV_PRGNAME = NULL;
			SET	LI_WTYPE = 20;
		ELSE
			SELECT 	MAX(T1.ICHKITEMNAME),
						NVL(MAX(ILEVELID), 20)
			INTO		LV_PRGNAME,
						LI_WTYPE
			FROM		IEAI_CHKITEM T1, IEAI_COM_CHK C,IEAI_CHKPOINT P
			WHERE		C.SCID=P.SCID 
			AND   C.CHKITEMID=T1.ICHKITEMID
			AND P.CPID=N.CPID;
			
		
		END IF;
		
		-- get warning type code
		SET N.WTYPE = LI_WTYPE;
		
		-- compose warning message
		IF	N.AMESSAGE IS NULL OR N.AMESSAGE = '' THEN
			SET N.WMESSAGE = LV_MSG CONCAT '。';
		ELSE
			SET N.WMESSAGE = LV_MSG CONCAT '(' CONCAT N.AMESSAGE CONCAT ')。';
		END IF;
		
		SET N.WMESSAGE = '自动化巡检系统报告：' CONCAT N.WMESSAGE CONCAT LV_SUPPORT CONCAT NVL('(' CONCAT LV_PRGNAME CONCAT ', 检测值:' CONCAT N.CHKVALUE CONCAT ',基线:' CONCAT N.THREADHOLD CONCAT ')', '');
	END 
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_AGREEMENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_AGREEMENT (IAGREEMENTID  DECIMAL(19) NOT NULL,IAGREEMENTNAME  VARCHAR(100),ICOMPUTERID   DECIMAL(19),IAGREEMENTTYPE  VARCHAR(20),IAGREEMENTIP  VARCHAR(100),IAGREEMENTDOMAIN VARCHAR(100),IAGREEMENTPORT  DECIMAL(5),IIPORDOMAN  DECIMAL(2),IUSERNAME  VARCHAR(100),IPASSWORD  VARCHAR(100),VERSION   VARCHAR(100),COMMUNITY   VARCHAR(100),CONTEXTNAME VARCHAR(100),SAFENAME VARCHAR(100),SAFELEVEL VARCHAR(100),VFTAGEEEMENT VARCHAR(100),VFTPASSWORD  VARCHAR(100),PRIVACYAGREEMENT  VARCHAR(100), PRIVATEKEY  VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT PRIMARY KEY (IAGREEMENTID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		
	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HD_ERROR_CODE_TABLE where ERRCODE =500;
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='INSERT INTO IEAI_HD_ERROR_CODE_TABLE (ERRCODE, MSG_CN, MSG_EN, SUPPORT) VALUES (500, ''解析异常'', NULL, ''需要AOMS技术支持人员协助。'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_AGREEMENT_CATEGORY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_AGREEMENT_CATEGORY (IID DECIMAL(19) NOT NULL,ICATEGORY VARCHAR(100),ITYPE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_CATEGORY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_AGREEMENT_TYPE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_AGREEMENT_TYPE(ICATEGORYID DECIMAL(19) NOT NULL,IAGREEMENTID DECIMAL(19),ICATEGORY DECIMAL(19),IVALUE VARCHAR(100),ITYPE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_TYPE PRIMARY KEY (ICATEGORYID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_AGREEMENT_DBTYYE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_AGREEMENT_DBTYYE(IID DECIMAL(19) NOT NULL,DBNAME VARCHAR(100),DBTYPE VARCHAR(100),ITYPE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_DBTYYE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HC_AGREEMENT_CONFIGURE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_HC_AGREEMENT_CONFIGURE(IID DECIMAL(19) NOT NULL,IVENDOR VARCHAR(100),IMODEL VARCHAR(100),ICATEGORY VARCHAR(100),ITYPE VARCHAR(100), IVALUE VARCHAR(100),CONSTRAINT PK_IEAI_HC_AGREEMENT_CONFIGURE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PRGNAME' AND TABNAME='IEAI_HC_ACTWARNING';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE IEAI_HC_ACTWARNING ALTER  COLUMN PRGNAME  set data type VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;  

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PRGNAME' AND TABNAME='IEAI_HC_ACTWARNING_HIS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE IEAI_HC_ACTWARNING_HIS ALTER  COLUMN PRGNAME  set data type VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;  
	END
	$
	
	
	-- v8.1.1
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PROTOCOL' AND TABNAME='IEAI_CHECK_SCRIPT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHECK_SCRIPT ADD COLUMN PROTOCOL VARCHAR(10) DEFAULT ''Entegor'' ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AGREEMENTCATEGORYID' AND TABNAME='IEAI_CHECK_SCRIPT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHECK_SCRIPT ADD COLUMN AGREEMENTCATEGORYID DECIMAL(19) DEFAULT -1 ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CMDDESC' AND TABNAME='IEAI_CHECK_CMD';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHECK_CMD ADD COLUMN CMDDESC VARCHAR(200) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CHKPOINTDESC' AND TABNAME='IEAI_CHKPOINT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT ADD COLUMN CHKPOINTDESC VARCHAR(2000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CHKPOINTDESC' AND TABNAME='IEAI_CHKPOINT_CONFIG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_CONFIG ADD COLUMN CHKPOINTDESC VARCHAR(200) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CHKPOINTDESC' AND TABNAME='IEAI_CHKPOINT_SYS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_SYS ADD COLUMN CHKPOINTDESC VARCHAR(200) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CHKPOINTDESC' AND TABNAME='IEAI_CHKPOINT_GROUP';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_CHKPOINT_GROUP ADD COLUMN CHKPOINTDESC VARCHAR(200) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECK_HANDWORK_PENGDING';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECK_HANDWORK_PENGDING(IID DECIMAL(19) NOT NULL ,SYSTEMID DECIMAL(19) ,COMPUTERID DECIMAL(19) ,CHKITEMID DECIMAL(19) ,CHKPOINTID DECIMAL(19) NOT NULL ,CHKPOINTNAME VARCHAR(150) ,CHKPOINTDESC VARCHAR(500) ,WDATE DECIMAL(19) ,LDATE DECIMAL(19) ,PUSHCOUNT DECIMAL(3) DEFAULT 1 ,CONSTRAINT PK_IEAI_HANDWORK_PENGDING PRIMARY KEY (IID) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CHECK_HANDWORK_HIS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_CHECK_HANDWORK_HIS(IID DECIMAL(19) NOT NULL ,SYSTEMID DECIMAL(19) ,SYSTEMNAME VARCHAR(300) ,COMPUTERID DECIMAL(19) ,COMPUTERIP VARCHAR(150) ,COMPUTERNAME VARCHAR(150) ,CHKITEMID DECIMAL(19) ,CHKITEMNAME VARCHAR(300) ,CHKPOINTID DECIMAL(19) NOT NULL ,CHKPOINTNAME VARCHAR(150) ,CHKPOINTDESC VARCHAR(500) ,CHKCMDID DECIMAL(19) ,CMDDESC VARCHAR(500) ,CMDNAME VARCHAR(200) ,WDATE DECIMAL(19) ,LDATE DECIMAL(19) ,HANDLEDATE DECIMAL(19) ,PUSHCOUNT DECIMAL(3) DEFAULT 1 ,HANDLEUSER VARCHAR(150) ,HANDLEMSG VARCHAR(500) ,EXCUTERESULT VARCHAR(50) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

	END
	$
	-- V8.2.0 version HC patch is as follows 
	CREATE or replace PROCEDURE PROC_HD_DATAOVERVIEW    (IN AN_USERID NUMERIC(19,0), IN AN_OBJ_ID NUMERIC(19,0), IN AI_OBJ_TYPE SMALLINT, IN AI_PAGE_ID INTEGER, IN AI_PAGE_SIZE INTEGER, IN AI_DATA_CENTER VARCHAR(255) , IN AN_STATUS NUMERIC(19,0), OUT AN_OPT_ID NUMERIC(19,0), OUT AI_REC_AMOUNT INTEGER)
	LANGUAGE SQL
	BEGIN
		DECLARE	CN_MENU_ID			SMALLINT DEFAULT 72;
		DECLARE	V_COUNT				INTEGER;
		DECLARE	LI_PAGE_FROM		INTEGER;
		DECLARE	LI_PAGE_TO			INTEGER;
		DECLARE	LI_PAGE_COUNT		INTEGER;
		
		
		IF	AI_OBJ_TYPE < 0 OR AI_OBJ_TYPE > 4	THEN
			RETURN;
		END	IF;
		
		CALL	PROC_GET_NEXT_ID ('TMP_DATASHOW', AN_OPT_ID);
		
		IF	AN_OPT_ID IS NULL	THEN
			RETURN;
		END	IF;
		
		
		IF AI_OBJ_TYPE BETWEEN 0 AND 1 THEN
			SELECT
				COUNT(P.IID) 
			INTO V_COUNT 
			FROM
				IEAI_USERINHERIT U,
				IEAI_SYS_PERMISSION S,
				IEAI_PROJECT P 
			WHERE
				U.IUSERID = AN_USERID 
				AND S.IROLEID = U.IROLEID
				AND S.IPERMISSION = 1 
				AND S.IPROID = -7
				AND P.IID = S.IPROID 
				AND (P.PROTYPE = -7 OR P.PROTYPE = 7)
				;
		END IF;
		
		
		
		
		IF	AI_OBJ_TYPE = 0	THEN
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S, IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.IID > 0
								AND S.IID=SS.IID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND S.IID=SS.IID
								AND		S.IID >= 0
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.APP_LEVEL,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											SS.SYSTYPE AS OID,
											L.APPLVL AS APP_LEVEL,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
							  AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, SS.SYSTYPE, L.APPLVL, N.NDCNAME
							) A,
							(
								SELECT	SS.SYSTYPE,
											R.CENTERNAME,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
					
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY SS.SYSTYPE, R.CENTERNAME, D.CPSTATUS
							) B
				WHERE		B.SYSTYPE = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				GROUP BY A.NDCNAME, A.OID, A.APP_LEVEL
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 1	THEN
		
			
			IF	V_COUNT > 0	THEN
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT P,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		S.PROTYPE = 7
								AND		S.IID >= 0
								AND S.IID=SS.IID
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		P.SCID = C.SCID
								AND		P.STARTLOGO = 1
								AND		D.CPID = P.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			ELSE
				
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							A.NDCNAME,
							A.OID,
							AN_OBJ_ID AS POID,
							A.OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
							SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
							SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
							SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
							SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
							SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
							SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
							SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	R.CENTERNAME,
											S.IID AS OID,
											S.INAME AS OBJ_NAME,
											N.NDCNAME,
											COUNT(*) AS DEV_NUM
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											(SELECT L.APPLVLID, RIGHT('000' CONCAT CAST(L.APPLVLID AS VARCHAR(20)), 3) CONCAT '.' CONCAT L.APPLVL AS APPLVL FROM IEAI_SYSLV L) L,
											(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND     P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		P.IPROID >= 0
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		L.APPLVLID = SS.SYSTYPE
								AND		N.CENTERNAME = R.CENTERNAME
								GROUP	BY	R.CENTERNAME, S.IID, S.INAME, N.NDCNAME
							) A,
							(
								SELECT	R.CENTERNAME,
											S.IID AS SYSTEMID,
											D.CPSTATUS,
											COUNT(*) AS DAT_CNT
								FROM		IEAI_USERINHERIT U,
											IEAI_SYS_PERMISSION P,
											IEAI_PROJECT S,IEAI_PROJECT_INFO SS,
											IEAI_SYS_RELATION R,
											IEAI_COM_CHK C,
											IEAI_CHKPOINT K,
											HD_CHECK_RESULT_DATA_LAST D
								WHERE		U.IUSERID = AN_USERID
								AND S.IID=SS.IID
								AND   P.IROLEID = U.IROLEID
								AND		P.IPERMISSION = 1
								AND		S.IID = P.IPROID
								AND		S.PROTYPE = 7
								AND		S.IID >= 0
								AND		SS.SYSTYPE = AN_OBJ_ID
								AND		R.SYSTEMID = S.IID
								AND		C.CPID = R.COMPUTERID
								AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
								AND		C.STARTLOGO = 1
								AND		K.SCID = C.SCID
								AND		K.STARTLOGO = 1
								AND		D.CPID = K.CPID
								GROUP BY R.CENTERNAME, S.IID, D.CPSTATUS
							) B
				WHERE		B.SYSTEMID = A.OID
				AND		B.CENTERNAME = A.CENTERNAME
				AND A.NDCNAME = AI_DATA_CENTER
				GROUP BY A.NDCNAME, A.OID, A.OBJ_NAME
				ORDER	BY	A.NDCNAME, A.OID;
			END	IF;
		ELSEIF	AI_OBJ_TYPE = 2	THEN
			IF	LENGTH(AI_DATA_CENTER) > 0	THEN
				INSERT	INTO	TMP_DATASHOW
						(
							OPT_ID,
							DATA_CENTER,
							OID,
							POID,
							OBJ_NAME,
							OBJ_TYPE,
							AMOUNT,
							CL0,	
							CL1,	
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							CHECK_TIME,
							CHECK_RESULT,
							ROW_ID
						)
				SELECT	AN_OPT_ID,
							NDCNAME,
							OID,
							AN_OBJ_ID,
							OBJ_NAME,
							(AI_OBJ_TYPE + 1),
							AMOUNT,
							CL0,
							CL1,
							CL2,
							CL3,
							CL4,
							CL5,
							CHECKING,
							NULL,
							NULL,
							ROW_NUMBER()OVER()
				FROM		(
								SELECT	A.NDCNAME,
											A.OID,
											A.OBJ_NAME,
											NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
											SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
											SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
											SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
											SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
											SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
											SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
											SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
											A.NIP
								FROM		(
												SELECT	R.CENTERNAME,
															R.SDID AS OID,
															L.IP AS OBJ_NAME,
															FUN_GET_IP_NUMBER(L.IP) AS NIP,
															N.NDCNAME
												FROM		IEAI_SYS_RELATION R,
															IEAI_COMPUTER_LIST L,
															(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		L.CPID = R.COMPUTERID
												AND		N.CENTERNAME = R.CENTERNAME
											) A,
											(
												SELECT	R.CENTERNAME,
															R.SDID,
															D.CPSTATUS,
															COUNT(*) AS DAT_CNT
												FROM		IEAI_SYS_RELATION R,
															IEAI_COM_CHK C,
															IEAI_CHKPOINT K,
															HD_CHECK_RESULT_DATA_LAST D
												WHERE		R.SYSTEMID = AN_OBJ_ID
												AND		C.CPID = R.COMPUTERID
												AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
												AND		C.STARTLOGO = 1
												AND		K.SCID = C.SCID
												AND		K.STARTLOGO = 1
												AND		D.CPID = K.CPID
												GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
											) B
								WHERE		B.SDID = A.OID
								AND		B.CENTERNAME = A.CENTERNAME
								AND A.NDCNAME = AI_DATA_CENTER
								GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP
							)
				ORDER	BY	NDCNAME, NIP;
			ELSE
				IF	AN_STATUS = 2	THEN
					INSERT	INTO	TMP_DATASHOW
							(
								OPT_ID,
								DATA_CENTER,
								OID,
								POID,
								OBJ_NAME,
								OBJ_TYPE,
								AMOUNT,
								CL0,	
								CL1,	
								CL2,
								CL3,
								CL4,
								CL5,
								CHECKING,
								CHECK_TIME,
								CHECK_RESULT,
								ROW_ID
							)
					SELECT	AN_OPT_ID,
								NDCNAME,
								OID,
								AN_OBJ_ID,
								OBJ_NAME,
								(AI_OBJ_TYPE + 1),
								AMOUNT,
								CL0,
								CL1,
								CL2,
								CL3,
								CL4,
								CL5,
								CHECKING,
								NULL,
								NULL,
								ROW_NUMBER()OVER()
					FROM		(
									SELECT VG.*,(VG.CL1+VG.CL2+VG.CL3+VG.CL4+VG.CL5) AS ISTATUS FROM (
										SELECT	A.NDCNAME,
													A.OID,
													A.OBJ_NAME,
													NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
													SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
													SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
													SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
													SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
													SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
													SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
													SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
													A.NIP
										FROM		(
														SELECT	R.CENTERNAME,
																	R.SDID AS OID,
																	L.IP AS OBJ_NAME,
																	FUN_GET_IP_NUMBER(L.IP) AS NIP,
																	N.NDCNAME
														FROM		IEAI_SYS_RELATION R,
																	IEAI_COMPUTER_LIST L,
																	(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
														WHERE		R.SYSTEMID = AN_OBJ_ID
														AND		L.CPID = R.COMPUTERID
														AND		N.CENTERNAME = R.CENTERNAME
													) A,
													(
														SELECT	R.CENTERNAME,
																	R.SDID,
																	D.CPSTATUS,
																	COUNT(*) AS DAT_CNT
														FROM		IEAI_SYS_RELATION R,
																	IEAI_COM_CHK C,
																	IEAI_CHKPOINT K,
																	HD_CHECK_RESULT_DATA_LAST D
														WHERE		R.SYSTEMID = AN_OBJ_ID
														AND		C.CPID = R.COMPUTERID
														AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
														AND		C.STARTLOGO = 1
														AND		K.SCID = C.SCID
														AND		K.STARTLOGO = 1
														AND		D.CPID = K.CPID
														GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
													) B
										WHERE		B.SDID = A.OID
										AND		B.CENTERNAME = A.CENTERNAME
										GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP) VG
									)
						ORDER	BY	NDCNAME, NIP;
			ELSE 
				IF	AN_STATUS = 1	THEN
					INSERT	INTO	TMP_DATASHOW
							(
								OPT_ID,
								DATA_CENTER,
								OID,
								POID,
								OBJ_NAME,
								OBJ_TYPE,
								AMOUNT,
								CL0,	
								CL1,	
								CL2,
								CL3,
								CL4,
								CL5,
								CHECKING,
								CHECK_TIME,
								CHECK_RESULT,
								ROW_ID
							)
					SELECT	AN_OPT_ID,
								NDCNAME,
								OID,
								AN_OBJ_ID,
								OBJ_NAME,
								(AI_OBJ_TYPE + 1),
								AMOUNT,
								CL0,
								CL1,
								CL2,
								CL3,
								CL4,
								CL5,
								CHECKING,
								NULL,
								NULL,
								ROW_NUMBER()OVER()
					FROM		(
									SELECT VG.*,(VG.CL1+VG.CL2+VG.CL3+VG.CL4+VG.CL5) AS ISTATUS FROM (
										SELECT	A.NDCNAME,
													A.OID,
													A.OBJ_NAME,
													NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
													SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
													SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
													SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
													SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
													SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
													SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
													SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
													A.NIP
										FROM		(
														SELECT	R.CENTERNAME,
																	R.SDID AS OID,
																	L.IP AS OBJ_NAME,
																	FUN_GET_IP_NUMBER(L.IP) AS NIP,
																	N.NDCNAME
														FROM		IEAI_SYS_RELATION R,
																	IEAI_COMPUTER_LIST L,
																	(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
														WHERE		R.SYSTEMID = AN_OBJ_ID
														AND		L.CPID = R.COMPUTERID
														AND		N.CENTERNAME = R.CENTERNAME
													) A,
													(
														SELECT	R.CENTERNAME,
																	R.SDID,
																	D.CPSTATUS,
																	COUNT(*) AS DAT_CNT
														FROM		IEAI_SYS_RELATION R,
																	IEAI_COM_CHK C,
																	IEAI_CHKPOINT K,
																	HD_CHECK_RESULT_DATA_LAST D
														WHERE		R.SYSTEMID = AN_OBJ_ID
														AND		C.CPID = R.COMPUTERID
														AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
														AND		C.STARTLOGO = 1
														AND		K.SCID = C.SCID
														AND		K.STARTLOGO = 1
														AND		D.CPID = K.CPID
														GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
													) B
										WHERE		B.SDID = A.OID
										AND		B.CENTERNAME = A.CENTERNAME
										GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP) VG
									) WHERE ISTATUS > 0
						ORDER	BY	NDCNAME, NIP;
			ELSE 
					INSERT	INTO	TMP_DATASHOW
							(
								OPT_ID,
								DATA_CENTER,
								OID,
								POID,
								OBJ_NAME,
								OBJ_TYPE,
								AMOUNT,
								CL0,	
								CL1,	
								CL2,
								CL3,
								CL4,
								CL5,
								CHECKING,
								CHECK_TIME,
								CHECK_RESULT,
								ROW_ID
							)
					SELECT	AN_OPT_ID,
								NDCNAME,
								OID,
								AN_OBJ_ID,
								OBJ_NAME,
								(AI_OBJ_TYPE + 1),
								AMOUNT,
								CL0,
								CL1,
								CL2,
								CL3,
								CL4,
								CL5,
								CHECKING,
								NULL,
								NULL,
								ROW_NUMBER()OVER()
					FROM		(
									SELECT VG.*,(VG.CL1+VG.CL2+VG.CL3+VG.CL4+VG.CL5) AS ISTATUS FROM (
										SELECT	A.NDCNAME,
													A.OID,
													A.OBJ_NAME,
													NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
													SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
													SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
													SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
													SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
													SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
													SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
													SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
													A.NIP
										FROM		(
														SELECT	R.CENTERNAME,
																	R.SDID AS OID,
																	L.IP AS OBJ_NAME,
																	FUN_GET_IP_NUMBER(L.IP) AS NIP,
																	N.NDCNAME
														FROM		IEAI_SYS_RELATION R,
																	IEAI_COMPUTER_LIST L,
																	(SELECT RIGHT('00' CONCAT CAST(ROW_NUMBER()OVER() AS VARCHAR(20)), 2) CONCAT '.' CONCAT CENTERNAME AS NDCNAME, CENTERNAME FROM IEAI_DC ORDER BY ISMAIN DESC, CENTERNAME) N
														WHERE		R.SYSTEMID = AN_OBJ_ID
														AND		L.CPID = R.COMPUTERID
														AND		N.CENTERNAME = R.CENTERNAME
													) A,
													(
														SELECT	R.CENTERNAME,
																	R.SDID,
																	D.CPSTATUS,
																	COUNT(*) AS DAT_CNT
														FROM		IEAI_SYS_RELATION R,
																	IEAI_COM_CHK C,
																	IEAI_CHKPOINT K,
																	HD_CHECK_RESULT_DATA_LAST D
														WHERE		R.SYSTEMID = AN_OBJ_ID
														AND		C.CPID = R.COMPUTERID
														AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
														AND		C.STARTLOGO = 1
														AND		K.SCID = C.SCID
														AND		K.STARTLOGO = 1
														AND		D.CPID = K.CPID
														GROUP BY R.CENTERNAME, R.SDID, D.CPSTATUS
													) B
										WHERE		B.SDID = A.OID
										AND		B.CENTERNAME = A.CENTERNAME
										GROUP	BY A.NDCNAME, A.OID, A.OBJ_NAME, A.NIP) VG
									) WHERE ISTATUS = 0
						ORDER	BY	NDCNAME, NIP;
					END IF;
			    END IF;
			END IF;
		ELSEIF	AI_OBJ_TYPE = 3	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						A.CENTERNAME,
						A.OID,
						AN_OBJ_ID AS POID,
						A.OBJ_NAME,
						(AI_OBJ_TYPE + 1),
						NVL(SUM(B.DAT_CNT), 0) AS AMOUNT,
						SUM(DECODE(B.CPSTATUS, 0, B.DAT_CNT, 0)) AS CL0,
						SUM(DECODE(B.CPSTATUS, 1, B.DAT_CNT, 0)) AS CL1,
						SUM(DECODE(B.CPSTATUS, 2, B.DAT_CNT, 0)) AS CL2,
						SUM(DECODE(B.CPSTATUS, 3, B.DAT_CNT, 0)) AS CL3,
						SUM(DECODE(B.CPSTATUS, 4, B.DAT_CNT, 0)) AS CL4,
						SUM(DECODE(B.CPSTATUS, 5, B.DAT_CNT, 0)) AS CL5,
						SUM(DECODE(B.CPSTATUS, -1, B.DAT_CNT, 0)) AS CHECKING,
						NULL,
						NULL,
						ROW_NUMBER()OVER()
			FROM		(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID AS OID,
										T.ICHKITEMNAME AS OBJ_NAME
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKITEM T
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		T.ICHKITEMID = C.CHKITEMID
						) A,
						(
							SELECT	R.CENTERNAME AS CENTERNAME,
										C.SCID,
										D.CPSTATUS,
										COUNT(*) AS DAT_CNT
							FROM		IEAI_SYS_RELATION R,
										IEAI_COM_CHK C,
										IEAI_CHKPOINT P,
										HD_CHECK_RESULT_DATA_LAST D
							WHERE		R.SDID = AN_OBJ_ID
							AND		C.CPID = R.COMPUTERID
							AND		(C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID)
							AND		C.STARTLOGO = 1
							AND		P.SCID = C.SCID
							AND		P.STARTLOGO = 1
							AND		D.CPID = P.CPID
							GROUP BY R.CENTERNAME, C.SCID, D.CPSTATUS
						) B
			WHERE		B.CENTERNAME = A.CENTERNAME
			AND		B.SCID = A.OID
			GROUP BY A.CENTERNAME, A.OID, A.OBJ_NAME
			ORDER	BY	A.OID;
		ELSEIF	AI_OBJ_TYPE = 4	THEN
			
			INSERT	INTO	TMP_DATASHOW
					(
						OPT_ID,
						DATA_CENTER,
						OID,
						POID,
						OBJ_NAME,
						OBJ_TYPE,
						AMOUNT,
						CL0,	
						CL1,	
						CL2,
						CL3,
						CL4,
						CL5,
						CHECKING,
						CHECK_TIME,
						CHECK_RESULT,
						ROW_ID
					)
			SELECT	AN_OPT_ID,
						NULL,
						D.RSDID,
						AN_OBJ_ID AS POID,
						D.CPTEXT,
						(AI_OBJ_TYPE + 1),
						1 AS AMOUNT,
						DECODE(D.CPSTATUS, 0, 1, 0) AS CL0,
						DECODE(D.CPSTATUS, 1, 1, 0) AS CL1,
						DECODE(D.CPSTATUS, 2, 1, 0) AS CL2,
						DECODE(D.CPSTATUS, 3, 1, 0) AS CL3,
						DECODE(D.CPSTATUS, 4, 1, 0) AS CL4,
						DECODE(D.CPSTATUS, 5, 1, 0) AS CL5,
						DECODE(D.CPSTATUS, -1, 1, 0) AS CHECKING,
						FUN_GET_TIMESTAMP(D.CPTIME, 8),
						D.CPSTATUS,
						ROW_NUMBER()OVER()
			FROM		IEAI_CHKPOINT P,
						HD_CHECK_RESULT_DATA_LAST D
			WHERE		P.SCID = AN_OBJ_ID
			AND		P.STARTLOGO = 1
			AND		D.CPID = P.CPID;
		END	IF;
		
		
		SELECT	COUNT(*)
		INTO		AI_REC_AMOUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID;
		
		
		SET	LI_PAGE_COUNT = INT(AI_REC_AMOUNT / AI_PAGE_SIZE);
		
		IF MOD(AI_REC_AMOUNT, AI_PAGE_SIZE) > 0 THEN
			SET	LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
		END	IF;
		
		IF	AI_PAGE_ID < 1 THEN
			SET	AI_PAGE_ID = 1;
		END	IF;
		
		IF	AI_PAGE_ID > LI_PAGE_COUNT	THEN
			SET	AI_PAGE_ID = LI_PAGE_COUNT;
		END	IF;
		
		SET	LI_PAGE_FROM = (AI_PAGE_ID - 1) * AI_PAGE_SIZE + 1;
		SET	LI_PAGE_TO = AI_PAGE_ID * AI_PAGE_SIZE;
		
		SELECT	SIGN(COUNT(*))
		INTO		V_COUNT
		FROM		TMP_DATASHOW
		WHERE		OPT_ID = AN_OPT_ID
		AND		(ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		
		IF V_COUNT > 0 THEN
			DELETE	FROM	TMP_DATASHOW WHERE OPT_ID = AN_OPT_ID AND (ROW_ID < LI_PAGE_FROM OR ROW_ID > LI_PAGE_TO);
		END IF;
	END
	$
	
	
	CREATE OR REPLACE PROCEDURE PROC_GET_A_MAIL_FOR_PINGAN (IN AI_TIMER INTEGER, OUT AN_MAIL_ID NUMERIC(19,0), 
	  OUT AN_SYS_ID NUMERIC(19,0), OUT AV_SYS_NAME VARCHAR(200),
	  OUT AV_MAILS VARCHAR(1000), OUT AV_SUMMARY VARCHAR(200))
		AUTONOMOUS
		LANGUAGE SQL
	  BEGIN
		DECLARE  LN_MAIL_ID  NUMERIC(19,0);
		DECLARE  SQLCODE    INTEGER  DEFAULT  0;
		DECLARE  RETSQLCODE  INTEGER  DEFAULT  0;
		DECLARE  LT_NOW    TIME;
		DECLARE  LI_REDO    SMALLINT  DEFAULT  0;
		DECLARE  LI_CNT    SMALLINT;
		DECLARE  LI_MaxRedo  SMALLINT  DEFAULT  10;
		DECLARE  LI_TIMERS  INTEGER;
		DECLARE  LI_HOURS    SMALLINT;
		DECLARE  LI_MINUTES  SMALLINT;
		DECLARE  LI_POWERS  SMALLINT  DEFAULT 5;
		DECLARE  LI_WEEK_DAY  SMALLINT;
		
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET  RETSQLCODE = SQLCODE;
		
		SET  LT_NOW = CURRENT TIME;
		SET  AN_MAIL_ID = -1;
		SET  LI_HOURS  = AI_TIMER * LI_POWERS / 3600;
		SET  LI_MINUTES = AI_TIMER * LI_POWERS / 60;
		
		SET  LI_TIMERS = LI_HOURS * 10000 + LI_MINUTES * 100 + MOD(AI_TIMER * 5, 60);
		
		
		SELECT  COUNT(*)
		INTO    LI_CNT
		FROM    IEAI_SENDMAIL
		WHERE    STATUS = 10
		AND    ((LT_NOW - GET_TIME) + (CASE WHEN LT_NOW < GET_TIME THEN 240000 ELSE 0 END)) > LI_TIMERS;
		
		IF  LI_CNT > 0  THEN
		  UPDATE  IEAI_SENDMAIL
		  SET    GET_TIME = TIME('00:00:00'),
				STATUS = 1
		  WHERE    STATUS = 10
		  AND    ((LT_NOW - GET_TIME) + (CASE WHEN LT_NOW < GET_TIME THEN 240000 ELSE 0 END)) > LI_TIMERS;
		  
		  COMMIT WORK;
		END IF;
		
		
		
		SET  LI_WEEK_DAY = 70 + DAYOFWEEK(CURRENT DATE);
		
		WHILE  (LI_REDO < LI_MaxRedo)
		DO
		  SELECT  MIN(A.IID)
		  INTO    LN_MAIL_ID
		  FROM    IEAI_SENDMAIL A
		  WHERE    A.STATUS = 1
		  AND    A.MSWITCH = 1
		  AND    DECODE(A.DAY_TYPE, 0, 1, DECODE(A.DAY_TYPE, LI_WEEK_DAY, 1, 0)) > 0
		  AND    A.PLANNING_TIME =
				  (
					SELECT  MAX(B1.PLANNING_TIME)
					FROM    IEAI_SENDMAIL B1
					WHERE    B1.SYSTEMID = A.SYSTEMID
					AND    B1.MSWITCH = 1
					AND    DECODE(B1.DAY_TYPE, 0, 1, DECODE(B1.DAY_TYPE, LI_WEEK_DAY, 1, 0)) > 0
					AND    B1.PLANNING_TIME <= LT_NOW
				  );
		  

		  UPDATE  IEAI_SENDMAIL M
		  SET    M.STATUS = 10,
				 M.GET_TIME = LT_NOW
		  WHERE EXISTS(
				SELECT 1  FROM IEAI_SENDMAIL BB 
				WHERE BB.DAY_TYPE=M.DAY_TYPE
				AND   BB.PLANNING_TIME=M.PLANNING_TIME
				AND   BB.MSWITCH=M.MSWITCH
				AND   BB.IID = LN_MAIL_ID
		  )
		  AND M.MSWITCH=1
		  AND M.STATUS = 1;
		  
		  IF  RETSQLCODE = 0 THEN
			COMMIT WORK;
			
			SELECT  IID,
				  SYSTEMID,
				  SYSNAME,
				  MAILS,
				  NVL(SUMMARY, '')
			INTO    AN_MAIL_ID,
				  AN_SYS_ID,
				  AV_SYS_NAME,
				  AV_MAILS,
				  AV_SUMMARY
			FROM    IEAI_SENDMAIL
			WHERE    IID = LN_MAIL_ID;
			
			SET  LI_REDO = LI_MaxRedo;
		  ELSE
			ROLLBACK WORK;
		  END IF;
		  
		  SET  LI_REDO = LI_REDO + 1;
		END WHILE;
	  END
	  $
	  
	-- v8.3.0
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IBASIC' AND TABNAME='IEAI_CHKITEM';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_CHKITEM ADD COLUMN IBASIC DECIMAL(2) DEFAULT 0 ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;


	END
	$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HC_INSPECT_TASK' AND  COLNAME='TASKDESC';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_HC_INSPECT_TASK ADD TASKDESC VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HC_ACTWARNING' AND  COLNAME='DELUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_HC_ACTWARNING ADD DELUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_HANDWORK_HIS' AND  COLNAME='TASKNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHECK_HANDWORK_HIS ADD TASKNAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$


-- 4.8.5 version HC patch is as follows

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_HC_CONFIRM';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_HC_CONFIRM (IID NUMERIC (19) NOT NULL,RSDID NUMERIC (19) ,CRID NUMERIC (19) ,IUSERID NUMERIC (19) ,IFULLNAME VARCHAR (255) ,ICONFIRMDATE NUMERIC (19) ,IWCOUNT NUMERIC (19) ,IWCODE NUMERIC (19) , CONSTRAINT PK_IEAI_HC_CONFIRM PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='HD_CHECK_RESULT_DATA_CACHE' AND  COLNAME='STARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE HD_CHECK_RESULT_DATA_CACHE ADD STARTUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_HANDWORK_HIS' AND  COLNAME='STARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHECK_HANDWORK_HIS ADD STARTUSER VARCHAR(50) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHECK_HANDWORK_PENGDING' AND  COLNAME='STARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHECK_HANDWORK_PENGDING ADD STARTUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='HD_CHECK_RESULT_DATA_HIS' AND  COLNAME='STARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE HD_CHECK_RESULT_DATA_HIS ADD STARTUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='HD_CHECK_RESULT_DATA_LAST' AND  COLNAME='STARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE HD_CHECK_RESULT_DATA_LAST ADD STARTUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='STARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ADD STARTUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='STOPUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ADD STOPUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='IMMESTARTUSER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ADD IMMESTARTUSER VARCHAR(30) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CHKPOINT' AND  COLNAME='IMMESTARTTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_CHKPOINT ADD IMMESTARTTIME NUMERIC(19) DEFAULT 0 '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$


CREATE OR REPLACE PROCEDURE	PROC_GET_CHECK_POINTS (IN AV_HOSTIP VARCHAR(15), OUT AI_DO INTEGER)
	LANGUAGE SQL
	BEGIN
		DECLARE	LI_NOW			INTEGER;
		DECLARE	LTM_NOW			TIME;
		DECLARE	LD_TODAY			DATE;
		DECLARE	LI_DayOfWeek	SMALLINT;
		DECLARE	LI_DAY			SMALLINT;
		
		SET	LTM_NOW = CURRENT TIME;
		SET	LI_NOW = HOUR(LTM_NOW) * 60 + MINUTE(LTM_NOW);
		
		SET	LD_TODAY = CURRENT DATE;
		SET	LI_DayOfWeek = DayOfWeek_ISO(LD_TODAY) + 70;
		SET	LI_DAY = DAY(LD_TODAY);
		
		SELECT	NVL(SIGN(COUNT(*)), 0)
		INTO		AI_DO
		FROM		IEAI_COMPUTER_LIST A, 
					IEAI_COM_CHK B LEFT JOIN IEAI_SYS_RELATION F ON F.SYSTEMID = B.APPLOGO, 
					IEAI_CHKPOINT C, 
					HD_AGENT_CHECK_TIME D, 
					IEAI_CHKITEM E
		WHERE		A.IP = AV_HOSTIP
		AND		B.CPID = A.CPID
		AND		B.STARTLOGO = 1
		AND		B.IHIDDEN = 0
		AND		C.SCID = B.SCID
		AND		C.STARTLOGO = 1
		AND		D.MEID = A.CPID
		AND		B.CHKITEMID = E.ICHKITEMID
		AND		MOD(D.INTERVAL_MINUTE, C.INTLLENGTH) = 0
		AND		(
						NOT EXISTS	(
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
										) OR
						EXISTS		(	-- Everyday
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 4
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	-- Every Month
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 8
											AND		(LI_DAY BETWEEN NVL(B1.START_DAY, 1) AND FUN_GET_END_DAY (LD_TODAY, B1.END_DAY))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										) OR
						EXISTS		(	-- Every Week
											SELECT	1
											FROM		HD_CHECK_TIME_INTERVAL_CONFIG B1
											WHERE		B1.CPID = C.CPID
											AND		B1.ENABLED = 1
											AND		B1.CYCLE_TYPE = 16
											AND		(LI_DayOfWeek BETWEEN NVL(B1.START_DAY, 0) AND NVL(B1.END_DAY, 0))
											AND		(LI_NOW BETWEEN B1.SET_START AND B1.SET_END)
										)
					);
	END
	$


CREATE OR REPLACE PROCEDURE PROC_DEAL_ACT_WARNING_MESSAGE   (IN AN_PK NUMERIC(19,0),
	IN AV_USER VARCHAR(50), IN AV_SUGGESTION VARCHAR(200))
		LANGUAGE SQL
	BEGIN
		DECLARE	LI_EXISTS	SMALLINT;
		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
		DECLARE	LI_COUNT		INTEGER;
		DECLARE	LN_KEY		NUMERIC(19,0);
		
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET	RETSQLCODE = SQLCODE;
		
		INSERT INTO IEAI_HC_ACTWARNING_HIS
				(
					IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					DELUSER,
					DELTIME,
					DISPOSETYPE,
					SUGGESTION
				)
		SELECT	IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					AV_USER,
					CURRENT TIMESTAMP,
					2,
					AV_SUGGESTION
		FROM		IEAI_HC_ACTWARNING
		WHERE		IID = AN_PK;
		
		DELETE	FROM	IEAI_HC_ACTWARNING WHERE IID = AN_PK;
		
		IF	RETSQLCODE = 0 OR RETSQLCODE = 100 THEN
			COMMIT WORK;
		ELSE
			ROLLBACK WORK;
		END IF;
	END
	$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HC_CONFIRM' AND  COLNAME='IFLAG';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_HC_CONFIRM ADD IFLAG NUMERIC(19) DEFAULT 0'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$



	CREATE OR REPLACE PROCEDURE PROC_SAVE_WARNING_MESSAGE 
		(
			IN AI_WCODE SMALLINT,
			IN AV_WDATE VARCHAR(23),
			IN AV_WSOURCE VARCHAR(20),
			IN AV_PRGNAME VARCHAR(50),
			IN AN_ERRCODE NUMERIC(12,0),
			IN AV_HOST VARCHAR(40),
			IN AV_IP VARCHAR(15),
			IN AV_CVALUE VARCHAR(128),
			IN AV_THREADHOLD VARCHAR(128),
			IN AV_AMSG VARCHAR(128),
			IN AN_CPID NUMERIC(19,0),
			IN AV_WARN_CODE_LIST VARCHAR(50),
			OUT AN_KEY NUMERIC(19,0),
			OUT AV_RETURN VARCHAR(128)
		)
		LANGUAGE SQL
	BEGIN
		DECLARE	LI_EXISTS	SMALLINT;
		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
		DECLARE	LI_COUNT		INTEGER;
		DECLARE	LN_KEY		NUMERIC(19,0);
		DECLARE	LI_LOOP		INTEGER;
		DECLARE	LI_LENGTH	INTEGER;
		DECLARE	LV_DATA		VARCHAR(200);
		DECLARE	LI_DATA		INTEGER;
		DECLARE	LI_SWITCH	SMALLINT;
		
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET	RETSQLCODE = SQLCODE;
		
		SET		AN_KEY = -1;
		
		SELECT	NVL(MAX(IID), -1)
		INTO		LN_KEY
		FROM		IEAI_HC_ACTWARNING
		WHERE		IP = AV_IP
		AND		ERRCODE = AN_ERRCODE
		AND		PRGNAME = AV_PRGNAME
		AND		WCODE = AI_WCODE
		AND		THREADHOLD = AV_THREADHOLD
		AND		AMESSAGE = AV_AMSG;
		
		
		IF LN_KEY < 0 THEN
		  INSERT INTO IEAI_HC_ACTWARNING_HIS
				  (
					IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					DELUSER,
					DELTIME,
					SUGGESTION,
					DISPOSETYPE
				  )
			  SELECT  IID,
					  CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					W.IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					'Auto change Close',
					CURRENT_TIMESTAMP,
					'告警改变或恢复',
					'2'
			  FROM   IEAI_HC_ACTWARNING W
			  WHERE	 W.IP = AV_IP
				AND	 W.AMESSAGE = AV_AMSG
				AND  W.CPID = AN_CPID;
				
				
			  DELETE	FROM	IEAI_HC_ACTWARNING WW
			  WHERE	 WW.IP = AV_IP
				AND	 WW.AMESSAGE = AV_AMSG
				AND  WW.CPID = AN_CPID;
				
		END IF;
		
		IF	LN_KEY >= 0 THEN
			UPDATE	IEAI_HC_ACTWARNING
			SET		WCOUNT = WCOUNT + 1,
						LDATE = TIMESTAMP(AV_WDATE),
						CHKVALUE = AV_CVALUE
			WHERE		IID = LN_KEY;
		ELSE
			CALL PROC_GET_NEXT_PK('IEAI_HC_ACTWARNING', LN_KEY);
			INSERT INTO IEAI_HC_ACTWARNING
					(
						IID,
						WCODE,
						WDATE,
						LDATE,
						WSOURCE,
						PRGNAME,
						ERRCODE,
						HOSTNAME,
						IP,
						CHKVALUE,
						THREADHOLD,
						AMESSAGE,
						WCOUNT,
						TFLAG,
						WMESSAGE,
						SYSNAME,
						CPID
					)
			SELECT	LN_KEY,
						AI_WCODE,
						TIMESTAMP(AV_WDATE),
						TIMESTAMP(AV_WDATE),
						AV_WSOURCE,
						AV_PRGNAME,
						AN_ERRCODE,
						AV_HOST,
						AV_IP,
						AV_CVALUE,
						AV_THREADHOLD,
						AV_AMSG,
						1,
						0,
						NULL,
						FUN_GET_APPS_NAME(AN_CPID, '运维服务自动化系统'),
						AN_CPID
			FROM		IDUAL;
			
			SELECT	WSWITCH
			INTO		LI_SWITCH
			FROM		IEAI_COMPUTER_LIST
			WHERE		IP = AV_IP;
			
			IF LI_SWITCH = 1 THEN
				SET		LI_LOOP = 1;
				SET		LI_DATA = 0;
				
				WHILE	LI_DATA IS NOT NULL
				DO
					SET	LI_DATA = FUN_GET_STRING_NUMBER(AV_WARN_CODE_LIST, LI_LOOP);
					
					IF	AI_WCODE >= LI_DATA THEN
						SET	AN_KEY = LN_KEY;
						SET	LI_DATA = NULL;
					END IF;
					
					SET	LI_LOOP = LI_LOOP + 1;
				END WHILE;
			END IF;
		END IF;
		
		IF	RETSQLCODE = 0 OR RETSQLCODE=100 THEN
			SET	AV_RETURN = 'SCUCCESS';
			COMMIT WORK;
		ELSE
			SET	AV_RETURN = 'FAILURE';
			ROLLBACK WORK;
		END IF;
	END 
	$


-- PROC_SET_CHK_DATA_LAST_STATUS move to v8.9.0 version change

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_HCIMME_CAMA_BATCH';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_HCIMME_CAMA_BATCH (HCBATCHID NUMERIC (19) NOT NULL,POINTID NUMERIC (19) NOT NULL,STARTPARAM VARCHAR (100) ,STARTUSER VARCHAR (30) ,STARTTIME NUMERIC (19) , CONSTRAINT PK_IEAI_HCIMME_CAMA_BATCH PRIMARY KEY (HCBATCHID, POINTID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

-- V8.8.0 version
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_GF_HC_XMDB_SYS';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_GF_HC_XMDB_SYS (IID NUMERIC (19) NOT NULL,IINSTID VARCHAR (255) ,IFIRSTSYS VARCHAR (255) ,ISECONDAPP VARCHAR (255) ,ICISTATUS VARCHAR (255) ,IUSEFOR VARCHAR (255) ,IAROLE VARCHAR (255) ,IBROLE VARCHAR (255) , CONSTRAINT PK_IEAI_GF_HC_XMDB_SYS PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$


-- V8.8.0 version PROC_GET_RESULT_ALL_REPORT 提升查询效率
CREATE OR REPLACE PROCEDURE PROC_GET_RESULT_ALL_REPORT  (IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_CPID	NUMERIC(19,0);
			DECLARE	LN_SYSID	NUMERIC(19,0);
			DECLARE	LN_NUM	INTEGER;

			-- GET System ID
			SELECT	IID
			INTO		LN_SYSID
			FROM		IEAI_PROJECT
			WHERE		INAME = AV_SYS_NAME
			AND		PROTYPE = 7;

			SET		LN_NUM = AV_END - AV_START;
			
			IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
                        )t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND   t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID	
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


			ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
								AND		t1.CPTIME <= AV_TIME_END
						)t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND		t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		t1.CPTIME <= AV_TIME_END
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


			ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
								AND		t1.CPTIME >= AV_TIME_START
						)t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND		t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		t1.CPTIME >= AV_TIME_START
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


			ELSE

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
								AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
						)t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND		t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;
			END	IF;
		END
		$
-- V8.8.0 version PROC_GET_RESULT_REPORT_GRID 巡检面板提升查询效率
CREATE OR REPLACE PROCEDURE PROC_GET_RESULT_REPORT_GRID  (IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
	LANGUAGE SQL
	BEGIN
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);
		DECLARE	LN_NUM	INTEGER;
		SELECT	CPID
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST
		WHERE		IP = AV_IP;
		
		
		SELECT	IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;

		SET		LN_NUM = AV_END - AV_START;
		
		IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;
		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND	 t1.CPTIME <= AV_TIME_END
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID
							AND		t1.CPTIME <= AV_TIME_END
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
					AND t5.RECID > AV_START
		       ;


		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
				SELECT	COUNT(1)
				INTO	RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND	 t1.CPTIME >= AV_TIME_START
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID	    
							AND		t1.CPTIME >= AV_TIME_START
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


		ELSE
				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID    
							AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;
		END	IF;
	END
	$
	
-- V8.9.0 version	
CREATE OR REPLACE PROCEDURE PROC_SET_CHK_DATA_LAST_STATUS  (IN AV_IP VARCHAR(25))
	LANGUAGE SQL
BEGIN
	DECLARE	LD_NOW	NUMERIC(19,0);
	DECLARE	LI_CNT	INTEGER;
	
	-- 1 get the number of NOW
	SET		LD_NOW = FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP, 8);
	
	-- 2 set check point status
	-- 2.1 Update CPSTATUS from The max of HD_CHECK_STATUS_LAST.CPSTATUS
	UPDATE	HD_CHECK_RESULT_DATA_LAST
	SET		CPSTATUS =	(
									SELECT	NVL(MAX(CAST(B1.I1 AS INTEGER)), -1)
									FROM		HD_CHECK_STATUS_LAST B1
									WHERE		B1.RSDID = HD_CHECK_RESULT_DATA_LAST.RSDID
								),
				CPTIME = LD_NOW
	WHERE		IP = AV_IP
	AND		CPSTATUS = -1;
	
	-- 2.2 Update CPSTATUS from the max of TMP_EXCEPTION_CHECK_POINT.ALARMCODE which it could identity by CIID and CPID
	UPDATE	HD_CHECK_RESULT_DATA_LAST
	SET		CPSTATUS =	(
									SELECT	NVL(MAX(B1.ALARMCODE), -1)
									FROM		TMP_EXCEPTION_CHECK_POINT B1
									WHERE		B1.IP = AV_IP
									AND		B1.CHKITEMID = HD_CHECK_RESULT_DATA_LAST.CIID
									AND		B1.CHKPOINTID = HD_CHECK_RESULT_DATA_LAST.CPID
								),
				CPTIME = LD_NOW
	WHERE		IP = AV_IP
	AND		CPSTATUS = -1;
	
	-- 2.3 Update CPSTATUS 
	UPDATE	HD_CHECK_RESULT_DATA_LAST
	SET		CPSTATUS =	FUN_GET_ALARM_CODE (CPTEXT),
				CPTIME = LD_NOW
	WHERE		IP = AV_IP
	AND		CPSTATUS = -1;
	
	-- 3 write data into CACHE table
	-- 3.1 write HD_CHECK_RESULT_DATA_CACHE
	INSERT	INTO	HD_CHECK_RESULT_DATA_CACHE
			(
				RSDID,
				ISYSID,
				ISYSNAME,
				CIID,
				CINAME,
				CIPARSRULE,
				CISTATUS,
				MEID,
				IP,
				PORT,
				CPID,
				CPTEXT,
				CPTIME,
				COMCHKID,
				CPSTATUS,
				CPTIMESTAMP
			)
	SELECT	RSDID,
				ISYSID,
				ISYSNAME,
				CIID,
				CINAME,
				CIPARSRULE,
				CISTATUS,
				MEID,
				IP,
				PORT,
				CPID,
				CPTEXT,
				CPTIME,
				COMCHKID,
				CPSTATUS,
				CPTIMESTAMP
	FROM		HD_CHECK_RESULT_DATA_LAST
	WHERE		IP = AV_IP
	AND		CISTATUS = 1;
	
	-- 3.2 write HD_CHECK_STATUS_CACHE
	INSERT	INTO	HD_CHECK_STATUS_CACHE
			(
				CRID,
				CPID,
				RSDID,
				I1,
				I2,
				I3,
				I4,
				I5,
				I6,
				I7,
				I8,
				I9,
				I10,
				I11,
				I12,
				I13,
				I14,
				I15,
				I16,
				I17,
				I18,
				I19,
				I20
			)
	SELECT	B.CRID,
				B.CPID,
				B.RSDID,
				B.I1,
				B.I2,
				B.I3,
				B.I4,
				B.I5,
				B.I6,
				B.I7,
				B.I8,
				B.I9,
				B.I10,
				B.I11,
				B.I12,
				B.I13,
				B.I14,
				B.I15,
				B.I16,
				B.I17,
				B.I18,
				B.I19,
				B.I20
	FROM		HD_CHECK_RESULT_DATA_LAST A,
				HD_CHECK_STATUS_LAST B
	WHERE		A.IP = AV_IP
	AND		A.CISTATUS = 1
	AND		B.RSDID = A.RSDID;
	
	-- 4 try to auto close warning event
	-- 4.1 to calculate the flag of auto closing
	SELECT	COUNT(DID)
	INTO		LI_CNT
	FROM		IEAI_SYS_DICTIONARY
	WHERE		DID = 100
	AND		DNAME = 'AutoCloseWarningEvent'
	AND		DINTVALUE = '1';
	
	IF LI_CNT > 0 THEN
		
		-- 4.2 get auto to close warning event list
		INSERT INTO TMP_AUTO_CLOSE_WARNING_EVENT
				(
					RID
				) 
		
		
		SELECT DISTINCT  W.IID
		  FROM IEAI_HC_ACTWARNING W
		 WHERE NOT EXISTS
		 (SELECT 1
				  FROM HD_CHECK_RESULT_DATA_LAST D LEFT JOIN HD_CHECK_STATUS_LAST S
				   ON   D.RSDID = S.RSDID
				   AND  D.CPID  = S.CPID
				 WHERE  W.CPID  =  D.CPID
				   AND  W.IP    = D.IP
				   AND  W.IP    = AV_IP
				   AND  (S.I1 > 2 OR (S.I1 IS NULL  AND D.CPSTATUS>2) )
				)
			AND     W.IP = AV_IP;
		
		-- 4.3 write warning history
		INSERT INTO IEAI_HC_ACTWARNING_HIS
				(
					IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					DELUSER,
					DELTIME
				)
		SELECT	IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					'Auto Close',
					CURRENT TIMESTAMP
		FROM		IEAI_HC_ACTWARNING W,
					TMP_AUTO_CLOSE_WARNING_EVENT A
		WHERE		W.IID = A.RID;
		
		-- 4.4 clean warning information
		DELETE
		FROM		IEAI_HC_ACTWARNING W
		WHERE		EXISTS (SELECT 1 FROM TMP_AUTO_CLOSE_WARNING_EVENT A WHERE A.RID = W.IID);
	END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_HC_WARN_SEQ';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_HC_WARN_SEQ (ICURRENTDATE VARCHAR (8) NOT NULL ,ITYPE VARCHAR (10)  NOT NULL ,ISEQNO NUMERIC (19) , CONSTRAINT PK_IEAI_HC_WARN_SEQ PRIMARY KEY (ICURRENTDATE,ITYPE))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
