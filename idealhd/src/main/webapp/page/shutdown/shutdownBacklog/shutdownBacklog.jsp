<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <style>
        .actmonitor .amtor_orange .x-grid-cell {

            background-color: orange;
        }

        .actmonitor .amtor_orange .x-grid-row-over .x-grid-td {
            background-color: orange;
        }

        .actmonitor .amtor_yellow .x-grid-cell {

            background-color: yellow;
        }

        .actmonitor .amtor_yellow .x-grid-row-over .x-grid-td {
            background-color: yellow;
        }

        .actmonitor .amtor_red .x-grid-cell {

            background-color: #EE3B3B;
        }

        .actmonitor .amtor_red .x-grid-row-over .x-grid-td {
            background-color: #EE3B3B;
        }

        .actmonitor .amtor_green .x-grid-cell {

            background-color: #48DD22;
        }

        .actmonitor .amtor_green .x-grid-row-over .x-grid-td {
            background-color: #48DD22;
        }

        .actmonitor .amtor_blue .x-grid-cell {

            background-color: #0099FF;
        }

        .actmonitor .amtor_blue .x-grid-row-over .x-grid-td {
            background-color: #0099FF;
        }

    </style>
    <script type="text/javascript">
        var jobSchedulingQuerySystemSwitch =<%=ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch()%>;
        var ycQueryGroNameSwitch =<%=ServerEnv.getInstance().getBooleanConfig("ycSwitch.groupname.switch", false)%>;
        var sysType = 28;
    </script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/ux/aomsMonitorBtn.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/ux/aomsGridNoCheck.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/ux/aomsUtils.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/common/shellinforuningieai.js"></script>

    <script type="text/javascript" src="<%=request.getContextPath()%>/page/common/Tn5250inforuningieai.js"></script>
    <script type="text/javascript"
            src="<%=request.getContextPath()%>/page/jobScheduling/snapshoot/snapshootIeaiWindow.js"></script>
    <script type="text/javascript"
            src="<%=request.getContextPath()%>/page/jobScheduling/flowQuery/flowSnapshoot.js"></script>
    <script type="text/javascript"
            src="<%=request.getContextPath()%>/page/shutdown/shutdownBacklog/shutdownBacklog.js"></script>

    <link type="text/css" rel="stylesheet" href="<%=request.getContextPath()%>/css/mergely.css"/>
    <script type="text/javascript" src="<%=request.getContextPath()%>/js/mergely.js"></script>
</head>
<body>
<div id="shutdownBacklog" style="width: 100%;height: 100%"></div>
</body>
</html>
