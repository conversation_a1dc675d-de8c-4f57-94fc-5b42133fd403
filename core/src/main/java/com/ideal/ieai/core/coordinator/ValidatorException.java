package com.ideal.ieai.core.coordinator;

import com.ideal.ieai.core.basis.*;

/**
 * <p>Title: iEAI Core3.0</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 1.0
 */

public class ValidatorException extends IEAIException
{
    public ValidatorException()
    {
        super();
    }

    public ValidatorException(Exception TheException)
    {
        super(TheException);
        _exception =TheException;
    }

    public ValidatorException(String message)
    {
        super(message);
        _exception = new Exception(message);
    }

    public ValidatorException (int ErrCode)
    {
        super();
        _type = ErrCode;

    }

    public String getMessage()
    {
        return (null == _exception)?_exception.getMessage():String.valueOf(_type);
    }

    public int getErrCode()
    {
        return _type;
    }

    private int _type = UNKNOWN_EXCEPTION;
    private Exception _exception = null;
    /**
     * Exception that we can not get the reason.
     */
    public static final int UNKNOWN_EXCEPTION = -1;

    /**
     * Exception that the declaration of the activity input param is not incorrect
     */
    public static final int INVALID_ACTIVITY_INPUT_PARAM_DECLARATIONS = 4;

    /**
     * Exception that the declaration of the activity output param is not incorrect
     */
    public static final int INVALID_ACTIVITY_OUTPUT_PARAM_DECLARATIONS = 8;

}