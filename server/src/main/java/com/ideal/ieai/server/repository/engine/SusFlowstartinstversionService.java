package com.ideal.ieai.server.repository.engine;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.sus.flowstart.FlowstartinstManager;
import com.ideal.ieai.server.repository.sus.flowstart.prepare.StartPreparedBean;
import com.ideal.ieai.server.repository.sus.importexecl.SusSysInfo;

public class SusFlowstartinstversionService
{
    private static SusFlowstartinstversionService _intance = new SusFlowstartinstversionService();
    static Logger                                 _logger  = Logger.getLogger(FlowstartinstManager.class);

    public static SusFlowstartinstversionService getInstance ()
    {
        if (_intance == null)
        {
            _intance = new SusFlowstartinstversionService();
        }
        return _intance;
    }

    public SusSysInfo getSusSysInfoObj ( String iinstanceName, int type ) throws RepositoryException
    {
        return FlowstartinstManager.getInstance().getSusSysInfoObjLoop(iinstanceName, type);
    }


    public long saveWorkItemGraph ( String startUser, String execUser, String iversiondes, String evnIds,
            String patchPATH, Integer graphType, String instanceName, String sysName, Long iid, Long ibusnesSysIid,
            Integer issusflow, Integer isbackinstance, Long workitemId, String prepareStartIpsJson, int type,String delayedStartTime
            ,long deployeType,String isDeplyedFlow,int startType,String userInnerCode,
            long busSysType,String isystype,String isysid,String version
            )
            throws RepositoryException
    {
        List<StartPreparedBean> stepIpDatas = parseStartPrepBean(prepareStartIpsJson);
        return FlowstartinstManager.getInstance().saveWorkItemGraphLoop(startUser, execUser, iversiondes, evnIds,
            patchPATH, graphType, instanceName, sysName, iid, ibusnesSysIid, issusflow, isbackinstance, workitemId,
            stepIpDatas, type,delayedStartTime,
             deployeType, isDeplyedFlow, startType, userInnerCode
            , busSysType, isystype, isysid, version, null, -1, "0",""
             );
    }

    public Map<String, Object> getColvalueByWorkitemid ( Long iworkItemid, int hisFlag, int type )
            throws RepositoryException
    {
        return FlowstartinstManager.getInstance().getColvalueByWorkitemidLoop(iworkItemid, hisFlag, type);
    }

    /**
     * 
     * @Title: parseStartPrepBean   
     * @Description: (解析ip服务器)   
     * @param jsonResData
     * @return      
     * @author: Administrator 
     * @date:   2017年9月6日 上午10:10:29
     */
    private List<StartPreparedBean> parseStartPrepBean ( String jsonResData )
    {
        List<StartPreparedBean> result = new ArrayList<StartPreparedBean>();
        JSONArray jsonArr;
        JSONObject json2;
        StartPreparedBean bean = null;
        try
        {
            jsonArr = new JSONArray(jsonResData);
            if (jsonArr != null)
            {
                for (int i = 0; i < jsonArr.length(); i++)
                {
                    json2 = jsonArr.getJSONObject(i);
                    bean = new StartPreparedBean();

                    bean.setIenvid(json2.getString("ienvid"));
                    bean.setEname(json2.getString("ename"));
                    bean.setHostiid(json2.getString("hostiid"));
                    bean.setIip(json2.getString("iip"));
                    bean.setIport(json2.getString("iport"));

                    bean.setIgroupid(json2.getString("igroupid"));
                    bean.setIresname((json2.getString("iresname")));
                    bean.setIappype(json2.getString("iappype"));
                    bean.setActiid(json2.getString("actiid"));
                    bean.setIinstanceid(json2.getString("iinstanceid"));
                    bean.setIinstancename(json2.getString("iinstancename"));

                    bean.setIserner(json2.getInt("iserner"));
                    bean.setIstepname(json2.getString("istepname"));
                    bean.setImodelInstName(json2.getString("imodelInstName"));
                    bean.setChecked(json2.getBoolean("checked"));

                    bean.setModelShellid(json2.getString("modelShellid"));
                    bean.setModelShellName(json2.getString("modelShellName"));
                    bean.setModelShellImxgraphid(json2.getInt("modelShellImxgraphid"));
                    result.add(bean);
                }
            }
        } catch (JSONException jsonexception)
        {
            _logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), jsonexception);

        }
        return result;
    }

}
