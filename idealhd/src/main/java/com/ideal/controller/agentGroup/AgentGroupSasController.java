package com.ideal.controller.agentGroup;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.hd.AgentGroup.*;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.Agent;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentExportData;
import com.ideal.service.agentGroup.AgentGroupService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Controller
public class AgentGroupSasController
{
    static Logger              log     = Logger.getLogger(AgentGroupSasController.class);
    public static final String SUCCESS = "success";
    public static final String MESSAGE = "message";

    @RequestMapping("getAgentGroup.do")
    public String getAgentGroup ( HttpServletRequest request )
    {
        List arr = new ArrayList();
        AgentGroupService service = new AgentGroupService();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            arr = service.queryMenuStore(sysType);
        } catch (Exception e)
        {
            log.error("getAgentGroup is error ", e);
        }
        if (null != arr && !arr.isEmpty())
        {
            request.setAttribute("arr", arr);
        } else
        {
            request.setAttribute("arr", null);
        }
        return "agentGroup/agentGroup";
    }

    @RequestMapping("addAgentInfo.do")
    public String addAgentInfo ( HttpServletRequest request, long groupId )
    {

        return "agentGroup/agentQuery";
    }

    /**
     * 
     * <li>Description:Agent组配置查询</li> 
     * <AUTHOR>
     * 2017年11月16日 
     * @param groupName
     * @param start
     * @param limit
     * @return
     * return Object
     */
    @RequestMapping("getAgentGroupList.do")
    @ResponseBody
    public Object getAgentGroupList ( String groupName, int start, int limit, String sort )
    {
        Object resp = null;
        AgentGroupService service = new AgentGroupService();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.getAgentGroupList(groupName, start, limit, sysType, sort);
        } catch (Exception e)
        {
            log.error("getAgentGroupList is error ", e);
        }
        return resp;
    }

    /**
     * 
     * <li>Description:Agent组保存</li> 
     * <AUTHOR>
     * 2017年11月16日 
     * @param jsonData
     * @return
     * return Object
     */
    @RequestMapping("saveAgentGroup.do")
    @ResponseBody
    public Object saveAgentGroup ( String jsonData, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            List<AgentGroupData> groupList = new ArrayList<AgentGroupData>();
            groupList = json2Beans(jsonData);
            resp = service.saveAgentGroup(groupList);
            log.info("用户名：" + userName + ",操作:Agent组保存。");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:Agent组删除</li> 
     * <AUTHOR>
     * 2017年11月17日 
     * @param iids
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("deleteAgentGroup.do")
    @ResponseBody
    public Object deleteAgentGroup ( long[] ids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            resp = service.deleteAgentGroup(ids);
            log.info("用户名：" + userName + ",操作:Agent组删除。");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:Agent绑定信息查询</li> 
     * <AUTHOR>
     * 2017年11月20日 
     * @param agentIp
     * @param groupId
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("agentqueryList.do")
    @ResponseBody
    public Object getAgentQueryList ( String agentIp, long groupId, HttpServletRequest request )
    {
        Object resp = null;
        AgentGroupService service = new AgentGroupService();
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            resp = service.getAgentQueryList(groupId, agentIp, sysType);
        } catch (Exception e)
        {
            log.error("getAgentQueryList is error ", e);
        }
        return resp;
    }

    /**
     * 
     * <li>Description:绑定Agent信息</li> 
     * <AUTHOR>
     * 2017年11月20日 
     * @param request
     * @param response
     * @param ruleIds
     * @param groupId
     * @throws Exception
     * return void
     */
    @RequestMapping("bindAgentList.do")
    public void bindAgentList ( HttpServletRequest request, HttpServletResponse response, String ruleIds, long groupId )
            throws Exception
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Agent> agentList = new ArrayList<Agent>();
        agentList = json(ruleIds);
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            AgentGroupService sms = new AgentGroupService();
            map = sms.bindAgentList(agentList, groupId);
            log.info("用户名：" + userName + ",操作:Agent组配置绑定。");

        } catch (Exception e1)
        {
            map.put(SUCCESS, false);
            map.put("msg", e1.getMessage());
        }
        response.setContentType("text/html;charset=UTF-8");
        try
        {
            response.getWriter().write("{'success':" + map.get(SUCCESS) + ",'msg':'" + map.get("msg") + "'}");
        } catch (IOException e)
        {
            log.error("bindAgentList is error ", e);
        }
    }

    /**
     * 
     * <li>Description:查询agent信息</li> 
     * <AUTHOR>
     * 2017年11月21日 
     * @param groupId
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("getGroupInfoList.do")
    @ResponseBody
    public Object getGroupInfoList ( long groupId, int typeId, int start, int limit, HttpServletRequest request )
    {
        Object resp = null;
        AgentGroupService service = new AgentGroupService();
        try
        {
            resp = service.getGroupInfoList(groupId, start, limit, typeId);
        } catch (Exception e)
        {
            log.error("getGroupInfoList is error ", e);
        }
        return resp;
    }

    /**
     * 
     * <li>Description:Agent信息保存</li> 
     * <AUTHOR>
     * 2017年11月22日 
     * @param jsonData
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("saveAgentGroupInfo.do")
    @ResponseBody
    public Object saveAgentGroupInfo ( String jsonData, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            List<AgentGroupInfo> groupInfoList = new ArrayList<AgentGroupInfo>();
            groupInfoList = json2Info(jsonData);
            resp = service.saveAgentGroupInfo(groupInfoList);
            log.info("用户名：" + userName + ",操作:Agent信息保存。");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:Agent信息删除</li> 
     * <AUTHOR>
     * 2017年11月21日 
     * @param iids
     * @param request
     * @return
     * return Object
     */
    @RequestMapping("deleteGroupInfo.do")
    @ResponseBody
    public Object deleteGroupInfo ( long[] iids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            resp = service.deleteGroupInfo(iids, Constants.IEAI_EMERGENCY_SWITCH);
            log.info("用户名：" + userName + ",操作:Agent信息删除。");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:查询模块类别</li> 
     * <AUTHOR>
     * 2017年12月8日 
     * @return
     * @throws Exception
     * return Map
     */
    @RequestMapping("queryMenuStore.do")
    @ResponseBody
    public Map<String, Object> queryMenuStore () throws Exception
    {
        Map<String, Object> map = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        int total = 0;
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            list = service.queryMenuStore(sysType);
            if (!list.isEmpty())
            {
                map.put("list", list);
            } else
            {
                list = new ArrayList<Map<String, Object>>();
                map.put("list", list);
            }
            map.put(SUCCESS, true);
            map.put(MESSAGE, "查询成功");
        } catch (Exception e)
        {
            map.put("total", total);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "查询失败");
            log.error("queryMenuStore is error ", e);
        }
        return map;
    }

    private List<AgentGroupInfo> json2Info ( String jsonData )
    {
        List<AgentGroupInfo> list = new ArrayList<AgentGroupInfo>();
        JSONArray jsonArr;
        JSONObject json2 = null;
        try
        {
            jsonArr = new JSONArray(jsonData);
            for (int i = 0; i < jsonArr.length(); i++)
            {
                json2 = jsonArr.getJSONObject(i);
                long cpuLimit = 0;
                long memoryLimit = 0;
                try {
                    cpuLimit = json2.getLong("cpuLimit");
                    memoryLimit = json2.getLong("memoryLimit");
                } catch (JSONException ignored) {
                }
                AgentGroupInfo bean = new AgentGroupInfo(json2.getLong("iid"), json2.getLong("inum"),
                        json2.getLong("iorder"),cpuLimit,memoryLimit,
                        json2.getString("cpuUsage"),json2.getString("memoryUsage"));
                list.add(bean);
            }
        } catch (JSONException jsonexception)
        {
            log.error("json2Info is error ", jsonexception);
        }
        return list;
    }

    private List<Agent> json ( String ruleIds )
    {
        List<Agent> list = new ArrayList<Agent>();
        JSONArray jsonArr;
        JSONObject json2 = null;
        try
        {
            jsonArr = new JSONArray(ruleIds);
            for (int i = 0; i < jsonArr.length(); i++)
            {
                json2 = jsonArr.getJSONObject(i);
                Agent bean = new Agent(json2.getLong("iid"), json2.getString("iagentip"), json2.getLong("iagentport"));
                list.add(bean);
            }
        } catch (JSONException jsonexception)
        {
            log.error("json is error ", jsonexception);
        }
        return list;
    }

    private List<AgentGroupData> json2Beans ( String jsonData )
    {
        List<AgentGroupData> list = new ArrayList<AgentGroupData>();
        JSONArray jsonArr;
        JSONObject json2 = null;
        try
        {
            jsonArr = new JSONArray(jsonData);
            for (int i = 0; i < jsonArr.length(); i++)
            {
                json2 = jsonArr.getJSONObject(i);
                AgentGroupData bean = new AgentGroupData(json2.getString("iid"), json2.getString("iname"),
                        json2.getString("idesc"), json2.getString("igrouptype"), json2.getBoolean("iisapt") ? 1 : 0,
                        json2.getString("iaptpath"), json2.getString("icpu"), json2.getString("iio"),
                        json2.getString("iloadway"));
                if ("0".equals(bean.getIid()))
                {
                    bean.setIid(null);
                }
                list.add(bean);
            }
        } catch (JSONException jsonexception)
        {
            log.error("json2Beans is error ", jsonexception);
        }
        return list;
    }

    @RequestMapping("updateRecoverActStatus.do")
    @ResponseBody
    public Map<String, Object> updateRecoverActStatus ( HttpServletRequest request )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        // 获取客户端IP
        String ip = request.getParameter("serverIp");
        log.info("本次请求客户端：" + ip);
        try
        {
            service.updateRecoverActStatus(request, ip);
            map.put(Constants.STR_SUCCESS, true);
        } catch (Exception e)
        {
            log.error("updateRecoverActStatus error", e);
            map.put(Constants.STR_SUCCESS, false);
        }
        return map;
    }

    /** 
     * 获取用户真实IP地址，不使用request.getRemoteAddr()的原因是有可能用户使用了代理软件方式避免真实IP地址, 
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值 
     */
    @SuppressWarnings("unused")
    private String getIpAddr ( HttpServletRequest request )
    {
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip) && ip.indexOf(",") != -1)
        {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            ip = ip.split(",")[0];
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
        {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


    @RequestMapping("deleteGroupInfoConcurrency.do")
    @ResponseBody
    public Object deleteGroupInfoConcurrency ( long[] iids, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();
        try
        {
            resp = service.deleteGroupInfoConcurrency(iids, Constants.IEAI_EMERGENCY_SWITCH,userName);
            //log.info("用户名：" + userName + ",操作:按钮操作agent组实时并发数清零");
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

 
     /***
     *
     * <li>校验agent并发数:</li>
     * <AUTHOR>
     * 2022年3月7日
     * @param iids
     * @param
     * @return
     * @throws Exception
     * return boolean
     */
    @RequestMapping("sendHttpForGetActRunNum.do")
    @ResponseBody
    public Object sysTance ( Long[] iids )
    {
        AgentGroupService service = new AgentGroupService();
        Map<String, Object> resp = new HashMap<String, Object>();

        try
        {
            // 根据选择查询列表中IID 删除
            boolean isOK = service.sendHttpForGetActRunNum(iids);
            // 根据执行结果，返回页面信息
            if (isOK)
            {
                resp.put(Constants.STR_SUCCESS, true);
                resp.put(Constants.STR_MESSAGE, "校验成功!");
            } else
            {
                resp.put(Constants.STR_SUCCESS, false);
                resp.put(Constants.STR_MESSAGE, "校验失败!");
            }
        } catch (Exception e)
        {
            resp.put(Constants.STR_SUCCESS, false);
            resp.put(Constants.STR_MESSAGE, e.getMessage());
        }
        return resp;
    }

    /**
     * Excel 导入 AgentGroup 数据
     * @param file 上传的 Excel 文件
     * @param request HttpServletRequest 对象
     * @return 返回操作结果
     */
    @RequestMapping("importAgentGroup.do")
    @ResponseBody
    public Object importAgentGroup (MultipartFile file, HttpServletRequest request )
    {
        Map<String, Object> resp = new HashMap<>();
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();

        try
        {
            String originalFilename = file.getOriginalFilename();
            if(originalFilename.lastIndexOf(".zip") == -1 ){
                throw new Exception("导入失败，文件格式必须为 ZIP！");
            }

            File tempDir = Files.createTempDirectory("agentGroupImport").toFile();
            File zipFile = new File(tempDir, file.getOriginalFilename());
            // 保存上传的 ZIP 文件
            file.transferTo(zipFile);
            // 解压 ZIP
            List<File> excelFiles = new ArrayList<>();
            unzipFile(zipFile, tempDir, excelFiles);

            // 遍历所有 Excel 文件
            for (File excelFile : excelFiles) {
                // 读取第一个 sheet: AgentGroupData
                List<AgentGroupData> groupList = EasyExcel.read(excelFile)
                        .head(AgentGroupData.class)
                        .sheet(0)
                        .doReadSync();

                // 读取第二个 sheet: AgentExportData
                List<Agent> agentList = EasyExcel.read(excelFile)
                        .head(Agent.class)
                        .sheet(1)
                        .doReadSync();

                // 处理 AgentGroupData（如校验、转换）
                if (!groupList.isEmpty()) {
                    service.getAgentGroupId(groupList);
                    resp = service.saveAgentGroup(groupList); // 保存组
                }

                for(AgentGroupData agentGroupData : groupList){
                    // 处理 AgentExportData 并绑定到对应组
                    if (!agentList.isEmpty()) {
                        List<Agent> insertList = new ArrayList<>();
                        Long groupId = Long.parseLong(agentGroupData.getIid());
                        // 假设根据 groupList 第一项获取 groupId，实际可按业务逻辑调整
                        service.bindAgentList(insertList, Long.parseLong(agentGroupData.getIid())); // 绑定 agent 到组
                        List<AgentGroupInfo> updateList = new ArrayList<>();
                        for(int i=0;i<agentList.size();i++){
                            List agentInfoList = (List)service.getAgentQueryList(groupId, agentList.get(i).getIagentip(),
                                    Constants.IEAI_IEAI_BASIC).get("dataList");
                            if(agentInfoList.size()<=0){
                                AgentGroupInfo groupInfo = new AgentGroupInfo();
                                groupInfo.setIid(service.getAgentInfoGroupId(groupId,service.getAgentInfoId(agentList.get(i).getIagentip())));
                                groupInfo.setIgroupid(groupId);
                                groupInfo.setInum(agentList.get(i).getInum());
                                groupInfo.setIorder(agentList.get(i).getIorder());
                                updateList.add(groupInfo);
                                continue;
                            }
                            Map agentInfoMap =  (Map)agentInfoList.get(0);
                            if(agentInfoMap.size()==0){
                                AgentGroupInfo groupInfo = new AgentGroupInfo();
                                groupInfo.setIid(service.getAgentInfoGroupId(groupId,service.getAgentInfoId(agentList.get(i).getIagentip())));
                                groupInfo.setIgroupid(groupId);
                                groupInfo.setInum(agentList.get(i).getInum());
                                groupInfo.setIorder(agentList.get(i).getIorder());
                                updateList.add(groupInfo);
                                continue;
                            }
                            if((Long)agentInfoMap.get("iid")<=0){
                                AgentGroupInfo groupInfo = new AgentGroupInfo();
                                groupInfo.setIid(service.getAgentInfoGroupId(groupId,(Long)agentInfoMap.get("iid")));
                                groupInfo.setIgroupid(groupId);
                                groupInfo.setInum(agentList.get(i).getInum());
                                groupInfo.setIorder(agentList.get(i).getIorder());
                                updateList.add(groupInfo);
                            }else{
                                agentList.get(i).setIid((Long)agentInfoMap.get("iid"));
                                insertList.add(agentList.get(i));
                                AgentGroupInfo groupInfo = new AgentGroupInfo();
                                groupInfo.setInodeid(service.getAgentInfoId(agentList.get(i).getIagentip()));
                                groupInfo.setIgroupid(groupId);
                                groupInfo.setInum(agentList.get(i).getInum());
                                groupInfo.setIorder(agentList.get(i).getIorder());
                                updateList.add(groupInfo);
                            }
                        }
                        if(insertList.size()>0){
                            resp = service.bindAgentList(insertList, groupId);
                        }
                        if(updateList.size()>0){
                            for(int i=0;i<updateList.size();i++){
                                if (updateList.get(i).getIid()<=0) {
                                    updateList.get(i).setIid(service.getAgentInfoGroupId(groupId,updateList.get(i).getInodeid()));
                                }
                            }
                            resp = service.saveAgentGroupInfo(updateList);
                        }

                    }
                }
            }

            // 调用服务层保存数据
            //resp = service.saveAgentGroup(groupList);
            log.info("用户：" + userName + ", 操作：导入 AgentGroup 数据成功");
        } catch (Exception e)
        {
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, e.getMessage());
            log.error("importAgentGroup is error ", e);
        }

        return resp;
    }

    /**
     * Excel 导入 AgentGroup的Agent 数据
     * @param file 上传的 Excel 文件
     * @param request HttpServletRequest 对象
     * @return 返回操作结果
     */
    @RequestMapping("importAgentGroupAgent.do")
    @ResponseBody
    public Object importAgentGroupAgent (MultipartFile file, HttpServletRequest request, long groupId )
    {
        Map<String, Object> resp = new HashMap<>();
        String userName = SessionData.getSessionData(request).getUserName();

        try
        {
            AgentGroupService service = new AgentGroupService();
            String originalFilename = file.getOriginalFilename();
            if(originalFilename.lastIndexOf(".xls") == -1 && originalFilename.lastIndexOf(".XLS") == -1){
                throw new Exception("导入失败，文件不是excel格式!");
            }

            // 使用 EasyExcel 解析 Excel 文件
            List<Agent> agentList = EasyExcel.read(file.getInputStream())
                    .registerReadListener(new AgentGroupAgentListener())
                    .head(Agent.class)
                    .sheet()
                    .doReadSync();

            List<Agent> insertList = new ArrayList<>();
            List<AgentGroupInfo> updateList = new ArrayList<>();
            for(int i=0;i<agentList.size();i++){
                List agentInfoList = (List)service.getAgentQueryList(groupId, agentList.get(i).getIagentip(),
                        Constants.IEAI_IEAI_BASIC).get("dataList");
                if(agentInfoList.size()<=0){
                    AgentGroupInfo groupInfo = new AgentGroupInfo();
                    groupInfo.setIid(service.getAgentInfoGroupId(groupId,service.getAgentInfoId(agentList.get(i).getIagentip())));
                    groupInfo.setIgroupid(groupId);
                    groupInfo.setInum(agentList.get(i).getInum());
                    groupInfo.setIorder(agentList.get(i).getIorder());
                    updateList.add(groupInfo);
                    continue;
                }
                Map agentInfoMap =  (Map)agentInfoList.get(0);
                if(agentInfoMap.size()==0){
                    AgentGroupInfo groupInfo = new AgentGroupInfo();
                    groupInfo.setIid(service.getAgentInfoGroupId(groupId,service.getAgentInfoId(agentList.get(i).getIagentip())));
                    groupInfo.setIgroupid(groupId);
                    groupInfo.setInum(agentList.get(i).getInum());
                    groupInfo.setIorder(agentList.get(i).getIorder());
                    updateList.add(groupInfo);
                    continue;
                }
                if((Long)agentInfoMap.get("iid")<=0){
                    AgentGroupInfo groupInfo = new AgentGroupInfo();
                    groupInfo.setIid(service.getAgentInfoGroupId(groupId,(Long)agentInfoMap.get("iid")));
                    groupInfo.setIgroupid(groupId);
                    groupInfo.setInum(agentList.get(i).getInum());
                    groupInfo.setIorder(agentList.get(i).getIorder());
                    updateList.add(groupInfo);
                }else{
                    agentList.get(i).setIid((Long)agentInfoMap.get("iid"));
                    insertList.add(agentList.get(i));
                    AgentGroupInfo groupInfo = new AgentGroupInfo();
                    groupInfo.setInodeid(service.getAgentInfoId(agentList.get(i).getIagentip()));
                    groupInfo.setIgroupid(groupId);
                    groupInfo.setInum(agentList.get(i).getInum());
                    groupInfo.setIorder(agentList.get(i).getIorder());
                    updateList.add(groupInfo);
                }
            }
            if(insertList.size()>0){
                resp = service.bindAgentList(insertList, groupId);
            }
            if(updateList.size()>0){
                for(int i=0;i<updateList.size();i++){
                    if (updateList.get(i).getIid()<=0) {
                        updateList.get(i).setIid(service.getAgentInfoGroupId(groupId,updateList.get(i).getInodeid()));
                    }
                }
                resp = service.saveAgentGroupInfo(updateList);
            }
            log.info("用户：" + userName + ", 操作：导入 AgentGroup.Agent 数据成功");
        } catch (Exception e)
        {
            resp.put(SUCCESS, true);
            resp.put(MESSAGE, e.getMessage());
            log.error("importAgentGroup.Agent is error ", e);
        }

        return resp;
    }

    /**
     * Excel  AgentGroup
     * @param request HttpServletRequest
     * @param response HttpServletResponse
     * @return ?
     */
    @RequestMapping("exportAgentGroup.do")
    public void exportAgentGroup(HttpServletRequest request, HttpServletResponse response, String agentGroupNames) {
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();

        try (OutputStream out = response.getOutputStream()) {
            // 查询要导出的数据
            List<AgentGroupExportData> groupList = new ArrayList<>();
            Map<AgentGroupExportData,List<AgentExportData>> agentGroupInfoMap = new HashMap<>();
            File tempDir = null;
            try
            {
                tempDir = Files.createTempDirectory("agentGroupExports").toFile();
                int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
                String[] agentGroupNamesArr = agentGroupNames.split(",");
                if(StringUtils.isBlank(agentGroupNames)){
                    List<AgentGroupExportData> groupList1 = new ArrayList<>();
                    service.organizeAgentGroupData(groupList1,"",sysType);
                    for(int i=0;i<groupList1.size();i++){
                        //根据组id获取组下agent  组织成 map<组,组下agent>
                        /*List agentInfoList = (List)service.getAgentQueryList(Long.parseLong(
                                        groupList1.get(i).getIid()), "",
                                Constants.IEAI_IEAI_BASIC).get("dataList");*/

                        List<AgentExportData> agentList = new ArrayList<>();
                        Map resp = service.getGroupInfoList(Long.parseLong(
                                groupList1.get(i).getIid()), 0, 10000, sysType);
                        if(resp.size()>0){
                            List dataList = (List) resp.get("dataList");
                            for (int t = 0; t < dataList.size(); t++) {
                                Map<String,String> map = (Map) dataList.get(t);
                                AgentExportData agentExportData = new AgentExportData();
                                agentExportData.setIagentip(map.get("iagentip"));
                                agentExportData.setIagentport(String.valueOf(map.get("iagentport")));
                                agentExportData.setInum(map.get("inum"));
                                agentExportData.setInowNum(map.get("inownum"));
                                agentExportData.setIorder(map.get("iorder"));
                                agentList.add(agentExportData);
                            }
                        }

                        agentGroupInfoMap.put(groupList1.get(i),agentList);
                    }
                }else{
                    if(agentGroupNamesArr.length>1){
                        for(String agentGroupName:agentGroupNamesArr){
                            List<AgentGroupExportData> groupList1 = new ArrayList<>();
                            service.organizeAgentGroupData(groupList1,agentGroupName,sysType);
                            //根据组id获取组下agent  组织成 map<组,组下agent>
                            /*List agentInfoList = (List)service.getAgentQueryList(Long.parseLong(
                                            groupList1.get(0).getIid()), "",
                                    Constants.IEAI_IEAI_BASIC).get("dataList");*/
                            List<AgentExportData> agentList = new ArrayList<>();
                            Map resp = service.getGroupInfoList(Long.parseLong(
                                    groupList1.get(0).getIid()), 0, 10000, sysType);
                            if(resp.size()>0){
                                List dataList = (List) resp.get("dataList");
                                for (int t = 0; t < dataList.size(); t++) {
                                    Map<String,String> map = (Map) dataList.get(t);
                                    AgentExportData agentExportData = new AgentExportData();
                                    agentExportData.setIagentip(map.get("iagentip"));
                                    agentExportData.setIagentport(String.valueOf(map.get("iagentport")));
                                    agentExportData.setInum(map.get("inum"));
                                    agentExportData.setInowNum(map.get("inownum"));
                                    agentExportData.setIorder(map.get("iorder"));
                                    agentList.add(agentExportData);
                                }
                            }
                            agentGroupInfoMap.put(groupList1.get(0),agentList);
                        }
                    }else{
                        service.organizeAgentGroupData(groupList,agentGroupNames,sysType);
                        List<AgentExportData> agentList = new ArrayList<>();
                        Map resp = service.getGroupInfoList(Long.parseLong(
                                groupList.get(0).getIid()), 0, 10000, sysType);
                        if(resp.size()>0){
                            List dataList = (List) resp.get("dataList");
                            for (int t = 0; t < dataList.size(); t++) {
                                Map<String,String> map = (Map) dataList.get(t);
                                AgentExportData agentExportData = new AgentExportData();
                                agentExportData.setIagentip(map.get("iagentip"));
                                agentExportData.setIagentport(String.valueOf(map.get("iagentport")));
                                agentExportData.setInum(map.get("inum"));
                                agentExportData.setInowNum(map.get("inownum"));
                                agentExportData.setIorder(map.get("iorder"));
                                agentList.add(agentExportData);
                            }
                        }

                        agentGroupInfoMap.put(groupList.get(0),agentList);
                    }
                }
            } catch (Exception e)
            {
                log.error("getAgentGroupList is error ", e);
            }

            if (agentGroupInfoMap.size()>1) {
                List<File> excelFiles = new ArrayList<>();

                for (Map.Entry<AgentGroupExportData, List<AgentExportData>> entry : agentGroupInfoMap.entrySet()) {
                    AgentGroupExportData key = entry.getKey();
                    List<AgentExportData> value = entry.getValue();

                    String fileName = "AgentGroup_" + key.getIname() + "_" + System.currentTimeMillis() + ".xlsx";
                    File excelFile = new File(tempDir, fileName);

                    try (OutputStream os = new FileOutputStream(excelFile)) {
                        ExcelWriter excelWriter = EasyExcel.write(os).excelType(ExcelTypeEnum.XLSX).build();
                        // 写入第一个 sheet: key 数据
                        WriteSheet sheet1 = EasyExcel.writerSheet(key.getIname()).head(AgentGroupExportData.class).build();
                        excelWriter.write(Collections.singletonList(key), sheet1);

                        WriteSheet sheet2 = EasyExcel.writerSheet("Agents").head(AgentExportData.class).build();
                        excelWriter.write(value, sheet2);
                        excelWriter.finish();
                    }
                    excelFiles.add(excelFile);
                }

                // 打包为 zip
                File zipFile = new File(tempDir.getParentFile(), "AgentGroups_Export_" + System.currentTimeMillis() + ".zip");
                zipFiles(excelFiles, zipFile);

                // 返回 zip 下载
                response.setContentType("application/zip");
                response.setCharacterEncoding("utf-8");
                response.setHeader("Content-Disposition", "attachment; filename=" + zipFile.getName());

                try (InputStream is = new FileInputStream(zipFile);
                     OutputStream os = response.getOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = is.read(buffer)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                }

                log.info("用户：" + userName + " 成功导出并打包多个 AgentGroup Excel 文件");

            } else {


                for (Map.Entry<AgentGroupExportData, List<AgentExportData>> entry : agentGroupInfoMap.entrySet()) {
                    AgentGroupExportData key = entry.getKey();
                    List<AgentExportData> value = entry.getValue();

                    String fileName = "AgentGroup_" + key.getIname() + "_" + System.currentTimeMillis() + ".xlsx";
                    File excelFile = new File(tempDir, fileName);

                    try (OutputStream os = new FileOutputStream(excelFile)) {
                        ExcelWriter excelWriter = EasyExcel.write(os).excelType(ExcelTypeEnum.XLSX).build();
                        // 写入第一个 sheet: key 数据
                        WriteSheet sheet1 = EasyExcel.writerSheet(key.getIname()).head(AgentGroupExportData.class).build();
                        excelWriter.write(Collections.singletonList(key), sheet1);

                        WriteSheet sheet2 = EasyExcel.writerSheet("Agents").head(AgentExportData.class).build();
                        excelWriter.write(value, sheet2);
                        excelWriter.finish();

                    }
                    // 设置响应头，告诉浏览器这是一个 Excel 文件下载
                    response.setContentType("application/x-download");
                    response.setCharacterEncoding("utf-8");
                    response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1"));
                    try (InputStream is = new FileInputStream(excelFile);
                         OutputStream os = response.getOutputStream()) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = is.read(buffer)) != -1) {
                            os.write(buffer, 0, bytesRead);
                        }
                    }
                }
                out.flush();
            }

            log.info("用户：" + userName + "，成功导出 AgentGroup 数据");
        } catch (Exception e) {
            log.error("exportAgentGroup 发生错误 ", e);
            try {
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }

    private Class<?> resolveHeadClass(List<?> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return Object.class;
        }

        Object firstItem = dataList.get(0);
        if (firstItem instanceof AgentExportData) {
            return AgentExportData.class;
        }

        return Object.class;
    }

    private void zipFiles(List<File> filesToZip, File zipFile) throws IOException {
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (File file : filesToZip) {
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] bytes = new byte[1024];
                    int length;
                    while ((length = fis.read(bytes)) >= 0) {
                        zos.write(bytes, 0, length);
                    }
                }

                zos.closeEntry();
            }
        }
    }

    /*private void deleteDirectory(File directory) {
        if (directory != null && directory.exists()) {
            Arrays.stream(Objects.requireNonNull(directory.listFiles()))
                    .forEach(f -> f.isDirectory() ? deleteDirectory(f) : f.delete());
            directory.delete();
        }
    }*/

    @RequestMapping("exportAgentGroupAgent.do")
    public void exportAgentGroupAgent ( long groupId, HttpServletRequest request, HttpServletResponse response )
    {
        AgentGroupService service = new AgentGroupService();
        String userName = SessionData.getSessionData(request).getUserName();

        try (OutputStream out = response.getOutputStream()) {
            // 查询要导出的数据
            List<AgentExportData> groupList = new ArrayList<>();
            try
            {
                int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
                Map resp = service.getGroupInfoList(groupId, 0, 10000, sysType);
                if(resp.size()>0){
                    List dataList = (List) resp.get("dataList");
                    for (int i = 0; i < dataList.size(); i++) {
                        Map<String,String> map = (Map) dataList.get(i);
                        AgentExportData agentExportData = new AgentExportData();
                        agentExportData.setIagentip(map.get("iagentip"));
                        agentExportData.setIagentport(String.valueOf(map.get("iagentport")));
                        agentExportData.setInum(map.get("inum"));
                        agentExportData.setInowNum(map.get("inownum"));
                        agentExportData.setIorder(map.get("iorder"));
                        groupList.add(agentExportData);
                    }
                }
            } catch (Exception e)
            {
                log.error("getAgentGroupList is error ", e);
            }
            // 设置响应头，告诉浏览器这是一个 Excel 文件下载
            response.setContentType("application/x-download");
            response.setCharacterEncoding("utf-8");
            String fileName = "AgentGroupBindAgent_Export_" + System.currentTimeMillis() + ".xls";
            response.setHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes("GB2312"), "ISO8859-1"));

            // 使用 EasyExcel 将数据写入 Excel
            EasyExcel.write(out)
                    .head(AgentExportData.class)
                    .autoCloseStream(true)
                    .excelType(ExcelTypeEnum.XLS)
                    .sheet("Sheet1")
                    .doWrite(groupList);
            out.flush();
            log.info("用户：" + userName + "，成功导出 AgentGroupBindAgent 数据");
        } catch (Exception e) {
            log.error("exportAgentGroupAgent 发生错误 ", e);
            try {
                response.getWriter().write("导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }

    private void unzipFile(File zipFile, File destDir, List<File> extractedExcelFiles) throws IOException {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File entryFile = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    entryFile.mkdirs();
                } else {
                    entryFile.getParentFile().mkdirs();
                    try (OutputStream os = new FileOutputStream(entryFile)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            os.write(buffer, 0, len);
                        }
                    }
                    // 如果是 Excel 文件，则加入列表
                    if (entry.getName().endsWith(".xlsx") || entry.getName().endsWith(".xls")) {
                        extractedExcelFiles.add(entryFile);
                    }
                }
            }
        }
    }

}
