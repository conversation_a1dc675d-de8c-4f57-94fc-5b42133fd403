package com.ideal.ieai.server.repository.topo.taskImport;

public class YaccBicModel
{
    private long iid;
    private long imainid;
    private String istage;
    private long istep_coner;
    private String istep_name;
    private long ipre_step_coner;
    private long istep_g_conner;
    private String istep_g_connertime;
    private long itime;
    private int istatus;
    private String istartOrendTime;
    private String istarttime;
    private long iendtime;
    private long irealstarttime;
    private long irealendtime;
    private String ioperuser;
    private String ireviewuser;
    private String ipartment;
    private String isieai;//启动条件
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public long getImainid ()
    {
        return imainid;
    }
    public void setImainid ( long imainid )
    {
        this.imainid = imainid;
    }
    public String getIstage ()
    {
        return istage;
    }
    public void setIstage ( String istage )
    {
        this.istage = istage;
    }
    public long getIstep_coner ()
    {
        return istep_coner;
    }
    public void setIstep_coner ( long istep_coner )
    {
        this.istep_coner = istep_coner;
    }
    public String getIstep_name ()
    {
        return istep_name;
    }
    public void setIstep_name ( String istep_name )
    {
        this.istep_name = istep_name;
    }
    public long getIpre_step_coner ()
    {
        return ipre_step_coner;
    }
    public void setIpre_step_coner ( long ipre_step_coner )
    {
        this.ipre_step_coner = ipre_step_coner;
    }
    public long getIstep_g_conner ()
    {
        return istep_g_conner;
    }
    public void setIstep_g_conner ( long istep_g_conner )
    {
        this.istep_g_conner = istep_g_conner;
    }
    public String getIstep_g_connertime ()
    {
        return istep_g_connertime;
    }
    public void setIstep_g_connertime ( String istep_g_connertime )
    {
        this.istep_g_connertime = istep_g_connertime;
    }
    public long getItime ()
    {
        return itime;
    }
    public void setItime ( long itime )
    {
        this.itime = itime;
    }
    public int getIstatus ()
    {
        return istatus;
    }
    public void setIstatus ( int istatus )
    {
        this.istatus = istatus;
    }
    
    public String getIstartOrendTime ()
    {
        return istartOrendTime;
    }
    public void setIstartOrendTime ( String istartOrendTime )
    {
        this.istartOrendTime = istartOrendTime;
    }
    
    public String getIstarttime ()
    {
        return istarttime;
    }
    public void setIstarttime ( String istarttime )
    {
        this.istarttime = istarttime;
    }
    public long getIendtime ()
    {
        return iendtime;
    }
    public void setIendtime ( long iendtime )
    {
        this.iendtime = iendtime;
    }
    public long getIrealstarttime ()
    {
        return irealstarttime;
    }
    public void setIrealstarttime ( long irealstarttime )
    {
        this.irealstarttime = irealstarttime;
    }
    public long getIrealendtime ()
    {
        return irealendtime;
    }
    public void setIrealendtime ( long irealendtime )
    {
        this.irealendtime = irealendtime;
    }
    public String getIoperuser ()
    {
        return ioperuser;
    }
    public void setIoperuser ( String ioperuser )
    {
        this.ioperuser = ioperuser;
    }
    public String getIreviewuser ()
    {
        return ireviewuser;
    }
    public void setIreviewuser ( String ireviewuser )
    {
        this.ireviewuser = ireviewuser;
    }
    public String getIpartment ()
    {
        return ipartment;
    }
    public void setIpartment ( String ipartment )
    {
        this.ipartment = ipartment;
    }
    public String getIsieai ()
    {
        return isieai;
    }
    public void setIsieai ( String isieai )
    {
        this.isieai = isieai;
    }
}
