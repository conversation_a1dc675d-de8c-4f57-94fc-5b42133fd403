package com.ideal.service.jobscheduling.common;

import java.util.List;

import com.ideal.ieai.server.jobscheduling.common.ProjectInfoManager;

public class ProjectInfoService {

	
	 
	
    public List getProjectInfo ( Long userId )
    {
	    return ProjectInfoManager.getInstance().getProjectInfo(userId);
    }
	
    public List getFlowInfo ( Long prjId )
    {
        return ProjectInfoManager.getInstance().getFlowInfo(prjId);
    }

    public List getFlowInfoByNew ( String prjName )
    {
        return ProjectInfoManager.getInstance().getFlowInfoByNew(prjName);
    }

    public List getActInfo ( String prjName )
    {
        return ProjectInfoManager.getInstance().getActInfo(prjName);
    }
	
    public List getActInfoWithNull ( String prjName )
    {
        return ProjectInfoManager.getInstance().getActInfoWithNull(prjName);
    }
	
    public List getActInfoDouble ( String prjNames )
    {
        return ProjectInfoManager.getInstance().getActInfoDouble(prjNames);
    }
     
}
