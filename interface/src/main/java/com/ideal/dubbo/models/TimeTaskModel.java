package com.ideal.dubbo.models;

import java.io.Serializable;

public class TimeTaskModel  implements Serializable
{
    private long   id;
    private long   taskTime;
    private long   serviceId;
    private long   isTimeTask;
    private long   workItemId;

    private String cronTime   = "";
    private String planTime   = "";
    private String excelModel = "";

    private long   istate;

    private String serviceIdNum;   // 服务id号

    public long getIstate ()
    {
        return istate;
    }

    public void setIstate ( long istate )
    {
        this.istate = istate;
    }

    public String getExcelModel ()
    {
        return excelModel;
    }

    public void setExcelModel ( String excelModel )
    {
        this.excelModel = excelModel;
    }

    public long getWorkItemId ()
    {
        return workItemId;
    }

    public void setWorkItemId ( long workItemId )
    {
        this.workItemId = workItemId;
    }

    public String getCronTime ()
    {
        return cronTime;
    }

    public void setCronTime ( String cronTime )
    {
        this.cronTime = cronTime;
    }

    public String getPlanTime ()
    {
        return planTime;
    }

    public void setPlanTime ( String planTime )
    {
        this.planTime = planTime;
    }

    public long getServiceId ()
    {
        return serviceId;
    }

    public void setServiceId ( long serviceId )
    {
        this.serviceId = serviceId;
    }

    public long getId ()
    {
        return id;
    }

    public void setId ( long id )
    {
        this.id = id;
    }

    public long getTaskTime ()
    {
        return taskTime;
    }

    public void setTaskTime ( long taskTime )
    {
        this.taskTime = taskTime;
    }

    public long getIsTimeTask ()
    {
        return isTimeTask;
    }

    public void setIsTimeTask ( long isTimeTask )
    {
        this.isTimeTask = isTimeTask;
    }

    public String getServiceIdNum ()
    {
        return serviceIdNum;
    }

    public void setServiceIdNum ( String serviceIdNum )
    {
        this.serviceIdNum = serviceIdNum;
    }

}
