	-- 4.7.17 version does not have patches for compare
	-- 4.7.18 version does not have patches for compare
	-- 4.7.19 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN	

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPARE_CLASS' AND COLUMN_NAME = 'ICLASSTYPE') THEN
			ALTER TABLE IEAI_COMPARE_CLASS ADD ICLASSTYPE NUMERIC(1) ;
			UPDATE IEAI_COMPARE_CLASS SET ICLASSTYPE=0;
			COMMIT;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPARE_PRO_AUTO_START' AND COLUMN_NAME = 'ISTARTTIMECRON') THEN
			ALTER TABLE IEAI_COMPARE_PRO_AUTO_START ADD ISTARTTIMECRON VARCHAR(50) ;
			COMMIT;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_COMPARE_SWITCH (IID NUMERIC(19,0) NOT NULL,IPROGRAMMEID NUMERIC(19,0),ITYPE NUMERIC(1),IPROID NUMERIC(19,0),ISWITCHID NUMERIC(19,0),ICHECKNAME VARCHAR(255),CONSTRAINT PK_IEAI_COMPARE_SWITCH PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPARE_RESULT' AND COLUMN_NAME = 'ISWITCHSTATE') THEN
				ALTER TABLE IEAI_COMPARE_RESULT ADD ISWITCHSTATE NUMERIC(1) DEFAULT 0 ;
			END IF;
		

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.20 version does not have patches for compare
	-- 4.7.21 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPARE_PROGRAMME_BASE' AND COLUMN_NAME = 'IISDISABLED') THEN
			ALTER TABLE IEAI_COMPARE_PROGRAMME_BASE ADD IISDISABLED NUMERIC(1) DEFAULT 0;
		END IF;


		CREATE TABLE IF NOT EXISTS IEAI_SYNC_NODERESULT (IID NUMERIC(19) NOT NULL ,IRESULTID NUMERIC(19) ,ISYSNAME VARCHAR(255),ISOURCEIP VARCHAR(255) ,ITARGETIP VARCHAR(255) ,ISOURCENAME VARCHAR(255) ,ITARGETNAME VARCHAR(255) ,IRESULT NUMERIC(1) ,ISTATE NUMERIC(1) ,ISOURCEPORT NUMERIC(19),ITARGETPORT NUMERIC(19),CONSTRAINT PK_IEAI_SYNC_NODERESULT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_RESULT (IID NUMERIC(19) NOT NULL ,IPLANID NUMERIC(19) ,ISTATE NUMERIC(1) ,IRESULT NUMERIC(1) ,IRUNTIME NUMERIC(19) ,CONSTRAINT PK_IEAI_SYNC_SYNCRESULT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_DIRRESULT (IID NUMERIC(19) NOT NULL ,INODERESULTID NUMERIC(19) ,ITYPE NUMERIC(1) ,IPATH VARCHAR(255) ,INATCHSYNC NUMERIC(1) ,IALLSYNC NUMERIC(1) ,ISYNCSTRATEGY VARCHAR(255) ,IRESULTSTR LONGTEXT ,IRESULT NUMERIC(1) ,ISTATE NUMERIC(1) ,IFLOWID NUMERIC(19) ,ISENCRYPTION NUMERIC(1) ,CONSTRAINT PK_IEAI_SYNC_DIRRESULT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_STRATEGYPLAN (IID NUMERIC(19) NOT NULL ,IPLANNAME VARCHAR(255) ,IPLANDESC VARCHAR(255) ,ISOURCEID NUMERIC(19) ,ITARGETID NUMERIC(19) ,ICREATETIME NUMERIC(19) ,ICREATER VARCHAR(255) ,CONSTRAINT PK_IEAI_SYNC_STRATEGYPLAN PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_PLANVIEW (IID NUMERIC(19) NOT NULL ,IPLANID NUMERIC(19) ,ISYSID NUMERIC(19) ,CONSTRAINT PK_IEAI_SYNC_PLANVIEW PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_CRONTASK (IID NUMERIC(19) NOT NULL ,IPLANID NUMERIC(19) ,ICRON VARCHAR(255) ,ISTATUS NUMERIC(1) ,CONSTRAINT PK_IEAI_SYNC_CRONTASK PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_AGENTRELATION (IID NUMERIC(19) NOT NULL ,ISYSID NUMERIC(19) ,ISOURCEAGENTID NUMERIC(19) ,ITARGETAGENTID NUMERIC(19) ,ISOURCEID NUMERIC(19) ,ITARGETID NUMERIC(19) ,CONSTRAINT PK_IEAI_SYNC_AGENTRELATION PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_SYSTEMAGENT (IID NUMERIC(19) NOT NULL ,ISYSID NUMERIC(19) ,IAGENTID NUMERIC(19) ,CONSTRAINT PK_IEAI_SYNC_SYSTEMAGENT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SYNC_CATALOG (IID NUMERIC(19) NOT NULL ,IRELATIONID NUMERIC(19) ,ITYPE NUMERIC(1) ,ISTATUS NUMERIC(1) ,IPATH VARCHAR(255) ,INATCHSYNC NUMERIC(1) ,IALLSYNC NUMERIC(1) ,ISYNCSTRATEGY VARCHAR(255) ,ISENCRYPTION NUMERIC(1) ,CONSTRAINT PK_IEAI_SYNC_CATALOG PRIMARY KEY (IID));

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IDCID') THEN
			ALTER TABLE IEAI_AGENTINFO ADD IDCID NUMERIC(19);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_PROJECT WHERE  IID=-13) THEN
			INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-13, 0, '所有一致性比对业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 13, -13, -13, -13);
		END IF;
		COMMIT;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.22 version does not have patches for compare
	-- 4.7.23 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN		

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CATALOG' AND COLUMN_NAME = 'IPATHNAME') THEN
			ALTER TABLE IEAI_SYNC_CATALOG ADD IPATHNAME VARCHAR(255);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CRONTASK' AND COLUMN_NAME = 'ICREATER') THEN
			ALTER TABLE IEAI_SYNC_CRONTASK ADD ICREATER VARCHAR(255);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CRONTASK' AND COLUMN_NAME = 'ICREATETIME') THEN
			ALTER TABLE IEAI_SYNC_CRONTASK ADD ICREATETIME NUMERIC(19) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_RESULT' AND COLUMN_NAME = 'IUSETIME') THEN
			ALTER TABLE IEAI_SYNC_RESULT ADD IUSETIME NUMERIC(19) ;
		END IF;

		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.24 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COMPARE_PROGRAMME' AND COLUMN_NAME = 'ISYSID') THEN
		ALTER TABLE IEAI_COMPARE_PROGRAMME ADD ISYSID NUMERIC(19);
	END IF;

	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.25 version does not have patches for compare
	-- 4.7.26 version does not have patches for compare
	-- 4.7.27 version does not have patches for compare
		DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.27 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
	CREATE TABLE IF NOT EXISTS IEAI_SYNC_WARNSTATE (IID NUMERIC(19) NOT NULL ,IRESULTID NUMERIC(19) ,ISYSNAME VARCHAR(255) ,ISTATE NUMERIC(1) ,CONSTRAINT PK_IEAI_SYNC_WARNSTATE PRIMARY KEY (IID));
	
	IF NOT EXISTS (SELECT * FROM IEAI_PARAMETER_CONFIG WHERE  IID=28) THEN
		INSERT INTO IEAI_PARAMETER_CONFIG(IID,IPARAMETER,IPARAVALUE) VALUES(28,'socketRsyncScript','-s ##/rsync_startsocket.sh##');
	END IF;
	
	CREATE TABLE IF NOT EXISTS IEAI_SYNC_SOCKETFLOW (IID NUMERIC(10) NOT NULL,IAGENTID NUMERIC(19) ,IFLOWID NUMERIC(19) ,ISTATE NUMERIC(1) ,IRESULTSTR VARCHAR(255) ,IRESULT NUMERIC(1) ,CONSTRAINT PK_SYNC_SOCKETFLOW PRIMARY KEY (IID));
	
	
	CREATE TABLE IF NOT EXISTS IEAI_SCOMPARE_CONFRESULT (IID NUMERIC(19) NOT NULL ,IRESULTSYSID NUMERIC(19) ,ITYPE VARCHAR(255) ,IPATH VARCHAR(255) ,IALLSYNC NUMERIC(1) ,INATCHSYNC NUMERIC(1) ,IISPASS NUMERIC(1) ,IISPASSHTML LONGTEXT ,IENCODE VARCHAR(255) ,ISYNCSTRATEGY VARCHAR(255) ,IFLOWID NUMERIC(19) ,CONSTRAINT PK_IEAI_SCOMPARE_CONFRESULT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_SCOMPARE_SYSRESULT (IID NUMERIC(19) NOT NULL ,IRESULTID NUMERIC(19) ,ISYSNAME VARCHAR(255) ,ISOURCEIP VARCHAR(255) ,ITARGETIP VARCHAR(255) ,ISOURCEPORT NUMERIC(19) ,ITARGETPORT NUMERIC(19) ,ISOURCENAME VARCHAR(255) ,ITARGETNAME VARCHAR(255) ,IISPASS NUMERIC(1) ,CONSTRAINT PK_IEAI_SCOMPARE_SYSRESULT PRIMARY KEY (IID));
	CREATE TABLE IF NOT EXISTS IEAI_SCOMPARE_RESULT (IID NUMERIC(19) NOT NULL ,IRESULTNAME VARCHAR(255) ,IRESULTDES VARCHAR(255) ,ICOMPARETIME NUMERIC(19) ,IISPASS NUMERIC(1) ,IUSETIME NUMERIC(19) ,CONSTRAINT PK_IEAI_SCOMPARE_RESULT PRIMARY KEY (IID));
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CATALOG' AND COLUMN_NAME = 'IMODEL') THEN
		ALTER TABLE IEAI_SYNC_CATALOG ADD IMODEL NUMERIC(1) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_CATALOG' AND COLUMN_NAME = 'IENCODE') THEN
		ALTER TABLE IEAI_SYNC_CATALOG ADD IENCODE VARCHAR(255) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYNC_STRATEGYPLAN' AND COLUMN_NAME = 'IMODEL') THEN
		ALTER TABLE IEAI_SYNC_STRATEGYPLAN ADD IMODEL NUMERIC(1) ;
	END IF;
	CREATE TABLE IF NOT EXISTS IEAI_SCOMPARE_CRONTASK (IID NUMERIC(19) NOT NULL ,IPLANID NUMERIC(19) ,ICRON VARCHAR(255) ,ISTATUS NUMERIC(1) ,ICREATER VARCHAR(255) ,ICREATETIME NUMERIC(19) ,CONSTRAINT PK_IEAI_SCOMPARE_CRONTASK PRIMARY KEY (IID));
	COMMIT;
	IF  EXISTS (SELECT * FROM IEAI_SYNC_CATALOG WHERE IMODEL IS NULL) THEN
		UPDATE IEAI_SYNC_CATALOG SET IMODEL= 1  WHERE IMODEL IS NULL ;
	END IF;
	IF  EXISTS (SELECT * FROM IEAI_SYNC_STRATEGYPLAN WHERE IMODEL IS NULL) THEN
		UPDATE IEAI_SYNC_STRATEGYPLAN SET IMODEL= 1  WHERE IMODEL IS NULL ;
	END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	
	-- 8.1.0 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
	CREATE TABLE IF NOT EXISTS IEAI_COMPARE_RELATION (IID NUMERIC(19) NOT NULL ,ISYSID NUMERIC(19) ,ICLASSID NUMERIC(19),CONSTRAINT PK_IEAI_COMPARE_RELATION PRIMARY KEY (IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
	

	-- 8.2.0 version compare patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCOMPARE_CONFRESULT' AND COLUMN_NAME = 'IPATHNAME') THEN
    ALTER TABLE IEAI_SCOMPARE_CONFRESULT ADD IPATHNAME VARCHAR(255) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCOMPARE_SYSRESULT' AND COLUMN_NAME = 'ISOURCECOMNAME') THEN
		ALTER TABLE IEAI_SCOMPARE_SYSRESULT ADD ISOURCECOMNAME VARCHAR(255) ;
	END IF;
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCOMPARE_SYSRESULT' AND COLUMN_NAME = 'ITARGETCOMNAME') THEN
		ALTER TABLE IEAI_SCOMPARE_SYSRESULT ADD ITARGETCOMNAME VARCHAR(255) ;
	END IF;
	CREATE TABLE IF NOT EXISTS IEAI_SYNC_CATELOGRELATION (IID NUMERIC(19) NOT NULL ,ICATELOGID NUMERIC(19) ,IPLANVIEWID NUMERIC(19) ,IPLANID NUMERIC(19) ,CONSTRAINT PK_IEAI_SYNC_CATELOGRELATION PRIMARY KEY (IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
		
	
