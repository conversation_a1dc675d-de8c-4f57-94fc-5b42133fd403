var compareStore;
Ext.onReady(function () {
//清理主面板的各种监听时间
	destroyRubbish();
	Ext.tip.QuickTipManager.init();

	Ext.define("centerModel", {
		extend: "Ext.data.Model",
		fields: [{
			name: "centerid", type: "long"
		}, {
			name: "centername", type: "string"
		}]
	});

	/**比较规则数据源**/
	compareStore = Ext.create('Ext.data.Store', {
		fields: ['iid', 'iname'],
		autoLoad: true,
		autoDestroy: true,
		data: [
			{"iid": "1", "iname": ">"},
			{"iid": "2", "iname": "<"},
			{"iid": "3", "iname": "="},
			{"iid": "4", "iname": ">="},
			{"iid": "5", "iname": "<="},
			{"iid": "6", "iname": "包含"},
			{"iid": "7", "iname": "不等于"}
		]
	});

	function getTextById(_store, id) {
		var r = _store.findRecord("iid", id);
		var text = "";
		if (r != null) {
			text = r.get("iname");
		}

		return text;
	}

	var centerStore = Ext.create('Ext.data.Store',
		{
			model: 'centerModel',
			autoLoad: true,
			proxy:
				{
					type: 'ajax',
					url: 'strategycommon/centerlist.do',
					reader:
						{
							type: 'json',
							root: 'dataList'
						}
				}
		});

	/** 查询条件-物理中心下拉 */
	var centerComboBox = Ext.create('Ext.form.field.ComboBox', {

		labelWidth: 70,
		labelAlign: 'right',
		fieldLabel: '物理中心',
		width: '25%',
		queryMode: 'local',
		emptyText: '全部',
		store: centerStore,
		displayField: 'centername',
		valueField: 'centerid',
		editable: true
	});

	Ext.define('systemModel',
		{
			extend: 'Ext.data.Model',
			fields: [
				{
					name: 'systemInfoName',
					type: 'string'
				},
				{
					name: 'systemInfoId',
					type: 'long'
				}
			]
		});

	var systemStore = Ext.create('Ext.data.Store',
		{
			autoLoad: true,
			autoDestroy: true,
			model: 'systemModel',
			proxy:
				{
					type: 'ajax',
					url: 'strategycommon/systemInfolist.do',
					reader:
						{
							type: 'json',
							root: 'dataList'
						}
				}
		});

	/** 查询条件-业务系统下拉 */
	var systemComboBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		name: 'cpstatus',
		labelWidth: 70,
		queryMode: 'local',
		fieldLabel: ' 业务系统',
		displayField: 'systemInfoName',
		valueField: 'systemInfoId',
		editable: true,
		emptyText: '全部',
		store: systemStore,
		width: '25%',
		labelAlign: 'right'
	});

	Ext.define('equipmentTypeModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'equipmentTypeId',
			type: 'long'
		}, {
			name: 'equipmentTypeName',
			type: 'string'
		}]
	});

	var equipmentTypeStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'equipmentTypeModel',
		proxy: {
			type: 'ajax',
			url: 'strategycommon/equipmentTypeList.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	/** 查询条件-设备类型下拉 */
	var equipmentTypeComboBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		labelWidth: 70,
		queryMode: 'local',
		fieldLabel: '设备类型',
		displayField: 'equipmentTypeName',
		valueField: 'equipmentTypeId',
		editable: true,
		emptyText: '全部',
		store: equipmentTypeStore,
		width: '25%',
		labelAlign: 'right'
	});

	Ext.define('manufactoryModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'manuFactoryId',
			type: 'long'
		}, {
			name: 'manuFactoryName',
			type: 'string'
		}]
	});

	var manufactoryStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'manufactoryModel',
		proxy: {
			type: 'ajax',
			url: 'strategycommon/manuFactory.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});

	/** 查询条件-设备品牌下拉 */
	var manufactoryComboBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		labelWidth: 70,
		queryMode: 'local',
		fieldLabel: ' 品牌',
		displayField: 'manuFactoryName',
		valueField: 'manuFactoryId',
		editable: true,
		emptyText: '全部',
		store: manufactoryStore,
		width: '25%',
		labelAlign: 'right',
		listeners: {
			select: function (combo, record, index) {
				equipmentmodelComboBox.setValue("");
				equipmentmodelStore.load();
			}
		}
	});

	Ext.define('equipmentmodelModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'equipmentModelId',
			type: 'long'
		}, {
			name: 'equipmentModelName',
			type: 'string'
		}]
	});

	var equipmentmodelStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'equipmentmodelModel',
		proxy: {
			type: 'ajax',
			url: 'strategycommon/equipmentModel.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	equipmentmodelStore.on('beforeload', function (store, options) {
		var manufactoryId = manufactoryComboBox.getValue();
		var new_params = {
			factoryId: manufactoryId
		};
		Ext.apply(equipmentmodelStore.proxy.extraParams, new_params);
	});

	/** 查询条件-设备型号下拉 */
	var equipmentmodelComboBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		name: 'cpstatus',
		labelWidth: 70,
		queryMode: 'local',
		fieldLabel: ' 型号',
		displayField: 'equipmentModelName',
		valueField: 'equipmentModelId',
		editable: true,
		emptyText: '全部',
		store: equipmentmodelStore,
		width: '25%',
		labelAlign: 'right'
	});

	/** 查询条件-设备名称输入框 */
	var equipmentNameText = Ext.create('Ext.form.TextField', {
		fieldLabel: '设备名称',
		emptyText: '--请输入设备名称--',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%'
	});

	/** 查询条件-IP起输入框 */
	var ipStartText = Ext.create('Ext.form.TextField', {
		fieldLabel: 'IP起',
		emptyText: '--请输入IP--',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%',
		regex:/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
		regexText:'请输入正确格式的IP地址'
	});

	/** 查询条件-IP止输入框 */
	var ipEndText = Ext.create('Ext.form.TextField', {
		fieldLabel: 'IP止',
		emptyText: '--请输入IP--',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%',
		regex:/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
		regexText:'请输入正确格式的IP地址'
	});

	/** 查询条件-序列号输入框 */
	var serialNumberText = Ext.create('Ext.form.TextField', {
		fieldLabel: '序列号',
		emptyText: '--请输入序列号--',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%'
	});

	/** 查询条件-机房输入框 */
	var equipmentRoomText = Ext.create('Ext.form.TextField', {
		fieldLabel: '机房',
		emptyText: '--请输入机房--',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%'
	});

	/** 查询条件-开始时间输入框 */
	var startTimeText = Ext.create('Ext.form.TextField', {
		fieldLabel: '开始时间',
		emptyText: '请输入',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%'
	});

	/** 查询条件-结束时间输入框 */
	var endTimeText = Ext.create('Ext.form.TextField', {
		fieldLabel: '结束时间',
		emptyText: '请输入',
		labelWidth: 70,
		labelAlign: 'right',
		width: '25%'
	});

	var date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
	var startTimeText = Ext.create('Ext.form.field.Date', {
		fieldLabel: '开始时间:',
		labelAlign: 'right',
		labelWidth: 70,
		emptyText: '--请输入开始时间--',
		name: 'startTime',
		width: '25%',
		// padding : '5 5 5 5',
		format: 'Y-m-d',
		value: ''
	});
	var date2 = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1);
	var endTimeText = Ext.create('Ext.form.field.Date', {
		fieldLabel: '结束时间:',
		labelAlign: 'right',
		name: 'endTime',
		emptyText: '--请输入结束时间--',
		labelWidth: 70,
		width: '25%',
		// padding : '5 5 5 5',
		format: 'Y-m-d',
		value: ''
	});
	Ext.define("HcStatusModel", {
		extend: "Ext.data.Model",
		fields: [{
			name: "iid", type: "long"
		}, {
			name: "iname", type: "string"
		}]
	});
	//巡检状态
	var HcStatusStore = Ext.create('Ext.data.Store', {
		model: "HcStatusModel",
		autoLoad: true,
		autoDestroy: true,
		data: [
			{"iid": -1, "iname": "全部"},
			{"iid": 1, "iname": "巡检正常"},
			{"iid": 2, "iname": "巡检异常"},
			{"iid": 0, "iname": "未巡检"},
			{"iid": -2, "iname": "Ping不通"},
			{"iid": -3, "iname": "停机"}
		]
	});

	var HcStatusComBox = new Ext.form.field.ComboBox({
		labelWidth: 70,
		labelAlign: 'right',
		displayField: 'iname',
		valueField: 'iid',
		width: '25.5%',
		fieldLabel: '巡检状态',
		value: -1,
		enableKeyEvents: true,
		forceSelection: true,
		store: HcStatusStore,
		anyMatch: true,
		queryMode: 'local',
		listeners: {
			select: function (combo, record, index) {
			}
		}
	});

	Ext.define("isBindModel", {
		extend: "Ext.data.Model",
		fields: [{
			name: "iid", type: "long"
		}, {
			name: "iname", type: "string"
		}]
	});
	var isHcStore = Ext.create('Ext.data.Store', {
		model: 'isBindModel',
		autoLoad : true,
		autoDestroy : true,
		data : [
			{iname: '全部',    iid: '-1'},
			{iname: '是',    iid: '1'},
			{iname: '否',    iid: '0'},
		]
	});
	/** 维护商查询输入框* */
	var mainTenanceVenderQuery = Ext.create ('Ext.form.TextField',
		{
			fieldLabel : '维护商',
			emptyText : '--请输入维护商--',
			labelAlign: 'right',
			labelWidth : 70,
			width : '25%',
		});

	/** 订单编号查询输入框* */
	var contractNoQuery = Ext.create ('Ext.form.TextField',
		{
			fieldLabel : '订单编号',
			emptyText : '--请输入维护商--',
			labelAlign: 'right',
			labelWidth : 70,
			width : '25%',
		});

	/** 设备模型查询输入框* */
	var bigModelQuery = Ext.create ('Ext.form.TextField',
		{
			fieldLabel : '设备模型',
			emptyText : '--请输入维护商--',
			labelAlign: 'right',
			labelWidth : 70,
			width : '25%',
		});

	/** 是否购买维保查询下拉* */
	var mainTenanceFlagBox = new Ext.form.field.ComboBox({
		labelWidth : 70,
		displayField : 'iname',
		valueField: 'iid',
		labelAlign: 'right',
		width : '25%',
		fieldLabel : '是否购买维保',
		value:'-1',
		enableKeyEvents: true,
		forceSelection:true,
		store : isHcStore,
		queryMode :'local',
		listeners: {
			select: function(combo, record, index) {
			}
		}
	});
	var usingCategoriesStore = Ext.create('Ext.data.Store', {
		model: 'isBindModel',
		autoLoad : true,
		autoDestroy : true,
		data : [
			{iname: '全部',    iid: '-1'},
			{iname: '生产',    iid: 'product'},
			{iname: '测试',    iid: 'testing'},
			{iname: '灾备',    iid: 'delivery'},
			{iname: '自用',    iid: 'self_use'},
			{iname: '调拨他用',    iid: 'allocation'},
		]
	});
	/** 使用类别查询下拉* */
	var usingCategoriesBox = new Ext.form.field.ComboBox({
		labelWidth : 70,
		displayField : 'iname',
		valueField: 'iid',
		width : '25.5%',
		fieldLabel : '使用类别',
		labelAlign: 'right',
		value:'-1',
		enableKeyEvents: true,
		forceSelection:true,
		store : usingCategoriesStore,
		queryMode :'local',
		listeners: {
			select: function(combo, record, index) {
			}
		}
	});

	Ext.define('equipmentHcHisModel', {
		extend: 'Ext.data.Model',
		fields:
			[{
				name: 'iid',
				type: 'string'
			}, {
				name: 'centername',
				type: 'string'
			}, {
				name: 'sysname',
				type: 'string'
			}, {
				name: 'applvl',
				type: 'string'
			}, {
				name: 'cpid',
				type: 'string'
			}, {
				name: 'cpname',
				type: 'string'
			}, {
				name: 'manufactoryname',
				type: 'string'
			}, {
				name: 'equipmentmodelname',
				type: 'string'
			}, {
				name: 'idate',
				type: 'string'
			}, {
				name: 'istatus',
				type: 'string'
			},{
				name: 'mainTenanceVender',
				type: 'string'
			},{
				name: 'bigModel',
				type: 'string'
			},{
				name: 'contractNo',
				type: 'string'
			},{
				name: 'mainTenanceFlag',
				type: 'string'
			},{
				name: 'usingCategories',
				type: 'string'
			}]
	});

	var equipmentHcHisStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'equipmentHcHisModel',
		proxy: {
			type: 'ajax',
			url: 'getEquimentHcHisList.do',
			actionMethods: 'post',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	})

	equipmentHcHisStore.on('beforeload', function (store, options) {
		var centerId = centerComboBox.getValue();
		var systemId = systemComboBox.getValue();
		var equipmentTypeId = equipmentTypeComboBox.getValue();
		var manufactoryId = manufactoryComboBox.getValue();
		var equipmentModelName = equipmentmodelComboBox.getValue();
		var equipmentName = equipmentNameText.getValue();
		var ipStart = ipStartText.getValue();
		var ipEnd = ipEndText.getValue();
		var serialNumber = serialNumberText.getValue();
		var equipmentRoom = equipmentRoomText.getValue();
		var startTime = startTimeText.getValue();
		var endTime = endTimeText.getValue();
		var status = HcStatusComBox.getValue();
		var mainTenanceVender = mainTenanceVenderQuery.getValue();
		var bigModel = bigModelQuery.getValue();
		var contractNo = contractNoQuery.getValue();
		var mainTenanceFlag = mainTenanceFlagBox.getValue();
		var usingCategories = usingCategoriesBox.getValue();
		var new_params = {
			centerId: centerId,
			systemId: systemId,
			equipmentTypeId: equipmentTypeId,
			manufactoryId: manufactoryId,
			equipmentModelName: equipmentModelName,
			equipmentName: equipmentName,
			ipStart: ipStart,
			ipEnd: ipEnd,
			serialNumber: serialNumber,
			equipmentRoom: equipmentRoom,
			startTime: startTime,
			endTime: endTime,
			status: status,
			mainTenanceVender : mainTenanceVender,
			bigModel : bigModel,
			contractNo : contractNo,
			mainTenanceFlag : mainTenanceFlag,
			usingCategories : usingCategories
		};
		Ext.apply(equipmentHcHisStore.proxy.extraParams, new_params);
	});


	var equipmentHcHisColumns = [{
		text: '序号',
		xtype: 'rownumberer',
		width: 50
	}, {
		text: '主键ID',
		dataIndex: 'iid',
		hidden: true
	}, {
		text: '物理中心',
		dataIndex: 'centername',
		minWidth: 120
	}, {
		text: '业务系统',
		dataIndex: 'sysname',
		minWidth: 290,
		flex: 1
	}, {
		text: '系统级别',
		dataIndex: 'applvl',
		minWidth: 100
	}, {
		text: '设备ID',
		dataIndex: 'cpid',
		width: 200,
		hidden: true
	}, {
		text: '设备名称',
		dataIndex: 'cpname',
		minWidth: 290,
		flex: 1,
		renderer: turnDetailOne,
		listeners: {
			click: function (a, b, c, d) {
				var cpid = a.getStore().getAt(c).data.cpid;
				var date = a.getStore().getAt(c).data.idate;
				/////////////////////////////////////////
				//设备信息的

				var centerNameValue = null;
				var equipmentTypeValue = null;
				var manuFactoryValue = null;
				var equipmentNameValue = null;
				var equipmentIPValue = null;
				var serialNumber = null;
				//查询
				Ext.define('EquipmentModel222', {
					extend: 'Ext.data.Model',
					fields: [
						{
							name: 'centerName',//物理中心
							type: 'string'
						},
						{
							name: 'equipmentType',//设备类型
							type: 'string'
						},
						{
							name: 'manuFactory',//品牌
							type: 'string'
						},
						{
							name: 'equipmentName',//设备名称
							type: 'string'
						},
						{
							name: 'equipmentIP',//IP
							type: 'string'
						},
						{
							name: 'serialNumber',//序列号
							type: 'string'
						},
					]
				});
				var equipmentStore = Ext.create('Ext.data.Store', {
					autoLoad: true,
					//autoDestroy: true,
					//remoteSort : true,
					model: 'EquipmentModel222',
					proxy: {
						type: 'ajax',
						url: 'strategicManager/queryEquipment.do',
						reader: {
							type: 'json',
							root: 'list'
						},
					},
					listeners: {
						load: function () {
							centerNameValue = equipmentStore.getAt(0).data.centerName;
							equipmentTypeValue = equipmentStore.getAt(0).data.equipmentType;
							manuFactoryValue = equipmentStore.getAt(0).data.manuFactory;
							equipmentNameValue = equipmentStore.getAt(0).data.equipmentName;
							equipmentIPValue = equipmentStore.getAt(0).data.equipmentIP;
							serialNumber = equipmentStore.getAt(0).data.serialNumber;
							//showPanelImme.show(batchid, cpid, "imme", centerNameValue,equipmentTypeValue,manuFactoryValue,equipmentNameValue,equipmentIPValue);
							showPanel.show(cpid, date, "his", centerNameValue, equipmentTypeValue, manuFactoryValue, equipmentNameValue, equipmentIPValue, serialNumber);
						}
					}
				});
				equipmentStore.on('beforeload', function (store, options) {
					var new_params = {
						cpId: cpid
					};
					Ext.apply(equipmentStore.proxy.extraParams, new_params);
				});
				////
				//showPanel.show(cpid, date, "his");
			}
		}
	}, {
		text: '品牌',
		dataIndex: 'manufactoryname',
		minWidth: 150,
		flex: 1
	}, {
		text: '型号',
		dataIndex: 'equipmentmodelname',
		minWidth: 150,
		flex: 1
	}, {
		text: '日期',
		dataIndex: 'idate',
		minWidth: 150,
		flex: 1
	}, {
		text: '巡检状态',
		dataIndex: 'istatus',
		minWidth: 120,
		flex: 1,
		renderer: function (value, metaData, record, rowIndex, colIndex) {
			var text = "";
			if(value=='-1'){
				text = '未巡检';
			}else if(value=='-2'){
				text = 'Ping不通';
			}else if(value=='-3'){
				text = '停机';
			}else if(value=='0'){
				text = '巡检正常';
			}else{
				text = '巡检异常';
			}

			var backValue = '<span style="float:left;width: 60px">' + text + '</span>';
			if (value == '0') {//正常
				backValue += '<span class="normal_green general_attribute"></span>';
			} else if (value == '1') {//警告
				backValue += '<span class="warning_yellow general_attribute"></span>';
			} else if (value == '2') {//错误
				backValue += '<span class="error_orange general_attribute"></span>';
			} else if (value == '3') {//紧急
				backValue += '<span class="emergency_red general_attribute"></span>';
			} else if (value == '-1') {//无结果
				backValue += '<span class="uninspected_black general_attribute"></span>';
			} else if (value == '-2') {//ping不通
				backValue += '<span class="noping_gray general_attribute"></span>';
			} else if (value == '-3') {//停机
				backValue += '<span class="noping_gray general_attribute"></span>';
			}
			return backValue;
		}
	}, {
		text: '维护商',
		dataIndex: 'mainTenanceVender',
		minWidth: 170,
		flex: 1
	}, {
		text: '设备模型',
		dataIndex: 'bigModel',
		minWidth: 170,
		flex: 1
	}, {
		text: '订单编号',
		dataIndex: 'contractNo',
		minWidth: 170,
		flex: 1
	}, {
		text: '是否购买维保',
		dataIndex: 'mainTenanceFlag',
		minWidth: 100,
		flex: 1,
		renderer: function (value, metaData, record, rowIndex, colIndex) {
			if (value == '0') {
				return '否';
			} else if (value == '1') {
				return '是';
			} else {
				return '待补充';
			}
		}
	}, {
		text: '使用类别',
		dataIndex: 'usingCategories',
		minWidth: 100,
		flex: 1,
		renderer: function (value, metaData, record, rowIndex, colIndex) {
			if (value == 'product') {
				return '生产';
			} else if (value == 'testing') {
				return '测试';
			} else if (value == 'delivery') {
				return '灾备';
			} else if (value == 'self_use') {
				return '自用';
			} else if (value == 'allocation') {
				return '调拨他用';
			}
		}
	}];

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});

	function turnDetailOne(value, metaData, record) {
		return "<a style='cursor: pointer;'>" + value + "</a>";
	}

	// function showPanel(cpid, date) {
	//
	// 	Ext.define("checkItemModel", {
	// 		extend: "Ext.data.Model",
	// 		fields: [{
	// 			name: "checkItemId", type: "long"
	// 		}, {
	// 			name: "checkItemName", type: "string"
	// 		}]
	// 	});
	//
	// 	var checkItemStore = Ext.create ('Ext.data.Store',
	// 		{
	// 			model : 'checkItemModel',
	// 			autoLoad : true,
	// 			proxy :
	// 				{
	// 					type : 'ajax',
	// 					url : 'strategycommon/getCheckItem.do?cpId=' + cpid,
	// 					reader :
	// 						{
	// 							type : 'json',
	// 							root : 'dataList'
	// 						}
	// 				}
	// 		});
	//
	// 	/** 查询条件-巡检项下拉 */
	// 	var checkItemComboBox = Ext.create('Ext.form.field.ComboBox', {
	//
	// 		labelWidth : 70,
	// 		labelAlign : 'right',
	// 		fieldLabel : '巡检项',
	// 		width : '25%',
	// 		margin : '5',
	// 		queryMode : 'local',
	// 		emptyText : '全部',
	// 		store : checkItemStore,
	// 		displayField : 'checkItemName',
	// 		valueField : 'checkItemId',
	// 		editable : true,
	// 		listConfig :
	// 			{
	// 				maxHeight : 280
	// 			}
	// 	});
	//
	// 	Ext.define('hcResultModel', {
	// 		extend : 'Ext.data.Model',
	// 		remoteSort : true,
	// 		fields : [
	// 			{
	// 				name : 'ciname',
	// 				type : 'string'
	// 			},
	// 			{
	// 				name : 'cptext',
	// 				type : 'string'
	// 			},
	// 			{
	// 				name : 'cpstatus',
	// 				type : 'string'
	// 			},
	// 			{
	// 				name : 'icomparerule',
	// 				type : 'string'
	// 			},
	// 			{
	// 				name : 'i2',
	// 				type : 'string'
	// 			},
	// 			{
	// 				name : 'i3',
	// 				type : 'string'
	// 			},
	// 			{
	// 				name : 'i4',
	// 				type : 'string'
	// 			}
	// 		]
	// 	});
	//
	// 	var hcResultStore = Ext.create('Ext.data.Store', {
	// 		autoLoad : true,
	// 		autoDestroy : true,
	// 		model : 'hcResultModel',
	// 		proxy : {
	// 			type : 'ajax',
	// 			url : 'getCheckPointInfo.do',
	// 			actionMethods : 'post',
	// 			reader : {
	// 				type : 'json',
	// 				root : 'dataList'
	// 			}
	// 		}
	// 	})
	//
	// 	hcResultStore.on ('beforeload', function (store, options)
	// 	{
	// 		var chkitemId = checkItemComboBox.getValue();
	// 		var new_params =
	// 			{
	// 				cpId : cpid,
	// 				chkitemId : chkitemId,
	// 				date : date,
	// 				type : 'his'
	// 			};
	// 		Ext.apply (hcResultStore.proxy.extraParams, new_params);
	// 	});
	//
	// 	var hcResultColumns = [
	// 		{
	// 			text : '序号',
	// 			width : 40,
	// 			xtype : 'rownumberer'
	// 		},{
	// 			text : '巡检项名称',
	// 			dataIndex : 'ciname',
	// 			minWidth: 200
	// 		},{
	// 			text : '巡检点名称',
	// 			dataIndex : 'cptext',
	// 			minWidth: 200
	// 		},{
	// 			text : '检查结果',
	// 			dataIndex : 'cpstatus',
	// 			minWidth: 130,
	// 			sortable : true
	// 		},
	// 		{
	// 			text : '检测值',
	// 			sortable : true,
	// 			minWidth: 130,
	// 			dataIndex : 'i3'
	// 		},
	// 		{
	// 			text : '比较规则',
	// 			sortable : true,
	// 			minWidth: 130,
	// 			dataIndex : 'icomparerule',
	// 			renderer: function (value) {
	// 				return getTextById(compareStore, value);
	// 			}
	// 		},
	// 		{
	// 			text : '基线（阈值）',
	// 			sortable : true,
	// 			minWidth: 130,
	// 			dataIndex : 'i4'
	// 		},
	// 		{
	// 			text : '最后巡检时间',
	// 			sortable : true,
	// 			minWidth: 160,
	// 			dataIndex : 'i2'
	// 		}
	// 	];
	//
	// 	var hcResultGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
	// 		width : '100%',
	// 		region:'center',
	// 		store : hcResultStore,
	// 		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	// 		// selModel : Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
	// 		plugins: [cellEditing],
	// 		border : true,
	// 		viewConfig:{
	// 			enableTextSelection:true
	// 		},
	// 		columnLines : true,
	// 		columns : hcResultColumns,
	// 		cls:'customize_panel_back',
	// 		padding : grid_space,
	// 		cellTip : true,
	// 		animCollapse : false,
	// 		dockedItems : [ {
	// 			xtype : 'toolbar',
	// 			items : ['->',checkItemComboBox,
	// 				{
	// 					xtype : 'button',
	// 					cls : 'Common_Btn',
	// 					text : '查询',
	// 					handler : function(){
	// 						hcResultStore.load();
	// 					}
	// 				}]
	// 		}]
	// 	});
	//
	// 	var showMainPanel = Ext.create('Ext.panel.Panel', {
	// 		border : false,
	// 		region: 'center',
	// 		height: contentPanel.getHeight() * 0.7-25,
	// 		layout : 'border',
	// 		cls:'panel_space_top',
	// 		items : [hcResultGrid]
	// 	});
	//
	// 	win = Ext.create('widget.window', {
	// 		id : "cmdAndScript",
	// 		draggable : true,// 禁止拖动
	// 		resizable : true,// 禁止缩放
	// 		// maskDiabled:true,
	// 		modal : false,
	// 		title : '检查点历史明细',
	// 		closable : true,
	// 		closeAction : 'destroy',
	// 		width : "65%",
	// 		height : "90%",
	// 		layout : 'fit',
	// 		bodyCls: 'service_platform_bodybg',
	// 		items : [showMainPanel]
	// 	});
	//
	// 	win.show();
	// }

	var equipmentHcHisGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		width: '100%',
		region: 'center',
		store: equipmentHcHisStore,
		ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		selModel: Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
		plugins: [cellEditing],
		border: true,
		viewConfig: {
			enableTextSelection: true
		},
		columnLines: true,
		columns: equipmentHcHisColumns,
		cls: 'customize_panel_back',
		padding: grid_space,
		cellTip: true,
		animCollapse: false,
		dockedItems: [{
			xtype: 'toolbar',
			items: []
		}]
	});

	var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {
		region: 'north',
		layout: 'anchor',
		buttonAlign: 'center',
		bodyCls: 'x-docked-noborder-top',
		// iqueryFun: barquery,
		border: false,
		dockedItems: [
			{
				xtype: 'toolbar',
				baseCls: 'customize_gray_back',
				border: false,
				dock: 'top',
				items: [
					centerComboBox, systemComboBox, equipmentTypeComboBox, manufactoryComboBox
				]
			},
			{
				xtype: 'toolbar',
				baseCls: 'customize_gray_back',
				border: false,
				dock: 'top',
				items: [
					equipmentmodelComboBox, equipmentNameText, ipStartText, ipEndText
				]
			},
			{
				xtype: 'toolbar',
				baseCls: 'customize_gray_back',
				border: false,
				dock: 'top',
				items: [
					serialNumberText, equipmentRoomText, startTimeText, endTimeText
				]
			},
			{
				xtype: 'toolbar',
				baseCls: 'customize_gray_back',
				border: false,
				dock: 'top',
				items: [
					mainTenanceVenderQuery, bigModelQuery, contractNoQuery, mainTenanceFlagBox
				]
			},
			{
				xtype: 'toolbar',
				baseCls: 'customize_gray_back',
				border: false,
				dock: 'top',
				items: [usingCategoriesBox,HcStatusComBox, '->',
					{
						text: '查询',
						baseCls: 'Common_Btn',
						handler: function () {
							//equipmentHcHisStore.load();
							var startStr =  ipStartText.getValue();
							var endStr = ipEndText.getValue();
							if(startStr!=null && startStr!=''){
								if(!checkIP(startStr,"IP起格式不正确！")){
									return;
								}
							}
							if(endStr!=null && endStr!=''){
								if(!checkIP(endStr,"IP止格式不正确！")){
									return;
								}
							}
							equipmentHcHisGrid.ipage.moveFirst();
						}
					},
					{
						text: '重置',
						baseCls: 'Common_Btn',
						handler: function () {
							centerComboBox.setValue("");
							systemComboBox.setValue("");
							equipmentTypeComboBox.setValue("");
							manufactoryComboBox.setValue("");
							equipmentmodelComboBox.setValue("");
							equipmentNameText.setValue("");
							ipStartText.setValue("");
							ipEndText.setValue("");
							serialNumberText.setValue("");
							equipmentRoomText.setValue("");
							startTimeText.setValue("");
							endTimeText.setValue("");
							HcStatusComBox.setValue(-1);
							mainTenanceVenderQuery.setValue("");
							bigModelQuery.setValue("");
							contractNoQuery.setValue("");
							mainTenanceFlagBox.setValue("-1");
							usingCategoriesBox.setValue("-1");
						}
					},
					{
						text: '生成报告',
						baseCls: 'Common_Btn',
						handler: reportExport
					}
				]
			}
		]
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo: "strategyEquipmentHcHis",
		layout: 'border',
		width: contentPanel.getWidth(),
		bodyCls: 'service_platform_bodybg',
		height: contentPanel.getHeight() - 50,
		border: true,
		bodyPadding: grid_margin,
		items: [formMainPanel, equipmentHcHisGrid]
	});

	// equipmentHcHisGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
	//   equipmentHcHisGrid.down('#delete').setDisabled(selections.length === 0);
	// });


	Date.prototype.toJSON = function () {
		return format(this); //
	}

//返回 yyyy-MM-dd HH:mm:ss格式日期
	function format(d) {
		var y = d.getFullYear();
		var m = d.getMonth() + 1;
		if (m < 10) {
			m = "0" + m;
		}
		var day = d.getDate();
		if (day < 10) {
			day = "0" + day;
		}
		var h = d.getHours();
		if (h < 10) {
			h = "0" + h;
		}
		var M = d.getMinutes();
		if (M < 10) {
			M = "0" + M;
		}
		var s = d.getSeconds();
		if (s < 10) {
			s = "0" + s;
		}
		return y + "-" + m + "-" + day + "T" + h + ":" + M + ":" + s;
	}

	function reportExport() {
		var iidArray = [];
		var selectedRecords = equipmentHcHisGrid.getSelectionModel().getSelection();

		var centerId = centerComboBox.getValue();
		var systemId = systemComboBox.getValue();
		var equipmentTypeId = equipmentTypeComboBox.getValue();
		var manufactoryId = manufactoryComboBox.getValue();
		var equipmentModelName = equipmentmodelComboBox.getValue();
		var equipmentName = equipmentNameText.getValue();
		var ipStart = ipStartText.getValue();
		var ipEnd = ipEndText.getValue();
		var serialNumber = serialNumberText.getValue();
		var equipmentRoom = equipmentRoomText.getValue();
		var startTime = startTimeText.getValue();
		var endTime = endTimeText.getValue();
		var status = HcStatusComBox.getValue();
		var mainTenanceVender = mainTenanceVenderQuery.getValue();
		var bigModel = bigModelQuery.getValue();
		var contractNo = contractNoQuery.getValue();
		var mainTenanceFlag = mainTenanceFlagBox.getValue();
		var usingCategories = usingCategoriesBox.getValue();
		var new_params = {
			centerId: centerId,
			systemId: systemId,
			equipmentTypeId: equipmentTypeId,
			manufactoryId: manufactoryId,
			equipmentModelName: equipmentModelName,
			equipmentName: equipmentName,
			ipStart: ipStart,
			ipEnd: ipEnd,
			serialNumber: serialNumber,
			equipmentRoom: equipmentRoom,
			startTime: startTime,
			endTime: endTime,
			status: status,
			mainTenanceVender : mainTenanceVender,
			bigModel : bigModel,
			contractNo : contractNo,
			mainTenanceFlag : mainTenanceFlag,
			usingCategories : usingCategories
		};

		if (selectedRecords == undefined || selectedRecords.length == 0) {
			Ext.Msg.confirm("请确认", "导出所有设备巡检历史信息？", function (button, text) {
				if (button == "yes") {
					$.fileDownload('equipmentHcHisExport.do', {
						httpMethod: 'POST',
						traditional: true,
						data: {
							params: JSON.stringify(new_params),
							cpids: ''
						},
						successCallback: function (url) {
						},
						failCallback: function (html, url) {
							Ext.Msg.alert('提示', '导出失败！');
							return;
						}
					});
				} else {
					return;
				}
			});
		} else {
			Ext.Array.each(selectedRecords, function (record) {
				var iid = record.get('iid');
				if (iid && iid != 'undefined' && iid != 'UNDEFINED') {
					iidArray.push("'" + iid + "'");
				}
			});
			$.fileDownload('equipmentHcHisExport.do', {
				httpMethod: 'POST',
				traditional: true,
				data: {
					params: JSON.stringify(new_params),
					cpids: iidArray.join(',')
				},
				successCallback: function (url) {
				},
				failCallback: function (html, url) {
					Ext.Msg.alert('提示', '导出失败！');
					return;
				}
			});
		}
	}

	function checkIP(ipstr,text) {
		var file_reg = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
		if (!file_reg.test(ipstr)) {
			Ext.Msg.alert('提示', text);
			return false;
		}
		return true;
	}
});
