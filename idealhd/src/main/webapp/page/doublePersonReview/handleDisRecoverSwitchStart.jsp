<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<html>
<head>
<script type="text/javascript">
var iworkItemid = <%=request.getParameter("iworkItemid")%>;
var istate= <%=request.getParameter("istate")%>;
var sysIds = '<%=request.getAttribute("isysIds") == null ? "" : request
	.getAttribute("isysIds")%>';
var isysNames = '<%=request.getParameter("isysNames") == null ? "" : request
	.getParameter("isysNames")%>';
var iinsName='<%=request.getAttribute("iinsName") == null ? "" : request
	.getAttribute("iinsName")%>';
var	switchInfo='<%=request.getAttribute("switchInfo") == null ? "" : request
	.getAttribute("switchInfo")%>';
var	switchDirString='<%=request.getAttribute("switchDir") == null ? "" : request
	.getAttribute("switchDir")%>';
var	switchCheckType='<%=request.getAttribute("switchCheckType") == null ? "" : request
	.getAttribute("switchCheckType")%>';
var	startType='<%=request.getAttribute("startType") == null ? "" : request
	.getAttribute("startType")%>';
var	iexecUser='<%=request.getAttribute("iexecUser") == null ? "" : request
	.getAttribute("iexecUser")%>';
var	ibackInfoString='<%=request.getAttribute("ibackInfo") == null ? "" : request
	.getAttribute("ibackInfo")%>';
var iscib = <%= Environment.getInstance().getBankSwitchIscib()%>;
var cibItsmSwtich = <%= Environment.getInstance().getCibItsmSwtich()%>;
var deskFlag = <%= Environment.getInstance().getSwitchDesktopFlag()%>;
	
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/doublePersonReview/handleDisRecoverSwitchStart.js"></script>

</head>
<body>
	<div id="disRecoverSwitchStart_dev" style="width: 100%; height: 100%"></div>
</body>
</html>