package com.ideal.ieai.core.io.project;

import java.awt.Point;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.jdom2.Element;

import com.ideal.ieai.core.element.workflow.FlowEdgeInfo;
import com.ideal.ieai.core.io.MarshallingHelperConstants;
import com.ideal.ieai.core.io.ProjectConstants;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.core.io.UnMarshallingHelper;
import com.ideal.ieai.core.io.XMLUnMarshaller;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR> attributable
 * @version 3.0
 */

public class FlowEdgeInfoUnM implements XMLUnMarshaller
{
    private static FlowEdgeInfoUnM _instance = null;

    /**
     * 
     */
    private FlowEdgeInfoUnM()
    {
    }

    public static FlowEdgeInfoUnM getInstance ()
    {
        if (null == _instance)
        {
            _instance = new FlowEdgeInfoUnM();
        }
        return _instance;
    }

    /**
     * 
     * @param TheElement
     * @return
     * @throws UnMarshallerException IF the name of TheElement is not ProjectConstants.FLOWEDGEINFO
     *             thorws it.
     */
    public Object revert ( Element TheElement, List TinyLst ) throws UnMarshallingException
    {
        if (!ProjectConstants.FLOWEDGEINFO.equals(TheElement.getName()))
        {
            throw new UnMarshallingException(UnMarshallingException.INVALID_ELEMENT_TYPE);
        } else
        {
            String startID = TheElement.getAttributeValue(ProjectConstants.STARTID);
            String targetID = TheElement.getAttributeValue(ProjectConstants.TARGETID);
            int lineStyle = Integer.parseInt(TheElement
                    .getAttributeValue(ProjectConstants.LINESTYLE));

            FlowEdgeInfo flowEdge = new FlowEdgeInfo(Integer.parseInt(startID), Integer
                    .parseInt(targetID));
            flowEdge.setLineStyle(lineStyle);

            List pointsList = new ArrayList();
            Element elBendList = TheElement.getChild(ProjectConstants.BENDLIST);
            if (null != elBendList && !elBendList.getChildren().isEmpty())
            {
                Iterator itPoints = elBendList.getChildren().iterator();
                while (itPoints.hasNext())
                {
                    Object obj = itPoints.next();
                    if (obj instanceof Element)
                    {
                        Point point = UnMarshallingHelper.transPoint((Element) obj);
                        pointsList.add(point);
                    }
                }
            }
            flowEdge.setBendList(pointsList);
            // set comment
            Element elComment = TheElement.getChild(ProjectConstants.COMMENT);
            if (elComment != null)
            {
                flowEdge.setName(elComment.getText());
            }
            // set labelPoint

            String sX = TheElement.getAttributeValue(MarshallingHelperConstants.X);
            String sY = TheElement.getAttributeValue(MarshallingHelperConstants.Y);
            if (sX != null && sY != null)
            {
                int x = Integer.valueOf(sX).intValue();
                int y = Integer.valueOf(sY).intValue();
                Point ThePoint = new Point(x, y);
                flowEdge.setLabelPosition(ThePoint);
            }
            return flowEdge;
        }
    }

}