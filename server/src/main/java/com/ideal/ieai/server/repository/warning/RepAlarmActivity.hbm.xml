<?xml version="1.0"?>

<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 2.0//EN" 
    "http://hibernate.sourceforge.net/hibernate-mapping-2.0.dtd">

<hibernate-mapping>
    <class
        name="com.ideal.ieai.server.repository.warning.RepAlarmActivity"
        table="IEAI_ALARM_ACTIVITY"
        dynamic-update="false"
        dynamic-insert="false"
    >

        <id
            name="iid"
            column="iid"
            type="java.lang.Long"
        >
            <generator class="com.ideal.ieai.server.repository.idgenerator.IdGenerator">
            </generator>
        </id>

        <property
            name="iflowId"
            type="java.lang.Long"
            update="true"
            insert="true"
            access="property"
            column="IFLOWID"
        />

        <property
            name="ipfjId"
            type="java.lang.Long"
            update="true"
            insert="true"
            access="property"
            column="IPRJID"
         />

        <property
            name="iactName"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="IACTNAME"
        />

        <property
            name="iflowName"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="IFLOWNAME"
            />
            
        <property
            name="iactType"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="IACTTYPE"
            />

        <!--
            To add non XDoclet property mappings, create a file named
                hibernate-properties-RepActRunInfo.xml
            containing the additional properties and place it in your merge dir.
        -->

    </class>

</hibernate-mapping>
