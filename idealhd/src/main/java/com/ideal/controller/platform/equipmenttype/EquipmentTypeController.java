package com.ideal.controller.platform.equipmenttype;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.platform.repository.equipmenttype.EquipmentTypeModel;
import com.ideal.service.platform.equipmenttype.EquipmentTypeService;
@Controller
public class EquipmentTypeController
{
    private Logger log = Logger.getLogger(EquipmentTypeController.class);
    @RequestMapping("initEquipmentTypePage.do")
    public String initEquipmentTypePage ( )
    {
        return "platform/equipmenttype/index";
    }
    @RequestMapping("getEquipmentTypeList.do")
    @ResponseBody
    public Map getEquipmentTypeList ( Integer start, Integer limit, EquipmentTypeModel model )
    {
        
        log.info(model.toString());
        EquipmentTypeService service = new EquipmentTypeService();
        return service.getEquipmentTypeList( start, limit, model, Constants.IEAI_OPM);
    }
    
    @RequestMapping("getTypeForSelect.do")
    @ResponseBody
    public Map getTypeForSelect(HttpServletRequest request, EquipmentTypeModel model ) {
        EquipmentTypeService service = new EquipmentTypeService();
        return service.getEquipmentTypeListForSelect(model, Constants.IEAI_OPM);
    }
    
    @RequestMapping("saveEquipmentType.do")
    @ResponseBody
    public Map saveEquipmentType ( String jsonData )
    {
        EquipmentTypeService service = new EquipmentTypeService();
        Map map = new HashMap();
        try
        {
            map = service.saveEquipmentType(jsonData);
        } catch (Exception e)
        {
            log.error("保存失败",e);
            map.put("success", false);
            map.put("message", "保存失败");
        }
        return map;
    }
    @RequestMapping("deleteEquipmentType.do")
    @ResponseBody
    public Map deleteEquipmentType ( String deleteIds )
    {
        EquipmentTypeService service = new EquipmentTypeService();
        Map map = new HashMap();
        try
        {
            map = service.deleteEquipmentType(deleteIds);
        } catch (Exception e)
        {
            log.error("删除失败",e);
            map.put("success", false);
            map.put("message", "删除失败");
        }
        return map;
    }
}