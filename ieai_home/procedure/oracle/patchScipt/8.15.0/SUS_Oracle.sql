
-- 4.7.17
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_MD5' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_SUS_MD5	(IID NUMBER(19) NOT NULL,IPKGNAME VARCHAR(255), IMD5VALUE VARCHAR(255), IMD5FLAG NUMBER(2),IUPDATETIME  VARCHAR(255), CONSTRAINT PK_IEAI_SUS_MD5 PRIMARY KEY (IID)	)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
END;
/

-- 4.7.18
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BUTTERFLYDATA' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_BUTTERFLYDATA	(IID NUMBER(19) NOT NULL,VERSION VARCHAR(255), SYSNAME VARCHAR(255), APPLYUSER VARCHAR(255),APPLYUSERDEPART  VARCHAR(255),STARTTIME  VARCHAR(255),ENDTIME  VARCHAR(255),CREATETIME  VARCHAR(255),UPDATETIME  VARCHAR(255), HOWEMER  VARCHAR(255), STATE  VARCHAR(255), NATURE  VARCHAR(255), APPADMIN  VARCHAR(255), CHARGERUSER  VARCHAR(255),APPADMINPHONE  VARCHAR(255),CHARGERUSERPHONE  VARCHAR(255),CONSTRAINT PK_IEAI_BUTTERFLYDATA PRIMARY KEY (IID)	)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
END;
/

-- 4.7.19
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BUTTERFLYDATA' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_BUTTERFLYDATA	(IID NUMBER(19) NOT NULL,VERSION VARCHAR(255), SYSNAME VARCHAR(255), APPLYUSER VARCHAR(255),APPLYUSERDEPART  VARCHAR(255),STARTTIME  VARCHAR(255),ENDTIME  VARCHAR(255),CREATETIME  VARCHAR(255),UPDATETIME  VARCHAR(255), HOWEMER  VARCHAR(255), STATE  VARCHAR(255), NATURE  VARCHAR(255), APPADMIN  VARCHAR(255), CHARGERUSER  VARCHAR(255),APPADMINPHONE  VARCHAR(255),CHARGERUSERPHONE  VARCHAR(255),CONSTRAINT PK_IEAI_BUTTERFLYDATA PRIMARY KEY (IID)	)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
     
    SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_startTime';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_startTime', '', '大屏监控预计开始时间');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_endTime';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_endTime', '', '大屏监控预计结束时间');
		COMMIT;
	END	IF;
		
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_total';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_total', '', '大屏监控系统总数');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_refreshFrequency1';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_refreshFrequency1', '5', '大屏监控刷新频率');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_refreshFrequency2';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_refreshFrequency2', '2', '柱图刷新频率');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_refreshFrequency3';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_refreshFrequency3', '5', '实时输出标题刷新频率');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='bigScreenConfig_refreshFrequency4';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'bigScreenConfig_refreshFrequency4', '1', '实时输出内容刷新频率');
		COMMIT;
	END	IF;
    
    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_SYSORDER' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SUS_SYSORDER (IID NUMBER(19) NOT NULL,  IORDER NUMBER(19),   CONSTRAINT IEAI_SUS_SYSORDER PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

-- 4.7.20
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='EXAMINE_STATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD EXAMINE_STATE NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IAUDITORID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IAUDITORID NUMBER(19) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='REJECT_REASON';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD REJECT_REASON  VARCHAR2(4000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='EXAMINE_TIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD EXAMINE_TIME NUMBER(19) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS  FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEFUALT_FLOWSTART_IP' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_DEFUALT_FLOWSTART_IP(  IID  NUMBER(19) NOT NULL PRIMARY KEY,  ITASKID  NUMBER(19),  IINSTANCEID   NUMBER(19),  ISERNER       NUMBER(19),  STEPIID       NUMBER(19),  STEPNAME      VARCHAR2(255),  BIID          NUMBER(19),  MODELSTEPIID  NUMBER(19),  MODELSTEPNAME VARCHAR2(255),  IGROUPID      NUMBER(19),  IBUSSINESSID  NUMBER(19),  IHOSTNAME     VARCHAR2(255),  IIP           VARCHAR2(255),  IPORT         VARCHAR2(255),  ISYSTEMTYPE   VARCHAR2(255),  IENVID        NUMBER(19),  IENVNAME      VARCHAR2(255),  IMODELINSTNAME VARCHAR2(255),MODELSTEPMXID NUMBER(19))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='IBRANCH';
	IF LI_EXISTS = 0 THEN
	 		 LS_SQL := 'ALTER TABLE  IEAI_INSTANCEINFO_HIS ADD  IBRANCH  VARCHAR2(255)';
	     EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_DOUBLECHECK_EXECUSER' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SUS_DOUBLECHECK_EXECUSER (ITASKID NUMBER(19),ISTARTUSER VARCHAR2(255),IEXECUSER VARCHAR2(255))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
END;
/

-- 4.7.21
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_APM_CODE' AND COLUMN_NAME='IISTRUE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_APM_CODE ADD IISTRUE INTEGER';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_EMAILMARK' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SUS_EMAILMARK( IID NUMBER (19), IRUNINSTANCEIID NUMBER(19))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ISERIALNO';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ISERIALNO VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='INO';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD INO VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IDOPLACE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IDOPLACE VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IISSUPTPRDCSERV';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IISSUPTPRDCSERV VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ISETTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ISETTYPE VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IISSTOPSERVICE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IISSTOPSERVICE VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='INEEDUNINOTE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD INEEDUNINOTE VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IISREALTEVENT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IISREALTEVENT VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ICHANGEPLAN';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ICHANGEPLAN VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IGROUP';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IGROUP VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IWHYEMER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IWHYEMER VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IISSTOPSERVNOTE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IISSTOPSERVNOTE VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IYLSTOPBTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IYLSTOPBTIME VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IYLSTOPETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IYLSTOPETIME VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IMAINTENANCEMEMO';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IMAINTENANCEMEMO VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IRELATENO';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IRELATENO VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ISPECIFYMAN';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ISPECIFYMAN VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IMAINDOMAN';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IMAINDOMAN VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IOTHERDOMAN';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IOTHERDOMAN VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IOTHERETCMAN';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IOTHERETCMAN VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IPRIMEDEPTOPTERMINAL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IPRIMEDEPTOPTERMINAL VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IDDNOTE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IDDNOTE VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ICHANGEDORESULT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ICHANGEDORESULT VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IACTBTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IACTBTIME VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IACTETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IACTETIME VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IACTBUG';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IACTBUG VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IACTPAUSETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IACTPAUSETIME VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IREALOPTERMINAL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IREALOPTERMINAL VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IDORESULT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IDORESULT VARCHAR(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IORDERNUM';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IORDERNUM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IORDERNUM';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IORDERNUM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='IINCLUDETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD IINCLUDETIME NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ISAOMS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ISAOMS VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BFINCLUDE_SYSCOUNT' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_BFINCLUDE_SYSCOUNT(IBIID NUMBER(19),IPRJNAME VARCHAR2(255),ISYSNAME VARCHAR2(255),IBFVERSION VARCHAR2(255),ISAOMS VARCHAR2(255),IINCLUDETIME NUMBER(19),ISTARTTIME VARCHAR2(255),IENDTIME VARCHAR2(255),INATURE VARCHAR2(255))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
END;

/


-- 4.7.22
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BFINCLUDE_SYSCOUNT' AND COLUMN_NAME='IFAILEDCAUSE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BFINCLUDE_SYSCOUNT ADD IFAILEDCAUSE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;

/


-- 4.7.23
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IEAI_EXECUTION_STRATEGY';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IEAI_EXECUTION_STRATEGY NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='ICRON';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD ICRON  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_ODSPROPORTTION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_ODSPROPORTTION (IID NUMBER(19) NOT NULL,SYSTEM VARCHAR2(225),IPRJNAME VARCHAR2(225),FLOWNAME VARCHAR2(225),INSTANCENAME VARCHAR2(8),PROPORTTTION VARCHAR2(225),ICREATETIME NUMBER(19),IUPDATETIME NUMBER(19),CONSTRAINT PK_IEAI_ODSPROPORTTION PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SENDMESS_RECORD' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_SENDMESS_RECORD (IID NUMBER(19) NOT NULL,IRUNINSID NUMBER(19) ,ITASKID NUMBER(19) ,ITYPE NUMBER(2),ICREATETIME NUMBER(19),CONSTRAINT PK_IEAI_SENDMESS_RECORD PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_STARTINST_LOCK' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_SUS_STARTINST_LOCK(IID  NUMBER(19) PRIMARY KEY,IEAI_INSTANCE_VERSION  NUMBER(19),IVERSIONALIAS   VARCHAR2(255),IWORKITEMID   NUMBER(19))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='sendMessageFlag1_sus';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (17, ''sendMessageFlag1_sus'', ''0'', ''应用变更流程运行是否发送短信标识'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.serverip';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (18, ''hsm.properties.serverip'', ''***********'', ''发送短信平台地址ip'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.serverport';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (19, ''hsm.properties.serverport'', ''5488'', ''发送短信平台地址port'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	     			 				 
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.tcptype';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (20, ''hsm.properties.tcptype'', ''0'', ''TCP连接类型0为短连接'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.tcpretry';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (21, ''hsm.properties.tcpretry'', ''2'', ''TCP重复连接次数'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
										
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.tcpcount';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (22, ''hsm.properties.tcpcount'', ''1'', ''当TCP_TYPE=0时此处配置为1'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.buffersize';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (23, ''hsm.properties.buffersize'', ''2048'', ''TCP连接数据缓冲区'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.timeout';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (24, ''hsm.properties.timeout'', ''8000'', ''TCP等待接收数据时间毫秒'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;			
			
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.recvproc';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (25, ''hsm.properties.recvproc'', ''fpsp.base.TCPClientPro'', ''TCP接收数据代理处理数据类'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.sysid';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (26, ''hsm.properties.sysid'', ''XBUS'', ''应用系统代号，加密平台提供**************'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
			
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='hsm.properties.keylabel';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (27, ''hsm.properties.keylabel'', ''xxxx'', ''密钥标签，加密平台提供***************'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.address';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (28, ''config.properties.address'', ''**********'', ''获取秘钥地址ip'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.port';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (29, ''config.properties.port'', ''9052'', ''获取秘钥地址port'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.service_id';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (30, ''config.properties.service_id'', ''01230000000001'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.requester_id';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (31, ''config.properties.requester_id'', ''0154'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;			
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.channel_id';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (32, ''config.properties.channel_id'', ''96'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;			
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.version_id';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (33, ''config.properties.version_id'', ''01'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.yybs';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (34, ''config.properties.yybs'', ''017'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;							
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.cpdm';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (35, ''config.properties.cpdm'', ''9990001701'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;												
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.jydm';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (36, ''config.properties.jydm'', ''825000'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;												
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.srvid';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (37, ''config.properties.srvid'', ''004'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;																										
					
										
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.immedflag';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (38, ''config.properties.immedflag'', ''1'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;	
					
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.lastsndtime';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (39, ''config.properties.lastsndtime'', ''0'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.encodingformat';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (40, ''config.properties.encodingformat'', ''UTF-8'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;					
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.branchid';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (41, ''config.properties.branchid'', ''866'', ''固定值'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='messageContent_start_sus';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (42, ''messageContent_start_sus'', ''流程启动！业务系统[{sysName}]方案名称[{instNameInput}]启动人[{istartUserFullName}]'', ''流程启动发送短信内容'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
					
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='messageContent_end_sus';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (43, ''messageContent_end_sus'', ''流程结束！业务系统[{sysName}]方案名称[{instNameInput}]'', ''流程结束发送短信内容'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;		
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='messageContent_err_sus';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (44, ''messageContent_err_sus'', ''流程报错！业务系统[{sysName}]方案名称[{instNameInput}]发起人[{istartUser}]发起人[{iactName}]报错信息[{erroMess}]'', ''流程报错发送短信内容'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;	

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ISROLLBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;		
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ISROLLBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ISROLLBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;		

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='IJUDGEISBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IJUDGEISBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD ISROLLBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
				
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ISROLLBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='IJUDGEISBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IJUDGEISBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
					
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD ISROLLBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME='IJUDGEISBACK';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD IJUDGEISBACK INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_RUN_INSTANCE' AND COLUMN_NAME='ISROLLBACK';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE TMP_RUN_INSTANCE ADD ISROLLBACK INTEGER DEFAULT 0';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_RUN_INSTANCE' AND COLUMN_NAME='IJUDGEISBACK';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE TMP_RUN_INSTANCE ADD IJUDGEISBACK INTEGER DEFAULT 0';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_DISPATCH_LIST' AND OBJECT_TYPE = 'TABLE';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SUS_DISPATCH_LIST ( IID NUMBER(19) NOT NULL, IINSTANCEID NUMBER(19), IINSTANCENAME VARCHAR2(255), IPARAMETER VARCHAR2(255), IPARAVALUE VARCHAR2(255), IDES VARCHAR2(255), CONSTRAINT PK_IEAI_SUS_DISPATCH_LIST PRIMARY KEY (IID))';
   		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
		
	commit;

END;

/


-- 4.7.24
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_IPMPDATA' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_IPMPDATA  (IID NUMBER(19) NOT NULL,VERSION VARCHAR(255), SYSNAME VARCHAR(255), APPLYUSER VARCHAR(255),APPLYUSERDEPART  VARCHAR(255),STARTTIME  VARCHAR(255),ENDTIME  VARCHAR(255),CREATETIME  VARCHAR(255),UPDATETIME  VARCHAR(255), HOWEMER  VARCHAR(255), STATE  VARCHAR(255), NATURE  VARCHAR(255), APPADMIN  VARCHAR(255), CHARGERUSER  VARCHAR(255),APPADMINPHONE  VARCHAR(255),CHARGERUSERPHONE  VARCHAR(255),ISERIALNO VARCHAR(255),INO VARCHAR(255),IDOPLACE VARCHAR(255),IISSUPTPRDCSERV VARCHAR(255),ISETTYPE VARCHAR(255),IISSTOPSERVICE VARCHAR(255),INEEDUNINOTE VARCHAR(255),IISREALTEVENT VARCHAR(255),ICHANGEPLAN VARCHAR(255),IGROUP VARCHAR(255),IWHYEMER VARCHAR(255),IISSTOPSERVNOTE VARCHAR(255),IYLSTOPBTIME VARCHAR(255),IMAINTENANCEMEMO VARCHAR(255),IRELATENO VARCHAR(255),ISPECIFYMAN VARCHAR(255),IMAINDOMAN VARCHAR(255),IOTHERDOMAN VARCHAR(255),IOTHERETCMAN VARCHAR(255),IPRIMEDEPTOPTERMINAL VARCHAR(255),IDDNOTE VARCHAR(255),ICHANGEDORESULT VARCHAR(255),IACTBTIME VARCHAR(255),IACTETIME VARCHAR(255),IACTBUG VARCHAR(255),IACTPAUSETIME VARCHAR(255),IREALOPTERMINAL VARCHAR(255),IDORESULT VARCHAR(255),IINCLUDETIME NUMBER(19),ISAOMS VARCHAR2(255),ICHANGESUMMARY VARCHAR2(255),CONSTRAINT PK_IEAI_IPMPDATA PRIMARY KEY (IID)	)';
   			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_BIGPKG_VERSION' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SUS_BIGPKG_VERSION(IID  NUMBER(19) not null,ICREATEDTIME  NUMBER(19,5) not null,IDEPLOYTASKID VARCHAR2(255),IURL          VARCHAR2(800),IUSER         VARCHAR2(255),IPASSWORD     VARCHAR2(255),ITYPE         INTEGER,ISATE         INTEGER,CONSTRAINT PK_IEAI_SUS_BIGPKG_VERSION PRIMARY KEY (IID))';
   			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_BIGPKG_ITEM' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SUS_BIGPKG_ITEM   (IID  NUMBER(19) not null, IEAI_SUS_BIGPKG_VERSION VARCHAR2(255), INAME        VARCHAR2(255) not null, IDX NUMBER(19,5) not null,CONSTRAINT PK_IEAI_SUS_BIGPKG_ITEM PRIMARY KEY (IID))';
   			EXECUTE IMMEDIATE LS_SQL;
		END IF;	

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEVOPS_RESOURCE_NATURE' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DEVOPS_RESOURCE_NATURE(IID NUMBER(19,0) NOT NULL,NATURE VARCHAR2(255),CONSTRAINT PK_IEAI_DEVOPS_NATURE PRIMARY KEY (IID))';
   			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEVOPS_RESOURCE_HOWEMER' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DEVOPS_RESOURCE_HOWEMER(IID NUMBER(19,0) NOT NULL,HOWEMER VARCHAR2(255),CONSTRAINT PK_IEAI_DEVOPS_HOWEMER PRIMARY KEY (IID))';
   			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEVOPS_AUDIT_DEPLOY' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_DEVOPS_AUDIT_DEPLOY(IID NUMBER(19,0) NOT NULL,NATURE VARCHAR2(255),HOWEMER VARCHAR2(255),DIGEST VARCHAR2(255),FLOWPATTERN VARCHAR2(255),CONSTRAINT PK_IEAI_DEVOPS_DEPLOY PRIMARY KEY (IID))';
   			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PUB_ENV' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_PUB_ENV(IID NUMBER(19) NOT NULL, IENVID NUMBER(19),CONSTRAINT PK_IEAI_PUB_ENV PRIMARY KEY (IID))';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_ISTANDERD_ENV' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_ISTANDERD_ENV(IID NUMBER(19) NOT NULL ,IENVID NUMBER(19),ISELECT INTEGER,CONSTRAINT PK_IEAI_ISTANDERD_ENV PRIMARY KEY (IID))';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_IBUS_ENV' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_IBUS_ENV(IID NUMBER(19) not null,IEAI_SUS_BUSNES_SYS VARCHAR2(255),IENVID NUMBER(19),CONSTRAINT PK_IEAI_IBUS_ENV PRIMARY KEY (IID))';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;			
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_MODELINFO' AND COLUMN_NAME='IEXECUSERENV';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SUS_MODELINFO  ADD IEXECUSERENV VARCHAR2 (255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
				
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BUSSINESS' AND COLUMN_NAME='IEXECUSERBUS';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SUS_BUSSINESS  ADD IEXECUSERBUS VARCHAR2 (255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUNINFO_DELAY' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_RUNINFO_DELAY(IID NUMBER(19) NOT NULL,IRUNID NUMBER(19) , ICREATETIME NUMBER(19), ISTATE NUMBER(10),ITIMEOUTTIMES NUMBER(19),IIP  VARCHAR2(255) ,CONSTRAINT PK_IEAI_RUNINFO_DELAY PRIMARY KEY (IID))';
		    EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='ISCHECKFLOW';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE  ADD ISCHECKFLOW NUMBER(1) DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='ISCHECKFLOW';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS  ADD ISCHECKFLOW NUMBER(1) DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS_DATA' AND COLUMN_NAME='ISCHECKFLOW';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS_DATA  ADD ISCHECKFLOW NUMBER(1) DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.properties.checklist.shell.path';  
			IF	LI_EXISTS = 0 THEN
			LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (55, ''config.properties.checklist.shell.path'', ''/opt/test/SUCHECK.sh'', ''浦发应用变更checklist检查流程壳脚本路径'')';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
		END	IF;
		
END;

/

-- 4.7.25
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_INSTANCEINFO01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_IEAI_INSTANCEINFO01 ON IEAI_INSTANCEINFO (IINSTANCEID ASC)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SUS_ENV_INFO01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_IEAI_SUS_ENV_INFO01 ON IEAI_SUS_ENV_INFO (IINSTANCEID ASC)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME01 ON IEAI_ACTRUNTIME (ITASKID ASC)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='ICOPYSTARTFLAG';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ICOPYSTARTFLAG INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='ICOPYSTARTFLAG';
	IF LI_EXISTS = 0 THEN
         LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD ICOPYSTARTFLAG INTEGER DEFAULT 0';
         EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_RUN_INSTANCE_HIS_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_IEAI_RUN_INSTANCE_HIS_01 ON IEAI_RUN_INSTANCE_HIS(ITASKID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CHECKLIST_TIMEOUT_INFO' AND OBJECT_TYPE = 'TABLE';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_CHECKLIST_TIMEOUT_INFO(IID NUMBER(19,0) NOT NULL,ITASKID NUMBER(19,0),INSTANCEID NUMBER(19,0),ISTARTTIME NUMBER(19,0),ISERVERIP VARCHAR2(255),ISYSNAME VARCHAR2(255),IVERSION VARCHAR2(255),ISTARTUSER VARCHAR2(255),INSTNAME VARCHAR2(255),ISFINISH INTEGER DEFAULT 0,ISTIMEOUT INTEGER DEFAULT 0,ISSEND INTEGER DEFAULT 0,CONSTRAINT PK_IEAI_CHECK_TIME PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='zhongyuanURL';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (45, ''zhongyuanURL'', ''中原银行接收变更发送信息地址'', ''中原银行接收变更发送信息地址'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='zhongyuanTimeout';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (46, ''zhongyuanTimeout'', ''10000'', ''中原银行接收变更发送信息超时时间'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_BUSINESS' AND COLUMN_NAME='ICOM_NAME';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_BUSINESS ADD ICOM_NAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='config.kfcs.devolps.product.time';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (47, ''config.kfcs.devolps.product.time'', ''30'', ''浦发kfcsDevolps同步生产数据等待时间'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	commit;
END;
/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IUSERNAME';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IUSERNAME  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IPASSWORD';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IPASSWORD  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IEAI_EXECUTION_STRATEGY';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IEAI_EXECUTION_STRATEGY  NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='ICOMPILETIME';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD ICOMPILETIME NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='ICOMPILEFINISH';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD ICOMPILEFINISH INTEGER';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='ICOMPILESERVER';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD ICOMPILESERVER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='ICOMPILECOUNT';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD ICOMPILECOUNT INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IPRIJECTNAMEPOC';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IPRIJECTNAMEPOC  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IMODELTYPENAME';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IMODELTYPENAME  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IDELETEFLAG';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IDELETEFLAG INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='IFLOWID';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD IFLOWID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_VERSION_MANAGER_COMP' AND OBJECT_TYPE = 'TABLE';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_VERSION_MANAGER_COMP(IID NUMBER(19) NOT NULL,MAINID NUMBER(19),ITARGETSERVER  VARCHAR2(255),ICHECKFLAG  INTEGER,IASSGINVERSION VARCHAR2(255),IBRANCHPATH  VARCHAR2(255),ICODESAVEPATH VARCHAR2(255),ITOOLS INTEGER,IMERGEFLAG  INTEGER,ICOMPILEFILEPATH  VARCHAR2(255),ICOMPILEFLAG INTEGER,ICOMPILETIME NUMBER(19),ICOMPILECYCLE VARCHAR2(255),IISOCHECKFLAG   INTEGER,IDOCKERPUSHPATH VARCHAR2(255),IPRETASK VARCHAR2(255),IPOSTTASK VARCHAR2(255),ISTESTCHECKFLAG   INTEGER,ITESTCONTENT VARCHAR2(4000),UPLOADFILELIST VARCHAR2(4000),CONSTRAINT PK_IEAI_VERSION_MANAGER_COMP PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 	 
	 	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_VERSION_MANAGER_REC' AND OBJECT_TYPE = 'TABLE';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_VERSION_MANAGER_REC(IID NUMBER(19) NOT NULL,MAINID NUMBER(19),SYSTYPE  INTEGER,PACKSERVER  VARCHAR2(255),COMPILEIP VARCHAR2(255),COMPILEPATH  VARCHAR2(255),COMPILEFILENAME VARCHAR2(255),UPLOADFILELIST VARCHAR2(4000),MODELNAME VARCHAR2(255),PACKAGEPATH  VARCHAR2(255),PACKAGENAME VARCHAR2(255),FTPTAG VARCHAR2(255), ITTIME   NUMBER(19),OUTPUTID  NUMBER(19), CONSTRAINT PK_IEAI_VERSION_MANAGER_REC PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
					
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER' AND COLUMN_NAME='ICOMMOND';
	IF	LI_EXISTS != 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER MODIFY (ICOMMOND VARCHAR2(4000) )';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_VERSION_MANAGER_MT' AND OBJECT_TYPE = 'TABLE';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_VERSION_MANAGER_MT(  iid         NUMBER(19) not null,  modeltypename VARCHAR2(255), CONSTRAINT PK_IEAI_VERSION_MANAGER_MT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
END;

/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE MODIFY (ISHELLSCRIPT VARCHAR2(800) )';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS MODIFY (ISHELLSCRIPT VARCHAR2(800) )';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		
END;

/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='IINSTANCETYPE2';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD IINSTANCETYPE2 INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='IINSTANCETYPE2';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD IINSTANCETYPE2 INTEGER DEFAULT 0';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ICALLINSTANCENAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ICALLINSTANCENAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ICALLPKGNAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ICALLPKGNAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ICALLVERSIONDES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ICALLVERSIONDES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ICALLSTARTTIMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ICALLSTARTTIMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO' AND COLUMN_NAME='ICALLSTARTENVNAMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO ADD ICALLSTARTENVNAMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ICALLINSTANCENAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ICALLINSTANCENAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ICALLPKGNAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ICALLPKGNAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ICALLVERSIONDES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ICALLVERSIONDES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ICALLSTARTTIMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ICALLSTARTTIMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCEINFO_HIS' AND COLUMN_NAME='ICALLSTARTENVNAMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD ICALLSTARTENVNAMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IINSTANCETYPE2';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IINSTANCETYPE2 INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLINSTANCENAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLINSTANCENAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLPKGNAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLPKGNAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLVERSIONDES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLVERSIONDES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLSTARTTIMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLSTARTTIMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLSTARTENVNAMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLSTARTENVNAMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLSTARTTIMESL';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLSTARTTIMESL NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLITASKID';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLITASKID NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ICALLIERRCLOBID';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ICALLIERRCLOBID NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IINSTANCETYPE2';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IINSTANCETYPE2 INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLINSTANCENAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLINSTANCENAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLPKGNAME';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLPKGNAME VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLVERSIONDES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLVERSIONDES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLSTARTTIMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLSTARTTIMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLSTARTENVNAMES';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLSTARTENVNAMES VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLSTARTTIMESL';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLSTARTTIMESL NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLITASKID';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLITASKID NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ICALLIERRCLOBID';
	IF	LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ICALLIERRCLOBID NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
END;

/


DECLARE
LS_SQL  VARCHAR2(2000);
LI_EXISTS  SMALLINT;
BEGIN  
   SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMINGSTART_VERSION' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_TIMINGSTART_VERSION
          (
            IID          NUMBER(19) NOT NULL PRIMARY KEY,
            ISTARTTIME   NUMBER(19),
            ISTATE       INTEGER DEFAULT 0,
            IVERSIONDES VARCHAR2(255),
            ISTARTSERVERIP VARCHAR2(255),
            ISTARTUSERID  NUMBER(19),
			IDISABLEUSERID  NUMBER(19),
			IBUSNESSYSNAME VARCHAR2(255)
          )';
    EXECUTE IMMEDIATE LS_SQL;
  END IF;
  
   
   SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMINGSTART_INFO' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_TIMINGSTART_INFO
          (
            IID          NUMBER(19) NOT NULL PRIMARY KEY,
            IVERSIONID   NUMBER(19),
            ISTARTTIME   NUMBER(19),
            ICONNER      NUMBER(19),
            ISTATE       INTEGER DEFAULT 0,
            IPACKAGENAME VARCHAR2(255),
            IPACKAGEPATH VARCHAR2(255),
            IRUNINSNAME VARCHAR2(255),
            ITASKID   NUMBER(19),
			IDISABLEUSERID  NUMBER(19),
			CLOB_IID1      NUMBER(19)
          )';
    EXECUTE IMMEDIATE LS_SQL;
  END IF;
  
  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CLOB_NEW' AND OBJECT_TYPE = 'TABLE';
  IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_CLOB_NEW
          (
            IID      NUMBER(19) NOT NULL PRIMARY KEY,
  			ICONTENT CLOB
          )';
    EXECUTE IMMEDIATE LS_SQL;
  END IF;
  
END;
/

DECLARE
LS_SQL  VARCHAR2(2000);
LI_EXISTS  SMALLINT;
BEGIN
	
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM COL WHERE TNAME = 'IEAI_TIMINGSTART_INFO' AND CNAME = 'IBUSNESSYSNAME';
	IF  LI_EXISTS = 0  THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMINGSTART_INFO ADD IBUSNESSYSNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM COL WHERE TNAME = 'IEAI_TIMINGSTART_INFO' AND CNAME = 'IVERSIONDES';
	IF  LI_EXISTS = 0  THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMINGSTART_INFO ADD IVERSIONDES VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='IDEPENDON';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD IDEPENDON INTEGER';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='ISTARTSTATUS';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD ISTARTSTATUS INTEGER';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='IDATE';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD IDATE NUMBER(19,5)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='ISVNURL';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD ISVNURL VARCHAR2(800)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='ISYSDIR';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD ISYSDIR VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='ISYSNUM';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD ISYSNUM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IDEPLOYERNAME';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IDEPLOYERNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IDEPLOYERID';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IDEPLOYERID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IDEVENAME';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IDEVENAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IDEVEID';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IDEVEID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IRELYPACKAGE';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IRELYPACKAGE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IRELYDIR';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IRELYDIR VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IRELYSYSTEM';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IRELYSYSTEM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='ISYSNAME';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD ISYSNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IFATHER_IID';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IFATHER_IID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_BIGPKG_SYSBAND' AND OBJECT_TYPE = 'TABLE';
	    IF LI_EXISTS = 0 THEN
	      LS_SQL := 'create table IEAI_SUS_BIGPKG_SYSBAND
			(
			  iid               NUMBER(19) not null,
			  iruninsname       VARCHAR2(255),
			  ibusnessysname    VARCHAR2(255),
			  iexternal_sysname VARCHAR2(255)
			)';
	    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
		
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IJSON';
	  IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IJSON  VARCHAR2(4000)';
			EXECUTE IMMEDIATE LS_SQL;
	  END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_DEPLOYFAIL_CODE' AND OBJECT_TYPE = 'TABLE';
	 	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_SUS_DEPLOYFAIL_CODE	(IID NUMBER(19) NOT NULL,ICODE number(19), IDETAIL VARCHAR2(255),CONSTRAINT PK_IEAI_SUS_DEPLOYFAIL_CODE PRIMARY KEY (IID)	)';
		EXECUTE IMMEDIATE LS_SQL;
		COMMIT;
	 END IF;
	 	 
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUNINS_FAILREASON' AND OBJECT_TYPE = 'TABLE';
   		IF LI_EXISTS = 0 THEN
        LS_SQL := '  CREATE TABLE IEAI_RUNINS_FAILREASON  (IID NUMBER(19) NOT NULL,IRUNINSID NUMBER(19), ICODE NUMBER(19),IDETAIL VARCHAR2(255),IMESSAGE CLOB,IFAILUSERID NUMBER(19),IFAILUSER VARCHAR2(255),CONSTRAINT PK_IEAI_RUNINS_FAILREASON PRIMARY KEY (IID)  )';
    	EXECUTE IMMEDIATE LS_SQL;
    	COMMIT;
   END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_DEPLOYFAIL_CODE  P WHERE P.ICODE='-1';  
		IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_DEPLOYFAIL_CODE(IID, ICODE, IDETAIL) VALUES (99999999,-1, '未知错误');
		COMMIT;
	END	IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 			
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IFROZEN';
	  IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IFROZEN  INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
	  END IF;	  
	  	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='zy.lockdate';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (60, ''zy.lockdate'', ''0'', ''中原预启任务自动合并线程锁数据用'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PKGSTART_VERSION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := '
		create table IEAI_PKGSTART_VERSION
		(
			iid                     NUMBER(19) not null,
			istarttime              NUMBER(19),
			istate                  INTEGER default 0,
			iversiondes             VARCHAR2(255),
			istartserverip          VARCHAR2(255),
			istartuserid            NUMBER(19),
			idisableuserid          NUMBER(19),
			ibusnessysname          VARCHAR2(255),
			ieai_sus_bigpkg_version NUMBER(19),
			CONSTRAINT PK_IEAI_PKGSTART_VERSION PRIMARY KEY (IID)
		)';
	    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PKGSTART_INFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := '
		create table IEAI_PKGSTART_INFO
		(
			iid            NUMBER(19) not null,
			iversionid     NUMBER(19),
			istarttime     NUMBER(19),
			iconner        NUMBER(19),
			istate         INTEGER default 0,
			ipackagename   VARCHAR2(255),
			ipackagepath   VARCHAR2(255),
			iruninsname    VARCHAR2(255),
			itaskid        NUMBER(19),
			idisableuserid NUMBER(19),
			clob_iid1      NUMBER(19),
			ibusnessysname VARCHAR2(255),
			iversiondes    VARCHAR2(255),
			ifather_iid    NUMBER(19),
			ipatch_path    VARCHAR2(850),
			CONSTRAINT PK_IEAI_PKGSTART_INFO PRIMARY KEY (IID)
		)';
	    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IPATCH_PATH';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IPATCH_PATH VARCHAR2(850)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IZIP_PATH';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IZIP_PATH VARCHAR2(850)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEVOPS_SYSTEM_RELATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_DEVOPS_SYSTEM_RELATION(
				IID NUMBER(19) NOT NULL,
				IDEVOPSID NUMBER(19),
				ISYSTEMID NUMBER(19),
				CONSTRAINT PK_IEAI_DEVOPS_SYSTEM_RELATION PRIMARY KEY (IID)
			   )';
	    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  commit;
END;
/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
     SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_RUN_INSTANCE' AND COLUMN_NAME='ISTATE';
     IF LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE TMP_RUN_INSTANCE ADD ISTATE NUMBER(2)';
          EXECUTE IMMEDIATE LS_SQL;
     END IF;
     
       SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IADVANCE';
     IF LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IADVANCE NUMBER(1) DEFAULT 0';
          EXECUTE IMMEDIATE LS_SQL;
     END IF;
     
            SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='ITSMID';
     IF LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD ITSMID NUMBER(19) ';
          EXECUTE IMMEDIATE LS_SQL;
     END IF;
     
                 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='ITSMID';
     IF LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD ITSMID NUMBER(19) ';
          EXECUTE IMMEDIATE LS_SQL;
     END IF;
     
          
END;
/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_SIGN SMALLINT;
BEGIN
            SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_AUTOSINGLE_ACT_01' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
					LS_SQL := 'CREATE INDEX IDX_IEAI_AUTOSINGLE_ACT_01 ON IEAI_AUTOSINGLE_ACT (REQID ASC, IACTSTATE DESC)';
					EXECUTE IMMEDIATE LS_SQL;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_01' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
					LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_01 ON IEAI_ITSM_AUTO_CHILD (ISTATE ASC, IP ASC, ITASKID ASC,IINSTANCEID ASC)';
					EXECUTE IMMEDIATE LS_SQL;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_02' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
					LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_02 ON IEAI_ITSM_AUTO_CHILD ( ITASKID ASC,IENDTIME ASC)';
					EXECUTE IMMEDIATE LS_SQL;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_03' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
					LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_03 ON IEAI_ITSM_AUTO_CHILD ( ITASKID ASC,ISTARTTIME ASC)';
					EXECUTE IMMEDIATE LS_SQL;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD_04' AND OBJECT_TYPE = 'INDEX';
					IF	LI_SIGN = 0 THEN
					LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD_04 ON IEAI_ITSM_AUTO_CHILD (  ITASKID ASC,IINSTANCEID ASC,ISTATE ASC)';
					EXECUTE IMMEDIATE LS_SQL;
			END	IF; 
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO' AND COLUMN_NAME='IWARNING_FLAG';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO ADD IWARNING_FLAG NUMBER(5) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IWARNING_FLAG';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IWARNING_FLAG NUMBER(5) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
END;
/ 

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='PRJNAME';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD PRJNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/ 


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_INSINFO_ERRREPORT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SUS_INSINFO_ERRREPORT(
				IID NUMBER(19) NOT NULL,
				IINFOIID NUMBER(19),
				IINFORUNINSID NUMBER(19),
				ISYSTEM VARCHAR2(255),
				IVERSION VARCHAR2(255),
				IVERSIONDESC VARCHAR2(255),
				IACTNAME VARCHAR2(255),
				ISTARTTIME NUMBER(19),
				IERRORTIME NUMBER(19),
				ISYSTYPE NUMBER(2,0),
				CONSTRAINT PK_IEAI_SUS_INSINFO_ERRREPORT PRIMARY KEY (IID)
			   )';
	    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  commit;
END;
/

create or replace trigger TRB_I_INSINFO_ERRREPORT
					AFTER UPDATE ON ieai_runinfo_instance
					REFERENCING NEW AS N
					FOR EACH ROW 
					DECLARE V_COUNT NUMBER;
					PRAGMA AUTONOMOUS_TRANSACTION;
					BEGIN
						IF :N.IISFAIL=1 THEN 
							INSERT INTO IEAI_SUS_INSINFO_ERRREPORT(IID,IINFOIID, IINFORUNINSID, ISYSTEM, IVERSION, IVERSIONDESC, IACTNAME, ISTARTTIME,IERRORTIME,ISYSTYPE)
								SELECT (SELECT FUN_GET_NEXT_PK('IEAI_SUS_INSINFO_ERRREPORT') as pk FROM IDUAL),
									:N.IID,
                                     B.IRUNINSID,
                                     A.ISYSNAME,
                                     A.IVERSION,
                                     A.IRUNINSNAME,
                                     :N.IACTNAME,
                                     :N.ISTARTTIME ,
									 FUN_GET_DATE_NUMBER_NEW(current_timestamp,8),
									 A.ISYSTYPE 
                              FROM IEAI_RUN_INSTANCE A LEFT JOIN IEAI_RUNINFO_INSTANCE B 
                                   ON A.IID=B.IRUNINSID 
                                   WHERE B.IID=:N.IID;
            
                                        COMMIT;
						END  IF;    
					END TRB_I_INSINFO_ERRREPORT;
/    


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='COORDINATECHANGESYSTEM';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD COORDINATECHANGESYSTEM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/ 

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='COORDINATECHANGESYSTEMCODE';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD COORDINATECHANGESYSTEMCODE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='CENTER';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD CENTER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/

-- 4.7.29
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IBUTTERFLYVERSION';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IBUTTERFLYVERSION VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IBUTTERFLYVERSION';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IBUTTERFLYVERSION VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	
END;
/

	
DECLARE
 LI_SIGN SMALLINT;
 LS_SQL  VARCHAR2(2000);
BEGIN 
	  SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ITSM_AUTO_CHILD01' AND OBJECT_TYPE = 'INDEX';
		IF	LI_SIGN = 0 THEN
			LS_SQL := 'CREATE INDEX IDX_IEAI_ITSM_AUTO_CHILD01 ON IEAI_ITSM_AUTO_CHILD (IINSTANCEID)';
			EXECUTE IMMEDIATE LS_SQL;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_ACTRUNTIME' AND OBJECT_TYPE = 'INDEX';
		IF	LI_SIGN = 0 THEN
			LS_SQL := 'CREATE INDEX IDX_IEAI_ACTRUNTIME ON IEAI_ACTRUNTIME (IERRORTASKID,IFLOWID,IACTNAME)';
			EXECUTE IMMEDIATE LS_SQL;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SHELLCMD_OUTPUT' AND OBJECT_TYPE = 'INDEX';
		IF	LI_SIGN = 0 THEN
			LS_SQL := 'CREATE INDEX IDX_IEAI_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT (IFLOWID,IFLOWNAME)';
			EXECUTE IMMEDIATE LS_SQL;
		END	IF;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	 
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='messageContent_srfh_sus';  
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (61, ''messageContent_srfh_sus'', ''模块名[变更发版] 任务名[{instanceName}] 任务操作概要[部署启动] 验证码[{authCode}]'', ''双人复核获取验证码发送短信内容'') ';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;	
		
	commit;

END;

/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='AUTOIMPL';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD AUTOIMPL VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='NONAUTOREASON';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD NONAUTOREASON VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_IPMPDATA' AND COLUMN_NAME='NONAUTOREASONOTHER';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_IPMPDATA ADD NONAUTOREASONOTHER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_PROCTOKFNKDATA' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SUS_PROCTOKFNKDATA(
				IID NUMBER(19) NOT NULL,
				IINSHISID NUMBER(19) NOT NULL,
				IVERSION VARCHAR2(255),
				ISYSNO VARCHAR2(255),
				ISYSTEM VARCHAR2(255),
				ISEMPTYPKG VARCHAR2(255),
				IALLDEPLOYSUCCESS VARCHAR2(255),
				IDEPLOYMENTES VARCHAR2(255),
				ISTARTTIME VARCHAR2(255),
				IENDTIME VARCHAR2(255),
				IPARTDEPLOYSUCCESS VARCHAR2(255),
				IALLDEPLOYMENTS VARCHAR2(255),
				IALLSTEPS VARCHAR2(255),
				IALLSTEPMANUALS VARCHAR2(255),
				IWASTTIME VARCHAR2(255),
				ISTATUS VARCHAR2(255),
				ISYNCTIME VARCHAR2(255),
				CONSTRAINT PK_IEAI_SUS_PROCTOKFNKDATA PRIMARY KEY (IID)
			   )';
	    EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  commit;
END;
/

-- V8.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_HOSTDEPLOYTY_ORDER' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SUS_HOSTDEPLOYTY_ORDER
						(
						  IID            NUMBER(19) PRIMARY KEY,
						  IBSYSID        NUMBER(19) not null,
						  IENVID         NUMBER(19) not null,
						  IRESGROUPID    NUMBER(19) not null,
						  IHOSTID        NUMBER(19) not null,
						  IDEPLOYTYPEIID NUMBER(19) not null,
						  IORDER         INTEGER not null
						)';
		    EXECUTE IMMEDIATE LS_SQL;
		  END IF;
		  
		  
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_COMMON_PARAM  P WHERE P.IPARAM_NAME='HDFB_ALL';  
		IF	LI_EXISTS = 0 THEN
			INSERT INTO IEAI_SUS_COMMON_PARAM (IID, IPARAM_VALUE, IISREADONLY, IPARAM_NAME)VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_COMMON_PARAM), null, 0, 'HDFB_ALL');
			COMMIT;
		END	IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_COMMON_PARAM  P WHERE P.IPARAM_NAME='HDFB_CUR';  
		IF	LI_EXISTS = 0 THEN
			INSERT INTO IEAI_SUS_COMMON_PARAM (IID, IPARAM_VALUE, IISREADONLY, IPARAM_NAME)VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_COMMON_PARAM), null, 0, 'HDFB_CUR');
			COMMIT;
		END	IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_COMMON_PARAM  P WHERE P.IPARAM_NAME='HDFB_UUID';  
		IF	LI_EXISTS = 0 THEN
			INSERT INTO IEAI_SUS_COMMON_PARAM (IID, IPARAM_VALUE, IISREADONLY, IPARAM_NAME)VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_COMMON_PARAM), null, 0, 'HDFB_UUID');
			COMMIT;
		END	IF;				
	  
	  commit;
END;
/

  DECLARE
    LS_SQL    VARCHAR2(3000);
    LI_EXISTS SMALLINT;
  BEGIN
  		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AZ_IP_RELATION' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_AZ_IP_RELATION(
					IID NUMBER(19) NOT NULL,
					IAZNAME VARCHAR2(255),
					IIPRANGE VARCHAR2(255),
					CONSTRAINT PK_IEAI_AZ_IP_RELATION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AZ_IP_INFO' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_AZ_IP_INFO(
					IID NUMBER(19) NOT NULL,
					IAZIID NUMBER(19),
					IAZIP VARCHAR2(255),
					CONSTRAINT PK_IEAI_AZ_IP_INFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AZ_F5_VERSION' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_AZ_F5_VERSION(
					IID NUMBER(19) NOT NULL,
					ISYSTEMNAME VARCHAR2(255),
					ILTMVSNAME VARCHAR2(255),
					ILTMVS VARCHAR2(255),
					ILTMPOOLNAME VARCHAR2(255),
					ILTMPOOLMEMBER VARCHAR2(255),
					IWAFVSNAME VARCHAR2(255),
					IWAFPOOLNAME VARCHAR2(255),
					IWAFPOOLMEMBER VARCHAR2(255),
					CONSTRAINT PK_IEAI_AZ_F5_VERSION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AZ_F5_INFO' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_AZ_F5_INFO(
					IID NUMBER(19) NOT NULL,
					IVERSIONID NUMBER(19),
					IPOOLMEMBER VARCHAR2(255),
					IMEMBERTYPE VARCHAR2(10),
					CONSTRAINT PK_IEAI_AZ_F5_INFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_AZSWITCHCONFIG' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_AZSWITCHCONFIG(IID   NUMBER(19) NOT NULL,IPROPERTYNAME  VARCHAR2(255),IPROPERTYVALUE VARCHAR2(255),IPROPERTYDESC VARCHAR2(255),CONSTRAINT PK_IEAI_AZSWITCHCONFIG PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
 END;
/

	
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IPMPORDER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IPMPORDER  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IPMPORDER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IPMPORDER  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINS_FAILREASON' AND COLUMN_NAME='IMESSAGE_SYS';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINS_FAILREASON ADD IMESSAGE_SYS CLOB';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINS_FAILREASON' AND COLUMN_NAME='IMESSAGE_PKG';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINS_FAILREASON ADD IMESSAGE_PKG CLOB';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;	

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINS_FAILREASON' AND COLUMN_NAME='ILOSTSYS';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINS_FAILREASON ADD ILOSTSYS NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINS_FAILREASON' AND COLUMN_NAME='ILOSTPKG';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUNINS_FAILREASON ADD ILOSTPKG  NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;				
END;
/	

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT COUNT(IBUTTONID) INTO LI_EXISTS  FROM IEAI_HIGHOPER  P WHERE P.IBUTTONID='3017';  
		IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(3017, '确认执行', 'processMonitorPersonExceptionExecSUS.do', '人工处理');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IMENUBUTTONID) INTO LI_EXISTS  FROM IEAI_MENU_BUTTON  P WHERE P.IMENUBUTTONID='3017';  
		IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(3017, 3004, 3017, '');
		COMMIT;
	END	IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_IWMODEL' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SUS_IWMODEL(IID   NUMBER(19) not null,INSERT_TYPE VARCHAR2(255), INSERT_FRONORPANDING    VARCHAR2(255),  INSERT_CONNER   VARCHAR2(255),  INSERT_SERNER VARCHAR2(255),INSERT_CONNERANDACTNAME VARCHAR2(255),INSERT_RESNAMES VARCHAR2(255),INSERT_VERSIONNAME  VARCHAR2(255),INSERT_CLOSESTEPFLAG    VARCHAR2(255),IACTNAME VARCHAR2(255),IINSTANCEID VARCHAR2(255),IINSTANCENAME VARCHAR2(255),INSERT_STAYPARAM  VARCHAR2(255),COMPUT_SERNER VARCHAR2(255),SUMMARY VARCHAR2(255),WEBINTERVAL VARCHAR2(255),IUSERID VARCHAR2(255),ALERTGROUP VARCHAR2(255), INSERT_FRONORPANDINGSTR VARCHAR2(255),ISYSNAME VARCHAR2(255), INSTNAME_INPUT  VARCHAR2(255),IVERSION VARCHAR2(255),IEAI_SUS_IWMODEL_BATCH  NUMBER(19),  ITASKID NUMBER(19),IRESID VARCHAR2(255),CONSTRAINT PK_IEAI_SUS_IWMODEL PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_IWMODEL_IP' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SUS_IWMODEL_IP(IID  NUMBER(19) not null,IEAI_SUS_IWMODEL NUMBER(19),IIP VARCHAR2(255),CONSTRAINT IEAI_SUS_IWMODEL_IP PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_PACKAGE_PATCH' AND COLUMN_NAME='ITASK_UUIID';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SUS_PACKAGE_PATCH ADD ITASK_UUIID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/



DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DEVOPS_SYSTEM_RELATION' AND COLUMN_NAME='IDIGESTCHECK';
		IF	LI_EXISTS = 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_DEVOPS_SYSTEM_RELATION ADD IDIGESTCHECK VARCHAR2(255) DEFAULT ''是''';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_EXECTORSYSTEM_RELATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_EXECTORSYSTEM_RELATION (SYSID NUMBER(19) NOT NULL,EXECTOR VARCHAR2(50), CONSTRAINT PK_IEAI_EXECTORSYSTEM_RELATION PRIMARY KEY (SYSID) )';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_EXECTORSYSTEM_RELATION' AND COLUMN_NAME='SYSNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_EXECTORSYSTEM_RELATION ADD SYSNAME  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;	
END;
/


-- ipmp缓存功能，浦发银行专用功能，清理butterflydata表
-- 浦发已废弃butterfly 所以这张表用于存储ipmp平台前后一周的数据 ，表结构与ipmpdata相同，
CREATE OR REPLACE PROCEDURE PROC_CLEAR_IPMP_CACHE IS
BEGIN
	EXECUTE IMMEDIATE 'TRUNCATE TABLE IEAI_BUTTERFLYDATA';
END PROC_CLEAR_IPMP_CACHE;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='ICHANGESUMMARY';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD ICHANGESUMMARY VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='PRJNAME';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD PRJNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='COORDINATECHANGESYSTEM';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD COORDINATECHANGESYSTEM VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/ 


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='COORDINATECHANGESYSTEMCODE';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD COORDINATECHANGESYSTEMCODE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='CENTER';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD CENTER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='AUTOIMPL';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD AUTOIMPL VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='NONAUTOREASON';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD NONAUTOREASON VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUTTERFLYDATA' AND COLUMN_NAME='NONAUTOREASONOTHER';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_BUTTERFLYDATA ADD NONAUTOREASONOTHER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IIPMPCACHECODE';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IIPMPCACHECODE VARCHAR2(10)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IIPMPCACHECODE';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IIPMPCACHECODE VARCHAR2(10)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_MENU  P WHERE P.IID='3549';  
		IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(3549, 'DevOps默认执行人导入', 3, 'susDevopsExectorIndex.do', 11, null,'images/info67.png');
		END	IF;
		COMMIT;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='ILCBP';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD ILCBP VARCHAR2(10)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='ILCBP';
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD ILCBP VARCHAR2(10)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_MENU  P WHERE P.IID='3550';  
		IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(3550, 'devops流转环境配置', 3, 'susDevopsCirculEnvConfigIndex.do', 12, null,'images/info67.png');
		END	IF;
		COMMIT;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEVOPS_ENVCONFIG' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_DEVOPS_ENVCONFIG (IID NUMBER(19, 0) NOT NULL,ICIRCULENV VARCHAR2(50), ICIRCULIP VARCHAR2(15) NOT NULL, ICIRCULPORT VARCHAR2(15) NOT NULL,ISYNCFILEPORT VARCHAR2(15) NOT NULL,CONSTRAINT PK_IEAI_DEVOPS_ENVCONFIG PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_ENVCONFIGSYS_RELATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_ENVCONFIGSYS_RELATION (IID NUMBER(19, 0) NOT NULL,ENVCONFIGID NUMBER(19, 0) NOT NULL,ISYSID NUMBER(19, 0) NOT NULL,CONSTRAINT PK_IEAI_ENVCONFIGSYS_RELATION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_TASK' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_TASK (IID NUMBER (19)   NOT NULL,ISVN VARCHAR2 (800)  ,ICREATETIME NUMBER (19)  ,ISVNUSER VARCHAR2 (255)  ,ISVNPASSWORD VARCHAR2 (255)  ,IVERSION VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_TASK PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_FILE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_FILE (IID NUMBER (19)   NOT NULL,IEAI_HLJ_TASK NUMBER (19)  ,IFILENAME VARCHAR2 (255)  ,ILOCAL_PATH VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_FILE PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_MQI' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_MQI (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IJYLX VARCHAR2 (255)  ,IMCDM VARCHAR2 (255)  ,ISSHJ VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IQYPCH VARCHAR2 (255)  ,IMBHJ VARCHAR2 (255)  ,IDBMC VARCHAR2 (255)  ,IBSZKM VARCHAR2 (255)  ,IMBIP VARCHAR2 (255)  ,IMBYH VARCHAR2 (255)  ,IMBMM VARCHAR2 (255)  ,IZT VARCHAR2 (255)  ,IJCLX VARCHAR2 (255)  ,IJCZD VARCHAR2 (255)  ,IBYZD1 VARCHAR2 (255)  ,IBYZD2 VARCHAR2 (255)  ,IBYZD3 VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_MQI PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_RPG' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_RPG (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IBYLX VARCHAR2 (255)  ,IKFYDMSZK VARCHAR2 (255)  ,IYDMSZK VARCHAR2 (255)  ,IYMSZWJ VARCHAR2 (255)  ,IRPGCXMC VARCHAR2 (255)  ,ISFXZ VARCHAR2 (255)  ,ICS1 VARCHAR2 (255)  ,ICS1Z VARCHAR2 (255)  ,ICS2 VARCHAR2 (255)  ,ICS2Z VARCHAR2 (255)  ,IMBBYK VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IDBMC VARCHAR2 (255)  ,IBSZKM VARCHAR2 (255)  ,ISCMBSZK VARCHAR2 (255)  ,IBZHJYMK VARCHAR2 (255)  ,IBZHJYMWJ VARCHAR2 (255)  ,IMBIP VARCHAR2 (255)  ,IMBYH VARCHAR2 (255)  ,IMBMM VARCHAR2 (255)  ,IZTBZ VARCHAR2 (255)  ,IBYZD1 VARCHAR2 (255)  ,IBYZD2 VARCHAR2 (255)  ,IBYZD3 VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_RPG PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_BZSJQY' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_BZSJQY (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IISJM VARCHAR2 (255)  ,IISJSZK VARCHAR2 (255)  ,IBFSJSZK VARCHAR2 (255)  ,IBFSJBSZK VARCHAR2 (255)  ,IBFSJBM VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IXH VARCHAR2 (255)  ,IZZBM VARCHAR2 (255)  ,ICZLX VARCHAR2 (255)  ,IBFWJ VARCHAR2 (255)  ,IDBMC VARCHAR2 (255)  ,IBSZK VARCHAR2 (255)  ,ISCSJSZK VARCHAR2 (255)  ,IJBSZK VARCHAR2 (255)  ,IBYZD1 VARCHAR2 (255)  ,IBYZD2 VARCHAR2 (255)  ,IBYZD3 VARCHAR2 (255)  ,IBYRQ VARCHAR2 (255)  ,ICZWJSZK VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_BZSJQY PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_MQYHT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_MQYHT (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_MQYHT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_RPGHT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_RPGHT (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_RPGHT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_BZSJQYHT' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_BZSJQYHT (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_BZSJQYHT PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_MQI_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_MQI_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IJYLX VARCHAR2 (255)  ,IMCDM VARCHAR2 (255)  ,ISSHJ VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IQYPCH VARCHAR2 (255)  ,IMBHJ VARCHAR2 (255)  ,IDBMC VARCHAR2 (255)  ,IBSZKM VARCHAR2 (255)  ,IMBIP VARCHAR2 (255)  ,IMBYH VARCHAR2 (255)  ,IMBMM VARCHAR2 (255)  ,IZT VARCHAR2 (255)  ,IJCLX VARCHAR2 (255)  ,IJCZD VARCHAR2 (255)  ,IBYZD1 VARCHAR2 (255)  ,IBYZD2 VARCHAR2 (255)  ,IBYZD3 VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  ,IDELTED NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_MQI_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_RPG_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_RPG_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IBYLX VARCHAR2 (255)  ,IKFYDMSZK VARCHAR2 (255)  ,IYDMSZK VARCHAR2 (255)  ,IYMSZWJ VARCHAR2 (255)  ,IRPGCXMC VARCHAR2 (255)  ,ISFXZ VARCHAR2 (255)  ,ICS1 VARCHAR2 (255)  ,ICS1Z VARCHAR2 (255)  ,ICS2 VARCHAR2 (255)  ,ICS2Z VARCHAR2 (255)  ,IMBBYK VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IDBMC VARCHAR2 (255)  ,IBSZKM VARCHAR2 (255)  ,ISCMBSZK VARCHAR2 (255)  ,IBZHJYMK VARCHAR2 (255)  ,IBZHJYMWJ VARCHAR2 (255)  ,IMBIP VARCHAR2 (255)  ,IMBYH VARCHAR2 (255)  ,IMBMM VARCHAR2 (255)  ,IZTBZ VARCHAR2 (255)  ,IBYZD1 VARCHAR2 (255)  ,IBYZD2 VARCHAR2 (255)  ,IBYZD3 VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  ,IDELTED NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_RPG_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_BZSJQY_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_BZSJQY_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IISJM VARCHAR2 (255)  ,IISJSZK VARCHAR2 (255)  ,IBFSJSZK VARCHAR2 (255)  ,IBFSJBSZK VARCHAR2 (255)  ,IBFSJBM VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IXH VARCHAR2 (255)  ,IZZBM VARCHAR2 (255)  ,ICZLX VARCHAR2 (255)  ,IBFWJ VARCHAR2 (255)  ,IDBMC VARCHAR2 (255)  ,IBSZK VARCHAR2 (255)  ,ISCSJSZK VARCHAR2 (255)  ,IJBSZK VARCHAR2 (255)  ,IBYZD1 VARCHAR2 (255)  ,IBYZD2 VARCHAR2 (255)  ,IBYZD3 VARCHAR2 (255)  ,IBYRQ VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  ,IDELTED NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_BZSJQY_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_MQYHT_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_MQYHT_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  ,IDELTED NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_MQYHT_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_RPGHT_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_RPGHT_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  ,IDELTED NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_RPGHT_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_BZSJQYHT_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_BZSJQYHT_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,IQYRQ VARCHAR2 (255)  ,IPCH VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  ,IDELTED NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_BZSJQYHT_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_BGJL_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_BGJL_HIS (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,ILSH VARCHAR2 (255)  ,IFZR VARCHAR2 (255)  ,IFATHER NUMBER (19)  ,IGROUPS NUMBER (19)  , CONSTRAINT PK_IEAI_HLJ_SHT_BGJL_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HLJ_SHT_BGJL' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_HLJ_SHT_BGJL (IID NUMBER (19)   NOT NULL,IEAI_HLJ_FILE NUMBER (19)  ,ISHEETNAME VARCHAR2 (255)  ,ILSH VARCHAR2 (255)  ,IFZR VARCHAR2 (255)  , CONSTRAINT PK_IEAI_HLJ_SHT_BGJL PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
							
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DOUBLECHECK_C_IWC' AND OBJECT_TYPE = 'INDEX';
			IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLECHECK_C_IWC  ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID,ICOLHEADER)';
			EXECUTE IMMEDIATE LS_SQL;
		END	IF; 	
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_DOUBLECHECK_C_IWCH' AND OBJECT_TYPE = 'INDEX';
			IF	LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE INDEX IDX_IEAI_DOUBLECHECK_C_IWCH ON IEAI_DOUBLECHECK_COLVALUE_HIS (IWORKITEMID,ICOLHEADER)';
			EXECUTE IMMEDIATE LS_SQL;
		END	IF;  

	           SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_MPS' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_EXISTS = 0 THEN
                                LS_SQL := 'create index IDX_MPS on IEAI_MENU_PERMISSIONS (IMENUID, IROLEID, IPERMISSION)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;


	          SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_MENU_IPG' AND OBJECT_TYPE = 'INDEX';
                            IF	LI_EXISTS = 0 THEN
                                LS_SQL := 'create index IDX_MENU_IPG on IEAI_MENU (IID, IPARENTNAME, IGROUPMESSID)';
                                EXECUTE IMMEDIATE LS_SQL;
                            END	IF;	
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_HLJ_SHT_BZSJQY_HIS' AND COLUMN_NAME='ICZWJ' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_HLJ_SHT_BZSJQY_HIS ADD ICZWJ VARCHAR2(225)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_HLJ_SHT_BZSJQY_HIS' AND COLUMN_NAME='ICZWJSZK' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_HLJ_SHT_BZSJQY_HIS ADD ICZWJSZK VARCHAR2(225)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_HLJ_SHT_BZSJQY' AND COLUMN_NAME='ICZWJ' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_HLJ_SHT_BZSJQY ADD ICZWJ VARCHAR2(225)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='IFLAG'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD IFLAG VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_APOLLOJSON' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_APOLLOJSON (IID NUMBER (19)   NOT NULL,ITASKID NUMBER (19)  ,IVERSIONDESC VARCHAR2 (255)  ,ILOGINNAME VARCHAR2 (255)  ,IENVABB VARCHAR2 (255)  ,ICONTENT CLOB   , CONSTRAINT PK_IEAI_SUS_APOLLOJSON PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_APOLLOJSON' AND COLUMN_NAME='IAPPID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_APOLLOJSON ADD IAPPID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_APOLLOJSON' AND COLUMN_NAME='ICLUSTERNAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_APOLLOJSON ADD ICLUSTERNAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT;
				
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_FORBIDDEN_VERSION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
        	LS_SQL := '	CREATE TABLE IEAI_SUS_FORBIDDEN_VERSION (IID NUMBER(19, 0) NOT NULL,IFVERSION VARCHAR2(255) NOT NULL,IFORBIDDENTIME VARCHAR2(255),CONSTRAINT PK_IEAI_SUS_FORBIDDEN_VERSION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
		COMMIT;

END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_ITEM' AND COLUMN_NAME='IFLAG'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_ITEM ADD IFLAG VARCHAR2(20) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_IWMODEL' AND COLUMN_NAME='IENVID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_IWMODEL ADD IENVID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_IWMODEL' AND COLUMN_NAME='IENVID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_IWMODEL ADD IENVID VARCHAR2(225) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_IWMODEL_IP' AND COLUMN_NAME='IRESID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_IWMODEL_IP ADD IRESID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
LS_SQL  VARCHAR2(2000);
LI_EXISTS  SMALLINT;
BEGIN
    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BUS_EXCEL' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
	LS_SQL := ' CREATE TABLE IEAI_BUS_EXCEL( IID   NUMBER(19) NOT NULL, IBUSSYSNAME VARCHAR2(255), IEXCELNAME  VARCHAR2(255) )';
    EXECUTE IMMEDIATE LS_SQL;
  END IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='IYWXTIID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD IYWXTIID VARCHAR2(225) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_TT_SKEPUT' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SUS_TT_SKEPUT (IID NUMBER (19) NOT NULL,ITASKID NUMBER(19) NOT NULL,ISKEPUT NUMBER(1) DEFAULT -1,IUSERID NUMBER(19),IFULLNAME VARCHAR2(255),CONSTRAINT PK_IEAI_SUS_TT_SKEPUT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 COMMIT;
END;
/




DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_PUB_INFO' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_PUB_INFO (IID NUMBER (19),IPUBDESC VARCHAR2 (255),IPUBIP VARCHAR2 (255),IPUBPORT NUMBER (5), CONSTRAINT PK_IEAI_SUS_PUB_INFO PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PUB_ENV_RELATION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_PUB_ENV_RELATION (IID NUMBER (19),IPUBID NUMBER (19),IENVID NUMBER (19), CONSTRAINT PK_IEAI_PUB_ENV_RELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM' AND COLUMN_NAME='IEXECGROUP'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD IEXECGROUP VARCHAR2(225) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM' AND COLUMN_NAME='IMANGROUP'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD IMANGROUP VARCHAR2(225) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLUMN_NAME='IEXECGROUP'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD IEXECGROUP VARCHAR2(225) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLUMN_NAME='IMANGROUP'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD IMANGROUP VARCHAR2(225) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_ITSM_VNUMB' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'create table IEAI_SUS_ITSM_VNUMB( iid    NUMBER(19) not null, susnumb    VARCHAR2(255), createtime DATE, eapsid    VARCHAR2(255))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_GROUP_ENV' AND COLUMN_NAME='IALLOWSYNCPROC' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_GROUP_ENV ADD IALLOWSYNCPROC VARCHAR2(1)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_PUB_INFO' AND COLUMN_NAME='ISYSTEMTYPE' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_PUB_INFO ADD ISYSTEMTYPE NUMBER(1)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
		END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DEVOPS_RESOURCE_NATURE' AND COLUMN_NAME='NODE' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_DEVOPS_RESOURCE_NATURE ADD NODE VARCHAR2(2)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DEVOPS_RESOURCE_HOWEMER' AND COLUMN_NAME='NODE' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_DEVOPS_RESOURCE_HOWEMER ADD NODE VARCHAR2(2)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
END;
/







DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_APOLLOCLOUD_VERSION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_APOLLOCLOUD_VERSION (IID NUMBER (19)   NOT NULL,ISRETRY VARCHAR2 (255)  ,ENGINEVERSION VARCHAR2 (255)  ,TOPFLOWINSTID VARCHAR2 (255)  ,IDENTIFIERID VARCHAR2 (255)  ,OPERATORINFO VARCHAR2 (255)  ,FLOWCOMPONENTINSTID VARCHAR2 (255)  ,FLOWINSTID VARCHAR2 (255)  ,REQUESTID VARCHAR2 (255)  ,ACTION VARCHAR2 (255)  ,CALLBACKURL VARCHAR2 (255)  ,SECURITYKEY VARCHAR2 (255)  ,RETRY VARCHAR2 (255)  ,STATUS VARCHAR2 (255)  ,STATE NUMBER (19)  ,INSERTTIME VARCHAR2 (20)  ,LOCALDOWNLOADPATH VARCHAR2 (255)  , CONSTRAINT PK_IEAI_APOLLOCLOUD_VERSION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_APOLLOCLOUD_ONEITEM' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_APOLLOCLOUD_ONEITEM (IID NUMBER (19)   NOT NULL,APOLOCLOUDVERSIONID NUMBER (19)  ,ISFIXCOMPONENT VARCHAR2 (255)  ,PACKAGE_URL VARCHAR2 (255)  ,PACKAGE_NAME VARCHAR2 (255)  ,SIGNINFO VARCHAR2 (255)  ,NAMEINFO VARCHAR2 (255)  ,SYS_NAME VARCHAR2 (255)  ,ENV VARCHAR2 (255)  ,STATE NUMBER (19)  ,INSERTTIME VARCHAR2 (20)  , CONSTRAINT PK_IEAI_APOLLOCLOUD_ONEITEM PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_APOLLOCLOUD_TWOITEM' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_APOLLOCLOUD_TWOITEM (IID NUMBER (19)   NOT NULL,APOLOCLOUDVERSIONID NUMBER (19)  ,PIPELINESETID VARCHAR2 (255)  ,PACKAGE_URL VARCHAR2 (255)  ,ENGINE_PIPELINE_ID VARCHAR2 (255)  ,SIGNINFO VARCHAR2 (255)  ,ENGINE_PIPELINE_PORTAL_URL VARCHAR2 (255)  ,ENV VARCHAR2 (255)  ,ENGINE_PIPELINE_CONFIG_ID VARCHAR2 (255)  ,OPERATORINFO VARCHAR2 (255)  ,REFOBJTYPE VARCHAR2 (255)  ,ISFIXCOMPONENT VARCHAR2 (255)  ,COMPANYID VARCHAR2 (255)  ,ENGINE_PIPELINE_NAME VARCHAR2 (255)  ,ENGINE_PIPELINE_INST_NUMBER VARCHAR2 (255)  ,PIPELINESETNAME VARCHAR2 (255)  ,PACKAGE_NAME VARCHAR2 (255)  ,NAMEINFO VARCHAR2 (255)  ,SYS_NAME VARCHAR2 (255)  ,PROJECTID VARCHAR2 (255)  ,ENGINE_PIPELINE_INST_ID VARCHAR2 (255)  ,STATE NUMBER (19)  ,INSERTTIME VARCHAR2 (20)  , CONSTRAINT PK_IEAI_APOLLOCLOUD_TWOITEM PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLUMN_NAME='IUUID' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD IUUID VARCHAR2(255)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PUB_PROXY_RELATION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_PUB_PROXY_RELATION (IID NUMBER (19) NOT NULL,IPUBID NUMBER (19),IPROXYID NUMBER (19), CONSTRAINT PK_IEAI_PUB_PROXY_RELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_GROUP_EXEC_STRATEGY' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_GROUP_EXEC_STRATEGY  (  IID NUMBER(19) NOT NULL, CRON VARCHAR2(255 ) NOT NULL, ISTATE NUMBER(4) DEFAULT 1, SERVERIP VARCHAR2(25 ) NULL, JOBCLASS VARCHAR2(500) NULL ,PRESTARTTIME NUMBER(19) , IFLOWID NUMBER(19),IDBTYPE NUMBER(5),PRESTARTTIMESTR varchar2(100) , CONSTRAINT PK_IEAI_GROUP_EXEC_STRATEGY PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DEVOPS_AUDIT_PAGE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_DEVOPS_AUDIT_PAGE (IID NUMBER (19) NOT NULL,NATURE VARCHAR2 (255),HOWEMER VARCHAR2 (255),DIGEST VARCHAR2 (255),FLOWPATTERN VARCHAR2 (255), CONSTRAINT PK_IEAI_DEVOPS_AUDIT_PAGE PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_PUB_INFO' AND COLUMN_NAME='IPUBNETTYPE' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_PUB_INFO ADD IPUBNETTYPE NUMBER(1)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
		END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_PUB_INFO' AND COLUMN_NAME='IPUBSERVERIP' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_PUB_INFO ADD IPUBSERVERIP  VARCHAR2(255)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;
		END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO' AND COLUMN_NAME='IIGNORESTEPS' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO ADD IIGNORESTEPS VARCHAR2(2000)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_TT_SKEPUT' AND COLUMN_NAME='ISKEPENV' ;
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_TT_SKEPUT ADD ISKEPENV VARCHAR2(255)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		commit;END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_GD_EXPORT_DIR' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_GD_EXPORT_DIR (IID NUMBER (19),IINSTACHENAME VARCHAR2 (255),IVERSIONALIAS VARCHAR2 (255),ICREATETIME NUMBER (19),ICREATEUSER VARCHAR2 (255), CONSTRAINT PK_IEAI_GD_EXPORT_DIR PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TOKEN_INFO' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_TOKEN_INFO (IID NUMBER (19) NOT NULL,ITOKENID NUMBER (19),IUSERID NUMBER (19), CONSTRAINT PK_IEAI_TOKEN_INFO PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='IBGTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD IBGTYPE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='ICQTASKID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ICQTASKID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IBGTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IBGTYPE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='ICQTASKID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD ICQTASKID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IBGTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IBGTYPE VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='ICQTASKID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD ICQTASKID VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_IINSTANC' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_IINSTANC (IID NUMBER (19)   NOT NULL,IINSTANCENAME VARCHAR2 (255)  ,IVERSIONALIAS VARCHAR2 (255)  ,INSTNAME_INPUT VARCHAR2 (255)  ,IDEPLOYMENTENV VARCHAR2 (255)  ,ISTATE VARCHAR2 (255)  ,ICREATEDATE NUMBER (19)  ,IDESC VARCHAR2 (255)  , CONSTRAINT PK_IEAI_IINSTANC PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUS_EXCEL' AND COLUMN_NAME='IROLLBACKEXCEL';
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_BUS_EXCEL ADD IROLLBACKEXCEL VARCHAR2(255)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_APOLLOCLOUD_VERSION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_APOLLOCLOUD_VERSION (IID NUMBER (19) ,ISRETRY VARCHAR2 (255) ,ENGINEVERSION VARCHAR2 (255) ,TOPFLOWINSTID VARCHAR2 (255) ,IDENTIFIERID VARCHAR2 (255) ,OPERATORINFO VARCHAR2 (255) ,FLOWCOMPONENTINSTID VARCHAR2 (255) ,FLOWINSTID VARCHAR2 (255) ,REQUESTID VARCHAR2 (255) ,ACTION VARCHAR2 (255) ,CALLBACKURL VARCHAR2 (255) ,SECURITYKEY VARCHAR2 (255) ,RETRY VARCHAR2 (255) ,STATUS VARCHAR2 (255) ,STATE NUMBER (19) ,INSERTTIME VARCHAR2 (20) ,LOCALDOWNLOADPATH VARCHAR2 (255) ,ITASKID NUMBER (19) , CONSTRAINT PK_IEAI_APOLLOCLOUD_VERSION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DOCKING_CMDB' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_DOCKING_CMDB (IID NUMBER (19)   NOT NULL,MODUL_TYPE VARCHAR2 (255)  ,BUSINESS_SYSTEM VARCHAR2 (255)  ,ENV VARCHAR2 (255)  ,IP VARCHAR2 (255)  ,SYSTYPE NUMBER (19)  ,STATUS NUMBER (19) DEFAULT 0
 , CONSTRAINT PK_IEAI_DOCKING_CMDB PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_CMDB_IP' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_CMDB_IP (IID NUMBER (19)   NOT NULL,IEAI_SUS_CMDB_MAIN NUMBER (19)  ,BUSINESSIP VARCHAR2 (255)  ,ENV VARCHAR2 (255)  ,FACILITY_USE_STATE VARCHAR2 (255)  ,SERVER_TYPE VARCHAR2 (255)  , CONSTRAINT PK_IEAI_SUS_CMDB_IP PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_CMDB_OPS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_CMDB_OPS (IID NUMBER (19)   NOT NULL,IEAI_SUS_CMDB_MAIN NUMBER (19)  ,IAD VARCHAR2 (255)  ,IEMAIL VARCHAR2 (255)  ,IFULLNAME VARCHAR2 (255)  ,IJOB_NUMBER VARCHAR2 (255)  ,IPHONE VARCHAR2 (255)  , CONSTRAINT PK_IEAI_SUS_CMDB_OPS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_CMDB_SYS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_CMDB_SYS (IID NUMBER (19)   NOT NULL,IEAI_SUS_CMDB_MAIN NUMBER (19)  ,IAD VARCHAR2 (255)  ,EMAIL VARCHAR2 (255)  ,FULLNAME VARCHAR2 (255)  ,JOB_NUMBER VARCHAR2 (255)  ,PHONE VARCHAR2 (255)  , CONSTRAINT PK_IEAI_SUS_CMDB_SYS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_CMDB_MAIN' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_CMDB_MAIN (IID NUMBER (19)   NOT NULL,INAME VARCHAR2 (255)  ,INO VARCHAR2 (255)  ,IOPSFLAG VARCHAR2 (255)  ,IORG VARCHAR2 (255)  ,IPARENT_NAME VARCHAR2 (255)  ,ISIMPLE_NAME VARCHAR2 (255)  ,ISYS_LEADER VARCHAR2 (255)  , CONSTRAINT PK_IEAI_SUS_CMDB_MAIN PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CQ_CMDB_HOST' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_CQ_CMDB_HOST (IID NUMBER (19) ,ENVNAME VARCHAR2 (255) ,SHORTNAME VARCHAR2 (255) ,RESNAME VARCHAR2 (255) ,IMODELTYPE VARCHAR2 (255) ,NAME VARCHAR2 (255) ,AGENTIP VARCHAR2 (255) ,IPORT VARCHAR2 (255) ,SYSTEMTYPE VARCHAR2 (255) ,SYSID NUMBER (19) ,ISTATE VARCHAR2 (255) ,ITIME TIMESTAMP(6)  , CONSTRAINT PK_IEAI_CQ_CMDB_HOST PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CQ_CMDB_JSON' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_CQ_CMDB_JSON (IID NUMBER (19) ,ITIME TIMESTAMP(6)  ,ISTATE VARCHAR2 (255) ,ICONTENT CLOB  , CONSTRAINT PK_IEAI_CQ_CMDB_JSON PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CQ_CMDB_SYS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_CQ_CMDB_SYS (IID NUMBER (19) ,ISYSNAME VARCHAR2 (255) ,FATHERSYSNAME VARCHAR2 (255) ,SYSSHORTNAME VARCHAR2 (255) ,SYSNO VARCHAR2 (255) ,JSONID NUMBER (19) ,ISTATE VARCHAR2 (255) ,ITIME TIMESTAMP(6)  , CONSTRAINT PK_IEAI_CQ_CMDB_SYS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_INSTANCE_VERSION_STA' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_INSTANCE_VERSION_STA (IID NUMBER (19)   NOT NULL,IINSTANCENAME VARCHAR2 (255)  ,IVERSIONALIAS VARCHAR2 (255)  ,INSTNAME_INPUT VARCHAR2 (255)  ,HIS_IID NUMBER (19)  ,ERROR_IID NUMBER (19)  ,RUN_IID NUMBER (19)  ,DOUBLE_IID NUMBER (19)  ,TIMING_IID NUMBER (19)  , CONSTRAINT PK_IEAI_INSTANCE_VERSION_STA PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LAST_VERSION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_LAST_VERSION (IID NUMBER (19)   NOT NULL,ISYSNO VARCHAR2 (255)  ,IENV VARCHAR2 (255)  ,IVERSION VARCHAR2 (255)   , CONSTRAINT PK_IEAI_LAST_VERSION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
		LS_SQL VARCHAR2(2000);
		LI_EXISTS SMALLINT;
	BEGIN 
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=29;
		IF  LI_EXISTS = 0 THEN
			LS_SQL :='INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(33,''gd_emergency'',''0'')';
			EXECUTE IMMEDIATE LS_SQL;
		END  IF;
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=29;
		IF  LI_EXISTS = 0 THEN
			LS_SQL :='INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(34,''gd_sycroTime'',''235900'')';
			EXECUTE IMMEDIATE LS_SQL;
		END  IF;

		COMMIT;
	END;
/

--8.14
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_RUN_INSTANCEINFO' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 1 THEN
        LS_SQL := 'DROP TABLE IEAI_SUS_RUN_INSTANCEINFO';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_RUN_INSTANCEINFO' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'create global temporary table IEAI_SUS_RUN_INSTANCEINFO
									(
									  iid               NUMBER(19) not null primary key,
									  iinstanceid       NUMBER(19),
									  iinstancename     VARCHAR2(255),
									  ieditversion      INTEGER,
									  iserner           NUMBER(19),
									  iconner           NUMBER(19),
									  iconnername       VARCHAR2(100),
									  iprener           VARCHAR2(255),
									  iactname          VARCHAR2(255),
									  iactdes           VARCHAR2(100),
									  iacttype          INTEGER,
									  ireminfo          VARCHAR2(255),
									  iipname           VARCHAR2(255),
									  imodeltype        VARCHAR2(100),
									  iip               VARCHAR2(50),
									  iport             INTEGER,
									  isystype          INTEGER,
									  iexecuser         VARCHAR2(100),
									  ishellscript      VARCHAR2(3000),
									  iisloadenv        INTEGER,
									  ishellpath        VARCHAR2(800),
									  itimeout          NUMBER(19),
									  iparameter        VARCHAR2(255),
									  iexpeceinfo       VARCHAR2(50),
									  iexceptinfo       VARCHAR2(100),
									  iredoable         INTEGER,
									  iisdisable        INTEGER,
									  ipkgname          VARCHAR2(800),
									  itype             INTEGER,
									  ipresysname       VARCHAR2(255),
									  ipreactname       VARCHAR2(1000),
									  imodelname        VARCHAR2(255),
									  imodelversion     NUMBER(19),
									  iscriptid         NUMBER(19),
									  imxgraphid        NUMBER(19),
									  itaskid           NUMBER(19),
									  iappflag          VARCHAR2(255),
									  inot_allowed_exec VARCHAR2(10),
									  isystemtypestep   VARCHAR2(255),
									  imodeltypestep    VARCHAR2(255),
									  singlerollback    VARCHAR2(255),
									  ibranch           VARCHAR2(255),
									  idisable          NUMBER(19),
									  ispaceiid         NUMBER(19),
									  igourpiid         NUMBER(19),
									  icfgfilechange    VARCHAR2(255),
									  iselfhealing      INTEGER default 0,
									  isrollback        INTEGER default 0,
									  iacttimeout       VARCHAR2(255),
									  iazname           VARCHAR2(255),
									  iproxyip          VARCHAR2(255),
									  iuuid             VARCHAR2(255),
									  icqtaskid         VARCHAR2(255),
									  ibgtype           VARCHAR2(255)
									)
on commit delete rows';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;	 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_REPORT_SYS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_REPORT_SYS (IID NUMBER (19)   NOT NULL,IUSER_ID NUMBER (19)  ,ISYS_ID NUMBER (19)  , CONSTRAINT PK_IEAI_REPORT_SYS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='ITAGDOWN'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD ITAGDOWN VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_BIGPKG_VERSION' AND COLUMN_NAME='MD5'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_BIGPKG_VERSION ADD MD5 VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER_MT' AND COLUMN_NAME='IBUSSNAME';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER_MT ADD IBUSSNAME VARCHAR2(255) ';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		COMMIT;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_VERSION_MANAGER_COMP' AND COLUMN_NAME='IREQUIREMENTNUMBER';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER_COMP ADD IREQUIREMENTNUMBER VARCHAR2(255) ';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		COMMIT;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESGROUP_PARAMS_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_RESGROUP_PARAMS_HIS (IID NUMBER (19) NOT NULL,IPARAMID NUMBER (19) ,IORIGINALNAME VARCHAR2 (255) ,IORIGINALVALUE VARCHAR2 (255) ,ICHANGENAME VARCHAR2 (255) ,ICHANGEVALUE VARCHAR2 (255) ,IUPDATEUSER VARCHAR2 (255) ,IUPDATETIME NUMBER (19) , CONSTRAINT PK_IEAI_RESGROUP_PARAMS_HIS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
