var paraViewWin = null;
Ext.onReady(function() {
			// 清理各种监听
			destroyRubbish();
			/**
			 * @desc 上传excel文件的formPanel
			 * 
			 */
			var upLoadformPane = Ext
					.create(
							'Ext.form.Panel',
							{
								cls:'customize_panel_back',
								width : Ext.get("uploadRelyExcel_gridArea").getWidth() - 30,
								frame : true,
								items : [
										{
											xtype : 'filefield',
											name : 'file', // 设置该文件上传空间的name，也就是请求参数的名字
											fieldLabel : '选择文件',
											labelWidth : 80,
											msgTarget : 'side',
											anchor : '100%',
											buttonText : '选择文件',
											width : Ext
													.get("uploadRelyExcel_gridArea")
													.getWidth() - 5
										}],
								buttons : [
										{
											text : '导入',
											margin: '0 0 0 5',
											handler : function() {
												var form = this.up('form')
														.getForm();
												var upfile = form.findField(
														"file").getValue();
												if (upfile == '') {
													Ext.Msg.alert('提示',
															"请选择文件...");
													return;
												}

												var hdtmpFilNam = form
														.findField("file")
														.getValue();
												if (!checkFile(hdtmpFilNam)) {
													form.findField("file")
															.setRawValue('');
													return;
												}
												if (form.isValid()) {
													Ext.MessageBox.wait(
															"数据处理中...", "进度条");
													form
															.submit({
																url : 'uploadRelyExcel.do',
																success : function(
																		form,
																		action) {
																	var msg = Ext
																			.decode(action.response.responseText).message;
																	Ext.Msg
																			.alert(
																					'提示',
																					msg);
																	return;
																},
																failure : function(
																		form,
																		action) {
																	secureFilterRsFrom(
																			form,
																			action);
																}
															});
												}
											}
										},
										{
											text : '重置',
											margin: '0 0 0 5',
											handler : function() {
												var form = this.up('form')
														.getForm();
												form.reset();
												return;
											}
										} ]
							});

			if (!paraViewWin) {
				paraViewWin = Ext.create('Ext.panel.Panel', {
//					closeAction : 'hide',
//					margin : '2 0 0 15px',
					border: false,
					layout:'fit',
					bodyPadding : 0,
					renderTo : "uploadRelyExcel_gridArea",
					width:contentPanel.getWidth(),
					bodyCls: 'service_platform_bodybg',
//					width : Ext.get("uploadRelyExcel_gridArea").getWidth()+50 ,
//					height : Ext.get("uploadRelyExcel_gridArea").getHeight()-10,
					items : [ upLoadformPane ]
				});
			}
			paraViewWin.show();

			// ---------------function------------------------

			function checkFile(fileName) {
				var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
				if (!file_reg.test(fileName)) {
					Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
					return false;
				}
				return true;
			}
			/** 窗口尺寸调节* */
			contentPanel.on('resize',
					function() {
						paraViewWin.setWidth(Ext.get("uploadRelyExcel_gridArea")
								.getWidth() );
						upLoadformPane.setWidth(Ext.get("uploadRelyExcel_gridArea")
								.getWidth() );
					})
			// 当页面即将离开的时候清理掉自身页面生成的组建
			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(paraViewWin);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});
		});
