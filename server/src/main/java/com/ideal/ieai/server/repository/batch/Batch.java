package com.ideal.ieai.server.repository.batch;

public class Batch
{
    private long iupperid ;
    private long iid ;
    private String iinstancename ;
    private String iversionalias ;
    private String instname_INPUT ;
    private long state ;
    private int start ;
    private int limit ;
    private String evnids ;
    
    public Batch ()
    {
       super() ;
    }
    
    public String getEvnids ()
    {
        return evnids;
    }

    public void setEvnids ( String evnids )
    {
        this.evnids = evnids;
    }

    public long getIid ()
    {
       return iid;
    }
    
    public int getStart ()
    {
       return start;
    }

    public void setStart (int start )
    {
       this.start=start;
    }

    public void setIid (long iid )
    {
       this.iid=iid;
    }

    public long getIupperid ()
    {
       return iupperid;
    }

    public void setIupperid (long iupperid )
    {
       this.iupperid=iupperid;
    }
    
    public String getIinstancename ()
    {
        return iinstancename;
    }

    public void setIinstancename ( String iinstancename )
    {
        this.iinstancename = iinstancename;
    }

    public String getIversionalias ()
    {
        return iversionalias;
    }

    public void setIversionalias ( String iversionalias )
    {
        this.iversionalias = iversionalias;
    }

    public String getInstname_INPUT ()
    {
        return instname_INPUT;
    }

    public void setInstname_INPUT ( String instname_INPUT )
    {
        this.instname_INPUT = instname_INPUT;
    }

    public long getState ()
    {
        return state;
    }

    public void setState ( long state )
    {
        this.state = state;
    }
    
    public int getLimit ()
    {
       return limit;
    }

    public void setLimit (int limit )
    {
       this.limit=limit;
    }
    
}
