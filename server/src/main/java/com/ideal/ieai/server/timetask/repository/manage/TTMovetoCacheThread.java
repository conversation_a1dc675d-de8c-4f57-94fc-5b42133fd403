package com.ideal.ieai.server.timetask.repository.manage;

import static org.quartz.CronScheduleBuilder.cronSchedule;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.impl.StdSchedulerFactory;

import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.cluster.monitor.TransCheckResultThread;
import com.ideal.ieai.server.ieaikernel.ServerEnv;

public class TTMovetoCacheThread extends Thread
{
    private static final Logger logger             = Logger.getLogger(TTMovetoCacheThread.class);
    ClearConfigManager manager  = new ClearConfigManager();

    public TTMovetoCacheThread()
    {

    }
    
    @Override
    public void run ()
    {
        if (Environment.getInstance().getBooleanConfig(Environment.TT_MOVETOCACHE_THREAD_SWITCH, false)){
           
                String crontext = Environment.getInstance().getSysConfig("tt.cleardatatime", "0 0 12 * * ?");
                JobDetail jobDetail = JobBuilder.newJob(ClearConfigJob.class).withIdentity("job_timetask", "group_timetask")
                        .build();
        
                Trigger trigger = TriggerBuilder.newTrigger()// 创建一个新的TriggerBuilder来规范一个触发器
                        .withIdentity("trigger_timetask", "group_timetask")// 给触发器一个名字和组名
                        //.startNow()
                        .withSchedule(cronSchedule(crontext))// 设置触发开始的时间 
                        .build();// 产生触发器
                Scheduler sched;
                try
                {
                    StdSchedulerFactory gSchedulerFactory = new StdSchedulerFactory();
                    sched = gSchedulerFactory.getScheduler();
                    sched.scheduleJob(jobDetail, trigger);
                    if (!sched.isStarted())
                    {
                        sched.start();
                    }
                } catch (SchedulerException e)
                {
                    logger.error("TTMovetoCacheThread class run method SchedulerException : " , e);
                }
            }

        
            
        
    }
}
