Ext.onReady(function () {
    var systemId=-1;
    destroyRubbish();
    Ext.tip.QuickTipManager.init();
    
    var ipInput = Ext.create('Ext.form.TextField', {
		name : 'ipAddress',
		fieldLabel : 'IP地址',
		margin : '5 0 0 10',
		labelWidth : 72,
		width : '60%',
		value : ipAdressVal,
		readOnly : true//isInspect == '1'
	});
	
	var hostNameInput = Ext.create('Ext.form.TextField', {
		name : 'hostNameInput',
		fieldLabel : '主机名',
		margin : '5 0 0 10',
		labelWidth : 72,
		width : '60%',
		value : hostNameVal,
		readOnly : true//isInspect == '1'
	});
	
	var sysNameInput = Ext.create('Ext.form.TextField', {
		name : 'sysNameInput',
		fieldLabel : '业务系统',
		margin : '5 0 0 10',
		labelWidth : 72,
		width : '60%',
		value : sysNameVal,
		readOnly : true//isInspect == '1'
	});
	
	var environInput = Ext.create('Ext.form.TextField', {
		name : 'environInput',
		fieldLabel : '环境',
		margin : '5 0 0 10',
		labelWidth : 72,
		width : '60%',
		value : environVal,
		readOnly : true//isInspect == '1'
	});
	
	var basicInfoPanel = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		layout : 'anchor',
		//height: '30%',
		//baseCls:'customize_gray_back',
		cls:'customize_panel_back',
		title : '基本信息',
		bodyCls:'x-docked-noborder-top',
		dockedItems : [
			{
				xtype : 'toolbar',
				//baseCls:'customize_gray_back',
				margin : '5 0 0 0',
				border : false,
				dock : 'top',
				items : [
				         ipInput				
				]
			},{
				xtype : 'toolbar',
				//baseCls:'customize_gray_back',
				margin : '5 0 0 0',
				border : false,
				dock : 'top',
				items : [
				         hostNameInput
				]
			},{
				xtype : 'toolbar',
				margin : '5 0 0 0',
				//baseCls:'customize_gray_back',
				border : false,
				dock : 'top',
				items : [
				         sysNameInput
				]
			},{
				xtype : 'toolbar',
				margin : '5 0 0 0',
				//baseCls:'customize_gray_back',
				border : false,
				dock : 'top',
				items : [
				         environInput
				]
			}
		]
		

	});
	
	
	
	
   
   var cpuGraphPanel = Ext.create('Ext.panel.Panel', {
		width : '33.3%',
		cls:'customize_panel_back',
		border: true,	
		//margin : '20 20 0 20',
		//title : '总览',
		html : '<div  id="cpugraph" style="width: 105%;height: 100%"></div>'
		
	});
	
	var memoryGraphPanel = Ext.create('Ext.panel.Panel', {
		width : '33.3%',
		cls:'customize_panel_back',
		border: true,	
		//margin : '20 20 0 20',
		//title : '总览',
		html : '<div  id="memorygraph" style="width: 105%;height: 100%"></div>'
		
	});
	
	var diskGraphPanel = Ext.create('Ext.panel.Panel', {
		width : '33.3%',
		cls:'customize_panel_back',
		border: true,	
		//margin : '20 20 0 20',
		//title : '总览',
		html : '<div  id="diskgraph" style="width: 105%;height: 100%"></div>'
		
	});
	
	var useRatePanel = Ext.create('Ext.panel.Panel', {
		border : false,
		region : 'south',
		title : '使用率',
		height: '50%',
		margins : '10 0 0 0',
		cls:'customize_panel_back',
		bbar: [
		        {
		            xtype: 'container',
		            layout: {
		                type: 'hbox', // 使用vbox布局来垂直排列项
		                align: 'stretch' // 使子项拉伸以填充容器
		            },
		            
		            items: [
		                {
		                    xtype: 'label',
		                    text: 'CPU使用率',
		                    margin : '0 120 80 50',
		                    style: {
		                        color: '#46545d', /* 设置文本颜色为蓝色 */
		                        fontSize: '25px' /* 设置字体大小 */
		                    },
		                    //value : cpuUseRateVal,
		                    flex: 1 // 使用flex属性来分配空间
		                },
		                {
		                    xtype: 'label',
		                    text: '内存使用率',
		                    style: {
		                        color: '#46545d', /* 设置文本颜色为蓝色 */
		                        fontSize: '25px' /* 设置字体大小 */
		                    },
		                    //value : memoryUseRateVal,
		                    //cls: 'valueTwoStyle',
		                    margin : '0 120 80 0',
		                    flex: 1
		                },
		                {
		                    xtype: 'label',
		                    text: '硬盘使用率',
		                    style: {
		                        color: '#46545d', /* 设置文本颜色为蓝色 */
		                        fontSize: '25px' /* 设置字体大小 */
		                    },
		                    //value : diskUseRateVal,
		                    //cls: 'valueTwoStyle',
		                    flex: 1
		                }
		            ]
		        }
		    ],
		layout : {
			type : 'hbox',
			align : 'stretch'
		},
		items : [cpuGraphPanel,memoryGraphPanel,diskGraphPanel ]
	});
    
	var configInfoForm = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		layout : 'anchor',
		cls:'customize_panel_back',
		bodyCls:'x-docked-noborder-top',
		dockedItems : [
			{
				xtype : 'toolbar',
				margin : '5 0 0 0',
				border : false,
				dock : 'top',
				items : ['-','-','-','-','-','-','-','-','-','-','-','-',{
			            xtype: 'textfield',
			            name: 'cpuValue',
			            cls:'noboder-style',
			            readOnly : 'true',
			            value :cpuCoreNumVal+"核" ,
			            width : '30%',
						margin : '0 110 0 0 ',
			            fieldCls: 'valueOneStyle'
		        	},{
			            xtype: 'textfield',
			            cls:'noboder-style',
			            name: 'memoryValue',
			            readOnly : 'true',
			            fieldCls: 'valueOneStyle',
						margin : '0 110 0 0 ',
			            width : '30%',
			            value : memorySizeVal+"G"
		        	},{
			            xtype: 'textfield',
			            cls:'noboder-style',
			            name: 'diskValue',
			            readOnly : 'true',
			            width : '30%',
			            fieldCls: 'valueOneStyle',
			            value : diskSizeVal+"G"
		        	}						
				]
			},{
				xtype : 'toolbar',
				margin : '5 0 0 0',
				border : false,
				dock : 'top',
				items : ['-','-','-','-','-','-','-','-','-','-','-',
			        {
			            xtype: 'textfield',
			            name: 'cpuDis',
			            cls:'noboder-style',
			            readOnly : 'true',
			            fieldCls: 'valueTwoStyle',
						margin : '0 110 0 0 ',
			            width : '28%',
			            value : 'CPU'
		        	},{
			            xtype: 'textfield',
			            cls:'noboder-style',
			            name: 'memoryDis',
			            readOnly : 'true',
			            fieldCls: 'valueTwoStyle',
						margin : '0 110 0 0 ',
			            width : '30%',
			            value : '内存'
		        	},{
			            xtype: 'textfield',
			            cls:'noboder-style',
			            name: 'diskDis',
			            readOnly : 'true',
			            fieldCls: 'valueTwoStyle',
			            width : '30%',
			            value : '硬盘'
		        	}		
				]
			}
		]
		

	});
    
	
	var configInfoPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		region : 'center',
		title:'配置信息',
		margins : '10 0 0 0',
		width:0,
		cls:'customize_panel_back',
		layout : 'fit',
		items : [configInfoForm]//pandectnewPanel2,pandectnewPanel3,pandectnewPanel4
	});
    
    var syncSystemPanel = Ext.create('Ext.panel.Panel', {
        region : 'west',
        layout : 'border',
        width:'40%',
        border : true,
        cls:' window_border panel_space_right',
        height : contentPanel.getHeight() - modelHeigth,
        split : true,
        items : [basicInfoPanel,configInfoPanel,useRatePanel  ]//,configInfoPanel ,useRatePanel
    });
    
    
    
    var cpuLineGraphPanel = Ext.create('Ext.panel.Panel', {
		region : 'north',
		layout : 'anchor',
		flex : 1,
		cls:'customize_panel_back',
		border: true,	
		html : '<div  id="cpuLineGraph" style="width: 100%;height: 100%"></div>'//1232131
		
	});
	
	var memoryLineGraphPanel = Ext.create('Ext.panel.Panel', {
		cls:'customize_panel_back',
		region : 'center',
		layout:'anchor',

		border: true,	
		flex : 1,
		html : '<div  id="memoryLineGraph" style="width: 100%;height: 100%"></div>'//5555
		
	});
	
	Ext.define ('diskSpaceModel',
			{
				extend : 'Ext.data.Model',
				fields : [
				    {
						name : 'diskPath',
						type : 'string'
				    },
					{
						name : 'mount',
						type : 'string'
					},
					{
						name : 'totalSpace',
						type : 'long'
					},
					{
						name : 'freeSpace',
						type : 'string'
					},
					{
						name : 'useRate',
						type : 'string'
					}
				]
			});
	
	var diskSpaceStore = Ext.create ('Ext.data.Store',
			{
				autoLoad : true,
				pageSize : '30',
				model : 'diskSpaceModel',
				proxy :
					{
						type : 'ajax',
						url : 'getDiskSpaceInfoList.do',
						reader :
							{
								type : 'json',
								root : 'dataList'
							}
					}
			});
	
	diskSpaceStore.on ('beforeload', function (store, options)
			{
				var dateval = dateSelectCombo.getValue();
				var startTimeVal = startTime.getRawValue();
				var endTimeVal = endTime.getRawValue();
				var new_params =
					{
						dateval : dateval,
						startTimeVal : startTimeVal,
						endTimeVal : endTimeVal,
						cpId : cpId
					};
				Ext.apply (diskSpaceStore.proxy.extraParams, new_params);
			});
	
	var diskSpaceColumns = [
	    	              		{
	    	              			text : '序号',
	    	              			width : 35,
	    	              			xtype : 'rownumberer'
	    	              		},
	    	              		{
	    	              			text : '路径',
	    	              			dataIndex : 'diskPath',
	    	              			width : 220,
	    	              			renderer : function(value, metaData, record, rowIndex, colIndex) {
	    	              				metaData.tdAttr = 'qclass="x-tip" data-qtitle="路径：" data-qwidth="200" data-qtip="'
	    	              					+ value + '"';
	    	              				return value;
	    	              			}
	    	              		},
	    	              		{
	    	              			text : '挂载',
	    	              			dataIndex : 'mount',
	    	              			width : 220,
	    	              			renderer : function(value, metaData, record, rowIndex, colIndex) {
	    	              				metaData.tdAttr = 'qclass="x-tip" data-qtitle="挂载：" data-qwidth="200" data-qtip="'
	    	              					+ value + '"';
	    	              				return value;
	    	              			}
	    	              		},{
	    	              			text : '总空间（G）',
	    	              			dataIndex : 'totalSpace',
	    	              			width : 220,
	    	              			renderer : function(value, metaData, record, rowIndex, colIndex) {
	    	              				metaData.tdAttr = 'qclass="x-tip" data-qtitle="总空间（G）：" data-qwidth="200" data-qtip="'
	    	              					+ value + '"';
	    	              				return value;
	    	              			}
	    	              		},
	    	              		{
	    	              			text : '可用空间（G）',
	    	              			dataIndex : 'freeSpace',
	    	              			width : 150,
	    	              			renderer : function(value, metaData, record, rowIndex, colIndex) {
	    	              				metaData.tdAttr = 'qclass="x-tip" data-qtitle="可用空间（G）：" data-qwidth="200" data-qtip="'
	    	              					+ value + '"';
	    	              				return value;
	    	              			}
	    	              		},
	    	              		{
	    	              			text : '使用率（%）',
	    	              			dataIndex : 'useRate',
	    	              			//width : 150,
	    	              			flex : 1,
	    	              			renderer : function(value, metaData, record, rowIndex, colIndex) {

	    	              				if( value==null ||value=='' || value=='null'){
											value = '0';
										}
										metaData.tdAttr = 'qclass="x-tip" data-qtitle="使用率：" data-qwidth="200" data-qtip="'
											+ value + '"';
	    	              				return value;
	    	              			}
	    	              		}
	    	              		
	    	              	];
	
	var diskSpace_grid_panel = Ext.create ('Ext.grid.Panel',
			{
				store : diskSpaceStore,
				region : 'south',
				flex : 1,
				//ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				cls:'customize_panel_back',
				border : true,
				title : '磁盘空间占用',
				columnLines : true,
				columns : diskSpaceColumns,
				viewConfig:{
					enableTextSelection:true
				},
				
				
			});
    
    var panela = Ext.create('Ext.panel.Panel', {
		border : false,
		region: 'center',
		title : '',
		layout:'border',
		items : [ cpuLineGraphPanel,memoryLineGraphPanel,diskSpace_grid_panel]
	});
    
    var dateSelectStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data : [ [1, '过去1天'], [2, '过去3天'],[3, '过去7天']]
    });
	
	var dateSelectCombo = Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'dateSelectCombo',
		queryMode : 'local',
		fieldLabel : '日期区间',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		width : '25%',
		labelWidth : 65,
		emptyText : '请选择...',
		value: 2,
		forceSelection : true, 
		store : dateSelectStore,
		renderer :function(value,metadata,record){
			var index = dateSelectStore.find('name',value);
			if(index!=-1){
				return dateSelectStore.getAt(index).data.name;
			}
		},
		listeners: {
			select: function(combo, record, index) {
				startTime.setValue('');
				endTime.setValue('');
			}
		}
	});
	
	var startTime = Ext.create('Go.form.field.DateTime', {
		fieldLabel : '开始时间:',
		labelAlign : 'left',
		labelWidth : 80,
		margin : '5',
		emptyText : '',
		name : 'startTime',
		format : 'Y-m-d H:i:s',
		width : 260,
		listeners: {
			select: function(field, value, eOpts) {
				dateSelectCombo.setValue(null);
			}
		}
	});
	
	var endTime = Ext.create('Go.form.field.DateTime', {
		fieldLabel : '结束时间:',
		labelAlign : 'right',
		labelWidth : 80,
		margin : '5',
		emptyText : '',
		//id : 'endTime',
		name : 'endTime',
		format : 'Y-m-d H:i:s',
		width : 260,
		listeners: {
			select: function(field, value, eOpts) {
				dateSelectCombo.setValue(null);
			}
		}
		
	});
    
    var queryConditionPanel = Ext.create('Ext.ux.ideal.form.Panel', {
		region : 'north',
		layout : 'anchor',
		buttonAlign : 'center',
		bodyCls:'x-docked-noborder-top',
		border: false,
		dockedItems : [
			{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
				items : [
					'->',dateSelectCombo,startTime,endTime,{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '查询',
						handler : queryLineData
					},{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '返回',
						handler : backFirstPage
					}
				]
			}
		]

	});
    
    var syncAgentForSystemPanel = Ext.create('Ext.panel.Panel', {
        region : 'center',
        layout : 'border',
        border : true,
        width:'59%',
        cls:'customize_panel_back window_border',
        height : contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_space,
        items : [queryConditionPanel, panela ]
    });
    
    
    
    var mainPanel = Ext.create('Ext.panel.Panel', {
        height : contentPanel.getHeight() - modelHeigth,
        width : contentPanel.getWidth(),
        layout : 'border',
        renderTo : 'hcEnvironInfoMonitor_area',
        border : true,
        split : true,
        items : [syncSystemPanel,syncAgentForSystemPanel]//syncSystemPanel ,
    });
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(contentPanel.getWidth());
    });
    
    function queryData(){
		
		    	   
    	/*var option = {
    			  tooltip: {
    				  trigger: 'none'
    			  },
    			  legend: {
    			    top: '5%',
    			    left: 'center'
    			  },
    			  series: [
    			    {
    			      name: 'Access From',
    			      type: 'pie',
    			      radius: ['40%', '70%'],
    			      avoidLabelOverlap: false,
    			      label: {
    			        show: false,
    			        position: 'center'
    			      },
    			      emphasis: {
    			        label: {
    			          show: false,
    			          fontSize: 40,
    			          fontWeight: 'bold',
    			          formatter:  function(params) {
    			              return '11' ;
    			          } //cpuFrequency + '<br/>' + cpuUsageRadio + '%' 
    			        }
    			      },
    			      labelLine: {
    			        show: false
    			      },
    			      data: [
    			        { value: cpuFreeVal, name: 'Cpu Free' },
    			        { value: cpuUseVal, name: 'Cpu Use' }
    			      ]
    			    }
    			  ]
    			};*/
    	if(cpuFreeVal != '' && cpuUseVal != '' && cpuFrequency != '' && cpuUsageRadio != ''){
	    	var option = {
					 tooltip : {
						trigger: 'item',
						 confine: true,
						formatter: "{b} : {c} ({d}%)"
					 },
	    			  series: [
	    			    {
	    			      name: 'Access From',
	    			      type: 'pie',
	    			      radius: ['50%', '70%'],
	    			      avoidLabelOverlap: false,
	    			      label: {
	    			        show: false,
	    			        position: 'center'
	    			      },
	    			      /*emphasis: {
	    			        label: {
	    			          show: true,
	    			          fontSize: 20,
	    			          fontWeight: 'bold',
	    			          formatter:  function(params) {
	    			    			              return cpuFrequency + '\n' + cpuUsageRadio + '%'  ;
	    			    			          }
	    			        }
	    			      },*/
	    			      labelLine: {
	    			        show: false
	    			      },
	    			      data: [
	    			        { value: cpuFreeVal, name: 'Cpu空闲' , itemStyle: { color: 'green' }},
	    			        { value: cpuUseVal, name: 'Cpu已使用' , itemStyle: { color: 'red' }}
	    			      ]
	    			    }
	    			  ],
	    			  graphic: [{
	    			        type: 'text',
	    			        left: 'center',
	    			        top: 'center',
	    			        style: {
	    			            text: '   '+cpuFrequency + '\n 占比' + cpuUsageRadio + '%', // 这里是你想要显示的固定值
	    			            fontSize: 15,
	    			            fontWeight: 'bold',
	    			            fill: '#000'
	    			        }
	    			    }]
	    			};
			var domone = document.getElementById("cpugraph");
			var cpuChart = echarts.init(domone);
			cpuChart.setOption(option, true); 
    	}
		if(memoryFreeVal != '' && memoryUsedVal != ''&& memoryUseRateVal != ''){
		var optiontwo = {
			tooltip : {
				trigger: 'item',
				confine: true,
				formatter: "{b} : {c} ({d}%)"
			},
  			  /*legend: {
  			    top: '5%',
  			    left: 'center'
  			  },*/
  			  series: [
  			    {
  			      name: 'Access From',
  			      type: 'pie',
  			      radius: ['50%', '70%'],
  			      avoidLabelOverlap: false,
  			      label: {
  			        show: false,
  			        position: 'center'
  			      },
  			      
  			      labelLine: {
  			        show: false
  			      },
  			      data: [
  			        { value: memoryFreeVal, name: '内存空闲', itemStyle: { color: 'green' } },
  			        { value: memoryUsedVal, name: '内存已使用', itemStyle: { color: 'red' } }
  			      ]
  			    }
  			  ],
			  graphic: [{
			        type: 'text',
			        left: 'center',
			        top: 'center',
			        style: {
			            text: '   '+memoryUsedVal + 'GB\n 占比' + memoryUseRateVal + '%', // 这里是你想要显示的固定值
			            fontSize: 15,
			            fontWeight: 'bold',
			            fill: '#000'
			        }
			    }]
  			};
		var domtwo = document.getElementById("memorygraph");
		var memoryChart = echarts.init(domtwo);
		memoryChart.setOption(optiontwo, true);
		}
		
		if(diskFreeVal != '0' && diskUsedVal != '0' &&  diskUseRateVal != '0'){
		var optionthree = {
				tooltip : {
					trigger: 'item',
					confine: true,
					formatter: "{b} : {c} ({d}%)"
				},
	  			  /*legend: {
	  			    top: '5%',
	  			    left: 'center'
	  			  },*/
	  			  series: [
	  			    {
	  			      name: 'Access From',
	  			      type: 'pie',
	  			      radius: ['50%', '70%'],
	  			      avoidLabelOverlap: false,
	  			      label: {
	  			        show: false,
	  			        position: 'center'
	  			      },
	  			      labelLine: {
	  			        show: false
	  			      },
	  			      data: [
	  			        { value: diskFreeVal, name: '硬盘空闲', itemStyle: { color: 'green' } },
	  			        { value: diskUsedVal, name: '硬盘已使用', itemStyle: { color: 'red' } }
	  			      ]
	  			    }
	  			  ],
				  graphic: [{
				        type: 'text',
				        left: 'center',
				        top: 'center',
				        style: {
				            text: '   '+diskUsedVal + 'GB\n 占比' + diskUseRateVal + '%', // 这里是你想要显示的固定值
				            fontSize: 15,
				            fontWeight: 'bold',
				            fill: '#000'
				        }
				    }]
	  			};
			var domthree = document.getElementById("diskgraph");
			var diskChart = echarts.init(domthree);
			diskChart.setOption(optionthree, true);
		}
		
		
	}
	
    function backFirstPage(){
    	contentPanel.getLoader().load({
			url : 'hcEnvironInfo.do',
			scripts : true
		}); 
    }
    
    function queryLineData(){
    	var dateval = dateSelectCombo.getValue();
    	var st = startTime.getValue();
        var et = endTime.getValue();
        if(st!=''&&et!='') {
            if(st>et) {
              Ext.Msg.alert('消息提示', '开始时间不得大于结束时间!');
              return false;
            }
          }
		var startTimeVal = startTime.getRawValue();
		var endTimeVal = endTime.getRawValue();
		Ext.Ajax.request({
			 url: 'cpulinevalue.do',
		       method: 'POST',
		       params: {
		    	   dateval:dateval,
		    	   startTimeVal : startTimeVal,
		    	   endTimeVal : endTimeVal,
		    	   cpId : cpId
		       },
		       success: function(data) {
		    	   	var jsonStr1 = new Array();
		    	   	jsonStr1 = JSON.parse(data.responseText);
		    	   	var cpuLineData = jsonStr1[1];
		    	   	var legendData = jsonStr1[0];
			    	var cpuLineoption = {
			    			  title: {
			    				    text: 'CPU使用情况'
			    				  },
			    				  tooltip: {
			    				    trigger: 'axis'
			    				  },
			    				  grid: {
			    				    left: '4%',
			    				    right: '4%',
			    				    bottom: '3%',
			    				    containLabel: true
			    				  },
			    				  xAxis: {
			    				    type: 'category',
			    				    boundaryGap: false,
			    				    data: legendData
			    				    /*axisLabel: {
			    				        interval: 0, // 确保所有标签都显示
			    				        rotate: 30, // 标签旋转45度
			    				        textStyle: {
			    				          fontSize: 12 // 调整字体大小以适应旋转
			    				        }
			    				      }*/
			    				  },
			    				  yAxis: {
			    				    type: 'value',
			    				    axisLabel: {
			    				        formatter: '{value} %'
			    				    }
			    				  },
			    				  series: [
			    				    {
			    				      name: 'CPU占用',
			    				      type: 'line',
			    				      stack: 'Total',
			    				      data: cpuLineData,
			    				      markPoint: {
			    				    	    data: [
			    				    	        {type: 'max', name: '最大值'},
			    				    	        {type: 'min', name: '最小值'}
			    				    	    ]
			    				    	},
			    				    	markLine: {
			    				    	    data: [
			    				    	        {type: 'average', name: '平均值'}
			    				    	    ]
			    				    	}
			    				    }
			    				  ]
			    				};
					var dom5 = document.getElementById("cpuLineGraph");
					var myChart5 = echarts.init(dom5);
					myChart5.setOption(cpuLineoption, true);
		       }
		});
		Ext.Ajax.request({
			 url: 'memorylinevalue.do',
		       method: 'POST',
		       params: {
		    	   dateval:dateval,
		    	   startTimeVal : startTimeVal,
		    	   endTimeVal : endTimeVal,
		    	   cpId : cpId
		       },
		       success: function(data) {
		    	   	var jsonStr1 = new Array();
		    	   	jsonStr1 = JSON.parse(data.responseText);
		    	   	var memoryTotalLineData = jsonStr1[1];
		    		var memoryUsedLineData = jsonStr1[2];
		    	   	var legendMemoryData = jsonStr1[0];
			var memoryLineoption = {
	    			  title: {
	    				    text: '内存使用情况'
	    				  },
	    				  tooltip: {
	    				    trigger: 'axis'
	    				  },
	    				  grid: {
	    				    left: '3%',
	    				    right: '4%',
	    				    bottom: '3%',
	    				    containLabel: true
	    				  },
	    				  xAxis: {
	    				    type: 'category',
	    				    boundaryGap: false,
	    				    data: legendMemoryData
	    				  },
	    				  yAxis: {
	    				    type: 'value',
	    				    axisLabel: {
	    				        formatter: '{value} GiB'
	    				      }
	    				  },
	    				  series: [
	    				    {
	    				      name: '内存总量',
	    				      type: 'line',
	    				      stack: 'Total',
	    				      data: memoryTotalLineData,
	    				      itemStyle:{
				                  normal:{color:'#007af5'}
				              },
		    				    markPoint: {
	    				    	    data: [
	    				    	        {type: 'max', name: '最大值'},
	    				    	        {type: 'min', name: '最小值'}
	    				    	    ]
	    				    	},
	    				    	markLine: {
	    				    	    data: [
	    				    	        {type: 'average', name: '平均值'}
	    				    	    ]
	    				    	}
	    				    },
	    				    {
	    				      name: '内存占用',
	    				      type: 'line',
	    				      stack: 'Total',
	    				      data: memoryUsedLineData,
	  			              itemStyle:{
				                  normal:{color:'#fc6f5e'}
				              },
				              markPoint: {
	    				    	    data: [
	    				    	        {type: 'max', name: '最大值'},
	    				    	        {type: 'min', name: '最小值'}
	    				    	    ]
	    				    	},
	    				    	markLine: {
	    				    	    data: [
	    				    	        {type: 'average', name: '平均值'}
	    				    	    ]
	    				    	}
	    				    }
	    				  ]
	    				};
				var dom6 = document.getElementById("memoryLineGraph");
				var myChart6 = echarts.init(dom6);
				myChart6.setOption(memoryLineoption, true);
    }
});
		diskSpaceStore.load();
    }
    
	queryData();
    queryLineData();

});

