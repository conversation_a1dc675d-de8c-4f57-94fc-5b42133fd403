/**
 * All rights Reserved, Designed By www.idealinfo.com
 * 
 * @Title: WorlflowStartAvgManager.java
 * @Package com.ideal.ieai.server.repository.hd.flowavg
 * @Description: 为首页看板24小时报表提供数据
 * @author: 理想科技
 * @date: 2017年8月3日 上午9:37:50
 * @version V1.0
 * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved.
 * 
 */
package com.ideal.ieai.server.jobscheduling.repository.sysalarmlevel;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.jobscheduling.repository.home.StringUtils;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public class SysalarmLevelManager
{
    private static final Logger                  _log      = Logger.getLogger(SysalarmLevelManager.class);
    private static final SysalarmLevelManager _instance = new SysalarmLevelManager();

    public static final SysalarmLevelManager getInstance ()
    {
        return _instance;
    }

 

    /**
     * 
     * @Title: isExist   
     * @Description:  (判断是否存在数据)   
     * @param projectName
     * @param flowName
     * @param actName
     * @return
     * @throws ServerException
     * @throws RepositoryException      
     * @author: yuxh 20180427
     */
    public boolean isExist ( String projectName, String flowName, String actName ) throws RepositoryException
    {
        int index = 0;
        boolean isExist = false;
        boolean isYcLevelSwitch = Environment.getInstance().isYcAlarmSendWarnLevelSwitch();
        String sql = " select iid from IEAI_SYSALARM_LEVEL where IPRJNAME= ? and iflowname=? and iactname = ? ";
        if ("".equals(actName))
        {
            sql = " select iid from IEAI_SYSALARM_LEVEL where IPRJNAME= ? and iflowname=? and (iactname is null or  iactname = '' ) ";
        }
        if(isYcLevelSwitch){
            if ("".equals(flowName))
            {
                sql = " select iid from IEAI_SYSALARM_LEVEL where IPRJNAME= ? and (iflowname is null or iflowname = '' ) and (iactname is null or  iactname = '' ) ";
            }
        }
        
        if(SystemConfig.isWebserviceWarnSwitchNMG()){
            if ("".equals(flowName))
            {
                sql = " select iid from IEAI_SYSALARM_LEVEL where IPRJNAME= ? and (iflowname is null or iflowname = '' ) and (iactname is null or  iactname = '' ) ";
            }
        }
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("isExist", _log, Constants.IEAI_IEAI);
                     
                    ps = conn.prepareStatement(sql);
                    ps.setString(++index, projectName);
                    if (isYcLevelSwitch) {
                        if (!"".equals(flowName)) {
                            ps.setString(++index, flowName);
                        }
                    } else if(SystemConfig.isWebserviceWarnSwitchNMG())
                    {
                        if (!"".equals(flowName)) {
                            ps.setString(++index, flowName);
                        }
                    }else {
                        ps.setString(++index, flowName);
                    }
                    if (!"".equals(actName))
                    {
                        ps.setString(++index, actName);
                    }
                     
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        isExist = true;
                    }
                } catch (SQLException e)
                {
                    _log.error("getActiveAlarmLevel() is error2" , e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "isExist", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return isExist;
    }

    
     
    
    //查询
    public Map<String,Object> listActivityByFroNameFlowName ( long prjname, String flowname, String actname,
            String page, String limit ) throws ServerException, RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String,Object> mapBack = new HashMap<String,Object>();
        String projectName = "";
        int count = 0;
        boolean isYcLevelSwitch = Environment.getInstance().isYcAlarmSendWarnLevelSwitch();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                PreparedStatement ps1 = null;
                PreparedStatement ps2 = null;
                PreparedStatement totalPs = null;
                ResultSet rset1 = null;
                ResultSet rset2 = null;
                ResultSet rset = null;
                ResultSet totalRset = null;
                List resultList = new ArrayList();
                try
                {
                    String totalSql = "";
                    int total = 0;
                    conn = DBResource.getConnection(method, _log,Constants.IEAI_IEAI);
                    long fromNum = 0;
                    long toNum = 0;
                    fromNum = ((Integer.parseInt(page) - 1) * Integer.parseInt(limit)) + 1L;
                    toNum = Long.parseLong(page) * Long.parseLong(limit);
                    StringBuilder sql = new StringBuilder(getSql( prjname,  flowname,  actname));

                    String proSql = "SELECT IPROJECTNAME FROM IEAI_ALARM_ACTIVITY WHERE IPRJID=" +prjname;
                    String projectSql = "SELECT A.IPROJECTNAME,B.IID, B.IPRJNAME,B.IFLOWNAME,B.IACTNAME,B.ILEVEL FROM IEAI_ALARM_ACTIVITY A LEFT JOIN IEAI_SYSALARM_LEVEL B ON A.IPROJECTNAME = B.IPRJNAME WHERE A.IPRJID = "
                            +prjname +" AND (B.IFLOWNAME = '' or B.IFLOWNAME is Null) ";
                    String projectSql1 = "SELECT A.IPROJECTNAME,B.IID, B.IPRJNAME,B.IFLOWNAME,B.IACTNAME,B.ILEVEL,B.ISTARTALARM,B.IENDALARM FROM IEAI_ALARM_ACTIVITY A LEFT JOIN IEAI_SYSALARM_LEVEL B ON A.IPROJECTNAME = B.IPRJNAME WHERE A.IPRJID = "
                            +prjname +" AND (B.IFLOWNAME = '' or B.IFLOWNAME is Null) ";

                    totalSql = "SELECT COUNT(1) FROM (" + sql +") DAC" ;

                    if (DBManager.Orcl_Faimily())
                    {
                        sql = new StringBuilder("SELECT IPROJECTNAME, IFLOWNAME, IACTNAME, IALARMLEVEL, IPRJID FROM"
                                + "  ( SELECT  ROWNUM AS RN ,  A.* FROM ( " + sql.toString() + " )  A "
                                + " ) WHERE RN BETWEEN  " + fromNum + "  AND  " + toNum)   ;
                    } else if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        sql = new StringBuilder("SELECT IPROJECTNAME, IFLOWNAME, IACTNAME, IALARMLEVEL, IPRJID FROM"
                                + "  ( SELECT  ROW_NUMBER() OVER() AS RN ,  A.* FROM ( " + sql.toString()
                                + ")  A ) WHERE RN BETWEEN  " + fromNum + "  AND  " + toNum);
                    } else if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        // 计算 mysql 分页最后一页显示条数不足时，取总数的余数为最后一页数量
                        long start = Long.parseLong(page);
                        long tolimit = Long.parseLong(limit);
                        long curpage = ((start - 1) * tolimit);
                        sql.append( " limit " + curpage + "," + tolimit) ;
                    }

                    totalPs = conn.prepareStatement(totalSql);
                    totalRset = totalPs.executeQuery();
                    while (totalRset.next())
                    {
                        total = totalRset.getInt(1);
                        mapBack.put("total", total);
                    }
                    if (prjname != 0 && isYcLevelSwitch) {
                        ps1 = conn.prepareStatement(proSql);
                        rset1 = ps1.executeQuery();
                        while (rset1.next()) {
                            projectName = rset1.getString("IPROJECTNAME");
                        }
                        if (!StringUtils.isEmpty(projectName)) {
                            ps2 = conn.prepareStatement(projectSql);
                            rset2 = ps2.executeQuery();

                            while (rset2.next()) {
                                Map map = new HashMap();
                                long iid = rset2.getLong("IID");
                                if (iid != 0) {
                                    count= 1;
                                    map.put("projectName", rset2.getString("IPRJNAME"));
                                    map.put("flowName", rset2.getString("IFLOWNAME"));
                                    map.put("actName", rset2.getString("IACTNAME"));
                                    map.put("iLevel", rset2.getString("ILEVEL"));
                                    resultList.add(map);
                                    mapBack.put("total", total + 1);
                                    break;
                                }
                            }
                            if (count ==0){
                                Map map = new HashMap();
                                map.put("projectName", projectName);
                                map.put("flowName", "");
                                map.put("actName", "");
                                map.put("iLevel", "3");
                                resultList.add(map);
                                mapBack.put("total", total + 1);
                            }
                        }
                    }
                    if (prjname != 0 && SystemConfig.isWebserviceWarnSwitchNMG()) {
                        ps1 = conn.prepareStatement(proSql);
                        rset1 = ps1.executeQuery();
                        while (rset1.next()) {
                            projectName = rset1.getString("IPROJECTNAME");
                        }
                        if (!StringUtils.isEmpty(projectName)) {
                            ps2 = conn.prepareStatement(projectSql1);
                            rset2 = ps2.executeQuery();

                            while (rset2.next()) {
                                Map map = new HashMap();
                                long iid = rset2.getLong("IID");
                                if (iid != 0) {
                                    map.put("projectName", rset2.getString("IPRJNAME"));
                                    map.put("flowName", rset2.getString("IFLOWNAME"));
                                    map.put("actName", rset2.getString("IACTNAME"));
                                    map.put("iLevel", rset2.getString("ILEVEL"));
                                    
                                    if("1".equals(rset2.getString("ISTARTALARM")))
                                    {
                                        map.put("istartAlarm", "是");
                                    }else
                                    {
                                        map.put("istartAlarm", "否");
                                    }
                                    
                                    if("1".equals(rset2.getString("IENDALARM")))
                                    {
                                        map.put("iendAlarm", "是");
                                    }else
                                    {
                                        map.put("iendAlarm", "否");
                                    }
                                    
                                    resultList.add(map);
                                    mapBack.put("total", total + 1);
                                    break;
                                } else {
                                    map.put("projectName", projectName);
                                    map.put("flowName", "");
                                    map.put("actName", "");
                                    map.put("iLevel", "3");
                                    map.put("istartAlarm", "否");
                                    map.put("iendAlarm", "否");
                                    resultList.add(map);
                                    mapBack.put("total", total + 1);
                                    break;
                                }
                            }
                        }
                    }
                    ps = conn.prepareStatement(sql.toString());
                    rset = ps.executeQuery();

                    while (rset.next())
                    {
                        Map map = new HashMap();
                        map.put("projectName", rset.getString("IPROJECTNAME"));
                        map.put("flowName", rset.getString("IFLOWNAME"));
                        map.put("actName", rset.getString("IACTNAME"));
                        map.put("iLevel", rset.getString("IALARMLEVEL"));
                        
                        if(SystemConfig.isWebserviceWarnSwitchNMG())
                        {
                            if(StringUtils.isBlank(rset.getString("IACTNAME")))
                            {
                                if("1".equals(rset2.getString("ISTARTALARM")))
                                {
                                    map.put("istartAlarm", "是");
                                }else
                                {
                                    map.put("istartAlarm", "否");
                                }
                                
                                if("1".equals(rset2.getString("IENDALARM")))
                                {
                                    map.put("iendAlarm", "是");
                                }else
                                {
                                    map.put("iendAlarm", "否");
                                }
                            }else
                            {
                                map.put("istartAlarm", "否");
                                map.put("iendAlarm", "否");
                            }
                            
                        }
                        resultList.add(map);
                    }
                    mapBack.put("dataList", resultList);
                } catch (SQLException e)
                {
                    _log.error("listActivityByFroNameFlowName() is error2" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rset, ps, method, _log);
                    DBResource.closeConn(conn, totalRset, totalPs, method, _log);
                    DBResource.closeConn(conn, rset1, ps1, method, _log);
                    DBResource.closeConn(conn, rset2, ps2, method, _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return mapBack;
    }
    
    private String getSql(long prjname, String flowname, String actname){
        String sql = "";
        String orderby = " ORDER BY IPROJECTNAME , IFLOWNAME, IACTNAME ";
        if(DBManager.Orcl_Faimily() || JudgeDB.IEAI_DB_TYPE == 2){
            sql = " SELECT IPROJECTNAME, IFLOWNAME, IACTNAME, IALARMLEVEL, IPRJID  FROM "
                    + "(SELECT T1.IPROJECTNAME, T1.IFLOWNAME, T1.IACTNAME, NVL(T2.ILEVEL,"
                    + SystemConfig.getAlarmLevel() + " ) IALARMLEVEL,T1.IPRJID,T2.ISTARTALARM,T2.IENDALARM "
                    + "   FROM (SELECT DISTINCT T.IPROJECTNAME, T.IFLOWNAME, '' IACTNAME, T.IPRJID  FROM IEAI_ALARM_ACTIVITY T) T1 "
                    + "  LEFT JOIN (SELECT * FROM   IEAI_SYSALARM_LEVEL WHERE ITYPE=1) T2 ON T1.IPROJECTNAME = T2.IPRJNAME AND T1.IFLOWNAME = T2.IFLOWNAME "
                    + "  UNION ALL      "
                    + "SELECT T1.IPROJECTNAME, T1.IFLOWNAME, T1.IACTNAME, NVL(T2.ILEVEL, "
                    + SystemConfig.getAlarmLevel() + ")  IALARMLEVEL,T1.IPRJID,T2.ISTARTALARM,T2.IENDALARM "
                    + "  FROM (SELECT DISTINCT T.IPROJECTNAME, T.IFLOWNAME, T.IACTNAME IACTNAME, T.IPRJID  FROM IEAI_ALARM_ACTIVITY T) T1  "
                    + "  LEFT JOIN (SELECT * FROM   IEAI_SYSALARM_LEVEL WHERE ITYPE=0) T2 ON T1.IPROJECTNAME = T2.IPRJNAME AND T1.IFLOWNAME = T2.IFLOWNAME "
                    + "  AND T1.IACTNAME = T2.IACTNAME ) DA  ";
                sql = sql + " WHERE IPRJID = " + prjname ;
            if (!"".equals(flowname))
            {
                sql = sql +  " AND IFLOWNAME='" + flowname + "'"  ;
            }  
            if (!"".equals(actname) )
            {
                sql =  sql + " AND IACTNAME='" + actname + "'" ;
            }
        }
         
        if(JudgeDB.IEAI_DB_TYPE == 3){
            sql="SELECT IPROJECTNAME, IFLOWNAME, IACTNAME, IALARMLEVEL, IPRJID FROM  "
                    + "(SELECT T1.IPROJECTNAME, T1.IFLOWNAME, T1.IACTNAME, "
                    + "IFNULL(T2.ILEVEL,"+ SystemConfig.getAlarmLevel() +") IALARMLEVEL,T1.IPRJID "
                    + "FROM (SELECT DISTINCT T.IPROJECTNAME, T.IFLOWNAME, '' IACTNAME, T.IPRJID  "
                    + "FROM IEAI_ALARM_ACTIVITY T) T1  LEFT JOIN "
                    + "(SELECT * FROM   IEAI_SYSALARM_LEVEL WHERE ITYPE=1) T2 "
                    + "ON T1.IPROJECTNAME = T2.IPRJNAME AND T1.IFLOWNAME = T2.IFLOWNAME "
                    +" UNION ALL  SELECT T1.IPROJECTNAME, T1.IFLOWNAME, T1.IACTNAME, "
                    + "IFNULL(T2.ILEVEL,"+ SystemConfig.getAlarmLevel() +") IALARMLEVEL,T1.IPRJID "
                    + "FROM (SELECT DISTINCT T.IPROJECTNAME, T.IFLOWNAME, T.IACTNAME IACTNAME, T.IPRJID  "
                    + "FROM IEAI_ALARM_ACTIVITY T) T1   LEFT JOIN "
                    + "(SELECT * FROM IEAI_SYSALARM_LEVEL WHERE ITYPE=0) T2 "
                    + "ON T1.IPROJECTNAME = T2.IPRJNAME AND T1.IFLOWNAME = T2.IFLOWNAME  "
                    + "AND T1.IACTNAME = T2.IACTNAME ) DA " ;
            sql = sql + " WHERE IPRJID = " + prjname ;
            if (!"".equals(flowname))
            {
                sql = sql +  " AND IFLOWNAME='" + flowname + "'"  ;
            }  
            if (!"".equals(actname) )
            {
                sql =  sql +  " AND IACTNAME='" + actname + "'" ;
            }
            
        }
        sql =sql + orderby;
        return sql;
    }
    //设置
    public boolean updSysalarmLevel ( String projectName, String flowName, String actName, long level, String istartAlarm, String iendAlarm )
            throws RepositoryException
    {
        boolean flag = false;
        int index = 0;
        boolean isYcLevelSwitch = Environment.getInstance().isYcAlarmSendWarnLevelSwitch();
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                String sql = "update IEAI_SYSALARM_LEVEL set ILEVEL = ?  where IPRJNAME= ? and iflowname=? and iactname = ?";
                try
                {
                    conn = DBResource.getConnection("updActiveAlarmLevel", _log,Constants.IEAI_IEAI);
                    boolean isExist = isExist(projectName, flowName, actName);
                    if (null == actName || "".equals(actName))
                    {
                        sql = " update IEAI_SYSALARM_LEVEL set ILEVEL = ?  where IPRJNAME= ? and iflowname=? and (iactname is null or iactname = '' ) ";
                    }
                    if (isYcLevelSwitch) {
                        if (null == flowName || "".equals(flowName)) {
                            sql = " update IEAI_SYSALARM_LEVEL set ILEVEL = ?  where IPRJNAME= ? and (iflowname is null or iflowname = '' ) and (iactname is null or iactname = '' ) ";
                        }
                    }
                    if(SystemConfig.isWebserviceWarnSwitchNMG())
                    {
                        if (null == flowName || "".equals(flowName)) {
                            sql = " update IEAI_SYSALARM_LEVEL set ILEVEL = ?,istartAlarm = ?,iendAlarm = ?  where IPRJNAME= ? and (iflowname is null or iflowname = '' ) and (iactname is null or iactname = '' ) ";
                        }else if(null == actName || "".equals(actName))
                        {
                            sql = " update IEAI_SYSALARM_LEVEL set ILEVEL = ?,istartAlarm = ?,iendAlarm = ?  where IPRJNAME= ? and iflowname = ? and (iactname is null or iactname = '' ) "; 
                        }else {
                            sql = " update IEAI_SYSALARM_LEVEL set ILEVEL = ? ,istartAlarm = ?,iendAlarm = ? where IPRJNAME= ? and iflowname=? and iactname = ?";
                        }
                    }
                    if(isExist){
                        ps = conn.prepareStatement(sql);
                        ps.setLong(++index, level);
                        if(SystemConfig.isWebserviceWarnSwitchNMG())
                        {
                            ps.setString(++index, istartAlarm);
                            ps.setString(++index, iendAlarm);
                        }
                        ps.setString(++index, projectName);
                        if (isYcLevelSwitch) {
                            if (!"".equals(flowName)) {
                                ps.setString(++index, flowName);
                            }
                        } else if(SystemConfig.isWebserviceWarnSwitchNMG())
                        {
                            if (!"".equals(flowName)) {
                                ps.setString(++index, flowName);
                            }
                        }
                        else {
                            ps.setString(++index, flowName);
                        }
                        
                        if (!"".equals(actName))
                        {
                            ps.setString(++index, actName);
                        }
                        ps.executeUpdate();
                        conn.commit();
                        flag = true;
                    }else{
                        long type = -1;
                        if(!"".equals(actName)){
                              type = 0;
                        }else{
                              type = 1;
                        }
                        if(SystemConfig.isWebserviceWarnSwitchNMG())
                        {
                            ps = conn.prepareStatement(
                                "insert into  IEAI_SYSALARM_LEVEL (iid,iprjname,iflowname,iactname,ilevel,itype,istartAlarm,iendAlarm) "
                                + " values (?,?,?,?,?,?,?,?)");
                            ps.setLong(1, IdGenerator.createId("IEAI_SYSALARM_LEVEL", conn));
                            ps.setString(2, projectName); 
                            ps.setString(3, flowName);
                            ps.setString(4, actName); 
                            ps.setLong(5, level);
                            ps.setLong(6, type); 
                            ps.setString(7, istartAlarm); 
                            ps.setString(8,iendAlarm); 
                        }else
                        {
                            ps = conn.prepareStatement(
                                "insert into  IEAI_SYSALARM_LEVEL (iid,iprjname,iflowname,iactname,ilevel,itype) "
                                + " values (?,?,?,?,?,?)");
                            ps.setLong(1, IdGenerator.createId("IEAI_SYSALARM_LEVEL", conn));
                            //ps.setLong(1, IdGenerator.createId("IEAI_SYSALARM_LEVEL", conn));
                            ps.setString(2, projectName); 
                            ps.setString(3, flowName);
                            ps.setString(4, actName); 
                            ps.setLong(5, level);
                            ps.setLong(6, type); 
                        }
                            
                            
                            ps.executeUpdate();
                            conn.commit();
                            flag = true;
                    }
                } catch (SQLException e)
                {
                    _log.error("updSysalarmLevel() is error2" , e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSConn(conn, ps, "updSysalarmLevel", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return flag;
    }
    /**
     *
     * @Title: queryLevelByprojectName
     * @Description:  (查询工程设置报警等级(邮储))
     * @param projectName
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * @author: lsd 20220531
     */
    public int queryLevelByProjectName ( String projectName ) throws RepositoryException
    {
        int level = -1;
        String sql = " SELECT ILEVEL FROM IEAI_SYSALARM_LEVEL WHERE IPRJNAME= ? and (iflowname is null or iflowname = '' ) and (iactname is null or iactname = '' )  ";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("queryLevelByProjectName", _log, Constants.IEAI_IEAI);

                    ps = conn.prepareStatement(sql);
                    ps.setString(1, projectName);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                       level= rs.getInt("ILEVEL");
                    }
                } catch (SQLException e)
                {
                    _log.error("queryLevelByProjectName() is error2" ,e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "queryLevelByProjectName", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return level;
    }
    /**
     *
     * @Title: queryLevelByprojectName
     * @Description:  (查询工程设置报警等级(邮储))
     * @param projectName
     * @param flowName
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * @author: lsd 20220531
     */
    public int queryLevelByFlowName ( String projectName,String flowName ) throws RepositoryException
    {
        int level = -1;
        String sql = " SELECT ILEVEL FROM IEAI_SYSALARM_LEVEL WHERE IPRJNAME= ? and iflowname= ? and (iactname is null or iactname = '' )  ";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("queryLevelByFlowName", _log, Constants.IEAI_IEAI);

                    ps = conn.prepareStatement(sql);
                    ps.setString(1, projectName);
                    ps.setString(2, flowName);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        level= rs.getInt("ILEVEL");
                    }
                } catch (SQLException e)
                {
                    _log.error("queryLevelByFlowName() is error2"  ,e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "queryLevelByFlowName", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return level;
    }
    /**
     *
     * @Title: queryLevelByprojectName
     * @Description:  (查询工程设置报警等级(邮储))
     * @param projectName
     * @param flowName
     * @param actName
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * @author: lsd 20220531
     */
    public int queryLevelByActName ( String projectName,String flowName,String actName ) throws RepositoryException
    {
        int level = -1;
        String sql = " SELECT ILEVEL FROM IEAI_SYSALARM_LEVEL WHERE IPRJNAME= ? and iflowname = ? and iactname = ?  ";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("queryLevelByActName", _log, Constants.IEAI_IEAI);

                    ps = conn.prepareStatement(sql);
                    ps.setString(1, projectName);
                    ps.setString(2, flowName);
                    ps.setString(3, actName);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        level= rs.getInt("ILEVEL");
                    }
                } catch (SQLException e)
                {
                    _log.error("queryLevelByActName() is error2"  ,e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "queryLevelByActName", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return level;
    }
    /**
     *
     * @Title: queryLevelByprojectName
     * @Description:  (查询工程设置报警等级(重庆))
     * @param projectName
     * @param flowName
     * @param actName
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * @author: lsd 20220531
     */
    public int queryLevel ( String projectName,String flowName,String actName ) throws RepositoryException
    {
        int level = -1;
        String sql = " SELECT ILEVEL FROM IEAI_SYSALARM_LEVEL WHERE IPRJNAME= ? and iflowname = ? and  iactname = ?  ";
        String sql1 = " SELECT ILEVEL FROM IEAI_SYSALARM_LEVEL WHERE IPRJNAME= ? and iflowname = ? ";
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    conn = DBResource.getConnection("queryLevelByProjectName", _log, Constants.IEAI_IEAI);

                    ps = conn.prepareStatement(sql);
                    ps.setString(1, projectName);
                    ps.setString(2, flowName);
                    ps.setString(3, actName);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        level= rs.getInt("ILEVEL");
                    }
                    if(-1 ==level){
                        ps = conn.prepareStatement(sql1);
                        ps.setString(1, projectName);
                        ps.setString(2, flowName);
                        rs = ps.executeQuery();
                        while (rs.next())
                        {
                            level= rs.getInt("ILEVEL");
                        }
                    }

                } catch (SQLException e)
                {
                    _log.error("getActiveAlarmLevel() is error2" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, ps, "isExist", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return level;
    }


    public Map<String, Object> listActivityForExcel(String projectName, String flowName, boolean isMain, String page, String limit) {
        Map<String, Object> resultMap = new HashMap<>();
        List<SysAlarmLevelDG> activityList = new ArrayList<>();
        String sqlWhere = "";
        String sqlWhere1 = "";
        String sql = "";
        String sql1 = "";
        if (StringUtils.isNotBlank(projectName)) {
            sqlWhere += " AND E.IMAINPRONAME = ? ";
            sqlWhere1 += " AND E.ICHILDPRONAME = ? ";
        }
        if (StringUtils.isNotBlank(flowName)) {
            sqlWhere += " AND E.IMAINLINENAME = ? ";
            sqlWhere1 += " AND E.IACTNAME = ?  ";
        }
        if (isMain) {
            sql = "SELECT DISTINCT E.IMAINPRONAME PROJECTNAME,E.IMAINLINENAME FLOWNAME,B.ILEVEL,P.ISYSTEMCODE,B.IID FROM IEAI_EXCELMODEL E LEFT JOIN IEAI_PROJECT P ON E.IMAINPRONAME = P.INAME  LEFT JOIN IEAI_SYSALARM_LEVEL_DG B ON E.IMAINPRONAME = B.IPRJNAME AND E.IMAINLINENAME = B.IFLOWNAME AND ( B.IACTNAME = '' or B.IACTNAME is Null )  WHERE P.IID = P.ILATESTID " + sqlWhere ;
        } else {
            sql = "SELECT DISTINCT E.ICHILDPRONAME PROJECTNAME,E.IACTNAME FLOWNAME,B.ILEVEL,P.ISYSTEMCODE,B.IID FROM IEAI_EXCELMODEL E LEFT JOIN IEAI_PROJECT P ON E.IMAINPRONAME = P.INAME  LEFT JOIN IEAI_SYSALARM_LEVEL_DG B ON E.ICHILDPRONAME = B.IPRJNAME AND E.IACTNAME = B.IFLOWNAME AND E.IACTNAME = B.IACTNAME WHERE P.IID = P.ILATESTID " + sqlWhere1 ;
        }
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rs = null;
        ResultSet rs1 = null;
        try {
            conn = DBResource.getConnection("listActivityByName", _log, Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setString(1, projectName);
            if (StringUtils.isNotBlank(flowName)) {
                ps.setString(2, flowName);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                SysAlarmLevelDG level = new SysAlarmLevelDG();
                level.setProjectName(rs.getString("PROJECTNAME"));
                level.setFlowName(rs.getString("FLOWNAME"));
                if (!isMain) {
                    level.setActName(rs.getString("FLOWNAME"));
                }
                level.setiLevel(rs.getString("ILEVEL"));
                level.setSystemCode(rs.getString("ISYSTEMCODE"));
                level.setIid(rs.getLong("IID"));
                activityList.add(level);
            }
            int start = (Integer.parseInt(page) - 1) * Integer.parseInt(limit);

            List<SysAlarmLevelDG> dataList = activityList.stream().skip(start).limit(Integer.parseInt(limit)).collect(Collectors.toList());
            resultMap.put("total", activityList.size());
            resultMap.put("dataList", dataList);
        } catch (Exception e) {
            _log.error("listActivityByName is error !", e);
        } finally {
            DBResource.closeConn(conn, rs, ps, "listActivityByName", _log);
        }
        return resultMap;
    }

    public boolean updateSysAlarmLevel(List<SysAlarmLevelDG> bsList) {
        boolean flag = false;
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rs = null;
        String sqlWhere = "";
        String insertSql = "INSERT INTO IEAI_SYSALARM_LEVEL_DG (IID,IPRJNAME,IFLOWNAME,IACTNAME,ILEVEL) VALUES (?,?,?,?,?)";
        String updateSql = "UPDATE IEAI_SYSALARM_LEVEL_DG SET ILEVEL =? WHERE IPRJNAME = ? AND IFLOWNAME = ? ";
        String selectSql = "SELECT COUNT(*) NUM FROM IEAI_SYSALARM_LEVEL_DG WHERE IPRJNAME = ? AND IFLOWNAME = ? ";
        try {
            conn = DBResource.getConnection("updSysAlarmLevel", _log, Constants.IEAI_IEAI);
            for (SysAlarmLevelDG bs : bsList) {
                if (StringUtils.isNotBlank(bs.getActName())) {
                    sqlWhere = " AND IACTNAME = ? ";
                }else {
                    sqlWhere = " AND (IACTNAME = '' OR IACTNAME is  NULL)  ";
                }
                ps = conn.prepareStatement(selectSql + sqlWhere);
                ps.setString(1, bs.getProjectName());
                ps.setString(2, bs.getFlowName());
                if (StringUtils.isNotBlank(bs.getActName())) {
                    ps.setString(3, bs.getActName());
                }
                rs = ps.executeQuery();
                if (rs.next()) {
                    int num = rs.getInt("NUM");
                    if (num > 0) {
                        ps1 = conn.prepareStatement(updateSql + sqlWhere);
                        ps1.setString(1, bs.getiLevel());
                        ps1.setString(2, bs.getProjectName());
                        ps1.setString(3, bs.getFlowName());
                        if (StringUtils.isNotBlank(bs.getActName())) {
                            ps1.setString(4, bs.getActName());
                        }
                        ps1.executeUpdate();
                    } else {
                        ps1 = conn.prepareStatement(insertSql);
                        long id = IdGenerator.createId("IEAI_SYSALARM_LEVEL_DG", conn);
                        ps1.setLong(1, id);
                        ps1.setString(2, bs.getProjectName());
                        ps1.setString(3, bs.getFlowName());
                        ps1.setString(4, bs.getActName());
                        ps1.setString(5, bs.getiLevel());
                        ps1.executeUpdate();
                    }
                }
            }
            conn.commit();
            flag = true;
        } catch (Exception e) {
            _log.error("updateSysAlarmLevel is error!", e);
        } finally {
            DBResource.closePreparedStatement(ps1, "updateSysAlarmLevel", _log);
            DBResource.closeConn(conn, rs, ps, "updateSysAlarmLevel", _log);
        }
        return flag;
    }



    /**
     * 查询
     * @param prjname
     * @param flowName
     * @param actName
     * @param page
     * @param limit
     * @return
     * @throws ServerException
     * @throws RepositoryException
     */
    public Map<String, Object> listActivityForStudio(long prjname, String flowName, String actName,
                                                     String page, String limit) throws ServerException, RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> mapBack = new HashMap<String, Object>();
        List<SysAlarmLevelDG> activityList = new ArrayList<>();
        String sqlWhere = "";
        String sqlWhere1 = "";

        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rset = null;
        ResultSet rs = null;
        List resultList = new ArrayList();
        try {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI);
            if (StringUtils.isNotBlank(flowName)) {
                sqlWhere += " AND T.IFLOWNAME = ? ";
                sqlWhere1 += " AND T.IFLOWNAME = ? ";
            }
            if (StringUtils.isNotBlank(actName)) {
                sqlWhere += " AND T.IACTNAME = ? ";
                sqlWhere1 += " AND T.IACTNAME = ?  ";
            }
            String projectSql = "SELECT DISTINCT T.IPROJECTNAME,T.IFLOWNAME,T.IPRJID,L.ILEVEL,P.ISYSTEMCODE,L.IID FROM IEAI_ALARM_ACTIVITY T LEFT JOIN IEAI_PROJECT P ON T.IPROJECTNAME = P.INAME AND P.IID = P.ILATESTID  LEFT JOIN IEAI_SYSALARM_LEVEL_DG L ON T.IPROJECTNAME = L.IPRJNAME AND T.IFLOWNAME = L.IFLOWNAME AND ( L.IACTNAME = '' OR L.IACTNAME IS NULL ) WHERE T.IPRJID = ? " + sqlWhere;
            String actSql = "SELECT DISTINCT T.IPROJECTNAME,T.IFLOWNAME,T.IACTNAME,T.IPRJID,L.ILEVEL,P.ISYSTEMCODE,L.IID FROM IEAI_ALARM_ACTIVITY T LEFT JOIN IEAI_PROJECT P ON T.IPROJECTNAME = P.INAME AND P.IID = P.ILATESTID LEFT JOIN IEAI_SYSALARM_LEVEL_DG L ON T.IPROJECTNAME = L.IPRJNAME AND T.IFLOWNAME = L.IFLOWNAME AND T.IACTNAME = L.IACTNAME AND L.IACTNAME != '' AND L.IACTNAME IS NOT NULL  WHERE T.IPRJID = ? " + sqlWhere1;

            ps = conn.prepareStatement(projectSql);
            ps.setLong(1, prjname);
            if (StringUtils.isNotBlank(flowName)){
                ps.setString(2, flowName);
            }
            if (StringUtils.isNotBlank(actName)){
                ps.setString(3, actName);
            }
            rset = ps.executeQuery();

            while (rset.next()) {
                SysAlarmLevelDG level = new SysAlarmLevelDG();
                level.setProjectName(rset.getString("IPROJECTNAME"));
                level.setFlowName(rset.getString("IFLOWNAME"));
                level.setiLevel(rset.getString("ILEVEL"));
                level.setSystemCode(rset.getString("ISYSTEMCODE"));
                level.setIid(rset.getLong("IID"));
                activityList.add(level);
            }
            ps1 = conn.prepareStatement(actSql);
            ps1.setLong(1, prjname);
            if (StringUtils.isNotBlank(flowName)){
                ps1.setString(2, flowName);
            }
            if (StringUtils.isNotBlank(actName)){
                ps1.setString(3, actName);
            }
            rs = ps1.executeQuery();
            while (rs.next()) {
                SysAlarmLevelDG level = new SysAlarmLevelDG();
                level.setProjectName(rs.getString("IPROJECTNAME"));
                level.setFlowName(rs.getString("IFLOWNAME"));
                level.setActName(rs.getString("IACTNAME"));
                level.setiLevel(rs.getString("ILEVEL"));
                level.setSystemCode(rs.getString("ISYSTEMCODE"));
                level.setIid(rs.getLong("IID"));
                activityList.add(level);
            }
            int start = (Integer.parseInt(page) - 1) * Integer.parseInt(limit);
            List<SysAlarmLevelDG> dataList = activityList.stream().skip(start).limit(Integer.parseInt(limit)).collect(Collectors.toList());
            mapBack.put("total", activityList.size());
            mapBack.put("dataList", activityList);
        } catch (SQLException e) {
            _log.error("listActivityByFroNameFlowName() is error2" + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closePSRS(rset, ps, method, _log);
            DBResource.closeConn(conn, rs, ps1, method, _log);
        }
        return mapBack;
    }

    /**
     * 报警等级 取消设置
     * @param deleteIds
     * @return
     */
    public Map deleteSysLevelList(String deleteIds) {
        Map map = new HashMap();
        Connection conn = null;
        PreparedStatement ps = null;
        String sql = "delete from IEAI_SYSALARM_LEVEL_DG where iid = ?";
        try {
            String[] iid = deleteIds.split(",");
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            if (iid != null && iid.length > 0) {
                for (int i = 0; i < iid.length; i++) {
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, Long.valueOf(iid[i]));
                    ps.executeUpdate();
                }
                conn.commit();
            }
            map.put("success", true);
            map.put("message", "删除成功");
        } catch (Exception e) {
            _log.error("deleteSysLevelList is error", e);
            map.put("success", false);
            map.put("message", "删除失败");
        } finally {
            DBResource.closePSConn(conn, ps, "deleteSysLevelList", _log);
        }
        return map;
    }

    /**
     * 获取20分钟内需要查询后继的工程
     * @param start
     * @param endTime
     * @return
     */
    public List<WarnModelDG> getWarningInfoList(long start, long endTime) {
        List<WarnModelDG> warnList = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "SELECT DISTINCT ID,PRJNAME,FLOWNAME,ACTNAME,TYPE,WARNTIME,WARNDES,IFLOWID,AGENTIP,IOPERATIONID,ISYSTEMCODE FROM (\n" +
//                " SELECT N.ID,N.PRJNAME,N.FLOWNAME,N.ACTNAME,N.TYPE,N.WARNTIME,N.WARNDES,N.IFLOWID,N.AGENTIP FROM  IEAI_NODE_WARNING N INNER JOIN IEAI_EXCELMODEL M ON N.PRJNAME = M.IMAINPRONAME AND N.FLOWNAME = M.IMAINLINENAME  WHERE N.WARNTIME > ? AND N.WARNTIME < ? \n" +
//                " UNION \n" +
                " SELECT N.ID,N.PRJNAME,N.FLOWNAME,N.ACTNAME,N.TYPE,N.WARNTIME,N.WARNDES,N.IFLOWID,N.AGENTIP,M.IOPERATIONID,P.ISYSTEMCODE FROM  IEAI_NODE_WARNING N INNER JOIN IEAI_EXCELMODEL M ON N.PRJNAME = M.ICHILDPRONAME AND N.FLOWNAME = M.IACTNAME  LEFT JOIN IEAI_PROJECT P ON N.PRJNAME = P.INAME AND P.IID = P.ILATESTID WHERE N.WARNTIME >= ? AND N.WARNTIME <= ? \n" +
                " UNION \n" +
//                " SELECT N.ID,N.PRJNAME,N.FLOWNAME,N.ACTNAME,N.TYPE,N.WARNTIME,N.WARNDES,N.IFLOWID,'' AGENTIP FROM  IEAI_NODE_WARNING_HISTORY N INNER JOIN IEAI_EXCELMODEL M ON N.PRJNAME = M.IMAINPRONAME AND N.FLOWNAME = M.IMAINLINENAME  WHERE N.WARNTIME > ? AND N.WARNTIME < ? \n" +
//                " UNION \n" +
                " SELECT N.ID,N.PRJNAME,N.FLOWNAME,N.ACTNAME,N.TYPE,N.WARNTIME,N.WARNDES,N.IFLOWID,'' AGENTIP,M.IOPERATIONID,P.ISYSTEMCODE FROM  IEAI_NODE_WARNING_HISTORY N INNER JOIN IEAI_EXCELMODEL M ON N.PRJNAME = M.ICHILDPRONAME AND N.FLOWNAME = M.IACTNAME LEFT JOIN IEAI_PROJECT P ON N.PRJNAME = P.INAME AND P.IID = P.ILATESTID WHERE N.WARNTIME >= ? AND N.WARNTIME <= ? \n" +
                ") NW ";

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, start);
            ps.setLong(2, endTime);
            ps.setLong(3, start);
            ps.setLong(4, endTime);
            rs = ps.executeQuery();
            while (rs.next()) {
                WarnModelDG warnModel = new WarnModelDG();
                warnModel.setId(rs.getLong("ID"));
                warnModel.setPrjName(rs.getString("PRJNAME"));
                warnModel.setFlowName(rs.getString("FLOWNAME"));
                warnModel.setActName(rs.getString("ACTNAME"));
                warnModel.setType(rs.getInt("TYPE"));
                warnModel.setWarnTime(rs.getLong("WARNTIME"));
                warnModel.setWarnDes(rs.getString("WARNDES"));
                warnModel.setFlowId(rs.getLong("IFLOWID"));
                warnModel.setAgentIp(rs.getString("AGENTIP"));
                warnModel.setOperationId(rs.getLong("IOPERATIONID"));
                warnModel.setSystemCode(rs.getString("ISYSTEMCODE"));
                if (StringUtils.isNotBlank(warnModel.getSystemCode())) {
                    warnList.add(warnModel);
                }
            }
        } catch (Exception e) {
            _log.error("getWarningInfoList is error", e);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getWarningInfoList is error", _log);
        }

        return warnList;
    }

    public List<WarnModelDG> getWarningInfoOrg(List<WarnModelDG> warningInfoList) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "SELECT A.ICHILDPROJECTNAME,A.ISUCCACTNAME,D.ILEVEL FROM ieai_actsucc a LEFT JOIN IEAI_SYSALARM_LEVEL_DG D ON A.ICHILDPROJECTNAME = D.IPRJNAME AND A.ISUCCACTNAME = D.IFLOWNAME AND A.ISUCCACTNAME = D.IACTNAME  WHERE A.IOPERATIONID =? ";

        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            for (WarnModelDG warnModel : warningInfoList) {
                /** 告警等级 是2  下游作业*/
                List<String> successList = new ArrayList<String>();
                /** 告警等级 是3  下游作业*/
                List<String> succeedList = new ArrayList<String>();
                ps = conn.prepareStatement(sql);
                ps.setLong(1, warnModel.getOperationId());
                rs = ps.executeQuery();
                while (rs.next()) {
                    /** 系统异常 告警等级默认是2 */
                    if (warnModel.getType() == 5) {
                        String successName = rs.getString("ISUCCACTNAME");
                        successList.add(successName);
                    } else {
                        String level = rs.getString("ILEVEL");
                        String sucName = rs.getString("ISUCCACTNAME");
                        if ("3".equals(level)) {
                            succeedList.add(sucName);
                        } else if ("2".equals(level)) {
                            successList.add(sucName);
                        }
                    }
                }
                warnModel.setSuccessList(successList);
                warnModel.setSucceedList(succeedList);
            }
        } catch (Exception e) {
            _log.error("getWarningInfoOrg is error", e);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getWarningInfoOrg is error", _log);
        }
        return warningInfoList;
    }

    /**
     * 查询告警等级
     * @param prjName
     * @param flowName
     * @param actName
     * @return
     */
    public String getWarningLevel( String prjName,String flowName,String actName) {
        String whereSql = "";
        String sql = " SELECT ILEVEL  FROM IEAI_SYSALARM_LEVEL_DG WHERE IPRJNAME = ? AND IFLOWNAME = ?";
        boolean isChild = false ;
        if (StringUtils.isNotBlank(actName) && !"null".equals(actName)) {
            whereSql += " AND IACTNAME = ? ";
        }else {
            isChild = isChild(prjName);
            if (isChild) {
                whereSql += " AND IACTNAME = ? ";
            }else {
                whereSql += " AND ( IACTNAME = '' OR IACTNAME IS NULL ) ";
            }
        }
        String level = "";
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = conn.prepareStatement(sql + whereSql);
            ps.setString(1,prjName);
            ps.setString(2, flowName);
            if (StringUtils.isNotBlank(actName) && !"null".equals(actName)) {
                ps.setString(3, actName);
            }else if (true == isChild){
                ps.setString(3, flowName);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                level = rs.getString("ILEVEL");
            }
        } catch (Exception e) {
            _log.error("getWarningLevel is error", e);
        } finally {
            DBResource.closeConn(conn,rs, ps, "getWarningLevel", _log);
        }
        return level;

    }

    /**
     * 判断是否是子流程
     * @param prjName
     * @return
     */
    public boolean isChild (String prjName) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean isMain = false;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int runningFlowNum = 0;
        String sql = "select count(1) from IEAI_EXCELMODEL WHERE ichildproname =? ";
        try {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = con.prepareStatement(sql);
            ps.setString(1, prjName);
            rs = ps.executeQuery();
            if (rs.next()) {
                runningFlowNum = rs.getInt(1);
            }
            if (runningFlowNum != 0) {
                isMain = true;
            }
        } catch (Exception e) {
            _log.info("isChild is error!", e);
        } finally {
            DBResource.closeConn(con, rs, ps, method, _log);
        }
        return isMain;
    }
    /**
     * 判断是否是健康的主server
     * @param ip
     * @return
     */
    public boolean isMainServer (String ip) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean isMain = false;
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int num = 0;
        String sql = "select count(1) from ieai_serverlist WHERE STATE = 0 AND IMAINSERVER = 1 AND IP = ? ";
        try {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            ps = con.prepareStatement(sql);
            ps.setString(1, ip);
            rs = ps.executeQuery();
            if (rs.next()) {
                num = rs.getInt(1);
            }
            if (num > 0) {
                isMain = true;
            }
        } catch (Exception e) {
            _log.info("isMainServer is error!", e);
        } finally {
            DBResource.closeConn(con, rs, ps, method, _log);
        }
        return isMain;
    }

}
