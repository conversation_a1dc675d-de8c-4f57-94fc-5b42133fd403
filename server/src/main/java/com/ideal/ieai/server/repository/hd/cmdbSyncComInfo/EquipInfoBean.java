package com.ideal.ieai.server.repository.hd.cmdbSyncComInfo;

public class EquipInfoBean {
    private long icpid;
    private String iname;
    private long imanufactoryId;
    private long iequtypeId;
    private long iequmodelnumberId;
    private String iipaddr;
    private long iid;
    private long ilastmodifytime;

    public long getIcpid() {
        return icpid;
    }

    public void setIcpid(long icpid) {
        this.icpid = icpid;
    }

    public String getIname() {
        return iname;
    }

    public void setIname(String iname) {
        this.iname = iname;
    }

    public long getImanufactoryId() {
        return imanufactoryId;
    }

    public void setImanufactoryId(long imanufactoryId) {
        this.imanufactoryId = imanufactoryId;
    }

    public long getIequtypeId() {
        return iequtypeId;
    }

    public void setIequtypeId(long iequtypeId) {
        this.iequtypeId = iequtypeId;
    }

    public long getIequmodelnumberId() {
        return iequmodelnumberId;
    }

    public void setIequmodelnumberId(long iequmodelnumberId) {
        this.iequmodelnumberId = iequmodelnumberId;
    }

    public String getIipaddr() {
        return iipaddr;
    }

    public void setIipaddr(String iipaddr) {
        this.iipaddr = iipaddr;
    }

    public long getIid() {
        return iid;
    }

    public void setIid(long iid) {
        this.iid = iid;
    }

    public long getIlastmodifytime() {
        return ilastmodifytime;
    }

    public void setIlastmodifytime(long ilastmodifytime) {
        this.ilastmodifytime = ilastmodifytime;
    }
}
