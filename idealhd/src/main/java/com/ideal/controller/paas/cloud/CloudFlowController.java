package com.ideal.controller.paas.cloud;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.jobscheduling.repository.flowquery.FlowQueryBean;
import com.ideal.ieai.server.paas.callconfig.CallconfigManage;
import com.ideal.service.jobscheduling.flowquery.FlowQueryService;
import com.ideal.service.paas.cloud.api.CloudAPIService;





@Controller
public class CloudFlowController
{
    
    private  Logger     log     = Logger.getLogger(CloudFlowController.class);
    
    @RequestMapping("passCloud/flowQuery.do")
    @ResponseBody
    public Map flowQuery ( HttpServletRequest request, Integer start, Integer limit,Long status, String queryString, Long iorderid,String modelType ) 
    {
        Map map = new HashMap();
        try
        {
            Map<String,String> params = new HashMap<String,String>();
            params.put("start", start.toString());
            params.put("limit", limit.toString());
            params.put("status", null==status?"":status.toString());
            params.put("queryString", queryString);
            params.put("iorderid", iorderid.toString());
            params.put("modelType", modelType);
            
            CloudAPIService cp = new CloudAPIService();
            String states = null;
            if(status != null) {
                states = String.valueOf(status);
            }
       map =    cp.queryFlow(start, limit,states , queryString, iorderid,27 , "27");
            
            
//            JSONObject obj = null;
//            
//            int total = obj.getIntValue("total");
//            
//            JSONArray datas = obj.getJSONArray("dataList");
//            List list = datas.toJavaList(CloudFlowModel.class);
//            List list =null;
//            map.put("count", total);
//            map.put("dataList", list);
            
        } catch (Exception e)
        {
            log.error("flowQuery is error!", e);
        }
        
        return map;
    }
  
    @RequestMapping("passCloud/resumeFlow.do")
    @ResponseBody
    public Map resumeFlow ( HttpServletRequest request,Long[] iids,Long iorderid ) {
        
        
        
        
        Map map = new HashMap();
        SessionData sessionData = SessionData.getSessionData(request);
        FlowQueryBean flowQueryBean = new FlowQueryBean();
        flowQueryBean.setUserId(Long.valueOf(sessionData.getUserInnerCode()));
        flowQueryBean.setUserName(sessionData.getUserName());
        flowQueryBean.setDbType(Constants.IEAI_PAAS);
        flowQueryBean.setFlowIds(iids);
        try
        {
            FlowQueryService.getInstance().resumeFlow(flowQueryBean);
            map.put(MESSAGE, "恢复成功！");
            map.put(SUCCESS, true);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "恢复失败！"+e.getMessage());
            log.error("resume Flow is error!", e);
        }
        return map;
        
    }

    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";
    @RequestMapping("passCloud/pauseFlow.do")
    @ResponseBody
    public Map pauseFlow ( HttpServletRequest request, Long[] iids ,Long iorderid) 
    {
        Map map = new HashMap();
        SessionData sessionData = SessionData.getSessionData(request);
        FlowQueryBean flowQueryBean = new FlowQueryBean();
        flowQueryBean.setUserId(Long.valueOf(sessionData.getUserInnerCode()));
        flowQueryBean.setUserName(sessionData.getUserName());
        flowQueryBean.setDbType(Constants.IEAI_PAAS);
        flowQueryBean.setFlowIds(iids);
        try
        {
            FlowQueryService.getInstance().pauseFlow(flowQueryBean);
            map.put(MESSAGE, "暂停成功！");
            map.put(SUCCESS, true);
        } catch (ServerException e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "暂停失败！"+e.getMessage());
            log.error("pause Flow is error!", e);
           
        }
        
        
        return map;

    }
    

    @RequestMapping("passCloud/killFlow.do")
    @ResponseBody
    public Map killFlow ( HttpServletRequest request,Long[] iids  ,Long iorderid) 
    {
        Map map = new HashMap();
        SessionData sessionData = SessionData.getSessionData(request);
        FlowQueryBean flowQueryBean = new FlowQueryBean();
        flowQueryBean.setUserId(Long.valueOf(sessionData.getUserInnerCode()));
        flowQueryBean.setUserName(sessionData.getUserName());
        flowQueryBean.setDbType(Constants.IEAI_PAAS);
        flowQueryBean.setFlowIds(iids);
        try
        {
            FlowQueryService.getInstance().killFlow(flowQueryBean);
            map.put(MESSAGE, "终止成功！");
            map.put(SUCCESS, true);
        } catch (ServerException e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "终止失败！"+e.getMessage());
            log.error("pause Flow is error!", e);
           
        }
        
        
        return map;
        
        

    }
    @RequestMapping("passCloud/repeatOrSkipAct.do")
    @ResponseBody
    public Map repeatOrSkipAct ( HttpServletRequest request, Long flowId, Long errorTaskId, String execModel,
            String sts, String actName, String actId,Long iorderid )
    {
        return null;
//        Map map = new HashMap();
//        try
//        {
//            Map<String,String> params = new HashMap<String,String>();
//            params.put("flowId", flowId.toString());
//            params.put("errorTaskId", errorTaskId.toString());
//            params.put("execModel",execModel);
//            params.put("state",sts);
//            params.put("actName",actName);
//            params.put("actId",actId);
//            SessionData sessionData = SessionData.getSessionData(request);
//            params.put("userid", sessionData.getUserInnerCode());
//            params.put("fullname", sessionData.getUserName());
//            
//            
//            HttpApi api = new HttpApi();
//            CallconfigManage cfgManager = new CallconfigManage();
//            String url = cfgManager.getCallConfigAddressWithOrderId(iorderid)+"cloudRepeatOrSkipAct.do";
//            String result = api.doGet(url, params);
//            
//            JSONObject obj = JSONObject.parseObject(result);
//            
//            boolean success = obj.getBooleanValue("success");
//            String message = obj.getString("message");
//            
//            map.put("success", success);
//            map.put("message", message);
//        } catch (Exception e)
//        {
//            map.put("success", false);
//            map.put("message", "repeat or skip act is error!"+e.getMessage());
//            log.error("repeat or skip act is error!", e);
//        }
//        return map;
    }
}
