	
-- 4.7.28
    DELIMITER ;; 
    DROP PROCEDURE IF EXISTS PROC_RUN_INSTANCE;
    CREATE PROCEDURE PROC_RUN_INSTANCE(IN FLAGTYPE NUMERIC(19,0),IN SERVERIP VARCHAR(255),OUT AN_OPT_ID NUMERIC(19,0))
	BEGIN
		DECLARE IS_COUNT  NUMERIC(19);
		DECLARE	LN_OUT_OPT_ID	NUMERIC(19,0);		SELECT
		COUNT(*) 
		INTO
			IS_COUNT 
		FROM
			IEAI_RUN_INSTANCE RI,
			IEAI_RUNINFO_INSTANCE R 
		WHERE
			RI.IID=R.IRUNINSID AND
			RI.IMXGRAPHID IS NULL AND
			R.IRERUNFLAG=0 AND
			(RI.ISTATE = 0 OR RI.ISTATE = -6) AND
			R.ISTATE IN (0,1) AND
			RI.ISERVERIP=SERVERIP;
		
		IF IS_COUNT > 0 THEN
			CALL	PROC_GET_NEXT_ID ('TMP_RUN_INSTANCE', LN_OUT_OPT_ID);
			SET	AN_OPT_ID = LN_OUT_OPT_ID;		INSERT
			INTO
				TMP_RUN_INSTANCE(OPID,
				MAINID,
				WORKID,
				IRUNINSNAME,
				ISERNER,
				ICONNER,
				IPRENER,
				IACTNAME,
				IACTDES,
				IACTTYPE,
				IREMINFO,
				IIP,
				IPORT,
				ISYSTYPE,
				ISHELLPATH,
				ITIMEOUT,
				ISWITCHSYSTYPE,
				IPARAMETER,
				IPARACHECK,
				IPARASWITCH,
				IPARASWITCHFORCE,
				IUSERNAME,
				ISHELLSCRIPT,
				IUSERID,
				IOPERTYPE,
				IEXECUSER,
				IEXPECEINFO,
				IISLOADENV ,
				ICONNERNAME,
				ICENTER ,
				IMODELTYPE ,
				IRETNVALEXCEPION ,
				IREDOABLE,
				IPKGNAME,
				IVERSION,
				IPRESYSNAME,
				IPREACTNAME,
				ISPACEIID,
       			IGOURPIID,ISTATE) 
			SELECT
				DISTINCT concat(LN_OUT_OPT_ID,'') ,
				BB.IID,
				AA.IID AS ID ,
				BB.IRUNINSNAME ,
				AA.ISERNER ,
				AA.ICONNER ,
				AA.IPRENER ,
				AA.IACTNAME ,
				AA.IACTDES ,
				AA.IACTTYPE ,
				AA.IREMINFO ,
				AA.IIP ,
				AA.IPORT ,
				BB.ISYSTYPE ,
				AA.ISHELLPATH ,
				AA.ITIMEOUT ,
				AA.ISWITCHSYSTYPE ,
				AA.IPARAMETER ,
				AA.IPARACHECK ,
				AA.IPARASWITCH ,
				AA.IPARASWITCHFORCE ,
				BB.ISTARTUSER ,
				AA.ISHELLSCRIPT ,
				BB.ISTARTUSERID ,
				AA.ISYSTYPE ,
				AA.IEXECUSER ,
				AA.IEXPECEINFO ,
				AA.IISLOADENV ,
				AA.ICONNERNAME,
				AA.ICENTER ,
				AA.IMODELTYPE ,
				AA.IRETNVALEXCEPION ,
				AA.IREDOABLE ,
				AA.IPKGNAME,
				BB.IVERSION,
				AA.IPRESYSNAME,
				AA.IPREACTNAME,
				AA.ISPACEIID,
				AA.IGOURPIID,
                BB.ISTATE
			FROM
				IEAI_RUNINFO_INSTANCE AA ,
				(	SELECT
						RI.IID ,
						MIN(R.ICONNER) AS ICONNER ,
						RI.IRUNINSNAME ,
						RI.ISYSTYPE ,
						RI.ISTARTUSER ,
						RI.ISERVERIP,
						RI.ISTARTUSERID ,
						RI.IVERSION ,
						RI.ISTATE
					FROM
						IEAI_RUN_INSTANCE RI ,
						IEAI_RUNINFO_INSTANCE R 
					WHERE
						RI.IID=R.IRUNINSID AND
						RI.IMXGRAPHID IS NULL AND
						(RI.ISTATE = 0 OR RI.ISTATE = -6) AND
						RI.ISERVERIP=concat(SERVERIP,'') AND
						R.ISTATE=1 
					GROUP BY
						RI.IID,
						RI.IRUNINSNAME,
						RI.ISYSTYPE,
						RI.ISTARTUSER,
						RI.ISERVERIP,
						RI.ISTARTUSERID,
						RI.IVERSION,
						RI.ISTATE ) BB 
			WHERE
				AA.IRUNINSID=BB.IID AND
				AA.ICONNER=BB.ICONNER AND
				AA.ISTATE=1 
			ORDER BY
				AA.ICONNER ASC ,
				AA.IPRENER DESC;	END IF;
	END;;
	DELIMITER ; 
	
-- 8.1.0
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS PROC_MV_RUN_INSTANCE;
		CREATE PROCEDURE PROC_MV_RUN_INSTANCE(IN WORKID NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
		BEGIN
		INSERT INTO IEAI_RUNINFO_INSTANCE_HIS(IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,ISRTO,IINFORTO,IAZNAME,IPROXYIP)   SELECT  IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,ISRTO,IINFORTO,IAZNAME,IPROXYIP  FROM  IEAI_RUNINFO_INSTANCE  WHERE  IRUNINSID=WORKID;
		INSERT INTO IEAI_RUN_INSTANCE_HIS(IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER)  SELECT  IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2 ,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER    FROM IEAI_RUN_INSTANCE WHERE  IID =WORKID;
		DELETE FROM  IEAI_RUNINFO_INSTANCE  WHERE  IRUNINSID=WORKID;
		DELETE FROM  IEAI_RUN_INSTANCE  WHERE IID=WORKID;
		END ;;
		DELIMITER ;
		CALL UPPRDE();