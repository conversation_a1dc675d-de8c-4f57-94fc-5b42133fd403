package com.ideal.service.ic.gfcheckhc;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.ic.gfcheckhc.*;
import com.ideal.ieai.server.repository.hd.ic.supperhc.checkresult.CheckActWarningBean;
import com.ideal.ieai.server.repository.hd.ic.supperhc.checkresult.CheckResultDataLastBean;
import com.ideal.ieai.server.repository.hd.ic.supperhc.checkresult.CheckResultManager;
import com.ideal.ieai.server.repository.hd.ic.supperhc.checkresult.CheckStatusLastBean;
import com.ideal.ieai.server.repository.hd.ic.supperhc.common.SupperHcConstants;
import com.ideal.ieai.server.repository.hd.ic.supperhc.exception.SupperhcException;
import com.ideal.service.ic.supperhc.warnlevel.HcWarnLevelCommon;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
public class GfCheckHcHandworkService
{
    private Logger log = Logger.getLogger(GfCheckHcHandworkService.class);
    
    /**
     * 
     * <li>Description:待办列表查询功能</li> 
     * <AUTHOR>
     * 2020年9月30日 
     * @param req
     * @param userId
     * @return
     * return Map
     */
    public Map getGfCheckHandworkPengdingList ( GfCheckHandworkShowReq req,String userId)
    {
        
        Map<String ,Object> map = null;
        Connection conn = null; 
        Long userid = -1L;
        GfCheckHcHandworkManage manager = new GfCheckHcHandworkManage();
        try
        {
            userid = Long.parseLong(userId);
            conn = DBResource.getConnection("getGfCheckHandworkPengdingList", log, Constants.IEAI_HEALTH_INSPECTION);
            map = manager.getGfCheckHandworkPengdingList(conn, userid,  req);
        } catch (Exception e)
        {
           log.error("获取手工巡检待办信息列表出错", e);
        }finally {
            DBResource.closeConnection(conn, "getGfCheckHandworkPengdingList", log);
        }
        return map;
    }


    public  Map excuteHandworkPengdingRecord(String username,Long iid,String res,String msg,String tskname, String batchNumber) throws SupperhcException {
        Map<String,Object> map = new HashMap<String,Object>();

        Connection conn = null;
        GfCheckHandworkHisModel hisModel = null;
        try
        {
            conn = DBResource.getConnection("excuteHandworkPengdingRecord", log, Constants.IEAI_HEALTH_INSPECTION);
            //迁移记录至历史
            GfCheckHcHandworkManage manager = new GfCheckHcHandworkManage();
            GfCheckHandworkPengdingShowModel existShowHand = manager.getGfCheckHandworkPengdingOneFull(iid, conn);
            if(existShowHand!=null) {
                hisModel = new GfCheckHandworkHisModel();
                hisModel.setIid(existShowHand.getIid());
                hisModel.setSystemid(existShowHand.getSystemid());
                hisModel.setSystemname(existShowHand.getSystemname());
                hisModel.setComputerid(existShowHand.getComputerid());
                hisModel.setComputerip(existShowHand.getComputerip());
                hisModel.setComputername(existShowHand.getComputername());

                hisModel.setChkitemid(existShowHand.getChkitemid());
                hisModel.setChkitemname(existShowHand.getChkitemname());

                hisModel.setChkpointid(existShowHand.getChkpointid());
                hisModel.setChkpointname(existShowHand.getChkpointname());
                hisModel.setChkpointdesc(existShowHand.getChkpointdesc());

                hisModel.setChkcmdid(existShowHand.getCmdid());
                hisModel.setCmdname(existShowHand.getCmdname());//20201210
                hisModel.setCmddesc(existShowHand.getCmddesc());

                hisModel.setWdate(existShowHand.getWdate());
                hisModel.setLdate(existShowHand.getLdate());
                hisModel.setPushcount(existShowHand.getPushcount());
                hisModel.setHandleuser(username);
                hisModel.setHandlemsg(msg);
                hisModel.setExcuteResult(res);
                hisModel.setTaskname(tskname);
                hisModel.setStartuser(existShowHand.getStartuser());

                GfCheckHcHandworkHisManage hisManage = new GfCheckHcHandworkHisManage();

                //插入历史表
                hisManage.addGfCheckHandworkHis(conn, hisModel);

                //删除在线表
                manager.deleteGfCheckHandworkPengding(conn, iid);
                conn.commit();
                //2生成巡检结果，存入结果表
                saveResultForHc(existShowHand.getChkpointid(), res, batchNumber);
            }else {
                map.put(SupperHcConstants.RES_SUCC, false);
                map.put(SupperHcConstants.RES_MSG, "巡检点配置信息未获取到");
            }

            map.put(SupperHcConstants.RES_SUCC, true);
            map.put(SupperHcConstants.RES_MSG, "处理成功");

        } catch (SQLException e)
        {
            log.error("处理手工巡检提交事务失败！", e);
            throw new SupperhcException("处理手工巡检提交事务失败！",e);
        } catch (RepositoryException e)
        {
            log.error("处理手工巡检失败！", e);
            throw new SupperhcException("处理手工巡检失败！",e);
        }finally {
            DBResource.closeConnection(conn, "excuteHandworkPengdingRecord", log);
        }
        return map;

    }
    
    
    
    
    
    public JSONArray buildDBJsonForHandWork(String key,String value, String errorcode) {
        JSONArray arr = new JSONArray();
        try {
                JSONObject j = new JSONObject();
                j.put("key", key);
                j.put("value", value);//"check IPMI device not found"
                j.put("errorcode", errorcode);
                j.put("cptime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                j.put("errortype", "0");
                arr.add(j);
        }catch (Exception e) {
            JSONObject j = new JSONObject();
            j.put("key", key);
            j.put("value", value);
            j.put("errorcode", "500");
            j.put("cptime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            j.put("errortype", "1");
            arr.add(j);
        }
        return arr;
    } 
    

    private String translateJsonObj(JSONObject obj,int i) {
        String res = "";
        try {
            res = obj.getString("I"+i);
        }catch (Exception e) {
        }
        return res;
    }
    
    private String translateNaToEmpty(String obj) {
        if(obj!=null && "NA".equals(obj.trim())) {
            return "";
        }else {
            return obj;
        }
    }

    public void saveResultForHc(Long cpid,String value ,String batchNumber) {
        GfCheckHcHandworkManage manager = new GfCheckHcHandworkManage();
        GfCheckPointBean bean = null;
        int defaultminLevel = 3;
        long checktime = System.currentTimeMillis();
        int totalLevel = 0;
        log.info("start anlysis result...");
        List<CheckResultDataLastBean> lastList = new ArrayList<CheckResultDataLastBean>();
        List<CheckStatusLastBean> checkDetail = new ArrayList<CheckStatusLastBean>();
        List<CheckActWarningBean> waringList  = new ArrayList<CheckActWarningBean>();
        CheckResultDataLastBean dataBean = new CheckResultDataLastBean();


        Connection conn = null;


        try
        {
            conn = DBResource.getConnection("saveResultForHc", log, Constants.IEAI_HEALTH_INSPECTION);
            bean = manager.getPointBean(conn, cpid);
            if(bean==null) {
                throw new SupperhcException("未能获取到对应的巡检点配置信息！");
            }
            int level =  Integer.parseInt(bean.getIdefaultwarnlevel());
            if(value!=null  ) {
                level = HcWarnLevelCommon.getInstance().getWarnLevel(value, Integer.parseInt(bean.getIdefaultwarnlevel()), Integer.parseInt(bean.getIcomparerule()), bean.getIthreshold(), bean.getIwarnlevel());
            }



            if (level >= defaultminLevel)
            {
                String  scriptparam = bean.getScriptparam();
                String errorcode = "";
                if( scriptparam!=null && !"".equals(scriptparam.trim()) && scriptparam.trim().length()>0 && !"null".equals(scriptparam)  ){
                    errorcode = scriptparam.split(",")[0];
                }

                if(errorcode==null || "".equals(errorcode.trim())) {
                    errorcode = bean.getErrorcode();
                }
                if(errorcode==null || "".equals(errorcode.trim())) {
                    errorcode="500";
                }
                log.info("获取到配置报警码为："+errorcode+";scriptparam="+scriptparam+";bean.getErrorcode()="+bean.getErrorcode());
                long errorcodeNum = 500L;

                try {
                    errorcodeNum =  Long.parseLong(errorcode.trim());
                }catch (Exception e) {
                    log.error("手工巡检配置错误格式非数字类型,默认错误码为500");
                    errorcodeNum = 500L;
                }


                CheckActWarningBean warningBean = new CheckActWarningBean();
                warningBean.setCpid(cpid);
                warningBean.setThreadhold(bean.getIthreshold());
                warningBean.setAmessage(bean.getKey());

                warningBean.setWcode(level);

                warningBean.setChkvalue(value);
                warningBean.setThreadhold(bean.getIthreshold() );
                warningBean.setErrcode(errorcodeNum);

                warningBean.setWdate(checktime);
                warningBean.setLdate(checktime);
                warningBean.setIbatchnumber(batchNumber);

                waringList.add(warningBean);
            }
            totalLevel=totalLevel>level?totalLevel:level;
            CheckStatusLastBean statusBean = new CheckStatusLastBean();
            statusBean.setCpid(cpid);
            statusBean.setI1(String.valueOf(level));
            statusBean.setI2(String.valueOf(checktime));
            statusBean.setI3(value);
            statusBean.setI4(bean.getIthreshold() );
            statusBean.setI5(bean.getKey());
            checkDetail.add(statusBean);

            dataBean.setCpid(cpid);
            dataBean.setCpstatus(totalLevel);
            dataBean.setCptimestamp(checktime);

            dataBean.setCptime(checktime);
            dataBean.addDetails(checkDetail);
            dataBean.addWarnings(waringList);

            //手工巡检产生巡检结果记录此时启动人
            dataBean.setStartuser(bean.getStartuser());
            dataBean.setIbatchnumber(batchNumber);

            lastList.add(dataBean);
            log.info("finish anlysis result...");
            //保存数据
            log.info("start save result...");

            if(!lastList.isEmpty()) {
                CheckResultManager resultmanager = CheckResultManager.getInstance();
                boolean aa = resultmanager.saveCheckResult(lastList, "HD_CHECK_RESULT_DATA_LAST", Constants.IEAI_HEALTH_INSPECTION)
                        && resultmanager.saveCheckResult(lastList, "HD_CHECK_RESULT_DATA_CACHE",Constants.IEAI_HEALTH_INSPECTION);

                if(aa) {
                    log.info("巡检点【"+cpid+":"+bean.getKey()+"】手工巡检结果生成成功！");
                }else {
                    log.info("巡检点【"+cpid+":"+bean.getKey()+"】手工巡检结果生成失败！");
                }

            }
            log.info("finish save result...");


        } catch (RepositoryException e)
        {
            log.error("手工巡检结果处理失败", e);
        }catch (SupperhcException e)
        {
            log.error("手工巡检结果处理过程中出现异常", e);
        }finally {
            DBResource.closeConnection(conn, "saveResultForHc", log);
        }


    }
    
    
    
    
    private List jsonTranslateGfCheckHandworkPengding ( String jsonData )
    {
        List dataList = new ArrayList();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData)
        {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++)
            {
                GfCheckHandworkPengdingModel model = new GfCheckHandworkPengdingModel();
                model.setIid(jsonArr.getJSONObject(i).optString("iid")==null||"".equals(jsonArr.getJSONObject(i).optString("iid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("iid")));
                model.setSystemid(jsonArr.getJSONObject(i).optString("systemid")==null||"".equals(jsonArr.getJSONObject(i).optString("systemid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("systemid")));
                model.setComputerid(jsonArr.getJSONObject(i).optString("computerid")==null||"".equals(jsonArr.getJSONObject(i).optString("computerid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("computerid")));
                model.setChkitemid(jsonArr.getJSONObject(i).optString("chkitemid")==null||"".equals(jsonArr.getJSONObject(i).optString("chkitemid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("chkitemid")));
                model.setChkpointid(jsonArr.getJSONObject(i).optString("chkpointid")==null||"".equals(jsonArr.getJSONObject(i).optString("chkpointid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("chkpointid")));
                model.setChkpointname(jsonArr.getJSONObject(i).optString("chkpointname"));
                model.setChkpointdesc(jsonArr.getJSONObject(i).optString("chkpointdesc"));
                model.setWdate(jsonArr.getJSONObject(i).optString("wdate")==null||"".equals(jsonArr.getJSONObject(i).optString("wdate"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("wdate")));
                model.setLdate(jsonArr.getJSONObject(i).optString("ldate")==null||"".equals(jsonArr.getJSONObject(i).optString("ldate"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("ldate")));
                model.setPushcount(jsonArr.getJSONObject(i).optString("pushcount")==null||"".equals(jsonArr.getJSONObject(i).optString("pushcount"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("pushcount")));
                dataList.add(model);
            }
        }
        return dataList;
    }
    
    
    /**
     * 
     * <li>Description:保存待办记录</li> 
     * <AUTHOR>
     * 2020年9月29日 
     * @param models
     * @param sysType
     * @throws SupperhcException
     * @throws RepositoryException
     * return void
     */
    public void saveGfCheckHandworkPengding(List<GfCheckHandworkPengdingModel> models,int sysType) throws SupperhcException, RepositoryException{
        Connection conn = null;
        GfCheckHcHandworkManage manage = new GfCheckHcHandworkManage();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if(!models.isEmpty()){
                for(int i=0;i<models.size();i++){
                    GfCheckHandworkPengdingModel model = (GfCheckHandworkPengdingModel)models.get(i);
                    if(model.getIid()==0){
                        manage.addGfCheckHandworkPengding(conn, model);
                    }else{
                        manage.updateGfCheckHandworkPengding(conn, model);
                    }
                }
            }
            conn.commit();
        } catch (Exception e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, e,"saveGfCheckHandworkPengding", log);
            log.error("GfCheckHcHandworkService.saveGfCheckHandworkPengding is error", e);
            throw new SupperhcException(e);
        } finally
        {
            DBResource.closeConnection(conn, "saveGfCheckHandworkPengding", log);
        }
    }
    
    
    
    public void deleteGfCheckHandworkPengding(String iids,int sysType) throws SupperhcException, RepositoryException{
        Connection conn = null;
        GfCheckHcHandworkManage manage = new GfCheckHcHandworkManage();
        try
        {
            String[] iid = iids.split(",");
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if(iid!=null&&iid.length>0){
                for(int i=0;i<iid.length;i++){
                    manage.deleteGfCheckHandworkPengding(conn, Long.valueOf(iid[i]));
                }
            }
            conn.commit();
        } catch (Exception e)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, e,"deleteGfCheckHandworkPengding", log);
            log.error("GfCheckHcHandworkService.deleteGfCheckHandworkPengding is error", e);
            throw new SupperhcException(e);
        } finally
        {
            DBResource.closeConnection(conn, "deleteGfCheckHandworkPengding", log);
        }
    }
    
    

    public Map<String ,Object> getGfCheckHandworkPengdingListFirst ( Long userid) throws SupperhcException {
        GfCheckHcHandworkManage manage = new GfCheckHcHandworkManage();
        return  manage.getGfCheckHandworkPengdingListFirst(userid);
    }
    
}