
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CICD_UPLOADDOCKERIMG' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_CICD_UPLOADDOCKERIMG (IID NUMBER (19)   NOT NULL,IBUILDSERVERID NUMBER (19)  ,IFLOWID NUMBER (19)  ,IUPLOADURL VARCHAR (255)  ,ICREATEUSERID NUMBER (19)  ,ICREATETIME NUMBER (19)  ,IUUID VARCHAR (255)  , CONSTRAINT PK_IEAI_CICD_UPLOADDOCKERIMG PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_FLOWTASK' AND COLUMN_NAME='IDESTROYDOCKER'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_FLOWTASK ADD IDESTROYDOCKER NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_FLOWTASK' AND COLUMN_NAME='IUPPERID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_FLOWTASK ADD IUPPERID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_TASK_PARAM' AND COLUMN_NAME='INEWDATA'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_TASK_PARAM ADD INEWDATA NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_TASK_PARAM' AND COLUMN_NAME='IENCRYPTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_TASK_PARAM ADD IENCRYPTYPE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_INSINFO_PARAMS' AND COLUMN_NAME='INEWDATA'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_INSINFO_PARAMS ADD INEWDATA NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_INSINFO_PARAMS' AND COLUMN_NAME='IENCRYPTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_INSINFO_PARAMS ADD IENCRYPTYPE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_CUSTOM_PARAMS' AND COLUMN_NAME='INEWDATA'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_CUSTOM_PARAMS ADD INEWDATA NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CICD_CUSTOM_PARAMS' AND COLUMN_NAME='IENCRYPTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_CICD_CUSTOM_PARAMS ADD IENCRYPTYPE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_STATIC_PARAM' AND COLUMN_NAME='INEWDATA'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_STATIC_PARAM ADD INEWDATA NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_STATIC_PARAM' AND COLUMN_NAME='IENCRYPTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SUS_STATIC_PARAM ADD IENCRYPTYPE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESGROUP_EXTENDSPARAMETER' AND COLUMN_NAME='INEWDATA'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RESGROUP_EXTENDSPARAMETER ADD INEWDATA NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESGROUP_EXTENDSPARAMETER' AND COLUMN_NAME='IENCRYPTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_RESGROUP_EXTENDSPARAMETER ADD IENCRYPTYPE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN     
    SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_CI_VARIABLE  P WHERE P.IID=51;  
  IF  LI_EXISTS = 0 THEN
  	INSERT INTO IEAI_CI_VARIABLE(IID, INAME, ITYPE, IDESCRIBE, IATTRIBUTE, ICREATE_USER, ICREATE_TIME,IVARIABLETYPE) VALUES(51,'dockerDestroy',0,'销毁容器:是/否',0,'ideal',sysdate,38);      COMMIT;
      COMMIT;
  END IF;     
END;
/
DECLARE 
     LI_SIGN SMALLINT;
     LS_SQL  VARCHAR2(2000); 
BEGIN
		SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME ='IDX_IEAI_RUN_SQLSCRIPT' AND OBJECT_TYPE = 'INDEX';
		IF	LI_SIGN = 0 THEN
            LS_SQL := 'CREATE INDEX IDX_IEAI_RUN_SQLSCRIPT ON IEAI_RUN_SQLSCRIPT (IBATCHINDEX, IFILEINDEX, IRUNINFO_ID, ISQLINDEX)';
            EXECUTE IMMEDIATE LS_SQL;
        END	IF; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IEAI_CICD_FLOWTASK' AND COLUMN_NAME='IDESTROYDOCKER'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE TMP_IEAI_CICD_FLOWTASK ADD IDESTROYDOCKER NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IEAI_CICD_FLOWTASK' AND COLUMN_NAME='IUPPERID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE TMP_IEAI_CICD_FLOWTASK ADD IUPPERID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IEAI_CICD_TASK_PARAM' AND COLUMN_NAME='INEWDATA'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE TMP_IEAI_CICD_TASK_PARAM ADD INEWDATA NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IEAI_CICD_TASK_PARAM' AND COLUMN_NAME='IENCRYPTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE TMP_IEAI_CICD_TASK_PARAM ADD IENCRYPTYPE NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN     
    SELECT COUNT(TID) INTO LI_EXISTS  FROM IEAI_FTPTYPE  P WHERE P.TID=200;  
  IF  LI_EXISTS = 0 THEN
      INSERT INTO IEAI_FTPTYPE(TID, FTPTYPENAME) VALUES(200, '镜像存储服务器');      
      COMMIT; 
  END IF;     
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN     
    SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_MENU  P WHERE P.IID=16103;  
  IF  LI_EXISTS = 0 THEN
      INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(16103,'审计模板设置',3,'auditModelSetting.do',8,'应用变更','null');      
      COMMIT; 
  END IF;     
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_FTP_PARAM' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SUS_FTP_PARAM (IID NUMBER (19)   NOT NULL,ISTEPID NUMBER (19)  ,IINSTANCEID NUMBER (19)  ,FTPID NUMBER (19)  ,TARGET_PATH VARCHAR2 (255)  ,PACKAGE_NAME VARCHAR2 (255)  ,SOURCE_PATH VARCHAR2 (255)  ,EXPANSION_TYPE VARCHAR2 (255)  ,EXISTTREATMENT NUMBER (19)  ,EXCEPTIONTREATMENT NUMBER (19)  ,RETRYCOUNT NUMBER (19)  ,DELAYINTERVAL NUMBER (19)  , CONSTRAINT PK_IEAI_SUS_FTP_PARAM PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
