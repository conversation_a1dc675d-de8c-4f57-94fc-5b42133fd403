/**
*@desc 系统模板二级编辑页面 
**/

var CodeMirrorEditor="";	
var scriptForm_edit ;


Ext.onReady(function (){
	  destroyRubbish();
      Ext.define('LSBTemplatePEXModel2',{
         extend: 'Ext.data.Model',
         fields: [{
            name: 'iid',
            type: 'long'
         }, {
            name: 'ios',
            type: 'string'
         }, {
            name: 'icontent',
            type: 'string'
         }]
      });
      

      


   	//右侧脚本内容			 
    scriptForm_edit = new Ext.form.FormPanel({
    	cls:'customize_panel_back',
    	region : 'center',
		border : true,
		fieldDefaults : {
			labelWidth : 60
		},
		dockedItems : [ {
			xtype : 'toolbar',
			//height:40,
			items : [{
		        cls : 'Common_Btn',
				text : '提交',
				hidden:hiddBtn,
				handler : updateClob
			}, '-', {
				text : '清空',
		        cls : 'Common_Btn',
				hidden:hiddBtn,
				handler : clearTextFiled
			} ]
		} ],
		defaultType : 'textfield',
		items : [
		 {
			labelWidth : 100,
			width: '200',
			xtype: 'textfield',
			fieldLabel: '操作系统版本',
			labelAlign : 'left',
			name: 'ios',
			margin: '0 0 10 10'
		 }, {
	        xtype: 'label',
	        text: 'PXE配置:',
			margin: '0 0 0 10'
	    }, {
			xtype : 'textarea',
			margin: '0 0 0 100',
			labelWidth : 100,
			fieldLabel: 'PXE配置',
			hideLabel : true,
			name : 'msg',
			height : contentPanel.getHeight()/2,
			width : '80%'
		} ]
	});
      var layoutPane_edit = Ext.create('Ext.panel.Panel',{
         region : 'center',
		 border: false,
         layout: 'border',
         width: '100%',
         height: contentPanel.getHeight()-30,
         items: [scriptForm_edit]
      });
      

      var mainPanel = Ext.create('Ext.panel.Panel',{
         width: '100%',
         height: '100%',
         items: [layoutPane_edit],
         border: false,
         renderTo: 'LSBTemplatePEX_editDiv'
      });
      

      contentPanel.on('resize', function (){
      LSBTemplatePEXGrid.setHeight(contentPanel.getHeight()-20);
      LSBTemplatePEXGrid.setWidth(contentPanel.getWidth());
      });
      

      contentPanel.getLoader().on('beforeload', function (obj, options, eOpts){
      Ext.destroy(mainPanel);
      if(Ext.isIE)
      {
         CollectGarbage();
      }
      });
      

      var LSBTemplatePEXStore_edit =Ext.create('Ext.data.Store', {
         pageSize: 25,
         autoDestroy: true,
         autoLoad: true,
         model: 'LSBTemplatePEXModel2',
         proxy: {
            type: 'ajax',
            url: 'queryLSBTemplateSys.do',
            reader: {
               type: 'json',
               root: 'dataList',
               totalProperty: 'total'
            }
         }
      });

		if(OPT_TYPE_ADD==_optType){
			setCodeMir();
		}else{
			//load data by iid 
			LSBTemplatePEXStore_edit.on ('beforeload', function (store, options)
		    {
			        var new_params =
			        {
			        		iid : curScriptIid
			        };
			        Ext.apply (LSBTemplatePEXStore_edit.proxy.extraParams, new_params);
		    });
			
			LSBTemplatePEXStore_edit.on ('load', function (store, options)
		    {
					for ( var k = 0; k < LSBTemplatePEXStore_edit.getCount(); k++) {
						var record = LSBTemplatePEXStore_edit.getAt(k);
						var ios = record.data.ios;
			   	  		scriptForm_edit.getForm().findField("ios").setValue(ios);
						
						var icontent = record.data.icontent;
			   	  		scriptForm_edit.getForm().findField("msg").setValue(icontent);
						 setCodeMir();
					}
		    });
		}

      String.prototype.trim=function (){
  		 return this.replace(/(^\s*)|(\s*$)/g, "");
	 };
      

      function setMessage (msg)      {
         Ext.Msg.alert('提示ʾ', msg);
      }



      

      function onSaveLSBTemplatePEX ()      {
         var m =LSBTemplatePEXStore_edit.getModifiedRecords();
         if(m.length<1)
         {
            return;
         }
         

         var jsonData ="[";
         for(var i =0,len=m.leng; len=m.length; i++)
         {
            var ss =Ext.JSON.encode( selections[i].data);
            if(i==0)
            {
               jsonData=jsonData+ss;
            }
            else
            {
               jsonData=jsonData+","+ss;
            }
         }
         var jsonData ="]";
         

         Ext.Ajax.request({
               url: 'saveLSBTemplatePEX.do',
               method: 'POST',
               params: {
                  jsonData: jsonData
               },
               success: function (response, opts){
                  onSaveLSBTemplatePEX.modified=[];
                  LSBTemplatePEXStore_edit.reload();
                  Ext.Msg.alert('提示ʾ', '保存成功!');
                  },
               failure: function (result, opts){
                  Ext.Msg.alert('提示ʾ', '保存失败!');
                  }
            });
      }



      

      function onDelLSBTemplatePEX ()      {
         var data =LSBTemplatePEXGrid.getView().getSelectionModel().getSelection();
         

         if(data.length==0)
         {
            Ext.Msg.alert('提示', '请至少选中一条记录!');
            return;
         }
         else
         {
            Ext.Msg.confirm('请确认', '是否要删除选中记录', function (button, text)   {
		      if(button=="yes")
		      {
		         var ids =[];
		         
		
		         Ext.Array.each(data, function (data){
				      var _iid =data.get('iid');
				      if(_iid)
				      {
				         ids.push(_iid);
				         Ext.Ajax({
				               url: 'deleteLSBTemplatePEX.do',
				               method: 'POST',
				               params: {
				                  iids: ids.join(',')
				               },
				               success: function (response, opts){
				                  LSBTemplatePEXStore_edit.reload();
				                  Ext.Msg.alert('提示ʾ', '删除成功!');
				                  },
				               failure: function (result, opts){
				                  Ext.Msg.alert('提示ʾ', '删除失败!');
				                  }
				            });
				      }
				});
		      }
   });
         }
      }



      




});

function setCodeMir(){
	 var mytextarea = scriptForm_edit.getForm().findField("msg");
	 var textarea = document.getElementsByName("msg")[0];
	 var fileType = "shell";
/*	 if(fileName != null && fileName != ''){
		 
		 var fileArray = fileName.split(".");
		 if(fileArray.length == 2){
			 if(fileArray[1] != "sh"){
				 fileType = fileArray[1];
			 }
			 
		 }
	 }*/
	 
	 CodeMirrorEditor = CodeMirror.fromTextArea(textarea, { 
        lineNumbers: true,//是否显示行号
        lineWrapping:true, //是否强制换行
        styleActiveLine: true,
    	 extraKeys : {
			"F11" : function(cm) {
				cm.setOption("fullScreen", !cm
						.getOption("fullScreen"));
			},
			"Esc" : function(cm) {
				if (cm.getOption("fullScreen"))
					cm.setOption("fullScreen", false);
			}
		 }
	});
	 
	 CodeMirrorEditor.setOption('mode', fileType);
}

/**
 * 更新脚本内容Clob到数据库中
 */
function updateClob(){
	CodeMirrorEditor.save();
	var content =scriptForm_edit.getForm().findField("msg").getValue();
	var _ios=scriptForm_edit.getForm().findField("ios").getValue();
	var main_iid=curScriptIid;
	if(content =='' || content ==null ){
		Ext.Msg.alert('错误提示', '模板内容不能为空');		
	}else{
	    Ext.Ajax.request({
	        url : 'saveLSBTemplateSys.do',
	        params : {
	        	iid : main_iid,
				ios:_ios,
	        	icontent:content
	        },
	        method : 'POST',
	        success : function(response, opts) {
	          var success = Ext.decode(response.responseText).success;
	          if (success) {
	        	  content =Ext.decode(response.responseText).icontent;
	        	  Ext.Msg.alert('提示', '脚本内容保存成功！');
	          } else {
	            Ext.Msg.alert('提示', '删除失败！');
	          }
	        }
	      });
	}

}

function clearTextFiled(){
	try{
		CodeMirrorEditor.undo();
	}catch(error){
		
	}	
	
	scriptForm_edit.getForm().findField("msg").setValue("");
}