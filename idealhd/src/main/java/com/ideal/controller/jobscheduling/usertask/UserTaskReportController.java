
package com.ideal.controller.jobscheduling.usertask;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ClientSessionHelper;
import com.ideal.ieai.server.jobscheduling.repository.project.ProjectBean;
import com.ideal.ieai.server.jobscheduling.repository.usertaskreport.UserTaskReportBean;
import com.ideal.ieai.server.jobscheduling.repository.workflow.WorkFlowBean;
import com.ideal.ieai.server.jobscheduling.util.tuple.TwoTuple;
import com.ideal.service.jobscheduling.project.ProjectService;
import com.ideal.service.jobscheduling.usertaskreport.UserTaskReportService;
import com.ideal.service.jobscheduling.workflow.WorkFlowService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * @ClassName: UserTaskReportController
 * @Description:人工任务详情 对应controller
 * @author: zhen_sui
 * @date: 2018年1月30日 上午11:22:51
 * 
 * @Copyright: 2018-2027 www.idealinfo.com Inc. All rights reserved.
 * 
 */
@Controller
public class UserTaskReportController
{
    private ProjectService  projectService  = ProjectService.getInstance();

    private WorkFlowService workFlowService = WorkFlowService.getInstance();

    private UserTaskReportService service= UserTaskReportService.getInstance();
    public static final String    DATALIST        = "dataList";

    /**
     * 
     * @Title: userTaskReportPage
     * @Description: 人工任务详情初始化页面
     * @return
     * @author: zhen_sui
     * @date: 2018年1月25日 上午11:47:12
     */
    @RequestMapping("userTaskReportPage.do")
    public String userTaskReportPage ()
    {
        return "jobScheduling/userTask/userTaskReport";
    }

    /**
     * 
     * @Title: getProjectInfoListForSelect
     * @Description: 获取工程名下拉框信息
     * @param request
     * @return
     * @throws Exception
     * @author: zhen_sui
     * @date: 2018年1月25日 上午11:47:58
     */
    @RequestMapping("getProjectInfoListForSelect.do")
    @ResponseBody
    public Object getProjectInfoListForSelect ( HttpServletRequest request )
    {
        JSONObject result = new JSONObject();
        JSONArray jsonArr = new JSONArray();
        SessionData sessData = SessionData.getSessionData(request);
        String sessionId = sessData.getSessionId();
        Long userId = ClientSessionHelper.getUserId(sessionId);
        List<ProjectBean> list = projectService.getProjectInfoListForSelect(userId.toString());
        jsonArr.addAll(list);
        result.put(DATALIST, jsonArr);
        return result;
    }

    /**
     * 
     * @Title: getWorkFlowListForSelect
     * @Description: 获取工作流名下拉框信息
     * @param request
     * @return
     * @throws Exception
     * @author: zhen_sui
     * @date: 2018年1月31日 上午8:48:42
     */
    @RequestMapping("getWorkFlowListForSelect.do")
    @ResponseBody
    public Object getWorkFlowListForSelect ( HttpServletRequest request )
    {
        JSONObject result = new JSONObject();
        JSONArray jsonArr = new JSONArray();
        Long projectId = Long.parseLong(request.getParameter("projectId"));
        List<WorkFlowBean> list = workFlowService.getWorkFlowName(projectId);
        jsonArr.addAll(list);
        result.put(DATALIST, jsonArr);
        return result;
    }

    /**
     * 
     * @Title: getUserTaskReportList
     * @Description: 获取人工任务详情列表
     * @param request
     * @return
     * @throws Exception
     * @author: zhen_sui
     * @date: 2018年1月25日 上午11:47:58
     */
    @RequestMapping("getUserTaskReportList.do")
    @ResponseBody
    public Object getUserTaskReportList ( HttpServletRequest request, UserTaskReportBean info )
    {
        JSONObject result = new JSONObject();
        TwoTuple<List<UserTaskReportBean>,Long> listResult= service.getList(info);
        JSONArray jsonArr = new JSONArray();
        jsonArr.addAll(listResult.first);
        result.put("total", listResult.second);
        result.put(DATALIST, jsonArr);
        return result;
    }
    
}
