package com.ideal.ieai.studio.view.editorui.acteditor;

import java.awt.BorderLayout;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

import javax.swing.ButtonGroup;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.event.ChangeListener;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.IActivityConfigView;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.studio.StudioRes;
import com.ideal.ieai.studio.util.adaptor.AdaptorManager;
import com.ideal.ieai.studio.view.editorui.flowchart.BasicCellObject;

/**
 * For act disaster recovery editor.
 * 
 * <AUTHOR>
 * 
 */
public class ActDisasterRecoveryEditor extends AbstractAttrEditor implements ActionListener,
        DocumentListener
{

    private JPanel          jPanel             = null;
    private JCheckBox       chkAsPoint         = null;
    private JPanel          panMethod          = null;
    private JRadioButton    rdRedoAct          = null;
    private JRadioButton    rdSkipAct          = null;
    private JRadioButton    rdManualProcess    = null;
    private JRadioButton    rdSelfCompensation = null;
    private JPanel          panDes             = null;
    private JScrollPane     scrDes             = null;
    private JTextArea       txtDes             = null;
    private JLabel          lblDes             = null;

    private ButtonGroup     _group             = new ButtonGroup();
    private BasicActElement _element;

    /**
     * This is the default constructor
     */
    public ActDisasterRecoveryEditor(Object obj)
    {
        super(obj);
        _object = (BasicCellObject) obj;
        _element = _object.getElement();
        initialize();
    }

    /**
     * This method initializes this
     * 
     * @return void
     */
    private void initialize ()
    {
        this.setLayout(new BorderLayout());
        this.setSize(300, 200);
        this.add(getJPanel(), java.awt.BorderLayout.CENTER);
        addListener();
        initView();
    }

    /**
     * init radio button
     * 
     */
    private void initRecoveryPolicy ()
    {
        int policy = Constants.RECOVERY_POLICY_REDO;
        if (_element instanceof ActivityElement)
        {
            IActivityConfigView view = null;
            try
            {
                view = AdaptorManager.getInstance().getActivityConfigView(
                    (ActivityElement) _element);
                view.initView(((ActivityElement) _element).getActConfig());
            } catch (ActivityException e)
            {
                view = null;
            }
            if (null != view)
            {
                policy = view.getSupportRecoveryPolicies();
            }
        } else
        {
            policy = (_element).getRecoveryPolicy();
        }
        if (Constants.containsRecoveryPolicy(policy, Constants.RECOVERY_POLICY_REDO))
        {
            this.rdRedoAct.setVisible(true);
        } else
        {
            this.rdRedoAct.setVisible(false);
        }
        if (Constants.containsRecoveryPolicy(policy, Constants.RECOVERY_POLICY_SKIP))
        {
            this.rdSkipAct.setVisible(true);
        } else
        {
            this.rdSkipAct.setVisible(false);
        }
        if (Constants.containsRecoveryPolicy(policy, Constants.RECOVERY_POLICY_MAN_PROCESS))
        {
            this.rdManualProcess.setVisible(true);
        } else
        {
            this.rdManualProcess.setVisible(false);
        }
        if (Constants.containsRecoveryPolicy(policy, Constants.RECOVERY_POLICY_COMPENSATE))
        {
            this.rdSelfCompensation.setVisible(true);
        } else
        {
            this.rdSelfCompensation.setVisible(false);
        }

    }

    private void initView ()
    {
        initRecoveryPolicy();
        this.chkAsPoint.setSelected(_element.isRecoveryPoint());
        this.chkAsPointAction();
        if (!this.chkAsPoint.isSelected())
        {// if not set as recovery point, return direct, not init components of method
            return;
        }
        int method = _element.getRecoveryMethod();
        if (method == Constants.RECOVERY_POLICY_REDO)
        {
            rdRedoAct.setSelected(true);
        } else if (method == Constants.RECOVERY_POLICY_SKIP)
        {
            this.rdSkipAct.setSelected(true);
        } else if (method == Constants.RECOVERY_POLICY_MAN_PROCESS)
        {
            this.rdManualProcess.setSelected(true);
        } else
        {
            this.rdSelfCompensation.setSelected(true);
        }
        // set description
        this.txtDes.setText(_element.getRecoveryDes());
    }

    private void addListener ()
    {
        this.chkAsPoint.addActionListener(this);
        this.chkAsPoint.addActionListener(new ActionListener()
        {

            public void actionPerformed ( ActionEvent e )
            {
                chkAsPointAction();
            }
        });
        this.rdRedoAct.addActionListener(this);
        this.rdSkipAct.addActionListener(this);
        this.rdManualProcess.addActionListener(this);
        this.rdSelfCompensation.addActionListener(this);
        this.txtDes.getDocument().addDocumentListener(this);
    }

    private void chkAsPointAction ()
    {
        boolean enable;
        if (this.chkAsPoint.isSelected())
        {
            enable = true;
        } else
        {
            enable = false;
        }
        int size = this.panMethod.getComponentCount();
        for (int i = 0; i < size; i++)
        {
            panMethod.getComponent(i).setEnabled(enable);
        }
        this.txtDes.setEnabled(enable);

    }

    /**
     * This method initializes jPanel
     * 
     * @return javax.swing.JPanel
     */
    private JPanel getJPanel ()
    {
        if (jPanel == null)
        {
            GridBagConstraints gridBagConstraints1 = new GridBagConstraints();
            gridBagConstraints1.gridx = 0;
            gridBagConstraints1.fill = java.awt.GridBagConstraints.BOTH;
            gridBagConstraints1.weightx = 1.0;
            gridBagConstraints1.weighty = 1.0;
            gridBagConstraints1.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints1.gridy = 1;
            GridBagConstraints gridBagConstraints = new GridBagConstraints();
            gridBagConstraints.gridx = 0;
            gridBagConstraints.insets = new java.awt.Insets(4, 4, 4, 4);
            gridBagConstraints.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints.gridy = 0;
            jPanel = new JPanel();
            jPanel.setLayout(new GridBagLayout());
            jPanel.add(getChkAsPoint(), gridBagConstraints);
            jPanel.add(getPanMethod(), gridBagConstraints1);
        }
        return jPanel;
    }

    /**
     * This method initializes chkAsPoint
     * 
     * @return javax.swing.JCheckBox
     */
    private JCheckBox getChkAsPoint ()
    {
        if (chkAsPoint == null)
        {
            chkAsPoint = new JCheckBox(StudioRes.getString("DisasterRecovery.point"));
        }
        return chkAsPoint;
    }

    /**
     * This method initializes panMethod
     * 
     * @return javax.swing.JPanel
     */
    private JPanel getPanMethod ()
    {
        if (panMethod == null)
        {
            GridBagConstraints gridBagConstraints7 = new GridBagConstraints();
            gridBagConstraints7.gridx = 0;
            gridBagConstraints7.insets = new java.awt.Insets(4, 4, 4, 4);
            gridBagConstraints7.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints7.gridwidth = 4;
            gridBagConstraints7.gridy = 1;
            lblDes = new JLabel();
            lblDes.setText(StudioRes.getString("DisasterRecovery.method.des"));
            GridBagConstraints gridBagConstraints3 = new GridBagConstraints();
            gridBagConstraints3.fill = GridBagConstraints.BOTH;
            gridBagConstraints3.gridwidth = 4;
            gridBagConstraints3.gridx = 0;
            gridBagConstraints3.gridy = 2;
            gridBagConstraints3.weightx = 1.0;
            gridBagConstraints3.weighty = 1.0;
            gridBagConstraints3.insets = new Insets(4, 4, 4, 4);
            GridBagConstraints gridBagConstraints6 = new GridBagConstraints();
            gridBagConstraints6.gridx = 3;
            gridBagConstraints6.insets = new java.awt.Insets(4, 4, 4, 4);
            gridBagConstraints6.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints6.gridy = 0;
            GridBagConstraints gridBagConstraints5 = new GridBagConstraints();
            gridBagConstraints5.gridx = 2;
            gridBagConstraints5.insets = new java.awt.Insets(4, 4, 4, 4);
            gridBagConstraints5.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints5.gridy = 0;
            GridBagConstraints gridBagConstraints4 = new GridBagConstraints();
            gridBagConstraints4.gridx = 1;
            gridBagConstraints4.insets = new java.awt.Insets(4, 4, 4, 4);
            gridBagConstraints4.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints4.gridy = 0;
            GridBagConstraints gridBagConstraints2 = new GridBagConstraints();
            gridBagConstraints2.insets = new java.awt.Insets(4, 4, 4, 4);
            gridBagConstraints2.gridy = 0;
            gridBagConstraints2.anchor = java.awt.GridBagConstraints.WEST;
            gridBagConstraints2.gridx = 0;
            panMethod = new JPanel();
            panMethod.setLayout(new GridBagLayout());
            panMethod.setBorder(javax.swing.BorderFactory.createTitledBorder(null, StudioRes
                    .getString("DisasterRecovery.method"),
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION, null, null));
            panMethod.add(getRdRedoAct(), gridBagConstraints2);
            panMethod.add(getRdSkipAct(), gridBagConstraints4);
            panMethod.add(getRdManualProcess(), gridBagConstraints5);
            panMethod.add(getRdSelfCompensation(), gridBagConstraints6);
            panMethod.add(getPanDes(), gridBagConstraints3);
            panMethod.add(lblDes, gridBagConstraints7);

            _group.add(this.rdRedoAct);
            _group.add(this.rdSkipAct);
            _group.add(this.rdManualProcess);
            _group.add(this.rdSelfCompensation);
            this.rdRedoAct.setSelected(true);
        }
        return panMethod;
    }

    /**
     * This method initializes rdRedoAct
     * 
     * @return javax.swing.JRadioButton
     */
    private JRadioButton getRdRedoAct ()
    {
        if (rdRedoAct == null)
        {
            rdRedoAct = new JRadioButton(StudioRes.getString("DisasterRecovery.redoAct"));
        }
        return rdRedoAct;
    }

    /**
     * This method initializes rdSkipAct
     * 
     * @return javax.swing.JRadioButton
     */
    private JRadioButton getRdSkipAct ()
    {
        if (rdSkipAct == null)
        {
            rdSkipAct = new JRadioButton(StudioRes.getString("DisasterRecovery.skipAct"));
        }
        return rdSkipAct;
    }

    /**
     * This method initializes rdManualProcess
     * 
     * @return javax.swing.JRadioButton
     */
    private JRadioButton getRdManualProcess ()
    {
        if (rdManualProcess == null)
        {
            rdManualProcess = new JRadioButton(StudioRes
                    .getString("DisasterRecovery.processManually"));
        }
        return rdManualProcess;
    }

    /**
     * This method initializes rdSelfCompensation
     * 
     * @return javax.swing.JRadioButton
     */
    private JRadioButton getRdSelfCompensation ()
    {
        if (rdSelfCompensation == null)
        {
            rdSelfCompensation = new JRadioButton(StudioRes
                    .getString("DisasterRecovery.compensation"));
        }
        return rdSelfCompensation;
    }

    /**
     * This method initializes panDes
     * 
     * @return javax.swing.JPanel
     */
    private JPanel getPanDes ()
    {
        if (panDes == null)
        {
            GridBagConstraints gridBagConstraints8 = new GridBagConstraints();
            gridBagConstraints8.fill = java.awt.GridBagConstraints.BOTH;
            gridBagConstraints8.gridx = 0;
            gridBagConstraints8.gridy = 0;
            gridBagConstraints8.ipadx = 0;
            gridBagConstraints8.weightx = 1.0;
            gridBagConstraints8.weighty = 1.0;
            gridBagConstraints8.insets = new java.awt.Insets(0, 0, 0, 0);
            panDes = new JPanel();
            panDes.setLayout(new GridBagLayout());
            panDes.add(getScrDes(), gridBagConstraints8);
        }
        return panDes;
    }

    /**
     * This method initializes scrDes
     * 
     * @return javax.swing.JScrollPane
     */
    private JScrollPane getScrDes ()
    {
        if (scrDes == null)
        {
            scrDes = new JScrollPane();
            scrDes.setViewportView(getTxtDes());
        }
        return scrDes;
    }

    /**
     * This method initializes txtDes
     * 
     * @return javax.swing.JTextArea
     */
    private JTextArea getTxtDes ()
    {
        if (txtDes == null)
        {
            txtDes = new JTextArea();
        }
        return txtDes;
    }

    public void setEditingObject ( Object UserObject )
    {
    }

    public void updateObject ()
    {
        // save is Set as recovery point
        if (!this.chkAsPoint.isSelected())
        {
            _element.setRecoveryPoint(false);
            return;
        }
        _element.setRecoveryPoint(true);
        // set recovery method
        if (this.rdRedoAct.isSelected())
        {
            _element.setRecoveryMethod(Constants.RECOVERY_POLICY_REDO);
        } else if (this.rdSkipAct.isSelected())
        {
            _element.setRecoveryMethod(Constants.RECOVERY_POLICY_SKIP);
        } else if (this.rdManualProcess.isSelected())
        {
            _element.setRecoveryMethod(Constants.RECOVERY_POLICY_MAN_PROCESS);
        } else
        {
            _element.setRecoveryMethod(Constants.RECOVERY_POLICY_COMPENSATE);
        }
        // save description
        _element.setRecoveryDes(this.txtDes.getText());
    }

    public void actionPerformed ( ActionEvent e )
    {
        updateAllListener();
    }

    public void changedUpdate ( DocumentEvent e )
    {
        updateAllListener();
    }

    public void insertUpdate ( DocumentEvent e )
    {
        updateAllListener();
    }

    public void removeUpdate ( DocumentEvent e )
    {
        updateAllListener();
    }

    /**
     * Notify all listener change hsa happend.
     */
    private void updateAllListener ()
    {
        for (int i = 0; i < _changeList.size(); i++)
        {
            ChangeListener listener = (ChangeListener) _changeList.get(i);
            listener.stateChanged(null);
        }

    }

}
