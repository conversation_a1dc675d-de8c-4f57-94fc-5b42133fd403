package com.ideal.ieai.server.repository.uploadrelyexcel;

import java.util.List;

/**
 * <ul>
 * <li>Title: ActRelyInfoBean.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2014-11-27
 */
public class ActFullInfoBean {

	//String listNum = null;
	String actName = null;
	String actSystem = null;
	String projectName = null;
	String flowName = null;
	List<ActInfoBean> relyActList = null;
	List<ActInfoBean> triggerActList = null;
	
	
	public String getActName() {
		return actName;
	}
	public void setActName(String actName) {
		this.actName = actName;
	}
	public String getActSystem() {
		return actSystem;
	}
	public void setActSystem(String actSystem) {
		this.actSystem = actSystem;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public List<ActInfoBean> getRelyActList() {
		return relyActList;
	}
	public void setRelyActList(List<ActInfoBean> relyActList) {
		this.relyActList = relyActList;
	}
	public List<ActInfoBean> getTriggerActList() {
		return triggerActList;
	}
	public void setTriggerActList(List<ActInfoBean> triggerActList) {
		this.triggerActList = triggerActList;
	}
    public String getFlowName ()
    {
        return flowName;
    }
    public void setFlowName ( String flowName )
    {
        this.flowName = flowName;
    }
}
