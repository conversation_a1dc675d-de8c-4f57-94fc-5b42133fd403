var personExcute_window;
var instanceIdAll;
var pkgnameAll;
  destroyRubbish();
contentPanel.setAutoScroll(false);
  var globalProjectInstanceId = 0;//刷新前的业务系统
  var globalConcurrentStep = 0;
  var globalStepNew = 0;//定位刷新前的步骤
  var globalPakgNew = 0;//定位刷新前的包名
  
  var autoRefreshTime=null;
  var autoRefreshCheck=null;
  var R_TIME = 30;
  var refreshButton_new=null;
  var pauseButton=null;
  var continueButton=null;
  var failureButton=null;
  var manualButton=null;
  var actObjectGlobal=null;
  Ext.onReady(function() {
	  	// 刷新时间输入框
	    autoRefreshTime = Ext.create ('Ext.form.NumberField',
	    {
	    	renderTo : "refresh_bottom",
	        fieldLabel : '刷新时间（秒）',
	        margin : '5',
	        labelWidth : 110,
	        width : 170,
	        value : '30',
	        allowDecimals : false,
	        minValue : R_TIME,
	        listeners :
	        {
	            blur : function ()
	            {
		            refreshTime = this.getValue ();
		            refreshTime = (refreshTime == '' || refreshTime == null) ? R_TIME : refreshTime;
		            try
		            {
			            refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
			            this.setValue (refreshTime);
		            }
		            catch (e)
		            {
			            refreshTime = R_TIME;
			            this.setValue (refreshTime);
		            }
		            if (autoRefreshCheck.checked)
		            {
			            clearInterval (refreshObj);
			            refreshObj = setInterval (refreshProjectList("","全部",1), refreshTime * 1000);
		            }
	            }
	        }
	    });
	    // 是否自动刷新复选框
	    autoRefreshCheck = Ext.create ('Ext.form.Checkbox',
	    {
	        fieldLabel : '自动刷新',
	        margin : '5',
	        labelWidth : 70,
	        width : 90,
	        checked : true,
	        listeners :
	        {
	            change : function ()
	            {
		            if (this.checked)
		            {
			            refreshTime = autoRefreshTime.getValue ();
			            refreshTime = refreshTime == '' ? R_TIME : refreshTime;
			            try
			            {
				            refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
				            autoRefreshTime.setValue (refreshTime);
			            }
			            catch (e)
			            {
				            refreshTime = R_TIME;
				            autoRefreshTime.setValue (refreshTime);
			            }
			            clearInterval (refreshObj);
			            refreshObj = setInterval (refreshProjectList("","全部",1), refreshTime * 1000);
		            }
		            else
		            {
			            clearInterval (refreshObj);
		            }
	            }
	        }
	    });
	    //首次加载定时刷新
	    if (autoRefreshCheck.checked == true)
	    {
	    	clearInterval (refreshObj);
	        refreshObj = setInterval (refreshProjectList("","全部",1), autoRefreshTime.getValue () * 1000);
	    }
	    /** 刷新按钮* */
	    refreshButton_new = Ext.create ("Ext.Button",
		{
			 text : '刷新',
	         cls : 'Common_Btn',
	         listeners: {
	             "click": function() {
	            	 reloadGroup_new();
	             }
	         }
		});
		
		pauseButton = Ext.create ("Ext.Button",
				{
	        text : '暂停',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	                var data = getCHKBoxIds();
	                if (data.length == 0) {
	                  Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                  return;
	                } else {
	                  Ext.Msg.confirm("请确认", "是否真的要进行<暂停>操作？", function(button, text) {
	                    if (button == "yes") {
	                      Ext.Ajax.request({
	                        url : 'rmPause.do',
	                        params : {
	                          insIds : data
	                        },
	                        method : 'POST',
	                        success : function(response, opts) {
	                          var success = Ext.decode(response.responseText).success;
	                          Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          // 当后台数据同步成功时
	                          if (success) {
//	                            reload();
	                        	  reloadGroup_new();
	                          }
	                        }
	                      });
	                    }
	                  });
	                }
	            }
	        }
	      
		});
		continueButton = Ext.create ("Ext.Button",
				{
	        text : '继续',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	                var data = getCHKBoxIds();
	                  if (data.length == 0) {
	                       Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                       return;
	                  } else {
	                    Ext.Msg.confirm("请确认", "是否真的要进行<继续>操作？", function(button, text) {
	                      if (button == "yes") {
	                        Ext.Ajax.request({
	                          url : 'rmResume.do',
	                          params : {
	                            insIds : data
	                          },
	                          method : 'POST',
	                          success : function(response, opts) {
	                            var success = Ext.decode(response.responseText).success;
	                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                            // 当后台数据同步成功时
	                            if (success) {
//	                            	 reload();
	                            	reloadGroup_new();
	                            }
	                          }
	                        });
	                      }
	                    });
	                  }
	            }}});
		
		failureButton = Ext.create ("Ext.Button",
				{
	        text : '部署失败',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	                var data = getCHKBoxIds();
	                  if (data.length == 0) {
	                       Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                       return;
	                  } else {
	                    Ext.Msg.confirm("请确认", "是否真的要进行<部署失败>操作，部署失败后整个部署将会被停止？", function(button, text) {
	                      if (button == "yes") {
	                        Ext.Ajax.request({
	                          url : 'rmKill.do?flag=4',
	                          params : {
	                            insIds : data,
	                            type : 'sus'
	                          },
	                          method : 'POST',
	                          success : function(response, opts) {
	                            var success = Ext.decode(response.responseText).success;
	                            // 当后台数据同步成功时
	                            //Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                            if (success) {
	                            	globalProjectInstanceId=0;
	                            	globalStepNew=0;
	                            	globalPakgNew=0;
	                        		setTimeout(reloadGroup_new, 1000);
	                            }else
	                            {
	                          	  Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          	}
	                          }
	                        });
	                      }
	                    });
	                  }
	            }}});
		manualButton= Ext.create ("Ext.Button",
				{
	        text : '手工部署',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	            	var data = getCHKBoxIds();
	                if (data.length == 0) {
	                    Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                    return;
	                } else {
	                  Ext.Msg.confirm("请确认", "是否真的要进行<手工部署>操作？", function(button, text) {
	                    if (button == "yes") {
	                      Ext.Ajax.request({
	                        url : 'rmKill.do?flag=2',
	                        params : {
	                          insIds : data,
	                          type : 'sus'
	                        },
	                        method : 'POST',
	                        success : function(response, opts) {
	                          var success = Ext.decode(response.responseText).success;
	                          // 当后台数据同步成功时
	                          //Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          if (success) {
	                        	  globalProjectInstanceId=0;
	                        	  globalStepNew=0;
	                        	  globalPakgNew=0;
	                        		setTimeout(reloadGroup_new, 1000);
	                          }
	                          else
	                          {
	                        	  Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          }
	                        }
	                      });
	                    }
	                  });
	                }
	              
	            }}});
});
  //TODO Ext结束  
  
  





//title="名称：<%= project.isysname %>\n变更单号：<%= project.iversion %>\n环境名：<%= project.ienvname %>\n启动用户：<%= project.istartuser %>\n启动时间：<%= project.istarttime %>\n变更说明：<%= project.iruninsname %>\n部署包总数：<%= project.ipkg %>\n当前部署：<%= project.step %>"
  var templates = {
      'projects': ''+
      '<dl class="monitor_sliderbox common_sliderbox" id="monitor_box_slider"> '+
          '<% _.each(projects, function (project) { %>' +
          		'<div class="monitor_box_bottom  <%= project.stateColor%>_border" >'+
	          		'<!--th.1 复选框  -->'+
					'<label><input type="checkbox" name="checkboxForLeft" value="<%= project.iid %>" onclick="testClickBigCheckBox(this,<%= project.iid %>);"/><span></span></label>'+
	            	'<!--th.2 业务系统  -->'+
	            	'<dt class="monitorboxclick" id=monitor_box_sys data-instance-id="<%= project.iid %>"  data-pack-flag="0" contact="名称：<%= project.isysname %><br />变更单号：<%= project.iversion %><br />环境名：<%= project.ienvname %><br />启动用户：<%= project.istartuser %><br />启动时间：<%= project.istarttime %><br />变更说明：<%= project.iruninsname %><br />部署包总数：<%= project.ipkg %><br />当前部署：<%= project.step %>">'+
	                   '<div><%= project.isysname %></div>'+
					   '<div class="mb_status common_cir_status">'+
						   '<span class="<%= project.stateColor%>_circle"></span>'+
						   '<% if (project.stateColor=="red") { %>' +
				              '<span>异常</span>' +
				           '<% } %>' +
				              
				           '<% if (project.stateColor=="gray") { %>' +
				              '<span>未运行</span>' +
				           '<% } %>' +
				              
				           '<% if (project.stateColor=="green") { %>' +
				              '<span>运行中</span>' +
				           '<% } %>' +
				              
				           '<% if (project.stateColor=="blue") { %>' +
				              '<span>运行中</span>' +
				           '<% } %>' +
				              
				           '<% if (project.stateColor=="purple") { %>' +
				              '<span>超时</span>' +
				           '<% } %>' +
				              
				           '<% if (project.stateColor=="orange") { %>' +
				              '<span>人工提醒</span>' +
				           '<% } %>' +
				              
						   '<span><%= project.step %></span>'+
					   '</div>'+
	                '</dt>'+
	                
	                '<!--th.3包  -->'+
	                '<dd>'+
						'<ul>'+
							'<% _.each(project.packetNameItem, function (packetNameItem) { %>' +
							'<li pakg-id="<%= project.iid %>_<%=packetNameItem.pkgname%>" data-parent-id="<%= project.iid %>"  data-pack-flag="1" data-instance-pkgname="<%=packetNameItem.pkgname%>">'+
								'<span class="<%=packetNameItem.stateColor%>_square monitor_square"></span>'+
								'<span><%=packetNameItem.pkgname%></span>'+
								
								'<% if (packetNameItem.stateColor=="red") { %>' +
					              '<span style="float:right"  >异常</span>' +
					              '<% } %>' +
					              
					              '<% if (packetNameItem.stateColor=="gray") { %>' +
					              '<span style="float:right"  >未运行</span>' +
					              '<% } %>' +
					              
					              '<% if (packetNameItem.stateColor=="green") { %>' +
					              '<span style="float:right"  >运行中</span>' +
					              '<% } %>' +
					              
					              '<% if (packetNameItem.stateColor=="blue") { %>' +
					              '<span style="float:right"  >运行中</span>' +
					              '<% } %>' +
					              
					              '<% if (packetNameItem.stateColor=="purple") { %>' +
					              '<span style="float:right"  >超时</span>' +
					              '<% } %>' +
					              
					              '<% if (packetNameItem.stateColor=="orange") { %>' +
					              '<span style="float:right"  >人工提醒</span>' +
					              '<% } %>' +
					              
							'</li>'+
							'<% }); %>' +
						'</ul>'+
	                '</dd>'+
          		'</div>'+   
          '<% }); %>' +
       '</dl>'
          ,
        
	    'pakages': ''+
	    '<ul>' +
	      '<% _.each(pakages, function (pkg) { %>' +
	        '<li class="DR_Leftmenu_btn kl_list_default" data-instance-id="<%= pkg.iruninsid %>" data-instance-pkgname="<%= pkg.ipkgname %>" >' +
	          '<span class="kl_list_text">&nbsp;&nbsp;<%= pkg.ipkgname %></span>' +
	        '</li>' +
	      '<% }); %>' +
	    '</ul>',
        
	    
	    'runInstanceConcurrent': '' +
	    '<% $.each(runInstanceConcurrents, function (index, runInstanceConcurrent) { %>' +
	    	'<div class="monitor_box_bottom <%= runInstanceConcurrent.stateColor %>_border stage_box   concurrent-step" data-step5="<%= cap(runInstanceConcurrent.stateColor ) %>"  data-step4="<%= instanceId %>_<%= runInstanceConcurrent.conner %>" data-step="<%= runInstanceConcurrent.conner %>" data-step2="<%= runInstanceConcurrent.ichildinstanceid %>" data-step3="<%= runInstanceConcurrent.pkgName %>">'+
				'<p><%= runInstanceConcurrent.actNamesSub %></p>'+
				'<div class="mb_status common_cir_status">'+
				
				'<% if (runInstanceConcurrent.stateColor=="red") { %>' +
					'<span class="<%= runInstanceConcurrent.stateColor %>_circle"></span>'+
		            '<span>异常</span>' +
	            '<% } %>' +
	            
	            '<% if (runInstanceConcurrent.stateColor=="gray") { %>' +
		            '<span class="<%= runInstanceConcurrent.stateColor %>_circle"></span>'+
		            '<span>未运行</span>' +
	            '<% } %>' +
	              
	            '<% if (runInstanceConcurrent.stateColor=="green") { %>' +
		            '<span class="<%= runInstanceConcurrent.stateColor %>_circle"></span>'+
		            '<span>已完成</span>' +
	            '<% } %>' +
	              
	            '<% if (runInstanceConcurrent.stateColor=="blue") { %>' +
		            '<span class="<%= runInstanceConcurrent.stateColor %>_circle"></span>'+
		            '<span>运行中</span>' +
	            '<% } %>' +
	            
	            '<% if (runInstanceConcurrent.stateColor=="purple") { %>' +
		            '<span class="<%= runInstanceConcurrent.stateColor %>_circle"></span>'+
		            '<span>超时</span>' +
	            '<% } %>' +
	              
	            '<% if (runInstanceConcurrent.stateColor=="orange") { %>' +
		            '<span class="<%= runInstanceConcurrent.stateColor %>_circle"></span>'+
		            '<span>人工提醒</span>' +
	            '<% } %>' +
	            
				'</div>'+
			'</div>'+
		'<% }); %>' 
  };
  
  function template(templateName, data) {
    var html = templates[templateName];
    return data ? _.template(html, data) : html;
  }
  if($('#ibody')){
	  $('#ibody').remove();
  }
  $('body').append('<div id="ibody"style="display:none;padding:14px;background:#777;color:#fff;box-shadow:1px 1px 4px #000;position:absolute;top:250px;left:340px;"></div>');

  //  setTimeout(function(){
//	  $('#monitor_box_slider').children('.monitor_box_bottom.red_border').mouseenter(function(){
//		  console.log(111)
//		  var contact=$(this).children('dt').attr('contact');
//			$('#ibody').html(contact);
//			if($('#ibody').css('display')=='block'){
//				$('#ibody').css('display','none')
//				
//			}else{
//				$('#ibody').css('display','block');
//				$('#ibody').css('top',$(this).position().top+160+'px');
//				
//			}
//		})
//		
//		$('#monitor_box_slider').children('.monitor_box_bottom.red_border').mouseleave(function(){
//			
//			if($('#ibody').css('display')=='block'){
//		    	$('#ibody').css('display','none')
//		    }
//		})
//  },500)
  
	
  
//	$('body').off('mouseenter', '.monitor_box dt').on('mouseenter', '.monitor_box dt', function(e){
//		  var content= $(this).attr('contact');
////		  var content='<span class="red">名称：wzc_test222\n变更单号：wzc_prename0910</span>';
////		  $(this).css("background","red")
//		  $(this).attr('title',content);
////		  console.log(content)
//	 }
//	);
	
  
  
  //监听(一)
  // 业务系统 中的 dt 被单机
  $('body').off('click', '.monitor_box dt').on('click', '.monitor_box dt', function(e){
	  var $this = $(this);
	  var $allLi = $('.monitor_box dt[data-pack-flag="0"]'); 	// 全部的业务系统  列表
	  onSysOrPkg_Handler(e,$this,$allLi);
   }
  );
  
  
  // “包” 中的 dd 被单机
  $('body').off('click', '.monitor_box li').on('click', '.monitor_box li', function(e){
	  var $this = $(this);
	  var $allLi = $('.monitor_box dd[data-pack-flag="0"]'); 	// 全部的业务系统  列表
	  $('.monitor_box li').removeClass("monitor_menuselected")
      $this.addClass("monitor_menuselected");
	  onSysOrPkg_Handler(e,$this,$allLi);
   }
  );
  
  /**
   * @desc 单击左侧“业务系统”和“包” 事件处理的回调函数
   */
  function onSysOrPkg_Handler(e,$this,$allLi){
     var instanceId = -1;
     
     if($this.data('pack-flag')=='0') {//001.单击"业务系统"组件时
	    instanceId = $this.data('instance-id');
	    var target0 = $('.monitor_box dt[data-pack-flag="1"]');
	    target0.hide();
	    
	    
	    var target = $('.monitor_box dt[data-parent-id="'+instanceId+'"]');
	    target.show();

	    
	    if(globalProjectInstanceId!=instanceId) {
	      globalProjectInstanceId = instanceId;
	      globalConcurrentStep = 0;
	    }
//	    $allLi.removeClass('DR_Leftmenu_over_btn').removeClass('kl_list_over').addClass('DR_Leftmenu_btn').addClass('kl_list_default');
//	    $this.removeClass('DR_Leftmenu_btn').removeClass('kl_list_default').addClass('DR_Leftmenu_over_btn').addClass('kl_list_over');
	//    
	    $('.stage_bar').html("");
	    var fisttarget = $('.monitor_box li[data-parent-id="'+instanceId+'"][pakg-id="'+globalPakgNew+'"]:first');
	    if(fisttarget.length) {
	    	fisttarget.trigger('click');
	    }
	    else
	    	{
	    	fisttarget = $('.monitor_box li[data-parent-id="'+instanceId+'"]:first');
	    	 fisttarget.trigger('click');
	    	}
	    //alert(fisttarget.data('instance-pkgname'));
	   
	    //$('.kl_sys_part li[data-parent-id="'+instanceId+'"]:first-child').trigger('click');
	   // loadPkgs(instanceId);
	  
	} else if($this.data('pack-flag')=='1')//001.单击“包名称”组件时           
	{ 
		instanceId = $this.data('parent-id');
		
		if(globalProjectInstanceId!=instanceId) {
		      globalProjectInstanceId = instanceId;
		      globalConcurrentStep = 0;
		    }
		var pkgname=$this.data('instance-pkgname');
		instanceIdAll = instanceId;
    	pkgnameAll = pkgname;
		loadStep("","全部",1,instanceId,pkgname,6); 	
		globalPakgNew=instanceId+"_"+pkgname;
		$('span[pkgspanid!=""]').css('color',''); 
		$('span[pkgspanid="'+instanceId+pkgname+'"]').css('color','#01bc3f'); 
		return;
	}
    
  }
  
  
  function loadPkgs(instanceId){
	  	
	    $.getJSON('getSUSMonitorInstancePkgnames.do', {instanceId:instanceId}, function(res){
	      if(res.length>0) {
	        $('.packgepackge').show();
	        $('.Disaster_Recovery_Packge').html(template("pakages", {'pakages':res, 'instanceId':instanceId}));
	        $(".Disaster_Recovery_Packge").niceScroll({cursorborder: "1px solid #9fa0a0"});
	        if(globalConcurrentStep!=0){
	          var target = $('.concurrent-step[data-step="'+globalConcurrentStep+'"]');
	          if(target.length) {
	            target.trigger('click');
	            if(target.position().top-60<0){
	            	$(".Disaster_Recovery_Packge").getNiceScroll(0).doScrollTop(-60, 100)
	            }else if((target.position().top-60)>$(".Disaster_Recovery_Packge").height()){
	            	$(".Disaster_Recovery_Packge").getNiceScroll(0).doScrollTop(target.position().top-60, 100);
	            }
	          }
	        }
	      } else {
	        $('.Disaster_Recovery_Packge').html("");
	      }
	    });
	    
}
  
  
  //监听(二) 单击  包 信息
  $('body').off('click', '.Disaster_Recovery_Packge li').on('click', '.Disaster_Recovery_Packge li', function(e){
	  var $this = $(this);
	    var $allLi = $('.Disaster_Recovery_Packge li'); 	// 全部的包 列表
	    var instanceId = $this.data('instance-id');
	    var pkgname = $this.data('instance-pkgname'); 
	    instanceIdAll = instanceId;
    	pkgnameAll = pkgname;
	    loadStep("","全部",1,instanceId,pkgname,type); 				//加载补丁包信息
	  });
  
  /**
	   * @desc 加载步骤 itype=6 标识，清空查询条件 type 1 状态 2 输入框
	   * */
  	 
	  function loadStep(iname,value,type,instanceId,pkgname,itype){
		  if(itype==6){
			  $("#jbusinesstype").val('全部');
			  $("#jinputValue").val('');
		  }
		  if(type==2){
			  value = $("#jbusinesstype").val(); //状态
		  }
		  if(type==1){
			  iname = $("#jinputValue").val(); //输入框
		  }
		  if(instanceId==null ||  instanceId=='' ||  instanceId=='undefined' ||  instanceId==undefined){
			  instanceId = instanceIdAll; //输入框
		  }
		  if(pkgname==null ||  pkgname=='' ||  pkgname=='undefined' ||  pkgname==undefined){
			  pkgname = pkgnameAll; //输入框
		  }
		    //加载步骤
		    $.getJSON('getSUSInstanceConcurrentNew3.do', {instanceId:instanceId,pkgname:pkgname,istateColor:value,iname:iname}, function(res){
		    	for(var i=0;i<res.length;i++){
		    		if(res[i].stateColor=='blue'){
		    			globalConcurrentStep=res[i].conner;
		    		}
		    	}
		    	//kl_sys_stage =  stage_bar
		      if(res.length>0) {
//		        $('.twotwo').show();
		        $('.stage_bar').html(template("runInstanceConcurrent", {'runInstanceConcurrents':res, 'instanceId':instanceId}));
		        $('.stage_bar').mouseenter (function(){
		        	
   				 windowAddMouseWheel($(this).attr('id'));});
		        if(globalStepNew!=0){
			          var target = $('.concurrent-step[data-step4="'+globalStepNew+'"]');
			          if(target.length) {
				            target.trigger('click');
				            if(target.position().top-60<0){
				            	$(".stage_bar").scrollTop(-60, 100)
				            }else if((target.position().top-60)>$(".stage_bar").height()){
				            	$(".stage_bar").scrollTop (target.position().top-60, 100);
				            }else
				            	{
				            	$(".stage_bar").scrollTop (target.position().top-60, 100);
				            	}
				            }else
				            	{
				            	//默认第一个步骤被点击
					        	 $('.concurrent-step:first').trigger('click');
				            	}
		        }else if(globalConcurrentStep!=0){
		          var target = $('.concurrent-step[data-step="'+globalConcurrentStep+'"]');
		          if(target.length) {
		            target.trigger('click');
		            if(target.position().top-60<0){
		            	$(".stage_bar").getNiceScroll(0).doScrollTop(-60, 100)
		            }else if((target.position().top-60)>$(".stage_bar").height()){
		            	$(".stage_bar").getNiceScroll(0).doScrollTop(target.position().top-60, 100);
		            }
		          }
		        }else
		        	{
		        	//默认第一个步骤被点击
		        	 $('.concurrent-step:first').trigger('click');
		        	}
		      } else {
		        $('.stage_bar').html("");
		      }
		    });
	  }

  
	  //监听(三) 步骤被单击了，弹出详情页面
	  $('body').off('click', '.concurrent-step').on('click', '.concurrent-step', function(e){
		     var $this = $(e.currentTarget);
		     var step = $this.attr("data-step");
		     var step2 = $this.attr("data-step2");
		     var step3 = $this.attr("data-step3");
		     var stepTemp=step;
		     var projectInstanceIdTemp=globalProjectInstanceId;
		     globalStepNew=$this.attr("data-step4");
		     if(step2>0)
		    	 {
		    	 stepTemp=1;
		    	 projectInstanceIdTemp=step2;
		    	 }
		   //清除掉步骤上的所有颜色
//		     $('.klstage_click_green').removeClass('klstage_click_green'); 
//		      $('.klstage_click_yellow').removeClass('klstage_click_yellow'); 
//		      $('.klstage_click_blue').removeClass('klstage_click_blue'); 
//		      $('.klstage_click_red').removeClass('klstage_click_red'); 
//		      //选中的步骤上色
//		     var step5 = $this.attr("data-step5");
//		     if(step5=='Green')
//		    	 {
//		    	  $this.addClass('klstage_click_green');
//		    	 }else if(step5=='Yellow')
//		    	 {
//			    	  $this.addClass('klstage_click_yellow');
//			    	 }
//		    	 else if(step5=='Blue')
//		    	 {
//			    	  $this.addClass('klstage_click_blue');
//			    	 }
//		    	 else if(step5=='Red')
//		    	 {
//			    	  $this.addClass('klstage_click_red');
//			    	 }
		   
		   
			    
		     var instanceId = $this.closest(".monitorGraph").attr("data-instance-id");
		     $('#sss').load("initGraphForSUSwindowNew.do",{insId : projectInstanceIdTemp, step : stepTemp,pkgName:step3,flag : true},function(){});  
		     
		     //		     winStep = Ext.create('Ext.window.Window', {
//		 		    title : '步骤详情',
//		 		    modal : true,
//		 		    closeAction : 'destroy',
//		 		    constrain : true,
//		 		    autoScroll : true,
//		 		    width : contentPanel.getWidth (),
//		 		    height : contentPanel.getHeight (),
//		 		    minWidth: 350,
//		 		    draggable : false,// 禁止拖动
//		 		    resizable : false,// 禁止缩放
//		 		    layout : 'fit',
//		 		   listeners:{"beforedestroy":function(obj) 
//		               {   
////		 			  globalProjectInstanceId=0;
//		 			  reload();
//		               }
//		               } ,
//		 		    loader :
//		 		    {
//		 		        url : 'initGraphForSUSwindow.do',
//		 		       params: {
//		 			         insId : projectInstanceIdTemp,
//		 			         step : stepTemp,
//		 			        pkgName:step3
//		 			       },
//		 		        autoLoad : true,
//		 		        scripts : true
//		 		    }
//		 		}).show();
		    

	  });
	  
  	
  
 /**	
    @Div结构
 		kl_sys_part  业务系统     oneone   
		kl_sys_stage   步骤		  twotwo
		Disaster_Recovery_Two	详情		  threethree
  */
  

  /**  
   * TODO 加载数据
   * @desc 
   * 		1.数据展示:
   * 				 先让 业务系统的 中的 'scroll层'的div进行显示。使用的的是 'oneone这个类型选择器'。
   * 		2.数据填充：
   * 				 只对 '业务系统' 块进行展示填充。 填充时，先project进行填充
   * 		3.事件触发:
   *				 业务系统列出低一级列表,每一个业务系统展示一条数据。默认单击第一条数据。 				
   * */
  function refreshProjectList(iname,value,type) {
	  
	  if(type==2){
		  value = $("#businesstype").val(); //状态
	  }
	  if(type==1){
		  iname = $("#inputValue").val(); //输入框
	  }
	  
	  //清空页面元素
	  resetElem();
    $.getJSON('getSUSSysInstanceList3.do?sysType=3&istateColor='+value+'&iname='+iname+'', function(res){
      if(res.length>0) {
//        $('.monitor_box').show();
        $('.monitor_box').html(template("projects", {'projects':res})); //业务系统填充
        
        $('.monitor_box').mouseenter (function(){
        	
				 windowAddMouseWheel($(this).attr('id'));});
        
        //触发单击事件,默认单击第一个业务系统
        if(globalProjectInstanceId==0) {
//          $('.monitor_box dt[data-pack-flag="0"]:first-child').trigger('click');
        	$('.monitorboxclick:first').trigger('click');
        } else {
          var target = $('.monitor_box li[data-instance-id="'+globalProjectInstanceId+'"]');
          if(target.length) {
            target.trigger('click');
          } else {
//            $('.monitor_box li:first-child').trigger('click');
        	  $('.monitorboxclick:first').trigger('click');
          }
        }
        
        //$(".kl_sys_part").niceScroll({cursorborder: "1px solid #9fa0a0"});
        
        monitor_box_slider=new accordion.slider("monitor_box_slider");
		monitor_box_slider.init("monitor_box_slider",0,"open");
		
		if($('#ibody')){
			  $('#ibody').remove();
		  }
		  $('body').append('<div id="ibody"style="display:none;padding:14px;background:#777;color:#fff;box-shadow:1px 1px 4px #000;position:absolute;top:250px;left:340px;"></div>');
		 
			  $('#monitor_box_slider').children('.monitor_box_bottom.red_border').mouseenter(function(){
				  //console.log(222)
				  var contact=$(this).children('dt').attr('contact');
					$('#ibody').html(contact);
					//console.log($('#ibody').css('display'),44444444)
					if($('#ibody').css('display')=='block'){
						$('#ibody').css('display','none')
						
					}else{
						$('#ibody').css('display','block');
						$('#ibody').css('top',$(this).position().top+160+'px');
					}
				})
				
				$('#monitor_box_slider').children('.monitor_box_bottom.red_border').mouseleave(function(){
					 $('#ibody').css('display','none')
				})
				
				$('#monitor_box_slider').children('.monitor_box_bottom.green_border').mouseenter(function(){
				  //console.log(222)
				  var contact=$(this).children('dt').attr('contact');
					$('#ibody').html(contact);
					//console.log($('#ibody').css('display'),44444444)
					if($('#ibody').css('display')=='block'){
						$('#ibody').css('display','none')
						
					}else{
						$('#ibody').css('display','block');
						$('#ibody').css('top',$(this).position().top+160+'px');
					}
				})
				
				$('#monitor_box_slider').children('.monitor_box_bottom.green_border').mouseleave(function(){
					 $('#ibody').css('display','none')
				})
      } else {
    	 
        $('.monitor_box').html("");
        
      }
    });
  }

  refreshProjectList("","全部",1);
  //var timer = setInterval(refreshProjectList,60000);
  
  //当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts) {
	  contentPanel.setAutoScroll(false);
    //clearInterval(timer);
  });
  
  contentPanel.on('resize', function() {
  //  alert(contentPanel.getHeight());
//    
    
  });
  
  function reloadGroup_new(){
	 
	  //清除定时刷新，重新设置
	  clearInterval (refreshObj);
      refreshObj = setInterval (refreshProjectList("","全部",1), autoRefreshTime.getValue () * 1000);
    refreshProjectList("","全部",1);
  }
  
  
  function cap(colorString)
  {
  	if(colorString=='orange')
  		{
  		colorString='yellow';
  		}
  	return colorString.replace(/(\w)/,function(v){return v.toUpperCase()});
  }
  /**
   * 点击包名链接，将该包下的所有步骤都略过
   */
  function skipoverAll (pkgName)
  {
  	Ext.Msg.confirm ("请确认", "是否真的要将该包下的所有步骤进行<略过>操作？", function (button, text)
  	{
  		if (button == "yes")
  		{
  			Ext.Ajax.request (
  			{
  			    url : 'skipStepAll.do',
  			    params :
  			    {
  			        runInsId : globalProjectInstanceId,
  			        pageName : pkgName
  			    },
  			    method : 'POST',
  			    success : function (response, opts)
  			    {
  				    var success = Ext.decode (response.responseText).success;
  				    if (!success)
  				    {
  					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
  				    }
  				  refreshButton_new.fireEvent ('click');
  			    }
  			});
  		}
  	});
  }
  /**
   * 获得左侧树选中的复选框id
   * @returns {String}
   */
  function getCHKBoxIds() {
  	var ids = "";
  	var ck = document.getElementsByName("checkboxForLeft");
  	var num = 0;
  	for (i = 0; i < ck.length; i++) {
  		if (ck[i].checked) {
  			if (num == 0) {
  				ids = ck[i].value;
  			} else {
  				ids = ids + "," + ck[i].value;
  			}
  			num = num + 1;
  		}
  	}
  	return ids;
  }
  /**
   * 页面刷新复选框选中
   * 
   * @param idsIn
   */
  function cHKBoxIdsSetChecked (idsIn)
  {
  	var ck = document.getElementsByName ("checkboxForLeft");
  	for (i = 0; i < ck.length; i++)
  	{
  		if (idsIn.indexOf (ck[i].value) > -1)
  		{
  			ck[i].checked = true;
  		}
  	}
  }
  /**
   * 根据全局map中的数据将滚动条位置重新定位
   */
  function scrollReset (divId)
  {
  	//遍历map
  	for(var prop in scrollMap){
  	    if(scrollMap.hasOwnProperty(prop)){
  	    	//正在运行的包不重新定位滚动条
  	    	if(divId!=prop)
  	    		{
  	    		if($('#' + prop)!=null)
  	    		{
  	    		$('#' + prop).scrollTop(scrollMap[prop]);
  	    		}
  	    		}
  	    	
  	    }
  	}
  }
  /**
   * 获取页面滚动条坐标并保存至全局map
   */
  function getScrollPosition ()
  {
  	scrollMap = {};
  	for (i = 1; i < packageCount+1; i++)
  	{
  		scrollMap['div'+i] = $ ('#div' + i).scrollTop();
  	}  
  	
  }
  //向上滚动
  function scrollUp (divId)
  {
//      var scrollTopNum = document.getElementById (divId).scrollTop;
  	 isFinish = false;
  	var scrollTopNum =$ ('#' + divId).scrollTop();
      if (scrollTopNum > 0)
      {
  	    $ ('#' + divId).animate (
  	    {
  		    scrollTop : scrollTopNum - 200
  	    }, "fast");
      }
      setTimeout(function(){isFinish = true;}, 500);    
  }
  //向下滚动
  function scrollDown (divId)
  {
//      var scrollTopNum = document.getElementById (divId).scrollTop;
  	 isFinish = false;
  	var scrollTopNum =$ ('#' + divId).scrollTop();
      //document.getElementById(divId).scrollTop=scrollTopNum+70;
      $ ('#' + divId).animate (
      {
  	    scrollTop : scrollTopNum + 200
      }, "fast");
      setTimeout(function(){isFinish = true;}, 500);    
  }
  //向右滚动
  function scrollRight (divId)
  {
      
//      var scrollLeftNum = document.getElementById (divId).scrollLeft;
      var scrollLeftNum =$ ('#' + divId).scrollLeft();
      $ ('#' + divId).animate (
      {
  	    scrollLeft : scrollLeftNum + 300
      }, 300);
  }
  //向左滚动
  function scrollLeftF (divId)
  {
      
//      var scrollLeftNum = document.getElementById (divId).scrollLeft;
      var scrollLeftNum =$ ('#' + divId).scrollLeft();
      $ ('#' + divId).animate (
      {
  	    scrollLeft : scrollLeftNum - 300
      }, 300);
  }
  
  var isFinish = true;
  var divIdGlobal = null;
  function windowAddMouseWheel(divId) {
  	divIdGlobal=divId;
      var scrollFunc = function (e) {
    	  if(isFinish){
    		e = e || window.event;
          if (e.wheelDelta) {  //判断浏览器IE，谷歌滑轮事件
              if (e.wheelDelta > 0) { //当滑轮向上滚动时
              	scrollUp(divIdGlobal);
              }
              if (e.wheelDelta < 0) { //当滑轮向下滚动时
              	scrollDown(divIdGlobal);
              }
          } else if (e.detail) {  //Firefox滑轮事件
              if (e.detail> 0) { //当滑轮向上滚动时
              	scrollUp(divIdGlobal);
              }
              if (e.detail< 0) { //当滑轮向下滚动时
              	scrollDown(divIdGlobal);
              }
          }  
    	  }
          
      };
      //给页面绑定滑轮滚动事件
      if (document.addEventListener) {
          document.removeEventListener('DOMMouseScroll', scrollFunc, false);
          document.addEventListener('DOMMouseScroll', scrollFunc, false);
      }
      //滚动滑轮触发scrollFunc方法
      window.onmousewheel = document.onmousewheel = scrollFunc;
  }
  
function reload(){
	refreshProjectList("","全部",1);
  }
//点击步骤详情触发事件
function backFun1(insId,step,state ,stepName,insName,flowId,sysName,stepId,singleRollback,redoable,ip,shellPath,actType)
{
	if(actObjectGlobal==null)
		{
		actObjectGlobal=new Object();
		}
	actObjectGlobal.state=state;
	actObjectGlobal.stepName=stepName;
	actObjectGlobal.insName=insName;
	actObjectGlobal.flowId=flowId;
	actObjectGlobal.sysName=sysName;
	actObjectGlobal.stepId=stepId;
	actObjectGlobal.singleRollback=singleRollback;
	actObjectGlobal.redoable=redoable;
	actObjectGlobal.ip=ip;
	actObjectGlobal.shellPath=shellPath;
	actObjectGlobal.actType=actType;
	actObjectGlobal.insId=insId;
	actObjectGlobal.step=step;
	//alert(stepName);alert(insName);alert(flowId);
    if (state == '异常') {
    	$('#rengongchuliid').html("代理日志输出监控");
    	 $('#sss2').load("initShellinfoerroperSUSNew.do",{state:state ,stepName:stepName,insName:insName,flowId:flowId,sysName:sysName,stepId:stepId,singleRollback:singleRollback,redoable:redoable,ip:ip,shellPath:shellPath},function(){});   
    	//按钮全部置为灰
 		setButtonClass('shougongbushu_tool',1);
 		setButtonClass('chongshi_tool',1);
 		setButtonClass('lueguo_tool',1);
 		setButtonClass('ut_tool',0);
 		//setButtonClass('zhongzhibushu_tool',1);
    }else  if (state == '运行中') 
    	{
    	//按钮全部置为灰
		setButtonClass('shougongbushu_tool',1);
		setButtonClass('chongshi_tool',0);
		setButtonClass('lueguo_tool',0);
		setButtonClass('ut_tool',0);
		//setButtonClass('zhongzhibushu_tool',1);
    	if (actType == 2) { // 人工
			//detailFunc = "openUTWindow(" + flowId + ","+stepId+")";
    		$('#rengongchuliid').html("人工处理");
			$('#sss2').load("switchUT_SUSNew.do",{iflowid : flowId, stepId:stepId},function(){});  
			setButtonClass('ut_tool',1);
		}else
			{
			$('#rengongchuliid').html("代理日志输出监控");
	    	$('#sss2').load("initShellinforuningNew.do",{flowId : flowId, stepName : stepName,insName:insName,ip:ip,shellPath:shellPath},function(){});   
			}
    	
    	} else if (state == '已完成' || state == '异常略过') {
    		//按钮全部置为灰
    		setButtonClass('shougongbushu_tool',0);
    		setButtonClass('chongshi_tool',0);
    		setButtonClass('lueguo_tool',0);
    		setButtonClass('ut_tool',0);
    		//setButtonClass('zhongzhibushu_tool',0);
    		if (actType == 2) { // 人工
    			//detailFunc = "openUTWindow(" + flowId + ","+stepId+")";
        		$('#rengongchuliid').html("人工处理");
    			$('#sss2').load("switchUT_SUSNew.do",{iflowid : flowId, stepId:stepId},function(){});   
    		}else
    			{
    			$('#rengongchuliid').html("代理日志输出监控");
        		$('#sss2').load("initShellinfofinishLinkNew.do",{flowId : flowId, stepName : stepName,insName:insName,stepId:stepId,ip:ip,shellPath:shellPath},function(){});   
    			}
    		
    	}else   if (state == '未开始') 
    		{
    		$('#rengongchuliid').html("无输出");
    		$('#sss2').html("");
    		//按钮全部置为灰
    		setButtonClass('shougongbushu_tool',0);
    		setButtonClass('chongshi_tool',0);
    		setButtonClass('lueguo_tool',1);
    		setButtonClass('ut_tool',0);
    		//setButtonClass('zhongzhibushu_tool',0);
    		}else
    		{
        		$('#rengongchuliid').html("无输出");
        		$('#sss2').html("");
        		actObjectGlobal=null;
        		//按钮全部置为灰
        		setButtonClass('shougongbushu_tool',0);
        		setButtonClass('chongshi_tool',0);
        		setButtonClass('lueguo_tool',0);
        		setButtonClass('ut_tool',0);
        		//setButtonClass('zhongzhibushu_tool',0);
        		}
   
}
//复选框勾选变色
function testClickBigCheckBox(thisobj,prjid){
	if(thisobj.checked)
		{
		$('#checkboxForLeftSpan'+prjid).removeClass('kl_on_check').addClass('kl_on_check');
		}
	else
		{
		$('#checkboxForLeftSpan'+prjid).removeClass('kl_on_check');
		}
}
//设置步骤4个操作的按钮样式
function setButtonClass(thisid,flag)
{
	if(flag==1)
		{
		$('#'+thisid).removeClass('public_btn_gray').addClass('public_btn');
		}else
			{
			$('#'+thisid).removeClass('public_btn').addClass('public_btn_gray');
			}
	
}
//步骤的4个操作
function toolclick(thisid){
	
	var value =$('#'+thisid).attr("class");
	if(value=='public_btn_gray')
		{
		return;
		}else
			{
//			alert(actObjectGlobal.state);
//			alert(actObjectGlobal.stepName);
//			alert(actObjectGlobal.insName);
//			alert(actObjectGlobal.flowId);
//			alert(actObjectGlobal.sysName);
//			alert(actObjectGlobal.stepId);
//			alert(actObjectGlobal.singleRollback);
//			alert(actObjectGlobal.redoable);
//			alert(actObjectGlobal.ip);
//			alert(actObjectGlobal.shellPath);
//			alert(actObjectGlobal.actStepState);
//			return;
			if('chongshi_tool'==thisid)
				{
				//重试
				errorOper_act(1);
				}else if('lueguo_tool'==thisid)
				{
					if (actObjectGlobal.state == '未开始') 
						{
						skipStep(actObjectGlobal.flowId,actObjectGlobal.stepId);
						}else
							{
							//略过
							errorOper_act(2);
							}
					} 
				else if('shougongbushu_tool'==thisid)
				{
					//手工部署
					DeploymentOK_act(actObjectGlobal.flowId,actObjectGlobal.stepId);
					} 
				else if('ut_tool'==thisid)
				{
					//人工提醒处理
					utconfirmFun();
					} 
//				else if('zhongzhibushu_tool'==thisid)
//				{
//					//终止部署
//					DeploymentFail_act(actObjectGlobal.flowId,actObjectGlobal.stepId);
//					} 
			}
	   
}


/* 普通活动执行情况信息 */
function skipStep(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<略过>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'rmSkipStep.do',
				params : {
					flowId :flowId,
					stepId : stepId,
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}

/**步骤重试略过**/
function errorOper_act(execModel) {
	Ext.Ajax.request({
		url : 'exceptionOperSUS.do',
		params : {
			flowId : actObjectGlobal.flowId,
			stepId : actObjectGlobal.stepId,
			execModel : execModel,
			actId : '1',
			actName : 'ShellCmd',
			sts : actObjectGlobal.state,
			actStepState : actObjectGlobal.actStepState,
			systemType:'3'

		},
		success : function(response, request) {
			var msg = Ext.decode(response.responseText).msg;
			if (msg != undefined) {
				Ext.Msg.alert('提示', msg);
				return;
			}
			var success = Ext.decode(response.responseText).success;
			if (success) {
				//errWindow.close();
				//errWindow.destroy();
				Ext.Msg.alert('提示', '操作成功!');
				//reloadGroup();
				reloadGroup_new();
			} else {
				Ext.Msg.alert('提示', '操作失败!');
			}
		},
		failure : function(result, request) {
			secureFilterRs(result,"操作失败！");
		}
	});
}
//手工部署
function DeploymentOK_act(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<手工部署>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'deploymentOKStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						//releaseMonitorStepStore.reload();
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
//部署失败
function DeploymentFail_act(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<部署失败>操作，部署失败后整个部署将会被停止？", function(
			button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'deploymentFailStep.do',
				params : {
					flowId : flowId,
					stepId : stepId,
					insId:insId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						//releaseMonitorStepStore.reload();
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
/** 终止操作* */
function stopStepFun_act(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<终止>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'stopStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						//releaseMonitorStepStore.reload();
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
//页面内容情况
function resetElem()
{
	
	$('#sss').html("");//步骤详情
	$('#rengongchuliid').html("无输出");//输出标题
	$('#sss2').html("");//输出
	 $('.stage_bar').html("");//活动
	//按钮全部置为灰
		setButtonClass('shougongbushu_tool',0);
		setButtonClass('chongshi_tool',0);
		setButtonClass('lueguo_tool',0);
		//setButtonClass('zhongzhibushu_tool',0);
	
}
function showSysPage(){
	var detal_hiswin = Ext.create ('Ext.window.Window',
			{
				title: "业务系统",
			    draggable : true,// 禁止拖动
			    resizable : true,// 禁止缩放
			    modal : true,
			    collapsible : true,
			    closeAction : 'destroy',
			    constrain : true,
			    autoScroll :true,
			    cls:"custom_window_style",
			    width : contentPanel.getWidth(),
			    height : contentPanel.getHeight(),
			    minWidth : 350,
			    layout : 'fit',
			    maximizable :true,
			    closable:true,
			    loader :
			    {
			        url : 'showSysDetailPage.do?',
			        params :
			        {},
			        autoLoad : true,
			        scripts : true
			    }
			    
			    
			});
	detal_hiswin.show();
}