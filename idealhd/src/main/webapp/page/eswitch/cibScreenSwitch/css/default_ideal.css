@charset "utf-8";
/* CSS Document */
body{
	margin:0;
	padding:0;
	}
html,body{
	width:100%;
	height:100%;
	}
.boot_bg{
    /*background-image: linear-gradient(to right top, #2a82e3, #6af2ff);
    background-image: -webkit-gradient(linear, 0 0, 100% 100%, from(#2a82e3),to(#6af2ff));*/
    background-image: -webkit-linear-gradient(top, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
  	background-image: -moz-linear-gradient(top, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
  	background-image: -o-linear-gradient(top, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
  	background-image: linear-gradient(to bottom, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
    width: 100%;
    height: 100%;
}
@keyframes move_wave {
    0% {
        transform: translateX(0) translateZ(0) scaleY(1)
    }
    50% {
        transform: translateX(-25%) translateZ(0) scaleY(0.55)
    }
    100% {
        transform: translateX(-50%) translateZ(0) scaleY(1)
    }
}
@-webkit-keyframes move-wave {
    0% {
        -webkit-transform: translateX(0) translateZ(0) scaleY(1)
    }
    50% {
        -webkit-transform: translateX(-25%) translateZ(0) scaleY(0.55)
    }
    100% {
        -webkit-transform: translateX(-50%) translateZ(0) scaleY(1)
    }
}
.waveWrapper {
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
    z-index: 0;
}
.waveWrapperInner {
    position: absolute;
    width: 100%;
    overflow: hidden;
    height: 100%;
    bottom: -1px;
    z-index: 0;
    top:-30px;
}
.bgTop {
    z-index: 15;
    opacity: 0.5;
}
.bgMiddle {
    z-index: 10;
    opacity: 0.75;
}
.bgBottom {
    z-index: 5;
}
.wave {
    position: absolute;
    left: 0;
    width: 200%;
    height: 100%;
    background-repeat: repeat no-repeat;
    background-position: 0 bottom;
    transform-origin: center bottom;
    -webkit-transform-origin:center bottom;
}
.waveTop {
    background-size: 50% 180px;
    background-image: url("../images/wave-top.png");

}
.waveAnimation .waveTop {
  animation: move-wave 3s;
  animation-delay: 1s;
   -webkit-animation: move-wave 3s;
   -webkit-animation-delay: 1s;

}
.waveMiddle {
    background-size: 50% 200px;
    background-image: url("../images/wave-mid.png");
}
.waveAnimation .waveMiddle {
    animation:move-wave 10s infinite;
    animation-timing-function:linear;
    -webkit-animation:move-wave 10s infinite;
    -webkitanimation-timing-function:linear;
}
.waveBottom {
    background-size: 50% 180px;
    background-image: url("../images/wave-bot.png");
}
.waveAnimation .waveBottom {
    animation:move-wave 15s infinite;
    animation-timing-function:linear;
    -webkit-animation:move-wave 15s infinite;
    -webkitanimation-timing-function:linear;
}
.img_container{
    text-align: center;
    width: 100%;
}
.img_size{
    background-image: url("../images/boot_img-1000.png");
    margin: 0 auto;
    width:500px;
   	height:500px;
   	padding:163px 20px 0 0;
}
.img_size1{
	background-image: url("../images/boot_img-1000.png");
    width:500px;
    height:500px;
    padding:150px 20px 0 0;
}
.img_button{
    position: relative;
    z-index: 1000;
    width: 200px;
    height: 200px;
    line-height:200px;
    cursor: pointer;
    border-radius: 100%;
    background-color:#013d99;
    background-image: -webkit-linear-gradient(top, #013d99 0%, #013d99 40%, #013d99 100%);
    background-image: -moz-linear-gradient(top, #013d99 0%, #013d99 40%, #013d99 100%);
    background-image: -o-linear-gradient(top, #013d99 0%, #013d99 40%, #013d99 100%);
    background-image: linear-gradient(to bottom, #013d99 0%,  #013d99 40%, #013d99 100%);
    box-shadow:0 0 40px #448fd4 inset,0 0 40px #00fffe;
    -webkit-box-shadow:0 0 40px #448fd4 inset,0 0 40px #00fffe;
    -moz-box-shadow:0 0 40px #448fd4 inset,0 0 40px #00fffe;
    -o-box-shadow:0 0 40px #448fd4 inset,0 0 40px #00fffe;
    border:1px solid #029fbb;
    color: #ffffff;
    font-size: 50px;
    text-align: center;
    font-family: "Microsoft Yahei";
    margin:0 0 0 5px;
}
.img_button:hover{
	background-color:#013d99;
    background-image: -webkit-linear-gradient(top, #013d99 0%, #013d99 40%, #013d99 100%);
    background-image: -moz-linear-gradient(top, #013d99 0%, #013d99 40%, #013d99 100%);
    background-image: -o-linear-gradient(top, #013d99 0%, #013d99 40%, #013d99 100%);
    background-image: linear-gradient(to bottom, #013d99 0%,  #013d99 40%, #013d99 100%);
    box-shadow:0 0 40px #76bdfe inset,0 0 40px #00fffe;
    -webkit-box-shadow:0 0 40px #76bdfe inset,0 0 40px #00fffe;
    -moz-box-shadow:0 0 40px #76bdfe inset,0 0 40px #00fffe;
    -o-box-shadow:0 0 40px #76bdfe inset,0 0 40px #00fffe;
}

.img_button:active{
	background-color:#1283f9;
    background-image: -webkit-linear-gradient(top, #9be7fd 0%, #33cefa 40%, #1283f9 100%);
    background-image: -moz-linear-gradient(top, #9be7fd 0%, #33cefa 40%, #1283f9 100%);
    background-image: -o-linear-gradient(top, #9be7fd 0%, #33cefa 40%, #1283f9 100%);
    background-image: linear-gradient(to bottom, #9be7fd 0%,  #33cefa 40%, #1283f9 100%);
}
.img_button:visited{
	background-color:#1283f9;
    background-image: -webkit-linear-gradient(top, #9be7fd 0%, #33cefa 40%, #1283f9 100%);
    background-image: -moz-linear-gradient(top, #9be7fd 0%, #33cefa 40%, #1283f9 100%);
    background-image: -o-linear-gradient(top, #9be7fd 0%, #33cefa 40%, #1283f9 100%);
    background-image: linear-gradient(to bottom, #9be7fd 0%,  #33cefa 40%, #1283f9 100%);
}

.yincang{
	display: none;
}

.bottom{
    width: 100%;
    text-align: center;
    position: relative;
    z-index: 1000;
    font-family: "Microsoft Yahei";
    font-size:14px;
    color: #333333;
}
.bottom1{
    width: 100%;
    text-align: center;
    position: relative;
    z-index: 1000;
    font-family: "Microsoft Yahei";
    font-size:20px;
    color: #ff2d00;
    animation: animation_f 1s ease-in infinite;
	-webkit-animation: animation_f 1s ease-in infinite;
	-moz-animation: animation_f 1s ease-in infinite;
}
@keyframes animation_f {
       0%{text-shadow: 0 0 4px #1d2088}
       50%{text-shadow: 0 0 50px #1d2088}
       100%{text-shadow: 0 0 4px #1d2088}
     }
@-webkit-keyframes animation_f {
       0%{text-shadow: 0 0 4px #1d2088}
       50%{text-shadow: 0 0 50px #1d2088}
       100%{text-shadow: 0 0 4px #1d2088}
     }
.ipadLogo{ 
	 background-image: url("../images/ipadLogo_ideal_xy.png");
	 width:145px;
	 height:62px;
}
.ipad_title{
	font-size:30px;
	color:#fff;
}
.ipad_forward{
	position:absolute;
	width:100%;
	text-align:right;
	color:#fff;
	padding:0 10px 0 0;
	font-size:16px;
}
/*.window_pos{
    width: 100%;
    margin: auto;
    position:absolute;
    top: 31%;
}
.window_size{
    width: 550px;
    height: 300px;
    background-color: #ffffff;
    border-radius: 4px;
    margin: auto;
}
.window_header{
    background-color: #14b1f6;
    height: 72px;
    border-radius: 2px 2px 0 0;
    color:#ffffff;
    font-size: 24px;
}*/