package com.ideal.ieai.core.basis;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.Arrays;
import java.util.Collection;
import java.util.Enumeration;


/**
 * <p>Title: IEAI Core</p>
 * <p>Description: A base class that can load jar or zip files dynamically from specified
 * directories. Derived class need to specifiy where to get those files from.
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 3.0
 */

public class DynamicClassLoader
    extends URLClassLoader
{

    public DynamicClassLoader(String LibDir, ClassLoader ParentCL)
    {
        this(LibDir, false, ParentCL);
    }

    public DynamicClassLoader(String LibDir, boolean AllowReload, ClassLoader ParentCL)
    {
        this(new String[]
             {LibDir}
             , AllowReload, ParentCL);
    }

    public DynamicClassLoader(String[] LibDirs, ClassLoader ParentCL)
    {
        this(LibDirs, true, ParentCL);
    }

    public DynamicClassLoader(String[] LibDirs, boolean AllowReload, ClassLoader ParentCL)
    {
        super(new URL[0], ParentCL);
        _libDirs = LibDirs;
        _allowReload = AllowReload;
        load();
    }

    public void addLibPath(final String LibPath)
    {
        String[] newLibs = new String[_libDirs.length + 1];
        System.arraycopy(_libDirs, 0, newLibs, 0, _libDirs.length);
        newLibs[_libDirs.length] = LibPath;
        _libDirs = newLibs;
    }

    /**
     * Through class name find the cprrespond Class object.
     * @param ClassName a class name.
     * @return
     * @throws java.lang.ClassNotFoundException
     */
    public Class loadClass(String ClassName) throws ClassNotFoundException
    {
        try
        {
            return super.loadClass(ClassName);
        }
        catch (ClassNotFoundException e)
        {
            if (_allowReload)
            {
                if (load())
                {
                    return super.loadClass(ClassName);
                }
            }
            throw e;
        }
        catch (NoClassDefFoundError er)
        {
            throw new ClassNotFoundException("", er);
        }
    }

    public URL findResource(final String Name)
    {
        URL ret = super.findResource(Name);
        if (null != ret)
        {
            return ret;
        }

        if (_allowReload)
        {
            if (load())
            {
                return super.findResource(Name);
            }
        }
        return null;
    }

    /**
     * Returns an Enumeration of URLs representing all of the resources
     * on the URL search path having the specified name.
     *
     * @param name the resource name
     * @exception IOException if an I/O exception occurs
     * @return an <code>Enumeration</code> of <code>URL</code>s
     */
    public Enumeration findResources(final String Name) throws IOException
    {
        Enumeration ret = null;
        try
        {
            ret = super.findResources(Name);
        }
        catch (IOException ex)
        {
        }

        if (null != ret && ret.hasMoreElements())
        {
            return ret;
        }

        if (_allowReload)
        {
            if (load())
            {
                return super.findResources(Name);
            }
        }
        return null;

    }

    static private FilenameFilter _filter = new FilenameFilter()
    {
        public boolean accept(File place, String pattern)
        {
            if (pattern.toLowerCase().endsWith(".jar") ||
                pattern.toLowerCase().endsWith(".zip"))
            {
                return true;
            }
            return false;
        }
    };

    /**
     *
     * @return boolean true: if caller can retry to load class. otherwise, it means
     * no new jar or class has been added, so no need to retry class loading here.
     */
    synchronized private boolean load()
    {
        try
        {
            URL[] urls = getURLs(); //get all existing URLs
            Collection urlList = Arrays.asList(urls);
            boolean foundNew = false;
            for (int iLibDir = 0; iLibDir < _libDirs.length; ++iLibDir)
            {
                File libDir = new File(_libDirs[iLibDir]);
                if (libDir.isFile())
                {
                    //it is a single, file, simply add it into URL list
                    if (!urlList.contains(libDir.toURL()))
                    {
                        addURL(libDir.toURL());
                        foundNew = true; //somehting new has been added
                    }
                    continue;
                }

                if (!libDir.isDirectory())
                {
                    continue; //skip if not a dir
                }

                //add it into URL list first, so all class files will be added
                if (!urlList.contains(libDir.toURL()))
                {
                    addURL(libDir.toURL());
                    foundNew = true; //somehting new has been added
                }
                //now get and check all jar/zip files in the dir
                File[] files = libDir.listFiles(_filter);
                if (null == files)
                {
                    continue;
                }

                for (int i = 0; i < files.length; i++)
                {
                    URL url = files[i].toURL();
                    if (!urlList.contains(url))
                    {
                        addURL(url);
                        foundNew = true; //somehting new has been added
                    }
                }
            } //end of for libDirs
            return foundNew;
        }
        catch (Exception e)
        {
            e.printStackTrace();
            return false;
        }
    }

    public void setAllowReload(boolean AllowReload)
    {
        this._allowReload = AllowReload;
    }

    public boolean isAllowReload()
    {
        return _allowReload;
    }

    public String[] getLibDirs()
    {
        return _libDirs;
    }

    public void setLibDirs(String[] LibDirs)
    {
        this._libDirs = LibDirs;
    }

    public String toString()
    {
        StringBuffer buf = new StringBuffer("DynamicClassloder:\n");
        for (int i = 0; i < _libDirs.length; i++) {
            buf.append("\t").append(_libDirs[i]).append("\n");
        }
        return buf.toString();
    }


    /**
     * The flag is a class can be reloaded.
     */

    private boolean _allowReload = true;

    /**
     * The lib dir, lib file or classes dir that classLoader will search.
     * If item is a dir, all jar and zip files under will be loaded, all class files
     * will be loaded too.
     * If item is a jar file or a zip file, only the file will be loaded.
     *
     */
    private String[] _libDirs = null;

}
