package com.ideal.ieai.server.repository.routinetasks.importexecl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.TransactionStorerJDBC;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBSourceMonitor;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.importexecl.InfoExeclUtilBean;
import com.ideal.ieai.server.repository.importexecl.SUSExcelBaisc;
import com.ideal.ieai.server.repository.importexecl.SUSExcelBaiscPro;
import com.ideal.ieai.server.repository.importexecl.SUSExcelContainer;
import com.ideal.ieai.server.repository.importexecl.SusExeclInfo;
import com.ideal.ieai.server.repository.importexecl.SusSysInfo;
import com.ideal.ieai.server.repository.importexecl.TmpltMagmntException;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.repository.project.RepProject;
import com.ideal.ieai.server.repository.role.RoleManager;
import com.ideal.ieai.server.repository.rolePermission.RolePermissionManager;
import com.ideal.util.UUID;

public class InfoExeclServicesMultipleForRoutineTasks
{
    private static final Logger                    _log           = Logger.getLogger(InfoExeclServicesMultipleForRoutineTasks.class);
    private static InfoExeclServicesMultipleForRoutineTasks _intance       = new InfoExeclServicesMultipleForRoutineTasks();
    private static final Logger                    dbsRollbacklog = Logger.getLogger("dbSRblog");

    public static InfoExeclServicesMultipleForRoutineTasks getInstance ()
    {
        if (_intance == null)
        {
            _intance = new InfoExeclServicesMultipleForRoutineTasks();
        }
        return _intance;
    }
    
    /**
     * 
     * @Title: saveAllSheetMultipleForSUS
     * @Description: TODO(EXCEL上传工程多写保存方法)
     * @param container
     * @param importUser
     * @param oldVersion
     * @param conn
     * @return
     * @throws RepositoryException
     * @throws TmpltMagmntException
     * @throws ServerException
     * @return Map<String,String> 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    public Map<String, String> saveAllSheetMultipleForSUS ( SUSExcelContainer container,
            UserInfo importUser, long oldVersion) throws RepositoryException,
            TmpltMagmntException, ServerException
    {
        Map<String, String> mapMsg = new HashMap<String, String>();
        InfoExeclUtilBean infoExeclUtilBean = new InfoExeclUtilBean();
        try
        {
            // 获取所有数据源并封装至InfoExeclUtilBean
            this.buildDBConnection(infoExeclUtilBean);
            // 组织数据
            mapMsg = this.constructData(container, importUser, oldVersion,
                infoExeclUtilBean.getBasicConnection(), infoExeclUtilBean);
            // 保存操作
            this.applyDatabase(infoExeclUtilBean);
        } catch (RepositoryException e)
        {
            _log.error(e);
            throw e;
        } catch (TmpltMagmntException e)
        {
            _log.error(e);
            throw e;
        } catch (ServerException e)
        {
            _log.error(e);
            throw e;
        } catch (Exception e)
        {
            _log.error(e);
            throw e;
        } finally
        {
            // 循环关闭所有数据库连接
            for (Iterator it = infoExeclUtilBean.getConnectionMap().keySet().iterator(); it
                    .hasNext();)
            {
                Object key = it.next();
                try
                {
                    if (infoExeclUtilBean.getConnectionMap().get(key) != null
                            || !infoExeclUtilBean.getConnectionMap().get(key).isClosed())
                    {
                        infoExeclUtilBean.getConnectionMap().get(key).close();
                    }

                } catch (Exception e)
                {
                    // TODO Auto-generated catch block
                    dbsRollbacklog.error("数据源:" + key + "关闭时发生错误!");
                }
            }
        }
        return mapMsg;

    }
    
    /**
     * 
     * @Title: buildDBConnection
     * @Description: TODO(获取所有数据源并封装至InfoExeclUtilBean)
     * @param infoExeclUtilBean
     * @throws RepositoryException
     * @return void 返回类型
     * @throws TmpltMagmntException
     * @throws
     * @变更记录 2016年6月3日 yunpeng_zhang
     */
    public void buildDBConnection ( InfoExeclUtilBean infoExeclUtilBean )
            throws RepositoryException, TmpltMagmntException
    {
        // 获取所有可用数据源
        List<DBSourceMonitor> dbList;
        boolean hasBasic = false;
        try
        {
            dbList = ProjectManagerForMultiple.getInstance().getDBsourceList(
                Constants.IEAI_IEAI_BASIC);
            for (DBSourceMonitor dBSourceMonitor : dbList)
            {
                Connection connection = DBResource.getConnection("buildDBConnection", _log,
                    (int) dBSourceMonitor.getGroupId());
                if (dBSourceMonitor.getBasic() == 1)
                {
                    hasBasic = true;
                    // 保存基线库数据库连接至工具类对象
                    infoExeclUtilBean.setBasicConnection(connection);
                    infoExeclUtilBean.setBasicConnectionName(dBSourceMonitor.getDbsourceName());
                }
                // 保存数据库连接集合至工具类对象
                infoExeclUtilBean.getConnectionMap().put(dBSourceMonitor.getDbsourceName(),
                    connection);
            }
        } catch (RepositoryException e1)
        {
            // TODO Auto-generated catch block
            throw e1;
        }
        if (hasBasic == false)
        {
            throw new TmpltMagmntException(false, "无基线数据库，不进行保存操作！");
        }

    }
    
    
    /**
     * 
     * @Title: constructData
     * @Description: TODO(组织多写数据方法)
     * @param container
     * @param importUser
     * @param oldVersion
     * @param conn
     * @param infoExeclUtilBean
     * @return
     * @throws RepositoryException
     * @throws TmpltMagmntException
     * @throws ServerException
     * @return Map<String,String> 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    private Map<String, String> constructData ( SUSExcelContainer container, UserInfo importUser,
            long oldVersion, Connection conn, InfoExeclUtilBean infoExeclUtilBean )
            throws RepositoryException, TmpltMagmntException, ServerException
    {
        Map<String, String> mapMsg = new HashMap<String, String>();
        if (null != container.getExeclInfo())
        {
            // 变更单号，不在从"系统录入"页面获得，而是从"基础信息sheet"中获取
            List<SUSExcelBaiscPro> baiscPro = container.getSusExcelBaisc().getParamsListPro();
            String iversionalias = "";
            String iversiondesc = "";
            String iversionStartime = "";
            for (SUSExcelBaiscPro baiscProO : baiscPro)
            {
                if ("预案名称".equals(baiscProO.getProName()))
                {
                    iversionalias = baiscProO.getProValue();
                }
                if ("任务说明".equals(baiscProO.getProName()))
                {
                    iversiondesc = baiscProO.getProValue();
                }
//                if ("启动时间".equals(baiscProO.getProName()))
//                {
//                    iversionStartime = baiscProO.getProValue();
//                }
            }
            Object[] oo = container.getExeclInfo();
            SusSysInfo susSysInfo = null;
//            int countStartime = 0;
//            int countPactName = 0;
            try
            {
                susSysInfo = (SusSysInfo) oo[0];
//                countStartime = InfoExeclServices.getInstance().validateSysStarttime(conn,
//                    susSysInfo.getInsName(), iversionStartime);
//                List<SUSExcelBaiscPacname> baiscPacnameList = container.getSusExcelBaisc()
//                        .getParamsListPacname();
//                for (SUSExcelBaiscPacname baiscPacname : baiscPacnameList)
//                {
////                    countPactName = InfoExeclServices.getInstance().validateSysPackName(conn,
////                        susSysInfo.getInsName(), baiscPacname.getPacketName());
//                }
            } catch (ClassCastException e)
            {
                throw new TmpltMagmntException(false, "系统信息为空");
            } catch (NullPointerException e)
            {
                throw new TmpltMagmntException(false, "系统信息为空");
            } /*catch (SQLException e)
            {
                throw new TmpltMagmntException(false, "validateSysStarttime query is error");
            }*/
//            if (countStartime > 0)
//            {
//                throw new TmpltMagmntException(false, "同系统名下启动时间不能相同");
//            }
//            if (countPactName > 0)
//            {
//                throw new TmpltMagmntException(false, "同系统名下包名不能相同");
//            }
            susSysInfo.setIversionalias(iversionalias);
            // 构建数据
            this.buildProjectData(conn, importUser, susSysInfo.getInsName(), Constants.IEAI_ROUTINE_TASKS,
                infoExeclUtilBean);
            this.buildVersion(container.getExeclInfo(), importUser, oldVersion, conn,
                infoExeclUtilBean);
            this.buildInstanceInfoData((List) oo[1], infoExeclUtilBean.getInsertVersion().getIid(),
                infoExeclUtilBean.getInsertVersion().getInsName(), conn, infoExeclUtilBean);
            this.buildExcelBaisc(container.getSusExcelBaisc(),
                infoExeclUtilBean.getInsertVersion(), conn, infoExeclUtilBean);
//            this.buildHostsOfEnv(container.getHostList(), infoExeclUtilBean.getInsertVersion(),
//                conn, infoExeclUtilBean);
            boolean isshowpanel=Environment.getInstance().getBooleanConfigNew("em.show.parampanel.switch", false);
            if(isshowpanel){
                 List params = (List)oo[2];
                if(params.size()!=0){
                    this.buildDynamicParams(infoExeclUtilBean,params);
                }
            }
            this.buildIsextend(conn,importUser,infoExeclUtilBean);
            mapMsg.put("ieai_sus_inst_version_id",
                String.valueOf(infoExeclUtilBean.getInsertVersion().getIid()));
            mapMsg.put("预案名称", iversionalias);
            mapMsg.put("急应说明", iversiondesc);
            return mapMsg;
        } else
        {
            throw new TmpltMagmntException(false, "系统信息为空");
        }

    }
    
    private void buildDynamicParams ( InfoExeclUtilBean infoExeclUtilBean,List params )
    {
        if(params.size()!= 0){
            infoExeclUtilBean.setParamsSet(params);
        }
        
    }
    /**
     * 
     * @Title: applyDatabase
     * @Description: TODO(Excel上传多写操作)
     * @param infoExeclUtilBean
     * @return
     * @return boolean 返回类型
     * @throws TmpltMagmntException
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    public void applyDatabase ( InfoExeclUtilBean infoExeclUtilBean ) throws TmpltMagmntException
    {
        boolean isBasicCon = true;//是否在操作基线库
        boolean hasError = false;// 是否发生异常
        // 循环执行保存操作
        for (Iterator it = infoExeclUtilBean.getConnectionMap().keySet().iterator(); it.hasNext();)
        {
            Object key = it.next();
            try
            {
//                this.saveDataForMultiple(infoExeclUtilBean.getConnectionMap().get(key),
//                    infoExeclUtilBean);
                this.saveDataForMultiple(infoExeclUtilBean.getConnectionMap().get(key),
                    infoExeclUtilBean,isBasicCon,key.toString());
                isBasicCon=false;
            } catch (Exception e)
            {
                _log.error(" applyDatabase is error:",e);
                // TODO Auto-generated catch block
                hasError = true;
                dbsRollbacklog.error("数据源:" + key + "数据库执行发生错误!工程名:"
                        + infoExeclUtilBean.getInsertProject().getName() + "工程id:"
                        + infoExeclUtilBean.getInsertProject().getId());
                // 回滚所有connection
                for (Iterator it2 = infoExeclUtilBean.getConnectionMap().keySet().iterator(); it2
                        .hasNext();)
                {
                    Object key2 = it2.next();
                    try
                    {
                        infoExeclUtilBean.getConnectionMap().get(key2).rollback();

                    } catch (Exception e2)
                    {
                        dbsRollbacklog.error("数据源:" + key2 + "数据库单独回滚发生错误!");
                    }
                }
                break;
            }
        }
        // 如果之前未发生过异常，则进入提交操作
        if (hasError == false)
        {
            // 其他库循环执行提交或者回滚操作
            for (Iterator it = infoExeclUtilBean.getConnectionMap().keySet().iterator(); it
                    .hasNext();)
            {
                Object key = it.next();
                try
                {
                    // 如果之前未发生异常，当前connect执行commit操作
                    if (hasError == false)
                    {

                        infoExeclUtilBean.getConnectionMap().get(key).commit();
                        // 将执行commit操作成功的connect记录
                        infoExeclUtilBean.getComitConnectionMap().put(key.toString(),
                            infoExeclUtilBean.getConnectionMap().get(key));
                    } else
                    {
                        // 如果之前发生过异常，当前connect执行rollback操作
                        infoExeclUtilBean.getConnectionMap().get(key).rollback();
                    }

                } catch (Exception e2)
                {
                    hasError = true;
                    // TODO Auto-generated catch block
                    try
                    {
                        infoExeclUtilBean.getConnectionMap().get(key).rollback();
                    } catch (Exception e)
                    {
                        // TODO Auto-generated catch block
                        dbsRollbacklog.error("数据源:" + key + "数据库单独回滚发生错误!");
                    }
                }

            }

        }
        if (hasError == true && infoExeclUtilBean.getComitConnectionMap().size() > 0)
        {
            // 手动回滚已经comit的connect
            for (Iterator it = infoExeclUtilBean.getComitConnectionMap().keySet().iterator(); it
                    .hasNext();)
            {
                Object key = it.next();
                try
                {
                    this.rollbackDataForMultiple(
                        infoExeclUtilBean.getComitConnectionMap().get(key), infoExeclUtilBean);
                } catch (Exception e)
                {
                    dbsRollbacklog.error("数据源:" + key + "数据回写至原值发生错误!工程名:"
                            + infoExeclUtilBean.getInsertProject().getName() + "工程id:"
                            + infoExeclUtilBean.getInsertProject().getId());
                }
            }
        }
        if (hasError == true)
        {
            throw new TmpltMagmntException(false, "Excel上传多写失败!");
        }

    }
    
    
    /**
     * 
     * @Title: buildProjectData
     * @Description: TODO(构建保存ieai_project表数据)
     * @param conn
     * @param user
     * @param sysName
     * @param groupId
     * @param infoExeclUtilBean
     * @return
     * @throws RepositoryException
     * @return RepProject 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    public void buildProjectData ( Connection conn, UserInfo user, String sysName, long groupId,
            InfoExeclUtilBean infoExeclUtilBean ) throws RepositoryException
    {

        RepProject prj = new RepProject();
        prj.setName(sysName);
        prj.setUploadUser(user);
        prj.setMajorVersion(new Integer(0));
        prj.setMinorVersion(new Integer(0));
        prj.setFreezed(false);
        prj.setComment("");
        prj.setUploadNum(1);
        prj.setUuid(UUID.uuid());
        // prj.setUploadTime(new Timestamp(System.currentTimeMillis()));
        prj.setPkgContentId(new Long(0));
        prj.setGroupId(groupId);
        prj.setId(new Long(IdGenerator.createIdForExecAct(RepProject.class, conn)));
        prj.setProType(groupId);
        // prj.setPrjDesc(PrjDesc);
        // 查询IEAI_PROJECT表中同工程名数据的IUPPERID
        Long iupperId = TransactionStorerJDBC.getInstance().queryIupperId(conn, prj.getName(),
            prj.getGroupId());
        if (iupperId == null || iupperId.longValue() == 0)
        {
            iupperId = prj.getId();
        }
        prj.setIupperId(iupperId);
        // 工程原始id
        infoExeclUtilBean.setIupperId(prj.getIupperId());
        // 工程id
        infoExeclUtilBean.setPrjId(prj.getId());
        // 更新数据的组id
        infoExeclUtilBean.setGroupId(prj.getGroupId());
        // 工程原初始工程id
        infoExeclUtilBean.setLatestId(ProjectManagerForMultiple.getInstance().queryIlatestId(
            infoExeclUtilBean.getBasicConnection(), prj.getName(), prj.getGroupId()));
        infoExeclUtilBean.setInsertProject(prj);

    }
    
    
    /**
     * 
     * @Title: buildVersion
     * @Description: TODO(构建保存IEAI_INSTANCE_VERSION表数据)
     * @param oo
     * @param user
     * @param oldVersion
     * @param conn
     * @param infoExeclUtilBean
     * @throws RepositoryException
     * @return void 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    public void buildVersion ( Object[] oo, UserInfo user, long oldVersion, Connection conn,
            InfoExeclUtilBean infoExeclUtilBean ) throws RepositoryException
    {
        long proid = 0;
        try
        {
            // 构建保存IEAI_INSTANCE_VERSION版本表
            SusSysInfo susSysInfo = (SusSysInfo) oo[0];
            susSysInfo.setIieaisysid(proid);
            susSysInfo.setIprojectid(infoExeclUtilBean.getInsertProject().getId());
            susSysInfo.setIupperId(infoExeclUtilBean.getInsertProject().getIupperId());
            susSysInfo.setIid(IdGenerator.createId("IEAI_INSTANCE_VERSION", conn));
            susSysInfo.setOldVersion(oldVersion);
            infoExeclUtilBean.setInsertVersion(susSysInfo);
        } catch (Exception ee)
        {
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        }
    }
    
    /**
     * 
     * @Title: buildInstanceInfoData
     * @Description: TODO(构建保存IEAI_INSTANCEINFO表数据)
     * @param list
     * @param insID
     * @param sysName
     * @param con
     * @param infoExeclUtilBean
     * @throws RepositoryException
     * @return void 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    public void buildInstanceInfoData ( List list, long insID, String sysName, Connection con,
            InfoExeclUtilBean infoExeclUtilBean ) throws RepositoryException
    {

        for (int i = 0; i < list.size(); i++)
        {
            SusExeclInfo susExeclInfo = (SusExeclInfo) list.get(i);
            susExeclInfo.setIid(IdGenerator.createId("IEAI_INSTANCEINFO", con));
            susExeclInfo.setIinstanceid(insID);
            susExeclInfo.setIinstanceName(sysName);
            infoExeclUtilBean.getInsertInstanceInfoList().add(susExeclInfo);

        }
    }
    
    
    /**
     * 
     * @Title: saveExcelBaisc
     * @Description: TODO(构建保存IEAI_SUS_BASIC_INFO表数据和保存IEAI_SUS_BASIC_PACNAME_INFO表数据)
     * @param susExcelBaisc
     * @param ieai_sus_inst_version_id
     * @param susSysInfo
     * @param con
     * @param infoExeclUtilBean
     * @throws RepositoryException
     * @return void 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    private void buildExcelBaisc ( SUSExcelBaisc susExcelBaisc, SusSysInfo susSysInfo,
            Connection con, InfoExeclUtilBean infoExeclUtilBean ) throws RepositoryException
    {
        if (null != susExcelBaisc)
        {
            // 构建保存属性信息
            List<SUSExcelBaiscPro> baiscProList = susExcelBaisc.getParamsListPro();
            for (SUSExcelBaiscPro baiscPro : baiscProList)
            {
                baiscPro.setIid(IdGenerator.createId("IEAI_SUS_BASIC_INFO", con));
                baiscPro.setInstanceId(susSysInfo.getIid());
                baiscPro.setInstanceName(susSysInfo.getInsName());
                infoExeclUtilBean.getInsertBasicInfoList().add(baiscPro);

            }
//            // 构建保存程序包信息
//            List<SUSExcelBaiscPacname> baiscPacnameList = susExcelBaisc.getParamsListPacname();
//            for (SUSExcelBaiscPacname baiscPacname : baiscPacnameList)
//            {
//                baiscPacname
//                        .setIpacNameId(IdGenerator.createId("IEAI_SUS_BASIC_PACNAME_INFO", con));
//                baiscPacname.setInstanceId(susSysInfo.getIid());
//                baiscPacname.setInstanceName(susSysInfo.getInsName());
//                infoExeclUtilBean.getInsertBaiscPacnameList().add(baiscPacname);
//
//            }

        }
    }
    
    /**
     * 
     * @Title: buildIsextend
     * @Description: TODO(组织上传工程后自动赋权执行语句和回滚语句)
     * @param user
     * @param susSysInfo
     * @param con
     * @param infoExeclUtilBean
     * @throws RepositoryException
     * @return void 返回类型
     * @throws
     * @变更记录 2016年7月21日 yunpeng_zhang
     */
    public void buildIsextend ( Connection con, UserInfo user, InfoExeclUtilBean infoExeclUtilBean ) throws RepositoryException
    {
        try
        {
            List<Long> roleIdList = RoleManager.getInstance().getIsextendRoles(con, user.getId(), 1);
            long upperId = infoExeclUtilBean.getInsertProject().getIupperId().longValue();
            for (Long roleId : roleIdList)
            {
                Map<String, String> sqlMap = RolePermissionManager.getInstance().getIsextendSqls(con, roleId, upperId);
                if (!"".equals(sqlMap.get("exeSql")))
                {
                    infoExeclUtilBean.getExeSqls().add(sqlMap.get("exeSql"));
                }
                if (!"".equals(sqlMap.get("rollbackSql")))
                {
                    infoExeclUtilBean.getRollbackSqls().add(sqlMap.get("rollbackSql"));
                }
            }
        } catch (SQLException e)
        {
            // TODO Auto-generated catch block
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
    }
    
    

    /**
     * 
     * @Title: saveDataForMultiple
     * @Description: TODO(保存Excel上传数据所有相关表)
     * @param con
     * @param infoExeclUtilBean
     * @throws SQLException
     * @return void 返回类型
     * @throws RepositoryException 
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     * @update by le_wang 2016-6-6
     */
    private void saveDataForMultiple ( Connection con, InfoExeclUtilBean infoExeclUtilBean,boolean isBasicCon,String DBkey )
            throws SQLException, RepositoryException
    {

        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        PreparedStatement ps7 = null;
        PreparedStatement ps8 = null;
        int index = 0;
        try
        {
            // 保存IEAI_PROJECT表数据
            String sql1 = "INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IUPLOADTIME, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID)  VALUES(?,?,?,?,?,?,?,?,?,?,("
                    + Constants.getCurrentSysDate() + "),?,?,?,?,?,?,?)";
            ps1 = con.prepareStatement(sql1);
            index = 0;
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getId().longValue());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getPkgContentId().longValue());
            ps1.setString(++index, infoExeclUtilBean.getInsertProject().getName());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getMajorVersion().longValue());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getMinorVersion().longValue());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().isFreezed() ? 1 : 0);
            ps1.setString(++index, infoExeclUtilBean.getInsertProject().getUploadUserFullName());
            ps1.setString(++index, infoExeclUtilBean.getInsertProject().getComment());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getUploadNum().longValue());
            ps1.setString(++index, infoExeclUtilBean.getInsertProject().getUuid());
            if (infoExeclUtilBean.getInsertProject().isFreezed())
            {
                ps1.setString(++index, infoExeclUtilBean.getInsertProject().getFreezeUserFullName());
                ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getFreezeUserId()
                        .longValue());
            } else
            {
                ps1.setString(++index, "");
                ps1.setLong(++index, 0);
            }
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getUploadUserId().longValue());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getGroupId().longValue());
            ps1.setDouble(++index, infoExeclUtilBean.getInsertProject().getProType());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getIupperId());
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getId().longValue());
            ps1.executeUpdate();
            // 修改IEAI_PROJECT表数据
            String sql2 = "UPDATE IEAI_PROJECT T SET T.ILATESTID=? WHERE T.INAME=?  AND T.IGROUPID=?";
            ps2 = con.prepareStatement(sql2);
            index = 0;
            ps2.setLong(++index, infoExeclUtilBean.getInsertProject().getId().longValue());
            ps2.setString(++index, infoExeclUtilBean.getInsertProject().getName());
            ps2.setLong(++index, infoExeclUtilBean.getInsertProject().getGroupId().longValue());
            ps2.executeUpdate();
            // 保存IEAI_INSTANCE_VERSION表数据
            String sql3 = "INSERT INTO IEAI_INSTANCE_VERSION(IID, IINSTANCENAME, IVERSION, IEDITVERSION, IVERSIONALIAS, ISYSTYPE, IDES, ISYSID, ITIME, ISWITCHID, IPROJECTID, IIEAISYSID, IENVNAME,IUPPERID)    VALUES(?, ?, ?, ?, ?, ?, ?,-1,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8) , -1, ?, ?, ?, ?)";
            ps3 = con.prepareStatement(sql3);
            index = 0;
            ps3.setLong(++index, infoExeclUtilBean.getInsertVersion().getIid());
            ps3.setString(++index, infoExeclUtilBean.getInsertVersion().getInsName());
            ps3.setLong(++index, infoExeclUtilBean.getInsertVersion().getOldVersion());
            ps3.setInt(++index, 0);
            ps3.setString(++index, infoExeclUtilBean.getInsertVersion().getIversionalias());
            ps3.setInt(++index, infoExeclUtilBean.getInsertVersion().getIsystype());
            ps3.setString(++index, "");
            ps3.setLong(++index, infoExeclUtilBean.getInsertVersion().getIprojectid());
            ps3.setLong(++index, infoExeclUtilBean.getInsertVersion().getIieaisysid());
            ps3.setString(++index, infoExeclUtilBean.getInsertVersion().getEnvName());
            ps3.setLong(++index, infoExeclUtilBean.getInsertVersion().getIupperId());
            ps3.executeUpdate();
            // 保存IEAI_INSTANCEINFO表数据
            String sql4 = "INSERT INTO IEAI_INSTANCEINFO(IID, IINSTANCEID, IINSTANCENAME, ISERNER, ICONNER, "
                    + "ICONNERNAME, IPRENER, IACTNAME, IACTDES, "
                    + "IACTTYPE, IREMINFO, IIPNAME, IMODELTYPE, IIP, "
                    + "IPORT, ISYSTYPE, IEXECUSER, ISHELLSCRIPT, "
                    + "IISLOADENV, ISHELLPATH, ITIMEOUT, IPARAMETER, "
                    + "IEXPECEINFO, IEXCEPTINFO, IREDOABLE, IISDISABLE, "
                    + "ISWITCHSYSTYPE,IPARACHECK, IPARASWITCH, IPARASWITCHFORCE, "
                    + "IPKGNAME, ICENTER, IRETNVALEXCEPION, IPFLAG) "
                    + "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            ps4 = con.prepareStatement(sql4);
            for (SusExeclInfo susExeclInfo : infoExeclUtilBean.getInsertInstanceInfoList())
            {
                index = 0;
                ps4.setLong(++index, susExeclInfo.getIid());// DECIMAL
                ps4.setLong(++index, susExeclInfo.getIinstanceid()); // DECIMAL
                ps4.setString(++index, susExeclInfo.getIinstanceName());
                ps4.setDouble(++index, susExeclInfo.getActNo());// DECIMAL
                ps4.setLong(++index, susExeclInfo.getConNo()); // DECIMAL

                ps4.setString(++index, susExeclInfo.getIconnerName());
                ps4.setString(++index, susExeclInfo.getPreNo());
                ps4.setString(++index, susExeclInfo.getActName());
                ps4.setString(++index, susExeclInfo.getActDes());

                ps4.setInt(++index, susExeclInfo.getTaskType()); // int
                ps4.setString(++index, susExeclInfo.getTaskRem());
                ps4.setString(++index, susExeclInfo.getServerAlias());
                ps4.setString(++index, susExeclInfo.getModuleType());
                
                ps4.setString(++index, susExeclInfo.getAppDevice() != null ? susExeclInfo.getAppDevice().replace("\n", " "):susExeclInfo.getAppDevice());// 执行设备

                ps4.setInt(++index, susExeclInfo.getPort()); // SMALLINT
                ps4.setInt(++index, susExeclInfo.getSusType()); // SMALLINT
                ps4.setString(++index, susExeclInfo.getExecuser());
                ps4.setString(++index, susExeclInfo.getShellscript());

                ps4.setInt(++index, susExeclInfo.getIsLoadEnv()); // SMALLINT
                ps4.setString(++index, susExeclInfo.getShellPath());
                ps4.setInt(++index, susExeclInfo.getTimeOutT()); // DECIMAL
                ps4.setString(++index, susExeclInfo.getShellPara());

                ps4.setString(++index, susExeclInfo.getExpectInfo());
                ps4.setString(++index, susExeclInfo.getRetnValWhenException());

                try
                {
                    int iRedo = Integer.parseInt(susExeclInfo.getRedoable());
                    ps4.setInt(++index, iRedo);
                } catch (Exception e)
                {
                    ps4.setInt(++index, 0);
                }
                ps4.setString(++index, "0");
                ps4.setInt(++index, -1);
                ps4.setString(++index, "");
                ps4.setString(++index, "");
                ps4.setString(++index, "");
                ps4.setString(++index, "");
                ps4.setString(++index, "");
                ps4.setString(++index, "");
                ps4.setString(++index, susExeclInfo.getIpflag());
                ps4.addBatch();
            }
            ps4.executeBatch();
            // 保存IEAI_SUS_BASIC_INFO表数据
            String sql5 = "INSERT INTO IEAI_SUS_BASIC_INFO(IID, IINSTANCEID, IINSTANCENAME, IPARAMETER, IPARAVALUE,IDES)  VALUES(?, ?, ?, ?, ?, ?)";
            ps5 = con.prepareStatement(sql5);
            for (SUSExcelBaiscPro baiscPro : infoExeclUtilBean.getInsertBasicInfoList())
            {
                index = 0;
                ps5.setLong(++index, baiscPro.getIid());
                ps5.setLong(++index, baiscPro.getInstanceId());
                ps5.setString(++index, baiscPro.getInstanceName());
                ps5.setString(++index, baiscPro.getProName());
                ps5.setString(++index, baiscPro.getProValue());
                ps5.setString(++index, baiscPro.getProDes());
                ps5.addBatch();
            }
            ps5.executeBatch();
            boolean isshowpanel=Environment.getInstance().getBooleanConfigNew("em.show.parampanel.switch", false);
            if(isshowpanel){

            String sql8 = "INSERT INTO IEAI_EM_DYNAMIC_PARAMETER(IID, YUANID, PARAMETERKEY)  VALUES(?, ?, ?)";
            ps8 = con.prepareStatement(sql8);
            List<String> paramset = infoExeclUtilBean.getParamsSet();
            if (null != paramset && !paramset.isEmpty())
            {
                for (String paramKey : paramset)
                {
                    index = 0;
                    ps8.setLong(++index, IdGenerator.createId("IEAI_EM_DYNAMIC_PARAMETER", con));
                    ps8.setLong(++index, infoExeclUtilBean.getInsertProject().getId().longValue());
                    // ps8.setLong(++index, infoExeclUtilBean.getInsertVersion().getIupperId());
                    ps8.setString(++index, paramKey);
                    ps8.addBatch();
                }
                ps8.executeBatch();
            }
            }
            // 保存IEAI_SUS_BASIC_PACNAME_INFO表数据
//            String sql6 = "INSERT INTO IEAI_SUS_BASIC_PACNAME_INFO(IPACNAMEID, IINSTANCEID, IINSTANCENAME, IPACKETNAME, IDISABLESER, IDISABLEACTNAME)  VALUES(?, ?, ?, ?, ?, ?)";
//            ps6 = con.prepareStatement(sql6);
//            for (SUSExcelBaiscPacname baiscPacname : infoExeclUtilBean.getInsertBaiscPacnameList())
//            {
//                index = 0;
//                ps6.setLong(++index, baiscPacname.getIpacNameId());
//                ps6.setLong(++index, baiscPacname.getInstanceId());
//                ps6.setString(++index, baiscPacname.getInstanceName());
//                ps6.setString(++index, baiscPacname.getPacketName());
//                ps6.setString(++index, baiscPacname.getDisableser());
//                ps6.setString(++index, baiscPacname.getDisableactName());
//                ps6.addBatch();
//            }
//            ps6.executeBatch();
            // 保存IEAI_SUS_ENV_INFO表数据
//            String sql7 = "INSERT INTO IEAI_SUS_ENV_INFO( IID,IINSTANCEID,IINSTANCENAME,"
//                    + "IIP,IPORT,IIPNAME,IMODULETYPE,IDES)" + "VALUES(?, ?, ?, ?, ?,?, ?, ?)";
//            ps7 = con.prepareStatement(sql7);
//            for (SUSExcelHostsOfEnv hostsOfEnv : infoExeclUtilBean.getInsertHostsOfEnvList())
//            {
//                index = 0;
//                ps7.setLong(++index, hostsOfEnv.getIid());
//                ps7.setLong(++index, hostsOfEnv.getInstanceId());
//                ps7.setString(++index, hostsOfEnv.getInstanceName());
//
//                ps7.setString(++index, hostsOfEnv.getExecOnHost());
//                ps7.setString(++index, hostsOfEnv.getAgentPort());
//                ps7.setString(++index, hostsOfEnv.getAppAlias());
//                ps7.setString(++index, hostsOfEnv.getModelType());
//                ps7.setString(++index, "");
//                ps7.addBatch();
//            }
//            ps7.executeBatch();
            
            // 执行新增修改语句(后期扩展用)
            for (String exeSql : infoExeclUtilBean.getExeSqls())
            {
                int returnVale = DBUtil.executeSqlReturnNum(con, exeSql);
                // 判断更新数据与基线库更新数据的数量是否一致
                if (Constants.DB_COMPARE)
                {
                    String mapKey2 = exeSql.trim();
                    if (isBasicCon)
                    {
                        if (mapKey2.startsWith("UPDATE") || mapKey2.startsWith("DELETE"))
                        {
                            infoExeclUtilBean.getEitRowCount().put(mapKey2, returnVale);
                        }

                    } else
                    {
                        if (mapKey2.startsWith("UPDATE") || mapKey2.startsWith("DELETE"))
                        {
                            if (returnVale != infoExeclUtilBean.getEitRowCount().get(mapKey2))
                            {
                                dbsRollbacklog.error("数据源：" + DBkey + " '例行任务>系统录入' " + mapKey2 + " 与操作基线库表中的数据量不一致！");
                                throw new SQLException("数据源：" + DBkey + " '例行任务>系统录入' " + mapKey2 + " 与操作基线库表中的数据量不一致！");
                            }
                        }

                    }
                }
            }
        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            boolean isshowpanel=Environment.getInstance().getBooleanConfigNew("em.show.parampanel.switch", false);
            if(isshowpanel){

            if (null != ps8)
            {
                ps8.close();
            }
            }
            if (null != ps7)
            {
                ps7.close();
            }
            if (null != ps6)
            {
                ps6.close();
            }
            if (null != ps5)
            {
                ps5.close();
            }
            if (null != ps4)
            {
                ps4.close();
            }
            if (null != ps3)
            {
                ps3.close();
            }
            if (null != ps2)
            {
                ps2.close();
            }
            if (null != ps1)
            {
                ps1.close();
            }
        }
    }
    
    /**
     * 
     * @Title: rollbackDataForMultiple
     * @Description: TODO(多写失败并有提交的链接，自定义回滚)
     * @param con
     * @param infoExeclUtilBean
     * @throws Exception
     * @return void 返回类型
     * @throws
     * @变更记录 2016年6月2日 yunpeng_zhang
     */
    private void rollbackDataForMultiple ( Connection con, InfoExeclUtilBean infoExeclUtilBean )
            throws Exception
    {

        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
//        PreparedStatement ps6 = null;
//        PreparedStatement ps7 = null;
        int index = 0;
        try
        {
            // 删除IEAI_SUS_ENV_INFO表数据
//            String sql7 = "DELETE IEAI_SUS_ENV_INFO T WHERE T.IINSTANCEID=?";
//            ps7 = con.prepareStatement(sql7);
//            index = 0;
//            ps7.setLong(++index, infoExeclUtilBean.getInsertVersion().getIid());
//            ps7.executeUpdate();
//            // 删除IEAI_SUS_BASIC_PACNAME_INFO表数据
//            String sql6 = "DELETE IEAI_SUS_BASIC_PACNAME_INFO T WHERE T.IINSTANCEID=?";
//            ps6 = con.prepareStatement(sql6);
//            index = 0;
//            ps6.setLong(++index, infoExeclUtilBean.getInsertVersion().getIid());
//            ps6.executeUpdate();
            // 删除IEAI_SUS_BASIC_INFO表数据
            String sql5 = "DELETE IEAI_SUS_BASIC_INFO T WHERE T.IINSTANCEID=?";
            ps5 = con.prepareStatement(sql5);
            index = 0;
            ps5.setLong(++index, infoExeclUtilBean.getInsertVersion().getIid());
            ps5.executeUpdate();
            // 删除IEAI_INSTANCEINFO表数据
            String sql4 = "DELETE IEAI_INSTANCEINFO T WHERE T.IINSTANCEID=?";
            ps4 = con.prepareStatement(sql4);
            index = 0;
            ps4.setLong(++index, infoExeclUtilBean.getInsertVersion().getIid());
            ps4.executeUpdate();
            // 保存IEAI_INSTANCE_VERSION表数据
            String sql3 = "DELETE IEAI_INSTANCE_VERSION T WHERE T.IID=?";
            ps3 = con.prepareStatement(sql3);
            index = 0;
            ps3.setLong(++index, infoExeclUtilBean.getInsertVersion().getIid());
            ps3.executeUpdate();
            // 修改IEAI_PROJECT表latestid回到原值
            String sql2 = "UPDATE IEAI_PROJECT T SET T.ILATESTID=? WHERE T.INAME=?  AND T.IGROUPID=?";
            ps2 = con.prepareStatement(sql2);
            index = 0;
            ps2.setLong(++index, infoExeclUtilBean.getLatestId());
            ps2.setString(++index, infoExeclUtilBean.getInsertProject().getName());
            ps2.setLong(++index, infoExeclUtilBean.getInsertProject().getGroupId().longValue());
            ps2.executeUpdate();
            // 删除IEAI_PROJECT表数据
            String sql1 = "DELETE IEAI_PROJECT T WHERE T.IID=?";
            ps1 = con.prepareStatement(sql1);
            index = 0;
            ps1.setLong(++index, infoExeclUtilBean.getInsertProject().getId().longValue());
            ps1.executeUpdate();

            con.commit();
        } catch (Exception e)
        {
            con.rollback();
            throw e;

        } finally
        {
            /*if (null != ps7)
            {
                ps7.close();
            }
            if (null != ps6)
            {
                ps6.close();
            }*/
            if (null != ps5)
            {
                ps5.close();
            }
            if (null != ps4)
            {
                ps4.close();
            }
            if (null != ps3)
            {
                ps3.close();
            }
            if (null != ps2)
            {
                ps2.close();
            }
            if (null != ps1)
            {
                ps1.close();
            }
        }
    }
}
