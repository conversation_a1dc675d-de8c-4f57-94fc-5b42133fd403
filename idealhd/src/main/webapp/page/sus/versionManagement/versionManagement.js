/*******************************************************************************
 * 变更版本管理列表
 ******************************************************************************/
var scriptNameObj;
var scriptNameStore;
var scriptContentObj;
var stepNameObj;
var flowStore;
var bsPageBar=null;
var queryForm = null;
var flowGrid=null;
var MainPanel=null;
Ext
        .onReady (function ()
        {
	        // 清理主面板的各种监听时间
	        destroyRubbish ();
	        /** 树数据Model* */
	        Ext.define ('flowModel',
	        {
	            extend : 'Ext.data.Model',
	            fields : [
	                    {
	                        name : 'iruninsname',
	                        type : 'string'
	                    }, {
	                        name : 'iversiondes',
	                        type : 'string'
	                    }, {
	                        name : 'iname',
	                        type : 'string'
	                    }, {
	                        name : 'isize',
	                        type : 'long'
	                    },
	                    {
	                        name : 'iid',
	                        type : 'long'
	                    },
	                    {
	                        name : 'istarttime',
	                        type : 'string'
	                    },{
	                        name : 'iendtime',
	                        type : 'string'
	                    }
	            ]
	        });
	         queryForm = Ext.create('Ext.form.Panel', {
	        	region: 'north',
	    		//height: 90,
	    		border : true,
	    		collapsible : true,//可收缩
	    		collapsed : false,//默认收缩
//	    	    border: false,
	    	    width : '100%',
//	    	    height: 65,
	    	    bodyPadding: 5,
	    	    layout: 'form',
	    	    dockedItems : [ {
	    	    	baseCls:'customize_gray_back',
	    			xtype : 'toolbar',
	    			items : [
	    		              {
	    			    	        labelWidth :51,
	    			    	        width: 300,
	    			    	        xtype: 'textfield',
	    			    	        labelAlign : 'right',
	    			    	        fieldLabel: '方案名',
	    			    	        name: 'instanceName'//,
	    			    	        //padding : '0 10 0 0' 
	    			    	      },{
	    			    	        xtype: 'button',
	    			    	        cls:'Common_Btn',
	    			    	        text: '查询',
	    			    	        //width:60,
	    			    	       // margin : '0 10 0 0',
	    			    	        handler: function() {
	    			    	          queryWhere();
	    			    	        }
	    			    	      },{
	    			    	        xtype: 'button',
	    			    	        cls:'Common_Btn',
	    			    	        text: '重置',
	    			    	        //width:60,
	    			    	        //margin : '0 10 0 0',
	    			    	        handler: function() {
	    			    	          resetWhere();
	    			    	        }
	    			    	      } ]}]//,
//	    	    items: [{
//	    	      layout:'column',
//	    	      border : false,
//	    	      items: []
//	    	    }]
	    	  });
	     	contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
	    	queryForm.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
	        /** 树数据源* */
	        flowStore = Ext.create ('Ext.data.Store',
	        {
	            autoLoad : true,
	            autoDestroy : true,
	            model : 'flowModel',
	            pageSize : 50,
	            proxy :
	            {
	                type : 'ajax',
	                url : 'getVersionList_sus.do',
	                reader :
	                {
	                    type : 'json',
	                    root : 'dataList'
	                }
	            }
	        });
	        
	        flowStore.on ('beforeload', function (store, options)
       		 {
       			 var instanceName = queryForm.getForm().findField("instanceName").getValue();
       			 var new_params =
       			 {
       					 instanceName : trim(instanceName)
       			 };
       			 Ext.apply (flowStore.proxy.extraParams, new_params);
       		 });
	        /** 树列表columns* */
	        var flowColumns = [
	                {
	                    text : '序号',
	                    // align : 'center',
	                    width : 65,
	                    xtype : 'rownumberer'
	                },
	                {
	                    text : '方案名',
	                    dataIndex : 'iruninsname',
	                    flex : 1
	                },
	                {
	                    text : '版本说明',
	                    dataIndex : 'iversiondes',
	                    flex : 1
	                },
	                {
	                    text : '文件名',
	                    dataIndex : 'iname',
	                    flex : 1
	                },
	                {
	                    text : '文件大小',
	                    dataIndex : 'isize',
	                    width : 100,
	                    renderer : function (value, metaData, record)
	                    {
	                    	return (Math.round(value/1024))+" KB";
	                    }
	                },
	                {
	                    text : '开始时间',
	                    dataIndex : 'istarttime',
	                    width : 150
	                },
	                {
	                    text : '结束时间',
	                    dataIndex : 'iendtime',
	                    width : 150
	                },
	                {
	                    text : '操作',
	                    dataIndex : 'instanceName',
	                    sortable : false,
	                    hideable : false,// 是否可以手动隐藏
	                    // flex : 1,
	                    width : 100,
	                    align : 'center',
	                    renderer : function (value, metaData, record)
	                    {
	                    	return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="downloadVersionFun('
                            + record.get ('iid')+ ')">下载</a>';
	                    }
	                }
	        ];
	        bsPageBar = Ext.create ('Ext.PagingToolbar',
	        {
	            store : flowStore,
	            dock : 'bottom',
	            baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	            border : false,
	            displayInfo : true,
	            emptyMsg : "没有记录"
	        });
	        /** 树列表panel* */
	        flowGrid = Ext.create ('Ext.grid.Panel',
	        {
	        	region: 'center',
	        	//extend : 'Ext.grid.Panel',
	            //alias : 'widget.ideapanel',
	            //ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	            cls:'customize_grid_back',
	            store : flowStore,
	            columns : flowColumns,
	            padding : grid_space,
	            bbar : bsPageBar,
	            collapsible : false,
	    	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    	    {
	    		    checkOnly : true
	    	    })
	        });
	        // 主Panel
	        MainPanel = Ext.create ('Ext.panel.Panel',
	        {
	            renderTo : "mainpanelE",
	            width : '100%',
	            height : contentPanel.getHeight (),
	            //bodyCls: 'service_platform_bodybg',
	            // overflowY:'scroll',
	           // bodyCls: 'service_platform_bodybg',
	            cls:'customize_panel_header_arrow',
	            border : true,
//	            bodyPadding : 5,
				bodyPadding : grid_space,
	            layout : 'border',
	            items : [
		            queryForm,flowGrid
	            ]
	        });
	        
	        
	        $("body").off('keydown').on('keydown',function(event) {
	     	    if (event.keyCode == "13") { 
	     	    	queryWhere();
	     	    }
	     	});
		           
		   	contentPanel.on ('resize', function ()
		   	{
		   		MainPanel.setWidth(contentPanel.getWidth ());
		   		MainPanel.setHeight (contentPanel.getHeight());
		   	});
	        contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	        {
		        Ext.destroy (MainPanel);
		        if (Ext.isIE)
		        {
			        CollectGarbage ();
		        }
	        });
	        function trim (t)
	        {
		        t = t.replace (/(^\s*)|(\s*$)/g, "");
		        return t.replace (/(^ *)|( *$)/g, "");
	        }
	        
	        /**查询条件清空*/
	    	function resetWhere(){
	    		queryForm.getForm().findField("instanceName").setValue('');
	    	};
	    	
	    	function queryWhere(){
	    		
	    		bsPageBar.moveFirst();
	    	};
        });

/**下载**/
function downloadVersionFun(versionid) {
	window.location.href = 'downloadVersion_sus.do?versionid='+versionid;
}
