package com.ideal.ieai.server.webservice;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.ideal.ieai.commons.UserBasicInfo;
import com.ideal.ieai.server.ieaikernel.ServerEnv;

/**
 * <p>
 * Title: User
 * </p>
 * <p>
 * Description:
 * </p>`
 * <p>
 * Copyright: Copyright 2003
 * </p>
 * <p>
 * Company: Ideal
 * </p>
 * 
 * <AUTHOR>
 * <AUTHOR>
 * @version 3.0
 */

public class WsUserBasicInfo extends WsUserInfo implements Cloneable
{

    // List _permissions;

    /**
     * destructor for init basic infomation of user
     */
    public WsUserBasicInfo(String lognName, String fullName, String password, String department,
            String email, String telephone, String locale)
    {
        this._loginName = lognName;
        setFullName(fullName);
        this._password = password;
        this._department = department;
        this._email = email;
        this._telephone = telephone;
        this._locale = locale;
    }

    /**
     * return commons userinfo
     * 
     * @return UserInfo
     */
    public com.ideal.ieai.commons.UserBasicInfo toCommonsUserBasicInfo ()
    {
        com.ideal.ieai.commons.UserBasicInfo instance = new com.ideal.ieai.commons.UserBasicInfo();
        // set department
        instance.setDepartment(this.getDepartment());
        // set emali
        instance.setEmail(this.getEmail());
        // set full name
        instance.setFullName(this.getFullName());
        // set log id
        instance.setLoginName(this.getLoginName());
        // set password
        instance.setPassword(this.getPassword());
       
       
        // set telephone
        instance.setTelephone(this.getTelephone());

        instance.setLocale(this._locale);
        // set user id
        instance.setId(this.getId());
        instance.setLocked(isLocked());
        return instance;
    }

    /**
     * give a commons userinfo ,create a webservice userInfo
     * 
     * @param info UserInfo
     * @return UserInfo
     */
    public static WsUserBasicInfo newInstance ( com.ideal.ieai.commons.UserBasicInfo info )
    {
        if (null == info)
        {
            return null;
        }
        WsUserBasicInfo instance = new WsUserBasicInfo();
        // set department
        instance.setDepartment(info.getDepartment());
        // set email
        instance.setEmail(info.getEmail());
        // set user name
        instance.setFullName(info.getFullName());
        // set log id
        instance.setLoginName(info.getLoginName());
        // set password
        instance.setPassword(info.getPassword());
        // set calendar id

        instance.setLocale(info.getLocale());
        
        // set telephone
        instance.setTelephone(info.getTelephone());
        // set user id
        instance.setId(info.getId());
        instance.setLocked(info.isLocked());
        instance.setLastUpdatedPasswordTime(info.getLastUpdatePasswordTime());
        instance.setPasswordExpired(info.isPasswordOvertime(ServerEnv.getServerEnv()
                .getPasswordExpiredTime()));
        return instance;
    }

    /**
     * translate a list of common.UserInfo instances to an UserBasicInfo Array
     * 
     * @param commonUsers the content is common.UserInfo instance
     * @return 0 length array if the param is null or empty list
     */
    public static WsUserBasicInfo[] toUserBasicInfoArray ( List commonUsers )
    {
        if (commonUsers == null || commonUsers.isEmpty())
        {
            return new WsUserBasicInfo[] {};
        }
        int size = commonUsers.size();
        WsUserBasicInfo[] result = new WsUserBasicInfo[size];
        for (int i = 0; i < size; i++)
        {
            UserBasicInfo commonUser = (UserBasicInfo) commonUsers
                    .get(i);
            result[i] = newInstance(commonUser);
        }
        return result;
    }

    /**
     * translate a array of webservice.RoleInfo to a list of common.UserInfo instances
     * 
     * @param wsUserInfos
     * @return a list of commmon.UserInfo instances, empty list if the param is null or of 0 length
     */
    public static List toCommonUserInfos ( WsUserBasicInfo[] wsUserInfos )
    {
        if (wsUserInfos == null || wsUserInfos.length == 0)
        {
            return Collections.EMPTY_LIST;
        }
        List result = new ArrayList();
        for (int i = 0; i < wsUserInfos.length; i++)
        {
            WsUserBasicInfo info = wsUserInfos[i];
            result.add(info.toCommonsUserBasicInfo());
        }
        return result;

    }

    /**
     * added by xu ming to perssit the server manager old code
     * 
     * @param id
     * @param password
     * @param fullName
     */
    public WsUserBasicInfo(String id, String password, String fullName)
    {
        this(id, fullName, password, null, null, null, null);
    }

    /**
     * default constructor
     */
    public WsUserBasicInfo()
    {
    }

    

    /**
     * geter of department attribute
     * 
     * @return
     */
    public String getDepartment ()
    {
        return _department;
    }

    /**
     * getter of email attribute
     * 
     * @return
     */
    public String getEmail ()
    {
        return _email;
    }

    /**
     * getter of password attribute
     * 
     * @return
     */
    public String getPassword ()
    {
        return _password;
    }

    /**
     * getter of telephone attribute
     * 
     * @return
     */
    public String getTelephone ()
    {
        return _telephone;
    }

    /**
     * setter of telephone attribute
     * 
     * @param telephone
     */
    public void setTelephone ( String telephone )
    {
        this._telephone = telephone;
    }

    /**
     * setter of password attribute
     * 
     * @param password
     */
    public void setPassword ( String password )
    {
        this._password = password;
    }

    /**
     * setter of email attribute
     * 
     * @param email
     */
    public void setEmail ( String email )
    {
        this._email = email;
    }

    /**
     * setter of department attribute
     * 
     * @param department
     */
    public void setDepartment ( String department )
    {
        this._department = department;
    }



    
    /**
     * override the toString method
     * 
     * @return
     */
    public String toString ()
    {
        return "the id is: " + getId() + "\n the fullname is: " + getFullName() + "\n the email is: "
                + _email + "\n the department is: " + _department + "\n the telephone is: "
                + _telephone + "\n the password is :" + _password + "\n the locale is :" + _locale;

    }

    public String getLoginName ()
    {
        return _loginName;
    }

    public void setLoginName ( String loginName )
    {
        this._loginName = loginName;
    }

    public String getLocale ()
    {
        return _locale;
    }

    public void setLocale ( String locale )
    {
        this._locale = locale;
    }

    
    private long          _lastUpdatedPasswordTime;
    private boolean       _passwordExpired;

   
    // user's id,it is unique and not null
    private String        _loginName;

    

    // use's password:
    private String        _password;

    // use's email
    private String        _email;

    // use's telephone
    private String        _telephone;

    private String        _locale;

    // user's depardment:
    private String        _department;

    public long getLastUpdatedPasswordTime ()
    {
        return _lastUpdatedPasswordTime;
    }

    public void setLastUpdatedPasswordTime ( long lastUpdatedPasswordTime )
    {
        _lastUpdatedPasswordTime = lastUpdatedPasswordTime;
    }



    public boolean isPasswordExpired ()
    {
        return _passwordExpired;
    }

    public void setPasswordExpired ( boolean passwordExpired )
    {
        _passwordExpired = passwordExpired;
    }
}
