package com.ideal.controller.sus.installation ;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.util.LinkedHashMap;
import com.ideal.ieai.commons.Constants;
import com.ideal.common.utils.ParseJson;
import com.ideal.ieai.server.repository.sus.installation.LSBDevice;
import com.ideal.ieai.server.repository.sus.installation.LSBDeviceService;
import com.ideal.ieai.server.repository.sus.installation.LSBDeviceManager;
/**
   *
   * @ClassName: LSBDeviceController
   * @Description: 裸设备安装,设备列表
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
   */
@Controller
public class LSBDeviceController 
{
   private static Logger _log =Logger.getLogger(LSBDeviceController.class);


   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表页面的访问(全部数据)
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("accessLSBDevice.do")
   public String accessLSBDevice (HttpServletRequest request )
   {
       request.setAttribute("install_istate", LSBDeviceManager.STATE_ALL);
      return "sus/installation/LSBDevice/LSBDevice";
   }
   
   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表页面的访问(安装中)
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("accessLSBDeviceInstalling.do")
   public String accessLSBDeviceInstalling (HttpServletRequest request )
   {
       request.setAttribute("install_istate", LSBDeviceManager.STATE_INSTALL_ING);
      return "sus/installation/LSBDevice/LSBDevice";
   }
   
   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表页面的访问(安装完成)
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("accessLSBDeviceFininshed.do")
   public String accessLSBDeviceFininshed (HttpServletRequest request )
   {
       request.setAttribute("install_istate", LSBDeviceManager.STATE_INSTALL_FAIL);
      return "sus/installation/LSBDevice/LSBDevice";
   }
   
   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表页面的访问
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("accessLSBDevice_detail.do")
   public String accessLSBDevice_detail (HttpServletRequest request )
   {
      return "sus/installation/LSBDevice/LSBDevice_detail";
   }
   
   /**
   *
   * @Title: 
   * @Description: 应用网段详情
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("accessLSBNetsgt_detail.do")
   public String accessLSBNetsgt_detail (HttpServletRequest request )
   {
      return "sus/installation/LSBNetsgtAPP/LSBNetsgt_detail";
   }
   
   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表的查询
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("queryLSBDevice.do")
   @ResponseBody
   public Map queryLSBDevice (HttpServletRequest request , LSBDevice lsbdevice , int start , int limit )
   {
      Map map =null;
      LSBDeviceService lSBDeviceService =new LSBDeviceService();
      Map<String,String> resp =new LinkedHashMap<String,String>();
      try
      {
         int type =Constants.IEAI_SUS;
         map=lSBDeviceService.queryLSBDevice(lsbdevice, start, limit, type);
         map.put("success", true);
         map.put("message", "操作成功");
      }catch(Exception e )
      {
         e.printStackTrace();
         _log.error(e);
         map.put("success", false);
         map.put("message", "操作失败");
      }
      return map;
   }



   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表的保存
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("saveLSBDevice.do")
   @ResponseBody
   public Map<String,Object> saveLSBDevice (HttpServletRequest request , String json )
   {
      Map<String,Object> resp =new LinkedHashMap<String,Object>();
      try
      {
         int type =Constants.IEAI_SUS;
         List<Map<String,Object>> dataList =new ArrayList<Map<String,Object>>();
         dataList=ParseJson.JSON2List(json);
         LSBDeviceService lSBDeviceService =new LSBDeviceService();
         lSBDeviceService.saveLSBDevice(dataList, type);
         resp.put("success", true);
      }catch(Exception e )
      {
         e.printStackTrace();
         _log.error(e);
         resp.put("success", false);
      }
      return resp;
   }



   /**
   *
   * @Title: 
   * @Description: 裸设备安装,设备列表的删除
   * @author: tiejun_fan
   * @date: 2020年11月17日 08:30:39
   */
   @RequestMapping("deleteLSBDevice.do")
   @ResponseBody
   public Map<String,Object> deleteLSBDevice (HttpServletRequest request , String iids )
   {
      Map<String,Object> resp =new LinkedHashMap<String,Object>();
      try
      {
         int type =Constants.IEAI_SUS;
         LSBDeviceService lSBDeviceService =new LSBDeviceService();
         lSBDeviceService.deleteLSBDevice(iids, type);
         resp.put("success", true);
      }catch(Exception e )
      {
         e.printStackTrace();
         _log.error(e);
         resp.put("success", false);
      }
      return resp;
   }



}