package com.ideal.ieai.commons.agent;

/**
 * 用于存放agent信息，类似实体bean
 * 
 * <AUTHOR>
 * 
 */
public class AgentObj {
	private String ip;
	private int port;
	private String flowName;

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public String getFlowName() {
		return flowName;
	}

	public void setFlowName(String flowName) {
		this.flowName = flowName;
	}
	
	public String toString()
	{
		return this.ip+":"+this.port;
	}
}
