package com.ideal.ieai.server.repository.topo.avgtimereport;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

import java.io.IOException;
import java.io.OutputStream;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;

public class AvgTimeReportManager
{
    private static final Logger _log = Logger.getLogger("topoLog");

    public Map getAvgTimeReportData ( String systemNames, String flowNames, String actType, String startTime,
            String endTime, boolean isGraph, String startLine, String limit ) throws RepositoryException
    {
        String method = "getAvgTimeReportData";
        Map map = new HashMap();
        List list = new ArrayList();
        Connection conn = null;
        CallableStatement cs = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        String sql = "call PROC_COUNT_AVG_JUST(?,?,?,?,?,?)";
        String sql1 = "select * from TMP_ACT_AVGTIME t";
        String sql2 = "select count(1) from TMP_ACT_AVGTIME";
        if (isGraph)
        {
            sql1 += " order by t.IAVGTIME desc ";
            sql1 = "select * from (" + sql1 + ") t1 where rownum <= 5 ";
        } else
        {
            sql1 += " order by t.IAVGTIME desc";
            sql1 = "select * from (select t1.*, rownum as r from (" + sql1 + ") t1) t2 where t2.r between "
                    + Integer.valueOf(startLine) + " and  " + (Integer.valueOf(startLine) + Integer.valueOf(limit));
        }
        try
        {
            Long startDate = dateStringToLong(startTime);
            Long endDate = dateStringToLong(endTime);
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            cs = conn.prepareCall(sql);
            cs.setLong(1, startDate);
            cs.setLong(2, endDate);
            cs.setString(3, "''".equals(systemNames) ? null : systemNames);
            cs.setString(4, "''".equals(flowNames) ? null : flowNames);
            cs.setString(5, null);
            cs.setString(6, actType);
            cs.execute();
            ps = conn.prepareStatement(sql1);
            rs = ps.executeQuery();
            DecimalFormat df = new DecimalFormat("0.00");
            while (rs.next())
            {
                AvgTimeReportModel model = new AvgTimeReportModel();
                model.setSystemName(rs.getString("IPRJNAME"));
                model.setFlowName(rs.getString("IFLOWNAME"));
                model.setActName(rs.getString("IACTNAME"));
                model.setRunTime(rs.getString("IRUNNUM"));
                model.setAvgTime(String.valueOf(df.format((rs.getDouble("IAVGTIME") / 1000 / 60))));
                String timeMonth = rs.getString("ITIMEMONTH") == null ? "0" : rs.getString("ITIMEMONTH");
                String timeDay = rs.getString("ITIMEDAY") == null ? "0" : rs.getString("ITIMEDAY");
                String timeWeek = rs.getString("ITIMEWEEK") == null ? "0" : rs.getString("ITIMEWEEK");
                String avgType = "月：" + timeMonth + " 日：" + timeDay + " 周：" + timeWeek;
                model.setAvgType(avgType);
                String prjActName = rs.getString("IPRJNAME") + "(" + rs.getString("IACTNAME") + ")";
                if (prjActName.length() > 10)
                {
                    prjActName = prjActName.substring(0, 10);
                    prjActName += "...";
                }
                model.setSuojianSystemName(prjActName);
                list.add(model);
            }
            map.put("avgReportList", list);
            ps1 = conn.prepareStatement(sql2);
            rs1 = ps1.executeQuery();
            int count = 0;
            while (rs1.next())
            {
                count = rs1.getInt(1);
            }
            map.put("totalProperty", count);
        } catch (Exception e)
        {
            _log.error(e);
        } finally
        {
            DBResource.closeCallableStatement(cs, method, _log);
            DBResource.closePSRS(rs1, ps1, method, _log);
            DBResource.closeConn(conn, rs, ps, method, _log);
        }
        return map;
    }

    private Long dateStringToLong ( String s )
    {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long l = 0L;
        if (StringUtils.isNotBlank(s))
        {
            try
            {
                l = format.parse(s).getTime();
            } catch (ParseException e)
            {
                _log.error(e);
            }
        }
        return l;
    }

    public void doExportAvgTimeReport ( Map avgTimeReport, OutputStream out )
    {
        HSSFWorkbook hssfworkbook = new HSSFWorkbook();
        // 获取所有记录
        try
        {
            // 将同一系统的数据放到同一个sheet页中

            // 说明样式
            HSSFFont headFont = hssfworkbook.createFont();
            headFont.setFontHeightInPoints((short) 11);
            headFont.setColor(IndexedColors.RED.getIndex());
            HSSFCellStyle headStyle = hssfworkbook.createCellStyle();
            headStyle.setFont(headFont);
            headStyle.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE.getIndex());
            headStyle.setFillPattern(SOLID_FOREGROUND);
            headStyle.setWrapText(true);

            // 标题栏样式
            HSSFFont titleFont = hssfworkbook.createFont();
            titleFont.setFontHeightInPoints((short) 10);
            titleFont.setBold(true);
            HSSFCellStyle titleStyle = hssfworkbook.createCellStyle();
            titleStyle.setFont(titleFont);
            titleStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());
            titleStyle.setFillPattern(SOLID_FOREGROUND);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            titleStyle.setBorderTop(BorderStyle.THIN);
            titleStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());

            // 数据样式
            HSSFFont dataFont = hssfworkbook.createFont();
            dataFont.setFontHeightInPoints((short) 10);
            HSSFCellStyle dataStyle = hssfworkbook.createCellStyle();
            dataStyle.setFont(dataFont);

            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
            // 获取7个sheet页数据
            List avgReportList = (List) avgTimeReport.get("avgReportList");
            String[] disBackupActInfoTitle = new String[] { "系统名称", "流程名", "批量名", "执行次数", "平均耗时", "平均耗时类型" };
            // 创建sheet页
            HSSFSheet disBackupActInfoSheet = hssfworkbook.createSheet("批量平均耗时报表");
            // 标题栏
            HSSFRow disBackupActInfoRowTitle = disBackupActInfoSheet.createRow(0);
            disBackupActInfoRowTitle.setHeightInPoints(24);
            for (int j = 0; j < disBackupActInfoTitle.length; j++)
            {
                HSSFCell titleCell = disBackupActInfoRowTitle.createCell(j);
                titleCell.setCellValue(disBackupActInfoTitle[j]);
                titleCell.setCellStyle(titleStyle);
            }
            // 数据栏
            for (int k = 0; k < avgReportList.size(); k++)
            {
                AvgTimeReportModel systemTimeActModel = (AvgTimeReportModel) avgReportList.get(k);
                HSSFRow dataRow = disBackupActInfoSheet.createRow(k + 1);
                dataRow.setHeightInPoints(27);
                HSSFCell prjNameCell = dataRow.createCell(0);
                prjNameCell.setCellValue(systemTimeActModel.getSystemName());
                prjNameCell.setCellStyle(dataStyle);
                HSSFCell flowNameCell = dataRow.createCell(1);
                flowNameCell.setCellValue(systemTimeActModel.getFlowName());
                flowNameCell.setCellStyle(dataStyle);
                HSSFCell startTimeCell = dataRow.createCell(2);
                startTimeCell.setCellValue(systemTimeActModel.getActName());
                startTimeCell.setCellStyle(dataStyle);
                HSSFCell endTimeCell = dataRow.createCell(3);
                endTimeCell.setCellValue(systemTimeActModel.getRunTime());
                endTimeCell.setCellStyle(dataStyle);
                HSSFCell endTimeCell1 = dataRow.createCell(4);
                endTimeCell1.setCellValue(systemTimeActModel.getAvgTime());
                endTimeCell1.setCellStyle(dataStyle);
                HSSFCell endTimeCell2 = dataRow.createCell(5);
                endTimeCell2.setCellValue(systemTimeActModel.getAvgType());
                endTimeCell2.setCellStyle(dataStyle);

            }
            hssfworkbook.write(out);
            out.flush();
        } catch (IOException e)
        {
            _log.error(e);
        } finally
        {
            try
            {
                out.close();
            } catch (IOException e)
            {
                _log.error(e);
            }
        }
    }
}
