package com.ideal.ieai.studio.view.editorui.flowchart;

import java.awt.Color;
import java.awt.Dimension;
import java.awt.Font;
import java.awt.Point;
import java.awt.Rectangle;
import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.Icon;
import javax.swing.ImageIcon;
import javax.swing.JFileChooser;
import javax.swing.SwingConstants;
import javax.swing.border.Border;

import org.jgraph.JGraph;
import org.jgraph.graph.DefaultEdge;
import org.jgraph.graph.DefaultGraphCell;
import org.jgraph.graph.DefaultGraphModel;
import org.jgraph.graph.DefaultPort;
import org.jgraph.graph.Edge;
import org.jgraph.graph.GraphConstants;
import org.jgraph.graph.ParentMap;
import org.jgraph.graph.Port;

import com.ideal.ieai.core.activity.ActivityDef;
import com.ideal.ieai.core.basis.IDObjectMap;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.element.workflow.AssistElement;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.core.element.workflow.CallWorkflowActElement;
import com.ideal.ieai.core.element.workflow.CommentElement;
import com.ideal.ieai.core.element.workflow.EndActElement;
import com.ideal.ieai.core.element.workflow.EntryActElement;
import com.ideal.ieai.core.element.workflow.ExceptionHandlerActElement;
import com.ideal.ieai.core.element.workflow.ExitActElement;
import com.ideal.ieai.core.element.workflow.FlowEdgeInfo;
import com.ideal.ieai.core.element.workflow.HelperElement;
import com.ideal.ieai.core.element.workflow.LoopEntryActElement;
import com.ideal.ieai.core.element.workflow.LoopExitActElement;
import com.ideal.ieai.core.element.workflow.ParaEntryActElement;
import com.ideal.ieai.core.element.workflow.ParaExitActElement;
import com.ideal.ieai.core.element.workflow.SequenceEntryActElement;
import com.ideal.ieai.core.element.workflow.SequenceExitActElement;
import com.ideal.ieai.core.io.ProjectConstants;
import com.ideal.ieai.core.util.graph.CommentCell;
import com.ideal.ieai.core.util.graph.EllipseCell;
import com.ideal.ieai.core.util.graph.GraphHelper;
import com.ideal.ieai.core.util.graph.HelperObject;
import com.ideal.ieai.core.util.graph.RectCell;
import com.ideal.ieai.core.util.graph.StructCell;
import com.ideal.ieai.core.util.graph.StructObject;
import com.ideal.ieai.studio.StudioRes;
import com.ideal.ieai.studio.util.io.ExFileFilter;
import com.ideal.ieai.studio.util.undo.graph.InsertEdgeUndo;
import com.ideal.ieai.studio.util.undo.graph.InsertSpecialStartUndo;
import com.ideal.ieai.studio.util.undo.graph.InsertStructUndo;
import com.ideal.ieai.studio.util.undo.graph.InsertVertexUndo;
import com.ideal.ieai.studio.util.view.FilePreviewer;
import com.ideal.ieai.studio.view.editorui.acteditor.GraphIcon;

public class FlowGraphHelper
{
    /**
     * insert cell to slect position
     * 
     * @param group
     * @param vertex
     * @param Icn
     * @param Pt
     * @param pane
     * @param needUndoSupport
     * @return
     */
    public static DefaultGraphCell insertVertex ( StructCell group, DefaultGraphCell vertex,
            Icon Icn, Point2D Pt, FlowChartPane pane, boolean needUndoSupport )
    {
        JGraph Graph = pane.getGraph();
        if (Graph.isGridEnabled())
        {
            Pt = Graph.snap(Pt);
        }
        Map attr = vertex.getAttributes();
        if (null != Icn)
        {
            GraphConstants.setIcon(attr, Icn);
        }
        GraphConstants.setSizeable(attr, false);
        GraphConstants.setAutoSize(attr, true);
        Dimension dm = new Dimension(34 + Icn.getIconWidth(), Icn.getIconHeight());
        Rectangle rect = new Rectangle((int) Pt.getX(), (int) Pt.getY(), (int) dm.getWidth(),
                (int) dm.getWidth());
        GraphConstants.setBounds(attr, rect);
        // add port
        vertex.add(new DefaultPort());

        ParentMap parentMap = null;
        // set act name id
        BasicCellObject userObj = (BasicCellObject) vertex.getUserObject();
        BasicActElement act = userObj.getElement();
        // get current flow idList
        java.util.List idList = FlowGraphHelper.getFlowActIDs(userObj.getFLow());
        act.setActID(getActIdbyName(idList, act.getName()));

        // the vertex insert to group
        if (group != null)
        {
            parentMap = new ParentMap();
            // set act parent entry flag
            act.setParentEntryFlag(group.getEntryElement().getID());
            parentMap.addEntry(vertex, group);
        }
      
        // insert vertex
        pane.getGraphUndoManager().isAddEdit(false);
        // here this edit should not add to undomanager
        Graph.getGraphLayoutCache().insert(new Object[] { vertex }, null, null, parentMap, null);
        pane.getGraphUndoManager().isAddEdit(true);
        if (needUndoSupport)
        {
            // create insertVertexUndo edit
            InsertVertexUndo edit = new InsertVertexUndo(pane, vertex);
            pane.getGraphUndoManager().addEdit(edit);
        }
        return vertex;
    }

    /**
     * insert vertex undo action
     * 
     * @param pane
     * @param parentStruct cell insert to parentStruct
     * @param cell inserted cell
     */
    public static void insertVertexUndo ( FlowChartPane pane, DefaultGraphCell cell )
    {
        pane.getGraphUndoManager().isAddEdit(false);

        List list = new ArrayList();
        list.add(cell);
        removeVertex(pane, list);

        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * insert vertex redo action
     * 
     * @param pane
     * @param parentStruct cell insert to parentStruct
     * @param cell inserted cell
     */
    public static void insertVertexRedo ( FlowChartPane pane, DefaultGraphCell cell )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        List list = new ArrayList();
        list.add(cell);
        insertVertex(pane, list);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * get struct all children
     * 
     * @param struct StructCell
     * @return List
     */
    public static java.util.List getStructAllChildren ( StructCell struct )
    {
        java.util.List list = new ArrayList();
        for (int i = 0, size = struct.getChildCount(); i < size; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) struct.getChildAt(i);
            if (cell instanceof StructCell)
            {
                list.add(cell);
                list.addAll(getStructAllChildren((StructCell) cell));
            } else
            {
                list.add(cell);
            }
        }
        return list;
    }

    /**
     * For DefaultGraphCell select icon chooser.
     * 
     * @return
     */
    public static JFileChooser getIconChooser ()
    {
        if (null == iconChooser)
        {
            iconChooser = new JFileChooser();
            FilePreviewer previewer = new FilePreviewer(iconChooser);
            iconChooser.setAccessory(previewer);
            ExFileFilter filter = new ExFileFilter();
            filter.addExtension("gif");
            filter.addExtension("jpg");
            filter.addExtension("png");
            iconChooser.removeChoosableFileFilter(iconChooser.getFileFilter());
            iconChooser.addChoosableFileFilter(filter);
        }
        return iconChooser;
    }

    /**
     * update cell size and update the cell connection edges length too
     * 
     * @param _graph JGraph
     * @param TheCell DefaultGraphCell
     */
    public static void updateCellSize ( JGraph Graph, DefaultGraphCell TheCell )
    {
        if (TheCell.getUserObject() instanceof BasicCellObject)
        { // set auto size true
            BasicCellObject obj = (BasicCellObject) TheCell.getUserObject();
            Map attr = new HashMap();
            GraphConstants.setAutoSize(attr, true);
            GraphConstants.setBounds(attr, Graph.getCellBounds(TheCell));
            if(obj.getElement().isImpo())
            {
            	Border border = BorderFactory.createEtchedBorder(Color.RED, Color.RED);
            	GraphConstants.setBorder(attr, border);
            	GraphConstants.setBorderColor(attr, Color.RED);
            }
            else
            	GraphConstants.setBorder(attr, BorderFactory.createEmptyBorder());
            obj.getFlowChartPane().getGraphUndoManager().isAddEdit(false);
            obj.getFlowChartPane().getGraph().getModel().removeGraphModelListener(
                obj.getFlowChartPane());

            Graph.getGraphLayoutCache().editCell(TheCell, attr);
            obj.getFlowChartPane().getGraphUndoManager().isAddEdit(true);
            obj.getFlowChartPane().getGraph().getModel().addGraphModelListener(
                obj.getFlowChartPane());
        }
    }

    /**
     * get identity act name flag
     * 
     * @param FlagsList List flags list
     * @param TheName String the act name
     * @return String the act flag name
     */
    public static String getActIdbyName ( java.util.List IDList, String TheName )
    {
        // remove useless " ",if repeat " ",only get one " "
        TheName = GraphHelper.formatString(TheName);

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < TheName.length(); i++)
        {
            char ch = TheName.charAt(i);
            if (!Character.isUnicodeIdentifierPart(ch))
            {
                // replace "_" instanceof " "
                if (Character.isSpaceChar(ch))
                {
                    sb.append(ProjectConstants.SUFFIX);
                }
            } else
            {
                sb.append(TheName.charAt(i));
            }
        }
        // the name can not be "env" or "syscontext"
        if (TheName.equals(ProjectConstants.ENV) || TheName.equals(ProjectConstants.SYSCONTEXT))
        {
            sb.append(ProjectConstants.SUFFIX);
        }

        String nameWithoutSpace = sb.toString();
        String actID = nameWithoutSpace;
        if (actID.equals(""))
        {
            actID = ProjectConstants.SUFFIX;
        }
        for (int i = 0; true; i++)
        {
            if (i != 0)
            {
                actID = nameWithoutSpace + ProjectConstants.SUFFIX + i;
            }
            if (!IDList.contains(actID))
            {
                return actID;
            }
        }
    }

    /**
     * get act name by default name
     * 
     * @param Flow Workflow
     * @param Elm BasicActElement
     * @param DefaultName String
     * @return String
     */
    public static String getActName ( Workflow Flow, BasicActElement Elm, String DefaultName )
    {
        java.util.List lst = Flow.getActivities();
        java.util.List acts = new ArrayList();
        String actName;
        for (int i = 0, size = lst.size(); i < size; i++)
        {
            Object obj = lst.get(i);
            if (Elm instanceof EndActElement && obj instanceof EndActElement)
            {
                EndActElement elm = (EndActElement) obj;
                acts.add(elm.getName());
            } else if (Elm instanceof CallWorkflowActElement
                    && obj instanceof CallWorkflowActElement)
            {
                CallWorkflowActElement elm = (CallWorkflowActElement) obj;
                acts.add(elm.getName());
            } else if (Elm instanceof ExceptionHandlerActElement
                    && obj instanceof ExceptionHandlerActElement)
            {
                ExceptionHandlerActElement elm = (ExceptionHandlerActElement) obj;
                acts.add(elm.getName());
            }
        }

        for (int i = 0; true; i++)
        {
            if (i == 0)
            {
                actName = DefaultName;
            } else
            {
                actName = DefaultName + ProjectConstants.SUFFIX + i;
            }
            if (!acts.contains(actName))
            {
                return actName;
            }
        }
    }

    /**
     * insert special starter act to flow.
     * 
     * @param pane
     * @param actDef
     * @param ic
     * @param pt
     */
    public static void insertSpecialStart ( FlowChartPane pane, ActivityDef actDef, Icon ic,
            Point pt )
    {
        // it is replace stander start to flow starter edit
        DefaultGraphCell oldStart = getCellByKey(pane.getGraph(), 0);
        // deal user object
        BasicCellObject oldObj = (BasicCellObject) oldStart.getUserObject();
        ActivityElement elm = new ActivityElement(0, actDef, pane.getWorkflow().getID());
        elm.setName(Workflow.START_NAME);
        // set exception act id
        elm.setExceptionActID(oldObj.getElement().getExceptionHandlerActID());
        // should get idList and remove original start act act id
        List idList = FlowGraphHelper.getFlowActIDs(oldObj.getFLow());
        idList.remove(oldObj.getElement().getActID());
        elm.setActID(FlowGraphHelper.getActIdbyName(idList, elm.getName()));

        // remove the old object element in flow activities
        pane.getWorkflow().getActivities().remove(oldObj.getElement());
        pane.getWorkflow().getActivities().add(elm);

        // create new user object
        BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), elm, pane);
        // create new graph cell
        DefaultGraphCell newStart = new DefaultGraphCell();
        // set user object
        newStart.setUserObject(ob);

        Map attr = newStart.getAttributes();
        Dimension dm = new Dimension(34 + ic.getIconWidth(), 12 + ic.getIconHeight());
        Rectangle rect = new Rectangle(pt.x, pt.y, (int) dm.getWidth(), (int) dm.getWidth());
        GraphConstants.setIcon(attr, ic);
        GraphConstants.setSize(attr, dm);
        GraphConstants.setConnectable(attr, true);
        GraphConstants.setSizeable(attr, false);
        GraphConstants.setBounds(attr, rect);
        GraphConstants.setEditable(attr, true);
        // create new port
        DefaultPort newPort = new DefaultPort();
        newStart.add(newPort);

        // get start act connect edges.
        java.util.List edgesList = getNotRelationEdge(new Object[] { oldStart }, pane.getGraph());
        // change edge and port edge and source info
        for (int i = 0, size = edgesList.size(); i < size; i++)
        {
            DefaultEdge eg = (DefaultEdge) edgesList.get(i);
            newPort.addEdge(eg);
            eg.setSource(newPort);

            // resume pre and post activities info.
            DefaultGraphCell target = (DefaultGraphCell) ((DefaultPort) eg.getTarget()).getParent();
            removeSuccessingAndPreActsList(oldStart, target);
            addSuccessingAndPreActsList(newStart, target);
        }
        // create insert special start undo edit
        InsertSpecialStartUndo edit = new InsertSpecialStartUndo(pane, oldStart, newStart,
                edgesList);
        pane.getGraphUndoManager().addEdit(edit);

        pane.getGraphUndoManager().isAddEdit(false);
        // remove old start
        pane.getGraph().getGraphLayoutCache().remove(new Object[] { oldStart });
        // insert new start
        pane.getGraph().getGraphLayoutCache().insert(new Object[] { newStart }, null, null, null);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * Here oldCell have no child(port) info,newPort have edges info,should move newPort edges to
     * oldPort
     * 
     * @param pane
     * @param oldCell old starter
     * @param newCell new starter
     * @param oldPort oldCell port
     * @param newPort newCell port
     */
    public static void insertSpecialStartUndo ( FlowChartPane pane, DefaultGraphCell oldCell,
            DefaultGraphCell newCell, List edgeList )
    {
        BasicCellObject oldObj = (BasicCellObject) oldCell.getUserObject();
        BasicCellObject newObj = (BasicCellObject) newCell.getUserObject();
        pane.getWorkflow().getActivities().remove(newObj.getElement());
        pane.getWorkflow().getActivities().add(oldObj.getElement());
        // get old cell port
        DefaultPort oldPort;
        if (oldCell.getChildCount() == 0)
        {
            oldPort = new DefaultPort();
            oldCell.add(oldPort);
        } else
        {
            oldPort = (DefaultPort) oldCell.getChildAt(0);
        }
        // change edges port info
        for (int i = 0, size = edgeList.size(); i < size; i++)
        {
            DefaultEdge edge = (DefaultEdge) edgeList.get(i);
            oldPort.addEdge(edge);
            edge.setSource(oldPort);

            // resume pre and post activities info.
            DefaultGraphCell target = (DefaultGraphCell) ((DefaultPort) edge.getTarget())
                    .getParent();
            removeSuccessingAndPreActsList(newCell, target);
            addSuccessingAndPreActsList(oldCell, target);
        }

        pane.getGraphUndoManager().isAddEdit(false);
        // remove old start
        pane.getGraph().getModel().remove(new Object[] { newCell });
        // insert new start
        pane.getGraph().getModel().insert(new Object[] { oldCell }, null, null, null, null);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * Here newCell have no child(port) info,oldPort have edges info,should move oldPort edges to
     * newPort
     * 
     * @param pane
     * @param oldCell old starter
     * @param newCell new starter
     * @param oldPort oldCell port
     * @param newPort newCell port
     */
    public static void insertSpecialStartRedo ( FlowChartPane pane, DefaultGraphCell oldCell,
            DefaultGraphCell newCell, List edgeList )
    {
        BasicCellObject oldObj = (BasicCellObject) oldCell.getUserObject();
        BasicCellObject newObj = (BasicCellObject) newCell.getUserObject();
        pane.getWorkflow().getActivities().remove(oldObj.getElement());
        pane.getWorkflow().getActivities().add(newObj.getElement());

        DefaultPort newPort;
        if (newCell.getChildCount() == 0)
        {
            newPort = new DefaultPort();
            newCell.add(newPort);
        } else
        {
            newPort = (DefaultPort) newCell.getChildAt(0);
        }

        for (int i = 0, size = edgeList.size(); i < size; i++)
        {
            DefaultEdge edge = (DefaultEdge) edgeList.get(i);
            newPort.addEdge(edge);
            edge.setSource(newPort);

            // resume pre and post activities info.
            DefaultGraphCell target = (DefaultGraphCell) ((DefaultPort) edge.getTarget())
                    .getParent();
            removeSuccessingAndPreActsList(oldCell, target);
            addSuccessingAndPreActsList(newCell, target);
        }
        pane.getGraphUndoManager().isAddEdit(false);
        // remove old start
        pane.getGraph().getModel().remove(new Object[] { oldCell });
        // insert new start
        pane.getGraph().getModel().insert(new Object[] { newCell }, null, null, null, null);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * delete the child nodes when select cells contains it parent cell
     * 
     * @param graph JGraph
     * @param TheCells the cells
     * @return List the list which not contain if have parent cell,not have child cell,if cells have
     *         not relation
     */
    public static java.util.List delChildWhenParentSelected ( JGraph graph, Object[] TheCells )
    {
        java.util.List selList = new ArrayList();
        selList.addAll(Arrays.asList(TheCells));
        for (int i = 0; i < TheCells.length; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) TheCells[i];
            if (!(selList.contains(cell)))
            {
                continue;
            }
            if (cell instanceof StructCell)
            {
                List childList = FlowGraphHelper.getStructAllChildren((StructCell) cell);
                selList.removeAll(childList);
                selList.addAll(FlowGraphHelper.getRelationEdge(childList.toArray(), graph));
            }
        }
        return selList;
    }

    /**
     * insert a comment cell
     * 
     * @param FlowPane
     * @param Graph
     * @param Pt
     * @return
     */
    public static CommentCell insertCommentCell ( StructCell group, FlowChartPane pane,
            JGraph Graph, Point2D Pt )
    {
        CommentCell vertex = new CommentCell();
        CommentElement commentElm = pane.getWorkflow().createCommentElement("Label");
        BasicHelperObject hObj = new BasicHelperObject(pane.getWorkflow(), commentElm, pane);
        vertex.setUserObject(hObj);
        if (Graph.isGridEnabled())
        {
            Pt = Graph.snap(Pt);
        }
        Map attr = vertex.getAttributes();
        GraphConstants.setBounds(attr, new Rectangle((int) Pt.getX(), (int) Pt.getY(), 50, 50));
        GraphConstants.setHorizontalTextPosition(attr, SwingConstants.LEFT);
        GraphConstants.setVerticalTextPosition(attr, SwingConstants.TOP);
        GraphConstants.setVerticalAlignment(attr, SwingConstants.TOP);
        // add to group
        ParentMap parentMap = null;
        if (group != null)
        {
            parentMap = new ParentMap();
            if (vertex.getUserObject() instanceof BasicHelperObject)
            {
                if (null != group.getEntryElement())
                {
                    ((BasicHelperObject) vertex.getUserObject()).getElement().setParentEntryFlag(
                        group.getEntryElement().getID());
                    parentMap.addEntry(vertex, group);
                }
            }
        }

        InsertVertexUndo edit = new InsertVertexUndo(pane, vertex);
        pane.getGraphUndoManager().addEdit(edit);

        pane.getGraphUndoManager().isAddEdit(false);
        Graph.getModel().insert(new Object[] { vertex }, null, null, parentMap, null);
        pane.getGraphUndoManager().isAddEdit(true);
        return vertex;
    }

    /**
     * insert ellipse assist element to graph
     * 
     * @param flowPane
     * @param Graph
     * @param start
     * @param end
     * @return
     */
    public static EllipseCell insertEllipseCell ( StructCell group, FlowChartPane pane,
            Point2D start, Point2D end )
    {
        Dimension dm = new Dimension(Math.abs((int) (end.getX() - start.getX())), Math
                .abs((int) (end.getY() - start.getY())));
        EllipseCell vertex = new EllipseCell();
        AssistElement elem = pane.getWorkflow().createAssistElement();
        BasicHelperObject hObj = new BasicHelperObject(pane.getWorkflow(), elem, pane);
        elem.setIsRectangle(false);
        vertex.setUserObject(hObj);

        Map attr = vertex.getAttributes();
        GraphConstants.setSize(attr, dm);
        GraphConstants.setSizeable(attr, true);
        GraphConstants.setOpaque(attr, false);
        GraphConstants.setEditable(attr, false);
        GraphConstants.setBorderColor(attr, Color.BLUE);
        GraphConstants.setMoveable(attr, true);
        int x = (int) (start.getX() < end.getX() ? start.getX() : end.getX());
        int y = (int) (start.getY() < end.getY() ? start.getY() : end.getY());
        Point2D pt = pane.getGraph().snap(new Point(x, y));
        Point p = new Point((int) pt.getX(), (int) pt.getY());
        GraphConstants.setBounds(attr, new Rectangle(p, dm));

        ParentMap parentMap = null;
        // add to group
        if (group != null)
        {
            parentMap = new ParentMap();
            if (vertex.getUserObject() instanceof BasicHelperObject)
            {
                if (null != group.getEntryElement())
                {
                    ((BasicHelperObject) vertex.getUserObject()).getElement().setParentEntryFlag(
                        group.getEntryElement().getID());
                    parentMap.addEntry(vertex, group);
                }
            }
        }
        Object[] arg = new Object[] { vertex };

        InsertVertexUndo edit = new InsertVertexUndo(pane, vertex);
        pane.getGraphUndoManager().addEdit(edit);

        pane.getGraphUndoManager().isAddEdit(false);
        pane.getGraph().getModel().insert(arg, null, null, parentMap, null);
        pane.getGraphUndoManager().isAddEdit(true);
        return vertex;
    }

    /**
     * insert rectcell
     * 
     * @param pane
     * @param Graph
     * @param start
     * @param end
     * @return
     */
    public static DefaultGraphCell insertRectCell ( StructCell group, FlowChartPane pane,
            JGraph Graph, Point2D start, Point2D end )
    {
        Dimension dm = new Dimension(Math.abs((int) (end.getX() - start.getX())), Math
                .abs((int) (end.getY() - start.getY())));
        RectCell vertex = new RectCell();
        AssistElement assistElm = pane.getWorkflow().createAssistElement();
        BasicHelperObject hObj = new BasicHelperObject(pane.getWorkflow(), assistElm, pane);
        vertex.setUserObject(hObj);
        assistElm.setIsRectangle(true);

        Map attr = vertex.getAttributes();

        GraphConstants.setSize(attr, dm);
        GraphConstants.setSizeable(attr, true);
        GraphConstants.setOpaque(attr, false);
        GraphConstants.setEditable(attr, false);
        GraphConstants.setBorderColor(attr, Color.BLUE);
        GraphConstants.setMoveable(attr, true);
        int x = (int) (start.getX() < end.getX() ? start.getX() : end.getX());
        int y = (int) (start.getY() < end.getY() ? start.getY() : end.getY());
        Point2D pt = Graph.snap(new Point(x, y));
        GraphConstants.setBounds(attr, new Rectangle((int) pt.getX(), (int) pt.getY(), (int) dm
                .getWidth(), (int) dm.getHeight()));

        ParentMap parentMap = null;
        if (group != null)
        {
            parentMap = new ParentMap();
            if (vertex.getUserObject() instanceof BasicHelperObject)
            {
                if (null != group.getEntryElement())
                {
                    ((BasicHelperObject) vertex.getUserObject()).getElement().setParentEntryFlag(
                        group.getEntryElement().getID());
                    parentMap.addEntry(vertex, group);
                }
            }
        }
        InsertVertexUndo edit = new InsertVertexUndo(pane, vertex);
        pane.getGraphUndoManager().addEdit(edit);

        pane.getGraphUndoManager().isAddEdit(false);
        Graph.getModel().insert(new Object[] { vertex }, null, null, parentMap, null);
        pane.getGraphUndoManager().isAddEdit(true);
        return vertex;
    }

    /**
     * get one cell connect edges
     * 
     * @param TheCell DefaultGraphCell
     * @param Graph JGraph
     * @return List
     */
    private static java.util.List getCellEdges ( DefaultGraphCell TheCell, JGraph Graph,
            java.util.List EdgeList )
    {
        Object b;
        DefaultPort ob;
        DefaultGraphModel model = (DefaultGraphModel) Graph.getModel();
        for (int j = 0; j < model.getChildCount(TheCell); j++)
        {
            b = model.getChild(TheCell, j);
            // get the default port of a vertex.
            if (b instanceof Port)
            {
                ob = (DefaultPort) b;
                // remove all edge of the port
                for (Iterator it = ob.getEdges().iterator(); it.hasNext();)
                {
                    Edge edge = (Edge) it.next();
                    if (!EdgeList.contains(edge))
                    {
                        EdgeList.add(edge);
                    }
                }
            }
        } // end of for
        return EdgeList;
    }

    /**
     * get Cells connect edge List,if cells contain edge,should add to return list,if the cell is
     * vertx,should add connect edges to return list
     * 
     * @param Graph
     * @param Cells
     * @return
     */
    public static List getCellConnectEdgeList ( Object[] Cells, JGraph Graph )
    {
        Object[] descCells = Graph.getDescendants(Cells);
        java.util.List edgesList = new ArrayList();
        java.util.List cellsLst = new ArrayList();
        for (int i = 0, size = descCells.length; i < size; i++)
        {
            if (descCells[i] instanceof DefaultEdge)
            {
                edgesList.add(descCells[i]);
            } else if (descCells[i] instanceof DefaultGraphCell)
            {
                cellsLst.add(descCells[i]);
                edgesList = getCellEdges((DefaultGraphCell) descCells[i], Graph, edgesList);
            }
        }
        return edgesList;
    }

    /**
     * get edges which source and target cell all in the Cells
     * 
     * @param Cells Object[]
     * @param FlowPane FlowChartPane
     * @return List edges
     */
    public static java.util.List getRelationEdge ( Object[] Cells, JGraph Graph )
    {
        // get all edges contain if cell is vertex connected edges
        java.util.List edgesList = getCellConnectEdgeList(Cells, Graph);
        // get all vertex
        java.util.List vertexList = getVertexList(Graph.getDescendants(Cells));
        java.util.List relationLst = new ArrayList();
        for (int i = 0, size = edgesList.size(); i < size; i++)
        {
            Edge edge = (Edge) edgesList.get(i);
            DefaultPort sourcePort = (DefaultPort) edge.getSource();
            DefaultPort targetPort = (DefaultPort) edge.getTarget();
            DefaultGraphCell cell1 = (DefaultGraphCell) sourcePort.getParent();
            DefaultGraphCell cell2 = (DefaultGraphCell) targetPort.getParent();
            if (vertexList.contains(cell1) && vertexList.contains(cell2))
            {
                DefaultEdge defaultEdge = (DefaultEdge) edge;
                if (!relationLst.contains(defaultEdge))
                {
                    relationLst.add(defaultEdge);
                }
            }
        }
        return relationLst;
    }

    /**
     * get the edges which source or target cell is not in the cells
     * 
     * @param cells Object[]
     * @param model GraphModel
     * @return List each element is Edge instance
     */
    public static java.util.List getNotRelationEdge ( Object[] cells, JGraph graph )
    {
        // get all edges contain if cell is vertex connected edges
        java.util.List edgesList = getCellConnectEdgeList(cells, graph);
        // get all relation edges
        java.util.List relationLst = getRelationEdge(cells, graph);

        edgesList.removeAll(relationLst);
        return edgesList;
    }

    /**
     * Add a edge between two jgraph cells
     * 
     * @param OrigPort the origin port
     * @param TargPort the target port
     */
    public static Edge insertEdge ( FlowChartPane pane, DefaultEdge edge, DefaultGraphCell origin,
            DefaultGraphCell target, int lineStyle )
    {
        FlowEdgeInfo edgeInfo = new FlowEdgeInfo(((BasicCellObject) origin.getUserObject())
                .getElement().getID(), ((BasicCellObject) target.getUserObject()).getElement()
                .getID());
        if (null == edge)
        {
            edge = new DefaultEdge();
        }
        JGraph graph = pane.getGraph();
        Port sp = (Port) origin.getChildAt(0);
        Port tp = (Port) target.getChildAt(0);
        // set edge source and target
        edge.setSource(sp);
        edge.setTarget(tp);

        Map attr = edge.getAttributes();
        GraphConstants.setLineEnd(attr, GraphConstants.ARROW_TECHNICAL);
        GraphConstants.setEndFill(attr, true);

        GraphConstants.setLineWidth(attr, 1);
        edgeInfo.setLineStyle(ConnectState.STYLE_BEELINE);
        if (-1 != lineStyle)
        {
            GraphConstants.setLineStyle(attr, lineStyle);
            GraphConstants.setRouting(attr, GraphConstants.ROUTING_SIMPLE);
            edgeInfo.setLineStyle(lineStyle);
        }

        GraphConstants.setBendable(attr, true);
        if (null == edge.getUserObject() || edge.getUserObject().equals(""))
        {
            // set user object
            edge.setUserObject(edgeInfo);
        }
        addSuccessingAndPreActsList(origin, target);

        InsertEdgeUndo edit = new InsertEdgeUndo(pane, edge);
        pane.getGraphUndoManager().addEdit(edit);

        pane.getGraphUndoManager().isAddEdit(false);
        graph.getGraphLayoutCache().insert(new Object[] { edge });
        pane.getGraphUndoManager().isAddEdit(true);
        return edge;
    }

    public static void insertEdgeUndo ( FlowChartPane pane, DefaultEdge edge )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        List edgeList = new ArrayList();
        edgeList.add(edge);
        removeEdge(pane, edgeList);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    public static void insertEdgeRedo ( FlowChartPane pane, DefaultEdge edge )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        List edgeList = new ArrayList();
        edgeList.add(edge);
        insertEdge(pane, edgeList);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * insert struct cell undo action
     * 
     * @param pane
     * @param struct inserted struct
     * @param entryCell entry cell
     * @param exitCell exit cell
     */
    public static void insertStructCellUndo ( FlowChartPane pane, StructCell struct,
            DefaultGraphCell entryCell, DefaultGraphCell exitCell )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        List list = new ArrayList();
        list.add(struct);
        list.add(entryCell);
        list.add(exitCell);
        removeVertex(pane, list);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * insert struct cell redo action
     * 
     * @param pane
     * @param parentStruct insert struct to parent struct cell
     * @param struct inserted struct
     * @param entryCell entry cell
     * @param exitCell exit cell
     */
    public static void insertStructCellRedo ( FlowChartPane pane, StructCell struct,
            DefaultGraphCell entryCell, DefaultGraphCell exitCell )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        List list = new ArrayList();
        list.add(struct);
        list.add(entryCell);
        list.add(exitCell);
        insertVertex(pane, list);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * @param pt insert strcut point
     * @structType loop sequence or syn type
     * @pane FlowChartPane instance
     */
    public static void insertStructCell ( FlowChartPane pane, StructCell group, int structType,
            Point2D pt )
    {
        int x = (int) pt.getX();
        int y = (int) pt.getY();
        DefaultGraphCell entryCell = insertEntryCell(new Point(x + 5, y + 5), pane, structType);
        DefaultGraphCell exitCell = insertExitCell(new Point(x + 75, y + 75), entryCell, pane,
            structType);

        EntryActElement sEntry = (EntryActElement) ((BasicCellObject) entryCell.getUserObject())
                .getElement();
        // set entry act element exit id
        BasicActElement sExit = ((BasicCellObject) exitCell.getUserObject()).getElement();
        sEntry.setExitId(sExit.getID());

        // set struct cell user object
        StructObject obj = new StructObject(sEntry);
        StructCell struct = new StructCell();
        struct.setUserObject(obj);

        Map attr = struct.getAttributes();
        GraphConstants.setOpaque(attr, false);
        GraphConstants.setBorderColor(attr, Color.BLUE);
        GraphConstants.setSizeable(attr, false);
        GraphConstants.setEditable(attr, true);

        ParentMap parentMap = new ParentMap();
        // Insert Child Parent Entries
        parentMap.addEntry(entryCell, struct);
        parentMap.addEntry(exitCell, struct);

        // crate insert struct undo edit
        InsertStructUndo edit = new InsertStructUndo(pane, struct, entryCell, exitCell);
        pane.getGraphUndoManager().addEdit(edit);
        /**
         * insert struct to graph
         */
        JGraph graph = pane.getGraph();
        pane.getGraphUndoManager().isAddEdit(false);
        graph.getGraphLayoutCache().insert(new Object[] { struct }, null, null, parentMap, null);

        if (null != group)
        {
            Rectangle rect = graph.getCellBounds(
                graph.getDescendants(new Object[] { struct, group })).getBounds();
            // get group entry element
            EntryActElement groupEntry = group.getEntryElement();
            // change view model
            parentMap = new ParentMap();
            parentMap.addEntry(struct, group);
            Map groupAttr = new HashMap();
            // GraphConstants.createMap();
            GraphConstants.setBounds(groupAttr, rect);
            Map nest = new Hashtable();
            nest.put(group, groupAttr);
            graph.getModel().edit(nest, null, parentMap, null);
            // change parent entry flag
            EntryActElement entry = struct.getEntryElement();
            ExitActElement exit = struct.getExitElement();
            long parentId = groupEntry.getID();
            entry.setParentEntryFlag(parentId);
            exit.setParentEntryFlag(parentId);
        }
        pane.getGraphUndoManager().isAddEdit(true);

        // add the struct to the struct list
        java.util.List structList = pane.getStructList();
        if (!structList.contains(struct))
        {
            structList.add(struct);
        }
    }

    /**
     * for click struct blank place slect the struct cell
     * 
     * @param Pt Point click point
     * @param Cells List
     * @return StructCell
     */
    public static StructCell getStructByPos ( Point2D Pt, FlowChartPane Pane )
    {
        // the list of all struct cell.
        java.util.List structList = Pane.getStructList();
        StructCell cel = null;
        java.util.List groupList = new ArrayList();

        Rectangle rect = null;
        for (int i = 0; i < structList.size(); i++)
        {
            cel = (StructCell) structList.get(i);
            rect = Pane.getGraph().getCellBounds(new Object[] { cel }).getBounds();
            if (null == rect)
            {
                structList.remove(cel);
                continue;
            }
            // get the struct cell that contains the _start position.
            if (rect.contains(Pane.getGraph().fromScreen(Pt)))
            {
                groupList.add(cel);
            }
        }
        StructCell group = null;
        Rectangle groupRect = null;
        for (int i = 0, size = groupList.size(); i < size; i++)
        {
            StructCell cell = (StructCell) groupList.get(i);
            rect = Pane.getGraph().getCellBounds(
                Pane.getGraph().getDescendants(new Object[] { cell })).getBounds();
            if (group != null)
            {
                groupRect = Pane.getGraph().getCellBounds(
                    Pane.getGraph().getDescendants(new Object[] { group })).getBounds().getBounds();
                // get area smallest struct cell
                group = groupRect.height * groupRect.width < rect.height * rect.width ? group
                        : cell;
            } else
            {
                group = cell;
            }
        }
        return group;
    }

    /**
     * get the struct by the entry element
     * 
     * @param Entry
     * @return
     */
    static public StructCell getStructCellByEntry ( EntryActElement Entry, FlowChartPane Pane )
    {
        for (int i = 0, size = Pane.getStructList().size(); i < size; i++)
        {
            StructCell struct = (StructCell) Pane.getStructList().get(i);
            if (null != struct && struct.getEntryElement().equals(Entry))
            {
                return struct;
            }
        }
        return null;
    }

    /**
     * file the struct or helper cell color
     * 
     * @param Graph JGraph
     * @param Cell DefaultGraphCell
     * @param Col Color
     */
    public static void fillColor ( JGraph Graph, DefaultGraphCell Cell, Color Col, int ColorType )
    {
        if (!(Cell.getUserObject() instanceof BasicHelperObject || Cell instanceof StructCell || Cell instanceof Edge))
        {
            return;
        }
        Map map = new HashMap();
        Map nested = new HashMap();
        if (ColorType == COLOR_TYPE_BORDER)
        {
            if (null == Col)
            {
                GraphConstants.setBorderColor(map, GraphHelper.COLOR_TRANSPARENCE);
            } else
            {
                GraphConstants.setBorderColor(map, Col);
            }
            nested.put(Cell, map);
        } else if (ColorType == COLOR_TYPE_FILL)
        {
            if (null == Col)
            {
                GraphConstants.setOpaque(map, false);
                map.remove(GraphConstants.BACKGROUND);
            } else
            {
                GraphConstants.setOpaque(map, true);
                GraphConstants.setBackground(map, Col);
            }
            nested.put(Cell, map);
            // if current cell is struct cell ,should fill the struct cell and
            // helper of it children
            if (Cell instanceof StructCell)
            {
                java.util.List list = getStructAllChildren((StructCell) Cell);
                for (int i = 0, size = list.size(); i < size; i++)
                {
                    DefaultGraphCell cel = (DefaultGraphCell) list.get(i);
                    map = cel.getAttributes();
                    if (cel instanceof StructCell)
                    {
                        nested.put(cel, map);
                    } else if (cel.getUserObject() instanceof HelperObject)
                    {
                        nested.put(cel, map);
                    }
                }
            }
        }
        Graph.getModel().edit(nested, null, null, null);
    }

    /**
     * set cell font attribute ,like size,color and so on
     * 
     * @param Graph JGraph
     * @param cell DefaultGraphCell need to change font cell
     * @param font Font the cell font
     * @param color Color the cell font color
     */
    public static void setCellFontAtt ( FlowChartPane pane, DefaultGraphCell cell, Font font,
            Color color )
    {
        Map attr = new HashMap();
        if (null != font)
        {
            GraphConstants.setFont(attr, font);
        }
        if (null != color)
        {
            GraphConstants.setForeground(attr, color);
        }
        Map nested = new Hashtable();
        nested.put(cell, attr);

        pane.getGraphUndoManager().isAddEdit(false);
        pane.getGraph().getGraphLayoutCache().edit(nested, null, null, null);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    static public void snapCells ( JGraph Graph )
    {
        // java.util.List cells = getAllCells(Graph);
        Object[] cells = Graph.getRoots();
        for (int i = 0, size = cells.length; i < size; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) cells[i];
            if (cell instanceof Edge || cell instanceof Port)
            {
                continue;
            }
            cellEdited(Graph, cell);
            Enumeration child = cell.breadthFirstEnumeration();
            while (child.hasMoreElements())
            {
                cellEdited(Graph, (DefaultGraphCell) child.nextElement());
            }
        }
    }

    // static public void setEdgeStyle(JGraph Graph, int EdgeStyle)
    // {
    // Object[] cells = Graph.getRoots();
    // for (int i = 0, size = cells.length; i < size; i++)
    // {
    // DefaultGraphCell cell = (DefaultGraphCell) cells[i];
    // if (cell instanceof Edge)
    // {
    // edgeEdited(Graph, (DefaultEdge) cell, EdgeStyle);
    // }
    // else if (cell instanceof Port)
    // {
    // continue;
    // }
    // else
    // {
    // Enumeration child = cell.breadthFirstEnumeration();
    // while (child.hasMoreElements())
    // {
    // Object obj = child.nextElement();
    // if (obj instanceof Edge)
    // {
    // edgeEdited(Graph, (DefaultEdge) obj, EdgeStyle);
    // }
    // }
    // }
    // }
    //
    // }
    //
    // private static void edgeEdited(JGraph Graph, DefaultEdge TheEdge, int
    // EdgeStyle)
    // {
    // if (null == TheEdge || ! (TheEdge instanceof Edge))
    // {
    // return;
    // }
    // Map attr = TheEdge.getAttributes();
    // if ( -1 != EdgeStyle)
    // {
    // GraphConstants.setLineStyle(attr, EdgeStyle);
    // GraphConstants.setRouting(attr, GraphConstants.ROUTING_SIMPLE);
    // }
    // else
    // {
    // attr.remove(GraphConstants.LINESTYLE);
    // attr.remove(GraphConstants.ROUTING);
    // }
    // Map nested = new Hashtable();
    // nested.put(TheEdge, GraphConstants.cloneMap(attr));
    // Graph.getGraphLayoutCache().edit(nested, null, null, null);
    // }

    private static void cellEdited ( JGraph Graph, DefaultGraphCell Cell )
    {
        if (null == Cell || Cell instanceof Port || Cell instanceof Edge)
        {
            return;
        }
        Rectangle rect = GraphConstants.getBounds(Cell.getAttributes()).getBounds();
        Point2D pt = Graph.snap(new Point(rect.x, rect.y));
        Map map = new HashMap();
        Point p = new Point((int) pt.getX(), (int) pt.getY());
        GraphConstants.setBounds(map, new Rectangle(p, rect.getSize()));
        Map nested = new Hashtable();
        nested.put(Cell, map);
        Graph.getGraphLayoutCache().edit(nested, null, null, null);
    }

    // static public void zoomCells(JGraph Graph, double Scale)
    // {
    // java.util.List cells = getAllCells(Graph);
    // for (int i = 0, size = cells.size(); i < size; i++)
    // {
    // DefaultGraphCell cell = (DefaultGraphCell) cells.get(i);
    // Rectangle rect = GraphConstants.getBounds(cell.getAttributes());
    // Point pt = new Point( (int) (rect.x * Scale), (int) (rect.y * Scale));
    // Dimension dm = rect.getSize();
    // Map map = GraphConstants.createMap();
    // GraphConstants.setBounds(map, new Rectangle(pt, dm));
    // map = GraphConstants.cloneMap(map);
    // Map nested = new Hashtable();
    // nested.put(cell, GraphConstants.cloneMap(map));
    // Graph.getGraphLayoutCache().edit(nested, null, null, null);
    // }
    // }

    // private static java.util.List getAllCells(JGraph Graph)
    // {
    // java.util.List cellLst = new ArrayList();
    // DefaultGraphModel model = (DefaultGraphModel) Graph.getModel();
    // for (int i = 0, size = model.getRootCount(); i < size; i++)
    // {
    // Object root = model.getRootAt(i);
    // if (root instanceof Edge || root instanceof Port)
    // {
    // continue;
    // }
    // cellLst.add(root);
    // getChildren(Graph, (DefaultGraphCell) root, cellLst);
    // }
    // return cellLst;
    // }
    //
    // private static void getChildren(JGraph Graph, DefaultGraphCell Cell,
    // java.util.List CellLst)
    // {
    // Enumeration e=Cell.breadthFirstEnumeration();
    // while(e.hasMoreElements())
    // {
    // CellLst.add(e.nextElement());
    // }
    // Iterator it = Cell.getChildren().iterator();
    // while (it.hasNext())
    // {
    // Object child = it.next();
    // if (child instanceof Edge || child instanceof Port)
    // {
    // continue;
    // }
    // CellLst.add(child);
    // getChildren(Graph, (DefaultGraphCell) child, CellLst);
    // }
    // }

    /**
     * get act cell by key
     * 
     * @param Cells
     * @param TheID
     * @return
     */
    public static DefaultGraphCell getActCellByKey ( Object[] Cells, long TheID )
    {
        for (int i = 0, length = Cells.length; i < length; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) Cells[i];
            Object obj = cell.getUserObject();
            if (obj instanceof BasicCellObject)
            {
                BasicActElement act = ((BasicCellObject) obj).getElement();
                if (act.getID() == TheID)
                {
                    return cell;
                }
            }
        }
        return null;
    }

    /**
     * througth the id ,find the cell in the Cells[]
     * 
     * @param Cells Object[] put defaultGraphCells
     * @param TheID int
     * @return DefaultGraphCell if the cell is in the Cells
     */
    public static DefaultGraphCell getCellByKey ( Object[] Cells, long TheID )
    {
        for (int i = 0, length = Cells.length; i < length; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) Cells[i];
            Object obj = cell.getUserObject();
            if (obj instanceof BasicCellObject)
            {
                BasicActElement act = ((BasicCellObject) obj).getElement();
                if (act.getID() == TheID)
                {
                    return cell;
                }
            } else if (obj instanceof HelperObject)
            {
                HelperElement elm = ((HelperObject) obj).getElement();
                if (elm instanceof CommentElement)
                {
                    if (((CommentElement) elm).getID() == TheID)
                    {
                        return cell;
                    }
                } else if (((AssistElement) elm).getID() == TheID)
                {
                    return cell;
                }
            }
        }
        return null;
    }

    /**
     * Get acts by regex name.
     * 
     * @param graph
     * @param regex
     * @return List each element is DefaultGraphCell which UserObject is BasicCellObject.
     */
    public static List getActCellsByName ( JGraph graph, String regex, boolean caseSensitive )
    {
        Object[] roots = graph.getDescendants(graph.getRoots());
        List cellList = new ArrayList();
        DefaultGraphCell cell;
        for (int i = 0, size = roots.length; i < size; i++)
        {
            if (!(roots[i] instanceof DefaultGraphCell))
            {
                continue;
            }
            cell = (DefaultGraphCell) roots[i];
            if (cell.getUserObject() instanceof BasicCellObject)
            {
                if (caseSensitive)
                {
                    if (((BasicCellObject) cell.getUserObject()).getElement().getName().matches(
                        regex))
                    {
                        cellList.add(cell);
                    }
                } else
                {// not case sensitive.
                    if (((BasicCellObject) cell.getUserObject()).getElement().getName()
                            .toLowerCase().matches(regex.toLowerCase()))
                    {
                        cellList.add(cell);
                    }
                }
            }
        } // end of for
        return cellList;
    }

    public static DefaultGraphCell getCellByKey ( JGraph graph, long TheID )
    {
        DefaultGraphCell cell;
        BasicCellObject binder;
        for (int i = 0; i < graph.getModel().getRootCount(); i++)
        {
            cell = (DefaultGraphCell) graph.getModel().getRootAt(i);
            if (null == cell)
            {
                continue;
            }
            if (cell.getUserObject() instanceof BasicCellObject)
            {
                binder = (BasicCellObject) cell.getUserObject();
                if (binder.getElement().getID() == TheID)
                {
                    return cell;
                }
            } else if (cell instanceof StructCell)
            {
                cell = getCellByKeyInStruct((StructCell) cell, TheID);
                if (null != cell)
                {
                    return cell;
                }
            }
        } // end of for
        return null;
    }

    /**
     * find the cell in the struct
     * 
     * @param struct StructCell
     * @param TheID int
     * @return DefaultGraphCell
     */
    public static DefaultGraphCell getCellByKeyInStruct ( StructCell struct, long TheID )
    {
        java.util.List structChildLst = struct.getChildren();
        for (int i = 0, size = structChildLst.size(); i < size; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) structChildLst.get(i);
            if (cell instanceof StructCell)
            {
                DefaultGraphCell c = getCellByKeyInStruct((StructCell) cell, TheID);
                // if find act ,return it direct,if not continue
                if (null != c)
                {
                    return getCellByKeyInStruct((StructCell) cell, TheID);
                }
            } else if (cell.getUserObject() instanceof BasicCellObject)
            {
                if (((BasicCellObject) cell.getUserObject()).getElement().getID() == TheID)
                {
                    return cell;
                }
            }
        }
        return null;
    }

    /**
     * get given workflow acts ids
     * 
     * @param TheFlow Workflow
     * @return List
     */
    public static java.util.List getFlowActIDs ( Workflow TheFlow )
    {
        java.util.List idList = new ArrayList();
        BasicActElement act = null;
        java.util.List baiscActElementList = TheFlow.getActivities();
        for (int i = 0, size = baiscActElementList.size(); i < size; i++)
        {
            act = (BasicActElement) baiscActElementList.get(i);
            idList.add(act.getActID());
        }
        return idList;
    }

    public static void startCellEditting ( JGraph Graph, Point Pt )
    {
        for (int i = 0; i < Graph.getModel().getRootCount(); i++)
        {
            DefaultGraphCell child = (DefaultGraphCell) Graph.getModel().getRootAt(i);
            Enumeration a = child.breadthFirstEnumeration();
            while (a.hasMoreElements())
            {
                DefaultGraphCell cell = (DefaultGraphCell) a.nextElement();
                if (cell.getUserObject() instanceof BasicCellObject
                        && Graph.getCellBounds(cell).contains(Graph.fromScreen(Pt)))
                {
                    BasicActElement elm = ((BasicCellObject) cell.getUserObject()).getElement();
                    if (elm instanceof ActivityElement || elm instanceof CallWorkflowActElement
                            || elm instanceof ExceptionHandlerActElement)
                    {
                        if (null != cell.getParent())
                        {
                            Map map = new HashMap();
                            GraphConstants.setEditable(map, true);
                            Map nested = new Hashtable();
                            nested.put(cell, map);
                            Graph.getGraphLayoutCache().edit(nested, null, null, null);
                            Graph.startEditingAtCell(cell);
                            Map m = new HashMap();
                            GraphConstants.setEditable(m, false);
                            Map n = new Hashtable();
                            n.put(cell, m);
                            Graph.getGraphLayoutCache().edit(n, null, null, null);
                        }
                    } else if (elm instanceof EntryActElement || elm instanceof ExitActElement)
                    {
                        Graph.startEditingAtCell(cell);
                    }
                    return;
                } // end of if
            } // end of while
        } // end of for

    }

    public static DefaultGraphCell insertEntryCell ( Point Pt, FlowChartPane pane, int StructType )
    {
        DefaultGraphCell entry = new DefaultGraphCell();
        if (StructType == StructCell.LOOP_STRUCT)
        {
            ImageIcon icon = GraphIcon.ICON_LOOP;
            LoopEntryActElement actElement = pane.getWorkflow().createLoopEntry();
            actElement.setName(getEntryName(pane.getWorkflow(), StructType, actElement));
            BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), actElement, pane);
            entry.setUserObject(ob);
            return insertVertex(null, entry, icon, Pt, pane, false);
        } else if (StructType == StructCell.PARA_STRUCT)
        {
            ImageIcon icon = GraphIcon.ICON_SYN;
            ParaEntryActElement actElement = pane.getWorkflow().createParallelEntry();
            actElement.setName(getEntryName(pane.getWorkflow(), StructType, actElement));
            BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), actElement, pane);
            entry.setUserObject(ob);
            return insertVertex(null, entry, icon, Pt, pane, false);
        } else if (StructType == StructCell.SEQUENCE_STRUCT)
        {
            ImageIcon icon = GraphIcon.ICON_SEQUENCEION;
            SequenceEntryActElement actElement = pane.getWorkflow().createSequenceEntry();
            actElement.setName(getEntryName(pane.getWorkflow(), StructType, actElement));
            BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), actElement, pane);
            entry.setUserObject(ob);
            return insertVertex(null, entry, icon, Pt, pane, false);
        }
        return null;
    }

    /**
     * get struct entryp name
     * 
     * @param Flow Workflow edit workflow
     * @param StructType int loop para or sequence type
     * @param act BasicActElement the element which will get name
     * @return String act name
     */
    private static String getEntryName ( Workflow Flow, int StructType, BasicActElement act )
    {
        java.util.List lst = Flow.getActivities();
        java.util.List acts = new ArrayList();
        String actName = null;
        for (int i = 0, size = lst.size(); i < size; i++)
        {
            Object obj = lst.get(i);
            if (StructType == StructCell.SEQUENCE_STRUCT)
            {
                if (obj instanceof SequenceEntryActElement && !obj.equals(act))
                {
                    SequenceEntryActElement elm = (SequenceEntryActElement) obj;
                    acts.add(elm.getName());
                }
            } else if (StructType == StructCell.PARA_STRUCT)
            {
                if (obj instanceof ParaEntryActElement && !obj.equals(act))
                {
                    ParaEntryActElement elm = (ParaEntryActElement) obj;
                    acts.add(elm.getName());
                }
            } else if (StructType == StructCell.LOOP_STRUCT)
            {
                if (obj instanceof LoopEntryActElement && !obj.equals(act))
                {
                    LoopEntryActElement elm = (LoopEntryActElement) obj;
                    acts.add(elm.getName());
                }
            }
        }
        for (int i = 0; true; i++)
        {
            if (i == 0)
            {
                if (StructType == StructCell.SEQUENCE_STRUCT)
                {
                    actName = NAME_SEQENTRYNAME;
                } else if (StructType == StructCell.PARA_STRUCT)
                {
                    actName = NAME_PARAENTRYNAME;
                } else if (StructType == StructCell.LOOP_STRUCT)
                {
                    actName = NAME_LOOPENTRYNAME;
                }
            } else
            {
                if (StructType == StructCell.SEQUENCE_STRUCT)
                {
                    actName = NAME_SEQENTRYNAME + ProjectConstants.SUFFIX + i;
                } else if (StructType == StructCell.PARA_STRUCT)
                {
                    actName = NAME_PARAENTRYNAME + ProjectConstants.SUFFIX + i;
                } else if (StructType == StructCell.LOOP_STRUCT)
                {
                    actName = NAME_LOOPENTRYNAME + ProjectConstants.SUFFIX + i;
                }
            }
            if (!acts.contains(actName))
            {
                return actName;
            }
        }
    }

    /**
     * get exit struct element name
     * 
     * @param Flow Workflow edit workflow
     * @param StructType int struct type ,loop ,para,sequence
     * @param act BasicActElement the element which will get name
     * @return String act name
     */
    private static String getExitName ( Workflow Flow, int StructType, BasicActElement act )
    {
        java.util.List lst = Flow.getActivities();
        java.util.List acts = new ArrayList();
        String actName = null;
        for (int i = 0, size = lst.size(); i < size; i++)
        {
            Object obj = lst.get(i);
            if (StructType == StructCell.SEQUENCE_STRUCT)
            {
                if (obj instanceof SequenceExitActElement && !obj.equals(act))
                {
                    SequenceExitActElement elm = (SequenceExitActElement) obj;
                    acts.add(elm.getName());
                }
            } else if (StructType == StructCell.PARA_STRUCT)
            {
                if (obj instanceof ParaExitActElement && !obj.equals(act))
                {
                    ParaExitActElement elm = (ParaExitActElement) obj;
                    acts.add(elm.getName());
                }
            } else if (StructType == StructCell.LOOP_STRUCT)
            {
                if (obj instanceof LoopExitActElement && !obj.equals(act))
                {
                    LoopExitActElement elm = (LoopExitActElement) obj;
                    acts.add(elm.getName());
                }
            }
        }
        for (int i = 0; true; i++)
        {
            if (i == 0)
            {
                if (StructType == StructCell.SEQUENCE_STRUCT)
                {
                    actName = NAME_SEQEXITNAME;
                } else if (StructType == StructCell.PARA_STRUCT)
                {
                    actName = NAME_PARAEXITNAME;
                } else if (StructType == StructCell.LOOP_STRUCT)
                {
                    actName = NAME_LOOPEXITNAME;
                }
            } else
            {
                if (StructType == StructCell.SEQUENCE_STRUCT)
                {
                    actName = NAME_SEQEXITNAME + ProjectConstants.SUFFIX + i;
                } else if (StructType == StructCell.PARA_STRUCT)
                {
                    actName = NAME_PARAEXITNAME + ProjectConstants.SUFFIX + i;
                } else if (StructType == StructCell.LOOP_STRUCT)
                {
                    actName = NAME_LOOPEXITNAME + ProjectConstants.SUFFIX + i;
                }
            }
            if (!acts.contains(actName))
            {
                return actName;
            }
        }
    }

    public static DefaultGraphCell insertExitCell ( Point Pt, DefaultGraphCell Entry,
            FlowChartPane pane, int StructType )
    {
        DefaultGraphCell Exit = new DefaultGraphCell();
        if (StructType == StructCell.LOOP_STRUCT)
        {
            ImageIcon icon = GraphIcon.ICON_LOOP;
            LoopExitActElement actElement = pane.getWorkflow().createLoopExit();
            actElement.setName(getExitName(pane.getWorkflow(), StructType, actElement));
            actElement.setEntryFlag(((BasicCellObject) Entry.getUserObject()).getElement().getID());
            BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), actElement, pane);
            Exit.setUserObject(ob);
            return insertVertex(null, Exit, icon, Pt, pane, false);
        } else if (StructType == StructCell.PARA_STRUCT)
        {
            ImageIcon icon = GraphIcon.ICON_SYN_EXIT;
            ParaExitActElement actElement = pane.getWorkflow().createParallelExit();
            // actElement.setName("Parallel_Exit");
            actElement.setName(getExitName(pane.getWorkflow(), StructType, actElement));
            actElement.setEntryFlag(((BasicCellObject) Entry.getUserObject()).getElement().getID());
            BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), actElement, pane);
            Exit.setUserObject(ob);
            return insertVertex(null, Exit, icon, Pt, pane, false);
        } else if (StructType == StructCell.SEQUENCE_STRUCT)
        {
            ImageIcon icon = GraphIcon.ICON_SEQUENCEION;
            SequenceExitActElement actElement = pane.getWorkflow().createSequenceExit();
            // actElement.setName("Tran_Exit");
            actElement.setName(getExitName(pane.getWorkflow(), StructType, actElement));
            actElement.setEntryFlag(((BasicCellObject) Entry.getUserObject()).getElement().getID());
            BasicCellObject ob = new BasicCellObject(pane.getWorkflow(), actElement, pane);
            Exit.setUserObject(ob);
            return insertVertex(null, Exit, icon, Pt, pane, false);
        }
        return null;
    }

    /**
     * add successing and condition to source and preActs to target
     * 
     * @param sourceCell
     * @param targetCell
     */
    public static void addSuccessingAndPreActsList ( DefaultGraphCell sourceCell,
            DefaultGraphCell targetCell )
    {
        BasicActElement source = ((BasicCellObject) sourceCell.getUserObject()).getElement();
        BasicActElement target = ((BasicCellObject) targetCell.getUserObject()).getElement();

        if (null == source.getSucceedingActs())
        {
            // put new ArrayList to succeeding acts
            source.setSucceedingActs(new ArrayList());
            // set new IDObjectMap
            source.setSucBranchCondition(new IDObjectMap());
        }
        // add target to succeeding acts
        source.getSucceedingActs().add(target);
        // put target condition ""
        source.getSucBranchCondition().put(target.getID(), "");
        // add source to target preActs
        if (null == target.getPreActs())
        {
            target.setPreActs(new ArrayList());
        }
        target.getPreActs().add(source);
    }

    /**
     * remove successing and condition from source and remove pre act from target
     * 
     * @param sourceCell
     * @param targetCell
     */
    public static void removeSuccessingAndPreActsList ( DefaultGraphCell sourceCell,
            DefaultGraphCell targetCell )
    {
        BasicActElement source = ((BasicCellObject) sourceCell.getUserObject()).getElement();
        BasicActElement target = ((BasicCellObject) targetCell.getUserObject()).getElement();

        // remove target element from the source succeeding act list
        source.getSucceedingActs().remove(target);
        IDObjectMap map = source.getSucBranchCondition();
        if (null != map && map.containsKey(target.getID()))
        {
            source.getSucBranchCondition().remove(target.getID());
        }
        // remove source element from target pre act list
        target.getPreActs().remove(source);
    }

    /**
     * remove vertex data
     * 
     * @param pane
     * @param vertexList each element is DefaultGraphCell and userObject may be struct, act and
     *            helper
     */
    public static void removeVertexData ( FlowChartPane pane, List vertexList )
    {
        if (vertexList == null)
        {
            return;
        }
        List exceptionActList = new ArrayList();
        Workflow flow = pane.getWorkflow();
        Iterator vertexIt = vertexList.iterator();
        while (vertexIt.hasNext())
        {
            DefaultGraphCell cell = (DefaultGraphCell) vertexIt.next();
            if (cell.getUserObject() instanceof BasicCellObject)
            {
                BasicCellObject obj = (BasicCellObject) cell.getUserObject();
                BasicActElement act = obj.getElement();
                // it is the exception element ,it is sepcial,also remove the
                // parent exception id
                if (act instanceof ExceptionHandlerActElement)
                {
                    exceptionActList.add(act);
                }
                // remove the data from workflow activity list
                flow.getActivities().remove(act);
            } else if (cell.getUserObject() instanceof HelperObject)
            {
                HelperElement helper = ((HelperObject) cell.getUserObject()).getElement();
                flow.getHelpers().remove(helper);
            } else if (cell instanceof StructCell)
            {
                pane.getStructList().remove(cell);
            }
        }
        deleteExceptionHandlerElmData(pane, exceptionActList);
    }

    /**
     * 
     * @param pane
     * @param exceptionActList each element is ExceptionHandlerActElement instance
     */
    public static void insertExceptionHandlerElmData ( FlowChartPane pane, List exceptionActList )
    {
        // deal with exception handler act element
        Iterator exceptionIt = exceptionActList.iterator();
        while (exceptionIt.hasNext())
        {
            ExceptionHandlerActElement exceptionAct = (ExceptionHandlerActElement) exceptionIt
                    .next();
            long excepId = exceptionAct.getID();
            long parentEntryFlag = exceptionAct.getParentEntryFlag();
            // it is the toppest exception element,remove the start
            // activity element exception flag
            if (-1 == parentEntryFlag)
            {
                if (null != pane.getWorkflow().getActivity(0))
                {
                    pane.getWorkflow().getActivity(0).setExceptionActID(excepId);
                }
            }
            // it is in the struct ,and the parent element is not select
            // to delte,so
            // delete the struct entry element's exception id flag
            else if (null != pane.getWorkflow().getActivity(parentEntryFlag))
            {
                pane.getWorkflow().getActivity(parentEntryFlag).setExceptionActID(excepId);
            }
        }
    }

    /**
     * delete exception hander act data
     * 
     * @param pane
     * @param exceptionActList
     */
    private static void deleteExceptionHandlerElmData ( FlowChartPane pane, List exceptionActList )
    {
        if (null == exceptionActList)
        {
            return;
        }
        // deal with exception handler act element
        Iterator exceptionIt = exceptionActList.iterator();
        while (exceptionIt.hasNext())
        {
            ExceptionHandlerActElement exceptionAct = (ExceptionHandlerActElement) exceptionIt
                    .next();
            long parentEntryFlag = exceptionAct.getParentEntryFlag();
            // it is the toppest exception element,remove the start
            // activity element exception flag
            if (-1 == parentEntryFlag)
            {
                if (null != pane.getWorkflow().getActivity(0))
                {
                    pane.getWorkflow().getActivity(0).removeExceptionHandlerActID();
                }
            }
            // it is in the struct ,and the parent element is not select
            // to delte,so
            // delete the struct entry element's exception id flag
            else if (null != pane.getWorkflow().getActivity(parentEntryFlag))
            {
                pane.getWorkflow().getActivity(parentEntryFlag).removeExceptionHandlerActID();
            }
        }
    }

    /**
     * get DefaultGraphCell which user object is BasicCellObject by id
     * 
     * @param pane
     * @param theID
     * @return
     */
    public static DefaultGraphCell getActCellByKey ( FlowChartPane pane, long theID )
    {
        Object[] Cells = pane.getGraph().getDescendants(pane.getGraph().getRoots());
        for (int i = 0, length = Cells.length; i < length; i++)
        {
            DefaultGraphCell cell = (DefaultGraphCell) Cells[i];
            Object obj = cell.getUserObject();
            if (obj instanceof BasicCellObject)
            {
                BasicActElement act = ((BasicCellObject) obj).getElement();
                if (act.getID() == theID)
                {
                    return cell;
                }
            }
        }
        return null;
    }

    /**
     * get edge list each element is Edge
     * 
     * @param cells
     * @return
     */
    public static List getEdgeList ( Object[] cells )
    {
        List edgeList = new ArrayList();
        for (int i = 0, size = cells.length; i < size; i++)
        {
            if (cells[i] instanceof DefaultEdge)
            {
                edgeList.add(cells[i]);
            }
        }
        return edgeList;
    }

    /**
     * get vertex list contain struct cell,act cell and assist cell from cells
     * 
     * @param cells
     * @return
     */
    public static List getVertexList ( Object[] cells )
    {
        List vertexList = new ArrayList();
        for (int i = 0, size = cells.length; i < size; i++)
        {
            if (cells[i] instanceof StructCell)
            {
                vertexList.add(cells[i]);
            } else if (cells[i] instanceof DefaultGraphCell)
            {
                Object userObj = ((DefaultGraphCell) cells[i]).getUserObject();
                if (userObj instanceof BasicCellObject)
                {
                    vertexList.add(cells[i]);
                } else if (userObj instanceof HelperObject)
                {
                    vertexList.add(cells[i]);
                }
            }
        }
        return vertexList;
    }

    /**
     * remove the edge connect source cell userobject succeeding info
     * 
     * @param pane
     * @param edgeList
     */
    public static void removeEdgesData ( FlowChartPane pane, List edgeList )
    {
        Iterator edgeIt = edgeList.iterator();
        while (edgeIt.hasNext())
        {
            DefaultEdge edge = (DefaultEdge) edgeIt.next();
            // get userobject
            FlowEdgeInfo edgeInfo = (FlowEdgeInfo) edge.getUserObject();
            // get source vertex
            DefaultGraphCell source = getCellByKey(pane.getGraph(), edgeInfo.getSourceID());
            // get target vertex
            DefaultGraphCell target = getCellByKey(pane.getGraph(), edgeInfo.getTargetID());

            // remove edges of act succeeding info
            removeSuccessingAndPreActsList(source, target);
        }
    }

    /**
     * only resume group
     * 
     * @param pane
     * @param dealVertex
     */
    public static void resumeGroup ( FlowChartPane pane, List dealVertex )
    {
        Map childM = new HashMap();
        List delStructLst = new ArrayList();
        Iterator it = dealVertex.iterator();
        while (it.hasNext())
        {
            DefaultGraphCell cell = (DefaultGraphCell) it.next();
            Object obj = cell.getUserObject();
            if (cell instanceof StructCell)
            {
                delStructLst.add(cell);
            } else if (obj instanceof BasicCellObject)
            {
                BasicActElement elm = ((BasicCellObject) obj).getElement();
                if (elm instanceof EntryActElement)
                {
                    addToMapValueList(childM, elm.getID(), cell);
                } else if (elm instanceof ExitActElement)
                {
                    long entryFlag = ((ExitActElement) elm).getEntryFlag();
                    addToMapValueList(childM, entryFlag, cell);
                } else if (elm.getParentEntryFlag() != -1)
                {
                    addToMapValueList(childM, elm.getParentEntryFlag(), cell);
                }
            } else if (obj instanceof HelperObject)
            {
                HelperElement elm = ((HelperObject) obj).getElement();
                if (elm.getParentEntryFlag() != -1)
                {
                    addToMapValueList(childM, elm.getParentEntryFlag(), cell);
                }
            }
        }
        Iterator delStructIt = delStructLst.iterator();
        while (delStructIt.hasNext())
        {
            StructCell delStruct = (StructCell) delStructIt.next();
            long entryId = ((StructObject) delStruct.getUserObject()).getEntry().getID();
            List childList = (List) childM.get(new Long(entryId));
            ParentMap pM = new ParentMap(childList.toArray(), delStruct);
            pane.getGraph().getModel().edit(null, null, pM, null);
        }
        Iterator parentEntryID = childM.keySet().iterator();
        while (parentEntryID.hasNext())
        {
            Long parentId = (Long) parentEntryID.next();
            DefaultGraphCell entry = getActCellByKey(pane, parentId.intValue());
            EntryActElement entryElm = (EntryActElement) ((BasicCellObject) entry.getUserObject())
                    .getElement();

            StructCell struct = getStructCellByEntry(entryElm, pane);
            // already resumed
            if (delStructLst.contains(struct))
            {
                continue;
            }
            List childList = (List) childM.get(parentId);
            ParentMap pM = new ParentMap(childList.toArray(), struct);
            pane.getGraph().getModel().edit(null, null, pM, null);
        }
        delStructIt = delStructLst.iterator();
        while (delStructIt.hasNext())
        {
            StructCell delStruct = (StructCell) delStructIt.next();
            long delEntryParentId = ((StructObject) delStruct.getUserObject()).getEntry()
                    .getParentEntryFlag();
            if (delEntryParentId != -1)
            {
                DefaultGraphCell parentEntryCell = getActCellByKey(pane, delEntryParentId);
                EntryActElement parentEntryElm = (EntryActElement) ((BasicCellObject) parentEntryCell
                        .getUserObject()).getElement();
                StructCell struct = getStructCellByEntry(parentEntryElm, pane);

                ParentMap pM = new ParentMap();
                pM.addEntry(delStruct, struct);
                pane.getGraph().getModel().edit(null, null, pM, null);
            }
        }
    }

    /**
     * add cell to map value List by given key,the map key is Integer instance,value is List each
     * element is DefaultGraphCell instance.
     * 
     * @param map
     * @param key
     * @param value
     */
    private static void addToMapValueList ( Map map, long key, DefaultGraphCell cell )
    {
        Long iKey = new Long(key);
        if (null == map.get(iKey))
        {
            map.put(iKey, new ArrayList());
        }
        List value = (List) map.get(iKey);
        value.add(cell);
    }

    public static void pasteUndo ( FlowChartPane pane, List delVertex, List delEdge,
            List pasteVertex, List pasteEdge )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        // remove paste edge
        removeEdge(pane, pasteEdge);
        // remove paste vertex
        removeVertex(pane, pasteVertex);
        // insert del vertex
        insertVertex(pane, delVertex);
        // insert del edge
        insertEdge(pane, delEdge);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    public static void pasteRedo ( FlowChartPane pane, List delVertex, List delEdge,
            List pasteVertex, List pasteEdge )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        // remove del edge
        removeEdge(pane, delEdge);
        // remove del vertex
        removeVertex(pane, delVertex);
        // insert paste vertex
        insertVertex(pane, pasteVertex);
        // insert paste edges
        insertEdge(pane, pasteEdge);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    public static void cutUndo ( FlowChartPane pane, DefaultGraphCell starter, List delVertex,
            List delEdge )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        // remove insert starter
        if (starter != null)
        {
            List l = new ArrayList();
            l.add(starter);
            removeVertex(pane, l);
        }
        // insert cut vertex
        insertVertex(pane, delVertex);
        // insert cut edge
        insertEdge(pane, delEdge);
        pane.getGraphUndoManager().isAddEdit(true);
    }

    public static void cutRedo ( FlowChartPane pane, DefaultGraphCell starter, List delVertex,
            List delEdge )
    {
        pane.getGraphUndoManager().isAddEdit(false);
        // remove delete edge
        removeEdge(pane, delEdge);
        // remove delete vertex
        removeVertex(pane, delVertex);
        // insert new starter
        if (null != starter)
        {
            List l = new ArrayList();
            l.add(starter);
            insertVertex(pane, l);
        }

        pane.getGraphUndoManager().isAddEdit(true);
    }

    /**
     * remove edge on model and data info
     * 
     * @param pane FlowChartPane instance
     * @param edges List each element is DefaultEdge info
     */
    public static void removeEdge ( FlowChartPane pane, List edges )
    {
        if (null != edges)
        {
            removeEdgesData(pane, edges);
            // delete edge list
            pane.getGraph().getGraphLayoutCache().remove(edges.toArray());
        }
    }

    /**
     * insert edge in model and data
     * 
     * @param pane FlowChartPane instance
     * @param edges List each element is DefaultEdge instance
     */
    public static void insertEdge ( FlowChartPane pane, List edges )
    {
        if (null != edges)
        {
            // insert edge list
            Iterator edgeIt = edges.iterator();
            while (edgeIt.hasNext())
            {
                DefaultEdge edge = (DefaultEdge) edgeIt.next();
                // get userobject
                FlowEdgeInfo edgeInfo = (FlowEdgeInfo) edge.getUserObject();
                // get source vertex
                DefaultGraphCell source = getCellByKey(pane.getGraph(), edgeInfo.getSourceID());
                // get target vertex
                DefaultGraphCell target = getCellByKey(pane.getGraph(), edgeInfo.getTargetID());
                // get source port
                DefaultPort sPort = (DefaultPort) source.getChildAt(0);
                DefaultPort tPort = (DefaultPort) target.getChildAt(0);

                // set edge source port
                edge.setSource(sPort);
                // set edge target port
                edge.setTarget(tPort);

                // resume data
                addSuccessingAndPreActsList(source, target);
            }
            // insert edge list
            pane.getGraph().getGraphLayoutCache().insert(edges.toArray());
        }
    }

    /**
     * remove vertex on model and data
     * 
     * @param pane
     * @param vertex List each element is DefaultGraphCell,user object may be structCell,act and
     *            assist element
     */
    public static void removeVertex ( FlowChartPane pane, List vertex )
    {
        if (null != vertex)
        {
            removeVertexData(pane, vertex);
            // insert vertex in to model
            pane.getGraph().getModel().remove(vertex.toArray());
        }
    }

    public static void insertVertex ( FlowChartPane pane, List vertex )
    {
        List exceptionHandlerElmList = new ArrayList();
        // deal with delete vertex
        if (null != vertex)
        {
            // insert delete vertex data
            Iterator vertexIt = vertex.iterator();
            while (vertexIt.hasNext())
            {
                DefaultGraphCell cell = (DefaultGraphCell) vertexIt.next();
                Object obj = cell.getUserObject();
                if (cell instanceof StructCell)
                {
                    // resume data , add to cellList
                    if (!pane.getStructList().contains(cell))
                    {
                        pane.getStructList().add(cell);
                    }
                } else if (cell.getUserObject() instanceof BasicCellObject)
                {
                    BasicActElement elm = ((BasicCellObject) obj).getElement();
                    // resume data
                    pane.getWorkflow().getActivities().add(elm);
                    // if not have port,set a port
                    if (cell.getChildCount() == 0)
                    {
                        cell.add(new DefaultPort());
                    }
                    if (elm instanceof ExceptionHandlerActElement)
                    {
                        exceptionHandlerElmList.add(elm);
                    }
                } else if (cell.getUserObject() instanceof BasicHelperObject)
                {
                    pane.getWorkflow().getHelpers().add(((BasicHelperObject) obj).getElement());
                }
            }
            // insert vertex in to model
            pane.getGraph().getGraphLayoutCache().insert(vertex.toArray());
            resumeGroup(pane, vertex);
        }
        insertExceptionHandlerElmData(pane, exceptionHandlerElmList);
    }

    /**
     * <li>Description:构建循环结构时，动态配置循环结束的位置</li> 
     * <AUTHOR>
     * 2014-3-28 
     * @param pane
     * @param group
     * @param structType
     * @param pt
     * @param xx
     * @param yy
     * return Object[]
     */
    public static Object[] insertStructCellGps ( FlowChartPane pane, StructCell group, int structType, Point2D pt,
            int xx, int yy )
    {
        int x = (int) pt.getX();
        int y = (int) pt.getY();
        DefaultGraphCell entryCell = insertEntryCell(new Point(x + 5, y + 5), pane, structType);
        DefaultGraphCell exitCell = insertExitCell(new Point(x + xx, y + yy), entryCell, pane, structType);

        EntryActElement sEntry = (EntryActElement) ((BasicCellObject) entryCell.getUserObject()).getElement();
        // set entry act element exit id
        BasicActElement sExit = ((BasicCellObject) exitCell.getUserObject()).getElement();
        sEntry.setExitId(sExit.getID());

        // set struct cell user object
        StructObject obj = new StructObject(sEntry);
        StructCell struct = new StructCell();
        struct.setUserObject(obj);

        Map attr = struct.getAttributes();
        GraphConstants.setOpaque(attr, false);
        GraphConstants.setBorderColor(attr, Color.BLUE);
        GraphConstants.setSizeable(attr, false);
        GraphConstants.setEditable(attr, true);

        ParentMap parentMap = new ParentMap();
        // Insert Child Parent Entries
        parentMap.addEntry(entryCell, struct);
        parentMap.addEntry(exitCell, struct);

        // crate insert struct undo edit
        InsertStructUndo edit = new InsertStructUndo(pane, struct, entryCell, exitCell);
        pane.getGraphUndoManager().addEdit(edit);
        /**
         * insert struct to graph
         */
        JGraph graph = pane.getGraph();
        pane.getGraphUndoManager().isAddEdit(false);
        graph.getGraphLayoutCache().insert(new Object[] { struct }, null, null, parentMap, null);

        if (null != group)
        {
            Rectangle rect = graph.getCellBounds(graph.getDescendants(new Object[] { struct, group })).getBounds();
            // get group entry element
            EntryActElement groupEntry = group.getEntryElement();
            // change view model
            parentMap = new ParentMap();
            parentMap.addEntry(struct, group);
            Map groupAttr = new HashMap();
            // GraphConstants.createMap();
            GraphConstants.setBounds(groupAttr, rect);
            Map nest = new Hashtable();
            nest.put(group, groupAttr);
            graph.getModel().edit(nest, null, parentMap, null);
            // change parent entry flag
            EntryActElement entry = struct.getEntryElement();
            ExitActElement exit = struct.getExitElement();
            long parentId = groupEntry.getID();
            entry.setParentEntryFlag(parentId);
            exit.setParentEntryFlag(parentId);
        }
        pane.getGraphUndoManager().isAddEdit(true);

        // add the struct to the struct list
        java.util.List structList = pane.getStructList();
        if (!structList.contains(struct))
        {
            structList.add(struct);
        }
        Object[] ob = new Object[2];
        ob[0] = sEntry;
        ob[1] = sExit;
        return ob;
    }

    public static JFileChooser getIconChooserExcel ()
    {
        if (null == iconChooser)
        {
            iconChooser = new JFileChooser();
            FilePreviewer previewer = new FilePreviewer(iconChooser);
            iconChooser.setAccessory(previewer);
            ExFileFilter filter = new ExFileFilter();
            filter.addExtension("xls");
            iconChooser.removeChoosableFileFilter(iconChooser.getFileFilter());
            iconChooser.addChoosableFileFilter(filter);
        }
        return iconChooser;
    }
    
    private static JFileChooser iconChooser;

    static final public int     COLOR_TYPE_BORDER  = 0;
    static final public int     COLOR_TYPE_FILL    = 1;

    private static String       NAME_SEQENTRYNAME  = StudioRes
                                                           .getString("view.editorui.act.seqentry");
    private static String       NAME_SEQEXITNAME   = StudioRes
                                                           .getString("view.editorui.act.seqexit");
    private static String       NAME_LOOPENTRYNAME = StudioRes
                                                           .getString("view.editorui.act.loopety");
    private static String       NAME_LOOPEXITNAME  = StudioRes
                                                           .getString("view.editorui.act.loopexit");
    private static String       NAME_PARAENTRYNAME = StudioRes
                                                           .getString("view.editorui.act.paraentry");
    private static String       NAME_PARAEXITNAME  = StudioRes
                                                           .getString("view.editorui.act.paraexit");

}
