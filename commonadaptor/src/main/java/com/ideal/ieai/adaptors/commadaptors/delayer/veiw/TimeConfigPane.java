package com.ideal.ieai.adaptors.commadaptors.delayer.veiw;

import java.awt.Dimension;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;

import javax.swing.ButtonGroup;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JScrollPane;
import javax.swing.JTextField;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;

import com.ideal.ieai.adaptors.commadaptors.delayer.DelayerConfigView;
import com.ideal.ieai.adaptors.commadaptors.delayer.Helper;
import com.ideal.ieai.commons.task.TimeLimitInCore;
import com.ideal.ieai.core.activity.DefaultConfig;

public class TimeConfigPane extends JScrollPane implements DocumentListener, ItemListener,
        ActionListener
{
    private DelayerConfigView _view;

    // //////////////////////////////
    JPanel                    PanelAll       = new JPanel();
    JPanel                    TimePane1      = new JPanel();
    JTextField                yearTxt        = new JTextField();
    JTextField                monthTxt       = new JTextField();
    JTextField                dayTxt         = new JTextField();
    JTextField                hourTxt        = new JTextField();
    JTextField                minuteTxt      = new JTextField();
    JTextField                secondTxt      = new JTextField();
    JLabel                    jLabel1        = new JLabel();
    JLabel                    jLabel2        = new JLabel();
    JLabel                    jLabel3        = new JLabel();
    JLabel                    jLabel4        = new JLabel();
    JLabel                    jLabel5        = new JLabel();
    JLabel                    jLabel6        = new JLabel();
    GridBagLayout             gridBagLayout1 = new GridBagLayout();
    JRadioButton              absRdBtn       = new JRadioButton();
    JRadioButton              timeRdBtn      = new JRadioButton();
    GridBagLayout             gridBagLayout2 = new GridBagLayout();
    ButtonGroup               bg             = new ButtonGroup();

    private JCheckBox         chkUseDyn      = null;

    public TimeConfigPane(DefaultConfig config, DelayerConfigView view)
    {
        _view = view;
        this.setName(Helper.getString("delayer"));
        try
        {
            jbInit();

            int delayType = -1;
            if (null != config.get(Helper.SAVE_DELAY_TYPE))
            {
                delayType = Integer.valueOf((String) config.get(Helper.SAVE_DELAY_TYPE)).intValue();
            }
            if (null != config.get(Helper.USE_DYN))
            {
                chkUseDyn.setSelected(((Boolean) config.get(Helper.USE_DYN)).booleanValue());
                this.setEnable(!chkUseDyn.isSelected());
            }
            // set time info.
            String timeStr = null;
            if (!chkUseDyn.isSelected())
            {
                timeStr = (String) config.get(Helper.SAVE_TIME_STR);
            }
            if (null != timeStr)
            {
                String[] sTimes = TimeLimitInCore.parseTimeStr(timeStr);
                yearTxt.setText(sTimes[0]);
                monthTxt.setText(sTimes[1]);
                dayTxt.setText(sTimes[2]);
                hourTxt.setText(sTimes[3]);
                minuteTxt.setText(sTimes[4]);
                secondTxt.setText(sTimes[5]);
            }

            yearTxt.getDocument().addDocumentListener(this);
            monthTxt.getDocument().addDocumentListener(this);
            dayTxt.getDocument().addDocumentListener(this);
            hourTxt.getDocument().addDocumentListener(this);
            minuteTxt.getDocument().addDocumentListener(this);
            secondTxt.getDocument().addDocumentListener(this);

            if (-1 == delayType)
            {
                absRdBtn.setSelected(true);
            } else if (delayType == Helper.ABS_TIME_DELAY)
            {
                absRdBtn.setSelected(true);
            } else
            {
                timeRdBtn.setSelected(true);
            }

            absRdBtn.addItemListener(this);
            timeRdBtn.addItemListener(this);
            chkUseDyn.addActionListener(this);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void jbInit () throws Exception
    {
        GridBagConstraints gridBagConstraints3 = new GridBagConstraints(1, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.NORTHWEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0);
        gridBagConstraints3.gridy = 1;
        GridBagConstraints gridBagConstraints2 = new GridBagConstraints(0, 1, 2, 1, 1.0, 1.0,
                GridBagConstraints.NORTH, GridBagConstraints.HORIZONTAL, new Insets(4, 4, 4, 4), 0,
                0);
        gridBagConstraints2.gridy = 3;
        GridBagConstraints gridBagConstraints1 = new GridBagConstraints(0, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.CENTER, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0);
        gridBagConstraints1.gridy = 1;
        GridBagConstraints gridBagConstraints = new GridBagConstraints();
        gridBagConstraints.gridx = 0;
        gridBagConstraints.anchor = GridBagConstraints.WEST;
        gridBagConstraints.insets = new Insets(4, 4, 4, 4);
        gridBagConstraints.gridy = 2;
        secondTxt.setHorizontalAlignment(javax.swing.JTextField.RIGHT);
        minuteTxt.setHorizontalAlignment(javax.swing.JTextField.RIGHT);
        hourTxt.setHorizontalAlignment(javax.swing.JTextField.RIGHT);
        dayTxt.setHorizontalAlignment(javax.swing.JTextField.RIGHT);
        monthTxt.setHorizontalAlignment(javax.swing.JTextField.RIGHT);
        PanelAll.setLayout(gridBagLayout2);
        TimePane1.setLayout(gridBagLayout1);
        yearTxt.setOpaque(true);
        yearTxt.setHorizontalAlignment(javax.swing.JTextField.RIGHT);
        yearTxt.setPreferredSize(new Dimension(6, 22));
        jLabel1.setText(Helper.getString("year"));
        jLabel2.setText(Helper.getString("month"));
        jLabel3.setText(Helper.getString("day"));
        jLabel4.setText(Helper.getString("hour"));
        jLabel5.setText(Helper.getString("minute"));
        jLabel6.setText(Helper.getString("second"));
        absRdBtn.setText(Helper.getString("abstimedelay"));
        timeRdBtn.setText(Helper.getString("timedelay"));
        TimePane1.add(yearTxt, new GridBagConstraints(0, 0, 1, 1, 1.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.HORIZONTAL, new Insets(4, 4, 4, 4), 0,
                0));
        TimePane1.add(monthTxt, new GridBagConstraints(2, 0, 1, 1, 1.0, 0.0,
                GridBagConstraints.CENTER, GridBagConstraints.BOTH, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(dayTxt, new GridBagConstraints(4, 0, 1, 1, 1.0, 0.0,
                GridBagConstraints.CENTER, GridBagConstraints.BOTH, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(jLabel2, new GridBagConstraints(3, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(hourTxt, new GridBagConstraints(6, 0, 1, 1, 1.0, 0.0,
                GridBagConstraints.CENTER, GridBagConstraints.BOTH, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(jLabel3, new GridBagConstraints(5, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(minuteTxt, new GridBagConstraints(8, 0, 1, 1, 1.0, 0.0,
                GridBagConstraints.CENTER, GridBagConstraints.BOTH, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(jLabel6, new GridBagConstraints(11, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(jLabel5, new GridBagConstraints(9, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(jLabel4, new GridBagConstraints(7, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(jLabel1, new GridBagConstraints(1, 0, 1, 1, 0.0, 0.0,
                GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        TimePane1.add(secondTxt, new GridBagConstraints(10, 0, 1, 1, 1.0, 0.0,
                GridBagConstraints.CENTER, GridBagConstraints.BOTH, new Insets(4, 4, 4, 4), 0, 0));
        PanelAll.add(absRdBtn, gridBagConstraints1);
        PanelAll.add(timeRdBtn, gridBagConstraints3);
        PanelAll.add(TimePane1, gridBagConstraints2);
        PanelAll.add(getChkUseDyn(), gridBagConstraints);
        this.getViewport().add(PanelAll, null);
        bg.add(absRdBtn);
        bg.add(timeRdBtn);
    }

    /**
     * This method initializes chkUseDyn
     * 
     * @return javax.swing.JCheckBox
     */
    private JCheckBox getChkUseDyn ()
    {
        if (chkUseDyn == null)
        {
            chkUseDyn = new JCheckBox(Helper.getString("dynconfig"));
        }
        return chkUseDyn;
    }

    // static public void main ( String argvs[] )
    // {
    // JFrame frame = new JFrame();
    // TimeConfigPane pane = new TimeConfigPane(1, "2005:3:6:7:8:9", null);
    // frame.getContentPane().add(pane);
    // frame.setDefaultCloseOperation(WindowConstants.EXIT_ON_CLOSE);
    // frame.setSize(400, 400);
    // frame.show();
    // }

    public void changedUpdate ( DocumentEvent e )
    {
        _view.fireDataChange();
    }

    public void insertUpdate ( DocumentEvent e )
    {
        _view.fireDataChange();
    }

    public void removeUpdate ( DocumentEvent e )
    {
        _view.fireDataChange();
    }

    public void itemStateChanged ( ItemEvent e )
    {
        if (e.getStateChange() == ItemEvent.DESELECTED)
        {
            return;
        }
        _view.fireDataChange();
    }

    public boolean canSave ()
    {
        if (this.chkUseDyn.isSelected())
        {
            return true;
        }
        try
        {
            TimeLimitInCore.getTimeStr(yearTxt.getText().trim(), monthTxt.getText().trim(), dayTxt
                    .getText().trim(), hourTxt.getText().trim(), minuteTxt.getText().trim(),
                secondTxt.getText().trim(), absRdBtn.isSelected());
        } catch (Exception e)
        {
            JOptionPane.showMessageDialog(this, Helper.getString("invalidtimesetting"));
            return false;
        }
        return true;
    }

    public void save ( DefaultConfig config )
    {
        int type;
        if (absRdBtn.isSelected())
        {
            type = Helper.ABS_TIME_DELAY;
        } else
        {
            type = Helper.TIME_DELAY;
        }
        config.put(Helper.SAVE_DELAY_TYPE, String.valueOf(type));
        config.put(Helper.USE_DYN, Boolean.valueOf(chkUseDyn.isSelected()));

        if (!chkUseDyn.isSelected())
        {
            String timeStr = TimeLimitInCore.getTimeStr(yearTxt.getText().trim(), monthTxt
                    .getText().trim(), dayTxt.getText().trim(), hourTxt.getText().trim(), minuteTxt
                    .getText().trim(), secondTxt.getText().trim(), absRdBtn.isSelected());
            config.put(Helper.SAVE_TIME_STR, timeStr);
        }
    }

    /*
     * (non-Javadoc)
     * 
     * @see java.awt.event.ActionListener#actionPerformed(java.awt.event.ActionEvent)
     */
    public void actionPerformed ( ActionEvent e )
    {
        _view.fireDataChange();
        setEnable(!chkUseDyn.isSelected());
    }

    private void setEnable ( boolean enable )
    {
        this.yearTxt.setEditable(enable);
        this.monthTxt.setEditable(enable);
        this.dayTxt.setEditable(enable);
        this.hourTxt.setEditable(enable);
        this.minuteTxt.setEditable(enable);
        this.secondTxt.setEditable(enable);
    }
}
