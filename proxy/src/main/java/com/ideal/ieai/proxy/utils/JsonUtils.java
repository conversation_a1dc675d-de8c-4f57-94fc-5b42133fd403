package com.ideal.ieai.proxy.utils;

import com.ideal.ieai.proxy.engine.ProxyEnv;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;

public class JsonUtils
{
    /**
     * String转json 
     */
    public static JSONObject strToJson ( String str )
    {
        JSONObject jsonObject = JSONObject.fromObject(str);
        return jsonObject;
    }

    /**
     * {
            "ip": [
                "**********",
                "**********",
                "**********"
            ],
            "port": 8000,
            "level":0,
            "proxy": {
                "ip": "**********",
                "port": 8000,
                "level":1,
                "proxy": {
                    "ip": "**********",
                    "port": 8000,
                    "level":2,
                    "proxy": {
                        "level":3,
                        "ip":"**********",
                        "port": 8000
                   }
                }
            }
        }
     * 根据等级获取proxy的信息
     */
    public static Map getMessage ( int level, String str, String serverIp, int pport )
    {
        int proxyServicePort = 7777;
        try
        {
            proxyServicePort = ProxyEnv.getInstance().getProxyServicePort();
        } catch (UnknownHostException e)
        {
            e.printStackTrace();
        }
        Map map = new HashMap();
        JSONObject jsonObj = JsonUtils.strToJson(str);
        if (level != Integer.parseInt(jsonObj.get("level").toString()))
        {
            JSONArray json_array = JSONArray.fromObject(jsonObj.get("nodes"));
            if (null != json_array && !json_array.isEmpty())
            {
                JSONObject jsonObj_0 = JsonUtils.strToJson(json_array.get(0).toString());
                JSONArray json_array_1 = JSONArray.fromObject(jsonObj_0.get("nodes").toString());
                if (Boolean.parseBoolean(String.valueOf(jsonObj_0.get("proxy"))))
                {
                    JSONObject proxy = JsonUtils.strToJson(json_array_1.get(0).toString());
                    serverIp = jsonObj_0.getString("ip");
                    serverIp = jsonObj_0.getString("proxyIpOut");
                    if (jsonObj_0.containsKey("switchOutIp"))
                    {
                        String switchIp = jsonObj_0.getString("switchOutIp");
                        if (null != switchIp && !"".equals(switchIp) && !"null".equals(switchIp))
                        {
                            serverIp = switchIp;
                        }
                    }

                    String port = String.valueOf(jsonObj_0.get("proxyPortOut"));
                    if (null != port && !"0".equals(port) && !"".equals(port) && !"null".equals(port))
                    {
                        proxyServicePort = Integer.parseInt(port);
                        pport = Integer.parseInt(port);
                    }
                    return getMessage(level, proxy.toString(), serverIp, pport);
                } else
                {
                    // serverIp = jsonObj.getString("ip");
                    serverIp = jsonObj.getString("proxyIpOut");
                    if (jsonObj.containsKey("switchOutIp"))
                    {
                        String switchIp = jsonObj.getString("switchOutIp");
                        if (null != switchIp && !"".equals(switchIp) && !"null".equals(switchIp))
                        {
                            serverIp = switchIp;
                        }
                    }

                    String port = String.valueOf(jsonObj.get("proxyPortOut"));
                    if (null != port && !"0".equals(port) && !"".equals(port) && !"null".equals(port))
                    {
                        proxyServicePort = Integer.parseInt(port);
                        pport = Integer.parseInt(port);
                        jsonObj_0.accumulate("servicePort", proxyServicePort);
                        jsonObj_0.put("servicePort", proxyServicePort);
                    } else
                    {
                        jsonObj_0.accumulate("servicePort", "0");
                        jsonObj_0.put("servicePort", "0");
                    }
                    // level--;
                    map.put("level", level);
                    map.put("isProxy", Boolean.parseBoolean(String.valueOf(jsonObj_0.get("proxy"))));
                    map.put("next_ip", jsonObj_0.get("ip").toString());
                    map.put("next_port", jsonObj_0.get("port").toString());
                }
            } else
            {
                // serverIp = jsonObj.getString("ip");
                // serverIp = jsonObj.getString("proxyIpOut");
                serverIp = jsonObj.getString("ip");
                serverIp = jsonObj.getString("proxyIpOut");
                if (jsonObj.containsKey("switchOutIp"))
                {
                    String switchIp = jsonObj.getString("switchOutIp");
                    if (null != switchIp && !"".equals(switchIp) && !"null".equals(switchIp))
                    {
                        serverIp = switchIp;
                    }
                }
                // level--;
                String port = String.valueOf(jsonObj.get("proxyPortOut"));
                if (pport != 0)
                {
                    jsonObj.accumulate("servicePort", pport);
                    jsonObj.put("servicePort", pport);
                } else
                {
                    if (null != port && !"0".equals(port) && !"".equals(port) && !"null".equals(port))
                    {
                        proxyServicePort = Integer.parseInt(port);
                        pport = Integer.parseInt(port);
                        jsonObj.accumulate("servicePort", proxyServicePort);
                        jsonObj.put("servicePort", proxyServicePort);
                    } else
                    {
                        jsonObj.accumulate("servicePort", "0");
                        jsonObj.put("servicePort", "0");
                    }
                }
                map.put("level", level);
                map.put("isProxy", Boolean.parseBoolean(String.valueOf(jsonObj.get("proxy"))));
                map.put("next_ip", jsonObj.get("ip").toString());
                map.put("next_port", jsonObj.get("port").toString());
            }

        } else
        {
            // serverIp = jsonObj.getString("ip");
            map.put("level", level);
            if (jsonObj.containsKey("isNginx") && Boolean.parseBoolean(String.valueOf(jsonObj.get("isNginx"))))
            {

            } else
            {
                String port = String.valueOf(jsonObj.get("proxyPortOut"));
                if (pport != 0)
                {
                    jsonObj.accumulate("servicePort", pport);
                    jsonObj.put("servicePort", pport);
                } else
                {
                    if (null != port && !"0".equals(port) && !"".equals(port) && !"null".equals(port))
                    {
                        proxyServicePort = Integer.parseInt(port);
                        pport = Integer.parseInt(port);
                        jsonObj.accumulate("servicePort", proxyServicePort);
                        jsonObj.put("servicePort", proxyServicePort);
                    } else
                    {
                        jsonObj.accumulate("servicePort", proxyServicePort);
                        jsonObj.put("servicePort", proxyServicePort);
                        pport = proxyServicePort;
                    }
                }
            }
            if (jsonObj.containsKey("ssl")){
                map.put("ssltype", jsonObj.get("ssl").toString());
            }
            map.put("isProxy", Boolean.parseBoolean(String.valueOf(jsonObj.get("proxy"))));
            map.put("next_ip", jsonObj.get("ip").toString());
            map.put("next_port", jsonObj.get("port").toString());
        }
        map.put("serverIp", serverIp);
        map.put("serverPort", pport);
        return map;
    }

    public static Map getMessageRet ( int level, String str )
    {
        Map map = new HashMap();
        JSONObject jsonObj = JsonUtils.strToJson(str);
        if (level == 0)
        {
            String nextIp = jsonObj.getString("ip").substring(2, jsonObj.getString("ip").length() - 2);
            if (null != jsonObj.get("nodes"))
            {
                JSONArray json = JSONArray.fromObject(jsonObj.get("nodes"));
                if (null != json && !json.isEmpty())
                {
                    JSONObject job = json.getJSONObject(0);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                    int plevel = Integer.parseInt(String.valueOf(job.get("level")));
                    if (plevel == 1)
                    {
                        if (job.containsKey("switchIp"))
                        {
                            String switchIp = job.get("switchIp").toString();
                            if (null != switchIp && !"".equals(switchIp) && !"null".equals(switchIp))
                            {
                                nextIp = switchIp;
                            }
                        }
                    }

                }
            }
            map.put("isProxy", false);
            map.put("next_ip", nextIp);
            map.put("next_port", jsonObj.get("serverPort").toString());
        } else
        {
            if (null != jsonObj.get("nodes"))
            {
                JSONArray json = JSONArray.fromObject(jsonObj.get("nodes"));
                if (null != json && !json.isEmpty())
                {
                    JSONObject job = json.getJSONObject(0);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                    int plevel = Integer.parseInt(String.valueOf(job.get("level")));
                    if (level == plevel)
                    {
                        map.put("isProxy", Boolean.parseBoolean(String.valueOf(job.get("proxy"))));
                        map.put("next_ip", job.get("proxyIpOut").toString());
                        if (job.containsKey("switchIp"))
                        {
                            String switchIp = job.get("switchIp").toString();
                            if (null != switchIp && !"".equals(switchIp) && !"null".equals(switchIp))
                            {
                                map.put("next_ip", switchIp);
                            }
                        }
                        map.put("next_port", job.get("serverPort").toString());
                        return map;
                    } else
                    {
                        map = getMessageRet(plevel, str);
                    }
                }
            } else
            {
                map.put("isProxy", Boolean.parseBoolean(jsonObj.getString("proxy")));
                map.put("next_ip",
                    jsonObj.getString("proxyIpOut").substring(2, jsonObj.getString("proxyIpOut").length() - 2));
                if (jsonObj.containsKey("switchIp"))
                {
                    String switchIp = jsonObj.getString("switchIp");
                    if (null != switchIp && !"".equals(switchIp) && !"null".equals(switchIp))
                    {
                        map.put("next_ip", switchIp.substring(2, switchIp.length() - 2));
                    }
                }

                map.put("next_port", jsonObj.get("serverPort").toString());
            }
        }
        return map;
    }

    public static void main ( String[] args )
    {
        String s = "[\"***********\",\"***********\",\"***********\",\"***********\"]";
        System.out.println(s.substring(1, s.length() - 1));
    }

    public static String setServicePort ( int level, String str )
    {
        JSONObject jsonObj = JsonUtils.strToJson(str);
        // JSONArray json = JSONArray.fromObject(jsonObj.get("nodes"));
        jsonObj.remove("nodes");
        String strSon = setServicePortSon(level, str);
        if (level == 1)
        {
            jsonObj.put("nodes", "[" + strSon + "]");
        } else
        {
            jsonObj.put("nodes", strSon);
        }
        // JSONArray json = JSONArray.fromObject(str); // 首先把字符串转成 JSONArray 对象
        return jsonObj.toString();
    }

    public static String setServicePortSon ( int level, String str )
    {
        int proxyServicePort = 7777;
        try
        {
            proxyServicePort = ProxyEnv.getInstance().getProxyServicePort();
        } catch (UnknownHostException e)
        {
            e.printStackTrace();
        }
        JSONObject jsonObj = JsonUtils.strToJson(str);

        JSONArray json = JSONArray.fromObject(jsonObj.get("nodes"));
        // JSONArray json = JSONArray.fromObject(str); // 首先把字符串转成 JSONArray 对象

        if (null != json && !json.isEmpty())
        {
            JSONObject job = json.getJSONObject(0);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
            int plevel = Integer.parseInt(String.valueOf(job.get("level")));
            if (level == plevel)
            {
                String port = String.valueOf(job.get("proxyPortOut"));
                if (null != port && !"0".equals(port) && !"".equals(port))
                {
                    proxyServicePort = Integer.parseInt(port);
                }
                job.put("serverPort", proxyServicePort);
                return job.toString();
            } else
            {
                String retjson = setServicePortSon(level, job.toString());
                job.remove("nodes");
                job.put("nodes", "[" + retjson + "]");
            }
            return json.toString();
        }
        return null;
    }

    public static String getMessageRetAgent ( String str )
    {
        String agent = "";
        JSONObject jsonObj = JsonUtils.strToJson(str);
        if (null != jsonObj.get("nodes"))
        {
            JSONArray json = JSONArray.fromObject(jsonObj.get("nodes"));
            if (null != json && !json.isEmpty())
            {
                JSONObject job = json.getJSONObject(0);  // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                agent = job.get("proxyIpOut").toString() + ":" + job.get("serverPort").toString();
            }
        } else
        {
            agent = jsonObj.getString("proxyIpOut").substring(2, jsonObj.getString("proxyIpOut").length() - 2) + ":"
                    + jsonObj.get("serverPort").toString();
        }
        return agent;
    }

    /**
     * 封装返回守护线程数据
     * @param level 参数值
     * @param str json 数据
     * @param serverIp IP
     * @param pport 端口
     * @return 返回值
     */

    public static Map getDaemonsMessage ( int level, String str, String serverIp, int pport )
    {
        int proxyServicePort = 7777;
        try
        {
            //返回webserver 端口号9999
            proxyServicePort = ProxyEnv.getInstance().getProxyServicePort();
        } catch (UnknownHostException e)
        {
            e.printStackTrace();
        }
        Map map = new HashMap();
        //将字符串转成JSON并赋值
        JSONObject jsonObj = JsonUtils.strToJson(str);
        JSONArray jsonNodes = JSONArray.fromObject(jsonObj.get("nodes"));
        JSONArray jsonDaemons = JSONArray.fromObject(jsonObj.get("daemons"));
        JSONObject json = JsonUtils.strToJson(jsonDaemons.get(0).toString());
        String port="";
        if (null != jsonNodes && !jsonNodes.isEmpty()) {
            JSONObject jsonObj_0 = JsonUtils.strToJson(jsonNodes.get(0).toString());
            port = String.valueOf(jsonObj_0.get("proxyPortOut"));
            serverIp=String.valueOf(jsonObj_0.get("proxyIpOut"));
        }
        if (pport != 0)
        {
            jsonObj.accumulate("servicePort", pport);
            jsonObj.put("servicePort", pport);
        } else
        {
            if (null != port && !"0".equals(port) && !"".equals(port) && !"null".equals(port))
            {
                proxyServicePort = Integer.parseInt(port);
                pport = Integer.parseInt(port);
                jsonObj.accumulate("servicePort", proxyServicePort);
                jsonObj.put("servicePort", proxyServicePort);
            } else
            {
                jsonObj.accumulate("servicePort", proxyServicePort);
                jsonObj.put("servicePort", proxyServicePort);
                pport = proxyServicePort;
            }
        }
        //封装map参数  并返回
        map.put("ssltype", jsonObj.get("ssltype").toString());
        map.put("level", level);
        if (jsonObj.containsKey("ssl")){
            map.put("ssltype", jsonObj.get("ssl").toString());
        }
        map.put("isProxy", Boolean.parseBoolean(String.valueOf(jsonObj.get("proxy"))));
        map.put("next_ip",String.valueOf(json.get("daemonsIp")));
        map.put("next_port",String.valueOf(json.get("daemonsPort")));
        map.put("serverIp", serverIp);
        map.put("serverPort", pport);
        return map;
    }

}
