package com.ideal.ieai.server.repository.hd.cicd.flow.monitor;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.actExcelToRun.ActExcelToRunData;
import com.ideal.ieai.server.repository.hd.cicd.pojo.CICDServicePoJo;
import com.ideal.ieai.server.repository.hd.cicd.pojo.ConDelTaskMonPojo;
import com.ideal.ieai.server.repository.hd.cicd.pojo.HisCiCdMonitorPojo;
import com.ideal.ieai.server.repository.sus.importexecl.TmpltMagmntException;

/**
   *
   * @ClassName: ConDelTaskMonService
   * @Description: 持续交付,任务监控
   * @author: guibin_zhang
   * @date: 2023年02月14日 11:07:08
   * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
   */
public class ConDelTaskMonService
{
    private static Logger                     _log     = Logger.getLogger(ConDelTaskMonService.class);
    public static ConDelTaskMonService        instance = null;

    // Dao层对象
    private static final ConDelTaskMonManager ctmm     = null;
    // 实例化
    public static ConDelTaskMonService        intance  = null;

    // 创建对象
    public static ConDelTaskMonService getInstance ()
    {
        if (intance == null)
        {
            intance = new ConDelTaskMonService();
        }
        return intance;
    }

    /**
    *
    * @Title: 
    * @Description: 持续交付,任务监控的查询
    * @author: guibin_zhang
    * @date: 2023年02月14日 11:07:08
    */
    public List<Map<String, Object>> queryConDelTaskMon ( ConDelTaskMonPojo condeltaskmon, int type ) throws Exception
    {
        return ctmm.getInstance().queryConDelTaskMon(condeltaskmon, type);
    }

    public Map<String, Object> ciCdMonHalt ( Long[] insIds ) throws SQLException
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean retFlag = ctmm.getInstance().ciCdMonHalt(insIds);
        if (retFlag){
            retMap.put(Constants.COMMON_SUCCESS, true);
            retMap.put(Constants.COMMON_MESSAGE, "<暂停>操作成功!");
        }else {
            retMap.put(Constants.COMMON_SUCCESS, false);
            retMap.put(Constants.COMMON_MESSAGE, "<暂停>操作失败!");
        }
        return retMap;
    }

    public Map<String, Object> ciCdMonGoOn ( Long[] insIds ) throws SQLException
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        boolean retFlag = ctmm.getInstance().ciCdMonGoOn(insIds);
        if (retFlag){
            retMap.put(Constants.COMMON_SUCCESS, true);
            retMap.put(Constants.COMMON_MESSAGE, "继续执行成功!");
        }else {
            retMap.put(Constants.COMMON_SUCCESS, false);
            retMap.put(Constants.COMMON_MESSAGE, "继续执行失败!");
        }
        return retMap;
    }

    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * @DESC 判断是否有运行中的活动   
     * 2023-2-24 
     * @param iid
     * @param type
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean checkRunStep ( Long iid, int type ) throws RepositoryException
    {
        boolean returnValue = false;
        String querySql = "SELECT COUNT(1) AS NUM FROM IEAI_RUNINFO_INSTANCE  WHERE ISTATE = 0    AND IISFAIL = -1   AND IACTTYPE = 1 AND IRUNINSID="
                + iid;

        int countNum = SQLUtil.executeCountSqlLoop(querySql, type);
        if (countNum < 1)
        {
            returnValue = true;
        }
        return returnValue;
    }
    
    public boolean kill ( UserInfo userInfo, ConDelTaskMonPojo condeltaskmon ) throws RepositoryException, SQLException
    {
        return ctmm.getInstance().kill(userInfo,condeltaskmon);
    }

    public Map<String, Object> queryConMonitorStore ( HisCiCdMonitorPojo hisCiCdMonitorPojo, int type ) throws Exception
    {
        return ctmm.getInstance().getTaskMonitor(hisCiCdMonitorPojo, type);
    }

    public Map<String, Object> queryConMonitorFlowJson ( HisCiCdMonitorPojo hisCiCdMonitorPojo, int type ) throws Exception
    {
        return ctmm.getInstance().queryConMonitorFlowJson(hisCiCdMonitorPojo, type);
    }

    public Map<String, Object> queryConMonitorTreeState ( HisCiCdMonitorPojo hisCiCdMonitorPojo, int type ) throws Exception
    {
        return ctmm.getInstance().queryConMonitorTreeState(hisCiCdMonitorPojo, type);
    }

    public Map<String, Object> queryCICDScriptStepStore ( CICDServicePoJo service, int type ) throws TmpltMagmntException
    {
        return ctmm.getInstance().queryCICDScriptStepStore(service, type);
    }

    public Map<String, Object> queryCICDConfigurationStore ( CICDServicePoJo service, int type ) throws TmpltMagmntException
    {
        return ctmm.getInstance().queryCICDConfigurationStore(service, type);
    }

    public Map<String, Object> queryCICDUTStore ( CICDServicePoJo service, int type ) throws TmpltMagmntException
    {
        return ctmm.getInstance().queryCICDUTStore(service, type);
    }

    public Map<String, Object> queryCICDMonSprParamStore ( CICDServicePoJo service, int type ) throws TmpltMagmntException
    {
        return ctmm.getInstance().queryCICDMonSprParamStore(service, type);
    }

    public Map<String, Object> queryConMonitorRunid ( HisCiCdMonitorPojo hisCiCdMonitorPojo, int type )
    {
        return ctmm.getInstance().queryConMonitorRunid(hisCiCdMonitorPojo, type);
    }

    public Map<String, Object> cicdConDelTaskMon_checkRun(Long[] iid, int type) {
        return ctmm.getInstance().cicdConDelTaskMon_checkRun(iid, type);
    }

    public Map<String, Object> cicdConDelTaskMon_checkRunStop(Long[] iid, int type) {
        return ctmm.getInstance().cicdConDelTaskMon_checkRunStop(iid, type);
    }

    public Map<String, Object> cicdchcekExcePower ( Long[] insIds, long userid, int type )
    {
        return ctmm.getInstance().cicdchcekExcePower(insIds,userid ,type);
    }
    public Map<String, Object> cdExceSystemPower ( Long[] insIds, int type )
    {
        return ctmm.getInstance().cdExceSystemPower(insIds ,type);
    }

    public List<ActExcelToRunData> getCICDDestroyDocker(UserInfo userInfo, ConDelTaskMonPojo condeltaskmon)
    {
        return ctmm.getInstance().getCICDDestroyDocker(userInfo,condeltaskmon);
    }

}