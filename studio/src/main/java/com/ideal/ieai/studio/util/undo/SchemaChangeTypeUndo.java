package com.ideal.ieai.studio.util.undo;

import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.undo.AbstractUndoableEdit;

import com.ideal.ieai.core.data.IDataArrayType;
import com.ideal.ieai.core.element.IEAISchemaRef;
import com.ideal.ieai.studio.view.cmdui.CmdManager;
import com.ideal.ieai.studio.view.editorui.ICommonSchemaEditor;

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR> attributable
 * @version 1.0
 */

public class SchemaChangeTypeUndo
    extends AbstractUndoableEdit
{
    public SchemaChangeTypeUndo(ICommonSchemaEditor edit, DefaultMutableTreeNode curNode,
                                int preType,
                                IDataArrayType preArrayType, IEAISchemaRef preRef, int newType,
                                IEAISchemaRef newRef, int typeIndex, IDataArrayType newArrayType,
                                java.util.List childNodeList, java.util.List childDefList)
    {
        _schemaEditor = edit;
        _curNode = curNode;
        _preType = preType;
        _preArrayType = preArrayType;
        _newType = newType;
        _newRef = newRef;
        _newArrayType = newArrayType;
        _preRef = preRef;
        _childNodeList = childNodeList;
        _childDefList = childDefList;
    }

    public void undo()
    {
        super.undo();
        _schemaEditor.changeType(_curNode, _preType, _preRef, _preArrayType, _childNodeList,
                                 _childDefList, false);
        CmdManager.getInstance().enableundoOption();
    }

    public void redo()
    {
        super.redo();
        _schemaEditor.changeType(_curNode, _newType, _newRef, _newArrayType, _childNodeList,
                                 _childDefList, false);
        CmdManager.getInstance().enableundoOption();
    }

    private ICommonSchemaEditor _schemaEditor;
    private DefaultMutableTreeNode _curNode;
    private IDataArrayType _preArrayType;
    private int _newType;
    private IDataArrayType _newArrayType;
    private int _preType;
    private IEAISchemaRef _preRef;
    private IEAISchemaRef _newRef;
    private java.util.List _childNodeList;
    private java.util.List _childDefList;
}
