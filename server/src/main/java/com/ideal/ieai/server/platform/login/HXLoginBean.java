package com.ideal.ieai.server.platform.login;

public class HXLoginBean
{
    //head
    private String org;
    private String oprtr;
    private String authOprtr;
    private String chckOprtr;
    private String chanlCd;
    private String chanlDt;
    private String chanlTm;
    private String chanlSn;
    private String afeCd;
    private String macName;
    private String macKeyName;
    private String macData;
    private String qryFileTrfFlg;
    //body
    private String oprtrNo;
    private String applId;
    private String hld;

    public String getOrg ()
    {
        return org;
    }

    public void setOrg ( String org )
    {
        this.org = org;
    }

    public String getOprtr ()
    {
        return oprtr;
    }

    public void setOprtr ( String oprtr )
    {
        this.oprtr = oprtr;
    }

    public String getAuthOprtr ()
    {
        return authOprtr;
    }

    public void setAuthOprtr ( String authOprtr )
    {
        this.authOprtr = authOprtr;
    }

    public String getChckOprtr ()
    {
        return chckOprtr;
    }

    public void setChckOprtr ( String chckOprtr )
    {
        this.chckOprtr = chckOprtr;
    }

    public String getChanlCd ()
    {
        return chanlCd;
    }

    public void setChanlCd ( String chanlCd )
    {
        this.chanlCd = chanlCd;
    }

    public String getChanlDt ()
    {
        return chanlDt;
    }

    public void setChanlDt ( String chanlDt )
    {
        this.chanlDt = chanlDt;
    }

    public String getChanlTm ()
    {
        return chanlTm;
    }

    public void setChanlTm ( String chanlTm )
    {
        this.chanlTm = chanlTm;
    }

    public String getChanlSn ()
    {
        return chanlSn;
    }

    public void setChanlSn ( String chanlSn )
    {
        this.chanlSn = chanlSn;
    }

    public String getAfeCd ()
    {
        return afeCd;
    }

    public void setAfeCd ( String afeCd )
    {
        this.afeCd = afeCd;
    }

    public String getMacName ()
    {
        return macName;
    }

    public void setMacName ( String macName )
    {
        this.macName = macName;
    }

    public String getMacKeyName ()
    {
        return macKeyName;
    }

    public void setMacKeyName ( String macKeyName )
    {
        this.macKeyName = macKeyName;
    }

    public String getMacData ()
    {
        return macData;
    }

    public void setMacData ( String macData )
    {
        this.macData = macData;
    }

    public String getQryFileTrfFlg ()
    {
        return qryFileTrfFlg;
    }

    public void setQryFileTrfFlg ( String qryFileTrfFlg )
    {
        this.qryFileTrfFlg = qryFileTrfFlg;
    }

    public String getOprtrNo ()
    {
        return oprtrNo;
    }

    public void setOprtrNo ( String oprtrNo )
    {
        this.oprtrNo = oprtrNo;
    }

    public String getApplId ()
    {
        return applId;
    }

    public void setApplId ( String applId )
    {
        this.applId = applId;
    }

    public String getHld ()
    {
        return hld;
    }

    public void setHld ( String hld )
    {
        this.hld = hld;
    }
     
}
