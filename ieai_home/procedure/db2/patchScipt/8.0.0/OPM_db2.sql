--4.7.18
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER' AND COLNAME = 'ICREATETIME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_USER ADD COLUMN ICREATETIME NUMERIC(19)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$
--4.7.19
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_OPERATION_STATUS';
	    IF LI_EXISTS = 1 THEN
	    SET LS_SQL ='DROP TABLE IEAI_COLLECT_OPERATION_STATUS';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	    END IF;
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESGROUP_MODEL';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_RESGROUP_MODEL(IID NUMERIC(19) NOT NULL,IGROUPMESID  NUMERIC(19),IISTRUE INTEGER,CONSTRAINT PK_IEAI_RESGROUP_MODEL PRIMARY KEY(IID))';
                   PREPARE      SQLA FROM LS_SQL;
                   EXECUTE SQLA;
        END     IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_OPERATION_STATUS';
	    IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_COLLECT_OPERATION_STATUS(IID DECIMAL(15) NOT NULL,  ISTATUS smallint,  CONSTRAINT  PK_IEAI_COLLECT_OPE_STATUS PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	    END IF;
	    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AUDIT_CLOB';
	    IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_AUDIT_CLOB ( AUDITID DECIMAL(19,0) NOT NULL, ICONTENT	CLOB(1048576), CONSTRAINT PK_IEAI_AUDIT_CLOB PRIMARY KEY(AUDITID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	    END IF;

END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ROLE' AND COLNAME = 'ICREATETIME';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL = 'ALTER TABLE IEAI_ROLE ADD COLUMN ICREATETIME NUMERIC(19)';
                   PREPARE      SQLA FROM LS_SQL;
                   EXECUTE SQLA;
        END     IF;

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_RESGROUP_MODEL WHERE IID = -1;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_RESGROUP_MODEL (IID,IGROUPMESID,IISTRUE) VALUES(-1,3,1)';
                        PREPARE SQLA FROM LS_SQL;
                        EXECUTE SQLA;
                        commit;
        END IF;
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_COLLECT_OPERATION_STATUS WHERE  IID=10;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_COLLECT_OPERATION_STATUS(IID,ISTATUS) VALUES(10, 0)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
		
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=15;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info6666.png'' where GROUPID=15';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=100;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info85.png'' where GROUPID=100';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=10;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info6667.png'' where GROUPID=10';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	    END	IF;
END
$
--4.7.20

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTINFO_DELETE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_AGENTINFO_DELETE(IAGENTINFO_ID DECIMAL(19) NOT NULL,IEQUIPMENT_OR_VM_ID DECIMAL(19),IAGENT_NAME VARCHAR(255),IAGENT_DES VARCHAR(255),IAGENT_IP VARCHAR(255) NOT NULL,IAGENT_PORT DECIMAL(19),ICLUSTER_ID DECIMAL(19),IDELETE_FLAG DECIMAL(1) DEFAULT 0,IOS_NAME VARCHAR(255),ICOM_NAME VARCHAR(255),ISTART_USER VARCHAR(255),IAGENT_STATE DECIMAL(1),IAGENT_VERSION VARCHAR(100),IAGENT_ACTIVITIES CLOB,IAGENT_ACTIVITY_NUM DECIMAL(19),IAGENTUP_ID DECIMAL(19),IPALIAS DECIMAL(12),IENV_TYPE DECIMAL(2) DEFAULT 1,IAGENT_CKSTATE DECIMAL(1),IAGENT_CKTIME DECIMAL(19),IAGENT_CCHANGE DECIMAL(1),JOURNAL CLOB,IAGENT_IF_SENDMSG DECIMAL(1) DEFAULT 1,IACTNUM DECIMAL(10),IACTNUMMAX DECIMAL(10),ICPUVALUE VARCHAR(255),IMEMVALUE VARCHAR(255),ICHECKTIME VARCHAR(255),ICUSTOM_CMD VARCHAR(1000),ICUSTOM_MESS CLOB,ICREATETIME DECIMAL(19),ICREATEUSER DECIMAL(19),ISSUED DECIMAL(1) DEFAULT 0,IDELETEUSER VARCHAR(255),IDELETETIME DECIMAL(19),IDELETEDESC VARCHAR(255),CONSTRAINT PK_IEAI_AGENTINFO_DELETE PRIMARY KEY(IAGENTINFO_ID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_DOUBLECHECK_COLVALUE' AND INDNAME='IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE INDEX IDX_IEAI_DOUBLECHECK_COLVALUE_WORKITEMID ON IEAI_DOUBLECHECK_COLVALUE (IWORKITEMID)';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EQU_GROUP_BUSINESS';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_EQU_GROUP_BUSINESS (IID DECIMAL(19,0) NOT NULL,EQUGROUPID DECIMAL(19,0),BUSINESSID DECIMAL(19,0),BUSINESSNAME VARCHAR(255),PROTYPE DECIMAL(5,0) ,CONSTRAINT PK_IEAI_EQU_GROUP_BUSINESS PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=145;
	IF LI_EXISTS = 1 THEN
	SET LS_SQL ='UPDATE IEAI_HIGHOPER SET IBUTTONURL=''deleteFormationByCP.do'' WHERE IBUTTONID=145';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=146;
	IF LI_EXISTS = 1 THEN
	SET LS_SQL ='UPDATE IEAI_HIGHOPER SET IBUTTONURL=''saveFormationByCP.do'' WHERE IBUTTONID=146';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
	END IF;
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EXCEL_UT_REVIEW';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_EXCEL_UT_REVIEW(IID DECIMAL(19,0) NOT NULL,IFLOWID DECIMAL(19,0),IACTID DECIMAL(19,0),IACTNAME VARCHAR(255),IACYTYPE DECIMAL(2),IUSERNAME VARCHAR(255),IUSERID DECIMAL(19,0),IREVIEWTAKEOVERTIME DECIMAL(19,0),IREVIEWTIME DECIMAL(19,0),IREVIEWFINISHTIME DECIMAL(19,0),CONSTRAINT PK_IEAI_EXCEL_UT_REVIEW PRIMARY KEY(IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD IEXPBEGINTIME decimal(19,0) default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD IEXPENDTIME decimal(19,0)  default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD IREMARK VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPBEGINTIME decimal(19,0) default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IEXPENDTIME decimal(19,0)  default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IREMARK VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IEXPBEGINTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPBEGINTIME decimal(19,0) default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IEXPENDTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IEXPENDTIME decimal(19,0)  default 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IREMARK';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IREMARK VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	
	END
	$
--4.7.21
    BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE  GROUPID=16;
	    IF	LI_EXISTS = 1 THEN
	    SET	LS_SQL = 'UPDATE  IEAI_GROUPMESSAGE SET IIMG=''images/info171.png'' where GROUPID=16';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE LI_EXISTS  NUMERIC(2);

        
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 20 AND IGROUPMESSGEID=20;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (20,20,''指令操作维护源'',0)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
	   SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_COLLECT_ITEM WHERE IID = -100 ;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_COLLECT_ITEM(IID, NAME, ITEM, WIN, LINUX, AIX, INFO, ISET)  VALUES(-100, ''主动采集对比'', '' '', '' '', '' '', '' '', '' '', 1)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-20;
	    IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADNUM, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-20, 0, ''所有指令操作业务系统'', 0, 0, 0, 0, 0, 0, 20, -20, -20, -20)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		commit;
	    END	IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INUMBERS' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN INUMBERS VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INUMBERS' AND TABNAME='IEAI_AGENTINFO_DELETE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO_DELETE ADD COLUMN INUMBERS VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$
--4.7.22
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_AGENTINFO_CZ';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_CMDB_AGENTINFO_CZ (IID DECIMAL(19) NOT NULL,IHOSTNAME	VARCHAR(255),IIP	VARCHAR(255),IMAC	VARCHAR(255),IMEMSIZE	VARCHAR(255),IDISKSIZE	VARCHAR(255),IOSSYSTEM	VARCHAR(255),IOSARCHITECTURE	VARCHAR(255),IOSVERSION	VARCHAR(255),IOSDISTRO	VARCHAR(255),IOSRELEASE	VARCHAR(255),IINSTANCEID	VARCHAR(255),IAGENTSTATUS	VARCHAR(255),ICPUMODEL	VARCHAR(255),CONSTRAINT PK_CMDB_AGENTINFO_CZ PRIMARY KEY (IID)) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IREVIEWER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN  IREVIEWER VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IREVIEWER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN  IREVIEWER VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IREVIEWER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IREVIEWER VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IBANKNAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN  IBANKNAME VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IBANKNAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN  IBANKNAME VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IBANKNAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IBANKNAME VARCHAR(255)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_ROLE_RELATION';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_USER_ROLE_RELATION(IID  DECIMAL(19) NOT NULL,  IUSERID  DECIMAL(19),  IROLEID  DECIMAL(19),  IVALIDSTARTTIME DECIMAL(19),  IVALIDENDTIME   DECIMAL(19),  IOPERUSERID     DECIMAL(19),  IOPERTIME   DECIMAL(19),  CONSTRAINT PK_IEAI_USER_ROLE_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;


		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME='IREVIEWSTATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN  IREVIEWSTATE INTEGER default 1 ' ;
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF; 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME='IREVIEWSTATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN  IREVIEWSTATE INTEGER default 1 ';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME='IREVIEWSTATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IREVIEWSTATE INTEGER default 1 ';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPWDFORAPPLY' AND TABNAME='IEAI_USER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_USER ADD COLUMN IPWDFORAPPLY VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		  

END
$
--4.7.23
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IBASEPASSWORD' AND TABNAME='IEAI_USER';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_USER ADD COLUMN IBASEPASSWORD VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT	COUNT(IID) INTO LI_EXISTS FROM  IEAI_PROPERTYCONFIG P WHERE P.IPROPERTYNAME='AutoChangePublicPasswordTime';
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PROPERTYCONFIG(IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) VALUES(2,''AutoChangePublicPasswordTime'',''30'',''浦发自动变更public密码署时长设置'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAPPNAME' AND TABNAME='IEAI_CMDB_AGENTINFO_CZ';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_CMDB_AGENTINFO_CZ ADD IAPPNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA; 
		END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISAPP' AND TABNAME='IEAI_MENU';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_MENU ADD COLUMN IISAPP INTEGER DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA; 
	END IF;	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_GRID_INFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_GRID_INFO(IID DECIMAL(19) NOT NULL ,IGRIDNAME VARCHAR(255) ,ICOLNAME VARCHAR(255) ,IATTRIBUTE VARCHAR(255) ,IATTRVALUE VARCHAR(255) ,CONSTRAINT PK_IEAI_GRID_INFO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$
--4.7.24
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
   
        
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_IPMPSERVICEINFO';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_IPMPSERVICEINFO(  IID DECIMAL(19,0) NOT NULL,  IRESOURCENAME VARCHAR(255),  IURL          VARCHAR(1000),  IUSERNAME     VARCHAR(255),  IPASSWORD     VARCHAR(255),  ISCOMMON      DECIMAL(2) DEFAULT 0,  ICREATEUSERID DECIMAL(19,0),  IUPDATEUSERID DECIMAL(19,0),  ICREATETIME   TIMESTAMP(6),  IUPDATETIME   TIMESTAMP(6) DEFAULT CURRENT TIMESTAMP,  IDELETE       DECIMAL(2) DEFAULT 0,  CONSTRAINT PK_IEAI_IPMPSERVICEINFO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROXY_LIST' AND COLNAME = 'ISTATE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_PROXY_LIST ADD ISTATE DECIMAL (2, 0)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SHOW_SERVERINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SHOW_SERVERINFO(IID DECIMAL(19) NOT NULL ,ILISTID DECIMAL(19) ,ICPU VARCHAR(255) ,IMEMORY VARCHAR(255) ,IDISK VARCHAR(255) ,ITASKNUM DECIMAL(19) ,ICREATETIME TIMESTAMP(6) ,CONSTRAINT PK_IEAI_SHOW_SERVERINFO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SHOW_SERVERLIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SHOW_SERVERLIST(IID DECIMAL(19) NOT NULL ,IIP VARCHAR(255) ,IDESC VARCHAR(255) ,ISTARTTIME DECIMAL(19) ,ICREATETIME DECIMAL(19) ,CONSTRAINT PK_IEAI_SHOW_SERVERLIST PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_PORTAL_MIDDLE';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_USER_PORTAL_MIDDLE(IID DECIMAL(19) NOT NULL ,USERID DECIMAL(19) NOT NULL ,PROTALID DECIMAL(19) NOT NULL ,IROW DECIMAL(2) NOT NULL ,ICOLUMN DECIMAL(3) NOT NULL ,POSITION DECIMAL(1) DEFAULT 1 ,CONSTRAINT PK_IEAI_USER_PORTAL_MIDDLE PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORTAL';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_PORTAL(IID DECIMAL(19) NOT NULL ,TITLE VARCHAR(225) NOT NULL ,NAME VARCHAR(225) NOT NULL ,DATAURL VARCHAR(100) ,FIELDURL VARCHAR(100) ,ITYPE DECIMAL(2) NOT NULL ,DES VARCHAR(225) ,CONSTRAINT PK_IEAI_PORTAL PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PORTALCLO' AND TABNAME='IEAI_USER';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_USER ADD COLUMN PORTALCLO DECIMAL(1)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IEFFECT' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IEFFECT DECIMAL(1) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_DETAIL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'CREATE TABLE IEAI_AGENT_DETAIL  ( IID DECIMAL(19,0) NOT NULL,AGENTIP    	VARCHAR(255),AGENTPORT  	DECIMAL(19,0),ICREATETIME	DECIMAL(19,0),AGENTDIR   	VARCHAR(255),DISKSIZE   	VARCHAR(255),LOGLOCATION	VARCHAR(255),CPURATE    	VARCHAR(255),MEMORYRATE 	VARCHAR(255),IOMESS     	VARCHAR(255),CONSTRAINT PK_IEAI_AGENT_DETAIL PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTSYSTEM_PERSONINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_AGENTSYSTEM_PERSONINFO(IID DECIMAL(19) NOT NULL ,ISYSNAME VARCHAR(255) ,IPERSONNAME VARCHAR(255) ,IEMAIL VARCHAR(255) ,IPHONE VARCHAR(255) ,CONSTRAINT PK_IEAI_AGENTSYSTEM_PERSONINFO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	

END
$
--4.7.25
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_ENTITY_MODEL';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_ENTITY_MODEL(IID DECIMAL(19) NOT NULL,ENAME VARCHAR(255),EID VARCHAR(255),ESYSCODING VARCHAR(255),EPARCODING VARCHAR(255),EDESC VARCHAR(255),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_ENTITY_MODEL PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_EQUIPMENT(IID DECIMAL(19) NOT NULL,EID VARCHAR(255),EIUPDTIME VARCHAR(50),ESYSCODING VARCHAR(255),EIROOTMARK DECIMAL(19),EIVERSION DECIMAL(10),EISMALLVERSION DECIMAL(10),EIDESC VARCHAR(255),EISTATUS DECIMAL(4),EIINSDATE VARCHAR(50),EIIP VARCHAR(18),EINAME VARCHAR(255),EIID DECIMAL(19),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUIPMENT PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_EQUI_RELATION';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_EQUI_RELATION(IID DECIMAL(19) NOT NULL,RLCODE VARCHAR(255),RLCATEGORY DECIMAL(5),FROMEID VARCHAR(255),TOEID VARCHAR(255),RLDESC VARCHAR(255),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_EQUI_RELATION PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_FINAL_RELATION(IID DECIMAL(19) NOT NULL,RLID DECIMAL(19),RLCODE VARCHAR(255),TOEIID DECIMAL(19),RLCATEGORY DECIMAL(4),FROMEIID DECIMAL(19),UPDATETIME VARCHAR(50),EIROOMARK VARCHAR(255),IINSTIME DECIMAL(19),IINSUSER VARCHAR(255),IUPDATETIME DECIMAL(19),IUPDATEUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_FINAL_RELATION PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDB_IG_SYS_RELATION';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_CMDB_IG_SYS_RELATION(IID DECIMAL(19) NOT NULL,CMDBEIID DECIMAL(19),IPRJUPPERID DECIMAL(19),IUPDATETIME DECIMAL(19),IOPERUSER VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_IG_SYS_RELATION PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
END
	 $
	 
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUI_RELATION' AND INDNAME='IDX_CMDB_IG_EQUI_RELATION_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUI_RELATION_01 ON IEAI_CMDB_IG_EQUI_RELATION(RLCODE)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION' AND INDNAME='IDX_CMDB_IG_FINAL_RELATION_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_01 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,TOEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION' AND INDNAME='IDX_CMDB_IG_FINAL_RELATION_02';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_02 ON IEAI_CMDB_IG_FINAL_RELATION(RLCODE,FROMEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT' AND INDNAME='IDX_CMDB_IG_EQUIPMENT_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_01 ON IEAI_CMDB_IG_EQUIPMENT(EIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_ENTITY_MODEL' AND INDNAME='IDX_CMDB_IG_ENTITY_MODEL_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_01 ON IEAI_CMDB_IG_ENTITY_MODEL(ESYSCODING)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_ENTITY_MODEL' AND INDNAME='IDX_CMDB_IG_ENTITY_MODEL_02';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_ENTITY_MODEL_02 ON IEAI_CMDB_IG_ENTITY_MODEL(ENAME)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT' AND INDNAME='IDX_CMDB_IG_EQUIPMENT_02';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_02 ON IEAI_CMDB_IG_EQUIPMENT(ESYSCODING)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_FINAL_RELATION' AND INDNAME='IDX_CMDB_IG_FINAL_RELATION_03';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_FINAL_RELATION_03 ON IEAI_CMDB_IG_FINAL_RELATION(FROMEIID,TOEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_SYS_RELATION' AND INDNAME='IDX_CMDB_IG_SYS_RELATION_01';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_SYS_RELATION_01 ON IEAI_CMDB_IG_SYS_RELATION(IPRJUPPERID,CMDBEIID)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CMDB_IG_EQUIPMENT' AND INDNAME='IDX_CMDB_IG_EQUIPMENT_03';
		IF	LI_EXISTS = 0 THEN
			SET LS_SQL  = 'CREATE INDEX IDX_CMDB_IG_EQUIPMENT_03 ON IEAI_CMDB_IG_EQUIPMENT (EIID, EIIP, EINAME)';
			PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
	END	IF;
	
END
	 $

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SEGMENT' AND COLNAME = 'IFUNCTIONALTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SEGMENT ADD IFUNCTIONALTYPE INTEGER WITH DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_INACRIVE_NUM' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO  ADD IAGENT_INACRIVE_NUM  DECIMAL(19) DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_CLASSIFY';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_COLLECTION_CLASSIFY(IID DECIMAL(19,0) NOT NULL, ICLASSNAME VARCHAR(255),  DES VARCHAR(1000),  CONSTRAINT PK_IEAI_COLLECTION_CLASSIFY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISPARAMETER' AND TABNAME='IEAI_COLLECT_ITEM';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_COLLECT_ITEM ADD COLUMN ISPARAMETER DECIMAL(2) DEFAULT 1';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IREMARK' AND TABNAME='IEAI_COLLECT_DICTIONARY';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COLLECT_DICTIONARY ADD COLUMN IREMARK VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CLASSID' AND TABNAME='IEAI_COLLECT_DICTIONARY';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COLLECT_DICTIONARY ADD COLUMN CLASSID DECIMAL(2) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;


END
$



--4.7.26
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BUSI_MODULE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BUSI_MODULE(IMODULEID DECIMAL(19) NOT NULL ,IMODULECODE VARCHAR(30) ,IMODULENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG DECIMAL(1),CONSTRAINT PK_IEAI_BUSI_MODULE PRIMARY KEY (IMODULEID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BUSI_TYPE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BUSI_TYPE(ITYPEID DECIMAL(19) NOT NULL ,ITYPECODE VARCHAR(30) ,ITYPENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG DECIMAL(1),CONSTRAINT PK_IEAI_BUSI_TYPE PRIMARY KEY (ITYPEID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BUSI_LEVEL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BUSI_LEVEL(ILEVELID DECIMAL(19) NOT NULL ,ILEVELCODE VARCHAR(30) ,ILEVELNAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG DECIMAL(1),CONSTRAINT PK_IEAI_BUSI_LEVEL PRIMARY KEY (ILEVELID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_SCENE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_WARN_SCENE(ISCENEID DECIMAL(19) NOT NULL ,IMODULECODE VARCHAR(30) ,ITYPECODE VARCHAR(30) ,ILEVELCODE VARCHAR(30) ,ISTATUS DECIMAL(1) DEFAULT 0,CONSTRAINT PK_IEAI_WARN_SCENE PRIMARY KEY (ISCENEID) )';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_WARN( IWARNID DECIMAL(19) NOT NULL, IMODULECODE VARCHAR(30),ITYPECODE VARCHAR(30), ILEVELCODE VARCHAR(30), IIP VARCHAR(30), IHAPPENTIME TIMESTAMP, IWARNMSG VARCHAR(4000), CREATETIME TIMESTAMP, CONSTRAINT PK_IEAI_WARN PRIMARY KEY (IWARNID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
        
        
        
        
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
		CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PORTAL');
		commit;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DATAURL' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 1 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ALTER  COLUMN DATAURL  drop not null';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF; 
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FIELDURL' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 1 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ALTER  COLUMN FIELDURL  drop not null';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF; 
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITYPE' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 1 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ALTER  COLUMN ITYPE  drop not null';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PORTAL');
			commit;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CUSTOMCLASS' AND TABNAME='IEAI_PORTAL';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='ALTER TABLE IEAI_PORTAL ADD COLUMN CUSTOMCLASS VARCHAR(225)';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			CALL SYSPROC.ADMIN_CMD('reorg table IEAI_PORTAL'); 
			commit;
END
$
BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_QUICK_MIDDLE';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_USER_QUICK_MIDDLE(IID DECIMAL(19) NOT NULL ,QUICKID DECIMAL(19) ,USERID DECIMAL(19),ISNODEF DECIMAL(2) ,ICON VARCHAR(255),CONSTRAINT PK_IEAI_USER_QUICK_MIDDLE PRIMARY KEY (IID) )';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NOTICE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NOTICE_MANAGE(IID DECIMAL(19) NOT NULL,NOTICENAME VARCHAR(225),NOTICECONTENT VARCHAR(3000),ISTATE DECIMAL(2) DEFAULT 1,TAKEEFFECTTIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,INVALIDTIME TIMESTAMP,USERID DECIMAL(19) NOT NULL,CONSTRAINT PK_IEAI_NOTICE_MANAGE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PLATFORM_NEWS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_PLATFORM_NEWS(IID DECIMAL(19) NOT NULL,NEWSSOURCE DECIMAL(2),NEWCONTENT VARCHAR(2000),ICREATETIME TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,USERID DECIMAL(19) NOT NULL,IENDTIME TIMESTAMP(6),PROJECTID DECIMAL(19),CONSTRAINT PK_IEAI_PLATFORM_NEWS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ORGMANAGEMENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_ORGMANAGEMENT(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,IPARENTID DECIMAL(19) ,CONSTRAINT PK_IEAI_ORGMANAGEMENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ORGMANAGEMENT_USER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_ORGMANAGEMENT_USER(IID DECIMAL(19) NOT NULL ,MANAGEMENTID DECIMAL(19) ,USERID DECIMAL(19) ,CONSTRAINT PK_IEAI_ORGMANAGEMENT_USER PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILECLASS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILECLASS(IID DECIMAL(19) NOT NULL ,ICLASSNAME VARCHAR(255) ,ICLASSDESC VARCHAR(255) ,ICREATETIME TIMESTAMP ,IPARENTID DECIMAL(19) ,CONSTRAINT PK_IEAI_FILECLASS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILE_USER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILE_USER(IID DECIMAL(19) NOT NULL ,IFILEID DECIMAL(19) ,IUSERID DECIMAL(19) ,CONSTRAINT PK_IEAI_FILE_USER PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILEINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILEINFO(IID DECIMAL(19) NOT NULL ,ICLASSIDONE DECIMAL(19) ,ICLASSIDTWO DECIMAL(19) ,IFILENAME VARCHAR(255) ,IFILETYPE VARCHAR(255) ,IFILECONTENT BLOB(20971520) ,IFILEVERSION DECIMAL(19) ,IFILEDESC VARCHAR(255) ,IUPLOADTIME TIMESTAMP ,IUPLOADUSERID DECIMAL(19) ,ISTATE DECIMAL(1) ,CONSTRAINT PK_IEAI_FILEINFO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILEINFO_HIS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_FILEINFO_HIS(IID DECIMAL(19) NOT NULL ,IMYFILEID DECIMAL(19) ,ICLASSIDONE DECIMAL(19) ,ICLASSIDTWO DECIMAL(19) ,IFILENAME VARCHAR(255) ,IFILETYPE VARCHAR(255) ,IFILECONTENT BLOB(20971520) ,IFILEVERSION DECIMAL(19) ,IFILEDESC VARCHAR(255) ,IUPLOADTIME TIMESTAMP ,IUPLOADUSERID DECIMAL(19) ,ISTATE DECIMAL(1) ,CONSTRAINT PK_IEAI_FILEINFO_HIS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AUDIT_DATADICTIONARY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AUDIT_DATADICTIONARY(IID DECIMAL(19) NOT NULL,URL VARCHAR(500),MODELTYPE DECIMAL(19),KEYNAME VARCHAR(100),CNNAME VARCHAR(100),DICTIONARYDESC VARCHAR(4000),CONSTRAINT PK_IEAI_AUDIT_DATADICTIONARY PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	
	
	
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PORTAL_TOP';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PORTAL_TOP(IID DECIMAL(19) NOT NULL ,EQUIPMENTNUM DECIMAL(19) ,ONLINEUSERNUM DECIMAL(5) ,PROCESSRELEASE DECIMAL(19) ,SCRIPTNUM DECIMAL(19) ,SYSTEMNUM DECIMAL(19) ,USERLOGINNUM DECIMAL(19) ,CONSTRAINT PK_IEAI_PORTAL_TOP PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	
	
	
	

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DICTIONARY VALUES WHERE DID = 998;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_DICTIONARY VALUES(998,0,998,''消息展示天数'',15)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENTSHELL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_AGENTSHELL(IID DECIMAL(19) NOT NULL, IAGENTINFO_ID DECIMAL(19) NOT NULL, ICHECKTIME DECIMAL(19),ISTATE DECIMAL(1), IMESSAGE CLOB, CONSTRAINT PK_IEAI_AGENTSHELL PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_LIST(IID DECIMAL(19) NOT NULL ,TASKID DECIMAL(19) ,TASKNAME VARCHAR(225) ,STRATEGYTYPE DECIMAL(2) ,INAME VARCHAR(225) ,STATUS VARCHAR(225) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_LIST PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_STATUS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_STATUS(IID DECIMAL(19) NOT NULL ,STATUS VARCHAR(50) ,TOTAL DECIMAL(10) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_STATUS PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_DETAILS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_DETAILS(IID DECIMAL(19) NOT NULL ,IP VARCHAR(225) ,TASKNAME VARCHAR(225) ,TASKID DECIMAL(19) ,STATUS VARCHAR(225) ,CPNAME VARCHAR(225) ,STRATEGYTYPE DECIMAL(1) ,IFLOWID DECIMAL(19) ,ISTARTUSER VARCHAR(225) ,IEXECUSER VARCHAR(225) ,PERFORMUSER VARCHAR(225) ,CONSTRAINT PK_IEAI_STANDA_SCREEN_DETAILS PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''1'', ''日常操作-执行中的运维监控'', ''collectEnvironment'', ''getDmRunningList.do'', ''getColurltaskMinitor.do'', ''1'', ''日常操作-执行中的运维监控'', NULL)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''2'', ''日常操作-待执行的运维监控'', ''taskMonitor'', ''getDmNotRunList.do'', ''getTaskMontior.do'', ''1'', ''日常操作-待执行的运维监控'', NULL)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=3;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''3'', ''标准运维统计'', ''standardLineChart'', NULL, NULL, ''0'', NULL, ''Ext.app.StandardLineChart'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PORTAL WHERE IID=4;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PORTAL(IID, TITLE, NAME, DATAURL, FIELDURL, ITYPE, DES, CUSTOMCLASS) VALUES (''4'', ''脚本服务化'', ''scriptServerLineChart'', NULL, NULL, ''0'', NULL, ''Ext.app.ScriptServerLineChart'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	COMMIT;
	

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (1,''定时任务'',''timetask'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (2,''脚本服务化'',''scriptservice'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (3,''巡检'',''hc'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (4,''作业调度'',''IEAI'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (5,''应用变更'',''SUS'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (6,''平台管理'',''platform'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;



	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (1,''执行超时'',''timetasktimeout'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (2,''定时任务执行Agent无法连接'',''timetaskagentnotconnect'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (3,''脚本服务化执行Agent无法连接'',''scriptserviceagentnotconnect'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (4,''巡检Agent无法连接'',''hcagentnotconnect'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (5,''作业调度系统异常'',''IEAI_SYSTEMEXCEPTION'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (6,''作业调度业务异常'',''IEAI_BUSINESSEXCEPTION'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (7,''应用变更代理连接异常'',''SUS_AGENTREFUSE'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (8,''应用变更PubAgent连接异常'',''SUS_PUBAGENTREFUSE'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=9;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (9,''应用变更业务系统异常'',''SUS_BUSINESSEXCEPTIONS'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=10;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (10,''Agent连接异常'',''agentnotconnect'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (1,''五级'',''5'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;





	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (1,''timetask'',''timetasktimeout'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (2,''timetask'',''timetaskagentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (3,''scriptservice'',''scriptserviceagentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (4,''hc'',''hcagentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (5,''IEAI'',''IEAI_SYSTEMEXCEPTION'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (6,''IEAI'',''IEAI_BUSINESSEXCEPTION'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (7,''SUS'',''SUS_AGENTREFUSE'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (8,''SUS'',''SUS_PUBAGENTREFUSE'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=9;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (9,''SUS'',''SUS_BUSINESSEXCEPTIONS'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=10;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (10,''platform'',''agentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_SCENE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_SCENE'',11)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =11 WHERE ICLASSNAME=''IEAI_WARN_SCENE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_MODULE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_MODULE'',6)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =6 WHERE ICLASSNAME=''IEAI_BUSI_MODULE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_TYPE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_TYPE'',11)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =11 WHERE ICLASSNAME=''IEAI_BUSI_TYPE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_LEVEL';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_LEVEL'',2)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =2 WHERE ICLASSNAME=''IEAI_BUSI_LEVEL''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ILOGINNAME' AND TABNAME='IEAI_GRID_INFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_GRID_INFO ADD COLUMN ILOGINNAME VARCHAR(255) ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ITYPE' AND TABNAME='IEAI_GRID_INFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_GRID_INFO ADD COLUMN ITYPE DECIMAL(1) ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWIDTH' AND TABNAME='IEAI_GRID_INFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_GRID_INFO ADD COLUMN IWIDTH VARCHAR(10) ';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_ENVIRONMENT';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_COLLECT_ENVIRONMENT(IID DECIMAL(14) NOT NULL,  INAME VARCHAR(32),  IP VARCHAR(25),  CONSTRAINT  PK_COLLECT_ENVIRONMENT PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='HOSTNAME' AND TABNAME='IEAI_AUDIT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AUDIT ADD COLUMN HOSTNAME VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_STANDA_SCREEN_CHART';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_STANDA_SCREEN_CHART(IID DECIMAL(19) NOT NULL ,TOTALFLWO DECIMAL(19) ,TORUNFLWO DECIMAL(19) ,ENDFLWO DECIMAL(19) ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,CONSTRAINT PK_IEAI_STANDA_SCREEN_CHART PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;


END
$
--4.7.27
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENCYTASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_AGENCYTASK (IID DECIMAL(19) NOT NULL, SUM_NO VARCHAR(255),SUM_REMARK VARCHAR(255),SUM_CONTENT VARCHAR(255),MODEL_TYPE VARCHAR(255),MODEL_NAME VARCHAR(255),OPER_ID VARCHAR(255),USER_ID VARCHAR(255),CALL_ID VARCHAR(255),CALL_NAME VARCHAR(255),CALL_DEPT VARCHAR(255), CREATE_TIME VARCHAR(255), DUE_TIME VARCHAR(255),TX_TIME VARCHAR(255),TX_CODE VARCHAR(255),TRADE_CODE VARCHAR(255),CLI_SERIAL_NO VARCHAR(255),TERMI_MARK VARCHAR(255),MAC_NAME VARCHAR(255),MAC_KEY_NAME VARCHAR(255),MAC_DATA VARCHAR(255),RESERVE VARCHAR(255),AUDIT_STATE DECIMAL(2),EXEC_STATE DECIMAL(2),RECORD0_CNT VARCHAR(255),RECORD0 VARCHAR(255),RECORD1_CNT VARCHAR(255),RECORD1 VARCHAR(255),CONSTRAINT PK_IEAI_AGENCYTASK PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BATCH_UPGRADE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_BATCH_UPGRADE(IID DECIMAL(19) NOT NULL ,ISRCPATH VARCHAR(255) ,IDESTPATH VARCHAR(255) ,IBAKPATH VARCHAR(255) ,IOSTYPE DECIMAL(1) ,CONSTRAINT PK_IEAI_BATCH_UPGRADE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=29;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(29,''batchUpgradeFtpInfo'',''-1'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GROUPNAME' AND TABNAME='IEAI_AUDIT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AUDIT ADD COLUMN GROUPNAME VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INSTANCETIME' AND TABNAME='IEAI_STANDARD_TASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_STANDARD_TASK ADD COLUMN INSTANCETIME DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INSTANCETIME' AND TABNAME='TEMP_IEAI_DM_TASKLIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE TEMP_IEAI_DM_TASKLIST ADD COLUMN INSTANCETIME DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTEPIDENTITY' AND TABNAME='IEAI_INSTANCEINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN ISTEPIDENTITY VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_DETAIL';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_DETAIL(IID DECIMAL(19) NOT NULL ,ISENCEID DECIMAL(19) ,IWARNTYPE VARCHAR(255) ,ITYPE DECIMAL(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_WARN_DETAIL PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_RELATION';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_USER_RELATION(IID DECIMAL(19) NOT NULL ,IUSERID DECIMAL(19) ,ISENCEID DECIMAL(19) ,CONSTRAINT PK_IEAI_USER_RELATION PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSTEMNAME' AND TABNAME='IEAI_WARN';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='ALTER TABLE IEAI_WARN ADD COLUMN ISYSTEMNAME VARCHAR(255) ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (2,''一级'',''1'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (3,''二级'',''2'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (4,''三级'',''3'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (5,''四级'',''4'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=11;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (11,''巡检结果出现1级告警'',''ALARM-1'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=12;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (12,''巡检结果出现2级告警'',''ALARM-2'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=13;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (13,''巡检结果出现3级告警'',''ALARM-3'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=14;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (14,''巡检结果出现4级告警'',''ALARM-4'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=15;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (15,''应急操作执行异常'',''epexecerror'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=16;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (16,''灾备切换步骤异常'',''eswitch_BUSINESSEXCEPTIONS'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=17;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (17,''灾备切换Agent连不上'',''eswitch_AGENTREFUSE'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=18;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (18,''server宕机'',''serverexceptiondown'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=19;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (19,''数据库异常'',''DBConnection'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=20;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (20,''巡检结果出现5级告警'',''ALARM-5'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (7,''应急操作'',''ep'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (8,''灾备切换'',''eswitch'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=11;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (11,''hc'',''ALARM-1'',''1'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=12;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (12,''hc'',''ALARM-2'',''2'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=13;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (13,''hc'',''ALARM-3'',''3'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=14;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (14,''hc'',''ALARM-4'',''4'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=15;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (15,''ep'',''epexecerror'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=16;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (16,''eswitch'',''eswitch_BUSINESSEXCEPTIONS'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=17;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (17,''eswitch'',''eswitch_AGENTREFUSE'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=18;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (18,''platform'',''serverexceptiondown'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=19;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (19,''platform'',''DBConnection'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=20;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (20,''hc'',''ALARM-5'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_SCENE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_SCENE'',21)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =21 WHERE ICLASSNAME=''IEAI_WARN_SCENE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_MODULE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_MODULE'',9)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =9 WHERE ICLASSNAME=''IEAI_BUSI_MODULE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_TYPE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_TYPE'',21)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =21 WHERE ICLASSNAME=''IEAI_BUSI_TYPE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_LEVEL';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_LEVEL'',6)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =6 WHERE ICLASSNAME=''IEAI_BUSI_LEVEL''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	COMMIT;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBINFO';
		 IF LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE TABLE IEAI_CMDBINFO (
						IID DECIMAL(19) NOT NULL,
						IINSTANCEID  Varchar(255),
						IIP 	Varchar(255),
						IHOSTNAME Varchar(255),
						IPORT	Varchar(50),
						IOPERATESYSTEM	Varchar(255),
						ISYSTEMVERSION	Varchar(255),
						ICPU	Varchar(255),
						IARCHITECTURE	Varchar(255),
						IPHYSICALCORES	Varchar(10),
						ILOGICALCORES	Varchar(10),
						IMEMORYSIZE	Varchar(100),
						IEQUIPMENT  Varchar(255),
						IOUTBANDIP 	Varchar(255),
					    IAGENTSTATUS VARCHAR(255),
					    IEQUTYPE VARCHAR(2),
						CONSTRAINT PK_IEAI_CMDBINFO PRIMARY KEY (IID)
					)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBCLOB_LOCAL';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBCLOB_LOCAL  (
						   IID DECIMAL(19) NOT NULL,   
						   ICMDBINFOID Varchar(19), 
						   IINSTANCEID  Varchar(19),  
						   ICOLUMNNAME Varchar(255), 
						   ITYPE   Varchar(255), 
						   INULL INTEGER,  
						   ICONTENT CLOB,  
						   IAID DECIMAL(19),  
						   ICPID DECIMAL(19),   
						   IEQUTYPE VARCHAR(2), 
						CONSTRAINT PK_IEAI_CMDBCLOB_LOCAL PRIMARY KEY (IID)
						)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;   	 

			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBCLOB';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBCLOB  (
					   IID DECIMAL(19) NOT NULL,   
					   ICMDBINFOID Varchar(19), 
					   IINSTANCEID  Varchar(19),  
					   ICOLUMNNAME Varchar(255), 
					   ITYPE   Varchar(255), 
					   INULL INTEGER,  
					   ICONTENT CLOB,  
					   IAID DECIMAL(19),  
					   ICPID DECIMAL(19),   
					   IEQUTYPE VARCHAR(2), 
					CONSTRAINT PK_IEAI_CMDBCLOB PRIMARY KEY (IID)
					)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;  

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBCLOB_SUS';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBCLOB_SUS  (
						   IID DECIMAL(19) NOT NULL, 
						   INULL INTEGER,  
						   ICONTENT CLOB , 
						CONSTRAINT PK_IEAI_CMDBCLOB_SUS PRIMARY KEY (IID)
						)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF;  
		 
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CMDBJSON';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_CMDBJSON  (
						   IID DECIMAL(19) NOT NULL, 
						   INULL INTEGER,  
						   ICONTENT CLOB , 
						CONSTRAINT PK_IEAI_CMDBJSON PRIMARY KEY (IID)
						)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		 END IF; 

END
$

--4.7.28

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER_QUICK_MIDDLE' AND COLNAME='IORDER';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_USER_QUICK_MIDDLE ADD IORDER DECIMAL(2)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	 END IF;
	 
	 	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AUDIT_DATADICTIONARY' AND COLNAME='SQLTEXT';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_AUDIT_DATADICTIONARY ADD SQLTEXT VARCHAR(4000)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	 END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IIDARR' AND TABNAME='IEAI_AUDIT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AUDIT ADD COLUMN IIDARR VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CREATETIME' AND TABNAME='IEAI_STANDA_SCREEN_DETAILS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_STANDA_SCREEN_DETAILS ADD COLUMN CREATETIME TIMESTAMP(6)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNCCMDB_INFO';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_SYNCCMDB_INFO (IID DECIMAL(19) NOT NULL,SERVICERUNFLAG	VARCHAR(4),CRONVALUE	VARCHAR(20),SYNC_IP_COUNT    VARCHAR(10),TIMINGJOBFLAG  VARCHAR(4),CMDBTHREADFLAG  VARCHAR(4),CONSTRAINT PK_IEAI_SYNCCMDB_INFO PRIMARY KEY (IID))';
           PREPARE    SQLA FROM LS_SQL;
           EXECUTE SQLA;
        END  IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='RECORD_NUM' AND TABNAME='IEAI_AGENCYTASK';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENCYTASK ADD COLUMN RECORD_NUM VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='RECORD_KEY' AND TABNAME='IEAI_AGENCYTASK';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENCYTASK ADD COLUMN RECORD_KEY VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYNCCMDB_INFO';
        IF      LI_EXISTS = 0 THEN
        SET     LS_SQL ='CREATE TABLE IEAI_SYNCCMDB_INFO (IID DECIMAL(19) NOT NULL,SERVICERUNFLAG	VARCHAR(4),CRONVALUE	VARCHAR(20),SYNC_IP_COUNT    VARCHAR(10),TIMINGJOBFLAG  VARCHAR(4),CMDBTHREADFLAG  VARCHAR(4),CONSTRAINT PK_IEAI_SYNCCMDB_INFO PRIMARY KEY (IID))';
           PREPARE    SQLA FROM LS_SQL;
           EXECUTE SQLA;
        END  IF;

END
$

--4.7.29

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AUDIT_USER' AND TABNAME='IEAI_AGENCYTASK';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENCYTASK ADD COLUMN AUDIT_USER VARCHAR(225)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AUTOCLEARSAVEDAY' AND TABNAME='IEAI_CLEARDATA';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_CLEARDATA ADD COLUMN AUTOCLEARSAVEDAY DECIMAL(19,0) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISAUTOCLEAR' AND TABNAME='IEAI_CLEARDATA';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_CLEARDATA ADD COLUMN ISAUTOCLEAR DECIMAL(19,0) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMSGTYPE' AND TABNAME='IEAI_WARN_DETAIL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WARN_DETAIL ADD COLUMN IMSGTYPE VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_USERS';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_USERS(IID DECIMAL(19) NOT NULL ,IWARNID DECIMAL(19) ,IUSERNAME VARCHAR(255) ,ICONNTYPE VARCHAR(255) ,CONSTRAINT PK_IEAI_WARN_USERS PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_COLUMNS';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_COLUMNS(IID DECIMAL(19) NOT NULL ,ICOLUMNKEY VARCHAR(255) ,ICOLUMNVALUE VARCHAR(255) ,ISCENEID DECIMAL(19) ,ITYPE VARCHAR(255) ,CONSTRAINT PK_IEAI_WARN_COLUMNS PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARN_MESSAGE';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_WARN_MESSAGE(IID DECIMAL(19) NOT NULL ,IWARNID DECIMAL(19) ,ICOLUMNKEY VARCHAR(255) ,ICOLUMNVALUE VARCHAR(255) ,ISCENEID DECIMAL(19) ,IVALUE CLOB ,CONSTRAINT PK_IEAI_WARN_MESSAGE PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ANNOUNCEMENT_USER';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_ANNOUNCEMENT_USER(IID DECIMAL(19) NOT NULL ,USERID DECIMAL(19) ,ITYPE VARCHAR(255)  ,CONSTRAINT PK_IEAI_ANNOUNCEMENT_USER PRIMARY KEY (IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICOMPARERULE' AND TABNAME='IEAI_WARN';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WARN ADD COLUMN ICOMPARERULE VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHOSTNAME' AND TABNAME='IEAI_WARN';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_WARN ADD COLUMN IHOSTNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_GUARDPORT' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_GUARDPORT DECIMAL(19) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(1,''模块名称'',''imodulename'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(2,''异常IP'',''iip'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(3,''告警时间'',''ihappentimeStr'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(4,''报警模块编码'',''imodulecode'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(5,''报警类型编码'',''itypecode'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(6,''用户责任人'',''userList'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(7,''报警信息'',''iwarnmsg'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=18;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(18,''业务系统名称'',''isystemname'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=84;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(84,''告警级别编码'',''ilevelcode'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(8,''任务组名称'',''itaskGroupName'',1,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=9;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(9,''任务组名称'',''itaskGroupName'',2,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=10;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(10,''任务名称'',''itaskName'',1,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=11;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(11,''任务名称'',''itaskName'',2,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=12;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(12,''工程名称'',''iprojectName'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=13;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(13,''工作流名称'',''iworkFlowName'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=14;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(14,''报错步骤名称'',''iErrorStepName'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=15;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(15,''工程名称'',''iprojectName'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=16;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(16,''工作流名称'',''iworkFlowName'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=17;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(17,''报错步骤名称'',''iErrorStepName'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=27;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(27,''方案名称'',''iswitchName'',7,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=28;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(28,''方案名称'',''iswitchName'',8,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=29;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(29,''方案名称'',''iswitchName'',9,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=30;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(30,''子步骤名称'',''isonStepName'',7,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=31;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(31,''子步骤名称'',''isonStepName'',8,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=32;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(32,''子步骤名称'',''isonStepName'',9,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=33;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(33,''服务名称'',''iserviceName'',3,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=34;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(34,''任务名称'',''itaskName'',3,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=39;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(39,''子步骤名称'',''isonStepName'',16,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=40;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(40,''子步骤名称'',''isonStepName'',17,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=42;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(42,''应急预案名称'',''iswitchName'',15,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=43;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(43,''应急预案子步骤'',''isonStepName'',15,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',44)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =44 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_GUARDPORT' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_GUARDPORT DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=23;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES(23,23,''数据采集源'','''','''','''','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_AZNAME' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_AZNAME VARCHAR(255) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_NETID' AND TABNAME='IEAI_AGENTINFO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IAGENT_NETID VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAGENT_NETID' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_COMPUTER_LIST ADD COLUMN IAGENT_NETID VARCHAR(255) default ''(无)'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	
	COMMIT;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=44;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(44,''巡检项名称'',''icheckItem'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=45;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(45,''巡检项名称'',''icheckItem'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=46;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(46,''巡检项名称'',''icheckItem'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=47;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(47,''巡检项名称'',''icheckItem'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=48;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(48,''巡检项名称'',''icheckItem'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=49;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(49,''巡检值'',''icheckValue'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=50;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(50,''巡检值'',''icheckValue'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=51;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(51,''巡检值'',''icheckValue'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=52;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(52,''巡检值'',''icheckValue'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=53;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(53,''巡检值'',''icheckValue'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=54;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(54,''基线值'',''iwarnValue'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=55;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(55,''基线值'',''iwarnValue'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=56;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(56,''基线值'',''iwarnValue'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=57;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(57,''基线值'',''iwarnValue'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=58;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(58,''基线值'',''iwarnValue'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=59;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(59,''巡检点名称'',''icheckObject'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=60;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(60,''巡检点名称'',''icheckObject'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=61;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(61,''巡检点名称'',''icheckObject'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=62;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(62,''巡检点名称'',''icheckObject'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=63;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(63,''巡检点名称'',''icheckObject'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=64;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(64,''设备主机名'',''ihostname'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=65;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(65,''设备主机名'',''ihostname'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=66;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(66,''设备主机名'',''ihostname'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=67;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(67,''设备主机名'',''ihostname'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=68;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(68,''设备主机名'',''ihostname'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=69;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(69,''比较规则'',''icomparerule'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=70;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(70,''比较规则'',''icomparerule'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=71;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(71,''比较规则'',''icomparerule'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=72;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(72,''比较规则'',''icomparerule'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=73;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(73,''比较规则'',''icomparerule'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=74;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(74,''检查点执行配置名称'',''cmdname'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=75;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(75,''检查点执行配置名称'',''cmdname'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=76;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(76,''检查点执行配置名称'',''cmdname'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=77;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(77,''检查点执行配置名称'',''cmdname'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=78;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(78,''检查点执行配置名称'',''cmdname'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=79;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(79,''辅助信息'',''amessage'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=80;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(80,''辅助信息'',''amessage'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=81;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(81,''辅助信息'',''amessage'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=82;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(82,''辅助信息'',''amessage'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=83;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(83,''辅助信息'',''amessage'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',100)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =100 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	
	COMMIT;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PROXY_VMIP';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PROXY_VMIP(IID DECIMAL(19,0) NOT NULL,IPROXYID DECIMAL(19,0) NOT NULL,IUSERID DECIMAL(19,0),IUPDATETIME DECIMAL(19,0),ISWITCHIP VARCHAR(25),ISWITCHOUTIP VARCHAR(25),CONSTRAINT PK_IEAI_PROXY_VMIP PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN

	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COMPUTER_LIST';
		IF LI_EXISTS = 1 THEN
		SET LS_SQL ='DROP INDEX IDX_IEAI_COMPUTER_LIST_01';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COMPUTER_LIST';
		IF LI_EXISTS = 1 THEN
		SET LS_SQL ='CREATE INDEX IDX_IEAI_COMPUTER_LIST_01 ON IEAI_COMPUTER_LIST (IP ASC)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$