Ext.onReady(function() {
	
	Ext.define('cmdbTypeAttributeGrid1Model', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}, {
			name : 'itypeid',
			type : 'string'
		}, {
			name : 'iattributename',
			type : 'string'
		}, {
			name : 'iattributecode',
			type : 'string'
		}, {
			name : 'iattributedes',
			type : 'string'
		}, {
			name : 'iattributetype',
			type : 'string'
		}, {
			name : 'icolumtype',
			type : 'string'
		} , {
			name : 'iattributekey',
			type : 'string'
		} , {
			name : 'iattributeson',
			type : 'string'
		}]
	});
	
	var cmdbTypeAttributeGrid1Store = Ext.create('Ext.data.Store', {
		autoDestroy : true,
		model : 'cmdbTypeAttributeGrid1Model',
		proxy : {
			type : 'ajax',
			url : 'getCmdbTypeAttributeListNoPage.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var cmdbInfoForm1 = Ext.create('Ext.form.Panel', {
		border : false,
		region : 'north',
		title : '',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [ {
				xtype : 'textfield',
				emptyText : '请输入查询条件',
				name : 'queryString',
				listeners: {
		            specialkey: function(field, e){
		                if (e.getKey() == e.ENTER) {
		                	queryCmdbInfo();
		                }
		            }
		        }
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : queryCmdbInfo
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '显示',
				handler : showTopoInfo
			},'->',, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '关闭',
				handler : closeWin
			}]
		} ]
	});
	Ext.define('cmdbInfoGrid1Model', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'string'
		}]
	});
	var cmdbInfoGrid1Store = Ext.create('Ext.data.Store', {
		autoDestroy : true,
		model : 'cmdbInfoGrid1Model',
		pageSize : 30,
		proxy : {
			type : 'ajax',
			url : 'getOtherCmdbInfoList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	
	cmdbInfoGrid1Store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	queryString:cmdbInfoForm1.getForm().findField("queryString").getValue(),
	    	itypeid:itypeid,
	    	hasShowids:hasShowids
	    };
	    Ext.apply(cmdbInfoGrid1Store.proxy.extraParams, new_params);
    });
	
	var pageBar1 = Ext.create ('Ext.PagingToolbar',
	{
	    store : cmdbInfoGrid1Store,
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	var cmdbInfoGrid1 = Ext.create('Ext.grid.Panel', {
		border : false,
		region : 'center',
		title : '',
		selModel : Ext.create('Ext.selection.CheckboxModel', {}),
		columns : [ {
			xtype : 'rownumberer',
			width : 60,
			text : '序号'
		}, {
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iid',
			text : '主键'
		}],
		store : cmdbInfoGrid1Store,
		bbar : pageBar1
	});
	
	
	var cmdbInfoPanel1 = Ext.create('Ext.panel.Panel', {
		region : 'center',
		layout : 'border',
		width:'100%',
		height:'100%',
		title : '',
		renderTo:'initOtherCmdbInfoIndex_div',
		items : [ cmdbInfoForm1, cmdbInfoGrid1 ]
	});

	
	var cmdbInfoDynamicFormItems;
	
	var attributeGridItems = [];
	
	var iattributekeys = "";
	

	attributeGridItems = [];
	
	iattributekeys = "";
	cmdbInfoDynamicFormItems = [ {
		xtype : 'textfield',
		anchor : '100%',
		name : 'iid',
		hidden : true,
		fieldLabel : '主键'
	}, {
		xtype : 'textfield',
		anchor : '100%',
		name : 'itypeid',
		hidden : true,
		fieldLabel : '类型主键'
	}, {
		xtype : 'textfield',
		anchor : '100%',
		name : 'itypename',
		readOnly : true,
		fieldLabel : '类型名称'
	} ];
	
	var fields = [ {
		name : 'iid',
		type : 'string'
	}, {
		name : 'itypename',
		type : 'string'
	} ];
	var columns = [ {
		xtype : 'rownumberer',
		width : 60,
		text : '序号'
	}, {
		xtype : 'gridcolumn',
		hidden : true,
		dataIndex : 'iid',
		text : '主键'
	}];
	cmdbTypeAttributeGrid1Store.load({
		params : {
			itypeid : itypeid
		},
		callback: function(records, operation, success) {
			Ext.Array.each(records,function(record){
				var iattributekey = record.get("iattributekey");
				if(iattributekey=='1'){
					var field = {};
					field.name = record.get("iattributecode");
					field.type = 'string';
					fields.push(field);
					
					var column = {};
					column.xtype = 'gridcolumn';
					column.dataIndex = record.get("iattributecode");
					column.text = record.get("iattributename");
					column.flex = 1;
					columns.push(column);
					
					iattributekeys = iattributekeys +","+record.get("iattributecode");
				}
				var item = {};
				if(record.get("icolumtype")=='文本'){
					item.xtype = 'textfield';
				}
				if(record.get("icolumtype")=='数字'){
					item.xtype = 'numberfield';
				}
				if(record.get("icolumtype")=='时间日期'){
					item.xtype = 'datefield';
				}
				var attributeGrid = {};
				if(record.get("icolumtype")=='表格'){
					attributeGrid.iattributecode = record.get("iattributecode");
					attributeGrid.iattributename = record.get("iattributename");
					attributeGrid.json = record.get("iattributeson");
					attributeGridItems.push(attributeGrid);
				}else{
					item.anchor = '100%';
					item.name = record.get("iattributecode");
					item.fieldLabel = record.get("iattributename");
					cmdbInfoDynamicFormItems.push(item);
				}
				
				
			}) 
			fields.push({
				name : 'istate',
				type : 'string'
			});
			columns.push({
				xtype : 'gridcolumn',
				dataIndex : 'istate',
				text : '状态',
				renderer : function(value){
					var backValue = "<span class='Green_color State_Color'>启用</span>";
					if(value=='1'){
						backValue = "<span class='Blue_color State_Color'>停用</span>";
					}
					return backValue
				}
			});
			
			cmdbInfoGrid1Model.setFields(fields);
			cmdbInfoGrid1.reconfigure( cmdbInfoGrid1Store, columns );
			cmdbInfoGrid1Store.load({
				params :{
					itypeid : itypeid,
					iattributekeys :iattributekeys,
					icmdbinfoid:icmdbinfoid
				}
			});
	    }
	});
    function closeWin(){
    	otherNode_window.close();
    }
	
	
	function queryCmdbInfo(){
		var queryString = cmdbInfoForm1.getForm().findField('queryString').getValue();
		cmdbInfoGrid1Store.load({
			params : {
				itypeid : itypeid,
				iattributekeys :iattributekeys,
				queryString:queryString,
				icmdbinfoid:icmdbinfoid
			}
		});
	}
	function showTopoInfo(){
		var record = cmdbInfoGrid1.getSelectionModel().getSelection();
		if(record.length==0){
			Ext.Msg.alert('提示', '请至少选择一条要显示的记录！');
			return;
		}else{
			
		}
	}
});