<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ConfigReader"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	boolean emApproveSwitch = Environment.getInstance().getBooleanConfig("yc.sms.emergency.audit.switch", false);
%>
<html>
<head>
<script type="text/javascript">
var configinstanceId = <%=request.getParameter("instanceId")%>;
var isAllForbidBtn =<%=ConfigReader.getInstance().getBooleanProperties("em.step.allforbiden", false)%>;
var emApproveSwitch=<%=emApproveSwitch%>;
</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/routinetasks/instanceConfig/eminstance_step_info_instance.js"></script>
</head>
<body>
<div id="instanceConfig_eminstance_step_info_instance_area" style="width: 100%;height: 100%">
</div>
</body>
</html>