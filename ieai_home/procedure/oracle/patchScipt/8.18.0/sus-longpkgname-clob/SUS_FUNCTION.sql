CREATE OR R<PERSON>LACE 
FUNCTION FUN_GET_PKGNAME1  ( IN_ID IN NUMERIC) RETURN CLOB IS
   PRAGMA  AUTONOMOUS_TRANSACTION;
     BEGIN
       DECLARE  l_result  CLOB;
       BEGIN
         FOR cc IN (SELECT
             DISTINCT(T.IPKGNAME) PNAME
           FROM
             IEAI_RUNINFO_INSTANCE_HIS T
           WHERE
             T.IRUNINSID=IN_ID )LOOP
            l_result := l_result || cc.PNAME||','; 
        END LOOP;
       RETURN l_result;
    END;
  END FUN_GET_PKGNAME1;