package com.ideal.ieai.server.repository.programcomparison.createfile;

/**
 * <ul>
 * <li>Title: FileCompareRSBean.java</li>
 * <li>Description:文件比对结果Bean</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2019年4月25日
 */
public class FileCompareRSBean
{
    private boolean flag =false;
    private String msg="";
    private String outputFilePath=""; //输出结果文件路径
    public boolean isFlag ()
    {
        return flag;
    }
    public void setFlag ( boolean flag )
    {
        this.flag = flag;
    }
    public String getMsg ()
    {
        return msg;
    }
    public void setMsg ( String msg )
    {
        this.msg = msg;
    }
    public String getOutputFilePath ()
    {
        return outputFilePath;
    }
    public void setOutputFilePath ( String outputFilePath )
    {
        this.outputFilePath = outputFilePath;
    }
}
