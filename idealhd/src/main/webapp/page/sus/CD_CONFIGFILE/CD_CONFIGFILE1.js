Ext.onReady(function () {
    var isClickCollect = false;
    var taskForUpdateContext = null;
    var taskForUpdateContext1 = null;
    var charCount = null;
    var descCount = null;
    destroyRubbish();
    var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
    /***********************项目概况tab ***********************************************/
    var ifileName = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 20 25 0',
        readOnly: optType == 0 ? false : true,
        afterLabelTextTpl: required,
        name: 'ifilename',
        value: ifilename,
        fieldLabel: '文件名'
    });
    var textFileSysName = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'sysName',
        value: iname,
        readOnly:  true,
        fieldLabel: '业务系统'
    });
    var proNum1 = Ext.create('Ext.form.ComboBox', {
        width: '10%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        editable: false,
        name: 'proNum1',
        hidden: optType == 0 ? false : true,
        displayField: 'text',
        valueField: 'text',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['0', '.properties'], ['1', '.xml'], ['2', '.yml'], ['3', '.txt'], ['4', '.conf'], ['5', '.ini'], ['6', '.cfg'], ['7', '.js'], ['8', '.json'], ['9', '.html'], ['10', '.其它']]
        }),
        listeners: {
            afterrender: function(combo) {
                combo.getStore().on('load', function(store, records) {
                    combo.setValue(records[0].get('text'));
                });
                combo.getStore().load();
            }
        }
        //readOnly:true,
        //name: 'handleTaskName',
        //  value:iprojectNum,
        // fieldLabel: '文件名'
    });

    var ireleaseId = Ext.create('Ext.form.field.Text', {
        // width: '20%',
        // labelWidth: 85,
        // labelAlign: 'right',
        // margin: '0 0 25 0',
        // afterLabelTextTpl: required,
        // displayField: 'envname',
        // valueField: 'ireleaseid',
        // name: 'ireleaseId',
        // readOnly: optType == 0 ? false : true,
        // hidden: optType == 0 ? false : true,
        // value: ireleaseId,
        // fieldLabel: '工单编号'
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 20 25 0',
        readOnly: optType == 0 ? true : true,
        //afterLabelTextTpl: required,
        hidden: optType == 0 ? true : true,
        name: 'ireleaseId',
        value: ireleaseIdValue,
        fieldLabel: '工单编号'
    });

    var proIp = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        afterLabelTextTpl: required,
        name: 'proIp',
        value: iip,
        id:'myagentIdText2',
        readOnly: optType == 1 || optType == 2,
        fieldLabel: 'IP地址',
        listeners: {
            render: function (p) {
                if( !(optType == 1 || optType == 2)) { //编辑配置不可以编辑IP地址
                    p.getEl().on('click', function (p) {
                        //处理点击事件代码
                        //重新
                        var ips = proIp.getValue();
                        var ids = ipIdTextField.getValue();
                        proIpwindow(ips,ids);
                    });
                }
            },
        }
    });

    //用来ip的选择以及回显
    var ipIdTextField = Ext.create('Ext.form.field.Text', {
        width: '33%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        afterLabelTextTpl: required,
        name: 'proIp',
        hidden:true,
        value: iagentIds,
        readOnly: true
    });

    /** 模块类型model* */
    Ext.define('proTypeModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long'
                },
                {
                    name: 'idepName',
                    type: 'string'
                }
            ]
        });

    var proTypeStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'proTypeModel',
        proxy:
            {
                type: 'ajax',
                url: 'getModelType.do?systemcode=' + sysName,
                reader:
                    {
                        type: 'json',
                        root: 'dataList'
                    }
            }
    });


    var proType = Ext.create('Ext.form.field.ComboBox', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        afterLabelTextTpl: required,
        store: proTypeStore,
        editable: false,
        emptyText: '------请选择------',
        name: 'proType',
        value: imodeltype,
        displayField: 'idepName',
        readOnly: optType == 1 || optType == 2,
        valueField: 'iid',
        fieldLabel: '模块类型'
    });

    var departMent = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        afterLabelTextTpl: required,
        name: 'departMent',
        value: ipath,
        readOnly: optType == 0 ? false : true,
        fieldLabel: '分发路径'
    });

    var comPany = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'comPany',
        value: igjpath,
        readOnly: optType == 0 || optType == 1 ? false : true,
        fieldLabel: '构建路径'
    });

    var busDept = Ext.create('Ext.form.field.ComboBox', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        emptyText: '------请选择------',
        afterLabelTextTpl: required,
        name: 'busDept',
        value: ibm,
        readOnly: optType == 0 || optType == 1 ? false : true,
        fieldLabel: '文件编码',
        displayField: 'text',
        valueField: 'text',
        editable: false,
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['0', 'GBK'], ['1', 'UTF-8'], ['2', 'UTF-16'], ['3', 'ANSI'], ['4', 'ASCII'], ['5', 'GB2312'], ['6', 'UNICODE']]
        })
    });

    var iversionCmp = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'iversion',
        value: iversion,
        readOnly:true,
        hidden: OPT_TYPE == '配置详情' ? false :true ,
        fieldLabel: '版本号'
    });

    var iconfigstatusVCmp = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'iconfigstatusV',
        value: iconfigstatusValue,
        readOnly:true,
        hidden: OPT_TYPE == '配置详情' ? false :true ,
        fieldLabel: '状态'
    });

    var ixgrCmp = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'ixgr',
        value: ixgrValue,
        readOnly:true,
        hidden: OPT_TYPE == '配置详情' ? false :true ,
        fieldLabel: '创建人'
    });

    var icreatetimeCmp = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'icreatetime',
        value: icreatetimeValue,
        readOnly:true,
        hidden: OPT_TYPE == '配置详情' ? false :true ,
        fieldLabel: '创建时间'
    });




    var izxxgr = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'izxxgr',
        value: izxxgrvalue,
        readOnly:true,
        hidden: OPT_TYPE == '配置详情' ? false :true ,
        fieldLabel: '最新修改人'
    });

    var iupdatetime = Ext.create('Ext.form.field.Text', {
        width: '20%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'iupdatetime',
        value: iupdatetimevalue,
        readOnly:true,
        hidden: OPT_TYPE == '配置详情' ? false :true ,
        fieldLabel: '修改时间'
    });

    var proExplain = Ext.create('Ext.form.field.Text', {
        width: '49.5%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proExplain',
        value: base64Decode(icontent),
        fieldLabel: '描述',
        readOnly: optType == 0 || optType == 1 ? false : true,
        maxLength: 200,
        maxLengthText: '请输入200位长度的描述',
        //onkeyup:setLength(this,200,'wordslength'),
        enforceMaxLength: true, // 是否强制限制字符串长度，超过最大长度将不能再被输入
    });

    function setLength(obj, maxlength, id) {
        var num = maxlength - obj.value.length;
        var leng = id;
        if (num < 0) {
            num = 0;
        }
        document.getElementById(leng).innerHTML = num + "/200";
    }

    var productAsk = Ext.create('Ext.panel.Panel', {
        labelWidth: 85,
        width: '95%',
        height: contentPanel.getHeight()-500,
        border: false,
        region: 'center',
        collapsible: false,
        title: '<span style="font-size: 14px">配置内容:</span>'+required,
        margin: '0 0 25 25',
        html: '<textarea id="productAsk" style="height:100%;"></textarea>',
    });


    var projectForm = Ext.create('Ext.form.Panel', {
        name: 'projectForm',
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        bodyBorder: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            padding: '0 10 0 0',
            items: ['->',{
                cls: 'Common_Btn',
                text: '采集',
                margin: '0 10 0 0',
                hidden: optType == 2 ||optType == 3 ? true : false,
                handler: function () {
                    var form = projectForm.getForm();
                    var ifilename = ifileName.getValue();//文件
                    var proNum1 = form.findField("proNum1").getValue();//后缀
                    if(iid>0){
                        //清空临时配置内容
                        if(iid>0){
                            deleteICONTENTTEXTTEMP(iid);
                        }
                        if(optType == 0){ //创建配置的二次采集
                            ifilename = ifilename + proNum1;
                        }



                        var proIp = form.findField("proIp").getValue();//IP地址
                        var departMent = form.findField("departMent").getValue();//分发路径
                        var comPany = form.findField("comPany").getValue();//构建路径
                        var busDept = form.findField("busDept").getValue();//文件编码

                        if (ifilename.endsWith("其它")) {
                            var target = "其它";
                            ifilename = ifilename.substring(0, ifilename.length - target.length);
                        }

                        var jsonData ="[{\"iid\":"+iid+",\"iip\":\""+proIp+"\",\"ipath\":\""+departMent+"\",\"ifilename\":\""+ifilename+"\",\"ibm\":\""+busDept+"\"}]";
                       // Ext.MessageBox.wait("配置采集中...", "进度条");
                        var waitMsgBox  = Ext.MessageBox.wait({
                            title :'进度条',
                            msg: "<div style='text-align: center;margin-right: 10px;'>配置采集中...</div>",
                            closable:true,
                            closeAction: 'destroy'
                        });
                        waitMsgBox.on('close', function() {
                            Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务
                        });

                        Ext.Ajax.request({
                            url: 'CICD_pullItem.do',
                            async:false,
                            method: 'POST',
                            params: {
                                jsonDate: jsonData
                            },
                            success: function (response, opts) {
                                var success = Ext.decode(response.responseText).success;
                                var message = Ext.decode(response.responseText).message;
                                if (success) {
                                    var taskStartTime = Ext.Date.now();
                                    taskForUpdateContext1 = Ext.TaskManager.start({
                                        run: function() {
                                            var currentTime = Ext.Date.now(); // 获取当前时间
                                            var elapsedTime = currentTime - taskStartTime; // 计算已经过去的时间
                                            if (elapsedTime >= 300000) { // 如果已经过去的时间超过了设定的超时时间
                                                if(null!=taskForUpdateContext1){
                                                    Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务
                                                }
                                                //Ext.Msg.alert('提示', "配置项同步超时，请检查Agent状态");
                                                //Ext.Msg.alert('提示', "由于Agent上执行任务过多，导致配置项同步超时！");
                                                Ext.Msg.alert('提示', "由于Agent上执行任务过多，导致配置项采集排队，请耐心等待！");


                                            } else {
                                                // 更新文本控件的内容
                                                // 发送异步请求
                                                if(null!=iid && "null" != iid ){
                                                    Ext.Ajax.request({
                                                        url: 'queryContext.do', // 后台接口地址
                                                        params: {
                                                            iid: iid // 要查询的记录ID
                                                        },
                                                        success: function(response) {
                                                            // 解析后台返回的数据
                                                            var data = Ext.decode(response.responseText);
                                                            var success = data.success;
                                                            if(success){//采集成功，临时配置内容已经存在
                                                                var icontenttexttemp = data.icontenttexttemp;
                                                                if("" != icontenttexttemp && "null" != icontenttexttemp && null != icontenttexttemp){ //说明采集成功，临时配置内容已经有内容
                                                                    //停止定时器
                                                                    if(null!=taskForUpdateContext){
                                                                        Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
                                                                    }
                                                                    if(null!=taskForUpdateContext1){
                                                                        Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务
                                                                    }
                                                                    if ( "error" == base64Decode(icontenttexttemp)) {
                                                                        Ext.Msg.alert('提示', "在服务器中找不到此文件，无法采集！");
                                                                    }else if ("isEmpty" == base64Decode(icontenttexttemp)){
                                                                        Ext.Msg.alert('提示', "已采集到配置文件内容为空！");
                                                                    }else if ("fail" == base64Decode(icontenttexttemp)){
                                                                        Ext.Msg.alert('提示', "比对Agent采集内容不一致！");
                                                                    }else {
                                                                        //弹出是否覆盖选择提示框
                                                                        Ext.MessageBox.confirm ('提示', "已采集到配置文件内容，是否覆盖配置内容？", function (btn){
                                                                            if (btn == 'no')
                                                                            {
                                                                                if(iid>0){
                                                                                    deleteICONTENTTEXTTEMP(iid);
                                                                                }
                                                                                return;
                                                                            }
                                                                            if (btn == 'yes')
                                                                            {
                                                                                // 更新文本控件的内容，填充内容
                                                                                // projectForm.getForm().findField("productAsk").setValue(base64Decode(icontenttexttemp));
                                                                                editor.getDoc().setValue(base64Decode(icontenttexttemp));
                                                                                editor.refresh();
                                                                            }
                                                                        });
                                                                    }
                                                                }
                                                            }

                                                        }
                                                    });
                                                }
                                            }
                                        },
                                        interval: 5000 // 每5秒钟执行一次
                                    });
                                } else {
                                    Ext.Msg.alert('提示', message);
                                }
                            },
                            failure: function (result, opts) {
                                result = false;
                                Ext.Msg.alert('提示', '采集失败!');
                            }
                        });
                    }else {
                        //先调用保存方法，然后进行信息采集
                        isClickCollect = true;
                        var form = projectForm.getForm();
                        var ifilename = ifileName.getValue();//文件
                        var proNum1 = form.findField("proNum1").getValue();//后缀
                        var proState = "";//环境
                        var proIp = form.findField("proIp").getValue();//IP地址
                        var proTypeId = form.findField("proType").getValue();//模块类型ID
                        var proType = form.findField("proType").getRawValue();//模块类型
                        var departMent = form.findField("departMent").getValue();//分发路径
                        var comPany = form.findField("comPany").getValue();//构建路径
                        var busDept = form.findField("busDept").getValue();//文件编码
                        var ireleaseid = form.findField("ireleaseId").getValue();//工单编号
                        var proExplain = form.findField("proExplain").getValue();//描述
                        // var productAsk = form.findField("productAsk").getValue();//配置内容
                        editor.save();
                        var productAsk = document.getElementById('productAsk').value;//配置内容
                        if (ifilename == '' || ifilename == null) {
                            Ext.Msg.alert('提示', "请输入文件名");
                            return;
                        }
                        // if (ireleaseid == '' || ireleaseid == null) {
                        //     Ext.Msg.alert('提示', "请输入工单编号");
                        //     return;
                        // }

                        if (proNum1 == '' || proNum1 == null) {
                            Ext.Msg.alert('提示', "请选择文件后缀");
                            return;
                        } else if(proNum1 == '其它'){
                            if(ifilename.indexOf(".")<=0){
                                Ext.Msg.alert('提示', "请填写文件后缀");
                                return;
                            }
                        }else{
                            if(ifilename.indexOf(".")>0){
                                Ext.Msg.alert('提示', "已填写文件后缀时，请选择文件后缀为其他");
                                return;
                            }

                            // ifilename = ifilename + "." + proNum1;
                            ifilename = ifilename + proNum1;
                        }
                        if (proIp == '' || proIp == null) {
                            Ext.Msg.alert('提示', "请选择IP地址...");
                            return;
                        }

                        if (proType == '' || proType == null) {
                            Ext.Msg.alert('提示', "请选择模块类型...");
                            return;
                        }
                        if (departMent == '' || departMent == null) {
                            Ext.Msg.alert('提示', "请输入分发路径...");
                            return;
                        }
                        var reg = /\s/; //
                        if (reg.test(departMent)) {
                            Ext.Msg.alert('提示', "请输入正确的分发路径...");
                            return;
                        }
                        if (busDept == '' || busDept == null) {
                            Ext.Msg.alert('提示', "请选择文件编码...");
                            return;
                        }
                        //Ext.MessageBox.wait("数据处理中...", "进度条");
                        Ext.Ajax.request({
                            url: 'editCI_CONFIGFILE.do',
                            timeout: 30000,
                            params: {
                                ifilename: ifilename,
                                ieai_group_env: proState,
                                iip: proIp,
                                imodeltype: proType,
                                ipath: departMent,
                                igjpath: comPany,
                                ibm: busDept,
                                icontent: proExplain,
                                icontenttext: productAsk,
                                isysname: sysName,
                                iproducetype: "1",
                                imodeltypeId:proTypeId,
                                agentids: ipIdTextField.getValue(),
                                ireleaseid:ireleaseid,
                                iconfigstatus:0,
                                fromClickCollect:true //采集
                            },
                            method: 'POST',
                            success: function (response, opts) {
                                var success = Ext.decode(response.responseText).success;
                                if (success) {
                                    //Ext.Msg.alert('提示', "保存成功！");
                                    var itemId = Ext.decode(response.responseText).itemId;
                                    iid = itemId; //保存成功
                                    //Ext.getCmp("cisaveBunId").hide();

                                    if (ifilename.endsWith("其它")) {
                                        var target = "其它";
                                        ifilename = ifilename.substring(0, ifilename.length - target.length);
                                    }

                                    var jsonData ="[{\"iid\":"+iid+",\"iip\":\""+proIp+"\",\"ipath\":\""+departMent+"\",\"ifilename\":\""+ifilename+"\",\"ibm\":\""+busDept+"\"}]";
                                    // pullItem(jsonData);
                                    //Ext.MessageBox.wait("配置采集中...", "进度条");
                                    var waitWindow1  = Ext.MessageBox.wait({
                                        title :'进度条',
                                        msg: "<div style='text-align: center;margin-right: 10px;'>配置采集中...</div>",
                                        closable:true,
                                        closeAction: 'destroy'
                                    });
                                    waitWindow1.on('close', function() {
                                        Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
                                    });

                                    Ext.Ajax.request({
                                        url: 'CICD_pullItem.do',
                                        method: 'POST',
                                        async: true,
                                        params: {
                                            jsonDate: jsonData
                                        },
                                        success: function (response, opts) {
                                            var success = Ext.decode(response.responseText).success;
                                            var message = Ext.decode(response.responseText).message;
                                            if (success) {
                                                var taskStartTime = Ext.Date.now(); // 记录任务开始时间
                                                taskForUpdateContext = Ext.TaskManager.start({
                                                    run: function() {
                                                        var currentTime = Ext.Date.now(); // 获取当前时间
                                                        var elapsedTime = currentTime - taskStartTime; // 计算已经过去的时间
                                                        if (elapsedTime >= 300000) { // 如果已经过去的时间超过了设定的超时时间
                                                            if(null!=taskForUpdateContext){
                                                                Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
                                                            }
                                                           // Ext.Msg.alert('提示', "配置项同步超时，请检查Agent状态");
                                                           // Ext.Msg.alert('提示', "由于Agent上执行任务过多，导致配置项同步超时！");
                                                            Ext.Msg.alert('提示', "由于Agent上执行任务过多，导致配置项同步排队，请耐心等待！");
                                                        } else {
                                                            // 更新文本控件的内容
                                                            // 发送异步请求
                                                            if(null!=iid && "null" != iid ){
                                                                Ext.Ajax.request({
                                                                    url: 'queryContext.do', // 后台接口地址
                                                                    params: {
                                                                        iid: iid // 要查询的记录ID
                                                                    },
                                                                    success: function(response) {

                                                                        // 解析后台返回的数据
                                                                        var data = Ext.decode(response.responseText);
                                                                        var success = data.success;
                                                                        if(success){//采集成功，临时配置内容已经存在
                                                                            var icontenttexttemp = data.icontenttexttemp;
                                                                            if(icontenttexttemp != ""  && "null" != icontenttexttemp && null != icontenttexttemp){ //说明采集成功，临时配置内容已经有内容
                                                                                //停止定时器
                                                                                if(null!=taskForUpdateContext){
                                                                                    Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
                                                                                }
                                                                                if(null!=taskForUpdateContext1){
                                                                                    Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务

                                                                                }
                                                                                if ( "error" == base64Decode(icontenttexttemp)) {
                                                                                    Ext.Msg.alert('提示', "在服务器中找不到此文件，无法采集！");
                                                                                }else if ("isEmpty" == base64Decode(icontenttexttemp)){
                                                                                    Ext.Msg.alert('提示', "已采集到配置文件内容为空！");
                                                                                }else if ("fail" == base64Decode(icontenttexttemp)){
                                                                                    Ext.Msg.alert('提示', "比对Agent采集内容不一致！");
                                                                                }else {
                                                                                    //弹出是否覆盖选择提示框
                                                                                    Ext.MessageBox.confirm ('提示', "已采集到配置文件内容，是否覆盖配置内容？", function (btn){
                                                                                        if (btn == 'no')
                                                                                        {
                                                                                            return;
                                                                                        }
                                                                                        if (btn == 'yes')
                                                                                        {
                                                                                            // 更新文本控件的内容，填充内容
                                                                                            // projectForm.getForm().findField("productAsk").setValue(base64Decode(icontenttexttemp));
                                                                                            editor.getDoc().setValue(base64Decode(icontenttexttemp));
                                                                                            editor.refresh();
                                                                                        }
                                                                                    });
                                                                                }
                                                                            }
                                                                        }


                                                                    }
                                                                });
                                                            }
                                                        }
                                                    },
                                                    interval: 5000 // 每5秒钟执行一次
                                                });

                                                //return;
                                            } else {
                                                Ext.Msg.alert('提示', message);
                                            }
                                        },
                                        failure: function (result, opts) {

                                            Ext.Msg.alert('提示', '采集失败!');
                                        }
                                    });


                                    // taskForUpdateContext = Ext.TaskManager.start({
                                    //     run: function() {
                                    //         // 更新文本控件的内容
                                    //         // 发送异步请求
                                    //         if(null!=iid && "null" != iid ){
                                    //             Ext.Ajax.request({
                                    //                 url: 'queryContext.do', // 后台接口地址
                                    //                 params: {
                                    //                     iid: iid // 要查询的记录ID
                                    //                 },
                                    //                 success: function(response) {
                                    //
                                    //                     // 解析后台返回的数据
                                    //                     var data = Ext.decode(response.responseText);
                                    //                     var success = data.success;
                                    //                     if(success){//采集成功，临时配置内容已经存在
                                    //                         var icontenttexttemp = data.icontenttexttemp;
                                    //                         if(icontenttexttemp != ""){ //说明采集成功，临时配置内容已经有内容
                                    //                             //停止定时器
                                    //                             if(null!=taskForUpdateContext){
                                    //                                 Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
                                    //                             }
                                    //                             if(null!=taskForUpdateContext1){
                                    //                                 Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务
                                    //
                                    //                             }
                                    //                             //弹出是否覆盖选择提示框
                                    //                             Ext.MessageBox.confirm ('提示', "是否覆盖配置内容!", function (btn){
                                    //                                 if (btn == 'no')
                                    //                                 {
                                    //                                     return;
                                    //                                 }
                                    //                                 if (btn == 'yes')
                                    //                                 {
                                    //                                     // 更新文本控件的内容，填充内容
                                    //                                     projectForm.getForm().findField("productAsk").setValue(base64Decode(icontenttexttemp));
                                    //                                 }
                                    //                             });
                                    //                         }
                                    //                     }
                                    //
                                    //
                                    //                 }
                                    //             });
                                    //         }
                                    //
                                    //     },
                                    //     interval: 5000 // 每5秒钟执行一次
                                    // });

                                } else {
                                    Ext.Msg.alert('提示', "保存失败！");
                                }

                            },
                        });

                        // Ext.Msg.alert('提示', "请保存基础信息后在进行采集！");
                    }

                }
            }]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [textFileSysName,ifileName, proNum1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [ireleaseId, proIp, proType,iconfigstatusVCmp]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [departMent, comPany, busDept,iversionCmp]
        },{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [izxxgr,iupdatetime,ixgrCmp,icreatetimeCmp]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proExplain]
        },{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [productAsk]
        }, {
            xtype: 'toolbar',
            border: false,
            padding: '0 10 0 0',
            items: [{
                cls: 'Common_Btn',
                text: '保存',
                margin: '0 10 0 870',
                id:'cdsaveBunId',
                hidden: optType == 0 ? false : true,
                handler: function () {
                    saveConfigFile();   //配置管理保存
                }
            }, {
                cls: 'Common_Btn',
                text: '更新',
                margin: '0 10 0 870',
                hidden: optType == 1 ? false : true,
                handler: function () {
                    updateConfigFile();   //配置管理编辑
                }
            }, {
                cls: 'Common_Btn',
                text: '取消',
                margin: optType == 2 ||optType == 3  ? '0 10 0 910' : '0 10 0 0',
                handler: function () {
                    //所有取消先去后台清理一下临时存储内容（通过采集操作采集过来的临时提取内容）
                    if(iid>0){ //无论编辑还是创建配置，取消的时候都去清空一下采集内容。
                        deleteICONTENTTEXTTEMP(iid);
                    }
                    if(isClickCollect && optType==0){ //创建配置的取消
                        deleteItemTEMP(iid);
                    }
                    createConfigWinCd.close();
                    //停止定时器
                    if(null!=taskForUpdateContext){
                        Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
                    }
                    if(null!=taskForUpdateContext1){
                        Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务

                    }
                    // contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
                    // queryForm.setTitle("业务系统");//将contentPanel标题显示在查询Form上
                }
            }]
        }]
    });


    var proTab = Ext.create('Ext.Panel', {
        title: OPT_TYPE,
        layout: 'border',
        border: false,
        items: [projectForm]
    });

    function closeThis() {
        createConfigWinCd.close();
        //停止定时器
        if(null!=taskForUpdateContext){
            Ext.TaskManager.stop(taskForUpdateContext); // 停止定时器任务
        }
        if(null!=taskForUpdateContext1){
            Ext.TaskManager.stop(taskForUpdateContext1); // 停止定时器任务

        }
        contentPanel.getLoader().load(
            {
                url: 'accessCD_CONFIGFILE.do',
                params:
                    {sysName: sysName},
                scripts: true
            });
    }

    function saveConfigFile() {
        var form = projectForm.getForm();
        var ifilename = ifileName.getValue();//文件
        var proNum1 = form.findField("proNum1").getValue();//后缀
        var ireleaseId = form.findField("ireleaseId").getValue();//工单编号
        var proIp = form.findField("proIp").getValue();//IP地址
        var proTypeId = form.findField("proType").getValue();//模块类型ID
        var proType = form.findField("proType").getRawValue();//模块类型
        var departMent = form.findField("departMent").getValue();//分发路径
        var comPany = form.findField("comPany").getValue();//构建路径
        var busDept = form.findField("busDept").getValue();//文件编码
        var proExplain = form.findField("proExplain").getValue();//描述
        editor.save();
        var productAsk = document.getElementById('productAsk').value;//配置内容
        if (ifilename == '' || ifilename == null) {
            Ext.Msg.alert('提示', "请输入文件名");
            return;
        }
        if (proNum1 == '' || proNum1 == null) {
            Ext.Msg.alert('提示', "请选择文件后缀");
            return;
        } else if(proNum1 == '其它'){
            if(ifilename.indexOf(".")<=0){
                Ext.Msg.alert('提示', "请填写文件后缀");
                return;
            }
        }else{
            if(ifilename.indexOf(".")>0 ){
                Ext.Msg.alert('提示', "已填写文件后缀时，请选择文件后缀为其他");
                return;
            }
            ifilename = ifilename  + proNum1;
        }
        if (proIp == '' || proIp == null) {
            Ext.Msg.alert('提示', "请选择IP地址...");
            return;
        }
        if (proType == '' || proType == null) {
            Ext.Msg.alert('提示', "请选择模块类型...");
            return;
        }
        if (departMent == '' || departMent == null) {
            Ext.Msg.alert('提示', "请输入分发路径...");
            return;
        }
        var reg = /\s/;
        if (reg.test(departMent)) {
            Ext.Msg.alert('提示', "请输入正确的分发路径...");
            return;
        }

        if (busDept == '' || busDept == null) {
            Ext.Msg.alert('提示', "请选择文件编码...");
            return;
        }
        if (productAsk == '' || productAsk == null) {
            Ext.Msg.alert('提示', "请输入配置内容...");
            return;
        }
        if(productAsk.length > 2000) {
            Ext.Msg.alert('提示', "配置内容长度超过2000个字符！");
            return;
        }
        Ext.getCmp("cdsaveBunId").disable();
        var url = 'editCI_CONFIGFILE.do';
        //先校验
        var validIsExist = "configItemIsExist.do";
        var params = {
            ifilename: ifilename,
                ireleaseid: ireleaseId,
                iip: proIp,
                imodeltype: proType,
                ipath: departMent,
                igjpath: comPany,
                ibm: busDept,
                icontent: proExplain,
                icontenttext: productAsk,
                isysname: sysName,
                iproducetype: "1",
                imodeltypeId:proTypeId,
                iconfigstatus:0,
                agentids: ipIdTextField.getValue()
        }

        params.from = 'cd';
        if(iid > 0 ){ //iid已存在了，通过采集操作先调用保存方法，所以iid已经存在了。创建配置页面，点击采集实际已经先保存了方法。当采集完配置询问是否覆盖，覆盖配置内容，再次点击保存，实际是修改操作。将临时配置内容转移到配置内容中，同时清空临时配置内容。
            params.excludedConfigItemId=iid; //创建配置，采集操作先调用保存会生成iid，这种情况下。通过唯一性校验是否存在，需要排除通过这条记录。
        }

        Ext.Ajax.request({
            url: validIsExist,
            timeout: 30000,
            params: params,
            method: 'POST',
            dataType: "json",
            success: function (response, opts) {
                var itemId = Ext.decode(response.responseText).iid;
                if(itemId>0){ //已存在
                    Ext.Msg.confirm("确认", "已存在相同配置文件，是否保存？", function (button, text) {
                        if (button == "yes") {
                            saveConfigSon(url,params);
                        }else {
                            Ext.getCmp("cdsaveBunId").enable();
                            return;
                        }
                    });

                }else{
                    saveConfigSon(url,params);
                }
            }
        });
    }

    function  saveConfigSon( url, params){
        Ext.Ajax.request({
            url: url,
            timeout: 30000,
            params:params,
            method: 'POST',
            success: function (response, opts) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    Ext.Msg.alert('提示', "保存成功！");
                    var itemId = Ext.decode(response.responseText).itemId;
                    iid = itemId;
                    closeThis();
                    //刷新
                    queryForm.getForm().findField("sysName").setValue(sysName);
                    CD_CONFIGFILEGrid.ipage.moveFirst()
                    return;
                } else {
                    if(null !=message){
                        Ext.Msg.alert('提示', message);
                    }else {
                        Ext.Msg.alert('提示', "保存失败！");
                    }

                }

            },
        });
    }
    function updateConfigFile() {
        var form = projectForm.getForm();
        var ifilename = ifileName.getValue();//文件
        var proNum1 = form.findField("proNum1").getValue();//后缀
        var ireleaseId = form.findField("ireleaseId").getValue();//工单编号
        var proIp = form.findField("proIp").getValue();//IP地址
        var proType = form.findField("proType").getValue();//模块类型
        var departMent = form.findField("departMent").getValue();//分发路径
        var comPany = form.findField("comPany").getValue();//构建路径
        var busDept = form.findField("busDept").getValue();//文件编码
        var proExplain = form.findField("proExplain").getValue();//描述
        editor.save();
        var productAsk = document.getElementById('productAsk').value;//配置内容
        if (busDept == '' || busDept == null) {
            Ext.Msg.alert('提示', "请选择文件编码...");
            return;
        }
        if (productAsk == '' || productAsk == null) {
            Ext.Msg.alert('提示', "请输入配置内容...");
            return;
        }
        if(productAsk.length > 2000) {
            Ext.Msg.alert('提示', "配置内容长度超过2000个字符！");
            return;
        }
        copyWindows1 = Ext.create('Ext.window.Window', {
            modal: true,
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            width: contentPanel.getWidth() * 0.6,
            height: contentPanel.getHeight() * 0.8,
            id:'copyWindows1_id',
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
            loader: {
                url: 'editConfigFileData_CD.do',
                params: {
                    sysName: sysName,
                    iid: iid,
                    igjpath: comPany,
                    icontenttext: productAsk,
                    icontent: proExplain,
                    ibm: busDept,
                    ifilename: ifilename,
                    ireleaseid: ireleaseId,
                    iip: proIp,
                    imodeltype: proType,
                    ipath: departMent,
                    icontenttext1:base64Decode(icontenttext),
                    agentids: ipIdTextField.getValue(),
                    iconfigstatus:iconfigstatus
                },
                autoLoad: true,
                scripts: true
            }
        });
        copyWindows1.show();
    }

    /***********************其他 ***********************************************/
    var tabArray = new Array();
    tabArray.push(proTab);


    var mainTabs = Ext.widget('tabpanel', {
        cls: 'customize_panel_back',
        region: 'center',
        tabPosition: 'top',
        activeTab: 0,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        buttonAlign: 'center',
        items: tabArray
    });


    var MainPanel = Ext.create('Ext.panel.Panel',
        {
            renderTo: "CI_CONFIGFILE_Div1",
            width: '100%',
            layout: 'border',
            cls: 'customize_panel_header_arrow',
            height: '100%',
            border: false,
            items: [mainTabs]
        });
    var editor = CodeMirror.fromTextArea(document.getElementById('productAsk'), {
        mode: 'shell',
        theme: "lesser-dark", // 主题
        keyMap: "sublime", // 快键键风格
        lineNumbers: true, // 显示行号
        smartIndent: true, // 智能缩进
        indentUnit: 4, // 智能缩进单位为4个空格长度
        indentWithTabs: true, // 使用制表符进行智能缩进
        lineWrapping: true, //
        // 在行槽中添加行号显示器、折叠器、语法检测器
        gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter", "CodeMirror-lint-markers"],
        foldGutter: true, // 启用行槽中的代码折叠
        autofocus: true, // 自动聚焦
        matchBrackets: true, // 匹配结束符号，比如"]、}"
        autoCloseBrackets: true, // 自动闭合符号
        styleActiveLine: true, // 显示选中行的样式
        readOnly: false,
    });
    editor.setSize(productAsk.getWidth() - 2, productAsk.getHeight() - 50);
    if (base64Decode(icontenttext) != undefined) {
        editor.getDoc().setValue(base64Decode(icontenttext));
        editor.refresh();
    }
    function afterRenderFunction() {
        var len = 0;
        // 获取编辑器中的文本内容
        var content = editor.getValue();

        // 计算文本内容的总字符数
        var contentLength = content.length;
        for (var i = 0; i < contentLength; i++) {
            var c = content.charCodeAt(i);
            if ((c >= 0x4e00 && c <= 0x9fa5) || // 汉字
                (c >= 0xff00 && c <= 0xffef) || // 全角字符
                (c >= 0x3040 && c <= 0x30ff)) { // 日文假名和片假名
                len += 2;
            } else {
                len += 1;
            }
        }
        var maxLength = 2000;

        if (!charCount) {
            charCount = productAsk.add({
                xtype: 'label',
                cls: 'char-count',
                style:'float:right;margin-right: 24px;margin-bottom:10px;'
            });
        }
        // 清空标签内容
        charCount.setText('');
        charCount.setText('输入字符数/总字符数('+len + '/' + maxLength+')');
    }
    setTimeout(afterRenderFunction, 0);
    editor.on("change", function() {
        // 在内容变化时执行的操作
        console.log('内容发生了变化');
        var len = 0;
        // 获取编辑器中的文本内容
        var content = editor.getValue();

        // 计算文本内容的总字符数
        var contentLength = content.length;
        for (var i = 0; i < contentLength; i++) {
            var c = content.charCodeAt(i);
            if ((c >= 0x4e00 && c <= 0x9fa5) || // 汉字
                (c >= 0xff00 && c <= 0xffef) || // 全角字符
                (c >= 0x3040 && c <= 0x30ff)) { // 日文假名和片假名
                len += 2;
            } else {
                len += 1;
            }
        }
        var maxLength = 2000;

        if (!charCount) {
            charCount = productAsk.add({
                xtype: 'label',
                cls: 'char-count',
                style:'float:right;margin-right: 24px;'
            });
        }
        // 清空标签内容
        charCount.setText('');
        charCount.setText('输入字符数/总字符数('+len + '/' + maxLength+')');
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function (obj, options, eOpts) {
            Ext.destroy(MainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });

    function proIpwindow(ipstr,idstr) {

        /** 查询模板信息* */
        var queryButton = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '查询',
            handler: queryWhere
        });

        /** 查询模板信息* */
        function queryWhere() {
            scriptInfo_store.reload();
        }

        var panelform = Ext.create('Ext.form.Panel', {
            layout: 'anchor',
            region: 'center',
            buttonAlign: 'center',
            autoScroll: true,
            border: false,
            bodyCls: 'x-docked-noborder-top',
            items: [
                {
                    xtype: 'combobox',
                    fieldLabel: 'IP地址',
                    anchor: '30%',
                    labelWidth: 80,
                    //store: gdStore,
                    margin: '0 0 0 0',
                    queryMode: 'local',
                    triggerAction: 'all',
                    name: 'ipName',
                    allowBlank: true,
                    enableKeyEvents: true,
                    afterLabelTextTpl: required,
                    mode: 'local',
                    hideTrigger: true,
                },queryButton]
        });
        Ext.define('scriptInfoData', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'string'
                }, {
                    name: 'iip',
                    type: 'long'
                }, {
                    name: 'port',
                    type: 'string'
                }
            ]
        });

        var scriptInfo_columns = [{
            text: '序号',
            dataIndex: 'iparamNum',
            width: '9%',
            hidden: true,
            renderer: function (value, metaData, record, rowIdx, colIdx, store) {
                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';
                return value;
            }
        },
            {text: 'iid', dataIndex: 'iid', width: 200, hidden: true,sortable: false},
            {
                text: 'IP地址', dataIndex: 'iip', flex: 1, sortable: false,
                //实现自动换行
                renderer: function (value, meta, record) {
                    meta.style = 'white-space:normal;word-break:break-all;';
                    return value;
                }
            },
            {text: '端口号', dataIndex: 'port', width: 200,sortable: false}];

        var scriptInfo_store = Ext.create('Ext.data.Store', {
            //autoLoad: true,
            autoDestroy: true,
            autoSync:true,
            pageSize: 30,
            remoteSort: true,
            model: 'scriptInfoData',
            proxy: {
                type: 'ajax',
                async:true,
                url: 'queryIpListPage.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });

        scriptInfo_store.on('beforeload', function (store, options) {
            let ids = [];
            gridstore.each(function(record) {
                var id = record.get('iid');
                ids.push(id);
            });
            console.log("selectedId:"+ids.join(','));
            var new_params =
                {
                    iid:iid,
                    ip: panelform.getForm().findField("ipName").getValue(),
                    selectedId :ids.join(','),
                    sysCode:sysName
                };
            Ext.apply(scriptInfo_store.proxy.extraParams, new_params);
        });


        var scriptInfo_grid = Ext.create('Ext.grid.Panel', {
            store: scriptInfo_store,
            padding: panel_margin,
            //selModel: selModel,
            region: 'west',
            title: '待选IP',
            selModel: Ext.create('Ext.selection.CheckboxModel'),
            cls: 'customize_panel_back',
            border: true,
            columnLines: true,
            width: '50%',
            cellTip: true,
            multiSelect: true,
            columns: scriptInfo_columns,
            plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit: 2})],
            listeners: {
                // "select": function () {
                //     gridstore.insert(0, this.value);
                // }
            }
        });
        var columns = [{
            text: '序号',
            width: 50,
            hidden: true,
            xtype: 'rownumberer'
        }, {text: 'iid', dataIndex: 'iid', width: 200, hidden: true,sortable: false},
            {
                text: 'IP地址', dataIndex: 'iip', flex: 1,sortable: false,
                //实现自动换行
                renderer: function (value, meta, record) {
                    meta.style = 'white-space:normal;word-break:break-all;';
                    return value;
                }
            },
            {text: '端口号', dataIndex: 'port', width: 200,sortable: false}];

        Ext.define('bindInfoData', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'string'
                }, {
                    name: 'iip',
                    type: 'long'
                }, {
                    name: 'port',
                    type: 'string'
                }
            ]
        });

        //获取已选择的ip
        var gridstore = Ext.create('Ext.data.Store', {
            //autoLoad: true,
            autoDestroy: true,
            autoSync:true,
            pageSize: 30,
            remoteSort: true,
            model: 'bindInfoData',
            proxy: {
                type: 'ajax',
                async:true,
                url: 'queryIpBindListPage.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });


        gridstore.on('beforeload', function (store, options) {
            var new_params =
                {
                    iid : iid,
                    ip: panelform.getForm().findField("ipName").getValue()
                };
            Ext.apply(gridstore.proxy.extraParams, new_params);
        });



        //获取已选择的ip
        var allIpstore = Ext.create('Ext.data.Store', {
            //autoLoad: true,
            autoDestroy: true,
            pageSize: 30,
            autoSync:true,
            remoteSort: true,
            model: 'bindInfoData',
            proxy: {
                type: 'ajax',
                url: 'queryIpListPage.do',
                async:true,
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });


        allIpstore.on('beforeload', function (store, options) {
            var new_params =
                {
                    iid : -1,
                    ip: panelform.getForm().findField("ipName").getValue(),
                    sysCode:sysName
                };
            Ext.apply(allIpstore.proxy.extraParams, new_params);
        });



        var s_grid = Ext.create('Ext.grid.Panel', {
            border: true,
            region: 'east',
            cls: 'customize_panel_back',
            forceFit: true,
            autoScroll: false,
            store: gridstore,
            columns: columns,
            columnLines: true,
            cellTip: true,
            width: '50%',
            title: '已选IP',
        });

        var paneltop = Ext.create('Ext.panel.Panel', {
            border: false,
            layout: 'border',
            height: 420,
            width: '100%',
            items: [scriptInfo_grid, s_grid]
        });
        var upgradeForm = Ext.create('Ext.form.FormPanel', {
            border: false,
            items: [panelform, paneltop],
            buttonAlign: 'center',
            buttons: [{
                text: '关闭',
                handler: function () {
                    upgradeWindows.close();
                }
            }, {
                text: '确定',
                hidden :  optType == 0 || optType == 1 ? false : true,
                handler: function () {
                    var records = s_grid.getStore().data.items;
                    let ids;
                    let ips = "";
                    if(s_grid.getStore().getCount() ==0){
                        Ext.Msg.alert('提示', "请选择IP");
                        return;
                    }
                    for (var i = 0; i < records.length; i++) {
                        if (i == 0) {
                            ids = records[i].get("iid");
                            ips = records[i].get("iip");
                        } else {
                            ids = ids + "," + records[i].get("iid");
                            ips = ips + ","+records[i].get("iip");
                        }
                    }
                    proIp.setValue(ips);
                    ipIdTextField.setValue(ids);
                    // panelform.getForm().findField("ipName").setValue(ids);
                    upgradeWindows.close();
                }
            }]
        });

        var upgradeWindows = Ext.create('Ext.window.Window', {
            title: 'IP选择',
            layout: 'fit',
            height: 650,
            width: 1400,
            modal: true,
            items: [upgradeForm],
            listeners: {
                close: function (g, opt) {
                    upgradeForm.destroy();
                }
            }
        });
        //显示弹窗
        upgradeWindows.show();

        // 开始检查两个grid是否都已经加载完成
        let leftstoreisLoad=false;
        let rigthStoreisLoad=false;
        let allIpStoreisLoad=false;

        checkLoaded();

        // 定义一个回调函数，用于检查两个grid是否都已经加载完成
        function checkLoaded() {
            if (leftstoreisLoad && rigthStoreisLoad && allIpStoreisLoad) {
                // 如果两个grid都已经加载完成，回显数据
                //判断一下ids是否有值，有的话从这个stroe中移除
                if(null!=idstr && "" != idstr ){
                    var idarr = idstr.split(',');
                    if(null!=idarr && idarr.length>0){
                        //已选择的先都remove掉
                        gridstore.removeAll();
                        for(let i=0;i<idarr.length;i++){
                            //然后将页面带过来都add进来。
                            let addRecord = allIpstore.findRecord('iid', idarr[i] ,0, false, false, true);
                            if (addRecord) {
                                gridstore.add(addRecord);
                            }

                        }
                        //重新计算，先清除左侧所有待选的ip记录。重新加进来
                        scriptInfo_store.removeAll();
                        for(let b =0;b<allIpstore.getCount();b++){
                            let arecord= allIpstore.getAt(b) //遍历每一行
                            scriptInfo_store.add(arecord);
                        }

                        for(let m=0;m<idarr.length;m++){
                            let removeRecord = allIpstore.findRecord('iid', idarr[m] ,0, false, false, true);
                            if (removeRecord) {
                                scriptInfo_store.remove(removeRecord);
                            }
                        }
                    }
                }
            } else {
                // 如果两个grid都还没有加载完成，等待一段时间后再次检查
                setTimeout(checkLoaded, 100);
            }
        }

        scriptInfo_store.load({
            callback: function(records, operation, success) {
                if (success) {
                    leftstoreisLoad = true;
                }
            }
        });

        gridstore.load({
            callback: function(records, operation, success) {
                if (success) {
                    rigthStoreisLoad = true;
                }
            }
        });

        allIpstore.load({
            callback: function(records, operation, success) {
                if (success) {
                    allIpStoreisLoad = true;
                }
            }
        });


        //用户勾选左侧待选择IP列表中的数据时将其同步到右侧已选择IP列表中。这可以通过监听左侧表格的selectionchange事件来实现
        scriptInfo_grid.on('selectionchange', function() {
            var selectedRecords = scriptInfo_grid.getSelectionModel().getSelection();
            gridstore.add(selectedRecords);
            scriptInfo_store.remove(selectedRecords);
        });
        //
        s_grid.on('selectionchange', function() {
            var selectedRecords = s_grid.getSelectionModel().getSelection();
            scriptInfo_store.add(selectedRecords);
            gridstore.remove(selectedRecords);
        });

    }
});

function pullItem(jsonDate,result) {

}

//清除采集过来的临时配置内容
function  deleteICONTENTTEXTTEMP(iid){
    Ext.Ajax.request({
        url: 'deleteIcontextTemp.do',
        method: 'POST',
        params: {
            iid: iid
        },
        success: function (response, opts) {
            var success = Ext.decode(response.responseText).success;
            var message = Ext.decode(response.responseText).message;
            console.log("清理临时配置内容(success:"+success+";message:"+message+")");
        },
        failure: function (result, opts) {
            console.log("清理临时配置内失败！");
        }
    });
}

//清除采集临时记录
function  deleteItemTEMP(iid){
    Ext.Ajax.request({
        //url: 'deleteCI_CONFIGFILE.do',
        url:'deleteTempItems.do',
        method: 'POST',
        params: {
            iids: iid
        },
        success: function (response, request) {
            var success = Ext.decode(response.responseText).success;
            var message = Ext.decode(response.responseText).message;
            console.log("清理临时配置(success:"+success+";message:"+message+")");
        },
        failure: function (result, request) {
            secureFilterRs(result, '清理临时配置失败！');
        }
    });
}

