package com.ideal.ieai.server.repository.hd.infoCollection.test;

import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.ideal.ieai.server.repository.hd.infoCollection.InfoCollectionHttpClient;
import com.ideal.util.CastUtil;
import com.ideal.util.JsonUtil;

public class HttpTest
{
    private Logger              log         = Logger.getLogger(InfoCollectionHttpClient.class);

    private static final String UTF8STR     = "UTF-8";
    private static final String TOKENERRMEG = "调用获取token接口错误,错误码为:";

    @SuppressWarnings("unchecked")
    public String testGetToken ( String url )
    {
        String token = "";
        DefaultHttpClient client = new DefaultHttpClient();
        HttpGet httpGet = new HttpGet();
        try
        {
            httpGet = new HttpGet(url);
            client = new DefaultHttpClient();
            HttpResponse res = client.execute(httpGet);
            int statusCode = res.getStatusLine().getStatusCode();
            if (statusCode == 200)
            {
                HttpEntity entity = res.getEntity();
                String tokenStr = EntityUtils.toString(entity);
                Map<String, Object> jsonMap = JsonUtil.getMapFromJson(tokenStr);
                token = jsonMap.get("tokenValue").toString();
                log.info("获取自动化运维平台token信息,返回结果为 " + token);
            } else
            {
                log.error(TOKENERRMEG + statusCode);
            }

        } catch (Exception ex)
        {
            log.error("信息采集上传文件,返回结果异常,异常信息为: ", ex);
        }
        return token;
    }

    public void testInfoCollectHttpClient ( String url, String msg, String token )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DefaultHttpClient client = null;
        HttpPost httpPost = null;
        try
        {
            // 设置头部信息
            httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.addHeader("Accept", "application/json");
            httpPost.addHeader("paramsHeader", "{\"tokenValue\":\"" + token + "\",\"userName\":\"sa\"}");

            StringEntity entity = new StringEntity(msg, UTF8STR);
            entity.setContentType("application/json;charset=UTF-8");
            httpPost.setEntity(entity);

            client = new DefaultHttpClient();
            HttpResponse res = client.execute(httpPost);
            String resContent = "";
            int statusCode = res.getStatusLine().getStatusCode();
            // log.info(statusCode);
            if (statusCode == 200)
            {
                HttpEntity httpEntity = res.getEntity();
                if (httpEntity != null)
                {
                    // log.info("向一体化运维平台发送上传文件执行结果信息，一体化运维平台返回结果为 " +
                    // EntityUtils.toString(httpEntity, UTF8STR));
                }
            } else if (statusCode == 500)
            {
                HttpEntity httpEntity = res.getEntity();
                if (httpEntity != null)
                {
                    log.info(EntityUtils.toString(httpEntity, UTF8STR));
                }
            } else
            {
                resContent = TOKENERRMEG + statusCode;
                log.error(resContent);
            }
        } catch (Exception ex)
        {
            log.error(method, ex);
        }
    }

    private String getInfoCollectJson ()
    {
        StringBuilder sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // sBuilder.append("\"recordid\":\"aa168\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"/igudm/cmdb/CM02010200/168/215170\",");
        // sBuilder.append("\"globalIP\":\"***********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"步骤1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"actName\":\"第一子步\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append(
        // "\"execCmd\":\"/igudm/cmdb/CM02010200/CM02010200/104/command.sh 215170
        // /igudm/cmdb/CM02010200/168/215170/\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        
       
        sBuilder.append("[{");
        String recordId = "28215";
        sBuilder.append("\"recordid\":\"" + recordId + "\",");
        sBuilder.append("\"taskType\":\"1\",");
        sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        sBuilder.append("\"globalIP\":\"**********\",");
        sBuilder.append("\"coats\":[{");
        sBuilder.append("\"actName\":\"step 1\",");
        sBuilder.append("\"actNo\":\"1\",");
        sBuilder.append("\"prener\":\"\",");
        sBuilder.append("\"connername\":\"step 1\",");
        sBuilder.append("\"conner\":\"1\",");
        sBuilder.append("\"agentIP\":\"\",");
        sBuilder.append("\"execUser\":\"\",");
        sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        sBuilder.append("\"timeout\":\"5\"");
        sBuilder.append("}]");
        sBuilder.append("}]");

        // [{"coats":[
        // {"actName":"step 1",
        // "actNo":"1",
        // "agentIP":"",
        // "conner":"",
        // "connername":"step 1",
        // "execCmd":"/igudm/cmdb/CM02010200/CM01030100/122/snmpCfCollect.sh ********** 215167
        // /igudm/cmdb/result/215167/ juniper firewall FIREWARE#@#community",
        // "execUser":"",
        // "prener":"",
        // "timeout":"5"}],
        // "globalIP":"**********",
        // "outpath":"/igudm/cmdb/result/215167/",
        // "recordid":"400",
        // "taskType":"0"}]
        return sBuilder.toString();
    }

    public static void main ( String[] args )
    {
        HttpTest test = new HttpTest();
        String tokenUrl = "http://**********:8888/aoms/infoCollection/token.do?userName=sa";

        String token = test.testGetToken(tokenUrl);
        String infoCollectUrl = "http://**********:8888/aoms/infoCollection/startConllection.do";

        StringBuilder sBuilder = new StringBuilder();
        String recordId = "";

        sBuilder = new StringBuilder();
        sBuilder.append("[{");
        recordId = CastUtil.castString(28214);
        sBuilder.append("\"recordid\":\"" + recordId + "\",");
        sBuilder.append("\"taskType\":\"1\",");
        sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        sBuilder.append("\"globalIP\":\"**********\",");
        sBuilder.append("\"coats\":[{");
        sBuilder.append("\"actName\":\"step 1\",");
        sBuilder.append("\"actNo\":\"1\",");
        sBuilder.append("\"prener\":\"\",");
        sBuilder.append("\"connername\":\"step 1\",");
        sBuilder.append("\"conner\":\"1\",");
        sBuilder.append("\"agentIP\":\"\",");
        sBuilder.append("\"execUser\":\"\",");
        sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        sBuilder.append("\"timeout\":\"5\"");
        sBuilder.append("}]");
        sBuilder.append("}]");
        test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);

        // for (int i = 28215, len = 28243; i < len; i++)
        // {
        // try
        // {
        // Thread.sleep(500);
        // } catch (InterruptedException e)
        // {
        // e.printStackTrace();
        // }
        // sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // recordId = CastUtil.castString(i);
        // sBuilder.append("\"recordid\":\"" + recordId + "\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        // sBuilder.append("\"globalIP\":\"**********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actName\":\"step 1\",");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        // test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);
        // }
        //
        // sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // recordId = "28243";
        // sBuilder.append("\"recordid\":\"" + recordId + "\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        // sBuilder.append("\"globalIP\":\"**********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actName\":\"step 1\",");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("},{");
        // sBuilder.append("\"actName\":\"step 2\",");
        // sBuilder.append("\"actNo\":\"2\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 2\",");
        // sBuilder.append("\"conner\":\"2\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        // test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);
        //
        // for (int i = 28244, len = 28316; i < len; i++)
        // {
        // try
        // {
        // Thread.sleep(500);
        // } catch (InterruptedException e)
        // {
        // e.printStackTrace();
        // }
        // sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // recordId = CastUtil.castString(i);
        // sBuilder.append("\"recordid\":\"" + recordId + "\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        // sBuilder.append("\"globalIP\":\"**********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actName\":\"step 1\",");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        // test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);
        // }
        //
        // sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // recordId = "28316";
        // sBuilder.append("\"recordid\":\"" + recordId + "\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        // sBuilder.append("\"globalIP\":\"**********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actName\":\"step 1\",");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("},{");
        // sBuilder.append("\"actName\":\"step 2\",");
        // sBuilder.append("\"actNo\":\"2\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 2\",");
        // sBuilder.append("\"conner\":\"2\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        // test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);
        //
        // sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // recordId = "28317";
        // sBuilder.append("\"recordid\":\"" + recordId + "\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        // sBuilder.append("\"globalIP\":\"**********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actName\":\"step 1\",");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("},{");
        // sBuilder.append("\"actName\":\"step 2\",");
        // sBuilder.append("\"actNo\":\"2\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 2\",");
        // sBuilder.append("\"conner\":\"2\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        // test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);
        //
        // for (int i = 28318, len = 28965; i < len; i++)
        // {
        // try
        // {
        // Thread.sleep(500);
        // } catch (InterruptedException e)
        // {
        // e.printStackTrace();
        // }
        // sBuilder = new StringBuilder();
        // sBuilder.append("[{");
        // recordId = CastUtil.castString(i);
        // sBuilder.append("\"recordid\":\"" + recordId + "\",");
        // sBuilder.append("\"taskType\":\"1\",");
        // sBuilder.append("\"outpath\":\"D:\\\\tmp\\\\\",");
        // sBuilder.append("\"globalIP\":\"**********\",");
        // sBuilder.append("\"coats\":[{");
        // sBuilder.append("\"actName\":\"step 1\",");
        // sBuilder.append("\"actNo\":\"1\",");
        // sBuilder.append("\"prener\":\"\",");
        // sBuilder.append("\"connername\":\"step 1\",");
        // sBuilder.append("\"conner\":\"1\",");
        // sBuilder.append("\"agentIP\":\"\",");
        // sBuilder.append("\"execUser\":\"\",");
        // sBuilder.append("\"execCmd\":\"c:\\\\ping.bat\",");
        // sBuilder.append("\"timeout\":\"5\"");
        // sBuilder.append("}]");
        // sBuilder.append("}]");
        // test.testInfoCollectHttpClient(infoCollectUrl, sBuilder.toString(), token);
        // }
    }
}
