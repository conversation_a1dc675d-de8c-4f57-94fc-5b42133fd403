package com.ideal.controller.paas.callconfig;
import java.util.HashMap;
import java.util.Map;

import com.ideal.util.CastUtil;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.commons.Constants;
import com.ideal.service.paas.callconfig.CallconfigService;

import javax.servlet.http.HttpServletRequest;

@Controller
public class CallconfigController
{
    private Logger log = Logger.getLogger(CallconfigController.class);
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";
    @RequestMapping("initCallConfigBeanPage.do")
    public String initCallConfigBeanConfig ()
    {
	    return "paas/callconfig/callConfig";
    }

    @RequestMapping("getCallConfigBeanList.do")
    @ResponseBody
    public Map getCallConfigBeanList ( Integer start, Integer limit, String queryString )
    {
        CallconfigService service = new CallconfigService();
        Map map = new HashMap();
        try
        {
            map = service.getCallConfigBeanList( start, limit, queryString,Constants.IEAI_OPM);
        } catch (Exception e)
        {
            log.error("查询失败", e);
        }
        return map;
    }

    @RequestMapping("saveCallConfigBean.do")
    @ResponseBody
    public Map saveCallConfigBean ( String jsonData )
    {
	     CallconfigService service = new CallconfigService();
        Map map = new HashMap();
        try
        {
            service.saveCallConfigBean(jsonData,Constants.IEAI_OPM);
            map.put(SUCCESS, true);
            map.put(MESSAGE, "保存成功");
        } catch (Exception e)
        {
            log.error("保存失败",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "保存失败");
        }
        return map;
    }

    @RequestMapping("deleteCallConfigBean.do")
    @ResponseBody
    public Map deleteCallConfigBean ( String deleteIds )
    {
        CallconfigService service = new CallconfigService();
        Map map = new HashMap();
        try
        {
            map = service.deleteCallConfigBean(deleteIds);
        } catch (Exception e)
        {
            log.error("删除失败",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "删除失败");
        }
        return map;
    }

    @RequestMapping("getModuleList.do")
    @ResponseBody
    public Map getModuleList ()
    {
        CallconfigService service = new CallconfigService();
        return service.getModuleList(Constants.IEAI_OPM);
    }

    @RequestMapping("getCallConfigComboxInfo.do")
    @ResponseBody
    public Object getCallConfigComboxInfo(HttpServletRequest request){
        Long templateType = CastUtil.castLong(request.getParameter("templateType"));
        CallconfigService service = new CallconfigService();
        return null;
    }
}