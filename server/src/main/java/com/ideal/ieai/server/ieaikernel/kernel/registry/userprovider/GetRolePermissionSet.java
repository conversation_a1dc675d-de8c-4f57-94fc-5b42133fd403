package com.ideal.ieai.server.ieaikernel.kernel.registry.userprovider;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.PermissionSet;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.communication.marshall.Request;
import com.ideal.ieai.communication.marshall.Response;
import com.ideal.ieai.communication.marshall.transfer.interfaces.APIService;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.kernel.registry.Provider;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ResponseHelper;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.permission.IPermissionManager;

/**
 * get a role's permissionSet clientAPI
 * <p>Title: ieai server</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR>
 * @version 3.0
 */
public class GetRolePermissionSet extends Provider
{
    /**
     * Logger for this class
     */
    private static final Logger _log = Logger
                                             .getLogger(GetRolePermissionSet.class);

    public GetRolePermissionSet(String name,IPermissionManager permMgr)
    {
        super(name);
        this._permManager=permMgr;
    }

    /**
     * to execute UserAdminAPI.getRolePermissionSet clientAPI,api info:
     * <ur>
     * <li> serviceName:getRolePermissionSet</li>
     * <li> sessionId :client session's ID</li>
     * <li> roleId :role's ID to get permissionSet</li>
     * <li> return value:role's permissionSet</li>
     * </ur>
     * @param req
     * @return
     * @throws ServerException
     */
    public Response executeService(Request req) throws ServerException
    {
        if(req.getParamNum()!=2)
        {
            _log.error("client api of checkUserPermission 's param number error!");
            return ResponseHelper.getErrorResponse(ServerError.ERR_INV_NUM_PARAM);
        }

        Long roleId=(Long)req.getParameterValue(APIService.P_ROLE_ROLEID);

        try
        {
            PermissionSet perms=_permManager.getRolePermissionSet(roleId);
            return ResponseHelper.getReturnValueResponse(perms);
        }
        catch(RepositoryException e)
        {
            return ResponseHelper.getErrorResponse(e.getServerError());
        }
    }

    private IPermissionManager _permManager;
}
