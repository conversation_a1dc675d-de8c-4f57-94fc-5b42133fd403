package com.ideal.ieai.server.repository.hd.cicd.flow.save.service;


import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.cicd.flow.task.*;
import com.ideal.ieai.server.repository.hd.cicd.pojo.CICDEnvInfo;
import com.ideal.ieai.server.repository.hd.cicd.pojo.CICDResGroup;
import com.ideal.ieai.server.repository.hd.cicd.pojo.CICDScriptContent;
import com.ideal.ieai.server.repository.sus.importexecl.TmpltMagmntException;
import com.ideal.util.CastUtil;
import com.ideal.util.uuid.UUID;
import com.kingbase8.jdbc.EsClob;
import oracle.sql.CLOB;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <ul>
 * <li>Title: CDSaveFlowService.java</li>
 * <li>Description:Cicid流程保存服务</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2023-3-2
 */
public class CDSaveFlowManager 
{

    private static final Logger _log = Logger.getLogger(CDSaveFlowManager.class);
    
    private static CDSaveFlowManager intance = null;

    public static CDSaveFlowManager getInstance ()
    {
        if (intance == null)
        {
            intance = new CDSaveFlowManager();
        }
        return intance;
    }
    
    
    /**
     * <li>Description:保存任务 IEAI_CICD_FLOWTASK</li> 
     * <AUTHOR>
     * 2023-3-17 
     * return void
     * @param cicdContainerExeUtil
     * @param con
     * @throws SQLException 
     * @throws TmpltMagmntException 
     */
    public void saveCICDTask(CICDContainerExeUtil cicdContainerExeUtil, Connection con) throws SQLException, TmpltMagmntException  {
        CICDFlowTask cicdFlowTask = cicdContainerExeUtil.getCicdFlowTask();
        PreparedStatement ps=null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String insSql="INSERT INTO IEAI_CICD_FLOWTASK ( IID,IEAI_PROJECT,ITASKNAME,IDESC,IEAI_CI_FLOWTYPE_ID,PRINCIPAL,SERVICE,IMAGENAME,IPUBLISH_STATE,ISTATE,ICREATETIME,IUPDATETIME,IUUID,ISVALIDATA,IVERSIONNO,ICICDSYS_CODE,EXTERNAL_START_TASK,ICREATEUSER,IDESTROYDOCKER,IUPPERID )"
                +"VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        String updSql = "UPDATE IEAI_CICD_FLOWTASK SET  IEAI_PROJECT = ?, ITASKNAME = ?, IDESC = ?,IEAI_CI_FLOWTYPE_ID = ?, PRINCIPAL=?,SERVICE=?,IMAGENAME=?,IUPDATETIME = ?, IUUID = ?,ICICDSYS_CODE=?,EXTERNAL_START_TASK=?,IDESTROYDOCKER=?,IUPPERID=? "
                +" WHERE IID = ?";
        try{
            if (cicdFlowTask.isIsmodify()){//修改update
                int index = 0;
                ps = con.prepareStatement(updSql);
                ps.setLong(++index, cicdFlowTask.getIeai_project());
                ps.setString(++index, cicdFlowTask.getItaskname());
                ps.setString(++index, cicdFlowTask.getIdesc());
                ps.setString(++index, cicdFlowTask.getIeai_ci_flowtype_id());
                ps.setString(++index, cicdFlowTask.getPrincipal());
                ps.setString(++index, cicdFlowTask.getService());
                ps.setString(++index, cicdFlowTask.getImagename());
                ps.setString(++index, df.format(new Date()));
                ps.setString(++index, cicdFlowTask.getIuuid());
                ps.setString(++index, cicdFlowTask.getIcicdsys_code());
                ps.setInt(++index, cicdFlowTask.getIexternalStartTask());
                ps.setInt(++index, cicdFlowTask.getIdestroyDocker());
                ps.setLong(++index, cicdFlowTask.getIupperid());
                ps.setLong(++index, cicdFlowTask.getIid());
                ps.executeUpdate();
                //如修改删除子数据
                ArrayList<String> arrayList = new ArrayList<String>();
                arrayList.add(String.valueOf(cicdFlowTask.getIid()));
                delCheldInfo(con,arrayList);
            }else {//增加
                
                //判断是否已存在未发布版本,已存在删除保留一份
                ArrayList<String> arrayList = queryUnpublishedInfo(con,cicdFlowTask);
                if (arrayList.size()>0){
                    //先删除以前数据
                    delCheldInfo(con,arrayList);
                    delFlowtaskInfo(con,arrayList);
                }
                //查询是否存在任务 存在获取是否启动
                long starttime2 = System.currentTimeMillis();
                int start = this.getFlowTaskStart(con,cicdFlowTask.getItaskname());
                int index = 0;
                ps = con.prepareStatement(insSql);
                ps.setLong(++index, cicdFlowTask.getIid());
                ps.setLong(++index, cicdFlowTask.getIeai_project());
                ps.setString(++index, cicdFlowTask.getItaskname());
                ps.setString(++index, cicdFlowTask.getIdesc());
                ps.setString(++index, cicdFlowTask.getIeai_ci_flowtype_id());
                ps.setString(++index, cicdFlowTask.getPrincipal());
                ps.setString(++index, cicdFlowTask.getService());
                ps.setString(++index, cicdFlowTask.getImagename());
                if (cicdFlowTask.getRiseflag()!=110) {
                    ps.setString(++index, "0");
                }else {
                    ps.setString(++index, cicdFlowTask.getIpublish_state());
                }
                if (cicdFlowTask.getRiseflag()!=110) {
                    ps.setLong(++index, start);
                }else {
                    ps.setLong(++index, cicdFlowTask.getIstate());
                }
                ps.setString(++index, df.format(new Date()));
                ps.setString(++index, df.format(new Date()));
                ps.setString(++index, cicdFlowTask.getIuuid());
                ps.setLong(++index, 0);
                if (cicdFlowTask.getRiseflag()!=110) {
                    ps.setString(++index, "无版本-未发布");
                }else {
                    ps.setString(++index, cicdFlowTask.getIversionno());
                }
                ps.setString(++index, cicdFlowTask.getIcicdsys_code());
                ps.setInt(++index, cicdFlowTask.getIexternalStartTask());
                ps.setLong(++index, cicdFlowTask.getUser().getId());
                ps.setInt(++index, cicdFlowTask.getIdestroyDocker());
                ps.setLong(++index, cicdFlowTask.getIupperid());
                ps.executeUpdate();
            }
        } catch (SQLException e){
            _log.error("saveCICDTask:保存失败,"+e);
            throw new TmpltMagmntException(false, "保存IEAI_CICD_FLOWTASK异常,保存失败!");
        }finally {
            DBResource.closePreparedStatement(ps, method, _log);
        }
    }
    
    

    /**
     *@desc 通过id查询uuid
     * <li>Description:</li> 
     * <AUTHOR>
     * 2023-6-9 
     * @param iid
     * @return
     * return String
     */
    public String getQueryUUID (long iid )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String uuid="";
        try{
            String sql = "SELECT IUUID FROM IEAI_CICD_FLOWTASK  WHERE IID = ?";
            conn = DBResource.getConnection("getQueryUUID", _log, Constants.IEAI_IEAI_BASIC);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next()) {
                uuid = rs.getString("IUUID");
            }
            if(StringUtils.isBlank(uuid)){
                uuid = UUID.create();
            }
        } catch (Exception e){
            e.printStackTrace();
        }finally {
            DBResource.closeConn(conn,rs, ps, "getQueryUUID", _log);
        }
        return uuid;
    }

    /** 
    * @Description: 新增流水线-通过任务名 获取 iupperid 
    * @Param: [cicdFlowTask] 
    * @return: void 
    * @Author: zuochao_wang
    * @Date: 2023/12/26 11:56
    */ 
    public void getUpperidByInsertTask (CICDFlowTask cicdFlowTask )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        int  count = -1;
        Long iid = null;
        Long iupperid = null;
        int index = 0;
        try{
            String sql = "SELECT COUNT(IID) AS COU FROM IEAI_CICD_FLOWTASK  WHERE ISVALIDATA = 0 and ITASKNAME = ?";
            String sql1 = "select IID , IUPPERID from IEAI_CICD_FLOWTASK where iid = (SELECT MIN( T.IID ) AS IID  FROM IEAI_CICD_FLOWTASK T WHERE T.ISVALIDATA = 0 AND t.ITASKNAME = ?  GROUP BY T.itaskname)";
            conn = DBResource.getConnection("getUpperidByInsertTask", _log, Constants.IEAI_IEAI_BASIC);
            ps = conn.prepareStatement(sql);
            if(cicdFlowTask.isIeditModifuTaskName()&&StringUtils.isNotBlank(cicdFlowTask.getIoldTaskname())){//当更改名称的时候，需要获取老的名称查询老的最小的ID
                ps.setString(++index, cicdFlowTask.getIoldTaskname());
            }else {
                ps.setString(++index, cicdFlowTask.getItaskname());
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                count = rs.getInt("COU");
            }
            if(count>0){
                index = 0;
                ps1 = conn.prepareStatement(sql1);
                if(cicdFlowTask.isIeditModifuTaskName()&&StringUtils.isNotBlank(cicdFlowTask.getIoldTaskname())){
                    ps1.setString(++index, cicdFlowTask.getIoldTaskname());
                }else {
                    ps1.setString(++index, cicdFlowTask.getItaskname());
                }
                rs1 = ps1.executeQuery();
                while (rs1.next()) {
                    iid = rs1.getLong("IID");
                    iupperid = rs1.getLong("IUPPERID");
                }
                if(iupperid!=null&&iupperid.longValue()>0){
                    cicdFlowTask.setIupperid(iupperid);
                }else{
                    cicdFlowTask.setUpdateUpperid(true);
                    cicdFlowTask.setIupperid(iid);
                }
            }else{
                //count为零，则认为是第一条数据，则以当前ID为IUPPERID
                cicdFlowTask.setIupperid(cicdFlowTask.getIid());
            }
        } catch (Exception e){
            e.printStackTrace();
        }finally {
            DBResource.closeConn(conn,rs, ps, "getUpperidByInsertTask", _log);
            DBResource.closePSRS(rs1,ps1,"getUpperidByInsertTask",_log);
        }
    }

    /** 
    * @Description: 更新流水线通过任务名称，任务ID 获取 iupperid（最小任务ID） 方法
    * @Param: [cicdFlowTask] 
    * @return: void 
    * @Author: zuochao_wang
    * @Date: 2023/12/27 10:45
    */ 
    public void getUpperidByUpdateTask (CICDFlowTask cicdFlowTask)
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        Long iid = null;
        Long iupperid = null;
        try{
            String sql = "SELECT IUPPERID FROM IEAI_CICD_FLOWTASK  WHERE IID = ?";
            String sql1 = "select IID ,IUPPERID from IEAI_CICD_FLOWTASK where iid = (SELECT MIN( T.IID ) AS IID  FROM IEAI_CICD_FLOWTASK T WHERE T.ISVALIDATA = 0 AND t.ITASKNAME = (select ITASKNAME from IEAI_CICD_FLOWTASK where IID = ?)  GROUP BY T.itaskname)";
            conn = DBResource.getConnection("getUpperidByUpdateTask", _log, Constants.IEAI_IEAI_BASIC);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, cicdFlowTask.getIid());
            rs = ps.executeQuery();
            while (rs.next()) {
                iupperid = rs.getLong("IUPPERID");
            }
            if(iupperid!=null&&iupperid.longValue()>0){
                cicdFlowTask.setIupperid(iupperid);
            }else{
                ps1 = conn.prepareStatement(sql1);
                ps1.setLong(1, cicdFlowTask.getIid());
                rs1 = ps1.executeQuery();
                while (rs1.next()) {
                    iid = rs1.getLong("IID");
                    iupperid = rs1.getLong("IUPPERID");
                }
                if(iupperid!=null&&iupperid.longValue()>0){
                    cicdFlowTask.setIupperid(iupperid);
                }else{
                    cicdFlowTask.setUpdateUpperid(true);
                    cicdFlowTask.setIupperid(iid);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }finally {
            DBResource.closeConn(conn,rs, ps, "getUpperidByUpdateTask", _log);
            DBResource.closePSRS(rs1,ps1,"getUpperidByUpdateTask",_log);
        }
    }


    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * @DESC 删除子数据
     * 2023-3-21 
     * @param con
     * @param arrayList
     * return void
     * @throws SQLException 
     * @throws TmpltMagmntException 
     */
    public void delCheldInfo ( Connection con, ArrayList<String> arrayList ) throws SQLException, TmpltMagmntException{
        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        PreparedStatement ps7 = null;
        PreparedStatement ps8 = null;
        PreparedStatement ps9 = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try {
            String sql1 = "DELETE FROM IEAI_CICD_TASK_PARAM WHERE IEAI_CICD_FLOWTASK in ("+StringUtils.join(arrayList,",")+")";
            ps = con.prepareStatement(sql1);
            ps.executeUpdate();

            String sql9 = "DELETE FROM IEAI_CICD_INSTANCE_V_REL WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+")";
            ps9 = con.prepareStatement(sql9);
            ps9.executeUpdate();

            String sql3 = "DELETE FROM IEAI_INSTANCE_VERSION_CONTENT  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+"))";
            ps3 = con.prepareStatement(sql3);
            ps3.executeUpdate();
                
            String sql5 = "DELETE FROM IEAI_CICD_INSINFO_CONTENT  WHERE iversioninfo_id IN (select IID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+")))";
            ps5 = con.prepareStatement(sql5);
            ps5.executeUpdate();
            
            String sql6 = "DELETE FROM IEAI_CICD_INSINFO_RESGROUP  WHERE iversioninfo_id IN (select IID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+")))";
            ps6 = con.prepareStatement(sql6);
            ps6.executeUpdate();
            
            String sql7 = "DELETE FROM IEAI_CICD_INSINFO_ENV  WHERE iversioninfo_id IN (select IID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+")))";
            ps7 = con.prepareStatement(sql7);
            ps7.executeUpdate();
            
            String sql8 = "DELETE FROM IEAI_CICD_INSINFO_PARAMS  WHERE iversioninfo_id IN (select IID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+")))";
            ps8 = con.prepareStatement(sql8);
            ps8.executeUpdate();
            
            String sql4 = "DELETE FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+"))";
            ps4 = con.prepareStatement(sql4);
            ps4.executeUpdate();
            
            String sql2 = "DELETE FROM IEAI_INSTANCE_VERSION WHERE IFLOWTASKID in ("+StringUtils.join(arrayList,",")+")";
            ps2 = con.prepareStatement(sql2);
            ps2.executeUpdate();
            
        } catch (SQLException e){
            _log.error("delCheldInfo:删除失败,"+e);
            throw new TmpltMagmntException(false, "删除delCheldInfo异常,保存失败!");
        }finally {
            DBResource.closePreparedStatement(ps, method, _log); 
            DBResource.closePreparedStatement(ps2, method, _log); 
            DBResource.closePreparedStatement(ps3, method, _log); 
            DBResource.closePreparedStatement(ps4, method, _log); 
            DBResource.closePreparedStatement(ps5, method, _log); 
            DBResource.closePreparedStatement(ps6, method, _log); 
            DBResource.closePreparedStatement(ps7, method, _log); 
            DBResource.closePreparedStatement(ps8, method, _log);
            DBResource.closePreparedStatement(ps9, method, _log);
        }
    }
    
    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * @param con 
     * @DESC 删除主数据
     * 2023-3-21 
     * @param cicdFlowTask
     * return void
     * @throws SQLException 
     * @throws TmpltMagmntException 
     */
    public void delFlowtaskInfo ( Connection con, ArrayList<String> arrayList ) throws SQLException, TmpltMagmntException{
        PreparedStatement ps = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try {
            String sql1 = "DELETE FROM  IEAI_CICD_FLOWTASK WHERE IID  IN ("+StringUtils.join(arrayList,",")+")";
            ps = con.prepareStatement(sql1);
            ps.executeUpdate();
        } catch (SQLException e){
            _log.error("delFlowtaskInfo:删除失败,"+e);
            throw new TmpltMagmntException(false, "删除delFlowtaskInfo异常,保存失败!");
        }finally {
            DBResource.closePreparedStatement(ps, method, _log); 
        }
    }
    /**
     * <li>Description:保存
     *  IEAI_INSTANCE_VERSION 、
     *  IEAI_INSTANCE_INFO 、
     *  IEAI_INSTANCE_VERSION_CONTENT</li> 
     * <AUTHOR>
     * 2023-3-17 
     * return void
     * @param cicdContainerExeUtil2 
     * @param connection 
     * @throws TmpltMagmntException 
     */
    public void saveInstance(CICDContainerExeUtil cicdContainerExeUtil, Connection connection) throws TmpltMagmntException{
        this.saveInstanceVersion( cicdContainerExeUtil,  connection);
        this.saveInstanceVersionContent( cicdContainerExeUtil,  connection);
    }

    /**
     * <li>Description:保存
     *  IEAI_CICD_TASK_PARAM</li> 
     * <AUTHOR>
     * 2023-3-17 
     * return void
     * @param cicdContainerExeUtil
     * @param connection 
     * @throws SQLException 
     * @throws TmpltMagmntException 
     */
    public void saveCICDTaskParam(CICDContainerExeUtil cicdContainerExeUtil, Connection connection) throws SQLException, TmpltMagmntException {
        CICDFlowTask cicdFlowTask = cicdContainerExeUtil.getCicdFlowTask();
        List<CICDTaskParam> cicdTaskParam = cicdContainerExeUtil.getCICDTaskParam();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        String insSql = "INSERT INTO IEAI_CICD_TASK_PARAM ( IID,IEAI_CICD_FLOWTASK,IPARAMNAME,IPARAMVALUE,IPARAMTYPE,IPARAMDESC,INEWDATA,IENCRYPTYPE,IEAI_CICD_FLOWTASK_UUID ,ICREATETIME,IUPDATETIME,IUUID) "
                +"VALUES( ?,?,?,?,?,?,?,?,?,?,?,?)";
        PreparedStatement ps = null;
        try{
            ps = connection.prepareStatement(insSql);
            for (CICDTaskParam cd : cicdTaskParam){
                int index = 0;
                ps.setLong(++index, cd.getIid());
                ps.setLong(++index, cicdFlowTask.getIid());
                ps.setString(++index, cd.getIparamname());
                ps.setString(++index, cd.getIparamvalue());
                ps.setString(++index, cd.getIparamtype());
                ps.setString(++index, cd.getIparamdesc());
                ps.setInt(++index, cd.getInewdata());
                ps.setInt(++index, cd.getIencryptype());
                ps.setString(++index, cicdFlowTask.getIuuid());
                ps.setString(++index, df.format(new Date()));
                ps.setString(++index, df.format(new Date()));
                ps.setString(++index, cd.getIuuid());
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (SQLException e){
            _log.error(method+",保存失败:"+e);
            throw new TmpltMagmntException(false, "保存IEAI_CICD_TASK_PARAM异常,保存失败!");
        }finally {
            DBResource.closePreparedStatement(ps, method, _log);
        }
    }

    /**
    * @Description: 任务编辑修改同名任务所有的名称 
    * @Param: [cicdContainerExeUtil, connection] 
    * @return: void 
    * @Author: zuochao_wang
    * @Date: 2023/6/8 10:26
    */ 
    public void updateCICDTaskName(CICDContainerExeUtil cicdContainerExeUtil, Connection connection) throws SQLException, TmpltMagmntException {
        CICDFlowTask cicdFlowTask = cicdContainerExeUtil.getCicdFlowTask();
        if(cicdFlowTask.isIeditModifuTaskName()&&StringUtils.isNotBlank(cicdFlowTask.getIoldTaskname())){
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            String updateSql1 = " update IEAI_INSTANCE_VERSION set IINSTANCENAME = ? where IPARENTID='-1' and IFLOWTASKID in  (select IID from IEAI_CICD_FLOWTASK where ISVALIDATA = 0 and ITASKNAME =? and IEAI_PROJECT = ? )";
            String updateSql2 = " update IEAI_CICD_FLOWTASK set ITASKNAME = ? where ISVALIDATA = 0 and ITASKNAME =? and IEAI_PROJECT = ?";
            PreparedStatement ps = null;
            PreparedStatement ps1 = null;
            int index = 0;
            try{
                ps = connection.prepareStatement(updateSql1);
                ps.setString(++index,cicdFlowTask.getItaskname());
                ps.setString(++index,cicdFlowTask.getIoldTaskname());
                ps.setLong(++index,cicdFlowTask.getIeai_project());
                ps.executeUpdate();
                index = 0;
                ps1 = connection.prepareStatement(updateSql2);
                ps1.setString(++index,cicdFlowTask.getItaskname());
                ps1.setString(++index,cicdFlowTask.getIoldTaskname());
                ps1.setLong(++index,cicdFlowTask.getIeai_project());
                ps1.executeUpdate();
            } catch (SQLException e){
                _log.error(method+",保存失败:"+e);
                throw new TmpltMagmntException(false, "更新IEAI_CICD_FLOWTASK字段ITASKNAME失败,保存失败!");
            }finally {
                DBResource.closePreparedStatement(ps, method, _log);
                DBResource.closePreparedStatement(ps1, method, _log);
            }
        }
    }

    /** 
    * @Description: 通过任务名称业务系统ID修改同名称所有的IUPPERID 
    * @Param: [cicdContainerExeUtil, connection] 
    * @return: void 
    * @Author: zuochao_wang
    * @Date: 2023/12/26 8:57
    */ 
    public void updateCICDTaskUpperid(CICDContainerExeUtil cicdContainerExeUtil, Connection connection) throws SQLException, TmpltMagmntException {
        CICDFlowTask cicdFlowTask = cicdContainerExeUtil.getCicdFlowTask();
        if(cicdFlowTask.isUpdateUpperid()){
            String method = Thread.currentThread().getStackTrace()[1].getMethodName();
            String updateSql = " update IEAI_CICD_FLOWTASK set IUPPERID = ? where ISVALIDATA = 0 and ITASKNAME =? and IEAI_PROJECT = ?";
            PreparedStatement ps = null;
            int index = 0;
            try{
                ps = connection.prepareStatement(updateSql);
                ps.setLong(++index,cicdFlowTask.getIupperid());
                ps.setString(++index,cicdFlowTask.getItaskname());
                ps.setLong(++index,cicdFlowTask.getIeai_project());
                ps.executeUpdate();
            } catch (SQLException e){
                _log.error(method+",保存失败:"+e);
                throw new TmpltMagmntException(false, "更新IEAI_CICD_FLOWTASK字段IUPPERID失败,保存失败!");
            }finally {
                DBResource.closePreparedStatement(ps, method, _log);
            }
        }
    }
    
    public void rollbackDataForMultiple ( Connection con, CICDContainerExeUtil cicdContainerExeUtil ) throws TmpltMagmntException{
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        PreparedStatement ps6 = null;
        PreparedStatement ps7 = null;
        PreparedStatement ps8 = null;
        PreparedStatement ps9 = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        int index = 0;
        try{
            CICDFlowTask cicdFlowTask = cicdContainerExeUtil.getCicdFlowTask();
            // 删除IEAI_INSTANCEINFO表数据
            String sql1 = "DELETE FROM IEAI_CICD_TASK_PARAM WHERE IEAI_CICD_FLOWTASK=?";
            ps1 = con.prepareStatement(sql1);
            index = 0;
            ps1.setLong(++index, cicdFlowTask.getIid());
            ps1.executeUpdate();

            String sql9 = "DELETE FROM IEAI_CICD_INSTANCE_V_REL WHERE IFLOWTASKID=?";
            ps9 = con.prepareStatement(sql9);
            index = 0;
            ps9.setLong(++index, cicdFlowTask.getIid());
            ps9.executeUpdate();

            String sql3 = "DELETE FROM IEAI_INSTANCE_VERSION_CONTENT  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID=?)";
            ps3 = con.prepareStatement(sql3);
            index = 0;
            ps3.setLong(++index, cicdFlowTask.getIid());
            ps3.executeUpdate();
            
            String sql5 = "DELETE FROM IEAI_CICD_INSINFO_CONTENT  WHERE iversioninfo_id IN (select IINSTANCEID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID=?))";
            ps5 = con.prepareStatement(sql5);
            index = 0;
            ps5.setLong(++index, cicdFlowTask.getIid());
            ps5.executeUpdate();
                        
            String sql6 = "DELETE FROM IEAI_CICD_INSINFO_RESGROUP  WHERE iversioninfo_id IN (select IINSTANCEID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID=?))";
            ps6 = con.prepareStatement(sql6);
            index = 0;
            ps6.setLong(++index, cicdFlowTask.getIid());
            ps6.executeUpdate();
                        
            String sql7 = "DELETE FROM IEAI_CICD_INSINFO_ENV  WHERE iversioninfo_id IN (select IINSTANCEID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID=?))";
            ps7 = con.prepareStatement(sql7);
            index = 0;
            ps7.setLong(++index, cicdFlowTask.getIid());
            ps7.executeUpdate();
                        
            String sql8 = "DELETE FROM IEAI_CICD_INSINFO_PARAMS  WHERE iversioninfo_id IN (select IINSTANCEID FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID=?))";
            ps8 = con.prepareStatement(sql8);
            index = 0;
            ps8.setLong(++index, cicdFlowTask.getIid());
            ps8.executeUpdate();
            
            String sql4 = "DELETE FROM IEAI_INSTANCEINFO  WHERE IINSTANCEID IN (SELECT IID FROM IEAI_INSTANCE_VERSION  WHERE IFLOWTASKID=?)";
            ps4 = con.prepareStatement(sql4);
            index = 0;
            ps4.setLong(++index, cicdFlowTask.getIid());
            ps4.executeUpdate();
            
            String sql2 = "DELETE FROM IEAI_INSTANCE_VERSION WHERE IFLOWTASKID=?";
            ps2 = con.prepareStatement(sql2);
            index = 0;
            ps2.setLong(++index, cicdFlowTask.getIid());
            ps2.executeUpdate();

            // 执行回滚语句(后期扩展用)
            for (String rollbackSql : cicdContainerExeUtil.getRollbackSqls())
            {
                boolean isOk = DBUtil.executeSql(con, rollbackSql);
                if (!isOk)
                {
                    throw new SQLException();
                }
            }
            con.commit();
        } catch (Exception e){
            try{
                con.rollback();
            } catch (SQLException e1){
                _log.error(method, e);
            }
            throw new TmpltMagmntException(false, e.getMessage());
        } finally{
            DBResource.closePreparedStatement(ps1, method, _log);
            DBResource.closePreparedStatement(ps2, method, _log);
            DBResource.closePreparedStatement(ps3, method, _log);
            DBResource.closePreparedStatement(ps4, method, _log);
            DBResource.closePreparedStatement(ps5, method, _log);
            DBResource.closePreparedStatement(ps6, method, _log);
            DBResource.closePreparedStatement(ps7, method, _log);
            DBResource.closePreparedStatement(ps8, method, _log);
            DBResource.closePreparedStatement(ps9, method, _log);
        }
    }
    
    public void saveInstanceVersionContent ( CICDContainerExeUtil cicdContainerExeUtil, Connection connection ) throws TmpltMagmntException{
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        ResultSet rs3 = null;
        InputStream fis1 = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try{
            // 保存IEAI_INSTANCE_VERSION_CONTENT bengin
            String Jsonflow = cicdContainerExeUtil.getCicdFlowTask().getJsonflow();//IEAI_INSTANCE_VERSION_CONTENT
            CICDVersionContent ct = cicdContainerExeUtil.getCicdversioncontent();
            
            String sql = "";
            // 判断数据库类型，true oracle; false DB2
            if (DBManager.Orcl_Faimily())
            {
                sql = "INSERT INTO IEAI_INSTANCE_VERSION_CONTENT(IID, IINSTANCEID,IUUID, ICONTENT)    " + " VALUES(?, ?, ?, EMPTY_CLOB())";
            } else if (DBManager.DB2_Faimily())
            {
                sql = "INSERT INTO IEAI_INSTANCE_VERSION_CONTENT(IID, IINSTANCEID, IUUID,ICONTENT)    " + " VALUES( ?, ?, ?, empty_clob())";
            }else if (DBManager.Mysql_Faimily())
            {
                sql = "INSERT INTO IEAI_INSTANCE_VERSION_CONTENT(IID, IINSTANCEID,IUUID, ICONTENT)       " + " VALUES( ?, ?, ?, ?)";
            }
            ps2 = connection.prepareStatement(sql);
            // oracle db2
            if (DBManager.Orcl_Faimily()||DBManager.DB2_Faimily()){
                ps2.setLong(1, ct.getIid());
                ps2.setLong(2, ct.getInsid());
                ps2.setString(3, cicdContainerExeUtil.getCicdFlowTask().getIuuid());
                ps2.executeUpdate();
            } else if (DBManager.Mysql_Faimily()){
                ps2.setLong(1, ct.getIid());
                ps2.setLong(2, ct.getInsid());
                ps2.setString(3, cicdContainerExeUtil.getCicdFlowTask().getIuuid());
                ps2.setString(4, Jsonflow);
                ps2.executeUpdate();
            }
            // 插入完成
            // 更新clob
            String sqlUpdate = "SELECT A.ICONTENT FROM IEAI_INSTANCE_VERSION_CONTENT A  WHERE A.IID=? FOR UPDATE ";
            String sql1 = " UPDATE IEAI_INSTANCE_VERSION_CONTENT SET ICONTENT=? WHERE IID=?";
            ps3 = connection.prepareStatement(sqlUpdate);
            ps3.setLong(1, ct.getIid());
            rs3 = ps3.executeQuery();
            CLOB clob = null;
            EsClob clob2 = null;
            while (rs3.next()) {
                if (JudgeDB.IEAI_DB_TYPE==1)
                {
                    clob = (CLOB) rs3.getClob("ICONTENT");
                    clob.setString(1, Jsonflow);
                }else if (JudgeDB.IEAI_DB_TYPE==5){
                    clob2 = (EsClob) rs3.getClob("ICONTENT");
                    clob2.setString(1, Jsonflow);
                }
            }
            ps4 = connection.prepareStatement(sql1);
            if (JudgeDB.IEAI_DB_TYPE==1){
                ps4.setClob(1, clob);
                ps4.setLong(2, ct.getIid());
            } else if(JudgeDB.IEAI_DB_TYPE==5){
                ps4.setClob(1, clob2);
                ps4.setLong(2, ct.getIid());
            }else{
                if (Jsonflow != null) {
                    fis1 = new ByteArrayInputStream(Jsonflow.getBytes(StandardCharsets.UTF_8));
                    ps4.setAsciiStream(1, fis1, Jsonflow.getBytes(StandardCharsets.UTF_8).length);
                } else {
                    ps4.setAsciiStream(1, null, 0);
                }
                ps4.setLong(2, ct.getIid());
            }
            ps4.executeUpdate();
            // 保存IEAI_INSTANCE_VERSION_CONTENT end
        }catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveInstance异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps2, method, _log);
            DBResource.closePreparedStatement(ps4, method, _log);
            DBResource.closePSRS(rs3, ps3, method, _log);
            if(fis1!=null){
                try{
                    fis1.close();
                } catch (IOException e){
                    _log.error(method, e);
                }
            }
        }
    }

    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * @DESC 
     * 2023-4-7 
     * @param list
     * @param connection
     * @throws TmpltMagmntException
     * return void
     * 1.ut 人工节点  acttype = 2 ,
     * 2.任务调用   acttype = 101  
     * 3. git代码拉取 acttype = 102
     * 4.部署状态确认 acttype = 103
     * 5.配置项更新 acttype = 104
     * 6.构建 shell 脚本执行  acttype = 105  存0
     * 7.部署shell 脚本执行  acttype = 106 存0
     */
    public void saveInstanceInfo ( List<CICDVersionInfo> list, Connection connection ) throws TmpltMagmntException{
        PreparedStatement ps = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try{
            String intSql = "INSERT INTO IEAI_INSTANCEINFO(IID, IINSTANCEID, IINSTANCENAME, ISERNER, ICONNER, ICONNERNAME,IPRENER, IACTNAME,IACTDES,IACTTYPE, IREMINFO, ISYSTYPE,IEXECUSER,ISHELLSCRIPT,IISLOADENV,ISHELLPATH,ITIMEOUT,IPARAMETER,IEXPECEINFO, IEXCEPTINFO, IREDOABLE, IISDISABLE, IPKGNAME, IRETNVALEXCEPION,ITYPE, IMODELNAME, IMODELVERSION, ISCRIPTID,  IMXGRAPHID, ISYSTEMTYPESTEP, IMODELTYPESTEP, IPREACTNAME,IUUID,ICICDSTEPID,IINSUUID,ISCRIPTMODULETYPE,ISCRIPTMODULECHILDTYPE,ISTAGETYPE,IEXECFAILED,ISCRIPTTYPE,IEXECPERMISSION,ICICDINFOTYPE,IIPNAME,IPLUGIN_UUID,IPLUGIN_VERSION,IPLUGIN_TYPE) "
                    + "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?)";
             //保存IEAI_INSTANCEINFO BENGIN
            ps = connection.prepareStatement(intSql);
            for (CICDVersionInfo susExeclInfo : list){
                int index=0;
                ps.setLong(++index, susExeclInfo.getIid());//id
                ps.setLong(++index, susExeclInfo.getIinstanceid());//version_id
                ps.setString(++index, susExeclInfo.getIinstanceName());//version_name
                ps.setLong(++index, susExeclInfo.getActNo());//序号
                ps.setLong(++index, susExeclInfo.getConNo());//阶段
                ps.setString(++index, susExeclInfo.getIconnerName());//阶段名称
                ps.setString(++index, susExeclInfo.getPreNo());//依赖
                ps.setString(++index, susExeclInfo.getActName());//步骤名称
                ps.setString(++index, susExeclInfo.getActDes());//步骤描述
                ps.setString(++index, susExeclInfo.getActtype());//步骤类型
                ps.setString(++index, susExeclInfo.getTaskRem());//任务提醒
                ps.setLong(++index, susExeclInfo.getSusType());// 步骤执行系统类型 0  win/1 Linux
                ps.setString(++index, susExeclInfo.getExecuser());// 执行用户
                ps.setString(++index, susExeclInfo.getShellscript());//壳脚本
                ps.setInt(++index, susExeclInfo.getIsLoadEnv());// 是否加载环境变量
                ps.setString(++index, susExeclInfo.getShellPath()); // 脚本信息
                ps.setInt(++index, susExeclInfo.getTimeOutT()); // 超时时间
                ps.setString(++index, susExeclInfo.getShellPara());//校验参数
                ps.setString(++index, susExeclInfo.getExpectInfo());//预期返回值
                ps.setString(++index, susExeclInfo.getRetnValWhenException());// 异常返回值
                ps.setInt(++index, 1);//默认为可重试
                ps.setString(++index, "0");
                ps.setString(++index, "");
                ps.setString(++index, "");
                ps.setInt(++index, susExeclInfo.getItype());//直接调用脚本2  模板加载3
                ps.setString(++index, susExeclInfo.getImodelName() == null ? "" : susExeclInfo.getImodelName());//模板名称
                ps.setLong(++index, susExeclInfo.getImodelVersion());//模板版本号
                ps.setLong(++index, susExeclInfo.getScriptId());//脚本id
                ps.setLong(++index, susExeclInfo.getImxgraphId());//序号id
                ps.setString(++index, susExeclInfo.getSystemType());//应用标识
                ps.setString(++index, susExeclInfo.getModuleType());//模块类型
                ps.setString(++index, susExeclInfo.getIpreActName() == null ? "" : susExeclInfo.getIpreActName());// 依赖步骤名称
                ps.setString(++index, susExeclInfo.getUuid());//uuid
                ps.setString(++index, susExeclInfo.getStepId());//cicd流程图节点id
                ps.setString(++index, susExeclInfo.getVersion_uuid());//cicd流程图节点id
                ps.setString(++index, susExeclInfo.getScriptModuleType());// 脚本大类 脚本服务化的还是在线的
                ps.setString(++index, susExeclInfo.getScriptModuleChildType());//脚本模板分类
                ps.setString(++index, susExeclInfo.getStageType());// 1ci还是2cd
                ps.setString(++index, susExeclInfo.getExecFailed());//1忽略 0 挂起
                ps.setString(++index, susExeclInfo.getScriptType());//脚本类型 -1 没有脚本类型  0sehll  1bat  2perl  3python  4 powershel
                ps.setString(++index, susExeclInfo.getExecPermission());//只有配置项才会有 配置项更新执行权限
                ps.setString(++index, susExeclInfo.getIcicdinfotype());//插件大类
                ps.setString(++index, susExeclInfo.getServerAlias()==null?"":susExeclInfo.getServerAlias());//共享资源名称
                ps.setString(++index, susExeclInfo.getPluginUuid());//插件UUID
                ps.setString(++index, susExeclInfo.getPluginVersion());//插件版本
                ps.setInt(++index, susExeclInfo.getPluginType()==null?0:susExeclInfo.getPluginType());//插件类型
                ps.addBatch();
                
                if (susExeclInfo.getScriptparams().size()>0){//脚本参数
                    this.saveScriptparams(susExeclInfo.getScriptparams(), connection);
                }
                if (susExeclInfo.getEnvInfos().size()>0){//环境
                    this.saveEnvInfos(susExeclInfo.getEnvInfos(), connection);
                }
                if (susExeclInfo.getResGroups().size()>0){//资源组
                    this.saveResGroups(susExeclInfo.getResGroups(), connection);
                }
                if (susExeclInfo.getCicdscriptContent() != null){//脚本内容
                    this.saveScriptContent(susExeclInfo.getCicdscriptContent(), connection);
                }
            }
            ps.executeBatch();
            //保存IEAI_INSTANCEINFO END
        } catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveInstance异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps, method, _log);  
        }
    }

    public void saveInstanceVersion ( CICDContainerExeUtil cicdContainerExeUtil, Connection connection ) throws TmpltMagmntException{
        List<CICDVersion> cicdVersions = cicdContainerExeUtil.getCicdVersion();//实例信息子表IEAI_INSTANCE_INFO
        Map<String, Object> idMap = cicdContainerExeUtil.getIdMap();
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try{
            // 保存IEAI_INSTANCE_VERSION表数据
            String insSql1 = "INSERT INTO IEAI_INSTANCE_VERSION(IID, IINSTANCENAME,  ISYSTYPE, IDES, ITIME, IUPPERID, IPROJECTID, IISMXGRAPH,IBUSNES_SYS_IID,IISSUSFLOW,ISBACKINSTANCE,IVERSIONTYPE,ICREATEUSRID,IINSTANCETYPE2 ,IFLOWTASKID,IUUID,IPARENTID,IFLOWTASKUUID,ICICDSTEPID) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            String ins_v_relSql="INSERT INTO IEAI_CICD_INSTANCE_V_REL ( IID,INAME,IINSTANCE_V_IID,IINSTANCE_V_UUID,IPR_V_IID,IPRE_V_UUID,IFLOWTASKID,ICREATETIME) VALUES(?,?,?,?,?,?,?,?)";
            ps1 = connection.prepareStatement(insSql1);
            ps2 = connection.prepareStatement(ins_v_relSql);
            if (cicdVersions.size()>0){
                for (CICDVersion cicdVersion : cicdVersions){
                    int index = 0;
                    ps1.setLong(++index, cicdVersion.getIid());
                    ps1.setString(++index, cicdVersion.getInsName());
                    ps1.setInt(++index, cicdVersion.getIsystype());
                    ps1.setString(++index, "");
                    ps1.setLong(++index, System.currentTimeMillis());
                    ps1.setLong(++index, cicdVersion.getIbusnesSysIid());
                    ps1.setLong(++index, cicdVersion.getIbusnesSysIid());
                    ps1.setInt(++index, 1);
                    ps1.setLong(++index, cicdVersion.getIbusnesSysIid());
                    ps1.setInt(++index, 1);
                    ps1.setInt(++index, 0);
                    ps1.setString(++index, "0");
                    ps1.setLong(++index, cicdContainerExeUtil.getCicdFlowTask().getUser().getId());
                    ps1.setInt(++index, 0);
                    ps1.setLong(++index, cicdContainerExeUtil.getCicdFlowTask().getIid());
                    ps1.setString(++index, cicdVersion.getUuid());
                    ps1.setString(++index, cicdVersion.getIparentuuid());
                    ps1.setString(++index, cicdVersion.getFlowtaskUuid());
                    ps1.setString(++index, cicdVersion.getIcicdstepid());
                    ps1.addBatch();

                    CICDInstancePojo cicdInstancePojo = cicdVersion.getCicdInstancePojo();
                    int index2 = 0;
                    ps2.setLong(++index2, cicdInstancePojo.getIid());
                    ps2.setString(++index2, cicdInstancePojo.getV_iname());
                    ps2.setLong(++index2, CastUtil.castLong(idMap.get(cicdInstancePojo.getV_id())));
                    ps2.setString(++index2, cicdInstancePojo.getV_uuid());
                    long iprvid=-1;
                    if (!"-1".equals(cicdInstancePojo.getIpr_v_id())){
                        iprvid= CastUtil.castLong(idMap.get(cicdInstancePojo.getIpr_v_id()));
                    }
                    ps2.setLong(++index2, iprvid);
                    ps2.setString(++index2, cicdInstancePojo.getIpr_v_uuid());
                    ps2.setLong(++index2, cicdContainerExeUtil.getCicdFlowTask().getIid());
                    ps2.setLong(++index2, System.currentTimeMillis());
                    ps2.addBatch();

                    this.saveInstanceInfo(cicdVersion.getVersionInfos(), connection);
                }
                ps1.executeBatch();
                ps2.executeBatch();
            }
        }catch (SQLException e){
            e.printStackTrace();
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveInstance异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps1, method, _log);
            DBResource.closePreparedStatement(ps2, method, _log);
        }
    }
    
    private void saveScriptparams ( List<CICDScriptParams> scriptparams, Connection connection ) throws TmpltMagmntException{
        PreparedStatement ps = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        try{
            String intSql = "INSERT INTO IEAI_CICD_INSINFO_PARAMS ( IID,IVERSIONINFO_ID,IPARM_NAME,IPARM_TYPE,IPARM_VALUE,IPRAM_CONER,ICREATETIME,IUPDATETIME,IUUID,IVERSIONINFO_UUID,ISVALIDATA,INEWDATA,IENCRYPTYPE ) "
                    +"VALUES( ?,?,?,?,?,?,?,?,?,?,?,?,? )";
            ps = connection.prepareStatement(intSql);
            for (CICDScriptParams sp : scriptparams){
                int index=0;
                ps.setLong(++index, sp.getIid());//id
                ps.setLong(++index, sp.getIversioninfo_id());//versioninfo_id
                ps.setString(++index, sp.getIparm_name());//名字
                ps.setString(++index, sp.getIparm_type());//类型
                ps.setString(++index, sp.getIparm_value());//值
                ps.setString(++index, sp.getIpram_coner());//序号
                ps.setString(++index, df.format(new Date()));//创建时间
                ps.setString(++index, df.format(new Date()));//更新时间
                ps.setString(++index, sp.getIuuid());//uuid
                ps.setString(++index, sp.getIversioninfo_uuid());//versioninfo_uuid
                ps.setInt(++index, 0);//是否生效
                ps.setInt(++index, sp.getInewdata());//是否是加密需求后的新数据
                ps.setInt(++index, sp.getIencryptype());//是否使用加密
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveScriptparams异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps, method, _log);  
        }
    }

    public void saveScriptContent ( CICDScriptContent cicdScriptContent, Connection connection ) throws TmpltMagmntException
    {
        PreparedStatement ps = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        try{
            String intSql = "INSERT INTO IEAI_CICD_INSINFO_CONTENT ( IID,IVERSIONINFO_ID,ISCRIPTNAME,ISCRIPTCONTENT,ICREATETIME,IUPDATETIME,IUUID,IVERSIONINFO_UUID,ISVALIDATA )VALUES(?,?,?,?,?,?,?,?,?)";
            ps = connection.prepareStatement(intSql);
            int index=0;
            ps.setLong(++index, cicdScriptContent.getIid());//id
            ps.setLong(++index, cicdScriptContent.getIversioninfo_id());//versioninfo_id
            ps.setString(++index, cicdScriptContent.getIscriptName());//名字
            ps.setString(++index, cicdScriptContent.getIscriptContent());//类型
            ps.setString(++index, df.format(new Date()));//创建时间
            ps.setString(++index, df.format(new Date()));//更新时间
            ps.setString(++index, cicdScriptContent.getIuuid());//uuid
            ps.setString(++index, cicdScriptContent.getIversioninfo_uuid());//versioninfo_uuid
            ps.setInt(++index, 0);//是否生效
            ps.executeUpdate();
        } catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveScriptparams异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps, method, _log);  
        }
        
    }


    public void saveResGroups ( List<CICDResGroup> cicdResGroups, Connection connection ) throws TmpltMagmntException
    {
        PreparedStatement ps = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        try{
            String intSql = "INSERT INTO IEAI_CICD_INSINFO_RESGROUP ( IID,IVERSIONINFO_ID,IRES_NAME,ICREATETIME,IUPDATETIME,IUUID,IVERSIONINFO_UUID,ISVALIDATA ) VALUES(?,?,?,?,?,?,?,? )";
            ps = connection.prepareStatement(intSql);
            for (CICDResGroup sp : cicdResGroups){
                int index=0;
                ps.setLong(++index, sp.getIid());//id
                ps.setLong(++index, sp.getIversioninfo_id());//versioninfo_id
                ps.setString(++index, sp.getIres_name());//名字
                ps.setString(++index, df.format(new Date()));//创建时间
                ps.setString(++index, df.format(new Date()));//更新时间
                ps.setString(++index, sp.getIuuid());//uuid
                ps.setString(++index, sp.getIversioninfo_uuid());//versioninfo_uuid
                ps.setInt(++index, 0);//是否生效
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveScriptparams异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps, method, _log);  
        }
        
    }


    public void saveEnvInfos ( List<CICDEnvInfo> CICDEnvInfo, Connection connection ) throws TmpltMagmntException
    {
        PreparedStatement ps = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
        try{
            String intSql = "INSERT INTO IEAI_CICD_INSINFO_ENV ( IID,IVERSIONINFO_ID,IENVID,IENVNAME,ICREATETIME,IUPDATETIME,IUUID,IVERSIONINFO_UUID,ISVALIDATA ) VALUES ( ?,?,?,?,?,?,?,?,? )";
            ps = connection.prepareStatement(intSql);
            for (CICDEnvInfo sp : CICDEnvInfo){
                int index=0;
                String envName = this.queryEnvName(connection,sp.getIenvid());
                ps.setLong(++index, sp.getIid());//id
                ps.setLong(++index, sp.getIversioninfo_id());//versioninfo_id
                ps.setString(++index, sp.getIenvid());//id
                ps.setString(++index, envName);//名字
                ps.setString(++index, df.format(new Date()));//创建时间
                ps.setString(++index, df.format(new Date()));//更新时间
                ps.setString(++index, sp.getIuuid());//uuid
                ps.setString(++index, sp.getIversioninfo_uuid());//versioninfo_uuid
                ps.setInt(++index, 0);//是否生效
                ps.addBatch();
            }
            ps.executeBatch();
        } catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, "保存saveScriptparams异常,保存失败!");
        } finally{
            DBResource.closePreparedStatement(ps, method, _log);  
        }
    }

    public ArrayList<String> queryUnpublishedInfo ( Connection con, CICDFlowTask cicdFlowTask ) throws TmpltMagmntException
    {
        ArrayList<String> arrayList = new ArrayList<String>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try{
            String intSql = "SELECT IID FROM IEAI_CICD_FLOWTASK  WHERE ITASKNAME=? AND IPUBLISH_STATE=0";
            ps = con.prepareStatement(intSql);
            ps.setString(1, cicdFlowTask.getItaskname());
            rs = ps.executeQuery();
            while (rs.next()){
                arrayList.add(rs.getString("IID"));
            }
        } catch (SQLException e){
            _log.error(method, e);
            throw new TmpltMagmntException(false, method+"查询异常!");
        } finally{
            DBResource.closePSRS(rs, ps, method, _log);
        }
        return arrayList;
    }
    
    public String queryEnvName ( Connection conn, String ienvid ){
        PreparedStatement ps = null;
        ResultSet rs = null;
        String envName="";
        try{
            String sql = "SELECT INAME FROM IEAI_GROUP_ENV  WHERE IID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, ienvid);
            rs = ps.executeQuery();
            while (rs.next()) {
                envName = rs.getString("INAME");
            }
        } catch (SQLException e){
            e.printStackTrace();
        }finally {
            DBResource.closePSRS(rs, ps, "queryEnvName", _log);
        }
        return envName;
    }
    
    public int getFlowTaskStart ( Connection con, String itaskname )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int state=1;
        try{
            String sql = "SELECT A.ISTATE FROM IEAI_CICD_FLOWTASK A WHERE A.IID = (SELECT MAX(T.IID) FROM IEAI_CICD_FLOWTASK T  WHERE  T.ITASKNAME=?)";
            ps = con.prepareStatement(sql);
            ps.setString(1, itaskname);
            rs = ps.executeQuery();
            while (rs.next()) {
                state = rs.getInt("ISTATE");
            }
        } catch (SQLException e){
            e.printStackTrace();
        }finally {
            DBResource.closePSRS(rs, ps, "getFlowTaskStart", _log);
        }
        return state;
    }
}
