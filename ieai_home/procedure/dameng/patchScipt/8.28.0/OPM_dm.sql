
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER_GROUP' AND COLUMN_NAME='ILIMIT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_USER_GROUP ADD ILIMIT VARCHAR2(5) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IHARDWAREINFO'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IHARDWAREINFO VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_SEND_MESS_HS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SEND_MESS_HS (IID NUMBER (19)   NOT NULL,IREVIEWERTELEPHONE VARCHAR2 (255)  ,IMODELTYPE VARCHAR2 (255)  ,IWORKITEMID NUMBER (19)  ,ISTATUS NUMBER (2) DEFAULT 0 , CONSTRAINT PK_IEAI_SEND_MESS_HS PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
