package com.ideal.ieai.commons.report;

import java.util.Date;

public class ActRunInfoStatReport
{
    private Date _statTime;
    private int  _successActTotal;
    private int  _failedActTotal;
    private int  _timeoutActTotal;
    private int  _skipActTotal;
    private int                   _totalRecord=0;

    public int getSkipActTotal ()
    {
        return _skipActTotal;
    }

    public void setSkipActTotal ( int skipacttotal )
    {
        _skipActTotal = skipacttotal;
    }

    public int getTimeoutActTotal ()
    {
        return _timeoutActTotal;
    }

    public void setTimeoutActTotal ( int timeoutActTotal )
    {
        _timeoutActTotal = timeoutActTotal;
    }

    public int getFailedActTotal ()
    {
        return _failedActTotal;
    }

    public void setFailedActTotal ( int failedActTotal )
    {
        _failedActTotal = failedActTotal;
    }

    public int getStartActTotal ()
    {
        return _successActTotal + _failedActTotal + _timeoutActTotal
                + _skipActTotal;
    }

    public int getSuccessActTotal ()
    {
        return _successActTotal;
    }

    public void setSuccessActTotal ( int successActTotal )
    {
        _successActTotal = successActTotal;
    }

    public Date getStatTime ()
    {
        return _statTime;
    }

    public void setStatTime ( Date statTime )
    {
        _statTime = statTime;
    }

    public int getTotalRecord ()
    {
        return _totalRecord;
    }

    public void setTotalRecord ( int record )
    {
        _totalRecord = record;
    }
}
