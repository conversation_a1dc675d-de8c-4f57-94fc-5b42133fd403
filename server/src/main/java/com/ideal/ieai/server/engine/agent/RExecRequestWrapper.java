package com.ideal.ieai.server.engine.agent;

import com.ideal.dubbo.interfaces.IScriptExec;
import com.ideal.dubbo.interfaces.IScriptInstance;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.agent.RemoteActivityExecRequest;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.data.BinaryObject;
import com.ideal.ieai.core.data.MapObject;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.opm.manager.CollectItemBean;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.datacollect.repository.upload.DownLoadModel;
import com.ideal.ieai.server.datacollect.repository.upload.FileUploadManager;
import com.ideal.ieai.server.dm.repository.audit.IcSPDBAuditManager;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.core.ExcelActUtil;
import com.ideal.ieai.server.engine.util.DBExecContext;
import com.ideal.ieai.server.engine.util.IExecContext;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.platform.common.GetFileName;
import com.ideal.ieai.server.platform.nessus.NessusRepairHandler;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.engine.DbOpScheduler;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.Agent;
import com.ideal.ieai.server.repository.hd.cicd.flow.start.service.CDStartFlowManager;
import com.ideal.ieai.server.repository.hd.collectItem.CollectItemManager;
import com.ideal.ieai.server.repository.hd.scriptManagement.BinaryEntityFiles;
import com.ideal.ieai.server.repository.hd.scriptManagement.ScriptsManager;
import com.ideal.ieai.server.repository.lob.CmBean;
import com.ideal.ieai.server.repository.lob.LobStorer;
import com.ideal.ieai.server.repository.vmMonitorManager.VmMonitorManager;
import com.ideal.ieai.server.springContext.IeaiSpringContextUtil;
import com.ideal.ieai.server.timetask.repository.executetask.TimeTaskManager;
import com.ideal.util.CollectionUtil;
import com.ideal.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class RExecRequestWrapper
{
    private IExecContext              _ctx;
    private RemoteActivityExecRequest _rexecRequest;
    private ActivityElement           _actElem;

    public ActivityElement getActElem ( int type ) throws ServerException
    {
        if (_rexecRequest.isSafeFirst())
        {
            Workflow flow = Engine.getInstance().getWorkfow(_rexecRequest.getFlowId(), type);
            return (ActivityElement) flow.getActivityByActID(_rexecRequest.getActId());
        } else
        {
            return _actElem;
        }
    }

    public ActivityElement getActElemForIEAI ( int type ) throws ServerException
    {
        if (_rexecRequest.isSafeFirst())
        {
            Workflow flow = Engine.getInstance().getWorkfow_KL(_rexecRequest.getFlowId(), type);
            return (ActivityElement) flow.getActivityByActID(_rexecRequest.getActId());
        } else
        {
            return _actElem;
        }
    }

    public RemoteActivityExecRequest getRexecRequest () throws ServerException
    {
        if (_rexecRequest.isSafeFirst())
        {
            if (_rexecRequest.getId().indexOf("cm") > -1)
            {

                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(0);
                _rexecRequest.setActId("cmShellCmd");
                _rexecRequest.setActName("cmShellCmd");
                DefaultConfig config = new DefaultConfig();
                String cmd = new String();
                cmd = cmd + "!@#$";
                cmd = cmd + _rexecRequest.getId() + ",";
                cmd = cmd + _rexecRequest.getServerHost() + ",";
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                // String
                // cmd=_rexecRequest.getId()+"************"+","+""+","+"set /p var=请输入用户名: echo
                // 您输入的用户名为%var% "+","+"2";
                try
                {
                    List<CmBean> listCmBean = bOpScheduler.getCmScriptIID(_rexecRequest.getId());
                    for (int i = 0; i < listCmBean.size(); i++)
                    {
                        CmBean cmBean = listCmBean.get(i);
                        if (i == 0)
                        {
                            byte[] content = LobStorer.getBlobData(Long.valueOf(cmBean.getId()));
                            if (content == null)
                            {
                                cmd = cmd + ",";
                            } else
                            {
                                String str = new String(content);
                                cmd = cmd + str + ",";
                            }
                        } else
                        {
                            byte[] content = LobStorer.getBlobData(Long.valueOf(cmBean.getId()));
                            if (content == null)
                            {
                                cmd = cmd + ",";
                                cmd = cmd + cmBean.getType();
                            } else
                            {
                                String str = new String(content);
                                cmd = cmd + str + ",";
                                cmd = cmd + cmBean.getType();
                            }
                        }

                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                config.put("command", cmd);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("cmshellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("cm");
                _rexecRequest.setServerHost(_rexecRequest.getServerHost());
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));

            } else if (_rexecRequest.getId().indexOf("ieai-auto-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-BigpkgBuild-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().startsWith(Constants.REQUEST_KEY_NESSUS))
            {
                this.nessusReqMake();
                // 组织命令数据
            } else if (_rexecRequest.getId().startsWith(Constants.REQUEST_KEY_COLLECT))
            {
                DefaultConfig dc = new DefaultConfig();
                // 根据reqid获取检查项中对应的操作指令。
                String cmd = "";
                try
                {
                    List<CollectItemBean> beans = CollectItemManager.getInstance()
                            .queryItemMessageForReqid(this._rexecRequest.getId(), Constants.IEAI_OPM);
                    if (beans.isEmpty())
                    {
                        cmd = "";
                    } else
                    {
                        cmd = beans.get(0).getShell();
                    }
                } catch (Exception e)
                {
                    _log.error("this._rexecRequest.getId():" + this._rexecRequest.getId());
                    _log.error(e);
                }
                HashMap map = new HashMap();
                map.put("cbxYorNWarn", true);
                map.put("command", cmd);
                map.put("params", new Hashtable());
                map.put("consoleInput", new ArrayList());
                map.put("params", new ArrayList());
                map.put("startIn", "");
                map.put("timeout", "");
                map.put("executeUsername", "");
                dc.putAll(map);
                _rexecRequest.setAdaptorConfig(dc);
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
            } else if (_rexecRequest.getId().indexOf("timetask") > -1)
            {
                DefaultConfig dc = new DefaultConfig();
                // dc.setWorkflowId(1);
                HashMap map = new HashMap();
                map.put("cbxYorNWarn", true);
                String cmd = "";
                try
                {
                    cmd = TimeTaskManager.getInstance().queryCmd(_rexecRequest.getId());
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                map.put("command", cmd);
                // map.put("consoleInput",new ArrayList());
                // map.put("params", new ArrayList());
                // map.put("startIn", "");
                // map.put("timeout", "");
                // map.put("executeUsername", "");
                dc.putAll(map);
                _rexecRequest.setAdaptorConfig(dc);
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
            } else if (_rexecRequest.getId().indexOf("ieai-vmVersion-") > -1)
            {
                String script = "";
                try
                {
                    script = VmMonitorManager.getInstance().vmAntRunScript(_rexecRequest.getId(),
                        Constants.IEAI_IEAI_BASIC);
                } catch (RepositoryException e1)
                {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                }
                List param = new ArrayList();

                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", script);
                config.put("params", param);
                // config.put("scriptContent", scriptContent);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                _rexecRequest.setServerHost(_rexecRequest.getServerHost());
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("agentmaintain-") > -1)
            { // 李洋增加-用于agent升级和监控 接口
                if (_rexecRequest.getId().indexOf("agentmaintain-monitor-") > -1
                        || _rexecRequest.getId().indexOf("agentmaintain-vcs-") > -1
                        || _rexecRequest.getId().indexOf("agentmaintain-smdb-") > -1)
                {
                    _rexecRequest.setAdaptorDefName(_rexecRequest.getAdaptorDefName());
                    _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                    _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                    _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                    _rexecRequest.setFlowId(0);
                    _rexecRequest.setFlowPoolNum(0);
                    _rexecRequest.setId(_rexecRequest.getId());
                    _rexecRequest.setLevelOfPRI("5");
                    _rexecRequest.setLevelOfWeight("1");
                    _rexecRequest.setProjectName(_rexecRequest.getProjectName());
                    String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                        _rexecRequest.getServerHost());
                    _rexecRequest.setServerHost(serverIp);
                    _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                    _rexecRequest.setStatus(2);
                    _rexecRequest.setTimeout(new Long(0));
                    _rexecRequest.setLevelOfPRI("5");
                    _rexecRequest.setLevelOfWeight("1");
                    Map agentMaintainParam = new HashMap();
                    try
                    {
                        String icustom_cmd = AgentMaintainManager.getInstance()
                                .getIcustomCmd(_rexecRequest.getAgentHost(), _rexecRequest.getAgentPort());
                        icustom_cmd = icustom_cmd == null ? "" : icustom_cmd;
                        // 增加光大SMDB集成，当类型为SMDB时，获取固定的脚本执行。
                        if (_rexecRequest.getId().indexOf("agentmaintain-smdb-") > -1)
                        {
                            Agent agentInfo = AgentMaintainManager.getInstance().getAgentByIpPort(
                                _rexecRequest.getAgentHost(), _rexecRequest.getAgentPort(), Constants.IEAI_IEAI_BASIC);
                            if (agentInfo != null && !StringUtil.isBlank(agentInfo.getIosName())
                                    && (agentInfo.getIosName().indexOf("Win") > -1
                                            || agentInfo.getIosName().indexOf("win") > -1))
                            {
                                icustom_cmd = PersonalityEnv.getSmdbShellnameWinValue() + " " + icustom_cmd;
                            } else
                            {
                                icustom_cmd = PersonalityEnv.getSmdbShellnameLinuxValue() + " " + icustom_cmd;
                            }
                        }
                        Hashtable t = new Hashtable();
                        t.put("icustom_cmd", icustom_cmd == null ? "" : icustom_cmd);
                        _rexecRequest.setInput(t);// ?默认值
                        agentMaintainParam = AgentMaintainManager.getInstance()
                                .getAgentMaintainParameter(_rexecRequest.getId());
                    } catch (Exception e)
                    {
                        _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                    }
                    DefaultConfig dc = new DefaultConfig();
                    HashMap map = new HashMap();
                    map.putAll(agentMaintainParam);
                    dc.putAll(map);
                    dc.put("proxyJson", _rexecRequest.getProxyJson());
                    _rexecRequest.setAdaptorConfig(dc);

                } else
                {
                    Map agentMaintainParam = new HashMap();
                    try
                    {
                        agentMaintainParam = AgentMaintainManager.getInstance()
                                .getAgentMaintainParameter(_rexecRequest.getId());
                    } catch (Exception e)
                    {
                        _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                    }
                    String paramer="";
                    Hashtable input=new Hashtable();
                    try{
                        if(Integer.parseInt(agentMaintainParam.get("fileUpdateIID").toString())!=0){
                            DownLoadModel model=new DownLoadModel();
                            model.setIid(Long.parseLong(agentMaintainParam.get("fileUpdateIID").toString()));
                            List<DownLoadModel> downLoadModels = FileUploadManager.getInstance().downLoadWebServer(model);
                            if(downLoadModels.size()>0){
                                paramer = FileUploadManager.getInstance().paramer(downLoadModels.get(0));
                            }
                        }
                    }catch (Exception e){
                        _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                    }
                    if(StringUtils.isNotBlank(paramer)){
                        input.put("fileIssueAgent",paramer);
                        _rexecRequest.setInput(input);
                    }
                    DefaultConfig dc = new DefaultConfig();
                    HashMap map = new HashMap();
                    map.putAll(agentMaintainParam);
                    dc.putAll(map);
                    dc.put("proxyJson", _rexecRequest.getProxyJson());
                    _rexecRequest.setAdaptorConfig(dc);
                    _rexecRequest.setLevelOfPRI("5");
                    _rexecRequest.setLevelOfWeight("1");
                }
            } else if (_rexecRequest.getId().indexOf("ieai-intall-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-checkini-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-CheckInstall-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-vmInstall-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-optDivideVLAN-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-asm-") > -1
                    || _rexecRequest.getId().indexOf("ieai-resapp-") > -1
                    || _rexecRequest.getId().indexOf("ieai-resrecover-") > -1)// ieai-resrecover
            {
                _rexecRequest.setAdaptorDefName(_rexecRequest.getAdaptorDefName());
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName(_rexecRequest.getProjectName());
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                DefaultConfig dc = new DefaultConfig();
                // 根据reqid获取检查项中对应的操作指令。
                String cmd = "";
                String resId = "";
                String setmodel = "1";
                HashMap map = new HashMap();
                try
                {
                    ConfigReader cfr = ConfigReader.getInstance();
                    cfr.init();

                    if (_rexecRequest.getId().indexOf("ieai-resapp-") > -1)
                    {
                        String[] ids = _rexecRequest.getId().split("ieai-resapp-");
                        resId = ids[1].substring(0, ids[1].length() - 1);
                        setmodel = ids[2].substring(0, ids[2].length() - 1);
                        Map<String, Object> mappara = ScriptsManager.getInstance()
                                .getResappParamSubValue(Long.parseLong(resId));
                        List result1 = (List) mappara.get("result1");
                        List result2 = (List) mappara.get("result2");
                        String para1 = StringUtils.join(result1, "%%%");
                        String para2 = StringUtils.join(result2, "%%%");
                        String plateform = String.valueOf(mappara.get("plateform"));
                        if (null == plateform || "".equals(plateform) || "null".equals(plateform))
                        {
                            plateform = "";
                        }
                        String script = "";
                        String scripts = "";
                        String scriptsWin = "";

                        if ("1".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECRAC_SCRIPT_PATH, "/opt/");
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECRAC_SCRIPT_PATH_WIN, "/opt/");
                        } else if ("2".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECINSTANCE_SCRIPT_PATH, "/opt/");
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECINSTANCE_SCRIPT_PATH_WIN, "/opt/");
                        } else if ("3".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECDATAGUARD_SCRIPT_PATH, "/opt/");
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECDATAGUARD_SCRIPT_PATH_WIN, "/opt/");
                        } else if ("4".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECUSER_SCRIPT_PATH, "/opt/");
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECUSER_SCRIPT_PATH_WIN, "/opt/");
                        }

                        if ("WINDOWS".equals(plateform))
                        {
                            script = "cmd.exe /c " + scriptsWin;
                        } else
                        {
                            script = "sh " + scripts;
                        }
                        cmd = script + " " + para1 + " " + para2;
                    } else if (_rexecRequest.getId().indexOf("ieai-asm-") > -1)
                    {
                        String[] ids = _rexecRequest.getId().split("ieai-asm-");
                        resId = ids[1].substring(0, ids[1].length() - 1);
                        String scripts = cfr.getProperties(Environment.DBAAS_SS_ASM_SCRIPT_PATH, "/opt/");
                        String scriptsWin = cfr.getProperties(Environment.DBAAS_SS_ASM_SCRIPT_PATH_WIN, "/opt/");
                        Map<String, Object> mappara = ScriptsManager.getInstance().getAsmParam(Long.parseLong(resId));
                        String sys = String.valueOf(mappara.get("IOPERSYS")).toUpperCase();
                        if ("WINDOWS".equals(sys))
                        {
                            cmd = "cmd.exe /c " + scriptsWin;
                        } else
                        {
                            cmd = "sh " + scripts;
                        }
                    } else if (_rexecRequest.getId().indexOf("ieai-resrecover-") > -1)
                    {
                        String[] ids = _rexecRequest.getId().split("ieai-resrecover-");
                        resId = ids[1].substring(0, ids[1].length() - 1);
                        Map<String, Object> mappara = ScriptsManager.getInstance()
                                .getResappServices(Long.parseLong(resId));
                        String sid = String.valueOf(mappara.get("ISID"));
                        String platform = String.valueOf(mappara.get("platform"));
                        setmodel = String.valueOf(mappara.get("setmodel"));
                        String usname = String.valueOf(mappara.get("userName"));
                        String scripts = cfr.getProperties(Environment.DBAAS_SS_EXECINSTANCE_RECOVER_SCRIPT_PATH,
                            "/opt/");
                        String scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECINSTANCE_RECOVER_SCRIPT_PATH_WIN,
                            "/opt/");
                        if ("1".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECRAC_RECOVER_SCRIPT_PATH, "/opt/")
                                    + sid;
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECRAC_RECOVER_SCRIPT_PATH_WIN,
                                "/opt/") + sid;
                        } else if ("2".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECINSTANCE_RECOVER_SCRIPT_PATH, "/opt/")
                                    + sid;
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECINSTANCE_RECOVER_SCRIPT_PATH_WIN,
                                "/opt/") + sid;
                        } else if ("3".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECDATAGUARD_RECOVER_SCRIPT_PATH, "/opt/")
                                    + sid;
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECDATAGUARD_RECOVER_SCRIPT_PATH_WIN,
                                "/opt/") + sid;
                        } else if ("4".equals(setmodel))
                        {
                            scripts = cfr.getProperties(Environment.DBAAS_SS_EXECUSER_RECOVER_SCRIPT_PATH, "/opt/")
                                    + " sid=" + sid + "%%%dbusername=" + usname;
                            scriptsWin = cfr.getProperties(Environment.DBAAS_SS_EXECUSER_RECOVER_SCRIPT_PATH_WIN,
                                "/opt/") + " sid=" + sid + "%%%dbusername=" + usname;
                        }
                        if ("WINDOWS".equals(platform))
                        {
                            cmd = "cmd.exe /c " + scriptsWin;
                        } else
                        {
                            cmd = "sh " + scripts;
                        }
                    }
                    _log.info(cmd);
                } catch (Exception e)
                {
                    _log.error("this._rexecRequest.getId():" + this._rexecRequest.getId(), e);
                }
                map.put("command", cmd);
                // map.put("params", new Hashtable());
                map.put("startIn", "");
                map.put("timeout", "");
                map.put("executeUsername", "");
                dc.putAll(map);
                _rexecRequest.setAdaptorConfig(dc);
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
            } else if (_rexecRequest.getId().indexOf("ieai-rpa-") > -1)
            {
                DefaultConfig dc = (DefaultConfig) _rexecRequest.getAdaptorConfig();
                String filename = "";
                if (null == dc)
                {
                    String command = (String) _rexecRequest.getInput().get("command");
                    filename = command;
                    String scriptContent = (String) _rexecRequest.getInput().get("scriptContent");
                    _rexecRequest.getInput().put("command", command);
                    _rexecRequest.getInput().put("scriptContent", scriptContent);
                    _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                } else
                {
                    filename = (String) dc.get("command");
                    _rexecRequest.getInput().put("command", filename);
                    _rexecRequest.getInput().put("scriptContent", "......");
                    _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                }
                MapObject aneex = new MapObject();

                try
                {
                    // 获取文件内容
                    StringBuilder result = new StringBuilder();
                    BufferedReader br = null;
                    String charsetName = "";
                    charsetName = filename.split(" ")[0];
                    String filePath = GetFileName.getInstance().getpathDir() + File.separator + charsetName;
                    /*
                     * String format = FileUtil.getFileCharsetByPath(filePath);
                     * if (StringUtils.isNotBlank(format))
                     * {
                     * br = new BufferedReader(new InputStreamReader(new FileInputStream(new
                     * File(filePath)), format));
                     * } else
                     * {
                     * br = new BufferedReader(new InputStreamReader(new FileInputStream(new
                     * File(filePath))));
                     * }
                     */
                    br = new BufferedReader(new InputStreamReader(new FileInputStream(new File(filePath)), "UTF-8"));
                    String lineTxt = null;
                    while ((lineTxt = br.readLine()) != null)
                    {
                        result.append(lineTxt).append("\n");
                    }
                    BinaryObject binaryObject = new BinaryObject(result.toString().getBytes("UTF-8"));
                    aneex.put(charsetName, binaryObject);
                } catch (Exception e)
                {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
                _rexecRequest.getInput().put("annexFiles", aneex);

                ActivityElement actElem = getActElem(Constants.IEAI_IEAI_BASIC);
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setAdaptorConfig(actElem.getActConfig());
                _rexecRequest.setLevelOfPRI(actElem.getLevelOfPRI());
                _rexecRequest.setLevelOfWeight(actElem.getLevelOfWeight());
            }else if(_rexecRequest.getId().startsWith("maop_")){
                _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setSafeFirst(true);
                _rexecRequest.setProjectName(_rexecRequest.getProjectName());
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
//                _rexecRequest.getInput().put("scriptContent", scriptContent);
            } else
            {

                if (_rexecRequest.getInput() != null && _rexecRequest.getInput().get("command") != null)
                {
                    String command = (String) _rexecRequest.getInput().get("command");
                    command = command.trim().replaceAll("\\s+", " ");
                    String scriptContent = "";
                    StringBuilder changedcommand = new StringBuilder();
                    if (!"".equals(command))
                    {
                        boolean flag = false;
                        boolean fjnxFlag = false;
                        boolean sendScriptFlag = false;
                        boolean sendScriptServiceFlag = false;
                        boolean passParams = false;
                        String[] commands = command.split(" ");
                        for (int i = 0; i < commands.length; i++)
                        {
                            if ("-s".equals(commands[i])&&i+1<commands.length&&commands[i+1].matches("\\##(.*?)\\##"))//这个位置判断，-s 搭配 ###脚本服务化###才是脚本服务化
                            {
                                flag = true;
                                changedcommand.append(" ");
                            }else if ("-s".equals(commands[i])&&i+1<commands.length&&commands[i+1].matches("\\@@(.*?)\\@@"))//这个位置判断，-s 搭配 @@@脚本服务化@@@是福建农信共享插件
                            {
                                fjnxFlag = true;
                                changedcommand.append(" ");
                            } else
                            {
//                                if(null != commands[i] && commands[i].contains(Constants.LAST_LINE)) {
//                                    passParams = true;
//                                    commands[i] = ExcelActUtil.getInstance().replaceLastLine(commands[i], _rexecRequest.getFlowId());
//                                    changedcommand.append("".equals(commands[i]) ? " " : " " + commands[i]);
//                                }else{
                                    changedcommand.append("".equals(commands[i]) ? " " : " " + commands[i]);
//                                }
                            }

                        }
//                        if(passParams){
//                            _log.info(_rexecRequest.getFlowId() + " :changedcommand:" + changedcommand);
//                            _rexecRequest.getInput().put("command", changedcommand);
//                        }
                        if (flag)
                        {
                            // 灾备切换脚本管理下发
                            String changedcommandsZS = changedcommand.toString();
                            Pattern pattern = Pattern.compile("\\##(.*?)\\##");
                            Matcher matcher = pattern.matcher(command);
                            while (matcher.find())
                            {
                                String filenName = matcher.group(1);
                                try
                                {
                                    scriptContent = ScriptsManager.getInstance()
                                            .queryscriptMessageByFileName(filenName);

                                    if (scriptContent == null || "".equals(scriptContent))
                                    {
                                        changedcommandsZS = changedcommandsZS.replaceAll("\\##" + filenName + "\\##",
                                            "loadfileerror");
                                    }
                                } catch (Exception e)
                                {
                                    changedcommandsZS = changedcommandsZS.replaceAll("\\##" + filenName + "\\##","loadfileerror");
                                    _log.error("load script is error", e);
                                }
                                sendScriptFlag = true;
                            }
                            if (sendScriptFlag)
                            {
                                _rexecRequest.getInput().put("command", changedcommandsZS);
                                _rexecRequest.getInput().put("scriptContent", scriptContent);
                                _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                            }
                            String serviceName = "";
                            // 脚本服务化脚本下发
                            Hashtable aneex = new Hashtable();
                            String changedcommands = changedcommand.toString();
                            Pattern patternService = Pattern.compile("\\###(.*?)\\###");
                            Matcher matcherService = patternService.matcher(command);
                            boolean isFlag = ServerEnv.getInstance().getBooleanConfigNew2(Environment.SCRIPT_UNION_GETREQUEST_ACT, false);
                            while (matcherService.find())
                            {
                                serviceName = matcherService.group(1);
                                _log.info("rexecRequestid:" + _rexecRequest.getId() + " old command:" + command);
                                _log.info("rexecRequestid:" + _rexecRequest.getId() + " serviceName:" + serviceName);
                                try
                                {
                                    Map map =repeatGetScript(command);
                                    scriptContent = getStringByMap(map, "icontent");
                                    String scriptName = getStringByMap(map, "iscriptname");
                                    String uuid = getStringByMap(map, "uuid");
                                    _log.info("rexecRequestid:" + _rexecRequest.getId() + " scriptName:" + scriptName);
                                    // 增加获取脚本服务化脚本重试功能
                                    int repeatNum = 0;
                                    while (("".equals(scriptContent) || "".equals(scriptName)) && repeatNum < 2)
                                    {
                                        map = repeatGetScript(command);
                                        scriptContent = getStringByMap(map, "icontent");
                                        scriptName = getStringByMap(map, "iscriptname");
                                        uuid = getStringByMap(map, "uuid");
                                        repeatNum++;
                                        Thread.sleep(1000);
                                    }
                                    
                                    getScriptDependAneexFiles(uuid, aneex,_rexecRequest.getInput());
                                    
                                    if (!"".equals(scriptContent) && !"".equals(scriptName))
                                    {
                                        changedcommands = changedcommands.replaceAll("\\###" + serviceName + "\\###",
                                            "\\##" + scriptName + "\\##");
                                    } else
                                    {
                                        changedcommands = changedcommands.replaceAll("\\###" + serviceName + "\\###",
                                            "loadfileerror");
                                    }
                                } catch (Exception e)
                                {
                                    changedcommands = changedcommands.replaceAll("\\###" + serviceName + "\\###",
                                            "loadfileerror");
                                    _log.error("load scriptService is error", e);
                                }
                                sendScriptServiceFlag = true;
                                break;
                            }
                            if (sendScriptServiceFlag)
                            {
                                _log.info(
                                    "rexecRequestid:" + _rexecRequest.getId() + " new command:" + changedcommands);
                                if ("sqladaptor".equals(_rexecRequest.getAdaptorDefName()))
                                {
                                    _rexecRequest.getInput().put("dbdriver",
                                        this.getDbDriverClass(String.valueOf(_rexecRequest.getInput().get("dbType"))));
                                    _rexecRequest.getInput().put("dburl", _rexecRequest.getInput().get("dbInfo"));
                                    _rexecRequest.getInput().put("servicesType", "sql");
                                    _rexecRequest.getInput().put("command", scriptContent);
                                    _rexecRequest.getInput().put("serviceName", serviceName);
                                    _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                                    _rexecRequest.getInput().put("annexFiles", aneex);
                                } else
                                {
                                    _rexecRequest.getInput().put("command", changedcommands);
                                    _rexecRequest.getInput().put("scriptContent", scriptContent);
                                    _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                                    _rexecRequest.getInput().put("annexFiles", aneex);
                                }
                            }
                        } else if(fjnxFlag){
                            //福建农信流水线共享资源插件
                            _log.error("福建农信共享资源插件下发逻辑开始");
                            String serviceName = "";
                            String regex = "\\@@@(.*?)\\@@@";
                            String changedcommands = changedcommand.toString();
                            Hashtable aneex = new Hashtable();
                            try {
                                Pattern pattern = Pattern.compile(regex);
                                Matcher matcher = pattern.matcher(command);
                                while (matcher.find())
                                {
                                    serviceName = matcher.group(1);
                                }
                                Map pluginScriptMap = getPluginScriptMap(serviceName);
                                scriptContent = getStringByMap(pluginScriptMap, "icontent");
                                String scriptName = getStringByMap(pluginScriptMap, "iscriptname");
                                if(!StringUtil.isBlank(scriptContent)){
                                    CDStartFlowManager.getInstance().getPluginContent(_rexecRequest.getFlowId(),aneex);
                                }
                                if (!"".equals(scriptContent) && !"".equals(scriptName))
                                {
                                    changedcommands = changedcommands.replaceAll("\\@@@" + serviceName + "\\@@@", "\\##" + scriptName + "\\##");
                                } else
                                {
                                    changedcommands = changedcommands.replaceAll("\\@@@" + serviceName + "\\@@@", "loadfileerror");
                                }
                            }catch (Exception e){
                                _log.error("load scriptService is error", e);
                            }
                            _rexecRequest.getInput().put("command", changedcommands);
                            _rexecRequest.getInput().put("scriptContent", scriptContent);
                            _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                            _rexecRequest.getInput().put("annexFiles", aneex);
                        }
                    }
                }
                //20210616 shellcmd处理流程预演
                if(StringUtils.isNotBlank(_rexecRequest.getAdaptorDefName()) && "shellcmd".equals(_rexecRequest.getAdaptorDefName())
                        && ServerEnv.getInstance().getSysConfig(Environment.STANDARD_PRE_EXEC_SWITCH, "false").equals("true")) {
                    Map<String, String> standardTaskMap = new HashMap<>();
                    try {
                        standardTaskMap = IcSPDBAuditManager.getInstance().getStandardFlowPreExec(_rexecRequest.getId());
                    } catch (RepositoryException e) {
                        _log.error("load standtask is error", e);
                    }
                    if (CollectionUtil.isNotEmpty(standardTaskMap)) {
                        //判断该shellcmd活动为预演
                        if (StringUtils.isNotBlank(standardTaskMap.get("preExec"))) {
                            if (StringUtils.isNotBlank(standardTaskMap.get("osName")) && standardTaskMap.get("osName").toUpperCase().indexOf("WINDOWS") >= 0)
                            {
                                _rexecRequest.getInput().put("command", "cmd.exe /c echo 0");
                            }else {
                                _rexecRequest.getInput().put("command", "echo 0");
                            }
                        }
                    }
                }


                ActivityElement actElem = getActElem(Constants.IEAI_IEAI_BASIC);
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setAdaptorConfig(actElem.getActConfig());
                _rexecRequest.setLevelOfPRI(actElem.getLevelOfPRI());
                _rexecRequest.setLevelOfWeight(actElem.getLevelOfWeight());
            }
        }
        return _rexecRequest;

    }

    public void getScriptDependAneexFiles ( String uuid, Hashtable aneex, Hashtable input ) throws Exception
    {
        List aneexFiles = null;
        try
        {
            IScriptExec scriptExec = IeaiSpringContextUtil.getDubboBean(IScriptExec.class);
            if (Objects.nonNull(scriptExec))
            {
                if (Objects.nonNull(input))
                {
                    Map mapa=scriptExec.invokeGetDependScripts(uuid);
                    List<String> denpendUuidList = (List)mapa.get("annexList");
                    List ha =  (List) mapa.get("dependScripts");
                    Hashtable dependScripts = new Hashtable();
                    if(null!=ha && ha.size()>0) {
                        BinaryEntityFiles blobEntity = null;
                        for (int i = 0; i < ha.size(); i++)
                        {
                            blobEntity = (BinaryEntityFiles) ha.get(i);
                            BinaryObject binaryObject = new BinaryObject(blobEntity.getContent());
                            dependScripts.put(blobEntity.getName(), binaryObject);
                        }
                        input.put("dependScripts", dependScripts);
                    }
                    aneexFiles = scriptExec.getAneexBinaryForServer(uuid, 0L, denpendUuidList);
                } else
                {
                    // input为空，那么只查下当前脚本的附件
                    List<String> denpendUuidList2 = new ArrayList<>();
                    denpendUuidList2.add(uuid);
                    aneexFiles = scriptExec.getAneexBinaryForServer(uuid, 0L, denpendUuidList2);
                }
            } else
            {
                throw new Exception("获取dubbo接口IScriptExec对象为NULL!!");
            }

        } catch (RepositoryException e1)
        {
            _log.error(" get AneexFile is Error!", e1);
        }
        if (null != aneexFiles && aneexFiles.size() > 0)
        {
            int n = 0;
            for (int m = 0; m < aneexFiles.size(); m++)
            {
                BinaryEntityFiles blobEntity = (BinaryEntityFiles) aneexFiles.get(m);
                BinaryObject binaryObject = new BinaryObject(blobEntity.getContent());
                aneex.put(blobEntity.getName(), binaryObject);
            }
        }
    }

    public String getStringByMap ( Map map, String key )
    {
        return map.get(key) == null ? "" : (String) map.get(key);
    }

   public Map repeatGetScript ( String command)
    {
        Map map = new HashMap();
        String scriptContent = "";
        String scriptName = "";
        String serviceName = "";
        Pattern patternService = Pattern.compile("\\###(.*?)\\###");
        Matcher matcherService = patternService.matcher(command);
        boolean isFlag = ServerEnv.getInstance().getBooleanConfigNew2(Environment.SCRIPT_UNION_GETREQUEST_ACT, false);
        while (matcherService.find())
        {
            serviceName = matcherService.group(1);
            _log.info("repeatGetScript rexecRequestid:" + _rexecRequest.getId() + " old command:" + command);
            _log.info("repeatGetScript rexecRequestid:" + _rexecRequest.getId() + " serviceName:" + serviceName);
            try
            {
                boolean iszk = false;
                if(isFlag) {
                    try
                    {
                        IScriptInstance instance = IeaiSpringContextUtil.getDubboBean(IScriptInstance.class);
                        if (Objects.nonNull(instance))
                        {
                            map = instance.getResultService(serviceName);
                            iszk = true;
                        }
                    } catch (Exception e)
                    {
                        _log.error("ClassPathXmlApplicationContext load scriptService is error", e);
                    }
                }
                if (!iszk || null == map || (null != map && map.isEmpty()))
                {
                    map = ScriptsManager.getInstance().queryscriptMessageByServiceName(serviceName);
                }
                _log.info("repeatGetScript rexecRequestid:" + _rexecRequest.getId() + " scriptName:" + scriptName);
            } catch (Exception e)
            {
                _log.error("load scriptService is error", e);
            }
            break;
        }
        return map;
    }


    /**
     * 获取插件脚本信息
     * @param serviceName
     * @return
     */
    public Map getPluginScriptMap (String serviceName)
    {
        Map map = new HashMap();
        String scriptName = "";
        boolean flag = ServerEnv.getInstance().getBooleanConfigNew2(Environment.SCRIPT_UNION_GETREQUEST_ACT, false);
        _log.info("getPluginScriptMap rexecRequestid:" + _rexecRequest.getId() + " serviceName:" + serviceName);
        try
        {
            if(flag) {
                try
                {
                    IScriptInstance instance = IeaiSpringContextUtil.getDubboBean(IScriptInstance.class);
                    if (Objects.nonNull(instance))
                    {
                        map = instance.getResultService(serviceName);
                    }
                } catch (Exception e)
                {
                    _log.error("ClassPathXmlApplicationContext load scriptService is error", e);
                }
            }
            if(CollectionUtil.isEmpty(map)){
                map = ScriptsManager.getInstance().queryscriptMessageByServiceName(serviceName);
            }
            _log.info("getPluginScriptMap rexecRequestid:" + _rexecRequest.getId() + " scriptName:" + scriptName);
        } catch (Exception e)
        {
            _log.error("load scriptService is error", e);
        }
        return map;
    }

    private void nessusReqMake ()
    {

        Long jobiid = NessusRepairHandler.getInstance().getJobiidWithReqid(Constants.IEAI_OPM,
            this._rexecRequest.getId());
        String cmd = NessusRepairHandler.getInstance().makeCmd(Constants.IEAI_OPM, jobiid);
        DefaultConfig dc = new DefaultConfig();
        HashMap map = new HashMap();
        map.put("cbxYorNWarn", true);
        map.put("command", cmd);
        map.put("params", new Hashtable());
        map.put("consoleInput", new ArrayList());
        map.put("params", new ArrayList());
        map.put("startIn", "");
        map.put("timeout", "");
        map.put("executeUsername", "");
        dc.putAll(map);
        _rexecRequest.setAdaptorConfig(dc);
        _rexecRequest.setLevelOfPRI("5");
        _rexecRequest.setLevelOfWeight("1");

    }

    public RemoteActivityExecRequest getRexecRequest ( int dbType ) throws ServerException
    {
        if (_rexecRequest.isSafeFirst())
        {
            if (_rexecRequest.getId().indexOf("cm") > -1)
            {

                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(0);
                _rexecRequest.setActId("cmShellCmd");
                _rexecRequest.setActName("cmShellCmd");
                DefaultConfig config = new DefaultConfig();
                String cmd = new String();
                cmd = cmd + "!@#$";
                cmd = cmd + _rexecRequest.getId() + ",";
                cmd = cmd + _rexecRequest.getServerHost() + ",";
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                // String
                // cmd=_rexecRequest.getId()+"************"+","+""+","+"set /p var=请输入用户名: echo
                // 您输入的用户名为%var% "+","+"2";
                try
                {
                    List<CmBean> listCmBean = bOpScheduler.getCmScriptIID(_rexecRequest.getId());
                    for (int i = 0; i < listCmBean.size(); i++)
                    {
                        CmBean cmBean = listCmBean.get(i);
                        if (i == 0)
                        {
                            byte[] content = LobStorer.getBlobData(Long.valueOf(cmBean.getId()));
                            if (content == null)
                            {
                                cmd = cmd + ",";
                            } else
                            {
                                String str = new String(content);
                                cmd = cmd + str + ",";
                            }
                        } else
                        {
                            byte[] content = LobStorer.getBlobData(Long.valueOf(cmBean.getId()));
                            if (content == null)
                            {
                                cmd = cmd + ",";
                                cmd = cmd + cmBean.getType();
                            } else
                            {
                                String str = new String(content);
                                cmd = cmd + str + ",";
                                cmd = cmd + cmBean.getType();
                            }
                        }

                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                config.put("command", cmd);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("cmshellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("cm");
                _rexecRequest.setServerHost(_rexecRequest.getServerHost());
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));

            } else if (_rexecRequest.getId().indexOf("ieai-auto-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-BigpkgBuild-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().startsWith(Constants.REQUEST_KEY_NESSUS))
            {
                this.nessusReqMake();
            } else if (_rexecRequest.getId().startsWith(Constants.REQUEST_KEY_COLLECT))
            {
                DefaultConfig dc = new DefaultConfig();
                // 根据reqid获取检查项中对应的操作指令。
                String cmd = CollectItemManager.getInstance()
                        .queryItemMessageForReqid(this._rexecRequest.getId(), Constants.IEAI_OPM).get(0).getShell();

                HashMap map = new HashMap();
                map.put("cbxYorNWarn", true);
                // try {
                // cmd = TimeTaskManager.getInstance().queryCmd(_rexecRequest.getId());
                // } catch (RepositoryException e) {
                // _log.error("RExecRequestWrapper.getRexecRequest () error" +e.getMessage());
                // }
                map.put("command", cmd);
                map.put("params", new Hashtable());
                map.put("consoleInput", new ArrayList());
                map.put("params", new ArrayList());
                map.put("startIn", "");
                map.put("timeout", "");
                map.put("executeUsername", "");
                dc.putAll(map);
                _rexecRequest.setAdaptorConfig(dc);
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
            } else if (_rexecRequest.getId().indexOf("timetask") > -1)
            {
                DefaultConfig dc = new DefaultConfig();
                // dc.setWorkflowId(1);
                HashMap map = new HashMap();
                map.put("cbxYorNWarn", true);
                String cmd = "";
                try
                {
                    cmd = TimeTaskManager.getInstance().queryCmd(_rexecRequest.getId());
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                map.put("command", cmd);
                // map.put("consoleInput",new ArrayList());
                // map.put("params", new ArrayList());
                // map.put("startIn", "");
                // map.put("timeout", "");
                // map.put("executeUsername", "");
                dc.putAll(map);
                _rexecRequest.setAdaptorConfig(dc);
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
            } else if (_rexecRequest.getId().indexOf("agentmaintain-") > -1)
            { // 李洋增加-用于agent升级和监控 接口
                if (_rexecRequest.getId().indexOf("agentmaintain-monitor-") > -1
                        || _rexecRequest.getId().indexOf("agentmaintain-vcs-") > -1
                        || _rexecRequest.getId().indexOf("agentmaintain-smdb-") > -1)
                {
                    _rexecRequest.setAdaptorDefName(_rexecRequest.getAdaptorDefName());
                    _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                    _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                    _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                    _rexecRequest.setFlowId(0);
                    _rexecRequest.setFlowPoolNum(0);
                    _rexecRequest.setId(_rexecRequest.getId());
                    _rexecRequest.setLevelOfPRI("5");
                    _rexecRequest.setLevelOfWeight("1");
                    _rexecRequest.setProjectName(_rexecRequest.getProjectName());
                    String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                        _rexecRequest.getServerHost());
                    _rexecRequest.setServerHost(serverIp);
                    _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                    _rexecRequest.setStatus(2);
                    _rexecRequest.setTimeout(new Long(0));
                    _rexecRequest.setLevelOfPRI("5");
                    _rexecRequest.setLevelOfWeight("1");
                    Map agentMaintainParam = new HashMap();
                    try
                    {

                        String icustom_cmd = AgentMaintainManager.getInstance()
                                .getIcustomCmd(_rexecRequest.getAgentHost(), _rexecRequest.getAgentPort());
                        Hashtable t = new Hashtable();
                        t.put("icustom_cmd", icustom_cmd == null ? "" : icustom_cmd);
                        _rexecRequest.setInput(t);// ?默认值
                        agentMaintainParam = AgentMaintainManager.getInstance()
                                .getAgentMaintainParameter(_rexecRequest.getId());
                    } catch (Exception e)
                    {
                        _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                    }
                    DefaultConfig dc = new DefaultConfig();
                    HashMap map = new HashMap();
                    map.putAll(agentMaintainParam);
                    dc.putAll(map);
                    _rexecRequest.setAdaptorConfig(dc);

                } else
                {
                    Map agentMaintainParam = new HashMap();
                    try
                    {
                        agentMaintainParam = AgentMaintainManager.getInstance()
                                .getAgentMaintainParameter(_rexecRequest.getId());
                    } catch (Exception e)
                    {
                        _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                    }
                    DefaultConfig dc = new DefaultConfig();
                    HashMap map = new HashMap();
                    map.putAll(agentMaintainParam);
                    dc.putAll(map);
                    _rexecRequest.setAdaptorConfig(dc);
                    _rexecRequest.setLevelOfPRI("5");
                    _rexecRequest.setLevelOfWeight("1");
                }
            } else if (_rexecRequest.getId().indexOf("ieai-intall-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-checkini-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-CheckInstall-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-vmInstall-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else if (_rexecRequest.getId().indexOf("ieai-optDivideVLAN-") > -1)
            {
                DbOpScheduler bOpScheduler = new DbOpScheduler();
                String cmd = "";
                String pkgs = "";
                List param = null;
                try
                {
                    Map maps = bOpScheduler.getAutoScriptInfo(_rexecRequest.getId());
                    if (null != maps)
                    {
                        cmd = (String) maps.get("command");
                        pkgs = (String) maps.get("params");
                        // param = new ArrayList();
                        // param.add(pkgs);
                    }
                } catch (RepositoryException e)
                {
                    _log.error("RExecRequestWrapper.getRexecRequest () error" + e.getMessage());
                }
                _rexecRequest.setActStateDataVersion(-1);
                _rexecRequest.setScopeId(162865);
                _rexecRequest.setActId("ShellCmd");
                _rexecRequest.setActName("ShellCmd");
                DefaultConfig config = new DefaultConfig();
                config.put("command", cmd);
                config.put("params", param);
                _rexecRequest.setAdaptorConfig(config);
                _rexecRequest.setAdaptorDefName("shellcmd");
                _rexecRequest.setAdaptorDefUUID(_rexecRequest.getAdaptorDefUUID());
                _rexecRequest.setAgentHost(_rexecRequest.getAgentHost());
                _rexecRequest.setAgentPort(_rexecRequest.getAgentPort());
                _rexecRequest.setFlowId(0);
                _rexecRequest.setFlowPoolNum(0);
                _rexecRequest.setId(_rexecRequest.getId());
                _rexecRequest.setLevelOfPRI("5");
                _rexecRequest.setLevelOfWeight("1");
                _rexecRequest.setProjectName("shell_test");
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setServerPort(_rexecRequest.getServerPort());
                _rexecRequest.setStatus(2);
                _rexecRequest.setTimeout(new Long(0));
            } else
            {

                if (_rexecRequest.getInput() != null && _rexecRequest.getInput().get("command") != null)
                {
                    String command = (String) _rexecRequest.getInput().get("command");
                    command = command.trim();
                    String scriptContent = "";
                    StringBuilder changedcommand = new StringBuilder();
                    if (!"".equals(command))
                    {
                        boolean flag = false;
                        boolean sendScriptFlag = false;
                        String[] commands = command.split(" ");
                        for (int i = 0; i < commands.length; i++)
                        {
                            if ("-s".equals(commands[i]))
                            {
                                flag = true;
                                changedcommand.append(" ");
                            } else
                            {
                                changedcommand.append("".equals(commands[i]) ? " " : commands[i]);
                            }
                        }
                        if (flag)
                        {
                            Pattern pattern = Pattern.compile("\\##(.*?)\\##");
                            Matcher matcher = pattern.matcher(command);
                            while (matcher.find())
                            {
                                String filenName = matcher.group(1);
                                try
                                {
                                    scriptContent = ScriptsManager.getInstance()
                                            .queryscriptMessageByFileName(filenName);
                                } catch (Exception e)
                                {
                                    _log.error("load script is error", e);
                                }
                                sendScriptFlag = true;
                            }
                            if (sendScriptFlag)
                            {
                                _rexecRequest.getInput().put("command", changedcommand.toString());
                                _rexecRequest.getInput().put("scriptContent", scriptContent);
                                _rexecRequest.getInput().put("requestId", _rexecRequest.getId());
                            }
                        }
                    }
                }
                ActivityElement actElem = getActElem(dbType);
                String serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST,
                    _rexecRequest.getServerHost());
                _rexecRequest.setServerHost(serverIp);
                _rexecRequest.setAdaptorConfig(actElem.getActConfig());
                _rexecRequest.setLevelOfPRI(actElem.getLevelOfPRI());
                _rexecRequest.setLevelOfWeight(actElem.getLevelOfWeight());
            }
        }
        return _rexecRequest;
    }

    public RemoteActivityExecRequest getCmRexecRequest ( String requestId, String Serverip, int ServerPort,
            String Cmsuscrinfo, String Cmscriptinfo, String icmscripttype, String agentIp, int agentPort, String proId )
            throws ServerException
    {
        if (_rexecRequest.isSafeFirst())
        {
            _rexecRequest.setActStateDataVersion(-1);
            _rexecRequest.setScopeId(0);
            _rexecRequest.setActId("cmShellCmd");
            _rexecRequest.setActName("cmShellCmd");
            DefaultConfig config = new DefaultConfig();
            String cmd = new String();
            cmd = cmd + "!@#$";
            cmd = cmd + requestId + ",";
            cmd = cmd + Serverip + ",";
            DbOpScheduler bOpScheduler = new DbOpScheduler();
            // String
            // cmd=_rexecRequest.getId()+"************"+","+""+","+"set /p var=请输入用户名: echo
            // 您输入的用户名为%var% "+","+"2";
            try
            {
                List<CmBean> listCmBean = bOpScheduler.getCmScriptIID(_rexecRequest.getId());
                for (int i = 0; i < listCmBean.size(); i++)
                {
                    CmBean cmBean = listCmBean.get(i);
                    if (i == 0)
                    {
                        byte[] content = LobStorer.getBlobData(Long.valueOf(cmBean.getId()));
                        if (content == null)
                        {
                            cmd = cmd + ",";
                        } else
                        {
                            String str = new String(content);
                            cmd = cmd + str + ",";
                        }
                    } else
                    {
                        byte[] content = LobStorer.getBlobData(Long.valueOf(cmBean.getId()));
                        if (content == null)
                        {
                            cmd = cmd + ",";
                            cmd = cmd + cmBean.getType();
                        } else
                        {
                            String str = new String(content);
                            cmd = cmd + str + ",";
                            cmd = cmd + cmBean.getType();
                        }
                    }

                }
            } catch (RepositoryException e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            config.put("command", cmd);
            _rexecRequest.setAdaptorConfig(config);
            _rexecRequest.setAdaptorDefName("cmshellcmd");
            _rexecRequest.setAdaptorDefUUID(proId);
            _rexecRequest.setAgentHost(agentIp);
            _rexecRequest.setAgentHost(String.valueOf(agentPort));
            _rexecRequest.setFlowId(0);
            _rexecRequest.setFlowPoolNum(0);
            _rexecRequest.setId(requestId);
            _rexecRequest.setLevelOfPRI("5");
            _rexecRequest.setLevelOfWeight("1");
            _rexecRequest.setProjectName("cm");
            _rexecRequest.setServerHost(Serverip);
            _rexecRequest.setServerPort(ServerPort);
            _rexecRequest.setStatus(2);
            _rexecRequest.setTimeout(new Long(0));
        }
        return _rexecRequest;
    }

    public void setActElem ( ActivityElement actElem )
    {
        _actElem = actElem;
    }

    public RExecRequestWrapper(RemoteActivityExecRequest rexecRequest)
    {
        _rexecRequest = rexecRequest;
    }

    public IExecContext getCtx ()
    {
        if (_rexecRequest.isSafeFirst())
        {
            return new DBExecContext(_rexecRequest.getFlowId(), _rexecRequest.getScopeId());
        } else
        {
            return _ctx;
        }
    }

    public void setCtx ( IExecContext ctx )
    {
        _ctx = ctx;
    }

    public void setRexecRequest ( RemoteActivityExecRequest rexecRequest )
    {
        _rexecRequest = rexecRequest;
    }

    public String getDbDriverClass ( String dbType )
    {
        String dbDriver = "oracle.jdbc.driver.OracleDriver";
        if ("oracle".equalsIgnoreCase(dbType))
        {
            dbDriver = "oracle.jdbc.driver.OracleDriver";
        } else if ("mysql".equalsIgnoreCase(dbType))
        {
            dbDriver = "com.mysql.jdbc.Driver";
        } else if ("db2".equalsIgnoreCase(dbType))
        {
            dbDriver = "com.ibm.db2.jcc.DB2Driver";
        }
        return dbDriver;
    }

    private static final Logger _log = Logger.getLogger(RExecRequestWrapper.class);
}
