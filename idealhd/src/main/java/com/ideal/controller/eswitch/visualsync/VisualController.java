package com.ideal.controller.eswitch.visualsync;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.clientapi.ws.WsClientSession;
import com.ideal.ieai.clientapi.ws.WsUserBasicInfo;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.user.UserManager;
import com.ideal.ieai.usertask.IEAIWSClientHD;
import com.ideal.ieai.usertask.UTConfigHD;
import com.ideal.ieai.usertask.utils.UTSessionDataHD;


@Controller
public class VisualController
{
    private Logger log = Logger.getLogger(VisualController.class);
    private static final String       CLIENT_TYPE           = "HD";

    @RequestMapping("cibinitscreen.do")
    @ResponseBody
    public Map cibinitscreen( HttpServletRequest request, HttpServletResponse response, String username,String password,String type )
    {
        Map res = new HashMap();
        try
        {
            if(!"aim".equals(type.trim())) {
                log.error("登录失败,类型错误");            
                res.put(Constants.STR_MESSAGE, "登录失败,类型错误");
                res.put(Constants.STR_SUCCESS, false);
                return res;       
            }       
            SessionData sessionData = SessionData.getSessionData(request);
            if (null == UTConfigHD.getInstance() && !UTConfigHD.init(request))
            {
                log.error("登录失败");
                res.put(Constants.STR_MESSAGE, "登录失败");
                res.put(Constants.STR_SUCCESS, false);
                return res; 
            }
            List user = UserManager.getInstance().getUsersById(username);
            if (user.isEmpty())
            {
                log.error("登录失败,用户不存在");
                res.put(Constants.STR_MESSAGE, "登录失败,用户不存在");
                res.put(Constants.STR_SUCCESS, false);
                return res;
            }
            WsClientSession sessionUT = new WsClientSession();
            sessionUT.setClient(CLIENT_TYPE);
            sessionUT.setLoginName(username);
            sessionUT = IEAIWSClientHD.getIEAIService().login(sessionUT, (String) password);
            String sessionId = sessionUT.getId();
            if (null == sessionId)
            {
                log.error("登录失败,sessionId为空");
                res.put(Constants.STR_MESSAGE, "登录失败,sessionId为空");
                res.put(Constants.STR_SUCCESS, false);
                return res;
            }
            WsUserBasicInfo uesrInfo = IEAIWSClientHD.getIEAIService().getSelfInfo(sessionUT.getId());
            UTSessionDataHD sessionDataUT = UTSessionDataHD.createSession(request.getSession());
            sessionDataUT.setClientSession(sessionUT);
            sessionDataUT.setUserLoginName(username);
            sessionDataUT.setUserInfo(uesrInfo);
            sessionData.setSessionId(sessionUT.getId());
            sessionData.setUserInnerCode(sessionUT.getUserId().toString());
            sessionData.setLoginName(username);
            sessionData.setUserName(sessionUT.getUserFullName());
            log.info("登录成功");
            response.sendRedirect("initScreenStart.do");
            return null;
        } catch (Exception e)
        {
            log.error("登录失败,密码不正确", e);
            res.put(Constants.STR_MESSAGE, "登录失败,密码不正确");
            res.put(Constants.STR_SUCCESS, false);
            return res;
        }
    } 
}
