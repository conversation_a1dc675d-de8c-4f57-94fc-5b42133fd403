INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (5,'定时任务','定时任务模块组',5,'images/info11.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(5001, '任务管理', 5, 'ttManage.do', 3, '定时任务', 'images/info98.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(5002, '任务监控', 5, 'timetaskMonitorHome.do', 4, '定时任务', 'images/info20.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(5003, '历史查询', 5, 'ttHisHome.do', 5, '定时任务', 'images/info35.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(5004, '预启任务', 5, 'expectedtask.do', 6, '定时任务', 'images/info38.png');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5001, '保存', 'ttManage/saveRows.do', '定时任务--任务管理--保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5002, '删除', 'ttManage/deleteRows.do', '定时任务--任务管理--删除');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5003, '启动', 'ttManage/startBefore.do', '定时任务--任务管理--启动');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5004, '停止', 'ttManage/stopBefore.do', '定时任务--任务管理--停止');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5005, '导入', 'ttManage/importExcel.do', '定时任务--任务管理--导入');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5006, '增加', 'ttManage/saveIp.do', '定时任务--任务管理--设备变更(IP)--增加');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5007, '删除', 'ttManage/deleteIp.do', '定时任务--任务管理--设备变更(IP)--删除');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5008, '强行终止', 'timetaskMonitorForcestop.do', '定时任务--任务监控--IP窗口--强行终止');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5009, '重试', 'timetaskMonitorRedo.do', '定时任务--任务监控--IP窗口--重试');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5010, '延长超时', 'ttTimeout/timetaskMonitorDelay.do', '定时任务--延长超时');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(5011, '取消预启', 'cancleExpectedTask.do', '定时任务--预启任务--取消预启');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5001, 5001, 5001, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5002, 5001, 5002, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5003, 5001, 5003, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5004, 5001, 5004, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5005, 5001, 5005, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5006, 5001, 5006, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5007, 5001, 5007, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5008, 5002, 5008, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5009, 5002, 5009, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5010, 5002, 5010, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(5011, 5004, 5011, '');


commit;