<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="java.lang.*"%>
<%@ page import="net.sf.json.JSONArray"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>长沙银行业务系统架构图</title>
    <script type="text/javascript">
        var path='<%=request.getContextPath()%>';
    </script>
    <link rel="stylesheet" href="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo_cs/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo_cs/css/bootstrap.min.css" />
    <link rel="stylesheet" href="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo_cs/css/style.css" />
    <link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/ext/resources/css/ext-all.css"/>

    <script type="text/javascript" src="<%=request.getContextPath()%>/ext/ext-all.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/ext/locale/ext-lang-zh_CN.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo_cs/js/jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo_cs/js/jquery.nicescroll.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/extjs/js/jquery-ui.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo_cs/cmdbscreentopo_cs.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/idealcmdb/cmdbscreentopo/cmdbTypeRelationWindow.js"></script>
</head>
<body>
<%--获取节点数据--%>
<%
    List<Map<String,String>> esbUltimate = (List<Map<String,String>> )request.getAttribute("esbUltimate");/*ESB*/
    List<Map<String,String>> icopUltimate = (List<Map<String,String>> )request.getAttribute("icopUltimate");/*ICOP*/
    List<Map<String,String>> wireDataList = (List<Map<String,String>> )request.getAttribute("wireDataList");/*连线*/
%>

    <div class="csframe_bg">
        <div class="csframe_header">
            <div class="header_con" id = "saveStyle">
                长沙银行业务系统架构图
            </div>
        </div>
        <div class="csframe_main">
            <div class="csframe_l ">
                <div class="csframe_con_main scrollauto">
                    <ul>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_1"></div>
                                <h2>UAT环境UAT环境</h2>
                            </a>
                        </li>
                    </ul>
                    <ul>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_1"></div>
                                <h2>UAT环境UAT环境</h2>
                            </a>
                        </li>

                    </ul>
                </div>
                <div class="clear"></div>
            </div>
            <div class="csframe_c">
                <div class="csframe_c_system">
                    <div class="csframe_con_main scrollauto">
                        <%--箭头--%>
                        <svg>
                            <defs>
                                <marker id="arrow" markerUnits="strokeWidth" markerWidth="12" markerHeight="12" viewBox="0 0 12 12" refX="6" refY="6" orient="auto">
                                    <path xmlns="http://www.w3.org/2000/svg" d="M2,2 L10,6 L2,10 L6,6 L2,2" style="fill: #0de3e3;" />
                                </marker>
                            </defs>
                        </svg>
                        <%--连线--%>
                            <%
                                for (Map line:wireDataList)
                                {
                                    String id = String.valueOf(line.get("id"));
                                    String source = String.valueOf(line.get("source"));
                                    String target = String.valueOf(line.get("target"));
                            %>
                            <svg class="lineWrap">
                                <line id= <%=id%> xmlns="http://www.w3.org/2000/svg" x1="0" y1="0" x2="0" y2="0" stroke="#0de3e3" stroke-width="1" marker-end="url(#arrow)"
                                      stroke-dasharray="10,10"></line>
                            </svg>
                            <%
                                }
                            %>

                        <ul><%--ICOP系统--%>
                            <%
                                int i = 1;
                                int top= 100;
                                int left= 100;
                                for (Map esbson:icopUltimate)
                                {
                                    String id = String.valueOf(esbson.get("id"));
                                    String value = String.valueOf(esbson.get("value"));
                                    String itypeid = String.valueOf(esbson.get("itypeid"));
                                    String icmdbinfoid = String.valueOf(esbson.get("icmdbinfoid"));
                                    String icmdbtyperelationid = String.valueOf(esbson.get("icmdbtyperelationid"));
                                    String irelationid = String.valueOf(esbson.get("irelationid"));//style
                                    String style = String.valueOf(esbson.get("style"));//style
                                    if(style.length() == 0 )
                                    {
                                        style = "top:"+top+"px;left:"+left+"px;";
                                        left+=5;
                                    }
                                    if(i == 12)
                                    {
                                        i = 1;
                                    }
                            %>
                                <li class="hj_item" id= <%=id%> value=<%=value%> style= <%=style%> icmdbinfoid=<%=icmdbinfoid%> icmdbtyperelationid=<%=icmdbtyperelationid%> irelationid=<%=irelationid%> itypeid=<%=itypeid%>>
                                    <a href="#">
                                        <h2 style="font-size: 5px"><%=value%></h2>
                                        <div class="ico_style_gg ico_style_<%=i++%>" ></div>
                                    </a>
                                </li>
                            <%
                                }
                            %>
                        </ul>
                    </div>
                </div>
                <div class="clear"></div>
                <div class="csframe_c_cen">
                    <ul>
                        <li class="hj_item" style="opacity: 1; top: 29px; left: -1px;">
                            <a href="#">
                                <div class="ico_style_gg ico_style_1"></div>
                                <h2>ICOP系统</h2>
                            </a>
                        </li>
                        <li class="hj_item" style="top: 101px; left: 1px; opacity: 1;">
                            <a href="#">
                                <div class="ico_style_gg ico_style_2"></div>
                                <h2>ESB系统</h2>
                            </a>
                        </li>
                    </ul>

                </div>
                <div class="csframe_c_system">
                    <div class="csframe_con_main scrollauto">
                        <div class="csframe_con_main scrollauto">
                            <ul><%--ESB系统--%>
                                <%
                                    int j = 1;
                                    int tope= 650;
                                    int lefte= 100;
                                    for (Map esbson:esbUltimate)
                                    {
                                        String id = String.valueOf(esbson.get("id"));
                                        String value = String.valueOf(esbson.get("value"));
                                        String icmdbinfoid = String.valueOf(esbson.get("icmdbinfoid"));
                                        String icmdbtyperelationid = String.valueOf(esbson.get("icmdbtyperelationid"));
                                        String irelationid = String.valueOf(esbson.get("irelationid"));
                                        String itypeid = String.valueOf(esbson.get("itypeid"));
                                        String style = String.valueOf(esbson.get("style"));//style
                                        if(style.length() == 0 )
                                        {
                                            style = "top:"+tope+"px;left:"+lefte+"px;";
                                            lefte+=5;
                                        }
                                        if(j == 12)
                                        {
                                            j = 1;
                                        }
                                %>
                                <li class="hj_item" id= <%=id%> value=<%=value%> style=<%=style%> icmdbinfoid=<%=icmdbinfoid%> icmdbtyperelationid=<%=icmdbtyperelationid%> irelationid=<%=irelationid%> itypeid=<%=itypeid%> >
                                    <a href="#">
                                        <h2><%=value%></h2>
                                        <div class="ico_style_gg ico_style_<%=j++%>" value = <%=value%>></div>
                                    </a>
                                </li>
                                <%
                                    }
                                %>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="csframe_r">
                <div class="r_demo_1">
                    <ul>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_1"></div>
                                <h2>UAT环境</h2>
                            </a>
                        </li>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_2"></div>
                                <h2>UAT环境</h2>
                            </a>
                        </li>
                    </ul>
                    <div class="clear"></div>
                </div>
                <div class="r_demo_2">
                    <ul>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_1"></div>
                                <h2>UAT环境</h2>
                            </a>
                        </li>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_2"></div>
                                <h2>UAT环境</h2>
                            </a>
                        </li>
                        <li class="hj_item">
                            <a href="#">
                                <div class="ico_style_gg ico_style_2"></div>
                                <h2>UAT环境</h2>
                            </a>
                        </li>
                    </ul>
                </div>
                </div>
            </div>
        </div>
    </div>
</body>
<!-- 文字超出省略号 -->
<script type="text/javascript" >

    $(document).ready(function () {

        $(".hj_item h2").each(function () {
            var str = $(this).html();
            var subStr = str.substring(0, 7);
            $(this).html(subStr + (str.length > 7 ? '...' : ''));
        });
    })
    var esbUltimate = '<%=JSONArray.fromObject(esbUltimate)%>';
    var icopUltimate = '<%=JSONArray.fromObject(icopUltimate)%>';
    var wireDataList = '<%=JSONArray.fromObject(wireDataList)%>';

</script>

<!-- 滚动条 -->
<script type="text/javascript">
    $(".scrollauto").niceScroll({
        autohidemode: true,
        cursorcolor: "#0b92fb",
        cursorborder: "1px solid #0b92fb"
    });
</script>

</html>