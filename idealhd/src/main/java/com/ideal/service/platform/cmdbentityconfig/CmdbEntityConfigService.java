package com.ideal.service.platform.cmdbentityconfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.platform.cmdbentity.bean.CmdbEntityFilter;
import com.ideal.ieai.server.repository.hd.platform.cmdbentity.bean.SystemRelationConfigBean;
import com.ideal.ieai.server.repository.hd.platform.cmdbentity.manager.CmdbEntityConfigManager;
import com.ideal.ieai.server.util.CalculationUtil;
import com.ideal.util.CastUtil;

/**   
 * @ClassName:  CmdbEntityConfigService   
 * @Description: V4.7.25 CMDB业务系统绑定配置业务层操作类  
 * @author: yue_sun 
 * @date:   2020-02-24 16:22:00   
 *     
 * @Copyright: 2020-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class CmdbEntityConfigService
{
    static Logger                          log     = Logger.getLogger(CmdbEntityConfigService.class);

    private static CmdbEntityConfigService intance = new CmdbEntityConfigService();

    private CmdbEntityConfigService()
    {

    }

    public static CmdbEntityConfigService getInstance ()
    {
        if (intance == null)
        {
            intance = new CmdbEntityConfigService();
        }
        return intance;
    }

    /**   
     * @Title: getEntityEquipmentList   
     * @Description: V4.7.25 CMDB业务系统列表信息
     * @param filter
     * @param type
     * @return      
     * @author: yue_sun
     * @date:   2020-02-24 16:52:39  
     */
    public Map<String, Object> getEntitySystemList ( CmdbEntityFilter filter, int type )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> resMap = new HashMap<String, Object>();
        try
        {
            resMap = CmdbEntityConfigManager.getInstance().getEntitySystemList(filter, type);
        } catch (RepositoryException e)
        {
            log.error(method, e);
        }
        return resMap;
    }
    
    /**   
     * @Title: bindingEntitySysRelation   
     * @Description: V4.7.25 【多写】绑定业务系统绑定关系数据信息     
     * @param modelList
     * @param userInfo
     * @param type
     * @return      
     * @author: yue_sun
     * @date:   2020-02-25 14:46:40  
     */
    public Map<String, Object> bindingEntitySysRelation ( List<SystemRelationConfigBean> sysRelationList,
            UserInfo userInfo )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> resEntitySysRelationMap = new HashMap<String, Object>();
        Map<String, Object> resSysRelationMap = new HashMap<String, Object>();
        try
        {
            resEntitySysRelationMap = CmdbEntityConfigManager.getInstance().bindingEntitySysRelation(sysRelationList,
                userInfo);
            resSysRelationMap = CmdbEntityConfigManager.getInstance().addSysRelationData(sysRelationList);
            if (!CastUtil.castBoolean(resEntitySysRelationMap.get(Constants.STR_SUCCESS)))
            {
                return resEntitySysRelationMap;
            }
            if (!CastUtil.castBoolean(resSysRelationMap.get(Constants.STR_SUCCESS)))
            {
                return resSysRelationMap;
            }
        } catch (RepositoryException e)
        {
            log.error(method, e);
        }
        return resEntitySysRelationMap;
    }

    /**   
     * @Title: deleteEntitySysRelation   
     * @Description: V4.7.25 【多写】解除绑定业务系统绑定关系数据信息 
     * @param sysRelationList
     * @param userInfo
     * @return      
     * @author: yue_sun
     * @date:   2020-02-28 11:38:56  
     */
    public Map<String, Object> deleteEntitySysRelation ( List<SystemRelationConfigBean> sysRelationList,
            UserInfo userInfo )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> resEntitySysRelationMap = new HashMap<String, Object>();
        Map<String, Object> resSysRelationMap = new HashMap<String, Object>();
        try
        {
            resEntitySysRelationMap = CmdbEntityConfigManager.getInstance().deleteEntitySysRelation(sysRelationList,
                userInfo);
            resSysRelationMap = CmdbEntityConfigManager.getInstance().deleteSysRelationData(sysRelationList);
            if (!CastUtil.castBoolean(resEntitySysRelationMap.get(Constants.STR_SUCCESS)))
            {
                return resEntitySysRelationMap;
            }
            if (!CastUtil.castBoolean(resSysRelationMap.get(Constants.STR_SUCCESS)))
            {
                return resSysRelationMap;
            }
        } catch (RepositoryException e)
        {
            log.error(method, e);
        }
        return resEntitySysRelationMap;
    }

    /**   
     * @Title: getSysRelationList   
     * @Description: V4.7.25 CMDB业务系统与本地业务系统关系生成 
     * @param filter
     * @return      
     * @author: yue_sun
     * @date:   2020-02-25 15:24:55  
     */
    @SuppressWarnings("unchecked")
    public List<SystemRelationConfigBean> getSysRelationList ( CmdbEntityFilter filter )
    {
        List<SystemRelationConfigBean> sysRelationList = new ArrayList<SystemRelationConfigBean>();
        SystemRelationConfigBean configBean = null;

        if (StringUtils.isNotBlank(filter.getCmdbSystemIds()) && StringUtils.isNotBlank(filter.getSystemIds()))
        {
            String[] cmdbSysIdArray = StringUtils.split(filter.getCmdbSystemIds(), ",");
            List<String> cmdbSysIdList = Arrays.asList(cmdbSysIdArray);
            String[] sysIdArray = StringUtils.split(filter.getSystemIds(), ",");
            List<String> sysIdList = Arrays.asList(sysIdArray);
            cmdbSysIdList = CalculationUtil.repeatListWayFourth(cmdbSysIdList);
            sysIdList = CalculationUtil.repeatListWayFourth(sysIdList);

            for (String sysIdStr : sysIdList)
            {
                long sysId = CastUtil.castLong(sysIdStr, 0);
                for (String cmdbSysIdStr : cmdbSysIdList)
                {
                    configBean = new SystemRelationConfigBean();
                    long cmdbSysId = CastUtil.castLong(cmdbSysIdStr, 0);
                    configBean.setCmdbeiid(cmdbSysId);
                    configBean.setIprjupperid(sysId);
                    sysRelationList.add(configBean);
                }
            }
        }
        return sysRelationList;
    }

    /**
    * notes:利用List集合contains方法循环遍历去重
    * @param list
    * @return
    */
    public static List repeatListWayFourth ( List<String> list )
    {
        // 新建新List集合，用于存放去重后的元素
        List<String> newList = new ArrayList<String>();
        // 循环遍历旧集合元素
        for (int i = 0; i < list.size(); i++)
        {
            // 判断新集合是否包含有，如果不包含有，则存入新集合中
            boolean isContains = newList.contains(list.get(i));
            if (!isContains)
            {
                newList.add(list.get(i));
            }
        }
        // 把List集合所有元素清空
        list.clear();
        // 把新集合元素添加至List集合
        list.addAll(newList);
        return list;
    }

    public Map<String, Object> getCmdbPrjIds ( CmdbEntityFilter filter, int type )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> resMap = new HashMap<String, Object>();
        try
        {
            String prjIds = CmdbEntityConfigManager.getInstance().getCmdbPrjIds(filter, type);
            resMap.put(Constants.STR_MESSAGE, prjIds);
        } catch (RepositoryException e)
        {
            log.error(method, e);
        }
        return resMap;
    }
}
