package com.ideal.ieai.proxy.engine;

import java.util.Date;
import java.util.Hashtable;

public class ProxyInfo
{
    public String  host;
    public int     port;
    public boolean isSSL               = false;
    public int     numTotalRequests    = 0;
    public int     numPendingReuquests = 0;
    public int     numFinishedRequests = 0;
    public int     numFailedRequests   = 0;
    public int     numTimedoutRequests = 0;
    public Date    starttime;
    public String  wsIp;
    public int     wsPort;

    public Hashtable toHashtable ()
    {
        Hashtable ret = new Hashtable();
        ret.put("host", this.host);
        ret.put("port", new Integer(this.port));
        ret.put("wsIp", this.wsIp);
        ret.put("wsPort", new Integer(this.wsPort));
        ret.put("starttime", this.starttime);
        return ret;
    }
}
