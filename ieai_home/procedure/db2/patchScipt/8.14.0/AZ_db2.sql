BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	 
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=40;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (40, ''AZ切换'', ''AZ切换模块组'', 40, ''images/info82.png'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-40;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVE<PERSON>, <PERSON>REEZED, IUPLOADUSER, ICOMMENT, <PERSON>UPL<PERSON>DNU<PERSON>, IUUID, <PERSON>RE<PERSON>Z<PERSON>USER, IF<PERSON><PERSON><PERSON><PERSON>USER<PERSON>, IUPLOADUSER<PERSON>, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-40, 0, ''所有AZ切换业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 40, -40, -40, -40)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=40;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (40,40,''AZ切换源'','''','''','''','''',0)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION' AND COLNAME = 'IAZTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN IAZTYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE' AND COLNAME = 'IAZTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE ADD COLUMN IAZTYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUN_INSTANCE_HIS' AND COLNAME = 'IAZTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD COLUMN IAZTYPE INTEGER';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AZ_IP_RELATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AZ_IP_RELATION(
					IID DECIMAL(19,0) NOT NULL,
					IAZNAME VARCHAR(255),
					IIPRANGE VARCHAR(255),
					CONSTRAINT PK_IEAI_AZ_IP_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AZ_IP_INFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AZ_IP_INFO(
					IID DECIMAL(19,0) NOT NULL,
					IAZIID DECIMAL(19),
					IAZIP VARCHAR(255),
					CONSTRAINT PK_IEAI_AZ_IP_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AZ_F5_VERSION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AZ_F5_VERSION(
				IID DECIMAL(19) NOT NULL,
				ISYSTEMNAME VARCHAR(255),
				ILTMVSNAME VARCHAR(255),
				ILTMVS VARCHAR(255),
				ILTMPOOLNAME VARCHAR(255),
				ILTMPOOLMEMBER VARCHAR(255),
				IWAFVSNAME VARCHAR(255),
				IWAFPOOLNAME VARCHAR(255),
				IWAFPOOLMEMBER VARCHAR(255),
				CONSTRAINT PK_IEAI_AZ_F5_VERSION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AZ_F5_INFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AZ_F5_INFO(
					IID DECIMAL(19) NOT NULL,
					IVERSIONID DECIMAL(19),
					IPOOLMEMBER VARCHAR(255),
					IMEMBERTYPE VARCHAR(10),
					CONSTRAINT PK_IEAI_AZ_F5_INFO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'IEAI_AZSWITCHCONFIG';
		IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'CREATE TABLE IEAI_AZSWITCHCONFIG(IID  DECIMAL(19) NOT NULL,IPROPERTYNAME  VARCHAR(255),IPROPERTYVALUE VARCHAR(255),IPROPERTYDESC VARCHAR(255),CONSTRAINT PK_IEAI_AZSWITCHCONFIG PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE LI_EXISTS NUMERIC(2);
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40001;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40001, ''AZ切换录入'', 40, ''azinfoexecl.do'',2, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40003;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40003, ''AZ切换资源组管理'', 40, ''resourceGroupForAZ/framOfBerForAZ.do'',2, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40005;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40005, ''AZ切换启动'', 40, ''flowstartworkflowForAZ.do'',4, '''' ,''images/info68.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40007;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40007, ''云IP地址规划导入'', 40, ''azcloudipimport.do'',9, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40010;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40010, ''云IP地址规划'', 40, ''azcloudipquery.do'',10, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40008;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40008, ''AZ切换监控'', 40, ''initGraphForAZ.do'',10, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40009;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40009, ''AZ系统维护'', 40, ''initInstanceConfigForAZ.do'',10, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40011;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40011, ''F5网络配置导入'', 40, ''azf5netconfigimport.do'',10, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40002;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40002, ''AZ切换参数设置'', 40, ''azinfoconfig.do'',3, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40004;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40004, ''AZ切换系统列表'', 40, ''azinfoSysList.do'',4, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40006;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40006, ''AZ切换历史'', 40, ''azinfoHistoryList.do'',5, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=40 AND IID=40012;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(40012, ''F5网络配置展示'', 40, ''azf5netconfigshow.do'',10, '''' ,''images/info67.png'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
END
$

	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_AZSWITCHCONFIG P WHERE P.IPROPERTYNAME='timingCronValue';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_AZSWITCHCONFIG (IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) VALUES (1,''timingCronValue'',''0 0 9,15 * * ?'',''AZ切换定时任务CRON表达式'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	END
	$

 BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'IAZNAME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN IAZNAME VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'IAZNAME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD COLUMN IAZNAME VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_BUSINESS' AND COLNAME = 'IAZNAME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_BUSINESS ADD COLUMN IAZNAME VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_BUSINESS' AND COLNAME = 'IAZNETID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_BUSINESS ADD COLUMN IAZNETID VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLNAME = 'IAZNAME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD COLUMN IAZNAME VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'IAZNAME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN IAZNAME VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'IAZNAME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IAZNAME VARCHAR(1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AZ_AREA_SYSNAME_RELATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AZ_AREA_SYSNAME_RELATION	(IID DECIMAL(19) NOT NULL,IINSTANCEID DECIMAL(19),ISYSNAME VARCHAR(255),IAZAREA VARCHAR(255),ISWITCHTIME DECIMAL(19),CONSTRAINT PK_IEAI_AZSYS_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		

	END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_BUSINESS' AND COLNAME = 'IAZNETID';
			IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_BUSINESS ALTER COLUMN IAZNETID SET DATA TYPE VARCHAR(20)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;
END
$

BEGIN
DECLARE LS_SQL VARCHAR(2000);
DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT COUNT(IBUTTONID) INTO LI_EXISTS  FROM IEAI_HIGHOPER  P WHERE P.IBUTTONID=40017;  
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(40017, ''确认执行'', ''processMonitorPersonExceptionExecAZ.do'', ''人工处理'')';
		PREPARE SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END	IF;
	
	SELECT COUNT(IMENUBUTTONID) INTO LI_EXISTS  FROM IEAI_MENU_BUTTON  P WHERE P.IMENUBUTTONID=40017;  
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(40017, 40008, 40017, '''')';
		PREPARE SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END	IF;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AZSWITCHCONFIG' AND COLNAME = 'EXTENDCOLUMN';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_AZSWITCHCONFIG ADD EXTENDCOLUMN VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO' AND COLNAME = 'IPROXYIP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO ADD COLUMN IPROXYIP VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCEINFO_HIS' AND COLNAME = 'IPROXYIP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCEINFO_HIS ADD COLUMN IPROXYIP VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_BUSINESS' AND COLNAME = 'IPROXYIP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_BUSINESS ADD COLUMN IPROXYIP VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLNAME = 'IPROXYIP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD COLUMN IPROXYIP VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE' AND COLNAME = 'IPROXYIP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN IPROXYIP VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'IPROXYIP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN IPROXYIP VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
END
$
