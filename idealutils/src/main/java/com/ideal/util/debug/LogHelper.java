package com.ideal.util.debug;

import org.apache.log4j.Logger;

public class LogHelper
{

    /**
     * @param args
     */
    public static void main ( String[] args )
    {
        // TODO Auto-generated method stub

    }

    public static String getMyName ( Class c )
    {
        String name = "["
                + c.getClass().getName().substring(c.getClass().getName().lastIndexOf(".") + 1)
                + "]";

        return name;
    }

    public static String getPhaseTime ( long start )
    {
        start = System.currentTimeMillis() - start;
        return String.valueOf(start);
    }

    /**
     * 时间戳测试用
     * 
     * @param start
     * @param str
     * @return
     */
    public static long getIntervalTime ( long start, String str )
    {
        long now = System.currentTimeMillis();
        System.out.println(str + " :" + (now - start));
        return now;
    }

    public static long getIntervalTime ( Logger _log, long start, String str )
    {
        long now = System.currentTimeMillis();
        _log.info(str + " :" + (now - start));
        return now;
    }
}
