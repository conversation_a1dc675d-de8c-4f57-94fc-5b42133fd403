/*******************************************************************************
 * ITIL信息展示,Tab2
 ******************************************************************************/
var queryFormSingleSys=null;
var tgStore;
var bigInfoStore;
var singleSysMainPanel =null;

Ext.onReady (function ()
{
	var globalSelectRecord;
	// 主panel高度减掉的定值
	var staticHeight = 100;
	// 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.define ('bigPackageModel',
	{
		extend : 'Ext.data.Model',
	    fields : [
	    	{
	    		name : 'IID',
	            type : 'long'
	    	},{
	    		name : 'ICREATEDTIME',
	            type : 'string'
	    	},{
	    		name : 'IDEPLOYTASKID',
	            type : 'string'
	    	},{
	        	name : 'ISATE',
	            type : 'string'
	        },{
	        	name : 'INAME',
	            type : 'string'
	        },{
	        	name : 'IPATCH_PATH',
	            type : 'string'
	        },{
	        	name : 'ISYSNAME',
	            type : 'string'
	        }
	        ]
	});
	bigInfoStore = Ext.create ('Ext.data.Store',
	{
		autoLoad : true,
	    autoDestroy : true,
	    model : 'bigPackageModel',
	    //groupField:'IDEPLOYTASKID',
	    pageSize : 50,
	    proxy :
	    {
	    	type : 'ajax',
	        url : 'getApolloCloudinfo.do',
	        reader :
	        {
	        	type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	tgStore=bigInfoStore;
	bigInfoStore.on ('beforeload', function (store, options)
	{
		var _isysname = queryFormSingleSys.getForm().findField("ISYSNAME").getValue();
		if(illegalCharN(trim(_isysname))){
			Ext.Msg.alert('提示', '变更单号不能含有特殊字符 请重新输入!');
			return;
		}
		var new_params =
       	{
				isysname:_isysname,
				dependon:"0",
       	};
		Ext.apply (bigInfoStore.proxy.extraParams, new_params);
	 });
	
	bigInfoStore.on ('load', function (store, options)
	{
		//bigPackageInfoGrid.view.features[0].collapseAll();
	});
	    	        
	/** 树列表columns* */
	var BigPackageInfoColumns = [
		{
			text : '序号',
			width : 35,
	        hidden:true,
	        xtype : 'rownumberer'
		},{
			text : '外部业务系统名',
	        dataIndex : 'ISYSNAME',
	        width : 235,
	        editor:{  
	        	allowBlank:true  
	        },
	     	renderer : function (value, metaData, record)
	     	{
	     		metaData.tdAttr = 'data-qtip="' + value + '"';
   	            	return value;
	     	}
	    },{
			text : '单号',
	        dataIndex : 'IDEPLOYTASKID',
	        width : 160,
	        editor:{  
	        	allowBlank:true  
	        },
	     	renderer : function (value, metaData, record)
	     	{
	     		metaData.tdAttr = 'data-qtip="' + value + '"';
   	            	return value;
	     	}
	    },{
			text : '接收云效报文时间',
	        dataIndex : 'ICREATEDTIME',
	        width : 300,
	        editor:{  
	        	allowBlank:true  
	        },
	        renderer : function (value, metaData, record)
   	        {
	        	metaData.tdAttr = 'data-qtip="' + value + '"';
   	            	return value;
   	        }
		},{
			text : '包名',
			width : 200,
	        dataIndex : 'INAME',
	        flex : 2,
	        editor:{  
	        	allowBlank:true  
	        },
	     	renderer : function (value, metaData, record)
	     	{
	     		metaData.tdAttr = 'data-qtip="' + value + '"';
   	            	return value;
	     	}
	    },{
			text : '包路径',
			width : 400,
	        dataIndex : 'IPATCH_PATH',
	        editor:{  
	        	allowBlank:true  
	        },
	     	renderer : function (value, metaData, record)
	     	{
	     		metaData.tdAttr = 'data-qtip="' + value + '"';
   	            	return value;
	     	}
	    },{
	    	text : '下载',
	        dataIndex : 'downFile',
	        sortable : false,
	        hideable : false,// 是否可以手动隐藏
	        width : 120,
	        renderer : function (value, metaData, record){
	        	var v1 = record.get ('IPATCH_PATH');
	        	var url="";
	        	if(typeof v1 == "undefined" || v1 == null || v1 == ""){
	        		
	        	}else{
	        		url='<a href="javascript:void(0);" title="下载" onclick="downLoadFile(\''+record.get ('IPATCH_PATH') +'\')">下载</a>';
	        	}
		        return  url;
	        }
	    },
	    {
        	text : '状态',
            dataIndex : 'ISATE',
            width : 120,
            renderer : function (value, metaData, record)
            {
            	var showValue='';
            	if(value=='0')
                {
                	showValue='等待';
                }else if(value=='1')
                {
                	showValue="部署中";
                }else if(value=='2')
                {
                	showValue="部署成功";
                }else if(value=='3'){
                	showValue="部署失败";
                }
                return showValue;
            }
        }];
	bsPageBar = Ext.create ('Ext.PagingToolbar',
	{
		store : bigInfoStore,
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
    });
	
	queryFormSingleSys = Ext.create('Ext.form.Panel', {
	    border: false,
	    width : '100%',
	    height: 100,
	    bodyPadding: 5,
    	region: 'north',
		collapsible : true,//可收缩
		collapsed : false,//默认收缩
	    layout: 'form',
	    items: [{
	      layout:'column',
	      border : false,
	      items: [
          {
	        labelWidth : 120,
	        width: 340,
	        xtype: 'textfield',
	        fieldLabel: '外部业务系统名',
	        name: 'ISYSNAME',
	        padding : '0 10 14 10' 
	      },{
	        xtype: 'button',
	        text: '查询',
	        margin:'0 10 14 0',
	        handler: function() {
	          queryWhere();
	        }
	      },{
	        xtype: 'button',
	        text: '重置',
	        margin:'0 10 14 0',
	        handler: function() {
	        	queryFormSingleSys.getForm().findField("ISYSNAME").setValue('');
	        }
	      }]
	    }]
	  });
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	var  bigPackageInfoGrid = Ext.create ('Ext.ux.ideal.grid.Panel',
	{
		region : 'center',
	    store : bigInfoStore,
	    border : true,
	    columnLines : true,
	    extend : 'Ext.grid.Panel',
	    alias : 'widget.ideapanel',
	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    columns : BigPackageInfoColumns,
//	    selModel : Ext.create ('Ext.selection.CheckboxModel',
//        {
//	    	checkOnly : true
//        }),
	    dockedItems : [
		{
			xtype : 'toolbar',
		    hidden:true,
		    height : 40,
		    items : [
		    	{
		    		text : '新增',
		    		cls : 'Common_Btn',
		    		hidden:false,
		    		handler : function ()
		    		{
		    			bigPackageInfoDetail(0);
		    		}
		    	},{
		    		text : '删除',
		    		cls : 'Common_Btn',
		    		hidden:false,
		    		handler : function ()
		    		{
		    			deleteGrid(0);
		    		}
		    	},{
		    		text: '终止',
		    		cls : 'Common_Btn',
		    		hidden:true,
		    		handler: disableFun
		    	}
		    	]
		}
		]
	});
	// 主Panel
	singleSysMainPanel = Ext.create ('Ext.panel.Panel',
	{
		renderTo : "bigPackageShow_SingleSysDIV",
	    width : '100%',
	    height : contentPanel.getHeight () - staticHeight,
	    border : false,
	    layout : 'border',
	    bodyPadding : grid_space,
	    items : [queryFormSingleSys,bigPackageInfoGrid]
	});
	
	
	contentPanel.on ('resize', function ()
	{
		//autoResize(bigPackageInfoGrid,queryFormPanel,singleSysMainPanel,contentPanel);
	});
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (singleSysMainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		singleSysMainPanel.setWidth(contentPanel.getWidth ());
	    singleSysMainPanel.setHeight (contentPanel.getHeight()-staticHeight);
	});
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
	        
	
	function queryWhere(){
		bsPageBar.moveFirst();
	}
	
 
	/**终止**/
	function disableFun ()
	{
		var data = bigPackageInfoGrid.getView().getSelectionModel().getSelection();
	    if (data.length == 0) {
	    	Ext.Msg.alert('提示', '请先选择您要操作的行!');
	    		return;
	    } else {
	    	var ids = [];
	    	Ext.Array.each(data,function(record) {
	    		var groupId = record.get('iid');
	    		// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
	    		if (groupId) {
	    			ids.push(groupId);
	    		}
	    	});
	    	Ext.Ajax.request ({
	    		url : 'disableTimingStartVersion.do',
	    		params :{
	    			ids : ids.join(',')
	    		},
	    		method : 'POST',
	    		success : function (response, opts)
	    		{
	    			var success = Ext.decode (response.responseText).success;
	    			Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
	    			bigInfoStore.reload ();
	    		},
	    		failure : function (result, request)
	    		{
	    			secureFilterRs (result, "操作失败！");
	    		}
	    	});
	    }
	}

	function deleteGrid ()
	{
		var data = bigPackageInfoGrid.getView().getSelectionModel().getSelection();
	    if (data.length == 0) {
	    	Ext.Msg.alert('提示', '请先选择您要操作的行!');
	    	return;
	    } else {
	    	var ids = [];
	    	Ext.Array.each(data,function(record) {
	    		var groupId = record.get('iid');
	    		// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
	    		if (groupId) {
	    			ids.push(groupId);
	    		}
	    	});
	    	var delFlag=false;
	    	for(var a=0;a<data.length;a++){
	    		var recd=data[a];
	    		var iState=recd.data.istate;
	    		if(iState!=0){
	    			delFlag=true;
	    			break;
	    		}
	    	}
	    	if(delFlag){
	    		Ext.Msg.alert('提示', '只能对未运行的记录进行删除!');
	    		return;
	    	}
	    	Ext.Ajax.request ({
	    		url : 'deleteTimingStartVersion.do',
	    		params :{
	    			ids : ids.join(',')
	    		},
	    		method : 'POST',
	    		success : function (response, opts)
	    		{
	    			var success = Ext.decode (response.responseText).success;
	    			Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
	    			tgStore.reload ();
	    		},
	    		failure : function (result, request)
	    		{
	    			secureFilterRs (result, "操作失败！");
	    		}
	    	});
	    }
	}
	

	function onBuildPkgNew(){
	    var m = bigPackageInfoGrid.getView().getSelectionModel().getSelection();
	    if (m.length < 1) {
	    	Ext.Msg.alert('提示', '请选中要构建的记录!');
	      return;
	    }
	    var jsonData = "[";
	    for ( var i = 0, len = m.length; i < len; i++) {
	      var ss = Ext.JSON.encode(m[i].data);
	      if (i == 0)
	        jsonData = jsonData + ss;
	      else
	        jsonData = jsonData + "," + ss;
	    }
	    jsonData = jsonData + "]";
	    Ext.Ajax.request({
	      url : 'onBuildPkgNew.do',
	      method : 'POST',
	      params : {
	        jsonData : jsonData
	      },
	      success : function(response, request) {
	        var success = Ext.decode(response.responseText).success;
	        if (success) {
	        	tgStore.reload();
	        	Ext.Msg.alert('提示', '构建成功!');
	        } else {
	        	Ext.Msg.alert('提示', '构建失败!');
	        }
	      },
	      failure : function(result, request) {
	        Ext.Msg.alert('提示', '保存失败!');
	      }
	    });
	  }
});

function bigPackageInfoDetail (iidIn)
{
	destroyRubbish ();
	contentPanel.getHeader().show();//让contentPanel显示标题头
	contentPanel.getLoader ().load (
	{
	    url : 'bigPackageInfoDetail.do',
	    params :
	    {
	    	iidIn : iidIn
	    },
	    scripts : true
	});
	if (Ext.isIE)
	{
		CollectGarbage ();
	}
}

function bigPackageInfoDetailNew (iidIn)
{
	destroyRubbish ();
	contentPanel.getHeader().show();//让contentPanel显示标题头
	contentPanel.setTitle('自定义变更监控2');
	//点击部署流程和 启动 跳转到自定义变更监控2， 同时带着单号传递过去
	contentPanel.getLoader ().load (
	{
	    url : 'initTimingStartNew.do',
	    params :
	    {
	    	IDeployTaskID : iidIn
	    },
	    scripts : true
	});
	if (Ext.isIE)
	{
		CollectGarbage ();
	}
}
function startTask(_ieai_sus_bigpkg_version_iid,IDEPLOYTASKID){
	Ext.Ajax.request ({
		url : 'bigPkgStartTask.do',
		params :{
			ieai_sus_bigpkg_version_iid : _ieai_sus_bigpkg_version_iid
		},
		method : 'POST',
		success : function (response, opts)
		{
			var success = Ext.decode (response.responseText).success;
			Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			if(success){
				bigPackageInfoDetailNew(IDEPLOYTASKID);				
			}
		},
		failure : function (result, request)
		{
			secureFilterRs (result, "操作失败！");
		}
	});
}

//下载信息
function downLoadFile(obj){
	if(typeof obj == "undefined" || obj == null || obj == ""){
		Ext.Msg.alert ('提示', "下载路径为空");
	}else{
		window.location.href = 'downloadMess.do?watchPath='+obj;
	}
}
