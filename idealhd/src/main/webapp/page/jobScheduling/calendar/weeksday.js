var mainGrid;
var me;
Ext.define('weeksDayModel', {
	extend: 'Ext.data.Model',
	fields: [{
		name: 'weekdayId',
		type: 'string'
	},
	{
		name: 'calId',
		type: 'long'
	},
	{
		name: 'weekOfDay',
		type: 'String'
	},
	{
		name: 'workday',
		type: 'boolean'
	}]
});
var weeksDayStore = Ext.create('Ext.data.Store', {
	autoLoad: true,
	autoDestroy: true,
	model: 'weeksDayModel',
	pageSize: 30,
	proxy: {
		type: 'ajax',
		url: 'getWeeksDay.do',
		reader: {
			type: 'json',
			root: 'dataList'
		}
	}
});
// Basic mask:
var myMask = new Ext.LoadMask(calanderWindow, {
	msg: "读取中..."
});
weeksDayStore.on('beforeload',
function(store, options) {
	myMask.show();
	var new_params = {
		calid: gcalid
	};
	Ext.apply(weeksDayStore.proxy.extraParams, new_params);
});
weeksDayStore.on('load',
function(store, options) {
	myMask.hide();
});
function displayInnerGrid(renderId) {

	Ext.define('weeksDayTimeModel', {
		extend: 'Ext.data.Model',
		// remoteSort : true,
		fields: [{
			name: 'workingtimeId',
			type: 'int'
		},
		{
			name: 'type',
			type: 'int'
		},
		{
			name: 'parentId',
			type: 'int'
		},
		{
			name: 'startHour',
			type: 'int'
		},
		{
			name: 'startMinute',
			type: 'int'
		},
		{
			name: 'endHour',
			type: 'int'
		},
		{
			name: 'endMinute',
			type: 'int'
		},
		{
			name: 'description',
			type: 'string'
		}]
	});
	var weeksDayColumns = [{
		text: '序号',
		width: 35,
		xtype: 'rownumberer'
	},
	{
		text: 'workingtimeId',
		dataIndex: 'workingtimeId',
		hidden: true
	},
	{
		text: 'pid',
		dataIndex: 'parentId',
		hidden: true
	},
	{
		text: 'type',
		dataIndex: 'type',
		hidden: true
	},
	{
		text: '开始时',
		editor: {
			allowBlank: false,
			maxLength: 2,
			regex: /^[0-9]*$/,
			regexText: '只能输入数字'

		},
		sortable: true,
		dataIndex: 'startHour',
		flex: 1
	},
	{
		text: '开始分',
		editor: {
			allowBlank: false
		},
		dataIndex: 'startMinute',
		flex: 1

	},
	{
		text: '结束时',
		editor: {
			allowBlank: false
		},
		sortable: true,
		dataIndex: 'endHour',
		flex: 1
	},
	{
		text: '结束分',
		editor: {
			allowBlank: false
		},
		dataIndex: 'endMinute',
		flex: 1
	},
	{
		text: '描述',
		editor: {
			allowBlank: false
		},
		sortable: true,
		dataIndex: 'description',
		flex: 1
	}];
	var weeksDayTimeStore = Ext.create('Ext.data.Store', {
		autoLoad: true,
		autoDestroy: true,
		model: 'weeksDayTimeModel',
		pageSize: 30,
		proxy: {
			type: 'ajax',
			url: 'getWeeksDayTime.do',
			reader: {
				type: 'json',
				root: 'dataList'
			}
		}
	});
	// Basic mask:
	var myMask = new Ext.LoadMask(calanderWindow, {
		msg: "读取中..."
	});
	weeksDayTimeStore.on('beforeload',
	function(store, options) {
		myMask.show();
		var new_params = {
			calid: renderId
		};
		Ext.apply(weeksDayTimeStore.proxy.extraParams, new_params);
	});
	weeksDayTimeStore.on('load',
	function(store, options) {
		myMask.hide();
	});

	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	innerGrid = Ext.create('Ext.grid.Panel', {
		selModel: Ext.create('Ext.selection.CheckboxModel', {
			checkOnly: true
		}),
		plugins: [cellEditing],
		store: weeksDayTimeStore,
		header: false,
		//        selModel: {
		//            selType: 'cellmodel'
		//        },
		columns: weeksDayColumns,
		autoWidth: true,
		autoHeight: true,
		//width: 400,
		height: 150,
		frame: false,
		iconCls: 'icon-grid',
		renderTo: renderId,
		dockedItems: [{
			xtype: 'toolbar',
			items: ['->', {
				text: '增加',
//				width: 70,
//				height: 22,
//				textAlign: 'center',
				cls: 'Common_Btn',
//				icon: '',
				handler: function() {
					var p = Ext.create('weeksDayTimeModel', {
						workingtimeId: -1,
						type: 1,
						parentId: renderId,
						startHour: '0',
						startMinute: '0',
						endHour: '0',
						endMinute: '0',
						description: ''
					});
					weeksDayTimeStore.insert(0, p);
				}
			},
			{
				text: '保存',
//				width: 70,
//				height: 22,
//				textAlign: 'center',
				cls: 'Common_Btn',
				icon: '',
				handler: function() {

					var m = weeksDayTimeStore.getModifiedRecords();
					if (m.length < 1) {
						return;
					}
					var jsonData = "[";
					for (var i = 0,len = m.length; i < len; i++) {
						
						var startHour = m[i].data.startHour;
				        var startMinute = m[i].data.startMinute;
				        var endHour = m[i].data.endHour;
				        var endMinute = m[i].data.endMinute;
				        if((startHour+startMinute+endHour+endMinute)==0){
				        	Ext.Msg.alert('提示', "请配置工作时间,时间配置不能全为0！");
				        	return;
				        }
				   /*
				        if((startHour+startMinute)==(endHour+endMinute)){
				        	Ext.Msg.alert('提示', "开始时间和结束时间不能相同！");
				        	return;
				        }*/

						if(startHour==endHour&&startMinute==endMinute){
							Ext.Msg.alert('提示', "开始时间和结束时间不能相同！");
							return;
						}
				        if(startHour>23){
				        	Ext.Msg.alert('提示', "开始时不能超过23！");
				        	return;
				        }
				        if(startMinute>59){
				        	Ext.Msg.alert('提示', "开始分不能超过59！");
				        	return;
				        }
				        if(endHour>23){
				        	Ext.Msg.alert('提示', "结束时不能超过23！");
				        	return;
				        }
				        if(endMinute>59){
				        	Ext.Msg.alert('提示', "结束分不能超过59！");
				        	return;
				        }

				        if(startHour>endHour){
				        	Ext.Msg.alert('提示', "开始时不能大于结束时！");
				        	return;
				        }
				        if(startHour==endHour){
				        	if(startMinute>endMinute)
				        	{
				        		Ext.Msg.alert('提示', "开始时间不能大于结束时间！");
				        		return;
				        	}
				        }



						var ss = Ext.JSON.encode(m[i].data);
						if (i == 0) jsonData = jsonData + ss;
						else jsonData = jsonData + "," + ss;
					}
					jsonData = jsonData + "]";
					Ext.Ajax.request({
						url: 'saveWorkDayTime.do',
						method: 'POST',
						params: {
							jsonData: jsonData
						},
						success: function(response, request) {
							var success = Ext.decode(response.responseText).success;
							var message = Ext.decode(response.responseText).message;
							if (success) {
								weeksDayTimeStore.modified = [];
								weeksDayTimeStore.reload();
								Ext.Msg.alert('提示', message);
							} else {
								Ext.Msg.alert('提示', message);
							}
						},
						failure: function(result, request) {
							secureFilterRs(result, "操作失败！");
						}
					});

				}
			},
			'-', {
				itemId: 'delete',
				text: '删除',
//				width: 70,
//				height: 22,
//				textAlign: 'center',
				cls: 'Common_Btn',
//				icon: '',
				disabled: true,
				handler: function() {

					var data = innerGrid.getView().getSelectionModel().getSelection();
					if (data.length == 0) {
						Ext.Msg.alert('提示', '请先选择您要操作的行!');
						return;
					} else {
						Ext.Msg.confirm("请确认", "请确认是否删除数据",
						function(button, text) {
							if (button == "yes") {
								var ids = [];
								var goBack = false; // 判断是否需要删除数据库数据
								for (var i = 0; i < data.length; i++) {
									if (0 != data[i].get('workingtimeId')) // 如果预删除数据id不为-1，则需要删除数据库数据
									{
										goBack = true;
										break;
									}
								}
								if (goBack) // 通过数据库删除数据
								{
									Ext.Array.each(data,
									function(record) {
										var checkItemId = record.get('workingtimeId');
										// 如果删除的是幻影数据，则id就不传递到后台了，直接在前台删除即可
										if (checkItemId) {
											ids.push(checkItemId);
										}
									});
									Ext.Ajax.request({
										url: 'deleteWorktingtimeInfo.do',
										params: {
											deleteIds: ids.join(',')
										},
										method: 'POST',
										success: function(response, opts) {
											var success = Ext.decode(response.responseText).success;
											// 当后台数据同步成功时
											if (success) {
												weeksDayTimeStore.reload();
												Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
											} else {
												Ext.Msg.alert('提示', '删除失败！');
											}
										},
										failure: function(result, request) {
											secureFilterRs(result, "操作失败！");
										}
									});
								} else {
									for (var i = 0; i < data.length; i++) {
										// 如果不需要删除数据库数据，则不刷新列表，仅移除store数据
										CalendarInfoItemStore.remove(data[i]);

									}
								}

							}
						});
					}

				}
			}]
		}]
	});

	innerGrid.getEl().swallowEvent(['mousedown', 'mouseup', 'click', 'contextmenu', 'mouseover', 'mouseout', 'dblclick', 'mousemove']);

	innerGrid.getSelectionModel().on('selectionchange',
	function(selModel, selections) {
		innerGrid.down('#delete').setDisabled(selections.length === 0);
	});
}

function destroyInnerGrid(record) {

	var parent = document.getElementById(record.get('weekdayId'));
	var child = parent.firstChild;

	while (child) {
		child.parentNode.removeChild(child);
		child = child.nextSibling;
	}

}

Ext.define('MainGrid', {
	extend: 'Ext.grid.Panel',
	alias: 'widget.MainGrid',
	store: weeksDayStore,
	header: false,
	columns: [{
		xtype: 'checkcolumn',
		text: '工作日',
		dataIndex: 'workday',
		width: 80,
		listeners: {
			checkchange: {
				fn: function(comp, rowIndex, checked, eOpts) {
					var record = weeksDayStore.getAt(rowIndex);
					var wid = record.get('weekdayId');
					updateWeekDay(wid, checked);
				}
			}

		}
	},
	{

		text: "星期",
		flex: 1,
		dataIndex: 'weekOfDay',
		renderer: function(value, metaData, record) {
			return cWeekDay(value);
		}

	}],
//	autoWidth: true,
	selModel: {
		selType: 'cellmodel'
	},
//	autoHeight: true,
	plugins: [{
		ptype: 'rowexpander',
		rowBodyTpl: ['<div id="{weekdayId}">', '</div>']
	}],
	animCollapse: false,
	renderTo: 'weeksday_div',
	width : '100%',
	height : '100%',
	border:false/*,
	iconCls: 'icon-grid',
	initComponent: function() {
		me = this;
		this.callParent(arguments);
	}*/
});

Ext.onReady(function() {

	Ext.QuickTips.init();
//	Ext.BLANK_IMAGE_URL = '/images/s.gif';

	mainGrid = new Ext.create('MainGrid');

	mainGrid.view.on('expandBody',
	function(rowNode, record, expandRow, eOpts) {
		if (record.get('weekdayId') == null) return;
		else {
			displayInnerGrid(record.get('weekdayId'));
		}

	});

	mainGrid.view.on('collapsebody',
	function(rowNode, record, expandRow, eOpts) {
		destroyInnerGrid(record);
	});

	//    mainGrid.render(Ext.getBody());
	//    mainGrid.setHeight(window.innerHeight);
	//    mainGrid.setWidth(window.innerWidth);
	//    Ext.EventManager.onWindowResize(function () {
	//        //console.log('-------');
	//        mainGrid.setHeight(window.innerHeight);
	//        mainGrid.setWidth(window.innerWidth);
	//    });
});

function updateWeekDay(wkid, isWorkDay) {
	Ext.Ajax.request({
		url: 'updateWeekDay.do',
		method: 'POST',
		params: {
			weekId: wkid,
			isWorkDay: isWorkDay

		},
		success: function(response, request) {
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (success) {
				weeksDayStore.reload();
				Ext.Msg.alert('提示', message);
			} else {
				Ext.Msg.alert('提示', message);
			}
		},
		failure: function(result, request) {
			secureFilterRs(result, "操作失败！");
		}
	});
}

function cWeekDay(v) {
	if (v == 1) {
		return "星期一";
	}
	if (v == 2) {
		return "星期二";
	}
	if (v == 3) {
		return "星期三";
	}
	if (v == 4) {
		return "星期四";
	}
	if (v == 5) {
		return "星期五";
	}
	if (v == 6) {
		return "星期六";
	}
	if (v == 7) {
		return "星期日";
	}
}