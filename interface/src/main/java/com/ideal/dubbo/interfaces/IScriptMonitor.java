package com.ideal.dubbo.interfaces;

import com.ideal.dubbo.models.*;
import com.ideal.ieai.commons.ClientSession;

import java.util.List;
import java.util.Map;

@SuppressWarnings("rawtypes")
public interface IScriptMonitor
{
    Map scriptServiceUT ( String coatId, int flag, int type );

    String startScriptServiceUT ( String ids, String flag, String coatId );

    Map getScriptCoatInfo ( String coatId );

    String groupUserIIDs(String userId);

    MonitorExecHistoryModel getScriptCoatInfoForWs ( ClientSession session, String coatId );

    Map getScriptCoatExecHistory ( int start, int limit, ScriptCoat filter );

    List getLastTestAgents ( long iid );

    List<MonitorExecHistoryModel> getScriptCoatExecHistoryForWs ( ClientSession session, int start, int limit,
            ScriptCoat filter );

    Long getScriptCoatExecHistoryCountForWs ( ClientSession session, ScriptCoat filter );

    String getScriptExecInstanceLogForWs ( String instanceId );



    MonitorInstanceModel getScriptInstanceInfoForWs ( ClientSession session, String instanceId );

    Map getScriptFlowExecHistory ( int start, int limit, ScriptFlow filter );

    Map getScriptExecList ( int start, int limit, String coatid, String flag );

    List getFLowRunningList ( String userId, Integer flag, Integer flowType, String flowName, String status );

    List getMonitorStepStatisticForScriptService ( long flowId );

    String getScriptExecOutput ( String requestId, String agentIp, String agentPort, String flag );

    FlowOutputModel getScriptOutput ( String flowId );

    FlowOutputModel getScriptOutputWithOutAgent ( String flowId );

    Map getScriptExecOutputForTry ( int isFromTryATry, String requestId, String agentIp, String agentPort );

    String scriptServerStop ( String requestId, String flag );

    String scriptCoatStop ( String coatId, String flag );

    List getStartUser ();

    Map<String, List<ScriptInstance>> getScriptExecAllList ( long coatId, int flag, long[] insIds ,String switchFlag );

    boolean hasPermissionExecHistory ( String userId, String requestId );

    boolean hasPermissionExecInstance ( String userId, String instanceId );

    boolean stopBySelect ( String jsonData );

    Map<String, List<ScriptInstance>> getScriptExecWhiteAllList ( long coatId, int flag );

    boolean updateButterflyVersion ( String butterflyversion, String iid, String flowId, int type,
            String oddNumbersType );

    Map getPlanScriptFlowExecHistory ( int start, int limit, ScriptFlow filter );

    Map getScriptExecList ( Integer start, Integer limit, String coatid, String flag,
            ScriptExecHistoryQueryBean queryBean );

    List<ScriptInstance> getMultiScriptExecAllList ( String flowids, int flag, String ids,String switchFlag);

    Map<String, java.util.LinkedHashMap<String,String>> exportAgentMessageData(String iids, String flowId,String state) throws Exception;

    Map<String,Object> getHisDataByIIds(String iids);

    Map getScriptExecHisListForTaskApplay ( Integer start, Integer limit, String scriptUuid,String flowId );

    String getScriptExecChildCoatIds ( String coatid );

    Map getScriptExecListForRest ( int start, int limit, String coatids, String flag );

    Map getDbaasScriptFlowExecHistory ( Integer start, Integer limit, ScriptFlow filter );

    Map dbaasgetScriptCoatList ( Integer start, Integer limit, ScriptCoat filter );

    Map dbaasgetScriptExecList ( Integer start, Integer limit, String coatid, String flag,
            ScriptExecHistoryQueryBean queryBean );

    List<Map> getDbaasMultiScriptExecAllList ( String ids, int flag );

    String searchAnalyzeResultByCoatId ( Long coatId );

    Map getScriptTaskMonitorList ( Integer start, Integer limit, ScriptTaskMonitorBean filter, String queryType );

    String getdbaasGroupScriptExecOutput ( Long flowId, Long resiId );

    public List<Map> dbaasgetScriptcolumnsforHistory ( String scriptuuid );

    Map dbaasgetHistoryDtailData ( Long resId, Long flowId, String scriptuuid, String tableName, int start, int limit,
            Long iid );
    /**
     * 
     * <li>Description:资源管理-资源展示-按资源id查询执行历史列表</li> 
     * <AUTHOR>
     * 2020年9月18日 
     * @param start
     * @param limit
     * @param filter
     * @param resId
     * @return
     * return Map
     */
    public Map getServiceExecuteHistoryByResource ( Integer start, Integer limit, ScriptFlow filter, long resId );

    public Map getParams(long workitemid);

    public boolean getEsProMessage();
}
