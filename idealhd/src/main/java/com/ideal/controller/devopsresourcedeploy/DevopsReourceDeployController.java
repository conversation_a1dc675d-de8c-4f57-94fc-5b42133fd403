package com.ideal.controller.devopsresourcedeploy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.devopsresourcedeloy.DevopsDeployBean;
import com.ideal.service.devopsresourcedeploy.DevopsReourceDeployService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 
 * <ul>
 * <li>Title: DevopsReourceDeployController.java</li>
 * <li>Description:Devops资源配置控制层</li>
 * <li>Copyright: Copyright 2019</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2019年12月26日
 */
@Controller
public class DevopsReourceDeployController
{
    private Logger log = Logger.getLogger(DevopsReourceDeployController.class);
    
    @RequestMapping("devopsResourceDeploy.do")
    public String devopsResourceDeploy(){
        return "devopsresourcedeploy/devopsResourceDeploy";
    }
    
    @RequestMapping("devopsAuditDeploy.do")
    public String devopsAuditDeploy(){
        return "devopsauditdeploy/devopsAuditDeploy";
    }
    
    /**
     * 
     * <li>Description:获取变更性质数据</li> 
     * <AUTHOR>
     * 2019年12月27日 
     * @return
     * return Map
     */
    @RequestMapping("getDevopsNatureList.do")
    @ResponseBody
    public Map getDevopsNatureList(int start,int limit){
        Map map = new HashMap();
        try
        {
            map = DevopsReourceDeployService.getInstance().getDevopsNatureList(start,limit,Constants.IEAI_SUS);
        } catch (RepositoryException e)
        {
            log.error("getDevopsNatureList is error："+e.getMessage());
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:获取紧急程度数据</li> 
     * <AUTHOR>
     * 2019年12月27日 
     * @return
     * return Map
     */
    @RequestMapping("getDevopsHowemerList.do")
    @ResponseBody
    public Map getDevopsHowemerList(int start,int limit){
        Map map = new HashMap();
        try
        {
            map = DevopsReourceDeployService.getInstance().getDevopsHowemerList(start,limit,Constants.IEAI_SUS);
        } catch (RepositoryException e)
        {
            log.error("getDevopsHowemerList is error："+e.getMessage());
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:devops资源配置保存</li> 
     * <AUTHOR>
     * 2019年12月27日 
     * @param jsonData
     * @return
     * return Map
     */
    @RequestMapping("saveDevopsData.do")
    @ResponseBody
    public Map saveDevopsResource(String jsonData){
        Map map = new HashMap(5);
        List<DevopsDeployBean> nList = new ArrayList<DevopsDeployBean>();
        List<DevopsDeployBean> hList = new ArrayList<DevopsDeployBean>();
        JSONObject jsonObject = JSONObject.fromObject(jsonData);
        JSONArray njsArray = jsonObject.getJSONArray("natureData");
        JSONArray hjsArray = jsonObject.getJSONArray("howemerData");
        for(int i=0;i<njsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(njsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            nList.add(bean);
        }
        
        for(int i=0;i<hjsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(hjsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            hList.add(bean);
        }
        
        DevopsReourceDeployService.getInstance().saveDevopsResource(nList, hList, Constants.IEAI_SUS,map);
        return map;
    }
    
    /**
     * 
     * <li>Description:devops资源配置删除</li> 
     * <AUTHOR>
     * 2019年12月27日 
     * @param jsonData
     * @return
     * return Map
     */
    @RequestMapping("delDevopsData.do")
    @ResponseBody
    public Map delDevopsResource(String jsonData){
        Map map = new HashMap(5);
        List<DevopsDeployBean> nList = new ArrayList<DevopsDeployBean>();
        List<DevopsDeployBean> hList = new ArrayList<DevopsDeployBean>();
        JSONObject jsonObject = JSONObject.fromObject(jsonData);
        JSONArray njsArray = jsonObject.getJSONArray("natureData");
        JSONArray hjsArray = jsonObject.getJSONArray("howemerData");
        for(int i=0;i<njsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(njsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            nList.add(bean);
        }
        
        for(int i=0;i<hjsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(hjsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            hList.add(bean);
        }
        DevopsReourceDeployService.getInstance().delDevopsResource(nList, hList, Constants.IEAI_SUS, map);
        return map;
    }
    
    /**
     * 
     * <li>Description:获取devops审核列表</li> 
     * <AUTHOR>
     * 2019年12月30日 
     * @return
     * return Map
     */
    @RequestMapping("getDevopsAuditList.do")
    @ResponseBody
    public Map getDevopsAuditList(int start,int limit){
        Map map = new HashMap();
        try
        {
            map = DevopsReourceDeployService.getInstance().getDevopsAuditList(start,limit,Constants.IEAI_SUS);
        } catch (Exception e)
        {
            log.error("getDevopsAuditList is error："+e.getMessage());
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:保存devops审核节点配置</li> 
     * <AUTHOR>
     * 2019年12月30日 
     * @param list
     * @param type
     * @return
     * return boolean
     */
    @RequestMapping("saveDevopsAuditData.do")
    @ResponseBody
    public Map saveDevopsAuditDeploy(String jsonData){
        Map map = new HashMap(5);
        List<DevopsDeployBean> nList = new ArrayList<DevopsDeployBean>();
        JSONObject jsonObject = JSONObject.fromObject(jsonData);
        JSONArray njsArray = jsonObject.getJSONArray("devopsData");
        for(int i=0;i<njsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(njsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            nList.add(bean);
        }
        
        boolean flag = DevopsReourceDeployService.getInstance().saveDevopsAuditDeploy(nList, Constants.IEAI_SUS);
        if(flag){
            map.put(Constants.SUS_SUCCESS, true);
            map.put(Constants.SUS_MESSAGE, "保存成功！");
        }else{
            map.put(Constants.SUS_SUCCESS, true);
            map.put(Constants.SUS_MESSAGE, "保存失败！");
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:删除devops审核配置</li> 
     * <AUTHOR>
     * 2019年12月30日 
     * @param list
     * @param type
     * @return
     * return boolean
     */
    @RequestMapping("delDevopsAuditDeploy.do")
    @ResponseBody
    public Map delDevopsAuditDeploy(String jsonData){
        Map map = new HashMap(5);
        List<DevopsDeployBean> list = new ArrayList<DevopsDeployBean>();
        JSONObject jsonObject = JSONObject.fromObject(jsonData);
        JSONArray jsArray = jsonObject.getJSONArray("jsonData");
        for(int i=0;i<jsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(jsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            list.add(bean);
        }
        
        boolean flag = DevopsReourceDeployService.getInstance().delDevopsAuditDeploy(list, Constants.IEAI_SUS);
        if(flag){
            map.put(Constants.SUS_SUCCESS, true);
            map.put(Constants.SUS_MESSAGE, "删除成功！");
        }else{
            map.put(Constants.SUS_SUCCESS, true);
            map.put(Constants.SUS_MESSAGE, "删除失败！");
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:获取应用变更下所有系统</li> 
     * <AUTHOR>
     * 2020年5月29日 
     * @return
     * return Map
     */
    @RequestMapping("getSystemInfo.do")
    @ResponseBody
    public Map getSystemInfo(){
        Map map = new HashMap(5);
        try
        {
            map = DevopsReourceDeployService.getInstance().getSystemInfo(Constants.IEAI_SUS);
            map.put(Constants.SUS_SUCCESS, true);
        } catch (RepositoryException e)
        {
            map.put(Constants.SUS_SUCCESS, false);
            log.error("DevopsReourceDeployController getSystemInfo is error："+e.getMessage());
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:保存绑定系统关系</li> 
     * <AUTHOR>
     * 2020年6月1日 
     * @param jsonData
     * @param devopsId
     * @return
     * return Map
     */
    @RequestMapping("saveBandSystemRelation.do")
    @ResponseBody
    public Map saveBandSystemRelation(String jsonData,long devopsId){
        return DevopsReourceDeployService.getInstance().saveBandSystemRelation(jsonData, devopsId, Constants.IEAI_SUS);
    }
    
    /**
     * 
     * <li>Description:查询绑定系统关系</li> 
     * <AUTHOR>
     * 2020年6月1日 
     * @param bean
     * @return
     * return Map
     */
    @RequestMapping("querBandSystem.do")
    @ResponseBody
    public Map querBandSystem(DevopsDeployBean bean,int start,int limit){
        Map map = new HashMap(5);
        try
        {
            map = DevopsReourceDeployService.getInstance().querBandSystem(bean, start, limit, Constants.IEAI_SUS);
        } catch (RepositoryException e)
        {
            log.error("querBandSystem is error："+e.getMessage());
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:删除绑定系统关系</li> 
     * <AUTHOR>
     * 2020年6月2日 
     * @param jsonData
     * @param iid
     * @return
     * return Map
     */
    @RequestMapping("delDevopsSystemRelation.do")
    @ResponseBody
    public Map delDevopsSystemRelation(String jsonData,long iid){
        Map map = new HashMap(5);
        List<DevopsDeployBean> list = new ArrayList<DevopsDeployBean>();
        JSONObject jsonObject = JSONObject.fromObject(jsonData);
        JSONArray jsArray = jsonObject.getJSONArray("jsonData");
        for(int i=0;i<jsArray.size();i++){
            JSONObject obj = JSONObject.fromObject(jsArray.get(i));
            DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
            bean.setIid(iid);
            list.add(bean);
        }
        
        boolean flag = DevopsReourceDeployService.getInstance().delDevopsSystemRelation(list, Constants.IEAI_SUS);
        if(flag){
            map.put(Constants.SUS_SUCCESS, true);
            map.put(Constants.SUS_MESSAGE, "删除成功！");
        }else{
            map.put(Constants.SUS_SUCCESS, true);
            map.put(Constants.SUS_MESSAGE, "删除失败！");
        }
        return map;
    }
    
    /**
     * 
     * <li>Description:获取devops审核列表--devops资源配置底部节点模式</li> 
     * <AUTHOR>
     * 2021年7月7日 
     * @param start
     * @param limit
     * @return
     * return Map
     */
     @RequestMapping("getDevopsAuditList1.do")
     @ResponseBody
     public Map getDevopsAuditList1(int start,int limit){
         Map map = new HashMap();
         try
         {
             map = DevopsReourceDeployService.getInstance().getDevopsAuditList1(start,limit,Constants.IEAI_SUS);
         } catch (Exception e)
         {
             log.error("getDevopsAuditList1 is error："+e.getMessage());
         }
         return map;
     }
     
     /**
      * 
      * <li>Description:保存devops审核节点配置----devops资源配置底部节点模式</li> 
      * <AUTHOR>
      * 2021年7月7日 
      * @param jsonData
      * @return
      * return Map
      */
     @RequestMapping("saveDevopsAuditData1.do")
     @ResponseBody
     public Map saveDevopsAuditDeploy1(String jsonData){
         Map map = new HashMap(5);
         List<DevopsDeployBean> nList = new ArrayList<DevopsDeployBean>();
         JSONObject jsonObject = JSONObject.fromObject(jsonData);
         JSONArray njsArray = jsonObject.getJSONArray("devopsData");
         for(int i=0;i<njsArray.size();i++){
             JSONObject obj = JSONObject.fromObject(njsArray.get(i));
             DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
             nList.add(bean);
         }
         
         boolean flag = DevopsReourceDeployService.getInstance().saveDevopsAuditDeploy1(nList, Constants.IEAI_SUS);
         if(flag){
             map.put(Constants.SUS_SUCCESS, true);
             map.put(Constants.SUS_MESSAGE, "保存成功！");
         }else{
             map.put(Constants.SUS_SUCCESS, true);
             map.put(Constants.SUS_MESSAGE, "保存失败！");
         }
         return map;
     }
     
     /**
      * 
      * <li>Description:删除devops审核配置--devops资源配置底部节点模式</li> 
      * <AUTHOR>
      * 2021年7月7日 
      * @param jsonData
      * @return
      * return Map
      */
     @RequestMapping("delDevopsAuditDeploy1.do")
     @ResponseBody
     public Map delDevopsAuditDeploy1(String jsonData){
         Map map = new HashMap(5);
         List<DevopsDeployBean> list = new ArrayList<DevopsDeployBean>();
         JSONObject jsonObject = JSONObject.fromObject(jsonData);
         JSONArray jsArray = jsonObject.getJSONArray("jsonData");
         for(int i=0;i<jsArray.size();i++){
             JSONObject obj = JSONObject.fromObject(jsArray.get(i));
             DevopsDeployBean bean = (DevopsDeployBean) JSONObject.toBean(obj, DevopsDeployBean.class);
             list.add(bean);
         }
         
         boolean flag = DevopsReourceDeployService.getInstance().delDevopsAuditDeploy1(list, Constants.IEAI_SUS);
         if(flag){
             map.put(Constants.SUS_SUCCESS, true);
             map.put(Constants.SUS_MESSAGE, "删除成功！");
         }else{
             map.put(Constants.SUS_SUCCESS, true);
             map.put(Constants.SUS_MESSAGE, "删除失败！");
         }
         return map;
     }
}
