<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.PersonalityEnv"%>
<%@ page import="com.ideal.ieai.commons.Constants"%>
<%
String bankFlag = Environment.getInstance().getBankSwitch();
boolean fjnxResFlag = Environment.getInstance().getBankSwitchIsFjnx();
boolean pfFlag=false;
if((Constants.BANK_PFB).equals(bankFlag))
{
    pfFlag=true;
}
%>

<html>
<head>
  <script type="text/javascript">
  		var overViewBtnShow=<%=ServerEnv.getInstance().getBooleanConfig(Environment.EQUIPMENTMAINTAINSAVE, false)%>;
  		var resourgroupisfilter=<%=ServerEnv.getInstance().getBooleanConfig(Environment.RESOURCEGROUP_ISFILTERSUS, false)%>;
  		var cmdbsyncfilter=<%=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_CMDB_SYNC_SWITCH, false)%>;
  		var resGroupParaImport=<%=PersonalityEnv.isPFSUSResGroupNewImportValue()%>;
  		var pfFlag=<%=pfFlag%>;
        var fjnxResFlag=<%=fjnxResFlag%>;
  		var resCopySwich=<%=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RES_COPY_SWITCH, false)%>;
        var resServerSwich=<%=ServerEnv.getInstance().getBooleanConfig(Environment.RESOURCE_SERVER_NEW_SWITCH, false)%>;
  		var compare = <%=Environment.getInstance().getBooleanConfig(Environment.getInstance().SUS_ENVRES_PARAMS_COMPARE, false)%>;
        var paraCheckNanJingSwitch = <%=Environment.getInstance().getBooleanConfig("paraCheckNanJingSwitch", false)%>;
         var fjnxSyncSystemSwitch = <%=Environment.getInstance().getBooleanConfig(Environment.getInstance().FJNX_SYNC_SYSTEM_SWITCH, false)%>;
	    var resExportSwitch=<%=ServerEnv.getInstance().getBooleanConfig(Environment.IEAI_SUS_RES_EXPORT_SWITCH, false)%>;
	    //#新版资源组环境与资源组绑定导入导入开关
        var env_download = <%=Environment.getInstance().getBooleanConfig(Environment.getInstance().SUS_RESBOOST_ENV_DOWNLOAD_ONOFF, false)%>;
        var scriptEncryptSwitch = <%= Environment.getInstance().getScriptEncrypt()%>;
  		</script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/resourcemanager/resnew/resboost.js"></script>
<style type="text/css">
	.x-panel-header-text-container-default{ font-size:14px;}
	.buttonLeftDiv{
	position: relative;
    width:0px;
    height:40px;
    padding-left:0px;
    padding-right:0px;
}
.buttonRightDiv{
	position: relative;
    width:0px;
    height:40px;
    padding-left:0px;
    padding-right:0px;
}
.my_row_yellow .x-grid-cell.x-grid-td{
    background-color:Yellow;
}
</style>
</head>
<body>
<!-- 资源组-->
<div id="resboostDiv" style="width: 100%;height: 100%">
</div>
</body>
</html>