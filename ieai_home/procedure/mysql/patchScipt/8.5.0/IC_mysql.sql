	-- 4.7.17 not exists	
	-- 4.7.18 not exists	
	-- 4.7.19 not exists	
	-- 4.7.20 not exists
	-- 4.7.21 not exists	
	
	-- 4.7.22
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
	
		CREATE TABLE IF NOT EXISTS IEAI_COLLECTION_UPLOAD (IID NUMERIC(19) NOT NULL,RECORDID VARCHAR(255),TASKTYPE NUMERIC(4),AGENTIP VARCHAR(30),SAVEFILEPATH VARCHAR(255),FILECONTENT VARCHAR(255),<PERSON>UNZ<PERSON> VARCHAR(10),UNZIPPATH VARCHAR(255),ISSUCCESS VARCHAR(10),USERPERMISSION VARCHAR(255),GROUPPERMISSION VARCHAR(255),<PERSON>RAMPERMISSION VARCHAR(255),OPDES<PERSON> VARCHAR(255),OPUSER VARCHAR(255),OP<PERSON><PERSON> NUMERIC(19),CO<PERSON><PERSON>AINT PK_IEAI_COLLECTION_UPLOAD PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_COLLECTION_PICKUPINFO (IID  NUMERIC(19) NOT NULL, RECORDID VARCHAR(255),TASKTYPE NUMERIC(4), IINSTANCEID NUMERIC(19), SENDURL VARCHAR(255), SENDUSER VARCHAR(255), SENDIP VARCHAR(20), SENDTIME NUMERIC(19), EXECSTATUS NUMERIC(4), IDESC VARCHAR(255), ISSENDSUCSTATUS NUMERIC(2),CONSTRAINT PK_IEAI_COLLECTION_PICKUPINFO PRIMARY KEY (IID));
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'ISYSTYPE') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD ISYSTYPE NUMERIC(19) DEFAULT 0 ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'DEVICETYPE') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD DEVICETYPE VARCHAR(255) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'TASKTYPE') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD TASKTYPE NUMERIC(4) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'OUTPATH') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD OUTPATH VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'GLOBALIP') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD GLOBALIP VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'MOBILEID') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD MOBILEID VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'TASKTYPE') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD TASKTYPE NUMERIC(4) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'OUTPATH') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD OUTPATH VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'GLOBALIP') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD GLOBALIP VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'MOBILEID') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD MOBILEID VARCHAR(255);
		END IF;	

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.23
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
	
		CREATE TABLE IF NOT EXISTS IEAI_COLLECTION_RESCALL(IID NUMERIC(19) NOT NULL, RECORDID VARCHAR(255), IINSTANCEID NUMERIC(19), STARTTIME NUMERIC(19), ENDTIME NUMERIC(19), STATUS NUMERIC(2),CONSTRAINT PK_IEAI_COLLECTION_RESCALL PRIMARY KEY (IID));
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECTION_PICKUPINFO' AND COLUMN_NAME = 'SENDMSG') THEN
			ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD SENDMSG VARCHAR(4000) ;
		END IF;	
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'ISYSTYPE') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD ISYSTYPE NUMERIC(19) DEFAULT 0 ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'DEVICETYPE') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD DEVICETYPE VARCHAR(255) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'DBINSNAME') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD DBINSNAME VARCHAR(255) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'USERPERMISSION') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD USERPERMISSION VARCHAR(255) ;
		END IF;	
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'GROUPPERMISSION') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD GROUPPERMISSION VARCHAR(255) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'HD_FTP_SCRIPT' AND COLUMN_NAME = 'PARAMPERMISSION') THEN
			ALTER TABLE HD_FTP_SCRIPT ADD PARAMPERMISSION VARCHAR(255) ;
		END IF;			
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'TASKTYPE') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD TASKTYPE NUMERIC(4) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'OUTPATH') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD OUTPATH VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'GLOBALIP') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD GLOBALIP VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'TASKTYPE') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD TASKTYPE NUMERIC(4) ;
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'OUTPATH') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD OUTPATH VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'GLOBALIP') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD GLOBALIP VARCHAR(255);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'NETEQUIIP') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE ADD NETEQUIIP VARCHAR(20);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'NETEQUIIP') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD NETEQUIIP VARCHAR(20);
		END IF;	
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME = 'NETEQUIIP') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD NETEQUIIP VARCHAR(20);
		END IF;	

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	
	IF NOT EXISTS (SELECT  *  FROM IEAI_DBSOURCE WHERE  IDBSOURCEID=19) THEN
		INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES(19,19,'信息采集源',0);
	commit;	
	END IF;
	
	
  IF NOT EXISTS (SELECT  *  FROM IEAI_PROJECT WHERE  IID=-19) THEN
		INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-19, 0, '所有采集信息业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 19, -19, -19, -19);
	commit;
	END IF;
	
	
	IF NOT EXISTS (SELECT  *  FROM IEAI_GROUPMESSAGE WHERE  GROUPID=19) THEN
		INSERT INTO IEAI_GROUPMESSAGE(GROUPID, GROUPNAME, GROUPDESCRIPTION, IORDER, IIMG)	VALUES(19, '信息采集', '信息采集模块组', 19, 'images/info110.png');
	commit;
	END IF;
	
	IF NOT EXISTS (SELECT  *  FROM IEAI_FTPTYPE WHERE  TID=45) THEN
		INSERT INTO IEAI_FTPTYPE(TID,FTPTYPENAME) VALUES(45,'信息采集');
	commit;
	END IF;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.24
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_COLLECTION_PICKUPINFO' AND COLUMN_NAME = 'AGENTIP') THEN
			ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD AGENTIP VARCHAR(50);
		END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.25 
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
	
	IF EXISTS (SELECT  *  FROM IEAI_DBSOURCE WHERE  IDBSOURCEID=19) THEN
		UPDATE IEAI_DBSOURCE SET IDBSOURCENAME = '递蓝科源' WHERE IDBSOURCEID = 19;
	commit;	
	END IF;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.26 not exists	
	
	