package com.ideal.ieai.adaptors.generaladaptors.datatransform;

import java.awt.BorderLayout;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.awt.LayoutManager;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.ArrayList;

import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JPanel;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;

import com.ideal.ieai.adaptors.generaladaptors.scriptact.ScriptInputEditor;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.DefaultConfig;

/**
 *
 * <p>Title:Adaptors </p>
 * <p>Description: for transform data form</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: </p>
 * <AUTHOR>
 * @version 3.0
 */
public class ExportSettingEditor
    extends JPanel
    implements ActionListener
{

    public ExportSettingEditor()
    {
        try
        {
            jbInit();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public ExportSettingEditor(LayoutManager layout, boolean isDoubleBuffered)
    {
        super(layout, isDoubleBuffered);
    }

    public ExportSettingEditor(LayoutManager layout)
    {
        super(layout);
    }

    public ExportSettingEditor(boolean isDoubleBuffered)
    {
        super(isDoubleBuffered);
    }

    private void jbInit() throws Exception
    {
        this.setName(ScriptInputEditor.res.getString("datahandler.format"));
        this.setLayout(borderLayout1);
        jchkb_log.setText(ScriptInputEditor.res.getString("datahandler.printInLog"));
        jchkb_log.addActionListener(this);
        jchkb_console.setText(ScriptInputEditor.res.getString("datahandler.printInConsole"));
        jchkb_console.addActionListener(this);
        //set combobox enable
        setLogEnable();
        jcmb_choose.addActionListener(this);
        jPanel1.setLayout(gridBagLayout1);

        this.add(jPanel1, BorderLayout.NORTH);
        jPanel1.add(jchkb_log, new GridBagConstraints(0, 1, 1, 1, 0.0, 0.0
            , GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        jPanel1.add(jchkb_console, new GridBagConstraints(0, 0, 1, 1, 0.0, 0.0
            , GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 0, 0));
        jPanel1.add(jcmb_choose,  new GridBagConstraints(1, 1, 1, 1, 1.0, 0.0
            ,GridBagConstraints.CENTER, GridBagConstraints.HORIZONTAL, new Insets(4, 4, 4, 4), 0, 0));
        //index 0,1,2,3
        jcmb_choose.addItem(ScriptInputEditor.res.getString("datahandler.log.info"));
        jcmb_choose.addItem(ScriptInputEditor.res.getString("datahandler.log.error"));
        jcmb_choose.addItem(ScriptInputEditor.res.getString("datahandler.log.info"));
        jcmb_choose.addItem(ScriptInputEditor.res.getString("datahandler.log.warn"));
    }

    public void actionPerformed(ActionEvent actionEvent)
    {
        fireDataChanged();
        //set combobox enable
        setLogEnable();
    }

    /**
     * for combobox enable,if log checkbox selected,set enable true;
     */
    private void setLogEnable()
    {
        if (jchkb_log.isSelected())
        {
            jcmb_choose.setEnabled(true);
        }
        else
        {
            jcmb_choose.setEnabled(false);
        }
    }

    public void init(DefaultConfig TheCfg) throws ActivityException
    {
        Object inConsoleFlag = TheCfg.get(DataHandlerActKey.PRINTINCONSOLE);
        Object inLogFlag = TheCfg.get(DataHandlerActKey.PRINTINLOG);
        if (null != inConsoleFlag)
        {
            jchkb_console.setSelected(true);
        }
        if (null != inLogFlag)
        {
            jchkb_log.setSelected(true);
            jcmb_choose.setEnabled(true);
            String logLevel = (String)TheCfg.get(DataHandlerActKey.LOGLEVEL);
            //init combobox text
            if (logLevel.equals(DataHandlerActKey.LEVEL_DEBUG))
            {
                jcmb_choose.setSelectedItem(ScriptInputEditor.res.getString("datahandler.log.info"));
            }
            else if (logLevel.equals(DataHandlerActKey.LEVEL_ERROR))
            {
                jcmb_choose.setSelectedItem(ScriptInputEditor.res.getString("datahandler.log.error"));
            }
            else if (logLevel.equals(DataHandlerActKey.LEVEL_INFO))
            {
                jcmb_choose.setSelectedItem(ScriptInputEditor.res.getString("datahandler.log.info"));
            }
            else if (logLevel.equals(DataHandlerActKey.LEVEL_WARN))
            {
                jcmb_choose.setSelectedItem(ScriptInputEditor.res.getString("datahandler.log.warn"));
            }
        }

    }

    public boolean canSave()
    {
        return true;
    }

    public void save(DefaultConfig TheCfg)
    {
        //print in console
        if (jchkb_console.isSelected())
        {
            TheCfg.put(DataHandlerActKey.PRINTINCONSOLE, DataHandlerActKey.PRINTFLAG);
        }
        if (jchkb_log.isSelected())
        { //put log level in
            TheCfg.put(DataHandlerActKey.PRINTINLOG, DataHandlerActKey.PRINTFLAG);
            if (jcmb_choose.getSelectedItem().equals(ScriptInputEditor.res.getString(
                "datahandler.log.info")))
            {
                TheCfg.put(DataHandlerActKey.LOGLEVEL, DataHandlerActKey.LEVEL_DEBUG);
            }
            if (jcmb_choose.getSelectedItem().equals(ScriptInputEditor.res.getString(
                "datahandler.log.error")))
            {
                TheCfg.put(DataHandlerActKey.LOGLEVEL, DataHandlerActKey.LEVEL_ERROR);
            }
            if (jcmb_choose.getSelectedItem().equals(ScriptInputEditor.res.getString(
                "datahandler.log.info")))
            {
                TheCfg.put(DataHandlerActKey.LOGLEVEL, DataHandlerActKey.LEVEL_INFO);
            }
            if (jcmb_choose.getSelectedItem().equals(ScriptInputEditor.res.getString(
                "datahandler.log.warn")))
            {
                TheCfg.put(DataHandlerActKey.LOGLEVEL, DataHandlerActKey.LEVEL_WARN);
            }
        }
    }

    public void addChangeListener(ChangeListener TheListener)
    {
        if (null != TheListener)
        {
            _changeListeners.add(TheListener);
        }
    }

    public void removeChangeListener(ChangeListener TheListener)
    {
        if (null != TheListener)
        {
            _changeListeners.remove(TheListener);
        }
    }

    protected void fireDataChanged()
    {
        for (int i = 0, size = _changeListeners.size(); i < size; i++)
        {
            ( (ChangeListener) _changeListeners.get(i)).stateChanged(new ChangeEvent(this));
        }
    }

//    static ResourceBundle res = ResourceBundle.getBundle(
//        "resource.ieaiacts");
    JPanel jPanel1 = new JPanel();
    BorderLayout borderLayout1 = new BorderLayout();
    JCheckBox jchkb_log = new JCheckBox();
    JCheckBox jchkb_console = new JCheckBox();
    JComboBox jcmb_choose = new JComboBox();
    GridBagLayout gridBagLayout1 = new GridBagLayout();

    private java.util.List _changeListeners = new ArrayList();
}
