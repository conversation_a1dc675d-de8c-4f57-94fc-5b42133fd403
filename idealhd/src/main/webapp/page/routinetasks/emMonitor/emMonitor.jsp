<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	//单号显示开关
	boolean numShowSwitch = Environment.getInstance().getemAssociatedSwitch();
%>
<html>
<head>
	<script type="text/javascript">
		var isysname = '<%=request.getParameter("isysname")==null?"":request.getParameter("isysname")%>';
		var istate = '<%=request.getParameter("istate")==null?"":request.getParameter("istate")%>';
		var numShowSwitch=<%=numShowSwitch%>;
		var comFrom = '<%=request.getParameter("comFrom")==null?"":request.getParameter("comFrom")%>';
	</script>
	<script type="text/javascript" src="<%=request.getContextPath()%>/page/routinetasks/common/shellinfoerroperEM.js"></script>
	<script type="text/javascript"
			src="<%=request.getContextPath()%>/page/routinetasks/emMonitor/emMonitor.js"></script>
</head>
<body>
<div id="em_monitor_div" style="width: 100%; height: 100%;">
</div>
</body>
</html>