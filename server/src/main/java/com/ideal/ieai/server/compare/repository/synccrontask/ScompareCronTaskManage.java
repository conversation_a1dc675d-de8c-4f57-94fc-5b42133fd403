package com.ideal.ieai.server.compare.repository.synccrontask;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.apache.tools.ant.util.DateUtils;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;

public class ScompareCronTaskManage
{
    private Logger log = Logger.getLogger(ScompareCronTaskManage.class);

    public List getScompareCronTaskList ( Connection conn, String sql,String queryString ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            if (queryString != null && !queryString.isEmpty()) {
                ps.setString(1, "%" + queryString + "%");
                ps.setString(2, "%" + queryString + "%");
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                SynccrontaskModel model = new SynccrontaskModel();
                model.setIid(rs.getLong("iid"));
                model.setIplanid(rs.getLong("iplanid"));
                model.setIplanname(rs.getString("iplanname"));
                model.setIplandesc(rs.getString("iplandesc"));
                model.setIcron(rs.getString("icron"));
                model.setIstatus(rs.getInt("istatus"));
                model.setIcreater(rs.getString("icreater"));
                model.setIcreatetime(
                    DateUtils.format(rs.getLong("icreatetime") + 8 * 60 * 60 * 1000, "yyyy-MM-dd HH:mm:ss"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("ScompareCrontaskManage.getScompareCrontaskList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getScompareCrontaskList", log);
        }
        return list;
    }

    public int getScompareCrontaskCount ( Connection conn, String sql ) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("countNum");
            }
        } catch (Exception e)
        {
            log.error("ScompareCrontaskManage.getScompareCrontaskCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getScompareCrontaskCount", log);
        }
        return count;
    }

    public List<SynccrontaskModel> getSysccronTaskInfo ( Connection conn, String iid ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<SynccrontaskModel> list = new ArrayList<SynccrontaskModel>();
        String sql = "select a.iid,a.icron from IEAI_SCOMPARE_CRONTASK a where a.iid in (";
        String[] ids = iid.split(",");
        StringBuilder placeholders = new StringBuilder();
        for (int i = 0; i < ids.length; i++) {
            if (i > 0) {
                placeholders.append(", ");
            }
            placeholders.append("?");
        }

        sql += placeholders + ")";
        try
        {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < ids.length; i++) {
                ps.setString(i + 1, ids[i]);
            }
            rs = ps.executeQuery();
            SynccrontaskModel model = null;
            while (rs.next())
            {
                String istarttime = rs.getString("icron");
                model = new SynccrontaskModel();
                model.setIid(rs.getLong("iid"));
                model.setIcron(istarttime);
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("ScompareCrontaskManage.getSysccronTaskInfo is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getSysccronTaskInfo", log);
        }
        return list;
    }

    public void updateScompareCronTaskState ( Connection conn, String iid, int state ) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "update IEAI_SCOMPARE_CRONTASK a set a.istatus=? where a.iid in (";
        String[] ids = iid.split(",");
        StringBuilder placeholders = new StringBuilder();
        for (int i = 0; i < ids.length; i++) {
            if (i > 0) {
                placeholders.append(", ");
            }
            placeholders.append("?");
        }

        sql += placeholders + ")";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setInt(1, state);
            for (int i = 0; i < ids.length; i++) {
                ps.setString(i + 2, ids[i]);
            }
            ps.executeUpdate();
        } catch (Exception e)
        {
            log.error("ScompareCrontaskManage.updateScompareCronTaskState is error", e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "ScompareCrontaskManage.updateScompareCronTaskState", log);
        }
    }

    public void deleteScompareCronTask ( Long iid, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        try
        {
            String sql = "delete from IEAI_SCOMPARE_CRONTASK  where iid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.executeUpdate();
        } catch (Exception e)
        {
            log.error("ScompareCrontaskManage.deleteSysccrontask is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "ScompareCrontaskManage.deleteSysccrontask", log);
        }
    }

    public SynccrontaskModel getScompareCrontaskModel ( Connection conn, Long iid ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        SynccrontaskModel model = null;
        try
        {
            String sql = "select a.iid,a.iplanid from IEAI_SCOMPARE_CRONTASK a where  a.iid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                model = new SynccrontaskModel();
                model.setIid(rs.getLong("iid"));
                model.setIplanid(rs.getLong("iplanid"));
            }
        } catch (Exception e)
        {
            log.error("ScompareCronTaskManage.getScompareCrontaskModel is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getScompareCrontaskModel", log);
        }
        return model;
    }

    public void changeCronTime ( Connection conn, String iid, String istarttime ) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "update IEAI_SCOMPARE_CRONTASK a set a.icron=? where a.iid=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, istarttime);
            ps.setString(2, iid);
            ps.executeUpdate();
        } catch (Exception e)
        {
            log.error("ScompareCrontaskManage.changeCronTime is error", e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "ScompareCrontaskManage.changeCronTime", log);
        }
    }
}
