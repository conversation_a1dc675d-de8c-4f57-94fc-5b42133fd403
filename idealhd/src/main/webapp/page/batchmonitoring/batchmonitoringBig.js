var delayedBatchStoreBh;
var manualBatchStoreBh;
var winWidthBh = 430;         // 弹出页面宽度
var winHeightBh = 320; 		// 弹出页面高度
var errorWinWidthBh = 430;         // 弹出页面宽度
var errorWinHeightBh = 630; 		// 弹出页面高度
var winDoubleCheckWidthBh = 760;
var customWidthBh;
var customHeightBh;
var panelTopLeftBh;               // 报错批量栏 topLeft
var panelTopRightBh;              // 超时批量栏 topRight
var panelMiddleBh;                // 延时批量栏 middle
var panelBottomLeftBh;            // 运行批量栏 bottomLeft
var panelBottomRightBh;           // 手工批量栏 bottomRight
var manualConfirmWinBh;
var monitorAutoFreshTimeBh;
var bhErrorBatchTreeStoreBh;      // 报错批量栏 treeStore
var bhErrorBatchTreeBh = null;    // 报错批量栏 tree
var bhTimeoutBatchTreeStoreBh;      // 超时批量栏 treeStore
var bhTimeoutBatchTreeBh = null;    // 超时批量栏 tree
var bhOdsStartBatchStoreBh = null;    // 超时批量栏 tree
// Store data
var utHomeFormBh;
var bhBigMiddlePanel;
Ext.onReady(function(){
    customWidthBh = contentPanel.getWidth () * 0.7 / 4 ;
    customHeightBh = contentPanel.getHeight () * 0.6;
    var treeFirWidth = parseInt(halfWidthBig/6);
    var treeSecWidth = parseInt(halfWidthBig/7);
    var treeSecTimeOutWidth = parseInt(halfWidthContent2/8);
    var treeTimeoutFirWidth = parseInt(halfWidthBig/3);
    var colMaxWidth = parseInt(halfWidthBig/5);
    var colMinWidth = parseInt(halfWidthBig/6);



    // 清理主面板的各种监听时间
    destroyRubbish();
    /* 报错批量栏tree列表-start */
    var operateTablePlugin = Ext.create('Ext.form.TextField', {
        fieldLabel: '操作Grid',
        name: 'operateGrid',
        id: 'operateGridId',
        width: 300,
        labelWidth: 90,
        labelAlign: 'right',
        left: 20,
        hidden:true,
        xtype: 'textfield'
    });
    var operatePlugin = Ext.create('Ext.form.TextField', {
        fieldLabel: '操作节点',
        name: 'operateName',
        id: 'operateNameId',
        width: 300,
        labelWidth: 90,
        labelAlign: 'right',
        left: 20,
        hidden:true,
        xtype: 'textfield'
    });
    var operateTypePlugin = Ext.create('Ext.form.TextField', {
        fieldLabel: '操作类型',
        name: 'operateType',
        id: 'operateTypeId',
        width: 300,
        labelWidth: 90,
        labelAlign: 'right',
        left: 20,
        hidden:true,
        xtype: 'textfield'
    });

    var operateMouseErrorTopBig= Ext.create('Ext.form.TextField', {
        fieldLabel: 'topBig(error)',
        name: 'err_scrollTopBig',
        id: 'err_scrollTopBig',
        width: 300,
        labelWidth: 90,
        labelAlign: 'right',
        left: 20,
        hidden:true,
        xtype: 'textfield'
    });
    var  operateMouseTimeOutTopBig = Ext.create('Ext.form.TextField', {
        fieldLabel: 'topBig(timeOut)',
        name: 'timeOut_scrollTopBig',
        id: 'timeOut_scrollTopBig',
        width: 300,
        labelWidth: 90,
        labelAlign: 'right',
        left: 20,
        hidden:true,
        xtype: 'textfield'
    });

    // 左侧列表
    Ext.define('bhErrorBatchTreeBhModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'childName',  //子系统
            type: 'string'
        }, {
            name: 'actName',  //子系统
            type: 'string'
        }, {
            name: 'dateTime', // 数据日期
            type: 'string'
        }, {
            name: 'userName', // 联系人
            type: 'string'
        }, {
            name: 'phone', // 电话
            type: 'string'
        }, {
            name: 'sumNum',//数量
            type: 'string'
        }, {
            name: 'level',//等级
            type: 'string'
        }, {
            name: 'createTime', // 报错时间
            type: 'string'
        }, {
            name: 'jobStatus', // 状态 SUCCESS, START, FAIL
            type: 'string'
        },{
            name: 'jobName', // 状态 SUCCESS, START, FAIL
            type: 'string'
        },  {
            name: 'flowId', // flowId
            type: 'long'
        }, {
            name: 'errorId', // errorId
            type: 'long'
        }, {
            name: 'oper', // errorId
            type: 'string'
        }, {
            name: 'baseName', // baseName 作业源
            type: 'string'
        }, {
            name: 'iwarnaffirm', // IEAI_MQ_CONSUMER增加报警确认标识
            type: 'int'
        }]
    });

    bhErrorBatchTreeStoreBh = Ext.create('Ext.data.TreeStore', {
        autoLoad : true,
        model : 'bhErrorBatchTreeBhModel',
        root : {
            expanded : false
        },
        proxy : {
            type : 'ajax',
            url : 'getBhOdsQueryErrorBatchTree.do'
        },
        autoSync: false,
        listeners:{ // 超时批量栏响铃
            load :function(store){
                if (store.proxy.reader.jsonData.volflag) {
                    loadAlarmImgAndDataErrorB(true);
                    loadAlarmImgAndDataError(true);
                }
                else {
                    loadAlarmImgAndDataErrorB(false);
                    loadAlarmImgAndDataError(false);
                }
            }
        }
    });

    bhErrorBatchTreeBh = Ext.create('Ext.tree.Panel', {
        id:'bhErrorBatchTreeBh',
        name:'bhErrorBatchTreeBh',
        height: customHeightModel,
        store: bhErrorBatchTreeStoreBh,
        expandLevel: 2,
        useArrows: true,
        rootVisible: false,
        multiSelect: true,    // 多选
        autoScroll: true,    // 自动滚屏
        //columns :gridColumns,
        width: '100%',
        columns: [
            {
                sortable: true,
                dataIndex: 'childName',
                xtype: 'treecolumn',
                width: '100%',
                expanded: true,
                text: '',
                renderer: function (data, metaData, record) {
                    // 子系统、数据日期、负责人、电话、报错数量，
                    var spanText1 = "";
                    var level = record.get("level");
                    if (level === "1") {
                        var childName = record.get('childName');
                        var dateTime = record.get('dateTime');
                        var userName = record.get('userName');
                        var baseName = record.get('baseName');
                        var phone = record.get('phone');
                        // 背景色 add by lili_xing 2023-0620
                        var iwarnaffirm = record.get("iwarnaffirm");
                        var nodeStatus1= "";
                        if (baseName != "作业源") {
                            if (0 == iwarnaffirm) {
                                nodeStatus1 = "x-tree-node-text-warning";
                            }
                            if (baseName == "翻牌检验" || baseName == "BSP"){
                                nodeStatus1 = "x-tree-node-text-warning";
                            }
                        }
                        spanText1 = '<span class="x-tree-node-text x-tree-node-content ' + nodeStatus1 + ' " style="width: '+halfWidthBigContent+'px">' +
                            '<span class="x-tree-node-text-first" style="text-align: left;width: '+70+'px" > ' + baseName + '</span>' +
                            '<span  class="x-tree-node-text-first" style="text-align: left;width: '+treeFirWidth+'px"> ' + childName + '</span>' +
                            '<span class="x-tree-node-text-first" style="width: '+treeFirWidth+'px" > ' + dateTime + '</span>' +
                            '<span class="x-tree-node-text-first" style="width: '+treeFirWidth+'px" > ' + userName + '</span>' +
                            '<span class="x-tree-node-text-first" style="width: '+treeFirWidth+'px" > ' + phone + '</span>' +
                            '<span class="x-tree-node-text-first" style="width: '+treeFirWidth+'px" > ' + record.get('sumNum') + '</span>' +
                            '</span>';
                    } else if (level === "2")  {
                        var childName = record.get('childName');
                        var dateTime = record.get('dateTime');
                        var jobStatus = record.get('jobStatus');
                        var jobStatus1 = record.get('jobStatus');
                        var createTime = record.get('createTime');
                        var jobName = record.get('jobName');
                        var actName = record.get('actName');
                        var oper = record.get('oper');
                        if (jobStatus != "作业状态"   ) {
                            if ( jobStatus != "fail"  ){
                                jobStatus = "<a href=javascript:void(0);' " +
                                    "onclick=\"errorBatchStatuBh('" + record.get('jobName') + "','" + record.get('flowId') + "','" +
                                    record.get('childName') +"','" + record.get('jobStatus')+ "','" + record.get('createTime') +"');\"><span>" + record.get('jobStatus') + "</span></a>"
                            }else {
                                jobStatus = "<a href=javascript:void(0);' " +
                                    "onclick=\"errorTimeCheck('" + record.get('jobName') + "','" + record.get('errorId') + "','" +
                                    record.get('childName') + "','" + record.get('jobStatus')+ "','"+ record.get('createTime') +"');\"><span>" + record.get('jobStatus') + "</span></a>"
                            }

                        }
                        if (jobName != "报错节点") {
                            if (jobStatus1 == "fail") {
                                // jobName = "<a href=javascript:void(0);' " +
                                //     "onclick=\"openErrorTaskDetail('" + record.get("jobStatus") + "','" + record.get('errorId') + "');\"><span>" + record.get("jobName") + "</span></a>";
                            } else if(jobStatus1 =="FAIL") {
                                jobName = "<a href=javascript:void(0);' " +
                                    "onclick=\"errorBatchxx('" + record.get("jobName") + "','" + record.get('flowId') + "','" +
                                    record.get('childName') + "','" + record.get('errorId') + "');\"><span>" + record.get("jobName") + "</span></a>";
                            }else {
                                jobName = "<a href=javascript:void(0);' " +
                                    "onclick=\"openErrorTaskDetail('" + record.get("jobStatus") + "','" + record.get('errorId') + "');\"><span>" + record.get("jobName") + "</span></a>";
                            }
                        }
                        if (oper != "操作") {
                            var iwarnaffirm = record.get("iwarnaffirm");
                            if (jobStatus1 == "fail"){
                                oper = '<a href="javascript:void(0);" ' +
                                    'onclick="executeTimeCheck(\''+record.get("jobName") + "','"+
                                    record.get("errorId") + "','"+ jobStatus1 + "','"+ record.get("childName")+ "','"+ 1 +'\')">强制  </a>' +

                                    '<a href="javascript:void(0);"   style="text-align: right" ' +
                                    'onclick="executeTimeCheck(\''+record.get("jobName") + "','"+
                                    record.get("errorId") + "','"+ jobStatus1 + "','"+ record.get("childName")+ "','"+ 2 +'\')">终止</a>' +

                                    '<a href="javascript:void(0);"   style="text-align: right" ' +
                                    'onclick="errorTimeCheck(\''+record.get("jobName") + "','"+ record.get("errorId") +"','"+
                                    record.get("childName") + "','"+ record.get("jobStatus") +'\')"> 查看</a>' ;

                            }else if(jobStatus1 == "FAIL") {
                                // 背景色 add by lili_xing 2023-0620
                                var nodeStatus = "";
                                if(0==iwarnaffirm){
                                    nodeStatus = "x-tree-node-text-warning";
                                    oper = '<a href="javascript:void(0);"  ' +
                                        'onclick="retryRunB(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                                        record.get("errorId") + "','"+ jobStatus1 +"','"+ dateTime + "','"+ record.get("childName")+ "','"+ actName+'\')">重跑 </a>' +

                                        '<a href="javascript:void(0);"  style="text-align: right" ' +
                                        'onclick="skipB(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                                        record.get("errorId") + "','"+ jobStatus1 + "','"+ dateTime +"','"+ record.get("childName")+ "','"+ actName+'\')">跳过 </a>'+

                                        '<a href="javascript:void(0);"  ' +
                                        'onclick="verifyC(\''+record.get("jobName") + "','"+ record.get("errorId") + "','"+
                                        record.get("dateTime") + "','"+
                                        record.get("childName")+ "','"+ actName+'\')">确认 </a>'+

                                        '<a href="javascript:void(0);"  style="text-align: right" ' +
                                        'onclick="errorDelete(\''+record.get("jobName") + "','"+ record.get("errorId") + "','"+
                                        record.get("childName")+ "','"+ actName+'\')">删除</a>';
                                }else {
                                    oper = '<a href="javascript:void(0);"  ' +
                                        'onclick="retryRunB(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                                        record.get("errorId") + "','"+ jobStatus1 + "','"+ dateTime +"','"+ record.get("childName")+ "','"+ actName+'\')">重跑 </a>' +
                                        '<a href="javascript:void(0);"  style="text-align: right" ' +
                                        'onclick="skipB(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                                        record.get("errorId") + "','"+ jobStatus1 + "','"+ dateTime +"','"+ record.get("childName")+ "','"+ actName+'\')">跳过 </a>'+
                                        '<a href="javascript:void(0);"  ' +
                                        'onclick="CFMD(\''+record.get("jobName") + "','"+ record.get("flowId") + "','"+
                                        record.get("childName")+ "','"+ actName+'\')">已确认 </a>'+

                                        '<a href="javascript:void(0);"  style="text-align: right" ' +
                                        'onclick="errorDelete(\''+record.get("jobName") + "','"+ record.get("errorId") + "','"+
                                        record.get("childName")+ "','"+ actName+'\')">删除</a>';
                                }
                            }else {
                                nodeStatus = "x-tree-node-text-warning";
                                oper = '<a href="javascript:void(0);" ' +
                                    'onclick="retryRunB(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                                    record.get("errorId") + "','"+ jobStatus1 + "','"+ dateTime +"','" + record.get("childName")+ "','"+ actName+'\')">重跑  </a>' +

                                    '<a href="javascript:void(0);"   style="text-align: right" ' +
                                    'onclick="skipB(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                                    record.get("errorId") + "','"+ jobStatus1 + "','"+ dateTime +"','" + record.get("childName")+ "','"+ actName+'\')">跳过</a>';

                            }
                        }
                        // if (oper != "操作") {
                        //     var iwarnaffirm = record.get("iwarnaffirm");
                        //     if(0==iwarnaffirm){
                        //         oper = '<a href="javascript:void(0);" class="second-oper" ' +
                        //             'onclick="retryRun(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                        //             record.get("errorId") + "','"+ record.get("childName")+ "','"+ actName+'\')">重跑</a>' +
                        //
                        //             '<a href="javascript:void(0);" class="second-oper"  style="text-align: right" ' +
                        //             'onclick="skip(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                        //             record.get("errorId") + "','"+ record.get("childName")+ "','"+ actName+'\')">跳过</a>'+
                        //
                        //             '<a href="javascript:void(0);" class="second-oper" ' +
                        //             'onclick="verifyC(\''+record.get("jobName") + "','"+ record.get("errorId") + "','"+
                        //             record.get("childName")+ "','"+ actName+'\')">确认</a>'+
                        //
                        //             '<a href="javascript:void(0);" class="second-oper" style="text-align: right" ' +
                        //             'onclick="errorDelete(\''+record.get("jobName") + "','"+ record.get("errorId") + "','"+
                        //             record.get("childName")+ "','"+ actName+'\')">删除</a>';
                        //     }else {
                        //         oper = '<a href="javascript:void(0);" class="second-oper" ' +
                        //             'onclick="retryRun(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                        //             record.get("errorId") + "','"+ record.get("childName")+ "','"+ actName+'\')">重跑</a>' +
                        //
                        //             '<a href="javascript:void(0);" class="second-oper" style="text-align: right" ' +
                        //             'onclick="skip(\''+record.get("jobName") + "','"+ record.get("flowId") +"','"+
                        //             record.get("errorId") + "','"+ record.get("childName")+ "','"+ actName+'\')">跳过</a>'+
                        //
                        //             '<a href="javascript:void(0);" class="second-oper" ' +
                        //             'onclick="CFMD(\''+record.get("jobName") + "','"+ record.get("flowId") + "','"+
                        //             record.get("childName")+ "','"+ actName+'\')">已确认</a>'+
                        //
                        //             '<a href="javascript:void(0);" class="second-oper" style="text-align: right" ' +
                        //             'onclick="errorDelete(\''+record.get("jobName") + "','"+ record.get("errorId") + "','"+
                        //             record.get("childName")+ "','"+ actName+'\')">删除</a>';
                        //     }
                        // }
                        spanText1 = '<span class="x-tree-node-text x-tree-node-content ' + nodeStatus + '" style="width: '+halfWidthBigContent2+'px">' +
                            '<span class="x-tree-node-text-second" style="text-align: left;width: '+treeSecWidth+'px"> ' + childName + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: '+treeSecWidth+'px"> ' + dateTime + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: '+(treeSecWidth-12)+'px"> ' + jobStatus + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: '+(treeSecWidth-12)+'px"> ' + jobName + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: '+treeSecWidth+'px" > ' + createTime + '</span>' +
                            '<span class="second-oper-content" style="width: 140px"> ' + oper + '</span>' +
                            '</span>';
                        if  (level === "2" && childName != "子系统名"){
                            var tipCon = '<div>' +
                                '<div>子系统名：'+childName+'</div>' +
                                '<div>数据日期：'+dateTime+'</div>' +
                                '<div>作业状态：'+jobStatus+'</div>' +
                                '<div>报错节点：'+jobName+'</div>' +
                                '<div>报错时间：'+createTime+'</div>' +
                                '</div>';
                            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(tipCon) + '"';
                        }
                    }
                    return spanText1;
                }, listeners: { // add by lili_xing 2023/0614
                    click: function (a, b, c, d, e, record) {
                        var childName = record.get('childName');
                        var dateTime = record.get('dateTime');
                        var oper = record.get('oper');
                        if (oper != "操作") {
                            if (childName != null && childName!= "" && childName != '子系统名') {
                                Ext.getCmp('operateGridId').setValue(1);
                                Ext.getCmp('operateNameId').setValue(childName);
                                Ext.getCmp('operateTypeId').setValue(dateTime);
                            }
                        }
                    },
                    dblClick: function (a, b, c, d, e, record) {
                        var childName = record.get('childName');
                        var dateTime = record.get('dateTime');
                        var oper = record.get('oper');
                        if (oper != "操作") {
                            if (childName != null && childName!= "" && childName != '子系统名') {
                                Ext.getCmp('operateGridId').setValue(1);
                                Ext.getCmp('operateNameId').setValue(childName);
                                Ext.getCmp('operateTypeId').setValue(dateTime);
                            }
                        }
                    }
                }
            } ,{
                text : 'LEVEL',
                dataIndex : 'level',
                hidden : true
            }
        ]
    });

    bhErrorBatchTreeStoreBh.on('beforeload', function (store, options) {
        var operateName = Ext.getCmp('operateNameId').getValue();
        var operateType = Ext.getCmp('operateTypeId').getValue();
        var new_params = {
            childName: operateName,
            dateTime: operateType,
        };
        Ext.apply(bhErrorBatchTreeStoreBh.proxy.extraParams, new_params);
    });

    /* 报错批量栏tree列表-end */


    /* 超时批量栏tree列表-start */

    // 右侧列表
    Ext.define('bhTimeoutBatchTreeModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'childName',  //子系统
            type: 'string'
        }, {
            name: 'errorType', // 超时类型
            type: 'string'
        }, {
            name: 'level', // 等级
            type: 'string'
        }, {
            name: 'ichildname',
            type: 'string'
        },
            {
                name: 'ijobstatus',
                type: 'string',
            }, {
                name: 'ijobname',
                type: 'string'
            },
            {
                name: 'iflowid',
                type: 'string'
            },
            {
                name: 'idatetype',
                type: 'string'
            },
            {
                name: 'iactname',
                type: 'string'
            },
            {
                name: 'iflowname',
                type: 'string'
            },
            {
                name: 'warningTime',
                type: 'string'
            },
            {
                name: 'warnID',
                type: 'string'
            },
            {
                name: 'detailid',
                type: 'string'
            },
            {
                name: 'waringtype',
                type: 'long'
            }, {
                name: 'playringstate',
                type: 'long'
            }, {
                name: 'volflag', // 响铃
                type: 'string'
            }
            , {
                name: 'warndes', // 报错日志
                type: 'string'
            }
            , {
                name: 'idatatime', // 报错日志
                type: 'string'
            }, {
                name: 'idatetime', // 实例日期
                type: 'string'
            },{
                name: 'iflowinsname', // 实例日期
                type: 'string'
            }
        ]
    });

    bhTimeoutBatchTreeStoreBh = Ext.create('Ext.data.TreeStore', {
        autoLoad : true,
        model : 'bhTimeoutBatchTreeModel',
        proxy : {
            type : 'ajax',
            url : 'getBhOdsQueryTimeOutBatchTree.do'
        },
        root : {
            expanded : true
        },
        folderSort : false,
        listeners:{ // 超时批量栏响铃
            load :function(store){
                if (store.proxy.reader.jsonData.volflag) {
                    loadAlarmImgAndDataOut(true);
                }
                else {
                    loadAlarmImgAndDataOut(false);
                }
            }
        }
    });

    bhTimeoutBatchTreeBh = Ext.create('Ext.tree.Panel', {
        id:'bhTimeoutBatchTreeBh',
        height: customHeightModel,
        store: bhTimeoutBatchTreeStoreBh,
        expandLevel: 2,
        useArrows: true,
        rootVisible: false,
        multiSelect: false,    // 多选
        //singleExpand: true,   //单展开
        autoScroll: true,    // 自动滚屏
        //columns :gridColumns,
        width: '100%',
        columns: [
            {
                sortable: true,
                dataIndex: 'childName',
                xtype: 'treecolumn',
                width: '100%',
                expanded: true,
                text: '',
                renderer: function (data, metaData, record) {
                    // 子系统、数据日期、负责人、电话、报错数量，
                    var spanText = "";
                    var level = record.get("level");
                    if (level === "1") {
                        var nodeStatus = "";
                        var childName = record.get('childName');
                        var errorType = record.get('errorType');
                        var playringstate = record.get("playringstate");
                        if (errorType == 0) {
                            errorType = "未启动";
                        } else if (errorType == 1) {
                            errorType = "未结束";
                        } else if (errorType == 2) {
                            errorType = "执行超时";
                        }
                        if (playringstate == 0  && childName != "子系统名" ) { // 未确认
                            nodeStatus = "x-tree-node-text-warning";
                        }
                        spanText = '<span class="x-tree-node-text x-tree-node-content ' + nodeStatus + '" style="width: ' + halfWidthBigContent + 'px">' +
                            '<span  class="x-tree-node-text-first" style="text-align: left;width: ' + treeTimeoutFirWidth + 'px"> ' + childName + '</span>' +
                            '<span class="x-tree-node-text-first" style="text-align: left;width: ' + treeTimeoutFirWidth + 'px" > ' + errorType + '</span>' +
                            '</span>';
                    } else {
                        var nodeStatus = "";
                        var ichildname = record.get('ichildname');  //子系统名
                        var iflowid = record.get('iflowid');        //工作流ID
                        var idatetype = record.get('idatetype');    //日期类型
                        var ijobstatus = record.get('ijobstatus');  //作业状态
                        var playringstate = "告警时间";                  //状态
                        var ijobname = record.get('ijobname');      //报错节点
                        var ilog = record.get('ijobname');          //日志
                        var oper = record.get('oper');              //操作
                        var warndes = record.get('warndes');      //报错日志
                        var idataTime = record.get('idatatime').substr(0,10); // 实例名
                        var idatetime = record.get('idatetime');
                        if ((!record.get("waringtype") == 0) && (idatetime == null || idatetime == undefined || idatetime == '')) {
                            idataTime = record.get('iflowinsname');
                        }
                        if (idatetime !== null && idatetime !== undefined && idatetime !== '') {
                            idataTime = idatetime;
                        }
                        var type ;
                        if (idatetype !== null && idatetype !== undefined && idatetype !== '') {
                            type  = "作业级";
                        }else {
                            type = "批量级";
                        }
                        if (ijobstatus != "作业状态") {
                            var nodeNalue = "";
                            if (record.get("waringtype") == 0) {
                                nodeNalue = "未启动";
                            } else if (record.get("waringtype") == 1) {
                                nodeNalue = "未结束";
                            } else if (record.get("waringtype") == 2) {
                                nodeNalue = "执行超时";
                            }
                            ijobstatus = "<a href=javascript:void(0);' " +
                                "onclick=\"timeOutBatchStatu('"
                                + record.get("ijobname") + "','" + record.get("iflowid") + "','"+ record.get("warnID") + "','" + record.get("waringtype") + "','" +record.get("idatetype") +"','" +record.get("warndes") +"','" +record.get("iactname") + "','" +record.get("iflowname") + "','" + record.get("ichildname")+ "','" + record.get("warningTime") + "');\"><span>" + nodeNalue + "</span></a>";
                        }
                        var date = CurentTime(record.get("warningTime"));
                        var day = date.substring(0,10);
                        var time =date.substring(11,date.length);

                        if (ijobname != "报错节点") {
                            ijobname = "<a href=javascript:void(0);' " +
                                "onclick=\"timeOutBatchxx('"
                                + record.get("ijobname") + "','" + record.get("iflowid") + "','"+ record.get("warnID") + "','" + record.get("waringtype") + "','" +record.get("idatetype") +"','" +record.get("warndes") +"','" +record.get("iactname") + "','" +record.get("iflowname") + "','" + record.get("ichildname") + "');\"><span>" + record.get("ijobname") + "</span></a>";
                        }
                        if (oper != "操作") {
                            if (0 == record.get("playringstate")) {
                                oper = '<a href="javascript:void(0);" onclick="confirmDo(\'' + record.get("ichildname") + "','" + record.get("warnID") + "','" + record.get("iactname") + "','" + record.get("iflowname") + "','" + idataTime + "','" + record.get("detailid") + "','" + record.get("waringtype") + '\')">确认';
                            } else {
                                oper = '<a href="javascript:void(0);" onclick="confirmDoY(\'' + record.get("ichildname") + "','" + record.get("warnID") + "','" + record.get("iactname") + "','" + record.get("iflowname") + "','" + record.get("warningTime") + "','" + record.get("detailid") + "','" + record.get("waringtype") + '\')">已确认';
                            }
                        }
                        if (record.get("playringstate") == 22) {
                            oper = "操作";
                            day = "告警日期";
                            time = "告警时间";
                            type = "告警类型";
                        }
                        spanText = '<span class="x-tree-node-text x-tree-node-content ' + nodeStatus + ' " style="width: ' + halfWidthBigContent2 + 'px">' +
                            '<span class="x-tree-node-text-second" style="text-align: left;width: ' + treeSecTimeOutWidth + 'px"> ' + ichildname + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: ' + (treeSecTimeOutWidth-8) + 'px"> ' + ijobname + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: ' + (treeSecTimeOutWidth+16) + 'px"> ' + type+ '</span>' +
                            '<span class="x-tree-node-text-second" style="width: ' + (treeSecTimeOutWidth-16) + 'px"> ' + ijobstatus + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: ' + (treeSecTimeOutWidth-16) + 'px" > ' + idataTime + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: ' + (treeSecTimeOutWidth-8) + 'px" > ' + day + '</span>' +
                            '<span class="x-tree-node-text-second" style="width: ' + (treeSecTimeOutWidth-8) + 'px" > ' + time + '</span>' +
                            '<span class="second-oper-content" style="width: '+(treeSecTimeOutWidth+30)+'" > ' + oper + '</span>' +
                            '</span>';
                        if  (level === "2" || ichildname != "子系统名称"){
                            var tipCon = '<div>' +
                                '<div>子系统名称：'+ichildname+'</div>' +
                                '<div>报错节点：'+ijobname+'</div>' +
                                '<div>告警类型：'+type+'</div>' +
                                '<div>作业状态：'+ijobstatus+'</div>' +
                                '<div>数据日期：'+idataTime+'</div>' +
                                '<div>告警日期：'+day+'</div>' +
                                '<div>告警时间：'+time+'</div>' +
                                '</div>';
                            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(tipCon) + '"';
                        }

                    }
                    return spanText;
                },
                listeners: { // add by lili_xing 2023/0614
                    click: function (a, b, c, d, e, record) {
                        var ichildname = record.get('ichildname');
                        var errorType = record.get('waringtype');
                        var oper = record.get('oper');
                        if (oper != "操作") {
                            if (ichildname != null && ichildname!= "" && ichildname != '子系统名称') {
                                Ext.getCmp('operateGridId').setValue(2);
                                Ext.getCmp('operateNameId').setValue(ichildname);
                                Ext.getCmp('operateTypeId').setValue(errorType);
                            }
                        }
                    },
                    dblClick: function (a, b, c, d, e, record) {
                        var ichildname = record.get('ichildname');
                        var errorType = record.get('waringtype');
                        var oper = record.get('oper');
                        if (oper != "操作") {
                            if (ichildname != null && ichildname!= "" && ichildname != '子系统名称') {
                                Ext.getCmp('operateGridId').setValue(2);
                                Ext.getCmp('operateNameId').setValue(ichildname);
                                Ext.getCmp('operateTypeId').setValue(errorType);
                            }
                        }
                    }
                }
            }
        ]
    });

    bhTimeoutBatchTreeStoreBh.on('beforeload', function (store, options) {
        var operateName = Ext.getCmp('operateNameId').getValue();
        var operateType = Ext.getCmp('operateTypeId').getValue();
        var new_params = {
            childName: operateName,
            errorType: operateType,
        };
        Ext.apply(bhTimeoutBatchTreeStoreBh.proxy.extraParams, new_params);
    });
    /* 超时批量栏tree列表-end */

    // 延时批量model
    Ext.define ('delayedBatchModel', {
        extend : 'Ext.data.Model',
        fields : [
            {
                name : 'iid',
                type : 'string'
            },
            {
                name : 'projectName',
                type : 'string'
            },{
                name : 'childName',
                type : 'string'
            }, {
                name : 'loginUser',
                type : 'string'
            },{
                name : 'createTimeStr',
                type : 'string'
            }, {
                name : 'department',
                type : 'string'
            }, {
                name : 'shouldEndTimeStr',
                type : 'string'
            },{
                name : 'createUser',
                type : 'string'
            }, {
                name : 'checkUser',
                type : 'string',
                hidden:true
            }
        ]
    });
    // 运行批量model
    Ext.define('odsStartBatchModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'childName',  //子系统
            type: 'string'
        }, {
            name: 'dateTime', // 数据日期
            type: 'string'
        }, {
            name: 'createTime', // 报错时间
            type: 'string'
        }, {
            name: 'jobStatus', // 状态 SUCCESS, START, FAIL
            type: 'string'
        },{
            name: 'jobName', // 状态 SUCCESS, START, FAIL
            type: 'string'
        },{
            name: 'actName', // 作业名
            type: 'string'
        },  {
            name: 'flowId', // flowId
            type: 'long'
        }, {
            name: 'baseName', // 作业源
            type: 'string'
        },{
            name: 'id', // errorId
            type: 'long'
        },{
            name: 'operation', // 操作
            type: 'string'
        }]
    });
    // 手工批量model
    Ext.define('manualBatchModel',{
        extend : 'Ext.data.Model',
        fields :[   {
            name : 'iid',
            type : 'string'
        },{
            name : 'iname',
            type : 'string'
        },{
            name : 'iflowname',
            type : 'string'
        },{
            name : 'iflowinsname',
            type : 'string'
        },{
            name : 'iactname',
            type : 'string'
        },{
            name : 'idesc',
            type : 'string'
        },{
            name : 'begintime',
            type : 'string'
        },{
            name : 'iactid',
            type : 'string'
        },{
            name : 'iflowid',
            type : 'string'
        },{
            name : 'itaskid',
            type : 'string'
        },{
            name : 'icheckstate',
            type : 'long'
        }
        ]
    });

    // 报错批量model
    Ext.define('errorBatchModel',{
        extend : 'Ext.data.Model',
        fields :[ {
            name : 'ichildname',
            type : 'string'
        },
            {
                name : 'ijobstatus',
                type : 'string',
            },{
                name : 'ijobname',
                type : 'string'
            },
            {
                name : 'iflowid',
                type : 'string'
            },
            {
                name: "iactname",
                type: 'string'
            },
            {
                name: "iwarnaffirm",
                type: 'string'
            },
            {
                name: "iid",
                type: 'string'
            }
        ]
    });
    // 超时批量model
    Ext.define('timeoutBatchModel',{
        extend : 'Ext.data.Model',
        fields :[
            {
                name : 'ichildname',
                type : 'string'
            },
            {
                name : 'ijobstatus',
                type : 'string',
            },{
                name : 'ijobname',
                type : 'string'
            },
            {
                name : 'iflowid',
                type : 'string'
            },
            {
                name : 'idatetype',
                type : 'string'
            },
            {
                name : 'iactname',
                type : 'string'
            },
            {
                name : 'iflowname',
                type : 'string'
            },
            {
                name : 'warningTime',
                type : 'string'
            },
            {
                name : 'warnID',
                type : 'string'
            },
            {
                name : 'detailid',
                type : 'string'
            },
            {
                name : 'waringtype',
                type : 'long'
            },{
                name : 'playringstate',
                type : 'long'
            }
        ]
    });
    // 延时批量 store
    delayedBatchStoreBh = Ext.create ('Ext.data.Store',
        {
            model: 'delayedBatchModel',
            pageSize : 20,
            autoLoad : true,
            proxy :
                {
                    type : 'ajax',
                    url : 'queryDelayStart.do',
                    reader :
                        {
                            type : 'json',
                            root : 'dataList',
                            totalProperty : 'total'
                        }
                }
        });
    // 手工批量 store
    manualBatchStoreBh = Ext.create ('Ext.data.Store',
        {

            model: 'manualBatchModel',
            autoLoad : true,
            pageSize : 30,
            proxy :
                {
                    type : 'ajax',
                    url : 'bhOdsUserTaskBatch.do',
                    // url : '',
                    reader :
                        {
                            type : 'json',
                            root : 'dataList',
                            totalProperty : 'total'
                        }
                },

            listeners:{ // 超时批量栏响铃
                load :function(store){
                    if (store.proxy.reader.jsonData.volflag) {
                        loadAlarmImgAndUserTaskBh(true);
                    }
                    else {
                        loadAlarmImgAndUserTaskBh(false);
                    }
                }
            }
        });
    // 运行批量store
    bhOdsStartBatchStoreBh = Ext.create ('Ext.data.Store',
        {

            model: 'odsStartBatchModel',
            autoLoad : true,
            pageSize : 30,
            proxy :
                {
                    type : 'ajax',
                    url : 'bhOdsStartBatch.do',
                    // url : '',
                    reader :
                        {
                            type : 'json',
                            root : 'dataList',
                            totalProperty : 'total'
                        }
                }
        });

    // 延时批量字段
    var delayedBatchColumns = [
        // {
        //     text : '序号',
        //     width : 35,
        //     xtype : 'rownumberer'
        // },
        {
            text : '工程名称',
            dataIndex : 'projectName',
            width : 200
        },{
            text : '批量名称',
            dataIndex : 'childName',
            width : 150
        }, {
            text : '操作用户',
            dataIndex : 'loginUser',
            // flex : 1
            width : 150
        },{
            text : '操作时间',
            dataIndex : 'createTimeStr',
            // flex : 1
            width : 150
        },{

            text : '申请人',
            dataIndex : 'createUser',
            // flex : 1
            width : 150
        },{
            text : '申请分行/部门',
            dataIndex : 'department',
            // flex : 1
            width : 150
        },{
            text : '正常起批时间',
            dataIndex : 'shouldEndTimeStr',
            // flex : 1
            width : 200
        },{
            text : '取消延迟信息',
            dataIndex : 'check',
            // flex : 1,
            width : 200,
            renderer: function(value,metaData,record){
                return '<a href="javascript:void(0);" onclick="delayCheck(\''+record.get('loginUser')+"','"+record.get('checkUser')+"','"+record.get('createTimeStr') +'\')">查看</a>';
            }}
    ];



    // 运行批量字段
    var bhOdsStartBatchColumns = [
        // {
        //     text : '序号',
        //     width : 35,
        //     xtype : 'rownumberer'
        // },
        {
            text : '作业源',
            dataIndex : 'baseName',
            // flex : 1
            width : 70
        },
        {
            text : '子系统名',
            dataIndex : 'childName',
            // flex : 1
            width : colMinWidth
        },{
            text : '子步骤名',
            dataIndex : 'jobName',
            // flex : 1
            width : colMinWidth
        },{
            text : '数据日期',
            dataIndex : 'dateTime',
            // flex : 1,
            width : colMinWidth
        },{
            text : '作业名称',
            dataIndex : 'actName',
            // flex : 1
            width : colMinWidth
        },{
            text : '作业状态',
            dataIndex : 'jobStatus',
            // flex : 1,
            width : colMinWidth,
            // renderer:function(value,p,record) {
            //     console.log(value)
            //     if (value == 'null') {
            //         var str = "";
            //         return str;
            //     }else {
            //         return value;
            //     }
            // }
        },{
            text : '采集时间',
            dataIndex : 'createTime',
            // flex : 1
            width : colMinWidth+8
        },{
            text : '操作',
            dataIndex : 'operation',
            hidden : true,
            width : colMinWidth-8,
            renderer: function(value,metaData,record){
                console.log("baseName1:"+record.get('baseName'));
                console.log("edwOne:"+edwOne);
                if (edwOne == record.get('baseName') || edwTwo == record.get('baseName') ) {
                    if (record.get('jobStatus') == "STOP" ||record.get('jobStatus') == "stop" ){
                        return '暂停'+'<a href="javascript:void(0);" onclick="dealExceptionPauseBh(\''+record.get('id')+"','"+record.get('baseName')+"','"+record.get('childName')+"','"+record.get('actName')+"','"+record.get('jobName')+"','"+ 2 +"','"+record.get('dateTime') +'\')"> 恢复</a>' ;
                    }else {
                        return '<a href="javascript:void(0);" onclick="dealExceptionPauseBh(\''+record.get('id')+"','"+record.get('baseName')+"','"+record.get('childName')+"','"+record.get('actName')+"','"+record.get('jobName')+"','"+ 1 +"','"+record.get('dateTime') +'\')">暂停 </a>' + '恢复';
                    }
                } else {
                    if (record.get('jobStatus') == "STOP" ||record.get('jobStatus') == "stop" ){
                        return '暂停'+'<a href="javascript:void(0);" onclick="dealExceptionPauseSimbatBh(\''+record.get('id')+"','"+record.get('baseName')+"','"+record.get('childName')+"','"+record.get('actName')+"','"+record.get('jobName')+"','"+ 2 +"','"+record.get('dateTime') +'\')"> 恢复</a>' ;
                    }else {
                        return '<a href="javascript:void(0);" onclick="dealExceptionPauseSimbatBh(\''+record.get('id')+"','"+record.get('baseName')+"','"+record.get('childName')+"','"+record.get('actName')+"','"+record.get('jobName')+"','"+ 1 +"','"+record.get('dateTime') +'\')">暂停 </a>' + '恢复';
        }
                }

            }}
    ];

    // 手工批量字段
    var manualBatchColumns = [
        // {
        //     text : '序号',
        //     width : 35,
        //     xtype : 'rownumberer'
        // },
        {
            text : '工程名称',
            dataIndex : 'iname',
            // flex : 1
            width : colMaxWidth
        },{
            text : '工作流名称',
            dataIndex : 'iflowname',
            // flex : 1
            width : colMaxWidth
        },{
            text : '实例名称',
            dataIndex : 'iflowinsname',
            // flex : 1
            width : colMaxWidth
        },{
            text : '操作',
            dataIndex : 'icheckstate',
            // flex : 1,
            width : colMaxWidth-8,
            renderer:function(value, p, record){
                if (record.get("icheckstate") == 0) {
                    return "<a href=javascript:void(0);' valign=\"middle\" onclick=\"delInfoUserTaskOneBh('" +record.get("iactid") + "','"+ record.get("iflowid") +"');\"><span>开始</span></a>";
                }else {
                    return "开始";
                }
            },
        },{
            text : '活动名称',
            dataIndex : 'iactname',
            // flex : 1,
            width : colMaxWidth-8,
            renderer:function(value, p, record){
                if (record.get("iactname") == "UserTask") {
                    return "<a href=javascript:void(0);' valign=\"middle\" onclick=\"manualConfirmBh('"
                        +record.get("iactid") + "','"+ record.get("iflowid") + "','"+ record.get("itaskid")+ "','"+ record.get("iname")+ "','"+ record.get("iflowname")+ "','"+ record.get("iflowinsname")+"');\"><span>结束</span></a>";
                }else {
                    return record.get("iactname");
                }
            },
        },{
            text : '活动描述',
            dataIndex : 'idesc',
            // flex : 1,
            width : colMinWidth,
            renderer:function(value,p,record) {
                if (value == 'null') {
                    var str = "";
                    return str;
                }else {
                    return value;
                }
            }
        }
        ,{
            text : '开始时间',
            dataIndex : 'begintime',
            // flex : 1
            width : colMinWidth
        }
    ];

    // 延时批量 grid
    var delayedBatchGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
        ipageSize : 30,
        width:"98%",
        autoScroll: false,
        bodyStyle: 'overflow-x:hidden; overflow-y:hidden',
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        cls:'customize_panel_back panel_space_top_bottom panel_space_left_right',
        store : delayedBatchStoreBh,
        // selModel : selModel,
        columns : delayedBatchColumns,
        height:customHeightGrid
    });
    // 运行批量栏
    var bhOdsStartBatchGridBig = Ext.create('Ext.ux.ideal.grid.Panel', {
        id:'bhOdsStartBatchGridBig',
        ipageSize : 30,
        autoScroll: false,
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        bodyStyle: 'overflow-x:hidden; overflow-y:hidden',
        //  cls:'customize_panel_back panel_space_top_bottom panel_space_left_right',
        store : bhOdsStartBatchStoreBh,
        // selModel : selModel,
        columns : bhOdsStartBatchColumns,
        height:customHeightGrid
    });
    // 手工批量 grid
    var manualBatchGridBig = Ext.create('Ext.ux.ideal.grid.Panel', {
        id:'manualBatchGridBig',
        ipageSize : 30,
        autoScroll: false,
        ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        bodyStyle: 'overflow-x:hidden; overflow-y:hidden',
        cls:'customize_panel_back panel_space_top_bottom panel_space_left_right',
        store : manualBatchStoreBh,
        // selModel : selModel,
        columns : manualBatchColumns,
        height:customHeightGrid
    });

    // 顶部刷新 + 取消响铃 Form
    utHomeFormBh = Ext.create('Ext.form.Panel', {
        id:'utHomeFormBh_div',
        region : 'north',
        layout : 'anchor',
        height:40,
        buttonAlign : 'center',
        bodyPadding : 5,
        border : false,
        baseCls:'customize_gray_back',
        hidden:true,
        items: [{
            layout:'column',
            anchor:'95%',
//		    	padding : '0 0 5 0',
            border : false,
            items: [
                {
                    fieldLabel: '刷新时间(分钟)',
                    labelAlign : 'right',
                    labelWidth : 100,
                    name: 'refreshTime',
                    regex: /^\d+$/,
                    regexText: "请输入正整数数字！",
                    allowBlank : false,
                    value: '1',
                    columnWidth:.15,
                    xtype: 'textfield'
                },{
                    xtype : 'button',
                    margin : '0 0 0 5',
//					scale: 'medium',
                    text : '刷新',
                    handler : queryWhereBig
                },operateTablePlugin,operatePlugin,operateTypePlugin,operateMouseErrorTopBig,operateMouseTimeOutTopBig]
        }]
    });
    //延时批量panel
    panelMiddleBh = Ext.create('Ext.panel.Panel', {
        id:'panelMiddleBh_div',
        title: '延时批量栏',
        bodyCls: 'x-docked-noborder-top',
        bodyStyle : 'overflow-x:hidden; overflow-y:hidden',
        cls: 'customize_panel_back   panel_space_left panel_space_right',
        border: true,
        region: 'south',
        // autoScroll: true,
        width: '90%',
        height: '30%',
        margin:"5 0 0 0",
        //    collapsible : true,//可收缩
        items: [delayedBatchGrid]
    });
    // 报错批量panel
    panelTopLeftBh = Ext.create('Ext.panel.Panel', {
        id:'panelTopLeftBh_div',
        title: '报错批量栏',
        bodyCls: 'x-docked-noborder-top',
        bodyStyle : 'overflow-x:hidden; overflow-y:hidden',
        cls: 'customize_panel_back',
        region: 'center',
        border: true,
        // autoScroll: true,
        margin:"5 3 5 0",
        // height : customHeightBh,
        items: [bhErrorBatchTreeBh]
    });
    //超时批量panel
    panelTopRightBh = Ext.create('Ext.panel.Panel', {
        id:'panelTopRightBh_div',
        title: '超时批量栏',
        bodyCls: 'x-docked-noborder-top',
        bodyStyle : 'overflow-x:hidden; overflow-y:hidden',
        cls: 'customize_panel_back   panel_space_left panel_space_right',
        border: true,
        region: 'north',
        // autoScroll: true,
        width: '100%',
        margin:"5 8 0 0",
        left:800,
        // collapsible : true,//可收缩
        items: [/*oam_alarmPaneloutTime,*/
            /*{
            xtype: 'button',
            text: '处理',
            cls:'Common_Btn',
            titleAlign : 'right',
            handler: function() {
                delInfoCs();
            }
        },*/bhTimeoutBatchTreeBh]
    });
    //手工批量panel
    panelBottomRightBh = Ext.create('Ext.panel.Panel', {
        id:'panelBottomRightBh_div',
        title: '手工批量栏',
        bodyCls: 'x-docked-noborder-top',
        bodyStyle : 'overflow-x:hidden; overflow-y:hidden',
        cls: 'customize_panel_back   panel_space_left panel_space_right',
        margin:"5 0 0 0",
        border: true,
        region: 'south',
        // autoScroll: true,
        width: '90%',
        height: '30%',
        //   collapsible : true,//可收缩
        items: [manualBatchGridBig]
    });

    //手工批量panel
    panelBottomLeftBh = Ext.create('Ext.panel.Panel', {
        id:'panelBottomLeftBh_div',
        title: '运行批量栏',
        bodyCls: 'x-docked-noborder-top',
        bodyStyle : 'overflow-x:hidden; overflow-y:hidden',
        cls: 'customize_panel_back   panel_space_left panel_space_right',
        margin:"5 0 0 0",
        border: true,
        region: 'south',
        // autoScroll: true,
        width: '90%',
        height: '30%',
        //    collapsible : true,//可收缩
        items: [bhOdsStartBatchGridBig]
    });
    if (panelNo === "1") {
        bhBigMiddlePanel = panelTopLeftBh;
    } else if (panelNo === "2"){
        bhBigMiddlePanel = panelTopRightBh;
    } else if (panelNo === "3"){
        bhBigMiddlePanel = panelMiddleBh;
    } else if (panelNo === "4"){
        bhBigMiddlePanel = panelBottomLeftBh;
    } else if (panelNo === "5"){
        bhBigMiddlePanel = panelBottomRightBh;
    }
    setPanelWidthHeight(); // 设置容器页面外观
    var anchorPanel = Ext.create ("Ext.panel.Panel", {
        layout: 'anchor',
        width: halfWidthBig,
        left:18,
        top:18,
        region: 'center',
        items: [
            {
                layout: {
                    type: 'hbox',
                },
                items: [utHomeFormBh]
            },
            {
                layout: {
                    type: 'hbox'
                },
                items: [bhBigMiddlePanel]
            }
        ]
    });
    var mainPanelBh = Ext.create ("Ext.panel.Panel", {
        renderTo: 'batchmonitorin_divBig',
        layout: {
            type: 'vbox',
            align: 'stretch' //拉伸使其充满整个父容器
        },
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        items: [anchorPanel]
    })

    contentPanel.on ('resize', function () {
        mainPanelBh.setWidth(contentPanel.getWidth());
        mainPanelBh.setHeight(contentPanel.getHeight()-modelHeigth);
    });

    // 预加载，对请求URL赋参
    delayedBatchStoreBh.on ('beforeload', function (s)
    {

        var params = s.getProxy ().extraParams;
        Ext.apply (params,
            {
                isSelect : false
            });
    });
    delayedBatchStoreBh.on ('load', function (r, options, success)
    {
        // Ext.MessageBox.hide();
    });
//防止定时器重复创建
    if (refreshObj) {
        clearInterval(refreshObj);
    }
    refreshObj = setInterval(utHomequery, 60 * 1000);
    monitorStartAutoFreshAlarm();

    // refreshObj = setInterval(utHomequery, 60*1000);
    // 设置容器页面外观
    function setPanelWidthHeight(){
        Ext.create('Ext.Button', {
            renderTo: Ext.getElementById('panelMiddleBh_zoomIn'),
            text: 'My Button',
            listeners: {
                click: function() {
                    Ext.MessageBox.alert('Alert box', 'Button is clicked');
                }
            }
        });
        panelTopLeftBh.setWidth(halfWidthBig);
        panelTopLeftBh.setHeight(customHeightModel);
        panelTopRightBh.setWidth(halfWidthBig);
        panelTopRightBh.setHeight(customHeightModel);
        panelMiddleBh.setWidth(halfWidthBig);
        panelMiddleBh.setHeight(customHeightModel);
        panelBottomLeftBh.setWidth(halfWidthBig);
        panelBottomLeftBh.setHeight(customHeightModel);
        panelBottomRightBh.setWidth(halfWidthBig);
        panelBottomRightBh.setHeight(customHeightModel);
        panelMiddleBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_delay batch_monitoring_common'/><span class='tit_box_text'>延时批量栏 </span><a href='javascript:void(0)' onclick='panelExpandClose(3)' ><img  src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_out batch_monitoring_common_zoom'/></a>");
        panelBottomLeftBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_run batch_monitoring_common'/><span class='tit_box_text'>运行批量栏 </span><a href='javascript:void(0)' onclick='panelExpandClose(4)' ><img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_out batch_monitoring_common_zoom'/></a>");
        panelBottomRightBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_edit batch_monitoring_common'/><span class='tit_box_text'>手工批量栏 </span><a href='javascript:void(0)' onclick='panelExpandClose(5)' ><img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_out batch_monitoring_common_zoom'/></a>");


    }
    getScrollListeners();
});


/*刷新方法*/
function utHomequery(){
    console.log('批量监控看板自动刷新时间'+Date.now());
    delayedBatchStoreBh.reload();
    bhErrorBatchTreeStoreBh.reload();
    bhTimeoutBatchTreeStoreBh.reload();
    bhOdsStartBatchStoreBh.reload();
    manualBatchStoreBh.reload();
    treeBeforeLoadBig();/*刷新方法*/
}

// 刷新方法 定义
function queryWhereBig() {
    if(refreshObj){
        clearInterval(refreshObj);
    }
    var refreshTime = utHomeFormBh.getForm().findField("refreshTime").getValue();
    if(!isNumber(refreshTime)){
        setMessage("请正确填写刷新时间！");
        return;
    }
    refreshObj = setInterval(utHomequery, parseInt(refreshTime)*60*1000);
    delayedBatchStoreBh.reload();
    bhErrorBatchTreeStoreBh.reload();
    bhTimeoutBatchTreeStoreBh.reload();
    bhOdsStartBatchStoreBh.reload();
    manualBatchStoreBh.reload();
    treeBeforeLoadBig();/*刷新方法*/
}
function monitorStartAutoFreshAlarm() {
    // 自动刷新查询报警方法
    var refreshTime = utHomeFormBh.getForm().findField("refreshTime").getValue();
    monitorAutoFresh_alarm(refreshTime);
}
function monitorAutoFresh_alarm(t) {
    //actMonitorLoadAlarmImgAndData();//test.暂时注释掉
    // 验证是否还在活动监控页面，如果不在，则取消页面的自动刷新功能
    if (contentPanel.title == '批量监控看板') {
        monitorAutoFreshTimeBh = setTimeout("monitorAutoFresh_alarm(" + t + ")", 1000 * t * 60); // 以分为单位
    } else {
        actMonitorStopAutoFresh_alarm();
        if (Ext.isIE) {
            CollectGarbage();
        }
    }
}
function actMonitorStopAutoFresh_alarm() {
    clearTimeout(monitorAutoFreshTimeBh);
}
// 重跑
function retryRunB(ijobname, iflowid,iid, jobStatus,dateTime,ichildname, iactname) {
    console.log("重跑点击进来了~~~")
    var actId= "";
    var errorTaskId = "";
    var childNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '子系统名称',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidthBh * 0.5,
            id: 'childNameAddSec',
            name: 'childNameAddSec',
            fieldCls : 'x-form-item-label',
            value:ichildname
        });
    var jobNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '子步骤名称',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidth * 0.5,
            id: 'jobNameAddSec',
            name: 'jobNameAddSec',
            fieldCls : 'x-form-item-label',
            value:ijobname
        });
    var type = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '操作类型',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidthBh * 0.5,
            id: 'mainNameAddSec',
            name: 'mainNameAddSec',
            fieldCls : 'x-form-item-label',
            value:"重跑"
        });
    var userIdentityForm  = Ext.create('Ext.form.Panel', {
        border: false,
        width: '100%',
        layout: 'anchor',
        region: 'center',
        collapsible : false,
        dockedItems : [
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [childNameAddCom ]
            }, {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [jobNameAddCom ]
            },
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [ type  ]
            },
            {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '用户名',
                    id:'checkUser',
                    name:'checkUser',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                }]
            }, {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '密码',
                    id:'password',
                    name:'password',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                    inputType: 'password'
                }]
            },
            {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '申请人',
                    id:'applicant',
                    name:'applicant',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                }]
            }]
    });


    var userIdentityPanel = Ext.create('Ext.panel.Panel', {
        layout : 'border',
        border : true,
        region : 'center',
        //renderTo:"top_userinfo_area",
        items : [
            userIdentityForm
        ]
    });
    // 请求errortaskid 和 actid
    Ext.Ajax.request({
        url : 'queryErrorTaskId.do',
        async: false,
        params : {
            flowId: iflowid,
            iactName:iactname,
        },
        success : function(response, opts) {
            actId = Ext.decode(response.responseText).actId;
            errorTaskId = Ext.decode(response.responseText).errorTaskId;
        },
        failure : function(response, opts) {
        }
    });

    var userIdentityWin = Ext.create ('Ext.window.Window',
        {
            title : '复核信息',
            modal : true,
            closeAction : 'destroy',
            buttonAlign : 'center',
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            items : [userIdentityPanel],
            width : winDoubleCheckWidthBh-20,
            height : winDoubleCheckWidthBh-120,
            buttons :[{
                xtype: "button",
                text: "确定",
                id: "rsButton",
                handler: function () {
                    var form_checkUser = userIdentityForm.getForm ().findField ("checkUser").getRawValue();
                    var form_password = userIdentityForm.getForm ().findField ("password").getRawValue();
                    var form_applicant = userIdentityForm.getForm ().findField ("applicant").getRawValue();
                    if (trim (form_checkUser) == '' || (null == form_checkUser))
                    {
                        Ext.MessageBox.alert ("提示", "请输入用户名");
                        return false;
                    }
                    if (trim (form_password) == '' || (null == form_password))
                    {
                        Ext.MessageBox.alert ("提示", "请输入密码");
                        return false;
                    }
                    Ext.getCmp("rsButton").disabled='disabled';
                    Ext.Msg.confirm("提示",'确认重跑?', function(btn){
                        if (btn == 'yes'){ // 提交数据
                            Ext.Ajax.request({
                                url : 'mqJobSchedulingRepeat.do',
                                params : {
                                    iid : iid,
                                    flowId : iflowid, // 工作流ID
                                    childName : ichildname, 	// 子系统名
                                    execModel : 1, 	// 1-重跑 2-略过
                                    jobName: ijobname, //子步骤名
                                    actName: iactname, //活动名
                                    actId:actId, // 活动ID
                                    jobStatus:jobStatus,
                                    dateTime:dateTime,
                                    user:form_checkUser, //用户名
                                    passWord:form_password, // 密码
                                    applicant:form_applicant
                                },
                                success : function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if(success){
                                        setMessage(message);
                                        userIdentityWin.close();
                                        treeBeforeLoadBig();
                                    } else {
                                        if (message==='审核用户不存在或密码错误！' ||message==='审核用户已被锁定！') {
                                            setMessage(message);
                                            Ext.getCmp("rsButton").enable();
                                            return;
                                        } else {
                                            Ext.Msg.alert ('提示', message, function (){
                                                userIdentityWin.close();
                                                treeBeforeLoadBig();
                                            });
                                        }
                                    }
                                },
                                failure : function(result, request) {
                                    Ext.Msg.alert('提示', '保存失败, 请联系管理员!');
                                }
                            });
                        }
                    });
                }
            },{
                xtype: "button",
                text: "取消",
                handler: function () {
                    userIdentityForm.getForm().reset();
                    userIdentityWin.close();
                }
            }]
        }).show();
    userIdentityWin.center();
}

//略过
function skipB(ijobname, iflowid, iid,jobStatus,dateTime,ichildname, iactname) {
    console.log("略过进来了~~~")
    var actId= "";
    var errorTaskId = "";
    var childNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '子系统名称',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidthBh * 0.5,
            id: 'childNameAddSec',
            name: 'childNameAddSec',
            fieldCls : 'x-form-item-label',
            value:ichildname
        });
    var jobNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '子步骤名称',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidth * 0.5,
            id: 'jobNameAddSec',
            name: 'jobNameAddSec',
            fieldCls : 'x-form-item-label',
            value:ijobname
        });
    var type = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '操作类型',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidthBh * 0.5,
            id: 'mainNameAddSec',
            name: 'mainNameAddSec',
            fieldCls : 'x-form-item-label',
            value:"跳过"
        });
    var userIdentityForm  = Ext.create('Ext.form.Panel', {
        border: false,
        width: '100%',
        layout: 'anchor',
        region: 'center',
        collapsible : false,
        dockedItems : [
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [childNameAddCom ]
            }, {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [jobNameAddCom ]
            },
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [ type  ]
            },
            {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '用户名',
                    id:'checkUser',
                    name:'checkUser',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                }]
            }, {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '密码',
                    id:'password',
                    name:'password',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                    inputType: 'password'
                }]
            },
            {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '申请人',
                    id:'applicant',
                    name:'applicant',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                }]
            }]
    });
    var userIdentityPanel = Ext.create('Ext.panel.Panel', {
        layout : 'border',
        border : true,
        region : 'center',
        //renderTo:"top_userinfo_area",
        items : [
            userIdentityForm
        ]
    });
    // 请求errortaskid 和 actid
    Ext.Ajax.request({
        url : 'queryErrorTaskId.do',
        async: false,
        params : {
            flowId: iflowid,
            iactName:iactname,
        },
        success : function(response, opts) {
            actId = Ext.decode(response.responseText).actId;
            errorTaskId = Ext.decode(response.responseText).errorTaskId;
        },
        failure : function(response, opts) {
        }
    });

    var userIdentityWin = Ext.create ('Ext.window.Window',
        {
            title : '复核信息',
            modal : true,
            closeAction : 'destroy',
            buttonAlign : 'center',
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            items : [userIdentityPanel],
            width : winDoubleCheckWidthBh-20,
            height : winDoubleCheckWidthBh-120,
            buttons :[{
                xtype: "button",
                text: "确定",
                id: "rsButton",
                handler: function () {
                    var form_checkUser = userIdentityForm.getForm ().findField ("checkUser").getRawValue();
                    var form_password = userIdentityForm.getForm ().findField ("password").getRawValue();
                    var form_applicant = userIdentityForm.getForm ().findField ("applicant").getRawValue();
                    if (trim (form_checkUser) == '' || (null == form_checkUser))
                    {
                        Ext.MessageBox.alert ("提示", "请输入用户名");
                        return false;
                    }
                    if (trim (form_password) == '' || (null == form_password))
                    {
                        Ext.MessageBox.alert ("提示", "请输入密码");
                        return false;
                    }
                    Ext.getCmp("rsButton").disabled='disabled';
                    Ext.Msg.confirm("提示",'确认略过?', function(btn){
                        if (btn == 'yes'){ // 提交数据
                            Ext.Ajax.request({
                                url : 'mqJobSchedulingRepeat.do',
                                params : {
                                    iid : iid,
                                    flowId : iflowid, // 工作流ID
                                    childName : ichildname, 	// 子系统名
                                    execModel : 2, 	// 1-重跑 2-略过
                                    jobName: ijobname, //子步骤名
                                    actName: iactname, //活动名
                                    actId:actId, // 活动ID
                                    jobStatus:jobStatus,
                                    dateTime:dateTime,
                                    user:form_checkUser, //用户名
                                    passWord:form_password, // 密码
                                    applicant:form_applicant
                                },
                                success : function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if(success){
                                        setMessage(message);
                                        userIdentityWin.close();
                                        treeBeforeLoadBig();
                                    } else {
                                        if (message==='审核用户不存在或密码错误！' ||message==='审核用户已被锁定！') {
                                            setMessage(message);
                                            Ext.getCmp("rsButton").enable();
                                            return;
                                        } else {
                                            Ext.Msg.alert ('提示', message, function (){
                                                userIdentityWin.close();
                                                treeBeforeLoadBig();
                                            });
                                        }
                                    }
                                },
                                failure : function(result, request) {
                                    Ext.Msg.alert('提示', '保存失败, 请联系管理员!');
                                }
                            });
                        }
                    });
                }
            },{
                xtype: "button",
                text: "取消",
                handler: function () {
                    userIdentityForm.getForm().reset();
                    userIdentityWin.close();
                }
            }]
        }).show();
    userIdentityWin.center();
}

//报错批量作业状态点击
function  errorBatchStatuBh(ijobname, iflowid, ichildname,jobStatus,createTime) {
    var subsystemNames = "";
    var jobNames = "";
    var insNames = "";
    var averageStarttimes = "";
    var nowStarttimes = "";
    var leaders = "";
    var phones = "";
    var iflowinsname= "";
    Ext.Ajax.request({
        url : 'bhOdsQueryErrorBatchActDetalle.do',
        async: false,
        params : {
            flowid: iflowid,
            jobName:ijobname,
            childName:ichildname,
            jobStatus:jobStatus
        },
        success : function(response, opts) {
            subsystemNames = Ext.decode(response.responseText).dataList.ichildname;
            jobNames = Ext.decode(response.responseText).dataList.ijobname;
            insNames = "";
            averageStarttimes = Ext.decode(response.responseText).dataList.avgStartTime;
            nowStarttimes = Ext.decode(response.responseText).dataList.istarttime;
            leaders = Ext.decode(response.responseText).dataList.ifullname;
            phones = Ext.decode(response.responseText).dataList.itelephone;
            iflowinsname = Ext.decode(response.responseText).dataList.iflowinsname;
        },
        failure : function(response, opts) {
        }
    });
    console.log("报错批量作业状态点击~~~" + ichildname)
    var subsystemName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '子系统名称',
        readOnly:true,
        value: ichildname
    });
    var jobName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        bodyPadding: 10,
        maxLength: 255,
        fieldLabel: '作业名称',
        readOnly:true,
        value: ijobname
    });
    var insName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '会计日期',
        readOnly:true,
        value: iflowinsname
    });
    // var timeType = Ext.create('Ext.form.field.Text', {
    //     width: '100%',
    //     labelWidth: 100,
    //     labelAlign: 'right',
    //     allowBlank: false,
    //     maxLength: 255,
    //     fieldLabel: '日期类型',
    //     readOnly:true,
    //     value: timeType
    // });
    var averageStarttime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '平均开始时间',
        readOnly:true,
        value: averageStarttimes
    });
    var nowStarttime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '当日开始时间',
        readOnly:true,
        value: nowStarttimes
    });
    var warningTime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '报警时间',
        readOnly:true,
        value: createTime
    });
    var leader = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '批量负责人',
        readOnly:true,
        value: leaders
    });
    var phone = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '联系电话',
        readOnly:true,
        value: phones
    });
    var errorForm = Ext.create('Ext.form.Panel', {
        name: 'errorForm',
        region: 'center',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        bodyBorder: false,
        dockedItems: [{
            border: false,
            items: [subsystemName,jobName,insName,averageStarttime,nowStarttime,warningTime,leader,phone]
        }]
    });

    var errorStartWin = Ext.create ('Ext.window.Window',
        {
            title : '节点信息',
            modal : true,
            closable : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
            width : errorWinWidthBh,
            autoDestroy:true,
            height : errorWinHeightBh,
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            items : [
                errorForm
            ],
        });
    errorStartWin.show();
}
//报错批量作业状态点击
function  errorTimeCheck(ijobname, iid, ichildname,jobStatus,createTime) {
    var des = "";
    Ext.Ajax.request({
        url : 'bhQueryErrorTimeCheckDetail.do',
        async: false,
        params : {
            iid: iid,
            jobName:ijobname,
            childName:ichildname,
            jobStatus:jobStatus
        },
        success : function(response, opts) {
            des = Ext.decode(response.responseText).dataList;
        },
        failure : function(response, opts) {
        }
    });
    console.log("翻牌错误" + des)
    var desTimeCheckName = Ext.create('Ext.form.TextArea', {
        width: '100%',
        labelWidth: 100,
        height:200,
        allowBlank: false,
        maxLength: 4000,
        readOnly:true,
        value: des
    });

    var errorTimeCheckForm = Ext.create('Ext.form.Panel', {
        name: 'errorForm',
        region: 'center',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        bodyBorder: false,
        dockedItems: [{
            border: false,
            items: [desTimeCheckName]
        }]
    });

    var errorTimeCheckStartWin = Ext.create ('Ext.window.Window',
        {
            title : '节点信息',
            modal : true,
            closable : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
            width : errorWinWidth,
            autoDestroy:true,
            height : errorWinHeight/2,
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            items : [
                errorTimeCheckForm
            ],
        });
    errorTimeCheckStartWin.show();
}
function  executeTimeCheck(ijobname, iid, jobStatus,ichildname,model) {
    console.log("终止和强制点击进来了~~~")
    var typeName= "";
    if (model==1){
        typeName="强制";
    }else {
        typeName="终止";
    }
    var des = "";
    Ext.Ajax.request({
        url : 'bhQueryErrorTimeCheckDetail.do',
        async: false,
        params : {
            iid: iid,
            jobName:ijobname,
            childName:ichildname,
            jobStatus:jobStatus
        },
        success : function(response, opts) {
            des = Ext.decode(response.responseText).dataList;
        },
        failure : function(response, opts) {
        }
    });
    console.log("翻牌错误" + des);

    var desTimeCheckName = Ext.create('Ext.form.TextArea', {
        width: '50%',
        labelWidth: 100,
        height:200,
        allowBlank: false,
        maxLength: 4000,
        readOnly:true,
        value: des
    });
    // var childNameAddCom = Ext.create ('Ext.form.DisplayField',
    //     {
    //         fieldLabel: '子系统名',
    //         labelAlign : 'right',
    //         labelWidth : 120,
    //         width : winWidth * 0.5,
    //         id: 'childNameAddSec',
    //         name: 'childNameAddSec',
    //         fieldCls : 'x-form-item-label',
    //         value:ichildname
    //     });
    // var jobNameCom = Ext.create('Ext.form.DisplayField', {
    //     fieldLabel: '主线名称',
    //     labelAlign : 'right',
    //     labelWidth : 120,
    //     width : winWidth * 0.5,
    //     id: 'jobNameAddSec',
    //     name: 'jobNameAddSec',
    //     fieldCls : 'x-form-item-label',
    //     value: ijobname
    // });
    var type = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '操作类型',
            labelAlign : 'right',
            labelWidth : 120,
            width : winWidth * 0.5,
            id: 'mainNameAddSec',
            name: 'mainNameAddSec',
            fieldCls : 'x-form-item-label',
            value:typeName +"执行"
        });
    var userIdentityForm  = Ext.create('Ext.form.Panel', {
        border: false,
        width: '100%',
        layout: 'anchor',
        region: 'center',
        collapsible : false,
        dockedItems : [
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [desTimeCheckName ]
            },
            // {
            //     layout:{
            //         type:'vbox',
            //         align:'center'
            //     },
            //     border : false,
            //     dock : 'top',
            //     region:'center',
            //     items : [ jobNameCom ]
            // },
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [type]
            },
            {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '用户名',
                    id:'checkUser',
                    name:'checkUser',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                }]
            }, {
                xtype : 'toolbar',
                border : false,
                dock : 'top',
                baseCls:'customize_gray_back',
                items:[{
                    fieldLabel: '密码',
                    id:'password',
                    name:'password',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                    inputType: 'password'
                }]
            }]
    });


    var userIdentityPanel = Ext.create('Ext.panel.Panel', {
        layout : 'border',
        border : true,
        region : 'center',
        //renderTo:"top_userinfo_area",
        items : [
            userIdentityForm
        ]
    });


    var userIdentityWin = Ext.create ('Ext.window.Window',
        {
            title : '复核信息',
            modal : true,
            closeAction : 'destroy',
            buttonAlign : 'center',
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            items : [userIdentityPanel],
            width : winDoubleCheckWidth-20,
            height : winDoubleCheckWidth-120,
            buttons :[{
                xtype: "button",
                text: "确定",
                id: "rsButton",
                handler: function () {
                    var form_checkUser = userIdentityForm.getForm ().findField ("checkUser").getRawValue();
                    var form_password = userIdentityForm.getForm ().findField ("password").getRawValue();
                    if (trim (form_checkUser) == '' || (null == form_checkUser))
                    {
                        Ext.MessageBox.alert ("提示", "请输入用户名");
                        return false;
                    }
                    if (trim (form_password) == '' || (null == form_password))
                    {
                        Ext.MessageBox.alert ("提示", "请输入密码");
                        return false;
                    }
                    Ext.getCmp("rsButton").disabled='disabled';
                    Ext.Msg.confirm("提示",'确认'+typeName+'?', function(btn){
                        if (btn == 'yes'){ // 提交数据
                            Ext.Ajax.request({
                                url : 'operateDataTimeCheckList.do',
                                params : {
                                    iid : iid,
                                    model : model, 	// 1-重跑 2-略过
                                    childName : ichildname, 	// 子系统名
                                    jobName: ijobname, //子步骤名
                                    jobStatus:jobStatus,
                                    user:form_checkUser, //用户名
                                    passWord:form_password // 密码
                                },
                                success : function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if(success){
                                        setMessage(message);
                                        userIdentityWin.close();
                                        searchB();
                                    } else {
                                        if (message==='审核用户不存在或密码错误！' ||message==='审核用户已被锁定！') {
                                            setMessage(message);
                                            Ext.getCmp("rsButton").enable();
                                            return;
                                        } else {
                                            Ext.Msg.alert ('提示', message, function (){
                                                userIdentityWin.close();
                                                searchB();
                                            });
                                        }
                                    }
                                },
                                failure : function(result, request) {
                                    Ext.Msg.alert('提示', '保存失败, 请联系管理员!');
                                }
                            });
                        }
                    });
                }
            },{
                xtype: "button",
                text: "取消",
                handler: function () {
                    userIdentityForm.getForm().reset();
                    userIdentityWin.close();
                }
            }]
        }).show();
    userIdentityWin.center();

}
//报错报错节点点击
function  errorBatchxx(ijobname, iflowid, ichildname, iid) {
    console.log("报错报错节点点击~~~" + iflowid, + " ~" + ijobname + " ~" + ichildname + "~" + iid)
    var nodeDetailInfoWin  = Ext.create ('Ext.window.Window',
        {
            title : '节点信息',
            modal : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
//				    width : 365,
//				    height : 385,
            width : contentPanel.getWidth()*0.66,
            height : contentPanel.getHeight()*0.8,
            minWidth : 350,
            draggable : false,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            loader :
                {
                    url : 'nodeDetailInfo.do',
                    params :
                        {
                            showFlag : true,
                            iflowid: iflowid,
                            ijobname:ijobname,
                            ichildname:ichildname,
                            iid:iid,
                            // flowId : recordFlowId
                        },
                    autoLoad : true,
                    scripts : true
                }
        });
    nodeDetailInfoWin.show ();
}

// 超时节点点击
function timeOutBatchxx(ijobname, iflowid,iid,iwaringtype,idatetype,warndes,iactname,iflowname, ichildname) {
    console.log("超时节点点击~~~")
    var nodeDetailInfoWin  = Ext.create ('Ext.window.Window',
        {
            title : '节点信息',
            modal : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
//				    width : 365,
//				    height : 385,
            width : contentPanel.getWidth()*0.66,
            height : contentPanel.getHeight()*0.8,
            minWidth : 350,
            draggable : false,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            loader :
                {
                    url : 'nodeDetailInfo.do',
                    params :
                        {
                            showFlag : false,
                            iflowid: iflowid,
                            ijobname:ijobname,
                            ichildname:ichildname,
                            idatetype:idatetype,
                            iid:iid
                            // flowId : recordFlowId
                        },
                    autoLoad : true,
                    scripts : true
                }
        });
    nodeDetailInfoWin.show ();
}

//超时批量作业状态点击
function  timeOutBatchStatu(ijobname, iflowid,iid,iwaringtype,idatetype,warndes,iactname,iflowname, ichildname,warningTime) {
    console.log("超时批量作业状态点击~~~")
    console.log(warningTime);
    var date = CurentTime(warningTime);
    console.log(date);
    var subsystemNames = "";
    var jobNames = "";
    var insNames = "";
    var iflowinsname= "";
    var averageStarttimes = "";
    var nowStarttimes = "";
    var leaders = "";
    var phones = "";
    var idatetypes = "";
    Ext.Ajax.request({
        url : 'bhOdsQueryErrorBatchTimeoutDetalle.do',
        async: false,
        params : {
            flowid: iflowid,
            jobName:ijobname,
            childName:ichildname,
            idatetype:idatetype,
            iactname:iactname,
            iflowname:iflowname,
            iwaringtype:iwaringtype,
            iid:iid
        },
        success : function(response, opts) {
            subsystemNames = Ext.decode(response.responseText).dataList.ichildname;
            jobNames = Ext.decode(response.responseText).dataList.ijobname;
            insNames = "";
            averageStarttimes = Ext.decode(response.responseText).dataList.avgStartTime;
            nowStarttimes = Ext.decode(response.responseText).dataList.istarttime;
            leaders = Ext.decode(response.responseText).dataList.ifullname;
            phones = Ext.decode(response.responseText).dataList.itelephone;
            idatetypes = Ext.decode(response.responseText).dataList.idatetype;
            iflowinsname = Ext.decode(response.responseText).dataList.iflowinsname;
            warndes = warndes;
        },
        failure : function(response, opts) {
        }
    });
    //todo
    var subsystemName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '子系统名称',
        readOnly:true,
        value: subsystemNames
    });
    var jobName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        bodyPadding: 10,
        maxLength: 255,
        fieldLabel: '作业名称',
        readOnly:true,
        value: jobNames
    });
    var insName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '会计日期',
        readOnly:true,
        value: iflowinsname
    });
    var timeType = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '日期类型',
        readOnly:true,
        value: idatetypes
    });
    var averageStarttime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '平均开始时间',
        readOnly:true,
        value: averageStarttimes
    });
    var averageStarttime1 = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '预开始时间',
        readOnly:true,
        value: averageStarttimes
    });
    var averageStarttime2 = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '预结束时间',
        readOnly:true,
        value: averageStarttimes
    });
    var nowStarttime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '当日开始时间',
        readOnly:true,
        value: nowStarttimes
    });
    var nowWarningTime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '告警时间',
        readOnly:true,
        value:date
    });
    var leader = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '批量负责人',
        readOnly:true,
        value: leaders
    });
    var phone = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 100,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '联系电话',
        readOnly:true,
        value: phones
    });
    var warndes = Ext.create('Ext.form.TextArea', {
        width: '100%',
        labelWidth: 100,
        height:100,
        labelAlign: 'right',
        allowBlank: false,
        preventScrollbars:true,
        maxLength: 255,
        fieldLabel: '报错日志',
        readOnly:true,
        value: warndes
    });
    var errorForm='';
    if (iwaringtype == 2){
        //超时
        errorForm = Ext.create('Ext.form.Panel', {
            name: 'errorForm',
            region: 'center',
            collapsible: false,//可收缩
            collapsed: false,//默认收缩
            border: false,
            bodyBorder: false,
            dockedItems: [{
                border: false,
                items: [subsystemName,jobName,insName,timeType,averageStarttime,nowStarttime,nowWarningTime,leader,phone,warndes]
            }]
        });
    }else if(iwaringtype == 1) {
        //未结束
        errorForm = Ext.create('Ext.form.Panel', {
            name: 'errorForm',
            region: 'center',
            collapsible: false,//可收缩
            collapsed: false,//默认收缩
            border: false,
            bodyBorder: false,
            dockedItems: [{
                border: false,
                items: [subsystemName, jobName, insName, timeType, averageStarttime2, nowStarttime, leader, phone,warndes]
            }]
        });
    }else {
        //未开始
        errorForm = Ext.create('Ext.form.Panel', {
            name: 'errorForm',
            region: 'center',
            collapsible: false,//可收缩
            collapsed: false,//默认收缩
            border: false,
            bodyBorder: false,
            dockedItems: [{
                border: false,
                items: [subsystemName, jobName, insName, timeType, averageStarttime1, nowStarttime,nowWarningTime, leader, phone,warndes]
            }]
        });
    }
    var timeOutStartWin = Ext.create ('Ext.window.Window',
        {
            title : '节点信息',
            modal : true,
            closable : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
            width : errorWinWidthBh,
            autoDestroy:true,
            height : errorWinHeightBh,
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            items : [
                errorForm
            ],
        });
    timeOutStartWin.show();
}
function CurentTime(warnTime)
{
    let now = new Date(parseInt(warnTime));

    let year = now.getFullYear();       //年
    let month = now.getMonth() + 1;     //月
    let day = now.getDate();            //日

    let hh = now.getHours();            //时
    let mm = now.getMinutes();          //分
    let ss = now.getSeconds();           //秒

    let clock = year;
    if(month < 10){

        clock += "-0";
    }else{
        clock += "-";
    }

    clock +=month;

    if(day < 10){
        clock += "-0";
    }else{
        clock += "-";
    }
    clock += day;
    if(hh < 10){
        clock += " 0";
    }else{
        clock += " ";
    }
    clock += hh;
    if (mm < 10)
    {
        clock += ':0';
    }
    else{
        clock += ":";
    }
    clock += mm ;

    if (ss < 10) {
        clock += ':0';
    }
    else{
        clock += ":";
    }
    clock += ss;
    console.log(clock);
    return(clock);
}
//取消延迟信息
function  delayCheck(loginUser, checkUser, handleTime) {
    var handleName = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 65,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '操作人',
        readOnly:true,
        value: loginUser
    });
    var checkUser = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 65,
        labelAlign: 'right',
        allowBlank: false,
        bodyPadding: 10,
        maxLength: 255,
        fieldLabel: '复核人',
        readOnly:true,
        value: checkUser
    });
    var handleTime = Ext.create('Ext.form.field.Text', {
        width: '100%',
        labelWidth: 65,
        labelAlign: 'right',
        allowBlank: false,
        maxLength: 255,
        fieldLabel: '操作时间',
        readOnly:true,
        value: handleTime
    });


    var delayForm = Ext.create('Ext.form.Panel', {
        name: 'delayForm',
        region: 'center',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        bodyBorder: false,
        dockedItems: [{
            border: false,
            items: [handleName,checkUser,handleTime]
        }]
    });


    var addDelayStartWin = Ext.create ('Ext.window.Window',
        {
            title : '取消延迟信息查看',
            modal : true,
            closable : true,
            closeAction : 'destroy',
            constrain : true,
            autoScroll : true,
            width : winWidthBh,
            autoDestroy:true,
            height : winHeightBh,
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            items : [
                delayForm
            ],
        });
    addDelayStartWin.show();
}

function  confirmDoY(iprjname,iid,iactname,iflowname,warningTime,warnID,waringtype) {

}
//超时确认方法
var checkCycleTime;
function  confirmDo(iprjname,iid,iactname,iflowname,idataTime ,warnID,waringtype, jobname) {
    Ext.Msg.confirm ("提示", "确认此数据么？", function (button, text)
    {
        if (button == "yes")
        {
            // 弹出再次检查周期窗口
            if (waringtype!=0 && waringtype!=1) {
                var timeCheckForm  = Ext.create('Ext.form.Panel', {
                    border: false,
                    width: '100%',
                    layout: 'anchor',
                    region: 'center',
                    collapsible : false,
                    dockedItems : [
                        {
                            xtype : 'toolbar',
                            border : false,
                            dock : 'top',
                            baseCls:'customize_gray_back',
                            items:[{
                                fieldLabel: '时间周期(*单位分钟)',
                                id:'timeCheck',
                                name:'timeCheck',
                                labelAlign: 'right',
                                labelWidth : 150,
                                width : '98%',
                                xtype: 'textfield',
                            }]
                        }]
                });

                //userIdentityPanel
                var timeCheckPanel = Ext.create('Ext.panel.Panel', {
                    layout : 'border',
                    border : true,
                    region : 'center',
                    //renderTo:"top_userinfo_area",
                    items : [
                        timeCheckForm
                    ]
                });
                var timecheckWin = Ext.create ('Ext.window.Window',
                    {
                        title : '检查周期',
                        modal : true,
                        closeAction : 'destroy',
                        buttonAlign : 'center',
                        draggable : true,// 禁止拖动
                        resizable : false,// 禁止缩放
                        layout : 'fit',
                        items : [timeCheckPanel],
                        width : winDoubleCheckWidthBh-100,
                        height : winDoubleCheckWidthBh-520,
                        buttons :[{
                            xtype: "button",
                            text: "确定",
                            handler: function () {
                                var form_checkTime = timeCheckForm.getForm ().findField ("timeCheck").getRawValue();
                                if (trim (form_checkTime) == '' || (null == form_checkTime))
                                {
                                    Ext.MessageBox.alert ("提示", "请输入检查周期");
                                    return false;
                                }
                                Ext.Msg.confirm("提示",'确认此周期?', function(btn){
                                    if (btn == 'yes'){ // 确认数据
                                        checkCycleTime = trim(form_checkTime).replace(/[^\d]/g,' ');
                                        // utHomeFormBh.getForm().findField("refreshTime").setValue(parseInt(internal));
                                        // queryWhere();
                                        Ext.Ajax.request (
                                            {
                                                // url : 'saveNodeWarnInfo.do',
                                                url : 'outtimeUpdateWarnStatu.do',
                                                params :
                                                    {
                                                        warnId: iid,
                                                        detailId : warnID,
                                                        waringType:waringtype,
                                                        checkCycle:checkCycleTime,
                                                        dataTime : idataTime
                                                        // iflowid : iflowid,
                                                        // prjName:iprjname,
                                                        // actName : iactname,
                                                        // flowName : iflowname,
                                                        // type : "运行耗时",
                                                        // warnTime : warningTime,
                                                    },
                                                method : 'POST',
                                                success : function (response, opts)
                                                {
                                                    var massage = Ext.decode (response.responseText).message;
                                                    var success = Ext.decode (response.responseText).success;
                                                    if (success)
                                                    {
                                                        bhTimeoutBatchTreeStoreBh.reload();
                                                        Ext.Msg.alert ('提示', "确认成功");

                                                    }
                                                    else
                                                    {
                                                        Ext.Msg.alert ('提示', "确认失败");
                                                    }
                                                    settingScrollPos(); // 超时批量栏确认
                                                }
                                            });
                                        timecheckWin.close();
                                    }else {

                                    }
                                });
                            }
                        },{
                            xtype: "button",
                            text: "取消",
                            handler: function () {
                                timeCheckForm.getForm().reset();
                                timecheckWin.close();
                            }
                        }]
                    }).show();
                timecheckWin.center();
            }
            else {
                // 非平均耗时类的确认
                Ext.Ajax.request (
                    {
                        // url : 'saveNodeWarnInfo.do',
                        url : 'outtimeUpdateWarnStatu.do',
                        params :
                            {
                                warnId: iid,
                                detailId : warnID,
                                waringType:waringtype,
                                checkCycle:checkCycleTime,
                                dataTime : idataTime
                                // iflowid : iflowid,
                                // prjName:iprjname,
                                // actName : iactname,
                                // flowName : iflowname,
                                // type : "运行耗时",
                                // warnTime : warningTime,
                            },
                        method : 'POST',
                        success : function (response, opts)
                        {
                            var massage = Ext.decode (response.responseText).message;
                            var success = Ext.decode (response.responseText).success;
                            if (success)
                            {
                                bhTimeoutBatchTreeStoreBh.reload();
                                Ext.Msg.alert ('提示', "确认成功");
                            }
                            else
                            {
                                Ext.Msg.alert ('提示', "确认失败");
                            }
                            settingScrollPos();// 非平均耗时类确认
                        }
                    });
            }
        }else{
            return ;
        }
    });
}

// 处理后的方法
function searchB(){
    bhErrorBatchTreeStoreBh.reload();
}

//毫秒处理函数
function formatDuring(millisecond) {
    var days = parseInt(millisecond / (1000 * 60 * 60 * 24));
    var hours = parseInt((millisecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    var minutes = parseInt((millisecond % (1000 * 60 * 60)) / (1000 * 60));
    var seconds = (millisecond % (1000 * 60)) / 1000;
    return days + " 天 " + hours + " 小时 " + minutes + " 分钟  ";
}


// //报错报警相关功能
// function loadAlarmImgAndData(value) {
//     console.log("新加日志调试" + value)
//     Ext.Ajax.request({
//         url : 'alarmNum.do',
//         params:{
//             isAlerm:value,
//         },
//         method : 'POST',
//         success : function(response, opts) {
//             var success = Ext.decode(response.responseText).success;
//             var isalarm = Ext.decode(response.responseText).isalarm;
//             if (success) {
//                 // panelTopLeftBh.setTitle('<img src="images/tp_common.png" align="absmiddle" class="wflow_fail"/>报错批量栏  '+isalarm +"<div align=\"right\"onclick=\"resourceAlarm()\">最大化</div>");
//                 panelTopLeftBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_error batch_monitoring_common'/>" +
//                     "<span class='tit_box_text'>报错批量栏 "+isalarm+"</span>" +
//                     "<a class = \"x-btn Common_Btn x-unselectable x-btn-default-small x-noicon x-btn-noicon x-btn-default-small-noicon tit_box_deal\" href=\"javascript:void(0)\" onclick=\"delInfoBc()\">处理</a>" +
//                     "<a class='batch_monitoring_common_zoom' href='javascript:void(0)' onclick='panelExpandClose(1)' ><img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_in batch_monitoring_common_zoom'/></a>");
//             }
//             var audio_player_actMonitor = document.getElementById("audio_player_actMonitor");
//             if(audio_player_actMonitor!=null && typeof(audio_player_actMonitor)!="undefined"){
//                 setTimeout(function(){audio_player_actMonitor.play()},1500);
//             }
//         },
//         failure : function(response, opts) {
//             Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
//         }
//     });
// }
//超时报警相关功能
function loadAlarmImgAndDataErrorB(value) {
    Ext.Ajax.request({
        url : 'alarmNumOut.do',
        params:{
            isAlerm:value,
        },
        method : 'POST',
        success : function(response, opts) {
            var success = Ext.decode(response.responseText).success;
            var isouttimeAlarm = Ext.decode(response.responseText).isouttimeAlarm;
            if (success) {
                panelTopLeftBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_error batch_monitoring_common'/>" +
                    "<span class='tit_box_text'>报错批量栏</span>" + isouttimeAlarm + "&ensp;" +
                    "<a class = \"x-btn Common_Btn x-unselectable x-btn-default-small x-noicon x-btn-noicon x-btn-default-small-noicon tit_box_deal\" href=\"javascript:void(0)\" onclick=\"delInfoBc()\">处理</a>" +
                    "<a class='batch_monitoring_common_zoom' href='javascript:void(0)' onclick='panelExpandClose(1)' ><img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_out batch_monitoring_common_zoom'/></a>");
            }
            var audio_player_actMonitors = document.getElementById("audio_player_actMonitors");
            if(audio_player_actMonitors!=null && typeof(audio_player_actMonitors)!="undefined"){
                setTimeout(function(){audio_player_actMonitors.play()},1500);
            }
        },
        failure : function(response, opts) {
            Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
        }
    });
}

//超时报警相关功能
function loadAlarmImgAndDataOut(value) {
    Ext.Ajax.request({
        url : 'alarmNumOut.do',
        params:{
            isAlerm:value,
        },
        method : 'POST',
        success : function(response, opts) {
            var success = Ext.decode(response.responseText).success;
            var isouttimeAlarm = Ext.decode(response.responseText).isouttimeAlarm;
            if (success) {
                panelTopRightBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_time_out batch_monitoring_common'/>" +
                    "<span class='tit_box_text'>超时批量栏</span>" + isouttimeAlarm + "&ensp;" +
                    "<a class = \"x-btn Common_Btn x-unselectable x-btn-default-small x-noicon x-btn-noicon x-btn-default-small-noicon tit_box_deal\" href=\"javascript:void(0)\" onclick=\"delInfoCs()\">处理</a>" +
                    "<a href='javascript:void(0)' onclick='panelExpandClose(2)' ><img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_out batch_monitoring_common_zoom'/></a>");
            }
            var audio_player_actMonitors = document.getElementById("audio_player_actMonitors");
            if(audio_player_actMonitors!=null && typeof(audio_player_actMonitors)!="undefined"){
                setTimeout(function(){audio_player_actMonitors.play()},1500);
            }
        },
        failure : function(response, opts) {
            Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
        }
    });
}
//手工批量小铃铛
function loadAlarmImgAndUserTaskBh(value) {
    Ext.Ajax.request({
        url : 'alarmNumUserTask.do',
        params:{
            isAlarm:value,
        },
        method : 'POST',
        success : function(response, opts) {
            var success = Ext.decode(response.responseText).success;
            var isUserTaskAlarm = Ext.decode(response.responseText).isUserTaskAlarm;
            console.log("isUserTaskAlarm:"+isUserTaskAlarm);
            if (success) {
                panelBottomRightBh.setTitle("<img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_time_out batch_monitoring_common'/>" +
                    "<span class='tit_box_text'>手工批量栏</span>" + isUserTaskAlarm + "&ensp;" +
                    "<a class = \"x-btn Common_Btn x-unselectable x-btn-default-small x-noicon x-btn-noicon x-btn-default-small-noicon tit_box_deal\" href=\"javascript:void(0)\" onclick=\"delInfoUserTask()\">处理</a>" +
                    "<a href='javascript:void(0)' onclick='panelExpandClose(5)' ><img src='images/tp_common.png' align='absmiddle' class='batch_monitoring_zoom_out batch_monitoring_common_zoom'/></a>");
            }
            var audio_player_actMonitors = document.getElementById("audio_player_actMonitors");
            if(audio_player_actMonitors!=null && typeof(audio_player_actMonitors)!="undefined"){
                setTimeout(function(){audio_player_actMonitors.play()},1500);
            }
        },
        failure : function(response, opts) {
            Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
        }
    });
}
function errorDelete(ijobname, iid, ichildname, iactname) {
    Ext.Msg.confirm ("提示", "确认删除此数据么？", function (button, text)
    {
        if (button == "yes")
        {
            console.log("yes")
            Ext.Ajax.request (
                {
                    url : 'bhOdsDeleteAffirmErrorBatch.do',
                    params :
                        {

                            iid : iid,

                            actName : iactname,



                        },
                    method : 'POST',
                    success : function (response, opts)
                    {
                        var massage = Ext.decode (response.responseText).message;
                        var success = Ext.decode (response.responseText).success;
                        if (success)
                        {
                            bhErrorBatchTreeStoreBh.reload();
                            Ext.Msg.alert ('提示', "删除成功");
                        }
                        else
                        {
                            Ext.Msg.alert ('提示', "删除失败");
                        }
                    }
                });
        }else{
            return ;
        }
    });

}
function verifyC(ijobname, iid, dateTime,ichildname, iactname) {
    // 非原始作业无需确认
    Ext.Msg.confirm ("提示", "确认此数据么？", function (button, text)
    {
        if (button == "yes")
        {
            console.log("yes")
            Ext.Ajax.request (
                {
                    url : 'bhOdsAffirmErrorBatch.do',
                    params :
                        {
                            iid : iid,
                            actName : iactname,

                        },
                    method : 'POST',
                    success : function (response, opts)
                    {
                        var massage = Ext.decode (response.responseText).message;
                        var success = Ext.decode (response.responseText).success;
                        if (success)
                        {
                            //todo
                            bhErrorBatchTreeStoreBh.reload();
                            // Ext.Ajax.request({
                            //     url : 'bhOdsQueryErrorBatch.do',// stepStop.do
                            //     param:{
                            //         page : 0,
                            //         limit : 100,
                            //     },
                            //     method : 'POST',
                            //     success : function(response, request) {
                            //         if (Ext.decode (response.responseText).volflag) {
                            //             loadAlarmImgAndData(true);
                            //         }else{
                            //             loadAlarmImgAndData(false);
                            //         }
                            //     },
                            //     failure : function(result, request) {
                            //     }
                            // });
                            Ext.Msg.alert ('提示', "确认成功");
                        }
                        else
                        {
                            Ext.Msg.alert ('提示', "确认失败");
                        }
                    }
                });
        }else{
            return ;
        }
        settingScrollPos();// 报错批量栏确认
    });
}

function CFMD(ijobname, iflowid, ichildname, iactname) {

}
// function delInfoCs(){
//
//     Ext.Msg.confirm ("提示", "确认批量处理超时数据响铃么？", function (button, text)
//     {
//         if (button == "yes")
//         {
//             var data =timeoutBatchGridBh.getSelectionModel().store.data.items;
//             console.log(data)
//             var ids = [];
//             var warnids = [];
//             for (var i = 0; i < data.length; i++) {
//                 ids.push(data[i].get('iflowid'));
//                 warnids.push(data[i].get('warnID'));
//             }
//             Ext.Ajax.request (
//                 {
//                     url : 'batchUpdateWarnStatu.do',
//                     params :
//                         {
//                             idarray : ids,
//                             iidarray: warnids
//                         },
//                     method : 'POST',
//                     success : function (response, opts)
//                     {
//                         var massage = Ext.decode (response.responseText).message;
//                         var success = Ext.decode (response.responseText).success;
//                         if (success)
//                         {
//                             bhTimeoutBatchTreeStoreBh.reload();
//                             // Ext.Ajax.request({
//                             //     url : 'bhOdsQueryErrorBatchTimeout.do',// stepStop.do
//                             //     param:{
//                             //         page : 0,
//                             //         limit : 100,
//                             //     },
//                             //     method : 'POST',
//                             //     success : function(response, request) {
//                             //         if (Ext.decode (response.responseText).volflag) {
//                             //             loadAlarmImgAndDataOut(true);
//                             //         }else{
//                             //             loadAlarmImgAndDataOut(false);
//                             //         }
//                             //     },
//                             //     failure : function(result, request) {
//                             //     }
//                             // });
//                             Ext.Msg.alert ('提示', "批量处理超时数据成功");
//                         }
//                         else
//                         {
//                             Ext.Msg.alert ('提示', "批量处理超时数据失败");
//                         }
//                     }
//                 });
//         }else{
//             return ;
//         }
//     });
//
// }
function delInfoCs(){

    Ext.Msg.confirm ("提示", "确认批量处理超时数据响铃么？", function (button, text)
    {
        if (button == "yes")
        {
            // 收集数据
            Ext.Ajax.request (
                {
                    // url : 'batchUpdateWarnStatu.do',
                    url : 'batchAllUpdateWarnStatus.do',
                    // params :
                    //     {
                    //         idarray : ids,
                    //         iidarray: warnids
                    //     },
                    method : 'POST',
                    success : function (response, opts)
                    {
                        var massage = Ext.decode (response.responseText).message;
                        var success = Ext.decode (response.responseText).success;
                        if (success)
                        {
                            bhTimeoutBatchTreeStoreBh.reload();
                            // Ext.Ajax.request({
                            //     url : 'bhOdsQueryErrorBatchTimeout.do',// stepStop.do
                            //     param:{
                            //         page : 0,
                            //         limit : 100,
                            //     },
                            //     method : 'POST',
                            //     success : function(response, request) {
                            //         if (Ext.decode (response.responseText).volflag) {
                            //             loadAlarmImgAndDataOut(true);
                            //         }else{
                            //             loadAlarmImgAndDataOut(false);
                            //         }
                            //     },
                            //     failure : function(result, request) {
                            //     }
                            // });
                            Ext.Msg.alert ('提示', "批量处理超时数据成功");
                        }
                        else
                        {
                            Ext.Msg.alert ('提示', "批量处理超时数据失败");
                        }
                    }
                });
        }else{
            return ;
        }
    });

}
function delInfoBc(){
    Ext.Msg.confirm ("提示", "确认批量处理报错数据么？", function (button, text)
    {
        if (button == "yes")
        {
            console.log("yes")
            Ext.Ajax.request (
                {
                    url : 'batchAllUpdateErrorWarnStatus.do',
                    // params :
                    //     {
                    //         flowId : iflowid,
                    //         actName : iactname,
                    //
                    //     },
                    method : 'POST',
                    success : function (response, opts)
                    {
                        var massage = Ext.decode (response.responseText).message;
                        var success = Ext.decode (response.responseText).success;
                        if (success)
                        {
                            bhErrorBatchTreeStoreBh.reload();
                            Ext.Msg.alert ('提示', "处理报错数据成功");
                        }
                        else
                        {
                            Ext.Msg.alert ('提示', "处理报错数据失败");
                        }
                    }
                });
        }else{
            return ;
        }
    });

}
function delInfoUserTask(){

    Ext.Msg.confirm ("提示", "确认批量处理手工批量数据响铃么？", function (button, text)
    {
        if (button == "yes")
        {
            // 收集数据
            Ext.Ajax.request (
                {
                    url : 'batchAllUpdateUserTaskWarnStatus.do',
                    method : 'POST',
                    success : function (response, opts)
                    {
                        var massage = Ext.decode (response.responseText).message;
                        var success = Ext.decode (response.responseText).success;
                        if (success)
                        {
                            manualBatchStoreBh.reload();
                            Ext.Msg.alert ('提示', "批量处理手工数据响铃成功");
                        } else
                        {
                            Ext.Msg.alert ('提示', "批量处理手工数据响铃失败");
                        }
                    }
                });
        }else{
            return ;
        }
    });

}

function delInfoUserTaskOneBh(actId,flowId){

    Ext.Msg.confirm ("提示", "确认处理当前手工批量数据响铃么？", function (button, text)
    {
        if (button == "yes")
        {
            // 收集数据
            Ext.Ajax.request (
                {
                    url : 'oneUpdateUserTaskWarnStatus.do',
                    params:{
                        flowId:flowId
                    },
                    method : 'POST',
                    success : function (response, opts)
                    {
                        var massage = Ext.decode (response.responseText).message;
                        var success = Ext.decode (response.responseText).success;
                        if (success)
                        {
                            manualBatchStore.reload();
                            Ext.Msg.alert ('提示', "处理手工数据响铃成功");
                        } else
                        {
                            manualBatchStore.reload();
                            Ext.Msg.alert ('提示', "处理手工数据响铃失败");
                        }
                    }
                });
        }else{
            return ;
        }
    });

}
function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
}

//手工栏执行确认
function  manualConfirmBh(iactid, iflowid, itaskid,iname,iflowname,iflowinsname) {
    console.log("手工执行进来了~~~");
    // 申请人下拉列表--model
    Ext.define('userNameModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'loginUser', // 系统名称
            type : 'string'
        }]
    });
    var getUserNameStore = Ext.create('Ext.data.Store', {
        autoLoad : true,
        autoDestroy : true,
        model : 'userNameModel',
        proxy : {
            type : 'ajax',
            url : 'queryUserName.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });
    var manulNameOne = Ext.create ('Ext.form.field.Text',
        {
            width: '100%',
            labelWidth: 100,
            labelAlign: 'right',
            allowBlank: false,
            maxLength: 255,
            fieldLabel: '操作进程名',
            readOnly:true,
            value:"任务手工执行"
        });
    // 申请人下拉列表--ComBox
    var userNameComBox1 = Ext.create('Ext.form.field.ComboBox', {
        id : 'userNameComBox1',
        name : 'userNameComBox1',
        fieldLabel : '申请人',
        labelAlign : 'right',
        displayField : 'loginUser',
        valueField : 'loginUser',
        emptyText : '--请选择申请人--',
        width : '100%',
        labelWidth: 100,

        editable : true,
        typeAhead : true,
        store : getUserNameStore,
        queryMode : 'local',
        listeners : {
            select : function() {
                //Ext.getCmp("act_combo").clearValue();
                userNameAdd = this.getRawValue();
            }
        }
    });
    var manualConfirmForm  = Ext.create('Ext.form.Panel', {
        border: false,
        width: '100%',
        layout: 'anchor',
        region: 'center',
        collapsible : false,
        dockedItems : [
            {
                baseCls:'customize_gray_back',
                border : false,
                xtype : 'toolbar',
                dock : 'top',
                region:'center',
                items : [manulName ]
            },
            {
                baseCls:'customize_gray_back',
                border : false,
                xtype : 'toolbar',
                dock : 'top',
                region:'center',
                items : [ manulNameOne  ]
            },{

                border : false,
                xtype : 'toolbar',
                dock : 'top',
                region:'center',
                items : [ userNameComBox1  ]
            }]
    });


    var manualConfirmPanel = Ext.create('Ext.panel.Panel', {
        layout : 'border',
        border : true,
        region : 'center',
        //renderTo:"top_userinfo_area",
        items : [
            manualConfirmForm
        ]
    });
    // Ext.Ajax.request({
    //     async: false,
    //     params : {
    //         flowId: iflowid,
    //         iactName:iactname,
    //     },
    //     success : function(response, opts) {
    //         actId = Ext.decode(response.responseText).actId;
    //         errorTaskId = Ext.decode(response.responseText).errorTaskId;
    //     },
    //     failure : function(response, opts) {
    //     }
    // });

    manualConfirmWinBh = Ext.create ('Ext.window.Window',
        {
            title : '人工处理',
            modal : true,
            closeAction : 'destroy',
            buttonAlign : 'center',
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            items : [manualConfirmPanel],
            width : 430,
            height : 300,
            buttons :[{
                xtype: "button",
                text: "确定执行",
                handler: function () {
                    Ext.Msg.confirm("提示",'确定执行?', function(btn){
                        if (btn == 'yes'){ // 提交数据
                            // 申请人
                            var form_userName = manualConfirmForm.getForm ().findField ("userNameComBox1").getValue();
                            // Ext.Ajax.request({
                            //     url : 'utRecordOptionForJob.do',
                            //     params : {
                            //         sysType : 1,
                            //         opId : "0",
                            //         taskId : itaskid,
                            //         opDesc : "",
                            //         flowId : iflowid,
                            //         actId : iactid
                            //     },
                            //     success : function(response, request) {
                            //         var success = Ext.decode(response.responseText).success;
                            //         var message = Ext.decode(response.responseText).message;
                            //         if(success){
                            //             manualBatchStore.reload();
                            //             Ext.Msg.alert('提示', '确定成功!');
                            //             manualConfirmWin.close();
                            //         } else {
                            //             Ext.Msg.alert('提示', '确定失败!');
                            //             manualConfirmWin.close();
                            //         }
                            //     },
                            //     failure : function(result, request) {
                            //         Ext.Msg.alert('提示', '确定执行失败, 请联系管理员!');
                            //     }
                            // });
                            userIdentityBatchBh(iactid, iflowid, itaskid,iname,iflowname,iflowinsname,form_userName);
                        }
                    });
                }
            },{
                xtype: "button",
                text: "取消",
                handler: function () {
                    manualConfirmForm.getForm().reset();
                    manualConfirmWinBh.close();
                }
            }]
        }).show();
    manualConfirmWinBh.center();
}
function dealExceptionPauseSimbatBh (id, baseName, childName, actName, jobName, type,dateTime) {
    Ext.Msg.alert('提示', '暂不支持Simbat作业!');
}

function dealExceptionPauseBh(id, baseName, childName, actName, jobName, type,dateTime) {
    var baseNameAddCom = Ext.create('Ext.form.DisplayField',
        {
            fieldLabel: '作业源',
            labelAlign: 'right',
            labelWidth: 120,
            width: '100%',
            id: 'baseNameAddSec',
            name: 'baseNameAddSec',
            fieldCls: 'x-form-item-label',
            value: baseName
        });
    var childNameAddCom = Ext.create('Ext.form.DisplayField',
        {
            fieldLabel: '子系统名称',
            labelAlign: 'right',
            labelWidth: 120,
            width: '100%',
            id: 'childNameAddSec',
            name: 'childNameAddSec',
            fieldCls: 'x-form-item-label',
            value: childName
        });
    var jobNameAddCom = Ext.create('Ext.form.DisplayField',
        {
            fieldLabel: '子步骤名称',
            labelAlign: 'right',
            labelWidth: 120,
            width: '100%',
            id: 'jobNameAddSec',
            name: 'jobNameAddSec',
            fieldCls: 'x-form-item-label',
            value: jobName
        });
    var dateTimeAddCom = Ext.create('Ext.form.DisplayField',
        {
            fieldLabel: '数据日期',
            labelAlign: 'right',
            labelWidth: 120,
            width: '100%',
            id: 'dateTimeAddSec',
            name: 'dateTimeAddSec',
            fieldCls: 'x-form-item-label',
            value: dateTime
        });
    var dealPauseFormBh = Ext.create('Ext.form.Panel', {
        border: false,
        width: '100%',
        layout: 'anchor',
        region: 'center',
        collapsible: false,
        dockedItems: [
            {
                layout: {
                    type: 'vbox',
                    align: 'center'
                },
                border: false,
                dock: 'top',
                region: 'center',
                items: [baseNameAddCom]
            },
            {
                layout: {
                    type: 'vbox',
                    align: 'center'
                },
                border: false,
                dock: 'top',
                region: 'center',
                items: [childNameAddCom]
            },
            {
                layout: {
                    type: 'vbox',
                    align: 'center'
                },
                border: false,
                dock: 'top',
                region: 'center',
                items: [jobNameAddCom]
            },
            {
                layout: {
                    type: 'vbox',
                    align: 'center'
                },
                border: false,
                dock: 'top',
                region: 'center',
                items: [dateTimeAddCom]
            }

        ]
    });

    var dealPausePanelBh = Ext.create('Ext.panel.Panel', {
        layout: 'border',
        border: true,
        region: 'center',
        //renderTo:"top_userinfo_area",
        items: [
            dealPauseFormBh
        ]
    });
    var modelType = "";
    var typeName = "";
    if (type == 1) {
        modelType = "exceptionPauseFlow.do";
        typeName ='暂停'
    } else {
        modelType = "exceptionResumeFlow.do";
        typeName ='恢复'
    }
    var dealPauseWinBh = Ext.create('Ext.window.Window',
        {
            title: '执行'+typeName+'操作',
            modal: true,
            closeAction: 'destroy',
            buttonAlign: 'center',
            draggable: true,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
            items: [dealPausePanelBh],
            width: 450,
            height: winHeight,
            buttons: [{
                xtype: "button",
                text: "确认",
                handler: function () {
                    Ext.Msg.confirm("提示", '确认执行?', function (btn) {
                        if (btn == 'yes') { // 提交数据
                            Ext.Ajax.request({
                                url: 'mqJobSchedulingPause.do',
                                params: {
                                    iid: id,
                                    baseName: baseName,
                                    childName: childName,
                                    actName: actName,
                                    jobName: jobName,
                                    dateTime: dateTime,
                                    type:modelType
                                },
                                success: function (response, request) {
                                    var success = Ext.decode(response.responseText).IsOk;
                                    var message = Ext.decode(response.responseText).message;
                                    if (success) {
                                        bhOdsStartBatchStore.reload();
                                        Ext.Msg.alert('提示', '确定成功!');
                                        dealPauseWinBh.close();

                                    } else {

                                        Ext.Msg.alert('提示', message, function () {
                                            dealPauseWinBh.close();
                                            bhOdsStartBatchStoreBh.reload();

                                        });

                                    }
                                },
                                failure: function (result, request) {
                                    Ext.Msg.alert('提示', '确定执行失败, 请联系管理员!');
                                }
                            });
                        }
                    });
                }
            }, {
                xtype: "button",
                text: "取消",
                handler: function () {
                    dealPauseFormBh.getForm().reset();
                    dealPauseWinBh.close();
                }
            }]
        }).show();
    dealPauseWinBh.center();

    }

//增加资源告警事件
function resourceAlarm(showPage) {
    var resourceAlarm_window = Ext.create('Ext.window.Window', {
        modal: true,
        closeAction: 'destroy',
        autoScroll: true,
        width: '80%',
        height: '80%',
        resizable: false,// 禁止缩放
        layout: 'fit',
        loader: {
            url: 'bMonitorExpandPage.do?showWindow='+showPage,
            autoLoad: true,
            scripts: true
        }
    }).show();
}
function userIdentityBatchBh(iactid, iflowid, itaskid,iname,iflowname,iflowinsname,userName){
    var nameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '工程名称',
            labelAlign : 'right',
            labelWidth : 120,
            width: '100%',
            id: 'nameAddSec',
            name: 'nameAddSec',
            fieldCls : 'x-form-item-label',
            value:iname
        });
    var flowNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '工作流名',
            labelAlign : 'right',
            labelWidth : 120,
            width: '100%',
            id: 'flowNameAddSec',
            name: 'flowNameAddSec',
            fieldCls : 'x-form-item-label',
            value:iflowname
        });
    var insNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '实例名称',
            labelAlign : 'right',
            labelWidth : 120,
            width: '100%',
            id: 'insNameAddSec',
            name: 'insNameAddSec',
            fieldCls : 'x-form-item-label',
            value:iflowinsname
        });
    var userNameAddCom = Ext.create ('Ext.form.DisplayField',
        {
            fieldLabel: '申请人',
            labelAlign : 'right',
            labelWidth : 120,
            width: '100%',
            id: 'userNameAddSec',
            name: 'userNameAddSec',
            fieldCls : 'x-form-item-label',
            value:userName
        });
    var userIdentityFormBh  = Ext.create('Ext.form.Panel', {
        border: false,
        width: '100%',
        layout: 'anchor',
        region: 'center',
        collapsible : false,
        dockedItems : [
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [ nameAddCom  ]
            },
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [ flowNameAddCom  ]
            },
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [ insNameAddCom   ]
            },
            {
                layout:{
                    type:'vbox',
                    align:'center'
                },
                border : false,
                dock : 'top',
                region:'center',
                items : [ userNameAddCom  ]
            },
            {
                xtype : 'toolbar',
                border : false,
                dock : 'top',

                items:[{
                    fieldLabel: '用户名',
                    id:'checkUser',
                    name:'checkUser',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                }]
            }, {
                xtype : 'toolbar',
                border : false,
                dock : 'top',

                items:[{
                    fieldLabel: '密码',
                    id:'password',
                    name:'password',
                    labelAlign: 'right',
                    labelWidth : 120,
                    width : '98%',
                    xtype: 'textfield',
                    inputType: 'password'
                }]
            }]
    });

    var userIdentityPanel = Ext.create('Ext.panel.Panel', {
        layout : 'border',
        border : true,
        region : 'center',
        //renderTo:"top_userinfo_area",
        items : [
            userIdentityFormBh
        ]
    });

    var userIdentityWinBh = Ext.create ('Ext.window.Window',
        {
            title : '执行手工批量审核',
            modal : true,
            closeAction : 'destroy',
            buttonAlign : 'center',
            draggable : true,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            items : [userIdentityPanel],
            width : 620,
            height : winHeight+100,
            buttons :[{
                xtype: "button",
                text: "确认",
                handler: function () {
                    var form_checkUser = userIdentityFormBh.getForm ().findField ("checkUser").getRawValue();
                    var form_password = userIdentityFormBh.getForm ().findField ("password").getRawValue();
                    if (trim (form_checkUser) == '' || (null == form_checkUser))
                    {
                        Ext.MessageBox.alert ("提示", "请输入用户名");
                        return false;
                    }
                    if (trim (form_checkUser) == trim (userName))
                    {
                        Ext.MessageBox.alert ("提示", "审批人和申请人不能相同");
                        return false;
                    }
                    if (trim (form_password) == '' || (null == form_password))
                    {
                        Ext.MessageBox.alert ("提示", "请输入密码");
                        return false;
                    }
                    Ext.Msg.confirm("提示",'确认执行审核?', function(btn){
                        if (btn == 'yes'){ // 提交数据
                            Ext.Ajax.request({
                                url : 'utRecordOptionForJobBH.do',
                                params : {
                                    sysType : 1,
                                    opId : "0",
                                    taskId : itaskid,
                                    opDesc : "",
                                    flowId : iflowid,
                                    actId : iactid,
                                    createUser : userName, 	// 申请人
                                    checkUser : form_checkUser, //审核人
                                    passWord : form_password 	// 未加密密码
                                },
                                success : function(response, request) {
                                    var success = Ext.decode(response.responseText).success;
                                    var message = Ext.decode(response.responseText).message;
                                    if(success){
                                        manualBatchStoreBh.reload();
                                        Ext.Msg.alert('提示', '确定成功!');
                                        manualConfirmWinBh.close();
                                        userIdentityWinBh.close();
                                    } else {
                                        if (message==='审核用户不存在或密码错误！'
                                            ||message==='审核用户已被锁定！'
                                            ||message==='登录用户和审核人相同,无法申请！') {
                                            setMessage(message);
                                            return;
                                        } else {
                                            Ext.Msg.alert ('提示', message, function (){
                                                userIdentityWinBh.close();
                                                manualConfirmWinBh.close();
                                                manualBatchStoreBh.reload();
                                            });
                                        }
                                    }
                                },
                                failure : function(result, request) {
                                    Ext.Msg.alert('提示', '确定执行失败, 请联系管理员!');
                                }
                            });
                        }
                    });
                }
            },{
                xtype: "button",
                text: "取消",
                handler: function () {
                    userIdentityFormBh.getForm().reset();
                    userIdentityWinBh.close();
                }
            }]
        }).show();

    userIdentityWinBh.center();
}
var errorTaskWin;
/** 原报错节点活动详情 */
function openErrorTaskDetail(state, errorTaskId) {
    // aoms.log('click errorTask ==' + errorTaskId);
    if (errorTaskWin == undefined || !errorTaskWin.isVisible()) {
        errorTaskWin = Ext.create('Ext.window.Window', {
            title : '异常处理',
            modal : true,
            id : 'errorTaskWin1',
            closeAction : 'destroy',
            constrain : true,
            autoScroll : false,
            upperWin : errorTaskWin,
            width : contentPanel.getWidth() - 350,
            height : contentPanel.getHeight() - 30,
            draggable : false,// 禁止拖动
            resizable : false,// 禁止缩放
            layout : 'fit',
            loader : {
                url : 'etInfo.do',
                params : {
                    sysType : "1",
                    state : state,
                    errorTaskId : errorTaskId,
                    pageType : 0,
                    pageName : "渤海"
                },
                autoLoad : true,
                autoDestroy : true,
                scripts : true
            }
        });
    }
    errorTaskWin.show();
}

function panelExpandClose(num){
    // todo 关闭当前页面
    destroyRubbish();
    parent.Ext.getCmp('panelExpandBigWin').close();
    if (num == 5){
        console.log("刷新手工提醒")
        manualBatchStore.reload();
    }
}

function treeBeforeLoad() {
    var operateGridId = Ext.getCmp('operateGridId').getValue();
    switch (operateGridId) {
        case 1: // 报错批量栏 bhErrorBatchTreeStore
            bhErrorBatchTreeStoreBh.reload();
            break;
        case 2: // 超时批量栏 bhTimeoutBatchTreeStore
            bhTimeoutBatchTreeStoreBh.reload();
            break;
    }
    settingScrollPosition();
}
// 设置监听
function getScrollListeners(){
    if (panelNo === "1") {
        // lili_xing bhErrorBatchTree-body (报错批量栏) bhTimeoutBatchTree-body (超时批量栏)
        var errorBigDiv0 = document.getElementById("bhErrorBatchTreeBh-body").getElementsByTagName("div")[0];
        errorBigDiv0.addEventListener('scroll', function(){
            var scTop = this.scrollTop;
            Ext.getCmp("err_scrollTopBig").setValue(scTop);
        });
    } else if (panelNo === "2"){
        var timeOutBigDiv0 = document.getElementById("bhTimeoutBatchTreeBh-body").getElementsByTagName("div")[0];
        timeOutBigDiv0.addEventListener('scroll', function(){
            var scTop = this.scrollTop;
            Ext.getCmp('timeOut_scrollTopBig').setValue(scTop);
        });
    }
}

function settingScrollPos(){
    // 回到之前的位置，根据业务需要，可以加个延迟（我是加了，不然没法回到之前的位置）
    if (panelNo === "1") {
        setTimeout(() => {
            var errorBigDiv = document.getElementById("bhErrorBatchTreeBh-body").getElementsByTagName("div")[0];
            setTimeout(() => {
                errorBigDiv.scrollTop = Ext.getCmp('err_scrollTopBig').getValue();
            }, 2000)
        }, 2000);
    } else if (panelNo === "2") {
        setTimeout(() => {
            var timeOutBigDiv = document.getElementById("bhTimeoutBatchTreeBh-body").getElementsByTagName("div")[0];
            setTimeout(() => {
                timeOutBigDiv.scrollTop = Ext.getCmp('timeOut_scrollTopBig').getValue();
            }, 2000)
        }, 2000);
    }
}

