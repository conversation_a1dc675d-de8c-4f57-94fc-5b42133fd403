package com.ideal.ieai.server.engine.function;

import com.ideal.ieai.core.IEAIRuntime;
import com.ideal.ieai.core.element.*;
import com.ideal.ieai.server.engine.expreval.ExprEvaluator;
import com.ideal.ieai.server.engine.function.js.JSScopeManager;
import com.ideal.ieai.server.engine.util.IExecContext;
import com.ideal.ieai.server.jobscheduling.util.FunctionParamsBean;
import org.apache.log4j.Logger;

import java.io.FileNotFoundException;
import java.util.*;

/**
 * <p>
 * Description:It is used to manage all functions. It is thread-safe.
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: Ideal Technologies Inc.
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */
public class FunctionManager
{
    private List                _innerFuncs = new ArrayList();
    private List                _outerFuncs = new ArrayList();
    private JSScopeManager      _jsScopeManager;

    private Map                 _funcMap    = new HashMap();
    /**
     * Logger for this class
     */
    private static final Logger _log        = Logger
                                                    .getLogger(FunctionManager.class);

    public FunctionManager(long flowId, Project prj, Workflow flow,
            ClassLoader classLoader) throws CompileFailedException,
            FileNotFoundException
    {
        initFunction(prj);
        // Process external function.
        for (int i = 0, size = _outerFuncs.size(); i < size; i++)
        {
            ExternalFunction outerFunc = (ExternalFunction) _outerFuncs.get(i);
            FunctionFullName funcFullName = new FunctionFullName(outerFunc
                    .getNameSpace(), outerFunc.getName());
            this.addIFunction(funcFullName, new JavaExternalFunction(outerFunc,
                    classLoader));
        }

        // Ready to create the JSScopeMananger instance.
        List funcfullNames = new ArrayList(_funcMap.keySet());

        Map globalPredefinedVars = globalPredefinedVars(prj, null, IEAIRuntime
                .current(), _log);

        Map prjPredefinedVars = this.prjPredefinedVars(prj);

        Map jsInnerFuncs = getInnerFuncs(prj);
        // Create the JSSCopeManager instance.
        _jsScopeManager = new JSScopeManager(jsInnerFuncs, funcfullNames,
                classLoader, this, globalPredefinedVars, prjPredefinedVars, prj
                        .getName());
        List iFuncList = _jsScopeManager.getFunctions();
        for (int i = 0, size = iFuncList.size(); i < size; i++)
        {
            IFunction iFunc = (IFunction) iFuncList.get(i);
            FunctionFullName funcFullName = iFunc.getFunctionFullName();
            this.addIFunction(funcFullName, iFunc);
        }
    }

    public FunctionManager(FunctionParamsBean functionParamsBean, long flowId, Project prj, Workflow flow,
            ClassLoader classLoader) throws CompileFailedException, FileNotFoundException
    {
        initDayStartFunction(functionParamsBean.getFunctionName());
        // Process external function.
        // for (int i = 0, size = _outerFuncs.size(); i < size; i++)
        // {
        // ExternalFunction outerFunc = (ExternalFunction) _outerFuncs.get(i);
        // FunctionFullName funcFullName = new FunctionFullName(outerFunc
        // .getNameSpace(), outerFunc.getName());
        // this.addIFunction(funcFullName, new JavaExternalFunction(outerFunc,
        // classLoader));
        // }

        // Ready to create the JSScopeMananger instance.
        List funcfullNames = new ArrayList(_funcMap.keySet());

        Map globalPredefinedVars = globalPredefinedVars(prj, null, IEAIRuntime.current(), _log);

        Map prjPredefinedVars = this.prjPredefinedVars(prj);

        Map jsInnerFuncs = getDayStartInnerFuncs(prj, functionParamsBean.getFunctionName());
        _log.info("FunctionManager prjName:" + prj.getName() + "   functionName:"+functionParamsBean.getFunctionName());
        // Create the JSSCopeManager instance.
        _jsScopeManager = new JSScopeManager(functionParamsBean, jsInnerFuncs, funcfullNames, classLoader, this,
                globalPredefinedVars, prjPredefinedVars, prj.getName());
        List iFuncList = _jsScopeManager.getFunctions();
        for (int i = 0, size = iFuncList.size(); i < size; i++)
        {
            IFunction iFunc = (IFunction) iFuncList.get(i);
            FunctionFullName funcFullName = iFunc.getFunctionFullName();
            this.addIFunction(funcFullName, iFunc);
        }
    }

    /**
     * added by Xu Ming:to query whether function called existed including inner
     * function and outer function
     * 
     * @param funcFullName
     *            FunctionFullName function's full name
     * @param args
     *            Object[] params of function
     * @return boolean true if fuctnion to query existed,false otherwise
     */
    public boolean isFuncExist ( FunctionFullName funcFullName, Object[] args )
    {
        // to ieai type arguments.
        Object[] ieaiTypeParams = new Object[args.length];
        for (int i = 0; i < args.length; i++)
        {
            ieaiTypeParams[i] = ExprEvaluator.toIEAIVlaue(args[i], null);
            if (!FuncHelper.isIDataType(ieaiTypeParams[i]))
            {
                return false;
            }
        }
        synchronized (_funcMap)
        {
            List iFuncList = (List) _funcMap.get(funcFullName);
            if (null == iFuncList || iFuncList.size() == 0)
            {
                return false;
            }

            Map tempMap = new HashMap();
            for (int i = 0; i < iFuncList.size(); i++)
            {
                IFunction tempiFunc = (IFunction) iFuncList.get(i);
                tempMap.put(new Integer(i), tempiFunc.getParamType());
            }
            int ret = FuncHelper.selectFunction(ieaiTypeParams, tempMap);
            if (FuncHelper.NOT_FOUND == ret)
            {
                return false;
            }
        }
        return true;
    }

    /**
     * @param funcFullName
     *            FunctionFullName
     * @param args
     *            Object[] it must be the value of ieai data type.
     * @throws FunctionException
     * @return Object
     */
    public Object call ( FunctionFullName funcFullName, Object[] args )
            throws FunctionException
    {
        // to ieai type arguments.
        Object[] ieaiTypeParams = new Object[args.length];
        for (int i = 0; i < args.length; i++)
        {
            ieaiTypeParams[i] = ExprEvaluator.toIEAIVlaue(args[i], null);
            if (!FuncHelper.isIDataType(ieaiTypeParams[i]))
            {
                throw new FunctionException("Invalid Parameters.");
            }
        }
        synchronized (_funcMap)
        {
            List iFuncList = (List) _funcMap.get(funcFullName);
            if (null == iFuncList || iFuncList.size() == 0)
            {
                throw new FunctionException("Function:" + funcFullName
                        + " not found.");
            }

            Map tempMap = new HashMap();
            for (int i = 0; i < iFuncList.size(); i++)
            {
                IFunction tempiFunc = (IFunction) iFuncList.get(i);
                tempMap.put(new Integer(i), tempiFunc.getParamType());
            }
            int ret = FuncHelper.selectFunction(ieaiTypeParams, tempMap);
            if (FuncHelper.NOT_FOUND == ret)
            {
                throw new FunctionException("Function:" + funcFullName
                        + " not found.");
            }
            if (FuncHelper.FOUND == ret)
            {
                Integer index = (Integer) FuncHelper.getKey();
                IFunction iFunc = (IFunction) iFuncList.get(index.intValue());
                return iFunc.call(ieaiTypeParams);
            }
            if (FuncHelper.NEED_CONVERT_TYPE == ret)
            {
                Integer index = (Integer) FuncHelper.getKey();
                IFunction iFunc = (IFunction) iFuncList.get(index.intValue());
                try
                {
                    return iFunc.call(FuncHelper.convertType(ieaiTypeParams,
                            iFunc.getParamType()));
                } catch (InvalidParamException ex)
                {
                    throw new FunctionException(ex);
                }
            }
        }
        throw new java.lang.IllegalStateException();
    }

    public JSScopeManager getJSScopeManager ()
    {
        return _jsScopeManager;
    }

    private void initFunction ( Project prj ) throws CompileFailedException,
            FileNotFoundException
    {
        Map innerFuncMap = new HashMap();
        Map outerFuncMap = new HashMap();
        getImportPrjInternalFuns(prj, innerFuncMap, outerFuncMap);

        _innerFuncs.addAll(prj.getInternalFunctions());
        for (Iterator iter = innerFuncMap.values().iterator(); iter.hasNext();)
        {
            _innerFuncs.addAll((List) iter.next());
        }

        _outerFuncs.addAll(prj.getExternalFunctions());
        for (Iterator iter = outerFuncMap.values().iterator(); iter.hasNext();)
        {
            _outerFuncs.addAll((List) iter.next());
        }
    }

    private void getImportPrjInternalFuns ( Project prj, Map innerFuncMap,
            Map outerFuncMap ) throws FileNotFoundException
    {
        Iterator iter = prj.getImpProjects().iterator();
        while (iter.hasNext())
        {
            ImpProject impPrj = (ImpProject) iter.next();
            List innerFuncList = new ArrayList();
            List outerFuncList = new ArrayList();
            List funcList = impPrj.getFunctions();
            for (Iterator iter1 = funcList.iterator(); iter1.hasNext();)
            {
                Function func = (Function) iter1.next();
                if (func instanceof InternalFunction)
                {
                    innerFuncList.add(func);
                } else
                {
                    outerFuncList.add(func);
                }
            }
            if (innerFuncMap != null)
            {
                innerFuncMap.put(impPrj.getName(), innerFuncList);
            }
            if (outerFuncMap != null)
            {
                outerFuncMap.put(impPrj.getName(), outerFuncList);
            }
        }
    }

    private Map getInnerFuncs ( Project prj ) throws FileNotFoundException
    {
        Map ret = new HashMap();
        String curPrjName = prj.getName();
        ret.put(curPrjName, prj.getInternalFunctions());
        Map importPrjInternalFuncs = new HashMap();
        getImportPrjInternalFuns(prj, importPrjInternalFuncs, null);
        ret.putAll(importPrjInternalFuncs);
        return ret;
    }

    private Map globalPredefinedVars ( Project curPrj, IExecContext ctx,
            IEAIRuntime runtime, Logger log )
    {
        Map map = new HashMap();
        map.put("runtime", runtime);
        map.put("project", curPrj);
        map.put("ctx", ctx);
        map.put("log", log);
        return map;
    }

    private Map prjPredefinedVars ( Project prj )
    {
        Map ret = new HashMap();
        /**
         * @todo need specify what var is predefined. If Env vars is predefined
         *       var, it wouldn't be modified in js. So, how to modify it?
         */
        // current project
        // Map curPrjMap = new HashMap();
        // curPrjMap.putAll(prjCtx.getAllEnvVarValue());
        // curPrjMap.putAll(prjCtx.getAllSharedResource());
        // ret.put(prjCtx.getProject().getName(), curPrjMap);
        // // import project
        // Map imEnvVarMap = prjCtx.getImportPrjCtx().getAllEnvVarMap();
        // ret.putAll(imEnvVarMap);
        //
        // Map imResMap = prjCtx.getImportPrjCtx().getAllSharedResourceMap();
        // for (Iterator iter = imResMap.keySet().iterator(); iter.hasNext();)
        // {
        // String prjName = (String) iter.next();
        // if (imEnvVarMap.get(prjName) != null)
        // {
        // ((Map) imEnvVarMap.get(prjName)).putAll((Map) imResMap
        // .get(prjName));
        // } else
        // {
        // ret.put(prjName, imResMap.get(prjName));
        // }
        // }
        return ret;
    }

    private void addIFunction ( FunctionFullName funcFullName, IFunction iFunc )
    {
        synchronized (_funcMap)
        {
            List l = (List) _funcMap.get(funcFullName);
            if (null == l)
            {
                l = new ArrayList();
                _funcMap.put(funcFullName, l);
            }
            l.add(iFunc);
        }
    }

    private void initDayStartFunction ( String functionName ) throws CompileFailedException, FileNotFoundException
    {
        Map innerFuncMap = new HashMap();
        // Map outerFuncMap = new HashMap();
        // getImportPrjInternalFuns(prj, innerFuncMap, outerFuncMap);

        _innerFuncs.add(functionName);
        // for (Iterator iter = innerFuncMap.values().iterator(); iter.hasNext();)
        // {
        // _innerFuncs.addAll((List) iter.next());
        // }

        // _outerFuncs.addAll(prj.getExternalFunctions());
        // for (Iterator iter = outerFuncMap.values().iterator(); iter.hasNext();)
        // {
        // _outerFuncs.addAll((List) iter.next());
        // }
    }

    private Map getDayStartInnerFuncs ( Project prj, String functionName ) throws FileNotFoundException
    {
        Map ret = new HashMap();
        String curPrjName = prj.getName();
        ret.put(curPrjName, functionName);
        // Map importPrjInternalFuncs = new HashMap();
        // getImportPrjInternalFuns(prj, importPrjInternalFuncs, null);
        // ret.putAll(importPrjInternalFuncs);
        return ret;
    }
}
