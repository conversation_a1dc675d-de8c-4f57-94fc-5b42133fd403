<!doctype html>

<title>CodeMirror: Factor mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link href='http://fonts.googleapis.com/css?family=Droid+Sans+Mono' rel='stylesheet' type='text/css'>
<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/mode/simple.js"></script>
<script src="factor.js"></script>
<style>
.CodeMirror {
    font-family: 'Droid Sans Mono', monospace;
    font-size: 14px;
}
</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Factor</a>
  </ul>
</div>

<article>

<h2>Factor mode</h2>

<form><textarea id="code" name="code">
! Copyright (C) 2008 Slava Pestov.
! See http://factorcode.org/license.txt for BSD license.

! A simple time server

USING: accessors calendar calendar.format io io.encodings.ascii
io.servers kernel threads ;
IN: time-server

: handle-time-client ( -- )
    now timestamp>rfc822 print ;

: <time-server> ( -- threaded-server )
    ascii <threaded-server>
        "time-server" >>name
        1234 >>insecure
        [ handle-time-client ] >>handler ;

: start-time-server ( -- )
    <time-server> start-server drop ;

MAIN: start-time-server
</textarea>
  </form>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    lineNumbers: true,
    lineWrapping: true,
    indentUnit: 2,
    tabSize: 2,
    autofocus: true,
    mode: "text/x-factor"
  });
</script>
<p/>
<p>Simple mode that handles Factor Syntax (<a href="http://en.wikipedia.org/wiki/Factor_(programming_language)">Factor on WikiPedia</a>).</p>

<p><strong>MIME types defined:</strong> <code>text/x-factor</code>.</p>

</article>
