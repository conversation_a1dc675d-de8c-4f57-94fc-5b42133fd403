	-- 4.7.17 version ZB patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
		BEGIN

		CREATE TABLE IF NOT EXISTS IEAI_DATASYNC_TABLEINFO(IID DECIMAL(19,0) NOT NULL ,SYNCTABLENAME VARCHAR(255),ORDERID DECIMAL(19,0),CONSTRAINT P_KEY_IEAI_DATASYNC_TABLEINFO PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_DATASYNC_CONFIGINFO(IID DECIMAL(19,0) NOT NULL ,IDBTYPE VARCHAR(2),<PERSON><PERSON><PERSON><PERSON> VARCHAR(1000),IDBUSER VARCHAR(255),IDBPASS VARCHAR(255),IISMAIN VARCHAR(2),CONSTRAINT P_KEY_IEAI_DATASYNC_CONFIGINFO PRIMARY KEY (IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_SHELLCMD_OUTPUT' AND INDEX_NAME = 'IDX_ISTEPID_IEAI_SHELLCMD_OUTPUT') THEN
			CREATE INDEX IDX_ISTEPID_IEAI_SHELLCMD_OUTPUT ON IEAI_SHELLCMD_OUTPUT (ISTEPID);
		END IF;
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_DATARELATION(IID NUMERIC(19,0) NOT NULL,ITABLENAME VARCHAR(255),ICMDBTYPE NUMERIC(19,0),ICREATEUSERID NUMERIC(19,0),IORDER NUMERIC(19,0),IIDCOLUMN VARCHAR(255),CONSTRAINT PK_IEAI_CMDB_DATARELATION PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_DATARELATION_CONFIG(IID NUMERIC(19,0) NOT NULL,IRELATIONID NUMERIC(19,0),ITABLECOLUMN VARCHAR(255),ITYPECOLUMN VARCHAR(255),IISMAIN VARCHAR(5),CONSTRAINT PK_IEAI_CMDB_DATARELATION_CONF PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_WARNINFO(IID NUMERIC(19,0) NOT NULL,ITYPEID NUMERIC(19,0),IASSETID NUMERIC(19,0),IWARNINFO TEXT,IGETTIME NUMERIC(19,0),CONSTRAINT PK_IEAI_CMDB_WARNINFO PRIMARY KEY (IID));
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.18 version ZB patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
		BEGIN
		
		CREATE TABLE IF NOT EXISTS IEAI_SCENECHANGE(IID NUMERIC(19,0) NOT NULL,ICHANGENAME VARCHAR(255),ICHANGEDESC VARCHAR(255),ISYSID NUMERIC(19,0),CONSTRAINT PK_IEAI_SCENECHANGE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SCENECHANGE_CONFIG(IID NUMERIC(19,0) NOT NULL,ICHANGEID NUMERIC(19,0),ICOLUMNNAME VARCHAR(255),ICOLUMNCODE VARCHAR(255),CONSTRAINT PK_IEAI_SCENECHANGE_CONFIG PRIMARY KEY (IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCENECHANGE_CONFIG' AND COLUMN_NAME = 'ISVALUEHIDDEN') THEN
			ALTER TABLE IEAI_SCENECHANGE_CONFIG ADD ISVALUEHIDDEN NUMERIC(1,0) default 0;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_GF_XMDB_SYS(IID NUMERIC(19) NOT NULL,IINSTID VARCHAR(255),IFIRSTSYS VARCHAR(255),ISECONDAPP VARCHAR(255),ICISTATUS VARCHAR(255),IUSEFOR VARCHAR(255),IAROLE VARCHAR(255),IBROLE VARCHAR(255),CONSTRAINT PK_IEAI_GF_XMDB_SYS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_GF_XMDB_COMPUTER(IID NUMERIC(19) NOT NULL,IINSTID VARCHAR(255),ICISTATUS VARCHAR(255),IUSEFOR VARCHAR(255),IOSTYPE VARCHAR(255),IBACKUPIP VARCHAR(255),IHOSTNAME VARCHAR(255),ICIOWNER VARCHAR(255),CONSTRAINT PK_IEAI_GF_XMDB_COMPUTER PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_GF_XMDB_RELATION (IID NUMERIC(19) NOT NULL,ISYSID NUMERIC(19),ICOMPUTERID NUMERIC(19),IFLAG VARCHAR(255),CONSTRAINT PK_IEAI_GF_XMDB_RELATION PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE IID = 4034) THEN
            INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(4034, '场景切换', 4, 'scenechange.do', 24, '', 'images/info88.png');
        END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_GETBYAGENT(IID DECIMAL(19) NOT NULL,IIP VARCHAR(20),IPORT VARCHAR(20),INAME VARCHAR(255),ISHELL TEXT,ITYPE VARCHAR(5),CONSTRAINT PK_IEAI_CMDB_GETBYAGENT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_GETBYSNMP(IID DECIMAL(19) NOT NULL,IVERSION VARCHAR(20),IAGENTIP VARCHAR(20),IIP VARCHAR(20),IPORT VARCHAR(20),ICOMMUNITY VARCHAR(20),IUSERNAME VARCHAR(255),IOUTTIME DECIMAL(3),ISELFFINDTYPE DECIMAL(19),ISAVETYPE DECIMAL(19),IRETRY DECIMAL(3),ITYPE VARCHAR(5),IENGINEID VARCHAR(50),ICONTEXTNAME VARCHAR(255),IAUTH VARCHAR(50),IAUTHKEY VARCHAR(255),IPRIV VARCHAR(50),IPRIVKEY VARCHAR(255),ICMDS VARCHAR(1000), CONSTRAINT PK_IEAI_CMDB_GETBYSNMP PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_CMDB_GETDATARELATION(IID DECIMAL(19) NOT NULL,IFLOWID DECIMAL(19) NOT NULL,IINFOID DECIMAL(19) NOT NULL,IGETTYPE VARCHAR(50),ISTATUS DECIMAL(1) DEFAULT 0,CONSTRAINT PK_IEAI_CMDB_GETDATARELATION PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_GETBYSSH' AND COLUMN_NAME = 'IAGENTIP') THEN
			ALTER TABLE IEAI_CMDB_GETBYSSH ADD IAGENTIP VARCHAR(20);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_GETBYSSH' AND COLUMN_NAME = 'ITYPE') THEN
			ALTER TABLE IEAI_CMDB_GETBYSSH ADD ITYPE VARCHAR(5);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_INFO' AND COLUMN_NAME = 'IUPDATETIME') THEN
			ALTER TABLE IEAI_CMDB_INFO ADD IUPDATETIME NUMERIC(19);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.19 version ZB patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_GF_XMDB_CHECKCONFIG (IID NUMERIC(19,0) NOT NULL,IPROJECTID NUMERIC(19,0),ICHECKITEM NUMERIC(1,0),CONSTRAINT PK_IEAI_GF_XMDB_CHECKCONFIG PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_GF_XMDB_CHECKRESULT (IID NUMERIC(19,0) NOT NULL,IPROJECTID NUMERIC(19,0),ICHECKRESULT VARCHAR(255),ICHECKTIME NUMERIC(19,0),ICHECKITEM NUMERIC(1,0),CONSTRAINT PK_IEAI_GF_XMDB_CHECKRESULT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_CONFIG (IID NUMERIC(19) NOT NULL,IPARAMNAME VARCHAR(255),IPARAMCODE VARCHAR(255),IISENCRYPT NUMERIC(1),IPARAMVALUE VARCHAR(1000),CONSTRAINT PK_IEAI_SWITCH_CONFIG PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IOWNERTYPE') THEN
			ALTER TABLE IEAI_INSTANCEINFO ADD IOWNERTYPE DECIMAL(1) DEFAULT -1 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IOWNER') THEN
			ALTER TABLE IEAI_INSTANCEINFO ADD IOWNER VARCHAR(255) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'IOWNERTYPE') THEN
			ALTER TABLE IEAI_INSTANCEINFO_COPY ADD IOWNERTYPE DECIMAL(1) DEFAULT -1 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'IOWNER') THEN
			ALTER TABLE IEAI_INSTANCEINFO_COPY ADD IOWNER VARCHAR(255) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'IPREACTNAME') THEN
			ALTER TABLE IEAI_INSTANCEINFO_COPY ADD IPREACTNAME VARCHAR(1000) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IOWNERTYPE') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IOWNERTYPE DECIMAL(1) DEFAULT -1 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IOWNER') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IOWNER VARCHAR(255) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IOWNERTYPE') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IOWNERTYPE DECIMAL(1) DEFAULT -1 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IOWNER') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IOWNER VARCHAR(255) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 46) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (46, 1, 'ipreactname', '系统间依赖', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 47) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (47, 1, 'itimeout', '超时时间', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 48) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (48, 1, 'iownertype', '负责人类型', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 49) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (49, 1, 'iowner', '负责人', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 50) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (50, 2, 'ipreactname', '系统间依赖', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 51) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (51, 2, 'itimeout', '超时时间', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 52) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (52, 2, 'iownertype', '负责人类型', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 53) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (53, 2, 'iowner', '负责人', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 54) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (54, 3, 'ipreactname', '系统间依赖', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 55) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (55, 3, 'itimeout', '超时时间', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 56) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (56, 3, 'iownertype', '负责人类型', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 57) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (57, 3, 'iowner', '负责人', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 58) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (58, 4, 'ipreactname', '系统间依赖', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 59) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (59, 4, 'itimeout', '超时时间', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 60) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (60, 4, 'iownertype', '负责人类型', NULL);
		END IF;
		IF NOT EXISTS (SELECT * FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE  IID = 61) THEN
			INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (61, 4, 'iowner', '负责人', NULL);
		END IF;		
		IF NOT EXISTS (SELECT * FROM IEAI_PARAMETER_CONFIG WHERE  IID = 24) THEN
			INSERT INTO  IEAI_PARAMETER_CONFIG(IID, IPARAMETER,iparavalue)  VALUES(24, 'issimulation', '0');
		END IF;	
		IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID = 4036) THEN
			INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(4036, '拓扑监控', 4, 'forwardTopoSwitchMonitor.do', 7, '', 'images/info88.png');
		END IF;					
		IF NOT EXISTS (SELECT * FROM IEAI_MENU WHERE  IID = 4038) THEN
			INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(4038, '参数配置', 4, 'initSwitchConfigPage.do', 26, '', 'images/info88.png');
		END IF;				
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDBMANAGEMENT' AND COLUMN_NAME = 'ICODE') THEN
			ALTER TABLE IEAI_CMDBMANAGEMENT ADD ICODE VARCHAR(255) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE' AND COLUMN_NAME = 'ITYPECODE') THEN
			ALTER TABLE IEAI_CMDB_TYPE ADD ITYPECODE VARCHAR(255) ;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_DOUBLECHECK_SIGN(IID NUMERIC(19,0) NOT NULL,IDOUBLECHECKID NUMERIC(19,0),IOWNERTYPE NUMERIC(1,0),IOWNER VARCHAR(255),ISTATUS NUMERIC(1,0),IPROLOGICID NUMERIC(19,0),ISWITCHREASON VARCHAR(255),CONSTRAINT PK_IEAI_DOUBLECHECK_SIGN PRIMARY KEY (IID));
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_CMDB_TYPE_ATTRIBUTE' AND COLUMN_NAME = 'IISIGNORECHECK') THEN
			ALTER TABLE IEAI_CMDB_TYPE_ATTRIBUTE ADD IISIGNORECHECK DECIMAL(1) DEFAULT 0;
		END IF;
		
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_SEGMENT( IID DECIMAL(19,0) NOT NULL, INAME 	VARCHAR(255) , IORDER	DECIMAL(3,0) , IDESC 	VARCHAR(512) , IPARENTID DECIMAL(19,0), CONSTRAINT PK_IEAI_SWITCH_SEGMENT PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_SEGMENT_RELATION ( IID DECIMAL(19,0) NOT NULL, ISEGMENTID DECIMAL(19,0), ISYSTEMID DECIMAL(19,0), ISWITCHDIRID	DECIMAL(19,0), CONSTRAINT PK_SWITCH_SEGMENT_RELATION PRIMARY KEY(IID));
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.20 version ZB patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
		BEGIN
		
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IOLDIIP') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IOLDIIP VARCHAR(1000);
			END IF;
			
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IOLDIIP') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IOLDIIP VARCHAR(1000);
			END IF;
			
			
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DOUBLECHECK_SIGN' AND COLUMN_NAME = 'IINSTANCEID') THEN
				ALTER TABLE IEAI_DOUBLECHECK_SIGN ADD IINSTANCEID NUMERIC(19);
			END IF;
			
			IF NOT EXISTS (SELECT * FROM IEAI_PARAMETER_CONFIG  WHERE  IID=25) THEN
				INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES (25, 'iswitchname', '');
			END IF;
			
			IF NOT EXISTS (SELECT * FROM IEAI_PARAMETER_CONFIG  WHERE  IID=26) THEN
				INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES (26, 'ioneswitchname', '福厦两中心业务连续性指挥系统');
			END IF;			
			COMMIT;
			
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IPROJECTNAME') THEN
				ALTER TABLE IEAI_INSTANCEINFO ADD IPROJECTNAME VARCHAR(255);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'IPROJECTNAME') THEN
				ALTER TABLE IEAI_INSTANCEINFO_COPY ADD IPROJECTNAME VARCHAR(255);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IPROJECTNAME') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IPROJECTNAME VARCHAR(255);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IPROJECTNAME') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IPROJECTNAME VARCHAR(255);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SWITCH_SEGMENT' AND COLUMN_NAME = 'ILOGIC_ID') THEN
				ALTER TABLE IEAI_SWITCH_SEGMENT ADD ILOGIC_ID NUMERIC(19);
			END IF;	
			
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IARRIVETIME') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IARRIVETIME NUMERIC(19);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IISMANUAL') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IISMANUAL NUMERIC(1);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IARRIVETIME') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IARRIVETIME NUMERIC(19);
			END IF;
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IISMANUAL') THEN
				ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IISMANUAL NUMERIC(1);
			END IF;
            IF  EXISTS (SELECT * FROM IEAI_HIGHOPER WHERE IBUTTONID=4022) THEN
                 UPDATE IEAI_HIGHOPER SET IBUTTONURL='commitSwitchDirOperation.do' WHERE IBUTTONID=4022;
				 commit;
             END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS PROC_RUN_INSTANCE_NOCONNER;;
		CREATE PROCEDURE PROC_RUN_INSTANCE_NOCONNER(IN FLAGTYPE NUMERIC(19,0),IN SERVERIP VARCHAR(255),OUT AN_OPT_ID NUMERIC(19,0))
			BEGIN
				DECLARE IS_COUNT  NUMERIC(19);
				DECLARE	LN_OUT_OPT_ID	NUMERIC(19,0);		SELECT
				COUNT(*) 
				INTO
					IS_COUNT 
				FROM
					IEAI_RUN_INSTANCE RI,
					IEAI_RUNINFO_INSTANCE R 
				WHERE
					RI.IID=R.IRUNINSID AND
						RI.IMXGRAPHID IS NULL AND
					R.IRERUNFLAG=0 AND
					RI.ISTATE=0 AND
					R.ISTATE IN (0,1) AND
					RI.ISERVERIP=SERVERIP;
				
				IF IS_COUNT > 0 THEN
					CALL	PROC_GET_NEXT_ID ('TMP_RUN_INSTANCE', LN_OUT_OPT_ID);
					SET	AN_OPT_ID = LN_OUT_OPT_ID;		INSERT
					INTO
						TMP_RUN_INSTANCE(OPID,
						MAINID,
						WORKID,
						IRUNINSNAME,
						ISERNER,
						ICONNER,
						IPRENER,
						IACTNAME,
						IACTDES,
						IACTTYPE,
						IREMINFO,
						IIP,
						IPORT,
						ISYSTYPE,
						ISHELLPATH,
						ITIMEOUT,
						ISWITCHSYSTYPE,
						IPARAMETER,
						IPARACHECK,
						IPARASWITCH,
						IPARASWITCHFORCE,
						IUSERNAME,
						ISHELLSCRIPT,
						IUSERID,
						IOPERTYPE,
						IEXECUSER,
						IEXPECEINFO,
						IISLOADENV ,
						ICONNERNAME,
						ICENTER ,
						IMODELTYPE ,
						IRETNVALEXCEPION ,
						IREDOABLE,
						IPKGNAME,
						IVERSION,
						IPRESYSNAME,
						IPREACTNAME,
						ISPACEIID,
							IGOURPIID) 
					SELECT
						DISTINCT concat(LN_OUT_OPT_ID,'') ,
						BB.IID,
						AA.IID AS ID ,
						BB.IRUNINSNAME ,
						AA.ISERNER ,
						AA.ICONNER ,
						AA.IPRENER ,
						AA.IACTNAME ,
						AA.IACTDES ,
						AA.IACTTYPE ,
						AA.IREMINFO ,
						AA.IIP ,
						AA.IPORT ,
						BB.ISYSTYPE ,
						AA.ISHELLPATH ,
						AA.ITIMEOUT ,
						AA.ISWITCHSYSTYPE ,
						AA.IPARAMETER ,
						AA.IPARACHECK ,
						AA.IPARASWITCH ,
						AA.IPARASWITCHFORCE ,
						BB.ISTARTUSER ,
						AA.ISHELLSCRIPT ,
						BB.ISTARTUSERID ,
						AA.ISYSTYPE ,
						AA.IEXECUSER ,
						AA.IEXPECEINFO ,
						AA.IISLOADENV ,
						AA.ICONNERNAME,
						AA.ICENTER ,
						AA.IMODELTYPE ,
						AA.IRETNVALEXCEPION ,
						AA.IREDOABLE ,
						AA.IPKGNAME,
						BB.IVERSION,
						AA.IPRESYSNAME,
						AA.IPREACTNAME,
						AA.ISPACEIID,
						AA.IGOURPIID
					FROM
						IEAI_RUNINFO_INSTANCE AA ,
						(	SELECT
								RI.IID ,								
								RI.IRUNINSNAME ,
								RI.ISYSTYPE ,
								RI.ISTARTUSER ,
								RI.ISERVERIP,
								RI.ISTARTUSERID ,
								RI.IVERSION 
							FROM
								IEAI_RUN_INSTANCE RI ,
								IEAI_RUNINFO_INSTANCE R 
							WHERE
								RI.IID=R.IRUNINSID AND
									RI.IMXGRAPHID IS NULL AND
								RI.ISTATE=0 AND
								RI.ISERVERIP=concat(SERVERIP,'') AND
								R.ISTATE=1 
							GROUP BY
								RI.IID,
								RI.IRUNINSNAME,
								RI.ISYSTYPE,
								RI.ISTARTUSER,
								RI.ISERVERIP,
								RI.ISTARTUSERID,
								RI.IVERSION ) BB 
					WHERE
						AA.IRUNINSID=BB.IID AND						
						AA.ISTATE=1 
					ORDER BY
						AA.ICONNER ASC ,
						AA.IPRENER DESC;	END IF;
			END;;
		DELIMITER ;;
		
		-- 4.7.21 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		DECLARE  target_database VARCHAR(100);
		SELECT DATABASE() INTO target_database;
		IF NOT EXISTS (SELECT * FROM information_schema.columns WHERE table_schema = target_database AND table_name = 'IEAI_ADAPTOR_PROPERTY_KEY_WS' AND column_name = 'ITYPE') THEN
		ALTER TABLE IEAI_ADAPTOR_PROPERTY_KEY_WS ADD ITYPE VARCHAR(255) null;
		END IF;	
		IF NOT EXISTS (SELECT * FROM information_schema.columns WHERE table_schema = target_database AND table_name = 'IEAI_ADAPTOR_PROPERTY_KEY_WS' AND column_name = 'ICONTEXT') THEN
		ALTER TABLE IEAI_ADAPTOR_PROPERTY_KEY_WS ADD ICONTEXT VARCHAR(4000) null;
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
        
         UPDATE  IEAI_ADAPTOR_PROPERTY_KEY_WS SET ICONTEXT='{"actpurl":"","localdata":[{"type":"UNIX/LINUX"},{"type":"WINDOWS"}],"model":[{"name":"type","type":"string"}],"combox":{"valueField":"type","displayField":"type"}}' , ITYPE ='combox' where IID=8;
         UPDATE  IEAI_ADAPTOR_PROPERTY_KEY_WS SET ICONTEXT='{"actpurl":"","localdata":[{"type":"UNIX/LINUX"},{"type":"WINDOWS"}],"model":[{"name":"type","type":"string"}],"combox":{"valueField":"type","displayField":"type"}}' , ITYPE ='combox' where IID=25;
         UPDATE  IEAI_ADAPTOR_PROPERTY_KEY_WS SET ICONTEXT='{"actpurl":"","localdata":[{"type":"UNIX/LINUX"},{"type":"WINDOWS"}],"model":[{"name":"type","type":"string"}],"combox":{"valueField":"type","displayField":"type"}}' , ITYPE ='combox' where IID=38;	

			UPDATE IEAI_MENU SET IURL='initEsbusinesstypePage.do' WHERE IID=4003;
			UPDATE IEAI_HIGHOPER SET IBUTTONURL='saveEsbusinesstype.do',IBUTTONDES='系统分类-保存' WHERE IBUTTONID=4002;
			
			UPDATE IEAI_MENU SET IURL='eshdinfoexecl.do' WHERE IID=4005;
			UPDATE IEAI_HIGHOPER SET IBUTTONURL='eshduploadSusExecl.do',IBUTTONDES='系统录入-上传' WHERE IBUTTONID=4010;
			COMMIT;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 4.7.22 version does not have patches for ZB		
		-- 4.7.23 version ZB patch is as follows
        DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_PARAMETER_CONFIG  WHERE  IID=27) THEN
			INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(27,'switchCheckScript','switch_check_script');
		END IF;
		UPDATE  IEAI_ADAPTOR_PROPERTY_KEY_WS SET ICONTEXT='{"actpurl":"getScriptServiceList1.do","model":[{"name":"serviceName","type":"string","text":"服务名称"},{"name":"params","type":"string","text":"参数类型"}],"root":"scriptTypebeans"}' , ITYPE ='popupwin' where IID=9;
		COMMIT;  

		CREATE TABLE IF NOT EXISTS IEAI_GRID_INFO (IID NUMERIC(19) NOT NULL ,IGRIDNAME VARCHAR(255) ,ICOLNAME VARCHAR(255) ,IATTRIBUTE VARCHAR(255) ,IATTRVALUE VARCHAR(255) ,CONSTRAINT PK_IEAI_GRID_INFO PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_NOTICE_CONTENT (IID NUMERIC(19) NOT NULL ,ITITLE VARCHAR(255) ,ICONTENT VARCHAR(255) ,ICREATETIME NUMERIC(19) ,ICREATEUSER VARCHAR(255) ,CONSTRAINT PK_IEAI_NOTICE_CONTENT PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_NOTICE_PERSON (IID NUMERIC(19) NOT NULL ,IRESOURCENAME VARCHAR(255) ,IPERSONNAME VARCHAR(255) ,ITELPHONE VARCHAR(255) ,IDEPARTMENT VARCHAR(255) ,ILOGINNAME VARCHAR(255) ,CONSTRAINT PK_IEAI_NOTICE_PERSON PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_NOTICE_RELATION (IID NUMERIC(19) NOT NULL ,ICONTENTID NUMERIC(19) ,IPERSONID NUMERIC(19) ,ICREATETIME NUMERIC(19) ,ICONFIRMTIME NUMERIC(19) ,ISTATE NUMERIC(1) ,CONSTRAINT PK_IEAI_NOTICE_RELATION PRIMARY KEY (IID));

		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=1) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (1, 'switchmonitor/switchmonitorsys_grid', 'iruninsname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=2) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (2, 'switchmonitor/switchmonitorsys_grid', 'iswitchto', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=3) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (3, 'switchmonitor/switchmonitorsys_grid', 'isysname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=4) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (4, 'switchmonitor/switchmonitorsys_grid', 'finishcount', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=5) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (5, 'switchmonitor/switchmonitorsys_grid', 'istarttime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=6) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (6, 'switchmonitor/switchmonitorsys_grid', 'iendtime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=7) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (7, 'switchmonitor/switchmonitorsys_grid', 'iruntime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=8) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (8, 'switchmonitor/switchmonitorsys_grid', 'istate', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=9) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (9, 'switchmonitor/switchmonitorsys_grid', 'iisfail', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=10) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (10, 'switchmonitor/switchmonitorstep_grid', 'isysname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=11) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (11, 'switchmonitor/switchmonitorstep_grid', 'iruninsname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=12) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (12, 'switchmonitor/switchmonitorstep_grid', 'iacttype', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=13) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (13, 'switchmonitor/switchmonitorstep_grid', 'iconnername', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=14) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (14, 'switchmonitor/switchmonitorstep_grid', 'iserner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=15) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (15, 'switchmonitor/switchmonitorstep_grid', 'iconner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=16) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (16, 'switchmonitor/switchmonitorstep_grid', 'iprener', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=17) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (17, 'switchmonitor/switchmonitorstep_grid', 'iswitchto', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=18) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (18, 'switchmonitor/switchmonitorstep_grid', 'icenter', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=19) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (19, 'switchmonitor/switchmonitorstep_grid', 'iprojectname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=20) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (20, 'switchmonitor/switchmonitorstep_grid', 'iactname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=21) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (21, 'switchmonitor/switchmonitorstep_grid', 'iip', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=22) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (22, 'switchmonitor/switchmonitorstep_grid', 'iactdes', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=23) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (23, 'switchmonitor/switchmonitorstep_grid', 'ishellpath', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=24) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (24, 'switchmonitor/switchmonitorstep_grid', 'iarrivetime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=25) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (25, 'switchmonitor/switchmonitorstep_grid', 'itimeOut', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=26) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (26, 'switchmonitor/switchmonitorstep_grid', 'istarttime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=27) THEN
			 INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (27, 'switchmonitor/switchmonitorstep_grid', 'iendtime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=28) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (28, 'switchmonitor/switchmonitorstep_grid', 'iruntime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=29) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (29, 'switchmonitor/switchmonitorstep_grid', 'istate', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=30) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (30, 'switchmonitor/switchmonitorstep_grid', 'iismanual', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=31) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (31, 'switchmonitor/switchmonitorstep_grid', 'iisfail', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=32) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (32, 'switchmonitor/switchmonitorstep_grid', 'iownertype', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=33) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (33, 'switchmonitor/switchmonitorstep_grid', 'iowner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=34) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (34, 'switchmonitor/switchmonitorstep_grid', 'ipreactname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=35) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (35, 'switchmonitor/switchmonitorstep_grid', 'ireminfo', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=36) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (36, 'switchrunins/switchrunins_grid', 'iruninsname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=37) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (37, 'switchrunins/switchrunins_grid', 'isysname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=38) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (38, 'switchrunins/switchrunins_grid', 'istarttime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=39) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (39, 'switchrunins/switchrunins_grid', 'iendtime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=40) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (40, 'switchrunins/switchrunins_grid', 'iruntime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=41) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (41, 'switchrunins/switchrunins_grid', 'istate', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=42) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (42, 'switchrunins/switchrunins_grid', 'iisfail', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=43) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (43, 'switchruninfoins/switchrunins_grid', 'isysname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=44) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (44, 'switchruninfoins/switchrunins_grid', 'iruninsname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=45) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (45, 'switchruninfoins/switchrunins_grid', 'iacttype', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=46) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (46, 'switchruninfoins/switchrunins_grid', 'icenter', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=47) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (47, 'switchruninfoins/switchrunins_grid', 'iconnername', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=48) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (48, 'switchruninfoins/switchrunins_grid', 'iconner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=49) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (49, 'switchruninfoins/switchrunins_grid', 'iserner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=50) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (50, 'switchruninfoins/switchrunins_grid', 'iprener', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=51) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (51, 'switchruninfoins/switchrunins_grid', 'iswitchto', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=52) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (52, 'switchruninfoins/switchrunins_grid', 'iactname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=53) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (53, 'switchruninfoins/switchrunins_grid', 'iactdes', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=54) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (54, 'switchruninfoins/switchrunins_grid', 'itimeOut', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=55) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (55, 'switchruninfoins/switchrunins_grid', 'iownertype', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=56) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (56, 'switchruninfoins/switchrunins_grid', 'iowner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=57) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (57, 'switchruninfoins/switchrunins_grid', 'ipreactname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=58) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (58, 'switchruninfoins/switchrunins_grid', 'iip', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=59) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (59, 'switchruninfoins/switchrunins_grid', 'ishellpath', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=60) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (60, 'switchruninfoins/switchrunins_grid', 'istarttime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=61) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (61, 'switchruninfoins/switchrunins_grid', 'iendtime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=62) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (62, 'switchruninfoins/switchrunins_grid', 'iruntime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=63) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (63, 'switchruninfoins/switchrunins_grid', 'istate', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=64) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (64, 'switchruninfoins/switchrunins_grid', 'iisfail', 'ishidden', '');
		END IF;
		COMMIT;
		
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=65) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (65, 'toposwitchmonitor/toposwitchmonitorgrid1', 'isysname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=66) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (66, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iruninsname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=67) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (67, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iacttype', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=68) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (68, 'toposwitchmonitor/toposwitchmonitorgrid1', 'icenter', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=69) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (69, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iconnername', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=70) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (70, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iconner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=71) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (71, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iserner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=72) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (72, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iprener', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=73) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (73, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iswitchto', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=74) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (74, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iactname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=75) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (75, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iactdes', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=76) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (76, 'toposwitchmonitor/toposwitchmonitorgrid1', 'itimeOut', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=77) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (77, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iownertype', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=78) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (78, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iowner', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=79) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (79, 'toposwitchmonitor/toposwitchmonitorgrid1', 'ipreactname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=80) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (80, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iip', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=81) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (81, 'toposwitchmonitor/toposwitchmonitorgrid1', 'ishellpath', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=82) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (82, 'toposwitchmonitor/toposwitchmonitorgrid1', 'istarttime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=83) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (83, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iendtime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=84) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (84, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iruntime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=85) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (85, 'toposwitchmonitor/toposwitchmonitorgrid1', 'istate', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=86) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (86, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iisfail', 'ishidden', '');
		END IF;
		COMMIT;	
		
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=87) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (87, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iprojectname', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=88) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (88, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iarrivetime', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=89) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (89, 'toposwitchmonitor/toposwitchmonitorgrid1', 'iismanual', 'ishidden', '');
		END IF;
		COMMIT;
		IF NOT EXISTS (SELECT * FROM IEAI_GRID_INFO  WHERE  IID=90) THEN
			INSERT INTO IEAI_GRID_INFO (IID, IGRIDNAME, ICOLNAME, IATTRIBUTE, IATTRVALUE) VALUES (90, 'toposwitchmonitor/toposwitchmonitorgrid1', 'ireminfo', 'ishidden', '');
		END IF;
		COMMIT;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 4.7.24 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		
		CREATE TABLE IF NOT EXISTS IEAI_SCENEAPPROVAL_RELATION (IID NUMERIC(19) NOT NULL ,ISCENEID NUMERIC(19) ,ICODE VARCHAR(30) ,IBUSINESSTYPE NUMERIC(5) DEFAULT 0 );
		CREATE TABLE IF NOT EXISTS IEAI_SWITCHSCENE_TYPE (IID NUMERIC(19) NOT NULL ,ISCENETYPENAME VARCHAR(255) ,ISCENETYPEDESC VARCHAR(255) ,ISCENETYPEREMARK VARCHAR(255) ,CONSTRAINT PK_IEAI_SWITCHSCENE_TYPE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SWITCHSCENE (IID NUMERIC(19) NOT NULL ,ISCENENAME VARCHAR(255) ,ISCENEDESC VARCHAR(255) ,ISCENEREMARK VARCHAR(255) ,ISCENETYPEID NUMERIC(19) ,CONSTRAINT PK_IEAI_SWITCHSCENE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SCENEPROLOGIC_RELATION (IID NUMERIC(19) NOT NULL ,ISWITCHSCENEID NUMERIC(19) ,ILOGICID NUMERIC(19) ,CONSTRAINT PK_IEAI_SCENEPROLOGIC_RELATION PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_TEMPLATE (IID NUMERIC(19) NOT NULL ,ICODE VARCHAR(30) ,IAPPROVALFLOWNAME VARCHAR(255) ,IAPPROVALFLOWDESC VARCHAR(500) ,IAPPROVALFLOWGRAPH LONGTEXT ,CONSTRAINT PK_IEAI_FLOW_TEMPLATE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_NODE (IID NUMERIC(19) NOT NULL ,IMODULEID NUMERIC(19) ,IGRAPHID NUMERIC(19) ,INODENAME VARCHAR(255) ,IUSERID NUMERIC(19) ,CONSTRAINT PK_IEAI_FLOW_NODE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_NODERELATION (IID NUMERIC(19) NOT NULL ,IMODULEID NUMERIC(19) ,INODEID NUMERIC(19) ,IRELATIONTYPE NUMERIC(2) ,IRELATIONNODEID NUMERIC(19) ,CONSTRAINT PK_IEAI_FLOW_NODERELATION PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_INSTANCE (IID NUMERIC(19) NOT NULL ,IMODULEID NUMERIC(19) ,ITASKID NUMERIC(19) ,ISTATUS NUMERIC(5) DEFAULT 100 ,ICODE VARCHAR(30) ,IAPPROVALFLOWNAME VARCHAR(255) ,IAPPROVALFLOWDESC VARCHAR(500) ,IAPPROVALFLOWGRAPH LONGTEXT ,CONSTRAINT PK_IEAI_FLOW_INSTANCE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_NODEINSTANCE (IID NUMERIC(19) NOT NULL ,IINSTANCEID NUMERIC(19) ,IGRAPHID NUMERIC(19) ,INODENAME VARCHAR(255) ,IUSERID NUMERIC(19) );
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_RELATIONINSTANCE (IID NUMERIC(19) NOT NULL ,IINSTANCEID NUMERIC(19) ,INODEID NUMERIC(19) ,IRELATIONTYPE NUMERIC(2) ,IRELATIONNODEID NUMERIC(19) );
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_TASKINSTANCE (IID NUMERIC(19) NOT NULL ,IINSTANCEID NUMERIC(19) ,INODEID NUMERIC(19) ,INODEORDER NUMERIC(2) ,INODEMARK NUMERIC(5) ,IUSERID NUMERIC(19) ,IRESULT NUMERIC(1) ,IAPPROVALREMARK VARCHAR(500) ,IATTACHID NUMERIC(19) ,IAPPROVALTIME TIMESTAMP ,CONSTRAINT PK_IEAI_FLOW_TASKINSTANCE PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SWITCHAPPROVE_TASK (IID NUMERIC(19) NOT NULL ,ISCENEID NUMERIC(19) ,ISCENENAME VARCHAR(255) ,ISCENEDESC VARCHAR(255) ,ISCENETYPEID NUMERIC(19) ,ILOGICID NUMERIC(19) ,IAPPLYBY NUMERIC(19) ,IAPPLYTIME TIMESTAMP ,IAPPLYREMARK VARCHAR(500) ,IATTACHID NUMERIC(19) ,ISTATUS NUMERIC(5) DEFAULT 0 ,IEXECUTE NUMERIC(5) DEFAULT 0 ,CONSTRAINT PK_IEAI_SWITCHAPPROVE_TASK PRIMARY KEY (IID));
		
		CREATE TABLE IF NOT EXISTS IEAI_FLOW_ATTACHMENT (IID NUMERIC(19) ,IBUSINESSID NUMERIC(19) ,IFILENAME VARCHAR(300) ,IFILE BLOB ,CONSTRAINT PK_IEAI_FLOW_ATTACHMENT PRIMARY KEY (IID) );
		CREATE TABLE IF NOT EXISTS IEAI_WARN_PLAN (IDETAILID NUMERIC(19) NOT NULL ,ISCENEID NUMERIC(19) NULL ,IUSERID NUMERIC(19) NULL ,IWAYTYPES VARCHAR(128) ,CONSTRAINT PK_IEAI_WARN_PLAN PRIMARY KEY (IDETAILID) );
		CREATE TABLE IF NOT EXISTS IEAI_BUSI_MODULE (IMODULEID NUMERIC(19) NOT NULL ,IMODULECODE VARCHAR(30) ,IMODULENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG NUMERIC(1) ,CONSTRAINT PK_IEAI_BUSI_MODULE PRIMARY KEY (IMODULEID) );
		CREATE TABLE IF NOT EXISTS IEAI_BUSI_TYPE (ITYPEID NUMERIC(19) NOT NULL ,ITYPECODE VARCHAR(30) ,ITYPENAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG NUMERIC(1) ,CONSTRAINT PK_IEAI_BUSI_TYPE PRIMARY KEY (ITYPEID) );
		CREATE TABLE IF NOT EXISTS IEAI_BUSI_LEVEL (ILEVELID NUMERIC(19) NOT NULL ,ILEVELCODE VARCHAR(30) ,ILEVELNAME VARCHAR(30) ,IREMARK VARCHAR(300) ,IDELETEFLAG NUMERIC(1) ,CONSTRAINT PK_IEAI_BUSI_LEVEL PRIMARY KEY (ILEVELID) );
		CREATE TABLE IF NOT EXISTS IEAI_WARN_SCENE (ISCENEID NUMERIC(19) NOT NULL ,IMODULECODE VARCHAR(30) ,ITYPECODE VARCHAR(30) ,ILEVELCODE VARCHAR(30) ,ISTATUS NUMERIC(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_WARN_SCENE PRIMARY KEY (ISCENEID) );
		CREATE TABLE IF NOT EXISTS IEAI_WARN( iwarnid NUMERIC(19) not null, imodulecode VARCHAR(30),itypecode VARCHAR(30), ilevelcode VARCHAR(30), iip VARCHAR(30), ihappentime TIMESTAMP, iwarnmsg VARCHAR(4000), createtime TIMESTAMP, CONSTRAINT PK_IEAI_WARN primary key (iwarnid));
		
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 4.7.25 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'ISYSSRTO') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD ISYSSRTO NUMERIC(19) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'ISYSYRTO') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD ISYSYRTO NUMERIC(19) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_COPY' AND COLUMN_NAME = 'ISYSSRTO') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_COPY ADD ISYSSRTO NUMERIC(19) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_COPY' AND COLUMN_NAME = 'ISYSYRTO') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_COPY ADD ISYSYRTO NUMERIC(19) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'ISRTO') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ISRTO NUMERIC(1) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IINFORTO') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IINFORTO NUMERIC(19)DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'ISRTO') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ISRTO NUMERIC(1) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IINFORTO') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IINFORTO NUMERIC(19)DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'ISRTO') THEN
			ALTER TABLE IEAI_INSTANCEINFO ADD ISRTO NUMERIC(1) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IINFORTO') THEN
			ALTER TABLE IEAI_INSTANCEINFO ADD IINFORTO NUMERIC(19)DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'ISRTO') THEN
			ALTER TABLE IEAI_INSTANCEINFO_COPY ADD ISRTO NUMERIC(1) DEFAULT 0 ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'IINFORTO') THEN
			ALTER TABLE IEAI_INSTANCEINFO_COPY ADD IINFORTO NUMERIC(19)DEFAULT 0 ;
		END IF;
		
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 4.7.26 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO' AND COLUMN_NAME = 'IROLLBACKSCHEME') THEN
			ALTER TABLE IEAI_INSTANCEINFO ADD IROLLBACKSCHEME VARCHAR(255) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCEINFO_COPY' AND COLUMN_NAME = 'IROLLBACKSCHEME') THEN
			ALTER TABLE IEAI_INSTANCEINFO_COPY ADD IROLLBACKSCHEME VARCHAR(240) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'IROLLBACKSCHEME') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE ADD IROLLBACKSCHEME VARCHAR(240) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'IROLLBACKSCHEME') THEN
			ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD IROLLBACKSCHEME VARCHAR(240) ;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DOUBLECHECK_SIGN' AND COLUMN_NAME = 'ISFORCE') THEN
			ALTER TABLE IEAI_DOUBLECHECK_SIGN ADD ISFORCE NUMERIC(1) DEFAULT 0 ;
		END IF;
		
		END;;
		DELIMITER ;
		CALL UPPRDE();
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		create PROCEDURE UPPRDE()
		BEGIN
		DECLARE  target_database VARCHAR(100);
		SELECT DATABASE() INTO target_database;
		IF NOT EXISTS (SELECT * FROM information_schema.columns WHERE table_schema = target_database AND table_name = 'IEAI_ADAPTOR_PROPERTY_KEY_WS' AND column_name = 'IREGEX') THEN
		ALTER TABLE IEAI_ADAPTOR_PROPERTY_KEY_WS ADD IREGEX VARCHAR(255) null;
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		delete from IEAI_ADAPTOR_PROPERTY_KEY_WS where IID in (62,63,64,65,66,67,68,69,70,71,72,73);
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,IREGEX)VALUES (62, 1,'orttime', '步骤预计RTO', NULL,'{"regex":"^[0-9]*$","regexText":"只能输入数字"}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (63, 1, 'orttext', '回退信息', NULL);
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,ITYPE,ICONTEXT)VALUES (64, 1, 'ortback', '是否是RTO步骤', NULL,'combox','{"actpurl":"","localdata":[{"value":"1","display":"是"},{"value":"0","display":"否"}],"model":[{"name":"display","type":"string"},{"name":"value","type":"int"}],"combox":{"valueField":"value","displayField":"display"}}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,IREGEX)VALUES (65, 2, 'orttime', '步骤预计RTO', NULL,'{"regex":"^[0-9]*$","regexText":"只能输入数字"}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (66, 2, 'orttext', '回退信息', NULL);
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,ITYPE,ICONTEXT)VALUES (67, 2, 'ortback', '是否是RTO步骤', NULL,'combox','{"actpurl":"","localdata":[{"value":"1","display":"是"},{"value":"0","display":"否"}],"model":[{"name":"display","type":"string"},{"name":"value","type":"int"}],"combox":{"valueField":"value","displayField":"display"}}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,IREGEX)VALUES (68, 3, 'orttime', '步骤预计RTO', NULL,'{"regex":"^[0-9]*$","regexText":"只能输入数字"}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (69, 3, 'orttext', '回退信息', NULL);
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,ITYPE,ICONTEXT)VALUES (70, 3, 'ortback', '是否是RTO步骤', NULL,'combox','{"actpurl":"","localdata":[{"value":"1","display":"是"},{"value":"0","display":"否"}],"model":[{"name":"display","type":"string"},{"name":"value","type":"int"}],"combox":{"valueField":"value","displayField":"display"}}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,IREGEX)VALUES (71, 4, 'orttime', '回退时长', NULL,'{"regex":"^[0-9]*$","regexText":"只能输入数字"}');
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (72, 4, 'orttext', '回退信息', NULL);
		INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS,ITYPE,ICONTEXT)VALUES (73, 4, 'ortback', '是否回退', NULL,'combox','{"actpurl":"","localdata":[{"value":"1","display":"是"},{"value":"0","display":"否"}],"model":[{"name":"display","type":"string"},{"name":"value","type":"int"}],"combox":{"valueField":"value","displayField":"display"}}');
	
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}' ,IDISNAME ='步骤预计RTO' WHERE IID = 62 ;
		
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}' ,IDISNAME ='步骤预计RTO' WHERE IID = 65;

		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}' ,IDISNAME ='步骤预计RTO' WHERE IID = 68 ;
		
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}' ,IDISNAME ='步骤预计RTO' WHERE IID = 71 ;

		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}'  WHERE IID = 47 ;
		
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}'  WHERE IID = 51 ;
		
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}'  WHERE IID = 55 ;

		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IREGEX = '{"regex":"^\\\\d{0,10}$","regexText":"只能输入0-10位的数字"}'  WHERE IID = 59 ;
		
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IDISNAME = '回退方案名称',IREGEX ='{"regex":"^.{0,80}$","regexText":"最大只能输入80个字符"}' WHERE IID=63 ;
		
		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IDISNAME = '回退方案名称',IREGEX ='{"regex":"^.{0,80}$","regexText":"最大只能输入80个字符"}' WHERE IID=66 ;

		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IDISNAME = '回退方案名称',IREGEX ='{"regex":"^.{0,80}$","regexText":"最大只能输入80个字符"}' WHERE IID=69 ;

		UPDATE IEAI_ADAPTOR_PROPERTY_KEY_WS SET IDISNAME = '回退方案名称',IREGEX ='{"regex":"^.{0,80}$","regexText":"最大只能输入80个字符"}' WHERE IID=72 ;
	
		
		DELIMITER ;;
		
		-- 4.7.27 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_INSTANCECLASS (IID NUMERIC(19) NOT NULL ,ICLASSNAME VARCHAR(255) ,ICLASSDESC VARCHAR(255) ,ICREATETIME TIMESTAMP(6) ,IPARENTID NUMERIC(19),CONSTRAINT PK_IEAI_INSTANCECLASS PRIMARY KEY(IID) );
		CREATE TABLE IF NOT EXISTS IEAI_INSTANCE_USER (IID NUMERIC(19) NOT NULL ,ISTEPIID NUMERIC(19) ,IUSERID NUMERIC(19),CONSTRAINT PK_IEAI_INSTANCE_USER PRIMARY KEY(IID) );
		CREATE TABLE IF NOT EXISTS IEAI_STEP_INSTANCEINFO (IID NUMERIC(19) NOT NULL ,IINSTANCEID NUMERIC(19) ,IINSTANCENAME VARCHAR(255) ,ISERNER VARCHAR(255) ,ICONNER NUMERIC(19) ,ICONNERNAME VARCHAR(100) ,IPRENER VARCHAR(255) ,IACTNAME VARCHAR(255) ,IACTDES VARCHAR(100) ,IACTTYPE INTEGER ,IREMINFO VARCHAR(255) ,IIPNAME VARCHAR(100) ,IMODELTYPE VARCHAR(100) ,IIP VARCHAR(2000) ,IPORT INTEGER ,ISYSTYPE INTEGER ,IEXECUSER VARCHAR(100) ,ISHELLSCRIPT VARCHAR(800) ,IISLOADENV INTEGER ,ISHELLPATH VARCHAR(800) ,ITIMEOUT NUMERIC(19) ,ISWITCHSYSTYPE INTEGER ,IPARAMETER VARCHAR(255) ,IPARACHECK VARCHAR(255) ,IPARASWITCH VARCHAR(255) ,IPARASWITCHFORCE VARCHAR(255) ,IEXPECEINFO VARCHAR(50) ,IEXCEPTINFO VARCHAR(50) ,IREDOABLE INTEGER ,IISDISABLE INTEGER ,IPKGNAME VARCHAR(255) ,ICENTER VARCHAR(255) ,IRETNVALEXCEPION VARCHAR(255) ,APPIDENTITY VARCHAR(255) ,IPRESYSNAME VARCHAR(255) ,IPREACTNAME VARCHAR(1000) ,ITYPE INTEGER ,IMODELNAME VARCHAR(255) ,IMODELVERSION NUMERIC(19) ,ISCRIPTID NUMERIC(19) ,IMXGRAPHID NUMERIC(19) ,ISYSTEMTYPESTEP VARCHAR(255) ,IMODELTYPESTEP VARCHAR(255) ,SINGLEROLLBACK VARCHAR(255) ,IBRANCH VARCHAR(255) ,IPAIR VARCHAR(255) ,IHIGHTRISK INTEGER ,IJLDISABLE INTEGER ,ICONCURRENCY VARCHAR(255) ,IINTERVALTIME VARCHAR(255) ,ICFGFILECHANGE VARCHAR(255) ,IOWNERTYPE NUMERIC(1) DEFAULT -1 ,IOWNER VARCHAR(255) ,IEXPBEGINTIME NUMERIC(19) DEFAULT 0 ,IEXPENDTIME NUMERIC(19) DEFAULT 0 ,IREMARK VARCHAR(1000) ,IPROJECTNAME VARCHAR(255) ,IREVIEWER VARCHAR(255) ,IBANKNAME VARCHAR(255) ,IREVIEWSTATE INTEGER DEFAULT 1 ,ISROLLBACK INTEGER DEFAULT 0 ,ISRTO NUMERIC(1) DEFAULT 0 ,IINFORTO NUMERIC(19) DEFAULT 0 ,ICALLINSTANCENAME VARCHAR(255) ,ICALLPKGNAME VARCHAR(255) ,ICALLVERSIONDES VARCHAR(255) ,ICALLSTARTTIMES VARCHAR(255) ,ICALLSTARTENVNAMES VARCHAR(255) ,IROLLBACKSCHEME VARCHAR(255) ,INSTANCECLASS NUMERIC(19) ,ISHAREINFO NUMERIC(1) ,ICLASSIDTWO NUMERIC(19) ,SYSTEMNAME VARCHAR(255) ,CONSTRAINT PK_IEAI_STEP_INSTANCEINFO PRIMARY KEY(IID));

		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 4.7.28 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_STEP_INSTANCEINFO' AND COLUMN_NAME = 'IUPLOADUSERID') THEN
		ALTER TABLE IEAI_STEP_INSTANCEINFO ADD IUPLOADUSERID NUMERIC(19) ;
		END IF;

		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_ITSMTASK (IID NUMERIC(19) NOT NULL ,ITASKID VARCHAR(255) ,ITASKNAME VARCHAR(255) ,ITASKDESC VARCHAR(255) ,ICREATER VARCHAR(255) ,ICREATTIME TIMESTAMP ,IVERSION VARCHAR(255) ,ISYSNUM INTEGER DEFAULT 0,ISWITCHTIME VARCHAR(255) ,IRTPO VARCHAR(255) ,IAPPTYPE VARCHAR(100) ,IAPPROVER VARCHAR(255) ,IAPPIDEA VARCHAR(255) ,IRESULT INTEGER ,ISDCODE VARCHAR(255) ,ISFLAG INTEGER,CONSTRAINT PK_IEAI_ITSMTASK PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PROJECT_PARAMINFO (IID NUMERIC(19) NOT NULL ,IPARAMNAME VARCHAR(255) ,IPARAMKEY VARCHAR(255) ,IPARAMINFO VARCHAR(255) ,IPROJECTID NUMERIC(19) ,CONSTRAINT IEAI_PROJECT_PARAMINFO PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PROJECT_XY (IID NUMERIC(19) NOT NULL ,IPKGCONTENTID NUMERIC(19) ,INAME VARCHAR(255) ,IMAJVER NUMERIC(10) ,IMINVER NUMERIC(10) ,IFREEZED INTEGER ,IUPLOADUSER VARCHAR(255) ,ICOMMENT VARCHAR(255) ,ISYSTEMTYPEUUID VARCHAR(100) DEFAULT '' ,IUPLOADNUM NUMERIC(10) ,IUUID VARCHAR(255) ,IUPLOADTIME TIMESTAMP(6) ,IFREEZEUSER VARCHAR(255) ,IFREEZEUSERID NUMERIC(19) ,IUPLOADUSERID NUMERIC(19) ,IGROUPID NUMERIC(19) ,PROTYPE NUMERIC(5,1) DEFAULT 0.0 ,IUPPERID NUMERIC(19) DEFAULT 0 ,ILATESTID NUMERIC(19) DEFAULT 0 ,ISVALIDATE INTEGER DEFAULT 1 ,ISYSTEMCODE VARCHAR(255) ,IFROM NUMERIC(4) DEFAULT 1 ,DAILYTYPE NUMERIC(2) DEFAULT 1 ,ISYSNAME VARCHAR(255) ,ISSELFHEALING NUMERIC(1) DEFAULT 0 ,ITASKID NUMERIC(19) ,CONSTRAINT PK_IEAI_PROJECT_XY PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_DIR_XY (IID NUMERIC(19) NOT NULL ,ISWITCHDES VARCHAR(255) ,ITASKID NUMERIC(19) ,CONSTRAINT PK_IEAI_SWITCH_DIR_XY PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_INSTANCE_VERSION_XY (IID NUMERIC(19) NOT NULL ,IINSTANCENAME VARCHAR(255) ,IVERSION NUMERIC(19) ,IEDITVERSION INTEGER ,IVERSIONALIAS VARCHAR(255) ,ISYSTYPE INTEGER ,IDES VARCHAR(255) ,ISYSID NUMERIC(19) ,ITIME NUMERIC(19) ,ISWITCHID NUMERIC(19) ,IUPPERID NUMERIC(19) ,IPROJECTID NUMERIC(19) ,IIEAISYSID NUMERIC(19) ,IENVNAME VARCHAR(255) ,IBUSNES_SYS_IID NUMERIC(19) ,IISSUSFLOW INTEGER ,IISMXGRAPH INTEGER DEFAULT 0 ,ISVALIDATE INTEGER DEFAULT 1 ,ISBACKINSTANCE INTEGER DEFAULT 0 ,IABB VARCHAR(225) ,ISCENENAME VARCHAR(255) ,ISCENETYPE VARCHAR(255) ,INEWDIRECT VARCHAR(255) ,IVERSIONTYPE VARCHAR(25) DEFAULT '0' ,IPLANDESC VARCHAR(4000) ,IAPMTYPE INTEGER ,ICREATEUSRID NUMERIC(19) DEFAULT 0 ,TASKTYPE NUMERIC(4) ,OUTPATH VARCHAR(255) ,GLOBALIP VARCHAR(255) ,ISSELFHEALING INTEGER DEFAULT 0 ,ISYSSRTO NUMERIC(19) ,ISYSYRTO NUMERIC(19) ,ITASKID NUMERIC(19) ,IPLANID NUMERIC(19) ,CONSTRAINT PK_IEAI_INSTANCE_VERSION_XY PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_INSTANCEINFO_XY (IID NUMERIC(19) NOT NULL ,IINSTANCEID NUMERIC(19) ,IINSTANCENAME VARCHAR(255) ,ISERNER NUMERIC(19) ,ICONNER NUMERIC(19) ,ICONNERNAME VARCHAR(100) ,IPRENER VARCHAR(255) ,IACTNAME VARCHAR(255) ,IACTDES VARCHAR(100) ,IACTTYPE INTEGER ,IREMINFO VARCHAR(255) ,IIPNAME VARCHAR(100) ,IMODELTYPE VARCHAR(100) ,IIP VARCHAR(2000) ,IPORT INTEGER ,ISYSTYPE INTEGER ,IEXECUSER VARCHAR(100) ,ISHELLSCRIPT VARCHAR(800) ,IISLOADENV INTEGER ,ISHELLPATH VARCHAR(800) ,ITIMEOUT NUMERIC(19) ,ISWITCHSYSTYPE INTEGER ,IPARAMETER VARCHAR(255) ,IPARACHECK VARCHAR(255) ,IPARASWITCH VARCHAR(255) ,IPARASWITCHFORCE VARCHAR(255) ,IEXPECEINFO VARCHAR(50) ,IEXCEPTINFO VARCHAR(50) ,IREDOABLE INTEGER ,IISDISABLE INTEGER ,IPKGNAME VARCHAR(255) ,ICENTER VARCHAR(255) ,IRETNVALEXCEPION VARCHAR(255) ,APPIDENTITY VARCHAR(255) ,IPRESYSNAME VARCHAR(255) ,IPREACTNAME VARCHAR(1000) ,ITYPE INTEGER ,IMODELNAME VARCHAR(255) ,IMODELVERSION NUMERIC(19) ,ISCRIPTID NUMERIC(19) ,IMXGRAPHID NUMERIC(19) ,ISYSTEMTYPESTEP VARCHAR(255) ,IMODELTYPESTEP VARCHAR(255) ,SINGLEROLLBACK VARCHAR(255) ,IBRANCH VARCHAR(255) ,IPAIR VARCHAR(255) ,IHIGHTRISK INTEGER ,IJLDISABLE INTEGER ,ICONCURRENCY VARCHAR(255) ,IINTERVALTIME VARCHAR(255) ,ICFGFILECHANGE VARCHAR(255) ,IOWNERTYPE NUMERIC(1) DEFAULT -1 ,IOWNER VARCHAR(255) ,IEXPBEGINTIME NUMERIC(19) DEFAULT 0 ,IEXPENDTIME NUMERIC(19) DEFAULT 0 ,IREMARK VARCHAR(1000) ,IPROJECTNAME VARCHAR(255) ,IREVIEWER VARCHAR(255) ,IBANKNAME VARCHAR(255) ,IREVIEWSTATE INTEGER DEFAULT 1 ,ISROLLBACK INTEGER DEFAULT 0 ,ISRTO NUMERIC(1) DEFAULT 0 ,IINFORTO NUMERIC(19) ,IROLLBACKSCHEME VARCHAR(255) ,ITASKID NUMERIC(19) ,CONSTRAINT PK_IEAI_INSTANCEINFO_XY PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PROJECT_PLAN_XY (IID NUMERIC(19) NOT NULL ,IPLANNUMBER VARCHAR(255) ,IMODEL VARCHAR(255) ,ISCENE VARCHAR(1000) ,IRECOVER VARCHAR(255) ,IPROJECTID NUMERIC(19) ,ITASKID NUMERIC(19),CONSTRAINT IEAI_PROJECT_PLAN_XY PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_PROJECT_PLAN (IID NUMERIC(19) NOT NULL ,IPLANNUMBER VARCHAR(255) ,IMODEL VARCHAR(255) ,ISCENE VARCHAR(1000) ,IRECOVER VARCHAR(255) ,IPROJECTID NUMERIC(19) ,CONSTRAINT IEAI_PROJECT_PLAN PRIMARY KEY (IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYS_PERSON' AND COLUMN_NAME = 'IPROPERTY') THEN
			ALTER TABLE IEAI_SYS_PERSON ADD IPROPERTY VARCHAR(255) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'IPLANID') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD IPLANID NUMERIC(19) ;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_COPY' AND COLUMN_NAME = 'IPLANID') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION_COPY ADD IPLANID NUMERIC(19) ;
		END IF;		
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 4.7.29 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTUPDATE_INFO' AND COLUMN_NAME = 'ISUSP') THEN
			ALTER TABLE IEAI_AGENTUPDATE_INFO  ADD ISUSP  NUMERIC(1) DEFAULT 2 ;
		END IF;
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_OPERATION (IID NUMERIC(19) ,IOPERUSER VARCHAR(128) ,IOPERTIME NUMERIC(15) ,IOPERDESC VARCHAR(512) ,IOPERBUTTON VARCHAR(128) ,IRUNINFOID NUMERIC(19) ,CONSTRAINT PK_IEAI_SWITCH_OPERATION PRIMARY KEY (IID));
		
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_ALIAS (IID NUMERIC(19) NOT NULL ,ISWITCHID NUMERIC(19) ,ISYSPROJECTID NUMERIC(19) ,ISWITCHMARK NUMERIC(19) ,CONSTRAINT PK_IEAI_SWITCH_ALIAS PRIMARY KEY (IID));
		CREATE TABLE IF NOT EXISTS IEAI_SWITCH_SYSTEM (IID NUMERIC(19) NOT NULL ,ISYSPROJECTID NUMERIC(19) ,ICENTERID NUMERIC(19) ,CONSTRAINT PK_IEAI_SWITCH_SYSTEM PRIMARY KEY (IID));
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SYS_PERSON' AND COLUMN_NAME = 'IPERSONNOSEID') THEN
			ALTER TABLE IEAI_SYS_PERSON ADD IPERSONNOSEID VARCHAR(255);
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DATASYNC_CONFIGINFO' AND COLUMN_NAME = 'IISAUTOSYNC') THEN
			ALTER TABLE IEAI_DATASYNC_CONFIGINFO ADD IISAUTOSYNC NUMERIC(1) DEFAULT 1;
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ITSMTASK' AND COLUMN_NAME = 'IISPASS') THEN
			ALTER TABLE IEAI_ITSMTASK ADD IISPASS INTEGER DEFAULT 1;
		END IF;
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ITSMTASK' AND COLUMN_NAME = 'IAPPROVESTEP') THEN
			ALTER TABLE IEAI_ITSMTASK ADD IAPPROVESTEP VARCHAR(255);
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_STEP_INSTANCEINFO' AND COLUMN_NAME = 'IPTYPE') THEN
			ALTER TABLE IEAI_STEP_INSTANCEINFO ADD IPTYPE INTEGER DEFAULT 1;
		END IF;		
		IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_STEP_INSTANCEINFO' AND COLUMN_NAME = 'IIP' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_STEP_INSTANCEINFO MODIFY IIP LONGTEXT; 
		END IF;
		IF  EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_STEP_INSTANCEINFO' AND COLUMN_NAME = 'SYSTEMNAME' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_STEP_INSTANCEINFO MODIFY SYSTEMNAME LONGTEXT; 
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
		-- 8.2 version ZB patch is as follows
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SCRIPT_MANAGEMENT' AND COLUMN_NAME = 'SYSTYPE') THEN
			ALTER TABLE IEAI_SCRIPT_MANAGEMENT ADD SYSTYPE NUMERIC(2) DEFAULT 4 ;
		END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
