--4.7.17 not exists
--4.7.18 not exists
--4.7.19 not exists
--4.7.20 not exists
--4.7.21 not exists
--4.7.22 not exists
--4.7.23 
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 
	--CREATE TABLE IEAI_FSH_*
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARM_TYPE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARM_TYPE (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), IKEYWORD VARCHAR2(255), IPLATFORM NUMBER(4), ICREATETIME NUMBER(19), ICREATEUSER VARCHAR2(50), CONSTRAINT PK_IEAI_FSH_ALARM_TYPE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCENE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_SCENE (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), ITYPE NUMBER(4), ITASKID NUMBER(19), ICREATETIME NUMBER(19), ICREATEUSER VARCHAR2(50), ITASKNAME VARCHAR2(255), CONSTRAINT PK_IEAI_FSH_SCENE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_TASK' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_TASK (IID NUMBER(19) NOT NULL, IALARMINFOID NUMBER(19), ISCHEMEID NUMBER(19), ICREATETIME NUMBER(19), ISTARTTIME NUMBER(19), IENDTIME NUMBER(19), ISTATE NUMBER(4), ITASK_EXECRESULT VARCHAR2(100), ITASKTYPE NUMBER(4), IAOMSTASKID NUMBER(19), IALARMIP VARCHAR2(20), ISUBMITUSER VARCHAR2(255), IAUDITOR VARCHAR2(255), ITIMEING VARCHAR2(50), IEXAMINETYPE NUMBER(4), CONSTRAINT PK_IEAI_FSH_TASK PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_TASK_HISTORY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_TASK_HISTORY	(IID NUMBER(19) NOT NULL, IALARMINFOID NUMBER(19), ISCHEMEID NUMBER(19), ICREATETIME NUMBER(19), ISTARTTIME NUMBER(19), IENDTIME NUMBER(19), ISTATE NUMBER(4), ITASK_EXECRESULT VARCHAR2(100), ITASKTYPE NUMBER(4), IAOMSTASKID NUMBER(19), IALARMIP VARCHAR2(20), ISUBMITUSER VARCHAR2(255), IAUDITOR VARCHAR2(255), ITIMEING VARCHAR2(50), IEXAMINETYPE NUMBER(4), CONSTRAINT PK_IEAI_FSH_TASK_HISTORY PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARMINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARMINFO (IID NUMBER(19) NOT NULL, IALARMID VARCHAR2(100), IPLATFORM VARCHAR2(100), IAPPNAME VARCHAR2(255), IHOSTIP VARCHAR2(25), IALARMCONTENT VARCHAR2(500), IALARMCODE VARCHAR2(255), IALARM_TIME NUMBER(19), ICONTANT VARCHAR2(500), IPHONE VARCHAR2(500), IALARM_MESSAGE VARCHAR2(2000), IIS_TRIGGER NUMBER(4), ITASK_HANDLE_DETAIL VARCHAR2(100), IALARMTYPEID VARCHAR2(1000), ICREATETIME NUMBER(19), CONSTRAINT PK_IEAI_FSH_ALARMINFO PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARMINFO_HISTORY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARMINFO_HISTORY (IID NUMBER(19) NOT NULL, IALARMID VARCHAR2(100), IPLATFORM VARCHAR2(100), IAPPNAME VARCHAR2(255), IHOSTIP VARCHAR2(25), IALARMCONTENT VARCHAR2(500), IALARMCODE VARCHAR2(255), IALARM_TIME NUMBER(19), ICONTANT VARCHAR2(500), IPHONE VARCHAR2(500), IALARM_MESSAGE VARCHAR2(2000), IIS_TRIGGER NUMBER(4), ITASK_HANDLE_DETAIL VARCHAR2(100), IALARMTYPEID VARCHAR2(1000), ICREATETIME NUMBER(19), CONSTRAINT PK_IEAI_FSH_ALARMINFO_HISTORY PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARM_COMBINATION' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARM_COMBINATION (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), IDES VARCHAR2(255), ICREATETIME NUMBER(19), ICREATEUSER VARCHAR2(50), CONSTRAINT PK_IEAI_FSH_ALARM_COMBINATION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_ALARM_COM_TYPE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_ALARM_COM_TYPE (IID NUMBER(19) NOT NULL, ICOMBINATIONID NUMBER(19), IALARMTYPEID NUMBER(19), CONSTRAINT PK_IEAI_FSH_ALARM_COM_TYPE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCHEME' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_SCHEME (IID NUMBER(19) NOT NULL, INAME VARCHAR2(255), ITYPE NUMBER(4), IALARMCOMBINATIONID NUMBER(19), ISCENE NUMBER(19), IEXECMODEL NUMBER(4), IFEEDBACK_TIME NUMBER(4), ICOOLING_TIME NUMBER(4), IOVERTIME_TIME NUMBER(4), ISTATE NUMBER(4), ICREATETIME NUMBER(19), IONLINETIME NUMBER(19), ICREATEUSER VARCHAR2(50), IAPPLYUSER VARCHAR2(50), IAUDITORUSER VARCHAR2(50), CONSTRAINT PK_IEAI_FSH_SCHEME PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCHEME_EQU' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FSH_SCHEME_EQU (IID NUMBER(19) NOT NULL, ISCHEMEID NUMBER(19), IEQUID NUMBER(19), IEQUIP VARCHAR2(20), CONSTRAINT PK_IEAI_FSH_SCHEME_EQU PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='ISSELFHEALING';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD ISSELFHEALING NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='ISSELFHEALING';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ISSELFHEALING INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;

	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='ISCHEMEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_ALARMINFO ADD ISCHEMEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='ISCHEMEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_ALARMINFO_HISTORY ADD ISCHEMEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	COMMIT;
	END IF;

END;
/
--4.7.24 
DECLARE
	LS_SQL VARCHAR2(2000);
	LI_EXISTS SMALLINT;
BEGIN 

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCENE_TSAKCONFIG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_FSH_SCENE_TSAKCONFIG (IID NUMBER(19) NOT NULL, ITASKID NUMBER(19), ICOMBINATIONID NUMBER(19), IALARMINFO VARCHAR2(255), IPARAMNAME  VARCHAR2(255),CONSTRAINT PK_IEAI_FSH_SCENE_TSAKCONFIG PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK' AND COLUMN_NAME='IAUDITOR';
	IF LI_EXISTS = 1 THEN 
			LS_SQL := 'ALTER TABLE IEAI_FSH_TASK MODIFY (IAUDITOR VARCHAR2(4000))';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME='IAUDITOR';
	IF LI_EXISTS = 1 THEN 
			LS_SQL := 'ALTER TABLE IEAI_FSH_TASK_HISTORY MODIFY (IAUDITOR VARCHAR2(4000))';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
--4.7.25 
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO rename column IALARMTYPEID to IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO add IALARMTYPEID VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'update IEAI_FSH_ALARMINFO set IALARMTYPEID=IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY rename column IALARMTYPEID to IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY add IALARMTYPEID VARCHAR2(1000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'update IEAI_FSH_ALARMINFO_HISTORY set IALARMTYPEID=IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME='IALARMTYPEID_TMP';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'alter table IEAI_FSH_ALARMINFO_HISTORY drop column IALARMTYPEID_TMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCENE_TSAKCONFIG' AND COLUMN_NAME='ISCENEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCENE_TSAKCONFIG  ADD ISCENEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCENE_TSAKCONFIG' AND COLUMN_NAME='ISCENEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCENE_TSAKCONFIG  ADD IALARMTYPEID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/ 
--4.7.26 not exists
--V8.7 
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCHEME' AND COLUMN_NAME='SYSTEMCODE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCHEME  ADD SYSTEMCODE VARCHAR2(20) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCHEME' AND COLUMN_NAME='SYSSHORT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCHEME  ADD SYSSHORT VARCHAR2(20) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCHEME' AND COLUMN_NAME='ISCENEIDIPMP';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCHEME  ADD ISCENEIDIPMP VARCHAR2(30) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCHEME' AND COLUMN_NAME='ISCHEMEIP';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCHEME  ADD ISCHEMEIP VARCHAR2(30) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK' AND COLUMN_NAME='IALARMID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_TASK  ADD IALARMID NUMBER(19,0) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_ALARM_COMBINATION' AND COLUMN_NAME='COMPOSITIONRELATIONSHIPS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_ALARM_COMBINATION  ADD COMPOSITIONRELATIONSHIPS NUMBER(19,0) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME='IALARMID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_TASK_HISTORY  ADD IALARMID NUMBER(19,0) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;
END;
/ 

--V8.9自愈sql

DECLARE
     LS_SQL VARCHAR2(2000);
     LS_SQL1 VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_AUDIT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := '  CREATE TABLE IEAI_FSH_AUDIT(	IID NUMBER(19,0), OPERATIONS VARCHAR2(50 ), URL VARCHAR2(50 ) NOT NULL , USERID NUMBER(19,0) NOT NULL , USERFULLNAME VARCHAR2(100 ) NOT NULL, OPERTIME NUMBER(25,0), ISTATE NUMBER(19,0), IP VARCHAR2(255 ), ITARGETDATA VARCHAR2(1000 ), HOSTNAME VARCHAR2(225 ), IDESC VARCHAR2(200 ), CONSTRAINT PK_IEAI_FSH_AUDIT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;

	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROPERTYCONFIG WHERE IPROPERTYNAME='FSHPROTECTTIME';
	IF LI_EXISTS > 0 THEN
		 LS_SQL := 'delete from IEAI_PROPERTYCONFIG where IPROPERTYNAME=''FSHPROTECTTIME''';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROPERTYCONFIG WHERE IPROPERTYNAME='FSHProtectTime';
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'Insert into IEAI_PROPERTYCONFIG (IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) values (437,''FSHProtectTime'',3,''故障自愈保护时间'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROPERTYCONFIG WHERE IPROPERTYNAME='FSHHISTORYCOUNT';
	IF LI_EXISTS > 0 THEN
		 LS_SQL := 'delete from IEAI_PROPERTYCONFIG where IPROPERTYNAME=''FSHHISTORYCOUNT''';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROPERTYCONFIG WHERE IPROPERTYNAME='FSHHistoryCount';
	IF LI_EXISTS = 0 THEN
		 LS_SQL := 'Insert into IEAI_PROPERTYCONFIG (IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) values (1057,''FSHHistoryCount'',3,''故障自愈历史查询条数'')';
		 EXECUTE IMMEDIATE LS_SQL;
		 commit;
	END IF;
	
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK' AND COLUMN_NAME='IALARMID' AND DATA_TYPE='VARCHAR2'; 
	IF	LI_EXISTS = 0 THEN 
		LS_SQL:='update IEAI_FSH_TASK set IALARMID =null';
		LS_SQL1:= 'alter table IEAI_FSH_TASK modify IALARMID varchar2(100)'; 
		EXECUTE IMMEDIATE LS_SQL; 
		EXECUTE IMMEDIATE LS_SQL1; 
	END IF; 
	COMMIT; 
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME='IALARMID' AND DATA_TYPE='VARCHAR2'; 
	IF	LI_EXISTS = 0 THEN 
		LS_SQL:='update IEAI_FSH_TASK set IALARMID =null';
		LS_SQL1:= 'alter table IEAI_FSH_TASK_HISTORY modify IALARMID varchar2(100)'; 
		EXECUTE IMMEDIATE LS_SQL; 
		EXECUTE IMMEDIATE LS_SQL1; 
	END IF; 
	COMMIT;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FSH_SCENE_TASKCONFIG' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
         LS_SQL := 'CREATE TABLE IEAI_FSH_SCENE_TASKCONFIG (IID NUMBER(19) NOT NULL, ITASKID NUMBER(19) , ISCENEID NUMBER(19), IPARAMNAME VARCHAR2(255), IPARAMTYPE VARCHAR2(255), IPARAMVALUE VARCHAR(2255),IPARAMDESC VARCHAR2(255),IPARAMIID NUMBER(19) , CONSTRAINT PK_IEAI_FSH_SCENE_TASKCONFIG PRIMARY KEY (IID))';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;

END;
/
--V8.20
DECLARE
     LS_SQL VARCHAR2(2000);
     LS_SQL1 VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_FSH_SCHEME' AND COLUMN_NAME='SYSNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_FSH_SCHEME  ADD SYSNAME VARCHAR2(255) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	commit;

END;
/