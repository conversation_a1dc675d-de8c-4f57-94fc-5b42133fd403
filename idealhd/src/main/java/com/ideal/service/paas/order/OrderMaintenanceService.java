package com.ideal.service.paas.order;

import com.ideal.ieai.server.paas.order.maintenance.OrderMaintenanceManager;
import com.ideal.ieai.server.paas.order.model.OrderMaintenanceModel;
import com.ideal.util.JsonUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrderMaintenanceService {

    public Map getOrderMaintenanceList(Integer start, Integer limit,String sceneName,String itasktype,
                                       String itaskname,String ifounder, int warmup,String sceneType){
        OrderMaintenanceManager manage = new OrderMaintenanceManager();
        Map map = manage.getOrderMaintenanceList(start,limit,sceneName,itasktype,itaskname,ifounder,warmup,sceneType);
        return map;
    }

    public Map getOrderNonCloudList(Integer start, Integer limit,String noQueryTaskNames, String sceneType, String taskName,String taskType, int warmup){
        OrderMaintenanceManager manage = new OrderMaintenanceManager();
        Map map = manage.getOrderNonCloudList(start,limit,noQueryTaskNames, sceneType, taskName,taskType,warmup);
        return map;
    }

    public boolean deleteOrderMaintenance(String[] iidStr) {
        boolean res;
        OrderMaintenanceManager manage = new OrderMaintenanceManager();
        res = manage.deleteOrderMaintenance(iidStr);
        return res;
    }

    public Map saveOrderMaintenance(String jsonData) {
        OrderMaintenanceManager manage = new OrderMaintenanceManager();
        Map<String, Object> map = new HashMap<String, Object>();
        List<OrderMaintenanceModel> list = JsonUtil.getListModelByJson(jsonData, OrderMaintenanceModel.class);
        manage.saveOrderMaintenanceList(list);
        return map;
    }

//    public Map getAllTaskNameByType(Integer start, Integer limit,String itasktype,String itasknamevalue) {
//        OrderMaintenanceManager manage = new OrderMaintenanceManager();
//        Map map = manage.getAllTaskNameByType(start,limit,itasktype,itasknamevalue);
//        return map;
//    }

    public OrderMaintenanceModel getOrderMaintenanceOne (String sceneId) {
        OrderMaintenanceManager manage = new OrderMaintenanceManager();
        return  manage.getOrderMaintenanceOne(sceneId);
    }

}
