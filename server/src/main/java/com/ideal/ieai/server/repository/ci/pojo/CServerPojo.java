package com.ideal.ieai.server.repository.ci.pojo;

import java.io.Serializable;
import java.util.Date;

public class CServerPojo  implements Serializable
{
  private static final long serialVersionUID = 1L;
  private String iid;//主建
  private String iagentId;
  private String name;//名称
  private String ip;//ip
  private String port;//端口
  private String computerName;//计算机名
  private String system;//操作系统
  private Integer isDocker = 0;//启动docker,默认否   ，1是0否
  private String  isDockerString;
  private String dockerPort;//docker端口
  private String describe;//描述
  private String createUser;//创建人
  private Date createTime;//创建时间
  private String state;//状态
  private int   start  = 0; // 起始页
  private int   limit  = 50; // 每页数据
  private String buildMachine;//构建机
  private String systemName ;//业务系统
  //查询使用
  private String startIp;//开始ip
  private String endIp;//终止ip
  private String sourceId;//资源组id名称
  private String status;//agent状态值

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getIid ()
{
    return iid;
}
public void setIid ( String iid )
{
    this.iid = iid;
}
public String getName ()
{
    return name;
}
public void setName ( String name )
{
    this.name = name;
}
public String getIp ()
{
    return ip;
}
public void setIp ( String ip )
{
    this.ip = ip;
}
public String getPort ()
{
    return port;
}
public void setPort ( String port )
{
    this.port = port;
}
public String getComputerName ()
{
    return computerName;
}
public void setComputerName ( String computerName )
{
    this.computerName = computerName;
}
public String getSystem ()
{
    return system;
}
public void setSystem ( String system )
{
    this.system = system;
}
public Integer getIsDocker ()
{
    return isDocker;
}
public void setIsDocker ( Integer isDocker )
{
    this.isDocker = isDocker;
}
public String getDockerPort ()
{
    return dockerPort;
}
public void setDockerPort ( String dockerPort )
{
    this.dockerPort = dockerPort;
}
public String getDescribe ()
{
    return describe;
}
public void setDescribe ( String describe )
{
    this.describe = describe;
}
public String getCreateUser ()
{
    return createUser;
}
public void setCreateUser ( String createUser )
{
    this.createUser = createUser;
}

public Date getCreateTime ()
{
    return createTime;
}
public void setCreateTime ( Date createTime )
{
    this.createTime = createTime;
}
public String getState ()
{
    return state;
}
public void setState ( String state )
{
    this.state = state;
}
public int getStart ()
{
    return start;
}
public void setStart ( int start )
{
    this.start = start;
}
public int getLimit ()
{
    return limit;
}
public void setLimit ( int limit )
{
    this.limit = limit;
}
public String getIsDockerString ()
{
    return isDockerString;
}
public void setIsDockerString ( String isDockerString )
{
    this.isDockerString = isDockerString;
}
public String getStartIp ()
{
    return startIp;
}
public void setStartIp ( String startIp )
{
    this.startIp = startIp;
}
public String getEndIp ()
{
    return endIp;
}
public void setEndIp ( String endIp )
{
    this.endIp = endIp;
}
public String getSourceId ()
{
    return sourceId;
}
public void setSourceId ( String sourceId )
{
    this.sourceId = sourceId;
}
public String getStatus ()
{
    return status;
}
public void setStatus ( String status )
{
    this.status = status;
}
public String getIagentId ()
{
    return iagentId;
}
public void setIagentId ( String iagentId )
{
    this.iagentId = iagentId;
}

    public String getBuildMachine() {
        return buildMachine;
    }

    public void setBuildMachine(String buildMachine) {
        this.buildMachine = buildMachine;
    }
}
