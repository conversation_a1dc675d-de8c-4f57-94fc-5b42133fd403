package com.ideal.ieai.server.repository.sus.hlj ;

import java.sql.Connection;
import java.util.List;
import java.util.Map;
import org.apache.log4j.Logger;

import com.ideal.ieai.server.repository.RepositoryException;
/**
   *
   * @ClassName: Hlj_Sht_RpghtService
   * @Description: (黑龙江_核心变更维护表)sheet,RPG程序回退
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:31:58
   * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
   */
public class Hlj_Sht_RpghtService 
{
   private static Logger _log =Logger.getLogger(Hlj_Sht_RpghtService.class);
   /**
   *
   * @Title: 
   * @Description: (黑龙江_核心变更维护表)sheet,RPG程序回退的查询
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:31:58
   */
   public Map queryHlj_Sht_Rpght (Hlj_Sht_Rpght hlj_sht_rpght , int start , int limit , int type )throws Exception
   {
      Map map =null;
      Hlj_Sht_RpghtManager hlj_Sht_RpghtManager =new Hlj_Sht_RpghtManager();
      map=hlj_Sht_RpghtManager.queryHlj_Sht_Rpght(hlj_sht_rpght, start, limit, type);
      return map;
   }



   /**
   *
   * @Title: 
   * @Description: (黑龙江_核心变更维护表)sheet,RPG程序回退的保存
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:31:58
   */
   public boolean saveHlj_Sht_Rpght (List<Map<String,Object>> dataList , int type )throws Exception
   {
      Hlj_Sht_RpghtManager hlj_Sht_RpghtManager =new Hlj_Sht_RpghtManager();
      return hlj_Sht_RpghtManager.saveHlj_Sht_Rpght(dataList, type);
   }



   /**
   *
   * @Title: 
   * @Description: (黑龙江_核心变更维护表)sheet,RPG程序回退的删除
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:31:58
   */
   public boolean deleteHlj_Sht_Rpght (String iids , int type )throws Exception
   {
      Hlj_Sht_RpghtManager hlj_Sht_RpghtManager =new Hlj_Sht_RpghtManager();
      return hlj_Sht_RpghtManager.deleteHlj_Sht_Rpght(iids, type);
   }

   //点击保存按钮修改RPG回退表中的数据
   public boolean saveRpght ( List<Hlj_Sht_Rpght> rpghtList , Connection conn ) throws RepositoryException
   {
       Hlj_Sht_RpghtManager hlj_Sht_RpghtManager =new Hlj_Sht_RpghtManager();
       return hlj_Sht_RpghtManager.saveRpght(rpghtList,conn);
   }


}