package com.ideal.service.agentUpDataInfo;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.registration.util.Tools;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.Agent;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.InstallResult;
import com.ideal.service.agentMaintain.AgentMaintainService;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TimeOutInstallThread extends Thread{
    private static final Logger _log = Logger.getLogger(TimeOutInstallThread.class);

    private Map<String, String> map;//安装所需信息
    public TimeOutInstallThread(Map<String, String> map){
        super("安装结果ID");
        this.map = map;
    }
    public  void removeMap(String key){
        map.remove(key);
        map.remove("0");
    }

    @Override
    public void run(){
        while (true){
            try {
                if(map.size()==0){
                    Thread.sleep(5000);
                }else{
                    List<InstallResult> results= getAgentInstallResult();
                    for (InstallResult modle:results){
                        Long currentTime = System.currentTimeMillis();
                        for (String key:map.keySet()){
                            if (Integer.parseInt(key)==modle.getIid()){
                                long time=Long.parseLong(map.get(key));
                                if(currentTime-time>0){
                                    _log.info("currentTime ="+currentTime +" time= " +time);
                                    map.remove(key);
                                    updateInstallInfo(modle.getIid());
                                    //安装失败删除agent信息
                                    delAgent(modle.getIagent_ip(),modle.getIagent_port(),"admin");
                                }
                            }
                        }
                    }
                }
                Thread.sleep(5000);
            }catch (Exception e){
                _log.error("TimeOutInstallThread Exception"+e);
            }
        }
    }

    private List<InstallResult> getAgentInstallResult()
            throws RepositoryException
    {
        int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
        List list = new ArrayList();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Connection con = null;
        try
        {
            String sql ="SELECT IID,ICREATETIME,IAGENT_IP, IAGENT_PORT FROM IEAI_AGENTINSTALL_RESULT WHERE IFINISH=0 ";
            con = DBResource.getConnection("getAgentInstallResult", _log, sysType);
            actStat = con.prepareStatement(sql);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                InstallResult model = new InstallResult();
                model.setIid(actRS.getLong("iid"));
                model.setTimeOut(actRS.getLong("icreatetime"));
                model.setIagent_ip(actRS.getString("IAGENT_IP"));
                model.setIagent_port(actRS.getLong("IAGENT_PORT"));
                list.add(model);
            }

        } catch (SQLException e)
        {
            _log.error("getAgentInstallResult is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(con, actRS, actStat, "getAgentInstallResult", _log);
        }
        return list;
    }

    public InstallResult getAgentInstallState(long iid)
            throws RepositoryException
    {
        int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
        List list = new ArrayList();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        Connection con = null;
        InstallResult model = new InstallResult();
        try
        {
            String sql ="SELECT IID,iinstall_state  FROM IEAI_AGENTINSTALL_RESULT WHERE IID ="+iid;
            con = DBResource.getConnection("getAgentInstallResult", _log, sysType);
            actStat = con.prepareStatement(sql);
            actRS = actStat.executeQuery();

            while (actRS.next())
            {
                model.setIid(actRS.getLong("iid"));
                model.setIinstall_state(actRS.getLong("iinstall_state"));
                list.add(model);
            }

        } catch (SQLException e)
        {
            _log.error("getAgentInstallResult is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(con, actRS, actStat, "getAgentInstallResult", _log);
        }
        return model;
    }

    private void updateInstallInfo ( long iid)
    {
        PreparedStatement actStat = null;
        Connection conn = null;
        String sql = " update ieai_agentinstall_result set ifinish=?,iinstall_state=?,iinstall_msg=? where iid=? ";
        try
        {
            int sysType = AgentMaintainManager.getInstance().getBasicGroupId();
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            actStat = conn.prepareStatement(sql);
            actStat.setLong(1, 1);
            actStat.setLong(2, 2);
            actStat.setString(3, "installation timeout");
            actStat.setLong(4, iid);

            actStat.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSConn(conn, actStat, "updateInstallInfo", _log);
        }
    }

    private void delAgent(String agentip, Long port,String userName) throws Exception {
        //第一步先查询是否有agent 存在
        Agent agent =getInstallAgent(agentip,port);
        if (agent.getIid()>0){
            Long[] deleteIds=new Long[1];
            deleteIds[0]=agent.getIid();
            AgentMaintainService.getInstance().deleteTimeTaskAgentMaintainInfos(deleteIds, userName,0);
        }
    }
    private Agent getInstallAgent (String agentip, Long port) throws Exception
    {
        Agent agent = new Agent();
        Connection baseConn = Tools.getBaseConnectionInfo();
        try
        {
            if (null != baseConn)
            { // 没有基线源，不可以
                agent = AgentMaintainManager.getInstance().getInstallAgent(agentip,port, baseConn);
            }
        } catch (Exception e)
        {
            _log.error("getInstallAgent Exception:" + e.getMessage(), e);
            throw new Exception(e);
        } finally
        {
            DBResource.closeConnection(baseConn, "getAgentMaintainInfoById", _log);
        }
        return agent;
    }

}
