package com.ideal.service.tokenValidate;

public class ZyMonitorTokenResp {

    private int code;
    private String msg;
    private ZyMonitorTokenRespData data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ZyMonitorTokenRespData getData() {
        return data;
    }

    public void setData(ZyMonitorTokenRespData data) {
        this.data = data;
    }
}

class ZyMonitorTokenRespData {
    private String token;

    private String tokenExpiration;

    private boolean needUpdatePwd;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTokenExpiration() {
        return tokenExpiration;
    }

    public void setTokenExpiration(String tokenExpiration) {
        this.tokenExpiration = tokenExpiration;
    }

    public boolean isNeedUpdatePwd() {
        return needUpdatePwd;
    }

    public void setNeedUpdatePwd(boolean needUpdatePwd) {
        this.needUpdatePwd = needUpdatePwd;
    }
}