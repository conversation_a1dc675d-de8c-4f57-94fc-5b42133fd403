package com.ideal.controller.platform.kafkaproxy;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.commons.Constants;
import com.ideal.service.platform.kafkaproxy.BindAgentService;
@Controller
public class BindAgentController
{
    private Logger log = Logger.getLogger(BindAgentController.class);
    
    
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";
    /**
     * 
     * <li>Description:已绑定的agent列表页</li> 
     * <AUTHOR>
     * 2020年2月26日 
     * @return
     * return String
     */
    @RequestMapping("initBindAgentPage.do")
    public String initBindAgentPage ()
    {
        return "platform/kafkaproxy/bindAgentListPage";
    }
    
    /**
     * 
     * <li>Description:进入绑定操作页面</li> 
     * <AUTHOR>
     * 2020年2月27日 
     * @return
     * return String
     */
    @RequestMapping("initConfigAgentPage.do")
    public String initConfigAgentPage ()
    {
        return "platform/kafkaproxy/configAgentListPage";
    }
    
    /**
     * 
     * <li>Description:获取已绑定的agent</li> 
     * <AUTHOR>
     * 2020年2月26日 
     * @param start
     * @param limit
     * @param queryString
     * @return
     * return Map
     */
    @RequestMapping("getKafkaAgentList.do")
    @ResponseBody
    public Map<String,Object> getKafkaAgentList ( Integer start, Integer limit, String queryString , String iparentid)
    {
        BindAgentService service = new BindAgentService();
        return service.getKafkaAgentList( start, limit, queryString,iparentid, Constants.IEAI_OPM);
    }
    
    // 弹窗列表
    @RequestMapping("getKafkaAgentListForChoose.do")
    @ResponseBody
    public Map<String,Object> getKafkaAgentListForChoose ( Integer start, Integer limit, String queryString )
    {
        BindAgentService service = new BindAgentService();
        return service.getAgentList( start, limit, queryString,Constants.IEAI_OPM);
    }
    
    //绑定agent到链路中
    @RequestMapping("doBindAgentToLink.do")
    @ResponseBody
    public Map<String,Object> doBindAgentToLink ( String iids,String iparentid )
    {
        BindAgentService service = new BindAgentService();
        Map<String,Object> map = new HashMap<String,Object>();
        try
        {
            map = service.bindKafkaAgent(iids, iparentid);
        } catch (Exception e)
        {
            log.error("绑定失败",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "绑定失败！");
        }
        return map;
    }
    
    // 删除绑定
    @RequestMapping("deleteKafkaAgent.do")
    @ResponseBody
    public Map<String,Object> deleteKafkaAgent ( String iids )
    {
        BindAgentService service = new BindAgentService();
        Map<String,Object> map = new HashMap<String,Object>();
        try
        {
            map = service.deleteKafkaAgent(iids);
        } catch (Exception e)
        {
            log.error("删除失败",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "删除失败！");
        }
        return map;
    }
}