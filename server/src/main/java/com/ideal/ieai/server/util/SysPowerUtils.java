package com.ideal.ieai.server.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.model.ProjectBean;

/**
 * @desc 业务系统权限类
 * <ul>
 * <li>Title: SysPowerUtils.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2022-6-5
 */
public class SysPowerUtils{

  //日志对象
    private static final Logger _log = Logger.getLogger(SysPowerUtils.class);
    //实例化
    public static SysPowerUtils      intance               = null;
    //创建对象
    public static SysPowerUtils getInstance ()
    {
        if (intance == null)
        {
            intance = new SysPowerUtils();
        }
        return intance;
    }
    public String getSysPowerInfo ( long userId, int type ) throws RepositoryException
    {
        StringBuffer sb = new StringBuffer();
        // 查询权限
        List<ProjectBean> projectList = DataCenterOperationManage.getInstance().getProject(null, userId, type);// 自动化应用变更(3)
        if (projectList.size() == 1)
        {
            if (projectList.get(0).getLastId() == (type * -1)){
                sb.append("AllSysPower");
            } else{
                for (int i = 0; i < projectList.size(); i++){
                    if (i!=projectList.size()-1){
                        sb.append("'"+projectList.get(i).getiName()+"',");
                    }else {
                        sb.append("'"+projectList.get(i).getiName()+"'");
                    }
                }
            }
        } else{
            for (int i = 0; i < projectList.size(); i++){
                if (i!=projectList.size()-1){
                    sb.append("'"+projectList.get(i).getiName()+"',");
                }else {
                    sb.append("'"+projectList.get(i).getiName()+"'");
                }
            }
        }
        _log.info("权限为:"+sb.toString());
        return sb.toString();
    }
    
    public List<String> getSysPowerList ( long userId, int type ) throws RepositoryException{
        
        List<String> filterList = new ArrayList<String>();
        List<ProjectBean> projectList = DataCenterOperationManage.getInstance().getProject(null, userId, type);// 自动化应用变更(3)
        if (projectList.size() == 1)
        {
            if (projectList.get(0).getLastId() == (type * -1))
            {
                filterList.add("AllSysPower");
            } else
            {
                for (ProjectBean projectBean : projectList)
                {
                    filterList.add(projectBean.getiName());
                }
            }
        } else
        {
            for (ProjectBean projectBean : projectList)
            {
                filterList.add(projectBean.getiName());
            }
        }
        return filterList;
    }
    public List<Long> getSysPowerIdList ( long userId, int type ) throws RepositoryException{
        List<Long> filterList = new ArrayList<Long>();
        List<ProjectBean> projectList = DataCenterOperationManage.getInstance().getProject(null, userId, type);// 自动化应用变更(3)
        if (projectList.size() == 1)
        {
            if (projectList.get(0).getLastId() == (type * -1))
            {
                filterList.add(projectList.get(0).getLastId());
            } else
            {
                for (ProjectBean projectBean : projectList)
                {
                    filterList.add(projectBean.getLastId());
                }
            }
        } else
        {
            for (ProjectBean projectBean : projectList)
            {
                filterList.add(projectBean.getLastId());
            }
        }
        return filterList;
    }
}
