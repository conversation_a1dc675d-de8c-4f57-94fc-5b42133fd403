package com.ideal.ieai.agentdamon.util.proxyapi;

import com.ideal.ieai.agentapi.ws.WSRExecRequest;
import com.ideal.ieai.agentdamon.util.agentapi.DataConvertHelper;
import com.ideal.ieai.agentdamon.util.proxyapi.client.ProxyModelnew;
import com.ideal.ieai.agentdamon.util.proxyapi.client.ProxyOpImpl;
import com.ideal.ieai.commons.agent.RemoteActivityExecRequest;
import com.ideal.ieai.core.activity.ActStateData;
import com.ideal.ieai.core.element.Resource;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.core.util.IDataMarshallerHelper;
import org.apache.commons.lang.SerializationUtils;

import javax.xml.namespace.QName;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Service;
import java.net.MalformedURLException;
import java.net.URL;
import java.rmi.RemoteException;
import java.util.Hashtable;
import java.util.Map;

public class ProxyWSClient
{

    private URL                  _agentWSUrl   = null;

    private static String        host;
    private static int           port;
    private static URL           url;
    private static ProxyWSClient _client;
    ProxyOpImpl _agentService = null;

    public ProxyWSClient(String ip, int port)
    {
        try
        {
            url = new URL("http://" + ip + ":" + port + "/proxyOpService?wsdl");
            _agentService = getProxyService();
        } catch (MalformedURLException e)
        {
        }
    }

    public ProxyOpImpl getProxyService ()
    {
        QName qname = new QName("http://proxy.webservice.server.ieai.ideal.com/", "ProxyOpImplService");
        Service service = Service.create(url, qname);
        QName portName = new QName("http://proxy.webservice.server.ieai.ideal.com/", "ProxyOpImplPort");
        ProxyOpImpl serviceImpl = service.getPort(portName, ProxyOpImpl.class);
        BindingProvider bindingProvider = (BindingProvider) serviceImpl;
        Map<String, Object> requestContext = bindingProvider.getRequestContext();
        requestContext.put("com.sun.xml.internal.ws.connection.timeout", 30 * 1000);// 建立连接的超时时间为10秒
        requestContext.put("com.sun.xml.internal.ws.request.timeout", 60 * 1000);// 指定请求的响应超时时间为15秒

        // 在调用接口方法时，内部会发起一个HTTP请求，发起HTTP请求时会从BindingProvider的getRequestContext()返回结果中获取超时参数，
        // 分别对应com.sun.xml.internal.ws.connection.timeout和com.sun.xml.internal.ws.request.timeout参数，
        // 前者是建立连接的超时时间，后者是获取请求响应的超时时间，单位是毫秒。如果没有指定对应的超时时间或者指定的超时时间为0都表示永不超时。

        return serviceImpl;
    }

    public byte[] loadShellFile (ProxyModelnew model, String agentip, String fileName ) throws RemoteException
    {
        return _agentService.loadShellFile(model, agentip, fileName);
    }

    public byte[] remoteDownloadAdaptor (ProxyModelnew model, String RequestUUID, String adaptorName, String AdaptorUUID )
            throws RemoteException
    {
        return _agentService.remoteDownloadAdaptor(model, RequestUUID, adaptorName, AdaptorUUID);
    }

    public synchronized void updateRequestStatus (ProxyModelnew model, String ReuqestUUID, int Status, Hashtable Output,
                                                  Throwable Exception, int version, ActStateData stateData ) throws RemoteException, MarshallingException
    {
        Map encodeMap = IDataMarshallerHelper.translateValueMap(Output);
        _agentService.updateRequestStatus(model, ReuqestUUID, Status, DataConvertHelper.mapToStringMap(encodeMap),
            SerializationUtils.serialize(Exception), version, stateData);
    }

    // public WSRExecRequest getRExecRequest ( ProxyModelnew model, String RequestUUID ) throws
    // RemoteException
    // {
    // WSRExecRequest wsRequest = _agentService.getRExecRequest(model, RequestUUID);
    // return wsRequest;
    // }
    public Resource getResourceByName (ProxyModelnew model, String requestId, String Name ) throws Exception
    {
        String result = _agentService.getResourceByName(model, requestId, Name);
        return IDataMarshallerHelper.unmarshalResource(result);
    }

    public RemoteActivityExecRequest getRExecRequest (ProxyModelnew model, String RequestUUID )
            throws RemoteException, UnMarshallingException
    {
        WSRExecRequest wsRequest = _agentService.getRExecRequest(model, RequestUUID);
        return DataConvertHelper.toRemoteActivityExecRequest(wsRequest);
    }

    public RemoteActivityExecRequest getRExecRequestRemote (ProxyModelnew model, String RequestUUID )
            throws RemoteException, UnMarshallingException
    {
        WSRExecRequest wsRequest = _agentService.getRExecRequest(model, RequestUUID);
        return DataConvertHelper.toRemoteActivityExecRequest(wsRequest);
    }

    public String demo ( String demo, String pwd )
    {
        return _agentService.demo(demo, pwd);
    }

    public String agentHandshakeValidate(ProxyModelnew pm, String requestId, String serviceName, int testFlag) {
        return _agentService.agentHandshakeValidate(pm, requestId,serviceName,testFlag);
    }
    
    public static void main ( String[] a )
    {
        ProxyWSClient aa = new ProxyWSClient("192.168.79.1", 7777);
        aa.demo("aaaa", "bbbbbb");
    }
}
