package com.ideal.ieai.agent.healthcheck.threads;

public class CheckActWarningBean
{
    private long iid;
    private int wtype;
    private int wcode;
    private long wdate;
    private long ldate;
    private String wsource;
    private String prgname;
    private long errcode;
    private String hostname;
    private String ip;
    private String chkvalue;
    private String threadhold;
    private String amessage;
    private int wcount;
    private int tflag;
    private String wmessage;
    private String sysname;
    private String serverip;
    private long cpid;
    private long rsdid;
    private String ibatchnumber;
    
    public long getCpid ()
    {
        return cpid;
    }
    public void setCpid ( long cpid )
    {
        this.cpid = cpid;
    }
    public String getServerip ()
    {
        return serverip;
    }
    public void setServerip ( String serverip )
    {
        this.serverip = serverip;
    }
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public int getWtype ()
    {
        return wtype;
    }
    public void setWtype ( int wtype )
    {
        this.wtype = wtype;
    }
    public int getWcode ()
    {
        return wcode;
    }
    public void setWcode ( int wcode )
    {
        this.wcode = wcode;
    }
    public long getWdate ()
    {
        return wdate;
    }
    public void setWdate ( long wdate )
    {
        this.wdate = wdate;
    }
    public long getLdate ()
    {
        return ldate;
    }
    public void setLdate ( long ldate )
    {
        this.ldate = ldate;
    }
    public String getWsource ()
    {
        return wsource;
    }
    public void setWsource ( String wsource )
    {
        this.wsource = wsource;
    }
    public String getPrgname ()
    {
        return prgname;
    }
    public void setPrgname ( String prgname )
    {
        this.prgname = prgname;
    }
    public long getErrcode ()
    {
        return errcode;
    }
    public void setErrcode ( long errcode )
    {
        this.errcode = errcode;
    }
    public String getHostname ()
    {
        return hostname;
    }
    public void setHostname ( String hostname )
    {
        this.hostname = hostname;
    }
    public String getIp ()
    {
        return ip;
    }
    public void setIp ( String ip )
    {
        this.ip = ip;
    }
    public String getChkvalue ()
    {
        return chkvalue;
    }
    public void setChkvalue ( String chkvalue )
    {
        this.chkvalue = chkvalue;
    }
    public String getThreadhold ()
    {
        return threadhold;
    }
    public void setThreadhold ( String threadhold )
    {
        this.threadhold = threadhold;
    }
    public String getAmessage ()
    {
        return amessage;
    }
    public void setAmessage ( String amessage )
    {
        this.amessage = amessage;
    }
    public int getWcount ()
    {
        return wcount;
    }
    public void setWcount ( int wcount )
    {
        this.wcount = wcount;
    }
    public int getTflag ()
    {
        return tflag;
    }
    public void setTflag ( int tflag )
    {
        this.tflag = tflag;
    }
    public String getWmessage ()
    {
        return wmessage;
    }
    public void setWmessage ( String wmessage )
    {
        this.wmessage = wmessage;
    }
    public String getSysname ()
    {
        return sysname;
    }
    public void setSysname ( String sysname )
    {
        this.sysname = sysname;
    }
    public long getRsdid ()
    {
        return rsdid;
    }
    public void setRsdid ( long rsdid )
    {
        this.rsdid = rsdid;
    }

    public String getIbatchnumber() {
        return ibatchnumber;
    }

    public void setIbatchnumber(String ibatchnumber) {
        this.ibatchnumber = ibatchnumber;
    }
}
