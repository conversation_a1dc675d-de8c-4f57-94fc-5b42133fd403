package com.ideal.ieai.server.timetask.repository.timeout;


public class TimeoutQueryCondition {
    
    private String inpermPrjIdsStr = null;//权限内所有工程IDS
    private String condition;//查询条件
    private String nameCondition;//任务名称
    private String stateCode;//状态码
    private String groupIdCode;//任务组id
    private String startTime;//开始时间
    private String endTime;//结束时间
//    private String searchType;
    
    public String getInpermPrjIdsStr ()
    {
        return inpermPrjIdsStr;
    }
    public void setInpermPrjIdsStr ( String inpermPrjIdsStr )
    {
        this.inpermPrjIdsStr = inpermPrjIdsStr;
    }
    public String getCondition ()
    {
        return condition;
    }
    public void setCondition ( String condition )
    {
        this.condition = condition;
    }
    public String getNameCondition ()
    {
        return nameCondition;
    }
    public void setNameCondition ( String nameCondition )
    {
        this.nameCondition = nameCondition;
    }
    public String getStateCode ()
    {
        return stateCode;
    }
    public void setStateCode ( String stateCode )
    {
        this.stateCode = stateCode;
    }
    public String getGroupIdCode ()
    {
        return groupIdCode;
    }
    public void setGroupIdCode ( String groupIdCode )
    {
        this.groupIdCode = groupIdCode;
    }
    public String getStartTime ()
    {
        return startTime;
    }
    public void setStartTime ( String startTime )
    {
        this.startTime = startTime;
    }
    public String getEndTime ()
    {
        return endTime;
    }
    public void setEndTime ( String endTime )
    {
        this.endTime = endTime;
    }
//    public String getSearchType ()
//    {
//        return searchType;
//    }
//    public void setSearchType ( String searchType )
//    {
//        this.searchType = searchType;
//    }
	
}
