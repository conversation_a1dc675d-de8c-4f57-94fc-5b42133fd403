package com.ideal.ieai.server.repository.hd.proxyManager;

public class PropertyModel
{
    private long iid;
    private long iclusterid;
    private String iinbroker;
    private String igroup;
    private String iip;
    private String iproxyname;
    private long iauth;
    private String iinname;
    private String iinpwd;
    private long imaxretry;
    private long ipollinterval;
    private long imaxblock;
    private long imaxrblock;
    private long iminbyte;
    private long imaxbyte;
    private long ibrokertime;
    private String iintopic;
    private String ioutbroker;
    private String ioutnanme;
    private String ioutpwd;
    private String iouttopic;
    private long itopictype;
    private String iserverip;
    private long iserverport;
    private long ireporttime;
    private String iremark;
    public long getIid (){
        return iid;
    }
    public void setIid ( long iid ){
        this.iid = iid;
    }
    public long getIclusterid (){
        return iclusterid;
    }
    public void setIclusterid ( long iclusterid ){
        this.iclusterid = iclusterid;
    }
    public String getIinbroker (){
        return iinbroker;
    }
    public void setIinbroker ( String iinbroker ){
        this.iinbroker = iinbroker;
    }
    public String getIgroup (){
        return igroup;
    }
    public void setIgroup ( String igroup ){
        this.igroup = igroup;
    }
    public String getIip (){
        return iip;
    }
    public void setIip ( String iip ){
        this.iip = iip;
    }
    public String getIproxyname (){
        return iproxyname;
    }
    public void setIproxyname ( String iproxyname ){
        this.iproxyname = iproxyname;
    }
    public long getIauth (){
        return iauth;
    }
    public void setIauth ( long iauth ){
        this.iauth = iauth;
    }
    public String getIinname (){
        return iinname;
    }
    public void setIinname ( String iinname ){
        this.iinname = iinname;
    }
    public String getIinpwd (){
        return iinpwd;
    }
    public void setIinpwd ( String iinpwd ){
        this.iinpwd = iinpwd;
    }
    public long getImaxretry (){
        return imaxretry;
    }
    public void setImaxretry ( long imaxretry ){
        this.imaxretry = imaxretry;
    }
    public long getIpollinterval (){
        return ipollinterval;
    }
    public void setIpollinterval ( long ipollinterval ){
        this.ipollinterval = ipollinterval;
    }
    public long getImaxblock (){
        return imaxblock;
    }
    public void setImaxblock ( long imaxblock ){
        this.imaxblock = imaxblock;
    }
    public long getImaxrblock (){
        return imaxrblock;
    }
    public void setImaxrblock ( long imaxrblock ){
        this.imaxrblock = imaxrblock;
    }
    public long getIminbyte (){
        return iminbyte;
    }
    public void setIminbyte ( long iminbyte ){
        this.iminbyte = iminbyte;
    }
    public long getImaxbyte (){
        return imaxbyte;
    }
    public void setImaxbyte ( long imaxbyte ){
        this.imaxbyte = imaxbyte;
    }
    public long getIbrokertime (){
        return ibrokertime;
    }
    public void setIbrokertime ( long ibrokertime ){
        this.ibrokertime = ibrokertime;
    }
    public String getIintopic (){
        return iintopic;
    }
    public void setIintopic ( String iintopic ){
        this.iintopic = iintopic;
    }
    public String getIoutbroker (){
        return ioutbroker;
    }
    public void setIoutbroker ( String ioutbroker ){
        this.ioutbroker = ioutbroker;
    }
    public String getIoutnanme (){
        return ioutnanme;
    }
    public void setIoutnanme ( String ioutnanme ){
        this.ioutnanme = ioutnanme;
    }
    public String getIoutpwd (){
        return ioutpwd;
    }
    public void setIoutpwd ( String ioutpwd ){
        this.ioutpwd = ioutpwd;
    }
    public String getIouttopic (){
        return iouttopic;
    }
    public void setIouttopic ( String iouttopic ){
        this.iouttopic = iouttopic;
    }
    public long getItopictype (){
        return itopictype;
    }
    public void setItopictype ( long itopictype ){
        this.itopictype = itopictype;
    }
    public String getIserverip (){
        return iserverip;
    }
    public void setIserverip ( String iserverip ){
        this.iserverip = iserverip;
    }
    public long getIserverport (){
        return iserverport;
    }
    public void setIserverport ( long iserverport ){
        this.iserverport = iserverport;
    }
    public long getIreporttime (){
        return ireporttime;
    }
    public void setIreporttime ( long ireporttime ){
        this.ireporttime = ireporttime;
    }
    public String getIremark (){
        return iremark;
    }
    public void setIremark ( String iremark ){
        this.iremark = iremark;
    }
    @Override
    public String toString ()
    {
        return "PropertyModel [iid=" + iid + ", iclusterid=" + iclusterid + ", iinbroker=" + iinbroker + ", igroup="
                + igroup + ", iip=" + iip + ", iproxyname=" + iproxyname + ", iauth=" + iauth + ", iinname=" + iinname
                + ", iinpwd=" + iinpwd + ", imaxretry=" + imaxretry + ", ipollinterval=" + ipollinterval
                + ", imaxblock=" + imaxblock + ", imaxrblock=" + imaxrblock + ", iminbyte=" + iminbyte + ", imaxbyte="
                + imaxbyte + ", ibrokertime=" + ibrokertime + ", iintopic=" + iintopic + ", ioutbroker=" + ioutbroker
                + ", ioutnanme=" + ioutnanme + ", ioutpwd=" + ioutpwd + ", iouttopic=" + iouttopic + ", itopictype="
                + itopictype + ", iserverip=" + iserverip + ", iserverport=" + iserverport + ", ireporttime="
                + ireporttime + ", iremark=" + iremark + "]";
    }
    
    

}
