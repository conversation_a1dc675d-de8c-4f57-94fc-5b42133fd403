package com.ideal.dubbo.models;

import java.io.Serializable;

public class ScriptDangerCmdBean implements Serializable {
    private String cmd;
    private String type;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
