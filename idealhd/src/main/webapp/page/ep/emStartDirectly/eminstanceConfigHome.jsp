<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<html>
<head>
<script type="text/javascript">
    var loginName = '<%=request.getAttribute("loginName")%>';
<%


	 //ip选择模式转换开关
	 boolean selectConvertSwitch = Environment.getInstance().getBooleanConfig("em.select.ip.model.switch", false);
	 boolean showParamSwitch = Environment.getInstance().getBooleanConfigNew("em.show.parampanel.switch", false);
	 // 预案字段显示顺序开关
	 boolean showOrderSwitch = Environment.getInstance().getBooleanConfigNew2("em.show.order.switch", false);
	 boolean ycShowFilterIpByFlagSwitch = Environment.getInstance().getBooleanConfigNew2("em.yc.filter.ip.switch", false);
	 boolean bhSwitch = Environment.getInstance().getBooleanConfigNew("timetask.bh.special.switch", false);
	 boolean emSwitch = Environment.getInstance().getBooleanConfigNew("em.classify.switch", false);

	 boolean emSelectStep = Environment.getInstance().getBooleanConfigNew("em.start.step.select", false);

    //em.show.ipselect.switch 是为false的时候ip选择模式就关闭了,em.select.ip.model.switch这个开关是切换选择模式，冲突所以必须强制关闭
	 boolean showIpSelectSwitch = Environment.getInstance().getBooleanConfigNew2("em.show.ipselect.switch", true);
     if((!showIpSelectSwitch)||emSelectStep){
         selectConvertSwitch=false;
     }

%>
    var selectConvertSwitch=<%=selectConvertSwitch%>;
    var showParamSwitch=<%=showParamSwitch%>;
    var showOrderSwitch=<%=showOrderSwitch%>;
    var ycShowFilterIpByFlagSwitch=<%=ycShowFilterIpByFlagSwitch%>;
    var bhSwitch =<%=bhSwitch%>;
    var emSwitch =<%=emSwitch%>;
    var showIpSelect=<%=showIpSelectSwitch%>;

    var psbcOrderShow = <%= Environment.getInstance().getItsmOrderNumShowSwitch()%>;
    var bankSwitch='<%=Environment.getInstance().getBankSwitch()%>';
    var psbcOrderFlag=(bankSwitch==="PSBC"&&psbcOrderShow);
</script>
<%if(!emSwitch){%>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/ep/emStartDirectly/eminstanceConfigHome.js"></script>
<%}else{%>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/ep/emStartDirectly/eminstanceConfigHomeWithClassify.js"></script>
<%}%>
</head>
<body>
<div id="emStartDirectly_eminstanceConfigHome_area" style="width: 100%;height: 100%"></div>
</body>
</html>