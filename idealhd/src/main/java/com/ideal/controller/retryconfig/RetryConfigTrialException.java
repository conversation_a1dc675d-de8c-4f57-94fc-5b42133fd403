package com.ideal.controller.retryconfig;

import com.ideal.controller.resourcemanage.exception.AppException;
/**
 * 
 * <ul>
 * <li>Title: RetryConfigTrialException.java</li>
 * <li>Description:作业级异常重试规则配置异常类</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2019年1月2日
 */
public class RetryConfigTrialException extends AppException
{

    public RetryConfigTrialException()
    {
        super();
    }

    public RetryConfigTrialException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public RetryConfigTrialException(String message)
    {
        super(message);
    }

    public RetryConfigTrialException(Throwable cause)
    {
        super(cause);
    }

    public RetryConfigTrialException(int errorCode, String message, Throwable cause)
    {
        super(errorCode, message, cause);
    }

    public RetryConfigTrialException(String traceId, int errorCode, String errorMsg, Throwable cause)
    {
        super(traceId, errorCode, errorMsg, cause);
    }
}
