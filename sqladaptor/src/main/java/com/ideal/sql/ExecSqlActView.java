package com.ideal.sql;

import java.awt.Component;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

import javax.swing.JPanel;
import javax.swing.event.ChangeListener;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.event.TableModelEvent;
import javax.swing.event.TableModelListener;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.activity.IActivityConfigView;
import com.ideal.ieai.core.activity.IConfig;

public class ExecSqlActView extends JPanel
        implements IActivityConfigView, ActionListener, DocumentListener, TableModelListener
{

    private DefaultConfig _config = null;

    /**
     * This is the default constructor
     */
    public ExecSqlActView()
    {
        super();
        initialize();
    }

    /**
     * This method initializes this
     * 
     * @return void
     */
    private void initialize ()
    {
        setName("SqlScript");
    }

    public void initView ( IConfig TheCfg ) throws ActivityException
    {
        initConifg(TheCfg);
    }

    public boolean canRemoteExec ()
    {   
        return true;
    }

    public int getSupportRecoveryPolicies ()
    {
        return Constants.RECOVERY_POLICY_WITHOUT_COMPENSATE;
    }

    public int getDefaultRecoveryPolicy ()
    {
        return Constants.RECOVERY_POLICY_REDO;
    }

    public void initConifg ( IConfig TheCfg ) throws ActivityException
    {
        if (null == TheCfg || !(TheCfg instanceof DefaultConfig))
        {
            throw new ActivityException("It is no default config");
        }
        _config = (DefaultConfig) TheCfg;
    }

    public void save ()
    {
        _config.clear();
    }

    public boolean canSave ()
    {
        return true;
    }

    public Component[] getUIComponents ()
    {
        return new Component[] { this };
    }

    public void close ()
    {

    }

    public List getInputDef () throws ActivityException
    {
        return ExecSqlAct.getScriptInputDef();
    }

    public List getOutputDef ()
    {
        return ExecSqlAct.getScriptOutputDef();
    }

    public String compileActView ()
    {
        return "";
    }

    @Override
    public void tableChanged ( TableModelEvent e )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void insertUpdate ( DocumentEvent e )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void removeUpdate ( DocumentEvent e )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void changedUpdate ( DocumentEvent e )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void actionPerformed ( ActionEvent e )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void discard ()
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void addChangeListener ( ChangeListener TheListener )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void removeChangeListener ( ChangeListener TheListener )
    {
        // TODO Auto-generated method stub

    }

    @Override
    public void removeAllChangeListener ()
    {
        // TODO Auto-generated method stub

    }

}
