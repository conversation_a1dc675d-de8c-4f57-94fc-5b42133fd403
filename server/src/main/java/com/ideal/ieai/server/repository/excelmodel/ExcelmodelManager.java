package com.ideal.ieai.server.repository.excelmodel;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
public class ExcelmodelManager
{

    private static final Logger            _log     = Logger.getLogger(ExcelmodelManager.class);

    private static final ExcelmodelManager _intance = new ExcelmodelManager();

    public static ExcelmodelManager getInstance ()
    {
        return _intance;
    }

    /**
     * 获取作业依赖调度列表
     * @param bean
     * @return
     * @throws RepositoryException
     */
    public Map getExcelmodelList ( ExcelmodelBean bean ) throws RepositoryException
    {
        Map returnMap = new HashMap();
        List excelmodelList = new ArrayList();
        String sqlWhere = "";
        if (StringUtils.isNotEmpty(bean.getImainproname()))
        {
            sqlWhere = sqlWhere + " AND IMAINPRONAME LIKE " + "'%" + bean.getImainproname().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(bean.getImainlinename()))
        {
            sqlWhere = sqlWhere + " AND IMAINLINENAME LIKE " + "'%" + bean.getImainlinename().trim()+ "%' ";
        }
        if (StringUtils.isNotEmpty(bean.getIactname()))
        {
            sqlWhere = sqlWhere + " AND IACTNAME LIKE " + "'%" + bean.getIactname().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(bean.getIsystem()))
        {
            sqlWhere = sqlWhere + " AND ISYSTEM LIKE " + "'%" + bean.getIsystem().trim() + "%' ";
        }
        if (StringUtils.isNotEmpty(bean.getIchildproname()))
        {
            sqlWhere = sqlWhere + " AND ICHILDPRONAME LIKE " + "'%" + bean.getIchildproname().trim() + "%' ";
        }
        String orderString = bean.getOrderString();
        String scString = bean.getScString();
        // 拼接排序语句
        String orderbySql = this.getOrderbyStringFainlForHis(orderString == null ? "" : orderString,
            scString == null ? "" : scString);
        String sql = "";
        if(DBManager.Orcl_Faimily()){
             sql = "SELECT T.IOPERATIONID ,T.IMAINPRONAME,T.IMAINLINENAME,T.IACTNAME,T.ISYSTEM,T.ICHILDPRONAME FROM  "
                + "( SELECT A.*, ROWNUM RN  "
                + "FROM (SELECT * FROM IEAI_EXCELMODEL_COPY WHERE 1=1 "
                + sqlWhere
                + orderbySql + " ) A  " + "WHERE ROWNUM <= ? ) T WHERE T.RN >= ?";
        }else if(JudgeDB.IEAI_DB_TYPE == 2){
            
            sql =  "SELECT T.IOPERATIONID ,T.IMAINPRONAME,T.IMAINLINENAME,T.IACTNAME,T.ISYSTEM,T.ICHILDPRONAME FROM  "
                    + "( SELECT A.*, ROW_NUMBER() OVER() AS  RN  "
                    + "FROM (SELECT * FROM IEAI_EXCELMODEL_COPY WHERE 1=1 "
                    + sqlWhere
                    + orderbySql + " ) A  " + "  )T  WHERE RN <= ? AND RN >= ? ";
        }else if(JudgeDB.IEAI_DB_TYPE == 3){
            
            sql =  "SELECT T.IOPERATIONID ,T.IMAINPRONAME,T.IMAINLINENAME,T.IACTNAME,T.ISYSTEM,T.ICHILDPRONAME FROM  "
                    + "( SELECT A.* "
                    + "FROM (SELECT * FROM IEAI_EXCELMODEL_COPY WHERE 1=1 "
                    + sqlWhere
                    + orderbySql + " ) A  " + "  )T  LIMIT ?,? ";
        }
        String sqlCount = "SELECT COUNT(*) AS NUM FROM IEAI_EXCELMODEL_COPY  WHERE 1=1 " + sqlWhere;

        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    int pageSize = bean.getPageSize();
                    int curPage = bean.getCurPage();
                    con = DBResource.getConnection("getExcelmodelList", _log, Constants.IEAI_IEAI);
                    actStat = con.prepareStatement(sql);
                    if(JudgeDB.IEAI_DB_TYPE == 3){
                        actStat.setInt(1, (curPage - 1) * pageSize);
                        actStat.setInt(2, ((curPage - 1) * pageSize) + pageSize);
                    }else{
                        actStat.setInt(1, ((curPage - 1) * pageSize) + pageSize);
                        actStat.setInt(2, (curPage - 1) * pageSize + 1);
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map map = new HashMap();
                        map.put("ioperationid", actRS.getLong("IOPERATIONID"));
                        map.put("imainproname", actRS.getString("IMAINPRONAME"));
                        map.put("imainlinename", actRS.getString("IMAINLINENAME"));
                        map.put("iactname", actRS.getString("IACTNAME"));
                        map.put("isystem", actRS.getString("ISYSTEM"));
                        map.put("ichildproname", actRS.getString("ICHILDPRONAME"));
                        excelmodelList.add(map);
                    }

                    actStat = con.prepareStatement(sqlCount);
                    actRS = actStat.executeQuery();
                    int total = 0;
                    while (actRS.next())
                    {
                        total = actRS.getInt("NUM");
                    }

                    returnMap.put("dataList", excelmodelList);
                    returnMap.put("total", total);
                } catch (SQLException e)
                {
                    _log.error("getExcelmodelList is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getExcelmodelList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }

        return returnMap;
    }

    /**
     * 拼接排序语句
     * 
     * @param orderString
     * @param scString
     * @return
     */
    public String getOrderbyStringFainlForHis ( String orderString, String scString )
    {
        String orderBegin = " ORDER BY ";
        if (null != orderString && !"".equals(orderString))
        {
            orderBegin = orderBegin + orderString + " " + scString + ",";
        }
        List<String> list = new ArrayList<String>();
        list.add("IOPERATIONID");
        String returnString = "";
        for (int i = 0; i < list.size(); i++)
        {
            String str = list.get(i);

            if (str.equals(orderString))
            {
                continue;
            } else
            {
                returnString = returnString + str + ",";
            }
        }
        return orderBegin + returnString.substring(0, returnString.length() - 1);
    }

}
