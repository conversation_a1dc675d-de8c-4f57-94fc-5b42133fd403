
Ext.onReady (function (){
	var itemsPerPage = 50;
	// 非禁用状态列表数据源Model
	Ext.define ('triggerBloodRelationData',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'FLOOR',
	                type : 'long'
	            },
	            {
	                name : 'IFLOWID',
	                type : 'string'
	            },
	            {
	                name : 'IOPERATIONID',
	                type : 'string'
	            },
	            {
	                name : 'LNAME',
	                type : 'string'
	            },
		        {
		            name : 'IACTNAME',
		            type : 'string'
		        },
		        {
		            name : 'IFLOWINSNAME',
		            type : 'string'
		        },
		        {
		            name : 'ISTATE',
		            type : 'string'
		        },
		        {
		            name : 'IBEGINEXCTIME',
		            type : 'string'
		        },
	            {
	                name : 'IENDTIME',
	                type : 'string'
	            },
	            {
	                name : 'IFLOWID2',
	                type : 'string'
	            },
	            {
	                name : 'IOPERATIONID2',
	                type : 'string'
	            },
	            {
	                name : 'RNAME',
	                type : 'string'
	            },
	            {
	                name : 'IACTNAME2',
	                type : 'string'
	            },
	            {
	                name : 'IFLOWINSNAME2',
	                type : 'string'
	            },
	            {
	                name : 'ISTATE2',
	                type : 'string'
	            },{
	      	      name : 'IBEGINEXCTIME2',
	    	      type : 'string'
	    	    },{
	    		  name : 'IENDTIME2',
	    		  type : 'string'
	    		}, {
	    	      name : 'GETORGISTATUS',
	    	      type : 'string'
	    	    }
	    ]
	});
	
	/** 列表store* */
	var home_trigger_store = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'triggerBloodRelationData',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'bloodRelationshipQueryTrial/deployAndTrigger.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	
	home_trigger_store.on ('beforeload', function (store, options)
		{
		var new_params =
		{
				floorNum : floorNum,
				flagType: 'trigger',//类型:依赖  触发
				jsonString : jsonString
		};
		Ext.apply (home_trigger_store.proxy.extraParams, new_params);
		});
	
	
	home_trigger_store.on ('load', function (r, options, success)
			{
				Ext.MessageBox.hide ();
			});
	
	// 显示错误信息用
	var home_trigger_state = Ext.create ('Ext.Button',
	{
	    text : '',
	    margin : '0 0 0 10'
	});
	
	
	var combo_trigger = Ext.create ('Ext.form.ComboBox',
			{
			    name : 'pagesize',
			    hiddenName : 'pagesize',
			    store : new Ext.data.ArrayStore (
			    {
			        fields : [
			                'text', 'value'
			        ],
			        data : [
			                [
			                        '50', 50
			                ], [
			                        '100', 100
			                ], [
			                        '150', 150
			                ]
			        ]
			    }),
			    valueField : 'value',
			    displayField : 'text',
			    emptyText : 50,
			    width : 50
			});
			/** *********************Panel********************* */
	// 列表PagingToolbar
	var	pageBar_trigger_home = Ext.create ('Ext.PagingToolbar',
		{
		    id : 'pagingbar4',
		    store : home_trigger_store,
		    dock : 'bottom',
		    displayInfo : true,
		    items : [
		            '-', combo_trigger, '->', home_trigger_state
		    ]
		
		});
			
		// 添加下拉显示条数菜单选中事件
		combo_trigger.on ("select", function (comboBox)
		{
			var pagingToolbar = Ext.getCmp ('pagingbar4');
			pagingToolbar.pageSize = parseInt (comboBox.getValue ());
			itemsPerPage = parseInt (comboBox.getValue ());// 更改全局变量itemsPerPage
			home_trigger_store.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
			search ();
		});
		
		
	   var job_trigger_list_columns = [
		                         {
		                 	        text : '层级',
		                 	        width : 40,
		                 	        dataIndex : 'FLOOR'
		                 	    },{
		                 	        text : '工作流ID',
		                 	        width : 80,
		                 	        dataIndex : 'IFLOWID'
		                 	    },{
		                 	    	text : '作业名ID',
		                 	    	width : 80,
		                 	        dataIndex : 'IOPERATIONID'
		                 	    },{
		                 	    	text : '主线名',
		                 	    	width : 100,
		                 	        dataIndex : 'LNAME'
		                 	    },{
		                 	    	text : '作业名',
		                 	    	width : 100,
		                 	        dataIndex : 'IACTNAME'
		                 	    },{
		                 	    	text : '数据日期',
		                 	    	width : 100,
		                 	        dataIndex : 'IFLOWINSNAME'
		                 	   /* 	renderer : function(value, p, r) {
		                 				var iisbasic = r.get('istate');
		                 				if (0 == iisbasic) {
		                 					var temp = "是";
		                 				} else {
		                 					temp = "否";
		                 				}
		                 				return temp;
		                 			}*/
		                 	    },{
		                 	    	text : '状态',
		                 	    	//hidden: true,
		                 	    	width : 40,
		                 	        dataIndex : 'ISTATE'
		                 	    },{
		                 	    	text : '开始时间',
		                 	    	width : 145,
		                 	        dataIndex : 'IBEGINEXCTIME'
		                 	    },{
		                 	    	text : '结束时间',
		                 	    	width : 145,
		                 	        dataIndex : 'IENDTIME'
		                 	    },{
		                 	    	text : '工作流ID',
		                 	    	width : 80,
		                 	        dataIndex : 'IFLOWID2'
		                 	    },{
		                 	    	text : '作业名ID',
		                 	    	width : 80,
		                 	        dataIndex : 'IOPERATIONID2'
		                 	    },{
		                 	    	text : '主线名',
		                 	    	width : 60,
		                 	        dataIndex : 'RNAME'
		                 	    },{
		                 	    	text : '作业名',
		                 	    	width : 60,
		                 	        dataIndex : 'IACTNAME2'
		                 	    },{
		                 	    	text : '数据日期',
		                 	    	width : 100,
		                 	        dataIndex : 'IFLOWINSNAME2'
		                 	    },{
		                 	    	text : '状态',
		                 	    	width : 40,
		                 	        dataIndex : 'ISTATE2'
		                 	    },{
		                 	    	text : '开始时间',
		                 	    	width : 145,
		                 	        dataIndex : 'IBEGINEXCTIME2'
		                 	    },{
		                 	    	text : '结束时间',
		                 	    	width : 145,
		                 	        dataIndex : 'IENDTIME2'
		                 	    }];
		// 导出按钮
	var	dc_btn = Ext.create ('Ext.Button',
		{
		    text : '导出',
		    cls : 'Common_Btn',
		    handler : exportflowFun
		});
	// 列表grid
	var	home_trigger_grid = Ext.create ('Ext.ux.ideal.grid.Panel',
		{
		    store : home_trigger_store,
		    columnLines : true,
		    columns : job_trigger_list_columns,
		    cls:'customize_panel_back',
		    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',     
		    height : screen.height - 400,
		    //selModel : selModel,
		    viewConfig :
		    {
		        selType : 'cellmodel',
		        stripeRows : true,// 在表格中显示斑马线
		        enableTextSelection : true,
		        loadMask : false
		    },
		    dockedItems : [
			    {
			        xtype : 'toolbar',
			        items : [
	                        dc_btn,
			        ]
			    }
		    ],
//		    bbar : pageBar_trigger_home
		});
	
	
		/** 主panel* */
		var mainDeployPanel = Ext.create ('Ext.panel.Panel',
		{
		    renderTo : "mainpanelES",
		    width : '100%',
		    height : '100%',
		    border : false,
		    bodyPadding : 5,
		    items : [
		             home_trigger_grid
		    ]
		});
		
		
		function exportflowFun(){
			
			var parms = {
	    		floorNum : floorNum,
	    		jsonString : jsonString
	    	};
	    	var form = construtForm ('bloodRelationshipTrialQuery.do?action=cfxygxdaochu', parms);
	    	form.submit ();
		}
		
		function search(){
			home_trigger_grid.ipage.moveFirst ();
		}
	 
		
		  /** 构建导出form* */
	    function construtForm (actionUrl, parms)
	    {
	    	var form = document.createElement ("form");
	    	form.style.display = 'none';
	    	form.action = actionUrl;
	    	form.method = "post";
	    	document.body.appendChild (form);
	    	
	    	for ( var key in parms)
	    	{
	    		var input = document.createElement ("input");
	    		input.type = "hidden";
	    		input.name = key;
	    		input.value = parms[key];
	    		form.appendChild (input);
	    	}
	    	return form;
	    	
	    }
});