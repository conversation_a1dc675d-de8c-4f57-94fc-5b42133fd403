package com.ideal.ieai.studio.view.editorui.flowchart;

import java.awt.event.MouseEvent;
import java.awt.geom.Point2D;

import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.util.graph.StructCell;

public class SequenceState
    extends BasicChartState
{
    public SequenceState(FlowChartPane FlowPane, Workflow TheFlow)
    {
        super(FlowPane, TheFlow);
    }

    public void mouseReleased(MouseEvent e)
    {
        Point2D pt = graph.fromScreen(e.getPoint());
        StructCell group = FlowGraphHelper.getStructByPos(pt, pane);
        FlowGraphHelper.insertStructCell(pane,group,  StructCell.SEQUENCE_STRUCT,pt);
    }
}
