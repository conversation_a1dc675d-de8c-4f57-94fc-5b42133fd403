package com.ideal.ieai.server.engine.execactivity;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.activity.*;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.element.workflow.ProcActActElement;
import com.ideal.ieai.server.jobscheduling.repository.home.StringUtils;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import org.apache.commons.dbcp.BasicDataSource;
import org.apache.log4j.Logger;

import java.sql.*;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcAct implements IActivity {
    private ProcActActElement _procElem = null;
    ActivityElement _actElem = null;
    private Map _reourceMap = null;
    private String _procName = null;
    private String _actName = null;
    private static final Logger _log = Logger.getLogger(ProcAct.class);

    public ProcAct(ActivityElement procElem) {
        _actElem = procElem;
    }

    @Override
    public void init(IConfig TheCfgs) throws OtherException {
        try {
            _reourceMap = getDataReource();
            _procName = getProcName(_actElem.getName());
//            _actName = _actElem.getName();
        } catch (RepositoryException e)
        {
            _log.error("init is error!",e);
        }
    }

    @Override
    public int execute(ActStateData actStateData, Map inputs, Map outputs) throws ActivityExecutingException, OtherException {
        int state = Constants.END;
        Connection conn = null;
        CallableStatement call = null;
        try
        {
            BasicDataSource jdbcDataSource = new BasicDataSource();
            if(!_reourceMap.isEmpty())
            {
                jdbcDataSource.setDriverClassName(String.valueOf(_reourceMap.get("idrive")));
                jdbcDataSource.setUrl(String.valueOf(_reourceMap.get("iurl")));
                jdbcDataSource.setUsername(String.valueOf(_reourceMap.get("iuser")));
                jdbcDataSource.setPassword(String.valueOf(_reourceMap.get("ipwd")));
            }

            jdbcDataSource.setInitialSize(10);
            jdbcDataSource.setMaxActive(20);
            jdbcDataSource.setMaxIdle(4);
            jdbcDataSource.setMinIdle(2);

            conn = jdbcDataSource.getConnection();
            conn.setAutoCommit(false);

            if(StringUtils.isNotBlank(_procName))
            {
                _log.info("========_procName is :" + _procName);
                call = conn.prepareCall(_procName);
                call.executeQuery();
                conn.commit();
            }
        } catch (Exception e)
        {
            _log.error("execute is error!",e);
        }finally
        {
            try
            {
                if (null != call)
                {
                    call.close();
                    call = null;
                }
                if (null != conn && !conn.isClosed())
                {
                    conn.close();
                }
                conn = null;
            } catch (SQLException e)
            {
                e.printStackTrace();
            }
        }

        return state;
    }

    @Override
    public int compensate(ActStateData actStateData, Map input, Map output) throws ActivityException {
        return 0;
    }

    @Override
    public List getInputDef() throws OtherException {
        return Collections.emptyList();
    }

    @Override
    public List getOutputDef() throws OtherException {
        return Collections.emptyList();
    }

    @Override
    public void dispose() {

    }

    @Override
    public boolean validate() {
        return true;
    }

    public Map getDataReource () throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(method + "is error!", e);
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select * from ieai_custom_report_datasource";
                    ps = con.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        map.put("iname", rs.getString("iname"));
                        map.put("idrive", rs.getString("idriver_class"));
                        map.put("iurl", rs.getString("ijdbc_url"));
                        map.put("iuser", rs.getString("iuser"));
                        map.put("ipwd", rs.getString("ipassword"));
                        break;
                    }
                } catch (SQLException e)
                {
                    _log.error(method + " is error! ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, rs, ps,Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            return map;
        }
    }

    public String getProcName (String flowName ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String procName = "";
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
                } catch (DBException e)
                {
                    _log.error(method + "is error!", e);
                    throw new RepositoryException(ServerError.ERR_DB_INIT);
                }
                try
                {
                    String sql = "select ISHELLABSOLUTEPATH from ieai_excelmodel ie,ieai_workflowinstance iw where ie.ICHILDPRONAME = iw.IPROJECTNAME and ie.IACTNAME = iw.IFLOWNAME and iw.Iflowname = ?";
                    ps = con.prepareStatement(sql);
                    ps.setString(1, flowName);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        procName = rs.getString("ISHELLABSOLUTEPATH");
                        break;
                    }
                } catch (SQLException e)
                {
                    _log.error(method + " is error! ", e);
                    DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, rs, ps,Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
            return procName;
        }
    }
}
