-- 4.7.17 version does not have patches for timed tasks 

-- 4.7.18 version timing task patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='PAUSE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO	ADD PAUSE INTEGER  DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='TASKTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO	ADD TASKTYPE INTEGER  DEFAULT -1';  
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

-- 4.7.19 version timing task patch is as follows
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 

		  
SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TASKDATERECORD' AND OBJECT_TYPE = 'TABLE';
	  IF LI_EXISTS = 0 THEN
		   LS_SQL := 'CREATE TABLE IEAI_TASKDATERECORD(IID  NUMBER(19) PRIMARY KEY NOT NULL ,TASKNAME VARCHAR2 (255) ,TASKRUNTIME VARCHAR2 (255),TASKCOMMAND VARCHAR2(1000),TASKGROUP VARCHAR2 (255),ACTIONTIME TIMESTAMP,FLAG VARCHAR2 (2))';
		   EXECUTE IMMEDIATE LS_SQL;
	  END IF;		  
END;
/

DECLARE
	LS_SQL    VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN
	
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_IMMED_INFO' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		  LS_SQL := 'create table IEAI_TIMETASK_IMMED_INFO ( ID NUMBER(19) not null,TASK_ID    NUMBER(19) not null,SERVERIP   VARCHAR2(255),EXECSTATUS INTEGER default -1,CONSTRAINT PK_IEAI_TIMETASK_IMMED_INFO PRIMARY KEY(ID))'; 
		  EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  
END;
/
DECLARE
	LS_SQL    VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN
	
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_APPOINTMENT_INFO' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		  LS_SQL := 'create table IEAI_TIMETASK_APPOINTMENT_INFO(ID NUMBER(19) not null,TASK_ID NUMBER(19) not null,EXPECTEDTIME VARCHAR2(22),EXPECT_TYPE INTEGER,TASKSTATUS INTEGER default 0,SERVERIP VARCHAR2(255),CONSTRAINT PK_IEAI_TT_APPOINTMENT_INFO PRIMARY KEY(ID))'; 
		  EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  
END;
/

-- 4.7.20 version timing task patch is as follows
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	
	
	SELECT SIGN(COUNT(COLUMN_NAME)) INTO LI_EXISTS FROM USER_CONS_COLUMNS CU, USER_CONSTRAINTS AU WHERE CU.CONSTRAINT_NAME = AU.CONSTRAINT_NAME AND AU.CONSTRAINT_TYPE = 'P' AND AU.TABLE_NAME = 'IEAI_AGENTINFO_MID' ;
		IF LI_EXISTS > 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO_MID DROP PRIMARY KEY';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
			SELECT SIGN(COUNT(COLUMN_NAME)) INTO LI_EXISTS FROM USER_CONS_COLUMNS CU, USER_CONSTRAINTS AU WHERE CU.CONSTRAINT_NAME = AU.CONSTRAINT_NAME AND AU.CONSTRAINT_TYPE = 'P' AND AU.TABLE_NAME = 'IEAI_PROJECT_MID' ;
		IF LI_EXISTS > 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_PROJECT_MID DROP PRIMARY KEY';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		SELECT SIGN(COUNT(COLUMN_NAME)) INTO LI_EXISTS FROM USER_CONS_COLUMNS CU, USER_CONSTRAINTS AU WHERE CU.CONSTRAINT_NAME = AU.CONSTRAINT_NAME AND AU.CONSTRAINT_TYPE = 'P' AND AU.TABLE_NAME = 'IEAI_SYS_RELATION_MID' ;
		IF LI_EXISTS > 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SYS_RELATION_MID DROP PRIMARY KEY';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		
		
END;
/

-- 4.7.21 version does not have patches for timed tasks
-- 4.7.22 version does not have patches for timed tasks


	
-- 4.7.24 version timing task patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_AUDIT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_AUDIT (IID NUMBER(19) NOT NULL, IPROJECTNAME VARCHAR2(255), ITASKNAME VARCHAR2(255), ITASKRUNTIME VARCHAR2(255), ITASKCOMMAND VARCHAR2(1000), ITASKGROUP VARCHAR2(255), ITASKDESC VARCHAR2(255), IRESOURCES VARCHAR2(255), IIP VARCHAR2(1800), IENABLE INTEGER, ISERVERIP VARCHAR2(255), IGID NUMBER(19), IOLDSERVERIP VARCHAR2(255), IRECOVERYTIME TIMESTAMP(6), ISTOPINSFLAG INTEGER, ITIMEOUTMINS NUMBER(19) DEFAULT 0, ISTARTFORBIDSTATUS INTEGER DEFAULT 1, IPAUSE INTEGER DEFAULT 0, ITASKTYPE INTEGER DEFAULT -1, ISTATUS NUMBER(1) DEFAULT 0,CONSTRAINT PK_IEAI_TIMETASK_AUDIT PRIMARY KEY (IID))';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/
	
-- 4.7.25 version does not have patches for timed tasks
-- 4.7.26 version timing task patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_OUTPUT_IPID' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_TIMETASK_OUTPUT_IPID ON IEAI_TIMETASK_OUTPUT(IPID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_HIS_TASKID' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_TIMETASK_HIS_TASKID ON IEAI_TIMETASK_HIS (TASKID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_IP_HIS_IP' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_TIMETASK_IP_HIS_IP ON IEAI_TIMETASK_IP_HIS (IP)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_TIMETASK_IP_HIS_TASKHISID' AND OBJECT_TYPE = 'INDEX';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_TIMETASK_IP_HIS_TASKHISID ON IEAI_TIMETASK_IP_HIS (TASKHISID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
END;
/
-- 4.7.27 version timing task patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_INSTANCE_HIS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_INSTANCE_HIS ( ID   NUMBER(19,0) NOT NULL,TASKINFOID     NUMBER(19,0) NOT NULL,TASKNAME  VARCHAR2(255),TASKRUNTIME    VARCHAR2(255),TASKCOMMAND    VARCHAR2(1000),IP   VARCHAR2(1800),GID  NUMBER(19,0),TASKGROUPNAME  VARCHAR2(255),SERVERIP  VARCHAR2(255),TASKDESC VARCHAR2(255) DEFAULT '''',TIMEOUTMINS NUMBER(19,0) DEFAULT 0,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP, CONSTRAINT PK_IEAI_TIMETASK_INSTANCE_HIS PRIMARY KEY(ID))';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_CONFIG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_CONFIG(ID NUMBER(19,0) NOT NULL,TEXT VARCHAR2(100), FLAG NUMBER(19,0),TEXTVALUE VARCHAR2(100),TYPE VARCHAR2(100) )';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_INSTANCE_CACH' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_INSTANCE_CACH ( ID   NUMBER(19,0) NOT NULL,TASKINFOID     NUMBER(19,0) NOT NULL,TASKNAME  VARCHAR2(255),TASKRUNTIME    VARCHAR2(255),TASKCOMMAND    VARCHAR2(1000),IP   VARCHAR2(1800),GID  NUMBER(19,0),TASKGROUPNAME  VARCHAR2(255),SERVERIP  VARCHAR2(255),TASKDESC VARCHAR2(255) DEFAULT '''',TIMEOUTMINS NUMBER(19,0) DEFAULT 0, CONSTRAINT PK_IEAI_TIMETASK_INSTANCE_CACH PRIMARY KEY(ID))';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_RUNTIME_CACH' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_RUNTIME_CACH ( TASKHISID  NUMBER(19,0) NOT NULL,TASKID NUMBER(19,0) NOT NULL,STARTTIME  TIMESTAMP NOT NULL,ENDTIME TIMESTAMP NOT NULL,INSRESULT INTEGER NOT NULL,IMMED INTEGER ,CONSTRAINT IEAI_TIMETASK_RUNTIME_CACH PRIMARY KEY(TASKHISID)  ) ';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
	LS_SQL VARCHAR2(4000); 
	LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_IP_CACH' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		    LS_SQL := 'CREATE TABLE IEAI_TIMETASK_IP_CACH ( ID      NUMBER(19,0) NOT NULL,TASKHISID    NUMBER(19,0),IP      VARCHAR2(30),TASKIPSTATE  INTEGER,CONSTRAINT IEAI_TIMETASK_IP_CACH PRIMARY KEY(ID))';
		    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
		 LS_SQL VARCHAR2(4000);
		 LI_EXISTS SMALLINT;
BEGIN 

	SELECT COUNT(ID) INTO LI_EXISTS  FROM IEAI_TIMETASK_CONFIG  P WHERE P.FLAG='1';  
		IF	LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_TIMETASK_CONFIG (ID,TEXT,FLAG,TEXTVALUE,TYPE) VALUES ( ''5'',''定时任务历史数据转移'',''1'',''2'',''定时任务'')';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;

END;
/

-- 8.0.0 version timing task patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IP' AND COLUMN_NAME='ICOM_NAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IP	ADD ICOM_NAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IP_HIS' AND COLUMN_NAME='ICOM_NAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IP_HIS	ADD ICOM_NAME VARCHAR2(255)';  
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

-- 8.2.0 version timing task patch is as follows	
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='IP';
			IF LI_EXISTS = 1 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO RENAME COLUMN IP TO IP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='IP';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO add IP CLOB';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME in('IP','IP_TMP');
			IF LI_EXISTS = 2 THEN
				LS_SQL := 'UPDATE IEAI_TIMETASK_INFO SET IP=IP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME in('IP','IP_TMP');
			IF LI_EXISTS = 2 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO DROP COLUMN IP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME='IIP';
			IF LI_EXISTS = 1 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_AUDIT RENAME COLUMN IIP TO IIP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME='IIP';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_AUDIT add IIP CLOB';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME in('IIP','IIP_TMP');
			IF LI_EXISTS = 2 THEN
				LS_SQL := 'UPDATE IEAI_TIMETASK_AUDIT SET IIP=IIP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME in('IIP','IIP_TMP');
			IF LI_EXISTS = 2 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_AUDIT DROP COLUMN IIP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME='IP';
			IF LI_EXISTS = 1 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE RENAME COLUMN IP TO IP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME='IP';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE add IP CLOB';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE';
			IF LI_EXISTS = 1 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE NOLOGGING';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			
			SELECT COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME in('IP','IP_TMP');
			IF LI_EXISTS = 2 THEN
				LS_SQL := 'UPDATE IEAI_TIMETASK_INSTANCE SET IP=IP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE';
			IF LI_EXISTS = 1 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE LOGGING';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
			
			SELECT COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME in('IP','IP_TMP');
			IF LI_EXISTS = 2 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE DROP COLUMN IP_TMP';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME='TASKLEVEL';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_AUDIT  ADD  TASKLEVEL NUMBER(2) DEFAULT 1';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='TASKLEVEL';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO  ADD  TASKLEVEL NUMBER(2) DEFAULT 1';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
END;
/
-- 8.3.0 version timing task patch is as follows
DECLARE
	LS_SQL    VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN
	
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_RUNDATA_STORAGE' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		  LS_SQL := 'create table IEAI_TIMETASK_RUNDATA_STORAGE ( IID NUMBER(19) not null,TASKID VARCHAR2(255),ITIME VARCHAR2(255),GID VARCHAR2(255),GNAME VARCHAR2(255),TASKSUM VARCHAR2(255),TASKNAME VARCHAR2(255),CRONDAYTOTALNUM VARCHAR2(255),EFFECTNUM VARCHAR2(255),INVALIDNUM VARCHAR2(255),NORMALNUM VARCHAR2(255),EXCEPTIONNUM VARCHAR2(255),FORCESTORNUM VARCHAR2(255),EXECTOTALNUM VARCHAR2(255),TASKENABLE VARCHAR2(255), CONSTRAINT PK_TT_RUNDATA_STORAGE PRIMARY KEY(IID))'; 
		  EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  
END;
/

DECLARE
	LS_SQL    VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN
	
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_LOAD_SWITCH' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		  LS_SQL := 'create table IEAI_TIMETASK_LOAD_SWITCH ( IID NUMBER(19) not null,TASKID NUMBER(19),ITIME VARCHAR2(19),SWITCHUSER VARCHAR2(50),OLDSERVERIP VARCHAR2(20),SERVERIP VARCHAR2(20),SERVERID NUMBER(19),STATUS INTEGER default 0,OLDENABLE VARCHAR2(50), CONSTRAINT PK_TT_LOAD_SWITCH PRIMARY KEY(IID))'; 
		  EXECUTE IMMEDIATE LS_SQL;
	  END IF;
	  
END;
/

-- 8.4.0 version does not have patches for timed tasks
-- 8.5.0 version does not have patches for timed tasks
-- 8.6.0 version timing task patch is as follows
    DECLARE
         LS_SQL VARCHAR2(2000);
         LI_EXISTS SMALLINT;
    BEGIN

            SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='IREDOTIME';
            IF	LI_EXISTS = 0 THEN
                LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD IREDOTIME NUMBER(19) DEFAULT 0';
                EXECUTE IMMEDIATE LS_SQL;
            END IF;
            COMMIT;

            SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='IREDONUM';
            IF	LI_EXISTS = 0 THEN
                LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD IREDONUM NUMBER(19) DEFAULT 0';
                EXECUTE IMMEDIATE LS_SQL;
            END IF;
            COMMIT;
    END;
/
    DECLARE
         LS_SQL VARCHAR2(2000);
         LI_EXISTS SMALLINT;
    BEGIN

            SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IP' AND COLUMN_NAME='IREDONUM';
            IF	LI_EXISTS = 0 THEN
                LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IP ADD IREDONUM NUMBER(19) DEFAULT 0';
                EXECUTE IMMEDIATE LS_SQL;
            END IF;
            COMMIT;
			
			SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=15;
			IF  LI_EXISTS = 0 THEN
				LS_SQL :='insert into IEAI_SCRIPT_AUDITING (IID,INAME)  VALUES(15,''定时任务发布专审权限'')';
			  EXECUTE IMMEDIATE LS_SQL;
			END  IF;
			COMMIT;
    END;
/
	
-- 8.8.0 version timing task patch is as follows
	DECLARE
		LS_SQL VARCHAR2(4000);
		LI_EXISTS SMALLINT;
	BEGIN 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME='EXECUTE_CENTER';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_TIMETASK_AUDIT  ADD  EXECUTE_CENTER VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	END;
/
	
	DECLARE
		LS_SQL VARCHAR2(4000);
		LI_EXISTS SMALLINT;
	BEGIN 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='EXECUTE_CENTER';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO  ADD  EXECUTE_CENTER VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	END;
/
	
	DECLARE
		LS_SQL VARCHAR2(4000);
		LI_EXISTS SMALLINT;
	BEGIN 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME='EXECUTE_CENTER';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE  ADD  EXECUTE_CENTER VARCHAR2(10)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	END;
/
	
	DECLARE
		LS_SQL VARCHAR2(4000);
		LI_EXISTS SMALLINT;
	BEGIN 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IMMED_INFO' AND COLUMN_NAME='IIDSTR';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IMMED_INFO  ADD  IIDSTR VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	END;
/
	
	DECLARE
	LS_SQL    VARCHAR2(4000);
	LI_EXISTS SMALLINT;
	BEGIN
		
		  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_LOSTTHENHELP' AND OBJECT_TYPE = 'TABLE';
		   IF LI_EXISTS = 0 THEN
			  LS_SQL := 'CREATE TABLE IEAI_TIMETASK_LOSTTHENHELP(ID NUMBER(19) NOT NULL PRIMARY KEY,TASKNAME VARCHAR2(255),TASKINFOID NUMBER(19),TASKRUNTIME VARCHAR2(255),TASKCOMMAND VARCHAR2(1000),TASKDESC VARCHAR2(255),IP VARCHAR2(1800),TASKGROUP  VARCHAR2(255),GID VARCHAR2(255),LOSTNUM NUMBER(19),TASKSTATE INTEGER,MESSAGEINFO VARCHAR2(255),RESTARTTIME TIMESTAMP,DEALTIME TIMESTAMP)'; 
			  EXECUTE IMMEDIATE LS_SQL;
		  END IF;
		  
	END;
/
	
	DECLARE
		LS_SQL VARCHAR2(4000);
		LI_EXISTS SMALLINT;
	BEGIN 
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IMMED_INFO' AND COLUMN_NAME='COMFROM';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IMMED_INFO  ADD  COMFROM INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
	END;
/

	

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='IIMPORTANCE';
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD IIMPORTANCE VARCHAR2(1)'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMETASK_WARNRELATION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_TIMETASK_WARNRELATION (IID NUMBER(19) NOT NULL,ITIMETASKID NUMBER(19),IUSERID NUMBER(19), CONSTRAINT PK_IEAI_TT_WARNRELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 

		  
SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TASKBINDUSER' AND OBJECT_TYPE = 'TABLE';
	  IF LI_EXISTS = 0 THEN
		   LS_SQL := 'CREATE TABLE IEAI_TASKBINDUSER ( IID NUMBER(19) NOT NULL,TASKID NUMBER(19),MODULETYPE NUMBER(2),USERID NUMBER(19),USERNAME VARCHAR2(255),TASKNAME VARCHAR2(255),UPDATETIME TIMESTAMP(6),OPERATEUSER VARCHAR2(255),ISEXTEND NUMBER(1),ISSENDALERM NUMBER(1),ISSENDEMAIL NUMBER(1),ISSENDMESSAGE NUMBER(1),CONSTRAINT PK_IEAI_TASKBINDUSER PRIMARY KEY (IID))';
		   EXECUTE IMMEDIATE LS_SQL;
	  END IF;		  
END;
/

DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IP' AND COLUMN_NAME='HISDESCIP';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IP	ADD HISDESCIP VARCHAR2(100)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	
END;
/

DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IP_HIS' AND COLUMN_NAME='HISDESCIP';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IP_HIS	ADD HISDESCIP VARCHAR2(100)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	
END;
/

-- 8.14.0 version timing task patch is as follows
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PRT_ITEM_APP' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_PRT_ITEM_APP (APL_CODE VARCHAR2 (255) ,ITEM_CD_LVL1 VARCHAR2 (60) ,ITEM_CD_LVL2 VARCHAR2 (60) ,ITEM_CD_APP VARCHAR2 (200) ,ITEM_APP_FLAG NUMBER (19) ,EXPRESSION VARCHAR2 (1000) ,IMPORTANT_FLAG NUMBER (19) ,ITEM_CREATE_USER VARCHAR2 (10) ,ITEM_CREATE_TIME TIMESTAMP(6)  ,ITEM_APP_NAME VARCHAR2 (200) )';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PRT_ITEM_BASE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_PRT_ITEM_BASE (ITEM_CD VARCHAR2 (50) ,PARENT_ITEM_CD VARCHAR2 (50) ,ITEM_NAME VARCHAR2 (200) ,PELATION_TABLENAME VARCHAR2 (200) ,ITEM_CREATE_USER VARCHAR2 (10) ,ITEM_CREATE_TIME TIMESTAMP(6)  )';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_CAP_THRESHOLD_ACQ' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_CAP_THRESHOLD_ACQ (APL_CODE VARCHAR2 (255) ,ITEM_CD_APP VARCHAR2 (200) ,ITEM_CREATE_USER VARCHAR2 (10) ,ITEM_CREATE_TIME TIMESTAMP(6)  ,THRESHOLD VARCHAR2 (20) ,THRESHOLD_RULE VARCHAR2 (1) ,THRESHOLD_DATE VARCHAR2 (8) ,THRESHOLD_EXPLAIN VARCHAR2 (300) )';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='SCRIPTVERSION';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  SCRIPTVERSION VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='SCRIPTSJC';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  SCRIPTSJC VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='BUSINESSTYPE';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  BUSINESSTYPE INTEGER DEFAULT 1'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='SCRIPTTYPE';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  SCRIPTTYPE INTEGER DEFAULT 1'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='SCRIPTID';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  SCRIPTID NUMBER(19) DEFAULT -1'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='SCRIPTUUID';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  SCRIPTUUID VARCHAR2(50)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='EXECUSER';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  EXECUSER VARCHAR2(50)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='ISCRIPTNAME';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  ISCRIPTNAME VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='SCRIPTPARAM';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  SCRIPTPARAM VARCHAR2(2000)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='ISERVICESNAME';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  ISERVICESNAME VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='IPATH';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  IPATH VARCHAR2(512)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IMMED_INFO' AND COLUMN_NAME='SCRIPTSJC';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IMMED_INFO ADD  SCRIPTSJC VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATE_TRANS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_DATE_TRANS (iid NUMBER(19) NOT NULL,trans_date  VARCHAR2(8), datasource_flag NUMBER(1) default 0, count_item   VARCHAR2(300), count_amount  VARCHAR2(18),  apl_code  VARCHAR2(10), file_name  VARCHAR2(255), CONSTRAINT PK_IEAI_DATE_TRANS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BATCH_TRANS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_BATCH_TRANS (iid NUMBER(19) NOT NULL,batch_date  VARCHAR2(8),  batch_name       VARCHAR2(300),  batch_start_time VARCHAR2(20), batch_end_time  VARCHAR2(20), batch_exe_time   VARCHAR2(5), end_flag    VARCHAR2(1), apl_code         VARCHAR2(10), comments         VARCHAR2(100), file_name        VARCHAR2(255),  CONSTRAINT PK_IEAI_BATCH_TRANS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_FIVE_MIN_TRANS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_FIVE_MIN_TRANS (IID NUMBER(19) NOT NULL,TRANS_DATE VARCHAR2(8), TRANS_NAME  VARCHAR2(300),MIN_POINT VARCHAR2(10),COUNT_AMOUNT VARCHAR2(18), APL_CODE VARCHAR2(50), file_name    VARCHAR2(255),  CONSTRAINT PK_IEAI_FIVE_MIN_TRANS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_MULT_TRANS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_MULT_TRANS (iid NUMBER(19) NOT NULL,trans_date  VARCHAR2(8),data_num    NUMBER(4), count_item  VARCHAR2(1000),count_value VARCHAR2(1000), apl_code    VARCHAR2(50), file_name   VARCHAR2(255), CONSTRAINT PK_IEAI_MULT_TRANS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_HOURS_TRANS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_HOURS_TRANS (iid NUMBER(19) NOT NULL,trans_date  VARCHAR2(8),count_hours_time NUMBER(2),count_item  VARCHAR2(300),count_amount  VARCHAR2(18), apl_code  VARCHAR2(50), file_name   VARCHAR2(255), CONSTRAINT PK_IEAI_HOURS_TRANS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TIMES_TRANS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_TIMES_TRANS (iid NUMBER(19) NOT NULL,trans_date   VARCHAR2(8),count_item   VARCHAR2(300),count_amount VARCHAR2(18),apl_code     VARCHAR2(50), file_name    VARCHAR2(255) , CONSTRAINT PK_IEAI_TIMES_TRANS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_AUDIT' AND COLUMN_NAME='EXECUSER';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_AUDIT ADD  EXECUSER VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/


-- 8.15.0 version timing task patch is as follows

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_LOSTTHENHELP' AND COLUMN_NAME='TASKINFOID';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_LOSTTHENHELP ADD  TASKINFOID NUMBER(19)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_LOSTTHENHELP' AND COLUMN_NAME='RESTARTTIME';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_LOSTTHENHELP ADD  RESTARTTIME TIMESTAMP'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_LOSTTHENHELP' AND COLUMN_NAME='DEALTIME';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_LOSTTHENHELP ADD  DEALTIME TIMESTAMP'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INFO' AND COLUMN_NAME='EFFECTUSER';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INFO ADD  EFFECTUSER VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_IMMED_INFO' AND COLUMN_NAME='EFFECTUSER';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_IMMED_INFO ADD  EFFECTUSER VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_APPOINTMENT_INFO' AND COLUMN_NAME='EFFECTUSER';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_APPOINTMENT_INFO ADD  EFFECTUSER VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME='EFFECTUSER';
	IF	LI_EXISTS = 0 THEN 
		LS_SQL := 'ALTER TABLE IEAI_TIMETASK_INSTANCE ADD  EFFECTUSER VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL; 
	END IF;
	
END;
/
