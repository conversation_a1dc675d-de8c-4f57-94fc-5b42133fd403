package com.ideal.service.sus.templtmagmt.helper;

import java.math.BigDecimal;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.sus.templtmagmt.basic.OLNParam;
import com.ideal.service.sus.templtmagmt.basic.OLNReayBasic;
import com.ideal.service.sus.templtmagmt.basic.OLNResGrop;
import com.ideal.service.sus.templtmagmt.basic.OLNStep;

/**
 * <ul>
 * <li>Title: QueryHelper.java</li>
 * <li>Description:查询助手</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2014-8-27
 */
public class QueryHelper
{

    /**
     * <li>Description:查询出全部数据，这也是action调用的方法</li> 
     * <AUTHOR>
     * 2014-8-27 
     * @param iidLists
     * @param conn
     * @return
     * @throws RepositoryException
     * return List<OLNReayBasic>
     */
    public List<OLNReayBasic> gainBasics ( List<String> iidLists, Connection conn ) throws RepositoryException   
    { 
        LinkedHashMap<String, OLNReayBasic> basicMap = null;
        LinkedHashMap<String, List<OLNStep>> stepsMap = null;
        LinkedHashMap<String, List<OLNParam>> paramsMap = null;
        LinkedHashMap<String, List<OLNResGrop>> resGropsMap = null;
        basicMap = queryBasics(iidLists, conn);
        stepsMap = querySteps(iidLists, conn);
        paramsMap = queryParams(iidLists, conn);
        resGropsMap =querySUSResGrop(iidLists, conn);

        List<OLNReayBasic> basicsList = new ArrayList<OLNReayBasic>();
        for (Iterator<String> iterator = basicMap.keySet().iterator(); iterator.hasNext();)
        {
            String iid = (String) iterator.next();
            OLNReayBasic tmpBasic = (OLNReayBasic) basicMap.get(iid);
            if (null != stepsMap && null != stepsMap.get(iid))
                tmpBasic.getOLNStepList().addAll(stepsMap.get(iid));
            if (null != paramsMap && null != paramsMap.get(iid))
                tmpBasic.getOLNParamList().addAll(paramsMap.get(iid));
            if (null != resGropsMap && null != resGropsMap.get(iid))
                tmpBasic.getOLNResGropList().addAll(resGropsMap.get(iid));
            basicsList.add(tmpBasic);

        }
        return basicsList;
    }

    /**
     * <li>Description:查询 上线实例的基础信息</li>
     * 
     * <AUTHOR> 2014-8-27
     * @param iidLists
     * @param conn
     * @return return Map<String,List<OLNReayBasic>>
     * @throws RepositoryException  
     */
    private LinkedHashMap<String, OLNReayBasic> queryBasics ( List<String> iidLists, Connection conn ) throws RepositoryException
    {  
        LinkedHashMap<String, OLNReayBasic> rsMap= new LinkedHashMap<String, OLNReayBasic>();
        List rsList = new ArrayList(); // 查询结果集
        OLNReayBasic tmpBasic =null;
        String sql =null ; 
        
        
        StringBuffer buf = new StringBuffer("SELECT IID,IMAIN_PROJECT_NAME,IMAIN_FLOW_NAME,ICREATE_TIME,IIMPORT_TIME,IONLINE_INS_NAME,ICREATE_USER,IIMPORT_USER ,ITYPE,IVERSION FROM IEAI_SUS_ONLINE_READY_BASEINFO  BAS WHERE");
        buf.append(gainSubStmt(iidLists));
        sql=buf.toString();
        rsList = SUSExportExcel.getInstance().excuteQuerySql(sql,
            new OLNReayBasic().getIndex(), null, conn);
        
        
        for (int j = 0; j < rsList.size(); j++)         //遍历结果集
        {   // 有n条记录
            tmpBasic = new OLNReayBasic();
            Object object[] = (Object[]) rsList.get(j); // 一条记录是以 对象数组的形式存在的。
            for (int k = 0; k < object.length; k++)
            { 
                //SELECT IID,IMAIN_PROJECT_NAME,IMAIN_FLOW_NAME,ICREATE_TIME,IIMPORT_TIME,IONLINE_INS_NAME,ICREATE_USER,IIMPORT_USER ,ITYPE FROM IEAI_SUS_ONLINE_READY_BASEINFO WHERE IID =?";             
                if (k == 0)
                {
                    BigDecimal iid = (BigDecimal) object[k];
                    tmpBasic.setIid(iid.intValue());
                } else if (k == 1)
                {
                    tmpBasic.setImain_project_name((String) object[k]);
                } else if (k == 2)
                {
                    tmpBasic.setImain_flow_name((String) object[k]);
                } else if (k == 3)
                {
                    tmpBasic.setIcreate_time((String) object[k]);
                } else if (k == 4)
                {
                    tmpBasic.setIimport_time((String) object[k]);
                } else if (k == 5)
                {
                    tmpBasic.setIonline_ins_name((String) object[k]);
                } else if (k == 6)
                {
                    tmpBasic.setIcreate_user((String) object[k]);
                }else if (k == 7)
                {
                    tmpBasic.setIimport_user((String) object[k]);
                }else if (k == 8)
                {
                    tmpBasic.setItype((String) object[k]);
                }else if (k == 9)
                {
                    tmpBasic.setVersion((String) object[k]);
                }
            }
            rsMap.put(Integer.toString(tmpBasic.getIid()), tmpBasic)  ;
        }
        return rsMap;
    }

    /**
     * <li>Description: 查询上线实例的步骤_OLNStep</li>
     * 
     * <AUTHOR> 2014-8-27
     * @param iidLists
     * @param conn
     * @return return Map<String,List<OLNStep>>
     * @throws RepositoryException 
     */
    private LinkedHashMap<String, List<OLNStep>> querySteps ( List<String> iidLists, Connection conn ) throws RepositoryException
    { 
        LinkedHashMap<String, List<OLNStep>> stepsMap = new LinkedHashMap<String, List<OLNStep>> ();
        List<OLNStep> stepList =null;
        List rsList = new ArrayList(); // 查询结果集
        String sql =null ; 
        OLNStep step =null;
        
        StringBuffer buf = new StringBuffer();
        buf.append("SELECT SID, BAS.IID,IPROJECT_NAME,IFLOW_NAME,IACT_NAME,IPROMPT_MSG ,IISPROMRT,IGNORE,IMPOR ");
        buf.append("  FROM IEAI_SUS_ONLINE_READY_BASEINFO BAS ,IEAI_SUS_ONLINE_STEP STP");
        buf.append("  WHERE BAS.IID = STP.IID ");
        buf.append("  AND ");
        buf.append(gainSubStmt(iidLists));
        sql=buf.toString();
        rsList = SUSExportExcel.getInstance().excuteQuerySql(sql,
            new OLNStep().getIndex(), null, conn);
        
        
        for (int j = 0; j < rsList.size(); j++)
        {                                                    // 只有一条记录
            step = new OLNStep();
            Object object[] = (Object[]) rsList.get(j);     // 一条记录是以 对象数组的形式存在的。
            for (int k = 0; k < object.length; k++)
            {
                if (k == 0)
                {
                    BigDecimal val = (BigDecimal) object[k];
                    step.setSid(val.intValue());
                } else if (k == 1)
                {
                    BigDecimal val = (BigDecimal) object[k];
                    step.setIid(val.intValue());
                } else if (k == 2)
                {
                    String val =null!=object[k]?(String)object[k]:"";
                    step.setIproject_name(val);
                } else if (k == 3)
                {
                    String val =null!=object[k]?(String)object[k]:"";
                    step.setIflow_name(val);
                } else if (k == 4)
                {
                    String val =null!=object[k]?(String)object[k]:"";
                    step.setIact_name(val);
                } else if (k == 5)
                {
                    String val =null!=object[k]?(String)object[k]:null;
                    step.setIprompt_msg(val);
                } else if (k == 6)
                {
                      BigDecimal val = (BigDecimal) object[k];
                      step.setIispromrt(val.intValue());
                } else if (k == 7)
                {
                      BigDecimal val = (BigDecimal) object[k];
                      step.setIgnore(val.intValue());
                } else if (k == 8)
                {
                  BigDecimal val = (BigDecimal) object[k];
                    step.setImpor(val.intValue());
              }
            }//end for 一个step对象装好了
            
            
            //判断map中是否已经含有这个iid对应的list,如果有直接取出，继续使用，如果没有就new一个新的List
            if(null ==stepsMap.get(Integer.toString(step.getIid())))
                stepList = new ArrayList<OLNStep>();
            else
                stepList =stepsMap.get(Integer.toString(step.getIid()));
            stepList.add(step);
            stepsMap.put(Integer.toString(step.getIid()), stepList);
        }
        
        return stepsMap;
    }

    /**
     * <li>Description:查询上线实例的参数 OLNParam</li>
     * 
     * <AUTHOR> 2014-8-27
     * @param iidLists
     * @param conn
     * @return return Map<String,List<OLNParam>>
     * @throws RepositoryException 
     */
    private LinkedHashMap<String, List<OLNParam>> queryParams ( List<String> iidLists,
            Connection conn ) throws RepositoryException
    { 
        LinkedHashMap<String, List<OLNParam>> paramsMap = new LinkedHashMap<String, List<OLNParam>> ();
        List<OLNParam> parmList =null;
        List rsList = new ArrayList(); // 查询结果集
        String sql =null ; 
        OLNParam param =null; 
        
        StringBuffer buf = new StringBuffer();
        buf.append("SELECT PID,PM.IID,IPARAM_NAME,IPARAM_TYPE,IPARAM_VALUE,IPARAM_DESC ");
        buf.append("   FROM IEAI_SUS_ONLINE_READY_BASEINFO BAS ,IEAI_SUS_ONLINE_PARAM PM");
        buf.append("   WHERE BAS.IID = PM.IID ");
        buf.append("  AND ");
        buf.append(gainSubStmt(iidLists));
        sql=buf.toString();
        rsList = SUSExportExcel.getInstance().excuteQuerySql(sql,
            new OLNParam().getIndex(), null, conn);
        
        
        // 对param对象进行封装
        for (int j = 0; j < rsList.size(); j++)
        { // 只有一条记录
            param = new OLNParam();
            Object object[] = (Object[]) rsList.get(j); // 一条记录是以 对象数组的形式存在的。
            // SELECT PID,IID,IPARAM_NAME,IPARAM_TYPE,IPARAM_VALUE,IPARAM_DESC 
            for (int k = 0; k < object.length; k++)
            {
                if (k == 0)
                {
                    if (null!=object[k] )
                    {
                        param.setPid(object[k].toString()); 
                    }
                } else if (k == 1)
                {
                    if (null!=object[k] )
                    {
                        String temp = object[k].toString();
                        try
                        {
                            int iid =Integer.parseInt(temp);
                            param.setIid(iid);
                        } catch (NumberFormatException e)
                        {
                            // TODO: handle exception
                        }

                    }
                } else if (k == 2)
                {
                    if (null!=object[k] )
                    {
                        param.setIparam_name(object[k].toString());
                    }
                } else if (k == 3)
                {
                    if (null!=object[k] )
                    {    
                        param.setIparam_type(object[k].toString());
                    }
                } else if (k == 4)
                {
                    if (null!=object[k] )
                    {
                        param.setIparam_value( object[k].toString());
                    }
                } else if (k == 5)
                {
                    if (null!=object[k] )
                    {
                        param.setIparam_desc((String) object[k]);
                    }
                }
            }
            
            //判断map中是否已经含有这个iid对应的list,如果有直接取出，继续使用，如果没有就new一个新的List
            if(null ==paramsMap.get(Integer.toString(param.getIid())))
                parmList = new ArrayList<OLNParam>();
            else
                parmList =paramsMap.get(Integer.toString(param.getIid()));
            parmList.add(param);
            paramsMap.put(Integer.toString(param.getIid()), parmList);
        }
        
        return paramsMap;
    }
    
    
    
    /**
     * <li>Description:查询上线实例的参数 OLNResGrop</li>
     * 
     * <AUTHOR> 2014-8-27
     * @param iidLists
     * @param conn
     * @return return Map<String,List<OLNResGrop>>
     * @throws RepositoryException 
     */
    private LinkedHashMap<String, List<OLNResGrop>> querySUSResGrop ( List<String> iidLists, Connection conn ) throws RepositoryException{
	    LinkedHashMap<String, List<OLNResGrop>> resGropsMap = new LinkedHashMap<String, List<OLNResGrop>> ();
	    List<OLNResGrop> resGropList =null;
	    List rsList = new ArrayList(); // 查询结果集
	    String sql =null ; 
	    OLNResGrop resGrop =null; 
	    
        StringBuffer buf = new StringBuffer();
        buf.append("SELECT RG.IRESNAME,RG.IRESID,RG.IPRONAME,RG.IFLOWNAME,RG.IACTNAME,RG.ITYPE,RG.IID ");
        buf.append("   from IEAI_SUS_ONLINE_READY_BASEINFO BAS ,IEAI_SUS_ONLINE_RESGROUP  RG ");
        buf.append("   WHERE BAS.IID = RG.IID ");
        buf.append("  AND ");
        buf.append(gainSubStmt(iidLists));
        sql=buf.toString();
        rsList = SUSExportExcel.getInstance().excuteQuerySql(sql,7, null, conn);
	    
        
        // 对resgr对象进行封装
        for (int j = 0; j < rsList.size(); j++)
        { // 只有一条记录
        	resGrop = new OLNResGrop();
            Object object[] = (Object[]) rsList.get(j); // 一条记录是以 对象数组的形式存在的。
            // RG.IRESNAME,RG.RID,RG.IPRONAME,RG.IFLOWNAME,RG.IACTNAME,RG.ITYPE,RG.IID 
            for (int k = 0; k < object.length; k++)
            {
                if (k == 0)
                {
                	resGrop.setIresname((String) object[k]);
                } else if (k == 1)
                {
                	if (object[k] instanceof BigDecimal)
                	{
                    	BigDecimal rid = (BigDecimal) object[k];
                        resGrop.setRid(rid.toString());
                        resGrop.setIresid(rid.toString());
                	}else if (object[k] instanceof Integer)
                	{
                		Integer rid = (Integer) object[k];
	                    resGrop.setRid(rid.toString());
	                    resGrop.setIresid(rid.toString());
                	}
                } else if (k == 2)
                {
                	resGrop.setIproname((String) object[k]);
                	resGrop.setImain_project_name((String) object[k]);
                } else if (k == 3)
                {
                	resGrop.setIflowname((String) object[k]);
                	resGrop.setImain_flow_name((String) object[k]);
                } else if (k == 4)
                {
                	resGrop.setIactname((String) object[k]);
                } else if (k == 5)
                {
                	if (object[k] instanceof BigDecimal)
                	{
                    	BigDecimal temp = (BigDecimal) object[k];
                    	resGrop.setItype(temp.toString());
					}else if((object[k] instanceof Integer))
					{
	                	Integer temp =	(Integer) object[k];
	                	resGrop.setItype(temp.toString());
					}	
                }else if (k == 6)
                {
                	if (object[k] instanceof BigDecimal)
                	{
                        BigDecimal iid = (BigDecimal) object[k];
                        resGrop.setIid(iid.intValue());
                	}else if (object[k] instanceof Integer)
                	{
                		Integer iid = (Integer) object[k];
                        resGrop.setIid(iid.intValue());
                	}

                }
            }
            
            //判断map中是否已经含有这个iid对应的list,如果有直接取出，继续使用，如果没有就new一个新的List
            if(null ==resGropsMap.get(Integer.toString(resGrop.getIid())))
            	resGropList = new ArrayList<OLNResGrop>(); 
            else
            	resGropList =resGropsMap.get(Integer.toString(resGrop.getIid()));
            resGropList.add(resGrop);
            resGropsMap.put(Integer.toString(resGrop.getIid()), resGropList);
        }
	    return resGropsMap;
	}
    /**
     * <li>Description:获得where in 子sql </li> 
     * <AUTHOR>
     * 2014-8-27 
     * @param iidLists
     * @return
     * return String
     */
    public static  String gainSubStmt(List<String> iidLists){
        StringBuffer buf =new StringBuffer(" ");
        String iid  =null; 
        
        
        if( null !=iidLists && iidLists.size()>0){
            buf.append(" BAS.IID IN ( ");
            for (int i =0;i<iidLists.size();i++)
            {
                iid = iidLists.get(i);
                buf.append(iid);
                if((1 !=iidLists.size()) && (i != iidLists.size()-1))
                    buf.append(",");  
                 
            }
            buf.append(")");
        }
        return buf.toString();
    }
    
    public static void main ( String[] args )
    {
        ArrayList<String>  testList = new ArrayList<String>();
        for (int i = 0; i < 10; i++)
        {
            testList.add(Integer.toString(i));
        }
       System.out.println( gainSubStmt(testList));
    }
}
