var pagebar = "";
Ext.onReady(function() {
	var itemsPerPage = 50;
	
	//工程名称下拉框model
	Ext.define ('prjModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'prjId',
		        type : 'string'
		    },{
		    	name : 'prjName',
		    	type : 'string'
		    }
	    ]
	});
	
	// 工程名称下拉框Store
	var prjName_store = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'prjModel',
	    proxy :
	    {
	        type : 'ajax',
	        timeout :  130000,
	        url : 'queryMonitorPrjCombox.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	//工作流名称下拉框model
	Ext.define ('flowModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'flowName',
		        type : 'string'
		    }
	    ]
	});
	
	// 工作流名称下拉框Store
	var flowName_store = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'flowModel',
	    proxy :
	    {
	        type : 'ajax',
	        timeout :  130000,
	        url : 'queryMonitorFlowNameCombox.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	// 所属系统下拉框数据源Model
	Ext.define ('systemModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
		    {
		        name : 'prjId',
		        type : 'string'
		    },{
		    	name : 'sysName',
		    	type : 'string'
		    }
	    ]
	});
	// 所属系统下拉框数据源
	var cstore_isystem = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'systemModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'queryMonitorSystemCombox.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	// 所属系统下拉框
	var combobox_isystem = Ext.create ('Ext.form.field.ComboBox',
	{
	    width : 1188,
	    labelWidth : 100,
	    padding : '0 10 5 0',
	    fieldLabel : '所属系统',
	    displayField : 'sysName',
	    valueField : 'sysName',
	    emptyText : '--请输选择所属系统名称--',
	    editable : true,
	    store : cstore_isystem,
	    queryMode : 'local',
		listeners : {
			select : function() {
				Ext.getCmp("combobox_prjName").clearValue();
				Ext.getCmp("combobox_flowName").clearValue();
				Ext.getCmp("combobox_actName").setValue('');
				// 参数信息列表grid重新加载
				var prjId = this.value;
				prjName_store.load({
					params:{
						prjId : prjId
					}
				});
			}
		}
	});
	
	// 工程名称下拉框
	var combobox_prjName = Ext.create ('Ext.form.field.ComboBox',
    {
        width : 1188,
        labelWidth : 100,
        padding : '0 10 5 0',
        fieldLabel : '工程名称',
        emptyText : '--请输选择工程名称--',
        displayField : 'prjName',
        valueField : 'prjId',
        id : 'combobox_prjName',
        editable : true,
        store : prjName_store,
        queryMode : 'local',
		listeners : {
			select : function() {
				Ext.getCmp("combobox_flowName").clearValue();
				Ext.getCmp("combobox_actName").setValue('');
				var prjId = this.value;
				// 参数信息列表grid重新加载
				flowName_store.load({
					params : {
						prjId : prjId
					}
				});
			}
		}
     });
	
	// 工作流名称下拉框
	var combobox_flowName = Ext.create ('Ext.form.field.ComboBox',
		    {
		        width : 1188,
		        labelWidth : 100,
		        padding : '0 10 5 0',
		        fieldLabel : '工作流名称',
		        emptyText : '--请输选择工作流名称--',
		        displayField : 'flowName',
		        valueField : 'flowName',
		        id : 'combobox_flowName',
		        editable : true,
		        store : flowName_store,
		        queryMode : 'local',
		        listeners : {
					select : function() {
						Ext.getCmp("combobox_actName").setValue('');
					}
				}
		     });
	
	
	var text_actName = Ext.create ('Ext.form.field.Text',
	{
		    width : 1188,
		    labelWidth : 100,
		    padding : '0 10 5 0',
		    fieldLabel : '活动名称',
		    id : 'combobox_actName'
	});
	
	//查询条件
	var monitorActQueryForm = Ext.create('Ext.form.Panel', {
		id : 'queryform',
		border : false,
		frame : true,
		fieldDefaults : {
			labelWidth : 100,
			labelAlign : "left"
		},
		items : [{
			xtype : "container",
            layout : "hbox",
            margin : '3 0 0 0',
            items : [combobox_isystem]
		}, {
			xtype : "container",
            layout : "hbox",
            margin : '3 0 0 0',
            items : [combobox_prjName]
		},{
			xtype : "container",
            layout : "hbox",
            margin : '3 0 0 0',
            items : [combobox_flowName]
		},{
			xtype : "container",
            layout : "hbox",
            margin : '3 0 0 0',
            items : [text_actName]
		}],
		 buttons : [
		            {
		                text : '查询',
		                handler : search
		            },{
		            	text : '重置',
		                handler : resetFun
		            },
		            {
		            	text : '返回',
		                handler : goBack
		            }
		    ]
	});
    
	//查询条件panel
	var formPanel = Ext.create('Ext.panel.Panel', {
		title : '查看监控活动',
	    titleCollapse : true,
	    collapsible : true,
	    witdh : '100%',
	    region : 'north',
	    layout : 'auto',
	    border : false,
		items : [ monitorActQueryForm ]
	});
	
	Ext.define('monitorActGridModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'actiid',
			type : 'string'
		}, {
			name : 'sysName',
			type : 'string'
		},{
			name : 'prjName',
			type : 'string'
		},{
			name : 'flowName',
			type : 'string'
		},{
			name : 'actName',
			type : 'string'
		} ]
	});
	
	//获取数据
	var monitorActQueryStore = Ext.create('Ext.data.Store', {	
		autoLoad : true,
		autoDestroy : true,
		model : 'monitorActGridModel',
	 	pageSize:itemsPerPage,	
		proxy: {
		    type: 'ajax',
		    timeout :  1300000,
		    url: 'showMonitorAct.do',
		    reader: {
		        type: 'json',
		        root: 'dataList',
		        totalProperty: 'total'	
		    }
	    }
	});
	
	
	monitorActQueryStore.on ('beforeload', function (s){
		//查询参数
		var sysName = combobox_isystem;
		var prjName = combobox_prjName;
		var flowName = combobox_flowName;
		var actName = text_actName.getValue();
		var sysNameV=trim(sysName.getRawValue()==null?'':sysName.getRawValue());
		var flowNameV = trim(flowName.getValue()==null?'':flowName.getValue());
		var actNameV=trim(actName==null?'':actName);
		var prjNameV=trim(prjName.getRawValue()==null?'':prjName.getRawValue());
		var params = s.getProxy ().extraParams;
		Ext.apply (params,{
			sysName:sysNameV,
			actName:actNameV,
			prjName:prjNameV,
			flowName:flowNameV,
			taskId : taskId
		});
	});
	
	
	
	// 分页的combobox下拉选择显示条数
	var combo = Ext.create ('Ext.form.ComboBox',
	{
	    name : 'pagesize',
	    hiddenName : 'pagesize',
	    store : new Ext.data.ArrayStore (
	    {
	        fields : [
	                'text', 'value'
	        ],
	        data : [
	                [
	                        '50', 50
	                ], [
	                        '100', 100
	                ], [
	                        '150', 150
	                ]
	        ]
	    }),
	    valueField : 'value',
	    displayField : 'text',
	    emptyText : 50,
	    width : 60
	});
	pagebar = Ext.create('Ext.PagingToolbar', {
		pageSize:itemsPerPage, 
		dock: 'bottom',
		id : 'pagingbar',
		border : false,
		baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		store:monitorActQueryStore,  
		displayInfo: true, 
		items : [
		            '-', combo		    ],
		displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
		emptyMsg: "没有记录"
	});
	
	// 添加下拉显示条数菜单选中事件
	combo.on ("select", function (comboBox)
	{
		var pagingToolbar = Ext.getCmp ('pagingbar');
		pagingToolbar.pageSize = parseInt (comboBox.getValue ());
		itemsPerPage = parseInt (comboBox.getValue ());// 更改全局变量itemsPerPage
		monitorActQueryStore.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
		search ();
	});
	
	combo.on ("blur", function (comboBox)
	{
		var pagingToolbar = Ext.getCmp ('pagingbar');
		pagingToolbar.pageSize = parseInt (comboBox.getValue ());
		itemsPerPage = parseInt (comboBox.getValue ());// 更改全局变量itemsPerPage
		monitorActQueryStore.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
		search ();
	});
	
	
	var monitorActGrid = Ext.create('Ext.grid.Panel', {
		columnLines : false,
		store : monitorActQueryStore,
		columns : [ {
			xtype : 'rownumberer',
			text : '序号',
			align : 'center',
			width : 60
		},{
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'actiid',
			text : 'iid'
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'sysName',
			align : 'center',
			flex : 1,
			text : '所属系统'
		},{
			xtype : 'gridcolumn',
			dataIndex : 'prjName',
			align : 'center',
			flex : 1,
			text : '工程名称'
		},{
			xtype : 'gridcolumn',
			dataIndex : 'flowName',
			align : 'center',
			flex : 1,
			text : '工作流名称'
		},{
			xtype : 'gridcolumn',
			dataIndex : 'actName',
			align : 'center',
			flex : 1,
			text : '活动名称'
		}],
		bbar: pagebar,
 	    autoScroll:true,
 	    selType: 'cellmodel',
 	    viewConfig :
	       {
	           selType : 'cellmodel',
	           stripeRows : true,// 在表格中显示斑马线
	           enableTextSelection : true
	       }
	});
	
	
	var monitorActGridPanel = Ext.create('Ext.panel.Panel', {
		region : 'center',
	    border : true,
	    margins:grid_margin,
	    witdh : '100%',
	    height : '60%',
		items : [ monitorActGrid ],
		listeners :
		    {
			    resize : function (a)
			    {
			    	monitorActGrid.setHeight (monitorActGridPanel.getHeight () - 24);
			    }
		    }
	});
	
	contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
	//formPanel.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
	
	
	var monitorActMainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : 'showMonitorActDiv',
		bodyCls: 'service_platform_bodybg',
		width : contentPanel.getWidth(),
        height : contentPanel.getHeight(),
	    layout : 'border',
	    header : false,
	    border : false,
		items : [ formPanel,
		          monitorActGridPanel ]
	});
	
	
	/* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

	contentPanel.on('resize', function() {
		monitorActMainPanel.setHeight(contentPanel.getHeight());
		monitorActMainPanel.setWidth(contentPanel.getWidth());
	});

	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (monitorActMainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	function search(){
		pagebar.moveFirst();
	}
	
	function resetFun(){
		combobox_isystem.setValue('');
		combobox_prjName.setValue('');
		combobox_flowName.setValue('');
		Ext.getCmp('combobox_actName').setValue('');
	}
	
	function goBack(){
		contentPanel.getLoader().load({
			 url : 'monitorTask.do',
			 params:{
				 iid : monitorViewId
			 },
			 scripts : true
		 });
	}
});
