package com.ideal.ieai.server.repository.hd.ic.classOverview;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClassificationOverviewManager
{
    static private final ClassificationOverviewManager _instProcess = new ClassificationOverviewManager();
    private static final Logger                        _log         = Logger.getLogger(ClassificationOverviewManager.class);

    static public ClassificationOverviewManager getInstance ()
    {
        return _instProcess;
    }

    private ClassificationOverviewManager()
    {
    }
    
    /**public List<ClassificationOverviewModel> queryClassOverviewSystypeToList ( long id, long type, String userId, String dataCenter)
    {
    
        String sql = "SELECT DATA_CENTER, OID, POID, OBJ_NAME, OBJ_TYPE, AMOUNT, CL0, CHECK_TIME, CHECK_RESULT FROM TMP_DATASHOW WHERE OPT_ID = ? AND DATA_CENTER=?";
        int total = 0;
        List<ClassificationOverviewModel> list = new ArrayList<ClassificationOverviewModel>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement call = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                } catch (DBException e)
                {
                    e.printStackTrace();
                }
                try
                {
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setLong(2, id);// 分类总览第一页调用初始值为 -1
                    call.setLong(3, type);// 分类总览第一页调用初始值为 0
                    call.setLong(4, 1);// 分页标识
                    call.setLong(5, 30);// 每页记录行数
                    call.registerOutParameter(6, java.sql.Types.NUMERIC); // 数据提取标识
                    call.registerOutParameter(7, java.sql.Types.NUMERIC); // 记录总数
                    call.execute();
    
                    long optid = call.getLong(6);
                    total = call.getInt(7);
    
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    ps.setString(2, dataCenter);
                    rs = ps.executeQuery();
                    String checktime = "";
                    while (rs.next())
                    {
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        bean.setTypeId(rs.getString("OID"));
                        bean.setCpId(rs.getString("POID"));
                        bean.setApplyType(rs.getString("OBJ_NAME"));
                        bean.setCheckAllNum(rs.getString("AMOUNT"));
                        bean.setCheckRightNum(rs.getString("CL0"));
                        bean.setGroupName(rs.getString("DATA_CENTER"));
                        checktime = rs.getString("CHECK_TIME");
                        if (!StringUtil.isEmptyStr(checktime))
                        {
                            bean.setCheckTime(checktime.substring(0, checktime.indexOf(".") - 1));
                        }
                        bean.setCheckResult(rs.getString("CHECK_RESULT"));
                        list.add(bean);
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryClassOverviewSystypeToList:"+e);
                    throw e;
                } finally
                {
                    DBResource.closeCallableStatement(call, "queryClassOverviewSystypeToList", _log);
                    DBResource.closeConn(conn, rs, ps, "queryClassOverviewSystypeToList", _log);
                }
            } catch (Exception ex)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e)
                {
                    _log.error("queryClassOverviewSystypeToList:"+e);
                }
            }
        }
        return list;
    }*/

    public ResultData queryClassOverviewSystype (PageData pd, long id,long type ,String userId ,String dataCenter)
    {

        ResultData ret = new ResultData();
//        String sql = "SELECT DATA_CENTER, OID, POID, OBJ_NAME, OBJ_TYPE, AMOUNT, NORMAL, ABNORMAL, CHECK_TIME, CHECK_RESULT FROM TMP_DATASHOW WHERE OPT_ID = ? AND DATA_CENTER=?";
        String sql = "SELECT DATA_CENTER, IOID, POID, OBJ_NAME, (SELECT CPNAME FROM IEAI_COMPUTER_LIST WHERE IP=A.OBJ_NAME ) AS CPNAME, OBJ_TYPE, AMOUNT, CL0,CL1,CL2,CL3,CL4,CL5,CHECKING, CHECK_TIME, CHECK_RESULT FROM TMP_DATASHOW A WHERE OPT_ID = ? AND DATA_CENTER=?";
        int total = 0;
        List list = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement call = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                } catch (DBException e)
                {
                    e.printStackTrace();
                }
                try
                {
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setLong(2, id);//分类总览第一页调用初始值为 -1
                    call.setLong(3, type);//分类总览第一页调用初始值为 0
                    // call.setLong(4, 1);//分页标识
                    // call.setLong(5, 30);//每页记录行数
                    call.setLong(4, pd.getPageTag());// 分页标识
                    call.setLong(5, pd.getRecordSize());// 每页记录行数
                    call.setString(6, dataCenter);// 中心
                    call.setString(7, "2");// 每页记录行数
                    call.registerOutParameter(8, java.sql.Types.NUMERIC); // 数据提取标识
                    call.registerOutParameter(9, java.sql.Types.NUMERIC); // 记录总数
                    call.execute();

                    long optid = call.getLong(8);
                    total = call.getInt(9);

                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    ps.setString(2, dataCenter);
                    rs = ps.executeQuery();
                    String checktime = "";
                    while (rs.next())
                    {
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        bean.setTypeId(rs.getString("IOID"));
                        bean.setCpId(rs.getString("POID"));
                        bean.setApplyType(rs.getString("OBJ_NAME"));
                        bean.setCpName(rs.getString("CPNAME"));
                        bean.setCheckAllNum(rs.getString("AMOUNT"));
//                        bean.setCheckRightNum(rs.getString("NORMAL"));
                        bean.setCheckRightNum(rs.getString("CL0"));
//                        bean.setCheckErrorNum(rs.getString("ABNORMAL"));
                        bean.setGroupName(rs.getString("DATA_CENTER"));
                        checktime = rs.getString("CHECK_TIME");
                        if(!StringUtil.isEmptyStr(checktime))
                        {
                            bean.setCheckTime(checktime.substring(0, checktime.indexOf(".")-1));
                        }
                        bean.setCheckResult(rs.getString("CHECK_RESULT"));
                        
                        bean.setCl1(rs.getInt("CL1"));
                        bean.setCl2(rs.getInt("CL2"));
                        bean.setCl3(rs.getInt("CL3"));
                        bean.setCl4(rs.getInt("CL4"));
                        bean.setCl5(rs.getInt("CL5"));
                        bean.setChecking(rs.getInt("CHECKING"));
                        
                        list.add(bean);
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryClassOverviewSystype:"+e);
                    throw new Exception(e);
                } finally
                {
                    DBResource.closeCallableStatement(call, "queryClassOverviewSystype", _log);
                    DBResource.closeConn(conn, rs, ps, "queryClassOverviewSystype", _log);
                }
            } catch (Exception ex)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e)
                {
                    // TODO 正常是要抛出异常 到上一层  此处有问题
                    e.printStackTrace();
                    _log.error("queryClassOverviewSystype:"+e);
                }
            }
        }
        ret.setRets(list);
        ret.setTotal(total);
        return ret;
    
    }
    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * 2016年5月25日 
     * @param map
     * @param start
     * @param limit
     * @param page
     * @param userId
     * @return
     * return List
     */
    public ResultData queryClassOverviewSystype (PageData pd, long id,long type ,String userId )
    {
        ResultData ret = new ResultData();
//        String sql = "SELECT DATA_CENTER, OID, POID, OBJ_NAME, OBJ_TYPE, AMOUNT, NORMAL, ABNORMAL, CHECK_TIME, CHECK_RESULT FROM TMP_DATASHOW WHERE OPT_ID = ?";
        // String sql = "SELECT DATA_CENTER, OID, POID, OBJ_NAME, OBJ_TYPE, AMOUNT,CHECKING , CL0,
        // CL1, CL2, CL3, CL4, CL5 , CHECK_TIME, CHECK_RESULT FROM TMP_DATASHOW WHERE OPT_ID = ?
        // ORDER BY DATA_CENTER ASC ";
        String sql = "SELECT A.DATA_CENTER,  A.IOID,  A.POID,  A.OBJ_NAME,(SELECT CPNAME FROM IEAI_COMPUTER_LIST WHERE IP= A.OBJ_NAME ) AS CPNAMEN,  A.OBJ_TYPE,  A.AMOUNT, A.CHECKING ,   A.CL0,  A.CL1,  A.CL2,  A.CL3,  A.CL4,  A.CL5 ,  A.CHECK_TIME,  A.CHECK_RESULT FROM TMP_DATASHOW A WHERE A.OPT_ID = ? ORDER BY A.DATA_CENTER ASC ";
        int total = 0;
        List list = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement call = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setLong(2, id);// 分类总览第一页调用初始值为 -1
                    call.setLong(3, type);// 分类总览第一页调用初始值为 0
                    // call.setLong(4, 2);// 分页标识
                    // call.setLong(5, 30);// 每页记录行数
                    call.setLong(4, pd.getPageTag());// 分页标识
                    call.setLong(5, pd.getRecordSize());// 每页记录行数
                    call.setString(6, "");// 每页记录行数
                    call.setString(7, pd.getStatus());// 每页记录行数
                    call.registerOutParameter(8, java.sql.Types.NUMERIC); // 数据提取标识
                    call.registerOutParameter(9, java.sql.Types.NUMERIC); // 记录总数
                    call.execute();

                    long optid = call.getLong(8);
                    total = call.getInt(9);

                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    rs = ps.executeQuery();
                    String checktime = "";
                    while (rs.next())
                    {
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        bean.setTypeId(rs.getString("IOID"));
                        bean.setCpId(rs.getString("POID"));
                        bean.setCpName(rs.getString("CPNAMEN"));
                        bean.setApplyType(rs.getString("OBJ_NAME"));
                        bean.setCheckAllNum(rs.getString("AMOUNT"));
                        // bean.setCheckRightNum(rs.getString("NORMAL"));
                        bean.setCheckRightNum(rs.getString("CL0")); // 原来的 NORMAL 改为 CL0
                        // bean.setCheckErrorNum(rs.getString("ABNORMAL"));
                        bean.setGroupName(rs.getString("DATA_CENTER"));
                        // bean.setCheckTime(rs.getString("CHECK_TIME"));
                        checktime = rs.getString("CHECK_TIME");

                        bean.setCl1(rs.getInt("CL1"));
                        bean.setCl2(rs.getInt("CL2"));
                        bean.setCl3(rs.getInt("CL3"));
                        bean.setCl4(rs.getInt("CL4"));
                        bean.setCl5(rs.getInt("CL5"));
                        bean.setChecking(rs.getInt("CHECKING"));
                        if (!StringUtil.isEmptyStr(checktime))
                        {
                            if ("1970-1-1".equals(checktime.substring(0, 10)))
                            {
                                bean.setCheckTime("");
                            } else
                            {
                                bean.setCheckTime(checktime.substring(0, 10));
                            }

                        }
                        bean.setCheckResult(rs.getString("CHECK_RESULT"));
                        list.add(bean);
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryClassOverviewSystype:"+e);
                    throw e;
                } finally
                {
                    DBResource.closeCallableStatement(call, "queryClassOverviewSystype", _log);
                    DBResource.closeConn(conn, rs, ps, "queryClassOverviewSystype", _log);
                }
            } catch (Exception ex)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e)
                {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                    _log.error("queryClassOverviewSystype:"+e);
                }
            }
        }
        ret.setRets(list);
        ret.setTotal(total);
        return ret;
    }
    
    
    
    public ResultData queryClassOverviewSystypeNew (long id,String status,String startStr, String limitStr ) throws RepositoryException
    {
        int start = 0;
        int limit = 30;
        if(StringUtils.isNotBlank(startStr)) {
            start = Integer.parseInt(startStr);
        }
        if(StringUtils.isNotBlank(limitStr)) {
            limit = Integer.parseInt(limitStr);
        }
        
        ResultData ret = new ResultData();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT SDID,COMPUTERIP,COMPUTERNAME,CL0,CL1,CL2,CL3,CL4,CL5,ISTATUS  ");
        sql.append(" FROM ( ");
        sql.append(" SELECT VG.*, (VG.CL1 + VG.CL2 + VG.CL3 + VG.CL4 + VG.CL5) AS ISTATUS ");
        sql.append(" FROM ( ");
        sql.append("        SELECT  A.SDID, A.COMPUTERIP, A.COMPUTERNAME, ");
        sql.append("                SUM(CASE  B.CPSTATUS WHEN 0 THEN B.DAT_CNT ELSE 0 END)   AS CL0, ");
        sql.append("                SUM(CASE  B.CPSTATUS WHEN 1 THEN B.DAT_CNT ELSE 0 END) AS CL1, ");
        sql.append("                SUM(CASE  B.CPSTATUS WHEN 2 THEN B.DAT_CNT ELSE 0 END) AS CL2, ");
        sql.append("                SUM(CASE  B.CPSTATUS WHEN 3 THEN B.DAT_CNT ELSE 0 END) AS CL3, ");
        sql.append("                SUM(CASE  B.CPSTATUS WHEN 4 THEN B.DAT_CNT ELSE 0 END) AS CL4, ");
        sql.append("                SUM(CASE  B.CPSTATUS WHEN 5 THEN B.DAT_CNT ELSE 0 END) AS CL5 ");
        sql.append("          FROM    ( ") ;
        sql.append("                SELECT R.SDID AS SDID,L.IP AS COMPUTERIP,L.CPNAME as COMPUTERNAME ") ;
        sql.append("                  FROM    IEAI_SYS_RELATION R, IEAI_COMPUTER_LIST L ");
        sql.append("                 WHERE    L.CPID = R.COMPUTERID  AND R.SYSTEMID =?").append(" ");
        sql.append("                    ) A, ") ;
        sql.append("              ( SELECT R.SDID,D.CPSTATUS,COUNT(*) AS DAT_CNT ") ;
        sql.append("                  FROM    IEAI_SYS_RELATION R,IEAI_COM_CHK C,IEAI_CHKPOINT K, HD_CHECK_RESULT_DATA_LAST D ") ;
        sql.append("                 WHERE    R.SYSTEMID =?").append(" ");
        sql.append("                   AND    C.CPID = R.COMPUTERID ") ;
        sql.append("                   AND    (C.APPLOGO = -1 OR C.APPLOGO = R.SYSTEMID) ") ;
        sql.append("                   AND    C.STARTLOGO = 1 ") ;
        sql.append("                   AND    K.SCID = C.SCID ") ;
        sql.append("                   AND    K.STARTLOGO = 1 ") ;
        sql.append("                   AND    D.CPID = K.CPID") ;
        sql.append("              GROUP BY  R.SDID, D.CPSTATUS ") ;
        sql.append("               ) B ") ;
        sql.append("   WHERE    B.SDID = A.SDID ") ;
        sql.append("   GROUP  BY  A.SDID, A.COMPUTERIP, A.COMPUTERNAME ") ;
        sql.append("    ) VG ") ;
        sql.append("    )VGG WHERE 1=1 ") ;
        if(status!=null && "0".equals(status)) {
            sql.append("  AND ISTATUS=0 ") ;
        }else if(status!=null && "1".equals(status)) {
            sql.append("  AND ISTATUS=1 ") ;
        }
        
        
        
        String order = "  ORDER BY ISTATUS DESC,  COMPUTERIP ASC  " ;
        
        String countSql = "SELECT COUNT(1) AS COU FROM ("+sql.toString()+" ) x ";
        
        
        String querySql = getSql(sql.toString()+order);
        
        Connection conn = null;
        PreparedStatement ps =null;
        ResultSet rs = null;
        
        PreparedStatement psCount =null;
        ResultSet rsCount = null;
        int total = 0;
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
            psCount = conn.prepareStatement(countSql);
            ps = conn.prepareStatement(querySql);

            psCount.setLong(1,id);
            ps.setLong(1,id);
            psCount.setLong(2,id);
            ps.setLong(2,id);
            rsCount = psCount.executeQuery();
            if(rsCount.next()) {
                total = rsCount.getInt("COU");
            }

            if (DBManager.Orcl_Faimily())
            {
                ps.setLong(3, (start + limit));
                ps.setLong(4, start );
            } else if (JudgeDB.IEAI_DB_TYPE==2)
            {
                ps.setLong(3, start);
                ps.setLong(4, (start + limit));
            }else if (JudgeDB.IEAI_DB_TYPE==3){
                ps.setLong(3, start);
                ps.setLong(4, limit);
                
            }
            rs =ps.executeQuery();
            while(rs.next()) {
                ClassificationOverviewModel bean = new ClassificationOverviewModel();
                bean.setCpName(rs.getString("COMPUTERNAME"));
                bean.setApplyType(rs.getString("COMPUTERIP"));
                bean.setIsysId(id+"");
                bean.setCl1(rs.getInt("CL1"));
                bean.setCl2(rs.getInt("CL2"));
                bean.setCl3(rs.getInt("CL3"));
                bean.setCl4(rs.getInt("CL4"));
                bean.setCl5(rs.getInt("CL5"));
                list.add(bean);
            }
            
        } catch (Exception e)
        {
            _log.error("获取符合的设备列表失败", e);
           throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally {
            DBResource.closePSRS(rsCount, psCount, "queryClassOverviewSystypeNew",_log);
            DBResource.closeConn(conn, rs, ps, "queryClassOverviewSystypeNew", _log);
            
        }
        ret.setRets(list);
        ret.setTotal(total);
        return ret;
    }
    
    private String getSql(String sql) {
        String s = "";
        if (DBManager.Orcl_Faimily())
        {
            s = SQLUtil.getQueryPageSQLNew("oracle", sql);
        } else if(JudgeDB.IEAI_DB_TYPE==2)
        {
            s = SQLUtil.getQueryPageSQLNew("db2", sql);
        }else if(JudgeDB.IEAI_DB_TYPE==3){
            s = SQLUtil.getQueryPageSQLNew("mysql", sql);
        }
        return s;
    }
    
    
    /**public List<ClassificationOverviewModel> queryClassOverviewSystypeToList (long id, long type, String userId )
    {
        String sql = "SELECT DATA_CENTER, OID, POID, OBJ_NAME, OBJ_TYPE, AMOUNT, CL0, CHECK_TIME, CHECK_RESULT FROM TMP_DATASHOW WHERE OPT_ID = ? ORDER BY DATA_CENTER DESC";
        int total = 0;
        List<ClassificationOverviewModel> list = new ArrayList<ClassificationOverviewModel>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement call = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                } catch (DBException e)
                {
                    e.printStackTrace();
                }
                try
                {
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setLong(2, id);// 分类总览第一页调用初始值为 -1
                    call.setLong(3, type);// 分类总览第一页调用初始值为 0
                    call.setLong(4, 1);// 分页标识
                    call.setLong(5, 30);// 每页记录行数
                    call.registerOutParameter(6, java.sql.Types.NUMERIC); // 数据提取标识
                    call.registerOutParameter(7, java.sql.Types.NUMERIC); // 记录总数
                    call.execute();
    
                    long optid = call.getLong(6);
                    total = call.getInt(7);
    
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    rs = ps.executeQuery();
                    String checktime = "";
                    while (rs.next())
                    {
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        bean.setTypeId(rs.getString("OID"));
                        bean.setCpId(rs.getString("POID"));
                        bean.setApplyType(rs.getString("OBJ_NAME"));
                        bean.setCheckAllNum(rs.getString("AMOUNT"));
                        bean.setCheckRightNum(rs.getString("CL0"));
                        // bean.setCheckErrorNum(rs.getString("ABNORMAL"));
                        bean.setGroupName(rs.getString("DATA_CENTER"));
                        // bean.setCheckTime(rs.getString("CHECK_TIME"));
                        checktime = rs.getString("CHECK_TIME");
                        if (!StringUtil.isEmptyStr(checktime))
                        {
                            bean.setCheckTime(checktime.substring(0, checktime.indexOf(".")));
                        }
                        bean.setCheckResult(rs.getString("CHECK_RESULT"));
                        list.add(bean);
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryClassOverviewSystypeToList:"+e);
                    throw e;
                } finally
                {
                    DBResource.closeCallableStatement(call, "queryClassOverviewSystypeToList", _log);
                    DBResource.closeConn(conn, rs, ps, "queryClassOverviewSystypeToList", _log);
                }
            } catch (Exception ex)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e)
                {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                    _log.error("queryClassOverviewSystypeToList:"+e);
                }
            }
        }
        return list;
    }*/
    
    /** public List queryOverviewData ( Map map, String start, String limit, String page, String userId )
    {
        String sql = "SELECT DATA_CENTER, OID, POID, OBJ_NAME, OBJ_TYPE, AMOUNT, NORMAL, ABNORMAL FROM TMP_DATASHOW WHERE OPT_ID = ?";
        List list = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement call = null;
                int total = 0;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                } catch (DBException e)
                {
                    e.printStackTrace();
                }
                try
                {
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setLong(2, -1);// 分类总览第一页调用初始值为 -1
                    call.setLong(3, 0);// 分类总览第一页调用初始值为 0
                    call.setLong(4, 1);// 分页标识
                    call.setLong(5, 30);// 每页记录行数
                    call.registerOutParameter(6, java.sql.Types.NUMERIC); // 数据提取标识
                    call.registerOutParameter(7, java.sql.Types.NUMERIC); // 记录总数
                    call.execute();
    
                    long optid = call.getLong(6);
                    total = call.getInt(7);
    
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        bean.setApplyType(rs.getString("OBJ_NAME"));
                        bean.setCheckAllNum(rs.getString("AMOUNT"));
                        bean.setCheckRightNum(rs.getString("NORMAL"));
                        bean.setCheckErrorNum(rs.getString("ABNORMAL"));
                        bean.setGroupName(rs.getString("DATA_CENTER"));
                        list.add(bean);
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryOverviewData:"+e);
                    throw e;
                } finally
                {
                    
                    DBResource.closeCallableStatement(call, "queryOverviewData", _log);
                    DBResource.closeConn(conn, rs, ps, "queryOverviewData", _log);
                }
            } catch (Exception ex)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e)
                {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                    _log.error("queryOverviewData:"+e);
                }
            }
        }
        return list;
    }*/
    
    
    
    /**
     * 页面数据源產生 <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月3日
     * @param start
     * @param limit
     * @param page
     * @return return List
     */
    public List queryClassOverview ( Map map, String start, String limit, String page, String userId )
    {
        List list = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                CallableStatement call = null;
                int total = 0;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                } catch (DBException e)
                {
                    e.printStackTrace();
                }
//                String sql = " SELECT   S.SYSTEMID, S.SYSTYPE, DC.CENTERGROUP FROM IEAI_SYS S, IEAI_DC DC, IEAI_SYS_RELATION R,    IEAI_COMPUTER_LIST CL,  IEAI_SYS_PERMISSION P , IEAI_ROLE R,    IEAI_USERINHERIT U WHERE    U.IROLEID=R.IID AND P.IROLEID=R.IID AND P.ISYSID=S.SYSTEMID AND DC.CENTERNAME=CL.CENTERNAME AND CL.CPID=R.COMPUTERID AND    S.PRJTYPE=5 AND P.IPERMISSION=1 AND S.SYSTEMID<>-5  AND DC.CENTERGROUP IS NOT NULL AND  U.IUSERID=111 GROUP BY S.SYSTEMID,S.SYSTYPE,DC.CENTERGROUP";
                String sql = null;
                try
                {
                    long fromNum = 0;
                    long toNum = 0;
                    fromNum = ((Integer.parseInt(page) - 1) * Integer.parseInt(limit)) + 1;
                    toNum = Integer.parseInt(page) * Integer.parseInt(limit);
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setString(2, "");
                    call.setLong(3, 1);
                    call.setInt(4, 0);
                    call.registerOutParameter(5, java.sql.Types.NUMERIC); // 设定返回值类型
                    // call.executeUpdate();
                    call.execute();
                    
                    long optid = call.getLong(5);
                    
                    sql = "SELECT COUNT(*) FROM  (SELECT DISTINCT  SYSTYPE,CENTERGROUPNAME FROM TMP_DATASHOW WHERE SYSTYPE IS NOT NULL AND ALLNUM<>0 AND  OPT_ID=? )";
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        total = rs.getInt(1);
                        map.put("total", total);
                    }
                    ps = null;
                    rs = null;
                    sql = "SELECT * FROM (SELECT ROW_NUMBER() OVER() AS ROWNUM ,SYSTYPE,CENTERGROUPNAME,ALLNUM,RIGHTNUM,ERRORNUM FROM  (SELECT DISTINCT  SYSTYPE,CENTERGROUPNAME,ALLNUM,RIGHTNUM,ERRORNUM  FROM TMP_DATASHOW WHERE SYSTYPE IS NOT NULL AND ALLNUM<>0 AND  OPT_ID=?  order by CENTERGROUPNAME)) "
                            + "  WHERE ROWNUM  BETWEEN  " + fromNum + "  AND  " + toNum;
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        String systype = rs.getString("SYSTYPE");
                        int type = 0;
                        if (!"".equals(systype) && systype != null)
                        {
                            type = Integer.parseInt(systype);
                        }
                        String showType = querySysType(type);
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        // switch (type)
                        // {
                        // case 0:
                        // showType = "未设置";
                        // break;
                        // case 1:
                        // showType = "重保系统";
                        // break;
                        // case 2:
                        // showType = "一类系统";
                        // break;
                        // case 3:
                        // showType = "二类系统";
                        // break;
                        // case 4:
                        // showType = "三类系统";
                        // break;
                        // }
                        String percent = "0%";
                        int allNum = rs.getString("ALLNUM") == null ? 0 : rs.getInt("ALLNUM");
                        int ERRNum = rs.getString("ERRORNUM") == null ? 0 : rs.getInt("ERRORNUM");
                        if (allNum != 0)
                        {
                            percent = String.valueOf(((double) (allNum - ERRNum) / allNum * 100)
                                + "%");
                            bean.setTypeId(String.valueOf(type));
                            bean.setApplyType(showType);
                            bean.setCheckAllNum(rs.getString("ALLNUM"));
                            bean.setCheckRightNum(String.valueOf((allNum - ERRNum)));
                            bean.setCheckErrorNum(rs.getString("ERRORNUM"));
                            bean.setCheckRightPercent(percent);
                            bean.setGroupName(rs.getString("CENTERGROUPNAME"));
                            list.add(bean);
                        }
                        // if (type != 0)
                        // {
                        //
                        // }
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (SQLException e)
                {
                    conn.rollback();
                    _log.error("queryClassOverview:"+e);
                    throw e;
                } finally
                {
                    DBResource.closeCallableStatement(call, "queryClassOverview", _log);
                    DBResource.closeConn(conn, rs, ps, "queryClassOverview", _log);
                }
            } catch (Exception ex)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e)
                {
                    // TODO 正常是要抛出异常 到上一层  此处有问题
                    e.printStackTrace();
                    _log.error("queryClassOverview:"+e);
                }
            }
        }
        return list;
    }

    /**
     * 获取系统类别值 <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月4日
     * @param typeId
     * @return
     * @throws Exception return String
     */
    public String querySysType ( int typeId ) throws Exception
    {
        String colNames = "";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rset = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                    String sql = "SELECT APPLVL FROM IEAI_SYSLV WHERE APPLVLID=?";
                    ps = conn.prepareStatement(sql);
                    ps.setInt(1, typeId);
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        colNames = rset.getString("APPLVL");
                    }
                    break;
                } catch (Exception e)
                {
                    _log.error("ClassificationOverviewManager.querySysType Error", e);
                    throw new Exception(e);
                } finally
                {
                    try
                    {
                        if (rset != null)
                        {
                            rset.close();
                        }
                        if (ps != null)
                        {
                            ps.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException e)
                    {
                        _log.error(
                            "Close Connection Of ClassificationOverviewManager.querySysType Error",
                            e);
                    }
                }
            } catch (Exception ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return colNames;
    }

    /**
     * 二二级页面，查询业务系统、设备、项 <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月4日
     * @param centerGroupName
     * @param sysType
     * @return
     * @throws Exception return List
     */
    public List queryClassOverviewExcep ( Map map, String centerGroupName, String sysType,
            String start, String limit, String page, String userId ) throws Exception
    {
        List resultList = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                CallableStatement call = null;
                ResultSet rset = null;
                String sql = "";
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                    int total = 0;
                    long fromNum = 0;
                    long toNum = 0;
                    fromNum = ((Integer.parseInt(page) - 1) * Integer.parseInt(limit)) + 1;
                    toNum = Integer.parseInt(page) * Integer.parseInt(limit);
                    call = conn.prepareCall("{call PROC_HD_DATAOVERVIEW(?,?,?,?,?)}");
                    call.setLong(1, Long.valueOf(userId));
                    call.setString(2, centerGroupName);
                    call.setLong(3, Long.valueOf(sysType));
                    call.setInt(4, 1);
                    call.registerOutParameter(5, java.sql.Types.NUMERIC); // 设定返回值类型
                    // call.executeUpdate();
                    call.execute();

                    long optid = call.getLong(5);

                    sql = "SELECT COUNT(*)  FROM (SELECT DISTINCT DL.CPSTATUS,D.SYSTEMID,    D.SYSTEMNAME,   D.CIID, D.CINAME,   D.MEID, D.IP  FROM    TMP_DATASHOW_CHECKRESULT D ,HD_CHECK_RESULT_DATA_LAST DL    WHERE   DL.ISYSID=D.SYSTEMID AND    DL.CIID=D.CIID AND  DL.MEID=D.MEID  AND DL.CPSTATUS NOT IN (0,-1,-5,-6) AND   D.OPT_ID=?   UNION  SELECT   DL.CPSTATUS,    D.SYSTEMID, D.SYSTEMNAME,   D.CIID, D.CINAME,   D.MEID, D.IP FROM   TMP_DATASHOW_CHECKRESULT D ,    HD_CHECK_RESULT_DATA_LAST DL WHERE  DL.ISYSID=-1 AND    DL.CIID=D.CIID AND  DL.MEID=D.MEID  AND  DL.CPSTATUS NOT IN (0,-1,-5,-6) AND  DL.MEID IN (SELECT  AA.CPID FROM    IEAI_COMPUTER_LIST AA,  IEAI_SYS_RELATION BB    WHERE   AA.CPID=BB.COMPUTERID AND   BB.SYSTEMID =D.SYSTEMID) AND    D.OPT_ID=?  )";
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    ps.setLong(2, optid);
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        total = rset.getInt(1);
                        map.put("total", total);
                    }
                    ps = null;
                    rset = null;

                    sql = "SELECT * FROM (SELECT  ROW_NUMBER() OVER() AS ROWNUM , SYSTEMID,   SYSTEMNAME, CIID,   CINAME, MEID,   CPSTATUS,   IP FROM (SELECT DISTINCT DL.CPSTATUS,D.SYSTEMID,    D.SYSTEMNAME,   D.CIID, D.CINAME,   D.MEID, D.IP    FROM    TMP_DATASHOW_CHECKRESULT D ,HD_CHECK_RESULT_DATA_LAST DL    WHERE   DL.ISYSID=D.SYSTEMID AND    DL.CIID=D.CIID AND  DL.MEID=D.MEID  AND DL.CPSTATUS NOT IN (0,-1,-5,-6) AND   D.OPT_ID=?    UNION  SELECT   DL.CPSTATUS,    D.SYSTEMID, D.SYSTEMNAME,   D.CIID, D.CINAME,   D.MEID, D.IP FROM   TMP_DATASHOW_CHECKRESULT D ,    HD_CHECK_RESULT_DATA_LAST DL WHERE  DL.ISYSID=-1 AND    DL.CIID=D.CIID AND  DL.MEID=D.MEID  AND  DL.CPSTATUS NOT IN (0,-1,-5,-6) AND  DL.MEID IN (SELECT  AA.CPID FROM    IEAI_COMPUTER_LIST AA,  IEAI_SYS_RELATION BB    WHERE   AA.CPID=BB.COMPUTERID AND   BB.SYSTEMID =D.SYSTEMID) AND    D.OPT_ID=?   ORDER BY    SYSTEMNAME))"
                            + "  WHERE ROWNUM  BETWEEN  " + fromNum + "  AND  " + toNum;
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, optid);
                    ps.setLong(2, optid);

                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        ClassificationOverviewModel bean = new ClassificationOverviewModel();
                        bean.setIsysId(rset.getString("SYSTEMID"));
                        bean.setIsysName(rset.getString("SYSTEMNAME"));
                        bean.setCiId(rset.getString("CIID"));
                        bean.setCiName(rset.getString("CINAME"));
                        bean.setMeId(rset.getString("MEID"));
                        bean.setIp(rset.getString("IP"));
                        bean.setCiStatus(rset.getString("CPSTATUS"));
                        resultList.add(bean);
                    }
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                        conn.rollback();
                    else
                        conn.commit();
                    break;
                } catch (Exception e)
                {
                    _log.error("ClassificationOverviewManager.queryClassOverviewExcep Error", e);
                    throw new Exception(e);
                } finally
                {
                    DBResource.closeCallableStatement(call, "queryClassOverviewExcep", _log);
                    DBResource.closeConn(conn, rset, ps, "queryClassOverviewExcep", _log);
                }
            } catch (Exception ex)
            {
                _log.error("queryClassOverviewExcep:"+ex);
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resultList;
    }

    /**
     * 三级页面数据源 <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月5日
     * @param sysid
     * @param meid
     * @param ciid
     * @return
     * @throws Exception return List
     */
    public List queryClassOverviewExcepPoint ( String sysid, String meid, String ciid )
            throws Exception
    {
        List resultList = new ArrayList();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rset = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                    String sql = "select a.CPID,a.CPTEXT,FUN_GET_DATE_STRING(a.CPTIME,8,'YYYY-MM-DD HH24:MI:SS') as CPTIME,a.CPSTATUS from HD_CHECK_RESULT_DATA_LAST a where  EXISTS (select  1   from    IEAI_COMPUTER_LIST aa,IEAI_SYS_Relation bb  where   aa.CPID=bb.COMPUTERID   and aa.CPID=a.MEID  and bb.SYSTEMID=?) and a.CPSTATUS NOT IN (0,-1,-5,-6)  and a.MEID=? and a.CIID=?";
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, Long.valueOf(sysid));
                    ps.setLong(2, Long.valueOf(meid));
                    ps.setLong(3, Long.valueOf(ciid));
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        Map map = new HashMap();
                        map.put("cpid", rset.getString("CPID"));
                        map.put("cptext", rset.getString("CPTEXT"));
                        map.put("cptime", rset.getString("CPTIME"));
                        map.put("cpstatus", rset.getString("CPSTATUS"));
                        resultList.add(map);
                    }
                    break;
                } catch (Exception e)
                {
                    _log.error("ClassificationOverviewManager.queryClassOverviewExcepPoint Error",
                        e);
                    throw new Exception(e);
                } finally
                {
                    try
                    {
                        if (rset != null)
                        {
                            rset.close();
                        }
                        if (ps != null)
                        {
                            ps.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException e)
                    {
                        _log.error(
                            "Close Connection Of ClassificationOverviewManager.queryClassOverviewExcepPoint Error",
                            e);
                    }
                }
            } catch (Exception ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resultList;
    }

    /**
     * 获取规则 <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月5日
     * @param cpid
     * @return
     * @throws Exception return String
     */
    public String queryRule ( String cpid ) throws Exception
    {
        String colNames = "";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rset = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                    String sql = "select a.CIPARSRULE from HD_CHECK_RESULT_DATA_LAST a where a.rsdid=?";
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, Long.valueOf(cpid));
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        colNames = rset.getString("CIPARSRULE");
                    }
                    break;
                } catch (Exception e)
                {
                    _log.error("ClassificationOverviewManager.queryRule Error", e);
                    throw new Exception(e);
                } finally
                {
                    try
                    {
                        if (rset != null)
                        {
                            rset.close();
                        }
                        if (ps != null)
                        {
                            ps.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException e)
                    {
                        _log.error(
                            "Close Connection Of ClassificationOverviewManager.queryRule Error", e);
                    }
                }
            } catch (Exception ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return colNames;
    }

    /**
     * 四级页面数据源 <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月5日
     * @param cpid
     * @return
     * @throws Exception return String
     */
    public String queryClassOverviewExcepPointValue ( String cpid ) throws Exception
    {
        String colNames = "";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection conn = null;
                PreparedStatement ps = null;
                ResultSet rset = null;
                try
                {
                    conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);
                    String sql = "select a.CIPARSRULE from HD_CHECK_RESULT_DATA_LAST a where a.RSDID =?";
                    ps = conn.prepareStatement(sql);
                    ps.setLong(1, Long.valueOf(cpid));
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        colNames = rset.getString("CIPARSRULE");
                    }
                    break;
                } catch (Exception e)
                {
                    _log.error(
                        "ClassificationOverviewManager.queryClassOverviewExcepPointValue Error", e);
                    throw new Exception(e);
                } finally
                {
                    try
                    {
                        if (rset != null)
                        {
                            rset.close();
                        }
                        if (ps != null)
                        {
                            ps.close();
                        }
                        if (conn != null)
                        {
                            conn.close();
                        }
                    } catch (SQLException e)
                    {
                        _log.error(
                            "Close Connection Of ClassificationOverviewManager.queryClassOverviewExcepPointValue Error",
                            e);
                    }
                }
            } catch (Exception ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return colNames;
    }
    /**   
     * @Title: queryHcSysNum   
     * @Description: 首页信息采集模块  饼图数据重构。 组织各类型的巡检业务系统数量
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: fengchun_zhang 
     * @date:   2017年11月7日 下午2:22:16   
     */  
    public List<Map<String,Object>> queryHcSysNum ( int type ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map<String,Object>> list = null;
        String sql = "SELECT    F.APPLVL , P.CNUM " + " FROM( SELECT APPLVLID ,APPLVL FROM IEAI_SYSLV )  F "
                + " LEFT JOIN  "
                + " ( SELECT COUNT(PRJ.IID) AS CNUM, INFO.SYSTYPE  FROM IEAI_PROJECT PRJ , IEAI_PROJECT_INFO INFO WHERE PRJ.IID=PRJ.ILATESTID AND PRJ.PROTYPE=7 AND INFO.IID=PRJ.IID GROUP BY INFO.SYSTYPE "
                + "  ) P ON P.SYSTYPE = F.APPLVLID";

        for (int i = 0; i < 10; i++)
        {
            try
            {
                list = new ArrayList<Map<String,Object>>();
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(method, _log, type);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        Map<String,Object> tmpMap = new HashMap<String,Object>();
                        tmpMap.put("applyType", actRS.getString("APPLVL"));
                        String sNum = actRS.getString("CNUM");
                        if (null == sNum || "".equals(sNum))
                        {
                            sNum = "0";
                        }
                        tmpMap.put("checkAllNum", sNum);
                        list.add(tmpMap);
                    }
                } catch (SQLException e)
                {
                    _log.error(method + " is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, method, _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
}
