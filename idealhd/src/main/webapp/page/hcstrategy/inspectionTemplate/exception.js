Ext.onReady(function () {

    Ext.define('IeaiHcActWarningModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iId',//主键ID
                type: 'long'
            },
            {
                name: 'wType',//报警类型
                type: 'long'
            },
            {
                name: 'wCode',//报警编码
                type: 'long'
            },
            {
                name: 'wDate',//报警日期
                type: 'string'
            },
            {
                name: 'lDate',//最近报警日期
                type: 'string'
            },
            {
                name: 'hostName',//主机名称
                type: 'string'
            },
            {
                name: 'ip',//主机IP
                type: 'string'
            },
            {
                name: 'aMessage',//消息
                type: 'string'
            },
            {
                name: 'wMessage',//报警消息
                type: 'string'
            },
            {
                name: 'wCount',//报警数量
                type: 'long'
            },
            {
                name: 'tFlag',//报警标识
                type: 'long'
            },
            {
                name: 'sysName',//系统名称
                type: 'string'
            },
            {
                name: 'cpId',//巡检点ID
                type: 'long'
            },
            {
                name: 'suggestion',//建议
                type: 'string'
            },
            {
                name: 'scId',//配置ID
                type: 'long'
            },
            {
                name: 'chkItemId',//巡检项ID
                type: 'long'
            },
            {
                name: 'eventCategory',//巡检项事件种类
                type: 'string'
            },
            {
                name: 'ihcAgentId',//巡检agentID
                type: 'long'
            },
            {
                name: 'iCenterId',//中心ID
                type: 'long'
            },
        ]
    });

    Ext.define("centerModel", {
        extend: "Ext.data.Model",
        fields: [{
            name: "centerid", type: "long"
        }, {
            name: "centername", type: "string"
        }]
    });


    var centerStore = Ext.create('Ext.data.Store',
        {
            model: 'centerModel',
            autoLoad: true,
            proxy:
                {
                    type: 'ajax',
                    url: 'strategycommon/centerlist.do',
                    reader:
                        {
                            type: 'json',
                            root: 'dataList'
                        }
                }
        });

    /** 查询条件-物理中心下拉 */
    var itemNameField = Ext.create('Ext.form.field.ComboBox', {

        labelWidth: 70,
        labelAlign: 'right',
        fieldLabel: '物理中心',
        width: '20%',
        queryMode: 'local',
        emptyText: '全部',
        store: centerStore,
        displayField: 'centername',
        valueField: 'centerid',
        editable: true
    });

    Ext.define('systemModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'systemInfoName',
                    type: 'string'
                },
                {
                    name: 'systemInfoId',
                    type: 'long'
                }
            ]
        });

    var systemStore = Ext.create('Ext.data.Store',
        {
            autoLoad: true,
            autoDestroy: true,
            model: 'systemModel',
            proxy:
                {
                    type: 'ajax',
                    url: 'strategycommon/systemInfolist.do',
                    reader:
                        {
                            type: 'json',
                            root: 'dataList'
                        }
                }
        });

    /** 查询条件-业务系统下拉 */
    var systemField = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'cpstatus',
        labelWidth: 70,
        queryMode: 'local',
        fieldLabel: ' 业务系统',
        displayField: 'systemInfoName',
        valueField: 'systemInfoId',
        editable: true,
        emptyText: '全部',
        store: systemStore,
        width: '20%',
        labelAlign: 'right'
    });

    //异常类型
    var errTypeStore = Ext.create('Ext.data.Store', {
        fields: ['dcode', 'dname'],
        autoLoad: true,
        autoDestroy: true,
        proxy: {
            type: 'ajax',
            url: 'queryWarnigTypeList.do',
            actionMethods: 'post',
            reader: {
                type: 'json',
                root: 'warningTypeList'
            }
        }
    });

    var eventCategoryField = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '巡检项分类:',
        name: 'errType',
        labelWidth: 80,
        padding: 5,
        store: errTypeStore,
        queryMode: 'local',
        width: 190,
        value: '0',
        editable: false,
        displayField: 'dname',
        valueField: 'dcode'
    });

    //异常等级
    var errLevelStore = Ext.create('Ext.data.Store', {
        fields: ['dcode', 'dname'],
        autoLoad: true,
        autoDestroy: true,
        proxy: {
            type: 'ajax',
            url: 'queryWarnigLevelList.do',
            actionMethods: 'post',
            reader: {
                type: 'json',
                root: 'warningLevelList'
            }
        }
    });

    var errorTypeField = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '异常等级:',
        name: 'errLevel',
        labelWidth: 60,
        padding: 5,
        store: errLevelStore,
        queryMode: 'local',
        width: 170,
        value: '-1',
        editable: false,
        displayField: 'dname',
        valueField: 'dcode'
    });


    /** 开始时间* */
    var startTimeInput = Ext.create('Ext.form.DateField',
        {
            labelWidth: 70,
            emptyText: '--请输入开始时间--',
            name: 'beginTime',
            width: 165,
            margin: '5',
            format: 'Y-m-d H:i:s'
        });

    /** 结束时间* */
    var endTimeInput = Ext.create('Ext.form.DateField',
        {
            name: 'endTime',
            emptyText: '--请输入结束时间--',
            labelWidth: 19,
            width: 190,
            margin: '5',
            format: 'Y-m-d H:i:s',
            fieldLabel: '至',
            labelSeparator: ''
        });


    warningStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'IeaiHcActWarningModel',
        proxy: {
            type: 'ajax',
            url: 'inspectionTemplate/queryHcActWarning.do',
            reader: {
                type: 'json',
                root: 'list',
                total: 'total'
            }
        }
    });

    warningStore.on('beforeload', function (store, options) {
        var new_params = {
            iCenterId: itemNameField.getValue(),
            systemId: systemField.getValue(),
            iChkItemId: eventCategoryField.getValue(),
            tFlag: errorTypeField.getValue(),
            startTime: startTimeInput.getValue(),
            endTime: endTimeInput.getValue(),
        };
        Ext.apply(warningStore.proxy.extraParams, new_params);
    });

    /** 分页工具栏* */
    var bsPageBar = Ext.create('Ext.ux.ideal.grid.PagingToolbar', {
        store: warningStore,
        dock: 'bottom',
        baseCls: Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border: false,
        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
        emptyMsg: "没有记录"
    });

    /**查询*/
    var queryButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '查询',
        handler: function () {
            warningStore.reload();
        }
    });

    /**导出*/
    var exportButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '导出',
        handler: exportWarning,
    });

    /**批处理*/
    var batchingButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '批处理',
        handler: warningWork,
    });

    var childColumns = [
        {
            text: '序号',
            xtype: 'rownumberer',
            width: 40,
        },
        {
            text: '业务系统',
            dataIndex: 'sysName',
            flex: 1
        },
        {
            text: '设备名称',
            dataIndex: 'hostName',
            flex: 1,
        },
        {
            text: '设备ip',
            dataIndex: 'ip',
            flex: 1,
        },
        {
            text: '报警次数',
            dataIndex: 'wCount',
            width: 100
        },
        {
            text: '异常等级',
            dataIndex: 'wCode',
            width: 100,
            renderer: function (value, metaData, record) {
                var backValue = '';
                if (value == '0') {
                    backValue += '<span style="float:left;width: 30px">' + "正常" + '</span><span class="normal_green general_attribute"></span>';
                } else if (value == '1') {
                    backValue += '<span style="float:left;width: 30px">' + "警告" + '</span><span class="warning_yellow general_attribute"></span>';
                } else if (value == '2') {
                    backValue += '<span style="float:left;width: 30px">' + "错误" + '</span><span class="error_orange general_attribute"></span>';
                } else if (value == '3') {
                    backValue += '<span style="float:left;width: 30px">' + "紧急" + '</span><span class="emergency_red general_attribute"></span>';
                }
                return backValue;
            }
        },
        {
            text: '异常说明',
            dataIndex: 'wMessage',
            flex: 1,
            renderer:function(value,p,record){
                p.tdAttr = " data-qtip = '"+value+"'";
                return value;
            }
        },
        {
            text: '巡检项分类',
            dataIndex: 'eventCategory',
            width: 100
        },
        {
            text: '首次报警时间',
            dataIndex: 'wDate',
            flex: 1,
        },
        {
            text: '最近报警时间',
            dataIndex: 'lDate',
            flex: 1,
        },
        {
            text: '操作',
            width: 60,
            renderer: function (value, p, record) {
                var iId = record.data.iId;
                var rs = "<a  href=\"#\" onclick='warningConnOne(\"" + iId + "\")' style=' display:block; text-decoration:none;float:left; margin-left: 5px '>处理</a>"
                return rs;
            }
        },
    ]

    function getWcodeValue(value, metaData, record) {
        if ("0" == value) {
            return "正常(" + value + ")";
        } else if ("1" == value) {
            return "关注(" + value + ")";
        } else if ("2" == value) {
            return "一般(" + value + ")";
        } else if ("3" == value) {
            return "次要(" + value + ")";
        } else if ("4" == value) {
            return "警告(" + value + ")";
        } else if ("5" == value) {
            return "严重(" + value + ")";
        }
    }

    var child_grid_panel = Ext.create('Ext.grid.Panel', {
        // title: '巡检系统异常',
        width: "100%",
        height: "100%",
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit: 2})],

        selType: "checkboxmodel",
        selModel: {
            injectCheckbox: 0,
            mode: "SIMPLE",     //"SINGLE"/"SIMPLE"/"MULTI"
            checkOnly: true     //只能通过checkbox选择
        },
        // renderTo : "exceptionPage_area",
        store: warningStore,
        region: 'center',
        border: false,
        columnLines: true,
        columns: childColumns,
        collapsible: false,
        bbar: bsPageBar
    });

    var actNameForm = Ext.create('Ext.form.Panel', {
        region: 'north',
        border: false,
        dockedItems: [
            {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items:
                    [
                        itemNameField,
                        systemField,
                        eventCategoryField,
                        errorTypeField,
                        startTimeInput,
                        endTimeInput
                    ]
            },
            {
                xtype: 'toolbar',
                border: false,
                dock: 'top',
                items: [
                    '->',
                    queryButton,
                    exportButton,
                    batchingButton
                ]
            }
        ]
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "exceptionPage_area",
        border: true,
        height: warnTabPanel.getHeight() - modelHeigth,
        width: warnTabPanel.getWidth(),
        layout: "border",
        items: [actNameForm, child_grid_panel]
    });

    warnTabPanel.on("resize", function () {
        mainPanel.setHeight(warnTabPanel.getHeight() - modelHeigth);
        mainPanel.setWidth(warnTabPanel.getWidth());
    });

    function warningWork() {
        var data = child_grid_panel.getView().getSelectionModel().getSelection();
        if (data.length == 0) {
            Ext.Msg.alert('提示', '请至少选择一条记录!');
            return;
        }
        var ids = [];
        for (let i = 0; i < data.length; i++) {
            ids.push(data[i].get('iId'));
        }
        warningConn(ids);
    }

    function warningConn(ids) {
        //提交
        var submit_saveButton = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '提交',
            handler: saveSysTemp
        });

        /**返回*/
        var submit_returnButton = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '返回',
            handler: returnButton
        });

        var submit_causeInput = Ext.create('Ext.form.TextArea', {
            xtype: 'textareafield',
            labelWidth: 80,
            // labelAlign: 'left',
            fieldLabel: '处理意见:',
            emptyText: '',
            labelSeparator: '',
            width: 600,
            height: 200,
            margin: '5',
            id: 'submit_causeInput',
        });

        var submit_grid_panel = Ext.create('Ext.form.Panel', {
            width: "100%",
            height: "100%",
            region: 'center',
            border: false,
            collapsible: false,
            dockedItems: [
                {
                    xtype: 'toolbar',
                    //baseCls: 'customize_gray_back',
                    items: [
                        "->",
                        submit_saveButton,
                        submit_returnButton,
                    ]
                },
                {
                    xtype: 'toolbar',
                    //baseCls: 'customize_gray_back',
                    items: [
                        submit_causeInput,
                    ]
                },
            ]
        });

        function saveSysTemp(btn) {
            var reason = submit_causeInput.getValue();
            if ("" == reason || null == reason) {
                Ext.Msg.alert('提示', '处理意见不能为空！');
                return;
            }
            Ext.Ajax.request({
                url: 'batchActWarning.do',
                params: {
                    batchIds: ids,
                    msg: reason,
                    disposeType: "2",
                },
                method: 'POST',
                success: function (response, opts) {
                    warningStore.reload();
                    var success = Ext.decode(response.responseText).success;
                    if (success) {
                        Ext.Msg.alert('提示', "操作成功!");
                        win.destroy();
                    }
                }
            });
        }

        win = Ext.create('widget.window', {
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            modal: true,
            title: '',
            closable: true,
            closeAction: 'destroy',
            width: 724,
            height: 400,
            layout: 'border',
            items: [submit_grid_panel]
        });
        win.show();

        function returnButton() {
            win.destroy()
        }
    }

    //导出按钮
    function exportWarning() {
        if (!startTimeInput.isValid()) {
            Ext.Msg.alert('提示', "开始时间格式错误,请重新输入。");
            return;
        }
        if (!endTimeInput.isValid()) {
            Ext.Msg.alert('提示', "结束时间格式错误,请重新输入。");
            return;
        }
        if (startTimeInput.getRawValue().length > 0 && endTimeInput.getRawValue().length) {
            if (startTimeInput.getRawValue() > endTimeInput.getRawValue()) {
                Ext.Msg.alert('提示', '开始时间不能大于结束时间');
                return;
            }
        }

        var startTime = startTimeInput.getValue();
        var endTime = endTimeInput.getValue();
        var iCenterId = itemNameField.getValue();
        var systemId = systemField.getValue();
        var iChkItemId = eventCategoryField.getValue();
        var tFlag = errorTypeField.getValue();
        var start = 0;
        var limit = 65530;

        var url = 'inspectionTemplate/exportHcActWarning.do?'
        if (typeof (startTime) == "undefined" || startTime == null || startTime == "") {
        } else {
            url = url + 'startTime=' + startTime
        }
        if (typeof (endTime) == "undefined" || endTime == null || endTime == "") {
        } else {
            url = url + '&endTime=' + endTime
        }
        if (typeof (iCenterId) == "undefined" || iCenterId == null || iCenterId == "") {
        } else {
            url = url + '&iCenterId=' + iCenterId
        }
        if (typeof (systemId) == "undefined" || systemId == null || systemId == "") {
        } else {
            url = url + '&systemId=' + systemId
        }
        if (typeof (iChkItemId) == "undefined" || iChkItemId == null || iChkItemId == "") {
        } else {
            url = url + '&iChkItemId=' + iChkItemId
        }
        if (typeof (tFlag) == "undefined" || tFlag == null || tFlag == "") {
        } else {
            url = url + '&tFlag=' + tFlag
        }
        url = url + '&start=' + start + '&limit=' + limit;
        window.location.href = url;
    }
});

function warningConnOne(id) {
    //提交
    var submit_saveButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '提交',
        handler: saveSysTemp
    });

    /**返回*/
    var submit_returnButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '返回',
        handler: returnButton
    });

    //处理意见
    var submit_causeInput = Ext.create('Ext.form.TextArea', {
        xtype: 'textareafield',
        labelWidth: 80,
        // labelAlign: 'left',
        fieldLabel: '处理意见:',
        emptyText: '',
        labelSeparator: '',
        width: 600,
        height: 200,
        margin: '5',
        id: 'submit_causeInput',
    });

    var submit_grid_panel = Ext.create('Ext.form.Panel', {
        width: "100%",
        height: "100%",
        region: 'center',
        border: false,
        collapsible: false,
        dockedItems: [
            {
                xtype: 'toolbar',
                //baseCls: 'customize_gray_back',
                items: [
                    "->",
                    submit_saveButton,
                    submit_returnButton,
                ]
            },
            {
                xtype: 'toolbar',
                //baseCls: 'customize_gray_back',
                items: [
                    submit_causeInput,
                ]
            },
        ]
    });

    function saveSysTemp(btn) {
        var reason = submit_causeInput.getValue();
        if ("" == reason || null == reason) {
            Ext.Msg.alert('提示', '处理意见不能为空！');
            return;
        }

        Ext.Ajax.request({
            url: 'dealActWarning.do',
            params: {
                aid: id,
                msg: reason,
                disposeType: "2",
            },
            method: 'POST',
            success: function (response, opts) {
                warningStore.reload();
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    Ext.Msg.alert('提示', "操作成功!");
                    win.destroy();
                }
            }
        });
    }

    win = Ext.create('widget.window', {
        draggable: false,// 禁止拖动
        resizable: false,// 禁止缩放
        modal: true,
        title: '',
        closable: true,
        closeAction: 'destroy',
        width: 724,
        height: 400,
        layout: 'border',
        items: [submit_grid_panel]
    });
    win.show();

    function returnButton() {
        win.destroy()
    }
}
