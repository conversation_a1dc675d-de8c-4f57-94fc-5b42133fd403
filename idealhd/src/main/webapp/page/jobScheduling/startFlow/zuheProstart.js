//begin.added by manxi_z<PERSON>.
//参数信息--导入Excel用
//var upLoadformPane = '';
//var upldWin = '';
//var paramNames = '';
//var paramName_splitStr = '@@';
//end.added by manxi_zhao.
//var rightPanel = null;
//var isystemId = 0;
//var hcitemId = 0;
//var curSysNam = "";
//var serverArr = [];//选中服务器
//Array.prototype.baoremove = function(dx)
//{
//    if(isNaN(dx)||dx>this.length){return false;}
//    this.splice(dx,1);
//}

Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.tip.QuickTipManager.init ();
	
/*************************************** 组件model ***************************************************/
	var loadMarsk = new Ext.LoadMask(contentPanel, {  
	    msg:'数据处理中，请稍候......',    
	    disabled:false,
	    removeMask:true, // 完成后移除 
	    store:environmentVariableStore  
	});
	
	 var date = new Date();
	 var iniwokflowinstance =  dateFormat("YYYYmmdd",date);  //date.getFullYear() + '' + (date.getMonth() + 1) + '' + date.getDate();
	/** 参数信息列表model* */
	Ext.define ('paramInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iparamName',//参数名称
	                type : 'string'
	            },
	            {
	                name : 'iparamType',//参数类型
	                type : 'string'
	            },
	            {
	                name : 'iparamValue',//参数值
	                type : 'string'
	            },
	            {
	                name : 'iparamDesc',//参数说明
	                type : 'string'
	            },
	            {
	                name : 'readOnly',
	                type : 'string'
	            }
	            
	    ]
	});
	
	/** 环境变量信息列表model* */
	Ext.define ('environmentVariableModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'ievaName',
	                type : 'string'
	            },
	            {
	                name : 'ievaType',
	                type : 'string'
	            },
	            {
	                name : 'ievaValue',
	                type : 'string'
	            },
	            {
	                name : 'readOnly',
	                type : 'string'
	            }
	    ]
	});
	
	/** 审核人列表model* */
	Ext.define ('AuditorModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'loginName',
	                type : 'string'
	            },
	            {
	                name : 'fullName',
	                type : 'string'
	            }
	    ]
	});
	
/*********************************  组件store ***********************************************************/
	/** 参数信息列表store* */
	var paramInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramInfoModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'startFlow/getParameterInfo.do',
	        params:{
	        	
	        },
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	paramInfoStore.on('load', function(store, options) {
//		paramNames = '';
//		for (var i = 0; i < store.getCount (); i++)
//		{
//			var record = store.getAt (i);
//			//参数校验
//			if(i==0){
//				paramNames += record.data.iparamName;
//			}else{
//				paramNames += paramName_splitStr + record.data.iparamName;
//			}
//		}
	});
	/** 环境变量信息列表store* */
	var environmentVariableStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'environmentVariableModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'startFlow/getEnvironmentVariable.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	/** 审核人列表store* */
	var auditorStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    model : 'AuditorModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getAuditorListForJobscheduling.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	auditorStore.load (
	{
	    params :
	    {
	    	//iupperId:ibusnesSysIid
	    },
	    callback : function (records, operation, success)
	    {
			for (var i = 0; i < auditorStore.getCount (); i++)
			{
				if (iexecUserString == auditorStore.getAt (i).data.loginName)
				{
					auditorComBox.setValue (iexecUserString);
				}
			}
	    }
	});
/*************************************** 组件columns ****************************************************/
	/** 参数信息列表column* */
	var paramInfoColumns = [
        {
            text : '序号',
            width : 70,
            align:'left',
            xtype : 'rownumberer'
        },
        {
            text : '参数名称',
            dataIndex : 'iparamName',
            sortable:false,
            width : 200
        },
        {
            text : '参数类型',
            dataIndex : 'iparamType',
            sortable:false,
            width : 170
        },
        {
        	id:'paramType_Clumn',
            text : '参数值',
            dataIndex : 'iparamValue',
            sortable:false,
            width : 400,
            flex:1,
            editor :
            {
//	            allowBlank : false
            }
        },
        {
            text : '参数说明',
            dataIndex : 'iparamDesc',
            sortable:false,
            width:200
//            ,flex : 1
        },
        {
            text : '是否只读',
            dataIndex : 'readOnly',
            sortable:false,
            width:100,
            hidden:true
//            ,flex : 1
        }
	];
	
	/** 环境变量信息列表column* */
	var environmentVariableColumns = [
        {
            text : '序号',
            width : 70,
            align:'left',
            xtype : 'rownumberer'
        },
        {
            text : '环境变量名称',
            dataIndex : 'ievaName',
            width : 200
        },
        {
            text : '环境变量类型',
            dataIndex : 'ievaType',
            width : 200
        },
        {
            text : '环境变量值',
            dataIndex : 'ievaValue',
            width : 200,
            flex:1,
            sortable:false,
            editor :
            {
//	            allowBlank : false
            }
        },
        {
            text : '是否只读',
            dataIndex : 'readOnly',
            sortable:false,
            width:100,
            hidden:true
//            ,flex : 1
        }
	];
	
/****************************************** 按钮和下拉框 ****************************************************/
	/** 提交按钮* */
	var submitButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : "启动",
	    handler : submitFunction
	});
	
	/** 双人复核发起审核按钮* */
	var doubleCheckButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    text : "发起审核",
	    handler : doubleCheckFunction
	});
	
/****************************** 组件gridpanel *******************************************************************/
	// 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
//	var cellEditing1 = Ext.create('Ext.grid.plugin.CellEditing', {
//		clicksToEdit : 2//,
//	});
	// 定义可编辑grid组件
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
//		,listeners:{  
////            'edit':function(e,s){  
////            	  if(s.field=="company"){  
//////	            	  alert(1);  
////	            	  Ext.getCmp('dg').setEditor(new Ext.form.field.Text({value: 3}));    
////	              }  
////	          }  
//			'beforeedit':function( editor, e, eOpts ){
//				var type = e.record.data.iparamType;
//				if('Boolean'==type){
////					alert('type='+type);
//					Ext.getCmp('paramType_Clumn').setEditor(new Ext.form.field.ComboBox({
//    	    		    editable : true,
//    	      	        valueField : 'value',
//    	      	        displayField : 'name',
//    	      	        queryMode : 'local' , 
//    	      	        store: comboStore, 
//    	      	        listeners:{
//    	      	             select:function(combo,comBoxsrecords,index){
//    	      	            	//把下拉选 选中的新的资源组id赋值到原有的record当中
//    	      	            	var residRepaced=comBoxsrecords[0].get('value');
////    	      	            	alert('val='+residRepaced);
//    	      	            	record.set('iparamValue',residRepaced);	
////    			      	            	record.commit();
//    	      	            }
//    	      	        }
//    	      	    })); 
//				}else{
//					Ext.getCmp('paramType_Clumn').setEditor(new Ext.form.field.Text({}));
//				}
//			}
//	      }  
	});
	
	/**参数信息列表gridpanel* */
	var param_info_grid = Ext.create ('Ext.grid.Panel',
	{
		id :'param_info_grid_id',
		region : 'center',
		padding:panel_margin,
		border : true,
		autoScroll : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
//		selModel : selModel,
	    store : paramInfoStore,
	    columnLines : true,
	    columns : paramInfoColumns,
	    plugins : [ cellEditing ]
//	    dockedItems : [ 
//	    {
//	        xtype : 'toolbar',
//	        items : [ hcitemComBox, '->', hisButton, importButton ]
//	    }]
	});
	
	/**环境变量信息列表gridpanel* */
	var rule_info_grid = Ext.create ('Ext.grid.Panel',
	{
//		selModel : selModel,
		store : environmentVariableStore,
		region : 'center',
		autoScroll : true,
		columnLines : true,
		padding:panel_margin,
		border : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
//		plugins : [ cellEditing1 ],
		columns : environmentVariableColumns
//		,dockedItems : [ 
//   	    {
//   	        xtype : 'toolbar',
//   	        items : [ hcitemComBox ]
//   	    }]
	});	

	//审核人列表combo
	var auditorComBox = Ext.create ('Ext.form.ComboBox',
	{
		padding : '0 0 0 5',
	    editable : false,
	    fieldLabel : "审核人",
	    labelWidth : 50,
	    //				padding : 5,
	    hidden : true,
	    store : auditorStore,
	    queryMode : 'local',
	    width : 250,
	    displayField : 'fullName',
	    valueField : 'loginName' ,
	    listeners :
	    {
	        beforequery : function (e)
	        {
		        var combo = e.combo;
		        if (!e.forceAll)
		        {
			        var value = e.query;
			        combo.store.filterBy (function (record, id)
			        {
				        var text = record.get (combo.displayField);
				        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
			        });
			        combo.expand ();
			        return false;
		        }
	        }
	    }
	});
/**************** 组件tabPanel **********************************************************************/
	/** tabPanel* */
	var tabPanelForICFS = Ext.create ('Ext.tab.Panel',
	{
		padding : '0 2 0 0',
	    region : 'center',
	    activeTab : 0,
	    bodyCls:'x-docked-noborder-top',
	    cls:'customize_panel_back',
	    border : false,
	    items : [
	            {
	                title : '参数信息',
	                layout : 'fit',
	                border : false,
	                items : param_info_grid
	            },
	            {
	                title : '环境变量信息',
	                layout : 'fit',
	                border : false,
	                items : rule_info_grid
	            }
	    ],
	    dockedItems : [
		    {
		        dock : 'bottom',
		        xtype : 'toolbar',
		        border : false,
		        items : [
		                 {
		                	 xtype: 'tbseparator'
		 		        },	       
		 		        {
		 		            xtype: 'tbfill'
		 		        },auditorComBox, submitButton,doubleCheckButton
		        ]
		    }
	    ]
	});
	
	if(doubleCheckSwitch){
		auditorComBox.show();
		submitButton.hide();
		doubleCheckButton.show();
	}else{
		auditorComBox.hide();
		submitButton.show();
		doubleCheckButton.hide();
	}
	
	
/**** form **************************************************************************/
	// 工程名称下拉列表--model
	Ext.define ('projectModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iprjName', // 系统名称
	                type : 'string'
	            },
	            {
	                name : 'iprjId', // 系统ID
	                type : 'long'
	            },
	            {
	                name : 'iupperId',
	                type : 'long'
	            }
	    ]
	});
	// 工程名称下拉列表stor
	var getPrjNameStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    model : 'projectModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'startFlow/getPrjNameForIEAI.do', 
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	   getPrjNameStore.on('beforeload', function(store, options) {  
        var new_params = {  
               protype:protype
                        };  
        Ext.apply(store.proxy.extraParams, new_params);  
        });
	
	
	// 工作流名称下拉列表--model
	Ext.define ('workFlowModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iworkFlowName', // 系统名称
	                type : 'string'
	            },
	            {
	                name : 'iworkFlowId', // 系统ID
	                type : 'long'
	            }
	    ]
	});
	// 工程名称下拉列表stor
	var getWorkFlowNameStore = Ext.create ('Ext.data.Store',
	{
		autoLoad : false,
	    autoDestroy : true,
	    model : 'workFlowModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'startFlow/getWorkFlowName.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	if(jobSchedulingQuerySystemSwitch){
		//工程名称下拉列表
		var prjNameComBox = Ext.create ('Ext.form.field.ComboBox',
		{
			id : 'prjNameComBox_id',
		    name : 'form_prjName',
		    fieldLabel : '工程名称',
		    displayField : 'iprjName',
		    valueField : 'iprjId',
		    emptyText : '--请输选择工程名称--',
			labelAlign: 'right', 
		    width : '105%',
		    labelWidth : 79,
		    editable : true,
		    typeAhead : true,
		    store : getPrjNameStore,
		    queryMode : 'local',
		    listeners :
		    {
		    	select : function ()
		        {
		    		Ext.getCmp("workFlowNameComBox_id").clearValue();
		    		Ext.getCmp("instName_id").setValue(iniwokflowinstance);
		    		Ext.getCmp("workFlowDesc_id").setValue();
		    		Ext.getCmp("flowAnnotation_id").setValue();
		    		var iprjId = this.getValue();
		    		var iprjName=this.getRawValue();
		    		loadMarsk.show();
		    		// 参数信息列表grid重新加载
			        getWorkFlowNameStore.load(
		    		{
		    			params :
				        {
		    				iprjId : iprjId
				        },
				        callback : function(record, options, success) {  
				        	loadMarsk.hide();
				        } 
		    		});
			        environmentVariableStore.load({
			        	params :
				        {
		    				iprjId : iprjId,
		    				iprjName:iprjName
		    				
				        },
				        callback : function(record, options, success) {  
				        	loadMarsk.hide();
				        } 
			        });
					// paramInfoStore.load({
					// params :
					// {
					// iprjId : iprjId,
					// iprjName:iprjName,
					// iworkFlowId:form.getForm ().findField
					// ("form_prjName").getValue(),
					// iworkFlowName:form.getForm ().findField
					// ("form_prjName").getRawValue()
					//			        }
					//		        });
			        auditorStore.load({
			        	params :
				        {
			        		iprjId : iprjId 
				        }
			        });
		        },
		        beforequery : function (e)
		        {
			        var combo = e.combo;
			        if (!e.forceAll)
			        {
				        var value = e.query;
				        combo.store.filterBy (function (record, id)
				        {
					        var text = record.get (combo.displayField);
					        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
				        });
				        combo.expand ();
				        return false;
			        }
		        }
		    }
		});
		//工作流名称下拉列表
		var workFlowNameComBox = Ext.create ('Ext.form.field.ComboBox',
		{
			id : 'workFlowNameComBox_id',
		    name : 'form_workFlowName',
		    fieldLabel : '工作流名称',
		    displayField : 'iworkFlowName',
		    valueField : 'iworkFlowId',
		    emptyText : '--请输选择工作流名称--',
			labelAlign: 'right', 
		    width : '100%',
		    labelWidth : 79,
		    editable : true,
		    typeAhead : true,
		    store : getWorkFlowNameStore,
		    queryMode : 'local',
		    listeners :
		    {
		    	select : function ()
		        {
		    		loadMarsk.show();
		    		Ext.getCmp("instName_id").setValue();
		    		Ext.getCmp("flowAnnotation_id").setValue();
		    		iworkFlowId = this.getValue ();
		    		iworkFlowName=this.getRawValue();
		    		// 参数信息列表grid重新加载
		    		Ext.Ajax.request (
					{
					    url : 'startFlow/getWorkFlowDesc.do',
					    method : 'POST',
					    params :
					    {
					    	iprjId : form.getForm ().findField ("form_prjName").getValue(),
		    				iprjName:form.getForm ().findField ("form_prjName").getRawValue(),
		    				iworkFlowId:iworkFlowId,
		    				iworkFlowName:iworkFlowName
					    },
					    success : function (response, options)
					    {
					    	loadMarsk.hide();
						    var success = Ext.decode (response.responseText).dataList;
						    if(success.length>0){
						    	Ext.getCmp("workFlowDesc_id").setValue(success[0].workFlowDesc);
						    }else{
						    	Ext.getCmp("workFlowDesc_id").setValue();
						    }
					    },
					    failure : function(result, request) {
					    	loadMarsk.hide();
					    	Ext.getCmp("workFlowDesc_id").setValue();
						}
					
					});
		    		//重新加载工作流参数
		    		paramInfoStore.load({
			        	params :
				        {
		    				iprjId : form.getForm ().findField ("form_prjName").getValue(),
		    				iprjName:form.getForm ().findField ("form_prjName").getRawValue(),
		    				iworkFlowId:iworkFlowId,
		    				iworkFlowName:iworkFlowName
				        },
				        callback : function(record, options, success) {  
				        	loadMarsk.hide();
				        }
			        });
		        },
		        beforequery : function (e)
		        {
			        var combo = e.combo;
			        if (!e.forceAll)
			        {
				        var value = e.query;
				        combo.store.filterBy (function (record, id)
				        {
					        var text = record.get (combo.displayField);
					        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
				        });
				        combo.expand ();
				        return false;
			        }
		        }
		    }
		});
		
		// 所属系统下拉框数据源Model
		Ext.define ('systemModel',
		{
		    extend : 'Ext.data.Model',
		    fields : [
			    {
			        name : 'prjId',
			        type : 'string'
			    },{
			    	name : 'sysName',
			    	type : 'string'
			    }
		    ]
		});
		// 所属系统下拉框数据源
		var cstore_isystem = Ext.create ('Ext.data.Store',
		{
		    autoLoad : true,
		    autoDestroy : true,
		    model : 'systemModel',
		    proxy :
		    {
		        type : 'ajax',
		        url : 'jzQuerySystemCombox.do',
		        reader :
		        {
		            type : 'json',
		            root : 'dataList'
		        }
		    }
		});
		// 所属系统下拉框
		var combobox_isystem = Ext.create ('Ext.form.field.ComboBox',
		{
		    width : '100%',
		    labelWidth : 79,
		    fieldLabel : '所属系统',
			labelAlign: 'right', 
		    displayField : 'sysName',
		    valueField : 'sysName',
		    emptyText : '--请输选择所属系统名称--',
		    editable : true,
		    hidden : true,
		    typeAhead : true,
		    store : cstore_isystem,
		    queryMode : 'local',
			listeners : {
				select : function() {
					Ext.getCmp("prjNameComBox_id").clearValue();
					Ext.getCmp("workFlowNameComBox_id").clearValue();
					var sysName = this.value;
					// 参数信息列表grid重新加载
					getPrjNameStore.load({
							params : {
								sysName : sysName
							}
					});
				},
		        beforequery : function (e)
		        {
			        var combo = e.combo;
			        if (!e.forceAll)
			        {
				        var value = e.query;
				        combo.store.filterBy (function (record, id)
				        {
					        var text = record.get (combo.displayField);
					        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
				        });
				        combo.expand ();
				        return false;
			        }
		        }
			}
		});
		
		//工作流说明
		var workFlowDesc = new Ext.form.TextField (
		{
			id:'workFlowDesc_id',
			name : 'form_workFlowDesc',
			fieldLabel : '工作流说明',
			emptyText : '--请输入工作流说明--',
			displayField : 'workFlowDesc',
			labelAlign: 'right', 
		    labelWidth : 79,
		    readOnly:true,
//		    disabled:true,
		    width : contentPanel.getWidth () 
		});
		//实例名称说明
		var instName = new Ext.form.TextField (
		{
			id:'instName_id',
		    emptyText : '--请输入实例名称--',
			labelAlign: 'right', 
		    fieldLabel : '<font color=\'red\'>实例名称</font>',
		    labelWidth : 79,
		    name : 'form_instName',
		    width : contentPanel.getWidth () 
		});
		//工作流注释说明
		var flowAnnotation = new Ext.form.TextField (
		{
			id:'flowAnnotation_id',
		    emptyText : '--请输入注释--',
		    fieldLabel : '工作流注释',
			labelAlign: 'right', 
		    labelWidth : 79,
		    name : 'form_flowAnnotation',
		    width : contentPanel.getWidth ()
		});
		
		/** form **/
		var form = new Ext.form.FormPanel (
		{
			region: 'north',
			layout : 'anchor',
//			bodyPadding : 5,
			border:false,
			bodyCls: 'service_platform_bodybg',
		    defaultType : 'textfield',
		    defaults :
		    {
//			    anchor : '99%'
		    	anchor : '100%'
		    },
		    items : [

		             {
							xtype : 'toolbar',
							border : false,
							//margin : '5',
							dock : 'top',
							items : [combobox_isystem,workFlowDesc]
						},{
							xtype : 'toolbar',
							border : false,
							//margin : '5',
							dock : 'top',
							items : [prjNameComBox, instName]
						},{
							xtype : 'toolbar',
							border : false,
							//margin : '5',
							dock : 'top',
							items : [workFlowNameComBox,flowAnnotation]
						}
		    
		             ]
		});
	}
	else
		{
		//工程名称下拉列表
		var prjNameComBox = Ext.create ('Ext.form.field.ComboBox',
		{
			id : 'prjNameComBox_id',
		    name : 'form_prjName',
		    fieldLabel : '工程名称',
		    displayField : 'iprjName',
		    valueField : 'iprjId',
		    emptyText : '--请输选择工程名称--',
			labelAlign: 'right', 
		    width : '105%',
		    labelWidth : 79,
		    editable : true,
		    typeAhead : true,
		    store : getPrjNameStore,
		    queryMode : 'local',
		    listeners :
		    {
		    	select : function ()
		        {
		    		Ext.getCmp("workFlowNameComBox_id").clearValue();
		    		Ext.getCmp("instName_id").setValue(iniwokflowinstance);
		    		Ext.getCmp("workFlowDesc_id").setValue();
		    		Ext.getCmp("flowAnnotation_id").setValue();
		    		var iprjId = this.getValue();
		    		var iprjName=this.getRawValue();
		    		loadMarsk.show();
		    		// 参数信息列表grid重新加载
			        getWorkFlowNameStore.load(
		    		{
		    			params :
				        {
		    				iprjId : iprjId
				        },
				        callback : function(record, options, success) {  
				        	loadMarsk.hide();
				        } 
		    		});
			        environmentVariableStore.load({
			        	params :
				        {
		    				iprjId : iprjId,
		    				iprjName:iprjName
		    				
				        },
				        callback : function(record, options, success) {  
				        	loadMarsk.hide();
				        } 
			        });
					// paramInfoStore.load({
					// params :
					// {
					// iprjId : iprjId,
					// iprjName:iprjName,
					// iworkFlowId:form.getForm ().findField
					// ("form_prjName").getValue(),
					// iworkFlowName:form.getForm ().findField
					// ("form_prjName").getRawValue()
					//			        }
					//		        });
			        auditorStore.load({
			        	params :
				        {
			        		iprjId : iprjId 
				        }
			        });
		        }
		    }
		});
		//工作流名称下拉列表
		var workFlowNameComBox = Ext.create ('Ext.form.field.ComboBox',
		{
			id : 'workFlowNameComBox_id',
		    name : 'form_workFlowName',
		    fieldLabel : '工作流名称',
		    displayField : 'iworkFlowName',
		    valueField : 'iworkFlowId',
		    emptyText : '--请输选择工作流名称--',
			labelAlign: 'right', 
		    width : '100%',
		    labelWidth : 79,
		    editable : true,
		    typeAhead : true,
		    store : getWorkFlowNameStore,
		    queryMode : 'local',
		    listeners :
		    {
		    	select : function ()
		        {
		    		loadMarsk.show();
		    		Ext.getCmp("instName_id").setValue(iniwokflowinstance);
		    		Ext.getCmp("flowAnnotation_id").setValue();
		    		iworkFlowId = this.getValue ();
		    		iworkFlowName=this.getRawValue();
		    		// 参数信息列表grid重新加载
		    		Ext.Ajax.request (
					{
					    url : 'startFlow/getWorkFlowDesc.do',
					    method : 'POST',
					    params :
					    {
					    	iprjId : form.getForm ().findField ("form_prjName").getValue(),
		    				iprjName:form.getForm ().findField ("form_prjName").getRawValue(),
		    				iworkFlowId:iworkFlowId,
		    				iworkFlowName:iworkFlowName
					    },
					    success : function (response, options)
					    {
					    	loadMarsk.hide();
						    var success = Ext.decode (response.responseText).dataList;
						    if(success.length>0){
						    	Ext.getCmp("workFlowDesc_id").setValue(success[0].workFlowDesc);
						    }else{
						    	Ext.getCmp("workFlowDesc_id").setValue();
						    }
					    },
					    failure : function(result, request) {
					    	loadMarsk.hide();
					    	Ext.getCmp("workFlowDesc_id").setValue();
						}
					
					});
		    		//重新加载工作流参数
		    		paramInfoStore.load({
			        	params :
				        {
		    				iprjId : form.getForm ().findField ("form_prjName").getValue(),
		    				iprjName:form.getForm ().findField ("form_prjName").getRawValue(),
		    				iworkFlowId:iworkFlowId,
		    				iworkFlowName:iworkFlowName
				        },
				        callback : function(record, options, success) {  
				        	loadMarsk.hide();
				        }
			        });
		        }
		    }
		});
		
		// 所属系统下拉框数据源Model
		Ext.define ('systemModel',
		{
		    extend : 'Ext.data.Model',
		    fields : [
			    {
			        name : 'prjId',
			        type : 'string'
			    },{
			    	name : 'sysName',
			    	type : 'string'
			    }
		    ]
		});
		// 所属系统下拉框数据源
		var cstore_isystem = Ext.create ('Ext.data.Store',
		{
		    autoLoad : true,
		    autoDestroy : true,
		    model : 'systemModel',
		    proxy :
		    {
		        type : 'ajax',
		        url : 'jzQuerySystemCombox.do',
		        reader :
		        {
		            type : 'json',
		            root : 'dataList'
		        }
		    }
		});
		// 所属系统下拉框
		var combobox_isystem = Ext.create ('Ext.form.field.ComboBox',
		{
		    width : '100%',
		    labelWidth : 79,
		    fieldLabel : '所属系统',
			labelAlign: 'right', 
		    displayField : 'sysName',
		    valueField : 'sysName',
		    emptyText : '--请输选择所属系统名称--',
		    editable : true,
		    hidden : true,
		    typeAhead : true,
		    store : cstore_isystem,
		    queryMode : 'local',
			listeners : {
				select : function() {
					Ext.getCmp("prjNameComBox_id").clearValue();
					Ext.getCmp("workFlowNameComBox_id").clearValue();
					var sysName = this.value;
					// 参数信息列表grid重新加载
					getPrjNameStore.load({
							params : {
								sysName : sysName
							}
					});
				},
		        beforequery : function (e)
		        {
			        var combo = e.combo;
			        if (!e.forceAll)
			        {
				        var value = e.query;
				        combo.store.filterBy (function (record, id)
				        {
					        var text = record.get (combo.displayField);
					        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
				        });
				        combo.expand ();
				        return false;
			        }
		        }
			}
		});
		
		//工作流说明
		var workFlowDesc = new Ext.form.TextField (
		{
			id:'workFlowDesc_id',
			name : 'form_workFlowDesc',
			fieldLabel : '工作流说明',
			displayField : 'workFlowDesc',
			labelAlign: 'right', 
			emptyText : '--请输入工作流说明--',
		    labelWidth : 79,
		    readOnly:true,
//		    disabled:true,
		    width : contentPanel.getWidth () 
		});
		//实例名称说明
		var instName = new Ext.form.TextField (
		{
			id:'instName_id',
		    emptyText : '--请输入实例名称--',
			labelAlign: 'right', 
		    fieldLabel : '<font color=\'red\'>实例名称</font>',
		    labelWidth : 79,
		    name : 'form_instName',
		    width : contentPanel.getWidth () 
		});
		//工作流注释说明
		var flowAnnotation = new Ext.form.TextField (
		{
			id:'flowAnnotation_id',
		    emptyText : '--请输入注释--',
		    fieldLabel : '工作流注释',
			labelAlign: 'right', 
		    labelWidth : 79,
		    name : 'form_flowAnnotation',
		    width : contentPanel.getWidth () 
		});
		
		/** form **/
		var form = new Ext.form.FormPanel (
		{
			region: 'north',
			layout : 'anchor',
//			bodyPadding : 5,
			border:false,
			bodyCls: 'service_platform_bodybg',
		    defaultType : 'textfield',
		    defaults :
		    {
//			    anchor : '99%'
		    	anchor : '100%'
		    },
		    items : [
		            combobox_isystem,prjNameComBox, workFlowNameComBox,workFlowDesc, instName,flowAnnotation
		    ]
		});
		}
	
	/********锦州银行需展示所属系统下拉选***********/
	if(jobSchedulingQuerySystemSwitch){
		combobox_isystem.show();
	}
	form.getForm ().findField ("form_instName").setValue(iniwokflowinstance)
/************************* 主panel *******************************************************/
	
	var rightPanel = Ext.create ("Ext.panel.Panel",
	{
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight () - modelHeigth - 5,
	    layout : 'border',
//	    bodyPadding:grid_margin,
	    bodyCls: 'service_platform_bodybg',
	    border : true,
	    items : [form,tabPanelForICFS],
	    renderTo : "startFlow_grid"
	});

	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		rightPanel.setHeight (contentPanel.getHeight () - modelHeigth- 5);
		rightPanel.setWidth (contentPanel.getWidth () );
	});
	//当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy (rightPanel);
//    	Ext.destroy (param_his_win);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
    });
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
/*************************************************************************/
	// 提交按钮
	function submitFunction(btn){
//		alert('提交按钮被点击了。。。');
//		return;
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm ("确认启动", "是否确认启动该流程?", function (id)
		{
			if (id == 'yes')
				submitIns (1);
		});
	}

	function submitIns (istate)
	{
		//工程名称
		var form_prjName = form.getForm ().findField ("form_prjName").getRawValue();
		var form_prjId = form.getForm ().findField ("form_prjName").getValue();
		//工作流名称
		var form_workFlowId = form.getForm ().findField ("form_workFlowName").getValue();
		var form_workFlowName = form.getForm ().findField ("form_workFlowName").getRawValue();
		//工作流实例名
		var form_instName = form.getForm ().findField ("form_instName").getRawValue();
		//工作流注释
		var form_flowAnnotation = form.getForm ().findField ("form_flowAnnotation").getValue();
		
		if (trim (form_prjName) == '' || (null == form_prjName))
		{
			Ext.MessageBox.alert ("提示", "请选择工程名称");
			return false;
		}
		if (trim (form_workFlowName) == '' || (null == form_workFlowName))
		{
			Ext.MessageBox.alert ("提示", "请选择工作流名称");
			return false;
		}
		if (trim (form_instName) == '' || (null == form_instName))
		{
			Ext.MessageBox.alert ("提示", "请输入实例名称");
			return false;
		}
		
		var params=param_info_grid.store.data.items;
		
		var paraJsonData="[";
		for(var i=0;i<params.length;i++){
			var paramsValue = params[i].data.iparamValue;
			if(paramsValue.indexOf("\"")>-1){
				paramsValue='\\'+paramsValue;
			}
			if(i==0){
				paraJsonData=paraJsonData+"{\"name\":\""+params[i].data.iparamName+"\",\"iparamType\":\""+params[i].data.iparamType+"\",\"iparamValue\":\""+paramsValue+"\"}";
			}else{
				paraJsonData=paraJsonData+",{\"iparamName\":\""+params[i].data.iparamName+"\",\"iparamType\":\""+params[i].data.iparamType+"\",\"iparamValue\":\""+paramsValue+"\"}";
			}
		}
		paraJsonData=paraJsonData+"]";
		
		
		var evas=rule_info_grid.store.data.items;
		var evaJsonData="[";
		for(var i=0;i<evas.length;i++){
			var evaValue = evas[i].data.ievaValue;
			if(evaValue.indexOf("\"")>-1){
				evaValue="\\"+evaValue;
			}
//			if(i==0){
//				evaJsonData=evaJsonData+"{\"ievaName\":\""+evas[i].data.ievaName+"\",\"ievaType\":\""+evas[i].data.ievaType+"\",\"ievaValue\":\""+evaValue+"\"}";
//			}else{
//				evaJsonData=evaJsonData+",{\"ievaName\":\""+evas[i].data.ievaName+"\",\"ievaType\":\""+evas[i].data.ievaType+"\",\"ievaValue\":\""+evaValue+"\"}";
//			}
			var ss = Ext.JSON.encode(evas[i].data);
	        if (i == 0)
	        	evaJsonData = evaJsonData + ss;
	        else
	        	evaJsonData = evaJsonData + "," + ss;
		}
		evaJsonData=evaJsonData+"]";
		
//		alert(paraJsonData);
//		alert(evaJsonData);
		
		
		
		
//		alert(form_prjName+"===="+form_prjId+"===="+form_workFlowName+"===="+form_instName+"===="+form_flowAnnotation);
//		return;

		Ext.MessageBox.wait ("启动中...", "提示");
		Ext.Ajax.request (
		{
		    url : 'group/groupProstart.do',
		    method : 'POST',
		    params :
		    {
		    	iprjName:form_prjName,
		    	iworkFlowName:form_workFlowName,
		    	instName:form_instName,
		    	iparamJsonData:paraJsonData,
		    	ievaJsonData:evaJsonData,
		    	protype:protype
		    	
		    },
		    success : function (response, options)
		    {
		    	Ext.MessageBox.hide();
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    if (success==true)
			    {
			    	Ext.Msg.alert ('提示', message, function ()
				    {
	    				//清理数据
			    		Ext.getCmp("instName_id").setValue();
					    if (Ext.isIE)
					    {
						    CollectGarbage ();
					    }
				    });
			    	
			    }
			    else
			    {
			    	Ext.MessageBox.hide();
				    Ext.MessageBox.show (
				    {
				        width : 300,
				        title : "启动失败",
				        msg : message,
				        buttonText :
				        {
					        yes : '确定'
				        },
				        buttons : Ext.Msg.YES
				    });
			    }
		    },
		    failure : function(result, request) {//alert(result);
		    	Ext.MessageBox.hide();
				secureFilterRs(result,"操作失败！");

			}
		
		});
	}
/*************************************************************************/
	// 双人复核发起审核按钮
	function doubleCheckFunction(btn){
//		alert('提交按钮被点击了。。。');
//		return;
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm ("确认发起审核", "是否确认发起审核?", function (id)
		{
			if (id == 'yes')
				doubleCheckIns (1);
		});
	}

	function doubleCheckIns (istate)
	{
		//工程名称
		var form_prjName = form.getForm ().findField ("form_prjName").getRawValue();
		var form_prjId = form.getForm ().findField ("form_prjName").getValue();
		//工作流名称
		var form_workFlowId = form.getForm ().findField ("form_workFlowName").getValue();
		var form_workFlowName = form.getForm ().findField ("form_workFlowName").getRawValue();
		//工作流实例名
		var form_instName = form.getForm ().findField ("form_instName").getRawValue();
		//工作流注释
		var form_flowAnnotation = form.getForm ().findField ("form_flowAnnotation").getValue();
		
		if (trim (form_prjName) == '' || (null == form_prjName))
		{
			Ext.MessageBox.alert ("提示", "请选择工程名称");
			return false;
		}
		if (trim (form_workFlowName) == '' || (null == form_workFlowName))
		{
			Ext.MessageBox.alert ("提示", "请选择工作流名称");
			return false;
		}
		if (trim (form_instName) == '' || (null == form_instName))
		{
			Ext.MessageBox.alert ("提示", "请输入实例名称");
			return false;
		}
		
		
		var auditorValue = auditorComBox.getValue ();
		if (trim (auditorValue) == '' || (null == auditorValue))
		{
			Ext.MessageBox.alert ("提示", "请选择审核人");
			return false;
		}
		
		var params=param_info_grid.store.data.items;
		
		var paraJsonData="[";
		for(var i=0;i<params.length;i++){
			var paramsValue = params[i].data.iparamValue;
			if(paramsValue.indexOf("\"")>-1){
				paramsValue='\\'+paramsValue;
			}
			if(i==0){
				paraJsonData=paraJsonData+"{\"iparamName\":\""+params[i].data.iparamName+"\",\"iparamType\":\""+params[i].data.iparamType+"\",\"iparamValue\":\""+paramsValue+"\",\"iparamDesc\":\""+params[i].data.iparamDesc+"\"}";
			}else{
				paraJsonData=paraJsonData+",{\"iparamName\":\""+params[i].data.iparamName+"\",\"iparamType\":\""+params[i].data.iparamType+"\",\"iparamValue\":\""+paramsValue+"\",\"iparamDesc\":\""+params[i].data.iparamDesc+"\"}";
			}
		}
		paraJsonData=paraJsonData+"]";
		
		
		var evas=rule_info_grid.store.data.items;
		var evaJsonData="[";
		for(var i=0;i<evas.length;i++){
			var evaValue = evas[i].data.ievaValue;
			if(evaValue.indexOf("\"")>-1){
				evaValue="\\"+evaValue;
			}
//			if(i==0){
//				evaJsonData=evaJsonData+"{\"ievaName\":\""+evas[i].data.ievaName+"\",\"ievaType\":\""+evas[i].data.ievaType+"\",\"ievaValue\":\""+evaValue+"\"}";
//			}else{
//				evaJsonData=evaJsonData+",{\"ievaName\":\""+evas[i].data.ievaName+"\",\"ievaType\":\""+evas[i].data.ievaType+"\",\"ievaValue\":\""+evaValue+"\"}";
//			}
			var ss = Ext.JSON.encode(evas[i].data);
	        if (i == 0)
	        	evaJsonData = evaJsonData + ss;
	        else
	        	evaJsonData = evaJsonData + "," + ss;
		}
		evaJsonData=evaJsonData+"]";
		
//		alert(paraJsonData);
//		alert(evaJsonData);
		
		
		
		
//		alert(form_prjName+"===="+form_prjId+"===="+form_workFlowName+"===="+form_instName+"===="+form_flowAnnotation);
//		return;

		
		Ext.Ajax.request (
		{
		    url : 'startFlow/startDoubleCheck.do',
		    method : 'POST',
		    params :
		    {
		    	iprjName:form_prjName,
		    	iworkFlowName:form_workFlowName,
		    	instName:form_instName,
		    	iparamJsonData:paraJsonData,
		    	ievaJsonData:evaJsonData,
		    	auditUser:auditorValue
		    },
		    success : function (response, options)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    if (success==true)
			    {
			    	Ext.Msg.alert ('提示', message, function ()
				    {
	    				//清理数据
			    		Ext.getCmp("instName_id").setValue();
					    if (Ext.isIE)
					    {
						    CollectGarbage ();
					    }
				    });
			    	
			    }
			    else
			    {
				    Ext.MessageBox.show (
				    {
				        width : 300,
				        title : "启动失败",
				        msg : message,
				        buttonText :
				        {
					        yes : '确定'
				        },
				        buttons : Ext.Msg.YES
				    });
			    }
		    },
		    failure : function(result, request) {//alert(result);
				secureFilterRs(result,"操作失败！");

			}
		
		});
	}
	
	
	function dateFormat(fmt, date) {
	    let ret;
	    let opt = {
	        "Y+": date.getFullYear().toString(),        // 年
	        "m+": (date.getMonth() + 1).toString(),     // 月
	        "d+": date.getDate().toString(),            // 日
	        "H+": date.getHours().toString(),           // 时
	        "M+": date.getMinutes().toString(),         // 分
	        "S+": date.getSeconds().toString()          // 秒
	        // 有其他格式化字符需求可以继续添加，必须转化成字符串
	    };
	    for (let k in opt) {
	        ret = new RegExp("(" + k + ")").exec(fmt);
	        if (ret) {
	            fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
	        };
	    };
	    return fmt;
	}
	
});
