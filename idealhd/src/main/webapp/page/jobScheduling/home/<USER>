<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="ISO-8859-1"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>
<% String prjId = String.valueOf(request.getAttribute("prjId"));
if(prjId==null){
    prjId="";
}
 
%>
<script type="text/javascript">
try{
		var v_filterStatus =null;
        var v_filterPrjName =null;
        var v_insName=null;
        var v_flowname=null;
        var v_keyFlow=null;
        var b_keyFlow=false;
        var v_act_state=null;
        var v_filterPrjid=null;
        var isFirstTimeFlag=false;
        var sysType = 1;
        var fromFlag = 0;
        var v_filterActName = null;
		<%
				Object obj_filterStatus= request.getAttribute("filterStatus");
				Object obj_filterPrjName= request.getAttribute("filterPrjName");
				Object obj_filter_insName= request.getAttribute("filter_insName");
				Object obj_filterflowname= request.getAttribute("flowname");
				Object obj_filter_keyFlow= request.getAttribute("keyFlow");
				Object obj_filter_act_state= request.getAttribute("act_state");
				Object obj_filterPrjid= request.getAttribute("filterPrjid");
				Object obj_fromFlag= request.getAttribute("fromFlag");
				Object obj_filterActName= request.getAttribute("actName");
		%>
		v_filterStatus ='<%= null!=obj_filterStatus ?(String)obj_filterStatus:obj_filterStatus%>';
		v_filterPrjName='<%= null!=obj_filterPrjName?(String)obj_filterPrjName:obj_filterPrjName%>';
		v_flowname     ='<%= null!=obj_filterflowname?(String)obj_filterflowname:obj_filterflowname%>';
		v_insName      ='<%= null!=obj_filter_insName?(String)obj_filter_insName:obj_filter_insName%>';
		v_act_state    ='<%= null!=obj_filter_act_state?(String)obj_filter_act_state:obj_filter_act_state%>';
		v_keyFlow      ='<%= null!=obj_filter_keyFlow?(String)obj_filter_keyFlow:obj_filter_keyFlow%>';
		v_filterPrjid  ='<%= null!=obj_filterPrjid?(String)obj_filterPrjid:obj_filterPrjid%>';
		fromFlag  ='<%= null!=obj_fromFlag?(String)obj_fromFlag:obj_fromFlag%>';
		v_filterActName  ='<%= null!=obj_filterActName?(String)obj_filterActName:obj_filterActName%>';
		var jobscDoubleCheck= '<%=ServerEnv.getServerEnv().getBooleanConfig(ServerEnv.JOBSCHEDULING_WORKFLOW_DOUBLECHECK, false)%>';
		var bhPerSwitch= '<%=ServerEnv.getServerEnv().getBooleanConfig(ServerEnv.BH_PERMISSION_SWITCH, false)%>';
		if(v_keyFlow == '1'){
			b_keyFlow=true;
		}
		
		/*
		alert(v_filterPrjid);
		alert('v_act_state'+v_act_state);
 		alert('v_filterPrjName'+v_filterPrjName);
		alert('v_flowname'+v_flowname);
		alert('v_insName'+v_insName);
		alert('v_keyFlow'+v_keyFlow); */
}catch(e)
{
	alert(e);
}
</script>
<script type="text/javascript">
var prjId = '<%=prjId%>'
	if(prjId==null||prjId=="null"){
	    prjId='';
	}
	if(null!=v_filterPrjid){
		prjId=v_filterPrjid;
		isFirstTimeFlag=true;
	}
function flowquery(prjName,flowName){
	window.parent.location='<%=request.getContextPath()%>/flowquery.do?action=QueryFlow&testPrj='+prjName+'&testFlowName='+ flowName;
}
var interval=30;
var itype = 1;
</script>
<script type="text/javascript">
var jobSchedulingQuerySystemSwitch=<%=ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch()%>;
var bohaiBank=<%=ServerEnv.getInstance().getBohaiBankAlarmConfigSwitch()%>;
var isJHSwitch =  <%=ServerEnv.getServerEnv().isJHBankSwitch()%>;
var odsYiLianFDSwitch = <%=Environment.getOdsYILIANFLOWDESCSWITCH()%>;
var OdsYLHDJKGLSwitch=<%=ServerEnv.getInstance().getOdsYLHDJKGL()%>;
</script>
<style>
tr.x-grid-red .x-grid-td{
	background:red;
}
tr.x-grid-orange .x-grid-td{
	background:orange;
}
tr.x-grid-yellow .x-grid-td{
	background:yellow;
}
tr.x-grid-Yellow .x-grid-td{
    background:yellow !important;
    color: black!important;
}
.x-grid-td-yellow{
    background:yellow !important;
    color: black!important;
}
.x-grid-td-yellow a font{
    color: black !important;
}

tr.x-grid-Yellow .x-grid-td {
    background: #ffef7bcf ! important;
}

</style>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/lookmonitor/shelloutputfinished.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/shellinforuningieai.js"></script>
<!-- 暂时注释掉 -->
<%-- <script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/workFlowQuery/workFlowSnapshoot.js"></script> --%>
<%-- <script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/actmonitor/actmonitorSnapshoot.js"></script> --%>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/flowQuery/flowSnapshoot.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/home/<USER>"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/common/Tn5250inforuningieai.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/jobScheduling/snapshoot/snapshootIeaiWindow.js"></script>
<body id="hbody">
<div id="newHomeGrid" style="width: 100%;height: 100%"></div>
</body>
</html>