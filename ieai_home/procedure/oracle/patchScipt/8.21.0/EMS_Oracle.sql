
DECLARE
LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=52;
IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (52,52,''外管报送源'','''','''','''','''',0)';
EXECUTE IMMEDIATE LS_SQL;
COMMIT;
END IF;
END;
/

DECLARE
LS_SQL VARCHAR2(2000);
    LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUNXML_HIS' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_RUNXML_HIS(IID NUMBER(19) NOT NULL ,IRUNINSID NUMBER(19) ,TASK_XML LONG ,CONSTRAINT PK_IEAI_RUNXML_HIS PRIMARY KEY (IID))';
EXECUTE IMMEDIATE LS_SQL;
END IF;
END;
/