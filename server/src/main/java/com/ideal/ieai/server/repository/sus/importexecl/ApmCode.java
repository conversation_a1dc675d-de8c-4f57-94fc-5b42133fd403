package com.ideal.ieai.server.repository.sus.importexecl;

import java.io.Serializable;

/**
 * 
 * @ClassName:  ApmCode   
 * @Description: 应用维护码表Model 
 * @author: ming_li 
 * @date:   2019年4月2日 上午11:09:45   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
public class ApmCode implements Serializable
{

    private static final long serialVersionUID = 7025922259342299603L;

    private Long              iId;
    private Integer           apmType;
    private String            apmName;
    private String            iistrue;

    public Long getiId ()
    {
        return iId;
    }

    public void setiId ( Long iId )
    {
        this.iId = iId;
    }

    public Integer getApmType ()
    {
        return apmType;
    }

    public void setApmType ( Integer apmType )
    {
        this.apmType = apmType;
    }

    public String getApmName ()
    {
        return apmName;
    }

    public void setApmName ( String apmName )
    {
        this.apmName = apmName;
    }

    public String getIistrue ()
    {
        return iistrue;
    }

    public void setIistrue ( String iistrue )
    {
        this.iistrue = iistrue;
    }



}
