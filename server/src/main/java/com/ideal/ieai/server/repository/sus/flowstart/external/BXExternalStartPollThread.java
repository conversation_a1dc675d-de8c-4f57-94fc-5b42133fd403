package com.ideal.ieai.server.repository.sus.flowstart.external;

import java.net.UnknownHostException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.susreport.ReportManager;

/**
 * <ul>
 * <li>Title: BXExternalStartPollThread.java</li>
 * <li>Description:百信外部接口启动流程</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2018年8月30日
 */
public class BXExternalStartPollThread extends Thread
{
    private static final Logger _log       = Logger.getLogger(BXExternalStartPollThread.class);
    private boolean             flag   = false;//开门验证的开关
    private int sleepTime =1000*3; //5分钟
    private int cnt=0;
    List<Map<String,String>> items =new ArrayList<Map<String,String>>();
    public BXExternalStartPollThread()
    {
        flag = ServerEnv.getServerEnv().isSUSGraphExterDelayStart();
    }

    @Override
    public void run ()
    {
        while (flag)
        {
            try
            {
                Thread.sleep(sleepTime);
                items.addAll(getODItem()) ;
                while (items.size() > 0)
                {
                    Map<String, String> execAct = items.remove(0);
                    try
                    {
                        delayStart(execAct);
                    } catch (Exception e)
                    {
                        _log.error("位置1，"+e.toString());
                    }
                }
            } catch (Exception ex)
            {
                _log.error("位置2，"+ex.toString());
            }
        }

    }

    /**
     * <li>Description:获得准备执行的活动</li> 
     * <AUTHOR>
     * 2018年8月31日 
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return List<Map<String,String>>
     */
    private List<Map<String,String>> getODItem () throws ServerException, RepositoryException
    {
        String serverip = "";
        // ISTATE -1未运行，0运行 ，2完成
        try
        {
            serverip = Environment.getInstance().getServerIP();
        } catch (UnknownHostException e1)
        {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }

        return ExternalStartMgr.getInstance().getPreparedRun(serverip);
    }
    
    /**
     * <li>Description:延时启动_百信银行_外部接口启动的流程</li> 
     * <AUTHOR>
     * 2018年8月30日 
     * return void
     */
    public void delayStart(Map<String, String> map){
        List<Map<String, String>> datalist=new ArrayList<Map<String,String>>();
        datalist.add(map);
        boolean startRs=ExternalStartMgr.getInstance().doWord_START_SUS_PROCESS(datalist);
        if (startRs)
        {
            try
            {
                ExternalStartMgr.getInstance().updateFinished(Long.valueOf(map.get("IID")),Constants.IEAI_SUS);
            } catch (NumberFormatException e)
            {
                e.printStackTrace();
            } catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    
    
    /**
     * 
     * @Title: balancingIp
     * @Description: TODO(负载均衡服务器)
     * @param listserip
     * @param conn
     * @return
     * @return String 返回类型
     * @throws
     * @变更记录 2017年2月16日
     */
    public String balancingIp ( List listserip, Connection conn )
    {
        String ip = "";
        StringBuffer ids = new StringBuffer("(");

        for (int nb = 0; nb < listserip.size(); nb++)
        {
            int count = ReportManager.getInstance().getServerIpcount((String) listserip.get(nb), conn);
            if (count == 0)
            {
                ip = (String) listserip.get(nb);
                break;
            } else
            {
                ids.append("'" + listserip.get(nb) + "'").append(",");
            }
        }
        if (ip.equals(""))
        {
            String idStr = ids.toString();
            idStr = idStr.endsWith(",") ? (idStr.substring(0, idStr.length() - 1) + ")") : "";
            String ip1 = ReportManager.getInstance().getServerIp(idStr, conn);
            if (ip1.equals(""))
            {
                ip = (String) listserip.get(0);
            } else
            {
                ip = ip1;
            }
        }
        return ip;
    }
}
