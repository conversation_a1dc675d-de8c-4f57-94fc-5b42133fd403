\set SQLTERM /
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER_QUICK_MIDDLE' AND COLUMN_NAME='TYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_USER_QUICK_MIDDLE ADD TYPE NUMBER(19) DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUSI_MODULE' AND COLUMN_NAME='IEVENTORDER'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_BUSI_MODULE ADD IEVENTORDER NUMBER(3) ';
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BUSI_MODULE' AND COLUMN_NAME='ITITLE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_BUSI_MODULE ADD ITITLE VARCHAR2(300) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
