/**
 * Class IEAIRemoteproxy Used to provide XML-RPC service to IEAI Server
 */
package com.ideal.ieai.proxy.xmlrpc;

import EDU.oswego.cs.dl.util.concurrent.ConcurrentHashMap;
import EDU.oswego.cs.dl.util.concurrent.PooledExecutor;
import com.ideal.ieai.core.EntegorInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.IShellCmdActivity;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.ieai.proxy.agentinstall.AgentInstall;
import com.ideal.ieai.proxy.api.ProxyWSClient;
import com.ideal.ieai.proxy.engine.*;
import com.ideal.ieai.proxy.kafka.KafkaProxyLauncher;
import com.ideal.ieai.proxy.kafka.KafkaStarter;
import com.ideal.ieai.proxy.utils.JsonUtils;
import com.ideal.ieai.proxy.utils.ProxyThreadUtils;
import com.ideal.ieai.proxy.utils.ProxyUtil;
import com.ideal.ieai.proxy.utils.ProxyXmlRpcClient;
import com.ideal.ieai.proxy.webservice.check.AgentCheck;
import com.ideal.ieai.proxy.webservice.service.*;
import com.ideal.util.JsonUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.xmlrpc.WebServer;
import org.apache.xmlrpc.XmlRpc;
import org.apache.xmlrpc.XmlRpcClient;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

public class IEAIImpProxy
{
    private ProxyInfo                      _proxyInfo;
    private static Object                  _waitLock      = new Object();
    private final long                     shellSleepTime = 1000;
    private static String                  aptResult      = "";

    private ClassPathXmlApplicationContext context        = null;

    public synchronized ClassPathXmlApplicationContext getContext ()
    {
        if (context == null || !context.isActive())
        {
            startContext();
        }
        return context;
    }

    public long getShellSleepTime ()
    {
        return shellSleepTime;
    }

    public static final IEAIImpProxy getInstance ()
    {
        return _inst;
    }

    /**
     * Initialize IEAIRemoteproxy
     *
     * @edit lei_wang take out two thread pools , _sendStateDataThreadPool and
     *       _sendExecResultThreadPool take out three thread class, _checkActsListThread ,
     *       _checkSendResultThread.start() , _checkSendStatThread
     * @return
     */
    public void start ( String wsIp, int wsPort )
    {
        int port = 0;
        try
        {
            _log.info("iEAI Proxy Initializing...");
            initThreadPool();
            _xmlrpc_server = null;
            _proxyInfo = new ProxyInfo();
            port = ProxyEnv.getInstance().getIntConfig(ProxyEnv.PROXY_PORT, ProxyEnv.PROXY_PORT_DEFAULT);
            String rpcIp = "";
            InetAddress addr = null;
            try
            {
                rpcIp = ProxyEnv.getInstance().getProxyRpcIp();
                addr = InetAddress.getByName(rpcIp);
                _proxyInfo.host = rpcIp;
                _proxyInfo.wsIp = wsIp;
                _proxyInfo.wsPort = wsPort;
            } catch (UnknownHostException e)
            {
                _proxyInfo.host = "Unknown";
            }
            _log.info("iEAI Proxy Initializing..." + port);
            // initialize proxy info
            if ("true".equals(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_ENABLE)))
            {
                SecurityConfig securityCfg = new SecurityConfig();

                securityCfg.setSSLMode(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_MODE));
                securityCfg.setKeyStore(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_KEY_STORE));
                securityCfg.setKeyStorePassword(
                        ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_KEY_STORE_PASSWORD));
                securityCfg.setTrustStore(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_TRUST_STORE));
                securityCfg.setTrustStorePassword(
                        ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_TRUST_STORE_PASSWORD));
                if (SecurityConfig.not_verify_client.equals(securityCfg.getSSLMode())
                        && (StringUtils.isEmpty(securityCfg.getKeyStore())))
                {
                    _log.error("in mode " + SecurityConfig.not_verify_client + ", must config keystore.");
                    throw new IllegalStateException("SSL Server Init Failed.");
                }
                if (SecurityConfig.verify_client.equals(securityCfg.getSSLMode())
                        && (StringUtils.isEmpty(securityCfg.getTrustStore())
                        || StringUtils.isEmpty(securityCfg.getKeyStore())))
                {
                    _log.error("in mode " + SecurityConfig.verify_client + ", must config keystore and truststore.");
                    throw new IllegalStateException("Invalid SSL Config.");
                }
                try
                {
                    _xmlrpc_server = IEAISSLServer.getInstance(port, securityCfg);
                } catch (Exception e)
                {
                    _log.error(e.getMessage(), e);
                    throw new IllegalStateException("Invalid SSL Config.");
                }
            } else
            {
                _xmlrpc_server = new WebServer(port, addr);
            }

            _proxyInfo.port = port;
            _proxyInfo.starttime = new Date();

            // set thread pool to pool requests when pool is full
            _actExecThreadPool.waitWhenBlocked();
            _actExecThreadPool.setKeepAliveTime(2000);
            _xmlrpc_server.addHandler(IEAI_PROXY_NAME, this);

            setupCtrlCHandler();
            _log.info("iEAI Proxy start pub rpc.ip " + rpcIp + " !");
            _xmlrpc_server.start();
            _log.info("iEAI Proxy rpc pub sucessfully!");

            _checkActListThread.start();

            _saveActTime = ProxyEnv.getInstance().getIntConfig(ProxyEnv.SAVE_ID_HOUR, ProxyEnv.SAVE_ID_HOUR_DEF);
            _checkSaveActThread.start();

            if (this.getBrokerSwitch())
            {
                new KafkaStarter().startBroker();
            }
            if (this.getKafkaSwitch())
            {
                // 启动Kafka转发功能 前提是启动broker
                KafkaProxyLauncher.getInstance().start();
            }
            // 启动连接zk connect服务
            boolean zkconnect = ProxyEnv.getInstance().getBooleanConfigNew2(Environment.AGENT_ZOOKEEPER_CONNECT, false);
            if (zkconnect)
            {
                _log.info("iEAI Proxy start zookeeper...");
                startContext();
                _log.info("iEAI Proxy zookeeper sucessfully!");
            } else
            {
                _log.info("iEAI Proxy zookeeper.connect=false!");
            }
        } catch (Exception e)
        {
            StringBuilder message = new StringBuilder(e.getLocalizedMessage());
            // message.append(" and the start port is " + port);
            _log.info(message);
            System.exit(0);
        }

        _log.info("iEAI Proxy has been started sucessfully!");
        synchronized (_waitLock)
        {
            try
            {
                _waitLock.wait();
            } catch (InterruptedException e)
            {
            }
        }
        _log.info("iEAI proxy stopped!\n");
    }

    public void startMulPort ( String wsIp, int wsPort, int port )
    {
        try
        {
            _log.info("iEAI Proxy Initializing...");
            initThreadPool();
            _xmlrpc_server = null;
            _proxyInfo = new ProxyInfo();
            String rpcIp = "";
            InetAddress addr = null;
            try
            {
                rpcIp = ProxyEnv.getInstance().getProxyRpcIp();
                addr = InetAddress.getByName(rpcIp);
                _proxyInfo.host = rpcIp;
                _proxyInfo.wsIp = wsIp;
                _proxyInfo.wsPort = wsPort;
            } catch (UnknownHostException e)
            {
                _proxyInfo.host = "Unknown";
            }
            _log.info("iEAI Proxy Initializing..." + port);
            // initialize proxy info
            if ("true".equals(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_ENABLE)))
            {
                SecurityConfig securityCfg = new SecurityConfig();

                securityCfg.setSSLMode(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_MODE));
                securityCfg.setKeyStore(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_KEY_STORE));
                securityCfg.setKeyStorePassword(
                        ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_KEY_STORE_PASSWORD));
                securityCfg.setTrustStore(ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_TRUST_STORE));
                securityCfg.setTrustStorePassword(
                        ProxyEnv.getInstance().getSysConfig(ProxyEnv.PROXY_SSL_TRUST_STORE_PASSWORD));
                if (SecurityConfig.not_verify_client.equals(securityCfg.getSSLMode())
                        && (StringUtils.isEmpty(securityCfg.getKeyStore())))
                {
                    _log.error("in mode " + SecurityConfig.not_verify_client + ", must config keystore.");
                    throw new IllegalStateException("SSL Server Init Failed.");
                }
                if (SecurityConfig.verify_client.equals(securityCfg.getSSLMode())
                        && (StringUtils.isEmpty(securityCfg.getTrustStore())
                        || StringUtils.isEmpty(securityCfg.getKeyStore())))
                {
                    _log.error("in mode " + SecurityConfig.verify_client + ", must config keystore and truststore.");
                    throw new IllegalStateException("Invalid SSL Config.");
                }
                try
                {
                    _xmlrpc_server = IEAISSLServer.getInstance(port, securityCfg);
                } catch (Exception e)
                {
                    _log.error(e.getMessage(), e);
                    throw new IllegalStateException("Invalid SSL Config.");
                }
            } else
            {
                _xmlrpc_server = new WebServer(port, addr);
            }

            _proxyInfo.port = port;
            _proxyInfo.starttime = new Date();

            // set thread pool to pool requests when pool is full
            _actExecThreadPool.waitWhenBlocked();
            _actExecThreadPool.setKeepAliveTime(2000);
            _xmlrpc_server.addHandler(IEAI_PROXY_NAME, this);

            setupCtrlCHandler();
            _log.info("iEAI Proxy start pub rpc.ip " + rpcIp + " !");
            _xmlrpc_server.start();
            _log.info("iEAI Proxy rpc pub sucessfully!");

            _checkActListThread.start();

            _saveActTime = ProxyEnv.getInstance().getIntConfig(ProxyEnv.SAVE_ID_HOUR, ProxyEnv.SAVE_ID_HOUR_DEF);
            _checkSaveActThread.start();

            if (this.getBrokerSwitch())
            {
                new KafkaStarter().startBroker();
            }
            if (this.getKafkaSwitch())
            {
                KafkaProxyLauncher.getInstance().start();           // 启动Kafka转发功能 前提是启动broker
            }
            // 启动连接zk connect服务
            boolean zkconnect = ProxyEnv.getInstance().getBooleanConfigNew2(Environment.AGENT_ZOOKEEPER_CONNECT, false);
            if (zkconnect)
            {
                _log.info("iEAI Proxy start zookeeper...");
                startContext();
                _log.info("iEAI Proxy zookeeper sucessfully!");
            } else
            {
                _log.info("iEAI Proxy zookeeper.connect=false!");
            }
        } catch (Exception e)
        {
            StringBuilder message = new StringBuilder(e.getLocalizedMessage());
            // message.append(" and the start port is " + port);
            _log.info(message);
            System.exit(0);
        }

        _log.info("iEAI Proxy has been started sucessfully!");
        synchronized (_waitLock)
        {
            try
            {
                _waitLock.wait();
            } catch (InterruptedException e)
            {
            }
        }
        _log.info("iEAI proxy stopped!\n");
    }

    public void startContext ()
    {
        context = new ClassPathXmlApplicationContext(ProxyEnv.getProxyEnv().getConsumerConfigFileName());
    }

    /**
     * Shutdown proxy. If force is true, kill all thread pools and clean up request queue even
     * there抯 request in the queue. Otherwise, proxy will shutdown when no request is in the queue.
     * In the mean time, no new request will be accepted.
     *
     * @param force
     * @return SHUTDOWN_SUCCESS If proxy has been shutdown sucessfully. SHUTDOWN_ERROR If error
     *         occured while shutting down SHUTDOWN_BUSY Cannot shutdown proxy at this moment.
     *         Mostly because there's still pending reuqests
     */
    public int shutdown ( final boolean force )
    {
        // first, check if there's anything pending in request manager
        if (!force && _actExecRequestManager.getNumRequests() > 0)
            return RET_BUSY;

        _log.info("Shutdown signal is received.");
        removeCtrlCHandler();

        // need to shutdown two thread pools, request manager, XML-RPC server
        _actExecThreadPool.shutdownNow();

        _actExecRequestManager.destroy();

        if (null != _xmlrpc_server)
            _xmlrpc_server.shutdown();

        synchronized (_waitLock)
        {
            _waitLock.notifyAll();
        }
        _log.info("Shutting down current iEAI proxy...\n");
        return RET_SUCCESS;
    }

    public int setShellSleepTime ( String shellsleeptime )
    {
        ProxyEnv.getInstance().setSysConfig(ProxyEnv.PROXY_SHELL_SLEEP_TIME_KEY, shellsleeptime);
        return 0;
    }

    public Hashtable getProxyInfo ()
    {
        return _proxyInfo.toHashtable();
    }

    /**
     * Is called by entegor,to check proxy process 入口方法:proxy自检进程，检测自身进程所占cpu及内存
     *
     * @param ProjectName
     * @param proxyIp
     * @param proxyPort
     * @param serverHost
     * @param serverPort
     * @date 2011-3-24
     * <AUTHOR>
     * @return
     */
    public boolean checkAgent ( final String projectName, final String proxyIp, final int proxyPort,
                                final String serverHost, final int serverPort )
    {
        PROJECT = projectName;
        PROXYTIP = proxyIp;
        PROXYPORT = proxyPort;
        SERVERHOST = serverHost;
        SERVERPORT = serverPort;
        return true;
    }

    /**
     * <li>Description:proxy自检进程，检测自身进程所占cpu及内存(多线程查询proxy内存以及cpu信息) 监控消息返回sever端</li>
     *
     * <AUTHOR> Oct 28, 2013
     * @param ProjectName
     * @param proxyIp
     * @param proxyPort
     * @param serverHost
     * @param serverPort
     * @return return boolean
     */
    public int checkAgent ( final String proxyIp, final int proxyPort, final String serverHost, final int serverPort )
    {
        PROXYTIP = proxyIp;
        PROXYPORT = proxyPort;
        SERVERHOST = serverHost;
        SERVERPORT = serverPort;
        AgentCheck ac = new AgentCheck();
        ac.start();
        int runActNum = runActNum();
        return runActNum;
    }

    /**
     * <li>Description:获取在执行活动列表中活动数量以及重发活动列表中活动数量</li>
     *
     * <AUTHOR> Oct 29, 2013
     * @return return String
     */
    public int runActNum ()
    {
        int num = 0;
        num = _execActsList.size() + _reExecActList.size();
        return num;
    }

    public void addRequest ( ActivityExecRequest Req )
    {
        if (!_execActsList.containsKey(Req.id))
        {
            _execActsList.put(Req.id, Req);
        }
    }

    public void addRequest ( ProxyModel model )
    {
        if (!_execActsList.containsKey(model.getReuqestUUID()))
        {
            _execActsList.put(model.getReuqestUUID(), model);
        }
    }

    public void addSaveAct ( ActTime act )
    {
        _saveActMap.put(act.requestId, act);
    }

    public void addReExcActReq ( ActivityExecRequest Req )
    {
        _reExecActList.put(Req.id, Req);
    }

    public void removeExecActList ( String RequestID )
    {
        _execActsList.remove(RequestID);
    }

    public boolean removeExecActList ( String RequestID, Hashtable vo ) throws Exception
    {
        if (_execActsList.containsKey(RequestID))
        {
            _execActsList.remove(RequestID);
        }
        if (_reExecActList.containsKey(RequestID))
        {
            _reExecActList.remove(RequestID);
        }
        return ProxyUtil.getInstance().removeExecActList(RequestID, vo);
    }

    public void removeSaveAct ( String reqId )
    {
        _saveActMap.remove(reqId);
    }

    /*
     * <AUTHOR>
     */
    public boolean ifActInQueue ( String requestId )
    {
        boolean ifin = false;
        try
        {
            if (null != _execActsList.get(requestId) || null != _reExecActList.get(requestId))
                ifin = true;
        } catch (Exception e)
        {
            _log.info("This activity is not in queue!" + e);
            return false;
        }
        return ifin;
    }

    public int getExecActListSize ()
    {
        int count = _execActsList.size();
        return count;
    }

    public int getReExcListSize ()
    {
        int count = _reExecActList.size();
        return count;
    }

    public void removeReExecActList ( String RequestID )
    {
        if (null != RequestID)
            _reExecActList.remove(RequestID);
    }


    private class CheckActsListThread implements Runnable {
        boolean chkloop = true;

        public void run() {
            while (chkloop) {
                try {
                    boolean printJson = false;
                    try {
                        ReLoadProxyEnv rpe = new ReLoadProxyEnv();
                        long times = rpe.getIntConfig(ProxyEnv.PROXY_ACTLIST_TIME, 2000);
                        printJson = rpe.getBooleanProperties(ProxyEnv.PROXY_PRINT_JSONDATA_SWITCH, false);
                        Thread.sleep(times);
                    } catch (InterruptedException e) {
                        continue;
                    }
                    if (_execActsList.size() <= 0)
                        continue;
                    synchronized (_execActsList) {
                        _log.info("_actExecThreadPool-max:" + _actExecThreadPool.getMaximumPoolSize()
                                + ",_actExecThreadPool-use:" + _actExecThreadPool.getPoolSize()
                                + "<--_execActsList.size:" + _execActsList.size());
                        for (Iterator iter = _execActsList.values().iterator(); iter.hasNext(); ) {
                            try {
                                if (_actExecThreadPool.getMaximumPoolSize() > _actExecThreadPool.getPoolSize()) {
                                    ProxyModel model = (ProxyModel) iter.next();
                                    if (printJson) {
                                        _log.info("_actExecThreadPool json:" + model.getProxyJsonData());
                                    }
                                    _log.info("model:" + model.getReuqestUUID() + ".start");

                                    ProxyActivityExe executor = new ProxyActivityExe(model);
                                    try {
                                        _actExecThreadPool.execute(executor);
                                        _log.info("_actExecThreadPool model:" + model.getReuqestUUID() + ".ok");
                                        iter.remove();
                                        removeExecActList(model.getReuqestUUID());
                                        removeSaveAct(model.getReuqestUUID());
                                    } catch (Exception e) {
                                        _log.error("send catch model:" + model.getReuqestUUID(), e);
                                        continue;
                                    }
                                } else {
                                    Thread.sleep(2000);
                                }
                            } catch (Exception e) {
                                _log.error("CheckActsListThread client is error ", e);
                                throw e;
                            }
                        }
                    }
                } catch (Throwable a) {
                    _log.error("CheckActsListThread is error ", a);
                }
            }
        }
    }

    /*
     * get File Content by FilePath
     */
    public String getFileContent ( String filePath ) throws IOException
    {

        String fileContent = "";
        StringBuilder fileLines = new StringBuilder();
        try
        {
            FileReader fileReader = new FileReader(filePath);
            BufferedReader bufferedFileReader = new BufferedReader(fileReader);
            String line = null;
            while ((line = bufferedFileReader.readLine()) != null)
            {
                fileLines.append(line + "\n");
            }

            fileContent = fileLines.toString();
            if (fileContent == null)
                fileContent = "";
            bufferedFileReader.close();
            fileReader.close();
        } catch (IOException e)
        {
            fileContent = "";
        }
        return fileContent;
    }

    /**
     * Given a workflow id, tell proxy to stop or kill all activities related to the workflow
     *
     * @edit lei_wang
     * @param FlowId
     * @param Force
     * @return
     */
    public boolean stopWorkflow ( final String FlowId, Hashtable vo ) throws Exception
    {
        try
        {
            toStopActFromReActList(new Long(FlowId).longValue());
            ActivityExecRequestManager.getInstance().removeRequestByWorkflow(Long.parseLong(FlowId));
            removeRequestByWorkflow(Long.parseLong(FlowId));
            ProxyUtil.getInstance().stopWorkflow(FlowId, vo);
            return true;
        } catch (NumberFormatException e)
        {
            _log.error("Bad workflow id:" + FlowId, e);
            return false;
        }
    }

    /*
     * <AUTHOR> To stop activity is running in thread pool , remove it in execute queue and
     * delay queue
     */
    public void toStopActFromReActList ( long FlowId )
    {
        // remove activity in delay queue
        for (Iterator iter = _reExecActList.keySet().iterator(); iter.hasNext();)
        {
            ActivityExecRequest req = (ActivityExecRequest) _reExecActList.get(iter.next());
            if (req.flowId == FlowId)
            {
                if (req.actInst instanceof IShellCmdActivity)
                {
                    IShellCmdActivity shellcmdInstance = getShellCmdInstance(req.id);
                    if (null != shellcmdInstance)
                        shellcmdInstance.stopProcess();
                }
                req.executorThread.destroy();
                req.executorThread = null;
                removeReExecActList(req.id);
                removeExecActList(req.id);
            }
        }
        // remove activity in execute queue
        for (Iterator iter = _execActsList.keySet().iterator(); iter.hasNext();)
        {
            ActivityExecRequest req = (ActivityExecRequest) _execActsList.get(iter.next());
            if (req.flowId == FlowId && req.actInst instanceof IShellCmdActivity)
            {
                if (req.actInst instanceof IShellCmdActivity)
                {
                    IShellCmdActivity shellcmdInstance = getShellCmdInstance(req.id);
                    if (null != shellcmdInstance)
                        shellcmdInstance.stopProcess();
                }
                req.executorThread.destroy();
                req.executorThread = null;
                removeReExecActList(req.id);
                removeExecActList(req.id);
            }
        }
    }

    public void removeRequestByWorkflow ( long FlowId )
    {
        synchronized (_execActsList)
        {
            for (Iterator iter = _execActsList.keySet().iterator(); iter.hasNext();)
            {
                ActivityExecRequest req = (ActivityExecRequest) _execActsList.get(iter.next());
                if (req.flowId == FlowId)
                {
                    removeExecActList(req.id);
                    req.stopped = true;
                }
            }// end of for
        }
    }

    /**
     * Notify proxy that a workflow has stopped. proxy will need to clean up resources and
     * activities. This is only a notification. The workflow is already stopped before the
     * notification is called. So if engine needs to call stopActivity, it is already called. All
     * the leftover activity requests are considered orphan requests and can be removed safely.
     *
     * @edit lei_wang
     * @param WorkflowId Long workflow id in string format
     * @return: must return whether be successful.
     */
    public boolean workflowStopped ( final String WorkflowId, Hashtable vo ) throws Exception
    {
        try
        {
            toStopActFromReActList(new Long(WorkflowId).longValue());
            ActivityExecRequestManager.getInstance().removeRequestByWorkflow(Long.parseLong(WorkflowId));
            removeRequestByWorkflow(Long.parseLong(WorkflowId));
            ProxyUtil.getInstance().stopWorkflow(WorkflowId, vo);
            return true;
        } catch (NumberFormatException e)
        {
            _log.error("Bad workflow id:" + WorkflowId, e);
            return false;
        }

    }

    public PooledExecutor getActExecThreadPool ()
    {
        return _actExecThreadPool;
    }

    /**
     * setup showdown ctrl when system shutting down,thread registed will be executed
     */
    private void setupCtrlCHandler ()
    {
        ctrlcHandler = new Thread()
        {
            public void run ()
            {
                shutdown(true);
                System.exit(0);
            }
        };
        Runtime.getRuntime().addShutdownHook(ctrlcHandler);
    }

    private void removeCtrlCHandler ()
    {
        if (null != ctrlcHandler)
            Runtime.getRuntime().removeShutdownHook(ctrlcHandler);
    }

    private int getThreadPoolCfg ( String key, int defaultValue )
    {
        String cfg = ProxyEnv.getInstance().getSysConfig(key, String.valueOf(defaultValue));
        try
        {
            int intValue = Integer.parseInt(cfg);
            return intValue > 0 ? intValue : defaultValue;
        } catch (NumberFormatException ex)
        {
            return defaultValue;
        }
    }

    private void initThreadPool ()
    {
        int xmlRpcMaxThreads = getThreadPoolCfg(ProxyEnv.PROXY_XMLRPC_MAXTHREADS, 500);
        int actExecThreadPoolMax = getThreadPoolCfg(ProxyEnv.PROXY_ACTEXEC_THREADPOOL_MAX, 300);
        XmlRpc.setMaxThreads(xmlRpcMaxThreads);
        _actExecThreadPool = new PooledExecutor(actExecThreadPoolMax);
        _proxyMaxNum = actExecThreadPoolMax;
        _actThreadPool = new PooledExecutor(5);
    }

    public boolean setAgentMaxnum ( final String num )
    {
        if (Integer.parseInt(num) > _actExecThreadPool.getMaximumPoolSize())
            _proxyMaxNum = _actExecThreadPool.getMaximumPoolSize();
        else
            _proxyMaxNum = Integer.parseInt(num);
        _log.info("setproxyMaxnum: " + _proxyMaxNum + ";num: " + num);
        return true;
    }

    /**
     * 向指定的server发送启动时的proxy版本
     *
     *
     * @param serverIp
     * @param serverPort
     */
    public void sendVersion ( final String serverIp, final String serverPort, Hashtable vo )
    {
        for (int i = 0; i < 10; i++)
        {
            ProxyWSClient wsclient = ProxyOpServiceManager.getInstance().getProxyService(serverIp,
                    Integer.parseInt(serverPort));

            String proxyIP = "";
            try
            {
                proxyIP = ProxyEnv.getInstance().getAgentIP();
            } catch (UnknownHostException e2)
            {
                _log.error("获取proxyip 出错");
                continue;
            }

            int port = ProxyEnv.getInstance().getIntConfig(ProxyEnv.PROXY_PORT, ProxyEnv.PROXY_PORT_DEFAULT);

            EntegorInfo info = new EntegorInfo();
            info.getAgentVersion();

            try
            {
                // wsclient.updateAgentAutoStatus(proxyIP, port, info.getAgentVersion());
                break;
            } catch (Exception e)
            {
                if (i == 9)
                {

                    _log.error("sendVersion to Entegor server: " + serverIp + " fail", e);
                }
                try
                {
                    Thread.sleep(60000);
                } catch (InterruptedException e1)
                {
                    e1.printStackTrace();
                }
            }
        }
    }

    private IShellCmdActivity getShellCmdInstance ( String requestId )
    {
        if (!_execActsList.containsKey(requestId))
        {
            return null;
        }
        ActivityExecRequest request = (ActivityExecRequest) _execActsList.get(requestId);
        if (null == request)
        {
            return null;
        }

        IShellCmdActivity instance = null;

        try
        {
            instance = (IShellCmdActivity) request.actInst;
        } catch (Throwable a)
        {
            _log.error(a);
        }

        if (null == instance)
        {
            removeExecActList(requestId);
            return null;
        }
        return instance;
    }

    class CheckSaveActListThread implements Runnable
    {
        boolean loop = true;
        public void run ()
        {
            while (loop)
            {
                try
                {
                    // wait for one minutes
                    Thread.sleep(7 * 1000);
                } catch (InterruptedException e)
                {
                    continue;
                }
                if (_saveActMap.size() <= 0)
                    continue;
                long current = System.currentTimeMillis();
                for (Iterator iter = _saveActMap.values().iterator(); iter.hasNext();)
                {
                    ActTime act = (ActTime) iter.next();

                    if (ifActInQueue(act.requestId))
                    {
                        continue;
                    }
                    if (current - act.startTime > _saveActTime * 60 * 60 * 1000)
                    {
                        String id = act.requestId;
                        removeSaveAct(act.requestId);
                        removeExecActList(act.requestId);
                        _log.info("remove the request Id" + id);
                    }
                }
            }
        }
    }

    public PooledExecutor                    _actThreadPool         = null;

    private Thread                           ctrlcHandler           = null;

    static final int                         RET_SUCCESS            = 0;
    static final int                         RET_ERROR              = -1;
    static final int                         RET_BUSY               = -2;
    static final int                         RET_REQ_NOT_FOUND      = -3;

    public static final String               IEAI_PROXY_NAME        = "IEAIAgent";
    private WebServer                        _xmlrpc_server;
    private static final IEAIImpProxy        _inst                  = new IEAIImpProxy();

    // maximum 10 threads to run requests
    private PooledExecutor                   _actExecThreadPool     = null;

    private final ActivityExecRequestManager _actExecRequestManager = ActivityExecRequestManager.getInstance();

    private static final Logger              _log                   = Logger.getLogger(IEAIImpProxy.class);

    // 存储server发送过来的活动信息，用来判断是否将活动放入执行线程中去
    private static Map                       _execActsList          = new ConcurrentHashMap();// new
    // 存储出现异常或延时的活动
    private static Map                       _reExecActList         = new ConcurrentHashMap();
    // 初始化监控连接池线程
    private Map                              _flowPoolNum           = new Hashtable();

    private long                             _proxyMaxNum           = 0;

    private static Map                       _saveActMap            = new ConcurrentHashMap();

    private Thread                           _checkSaveActThread    = new Thread(new CheckSaveActListThread(),
            "CheckSaveActListThread");
    private Thread                           _checkActListThread    = new Thread(new CheckActsListThread(),
            "CheckActsListThread");
    private long                             _saveActTime           = 0;

    // 用于proxy监控的参数设置 开始
    public static String                     PROJECT                = "";
    public static String                     PROXYTIP               = "";
    public static int                        PROXYPORT              = 15000;
    public static String                     SERVERHOST             = "";
    public static int                        SERVERPORT             = 8888;
    // 结束

    public void initLog ()
    {
        /*
         * Layout layout = new PatternLayout("%d{ISO8601} %-5p %c{2}: %m%n");
         *
         * ConsoleAppender consoleAppendar = new ConsoleAppender(layout, "System.out");
         * consoleAppendar.setLayout(layout);
         * RollingFileAppender fileAppendar = null;
         *
         * try
         * {
         * fileAppendar = new RollingFileAppender(layout, ((ProxyEnv)
         * ProxyEnv.getInstance()).getLogFileName(), true);
         * fileAppendar.setMaximumFileSize(
         * (ProxyEnv.getInstance().getIntConfig(ProxyEnv.AGENT_SYSLOG_FILESIZE, 10) * 1024 * 1024) -
         * 1);
         * fileAppendar
         * .setMaxBackupIndex(ProxyEnv.getInstance().getIntConfig(ProxyEnv.
         * AGENT_SYSLOG_MAXBACKUPINDEX,
         * 10));
         * } catch (IOException e)
         * {
         * // no code.
         * }
         *
         * Logger rootLogger = Logger.getRootLogger();
         *
         * // remove configuration set before
         * LogManager.resetConfiguration();
         *
         * // set the root log to error and to file only
         * rootLogger.setLevel(Level.ERROR);
         * if (fileAppendar != null)
         * {
         * rootLogger.addAppender(fileAppendar);
         * }
         *
         * set the level of loggers in code generated by ideal to logLevel and log to console if
         * it's set
         *
         * Logger idealLogger = Logger.getLogger("com.ideal");
         * idealLogger.setLevel(Level.toLevel("DEBUG", Level.INFO));
         * if (consoleAppendar != null)
         * {
         * idealLogger.addAppender(consoleAppendar);
         * }
         */

    }

    public String taskOperation ( String methodname, String token, String postString, Hashtable hashtable )
            throws Exception
    {

        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        _log.info(method + " INPA1: " + methodname + " INPA2:" + token + " INPA3:" + postString);
        String backMessage = "";
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        try
        {
            // CONN_COMMON = 0;
            // SSL_CONN_NO_CERTIFICATE = 1;
            // SSL_CONN_WITH_CERTIFICATE = 2;
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(methodname);
            params.addElement(token);
            params.addElement(postString);
            hashtable.put("level", level);
            hashtable.put("proxyJson", proxyJson);
            hashtable.put("isProxy", isProxy);
            hashtable.put("serverHost", server_ip);
            hashtable.put("serverPort", server_port);
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.taskOperation", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info(method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }

    public String hcOperation ( String methodname, String token, String postString, Hashtable hashtable )
            throws Exception
    {

        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        _log.info(
                "HC proxy:----method=" + method + " INPA1: " + methodname + " INPA2:" + token + " INPA3:" + postString);
        String backMessage = "";
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        try
        {
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.hcOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(methodname);
            params.addElement(token);
            params.addElement(postString);
            hashtable.put("level", level);
            hashtable.put("proxyJson", proxyJson);
            hashtable.put("isProxy", isProxy);
            hashtable.put("serverHost", server_ip);
            hashtable.put("serverPort", server_port);
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.hcOperation", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info("HC proxy:----method=" + method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }

    // ******************************************************************************************************************//
    /**
     * Start a new execution request to proxy. proxy will create a new ActivityExecRequest object
     * and save into ActivityExecRequests queue It will also create an ActivityExecutor and assign
     * the request to it. The new executor will be put into _actExecThreadPool.
     *
     * @edit lei_wang add Hashtable parameter for request
     * @param RequestID
     * @return
     * @throws Exception
     */
    public boolean executeAct ( final String RequestID, final String ServerIP, final int ServerPort,
                                Hashtable hashtable ) throws Exception
    {
        executeAct(RequestID, ServerIP, ServerPort, ActivityExecutor.EXECUTE, hashtable);
        return true;
    }

    public String agentinstall ( Hashtable hashtable ) throws Exception
    {
        String backMessage = "";
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        String proxyString = String.valueOf(hashtable.get("proxyString"));
        proxyString = proxyString + server_ip + ":" + server_port + ":true;";
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        if (isProxy)
        {
            try
            {
                int ssltype=ActivityElement.CONN_COMMON;
                if (map.containsKey("ssltype")){
                    ssltype = Integer.parseInt(map.get("ssltype").toString());
                }
                client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
                // client = new XmlRpcClient(next_ip, next_port);
                Vector params = new Vector();
                hashtable.put("level", level);
                hashtable.put("proxyJson", proxyJson);
                hashtable.put("isProxy", isProxy);
                hashtable.put("serverHost", server_ip);
                hashtable.put("serverPort", server_port);
                hashtable.put("proxyString", proxyString);
                params.addElement(hashtable);
                backMessage = (String) client.execute("IEAIAgent.agentinstall", params);
                _log.info(method + " ip:" + next_ip + " port:" + next_port + ".ok");
            } catch (Exception e)
            {
                e.printStackTrace();
                _log.info(method + " ip:" + next_ip + " port:" + next_port + ".xml" + e);
                throw e;
            }
        } else
        {
            AgentInstall ai = new AgentInstall();
            Map installMap = ai.install(hashtable, proxyString);
            backMessage = JSONObject.fromObject(installMap).toString();

        }
        return backMessage;
    }

    private void reSetAct ( String requestID, String serverIP, int serverPort, int execute, Hashtable vo )
            throws Exception
    {
        boolean isProxy = false;
        String ip = "";
        int port = 0;
        int level = 0;
        String proxyJson = "";
        if (null != vo && vo.containsKey("proxyJson"))
        {
            proxyJson = String.valueOf(vo.get("proxyJson"));
            level = Integer.parseInt(String.valueOf(vo.get("level")));
            Map map = JsonUtils.getMessage(level, proxyJson, serverIP, 0);
            serverIP = String.valueOf(map.get("serverIp"));
            serverPort = Integer.parseInt(String.valueOf(map.get("serverPort")));
        }
        if (_saveActMap.containsKey(requestID))
        {
            if (ifActInQueue(requestID))
            {
                // 重定向
                while (true)
                {
                    if (_execActsList.containsKey(requestID))
                    {
                        if (_execActsList.get(requestID) instanceof ProxyModel)
                        {
                            ProxyModel model = (ProxyModel) _execActsList.get(requestID);
                            if (null != vo && vo.containsKey("proxyJson")
                                    && !String.valueOf(vo.get("proxyJson")).isEmpty())
                            {
                                String str = String.valueOf(vo.get("proxyJson"));
                                JSONObject jsonObj = JSONObject.fromObject(str);
                                String new_serverips = String.valueOf(jsonObj.get("ip"));
                                int new_serverPort = Integer.parseInt(String.valueOf(jsonObj.get("serverPort")));
                                int new_port = Integer.parseInt(String.valueOf(jsonObj.get("port")));
                                String oldjson = model.getProxyJsonData();
                                JSONObject oldjsonObj = JSONObject.fromObject(oldjson);
                                oldjsonObj.remove("ip");
                                oldjsonObj.remove("port");
                                oldjsonObj.remove("serverPort");
                                oldjsonObj.put("ip", new_serverips);
                                oldjsonObj.put("port", new_port);
                                oldjsonObj.put("serverPort", new_serverPort);
                                model.setProxyJsonData(oldjsonObj.toString());
                            }
                        } else
                        {
                            ActivityExecRequest er = (ActivityExecRequest) _execActsList.get(requestID);
                            if (null != vo && vo.containsKey("proxyJson")
                                    && !String.valueOf(vo.get("proxyJson")).isEmpty())
                            {
                                String str = String.valueOf(vo.get("proxyJson"));
                                JSONObject jsonObj = JSONObject.fromObject(str);
                                String new_serverips = String.valueOf(jsonObj.get("ip"));
                                int new_serverPort = Integer.parseInt(String.valueOf(jsonObj.get("serverPort")));
                                int new_port = Integer.parseInt(String.valueOf(jsonObj.get("port")));
                                String json = er.proxyJson;
                                if (null != json && !json.isEmpty())
                                {
                                    JSONObject oldjsonObj = JSONObject.fromObject(json);
                                    oldjsonObj.remove("ip");
                                    oldjsonObj.remove("port");
                                    oldjsonObj.remove("serverPort");
                                    oldjsonObj.put("ip", new_serverips);
                                    oldjsonObj.put("port", new_port);
                                    oldjsonObj.put("serverPort", new_serverPort);
                                    er.proxyJson = oldjsonObj.toString();
                                }
                            }
                            er.setServerHost(serverIP);
                            er.setServerPort(serverPort);
                        }
                        break;
                    }
                    if (_reExecActList.containsKey(requestID))
                    {
                        if (_execActsList.get(requestID) instanceof ProxyModel)
                        {
                            ProxyModel model = (ProxyModel) _execActsList.get(requestID);
                            String str = String.valueOf(vo.get("proxyJson"));
                            JSONObject jsonObj = JSONObject.fromObject(str);
                            String new_serverips = String.valueOf(jsonObj.get("ip"));
                            int new_serverPort = Integer.parseInt(String.valueOf(jsonObj.get("serverPort")));
                            int new_port = Integer.parseInt(String.valueOf(jsonObj.get("port")));
                            String oldjson = model.getProxyJsonData();
                            JSONObject oldjsonObj = JSONObject.fromObject(oldjson);
                            oldjsonObj.remove("ip");
                            oldjsonObj.remove("port");
                            oldjsonObj.remove("serverPort");
                            oldjsonObj.put("ip", new_serverips);
                            oldjsonObj.put("port", new_port);
                            oldjsonObj.put("serverPort", new_serverPort);
                            model.setProxyJsonData(oldjsonObj.toString());
                        } else
                        {
                            ActivityExecRequest er = (ActivityExecRequest) _reExecActList.get(requestID);
                            if (null != vo && vo.containsKey("proxyJson")
                                    && !String.valueOf(vo.get("proxyJson")).isEmpty())
                            {
                                String str = String.valueOf(vo.get("proxyJson"));
                                JSONObject jsonObj = JSONObject.fromObject(str);
                                String new_serverips = String.valueOf(jsonObj.get("ip"));
                                int new_serverPort = Integer.parseInt(String.valueOf(jsonObj.get("serverPort")));
                                int new_port = Integer.parseInt(String.valueOf(jsonObj.get("port")));
                                String json = er.proxyJson;
                                if (null != json && !json.isEmpty())
                                {
                                    JSONObject oldjsonObj = JSONObject.fromObject(json);
                                    oldjsonObj.remove("ip");
                                    oldjsonObj.remove("port");
                                    oldjsonObj.remove("serverPort");
                                    oldjsonObj.put("ip", new_serverips);
                                    oldjsonObj.put("port", new_port);
                                    oldjsonObj.put("serverPort", new_serverPort);
                                    er.proxyJson = oldjsonObj.toString();
                                }
                            }
                            er.setServerHost(serverIP);
                            er.setServerPort(serverPort);
                        }
                        break;
                    }
                }
            }
        }
        executeActReset(requestID, serverIP, serverPort, execute, vo);
    }

    public boolean compensateAct ( final String requestId, final String serverIp, final int serverPort,
                                   Hashtable hashtable ) throws Exception
    {
        executeAct(requestId, serverIp, serverPort, ActivityExecutor.COMPENSATE, hashtable);
        return true;
    }

    public String optAgent ( String opt, Hashtable hashtable ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        _log.info(method + " INPA1: " + opt);
        String backMessage = "";
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        try
        {
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(opt);
            hashtable.put("level", level);
            hashtable.put("proxyJson", proxyJson);
            hashtable.put("isProxy", isProxy);
            hashtable.put("serverHost", server_ip);
            hashtable.put("serverPort", server_port);
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.optAgent", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info(method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }

    /*
     * @Edit lei_wang add parameter RemoteActivityExecRequest(Hashtable)
     */
    private void executeAct ( final String RequestID, final String ServerIP, final int ServerPort, int executeMethod,
                              Hashtable vo ) throws Exception
    {
        ProxyUtil.getInstance().sendXmlRpc(RequestID, ServerIP, ServerPort, executeMethod, vo, "IEAIAgent.executeAct");
    }

    private void executeActReset ( final String RequestID, final String ServerIP, final int ServerPort,
                                   int executeMethod, Hashtable vo ) throws Exception
    {
        ProxyUtil.getInstance().sendXmlRpcReset(RequestID, ServerIP, ServerPort, executeMethod, vo,
                "IEAIAgent.reSetAct");
    }

    public void updateRequestStatus ( ProxyModel model, ActTime act )
    {
        addSaveAct(act);
        addRequest(model);
    }

    public String getComputerName ( Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getComputerName(vo);
    }

    public String getShellCmdOutput ( String requestId, int stdErrStartLine, int stdOutStartLine, int maxLines,
                                      int countOfLastLineForStdErr, int countOfLastLineForStd, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getShellCmdOutput(requestId, stdErrStartLine, stdOutStartLine, maxLines,
                countOfLastLineForStdErr, countOfLastLineForStd, vo);
    }

    public String checkAgentRe ( final String proxyIp, final int proxyPort, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().checkAgentRe(proxyIp, proxyPort, vo);
    }

    public String checkAgentApt ( final String proxyIp, final int proxyPort, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().checkAgentApt(proxyIp, proxyPort, vo);
    }

    public boolean createKeyFile ( final String keyContext, final String filepath, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().createKeyFile(keyContext, filepath, vo);
    }

    public boolean updateFlowPoolNum ( final String key, final String value, Hashtable vo ) throws Exception
    {
        synchronized (_flowPoolNum)
        {
            _flowPoolNum.put(key, value);
            ProxyUtil.getInstance().updateFlowPoolNum(key, value, vo);
            return true;
        }
    }

    public boolean reExecuteAct ( final String requestId, final String serverIp, final int serverPort,
                                  Hashtable hashtable ) throws Exception
    {
        executeAct(requestId, serverIp, serverPort, ActivityExecutor.REDO, hashtable);
        return true;
    }

    public boolean reSetAct ( final String requestID, final String serverIP, final int serverPort, Hashtable hashtable )
            throws Exception
    {
        reSetAct(requestID, serverIP, serverPort, ActivityExecutor.EXECUTE, hashtable);
        return true;
    }

    /**
     * 仅返回指定最后行数的控制台输出
     *
     * @param requestId
     * @param line
     * @return
     */
    public String getShellCmdOutputNLine ( String requestId, int line, Hashtable vo )
    {
        String xml = null;
        try
        {
            xml = ProxyUtil.getInstance().getShellCmdOutputNLine(requestId, line, vo);
        } catch (Exception ex)
        {
            _log.error("getShellCmdOutputNLine is err");
        }
        return xml;
    }

    /**
     * 獲取agent狀態
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年6月5日
     * @param vo
     * @return
     * @throws Exception
     * return String
     */
    public String getVersion ( Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getVersion(vo);
    }

    /**
     * 单层获取proxy状态
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年6月5日
     * @param vo
     * @return
     * @throws Exception
     * return String
     */
    public String getProxyVersion ( Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getProxyVersion(vo);
    }

    /**
     * 单层获取proxy发布的webservice状态
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年6月5日
     * @param proxyIp
     * @param port
     * @param vo
     * @return
     * @throws Exception
     * return String
     */
    public String getProxyWebS ( String proxyIp, int port, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getProxyWebS(proxyIp, port, vo);
    }

    public boolean stopShellCmdProcess ( final String RequestID, Hashtable vo ) throws Exception
    {
        if (_execActsList.containsKey(RequestID))
        {
            removeExecActList(RequestID, vo);
        }
        ProxyUtil.getInstance().stopShellCmdProcess(RequestID, vo);
        _log.info("proxy forcing kill task. RequestID: " + RequestID);
        return true;
    }

    public Object executeActSyn ( String RequestID, String ServerIP, int ServerPort, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().executeActSyn(RequestID, ServerIP, ServerPort, vo);
    }

    public boolean sendScriptToAgents ( String sendPath, String userPermission, String groupPermission,
                                        String parameterPermission, String agentOsType, Vector<Hashtable> arl, Hashtable hashtable )
            throws Exception
    {
        return ProxyUtil.getInstance().sendScriptToAgents(sendPath, userPermission, groupPermission,
                parameterPermission, agentOsType, arl, hashtable);
    }

    public Hashtable getAgentInfo ( Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getAgentInfo(vo);
    }

    public boolean checkSysState ( final String SysId, final String ComputerIp, final int AgentPort,
                                   final String FileName, final String SysParam, final String ServerIp, final int ServerPort,
                                   final String AgentIp, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().checkSysState(SysId, ComputerIp, AgentPort, FileName, SysParam, ServerIp,
                ServerPort, AgentIp, vo);
    }

    /**
     * Input some command line for the sub process when it need interact.
     *
     * @param requestId:activity's unique ID at remote proxy.
     * @param input:command line for interacting with sub process.
     * @throws ActivityException
     */
    public boolean inputForShellCmd ( String requestId, String input, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().inputForShellCmd(requestId, input, vo);
    }

    public boolean updateAgents ( final String serverIp, final int serverPort, final int isConsole,
                                  Hashtable renewTable, Hashtable otherServer ) throws Exception
    {
        return ProxyUtil.getInstance().updateAgents(serverIp, serverPort, isConsole, renewTable, otherServer);
    }

    public byte[] getAgentLogByName ( String fileName, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getAgentLogByName(fileName, vo);
    }

    public Hashtable getAgentLog ( String agentip, int port, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getAgentLog(agentip, port, vo);
    }

    public String getTN5250Screen ( String requestId, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getTN5250Screen(requestId, vo);
    }

    public String Tn5250Send ( String requestId, String cmd, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().Tn5250Send(requestId, cmd, vo);
    }

    private boolean getKafkaSwitch ()
    {
        ConfigReader cr = new ConfigReader();
        return cr.getBooleanProperties(ProxyEnv.KAFKA_PROXY_PRODUCER_CONSUMER_SWITCH, false);
    }

    private boolean getBrokerSwitch ()
    {
        ConfigReader cr = new ConfigReader();
        return cr.getBooleanProperties(ProxyEnv.KAFKA_PROXY_BROKER_SWITCH, false);
    }

    public String startKafkaConsumer (final String parmsInfo)
    {
        String backMsg = "";
        //ProxyServer consumer 增加一个参数，为空时是正常启动，stop 时是关闭消费者线程，begin 时时从头消费，end 时是从末尾消费 jiaMing 2023-2-3 start
        Map<String, String> configs = KafkaProxyLauncher.getInstance().getConfigs();
        configs.put("consumerParms",parmsInfo);
        //ProxyServer consumer 增加一个参数，为空时是正常启动，stop 时是关闭消费者线程，begin 时时从头消费，end 时是从末尾消费 jiaMing 2023-2-3 end
        try
        {
            if ("stop".equals(parmsInfo))
            {
                _log.info("stop consumer!-->parmsInfo:" + parmsInfo);
                // 停止消费者
                KafkaProxyLauncher.getInstance().stop();
            } else
            {
                _log.info("start consumer!-->parmsInfo:" + parmsInfo);
                // 启动消费者
                KafkaProxyLauncher.getInstance().start();
            }
            backMsg = " success!";
        } catch (Exception e)
        {
            _log.error("failed start kafka consumer :", e);
            backMsg = " failed";
        }
        return backMsg;
    }

    /**
     * jiaMing 2023-4-20
     * 启动 monitor-topic 主题 kafka消费者线程
     * @return
     */
    public  String startMonitorKafkaConsumer(final String parmsInfo){
        String backMsg = "";
        Map<String, String> configs = KafkaProxyLauncher.getInstance().getConfigs();
        configs.put("monitorParms",parmsInfo);
        try
        {
            _log.info("start  monitor consumer!");
            KafkaProxyLauncher.getInstance().startMonitor();           // 启动 monitor-topic 消费者
            backMsg = " success!";
        } catch (Exception e)
        {
            _log.error("failed start monitor kafka consumer : ", e);
            backMsg = " failed";
        }
        return backMsg;
    }

    public String getProxyConfigsByXmlrpc ( final String RequestID, final String ServerIP, final int ServerPort,
                                            Hashtable hashtable ) throws Exception
    {
        String props = ProxyUtil.getInstance().getProxyConfigurations();
        _log.info("get proxy configs success");
        return props;
    }

    public String getInactiveInfo ( String agentHost, String serverHost, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getInactiveInfo(agentHost, serverHost, vo);
    }

    public String getProxyInfo ( Hashtable vo )
    {
        Hashtable ha = _proxyInfo.toHashtable();
        String ver = "";
        try
        {
            ver = this.getProxyVersion(vo);
        } catch (Exception e)
        {
            _log.error("getProxyInfo is error", e);
        }
        ha.put("version", ver);
        return JsonUtil.getJsonByMapModel(ha);
    }

    /**
     * 停启Agent
     * @param methodName 参数值
     * @param hashtable 参数值
     * @return 返回值
     * @throws Exception 异常
     */
    public String startStopAgent ( String methodName, Hashtable hashtable ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        _log.info(method + " INPA1: " + methodName);
        String backMessage = "";
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        try
        {
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(methodName);
            hashtable.put("level", level);
            hashtable.put("proxyJson", proxyJson);
            hashtable.put("isProxy", isProxy);
            hashtable.put("serverHost", server_ip);
            hashtable.put("serverPort", server_port);
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.startStopAgent", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info(method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }

    /**
     * 停启守护进程
     * @param opt 参数值
     * @param hashtable 参数值
     * @return 返回值
     * @throws Exception 异常
     */
    public String startStopDaemons ( String opt, Hashtable hashtable ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        _log.info(method + " INPA1: " + opt);
        String backMessage = "";
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        try
        {
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(opt);
            hashtable.put("level", level);
            hashtable.put("proxyJson", proxyJson);
            hashtable.put("isProxy", isProxy);
            hashtable.put("serverHost", server_ip);
            hashtable.put("serverPort", server_port);
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.startStopDaemons", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info(method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }
    /**
     * 守护线程调用proxy
     * @param hashtable
     * @return 返回获取守护进程状态
     * @throws Exception
     */
    public String pollingThread (Hashtable hashtable )
            throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String backMessage = "";
        XmlRpcClient client = null;
        String next_ip = null;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getDawmonsXmlRpcMap(hashtable);
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        try
        {
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.pollingThread", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info(method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }
    /**
     * 守护线程调用proxy
     * @param methodname
     * @param token
     * @param postString
     * @param hashtable
     * @return
     * @throws Exception
     */
    public String taskAgentDeploy ( String methodname, String token, String postString, Hashtable hashtable )
            throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        _log.info(method + " INPA1: " + methodname + " INPA2:" + token + " INPA3:" + postString);
        String backMessage = "";
        XmlRpcClient client = null;
        String proxyJson = null;
        String next_ip = null;
        String server_ip = null;
        int server_port = 0;
        int next_port = 0;
        int level = 0;
        Map map = ProxyUtil.getInstance().getDawmonsXmlRpcMap(hashtable);
        proxyJson = String.valueOf(map.get("proxyJson"));
        next_ip = map.get("next_ip").toString();
        next_port = Integer.parseInt(map.get("next_port").toString());
        server_ip = map.get("serverIp").toString();
        server_port = Integer.parseInt(map.get("serverPort").toString());
        level = Integer.parseInt(map.get("level").toString());
        boolean isProxy = Boolean.parseBoolean(String.valueOf(map.get("isProxy")));
        try
        {
            int ssltype = ProxyEnv.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            if (map.containsKey("ssltype")){
                ssltype = Integer.parseInt(map.get("ssltype").toString());
            }
            client = new ProxyXmlRpcClient(next_ip, next_port, ssltype);
            Vector params = new Vector();
            params.addElement(methodname);
            params.addElement(token);
            params.addElement(postString);
            hashtable.put("level", level);
            hashtable.put("proxyJson", proxyJson);
            hashtable.put("isProxy", isProxy);
            hashtable.put("serverHost", server_ip);
            hashtable.put("serverPort", server_port);
            params.addElement(hashtable);
            backMessage = (String) client.execute("IEAIAgent.taskAgentDeploy", params);
            _log.info(method + level + level + ",ip: " + next_ip + ",port: " + next_port + ".ok");
        } catch (Exception e)
        {
            _log.info(method + "ip: " + next_ip + "port: " + next_port + ".xml", e);
            throw e;
        }
        return backMessage;
    }
    
    public boolean stopTN5250 ( String requestId, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().stopTN5250(requestId, vo);
    }

    public int getActState ( String requestId, Hashtable vo ) throws Exception
    {
        return ProxyUtil.getInstance().getActState(requestId, vo);
    }
    
    public String sendCollectionFileToAgents ( Vector<Hashtable<String, Object>> fileVector, Hashtable vo )
    {
        String xml = null;
        try
        {
            xml = ProxyUtil.getInstance().sendCollectionFileToAgents(fileVector, vo);
        } catch (Exception ex)
        {
            _log.error("sendCollectionFileToAgents is err");
        }
        return xml;
    }

    public Hashtable smdbResultManager ( Vector<Hashtable<String, Object>> infoVector, Hashtable vo )
    {
        Hashtable xml = null;
        try
        {
            xml = ProxyUtil.getInstance().smdbResultManager(infoVector, vo);
        } catch (Exception ex)
        {
            _log.error("sendCollectionFileToAgents is err");
        }
        return xml;
    }

    /**
     * 线程是否存在 jiaMing 2023-7-7
     * @param vo
     * @return
     */
    public boolean checkProxyThread(Hashtable vo){
        //ProxyThreadUtils.getInstance().proxyThreadExit(threadName)
        String threadName = String.valueOf(vo.get("threadName"));
        return  ProxyThreadUtils.getInstance().proxyThreadExit(threadName);
    }
}
