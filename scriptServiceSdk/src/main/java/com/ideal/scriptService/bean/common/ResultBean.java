package com.ideal.scriptService.bean.common;

import lombok.Data;

/**
 * <pre>
 *  该类为接口调用后的返回值对象
 * </pre>
 */
@Data
public class ResultBean<T> {
     /**
      * 状态：success/fail
      */
     private String status;
     /**
      * 接口调用返回的消息内容
      */
     private String message;
     /**
      * 接口调用返回的实际数据内容，泛型属性，可能存在多种数据类型 {@link ContentDataListBean} 或者其他bean对象或者string类型
      */
     private T content;
}
