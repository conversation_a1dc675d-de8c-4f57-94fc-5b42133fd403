
Ext.onReady(function() {
  Ext.define('systemModel', {
    extend : 'Ext.data.Model',
    fields : [
      {
        name : 'iid',
        type : 'int'
    },{
      name : 'isysname',
      type : 'string'
    },{
      name : 'imatchcondition',
      type : 'string'
    }]
  });

  // create the data store
  var firstGridStore = Ext.create('Ext.data.Store', {
    autoLoad: true,
    autoDestroy: true,
    model: 'systemModel',
    proxy: {
      type: 'ajax',
      url: 'getSelectedSystemListForFollow.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  firstGridStore.on('beforeload', function(store, options) {
    var new_params = {
      sysId : sysId
    };
    Ext.apply(firstGridStore.proxy.extraParams, new_params);
  });

  // Column Model shortcut array
  var columns = [ {
    text : "ID",
    width : 100,
    hidden : true,
    dataIndex : 'iid'
  }, {
    text : "业务系统名称",
    flex : 1,
    dataIndex : 'isysname'
  }, {
    text : "匹配条件",
    width : 250,
    dataIndex : 'imatchcondition',
    editor : {
      allowBlank : true
    }
  } ];
  
  var secondColumns = [ {
    text : "ID",
    width : 100,
    hidden : true,
    dataIndex : 'iid'
  }, {
    text : "业务系统名称",
    flex : 1,
    dataIndex : 'isysname'
  }];
  
  var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
    clicksToEdit : 2
  });

  // declare the source Grid
  var firstGrid = Ext.create('Ext.grid.Panel', {
    multiSelect : true,
    viewConfig : {
      plugins : {
        ptype : 'gridviewdragdrop',
        dragGroup : 'firstGridDDGroup',
        dropGroup : 'secondGridDDGroup'
      },
      listeners: {
        drop: function(node, data, dropRec, dropPosition) {
          Ext.Ajax.request({
            url : 'addFollowSystem.do',
            params : {
              sysId : sysId,
              followSysId: data.records[0].get('iid')
            },
            method : 'POST',
            success : function(response, opts) {
              var success = Ext.decode(response.responseText).success;
              var msg = Ext.decode(response.responseText).msg;
              // 当后台数据同步成功时
              if (success) {
                Ext.flash.msg('成功', msg);
              } else {
                Ext.flash.msg('错误',msg);
                firstGridStore.load();
                secondGridStore.load();
              }
            },
            failure: function() {
              Ext.flash.msg('错误', "网络或服务器出现问题，请联系管理员");
              firstGridStore.load();
              secondGridStore.load();
            }
          });
        }
      }
    },
    store : firstGridStore,
    columns : columns,
    columnLines : true,
    stripeRows : true,
    title : '已选择的后续系统',
    margins : '0 2 0 0',
    plugins : [ cellEditing ],
    dockedItems : [{
      xtype : 'toolbar',
      dock : 'top',
      items : [ "->", {
        xtype : 'button',
        width : 70,
        height : 22,
        textAlign : 'center',
        cls : 'Common_Btn',
        text : '保存',
        handler : save
      }]
    }]
  });
  
  var sysNameForQuery = Ext.create('Ext.form.TextField', {
    margin : '5',
    emptyText : '--请输入系统分类名称--',
    labelWidth : 80,
    width : 250,
    xtype : 'textfield'
  });

  var secondGridStore = Ext.create('Ext.data.Store', {
    autoLoad: true,
    autoDestroy: true,
    model: 'systemModel',
    proxy: {
      type: 'ajax',
      url: 'getUnSelectedSystemListForFollow.do',
      reader : {
        type : 'json',
        root : 'dataList',
        totalProperty : 'total'
      }
    }
  });
  
  secondGridStore.on('beforeload', function(store, options) {
    var new_params = {
      sysId : sysId,
      filterName: sysNameForQuery.getValue().trim()
    };
    Ext.apply(secondGridStore.proxy.extraParams, new_params);
  });
  
  /** 列表分页工具栏* */
  var bsPageBar = Ext.create('Ext.PagingToolbar', {
    store : secondGridStore,
    dock : 'bottom',
    displayInfo : true,
    emptyMsg : "没有记录"
  });

  // create the destination Grid
  var secondGrid = Ext.create('Ext.grid.Panel', {
    viewConfig : {
      plugins : {
        ptype : 'gridviewdragdrop',
        dragGroup : 'secondGridDDGroup',
        dropGroup : 'firstGridDDGroup'
      },
      listeners: {
        drop: function(node, data, dropRec, dropPosition) {
          Ext.Ajax.request({
            url : 'deleteFollowSystem.do',
            params : {
              sysId : sysId,
              followSysId: data.records[0].get('iid')
            },
            method : 'POST',
            success : function(response, opts) {
              var success = Ext.decode(response.responseText).success;
              var msg = Ext.decode(response.responseText).msg;
              // 当后台数据同步成功时
              if (success) {
                Ext.flash.msg('成功', msg);
              } else {
                Ext.flash.msg('错误',msg);
                firstGridStore.load();
                secondGridStore.load();
              }
            },
            failure: function() {
              Ext.flash.msg('错误', "网络或服务器出现问题，请联系管理员");
              firstGridStore.load();
              secondGridStore.load();
            }
          });
        }
      }
    },
    store : secondGridStore,
    columns : secondColumns,
    columnLines : true,
    stripeRows : true,
    bbar : bsPageBar,
    title : '未选择的业务系统',
    margins : '0 0 0 3',
    dockedItems : [{
      xtype : 'toolbar',
      dock : 'top',
      items : [ sysNameForQuery, {
        xtype : 'button',
        width : 70,
        height : 22,
        textAlign : 'center',
        cls : 'Common_Btn',
        text : '查询',
        handler : function(){
          bsPageBar.moveFirst ();
        }
      }, {
        xtype : 'button',
        width : 70,
        height : 22,
        textAlign : 'center',
        cls : 'Common_Btn',
        text : '重置',
        handler : function(){
          sysNameForQuery.setValue ('');
        }
      }]
    }]
  });

  // Simple 'border layout' panel to house both grids
  var displayPanel = Ext.create('Ext.Panel', {
    width : '100%',
    height : '100%',
    layout : {
      type : 'hbox',
      align : 'stretch'
    },
    border: false,
    renderTo : "follow_system_config_area",
    defaults : {
      flex : 1
    }, // auto stretch
    items : [ firstGrid, secondGrid ]
  });

  // 当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
    Ext.destroy(firstGrid);
    Ext.destroy(secondGrid);
    Ext.destroy(firstGridStore);
    Ext.destroy(secondGridStore);
    Ext.destroy(displayPanel);
    if (Ext.isIE) {
      CollectGarbage();
    }
  });
  
  String.prototype.trim = function () {
    return this.replace (/(^\s*)|(\s*$)/g, "");
  };
  function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
  }
  
  function save() {
    var m = firstGridStore.getModifiedRecords();
    if (m.length < 1) {
      setMessage('您没有进行任何修改，无需保存');
      return;
    }
    var jsonData = "[";
    for (var i = 0, len = m.length; i < len; i++) {
      var ss = Ext.JSON.encode(m[i].data);
      if (i == 0)
        jsonData = jsonData + ss;
      else
        jsonData = jsonData + "," + ss;
    }
    jsonData = jsonData + "]";
    Ext.Ajax.request({
      url : 'saveFollowSystemMatchCondition.do',
      method : 'POST',
      params : {
        sysId: sysId,
        jsonData : jsonData
      },
      success : function(response, request) {
        var success = Ext.decode(response.responseText).success;
        if (success) {
          firstGridStore.modified = [];
          firstGridStore.reload();
          Ext.Msg.alert('提示', '保存成功');
        } else {
          Ext.Msg.alert('提示', '保存失败！');
        }
      },
      failure : function(result, request) {
        Ext.Msg.alert('提示', '保存失败！');
      }
    });
  }
});