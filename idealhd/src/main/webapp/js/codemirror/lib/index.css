body {
    font-family: <PERSON><PERSON>;
  }
  
  #codeMirror {
    margin: 0 auto;
  }
  .help-text-pop-up{
    width: 250px;
    width: 450px;
    max-height: 350px;
    overflow-y: auto;
    box-sizing: border-box;
    padding: 0px;
    background-color: rgba(255, 255 , 255);
    color: black;
    border-radius: 4px;
    font-size: 8px;
    font-family: PingFang SC, helvetica neue, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun,
          sans-serif;
    border: 1px solid rgb(168, 168, 168);
    color: rgb(51, 51, 51);
  }
  .help-text-pop-up p{
    margin: 0px;
  }
  .help-text-pop-up .help-text-pop-up-header{
    background-color: #f8f8f8;
    border-radius: 4px 4px 0px 0px;
    padding: 8px;
    color: rgb(146, 146, 146);
    font-weight: 400;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    border-bottom: 1px solid rgb(203, 203, 203);
  }
  
  .help-text-pop-up .help-text-pop-up-header::before{
    display: block;
    content: " ";
    width: 8px;
    height: 8px;
    background-color: rgba(65, 105, 225, 0.8);
    border-radius: 4px;
    position: absolute;
    left: 8px;
  }
  
  .help-text-pop-up-container {
    padding: 16px 12px;
  
  }
  .help-text-pop-up-container .keyword{
    font-size: 16px;
    font-weight: 800;
    margin-bottom: 4px; 
    
  }
  .help-text-pop-up-container .command {
    font-size: 16px;
    font-weight: 800;
  }
  .help-text-pop-up-container .command-explain{
    margin-top: 4px; 
    margin-bottom: 12px; 
  }
  
  .help-text-pop-up-container .brief-explain{
    font-size: 12px;
    color: rgb(146, 146, 146);
    
   
    margin-bottom: 12px;
    /* overflow:hidden; 
    text-overflow:ellipsis;
    display:-webkit-box; 
    -webkit-box-orient:vertical;
    -webkit-line-clamp:3;  */
  }
  .required-args-container .part-header {
    font-size: 14px;
    font-weight: 500;
    padding-top: 12px; 
    border-top: 1px solid rgb(197, 197, 197);
  }
  .help-text-pop-up .args-list {
    margin: 4px;
  }
  .help-text-pop-up .args-list .args {
    margin: 6px 0px 4px 0px;
    display: flex;
    align-items: center;
  }
  .help-text-pop-up .args-list .args::before {
    color: rgb(146, 146, 146);
    display: block;
    content: '';
    height: 4px;
    width: 4px;
    /* background-color: #bfbcbc; */
    background-color: rgba(65, 105, 225, 0.6);
    border-radius: 2px;
    margin-right: 8px;
  }
  .help-text-pop-up .args-desc{
    color: rgb(146, 146, 146);
    margin-left: 12px;
  }
  
  .option-args-container .part-header {
    font-size: 14px;
    font-weight: 500;
  }
  
  .examples-container .part-header {
    font-size: 14px;
    font-weight: 500;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgb(197, 197, 197);
  }
  
  
  .auto-generate-code span {
    color: rgba(255, 255 , 255, 0.6) !important;
  }
  
  .code-template{
    margin: 4px auto !important;
    background-color: #f8f8f8;
    padding: 6px;
    border-radius: 0px 2px 2px 0px;
    border-left: 4px solid rgba(65, 105, 225, 0.6);
  }
  .help-text-pop-up-container .example-explain{
    margin-top: 12px;
  }
  .code-recommand-popover{
    width: 220px;
    max-height: 180px;
    overflow-y: auto;
    box-sizing: border-box;
    padding: 10px 8px;
    border-radius: 6px;
    background-color: #010304;
  }
  .code-recommand-popover .code-comment{
    color: #666;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 14px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  .code-recommand-popover .active-code-recommand{
    background-color: #0075FF;
    color: white;
  }
  .code-recommand{
    max-height: 338px;
    overflow-y: auto;
    word-break: break-word;
    white-space: break-spaces;
  }
  .code-recommand-container{
    width: 650px;
    max-height: 370px;
    padding: 16px;
    border-radius: 6px;
    background-color: #010304;
    color: #666;
    line-height: 2;
  }
  .enter-tab-tip{
    position: absolute;
    right: 16px;
    top: 16px;
    padding: 3px;
    background-color: rgba(36, 37, 45, 0.8);
    border-radius: 6px;
    display: flex;
    width: 91px;
    height: 32px;
    justify-content: center;
    align-items: center;
    flex-direction: row;
  }
  .enter-tab-tip .tip-text{
    flex: 1px;
    font-size: 14px;
    color: #A8A8A8;
    text-align: center;
  }
  .tab-icon-container{
    width: fit-content;
    height: fit-content;
    border-radius: 3px;
    background: linear-gradient(90deg, #3A3C48, #0075FF);
    background-size: 400% 400%;
    -webkit-animation: Gradient 4s ease infinite;
    -moz-animation: Gradient 4s ease infinite;
    animation: Gradient 4s ease infinite;
  }
  
  .tab-icon-container .tab-icon{
    width: 44px;
    height: 26px;
    display: block;
  }
  
  @-webkit-keyframes Gradient {
    0% {
        background-position: 0% 50%;
    }
    25% {
      background-position: 50% 100%;
    } 
    50% {
      background-position: 50% 100%;
    }
    75% {
        background-position: 100% 50%;
    }
    100% {
      background-position: 50% 0%;
    }
  }
  
  @-moz-keyframes Gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 50% 100%;
    }
    75% {
        background-position: 100% 50%;
    }
    100% {
      background-position: 50% 0%;
    }
  }
  
  @keyframes Gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 50% 100%;
    }
    75% {
        background-position: 100% 50%;
    }
    100% {
      background-position: 50% 0%;
    }
  }