package com.ideal.ieai.core.util.schema.xsd;


/**
 * <p>Title: IEAI Core</p>
 * <p>Description:translate xsd type to schema type </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 3.0
 */

public class XSDType2SchemaTypeHelper
{
    public XSDType2SchemaTypeHelper()
    {
    }

//    public static int getType(String name) throws ImportException
//    {
//
//    }

}