package com.ideal.ieai.server.repository.groupMessage;

import com.ideal.ieai.server.ServerException;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @since 2013.10.25
 *
 * @see 操作接口
 */

public interface IAgentGroupManager
{
    public boolean isSameAgentGroupName(String groupName, String groupId)
            throws ServerException;

    // start by 2013-11-04 add by licheng_zhao agent服务器管理及分组信息操作方法
    public Long saveAgentGroupManager(String agentGroupIP, Long port, Long os,
                                      String user, String passwd, Long actnummax, Long actnum, String state) throws ServerException;

    public boolean isSameAgentGroupManagerName(String agentGroupIP, String port) throws ServerException;

    public List listAgentGroupInfo(String ip, String port, String agentGroupName, String sortCriterion, String sortDirection, Integer curPage, Integer pageSize) throws ServerException;

    public List listOneGroupManager(Long groupId) throws ServerException;

    public Long updateGroupManager(Long groupId, String agentGroupIP, Long port, Long os, String user, String passwd, Long actnummax) throws ServerException;

    public void deleteGroupManager(long groupId) throws ServerException;

    public void deleteAgentInfoGroup(long nodeid) throws ServerException;

    public Long saveAgentInfoGroup(Long groupid, Long nodeid) throws ServerException;

    public List listAgentInfo(String ip, String port, Integer curPage, Integer pageSize)
            throws ServerException;
    //end by licheng_zhao

    // add by gang_guo
    public List listAgentGroupMessage(String agentGroupName) throws ServerException;

    public List listSelectedGroupMessage(Long groupId) throws ServerException;

    public Long updateAgentGroupMessage(Long groupId, String groupName, String groupDescription) throws ServerException;

    public Long saveAgentGroupMessage(String groupName, String groupDescription) throws ServerException;

    public boolean isNotUnionGroup(long groupId) throws ServerException;

    public void deleteAgentGroupMessage(long groupId) throws ServerException;



}