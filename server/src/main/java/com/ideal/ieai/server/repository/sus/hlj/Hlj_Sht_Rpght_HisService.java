package com.ideal.ieai.server.repository.sus.hlj ;

import java.util.List;
import java.util.Map;
import org.apache.log4j.Logger;
/**
   *
   * @ClassName: Hlj_Sht_Rpght_HisService
   * @Description: (黑龙江_核心变更维护表sheet_历史),RPG程序回退
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:40:41
   * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
   */
public class Hlj_Sht_Rpght_HisService 
{
   private static Logger _log =Logger.getLogger(Hlj_Sht_Rpght_HisService.class);
   /**
   *
   * @Title: 
   * @Description: (黑龙江_核心变更维护表sheet_历史),RPG程序回退的查询
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:40:41
   */
   public Map queryHlj_Sht_Rpght_His (Hlj_Sht_Rpght_His hlj_sht_rpght_his , int start , int limit , int type )throws Exception
   {
      Map map =null;
      Hlj_Sht_Rpght_HisManager hlj_Sht_Rpght_HisManager =new Hlj_Sht_Rpght_HisManager();
      map=hlj_Sht_Rpght_HisManager.queryHlj_Sht_Rpght_His(hlj_sht_rpght_his, start, limit, type);
      return map;
   }



   /**
   *
   * @Title: 
   * @Description: (黑龙江_核心变更维护表sheet_历史),RPG程序回退的保存
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:40:41
   */
   public boolean saveHlj_Sht_Rpght_His (List<Map<String,Object>> dataList , int type )throws Exception
   {
      Hlj_Sht_Rpght_HisManager hlj_Sht_Rpght_HisManager =new Hlj_Sht_Rpght_HisManager();
      return hlj_Sht_Rpght_HisManager.saveHlj_Sht_Rpght_His(dataList, type);
   }



   /**
   *
   * @Title: 
   * @Description: (黑龙江_核心变更维护表sheet_历史),RPG程序回退的删除
   * @author: tiejun_fan
   * @date: 2020年12月25日 16:40:41
   */
   public boolean deleteHlj_Sht_Rpght_His (String iids , int type )throws Exception
   {
      Hlj_Sht_Rpght_HisManager hlj_Sht_Rpght_HisManager =new Hlj_Sht_Rpght_HisManager();
      return hlj_Sht_Rpght_HisManager.deleteHlj_Sht_Rpght_His(iids, type);
   }



}