<%@page contentType="text/html; charset=utf-8"%>
<html>  
  <head>    
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/hcEnvironInfo/hcEnvironInfoMonitor.js"></script>
    <script>
    var ipAdressVal  = '<%=request.getParameter("ipAdress")%>';
    var hostNameVal  = '<%=request.getParameter("hostName")%>';
    var sysNameVal  = '<%=request.getParameter("sysName")%>';
    var environVal  = '<%=request.getParameter("environ")%>';
    var cpuCoreNumVal  = '<%=request.getParameter("cpuCoreNum")%>';
    var memorySizeVal  = '<%=request.getParameter("memorySize")%>';
    var diskSizeVal  = '<%=request.getParameter("diskSize")%>';
    var cpuFrequency  = '<%=request.getAttribute("cpuFrequency")%>';
    var cpuUsageRadio  = '<%=request.getAttribute("cpuUsageRadio")%>';  
    var cpuFreeVal  = '<%=request.getAttribute("cpuFree")%>';
    var cpuUseVal  = '<%=request.getAttribute("cpuUse")%>';
    var memoryUseRateVal  = '<%=request.getAttribute("memoryUseRate")%>';
    var memoryFreeVal  = '<%=request.getAttribute("memoryFree")%>';
    var memoryUsedVal  = '<%=request.getAttribute("memoryUsed")%>';
    var diskUseRateVal  = '<%=request.getAttribute("diskUseRate")%>';
    var diskFreeVal  = '<%=request.getAttribute("diskFree")%>';
    var diskUsedVal  = '<%=request.getAttribute("diskUsed")%>';
    var cpId = '<%=request.getParameter("cpId")%>'
    </script>
    <script type="text/javascript"
	src="<%=request.getContextPath()%>/js/charts/echarts.min.js"></script>	
	
	 <style type="text/css">
	
		.numberone_style{
			color:'#fd7a4c';
		} 
	</style>
	
	<style type="text/css">
		
		
		.customize_body .noboder-style .x-form-text {
		  padding: 4px 0 3px 10px;
		  color: #46545d;
		  background: #ffffff repeat-x 0 0;
		  border-width: 0px;
		  border-style: solid;
		  border-color: #d9d9d9;
		  height: 34px;
		  border-radius: 2px;
		  line-height: 34px;
		}
		
		.valueOneStyle {
			 color: #00dbc2; 
	   		 font-size: 20px; 
		}
		
		.valueTwoStyle {
			 color: #00dbc2; 
	   		 font-size: 30px; 
		}
	</style>	
  </head>  
  <body>  
	 <div id="hcEnvironInfoMonitor_area" style="width: 100%;height: 100%">
	 </div>
  </body>  
</html>