Ext.onReady (function ()
{
	
	var iflook = false;
	if(look == 1){
		iflook =true;
	}
	
	// 清理主面板的各种监听时间
	destroyRubbish ();
	var titleHeight = 37;
	var arrPubParms = [];
	Ext.define ('paramModel',
		{
		    extend : 'Ext.data.Model',
		    fields : [
		            {
		                name : 'iid',
		                type : 'string'
		            },
		            {
		                name : 'iparam_name',
		                type : 'string'
		            },
		            {
		                name : 'iname',
		                type : 'string'
		            }
		      ]
		});
	Ext.define ('commonParamGridPanelModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'int',
	                useNull : true
	            },
	            {
	                name : 'paramType',
	                type : 'string'
	            },
	            {
	                name : 'iparam_name',
	                type : 'string'
	            },
	            {
	                name : 'iparam_value',
	                type : 'string'
	            },
	            {
	                name : 'iisreadonly',
	                type : 'string'
	            },
	            {
	                name : 'envId',
	                type : 'string'
	            },
	            {
	                name : 'envName',
	                type : 'string'
	            },
	            {
	                name : 'envName',
	                type : 'string'
	            },
	            {
	                name : 'iparamname',
	                type : 'string'
	            }
	    ]
	});
	 Ext.define('enviromentModel1', {
		    extend : 'Ext.data.Model',
		    fields : [{
		      name : 'iid',
		      type : 'int',
		      useNull : true
		    }, {
		        name : 'name',
		        type : 'string'
		      }, {
		      name : 'useing',
		      type : 'string'
		    }, {
		      name : 'description',
		      type : 'string'
		    },{
		        name : 'envabbID',
		        type : 'string'
		      }]
		  });
	var commonParamGridPanelStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'commonParamGridPanelModel',
	    pageSize : 50,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'paasInstanceInsParamList.do?insid='+instanceiid+"&modeltype="+sysType,//'getPassTestData.do?dataType=getStaticParamSUS',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	        }
	    }
	});

	var cellEditing = Ext.create ('Ext.grid.plugin.CellEditing',
	{
		clicksToEdit : 2
	});
	
	var pPageBar = Ext.create('Ext.PagingToolbar', {
		store : commonParamGridPanelStore,
		dock : 'bottom',
		displayInfo : true,
		border : false,
		emptyMsg : "没有记录"
	});
	var commonParamGridPanel = Ext.create ('Ext.grid.Panel',
	{
		region : 'center',
		//extend : 'Ext.grid.Panel',
	    //alias : 'widget.ideapanel',
	    //ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		bbar : pPageBar,
	    store : commonParamGridPanelStore,
	    title : '',
	   /* plugins : [
		    cellEditing
	    ],*/
	    plugins: [cellEditing],//rowEditing
	    split : true,
	    //padding : grid_margin,
	    multiSelect : true,
	    columns : [
	            {
	                text : '编号',
	                width : 65,
	                sortable : true,
	                hidden : true,
	                dataIndex : 'iid'
	            },
	            {
	                text : '序号',
	                xtype : 'rownumberer',
	                width : 65
	            },
	            {
	                text : '参数名称',
	                dataIndex : 'iparam_name',
	                flex : 1,
	                sortable : true,
                    id:'iparamid',
                    editor : {
    	    			allowBlank : true
    	    		}
	            },
				{
					text : '参数类型',
					dataIndex : 'paramType',
					flex : 1,
					sortable : true,
					editor : new Ext.form.field.ComboBox(
					{
						editable : false,
						store : [
								[ '0',
										'枚举类型' ],
								[ '1',
										'非枚举类型' ]
								]
					}),
					renderer : function(value, metaData, record) {
						if (value == 0) {
							return "枚举类型";
						} else if (value == 1){
							return "非枚举类型";
						}else{
							return "固定参数";
						}
					}
				}
	    ],
	    listeners: {
                beforeedit: function (editor, e, eOpts) {
                	if(e.record.get('paramType') == 2)
            		{
            		   return false;
            		}
                    if (e.colIdx==2) {
                    	if(e.record.get('paramType') == 0){
                    		var paramStore = Ext.create ('Ext.data.Store',
                				{
                				    autoLoad : true,
                				    autoDestroy : true,
                				    model : 'paramModel',
                				    pageSize : 50,
                				    proxy :
                				    {
                				        type : 'ajax',
                				        url : 'getPaasParameterList.do?scope='+sysType,//'getPassTestData.do?dataType=getStaticParam',
                				        reader :
                				        {
                				            type : 'json',
                				            root : 'dataList',
                				        }
                				    }
                				});
	                        e.column.setEditor({
	                            xtype: 'combobox',
	                            valueField: "iparam_name",
	            	         	displayField: "iparam_name",
	                            editable: false,
	                            store: paramStore
	                        });
                    	}else{
                    		 e.column.setEditor({xtype: 'textfield',})
                    	}
                    }
                }
	    },
	    dockedItems : [
	            {
	                xtype : 'toolbar',
	                //height : 40,
	                //cls:'customize_form_cn',
	                dock : 'top',
	                items : [
	                        '->',{
//	                            text : '<u>增加</u>',
//	                            icon : 'images/add.png',
	                        	text : '返回',
	                            cls : 'Common_Btn',
	                            handler : function ()
	                            {
	                            	//destroyRubbish();
	                        		contentPanel.getLoader().load({
	                        			url: 'accessPaasSysList.do',
	                        			params: {},
	                        			scripts: true
	                        		});
	                            }
	                        },{
//	                            text : '<u>增加</u>',
//	                            icon : 'images/add.png',
	                        	text : '增加',
	                            cls : 'Common_Btn',
	                            hidden : iflook,
	                            handler : function ()
	                            {
	                            	commonParamGridPanelStore.insert (0, new commonParamGridPanelModel ());
		                            /*cellEditing.startEditByPosition (
		                            {
		                                row : 0,
		                                column : 0
		                            });*/
	                            }
	                        },
	                        {
//	                            text : '<u>保存</u>',
//	                            icon : 'images/save.gif',
	                        	text : '保存',
	                            cls : 'Common_Btn',
	                            hidden : iflook,
	            				handler : function(){
	            					//Ext.Msg.alert("提示","面板中，新增和保存的数据将会被保存!");
	            					addparam();
	            					return;
	            				}
	                        },
	                        {
	                            itemId : 'delete',
//	                            text : '<u>删除</u>',
//	                            icon : 'images/delete.png',
	                            text : '删除',
	                            hidden : iflook,
	                            cls : 'Common_Btn',
	                            disabled : false,
	            				handler : function(){
	            					delparam();
	            					return;
	            				}
	                        }/*,
	                        {
	                            xtype : 'button',
	                            text : '<u>导出</u>',
	                            iconCls : 'export',
	                            handler : function ()
	                            {
		                            window.location.href = 'exportCommonParamExcel.do';
	                            }
	                        },
	                        {
	                            xtype : 'button',
	                            text : '<u>导入</u>',
	                            iconCls : 'import',
	                            handler : openImportWindows
	                        }*/
	                ]
	            }
	    ]
	});
	commonParamGridPanel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		commonParamGridPanel.down ('#delete').setDisabled (selections.length === 0);
	});
	
	var commonParamPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : 'staticParam_div',
	    width : contentPanel.getWidth (),//'100%',
	    height : contentPanel.getHeight ()-100,
	    layout : 'border',
	    border:false,
	    //bodyCls: 'service_platform_bodybg',
//	    bodyPadding : 5,
	    title : '',
	    items : [
		    commonParamGridPanel
	    ]
	});
	contentPanel.on ('resize', function ()
	{
		commonParamPanel.setHeight (contentPanel.getHeight () - titleHeight);
		commonParamPanel.setWidth (contentPanel.getWidth ());
	});
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (commonParamPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	function setMessage (msg)
	{
		Ext.Msg.alert ('提示', msg);
	}
	
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	
	/* 保存业务系统记录 */
	function addparam ()
	{
		var m = commonParamGridPanelStore.getModifiedRecords ();
		if (m.length < 1)
		{
			return;
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++)
		{
			if( m[i].get ("paramType")==2)
			{
				setMessage ('固定参数名称不能修改！');
				return;
			}
			var n = 0;
			var iparam_name = m[i].get ("iparam_name").trim ();
			if ("" == iparam_name || null == iparam_name)
			{
				setMessage ('参数名称不能为空！');
				return;
			}
			if (fucCheckLength (iparam_name) > 250)
			{
				setMessage ('参数名称长度不能超过250！');
				return;
			}			
			var ss = Ext.JSON.encode (m[i].data);
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
		
		Ext.Ajax.request (
		{
		    url : 'savePaasInsParameter.do',
		    method : 'POST',
		    params :
		    {
			    jsonData : jsonData,
			    insid:instanceiid,
			    modeltype:sysType
		    },
		    success : function (response, request)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    if (success)
			    {
				    commonParamGridPanelStore.modified = [];
				    commonParamGridPanelStore.reload ();
				    Ext.Msg.alert ('提示', '保存成功');
			    }
			    else
			    {
				    Ext.Msg.alert ('提示', message);
			    }
		    },
		    failure : function (result, request)
		    {
			    Ext.Msg.alert ('提示', '保存失败！');
		    }
		});
	}
	
	function delparam()
	{
		var record = commonParamGridPanel.getSelectionModel().getSelection();
		if(record.length==0)
		{
			Ext.MessageBox.alert("提示", "请选择要删除的数据");
			return;
		}
		var iidStr = "";
		Ext.Array.each (record, function (recordObj)
		{
			if(recordObj.get("paramType")==2)
			{
				Ext.MessageBox.alert("提示", "固定参数不能删除");
				return;
			}
			iidStr+=recordObj.get ('iid')+",";
		});
		Ext.Ajax.request({
		    url : 'deletePaasInsParameter.do',//scriptService/cancelShare.do
		    method : 'POST',
		    params : {
		  	  iidStr:iidStr
		    },
		    success: function(response, opts) {
	    		var message = '操作成功！';
	    		Ext.MessageBox.show({
	                title : "提示",
	                msg : message,
	                buttonText: {
	                    yes: '确定'
	                },
	                buttons: Ext.Msg.YES
	              });
	    		commonParamGridPanelStore.reload();
		        
		    },
		    failure: function(result, request) {
		    	secureFilterRs(result,"操作失败！");
		    }
	    });
	}
	/** 弹出上传窗口* */
	function openImportWindows (iid)
	{
		configwindow = Ext.create ('Ext.window.Window',
		{
		    title : '静态参数导入',
		    autoScroll : true,
		    modal : true,
		    closeAction : 'destroy',
		    buttonAlign : 'center',
		    draggable : false,// 禁止拖动
		    resizable : false,// 禁止缩放
		    width : 500,
		    height : 100,
		    loader :
		    {
		        url : "page/resourcemanager/static/assets/js/importWindow.jsp",
		        params :
		        {
			        importUrl : 'importCommonParamExcel.do'
		        },
		        autoLoad : true,
		        autoDestroy : true,
		        scripts : true
		    },
		    listeners :
		    {
			    close : function ()
			    {
				    commonParamGridPanelStore.reload ();
			    }
		    }
		}).show ();
	}
});
