package com.ideal.ieai.server.repository.hd.cicd.configitem.utils;

public class Base64Utils {
    // 编码
    public static String base64Encode(String token) {
        byte[] encodedBytes = java.util.Base64.getEncoder().encode(token.getBytes());
        return new String(encodedBytes,java.nio.charset.Charset.forName("UTF-8"));
    }
    // 解码
    public static String base64Decode(String token) {
        byte[] decodedBytes = java.util.Base64.getDecoder().decode(token.getBytes());
        return new String(decodedBytes, java.nio.charset.Charset.forName("UTF-8"));
    }
}