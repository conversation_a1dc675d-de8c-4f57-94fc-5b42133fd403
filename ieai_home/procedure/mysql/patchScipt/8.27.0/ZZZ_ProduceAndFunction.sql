DELIMITER ;;
DROP PROCEDURE IF EXISTS PROC_MV_RUN_INSTANCE;
CREATE PROCEDURE PROC_MV_RUN_INSTANCE(IN WORKID NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
BEGIN
	INSERT INTO IEAI_RUNINFO_INSTANCE_HIS(IID,IRUNINSID,<PERSON><PERSON><PERSON><PERSON>,IRUNIN<PERSON>AM<PERSON>,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPA<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>TCH,<PERSON><PERSON><PERSON><PERSON>TC<PERSON>OR<PERSON>,IS<PERSON><PERSON>,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISH<PERSON>LSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IRED<PERSON>BLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,ISRTO,IINFORTO,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME,IEXECUTOR,IMESSAGEREMIND,ICICDSTEPID,ISCRIPTTYPE,ISONAME,ISTEPIUUID,ISCRIPTMODULETYPE,ISCRIPTMODULECHILDTYPE,ISTAGETYPE,IEXECFAILED,IHOSTNAME,ICICDCONFIGFILE,IPRIORITY,ICICDEXECBATCHNUM,IEXECPERMISSION)   SELECT  IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,ISRTO,IINFORTO,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME,IEXECUTOR,IMESSAGEREMIND,ICICDSTEPID,ISCRIPTTYPE,ISONAME,ISTEPIUUID,ISCRIPTMODULETYPE,ISCRIPTMODULECHILDTYPE,ISTAGETYPE,IEXECFAILED,IHOSTNAME,ICICDCONFIGFILE,IPRIORITY,ICICDEXECBATCHNUM,IEXECPERMISSION FROM  IEAI_RUNINFO_INSTANCE  WHERE  IRUNINSID=WORKID;
	INSERT INTO IEAI_RUN_INSTANCE_HIS(IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID,ICHANGETYPE,IFATHER,IPROJECT_NUM,ICICDTASKTYPE,IREVIEWEDBY,IDISPLAYGROUPID)  SELECT  IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2 ,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID,ICHANGETYPE,IFATHER,IPROJECT_NUM,ICICDTASKTYPE,IREVIEWEDBY,IDISPLAYGROUPID    FROM IEAI_RUN_INSTANCE WHERE  IID =WORKID;
  INSERT INTO IEAI_RUNINFO_INSTANCE_HIS_IPS(IID, IINSTANCEID, INSTANCEINFO_ID, IPS, PORTS, REMARK) SELECT IID, IINSTANCEID, INSTANCEINFO_ID, IPS, PORTS, REMARK FROM IEAI_RUNINFO_INSTANCE_IPS WHERE IINSTANCEID IN (SELECT ISYSID FROM IEAI_RUN_INSTANCE WHERE ISYSTYPE=51 AND IID=WORKID);
	DELETE FROM  IEAI_RUNINFO_INSTANCE  WHERE  IRUNINSID=WORKID;
	DELETE FROM  IEAI_RUN_INSTANCE  WHERE IID=WORKID;
END ;;
DELIMITER ;


DELIMITER ;;
DROP PROCEDURE IF EXISTS PROC_WORKFLOW_QUERY_KL;
CREATE PROCEDURE PROC_WORKFLOW_QUERY_KL (IN AN_USERID decimal(19,0), OUT AN_OPT_ID decimal(19,0), IN AV_PRJ_NAME varchar(255), IN AV_FLOW_NAME varchar(255), IN AV_START_USER varchar(255), IN AV_INSTANCE_NAME varchar(255), IN AI_START_TIME_FROM bigint(20), IN AI_START_TIME_TO bigint(20), IN AI_END_TIME_FROM bigint(20), IN AI_END_TIME_TO bigint(20), IN AI_SHOW_ACTIVE smallint(6), IN AI_SHOW_RUN_SELF smallint(6), IN AI_NUM_OF_PAGE smallint(6), IN AI_PAGE_ID int(11), OUT AI_REC_COUNT int(11), IN AV_ORDER_ITEM varchar(80), IN AI_ORDER_TYPE smallint(6), IN AN_CHECK_DATE decimal(19,0), IN AV_HOST_NAME varchar(50), IN FLOW_ID decimal(19,0),IN SHOW_FLOW_STATE smallint(6),IN AV_WORKFLOW_DESC  VARCHAR(255),IN AV_ISYSTEM  VARCHAR(255),IN AI_SERACH_TYPE SMALLINT(6), IN AI_EXPROT_SIZE SMALLINT(6))
BEGIN

	   DECLARE LN_OPT_ID  NUMERIC(19,0);
	   DECLARE LV_SQL    VARCHAR(4000);
	   DECLARE LV_ORDER_BY  VARCHAR(100);
	   DECLARE LN_MILLISEC  NUMERIC(19,0);
	   DECLARE LV_WHERE  VARCHAR(4000);
	   DECLARE LI_PAGE_ID  BIGINT;
	   DECLARE LI_MAX_RECID  BIGINT;
	   DECLARE LI_MIN_RECID  BIGINT;
	   DECLARE LI_PAGE_COUNT  BIGINT;
	   DECLARE LN_ISALL NUMERIC(19, 0);
	   DECLARE	LI_ZONE		SMALLINT;
	   DECLARE	LV_FORMAT	VARCHAR(50);

	   DECLARE	SI_TIMEZONE_CUR SMALLINT DEFAULT 8;
	   DECLARE	SI_TIMEZONE_GMT SMALLINT DEFAULT 0;

	   DECLARE	LI_RETURN	SMALLINT;


		 IF  AI_NUM_OF_PAGE IS NULL OR AI_NUM_OF_PAGE <= 0  THEN

					SET LI_RETURN = 0;
ELSE

					CALL PROC_GET_NEXT_ID('TMP_USER_VALIDATE', LN_OPT_ID);

					SET	AN_OPT_ID = LN_OPT_ID;


					SET	LI_ZONE = 0;
					SET	LV_FORMAT = 'YYYY-MM-DD HH24:MI:SS';

SELECT COUNT(*)
INTO LN_ISALL
FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
WHERE T.IROLEID = T2.IROLEID
  AND T2.IPERMISSION = 1
  AND T.IUSERID = AN_USERID
  AND T2.IPROID = -1;


IF LN_ISALL > 0 THEN
						INSERT INTO TMP_USER_VALIDATE
	          (OPT_ID, PRJ_ID, PRJ_NAME, PERMIT_ID, PERMIT_NAME)
SELECT LN_OPT_ID AS OPT_ID,
       IID       AS PRJ_ID,
       INAME     AS PRJ_NAME,
       0         AS PERMIT_ID,
       1         AS PERMIT_NAME
FROM IEAI_PROJECT
WHERE PROTYPE = 1
  AND (IPKGCONTENTID<>0 OR IPKGCONTENTID IS NULL)
  AND IUPPERID = IID;
ELSE
						INSERT INTO TMP_USER_VALIDATE
	          (OPT_ID, PRJ_ID, PRJ_NAME, PERMIT_ID, PERMIT_NAME)
SELECT LN_OPT_ID AS OPT_ID,
       PRJ.IID   AS PRJ_ID,
       PRJ.INAME AS PRJ_NAME,
       0         AS PERMIT_ID,
       1         AS PERMIT_NAME
FROM IEAI_PROJECT        PRJ,
     IEAI_SYS_PERMISSION ISP,
     IEAI_USERINHERIT    IU
WHERE IU.IUSERID = AN_USERID
  AND ISP.IROLEID = IU.IROLEID
  AND PRJ.IID = ISP.IPROID
  AND ISP.IPROID > 0
  AND ISP.IPERMISSION = 1
  AND PRJ.PROTYPE = 1
  AND (PRJ.IPKGCONTENTID <> 0 OR PRJ.IPKGCONTENTID IS NULL);
END IF;


				  SET LN_MILLISEC = AN_CHECK_DATE;
				  SET	LV_WHERE = '';


				IF	LENGTH(AV_PRJ_NAME) > 0	THEN
				   IF  INSTR(AV_PRJ_NAME,'%')>0 THEN
                    SET	LV_WHERE = LV_WHERE;
ELSE
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IPROJECTNAME  =' , CHAR(39) , AV_PRJ_NAME , CHAR(39));
			 	--  SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IPROJECTNAME LIKE (' , CHAR(39) , '%' , AV_PRJ_NAME , '%' , CHAR(39) , ')');
END IF;
END  IF;

					IF	LENGTH(AV_FLOW_NAME) > 0	THEN
	            SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWNAME LIKE (' , CHAR(39) , '%' , AV_FLOW_NAME , '%' , CHAR(39) , ')');
END	IF;

					IF	LENGTH(AV_START_USER) > 0	THEN
	            SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTARTUSERFULLNAME LIKE (' , CHAR(39) , '%' , AV_START_USER , '%' , CHAR(39) , ')');
END	IF;

					IF	LENGTH(AV_INSTANCE_NAME) > 0	THEN
	            SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWINSNAME LIKE (' , CHAR(39) , '%' , AV_INSTANCE_NAME , '%' , CHAR(39) , ')');
END	IF;

					 IF	AI_START_TIME_FROM > 0	THEN

	            IF	AI_START_TIME_TO > 0	THEN
	                SET	LV_WHERE = CONCAT(LV_WHERE , ' AND (C.ISTARTTIME BETWEEN ' , AI_START_TIME_FROM , ' AND ' , AI_START_TIME_TO , ')');
ELSE
	                SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTARTTIME >= ' , AI_START_TIME_FROM);
END	IF;
ELSE

	            IF	AI_START_TIME_TO > 0	THEN
	                SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTARTTIME <= ' , AI_START_TIME_TO);
END	IF;
END	IF;


	        IF	AI_END_TIME_FROM > 0	THEN

	            IF	AI_END_TIME_TO > 0	THEN
	                SET	LV_WHERE = CONCAT(LV_WHERE , ' AND (C.IENDTIME BETWEEN ' , AI_END_TIME_FROM , ' AND ' , AI_END_TIME_TO , ')');
ELSE
	                SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IENDTIME >= ' , AI_END_TIME_FROM);
END	IF;
ELSE

	            IF	AI_END_TIME_TO > 0	THEN
	                SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IENDTIME <= ' , AI_END_TIME_TO);
END	IF;
END	IF;


	        IF	AI_SHOW_ACTIVE = 1	THEN
	            SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTATUS IN (0, 6, 8, 15, 30, 40, 46, 47)');
END	IF;

	        IF	AI_SHOW_RUN_SELF = 1	THEN
	            SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IISAUTOSTART = 1');
END	IF;


	        IF	LENGTH(AV_HOST_NAME) > 0	THEN
	           SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IHOSTNAME LIKE (' , CHAR(39) , '%' , AV_HOST_NAME , '%' , CHAR(39) , ')');
END	IF;


	        IF	FLOW_ID > 0	THEN
							SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWID = ' , FLOW_ID);
END	IF;

	        IF  SHOW_FLOW_STATE >= 0  THEN

	            SET  LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTATUS = (' , CHAR(39)  , SHOW_FLOW_STATE  , CHAR(39) , ')');
END  IF;


	        IF  LENGTH(AV_WORKFLOW_DESC) > 0  THEN

	          SET LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWDES LIKE (' , CHAR(39) , '%' , AV_WORKFLOW_DESC , '%' , CHAR(39) , ')');

END  IF;


	        IF  LENGTH(AV_ISYSTEM) > 0  THEN
               SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISYSTEM  =' , CHAR(39) , AV_ISYSTEM , CHAR(39));

	    	-- SET  LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISYSTEM LIKE (' , CHAR(39)  , '%' , AV_ISYSTEM  , '%' , CHAR(39) , ')');
END  IF;

					SET @LV_SQL = CONCAT('SELECT COUNT(*) INTO  @LI_MAX_RECID FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C WHERE A.OPT_ID = ' , LN_OPT_ID , ' AND C.IPROJECTNAME = A.PRJ_NAME' , LV_WHERE);

					-- INSERT INTO A_TEMP VALUES(@LV_SQL);
				  	-- COMMIT;
PREPARE	SQLA FROM @LV_SQL;
EXECUTE SQLA;

SET LI_MAX_RECID = @LI_MAX_RECID;



					SET LI_PAGE_COUNT  = CEIL(LI_MAX_RECID / AI_NUM_OF_PAGE);

					IF LI_PAGE_COUNT = 0 THEN
						SET LI_PAGE_COUNT = 1;
END IF;

					IF  MOD(LI_MAX_RECID, AI_NUM_OF_PAGE) > 0  THEN
	         SET LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
END  IF;

	        IF  AI_PAGE_ID > LI_PAGE_COUNT  THEN
	         SET LI_PAGE_ID = LI_PAGE_COUNT;
ELSE
	         SET LI_PAGE_ID = AI_PAGE_ID;
END  IF;

	        IF  LI_PAGE_ID IS NULL OR LI_PAGE_ID < 0  THEN
	         SET LI_PAGE_ID = 1;
END  IF;

				SET AI_REC_COUNT = LI_MAX_RECID;

				IF AI_SERACH_TYPE =0 THEN

					SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE;
					SET LI_MAX_RECID = LI_PAGE_ID * AI_NUM_OF_PAGE;
END IF;

	      IF AI_SERACH_TYPE > 0 THEN

	         SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE;
	         SET  LI_MAX_RECID =AI_EXPROT_SIZE+LI_MIN_RECID;
END IF;


			  IF	AI_ORDER_TYPE = 1	THEN
	          SET	LV_ORDER_BY = ' ORDER BY 2 ASC';
ELSE
	          SET	LV_ORDER_BY = ' ORDER BY 2 DESC';
END	IF;


				SET @row = 0;
				IF  LENGTH(AV_INSTANCE_NAME) > 0 or FLOW_ID > 0  THEN


	            SET  LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, @row:=case when @row is null then 1 else @row+1 end   AS RID FROM (SELECT A.OPT_ID AS OPTID, ';

	            IF  UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'FLOW_ID'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'STATUS'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_USER'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_TIME'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.ISTARTTIME AS ISTART_TIME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'END_TIME'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'PAID_TIME'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN ' , LN_MILLISEC , ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	            ELSEIF  UPPER(AV_ORDER_ITEM) = 'TASK_NUM'  THEN
	                SET LV_SQL = CONCAT(LV_SQL , 'IFNULL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ');
ELSE

	             SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWID AS FLOW_ID,IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
END  IF;


	            SET LV_SQL = CONCAT(LV_SQL , 'FROM TMP_USER_VALIDATE A INNER JOIN IEAI_WORKFLOWINSTANCE C ON C.IPROJECTNAME = A.PRJ_NAME LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' , LN_OPT_ID );
	            SET LV_SQL = CONCAT(LV_SQL , LV_WHERE);

	            SET LV_SQL = CONCAT(LV_SQL , LV_ORDER_BY, ')UW) LT LIMIT ' , LI_MIN_RECID , ' , ' , LI_MAX_RECID);
ELSE

	       SET LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, @row:=case when @row is null then 1 else @row+1 end  AS RID FROM (SELECT A.OPT_ID AS OPTID, ';

	        IF  UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'  THEN
	             SET  LV_SQL = CONCAT(LV_SQL , 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'FLOW_ID'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'STATUS'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_USER'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_TIME'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.ISTARTTIME AS ISTART_TIME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'END_TIME'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'PAID_TIME'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN ' , LN_MILLISEC , ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
	        ELSEIF  UPPER(AV_ORDER_ITEM) = 'TASK_NUM'  THEN
	            SET LV_SQL = CONCAT(LV_SQL , 'IFNULL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ');
ELSE
	            SET LV_ORDER_BY = ' ORDER BY 2 DESC';
	            SET LV_SQL = CONCAT(LV_SQL , 'C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
END  IF;

	        SET LV_SQL = CONCAT(LV_SQL , 'FROM TMP_USER_VALIDATE A INNER JOIN IEAI_WORKFLOWINSTANCE C ON C.IPROJECTNAME = A.PRJ_NAME LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' , LN_OPT_ID );

	        SET LV_SQL = CONCAT(LV_SQL , LV_WHERE , LV_ORDER_BY,')UW) LT LIMIT ' , LI_MIN_RECID , ' , ' , LI_MAX_RECID);

END  IF;

					SET  @LV_SQL = LV_SQL;
					-- INSERT INTO A_TEMP VALUES(@LV_SQL);
					-- COMMIT;
PREPARE	SQLA FROM @LV_SQL;
EXECUTE SQLA;



INSERT  INTO  TMP_WORKFLOW_QUERY
(
    OPTID,
    FLOW_ID,
    FLOW_NAME,
    INSTANCE_NAME,
    PROJECT_NAME,
    STATUS,
    START_USER,
    START_TIME,
    END_TIME,
    PAID_TIME,
    HOST_NAME,
    TASK_NUM,
    REC_ID,
    ISYSTEM,
    AGENTIP,
    AGENTPORT,
    IAGENTHOSTNAME,
    IAGENT_IPASNAME,
    ISONLINE
)
SELECT  A.OPTID,
        A.FLOW_ID,
        B.IFLOWNAME,
        B.IFLOWINSNAME,
        B.IPROJECTNAME,
        LTRIM(B.ISTATUS),
        B.ISTARTUSERFULLNAME,
        (CASE WHEN B.ISTARTTIME > 0 THEN FUN_GET_DATE_STRING(B.ISTARTTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
        (CASE WHEN B.IENDTIME > 0 THEN FUN_GET_DATE_STRING(B.IENDTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
        (
            CASE  WHEN  B.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN CONCAT(FLOOR(LN_MILLISEC - B.ISTARTTIME) ,'')
                  WHEN  B.ISTATUS IN (2, 4, 7) THEN CONCAT(FLOOR(B.IENDTIME - B.ISTARTTIME),'')
                  ELSE  '0'
                END
            ),
        B.IHOSTNAME,
        A.TASK_NUM,
        A.REC_ID ,
        B.ISYSTEM,
        '','',
        (
            SELECT
                T2.IAGENT_NAME
            FROM
                TEMP_IEAI_REMOTEEXECACT T1 ,
                IEAI_AGENTINFO T2
            WHERE
                    T2.IAGENT_IP= T1.IAGENTHOST AND
                    T2.IAGENT_PORT=T1.IAGENTPORT AND
                    T1.IFLOWID=A.FLOW_ID
            LIMIT 1
    ),
	      (
	      SELECT
	        IAGENTHOST
	      FROM
	        TEMP_IEAI_REMOTEEXECACT
	      WHERE
	        IFLOWID=A.FLOW_ID
	       LIMIT 1
	      ),
	    FUN_CHECK_ONLINE(B.IFLOWNAME,B.IPROJECTNAME)
FROM  TMP_WORKFLOW_QUERY_TEMP A,
    IEAI_WORKFLOWINSTANCE B
WHERE  A.OPTID = LN_OPT_ID
  AND  B.IFLOWID = A.FLOW_ID
ORDER  BY A.REC_ID;
END  IF;
END ;;
	DELIMITER ;