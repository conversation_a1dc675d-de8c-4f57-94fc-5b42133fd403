package com.ideal.ieai.server.engine.util;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.execactivity.ExecAct;

/**
 * <li>Title: MemoryBranchManagerRepository.java</li>
 * <li>Project: server</li>
 * <li>Package: com.ideal.ieai.server.engine.util</li>
 * <li>Description: This class is control the branch infomaction of effcient flow which in memory.<li>
 * <li>Copyright: Copyright (c) 2006</li>
 * <li>Company: IdealTechnologies </li>
 * <li>Created on 07-Jun-2006, 14:58:12</li>
 * 
 * <AUTHOR> liancheng_gu
 * @version iEAI v3.5
 */
public class MemoryBranchManagerRepository implements IBranchManagerRepository
{
    private Map     _structInfos      = new HashMap();
    private Map     _indepBranchInfos = new HashMap();
    private boolean _debug            = false;

    public MemoryBranchManagerRepository()
    {
        if (_debug)
        {
            new Thread()
            {
                public void run ()
                {
                    while (true)
                    {
                        System.out.println(_structInfos);
                        System.out.println(_indepBranchInfos);
                        try
                        {
                            sleep(3000);
                        } catch (InterruptedException ex)
                        {
                            // no code.
                        }
                    }
                }
            }.start();
        }
    }

    public void saveStructInfo ( StructInfo info )
    {
        synchronized (_structInfos)
        {
            _structInfos.put(new Long(info.getScopeId()), info);
        }
    }

    public StructInfo getStructInfo ( long scopeId )
    {
        synchronized (_structInfos)
        {
            return (StructInfo) _structInfos.get(new Long(scopeId));
        }
    }

    public int getStructRunningBranchNum ( long scopeId )
    {
        synchronized (_structInfos)
        {
            StructInfo structInfo = (StructInfo) _structInfos.get(new Long(
                    scopeId));
            if (null == structInfo)
            {
                throw new IllegalStateException();
            }
            int ret = 0;
            Iterator iter = structInfo.getBranchInfos().values().iterator();
            while (iter.hasNext())
            {
                BranchInfo branchInfo = (BranchInfo) iter.next();
                if (!branchInfo.isFinish())
                {
                    ret++;
                }
            }
            return ret;
        }
    }

    public void updateStructBranchFinish ( long structScopeId, long scopeId )
    {
        synchronized (_structInfos)
        {
            StructInfo structInfo = (StructInfo) _structInfos.get(new Long(
                    structScopeId));
            BranchInfo branchInfo = (BranchInfo) structInfo.getBranchInfos()
                    .get(new Long(scopeId));
            branchInfo.setFinish(true);
        }
    }

    public void updateStructTimeOut ( long structScopeId )
    {
        synchronized (_structInfos)
        {
            StructInfo structInfo = (StructInfo) _structInfos.get(new Long(
                    structScopeId));
            if(null==structInfo)
            {
                return;
            }
            structInfo.setTimeOut(true);
            structInfo.addRetryTimes();
        }
    }

    public void releaseStructInfo ( long scopeId )
    {
        synchronized (_structInfos)
        {
            _structInfos.remove(new Long(scopeId));
        }
    }

    public void saveIndepBranchInfo ( BranchInfo branchInfo )
    {
        synchronized (_indepBranchInfos)
        {
            _indepBranchInfos
                    .put(new Long(branchInfo.getScopeId()), branchInfo);
        }
    }

    public void releaseIndepBranchInfo ( long scopeId )
    {
        synchronized (_indepBranchInfos)
        {
            _indepBranchInfos.remove(new Long(scopeId));
        }
    }

    public List getAllIndepBranchScopeIds ( long flowId )
    {
        List ret = new ArrayList();
        synchronized (_indepBranchInfos)
        {
            Iterator iter = _indepBranchInfos.values().iterator();
            while (iter.hasNext())
            {
                BranchInfo branchInfo = (BranchInfo) iter.next();
                ret.add(new Long(branchInfo.getScopeId()));
            }
            return ret;
        }
    }

    public boolean hasIndepBranch ( long flowId )
    {
        synchronized (_indepBranchInfos)
        {
            Iterator iter = _indepBranchInfos.values().iterator();
            while (iter.hasNext())
            {
                BranchInfo branchInfo = (BranchInfo) iter.next();
                if (branchInfo.getFlowId() == flowId)
                {
                    return true;
                }
            }
            return false;
        }
    }

    public boolean isIndependentBranch ( long scopeId )
    {
        synchronized (_indepBranchInfos)
        {
            return _indepBranchInfos.keySet().contains(new Long(scopeId));
        }
    }

    public void saveBranchScope ( BranchScope branchScope )
    {
        /**
         * It is useless for the class.
         */
        throw new UnsupportedOperationException();
    }

    public void markTransactionCommit ( long scopeId, long entryActId )
    {
        /**
         * @todo it is a ExecAct operation. it is for disaster recovery.
         */
        throw new UnsupportedOperationException();
    }

    public void markTransactionRollBack ( long scopeId, long entryActId )
    {
        /**
         * @todo it is a ExecAct operation. it is for disaster recovery.
         */
        throw new UnsupportedOperationException();

    }

    public void markTrStructOutputSaved ( long scopeId, long entryActId )
    {
        /**
         * @todo it is a ExecAct operation. it is for disaster recovery.
         */
        throw new UnsupportedOperationException();
    }

    public ExecAct markTrStructException ( long scopeId, long actId )
    {
        /**
         * @todo it is a ExecAct operation. it is for disaster recovery.
         */
        throw new UnsupportedOperationException();
    }

    public void saveBranchScope ( BranchScope branchScope, Connection con ) throws ServerException
    {
        // TODO Auto-generated method stub
        
    }
}
