<?xml version="1.0"?>

<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 2.0//EN" 
    "http://hibernate.sourceforge.net/hibernate-mapping-2.0.dtd">

<hibernate-mapping>
    <class
        name="com.ideal.ieai.server.repository.project.RepAdaptorHistory"
        table="ieai_adphistory"
        dynamic-update="false"
        dynamic-insert="false"
    >

        <id
            name="id"
            column="iid"
            type="java.lang.Long"
        >
            <generator class="com.ideal.ieai.server.repository.idgenerator.IdGenerator">
            </generator>
        </id>

        <property
            name="userFullName"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="iuser"
        />

        <property
            name="action"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="iaction"
        />

        <property
            name="actionTime"
            type="long"
            update="true"
            insert="true"
            access="property"
            column="iactionTime"
        />

        <property
            name="adaptorName"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="iadpname"
        />

        <property
            name="uploadNum"
            type="java.lang.Integer"
            update="true"
            insert="true"
            access="property"
            column="iuploadnum"
        />

        <property
            name="uuid"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="iuuid"
            length="36"
        />

        <property
            name="userId"
            type="java.lang.Long"
            update="true"
            insert="true"
            access="property"
            column="iuserid"
        />

        <!--
            To add non XDoclet property mappings, create a file named
                hibernate-properties-RepAdaptorHistory.xml
            containing the additional properties and place it in your merge dir.
        -->

    </class>

</hibernate-mapping>
