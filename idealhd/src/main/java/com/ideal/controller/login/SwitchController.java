package com.ideal.controller.login;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
public class SwitchController {
    private static final Logger _log = Logger.getLogger(SwitchController.class);
    protected static SwitchController _env = null;

    static {
        _env = new SwitchController();
    }

    @RequestMapping("switch.do")
    @ResponseBody
    public String setSwitch(HttpServletRequest request) throws Exception {
        if (Environment.getInstance().getYcUpadateSwitch()) {
            String switchValue = request.getParameter("switch");
            boolean newSwitchValue = Boolean.parseBoolean(switchValue);
            int intValue = newSwitchValue ? 1 : 0;
            changeSwitch(String.valueOf(intValue));
            _log.info("配置文件yc.login.redirect.switch开关为：" + Environment.getInstance().getYcSumPSwitch());
            _log.info("yc.login.redirect.switch开关已经更新为：" + SwitchController.getInstance().getYcSumPSwitch());
            return "yc.login.redirect.switch is set to: " + newSwitchValue;
        } else {
            return "未启用yc.update.switch开关！";
        }
    }
    public boolean getYcSumPSwitch() throws Exception {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "select iparavalue from ieai_parameter_config a where a.iparameter='YCupdateSwitch'";
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        String switchresult = "";
        boolean result = false;
        try {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                switchresult = rs.getString("iparavalue");
                if ("1".equals(switchresult)) {
                    result = true;
                } else {
                    result = false;
                }
            }
        } catch (Exception e) {
            _log.error("getYcSumPSwitch is error", e);
            throw new Exception("getYcSumPSwitch is error" + e.getMessage(), e);
        } finally {
            DBResource.closeConn(conn, rs, ps, method, _log);
        }
        return result;
    }

    public void changeSwitch(String changeswitch) throws Exception {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String querysql = "select iid,iparameter,iparavalue from ieai_parameter_config a where a.iparameter='YCupdateSwitch'";
        String updatesql = "update ieai_parameter_config set iparavalue=? where iparameter='YCupdateSwitch'";
        Connection conn = null;
        try {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
            List parameterlist = getParameterConfig(conn, querysql);
            if (!parameterlist.isEmpty()) {
                updateSwitch(conn, changeswitch, updatesql);
            }
        } catch (Exception e) {
            _log.error("changeSwitch is error!", e);
            throw new Exception("changeSwitch is error!" + e.getMessage(), e);
        } finally {
            if (conn != null) {
                DBResource.closeConnection(conn, method, _log);
            }
        }
    }
    public List<Map> getParameterConfig(Connection conn, String querysql) throws Exception {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map> list = new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(querysql);
            rs = ps.executeQuery();
            while (rs.next()) {
                Map map = new HashMap();
                map.put("iid", rs.getLong("iid"));
                map.put("iparameter", rs.getString("iparameter"));
                map.put("iparavalue", rs.getString("iparavalue"));
                list.add(map);
            }

        } catch (Exception e) {
            _log.error("getParameterConfig is error!", e);
            throw new Exception("getParameterConfig is error!" + e.getMessage(), e);
        } finally {
            DBResource.closePSRS(rs, ps, method, _log);
        }
        return list;
    }

    public void updateSwitch(Connection conn, String changeswitch, String updatesql) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(updatesql);
            ps.setString(1, changeswitch);
            ps.executeUpdate();
            conn.commit();
        } catch (SQLException e) {
            throw new RuntimeException("updateSwitch is error!" + e.getMessage(), e);
        } finally {
            DBResource.closePreparedStatement(ps, method, _log);
        }
    }

    public static SwitchController getInstance() {
        if (null == _env) {
            throw new java.lang.IllegalStateException("SwitchController is not initialized!");
        }
        return _env;
    }


}
