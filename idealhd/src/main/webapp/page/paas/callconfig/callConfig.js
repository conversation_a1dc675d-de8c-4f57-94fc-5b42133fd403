var addOrderWin;
Ext.onReady(function() {
    //清理主面板的各种监听时间
    destroyRubbish();
    var pagelimit = 30;
    Ext.define('callConfigModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'iid',
            type : 'Long'
        }, {
            name : 'ienvtypename',
            type : 'string'
        },{
            name : 'imoduletype',
            type : 'string'
        },{
            name : 'icommunicatetype',
            type : 'int'
        },{
            name :'icommunicateaddress',
            type:'string'
        },{
            name :'ioperant',
            type:'string'
        }]
    });

    Array.prototype.baoremove = function(dx) {
        if (isNaN(dx) || dx > this.length) {
            return false
        }
        this.splice(dx, 1)
    };
    var callConfigStore = Ext.create('Ext.data.Store',{
        autoLoad : true,
        autoDestroy : true,
        model : 'callConfigModel',
        proxy : {
            type : 'ajax',
            url : 'getCallConfigBeanList.do',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    });

    var pagebar = Ext.create('Ext.PagingToolbar', {
        pageSize:pagelimit,
        dock: 'bottom',
        id: 'pageBarId',
        store:callConfigStore,
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        displayMsg: '显示 {0}-{1}条记录，共 {2} 条',
        emptyMsg: "没有记录"
    });

    Ext.define('templateTypeModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'groupId',
            type : 'Long'
        }, {
            name : 'groupName',
            type : 'string'
        }]
    });

    // 3	变更管理
    // 4	灾备切换
    // 6	应急操作
    // 26	中台云管
    // 24	网络自动化
    // 25  组合模板
    var templateTypeStore = Ext.create('Ext.data.Store', {
        fields : [ 'id', 'name' ],
        data : [ {
            "id" : "变更管理",
            "name" : "变更管理"
        }, {
            "id" : "灾备切换",
            "name" : "灾备切换"
        }, {
            "id" : "应急操作",
            "name" : "应急操作"
        }, {
            "id" : "中台云管",
            "name" : "中台云管"
        } , {
            "id" : "网络自动化",
            "name" : "网络自动化"
        },
        {
            "id" : "组合模板",
            "name" : "组合模板"
        }]
    });

    var operantStore = Ext.create('Ext.data.Store', {
        fields : [ 'id', 'name' ],
        data : [ {
            "id" : "1",
            "name" : "启用"
        }, {
            "id" : "0",
            "name" : "禁用"
        } ]
    });
    // var templateTypeStore = Ext.create('Ext.data.Store',{
    //     autoLoad : true,
    //     autoDestroy : true,
    //     model : 'callConfigModel',
    //     proxy : {
    //         type : 'ajax',
    //         url : 'getModuleList.do',
    //         reader : {
    //             type : 'json',
    //             root : 'dataList'
    //         }
    //     }
    // });

    templateTypeStore.on('beforeload', function(store, options) {
        var new_params =
        {

        };
        Ext.apply(store.proxy.extraParams, new_params);
    });

    var callConfigColumns = [
        {text: '序号', dataIndex : 'callConfigNo',width: 50, sortable: false,
            renderer: function (value, metadata, record, rowIndex) {
                return rowIndex + 1; // 获取序号，方法2
            }
        },
        {text : '环境ID', dataIndex : 'iid', hidden:true,width:350,editor:{allowBlank:false}},
        {text : '环境名称', dataIndex : 'ienvtypename', width:350,editor:{allowBlank:false}},
        {text : '模块类型',dataIndex : 'imoduletype', hidden:true,width:350,editor:{allowBlank:false},
            editor : new Ext.form.field.ComboBox({
                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
                editable : false,// 是否可输入编辑
                store : templateTypeStore,
                displayField : 'name',
                valueField : 'id'
            }),
            renderer : function(value, metaData, record) {
                if (value == 3) {
                    return "变更管理";
                } else if (value == 4) {
                    return "灾备切换";
                }else if (value == 6){
                    return "应急操作";
                }else if (value == 26)
                {
                    return "中台云管";
                }else if (value == 24)
                {
                    return "网络自动化";
                }else if (value == 25)
                {
                    return "组合模板";
                }else
                {
                    return value;
                }
            }
        },
        {text : '通信类型',dataIndex : 'icommunicatetype',hidden:true,width:200,editor:{allowBlank:false}},
        {text : '通信地址',dataIndex : 'icommunicateaddress',width:500,editor:{allowBlank:false}},
        {text : '是否生效',dataIndex : 'ioperant', width:120, editor:{allowBlank:false},
            editor : new Ext.form.field.ComboBox({
                triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
                editable : false,// 是否可输入编辑
                store : operantStore,
                displayField : 'name',
                valueField : 'id'
            }),
            renderer : function(value, metaData, record) {
                if (value == 1) {
                    return "启用";
                } else
                {
                    return "禁用";
                }
            }}
    ];

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    var formMainPanel = Ext.create('Ext.form.Panel', {
        region : 'north',
        layout : 'anchor',
        buttonAlign : 'center',
        iselect : false,
        bodyCls : 'x-docked-noborder-top',
        baseCls:'customize_gray_back',
        border: false,
        dockedItems : [
            {
                xtype : 'toolbar',
                baseCls:'customize_gray_back',
                items : [
                    {
                        fieldLabel: '环境名称',
                        name:'iEnvTypeName',
                        labelAlign : 'right',
                        width : '23%',
                        labelWidth : 70,
                        xtype: 'textfield'
                    },
                    {
                        text : '查询',
                        cls : 'Common_Btn',
                        handler : function(){
                            //orderStore.load();
                            queryBtnFun();
                        }
                    },'->',
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '增加',
                        handler:function(){
                            var store = callConfigGrid.getStore();
                            var p = {
                                iEnvTypeName: '',
                                iCommunicateType: 1,
                                ioperant: 1
                            };
                            store.insert(0, p);
                            callConfigGrid.getView().refresh();
                        }
                    },/*{
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '启用',
                        handler:function(){
                           alert("启用暂未开发");
                        }
                    },{
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '禁用',
                        handler:function(){
                            alert("禁用暂未开发");
                        }
                    },*/
                    {
                        xtype: 'button',
                        cls: 'Common_Btn',
                        text: '删除',
                        handler: function() {
                            deleteCallConfigInfo()
                        }
                    },
                    {
                         xtype: 'button',
                         cls: 'Common_Btn',
                         text: '保存',
                         handler: function() {
                             saveCallConfigBean()
                         }
                     }
                ]
            }
        ]
    });

    var callConfigGrid = Ext.create('Ext.grid.Panel',{
        width: contentPanel.getWidth(),
        cls:'customize_panel_back',
        region : 'center',
        forceFit: true,
        store : callConfigStore,
        selModel : Ext.create('Ext.selection.CheckboxModel',{checkOnly:true}),
        plugins: [cellEditing],
        border : false,
        columnLines : true,
        columns : callConfigColumns,
        animCollapse : false,
        autoScroll:true,
        bbar : pagebar,
        listeners : {
            beforeedit : function(editor, e, eOpts){
                /*if(e.field == 'errcode' && e.record.data.errcode!=""){
                    return false;
                }else{
                    return true;
                }*/
                return true;
            },
            cellclick : function(grid, rowIndex, columnIndex, e) {
                if(columnIndex == 5) {
                    return false;
                }
            }
        }
    });

    callConfigStore.on('beforeload',function (store,options){
        var iEnvTypeName = formMainPanel.getForm().findField("iEnvTypeName").getValue();
        Ext.apply(callConfigStore.proxy.extraParams,{
            queryString:iEnvTypeName
        });
    });


    var mainPanel =  Ext.create('Ext.panel.Panel',{
        renderTo : "order_callconfig_area",
        border : false,
        layout : 'border',
        bodyPadding : 0,
        width : contentPanel.getWidth (),
        height : contentPanel.getHeight ()-modelHeigth,
        items : [formMainPanel,callConfigGrid]
    });

    contentPanel.on('resize',function(){
        mainPanel.setHeight(contentPanel.getHeight()-5);
    })

    callConfigGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        // mainPanel.down('#delete').setDisabled(selections.length === 0);
        // console.log(selModel);
        // console.log(selections);
    });


    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
        contentPanel.getHeader().show();
        Ext.destroy(callConfigGrid);
        if(Ext.isIE){
            CollectGarbage();
        }
    });

    /* 解决IE下trim问题 */
    String.prototype.trim=function(){
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function queryBtnFun() {
        if (Ext.isIE) {
            CollectGarbage();
        }
        pagebar.moveFirst();
    }

    function deleteCallConfigInfo() {
        var records = callConfigGrid.getSelectionModel().getSelection();
        if (records.length == 0) {
            Ext.MessageBox.alert('提示', '请选择要删除的数据!');
            return
        } else {
            Ext.Msg.confirm('请确认', '是否真的要删除数据？',
                function(button, text) {
                    if (button == 'yes') {
                        var iidStr = "";
                        Ext.Array.each (records, function (recordObj)
                        {
                            iidStr+=recordObj.get ('iid')+",";
                        });

                        if ("" != iidStr && "," != iidStr) {
                            Ext.Ajax.request({
                                url : 'deleteCallConfigBean.do',//scriptService/cancelShare.do
                                method : 'POST',
                                params : {
                                    deleteIds:iidStr
                                },
                                success: function(response, opts) {
                                    var message = '删除成功！';
                                    Ext.MessageBox.show({
                                        title : "提示",
                                        msg : message,
                                        buttonText: {
                                            yes: '确定'
                                        },
                                        buttons: Ext.Msg.YES
                                    });
                                    callConfigStore.reload();
                                },
                                failure: function(result, request) {
                                    secureFilterRs(result,"操作失败！");
                                }
                            });
                        } else {
                            for(var i = 0,len = records.length;i<len;i++ ){
                                callConfigGrid.store.remove(records[i]);
                            }
                        }
                    }
                });
        }
    }
    function saveCallConfigBean() {
        var m = callConfigStore.getModifiedRecords();
        if (m.length < 1) {
            Ext.Msg.alert('提示', '无修改数据！');
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var envTypeName = m[i].get("ienvtypename").trim();
            var iCommunicateAddress = m[i].get("icommunicateaddress").trim();
            var itype = m[i].get("imoduletype").trim();
            if(envTypeName=='')
            {
                Ext.Msg.alert('提示', '第'+ (i + 1) + '行环境名称不能为空!');
                return;
            }
            if (fucCheckLength(envTypeName)> 255) {
                Ext.Msg.alert('提示', '第'+ (i + 1) + '行环境名称不能超过255字符!');
                return;
            }
            if(iCommunicateAddress=='')
            {
                Ext.Msg.alert('提示', '第'+ (i + 1) +'行通信地址不能为空!');
                return;
            }
            if (fucCheckLength(iCommunicateAddress)> 255) {
                Ext.Msg.alert('提示', '第'+ (i + 1) +'行通信地址不能超过255字符!');
                return;
            }

            // if (itype=='') {
            //     Ext.Msg.alert('提示', '第'+i+'行未选择模块类型!');
            //     return;
            // }
            if (itype== "变更管理") {
                m[i].set("imoduletype",3);
            }else if (itype== "灾备切换") {
                m[i].set("imoduletype",4);
            }else if (itype== "应急操作"){
                m[i].set("imoduletype",6);
            }else if (itype== "中台云管")
            {
                m[i].set("imoduletype",26);
            }else if (itype== "网络自动化")
            {
                m[i].set("imoduletype",24);
            }else if (itype== "组合模板")
            {
                m[i].set("imoduletype",25);
            } else {
                m[i].set("imoduletype",-1);
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0)
                jsonData = jsonData + ss;
            else
                jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";
        var saveUrl = "saveCallConfigBean.do";
        Ext.MessageBox.wait("数据处理中...", "进度条");
        Ext.Ajax.request({
            url : saveUrl,
            timeout : 30000,
            params : {
                jsonData : jsonData,
            },
            method : 'POST',
            success : function(response, opts) {
                var success = Ext.decode(response.responseText).success;
                // 当后台数据同步成功时
                if (success) {
                    pagebar.moveFirst();
                }
                Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
            },
            failure : function(response, ooptions) {
                Ext.MessageBox.hide();
                secureFilterRs(response,"操作失败！");
            }
        });
    }

//当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
// //  	addForm.getForm().reset();
//         Ext.destroy (addOrderWin);
        Ext.destroy (mainPanel);
        contentPanel.getHeader().show();
        if(Ext.isIE){
            CollectGarbage();
        }
    });
});
