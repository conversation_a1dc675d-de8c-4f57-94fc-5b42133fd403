@charset "utf-8";
/* CSS Document */
html,body{
	height:100%;
}
body{ 
	padding:0; margin:0;
	width:100%;
	height:100%;
	margin:0 auto;
}
.body_bg{ 
	width:1920px;
	height:1079px;
	background:#011659;
	background-image: -webkit-linear-gradient(top, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
  	background-image: -moz-linear-gradient(top, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
  	background-image: -o-linear-gradient(top, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
  	background-image: linear-gradient(to bottom, #011659 0%, #011659 20%, #013ba9 80%, #116cd9 100%);
	font-family:Microsoft YaHei;
	}
.overview_logo{
	height:70px;
	line-height:70px;
	text-align:center;
	border-bottom:1px solid #0079cb;
	font-size:30px;
	color:#fff;
	}
.overview_logo_width{
	width:410px;
	margin:0 auto;
}
.PA_logo{
	background-image:url(../images_bx_1920/overvie_logo_bx.png);
	width:138px;
	height:60px;
	margin:5px 20px 0 0;
	}
.overview_logo span{
	float:left;
	}
.overview_title{
	font-size:20px;
	color:#00fffe;
	padding:10px 0;
	margin:0 auto;
	width:1700px;
	}
.marquee_w{
	width:1700px;
	margin:auto;
}
.overview_map{
	background-image:url(../images_bx_1920/overview_img1_bx.png);
	width:1700px;
	height:489px;
	margin:0 auto;
	}
.overview_table{
	margin:125px 0 0 88px;
	color:#fff;
	font-size:13px;
	font-weight:bold;
	float:left;
	/*filter:alpha(opacity=30);
	opacity:0.3;*/
	}
.overview_table2{
	color:#fff;
	font-size:13px;
	font-weight:bold;
	float:left;
	margin:125px 0 0 356px;
	/*filter:alpha(opacity=30);
	opacity:0.3;*/
	
	}
.overview_table3{
	color:#fff;
	font-size:13px;
	font-weight:bold;
	float:left;
	margin:125px 0 0 356px;
	display:none;
	}
.triangle_body{
	position:relative;
	display:block; 
	width:232px; 
	height:111px;
}
.triangle{
	background:url(../images_bx_1920/triangle.png) no-repeat bottom; 
	float:left;
	width:232px;
	height:111px;
	margin:378px 0 0 354px;
	}
.triangle_load{
	background:url(../images_bx_1920/loading.png) no-repeat bottom; 
	width:100%;
	height:100%;
	position:relative;
	z-index:0;
	}
.load_value{
	font-size:28px;
	text-align:center;
	color:#fff;
	position:absolute;
	z-index:1000;
	float:left;
	margin:40px 0 0 65px;
	width:100px;
	}
.overview_tab_title{ 
	background-image:url(../images_bx_1920/overview_tab_title.png);
	width:291px;
	height:33px;
	}
.overview_tab_title span{
	padding:8px 0 0 27px;
	display:block
	}
.overview_con{
	border-left:1px solid #00b6d6;
	border-right:1px solid #00b6d6;
	border-bottom:1px solid #00b6d6;
	width:289px;
	height:auto;
	}
.overview_con table{
	background-color:#0062b1;
	width:100%;
	font-size:13px;
	}
.overview_con table tr{
	background-color:#004591;
	height:26px;
	}
.overview_con table tr td{
	width:50%
	}
.overview_con table tr td span{ margin:0 0 0 35px;}

.overview_con2{
	border-left:1px solid #00b6d6;
	border-right:1px solid #00b6d6;
	border-bottom:1px solid #00b6d6;
	width:289px;
	height:auto;
	}
.overview_con2 table{
	background-color:#0062b1;
	width:100%;
	font-size:13px;
	}
.overview_con2 table tr{
	background-color:#004591;
	height:26px;
	}
.overview_con2 table tr td{
	width:50%
	}
.overview_con2 table tr td span{ margin:0 0 0 35px;}



.overview_green{
	color:#24fc00;
	font-weight:bold
	}
.overview_yellow{
	color:#fff100;
	font-weight:bold
	}
.overview_gray{
	color:#c6c7b9;
	font-weight:bold
	}
.overview_red{
	color:#fe0300;
	font-weight:bold
	}
.overview_blue{
	color:#00fffe;
	font-weight:bold
	}
.overview_chart{
	width:1700px;
	margin:0 auto;
	padding:15px 0;
	height:180px;
	}
.chart_sb{
	background-image:url(../images_bx_1920/overview_chart.png);
	width:514px;
	height:139px;
	float:left;
	margin:20px 79px 0 0;
	}
.chart_sb_01{
	width:144px;
	float:left;
	color:#fff;
	font-size:13px;
	}
.chart_list3{
	width:100%;
	height:30px;
	float:left;
	}
.chart_list4{
	width:100%;
	height:30px;
	float:left;
	margin:60px 0 0 40px;
	}
.chart_sb_01 span{
	float:left;
	}
.chart_count{
	margin:0 8px 0 8px;
	width:15px;
	}
.chart_icon{
	background-position:0 0;
	}
.chart_icon2{
	background-position:0 -14px;
	}
.chart_icon3{
	background-position:0 -28px;
	}
.chart_icon4{
	background-position:0 -42px;
	}
.chart_icon_common{
	background-image:url(../images_bx_1920/overview_circle.png);
	width:14px;
	height:14px;
	display:block;
	margin:3px 10px 0 10px;
	float:left;
	}
.chart_sb_02{
	width:190px;
	height:190px;
	float:left;
	margin:-31px 0 0 9px;
	font-size:14px;
	font-weight:bold;
	text-align:center;
	color:#fff;
	line-height:2;
	}
.chart_sb_02 span{
	display:block;
	margin:40px 0 0 0;
	}
.chart_sb_02_xt{
	width:190px;
	height:190px;
	float:left;
	margin:-31px 0 0 -10px;
	font-size:14px;
	font-weight:bold;
	text-align:center;
	color:#fff;
	line-height:2;
	}
.chart_sb_02_xt span{
	display:block;
	margin:40px 0 0 0;
	}
.chart_sb_03{
	width:115px;
	height:auto;
	float:left;
	color:#fff;
	font-size:13px;
	padding:45px 0 0 10px;
	}
.chart_sb_03 span{
	float:left;
	}
.chart_sb_04{
	width:124px;
	height:auto;
	float:left;
	color:#fff;
	font-size:13px;
	padding:45px 0 0 40px;
	}
.chart_list1{
	width:100%;
	height:30px;
	}
.chart_sb_04 span{
	float:left;
	}
.chart_sb_05{
	width:115px;
	height:auto;
	float:left;
	color:#fff;
	font-size:13px;
	padding:45px 0 0 9px;
	}
.chart_sb_05 span{
	float:left;
	}
.chart_name{
	width:40px;
	}
.overview_time{
	background-image:url(../images_bx_1920/overview_time.png);
	width:514px;
	height:139px;
	float:left;
	margin:20px 0 0 0;
	color:#fff;
	}
.time_text{
	font-size:20px;
	font-weight:bold;
	margin:50px 0 0 75px;
	float:left;
	}
.time_run{
	float:left;
	font-family:Digiface;
	font-size:40px;
	}
.time_run span{ 
	display:block;
	float:left;
	width:24px;
	height:31px;
	text-align:center;
	line-height:28px;
	}
.run_first{
	margin:55px 0 0 58px;
	}
.run_two{
	margin:55px 0 0 13px;
	}
.run_three{
	margin:55px 0 0 42px;
	}
.oview_table_info{
	color:#fff;
	font-size:13px;
	width:1700px;
	margin:0 auto;
	clear:both;
	padding:0 0 15px 0;
	height:225px;
	overflow:auto;
	}
.oview_table_list{
	width:100%;
	background-color:#003bad;
	border-top:1px solid #037fcf;
	border-left:1px solid #037fcf;
	border-right:1px solid #037fcf;
	}
.oview_table_list tr td{
	padding:0 0 0 10px;
	border-bottom:1px solid #037fcf;
	}
.tab_info_tr1{
	height:30px;
	background-color:#0076c6;
	font-weight:bold;
	}
.tab_info_tr2{
	height:30px;
	}
.chart_Font{
	position:absolute;
	z-index:1000;
	margin:-117px 0 0 66px;
}
.tab_step{
	width: 300px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
/*screen*/
.screen_body{
	background:rgba(0,0,0,0.6);
	width:100%;
	height:100%;
	margin:0;
	padding:0;
	color:#fff;
	font-family:Microsoft YaHei;
	}
.screen_p1{
	background-image:url(../images_bx_1920/screen_p1.png);
	width:86px;
	height:25px;
	}
.screen_p2{
	background-image:url(../images_bx_1920/screen_p2.png);
	height:25px;
	background-repeat:repeat-x;
	}
.screen_p3{
	background-image:url(../images_bx_1920/screen_p3.png);
	width:86px;
	height:25px;
	}
.screen_p4{
	background-image:url(../images_bx_1920/screen_p4.png);
	background-repeat:repeat-y;
	width:27px;
	}
.screen_p5{
	background-image:url(../images_bx_1920/screen_p5.png);
	background-repeat:repeat;
	}
.screen_p6{
	background-image:url(../images_bx_1920/screen_p6.png);
	width:27px;
	background-repeat:repeat-y;
	}
.screen_p7{
	background-image:url(../images_bx_1920/screen_p7.png);
	width:86px;
	height:25px;
	}
.screen_p8{
	background-image:url(../images_bx_1920/screen_p8.png);
	height:25px;
	background-repeat:repeat-x;
	}
.screen_p9{
	background-image:url(../images_bx_1920/screen_p9.png);
	width:86px;
	height:25px;
	}
.scr_title{
	width:100%;
	padding:5px 0;
	font-size:12px;
	}
.scr_tl_left{
	float:left;
	width:80%;
	}
.scr_tl_left a{
	color:#fff;
	font-size:14px;
}
.scr_tl_right{
	float:left;
	width:20%;
	}
.scr_img1{
	background-image:url(../images_bx_1920/screen_arrow.png);
	width:16px;
	height:16px;
	float:left;
	margin:0 10px 0 0;
	}
.scr_close{
	background-image:url(../images_bx_1920/screen_close.png);
	width:16px;
	height:16px;
	cursor:pointer;
	display:block;
	float:right
	}
.screen_table_info{
	width:100%;
	font-size:12px;
	padding:20px 0;
	float:left;
	}
.screen_table_info2{
	width:100%;
	height:260px;
	font-size:12px;
	float:left;
	overflow-y:auto;
	overflow-x:hidden;
	}
.oview_table_list_tl{
	width:1045px;
	background-color:#003bad;
	border-top:1px solid #037fcf;
	border-left:1px solid #037fcf;
	border-right:1px solid #037fcf;
	}
.oview_table_list_tl tr td{
	padding:0 0 0 10px;
	border-bottom:1px solid #037fcf;
	color:#fff;
	font-size:12px;
	}
.oview_table_list2{
	width:1045px;
	background-color:#003bad;
	border-top:1px solid #037fcf;
	border-left:1px solid #037fcf;
	border-right:1px solid #037fcf;
	overflow-x:hidden;
	}
.oview_table_list2 tr td{
	border-bottom:1px solid #037fcf;
	color:#fff;
	font-size:12px;
	}
.start_td{
	width:50px;
	padding:0 0 0 10px;
}
.start_td2{
	width:100px;
	padding:0 0 0 10px;
}
.start_td3{
	width:150px;
	padding:0 0 0 10px;
}
.start_td4{
	width:150px;
	padding:0 0 0 0;
}



@keyframes warn {
    0% {
        transform: scale(0);
        opacity: 0.0;
    }
    25% {
        transform: scale(0);
        opacity: 0.3;
    }
    50% {
        transform: scale(0.1);
        opacity: 0.5;
    }
    75% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.0;
    }
}
@-webkit-keyframes "warn" {
    0% {
        -webkit-transform: scale(0);
        opacity: 0.0;
    }
    25% {
        -webkit-transform: scale(0);
        opacity: 0.3;
    }
    50% {
        -webkit-transform: scale(0.1);
        opacity: 0.5;
    }
    75% {
        -webkit-transform: scale(0.5);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 0.0;
    }
}
.img1_location{
	margin:178px 0 0 940px;
	position:absolute
	}
.img2_location{
	margin:175px 0 0 955px;
	position:absolute
	}
.img3_location{
	margin:336px 0 0 993px;
	position:absolute;
	display:none;
	}

	
.animation {
    position: relative;
	width:80px;
	height:80px;
}
.animation .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.animation .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.animation .pulse {
    position: absolute;
	top:0;
	left:0;
    width: 60px; 
    height: 60px;
	background-color: rgba(255, 255, 255,0);
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border-radius: 100px;
	-webkit-box-shadow:inset 0 0 50px #24fc00;  
 	-moz-box-shadow:inset 0 0 50px #24fc00;  
    box-shadow:inset 0 0 50px #24fc00; 
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.animation02 {
    position: relative;
	width:80px;
	height:80px;
}
.animation02 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.animation02 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.animation02 .pulse {
    position: absolute;
	top:0;
	left:0;
    width: 60px; 
    height: 60px;
	background-color: rgba(255, 255, 255,0);
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border-radius: 100px;
	-webkit-box-shadow:inset 0 0 50px #24fc00;  
 	-moz-box-shadow:inset 0 0 50px #24fc00;  
    box-shadow:inset 0 0 50px #24fc00; 
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.animation03 {
    position: relative;
	width:80px;
	height:80px;
}
.animation03 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.animation03 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.animation03 .pulse {
    position: absolute;
	top:0;
	left:0;
    width: 60px; 
    height: 60px;
	background-color: rgba(255, 255, 255,0);
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border-radius: 100px;
	-webkit-box-shadow:inset 0 0 50px #24fc00;  
 	-moz-box-shadow:inset 0 0 50px #24fc00;  
    box-shadow:inset 0 0 50px #24fc00; 
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

/*static*/
.static {
    position: relative;
	width:80px;
	height:80px;
}
.static .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.static .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.static .pulse {
}

.static02 {
    position: relative;
	width:80px;
	height:80px;
}
.static02 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.static02 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.static02 .pulse {
}

.static03 {
    position: relative;
	width:80px;
	height:80px;
}
.static03 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.static03 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.static03 .pulse {
}
.box{ animation: change 1s  ease-in  infinite ; font-size: 13px; color:#fff; font-weight: bold}
@keyframes change {
    0%{ text-shadow: 0 0 4px #fff}
    50%{ text-shadow: 0 0 40px #fff}
    100%{ text-shadow: 0 0 4px #fff}
}