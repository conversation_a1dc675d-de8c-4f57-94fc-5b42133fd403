<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/grapheditor/webstudio/act/datamapping/struct.js"></script>
<script type="text/javascript">
	var parentId = '<%=request.getParameter("parentId")%>';
	function INode(text,leaf,expanded,children){
		if(text)
		{
			if(leaf)
			{
				var arr = text.split(":");
				this.name = arr[0];
				this.text = text;
				this.type = arr[1];
				this.leaf = leaf;
				this.value = '';
			}else{
				this.name = text;
				this.text = '$'+text;
				this.leaf = leaf;
				this.expanded = expanded;
				this.children = children;
			}
			
		}
	}
</script>
</head>
<body>
<div id="shellcmd_config_div" style="width: 100%;height: 100%"></div>
</body>
</html>