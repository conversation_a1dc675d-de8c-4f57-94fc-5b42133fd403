/**
 * 
 */
Ext.define('page.common.window.common.openhttpWindow', {
	extend: 'Ext.window.Window',
	title: '活动信息详情',
	autoScroll: true,
	modal: true,
	closeAction: 'destroy',
	buttonAlign: 'center',
	draggable: true,
	resizable: false,
	width: 800,

	height: 600,

	layout: 'fit',


	initComponent: function () {

		var me = this;
		var isShowSql = me.isShowSql;
		var sqltype = false;
		if(isShowSql != undefined && (isShowSql == true || isShowSql == 'true')){
			sqltype = true;
		}
		
		var formPanelConfig = Ext.create('Ext.form.Panel', {
			border: false,
			layout:'anchor',
			
			
			items: [{
				labelAlign : 'left',fieldLabel : '活动名称',labelWidth : 100,xtype:'textfield',name:'actName',anchor:'100% 10%',readOnly:true
			},{
				labelAlign : 'left',hidden:!sqltype, fieldLabel : 'SQL语句',labelWidth : 100,xtype:'textfield',name:'sql',anchor:'100% 10%',readOnly:true
			},{
				labelAlign : 'left',fieldLabel : '活动输出信息',labelWidth : 100,xtype:'textareafield',name:'actout',anchor:'100% 80%',readOnly:true
			}]
		})

		Ext.applyIf(me, {
			items: [formPanelConfig],
		})



		me.callParent(arguments);

	}
});
