-- 4.7.17 version SCRIP<PERSON> patch is as follows 
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PLATFORM_CODE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_PLATFORM_CODE( IID DECIMAL(19,0) NOT NULL  , INAME VARCHAR(30), ICODEVALUE VARCHAR(30), CONSTRAINT PK_IEAI_PLATFORM_CODE  PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	 
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(1,''全部'',''全部'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(2,''HP-UX'',''HP-UX'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=3;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(3,''Unix'',''Unix'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=4;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(4,''Windows'',''Windows'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=5;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(5,''SUSE'',''SUSE'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=6;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(6,''RedHat'',''RedHat'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=7;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(7,''CentOS'',''CentOS'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=8;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(8,''Linux'',''Linux'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=9;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(9,''OracleLinux'',''OracleLinux'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$

-- 4.7.18 version script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	 
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_CONFIG_SWITCH WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_CONFIG_SWITCH (IID, INAME, IDES, ISTATE, ITYPE)VALUES (2, ''ISRELEASETOPRODUCT'', NULL, 1, 9)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_AG_BS';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_AG_BS(IID DECIMAL(15) NOT NULL,BSNAME     VARCHAR(255), IISEXTEND  INTEGER,ISHOWORDER INTEGER,CONSTRAINT  PK_AG_BS PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_AG_BSTYPE';
	IF LI_EXISTS = 0 THEN
	 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_AG_BSTYPE(IID DECIMAL(15) NOT NULL,  TYPENAME VARCHAR(255),  BSID DECIMAL(15),  CONSTRAINT  PK_AG_BSTYPE PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_LOGANALIZE';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_LOGANALIZE  ( IID  DECIMAL(19,0) NOT NULL,IOPERID    	DECIMAL(19,0) NULL,IKEY       	VARCHAR(200) NULL,IVALUE     	VARCHAR(200) NULL,IFLOWID    	DECIMAL(19,0) NULL,ICOATID    	DECIMAL(19,0) NULL,IINSTANCEID	DECIMAL(19,0) NULL,ISCRIPTID  	DECIMAL(19,0) NULL,IAGENTID   	DECIMAL(19,0) NULL,CONSTRAINT IEAI_SCRIPT_LOGANALIZE PRIMARY KEY(IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'IEXECUSER';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  IEXECUSER VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'IEXECUSER';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD  IEXECUSER VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND COLNAME = 'IEXECUSER';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SHARE ADD  IEXECUSER VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AGENT_GROUP' AND COLNAME = 'BSID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP ADD   BSID DECIMAL(15)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AGENT_GROUP' AND COLNAME = 'BSTYPEID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP ADD   BSTYPEID DECIMAL(15)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_ISSUERECORD' AND COLNAME = 'ISENDPATH';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD  ISENDPATH VARCHAR(255)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_SERVICES';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_SERVICES'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_TEST';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_TEST'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_SHARE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_SHARE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_AGENT_GROUP';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_AGENT_GROUP'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_ISSUERECORD';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_ISSUERECORD'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DOUBLECHECK_WORKITEM' AND COLNAME = 'IMODELTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD IMODELTYPE INTEGER DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLNAME = 'IMODELTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD IMODELTYPE INTEGER DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_INSTANCE' AND COLNAME = 'ISERVICEGID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_INSTANCE ADD ISERVICEGID DECIMAL(19,0) DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER_GROUP_RELATION' AND COLNAME = 'IROLE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_USER_GROUP_RELATION ADD IROLE INTEGER DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_VERRES' AND COLNAME = 'IFROM';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_VERRES ADD  IFROM INTEGER DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_OF_MY' AND COLNAME = 'IEXECTIMENEXT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD IEXECTIMENEXT VARCHAR(255)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_OF_MY' AND COLNAME = 'ISERVICEGID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD ISERVICEGID DECIMAL(19,0) DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_OF_MY' AND COLNAME = 'ISERVICEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD ISERVICEID DECIMAL(19,0) DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_OF_MY' AND COLNAME = 'IDELETED';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD IDELETED DECIMAL(1,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_INSTANCE_TEST' AND COLNAME = 'ISERVICEGID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_INSTANCE_TEST ADD ISERVICEGID  DECIMAL(19,0) DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPTS_COAT' AND COLNAME = 'ISERVICEGID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPTS_COAT ADD ISERVICEGID DECIMAL(19,0) DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPTS_COAT' AND COLNAME = 'IDBSTATE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPTS_COAT ADD IDBSTATE INTEGER DEFAULT 10';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLNAME = 'IGROUPS';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD IGROUPS DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER' AND COLNAME = 'IDEL';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SERVICESUB_USER ADD IDEL INTEGER DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_FLOW' AND COLNAME = 'ISERVICEGID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_FLOW ADD ISERVICEGID DECIMAL(19,0)  DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER_GROUP' AND COLNAME = 'IUSERID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_USER_GROUP ADD IUSERID DECIMAL(19) DEFAULT 0';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_S_G_RELATION';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_S_G_RELATION (IID DECIMAL(19,0) NOT NULL,ISERVICEGID DECIMAL(19,0),ISERVICEID  DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_S_G_RELATION PRIMARY KEY(IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_SERVICE_GROUP';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_SERVICE_GROUP (ICREATETIME DECIMAL(19,0),ICREATEUSERID DECIMAL(19,0),IFLAG INTEGER DEFAULT 0,IID DECIMAL(19,0) NOT NULL,INAME VARCHAR(255),INAMEDESC VARCHAR(255),IUPDATETIME DECIMAL(19,0),IUPDATEUSERID DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_SERVICE_GROUP PRIMARY KEY(IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DOUBLECHECK_SCRIPT_S_G ';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_DOUBLECHECK_SCRIPT_S_G(IID DECIMAL(19,0) NOT NULL,ISERVICEGID DECIMAL(19,0),ISERVICEID  DECIMAL(19,0),ISTATUS INTEGER DEFAULT 0,ITYPE   INTEGER DEFAULT 0,IWORKITEMID DECIMAL(19,0),CONSTRAINT PK_IEAI_DOUBLEC_SCRIPT_S_G PRIMARY KEY(IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_COLLECT_RESULT_COL';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_COLLECT_RESULT_COL(IID NUMERIC(19) NOT NULL,ITEMPLATEID NUMERIC(19), ICOLNAME VARCHAR(255),ICOL INTEGER,ICLLECTTIME TIMESTAMP(6),CONSTRAINT PK_IEAI_S_C_RESULT_COL PRIMARY KEY (IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_COLLECT_RESULT';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_COLLECT_RESULT(IID NUMERIC(19) NOT NULL,ITEMPLATEID NUMERIC(19),I1 VARCHAR(255), I2 VARCHAR(255), I3 VARCHAR(255), I4 VARCHAR(255), I5 VARCHAR(255), I6 VARCHAR(255), I7 VARCHAR(255), I8 VARCHAR(255), I9 VARCHAR(255), I10 VARCHAR(255), I11 VARCHAR(255), I12 VARCHAR(255), I13 VARCHAR(255), I14 VARCHAR(255), I15 VARCHAR(255), I16 VARCHAR(255), I17 VARCHAR(255), I18 VARCHAR(255), I19 VARCHAR(255), I20 VARCHAR(255), I21 VARCHAR(255), I22 VARCHAR(255), I23 VARCHAR(255), I24 VARCHAR(255), I25 VARCHAR(255), I26 VARCHAR(255), I27 VARCHAR(255), I28 VARCHAR(255), I29 VARCHAR(255), I30 VARCHAR(255), ICLLECTTIME TIMESTAMP(6),CONSTRAINT PK_IEAI_S_C_RESULT PRIMARY KEY (IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ANALYSE_RESULT';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_ANALYSE_RESULT (IID NUMERIC (19) NOT NULL,SCRIPT_SERVICES_IID NUMERIC (19),WORK_ITEM_ID NUMERIC (19),PROCEDURE_NAME VARCHAR(255),ANALYSE_RESULT VARCHAR(4000),CREATE_DATE NUMERIC (19),CONSTRAINT PK_IEAI_SCRIPT_ANALYSE_RESULT PRIMARY KEY (IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_DOUBLECHECK_WORKITEM';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_DOUBLECHECK_WORKITEM'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_DOUBLECHECK_WORKITEM_HIS';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_DOUBLECHECK_WORKITEM_HIS'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_INSTANCE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_INSTANCE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_USER_GROUP_RELATION';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_USER_GROUP_RELATION'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_VERRES';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_VERRES'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_OF_MY';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_OF_MY'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_INSTANCE_TEST';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_INSTANCE_TEST'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPTS_COAT';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPTS_COAT'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_BUSINESS_SYSTEM'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICESUB_USER';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICESUB_USER'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_FLOW';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_FLOW'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_USER_GROUP';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_USER_GROUP'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$

-- 4.7.19 version script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'ISAUTOSUB';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD  ISAUTOSUB DECIMAL(1,0) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'MANUALSTART';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD  MANUALSTART DECIMAL(1,0) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ISAUTOSUB';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  ISAUTOSUB DECIMAL(1,0) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'MANUALSTART';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  MANUALSTART DECIMAL(1,0) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'IANALYZEFUNFLAG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  IANALYZEFUNFLAG DECIMAL(1,0) DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'IANALYZEFUNTEXT';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IANALYZEFUNTEXT CLOB ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'ISQLEXECMODEL';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD ISQLEXECMODEL INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ISQLEXECMODEL';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD ISQLEXECMODEL INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ISDELETE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  ISDELETE INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'ISDELETE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_SCRIPT_TEST ADD  ISDELETE INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TOOL_BOX' AND COLNAME = 'ISDELETE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_SCRIPT_TOOL_BOX ADD  ISDELETE INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND COLNAME = 'ISDELETE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_SCRIPT_SHARE  ADD ISDELETE INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AUDITING_IPS' AND COLNAME = 'IOPERID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_SCRIPT_AUDITING_IPS  ADD IOPERID INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_FLOW' AND COLNAME = 'IPARTEXEC';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_SCRIPT_FLOW  ADD IPARTEXEC VARCHAR(2) DEFAULT ''0'' ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_FLOW' AND COLNAME = 'IIGNORE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_SCRIPT_FLOW  ADD IIGNORE VARCHAR(2) DEFAULT ''0'' ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION' AND COLNAME = 'IISSCRIPT';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_EMER_SCENE_SS_RELATION  ADD IISSCRIPT INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DOUBLE_AUTH_CODE' AND COLNAME = 'IISSCRIPT';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE  IEAI_DOUBLE_AUTH_CODE  ADD IISSCRIPT INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DATABASE_TYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_DATABASE_TYPE (IID NUMERIC(19) NOT NULL, IDBTYPE VARCHAR(255) NOT NULL, IORDER NUMERIC(19), CONSTRAINT PK_IEAI_DATABASE_TYPE PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	

	SET LS_SQL ='CREATE OR REPLACE VIEW V_SCRIPT_TASK_EXECTIME AS SELECT T.IID, SUM(CASE WHEN D.ISTATUS = 20 THEN 1  ELSE 0 END) AS SUCCESSTIMES, COUNT(D.IID) TOTALTIMES, (CASE  WHEN COUNT(D.IID) = 0 THEN 0  ELSE ROUND(SUM(CASE WHEN D.IID IS NOT NULL AND D.ISTATUS = 20 THEN 1  ELSE  0 END) * 100 / COUNT(D.IID)) END) AS SUCCESSRATE FROM IEAI_SCRIPT_TEST T LEFT JOIN IEAI_SCRIPT_FLOW D ON T.ISCRIPTUUID = D.IMXSERVICEID WHERE T.IISFLOW = 1  AND T.ISDELETE=0  GROUP BY T.IID UNION ALL SELECT T.IID, SUM(CASE WHEN D.ISTATE = 20 THEN 1 ELSE  0  END) AS SUCCESSTIMES, COUNT(D.IID) TOTALTIMES,  (CASE WHEN COUNT(D.IID) = 0 THEN  0 ELSE  ROUND(SUM(CASE WHEN D.IID IS NOT NULL AND D.ISTATE = 20 THEN 1 ELSE 0 END) * 100 / COUNT(D.IID))  END) AS SUCCESSRATE  FROM IEAI_SCRIPT_TEST T LEFT JOIN IEAI_SCRIPT_INSTANCE D ON T.ISCRIPTUUID = D.ISERVICEID WHERE T.IISFLOW = 0  AND T.ISDELETE=0  GROUP BY T.IID';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	
	
	SET LS_SQL ='CREATE OR REPLACE VIEW V_SCRIPT_EXECTIME AS SELECT T.ILASTID,SUM(CASE WHEN D.ISTATE = 20 THEN  1 ELSE  0 END) AS SUCCESSTIMES,  COUNT(D.IID) TOTALTIMES,   (CASE WHEN COUNT(D.IID) = 0 THEN 0    ELSE ROUND(SUM(CASE WHEN D.IID IS NOT NULL AND D.ISTATE = 20 THEN   1 ELSE   0 END) * 100 / COUNT(D.IID)) END) AS SUCCESSRATE FROM IEAI_SCRIPT_TEST T  LEFT JOIN IEAI_SCRIPT_INSTANCE D ON T.ISCRIPTUUID = D.ISERVICEID  WHERE T.ISDELETE=0 GROUP BY T.ILASTID';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER' AND COLNAME = 'ISAUTOFLAG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' ALTER TABLE IEAI_SERVICESUB_USER ADD  ISAUTOFLAG decimal(1,0)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER' AND COLNAME = 'ISERVICEGROUPID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' ALTER TABLE IEAI_SERVICESUB_USER ADD  ISERVICEGROUPID decimal(19,0)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER' AND COLNAME = 'IUSERGROUPID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' ALTER TABLE IEAI_SERVICESUB_USER ADD  IUSERGROUPID decimal(19,0)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SERVICESUB_USER_REL';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SERVICESUB_USER_REL (IID decimal(19,0) NOT NULL,ISERVICEGROUPID decimal(19,0),IUSERID decimal(19,0),ISAUTOFLAG decimal(1,0) NOT NULL,ISERVICEID decimal(19,0) NOT NULL, ISCRIPTTYPE decimal(5,0), CONSTRAINT PK_IEAI_SERVICESUB_USER_REL PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_SHARE_RELATION';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SHARE_RELATION ( IID DECIMAL(19,0) NOT NULL, ISHAREID DECIMAL(19,0) NOT NULL, ISHAREOBJECTID DECIMAL(19,0) NOT NULL, ISHARETYPE DECIMAL(1,0) NOT NULL, ISFLOW DECIMAL(1,0), ISCUSTOMTASK   DECIMAL(1,0), CONSTRAINT PK_SCRIPT_SHARE_RELATION PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER_REL' AND COLNAME = 'ISTATUS';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' ALTER TABLE IEAI_SERVICESUB_USER_REL ADD  ISTATUS VARCHAR(50)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER_REL' AND COLNAME = 'ISTARTTIME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' ALTER TABLE IEAI_SERVICESUB_USER_REL ADD  ISTARTTIME decimal(19,0)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICESUB_USER_REL' AND COLNAME = 'IEXECTIME';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' ALTER TABLE IEAI_SERVICESUB_USER_REL ADD  IEXECTIME decimal(19,0)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
			
			
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_RULEOUTPARAMETER' AND COLNAME = 'ISCRIPTID';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_RULEOUTPARAMETER ALTER ISCRIPTID SET DATA TYPE VARCHAR (50)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SET LS_SQL ='CREATE OR REPLACE VIEW V_SCRIPT_SHAREUSERID AS SELECT A.ISHAREID, B.IID AS IUSERID FROM IEAI_SCRIPT_SHARE_RELATION A, IEAI_USER B, IEAI_USER_GROUP_RELATION  C WHERE A.ISHAREOBJECTID = C.IGROUPID AND C.IUSERID = B.IID AND A.ISHARETYPE =2 AND A.ISCUSTOMTASK=0 UNION SELECT ISHAREID,ISHAREOBJECTID AS IUSERID  FROM IEAI_SCRIPT_SHARE_RELATION WHERE ISHARETYPE in( 1,0) AND  ISCUSTOMTASK=0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	SET LS_SQL ='CREATE OR REPLACE VIEW V_SCRIPT_CUS_SHAREUSERID AS SELECT A.ISHAREID, B.IID AS IUSERID FROM IEAI_SCRIPT_SHARE_RELATION A,  IEAI_USER B, IEAI_USER_GROUP_RELATION  C WHERE A.ISHAREOBJECTID = C.IGROUPID AND C.IUSERID = B.IID AND A.ISHARETYPE = 2 AND A.ISCUSTOMTASK=1 UNION SELECT ISHAREID,ISHAREOBJECTID AS IUSERID  FROM IEAI_SCRIPT_SHARE_RELATION WHERE ISHARETYPE in( 1,0) AND  ISCUSTOMTASK=1';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SEQUENTIAL_APP_GROUP';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE  IEAI_SEQUENTIAL_APP_GROUP(IID  DECIMAL(19,0)  NOT NULL  , IAPPLICATIONNAME VARCHAR(255), IGROUP   VARCHAR(255),  ISUPPORLINE    VARCHAR(255),  USEROFFICE  VARCHAR(255), CONSTRAINT PK_SEQUENTIAL_APP_GROUP PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_DC_LOGIN';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE   IEAI_SCRIPT_DC_LOGIN(IID DECIMAL(19,0) NOT NULL, ILOGINNAME VARCHAR(255), ISEI DECIMAL(19,0),ISVALIDATE DECIMAL(2,0),CONSTRAINT PK_IEAI_SCRIPT_DC_LOGIN PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_TEST';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_TEST'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_SERVICES';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_SERVICES'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN
	DECLARE	LN_KEY	 		NUMERIC(19,0);
	DECLARE	LI_ROWCNT		INTEGER; 
	SELECT COUNT(*) INTO LI_ROWCNT FROM IEAI_SCRIPT_SHARE A 
		 WHERE NOT EXISTS (SELECT 1 FROM IEAI_SCRIPT_SHARE_RELATION B WHERE A.IID=B.ISHAREID);
		 
		IF	LI_ROWCNT > 0 THEN
			CALL PROC_GET_MULT_ID('IEAI_SCRIPT_SHARE_RELATION', LI_ROWCNT, LN_KEY);
			IF LN_KEY IS NULL THEN
				RETURN;
			END IF;
			insert into IEAI_SCRIPT_SHARE_RELATION
			  select ROW_NUMBER()OVER() + LN_KEY - 1,
					 iid,
					 -1,
					 0,
					 (select iisflow from ieai_script_services where iid = a.iid),
					 0
				from ieai_script_share a;
			COMMIT WORK;
		END IF;
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'IANALYZEFUNTYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  IANALYZEFUNTYPE VARCHAR(255) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_REPORT_TEMPLATE' AND COLNAME = 'IUSERID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_REPORT_TEMPLATE ADD IUSERID DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IMODEL';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IMODEL DECIMAL(2)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IREAPPSID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IREAPPSID DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'ICREATETIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD ICREATETIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IUPDATETIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IUPDATETIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'ICPU';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD ICPU DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IMEMORY';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IMEMORY DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IDISK';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IDISK DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_APPLY' AND COLNAME = 'SETMODEL';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_APPLY ADD SETMODEL VARCHAR(50)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_APPLY' AND COLNAME = 'ENV';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_APPLY ADD ENV VARCHAR(50)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_APPLY' AND COLNAME = 'USEDATE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_APPLY ADD USEDATE VARCHAR(50)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_APPLY' AND COLNAME = 'IRMANAGE_ID';
		IF	LI_EXISTS != 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_APPLY ALTER IRMANAGE_ID SET DATA TYPE DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TMP_SERVICE' AND COLNAME = 'SERVICEID';
		IF	LI_EXISTS != 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_TMP_SERVICE ALTER SERVICEID SET DATA TYPE VARCHAR(100)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		 
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DATABASE_TYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_DATABASE_TYPE (IID NUMERIC(19) NOT NULL, IDBTYPE VARCHAR(255) NOT NULL, IORDER NUMERIC(19), CONSTRAINT PK_IEAI_DATABASE_TYPE PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ASM';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_ASM( IID DECIMAL(19,0) NOT NULL, ISYSNAME VARCHAR(255), INAME VARCHAR(255), IRACVERSION VARCHAR(50), ISTATUS VARCHAR(50), IRACIP VARCHAR(16), IRACUSER VARCHAR(50), IAGENTID DECIMAL(19,0), IOPERSYS VARCHAR(255), constraint PK_IEAI_SCRIPT_ASM PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ASM_INFO';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_ASM_INFO( IID DECIMAL(19,0) NOT NULL, IMAINID DECIMAL(19,0), STATE  VARCHAR(50), TYPE  VARCHAR(50),  REBAL  VARCHAR(50), SECTOR VARCHAR(50), BLOCK   VARCHAR(50), AU  VARCHAR(50), TOTAL_MB  VARCHAR(50), FREE_MB  VARCHAR(50),  REQ_MIR_FREE_MB  VARCHAR(50), USABLE_FILE_MB  VARCHAR(50), OFFLINE_DISKS  VARCHAR(50), VOTING_FILES  VARCHAR(50), NAME  VARCHAR(50), constraint PK_IEAI_SCRIPT_ASM_INFO PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_RULE_SERVICES';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_RULE_SERVICES(IID DECIMAL(19,0) NOT NULL,ISERVICEUUID VARCHAR(255),ICOLNAME VARCHAR(50),IRULEID DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_RULE_SERVICES PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_RESAPP_PARAM';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_RESAPP_PARAM(  IID   DECIMAL(19) NOT NULL,  INAME VARCHAR(255),  IDESC VARCHAR(255),  ITYPE DECIMAL(2),  CONSTRAINT PK_IEAI_SCRIPT_RESAPP_PARAM PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_RESAPP_PARAM_SUB';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_RESAPP_PARAM_SUB(  IID DECIMAL(19,0) NOT NULL,  INAME   VARCHAR(255),  IVALUE  VARCHAR(255),  IORDER  INTEGER,  IMAINID DECIMAL(19),  IDESC   VARCHAR(255),  IFLAG   DECIMAL(1),  CONSTRAINT PK_IEAI_SCRIPT_RESAPP_P_S PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_RESAPP_PARAM_VALUE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_RESAPP_PARAM_VALUE(  IID DECIMAL(19,0) NOT NULL,  INAME VARCHAR(255),  IVALUE VARCHAR(255),  IORDER INTEGER,  IMAINID DECIMAL(19),  IWORKITEMID DECIMAL(19),  IRESID DECIMAL(19),  IFLAG DECIMAL(1),  CONSTRAINT PK_IEAI_SCRIPT_RESAPP_P_V PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SERVICE_ANA_COLUMNS';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_SERVICE_ANA_COLUMNS (IID decimal(19,0) NOT NULL ,ISERVICEID decimal(19,0) ,ICOLUMNNAME varchar(50),ICOLUMNTYPE decimal(1,0),ICOLUMNORDER decimal(11,0) ,ICOLUMNLEN decimal(5,0),ICOLUMNDESC varchar(500), TABLENAME varchar(50),MCFLAG decimal(1,0), CONSTRAINT PK_IEAI_SERVICE_ANA_COLUMNS PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL =' CREATE TABLE IEAI_SERVICE_ANA_TABLE_REL (IID decimal(19,0) NOT NULL ,IORIGINAL varchar(50),IMAIN varchar(50) ,ICHILD varchar(50),IANALYZETEXT varchar(4000),ISTATUS decimal(1,0),ISERVICEID decimal(19,0),IVALUE VARCHAR (255),IOPERATOR decimal (1,0), CONSTRAINT PK_IEAI_SERVICE_ANA_TABLE_REL PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_ANA_COLUMNS' AND COLNAME = 'ISERVICEID';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_COLUMNS DROP COLUMN ISERVICEID';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_ANA_COLUMNS' AND COLNAME = 'ISCRIPTUUID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_COLUMNS  ADD ISCRIPTUUID VARCHAR (50)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLNAME = 'ISERVICEID';
	IF	LI_EXISTS = 1 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL DROP COLUMN ISERVICEID';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLNAME = 'ISCRIPTUUID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL  ADD ISCRIPTUUID VARCHAR (50)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'insert into IEAI_DATABASE_TYPE (iid, idbtype, iorder) values (1, ''ORACLE'', 1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'insert into IEAI_DATABASE_TYPE (iid, idbtype, iorder) values (2, ''DB2'', 1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID=3;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'insert into IEAI_DATABASE_TYPE (iid, idbtype, iorder) values (3, ''MYSQL'', 1)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	COMMIT WORK;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TMP_S_R';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_TMP_S_R ( IID DECIMAL (19) NOT NULL, RID DECIMAL (19), SID DECIMAL (19) , CONSTRAINT PK_IEAI_TMP_S_R PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_COLUMNS';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_COLUMNS'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_TABLE_REL'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN

  	UPDATE IEAI_SCRIPT_TEST 
	SET ICONTENT=REPLACE(ICONTENT,'<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="200" as="view"/>','<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="237" as="view"/>')  
	WHERE IISFLOW=1 ;
	 
	UPDATE IEAI_SCRIPT_SERVICES 
	SET ICONTENT=REPLACE(ICONTENT,'<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="200" as="view"/>','<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="237" as="view"/>') 
	WHERE IISFLOW=1 ;
	
	COMMIT;
END
$
-- 4.7.20 version script patch is as follows

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	

		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IMPAGENTIP';
		IF LI_EXISTS = 1 THEN
		SET LS_SQL ='DROP TABLE TMP_IMPAGENTIP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IMPAGENTIP';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE GLOBAL TEMPORARY TABLE TMP_IMPAGENTIP( AGENTIP VARCHAR(200), PORT VARCHAR(200),OSTYPE VARCHAR(200),CONFIGPARAMS VARCHAR(200)) ON COMMIT DELETE ROWS';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

END
	$
	
	
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IBUSINESSTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD  IBUSINESSTYPE VARCHAR(255) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ISERVICEAUTO';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  ISERVICEAUTO DECIMAL(1,0) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICE_GROUP' AND COLNAME = 'ISERVICEAUTO';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICE_GROUP ADD  ISERVICEAUTO DECIMAL(1,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;


		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_SYSTEMTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = ' CREATE TABLE IEAI_RESOURCE_SYSTEMTYPE( IID DECIMAL(19,0) NOT NULL,  INAME  VARCHAR(50), constraint PK_IEAI_RESOURCE_SYSTEMTYPE PRIMARY KEY (IID) )';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_MANUAL_START_RESULT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE TABLE IEAI_MANUAL_START_RESULT( IID DECIMAL(19,0) NOT NULL,  ISTARTTIME  TIMESTAMP,IUSERID DECIMAL(19,0),IRESID DECIMAL(19,0),  ISERVICEUUID  VARCHAR(50) , IGROUPID  DECIMAL(19,0),IRESULT1  VARCHAR(255),IRESULT2  VARCHAR(255),IRESULT3  VARCHAR(255),IRESULT4  VARCHAR(255),  IRESULT5  VARCHAR(255),IRESULT6  VARCHAR(255),IRESULT7  VARCHAR(255),IRESULT8  VARCHAR(255),IRESULT9  VARCHAR(255),IRESULT10  VARCHAR(255),  IRESULT11  VARCHAR(255),IRESULT12  VARCHAR(255),IRESULT13  VARCHAR(255),IRESULT14  VARCHAR(255),IRESULT15  VARCHAR(255),IRESULT16  VARCHAR(255),  IRESULT17  VARCHAR(255),IRESULT18  VARCHAR(255),IRESULT19  VARCHAR(255),IRESULT20  VARCHAR(255),IRESULT21  VARCHAR(255),IRESULT22  VARCHAR(255),  IRESULT23  VARCHAR(255),IRESULT24  VARCHAR(255),IRESULT25  VARCHAR(255),IRESULT26  VARCHAR(255),IRESULT27  VARCHAR(255),IRESULT28  VARCHAR(255),	IRESULT29  VARCHAR(255),IRESULT30  VARCHAR(255),   constraint PK_IEAI_MANUAL_START_RESULT PRIMARY KEY (IID) )';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_COLLECTION' AND COLNAME = 'IGROUPID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_COLLECTION ADD  IGROUPID DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_COLLECTION' AND COLNAME = 'ISCRIPTSERVICEID';
		IF	LI_EXISTS = 1 THEN
		SET	LS_SQL = 'alter table IEAI_SCRIPT_COLLECTION alter ISCRIPTSERVICEID drop not null';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IALTERLOGNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD  IALTERLOGNAME VARCHAR(300) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_USER' AND COLNAME = 'IMODIFYTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_USER ADD  IMODIFYTIME NUMERIC(19) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'ICONTINUECOUNT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD ICONTINUECOUNT decimal(19,0) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'ICUMULATECOUNT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD ICUMULATECOUNT decimal(19,0) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_CHECK_RESULT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = ' CREATE TABLE IEAI_RESOURCE_CHECK_RESULT( IID decimal(19,0) NOT NULL,IREID  decimal(19,0), ICHECKTIME decimal(19,0),ICHECKRESULT  decimal(1,0),IMESS  CLOB, constraint PK_IEAI_RESOURCE_CHECK_RESULT PRIMARY KEY (IID) )';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'ISERVICEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST  ADD ISERVICEID VARCHAR(255)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ISERVICEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES  ADD ISERVICEID VARCHAR(255)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_S_G_RELATION' AND COLNAME = 'IORDER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_S_G_RELATION  ADD IORDER decimal(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
	
END
$

-- 4.7.21 version script patch is as follows

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ANALYALGOR';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = ' CREATE TABLE IEAI_SCRIPT_ANALYALGOR (IID DECIMAL (19, 0) NOT NULL, ISERVICEID VARCHAR (255), IVERSION VARCHAR (255), ICREATETIME DECIMAL (19, 0), ICONTENT VARCHAR(4000), CONSTRAINT PK_IEAI_SCRIPT_ANALYALGOR PRIMARY KEY (IID) )';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ISERVICEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  ISERVICEID VARCHAR(255) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_ANA_COLUMNS' AND COLNAME = 'ISERVICEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_COLUMNS ADD  ISERVICEID VARCHAR(255) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLNAME = 'ISERVICEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL ADD  ISERVICEID VARCHAR(255) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'ITIMEOUT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD  ITIMEOUT DECIMAL (19, 0)  DEFAULT -1 ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'ITIMEOUT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  ITIMEOUT DECIMAL (19, 0) DEFAULT -1  ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_INSTANCE' AND COLNAME = 'ITIMEOUT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_INSTANCE ADD  ITIMEOUT DECIMAL (1, 0) DEFAULT 0 ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_TABLE_REL'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN
    DECLARE LS_SQL VARCHAR(4000);
    DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND COLNAME = 'IDBTYPE';
    IF  LI_EXISTS = 1 THEN
        SET LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST DROP COLUMN IDBTYPE';
            PREPARE SQLA FROM LS_SQL; 
            EXECUTE SQLA;
        SET LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD IDBTYPE VARCHAR(255) ';
            PREPARE SQLA FROM LS_SQL; 
            EXECUTE SQLA;
    END IF;
   
    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND COLNAME = 'IDBTYPE';
    IF	LI_EXISTS = 1 THEN
        SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES DROP COLUMN IDBTYPE';
            PREPARE	SQLA FROM LS_SQL; 
            EXECUTE SQLA;
        SET    LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IDBTYPE VARCHAR(255) ';
            PREPARE SQLA FROM LS_SQL; 
            EXECUTE SQLA;
    END	IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TOOL_BOX' AND COLNAME = 'IDBTYPE';
    IF  LI_EXISTS = 1 THEN
        SET LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TOOL_BOX DROP COLUMN IDBTYPE';
            PREPARE SQLA FROM LS_SQL; 
            EXECUTE SQLA;
        SET LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TOOL_BOX ADD IDBTYPE VARCHAR(255)';
            PREPARE SQLA FROM LS_SQL; 
            EXECUTE SQLA;
    END IF;
    
END
$



BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SERVICE_BIND_CONDITION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SERVICE_BIND_CONDITION(ISERVICEID DECIMAL(19) ,IORDER DECIMAL(19) ,ITYPE DECIMAL(1) ,ICONDITION VARCHAR(125) ,IWORKITEMID DECIMAL(19) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IORDER' AND TABNAME='IEAI_SCRIPT_S_G_RELATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_S_G_RELATION ADD COLUMN IORDER DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWORKITEMID' AND TABNAME='IEAI_SERVICE_VERRES';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SERVICE_VERRES ADD COLUMN IWORKITEMID DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPROTYPE' AND TABNAME='IEAI_RESOURCE_VERSION_TREE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_VERSION_TREE ADD COLUMN IPROTYPE VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUPID' AND TABNAME='IEAI_SCRIPT_TEST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_TEST ADD COLUMN IUPID DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUPID' AND TABNAME='IEAI_SCRIPT_SERVICES';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_SERVICES ADD COLUMN IUPID DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USR_SYS_RELATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_USR_SYS_RELATION(IID  NUMERIC(19) NOT NULL, IUSERID NUMERIC(19) NOT NULL,ISYSID NUMERIC(19) NOT NULL, IMANAGEFLAG NUMERIC(19) DEFAULT 0, CONSTRAINT PK_IEAI_USR_SYS_RELATION PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_APPSYSTEM_PROP';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_APPSYSTEM_PROP(IID NUMERIC(19) NOT NULL,IPROPNAME VARCHAR(255),IORDER INTEGER,ICREATETIME NUMERIC(19),IMODIFYTIME NUMERIC(19),IDEL INTEGER,CONSTRAINT PK_IEAI_SCRIPT_APPSYSTEM_PROP PRIMARY KEY(IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_OS' AND  COLNAME='ICREATETIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_OS ADD COLUMN ICREATETIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_OS' AND  COLNAME='IMODIFYTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_OS ADD COLUMN IMODIFYTIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDEL' AND TABNAME='IEAI_OS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_OS ADD COLUMN IDEL INTEGER';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DATABASE_TYPE' AND  COLNAME='ICREATETIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_DATABASE_TYPE ADD COLUMN  ICREATETIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DATABASE_TYPE' AND  COLNAME='IMODIFYTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_DATABASE_TYPE ADD COLUMN  IMODIFYTIME  DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DATABASE_TYPE' AND  COLNAME='IURL';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_DATABASE_TYPE ADD column IURL VARCHAR(255)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DATABASE_TYPE' AND  COLNAME='IDRIVER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_DATABASE_TYPE ADD column IDRIVER VARCHAR(255)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDEL' AND TABNAME='IEAI_DATABASE_TYPE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_DATABASE_TYPE ADD COLUMN IDEL INTEGER';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_SYSTEMTYPE' AND  COLNAME='ICREATETIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_RESOURCE_SYSTEMTYPE ADD column ICREATETIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_SYSTEMTYPE' AND  COLNAME='IMODIFYTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE  IEAI_RESOURCE_SYSTEMTYPE ADD column IMODIFYTIME DECIMAL(19,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IDEL' AND TABNAME='IEAI_RESOURCE_SYSTEMTYPE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RESOURCE_SYSTEMTYPE ADD COLUMN IDEL INTEGER';
		    PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_DATABASE_TYPE';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_DATABASE_TYPE'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
end
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_WORKRES_RECORD';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_WORKRES_RECORD (IID DECIMAL(19) NOT NULL,IWORKITEMID DECIMAL(19),ISID DECIMAL(19),ISERVICEID VARCHAR (255),IRID DECIMAL(19),IEXECUSER VARCHAR (255),IUPDATETIME DECIMAL(19),ISTATE DECIMAL(1),IDESC VARCHAR (255),CONSTRAINT PK_IEAI_SCRIPT_WORKRES_RECORD PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		
END
$

-- 4.7.21 add reorg
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_SERVICES';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_SERVICES'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_INSTANCE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_INSTANCE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_COLUMNS';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_COLUMNS'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_TABLE_REL'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_TEST';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_TEST'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_TOOL_BOX';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_TOOL_BOX'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_S_G_RELATION';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_S_G_RELATION'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_VERRES';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_VERRES'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_RESOURCE_VERSION_TREE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_RESOURCE_VERSION_TREE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_OS';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_OS'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_DATABASE_TYPE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_DATABASE_TYPE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_RESOURCE_SYSTEMTYPE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_RESOURCE_SYSTEMTYPE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$


-- 4.7.22 version script patch is as follows

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUPDATEDATE' AND TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD COLUMN IUPDATEDATE DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUPDATEUSER' AND TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD COLUMN IUPDATEUSER VARCHAR(200)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUPDATEUSERID' AND TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD COLUMN IUPDATEUSERID DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUPDATEUSERID' AND TABNAME='IEAI_SCRIPT_TEST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_SCRIPT_TEST  ADD COLUMN IUPDATEUSERID DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSNAMEABB' AND TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM   ADD COLUMN ISYSNAMEABB  VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IVALUE' AND TYPENAME = 'VARCHAR' AND LENGTH < 2000 AND TABNAME='IEAI_SCRIPT_AUDITING_PARAMS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_SCRIPT_AUDITING_PARAMS   ALTER IVALUE  SET  DATA TYPE VARCHAR(2000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		
END
$

-- 4.7.22 add reorg
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_AUDITING_PARAMS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_AUDITING_PARAMS'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_TEST';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_TEST'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
end
$

-- 4.7.23 version script patch is as follows

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	DECLARE LI_EXISTS1 NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IANALYZETEXT' AND TABNAME='IEAI_SERVICE_ANA_TABLE_REL' AND TYPENAME<>'CLOB';
		IF LI_EXISTS > 0 THEN 
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS1 FROM SYSCAT.COLUMNS WHERE COLNAME='IANALYZETEXTTMP' AND TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
			IF LI_EXISTS1 = 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL ADD COLUMN IANALYZETEXTTMP CLOB';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_TABLE_REL'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IANALYZETEXT' AND TABNAME='IEAI_SERVICE_ANA_TABLE_REL' AND TYPENAME<>'CLOB';
		IF LI_EXISTS > 0 THEN 
			SET LS_SQL = 'UPDATE IEAI_SERVICE_ANA_TABLE_REL SET IANALYZETEXTTMP=IANALYZETEXT';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'UPDATE IEAI_SERVICE_ANA_TABLE_REL SET IANALYZETEXT=NULL';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL ALTER  COLUMN IANALYZETEXT SET DATA TYPE CLOB ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'UPDATE IEAI_SERVICE_ANA_TABLE_REL SET IANALYZETEXT=IANALYZETEXTTMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			COMMIT;
			SET LS_SQL = 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL DROP (IANALYZETEXTTMP)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPROPNAME' AND TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD COLUMN IPROPNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPTS_FLOW';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPTS_FLOW(FLOWID DECIMAL(19),INSTANCEID DECIMAL(19),RESID DECIMAL(19))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_TIMECONFIG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_TIMECONFIG( IID DECIMAL(19) NOT NULL, INAME VARCHAR(50), CREATETIME	VARCHAR(50), CREATEUSER DECIMAL(19),CORNTIME VARCHAR(50) ,SERVICEID DECIMAL(19) , SERVICENAME VARCHAR(255), IDESC VARCHAR(255), STATUS DECIMAL(1) default 0 , CORNTYPE DECIMAL(1) default 0 , CONSTRAINT IEAI_SCRIPT_TIMECONFIG PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRAC' AND TABNAME='IEAI_RESOURCE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN IRAC VARCHAR(20)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IFLOWID' AND TABNAME='IEAI_SCRIPT_ANALYSE_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_ANALYSE_RESULT ADD COLUMN IFLOWID DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPROJECTFLAG' AND TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD COLUMN IPROJECTFLAG DECIMAL(1)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_TEMPLATE_ANALY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE  IEAI_SCRIPT_TEMPLATE_ANALY (IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,ISERVICEID DECIMAL(19) ,IAGENTID DECIMAL(19) ,IPARAMETER VARCHAR(255))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_PARAMETER_WORKITEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_PARAMETER_WORKITEM(IID DECIMAL(19) NOT NULL ,ISERVICEID DECIMAL(19) ,IPARAM_TYPE VARCHAR(500) ,IPARAM_DEFAULT_VALUE VARCHAR(500) ,IPARAM_DESC VARCHAR(500) ,IPARAM_ORDER DECIMAL(10) ,IWORKITEMID DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPT_PARAMETER_W PRIMARY KEY (IID) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SERVICE_ANA_TABLE_REL';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SERVICE_ANA_TABLE_REL'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

		CREATE or replace FUNCTION FUN_GET_RUNTIME(starttime NUMERIC(24, 0),endtime NUMERIC(24, 0)) RETURNS varchar(30)
		BEGIN
			DECLARE LV_RETURN     VARCHAR(30); -- 返回值
			DECLARE LI_BETWEENSEC DECIMAL(13,3); -- 秒差
			DECLARE LV_NOWDATENUMBER BIGINT;
			DECLARE LI_SECONDS    SMALLINT; -- 秒数
			DECLARE LI_MINUTES    SMALLINT; -- 分钟数
			DECLARE LI_HOURS      SMALLINT; -- 小时数
			DECLARE LI_DAYS       SMALLINT; -- 日数
		   
			DECLARE SI_SECONDSMILLISEC   SMALLINT;
			DECLARE si_day      VARCHAR(2);
			DECLARE si_sec      VARCHAR(2);
			DECLARE si_hour     VARCHAR(4);
			DECLARE si_minute   VARCHAR(2);
		 
		 
			SET	SI_SECONDSMILLISEC = 1000;
			SET	si_day = '天';
			SET	si_sec = '秒';
			SET	si_hour = '小时';
			SET	si_minute = '分';
			SET LV_RETURN = '';
			
			 
			SET LV_nowdateNumber =FUN_GET_DATE_NUMBER_NEW(current_timestamp  , 8);
			-- 计算时间差   秒
			set LI_BETWEENSEC = (nvl(endtime, LV_nowdateNumber) - nvl(starttime, 0)) /
							 SI_SECONDSMILLISEC;
		  
			/*
			 天：( 总秒数 /60/60/24)
			小时：(( 总秒数-60*60*24*天)/60*60)
			分钟：( 总秒数-天*24*60*60-小时*60*60)/60
			秒：总秒数 - 天*24*60*60-小时*60*60-分钟*60
			*/
		  
			set LI_DAYS     = truncate(LI_BETWEENSEC / 60 / 60 / 24, 0);
			set  LI_HOURS    = truncate((LI_BETWEENSEC - 60 * 60 * 24 * LI_DAYS) / 60 / 60,
								0);
			set  LI_MINUTES  = truncate((LI_BETWEENSEC - LI_DAYS * 60 * 60 * 24 -
								LI_HOURS * 60 * 60) / 60,
								0);
			set  LI_SECONDS  = truncate((LI_BETWEENSEC - LI_DAYS * 60 * 60 * 24 -
								LI_HOURS * 60 * 60 - LI_MINUTES * 60),
								0);
			if LI_DAYS != 0 then
			  set LV_RETURN =  LV_RETURN||LI_DAYS||si_day;
			end if;
			if LI_HOURS != 0 then
			  set LV_RETURN =  LV_RETURN||LI_HOURS||si_hour;
			end if;
			if LI_MINUTES != 0 then
			  set LV_RETURN =  LV_RETURN||LI_MINUTES||si_minute;
			end if;
			if LI_SECONDS != 0 then
			  set LV_RETURN =  LV_RETURN||LI_SECONDS||si_sec;
			end if;
		  
			RETURN LV_RETURN;
			 
		END
		$
		
-- 4.7.24 version script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);



SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAUDITPASSTIME' AND TABNAME='IEAI_DOUBLECHECK_WORKITEM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD COLUMN IAUDITPASSTIME TIMESTAMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IAUDITPASSTIME' AND TABNAME='IEAI_DOUBLECHECK_WORKITEM_HIS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD COLUMN IAUDITPASSTIME TIMESTAMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_DOUBLECHECK_WORKITEM';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_DOUBLECHECK_WORKITEM'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
end
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_DOUBLECHECK_WORKITEM_HIS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_DOUBLECHECK_WORKITEM_HIS'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
end
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_INSTANCE_TEST' AND COLNAME = 'ITIMEOUT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_INSTANCE_TEST ADD  ITIMEOUT DECIMAL (1, 0) DEFAULT 0 ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'ISTARTUPTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD  ISTARTUPTIME VARCHAR (255) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TMP_SERVICE' AND COLNAME = 'IORDER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_TMP_SERVICE ADD  IORDER DECIMAL (19, 0) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=8;
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(8,''管理员'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END	IF;
		
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAM_DEFAULT_VALUE' AND TABNAME='IEAI_SCRIPT_PARAMETER';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_PARAMETER ALTER  COLUMN IPARAM_DEFAULT_VALUE SET  DATA  TYPE VARCHAR(4000)';
			PREPARE  SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IVALUE' AND TABNAME='IEAI_SCRIPT_AUDITING_PARAMS' AND TYPENAME = 'VARCHAR' AND LENGTH < 4000;
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS ALTER  COLUMN IVALUE SET  DATA  TYPE VARCHAR(4000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DOUBLECHECK_WORKITEM where  iauditpasstime is null and istate  not in(1,2,8,6)  and iitemtype=10;
		IF LI_EXISTS > 0 THEN
			SET LS_SQL = 'update IEAI_DOUBLECHECK_WORKITEM set iauditpasstime= icreatetime  where iauditpasstime is null and istate  not in(1,2,8,6)  and iitemtype=10';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMETERS1'   AND TABNAME='IEAI_SCRIPT_INSTANCE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_INSTANCE  ADD  IPARAMETERS1  CLOB';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			
			
		END IF;
 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSDATE' AND TABNAME='IEAI_SCRIPT_INSTANCE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_INSTANCE ADD ISYSDATE TIMESTAMP DEFAULT CURRENT TIMESTAMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSDATE' AND TABNAME='IEAI_SCRIPT_INSTANCE_TEST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_INSTANCE_TEST ADD ISYSDATE TIMESTAMP DEFAULT CURRENT TIMESTAMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_INSTANCE';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table ieai_script_instance'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_AUDITING_PARAMS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_AUDITING_PARAMS'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
end
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMETERS1'  AND TABNAME='IEAI_SCRIPT_INSTANCE';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='UPDATE  IEAI_SCRIPT_INSTANCE   SET  IPARAMETERS1= IPARAMETERS';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
		END IF;
		
		SELECT COUNT(1) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME in('IPARAMETERS','IPARAMETERS1')  AND TABNAME='IEAI_SCRIPT_INSTANCE';
		IF LI_EXISTS = 2 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_INSTANCE DROP COLUMN IPARAMETERS';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMETERS1'  AND TABNAME='IEAI_SCRIPT_INSTANCE';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_INSTANCE RENAME COLUMN IPARAMETERS1 TO IPARAMETERS';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	 
end
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_INSTANCE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table ieai_script_instance'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_INSTANCE_TEST';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_INSTANCE_TEST'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
end
$




create or replace view v_script_monitor_todotask as
		select te.taskid,
			   te.taskName,
			   te.taskType,
			   se.iid              scriptiid,
			   se.iscriptuuid      scriptuuid,
			   se.iservicesname    serviceName,
			   se.iscripttype      scriptType,
			   se.iisflow          isflow,
			   te.iignore,
			   te.coatid,
			   te.status,
			   te.istate,
			   te.startUser,
			   te.execuser,
			   te.butterflyVersion,
			   te.iistimetask,
			   te.execStrategy,
			   te.createTime,
			   te.auditPassTime, 
			   te.starttime
		  from (select dw.iid as taskId,
					   '自定义运维' taskType,
					   (select icolvalue
						  from IEAI_DOUBLECHECK_COLVALUE co1
						 where co1.iworkitemid = dw.iid
						   and co1.ICOLHEADER = 'fromId') scriptiid,
					   nvl(fl.iignore, 0) iignore,
					   nvl(cc.iid, 0) coatid,
					   case cc.istate
						 when 11 then
						  '部分运行'
						 else
						  '未运行'
					   end as status,
					   nvl(cc.istate, 0) istate,
					   u.ifullname as startUser,
					   (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) EXECUSER,
					   dc.ICOLVALUE as taskName,
					   dw.butterflyversion as butterflyVersion,
					   dw.iistimetask iistimetask,
					   case dw.iistimetask
						 when 0 then
						  1
						 when 2 then
						  2
					   end as execStrategy,
					   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
					   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
					   case dw.iistimetask
						 when 0 then
							to_char(dw.iexectime, 'yyyy-mm-dd hh24:mi:ss')
						 when 2 then --定时
							 (select distinct icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'taskTimeForDispaly')
					   end  starttime
				  from IEAI_DOUBLECHECK_WORKITEM dw
				  left join IEAI_DOUBLECHECK_COLVALUE dc
					on dw.IID = dc.IWORKITEMID
				  left join ieai_user u
					on u.iloginname = dw.istartuser
				  left join IEAI_SCRIPTS_COAT cc
					on cc.iworkitemid = dw.iid
				  left join ieai_script_flow fl
					on fl.iid = cc.iflowid
				 where dc.ICOLHEADER = 'taskName'
				   and dw.iistimetask in (0, 2)
				   and (dw.istate = 10 or
					   (dw.istate = 5 and cc.istate = 11 AND CC.FLAG = 1))
				   and dw.IITEMTYPE = 10
				   and dw.IAUDITYPE = 1) te
		  left join ieai_script_services se
			on se.iid = te.scriptiid
		$
		
		create or replace view v_script_monitor_runningtask as
		select te.taskid,
			   te.taskName,
			   te.taskType,
			   se.iid              scriptiid,
			   se.iscriptuuid      scriptuuid,
			   se.iservicesname    serviceName,
			   se.iscripttype      scriptType,
			   se.iisflow          isflow,
			   te.iignore,
			   te.flowid,
			   te.coatid,
			   te.status,
			   te.istate,
			   te.startUser,
			   te.performUser,
			   te.butterflyVersion,
			   te.iistimetask,
			   te.execStrategy,
			   te.createTime,
			   te.auditPassTime, 
			   te.starttime,
			   te.runTime
		  from (select dw.iid as taskId,
					   '自定义运维' taskType,
					   (select icolvalue
						  from IEAI_DOUBLECHECK_COLVALUE co1
						 where co1.iworkitemid = dw.iid
						   and co1.ICOLHEADER = 'fromId') scriptiid,
					   nvl(fl.iignore, 0) iignore,
					   nvl(cc.iid, 0) coatid,
					   nvl(fl.iid, 0) flowid,
					   case cc.istate
						 when 10 then
						  '运行'
						 when 30 then
						  '异常'
						  when 60 then '终止'
					   end as status,
					   nvl(cc.istate, 0) istate,
					   u.ifullname as startUser,
					   (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
					   dc.ICOLVALUE as taskName,
					   dw.butterflyversion as butterflyVersion,
					   dw.iistimetask iistimetask,
					   case dw.iistimetask
						 when 0 then
						  1
						 when 2 then
						  2
					   end as execStrategy,
					   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
					   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
					   FUN_GET_DATE_STRING(fl.istarttime, 8, 'yyyy-mm-dd hh24:mi:ss') as starttime,
					   FUN_GET_RUNTIME(fl.istarttime, fl.iendtime) as runTime
				  from IEAI_DOUBLECHECK_WORKITEM dw
				  left join IEAI_DOUBLECHECK_COLVALUE dc
					on dw.IID = dc.IWORKITEMID
				  left join ieai_user u
					on u.iloginname = dw.istartuser
				  left join IEAI_SCRIPTS_COAT cc
					on cc.iworkitemid = dw.iid
				  left join ieai_script_flow fl
					on fl.iid = cc.iflowid
				 where dc.ICOLHEADER = 'taskName'
				   and dw.iistimetask in (0, 2)
				   and dw.istate in (5, 6)
				   and   fl.istart_type= 0
				   and fl.istatus in (10, 30)
				   and dw.IITEMTYPE = 10
				   and dw.IAUDITYPE = 1) te
		  left join ieai_script_services se
			on se.iid = te.scriptiid
		union all
		select te.taskid,
			   te.taskName,
			   te.taskType,
			   se.iid              scriptiid,
			   se.iscriptuuid      scriptuuid,
			   se.iservicesname    serviceName,
			   se.iscripttype      scriptType,
			   se.iisflow          isflow,
			   te.iignore,
			   te.coatid,
			   te.flowid,
			   te.status,
			   te.istate,
			   te.startUser,
			   te.performUser,
			   te.butterflyVersion,
			   te.iistimetask,
			   te.execStrategy,
			   te.createTime,
			   te.auditPassTime, 
			   te.starttime,
			   te.runTime
		  from (select dw.iid as taskId,
					   '自定义运维' taskType,
					   (select icolvalue
						  from IEAI_DOUBLECHECK_COLVALUE co1
						 where co1.iworkitemid = dw.iid
						   and co1.ICOLHEADER = 'fromId') scriptiid,
					   nvl(fl.iignore, 0) iignore,
					   nvl(cc.iid, 0) coatid,
					   nvl(fl.iid, 0) flowid,
					   case cc.istate
						 when 10 then
						  '运行'
						 when 30 then
						  '异常'
						  when 60 then '终止'
					   end as status,
					   nvl(cc.istate, 0) istate,
					   u.ifullname as startUser,
					    (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
					   dc.ICOLVALUE as taskName,
					   dw.butterflyversion as butterflyVersion,
					   dw.iistimetask iistimetask,
					   case dw.iistimetask
						 when 0 then
						  1
						 when 1 then
						  3
					   end as execStrategy,
					   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
					   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
					   FUN_GET_DATE_STRING(fl.istarttime, 8, 'yyyy-mm-dd hh24:mi:ss') as starttime,
					   FUN_GET_RUNTIME(fl.istarttime, fl.iendtime) as runTime
				  from IEAI_DOUBLECHECK_WORKITEM dw
				  left join IEAI_DOUBLECHECK_COLVALUE dc
					on dw.IID = dc.IWORKITEMID
				  left join ieai_user u
					on u.iloginname = dw.istartuser
				  left join (select dw.iid as itaskid, max(co.iid) coatid
							  from IEAI_DOUBLECHECK_WORKITEM dw
							  left join IEAI_SCRIPTS_COAT co
								on co.iworkitemid = dw.iid
							 where dw.iistimetask = 1
							   and dw.istate = 12
							   and co.istate in (10, 30)
							   and dw.IITEMTYPE = 10
							   and dw.IAUDITYPE = 1
							 group by dw.iid) coo
					on coo.itaskid = dw.iid
				  left join IEAI_SCRIPTS_COAT cc
					on cc.iworkitemid = dw.iid
				   and cc.iid = coo.coatid
				  left join ieai_script_flow fl
					on fl.iid = cc.iflowid
				  left join ieai_script_services se
					on se.iscriptuuid = fl.imxserviceid
				 where dc.ICOLHEADER = 'taskName'
				   and dw.iistimetask = 1
				   and dw.istate = 12
				   and   fl.istart_type= 0
				   and fl.istatus in (10, 30)
				   and dw.IITEMTYPE = 10
				   and dw.IAUDITYPE = 1) te
		  left join ieai_script_services se
			on se.iid = te.scriptiid
		$
		
		create or replace view v_script_monitor_historytask as
		select te.taskid,
			   te.taskName,
			   te.taskType,
			   se.iid              scriptiid,
			   se.iscriptuuid      scriptuuid,
			   se.iservicesname    serviceName,
			   se.iscripttype      scriptType,
			   se.iisflow          isflow,
			   te.iignore,
			   te.coatid,
			   te.status,
			   te.istate,
			   te.startUser,
			   te.performUser,
			   te.butterflyVersion,
			   te.iistimetask,
			   te.execStrategy,
			   te.createTime,
			   te.auditPassTime,
			   te.starttime,
			   te.endTime,
			   te.runTime
		  from (select dw.iid as taskId,
					   '自定义运维' taskType,
					   (select icolvalue
						  from IEAI_DOUBLECHECK_COLVALUE co1
						 where co1.iworkitemid = dw.iid
						   and co1.ICOLHEADER = 'fromId') scriptiid,
					   nvl(fl.iignore, 0) iignore,
					   nvl(cc.iid, 0) coatid,
					   case cc.istate
						 when 20 then
						  '完行'
						 when 5 then
						  '忽略'
						 when 60 then
						  '终止'
					   end as status,
					   nvl(cc.istate, 0) istate,
					   u.ifullname as startUser,
					   (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
					   dc.ICOLVALUE as taskName,
					   dw.butterflyversion as butterflyVersion,
					   dw.iistimetask iistimetask,
					   case dw.iistimetask
						 when 0 then
						  1
						 when 2 then
						  2
					   end as execStrategy,
					   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
					   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
					   FUN_GET_DATE_STRING(fl.istarttime, 8, 'yyyy-mm-dd hh24:mi:ss') as starttime,
					   FUN_GET_DATE_STRING(fl.iendtime, 8, 'yyyy-mm-dd hh24:mi:ss') as endTime,
					   FUN_GET_RUNTIME(fl.istarttime, fl.iendtime) as runTime
				  from IEAI_DOUBLECHECK_WORKITEM dw
				  left join IEAI_DOUBLECHECK_COLVALUE dc
					on dw.IID = dc.IWORKITEMID
				  left join ieai_user u
					on u.iloginname = dw.istartuser
				  left join IEAI_SCRIPTS_COAT cc
					on cc.iworkitemid = dw.iid
				  left join ieai_script_flow fl
					on fl.iid = cc.iflowid
				 where dc.ICOLHEADER = 'taskName'
				   and dw.iistimetask in (0, 2)
				   and dw.istate in (5, 6)
				   and fl.istatus in (20, 60, 5)
				   and fl.istart_type= 0
				   and dw.IITEMTYPE = 10
				   and dw.IAUDITYPE = 1) te
		  left join ieai_script_services se
			on se.iid = te.scriptiid
		union all
		select te.taskid,
			   te.taskName,
			   te.taskType,
			   se.iid              scriptiid,
			   se.iscriptuuid      scriptuuid,
			   se.iservicesname    serviceName,
			   se.iscripttype      scriptType,
			   se.iisflow          isflow,
			   te.iignore,
			   te.coatid,
			   te.status,
			   te.istate,
			   te.startUser,
			    te.performUser,
			   te.butterflyVersion,
			   te.iistimetask,
			   te.execStrategy,
			   te.createTime,
			   te.auditPassTime,
			   te.starttime,
			   te.endTime,
			   te.runTime
		  from (select dw.iid as taskId,
					   '自定义运维' taskType,
					   (select icolvalue
						  from IEAI_DOUBLECHECK_COLVALUE co1
						 where co1.iworkitemid = dw.iid
						   and co1.ICOLHEADER = 'fromId') scriptiid,
					   nvl(fl.iignore, 0) iignore,
					   nvl(cc.iid, 0) coatid,
					   case cc.istate
						 when 20 then
						  '完行'
						 when 5 then
						  '忽略'
						 when 60 then
						  '终止'
					   end as status,
					   nvl(cc.istate, 0) istate,
					   u.ifullname as startUser,
					   (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
					   dc.ICOLVALUE as taskName,
					   dw.butterflyversion as butterflyVersion,
					   dw.iistimetask iistimetask,
					   case dw.iistimetask
						 when 0 then
						  1
						 when 1 then
						  3
					   end as execStrategy,
					   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
					   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
					   FUN_GET_DATE_STRING(fl.istarttime, 8, 'yyyy-mm-dd hh24:mi:ss') as starttime,
					   FUN_GET_DATE_STRING(fl.iendtime, 8, 'yyyy-mm-dd hh24:mi:ss') as endTime,
					   FUN_GET_RUNTIME(fl.istarttime, fl.iendtime) as runTime
				  from IEAI_DOUBLECHECK_WORKITEM dw
				  left join IEAI_DOUBLECHECK_COLVALUE dc
					on dw.IID = dc.IWORKITEMID
				  left join ieai_user u
					on u.iloginname = dw.istartuser
				  left join (select dw.iid as itaskid, max(co.iid) coatid
							  from IEAI_DOUBLECHECK_WORKITEM dw
							  left join IEAI_SCRIPTS_COAT co
								on co.iworkitemid = dw.iid
							 where dw.iistimetask = 1
							   and dw.istate = 12
							   and co.istate in (20, 60, 5)
							   and dw.IITEMTYPE = 10
							   and dw.IAUDITYPE = 1
							 group by dw.iid) coo
					on coo.itaskid = dw.iid
				  left join IEAI_SCRIPTS_COAT cc
					on cc.iworkitemid = dw.iid
				   and cc.iid = coo.coatid
				  left join ieai_script_flow fl
					on fl.iid = cc.iflowid
				  left join ieai_script_services se
					on se.iscriptuuid = fl.imxserviceid
				 where dc.ICOLHEADER = 'taskName'
				   and dw.iistimetask = 1
				   and dw.istate = 12
				   and fl.istart_type= 0
				   and fl.istatus in (20, 60, 5)
				   and dw.IITEMTYPE = 10
				   and dw.IAUDITYPE = 1) te
		  left join ieai_script_services se
			on se.iid = te.scriptiid
		 $
		 
-- 4.7.25 version script patch is as follows
BEGIN
 DECLARE LS_SQL VARCHAR(4000);
 DECLARE LI_EXISTS NUMERIC(5);
	 	
	 		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IQDBUSER';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IQDBUSER VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;
	
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'IQDBPWD';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IQDBPWD VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_TOPRODUCT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_TOPRODUCT(IID DECIMAL(19) NOT NULL ,ICONTENT BLOB(200M) NOT NULL ,IDATETIME DECIMAL(19) NOT NULL ,IUSERID DECIMAL(19) NOT NULL ,ISIZE DECIMAL(19) NOT NULL,ISCRIPTUSERID DECIMAL(19) NOT NULL ,FILENAME VARCHAR(255) NOT NULL, IDESCRIPTION VARCHAR(255) NOT NULL ,CONSTRAINT PK_IEAI_SCRIPT_TOPRODUCT PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_BS_ATOMIC';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_BS_ATOMIC (IID DECIMAL(19) NOT NULL,IATOMICBSNAME VARCHAR(255) NOT NULL,IUPERID DECIMAL(19),IBSID DECIMAL(19),IBSTYPEID DECIMAL(19),IISATOMIC DECIMAL(1), CONSTRAINT PK_IEAI_SCRIPT_BS_ATOMIC PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_LABLEANDCATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_LABLEANDCATE( IID NUMERIC (19,0) NOT NULL,   IFROM INTEGER,  ILABLEVALUE VARCHAR(100),  ILABLERELATIONID NUMERIC(19,0) NOT NULL,     ICATAVALUE VARCHAR(100),   ICATARELATIONID NUMERIC(19,0) NOT NULL,  ISERVICEID NUMERIC(19,0) NOT NULL,   CONSTRAINT PK_IEAI_SCRIPT_LABLEANDCATE PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_DBAAS_KEYWORD';
		IF LI_EXISTS = 0 THEN
   	 		SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_DBAAS_KEYWORD(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,IDESC VARCHAR(255) ,ITYPE DECIMAL(19), CONSTRAINT PK_IEAI_SCRIPT_DBAAS_KEYWORD PRIMARY KEY (IID) )';
    		PREPARE	SQLA FROM LS_SQL;
    		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_OPERRECORD';
		IF LI_EXISTS = 0 THEN
    		SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_OPERRECORD(IID DECIMAL(19) NOT NULL ,IRESID DECIMAL(19) ,ISCRIPTUUID VARCHAR(255) ,IOUTCONTENT CLOB ,IOPERUSER DECIMAL(19) ,ICHECKUSER DECIMAL(19) ,ISTATUS DECIMAL(1) ,IOPERTIME DECIMAL(19) ,IRESERVEPLANID DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPT_OPERRECORD PRIMARY KEY (IID) )';
    		PREPARE	SQLA FROM LS_SQL;
    		EXECUTE SQLA;
		END IF;


END 
$

BEGIN
 DECLARE LS_SQL VARCHAR(4000);
 DECLARE LI_EXISTS NUMERIC(5);
 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMETER' AND TABNAME='IEAI_SCRIPT_TEMPLATE_ANALY';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_TEMPLATE_ANALY ALTER  COLUMN IPARAMETER SET  DATA  TYPE VARCHAR(4000)';
		PREPARE  SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
 
END 
$

-- 4.7.25 add reorg
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_TEMPLATE_ANALY';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_TEMPLATE_ANALY'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$

-- 4.7.26 version script patch is as follows

BEGIN
 DECLARE LS_SQL VARCHAR(4000);
 DECLARE LI_EXISTS NUMERIC(5);
	 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DEVICETYPE' AND TABNAME='IEAI_SCRIPT_FLOW_TEMPLATE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_FLOW_TEMPLATE ADD COLUMN DEVICETYPE DECIMAL(1) DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF; 	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AGENT_GROUP_BUSINE' AND COLNAME = 'IEXECUSERNAME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP_BUSINE ADD IEXECUSERNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AGENT_GROUP' AND COLNAME = 'IEXECUSERNAME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP ADD IEXECUSERNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_OPER_PARAMS';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_OPER_PARAMS(IID DECIMAL(19) NOT NULL ,IOPERID DECIMAL(19) ,IPARAM_TYPE VARCHAR(500) ,IPARAM_DEFAULT_VALUE VARCHAR(4000) ,IPARAM_DESC VARCHAR(500) ,IPARAM_ORDER DECIMAL(10),CONSTRAINT PK_IEAI_SCRIPT_OPER_PARAMS PRIMARY KEY (IID) )';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_OPER_RESULT';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_OPER_RESULT(IID DECIMAL(19) NOT NULL ,IOPERID DECIMAL(19)  ,IRESULT CLOB ,CONSTRAINT PK_IEAI_SCRIPT_OPER_RESULT PRIMARY KEY (IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_CATALOING';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_CATALOING (IID NUMERIC(19) NOT NULL,INAME VARCHAR(100),ICREATEUSER VARCHAR(100), ICREATETIME NUMERIC(19),IMODIFYUSER VARCHAR(100),IMODIFYTIME NUMERIC(19), IPARENTID NUMERIC(19),CONSTRAINT PK_IEAI_SCRIPT_CATALOING PRIMARY KEY (IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_LABEL';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_LABEL (IID NUMERIC(19) NOT NULL,INAME VARCHAR(100),ICREATEUSER  VARCHAR(100), ICREATETIME NUMERIC(19),IMODIFYUSER VARCHAR(100),IMODIFYTIME NUMERIC(19), IPARENTID NUMERIC(19),CONSTRAINT PK_IEAI_SCRIPT_LABEL PRIMARY KEY (IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_RESERVE_PLAN';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_RESERVE_PLAN (IID NUMERIC(19) NOT NULL,INAME VARCHAR(100),ICREATEUSER VARCHAR(100), ICREATETIME NUMERIC(19),IMODIFYUSER VARCHAR(100),IMODIFYTIME NUMERIC(19), IPARENTID NUMERIC(19),ISERVICEID NUMERIC(19) DEFAULT 0,IDBTYPE NUMERIC(19),CONSTRAINT PK_IEAI_SCRIPT_RESERVE_PLAN PRIMARY KEY (IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_RESERVE_PLAN' AND COLNAME = 'IORDER';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_RESERVE_PLAN ADD IORDER NUMERIC(19)';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	
	
	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE  IID=1;
	  IF	LI_EXISTS = 1 THEN
	    	SET	LS_SQL = 'UPDATE IEAI_DATABASE_TYPE SET  IURL = ''jdbc:oracle:thin:@//{IP}:{PORT}/{NAME}'' ,IDRIVER=''oracle.jdbc.driver.OracleDriver'' WHERE IID = 1';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	  END	IF;
	    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE  IID=2;
	  IF	LI_EXISTS = 1 THEN
	    	SET	LS_SQL = 'UPDATE IEAI_DATABASE_TYPE SET  IURL = ''jdbc:db2://{IP}:{PORT}/{NAME}'' ,IDRIVER=''com.ibm.db2.jcc.DB2Driver'' WHERE IID = 2';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	  END	IF;
	    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE  IID=3;
	  IF	LI_EXISTS = 1 THEN
	    	SET	LS_SQL = 'UPDATE IEAI_DATABASE_TYPE SET  IURL = ''jdbc:mysql://{IP}:{PORT}/{NAME}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&autoReconnect=true&failOverReadOnly=false'' ,IDRIVER=''com.mysql.jdbc.Driver'' WHERE IID = 3';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	  END	IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AUDITING_PERMISSION' AND  COLNAME='IAUDITINGCODE';
		IF	LI_EXISTS = 1 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AUDITING_PERMISSION  ALTER COLUMN IAUDITINGCODE SET DATA TYPE DECIMAL(2,0)';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;
		
    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=10;
	  IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(10,''脚本服务化高权限'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		commit;
	  END	IF;

	  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE  IID=9;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING (IID, INAME) VALUES (9, ''查询脚本服务化复核任务查询的所有数据 '')';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRMANAGE_ID' AND TABNAME='IEAI_RESOURCE_APPLY';
    IF LI_EXISTS = 1 THEN
        SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_APPLY ALTER  COLUMN IRMANAGE_ID SET  DATA  TYPE NUMERIC(19,0)';
        PREPARE  SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
	
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SERVICE_MONITOR';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SERVICE_MONITOR( IID DECIMAL(19) NOT NULL,ICOLUMNNAME VARCHAR(255) NOT NULL,ISYMBOL VARCHAR(255) NOT NULL,		ITHRESHOLD  VARCHAR(255) NOT NULL,ITYPE   DECIMAL(19) NOT NULL, ISERVICEID   VARCHAR(255) NOT NULL,ISERVICEUUD   VARCHAR(255),CONSTRAINT PK_IEAI_SERVICE_MONITOR PRIMARY KEY (IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SERVICE_MONITOR_BIND';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SERVICE_MONITOR_BIND(IID DECIMAL(19) NOT NULL,ISERVICEUUID VARCHAR(36)  NOT NULL,IMONITORID DECIMAL(19) NOT NULL,CONSTRAINT PK_IEAI_SERVICE_MONITOR_BIND PRIMARY KEY(IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;	
	
	
		
END 
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IFUNCTIONALTYPE' AND TABNAME='IEAI_SCRIPT_SEGMENT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_SEGMENT ADD COLUMN IFUNCTIONALTYPE INTEGER DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYSTEM_CLASS_ROLE';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_SYSTEM_CLASS_ROLE (ICLASSID DECIMAL(19,0), IROLEID DECIMAL(19,0), ITYPE INTEGER)';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_RESCALL';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEAI_COLLECTION_RESCALL(ENDTIME DECIMAL(19,0), IID DECIMAL(19,0) NOT NULL,IINSTANCEID  DECIMAL(19,0),RECORDID  VARCHAR(255),STARTTIME  DECIMAL(19,0),STATUS DECIMAL(2,0),CONSTRAINT PK_IEAI_COLLECTION_RESCALL PRIMARY KEY(IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPLATEFORM' AND TABNAME='IEAI_RESOURCE_MANAGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN IPLATEFORM VARCHAR(100)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRUNDAY' AND TABNAME='IEAI_RESOURCE_MANAGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN IRUNDAY VARCHAR(25)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IIEXISTSCFG' AND TABNAME='IEAI_SCRIPT_FLOW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_FLOW ADD COLUMN IIEXISTSCFG INTEGER DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISWARNDBMONITOR' AND TABNAME='IEAI_SCRIPT_FLOW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_FLOW ADD COLUMN IISWARNDBMONITOR INTEGER DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEIA_SCRIPT_SERVICE_RELATION';
	IF LI_EXISTS = 0 THEN
    	SET LS_SQL ='CREATE TABLE IEIA_SCRIPT_SERVICE_RELATION(IID NUMERIC(19,0) NOT NULL,IUUID VARCHAR(255),IFLOWID NUMERIC(19,0)NOT NULL,ICOATID NUMERIC(19,0),ISERVICENO VARCHAR (255),IRESID NUMERIC(19,0), ISWARN INTEGER DEFAULT 0,CONSTRAINT PK_IEIA_SCR_SER_REL PRIMARY KEY(IID))';
    	PREPARE	SQLA FROM LS_SQL;
    	EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IKEY' AND TABNAME='IEAI_SCRIPT_OPER_RESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD COLUMN IKEY DECIMAL(10)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	DECLARE LI_EXISTS1 NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISSCRIPT' AND TABNAME='IEAI_EMER_SCENE_SS_RELATION' AND TYPENAME<>'INTEGER';
		IF LI_EXISTS > 0 THEN 
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS1 FROM SYSCAT.COLUMNS WHERE COLNAME='IISSCRIPTTEMP' AND TABNAME='IEAI_EMER_SCENE_SS_RELATION';
			IF LI_EXISTS1 = 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_EMER_SCENE_SS_RELATION ADD COLUMN IISSCRIPTTEMP INTEGER DEFAULT 0';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
		END IF;	
			
	END
$
BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_EMER_SCENE_SS_RELATION'') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION' AND COLNAME = 'IISSCRIPT' AND TYPENAME<>'INTEGER';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'UPDATE IEAI_EMER_SCENE_SS_RELATION SET IISSCRIPTTEMP=IISSCRIPT';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'UPDATE IEAI_EMER_SCENE_SS_RELATION SET IISSCRIPT=NULL';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'ALTER TABLE IEAI_EMER_SCENE_SS_RELATION ALTER COLUMN IISSCRIPT SET DATA TYPE INTEGER';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'ALTER TABLE IEAI_EMER_SCENE_SS_RELATION ALTER COLUMN IISSCRIPT SET DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$
BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_EMER_SCENE_SS_RELATION'') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION' AND COLNAME = 'IISSCRIPTTEMP';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'UPDATE IEAI_EMER_SCENE_SS_RELATION SET IISSCRIPT=IISSCRIPTTEMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			COMMIT;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION' AND COLNAME = 'IISSCRIPTTEMP';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_EMER_SCENE_SS_RELATION DROP COLUMN IISSCRIPTTEMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$
BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_EMER_SCENE_SS_RELATION'')';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
END
$
-- 4.7.26 add reorg
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_OPER_RESULT';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_OPER_RESULT'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_FLOW_TEMPLATE';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_FLOW_TEMPLATE'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_AUDITING_PERMISSION';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_AUDITING_PERMISSION'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ASM_RESULT';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_ASM_RESULT(IID DECIMAL(19) NOT NULL,IRESID DECIMAL(19),ITOTAL VARCHAR(255),IFREE  VARCHAR(255),IUSABLE VARCHAR(255),IDG_NAME VARCHAR(255),IDG_TYPE VARCHAR(255),ID_NUMBER DECIMAL(19),IDISK_NAME VARCHAR(255),ISTATE DECIMAL(19),ID_TOTAL VARCHAR(255),ID_FREE VARCHAR(255),ITIME DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPT_ASM_RESULT PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRACGROUP' AND TABNAME='IEAI_RESOURCE_MANAGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN IRACGROUP VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	
END
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IOPERID1'   AND TABNAME='IEAI_SCRIPT_AUDITING_IPS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_AUDITING_IPS  ADD  IOPERID1  DECIMAL(19,0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		
		
	END IF;
	
END
$
 
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_AUDITING_IPS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_AUDITING_IPS'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
 
end
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IOPERID1'  AND TABNAME='IEAI_SCRIPT_AUDITING_IPS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='UPDATE  IEAI_SCRIPT_AUDITING_IPS   SET  IOPERID1= IOPERID';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
		END IF;
		
		SELECT COUNT(1) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME in('IOPERID1','IOPERID')  AND TABNAME='IEAI_SCRIPT_AUDITING_IPS';
		IF LI_EXISTS = 2 THEN
			SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_AUDITING_IPS DROP COLUMN IOPERID';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IOPERID1'  AND TABNAME='IEAI_SCRIPT_AUDITING_IPS';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_AUDITING_IPS RENAME COLUMN IOPERID1 TO IOPERID';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	 
end
$


BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE  TABNAME='IEAI_SCRIPT_AUDITING_IPS';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='call sysproc.admin_cmd(''reorg table IEAI_SCRIPT_AUDITING_IPS'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
end
$
--4.7.27
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_LABLE_RELATION';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_LABLE_RELATION(IID DECIMAL(19) NOT NULL ,ILABLEID DECIMAL(19) ,ISERVICEID DECIMAL(19) , CONSTRAINT PK_IEAI_SCRIPT_LABLE_RELATION PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

end
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISCRIPTCONTENT' AND TABNAME='IEAI_SCRIPT_OPERRECORD';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPERRECORD  ADD COLUMN ISCRIPTCONTENT CLOB';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

end
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRACPORT'  AND TABNAME='IEAI_SCRIPT_ASM';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_ASM ADD COLUMN IRACPORT VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRACPASSWORD'  AND TABNAME='IEAI_SCRIPT_ASM';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_ASM ADD COLUMN IRACPASSWORD VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRACSID'  AND TABNAME='IEAI_SCRIPT_ASM';
	IF LI_EXISTS =0 THEN
		SET LS_SQL ='ALTER  TABLE  IEAI_SCRIPT_ASM ADD COLUMN IRACSID VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPTS_UNMONITORED';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCRIPTS_UNMONITORED(IID DECIMAL(19) NOT NULL,IRESID DECIMAL(19),IFLOWID DECIMAL(19),ICOATID DECIMAL(19),ISCRIPTUUID VARCHAR(255),IPARENTFLOWID DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPTS_UNMONITORED PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	

end
$

-- 4.7.28 version script patch is as follows
BEGIN
    DECLARE LS_SQL VARCHAR(4000);
    DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IUUID' AND TABNAME='IEAI_SCRIPT_TOPRODUCT';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_TOPRODUCT ADD COLUMN IUUID VARCHAR(100)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$


BEGIN
    DECLARE LS_SQL VARCHAR(4000);
    DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISQLKEY' AND TABNAME='IEAI_SCRIPT_OPER_RESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD COLUMN ISQLKEY VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IKEY' AND TABNAME='IEAI_SCRIPT_OPER_RESULT';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPER_RESULT DROP COLUMN IKEY';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;		
END
$

BEGIN
    DECLARE LS_SQL VARCHAR(2000);
    DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CHECKTYPE' AND TABNAME='IEAI_SCRIPT_DANGER_CMD';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DANGER_CMD ADD COLUMN CHECKTYPE VARCHAR(2)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$

BEGIN
   DECLARE LS_SQL VARCHAR(2000);
   DECLARE  LI_EXISTS  NUMERIC(2);
        
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_AUDITING_DANGER';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_AUDITING_DANGER( IID DECIMAL(19) NOT NULL ,ISCRIPTNAME VARCHAR(255),ITASKNAME VARCHAR(255),ICOMMAND VARCHAR(255),IIP VARCHAR(255),IEXECUSER VARCHAR(255),IEXECTIME TIMESTAMP(6),IBUTTERFLYVERSION VARCHAR(255),ITASKTYPE DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPT_AUDITING_DANGER PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;	
END
$


BEGIN
   DECLARE LS_SQL VARCHAR(2000);
   DECLARE  LI_EXISTS  NUMERIC(2);
        
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPTS_RELATON';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCRIPTS_RELATON(IID DECIMAL(19) NOT NULL,INAME  VARCHAR(255),ISERVICEID VARCHAR(255) ,IPARENTID DECIMAL(19) ,IPARENTSERVICEID VARCHAR(255) ,IROOTID DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPTS_RELATON PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPTS_REL_COLUMN';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCRIPTS_REL_COLUMN(IID DECIMAL(19) NOT NULL, IRELATIONID DECIMAL(19),IPARAMORDER DECIMAL(10), ISERVICEID VARCHAR(255) ,IRELSERVICEID VARCHAR(255) ,IRELCOLUMN VARCHAR(255) ,CONSTRAINT PK_IEAI_SCRIPTS_REL_COLUMN PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IOPERKEY' AND TABNAME='IEAI_SCRIPT_OPERRECORD';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPERRECORD ADD COLUMN IOPERKEY DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISERVICEID' AND TABNAME='IEAI_SCRIPT_OPERRECORD';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPERRECORD ADD COLUMN ISERVICEID VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DBAAS_CENTER_DBINFO';
		IF LI_EXISTS = 0 THEN
		 SET LS_SQL =' CREATE TABLE IEAI_DBAAS_CENTER_DBINFO( IID DECIMAL(19,0) NOT NULL, ISYSNAME VARCHAR(255), ISYSNAME_VERSION VARCHAR(25), IDBUSERNAME VARCHAR(50), IDBPAWD VARCHAR(50), ICREATTIME DECIMAL(19,0), ICREATUSERID DECIMAL(19,0), ISTATE DECIMAL(2) DEFAULT 0, CONSTRAINT PK_IEAI_DBAAS_CENTER_DBINFO PRIMARY KEY(IID))';
		        PREPARE	SQLA FROM LS_SQL;
		        EXECUTE SQLA;
		END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_LASTTABLENAME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_LASTTABLENAME(IID DECIMAL(19)  NOT NULL,ISCRIPTUUID VARCHAR(255) ,ILASTOUT_TABLENAME VARCHAR(255),CONSTRAINT PK_IEAI_SCRIPT_LASTTABLENAME PRIMARY KEY (IID) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DBNAME' AND TABNAME='IEAI_RESOURCE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN DBNAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='INSTANCE_NAME' AND TABNAME='IEAI_RESOURCE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN INSTANCE_NAME VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='NLS_CHARACTERSET' AND TABNAME='IEAI_RESOURCE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN NLS_CHARACTERSET VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='NLS_NCHAR_CHARACTERSET' AND TABNAME='IEAI_RESOURCE_MANAGE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_RESOURCE_MANAGE ADD COLUMN NLS_NCHAR_CHARACTERSET VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DBAAS_CHECK_RULE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_DBAAS_CHECK_RULE(IID DECIMAL(19) NOT NULL,ICODE VARCHAR(255),IDES VARCHAR(255),
			CONSTRAINT PK_IEAI_DBAAS_CHECK_RULE PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DBAAS_LOCAL_RESOURCE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL =' CREATE TABLE IEAI_DBAAS_LOCAL_RESOURCE(IID DECIMAL(19) NOT NULL,INAME VARCHAR(255) NULL,IRSTYPE VARCHAR(100) NULL,IIP VARCHAR(15) NULL,IBUSINESSID DECIMAL(19) NOT NULL,IBUSINESSNAME VARCHAR(255),IDBUSER VARCHAR(255),IDBPWD VARCHAR(50),IAPPLYUSER VARCHAR(255),IUSERID DECIMAL(19),ISID VARCHAR(255),IDBPORT DECIMAL(10),IVERSION VARCHAR(255),ICREATETIME DECIMAL(19),ISYSUSESR VARCHAR(255),ISYSPWD VARCHAR(50),ISYSPORT DECIMAL(10),CONSTRAINT PK_IEAI_DBAAS_LOCAL_RESOURCE PRIMARY KEY(IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;

END
$

-- 4.7.29 version script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_UPDATE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL =' CREATE TABLE  IEAI_RESOURCE_UPDATE(IID DECIMAL(19,0) NOT NULL,IOPERID DECIMAL(19,0) NOT NULL,IRESID DECIMAL(19,0) NOT NULL,ISTATUS INTEGER DEFAULT 0,CONSTRAINT PK_IEAI_RESOURCE_UPDATE  PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_UPDATE_MSG';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE  IEAI_RESOURCE_UPDATE_MSG(IID DECIMAL(19,0) NOT NULL,IOPERID DECIMAL(19,0) NOT NULL,IMESS varchar(2000),CONSTRAINT PK_IEAI_RESOURCE_UPDATE_MSG  PRIMARY KEY (IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_REL_BUSINESS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_RESOURCE_REL_BUSINESS(IRESID DECIMAL(19,0) NOT NULL,ISYSID DECIMAL(19,0) NOT NULL,PRIMARY KEY(IRESID,ISYSID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE_MANAGE' AND COLNAME = 'INAME' AND TYPENAME<>'VARCHAR';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RESOURCE_MANAGE ALTER COLUMN INAME SET DATA TYPE  VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RESOURCE' AND COLNAME = 'INAME' AND TYPENAME<>'VARCHAR';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RESOURCE ALTER COLUMN INAME SET DATA TYPE  VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
END
$
BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_MANAGE';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RESOURCE_MANAGE'') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RESOURCE'') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PROTOCOL' AND TABNAME='IEAI_ISSUE_CONFIG';
        IF LI_EXISTS = 0 THEN
            SET LS_SQL ='ALTER TABLE IEAI_ISSUE_CONFIG ADD COLUMN PROTOCOL VARCHAR(5) DEFAULT ''FTP''';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PROTOCOL' AND TABNAME='IEAI_SCRIPT_F_EXTRACT_CONFIG';
        IF LI_EXISTS = 0 THEN
            SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_F_EXTRACT_CONFIG ADD COLUMN PROTOCOL VARCHAR(5) DEFAULT ''FTP''';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_EDIT_TEMPLATE';
        IF LI_EXISTS = 0 THEN
            SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_EDIT_TEMPLATE(IID DECIMAL(19,0) NOT NULL ,ICONTENT CLOB NOT NULL ,ITYPE VARCHAR(255) NOT NULL ,INAME VARCHAR(255) NOT NULL ,ICREATEUSERID DECIMAL(19,0) NOT NULL ,ISCRIPTTYPE VARCHAR(10) NOT NULL ,CONSTRAINT PK_IEAI_SCRIPT_EDIT_TEMPLATE PRIMARY KEY(IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BILOCALCHECK_DATA';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_BILOCALCHECK_DATA (IID NUMERIC (19,0) NOT NULL,VERSIONSTATE VARCHAR (16),IWORKITEMID NUMERIC (19,0),STARTTIME NUMERIC (19,0),ENDTIME NUMERIC (19,0),TASK_TYPE NUMERIC (2,0),CONSTRAINT PK_IEAI_BILOCALCHECK_DATA PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 

END
$

-- 8.0 version Script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISHUTDOWNUSERID' AND TABNAME='IEAI_SCRIPT_AUDITING_IPS';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_AUDITING_IPS ADD COLUMN ISHUTDOWNUSERID DECIMAL(19)  DEFAULT -1';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISAUTOSUB' AND TABNAME='IEAI_SCRIPT_SHARE';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_SHARE ADD  ISAUTOSUB DECIMAL(1,0) ';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IMPAGENTIP';
	IF LI_EXISTS = 1 THEN
	SET LS_SQL ='DROP TABLE TMP_IMPAGENTIP';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IMPAGENTIP';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE GLOBAL TEMPORARY TABLE TMP_IMPAGENTIP( AGENTIP VARCHAR(200), PORT VARCHAR(200),OSTYPE VARCHAR(200),CONFIGPARAMS VARCHAR(200),ISHUTDOWNUSERNAME VARCHAR(100)) ON COMMIT DELETE ROWS';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$
 
BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_FLOW' AND INDNAME='IDX_IEAI_SCRIPT_FLOW';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_IEAI_SCRIPT_FLOW ON IEAI_SCRIPT_FLOW (IWORKITEMID)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
END
$

-- 8.1.0 version Script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=11;
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(11,''数据库服务化控制服务启动方式权限'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
		END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_APPSYSTEM_PROP WHERE iid=1;
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_APPSYSTEM_PROP(IID,IPROPNAME,IORDER,ICREATETIME,IMODIFYTIME,IDEL)  VALUES(1,''DBA-A'',0,FUN_GET_DATE_NUMBER(CURRENT TIMESTAMP, 8), FUN_GET_DATE_NUMBER(CURRENT TIMESTAMP, 8),0)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
		END	IF;
		  
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_APPSYSTEM_PROP WHERE iid=2;
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_APPSYSTEM_PROP(IID,IPROPNAME,IORDER,ICREATETIME,IMODIFYTIME,IDEL)  VALUES(2,''DBA-B'',0,FUN_GET_DATE_NUMBER(CURRENT TIMESTAMP, 8), FUN_GET_DATE_NUMBER(CURRENT TIMESTAMP, 8),0)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
		END	IF; 
END
$
BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_SQL_RESULT';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SQL_RESULT(IID  DECIMAL(19) NOT NULL, IFOREIGNID VARCHAR(100), IFOREIGNHEADID DECIMAL(19), IRESULT CLOB, ISQLKEY VARCHAR(255), IFROM DECIMAL(1), ISQL CLOB, CONSTRAINT PK_IEAI_SCRIPT_SQL_RESULT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 
		
			 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_SQL_HEAD';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SQL_HEAD(IID DECIMAL(19) NOT NULL, IFOREIGNID VARCHAR(100), IRESULT CLOB,IFROM DECIMAL(1),ISQLKEY VARCHAR(255),CONSTRAINT PK_IEAI_SCRIPT_SQL_HEAD PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 

END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_DEVICE';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE  IEAI_RESOURCE_DEVICE(IID  DECIMAL(19) NOT NULL,  IRESOURCEID DECIMAL(19), IDEVICENAME VARCHAR(255), CONSTRAINT PK_IEAI_RESOURCE_DEVICE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 
		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_FAILOPER_CONN';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_SCRIPT_FAILOPER_CONN(IID DECIMAL(19,0) NOT NULL,IRESID DECIMAL(19,0),IRESMAINPID VARCHAR(255),IAPPIP VARCHAR(25),IUSERID DECIMAL(19,0),ICREATETIME DECIMAL(19,0),ILASTUPDATETIME DECIMAL(19,0),IKILLFLAG INTEGER DEFAULT 0,CONSTRAINT PK_IEAI_SCRIPT_FAIL_CONN PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISQL' AND TABNAME='IEAI_SCRIPT_OPER_RESULT';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD  ISQL CLOB';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_FAILOPER_PARA';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_SCRIPT_FAILOPER_PARA(IID DECIMAL(19,0) NOT NULL,IUSERID DECIMAL(19,0),IMAINPID VARCHAR(255),IUUID VARCHAR(255),IPARAMVALUE VARCHAR(255),IPARAORDER INTEGER,CONSTRAINT PK_IEAI_SCRIPT_FAILOPER_PARA PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RESOURCE_AWR';
		 IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_RESOURCE_AWR (IRESID DECIMAL(19) NOT NULL,AWR CLOB,CONSTRAINT PK_IEAI_RESOURCE_AWR PRIMARY KEY (IRESID))';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END	IF;	 
END
$

-- 8.2.0 version Script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IFULLCONTENT' AND TABNAME='IEAI_SCRIPT_AUDITING_DANGER';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_AUDITING_DANGER ADD COLUMN IFULLCONTENT VARCHAR(1000)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_BILOCALCHECK_DATA' AND COLNAME = 'VERSIONS';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_BILOCALCHECK_DATA ADD  VERSIONS VARCHAR(50)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_EXEC_EMAIL';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_EXEC_EMAIL(IID DECIMAL(19)  NOT NULL,IWORKITEMID DECIMAL(19) ,IUSERID DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPT_EXEC_EMAIL PRIMARY KEY(IID) )';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$
-- 8.3.0 version Script patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IISSYNC' AND TABNAME='IEAI_SCRIPT_TEST';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_TEST ADD IISSYNC DECIMAL(1,0) default 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_AUDI_ATTACHMENT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='	CREATE TABLE IEAI_SCRIPT_AUDI_ATTACHMENT   (IID DECIMAL(19,0) NOT NULL, IWORKITEMID  DECIMAL(19,0), INAME   VARCHAR(500),ISIZE  DECIMAL(19,0),IUPLOADTIME	DECIMAL(19,0),ICONTENTS  	 BLOB(3670016),CONSTRAINT PK_IEAI_SCRIPT_AUDI_ATTACHMENT  PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ATTACH_REL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='	CREATE TABLE IEAI_SCRIPT_ATTACH_REL(IID DECIMAL(19,0) NOT NULL,IWORKITEMID DECIMAL(19,0),IATTACHID DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_ATTACH_REL PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_TEST_TRY';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='	CREATE TABLE IEAI_SCRIPT_TEST_TRY(IID DECIMAL(19,0) NOT NULL,ISCRIPTUUID VARCHAR(50),IISTRY INTEGER DEFAULT 0,ISCHANGE INTEGER DEFAULT 0,ITRYGROUPID DECIMAL(19,0),ITRYUSERID DECIMAL(19,0),ICREATEUSERID DECIMAL(19,0),RETURNTEXT CLOB,ICREATETIME DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_TEST_TRY PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE IID=12;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'insert into IEAI_SCRIPT_AUDITING (IID,INAME)  VALUES(12,''同步到生产审核权限'')';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IFROM' AND TABNAME='IEAI_SCRIPT_DATASOURCE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD IFROM DECIMAL(1,0) default 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSID' AND TABNAME='IEAI_SCRIPT_DATASOURCE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD ISYSID DECIMAL(19,0) ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISBLJ' AND TABNAME='IEAI_SCRIPT_DATASOURCE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD ISBLJ DECIMAL(1,0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='	 CREATE TABLE IEAI_SCRIPT_DATA_CHANGE(IID DECIMAL(19,0) NOT NULL,IDATA_TASK_NO VARCHAR(255),IDATA_NO VARCHAR(255),IDATA_CONTENT VARCHAR(4000),ISYSTEM_NAME VARCHAR(255),ISYSTEM_ID DECIMAL(19,0),IDBTYPE INTEGER,ISQL_CONTENT CLOB,ISYS_ORG_ID DECIMAL(19,0),ISYS_ORG_NAME VARCHAR(255),IUSERID DECIMAL(19,0),IAUDI_USERID VARCHAR(255),IEXECTIME VARCHAR(25),ICREATETIME DECIMAL(19,0),ISTATUS INTEGER,IMODIFYTIME DECIMAL(19,0),ISUBMITTIME DECIMAL(19,0),IISAUDI INTEGER,IREVIEWTIME DECIMAL(19,0),IREVIEW_OPINIONS VARCHAR(255),IREFUSETIME DECIMAL(19,0),IREFUSE_REASON VARCHAR(255),ISERVERIP VARCHAR(25),ISERVERPORT INTEGER, ISTARTTIME DECIMAL(19,0), IENDTIME DECIMAL(19,0),IAUDITIME DECIMAL(19,0),IAUDINAME VARCHAR(50),CONSTRAINT PK_IEAI_SCRIPT_DATA_CHANGE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='	CREATE TABLE IEAI_SCRIPT_DATA_CHANGE_INS ( IID DECIMAL(19,0) NOT NULL, ITASKID DECIMAL(19,0), IAGENT_IP VARCHAR(25), IGANET_PORT DECIMAL(10), IDBTYPE INTEGER, IDBDRIVER VARCHAR(100), IDBNAME VARCHAR(50), IDBUSER VARCHAR(50), IDBURL VARCHAR(255), IBLJ INTEGER, IDBPD VARCHAR(100), IUUID VARCHAR(60), ISTARTTIME DECIMAL(19,0), IENDTIME DECIMAL(19,0), ISTATUS INTEGER DEFAULT 0, ISTDOUT CLOB, ISTDLASTLINE CLOB, CONSTRAINT PK_IEAI_SCRIPT_DATA_CHANGE_INS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_DATACHANGE_NO';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_DATACHANGE_NO(IID  DECIMAL(19,0) NOT NULL,ITABLENAME VARCHAR(50),IDATE DECIMAL(8) NOT NULL,INO DECIMAL(10),CONSTRAINT PK_IEAI_SCRIPT_DATACHANGE_NO PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DATACHANGE_NO WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_SCRIPT_DATACHANGE_NO(IID,ITABLENAME,IDATE,INO) VALUES(1,''SJBG'',20201225,1)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DATACHANGE_NO WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_SCRIPT_DATACHANGE_NO(IID,ITABLENAME,IDATE,INO) VALUES(2,''SJBGRW'',20201225,1)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM  SYSCAT.COLUMNS WHERE LENGTH=255 AND  COLNAME='IDESCRIPTION' AND TABNAME='IEAI_SCRIPT_TOPRODUCT';
    IF	LI_EXISTS = 1 THEN
        SET	LS_SQL ='ALTER TABLE IEAI_SCRIPT_TOPRODUCT ALTER IDESCRIPTION SET DATA TYPE VARCHAR(1000)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END	IF;
END
$
-- 8.4.0 version Script patch is as follows
BEGIN
    DECLARE LS_SQL VARCHAR(2000);
    DECLARE  LI_EXISTS  NUMERIC(2);
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_AUDIT_SYS_AGENT';
IF LI_EXISTS = 0 THEN
        SET LS_SQL ='CREATE TABLE  IEAI_SCRIPT_AUDIT_SYS_AGENT(IID DECIMAL(19) NOT NULL,IWORKITEMID DECIMAL(19) ,ISYSID DECIMAL(19) ,IAGENTID DECIMAL(19),CONSTRAINT PK_IEAI_SCRIPT_AUDIT_SYS_AGENT primary key(IID))';
PREPARE	SQLA FROM LS_SQL;
EXECUTE SQLA;
END IF;

	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_SYS_CP_RELATION';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SYS_CP_RELATION (IID DECIMAL(19,0) NOT NULL,ISCRIPTUUID VARCHAR(255),ISYSTEMID DECIMAL(19,0),ICPID DECIMAL(19,0), CONSTRAINT PK_IEAI_SCRIPT_SYS_CP_RELATION PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_SYSTEM_RELATION';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_SYSTEM_RELATION (IID DECIMAL(19,0) NOT NULL,ISCRIPTUUID VARCHAR(255),ISYSTEMID DECIMAL(19,0), CONSTRAINT PK_IEAI_SCRIPT_SYSTEM_RELATION PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATE' AND TABNAME='IEAI_SCRIPT_DATASOURCE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD ISTATE INTEGER';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICOLLECT' AND TABNAME='IEAI_SCRIPT_DATASOURCE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD ICOLLECT VARCHAR(255)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISBZ' AND TABNAME='IEAI_SCRIPT_DATA_CHANGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATA_CHANGE ADD ISBZ INTEGER DEFAULT 0';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='BZ' AND TABNAME='IEAI_SCRIPT_DATA_CHANGE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_DATA_CHANGE ADD BZ CLOB';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$
-- 8.5.0 version Script patch is as follows
BEGIN
    DECLARE LS_SQL VARCHAR(2000);
    DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PERMISSION' AND TABNAME='IEAI_SCRIPT_ISSUERECORD';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD COLUMN PERMISSION VARCHAR(10)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='USERPERMISSION' AND TABNAME='IEAI_SCRIPT_ISSUERECORD';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD COLUMN USERPERMISSION VARCHAR(50)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GROUPPERMISSION' AND TABNAME='IEAI_SCRIPT_ISSUERECORD';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD COLUMN GROUPPERMISSION VARCHAR(50)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND  COLNAME='ITIMEOUT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SHARE ADD ITIMEOUT NUMERIC(19) DEFAULT -1'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_INSTANCE' AND  COLNAME='ITIMEOUTVALUE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_INSTANCE ADD ITIMEOUTVALUE NUMERIC(5) DEFAULT -1';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_BSTYPETHREELEVEL';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_BSTYPETHREELEVEL (IID NUMERIC (19)  NOT NULL,THREETYPENAME VARCHAR (255) ,TWOBSTYPEID NUMERIC (19) ,IDEL NUMERIC (19) DEFAULT 0, CONSTRAINT PK_SCRIPT_BSTYPETHREELEVEL PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND  COLNAME='THREETYPEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD THREETYPEID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND  COLNAME='THREETYPENAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD THREETYPENAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND  COLNAME='THREETYPEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SHARE ADD THREETYPEID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND  COLNAME='THREETYPENAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SHARE ADD THREETYPENAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND  COLNAME='THREETYPEID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD THREETYPEID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND  COLNAME='THREETYPENAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD THREETYPENAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='ISJDBC';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISJDBC INTEGER DEFAULT 0'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='ISQLBAK';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISQLBAK  clob'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='ISQLROLLBACK';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISQLROLLBACK  clob'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='ISBAK';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISBAK INTEGER'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IMODELFLAG';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IMODELFLAG INTEGER DEFAULT 0'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='ISCRIPTUUID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISCRIPTUUID VARCHAR(255)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IBAKSTATUS';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBAKSTATUS INTEGER'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IBACKSTATUS';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBACKSTATUS INTEGER'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IBAKSTARTTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBAKSTARTTIME DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IBAKENDTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBAKENDTIME DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IBACKSTARTTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBACKSTARTTIME DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IBACKENDTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBACKENDTIME DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IEXECSTATUS';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IEXECSTATUS INTEGER'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IEXECSTARTTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD  IEXECSTARTTIME DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE' AND  COLNAME='IEXECENDTIME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IEXECENDTIME DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND  COLNAME='IDBENCODE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBENCODE VARCHAR(255)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND  COLNAME='IDBPARAMS';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBPARAMS VARCHAR(3000)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND  COLNAME='IDBIP';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBIP VARCHAR(25)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND  COLNAME='IDBPORT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBPORT DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND  COLNAME='ISTEPID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD ISTEPID DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND  COLNAME='IRESID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IRESID DECIMAL(19,0)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATASOURCE' AND  COLNAME='IDBENCODE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD IDBENCODE VARCHAR(255)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATASOURCE' AND  COLNAME='IDBIP';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD IDBIP VARCHAR(25)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
							SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATASOURCE' AND  COLNAME='ISU_USER';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD ISU_USER VARCHAR(25)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
						
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DBBINDSERVICE';
      IF	LI_EXISTS = 0 THEN
          SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DBBINDSERVICE( IID  DECIMAL(19) NOT NULL,  ISERUUID VARCHAR(255),  IDBTYPE  DECIMAL(19),  IOSTYPE  VARCHAR(10),  CONSTRAINT PK_IEAI_SCRIPT_DBBINDSERVICE PRIMARY KEY(IID))';
          PREPARE	SQLA FROM LS_SQL;
          EXECUTE SQLA;
      END IF;
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DC_PARAM';
      IF	LI_EXISTS = 0 THEN
          SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DC_PARAM(IID DECIMAL(19) NOT NULL,  ITASKID DECIMAL(19),  ITYPE VARCHAR(25),IVALUE VARCHAR(255),  IORDER INTEGER,ISCRIPTUUID VARCHAR(255),CONSTRAINT PK_IEAI_SCRIPT_DC_PARAM PRIMARY KEY (IID))';
          PREPARE	SQLA FROM LS_SQL;
          EXECUTE SQLA;
      END IF;
      SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_USER_DEPARTMENT';
      IF	LI_EXISTS = 0 THEN
          SET	LS_SQL = 'CREATE TABLE IEAI_USER_DEPARTMENT(IID DECIMAL(19,0) NOT NULL,IDEPNAME VARCHAR(64),IORDER INTEGER,CONSTRAINT PK_IEAI_USER_DEPARTMENT PRIMARY KEY (IID))';
          PREPARE	SQLA FROM LS_SQL;
          EXECUTE SQLA;
      END IF;

END
$

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID=4;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_SCRIPT_DBBINDSERVICE (IID, IDBTYPE, IOSTYPE) values (4, 1, ''win'')';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
					SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID=5;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_SCRIPT_DBBINDSERVICE (IID, IDBTYPE, IOSTYPE) values (5, 2, ''win'')';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
					SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID=6;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_SCRIPT_DBBINDSERVICE (IID, IDBTYPE, IOSTYPE) values (6, 3, ''win'')';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
					SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID=1;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_SCRIPT_DBBINDSERVICE (IID, IDBTYPE, IOSTYPE) values (1, 1, ''linux'')';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
					SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID=2;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_SCRIPT_DBBINDSERVICE (IID, IDBTYPE, IOSTYPE) values (2, 2, ''linux'')';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
					SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID=3;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_SCRIPT_DBBINDSERVICE (IID, IDBTYPE, IOSTYPE) values (3, 3, ''linux'')';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
				SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_USER_DEPARTMENT WHERE IID=1;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_USER_DEPARTMENT (IID, IDEPNAME, IORDER) values (1, ''应用管理一处'', 1)';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
					SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_USER_DEPARTMENT WHERE IID=2;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='insert into IEAI_USER_DEPARTMENT (IID, IDEPNAME, IORDER) values (2, ''应用管理二处'', 2)';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_USER_DEPARTMENT WHERE IID=3;
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL ='	insert into IEAI_USER_DEPARTMENT (IID, IDEPNAME, IORDER) values (3, ''应用管理三处'', 3)';
			 PREPARE SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
END
$
--8.6.0
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TRY_TRY' AND  COLNAME='ICONTENT';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TRY_TRY ADD ICONTENT CLOB '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DATABASE_ENV';
      IF	LI_EXISTS = 0 THEN
          SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DATABASE_ENV(IID    DECIMAL(19) NOT NULL,  IDSID  DECIMAL(19) NOT NULL,  IORDER DECIMAL(19),  INAME  VARCHAR(255),  IVALUE VARCHAR(255),  CONSTRAINT PK_IEAI_SCRIPT_DATABASE_ENV PRIMARY KEY (IID))';
          PREPARE	SQLA FROM LS_SQL;
          EXECUTE SQLA;
      END IF;
      SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_DATASOURCE' AND  COLNAME='IMSG';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD IMSG CLOB'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
      SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND  COLNAME='ISYSIDEN';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD ISYSIDEN VARCHAR(25)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
--8.7-------------
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_TEST' AND  COLNAME='CREATEUSERNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_TEST ADD CREATEUSERNAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND  COLNAME='CREATEUSERNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD CREATEUSERNAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND  COLNAME='IUPDATEUSERID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SHARE ADD IUPDATEUSERID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SHARE' AND  COLNAME='CREATEUSERNAME';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SHARE ADD CREATEUSERNAME VARCHAR(255) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_SERVICES' AND  COLNAME='IUPDATEUSERID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IUPDATEUSERID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;

         SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_FLOW' AND INDNAME='IDX_SCRIPT_FLOW_SCRIPTUUID';
         IF	LI_EXISTS = 0 THEN
             SET	LS_SQL = 'CREATE INDEX IDX_SCRIPT_FLOW_SCRIPTUUID ON IEAI_SCRIPT_FLOW (IMXSERVICEID)';
             PREPARE	SQLA FROM LS_SQL;
             EXECUTE SQLA;
         END	IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_ITSMPUBLISH';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_ITSMPUBLISH (IID NUMERIC (19)  NOT NULL,WORKITEMIID NUMERIC (19) ,SCRIPTIID NUMERIC (19) ,SCRIPTUUID VARCHAR (50) ,SCRIPTLEVEL NUMERIC(1),TASKUUID VARCHAR (50) , CONSTRAINT PK_IEAI_SCRIPT_ITSMPUBLISH PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
         SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_ITSMPUBLISH' AND INDNAME='IDX_SCRIPT_ITSM_TASKUUID';
         IF	LI_EXISTS = 0 THEN
             SET	LS_SQL = 'CREATE INDEX IDX_SCRIPT_ITSM_TASKUUID ON IEAI_SCRIPT_ITSMPUBLISH (TASKUUID)';
             PREPARE	SQLA FROM LS_SQL;
             EXECUTE SQLA;
         END	IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ITSMTASK' AND  COLNAME='ISCRIPTBUSSTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_ITSMTASK ADD ISCRIPTBUSSTYPE NUMERIC(1) ';
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTINFO' AND  COLNAME='IUNUSERD';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTINFO ADD IUNUSERD NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_AGENTINFO' AND  COLNAME='IAUDITSTATE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_AGENTINFO ADD IAUDITSTATE NUMERIC(19) DEFAULT -1
'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IID=50005;
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL ='INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(50005,''未启用设备管理'',10,''notEnableEquipList.do'',61,''脚本服务化'','''')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END	IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_CIB_HTMLFILE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_CIB_HTMLFILE (IID NUMERIC (19)  NOT NULL,ITASKUUID VARCHAR (50) ,IFILENAME VARCHAR (100) ,IHTMLFILE CLOB  , CONSTRAINT PK_IEAI_SCRIPT_CIB_HTMLFILE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_CIB_HTMLFILE' AND INDNAME='IDX_SCRIPT_ITSMHTML_TASKUUID';
        IF	LI_EXISTS = 0 THEN
               SET	LS_SQL = 'CREATE INDEX IDX_SCRIPT_ITSMHTML_TASKUUID ON IEAI_SCRIPT_CIB_HTMLFILE (ITASKUUID)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
        END	IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_ATTACHMENT_TEMP';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_ATTACHMENT_TEMP (IID NUMERIC (19)  NOT NULL,ISCRUPTUUID VARCHAR (50) ,INAME VARCHAR (200) ,ISIZE NUMERIC (19) ,IUPLOADTIME NUMERIC (19) ,ICONTENTS BLOB  ,IWORKITEMID NUMERIC (19) ,ISERVERIP VARCHAR(100), CONSTRAINT PK_IEAI_SCRIPT_ATTACHMENT_TEMP PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_ATTACHMENT_TEMP' AND INDNAME='IDX_IEAI_SCRIPT_FUJIAN_TEMP';
        IF	LI_EXISTS = 0 THEN
          SET	LS_SQL = 'CREATE INDEX IDX_IEAI_SCRIPT_FUJIAN_TEMP ON IEAI_SCRIPT_ATTACHMENT_TEMP (IWORKITEMID)';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
        END	IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IMPAGENTIP';
    IF LI_EXISTS = 1 THEN
    SET LS_SQL ='DROP TABLE TMP_IMPAGENTIP';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IMPAGENTIP';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE GLOBAL TEMPORARY TABLE TMP_IMPAGENTIP( AGENTIP VARCHAR(200), PORT VARCHAR(200),OSTYPE VARCHAR(200),CONFIGPARAMS VARCHAR(200),ISHUTDOWNUSERNAME VARCHAR(100),COMPUTERNAME VARCHAR(200)) ON COMMIT DELETE ROWS';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

END
$
-- 8.9.0 version Script patch is as follows
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_EXECTIME';
    IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_EXECTIME (IID NUMERIC (19)  NOT NULL,SUCCESSTIMES NUMERIC (19) DEFAULT 0,TOTALTIMES NUMERIC (19) DEFAULT 0,SCRIPTUUID VARCHAR (50) ,TASKCOUNT NUMERIC (19) DEFAULT 0, CONSTRAINT PK_IEAI_SCRIPT_EXECTIME PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
    END IF;

    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_EXECTIME' AND INDNAME='IDX_SCRIPT_EXECTIME_UUID';
    IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE INDEX IDX_SCRIPT_EXECTIME_UUID ON IEAI_SCRIPT_EXECTIME(SCRIPTUUID)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
    END	IF;


END
$
BEGIN
    DECLARE LS_SQL VARCHAR(2000);
    DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.SEQUENCES WHERE SEQNAME='SEQ_TEMP_IEAI_SCRIPT_EXECTIME';
    IF LI_EXISTS = 0 THEN
        SET	LS_SQL = 'CREATE SEQUENCE SEQ_TEMP_IEAI_SCRIPT_EXECTIME AS INT START WITH 1 INCREMENT BY 1';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

     SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_EXECTIME;
     IF LI_EXISTS = 0 THEN
         insert into IEAI_SCRIPT_EXECTIME
         select SEQ_TEMP_IEAI_SCRIPT_EXECTIME.nextval,
                SUCCESSTIMES,
                TOTALTIMES,
                ISCRIPTUUID,
                TASKCOUNT
                from  (SELECT
                SUM(CASE
                        WHEN D.ISTATE = 20 THEN
                            1
                        ELSE
                            0
                    END) AS SUCCESSTIMES,
                COUNT(D.IID) TOTALTIMES,
                T.ISCRIPTUUID,
                (select count(1)
                 from IEAI_SCRIPT_FLOW f
                 where t.ISCRIPTUUID = f.IMXSERVICEID) as TASKCOUNT
         FROM IEAI_SCRIPT_TEST T
                  LEFT JOIN IEAI_SCRIPT_INSTANCE D
                            ON T.ISCRIPTUUID = D.ISERVICEID
         WHERE T.ISDELETE = 0
         GROUP BY T.ISCRIPTUUID) cse;



         delete from IEAI_ID where ICLASSNAME='IEAI_SCRIPT_EXECTIME';
         SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_EXECTIME;
         IF LI_EXISTS >0 THEN
             INSERT INTO IEAI_ID  select 'IEAI_SCRIPT_EXECTIME',max(iid) from IEAI_SCRIPT_EXECTIME;
         END IF;

         commit;
     END IF;

     SET	LS_SQL = 'CREATE OR REPLACE VIEW V_SCRIPT_EXECTIME AS SELECT T.ILASTID, SUM(CASE WHEN D.SUCCESSTIMES IS NULL THEN  0  ELSE  D.SUCCESSTIMES  END) AS SUCCESSTIMES,  SUM(CASE  WHEN D.TOTALTIMES IS NULL THEN  0  ELSE  D.TOTALTIMES  END) AS TOTALTIMES,    (CASE  WHEN COUNT(D.IID) = 0 OR SUM(D.TOTALTIMES) = 0 THEN  0  ELSE  ROUND(SUM(CASE  WHEN D.SUCCESSTIMES IS NULL THEN   0  ELSE   D.SUCCESSTIMES  END) * 100 / SUM(D.TOTALTIMES))   END) AS SUCCESSRATE  FROM IEAI_SCRIPT_TEST T  LEFT JOIN IEAI_SCRIPT_EXECTIME D    ON T.ISCRIPTUUID = D.SCRIPTUUID   AND T.ISDELETE = 0 GROUP BY T.ILASTID';
     PREPARE	SQLA FROM LS_SQL;
     EXECUTE SQLA;

     SET	LS_SQL = 'CREATE OR REPLACE VIEW V_SCRIPT_TASK_EXECTIME AS SELECT T.IID, SE.SUCCESSTIMES,  SE.TOTALTIMES,  (CASE WHEN TOTALTIMES = 0 THEN 0  ELSE  ROUND(SUCCESSTIMES * 100 / TOTALTIMES)       END) AS SUCCESSRATE  FROM IEAI_SCRIPT_SERVICES T, IEAI_SCRIPT_EXECTIME SE WHERE SE.SCRIPTUUID = T.ISCRIPTUUID';
     PREPARE	SQLA FROM LS_SQL;
     EXECUTE SQLA;

     SET	LS_SQL = 'drop sequence SEQ_TEMP_IEAI_SCRIPT_EXECTIME';
     PREPARE	SQLA FROM LS_SQL;
     EXECUTE SQLA;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=80030;
        IF	LI_EXISTS = 0 THEN
                        SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES)   VALUES(80030,''创建者转移'',''changeTransCreateUser.do'',''脚本开发—我的脚本—创建者转移按钮'')';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END	IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID=80030;
        IF LI_EXISTS = 0 THEN
                     SET LS_SQL ='INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES)  VALUES(80030, 10002, 80030, '''')';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_ATTA_TEMPLATE';
    IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_ATTA_TEMPLATE(IID DECIMAL(19,0) NOT NULL,ISCRIPTIID VARCHAR(50),INAME VARCHAR(500),ISIZE DECIMAL(19,0),IUPLOADTIME DECIMAL(19,0),ICONTENTS BLOB(3670016),CONSTRAINT PK_IEAI_SCRIPT_ATTA_TEMPLATE PRIMARY KEY(IID))';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DEPENDSCRIPT';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DEPENDSCRIPT (IID NUMERIC (19)  NOT NULL,SRCSCRIPTUUID VARCHAR (255) ,DEPENDSCRIPTUUID VARCHAR (255) , CONSTRAINT PK_IEAI_SCRIPT_DEPENDSCRIPT PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

         SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPT_DEPENDSCRIPT' AND INDNAME='IDX_SCRIPTDEPEND_SRCUUID';
         IF	LI_EXISTS = 0 THEN
             SET	LS_SQL = 'CREATE INDEX IDX_SCRIPTDEPEND_SRCUUID ON IEAI_SCRIPT_DEPENDSCRIPT (SRCSCRIPTUUID)';
             PREPARE	SQLA FROM LS_SQL;
             EXECUTE SQLA;
         END IF;
END
$
-- 8.10.0 version Script patch is as follows
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

    SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCRIPTS_COAT' AND INDNAME='IDX_SCRIPTS_COAT_WORKID';
    IF	LI_EXISTS = 0 THEN
                 SET	LS_SQL = 'CREATE INDEX IDX_SCRIPTS_COAT_WORKID ON IEAI_SCRIPTS_COAT(IWORKITEMID)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
    END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_EXCEL_COMPARE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_EXCEL_COMPARE (IID NUMERIC (19) NOT NULL ,IFILE_NAME VARCHAR (100) ,ICONTENTS BLOB  ,ICONTENTS_OLD_FILE BLOB ,IUPLOADTIME NUMERIC (30) , ILOGIN_NAME VARCHAR (50) , CONSTRAINT PK_IEAI_SCRIPT_EXCEL_COMPARE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
-- 8.14.0 version Script patch is as follows
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_PARAMETER' AND  COLNAME='IPARAM_PARA_IID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_PARAMETER ADD IPARAM_PARA_IID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_EDIT_LABEL';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_EDIT_LABEL (IID NUMERIC (19) NOT NULL ,ISCRIPTLABEL VARCHAR (255) ,ISCRIPTUUID VARCHAR (50) , CONSTRAINT PK_IEAI_SCRIPT_EDIT_LABEL PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AUDITING_PARAMS' AND  COLNAME='IPARAM_PARA_IID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS ADD IPARAM_PARA_IID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DIRECTORY';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DIRECTORY (IID NUMERIC (19) NOT NULL,ISCRIPTDIRNAME VARCHAR (50) ,ISCRIPTDIRSORT NUMERIC (19) ,ISCRIPTDIRLEVEL NUMERIC (19) ,ISCRIPTDIRDESCRIPT VARCHAR (255) , CONSTRAINT PK_IEAI_SCRIPT_DIRECTORY PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DIR_AUTH';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DIR_AUTH (IID NUMERIC (19) NOT NULL,IISUSERGROUP NUMERIC (19) ,IAUTHITEMS VARCHAR (255) ,ISCRIPTDIRID NUMERIC (19) , CONSTRAINT PK_IEAI_SCRIPT_DIR_AUTH PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DIR_BIND';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DIR_BIND (IID NUMERIC (19) NOT NULL,ISCRIPTDIRID NUMERIC (19) ,ISCRIPTUUID VARCHAR (50) , CONSTRAINT PK_IEAI_SCRIPT_DIR_BIND PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_DIR_RELATION';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_DIR_RELATION (IID NUMERIC (19) NOT NULL,ISCRIPTDIRROOTID NUMERIC (19) ,ISCRIPTDIRCHILDID NUMERIC (19) , CONSTRAINT PK_IEAI_SCRIPT_DIR_RELATION PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SCRIPT_PARAMETER_CHECK';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SCRIPT_PARAMETER_CHECK (IID NUMERIC (19)NOT NULL  ,IRULENAME VARCHAR (255) ,ICHECKRULE VARCHAR (1000) ,IRULEDES VARCHAR (255) , CONSTRAINT PK_IEAI_SCRIPT_PARAMETER_CHECK PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_PARAMETER' AND  COLNAME='IPARACHECKIID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_PARAMETER ADD IPARACHECKIID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AUDITING_PARAMS' AND  COLNAME='IPARAM_PARA_IID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS ADD IPARAM_PARA_IID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_AUDITING_PARAMS' AND  COLNAME='IPARACHECKIID';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS ADD IPARACHECKIID NUMERIC(19) '; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
