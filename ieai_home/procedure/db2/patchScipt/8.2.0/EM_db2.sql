-- 4.7.17 version does not have patches for EM
-- 4.7.18 version does not have patches for EM

-- 4.7.19 version EM patch is as follows 
BEGIN
	DECLARE   LS_SQL VARCHAR(4000);
	DECLARE   LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMERPLAN_PERMISSIONS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_EMERPLAN_PERMISSIONS ( IPERMISSION SMALLINT, IPLANID DECIMAL(19,0), IROLEID DECIMAL(19,0), IID DECIMAL(19,0) NOT NULL, CONSTRAINT PK_IEAI_EMERPLAN_PERMISSIONS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SCRIPT_FLOW' AND  COLNAME='ISTART_TYPE';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_SCRIPT_FLOW ADD COLUMN ISTART_TYPE DECIMAL(3,0) DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_INSTANCE_VERSION' AND  COLNAME='ICREATEUSRID';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN ICREATEUSRID DECIMAL(19,0) DEFAULT 0';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SCRIPT_RUNINSTANCE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_SCRIPT_RUNINSTANCE  ( IID       	DECIMAL(19,0) NOT NULL,FLOWID    	DECIMAL(19,0),PLANID    	DECIMAL(19,0),SCENCEID  	DECIMAL(19,0),STEPID    	DECIMAL(19,0),SERVICEID 	DECIMAL(19,0),WORKITEMID	DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_RUNINSTANCE PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_SCENE_SS_RELATION';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_EMER_SCENE_SS_RELATION  ( IID         	DECIMAL(19,0) NOT NULL,SCENEIID    	DECIMAL(19,0),SSIID       	DECIMAL(19,0),STEPNAME    	VARCHAR(100),EXECORDER   	VARCHAR(100),CREATETIME  	DATE,CREATEUSER  	VARCHAR(1000),CREATEUSERID	DECIMAL(19,0),UPDATEUSERID	DECIMAL(19,0),UPDATETIME  	DATE,UPDATEUSER  	VARCHAR(100),CONSTRAINT PK_IEAI_EMER_SCENE_SS_R  PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_SCENE_PLAN_RELATION';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_EMER_SCENE_PLAN_RELATION  ( IID         	DECIMAL(19,0) NOT NULL,PLANIID     	DECIMAL(19,0),SCENEIID    	DECIMAL(19,0),CREATETIME  	DATE,CREATEUSER  	VARCHAR(1000),CREATEUSERID	DECIMAL(19,0),UPDATEUSERID	DECIMAL(19,0),UPDATETIME  	DATE,UPDATEUSER  	VARCHAR(100),CONSTRAINT PK_IEAI_EMER_SCENE_PLAN_R PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
END
$

BEGIN
	DECLARE   LS_SQL VARCHAR(4000);
	DECLARE   LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_SCENE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_EMER_SCENE  (IID  DECIMAL(19,0) NOT NULL,SCENENO  VARCHAR(1000),SCENENAME  VARCHAR(1000),SCENELEVEL  VARCHAR(1000),SCENEACCREDITLEVEL  VARCHAR(1000),IMPACTANALYSIS   VARCHAR(1000),OPERDESCRIPTION   VARCHAR(1000),CREATETIME  DATE,CREATEUSER  VARCHAR(100),CREATEUSERID  DECIMAL(19,0),UPDATEUSERID  DECIMAL(19,0),UPDATETIME  DATE,UPDATEUSER  VARCHAR(1000),CONSTRAINT PK_IEAI_EMER_SCENE PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EMER_PLAN';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_EMER_PLAN  ( IID  DECIMAL(19,0) NOT NULL,PLANNAME  VARCHAR(1000),PLANDESCRIPTION	VARCHAR(1000),CREATETIME  DATE,CREATEUSER  VARCHAR(1000),CREATEUSERID  DECIMAL(19,0),UPDATEUSERID  DECIMAL(19,0),UPDATETIME  DATE,UPDATEUSER  VARCHAR(100),CONSTRAINT PK_IEAI_EMER_PLAN PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
END
$

-- 4.7.20 version EM patch is as follows

-- 4.7.21 version EM patch is as follows	
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SYSTEMID' AND TABNAME='IEAI_EMER_PLAN';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_EMER_PLAN ADD COLUMN SYSTEMID DECIMAL(19,0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SYSID' AND TABNAME='IEAI_SCRIPT_RUNINSTANCE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_SCRIPT_RUNINSTANCE ADD COLUMN SYSID DECIMAL(19,0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$

-- 4.7.22 version does not have patches for EM
-- 4.7.23 version does not have patches for EM

-- 4.7.24 version EM patch is as follows	
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PROTYPE' AND TABNAME='IEAI_DOUBLE_AUTH_CODE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_DOUBLE_AUTH_CODE ADD COLUMN PROTYPE DECIMAL(19,0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SYSTEMID' AND TABNAME='IEAI_DOUBLE_AUTH_CODE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_DOUBLE_AUTH_CODE ADD COLUMN SYSTEMID DECIMAL(19,0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

END
$
-- 4.7.25 version does not have patches for EM

-- 4.7.26 version EM patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EM_STAND_FLOW_RELATE';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_EM_STAND_FLOW_RELATE(IID DECIMAL(19) NOT NULL ,FLOWID DECIMAL(19) ,PLANID DECIMAL(19) ,STEPID DECIMAL(19) ,SCENCEID DECIMAL(19) ,SSREALTEID DECIMAL(19) ,NORMALTASKID DECIMAL(19) ,SYSID DECIMAL(19) ,PLANNAME VARCHAR(400) ,SCENCENAME VARCHAR(400) ,STEPNAME VARCHAR(400) ,SYSNAME VARCHAR(400) ,NORMALTASKNAME VARCHAR(400) ,IWORKITEMID DECIMAL(19) ,TASKN VARCHAR(400) , CONSTRAINT PK_STAND_FLOW_RELATE PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_EM_STAND_FLOW_RELATE';
	IF LI_EXISTS = 0 THEN
    SET LS_SQL ='CREATE TABLE IEAI_EM_STAND_FLOW_RELATE(IID DECIMAL(19) NOT NULL ,FLOWID DECIMAL(19) ,PLANID DECIMAL(19) ,STEPID DECIMAL(19) ,SCENCEID DECIMAL(19) ,SSREALTEID DECIMAL(19) ,NORMALTASKID DECIMAL(19) ,SYSID DECIMAL(19) ,PLANNAME VARCHAR(400) ,SCENCENAME VARCHAR(400) ,STEPNAME VARCHAR(400) ,SYSNAME VARCHAR(400) ,NORMALTASKNAME VARCHAR(400) ,IWORKITEMID DECIMAL(19) ,TASKN VARCHAR(400) , CONSTRAINT PK_STAND_FLOW_RELATE PRIMARY KEY(IID))';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID in (select IMENUBUTTONID from IEAI_MENU_BUTTON where imenuid=(select iid from IEAI_MENU where  iurl='scenelist.do'));
	IF	LI_EXISTS >0 THEN
	SET	LS_SQL = 'DELETE FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID in (select IMENUBUTTONID from IEAI_MENU_BUTTON where imenuid=(select iid from IEAI_MENU where  iurl=''scenelist.do''))';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID in (select IMENUBUTTONID from IEAI_MENU_BUTTON where imenuid=(select iid from IEAI_MENU where  iurl='emStart.do'));
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'DELETE FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID in (select IMENUBUTTONID from IEAI_MENU_BUTTON where imenuid=(select iid from IEAI_MENU where  iurl=''emStart.do''))';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;

	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE  imenuid IN (select iid from IEAI_MENU where  iurl='scenelist.do');
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'delete from IEAI_MENU_BUTTON where imenuid IN (select iid from IEAI_MENU where  iurl=''scenelist.do'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE imenuid IN (select iid from IEAI_MENU where  iurl='emStart.do');
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'delete from IEAI_MENU_BUTTON where imenuid IN (select iid from IEAI_MENU where  iurl=''emStart.do'')';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=6 AND iurl = 'scenelist.do';
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'delete from ieai_menu where iurl = ''scenelist.do''';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=6 AND iurl = 'emStart.do';
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'delete from ieai_menu where iurl = ''emStart.do''';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;

	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=6 AND iurl = 'emStartNew.do';
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'delete from ieai_menu where iurl = ''emStartNew.do''';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IGROUPMESSID=6 AND iurl = 'scriptServicesPlanTaskExec.do';
	IF	LI_EXISTS > 0 THEN
	SET	LS_SQL = 'delete from ieai_menu where iurl = ''scriptServicesPlanTaskExec.do''';
		 PREPARE	SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$