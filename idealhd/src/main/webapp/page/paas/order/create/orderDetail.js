


Array.prototype.baoremove = function(dx)
{
    if(isNaN(dx)||dx>this.length){return false;}
    this.splice(dx,1);
}

Ext.onReady (function ()
{
	var rightPanel = null;
	var serverArr = [];//选中服务器

	var paramArr=[];
	var record7= {};
	record7.iid = 18;
	record7.iparamName = 'shellPath';
	record7.iparamValue='';
	paramArr.push(record7);

	var record2= {};
	record2.iid = 18;
	record2.iparamName = 'sourcePath';
	record2.iparamValue='';
	paramArr.push(record2);
	
	
	
	//----------------服务器----------------
	var noSelectServer=[];
	var record = {};
	record.cpId = 12;
	record.ip = '*************';
	record.ctype='APP';
	record.os='Windows';
	record.port=15000;
	noSelectServer.push(record);
	  
	var record9 = {};
	record9.cpId = 13;
	record9.ip = '*************';
	record9.ctype='WBE';
	record9.port=15000;
	record9.os='Linux';
	noSelectServer.push(record9);
	
	var record3 = {};
	record3.cpId = 14;
	record3.ip = '*************';
	record3.ctype='DB';
	record3.os='Linux';
	record3.port=15000;
	noSelectServer.push(record3);
	
	var record4= {};
	record4.cpId = 15;
	record4.ip = '*******';
	record4.ctype='APP';
	record4.port=15000;
	record4.os='Linux';
	noSelectServer.push(record4);
	
	var record5= {};
	record5.cpId = 16;
	record5.ip = '***********';
	record5.ctype='FTP';
	record5.port=15000;
	record5.os='Linux';
	noSelectServer.push(record5);
	
	var record6= {};
	record6.cpId = 17;
	record6.ip = '***********';
	record6.port=15000;
	record6.ctype='WEB';
	record6.os='Linux';
	noSelectServer.push(record6);
	
	var record11= {};
	record11.cpId = 18;
	record11.ip = '**********';
	record11.ctype='APP';
	record11.port=15000;
	record11.os='Linux';
	noSelectServer.push(record11);
	
	//-------personalArr
	
	var personalArr=[];
	var personal1={};
	personal1.stepid=12;
	personalArr.push(personal1);
	
	var personal2={};
	personal2.stepid=14;
	personalArr.push(personal2);
	
	var personal3={};
	personal3.stepid=162;
	personalArr.push(personal3);
	
	var personal4={};
	personal4.stepid=187;
	personalArr.push(personal4);
	
	var personal5={};
	personal5.stepid=190;
	personalArr.push(personal5);
	// 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.tip.QuickTipManager.init ();

	/************个性化配置************************/
	Ext.define ('personalModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
            {
                name : 'stepid',
                type : 'long'
            }
	    ]
	});
	var personalColumns = [
        {
            text : '序号',
            width : 40,
            xtype : 'rownumberer'
        },
        {
            text : '设备步骤号',
            dataIndex : 'stepid',
            flex:1
        },
        {
            text : '设备',
            dataIndex : 'systemId',
            hideable: false,
            width : 100,
            align : 'center',
            flex:1,
            renderer : function (value, metaData, record)
            {
            	return "<a href='javascript:void(0)'><img src='images/monitor_bg.png' class='equipment'></a>";
            },
            listeners :
            {
	            click : function (a, b, c, d, e, record)
	            {
	            	openSelectWindow ();
	            	
	            }
            }
        }
	];
	
	var personalStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'personalModel',
	    data : personalArr
	});
	var personal_conf_grid = Ext.create ('Ext.grid.Panel',
	{
		id:'personal_grid_id',
		title : '个性服务器',
		border : false,
		store :personalStore,
		hidden : onlyShowMbxx,
		autoScroll : true,
		columnLines : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		columns : personalColumns
	});
	
	
	
/*************************************** 组件model ***************************************************/
	//服务器加载方式下拉框
	var typeComboStore = new Ext.data.ArrayStore({
        fields: ['id', 'name'],
        data: [[0,'设备'],[1, '设备组']]
    });
	var typeComBox = Ext.create ('Ext.form.field.ComboBox',
	{});
	
	/** 参数信息列表model* */
	Ext.define ('paramInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'iparamName',
	                type : 'string'
	            },
	            {
	                name : 'iparamValue',
	                type : 'string'
	            },
	            {
	                name : 'iparamDesc',
	                type : 'string'
	            }
	    ]
	});
	
	/** 规则信息列表model* */
	Ext.define ('ruleInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'ihcName',
	                type : 'string'
	            },
	            {
	                name : 'ihcItem',
	                type : 'string'
	            },
	            {
	                name : 'ihcSeparator',
	                type : 'string'
	            },
	            {
	                name : 'ihcKeywords',
	                type : 'string'
	            },
	            {
	                name : 'ihcAlermCode',
	                type : 'string'
	            }
	    ]
	});
	
	/** 服务器信息列表model* */
	Ext.define ('serverInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
				{
					name : 'id',
					type : 'long'
				}, 
				{
					name : 'agentip',
					type : 'string'
				}, 
				{
					name : 'ip',
					type : 'string'
				}, 
				{
					name : 'gname',
					type : 'string'
				}, 
				{
					name : 'port',
					type : 'string'
				}, 
//				{
//					name : 'os',
//					type : 'string'
//				}, 

//				{
//					name : 'agentpath',
//					type : 'string'
//				}, 
				{
					name : 'ctype',
					type : 'string'
				}, 
				{
					name : 'cname',
					type : 'string'
				}

	    ]
	});
	
	
	
	
/*********************************  组件store ***********************************************************/
	/** 参数信息列表store* */
	var paramInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'paramInfoModel',
	   /** proxy :
	    {
	        type : 'ajax',
	        url : 'getParamInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }*/
	    data:paramArr
	
	});
	
	paramInfoStore.on('load', function(store, options) {
	});
	
	
	/** 服务器信息列表store* */
	var serverInfoStore = Ext.create ('Ext.data.Store',
	{
	    model : 'serverInfoModel',
	    data : serverArr
	});
	serverInfoStore.on('beforeload', function(store, options) {
		Ext.apply(store.proxy.extraParams, new_params);
	});
	
/*************************************** 组件columns ****************************************************/
	Ext.define ('comboModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
				{
					name : 'name',
					type : 'string'
				}, 
				{
					name : 'value',
					type : 'string'
				}
		]
	});
	var comboStore = Ext.create ('Ext.data.Store',
	{
	    model : 'comboModel',
	    data : [{name:'是',value:'true'},{name:'否',value:'false'}]
	});
	/** 参数信息列表column* */
	var paramInfoColumns = [
        {
            text : '序号',
            width : 40,
//            locked : true,
            xtype : 'rownumberer'
        },
        {
            text : 'ID',
            dataIndex : 'iid',
//            flex : 1,
            // locked : true,
            hidden : true
        },
        {
            text : '参数标识',
            dataIndex : 'iparamName',
            sortable:false,
            width:200
        },
        {
        	id:'paramType_Clumn',
            text : '参数值',
            dataIndex : 'iparamValue',
            sortable:false,
            flex:1,
            editor :
            {
//	            allowBlank : false
            }
        }
	];
	
	/** 规则信息列表column* */
	var ruleInfoColumns = [
        {
            text : '序号',
            width : 40,
//            locked : true,
            xtype : 'rownumberer'
        },
        {
            text : 'ID',
            dataIndex : 'iid',
//            flex : 1,
            // locked : true,
            hidden : true
        },
        {
            text : '检查规则名称',
            dataIndex : 'ihcName',
            width : 200
        },
        {
            text : '检查项目(多个项目以后面自定义的分隔符分隔)',
            dataIndex : 'ihcItem',
            width : 300
        },
        {
            text : '分隔符',
            dataIndex : 'ihcSeparator',
            width : 100
        },
        {
            text : '检查关键字',
            dataIndex : 'ihcKeywords',
            width : 150
        },
        {
            text : '报警代码(报警代码之间以“/”号分隔，编号与描述之间用“#”号分隔)',
            dataIndex : 'ihcAlermCode',
            width : 200
            ,flex : 1
        }
	];

	/** 服务器信息列表column* */
	var serverInfoColumns = [
		{
            text : '序号',
            width : 40,
//            locked : true,
            resizable:true,
            xtype : 'rownumberer'
        },
		{
        	text : '设备ID',
			dataIndex : 'id',
			hidden : true
		}, 
		{
			text : '设备名IP',
//			locked : true,
			dataIndex : 'ip',
			width : 200
			,flex : 1
		}, 
//		{
//			text : '所属中心',
//			dataIndex : 'centername',
//			width : 150
//		}, 
//		{
//			text : '操作系统',
//			dataIndex : 'os',
//			width : 150
//		}, 
//		{
//			text : 'AgentIP',
//			dataIndex : 'agentip',
//			width : 200
//			,flex : 1
//		}, 
		{
			text : '设备端口',
			dataIndex : 'port',
			width : 80
		}, 
//		{
//			text : 'Agent用户',
//			dataIndex : 'user',
//			width : 120
//		}, 
//		{
//			text : 'Agent密码',
//			dataIndex : 'passwd',
//			width : 120
//		}, 
//		{
//			text : 'Agent路径',
//			dataIndex : 'agentpath',
//			width : 120
//		}, 
		{
			text : '设备类型',
			dataIndex : 'ctype',
			width : 90
		}/*, 
		{
			text : '中间件',
			dataIndex : 'cname',
			width : 90
		}*/
	];
	
/****************************************** 按钮和下拉框 ****************************************************/
	/** 提交按钮* */
	var submitButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    align : 'center',
	    text : "保存",
	    hidden : saveButtonShow,
	    handler : submitFunction
	});
	
	var backButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    align : 'center',
	    text : "返回",
	    handler : fanhui
	});
	
	function fanhui(){
		var url='';
		if(menuFrom=='manager'){
			url='orderManageIndex.do';
		}else if(menuFrom=='monitor'){
			url='orderMonitorIndex.do';
		}else{
			url='orderIndex.do';
		}
    	
    	destroyRubbish();
		contentPanel.getLoader().load({
			url: url,
			params: {},
			scripts: true
		});
    }
	
	/** 移除设备按钮* */
	var removeButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "移除设备",
	    handler : removeDeviceRelation
	});
	/** 选择设备按钮* */
	var resButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "选择设备",
	    handler : openSelectWindow
	});


/****************************** 组件gridpanel *******************************************************************/
	// 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
	// 定义可编辑grid组件
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	
	/**参数信息列表gridpanel* */
	var param_info_grid = Ext.create ('Ext.grid.Panel',
	{
		title : '参数信息',
		id :'param_info_grid_id',
		region: 'center', 
		autoScroll : true,
		hidden : onlyShowMbxx,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
	    store : paramInfoStore,
	    border : false,
	    columnLines : true,
	    columns : paramInfoColumns,
	    plugins : [ cellEditing ]
	});
	
	/**服务器信息列表gridpanel* */
	var server_info_grid = Ext.create ('Ext.grid.Panel',
	{
		id:'server_info_grid_id',
		title : '统一服务器',
		border : false,
//		hidden : true,
//		height : contentPanel.getHeight() - 225,
		selModel : selModel,
		store : serverInfoStore,
		hidden : onlyShowMbxx,
		autoScroll : true,
		columnLines : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		columns : serverInfoColumns,
		dockedItems : [ 
   	    {
   	        xtype : 'toolbar',
   	        items : [ /*groupComBox,*/ '->', removeButton, resButton ]
   	    }]
	});
	
	// 设备组
	Ext.define ('groupGridModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'igroupName',
	                type : 'string'
	            },
	            {
	                name : 'igroupDes',
	                type : 'string'
	            }
	    ]
	});
	/** 服务器组数据源* */
	var groupGridStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    pageSize: 200,
	    model : 'groupGridModel',
	    proxy :
	    {
	        type : 'ajax',
//	        url : 'getComputerGroupList.do',
	        url : 'getComputerGroupListForIC.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	groupGridStore.on ('beforeload', function (store, options)
	{
		var qryCon = groupNameForQuery.getValue();
		var new_params =
		{
			qryCon : qryCon,
		};
		Ext.apply (groupGridStore.proxy.extraParams, new_params);
	});
	// 定义列模板
	var groupColumns = [
        {
            text : '序号',
            width : 40,
            xtype : 'rownumberer'
        },
        {
            text : 'ID',
            dataIndex : 'iid',
            flex : 1,
            // locked : true,
            hidden : true
        },
        {
            text : '组名称',
            dataIndex : 'igroupName',
            width : 500
        },
        {
            text : '组描述',
            dataIndex : 'igroupDes',
            flex:1
        }
	];
	/** 组名查询输入框* */
	var groupNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    margin : '5',
	    emptyText : '--请输入设备组名称--',
	    labelWidth : 80,
	    width : '30%',
	    xtype : 'textfield'
	});
//	/** 分页工具栏* */
//	var bsPageBar = Ext.create ('Ext.PagingToolbar',
//	{
//	    store : groupGridStore,
//	    dock : 'bottom',
//	    displayInfo : true,
//	    emptyMsg : "没有记录"
//	});
//	/** 查询按钮* */
//	var qryButton = Ext.create ("Ext.Button",
//	{
//	    cls : 'Common_Btn',
//	    text : "查询",
//	    handler : function()
//	    {
//	    	bsPageBar.moveFirst();
//	    }
//	});
	/** 设备组信息列表gridpanel * */
	var groupGrid = Ext.create ('Ext.grid.Panel',
	{
		id:'groupGrid_id',
		title : '服务器组信息',
		region : 'center',
		border : false,
//		hidden : true,
		selModel : Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true
		}),
		store : groupGridStore,
		autoScroll : true,
		columnLines : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		columns : groupColumns,
//		plugins : [ cellEditing ],
		animCollapse : false,
//		bbar:bsPageBar,
//	    dockedItems : [ 
//	    {
//	        xtype : 'toolbar',
//	        items : [ groupNameForQuery,qryButton]
//	    }]
	});	

	
	/** *********************组件********************* */
	/** 系统信息列表column* */
	var instacneInfoColumns = [ {
		text : '是否禁用',
		xtype : 'checkcolumn',
		dataIndex : 'iisDisable',
		hidden : true,
		width : 70
	}, {
		text : 'ID',
		dataIndex : 'iid',
		flex : 1,
		hidden : true
	}, {
		text : '步骤标识',
		dataIndex : 'iserner',
		editor : {
			readOnly : true,
			allowBlank : true
		},
		width : 75
	}, {
		text : '阶段名称',
		dataIndex : 'iconnerName',
		editor : {
			readOnly : true,
			allowBlank : true
		},
		width : 180
	}, {
		text : '顺序步骤',
		dataIndex : 'iconner',
		editor : {
			readOnly : true,
			allowBlank : true
		},
		width : 65
	}, {
		text : '子步骤名称',
		dataIndex : 'iactName',
		editor : {
			readOnly : true,
			allowBlank : true
		},
		flex:1
	}, {
		text : '目标组件',
		dataIndex : 'ipName',
		hidden : true,
		renderer: function(value,metaData,record){
  	      return '<a href="javascript:void(0);" onclick="openWindwResgroup('+record.get('iid')+','+record.get('iinstanceId')+')">详细</a>';
  	    },
		width : 85
	},{
		text : '是否禁用',
		xtype : 'checkcolumn',
		hidden : true,
		dataIndex : 'jlDisable',
		width : 70
	},{
		text : '是否高风险',
		hidden : true,
		xtype : 'checkcolumn',
		dataIndex : 'iiHightRisk',
		width : 70
	}, {
		text : '配对',
		dataIndex : 'ipair',
		hidden : true,
		width : 70,
		editor : {
			readOnly : true,
			allowBlank : true
		}
	}, {
		text : '模块类型',
		dataIndex : 'imodeltypestep',
		editor : {
			readOnly : true,
			allowBlank : true
		},
		width : 75
	}, {
		text : '应用标识',
		dataIndex : 'isystemtypestep',
		editor : {
			readOnly : true,
			allowBlank : true
		},
		width : 75
	}, {
		text : '提醒内容',
		dataIndex : 'iremInfo',
		width : 150,
		hidden : true,
		editor : {
			readOnly : true,
			allowBlank : true
		}
	}, {
		text : '依赖',
		dataIndex : 'iprener',
		width : 100,
		hidden : true,
		editor : {
			readOnly : true,
			allowBlank : true
		}
	}, {
		text : '系统类型',
		dataIndex : 'isysType',
		width : 100
	}, {
		text : '任务类型',
		dataIndex : 'iactType',
		width : 80
	}, {
		text : '执行用户',
		dataIndex : 'iexecUser',
		width : 80
	}, {
		text : '壳脚本',
		dataIndex : 'ishellScript',
		width : 80
	}, {
		text : '壳脚本参数',
		dataIndex : 'ishellScript',
		width : 100,
		hidden : true,
	    renderer: function(value,metaData,record){
			return '<a href="javascript:void(0);"  style="text-decoration:underline;" onclick="showParam()">选择参数</a>';
	    }
	}, {
		text : '环境变量',
		dataIndex : 'iisloadenv',
		width : 80
	}, {
		text : '执行脚本',
		dataIndex : 'ishellPath',
		hidden : true,
		width : 200
	},
	{
		text : '执行脚本参数',
		dataIndex : 'ishellPath',
		hidden : true,
		width : 200,
	    renderer: function(value,metaData,record){
			return '<a href="javascript:void(0);"  style="text-decoration:underline;" onclick="showParam()">选择参数</a>';
	    }
	}, {
		text : '成功返回值',
		dataIndex : 'iexpeceInfo',
		width : 60
	}, {
		text : '异常返回值',
		dataIndex : 'iexceptinfo',
		width : 200,
		hidden : true
	}, {
		text : '是否可重做',
		dataIndex : 'iredoable',
		width : 80
	// flex : 1
	} , {
		text : '依赖系统',
		dataIndex : 'ipresysname',
		width : 200,
		hidden : true,
		editor : {
			allowBlank : true
		}}, {
			text : '依赖步骤名称',
			dataIndex : 'ipreactname',
			width : 200,
			hidden : true,
			editor : {
				allowBlank : true
			}}, {
				text : '单步回退',
				dataIndex : 'singleRollback',
				width : 200,
				hidden : true,
				editor : {
					allowBlank : true
				}},{
					text : '并发数',
					dataIndex : 'iconcurrency',
					width : 150,
					hidden : true,
					editor : {
						allowBlank : true,
						regex : /^\d+$/,
						regexText : '请输入整数'
					}
				},{
					text : '间隔时间',
					dataIndex : 'iintervaltime',
					width : 150,
					hidden : true,
					editor : {
						allowBlank : true,
						regex: /^\d+$/,
						regexText : '请输入整数'
					}
				},{
					text : '配置文件变更',
					dataIndex : 'icfgfilechange',
					name : 'icfgfilechange',
					width : 150,
					editor : {
						allowBlank : true
					},
					renderer: function(value,metaData,record){
                        if(value=="1")
                        {
                        	return "是"
                        }else if(value=="0")
                        {
                        	return "否"
                        }else
                        {
                        	return value;
                        }  	
					},
					hidden : true
				},{
					text : '是否支持回退',
					dataIndex : 'isRollBack',
					width : 100
				}
			];

	/** 系统信息列表model* */
	Ext.define('instanceInfoModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'long'
		}, {
			name : 'iinstanceId',
			type : 'long'
		}, {
			name : 'iinstanceName',
			type : 'string'
		}, {
			name : 'iserner',
			type : 'long'
		}, {
			name : 'iconner',
			type : 'long'
		}, {
			name : 'iconnerName',
			type : 'string'
		}, {
			name : 'iprener',
			type : 'string'
		}, {
			name : 'iactName',
			type : 'string'
		}, {
			name : 'iactDes',
			type : 'string'
		}, {
			name : 'iactType',
			type : 'string'
		}, {
			name : 'iremInfo',
			type : 'string'
		}, {
			name : 'ipName',
			type : 'string'
		}, {
			name : 'imodelType',
			type : 'string'
		}, {
			name : 'iip',
			type : 'string'
		}, {
			name : 'iprot',
			type : 'int'
		}, {
			name : 'isysType',
			type : 'string'
		}, {
			name : 'iexecUser',
			type : 'string'
		}, {
			name : 'ishellScript',
			type : 'string'
		}, {
			name : 'iisloadenv',
			type : 'string'
		}, {
			name : 'ishellPath',
			type : 'string'
		}, {
			name : 'itimeOut',
			type : 'long'
		}, {
			name : 'iparameter',
			type : 'string'
		}, {
			name : 'iexpeceInfo',
			type : 'string'
		}, {
			name : 'iexceptinfo',
			type : 'string'
		}, {
			name : 'iredoable',
			type : 'string'
		}, {
			name : 'iisDisable',
			type : 'boolean'
		}, {
			name : 'appIdentity',
			type : 'string'
		}, {
			name : 'ipresysname',
			type : 'string'
		}, {
			name : 'ipreactname',
			type : 'string'
		}, {
			name : 'isystemtypestep',
			type : 'string'
		}, {
			name : 'imodeltypestep',
			type : 'string'
		}, {
            name : 'singleRollback',
            type : 'string'
        },
        {
            name : 'iiHightRisk',
            type : 'boolean'
        },
        {
            name : 'ipair',
            type : 'string'
        }, {
			name : 'jlDisable',
			type : 'boolean'
		},
        {
			name : 'iconcurrency',
			type : 'string'
        },{
        	name : 'iintervaltime',
        	type : 'string'
        },{
        	name : 'icfgfilechange',
        	type : 'string'
        },{
        	name : 'isRollBack',
        	type : 'string'
        }
		]
	});
	
	var templateInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'instanceInfoModel',
	    pageSize : 30,
		proxy : {
			type : 'ajax',
			url : 'getTemplateInfoList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	
	});	
	
	templateInfoStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			templateVal :type
		};
		Ext.apply (templateInfoStore.proxy.extraParams, new_params);
	});
	
	
	var template_info_grid = Ext.create ('Ext.grid.Panel',
	{
		title : '模板信息',
		id :'template_info_grid_id',
		region: 'center', 
		autoScroll : true,
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
	    store : templateInfoStore,
	    border : false,
	    columnLines : true,
	    columns : instacneInfoColumns
	});
	
	
	
	
	
/**************** 组件tabPanel **********************************************************************/
	/** tabPanel* */
	var activeTab=0;
	if(onlyShowMbxx){
		activeTab=2;
	}
	var tabPanelForICFS = Ext.create ('Ext.tab.Panel',
	{
		id:'tabPanelForICFS_id',
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : activeTab,
	    border : false,
	    // plain : true,
	    items : [param_info_grid,/*rule_info_grid,*/server_info_grid,personal_conf_grid/*,groupGrid*/,template_info_grid]
	    /*,dockedItems : [
		    {
		        dock : 'bottom',
		        xtype : 'toolbar',
		        items : [
		        	'->',
		               submitButton,
		               '->'
		        ]
		    }
	    ]*/
	});
	
	
/**** form **************************************************************************/
	var templateType = Ext.create ('Ext.form.TextField',
	{
	    width : '50%',
	    xtype : 'textfield',
	    columnWidth: 1,
	    labelWidth : 70,
	    padding : '5',
	    fieldLabel: '模板类型',
	    name: 'templateType',
	    value:'应急操作',
	   	readOnly : true
	    
	});
	var templateName = Ext.create ('Ext.form.TextField',
	{
	    width : '50%',
	    xtype : 'textfield',
	    columnWidth: 1,
	    labelWidth : 70,
	    padding : '5',
	    fieldLabel: '模板名称',
	    name: 'templateName',
	    value:'应急操作_中信信用卡系统模板',
	    readOnly : true
	    
	});
			
	var orderName = Ext.create ('Ext.form.TextField',
	{
	    width : '50%',
	    xtype : 'textfield',
	    columnWidth: 1,
	    labelWidth : 70,
	    padding : '5',
	    fieldLabel: '工单名称',
	    name: 'orderName',
	    value:'应急操作_中信信用卡系统订单'
	    
	});
	
	/** form **/
	var form = new Ext.form.FormPanel (
	{
		region : 'north',
	  	layout : 'anchor',
	  	buttonAlign : 'center',
	  	border : false,
	    width : contentPanel.getWidth () - 2,
	    frame : true,
	    defaultType : 'textfield',
	    defaults :
	    {
		    anchor : '99%'
	    },
	    dockedItems : [
		    {
		        dock : 'bottom',
		        xtype : 'toolbar',
		        items : [
		        	'->',
		        	backButton,
		            submitButton
		        ]
		    }
	    ],
	    items : [
	    	orderName,templateType,templateName
	    ]
	});
	
/************************* 主panel *******************************************************/
	var rightPanel = Ext.create('Ext.panel.Panel',{
		renderTo : "grid_area",
		layout : 'border',
		border : false,
		width : contentPanel.getWidth(),
		height : contentPanel.getHeight()-modelHeigth,
		items : [form,tabPanelForICFS]
    });
	
/********************** 选择设备窗口相关组件 *******************************************************************/
	/** *********************Model********************* */
	Ext.define ('computerModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'name',
	                type : 'string'
	            },
	            {
	                name : 'gname',
	                type : 'string'
	            },
	            {
	                name : 'cpId',
	                mapping: 'id',
	                type : 'long'
	            },
	            {
	                name : 'ip',
	                type : 'string'
	            },
	            {
	                name : 'agentip',
	                type : 'string'
	            },
//	            {
//					name : 'centername',
//					type : 'string'
//				}, 
				{
					name : 'port',
					type : 'string'
				}, 
//				{
//					name : 'os',
//					type : 'string'
//				}, 
//				{
//					name : 'user',
//					type : 'string'
//				}, 
//				{
//					name : 'passwd',
//					type : 'string'
//				}, 
//				{
//					name : 'agentpath',
//					type : 'string'
//				}, 
				{
					name : 'ctype',
					type : 'string'
				}, 
				{
					name : 'cname',
					type : 'string'
				}
//				, 
//				{
//					name : 'cputhreshold',
//					type : 'string'
//				}, 
//				{
//					name : 'memorythreshold',
//					type : 'string'
//				}, 
//				{
//					name : 'iowaitthreshold',
//					type : 'string'
//				}, 
//				{
//					name : 'des',
//					type : 'string'
//				}
	            
	    ]
	});
	/** *********************Store********************* */
	/** 设备列表数据源* */
	
	
	var cmoputerStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    model : 'computerModel',
	   /* proxy :
	    {
	        type : 'ajax',
	        actionMethods : {  
	            read : 'POST' 
	        },  
	        url : 'getComputerListForIC.do',
	        timeout: 600000 ,
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }*/
	    data : noSelectServer
	});
	/*cmoputerStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			systemId : isystemId
		};
		Ext.apply (cmoputerStore.proxy.extraParams, new_params);
	});*/
	/** *********************组件********************* */
	/** 起始ip* */
	var ipBetween = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 50,
	    // fieldLabel : '设备查询',
	    emptyText : '--请输入开始IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 19,
	    fieldLabel : '至',
	    emptyText : '--请输入截止IP--',
	    labelSeparator : '',
	    width : 240,
	    margin : '5'
	// padding : '0 10 0 0'
	});
	
	
	/** 应用类型条件 */
	var ctStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'ctid','ctype' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'ctList.do',
			reader : {
				type : 'json',
				root : 'ctlist'
			}
		}
	});
	var ctCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : ctStore,
		queryMode : 'local',
		emptyText : '---请选择应用类型---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'ctype',
		valueField : 'ctid',
		triggerAction : "all"
	});
	
//	/** 应用类型 */
//	var ctStore = Ext.create('Ext.data.JsonStore', {
//		fields : [ 'ctype' ],
//		autoLoad : true,
//		autoDestroy : true,
//		proxy : {
//			type : 'ajax',
//			url : 'ctList.do',
//			reader : {
//				type : 'json',
//				root : 'ctlist'
//			}
//		}
//	});
//	var ctCombo = Ext.create('Ext.form.field.ComboBox', {
//		margin : '5',
//		store : ctStore,
//		queryMode : 'local',
//		width : 600,
//		forceSelection : true, // 要求输入值必须在列表中存在
//		typeAhead : true, // 允许自动选择
//		displayField : 'ctype',
//		valueField : 'ctype',
//		triggerAction : "all"
//	});
	
	/** 中间件 */
	var cmStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'cmid','cname' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'cmList.do',
			reader : {
				type : 'json',
				root : 'cmlist'
			}
		}
	});
	var cmCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : cmStore,
		queryMode : 'local',
		emptyText : '---请选择中间件---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'cname',
		valueField : 'cmid',
		triggerAction : "all"
	});
	
	/** 设备组 */
	var groupStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'groupId','groupName' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'groupList.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var groupCombo = Ext.create('Ext.form.field.ComboBox', {
		margin : '5',
		store : groupStore,
		queryMode : 'local',
//		hidden:true,
		emptyText : '---请选择设备组---',
		width : 200,
		forceSelection : true, // 要求输入值必须在列表中存在
		typeAhead : true, // 允许自动选择
		displayField : 'groupName',
		valueField : 'groupId',
		triggerAction : "all"
	});
	
	/** 查询按钮* */
	var queryButtonForBSMNoSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 70,
//	    height : 22,
	    text : '查询',
	    handler : queryWhere
	});
	/** 重置按钮* */
	var resetBtn = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '重置',
	    handler : resetWhere
	});
	/** 增加按钮* */
	var saveButtonForBSMNoSelected = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    disabled : false,
	    text : '增加',
	    handler : onSaveListener
	});
	/** *********************Panel********************* */
	/** 设备列表panel* */
	var grid_panel = Ext.create ('Ext.grid.Panel',
	{
		id : 'grid_panel',
	    store : cmoputerStore,
	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    {
		    checkOnly : true
	    }),
	    region : 'center',
	    border : true,
	    columnLines : true,
	    columns : [
	        {
		        text : '设备ID',
		        sortable : true,
		        hidden : true,
		        dataIndex : 'cpId',
		        flex : 1
	        },
		    {
		        text : '设备IP',
		        sortable : true,
		        dataIndex : 'ip',
		        flex : 1
		    },
//		    {
//				text : '所属中心',
//				dataIndex : 'centername',
//				width : 150
//			}, 
//			{
//				text : '操作系统',
//				dataIndex : 'os',
//				width : 150
//			},
//			{
//		        text : 'AgentIP',
//		        sortable : true,
//		        dataIndex : 'agentip',
//		        flex : 1
//		    },
		    {
				text : '设备端口',
				dataIndex : 'port',
				width : 80,
				flex : 1
			}, 
//			{
//				text : 'Agent用户',
//				dataIndex : 'user',
//				hidden:true,
//		        hideable: false,
//				width : 120
//			}, 
//			{
//				text : 'Agent密码',
//				dataIndex : 'passwd',
//				hidden:true,
//		        hideable: false,
//				width : 120
//			}, 
//			{
//				text : 'Agent路径',
//				dataIndex : 'agentpath',
//				hidden:true,
//		        hideable: false,
//				width : 120
//			}, 
			{
				text : '设备类型',
				dataIndex : 'ctype',
				width : 90,
				flex : 1
			}
//			,{
//				text : '中间件',
//				dataIndex : 'cname',
//				width : 90
//			}
//			, 
//			{
//				text : '设备描述',
//				dataIndex : 'des',
//				width : 160
//			}
	    ],
	    dockedItems : [/*{
		xtype : 'toolbar',
		dock : 'top',
    	border: false,
  		items:[
			ipBetween, 
			ipEnd,
			ctCombo
			]
	    },*/{
			xtype : 'toolbar',
			dock : 'top',
	    	border: false,
			items:[
				/*cmCombo,
				groupCombo,
				queryButtonForBSMNoSelected,
				resetBtn,*/
				'->',
				saveButtonForBSMNoSelected//增加按钮
			]
		}
		]
	   // ,bbar : bsPageBar
	});
	/** 判断增加按钮是否可用* */
	grid_panel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		saveButtonForBSMNoSelected.setDisabled (selections.length === 0);
	});
	/** 选择设备窗口* */
	var selCmpterWin = Ext.create('Ext.window.Window', {
		id:'selCmpterWinId',
	    title: '选择设备',
	    width: 800,
	    height: 570,
	    modal:true,
	    resizable:false,
	    closeAction: 'hide',
	    items:  [grid_panel]
	});
	
	selCmpterWin.on("show",function(self, eOpts){
		grid_panel.setHeight(selCmpterWin.getHeight()-modelHeigth-5);
		grid_panel.setWidth(selCmpterWin.getWidth()-10);
	});
	selCmpterWin.on("hide",function(self, eOpts){
		//resetWhere();
	});
	selCmpterWin.on("resize",function(self, eOpts){
		grid_panel.setHeight(selCmpterWin.getHeight()-modelHeigth-5);
		grid_panel.setWidth(selCmpterWin.getWidth()-10);
	});
	
/********************************************************************************************/
	
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		rightPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		rightPanel.setWidth (contentPanel.getWidth () );
	});
	//当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy (rightPanel);
    	Ext.destroy(selCmpterWin);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
    });
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	
	/** 重置 * */
	function resetWhere()
	{
		ipBetween.setValue(null);//起始IP
		ipEnd.setValue(null);//结束IP
		ctCombo.clearValue();//应用类型
		cmCombo.clearValue();//中间件
		groupCombo.clearValue();//任务组
	}
	
	/** 查询* */
	function queryWhere ()
	{
		
	}
	
	/** 增加设备* */
	function onSaveListener (btn)
	{
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		//把选中ip添加到数组中
		for (var i = 0, len = record.length; i < len; i++)
		{
			var serverRec = {};
			serverRec.id = record[i].data.cpId;
			serverRec.ip = record[i].data.ip; 
			serverRec.agentip = record[i].data.agentip;
//			serverRec.centername = record[i].data.centername;
			serverRec.port = record[i].data.port;
//			serverRec.os = record[i].data.os;
//			serverRec.user = record[i].data.user;
//			serverRec.passwd = record[i].data.passwd;
//			serverRec.agentpath = record[i].data.agentpath;
			serverRec.ctype = record[i].data.ctype;
			serverArr.push(serverRec);
		}
		serverInfoStore.add(serverArr);
		server_info_grid.view.refresh();
		Ext.getCmp("selCmpterWinId").hide();
	}
	
	//移除设备按钮
	function removeDeviceRelation()
	{
//		alert('移除设备按钮被点击了。。。');return;
		var grid_panel = Ext.getCmp("server_info_grid_id");
		var record = grid_panel.getSelectionModel ().getSelection ();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		Ext.MessageBox.confirm ('提示', "是否删除选中数据!", function (btn)
		{
			if (btn == 'no')
			{
				return;
			}
			if (btn == 'yes')
			{
				//处理勾选数据
				for (var i = 0, len = record.length; i < len; i++)
				{
					var selRec = record[i];
					var iid = selRec.data.id;
					for(var x=0; x<serverArr.length; x++){
						if(iid==serverArr[x].id){
							serverArr.baoremove(x);
							serverInfoStore.remove(selRec);
							break;
						}
					}
				}
				server_info_grid.view.refresh();
			}
		});
	}
	
	
/*************************************************************************/
	// 提交按钮
	function submitFunction(btn){
		Ext.MessageBox.alert('提示','保存成功');
	}
	
});

// 选择设备按钮
function openSelectWindow(){
	Ext.getCmp("selCmpterWinId").show();
}
