package com.ideal.controller.topo.monitorconfig;

import java.sql.SQLException;
import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.service.topo.monitorShell.MonitorShellService;

/**
 * 
 * @ClassName:  MonitorShellController   
 * @Description:监控脚本 管理 controller
 * @author: lichao_liu 
 * @date:   2017年9月5日 上午11:42:07   
 *     
 * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
@Controller
public class MonitorShellController
{

    private MonitorShellService monitorShellService = new MonitorShellService();

    /**
     * 
     * @Title: initMonitorShell   
     * @Description: 初始化页面  
     * @param: @return      
     * @return: String      
     * @throws   
     * @author: lichao_liu 
     * @date:   2017年9月5日 上午11:42:27
     */
    @RequestMapping("initMonitorShell.do")
    public String initMonitorShell ()
    {
        return "topo/monitorShell/monitorShell";
    }

    /**
    * 
    * @Title: getMonitorShellList   
    * @Description:监控脚本 数据获取   
    * @param name
    * @param shellName
    * @param page
    * @param limit
    * @param noSelectShellIds
    * @return
    * @throws DBException
    * @throws SQLException      
    * @author: wu_jian 
    * @date:   2018年2月9日 下午2:03:32
    */
    @RequestMapping("getMonitorShellList.do")
    @ResponseBody
    public Map getMonitorShellList ( String name, String shellName, String page, String limit, String noSelectShellIds )
    {
        return monitorShellService.getMonitorShellList(name, shellName, page, limit, noSelectShellIds);
    }

    /**
     * 
     * @Title: saveMonitorShell   
     * @Description: 保存
     * @param: @param jsonData
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: lichao_liu 
     * @date:   2017年9月5日 上午11:43:11
     */
    @RequestMapping("saveMonitorShell.do")
    @ResponseBody
    public Map saveMonitorShell ( String jsonData )
    {
        return monitorShellService.saveMonitorShell(jsonData);
    }

    /**
     * 
     * @Title: delMonitorShell   
     * @Description: 删除
     * @param: @param deleteIds
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: lichao_liu 
     * @date:   2017年9月5日 上午11:43:26
     */
    @RequestMapping("delMonitorShell.do")
    @ResponseBody
    public Map delMonitorShell ( String deleteIds )
    {
        return monitorShellService.deleteDataBase(deleteIds);
    }
}
