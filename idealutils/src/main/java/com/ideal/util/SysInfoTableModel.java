package com.ideal.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;

import javax.swing.table.AbstractTableModel;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR> attributable
 * @version 1.0
 */

public class SysInfoTableModel extends AbstractTableModel
{
    static private Object[] keys;
    static
    {
        ArrayList listKey = new ArrayList();
        Enumeration keysEnum = System.getProperties().keys();
        while (keysEnum.hasMoreElements())
        {
            listKey.add(keysEnum.nextElement());
        }
        keys = listKey.toArray();
        Arrays.sort(keys);
    }
    private String          _colNameProp, _colNameVal;

    public SysInfoTableModel(String ColNameProp, String ColNameVal)
    {
        super();
        _colNameProp = ColNameProp;
        _colNameVal = ColNameVal;
    }

    public String getColumnName ( int column )
    {
        if (0 == column)
            return _colNameProp;
        return _colNameVal;
    }

    public int getRowCount ()
    {
        return keys.length;
    }

    public int getColumnCount ()
    {
        return 2;
    }

    public Object getValueAt ( int rowIndex, int columnIndex )
    {
        if (rowIndex < 0 || rowIndex >= keys.length)
            return "";
        if (columnIndex == 0)
        {
            return keys[rowIndex];
        } else
        {
            return System.getProperty(keys[rowIndex].toString());
        }
    }

}