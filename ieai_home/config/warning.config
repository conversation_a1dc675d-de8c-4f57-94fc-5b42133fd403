##################################################
#告警开关设置

# CHECKSWITCH	巡检告警开关
# ENTEGORWARN	切换、系统、调度告警开关

CHECKSWITCH=false
ENTEGORWARN=false

##################################################
#告警报文设置

warninginfo=日期|时间|被管IP|告警类型|告警级别|被管主机名|告警描述信息

##################################################
#告警写入设置
#WARNLEVEL	仅写入符合设置级别的告警到文件

WARNLEVEL=3,4,5

##################################################
#告警写入设置

#DBMINCONN	数据库连接最小剩余数
DBMINCONN=20

##################################################
#各异常告警级别设置

W_SYSERR=4
W_ACTERR=3
W_DBERR=5
W_SYSHEALTH=5
W_SYSFAILOVER=5
W_ACTTIMEOUT=3
W_OUTOFMEMERY=5

##################################################
#监控开关设置

# SYSERR			作业系统异常告警
# ACTERR			批量执行中断告警
# DBERR			系统数据库异常告警
# SYSHEALTH		系统心跳检查告警
# SYSFAILOVER	系统失效备援告警
# ACTTIMEOUT	作业超时告警

SYSERR=false
ACTERR=false
DBERR=flase
SYSHEALTH=false
SYSFAILOVER=false
ACTTIMEOUT=false
SYSMEMERR=false

################################################## 
#检查结果中不存在的检查点报警级别
NOCHECKLEVEL=-3