package com.ideal.ieai.server.webservice;




import java.util.Map;

/**
 * It is used to pass the definition of workflow's input parameters and
 * environment variables to client.
 * <p>Title: communication</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: ideal</p>
 * <AUTHOR>
 * @version 3.0
 */
public class InputParamAndEnvVarDef
{
    public InputParamAndEnvVarDef()
    {
    }

    /**
     * The key of inputParamDef is name of workflow, and value is a List, of which
     * first element is a parameter name, and second element is the type name of the parameter,
     * According to the order stores other parameters.
     */
    public Map inputParamDef;

    /**
     * The list contains some instances of class EnvVar
     */
    public EnvVar[] envVarDef;

}
