Ext.onReady(function() {
	
	//清理各种监听
	destroyRubbish();
	
	var hdSysStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		storeId : 'hssys_store',
		fields : [ 'id', 'name' ],
		proxy : {
			type : 'ajax',
			url : 'hdgetSysName.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var leftComboBox = Ext.create('Ext.form.field.ComboBox', {
		name : 'hdsysname',
		store : hdSysStore,
		fieldLabel: '系统分类',
		labelWidth: 65,
		anchor: '99.5%',
		displayField : 'name',
		valueField : 'name',
//		emptyText : "--请选择系统分类--",
		listConfig : {
			maxHeight : 200
		},
		editable : true,
		listeners : {
			select : function() {
			},
			beforequery : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var value = e.query;
					combo.store.filterBy(function(record, id) {
						var text = record.get(combo.displayField);
						return (text.toLowerCase().indexOf(
								value.toLowerCase()) != -1);
					});
					combo.expand();
					return false;
				}
			}
		}
	});
	
	/* 解决IE下trim问题 */
  String.prototype.trim = function() {
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
	
	
	var	hdupLoadformPane =Ext.create('Ext.form.Panel', {
			cls:'customize_panel_back panel_space_bottom',
	        border:false,
	        bodyPadding : 10,
			items: [
				leftComboBox,
				{
					xtype: 'filefield',
					clearOnSubmit : false,
					name: 'hdfile', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelStyle:'margin:10px 0 0 0',
					labelWidth: 65,
					msgTarget: 'side',
					height : 30,
					anchor: '100%',
					buttonText: '选择文件'
				},{
					xtype : 'textfield',
					name:'hdversioninfo',
					fieldLabel: '版本说明',
					labelWidth: 65,   
				    emptyText : '--请输入版本说明--',
				    anchor: '99.5%'
				},{
					xtype : 'textfield',
					name:'flag',
					hidden:true,
					value:0,
					labelWidth: 65,
				    anchor: '100%'
				},
				{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items:['->',{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '上传',
						handler: function() {
							var form = hdupLoadformPane.getForm();
							var hdupfile=form.findField("hdfile").getValue();
							var hdtmpFilNam=hdupfile;
			    			if(hdupfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			if(!checkFile(hdtmpFilNam)){
			    			  form.findField("hdfile").setRawValue('');
			    			  return;
			    			}
			    			
			    			var hdvinfo=form.findField("hdversioninfo").getValue();
				            if(hdvinfo==''){
				              Ext.Msg.alert('提示',"请填写版本说明！");
				              return ;
				            }
				            if(hdvinfo.length>200){
				              Ext.Msg.alert('提示',"版本说明长度超过200字符！");
				              return ;
				            }
			    			
			    			var hdsysnameup=form.findField("hdsysname").getValue();
			    			if(hdsysnameup!=null){
			    			  hdsysnameup = hdsysnameup.trim();
			    			}
			    			if(isZip(hdtmpFilNam)) {			    			  
			    			    Ext.Msg.confirm("请确认", "由于您上传的是zip文件，zip文件中的文件名将做为系统分类", function(button, text) {
			    			      if (button == "yes") {
			    			        uploadTemplate(form);
			    			      }
			    			    });
			    			  
			    			} else {
				                if(hdsysnameup==''||hdsysnameup==null){
				                  Ext.Msg.alert('提示',"请选择系统分类！");
				                  return ;
				                }
				                uploadTemplate(form);
			    			}
						}
					},{
						xtype : 'button',
						cls : 'Common_Btn',
						text : '重置',
						handler: function() {
							var form = this.up('form').getForm();
							form.reset();
							return;
						}
					}]
				}
				
			]
		});
	
	 var mainPanel = Ext.create('Ext.panel.Panel',{
        renderTo : "hdinfo_grid_area",
        layout:'fit',
        width:contentPanel.getWidth(),
        border : false,
        bodyPadding : 0,
        bodyCls: 'service_platform_bodybg',
        items : [hdupLoadformPane]
     });
	 
	 contentPanel.on('resize', function() {
		 mainPanel.setWidth(contentPanel.getWidth());
	 })
	 
	 function checkFile(fileName){  
	    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([zZ][iI][pP]){1}$/;  
	    if(!file_reg.test(fileName)){  
	        Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
	        return false;
	    }
	    return true;
	}
	 
	 function isZip(fileName) {
	   var file_reg = /\.([zZ][iI][pP]){1}$/;   
	   if(file_reg.test(fileName)){
	     return true;
	   } else {
	     return false;
	   }
	 }
	 function isExcel(fileName) {
	   var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$/;   
	   if(file_reg.test(fileName)){
	     return true;
	   } else {
	     return false;
	   }
	 }
	 
	 function uploadTemplate(form,x) {
		 
	   var flag;
	   if (x==undefined){
		   flag=0;
	   } else {
		   flag=x;
	   }
	   
	   // flag 标志是否强制  0否  1是
	   if (form.isValid()) {
       form.submit({
         url: 'hduploadSusExecl.do',
           success: function(form, action) {
        	   var msg = Ext.decode(action.response.responseText).message;
               Ext.Msg.alert('提示', msg);
               return;
           },
           failure: function(form, action) {
               var msg = Ext.decode(action.response.responseText).message;
               
               msg = msg.replace(/！/g,"！<br>");
               var mess = Ext.create('Ext.window.MessageBox', {
               minHeight : 110,
               minWidth : 500,
               maxWidth:1080
             });
             if (flag==0){
            	 mess.confirm("提示",msg+"是否忽略提示,强制导入?", 
         			function(btn){
         			    if (btn == 'yes'){
         			    	form.findField("flag").setRawValue(1);
         			    	uploadTemplate(form,1);
         			    }else{
         			    	return;
         			    }
         			});
               } else {
            	 mess.alert('提示', msg);
               }
           }
       });
	   }
	 }
});
