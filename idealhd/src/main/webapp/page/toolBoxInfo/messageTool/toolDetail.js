var messageToolDetailForm;
var messageToolDetailForm1;
var message_agentInfo_grid;
var ageForm;
var message_agentInfo_store;
Ext.onReady(function() {
    Ext.define('messageAgentInfoData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid',     type: 'int'},
            {name: 'itoolsId', type: 'int'},
            {name: 'iparamNum', type: 'string'},
            {name: 'iparamName',     type: 'string'},
            {name: 'iparamValue',   type: 'string'},
            {name: 'iparamDes',   type: 'string'},
            {name: 'iscriptParamValue',   type: 'string'}
        ]
    });
    var messageShows =  [





    ];
     message_agentInfo_store = Ext.create('Ext.data.Store', {
        autoLoad : true,
        autoDestroy : true,
        remoteSort: true,
        model: 'messageAgentInfoData',
        tbar:false,
        proxy: {
            type: 'ajax',
            url: 'everbrightbankToolInfo/getScriptInfoList.do',
            extraParams:{
                toolsId : toolsId,
                type : 1,
                toolScriptType : toolScriptType,//'1'?'脚本服务化':'agent本地脚本'
            },
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    if(itoolScriptId!=null&&itoolScriptId!=''&&itoolScriptId!="null"){
    }

    var message_agentInfo_columns = [
        { text: 'iid',  dataIndex: 'iid',hidden:true,width: '10%'},
        { text: '执行顺序', dataIndex:'iparamNum', width: '25%'},
        { text: '参数类型',  dataIndex: 'iparamName',width: '25%'},
        { text: '默认值',  dataIndex: 'iparamValue',hidden:true,width: '25%', renderer: function (value, metaData, record) {
                //alert(value);
                let showValue = value;
                let paramType = record.get('iparamName');
                if (paramType == 'IN-string(加密)') {
                    let xing = "";
                    var decodeVal = getSMEncode(value, 0);
                    if (!(decodeVal == null || decodeVal == '')) {
                        value = decodeVal;
                    }
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }
            },
        { text: '参数值',  dataIndex: 'iscriptParamValue',width: '25%', renderer: function (value, metaData, record) {
                //alert(value);
                let showValue = value;
                let paramType = record.get('iparamName');
                if (paramType == 'IN-string(加密)') {
                    let xing = "";
                    var decodeVal = getSMEncode(value, 0);
                    if (!(decodeVal == null || decodeVal == '')) {
                        value = decodeVal;
                    }
                    let len = value.length;
                    for (let i = 0; i < len; i++) {
                        xing += "*";
                    }
                    showValue = xing;
                }
                return showValue;
            }
        },
        {
            text: '参数描述',
            dataIndex: 'iparamDes',width: '25%'
        }
    ];
    //message_agentInfo_store.load();
    var selModelServer = Ext.create('Ext.selection.CheckboxModel');
    message_agentInfo_grid = Ext.create('Ext.grid.Panel', {
        selModel:selModelServer,
        region : 'center',
        store:message_agentInfo_store,
        padding : panel_margin,
//         	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border:true,
        columnLines : true,
        columns:message_agentInfo_columns,
        viewConfig:{
            enableTextSelection:true
        },
        overflow:'auto',
        autoScroll : true,
        bodyCls : 'x-docked-noborder-down',
        height:200,
        listeners: {
            beforeEdit: function (editor, e) {
                if('脚本服务化'==itoolScriptId){
                    if (e.field == "iparamType" || e.field == "iparamValue" || e.field == "iparamDes") {
                        return false;
                    }
                }
            }
        },
        viewConfig:{
            enableTextSelection:true
        },
        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit:2 })]

    });
    //附件panel
    var panelFile = Ext.create("Ext.form.FormPanel",{
        title:'附件',
        id:"addONotice",
        border: false,
        fileUpload: true,
        autoScroll : true,
        fieldDefaults: {
            labelWidth: 70,
            labelAlign:'right',
            labelStyle:'padding-right:10px'
        },
        layout:'border',
        bodyPadding: 10,
        height:180,
        items:[]
    });

    message_form = Ext.create('Ext.form.Panel', {
//		title:'报警详情',
        bodyCls : 'x-docked-noborder-top',
        //cls:'customize_panel_back panel_space_right',
        border:false,
        region : 'west',
        split : true,
        width : 900,
        padding : '10 0 0 0',
        items : [{
            layout:'column',
            width : '100%',
            border : false,
            items : [
                {fieldLabel:'工具编号',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'itoolCode',xtype:"textfield",id: 'itoolCode',value:""},
                {fieldLabel:'工具名称',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'itoolName',xtype:"textfield",id:'itoolName',value:""},
                {fieldLabel:'应用系统',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'isystem',xtype:"textfield",id:'isystem',value:""}
            ]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items : [
                {fieldLabel:'操作系统',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'nOsType',xtype:"textfield",id:'nOsType',value:""},
                {fieldLabel:'一级分类',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'ionetypeId',xtype:"textfield",id:'ionetypeId',value:""},
                {fieldLabel:'二级分类',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itwotypeId',xtype : "textfield",id :'itwotypeId',value:""}
            ]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items: [
                {fieldLabel:'三级分类',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'ithreetypeId',xtype:"textfield",id :'ithreetypeId',value:""},
                {fieldLabel:'工具类型',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolTypeId',xtype:"textfield",id:'itoolTypeId',value:""},
                {fieldLabel:'工具状态',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolStatusId',xtype:"textfield",id:'itoolStatusId',value:""}

            ]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items: [
                {fieldLabel:'关键字' ,padding: '5 5 5 5', labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolKeyword',xtype:"textfield",id:'itoolKeyword',value:""},
                {fieldLabel:'匹配IP' ,padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'nSummarycn',xtype:"textfield",id:'itoolMatchIp',value:""},
                {fieldLabel:'脚本来源',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolScriptType',xtype:"textfield",id:'itoolScriptType',value:""}


            ]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items: [
                {fieldLabel:'脚本名称',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolScriptName',xtype:"textfield",id:'itoolScriptName',value:""},
                {fieldLabel:'操作用户',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'iuserName',xtype:"textfield",id :'iuserName',value:""},
                {fieldLabel:'是否高危',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'isHighRisk',xtype:"textfield",id :'isHighRisk',value:""}
            ]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items: [{fieldLabel:'工具描述',padding: '5 5 5 5',labelWidth:90,width:860,grow:true,growMin:100,growMax:100,labelAlign:'right',readOnly:true,name:'itoolDescribe',xtype:"textareafield",id:'itoolDescribe',value:""}]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items: [{fieldLabel:'一线工具描述',padding: '5 5 5 5',labelWidth:90,width:860,grow:true,growMin:100,growMax:100,labelAlign:'right',readOnly:true,name:'itoolFirstDescribe',xtype:"textareafield",id:'itoolFirstDescribe',value:""}]
        },{
            layout:'column',
            width : '100%',
            border : false,
            items: [
                {fieldLabel:'退回原因',padding: '5 5 5 5',labelWidth:90,width:860,grow:true,growMin:100,growMax:100,labelAlign:'right',readOnly:false,maxLength:1500,name:'itoolReturnCause',xtype:"textareafield",id:'itoolReturnCause',value:""}
            ]
        // },{
        //     layout: 'column',
        //     with:'100%',
        //     border:false,
        //     items: [panelFile]
        }
        ]
    });


    messageToolDetailForm1 = Ext.create('Ext.form.Panel', {
        region: 'south',
        border: false,
        bodyStyle: "height:100%;width:100%",
        autoScroll: true,
        items: [panelFile]
    });
    var paneltop = Ext.create('Ext.panel.Panel', {
        border: false,
        layout: 'border',
        height: 200,
        width: '95%',
        region: 'center',
        items: [messageToolDetailForm1]
    });

    var messagepaneltop = Ext.create('Ext.panel.Panel',{
        title:'工具详情',
        border : false,
        split : true,
        region : 'north',
        height: 650,
        width : 880,
        cls:'customize_panel_back',
        layout : {
            type : 'border'
        },
        autoScroll : false,
        items : [message_form]
    });
    var messagepaneldown = Ext.create('Ext.panel.Panel',{
        title :'脚本详情',
        hidden:itoolTypeId==1?true:false,
        border : false,
        split:true,
        width : 880,
        region : 'center',
        height:200,
        items : [message_agentInfo_grid]
    });
    var messagemainPanel = Ext.create('Ext.panel.Panel',{
        renderTo : "messageToolDetailDiv",
        bodyPadding : grid_margin,
        cls:'customize_panel_back',
        border : true,
        layout : 'border',
        bodyCls:'service_platform_bodybg',
        height : itoolTypeId==1?880:1000,
        width : 880,
        border : false,
        buttonAlign: "center",
        items : [messagepaneltop,paneltop,messagepaneldown],
        buttons : [ {
            xtype : 'toolbar',
            dock : 'bottom',
            items : [ {
                xtype : 'button',
                textAlign : 'center',
                cls : 'Common_Btn',
                text : '通过',
                handler : function(){
                    saveRole(2);
                }
            },{
                xtype : 'button',
                textAlign : 'center',
                cls : 'Common_Btn',
                text : '退回',
                handler : function(){
                    saveRole(3);
                }
            },{
                xtype : 'button',
                textAlign : 'center',
                cls : 'Common_Btn',
                text : '关闭',
                handler : function(){
                    ToolDetailWindow.close();
                }
            } ]
        } ]
    });
    Ext.Ajax.request({
        url : 'everbrightbankToolInfo/getToolDetailView.do',
        params : {
            iid:iid
        },
        success : function(response, opts) {
            var toolDetail =Ext.decode(response.responseText).toolsModel;
            var itoolCode=toolDetail.itoolCode;
            Ext.getCmp('itoolCode').setValue(itoolCode);
            var iid=toolDetail.iid;
            var isystem= toolDetail.isystem;
            Ext.getCmp('isystem').setValue(isystem);
            var ionetypeId=toolDetail.ionetypeId;
            var ionetypeId=toolDetail.params['ionetypeId'];
            Ext.getCmp('ionetypeId').setValue(ionetypeId);
            var itwotypeId=toolDetail.params['itwotypeId'];
            Ext.getCmp('itwotypeId').setValue(itwotypeId);
            var ithreetypeId=toolDetail.params['ithreetypeId'];
            Ext.getCmp('ithreetypeId').setValue(ithreetypeId);
            var iuserName=toolDetail.iuserName;
            Ext.getCmp('iuserName').setValue(iuserName);
            var isHighRisk=toolDetail.isHighRisk;
            Ext.getCmp('isHighRisk').setValue(isHighRisk);
            var itoolMatchIp=toolDetail.itoolMatchIp;
            Ext.getCmp('itoolMatchIp').setValue(itoolMatchIp);
            var itoolName=toolDetail.itoolName;
            Ext.getCmp('itoolName').setValue(itoolName);
            var itoolTypeId=toolDetail.params['itooltypeId'];
            Ext.getCmp('itoolTypeId').setValue(itoolTypeId);
            var itoolScriptType=toolDetail.itoolScriptType;
            if("1"==itoolScriptType){itoolScriptType="脚本服务化"};
            if("2"==itoolScriptType){itoolScriptType="agent本地脚本"};
            var itoolScriptNameComponent = Ext.getCmp('itoolScriptName');
            itoolScriptNameComponent.setValue(toolDetail.itoolScriptName);
            if("脚本服务化" == itoolScriptType){
                itoolScriptNameComponent.setFieldLabel('服务名称');
            }
            else{
                itoolScriptNameComponent.setFieldLabel('脚本名称');
            }
            Ext.getCmp('itoolScriptType').setValue(itoolScriptType);
            var itoolScriptName=toolDetail.itoolScriptName;
            Ext.getCmp('itoolScriptName').setValue(itoolScriptName);
            var itoolStatusId=toolDetail.params['itoolStatusId'];
            Ext.getCmp('itoolStatusId').setValue(itoolStatusId);
            var itoolKeyword=toolDetail.itoolKeyword;
            Ext.getCmp('itoolKeyword').setValue(itoolKeyword);
            var itoolDescribe=toolDetail.itoolDescribe;
            Ext.getCmp('itoolDescribe').setValue(itoolDescribe);
            var itoolFirstDescribe=toolDetail.itoolFirstDescribe;
            Ext.getCmp('itoolFirstDescribe').setValue(itoolFirstDescribe);
            var itoolReturnCause=toolDetail.itoolReturnCause;
            Ext.getCmp('itoolReturnCause').setValue(itoolReturnCause);
            //nOsType
            var nOsType=toolDetail.nOsType;
            Ext.getCmp('nOsType').setValue(nOsType);
            console.log("工具类型"+itoolTypeId)
            if(itoolTypeId=="描述工具"){
                var listfile=toolDetail.params.fileList;
                var file="";
                for(var i=0;i<listfile.length;i++){
                    var fileName=listfile[i].fileName;
                    var fileId=listfile[i].fileId;
                    file+="<a href='javaScript:void(0)' onclick=downFile('"+fileId+"')>"+listfile[i].fileName+"</a><br>"
                }
                var p =Ext.getCmp("addONotice");
                p.body.update(file);
                Ext.getCmp('iuserName').hide();
                Ext.getCmp('itoolScriptType').hide();
                Ext.getCmp('itoolScriptName').hide();
            }
        },
        failure : function(response, opts) {

        }
    });
});
function downFile(fileId){
    	   var userAgent = navigator.userAgent; // 取得浏览器的userAgent字符串
    		var isOpera = userAgent.indexOf("Opera") > -1; // 判断是否Opera浏览器
    		var isIE = userAgent.indexOf("compatible") > -1
    			&& userAgent.indexOf("MSIE") > -1 && !isOpera; // 判断是否IE浏览器
    		var isEdge = userAgent.indexOf("Edge") > -1; // 判断是否IE的Edge浏览器
    		var isFF = userAgent.indexOf("Firefox") > -1; // 判断是否Firefox浏览器
    		var isSafari = userAgent.indexOf("Safari") > -1
    			&& userAgent.indexOf("Chrome") == -1; // 判断是否Safari浏览器
    		var isChrome = userAgent.indexOf("Chrome") > -1
    			&& userAgent.indexOf("Safari") > -1; // 判断Chrome浏览器
    	if(isFF){
    	  window.location ='toolboxInfo/toolsFileDown.do?fileId='+fileId;
    	}else{
    	   window.location.href ='toolboxInfo/toolsFileDown.do?fileId='+fileId;
    	}
}
function getParams(value) {
    message_agentInfo_store.sort('iparamNum', 'ASC');
    var m = message_agentInfo_store.getRange(0, message_agentInfo_store.getCount() - 1);
    var aaaa = [];
    for (var i = 0, len = m.length; i < len; i++) {
        var paramValue = m[i].get(value) ? m[i].get(value): '';
        aaaa.push(paramValue);
    }
    return aaaa.join("@@script@@service@@");
}

function checkJiaoben(key,value){
    var scriptP = value.split('@@script@@service@@');
    if(key=="默认值"){
        for (var i = 0, len = scriptP.length; i < len; i++) {
            if(scriptP[i].length>1000){
                Ext.Msg.alert('提示', key+'长度不得超过1000');
                return false;
            }
        }
    }else{
        for (var i = 0, len = scriptP.length; i < len; i++) {
            if(scriptP[i].length>255){
                Ext.Msg.alert('提示', key+'长度不得超过255');
                return false;
            }
        }
    }
    return true;
}
// 保存记录
function saveRole(reCode) {
    var itoolReturnCause=Ext.getCmp('itoolReturnCause');
    if(reCode==3){
        if(Ext.isEmpty(itoolReturnCause.getValue().trim())){
            return Ext.Msg.alert("提示", "请填写退回原因！");
        }
        if(itoolReturnCause.getValue().trim().length>1500){
            return Ext.Msg.alert("提示", "退回原因超长！");
        }
        if(itoolReturnCause.wasValid==false){
            return Ext.Msg.alert("提示", "【"+itoolReturnCause.fieldLabel+"】"+itoolReturnCause.activeErrors);
        }
    }
    var scriptName = getParams('iparamName');
    var scriptValue = getParams('iparamValue');
    var scriptParaDesc = getParams('iparamDes');
    if(!checkJiaoben('参数名称',scriptName)){
        return;
    }
    if(!checkJiaoben('默认值',scriptValue)){
        return;
    }
    if(!checkJiaoben('描述',scriptParaDesc)) {
        return;
    }
     scriptName = getParams('iparamName');
     scriptValue = getParams('iparamValue');
     scriptParaDesc = getParams('iparamDes');

    Ext.Ajax.request({
        url : 'everbrightbankToolInfo/checkToolDetail.do',
        params : {
            reCode:reCode,
            iid:iid,
            itoolReturnCause:itoolReturnCause.getValue(),
            scriptName:scriptName,
            scriptValue:scriptValue,
            scriptParaDesc:scriptParaDesc
        },
        success : function(response) {
            var success = Ext.decode(response.responseText).success;
            var message = Ext.decode(response.responseText).message;
            if (success==0) {
                if(reCode==3){
                    Ext.Msg.alert("提示", "退回成功！");
                }
                if(reCode==2){
                    Ext.Msg.alert("提示", "审核成功！");
                }
                ToolDetailWindow.close();

            } else {
                Ext.Msg.alert("提示", message);
            }
            //store1.reload();
        },
        failure : function(result, request) {
            secureFilterRs(result,"操作失败！",request);

        }

    });
}