--4.7.22
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_RESULT_CSV';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NESSUS_RESULT_CSV(IID DECIMAL(19) NOT NULL ,PLUGINID VARCHAR(50) ,<PERSON><PERSON> VARCHAR(50) ,CVSS VARCHAR(50) ,RISK VARCHAR(50) ,HOST VARCHAR(50) ,PROTOCOL VARCHAR(50) ,PORT DECIMAL(6) ,NAME VARCHAR(255) ,SYNOPSIS VARCHAR(255) ,DESCRIPTION VARCHAR(1000) ,SOLUTION VARCHAR(255) ,SEEALSO VARCHAR(255) ,PLUGINOUTPUT CLOB ,INESSUSID DECIMAL(19) NOT NULL ,CONSTRAINT PK_IEAI_NESSUS_RESULT_CSV PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_FILE_CONTENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NESSUS_FILE_CONTENT(IID DECIMAL(19) NOT NULL ,IP VARCHAR(255) ,BUSINESSNAME VARCHAR(255) ,OPERATSYSTEM VARCHAR(255) ,BINARYFILEIID DECIMAL(19) NOT NULL ,CONSTRAINT PK_IEAI_NESSUS_FILE_CONTENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BINARY_FILE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_BINARY_FILE(IID DECIMAL(19) NOT NULL ,USERID DECIMAL(19) ,BINARYFILE BLOB ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,EXECOPERATION DECIMAL(5) ,FILENAME VARCHAR(1000) ,CONSTRAINT PK_IEAI_BINARY_FILE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_EQUIPMENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NESSUS_EQUIPMENT(IID DECIMAL(19) NOT NULL ,INESSUSID DECIMAL(19) NOT NULL ,IP VARCHAR(255) NOT NULL ,CPID DECIMAL(19) ,ISTATUS DECIMAL(2) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,CONSTRAINT PK_IEAI_NESSUS_EQUIPMENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NESSUS_RESULT(IID DECIMAL(19) NOT NULL ,INESSUSPLUS VARCHAR(255) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,IEQUIPMENTID DECIMAL(19) NOT NULL ,CONSTRAINT PK_IEAI_NESSUS_RESULT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DISTRIBUTED_TASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_DISTRIBUTED_TASK(IID DECIMAL(19) NOT NULL ,ISTATUS DECIMAL(5) DEFAULT 1 ,SERVERIP VARCHAR(50) ,JOBIID DECIMAL(19) ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,JOBCLASS VARCHAR(255) ,CONSTRAINT PK_IEAI_DISTRIBUTED_TASK PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_REPAIR_TASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NESSUS_REPAIR_TASK(IID DECIMAL(19) NOT NULL ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ITYPE DECIMAL(5) ,ITASKNAME VARCHAR(255) ,REQID VARCHAR(100) ,IENDTIME TIMESTAMP ,CONSTRAINT PK_IEAI_NESSUS_REPAIR_TASK PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_QUERY_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_NESSUS_QUERY_RESULT(IID DECIMAL(19) NOT NULL ,IP VARCHAR(255) ,CURNESSUS VARCHAR(1000) ,CURREPAIR VARCHAR(255) ,CURREPAIRTIME TIMESTAMP DEFAULT NULL ,CURNESSUSID DECIMAL(19) ,LASTNESSUS VARCHAR(1000) ,LASTREPAIR VARCHAR(255) ,LASTREPAIRTIME TIMESTAMP DEFAULT NULL ,LASTNESSUSID DECIMAL(19) ,CONSTRAINT PK_IEAI_NESSUS_QUERY_RESULT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_REPAIR_PLUS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_REPAIR_PLUS(IID DECIMAL(19) NOT NULL ,INESSUSPLUS VARCHAR(225) ,INESSUSID DECIMAL(19) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ISTATUS DECIMAL(2) DEFAULT 10 ,CONSTRAINT PK_IEAI_REPAIR_PLUS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_REPAIR_ACT_LIST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_REPAIR_ACT_LIST(IID DECIMAL(19) NOT NULL ,IACTNAME VARCHAR(255) ,IFOLLOWID DECIMAL(19) ,ISTATUS DECIMAL(2) DEFAULT 11 ,IPARENTID DECIMAL(19) ,IBASEAUTOID DECIMAL(19) ,IACTCLASS VARCHAR(255) ,ISTANDARDID DECIMAL(19) ,CONSTRAINT PK_IEAI_REPAIR_ACT_LIST PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_REPAIR_EQUIPMENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_REPAIR_EQUIPMENT(IID DECIMAL(19) NOT NULL ,INESSUSPLUS VARCHAR(255) ,IREPAIRID DECIMAL(19) ,INESSUSID DECIMAL(19) NOT NULL ,IP VARCHAR(255) NOT NULL ,CPID DECIMAL(19) ,IRESULT VARCHAR(2000) ,ISTATUS DECIMAL(2) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ERRORDESC VARCHAR(1000) ,CONSTRAINT PK_IEAI_REPAIR_EQUIPMENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SAFETY_BASE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SAFETY_BASE(IID DECIMAL(19) NOT NULL ,SAFETYCONFIGNAME VARCHAR(255) ,NESSUSPLUGINID VARCHAR(255) ,SAFETYDEFECTNAME VARCHAR(225) ,DEFECTDESC VARCHAR(1000) ,ISMUST DECIMAL(2) ,HARMLEVEL DECIMAL(2) ,USEDIFFICULT DECIMAL(2) ,CONFIGOBJECT VARCHAR(255) ,BASEASK VARCHAR(1000) ,BASECOMMAND VARCHAR(600) ,REMARKS VARCHAR(1000) ,BINARYFILEIID DECIMAL(19) ,SERIALNUMBER VARCHAR(200) ,AOMS VARCHAR(500) ,CONSTRAINT PK_IEAI_SAFETY_BASE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SAFETY_BASE_AUTO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_SAFETY_BASE_AUTO(IID DECIMAL(19) NOT NULL ,BASEIID DECIMAL(19) ,OPERATENAME VARCHAR(255) ,OPERATEVALUE VARCHAR(255) ,UPDATETIME TIMESTAMP ,CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,STATE DECIMAL(2) ,CONSTRAINT PK_IEAI_SAFETY_BASE_AUTO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_REPAIR_PROGRAM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_REPAIR_PROGRAM(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,ICONFIG VARCHAR(255) ,IDESC VARCHAR(1000) ,ITYPE DECIMAL(3) ,CONSTRAINT PK_IEAI_REPAIR_PROGRAM PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ASYNCHRONY_RUNTIME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_ASYNCHRONY_RUNTIME(IID DECIMAL(19) NOT NULL ,ITASKID DECIMAL(19) ,ISERVERIP VARCHAR(50) ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,ISTATUS DECIMAL(5) DEFAULT 1 ,ITASKCLASS VARCHAR(500) ,CONSTRAINT PK_IEAI_ASYNCHRONY_RUNTIME PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SOLUTION' AND TABNAME='IEAI_NESSUS_RESULT_CSV';
		IF LI_EXISTS = 1 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_NESSUS_RESULT_CSV ALTER COLUMN  SOLUTION SET DATA TYPE VARCHAR(500)';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ERRORDESC' AND TABNAME='IEAI_NESSUS_EQUIPMENT';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_NESSUS_EQUIPMENT ADD COLUMN ERRORDESC VARCHAR(1000)';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATUS' AND TABNAME='IEAI_ASYNCHRONY_RUNTIME';
		IF LI_EXISTS = 1 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_ASYNCHRONY_RUNTIME ALTER COLUMN  ISTATUS SET  DEFAULT 1';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;

	END
	$
--4.7.23
    BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SAFETYDEFECTNAME' AND TABNAME='IEAI_REPAIR_EQUIPMENT';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_REPAIR_EQUIPMENT ADD COLUMN SAFETYDEFECTNAME VARCHAR(255)';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_USER_MANAGE_MENT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_USER_MANAGE_MENT(IID DECIMAL(19) NOT NULL, ITYPE DECIMAL(5), IADDRESSOR VARCHAR(50), ISSEND DECIMAL(2), ICOPYUSER VARCHAR(50), IEMAIL VARCHAR(50),CONSTRAINT PK_IEAI_USER_MANAGE_MENT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BINARY_MIDDLE';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='CREATE TABLE IEAI_BINARY_MIDDLE(IID DECIMAL(19) NOT NULL ,BINARY_ID DECIMAL(19) NOT NULL ,RELATION_ID DECIMAL(19) NOT NULL ,OPERATIONTYPE DECIMAL(5) NOT NULL ,CONSTRAINT PK_IEAI_BINARY_MIDDLE PRIMARY KEY (IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='BINARYFILE' AND TABNAME='IEAI_BINARY_FILE';
			IF LI_EXISTS > 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_BINARY_FILE ALTER COLUMN BINARYFILE SET DATA TYPE BLOB(10485760)';
		    	PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
		END IF;
	END
	$
--4.7.25
    BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SERIALNUMBER' AND TABNAME='IEAI_REPAIR_EQUIPMENT';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_REPAIR_EQUIPMENT ADD COLUMN SERIALNUMBER VARCHAR(200)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='LASTNESSUS' AND TABNAME='IEAI_NESSUS_QUERY_RESULT';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='ALTER TABLE IEAI_NESSUS_QUERY_RESULT ALTER  COLUMN LASTNESSUS  set data type varchar(1000)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;  
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='CURNESSUS' AND TABNAME='IEAI_NESSUS_QUERY_RESULT';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL ='ALTER TABLE IEAI_NESSUS_QUERY_RESULT ALTER  COLUMN CURNESSUS  set data type varchar(1000)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;  
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISCREATE' AND TABNAME='IEAI_NESSUS_EQUIPMENT';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_NESSUS_EQUIPMENT ADD COLUMN ISCREATE DECIMAL(2)';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FLAG' AND TABNAME='IEAI_REPAIR_PLUS';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_REPAIR_PLUS ADD COLUMN FLAG DECIMAL(19)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FLAG' AND TABNAME='IEAI_REPAIR_EQUIPMENT';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='ALTER TABLE IEAI_REPAIR_EQUIPMENT ADD COLUMN FLAG DECIMAL(19)';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NESSUS_REPAIR_KEY';
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='CREATE TABLE IEAI_NESSUS_REPAIR_KEY(IID DECIMAL(19) NOT NULL ,NESSUSTADKID DECIMAL(19) ,REPAIRTADKID DECIMAL(19) ,TYPE DECIMAL(2)  DEFAULT 0,CONSTRAINT PK_IEAI_NESSUS_REPAIR_KEY PRIMARY KEY (IID))';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ERRORDESC' AND TABNAME='IEAI_NESSUS_RESULT';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_NESSUS_RESULT ADD COLUMN ERRORDESC VARCHAR(1000)';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ERRORDESC' AND TABNAME='IEAI_NESSUS_RESULT';
		IF LI_EXISTS = 1 THEN
		    SET LS_SQL ='ALTER TABLE IEAI_NESSUS_RESULT ALTER COLUMN ERRORDESC SET  DATA TYPE VARCHAR(1000)';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;

END
$