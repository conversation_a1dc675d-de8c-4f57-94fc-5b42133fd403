/*******************************************************************************
 * Shellcmd 活动配置
 ******************************************************************************/
var groupSus__webStudio = groupSus__webStudio || {};

for(var key in groupSus__webStudio){
	delete groupSus__webStudio[key];
}

var IEAI_SUS_STEP_CLOSE='告警压制_开启';
var IEAI_SUS_STEP_OPEN ='告警压制_解除';
var IEAI_SUS_STEP_CLOSE_2='关';
var IEAI_SUS_STEP_OPEN_2 ='开';
var tabpanel ;
var form;
//var groupSus__webStudio.instanceInsInfoStore_pub;
//var iinstanceid = -1;


groupSus__webStudio.form;
groupSus__webStudio.getAllSysNameStore;
groupSus__webStudio.getAllVersionStore;
groupSus__webStudio.rightPanel = null;

groupSus__webStudio.curSysNam = null;
groupSus__webStudio.iinstanceid = -1;

groupSus__webStudio.versionInfo = null;
groupSus__webStudio._isystype = null;

groupSus__webStudio.sysNameMap = new Ext.util.HashMap ();
groupSus__webStudio.versionMap = new Ext.util.HashMap ();
groupSus__webStudio.butterflyVerion =null;
groupSus__webStudio.submitButton =null;
groupSus__webStudio.showSingleRollbackObj=null;
groupSus__webStudio.arrEvnids;
groupSus__webStudio.instance_env_info_gridNew;
groupSus__webStudio.tabPanelForICFS;
groupSus__webStudio.envGrid=null;
groupSus__webStudio.ipStore=null;
groupSus__webStudio.ipMap = new Ext.util.HashMap();
groupSus__webStudio.flowcCfgHostMap = new Ext.util.HashMap();
groupSus__webStudio.newFlowcCfgHostMap = new Ext.util.HashMap();
groupSus__webStudio.autoFlag=false;//自动选择服务器数据标识
groupSus__webStudio.instanceEnvInfoStoreNe=null;
groupSus__webStudio.swichFlag=false;
groupSus__webStudio.timingBox=null;
groupSus__webStudio.getInstNameStore;
groupSus__webStudio.isWarning = 0;  //是否告警值 0:不告警,-1:告警
groupSus__webStudio.warningBox=null;  //是否告警复选框

groupSus__webStudio.iWStore=null;      //告警压制步骤 store
groupSus__webStudio.iwGrid=null;  	   //告警压制步骤
groupSus__webStudio.data_iwActArr=[];  //告警压制_“告警压制步骤”列的下拉选
groupSus__webStudio.iwSelModel=null;
groupSus__webStudio.IPSSelModel=null;
groupSus__webStudio.iwCellEditing=null;
groupSus__webStudio.iwComboxStore=null;
groupSus__webStudio.iwComboxStoreMap = new Ext.util.HashMap ();
groupSus__webStudio.iwResStore =null;
groupSus__webStudio.iwHostStore =null;
groupSus__webStudio.iwform_instName_input=null;
groupSus__webStudio.auditorStoreValidate=null;
groupSus__webStudio.auditorComBoxValidate=null;
groupSus__webStudio.IPMPOrderNumberComBox ;//获取IPMP对应单号
groupSus__webStudio.instanceInsInfoStore_pub;
groupSus__webStudio.curIwRecord=null;
groupSus__webStudio.selectIdx=0;		   //selectIdx
groupSus__webStudio.IPSStore=null;		   //告警压制，二级弹出
groupSus__webStudio.IPSGrid=null;  	   	   //告警压制，二级弹出
groupSus__webStudio.selectIdxIPS=0;
groupSus__webStudio.curIwRecordIPS=null;
groupSus__webStudio.tabPan_POP =null;

groupSus__webStudio.ITILGDItaskId = null;
groupSus__webStudio.ITILGDEAPSId = null;
groupSus__webStudio.ITILBusNumExist = false;


groupSus__webStudio.curIWStore=null;
groupSus__webStudio.curIWGrid=null;
groupSus__webStudio.tabItems = [];
groupSus__webStudio._iwtitle =null;
groupSus__webStudio.iwresMap = new Ext.util.HashMap();
groupSus__webStudio.skepAllUTBox=null;//是否忽略所有人工步骤
groupSus__webStudio.skepallut=-1;//是否忽略所有人工步骤 0忽略 -1不忽略
groupSus__webStudio.iwranjsondata;
groupSus__webStudio.popWindow;


Ext.onReady(function() {
	
	
	var save_parent_but = Ext.getCmp('act_config').down('toolbar[name=toolbar_act_config_lyq]').down('button[name=saveObj_act]');
	


	var superTab = Ext.getCmp(parentId);
	var obj = parent.GRAPHS[currentEDITOR].currentCell.obj;
	
	// 组织当前页的grid数据，先组织数据，可以在后续生成grid对象的时候，直接使用。
	var datas = superTab.init_datas_s(obj);
	
	
		Ext.define('resourceGroupModel', {
		extend : 'Ext.data.Model',
		fields : [{
		  name : 'name',
		  type : 'string'
		},{
		  name : 'iid',
		  type : 'long'
		}]
	  });
	/** 告警压制步骤 信息列表model* */
	Ext.define('IPSModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iip',
			type : 'string'
		}]
	});
	
	
	Ext.define ('instanceInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'iinstanceId',
	                type : 'long'
	            },
	            {
	                name : 'iinstanceName',
	                type : 'string'
	            },
	            {
	                name : 'iserner',
	                type : 'long'
	            },
	            {
	                name : 'iconner',
	                type : 'long'
	            },
	            {
	                name : 'iconnerName',
	                type : 'string'
	            },
	            {
	                name : 'iprener',
	                type : 'string'
	            },
	            {
	                name : 'iactName',
	                type : 'string'
	            },
	            {
	                name : 'iactDes',
	                type : 'string'
	            },
	            {
	                name : 'iactType',
	                type : 'string'
	            },
	            {
	                name : 'iremInfo',
	                type : 'string'
	            },
	            {
	                name : 'ipName',
	                type : 'string'
	            },
	            {
	                name : 'imodelType',
	                type : 'string'
	            },
	            {
	                name : 'iip',
	                type : 'string'
	            },
	            {
	                name : 'iprot',
	                type : 'int'
	            },
	            {
	                name : 'isysType',
	                type : 'string'
	            },
	            {
	                name : 'iexecUser',
	                type : 'string'
	            },
	            {
	                name : 'ishellScript',
	                type : 'string'
	            },
	            {
	                name : 'iisloadenv',
	                type : 'string'
	            },
	            {
	                name : 'ishellPath',
	                type : 'string'
	            },
	            {
	                name : 'itimeOut',
	                type : 'long'
	            },
	            {
	                name : 'iparameter',
	                type : 'string'
	            },
	            {
	                name : 'iexpeceInfo',
	                type : 'string'
	            },
	            {
	                name : 'iexceptinfo',
	                type : 'string'
	            },
	            {
	                name : 'iredoable',
	                type : 'string'
	            },
	            {
	                name : 'iisDisable',
	                type : 'boolean'
	            },
	            {
	                name : 'iprenerString',
	                type : 'string'
	            },
	            {
	                name : 'ipresysname',
	                type : 'string'
	            },
	            {
	                name : 'ipreactname',
	                type : 'string'
	            }, {
	    			name : 'isystemtypestep',
	    			type : 'string'
	    		}, {
	    			name : 'imodeltypestep',
	    			type : 'string'
	    		},
	            {
	                name : 'singleRollback',
	                type : 'string'
	            },
	            {
	                name : 'ihightrisk',
	                type : 'string'
	            },
	            {
	                name : 'ipair',
	                type : 'string'
	            },{
	            	name : 'iconcurrency',
	            	type : 'string'
	            },{
	            	name : 'iintervaltime',
	            	type : 'string'
	            },{
	            	name : 'isRollBack',
	            	type : 'string'
	            }, {
			      name : 'checked',
			      type : 'boolean'
			    }
	    
	    ]
	});
	Ext.define ('iwModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'insert_Type',			   //告警压制类型（关  、开）
	                type : 'string'
		        },
	            {
	                name : 'insert_FronOrPanding',		//在当前步骤前面或者后面插入（0：之前；1：之后）
	                type : 'int'
	            },
	            {
	                name : 'insert_Conner',			    //插入的"并发步骤序号" 阶段好
	                type : 'int'
	            },
	            {
	                name : 'insert_serner',				//插入的"序号"
	                type : 'string'
	            },
	            {
	                name : 'insert_ConnerAndActName',	//插入的步骤名称和步骤名称（用:进行分隔）
	                type : 'string'
	            },
	            {
	                name : 'insert_ResNames',			//插入告警压制资源组名称
	                type : 'string'
	            },
	            {
	                name : 'insert_VersionName',		//插入版本说明
	                type : 'string'
	            },
	            {
	                name : 'insert_CloseStepFlag',		//插入步骤，告警压制 关闭步骤标示
	                type : 'boolean'
	            },
	            {
	            	name : 'iactName',					//步骤名称
	            	type : 'string'
	            },
	            {
	            	name : 'iinstanceId',				//实例id
	            	type : 'long'
	            },
	            {
	            	name : 'iinstanceName',			    //实例名称
	            	type : 'string'
	            },
	            {
	            	name : 'insert_stayParam',			//预留参数
	            	type : 'string'
	            },
	            {
	            	name : 'insert_FronOrPandingStr',	//(str)在当前步骤前面或者后面插入（0：之前；1：之后）之前之后）
	            	type : 'string'
	            },
	            {
	            	name : 'comput_serner',				//用于计算的Soner且排序
	            	type : 'int'
	            },
	            {
	            	name : 'alertGroup',
	            	//defaultValue:'alertGroup#',
	            	type : 'string'
	            },
	            {
	            	name : 'summary',
	            	//defaultValue:'summary#',
	            	type : 'string'
	            },
	            {
	            	name : 'webInterval',
					//defaultValue:'Interval#',
	            	type : 'string'
	            },
	            {
	            	name : 'iip',
	            	type : 'string'
	            },
	            {
	            	name : 'iip_temp',
	            	type : 'string'
	            },
	            {
	            	name : 'iresid',
	            	type : 'string'
	            },
	            {
	            	name : 'ienvid',
	            	type : 'string'
	            },
	            {
	            	name : 'ienvname',
	            	type : 'string'
	            }
	    ]
	});
	var instanceInsInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    pageSize : 1000,
	    model : 'instanceInfoModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getInstanceInsInfoList.do?systemType=3',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
		groupSus__webStudio.instanceInsInfoStore_pub=instanceInsInfoStore;
	instanceInsInfoStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
		    iidForQuery : groupSus__webStudio.iinstanceid,
		    iisDisable:"0",
		    //					iiactNameForQuery : iactNameForQuery.getValue ().trim (),
		    isysType : 1
		};
		Ext.apply (store.proxy.extraParams, new_params);
	});
		/**告警压制，根据业务系统查询服务器*/
	Ext.define('resHostModel', {
		extend : 'Ext.data.Model',
		fields : [{
		  name : 'key',
		  type : 'long'
		},{
		  name : 'value',
		  type : 'value'
		}]
	  });
	 groupSus__webStudio.iwHostStore = Ext.create('Ext.data.Store', {
		    autoLoad: false,
		    autoDestroy: true,
		    model: 'resHostModel',
		    proxy: {
		      type: 'ajax',
		      url : 'getHostByProject.do',
		      reader: {
		        type: 'json',
		        root: 'dataList',
		        totalProperty: 'totalCount'
		      }
		    }
		  });
	   
	   groupSus__webStudio.iwHostStore.on ('beforeload', function (store, options)
		{
			var _sysName = trim(commandField.getRawValue());
			var new_params =
			{
				prjectName : _sysName
			};
			Ext.apply (store.proxy.extraParams, new_params);
		});
	
	instanceInsInfoStore.on ('load', function (store, options)
		{
			groupSus__webStudio.iwform_instName_input=changeState;
			groupSus__webStudio.data_iwActArr=[];
			groupSus__webStudio.iwHostStore.load();
			groupSus__webStudio.iwComboxStoreMap.clear();
			
			for ( var i = 0; i < instanceInsInfoStore.getCount(); i++) {
		    	var _recoardIW     = instanceInsInfoStore.getAt(i);
				var _iconnerNameIW =_recoardIW.get('iconnerName');
				var _iactNameIW    =_recoardIW.get('iactName');
				
				var _iconnerIW =_recoardIW.get('iconner');
				var _isernerIW    =_recoardIW.get('iserner');
				
				
				
				var tempIW=_iconnerNameIW+":"+_iactNameIW;
				var tempCS=_iconnerIW	 +":"+_isernerIW;
				
				
				var temp_iwActArr=[];	   		//告警压制_“告警压制步骤”列的下拉选
				temp_iwActArr.push(tempIW); 
				temp_iwActArr.push(tempIW);
				
				groupSus__webStudio.data_iwActArr.push(temp_iwActArr);
				groupSus__webStudio.iwComboxStoreMap.add(tempIW,tempCS);
			}
		});
	groupSus__webStudio.IPSStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    pageSize : 2000,
	    model : 'IPSModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : '',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
		Ext.define ('instanceEnvInfoModelNew',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'iname',
	                type : 'string'
	            }
	    ]
	});
	Ext.define('ipModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'ienvid',
			type : 'string'
		}, {
			name : 'ename',
			type : 'string'
		}, {
			name : 'ihostname',
			type : 'string'
		}, {
			name : 'hostiid',
			type : 'string'
		}, {
			name : 'iip',
			type : 'string'
		}, {
			name : 'iport',
			type : 'string'
		}, {
			name : 'igroupid',
			type : 'string'
		}, {
			name : 'iresname',
			type : 'string'
		}, {
			name : 'iappype',
			type : 'string'
		}, {
			name : 'actiid',
			type : 'string'
		}, {
			name : 'iinstanceid',
			type : 'string'
		}, {
			name : 'iinstancename',
			type : 'string'
		}, {
			name : 'iserner',
			type : 'string'
		}, {
			name : 'istepname',
			type : 'string'
		}, {
			name : 'imodelname',
			type : 'string'
		}, {
			name : 'istepname',
			type : 'string'
		}, {
			name : 'imodelInstName',
			type : 'string'
		}, {
			name : 'modelShellid',
			type : 'string'
		}, {
			name : 'modelShellName',
			type : 'string'
		}, {
			name : 'checked',
			type : 'boolean'
		}, {
			name : 'iconnername',
			type : 'string'
		} ]
	});
	var ipColum = [{
		text : '序号',
		xtype : 'rownumberer',
		width : 65
	}, {
		text : 'envid',
		dataIndex : 'ienvid',
		hidden : true,
		width : 70
	}, {
		text : '环境',
		dataIndex : 'ename',
		flex: 2
	}, {
		text : '服务器名称',
		dataIndex : 'ihostname',
		flex: 2/*,
        renderer : function (value, metaData, record)
        {
            return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="onParamViewListener('
            + record.get ('hostiid') + ',' 
            + record.get ('igroupid') + ')">' + value + '</a>' ;
        }*/
	}, {
		text : 'hostiid',
		dataIndex : 'hostiid',
		hidden : true,
		flex: 2
	}, {
		text : 'IP',
		dataIndex : 'iip',
		flex: 2
	}, {
		text : '端口',
		dataIndex : 'iport',
		flex: 2
	}, {
		text : '应用标识',
		dataIndex : 'iappype',
		flex: 2
	}, {
		text : 'resid',
		dataIndex : 'igroupid',
		hidden : true,
		flex: 2
	}, {
		text : '资源组',
		dataIndex : 'iresname',
		flex: 2
	} ];
	 var ipStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		model : 'ipModel',
		proxy : {
			type : 'ajax',
			url : 'webstudio/getStartPrepIps.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	ipStore.on('beforeload', function(store, options) {
		var new_params =
		{
				 sysName:commandField.getValue(),
				 version:changeOdd.getValue(),
				 instName:changeState.getValue(),
				 env:arrEvnids.join(),
				 type:300
		};
		Ext.apply(store.proxy.extraParams, new_params);
	});
	var arrEvnids = [];
	function pushEnvid(){
	var data = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	var envid=null;
	arrEvnids=[];
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
    	arrEvnids.push(envid);
    }
}

		
var sm_OfIp = Ext.create('Ext.selection.CheckboxModel', {	
//	    checkOnly : true
	});
	var ipGrid = Ext.create('Ext.grid.Panel', {
		store : ipStore,
		autoScroll : true,
		border : false,
		columnLines : true,
		forceFit : true,
		columns : ipColum,
		selModel : sm_OfIp,
	});
	
	var	instanceEnvInfoStoreNew = Ext.create ('Ext.data.Store',
	{
//	    autoLoad : true,
	    autoDestroy : true,
	    model : 'instanceEnvInfoModelNew',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'wbstudio/queryEnvironmentForInsatnce.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	instanceEnvInfoStoreNew.on ('beforeload', function (store, options)
	{
		var new_params =
		{
				 sysName:commandField.getValue(),
				 version:changeOdd.getValue(),
				 instName:changeState.getValue(),
				 type:300
		};
		Ext.apply (instanceEnvInfoStoreNew.proxy.extraParams, new_params);
	});
	var instanceEnvInfoColumnsNew = [
	        {
	            text : '序号',
	            width : 65,
	            xtype : 'rownumberer'
	        },
	        {
	            text : '环境ID',
	            dataIndex : 'iid',
	            flex : 1,
	            hidden : true
	        },
	        {
	            text : '环境名',
	            dataIndex : 'iname',
	            flex : 1
	        }
	];
	var instance_env_info_gridNew = Ext.create ('Ext.grid.Panel',
	{
		cls:'customize_panel_back',
	    store : instanceEnvInfoStoreNew,
	    border : false,
	    columnLines : true,
	    columns : instanceEnvInfoColumnsNew,
        selModel:new Ext.create('Ext.selection.CheckboxModel', {}),
		dockedItems : [ {
			xtype : 'toolbar',
			items : [ {
				itemId : 'save',
				name:'changeEnvBtn',
				text : '切换环境',
				cls : 'Common_Btn',
				handler : function() {
//					swichFlag=true;
					swtichEnv();
				}
			} ]
		} ]
	});
	 groupSus__webStudio.envGrid=instance_env_info_gridNew;
	
	/**系统名称下拉选模型 */
	Ext.define('systemtModelName', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'systemName',
	      type : 'string'
		  }]
	  });
	
	/**系统名称store */
	 var systemStore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'systemtModelName',
			proxy : {
				type : 'ajax',
				url : 'getAllSystemName.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProterty : 'total'
				}
			}
		});
	
	// 系统名称
	var commandField = Ext.create ('Ext.form.field.ComboBox',{
		name:'Sysname',
		fieldLabel : '系统名称',
		labelWidth : 100,
		store : systemStore,
		displayField: 'systemName',
        valueField: 'systemName',
		labelAlign : 'left',
		listeners :
			    {
			        select : function ()
			        {
				        changeOdd.clearValue ();
				        if(null!=this.getRawValue())
				        var curSysNam=this.getRawValue();
				        changeOrderStore.getProxy ().setExtraParam ("sysnam", curSysNam);
				        changeOrderStore.load ();
//				        changeState.setValue('');
			        },
			         change : function ()
			        {
				        changeOdd.clearValue ();
				        if(null!=this.getRawValue())
				        var curSysNam=this.getRawValue();
				        changeOrderStore.getProxy ().setExtraParam ("sysnam", curSysNam);
				        changeOrderStore.load ();
//				        changeState.setValue('');
			        },
			        beforequery : function (e)
			        {
				        var combo = e.combo;
				        if (!e.forceAll)
				        {
					        var value = e.query;
					        combo.store.filterBy (function (record, id)
					        {
						        var text = record.get (combo.displayField);
						        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					        });
					        combo.expand ();
					        return false;
				        }
			        }
			    }
	});
	
	/**变更单号下拉选模型 */
	Ext.define('changeOddModelName', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'changeOdd',
	      type : 'string'
		  }]
	  });
	
	 /**变更单号store */
	 var changeOrderStore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'changeOddModelName',
			proxy : {
				type : 'ajax',
				url : 'getAllChangeOdd.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProterty : 'total'
				}
			}
		});
	
	//变更单号
	var changeOdd = Ext.create('Ext.form.field.ComboBox', {
		name:'Versionalias',
		fieldLabel : '变更单号',
		labelWidth : 100,
		store : changeOrderStore,
		displayField: 'changeOdd',
        valueField: 'changeOdd',
		labelAlign : 'left',
		listeners :
			    {
			        select : function (index)
			        {
//						changeState.clearValue ();
						var sysName = commandField.getValue ();
				        if(null!=this.getRawValue())
				        var state=this.getRawValue();
				        changeStateStore.getProxy ().setExtraParam ("sysnam", sysName);
 						changeStateStore.getProxy ().setExtraParam ("state", state);
			        	changeStateStore.load();
				        //changeState.setValue('');
			        },
			        change : function (index)
			        {
//						changeState.clearValue ();
						var sysName = commandField.getValue ();
				        if(null!=this.getRawValue())
				        var state=this.getRawValue();
				        changeStateStore.getProxy ().setExtraParam ("sysnam", sysName);
 						changeStateStore.getProxy ().setExtraParam ("state", state);
			        	changeStateStore.load();
				        //changeState.setValue('');
			        },
			        beforequery : function (e)
			        {
				        var combo = e.combo;
				        if (!e.forceAll)
				        {
					        var value = e.query;
					        combo.store.filterBy (function (record, id)
					        {
						        var text = record.get (combo.displayField);
						        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					        });
					        combo.expand ();
					        return false;
				        }
			        }
			    }
	});
	
	/**变更说明下拉选模型 */
	Ext.define('changeStateModelName', {
	    extend : 'Ext.data.Model',
	    fields : [{
		 name : 'iId',
	      type : 'string'
	},{
	      name : 'iversionInfo',
	      type : 'string'
		  }]
	  });
	
	/**变更说明store */
	 var changeStateStore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'changeStateModelName',
			proxy : {
				type : 'ajax',
				url : 'getAllChangeState.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProterty : 'total'
				}
			}
		});
	var biangengIID = Ext.create('Ext.form.field.Text', {
		hidden:true,
		name:'biangengIID',
		listeners:{
			change:function(a,b,c){
				if(b != ''){
					groupSus__webStudio.iinstanceid = b
				}
			}
		}
	})
	var changeState = Ext.create('Ext.form.field.ComboBox', {
		name:'Sysinfo',
		fieldLabel : '变更说明',
		labelWidth : 100,
		store : changeStateStore,
		displayField: 'iversionInfo',
        valueField: 'iversionInfo',
		labelAlign : 'left',
		listeners:{
			select:function(combo, records){
			
				groupSus__webStudio.iinstanceid = records[0].get('iId');
				instanceInsInfoStore.load();
				biangengIID.setValue(records[0].get('iId'))
				instanceEnvInfoStoreNew.load({
					callback:function(a,b,c){
						ipGridLoad(0);
						instance_env_info_gridNew.getSelectionModel().selectAll(true);
					}
				})
			}
		}
	});

	var formPanel = Ext.create('Ext.form.Panel',
			{
//				region : "north",
//				height:'30%',
				border : false,
				fieldDefaults : {
					labelAlign : 'right',
					width : 600,
					labelWidth : 60
				},
//				width : 560,
				buttonAlign : 'center',
				items : [ commandField, changeOdd, changeState,biangengIID,{xtype:'textfield',hidden:true,name:'loginName',value:loginName}]
			});
	
	// 使用自定义的panel，专门用于接口表格界面的
	var modelName = 'paramModel';
	Ext.define(modelName, {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'param',
			type : 'string'
		}, {
			name : 'iid',
			type : 'string'
		}]
	});
	
	var  columns = [
        { text: '参数列表',	dataIndex: 'param' ,		editor : { allowBlank : true} , flex: 1 },
        { text: '隐藏主键',	dataIndex: 'iid' ,	hidden : true	}
    ];
	
	var oper_store = Ext.create('Ext.data.Store', {
	    fields:['param','iid'],
	    data: datas
	});
	
	var grid_panel = {
			xtype : 'iadaptergrid',
			modelName : modelName,
			store : oper_store,
		    columns : columns
	};
	 tabpanel = Ext.create('Ext.tab.Panel', {
    items: [{
        title: '选择环境',
        layout:'fit',
        items:[instance_env_info_gridNew]
    }, {
        title: '服务器',
        layout:'fit',
        items:[ipGrid]
       
    }]
});
form = new Ext.form.FormPanel (
	{
		cls:'customize_panel_back panel_space_bottom',
		region : 'north',
	    width : contentPanel.getWidth () - 2,
	    frame : true,
	    defaultType : 'textfield',
	    defaults :
	    {
		    anchor : '99%'
	    },
	    items : [
	
	    ]
	});
var mainPanel = new Ext.create('Ext.panel.Panel', {
	region : "center",
    layout:'anchor',
    items:[formPanel,tabpanel],
});

	function swtichEnv(doNotFlshFlag){
		var data = instance_env_info_gridNew.getSelectionModel().getSelection();
		var envid=null;
		var tempEnvArr=[];
	    for(var i=0;i<data.length;i++){
	    	envid =data[i].get('iid');
	    	tempEnvArr.push(envid);
	    }
	    
	    
		if (tempEnvArr.length == 0 && doNotFlshFlag!=1) {
			Ext.MessageBox.alert("提示信息", "至少选择一个环境再进行切换");
		} else {
			 if(doNotFlshFlag!=1){
			        instanceEnvInfoStoreNew.load (
			        		{
			        			callback:function(){
			        				var checkAr=[];
			        				for ( var k = 0; k < instanceEnvInfoStoreNew.getCount(); k++) {
			        					var record =instanceEnvInfoStoreNew.getAt(k);
			        					var _iid = record.data.iid;
			        					
			        					
			        					for(var z=0;z<tempEnvArr.length;z++){
			        						if(tempEnvArr[z] == _iid){
			        							groupSus__webStudio.envGrid.getSelectionModel().select(record,true);
			        						}
			        					}
			        				}
		        					
			        			}
			        	    }
			        );				 
			 }
			 var repush=2;
			 if(doNotFlshFlag==1){
				 repush=1;
			 }
			 ipGridLoad(repush);
		}
	
	}
	function ipGridLoad(flag){
	if(2==flag){
		pushEnvid();		
	}else{
		arrEvnids = [];
		for ( var k = 0; k < instanceEnvInfoStoreNew.getCount(); k++) {
			var record =instanceEnvInfoStoreNew.getAt(k);
			var _iid = record.data.iid;
			arrEvnids.push(_iid);
		}
	}

	ipStore.load({
		callback:function(a,b,c){
			if(c){
			   var at=tabpanel.getActiveTab();
				tabpanel.setActiveTab(1);
ipGrid.getSelectionModel().selectAll(true);
tabpanel.setActiveTab(at);
			}
		}
	});

}
function checkIpList(){
	var iprows = ipGrid.getSelectionModel().getSelection();
	if(iprows&&iprows.length==0){
		Ext.MessageBox.alert ("提示", "请选择服务器");
		return false;
	}
	for(var i=0;i<iprows.length;i++){
		var iips = iprows[i].get("iip");
		if(iips==null||iips==""){
			Ext.MessageBox.alert ("提示", "服务器IP不能为空!");
			return false;
		}
		/*if(!isIP(iips)){
			Ext.MessageBox.alert ("提示", "服务器IP格式错误!");
			return false;
		}*/
	}
	var envrows = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	if(envrows.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return false;
	}
	for(var i=0;i<envrows.length;i++){
		var evnId = envrows[i].get("iid");
		var evnName = envrows[i].get("iname");
		var ipcount = 0;
		for(var j=0;j<iprows.length;j++){
			var ip_envid = iprows[j].get('ienvid');
			if(ip_envid == evnId){
				ipcount++;
			}
		}
		if(ipcount==0){
			Ext.MessageBox.alert ("提示", "环境"+evnName+"下未选择服务器!");
			return false;
		}
	}
	return true;
}
function popIwStep(button_save){
	groupSus__webStudio.iwresMap = new Ext.util.HashMap();	
	groupSus__webStudio.tabItems = [];
	
	
	//获取选择的环境id
	var data = instance_env_info_gridNew.getSelectionModel().getSelection();
	var vv_envid=null;
	var vv_iname=null;	
	
    for(var i=0;i<data.length;i++){
    	vv_envid =data[i].get('iid');
		vv_iname=data[i].get('iname');
		
		
		//告警压制步骤 store
		var f_iWStore = Ext.create ('Ext.data.Store',
		{
		    autoLoad : false,
		    autoDestroy : true,
		    pageSize : 25,
		    model : 'iwModel',
		    proxy :
		    {
		        type : 'ajax',
		        url : 'queryIWModel.do',
		        reader :
		        {
		            type : 'json',
		            root : 'dataList'
		        }
		    }
		});
		f_iWStore.on ('beforeload', function (store)
		{
			
			var new_params =
			{
				isysName : commandField.getRawValue(),  
        		instName_input : changeState.rawValue,
        		iversion : changeOdd.getValue(),
				instanceId : iinstanceid,
				ienvid:getEnvNameByEnvid(groupSus__webStudio.iwGrid.initialConfig.title)  //当前title，即环境名
			};
			Ext.apply (store.proxy.extraParams, new_params);
		});
		
		f_iWStore.on ('load', function (store, options)
		{
			for(var bb=0;bb<f_iWStore.getCount();bb++){
				
				var _tempIwRecord=f_iWStore.getAt(bb);
				var tempIp=_tempIwRecord.get('iip')
				var iipArr=tempIp.split(',');
				
				var iresid=_tempIwRecord.get('iresid');
				var allIP=getIWGoupIpsResid(iresid);
				var allIpArr=[];
				if(null!=allIP){
					allIpArr=allIP.split(',');
				}
				

				if(null!=iipArr && iipArr.length>0){
					var tempValForShow='';  			//表格显示
					var _tIdx=0;
					for(var i=0;i<iipArr.length;i++){
						
						for(var a=0;a<allIpArr.length;a++){
							var curAllIp=allIpArr[a];
							
							if(curAllIp ==iipArr[i]  ){  //如果该IP存在于
								
								if(_tIdx==0){
									tempValForShow+=iipArr[i];
								}else{
									tempValForShow+=',' + iipArr[i];
								}
								_tIdx++;
								break;
							}
						}									
					}
				 _tempIwRecord.set('iip', tempValForShow);
        	  	 _tempIwRecord.commit();
				}				

			}
		});
					
	    var f_iwSelModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			listeners : {
				selectionchange : function(sm, selections) {
				}
			}
		});
		  
		var f_iwCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
			clicksToEdit : 2
		});
		
		
		var f_IPSSelModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			listeners : {
				selectionchange : function(sm, selections) {
				}
			}
		});
		
		IPSSelModel = Ext.create('Ext.selection.CheckboxModel', {
			checkOnly : true,
			listeners : {
				selectionchange : function(sm, selections) {
				}
			}
		});
		
			   //告警压制 资源组列表
	  var  f_iwResStore = Ext.create('Ext.data.Store', {
		    autoLoad: false,
		    autoDestroy: false,
		    model: 'resourceGroupModel',
		    proxy: {
		      type: 'ajax',
		      url : 'getResNameByProject.do',
		      reader: {
		        type: 'json',
		        root: 'dataList',
		        totalProperty: 'totalCount'
		      }
		    }
		  });
	   
	   f_iwResStore.on ('beforeload', function (store)
		{
			var _sysName = trim(commandField.getRawValue());
			var new_params =
			{
				prjectName : _sysName,
				curenvname:groupSus__webStudio.iwGrid.initialConfig.title //当前title，即环境名
			};
			Ext.apply (store.proxy.extraParams, new_params);
		});
		
		var tempGridPanel  = Ext.create('Ext.grid.Panel', {
			    title:vv_iname,
				columnLines : true,
				store : f_iWStore,
				selModel: f_iwSelModel,
				columns : [
				{
					text : '告警压制类型',						//告警压制类型（0：关闭； 1:开启）
					width : 100,
					dataIndex : 'insert_Type'/*,
					editor : new Ext.form.field.ComboBox(
					{
						editable : false,
						store : [
						         [ '关', '关' ],
								 [ '开','开' ]
						        ]
					})*/
	
				},
				{
					text : '资源组',					  		//插入的步骤名称和步骤名称（用:进行分隔）
					dataIndex : 'insert_ResNames',
					flex : 1,
		              editor : new Ext.form.field.ComboBox (
		            		{
		            			triggerAction : 'all',		// 用all表示把下拉框列表框的列表值全部显示出来
		            	        editable : false,			// 是否可输入编辑
		            	        store : f_iwResStore,
		            	        queryMode : 'local',
		            	        displayField : 'name',
		            	        valueField : 'name',
		            		    multiSelect : true,//控制搜索框多选 默认false(单选)
		            	        listeners:{
		            	        	blur:function(combo,record,opts) {
		            	        		if(null!=combo)
		            	        		{
		            	        			var _rawValue=combo.rawValue;
		            	        			if(null!=_rawValue && ''!=_rawValue){
		            	        				var resArr=_rawValue.split(",");
		            	        				
		            	        				if(null!=resArr && resArr.length!=0 ){
		            	        					var sFindFlag=false;
		            	        					for(var a=0;a<resArr.length;a++){
		            	        						var ves1=resArr[a];
		            	        						var ps1=null;
		            	        						var _records=null;
		               	        						for(var b=0; b <groupSus__webStudio.iwResStore.getCount();b++){
		            	        							_records=groupSus__webStudio.iwResStore.getAt(b);
		            	        							ps1=_records.data.name;
		            	        							if(ps1==ves1){
		            	        								sFindFlag=true;
		            	        								break;
		            	        							}
		            	        							
		            	        						}
		               	        						
		            	        						if(false == sFindFlag){
		            	        							Ext.Msg.alert ('提示', ves1+":该资源组不存在", function (){
		            	        							});
		            	        					
		            	        						}
		            	        					}
		            	        				}
		            	        			}
											
											//record记录的保存
											{
												if(null!=groupSus__webStudio.curIwRecord){
													var recordArr =combo.lastSelection;
													var tempIIP=getIWGoupIps(recordArr);
													var iresid=getIWGoupPrimaryKey(recordArr);
													groupSus__webStudio.iWStore.getAt(selectIdx).set('iip',tempIIP);
													groupSus__webStudio.iWStore.getAt(selectIdx).set('iip_temp',tempIIP);
													groupSus__webStudio.iWStore.getAt(selectIdx).set('iresid',iresid);
												}
											}
										
		            	                }
		            	        	},
									
									//行方人员可能单击del对render内容进行清空，压制IP列随之清空。防止他们操作，屏蔽操作。
									keyup:function(combo,record,opts) {
										return;
									},
									keydown:function(combo,record,opts) {
										return;
									},
									keypress:function(combo,record,opts) {
										return;
									}
		            	        }  
		            		})
				}, {
					text : '压制IP',
					dataIndex : 'iip',//插入的步骤名称和步骤名称（用:进行分隔）
					flex : 1,
				    renderer : function (value, metaData, record, colIndex, store, view)
	            	{
						//value:是用户上次告警压制选中的记录。本次要选择一下
						//allIP:是根据资源组ip查询出全部IP，显示在面板上。
						
						var iresid=record.get('iresid');
						var allIP=getIWGoupIpsResid(iresid);
						var allIpArr=[];
						if(null!=allIP){
							allIpArr=allIP.split(',');
						}
						
						
						//显示：将上次用户选择的 告警IP 显示在表格上, (由于存在IP被更改情况，在allIpArr中做比较，把不存在IP移除)
						if(null!=value){
							var iipArr=value.split(',');
							if(null!=iipArr && iipArr.length>0){
								var tempVal='';			//鼠标悬浮值
								var tempValForShow='';  //表格显示
								var _tIdx=0;
								for(var i=0;i<iipArr.length;i++){
									if(i==0){
										tempVal+='【'+(i+1)+'】'+(iipArr[i]);
									}else{
										tempVal+=(',' + '</br>' +  '【' + (i+1)+ '】' +iipArr[i]);
									}
								}								
								metaData.tdAttr = 'data-qtip="' + tempVal + '"';
							}
						}else{
		                	metaData.tdAttr = 'data-qtip="' + value + '"';
						}
						
						
	
						var ips_defualtSeleced=value; //弹出窗口后，默认选中的IP
						
						
			           return '<a href="javascript:void(0);"   onclick="popSetIPWin(\'' +ips_defualtSeleced+"','"+ allIP+'\')">' + value + '</a>';
	                 }
				}, {
					text : '告警压制步骤',
					dataIndex : 'insert_ConnerAndActName',//插入的步骤名称和步骤名称（用:进行分隔）
					flex : 1,
					editor : new Ext.form.field.ComboBox(
					{
						editable : false,
						store : groupSus__webStudio.data_iwActArr
					})
				}, 
				{
					text : '告警压制步骤前后',					//在当前步骤前面或者后面插入（0：之前；1：之后）
					dataIndex : 'insert_FronOrPandingStr',
					width : 120,
					editor : new Ext.form.field.ComboBox(
						{
							editable : false,
							store : [
							         [ '前', '前' ],
									 [ '后','后' ]
							        ]
						})
				},{
					text : '告警组',
					dataIndex : 'alertGroup',
					flex : 1,
					editor : {
						allowBlank : true
					}
				},
				{
					text : '告警内容',
					dataIndex : 'summary',
				    flex : 1,
					editor : {
						allowBlank : true
					}
				},
				{
					text : '告警时间(分钟)',
					dataIndex : 'webInterval',
					flex : 1,
					editor : {
						allowBlank : true
					}
				}, 
				{
					text : '预留参数',
					dataIndex : 'insert_stayParam',
					flex : 1,
					editor : {
						allowBlank : true
					}
				} ],
				region : 'center',
				emptyText : '没有数据',
				loadMask : true,
				border : false,
				plugins: [ f_iwCellEditing ],
				listeners: {
			        itemclick: function(dv, record, item, index, e) {
						selectIdx=index;
						groupSus__webStudio.curIwRecord=record;
			        }
			    },
				dockedItems : [ {
					xtype : 'toolbar',
					items : [ 
					{
						text : '新增',
						cls:'Common_Btn',
						handler : function() {
							var frontObj=new iwModel();
							frontObj.data.insert_Type=IEAI_SUS_STEP_CLOSE;
							//frontObj.data.insert_Type="关";
							groupSus__webStudio.iWStore.insert(0,frontObj);
							
							
							var pandingObj=new iwModel();
							//pandingObj.data.insert_Type="开";							
							pandingObj.data.insert_Type=IEAI_SUS_STEP_OPEN;						
							groupSus__webStudio.iWStore.insert(1,pandingObj);
				            
							/*
							iwCellEditing.startEditByPosition({
				              row: 0,
				              column: 0
				            });*/
						}
					}, 
					{
						text : '保存',
						cls:'Common_Btn',
						handler : function() {
					        var rsValidateWarnStep=validateWarnStepJson_loop();
									if(true==rsValidateWarnStep){
										iwWindow.hide();
										iwWindow.savesign_l=true;
										button_save.handler();
									}
						}
					},
					{
						text : '删除',
						cls:'Common_Btn',
						handler : function() {
							var data = groupSus__webStudio.iwGrid.getView ().getSelectionModel ().getSelection ();
							if (data.length == 0)
							{
								Ext.Msg.alert ('提示', '请先选择您要操作的行!');
								return;
							}
							else
							{
								Ext.Msg.confirm ("请确认", "是否真的要删除数据？", function (button, text)
								{
									if (button == "yes")
									{
										Ext.Array.each (data, function (record)
												{
											groupSus__webStudio.iWStore.remove(record);
												});
									}
								});
							}
						}
					},
					{
						text : '显示上次配置',
						cls:'Common_Btn',
						handler : function() {
							groupSus__webStudio.iWStore.load();
						}
					} ]
				} ]
			});
			groupSus__webStudio.tabItems.push(tempGridPanel);
    }
	

	//告警压制
	tabPan_POP = Ext.create ('Ext.tab.Panel',
	{
		cls:'customize_panel_back',
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : 0,
	    border : false,
	    // plain : true,
	    defaults :
	    {
		    autoScroll : true
	    },
	    items : groupSus__webStudio.tabItems,
	    listeners:{
            'tabchange':function (t, n) {
					var curActive=tabPan_POP.getActiveTab();
					
					
					groupSus__webStudio.iwGrid=curActive;
					groupSus__webStudio.iWStore=groupSus__webStudio.iwGrid.store;
					
					groupSus__webStudio.iwResStore=getGridPanelColumsStore(groupSus__webStudio.iwGrid);
					loadIresStore(groupSus__webStudio.iwResStore);//加载资源组combo
					console.log(groupSus__webStudio.iwResStore.getCount())
            }
        }
	});
	
	if(null!=groupSus__webStudio.tabItems && groupSus__webStudio.tabItems.length>0){
		groupSus__webStudio.iwGrid=groupSus__webStudio.tabItems[0];
		groupSus__webStudio.iWStore=groupSus__webStudio.iwGrid.store;
		
		groupSus__webStudio.iwResStore=getGridPanelColumsStore(groupSus__webStudio.iwGrid);
		loadIresStore(groupSus__webStudio.iwResStore);//加载资源组combo
	}
	
	
	
	
		 var iwSaveButton = Ext.create("Ext.Button",{
			 cls : 'Common_Btn',
			 textAlign : 'center',
		     text : "保存",
		     handler : function(){
			
			}
		 });
		

		var iw_bottomPanel = Ext.create ("Ext.panel.Panel",
		{
			baseCls:'customize_gray_back',
		 	region : 'south',
			    border : false,
			    dockedItems : [
				    {
				    	baseCls:'customize_gray_back',
				        dock : 'bottom',
				        xtype : 'toolbar',
				        items : [
				        	 iwSaveButton
				        ]
				    }
			    ]
			});
	var iwWindow = Ext.create('widget.window', {
		title : '告警压制配置',
		closeAction : 'hide',
		width : 1200,
		height : 600,
		savesign_l:false,
		modal : true,
		layout : 'border',
		items : [tabPan_POP/*,iw_bottomPanel*/]
	});
	
	
	if (iwWindow.isVisible()) {
		iwWindow.hide(this, function() {
		});
	} else {
		iwWindow.show();
	}
	return iwWindow;
}
/****
*@desc 根据资源组，选择IP
*/
function getIWGoupIps(recordArr){
	var iip="";
	if(null!=recordArr){
		for(var i=0;i<recordArr.length;i++){
			var recordR=recordArr[i];
			var tempResid=recordR.data.iid;
			
			var tempIip=getIpByGroupId(tempResid);
			if(""==iip){
				iip=tempIip;
			}else{
				iip += ","+tempIip;
			}
		}
	}
	return iip;
}
/****
*@desc 根据资源组数组，获得资源组id
*/
function getIWGoupPrimaryKey(recordArr){
	var pk="";
	if(null!=recordArr){
		for(var i=0;i<recordArr.length;i++){
			var recordR=recordArr[i];
			var tempResid=recordR.data.iid;
			
			if(""==tempResid||0==i){
				pk=tempResid;
			}else{
				pk += ","+tempResid;
			}
		}
	}
	return pk;
}
function getGridPanelColumsStore(scrGridPanel){
	var columns = scrGridPanel.columns;
          if (columns ) {
              for (var i = 1; i < columns.length; i++) {
                  var column = columns[i];
                  if (column.text=='资源组') {
                      var editor = column.editor;
                      if (editor) {
                          return editor.getStore();
                      }
                  }
              }
          }
}

function loadIresStore(iwResStore){			
	var tempenvid=getEnvNameByEnvid(groupSus__webStudio.iwGrid.initialConfig.title) ;
	var tempVaL =groupSus__webStudio.iwresMap.get(tempenvid);
	if(null==tempVaL){
		groupSus__webStudio.iwresMap.add(tempenvid,iwResStore);
		iwResStore.load();
	}
}


function getEnvNameByEnvid(envname){
	var data = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	var envid=-1;
	var rs_envid=-1;
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
		var _tempIname =data[i].get('iname');
		
		if(_tempIname == envname){
			rs_envid=envid;
			break;
		}
    }
	return rs_envid;
}

function pushEnvid(){
	var data = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	var envid=null;
	arrEvnids=[];
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
    	arrEvnids.push(envid);
    }
}
	
	superTab.add(mainPanel);
	
	
	superTab.childSave = function() {
		groupSus__webStudio.instanceInsInfoStore_pub.load()
		if(checkIpList()){
			if(groupSus__webStudio.popWindow){
				if(groupSus__webStudio.popWindow.savesign_l){
					var fff = getWarnStepJson_Loop();
					return superTab.callsusAndAz(formPanel,instance_env_info_gridNew.getStore(),ipGrid.getStore(),instance_env_info_gridNew.getSelectionModel().getSelection(),ipGrid.getSelectionModel().getSelection(),fff);
				}else{
					groupSus__webStudio.popWindow.show();
					return 0;
				}
			}else{
				groupSus__webStudio.popWindow=popIwStep(save_parent_but);
				return 0;
			}
		}
	}
	
	superTab.init_form_data(obj,parentId,formPanel);
	superTab.init_tab_datas(datas,tabpanel);
});

function openResourceGroupWindow(groupName){
	configwindowFlow = Ext.create ('Ext.window.Window',
			{
			    title : '资源组绑定服务器列表',
			    autoScroll : true,
			    modal : true,
			    closeAction : 'destroy',
			    buttonAlign : 'center',
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
			    width : contentPanel.getWidth ()/2,
			    height : contentPanel.getHeight ()/2,
			    loader :
			    {
			        url : "resourceServer/getResourceGroupByName.do",
			        params :
			        {
			        	groupName : groupName
			        }, 
			        autoLoad : true,
			        autoDestroy : true,
			        scripts : true
			    }
			}).show ();
}
function openResourceGroupWindowNew (istepId,iinstanceId)
{
	configwindowFlow = Ext.create ('Ext.window.Window',
	{
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/sus/flowstart/servers_config.jsp",
	        params :
		    {
	        	istepId:istepId,
	        	vinstanceId:iinstanceId
		    },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function flowGlobalShowfun(){
	if(instanceEnvInfoStoreNew.data.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return;
	}
	var env_rows = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	if(env_rows.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return false;
	}
	var envids = "";
	for(var i=0;i<env_rows.length;i++){
		envids += env_rows[i].get("iid")+",";
	}
	if(envids.length>0){
		envids=envids.substring(0,envids.length-1);
	}
	var ipIds = "";
	var ip_rows = ipGrid.getSelectionModel().getSelection();
	for(var j=0;j<ip_rows.length;j++){
		var ipstr =ip_rows[j].get("ienvid")+"||"+ip_rows[j].get("hostiid")+"||"+ip_rows[j].get("igroupid");
		if(ipIds.indexOf(ipstr)<0){
		    ipIds += ipstr+",";
		}
	}
	var ipList = "";
	for(var j=0;j<ip_rows.length;j++){
		if(j==0){
			ipList = ipList + ip_rows[j].get("hostiid");
		}else{
			ipList = ipList + "," + ip_rows[j].get("hostiid");
		}
	}
	if(ipIds.length>0){
		ipIds=ipIds.substring(0,ipIds.length-1);
	}
	configwindowFlow = Ext.create ('Ext.window.Window',
			{
			    title : '全局预览',
			    autoScroll : true,
			    modal : true,
			    closeAction : 'destroy',
			    buttonAlign : 'center',
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
			    width : contentPanel.getWidth (),
			    height : contentPanel.getHeight (),
			    loader :
			    {
			        url : "flowGlobalShow.do",
			        params :
			        {
			        	instanceId:iinstanceid,
			        	iidForQuery:iinstanceid,
			        	envids:envids,
			        	ipIds:ipIds,
			        	sysType:3,
			        	ipList:ipList
			        }, 
			        autoLoad : true,
			        autoDestroy : true,
			        scripts : true
			    }
			}).show ();
}

var checklistShowWin = "";
function checklistInfoShow(){
	//var ip_flag =checkIpList();
	var starttime=Ext.getCmp('startTime').getValue();
	var _verionInfo=versionInfo.getValue();
	var _sysName = trim(form.getForm ().findField ("form_sysName").getRawValue());
	var form_instName_input = form.getForm ().findField ("form_instName_input").getRawValue();
//	if(!ip_flag){
//		return;
//	}
	if(''==_sysName || null==_sysName){
		Ext.MessageBox.alert ("提示", "请选择系统名称");
		return;
	}else if(''==_verionInfo || null==_verionInfo){
		Ext.MessageBox.alert ("提示", "请选择变更单号");
		return;
	}
	if (trim (form_instName_input) == '' || (null == form_instName_input))
	{
		Ext.MessageBox.alert ("提示", "变更说明不能为空");
		return false;
	}
	
	Ext.define('checklistModel', {
        extend: 'Ext.data.Model',
        fields: [
                 {name: 'checkType',  type: 'string'},
                 {name: 'errorInfo',  type: 'string'},
                 {name: 'itaskid', type:'string'}
                ]
    	});
    	var checklistStore = Ext.create('Ext.data.Store', {
	        autoLoad: false,
	        model: 'checklistModel',
	        proxy: {
	            type: 'ajax',
	            url: 'checklist.do',
	            getMethod: function(){ return 'POST'; },
	            method : 'POST',
	            reader: {
	                type: 'json',
	                root: 'dataList',
	                totalProperty : 'total'
	            }
	        }
	    });
    	pushEnvid();
    	//遍历map，将页面选择的服务器信息后
    	var ipArr =new Array();
    	ipMap.each(function(key, value, length)
    	{
    		ipArr.push(value);
    	});
    	checklistStore.on('beforeload', function(store, options) {
            var new_params = {
            		isysId : iinstanceid,
            		sysName : form.getForm ().findField ("form_sysName").getRawValue(),
            		instName : form.getForm ().findField ("form_instName_input").getRawValue(),
            		version : versionInfo.rawValue,
            		isCheck : true,
        	        isystype : 1,
        	        evnIds:arrEvnids,
        	        prepareStartIpsJson:pars(ipArr)
        	    
            };
            Ext.apply(checklistStore.proxy.extraParams, new_params);
        });
    	
    	var columns = [
    	               {
    	                   text : '序号',
                           width : 200,
                           hidden : true,
                           sortable : false,
    	            	   xtype: 'rownumberer',
    	            	   align : 'left'
    	               },
    	               { 
    		               text: '类型',  
    		               dataIndex: 'checkType',
    		               width:200,
    		               align : 'left'
    		           },
    	    	       { 
    		        	   text: '异常信息', 
    		        	   flex:2,
    		        	   dataIndex: 'errorInfo',
    		        	   align : 'left',
    			  		  	renderer : function(value, p, r) {
    							var isTrue = r.get('errorInfo');
    							var temp = null;
    							if (isTrue) {
    								temp = '<font color="red"><b>'+value+'</b></font>';
    							} else {
    								temp = value;
    							}
    							return temp;
    					   }
    		           }
    	    	      ];
    	
    	var checkListPane = Ext.create('Ext.grid.Panel', {
			columns:columns,
			autoScroll:true,
			height:screen.height - 290,
			width:screen.width,
            border:false,
            dockedItems: [{
	            xtype: 'toolbar'
	        }],
            store: checklistStore
		});
    	
    	var h = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    	var fp1 = new Ext.form.Panel({
    		border : false,
    		id : 'baseinfo_form',
    		height : '100%',
    		readOnly:true,
    		margin:'0 0 100 0',
    		padding :5,
    		fieldDefaults : {
    			labelWidth : 60
    		},
    		defaultType : 'textfield',
    		items : [ {
    			fieldLabel : '脚本检查',
    			labelWidth : 70,
    			readOnly:true,
    			xtype : 'textarea',
    			grow : true,
    			height : h-200,
    			name : 'shellCheckInfo',
    			anchor : '90%'
    		} ]
    	});
    	
    	var itaskid = -1;
    	checklistStore.load({
			callback : function(r, options, success) {
				if (success) {
					checklistStore.each(function (record) {
			    	    if(record.get('itaskid')>0){
			    	    	itaskid = record.get('itaskid');
			    	    }
			    	});
					
					Ext.Ajax.request({
			    		url : 'getShellCheckInfo.do',
			    		method : 'POST',
			    		timeout: 100000000,
			    		params : {
			    			isysId : iinstanceid,
			        		sysName : form.getForm ().findField ("form_sysName").getRawValue(),
			        		instName : form.getForm ().findField ("form_instName_input").getRawValue(),
			        		version : versionInfo.rawValue,
			        		isCheck : true,
			        		itaskid : itaskid
			    		},
			    		success : function(response, opts) {
			    			var msg =Ext.decode ( response.responseText);
			    			var json={
			    					shellCheckInfo:msg.shellCheckInfo
			    			};
			    			Ext.getCmp('baseinfo_form').getForm().setValues(json);
			    		},
			    		failure : function(response, opts) {
			    			Ext.Msg.alert('提示', '获取信息失败！');
			    		}
			    	});
				}
			}
    	});

    	
    	checklistShowWin = Ext.create ('Ext.window.Window',
    		    {
    		        title : 'checklist检查信息',
    		        autoScroll : true,
    		        modal : true,
    		        closeAction : 'destroy',
    		        buttonAlign : 'center',
    		        draggable : true,// 禁止拖动
    		        resizable : false,// 禁止缩放
    		        width : 1200,
    		        height : screen.height - 100,
    		        items:[checkListPane,fp1,{title:'人工检查信息：',html:'<div><table border=\"1\" cellspacing=\"0\" cellpadding=\"0\" width=\"800\" height=\"400\" style="margin:0 0 0 85px"><tr><td>项目</td><td>检查细则</td></tr><tr><td>变更可行性评估检查项</td><td>1）平台配置的参数值，是否在纳管机本地存在<br/> 2）执行路径、执行用户是否存在、是否对执行路径有读写、执行权限<br/> 3) 执行脚本，最后是否返回0<br/> 4）评估：开发、测试的执行过程、执行结果是否对生产执行有影响</td></tr><tr><td>打包介质检查项</td><td>1）使用FTP，登录生产投产介质平台10.200.168.226<br/> 2）通过Excel获取“系统编号/简称”和“变更说明”对应的值<br/> 3）检查FTP服务器目录：/devops/“系统编号/简称”/“变更说明”/目录下是否存在与Excle中“程序包名”对应的程序包和MD5文件<br/> 4）打包介质的目录格式（包名、模块名、order.txt），是否符合自动化要求</td></tr></table></div>'}]
    		    }).show ();
}

/**
 * @desc "服务器"Tab页中表格加载数据。
 * @desc Step:
 * 			  (1). Push envids into arr 'arrEvnids'.
 * 			  (2). store.on('beforload')  use the arrEvnids
 * 			  (3). store.load and grid fress.
 * @using:	 
 * 		 	  (1).When js file init call this func.
 * 			  (2).When Click the swich-evn-button change env.
 */
function ipGridLoad(flag){
	if(2==flag){
		pushEnvid();		
	}else{
		arrEvnids = [];
		for ( var k = 0; k < instanceEnvInfoStoreNew.getCount(); k++) {
			var record =instanceEnvInfoStoreNew.getAt(k);
			var _iid = record.data.iid;
			arrEvnids.push(_iid);
		}
	}
//	fillMap();
	fillMapOut();
	ipStore.load();
	preparedStepStore.getProxy().setExtraParam('startInstid',0);
		preparedStepStore.getProxy().setExtraParam('hostiid', 0);
		preparedStepStore.getProxy().setExtraParam('envid', 0);
		preparedStepStore.getProxy().setExtraParam('optTpye', 'excel');
		preparedStepStore.load();
}


function pushEnvid(){
	var data = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	var envid=null;
	arrEvnids=[];
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
    	arrEvnids.push(envid);
    }
}

function fillMapOut(){
	//双人复核初始化
		if(doubleCheckState!=null&&doubleCheckState!='')
			{
			Ext.Ajax.request (
					{
					    url : 'getDoublecheckSusFsIp.do',
					    method : 'POST',
					    params :
					    {
					    	iworkItemid : iworkItemid,
					    	istate:doubleCheckState
					    },
					    success : function (response, opts)
					    {
						    var success = Ext.decode (response.responseText).success;
						    var paramObj=Ext.decode (response.responseText);
						    dsfiList= paramObj.dsfiList;
						    fillMap();
					    }
					});
			}else
				{
				fillMap();
				}
}
/**
 * @desc step2:环境加载后，初始化步骤的使用的ipMap
 */
function fillMap(){
	console.log('loadip envid:'+arrEvnids.length +'');
	
	//waitDialog('正在进行环境信息加载');
    Ext.Ajax.request({
        url : 'getAllStartPrepIps.do',
        method : 'POST',
        params : {
        	startInstid : iid,
        	strEnvid:arrEvnids,
        	optType:'excel'
        },
        success : function(response, request) {
          var success = Ext.decode(response.responseText).success;
          if (success) {
        	  var _dataList=Ext.decode(response.responseText).dataList;
        	  var ipRecd=null;
        	  var _ienvid=null;
        	  var _igroupid=null;
        	  var _hostiid=null;
        	  
        	  var _istepname=null;
        	  var _imodelInstName=null;
        	  var _modelShellName=null;
        	  var sp="&&**";
        	  var key="";
        	  ipMap.clear();//重新装载之前将map清空
        	  if(null!=_dataList){
        		  for(var i=0;i<_dataList.length;i++){
        			  ipRecd =_dataList[i];
        			  _ienvid=ipRecd.ienvid;
        			  _igroupid=ipRecd.igroupid;
                	  _hostiid=ipRecd.hostiid;
                	  
                	  _istepname=ipRecd.istepname;
                	  _imodelInstName=ipRecd.imodelInstName;
                	  _modelShellName=ipRecd.modelShellName;
                	  key= (_ienvid+sp) +(_igroupid+sp) +(_hostiid+sp) +(_istepname+sp) +(_imodelInstName+sp) +(_modelShellName+sp);
                	  //双人复核页面，回显预启动服务器
                		if(doubleCheckState!=null&&doubleCheckState!=''&&initFlag==true)
                			{
                			var checkedFlag=false;
                			$.each(dsfiList, function(keye,value){
  	    	        		  _ienvid2=value.ienvid;
  	            			  _igroupid2=value.igroupid;
  	                    	  _hostiid2=value.ihostiid;
  	                    	  _istepname2=value.stepname==null?"":value.stepname;
  	                    	  _imodelInstName2=value.imodelinstname==null?"":value.imodelinstname;
  	                    	  _modelShellName2=value.modelshellname==null?"":value.modelshellname;
  	                    	  key2= (_ienvid2+sp) +(_igroupid2+sp) +(_hostiid2+sp) +(_istepname2+sp) +(_imodelInstName2+sp) +(_modelShellName2+sp);
  	                    	  if(key==key2)
  	    	        			 {
  	    	        			checkedFlag=true;
  	    	        			 }
  	    	        		 });
                			ipRecd.checked=checkedFlag;
                			}
                	  ipMap.add(key,ipRecd);
        		  }
        	  }
        	  initFlag=false;
        	  console.log('加载环境后，加载该环境下的各个步骤的服务器信息');
        	  ipMap.each(function(key,value,length){
        		  console.log(key);
        	  });
          } else {
            Ext.Msg.alert('提示', '加载预启动服务器失败');
          }
          //msg_hide();
        },
        failure : function(result, request) {
        	  Ext.Msg.alert('提示', '加载预启动服务器，通信失败.'+result);
        	  //msg_hide();
        }
      });
}
function waitDialog(msg) {
    Ext.MessageBox.wait(msg, "进度条");
}

function timedMsg()
{
}
function msg_hide() {
	setTimeout('timedMsg()',1000);
    Ext.MessageBox.hide();
}
/**判断服务器列表书否选中**/			
function checkIpGrid()
{
	for(i=0;i<ipStore.getCount();i++)
	{
	var checkedFlag=false;
	var _ienvid = ipStore.getAt(i).get ('ienvid');
	var _igroupid = ipStore.getAt(i).get ('igroupid');
	var _hostiid = ipStore.getAt(i).get ('hostiid') ;
	var sp="&&**";
	//遍历所有服务器的所有步骤
	ipMap.each(function(key, value, length)
			{
				var keyitem=key.split(sp);
				
				if(keyitem[0]==_ienvid&&keyitem[1]==_igroupid&&keyitem[2]==_hostiid)
				{
					
					  var curRecord=ipMap.get(key);
			    	  var isChecked =curRecord.checked;
			    	  if(isChecked)
			    		  {
			    		  //如果有一个步骤选中了，那么所属服务器就选中
			    		  checkedFlag=true;
			    		  }
					
				}
				
			});
	//将服务器选中
	//if(checkedFlag)
		//{
		//是否自动选择标识，如果自动选中，则不触发级联时间
		autoFlag=true;
		//获取当前激活的tab
		var at=tabPanelForICFS.getActiveTab();
		//将服务器tab激活
		tabPanelForICFS.setActiveTab(hostTab);
		//ipGrid.getSelectionModel().select(records[i]);
		if(checkedFlag)
			{
			ipGrid.getSelectionModel().select(ipStore.getAt(i), checkedFlag);
			}else
				{
				ipGrid.getSelectionModel().deselect(ipStore.getAt(i));
				}
		
		//将之前激活的tab重新激活
		tabPanelForICFS.setActiveTab(at);
		//一秒钟后将自动选择标识重置
		window.setTimeout(function(){ 
			autoFlag=false;
		},1000);
		//}
	
	}
}
/**判断步骤列表书否选中**/			
function checkStepGrid( record,checkedFlagIn)
{
	//非自动选择的情况，触发级联事件
	if(autoFlag==false)
		{
		var sp="&&**";
		ipMap.each(function(key, value, length)
				{
					var keyitem=key.split(sp);
					var _ienvid = record.data.ienvid;
						var _igroupid = record.data.igroupid;
						var _hostiid =record.data.hostiid;
						if(keyitem[0]==_ienvid&&keyitem[1]==_igroupid&&keyitem[2]==_hostiid)
						{
							if(null!=value){
			            		  value.checked=checkedFlagIn;
			            		  ipMap.add(key,value);
			            	  }
						}
				});
		}
	
}
function pars(arrObj) {
	var jsonData = "[";
	for ( var i = 0, len = arrObj.length; i < len; i++) {
		var ss = Ext.JSON.encode(arrObj[i]);
		if (i == 0)
			jsonData = jsonData + ss;
		else
			jsonData = jsonData + "," + ss;
	}
	jsonData = jsonData + "]";
	return jsonData;
}

/** 启动按钮触发方法* */
function startIns (btn)
{
	Ext.MessageBox.buttonText.yes = "确定";
	Ext.MessageBox.buttonText.no = "取消";
	Ext.Ajax.request (
			{
			    url : 'isEmergencyVersion.do',
			    method : 'POST',
			    timeout: 900000,
			    params :
			    {
				    version:versionInfo.rawValue,
				    isCheckEnv:1
			    },
			    success : function (response, options)
			    {
				    var success = Ext.decode (response.responseText).success;
				    if (success)
				    {
				    	Ext.MessageBox.buttonText.yes = "确定";
				    	Ext.MessageBox.buttonText.no = "取消";
				    	Ext.Msg.confirm ("确认执行", "此单号为平台预留紧急变更单号，请确认是否要执行", function (id)
						{
							if (id == 'yes')
							{
								doStartIns(btn)
							}
						});
				    }else
				    {
				    	doStartIns(btn)
				    }
			    },
			    failure : function (result, request)
			    {
				    Ext.Msg.alert ('提示', '启动失败  ! ');
			    }
			});
}
function doStartIns(btn)
{
	var ip_flag =checkIpList();
	var starttime=Ext.getCmp('startTime').getValue();
	var _verionInfo=versionInfo.getValue();
	var _sysName = trim(form.getForm ().findField ("form_sysName").getRawValue());
	var form_instName_input = form.getForm ().findField ("form_instName_input").getRawValue();
	var ipmpOrder=IPMPOrderNumberComBox.getRawValue();
	if(ipmpOrder.length>255){
		Ext.MessageBox.alert ("提示", "IPMP关联单号过长，超过了255，请重新输入");
		return;
	}
	if(!ip_flag){
		return;
	}
	console.log(timingBox);
	if(timeStartFlag&&timingBox.getValue())
	{
		if(''==trim(starttime) || null==starttime)
		{
			Ext.MessageBox.alert ("提示", "请输入启动时间");
			return;
		}
		starttime=Ext.Date.format(new Date(starttime),'Y-m-d H:i'); 
	}
	
	if(''==_sysName || null==_sysName){
		Ext.MessageBox.alert ("提示", "请选择系统名称");
		return;
	}else if(''==_verionInfo || null==_verionInfo){
		Ext.MessageBox.alert ("提示", "请选择变更单号");
		return;
	}
	if (trim (form_instName_input) == '' || (null == form_instName_input))
	{
		Ext.MessageBox.alert ("提示", "变更说明不能为空");
		return false;
	}
	Ext.Msg.confirm ("确认启动", "是否确认启动此任务?", function (button)
	{
		if (button == 'yes'){
	    	if(pfSUSRepeatSwitch){
				var form_sysName = trim(form.getForm ().findField ("form_sysName").getRawValue());
				var form_instName_input = form.getForm ().findField ("form_instName_input").getRawValue();
				var form_version = versionInfo.rawValue;
				Ext.Ajax.request (
						{
						    url : 'judgeSUSFlowIsDeploy.do',
						    method : 'POST',
						    params :
						    {
						        sysName : form_sysName,
						        version : form_version,
						        instName : form_instName_input,
						        evnIds:arrEvnids
						    },
						    success : function (response, options)
						    {
						    	var success = Ext.decode (response.responseText).success;
							    if (success)
							    {
							    	Ext.Msg.confirm ("确认提交", "该流程已执行，请确认是否再次执行?<br>",
											function (id)
											{
												if (id == 'yes'){
													if(warnclose_window_swich){  		//如果告警压制开关开启，先插入告警压制步骤，再启动
														popIwStep(starttime);					
													}else{
														realyDirectStart(starttime);	//直接启动
													}
												}
											});
							    }else{
									if(warnclose_window_swich){  		//如果告警压制开关开启，先插入告警压制步骤，再启动
										popIwStep(starttime);					
									}else{
										realyDirectStart(starttime);	//直接启动
									}
							    }
						    },
							failure : function(result, request) {
								secureFilterRs(result,"操作失败！");
							}
						
						});
			}else{
				if(warnclose_window_swich){  		//如果告警压制开关开启，先插入告警压制步骤，再启动
					popIwStep(starttime);					
				}else{
					realyDirectStart(starttime);	//直接启动
				}
			}
			
		}
	});
}

function realyDirectStart(starttime){
	//禁用步骤
	var ignoreStepIdArr = [];
    groupSus__webStudio.instanceInsInfoStore_pub.each(function(record) {
      if(record.get('checked') && record.get('iid')){
        ignoreStepIdArr.push(record.get('iid'));
      }
    });

	Ext.MessageBox.wait("数据处理中...", "进度条");
	pushEnvid();
	//遍历map，将页面选择的服务器信息后
	var ipArr =new Array();
	ipMap.each(function(key, value, length)
	{
		ipArr.push(value);
	});
	Ext.Ajax.request (
	{
	    url : 'submitInsgdbForSUSCeb.do',
	    timeout: 1800000,
	    method : 'POST',
		dataType: 'json',
		jsonData: Ext.JSON.encode(ipArr),
	    params :
	    {
	        sysName : form.getForm ().findField ("form_sysName").getRawValue(),
	        instName_input : form.getForm ().findField ("form_instName_input").getRawValue(),
	        version : versionInfo.rawValue,
	        isysid : iinstanceid,
	        isystype : 1,
	        evnIds:arrEvnids,
	        //prepareStartIpsJson:pars(ipArr),
	        startTime : starttime,
	        istartUserFullName:current_userName,
            warning:isWarning,
	        startUserLoginName:current_userLoginName,
	        gdbStartFlagApi:""+gdbStartFlagApi+"",
	        wartStepJson:getWarnStepJson_Loop(),
	        IPMPOrderNumber:IPMPOrderNumberComBox.getRawValue(),
			ignoreStepids:ignoreStepIdArr,
			skepallut:skepallut
	    },
	    success : function (response, options)
	    {
		    var success = Ext.decode (response.responseText).success;
		    var dataList1 = Ext.decode (response.responseText).sqlDataList;
		    if (success)
		    {
		    	if(dangerSqlFlag){
		    		console.log(dataList1);
		    		if(dataList1 && dataList1.length != 0){
		    			showDangerSqlCheck(dataList1,Ext.decode (response.responseText).message,Ext.decode (response.responseText).itaskid);
		    		}else{
		    			skipGraphForSus(Ext.decode (response.responseText).message);
		    		}
		    	}else{
		    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
		    			    {
		    				    var moniUrl="initGraphForSUS.do";
		    					destroyRubbish();
		    					contentPanel.setTitle ('变更监控');
		    					contentPanel.getLoader().load({
		    						url: moniUrl,
		    						params : {
		    							contentPanelHeight:contentPanel.getHeight(),
		    							windowScHeight:window.screen.height
		    						}
		    					});
		    					if (Ext.isIE) {
		    						CollectGarbage();
		    					}
		    			    });
		    	}
		    	
				
		    }else
		    {
		    	if("error"==(Ext.decode (response.responseText).cacheinfo)){
		    		Ext.Msg.confirm("请确认", Ext.decode (response.responseText).message+",是否仍然发起", function(button, text) {
		    			if (button == "yes") {
		    				Ext.MessageBox.wait("数据处理中...", "进度条");
		    				Ext.Ajax.request({
		    					    url : 'submitInsgdbForSUSCeb.do',
		    					    timeout: 1800000,
		    					    method : 'POST',
									dataType: 'json',
									jsonData: Ext.JSON.encode(ipArr),
		    					    params :
		    					    {
		    					        sysName : form.getForm ().findField ("form_sysName").getRawValue(),
		    					        instName_input : form.getForm ().findField ("form_instName_input").getRawValue(),
		    					        version : versionInfo.rawValue,
		    					        isysid : iinstanceid,
		    					        isystype : 1,
		    					        evnIds:arrEvnids,
		    					        //prepareStartIpsJson:pars(ipArr),
		    					        startTime : starttime,
		    					        istartUserFullName:current_userName,
		    				            warning:isWarning,
		    					        startUserLoginName:current_userLoginName,
		    					        gdbStartFlagApi:""+gdbStartFlagApi+"",
		    					        wartStepJson:getWarnStepJson_Loop(),
		    					        IPMPOrderNumber:IPMPOrderNumberComBox.getRawValue(),
		    							ignoreStepids:ignoreStepIdArr,
		    							continueFlag:1,
		    							skepallut:skepallut
		    					    },
		    					    success : function (response, options)
		    					    {
		    						    var success = Ext.decode (response.responseText).success;
		    						    var dataList1 = Ext.decode (response.responseText).sqlDataList;
		    						    if (success)
		    						    {
		    						    	if(dangerSqlFlag){
		    						    		console.log(dataList1);
		    						    		if(dataList1 && dataList1.length != 0){
		    						    			showDangerSqlCheck(dataList1,Ext.decode (response.responseText).message,Ext.decode (response.responseText).itaskid);
		    						    		}else{
		    						    			skipGraphForSus(Ext.decode (response.responseText).message);
		    						    		}
		    						    	}else{
		    						    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
		    						    			    {
		    						    				    var moniUrl="initGraphForSUS.do";
		    						    					destroyRubbish();
		    						    					contentPanel.setTitle ('变更监控');
		    						    					contentPanel.getLoader().load({
		    						    						url: moniUrl,
		    						    						params : {
		    						    							contentPanelHeight:contentPanel.getHeight(),
		    						    							windowScHeight:window.screen.height
		    						    						}
		    						    					});
		    						    					if (Ext.isIE) {
		    						    						CollectGarbage();
		    						    					}
		    						    			    });
		    						    	}
		    								
		    						    }else
		    						    {
	    						    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
	    						    		return;
		    						    }
		    					    },
		    					    failure : function (result, request)
		    					    {
		    						    Ext.Msg.alert ('请求失败');
		    						    return;
		    					    }
		    					
		    				});
		    			}
				    });
				  
					
		    	}else{
		    		Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
		    	}
		    }
	    },
	    failure : function (result, request)
	    {
		    Ext.Msg.alert ('请求失败');
	    }
	
	});

}
function checkIpList(){
	var iprows = ipGrid.getSelectionModel().getSelection();
	if(iprows&&iprows.length==0){
		Ext.MessageBox.alert ("提示", "请选择服务器");
		return false;
	}
	for(var i=0;i<iprows.length;i++){
		var iips = iprows[i].get("iip");
		if(iips==null||iips==""){
			Ext.MessageBox.alert ("提示", "服务器IP不能为空!");
			return false;
		}
		/*if(!isIP(iips)){
			Ext.MessageBox.alert ("提示", "服务器IP格式错误!");
			return false;
		}*/
	}
	var envrows = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	if(envrows.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return false;
	}
	for(var i=0;i<envrows.length;i++){
		var evnId = envrows[i].get("iid");
		var evnName = envrows[i].get("iname");
		var ipcount = 0;
		for(var j=0;j<iprows.length;j++){
			var ip_envid = iprows[j].get('ienvid');
			if(ip_envid == evnId){
				ipcount++;
			}
		}
		if(ipcount==0){
			Ext.MessageBox.alert ("提示", "环境"+evnName+"下未选择服务器!");
			return false;
		}
	}
	return true;
}

var dangerSqlWin = '';
function showDangerSqlCheck(data,message,itaskid){
	Ext.define('dangerSqlModel', {
    extend: 'Ext.data.Model',
    fields: [
             {name: 'iid',  type: 'long'},
             {name: 'shellName',  type: 'string'},
             {name: 'sqlCommand',   type: 'string'}
            ]
	});
	var dangerSqlStore = Ext.create('Ext.data.Store', {
        model: 'dangerSqlModel',
        data:data
    });
    
	var columns = [
           {
               text : '序号',
               width : 200,
               hidden : true,
               sortable : false,
        	   xtype: 'rownumberer',
        	   align : 'left'
           },
           { 
               text: '脚本名称',  
               dataIndex: 'shellName',
               flex : 1,
               align : 'left'
           },
	       { 
        	   text: '高危标识', 
        	   flex : 2,
        	   dataIndex: 'sqlCommand',
        	   align : 'left',
	  		   renderer : function(value, p, r) {
					var	temp = '<font color="red"><b>'+value+'</b></font>';
					return temp;
			   }
           }
	      ];
	var dangerSqlPane = Ext.create('Ext.grid.Panel', {
		columns:columns,
		autoScroll:true,
		height:screen.height - 490,
        border:false,
        dockedItems: [{
        	dock: 'bottom',
            xtype: 'toolbar',
            layout : {
                type : 'hbox',//布局选择vbox
                align : 'middle',
                pack : 'center',
             },
            items: [
                {
                    text: '确认',
                    cls : 'Common_Btn',
                    handler : function(){
						confirmStart(itaskid);
                    }
                },'-', {
                    text: '取消',
                    cls : 'Common_Btn',
                    handler :function(){
                    	Ext.Msg.confirm ("确认", "该流程含有高危命令，请确认是否取消执行?<br>",
								function (id)
								{
									if (id == 'yes'){
										confirmCancle(itaskid);
									}
								});
                    }
                }
    		]
        }],
        store: dangerSqlStore
	});
	
	dangerSqlWin = Ext.create ('Ext.window.Window',
    {
        title : '高危标识信息',
        autoScroll : true,
        modal : true,
        closeAction : 'destroy',
        buttonAlign : 'center',
        draggable : true,// 禁止拖动
        resizable : false,// 禁止缩放
        width : 900,
        listeners   : {'close':{fn:skipGraphForSus(message)}},
        height : screen.height - 390,
        items:[dangerSqlPane]
    }).show ();
}

function skipGraphForSus(message){
	Ext.Msg.alert ('提示', message, function ()
		    {
			    var moniUrl="initGraphForSUS.do";
				destroyRubbish();
				contentPanel.setTitle ('变更监控');
				contentPanel.getLoader().load({
					url: moniUrl,
					params : {
						contentPanelHeight:contentPanel.getHeight(),
						windowScHeight:window.screen.height
					}
				});
				if (Ext.isIE) {
					CollectGarbage();
				}
		    });
}

function confirmStart(itaskid){
	Ext.Ajax.request (
			{
			    url : 'confirmStart.do',
			    method : 'POST',
			    timeout: 900000,
			    params :
			    {
			    	itaskid:itaskid
			    },
			    success : function (response, options)
			    {
				    var success = Ext.decode (response.responseText).success;
				    if (success)
				    {
				    	dangerSqlWin.close();
				    }
			    },
			    failure : function (result, request)
			    {
				    Ext.Msg.alert ('提示', '启动失败  ! ');
			    }
			});
}

function confirmCancle(itaskid){
	Ext.Ajax.request (
			{
			    url : 'concirmCancel.do',
			    method : 'POST',
			    timeout: 900000,
			    params :
			    {
			    	itaskid:itaskid
			    },
			    success : function (response, options)
			    {
				    var success = Ext.decode (response.responseText).success;
				    if (success)
				    {
				    	dangerSqlWin.close();
				    }
			    },
			    failure : function (result, request)
			    {
				    Ext.Msg.alert ('提示', '取消失败  ! ');
			    }
			});
}







/**
 * @desc 校验告警压制的JSON 循环层
 * **/
function getWarnStepJson_Loop(){
	var isFirst=false;
	var jsonData ="";
	var envid=-1;
	
	try{
			jsonData +="[";
			for(var i=0;i<groupSus__webStudio.tabItems.length;i++){
	
				groupSus__webStudio.iwGrid=groupSus__webStudio.tabItems[i];
				groupSus__webStudio.iWStore=groupSus__webStudio.iwGrid.store;
				
				
				if(i==0){
					isFirst=true;
				}else{
					isFirst=false;
				}
				
				var temp_envname=groupSus__webStudio.iwGrid.initialConfig.title;
				envid=getEnvNameByEnvid(temp_envname);
				jsonData += getWarnStepJson_newLogic(isFirst,envid,temp_envname);
			}
			jsonData +="]";
			
			
			if("[]" == jsonData){
				jsonData="-1";
			}
	}catch(e){
		console.log(e);
	}
		
		
	return jsonData;
}

/**
 * @desc 获得告警压制的JSON
 * **/
function getWarnStepJson_newLogic(isFirst,envid,ienvname){
	var jsonData="";
	var m = [];
	for(var bb=0;bb<groupSus__webStudio.iWStore.getCount();bb++){
		m.push(groupSus__webStudio.iWStore.getAt(bb));	
	}	
	if (m.length < 1)
	{
		return jsonData;
	}
	
	
	var _insert_VersionName = groupSus__webStudio.iwform_instName_input.getRawValue();
	for (var i = 0, len = m.length; i < len; i++)
	{
		var n = 0;
		var tempRecord=m[i].data;
		var _key=tempRecord.insert_ConnerAndActName;
		var _value=groupSus__webStudio.iwComboxStoreMap.get(_key);
		if(null!=_value ){
			_value =_value.toString();
			if((-1!=_value.indexOf(":"))){				
				var tempArr=_value.split(":");
				if(null!=tempArr && tempArr.length==2){
					tempRecord.insert_Conner=tempArr[0];
					tempRecord.insert_serner=tempArr[1];
					tempRecord.insert_VersionName=_insert_VersionName;
					tempRecord.ienvid=envid;
					tempRecord.ienvname=ienvname;
					tempRecord.commit;
				}
			}
		}
		
		if(null!=tempRecord && (tempRecord.insert_Type==IEAI_SUS_STEP_CLOSE ||tempRecord.insert_Type==IEAI_SUS_STEP_CLOSE_2)){
			tempRecord.insert_Type='关';
			tempRecord.commit;
		}else{
			tempRecord.insert_Type='开';
			tempRecord.commit;
		}
		
		
		
		var ss = Ext.JSON.encode (m[i].data);
		if (isFirst && i == 0)
			jsonData = jsonData + ss;
		else
			jsonData = jsonData + "," + ss;
	}
	
	console.log("JSON:"+jsonData);
	return jsonData;
}

/**
 * @desc 设置步骤的序号和阶段号
 * **/
function setCornnerAndSerner(){
	var m = [];
	for(var bb=0;bb<groupSus__webStudio.iWStore.getCount();bb++){
		m.push(groupSus__webStudio.iWStore.getAt(bb));	
	}	
	if (m.length < 1)
	{
		return;
	}
	
	
	var _insert_VersionName = groupSus__webStudio.iwform_instName_input.getRawValue();
	var jsonData = "[";
	for (var i = 0, len = m.length; i < len; i++)
	{
		var n = 0;
		var tempRecord=m[i].data;
		var _key=tempRecord.insert_ConnerAndActName;
		var _value=groupSus__webStudio.iwComboxStoreMap.get(_key);
		if(null!=_value ){
			_value =_value.toString();
			if((-1!=_value.indexOf(":"))){				
				var tempArr=_value.split(":");
				if(null!=tempArr && tempArr.length==2){
					tempRecord.insert_Conner=parseInt(tempArr[0]);
					tempRecord.insert_serner=parseInt(tempArr[1]);
					tempRecord.insert_VersionName=_insert_VersionName;
					
					var _insert_FronOrPandingStr=tempRecord.insert_FronOrPandingStr;
					if('前'==_insert_FronOrPandingStr){
						tempRecord.comput_serner=parseInt(tempRecord.insert_serner);
					}else{
						tempRecord.comput_serner=parseInt(tempRecord.insert_serner)+1;
					}
					tempRecord.commit;
				}
			}
		}
	}
}


/**
 * @desc 校验告警压制的JSON 循环层
 * **/
function validateWarnStepJson_loop(){
	var rs=false;
		for(var i=0;i<groupSus__webStudio.tabItems.length;i++){
			groupSus__webStudio.iwGrid=groupSus__webStudio.tabItems[i];
			groupSus__webStudio.iWStore=groupSus__webStudio.iwGrid.store;
			groupSus__webStudio.iwResStore=getiwIResSore2(groupSus__webStudio.iwGrid);
			rs =validateWarnStepJson();
			groupSus__webStudio._iwtitle =groupSus__webStudio.iwGrid.initialConfig.title+",";
			if(false==rs){
				break;
			}
		}
	return rs;
}


/**
 * @desc 校验告警压制的JSON 业务逻辑层
 * **/
function validateWarnStepJson(){
	var success=true;
	setCornnerAndSerner();

	//告警压制，校验插入步骤是否存在于Excel当中	
	success=validateWarnResName();
	if(false==success){
		return success;	
	}	
	var tempRs=validateconnerAndActName();
	if(false==tempRs){
		success=false;
		return success;	
	}
	
	
	
	var m = [];
	for(var bb=0;bb<groupSus__webStudio.iWStore.getCount();bb++){
		m.push(groupSus__webStudio.iWStore.getAt(bb));	
	}
	if (m.length < 1)
	{
		success=true;
		return success;
	}
	
	
	
	for (var i = 0, len = m.length; i < len; i++)
	{
		var n = 0;
		var tempRecord=m[i].data;
		var _insert_ResNames=tempRecord.insert_ResNames;
		var _insert_FronOrPandingStr=tempRecord.insert_FronOrPandingStr;
		var _insert_Type=r=tempRecord.insert_Type;
		var _key=tempRecord.insert_ConnerAndActName;
		var _alertGroup=tempRecord.alertGroup;
		var _summary=tempRecord.summary;
		var _webInterval=tempRecord.webInterval;
		var _value=groupSus__webStudio.iwComboxStoreMap.get(_key);
		
		
		if(null!=_value &&""!=_key){
			_value =_value.toString();
			if((-1!=_value.indexOf(":"))){				
				var tempArr=_value.split(":");
				if(null!=tempArr && tempArr.length==2){
					tempRecord.insert_Conner=tempArr[0];
					tempRecord.insert_serner=tempArr[1];
					if('前'==_insert_FronOrPandingStr){
						tempRecord.comput_serner=parseInt(tempRecord.insert_serner);
					}else{
						tempRecord.comput_serner=parseInt(tempRecord.insert_serner)+1;
					}
					tempRecord.commit;
				}
			}
		}else{
			Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警压制步骤不能空 ！", function (){});
			success=false;
			return success;
		}
		
		if (_alertGroup.indexOf(" ")>-1) 
		{
			Ext.MessageBox.alert ("提示", groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警组中不能包含空格！");
			return;
		}
		
		if (_summary.indexOf(" ")>-1) 
		{
			Ext.MessageBox.alert ("提示", groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警内容中不能包含空格！");
			return;
		}
		
		
		if (trim (_alertGroup).length > 255)
		{
			Ext.MessageBox.alert ("提示", groupSus__webStudio._iwtitle+"告警组长度不能超过255个字符");
			return false;
		}
		if (trim (_summary).length > 255)
		{
			Ext.MessageBox.alert ("提示", groupSus__webStudio._iwtitle+"告警内容长度不能超过255个字符");
			return false;
		}
		if (trim (_webInterval).length > 255)
		{
			Ext.MessageBox.alert ("提示", groupSus__webStudio._iwtitle+"告警时间长度不能超过255个字符");
			return false;
		}
		
		
		if(""!=_webInterval){			
			if(!isNumber(_webInterval)) {
				Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警时间必须是正整数或空白！", function (){});
				success=false;
				return success;
			}
			var web= parseInt(_webInterval);
			if(web<=0) {
				Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警时间必须是正整数或空白！", function (){});
				success=false;
				return success;
			}
		}
		
		if(null==_insert_ResNames ||""==_insert_ResNames){
			Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,资源组不能空 ！", function (){});
			success=false;
			return success;
		}
		
		if(null==_insert_FronOrPandingStr ||""==_insert_FronOrPandingStr){
			Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警压制步骤前后不能空 ！", function (){});
			success=false;
			return success;
		}
		
		if(null==_insert_Type ||""==_insert_Type){
			Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"第"+(i+1)+"条记录中,告警压制类型不能空 ！", function (){});
			success=false;
			return success;
		}
	}
	

	//001 奇偶数判断
	if(m.length==0){
		Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"告警压制步骤个数应大于0！", function (){});
		success=false;
		return success;
	}
	
	if(m.length%2==1){
		Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"告警压制步骤个数应该是偶数个！", function (){});
		success=false;
		return success;
	}
	
	var cnt_Close=0;
	var cnt_Open=0;
	for (var i = 0, len = m.length; i < len; i++)
	{
		var n = 0;
		var tempRecord=m[i].data;
		var _tempinsert_Type=tempRecord.insert_Type;
		var _tempSerner=tempRecord.insert_serner;
		var _tempConner=tempRecord.insert_Conner;
		
		if(IEAI_SUS_STEP_CLOSE==_tempinsert_Type||IEAI_SUS_STEP_CLOSE_2==_tempinsert_Type){
			cnt_Close++;
		}else{
			cnt_Open++;
		}
	}
	
	
	
	//002 关闭与开启 成对判断
	if(m.length%2==1){
		Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"告警压制步骤个数应以该是偶数个！", function (){});
		success=false;
		return success;
	}
	return success;
}


//告警压制，校验资源组信息
function validateWarnResName(){
	var sortArr=[];
	var success=true;
	var m = [];
	for(var bb=0;bb<groupSus__webStudio.iWStore.getCount();bb++){
		m.push(groupSus__webStudio.iWStore.getAt(bb));	
	}
	
	if (m.length < 1)
	{
		success=true;
		return success;
	}
	
	
	for (var i = 0, len = m.length; i < len; i++)
	{
		var tempRecord=m[i].data;
		sortArr.push(tempRecord);
	}
	
	
	//================001.冒泡排序===============================
	for (var i = 0, len = sortArr.length; i < len; i++)
	{
		for (var j = 0; j < sortArr.length-i-1; j++)
		{
			if ((sortArr[j].comput_serner == sortArr[j + 1].comput_serner)
					&& (sortArr[j].insert_serner > sortArr[j + 1].insert_serner)) {   // 当比较序号相同时，比较 真实序号。
				var swap = sortArr[j];
				sortArr[j] = sortArr[j + 1];
				sortArr[j + 1] = swap;
			}
			else if ((sortArr[j].comput_serner == sortArr[j + 1].comput_serner)
					&& (sortArr[j].insert_serner == sortArr[j + 1].insert_serner)
					&& (sortArr[j].insert_FronOrPandingStr == '后')					  // 当比较序号相同时，后在前
					&& (sortArr[j+1].insert_FronOrPandingStr == '前')
					) {   
				var swap = sortArr[j];
				sortArr[j] = sortArr[j + 1];
				sortArr[j + 1] = swap;
			}
			else if(sortArr[j].comput_serner > sortArr[j+1].comput_serner){
			      var swap = sortArr[j];
			      sortArr[j] = sortArr[j+1];
			      sortArr[j+1] = swap;
			}

			
		}
	}
	
	
	console.log("============按照告警压制 插入后， 真实的序号——comput_serner 进行排序后，告警步骤输出==================");
	for (var i = 0, len = sortArr.length; i < len; i++)
	{
	   var rss=  "【"
			   		+"  comput_serner："+sortArr[i].comput_serner 
		   			+" ,insert_serner：" +sortArr[i].insert_serner
		   			+" ,insert_ConnerAndActName：" +sortArr[i].insert_ConnerAndActName
		   			+" ,insert_Type：" +sortArr[i].insert_Type
		   		 +"】";
       console.log(rss);
	}
	
	
	//================002.校验资源组===============================
	//================002.1收集资源组===============================
	var iwIResNameMap = new Ext.util.HashMap ();
	for (var i = 0, len = sortArr.length; i < len; i++)
	{
		var _tempRecord=sortArr[i];
		var tempResName=_tempRecord.insert_ResNames;
		var _valArr=iwIResNameMap.get(tempResName);
		
		if(null == _valArr){
			_valArr=[];
		}
		_valArr.push(_tempRecord);
		iwIResNameMap.add(tempResName,_valArr);
	}
	
	//================002.2校验 同名资源组是否成对===============================
	var isResNameSame=true;
	var errorResName="";
	iwIResNameMap.each(function(key, value, length)
	{
		if(null!=value && (value instanceof Array)){
			var _valArr=value;
			if(_valArr.length%2==1){
				if(isResNameSame){
					isResNameSame=false;
					errorResName=key;
				}
			}
		}
	});
	
	if(false==isResNameSame){
		Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"资源组: "+errorResName+" 在使用时没有成对出现!", function (){});
		success=false;
		return success;
	}
	
	
	//================002.3校验 同名资源组先关后开===============================
	console.log("============先关后开校验==================");
	var isCloseSuucess=true;
	var errorRecord=null;
	var cnt=0;
	iwIResNameMap.each(function(key, value, length)
	{
		if(null!=value && (value instanceof Array)){
			var _valArr=value;
			for(var z=0;z<_valArr.length;++z){
				
				if(isCloseSuucess){
					var _insert_Type=r=_valArr[z].insert_Type;
					if(IEAI_SUS_STEP_CLOSE==_insert_Type||_insert_Type==IEAI_SUS_STEP_CLOSE_2){
						cnt++;
					}else{
						cnt--;
					}
					
					var rs1=printWarnRecord(_valArr[z]) +" cnt:"+cnt;
				    console.log(rs1);
					
				    
				    if(cnt<0){
						isCloseSuucess=false;
						errorRecord=_valArr[z];
					}
				}
			}
		}
	});
	
    if(cnt<0){
		Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"请保证告警压制步骤先关后开", function (){});
		success=false;
		return success;
	}else if(cnt>0){
		Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+"请保证告警压制步骤，关开成对出现", function (){});
		success=false;
		return success;
	}
    
	if(false==isCloseSuucess){
		Ext.Msg.alert ('提示1', groupSus__webStudio._iwtitle+"告警压制出现了，先开后关 的,请将  开启位置 由 <B>"+errorRecord.insert_ConnerAndActName+"</B> 向后移", function (){});
		success=false;
		return success;
	}
	
	
	var isResexist=true;
	for(var a=0; a <groupSus__webStudio.iWStore.getCount();a++){
			var _records=groupSus__webStudio.iWStore.getAt(a);
			var _resnames=_records.data.insert_ResNames;
			var resArr=_resnames.split(",");
			
				if(null!=resArr && resArr.length!=0 ){
					var sFindFlag=false;
					for(var b=0;b<resArr.length;b++){
						var ves1=resArr[b];
						
						var ps1=null;
						var _records=null;
							for(var c=0; c <groupSus__webStudio.iwResStore.getCount();c++){
							_records=groupSus__webStudio.iwResStore.getAt(c);
							ps1=_records.data.name;
							if(ps1==ves1){
								sFindFlag=true;
								break;
							}
							
						}
							
						if(false == sFindFlag){
							Ext.Msg.alert ('提示', groupSus__webStudio._iwtitle+ves1+":该资源组不存在", function (){});
							isResexist=false;
							success=false;
							return success;
						}
					}
				}
		
	}
	
	if(false==isResexist){
		Ext.Msg.alert ('提示2', groupSus__webStudio._iwtitle+"告警压制出现了，先开后关 的,请将  开启位置 由 <B>"+errorRecord.insert_ConnerAndActName+"</B> 向后移", function (){});
		success=false;
		return success;
	}
	
	var isCCFront=isCompareCloseFrontBySerner(iwIResNameMap);
	if(false ==isCCFront){
		success=false;
		return success;
	}
	
	//告警压制，资源组IP必须一致的校验
	var iwIPMap = new Ext.util.HashMap ();
	for (var i = 0, len = sortArr.length; i < len; i++)
	{
		var _tempRecord=sortArr[i];
		var tempResName=_tempRecord.insert_ResNames;
		var _valArr=iwIPMap.get(tempResName);
		
		if(null == _valArr){
			_valArr=[];
		}
		_valArr.push(_tempRecord.iip);
		iwIPMap.add(tempResName,_valArr);
	}
	
	//
	var ipNullFlag=false;
	for (var i = 0, len = sortArr.length; i < len; i++)
	{
		var _tempRecord=sortArr[i];
		if(_tempRecord.iip== null || _tempRecord.iip== ''){
			ipNullFlag=true;
		}
	}
	if(true==ipNullFlag){
		Ext.Msg.alert ('提示3', groupSus__webStudio._iwtitle+'告警压制ip不能为空', function (){});
		success=false;
		return success;
	}
	
	var ipValidateRes=true;
	var ipValidateResErrorMsg="";
	if (null != iwIPMap)
	{
		iwIPMap.each (function (key0, value, length)
		{
			var _valArr=iwIPMap.get (key0);
			if(null!=_valArr && 0!=_valArr.length){
				if(ipValidateRes){
					
					var tempMap = new Ext.util.HashMap ();
					for(var a=0;a<_valArr.length;a++){
						var curIp=_valArr[a];
						var curIpVal=tempMap.get(curIp);
						if(null==curIpVal){
							curIpVal=1;
						}else{
							curIpVal=curIpVal+1;
						}
						tempMap.add(curIp,curIpVal);
					}
					
					tempMap.each (function (key, value, length){
						var _tempVal=tempMap.get (key);
						if(_tempVal %2 ==1){
							ipValidateRes=false;
							ipValidateResErrorMsg="资源组:"+key0+"压制IP不一致";
							
						}
					});
			}
		}
		});
		
		if(false==ipValidateRes){
			Ext.Msg.alert ('提示3', ipValidateResErrorMsg, function (){});
			success=false;
			return success;
		}
	}
	return success
}

//告警压制，校验先关后开_02,根据序号
function isCompareCloseFrontBySerner(iwIResNameMap){
	var success=true;
	
	iwIResNameMap.each(function(key, value, length)
	{
		if(null!=value && (value instanceof Array)){
			var _valArr=value;
			var opentStepArr=[];
			var closeStepArr=[];
			
			
			for(var z=0;z<_valArr.length;++z){
				var _insert_Type=_valArr[z].insert_Type;
				if(IEAI_SUS_STEP_CLOSE==_insert_Type || _insert_Type==IEAI_SUS_STEP_CLOSE_2){
					closeStepArr.push(_valArr[z]);
				}else{
					opentStepArr.push(_valArr[z]);
				}
			}
			var rsSuc=isCompareCloseFrontBySerner2(opentStepArr,closeStepArr);
			if(false ==rsSuc){
				success=rsSuc;
			}
			
		}
	});
	
	return success;
}

//告警压制，校验资源组信息,根据序号2
function isCompareCloseFrontBySerner2(opentStepArr,closeStepArr){
	var success=true;
/*	if(null!=opentStepArr  && null!=closeStepArr){
		var openSerner =0;  //开启的序号
		var closeSerner=0;  //关闭的序号
				
				
		for(var a=0;a<closeStepArr.length;a++){
			if(false==success){
				break;
			}
			
			var validateOpenCnt=0;                    	//开启 步骤合格的数量
			closeSerner=closeStepArr[a].insert_serner;
			for(var i=0;i<opentStepArr.length;i++){
				openSerner=opentStepArr[i].insert_serner;				
				if(closeSerner<openSerner){				//关的序号小 OK
					validateOpenCnt++;
					continue;
				}else if(closeSerner == openSerner){	//关的序号 与开的序号相同，则比较 插入的位置，前后()
				
				
					if(opentStepArr[i].insert_FronOrPandingStr=='前'||opentStepArr[i].insert_FronOrPandingStr=='0'){
						Ext.Msg.alert ('提示3', "告警压制出现了，先开后关 的,请将  开启位置 由 <B>"+opentStepArr[i].insert_ConnerAndActName+"</B> 向后移", function (){});
						success=false;
						break;
					}else{
						validateOpenCnt++;	
					}
					
					
				}else if(closeSerner>openSerner){
					validateOpenCnt--;	
				}
			}
			
			if(validateOpenCnt<0){ //开在前，未找到它前面的 关
				Ext.Msg.alert ('提示4', "告警压制出现了，先开后关 的,请将  开启位置 由 <B>"+opentStepArr[i].insert_ConnerAndActName+"</B> 向后移", function (){});
				success=false;
			}
		}
	}*/
	
	return success;
}

function printWarnRecord(srcRecord){
	   var rss=  "【"
	   		+"  comput_serner："+srcRecord.comput_serner 
  			+" ,insert_serner：" +srcRecord.insert_serner
  			+" ,insert_ConnerAndActName：" +srcRecord.insert_ConnerAndActName
  			+" ,insert_Type：" +srcRecord.insert_Type
  		 +"】";
	 return rss;
}



function validateLoginName(){
	var flag=false;
	if(null!=auditorStoreValidate){
		var _auStoreCnt=auditorStoreValidate.getCount();
		for(var zz=0;zz<_auStoreCnt;zz++){
			var _record=auditorStoreValidate.getAt(zz);
			var tempLoginName=_record.get('loginName');

			var auditorValue = auditorComBoxValidate.getValue ();
			if (auditorValue== tempLoginName)
			{
				flag=true;
				break;
			}
		}
	}
	return flag;
}

/****
*@desc 根据资源组，选择IP
*/
function getIWGoupIps(recordArr){
	var iip="";
	if(null!=recordArr){
		for(var i=0;i<recordArr.length;i++){
			var recordR=recordArr[i];
			var tempResid=recordR.data.iid;
			
			var tempIip=getIpByGroupId(tempResid);
			if(""==iip){
				iip=tempIip;
			}else{
				iip += ","+tempIip;
			}
		}
	}
	return iip;
}

/**
* @desc 根据资源组id字符串组合。来获得IP
*/
function getIWGoupIpsResid(iresid){
	var iip="";
	var recordArr =iresid.split(',');
	if(null!=recordArr){
		for(var i=0;i<recordArr.length;i++){
			var tempResid=recordArr[i];
			var tempIip=getIpByGroupId(tempResid);
			if(""==iip){
				iip=tempIip;
			}else{
				iip += ","+tempIip;
			}
		}
	}
	return iip;
}

/****
*@desc 根据资源组数组，获得资源组id
*/
function getIWGoupPrimaryKey(recordArr){
	var pk="";
	if(null!=recordArr){
		for(var i=0;i<recordArr.length;i++){
			var recordR=recordArr[i];
			var tempResid=recordR.data.iid;
			
			if(""==tempResid||0==i){
				pk=tempResid;
			}else{
				pk += ","+tempResid;
			}
		}
	}
	return pk;
}

function getIpByGroupId(tempResid){
	var iip="";
	for(var i=0;i<groupSus__webStudio.iwHostStore.getCount();i++){
		var iwHostRecord=groupSus__webStudio.iwHostStore.getAt(i);
		
		var key =iwHostRecord.data.key;
		if(key==tempResid){
			var valArr =iwHostRecord.data.value;
			if(null!=valArr){
				for(var b=0;b<valArr.length;b++){
					var valRecord=valArr[b];
					var tempIp=valRecord.ip;
					
					if(iip == ""){
						iip=tempIp;
					}else{
						iip+=(","+tempIp);
					}
				}
			}
		}
	}
	
	return iip;
}

/**
 * @desc 校验 插入的告警压制步骤是否存在于 '原始步骤''当中
 * **/
function validateconnerAndActName(){
	var success=true;
	var m = [];
	for(var bb=0;bb<groupSus__webStudio.iWStore.getCount();bb++){
		m.push(groupSus__webStudio.iWStore.getAt(bb));	
	}
	if (m.length < 1)
	{
		success=true;
		return success;
	}
	
	
	
	for (var i = 0, len = m.length; i < len; i++)
	{
		var tempRecord=groupSus__webStudio.iWStore.getAt(i);
		var _connerAndActName=tempRecord.data.insert_ConnerAndActName;
		var isExist = isConnerActNameExist(_connerAndActName);
		if(isExist==false){
			success=false;
			break;
		}
	}
	return success;
}

/***
*@desc 检查 阶段和 活动名 是否存在于  '系统信息'tab页中
*/
function isConnerActNameExist(_connerAndActName){
	var isExist=false;
	if(null!=_connerAndActName){
		var caaArr=_connerAndActName.split(':');
		if(null!=caaArr && caaArr.length==2){
			var tempCooner=caaArr[0];
			var tempActName=caaArr[1];
			
			for(var a=0; a <groupSus__webStudio.instanceInsInfoStore_pub.getCount();a++){
				var vvv_record=groupSus__webStudio.instanceInsInfoStore_pub.getAt(a);
				var _icName=vvv_record.data.iconnerName;
				var _iaName=vvv_record.data.iactName;
				
				if(_icName ==tempCooner && tempActName== _iaName){
					isExist=true;
					break;
				}
			}
		}
	}
	if(false ==isExist){
		Ext.Msg.alert ('提示', "告警压制步骤 【"+_connerAndActName+"】不存在于当前启动实例的步骤列表中！", function (){});
	}
	return isExist;
}

/***
*@desc 设置IP
* @param ips_defualtSeleced 弹出告警IP时，默认选中的记录
* @param allip GridPanel中，全部选中的IP
*/
function popSetIPWin(ips_defualtSeleced,allip){
	groupSus__webStudio.IPSStore.removeAll();
	
	
	
	//告警压制GridPanel
	IPSGrid = Ext.create('Ext.grid.Panel', {
			columnLines : true,
			store : groupSus__webStudio.IPSStore,
			selModel: IPSSelModel,
			columns : [ {
				text : '压制IP',
				dataIndex : 'iip',//插入的步骤名称和步骤名称（用:进行分隔）
				flex : 1
			} ],
			region : 'center',
			emptyText : '没有数据',
			loadMask : true,
			border : false,
			dockedItems : [ {
				xtype : 'toolbar',
				items : [ 
				{
					text : '保存',
					cls:'Common_Btn',
					handler : function() {
				    	Ext.MessageBox.buttonText.yes = "确定";
				    	Ext.MessageBox.buttonText.no = "取消";
				    	Ext.Msg.confirm ("确认", "是否已经选定压制IP？", function (id)
						{
							if (id == 'yes')
							{
								var data = IPSGrid.getSelectionModel().getSelection();
								if(0==data.length){
									Ext.Msg.alert ('提示', "告警压制ip中，请至少选中一个IP进行压制！", function (){});
								}else{
									var tempiip="";
								    for(var i=0;i<data.length;i++){
								    	var _ips =data[i].get('iip');
								    	if(i==0){
											tempiip=_ips;
										}else{
											tempiip+=','+_ips;
										}
								    }
									
									groupSus__webStudio.iWStore.getAt(selectIdx).set('iip',tempiip);
									IPSWindow.close();
								}
							}
						});
					}
				} ]
			} ]
		});
	
		if(null!=allip && ''!=allip){
			var j=0;
			var iparrs =allip.split(',');
			if(null!=iparrs && iparrs.length>0){
				for(var i=0;i<iparrs.length;i++){
					var _tempIP =iparrs[i];
					if(null!=_tempIP && ''!=_tempIP){
						var ipsObj=new IPSModel();
						ipsObj.data.iip=_tempIP;
						groupSus__webStudio.IPSStore.insert(j++,ipsObj);
					}
				}
			}
		}
		
		var IPSWindow = Ext.create('widget.window', {
			title : '告警压制IP设定',
			closeAction : 'hide',
			width : 1200,
			height : 600,
			modal : true,
			layout : 'border',
			items : [IPSGrid]
		});
		
		
		if (IPSWindow.isVisible()) {
			IPSWindow.hide(this, function() {
			});
		} else {
			IPSWindow.show();
			//IPSGrid.getSelectionModel().selectAll(true);//选择所有行  
			
			var checkedArr=[];
			var iipArrSelected =ips_defualtSeleced.split(',');
			for(var a=0; a <groupSus__webStudio.IPSStore.getCount();a++){
				var vvv_record=groupSus__webStudio.IPSStore.getAt(a);
				var _iip=vvv_record.data.iip;
				for(var b=0;b<iipArrSelected.length;b++){
					if(_iip== iipArrSelected[b]){
						checkedArr.push(vvv_record);
					}
				}
			}
			IPSGrid.getSelectionModel().select(checkedArr);
		}	
}


/***
*@desc 根据环境名获得envid
*/
function getEnvNameByEnvid(envname){
	var data = groupSus__webStudio.envGrid.getSelectionModel().getSelection();
	var envid=-1;
	var rs_envid=-1;
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
		var _tempIname =data[i].get('iname');
		
		if(_tempIname == envname){
			rs_envid=envid;
			break;
		}
    }
	return rs_envid;
}

/**
* @根据 表格面板，获得它某个colums的store
**/
function getGridPanelColumsStore(scrGridPanel){
	var columns = scrGridPanel.columns;
          if (columns ) {
              for (var i = 1; i < columns.length; i++) {
                  var column = columns[i];
                  if (column.text=='资源组') {
                      var editor = column.editor;
                      if (editor) {
                          return editor.getStore();
                      }
                  }
              }
          }
}


function getiwIResSore2(){
	var tempenvid=getEnvNameByEnvid(groupSus__webStudio.iwGrid.initialConfig.title) ;
	var tempVaL =groupSus__webStudio.iwresMap.get(tempenvid);
	return tempVaL;
}

/**
* @加载资源组store
**/
function loadIresStore(iwResStore){			
	var tempenvid=getEnvNameByEnvid(groupSus__webStudio.iwGrid.initialConfig.title) ;
	var tempVaL =groupSus__webStudio.iwresMap.get(tempenvid);
	if(null==tempVaL){
		groupSus__webStudio.iwresMap.add(tempenvid,iwResStore);
		iwResStore.load();
	}
}

function gdITILBgDhJy(){
		var itaskId = form.getForm().findField("form_ITIL_input").getValue();
		if(itaskId==""||itaskId==null){
			form.getForm().findField("GDEAPSId").setValue("");	
		}else{
					
			Ext.Ajax.request({
							url : 'getBusNumIsExist.do',
							timeout : 30000,
							params : {
								ITILBusNum : itaskId
							},
							async: true,
							method : 'POST',
							success : function(response, opts) {

								ITILBusNumExist = Ext.decode (response.responseText).success;
								var iidString =  Ext.decode (response.responseText).iiString; 

								if(ITILBusNumExist){
										form.getForm().findField("GDEAPSId").setValue(iidString);									
								}else{
										form.getForm().findField("GDEAPSId").setValue("投产编号不存在！");
								}
							    							
							},
							failure : function(result, request) {
								form.getForm().findField("ysbFileName").setValue("");
								secureFilterRs(result, "ITIL单号校验失败！");
							}
						});
		}

}