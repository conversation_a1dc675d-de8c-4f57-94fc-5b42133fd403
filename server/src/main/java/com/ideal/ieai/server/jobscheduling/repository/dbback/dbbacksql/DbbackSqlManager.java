/**  
 * All rights Reserved, Designed By www.idealinfo.com
 * @Title:  DbbackSqlManager.java   
 * @Package com.ideal.ieai.server.jobScheduling.repository.dbback.dbbacksql   
 * @Description:  数据库恢复备份SQL文管理   
 * @author: 理想科技     
 * @date:   2019年6月12日 下午4:24:38   
 * @version V1.0 
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
package com.ideal.ieai.server.jobscheduling.repository.dbback.dbbacksql;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.jobscheduling.repository.dbback.dbinfomaintenance.DbInfoMaintenanceBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;

/**   
 * @ClassName:  DbbackSqlManager   
 * @Description:数据库恢复备份SQL文管理   
 * @author: junyu_zhang 
 * @date:   2019年6月12日 下午4:24:38   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class DbbackSqlManager
{
    

    private static final Logger  log       = Logger.getLogger(DbbackSqlManager.class);
    
    private static final String METHODSTR = "method :"; 
    private static final String CIDBINSNAME = "IDBINSNAME"; 
    
    private static final DbbackSqlManager instance = new DbbackSqlManager();
    
    public static DbbackSqlManager getInstance(){
        
        return instance;
    }
    
    /**
     * 
     * @Title: queryDBBackSQLStoreList   
     * @Description: 查询   
     * @param sqlName
     * @param orderString
     * @param lapMap
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月19日 下午1:54:11
     */
    public Map<String ,Object> queryDBBackSQLStoreList(String sqlName,String orderString,Map<String, Integer> lapMap) throws RepositoryException{
        
        List<DbbackSqlInfo> resultList = new ArrayList<DbbackSqlInfo>();
        int total = 0;
        Map<String ,Object> rsMap = new HashMap<String ,Object>();
        
        for (int i = 0;; i++){
            try{ 
                 //设置分页信息
                 int pageNum = Integer.parseInt(lapMap.get("page").toString());
                 int limitNum = Integer.parseInt(lapMap.get("limit").toString());
                 int pageF = (limitNum * (pageNum - 1)) + 1;
                 int pageT = limitNum * pageNum;
                 Connection conn = null; 
                 PreparedStatement ps = null;
                 PreparedStatement psc = null;
                 ResultSet rset = null;
                 ResultSet rsetc = null;
                 StringBuilder sqlc = new StringBuilder();
                 StringBuilder sql = new StringBuilder();
                  sqlc.append("SELECT COUNT(*) AS RCOUNT FROM IEAI_BACKUP_SQL S WHERE 1 = 1 ");
                 String sqls = " SELECT * FROM ( SELECT AT.* , ROWNUM AS RN FROM (";
                  sql.append("SELECT S.IID,S.ISQLNAME,S.ISQLCONTENT FROM IEAI_BACKUP_SQL S WHERE 1 = 1");
                 //设置查询条件
                 if(sqlName != null && !"".equals(sqlName)){
                     
                     sql.append(" AND S.ISQLNAME LIKE '%" + sqlName + "%'");
                     sqlc.append(" AND S.ISQLNAME LIKE '%" + sqlName + "%'");
                 }
                 
                 String sqle = orderString + " ) AT  WHERE ROWNUM <= " + pageT + ") WHERE RN >= "+ pageF;
                 
                 String sqlAll = sqls + sql.toString() + sqle;
                 try
                 {
                     //获取数据库connect连接
                     conn = DBResource.getConnection("queryDBBackSQLStoreList", log, Constants.IEAI_IEAI);
                     ps = conn.prepareStatement(sqlAll);
                     psc = conn.prepareStatement(sqlc.toString());
                     
                     rset = ps.executeQuery();
                     rsetc = psc.executeQuery();
                     while (rset.next())
                     {
                         DbbackSqlInfo ojectInfo = new DbbackSqlInfo();
                         ojectInfo.setIid(rset.getLong("IID"));
                         ojectInfo.setIsqlName(rset.getString("ISQLNAME"));
                         ojectInfo.setIsqlContent(rset.getString("ISQLCONTENT"));
                         resultList.add(ojectInfo);
                     }
                     
                     while (rsetc.next())
                     {
                         total = rsetc.getInt(1);
                     }
                     
                 } catch (SQLException e)
                 {
                     log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                 }finally
                 {
                     DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                     DBResource.closePSRS(rsetc, psc, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                 }
                 break;
            }catch(RepositoryException ex){
                
                DBRetryUtil.waitForNextTry(i, ex);
            }
         }
        //返回备份恢复规则 信息
        rsMap.put("total", total);
        rsMap.put("dataList", resultList);
        return rsMap;
    }
    
    /**
     * 
     * @Title: queryDBBackAppliobjStoreList   
     * @Description: 查询应用对象信息 
     * @param sysName
     * @param orderString
     * @param lapMap
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月20日 下午1:33:16
     */
    public Map<String ,Object> queryDBBackAppliobjStoreList(String sysName,String orderString,Map<String, Integer> lapMap) throws RepositoryException{
        
        List<DbbackApplicobjInfo> resultList = new ArrayList<DbbackApplicobjInfo>();
        int total = 0;
        Map<String ,Object> rsMap = new HashMap<String ,Object>();
        
        for (int i = 0;; i++){
            try{ 
                 //设置分页信息
                 int pageNum = Integer.parseInt(lapMap.get("page").toString());
                 int limitNum = Integer.parseInt(lapMap.get("limit").toString());
                 int pageF = (limitNum * (pageNum - 1)) + 1;
                 int pageT = limitNum * pageNum;
                 Connection conn = null; 
                 PreparedStatement ps = null;
                 PreparedStatement psc = null;
                 ResultSet rset = null;
                 ResultSet rsetc = null;
                 StringBuilder sqlc = new StringBuilder();
                 StringBuilder sql = new StringBuilder();
                 sqlc.append("SELECT COUNT(*) AS RCOUNT FROM IEAI_BACKUP_APPLICOBJ A LEFT JOIN IEAI_BACKDB_SYSTEM S ON A.ISYSID = S.IID WHERE 1 = 1 ");
                 String sqls = " SELECT * FROM (SELECT AT.* ,ROWNUM AS RN FROM (";
                  sql.append("SELECT A.IID,A.ISYSID,A.IDBINSNAME,A.IDBID,A.IOWNER,A.ITABLENAME,A.IROWNUM,A.ICREATETIME,S.ISYSTEMNAME "
                         + " FROM IEAI_BACKUP_APPLICOBJ A  LEFT JOIN IEAI_BACKDB_SYSTEM S   ON A.ISYSID = S.IID WHERE 1 = 1 ");
                 //设置查询条件
                 if(sysName != null && !"".equals(sysName)){
                     
                     sql.append(" AND  S.ISYSTEMNAME LIKE '%" + sysName + "%'");
                     sqlc.append(" AND  S.ISYSTEMNAME LIKE '%" + sysName + "%'");
                 }
                 
                 String sqle = orderString + " ) AT  WHERE ROWNUM <= " + pageT + ")  WHERE RN >= "+ pageF;
                 
                 String sqlAll = sqls + sql.toString() + sqle;
                 try
                 {
                     //获取数据库connect连接
                     conn = DBResource.getConnection("queryDBBackAppliobjStoreList", log, Constants.IEAI_IEAI);
                     ps = conn.prepareStatement(sqlAll);
                     psc = conn.prepareStatement(sqlc.toString());
                     
                     rset = ps.executeQuery();
                     rsetc = psc.executeQuery();
                     while (rset.next())
                     {
                         DbbackApplicobjInfo ojectInfo = new DbbackApplicobjInfo();
                         ojectInfo.setIid(rset.getLong("IID"));
                         ojectInfo.setIdbinsName(rset.getString(CIDBINSNAME));
                         ojectInfo.setIdbId(rset.getLong("IDBID"));
                         ojectInfo.setIowner(rset.getString("IOWNER"));
                         ojectInfo.setIrownum(rset.getLong("IROWNUM"));
                         ojectInfo.setIsysId(rset.getLong("ISYSID"));
                         ojectInfo.setItableName(rset.getString("ITABLENAME"));
                         ojectInfo.setIsysName(rset.getString("ISYSTEMNAME"));
                         resultList.add(ojectInfo);
                     }
                     
                     while (rsetc.next())
                     {
                         total = rsetc.getInt(1);
                     }
                     
                 } catch (SQLException e)
                 {
                     log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                 }finally
                 {
                     DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                     DBResource.closePSRS(rsetc, psc, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                 }
                 break;
            }catch(RepositoryException ex){
                
                DBRetryUtil.waitForNextTry(i, ex);
            }
         }
        //返回备份恢复规则 信息
        rsMap.put("total", total);
        rsMap.put("dataList", resultList);
        return rsMap;
    }
    
    
    /**
     * 
     * @Title: exportDBBackAppliobjStoreList   
     * @Description: 数据库对象信息导出
     * @param sysName
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年8月13日 下午2:58:10
     */
    public List<DbbackApplicobjInfo> exportDBBackAppliobjStoreList(String sysName) throws RepositoryException{
        
        List<DbbackApplicobjInfo> resultList = new ArrayList<DbbackApplicobjInfo>();
        
        for (int i = 0;; i++){
            try{ 
                 //设置分页信息
                 int pageF = 0;
                 int pageT = 10000;
                 Connection conn = null; 
                 PreparedStatement ps = null;
                 ResultSet rset = null;
                 StringBuilder sql = new StringBuilder();
                 String sqls = " SELECT * FROM (SELECT AT.* ,ROWNUM AS RN FROM (";
                  sql.append("SELECT A.IID,A.ISYSID,A.IDBINSNAME,A.IDBID,A.IOWNER,A.ITABLENAME,A.IROWNUM,A.ICREATETIME,S.ISYSTEMNAME "
                         + " FROM IEAI_BACKUP_APPLICOBJ A  LEFT JOIN IEAI_BACKDB_SYSTEM S   ON A.ISYSID = S.IID WHERE 1 = 1 ");
                 //设置查询条件
                 if(sysName != null && !"".equals(sysName)){
                     
                     sql.append(" AND S.ISYSTEMNAME LIKE '%" + sysName + "%'");
                 }
                 
                 String sqle =  " ORDER BY A.ISYSID, A.IDBINSNAME DESC ) AT  WHERE ROWNUM <= " + pageT + ")  WHERE RN >= "+ pageF;
                 
                 String sqlAll = sqls + sql.toString() + sqle;
                 try
                 {
                     //获取数据库connect连接
                     conn = DBResource.getConnection("exportDBBackAppliobjStoreList", log, Constants.IEAI_IEAI);
                     ps = conn.prepareStatement(sqlAll);
                     
                     rset = ps.executeQuery();
                     while (rset.next())
                     {
                         DbbackApplicobjInfo ojectInfo = new DbbackApplicobjInfo();
                         ojectInfo.setIid(rset.getLong("IID"));
                         ojectInfo.setIdbinsName(rset.getString(CIDBINSNAME));
                         ojectInfo.setIdbId(rset.getLong("IDBID"));
                         ojectInfo.setIowner(rset.getString("IOWNER"));
                         ojectInfo.setIrownum(rset.getLong("IROWNUM"));
                         ojectInfo.setIsysId(rset.getLong("ISYSID"));
                         ojectInfo.setItableName(rset.getString("ITABLENAME"));
                         ojectInfo.setIsysName(rset.getString("ISYSTEMNAME"));
                         resultList.add(ojectInfo);
                     }
                     
                 } catch (SQLException e)
                 {
                     log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                 }finally
                 {
                     DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                 }
                 break;
            }catch(RepositoryException ex){
                
                DBRetryUtil.waitForNextTry(i, ex);
            }
         }
        
        return resultList;
    }
    
    
    
    
    /**
     * 
     * @Title: saveDBBackSql   
     * @Description:保存   
     * @param list
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月19日 下午1:53:58
     */
    public void saveDBBackSql(List<DbbackSqlInfo> list) throws RepositoryException{
        for (int i = 0;; i++) {
          try {
                Connection conn = null; 
                PreparedStatement ps = null;
                
                PreparedStatement psu = null;
                
                String sql1= " INSERT INTO IEAI_BACKUP_SQL (IID,ISQLNAME,ISQLCONTENT,IDESC) VALUES (?,?,?,?) ";
                
                String sql3 = " UPDATE IEAI_BACKUP_SQL S SET S.ISQLNAME =? , S.ISQLCONTENT = ?  WHERE S.IID = ?";
                
                try
                {
                    conn = DBResource.getConnection("saveDBBackSql", log, Constants.IEAI_IEAI);
                    //插入备份恢复规则
                    ps = conn.prepareStatement(sql1);
                    //更新备份恢复规则
                    psu = conn.prepareStatement(sql3);
                       //遍历备份规则对象list
                        for(int n = 0 ; n < list.size(); n++){
                            
                            DbbackSqlInfo dbOjectInfo =  list.get(n);
                                 //页面恢复备份规则保存
                            long iid = dbOjectInfo.getIid();
                             if(iid > 0){
                                 updateDbbackSql(dbOjectInfo, psu, iid);
                             }else{
                                 
                                 insertDbbackSql(conn, dbOjectInfo, ps);
                             }
                        }
                        ps.executeBatch();
                        psu.executeBatch();
                    
                    conn.commit();
                    
                } catch (SQLException e)
                {
                    log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                }finally
                {
                    DBResource.closePSConn(conn, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                    DBResource.closePreparedStatement(psu, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
                break;
            }catch(RepositoryException ex){
                
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }
    /**
     * 
     * @Title: updateDbbackSql   
     * @Description: 更新
     * @param dbOjectInfo
     * @param psu
     * @param iid
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月19日 下午1:53:46
     */
    private void updateDbbackSql(DbbackSqlInfo dbOjectInfo,PreparedStatement psu,long iid) throws RepositoryException{
        
        try
        {
            psu.setString(1, dbOjectInfo.getIsqlName());
            psu.setString(2,dbOjectInfo.getIsqlContent());
            psu.setLong(3, iid);
            psu.addBatch();
        } catch (SQLException e)
        {
            log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        }
       
    }
    /**
     * 
     * @Title: insertDbbackSql   
     * @Description:新增 
     * @param conn
     * @param dbOjectInfo
     * @param ps
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月19日 下午1:53:35
     */
    private long  insertDbbackSql(Connection conn,DbbackSqlInfo dbOjectInfo,PreparedStatement ps) throws RepositoryException{
        
        long iid = 0;
        //插入备份恢复规则
        try
        {
            iid = IdGenerator.createId("IEAI_BACKUP_SQL", conn);
            ps.setLong(1, iid);
            ps.setString(2, dbOjectInfo.getIsqlName());
            ps.setString(3, dbOjectInfo.getIsqlContent());
            ps.setString(4, "");
            ps.addBatch();
            
        } catch (SQLException e)
        {
            log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT); 
        }
        
        return iid;
       
    }
    
  /**
   * 
   * @Title: deleteDBBackSql   
   * @Description: 删除  
   * @param deleteIds
   * @return
   * @throws RepositoryException      
   * @author: junyu_zhang 
   * @date:   2019年6月19日 下午1:58:28
   */
  public boolean deleteDBBackSql( Long[] deleteIds) throws RepositoryException{
        
        boolean flag =true;
        if (deleteIds.length == 0)
        {
            return flag;
        }
        for (int i = 0;; i++) {
            String sql = "DELETE FROM IEAI_BACKUP_SQL S WHERE S.IID IN ("
                    + StringUtils.join(deleteIds, ",") + ")";
    
            try{
                PreparedStatement actStat = null;
                ResultSet rs = null;
                Connection con = null;
                
                try{
                    con = DBResource.getConnection("deleteDBBackSql", log, Constants.IEAI_IEAI);
                    //删除备份恢复规则
                    actStat = con.prepareStatement(sql);
                    actStat.executeUpdate();
                  
                
                    con.commit();
                     
                }catch(SQLException e){
                    
                    flag = false;
                    log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                }finally
                {
                    DBResource.closeConn(con, rs, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
                break;
            }catch(RepositoryException ex){
                
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        
        return flag;
        
    }
  
  
     
   public List<DbbackApplicobjInfo> getDbbackApplicobjInfoList(String sql,Connection conn,long isysId){
       
       List<DbbackApplicobjInfo> list = new ArrayList<DbbackApplicobjInfo>();
       
       PreparedStatement ps = null;
       ResultSet rset = null;
       
       
       try
        {
            ps = conn.prepareStatement(sql);
            rset = ps.executeQuery();
            
            while(rset.next()){
                DbbackApplicobjInfo applicobjInfo = new DbbackApplicobjInfo();
                applicobjInfo.setIdbId(rset.getLong("dbid"));
                applicobjInfo.setIsysId(isysId);
                applicobjInfo.setIowner(rset.getString("owner"));
                applicobjInfo.setIrownum(rset.getLong("num_rows"));
                applicobjInfo.setItableName(rset.getString("table_name"));
                applicobjInfo.setIdbinsName(rset.getString("name"));
                list.add(applicobjInfo);
            }
        } catch (SQLException e)
        {
            log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }finally{
            
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
       
       return list;
       
   }
   
   /**
    * 
    * @Title: insertDbbackApplicobj   
    * @Description:保存应用对象信息
    * @param list
    * @throws RepositoryException      
    * @author: junyu_zhang 
    * @date:   2019年6月20日 下午1:34:09
    */
    public void insertDbbackApplicobj(List<DbbackApplicobjInfo>list) throws RepositoryException{
        
        

        for (int i = 0;; i++) {
          try {
                Connection conn = null; 
                PreparedStatement ps = null;
                
                
                PreparedStatement trunStmt = null;
                
                String sql1= " INSERT INTO IEAI_BACKUP_APPLICOBJ (IID,ISYSID,IDBINSNAME,IDBID,IOWNER,ITABLENAME,IROWNUM,ICREATETIME,IDESC) VALUES (?,?,?,?,?,?,?,?,?) ";
                
                String truncateSql = " TRUNCATE  TABLE  IEAI_BACKUP_APPLICOBJ";
                
                try
                {
                    conn = DBResource.getConnection("insertDbbackApplicobj", log, Constants.IEAI_IEAI);
              
                    //保存前删除数据
                    trunStmt = conn.prepareStatement(truncateSql);
                    trunStmt.executeUpdate();
                    //插入应用对象信息
                    ps = conn.prepareStatement(sql1);
                    //遍历备份规则对象list
                    long index = 0;
                    for(int n = 0 ; n < list.size(); n++){
                       DbbackApplicobjInfo applicobjInfo = list.get(n);
                       long  iid = IdGenerator.createId("IEAI_BACKUP_APPLICOBJ", conn);
                       ps.setLong(1, iid);
                       ps.setLong(2, applicobjInfo.getIsysId());
                       ps.setString(3, applicobjInfo.getIdbinsName());
                       ps.setLong(4, applicobjInfo.getIdbId());
                       ps.setString(5, applicobjInfo.getIowner());
                       ps.setString(6, applicobjInfo.getItableName());
                       ps.setLong(7, applicobjInfo.getIrownum());
                       ps.setLong(8, System.currentTimeMillis());
                       ps.setString(9, "");
                       ps.addBatch();
                       if (index % 1000 == 0)
                       {
                           ps.executeBatch();
                           ps.clearBatch();
                       }
                       index++;
                    }
                    ps.executeBatch();
                    
                    conn.commit();
                    
                } catch (SQLException e)
                {
                    log.error(METHODSTR +Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                }finally
                {
                    DBResource.closePreparedStatement(trunStmt, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                    DBResource.closePSConn(conn, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
                break;
            }catch(RepositoryException ex){
                
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    
    }
    /**
     * 
     * @Title: getMainSql   
     * @Description:获取查询应用对象信息主SQL
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月20日 上午10:26:35
     */
    public String getMainSql() throws RepositoryException{
        
        
        String imainSql  = "";
        for (int i = 0;; i++)
        {
            Connection conn = null;
            PreparedStatement actStat = null;
            ResultSet actRS = null;
            try
            {
                String sql = "SELECT S.IID,S.ISQLNAME,S.ISQLCONTENT  FROM IEAI_BACKUP_SQL S WHERE  ROWNUM = 1";
                try
                {
                    // 获取数据库连接
                    conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_IEAI);
                    actStat = conn.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        imainSql = actRS.getString("ISQLCONTENT");
                    }
                } catch (SQLException e)
                {
                    log.error(METHODSTR + Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                } finally
                {
                    DBResource.closeConn(conn, actRS, actStat, "getMainSql", log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return imainSql;
    }
    /**
     * 
     * @Title: getDbBackInfoList   
     * @Description: 获取备份数据库信息 
     * @return
     * @throws RepositoryException      
     * @author: junyu_zhang 
     * @date:   2019年6月20日 下午1:34:35
     */
    public List<DbInfoMaintenanceBean> getDbBackInfoList () throws RepositoryException
    {
  
        List<DbInfoMaintenanceBean> res = null;
        for(int i = 0;;i++){
            try{
                res = getDbBackInfoListSub();
                break;
               } catch (RepositoryException ex)
               {
                DBRetryUtil.waitForNextTry(i, ex);
               }
        }
        return res;
    }
    
    private List<DbInfoMaintenanceBean> getDbBackInfoListSub() throws RepositoryException{
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<DbInfoMaintenanceBean> res = new ArrayList<DbInfoMaintenanceBean>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        ResultSet rsCount = null;
        try
        {
            conn = DBResource.getConnection(method, log, Constants.IEAI_IEAI);
            //查询备份数据库
           String sql =   "SELECT T.IID,T.IDBNAME,T.IDBID,T.IDBIP,T.IDBPORT,T.IDBINSNAME,T.IDBUSER,T.IDBPASS, "
                        + "T.ISTATES,T.ISYSTEMID FROM IEAI_BACKDB_DBINFO T LEFT JOIN IEAI_BACKDB_SYSTEM S "
                        + "ON  T.ISYSTEMID = S.IID WHERE 1=1  AND T.IDBTYPE = 1";
            ps = conn.prepareStatement(sql);
            rs= ps.executeQuery();
            while(rs.next()){
                DbInfoMaintenanceBean dbInfoMaintenanceBean = new DbInfoMaintenanceBean();
                dbInfoMaintenanceBean.setIid(rs.getLong("IID"));
                dbInfoMaintenanceBean.setIdbinsname(rs.getString(CIDBINSNAME));
                dbInfoMaintenanceBean.setIdbip(rs.getString("IDBIP"));
                dbInfoMaintenanceBean.setIsystemid(rs.getLong("ISYSTEMID"));
                dbInfoMaintenanceBean.setIdbport(rs.getLong("IDBPORT"));
                dbInfoMaintenanceBean.setIdbuser(rs.getString("IDBUSER"));
                dbInfoMaintenanceBean.setIdbpassword(rs.getString("IDBPASS"));
                
                res.add(dbInfoMaintenanceBean);
            }
           
        } catch (SQLException e)
        {
            log.error(METHODSTR + Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, log);
            DBResource.closeResultSet(rsCount, method, log);
        }
        
        return res;
    }
}
