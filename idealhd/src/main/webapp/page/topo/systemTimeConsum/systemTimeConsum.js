Date.prototype.format = function(format) {
	var o = {
		"M+" : this.getMonth() + 1, // month
		"d+" : this.getDate(), // day
		"h+" : this.getHours(), // hour
		"m+" : this.getMinutes(), // minute
		"s+" : this.getSeconds(), // second
		"q+" : Math.floor((this.getMonth() + 3) / 3), // quarter
		"S" : this.getMilliseconds()
	// millisecond
	}
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (this.getFullYear() + "")
				.substr(4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp("(" + k + ")").test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k]
					: ("00" + o[k]).substr(("" + o[k]).length));
	return format;
}
var systimeconsum_Window;
Ext.onReady(function() {
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	Ext.define('ImportModel', {
		extend : 'Ext.data.Model',
		fields : [ 
		{
			name : 'iid',
			type : 'long'
		}, 
		{
			name : 'systemname',
			type : 'string'
		}, {
			name : 'date',
			type : 'string'
		}, {
			name : 'opentime',
			type : 'string'
		}, {
			name : 'arrivetime',
			type : 'string'
		}, {
			name : 'finishCount',
			type : 'string'
		}, {
			name : 'failCount',
			type : 'string'
		}, {
			name : 'state',
			type : 'string'
		}, {
			name : 'runTime',
			type : 'string'
		}]
	});

	var columns = [ {
		xtype : 'rownumberer',
		header:'序号',
		width : 40
	},
	{
		header:'iid',
		dataIndex : 'iid',
		hidden:true
	},
	{
		text : '系统名称',
		dataIndex : 'systemname',
		width : 120,
		flex:1,
	    renderer : function(value, metadata) {
	        if(value) {
	          var n = value.replace(new RegExp(',','gm'),'<br>');
	          metadata.tdAttr = 'data-qtip="' + n + '"';
	        }
	        return value;
	      }
	}, {
		text : '数据日期',
		dataIndex : 'date',
		width : 120,
	    renderer : function(value, metadata) {
	        if(value) {
	          var n = value.replace(new RegExp(',','gm'),'<br>');
	          metadata.tdAttr = 'data-qtip="' + n + '"';
	        }
	        return value;
	      }
	}, {
		text : '开始时间',
		dataIndex : 'opentime',
		width : 140,
	    renderer : function(value, metadata) {
	        if(value) {
	          var n = value.replace(new RegExp(',','gm'),'<br>');
	          metadata.tdAttr = 'data-qtip="' + n + '"';
	        }
	        return value;
	      }
	}, {
		text : '结束时间',
		dataIndex : 'arrivetime',
		width : 140,
	    renderer : function(value, metadata) {
	        if(value) {
	          var n = value.replace(new RegExp(',','gm'),'<br>');
	          metadata.tdAttr = 'data-qtip="' + n + '"';
	        }
	        return value;
	      }
	},{
		text : '完成作业数',
		width : 120,
		dataIndex : 'finishCount',
		 renderer : function(value, metadata) {
		        if(value) {
		          var n = value.replace(new RegExp(',','gm'),'<br>');
		          metadata.tdAttr = 'data-qtip="' + n + '"';
		        }
		        return value;
		      }
	},{
		text : '异常作业数',
		width : 120,
		dataIndex : 'failCount',
		 renderer : function(value, metadata) {
		        if(value) {
		          var n = value.replace(new RegExp(',','gm'),'<br>');
		          metadata.tdAttr = 'data-qtip="' + n + '"';
		        }
		        return value;
		      }
	},{
		text : '运行耗时',
		width : 120,
		dataIndex : 'runTime',
		 renderer : function(value, metadata) {
		        if(value) {
		          var n = value.replace(new RegExp(',','gm'),'<br>');
		          metadata.tdAttr = 'data-qtip="' + n + '"';
		        }
		        return value;
		      }
	},{
		text : '运行状态',
		width : 120,
		dataIndex : 'state',
		 renderer : function(value, metadata) {
		        if(value) {
		          var n = value.replace(new RegExp(',','gm'),'<br>');
		          metadata.tdAttr = 'data-qtip="' + n + '"';
		        }
		        return value;
		      }
	}];
	
	var store = Ext.create('Ext.data.Store', {
		pageSize: 25,
		autoLoad : false,
		model : 'ImportModel',
		proxy : {
			type : 'ajax',
			url : 'querySysTimeConsumInfo.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty: 'totals'
			}
		} 
	});

	var store1 = Ext.create('Ext.data.Store', {
		autoLoad : true,
		model : 'ImportModel',
		proxy : {
			type : 'ajax',
			url : 'queryBussPrjName.do',
			reader : {
				type : 'json',
				root : 'prjList'
			}
		}
	});

	var pageBar = Ext.create('Ext.PagingToolbar', {
    	pageSize: 25,
        store: store,
        displayInfo: true,
        displayMsg: '显示{0}-{1}条，共{2}条',
        emptyMsg: "没有数据"
    })
    var exportBtn = Ext.create('Ext.Button', {
		text : '导出',
		margin:'0 0 0 10',
		handler : exportExcel
	});
	var graphBtn = Ext.create('Ext.Button', {
			text : '耗时曲线',
			margin:'0 0 0 10',
			handler : systemGraph
		});
	var store_calculation = Ext.create('Ext.data.Store', {
		fields: ['abbr', 'name'],
		data:[
		        {"abbr":"-1", "name":"无"},
		        {"abbr":"1", "name":"周一"},
		        {"abbr":"2", "name":"周二"},
		        {"abbr":"3", "name":"周三"},
		        {"abbr":"4", "name":"周四"},
		        {"abbr":"5", "name":"周五"},
		        {"abbr":"6", "name":"周六"},
		        {"abbr":"7", "name":"周日"},
		        {"abbr":"8", "name":"月末"},
		        {"abbr":"9", "name":"季末"}	        
		     ]
	});
	
	var combobox_calculation = Ext.create('Ext.form.field.ComboBox', {
		columnWidth : '.15',
		labelWidth : 40,
		padding : '0 10 5 0',
		fieldLabel : '维度',
		displayField : 'name',
		valueField : 'abbr',
		name:'rule',
		editable : true,
		store : store_calculation,
		queryMode : 'local',
		listeners : {}
	});
	var form = Ext.create('Ext.form.Panel', {
		region:'north',
		border: false,
		bodyPadding: 8,
	    layout: 'column',
	    items: [{
	    	labelWidth : 60,
	    	xtype: 'combo',
	        fieldLabel: '系统名称',
	        columnWidth : '.4',
	        name: 'prjName',
	        store: store1,
	        displayField:'systemname',
	        queryModel:'local'
	    },{
			labelWidth : 60,
			xtype : 'datefield',
			columnWidth : '.21',
			fieldLabel : '批量日期',
			name : 'startTime1',
			value:new Date(),
			maxValue : new Date(),
			format : 'Y-m-d',
			editable : false,
			padding : '0 5 0 5'
		},{
			labelWidth : 60,
			xtype : 'datefield',
			columnWidth : '.21',
			fieldLabel : '至',
			name : 'startTime2',
			value:new Date(),
			maxValue : new Date(),
			format : 'Y-m-d',
			editable : false,
			padding : '0 5 0 0'
		},combobox_calculation,{
	    	xtype: 'button',
	    	text: '查询',
	    	margin:'0 0 0 10',
			handler: queryData
	    },exportBtn,graphBtn]
	});

	store.on('beforeload', function (store, options) {
	    var new_params = {  
	    		prjName: form.getForm().findField('prjName').getValue(),
	    		startTime1: form.getForm().findField('startTime1').getValue().format('yyyy-MM-dd'),
	    		startTime2: form.getForm().findField('startTime2').getValue().format('yyyy-MM-dd'),
	    		dateType: form.getForm().findField('rule').getValue()
	    };
	    Ext.apply(store.proxy.extraParams, new_params);
	    
    });
	store1.on('beforeload', function (store, options) {
	    var new_params = {  
	    };
	    Ext.apply(store.proxy.extraParams, new_params);
	    
    });
	var grid = Ext.create('Ext.grid.Panel', {
		store : store,
		region:'center',
		border : true,
		columns : columns,
        bbar: pageBar
	});
	var mainPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		height:contentPanel.getHeight()-modelHeigth,
		width:contentPanel.getWidth(),
		layout:'border',
		items: [form,grid],
//		items : [ {
//			title : '耗时统计报表',
//			items : [ form, grid ]
//		}, {
//			title : '耗时统计图',
//			items : [grapGrid ]
//		} ],
	    renderTo : "systemTimeConsum_grid_area"
	});

	contentPanel.on("resize",function(){
		mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
		mainPanel.setWidth(contentPanel.getWidth());
	});
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy(mainPanel);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
	 });
	
	function queryData() {
    	var prjName = form.getForm().findField('prjName').getValue();
    	var startTime1= form.getForm().findField('startTime1').getValue();
    	var startTime2=form.getForm().findField('startTime2').getValue();
    	if(prjName=='' || null==prjName){
    		Ext.Msg.alert("消息提示","请选择系统名称!");
    		return;
    	}
    	if(startTime1==''||startTime2==''){
    		Ext.Msg.alert("消息提示","开始时间和结束时间不允许为空!");
    		return;
    	}
    	store.load({
    		params: {start: 0, limit: 25}
    	});
	}
	
	function exportExcel(){
		var prjName=form.getForm().findField('prjName').getValue();
		var startTime1=form.getForm().findField('startTime1').getValue().format('yyyy-MM-dd');
		var startTime2=form.getForm().findField('startTime2').getValue().format('yyyy-MM-dd');
		var dateType = form.getForm().findField('rule').getValue();
    	if(prjName=='' || null==prjName){
    		Ext.Msg.alert("消息提示","请选择系统名称!");
    		return;
    	}
    	if(startTime1==''||startTime2==''){
    		Ext.Msg.alert("消息提示","开始时间和结束时间不允许为空!");
    		return;
    	}
		window.location.href = encodeURI('exportSysTimeConsum.do?iname='+prjName+"&s1="+startTime1+"&s2="+startTime2+"&dateType="+dateType);
	}
	
	function systemGraph() {
		var prjName=form.getForm().findField('prjName').getValue();
		var startTime1=form.getForm().findField('startTime1').getValue().format('yyyy-MM-dd');
		var startTime2=form.getForm().findField('startTime2').getValue().format('yyyy-MM-dd');
		var dateType = form.getForm().findField('rule').getValue();
    	if(prjName=='' || null==prjName){
    		Ext.Msg.alert("消息提示","请选择系统名称!");
    		return;
    	}
    	if(startTime1==''||startTime2==''){
    		Ext.Msg.alert("消息提示","开始时间和结束时间不允许为空!");
    		return;
    	}
		systimeconsum_Window = Ext.create ('Ext.window.Window',
				{
				    title : '系统耗时统计',
				    modal : true,
				    closeAction : 'destroy',
				    constrain : true,
				    autoScroll : true,
				    width : contentPanel.getWidth()-modelHeigth,
				    height : contentPanel.getHeight()-modelHeigth,
				    draggable : false,// 禁止拖动
				    resizable : false,// 禁止缩放
				    layout : 'fit',
				    loader :
				    {
				        url : 'systemTimeConsum_win.do',
				         params :
		                 {
				        	prjName:prjName,
				     		startTime1:startTime1,
				     		startTime2:startTime2,
				     		dateType:dateType,
				     		startPage:store.currentPage,
				     		endPage:store.pageSize
		                 },
				        autoLoad : true,
				        scripts : true
				    }
				});
		systimeconsum_Window.show();
	}
	
});
