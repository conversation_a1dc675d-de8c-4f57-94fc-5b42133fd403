package com.ideal.ieai.prv.provider.scriptManager;

import com.alibaba.fastjson.JSON;
import com.ideal.dubbo.interfaces.scriptManager.IAuditing;
import com.ideal.dubbo.models.IeaiScriptItsmpublish;
import com.ideal.dubbo.models.ScriptParameter;
import com.ideal.dubbo.models.product.IeaiScriptAttachment;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.prv.provider.ScriptServiceManage;
import com.ideal.ieai.prv.provider.scriptManager.orderNumber.OrderValidData;
import com.ideal.ieai.prv.provider.scriptManager.orderNumber.OrderValidResp;
import com.ideal.ieai.prv.provider.utils.ParseJson;
import com.ideal.ieai.prv.repository.hd.dubbo.taskexec.QuartZManager;
import com.ideal.ieai.prv.repository.hd.scriptAuditing.ScriptAuditingManager;
import com.ideal.ieai.server.emergency.repository.emins.EmInstanceConfigManager;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.itsmutil.ApiResData;
import com.ideal.ieai.server.itsmutil.ApiUtil;
import com.ideal.ieai.server.itsmutil.EventBean;
import com.ideal.ieai.server.itsmutil.ItsmSystemFlag;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.user.RepUserBack;
import com.ideal.ieai.server.repository.user.UserManager;
import com.ideal.ieai.server.util.CronUtil;
import com.ideal.ieai.server.util.HttpUtils;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.http.client.ClientProtocolException;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.rmi.ServerException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@DubboService
public class AuditingImpl implements IAuditing
{
    private static final Logger logger = Logger.getLogger(AuditingImpl.class);

    @Override
    public List getPublishAuditorList ( int auditingType, String userId, Integer dbaasFlag )
    {
        List res = new ArrayList();
        try
        {
            if (dbaasFlag != null && dbaasFlag == 1)
            {
                res = ScriptAuditingManager.getInstance().getPublishAuditorListDbaas(auditingType, userId);
            } else
            {
                res = ScriptAuditingManager.getInstance().getPublishAuditorList(auditingType, userId);
            }
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }
    
    @Override
    public List getPublishSyncAuditorList (String userId)
    {
        List res = new ArrayList();
        try
        {
                res = ScriptAuditingManager.getInstance().getPublishSyncAuditorList(userId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public String getGroupLeader ( int auditingType, String userId )
    {
        String res = "";
        try
        {
            res = ScriptAuditingManager.getInstance().getGroupLeader(auditingType, userId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public List getAppSysList ()
    {
        List res = new ArrayList();
        try
        {
            res = ScriptAuditingManager.getInstance().getAppSysList();
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public List getExecAuditorList ( int auditingType, String userId, Integer dbaasFlag , boolean heightPermissionFlag ,String scriptId )
    {
        List res = new ArrayList();
        try
        {
            //是否是值班人员，值班人员审核人是脚本创建人所属用户组的组长、副组长
            if(heightPermissionFlag){
                res = ScriptAuditingManager.getInstance().getExecAuditorLeaderList(userId,scriptId);
            }else {
                if (dbaasFlag != null && dbaasFlag == 1)
                {
                    res = ScriptAuditingManager.getInstance().getExecAuditorListDbaas(auditingType, userId);
                } else
                {
                    res = ScriptAuditingManager.getInstance().getExecAuditorList(auditingType, userId);
                }
            }

        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public List getExecUserList ( String userId )
    {
        List res = new ArrayList();
        try
        {
            res = ScriptAuditingManager.getInstance().getExecUserList(userId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public List getPublishRecheckAuditorList ( int auditingType, String userId, long iworkItemid )
    {
        List res = new ArrayList();
        try
        {
            res = ScriptAuditingManager.getInstance().getPublishRecheckAuditorList(auditingType, userId, iworkItemid);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public boolean queryIsAuditing ( long serviceId )
    {
        boolean audiFlag = false;
        try
        {
            audiFlag = ScriptAuditingManager.getInstance().queryIsAuditing(serviceId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return audiFlag;
    }

    @Override
    public Map queryCommonTask(String serviceName,String version) {
        Map commonTask = new HashMap();
        List res;
        try {
            //根据脚本服务名称查询该脚本下的常用任务的列表
            res = ScriptAuditingManager.getInstance().getCommonTaskrList(serviceName,version);
            if(!res.isEmpty()) {
                commonTask.put("isTemplateFlag", true);
                commonTask.put("commonTaskNameList", res);
            }
        } catch (Exception e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return commonTask;
    }

    @Override
    public Map scriptRePublishAuditing ( String auditor, long iworkItemid, long fromId, String planTime,
            int scriptLevel, String publishDesc, int isEmScript, String appSysIds )
    {
        Map map = new HashMap();
        try
        {

            map = ScriptAuditingManager.getInstance().scriptRePublishAuditing(auditor, iworkItemid, fromId, planTime,
                scriptLevel, publishDesc, isEmScript, appSysIds);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }
    
    @Override
    public Map scriptTryAuditing ( String uuid, String group, String CreateUserName)
    {
        Map map = new HashMap();
        map = ScriptAuditingManager.getInstance().scriptTryAuditing(uuid,group,CreateUserName);
        return map;
    }

    @Override
    public Map scriptPublishAuditing ( String userLoginName, String auditor, String[] sIds, String planTime,
            int scriptLevel, String publishDesc, int flag, int isEmScript, String appSysIds, String visiableUser,
            Map paramap, int isForbidden , String[] attachmentIds, String[] systemIds, String cpIds, String orderNumber )
    {
        Map map = new HashMap();
        try
        {
            String planTime_Type = String.valueOf(paramap.get("planTime_Type"));
            String dbaaswitch = ServerEnv.getInstance().getSysConfig(Environment.ENTEGOR_PROJECT_FLAG,
                Environment.ENTEGOR_PROJECT_FLAG_DEFAULT);
            dbaaswitch = String.valueOf(paramap.get("switchFlag"));
            if ("1".equals(dbaaswitch))
            {
                String planTime_MDHm = "";
                String planTime_DD = String.valueOf(paramap.get("planTime_DD"));
                String planTime_HH = String.valueOf(paramap.get("planTime_HH"));
                String planTime_mm = String.valueOf(paramap.get("planTime_mm"));
                String tablename = String.valueOf(paramap.get("tablename"));
                String username = String.valueOf(paramap.get("username"));
                Integer serviceAuto = null;
                if (paramap.get("serviceAuto") != null)
                {
                    serviceAuto = Integer.parseInt((String) paramap.get("serviceAuto"));
                }
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 加上时间
                if ("按计划执行一次".equals(planTime_Type))
                {
                    Date date = sdf.parse(planTime);
                    planTime_MDHm = CronUtil.getCron(date);
                } else if ("间隔x日".equals(planTime_Type))
                {
                    planTime_MDHm = "0 mi HH 1/dd * ?";
                    planTime_MDHm = StringUtils.replace(planTime_MDHm, "dd", planTime_DD);
                    planTime_MDHm = StringUtils.replace(planTime_MDHm, "HH", planTime_HH);
                    planTime_MDHm = StringUtils.replace(planTime_MDHm, "mi", planTime_mm);
                } else if ("间隔x小时".equals(planTime_Type))
                {
                    planTime_MDHm = "0 mi 0/HH * * ?";
                    if (null == planTime_mm || "".equals(planTime_mm) || "null".equals(planTime_mm))
                    {
                        planTime_mm = "0";
                    }
                    if (null == planTime_HH || "".equals(planTime_HH) || "null".equals(planTime_HH))
                    {
                        planTime_HH = "0";
                        planTime_MDHm = StringUtils.replace(planTime_MDHm, "0/HH", "HH");
                    }
                    planTime_MDHm = StringUtils.replace(planTime_MDHm, "HH", planTime_HH);
                    planTime_MDHm = StringUtils.replace(planTime_MDHm, "mi", planTime_mm);
                } else if ("间隔x分钟".equals(planTime_Type))
                {
                    planTime_MDHm = "0 0/mi * * * ?";
                    if (null == planTime_mm || "".equals(planTime_mm) || "null".equals(planTime_mm))
                    {
                        planTime_mm = "0";
                        planTime_MDHm = StringUtils.replace(planTime_MDHm, "0/mi", "mi");
                    }
                    planTime_MDHm = StringUtils.replace(planTime_MDHm, "mi", planTime_mm);

                }
                map = ScriptAuditingManager.getInstance().scriptPublishAuditingByDbaas(userLoginName, username, auditor,
                    sIds, planTime, scriptLevel, publishDesc, flag, planTime_MDHm, tablename, planTime_Type,
                    serviceAuto, "false");

            } else
            {
                map = ScriptAuditingManager.getInstance().scriptPublishAuditing(userLoginName, auditor, sIds, planTime,
                    scriptLevel, publishDesc, flag, isEmScript, appSysIds, visiableUser, isForbidden,attachmentIds, systemIds, cpIds, orderNumber );
            }

        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    @Override
    public void bindEquipGroup(String agentIIds , String uuid) {
        if(StringUtils.isNotEmpty(agentIIds)){
            Connection con = null;
            PreparedStatement ps = null;
            try {
                con = DBResource.getConnection("saveWorkItemForSsPublish", logger, Constants.IEAI_SCRIPT_SERVICE);
                //将字符串转换为数组
                String agentIdSz [] = agentIIds.split(",");
                for(int i=0 ; i<agentIdSz.length ; i++){
                    //生成iid
                    long id = IdGenerator.createId("IEAI_SCRIPT_SYS_CP_RELATION", con);
                    int isystemId = -1;
                    int agentId = Integer.valueOf(agentIdSz[i]);
                    String sql = "INSERT INTO IEAI_SCRIPT_SYS_CP_RELATION(IID, ISCRIPTUUID,ISYSTEMID, ICPID )  VALUES (?,?,?,?)";
                    ps = con.prepareStatement(sql);
                    ps.setLong(1,id);
                    ps.setString(2,uuid);
                    ps.setInt(3,isystemId);
                    ps.setInt(4,agentId);
                    ps.executeUpdate();
                }
                con.commit();
            }catch (Exception e){
                logger.error(e);
            }finally {
                DBResource.closePSConn(con, ps, "saveWorkItemForSsPublish", logger);
            }
        }
    }

    @Override
    public Map scriptPublishAuditingDelete ( String userLoginName, String auditor, String[] sIds, String publishDesc )
    {
        Map map = new HashMap();
        try
        {
            map = ScriptAuditingManager.getInstance().scriptPublishAuditingDelete(userLoginName, auditor, sIds,
                publishDesc);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }
    
    @Override
    public Map scriptPublishSyncAuditing ( String userLoginName, String auditor, String[] sIds, String publishDesc, int flag)
    {
        Map map = new HashMap();
        try
        {
                map = ScriptAuditingManager.getInstance().scriptPublishSyncAuditing(userLoginName, auditor, sIds,publishDesc, flag);

        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    @Override
    public Map getUserByLoginName(String loginName){
        Map res = new HashMap();
        try {
            res = ScriptAuditingManager.getInstance().getUserByLoginName(loginName);
        }catch (Exception e){
            logger.info("getUserByLoginName is error : " , e);
        }
        return res;
    }


    @Override
    public Map scriptExecAuditing(String userLoginName, String serviceId, String auditor, String execStrategy,
                                  String execTime, String execTimeDisplay, String[] agents, String[] rgIds, String execUser, String execDesc,
                                  String params, String taskName, String performUser, String butterflyversion, String data, String eachNum,
                                  Integer scriptLevel, String oddNumbersType, boolean checkVersion, String isFromCustom, String shutDownUUid,
                                  String iplanId, String iscenceId, String stepId, String sysid, String timeTaskid, String resGroupFlag,
                                  String[] chosedAgentUsers, String customId, String userlistJson, String sysAgentData,
                                  List<IeaiScriptAttachment> scriptTempAttachments,String [] templateIds,String scriptuuid,String startUser,Map dataMap)
    {
        Map res = new HashMap();
        List<Map<String, Object>> paramsMap = null;
        String cronErrMsg = "";
        try
        {
            paramsMap = ParseJson.JSON2List(params);
            List<ScriptParameter> repParams = new ArrayList<ScriptParameter>();
            for (int i = 0; i < paramsMap.size(); i++)
            {
                Map<String, Object> paramMap = paramsMap.get(i);
                repParams.add(this._convertToScriptParameter(paramMap));
            }
            // 校验Cron表达式(勾选了定时执行)
            if (Integer.valueOf(execStrategy) == 1)
            {
                boolean isCronOK = org.quartz.CronExpression.isValidExpression(execTime);
                if (!isCronOK)
                {
                    cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                    throw new ServerException(cronErrMsg);
                }
            }
            String invokeInfo = RpcContext.getContext().getAttachment("invokeInfo");
            res = ScriptAuditingManager.getInstance().scriptExecAuditing(userLoginName, serviceId, auditor,
                execStrategy, execTime, execTimeDisplay, agents, rgIds, execUser, execDesc, repParams, taskName,
                performUser, butterflyversion, data, eachNum, scriptLevel, oddNumbersType, checkVersion, isFromCustom,
                shutDownUUid, iplanId, iscenceId, stepId,sysid,timeTaskid,resGroupFlag,chosedAgentUsers,customId,
                    userlistJson,sysAgentData, scriptTempAttachments,templateIds,scriptuuid, invokeInfo,startUser);

            //邮储任务申请发送短信，通过短信进行审批
            boolean ycsms = ServerEnv.getInstance().getBooleanConfig(Environment.YC_SMS_SCRIPT_SERVICE_AUDIT_SWITCH, false);
            if(ycsms)
            {
                Long workItemId = (Long) res.get("workItemId");
                Map returnMap = ScriptServiceManage.getInstance().getWorkitemRecordForSsPublish(workItemId);
                Connection conn = null;
                //任务申请风险脚本或者是值班任务申请走下面发短信方法，heightPermissionFlag是判断请求是否为值班任务申请的依据
                if(!"白名单".equals((String) returnMap.get("iscriptLevelDisplay") == null ? ""
                        : (String) returnMap.get("iscriptLevelDisplay"))
                        || "true".equals((String) dataMap.get("heightPermissionFlag") == null ? ""
                        : (String) dataMap.get("heightPermissionFlag")))
                {
                    try {
                        String serviceType = String.valueOf(returnMap.get("iserviceType"));
                        String scriptName = String.valueOf(returnMap.get("iscriptName"));
                        String bussName = String.valueOf(returnMap.get("ibussName"));
                        String scriptLevelDisplay = (String) returnMap.get("iscriptLevelDisplay") == null ? ""
                                : (String) returnMap.get("iscriptLevelDisplay");
                        Map map = ScriptAuditingManager.getInstance().getPerformUserphone(performUser);
                        String ifullname =map.get("ifullname").toString();
                        String itelephone =map.get("itelephone").toString();
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String outtime=Environment.getInstance().getSysConfig("task.out.autoback.time", "30");
                        String content = "任务名称："+taskName+"，脚本名称："+scriptName+"，一级分类："+bussName+"，风险级别："+scriptLevelDisplay+
                                "，发起人："+ifullname+"-"+itelephone+"，发起时间："+format.format(System.currentTimeMillis())+"，执行描述："+execDesc+"，" +
                                "确认审批通过请在"+outtime+"分钟内回复短信【auto+1"+workItemId+"+10】。";
                        String contentTitle = "";
                        //任务申请风险脚本与值班任务申请短信的标题不一样。
                        if("true".equals((String) dataMap.get("heightPermissionFlag") == null ? ""
                                : (String) dataMap.get("heightPermissionFlag"))){
                            contentTitle = "【邮储银行】标题：高权任务应急申请执行审批，";
                        }else{
                            contentTitle = "【邮储银行】标题：脚本服务化任务申请执行审批，";
                        }
                        final String lastContent = contentTitle + content;

                        logger.info(lastContent);
                        conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SCRIPT_SERVICE);
                        RepUserBack user = UserManager.getInstance().getRepUserBackOne(UserManager.getInstance().getLoginId(auditor),conn);
                        String telephoneNumber= user.getTelephone();
                        new Thread(new Runnable()
                        {
                            @Override
                            public void run ()
                            {
                                try
                                {
                                    EmInstanceConfigManager.getInstance().sendToVerifyUser(telephoneNumber,lastContent,workItemId);
                                } catch (Exception e)
                                {
                                    logger.error("scriptExecAuditingSMS is error : ", e);
                                }
                            }
                        }).start();
                    }catch (Exception e)
                    {
                        logger.error("AuditingImpl.scriptExecAuditingSMS is error", e);
                    }finally {
                        DBResource.closeConnection(conn, "scriptExecAuditingSMS", logger);
                    }
                }
            }
        } catch (JSONException e)
        {
            logger.error("解析脚本参数数据出错");
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
            res.put("success", false);
            res.put("message", "解析脚本参数数据出错");
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    @Override
    public Map scriptFlowExecAuditing(String userLoginName, Long iidForQuery, String serviceId, String auditor,
                                      String taskName, String startData, Integer scriptLevel, String isDelay, String execTime, String execUser,
                                      String execDesc, String butterflyversion, String resGroupFlag, String customId, String apiFlag, int startType)
    {
        Map res = new HashMap();
        List<Map<String, Object>> paramsMap = null;
        String cronErrMsg = "";
        try
        {
            // 校验Cron表达式(勾选了定时执行)
            if (Boolean.valueOf(isDelay))
            {
                boolean isCronOK = org.quartz.CronExpression.isValidExpression(execTime);
                if (!isCronOK)
                {
                    cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                    throw new ServerException(cronErrMsg);
                }
            }
            res = ScriptAuditingManager.getInstance().scriptFlowExecAuditing(userLoginName, iidForQuery, serviceId,
                auditor, taskName, startData, scriptLevel, isDelay, execTime, execUser, execDesc, butterflyversion,  resGroupFlag,customId, apiFlag, startType);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    @Override
    public Map organizationStartData ( long serviceId, String[] agentIds, String realStartUser, Map agentsAndDbSources,
            boolean isShutdown, long eachNum, String[] chosedGroupNames )
    {
        Map res = new HashMap();
        List<Map<String, Object>> paramsMap = null;
        String cronErrMsg = "";
        try
        {
            Map end = new HashMap();
            res = ScriptAuditingManager.getInstance().organizationStartData(serviceId, agentIds, realStartUser,
                agentsAndDbSources, isShutdown, eachNum, end, "",chosedGroupNames);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " method err : " + e.getMessage());
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    @Override
    public Map scriptExecAuditingDelete ( String workItemId )
    {
        Map map = new HashMap();
        try
        {
            map = ScriptAuditingManager.getInstance().scriptExecAuditingDelete(workItemId);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return map;
    }

    private ScriptParameter _convertToScriptParameter ( Map<String, Object> rgMap )
    {
        ScriptParameter res = new ScriptParameter();
        Object idd = rgMap.get("iid");
        if (idd == JSONObject.NULL || "".equals(String.valueOf(idd)))
        {
            res.setIid(-1);
        } else
        {
            res.setIid(Long.parseLong(String.valueOf(rgMap.get("iid"))));
        }

        Object paramType = rgMap.get("paramType");
        if (null != paramType)
        {
            res.setParamType(((String) rgMap.get("paramType")).trim());
        }
        Object paraIiid = rgMap.get("paraIid");
        if (null != paraIiid)
        {
            res.setParaIid((Integer) rgMap.get("paraIid"));
        }
        Object paramDefaultValue = rgMap.get("paramDefaultValue");
        if (null != paramDefaultValue)
        {
            res.setParamDefaultValue(((String) rgMap.get("paramDefaultValue")).trim());
        }
        Object paramValue1 = rgMap.get("paramValue");
        if (StringUtils.isNotBlank((String) paramValue1))
        {
            res.setParamDefaultValue(((String) rgMap.get("paramValue")).trim());
        }
        Object paramDesc = rgMap.get("paramDesc");
        if (null != paramDesc)
        {
            res.setParamDesc(((String) rgMap.get("paramDesc")).trim());
        }
        Object paramOrder = rgMap.get("paramOrder");
        if (null != paramOrder)
        {
            res.setParamOrder(Integer.parseInt(String.valueOf(rgMap.get("paramOrder"))));
        }
        Object ruleName = rgMap.get("ruleName");
        if (null != ruleName)
        {
            res.setRuleName(((String) rgMap.get("ruleName")).trim());
        }
        return res;
    }

    @Override
    public String[] getCpidAndDsidFromJson ( String agentsJson )
    {
        try
        {
            List<Map<String, Object>> infoList = ParseJson.JSON2List(agentsJson);
            String[] infoArr = new String[infoList.size()];
            if (!infoList.isEmpty())
            {
                int index = 0;
                for (int i = 0; i < infoList.size(); i++)
                {
                    Map<String, Object> cmdMap = infoList.get(i);
                    String cpid = cmdMap.get("cpid").toString();
                    String dsid = cmdMap.get("dsid").toString();
                    if (!"".equals(cpid) && !"".equals(dsid) && !"-1".equals(cpid) && !"-1".equals(dsid))
                    {
                        infoArr[index++] = cpid + "_" + dsid;
                    }
                }
            }
            return infoArr;
        } catch (JSONException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return null;
    }

    @Override
    public Map checkScriptFlowHasTestScript ( Long serviceId )
    {
        Map map = new HashMap();
        try
        {
            map = ScriptAuditingManager.getInstance().checkScriptFlowHasTestScript(serviceId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    @Override
    public Map checkScriptFlowHasNoShareScript ( long[] serviceId )
    {
        Map map = new HashMap();
        try
        {
            map = ScriptAuditingManager.getInstance().checkScriptFlowHasNoShareScript(serviceId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    @Override
    public Map scriptFlowExecPlanScence ( String userLoginName, Long iidForQuery, String serviceId, String auditor,
            String startData, Integer scriptLevel, String isDelay, String execTime, String execDesc, String iplanId,
            String iplanName, String iscenceId, String iscenceName, String stepName, String stepId,String sysid )
    {
        Map res = new HashMap();
        List<Map<String, Object>> paramsMap = null;
        String cronErrMsg = "";
        try
        {
            // 校验Cron表达式(勾选了定时执行)
            if (Boolean.valueOf(isDelay))
            {
                boolean isCronOK = org.quartz.CronExpression.isValidExpression(execTime);
                if (!isCronOK)
                {
                    cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                    throw new com.ideal.ieai.server.ServerException(ServerError.ERR_UNKNOWN, cronErrMsg);
                }
            }
            res = ScriptAuditingManager.getInstance().scriptFlowExecPlanScence(userLoginName, iidForQuery, serviceId,
                auditor, startData, scriptLevel, isDelay, execTime, execDesc, iplanId, iplanName, iscenceId,
                iscenceName, stepName, stepId,sysid);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " method err : " + e.getMessage());
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    @Override
    public Map scriptCallSearch ( Long serviceId )
    {
        Map res = new HashMap();
        try
        {
            res = ScriptAuditingManager.getInstance().scriptCallSearch(serviceId);
        } catch (RepositoryException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;

    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public Map serviceStart ( String userLoginName, String serviceId, String auditor, String isDelay, String execTime,
            String[] agents, String[] rgIds, String execUser, String execDesc, String args, String taskName,
            String eachNum, Integer scriptLevel, String execRule, String serviceType, String userId, String scriptType )
    {
        Map res = new HashMap();
        String cronErrMsg = "";
        try
        {
            List<ScriptParameter> repParams = new ArrayList<ScriptParameter>();
            // 校验Cron表达式(勾选了定时执行)
            String cronExecTime = "";
            if (Boolean.valueOf(isDelay))
            {
                String cronByPoint = "";
                String cronByLoop = "";
                if (!"1".equals(execRule))
                {
                    String[] s = execTime.split(",");
                    if ("2".equals(execRule))
                    {
                        cronByPoint = CronUtil.getCronByPoint(s[0], s[1], s[2], s[3], s[4], s[5]);
                        cronExecTime = cronByPoint;
                        boolean isCronOK1 = org.quartz.CronExpression.isValidExpression(cronByPoint);
                        if (!isCronOK1)
                        {
                            cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                            throw new Exception(cronErrMsg);
                        }
                    } else if ("3".equals(execRule))
                    {
                        cronByLoop = CronUtil.getCronByLoop(s[0], s[1], s[2], s[3]);
                        cronExecTime = cronByLoop;
                        boolean isCronOK2 = org.quartz.CronExpression.isValidExpression(cronByLoop);
                        if (!isCronOK2)
                        {
                            cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                            throw new Exception(cronErrMsg);
                        }
                    }
                }
            }
            repParams = json2Beans(args, serviceId);
            res = ScriptAuditingManager.getInstance().scriptExecAuditingByDbaas(userLoginName, serviceId, auditor,
                isDelay, cronExecTime, agents, rgIds, execUser, execDesc, repParams, taskName, eachNum, scriptLevel,
                execRule, serviceType, userId, execTime, scriptType);

        } catch (JSONException e)
        {
            logger.error("解析脚本参数数据出错");
            e.printStackTrace();
            res.put("success", false);
            res.put("message", "解析脚本参数数据出错");
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " method err : " + e.getMessage());
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    private List<ScriptParameter> json2Beans ( String jsonData, String serviceId )
    {
        List<ScriptParameter> list = new ArrayList<ScriptParameter>();
        JSONArray jsonArr;
        JSONObject json2 = null;
        try
        {
            jsonArr = new JSONArray(jsonData);
            if (jsonArr != null)
            {
                for (int i = 0; i < jsonArr.length(); i++)
                {
                    json2 = jsonArr.getJSONObject(i);
                    ScriptParameter bean = new ScriptParameter();
                    bean.setParamDefaultValue(json2.getString("argsDefaultValue"));
                    bean.setParamDesc(json2.getString("argsDesc"));
                    bean.setParamOrder(json2.getInt("argsOrder"));
                    bean.setParamType(json2.getString("argsType"));
                    bean.setScriptId(serviceId);
                    bean.setIid(Long.parseLong(json2.getString("argsIid")));
                    list.add(bean);
                }
            }
        } catch (JSONException jsonexception)
        {
            jsonexception.printStackTrace();
        }
        return list;
    }

    @Override
    public Map scriptPublishAuditingByDbaas ( String userLoginName, String userName, String auditor, String[] sIds,
            String planTime, int scriptLevel, String publishDesc, int flag, String planTimeMDHm, String tablename,
            String planTimeType, Integer serviceAuto, String tableFlag )
    {
        Map map = new HashMap();
        try
        {
            map = ScriptAuditingManager.getInstance().scriptPublishAuditingByDbaas(userLoginName, userName, auditor,
                sIds, planTime, scriptLevel, publishDesc, flag, planTimeMDHm, tablename, planTimeType, serviceAuto,
                tableFlag);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    public Map scriptPublishAuditingByDbaasForGroup ( String userLoginName, String userName, String auditor,
            String sId, String planTime, int scriptLevel, String publishDesc, int flag, String planTimeMDHm,
            String tablename, String planTimeType, String versionData, String resourceGroupData,
            String systemTypeData, String jsonTypeData, String params )
    {
        Map map = new HashMap();
        List<Map<String, Object>> paramsMap = null;
        try
        {
            paramsMap = ParseJson.JSON2List(params);

            List<ScriptParameter> repParams = new ArrayList<ScriptParameter>();
            for (int i = 0; i < paramsMap.size(); i++)
            {
                Map<String, Object> paramMap = paramsMap.get(i);
                repParams.add(this._convertToScriptParameter(paramMap));
            }
            map = ScriptAuditingManager.getInstance().scriptPublishAuditingByDbaasForGroup(userLoginName, userName,
                auditor, sId, planTime, scriptLevel, publishDesc, flag, planTimeMDHm, tablename, planTimeType,
                versionData, resourceGroupData, systemTypeData, jsonTypeData, repParams);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    @Override
    public Map analyzePublishAuditing ( String loginname, String auditor, Long relId, String publishDesc,
            String content )
    {
        Map map = new HashMap();
        try
        {
            // 解析一下分析算法，如果解析不出来，不让提交

            String typeOrName = QuartZManager.getInstance().analysisAnalyzeFun(content);
            if (typeOrName == null || !typeOrName.contains(":"))
            {
                map.put("success", false);
                map.put("message", "分析算法格式有问题，请检查！");
            } else
            {
                map = ScriptAuditingManager.getInstance().analyzePublishAuditing(loginname, auditor, relId,
                    publishDesc);
            }


        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return map;
    }

    @Override
    public Map rollbackShutDownTask ( Long long1 )
    {
        return ScriptAuditingManager.getInstance().rollbackShutDownTask(long1);
    }

    @Override
    public Map<String, Object> scriptTableSearch ( Long serviceId )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SCRIPT_SERVICE);
            Map<String, String> lastMap = new HashMap<String, String>();
            // 查询当前服务对应的表结构信息
            Map<String, String> currMap = ScriptAuditingManager.getInstance().scriptTableSearchNew(serviceId, conn);
            // 查询上一版本对应的服务表结构信息
            Map<String, String> oldMap = ScriptAuditingManager.getInstance().scriptTableSearchOld(serviceId, res, conn);
            boolean tableflag = false;
            for (Map.Entry<String, String> entry : oldMap.entrySet())
            {
                String mapKey = entry.getKey();
                String mapValue = entry.getValue();
                if (currMap.containsKey(mapKey))
                {
                    if (!mapValue.equals(currMap.get(mapKey)))
                    {
                        // 表有變化
                        tableflag = true;
                        break;
                    }
                    currMap.remove(mapKey);
                    lastMap.put(mapKey, mapValue);
                } else
                {
                    // 表有變化
                    tableflag = true;
                    break;
                }
            }
            if (tableflag || !currMap.isEmpty() || (currMap.isEmpty() && oldMap.isEmpty()))
            {
                res.put("success", false);
                res.put("message", "表结构存在变化，重新生成服务");
            } else
            {
                res.put("success", true);
                res.put("message", "生成新服务");
            }
        } catch (Exception e)
        {
            res.put("success", false);
            res.put("message", "操作失败！");
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), logger);
        }
        return res;
    }

    @Override
    public Map scriptExecAuditingForDbaasReSQL ( String loginName, long workitemid ,String auditor)
    {
        Map map = new HashMap();
        try
        {
            map = ScriptAuditingManager.getInstance().scriptExecAuditingForDbaasReSQL(loginName, workitemid, auditor);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return map;
    }
    
    @Override
    public List getTryGroupList ()
    {
        List res = new ArrayList();
        try
        {
            res = ScriptAuditingManager.getInstance().getTryGroupList();
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getItsmOrderNumber ( Map<String,String> dataMap )
    {
        Map<String,String> map = new HashMap();
        map.put("success","-1");
        map.put("message","单号获取失败");
        //获取配置的url
        String url = Environment.getInstance().itsmCreateOrderNumUrl();
        if(StringUtils.isEmpty(url)){
            logger.error("请配置itsm单号获取接口url");
            map.put("message","请配置itsm单号获取接口url");
            return map;
        }
        String key = Environment.getInstance().itsmCheckOrderKeyParam();
        if(StringUtils.isEmpty(key)){
            String errorMsg = "请配置itsm单号校验接口密钥";
            logger.error(errorMsg);
            map.put("success","error");
            map.put("message",errorMsg);
            return map;
        }
        EventBean parameters = new EventBean();
        parameters.setAutoSysName(dataMap.get("sysName"));//系统名称
        parameters.setAutoTaskName(dataMap.get("taskName"));//任务名称
        parameters.setCreaterPhone(dataMap.get("teltephone"));//提单人手机号
        //调用接口获取校验结果
        ApiResData res = null;
        try {
            res = ApiUtil.getInstance().itsmPostJson(url,parameters,60,"");
        }catch (Exception e){
            map.put("message","获取单号接口调用失败");
            logger.error("AuditingImpl.getItsmOrderNumber error : ",e);
            return map;
        }
        //resCode=0是成功，其它码值为失败
        map.put("success",res.getResCode());
        map.put("message",res.getResMsg());
        if("itsmerror".equals(res.getResCode())){
            map.put("success","error");
            map.put("message","ITSM接口调用失败");
        }
        return map;
    }

    @Override
    public Map<String,String> checkItsmOrderNumber ( String orderNumber )
    {
        Map map = new HashMap();
        map.put("success","-1");
        map.put("message","单号校验失败");
        //获取配置的url
        String url = Environment.getInstance().itsmCheckOrderNumUrl();
        if(StringUtils.isEmpty(url)){
            logger.error("请配置itsm单号校验接口url");
            map.put("message","请配置itsm单号校验接口url");
            return map;
        }
        String key = Environment.getInstance().itsmCheckOrderKeyParam();
        if(StringUtils.isEmpty(key)){
            String errorMsg = "请配置itsm单号校验接口密钥";
            logger.error(errorMsg);
            map.put("success","error");
            map.put("message",errorMsg);
            return map;
        }
        //调用接口获取校验结果
        ApiResData res = null;
        try {
            res = ApiUtil.getInstance().itsmPostJson(url,orderNumber,60, ItsmSystemFlag.getInstance().getSystemAuto());
        }catch (Exception e){
            map.put("message","校验单号接口调用失败");
            logger.error("AuditingImpl.checkItsmOrderNumber error : ",e);
            return map;
        }
        //resCode=0是成功，其它码值为失败，当成功时，resMsg是单号
        map.put("success",res.getResCode());
        map.put("message",res.getResMsg());
        if("itsmerror".equals(res.getResCode())){
            map.put("success","error");
            map.put("message","ITSM接口调用失败");
        }

        return map;
    }
    
    public Map queryIsAuditingIds ( String[] serviceIds )
    {
        Map map = new HashMap();
        boolean audiFlag = false;
        StringBuilder sbl = new StringBuilder();
        StringBuilder sbl1 = new StringBuilder();
        try
        {
            for (int i = 0; i < serviceIds.length; i++)
            {
                long id = Long.parseLong(serviceIds[i]);
                if (!audiFlag)
                {
                    audiFlag = ScriptAuditingManager.getInstance().queryIsAuditing(id);
                } else
                {
                    ScriptAuditingManager.getInstance().queryIsAuditing(id);
                }
                if (i == serviceIds.length - 1)
                {
                    if (audiFlag)
                    {
                        sbl.append(id);
                    } else
                    {
                        sbl1.append(id);
                    }
                } else
                {
                    if (audiFlag)
                    {
                        sbl.append(id).append(",");
                    } else
                    {
                        sbl1.append(id).append(",");
                    }
                }
            }
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        } finally
        {
            map.put("isAudiFlag", audiFlag);
            map.put("noDelIds", sbl.toString());
            map.put("delIds", sbl1.toString());
        }
        return map;
    }

    @Override
    public void rollBackCibItsmPublishWorkitemData(List<IeaiScriptItsmpublish> itsmpublishList) throws SQLException {
        QueryRunner qr = new QueryRunner(DBManager.getInstance().getDBSource(Constants.IEAI_SCRIPT_SERVICE));

        //获取workitemid不为空的数据
        Integer[] workitemids =   itsmpublishList.stream()
                .map(IeaiScriptItsmpublish::getWorkitemiid)
                .filter(workitemiid -> workitemiid > 0)
                .toArray(Integer[]::new);
        //获取workitemid不为空的数据
        Integer[] scriptiids =  itsmpublishList.stream()
                .map(IeaiScriptItsmpublish::getScriptiid)
                .filter(scriptiid -> scriptiid > 0)
                .toArray(Integer[]::new);

        String conditionStr1 = Arrays.stream(scriptiids).map(integer -> "?").collect(Collectors.joining(","));
        String conditionStr2 = Arrays.stream(workitemids).map(integer -> "?").collect(Collectors.joining(","));
        //把状态更新回草稿
        String rollbackTestSql = "update ieai_script_test set istatus = -1 where iid in("+conditionStr1+") and ISTATUS=2";
        //删除复核表数据
        String rollbackWorkitemSql = "delete from IEAI_DOUBLECHECK_WORKITEM where iid in("+conditionStr2+")";
        String rollbackColvalueSql = "delete from IEAI_DOUBLECHECK_COLVALUE where IWORKITEMID in("+conditionStr2+")";
        qr.update( rollbackWorkitemSql, workitemids);
        qr.update( rollbackColvalueSql, workitemids);
        qr.update( rollbackTestSql, scriptiids);
    }

    /**
     * @Title: getdeleteAuditorList
     * @Description 支持创建者删除自己的脚本
     * @param
     * @return java.util.List
     * <AUTHOR>
     * @date  2021年04月12日 14:10:09
     **/
    @Override
    public List getDeleteAuditorList ( int auditingType, String userId, Integer dbaasFlag )
    {
        List res = new ArrayList();
        try
        {
            if (dbaasFlag != null && dbaasFlag == 1)
            {
                res = ScriptAuditingManager.getInstance().getdeleteAuditorListDbaas(auditingType, userId);
            } else
            {
                res = ScriptAuditingManager.getInstance().getdeleteAuditorList(auditingType, userId);
            }
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map scriptFlowExecAuditingCustom(String userLoginName, Long iidForQuery, String serviceId, String auditor,
                                            String taskName, String startData, Integer scriptLevel, String isDelay, String execTime, String execUser,
                                            String execDesc, String butterflyversion, String resGroupFlag, String customId, String apiFlag, int startType,
                                            Map<String, Object> customMap) {
        Map res = new HashMap();
        List<Map<String, Object>> paramsMap = null;
        String cronErrMsg = "";
        try
        {
            // 校验Cron表达式(勾选了定时执行)
            if (Boolean.valueOf(isDelay))
            {
                boolean isCronOK = org.quartz.CronExpression.isValidExpression(execTime);
                if (!isCronOK)
                {
                    cronErrMsg = "执行时间填写错误(提示：0 */1 * * * ?)";
                    throw new ServerException(cronErrMsg);
                }
            }
            res = ScriptAuditingManager.getInstance().scriptFlowExecAuditingCustom(userLoginName, iidForQuery, serviceId,
                    auditor, taskName, startData, scriptLevel, isDelay, execTime, execUser, execDesc, butterflyversion,
                    resGroupFlag, customId, apiFlag, startType, customMap);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    @Override
    public boolean validateOrderNumber(String orderNumber) throws Exception {
        logger.info("提交发布工单号：" + orderNumber);
        String itsmUrl = Environment.getInstance().getHSScriptPublishItsm();
        String apiKey = Environment.getInstance().getHSScriptPublishItsmApiKey();

        Map param = new HashMap();
        param.put("keyword", orderNumber);

        try {
            String resp = HttpUtils.doPost(itsmUrl + "?apikey="+apiKey,  JSON.toJSONString(param));
            OrderValidResp orderValidResp = JSON.parseObject(resp, OrderValidResp.class);
            if (null != orderValidResp && orderValidResp.getCounts() != 0) {
                OrderValidData[] datas = orderValidResp.getTicket_list();
                for (int i = 0; i < datas.length; i++) {
                    if (orderNumber.equals(datas[i].getNumber())) {
                        // 1-待处理 2-处理中
                        if (datas[i].getStatus() == 1 || datas[i].getStatus() == 2) {
                            return true;
                        } else if (datas[i].getStatus() == 3) {
                            throw new Exception("单号已完成，不合规");
                        } else if (datas[i].getStatus() == 7 || datas[i].getStatus() == 11) {
                            throw new Exception("单号已失效");
                        } else {
                            throw new Exception("单号已挂起");
                        }
                    }
                }
                // 返回数据没有当前单号
                throw new Exception("单号不存在");
            }
        } catch ( ClientProtocolException e) {
            logger.error("单号校验异常", e);
            throw e;
        } catch ( IOException e) {
            logger.error("单号校验异常", e);
            throw new Exception("单号校验网络异常");
        } catch (Exception e) {
            throw e;
        }
        return false;
    }

    public void hsPublishNotify(long workItemId, String id, int type) {
        ScriptAuditingManager.getInstance().hsPublishNotify(workItemId, id, type);
    }
}
