package com.ideal.ieai.server.repository.hd.agentUpDataInfo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.jobscheduling.repository.home.StringUtils;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBSourceMonitor;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.util.DateUtil;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class AgentUpDataManager
{
    private static final Logger       dbsRollbacklog = Logger.getLogger("dbSRblog");
    private static final Logger       _log           = Logger.getLogger(AgentUpDataManager.class);

    private static final String SQLONE = "数据库执行发生错误!表名:IEAI_AGENTUPDATE_INFO iagentup_id:";
    private static final String SQLTWO = " NULLS LAST ";
    private static final String PROPERTY = "property";
    private static final String OPERATIONTYPE = "operationType";
    private static final String ITRANSMISSIONID = "itransmission_id";
    private static final String ITARGETPATH = "itarget_path";
    private static final String ISOURCEPATH = "isource_path";
    private static final String IOPERATIONUSER = "ioperation_user";
    private static final String IOPERATIONPD = "ioperation_password";
    private static final String INSERTAGENTUPDATA = "insertAgentUpData";
    private static final String ICONNECTTYPE = "iconnect_type";
    private static final String ICONNECTPORT = "iconnect_port";
    private static final String IBACKUPPATH = "ibackup_path";
    private static final String IAGENTUPID = "iagentup_id";
    private static final String DIRECTION = "direction";
    private static final String MESSAGEONE = "agentupdataRollBack error!";
    private static final String ITRANSMISSIONIDU = "ITRANSMISSION_ID";
    private static final String ISUSP = "isusp";
    private static final String IOSNAME ="ios_name";

    private static AgentUpDataManager intance       = new AgentUpDataManager();

    public static AgentUpDataManager getInstance ()
    {
        if (intance == null)
        {
            intance = new AgentUpDataManager();
        }
        return intance;
    }

    /**
     *
     * <li>Description:加载Agent升级数据信息</li> 
     * <AUTHOR>
     * 2016年7月25日 
     * @param start
     * @param limit
     * @param iosName
     * @param sysType
     * @return
     * return Map
     * @throws RepositoryException
     */
    public Map<String, Object> getAgentUpData ( String start, String limit, String iosName, String sort ) throws RepositoryException

    {
        Map<String, Object> map = new HashMap<String, Object>();
        int count = 0;
        List<AgentUpData> list = new ArrayList<AgentUpData>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet res = null;
        String sql = "";
        String sqlWhere = "";
        int li = 0;
        String orderBy = getAgentUpDataPartOne(sort);
        try
        {
            if (!"".equals(iosName) && iosName != null)
            {
                sqlWhere = "and (upper(IOS_NAME) like ? or upper(IOPERATION_USER) like ? )";

            }
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sql = "select IAGENTUP_ID, IOS_NAME,IOPERATION_USER,IOPERATION_PASSWORD,ISOURCE_PATH, ITARGET_PATH, IBACKUP_PATH, ICONNECT_TYPE, ICONNECT_PORT,ISUSP, b.ITRANSMISSION_ID,b.itransmission_type,b.itransmission_ip,updateversion,actfinishmaxwait,writebuffersize from IEAI_AGENTUPDATE_INFO a left join IEAI_TRANSMISSION_CONFIG b on a.ITRANSMISSION_ID = b.ITRANSMISSION_ID where 1=1 "
                        + sqlWhere + orderBy + " limit ?,? ";
                li = Integer.parseInt(limit);
            } else
            {
                orderBy += SQLTWO;
                sql = "select IAGENTUP_ID, IOS_NAME,IOPERATION_USER,IOPERATION_PASSWORD,ISOURCE_PATH, ITARGET_PATH, IBACKUP_PATH, ICONNECT_TYPE, ICONNECT_PORT,ISUSP, ITRANSMISSION_ID,itransmission_type,itransmission_ip,updateversion,actfinishmaxwait,writebuffersize from (SELECT ROW_NUMBER() OVER("
                        + orderBy
                        + ") as ro, b.* FROM (select a.*,b.ITRANSMISSION_TYPE,b.ITRANSMISSION_IP from IEAI_AGENTUPDATE_INFO a left join IEAI_TRANSMISSION_CONFIG b on a.ITRANSMISSION_ID = b.ITRANSMISSION_ID where 1 = 1 "
                        + sqlWhere + orderBy + " ) b) aa  where aa.ro > ? and aa.ro<=?";
                li = Integer.parseInt(start) + Integer.parseInt(limit);
            }

            conn = DBResource.getConnection("getAgentUpData", _log, 1);
            count = cntAgentUpData(conn, iosName);
            ps = conn.prepareStatement(sql);
            int sqlIndex=1;
            if (!"".equals(iosName) && iosName != null)
            {
                ps.setString(sqlIndex++, "%"+iosName.toUpperCase()+"%");
                ps.setString(sqlIndex++, "%"+iosName.toUpperCase()+"%");
            }
            ps.setInt(sqlIndex++, Integer.valueOf(start));
            ps.setInt(sqlIndex++, li);
            res = ps.executeQuery();
            while (res.next())
            {
                AgentUpData aud = new AgentUpData();
                aud.setAgentUpId(res.getLong("IAGENTUP_ID"));
                aud.setIosName(res.getString("IOS_NAME"));
                aud.setIosUser(res.getString("IOPERATION_USER"));
                aud.setIsourcePath(res.getString("ISOURCE_PATH"));
                aud.setItargetPath(res.getString("ITARGET_PATH"));
                aud.setIbackupPath(res.getString("IBACKUP_PATH"));
                aud.setOperPwd(res.getString("IOPERATION_PASSWORD"));
                aud.setConnPort(res.getLong("ICONNECT_PORT"));
                aud.setConnType(res.getInt("ICONNECT_TYPE"));
                aud.setTransmissionId(res.getLong(ITRANSMISSIONIDU));
                aud.setTransmissionType(res.getString("itransmission_type"));
                aud.setTransmissionIp(res.getString("itransmission_ip"));
                aud.setIsusp(res.getInt("ISUSP"));
                aud.setUpdateVersion(res.getString("updateversion"));
                aud.setActFinishMaxWait(res.getLong("actfinishmaxwait"));
                aud.setWriteBufferSize(res.getLong("writebuffersize"));
                list.add(aud);
            }
            map.put("total", count);
            map.put("dataList", list);

        } catch (Exception ee)
        {
            _log.error("AgentUpDataManager.getAgentUpData error!", ee);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, res, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public String getAgentUpDataPartOne(String sort) {
        String orderBy = " order by IOS_NAME ";
        try
        {
            if (sort != null && !"".equals(sort))
            {
                JSONArray jsonList = JSONArray.parseArray(sort);
                JSONObject jsonObj = (JSONObject) jsonList.get(0);
                if ("agentUpId".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by IAGENTUP_ID " + jsonObj.getString(DIRECTION);
                } else if ("iosUser".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by IOPERATION_USER " + jsonObj.getString(DIRECTION);
                } else if ("isourcePath".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ISOURCE_PATH " + jsonObj.getString(DIRECTION);
                } else if ("itargetPath".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ITARGET_PATH " + jsonObj.getString(DIRECTION);
                } else if ("connType".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ICONNECT_TYPE " + jsonObj.getString(DIRECTION);
                } else if ("connPort".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ICONNECT_PORT " + jsonObj.getString(DIRECTION);
                } else if ("transmissionType".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by b.ITRANSMISSION_TYPE " + jsonObj.getString(DIRECTION);
                } else if ("transmissionIp".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by b.ITRANSMISSION_IP " + jsonObj.getString(DIRECTION);
                } else
                {
                    orderBy = " order by IOS_NAME" + " " + jsonObj.getString(DIRECTION);
                }
            }
        } catch (Exception e)
        {
            orderBy = " order by IOS_NAME ";
        }
        return orderBy;
    }

    /**
     *
     * <li>Description:获取传输配置主键信息</li>
     * <AUTHOR>
     * 2016年7月25日
     * @param sysType
     * @return
     * @throws Exception
     * return Map
     */
    public Map<String, Object> getTrsPrimaryKey ( String start, String limit, long agentUpId, String sort )
            throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Object> list = new ArrayList<Object>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet res = null;
        String sql = "";
        int li = 0;
        String sqlCount = "SELECT COUNT(*) AS NUM FROM IEAI_TRANSMISSION_CONFIG ";
        String orderBy = getTrsPrimaryKeyPartOne(sort);
        try
        {
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sql = "SELECT ITRANSMISSION_ID,ITRANSMISSION_TYPE,ITRANSMISSION_IP,ITRANSMISSION_PROT,ITRANSMISSION_USER,ITRANSMISSION_PASSWORD FROM IEAI_TRANSMISSION_CONFIG "
                        + orderBy + " limit ?,?";
                li = Integer.parseInt(limit);
            } else
            {
                sql = "SELECT ITRANSMISSION_ID,ITRANSMISSION_TYPE,ITRANSMISSION_IP,ITRANSMISSION_PROT,ITRANSMISSION_USER,ITRANSMISSION_PASSWORD FROM IEAI_TRANSMISSION_CONFIG "
                        + orderBy;
                orderBy += SQLTWO;
                sql = " SELECT * from (SELECT ROW_NUMBER() over(" + orderBy + ") as ro ,a.* from (" + sql
                        + ") a) aa where aa.ro > ? and aa.ro<=?";
                li = Integer.parseInt(start) + Integer.parseInt(limit);
            }
            conn = DBResource.getConnection("getTrsPrimaryKey", _log, Constants.IEAI_EMERGENCY_SWITCH);
            long transmissionId = queryTsIdByAgentId(agentUpId, conn);

            ps = conn.prepareStatement(sql);
            ps.setInt(1, Integer.valueOf(start));
            ps.setInt(2, li);

            res = ps.executeQuery();
            while (res.next())
            {
                TransmissionConfigData tcd = new TransmissionConfigData();
                tcd.setTransmissionId(res.getLong(ITRANSMISSIONIDU));
                tcd.setTransmissionType(res.getInt("ITRANSMISSION_TYPE"));
                tcd.setTransmissionIp(res.getString("ITRANSMISSION_IP"));
                tcd.setTransmissionPort(res.getLong("ITRANSMISSION_PROT"));
                tcd.setTransmissionUser(res.getString("ITRANSMISSION_USER"));
                tcd.setTransmissionPwd(res.getString("ITRANSMISSION_PASSWORD"));
                if (transmissionId == res.getLong(ITRANSMISSIONIDU))
                {
                    tcd.setIscheck(1);
                } else
                {
                    tcd.setIscheck(0);
                }
                list.add(tcd);
            }
            map.put("tranIdList", list);
            ps.close();

            getTrsPrimaryKeyPartOne(conn, sqlCount, map);

        } catch (Exception ee)
        {
            _log.error("AgentUpDataManager.getTrsPrimaryKey error!", ee);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, res, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return map;
    }

    public String getTrsPrimaryKeyPartOne (String sort) {
        String orderBy = " ORDER BY ITRANSMISSION_ID ";
        try
        {
            if (sort != null && !"".equals(sort))
            {
                JSONArray jsonList = JSONArray.parseArray(sort);
                JSONObject jsonObj = (JSONObject) jsonList.get(0);
                if ("transmissionType".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ITRANSMISSION_TYPE " + jsonObj.getString(DIRECTION);
                } else if ("transmissionIp".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ITRANSMISSION_IP " + jsonObj.getString(DIRECTION);
                } else if ("transmissionPort".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ITRANSMISSION_PROT " + jsonObj.getString(DIRECTION);
                } else if ("transmissionUser".equals(jsonObj.getString(PROPERTY)))
                {
                    orderBy = " order by ITRANSMISSION_USER " + jsonObj.getString(DIRECTION);
                } else
                {
                    orderBy = " order by ITRANSMISSION_ID" + " " + jsonObj.getString(DIRECTION);
                }
            }
        } catch (Exception e)
        {
            orderBy = " ORDER BY ITRANSMISSION_ID ";
        }
        return orderBy;
    }

    public void getTrsPrimaryKeyPartOne ( Connection conn,String sqlCount,Map<String, Object> map) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sqlCount);
            rs = ps.executeQuery();

            while (rs.next())
            {
                count = rs.getInt("NUM");
            }
            map.put("total", count);
        } catch (Exception e)
        {
            _log.error("AgentUpDataManager.getTrsPrimaryKeyPartOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getTrsPrimaryKeyPartOne", _log);
        }
    }


    /**
     *
     * <li>Description:通过AgentUpId查询transmissionId</li>
     * <AUTHOR>
     * 2016年7月27日
     * @param agentUpId
     * @param conn
     * @return
     * return long
     */
    public long queryTsIdByAgentId ( long agentUpId, Connection conn )
    {
        PreparedStatement ps = null;
        ResultSet res = null;
        long transmissionId = 0;
        String sql = "SELECT ITRANSMISSION_ID FROM IEAI_AGENTUPDATE_INFO WHERE IAGENTUP_ID=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, agentUpId);
            res = ps.executeQuery();
            while (res.next())
            {
                transmissionId = res.getLong(ITRANSMISSIONIDU);
            }
        } catch (Exception ee)
        {
            _log.error("AgentUpDataManager.queryTsIdByAgentId Error!", ee);
        } finally
        {
            DBResource.closePSRS(res, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return transmissionId;
    }

    /**
     *
     * <li>Description:对Agent升级信息数据进行修改操作</li>
     * <AUTHOR>
     * 2016年7月26日
     * @param sysType
     * @param agentUpId
     * @param iosName
     * @param iosUser
     * @param isourcePath
     * @param itargetPath
     * @param ibackupPath
     * @param operPwd
     * @param connType
     * @param connPort
     * @param transmissionId
     * @return
     * @throws RepositoryException
     * @throws Exception
     * return boolean
     */
    public boolean updateAgentUpData ( long agentUpId, ParamBean bean ) throws RepositoryException
    {
        int exceptionPosition = 0;
        List<Object> listRollBack = new ArrayList<Object>();
        boolean resp = true;
        try
        {
            List<DBSourceMonitor> list = ProjectManagerForMultiple.getInstance()
                    .getDBsourceList(Constants.IEAI_IEAI_BASIC);
            if (null != list && !list.isEmpty())
            {
                for (int k = 0; k < list.size(); k++)
                {
                    DBSourceMonitor dBSourceMonitor = list.get(k);
                    updateAgentUpDataPartTwo(agentUpId, dBSourceMonitor, listRollBack, bean);
                }
            }
        } catch (Exception e)
        {
            List<Object> loglist = new ArrayList<Object>();
            try
            {
                agentupdataRollBack(listRollBack, exceptionPosition, loglist);
            } catch (Exception e1)
            {
                for (int logi = 0; logi < loglist.size(); logi++)
                {
                    String rRoll = (String) loglist.get(logi);
                    dbsRollbacklog.error(rRoll);
                }
                _log.error(MESSAGEONE, e1);
            }
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        }
        return resp;
    }

    public void updateAgentUpDataPartTwo(long agentUpId,DBSourceMonitor dBSourceMonitor,List<Object> listRollBack,ParamBean bean) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        String  iosName = bean.getIosName();
        String  isourcePath = bean.getIsourcepath();
        String  itargetPath = bean.getItargetpath();
        String  ibackupPath = bean.getIbackupPath();
        int connType = bean.getConnType();
        long connPort = bean.getConnPort();
        String  iosUser = bean.getIosUser();
        String  operPwd = bean.getOperPwd();
        int isusp = bean.getIsusp();
        String updateVersion=bean.getUpdateVersion();
        long actFinishMaxWait=bean.getActFinishMaxWait();
        long writeBufferSize=bean.getWriteBufferSize();
        try
        {
            String format = DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss");
            String time="";
            if(JudgeDB.IEAI_DB_TYPE == 3){
                time="STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s')";
            }else{
                time="TO_DATE(?,'yyyy-mm-dd,hh24:mi:ss')";
            }
            String tacticsName="";
            String outTime="";

            if(StringUtils.isNotBlank(bean.getTacticsName())){
                tacticsName =" TACTICS_NAME='"+bean.getTacticsName()+"'," ;
            }
            if(bean.getTimeOut()>0){
                outTime =" OUT_TIME="+bean.getTimeOut()+"," ;
            }

            String sql = "update IEAI_AGENTUPDATE_INFO set ISOURCE_PATH =?,ITARGET_PATH =?,IBACKUP_PATH =?,IOS_NAME =?,ICONNECT_TYPE =?, ICONNECT_PORT =?,IOPERATION_USER =?,IOPERATION_PASSWORD =?,ISUSP=?,UPDATEVERSION=?,ACTFINISHMAXWAIT=?,WRITEBUFFERSIZE=?,"+tacticsName+outTime+"CREATE_TIME="+time+"  where IAGENTUP_ID =?";
            conn = DBResource.getConnection("saveServiceInfo", _log,
                    Integer.valueOf(String.valueOf(dBSourceMonitor.getGroupId())));
            updateAgentUpDataPartOne(agentUpId, listRollBack, conn);
            ps = conn.prepareStatement(sql);
            ps.setString(1, isourcePath);
            ps.setString(2, itargetPath);
            ps.setString(3, ibackupPath);
            ps.setString(4, iosName);
            ps.setInt(5, connType);
            ps.setLong(6, connPort);
            ps.setString(7, iosUser);
            ps.setString(8, operPwd);
            ps.setInt(9, isusp);
            ps.setString(10, updateVersion);
            ps.setLong(11, actFinishMaxWait);
            ps.setLong(12, writeBufferSize);
            ps.setString(13, format);
            ps.setLong(14, agentUpId);
            ps.executeUpdate();
            conn.commit();
        } catch (Exception ee)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, ee, "updateAgentUpDataPartTwo", _log);
            _log.error("AgentUpDataManager.updateAgentUpDataPartTwo Error!", ee);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);

        } finally
        {
            DBResource.closePSConn(conn, ps, "updateAgentUpDataPartTwo", _log);
        }
    }

    public void updateAgentUpDataPartOne(long agentUpId,List<Object> listRollBack,Connection conn) throws RepositoryException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String rolsql = "select * from IEAI_AGENTUPDATE_INFO where IAGENTUP_ID = ?";
            ps = conn.prepareStatement(rolsql);
            ps.setLong(1, agentUpId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put(IAGENTUPID, rs.getString(IAGENTUPID));
                map.put(ISOURCEPATH, rs.getString(ISOURCEPATH));
                map.put(ITARGETPATH, rs.getString(ITARGETPATH));
                map.put(IBACKUPPATH, rs.getString(IBACKUPPATH));
                map.put(IOPERATIONUSER, rs.getString(IOPERATIONUSER));
                map.put(IOPERATIONPD, rs.getString(IOPERATIONPD));
                map.put(ICONNECTTYPE, rs.getString(ICONNECTTYPE));
                map.put(ICONNECTPORT, rs.getString(ICONNECTPORT));
                map.put(ITRANSMISSIONID, rs.getString(ITRANSMISSIONID));
                map.put(ISUSP, rs.getString(ISUSP));
                map.put(IOSNAME, rs.getString(IOSNAME));
                map.put(OPERATIONTYPE, "update");
                listRollBack.add(map);
            }
        } catch (Exception e)
        {
            _log.error("AgentUpDataManager.updateAgentUpDataPartOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "updateAgentUpDataPartOne", _log);
        }
    }

    public void agentupdataRollBack ( List<Object> listRollBack, int exceptionPosition, List<Object> loglist ) throws RepositoryException
    {
        List<DBSourceMonitor> list = ProjectManagerForMultiple.getInstance().getDBsourceList(Constants.IEAI_IEAI_BASIC);
        int key = 0;
        if (list == null || list.isEmpty())
        {
            return;
        }
        for (int i = 0; i < exceptionPosition; i++)
        {
            DBSourceMonitor dBSourceMonitor = list.get(i);
            Connection conn = null;
            try
            {
                key = Integer.valueOf(String.valueOf(dBSourceMonitor.getGroupId()));
                conn = DBResource.getConnection("uploadRollBack", _log, key);
                if (listRollBack == null || listRollBack.isEmpty())
                {
                    return;
                }
                for (int a = 0; a < listRollBack.size(); a++)
                {
                    Map<String, Object> map = (Map) listRollBack.get(a);
                    String businessTypeRollBackLog = "";
                    if (map.get(OPERATIONTYPE) != null)
                    {
                        businessTypeRollBackLog = agentupdataRollBackPartOne(key, map, conn);
                    }
                    loglist.add(businessTypeRollBackLog);
                }
                conn.commit();
            } catch (Exception e)
            {
                DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, e,"agentupdataRollBack", _log);
            } finally
            {
                DBResource.closeConnection(conn, "agentupdataRollBack", _log);
            }

        }
    }

    public String agentupdataRollBackPartOne(int key,Map<String, Object> map,Connection conn) throws RepositoryException {
        String businessTypeRollBackLog = "";
        if ("insert".equals(String.valueOf(map.get(OPERATIONTYPE))))
        {
            deleteAgentupdataRoll(Long.valueOf(String.valueOf(map.get(IAGENTUPID))), conn);
            businessTypeRollBackLog = "数据源:" + key
                    + SQLONE
                    + map.get(IAGENTUPID);
        } else if ("update".equals(String.valueOf(map.get(OPERATIONTYPE))))
        {
            updateAgentupdataRoll(map, conn);
            businessTypeRollBackLog = "数据源:" + key
                    + SQLONE
                    + map.get(IAGENTUPID);
        } else if ("delete".equals(String.valueOf(map.get(OPERATIONTYPE))))
        {
            insertAgentupdataRoll(map, conn);
            businessTypeRollBackLog = "数据源:" + key
                    + SQLONE
                    + map.get(IAGENTUPID) + " isource_path:" + map.get(ISOURCEPATH)
                    + " itarget_path:" + map.get(ITARGETPATH) + " ibackup_path:"
                    + map.get(IBACKUPPATH) + " ioperation_user:"
                    + map.get(IOPERATIONUSER) + " ioperation_password:"
                    + map.get(IOPERATIONPD) + " iconnect_type:"
                    + map.get(ICONNECTTYPE) + " iconnect_port:" + map.get(ICONNECTPORT)
                    + " itransmission_id:" + map.get(ITRANSMISSIONID);
        } else if ("updatePk".equals(String.valueOf(map.get(OPERATIONTYPE))))
        {
            updateAgentupdataPkRoll(map, conn);
            businessTypeRollBackLog = "数据源:" + key
                    + SQLONE
                    + map.get(IAGENTUPID);
        }
        return businessTypeRollBackLog;
    }

    public void updateAgentupdataPkRoll ( Map<String, Object> map, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "update IEAI_AGENTUPDATE_INFO set itransmission_id = 0 where iagentup_id = ?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.parseLong(map.get(IAGENTUPID).toString()));
            ps.executeUpdate();
        } catch (SQLException e)
        {
            _log.error("AgentUpDataManager.updateAgentupdataPkRoll Error", e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateAgentupdataPkRoll", _log);
        }
    }

    public void deleteAgentupdataRoll ( long iid, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "delete from IEAI_AGENTUPDATE_INFO where iagentup_id = ?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.executeUpdate();
        } catch (SQLException e)
        {
            _log.error("AgentUpDataManager.deleteAgentupdataRoll Error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "deleteAgentupdataRoll", _log);
        }
    }

    public void updateAgentupdataRoll ( Map<String, Object> map, Connection conn ) throws RepositoryException
    {
        String pd = "password";
        PreparedStatement ps = null;
        String sql = "update IEAI_AGENTUPDATE_INFO set isource_path=?,itarget_path=?,ibackup_path=?,ioperation_user=?, ioperation_"+pd+"=?,ios_name=?,iconnect_type=?,iconnect_port=?,itransmission_id=?,isusp=? where iagentup_id=?";
        try
        {
            int index = 0;
            ps = conn.prepareStatement(sql);
            ps.setString(++index, map.get(ISOURCEPATH).toString());
            ps.setString(++index, map.get(ITARGETPATH).toString());
            ps.setString(++index, map.get(IBACKUPPATH).toString());
            ps.setString(++index, map.get(IOPERATIONUSER).toString());
            ps.setString(++index, map.get(IOPERATIONPD).toString());
            ps.setString(++index, map.get(IOSNAME).toString());
            ps.setInt(++index, Integer.parseInt(map.get(ICONNECTTYPE).toString()));
            ps.setString(++index, map.get(ICONNECTPORT).toString());
            ps.setLong(++index, Long.parseLong(map.get(ITRANSMISSIONID).toString()));
            ps.setInt(++index, Integer.parseInt(map.get(ISUSP).toString()));
            ps.setLong(++index, Long.parseLong(map.get(IAGENTUPID).toString()));
            ps.executeUpdate();
        } catch (SQLException e)
        {
            _log.error("AgentUpDataManager.updateAgentupdataRoll Error", e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateAgentupdataRoll", _log);
        }
    }

    public void insertAgentupdataRoll ( Map<String, Object> map, Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "insert into IEAI_AGENTUPDATE_INFO (IAGENTUP_ID,ISOURCE_PATH,ITARGET_PATH,IBACKUP_PATH,IOPERATION_USER,IOPERATION_PASSWORD,ICONNECT_TYPE,ICONNECT_PORT,ISUSP,IOS_NAME) values(?,?,?,?,?,?,?,?,?,?) ";
        int index = 0;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(++index, Long.parseLong(map.get(IAGENTUPID).toString()));
            ps.setString(++index, map.get(ISOURCEPATH).toString());
            ps.setString(++index, map.get(ITARGETPATH).toString());
            ps.setString(++index, map.get(IBACKUPPATH).toString());
            ps.setString(++index, map.get(IOPERATIONUSER).toString());
            ps.setString(++index, map.get(IOPERATIONPD).toString());
            ps.setString(++index, map.get(ICONNECTTYPE).toString());
            ps.setString(++index, map.get(ICONNECTPORT).toString());
            ps.setString(++index, map.get(ITRANSMISSIONID).toString());
            ps.setString(++index, map.get(ISUSP).toString());
            ps.setString(++index, map.get(IOSNAME).toString());
            ps.executeUpdate();
        } catch (SQLException e)
        {
            _log.error("ServiceInfoConfigManager.insertServiceTypeRoll Error", e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePreparedStatement(ps, "insertServiceTypeRoll", _log);
        }

    }

    /**
     *
     * <li>Description:添加Agent升级信息</li>
     * <AUTHOR>
     * 2016年7月26日
     * @param sysType
     * @param iosName
     * @param iosUser
     * @param isourcePath
     * @param itargetPath
     * @param ibackupPath
     * @param operPwd
     * @param connType
     * @param connPort
     * @param transmissionId
     * @return
     * @throws RepositoryException
     * @throws Exception
     * return boolean
     */
    public Map<String, Object> insertAgentUpData ( ParamBean bean ) throws RepositoryException
    {
        int exceptionPosition = 0;
        List<Object> listRollBack = new ArrayList<Object>();
        boolean resp = true;
        long agentUpId = 0;
        Map<String, Object> resMap = new HashMap<String, Object>();
        String sql1 = "insert into IEAI_AGENTUPDATE_INFO(IAGENTUP_ID,ISOURCE_PATH,ITARGET_PATH,IBACKUP_PATH,IOS_NAME,ICONNECT_TYPE,ICONNECT_PORT,IOPERATION_USER,IOPERATION_PASSWORD,ISUSP,UPDATEVERSION,ACTFINISHMAXWAIT,WRITEBUFFERSIZE,TACTICS_NAME,OUT_TIME,CREATE_TIME)" +
                " values";
        String value="";
        if(JudgeDB.IEAI_DB_TYPE == 3){
            value= "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,STR_TO_DATE(?,'%Y-%m-%d %H:%i:%s') ) ";
        }else{
            value= "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,TO_DATE(?,'yyyy-mm-dd,hh24:mi:ss') ) ";
        }

        String sql=sql1+value;
        try
        {
            List<DBSourceMonitor> list = ProjectManagerForMultiple.getInstance().getDBsourceList(
                    Constants.IEAI_IEAI_BASIC);
            if (null != list && !list.isEmpty())
            {
                for (int k = 0; k < list.size(); k++)
                {
                    exceptionPosition = k;
                    DBSourceMonitor dBSourceMonitor = list.get(k);
                    agentUpId = insertAgentUpDataPartOne(dBSourceMonitor, sql, bean, listRollBack);
                }
            }
        } catch (Exception e)
        {
            List<Object> loglist = new ArrayList<Object>();
            try
            {
                agentupdataRollBack(listRollBack, exceptionPosition, loglist);
            } catch (Exception e1)
            {
                for (int logi = 0; logi < loglist.size(); logi++)
                {
                    String rRoll = (String) loglist.get(logi);
                    dbsRollbacklog.error(rRoll);
                }
                _log.error(MESSAGEONE, e1);
            }
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        }
        resMap.put("res", resp);
        resMap.put("primaryKey", String.valueOf(agentUpId));
        return resMap;
    }

    public long insertAgentUpDataPartOne(DBSourceMonitor dBSourceMonitor,String sql,ParamBean bean,List<Object> listRollBack) throws RepositoryException {
        long agentUpId = 0;
        Connection conn = null;
        PreparedStatement ps = null;
        String  iosName = bean.getIosName();
        String  isourcePath = bean.getIsourcepath();
        String  itargetPath = bean.getItargetpath();
        String  ibackupPath = bean.getIbackupPath();
        int connType = bean.getConnType();
        long connPort = bean.getConnPort();
        String  iosUser = bean.getIosUser();
        String  operPwd = bean.getOperPwd();
        int usp = bean.getIsusp();
        String updateVersion=bean.getUpdateVersion();
        long actFinishMaxWait=bean.getActFinishMaxWait();
        long writeBufferSize=bean.getWriteBufferSize();
        String format = DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss");
        try
        {
            conn = DBResource.getConnection(INSERTAGENTUPDATA, _log,
                    Integer.valueOf(String.valueOf(dBSourceMonitor.getGroupId())));
            ps = conn.prepareStatement(sql);
            agentUpId = IdGenerator.createId("IEAI_AGENTUPDATE_INFO", conn);
            Map<String, Object> tempMap = new HashMap<String, Object>();
            tempMap.put(IAGENTUPID, agentUpId);
            tempMap.put(OPERATIONTYPE, "insert");
            listRollBack.add(tempMap);
            ps.setLong(1, agentUpId);
            ps.setString(2, isourcePath);
            ps.setString(3, itargetPath);
            ps.setString(4, ibackupPath);
            ps.setString(5, iosName);
            ps.setInt(6, connType);
            ps.setLong(7, connPort);
            ps.setString(8, iosUser);
            ps.setString(9, operPwd);
            ps.setInt(10, usp);
            ps.setString(11, updateVersion);
            ps.setLong(12, actFinishMaxWait);
            ps.setLong(13, writeBufferSize);
            ps.setString(14, bean.getTacticsName());
            ps.setLong(15, bean.getTimeOut());
            ps.setString(16,format);
            ps.executeUpdate();
            conn.commit();
        } catch (Exception ee)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, ee,INSERTAGENTUPDATA, _log);
            _log.error("AgentUpDataManager.insertAgentUpData Error!", ee);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePSConn(conn, ps, INSERTAGENTUPDATA, _log);
        }
        return agentUpId;
    }

    /**
     *
     * <li>Description:删除数据操作</li>
     * <AUTHOR>
     * 2016年7月26日
     * @param agentUpId
     * @param sysType
     * @return
     * @throws RepositoryException
     * @throws Exception
     * return boolean
     */
    public boolean deleteAgentUpData ( long[] agentUpIds ) throws RepositoryException
    {
        int exceptionPosition = 0;
        List<Object> listRollBack = new ArrayList<Object>();
        boolean resp = true;
        try
        {
            List<DBSourceMonitor> list = ProjectManagerForMultiple.getInstance().getDBsourceList(
                    Constants.IEAI_IEAI_BASIC);
            if (null != list && !list.isEmpty())
            {
                for (int k = 0; k < list.size(); k++)
                {
                    exceptionPosition = k;
                    DBSourceMonitor dBSourceMonitor = list.get(k);
                    deleteAgentUpDataPartThree(agentUpIds, dBSourceMonitor,listRollBack);
                }
            }
        } catch (Exception e)
        {
            List<Object> loglist = new ArrayList<Object>();
            try
            {
                agentupdataRollBack(listRollBack, exceptionPosition, loglist);
            } catch (Exception e1)
            {
                for (int logi = 0; logi < loglist.size(); logi++)
                {
                    String rRoll = (String) loglist.get(logi);
                    dbsRollbacklog.error(rRoll);
                }
                _log.error(MESSAGEONE, e1);
            }
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        }
        return resp;
    }

    public void deleteAgentUpDataPartThree(long[] agentUpIds,DBSourceMonitor dBSourceMonitor,List<Object> listRollBack) throws RepositoryException {
        Connection conn = null;
        try
        {
            int sysType = Integer.parseInt(String.valueOf(dBSourceMonitor.getGroupId()));
            conn = DBResource.getConnection("deleteAgentUpData", _log, sysType);
            for (int i = 0; i < agentUpIds.length; i++)
            {
                long agentUpId = agentUpIds[i];
                if (listRollBack.size() != agentUpIds.length)
                {
                    deleteAgentUpDataPartOne(agentUpId, listRollBack, conn);
                }
                deleteAgentUpDataPartTwo(agentUpId, conn);
            }
            conn.commit();
        } catch (Exception ee)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, ee, "deleteAgentUpDataPartThree", _log);
            _log.error("AgentUpDataManager.deleteAgentUpData Error!", ee);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        }finally {
            DBResource.closeConnection(conn, "deleteAgentUpData", _log);
        }
    }

    public void deleteAgentUpDataPartTwo(long agentUpId,Connection conn) throws RepositoryException {
        PreparedStatement ps = null;
        try
        {
            String sql = "DELETE FROM IEAI_AGENTUPDATE_INFO WHERE IAGENTUP_ID=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, agentUpId);
            ps.executeUpdate();
        } catch (Exception e)
        {
            _log.error("AgentUpDataManager.deleteAgentUpDataPartTwo is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "deleteAgentUpDataPartTwo", _log);
        }

    }

    public void deleteAgentUpDataPartOne(long agentUpId,List<Object> listRollBack,Connection conn) throws RepositoryException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String bksql = "select * from IEAI_AGENTUPDATE_INFO where IAGENTUP_ID = ?";
            ps = conn.prepareStatement(bksql);
            ps.setLong(1, agentUpId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put(IAGENTUPID, rs.getString(IAGENTUPID));
                map.put(ISOURCEPATH, rs.getString(ISOURCEPATH));
                map.put(ITARGETPATH, rs.getString(ITARGETPATH));
                map.put(IBACKUPPATH, rs.getString(IBACKUPPATH));
                map.put(IOPERATIONUSER, rs.getString(IOPERATIONUSER));
                map.put(IOPERATIONPD, rs.getString(IOPERATIONPD));
                map.put(ICONNECTTYPE, rs.getString(ICONNECTTYPE));
                map.put(ICONNECTPORT, rs.getString(ICONNECTPORT));
                map.put(ITRANSMISSIONID, rs.getString(ITRANSMISSIONID));
                map.put(IOSNAME, rs.getString(IOSNAME));
                map.put(ISUSP, rs.getString(ISUSP));
                map.put(OPERATIONTYPE, "delete");
                listRollBack.add(map);
            }
        } catch (Exception e)
        {
            _log.error("AgentUpDataManager.deleteAgentUpDataPartOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "deleteAgentUpDataPartOne", _log);
        }
    }

    /**
     *
     * <li>Description:添加传输配置主键</li>
     * <AUTHOR>
     * 2016年7月26日
     * @param transmisionId
     * @param agentUpId
     * @param sysType
     * @return
     * @throws RepositoryException
     * @throws Exception
     * return boolean
     */
    public boolean savePrimaryKey ( long transmisionId, long agentUpId ) throws RepositoryException
    {
        int exceptionPosition = 0;
        List<Object> listRollBack = new ArrayList<Object>();
        boolean resp = true;
        String sql = "update IEAI_AGENTUPDATE_INFO set ITRANSMISSION_ID =? where IAGENTUP_ID =?";
        try
        {
            List<DBSourceMonitor> list = ProjectManagerForMultiple.getInstance().getDBsourceList(
                    Constants.IEAI_IEAI_BASIC);
            if (null != list && !list.isEmpty())
            {
                for (int k = 0; k < list.size(); k++)
                {
                    exceptionPosition = k;
                    DBSourceMonitor dBSourceMonitor = list.get(k);
                    savePrimaryKeyPartOne(listRollBack, dBSourceMonitor, sql, transmisionId, agentUpId);
                }
            }

        } catch (Exception e)
        {
            List<Object> loglist = new ArrayList<Object>();
            try
            {
                agentupdataRollBack(listRollBack, exceptionPosition, loglist);
            } catch (Exception e1)
            {
                for (int logi = 0; logi < loglist.size(); logi++)
                {
                    String rRoll = (String) loglist.get(logi);
                    dbsRollbacklog.error(rRoll);
                }
                _log.error(MESSAGEONE, e1);
            }
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        }
        return resp;
    }

    public void savePrimaryKeyPartOne ( List<Object> listRollBack, DBSourceMonitor dBSourceMonitor, String sql,
                                        long transmisionId, long agentUpId ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBResource.getConnection("savePrimaryKey", _log,
                    Integer.valueOf(String.valueOf(dBSourceMonitor.getGroupId())));
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("IAGENTUP_ID", agentUpId);
            map.put(OPERATIONTYPE, "updatePk");
            listRollBack.add(map);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, transmisionId);
            ps.setLong(2, agentUpId);
            ps.executeUpdate();
            conn.commit();
        } catch (Exception ee)
        {
            DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, ee,"savePrimaryKeyPartOne", _log);
            _log.error("AgentUpDataManager.savePrimaryKeyPartOne Error!", ee);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "savePrimaryKeyPartOne", _log);
        }
    }

    /**
     *
     * <li>Description:分页查询，查看数据总数</li>
     * <AUTHOR>
     * 2016年7月25日
     * @param conn
     * @param iosName
     * @return
     * @throws Exception
     * return int
     */
    public int cntAgentUpData ( Connection conn, String iosName ) throws RepositoryException
    {
        String sqlWhere = "";
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            if (!"".equals(iosName) && iosName != null)
            {
                sqlWhere = "and (upper(IOS_NAME) like ? or upper(IOPERATION_USER) like ? )";

            }
            ps = conn.prepareStatement("select count(*) as count from IEAI_AGENTUPDATE_INFO  where 1=1 " + sqlWhere);
            int sqlIndex=1;
            if (!"".equals(iosName) && iosName != null)
            {
                ps.setString(sqlIndex++, "%"+iosName.toUpperCase()+"%");
                ps.setString(sqlIndex++, "%"+iosName.toUpperCase()+"%");
            }
            rset = ps.executeQuery();
            while (rset.next())
            {
                count = rset.getInt("count");
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return count;

    }

}
