Ext.onReady(function() {


	/** 采集状态Model**/
	Ext.define ('hcCollectHisModel',
		{
			extend : 'Ext.data.Model',
			fields : [
				{
					name :'checktime',
					type : 'string'
				},{
					name :'storetime',
					type : 'string'
				}, {
					name : 'computerid',
					type : 'long'
				}, {
					name : 'rsdid',
					type : 'long'
				}, {
					name : 'ichkitemid',
					type : 'long'
				}, {
					name : 'pointid',
					type : 'long'
				}, {
					name : 'sysid',
					type : 'long'
				}, {
					name : 'collectstatus',
					type : 'long'
				},{
					name :'hcstatus',
					type : 'long'
				},{
					name :'chkpoint',
					type : 'string'
				},{
					name :'chkitemname',
					type : 'string'
				},{
					name :'computerip',
					type : 'string'
				},{
					name :'warnlevel',
					type : 'long'
				},{
					name :'lastline',
					type : 'string'
				}
			]
		});



	var hcCollectHisStore = Ext.create ('Ext.data.Store',
		{
			autoLoad : true,
			pageSize : '30',
			model : 'hcCollectHisModel',
			proxy :
				{
					type : 'ajax',
					url : 'hccollect/monitorHisDetails.do',
					reader :
						{
							type : 'json',
							root : 'dataList'
						}
				}
		});


	hcCollectHisStore.on ('beforeload', function (store, options)
	{
		var new_params =
			{
				computerid : hiscomputerid,
				ichkitemid:hisichkitemid,
				isysid : hisisysid,
				start:0,
				limit:10,
			};
		Ext.apply (hcCollectHisStore.proxy.extraParams, new_params);
	});


	var hcCollectHisColumns = [
		{
			text : '序号',
			width : 35,
			xtype : 'rownumberer'
		},
		{
			text : '设备ID',
			dataIndex : 'computerid',
			width : 100,
			hidden:true,

			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		},
		{
			text : '结果ID',
			dataIndex : 'rsdid',
			width : 100,
			hidden:true,

			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		},
		{
			text : 'applogo',
			dataIndex : 'sysid',
			width : 100,
			hidden : true,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		}, {
			text : '采集项名称',
			dataIndex : 'chkitemname',
			width : 150,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		}, {
			text : '采集设备IP',
			dataIndex : 'computerip',
			width : 150,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		}, {
			text : '采集点名称',
			dataIndex : 'chkpoint',
			minWidth : 150,
			flex:1,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		}, {
			text : '采集时间',
			dataIndex : 'checktime',
			width : 180,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		}, {
			text : '存储时间',
			dataIndex : 'storetime',
			width : 180,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		},
		{
			text : '采集状态',
			dataIndex : 'collectstatus',
			width : 120,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				var returnname = "";
				if(value==-2){
					returnname = "<span style='color:#B22222;'>推送异常</span>";//推送kafka异常
				}else if(value==0){
					returnname = "<span style='color:#ffcc00;'>采集中</span>";//结果不存在，未推送kafka
				}else if(value==-1){
					returnname = "<span style='color:#71C671;'>已采集</span>";//结果存在，未推送kafka
				}else if(value==1){
					returnname = "<span style='color:#008000;'>已推送</span>";//结果存在，成功推送kafka
				}
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ returnname + '"';
				return returnname;
			}
		},{
			text : '脚本状态',
			dataIndex : 'warnlevel',
			width : 120,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				var returnname = "";
				if(value=='0' || value=='' ){
					returnname = "<span style='color:#008000;'>正常</span>";//正常输出
				}else if(value=='-2'){
					returnname = "<span style='color:#B22222;'>未执行</span>";//执行脚本出错
				}else{
					returnname = "<span style='color:#B22222;'>异常</span>";//执行脚本出错
				}

				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ returnname + '"';
				return returnname;
			}
		},{
			text : '末行输出',
			dataIndex : 'lastline',
			// minWidth:120,
			// flex:1,
			hidden:true,
			renderer : function(value, metaData, record, rowIndex, colIndex) {
				metaData.tdAttr = 'qclass="x-tip" data-qtitle="" data-qwidth="200" data-qtip="'
					+ value + '"';
				return value;
			}
		}, {
			text : '操作',
			align : 'left',
			xtype:'actiontextcolumn',
			dataIndex : 'ichkitemid',
			width : 150,
			items : [
				{
					text : '采集输出',
					iconCls : 'monitor_search',
					handler : function(grid, rowIndex) {
						var rsdid = grid.getStore().getAt(rowIndex).get('rsdid');
						var pointid = grid.getStore().getAt(rowIndex).get('pointid');
						showCollectOutPut(rsdid,pointid,grid.getStore().getAt(rowIndex).get('chkpoint'));
					}
				}
			]
		}
	];




	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});

	var hcCollect_grid_panel = Ext.create('Ext.grid.Panel',{
		//  width: contentPanel.getWidth(),
		cls:'customize_panel_back',
		region : 'center',
		forceFit: true,
	//	width:contentPanel.getWidth ()-200,
		width : contentPanel.getWidth ()-240,
		height : contentPanel.getHeight ()-modelHeigth-150,
		store : hcCollectHisStore,
		//  selModel : Ext.create('Ext.selection.CheckboxModel',{checkOnly:true}),
		//  plugins: [cellEditing],
		border : false,
		columnLines : true,
		padding : grid_space,
		columns : hcCollectHisColumns,
		animCollapse : false,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		autoScroll:true,
		listeners : {
			beforeedit : function(editor, e, eOpts){
				return true;
			}
		}
	});







	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "hccollectmonitor_HIS_div",
		border : false,
		autoScroll : true,
	//	width : contentPanel.getWidth ()-100,
	//	height : contentPanel.getHeight ()-modelHeigth,
		cls:'customize_panel_back', //引入圆角
		bodyCls: 'service_platform_bodybg',

		items : [ hcCollect_grid_panel ]
	});
	mainPanel.setWidth(contentPanel.getWidth()-240);
	mainPanel.setHeight(contentPanel.getHeight()-modelHeigth-150);


	/*	if(contentPanel.getHeader()){
            contentPanel.getHeader().hide();//设置contentPanel标题头隐藏

        }*/

	contentPanel.on('resize',function(){
		mainPanel.setWidth(contentPanel.getWidth()-240);
		mainPanel.setHeight(contentPanel.getHeight()-modelHeigth-150);
	});

	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader().on("beforeload",
		function (obj, options, eOpts) {
			Ext.destroy(mainPanel);
			if (Ext.isIE) {
				CollectGarbage();
			}
		});


	function showCollectOutPut(rsdid,cpid,title){
		if(rsdid<=0 || rsdid=='undefined' || rsdid==undefined){

			Ext.Msg.show({
				title:'提示',
				msg: '暂无脚本输出信息！',
				buttons: Ext.Msg.OK,
				icon: Ext.Msg.INFO
			});
			return;

		}else{
			var showCollectHisDetailOutPutWin = Ext.create('Ext.window.Window', {
				draggable : true,// 禁止拖动
				resizable : false,// 禁止缩放
				modal : true,
				title : title,
				closable : true,
				closeAction : 'destroy',
				width : contentPanel.getWidth()-100,
				height : contentPanel.getHeight()-modelHeigth-100,
				layout : 'fit',
				autoScroll:false,
				loader : {
					url : 'hccollect/collectOutPutindex.do',
					params : {
						rsdid : rsdid,
						cpid:cpid,
						height:contentPanel.getHeight()-modelHeigth-165
					},
					autoLoad : true,
					autoDestroy : true,
					scripts : true
				}
			}).show();
		}
	}

});