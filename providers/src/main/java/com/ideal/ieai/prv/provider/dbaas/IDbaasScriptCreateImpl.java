package com.ideal.ieai.prv.provider.dbaas;

import com.ideal.dubbo.interfaces.dbaas.IDbaasScriptCreateService;
import com.ideal.dubbo.models.ScriptParameter;
import com.ideal.dubbo.models.ScriptService;
import com.ideal.ieai.ExternalSysDBManager;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.prv.provider.ScriptServiceManage;
import com.ideal.ieai.prv.provider.utils.ParseJson;
import com.ideal.ieai.prv.repository.hd.dubbo.dbaas.DbaasScriptCreateManager;
import com.ideal.ieai.prv.repository.hd.scriptCreate.ScriptCreateManager;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.topo.common.DbPageSize;
import com.ideal.util.JsonUtil;
import com.ideal.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@DubboService
public class IDbaasScriptCreateImpl implements IDbaasScriptCreateService
{
    private static final String MESSAGE  = "message";
    private static final String SUCCESS  = "success";
    private static final String DATALIST = "dataList";
    private static final String LISTDATA = "listData";
    private static final String LISTCOL  = "listCol";
    private static final Logger logger   = Logger.getLogger(IDbaasScriptCreateImpl.class);

    public Map<String, Object> queryScriptInfo ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            String scriptId = map.get("iid");
            retMap = manage.queryScriptInfo(conn, scriptId);
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    public Map<String, Object> saveDbaasScriptCreate ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            String oldId = map.get("iid");

            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            // 黑名单命令检测
            Map keyWordMap = new HashMap();
            boolean isKeyWord = ScriptCreateManager.getInstance().compareKeyWord(keyWordMap,
                String.valueOf(map.get("content")), String.valueOf(map.get("scriptType")));
            boolean tipKeyWord = false;
            boolean screenKeyWord = false;
            if (isKeyWord)
            {
                tipKeyWord = Boolean.parseBoolean(String.valueOf(keyWordMap.get("hasTipKeyWord")));
                screenKeyWord = Boolean.parseBoolean(String.valueOf(keyWordMap.get("hasScreenKeyWord")));
            }
            if (screenKeyWord)
            {
                retMap.put("success", false);
                retMap.put("message", String.valueOf(keyWordMap.get("screenkeyword")));
                return retMap;
            }

            List<ScriptParameter> repParams = new ArrayList<ScriptParameter>();
            List<Map<String, Object>> listMaps = ParseJson.JSON2List(map.get("params"));
            for (int i = 0; i < listMaps.size(); i++)
            {
                Map<String, Object> paramMap = listMaps.get(i);
                repParams.add(this._convertToScriptParameter(paramMap));
            }
            long id = IdGenerator.createId("IEAI_SCRIPT_TEST", conn);
            int flag = Integer.parseInt(map.get("scriptstatus"));
            String serviceID = "";
            String scriptUUID = "";
            if (!map.containsKey("serviceNo") || (map.containsKey("serviceNo") && "".equals(map.get("serviceNo"))))
            {
                serviceID = ExternalSysDBManager._instance.createServiceID(id, 2);
            } else
            {
                serviceID = map.get("serviceNo");
            }
            scriptUUID = UUID.uuid();
            map.put("uuid", scriptUUID);
            map.put("serviceNo", serviceID);
            map.put("iid", String.valueOf(id));
            if (!map.containsKey("upperId"))
            {
                map.put("upperId", String.valueOf(id));
            }
            String relationsql = " insert into IEAI_SCRIPT_LABLEANDCATE (IID, ICATAVALUE, ICATARELATIONID, ILABLEVALUE,ILABLERELATIONID, ISERVICEID) values (?,?,?,?,?,?) ";
            String parasql = " insert into IEAI_SCRIPT_PARAMETER (IID, ISCRIPTID, IPARAM_TYPE, IPARAM_DEFAULT_VALUE, IPARAM_DESC, IPARAM_ORDER) values (?,?,?,?,?,?) ";
            String sql;
            if (oldId != null && !"0".equals(oldId) && !"0".equals(oldId))
            {
                // 根据oldId获取创建人id
                Long userid = manage.getScriptUserIdByiid(Long.parseLong(oldId));
                map.put("uperUserId", String.valueOf(userid));
                map.put("createFlag", "1");
                sql = "INSERT INTO IEAI_SCRIPT_TEST ( IID, ISCRIPTNAME, ISYSNAME,IBUSSNAME,ISCRIPTTYPE, ISERVICESNAME, ISCRIPTPARA,ICREATETIME, IUSERID, ISTATUS,ICONTENT,IBUSSID,IBUSSTYPEID,IINPUTDESC,IFUNCDESC,IUPERID,ILASTID,ISCRIPTUUID,IEXECUSER,IDBTYPE,ISERVICETYPE,IISEXAM,IPROJECTFLAG,ISAUTOSUB,MANUALSTART,ISQLEXECMODEL,ISERVICEID,ITIMEOUT,IUPDATEUSERID) values(?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW("
                        + Constants.getCurrentSysDate() + ",8),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            } else
            {
                map.put("createFlag", "0");
                sql = "INSERT INTO IEAI_SCRIPT_TEST ( IID, ISCRIPTNAME, ISYSNAME,IBUSSNAME,ISCRIPTTYPE, ISERVICESNAME, ISCRIPTPARA,ICREATETIME, IUSERID, ISTATUS,ICONTENT,IBUSSID,IBUSSTYPEID,IINPUTDESC,IFUNCDESC,IUPERID,ILASTID,ISCRIPTUUID,IEXECUSER,IDBTYPE,ISERVICETYPE,IISEXAM,IPROJECTFLAG,ISAUTOSUB,MANUALSTART,ISQLEXECMODEL,ISERVICEID,ITIMEOUT) values(?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW("
                        + Constants.getCurrentSysDate() + ",8),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            }

            retMap = manage.saveDbaasScriptCreate(sql, parasql, conn, repParams, map, relationsql);
            retMap.put("uuid", scriptUUID);
            retMap.put("iid", id);
            retMap.put("serviceNo", serviceID);

            if (tipKeyWord)
            {
                retMap.put("message",
                    String.valueOf(retMap.get("message")) + "\n" + String.valueOf(keyWordMap.get("tipkeyword")));
            }
            conn.commit();
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    public Map<String, Object> saveDbaasScriptEdit ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);

            // 黑名单命令检测
            Map keyWordMap = new HashMap();
            boolean isKeyWord = ScriptCreateManager.getInstance().compareKeyWord(keyWordMap,
                String.valueOf(map.get("content")), String.valueOf(map.get("scriptType")));
            boolean tipKeyWord = false;
            boolean screenKeyWord = false;
            if (isKeyWord)
            {
                tipKeyWord = Boolean.parseBoolean(String.valueOf(keyWordMap.get("hasTipKeyWord")));
                screenKeyWord = Boolean.parseBoolean(String.valueOf(keyWordMap.get("hasScreenKeyWord")));
            }
            if (screenKeyWord)
            {
                retMap.put("success", false);
                retMap.put("message", String.valueOf(keyWordMap.get("screenkeyword")));
                return retMap;
            }

            // 查询原来状态是草稿还是上线，上线，此处调用save，草稿直接调用下面方法
            manage.scriptStatus(conn, map);
            if (manage.isOnline(conn, map))
            {
                retMap = this.saveDbaasScriptCreate(map);
            } else
            {
                List<ScriptParameter> repParams = new ArrayList<ScriptParameter>();
                List<Map<String, Object>> listMaps = ParseJson.JSON2List(map.get("params"));
                for (int i = 0; i < listMaps.size(); i++)
                {
                    Map<String, Object> paramMap = listMaps.get(i);
                    repParams.add(this._convertToScriptParameter(paramMap));
                }
                String relationsql = " update IEAI_SCRIPT_LABLEANDCATE  set ICATAVALUE=?, ICATARELATIONID=?, ILABLEVALUE=?, ILABLERELATIONID=? where ISERVICEID=? ";
                String parasql = " insert into IEAI_SCRIPT_PARAMETER (IID, ISCRIPTID, IPARAM_TYPE, IPARAM_DEFAULT_VALUE, IPARAM_DESC, IPARAM_ORDER) values (?,?,?,?,?,?) ";
                String sql = "update IEAI_SCRIPT_TEST set  ISCRIPTNAME=? ,ISCRIPTTYPE=?, ICONTENT=?,IINPUTDESC=?,IFUNCDESC=?,IDBTYPE=?,ISERVICETYPE=?,IISEXAM=?,MANUALSTART=?,ISQLEXECMODEL=?, ISTATUS=?,ISERVICEID=?,IUPDATEUSERID=? where  IID=? ";
                retMap = manage.saveDbaasScriptEdit(sql, parasql, conn, repParams, map, relationsql);
            }
            manage.scriptLastId(conn, map, 0);

            if (tipKeyWord)
            {
                retMap.put("message",
                    String.valueOf(retMap.get("message")) + "\n" + String.valueOf(keyWordMap.get("tipkeyword")));
            }

            conn.commit();
        } catch (Exception e)
        {
            retMap.put(SUCCESS, false);
            retMap.put(MESSAGE, e.getMessage());
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    public Map<String, Object> dbaasScriptOnline ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            // 查询原来状态是草稿还是上线，上线，此处调用save，草稿直接调用下面方法
            // if(manage.isOnline(conn, map)) {
            // retMap.put(SUCCESS, true);
            // retMap.put(MESSAGE, "该脚本已经上线，不能在重复上线");
            // }
            Map resultMap = manage.scriptStatus(conn, map);
            retMap.put("iid", map.get("iid"));
            retMap.put("uuid", map.get("uuid"));
            if (!Boolean.parseBoolean(String.valueOf(resultMap.get("flag"))))
            {
                retMap = this.saveDbaasScriptCreate(map);
            } else
            {
                List<ScriptParameter> repParams = new ArrayList<ScriptParameter>();
                List<Map<String, Object>> listMaps = ParseJson.JSON2List(map.get("params"));
                for (int i = 0; i < listMaps.size(); i++)
                {
                    Map<String, Object> paramMap = listMaps.get(i);
                    repParams.add(this._convertToScriptParameter(paramMap));
                }
                String relationsql = " update IEAI_SCRIPT_LABLEANDCATE  set ICATAVALUE=?, ICATARELATIONID=?, ILABLEVALUE=?, ILABLERELATIONID=? where ISERVICEID=? ";
                String parasql = " insert into IEAI_SCRIPT_PARAMETER (IID, ISCRIPTID, IPARAM_TYPE, IPARAM_DEFAULT_VALUE, IPARAM_DESC, IPARAM_ORDER) values (?,?,?,?,?,?) ";
                String sql = "update IEAI_SCRIPT_TEST set  ISCRIPTNAME=? ,ISCRIPTTYPE=?, ICONTENT=?,IINPUTDESC=?,IFUNCDESC=?,IDBTYPE=?,ISERVICETYPE=?,IISEXAM=?,MANUALSTART=?,ISQLEXECMODEL=?, ISTATUS=?,ISERVICEID=? ,IUPDATEUSERID=? where  IID=? ";
                retMap = manage.saveDbaasScriptEdit(sql, parasql, conn, repParams, map, relationsql);
            }
            manage.dbaasScriptOnline(retMap, conn, map);
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    /**
     * 
     * <p>Title: dbaasScriptsOnline</p>   
     * <p>Description: 批量上线</p>   
     * @param map
     * @return   
     * @see com.ideal.dubbo.interfaces.dbaas.IDbaasScriptCreateService#dbaasScriptOnline(java.util.Map)   
     * @author: Administrator 
     * @date:   2020年3月16日 下午3:24:50
     */
    public Map<String, Object> dbaasScriptsOnline ( Map<String, Object> params )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);

            List<Map<String, String>> mapPara = manage.getScriptsByIds(conn, params);
            StringBuffer successNames = new StringBuffer();
            StringBuffer falseNames = new StringBuffer();
            // StringBuffer onlineNames = new StringBuffer();
            int falseFlag = 0;
            for (Map<String, String> para : mapPara)
            {
                String scriptName = para.get("scriptName");
                if (!"1".equals(para.get("scriptstatus")))
                {
                    // 查询原来状态是草稿还是上线，上线，此处调用save，草稿直接调用下面方法
                    // if(manage.isOnline(conn, map)) {
                    // retMap.put(SUCCESS, true);
                    // retMap.put(MESSAGE, "该脚本已经上线，不能在重复上线");
                    // }
                    // Map resultMap = manage.scriptStatus(conn, para);

                    if (!Boolean.parseBoolean(String.valueOf(params.get("flag"))))
                    {
                        retMap = this.saveDbaasScriptCreate(para);
                    } else
                    {
                        List<ScriptParameter> repParams = (List<ScriptParameter>) params.get("params");
                        String relationsql = " update IEAI_SCRIPT_LABLEANDCATE  set ICATAVALUE=?, ICATARELATIONID=?, ILABLEVALUE=?, ILABLERELATIONID=? where ISERVICEID=? ";
                        String parasql = " insert into IEAI_SCRIPT_PARAMETER (IID, ISCRIPTID, IPARAM_TYPE, IPARAM_DEFAULT_VALUE, IPARAM_DESC, IPARAM_ORDER) values (?,?,?,?,?,?) ";
                        String sql = "update IEAI_SCRIPT_TEST set  ISCRIPTNAME=? ,ISCRIPTTYPE=?, ICONTENT=?,IINPUTDESC=?,IFUNCDESC=?,IDBTYPE=?,ISERVICETYPE=?,IISEXAM=?,MANUALSTART=?,ISQLEXECMODEL=?, ISTATUS=? , IUPDATEUSERID=? where  IID=? ";
                        retMap = manage.saveDbaasScriptEdit(sql, parasql, conn, repParams, para, relationsql);
                    }
                    manage.dbaasScriptOnline(retMap, conn, para);
                    if (!(Boolean) retMap.get(SUCCESS))
                    {
                        falseFlag++;
                        falseNames.append(scriptName + ",");
                    } else
                    {
                        successNames.append(scriptName + ",");
                    }
                }

            }
            if (falseNames.length() > 0 && successNames.length() > 0)
            {
                String falseNames1 = falseNames.toString().substring(0, falseNames.length() - 1);
                String successNames1 = successNames.toString().substring(0, successNames.length() - 1);
                retMap.put(MESSAGE, falseNames1 + "脚本上线失败;" + "successNames1" + "脚本上线成功！");
            }
            if (falseNames.length() > 0 && successNames.length() == 0)
            {
                retMap.put(MESSAGE, "脚本上线失败！");
            }
            if (falseNames.length() == 0 && successNames.length() > 0)
            {
                retMap.put(MESSAGE, "脚本上线成功！");
            }

        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    private ScriptParameter _convertToScriptParameter ( Map<String, Object> rgMap )
    {
        ScriptParameter res = new ScriptParameter();
        Object idd = rgMap.get("iid");
        if (idd == JSONObject.NULL || "".equals(String.valueOf(idd)))
        {
            res.setIid(-1);
        } else
        {
            res.setIid(Long.parseLong(String.valueOf(rgMap.get("iid"))));
        }

        Object paramType = rgMap.get("paramType");
        if (null != paramType)
        {
            res.setParamType(((String) rgMap.get("paramType")).trim());
        }
        Object paramDefaultValue = rgMap.get("paramDefaultValue");
        if (null != paramDefaultValue)
        {
            res.setParamDefaultValue(((String) rgMap.get("paramDefaultValue")).trim());
        }
        Object paramDesc = rgMap.get("paramDesc");
        if (null != paramDesc)
        {
            res.setParamDesc(((String) rgMap.get("paramDesc")).trim());
        }
        Object paramOrder = rgMap.get("paramOrder");
        if (null != paramOrder)
        {
            res.setParamOrder((Integer) rgMap.get("paramOrder"));
        }
        return res;
    }

    @Override
    public Map<String, Object> queryDbaasScriptMySelf ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        String searchStr = "";
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            // 查询原来状态是草稿还是上线，上线，此处调用save，草稿直接调用下面方法
            ScriptService ss = new ScriptService();
            ss.setUserId(map.get("userId"));
            ss.setStart(Integer.parseInt(map.get("start")));
            ss.setLimit(Integer.parseInt(map.get("limit")));
            ss.setScriptName(map.get("param"));
            ss.setStatus("".equals(map.get("scriptStatus")) || "null".equals(map.get("scriptStatus"))
                    || null == map.get("scriptStatus") ? 0 : Integer.parseInt(map.get("scriptStatus")));
            ss.setServiceType("".equals(map.get("serviceType")) || "null".equals(map.get("serviceType"))
                    || null == map.get("serviceType") ? -1 : Integer.parseInt(map.get("serviceType")));
            String param = map.get("param");
            Integer start = ss.getStart();
            Integer limit = ss.getLimit();
            if (param != null && !"".equals(param))
            {
                searchStr = searchStr + " and( UPPER(ISCRIPTNAME) like '%" + param.toUpperCase()
                        + "%' or  UPPER(ILABLEVALUE) like '%" + param.toUpperCase() + "%' or  UPPER(ICATAVALUE) like '%"
                        + param.toUpperCase() + "%')";
            }
            // if (ss.getScriptName() != null && !"".equals(ss.getScriptName()))
            // {
            // searchStr = searchStr + " and UPPER(ISCRIPTNAME) like '%" +
            // ss.getScriptName().toUpperCase() + "%' ";
            // }
            if (ss.getServiceName() != null && !"".equals(ss.getServiceName()))
            {
                searchStr = searchStr + " and UPPER(ISERVICESNAME) like '%" + ss.getServiceName().toUpperCase() + "%' ";
            }

            if (ss.getServiceId() != null && !"".equals(ss.getServiceId()))
            {
                searchStr = searchStr + " and  UPPER(iserviceid) like '%" + ss.getServiceId().toUpperCase() + "%' ";
            }
            if (-1 != ss.getServiceType())
            {
                searchStr += " and ISERVICETYPE =" + ss.getServiceType();
            }
            // 白名单管理 使用的查询条件 命令
            if (ss.getContent() != null && !"".equals(ss.getContent()))
            {
                switch (JudgeDB.IEAI_DB_TYPE)
        {
            case 1:
            case 4:// oracle
            case 5:// kingbase
                        searchStr = searchStr + " and  to_char(ICONTENT) like '%" + ss.getContent() + "%'";
                        break;
                    case 2:// db2
                        searchStr = searchStr + " and  ICONTENT like '%" + ss.getContent() + "%' ";
                        break;
                    case 3:// mysql
                        searchStr = searchStr + " and  ICONTENT like '%" + ss.getContent() + "%' ";
                        break;
                }
            }

            if (ss.getScriptType() != null && !"".equals(ss.getScriptType()) && !"-2".equals(ss.getScriptType()))
            {
                searchStr = searchStr + " and ISCRIPTTYPE ='" + ss.getScriptType() + "' ";
            }

            if (ss.getStatus() != -10000 && ss.getStatus() != 0)
            {
                if (ss.getStatus() == 3)
                {
                    searchStr = searchStr + " and IISSHARE =1 ";
                } else
                {
                    searchStr = searchStr + " and ISTATUS =" + ss.getStatus();
                }
            }
            if (map.get("dbType") != null && !"".equals(map.get("dbType")))
            {
                searchStr = searchStr + " and  IDBTYPE=(select IID from IEAI_DATABASE_TYPE where IDBTYPE='"
                        + map.get("dbType") + "') ";
            }

            String sql = "select  *  from ( SELECT A.*,(select IFULLNAME from ieai_user where IID=A.IUSERID) as ICREATEUSERNAME,"
                    + "(CASE  WHEN  u.IFULLNAME  IS NULL  THEN (select IFULLNAME from ieai_user where IID=A.IUSERID) ELSE u.IFULLNAME END) AS IUPDATEUSERNAME"
                    + " FROM (select t.*,L.ILABLERELATIONID,L.ICATARELATIONID,L.ILABLEVALUE,L.ICATAVALUE from IEAI_SCRIPT_TEST T ,IEAI_SCRIPT_LABLEANDCATE L WHERE t.iid=t.ilastid and t.ISDELETE=0  and T.IID=L.ISERVICEID) A   LEFT JOIN IEAI_USER u on u.IID=A.IUPDATEUSERID ORDER BY A.IID DESC,A.ISTATUS) t where  t.ISAUTOSUB=3 "
                    + searchStr;
            String sqlCount = "select count(*) as total from (" + sql + ") m ";
            DbPageSize dps = new DbPageSize();
            sql = dps.sqlPage(sql, start, start + limit);
            retMap = manage.queryDbaasScriptMySelf(conn, sql, sqlCount);
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    public Map<String, Object> deleteDbaasScriptForTest ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            // 查询原来状态是草稿还是上线，上线，此处调用save，草稿直接调用下面方法
            List list = JsonUtil.getDTOList(map.get("jsonData"), Map.class);
            List listUuid = JsonUtil.getDTOList(map.get("jsonUuidData"), Map.class);
            List<String> ids = new ArrayList<String>();
            List<String> uuids = new ArrayList<String>();
            for (int i = 0; i < list.size(); i++)
            {
                Map ss = (Map) list.get(i);
                ids.add(String.valueOf(ss.get("iid")));
            }
            for (int i = 0; i < listUuid.size(); i++)
            {
                Map ss = (Map) listUuid.get(i);
                uuids.add("'" + ss.get("uuid") + "'");
            }
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            String sql = " delete from IEAI_SCRIPT_TEST where IID in (" + StringUtils.join(ids, ",") + ")";
            String sql1 = " delete from IEAI_SCRIPT_PARAMETER where ISCRIPTID in (" + StringUtils.join(uuids, ",")
                    + ")";
            String sql2 = " delete from IEAI_SCRIPT_LABLEANDCATE where ISERVICEID IN (" + StringUtils.join(ids, ",")
                    + ")";
            String sql3 = " delete from IEAI_SCRIPT_Services where IID IN (" + StringUtils.join(ids, ",") + ")";
            retMap = manage.deleteDbaasScriptForTest(conn, sql, sql3, sql1, sql2, ids);
            conn.commit();
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    public Map<String, Object> getScriptVersionListForDbaas ( String serviceId )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            res = manage.getScriptVersionListForDbaas(conn, serviceId);
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return res;
    }

    public Map<String, Object> isDbaasScriptOnline ( String serviceId )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        try
        {
            Map<String, String> map = new HashMap<String, String>();
            map.put("iid", serviceId);
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI_BASIC);
            res.put("success", manage.isOnline(conn, map));
        } catch (Exception e)
        {
            res.put("success", false);
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return res;
    }

    @Override
    public Map<String, Object> queryDbaasScriptOnline ( Map<String, String> map )
    {
        Map<String, Object> retMap = new HashMap<String, Object>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        Connection conn = null;
        String searchStr = "";
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            // 查询原来状态是草稿还是上线，上线，此处调用save，草稿直接调用下面方法
            ScriptService ss = new ScriptService();
            ss.setUserId(map.get("userId"));
            ss.setStart(Integer.parseInt(map.get("start")));
            ss.setLimit(Integer.parseInt(map.get("limit")));
            ss.setScriptName(map.get("param"));
            ss.setStatus("".equals(map.get("scriptStatus")) || "null".equals(map.get("scriptStatus"))
                    || null == map.get("scriptStatus") ? 0 : Integer.parseInt(map.get("scriptStatus")));
            ss.setServiceType("".equals(map.get("serviceType")) || "null".equals(map.get("serviceType"))
                    || null == map.get("serviceType") ? -1 : Integer.parseInt(map.get("serviceType")));
            String param = map.get("param");
            Integer start = ss.getStart();
            Integer limit = ss.getLimit();
            if (param != null && !"".equals(param))
            {
                searchStr = searchStr + " and( UPPER(ISCRIPTNAME) like '%" + param.toUpperCase()
                        + "%' or  UPPER(ILABLEVALUE) like '%" + param.toUpperCase() + "%' or  UPPER(ICATAVALUE) like '%"
                        + param.toUpperCase() + "%')";
            }
            // if (ss.getScriptName() != null && !"".equals(ss.getScriptName()))
            // {
            // searchStr = searchStr + " and UPPER(ISCRIPTNAME) like '%" +
            // ss.getScriptName().toUpperCase() + "%' ";
            // }
            if (ss.getServiceName() != null && !"".equals(ss.getServiceName()))
            {
                searchStr = searchStr + " and UPPER(ISERVICESNAME) like '%" + ss.getServiceName().toUpperCase() + "%' ";
            }

            if (ss.getServiceId() != null && !"".equals(ss.getServiceId()))
            {
                searchStr = searchStr + " and  UPPER(iserviceid) like '%" + ss.getServiceId().toUpperCase() + "%' ";
            }
            if (-1 != ss.getServiceType())
            {
                searchStr += " and ISERVICETYPE =" + ss.getServiceType();
            }
            // 白名单管理 使用的查询条件 命令
            if (ss.getContent() != null && !"".equals(ss.getContent()))
            {
                switch (JudgeDB.IEAI_DB_TYPE)
        {
            case 1:
            case 4:// oracle
            case 5:// kingbase
                        searchStr = searchStr + " and  to_char(ICONTENT) like '%" + ss.getContent() + "%'";
                        break;
                    case 2:// db2
                        searchStr = searchStr + " and  ICONTENT like '%" + ss.getContent() + "%' ";
                        break;
                    case 3:// mysql
                        searchStr = searchStr + " and  ICONTENT like '%" + ss.getContent() + "%' ";
                        break;
                }
            }

            if (ss.getScriptType() != null && !"".equals(ss.getScriptType()) && !"-2".equals(ss.getScriptType()))
            {
                searchStr = searchStr + " and ISCRIPTTYPE ='" + ss.getScriptType() + "' ";
            }

            if (ss.getStatus() != -10000 && ss.getStatus() != 0)
            {
                if (ss.getStatus() == 3)
                {
                    searchStr = searchStr + " and IISSHARE =1 ";
                } else
                {
                    searchStr = searchStr + " and ISTATUS =" + ss.getStatus();
                }
            }
            if (map.get("dbType") != null && !"".equals(map.get("dbType")))
            {
                searchStr = searchStr + " and  IDBTYPE=(select IID from IEAI_DATABASE_TYPE where IDBTYPE='"
                        + map.get("dbType") + "') ";
            }

            String sql = "SELECT A.*  ,(select IFULLNAME from ieai_user where IID=A.IUSERID) as ICREATEUSERNAME from (select t.*,L.ILABLERELATIONID,L.ICATARELATIONID,L.ILABLEVALUE,L.ICATAVALUE from IEAI_SCRIPT_SERVICES T ,IEAI_SCRIPT_LABLEANDCATE L WHERE t.iid=t.ilastid and t.ISDELETE=0 and T.IID=L.ISERVICEID) A   WHERE A.ISAUTOSUB=3  "
                    + searchStr;
            String sqlCount = "select count(*) as total from (" + sql + ") m ";
            DbPageSize dps = new DbPageSize();
            dps.sqlPage(sql, 0, start + limit);
            retMap = manage.queryDbaasScriptOnline(conn, sql, sqlCount);
        } catch (Exception e)
        {
            logger.error(method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, logger);
        }
        return retMap;
    }

    @Override
    public Map hasVersionRollBackForDbaas ( String iid, String oldId, String oldUuid, String uuid )
    {
        Connection conn = null;
        Map retMap = new HashMap();
        DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
        String sql = " delete from IEAI_SCRIPT_TEST where IID in (" + oldId + ")";
        String sql1 = " delete from IEAI_SCRIPT_PARAMETER where ISCRIPTID in ('" + oldUuid + "')";
        String sql2 = " delete from IEAI_SCRIPT_LABLEANDCATE where ISERVICEID IN (" + oldId + ")";
        String sql3 = " delete from IEAI_SCRIPT_Services where IID IN (" + oldId + ")";
        List<String> ids = new ArrayList<String>();
        ids.add(String.valueOf(oldId));
        try
        {
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            manage.deleteDbaasScriptForTest(conn, sql, sql3, sql1, sql2, ids);
            conn.commit();
        } catch (Exception e)
        {
            logger.error(e.getMessage(), e);

        } finally
        {
            DBResource.closeConnection(conn, "hasVersionRollBackForDbaas", logger);
        }
        return ScriptServiceManage.getInstance().versionRollBack(iid, uuid ,"");

    }

    public Map<String, Object> importFailOperScriptManage ( String filename, String userName, byte[] ins, long userId )
    {
        String thread = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> res = new HashMap<String, Object>();
        Connection conn = null;
        Workbook workbook = null;
        InputStream fis = null;
        try
        {
            fis = new ByteArrayInputStream(ins);
            if (filename.toLowerCase().endsWith("xlsx") || filename.toLowerCase().endsWith("xlsm"))
            {
                workbook = new XSSFWorkbook(fis);
            } else if (filename.toLowerCase().endsWith("xls"))
            {
                workbook = new HSSFWorkbook(fis);
            }
            Map<String, Map<String, String>> map = new HashMap<String, Map<String, String>>();
            Map<String, List<Map<String, String>>> mappara = new HashMap<String, List<Map<String, String>>>();
            Map<String, List<Map<String, String>>> maplable = new HashMap<String, List<Map<String, String>>>();
            Map<String, List<String>> mapRelation = new HashMap<String, List<String>>();
            Sheet sheet = workbook.getSheetAt(0);
            Row row = sheet.getRow(0);
            int colum = row.getLastCellNum();
            Map<String, String> data = null;
            List<Map<String, String>> valueList = null;
            DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            for (int i = 1; i <= sheet.getLastRowNum(); i++)
            {
                valueList = new ArrayList<Map<String, String>>();
                data = new HashMap<String, String>();
                row = sheet.getRow(i);
                String IID = "";
                for (int j = 0; j < colum; j++)
                {
                    if (j == 0)
                    {
                        IID = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IID", IID);
                        if ("".equals(IID))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，脚本ID不能为空!");
                        }
                        continue;
                    }
                    if (j == 1)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISCRIPTNAME", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ISCRIPTNAME不能为空!");
                        }
                        continue;
                    }
                    if (j == 2)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        String busname = manage.getBussInfo(conn, value, 1);
                        if (null == value || "".equals(value) || "".equals(busname))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ISYSNAME在本库中不存在!");
                        }
                        data.put("ISYSNAME", value);
                        continue;
                    }
                    if (j == 3)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        String busname = manage.getBussInfo(conn, value, 1);
                        if (null == value || "".equals(value) || "".equals(busname))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，IBUSSNAME在本库中不存在!");
                        }
                        data.put("IBUSSNAME", value);
                        continue;
                    }
                    if (j == 4)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISCRIPTTYPE", value);
                        continue;
                    }
                    if (j == 5)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISERVICESNAME", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ISERVICESNAME不能为空!");
                        }
                        continue;
                    }
                    if (j == 6)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISCRIPTPARA", value);
                        continue;
                    }
                    if (j == 7)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，IUSERID不能为空!");
                        }
                        String name = manage.getUserInfo(conn, value, 1);
                        if (null == value || "".equals(value) || "".equals(name))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，IUSERID:" + value + " 在本库中不存在!");
                        }
                        data.put("IUSERID", name);
                        continue;
                    }
                    if (j == 8)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISTATUS", value);
                        continue;
                    }
                    if (j == 9)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ICONTENT", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ICONTENT不能为空!");
                        }
                        continue;
                    }
                    if (j == 10)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        String busname = manage.getBussInfo(conn, value, 0);
                        if ("".equals(busname) || (!"".equals(busname) && !busname.equals(data.get("ISYSNAME"))))
                        {
                            String busTypename1 = manage.getBussInfo(conn, data.get("ISYSNAME"), 1);
                            value = busTypename1;
                            // throw new IOException("脚本信息表Sheet，第" + i +
                            // "行数据，IBUSSID在本库中存在的信息与表格中不符合!");
                        }
                        data.put("IBUSSID", value);
                        continue;
                    }
                    if (j == 11)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        String busTypename = manage.getBussTypeInfo(conn, value, 0);
                        if ("".equals(busTypename)
                                || (!"".equals(busTypename) && !busTypename.equals(data.get("IBUSSNAME"))))
                        {
                            String busTypename1 = manage.getBussTypeInfo(conn, data.get("IBUSSNAME"), 1);
                            value = busTypename1;
                            // throw new IOException("脚本信息表Sheet，第" + i +
                            // "行数据，IBUSSTYPEID在本库中存在的信息与表格中不符合!");
                        }
                        data.put("IBUSSTYPEID", value);//
                        continue;
                    }
                    if (j == 12)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IINPUTDESC", value);
                        continue;
                    }
                    if (j == 13)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IFUNCDESC", value);
                        continue;
                    }
                    if (j == 14)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IUPERID", value);
                        continue;
                    }
                    if (j == 15)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ILASTID", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ILASTID不能为空!");
                        }
                        continue;
                    }
                    if (j == 16)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISCRIPTUUID", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ISCRIPTUUID不能为空!");
                        }
                        continue;
                    }
                    if (j == 17)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IEXECUSER", value);
                        continue;
                    }
                    if (j == 18)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IDBTYPE", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，IDBTYPE不能为空!");
                        }
                        continue;
                    }
                    if (j == 19)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISERVICETYPE", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ISERVICETYPE不能为空!");
                        }
                        continue;
                    }
                    if (j == 20)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IISEXAM", value);
                        continue;
                    }
                    if (j == 21)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("IPROJECTFLAG", value);
                        continue;
                    }
                    if (j == 22)
                    {
                        data.put("ISAUTOSUB", "3");
                        continue;
                    }
                    if (j == 23)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("MANUALSTART", value);
                        continue;
                    }
                    if (j == 24)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISQLEXECMODEL", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本信息表Sheet，第" + i + "行数据，ISQLEXECMODEL不能为空!");
                        }
                        continue;
                    }
                    if (j == 25)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ISERVICEID", value);
                        continue;
                    }
                    if (j == 26)
                    {
                        String value = null == row.getCell(j) ? "" : row.getCell(j).getStringCellValue();
                        data.put("ITIMEOUT", value);
                        continue;
                    }
                }
                map.put(IID, data);
            }

            Sheet sheet1 = workbook.getSheetAt(1);
            Row row1 = sheet1.getRow(0);
            for (int i = 1; i <= sheet1.getLastRowNum(); i++)
            {
                int colum1 = row1.getLastCellNum();
                row1 = sheet1.getRow(i);
                String IID = "";
                valueList = new ArrayList<Map<String, String>>();
                data = new HashMap<String, String>();
                for (int j = 0; j < colum1; j++)
                {
                    if (j == 0)
                    {
                        IID = null == row1.getCell(j) ? "" : row1.getCell(j).getStringCellValue();
                        if ("".equals(IID))
                        {
                            throw new IOException("脚本参数信息表Sheet,第" + i + "行数据，脚本UUID不能为空!");
                        }
                        continue;
                    }
                    if (j == 1)
                    {
                        String value = null == row1.getCell(j) ? "" : row1.getCell(j).getStringCellValue();
                        data.put("IPARAM_TYPE", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本参数信息表Sheet,第" + i + "行数据，IPARAM_TYPE不能为空!");
                        }
                        continue;
                    }
                    if (j == 2)
                    {
                        String value = null == row1.getCell(j) ? "" : row1.getCell(j).getStringCellValue();
                        data.put("IPARAM_DEFAULT_VALUE", value);
                        continue;
                    }
                    if (j == 3)
                    {
                        String value = null == row1.getCell(j) ? "" : row1.getCell(j).getStringCellValue();
                        data.put("IPARAM_DESC", value);
                        continue;
                    }
                    if (j == 4)
                    {
                        String value = null == row1.getCell(j) ? "" : row1.getCell(j).getStringCellValue();
                        data.put("IPARAM_ORDER", value);
                        if ("".equals(value))
                        {
                            throw new IOException("脚本参数信息表Sheet,第" + i + "行数据，IPARAM_ORDER不能为空!");
                        }
                        continue;
                    }
                }
                if (mappara.containsKey(IID))
                {
                    valueList = mappara.get(IID);
                    valueList.add(data);
                } else
                {
                    valueList.add(data);
                }
                mappara.put(IID, valueList);
            }
            Sheet sheet2 = workbook.getSheetAt(2);
            Row row2 = sheet2.getRow(0);
            for (int i = 1; i <= sheet2.getLastRowNum(); i++)
            {
                data = new HashMap<String, String>();
                valueList = new ArrayList<Map<String, String>>();
                int colum1 = row2.getLastCellNum();
                row2 = sheet2.getRow(i);
                String IID = "";
                data = new HashMap<String, String>();
                for (int j = 0; j < colum1; j++)
                {
                    if (j == 0)
                    {
                        String value = null == row2.getCell(j) ? "" : row2.getCell(j).getStringCellValue();
                        data.put("ICATAVALUE", value);
                        continue;
                    }
                    if (j == 1)
                    {
                        String value = null == row2.getCell(j) ? "" : row2.getCell(j).getStringCellValue();
                        data.put("ICATARELATIONID", value);
                        continue;
                    }
                    if (j == 2)
                    {
                        String value = null == row2.getCell(j) ? "" : row2.getCell(j).getStringCellValue();
                        data.put("ILABLEVALUE", value);
                        continue;
                    }
                    if (j == 3)
                    {
                        String value = null == row2.getCell(j) ? "" : row2.getCell(j).getStringCellValue();
                        data.put("ILABLERELATIONID", value);
                        continue;
                    }
                    if (j == 4)
                    {
                        IID = null == row2.getCell(j) ? "" : row2.getCell(j).getStringCellValue();
                        if ("".equals(IID))
                        {
                            throw new IOException("标签信息表Sheet,第" + i + "行数据，脚本ID不能为空!");
                        }
                        continue;
                    }
                }
                if (maplable.containsKey(IID))
                {
                    valueList = maplable.get(IID);
                    valueList.add(data);
                } else
                {
                    valueList.add(data);
                }
                maplable.put(IID, valueList);
            }
            Sheet sheet3 = workbook.getSheetAt(3);
            Row row3 = sheet3.getRow(0);
            List<String> valueList1 = null;
            for (int i = 1; i <= sheet3.getLastRowNum(); i++)
            {
                valueList1 = new ArrayList<String>();
                int colum1 = row3.getLastCellNum();
                row3 = sheet3.getRow(i);
                String IID = "";
                String value = "";
                for (int j = 0; j < colum1; j++)
                {
                    if (j == 0)
                    {
                        value = null == row3.getCell(j) ? "" : row3.getCell(j).getStringCellValue();
                        if ("".equals(value))
                        {
                            throw new IOException("标签关系信息表Sheet,第" + i + "行数据，ILABLEID不能为空!");
                        }
                        continue;
                    }
                    if (j == 1)
                    {
                        IID = null == row3.getCell(j) ? "" : row3.getCell(j).getStringCellValue();
                        if ("".equals(IID))
                        {
                            throw new IOException("标签关系信息表Sheet,第" + i + "行数据，脚本ID不能为空!");
                        }
                        continue;
                    }
                }
                if (mapRelation.containsKey(IID))
                {
                    valueList1 = mapRelation.get(IID);
                    valueList1.add(value);
                } else
                {
                    valueList1.add(value);
                }
                mapRelation.put(IID, valueList1);
            }

            String sql = "INSERT INTO IEAI_SCRIPT_TEST ( IID, ISCRIPTNAME, ISYSNAME,IBUSSNAME,ISCRIPTTYPE, ISERVICESNAME, ISCRIPTPARA,ICREATETIME, IUSERID, ISTATUS,ICONTENT,IBUSSID,IBUSSTYPEID,IINPUTDESC,IFUNCDESC,IUPERID,ILASTID,ISCRIPTUUID,IEXECUSER,IDBTYPE,ISERVICETYPE,IISEXAM,IPROJECTFLAG,ISAUTOSUB,MANUALSTART,ISQLEXECMODEL,ISERVICEID,ITIMEOUT,IVERSION) values(?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
            String lablesql = " insert into IEAI_SCRIPT_LABLEANDCATE (IID, ICATAVALUE, ICATARELATIONID, ILABLEVALUE,ILABLERELATIONID, ISERVICEID) values (?,?,?,?,?,?) ";
            String parasql = " insert into IEAI_SCRIPT_PARAMETER (IID, ISCRIPTID, IPARAM_TYPE, IPARAM_DEFAULT_VALUE, IPARAM_DESC, IPARAM_ORDER) values (?,?,?,?,?,?) ";
            String relationsql = " insert into IEAI_SCRIPT_LABLE_RELATION (IID, ILABLEID,ISERVICEID) values (?,?,?) ";
            res = manage.importFailOperScriptManage(conn, sql, parasql, lablesql, relationsql, map, mappara, maplable,
                mapRelation);
            conn.commit();
            res.put("success", true);
            res.put("message", "数据导入成功");
        } catch (Exception e)
        {
            res.put("success", false);
            res.put("message", "数据导入失败" + e.getMessage());
            logger.error(e.getMessage(), e);
        } finally
        {
            if (null != fis)
            {
                try
                {
                    fis.close();
                } catch (IOException e)
                {
                    e.printStackTrace();
                }
            }
            DBResource.closeConnection(conn, thread, logger);
        }
        return res;
    }

    public Map<String, Object> exportFailOperScriptManage ( String ids, String flag, long userId )
    {
        String thread = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            DbaasScriptCreateManager manage = new DbaasScriptCreateManager();
            String lablesql = " SELECT IID, ICATAVALUE, ICATARELATIONID, ILABLEVALUE,ILABLERELATIONID, ISERVICEID FROM  IEAI_SCRIPT_LABLEANDCATE WHERE ISERVICEID=?";
            String relationsql = "SELECT IID,ILABLEID,ISERVICEID from IEAI_SCRIPT_LABLE_RELATION WHERE ISERVICEID=?";
            String parasql = " SELECT IID, ISCRIPTID, IPARAM_TYPE, IPARAM_DEFAULT_VALUE, IPARAM_DESC, IPARAM_ORDER FROM IEAI_SCRIPT_PARAMETER WHERE ISCRIPTID=(select t.iscriptuuid from ieai_script_test t where t.iid=?)";
            String sql = " SELECT IID, ISCRIPTNAME, ISYSNAME,IBUSSNAME,ISCRIPTTYPE, ISERVICESNAME, ISCRIPTPARA,ICREATETIME, IUSERID, ISTATUS,ICONTENT,IBUSSID,IBUSSTYPEID,IINPUTDESC,IFUNCDESC,IUPERID,ILASTID,ISCRIPTUUID,IEXECUSER,IDBTYPE,ISERVICETYPE,IISEXAM,IPROJECTFLAG,ISAUTOSUB,MANUALSTART,ISQLEXECMODEL,ISERVICEID,ITIMEOUT FROM IEAI_SCRIPT_TEST WHERE IID=ILASTID AND ISAUTOSUB=3 ";
            conn = DBManager.getInstance().getSchedulerConnection(Constants.IEAI_IEAI_BASIC);
            if (!"1".equals(flag))
            {
                sql = sql + " AND IID IN (" + ids + ")";
            }
            map = manage.exportFailOperScriptManage(conn, sql, parasql, lablesql, relationsql);
            map.put("success", true);
            map.put("message", "数据导出成功");
        } catch (Exception e)
        {
            logger.error(e.getMessage(), e);
            map.put("success", false);
            map.put("message", "数据导出失败" + e.getMessage() == null ? "" : e.getMessage());
        } finally
        {
            DBResource.closeConnection(conn, thread, logger);
        }
        return map;
    }
}
