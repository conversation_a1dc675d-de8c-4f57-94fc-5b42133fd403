Ext.onReady(function () {
    destroyRubbish();
    /***********************项目概况tab ***********************************************/
    var ifileName = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        readOnly: true,
        name: 'ifilename',
        value: ifilename,
        fieldLabel: '文件名称'
    });

    var proState = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proState',
        readOnly: true,
        hidden:type==0||type==3?false:true,
        value: ieai_group_env,
        fieldLabel: '环境'
    });

    var proStatus = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proStatus',
        readOnly: true,
        hidden:type==1||type==2?false:true,
        value: iconfigstatus,
        fieldLabel: '状态'
    });

    var proIp = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proIp',
        value: iip,
        readOnly: true,
        fieldLabel: 'IP地址'
    });

    var proType = Ext.create('Ext.form.field.Text',  {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        editable: false,
        name: 'proType',
        value: imodeltype,
        readOnly: true,
        fieldLabel: '模块类型'
    });

    var departMent = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'departMent',
        value: ipath,
        readOnly: true,
        fieldLabel: '分发路径'
    });

    var project = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'project',
        value: iproject,
        hidden:type==2?false:true,
        readOnly: true,
        fieldLabel: '项目编号'
    });

    var info = "";
    if(Ext.isEmpty(type)){
        info = '<header id="rightVersion" style="font-size:15px;margin-left:37px;" >采集版本配置内容：</header><div id="ci_rightDiv" class="layout_cn" style="background: #000000;color: white; margin-left:30px;white-space: pre-wrap;">'+base64Decode(icontenttext)+'</div>';
    }else{
        info = '<header id="rightVersion" style="font-size:15px;margin-left:37px;" >配置内容：</header><div id="ci_rightDiv" class="layout_cn" style="background: #000000;color: white; margin-left:30px;white-space: pre-wrap;">'+base64Decode(icontenttext)+'</div>';
    }

    var productAsk = Ext.create('Ext.panel.Panel', {
        width: '90%',
        labelWidth: 85,
        height: 450,
        labelAlign: 'right',
        margin: '0 0 25 0',
        readOnly: true,
        //hidden:true,
        name: 'productAsk',
        //value: base64Decode(icontenttext),
        // fieldLabel: '配置内容',
        id:'productAsk',
        // html:'<header id="rightVersion" style="font-size:15px;margin-left:27px;" >配置内容：</header><div class="layout_cn" style="background: #000000;color: white; margin-left:30px;">\n' +
        //     '                        <div id="rightDiv" style="white-space: pre;font-family: math;" class="cicd_vc_cn">'+utf8ToBase64(icontenttext)+'</div>\n' +
        //     '                    </div>'
        html:info

    });


    var ifileName1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        readOnly: true,
        name: 'ifilename1',
        value: ifilename1,
        fieldLabel: '文件名称'
    });

    var proState1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proState1',
        readOnly: true,
        hidden:type==0||type==3?false:true,
        value: ieai_group_env1,
        fieldLabel: '环境'
    });

    var proStatus1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proStatus1',
        readOnly: true,
        hidden:type==1||type==2?false:true,
        value: iconfigstatus1,
        fieldLabel: '状态'
    });

    var proIp1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'proIp1',
        value: iip1,
        readOnly: true,
        fieldLabel: 'IP地址'
    });

    var proType1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        editable: false,
        name: 'proType1',
        value: imodeltype1,
        readOnly: true,
        fieldLabel: '模块类型'
    });

    var departMent1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'departMent1',
        value: ipath1,
        readOnly: true,
        fieldLabel: '分发路径'
    });

    var project1 = Ext.create('Ext.form.field.Text', {
        width: '45%',
        labelWidth: 85,
        labelAlign: 'right',
        margin: '0 0 25 0',
        name: 'project1',
        value: iproject1,
        hidden:type==2?false:true,
        readOnly: true,
        fieldLabel: '项目编号'
    });

    // var productAsk1 = Ext.create('Ext.form.TextArea', {
    //     width: '90%',
    //     labelWidth: 85,
    //     height: 450,
    //     //hidden:true,
    //     labelAlign: 'right',
    //     margin: '0 0 25 0',
    //     readOnly: true,
    //     name: 'productAsk1',
    //     value: utf8ToBase64(icontenttext1),
    //     fieldLabel: '配置内容',
    //     id:'productAsk1'
    // });


    // var productAsk1 = Ext.create('Ext.form.TextArea', {
    //    // width: '90%',
    //     labelWidth: 85,
    //   //  height: 450,
    //     labelAlign: 'right',
    //     margin: '0 0 25 0',
    //     readOnly: true,
    //     name: 'productAsk1',
    //     // value: utf8ToBase64(icontenttext1),
    //     fieldLabel: '配置内容',
    //     id:'productAsk1',
    //     html:'<div style="background: red;color: white;width:80px;height: 200px;"><span>哈哈哈</span></div>'
    //
    // });
    var info1 = "";
    if(Ext.isEmpty(type)){
        info1 = '<header id="leftVersion" style="font-size:15px;margin-left:37px;" >当前版本配置内容:</header><div id="ci_leftDiv" class="layout_cn" style="background: #000000;color: white;margin-left:25px;white-space: pre-wrap;">'+base64Decode(icontenttext1)+'</div>';
    }else{
        info1 = '<header id="leftVersion" style="font-size:15px;margin-left:37px;" >配置内容：</header><div id="ci_leftDiv" class="layout_cn" style="background: #000000;color: white; margin-left:30px;white-space: pre-wrap;">'+base64Decode(icontenttext1)+'</div>';
    }

    var productAsk1 = Ext.create('Ext.panel.Panel', {
        width: '90%',
        labelWidth: 85,
        height: 450,
        labelAlign: 'right',
        margin: '0 0 25 0',
        readOnly: true,
        name: 'productAsk1',
        // value: utf8ToBase64(icontenttext1),
        fieldLabel: '配置内容',
        id:'productAsk1',
        // html:'<header id="leftVersion" style="font-size:15px;margin-left:37px;" >配置内容:</header><div class="layout_cn" style="background: #000000;color: white;margin-left:25px;">\n' +
        //     '                        <div id="leftDiv" style="white-space: pre;font-family: math;" class="cicd_vc_cn">'+utf8ToBase64(icontenttext1)+'</div>\n' +
        //     '                    </div>'
        html:info1


    });

    var projectForm = Ext.create('Ext.panel.Panel', {
        name: 'projectForm',
        region: 'east',
        layout: 'anchor',
        buttonAlign: 'center',
        width: '50%',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        bodyBorder: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [ifileName,proState,proStatus]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proIp, proType]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [departMent,project]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [productAsk]
        }]
    });

    var projectForm1 = Ext.create('Ext.panel.Panel', {
        name: 'projectForm1',
        region: 'west',
        layout: 'anchor',
        buttonAlign: 'center',
        collapsible: false,//可收缩
        collapsed: false,//默认收缩
        border: false,
        width: '50%',
        bodyBorder: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [ifileName1,proState1,proStatus1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [proIp1, proType1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [departMent1,project1]
        }, {
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [productAsk1]
        }]
    });

    var proTab = Ext.create('Ext.form.FormPanel', {
        title: '配置对比',
        layout: 'border',
        border: false,
        buttonAlign: 'center',
        items: [projectForm, projectForm1],
        buttons: [{
            text: '返回',
            handler: function () {
                closeThis();
            }
        }]
    });




    function closeThis() {
        if(type==2){
            contentPanel.getLoader().load(
                {
                    url: 'accessCD_CONFIGFILE.do',
                    params:
                        {sysName: sysName,iproducetype:iproducetype,page:page},
                    scripts: true
                });
        }else if(type == 3){
            scCopyWindows.close();
        }else if(type == 4){
            //配置项  非生产采集比对当前版本
            compareCurrentWindows.close();
        }else{
            contentPanel.getLoader().load(
                {
                    url: 'accessCI_CONFIGFILE.do',
                    params:
                        {sysName: sysName,iproducetype:iproducetype},
                    scripts: true
                });
        }
    }

    /***********************其他 ***********************************************/
    var tabArray = new Array();
    tabArray.push(proTab);


    var mainTabs = Ext.widget('tabpanel', {
        cls: 'customize_panel_back',
        region: 'center',
        tabPosition: 'top',
        activeTab: 0,
        plain: true,
        defaults: {
            autoScroll: true,
            bodyPadding: 5
        },
        buttonAlign: 'center',
        items: tabArray
    });


    let renderTo = "CI_CONFIG_COM";
    if (isTabSwitch){
        renderTo += configcomnow;
    }
    var MainPanel = Ext.create('Ext.panel.Panel',
        {
            renderTo: renderTo,
            width: '100%',
            layout: 'border',
            cls: 'customize_panel_header_arrow',
            height: '100%',
            border: false,
            items: [mainTabs],
            listeners: {
                afterrender: function(panel) {
                    ci_compareVersion();
                }
            }
        });

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function (obj, options, eOpts) {
            Ext.destroy(MainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });

});
