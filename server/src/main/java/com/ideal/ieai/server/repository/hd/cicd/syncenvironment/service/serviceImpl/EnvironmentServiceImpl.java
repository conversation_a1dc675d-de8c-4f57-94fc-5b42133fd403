package com.ideal.ieai.server.repository.hd.cicd.syncenvironment.service.serviceImpl;


import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.platform.repository.computerManage.shell.Tools;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.cicd.syncenvironment.bean.EnvironmentBean;
import com.ideal.ieai.server.repository.hd.cicd.syncenvironment.manager.EnvironmentManager;
import com.ideal.ieai.server.repository.hd.cicd.syncenvironment.service.EnvironmentService;
import com.ideal.ieai.server.util.HttpUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.apache.log4j.Logger;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.util.*;

public class EnvironmentServiceImpl implements EnvironmentService {

    private static String URL = "";
    private static Logger _log = Logger.getLogger(EnvironmentServiceImpl.class);
    private static final String SUCCESS_TEXT = "success";
    private static final String MESSAGE_TEXT = "message";

    @Override
    public synchronized Map EnvironmentSynchronization(EnvironmentBean bean) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        Map res = new HashMap();
        EnvironmentManager environmentManager = new EnvironmentManager();
        try {
            String result = exec();
            List list = data(result);

            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get("baseConn");
            if (null != baseConn) {
                List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
                //int itype = Constants.IEAI_CICD;
                //String msg = "environmentSync";
                //CiCdSyncMsgManager.getInstance().insertSyncMsg(msg, itype);
                Map orgSql = environmentManager.saveData(list, baseConn, dbConns);
                if ((Boolean) orgSql.get(SUCCESS_TEXT)) {
                    if (((List<String>) orgSql.get("exeSqls")).size() == 0 || ((List<String>) orgSql.get("rollbackSqls")).size() == 0) {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "数据集合为空！！");
                        res.put("flag", false);
                        _log.error("获取数据集合为空");
                    } else {
                        if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"), (List<String>) orgSql.get("rollbackSqls"))) {
                            DBResource.closeConnection(baseConn, "environmentManager.saveData", _log);
                            res.put(SUCCESS_TEXT, true);
                            res.put(MESSAGE_TEXT, "数据保存成功！");
                            res.put("flag", true);
                        }
                    }
                }else {
                    DBResource.closeConnection(baseConn, method, _log);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, method, _log);
                    }
                    res.put(Constants.STR_SUCCESS, false);
                    res.put(Constants.STR_MESSAGE, "组织信息sql时出现错误！");
                    return res;
                }
            }
        } catch (Exception e) {
            _log.error("EnvironmentServiceImpl.EnvironmentSynchronization is error " + e, e);
            res.put(SUCCESS_TEXT, false);
            res.put(MESSAGE_TEXT, "数据保存失败！");
            res.put("flag", false);
        }
        return res;
    }

    private String exec() {
        String result = "";
        init();
        try {
            result = HttpUtils.doPost(URL);
            _log.info("固化环境类型同步数据====" + result);
//            result = "{\r\n" +
//                    "    \"code\": 0,\r\n" +
//                    "    \"message\": null,\r\n" +
//                    "    \"data\": [{\r\n" +
//                    "        \"solidfyNo\": \"编号1\",\r\n" +
//                    "        \"solidfyName\": \"测试1\",\r\n" +
//                    "        \"solidfyType\": \"测试1\",\r\n" +
//                    "        },{\r\n" +
//                    "     \"solidfyNo\": \"编号2\",\r\n" +
//                    "     \"solidfyName\": \"测试3\",\r\n" +
//                    "      \"solidfyType\": \"测试3\",\r\n" +
//                    "        },{" +
//                    "     \"solidfyNo\": \"编号322\",\r\n" +
//                    "     \"solidfyName\": \"测试2222\",\r\n" +
//                    "      \"solidfyType\": \"测试2222\",\r\n" +
//                    "}]\r\n" +
//                    "    }]\r\n" +
//                    "}";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public void init() {
        try {
            String appid = Environment.getInstance().getSysConfig(com.ideal.ieai.core.Environment.CICD_SYNC_APPID, "");
            String version = Environment.getInstance().getSysConfig(com.ideal.ieai.core.Environment.CICD_SYNC_VERSION, "");

            Map<String, String> map = new HashMap<String, String>();
            Long time = System.currentTimeMillis();
            StringBuilder sb = new StringBuilder();
            String token = "PMSIFC"; //科管识别码
            map.put("appid",appid);
            map.put("version", version);
            map.put("time", time.toString());
            Map<String, String> resMap = new HashMap<String, String>();
            resMap = sortMapByKey(map);
            for (Map.Entry<String, String> entry : resMap.entrySet()) {
                sb.append(entry.getKey()).append(entry.getValue());
            }
            sb.append(token).toString();
            String sign = stringMD5(sb.toString()).toLowerCase();
            map.put("sign", sign);
            URL = url(map);
            _log.info("同步请求地址：" + URL);
        } catch (Exception e) {
            _log.error("EnvironmentServiceImpl.init is error " + e);
        }
    }

    public Map<String, String> sortMapByKey(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, String> sortMap = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return o1.compareTo(o2);
            }
        });
        sortMap.putAll(map);
        return sortMap;
    }

    public String stringMD5(String input) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        byte[] inputByteArray = input.getBytes();
        messageDigest.update(inputByteArray);
        byte[] resultByteArray = messageDigest.digest();
        return byteArrayToHex(resultByteArray);
    }

    public String byteArrayToHex(byte[] byteArray) {
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] resultCharArray = new char[byteArray.length * 2];
        int index = 0;
        for (byte b : byteArray) {
            resultCharArray[index++] = hexDigits[b >>> 4 & 0xf];
            resultCharArray[index++] = hexDigits[b & 0xf];
        }
        return new String(resultCharArray);
    }

    public List data(String result) {
        List list = new ArrayList();
        Map resultMap = new HashMap();
        try {
            JSONObject obj = JSONObject.fromObject(result);
            JSONArray data = obj.getJSONArray("data");
            System.err.println(data.toString());
            for (int i = 0; i < data.size(); i++) {
                JSONObject objectJson = data.getJSONObject(i);
                EnvironmentBean bean = new EnvironmentBean();
                bean = (EnvironmentBean) JSONObject.toBean(objectJson, EnvironmentBean.class, resultMap);
                list.add(bean);
            }
        } catch (Exception e) {
            _log.error("EnvironmentServiceImpl.data is error" + e);
        }
        return list;
    }

    public String url(Map<String, String> paramMap) {
        String url = Environment.getInstance().getSysConfig(Environment.CICD_FJ_SYNC_ENVIRONMENT_URL, "");
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        Set<Map.Entry<String, String>> set = paramMap.entrySet();
        for (Map.Entry<String, String> entry : set) {
            params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        String param = URLEncodedUtils.format(params, "UTF-8");
        String uri = url + "?" + param;
        return uri;
    }
}
