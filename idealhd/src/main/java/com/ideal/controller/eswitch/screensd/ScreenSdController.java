package com.ideal.controller.eswitch.screensd;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.commons.Constants;
import com.ideal.service.eswitch.screensd.ScreenSdService;

/**   
 * @ClassName:  ScreenSdController   
 * @Description:山东城商联盟大屏控制类   
 * @author: yunsong_ge 
 * @date:   2020年8月13日 下午4:08:49   
 *     
 * @Copyright: 2020-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
@Controller
public class ScreenSdController
{
    private static final Logger _log         = Logger.getLogger(ScreenSdController.class);
    private static final String ERRORMESSAGE = "获取信息失败！";

    /**   
     * @Title: screenMonitorSd   
     * @Description: 郑州银行，灾备切换监控大屏   
     * @param request
     * @return      
     * @author: yunsong_ge 
     * @date:   2020年8月13日 下午4:09:38   
     */
    @RequestMapping("initScreenSd.do")
    public String screenMonitorSd ( HttpServletRequest request )
    {
        ScreenSdService service = new ScreenSdService();
        try
        {
            Map map = service.getRunningProLogicList(Constants.IEAI_EMERGENCY_SWITCH);
            String insname = service.getintanceName(Constants.IEAI_EMERGENCY_SWITCH);
            request.setAttribute("logicList", map.get("list"));
            request.setAttribute("insname", insname);
        } catch (Exception e)
        {
            _log.error(e);
        }
        return "eswitch/screen_sd/screensd";
    }
    
    @RequestMapping("initScreenNmg.do")
    public String screenMonitorNmg ( HttpServletRequest request )
    {
        ScreenSdService service = new ScreenSdService();
        try
        {
            Map map = service.getRunningProLogicList(Constants.IEAI_EMERGENCY_SWITCH);
            String insname = service.getintanceName(Constants.IEAI_EMERGENCY_SWITCH);
            request.setAttribute("logicList", map.get("list"));
            request.setAttribute("insname", insname);
        } catch (Exception e)
        {
            _log.error(e);
        }
        return "eswitch/nmgScreenSwitch/nmgScreenFromSd/screensd";
    }

    /**   
     * @Title: getSdCslmInfoList   
     * @Description: 获取大屏信息。  系统、设备、生产、同城、异地信息
     * @return      
     * @author: yunsong_ge 
     * @date:   2020年8月13日 下午4:10:04   
     */  
    @RequestMapping("getSdCslmInfoList.do")
    @ResponseBody
    public Map getSdCslmInfoList ()
    {
        Map map = new HashMap();
        ScreenSdService service = new ScreenSdService();
        try
        {
            map = service.getSdCslmInfoList(Constants.IEAI_EMERGENCY_SWITCH);
        } catch (Exception e)
        {
            _log.error(ERRORMESSAGE, e);
        }
        return map;
    }

    /**   
     * @Title: getSwitchMonitorConnerSdCslmScreen   
     * @Description: 查询顺序并发   
     * @param request
     * @param iruninsid
     * @return      
     * @author: yunsong_ge 
     * @date:   2020年8月13日 下午4:11:04   
     */  
    @RequestMapping("getSwitchMonitorConnerSdCslmScreen.do")
    @ResponseBody
    public Map getSwitchMonitorConnerSdCslmScreen ( HttpServletRequest request, Long iruninsid )
    {
        Map map = new HashMap();
        ScreenSdService service = new ScreenSdService();
        try
        {
            map = service.getSwitchMonitorConnerSdCslmScreen(iruninsid, Constants.IEAI_EMERGENCY_SWITCH);
        } catch (Exception e)
        {
            _log.error(ERRORMESSAGE, e);
        }
        return map;
    }

    /**   
     * @Title: getSysProgressDataSdCslmScreen   
     * @Description: 系统下的步骤状态 
     * @param request
     * @param iruninsid
     * @return      
     * @author: yunsong_ge 
     * @date:   2020年8月13日 下午4:11:18   
     */  
    @RequestMapping("getSysProgressDataSdCslmScreen.do")
    @ResponseBody
    public List getSysProgressDataSdCslmScreen ( HttpServletRequest request, Long iruninsid )
    {
        List list = new ArrayList();
        ScreenSdService service = new ScreenSdService();
        try
        {
            list = service.getSysProgressDataSdCslmScreen(iruninsid, Constants.IEAI_EMERGENCY_SWITCH);
        } catch (Exception e)
        {
            _log.error(ERRORMESSAGE, e);
        }
        return list;
    }

    /**   
     * @Title: getSwitchStepSdCslmScreen   
     * @Description: 获取子步骤
     * @param iruninsid
     * @param connerid
     * @return      
     * @author: yunsong_ge 
     * @date:   2020年8月13日 下午4:12:13   
     */  
    @RequestMapping("getSwitchStepSdCslmScreen.do")
    @ResponseBody
    public List getSwitchStepSdCslmScreen ( Long iruninsid, Long connerid )
    {
        List list = new ArrayList();
        ScreenSdService service = new ScreenSdService();
        try
        {
            list = service.getSwitchStepSdCslmScreen(iruninsid, connerid, Constants.IEAI_EMERGENCY_SWITCH);
        } catch (Exception e)
        {
            _log.error(ERRORMESSAGE, e);
        }
        return list;
    }
    
    
    @RequestMapping("querySdCslmSwitchXML.do")
    public void queryCibSwitchXML ( String instancename, HttpServletResponse response )
    {
        ScreenSdService cs=new ScreenSdService();
        try
        {
            String xml = cs.querySdCslmSwitchXML(instancename, Constants.IEAI_EMERGENCY_SWITCH);
            response.setContentType("text/xml;charset=utf-8");
            response.getWriter().write(xml);
        } catch (Exception e)
        {
            _log.error("查询失败", e);
        }
    }
    
    @RequestMapping("querySdCslmSwitchState.do")
    @ResponseBody
    public Map getPrologicSwitchState ( String instancename )
    {
        Map map = new HashMap();
        ScreenSdService cs=new ScreenSdService();
        try
        {
            map = cs.getPrologicSwitchState(instancename, Constants.IEAI_EMERGENCY_SWITCH);
        } catch (Exception e)
        {
            _log.error("查询失败", e);
        }
        return map;
    }
    @RequestMapping("getPredictTime.do")
    @ResponseBody
    public Map getPredictTime ( String iworkitemidListStr )
    {
        Map map = new HashMap();
        ScreenSdService cs=new ScreenSdService();
        try
        {
            map = cs.getPredictTime(iworkitemidListStr, Constants.IEAI_EMERGENCY_SWITCH);
        } catch (Exception e)
        {
            _log.error("查询失败", e);
        }
        return map;
    }
}
