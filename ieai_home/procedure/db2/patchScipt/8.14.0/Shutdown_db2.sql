-- 8.14.0 version patch is as follows
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_RESOURCE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_RESOURCE (ID NUMERIC (19) NOT NULL,INAME VARCHAR (100),ITYPENAME VARCHAR (50),IREMARKS VARCHAR (255),XNAME VARCHAR (100),XREMARK VARCHAR (255),XSERVER VARCHAR (50),XPORT NUMERIC (19),XTIME VARCHAR (255),XAGREEMENT VARCHAR (10), CONSTRAINT PK_IEAI_SHUTDOWN_RESOURCE PRIMARY KEY (ID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_ACTION_SCRIPT';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_ACTION_SCRIPT (IID NUMERIC (19) NOT NULL,INFOID NUMERIC (19),SERVICEIP VARCHAR (255),PORT NUMERIC (19),TASKNAME VARCHAR (255),EACHNUM VARCHAR (255),EXECUSER VARCHAR (255),SCRIPTPARAMS VARCHAR (1000),USERNAME VARCHAR (255),UUID VARCHAR (255),ISORDER NUMERIC (19),AGENTIPANDPORT VARCHAR (255),SCRIPTID NUMBER(19), CONSTRAINT PK_IEAI_SHUTDOWN_ACTION_SCRIPT PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_ACTION_INTO';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_ACTION_INTO (IID NUMERIC (19) NOT NULL,IORDER NUMERIC (19),IACTID NUMERIC (19),IDETAILSID NUMERIC (19),INAME VARCHAR (255),IOS_TYPE VARCHAR (255),INODE_TYPE VARCHAR (255),ICREATION_USER VARCHAR (255),ICREATION_TIME TIMESTAMP ,IVERSION VARCHAR (50), CONSTRAINT PK_IEAI_SHUTDOWN_ACTION_INTO PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_ACTION';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_ACTION (IID NUMERIC (19) NOT NULL,INAME VARCHAR (255),ITYPE VARCHAR (255),IDESC VARCHAR (500),ICREATION_USER VARCHAR (255),ICREATION_TIME TIMESTAMP ,IVERSION VARCHAR (50),ISTATUS NUMERIC (19), CONSTRAINT PK_IEAI_SHUTDOWN_ACTION PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_PARAM_HIS';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_PARAM_HIS (IID NUMERIC (19) NOT NULL,HOST_ID NUMERIC (19),TASK_ID NUMERIC (19),NAME VARCHAR (255),TYPE VARCHAR (255),VALUE VARCHAR (255),IDESCRIBE VARCHAR (1000), CONSTRAINT PK_IEAI_SHUTDOWN_PARAM_HIS PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_HOST_HIS';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_HOST_HIS (IID NUMERIC (19) NOT NULL,TASK_ID NUMERIC (19),FLOW_ID NUMERIC (19),HOST_NAME VARCHAR (255),OS VARCHAR (255),IP VARCHAR (255),PORT NUMERIC (19),IS_SHUTDOWN NUMERIC (19), CONSTRAINT PK_IEAI_SHUTDOWN_HOST_HIS PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_TASK_HIS';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_TASK_HIS (IID NUMERIC (19) NOT NULL,TASK_NAME VARCHAR (255),TASK_STATE NUMERIC (19),SOURCE_USER VARCHAR (255),CREATION_TIME TIMESTAMP ,START_USER VARCHAR (255),STARTTIME TIMESTAMP ,ENDTIME TIMESTAMP ,WEBSTUDIO_ID NUMERIC (19),FLOW_NAME VARCHAR (255),PROJECT_ID NUMERIC (19),PROJECT_NAME VARCHAR (255),HOST_NAME VARCHAR (255),APP_NAME VARCHAR (255),FRAMEWORK VARCHAR (255),CARRY_OUT_SYS VARCHAR (255),CARRY_OUT_APP VARCHAR (255),CARRY_OUT_DBA VARCHAR (255),MAINTAIN_TYPE VARCHAR (255),MAINTAIN_INFO VARCHAR (500),IDESCRIBE VARCHAR (255),PLANID VARCHAR (50), CONSTRAINT PK_IEAI_SHUTDOWN_TASK_HIS PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_PARAM';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_PARAM (IID NUMERIC (19) NOT NULL,HOST_ID NUMERIC (19),TASK_ID NUMERIC (19),NAME VARCHAR (255),TYPE VARCHAR (255),VALUE VARCHAR (255),IDESCRIBE VARCHAR (1000), CONSTRAINT PK_IEAI_SHUTDOWN_PARAM PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_TASK';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_TASK (IID NUMERIC (19) NOT NULL,TASK_NAME VARCHAR (255),TASK_STATE NUMERIC (19),SOURCE_USER VARCHAR (255),CREATION_TIME TIMESTAMP ,START_USER VARCHAR (255),STARTTIME TIMESTAMP ,ENDTIME TIMESTAMP ,WEBSTUDIO_ID NUMERIC (19),FLOW_NAME VARCHAR (255),PROJECT_ID NUMERIC (19),PROJECT_NAME VARCHAR (255),HOST_NAME VARCHAR (255),APP_NAME VARCHAR (255),FRAMEWORK VARCHAR (255),CARRY_OUT_SYS VARCHAR (255),CARRY_OUT_APP VARCHAR (255),CARRY_OUT_DBA VARCHAR (255),MAINTAIN_TYPE VARCHAR (255),MAINTAIN_INFO VARCHAR (500),IDESCRIBE VARCHAR (255),PLANID VARCHAR (50), CONSTRAINT PK_IEAI_SHUTDOWN_TASK PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_PLAN';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_PLAN (IID NUMERIC(19) NOT NULL,HOSTID VARCHAR (32),YEAR VARCHAR (40),STATE VARCHAR (40),APP_OF_AFF VARCHAR (50),SERVER_RUN_STATE VARCHAR (40),APP_PUT_STATE VARCHAR (40),OPE_SYS_VER VARCHAR (40),HOSTNAME VARCHAR (100),IP VARCHAR (50),LAST_UP_TIME VARCHAR (100),CI_PERSON VARCHAR (30),APP_ADMIN_A VARCHAR (30),GROUP_OF_AFF VARCHAR (40),OPE_SYS_INFO VARCHAR (100),SERVER_USAGE VARCHAR (30),SERVER_TYPE VARCHAR (40),DUTY_GROUP VARCHAR (30),DUTY_PERSON VARCHAR (30),FRAMEWORK VARCHAR (50),SHUTDOWN_ADVICE VARCHAR (50),NO_SHUTDOWN_CASE VARCHAR (255),MASTERS_NEED_ATT VARCHAR (50),EARLY_MAIN_IDEN VARCHAR (30),EARLY_YEAR_MEMO VARCHAR (30),EARLY_YEAR_MES VARCHAR (100),EARLY_EXEC_WINDOW VARCHAR (50),MAIN_PLAN_ADJ_SIGN VARCHAR (100),ADJ_MAIN_IDEN VARCHAR (30),ADJ_YEAR_MEMO VARCHAR (255),ADJ_EXEC_WINDOW VARCHAR (50),ADJ_CAUSE VARCHAR (1000),WINDOW_EXEC_MATTER VARCHAR (30),PLAN_START_TIME TIMESTAMP ,PLAN_END_TIME TIMESTAMP ,CARRY_OUT_SYS VARCHAR (50),CARRY_OUT_DBA VARCHAR (50),CARRY_OUT_APP VARCHAR (50),IM_ON_SERVICE_DU_MAIN VARCHAR (1000),GEN_PRE_PRE VARCHAR (100),MOTES_MAIN_EXP_SUM VARCHAR (255),MAIN_COM_IDEN VARCHAR (30),REALLY_START_TIME TIMESTAMP ,REALLY_END_TIME TIMESTAMP ,DESC_OF_OTHER_EXCE VARCHAR (255),EXPORT_FLAG NUMERIC (19),SYNC_STATE NUMERIC (19),SCHEDULE_STATE NUMERIC (19), CONSTRAINT PK_IEAI_SHUTDOWN_PLAN PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_SCHEDULE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_SCHEDULE (IID NUMERIC (19) NOT NULL,PLAN_ID NUMERIC (19),WEBSTUDIO_ID NUMERIC (19),FLOW_NAME VARCHAR (255),PROJECTID NUMERIC (19),CREATION_USER VARCHAR (255),CREATION_TIME TIMESTAMP ,EXECWINDOW VARCHAR (255), CONSTRAINT PK_IEAI_SHUTDOWN_SCHEDULE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_HOST';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_HOST (IID NUMERIC (19) NOT NULL,TASK_ID NUMERIC (19),FLOW_ID NUMERIC (19),HOST_NAME VARCHAR (255),OS VARCHAR (255),IP VARCHAR (255),PORT NUMERIC (19),IS_SHUTDOWN NUMERIC (19), CONSTRAINT PK_IEAI_SHUTDOWN_HOST PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROJECT_INFO_WS' AND  COLNAME='IFRAMEWORK';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'ALTER TABLE IEAI_PROJECT_INFO_WS ADD IFRAMEWORK VARCHAR(255)'; 
			   PREPARE	SQLA FROM LS_SQL; 
			   EXECUTE SQLA;
		END	IF;	
END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_ACTION_HTTP';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_ACTION_HTTP (IID NUMERIC (19) NOT NULL,IPORT NUMERIC (19),IHTTPTYPE VARCHAR (255),ISERVER VARCHAR (255),IREQPATH VARCHAR (255),ISTRINGPARAMS VARCHAR (255),IURLENCODING VARCHAR (255),IAS VARCHAR (255),IMETHOD VARCHAR (255),ITIMEOUT NUMERIC (19), CONSTRAINT PK_IEAI_SHUTDOWN_ACTION_HTTP PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_XMDB_BPAPPSYSTEM';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_XMDB_BPAPPSYSTEM (IID NUMERIC (19) NOT NULL,INSTID VARCHAR (255),SECONDAPP VARCHAR (255),ITIME NUMERIC (19), CONSTRAINT PK_IEAI_XMDB_BPAPPSYSTEM PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_XMDB_GETBYCLASSNAME';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_XMDB_GETBYCLASSNAME (IID NUMERIC (19) NOT NULL,DOMAIN VARCHAR (255),CLASSNAME VARCHAR (255),CLASSDISPLAYNAME VARCHAR (255),LASTUPDATETIME NUMERIC (19),LASTUPDATEUSER VARCHAR (255),CREATETIME NUMERIC (19),CREATEUSER VARCHAR (255),DISPLAYNAME VARCHAR (255),NAME VARCHAR (255),DETAIL VARCHAR (255),ISQUERY VARCHAR (255),ID VARCHAR (255),ISVIEW VARCHAR (255),CICLASSID VARCHAR (255),CLSNAME VARCHAR (255),SOURCE VARCHAR (255),STATUS VARCHAR (255),VERSION VARCHAR (255),CICLASSNAME VARCHAR (255),CICLASSDISPLAYNAME VARCHAR (255),TAGS VARCHAR (255),VIEWID VARCHAR (255),FLAG VARCHAR (255),USEDYEAR VARCHAR (255),HOSTNAME VARCHAR (255),CREATEUSERID VARCHAR (255),MEMORY VARCHAR (255),PRODUCTID VARCHAR (255),CPUMODEL VARCHAR (255),UPDATEAT VARCHAR (255),REMARK VARCHAR (255),CISTATUS VARCHAR (255),HOSTZHDESC VARCHAR (255),CREATEAT VARCHAR (255),MANUFACTURER VARCHAR (255),BACKUPIP VARCHAR (255),OSVERSION VARCHAR (255),PHYSICTYPE VARCHAR (255),OSBIT VARCHAR (255),OSTYPE VARCHAR (255),MODEL VARCHAR (255),SN VARCHAR (255),BRAND VARCHAR (255),AUTOMATICDISCOVERYTIME VARCHAR (255),CIROLE VARCHAR (255),SUBCLASS VARCHAR (255),UPDATEUSERID VARCHAR (255),USEFOR VARCHAR (255),UPDATEUSER VARCHAR (255),NETCARDNUM NUMERIC (19),CIOWNER VARCHAR (255),VERSION2 VARCHAR (255),CPUNUM NUMERIC (19),SERVICEIP VARCHAR (255),ISAUTODISCOVERY VARCHAR (255),ENTRY VARCHAR (255),INSTID VARCHAR (255),DISK NUMERIC (19),TENANTID VARCHAR (255),HBACARDNUM NUMERIC (19),CREATEUSER2 VARCHAR (255),CATEGORY VARCHAR (255),CPUCORENUM VARCHAR (255), CONSTRAINT PK_IEAI_XMDB_GETBYCLASSNAME PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_XMDB_IGNORE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_XMDB_IGNORE (IID NUMERIC (19) NOT NULL,IIP VARCHAR (255),IIGNORE NUMERIC (19), CONSTRAINT PK_IEAI_XMDB_IGNORE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_XMDB_IGNORE_LOG';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_XMDB_IGNORE_LOG (IID NUMERIC (19) NOT NULL,IIP VARCHAR (255),IIGNORE NUMERIC (19),IUSERID NUMERIC (19),ITIME NUMERIC (19), CONSTRAINT PK_IEAI_XMDB_IGNORE_LOG PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_XMDB_ISRELRULE';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_XMDB_ISRELRULE (IID NUMERIC (19) NOT NULL,SRCCIPK VARCHAR (255),SRCCLSNAME VARCHAR (255),DESTCIPK VARCHAR (255),DESTCLSNAME VARCHAR (255),IFLAG VARCHAR (255),LASTUPDATETIME NUMERIC (19),ITIME NUMERIC (19), CONSTRAINT PK_IEAI_XMDB_ISRELRULE PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_XMDB_LASTUPDATETIME';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_XMDB_LASTUPDATETIME (IID NUMERIC (19) NOT NULL,SECONDAPP VARCHAR (255),IIP VARCHAR (255),IHOSTNAME VARCHAR (255),IFLAG NUMERIC (19),LASTUPDATETIME NUMERIC (19), CONSTRAINT PK_IEAI_XMDB_LASTUPDATETIME PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$

BEGIN
        DECLARE LS_SQL VARCHAR(2000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=28;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_GROUPMESSAGE(GROUPID, GROUPNAME, GROUPDESCRIPTION, IORDER, IIMG) VALUES(28, ''关机维护'', ''关机维护模块组'', 10, ''images/info6667.png'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 28;
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (28,28,''关机维护源'',0)';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='xmdbPageSize';
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''xmdbPageSize'', ''200'', ''xmdb同步接口单页数据条数'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='xmdbSysnFlag'; 
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''xmdbSysnFlag'', ''false'', ''xmdb同步标识'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='xmdbSysnLastTime';
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''xmdbSysnLastTime'', ''暂未同步'', ''xmdb最后同步时间'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='xmdbSysnFlag_bpappsystem';
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''xmdbSysnFlag_bpappsystem'', ''false'', ''xmdb同步应用标识'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='xmdbSysnFlag_isRelRule';
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''xmdbSysnFlag_isRelRule'', ''false'', ''xmdb同步应用标识与服务器关系'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='serverip1_ActiveDiscovery';
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''serverip1_ActiveDiscovery'', '''', ''XMDB定时同步数据IP'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;
		
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='startTime1_ActiveDiscovery';
        IF LI_EXISTS = 0 THEN
        SET LS_SQL ='INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), ''startTime1_ActiveDiscovery'', ''4'', ''XMDB定时同步时间（时）'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
			commit;
        END IF;

END
$
BEGIN
     DECLARE LS_SQL VARCHAR(2000);
     DECLARE  LI_EXISTS  NUMERIC(2);

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_ACTION_PARAM';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_ACTION_PARAM (IID NUMERIC (19) NOT NULL,IMID NUMERIC (19),IMTYPE VARCHAR (255),IPARAMTYPE VARCHAR (255),INAME VARCHAR (255),IVALUE VARCHAR (255),IBODY CLOB ,IDESC VARCHAR (255), CONSTRAINT PK_IEAI_SHUTDOWN_ACTION_PARAM PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_APP_PARAM';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_APP_PARAM (IID NUMERIC (19) NOT NULL,APPID NUMERIC (19),IPARAM VARCHAR (255), CONSTRAINT PK_IEAI_SHUTDOWN_APP_PARAM PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_APPLICATION';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_APPLICATION (IID NUMERIC (19) NOT NULL,ALLID NUMERIC(19),STEP NUMERIC(19),IP VARCHAR (50),PERFORMUSER VARCHAR (50),APPNAME VARCHAR (255),SSFLAG VARCHAR (50),CMD VARCHAR (255),OUTTIME VARCHAR (50),REMARK VARCHAR (255), CONSTRAINT PK_IEAI_SHUTDOWN_APPLICATION PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME= 'IEAI_SHUTDOWN_ALL_APP';
        IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'CREATE TABLE IEAI_SHUTDOWN_ALL_APP(IID NUMERIC(19) NOT NULL,IP VARCHAR(32),STATE VARCHAR(32),CONSTRAINT PK_IEAI_SHUTDOWN_ALL_APP  PRIMARY KEY (IID))';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END IF;
END
$
