-- 4.7.17
	DELIMITER ;;
	DROP PROCEDURE IF <PERSON>XIS<PERSON> UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
	  CREATE TABLE IF NOT EXISTS IEAI_APM_CODE (IID   DECIMAL(19,0) NOT NULL, IAP<PERSON>YPE INTEGER, IAPMNAME VARCHAR(255), CONSTRAINT PK_IEAI_APM_CODE  PRIMARY KEY(IID));
	   
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	
	DELIMITER ;;
  DROP PROCEDURE IF EXISTS UPPRDE;;
  CREATE PROCEDURE UPPRDE()
  BEGIN
        IF NOT EXISTS (SELECT * FROM IEAI_GROUPMESSAGE WHERE GROUPID=16) THEN
           INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (16, '应用维护', '应用维护模块组', 16, 'images/info82.png');
        END IF;
       
        IF NOT EXISTS (SELECT * FROM IEAI_PROJECT WHERE IID=-16) THEN
            INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-16, 0, '所有应用维护业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 16, -16, -16, -16);
        END IF;
				
		IF NOT EXISTS (SELECT * FROM IEAI_DBSOURCE WHERE IDBSOURCEID=16) THEN
            INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (16,16,'应用维护源','','','','',0);
        END IF;
        
        IF NOT EXISTS (SELECT * FROM IEAI_APM_CODE WHERE IID=1) THEN
            INSERT INTO IEAI_APM_CODE(IID,IAPMTYPE,IAPMNAME) VALUES(1,0,'应用启停');
        END IF;
        
        IF NOT EXISTS (SELECT * FROM IEAI_APM_CODE WHERE IID=2) THEN
            INSERT INTO IEAI_APM_CODE(IID,IAPMTYPE,IAPMNAME) VALUES(2,1,'数据获取');
        END IF;
        
  END;;
  DELIMITER ;
  CALL UPPRDE();
  
  
  DELIMITER ;;
  DROP PROCEDURE IF EXISTS UPPRDE;;
  CREATE PROCEDURE UPPRDE()
  BEGIN
        
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'IAPMTYPE') THEN
            ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN IAPMTYPE INTEGER ;
      END IF;
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE' AND COLUMN_NAME = 'IAPMTYPE') THEN
            ALTER TABLE IEAI_RUN_INSTANCE ADD COLUMN IAPMTYPE INTEGER ;
      END IF;
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME = 'IAPMTYPE') THEN
            ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD COLUMN IAPMTYPE INTEGER ;
      END IF;
      
      IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE' AND COLUMN_NAME = 'ISYSTYPE') THEN
            ALTER TABLE IEAI_RUN_INSTANCE MODIFY COLUMN ISYSTYPE INTEGER ;
      END IF;
      
      IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME = 'ISYSTYPE') THEN
            ALTER TABLE IEAI_RUN_INSTANCE_HIS MODIFY COLUMN ISYSTYPE INTEGER ;
      END IF;
        
  END;;
  DELIMITER ;
  CALL UPPRDE();

-- 4.7.18
		DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	 	CREATE PROCEDURE UPPRDE()
		BEGIN	
			IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_SUS_ITSM_TASKID_REL' AND INDEX_NAME = 'IDX_IEAISUSITSMTASKIDREL_01') THEN
				CREATE INDEX IDX_IEAISUSITSMTASKIDREL_01 ON IEAI_SUS_ITSM_TASKID_REL (IITSM_TASKID);
			END IF;
		END;;
		DELIMITER ;
		CALL UPPRDE();

-- 4.7.23    
  DELIMITER ;;
  DROP PROCEDURE IF EXISTS UPPRDE;;
  CREATE PROCEDURE UPPRDE()
  BEGIN
        
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'ISSELFHEALING') THEN
            ALTER TABLE IEAI_INSTANCE_VERSION ADD ISSELFHEALING INTEGER DEFAULT 0 ;
      END IF;
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME = 'ISSELFHEALING') THEN
            ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD ISSELFHEALING INTEGER DEFAULT 0 ;
      END IF;
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME = 'ISELFHEALING') THEN
            ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ISELFHEALING INTEGER DEFAULT 0 ;
      END IF;
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME = 'ISELFHEALING') THEN
            ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ISELFHEALING INTEGER DEFAULT 0 ;
      END IF;
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME = 'ISELFHEALING') THEN
            ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD ISELFHEALING INTEGER DEFAULT 0 ;
      END IF;
	  
	  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'TMP_RUN_INSTANCE' AND COLUMN_NAME = 'ISELFHEALING') THEN
            ALTER TABLE TMP_RUN_INSTANCE ADD ISELFHEALING INTEGER DEFAULT 0;
      END IF;
	  
	  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_SUS_RUN_INSTANCEINFO' AND COLUMN_NAME = 'ISELFHEALING') THEN
            ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD ISELFHEALING INTEGER DEFAULT 0;
      END IF;
        
  END;;
  DELIMITER ;
  CALL UPPRDE();

-- 4.7.24
  DELIMITER ;;
  DROP PROCEDURE IF EXISTS UPPRDE;;
  CREATE PROCEDURE UPPRDE()
  BEGIN
        
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DOUBLECHECK_WORKITEM' AND COLUMN_NAME = 'CPFLAG') THEN
            ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD CPFLAG INTEGER DEFAULT 0;
      END IF;
	  
	  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLUMN_NAME = 'CPFLAG') THEN
            ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD CPFLAG INTEGER DEFAULT 0;
      END IF;
              
  END;;
  DELIMITER ;
  CALL UPPRDE();

 -- 4.7.25    
  DELIMITER ;;
  DROP PROCEDURE IF EXISTS UPPRDE;;
  CREATE PROCEDURE UPPRDE()
  BEGIN
        
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME = 'IERROR') THEN
            ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IERROR VARCHAR(800);
      END IF;
	  
	  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_ITSM_AUTO' AND COLUMN_NAME = 'ORDERNUM') THEN
            ALTER TABLE IEAI_ITSM_AUTO ADD ORDERNUM VARCHAR(2000);
      END IF;
        
  END;;
  DELIMITER ;
  CALL UPPRDE(); 
  
   DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN
		  
		IF NOT EXISTS (SELECT * FROM IEAI_HIGHOPER WHERE  IBUTTONID=111117) THEN
			INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(111117, '确认执行', 'processMonitorPersonExceptionExecAPM.do', '人工处理');
			commit;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM IEAI_MENU_BUTTON WHERE  IMENUBUTTONID=111117) THEN
			INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(111117, 16005, 111117, '');
			commit;
		END IF;
	    END;;
  DELIMITER ;
  CALL UPPRDE();