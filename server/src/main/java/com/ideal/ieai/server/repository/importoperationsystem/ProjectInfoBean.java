package com.ideal.ieai.server.repository.importoperationsystem;

public class ProjectInfoBean
{
    private long   systemId;
    public long getSystemId ()
    {
        return systemId;
    }
    public void setSystemId ( long systemId )
    {
        this.systemId = systemId;
    }
    private String sysParam;
    private String sysDesc;
    private long priority;
    private long sysType;
    public String getSysParam ()
    {
        return sysParam;
    }
    public void setSysParam ( String sysParam )
    {
        this.sysParam = sysParam;
    }
    public String getSysDesc ()
    {
        return sysDesc;
    }
    public void setSysDesc ( String sysDesc )
    {
        this.sysDesc = sysDesc;
    }
    public long getPriority ()
    {
        return priority;
    }
    public void setPriority ( long priority )
    {
        this.priority = priority;
    }
    public long getSysType ()
    {
        return sysType;
    }
    public void setSysType ( long sysType )
    {
        this.sysType = sysType;
    }
    
    
}
