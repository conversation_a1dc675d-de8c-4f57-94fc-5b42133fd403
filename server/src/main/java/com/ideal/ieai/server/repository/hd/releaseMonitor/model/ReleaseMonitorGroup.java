package com.ideal.ieai.server.repository.hd.releaseMonitor.model;

import java.util.ArrayList;
import java.util.List;

public class ReleaseMonitorGroup
{
    private long iid;
    private long flowId;
    private long conner; // 并发步骤
    private long serner; // 步骤序号
    private String runInsName; // 任务实例名称
    private String actName; // 任务名称，也是步骤名称
    private String sysName; // 系统名称
    private String dependentStepId; // 依赖步骤ID
    private String dependentStepName; // 依赖步骤名称(显示)
    private String ip; // 代理服务器
    private int actType; // 步骤类型
    private String startTime;
    private String endTime;
    private long runLength;
    private String state;
    private int orignalState;
    private int isFail;
    private String shellPath;
    private String execUser;
    private String modelType;
    private long redoable;
    private String pkgName;
    private boolean leaf;
    private boolean expanded;
    private String iconCls ; 
    private String ipreSysName;      // 依赖系统名称
    private String ipreActName;      // 依赖系统中的活动名称
    private String singleRollback;
    private List<ReleaseMonitorGroup> children = new ArrayList<ReleaseMonitorGroup>();
    private String isRollBack;          // 是否支持回退标识：1是  0否
    private String iJudgeIsBack;        // 是否是监控跳转回退启动，涉及回退标识 1是  0否
    private Integer isHis;             //流程是否已经结束,1: 已经结束  0: 运行中 
    private String ireminfo;
    private String istdout;            //步骤运行输出
    private String iazname;            //AZ切换区域
    private String iproxyIp;           //az proxy ip
    public String getIazname ()
    {
        return iazname;
    }

    public void setIazname ( String iazname )
    {
        this.iazname = iazname;
    }
    
    public String getIreminfo ()
    {
        return ireminfo;
    }

    public void setIreminfo ( String ireminfo )
    {
        this.ireminfo = ireminfo;
    }

    public Integer getIsHis ()
    {
        return isHis;
    }

    public void setIsHis ( Integer isHis )
    {
        this.isHis = isHis;
    }

    public String getSingleRollback ()
    {
        return singleRollback;
    }

    public void setSingleRollback ( String singleRollback )
    {
        this.singleRollback = singleRollback;
    }
    public String getIpreSysName ()
    {
        return ipreSysName;
    }

    public void setIpreSysName ( String ipreSysName )
    {
        this.ipreSysName = ipreSysName;
    }

    public String getIpreActName ()
    {
        return ipreActName;
    }

    public void setIpreActName ( String ipreActName )
    {
        this.ipreActName = ipreActName;
    }
    public List<ReleaseMonitorGroup> getChildren ()
    {
        return children;
    }
    public void setChildren ( List<ReleaseMonitorGroup> children )
    {
        this.children = children;
    }
    public String getIconCls ()
    {
        return iconCls;
    }
    public void setIconCls ( String iconCls )
    {
        this.iconCls = iconCls;
    }
    public boolean isExpanded ()
    {
        return expanded;
    }
    public void setExpanded ( boolean expanded )
    {
        this.expanded = expanded;
    }
    public boolean isLeaf ()
    {
        return leaf;
    }
    public void setLeaf ( boolean leaf )
    {
        this.leaf = leaf;
    }
    public String getPkgName ()
    {
        return pkgName;
    }
    public void setPkgName ( String pkgName )
    {
        this.pkgName = pkgName;
    }
    public long getRedoable ()
    {
        return redoable;
    }
    public void setRedoable ( long redoable )
    {
        this.redoable = redoable;
    }
    public String getModelType ()
    {
        return modelType;
    }
    public void setModelType ( String modelType )
    {
        this.modelType = modelType;
    }
    public long getConner ()
    {
        return conner;
    }
    public void setConner ( long conner )
    {
        this.conner = conner;
    }
    public long getSerner ()
    {
        return serner;
    }
    public void setSerner ( long serner )
    {
        this.serner = serner;
    }
    public String getRunInsName ()
    {
        return runInsName;
    }
    public void setRunInsName ( String runInsName )
    {
        this.runInsName = runInsName;
    }
    public String getSysName ()
    {
        return sysName;
    }
    public void setSysName ( String sysName )
    {
        this.sysName = sysName;
    }
    public String getDependentStepName ()
    {
        return dependentStepName;
    }
    public void setDependentStepName ( String dependentStepName )
    {
        this.dependentStepName = dependentStepName;
    }
    public String getIp ()
    {
        return ip;
    }
    public void setIp ( String ip )
    {
        this.ip = ip;
    }
    public String getStartTime ()
    {
        return startTime;
    }
    public void setStartTime ( String startTime )
    {
        this.startTime = startTime;
    }
    public String getEndTime ()
    {
        return endTime;
    }
    public void setEndTime ( String endTime )
    {
        this.endTime = endTime;
    }
    public long getRunLength ()
    {
        return runLength;
    }
    public void setRunLength ( long runLength )
    {
        this.runLength = runLength;
    }
    public String getState ()
    {
        return state;
    }
    public void setState ( String state )
    {
        this.state = state;
    }
    public int getOrignalState ()
    {
        return orignalState;
    }
    public void setOrignalState ( int orignalState )
    {
        this.orignalState = orignalState;
    }
    public int getIsFail ()
    {
        return isFail;
    }
    public void setIsFail ( int isFail )
    {
        this.isFail = isFail;
    }
    public int getActType ()
    {
        return actType;
    }
    public void setActType ( int actType )
    {
        this.actType = actType;
    }
    public long getFlowId ()
    {
        return flowId;
    }
    public void setFlowId ( long flowId )
    {
        this.flowId = flowId;
    }
    public String getActName ()
    {
        return actName;
    }
    public void setActName ( String actName )
    {
        this.actName = actName;
    }
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public String getShellPath ()
    {
        return shellPath;
    }
    public void setShellPath ( String shellPath )
    {
        this.shellPath = shellPath;
    }
    public String getExecUser ()
    {
        return execUser;
    }
    public void setExecUser ( String execUser )
    {
        this.execUser = execUser;
    }
    public String getDependentStepId ()
    {
        return dependentStepId;
    }
    public void setDependentStepId ( String dependentStepId )
    {
        this.dependentStepId = dependentStepId;
    }

    public String getIsRollBack ()
    {
        return isRollBack;
    }

    public void setIsRollBack ( String isRollBack )
    {
        this.isRollBack = isRollBack;
    }

    public String getiJudgeIsBack ()
    {
        return iJudgeIsBack;
    }

    public void setiJudgeIsBack ( String iJudgeIsBack )
    {
        this.iJudgeIsBack = iJudgeIsBack;
    }

    public String getIstdout ()
    {
        return istdout;
    }

    public void setIstdout ( String istdout )
    {
        this.istdout = istdout;
    }

    public String getIproxyIp ()
    {
        return iproxyIp;
    }

    public void setIproxyIp ( String iproxyIp )
    {
        this.iproxyIp = iproxyIp;
    }
    
    
}
