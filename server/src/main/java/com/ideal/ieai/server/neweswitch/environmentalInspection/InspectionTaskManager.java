package com.ideal.ieai.server.neweswitch.environmentalInspection;

import com.alibaba.fastjson.JSON;
import com.ideal.ieai.commons.Conscommon;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.platform.repository.equipchar.EquipCharFormModel;
import com.ideal.ieai.server.platform.repository.equipchar.EquipCharModel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.ic.supperhc.common.ListUtils;
import com.ideal.ieai.server.repository.hd.ic.supperhc.common.SupperHcConstants;
import com.ideal.ieai.server.repository.hd.ic.supperhc.common.SupperHcToolUtil;
import com.ideal.ieai.server.repository.hd.ic.supperhc.exception.SupperhcException;
import com.ideal.ieai.server.repository.hd.ic.supperhc.fortkey.FortKeyManager;
import com.ideal.ieai.server.repository.hd.ic.supperhc.fortkey.UserAndIpModel;
import com.ideal.ieai.server.repository.hd.ic.supperhc.hcstart.HcStartManager;
import com.ideal.ieai.server.repository.hd.ic.supperhc.hcstart.model.*;
import com.ideal.ieai.server.repository.hd.ic.supperhc.sendhttp.HcSendHttp;
import com.ideal.ieai.server.repository.hd.ic.supperhc.xmrpc.HcSendXmlRpc;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.PageParamBean;
import com.ideal.util.JsonUtil;
import org.apache.commons.dbutils.DbUtils;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class InspectionTaskManager {
    private static final String FAILNUM = "failNum";
    private static final String SUCCNUM = "succNum";
    private static final String FAILCAUSE = "failCause";
    private static final String FAILCAUSEDOWNLOAD = "falicausedownload";

    public static final Logger log = Logger.getLogger(InspectionTaskManager.class);

    public Map<String,Object> getResultInfo (Connection conn, String sql, int start, int limit) throws SQLException
    {
        Map<String,Object> res = new HashMap<>();
        QueryRunner qr = new QueryRunner();
        PageParamBean pageParamBean = PageParamBean.getInstance().getParamPageBean(sql, start, limit);
        Object[] parms = { pageParamBean.getPagePara1(), pageParamBean.getPagePara2() };
        BeanListHandler<InspectionTaskModel> beanhandler = new BeanListHandler<InspectionTaskModel>(InspectionTaskModel.class);
        List<InspectionTaskModel> list = qr.query(conn, pageParamBean.getSql(), beanhandler, parms);
        Number count = qr.query(conn, PageParamBean.getInstance().toCount(sql), new ScalarHandler<Number>());
        res.put("dataList", list);
        res.put("total", count == null ? 0 : count.intValue());

        return res;
    }

    public List getTaskHisExportData (Connection conn, String sql) throws RepositoryException
    {
        List<InspectionTaskModel> list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                InspectionTaskModel model = new InspectionTaskModel();
                model.setItaskname(rs.getString("ITASKNAME"));
                model.setIstarttime(rs.getString("ISTARTTIME"));
                model.setIendtime(rs.getString("IENDTIME"));
                model.setIbatchid(rs.getString("IBATCHID"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getTaskHisExportData is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getTaskHisExportData", log);
        }
        return list;
    }

    public List getcheckResultExportData (Connection conn, String sql) throws RepositoryException
    {
        List<InspectionTaskModel> list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                InspectionTaskModel model = new InspectionTaskModel();
                model.setIsystemname(rs.getString("ISYSTEMNAME"));
                model.setIcheckitemname(rs.getString("ICHECKITEMNAME"));
                model.setIcheckpointname(rs.getString("ICHECKPOINTNAME"));
                model.setIcheckobjectname(rs.getString("ICHECKOBJECTNAME"));
                model.setCheckresult(rs.getString("CHECKRESULT"));
                model.setChecktime(rs.getString("CHECKTIME"));
                model.setCheckvalue(rs.getString("CHECKVALUE"));
                model.setIthreshold(rs.getString("ITHRESHOLD"));
                model.setIcpip(rs.getString("ICPIP"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getcheckResultExportData is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getcheckResultExportData", log);
        }
        return list;
    }

    public List getcompareResultExportData (Connection conn, String sql) throws RepositoryException
    {
        List<InspectionTaskModel> list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                InspectionTaskModel model = new InspectionTaskModel();
                model.setIid(rs.getLong("IID"));
                model.setIprogrammename(rs.getString("IPROGRAMMENAME"));
                model.setIprogrammedes(rs.getString("IPROGRAMMEDES"));
                model.setComparetime(rs.getString("COMPARETIME"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getcompareResultExportData is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getcompareResultExportData", log);
        }
        return list;
    }

    public List getResultNumberLast (Connection conn, String sql) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, String> map = new HashMap<>();
                map.put("ibatchid", rs.getString("IBATCHID"));
                map.put("resultnumber", rs.getString("RESULTNUMBER"));
                map.put("wrongnumber", rs.getString("WRONGNUMBER"));
                list.add(map);
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getResultNumberLast is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getResultNumberLast", log);
        }
        return list;
    }

    public List getResultNumber (Connection conn, String sql, InspectionTaskModel model) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, model.getIid());
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, String> map = new HashMap<>();
                map.put("ibatchid", rs.getString("IBATCHID"));
                map.put("resultnumber", rs.getString("RESULTNUMBER"));
                map.put("wrongnumber", rs.getString("WRONGNUMBER"));
                list.add(map);
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getCheckPointAndProgrammeIds is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCheckPointAndProgrammeIds", log);
        }
        return list;
    }

    public List getCheckPointAndProgrammeIds (Connection conn, String sql, InspectionTaskModel model) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, model.getIid());
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> map = new HashMap<>();
                map.put("IDATATYPE", rs.getString("IDATATYPE"));
                map.put("IDATAID", rs.getString("IDATAID"));
                list.add(map);
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getCheckPointAndProgrammeIds is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCheckPointAndProgrammeIds", log);
        }
        return list;
    }

    public List getDataIds (Connection conn, String sql, InspectionTaskModel model) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, model.getIid());
            rs = ps.executeQuery();
            while (rs.next())
            {
                list.add(rs.getLong("IDATAID"));
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getDataIds is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDataIds", log);
        }
        return list;
    }

    public void saveTask (Connection conn, InspectionTaskModel model) throws RepositoryException
    {
        PreparedStatement configPs = null;
        PreparedStatement taskPs = null;
        PreparedStatement chkPointPs = null;
        PreparedStatement programmePs = null;
        PreparedStatement delPs = null;
        try
        {
            // 任务类型
            String runType = model.getIruntype();
//            // 如果任务是周期执行，则更新所有选中检查点巡检周期
//            if ("1".equals(runType) && model.getChkpointids() != null) {
//                String[] chkPointIds = model.getChkpointids().split(",");
//                for (int i = 0;i < chkPointIds.length;i++) {
//                    String updatePointConfigSql = "UPDATE IEAI_CHKPOINT_CONFIG SET ICRON = ? WHERE IID = ?";
//                    configPs = conn.prepareStatement(updatePointConfigSql);
//                    configPs.setString(1, model.getIcron());
//                    configPs.setLong(2, Long.parseLong(chkPointIds[i]));
//                    configPs.executeUpdate();
//                }
//            }

            // 新增任务
            if (model.getIid() <= 0) {
                long taskId = IdGenerator.createId("IEAI_ENVIRONMENT_CHECK", conn);
                model.setIid(taskId);
                String insertTaskSql = "INSERT INTO IEAI_ENVIRONMENT_CHECK(IID, ITASKNAME, IRUNTYPE, ICRON, IREMARK, ISTATE) VALUES( ?, ?, ?, ?, ?, 0)";
                taskPs = conn.prepareStatement(insertTaskSql);
                taskPs.setLong(1, taskId);
                taskPs.setString(2, model.getItaskname());
                taskPs.setString(3, model.getIruntype());
                taskPs.setString(4, "1".equals(runType) ? model.getIcron() : "");
                taskPs.setString(5, model.getIremark());
                taskPs.executeUpdate();
            }
            // 修改任务
            else {
                String updateTaskSql = "UPDATE IEAI_ENVIRONMENT_CHECK SET ITASKNAME = ?, IRUNTYPE = ?, ICRON = ?, IREMARK = ? WHERE IID = ?";
                taskPs = conn.prepareStatement(updateTaskSql);
                taskPs.setString(1, model.getItaskname());
                taskPs.setString(2, model.getIruntype());
                taskPs.setString(3, "1".equals(runType) ? model.getIcron() : "");
                taskPs.setString(4, model.getIremark());
                taskPs.setLong(5, model.getIid());
                taskPs.executeUpdate();

                String delChkPointOrProgrammeSql = "DELETE FROM IEAI_ENVIRONMENT_CHECK_VIEW WHERE ICHECKID = ?";
                delPs = conn.prepareStatement(delChkPointOrProgrammeSql);
                delPs.setLong(1, model.getIid());
                delPs.executeUpdate();
            }

            // 任务添加检查点
            if (model.getChkpointids() != null) {
                String[] chkPointIds = model.getChkpointids().split(",");
                for (int i = 0;i < chkPointIds.length;i++) {
                    long viewId = IdGenerator.createId("IEAI_ENVIRONMENT_CHECK_VIEW", conn);
                    String insertChkPointSql = "INSERT INTO IEAI_ENVIRONMENT_CHECK_VIEW(IID, ICHECKID, IDATAID, IDATATYPE) VALUES( ?, ?, ?, 0)";
                    chkPointPs = conn.prepareStatement(insertChkPointSql);
                    chkPointPs.setLong(1, viewId);
                    chkPointPs.setLong(2, model.getIid());
                    chkPointPs.setLong(3, Long.parseLong(chkPointIds[i]));
                    chkPointPs.executeUpdate();
                }
            }

            // 任务添加一致性比对方案
            if (model.getProgrammeids() != null) {
                String[] programmeIds = model.getProgrammeids().split(",");
                for (int i = 0;i < programmeIds.length;i++) {
                    long viewId = IdGenerator.createId("IEAI_ENVIRONMENT_CHECK_VIEW", conn);
                    String insertProgrammeSql = "INSERT INTO IEAI_ENVIRONMENT_CHECK_VIEW(IID, ICHECKID, IDATAID, IDATATYPE) VALUES( ?, ?, ?, 1)";
                    programmePs = conn.prepareStatement(insertProgrammeSql);
                    programmePs.setLong(1, viewId);
                    programmePs.setLong(2, model.getIid());
                    programmePs.setLong(3, Long.parseLong(programmeIds[i]));
                    programmePs.executeUpdate();
                }
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.saveTask is error", e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePreparedStatement(configPs, "saveTask", log);
            DBResource.closePreparedStatement(taskPs, "saveTask", log);
            DBResource.closePreparedStatement(chkPointPs, "saveTask", log);
            DBResource.closePreparedStatement(programmePs, "saveTask", log);
            DBResource.closePreparedStatement(delPs, "saveTask", log);
        }
    }

    public void deleteTask (Connection conn, String[] deleteIds) throws RepositoryException
    {
        PreparedStatement delTaskPs = null;
        PreparedStatement delViewPs = null;
        try
        {
            String delTaskSql = "DELETE FROM IEAI_ENVIRONMENT_CHECK WHERE ("+ Conscommon.getInSql(deleteIds, "IID", 1000)+")";
            delTaskPs = conn.prepareStatement(delTaskSql);
            delTaskPs.executeUpdate();

            String delViewSql = "DELETE FROM IEAI_ENVIRONMENT_CHECK_VIEW WHERE ("+ Conscommon.getInSql(deleteIds, "ICHECKID", 1000)+")";
            delViewPs = conn.prepareStatement(delViewSql);
            delViewPs.executeUpdate();
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.deleteTask is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePreparedStatement(delViewPs, "deleteTask", log);
            DBResource.closePreparedStatement(delTaskPs, "deleteTask", log);
        }
    }

    public List<Map<String,Object>> getBusinessSystemForTaskQuery(Connection conn) throws RepositoryException{
        String syssql = " SELECT T.IID,T.INAME FROM IEAI_PROJECT T WHERE T.IPKGCONTENTID=0 AND T.PROTYPE=7 ";
        syssql=syssql+" ORDER BY T.INAME ";
        List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
        PreparedStatement ps =null;
        ResultSet rs =null;
        try
        {
            ps = conn.prepareStatement(syssql);
            rs =ps.executeQuery();
            while(rs.next()) {
                Map<String,Object> map = new HashMap<String, Object>();
                map.put("isystemid",rs.getLong("IID") );
                map.put("isysname",rs.getString("INAME") );
                list.add(map);
            }
        } catch (SQLException e)
        {
            log.error("获取任务配置中下拉业务系统列表数据出错", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally {
            DBResource.closePSRS(rs, ps, "getBusinessSystemForQuery", log);

        }
        return list;
    }

    public List<Map<String,Object>> getComputerForTaskQuery(Connection conn,Long sysid) throws RepositoryException{
        String computersql = " SELECT DISTINCT CL.IP,CL.CPNAME,CL.CPID FROM IEAI_COMPUTER_LIST CL , IEAI_SYS_RELATION R WHERE CL.CPID=R.COMPUTERID ";
        if(sysid!=null) {
            computersql = computersql+ " AND R.SYSTEMID ="+sysid;
        }
        computersql = computersql+ " ORDER BY CL.IP ";
        List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
        PreparedStatement ps =null;
        ResultSet rs =null;
        try
        {
            ps = conn.prepareStatement(computersql);
            rs =ps.executeQuery();
            while(rs.next()) {
                Map<String,Object> map = new HashMap<String, Object>();
                map.put("icomputerid",rs.getLong("CPID") );
                map.put("icomputername",rs.getString("CPNAME")+"("+rs.getString("IP")+")" );
                list.add(map);
            }
        } catch (SQLException e)
        {
            log.error("获取任务配置中下拉设备列表数据出错", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally {
            DBResource.closePSRS(rs, ps, "getComputerForQueryAll", log);

        }
        return list;

    }

    public List<Map<String,Object>> getItemForTaskQuery(Connection conn,Long systemid,Long cpid) throws RepositoryException{
        String itemsql = "SELECT DISTINCT T.ICHKITEMID,T.ICHKITEMNAME FROM IEAI_CHKITEM T WHERE 1=1 ";
        StringBuilder sbb = new StringBuilder();
        sbb.append(" AND  EXISTS  (");
        sbb.append(" SELECT 1 FROM IEAI_COM_CHK WHERE   CHKITEMID=T.ICHKITEMID  ");
        if(systemid!=null) {
            sbb.append(" AND APPLOGO= ").append(systemid);
        }
        if(cpid!=null && cpid>0) {
            sbb.append(" AND CPID= ").append(cpid);
        }
        sbb.append(" ) ");

        itemsql = itemsql+sbb.toString()+ " ORDER BY T.ICHKITEMNAME ";


        List<Map<String,Object>> list = new ArrayList<Map<String,Object>>();
        PreparedStatement ps =null;
        ResultSet rs =null;
        try
        {
            ps = conn.prepareStatement(itemsql);
            rs =ps.executeQuery();
            while(rs.next()) {
                Map<String,Object> map = new HashMap<String, Object>();
                map.put("icheckitemid",rs.getLong("ICHKITEMID") );
                map.put("icheckitemname",rs.getString("ICHKITEMNAME") );
                list.add(map);
            }
        } catch (SQLException e)
        {
            log.error("获取任务配置中下拉巡检项列表数据出错", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally {
            DBResource.closePSRS(rs, ps, "getItemForQuery", log);

        }
        return list;
    }

    public Map<String, String> getProgrammeInfoByIid (Connection conn, String sql, long programmeId) throws RepositoryException
    {
        Map<String, String> map = new HashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, programmeId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("programmename", rs.getString("IPROGRAMMENAME"));
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getProgrammeInfoByIid is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getProgrammeInfoByIid", log);
        }
        return map;
    }

    public int checkTaskName (Connection conn, String sql, InspectionTaskModel model) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, model.getItaskname());
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("COUNT");
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.checkTaskName is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "checkTaskName", log);
        }
        return count;
    }

    public Long[] getDataIdsForTask (Connection conn, String sql, long taskId, String type) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Long[] dataIds = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, taskId);
            ps.setString(2, type);
            rs = ps.executeQuery();
            while (rs.next())
            {
                list.add(rs.getLong("IDATAID"));
            }
            dataIds = (Long[])list.toArray(new Long[0]);
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getDataIdsForTask is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getDataIdsForTask", log);
        }
        return dataIds;
    }

    public Map<String, String> getTaskStartUser (Connection conn, String sql, long taskId) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        Map<String, String> map = new HashMap<>();
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, taskId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("istartuserid", rs.getString("ISTARTUSERID"));
                map.put("istartusername", rs.getString("ISTARTUSERNAME"));
            }
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.getTaskStartUser is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getTaskStartUser", log);
        }
        return map;
    }

    public void insertTaskRun (Connection conn, InspectionTaskModel model) throws RepositoryException
    {
        PreparedStatement ps = null;
        try
        {
            long iId = IdGenerator.createId("IEAI_ENVIRONMENT_CHECK_RUN", conn);
            String sql = "";
            if (JudgeDB.IEAI_DB_TYPE == 2) {
                sql = "INSERT INTO IEAI_ENVIRONMENT_CHECK_RUN (IID, ICHECKID, ISTARTUSER, ISTARTTIME, IBATCHID) VALUES (?, ?, ?, CURRENT TIMESTAMP, ?)";
            } else if (JudgeDB.IEAI_DB_TYPE == 3) {
                sql = "INSERT INTO IEAI_ENVIRONMENT_CHECK_RUN (IID, ICHECKID, ISTARTUSER, ISTARTTIME, IBATCHID) VALUES (?, ?, ?, NOW(), ?)";
            } else {
                sql = "INSERT INTO IEAI_ENVIRONMENT_CHECK_RUN (IID, ICHECKID, ISTARTUSER, ISTARTTIME, IBATCHID) VALUES (?, ?, ?, SYSDATE, ?)";
            }
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iId);
            ps.setLong(2, model.getIid());
            ps.setString(3, model.getIstartuser());
            ps.setString(4, model.getIbatchid());
            ps.executeUpdate();
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.insertTaskRun is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "insertTaskRun", log);
        }
    }

    public void updateTaskStatus (Connection conn, InspectionTaskModel model) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "";
        try
        {
            if ("1".equals(model.getIstate())) {
                sql = "UPDATE IEAI_ENVIRONMENT_CHECK SET ISTATE = ?, ISTARTUSER = ?, ISTARTTIME = ? WHERE IID = ?";
            } else {
                sql = "UPDATE IEAI_ENVIRONMENT_CHECK SET ISTATE = ?, ISTOPUSER = ?, ISTOPTIME = ? WHERE IID = ?";
            }

            ps = conn.prepareStatement(sql);
            ps.setString(1, model.getIstate());
            ps.setString(2, model.getIstate() == "1" ? model.getIstartuser() : model.getIstopuser());
            Date date = new Date();
            java.sql.Date ts = new java.sql.Date(date.getTime());
            ps.setDate(3, ts);
            ps.setLong(4, model.getIid());
            ps.executeUpdate();
        } catch (Exception e)
        {
            log.error("InspectionTaskManager.updateTaskStatus is error", e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateTaskStatus", log);
        }
    }

    public ComputerBean  getAlreadyStartAgentOfCp ( ComputerBean bean)
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet  rs = null;
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT A.IAGENTINFO_ID, A.IAGENT_IP, A.IAGENT_PORT, A.IAGENT_WEBPORT ");
        sql.append("   FROM IEAI_AGENTINFO A ");
        sql.append("   WHERE EXISTS (SELECT 1 ");
        sql.append("                    FROM IEAI_CHKPOINT P, IEAI_COM_CHK C ");
        sql.append("                   WHERE P.SCID = C.SCID ");
        sql.append("                     AND P.IHCAGENTID = A.IAGENTINFO_ID ");
        sql.append("                     AND P.IHCAGENTID > 0 ");
        sql.append("                     AND C.CPID = ?) ");
        try
        {
            conn = DBResource.getConnection("getAlreadyStartAgentOfCp", log, Constants.IEAI_HEALTH_INSPECTION);
            ps = conn.prepareStatement(sql.toString());
            ps.setLong(1, bean.getCpid());
            rs = ps.executeQuery();
            if(rs.next()) {
                String agentport = rs.getString("IAGENT_PORT");

                String httpPort =rs.getString("IAGENT_WEBPORT");
                if(httpPort==null||"".equals(httpPort)||"0".equals(httpPort)) {
                    httpPort= SupperHcConstants.HC_CHECK_AGENT_WEBPORT_DEFAULT.toString();
                }

                //从新封装回实际使用的agent
                bean.setAgentId(rs.getLong("IAGENTINFO_ID"));
                bean.setAgentIp(rs.getString("IAGENT_IP"));
                bean.setAgentPort(agentport == null || "".equals(agentport) ? 0
                        : Integer.parseInt(agentport));
                bean.setAgentWebPort(httpPort == null || "".equals(httpPort) ? 0
                        : Integer.parseInt(httpPort));

            }

        } catch (RepositoryException e)
        {
            log.error("getAlreadyStartAgentOfCp 获取该设备是否已经有启动的agent时出错", e);
        } catch (SQLException e)
        {
            log.error("getAlreadyStartAgentOfCp 获取该设备是否已经有启动的agent时查询执行异常", e);
        }finally {
            DBResource.closeConn(conn, rs, ps, "getAlreadyStartAgentOfCp", log);
        }
        return bean;

    }

    /**
     *
     * @Title: getHCData
     * @Description: 获取巡检数据
     * @param agent
     * @param cpList
     * @param cpGroupId
     * @param type
     * @return
     * @throws RepositoryException
     * @author: 77
     * @throws SupperhcException
     * @date:   2019年11月21日 下午1:35:12
     */
    public HealthCheckData getHCData(AgentInfoModel agent, List<ComputerBean> cpList, Long[] scids, Long[] itemids, Long [] pointids, long cpGroupId, int systype, String startType, String startuser,HcStartManager manager, String batchId) throws RepositoryException, SupperhcException{

        HealthCheckData hcd = null;
        try
        {

            hcd = getHCDataChild( agent, cpList, scids, itemids, pointids, cpGroupId, systype,startType,startuser, manager, batchId);
        } catch (RepositoryException ex)
        {
            log.error("组织启动巡检数据失败！", ex);
            throw new SupperhcException("组织数据失败！",ex);
        }
        return hcd;

    }

    public HealthCheckData getHCDataChild( AgentInfoModel agent,List<ComputerBean> cpList,Long[] scids, Long[] itemids, Long [] pointids,long cpGroupId,int systype ,String startType,String startuser,HcStartManager manager, String batchId) throws RepositoryException, SupperhcException{
        HealthCheckData hcd = new HealthCheckData();
        Connection con = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try
        {
            con = DBResource.getConnection(method, log, systype);

            //封装设备关联的协议信息
            for(ComputerBean bean:cpList) {
                bean.setAgreements(manager.getHcAgreementsByComputer(con,  bean.getCpid()));
            }

            hcd.setComputerList(cpList);

            hcd.setFtpInfos(manager.getAllFtpInfo(con));
            hcd.setAdaptorList(manager.getAdaptorList (con ));

            //获取设备的检查项，检查点，检查项和检查点关系，命令
            Long [] cpids = manager.getCpIds(cpList);
            List<ComChkBean> comChkList = getComChkList(con,cpids,scids, itemids, pointids,cpGroupId);
            hcd.setComChkList(comChkList);


            /**
             *
             if("imme".equals(startType)) {
             log.info("本次为立即巡检，故不需更改巡检项执行agent信息...");
             }else {
             //更新设备表agentId
             updateComAgent(con,comChkList,agent.getAgentId());
             }
             */

            Long [] scIds = manager.getScIds(comChkList);
            if(scIds==null ||scIds.length==0) {
                throw new SupperhcException("未获取到配置的可巡检项");
            }
            List<ChkPointBean> chkPointList =  getChkPointList(con,scIds,pointids,startType,startuser, batchId);
            hcd.setChkPointList(chkPointList);

            //检查点命令
            Long[] chkPointCfgIds = manager.getChkPointCfgIds(chkPointList);
            Long[] cpointids = manager.getChkPointIds(chkPointList);//记录一共需要启动的巡检点集合
            if(chkPointCfgIds==null ||chkPointCfgIds.length==0) {
                throw new SupperhcException("未获取到巡检点");
            }
            List<CheckCmdBean> checkCmdList = manager.getChkCmdList ( con ,chkPointCfgIds,scIds,cpointids);
            hcd.setCheckCmdList(checkCmdList);


            Long [] shellIds = manager.getShellIds(chkPointList);

            if(shellIds==null ||shellIds.length==0) {
                throw new SupperhcException("未获取到巡检点配置的巡检脚本 ");
            }
            List<CheckScriptBean> chkScriptList =  manager.getChkScriptList ( con ,shellIds,scIds,cpointids);
            hcd.setChkScriptList(chkScriptList);
            if(chkScriptList==null ||chkScriptList.isEmpty()) {
                throw new SupperhcException("未获取到巡检点配置的解析脚本");
            }
            Long [] chkItemIds = manager.getChkItemIds(comChkList);
            List<ChkItemBean> chkItemList = manager.getChkItemList(con,chkItemIds);
            hcd.setChkItemList(chkItemList);
            if(chkItemList==null ||chkItemList.isEmpty()) {
                throw new SupperhcException("未获取到巡检项");
            }
            hcd.setScids(scIds);
            /**  hcd.setPointids(pointids);*/
            hcd.setPointids(pointids);//改为查到哪些巡检点，就发送哪些巡检点

            /** if("imme".equals(startType)) {
             log.info("本次为立即巡检，故不需更改巡检项执行agent信息...");
             }else {
             //更新设备表agentId
             updateComPointAgent(con,comChkList,pointids,agent.getAgentId());
             }*/


            //封装一下当前系统下可用的集群serverip
            String osiServerList = manager.getOsiServerList(con);
            List<String> list = new ArrayList<String>();
            if(StringUtils.isNotBlank(osiServerList)) {
                String[] serverIpSplit = osiServerList.split(",");
                for(int i=0;i<serverIpSplit.length;i++) {
                    list.add(serverIpSplit[i]);
                }
            }

            String serverIp = Environment.getInstance().getServerIP();
            if(StringUtils.isNotBlank(serverIp)) {
                list.add(serverIp);
            }

            String serverIpslocal =  Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST, Environment.getInstance().getServerIP());
            if(StringUtils.isNotBlank(serverIpslocal)) {
                String[] serverIpslocalSplit = serverIpslocal.split(",");
                for(int i=0;i<serverIpslocalSplit.length;i++) {
                    if(StringUtils.isNotBlank(serverIpslocalSplit[i])) {
                        list.add(serverIpslocalSplit[i]);
                    }

                }
            }
            //list 去重
            String allIp = SupperHcToolUtil.removeDuplicateServer(list);


            hcd.setServerList(allIp);

            con.commit();
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e, method, log);
            log.error(method+ " is    error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } catch (IOException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_EXEC, e, method, log);
            log.error(method+ " is    error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } finally
        {
            DBResource.closeConnection(con, method, log);
        }
        return hcd;
    }

    public List<ChkPointBean> getChkPointList (Connection con , Long [] scIds, Long [] pointids, String startType, String startuser, String batchId) throws RepositoryException, SQLException
    {
        List<ChkPointBean> list = new ArrayList<ChkPointBean>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        /**String sql = "SELECT CPID, SCID, CHKPOINT, INTLHOUR, INTLMINUTE, INTLLENGTH, STARTLOGO, CRTUSER, CRTTIME, "
         + "UPUSER, UPTIME, ICOMPARERULE, IDEFAULTWARNLEVEL, IWARNLEVEL, ITHRESHOLD, ISHELLIID , ICHECKPOINTID ,ICORN,DYNPAR "
         + " FROM IEAI_CHKPOINT where ICHECKPOINTID > 0 and SCID in (" + StringUtils.join(scIds, ",") + ")";*/


        String sql = "SELECT a.CPID, a.SCID, a.CHKPOINT, a.INTLHOUR, a.INTLMINUTE, a.INTLLENGTH, a.STARTLOGO, a.CRTUSER, a.CRTTIME, "
                + "a.UPUSER, a.UPTIME, a.ICOMPARERULE, a.IDEFAULTWARNLEVEL, a.IWARNLEVEL, a.ITHRESHOLD, a.ISHELLIID , a.ICHECKPOINTID ,a.ICORN,a.DYNPAR, a.scripttag,a.scriptparam,"
                + " b.EQUIPVENDOR, b.EQUIPTYPE ,b.CMDINFO  "
                + " FROM IEAI_CHKPOINT a left join IEAI_CHECK_CMD b on a.ICHKCMDID = b.IID  where a.ICHECKPOINTID > 0 "
                /*+ " and a.SCID in (" + StringUtils.join(scIds, ",") + ")  "*/

                + " and (" + Conscommon.getInSql(scIds, "a.SCID", 1000)+")"
                 + " AND A.STARTLOGO=1 ";//添加只有启用的才发送
        if(pointids!=null && pointids.length>0) {
            /** sql = sql + " AND A.CPID IN ("+StringUtils.join(pointids, ",")+") ";*/

            sql = sql + " AND ("+Conscommon.getInSql(pointids, "A.CPID", 1000)+")";

        }


        String provInstanceConf = ServerEnv.getServerEnv().getScriptAgentConsumerScriptinstanceBeanId();
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {

            actStat = con.prepareStatement(sql);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                ChkPointBean info = new ChkPointBean();
                info.setCpid(actRS.getLong("CPID"));
                info.setScid(actRS.getLong("SCID"));
                info.setChkpoint(actRS.getString("CHKPOINT"));
                info.setIntlhour(actRS.getLong("INTLHOUR"));
                info.setIntlminute(actRS.getLong("INTLMINUTE"));
                info.setIntllength(actRS.getLong("INTLLENGTH"));
                info.setStartlogo(actRS.getInt("STARTLOGO"));
                info.setCrtuser(actRS.getString("CRTUSER"));
                info.setCrttime(actRS.getLong("CRTTIME"));
                info.setUpuser(actRS.getString("UPUSER"));
                info.setUptime(actRS.getLong("UPTIME"));
                info.setIcomparerule(actRS.getString("ICOMPARERULE"));
                info.setIdefaultwarnlevel(actRS.getString("IDEFAULTWARNLEVEL"));
                info.setIwarnlevel(actRS.getString("IWARNLEVEL"));
                info.setIthreshold(actRS.getString("ITHRESHOLD"));
                info.setIshelliid(actRS.getLong("ISHELLIID"));
                info.setCheckpointcfgId(actRS.getLong("ICHECKPOINTID"));
                info.setCron(actRS.getString("ICORN"));

                info.setScripttag(actRS.getString("SCRIPTTAG"));
                info.setScriptparam(actRS.getString("SCRIPTPARAM"));
                info.setIbatchid(batchId);

                //新增server和server的端口，解决server更改端口后下载接口等问题
                info.setReturnUrl(SupperHcConstants.getServerIpAndPort());

                // 封装巡检实际执行网络设备命令


                long equipvendor = actRS.getLong("EQUIPVENDOR");
                long equiptype = actRS.getLong("EQUIPTYPE");
                log.info("=============封装巡检点【巡检对象："+info.getChkpoint()+",巡检点ID："+info.getCpid()+",新巡检点ID:"+info.getCheckpointcfgId()+",巡检命令设备类型："+equiptype+"】==================");
                if(equiptype!=-1) {//如果是网络设备封装动态参数，非网络设备无需封装参数
                    String dynpar = actRS.getString("DYNPAR");
                    String proCmd = actRS.getString("CMDINFO");
                    String cmd = actRS.getString("CMDINFO");
                    // 判断命令中是否包含特殊符号“$”
                    if(proCmd!=null && proCmd.indexOf('$') != -1 && dynpar != null){
                        // 对动态参数进行分割
                        String[] dynparArr = dynpar.split("\n");
                        // 对符号“$”进行替换
                        for(int j = 1; j<=dynparArr.length; j++){
                            String oldStr = "$" + j;
                            String newStr = dynparArr[j-1];
                            cmd = cmd.replace( oldStr, newStr);
                        }
                    }
                    String cmdJson = this.getCustomCmdJson(equipvendor,equiptype,cmd);
                    info.setActcmdinfo(cmdJson);
                }

                if(!"imme".equals(startType)) {
                    //获取错峰信息
                    List<CheckIntervalConfigBean> checkIntervalConfigBeans = intervalConfigList(info.getCpid(), info.getScid(), con);
                    info.setIntervals(checkIntervalConfigBeans);
                }


                //增加启动人
                info.setStartuser(startuser);

                //provider形式返回巡检结果，或者拉起巡检时，是否调用指定provider
                info.setProvrefer(provInstanceConf);
                list.add(info);
            }
        } catch (SQLException e)
        {
            log.error(method +" is error       ! " + e.getMessage(),e);
            throw new  RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS( actRS, actStat, method, log);
        }

        return list;
    }

    public List<CheckIntervalConfigBean> intervalConfigList (   Long icpid,Long scid,  Connection con)
            throws RepositoryException
    {
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        List<CheckIntervalConfigBean> list = new ArrayList<CheckIntervalConfigBean>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT distinct T.* FROM HD_CHECK_TIME_INTERVAL_CONFIG T,ieai_com_chk ck,IEAI_CHKPOINT P ");
        sb.append(" WHERE ck.scid=p.scid and p.cpid=T.CPID and ck.cpid=T.DEVID and ck.chkitemid=T.CIID ");
        sb.append(" AND T.CPID = ? and ck.scid=? ");
        sb.append(" ORDER BY T.START_TIME ");
        try
        {
            actStat = con.prepareStatement(sb.toString());
            actStat.setLong(1, icpid);
            actStat.setLong(2, scid);
            actRS = actStat.executeQuery();
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            while (actRS.next())
            {
                CheckIntervalConfigBean intervalConfigBean = new CheckIntervalConfigBean();
                intervalConfigBean.setCfgid(actRS.getLong("CFGID"));
                intervalConfigBean.setDevid(actRS.getLong("DEVID"));
                intervalConfigBean.setCiid(actRS.getLong("CIID"));
                intervalConfigBean.setCpid(actRS.getLong("CPID"));
                intervalConfigBean.setCycletype(actRS.getInt("CYCLE_TYPE"));
                intervalConfigBean.setEnabledflag(actRS.getInt("ENABLED"));
                intervalConfigBean.setEndDay(actRS.getInt("END_DAY"));


                intervalConfigBean.setSetEnd(actRS.getInt("SET_END"));
                intervalConfigBean.setSetStart(actRS.getInt("SET_START"));
                intervalConfigBean.setStartDay(actRS.getInt("START_DAY"));
                intervalConfigBean.setStartTime(sdf.format(actRS.getTimestamp("START_TIME")));
                intervalConfigBean.setEndTime(sdf.format(actRS.getTimestamp("END_TIME")));

                list.add(intervalConfigBean);
            }
        } catch (SQLException e)
        {
            log.error("intervalConfigList is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, "intervalConfigList", log);
        }
        return list;
    }

    public String getCustomCmdJson(long equipvendor ,long equiptype,String customCmd){
        String cmdStringJson = "";
        if( customCmd==null) {
            customCmd = "";
        }
        String [] cmdArr = customCmd.split("\n");
        List<CmdJsonBean> jsonList = new ArrayList<CmdJsonBean>();
        EquipCharModel equipCharModel = this.getSpecialCharConfig( equipvendor , equiptype);
        if(null != equipCharModel ){
            List<EquipCharFormModel> endList = equipCharModel.getEndList();
            List<EquipCharFormModel> noConfigList  = equipCharModel.getNoConfigList();
            String successList = equipCharModel.getSuccessList();
            if(null!=successList){
                buildJsonList(cmdArr, equipCharModel, noConfigList, endList, jsonList);
            }
        }
        if(!jsonList.isEmpty()){
            cmdStringJson = JSON.toJSONString(jsonList);
        }else {
            cmdStringJson = customCmd;
        }
        return cmdStringJson;
    }

    private void buildJsonList(String [] cmdArr, EquipCharModel equipCharModel,List<EquipCharFormModel> noConfigList,List<EquipCharFormModel> endList,List<CmdJsonBean> jsonList) {
        for (int i = 0; i < cmdArr.length; i++)
        {
            //只保留一个空格
            String customCmdRplace = cmdArr[i].replaceAll("\\s{1,}", " ").trim();
            CmdJsonBean jsonBean = new CmdJsonBean();
            jsonBean.setOrder(Integer.toString(i+1));
            jsonBean.setNetcmd(customCmdRplace);
            jsonBean.setCmdtype("0");
            jsonBean.setCmdend(equipCharModel.getSuccessList());
            //匹配非配置
            if(noConfigList!=null && !noConfigList.isEmpty()) {
                buildJsonListInner(noConfigList, customCmdRplace, jsonBean);
            }
            if(null!=endList && !endList.isEmpty() ){
                for(EquipCharFormModel end : endList){
                    if(customCmdRplace.equals(end.getEndCmd())){
                        jsonBean.setCmdend(end.getEndChar());
                        break;
                    }
                }
            }
            jsonList.add(jsonBean);
        }
    }

    private void buildJsonListInner(List<EquipCharFormModel> noConfigList,String customCmdRplace,CmdJsonBean jsonBean) {
        for(EquipCharFormModel noConfig : noConfigList){
            int customCmdRplaceLen = customCmdRplace.length();
            String noConfigLower = noConfig.getNoConfig().toLowerCase();
            int len = noConfigLower.length();
            if(len > 0 && customCmdRplaceLen >= len && customCmdRplace.toLowerCase().substring(0, len).contains(noConfigLower))
            {
                jsonBean.setCmdtype("1");
                break;
            }
        }
    }

    /**
     *
     * @Title: getComChkList
     * @Description: 获取巡检配置数据
     * @param con
     * @param cpIds
     * @param cpGroupId
     * @return
     * @throws RepositoryException
     * @throws SQLException
     * @author: 77
     * @date:   2019年11月21日 下午1:41:30
     */
    public List<ComChkBean> getComChkList (Connection con ,Long [] cpIds,Long [] scids, Long [] itemids, Long [] pointids,long cpGroupId) throws RepositoryException
    {
        List<ComChkBean> list = new ArrayList<ComChkBean>();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();

        StringBuilder sql = new StringBuilder();
        sql.append("SELECT DISTINCT C.SCID, C.CPID,C.CHKITEMID, C.PERFORMUSER, C.APPLOGO,C.STARTLOGO,C.CRTUSER,C.CRTTIME, C.UPUSER,C.UPTIME,C.IHIDDEN,C.IGROUPIID, C.IPROJECTID ");
        sql.append(" FROM IEAI_COM_CHK C,IEAI_CHKPOINT P WHERE 1=1 ");
        sql.append(" and (" + Conscommon.getInSql(cpIds, "C.CPID", 1000) + ") ");

        if (cpGroupId > 0) {
            sql.append(" AND (C.APPLOGO =? OR C.APPLOGO = -1)");
        }
        sql.append(" AND C.STARTLOGO = 1 ");
        sql.append(" AND P.STARTLOGO = 1 ");
        sql.append(" AND C.SCID=P.SCID ");
        sql.append(" AND P.ICHECKPOINTID>-1 ");
        if(scids!=null && scids.length>0) {
            sql.append("  AND (").append(Conscommon.getInSql(scids, "C.SCID", 1000)).append(")");
        }
        if(itemids!=null && itemids.length>0) {
            sql.append("  AND (").append(Conscommon.getInSql(itemids, "C.CHKITEMID", 1000)).append(")");
        }
        if(pointids!=null  && pointids.length>0) {
            sql.append("  AND (").append(Conscommon.getInSql(pointids, "P.CPID", 1000)).append(")");
        }

        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(sql.toString());
            if (cpGroupId > 0) {
                actStat.setLong(1, cpGroupId);
            }
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                ComChkBean info = new ComChkBean();
                info.setScid(actRS.getLong("SCID"));
                info.setCpid(actRS.getLong("CPID"));
                info.setChkitemid(actRS.getLong("CHKITEMID"));
                info.setPerformuser(actRS.getString("PERFORMUSER"));
                info.setApplogo(actRS.getLong("APPLOGO"));
                info.setStartlogo(actRS.getInt("STARTLOGO"));
                info.setCrtuser(actRS.getString("CRTUSER"));
                info.setCrttime(actRS.getLong("CRTTIME"));
                info.setUpuser(actRS.getString("UPUSER"));
                info.setUptime(actRS.getLong("UPTIME"));
                info.setIhidden(actRS.getInt("IHIDDEN"));
                info.setIgroupiid(actRS.getLong("IGROUPIID"));
                info.setIprojectid(actRS.getLong("IPROJECTID"));
                list.add(info);
            }
        } catch (SQLException e)
        {
            log.error(method +" is   error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        } finally
        {
            DBResource.closePSRS( actRS, actStat, method, log);
        }

        return list;
    }

    public void hcStart ( String cpGroupId,Long[] cpIds,Long[] scids,Long[] itemids,Long[] pointids, int systype, String type ,Map<String, Object> context,String startType, String batchId)
            throws UnknownHostException, SupperhcException, NumberFormatException, RepositoryException
    {
        List<ComputerBean> netbeans = new ArrayList<ComputerBean>();
        List<ComputerBean> serverbeans = new ArrayList<ComputerBean>();
        List<ComputerBean> combeans = null;
        combeans = getClassicCps( Long.parseLong(cpGroupId), cpIds, scids, itemids, pointids, type, systype);

        setServeIpForComputerBeans(combeans, systype);

        // 获取设备，并分堆出网络设备和服务器设备
        diffEquipType(combeans, netbeans, serverbeans);

        //释放内存
        combeans.clear();
        combeans=null;

        if(!netbeans.isEmpty()) {
            // 网络设备巡检启动
            hcNetStart(netbeans, cpGroupId, systype, type, scids, itemids, pointids,context,startType, batchId);
        }

        if(!serverbeans.isEmpty()) {
            // 服务器设备巡检启动
            hcServerStart(serverbeans, systype, cpGroupId, scids, itemids, pointids,context,startType, batchId);
        }

        // 网络设备调用网络设备启动方法

        // 服务器设备调用服务器设备启动方法

        // 启动结果信息汇总
        String getOutResult = "检查点启动成功数量:" + context.get(SUCCNUM);
        getOutResult = getOutResult + "<br>检查点启动失败数量:" + context.get(FAILNUM);
        getOutResult = getOutResult + "<br>失败原因:" + context.get(FAILCAUSE).toString();
        context.put("detailInfo", getOutResult);
    }

    /**
     *
     * @Title: setServeIpForComputerBeans
     * @Description: 对巡检设备绑定返回接收巡检结果的服务器端ip
     * @param combeans
     * @param systype
     * @throws UnknownHostException
     * @author: 77
     * @date:   2019年11月21日 上午10:45:40
     */
    public void setServeIpForComputerBeans ( List<ComputerBean> combeans, int systype ) throws UnknownHostException
    {
        // 如果只有一台设备巡检，那用当前severip即可
        if (combeans != null && combeans.size() == 1)
        {
            String serverIp = Environment.getInstance().getServerIP();
            String serverIps =  Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST, Environment.getInstance().getServerIP());
            if(StringUtils.isBlank(serverIp)) {
                if(serverIps!=null ) {
                    String[] serverIpSplit= serverIps.split(",");
                    serverIp = serverIpSplit[SupperHcToolUtil.randomServerIndex(0,serverIpSplit.length)];
                }
            }

            for (ComputerBean bean : combeans)
            {

                bean.setServerIp(serverIp);
            }
        } else
        {
            // 多台采用负载机制
            String serverIps = getOsiServerList(systype);
            String[] serverIpSplit = serverIps.split(",");
            List<String> list = new ArrayList<String>();
            for(int i=0;i<serverIpSplit.length;i++) {
                list.add(serverIpSplit[i]);
            }

            String serverIp = Environment.getInstance().getServerIP();
            if(StringUtils.isNotBlank(serverIp)) {
                list.add(serverIp);
            }

            String serverIpslocal =  Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST, Environment.getInstance().getServerIP());
            if(StringUtils.isNotBlank(serverIpslocal)) {
                String[] serverIpslocalSplit = serverIpslocal.split(",");
                for(int i=0;i<serverIpslocalSplit.length;i++) {
                    list.add(serverIpslocalSplit[i]);
                }
            }
            //list 去重
            String allIp = SupperHcToolUtil.removeDuplicateServer(list);
            // 首先给设备分配serverIp
            if (StringUtils.isNotBlank(allIp))
            {
                setComputerServerIp(allIp, combeans);
            }
        }
    }

    private void setComputerServerIp ( String serverIps, List<ComputerBean> combeans )
    {
        String[] serverIpArr = serverIps.split(",");
        List<List<ComputerBean>> computerAvgList = ListUtils.averageAssign(combeans, serverIpArr.length);

        for (int i = 0; i < serverIpArr.length; i++)
        {
            List<ComputerBean> cpAvgList = computerAvgList.get(i);
            if (!cpAvgList.isEmpty())
            {
                for (ComputerBean computerBean : cpAvgList)
                {
                    computerBean.setServerIp(serverIpArr[SupperHcToolUtil.randomServerIndex(0, serverIpArr.length)]);
                    log.info("发往Agent的巡检设备:"+computerBean.getIp()+"分配返回巡检结果主Server地址："+computerBean.getServerIp());
                }
            }
        }
    }

    public void diffEquipType ( List<ComputerBean> combeans, List<ComputerBean> netbeans,
                                List<ComputerBean> serverbeans )
    {
        if (combeans != null && !combeans.isEmpty())
        {
            for (ComputerBean bean : combeans)
            {
                if (bean.getEqtype() != null && bean.getEqtype() == 1)
                {
                    netbeans.add(bean);
                } else
                {
                    serverbeans.add(bean);
                }
            }
        }
    }

    /**
     *
     * @Title: getOsiServerList
     * @Description: 获取开启巡检功能的entegorserver
     * @param systype
     * @return
     * @author: 77
     * @date:   2019年11月21日 上午10:44:48
     */
    public String getOsiServerList ( int systype )
    {
        String serverIps = null;
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection("getOsiServerList()", log, systype);
            HcStartManager manager = new HcStartManager();

            serverIps = manager.getOsiServerList(conn);

        } catch (RepositoryException e)
        {
            log.error("获取entegor集群server出错", e);
        } finally
        {
            DBResource.closeConnection(conn, "getOsiServerList()", log);
        }
        return serverIps;

    }

    public List<ComputerBean> getClassicCps ( long groupid,Long[] cpids,Long[] scids,Long[] itemids,Long[] pointids, String type, int systype ) throws RepositoryException
    {
        Connection conn = null;
        List<ComputerBean> combeans = new ArrayList<ComputerBean>();
        try
        {
            conn = DBResource.getConnection("getClassicCps", log, systype);
            combeans = this.getComputers(conn, groupid, cpids,scids, itemids, pointids, type);
        } catch (RepositoryException e)
        {
            log.error("获取设备集合出错", e);
            throw e;
        } finally
        {
            DBResource.closeConnection(conn, "getClassicCps", log);
        }

        return combeans;
    }

    public List<ComputerBean> getComputers(Connection conn,long gid,Long[] cpids,Long[] scids,Long[] itemids,Long[] pointids, String type) throws RepositoryException{
        List<ComputerBean> cbean = new ArrayList<ComputerBean>();
        StringBuilder sb = new StringBuilder();

        sb.append(" select ceb.*,  a.iagent_ip,a.iagent_port,a.iagent_webport");
        sb.append("  from ( ");
        sb.append("         select ce.cpid,ce.ip,ce.cpport,ce.iagentinfo_id,ce.eqtype,ce.cpname,ce.cpuser,ce.cppasswd,ce.ILOWPERMIT_USER,ce.IEQUTYPE_ID,ce.IMANUFACTORY_ID,ce.ILOWPERMIT_ACSKEY,ce.IPROTLCOL,ce.ILOWPERMIT_USER_ALIAS,");
        sb.append("         B.IIP FORTKEYIP,B.IUSER FORTKEYUSER ");
        sb.append("  from ( ");
        sb.append(" select c.cpid, c.ip,c.cpport,c.iagentinfo_id,c.eqtype,c.cpname, c.cpuser,c.cppasswd,");
        sb.append("  E.ILOWPERMIT_USER,E.IEQUTYPE_ID,E.IMANUFACTORY_ID, E.ILOWPERMIT_ACSKEY, E.IPROTLCOL,E.ILOWPERMIT_USER_ALIAS ");
        sb.append(" from ieai_computer_list c left join IEAI_EQUIPMENT E on c.cpid = e.icpid ");
        sb.append(" where   ");

        sb.append(" c.cpid in (");
        sb.append(" SELECT DISTINCT CL.CPID FROM IEAI_COMPUTER_LIST CL,IEAI_SYS_RELATION R,IEAI_COM_CHK CC,IEAI_CHKPOINT P ,IEAI_CHKITEM T WHERE 1=1 ");
        sb.append(" AND CC.CHKITEMID = T.ICHKITEMID AND CC.SCID = P.SCID AND CC.CPID = R.COMPUTERID AND CC.CPID = CL.CPID AND (CC.APPLOGO = R.SYSTEMID OR CC.APPLOGO = -1) ");

        // 业务系统ID
        if(gid>0) {
            sb.append( " AND R.SYSTEMID = '" + gid ).append("' ");
        }
        // 设备ID
        if(cpids!=null && cpids.length>0) {
            sb.append("AND (") .append(Conscommon.getInSql(cpids, "CL.CPID", 1000)).append(" ) ");
        }
        // 巡检项配置ID
        if(scids!=null && scids.length>0) {
            sb.append("AND (") .append(Conscommon.getInSql(scids, "CC.SCID", 1000)).append(" ) ");
        }
        // 巡检项ID
        if(itemids!=null && itemids.length>0) {
            sb.append("AND (") .append(Conscommon.getInSql(itemids, "CC.CHECKITEMID", 1000)).append(" ) ");
        }
        // 巡检点ID
        if(pointids!=null && pointids.length>0) {
            sb.append("AND (") .append(Conscommon.getInSql(pointids, "P.CPID", 1000)).append(" ) ");
        }
        sb.append( " )" );

        sb.append(" ) ce ");
        sb.append("  left join IEAI_FORTKEY B ");
        sb.append("  on ce.ILOWPERMIT_USER_ALIAS = B.IALIAS) ceb ");
        sb.append(" left join ieai_agentinfo a ");
        sb.append(" on ceb.iagentinfo_id = a.iagentinfo_id ");

        PreparedStatement ps = null;
        ResultSet rs = null;

        try
        {
            ps = conn.prepareStatement(sb.toString());
            rs =ps.executeQuery();
            while(rs.next()) {
                ComputerBean bean = new ComputerBean();
                bean.setCpid(rs.getLong("CPID"));
                bean.setCpname(rs.getString("CPNAME"));
                bean.setCpport(rs.getInt("CPPORT"));
                bean.setCpuser(rs.getString("ILOWPERMIT_USER"));
                bean.setCppasswd(rs.getString("ILOWPERMIT_ACSKEY"));
                bean.setProtocol(rs.getString("IPROTLCOL"));
                bean.setAgentId(rs.getLong("iagentinfo_id"));
                bean.setEqtype(rs.getLong("EQTYPE"));
                bean.setCpGroupId(gid);
                bean.setIp(rs.getString("IP"));
                bean.setServerIp(bean.getIp());
                bean.setAgentIp(rs.getString("iagent_ip"));
                bean.setAgentPort(rs.getInt("iagent_port"));
                bean.setAgentWebPort(rs.getInt("iagent_webport")==0?SupperHcConstants.HC_CHECK_AGENT_WEBPORT_DEFAULT:rs.getInt("iagent_webport"));

                if(bean.getEqtype()!=null && bean.getEqtype()==1) {
                    EquipCharModel equipCharModel = this.getSpecialCharConfig( rs.getLong("IMANUFACTORY_ID"),rs.getLong("IEQUTYPE_ID"));

                    String lower = rs.getString("ILOWPERMIT_USER_ALIAS");
                    String forKeyIip = rs.getString("IP");
                    String forKeyUser = rs.getString("ILOWPERMIT_USER");
                    if(StringUtils.isNotBlank(lower)&&lower!=null){
                        UserAndIpModel sumodel = FortKeyManager.getInstance().getUserAndIpByAlias(lower);
                        if(sumodel!=null){
                            if (StringUtils.isNotBlank(sumodel.getIp())&&sumodel.getIp()!=null)
                            {
                                forKeyIip = sumodel.getIp();
                            }
                            if(StringUtils.isNotBlank(sumodel.getUser())&&sumodel.getIp()!=null){

                                forKeyUser = sumodel.getUser();
                            }
                        }

                    }
                    bean.setFortkeyip(forKeyIip);
                    bean.setFortkeyuser(forKeyUser);

                    if(equipCharModel!=null) {
                        bean.setExpectCmd(equipCharModel.getSpecialChar());
                        bean.setEndstring( equipCharModel.getSuccessList());
                    }else {
                        log.info("网络设备["+bean.getIp()+"]未配置结束 符");
                    }
                }

                //缺啥加啥
                cbean.add(bean);
            }

        } catch (SQLException e)
        {
            log.error("根据组获取设备信息出错", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY,e);
        }finally {
            DBResource.closePSRS(rs, ps, "HcStartManager.getComputersByGroup", log);
        }
        return cbean;

    }

    public EquipCharModel getSpecialCharConfig ( long equipvendor ,long equiptype)
    {
        Connection conn = null;
        EquipCharModel query = new EquipCharModel();
        try
        {
            QueryRunner runner = new QueryRunner();
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_HEALTH_INSPECTION);

            if(equiptype==-1) {
                query = runner.query(conn,
                        "SELECT a.* FROM IEAI_EQUIPCHAR a where  a.EQUIPTYPE ="+equiptype ,
                        new BeanHandler<EquipCharModel>(EquipCharModel.class));
            }else {
                query = runner.query(conn,
                        "SELECT a.* FROM IEAI_EQUIPCHAR a where a.VENDOR ="+equipvendor+"  and a.EQUIPTYPE ="+equiptype ,
                        new BeanHandler<EquipCharModel>(EquipCharModel.class));
            }

            if (null != query)
            {
                if (StringUtils.isNotBlank(query.getSuccessChar()))
                {
                    List<EquipCharFormModel> listModelByJson = JsonUtil
                            .getListModelByJson(query.getSuccessChar(), EquipCharFormModel.class);
                    StringBuilder sb = new StringBuilder();
                    for (EquipCharFormModel mm : listModelByJson)
                    {
                        sb.append(mm.getSuccess());
                        sb.append(";");
                    }
                    query.setSuccessList(
                            StringUtils.substring(sb.toString(), 0, sb.toString().length() - 1));
                }
                if (StringUtils.isNotBlank(query.getEndChar()))
                {
                    query.setEndList(
                            JsonUtil.getListModelByJson(query.getEndChar(), EquipCharFormModel.class));
                }
                if (StringUtils.isNotBlank(query.getNoConfigChar()))
                {
                    query.setNoConfigList(JsonUtil.getListModelByJson(query.getNoConfigChar(),
                            EquipCharFormModel.class));
                }
            }

        } catch (Exception e)
        {
            log.error("EquipCharManage.getSpecialCharConfig is error", e);
        } finally
        {
            try
            {
                DbUtils.close(conn);
            } catch (SQLException e)
            {
                log.error("EquipCharManage.getSpecialCharConfig is error", e);
            }
        }
        return query;
    }

    /**
     *
     * @Title: hcNetStart
     * @Description: 网络设备巡检启动
     * @param netbeans
     * @param cpGroupId
     * @author: 77
     * @throws SupperhcException
     * @throws NumberFormatException
     * @date:   2019年11月21日 下午1:02:10
     */
    public void hcNetStart ( List<ComputerBean> netbeans, String cpGroupId, int systype, String type, Long[] scids, Long[] itemids, Long [] pointids,Map<String, Object> context,String startType, String batchId )
            throws SupperhcException
    {

        long gid = Long.parseLong(cpGroupId);
        List<AgentInfoModel> agentList = null;
        agentList = this.getAgentList(gid, systype, type);
        valiate(agentList);
        if (null != agentList && !agentList.isEmpty())
        {
            List<List<ComputerBean>> computerAvgList = ListUtils.averageAssign(netbeans, agentList.size());
            HcStartManager manager = new HcStartManager();

            for (int i = 0; i < agentList.size(); i++)
            {
                List<ComputerBean> cpAvgList = computerAvgList.get(i);
                if (!cpAvgList.isEmpty())
                {
                    for (ComputerBean computerBean : cpAvgList)
                    {
                        setAgentForNet(computerBean, agentList.get(i));
                    }
                    startNetToAgent(agentList.get(i), cpAvgList, scids, itemids, pointids, cpGroupId, systype, context, manager,startType, batchId);
                }
            }
        }else {
            throw new SupperhcException("此系统未绑定到设备组或者设备组未与agent组关联，网络设备无法获取agent");
        }

    }

    /**
     *
     * @Title: getAgentList
     * @Description: 获取组的agent
     * @param gid
     * @param systype
     * @return
     * @author: 77
     * @date:   2019年11月21日 下午1:29:53
     */
    public List<AgentInfoModel> getAgentList ( long gid, int systype, String type )
    {
        Connection conn = null;
        HcStartManager manager = new HcStartManager();
        List<AgentInfoModel> list = null;
        try
        {
            conn = DBResource.getConnection("getAgentList", log, systype);
            list = manager.getAgentList(conn, gid, type);
        } catch (RepositoryException e)
        {
            log.error("获取组绑定的agent组失败", e);
        } catch (Exception e)
        {
            log.error("获取组绑定的agent组失败;" + e.getMessage(), e);
        } finally
        {
            DBResource.closeConnection(conn, "getAgentList", log);
        }
        return list;
    }

    public void valiate ( List<AgentInfoModel> agentList ) throws SupperhcException
    {
        for (AgentInfoModel agentInfo : agentList)
        {
            if (StringUtils.isBlank(agentInfo.getHttpPort()))
            {
                throw new SupperhcException("Agent:" + agentInfo.getAgentIp() + "没有配置WEB端口");
            }
        }
    }

    private void setAgentForNet ( ComputerBean computerBean, AgentInfoModel agent )
    {
        // 如果网络设备自身配置了agent用自身agent不采用agent组内agent
        if (computerBean.getAgentId() == null || computerBean.getAgentId() <= 0)
        {
            computerBean.setAgentId(agent.getAgentId());
            if (computerBean.getAgentPort() == null || computerBean.getAgentPort() <= 0)
            {
                computerBean.setAgentPort(agent.getAgentPort() == null || "".equals(agent.getAgentPort()) ? 0
                        : Integer.parseInt(agent.getAgentPort()));
            }
            computerBean.setAgentWebPort(agent.getHttpPort() == null || "".equals(agent.getHttpPort()) ? 0
                    : Integer.parseInt(agent.getHttpPort()));
        }

    }

    public void startNetToAgent(AgentInfoModel agent,List<ComputerBean> cplist,Long[] scids, Long[] itemids, Long [] pointids,String cpGroupId,int systype,Map<String, Object> sendMsg,HcStartManager manager,String startType, String batchId) throws SupperhcException {
        HealthCheckData hcd = null;

        try
        {
//            log.info("分配的启动巡检：agentId：" + agent.getAgentId() + ",agentIp:" + agent.getAgentIp() + ",agentPort:"+ agent.getAgentPort());
            Integer succNum = 0;
            Integer failNum = 0;
            Set<String> failCause = new HashSet<String>();
            Set<String> failCausedownload = new HashSet<String>();
            // 获取成功数量
            if(sendMsg.containsKey(SUCCNUM)){
                succNum = (Integer) sendMsg.get(SUCCNUM);
            } else {
                sendMsg.put(SUCCNUM, succNum);
            }
            // 获取失败数量
            if(sendMsg.containsKey(FAILNUM)){
                failNum = (Integer) sendMsg.get(FAILNUM);
            } else {
                sendMsg.put(FAILNUM, failNum);
            }
            // 获取失败原因
            if(sendMsg.containsKey(FAILCAUSE)){
                failCause =  (Set<String>) sendMsg.get(FAILCAUSE);
            } else {
                sendMsg.put(FAILCAUSE, failCause);
            }

            //获取本次启动未能成功的设备ip地址和原因
            if(sendMsg.containsKey(FAILCAUSEDOWNLOAD)){
                failCausedownload =  (Set<String>) sendMsg.get(FAILCAUSEDOWNLOAD);
            } else {
                sendMsg.put(FAILCAUSEDOWNLOAD, failCausedownload);
            }

            String startuser = SupperHcToolUtil.getStartHcUser(sendMsg.get("startuser"));

            //验证是否开启http形式发送
            boolean hchttpSendFlag = ServerEnv.getInstance().getBooleanConfig("hc.httpserver.flag", false);


            for(ComputerBean bean:cplist) {

                //获取一下是否该设备已经启动过巡检，如果启动过，使用当时的agent进行调用
                manager.getAlreadyStartAgentOfCp(bean);
                log.info("启动巡检：agentId：" + agent.getAgentId() + ",agentIp:" + agent.getAgentIp() + ",agentPort:"+ agent.getAgentPort());

                List<ComputerBean> comList = new ArrayList<ComputerBean>();
                comList.add(bean);



                String token = "";
                if(hchttpSendFlag) {
                    String url = "http://" + agent.getAgentIp() + ":" + agent.getHttpPort() + "/restfulapi";
                    token = HcSendHttp.getInstance().getTokenFromHttpServer(url);
                }else {
                    //xmlrpc
                    token = HcSendXmlRpc.getInstance().getXmlRpcToken();
                }


/**             String sendres = sendNetToAgent(startHcUrl, agent, comList, scids, pointids, Long.parseLong(cpGroupId), systype, manager, startType);*/
                String sendres = sendNetToAgent( agent, comList, scids, itemids, pointids, Long.parseLong(cpGroupId), systype, manager, startType,token,startuser, batchId);
                if("".equals(sendres)) {
                    succNum++;
                }else {
                    failNum++;
                    failCause.add(sendres);
                    failCausedownload.add(bean.getIp());
                }
                sendMsg.put(SUCCNUM, succNum);
                sendMsg.put(FAILNUM, failNum);
                Thread.sleep(100);
            }

        } catch (ClientProtocolException e)
        {
            log.error(e);
            throw new SupperhcException("agent连接失败:" + agent.getAgentIp());
        } catch (IOException e)
        {
            log.error(e);
            throw new SupperhcException("agent连接失败:" + agent.getAgentIp());
        } catch (Exception e)
        {
            log.error(e);
            throw new SupperhcException(e);
        }

    }

    public String sendNetToAgent(AgentInfoModel agent,List<ComputerBean> beans,Long[] scids, Long[] itemids, Long [] pointids,long cpGroupId,int systype,HcStartManager manager,String startType,String token,String startuser, String batchId) {
        HealthCheckData hcd = null;
        String sendExceptions = "";
        boolean isException = false;
        InspectionTaskManager inspectionTaskManager = new InspectionTaskManager();
        try
        {
            hcd = inspectionTaskManager.getHCData(agent, beans, scids, itemids, pointids, cpGroupId, systype,startType,startuser, manager, batchId);
            hcd.setBatchId(batchId);
            if (hcd != null)
            {
                String jsonData = JSON.toJSONString(hcd);
                log.info(jsonData);

                Map<String, Object> resMap = null;
                String methodname = "";
                if(startType!=null && !"".equals(startType) && "imme".equals(startType)) {
                    methodname = "immhc";
                }else {
                    methodname = "addhc";
                }
                //验证是否开启xmlrpc形式发送
                boolean hchttpSendFlag = ServerEnv.getInstance().getBooleanConfig("hc.httpserver.flag", false);
                if(hchttpSendFlag) {
                    resMap = HcSendHttp.getInstance().sendHttpServer(agent.getAgentIp(), Integer.parseInt(agent.getHttpPort()), methodname, jsonData,token,null);
                }else {
                    //xmlrpc
                    resMap =  HcSendXmlRpc.getInstance().sendXmlRpcForHc(agent.getAgentIp(), Integer.parseInt(agent.getAgentPort()), methodname, jsonData, token);
                }

                if(resMap==null) {
                    sendExceptions="发送操作返回为空！";
                    isException = true;
                }else {
                    if(resMap.get(SupperHcConstants.RES_SUCC)==null|| !(Boolean)(resMap.get(SupperHcConstants.RES_SUCC))) {
                        sendExceptions=resMap.get(SupperHcConstants.RES_MSG)==null?"":resMap.get(SupperHcConstants.RES_MSG).toString();
                        isException = true;
                    }else {
                        isException = false;
                    }
                }

            }else {
                sendExceptions="未获取到需要发送的数据信息！";
                isException = true;
            }

        }catch (RepositoryException e)
        {
            log.error(e.getMessage(), e);
            sendExceptions=e.getMessage();
            isException = true;
        } catch (SupperhcException e)
        {
            log.error(e.getMessage(), e);
            sendExceptions=e.getMessage();
            isException = true;
        }
        try
        {
            if(!isException) {
                if(!"imme".equals(startType)) {
                    /**  manager.updateComPointHcStatus(hcd.getComChkList(), 1,hcd.getPointids());*/
                    manager.updateComPointHcStatus2(hcd.getComChkList(),agent.getAgentId() ,1,pointids,startuser);
                }
                //如果是立即巡检记录下启动人和启动时间
                else if("imme".equals(startType)) {
                    manager.updateComPointHcStatus2OfImme(hcd.getComChkList(),hcd.getPointids(),startuser);
                    log.info("立即巡检不进行状态变更！");
                }

                // 插入批次数据

            }

            /**
             else {
             if(!"imme".equals(startType) && hcd!=null) {
             manager.updateComPointHcStatusToStop(hcd.getComChkList(),pointids);
             }
             }*/

        } catch (RepositoryException e)
        {
            log.error("巡检启动出现异常，更新启动状态为失败出错", e);
            sendExceptions = "，更新启动状态出错";
        }finally {
            //释放大对象
            hcd = null;
        }
        return sendExceptions;
    }

    public void hcServerStart ( List<ComputerBean> serverbeans, int systype, String cpGroupId, Long[] scids,  Long[] itemids, Long [] pointids,Map<String, Object> context,String startType, String batchId )
            throws SupperhcException
    {
        Integer succNum = 0;
        Integer failNum = 0;
        Set<String> failCause = new HashSet<String>();
        Set<String> failCausedownload = new HashSet<String>();
        String startuser = SupperHcToolUtil.getStartHcUser(context.get("startuser"));

        // 获取成功数量
        if(context.containsKey(SUCCNUM)){
            succNum = (Integer) context.get(SUCCNUM);
        } else {
            context.put(SUCCNUM, succNum);
        }
        // 获取失败数量
        if(context.containsKey(FAILNUM)){
            failNum = (Integer) context.get(FAILNUM);
        } else {
            context.put(FAILNUM, failNum);
        }
        // 获取失败原因
        if(context.containsKey(FAILCAUSE)){
            failCause =  (Set<String>) context.get(FAILCAUSE);
        } else {
            context.put(FAILCAUSE, failCause);
        }

        //获取本次启动未能成功的设备ip地址和原因
        if(context.containsKey(FAILCAUSEDOWNLOAD)){
            failCausedownload =  (Set<String>) context.get(FAILCAUSEDOWNLOAD);
        } else {
            context.put(FAILCAUSEDOWNLOAD, failCausedownload);
        }


        InspectionTaskManager inspectionTaskManager = new InspectionTaskManager();
        for (ComputerBean computerBean : serverbeans)
        {
            List<ComputerBean> beans = new ArrayList<ComputerBean>();
            beans.add(computerBean);

            AgentInfoModel agent = new AgentInfoModel();
            agent.setAgentId(computerBean.getAgentId());
            agent.setAgentIp(computerBean.getAgentIp());
            agent.setAgentPort(computerBean.getAgentPort() + "");
            agent.setHttpPort(computerBean.getAgentWebPort() + "");


            HcStartManager manager = new HcStartManager();
            HealthCheckData hcd = null;
            try
            {
                hcd = inspectionTaskManager.getHCData(agent, beans, scids, itemids, pointids, Long.parseLong(cpGroupId), systype,startType,startuser, manager, batchId);
                hcd.setBatchId(batchId);
                if (hcd != null && checkAgentForServer(agent, computerBean))
                {
                    startToAgent(agent, hcd, manager,startType,startuser);
                    succNum++;
                }else {
                    failNum++;
                    failCause.add("未获取到需要发送 给Agent【"+computerBean.getAgentIp()+"】的数据。");
                }

            } catch (Exception e)
            {
                log.error(e.getMessage(), e);
                failNum++;
                failCause.add(e.getMessage());
                failCausedownload.add(computerBean.getIp());
                try
                {
                    if(hcd!=null && !"imme".equals(startType)) {
                        /**   manager.updateComHcStatusToStop(hcd.getComChkList());*/
//                        manager.updateComPointHcStatusToStop(hcd.getComChkList(),pointids);
                    }
                } catch (Exception e1)
                {
                    log.error("巡检启动出现异常，更新启动状态为失败出错", e);
                }
            }

            context.put(SUCCNUM, succNum);
            context.put(FAILNUM, failNum);
            try
            {
                Thread.sleep(100);
            } catch (InterruptedException e)
            {
                log.error(e.getMessage(), e);
            }

            //释放资源
            agent = null;
            beans.clear();
            beans=null;

        }
        // 将设备列表启动

    }

    private boolean checkAgentForServer ( AgentInfoModel agent, ComputerBean computerBean )
    {
        boolean can = true;
        if (agent == null)
        {
            log.error("设备【" + computerBean.getIp() + "】未获取到Agent信息无法继续巡检。。。");
            return false;
        }
        if (agent.getAgentIp() == null || "".equals(agent.getAgentIp()))
        {
            log.error("设备【" + computerBean.getIp() + "】未获取到Agent的IP无法继续巡检。。。");
            return false;
        }
        //验证是否开启http形式发送
        boolean hchttpSendFlag = ServerEnv.getInstance().getBooleanConfig("hc.httpserver.flag", false);
        if(hchttpSendFlag) {
            if (agent.getHttpPort() == null || "".equals(agent.getHttpPort()) || Long.parseLong(agent.getHttpPort()) <= 0)
            {
                log.error("设备【" + computerBean.getIp() + "】未获取到Agent的网络端口无法继续巡检。。。");
                return false;
            }
        }else {
            //xmlrpc 验证agent端口
            if(StringUtils.isBlank(agent.getAgentPort())|| Integer.parseInt(agent.getAgentPort())<=0) {
                log.error("设备【" + computerBean.getIp() + "】未获取到Agent的端口无法继续巡检。。。");
                return false;
            }
        }
        return can;
    }

    public void startToAgent ( AgentInfoModel agent, HealthCheckData hcd, HcStartManager manager,String startType,String startuser)
            throws SupperhcException
    {
        try
        {

            log.info("启动巡检：agentId：" + agent.getAgentId() + ",agentIp:" + agent.getAgentIp() + ",agentPort:"
                    + agent.getAgentPort());
            String jsonData = JSON.toJSONString(hcd);
            log.info(jsonData);

            //验证是否开启http形式发送.默认不开启
            boolean hchttpSendFlag = ServerEnv.getInstance().getBooleanConfig("hc.httpserver.flag", false);
            String token = "";
            if(hchttpSendFlag) {
                String url = "http://" + agent.getAgentIp() + ":" + agent.getHttpPort() + "/restfulapi";
                token = HcSendHttp.getInstance().getTokenFromHttpServer(url);
            }else {
                //xmlrpc
                token = HcSendXmlRpc.getInstance().getXmlRpcToken();
            }

            Map<String, Object> resMap = null;
            String methodname = "";
            if(startType!=null && !"".equals(startType) && "imme".equals(startType)) {
                methodname = "immhc";
            }else {
                methodname = "addhc";
            }
            if(hchttpSendFlag) {
                resMap = HcSendHttp.getInstance().sendHttpServer(agent.getAgentIp(), Integer.parseInt(agent.getHttpPort()), methodname, jsonData,token,null);
            }else {
                //xmlrpc
                resMap =  HcSendXmlRpc.getInstance().sendXmlRpcForHc(agent.getAgentIp(), Integer.parseInt(agent.getAgentPort()), methodname, jsonData, token);
            }

            if(resMap==null) {
                /**
                 if(!"imme".equals(startType)) {
                 manager.updateComPointHcStatusToStop(hcd.getComChkList(),hcd.getPointids());
                 }
                 */
                throw new  SupperhcException("巡检启动异常！发送操作返回为空！");
            }else {
                if(resMap.get(SupperHcConstants.RES_SUCC)==null|| !(Boolean)(resMap.get(SupperHcConstants.RES_SUCC))) {
                    /** 注释掉，原逻辑是失败更新为未启动，存在问题
                     if(!"imme".equals(startType)) {
                     manager.updateComPointHcStatusToStop(hcd.getComChkList(),hcd.getPointids());
                     }*/
                    String messExceptions=resMap.get(SupperHcConstants.RES_MSG)==null?"":resMap.get(SupperHcConstants.RES_MSG).toString();
                    throw new  SupperhcException("巡检启动异常！"+messExceptions);
                }else {
                    if(!"imme".equals(startType)){
                        manager.updateComPointHcStatus2(hcd.getComChkList(),agent.getAgentId(), 1,hcd.getPointids(),startuser);
                    }
                    //如果是立即巡检记录下启动人和启动时间
                    else if("imme".equals(startType)) {
                        manager.updateComPointHcStatus2OfImme(hcd.getComChkList(),hcd.getPointids(),startuser);
                    }

                }
            }
        } catch (ClientProtocolException e)
        {
            log.error(e);
            throw new SupperhcException("agent连接失败 :" + agent.getAgentIp());
        } catch (IOException e)
        {
            log.error(e);
            throw new SupperhcException("agent连接失败: " + agent.getAgentIp());
        } catch (RepositoryException e)
        {
            log.error(e);
            throw new SupperhcException(e);
        }
    }

    public String getIBatchId ( int isysType )
    {
        String ids="'";
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn=null;
        String sql="select distinct IBATCHID from IEAI_ENVIRONMENT_CHECK_RUN where IBATCHID is not null";
        try
        {
            conn=DBManager.getInstance().getJdbcConnection(isysType);
            ps=conn.prepareStatement(sql);
            rs=ps.executeQuery();
            while(rs.next()) {
                if("'".equals(ids)) {
                    ids=ids+rs.getString("IBATCHID");
                }else {
                    ids=ids+"','"+rs.getString("IBATCHID");
                }
            }
            ids=ids+"'";
        } catch (Exception e)
        {
            log.error("getIBatchId is error ",e);
        }finally {
            DBResource.closeConn(conn, rs, ps, "getIBatchId", log);
        }
        return ids;
    }
}
