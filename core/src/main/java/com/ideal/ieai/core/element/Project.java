package com.ideal.ieai.core.element;

import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.agent.AgentObj;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.projectType.ProjectType;
import com.ideal.ieai.core.util.PrjVisitor;
import com.ideal.ieai.core.version.Version;

/**
 * <p>Title: iEAI Core3.0</p>
 * <p>Description:The class define IEAI project object .
 * Here save all project element mapand the action to the map . </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 3.0
 */

public class Project
    extends AbstractElement
{

    public Project()
    {
        this(0);
    }

    public Project(long TheID)
    {
        super(TheID);
    }

    Project(String TheName, long TheID)
    {
        super(TheName, TheID);

    }

    public void setVersion(Version TheVer)
    {
        _version = new Version(TheVer);
    }

    public Version getVersion()
    {
        return new Version(_version);
    }
    
    public ProjectType getProjectType()
    {
        return new ProjectType(projectTpye);
    }

    /**
     * @return collection project's Variant
     */
    public Collection getVariants()
    {
        return _envVars.values();
    }

    public Collection getResources()
    {
        return _resources.values();
    }

    public Resource getResourceByName(final String Name)
    {
        for (Iterator i = _resources.values().iterator(); i.hasNext(); )
        {
            Resource res = (Resource) i.next();
            if (res.getName().equals(Name))
            {
                return res;
            }
        }
        return null;
    }

    /**
     * @param Name the name of a envar.
     * @return the corresponding envar of the name.
     */
    public EnvVariant getVariantByName(final String Name)
    {
        for (Iterator i = _envVars.values().iterator(); i.hasNext(); )
        {
            EnvVariant env = (EnvVariant) i.next();
            if (env.getName().equals(Name))
            {
                return env;
            }
        }
        return null;
    }

    /**
     * @return collection of project's SchemaDef
     */
    public Collection getSchemaDefs()
    {
        return _schemaDefs.values();
    }

    /**
     * @return collection of project's import projects
     */

    public Collection getImpProjects()
    {
        return _impPrjs.values();
    }

    /**
     * @return collection of all project's functions
     */

    public Collection getFunctions()
    {
        return _functions.values();
    }

    /**
     * Return all function include current project and imp project's .
     * @return
     */
    public Collection getAllFunctions()
    {
        Iterator it = getImpProjects().iterator();
        java.util.List impLst = new ArrayList();
        java.util.List VisitedImp = new ArrayList();
        impLst.add(this);
        VisitedImp.add(this);
        while (it.hasNext())
        {
            ImpProject prj = (ImpProject) it.next();
            getAllImprj(prj, impLst, VisitedImp);
        }
        VisitedImp.clear();
        for (int i = 0, size = impLst.size(); i < size; i++)
        {
            if (impLst.get(i)instanceof Project)
            {
                VisitedImp.addAll( ( (Project) impLst.get(i)).getFunctions());
            }
            else if (impLst.get(i)instanceof ImpProject)
            {
                try
                {
                    VisitedImp.addAll( ( (ImpProject) impLst.get(i)).getFunctions());
                }
                catch (FileNotFoundException ex)
                {
                    continue;
                }
            }
        }
        return VisitedImp;
    }

    /**
     * @return collection of all project's internal functions
     */

    public Collection getInternalFunctions()
    {
        Iterator it = this.getFunctions().iterator();
        List internalFuncs = new ArrayList();
        while (it.hasNext())
        {
            Object elm = it.next();
            if (elm instanceof InternalFunction)
            {
                internalFuncs.add(elm);
            }
        }
        return internalFuncs;
    }

    /**
     * @return collection of all project's external functions
     */

    public Collection getExternalFunctions()
    {
        Iterator it = this.getFunctions().iterator();
        List externalFuncs = new ArrayList();
        while (it.hasNext())
        {
            Object elm = it.next();
            if (elm instanceof ExternalFunction)
            {
                externalFuncs.add(elm);
            }
        }
        return externalFuncs;
    }

    /**
     * @return collection of all project's workflows.
     */

    public Collection getWorkflows()
    {
        return _workflows.values();
    }

    public Map getWorkflowsForMap()
    {
        return _workflows;
    }
    
    
    /**
     * Get workflow based-on flow id
     * @param WorkflowId
     * @return
     */
    public Workflow getWorkflow ( final Long WorkflowId )
    {
        return (Workflow) _workflows.get(WorkflowId);
    }

    /**
     * Get workflow by name
     * @param WorkflowName String: Flow name
     * @return Workflow
     */
    public Workflow getWorkflowByName(final String WorkflowName)
    {
        for (Iterator i = _workflows.values().iterator(); i.hasNext(); )
        {
            Workflow flow = (Workflow) i.next();
            if (flow.getName().equals(WorkflowName))
            {
                return flow;
            }
        }
        return null;

    }

    public void setWorkflows(Map TheWorkflows)
    {
        _workflows = new HashMap(TheWorkflows);
    }

    /**
     * Update an element of project.
     * Get the element in maps and update it with a put operation
     * @param TheID the id of a project element
     * @param TheValue the new value of a project element.
     */
    public void updateComponent ( Long TheID, AbstractElement TheElement )
    {
        if ( (TheElement instanceof IEAISchemaDef && _schemaDefs.containsKey(TheID)))
        {
            AbstractElement elem = (AbstractElement) _schemaDefs.get(TheID);
            updateElements(elem, TheElement);
            _schemaDefs.put(TheID, TheElement);
        }
        else if (TheElement instanceof ImpProject && _impPrjs.containsKey(TheID))
        {
            AbstractElement elem = (AbstractElement) _impPrjs.get(TheID);
            updateElements(elem,  TheElement);
            _impPrjs.put(TheID, TheElement);
        }
        else if (TheElement instanceof Function && _functions.containsKey(TheID))
        {
            _functions.put(TheID, TheElement);
        }
        else if (TheElement instanceof Workflow && _workflows.containsKey(TheID))
        {
            _workflows.put(TheID, TheElement);
        }
        else if (TheElement instanceof Resource && _resources.containsKey(TheID))
        {
            _resources.put(TheID, TheElement);
        }
        else if (TheElement instanceof EnvVariant && _envVars.containsKey(TheID))
        {
            _envVars.put(TheID, TheElement);
        }
        else
        {
            throw new CompositeException(CompositeException.CAN_NOT_FIND_CHILD);
        }
    }

    /**
     * Remove an element from current project.
     * @param theElement the element that will be removed.
     * @throws com.ideal.ieai.core.element.CompositeException
     */
    public void removeComponent(AbstractElement theElement) throws com.ideal.ieai.core.element.
        CompositeException
    {
        Long elementId = theElement.getID();
        if (theElement instanceof IEAISchemaDef && _schemaDefs.containsKey(elementId))
        {
            updateElements(  theElement, null);
            _schemaDefs.remove(elementId);
        }
        else if (theElement instanceof ImpProject && _impPrjs.containsKey(elementId))
        {
            updateElements( theElement, null);
            _impPrjs.remove(elementId);
        }
        else if (theElement instanceof Function && _functions.containsKey(elementId))
        {
            _functions.remove(elementId);
        }
        else if (theElement instanceof Workflow && _workflows.containsKey(elementId))
        {
            _workflows.remove(elementId);
        }
        else if (theElement instanceof Resource && _resources.containsKey(elementId))
        {
            _resources.remove(elementId);
        }
        else if (theElement instanceof EnvVariant && _envVars.containsKey(elementId))
        {
            _envVars.remove(elementId);
        }
        else
        {
            throw new CompositeException(CompositeException.CAN_NOT_FIND_CHILD);
        }
    }

    /**
     * Add a project element to current project.
     * @param theElement the new element.
     * @throws com.ideal.ieai.core.element.CompositeException
     */
    public void addComponent(AbstractElement theElement) throws com.ideal.ieai.core.element.
        CompositeException
    {
        Long elementId = theElement.getID();
        if (theElement instanceof IEAISchemaDef)
        {
            _schemaDefs.put(elementId, theElement);
        }
        else if (theElement instanceof ImpProject)
        {
            _impPrjs.put(elementId, theElement);
        }
        else if (theElement instanceof Function)
        {
            _functions.put(elementId, theElement);
        }
        else if (theElement instanceof Workflow)
        {
            _workflows.put(elementId, theElement);
        }
        else if (theElement instanceof Resource)
        {
            _resources.put(elementId, theElement);
        }
        else if (theElement instanceof EnvVariant)
        {
            _envVars.put(elementId, theElement);
        }
        else
        {
            throw new CompositeException(CompositeException.ADD_WRONG_TYPE);
        }
    }

    /**
     * Get a project element by its key.
     * @param TheKey the element's key.
     * @return the corresponding element of the key.
     * @throws com.ideal.ieai.core.element.CompositeException
     */
    public AbstractElement getComponent ( Long TheKey )
    {
        if (_schemaDefs.containsKey(TheKey))
        {
            return (AbstractElement) _schemaDefs.get(TheKey);
        }
        else if (_impPrjs.containsKey(TheKey))
        {
            return (AbstractElement) _impPrjs.get(TheKey);
        }
        else if (_functions.containsKey(TheKey))
        {
            return (AbstractElement) _functions.get(TheKey);
        }
        else if (_workflows.containsKey(TheKey))
        {
            return (AbstractElement) _workflows.get(TheKey);
        }
        else if (_resources.containsKey(TheKey))
        {
            return (AbstractElement) _resources.get(TheKey);
        }
        else if (_envVars.containsKey(TheKey))
        {
            return (AbstractElement) _envVars.get(TheKey);
        }
        else
        {
            return null;
        }
    }

    /**
     * Check whether current project contain a given element.
     * @param Element a AbstractElement object
     * @return
     */
    public boolean contains(AbstractElement Element)
    {
        if (null == Element)
        {
            return false;
        }

        if (Element instanceof Project && Element.getName().equals(this.getName()))
        {
            return true;
        }
        else if (Element instanceof ImpProject && this.getImpProjects().contains(Element))
        {
            return true;
        }
        else if (Element instanceof IEAISchemaDef && this.getSchemaDefs().contains(Element))
        {
            if (Element.getNameSpace().equals(this.getName()))
            {
                return true;
            }
        }
        else if (Element instanceof Function && this.getFunctions().contains(Element))
        {
            if (Element.getNameSpace().equals(this.getName()))
            {
                return true;
            }
        }

        else if (Element instanceof Resource && this.getResources().contains(Element))
        {
            return true;
        }

        else if (Element instanceof Workflow && this.getWorkflows().contains(Element))
        {
            if (Element.getNameSpace().equals(this.getName()))
            {
                return true;
            }
        }
        else if (Element instanceof EnvVariant && this.getVariants().contains(Element))
        {
            return true;
        }
        return false;
    }

    /**
     * When a schemadef has changed ,project need to update those project element
     * that use the schema.
     * @param OldElement a IEAISchemaDef object that did not changed.
     * @param NewElement a IEAISchemaDef object that has changed.
     */
    private void updateElements(AbstractElement OldElement, AbstractElement NewElement)
    {
        Object key;
        IEAISchemaDef schema = null;
        Iterator i;
        //update all schemadef that has used the changed schemadef.
        for (i = _schemaDefs.keySet().iterator(); i.hasNext(); )
        {
            key = i.next();
            schema = (IEAISchemaDef) _schemaDefs.get(key);
            schema.accept(_visitor, OldElement, NewElement);
        }

        //update all EnvVariant that has used the changed schemadef.
        EnvVariant var;
        for (i = _envVars.keySet().iterator(); i.hasNext(); )
        {
            key = i.next();
            var = (EnvVariant) _envVars.get(key);
            var.accept(_visitor, OldElement, NewElement);
        } //end of for

        //update all Workflow that has used the changed schemadef.
        Workflow workflow;
        for (i = _workflows.keySet().iterator(); i.hasNext(); )
        {
            key = i.next();
            workflow = (Workflow) _workflows.get(key);
            workflow.accept(_visitor, OldElement, NewElement);
        } //end of for
    }

    private void getAllImprj(ImpProject Prj, java.util.List ImpLst, java.util.List VisitedImp)
    {
        if (!ImpLst.contains(Prj))
        {
            ImpLst.add(Prj);
        }
        if (VisitedImp.contains(Prj))
        {
            return;
        }
        Iterator it = null;
        try
        {
            it = Prj.getImpProjects().iterator();
        }
        catch (FileNotFoundException ex)
        {
        }
        if (null != it)
        {
            while (it.hasNext())
            {
                ImpProject prj = (ImpProject) it.next();
                getAllImprj(prj, ImpLst, VisitedImp);
            }
        }
    }
    /**
     * Map of schema ID to SchemaDef
     */
    private Map _schemaDefs = new HashMap();
    /**
     * Map of function ID to Function
     */
    private Map _functions = new HashMap();
    /**
     * Map of import projects ID of current project.
     */
    private Map _impPrjs = new HashMap();
    /**
     * Map of workflow ID to Workflow
     */
    private Map _workflows = new HashMap();
    /**
     * Map of resource ID to Resource
     */
    private Map _resources = new HashMap();
    /**
     * Map of ID to EnvVariant
     */
    private Map _envVars = new HashMap();
    /**
     * The ieai version under which this project is edited
     */
    private PrjVisitor _visitor = new PrjVisitor(this);

    private Version _version = null;
    
    private String  projectSystem="";
    
    private String isValidate = "false";
    /**工程上传类型，studio上传工程为0，excel为1，excel上传日启动工程为2**/
    private int        proUpType   = Constants.PROJECT_UP_STUDIO;

    public String isValidate ()
    {
        return isValidate;
    }

    public void setValidate ( String isValidate )
    {
        this.isValidate = isValidate;
    }

    /**
     * 获取工程对象本身所有的agent信息
     * agent信息包含资源及各远程活动自定义的信息
     * @return
     */
    public Map getAllAgent() {
        // 分两步。

        if (null != _agents)
            return _agents;
        else
            _agents = new HashMap();
        // 第一步从工程资源中获取；
        for (Iterator i = _resources.values().iterator(); i.hasNext();) {
            Resource res = (Resource) i.next();
            if (res
                    .getResourceInfo()
                    .getConfigViewClassName()
                    .equals(
                            "com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResourceView")) {
                AgentObj obj = new AgentObj();
                obj.setIp((String) ((DefaultConfig) res.getConfig()).get("host"));
                obj.setPort(Integer.parseInt((String)((DefaultConfig) res.getConfig()).get("port")));
                _agents.put(obj.getIp()+":"+obj.getPort(), obj);
            }
        }
        
        // 第二步，从具体活动中获取
        for (Iterator i = _workflows.values().iterator(); i.hasNext();) {
            Workflow flow = (Workflow) i.next();
            _agents.putAll(flow.getAllAgentFromFlow());
        }
        
        return _agents;
    }
    
    private Map _agents = null;



    private String uuid;
    public String getUuid ()
    {
        return uuid;
    }

    public void setUuid ( String uuid )
    {
        this.uuid = uuid;
    }

    private String projectFrom = "1";
    /**系统UUID,默认空串*/
    private String isystemtypeuuid = "";
    private String dailytype       = "1";
    private long imodelid = 1l;

    public long getImodelid ()
    {
        return imodelid;
    }

    public void setImodelid ( long imodelid )
    {
        this.imodelid = imodelid;
    }

    public String getDailytype ()
    {
        return dailytype;
    }

    public void setDailytype ( String dailytype )
    {
        this.dailytype = dailytype;
    }

    public String getIsystemtypeuuid ()
    {
        return isystemtypeuuid;
    }

    public void setIsystemtypeuuid ( String isystemtypeuuid )
    {
        this.isystemtypeuuid = isystemtypeuuid;
    }

    public String getProjectFrom ()
    {
        return projectFrom;
    }

    public void setProjectFrom ( String projectfrom )
    {
        this.projectFrom = projectfrom;
    }

    public int getProUpType ()
    {
        return proUpType;
    }

    public void setProUpType ( int proUpType )
    {
        this.proUpType = proUpType;
    }

    public String getProjectSystem ()
    {
        return projectSystem;
    }

    public void setProjectSystem ( String projectSystem )
    {
        this.projectSystem = projectSystem;
    }

}