Ext.onReady(function () {
    // 清理主面板的各种监听时间
    destroyRubbish();
    checkPerson = [];
    Ext.define('auditUserModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'id',
                    type: 'long'
                },
                {
                    name: 'name',
                    type: 'string'
                }
            ]
        });
    Ext.define('ipowerabilitymodel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long'
                },
                {
                    name: 'ilevel',
                    type: 'string'
                }
            ]
        });
    var auditUserStore = Ext.create('Ext.data.JsonStore', {
        fields: ['iparamname'],
        autoLoad: true,
        model: 'auditUserModel',
        proxy: {
            type: 'ajax',
            url: 'getUserList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    Ext.define('iscoremodel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long'
                },
                {
                    name: 'iscorename',
                    type: 'string'
                }
            ]
        });
    Ext.define('planModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {name: 'iid', type: 'long'},
                {name: 'iprocessdesc', type: 'string'},
                {name: 'iuserid', type: 'string'},
                {name: 'name', type: 'string'}
            ]
        });
    var noticemodel = Ext.create('Ext.form.field.ComboBox', {
        id: 'noticemodelId',
        labelWidth: 100,
        anchor: '94% 7%',
        margin: '0 0 0 30',
        padding: 3,
        name: 'noticemodel',
        store: auditUserStore,
        queryMode: 'local',
        forceSelection: true, // 要求输入值必须在列表中存在
        //typeAhead: true, // 允许自动选择
        displayField: 'name',
        valueField: 'id',
        triggerAction: "all",
        labelAlign: 'left',
        emptyText: '请选择审批人',
        readOnly: true,
        fieldLabel: '审批人<span style="color:red">*</span>'
    });
    auditUserStore.on('load', function () {
        Ext.getCmp('noticemodelId').setValue(parseInt(iaudituserid));
        Ext.getCmp('ipowerabilitymodelId').setValue(parseInt(ipowerabilityid));
        Ext.getCmp('iscoremodelId').setValue(parseInt(iscoreid));
        Ext.getCmp('istarttime').setValue(stime);
        Ext.getCmp('iendtime').setValue(etime);
        // var personArray = Ext.decode(checkPersons);
        // var personName = [];
        // for (let i = 0; i < personArray.length; i++) {
        //     var iid = personArray[i].iid;
        //     var iuserid = personArray[i].iuserid;
        //      Ext.getCmp('dutymodelId').setValue(iuserid);
        // }
        // checkPerson = personArray;
    });

    var ipowerabilityStore = Ext.create('Ext.data.JsonStore', {
        fields: ['ipowerabilityid'],
        autoLoad: true,
        model: 'ipowerabilitymodel',
        proxy: {
            type: 'ajax',
            url: 'getIpowerAbilityList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var ipowerabilitymodel = Ext.create('Ext.form.field.ComboBox', {
        id: 'ipowerabilitymodelId',
        name: 'ipowerabilitymodel',
        store: ipowerabilityStore,
        queryMode: 'local',
        labelWidth: 100,
        padding: 3,
        anchor: '94% 7%',
        margin: '0 0 0 30',
        forceSelection: true, // 要求输入值必须在列表中存在
        //typeAhead: true, // 允许自动选择
        displayField: 'ilevel',
        valueField: 'iid',
        triggerAction: "all",
        labelAlign: 'left',
        readOnly: true,
        emptyText: '请选择能力等级',
        fieldLabel: '能力等级<span style="color:red">*</span>'
    });
    var nameField = Ext.create('Ext.form.field.Text', {
        name: 'iplanname',
        fieldLabel: '计划名称<span style="color:red">*</span>',
        labelWidth: 100,
        padding: 3,
        anchor: '94% 7%',
        margin: '0 0 0 30',
        value: iplanname,
        readOnly: true,
        labelAlign: 'left'
    });
    var iscoreStore = Ext.create('Ext.data.JsonStore', {
        fields: ['iscoreid'],
        autoLoad: true,
        model: 'iscoremodel',
        proxy: {
            type: 'ajax',
            url: 'getPowerScoreList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    var iscoremodel = Ext.create('Ext.form.field.ComboBox', {
        id:'iscoremodelId',
        labelWidth: 100,
        padding: 3,
        name: 'iscoremodel',
        store: iscoreStore,
        queryMode: 'local',
        anchor: '94% 7%',
        margin: '0 0 0 30',
        forceSelection: true, // 要求输入值必须在列表中存在
        //typeAhead: true, // 允许自动选择
        displayField: 'iscorename',
        valueField: 'iid',
        triggerAction: "all",
        labelAlign: 'left',
        readOnly: true,
        emptyText: '请选择评估问卷',
        fieldLabel: '评估问卷<span style="color:red">*</span>'
    });
    var istarttime = Ext.create('Go.form.field.DateTime', {
        fieldLabel: '计划评估时间<span style="color:red">*</span>',
        labelAlign: 'left',
        id: 'istarttime',
        labelWidth: 100,
        padding: 3,
        anchor: '94% 7%',
        width:420,
        margin: '0 0 0 30',
        format: 'Y-m-d H:i:s',
        name: 'istarttime',
        readOnly: true,
        emptyText: '请选择日期时间',
    });
    var iendtime = Ext.create('Go.form.field.DateTime', {
        fieldLabel: '至<span style="color:red">*</span>',
        labelAlign: 'left',
        id: 'iendtime',
        labelWidth: 50,
        padding: 3,
        anchor: '94% 7%',
        width:415,
        margin: '0 0 0 30',
        format: 'Y-m-d H:i:s',
        name: 'iendtime',
        readOnly: true,
        emptyText: '请选择日期时间',
    });
    var irmarkField = Ext.create('Ext.form.field.TextArea', {
        name: 'iremark',
        fieldLabel: '备注<span style="color:red">*</span>',
        labelWidth: 100,
        anchor: '94% 10%',
        padding: 3,
        margin: '0 0 0 30',
        value: iremark,
        readOnly: true,
        labelAlign: 'left'
    });
    var returnField = Ext.create('Ext.form.field.TextArea', {
        name: 'ireturnreason',
        fieldLabel: '退回原因',
        labelWidth: 100,
        anchor: '98% 10%',
        width: 650,
        padding: 5,
        margin: '0 0 0 30',
        value: ireturnreason,
        readOnly: true,
        hidden: (status == 4 ? false : true),
        labelAlign: 'left'
    });
    var dutyStore = Ext.create('Ext.data.Store', {
        // fields: ['id', 'name'],
        autoLoad: true,
        model: 'dutymodel',
        proxy: {
            type: 'ajax',
            url: 'getUserList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var dytyCombo = Ext.create('Ext.form.field.ComboBox', {
        id: 'dutymodelId',
        margin: '5',
        store: dutyStore,
        queryMode: 'local',
        width: 600,
        //  forceSelection: true, // 要求输入值必须在列表中存在
        // typeAhead: true, // 允许自动选择
        displayField: 'name',
        valueField: 'id',
        readOnly: true
        //   editable: false,
        //  triggerAction: "all"
    });

    var columns = [{text: '序号', xtype: 'rownumberer', width: 65, hidden: true},
        {text: 'iid', dataIndex: 'iid', hidden: true},
        {
            text: '过程域',
            dataIndex: 'iprocessdesc', flex: 1
        },
        {
            text: '负责人',
            dataIndex: 'name', flex: 1
        }

    ];
    /** store* */
    planStore = Ext.create('Ext.data.Store',
        {
            autoLoad: true,
            autoDestroy: true,
            model: 'planModel',
            proxy:
                {
                    type: 'ajax',
                    url: 'getPowerScopeList1.do',
                    reader:
                        {
                            type: 'json',
                            root: 'dataList'
                        }
                }
        });
    planStore.on('beforeload', function(store, options) {
        var new_params = {
            iid : iid,
            iscoreid:iscoreid,
            status:status
        };
        Ext.apply(planStore.proxy.extraParams, new_params);
    });

    var planPanel = Ext.create('Ext.grid.Panel', {
        region: 'center',
        border: false,
        store: planStore,
        cls:'customize_panel_back',
        autoScroll : true,
        columns: columns,
        anchor: '94% 37%',
        margin: '0 0 0 30',
        loadMask: {
            msg: ' 数据加载中，请稍等 ',
            removeMask: true
        }
    });
    var formPanel = Ext.create('Ext.form.Panel', {
        region: "north",
        border: false,
        layout: 'anchor',
        width: 960,
        height: '100%',
        items: [nameField, ipowerabilitymodel, {
            border: false,
            layout: 'column',
            //margin: '5',
            items: [
                istarttime, iendtime
            ]
        }, irmarkField, iscoremodel, planPanel, noticemodel],
        buttonAlign: 'center',
        buttons: [{
            text: '取消',
            handler: function () {
                noticewindow.close();
            }
        }
        ]
    });
    /** 主Panel* */
    var mainPanel = Ext.create('Ext.panel.Panel',
        {
            renderTo: "noticeQuery_area",
            layout: 'border',
            width: 960,
            height: '100%',
            bodyCls: 'service_platform_bodybg',
            cls: 'customize_panel_back',
            bodyPadding: grid_margin,
            border: true,
            items: [formPanel]
        });

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

    /** *********************方法********************* */
    /* 解决IE下trim问题 */
    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };
});

