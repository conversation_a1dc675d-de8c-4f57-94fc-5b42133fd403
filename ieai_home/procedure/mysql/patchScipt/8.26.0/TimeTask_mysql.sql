
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

    CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_EXEC_STATIS (IID NUMERIC (19)   NOT NULL,TASK<PERSON>FO_ID NUMERIC (19)  ,CREATEDATE NUMERIC (8)  ,STATE NUMERIC (1)  ,SUCCESS_NUM NUMERIC (19) DEFAULT 0 ,DAILY_TOTAL_NUM NUMERIC (19) DEFAULT 1 , CONSTRAINT PK_IEAI_TIMETASK_EXEC_STATIS PRIMARY KEY (IID));
END;;
DELIMITER ;
CALL UPPRDE();
DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_TIMETASK_EXEC_STATIS' AND INDEX_NAME = 'IDX_TK_STATIS_INFOID_DATE' )THEN
        CREATE INDEX IDX_TK_STATIS_INFOID_DATE ON IEAI_TIMETASK_EXEC_STATIS (CREATEDATE, TASKINFO_ID);
    END IF;
END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN
    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_SCRIPT_INSTANCE' AND INDEX_NAME = 'IDX_SCRIPT_INS_TIMEOUT' )THEN
        CREATE INDEX IDX_SCRIPT_INS_TIMEOUT ON IEAI_SCRIPT_INSTANCE (ITIMEOUT,ICONSUMERIP,ICONSUMERPORT);
    END IF;
END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
DROP PROCEDURE IF EXISTS PROC_TIMETASK_MOVE_HIS;;
CREATE PROCEDURE PROC_TIMETASK_MOVE_HIS (IN AV_TASKID NUMERIC(19,0),OUT TEST_RESULT VARCHAR(50))
BEGIN
    DECLARE	LN_TASKID NUMERIC(19,0);
    DECLARE	LN_IP_NUM NUMERIC(19,0);
    DECLARE	LN_IP_OK_NUM NUMERIC(19,0);
    DECLARE LN_KILL_NUM NUMERIC(19,0);
    DECLARE LN_INSTANCE_STATE NUMERIC(2,0) DEFAULT 0;
    DECLARE LN_TASK_STARTTIME VARCHAR(20);
    DECLARE LN_TASKINFO_ID NUMERIC(19,0);

    SET LN_TASKID = AV_TASKID;
    SET TEST_RESULT='false';

    SELECT COUNT(ID) INTO LN_IP_OK_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND (TASKIPSTATE =0 OR TASKIPSTATE=-1);
    SELECT COUNT(ID) INTO LN_IP_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID;
    IF LN_IP_OK_NUM = LN_IP_NUM THEN
        SELECT COUNT(1) INTO LN_KILL_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND TASKIPSTATE=-1;
        -- 查询当前任务的开始时间
        SELECT DATE_FORMAT(STARTTIME, '%Y%m%d') INTO LN_TASK_STARTTIME FROM IEAI_TIMETASK_RUNTIME WHERE TASKID=LN_TASKID;
        -- 查询当前任务的infoId
        SELECT TASKINFOID INTO LN_TASKINFO_ID FROM IEAI_TIMETASK_INSTANCE WHERE ID = LN_TASKID;

        SELECT COUNT(1) INTO LN_KILL_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND TASKIPSTATE=-1;
        IF LN_KILL_NUM >0 THEN
            SET LN_INSTANCE_STATE=2;
            -- 更新异常状态
            UPDATE IEAI_TIMETASK_EXEC_STATIS SET STATE = LN_INSTANCE_STATE WHERE TASKINFO_ID = LN_TASKINFO_ID AND CREATEDATE = LN_TASK_STARTTIME AND (STATE != LN_INSTANCE_STATE OR STATE IS NULL);
        ELSE
            UPDATE IEAI_TIMETASK_EXEC_STATIS SET STATE = LN_INSTANCE_STATE+1, SUCCESS_NUM = SUCCESS_NUM+1 WHERE TASKINFO_ID = LN_TASKINFO_ID AND CREATEDATE = LN_TASK_STARTTIME;
        END IF;

        INSERT INTO IEAI_TIMETASK_HIS (TASKHISID,TASKID,STARTTIME,ENDTIME,INSRESULT,IMMED) SELECT A.TASKINSID,A.TASKID,A.STARTTIME,CURRENT_TIMESTAMP,LN_INSTANCE_STATE,A.IMMED FROM IEAI_TIMETASK_RUNTIME A WHERE A.TASKID = LN_TASKID;
        DELETE FROM IEAI_TIMETASK_RUNTIME WHERE TASKID = LN_TASKID ;
        INSERT INTO IEAI_TIMETASK_IP_HIS (ID,TASKHISID,IP,TASKIPSTATE,ICOM_NAME,HISDESCIP,UUID) SELECT B.ID,B.TASKINSID,B.IP,B.TASKIPSTATE,B.ICOM_NAME,B.HISDESCIP,B.UUID FROM IEAI_TIMETASK_IP B WHERE B.TASKINSID = LN_TASKID;
        DELETE FROM IEAI_TIMETASK_IP WHERE TASKINSID = LN_TASKID;
        DELETE FROM IEAI_TIMETASK_TIMEOUTINS WHERE INSTANCEID in (SELECT TASKID FROM IEAI_TIMETASK_HIS WHERE TASKID=LN_TASKID);
        SET TEST_RESULT='true';
    END IF;
    COMMIT;
END;;
DELIMITER ;

DELIMITER ;;
DROP PROCEDURE IF EXISTS PROC_AGENT_UPDATE_TASK_STATS;;
CREATE PROCEDURE PROC_AGENT_UPDATE_TASK_STATS(IN AV_IP_STATS NUMERIC(2,0),IN AV_REQID VARCHAR(50),OUT TEST_RESULT VARCHAR(50) )
BEGIN
    DECLARE	LN_TASKID NUMERIC(19,0);
    DECLARE	LN_IP_ID NUMERIC(19,0);
    DECLARE TEMPX NUMERIC(19,0);
    DECLARE LN_TASKINFO_ID NUMERIC(19,0);
    DECLARE LN_TASK_STARTTIME VARCHAR(20);

    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
        BEGIN
            SELECT TASKHISID, ID INTO LN_TASKID, LN_IP_ID FROM IEAI_TIMETASK_IP_HIS WHERE UUID = AV_REQID LIMIT 1;
        END;

    BEGIN
        SELECT COUNT(1) INTO TEMPX FROM IEAI_TIMETASK_IP WHERE UUID = AV_REQID;

        IF TEMPX > 0 THEN
            -- 可能异常
            SELECT TASKINSID, ID INTO LN_TASKID, LN_IP_ID FROM IEAI_TIMETASK_IP WHERE UUID = AV_REQID LIMIT 1;
            UPDATE IEAI_TIMETASK_IP SET TASKIPSTATE = AV_IP_STATS WHERE UUID = AV_REQID AND TASKIPSTATE != -1 AND TASKIPSTATE != 2;
            -- 如果更新条数为0，说明已经被迁移历史了，应该是做了强行终止或者失效
            IF AV_IP_STATS = -5 THEN
                UPDATE IEAI_TIMETASK_RUNTIME SET RUNSTATE = 1 WHERE TASKID = LN_TASKID AND RUNSTATE < 2;
                -- 查询当前任务的开始时间
                SELECT DATE_FORMAT(STARTTIME, '%Y%m%d') INTO LN_TASK_STARTTIME FROM IEAI_TIMETASK_RUNTIME WHERE TASKID=LN_TASKID;
                -- 查询当前任务的infoId
                SELECT TASKINFOID INTO LN_TASKINFO_ID FROM IEAI_TIMETASK_INSTANCE WHERE ID = LN_TASKID;
                -- 更新异常状态
                UPDATE IEAI_TIMETASK_EXEC_STATIS SET STATE = 2 WHERE TASKINFO_ID = LN_TASKINFO_ID AND CREATEDATE = LN_TASK_STARTTIME AND (STATE != 2 OR STATE IS NULL);
            END IF;
        END IF;
        SET TEST_RESULT = CONCAT (LN_TASKID , ',' ,  LN_IP_ID);
    END;
END ;;
DELIMITER ;