package com.ideal.ieai.agent.healthcheck.db;

import java.sql.Date;

public class H2FunctionCreate
{
public static void main ( String[] args )
{
    Date data = new Date(System.currentTimeMillis());
    int funGetEndDay = H2FunctionCreate.funGetEndDay(data,10);
    System.out.println(funGetEndDay);
}
    public static int funGetEndDay(Date date,Integer aiEndDay) {
        int liDay = 0;
        int beforeday = 0;
        int liendday = 0;
        if(date==null) {
            return 0;
        }
        
        if(aiEndDay==null) {
            beforeday = 0;
        }else if(aiEndDay<0){
            beforeday = Math.abs(aiEndDay);
        }else {
            beforeday = aiEndDay;
        }
        
        if(beforeday==0) {
            beforeday = 50;
        }else if(beforeday>60) {
            beforeday = 60;
        }
        
        int month = date.getMonth();
        Date date1 = new Date(date.getTime());
        date1.setMonth((month+1));
        long caz =  date1.getTime()-date.getTime();
        
        long dddd = (caz/(24*60*60*1000));
        
        
        liendday = (int) dddd;
        if(beforeday>=1 && beforeday<=31) {
            if(beforeday>liendday) {
                liDay = liendday;
            }else {
                liDay = beforeday;
            }
        }else {
            liDay = liendday-beforeday + 50 ;
        }
        return liDay;
    }
}
