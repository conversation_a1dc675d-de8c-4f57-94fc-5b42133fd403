Ext
		.onReady(function() {

			destroyRubbish();

			Ext.define('projectModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'divId',
					type : 'string'
				}, {
					name : 'groupName',
					type : 'string'
				}, {
					name : 'projectName',
					type : 'string'
				}, {
					name : 'flowName',
					type : 'string'
				}, {
					name : 'startTime',
					type : 'string'
				}, {
					name : 'state',
					type : 'string'
				}, {
					name : 'runNumber',
					type : 'string'
				}, {
					name : 'pollingTime',
					type : 'string'
				}, {
					name : 'nexRunTime',
					type : 'string'
				} ]
			});

			var columns = [ {
				xtype : 'rownumberer',
				header : '序号',
				align : 'center',
				width : 70
			}, {
				text : 'divId',
				dataIndex : 'divId',
				hidden : true,
				hideable : true
			}, {
				text : '组别',
				dataIndex : 'groupName',
				align : 'left',
				flex : 1,
				renderer : function (value, meta, record) {
					meta.style = 'white-space:normal;word-break:break-all;';
					return value;
				}
			}, {
				text : '系统名称',
				dataIndex : 'projectName',
				align : 'left',
				flex : 1,
				renderer : function (value, meta, record) {
					meta.style = 'white-space:normal;word-break:break-all;';
					return value;
				}
			}, {
				text : '批次名称',
				dataIndex : 'flowName',
				align : 'left',
				flex : 1,
				renderer : function (value, meta, record) {
					meta.style = 'white-space:normal;word-break:break-all;';
					return value;
				}
			}, {
				text : '轮询间隔(分钟)',
				dataIndex : 'pollingTime',
				align : 'left',
				flex : 1,
				renderer : function (value, meta, record) {
					meta.style = 'white-space:normal;word-break:break-all;';
					return value;
				}
			}/*
				 * ,{ text : '开始时间', dataIndex : 'startTime', align: 'left',
				 * flex :1 }
				 */, {
				text : '当前状态',
				dataIndex : 'state',
				align : 'left',
				flex : 1,
				renderer : function(value, metaData, record) {
					var status1 = "";
					var type = record.get("state");
					if (type == '1') {
						status1 = "正在运行";
					} else {
						status1 = "等待运行";
					}
					return status1;
				}
			}, {
				text : '下一次运行时间',
				dataIndex : 'nexRunTime',
				align : 'left',
				flex : 1,
				renderer : function (value, meta, record) {
					meta.style = 'white-space:normal;word-break:break-all;';
					return value;
				}
			}, {
				text : 'runNumber',
				dataIndex : 'runNumber',
				hidden : true,
				hideable : true
			} ];

			var store = Ext.create('Ext.data.Store', {
				pageSize : 25,
				autoLoad : true,
				model : 'projectModel',
				proxy : {
					type : 'ajax',
					url : 'queryPollingTaskList_nx.do',
					reader : {
						type : 'json',
						root : 'pollingTaskList',
						totalProperty : 'totalProperty'
					}
				}

			});

			var pageBar = Ext.create('Ext.PagingToolbar', {
				pageSize : 25,
				store : store,
				displayInfo : true,
				displayMsg : '显示{0}-{1}条，共{2}条',
				emptyMsg : "没有数据"
			});

			Ext.define('prjFlowGroupModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'prjName',
					type : 'String'
				}, {
					name : 'flowName',
					type : 'String'
				}, {
					name : 'groupName',
					type : 'String'
				} ]
			});

			Ext.define('prjFlowGroupModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'prjName',
					type : 'String'
				}, {
					name : 'flowName',
					type : 'String'
				}, {
					name : 'groupName',
					type : 'String'
				} ]
			});

			var groupStore = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoSync : true,
				fields : [ 'group1Name' ],
				proxy : {
					type : 'ajax',
					url : 'queryGroupNameList_nx.do',
					reader : {
						type : 'json',
						root : 'groupNameList'
					}
				}
			});

			var prjStore = Ext.create('Ext.data.Store', {
				pageSize : 15,
				autoLoad : true,
				model : 'prjFlowGroupModel',
				proxy : {
					type : 'ajax',
					url : 'queryflow/getprjname.do',
					reader : {
						type : 'json',
						root : 'datalist'
					}
				}
			});

			var flowStore = Ext.create('Ext.data.Store', {
				pageSize : 15,
				autoLoad : false,
				model : 'prjFlowGroupModel',
				proxy : {
					type : 'ajax',
					url : 'queryflow/getflowname.do',
					reader : {
						type : 'json',
						root : 'datalist'
					}
				}
			});

			flowStore.on('beforeload', function(store, options) {
				var new_params = {
					prjName : form.getForm()
							.findField('proName_pollingTask').getValue(),
					flowName : form.getForm().findField('flowName_pollingTask')
							.getValue()
				};
				Ext.apply(store.proxy.extraParams, new_params);
			});

			prjStore.on('beforeload', function(store, options) {
				var new_params = {
					groupName : form.getForm().findField(
							'groupName_pollingTask').getValue()
				};
				Ext.apply(store.proxy.extraParams, new_params);
			});

			var runDate_pollingTask = Ext.create('Go.form.field.DateTime', {
//				layout : 'anchor',
				labelWidth : 80,
				labelAlign : 'right',
				xtype : 'datefield',
				fieldLabel : '运行日期',
				name : 'runDate_pollingTask',
				value : pollingTask_queryDate_ALL,
				maxValue : new Date(),
				format : 'Y-m-d H:i:s',
				//width : 205,
				width : '25%',
//				anchor:'30%',
				columnWidth:.3,
				editable : false,
				padding : '0 10 0 0'
			
			});
			
			var form = Ext.create('Ext.form.Panel', {
				border : false,
				width : contentPanel.getWidth() - 20,
				layout : "column",
				bodyPadding : 10,
				items : [
						{

							xtype : 'combobox',
							labelStyle : "text-align:right;",
							width : '100%',
							margin : '5 0 5 0',
							labelWidth : 80,
							editable : true,
							fieldLabel : '系统名称',
							name : 'proName_pollingTask',
							store : prjStore,
							displayField : 'prjName',
							listeners : {
								select : function() {
									form.getForm().findField(
											'flowName_pollingTask')
											.setValue('');
									flowStore.reload();
								}
							}
						},
						{
							fieldLabel : '组别',
							labelWidth : 80,
							labelStyle : "text-align:right;",
							name : 'groupName_pollingTask',
							displayField : 'group1Name',
							margin : '5 0 5 0',
							// valueField : 'group1Name',
							editable : false,
							// emptyText : '请选择',
							value : pollingTask_groupName_ALL,
							store : groupStore,
							width : '50%',
							queryMode : 'local',
							xtype : 'combobox',
							listeners : {
								select : function() {
									form.getForm().findField('proName_pollingTask').setValue('');
									form.getForm().findField('flowName_pollingTask').setValue('');
									prjStore.reload();
								}
							}
						},
						{
							xtype : 'combobox',
							labelStyle : "text-align:right;",
							width : '50%',
							margin : '5 0 5 0',
							fieldLabel : '批次名称',
							name : 'flowName_pollingTask',
							store : flowStore,
							displayField : 'flowName',
							typeAhead : true,
							triggerAction : 'all',
							selectOnFocus : true,
							listeners : {
								'beforequery' : function(e) {
									var combo = e.combo;
									if (!e.forceAll) {
										var input = e.query;
										// 检索的正则
										var regExp = new RegExp(".*" + input
												+ ".*");
										// 执行检索
										combo.store.filterBy(function(record,
												id) {
											// 得到每个record的项目名称值
											var text = record
													.get(combo.displayField);
											return regExp.test(text);
										});
										combo.expand();
										return false;
									}
								}
							}
						},
						{
							width : '50%',
							layout:'column', 
							border:false, 
							items:[
								
								{
//									layout : 'anchor',
									labelWidth : 80,
									labelAlign : 'right',
									fieldLabel : '当前状态',
									name : 'state_pollingTask',
									displayField : 'name',
									valueField : 'id',
									editable : false,
									emptyText : '请选择',
									// width : 400,
									width : '25%',
//									anchor:'30%',
									columnWidth:.3,
									queryMode : 'local',
									xtype : 'combobox',
									store : new Ext.data.ArrayStore({
										fields : [ 'id', 'name' ],
										data : [ [ "1", '正在运行' ], [ "3", '等待运行' ] ]
									})
								},
								runDate_pollingTask
								]
						},
						{
							width : '50%',
							layout:'column', 
							border:false, 
							items:[
								
								{
//									layout : 'anchor',
									border : false,
									width : "25%",
									items : [ {
//										 anchor:'20%',
										xtype : 'button',
										text : '查询',
										allowBlank : true,
										handler : function() {
											pageBar.moveFirst();
											store.load({
												params : {
													start : 0,
													limit : 25
												}
											});
										}
									} ]

								},
								{
//									layout : 'anchor',
									border : false,
									width : "25%",
									items : [ {
//										 anchor:'20%',
										xtype : 'button',
										text : '重置',
										allowBlank : true,
										handler : function() {
											form.getForm().findField(
													'groupName_pollingTask').reset();
											form.getForm().findField(
													'proName_pollingTask').reset();
											form.getForm().findField(
													'flowName_pollingTask').reset();
											form.getForm().findField(
													'state_pollingTask').reset();
											form.getForm().findField(
													'runDate_pollingTask').reset();
										}
									} ]
								}
							]
						}
						 ]
			});

			store.on('beforeload', function(store, options) {
				var new_params = {
					groupName : form.getForm().findField(
							'groupName_pollingTask').getValue(),
					proName : form.getForm().findField('proName_pollingTask')
							.getValue(),
					flowName : form.getForm().findField('flowName_pollingTask')
							.getValue(),
					state : form.getForm().findField('state_pollingTask')
							.getValue(),
					queryDate : Ext.util.Format.date(form.getForm().findField(
							'runDate_pollingTask').getValue(), 'Ymd')
				};

				Ext.apply(store.proxy.extraParams, new_params);

			});

			store
					.on(
							"load",
							function(store, records) {
								for (var i = 0; i < store.getCount(); i++) {
									// 初始化默认展开行
									rowExpanderHis.toggleRow(i, store.getAt(i));
									// 动态展示进度条
									var divId = store.getAt(i).get('divId');
									var state = store.getAt(i).get('state');
									var runNumber = store.getAt(i).get(
											'runNumber');
									var pollingStepStr = "<div class='PTask_border'>";
									for (var j = 1; j <= runNumber; j++) {
										if (j < runNumber) {
											pollingStepStr += "<div class='PTask_green'></div>";
										} else {
											if (state == '1') {
												pollingStepStr += "<div class='PTask_green'></div>";
											} else {
												pollingStepStr += "<div class='PTask_blue'></div>";
											}
										}
									}
									pollingStepStr += "</div>"
									document.getElementById("pollingStep"
											+ divId).innerHTML = pollingStepStr;
								}
								/*
								 * store.each(function(record){ var flowId1 =
								 * record.get('flowId');
								 * document.getElementById("pollingStep"+flowId1).innerHTML =
								 * flowId1; });
								 */
							})

			var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
				clicksToEdit : 2
			});

			var rowExpanderHis = Ext.create('Ext.grid.plugin.RowExpander', {
				expandOnDblClick : false,
				expandOnEnter : false,
				rowBodyTpl : [ '<div id="pollingStep{divId}">', '</div>' ]
			});

			// 覆盖重写Ext.grid.plugin.RowExpander里的方法
			Ext.override(Ext.grid.plugin.RowExpander, {
				// 重写添加伸缩按钮的方法
				addExpander : function() {
					var b = this, a = b.grid, c = b.getHeaderConfig();
					if (a.ownerLockable) {
						a = a.ownerLockable.lockedGrid;
						// 行的宽度不增加
						// a.width+=c.width
					}
					// 隐藏掉控件的伸缩键列
					// a.headerCt.insert(0,c);
				}
			});

			var grid = Ext.create('Ext.grid.Panel', {
				// height : contentPanel.getHeight()-100,
				// width : contentPanel.getWidth(),
				// autoScroll : false,
				minHeight : contentPanel.getHeight() - 100,
				autoHeight : true,
				width : contentPanel.getWidth() - 20,
				plugins : [ cellEditing, rowExpanderHis ],
				store : store,
				border : false,
				columns : columns,
				sortableColumns : false,
				enableColumnHide : false,
				bbar : pageBar
			/*
			 * , listeners:{          viewready:function(g){             
			 * for(var i in g.view.getRows()){                  //expander为插件
			 *              expander.toggleRow(g.view.getRow(i));              }
			 *          }         }
			 */
			});

			var mainPanel = Ext.create('Ext.panel.Panel', {
				border : false,
				height : contentPanel.getHeight(),
				width : contentPanel.getWidth(),
				overflowX : 'hidden',
				overflowY : 'scroll',
				items : [ form, grid ],
				renderTo : "pollingTaskList_grid_area"
			});

			contentPanel.on("resize", function() {
				form.setWidth(contentPanel.getWidth() - 20);
				// grid.setHeight(contentPanel.getHeight()-100);
				grid.setWidth(contentPanel.getWidth() - 20);
			});

			contentPanel.getLoader().on("beforeload",
					function(obj, options, eOpts) {
						Ext.destroy(mainPanel);
						if (Ext.isIE) {
							CollectGarbage();
						}
					});

		});
/*
 * function forwardFlowMessage(projectId,projectName,uuid){
 * 
 * contentPanel.getLoader().load({url: 'batchConfig_gf.do?tmp='+new
 * Date().getTime(),params: {projectId: projectId,
 * projectName:projectName,uuid:uuid},scripts: true}); }
 */
