package com.ideal.service.timetask.audit;

import com.ideal.common.utils.PoiUtil;
import com.ideal.ieai.commons.Conscommon;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.jobscheduling.repository.home.StringUtils;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.timetask.common.TimetaskConstants;
import com.ideal.ieai.server.timetask.repository.TimetaskInfoBean;
import com.ideal.ieai.server.timetask.repository.audit.TTauditManage;
import com.ideal.ieai.server.timetask.repository.audit.TTauditModel;
import com.ideal.ieai.server.timetask.repository.importexcel.ImportExcelImpl;
import com.ideal.ieai.server.util.DateUtil;
import com.ideal.service.timetask.manage.TTManageService;
import com.ideal.util.log.LogPrintUtil;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.JspException;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

/**
 * 
 * @ClassName:  TTauditService   
 * @Description:定时任务 任务维护业务层   
 * @author: lei_wang 
 * @date:   2019年12月29日 上午8:38:59   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
public class TTauditService
{

    private Logger                log     = Logger.getLogger(TTauditService.class);

    private static TTauditService intance = new TTauditService();

    private static final String   UTF_8   = "UTF-8";

    public static TTauditService getInstance ()
    {
        if (intance == null)
        {
            intance = new TTauditService();
        }
        return intance;
    }

    /**
     * 
     * @Title: getTTauditList   
     * @Description: 任务维护   
     * @param start
     * @param limit
     * @param sysType
     * @param userId
     * @param condition
     * @param ip
     * @param taskType
     * @param groupId
     * @param stateCode
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月31日 下午1:21:16
     */
    public Map getTTauditList ( Integer start, Integer limit, int sysType, Long userId, String condition, String ip,
            Integer taskType, Integer groupId, Integer stateCode ,Integer stateCodeManage,Integer tasklevel) throws RepositoryException
    {
        Connection conn = null;
        TTauditManage manage = new TTauditManage();
        String permFilterSql = "";
        int dbtype = JudgeDB.IEAI_DB_TYPE;
        StringBuilder sbf = new StringBuilder();

        // 处理权限条件
        String inpermPrjIdsStr = this.getInpermPrjIdsStr(userId, sysType).toString();
        if (null != inpermPrjIdsStr)
        {
            if ("".equals(inpermPrjIdsStr))// 没有任何权限
            {
                permFilterSql = " and  TTG.IID is null ";
            } else// 权限内工程过滤
            {
                permFilterSql = " and  TTG.IID in (" + inpermPrjIdsStr + ")";
            }
        }
        // 处理查询条件
        if ("".equals(condition) || condition == null)
        {
            sbf.append(" WHERE TTI.IGID = TTG.IID  " + permFilterSql + "  ) AAA ");
            sbf.append(
                " WHERE 1=1  AND IID NOT IN  (SELECT DISTINCT TID AS ID FROM IEAI_TIMETASK_SUBINFO WHERE TASKSTATUS=2 OR (OPTTYPE=4 AND TASKSTATUS = 1))  ");
        } else if (condition.contains("_") || condition.contains("%"))
        {
            condition = condition.replaceAll("_", "/_");
            condition = condition.replaceAll("%", "/%");
            sbf.append(
                " WHERE  TTI.IID NOT IN  (SELECT DISTINCT TID AS ID FROM IEAI_TIMETASK_SUBINFO WHERE TASKSTATUS=2 OR (OPTTYPE=4 AND TASKSTATUS = 1))  AND TTI.IGID = TTG.IID "
                        + permFilterSql + " ) AAA ");
            sbf.append("WHERE  (ITASKNAME LIKE '%" + condition + "%'  escape  '/' ");
            sbf.append(" or  ITASKRUNTIME LIKE '%" + condition + "%'  escape '/'");
            sbf.append(" or  ITASKCOMMAND LIKE '%" + condition + "%' escape  '/' ");
            sbf.append(" or  ITASKDESC LIKE '%" + condition + "%' escape '/'  ");
            sbf.append(" ) ");
        } else
        {
            sbf.append(
                " WHERE TTI.IID  NOT IN  (SELECT DISTINCT TID AS ID FROM IEAI_TIMETASK_SUBINFO WHERE TASKSTATUS=2 OR (OPTTYPE=4 AND TASKSTATUS = 1))  AND TTI.IGID = TTG.IID "
                        + permFilterSql + " ) AAA ");
            if(condition.indexOf("'")!=-1){
                sbf.append(" WHERE (ITASKNAME  LIKE '%" + condition.replace("'","''") + "%'");
                sbf.append(" or ITASKRUNTIME  LIKE '%" + condition.replace("'","''") + "%'");
                sbf.append(" or ITASKCOMMAND  LIKE '%" + condition.replace("'","''") + "%'");
                sbf.append(" or ITASKDESC  LIKE '%" + condition.replace("'","''") + "%'");
                sbf.append(" ) ");

            }else{
                sbf.append(" WHERE (ITASKNAME  LIKE '%" + condition + "%'");
                sbf.append(" or ITASKRUNTIME  LIKE '%" + condition + "%'");
                sbf.append(" or ITASKCOMMAND  LIKE '%" + condition + "%'");
                sbf.append(" or ITASKDESC  LIKE '%" + condition + "%'");
                sbf.append(" ) ");
            }

        }

        if (null != taskType && taskType != 0)
        {
            sbf.append(" and ITASKTYPE = '" + taskType + "'");
        }

        if (null != groupId && groupId != 0)
        {
            sbf.append(" and IGID = '" + groupId + "'");
        }

        if (null != stateCode)
        {
            sbf.append(" and ISTATUS = " + stateCode + "");
        }

        String stateCodeM = "";
        // 处理任务状态条件
        if (!(stateCodeManage == null || "".equals(stateCodeManage)))
        {
            if (100 == stateCodeManage)
            { // 暂停
                sbf.append(" and INFOENABLE = 0  AND INFOPAUSE=1 ");
            } else if (0 == stateCodeManage)
            {
                // 失效
                sbf.append(" and ((INFOENABLE = 0  AND INFOPAUSE=0) or (INFOENABLE = -1)) ") ;
            } else
            {
                sbf.append(" and INFOENABLE  = " + stateCodeManage + "");
            }

        }
        // 处理任务级别查询条件
        if (!(tasklevel == null || "".equals(tasklevel)))
        {
            sbf.append(" and TASKLEVEL = " + tasklevel);
        }


        StringBuilder ipwhere = new StringBuilder();
        if (null != ip && !"".equals(ip))
        {
            PreparedStatement ps = null;
            ResultSet rs = null;
            PreparedStatement ps1= null;
            ResultSet rs1= null;
            Connection con = null;
            Set<Long> idSet = new HashSet<>();
            try {
                con = DBResource.getConnection("getTimetaskList", log, Constants.IEAI_TIMINGTASK);
                long startTime1 = System.currentTimeMillis();
                String getAgentIdSql = "SELECT IAGENTINFO_ID FROM IEAI_AGENTINFO WHERE IAGENT_IP like ?";
                ps = con.prepareStatement(getAgentIdSql);
                ps.setString(1, "%"+ip+"%");
                rs = ps.executeQuery();
                List<String> agentIdList = new ArrayList<>();
                while (rs.next()) {
                    agentIdList.add(rs.getString(1));
                }

                String getIpSql = "SELECT IID, IIP FROM IEAI_TIMETASK_AUDIT";
                ps1 = con.prepareStatement(getIpSql);
                rs1 = ps1.executeQuery();
                while (rs1.next()) {
                    Long id = rs1.getLong(1);
                    String[] ips = rs1.getString(2) == null ? new String[0]:rs1.getString(2).split(",");
                    for (int j = 0; j < ips.length; j++) {
                        if (agentIdList.contains(ips[j])) {
                            idSet.add(id);
                            break;
                        }
                    }
                }
                log.info("根据调度ip获取符合的任务id耗时：" + (System.currentTimeMillis() - startTime1));
            } catch (Exception e) {
                log.error("定时任务任务维护查询，解析调度ip异常",e);
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally {
                DBResource.closeConn(con, rs, ps, "getTTauditList", log);
            }
            // oralce
            ipwhere.append("  WHERE 1=1 ");
            if (!idSet.isEmpty()) {
                ipwhere.append("  AND ").append(Conscommon.getInSql(idSet.toArray(new Long[0]), "A.IID", 1000));
            }else{
                //如果没查到对应的id则置为null，避免错误的ip将所有的记录都查出来
                ipwhere.append(" AND A.IID = null ");
            }
        }
//        if (null != ip && !"".equals(ip))
//        {
//            if (DBManager.Orcl_Faimily())
//            {
//                // oralce
//                ipwhere.append("   JOIN (select IID, dbms_lob.substr(regexp_substr(IIP, '[^,]+', 1, level)) IIP ");
//                ipwhere.append("  from IEAI_TIMETASK_AUDIT ");
//                ipwhere.append("   connect by level <= regexp_count(IIP, ',') + 1 " );
//                ipwhere.append("  and IID = prior IID ");
//                ipwhere.append(" and prior dbms_random.value is not null)  TA ON TA.IID = A.IID ");
//                ipwhere.append("  AND TA.IIP IN (SELECT CP.IAGENTINFO_ID AS COMPUTERID ");
//                ipwhere.append("  FROM IEAI_AGENTINFO CP ");
//                if(ip.indexOf("'")!=-1){
//                    ipwhere.append("  WHERE CP.IAGENT_IP like '%" + ip.replace("'","''") + "%' ");
//                }else{
//                    ipwhere.append("  WHERE CP.IAGENT_IP like '%" + ip + "%' ");
//                }
//
//                ipwhere.append(" ) ");
//            } else if (2 == dbtype)
//            {
//                ipwhere.append("   JOIN (SELECT CP.IAGENTINFO_ID AS COMPUTERID, ");
//                ipwhere.append("  CP.IAGENT_IP     AS IP, ");
//                ipwhere.append("  CP.IAGENT_PORT   AS PORT,  ");
//                ipwhere.append("  RE.SYSTEMID AS SYSTEMIDA, ");
//                ipwhere.append("  to_char(RE.COMPUTERID) as COMPUTERIDS  ");
//                ipwhere.append("   FROM IEAI_AGENTINFO CP, IEAI_SYS_RELATION RE ");
//                ipwhere.append("  WHERE CP.IAGENTINFO_ID  = RE.COMPUTERID   ");
//                if(ip.indexOf("'")!=-1){
//                    ipwhere.append("  AND CP.IAGENT_IP like '%" + ip.replace("'","''") + "%' ");
//
//                }else{
//                    ipwhere.append("  AND CP.IAGENT_IP like '%" + ip + "%' ");
//                }
//                ipwhere.append("   )  TEMP    ");
//                ipwhere.append("   ON TEMP.SYSTEMIDA =A.IGID ");
//                ipwhere.append("  WHERE  A.IID = TEMPA.ID ");
//                ipwhere.append("  AND TEMP.COMPUTERIDS =TEMPA.result1  ");
//
//            } else if (3 == dbtype)
//            {
//                // mysql
//                ipwhere.append(" JOIN  (SELECT CP.IAGENTINFO_ID AS COMPUTERID , " + " CP.IAGENT_IP     AS IP, ");
//                ipwhere.append("   CP.IAGENT_PORT   AS PORT, " + "  RE.SYSTEMID AS SYSTEMIDA,  ");
//                ipwhere.append("   RE.COMPUTERID AS COMPUTERIDS ");
//
//                ipwhere.append("  FROM IEAI_AGENTINFO CP,  IEAI_SYS_RELATION RE ");
//                ipwhere.append("   WHERE CP.IAGENTINFO_ID = RE.COMPUTERID    ");
//                if(ip.indexOf("'")!=-1){
//                    ipwhere.append("   AND CP.IAGENT_IP  like '%" + ip.replace("'","''") + "%'  ");
//
//                }else{
//                    ipwhere.append("   AND CP.IAGENT_IP  like '%" + ip + "%'  ");
//                }
//                ipwhere.append("    )  TEMP , ");
//                ipwhere.append(
//                    "                  (select a.IID,substring_index(substring_index(a.col,',',b.help_topic_id+1),',',-1) AS RESULT1  ");
//                ipwhere.append("                      from ");
//                ipwhere.append("                         (select IIP as col,iid from IEAI_TIMETASK_AUDIT) as a ");
//                ipwhere.append("                      join   mysql.help_topic as b ");
//                ipwhere.append(
//                    "                       on b.help_topic_id < (char_length(a.col) - char_length(replace(a.col,',',''))+1) ");
//                ipwhere.append("                  ) TEMPA WHERE ");
//                ipwhere.append("                 TEMP.SYSTEMIDA =A.IGID    AND  TEMPA.IID= A.IID ");
//                ipwhere.append("               AND  TEMP.COMPUTERIDS = TEMPA.RESULT1");
//            }
//        }

        String sqlList = "";
        String sqlCount="";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlCount = "select count(1) AS NUM from  (select  A.*,B.TASKCOUNT from  ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME, "
                    + " ifnull(INFO.ENABLE,-1) AS INFOENABLE,ifnull(INFO.PAUSE,-1) AS INFOPAUSE "
                    + "FROM IEAI_TIMETASK_AUDIT TTI LEFT JOIN IEAI_TIMETASK_INFO INFO ON  INFO.ID = TTI.IID, IEAI_PROJECT  TTG " + sbf.toString() + ")A  "
                    + "LEFT JOIN  (SELECT T2.TASKINFOID , COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY T2.TASKINFOID)  B ON A.IID = B.TASKINFOID " + ipwhere.toString()
                    + " order by  A.IGID,  A.IID DESC ) dc ";
            sqlList = "select  A.*,B.TASKCOUNT  from   ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME, "
                    + " ifnull(INFO.ENABLE,-1) AS INFOENABLE,ifnull(INFO.PAUSE,-1) AS INFOPAUSE "
                    + "FROM IEAI_TIMETASK_AUDIT TTI LEFT JOIN IEAI_TIMETASK_INFO INFO ON  INFO.ID = TTI.IID, IEAI_PROJECT  TTG " + sbf.toString() + ")A  "
                    + "LEFT JOIN  (SELECT T2.TASKINFOID ,COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY  T2.TASKINFOID) B ON A.IID = B.TASKINFOID "
                    + "LEFT JOIN  IEAI_TIMETASK_INFO INFO ON A.IID = INFO.ID " + ipwhere.toString()
                    + " order by  A.IGID,  A.IID  DESC ";
        } else if (DBManager.Orcl_Faimily())
        {
            sqlCount = "select count(1) AS NUM from (select  A.*,B.TASKCOUNT from  ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME ,"
                    + " nvl(INFO.ENABLE,-1) AS INFOENABLE,nvl(INFO.PAUSE,-1) AS INFOPAUSE "
                    + "FROM  IEAI_TIMETASK_AUDIT TTI LEFT JOIN IEAI_TIMETASK_INFO INFO ON  INFO.ID = TTI.IID,  IEAI_PROJECT TTG " + sbf.toString() + "  ) A  "
                    + "LEFT JOIN  (SELECT T2.TASKINFOID ,COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY  T2.TASKINFOID) B  ON A.IID = B.TASKINFOID " + ipwhere.toString()
                    + " order by  A.IGID,  A.IID DESC) dc";
            sqlList = "select  A.*,B.TASKCOUNT from   ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME, "
                    +" nvl(INFO.ENABLE,-1) AS INFOENABLE,nvl(INFO.PAUSE,-1) AS INFOPAUSE "
                    + "FROM IEAI_TIMETASK_AUDIT TTI LEFT JOIN IEAI_TIMETASK_INFO INFO ON  INFO.ID = TTI.IID,  IEAI_PROJECT TTG " + sbf.toString() + "  )A  "
                    + "LEFT JOIN (SELECT  T2.TASKINFOID ,COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY T2.TASKINFOID)  B ON A.IID = B.TASKINFOID "
                    + "LEFT JOIN  IEAI_TIMETASK_INFO INFO ON A.IID = INFO.ID  " + ipwhere.toString()
                    + " order by   A.IGID,  A.IID DESC ";
        } else
        {
            sqlCount = "select count(1) AS NUM from (select  A.*,B.TASKCOUNT from  ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME, "
                    + " COALESCE(INFO.ENABLE,-1) AS INFOENABLE,COALESCE(INFO.PAUSE,-1) AS INFOPAUSE "
                    + "FROM IEAI_TIMETASK_AUDIT TTI LEFT JOIN IEAI_TIMETASK_INFO INFO ON  INFO.ID = TTI.IID ,  IEAI_PROJECT TTG " + sbf.toString() + ")A  "
                    + "LEFT JOIN (SELECT T2.TASKINFOID ,COUNT(T2.ID) TASKCOUNT FROM  IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY T2.TASKINFOID) B ON A.IID =  B.TASKINFOID  " + ipwhere.toString()
                    + "order by  A.IGID,  A.IID DESC) dc";

            sqlList = "select  A.*,B.TASKCOUNT,coalesce(info.ENABLE,-1) AS INFOENABLE from  ( SELECT   AAA.*   FROM (SELECT TTI.*, TTG.INAME AS GNAME, "
                    + " COALESCE(INFO.ENABLE,-1) AS INFOENABLE,COALESCE(INFO.PAUSE,-1) AS INFOPAUSE "
                    + "FROM IEAI_TIMETASK_AUDIT  TTI LEFT JOIN IEAI_TIMETASK_INFO INFO ON  INFO.ID = TTI.IID , IEAI_PROJECT TTG " + sbf.toString() + ")A  "
                    + "LEFT JOIN (SELECT T2.TASKINFOID ,COUNT(T2.ID)  TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID "
                    + "GROUP BY T2.TASKINFOID) B ON  A.IID =  B.TASKINFOID  "
                    + "LEFT JOIN  IEAI_TIMETASK_INFO INFO ON A.IID = INFO.ID  " + ipwhere.toString() +"order by  A.IGID,   A.IID  DESC";


        }
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            map = manage.getTTauditList(conn, sqlList, sqlCount, start, limit, ip);
        } catch (Exception e)
        {
            log.error("TTauditService.getTTauditList is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getTTauditList", log);
        }
        return map;
    }

    /**
     * 
     * @Title: getInpermPrjIdsStr   
     * @Description:    
     * @param userId
     * @param type
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月23日 下午12:57:22
     */
    private StringBuilder getInpermPrjIdsStr ( long userId, int type )
    {
        StringBuilder inpermPrjIdsStr = new StringBuilder();
        try
        {
            List inpermPrjList = TTauditManage.getInstance().getBusinessSystemNameList(userId, type);
            for (int i = 0; inpermPrjList != null && i < inpermPrjList.size(); i++)
            {
                Map tmpMap = (Map) inpermPrjList.get(i);
                long prjId = (Long) tmpMap.get("isystemId");
                if (i == 0)
                {
                    inpermPrjIdsStr.append(prjId);
                } else
                {
                    inpermPrjIdsStr.append("," + prjId);
                }

            }
        } catch (Exception e)
        {
            log.error("getTimetaskList method err:",e);
        }
        return inpermPrjIdsStr;
    }
    public List<Map> jsonTransAlertMapMap(String jsonData)
    {
        List<Map> dataList = new ArrayList<Map>();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData)
        {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++)
            {
                Map map = new HashMap();
                map.put("IID", jsonArr.getJSONObject(i).optString("IID"));
                map.put("ILOGINNAME", jsonArr.getJSONObject(i).optString("ILOGINNAME"));
                map.put("IFULLNAME", jsonArr.getJSONObject(i).optString("IFULLNAME"));
                map.put("ISEXTEND", jsonArr.getJSONObject(i).optString("ISEXTEND"));
                map.put("ISSENDALERM", jsonArr.getJSONObject(i).get("ISSENDALERM"));
                map.put("ISSENDEMAIL", jsonArr.getJSONObject(i).get("ISSENDEMAIL"));
                map.put("ISSENDMESSAGE", jsonArr.getJSONObject(i).get("ISSENDMESSAGE"));
                dataList.add(map);
            }
        }
        return dataList;
    }

    /**
     * 
     * @Title: jsonTranslateTTaudit   
     * @Description: json转换翻译   
     * @param jsonData
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月24日 上午10:11:45
     */
    public List<TTauditModel> jsonTranslateTTaudit ( String jsonData,String realExecDataCenter )
    {
        List<TTauditModel> dataList = new ArrayList<TTauditModel>();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData)
        {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++)
            {
                TTauditModel model = new TTauditModel();
                model.setIid(jsonArr.getJSONObject(i).optString("iid") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("iid")) ? -1l
                                : Long.valueOf(jsonArr.getJSONObject(i).optString("iid")));
                model.setItaskName(jsonArr.getJSONObject(i).optString("itaskName"));
                model.setItaskRunTime(jsonArr.getJSONObject(i).optString("itaskRunTime"));
                model.setItaskCommand(jsonArr.getJSONObject(i).optString("itaskCommand"));
                model.setItaskGroup(jsonArr.getJSONObject(i).optString("itaskGroup"));
                model.setItaskDesc(jsonArr.getJSONObject(i).optString("itaskDesc"));
                model.setIip(jsonArr.getJSONObject(i).optString("iip"));
                model.setIenable(jsonArr.getJSONObject(i).optString("ienable") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("ienable")) ? 0
                                : Integer.valueOf(jsonArr.getJSONObject(i).optString("ienable")));
                model.setIserverIp(jsonArr.getJSONObject(i).optString("iserverIp"));
                model.setIgid(jsonArr.getJSONObject(i).optString("igid") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("igid")) ? 0l
                                : Long.valueOf(jsonArr.getJSONObject(i).optString("igid")));
                model.setIoldServerIp(jsonArr.getJSONObject(i).optString("ioldserverip"));

                model.setItimeoutMins(jsonArr.getJSONObject(i).optString("itimeoutMins") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("itimeoutMins")) ? "0"
                                : jsonArr.getJSONObject(i).optString("itimeoutMins"));

                model.setIpause(jsonArr.getJSONObject(i).optString("ipause") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("ipause")) ? 0
                                : Integer.valueOf(jsonArr.getJSONObject(i).optString("ipause")));

                model.setItaskType(jsonArr.getJSONObject(i).optString("itaskType") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("itaskType")) ? 0
                                : Integer.valueOf(jsonArr.getJSONObject(i).optString("itaskType")));
                Date date1 = DateUtil.strToDate(DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00");
                Date date2 = DateUtil.strToDate(DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59");
                int tnum = TTManageService.getInstance()
                        .getTotalByCron(jsonArr.getJSONObject(i).optString("itaskRunTime"), date1, date2);
                if (tnum == 1)
                {
                    model.setItaskType(Constants.TIME_TASK_TYPE_ONETIME);
                } else
                {
                    model.setItaskType(Constants.TIME_TASK_TYPE_MORETIME);
                }

                model.setIstatus(jsonArr.getJSONObject(i).optString("istatus") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("istatus")) ? 0
                                : Integer.valueOf(jsonArr.getJSONObject(i).optString("istatus")));
                model.setInfoenable(jsonArr.getJSONObject(i).optString("infoenable") == null
                        || "".equals(jsonArr.getJSONObject(i).optString("infoenable")) ? 0
                                : Integer.valueOf(jsonArr.getJSONObject(i).optString("infoenable")));
                int taskleval=1;
                String level = jsonArr.getJSONObject(i).optString("tasklevel");
                if(null!=level && !"".equals(level)){
                    taskleval = Integer.parseInt(level);
                }
                model.setTasklevel(taskleval);
                if(StringUtils.isNotBlank(realExecDataCenter)){
                    model.setExecuteCenter(realExecDataCenter);
                }else{
                    model.setExecuteCenter(jsonArr.getJSONObject(i).optString("executeCenter"));
                }
                model.setItaskName(jsonArr.getJSONObject(i).optString("itaskName"));
                model.setExecUser(jsonArr.getJSONObject(i).optString("execUser"));
                dataList.add(model);
            }
        }
        return dataList;
    }

    /**
     * 
     * @Title: saveTTaudit   
     * @Description: 保存任务维护数据   
     * @param jsonData
     * @param sysType
     * @author: lei_wang 
     * @throws Exception 
     * @date:   2019年12月26日 下午1:51:27
     */
    public void saveTTaudit ( String jsonData, int sysType ) throws Exception
    {
        Connection conn = null;
        TTauditManage manage = new TTauditManage();
        try
        {
            List list = jsonTranslateTTaudit(jsonData,"");
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (!list.isEmpty())
            {
                for (int i = 0; i < list.size(); i++)
                {
                    TTauditModel model = (TTauditModel) list.get(i);
                    if (model.getIid() == -1)
                    {
                        manage.addTTaudit(conn, model);
                    } else
                    {
                        model.setIstatus(0);
                        manage.updateTTaudit(conn, model);
                    }
                }
            }
            conn.commit();
        } catch (Exception e)
        {
            if (conn != null)
            {
                conn.rollback();
            }
            log.error("TTauditService.saveTTaudit is error", e);
            throw new Exception();
        } finally
        {
            DBResource.closeConnection(conn, "saveTTaudit", log);
        }
    }

    /**
     * 
     * @Title: saveTTaudit   
     * @Description: 保存导入的任务维护数据  
     * @param excelList
     * @param sysType
     * @throws SQLException      
     * @author: lei_wang 
     * @date:   2019年12月26日 下午1:55:21
     */
    public Map saveTTaudit ( List<TTauditModel> excelList, int sysType ) throws SQLException
    {
        Connection conn = null;
        TTauditManage manage = new TTauditManage();
        Date date1 = DateUtil.strToDate(DateUtil.format(new Date(), "yyyy-MM-dd") + " 00:00:00");
        Date date2 = DateUtil.strToDate(DateUtil.format(new Date(), "yyyy-MM-dd") + " 23:59:59");
        Map remap = new HashMap<String, String>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (!excelList.isEmpty())
            {
                // 重复名称及状态
                Map<String, String> map = TTauditManage.getInstance().checkTaskNames(excelList, conn);
                // 保存状态为草稿、已发布重复的名称
                List<String> draftState = new ArrayList<String>();
                // 保存审核中的重复的名称
                List<String> elseState = new ArrayList<String>();
                for (int i = 0; i < excelList.size(); i++)
                {
                    TTauditModel model = excelList.get(i);
                    int tnum = TTManageService.getInstance().getTotalByCron(model.getItaskRunTime(), date1, date2);
                    if (tnum == 1)
                    {
                        model.setItaskType(Constants.TIME_TASK_TYPE_ONETIME);
                    } else
                    {
                        model.setItaskType(Constants.TIME_TASK_TYPE_MORETIME);
                    }
                    if (!map.isEmpty())
                    {
                        for (Map.Entry<String, String> entry : map.entrySet())
                        {
                            // 重复任务名称
                            String[] reNames = String.valueOf(entry.getKey()).split(",");
                            // 重复任务状态
                            String[] reStatus = String.valueOf(entry.getValue()).split(",");
                            int count = 0;
                            // 审核中任务不允许修改
                            for (int j = 0; j < reStatus.length; j++)
                            {
                                if (reNames[j].equals(excelList.get(i).getItaskName())
                                        && !"1".equals(reStatus[j]))
                                {
                                    manage.updateTTauditByTaskNameEx(conn, model);
                                    draftState.add(reNames[j]);
                                    count++;
                                    break;
                                } else if (reNames[j].equals(excelList.get(i).getItaskName())
                                        && "1".equals(reStatus[j]))
                                {
                                    elseState.add(reNames[j]);
                                    count++;
                                    break;
                                }
                            }
                            if (count == 0)
                            {
                                manage.importAddTTaudit(conn, model);
                            }
                        }
                    }
                }
                remap.put("draftState", draftState);
                remap.put("elseState", elseState);
            }
            conn.commit();
        } catch (DBException e)
        {
            log.error("TTauditService.saveTTaudit is DBException", e);
        } catch (RepositoryException e)
        {
            log.error("TTauditService.saveTTaudit is RepositoryException", e);
        } finally
        {
            DBResource.closeConnection(conn, "saveTTaudit", log);
        }
        return remap;
    }

    // 检查数据库中已存在的任务名称
    public Map checkTaskNamesDb ( List names, int sysType ) throws RepositoryException
    { 
        Connection conn = null;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            map = TTauditManage.getInstance().checkTaskNames(names, conn);
        } catch (Exception e)
        {
            log.error("TTauditService.checkTaskNamesDb is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "checkTaskNamesDb", log);
        }
        return map;
    }

    // 校验cron表达式
    public String validateCronForTaskList ( List<TTauditModel> taskList )
    {
        return TTauditManage.getInstance().validateCronForTaskList(taskList);
    }

    // 通过taskid查iip
    public TTauditModel getSelIpidStrByTaskId ( long taskid, int sysType ) throws RepositoryException
    {
        Connection conn = null;
        TTauditModel model = new TTauditModel();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            model = TTauditManage.getInstance().getTTauditOne(taskid, conn);
        } catch (Exception e)
        {
            log.error("TTauditService.getSelIpidStrByTaskId is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getSelIpidStrByTaskId", log);
        }
        return model;
    }

    // 通过taskid修改iip
    public void updateTaskIpByid ( TTauditModel model, int sysType ) throws RepositoryException
    {
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            TTauditManage.getInstance().updateTTauditByid(conn, model);
            conn.commit();
        } catch (DBException e)
        {
            log.error("TTAuditService.updateTaskIpByid DBException error：" ,e);
        } catch (RepositoryException e)
        {
            log.error("TTAuditService.updateTaskIpByid RepositoryException error：" ,e);
        } catch (Exception e)
        {
            log.error("TTAuditService.updateTaskIpByid Exception error：" ,e);
        } finally
        {
            DBResource.closeConnection(conn, "updateTaskIpByid", log);
        }

    }

    // 拼接删除后的iipid
    public String delWithDelIpidStr ( String oldIpidStr, String iipStr )
    {
        String newIpidStr = "";
        if (null == iipStr || "".equals(iipStr.trim()))
        {
            newIpidStr = oldIpidStr;
        } else if (null == oldIpidStr || "".equals(oldIpidStr.trim()))
        {
            newIpidStr = "";
        } else
        {
            newIpidStr = this.makeips(oldIpidStr, iipStr).toString();
        }
        if (newIpidStr.length() > 0)
        {
            newIpidStr = newIpidStr.substring(1);
        }
        return newIpidStr;
    }

    public void deleteTTaudit ( String iids, int sysType, String jsondata ) throws SQLException
    {
        Connection conn = null;
        TTauditManage manage = new TTauditManage();
        try
        {

            String[] iid = iids.split(",");
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (iid != null && iid.length > 0)
            {
                for (int i = 0; i < iid.length; i++)
                {
                    manage.deleteTTaudit(conn, Long.valueOf(iid[i]));
                }
            }
            conn.commit();
        } catch (Exception e)
        {
            if (conn != null)
            {
                conn.rollback();
            }
            log.error("TTauditService.deleteTTaudit is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "deleteTTaudit", log);
        }
    }

    /**
     * 
     * @Title: exportTTauditList   
     * @Description: 查询导出数据 
     * @param start
     * @param limit
     * @param sysType
     * @param userId
     * @param condition
     * @param ip
     * @param taskType
     * @param groupId
     * @param stateCode
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月26日 上午10:36:59
     */
    public Map exportTTauditList ( Integer start, Integer limit, int sysType, Long userId, String condition, String ip,
            Integer taskType, Integer groupId, Integer stateCode, String iids,boolean isshowcenter,String taskstate,String tasklevelCombo )
    {
        Connection conn = null;
        TTauditManage manage = new TTauditManage();
        String permFilterSql = "";
        int dbtype = JudgeDB.IEAI_DB_TYPE;
        StringBuilder sbf = new StringBuilder();

        // 处理权限条件
        String inpermPrjIdsStr = this.getInpermPrjIdsStr(userId, sysType).toString();
        if (null != inpermPrjIdsStr)
        {
            if ("".equals(inpermPrjIdsStr))// 没有任何权限
            {
                permFilterSql = " and  TTG.IID is null ";
            } else// 权限内工程过滤
            {
                permFilterSql = " and  TTG.IID in (" + inpermPrjIdsStr + ")";
            }
        }
        // 处理查询条件
        if ("".equals(condition) || condition == null)
        {
            sbf.append(" WHERE TTI.IGID = TTG.IID  " + permFilterSql + "  ) AAA ");
            sbf.append(
                " WHERE 1=1  AND IID NOT IN  (SELECT DISTINCT TID AS ID FROM IEAI_TIMETASK_SUBINFO WHERE TASKSTATUS=2 OR (OPTTYPE=4 AND TASKSTATUS = 1))  ");
        } else if (condition.contains("_") || condition.contains("%"))
        {
            condition = condition.replaceAll("_", "/_");
            condition = condition.replaceAll("%", "/%");
            sbf.append(
                " WHERE  TTI.IID NOT IN  (SELECT DISTINCT TID AS ID FROM IEAI_TIMETASK_SUBINFO WHERE TASKSTATUS=2 OR (OPTTYPE=4 AND TASKSTATUS = 1))  AND TTI.IGID = TTG.IID "
                        + permFilterSql + " )  AAA ");
            sbf.append("WHERE  (ITASKNAME LIKE '%" + condition + "%'  escape '/' ");
            sbf.append(" or  ITASKRUNTIME LIKE '%" + condition + "%'  escape '/' ");
            sbf.append(" or  ITASKCOMMAND LIKE '%" + condition + "%' escape  '/' ");
            sbf.append(" or  ITASKDESC LIKE '%" + condition + "%' escape '/'  ");
            sbf.append(" ) ");
        } else
        {
            sbf.append(
                " WHERE TTI.IID  NOT IN  (SELECT DISTINCT TID AS ID FROM IEAI_TIMETASK_SUBINFO WHERE TASKSTATUS=2 OR (OPTTYPE=4 AND TASKSTATUS = 1))  AND TTI.IGID = TTG.IID "
                        + permFilterSql + " )  AAA ");
            sbf.append("WHERE (ITASKNAME  LIKE '%" + condition + "%'");
            sbf.append(" or ITASKRUNTIME  LIKE '%" + condition + "%'");
            sbf.append(" or ITASKCOMMAND  LIKE '%" + condition + "%'");
            sbf.append(" or ITASKDESC  LIKE '%" + condition + "%'");
            sbf.append(" ) ");
        }

        if (null != taskType && taskType != 0)
        {
            sbf.append(" and ITASKTYPE =  ? " );
        }

        if (null != groupId && groupId != 0)
        {
            sbf.append(" and IGID = ? " );
        }

        if (null != stateCode && stateCode != 0)
        {
            sbf.append(" and ISTATUS =  ? " );
        }
        
        if (!(taskstate == null || "".equals(taskstate)))
        {
            if (100 == Integer.valueOf(taskstate))
            { // 暂停
                sbf.append(" and INFOENABLE = 0  AND INFOPAUSE=1 ");
            } else if (0 == Integer.valueOf(taskstate))
            {
                // 失效
                sbf.append(" and INFOENABLE = 0  AND INFOPAUSE=0 ") ;
            } else
            {
                sbf.append(" and INFOENABLE  = ? " );
            }

        }
        // 处理任务级别查询条件
        if (!(tasklevelCombo == null || "".equals(tasklevelCombo)))
        {
            sbf.append(" and TASKLEVEL = ? ");
        }
     // id条件
        if (!(iids == null || "".equals(iids)))
        {
            sbf.append(" and IID in"+"( ");
            String[] iidArray = iids.split(",");
            for(int i = 0;i < iidArray.length;i++){
                sbf.append("?");
                if(i < iidArray.length - 1){
                    sbf.append(",");
                }
            }
            sbf.append(")"+"");
        }
        

        StringBuilder ipwhere = new StringBuilder();
        if (null != ip && !"".equals(ip))
        {
            if (DBManager.Orcl_Faimily())
            {
                // oralce
                /*ipwhere.append(" JOIN  (SELECT CP.IAGENTINFO_ID AS COMPUTERID, ");
                ipwhere.append(
                    "  CP.IAGENT_IP     AS IP,   CP.IAGENT_PORT   AS PORT,     RE.SYSTEMID AS SYSTEMIDA,  to_char(RE.COMPUTERID) as COMPUTERIDS   ");
                ipwhere.append("   FROM  IEAI_AGENTINFO CP, IEAI_SYS_RELATION RE ");
                ipwhere.append("   WHERE CP.IAGENTINFO_ID = RE.COMPUTERID   ");
                ipwhere.append(" AND CP.IAGENT_IP like '%" + ip + "%'    ");
                ipwhere.append("  )  TEMP   ");
                ipwhere.append("  ON TEMP.SYSTEMIDA =A.IGID ");
                ipwhere.append(
                    " AND TEMP.COMPUTERIDS in (   select regexp_substr(A.IIP, '[^,]+', 1, level) from dual ");
                ipwhere.append("  connect by regexp_substr(A.IIP, '[^,]+', 1, level) is not null  ");
                ipwhere.append(" ) ");*/
                ipwhere.append("   JOIN (select IID, dbms_lob.substr(regexp_substr(IIP, '[^,]+', 1, level)) IIP ");
                ipwhere.append("  from IEAI_TIMETASK_AUDIT ");
                ipwhere.append("   connect by level <= regexp_count(IIP, ',') + 1 " );
                ipwhere.append("  and IID = prior IID ");
                ipwhere.append(" and prior dbms_random.value is not null)  TA ON TA.IID = A.IID ");
                ipwhere.append("  AND TA.IIP IN (SELECT CP.IAGENTINFO_ID AS COMPUTERID ");
                ipwhere.append("  FROM IEAI_AGENTINFO CP ");
                ipwhere.append("  WHERE CP.IAGENT_IP like '%" + ip + "%' ");
                ipwhere.append(" ) ");

            } else if (2 == dbtype)
            {
                ipwhere.append("   JOIN (SELECT CP.IAGENTINFO_ID AS COMPUTERID, ");
                ipwhere.append("  CP.IAGENT_IP     AS IP, ");
                ipwhere.append("  CP.IAGENT_PORT   AS PORT,  ");
                ipwhere.append("  RE.SYSTEMID AS SYSTEMIDA, ");
                ipwhere.append("  to_char(RE.COMPUTERID) as COMPUTERIDS  ");
                ipwhere.append("   FROM IEAI_AGENTINFO CP, IEAI_SYS_RELATION RE ");
                ipwhere.append("  WHERE CP.IAGENTINFO_ID  = RE.COMPUTERID   ");
                ipwhere.append("  AND CP.IAGENT_IP like '%" + ip + "%' ");
                ipwhere.append("   )  TEMP    ");
                ipwhere.append("   ON TEMP.SYSTEMIDA =A.IGID ");
                ipwhere.append("  WHERE  A.IID = TEMPA.ID ");
                ipwhere.append("  AND TEMP.COMPUTERIDS =TEMPA.result1  ");

            } else if (3 == dbtype)
            {
                // mysql
                ipwhere.append(" JOIN  (SELECT CP.IAGENTINFO_ID AS COMPUTERID, " + " CP.IAGENT_IP     AS IP, ");
                ipwhere.append("   CP.IAGENT_PORT   AS PORT, " + "  RE.SYSTEMID AS SYSTEMIDA,  ");
                ipwhere.append("   RE.COMPUTERID AS COMPUTERIDS ");

                ipwhere.append("  FROM IEAI_AGENTINFO CP,  IEAI_SYS_RELATION RE ");
                ipwhere.append("   WHERE CP.IAGENTINFO_ID = RE.COMPUTERID    ");
                ipwhere.append("   AND CP.IAGENT_IP  like '%" + ip + "%'  ");
                ipwhere.append("    )  TEMP , ");
                ipwhere.append(
                    "                  (select a.IID,substring_index(substring_index(a.col,',',b.help_topic_id+1),',',-1) AS RESULT1  ");
                ipwhere.append("                      from ");
                ipwhere.append("                         (select IIP as col,iid from IEAI_TIMETASK_AUDIT) as a ");
                ipwhere.append("                      join   mysql.help_topic as b ");
                ipwhere.append(
                    "                       on b.help_topic_id < (char_length(a.col) - char_length(replace(a.col,',',''))+1) ");
                ipwhere.append("                  ) TEMPA WHERE ");
                ipwhere.append("                 TEMP.SYSTEMIDA =A.IGID    AND  TEMPA.IID= A.IID ");
                ipwhere.append("               AND  TEMP.COMPUTERIDS = TEMPA.RESULT1");
            }
        }

        String sqlList = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList = "select  A.*,B.TASKCOUNT  from  (  SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME , ifnull(INFO.ENABLE, -1) AS INFOENABLE,  ifnull(INFO.PAUSE, -1) AS INFOPAUSE "
                    + "FROM IEAI_TIMETASK_AUDIT TTI  LEFT JOIN IEAI_TIMETASK_INFO INFO  ON INFO.ID = TTI.IID,   IEAI_PROJECT  TTG " + sbf.toString() + ")A  "
                    + "LEFT JOIN (SELECT T2.TASKINFOID , COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY T2.TASKINFOID) B ON A.IID = B.TASKINFOID " + ipwhere.toString()
                    + " order by  A.IGID,  A.IID DESC ";
        } else if (DBManager.Orcl_Faimily())
        {
            sqlList = "select  A.*,B.TASKCOUNT from  ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME, nvl(INFO.ENABLE, -1) AS INFOENABLE, nvl(INFO.PAUSE, -1) AS INFOPAUSE "
                    + "FROM  IEAI_TIMETASK_AUDIT TTI  LEFT JOIN IEAI_TIMETASK_INFO INFO  ON INFO.ID = TTI.IID, IEAI_PROJECT  TTG " + sbf.toString() + "  )A  "
                    + "LEFT JOIN (SELECT  T2.TASKINFOID ,COUNT(T2.ID)  TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                    + "GROUP BY T2.TASKINFOID) B ON A.IID = B.TASKINFOID " + ipwhere.toString()
                    + " order by  A.IGID,  A.IID DESC ";
        } else
        {
            if (null != ip && !"".equals(ip))
            {
                sqlList = "select  A.*,B.TASKCOUNT from (  SELECT   n.ID ,  substr(str,ori,case pos-ori when -1 then -111 else pos - ori end) as result1 FROM n ) TEMPA , ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME ,COALESCE(INFO.ENABLE, -1) AS INFOENABLE, COALESCE(INFO.PAUSE, -1) AS INFOPAUSE "
                        + "FROM IEAI_TIMETASK_AUDIT TTI  LEFT JOIN IEAI_TIMETASK_INFO INFO  ON INFO.ID = TTI.IID, IEAI_PROJECT TTG " + sbf.toString() + ")A  "
                        + "LEFT JOIN (SELECT T2.TASKINFOID ,COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                        + "GROUP BY T2.TASKINFOID) B ON A.IID = B.TASKINFOID  " + ipwhere.toString()
                        + "order by  A.IGID,  A.IID DESC";
            } else
            {
                sqlList = "select  A.*,B.TASKCOUNT from  ( SELECT  AAA.*  FROM (SELECT TTI.*, TTG.INAME AS GNAME, COALESCE(INFO.ENABLE, -1) AS INFOENABLE,  COALESCE(INFO.PAUSE, -1) AS INFOPAUSE "
                        + "FROM IEAI_TIMETASK_AUDIT TTI  LEFT JOIN IEAI_TIMETASK_INFO INFO  ON INFO.ID = TTI.IID, IEAI_PROJECT TTG " + sbf.toString() + ")A  "
                        + "LEFT JOIN (SELECT T2.TASKINFOID ,COUNT(T2.ID) TASKCOUNT FROM IEAI_TIMETASK_RUNTIME TRUN ,IEAI_TIMETASK_INSTANCE T2 WHERE T2.ID=TRUN.TASKID  "
                        + "GROUP BY T2.TASKINFOID) B ON A.IID = B.TASKINFOID  " + "order by  A.IGID,  A.IID DESC";
            }

        }
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            //sql注入优化处理
            List list = manage.queryForExport(conn, sqlList, start, limit, ip,isshowcenter,taskType,groupId,stateCode,taskstate,tasklevelCombo,iids);
            map.put("dataList", list);
        } catch (Exception e)
        {
            log.error("TTauditService.exportTTauditList is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "exportTTauditList", log);
        }
        return map;
    }

    /**
     * 
     * @Title: exportTaskMonitorExcel   
     * @Description: 导出查询到的任务维护数据    
     * @param response
     * @param start
     * @param limit
     * @param sname
     * @param sysType
     * @param taskMonitorModel      
     * @author: lei_wang 
     * @date:   2019年12月26日 上午10:49:56
     */
    public void exportTTauditExcel ( HttpServletResponse response, Integer start, Integer limit, int sysType,
            Long userId, String condition, String ip, Integer taskType, Integer groupId, Integer stateCode, String iids,String taskstate,String tasklevelCombo )
    {
        response.setContentType("application/vnd.ms-excel; GBK");
        response.setHeader("Content-disposition", "attachment;filename=" + PoiUtil.getRandomFileName() + ".xls");
        response.setCharacterEncoding(UTF_8);
        Map<String, String> map = new LinkedHashMap();
        map.put("任务名称", "itaskName");
        map.put("周期", "itaskRunTime");
        map.put("执行脚本", "itaskCommand");
        map.put("描述", "itaskDesc");
        map.put("任务组", "itaskGroup");
        map.put("ip", "iip");
        map.put("超时时长", "itimeoutMins");
        map.put("任务分级", "levelToString");
        boolean isShowCenter =Environment.getInstance().getBooleanConfigNew(Environment.TT_SHOW_DATACENTER_SWITCH,true);//ConfigReader.getInstance().getBooleanProperties("timetask.showcenter.switch",true);
        if(isShowCenter){
            map.put("数据中心", "executeCenter");
        }
        map.put("复核状态", "istatusToString");
        map.put("任务类型", "itaskTypeToString");
        map.put("任务状态", "enableToString");
        
        try
        {
            Map auditMap = exportTTauditList(start, limit, sysType, userId, condition, ip, taskType, groupId,
                stateCode, iids,isShowCenter,taskstate,tasklevelCombo);
            List<TTauditModel> list = (List<TTauditModel>) auditMap.get("dataList");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String res = simpleDateFormat.format(new Date());
            String fileName = "任务维护" + res;
            OutputStream output = response.getOutputStream();
            response.reset();
            // 设置响应头信息
            response.setHeader("Content-disposition",
                "attachment;filename=" + new String(fileName.getBytes(), "iso8859-1") + (Environment.getInstance().getExcelExportEtSwitch()?".et":".xls"));
            // 通用方法 将集合中并且符合一定条件的数据以EXCEL 的形式输出
            PoiUtil.exportExcel("任务维护", map, list, output);
        } catch (JspException e)
        {
            log.error("exportStaffwlExcel is JspException", e);
        } catch (IOException e)
        {
            log.error("exportStaffwlExcel is IOException", e);
        }
    }

    /**
     * 
     * @Title: importExcel   
     * @Description:导入查询到的任务维护数据    
     * @param filename
     * @param fis
     * @param type
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月26日 下午1:21:52
     */
    public Map importExcel ( String filename, FileInputStream fis, int type )
    {
        Map resmap = new HashMap();
        List list = new ArrayList();

        Map methodMap = new HashMap();
        methodMap.put("任务名称", "itaskName");
        methodMap.put("周期", "itaskRunTime");
        methodMap.put("执行脚本", "itaskCommand");
        methodMap.put("描述", "itaskDesc");
        methodMap.put("任务组", "itaskGroup");
        methodMap.put("ip", "iip");
        methodMap.put("超时时长", "itimeoutMins");
        Map groupMap = new HashMap();
        try
        {
            groupMap = TTManageService.getInstance().getAllSysNameList(type);
        } catch (RepositoryException e)
        {
            LogPrintUtil.getInstance().logPrintErr(log, e);
        }

        ImportExcelImpl m = ImportExcelImpl.getInstance();
        Map returnMap = m.readAuditExceltoArray(filename, fis, methodMap, groupMap);
        // 设备 信息sheet页组织数据

        TTauditModel cBean = new TTauditModel();
        cBean.setTasklevel(1);
        if (m.isExistErroMsg(returnMap))
        {
            resmap.put("errormsg", returnMap.get("errormsg"));
        } else
        {
            String[] method = (String[]) returnMap.get("rowColums");
            Map map = m.getBeanList(cBean, method, returnMap);
            if (!m.isExistErroMsg(map))
            {
                list = (List) map.get("resList");
            }
            String[] taskNames = new String[list.size()];
            Map<String, String> groupName = new HashMap<String, String>();
            for (int i = 0; i < list.size(); i++)
            {
                TTauditModel cbb = (TTauditModel) list.get(i);
                taskNames[i] = cbb.getItaskName();
                if ("".equals(cbb.getItaskGroup()))
                {
                    cbb.setIgid(0);
                } else
                {
                    groupName.put(cbb.getItaskGroup(), cbb.getItaskGroup());
                    int gid = 0;
                    try
                    {
                        gid = Integer.parseInt(groupMap.get(cbb.getItaskGroup()).toString());
                    } catch (Exception e)
                    {
                        gid = 0;
                    }
                    cbb.setIgid(gid);
                }
            }
            resmap.put("beanlist", list);
            resmap.put("tasknames", taskNames);
            String[] gnames = new String[groupName.size()];
            int i = 0;
            for (String string : groupName.keySet())
            {
                gnames[i] = string;
                i++;
            }
            resmap.put("gname", gnames);
        }

        return resmap;
    }

    /**
     * 
     * @Title: checkTaskNamesExcel   
     * @Description: 检查Excel中组内重名任务   
     * @param excelList
     * @param type
     * @return      
     * @author: lei_wang 
     * @date:   2019年12月26日 下午1:41:07
     */
    public String checkTaskNamesExcel ( List excelList )
    {
        String returnNames = "";
        StringBuilder build = new StringBuilder();
        List okList = new ArrayList();
        TTauditModel bean = null;
        TTauditModel okBean = null;
        for (int i = 0; excelList != null && i < excelList.size(); i++)
        {
            bean = (TTauditModel) excelList.get(i);
            boolean isOk = true;// 组内名称不重复
            for (int m = 0; m < okList.size(); m++)
            {
                okBean = (TTauditModel) okList.get(m);
                if (bean.getItaskName().equals(okBean.getItaskName())
                        && bean.getItaskGroup().equals(okBean.getItaskGroup()))
                {
                    isOk = false;
                    break;
                }
            }
            if (isOk)
            {
                okList.add(bean);
            } else
            {
                build.append("," + bean.getItaskName());
            }
        }
        if (build.length() > 1)
        {
            returnNames = build.toString().substring(1);
        }
        return returnNames;
    }

    /**
     * 
     * @Title: updateTaskStastByid   
     * @Description: 根据id修改任务状态  为审核中
     * @param jsonData
     * @param sysType
     * @throws SQLException      
     * @author: lei_wang 
     * @date:   2019年12月29日 上午8:55:59
     */
    public void updateTaskStatusByid ( String jsonData, int sysType ) throws SQLException
    {

        Connection conn = null;
        TTauditManage manage = new TTauditManage();
        try
        {
            List<TTauditModel> list = jsonTranslateTTaudit(jsonData,"");
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            if (!list.isEmpty())
            {
                for (int i = 0; i < list.size(); i++)
                {
                    TTauditModel model = list.get(i);
                    manage.updateTTaudit(conn, model);
                }
            }
            conn.commit();
        } catch (Exception e)
        {
            if (conn != null)
            {
                conn.rollback();
            }
            log.error("TTauditService.updateTaskStatusByid is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "updateTaskStatusByid", log);
        }

    }

    /**
     * 
     * @Title: userGroupById   
     * @Description: 根据用户ID 获取与此用户同组的其他用户ID:名称   
     * @param sysType
     * @param userId
     * @return      
     * @author: lei_wang 
     * @date:   2020年1月7日 上午8:24:03
     */
    public Map userGroupById ( int sysType, long userId )
    {
        Connection conn = null;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            map = TTauditManage.getInstance().userGroupById(conn, userId);
        } catch (Exception e)
        {
            log.error("TTauditService.userGroupById is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "userGroupById", log);
        }
        return map;
    }
    private StringBuilder makeips ( String oldIpidStr, String iipStr )
    {
        StringBuilder ips = new StringBuilder();
        String[] oldIpArr = oldIpidStr.split(",");
        String[] iipArr = iipStr.split(",");
        for (int i = 0; i < oldIpArr.length; i++)
        {
            String oneIpId = oldIpArr[i];
            boolean needsDel = false;
            for (int j = 0; j < iipArr.length; j++)
            {
                if (oneIpId.trim().equals(iipArr[j].trim()))
                {
                    needsDel = true;
                    break;
                }
            }
            if (!needsDel)
            {
                ips.append("," + oneIpId.trim());
            }
        }
        return ips;
    }
    
    public List<Map> getSysRelation ( int sysType, List<TimetaskInfoBean> infoBeanList )
    {
        Connection conn = null;
        
        List<Map> resList = new ArrayList<Map>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            for (TimetaskInfoBean timetaskInfoBean : infoBeanList)
            {
                String taskAgentIp=timetaskInfoBean.getIp().replace("\n", " ");
                List sysRelationList = new ArrayList();
                List agentipList = new ArrayList();
                Map sysmap = TTauditManage.getInstance().getSysRelation(conn, timetaskInfoBean.getGroupId());
                //当前业务系统下绑定的IP
                List list = (ArrayList)sysmap.get("AGENINFO");
                //导入的IP数组
                
                String[] agentips = taskAgentIp.split(" ");
                List<String> impList = new ArrayList();
                for(String str:agentips){
                    if(str!=null && str.length()!=0){
                        impList.add(str);
                    }
                }
                for (String str : impList)
                {
                    boolean sysRelation = true;
                    String agentip = str.trim();
                    if (agentip.length() == 0)
                    {
                        continue;
                    }
                    if (agentip.indexOf(":") == -1)
                    {
                        agentip = agentip + ":" + TimetaskConstants.DEFALUT_POT;
                    }
                    if (!agentipList.contains(agentip))
                    {
                        agentipList.add(agentip);
                        if (!list.contains(agentip))
                        {
                            // 业务系统中不存在此IP
                            sysRelation = false;
                        }
                        sysRelationList.add(sysRelation);
                    }
                }
                //验证的业务系统名称
                sysmap.put("agentip", agentipList);
                sysmap.put("sysRelation", sysRelationList);
                resList.add(sysmap);
            }
        } catch (Exception e)
        {
            log.error("TTauditService.getSysRelation is error", e);

        } finally
        {
            DBResource.closeConnection(conn, "getSysRelation", log);
        }
        return resList;
    }

    public void dowloadTtauditTemplate ( HttpServletResponse response ,String[] columns )
    {
        HSSFWorkbook book = new HSSFWorkbook();
        HSSFSheet sheet = book.createSheet("任务维护");
        HSSFRow row = null;
        HSSFCell cell = null;
        HSSFCellStyle cellStyleTitle = book.createCellStyle(); 
        cellStyleTitle.setFillPattern(SOLID_FOREGROUND); 
        cellStyleTitle.setFillForegroundColor(IndexedColors.BLACK.getIndex());
        cellStyleTitle.setAlignment(HorizontalAlignment.CENTER);

        HSSFCellStyle textStyle = book.createCellStyle();
        HSSFDataFormat format = book.createDataFormat();
        textStyle.setDataFormat(format.getFormat("@"));

        HSSFFont bold = book.createFont();
        bold.setBold(true);
        bold.setColor(IndexedColors.WHITE.getIndex());
        cellStyleTitle.setFont(bold);
        
        row = sheet.createRow(0);
        for (int i=0;i<columns.length;i++){ 
            sheet.setColumnWidth((short) i, (short) 8000);
            cell = row.createCell((short) i);           
            cell.setCellStyle(cellStyleTitle);
            cell.setCellValue(columns[i]);  
        }   
                  
        try {           
            OutputStream out = response.getOutputStream();  
            response.reset();
            response.setContentType("application/msexcel;charset=GBK");             
            response.addHeader("Content-Disposition", "attachment;filename=\"" +
                        new String(("任务维护模板"+ (Environment.getInstance().getExcelExportEtSwitch()?".et":".xls")).getBytes("GBK"), "ISO8859_1") + "\"");
            book.write(out);            
            out.flush();
            out.close(); 
        }catch (Exception e) {          
            log.info("导出失败:"+e.getMessage(),e);
        }      
    }

    public List getNewCenterByip ( String iipStr ) throws RepositoryException
    {
        return TTauditManage.getInstance().getDataCenterByIp(iipStr);
    }

    public String getAgentListBySysId ( String batchComputerName, long systemId, int type ) throws RepositoryException
    { 
        String uncheckips = "";
        String[]  computerArr = batchComputerName.split("\n");
        List list =  TTauditManage.getInstance().getAgentListBySysId(systemId,type);
        for(int i = 0; i<computerArr.length;i++){
            if(!list.contains(computerArr[i])){
                if(i==computerArr.length-1){
                    uncheckips+=computerArr[i];
                }else{
                    uncheckips+=computerArr[i]+"\n";
                }
            }
        }
        return uncheckips;
    }

    public String filterAllIp ( String allip, String uncheckip )
    {
        List newlist = new ArrayList();
        String ipnewstr = "";
        String[] allipArr = allip.split("\n");
        String[] uncheckipArr = uncheckip.split("\n");
        List<String> alliplist = Arrays.asList(allipArr);
        List<String> uncheckiplist = Arrays.asList(uncheckipArr);
        for(String str : alliplist){
            if(!uncheckiplist.contains(str)){
                newlist.add(str);
            }
        }
        for(int i = 0;i< newlist.size();i++){
            if(i == newlist.size()-1){
                ipnewstr= ipnewstr+newlist.get(i);
            }else{
                ipnewstr= ipnewstr+newlist.get(i)+"\n";
            }
            
        }
        return ipnewstr;
    }

    public void updateTTaudit ( TTauditModel model, int sysType ) throws RepositoryException, SQLException
    {
        TTauditManage.getInstance().updateTTaudit(model,sysType);
        
    }

    public Map alertManList ( String iid,Integer start,Integer limit,String groupId,String igid, String userName ) throws SQLException, RepositoryException
    {
        
        return TTauditManage.getInstance().alertManList(iid,start,limit,groupId,igid, userName);
    }

    public void saveAlertMan ( List<Map> datalist, String taskid,String taskname,String loginname,String groupId ) throws SQLException
    {
        
        try{
            
            boolean istrue = TTauditManage.getInstance().deleteAlertMan(taskid,groupId);
            if(istrue){
                TTauditManage.getInstance().saveAlertMan(datalist,taskid,taskname,loginname,groupId);
            }
        }catch(Exception e){
            log.error("TTauditService.saveAlertMan Exception error:",e);
        }
        
        
    }
}
