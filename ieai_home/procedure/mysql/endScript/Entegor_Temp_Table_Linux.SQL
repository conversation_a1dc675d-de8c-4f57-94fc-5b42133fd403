	CREATE TABLE TEMP_IEAI_PERMIT_INVALIDATE (OPT_ID NUMERIC(19) NOT NULL, PERMIT_NAME VARCHAR(50) NOT NULL, OBJECT_NAME VARCHAR(255) NOT NULL, CONSTRAINT PK_TEMP_IEAI_PERMIT_INVALIDATE PRIMARY KEY(OPT_ID));
	CREATE TABLE TEMP_IEAI_PERMIT_VALIDATE (OPT_ID NUMERIC(19) NOT NULL, PERMIT_NAME VARCHAR(50) NOT NULL, OBJECT_NAME VARCHAR(255) NOT NULL, CONSTRAINT PK_TEMP_IEAI_PERMIT_VALIDATE PRIMARY KEY(OPT_ID));
	CREATE TABLE TEM_ACTIVITY_QUERY (iid int not null  PRIMARY KEY AUTO_INCREMENT,FLOWID NUMERIC(19), ACTIVITYNAME VARCHAR(255), FLOWINSNAME VARCHAR(255), STARTTIME NUMERIC(19), ENDTIME NUMERIC(19), SUNFLAG NUMERIC(19));
	CREATE TABLE TMP_FIRST_PAGE_LIST (LID  int not null  PRIMARY KEY AUTO_INCREMENT, OPT_ID NUMERIC(19) NOT NULL, IID NUMERIC(19), FLOW_ID NUMERIC(19) NOT NULL, ACT_ID NUMERIC(19) NOT NULL, TASK_ID NUMERIC(19), FLOW_INSTANCE_NAME VARCHAR(255), FLOW_NAME VARCHAR(255), ACT_NAME VARCHAR(255), ACT_DESCRIPTION VARCHAR(255), SHOULDSTARTTIME NUMERIC(19), BEGINEXCTIME NUMERIC(19), ACT_STATES VARCHAR(50), IOWNER VARCHAR(255), PRJ_NAME VARCHAR(255), ACT_TYPE VARCHAR(255), STARTUSER_NAME VARCHAR(255), ACT_ERRORTASKID NUMERIC(19), EXECACT_IREXECREQUESTID VARCHAR(255), ACT_DEF_NAME VARCHAR(255), SHOULDENDTIME DECIMAL(19, 0),SHELLNAME VARCHAR(255),SHELLPATH VARCHAR(4000),AGENTIP VARCHAR(16));
	CREATE TABLE TMP_PROJECT_LIST (IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT, OPT_ID DECIMAL(19), PRJ_NAME VARCHAR(255), UUID VARCHAR(36), PRJ_ID DECIMAL(19), PRJ_UPPERID DECIMAL(19) );
	CREATE TABLE TMP_TREE_VIEW (ID NUMERIC(19), PID NUMERIC(19), ITEM_LABEL VARCHAR(255), ITEM_LEVEL INTEGER, ORDERID INTEGER DEFAULT 0, ITEM_ORDER NUMERIC(19) DEFAULT 0, OPT_ID NUMERIC(19) NOT NULL, ITEM_COUNTS NUMERIC(19), ITEM_CONTENT VARCHAR(255) DEFAULT '0', CONSTRAINT PK_TMP_TREE_VIEW PRIMARY KEY(ID));
	CREATE TABLE TMP_USER_INVALIDATE (IID int PRIMARY KEY AUTO_INCREMENT,OPT_ID NUMERIC (19) NOT NULL,PRJ_ID NUMERIC (19) NOT NULL,PRJ_NAME VARCHAR (255),PERMIT_ID INTEGER NOT NULL,PERMIT_NAME VARCHAR (50));
	CREATE TABLE TMP_USER_VALIDATE (IID int PRIMARY KEY AUTO_INCREMENT,OPT_ID NUMERIC(19) NOT NULL, PRJ_ID NUMERIC(19) NOT NULL, PRJ_NAME VARCHAR(255), PERMIT_ID INTEGER NOT NULL, PERMIT_NAME VARCHAR(50));
	CREATE TABLE TMP_WORKFLOW_QUERY (IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT, optid NUMERIC (19),flow_id NUMERIC (19),flow_name VARCHAR (255),instance_name VARCHAR (255),	project_name VARCHAR (255),STATUS VARCHAR (50),start_user VARCHAR (255),start_time VARCHAR (20),end_time VARCHAR (20),	paid_time VARCHAR (50),	host_name VARCHAR (255),task_num INTEGER,rec_id INTEGER,prj_uuid VARCHAR (255),	obj_type INTEGER,ischeck INTEGER,prj_permit_res NUMERIC (19),	isonline NUMERIC (19),	agentip VARCHAR (50),agentport VARCHAR (50), iagenthostname VARCHAR (255), 	isystem VARCHAR (255), 	iagent_ipasname VARCHAR (255),ISCOMPENSATE  VARCHAR(255) DEFAULT NULL);
	CREATE TABLE TMP_WORKFLOW_QUERY_TEMP (IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,OPTID NUMERIC(19), FLOW_ID NUMERIC(19), TASK_NUM INTEGER, PAGE_ID INTEGER, REC_ID INTEGER, PRJ_UUID VARCHAR(255), ISCHECK SMALLINT, FLOW_NAME VARCHAR(255));
	CREATE TABLE TMP_IEAI_ACT_CFG (IID  int not null  PRIMARY KEY AUTO_INCREMENT, OPID NUMERIC(19), PROJECTUUID VARCHAR(36), FLOWNAME VARCHAR(255), ACTIDINFLOW INTEGER, ACTNAME VARCHAR(255), USHELLGROUPID VARCHAR(20), USHELLTYPE INTEGER, ACTTYPE VARCHAR(100), ACTID VARCHAR(20));
	CREATE TABLE TMP_IEAI_ACT_RELATION (IID  int not null  PRIMARY KEY AUTO_INCREMENT, OPID NUMERIC(19), PROJECTUUID VARCHAR(36), FLOWNAME VARCHAR(255), FORWARDACTIDINFLOW INTEGER, BACKWARDACTIDINFLOW INTEGER, RELATION_TYPE INTEGER DEFAULT 0, MONITOR_STATE VARCHAR(20));
	CREATE TABLE TMP_IEAI_PRJ_UPLOAD (IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT, OPID NUMERIC(19), PROJECTUUID VARCHAR(36), PROJECTNAME VARCHAR(255), FLOWID INTEGER, FLOWNAME VARCHAR(255), UPLOADDAY TIMESTAMP, ACTTIMESTATEID VARCHAR(20));
	CREATE TABLE TMP_HC_FIRSTPAGE (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,OPT_ID NUMERIC(19) NOT NULL, FLOW_ID NUMERIC(19) NOT NULL, ACT_ID SMALLINT NOT NULL, HCNAME VARCHAR(255), PROJECTNAME VARCHAR(255), FLOWNAME VARCHAR(255), HOSTNAME VARCHAR(1), HOSTIP VARCHAR(1), HOSTPORT INTEGER, STARTTIME NUMERIC(19), ENDTIME NUMERIC(19), STATE SMALLINT, CHECKRESULT SMALLINT, ENDNUM VARCHAR(10));
	CREATE TABLE TMP_SUS_WORKFLOW (OPT_ID NUMERIC(19) NOT NULL, FLOW_ID NUMERIC(19), ACT_ID SMALLINT, IIEIATYPE SMALLINT, ISUSTYPE NUMERIC(19), IPORT INTEGER, IFLOWNAMEG VARCHAR(255), IPROJECTNAMEG VARCHAR(255), ISERVERIPGROUP VARCHAR(15), ISERVERNAME VARCHAR(255), GROUPNAME VARCHAR(255), IID BIGINT(19) NOT NULL AUTO_INCREMENT, PROJECTNAME VARCHAR(255), FLOWNAME VARCHAR(255), ISERVERIP VARCHAR(15), ACTNAME VARCHAR(255), STARTTIME NUMERIC(19), ENDTIME NUMERIC(19), STATE SMALLINT, ISAPPEXCEPTION SMALLINT, RID NUMERIC(19), CONSTRAINT PK_TMP_SUS_WORKFLOW PRIMARY KEY(IID));
	CREATE TABLE TMP_SUS_FIRST_PAGE_LIST (IID BIGINT(19) NOT NULL AUTO_INCREMENT,OPT_ID NUMERIC(19) NOT NULL, FLOW_ID NUMERIC(19) NOT NULL, ACT_ID NUMERIC(19) NOT NULL, TASK_ID NUMERIC(19), FLOW_INSTANCE_NAME VARCHAR(255), FLOW_NAME VARCHAR(255), ACT_NAME VARCHAR(255), ACT_DESCRIPTION VARCHAR(255), SHOULDSTARTTIME NUMERIC(19), BEGINEXCTIME NUMERIC(19), ACT_STATES VARCHAR(20), IOWNER VARCHAR(255), PRJ_NAME VARCHAR(255), ACT_TYPE VARCHAR(255), STARTUSER_NAME VARCHAR(255), ACT_ERRORTASKID NUMERIC(19), EXECACT_IREXECREQUESTID VARCHAR(255), ACT_DEF_NAME VARCHAR(255), CONSTRAINT PK_TMP_SUS_FIRST_PAGE_LIST PRIMARY KEY(IID));
	CREATE TABLE TMP_SUS_FIRSTPAGE (IID BIGINT(19) NOT NULL AUTO_INCREMENT,OPT_ID DECIMAL(19, 0) NOT NULL, FLOW_ID DECIMAL(19, 0) NOT NULL, ACT_ID DECIMAL(19, 0) NOT NULL, TASK_ID DECIMAL(19, 0), FLOW_INSTANCE_NAME VARCHAR(255), FLOW_NAME VARCHAR(255), ACT_NAME VARCHAR(255), ACT_DESCRIPTION VARCHAR(255), SHOULDSTARTTIME DECIMAL(19, 0), BEGINEXCTIME DECIMAL(19, 0), ACT_STATES VARCHAR(50), IOWNER VARCHAR(255), PRJ_NAME VARCHAR(255), ACT_TYPE VARCHAR(255), STARTUSER_NAME VARCHAR(255), ACT_ERRORTASKID DECIMAL(19, 0), EXECACT_IREXECREQUESTID VARCHAR(255), ACT_DEF_NAME VARCHAR(255), CONSTRAINT PK_TMP_SUS_FIRSTPAGE PRIMARY KEY(IID));
	CREATE TABLE TMP_SUS_TASKINFO (IID BIGINT(19) NOT NULL AUTO_INCREMENT,OPT_ID DECIMAL(19, 0), ACT_ID SMALLINT, IFLOWID DECIMAL(19, 0), ISTARTUSERFULLNAME VARCHAR(255), IFLOWCOMMENT VARCHAR(255), IFLOWDES VARCHAR(255), IFLOWINSNAME VARCHAR(255), IFLOWNAME VARCHAR(255), IFLOWPRIOR DECIMAL(3, 0), IHOSTNAME VARCHAR(15), IPROJECTNAME VARCHAR(255), ISTARTTIME VARCHAR(25), ISTATUS DECIMAL(3, 0), IENDTIME VARCHAR(25), IRUNTIME VARCHAR(25), IFLOWINSPORT INTEGER, RID DECIMAL(19, 0), CONSTRAINT PK_TMP_SUS_TASKINFO PRIMARY KEY(IID));
	CREATE TABLE TMP_HD_PROCESSSWITCH (OPT_ID DECIMAL (19, 0) NOT NULL, IPROJECTNAME VARCHAR(255), IFLOWINSNAME VARCHAR(255), IFLOWNAME VARCHAR(255), ISTARTTIME DECIMAL(19, 0), ISTATUS INTEGER, ISTARTUSERFULLNAME VARCHAR(255), IFLOWID DECIMAL(19, 0), IUSERID DECIMAL(19, 0), IENDTIME DECIMAL(19, 0),CONSTRAINT PK_TMP_HD_PROCESSSWITCH PRIMARY KEY(OPT_ID,IFLOWID));
	CREATE TABLE TMP_DATASHOW_CHECKRESULT (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,OPT_ID DECIMAL (19, 0) NOT NULL, SYSTEMID DECIMAL (19, 0), SYSTEMNAME VARCHAR (1000), MEID DECIMAL (19, 0), IP VARCHAR (20), CIID DECIMAL (19, 0), CINAME VARCHAR (1000), CENTERGROUPNAME VARCHAR (1000));
	CREATE TABLE TMP_BATCH_UPDATE_EXEC (UID int NOT NULL PRIMARY KEY AUTO_INCREMENT,IID NUMERIC(19) NOT NULL, IDELAYTO NUMERIC(19), ITRRAISEEXCEPTION SMALLINT, ITRSTATE NUMERIC(10), ITRSTRUCTOUTPUTSAVED SMALLINT, IACTID NUMERIC(10), IACTNAME VARCHAR(255), IFLAG NUMERIC(10), IFLOWID NUMERIC(19), IISFORRECOVERY SMALLINT, IISINTRANSACTION SMALLINT, IISSAFEFIRST SMALLINT, IISTIMEOUT SMALLINT, IREXECREQUESTID VARCHAR(255), IRUNINGPOSITION NUMERIC(10), ISCOPEID NUMERIC(19), ISTATE NUMERIC(10), ITIMEOUTTIMES NUMERIC(10), ISTARTERRUNTIMES NUMERIC(10), ISEXCEPTED SMALLINT, IHOSTNAME VARCHAR(255));
	CREATE TABLE TMP_PROCESSMONITOR_INFO (OPT_ID DECIMAL (19, 0) NOT NULL, IPROJECTNAME VARCHAR(255), IFLOWINSNAME VARCHAR(255), IFLOWNAME VARCHAR(255), ISTARTTIME DECIMAL(19, 0), ISTATUS VARCHAR(255), IACTNAME VARCHAR(255), IFLOWID DECIMAL(19, 0), IACTID DECIMAL(19, 0), IENDTIME DECIMAL(19, 0), IACTDEFNAME VARCHAR(255), ITASKID DECIMAL(19, 0), IERRORTASKID DECIMAL(19, 0),PRIMARY KEY(OPT_ID));
	CREATE TABLE TMP_RESULT_REPORT_GRID (CRID DECIMAL(12,0) NOT NULL, CPID DECIMAL(12,0), RSDID DECIMAL(12,0), CPTEXT VARCHAR(100), CPTIME DECIMAL(19,0), ISYSID DECIMAL(12,0), I1 VARCHAR(255), I2 VARCHAR(255), I3 VARCHAR(255), I4 VARCHAR(255), I5 VARCHAR(255), I6 VARCHAR(255), I7 VARCHAR(255), I8 VARCHAR(255), I9 VARCHAR(255), I10 VARCHAR(255), I11 VARCHAR(255), I12 VARCHAR(255), I13 VARCHAR(255), I14 VARCHAR(255), I15 VARCHAR(255), I16 VARCHAR(255), I17 VARCHAR(255), I18 VARCHAR(255), I19 VARCHAR(255), I20 VARCHAR(255) ,CONSTRAINT PK_RESULT_REPORT_GRID PRIMARY KEY(CRID)) ;
	CREATE TABLE TMP_RESULT_ALL_REPORT  (CRID DECIMAL(12,0)  NOT NULL, CPID DECIMAL(12,0), RSDID DECIMAL(12,0), CPTEXT VARCHAR(100), CPTIME DECIMAL(19,0), ISYSID DECIMAL(12,0), MEID DECIMAL(19,0),I1 VARCHAR(255), I2 VARCHAR(255), I3 VARCHAR(255), I4 VARCHAR(255), I5 VARCHAR(255), I6 VARCHAR(255), I7 VARCHAR(255), I8 VARCHAR(255), I9 VARCHAR(255), I10 VARCHAR(255), I11 VARCHAR(255), I12 VARCHAR(255), I13 VARCHAR(255), I14 VARCHAR(255), I15 VARCHAR(255), I16 VARCHAR(255), I17 VARCHAR(255), I18 VARCHAR(255), I19 VARCHAR(255), I20 VARCHAR(255),CONSTRAINT PK_RESULT_ALL_REPORT PRIMARY KEY(CRID) ) ;
	CREATE TABLE TMP_RESULT_REPORT_CHART (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,CPTEXT VARCHAR(4000), CPTIME DECIMAL(19,0), VV VARCHAR(2000) );
	CREATE TABLE TMP_RESULT_CPTEXT (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,CPTEXT VARCHAR(4000));
	CREATE TABLE TEMP_INI_RESLUT ( IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,IHIDDEN DECIMAL(2,0), COMCHID DECIMAL(12,0), ISYSID DECIMAL(12,0), SYSNAME VARCHAR(255), CHKITEMID DECIMAL(12,0), ICHKITEMNAME VARCHAR(255), IPARSERULE VARCHAR(255), MEID DECIMAL(12,0), IP VARCHAR(16), CPPORT DECIMAL(6,0), CPID DECIMAL(12,0), CHKPOINT  VARCHAR(512), INIITEM VARCHAR(512), INICHKPOINT VARCHAR(512), RSDID  DECIMAL(19,0),ITEM_TAG  VARCHAR(8),POINT_TAG VARCHAR(8) );
	CREATE TABLE TMP_DATASHOW (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT, OPT_ID DECIMAL(19,0) NOT NULL, DATA_CENTER VARCHAR(100), IOID DECIMAL(19,0), POID DECIMAL(19,0), OBJ_NAME  VARCHAR(512), OBJ_TYPE  SMALLINT,AMOUNT INTEGER,CL0 INTEGER,CL1 INTEGER,CL2 INTEGER,CL3 INTEGER,CL4 INTEGER,CL5 INTEGER,CHECKING INTEGER,CHECK_TIME TIMESTAMP,CHECK_RESULT VARCHAR(255),ROW_ID DECIMAL(19,0) );
	CREATE TABLE TMP_HD_BUSINESSMONITOR (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT, OPT_ID DECIMAL(19,0) NOT NULL, ISYSID DECIMAL(19,0), SYSNAME VARCHAR(255), CPSTATUS DECIMAL(19,0),ALARMLEVEL DECIMAL(4) DEFAULT 0);
	CREATE TABLE TMP_EXCEPTION_CHECK_POINT (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT, IP VARCHAR(15), PRGNAME VARCHAR(100), ALARMCODE SMALLINT, CHKITEMID DECIMAL(19,0), CHKPOINTID DECIMAL(19,0) );
	CREATE TABLE TMP_USER_PRJ_VALIDATE ( PRJID  DECIMAL(19,0) NOT NULL, CONSTRAINT PK_TMP_USER_PRJ_VALIDATE PRIMARY KEY(PRJID) );
	CREATE TABLE TMP_COUNT (IEAICOUNT DECIMAL(15,0), CONSTRAINT PK_TMP_COUNT PRIMARY KEY(IEAICOUNT));
	CREATE TABLE TMP_RUN_INSTANCE( OPID  DECIMAL(19,0), MAINID DECIMAL(19,0), WORKID DECIMAL(19,0), IRUNINSNAME VARCHAR(255), ISERNER VARCHAR(19), ICONNER VARCHAR(19), IPRENER VARCHAR(255), IACTNAME VARCHAR(255), IACTDES VARCHAR(255), IACTTYPE VARCHAR(10), IREMINFO VARCHAR(1000), IIP VARCHAR(4000), IPORT VARCHAR(19), ISYSTYPE VARCHAR(255), IOPERTYPE VARCHAR(255), ISHELLPATH VARCHAR(800) , ITIMEOUT VARCHAR(19), ISWITCHSYSTYPE  VARCHAR(255), IPARAMETER VARCHAR(255), IPARACHECK VARCHAR(255), IPARASWITCH VARCHAR(255), IPARASWITCHFORCE VARCHAR(255), IUSERNAME VARCHAR(255), IUSERID VARCHAR(19), ISHELLSCRIPT VARCHAR(800), IMODELVERSION VARCHAR(10), IEXECUSER VARCHAR(255), IEXPECEINFO VARCHAR(255), IISLOADENV  VARCHAR(10), ICONNERNAME VARCHAR(255), ICENTER VARCHAR(255), IMODELTYPE VARCHAR(255), IRETNVALEXCEPION VARCHAR(255), IREDOABLE VARCHAR(255), IPKGNAME VARCHAR(255), IVERSION VARCHAR(255), IPRESYSNAME VARCHAR(255), IPREACTNAME VARCHAR(1000), IMXGRAPHID DECIMAL(10),ISPACEIID NUMERIC(19),IGOURPIID NUMERIC(19),ISELFHEALING INTEGER DEFAULT 0,ISROLLBACK INTEGER DEFAULT 0,IJUDGEISBACK INTEGER DEFAULT 0,ISTATE DECIMAL(2), CONSTRAINT PK_TMP_RUN_INSTANCE PRIMARY KEY(OPID,MAINID,WORKID));
	CREATE TABLE TMP_SUS_WORKFIOWINFO ( IID BIGINT(19) NOT NULL AUTO_INCREMENT,OPT_ID DECIMAL(19,0), ACT_ID SMALLINT, IFLOWID  DECIMAL(19,0), ISTARTUSERFULLNAME VARCHAR(255), IFLOWCOMMENT VARCHAR(255), IFLOWDES VARCHAR(255), IFLOWINSNAME VARCHAR(255), IFLOWNAME VARCHAR(255), IFLOWPRIOR DECIMAL(3,0), IHOSTNAME VARCHAR(15), IPROJECTNAME VARCHAR(255), ISTARTTIME VARCHAR(25), ISTATUS  DECIMAL(3,0), IENDTIME VARCHAR(25), IRUNTIME VARCHAR(25), IFLOWINSPORT INTEGER, RID  DECIMAL(19,0), ITASKNAME VARCHAR(255), ISYSTEMCODE VARCHAR(255) , CONSTRAINT PK_TMP_SUS_WORKFIOWINFO PRIMARY KEY(IID));
	CREATE TABLE TMP_NODE_WARNING (IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT, ID DECIMAL(19,0) NOT NULL, PRJNAME VARCHAR(255) DEFAULT NULL, FLOWNAME VARCHAR(255) DEFAULT NULL, ACTNAME VARCHAR(255) DEFAULT NULL, TYPE DECIMAL(19,0) DEFAULT NULL, WARNTIME DECIMAL(19,0) DEFAULT NULL, WARNDES VARCHAR(255) DEFAULT NULL,IFLOWID DECIMAL(19,0) DEFAULT NULL,WARNSTATE DECIMAL(19,0) DEFAULT NULL);
	CREATE TABLE TMP_IEAI_SENDMAIL ( SYSTEMID DECIMAL(19,0) NOT NULL,SYSNAME  VARCHAR(200),MAILS VARCHAR(1000), CONSTRAINT PK_TMP_IEAI_SENDMAIL PRIMARY KEY(SYSTEMID)) ;
	CREATE TABLE TMP_EXP_CHK_DATA_SWAP (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,  CHK_OBJECT VARCHAR(255), CHK_DATE  TIMESTAMP, THREADHOLD VARCHAR(255), CHK_DATA  VARCHAR(255), ALARM_CODE SMALLINT, NFLAG SMALLINT DEFAULT 0 );
	CREATE TABLE TMP_EXPORT_CHECK_DATA ( IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT, CHK_OBJECT  VARCHAR(255), CHK_DATE TIMESTAMP, LOWER_LIMIT VARCHAR(255), UPPER_LIMIT VARCHAR(255), CHK_DATA VARCHAR(255), DATA_AVG VARCHAR(30), DATA_MIN VARCHAR(30), DATA_MAX VARCHAR(30), DATA_TYPE  SMALLINT );
	CREATE TABLE TEMP_LOOKMONITOR_BASE(pid int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IID DECIMAL(19,0) ,IFLOWID DECIMAL(19),IFLOWOWNID DECIMAL(19),IFLOWUPPERID DECIMAL(19),IPRJID DECIMAL(19),IPRJUPPERID DECIMAL(19),IPRJNAME VARCHAR(255),IFLOWNAME VARCHAR(255),ISTARTTIME DECIMAL(19),IENDTIME DECIMAL(19),ISTATUS DECIMAL(19),IDATADATE VARCHAR(255),IHOUR DECIMAL(2),IEXCEPTION DECIMAL(1),IEXCEPTIONTIME DECIMAL(19),IRESTARTTIME DECIMAL(19));
	create table TMP_IEAI_TOPO_AVGRECODER_QUERY (OPT_ID decimal(19) NOT null, PRJNAME  varchar(255) , FLOWNAME varchar(255), ACTNAME  varchar(255) , ACTTYPE  varchar(255) , ACTDESC  varchar(255) , AMONTH   varchar(10) , ADAY     varchar(10)  , AWEEK    varchar(10)  ,AOFFSET DECIMAL(5, 2),ADATAFLAG INTEGER, CONSTRAINT PK_TMP_IEAI_TOPO_CODEQUERY PRIMARY KEY(OPT_ID));
	CREATE TABLE TMP_IEAI_TOPO_INSTANCE_QUERY(OPT_ID DECIMAL(19),IID DECIMAL(19), CONSTRAINT PK_TMP_IEAI_TOPO_INS_QUERY PRIMARY KEY(IID)); 
	CREATE TABLE TMP_IEAI_EXCELMODEL(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IOPERATIONID VARCHAR(255) NOT NULL,IMAINPRONAME VARCHAR(255),ICHILDPRONAME VARCHAR(255),IACTNAME VARCHAR(200),IACTDESCRIPTION VARCHAR(4000),ICHANGEOPR VARCHAR(255),IOKFILEABSOLUTEPATH VARCHAR(4000),IOKFILEFINDWEEK VARCHAR(255),ISHELLHOUSE VARCHAR(4000),ISHELLABSOLUTEPATH VARCHAR(4000),IINPUTPARAM VARCHAR(200),ILASTLINE VARCHAR(255),IAGENTSOURCEGROUP VARCHAR(255),IMAINLINENAME VARCHAR(255),IHEADTAILFLAG VARCHAR(255),APTGROUPNAME VARCHAR(255),APTFILENAME VARCHAR(255),ISDB2 VARCHAR(255),DB2IP VARCHAR(255),APTRESGROUPNAME VARCHAR(255),DAYS VARCHAR(100),LOGIC VARCHAR(10),WDAYS VARCHAR(100),IMONTH VARCHAR(100));
	CREATE TABLE TEMP_IEAI_ACTSUCC_COPY(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IOPERATIONID DECIMAL(19),ISUCCACTNAME VARCHAR(255),ICHILDPROJECTNAME VARCHAR(255),IPROJECTNAME VARCHAR(255),IMAINLINENAME VARCHAR(255),ISELFOPERATIONID DECIMAL(19));
	CREATE TABLE TEMP_IEAI_ACTPRE_COPY(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IOPERATIONID DECIMAL(19),IPREACTNAME VARCHAR(255),ICHILDPROJECTNAME VARCHAR(255),IPROJECTNAME VARCHAR(255),IMAINLINENAME VARCHAR(255),ISELFOPERATIONID DECIMAL(19));
	CREATE TABLE TEMP_ERROROPER (ITASKID	NUMERIC(19) NOT NULL,ISTATE 	NUMERIC(19),IFLOWID	NUMERIC(19),CONSTRAINT PK_TEMP_ERROROPER PRIMARY KEY(ITASKID));
	CREATE TABLE TMP_FIRST_PAGE_LIST_HOME (OPT_ID NUMERIC(19) NOT NULL,FLOW_ID NUMERIC(19) NOT NULL,ACT_ID NUMERIC(19) NOT NULL,TASK_ID NUMERIC(19),FLOW_INSTANCE_NAME VARCHAR(255),FLOW_NAME VARCHAR(255),ACT_NAME VARCHAR(255),ACT_DESCRIPTION VARCHAR(255),SHOULDSTARTTIME NUMERIC(19),QUEUEUPSTARTTIME NUMERIC(19),QUEUEUPENDTIME NUMERIC(19),BEGINEXCTIME NUMERIC(19),ACT_STATES VARCHAR(50),IOWNER VARCHAR(255),PRJ_NAME VARCHAR(255),ACT_TYPE VARCHAR(255),STARTUSER_NAME VARCHAR(255),ACT_ERRORTASKID NUMERIC(19),EXECACT_IREXECREQUESTID	VARCHAR(255),ACT_DEF_NAME VARCHAR(255),ISYSTEM VARCHAR(255),AGENTIP VARCHAR(50),AGENTPORT VARCHAR(50),AGENTHOSTNAME VARCHAR(255),SHELLPATH VARCHAR(4000),ENDTIME DECIMAL(19),CONSTRAINT PK_TMP_FIRST_PAGE_LIST_HOME PRIMARY KEY(OPT_ID,FLOW_ID,ACT_ID));
	CREATE TABLE TMP_FIRST_PAGE_STATE_NUM (IID  int not null  PRIMARY KEY AUTO_INCREMENT, ISTATE VARCHAR(50),ACTNUM NUMERIC(6),IACTNAME VARCHAR(255));
	CREATE TABLE TMP_QUERY_ACTNEXTINFO(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IPRJID DECIMAL(19), IFLOWID DECIMAL(19), IFLOWINS VARCHAR(255), IPROJECTNAME VARCHAR(255), ISYSTEM VARCHAR(255), IPRJUUID VARCHAR(255),IFLOWNAME VARCHAR(255),IACTNAME VARCHAR(255),IPRENUM DECIMAL(38),ISERVERIP VARCHAR(255));
	CREATE TABLE TMP_EXEC_ACTNEXTINFO( IID  int not null  PRIMARY KEY AUTO_INCREMENT , IPRJID DECIMAL(19), IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255),IPROJECTNAME VARCHAR(255), ISYSTEM VARCHAR(255),IPRJUUID VARCHAR(255),IFLOWNAME VARCHAR(255), IACTNAME VARCHAR(255),IPRENUM DECIMAL(38), ISERVERIP VARCHAR(255));
	CREATE TABLE TMP_MUTEX_INFO(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT, IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255),IACTNAME VARCHAR(255),IMUTEXRULEID DECIMAL(19),ISYSTEM VARCHAR(255));
	CREATE TABLE TMP_BATCH_UPDATE_EXEC_NEW(BID int NOT NULL PRIMARY KEY AUTO_INCREMENT,IID DECIMAL(19) NOT NULL, IDELAYTO DECIMAL(19),ITRRAISEEXCEPTION DECIMAL,ITRSTATE DECIMAL(10),ITRSTRUCTOUTPUTSAVED DECIMAL,IACTID DECIMAL(19),IACTNAME VARCHAR(255),IFLAG DECIMAL(10),IFLOWID DECIMAL(19),IISFORRECOVERY DECIMAL,IISINTRANSACTION DECIMAL,IISSAFEFIRST DECIMAL,IISTIMEOUT DECIMAL,IREXECREQUESTID VARCHAR(255),IRUNINGPOSITION DECIMAL(10),ISCOPEID DECIMAL(19),ISTATE DECIMAL(10),ITIMEOUTTIMES DECIMAL(10),ISTARTERRUNTIMES DECIMAL(10),ISEXCEPTED DECIMAL,IHOSTNAME VARCHAR(255),IFLOWDEFID DECIMAL(19),IPRJUUID VARCHAR(255),IPROJECTNAME VARCHAR(255),IFLOWNAME VARCHAR(255));
	CREATE TABLE TMP_EXECACT(IID  DECIMAL(19) NOT NULL,IDELAYTO  DECIMAL(19),ITRRAISEEXCEPTION  DECIMAL(1),ITRSTATE DECIMAL(10),ITRSTRUCTOUTPUTSAVED DECIMAL(1),IACTID  DECIMAL(19),IACTNAME  VARCHAR(255),IFLAG DECIMAL(10),IFLOWID  DECIMAL(19),IISFORRECOVERY DECIMAL(1),IISINTRANSACTION DECIMAL(1),IISSAFEFIRST  DECIMAL(1),IISTIMEOUT DECIMAL(1),IREXECREQUESTID VARCHAR(255),IRUNINGPOSITION DECIMAL(10),ISCOPEID  DECIMAL(19),ISTATE DECIMAL(10),ITIMEOUTTIMES DECIMAL(10),ISTARTERRUNTIMES DECIMAL(10),ISEXCEPTED DECIMAL(1),CONSTRAINT PK_TMP_EXECACT PRIMARY KEY(IID));
	CREATE TABLE TEMP_ACT_HISTORY_FOR_REPORT ( IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,opt_id DECIMAL (19, 0), isystem VARCHAR (255),iflowid DECIMAL (19, 0),iprojectname VARCHAR (255),iflowname VARCHAR (255),iflowinsname VARCHAR (255),iactname VARCHAR (255),pdks VARCHAR (255),pdjs VARCHAR (255),pdsc VARCHAR (255),pdscnum DECIMAL (19, 0),ibeginexctime VARCHAR (255),iendtime VARCHAR (255),yxsc VARCHAR (255),yxscnum DECIMAL (19, 0));
	CREATE TABLE TEMP_EXCELMODEL (IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT, opt_id DECIMAL (19, 0), floor DECIMAL (19, 0), iflowid DECIMAL (19, 0), ioperationid DECIMAL (19, 0), iactname VARCHAR (200), iflowinsname VARCHAR (200), istate VARCHAR (200), ibeginexctime DECIMAL (19, 0), iendtime DECIMAL (19, 0), iflowid2 DECIMAL (19, 0), ioperationid2 DECIMAL (19, 0), iactname2 VARCHAR (200), iflowinsname2 VARCHAR (200), istate2 VARCHAR (200), ibeginexctime2 DECIMAL (19, 0), iendtime2 DECIMAL (19, 0), mainlinename1 VARCHAR (255), mainlinename2 VARCHAR (255));
	CREATE TABLE TMP_HC_PANDECT_REPORT_COUNT  (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,  NUMCOUNT	decimal(12,0) NULL );
	CREATE TABLE TMP_HC_BUSINESSSYS_TYPE_REPORT  (IID BIGINT(19) NOT NULL PRIMARY KEY AUTO_INCREMENT,  OPTID  decimal(19,0) NULL, SYSTEMID decimal(12,0) NULL, SYSTENNAME	varchar(100) NULL, ALARMNUM decimal(12,0) NULL, ALARMTIME varchar(50) NULL );
	CREATE TABLE TMP_QUERY_ACTNAME(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,iflowname VARCHAR(255));
	create table TMP_IMPAGENTIP(IID BIGINT(19) NOT NULL AUTO_INCREMENT,AGENTIP VARCHAR(200),PORT VARCHAR(200),OSTYPE VARCHAR(200),CONFIGPARAMS VARCHAR(200),ISHUTDOWNUSERNAME VARCHAR(100),COMPUTERNAME VARCHAR(200),CONSTRAINT PK_TMP_IMPAGENTIPT PRIMARY KEY(IID));
	CREATE TABLE TMP_AUTO_CLOSE_WARNING_EVENT (RID DECIMAL(19,0),CONSTRAINT PK_TMP_AUTO_CLOSE_WARNING_EVENT PRIMARY KEY(RID));
	CREATE TABLE TMP_SCIPRTMONITORFLOW(FLOWID BIGINT(19) NOT NULL AUTO_INCREMENT,CONSTRAINT PK_TMP_SCIPRTMONITORFLOW PRIMARY KEY(FLOWID));
	CREATE TABLE TMP_EXEC_ACTNEXTINFO_NEW( IID  int not null  PRIMARY KEY AUTO_INCREMENT, IPRJID DECIMAL(19), IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255),IPROJECTNAME VARCHAR(255), ISYSTEM VARCHAR(255),IPRJUUID VARCHAR(255),IFLOWNAME VARCHAR(255), IACTNAME VARCHAR(255),IPRENUM DECIMAL(38), ISERVERIP VARCHAR(255),IUUID VARCHAR(255));
  	CREATE TABLE TMP_QUERY_ACTNEXTINFO_NEW(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IPRJID DECIMAL(19), IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255), IPROJECTNAME VARCHAR(255), ISYSTEM VARCHAR(255), IPRJUUID VARCHAR(255),IFLOWNAME VARCHAR(255),IACTNAME VARCHAR(255),IPRENUM DECIMAL(38),ISERVERIP VARCHAR(255),IUUID VARCHAR(255));
  	CREATE TABLE TMP_MUTEX_INFO_NEW(IID int PRIMARY KEY  NOT NULL AUTO_INCREMENT,IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255),IACTNAME VARCHAR(255),IMUTEXRULEID DECIMAL(19),ISYSTEM VARCHAR(255),IUUID VARCHAR(255));
  	CREATE TABLE TMP_EXECACT_NEW(EID int NOT NULL PRIMARY KEY AUTO_INCREMENT, IID  DECIMAL(19) NOT NULL ,IDELAYTO  DECIMAL(19),ITRRAISEEXCEPTION  DECIMAL(1),ITRSTATE DECIMAL(10),ITRSTRUCTOUTPUTSAVED DECIMAL(1),IACTID  DECIMAL(19),IACTNAME  VARCHAR(255),IFLAG DECIMAL(10),IFLOWID  DECIMAL(19),IISFORRECOVERY DECIMAL(1),IISINTRANSACTION DECIMAL(1),IISSAFEFIRST  DECIMAL(1),IISTIMEOUT DECIMAL(1),IREXECREQUESTID VARCHAR(255),IRUNINGPOSITION DECIMAL(10),ISCOPEID  DECIMAL(19),ISTATE DECIMAL(10),ITIMEOUTTIMES DECIMAL(10),ISTARTERRUNTIMES DECIMAL(10),ISEXCEPTED DECIMAL(1),IUUID VARCHAR(255));
  	CREATE TABLE TMP_FIRST_PAGE_HOME_JH (IID  int not null  PRIMARY KEY AUTO_INCREMENT, OPT_ID NUMERIC(19) NOT NULL,FLOW_ID NUMERIC(19) NOT NULL,ACT_ID NUMERIC(19) NOT NULL,TASK_ID NUMERIC(19),FLOW_INSTANCE_NAME VARCHAR(255),FLOW_NAME VARCHAR(255),ACT_NAME VARCHAR(255),ACT_DESCRIPTION VARCHAR(255),SHOULDSTARTTIME NUMERIC(19),QUEUEUPSTARTTIME NUMERIC(19),QUEUEUPENDTIME NUMERIC(19),BEGINEXCTIME NUMERIC(19),ACT_STATES VARCHAR(50),IOWNER VARCHAR(255),PRJ_NAME VARCHAR(255),ACT_TYPE VARCHAR(255),STARTUSER_NAME VARCHAR(255),ACT_ERRORTASKID NUMERIC(19),EXECACT_IREXECREQUESTID	VARCHAR(255),ACT_DEF_NAME VARCHAR(255),ISYSTEM VARCHAR(255),AGENTIP VARCHAR(50),AGENTPORT VARCHAR(50),AGENTHOSTNAME VARCHAR(255),SHELLPATH VARCHAR(4000),ENDTIME DECIMAL(19));
    CREATE TABLE TMP_IEAI_DELETING_FLOWID (IID INT AUTO_INCREMENT PRIMARY KEY,ID NUMERIC (19, 0) NOT NULL,FLOWID NUMERIC (19, 0) NOT null,serverType smallint);