package com.ideal.ieai.prv.provider.dbaas.thread;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.AgentCommunicationException;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.ieai.prv.repository.hd.dubbo.dbaas.AsmManager;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.agent.AgentXmlRpcClient;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.proxy.execremote.PerformDataProcessService;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.agentMaintain.RpcSslType;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.util.UUID;
import org.apache.log4j.Logger;
import org.apache.xmlrpc.XmlRpcClient;

import java.net.MalformedURLException;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

public class AsmInfoThread extends Thread
{
    static private final Logger _log     = Logger.getLogger(AsmInfoThread.class);
    private long                pollTime = 24 * 60 * 60 * 1000l;

    public void run ()
    {
        try
        {
            ConfigReader rf = ConfigReader.getInstance();
            rf.init();
            boolean flag = ServerEnv.getInstance().getBooleanConfigNew(Environment.ASM_THREAD_SWITCH,
                Environment.FALSE_BOOLEAN);
            _log.info("AsmInfoThread start switch is " + flag);
            while (true)
            {
                if (flag)
                {
                    pollTime = Long.parseLong(rf.getProperties(Environment.ASM_THREAD_POLLTIME, "3000000"));
                    //
                    List<Map<String, Object>> list = AsmManager.getInstance().getAsmForThread();
                    for (int i = 0; i < list.size(); i++)
                    {
                        Long iid = Long.parseLong(String.valueOf(list.get(i).get("IID")));
                        String iagentid = (String) list.get(i).get("IAGENTID");
                        if (iagentid == null || iagentid.equals("") || iagentid.equals("-1"))
                        {
                            continue;
                        }
                        String iagentip = String.valueOf(list.get(i).get("IAGENT_IP"));
                        int iagentport = Integer.parseInt(String.valueOf(list.get(i).get("IAGENT_PORT")));
                        String operSys = String.valueOf(list.get(i).get("IOPERSYS"));
                        String result = connectAgent(iagentip, iagentport, true, iid.toString(), operSys);
                        // result = result.replace("/", "`");// parseJson解析含有/的字符串会报错
                        // String json =
                        // "{AsmState:true,AsmValue:[{State:MOUNTED,Type:EXTERN,Rebal:N,Sector:512,Block:4096,AU:1048576,Total_MB:20479,Free_MB:20083,Req_mir_free_MB:0,Usable_file_MB:20083,Offline_disks:0,Voting_files:Y,Name:CRS/},{State:MOUNTED,Type:EXTERN,Rebal:N,Sector:512,Block:4096,AU:1048576,Total_MB:61439,Free_MB:58565,Req_mir_free_MB:0,Usable_file_MB:58565,Offline_disks:0,Voting_files:N,Name:DATADG/}]}";
                        //
                        // Map<String, Object> a = ParseJson.JSON2Map(result);
                        // List<Map<String, Object>> jsonlist =
                        // ParseJson.JSON2List(String.valueOf(a.get("AsmValue")));
                        // String istatus = String.valueOf(a.get("AsmState"));
                        // AsmManager.getInstance().saveAsmInfoById(iid, istatus, jsonlist);
                    }
                    //
                }
                Thread.sleep(pollTime);
            }
        } catch (Exception e)
        {
            _log.error(" 执行线程异常: ", e);
            try
            {
                Thread.sleep(10000);
            } catch (InterruptedException e1)
            {
                e1.printStackTrace();
            }
        }

    }

    public static String connectAgent ( String agentIp, int port, boolean flag, String resId, String operSys )
            throws RepositoryException
    {
        String result = "";
        Connection con = null;
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        try
        {
            String serverIp = Environment.getInstance().getServerIP();
            String serverPort = Environment.getInstance().getSysConfig(Environment.TOM_PORT, "8888");

            String requestid = "ieai-asm-" + resId + "-ieai-asm-" + UUID.uuid();
            con = DBResource.getConnection("connectAgent", _log, Constants.IEAI_IEAI_BASIC);
            String adpDefUuid = ProjectManager.getInstance().getLatestAdaptorInfo("shellcmd", con);// 获取接口UUID
            XmlRpcClient rpcClient;
            Vector params = new Vector();
            params.addElement(requestid);
            params.addElement(serverIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = new Hashtable();
            table.put("adaptorDefUUID", adpDefUuid);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("Id", requestid);
            table.put("serverHost", serverIp);
            table.put("serverPort", Integer.parseInt(serverPort));
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", port);
            table.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("actName", "ShellCmd"); // 默认值
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", requestid);
            table.put("flowId", "-1");// 使用taskName可否?可以
            table.put("projectName", "shell_vm");// ?
            table.put("flowName", "ShellCmd");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            Hashtable a = new Hashtable();
            String script;
            ConfigReader cfr = ConfigReader.getInstance();
            cfr.init();
            String scripts = cfr.getProperties(Environment.DBAAS_SS_ASM_SCRIPT_PATH, "/opt/");
            String scriptsWin = cfr.getProperties(Environment.DBAAS_SS_ASM_SCRIPT_PATH_WIN, "/opt/");
            if (operSys.toLowerCase().indexOf("windows") >= 0)
            {
                script = "cmd.exe /c  " + scriptsWin;
            } else
            {
                script = "sh " + scripts;
            }
            a.put("command", script);
            table.put("input", a);
            params.addElement(table);

            rpcClient = getXmlRpcClient(agentIp, port);

            for (int ii = 0; ii <= 2; ii++)
            {
                try
                {
                    long start = System.currentTimeMillis();
                    result = String.valueOf(rpcClient.execute("IEAIAgent.executeAct", params));
                    break;
                } catch (Exception e)
                {
                    if (ii != 2)
                    {
                        _log.error("It will retry. pubType", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            // no code
                        }
                    } else
                    {

                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (Exception e)
        {
            _log.error(method + " is error messages:", e);
            throw new RepositoryException(ServerError.ERR_DB_EXEC);
        } finally
        {
            DBResource.closeConnection(con, method, _log);
        }

        return result;
    }

    private static XmlRpcClient getXmlRpcClient ( String agentIp, int agentPort ) throws AgentCommunicationException
    {

        XmlRpcClient _rpcClient;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(agentIp, agentPort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, agentPort, Constants.IEAI_SCRIPT_SERVICE);
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIp
                            + ":" + agentPort + " proxySwitch is true ");
                }
                _rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                _rpcClient = new AgentXmlRpcClient(agentIp, agentPort, ssltype);
            }
        } catch (MalformedURLException ex)
        {
            AgentCommunicationException agentExp = new AgentCommunicationException(ex);
            agentExp.setAgentPort(String.valueOf(agentPort));
            agentExp.setAgentHost(agentIp);
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error for AgentAutoRenew!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        return _rpcClient;
    }

}
