DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_API_BASICINFO_PUBLISH' AND COLUMN_NAME='IMINTIME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_API_BASICINFO_PUBLISH ADD IMINTIME NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_API_BASICINFO_PUBLISH' AND COLUMN_NAME='IMAXNUM'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_API_BASICINFO_PUBLISH ADD IMAXNUM NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
END;
/
