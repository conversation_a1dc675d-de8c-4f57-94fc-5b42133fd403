package com.ideal.service.routinetasks.emmonitor;

import com.ideal.ieai.commons.shellcmd.ShellCmdOutput;
import com.ideal.ieai.server.engine.task.TaskManager;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.engine.EngineRepository;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.model.ProjectBean;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoBean;
import com.ideal.ieai.server.routinetasks.repository.monitor.listbox.RoutineTasksMonitorManage;
import com.ideal.service.shellcmdoutout.ShellCmdOutPutService;
import com.ideal.util.CLSBase64;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

/**
 * 
 * <ul>
 * <li>Title: SwitchMonitorService.java</li>
 * <li>Description:切换监控Service</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2016年1月20日
 */
public class RoutineTasksMonitorService
{
    
    /**
     * 
     * <li>Description:获取实例列表</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninsname
     * @param iinsdes
     * @param isysclassid
     * @param isysname
     * @param istate
     * @param userid
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorSys ( String iruninsname, String iinsdes, String version, String isysclassid,
            String isysname, String istate, String userid, int sysType ) throws Exception
    {

        return RoutineTasksMonitorManage.getInstance().getSwitchMonitorSys(iruninsname, iinsdes, version, isysclassid, isysname,
            istate, userid, sysType);

    }
    
    public List getSwitchMonitorInstanceConcurrent ( Long instanceId, int sysType ) throws Exception
    {

        return RoutineTasksMonitorManage.getInstance().getSwitchMonitorInstanceConcurrent(instanceId, sysType);

    }
    
    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2016年5月24日 
     * @param userid
     * @param sysType
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorSysInstanceList(String userid, int sysType) throws Exception{
        StringBuilder sysnames = new StringBuilder();
        sysnames.append("");
        List<ProjectBean> projectList=DBUtil.getProject(null, Long.valueOf(userid), sysType);// ( 1作业调度 2信息采集 3变更管理 4灾备切换 5定时任务 6应急操作 7健康巡检  47例行任务)
        int i=0;
        for(ProjectBean projectBean:projectList)
        {
            if(i==0){
                sysnames.append("'");
                sysnames.append(projectBean.getiName());
                sysnames.append("'");
            }else{
                sysnames.append(",");
                sysnames.append("'");
                sysnames.append(projectBean.getiName());
                sysnames.append("'");
            }
            i++;
        }
        return RoutineTasksMonitorManage.getInstance().getSwitchMonitorSysInstanceList(sysnames.toString(), sysType);
        
    }
    
    /**
     * 
     * <li>Description:获取大系统列表</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @return
     * @throws Exception
     * return List
     */
    public List getSysType(int sysType) throws Exception{
        
        return RoutineTasksMonitorManage.getInstance().getSysType(sysType);
        
    }
    
    /**
     * 
     * <li>Description:获取步骤列表</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninsid
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStep(String iruninsid,int sysType) throws Exception{
        
        return RoutineTasksMonitorManage.getInstance().getSwitchMonitorStep(iruninsid, sysType);
        
    }
    
    /**
     * 
     * <li>Description:实例终止</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninsid
     * @throws Exception
     * return void
     */
    public void sysStop(String iruninsid,int sysType) throws Exception{
        
        Connection conn = null;
        
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            
            RoutineTasksMonitorManage.getInstance().sysStop(iruninsid, 2l,conn);
            
            RoutineTasksMonitorManage.getInstance().stepStopByruninsid(iruninsid, 3l, conn);
            
            conn.commit();
            
        } catch (Exception e)
        {
            if (null != conn)
            {
                conn.rollback();
            }
            throw new Exception(e);
            
        }finally{
            if(conn!=null){
                conn.close();
            }
        }
    }
    
    /**
     * 
     * <li>Description:实例全部终止</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninsid
     * @throws Exception
     * return void
     */
    public void sysStopAll(int sysType) throws Exception{
        
        Connection conn = null;
        
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            
            RoutineTasksMonitorManage.getInstance().stepStopByruninsidAll(3l, conn);
            
            RoutineTasksMonitorManage.getInstance().sysStopAll(2l,conn);
            
            conn.commit();
            
        } catch (Exception e)
        {
            if(null!=conn){
                conn.rollback();
            }
           
            throw new Exception(e);
            
        }finally{
            if(conn!=null){
                conn.close();
            }
        }
    }
    
    /**
     * 
     * <li>Description:实例重新执行</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninsid
     * @throws Exception
     * return void
     */
    public void sysReStart(String iruninsid,int sysType) throws Exception{
        
        EngineRepository.getInstance().InstanceLevelReRun(Long.valueOf(iruninsid),sysType);
        
    }
    
    /**
     * 
     * <li>Description:步骤终止</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void stepStop(String iruninfoinsid,int sysType) throws Exception{
        
        RoutineTasksMonitorManage.getInstance().stepStop(iruninfoinsid, 3l,sysType);
        
    }
    
    /**
     * 
     * <li>Description:步骤重新执行</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void stepReStart(String iruninfoinsid,int flag,int sysType) throws Exception{
        
        EngineRepository.getInstance().flowLevelReRun(Long.valueOf(iruninfoinsid),flag,sysType);
        
    }
    
    /**
     * <li>Description:步骤暂停</li> 
     * <AUTHOR>
     * 2016年4月7日 
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void stepPause(String iruninfoinsid, int sysType) throws Exception{
        
        RoutineTasksMonitorManage.getInstance().stepPause(iruninfoinsid, sysType);
        
    }
    
    /**
     * <li>Description:步骤恢复</li> 
     * <AUTHOR>
     * 2016年4月7日 
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void stepResume(String iruninfoinsid, int sysType) throws Exception{
        
        RoutineTasksMonitorManage.getInstance().stepResume(iruninfoinsid, sysType);
        
    }
    
    /**
     * 
     * <li>Description:步骤强制执行</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninfoinsid
     * @throws Exception
     * return void
     */
    public void forceStart(String iruninfoinsid,int sysType) throws Exception{
        
        EngineRepository.getInstance().flowLevelReRun(Long.valueOf(iruninfoinsid),0,sysType);
        
    }
    
    /**
     * 
     * <li>Description:UT信息查询</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iflowid
     * @return
     * @throws Exception
     * return Map
     */
    public Map getUTMess(String iflowid) throws Exception{
        
        return RoutineTasksMonitorManage.getInstance().getUTMess(Long.valueOf(iflowid));
        
    }
    
    /**
     * 
     * <li>Description:获取活动输出</li> 
     * <AUTHOR>
     * 2016年1月20日 
     * @param iruninfoinsid
     * @return
     * @throws Exception
     * return String
     */
    public String getShelloutput(String iruninfoinsid,String flag, Boolean isJoinedIp,int sysType) throws Exception{
        
        String stdout = "";
        String stderr = "";
        List list = RoutineTasksMonitorManage.getInstance().getSwitchMonitorStepByStepId(iruninfoinsid,flag,sysType);
        ShellCmdOutPutService service = new ShellCmdOutPutService();
        if (list != null && !list.isEmpty())
        {
            for(int i=0;i<list.size();i++){
                Map map = (Map)list.get(i);
                
                Long iflowid = (Long)map.get("iflowid");
                Long istate = (Long)map.get("istate");
                Long iisfail = (Long)map.get("iisfail");
                Long iacttype = (Long) map.get("iacttype");
                String ireminfo = (String) map.get("ireminfo");

                if((Long.valueOf("0").equals(istate)&&Long.valueOf("-1").equals(iisfail)) ||
                   (isJoinedIp&&Long.valueOf("0").equals(istate)&&Long.valueOf("2").equals(iisfail))){
                    
                    if ("2".equals(String.valueOf(iacttype)))
                    {
                        stdout = ireminfo;
                    } else
                    {
                        /**
                         * update by li_yang
                         * 有组合式IP时（IP1+IP2+...+IPx），之前跑过的shell的输出会先行入库
                         */
                        if (isJoinedIp)
                        {
                            stdout = service.getFinishSehllCmdStdoutJoinIps(Long.valueOf(iruninfoinsid), sysType);
                        }

                        String reqId = service.getRequestId(iflowid, sysType);

                        ShellCmdOutput shellcmdoutput = null;
                        shellcmdoutput = TaskManager.getInstance().getShellCmdOutput("", reqId, 0, 0, 100, 0, 0,
                            sysType);
                        stdout += CLSBase64.getFromBASE64(shellcmdoutput.getStdOut());
                        if (null == stdout || "null".equals(stdout))
                        {
                            stdout = "";
                        }
                        stderr = CLSBase64.getFromBASE64(shellcmdoutput.getStdErr());
                        if (null == stderr || "null".equals(stderr))
                        {
                            stderr = "";
                        }
                    }
                    
                    
                }else{
                    if ("2".equals(String.valueOf(iacttype)))
                    {
                        stdout = ireminfo;
                    } else
                    {
                        /**
                         * update by li_yang
                         * 有组合式IP时（IP1+IP2+...+IPx），shell的输出要将这些组合IP下跑出来的输出联合起来
                         */
                        if (isJoinedIp)
                        {
                            stdout = service.getFinishSehllCmdStdoutJoinIps(Long.valueOf(iruninfoinsid), sysType);
                        } else
                        {
                            ShellInfoBean bean = service.getFinishSehllCmdStdout(Long.valueOf(iruninfoinsid), sysType,"0");
                            stdout = bean.getStdout();
                        }
                        if (null == stdout || "null".equals(stdout))
                        {
                            stdout = "";
                        }
                    }

                }
                
                
            }
        }
       
        return stdout+stderr;
        
    }
    

    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2016年1月26日 
     * @param instanceId
     * @param step
     * @return
     * @throws Exception
     * return List
     */
    public List getSwitchMonitorStepForGraph(Long instanceId, Integer step, int sysType) throws Exception{
        
        return RoutineTasksMonitorManage.getInstance().getSwitchMonitorStepForGraph(instanceId, step, sysType);
        
    }
}
