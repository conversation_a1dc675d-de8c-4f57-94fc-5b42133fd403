<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="java.util.*"%>
<%
    List listCol = (List) request.getAttribute("listCol");
%>
<html>
<head>
<script type="text/javascript">
var cpid = '<%=request.getParameter("cpid")%>';
var fieldsData = [];

var columnsData = [{ text: '序号', xtype:'rownumberer', width: 40 }];
var isLevelConvertSwitch = <%=Environment.getInstance().isHcAlarmLevelConvertSwitch()%>;

<%
for(int i = 0;i<listCol.size();i++){
	Map map = (Map)listCol.get(i);
%>
	var fieldsRow = {};
	fieldsRow.name = '<%=(String)map.get("colValue")%>';
	fieldsRow.type = 'string';
	fieldsData.push(fieldsRow);
	
	var columnsRow = {};
	columnsRow.text = '<%=(String)map.get("colName")%>';
	columnsRow.dataIndex = '<%=(String)map.get("colValue")%>';
	if(columnsRow.dataIndex=='I1'){
		columnsRow.renderer=function(value,metaData,record){
			var backValue = ""; 
			if(isLevelConvertSwitch){
				if(value=='-6'){
					backValue = '<img src="images/tp_common.png" class="check_termination"></img>';
			   	}else if(value=='-5'){
					backValue = '<img src="images/tp_common.png" class="nocheck"></img>';
			   	}else if(value=='-1'){
					backValue = '<img src="images/tp_common.png" class="detect"></img>';
			   	}else if(value=='0'){
			   		backValue = '<img src="images/tp_common.png" class="normal"></img>';
			   	}else if(value=='1'){
			   		backValue = '<img src="images/tp_common.png" class="warn_05"></img>';
			   	}else if(value=='2'){
			   		backValue = '<img src="images/tp_common.png" class="warn_04"></img>';
			   	}else if(value=='3'){
			   		backValue = '<img src="images/tp_common.png" class="warn_03"></img>';
			   	}else if(value=='4'){
			   		backValue = '<img src="images/tp_common.png" class="warn_02"></img>';
			   	}else if(value=='5'){
			   		backValue = '<img src="images/tp_common.png" class="warn_01"></img>';
			   	}
			}else{
				if(value=='-6'){
					backValue = '<img src="images/tp_common.png" class="check_termination"></img>';
			   	}else if(value=='-5'){
					backValue = '<img src="images/tp_common.png" class="nocheck"></img>';
			   	}else if(value=='-1'){
					backValue = '<img src="images/tp_common.png" class="detect"></img>';
			   	}else if(value=='0'){
			   		backValue = '<img src="images/tp_common.png" class="normal"></img>';
			   	}else if(value=='1'){
			   		backValue = '<img src="images/tp_common.png" class="green_light"></img>';
			   	}else if(value=='2'){
			   		backValue = '<img src="images/tp_common.png" class="blue_light"></img>';
			   	}else if(value=='3'){
			   		backValue = '<img src="images/tp_common.png" class="purple_light"></img>';
			   	}else if(value=='4'){
			   		backValue = '<img src="images/tp_common.png" class="orange_light"></img>';
			   	}else if(value=='5'){
			   		backValue = '<img src="images/tp_common.png" class="red_light"></img>';
			   	}
			}
		   	return backValue;
		}
	}else{
		columnsRow.renderer=function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'"; 
		    return value; 
		};
	}
	columnsRow.minWidth = 120;
	columnsRow.flex = 1;
	columnsRow.menuDisabled=true;
	
	columnsData.push(columnsRow);
<%
}
%>
	
</script>
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/classOverview/classOverviewExcepPointValue.js"></script>
</head>
<body>
	<div id="classOverviewExcepPointValue_area"
		style="width: 100%; height: 100%"></div>
</body>
</html>