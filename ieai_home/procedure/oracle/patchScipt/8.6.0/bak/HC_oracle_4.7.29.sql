	CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE PROC_EXPORT_CHECK_DATA  ( AN_CHKPOINT_ID IN NUMBER,  ADT_START IN VARCHAR2, ADT_END IN VARCHAR2,  <PERSON>_PAGE_ID IN SMALLINT,  AI_<PERSON><PERSON>_SIZE IN INTEGER,  AI_DAT_TYPE IN SMALLINT,  AI_AMOUNT OUT INTEGER) AS
 		BEGIN
 			/*
 				Parameters:
 					1 AN_CHKPOINT_ID	--<in>	the check point ID
 					2 ADT_START			--<in>	begin time (varchar, such as：'2016-12-01 00:00:00')
 					3 ADT_START			--<in>	end time (varchar, such as：'2016-12-01 23:59:59')
 					4 AI_PAGE_ID		--<in>	the page code
 					5 AI_PAGE_SIZE		--<in>	the page size
 					6 AI_DAT_TYPE		--<in>	report data type: 0 -- the original data
 																  1 -- the statistics data, such as AVG and MIN and MAX, etc.
 					7 AI_AMOUNT			--<OUT>	the records count
 			*/
 			DECLARE	LI_CNT		INTEGER;
 				LI_LLIMIT	INTEGER;
 				LI_ULIMIT	INTEGER;
 				LV_STIME		VARCHAR2(23);
 				LV_ETIME		VARCHAR2(23);
 				LV_SQL		VARCHAR2(1024);
 				LI_DIGIT		SMALLINT;
 				
 				LI_PAGE_ID SMALLINT;
 				LI_PAGE_SIZE INTEGER;
 		
 			BEGIN
 			--SET	LV_SQL = CAST(AN_CHKPOINT_ID AS VARCHAR(20)) || ', ' || CHR(39) || ADT_START || CHR(39) || ', ' || CHR(39) || ADT_END || CHR(39) || ', ' || CAST(AI_PAGE_ID AS VARCHAR(12)) || ', ' || CAST(AI_PAGE_SIZE AS VARCHAR(12)) || ', ' || CAST(AI_DAT_TYPE AS VARCHAR(6));
 			
 			LV_STIME := TO_CHAR(TO_DATE(ADT_START,'YYYY-MM-DD'), 'YYYY-MM-DD') || ' 00:00:00';
 			LV_ETIME := TO_CHAR(TO_DATE(ADT_END,'YYYY-MM-DD'), 'YYYY-MM-DD') || ' 23:59:59';
 			
 			-- 1 ready data from CACHE and HIS
 			INSERT INTO TMP_EXP_CHK_DATA_SWAP
 					(
 						CHK_OBJECT,
 						CHK_DATE,
 						THREADHOLD,
 						CHK_DATA,
 						ALARM_CODE,
 						NFLAG
 					)
 			SELECT	C.I5,
 						TO_TIMESTAMP(C.I2,'yyyy-mm-dd hh24:mi:ss.ff'),
 						C.I4,
 						TRIM(C.I3),
 						CAST(C.I1 AS INTEGER),
 						0
 			FROM		HD_CHECK_STATUS_CACHE C
 			WHERE		C.CPID = AN_CHKPOINT_ID
 			AND		(C.I2 BETWEEN LV_STIME AND LV_ETIME)
 			UNION
 			SELECT	H.I5,
 						TO_TIMESTAMP(H.I2,'yyyy-mm-dd hh24:mi:ss.ff'),
 						H.I4,
 						H.I3,
 						CAST(H.I1 AS INTEGER),
 						0
 			FROM		HD_CHECK_STATUS_HIS H
 			WHERE		H.CPID = AN_CHKPOINT_ID
 			AND		(H.I2 BETWEEN LV_STIME AND LV_ETIME);
 			
 			-- 2 Get affect rows count
 			--GET DIAGNOSTICS LI_CNT = ROW_COUNT;
 			LI_CNT := sql%rowcount;
 			
 			IF LI_CNT = 0 THEN
 				RETURN;
 			END IF;
 			
 			-- 3 Valid page_size
 			IF AI_PAGE_SIZE IS NULL THEN
 				LI_PAGE_SIZE := 50;
 			ELSE 
 				LI_PAGE_SIZE:=AI_PAGE_SIZE;
 			END IF;
 			
 			IF LI_PAGE_SIZE > 65530 THEN
 				LI_PAGE_SIZE := 65530;
 			END IF;
 			
 			-- 4 Valid page_id
 			IF AI_PAGE_ID IS NULL THEN
 				LI_PAGE_ID := 1;
 			ELSE 
 				LI_PAGE_ID:=AI_PAGE_ID;
 			END IF;
 			
 			IF LI_CNT < (((LI_PAGE_ID - 1) * LI_PAGE_ID) + 1) THEN
 				LI_PAGE_ID := TRUNC(LI_CNT / LI_PAGE_ID, 0);
 				IF MOD(LI_CNT, LI_PAGE_SIZE) > 0 THEN
 					LI_PAGE_ID := LI_PAGE_ID + 1;
 				END IF;
 			END IF;
 			
 			-- 5 Lower Limit and Upper Limit
 			LI_LLIMIT := ((LI_PAGE_ID - 1) * LI_PAGE_SIZE) + 1;
 			LI_ULIMIT := LI_PAGE_ID * LI_PAGE_SIZE;
 			
 			IF AI_DAT_TYPE = 0 THEN
 				-- 6 to view or export the original data
 				INSERT INTO TMP_EXPORT_CHECK_DATA
 						(
 							CHK_OBJECT,
 							CHK_DATE,
 							LOWER_LIMIT,
 							UPPER_LIMIT,
 							CHK_DATA,
 							DATA_AVG,
 							DATA_MIN,
 							DATA_MAX,
 							DATA_TYPE
 						)
 				SELECT	A.CHK_OBJECT,
 							A.CHK_DATE,
 							A.LOWER_LIMIT,
 							A.UPPER_LIMIT,
 							A.CHK_DATA,
 							A.DATA_AVG,
 							A.DATA_MIN,
 							A.DATA_MAX,
 							A.DATA_TYPE
 				FROM		(
 								SELECT	CHK_OBJECT,
 											CHK_DATE,
 											FUN_GET_LIMIT(THREADHOLD, 0) AS LOWER_LIMIT,
 											FUN_GET_LIMIT(THREADHOLD, 1) AS UPPER_LIMIT,
 											CHK_DATA,
 											'' AS DATA_AVG,
 											'' AS DATA_MIN,
 											'' AS DATA_MAX,
 											0 AS DATA_TYPE,
 											ROWNUM AS RID
 								FROM		TMP_EXP_CHK_DATA_SWAP
 								ORDER	BY	CHK_DATE
 							) A
 				WHERE		A.RID BETWEEN LI_LLIMIT AND LI_ULIMIT;
 				--DBMS_OUTPUT.PUT_LINE();
 				AI_AMOUNT := LI_CNT;
 			ELSE
 				-- 7 to export statistics data
 				
 				-- 7.1 valid the CHK_DATA is number
 				UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (INSTR(CHK_DATA, ' ') > 1 OR INSTR(CHK_DATA, '-') > 1 OR INSTR(CHK_DATA, '+') > 1 OR LENGTH(TRIM(TRANSLATE(CHK_DATA, '', '.+-'))) = 0);
 				UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (LENGTH(CHK_DATA) - LENGTH(REPLACE(CHK_DATA, '-', ''))) > 1;
 				UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (LENGTH(CHK_DATA) - LENGTH(REPLACE(CHK_DATA, '+', ''))) > 1;
 				UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND (LENGTH(CHK_DATA) - LENGTH(REPLACE(CHK_DATA, '.', ''))) > 1;
 				UPDATE TMP_EXP_CHK_DATA_SWAP SET NFLAG = 1 WHERE NFLAG = 0 AND LENGTH(TRIM(TRANSLATE(CHK_DATA, '', '0123456789+-.'))) > 0;
 				
 				SELECT	SIGN(SUM(NFLAG))
 				INTO		LI_CNT
 				FROM		TMP_EXP_CHK_DATA_SWAP;
 				
 				IF LI_CNT > 0 THEN
 					-- The CHK_DATA include invalid charactor, and do not to statistic it again.
 					AI_AMOUNT := -1;
 					RETURN;
 				END IF;
 				
 				-- 7.2 calculate digit
 				SELECT	NVL(MAX(ALEN - DECODE(DIGIT, 0, ALEN, DIGIT)), 0) + 1
 				INTO		LI_DIGIT
 				FROM		(
 								SELECT	LENGTH(CHK_DATA) AS ALEN,
 											INSTR(CHK_DATA, '.') AS DIGIT
 								FROM		TMP_EXP_CHK_DATA_SWAP
 								WHERE		NFLAG = 0
 							);
 				
 				-- 7.3 Statistic AVG, MIN, MAX
 				INSERT INTO TMP_EXPORT_CHECK_DATA
 						(
 							CHK_OBJECT,
 							CHK_DATE,
 							LOWER_LIMIT,
 							UPPER_LIMIT,
 							CHK_DATA,
 							DATA_AVG,
 							DATA_MIN,
 							DATA_MAX,
 							DATA_TYPE
 						)
 				SELECT	CHK_OBJECT,
 							CHK_DATE,
 							LOWER_LIMIT,
 							UPPER_LIMIT,
 							CHK_DATA,
 							DATA_AVG,
 							DATA_MIN,
 							DATA_MAX,
 							DATA_TYPE
 				FROM	(
 							SELECT	A.CHK_OBJECT,
 										A.CHK_DATE,
 										A.LOWER_LIMIT,
 										A.UPPER_LIMIT,
 										'' AS CHK_DATA,
 										ROUND(AVG(A.CHK_DATA), LI_DIGIT) AS DATA_AVG,
 										ROUND(MIN(A.CHK_DATA), LI_DIGIT) AS DATA_MIN,
 										ROUND(MAX(A.CHK_DATA), LI_DIGIT) AS DATA_MAX,
 										1 AS DATA_TYPE,
 										ROW_NUMBER() over(ORDER BY A.CHK_DATE) AS RID
 							FROM		(
 											SELECT	CHK_OBJECT,
 														to_date(to_char(CHK_DATE,'yyyy-mm-dd'),'yyyy-mm-dd') AS CHK_DATE,
 														FUN_GET_LIMIT(THREADHOLD, 0) AS LOWER_LIMIT,
 														FUN_GET_LIMIT(THREADHOLD, 1) AS UPPER_LIMIT,
 														CHK_DATA
 											FROM		TMP_EXP_CHK_DATA_SWAP
 											WHERE		NFLAG = 0
 										) A
 							GROUP BY	A.CHK_OBJECT, A.CHK_DATE, A.LOWER_LIMIT, A.UPPER_LIMIT
 							
 						)
 				WHERE	RID <= LI_PAGE_SIZE;
 				
 				LI_CNT := sql%rowcount;
 				AI_AMOUNT := LI_CNT;
 			END IF;
 		END;
 		END PROC_EXPORT_CHECK_DATA;
 		/
		CREATE OR REPLACE TRIGGER TRB_IU_HC_ACTWARNING
				BEFORE INSERT OR UPDATE
				ON IEAI_HC_ACTWARNING
				REFERENCING NEW AS N
			  FOR EACH ROW
			
					
				DECLARE	LV_MSG		VARCHAR2(128);
						LV_PRGNAME	VARCHAR2(50);
						LV_SUPPORT	VARCHAR2(128);
						LI_WTYPE		INTEGER;
				BEGIN
					SELECT	NVL(MAX(A.MSG_CN), ''),
								NVL(MAX(A.SUPPORT), '')
					INTO		LV_MSG,
								LV_SUPPORT
					FROM		IEAI_HD_ERROR_CODE_TABLE A
					WHERE		A.ERRCODE = :N.ERRCODE;
			
					IF :N.PRGNAME IS NULL THEN
							LV_PRGNAME := NULL;
							LI_WTYPE := 20;
					ELSE
						SELECT 	MAX(T1.ICHKITEMNAME),
									NVL(MAX(ILEVELID), 20)
						INTO		LV_PRGNAME,
									LI_WTYPE
						FROM		IEAI_CHKITEM T1, IEAI_COM_CHK C
						WHERE		T1.ISHELLNAME = :N.PRGNAME
            AND  T1.ICHKITEMID=C.CHKITEMID;
					END IF;
			
					 :N.WTYPE := LI_WTYPE;
			
					IF	:N.AMESSAGE IS NULL OR :N.AMESSAGE = '' THEN
						 :N.WMESSAGE := LV_MSG || '。';
					ELSE
						 :N.WMESSAGE := LV_MSG || '(' || :N.AMESSAGE || ')。';
					END IF;

					IF	LV_PRGNAME IS NULL OR :N.CHKVALUE IS NULL OR :N.THREADHOLD IS NULL THEN
						 :N.WMESSAGE := '自动化巡检系统报告：' || :N.WMESSAGE || LV_SUPPORT;
					ELSE
						 :N.WMESSAGE := '自动化巡检系统报告：' || :N.WMESSAGE || LV_SUPPORT || '(' || LV_PRGNAME || ', 检测值:' || :N.CHKVALUE || ',基线:' || :N.THREADHOLD || ')';
					END IF;
			
		END TRB_IU_HC_ACTWARNING;
		/