var serverCPValueWindow;
function detailMonitorWindow(meid,cinameshow,cpname,sysid) {
	var serverCP_win;
	
	contentPanel.setTitle(contentPanel.title+"->"+cpname+"->"+cinameshow);
	var serverCP_form = Ext.create('Ext.form.Panel', {
		border : false,
    	buttonAlign : 'right',
	    buttons: [{
	        text: '返回',
	        handler: function() {
	        	serverCP_win.close();
		    }
	    }]
	});
    

	Ext.define('serverCPData', {
        extend: 'Ext.data.Model',
        fields: [{name : 'cpid',type : 'string'},
                 {name : 'cptext',type : 'string'},
                 {name : 'cptime',type : 'string'},
                 {name : 'cpstatus',type : 'string'},
                 {name : 'ciid',type : 'string'},
                 {name : 'chkitemName',type : 'string'},
                 ]
    });
	
	serverCP_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        model: 'serverCPData',
        proxy: {
            type: 'ajax',
            url: 'hcpanelQueryCP.do?sysid='+sysid+'&meid='+meid,
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
	

	var serverCP_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
		   			   { text: '检查项主键',  dataIndex: 'ciid',hidden:true,menuDisabled : true},
		   			   { text: '检查项',  dataIndex: 'chkitemName',width:340,flex:1,menuDisabled : true},
	                   { text: '检查点主键',  dataIndex: 'cpid',hidden:true,menuDisabled : true},
	                   { text: '检查点内容',  dataIndex: 'cptext',flex:1,width:600,menuDisabled : true},
	                   { text: '检查时间',  dataIndex: 'cptime',width:140,flex:1,menuDisabled : true},
	                   { text: '检查结果',  dataIndex: 'cpstatus',width:80,menuDisabled : true,align:"center",renderer:function(value,metaData,record){
	                	   var backValue = ""; 
	                	   if(isCovertLevelinspectionpanel){
								if(value=='-6'){
									backValue = '<img src="images/tp_common.png" class="check_termination"></img>';
							   	}else if(value=='-5'){
									backValue = '<img src="images/tp_common.png" class="nocheck"></img>';
							   	}else if(value=='-1'){
									backValue = '<img src="images/tp_common.png" class="detect"></img>';
							   	}else if(value=='0'){
							   		backValue = '<img src="images/tp_common.png" class="normal"></img>';
							   	}else if(value=='1'){
							   		backValue = '<img src="images/tp_common.png" class="warn_05"></img>';
							   	}else if(value=='2'){
							   		backValue = '<img src="images/tp_common.png" class="warn_04"></img>';
							   	}else if(value=='3'){
							   		backValue = '<img src="images/tp_common.png" class="warn_03"></img>';
							   	}else if(value=='4'){
							   		backValue = '<img src="images/tp_common.png" class="warn_02"></img>';
							   	}else if(value=='5'){
							   		backValue = '<img src="images/tp_common.png" class="warn_01"></img>';
							   	}
							}else{
								if(value=='-6'){
		           					backValue = '<img src="images/tp_common.png" class="check_termination"></img>';
			           		   	}else if(value=='-5'){
			           				backValue = '<img src="images/tp_common.png" class="nocheck"></img>';
			           		   	}else if(value=='-1'){
			           				backValue = '<img src="images/tp_common.png" class="detect"></img>';
			           		   	}else if(value=='0'){
			           		   		backValue = '<img src="images/tp_common.png" class="normal"></img>';
			           		   	}else if(value=='1'){
			           		   		backValue = '<img src="images/tp_common.png" class="green_light"></img>';
			           		   	}else if(value=='2'){
			           		   		backValue = '<img src="images/tp_common.png" class="blue_light"></img>';
			           		   	}else if(value=='3'){
			           		   		backValue = '<img src="images/tp_common.png" class="purple_light"></img>';
			           		   	}else if(value=='4'){
			           		   		backValue = '<img src="images/tp_common.png" class="orange_light"></img>';
			           		   	}else if(value=='5'){
			           		   		backValue = '<img src="images/tp_common.png" class="red_light"></img>';
			           		   	}
							}
	                	   	return backValue;
	                   }}];

	
    var serverCP_grid = Ext.create('Ext.grid.Panel', {
    	height: contentPanel.getHeight()-120,
	    store:serverCP_store,
	    border:true,
	    viewConfig:{  
 	    	enableTextSelection:true  
 	    },
	    columnLines : true,
	    columns:serverCP_columns
	});
    
    serverCP_grid.on('celldblclick',function(obj, td, cellIndex, record, tr, rowIndex, e, eOpts){
    	var cpid = record.get("cpid");
    	serverCPValueWindow = Ext.create('Ext.window.Window', {
    	    title: '检查点值',
    	    modal : true,
    	    closeAction: 'destroy',
    	    constrain:true,
    	    autoScroll:true,
    	    width:900,
    	    height:contentPanel.getHeight()-20,
    	    resizable : true,
    	    layout: 'fit',
    	    loader: {
                url: 'forwardCPValue.do?cpid='+cpid,
                autoLoad: true,
                scripts : true
            }
    	}).show();
    })
    
   var mainPanel = Ext.create('Ext.panel.Panel',{
        border : false,
        bodyPadding : 5,
        items : [serverCP_form,serverCP_grid]
    });
    
    
    contentPanel.on('resize',function(){
    	serverCP_grid.setHeight(contentPanel.getHeight()-65);
    })
    
	serverCP_win = Ext.create('Ext.window.Window', {
	    title: contentPanel.title,
	    modal : true,
	    closeAction: 'destroy',
	    constrain:true,
	    //autoScroll:true,
	    width:contentPanel.getWidth()-20,
	    height:contentPanel.getHeight()-20,
	    resizable : true,
	    items : [mainPanel],
	    layout: 'fit',
	}).show();
    
    serverCP_win.on('close',function(){
    	contentPanel.setTitle('巡检看板');
    });
}
