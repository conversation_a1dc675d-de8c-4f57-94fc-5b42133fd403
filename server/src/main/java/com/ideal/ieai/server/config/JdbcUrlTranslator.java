/*
 * Created on 2005-8-3
 */
package com.ideal.ieai.server.config;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * a util used to translate jdbcUrl and it's port,server,schema and type
 * 
 * <AUTHOR> Shi
 */
public class JdbcUrlTranslator
{
    public static String getDBAddress ( String jdbcUrl, String dbName )
    {
        return getJdbcComponent(jdbcUrl, dbName, JDBC_SERVER);
    }

    public static String getDBPort ( String jdbcUrl, String dbName )
    {
        return getJdbcComponent(jdbcUrl, dbName, JDBC_PORT);
    }

    public static String getDBDataBase ( String jdbcUrl, String dbName )
    {
        return getJdbcComponent(jdbcUrl, dbName, JDBC_DATABASE);
    }

    public static String getDBTypeByName ( String name )
    {
        String jdbcName = (String) jdbcNameTypeMap.get(name);
        return jdbcName;
    }

    //
    // public static String getDBNameByType ( String type )
    // {
    // Iterator keyIt = jdbcNameTypeMap.keySet().iterator();
    // while (keyIt.hasNext())
    // {
    // String key = (String) keyIt.next();
    // String value = (String) jdbcNameTypeMap.get(key);
    // if (value.equals(type))
    // {
    // return key;
    // }
    // }
    // return null;
    // }

    public static String getDriver ( String dbName )
    {
        if (dbName == null)
        {
            return null;
        }
        if (dbName.equals(DBNAME_ORACLE9))
        {
            return "oracle.jdbc.driver.OracleDriver";
        }
        if (dbName.equals(DBNAME_SYBASE))
        {
            return "net.sourceforge.jtds.jdbc.Driver";
        }
        if (dbName.equals(DBNAME_MSSQL))
        {
            return "net.sourceforge.jtds.jdbc.Driver";
        }
        if (dbName.equals(DBNAME_DB2))
        {
            return "com.ibm.db2.jcc.DB2Driver";
        }
        if (dbName.equals(DBNAME_MYSQL))
        {
            return "com.mysql.jdbc.Driver";
        }
        if (dbName.equals(DBNAME_MSSQL_ODBC))
        {
            return "sun.jdbc.odbc.JdbcOdbcDriver";
        }
        //add by lch
        if (dbName.equals(DBNAME_DM))
        {
            return "dm.jdbc.driver.DmDriver";
        }
        //add by lzh
        if(dbName.equals(DBNAME_OCEANBASE)){
            return "com.mysql.jdbc.Driver";
        }
        if (dbName.equals(DBNAME_KINGBASEES))
        {
            return "com.kingbase8.Driver";
        }
        if(dbName.equals(DBNAME_GOLDENDB)){
            return "com.goldendb.jdbc.Driver";
        }
        return null;
    }

    public static String getJdbcUrl ( String dbName, String address,
            String port, String database )
    {
        String pattern = (String) jdbcUrlPatterns.get(dbName);
        if (pattern == null)
        {
            return null;
        }
        if ( null == port )
        	port = "";
        if ( port.length() > 0 )
        	port = ":" + port; //insert : in front of port number
        
        Object[] args = { address, port, database };
        String result = MessageFormat.format(pattern, args);
        return result;
    }

    public static String getDbNameByUrl ( String dbUrl )
    {
        if(dbUrl==null){
            return null;
        }
        for (Iterator iter = regexs.entrySet().iterator(); iter.hasNext();)
        {
            Entry entry = (Entry) iter.next();
            String regex = (String) entry.getValue();
            if (Pattern.matches(regex, dbUrl))
            {
                return (String) entry.getKey();
            }
        }
        if (dbUrl.startsWith("jdbc:oracle:thin:@")){
            return DBNAME_ORACLE9;
        } else if (dbUrl.startsWith("jdbc:kingbase8:")){
            return DBNAME_KINGBASEES;
        }else if (dbUrl.startsWith("jdbc:goldendb:")){
            return DBNAME_GOLDENDB;
        }
        return null;
    }
    
    public static String getJdbcUrl(String dbName,String url){
        String pattern=(String)jdbcUrlPatterns.get(dbName);
        if(pattern==null)
        {
            return null;
        }
        
        if(url.startsWith("jdbc:oracle:thin:@"))
            return url;
        
        Object[] args = { url};
        String result=MessageFormat.format(pattern, args);
        return result;
    }

    private static String getJdbcComponent ( String jdbcUrl, String dbName,
            int part )
    {
        if (jdbcUrl == null || jdbcUrl.length() == 0)
        {
            return null;
        }
        String regex = (String) regexs.get(dbName);
        if (regex == null)
        {
            return null;
        }

        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(jdbcUrl);

        if (m.matches())
        {
            if (m.groupCount() >= part)
            {
                return m.group(part);
            }
        }
        return null;
    }

    public static Map          regexs;                                // map
    // of
    // regexs to
    // match
    // jdbcUrl
    public static Map          jdbcUrlPatterns;                       // map
    // of
    // formats
    // to create
    // jdbcUrl
    public static Map          jdbcNameTypeMap;                       // map
    // of
    // show name
    // and db
    // type map

    public static final String DBNAME_DB2        = "DB2";

    public static final String DBNAME_MSSQL      = "SQL Server";

    public static final String DBNAME_MSSQL_ODBC = "SQL Server(ODBC)";

    public static final String DBNAME_ORACLE9    = "Oracle 9i";
    
    public static final String DBNAME_DM    = "dameng";

    public static final String DBNAME_SYBASE     = "Sybase";

    public static final String DBNAME_MYSQL      = "MySQL";

    public static final String DBNAME_OCEANBASE   =  "OceanBase(MySQL)";

    public static final String DBNAME_KINGBASEES    = "KingBaseES";
    
    public static final String DBNAME_GOLDENDB    = "GoldenDb(MySQL)";
    /**
     * DB TYPE
     */
    public static final String DBTYPE_DB2        = "db2";

    public static final String DBTYPE_MSSQL      = "mssql";

    public static final String DBTYPE_MSSQL_ODBC = "mssql";

    public static final String DBTYPE_ORACLE9    = "oracle9";
    
    public static final String DBTYPE_DM    = "dameng";

    public static final String DBTYPE_OCEANBASE = "OceanBase";

    public static final String DBTYPE_KINGBASE8    = "kingbase8";

    public static final String DBTYPE_SYBASE     = "sybase";

    public static final String DBTYPE_MYSQL      = "mysql";
    
    public static final String DBTYPE_GOLDENDB = "goldendb";
    // public static final String

    static
    {
    	//Pattern expect address, port, database
        jdbcUrlPatterns = new HashMap();
        jdbcUrlPatterns.put(DBNAME_DB2, "jdbc:db2://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_MSSQL, "jdbc:jtds:sqlserver://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_MSSQL_ODBC, "jdbc:odbc:{0}");
        jdbcUrlPatterns.put(DBNAME_ORACLE9, "jdbc:oracle:thin:@{0}{1}:{2}");
        jdbcUrlPatterns.put(DBNAME_SYBASE, "jdbc:jtds:sybase://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_MYSQL, "jdbc:mysql://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_DM, "jdbc:dm://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_OCEANBASE, "jdbc:mysql://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_KINGBASEES, "jdbc:kingbase8://{0}{1}/{2}");
        jdbcUrlPatterns.put(DBNAME_GOLDENDB, "jdbc:goldendb://{0}{1}/{2}");

        regexs = new HashMap();
        // regexs.put(DBNAME_ORACLE9, "jdbc:oracle:thin:@([^:]*):((\\d*)?:?)(.*)");
        regexs.put(DBNAME_ORACLE9, "jdbc:oracle:thin:@{0}");
        regexs.put(DBNAME_SYBASE, "jdbc:jtds:sybase://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_MSSQL,
                "jdbc:jtds:sqlserver://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_DB2, "jdbc:db2://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_MYSQL, "jdbc:mysql://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_MSSQL_ODBC, "jdbc:odbc:(.*)");
        regexs.put(DBNAME_DM, "jdbc:dm://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_OCEANBASE, "jdbc:mysql://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_KINGBASEES, "jdbc:kingbase8://([^:]*)(:?(\\d*)?)/(.*)");
        regexs.put(DBNAME_GOLDENDB, "jdbc:goldendb://([^:]*)(:?(\\d*)?)/(.*)");

        jdbcNameTypeMap = new HashMap();
        jdbcNameTypeMap.put(DBNAME_DB2, DBTYPE_DB2);
        jdbcNameTypeMap.put(DBNAME_MSSQL, DBTYPE_MSSQL);
        jdbcNameTypeMap.put(DBNAME_MSSQL_ODBC, DBTYPE_MSSQL_ODBC);
        jdbcNameTypeMap.put(DBNAME_ORACLE9, DBTYPE_ORACLE9);
        jdbcNameTypeMap.put(DBNAME_SYBASE, DBTYPE_SYBASE);
        jdbcNameTypeMap.put(DBNAME_MYSQL, DBTYPE_MYSQL);
        jdbcNameTypeMap.put(DBNAME_DM, DBTYPE_DM);
        jdbcNameTypeMap.put(DBNAME_OCEANBASE, DBTYPE_OCEANBASE);
        jdbcNameTypeMap.put(DBNAME_KINGBASEES, DBTYPE_KINGBASE8);
        jdbcNameTypeMap.put(DBNAME_GOLDENDB, DBTYPE_GOLDENDB);

    }

    private static final int   JDBC_DATABASE     = 4;

    private static final int   JDBC_PORT         = 3;

    private static final int   JDBC_SERVER       = 1;

    public static void main ( String[] args )
    {
        if (Pattern.matches("(.*):(\\d*):(.*)", "asdfsf:12414223:asfs"))
        {
            System.out.println("OK");
        }
        Pattern pattern = Pattern
                .compile("jdbc:jtds:sqlserver://([^:]*)(:?(\\d*)?)/(.*)");
        Matcher m = pattern.matcher("***************************************");
        if (m.matches())
        {
            System.out.println("OOOOOOOOOOOOOOk");
            System.out.println(m.groupCount());
            System.out.println(m.group(1));
            System.out.println(m.replaceFirst("abc"));
            String[] rsult = pattern.split(
                    "***************************************", 5);

            for (int i = 0; i < rsult.length; i++)
            {
                String string = rsult[i];
                System.out.println(string);
            }

        }

        System.out
                .println(getJdbcUrl(DBNAME_DB2, "localhost", "2222", "abcde"));

    }

}
