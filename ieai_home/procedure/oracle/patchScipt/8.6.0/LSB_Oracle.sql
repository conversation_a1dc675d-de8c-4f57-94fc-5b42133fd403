-- 8.0.3

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_NEW_EQUIPMENT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_LSB_NEW_EQUIPMENT
                    (IID NUMBER(19) NOT NULL,
                     ISNNUMBER VARCHAR(255),
                     ICOMPANY VARCHAR(255),
                     IMODELNAME VARCHAR(255),
                     ICPU  VARCHAR(255),
                     IMEMORY  VARCHAR(255),
                     <PERSON>IS<PERSON>  VARCHAR(255),
                     IMEMORYSUM NUMBER(19),
                     IDISKSUM NUMBER(19),
                     ICPUSUM NUMBER(19),
                     IDHCPIP  VARCHAR(255),
                     IOOBIP  VARCHAR(255),
                     IUSERID NUMBER(19),
                     IUSERNAME VARCHAR(255),
                     ICREATETIME VARCHAR(255),
                     ISVM VARCHAR(5),
                     CONSTRAINT PK_IEAI_LSB_NEW_EQUIPMENT PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_HARDWARE_TEMPLATE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_LSB_HARDWARE_TEMPLATE 
                   (IID NUMBER(19) NOT NULL,
                    ICOMPANY VARCHAR(255),
                    IMODELNAME VARCHAR(255),
                    ISTATUS SMALLINT,
                    ITYPE SMALLINT,
                    IUSERID NUMBER(19),
                    IUSERNAME VARCHAR(255),
                    ICREATETIME VARCHAR(255),
                    IUPPERID NUMBER(19), 
                    CONSTRAINT PK_IEAI_LSB_HARDWARE_TEMPLATE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_HARDWARE_TEM_RAID' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_LSB_HARDWARE_TEM_RAID 
                    (IID NUMBER(19) NOT NULL,
                     ITEMPLATEID NUMBER(19),
                     ICONFIGITEM VARCHAR(255),
                     ITYPE VARCHAR(2),
                     IITEMTYPE VARCHAR(2),
                     ICHECKED SMALLINT,
                     INAME VARCHAR(19),
                     ISCRIPT VARCHAR(255),
                     IPLACEHOLDER VARCHAR(255),
                     CONSTRAINT PK_IEAI_LSB_HARDWARE_TEM_RAID PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_HARDWARE_TEM_OOB' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_LSB_HARDWARE_TEM_OOB 
                    (IID NUMBER(19) NOT NULL,
                     ITEMPLATEID NUMBER(19),
                     ICONFIGITEM VARCHAR(255),
                     ITYPE VARCHAR(2),
                     IITEMTYPE VARCHAR(2),
                     ICHECKED SMALLINT,
                     INAME VARCHAR(19),
                     ISCRIPT VARCHAR(255),
                     IPLACEHOLDER VARCHAR(255),
                     CONSTRAINT PK_IEAI_LSB_HARDWARE_TEM_OOB PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_HARDWARE_TEM_ITEM' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_LSB_HARDWARE_TEM_ITEM 
                    (IID NUMBER(19) NOT NULL,
                     ITEMPLATEID NUMBER(19),
                     ICONTENT CLOB,
                     CONSTRAINT PK_IEAI_LSB_HARDWARE_TEM_ITEM PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_INSTALL_SYSCONFIG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := '	CREATE TABLE IEAI_LSB_INSTALL_SYSCONFIG (IID NUMBER(19) NOT NULL,ISTARTIP VARCHAR(255), IENDIP VARCHAR(255), IGATEWAY VARCHAR(255), CONSTRAINT PK_IEAI_LSB_INSTALL_SYSCONFIG PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 			
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_DEVICE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_LSB_DEVICE(
					 IID NUMBER(19) ,
					 ISN  VARCHAR2(255),
					 IHOSTNAME VARCHAR2(255) NOT NULL,
					 IAPP_IP_IID NUMBER(19) NOT NULL,
					 IMGR_IP_IID NUMBER(19) ,
					 IPEX_IID NUMBER(19)NOT NULL,
					 IHARDWARE_IID NUMBER(19),
					 ISYSINSTALL_IID NUMBER(19)NOT NULL,
					 ILOCATION_IID NUMBER(19)NOT NULL,
					 IFINACE_CODE VARCHAR2(255),
					 IINSTALL_PROCESS VARCHAR2(255),
					 IINSTALL_DETAIL CLOB,
					 IUSERID NUMBER(19),
					 IUSERNAME VARCHAR(255),
					 IBOOTOS_IP VARCHAR(255),
					 IEXTERNAL_IP VARCHAR(255),
					 ISTATE INTEGER,
					 ICREATETIME      NUMBER(19),
					 ILASTUPDATETIME  NUMBER(19),
					 CONSTRAINT PK_IEAI_LSB_DEVICE PRIMARY KEY (IID)
					)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_NETSGT_APP' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_LSB_NETSGT_APP(
					 IID NUMBER(19) ,
					 IP VARCHAR2(255) NOT NULL,
					 IMASK VARCHAR2(255) NOT NULL,
					 IGATWAY VARCHAR2(255) NOT NULL,
					 ITRUNK VARCHAR2(255) NOT NULL,
					 IBONDING VARCHAR2(255) NOT NULL,
					 CONSTRAINT PK_IEAI_LSB_NETSGT_APP PRIMARY KEY (IID)
					)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_NETSGT_MGR' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_LSB_NETSGT_MGR(
					 IID NUMBER(19),
					 IP VARCHAR2(255) NOT NULL,
					 IMASK VARCHAR2(255) NOT NULL,
					 IGATWAY VARCHAR2(255) NOT NULL,
					 CONSTRAINT PK_IEAI_LSB_NETSGT_MGR PRIMARY KEY (IID)
					)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_TEMPLATE_PEX' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_LSB_TEMPLATE_PEX(
					 IID NUMBER(19),
					 IOS VARCHAR2(255) NOT NULL,
					 ICONTENT CLOB,
					 CONSTRAINT PK_IEAI_LSB_TEMPLATE_PEX PRIMARY KEY (IID)
					)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_TEMPLATE_SYS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_LSB_TEMPLATE_SYS(
					 IID NUMBER(19) ,
					 IOS VARCHAR2(255) NOT NULL,
					 ICONTENT CLOB,
					 CONSTRAINT PK_IEAI_LSB_TEMPLATE_SYS PRIMARY KEY (IID)
					)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_LSB_LOCATION_MGR' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_LSB_LOCATION_MGR(
					 IID NUMBER(19),
					 ILOCATIONNAME VARCHAR2(255) NOT NULL,
					 IFATHER       NUMBER(19),
					 CONSTRAINT PK_IEAI_LSB_LOCATION_MGR PRIMARY KEY (IID)
					)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/