<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.common.utils.SessionData" %>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<html>  
  <head>    
    <title>access control</title>
     <script>
    <% SessionData sessionData = SessionData.getSessionData(request);
    String loginUser = sessionData.getLoginName();%>
    <%-- var isImmediate='<%=ServerEnv.getInstance().getBooleanConfig(Environment.DOUBLE_CHECK_EMER_PLANSTART_IMMEDIATE_SWITCH, false)%>'; --%>
    var loginUser = '<%=loginUser%>';
    </script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/ep/emstartNew/emstartNew.js"></script>
  </head>  
    
  <body >  
	 <div id="emstartNew" style="width: 100%;height: 100%">
</div>
  
  </body>  
  
</html> 