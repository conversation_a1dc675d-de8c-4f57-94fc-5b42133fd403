package com.ideal.dubbo.models.product;

import java.io.Serializable;
/**
 * 2023-09-22 heng<PERSON>_liu
 * 存放用于操作IEAI_SCRIPT_BIND_FUNC_VAR表的实体
 */
public class SaveVarAndFuncBean implements Serializable {
    private static final long serialVersionUID = 1L;

    private String iname;                   //绑定变量、函数名称
    private int bindVarFuncId;              //绑定变量、函数主键id
    private int ibandType;                  //绑定类型 1-变量，2-函数

    public String getIname() {
        return iname;
    }

    public void setIname(String iname) {
        this.iname = iname;
    }

    public int getBindVarFuncId() {
        return bindVarFuncId;
    }

    public void setBindVarFuncId(int bindVarFuncId) {
        this.bindVarFuncId = bindVarFuncId;
    }

    public int getIbandType() {
        return ibandType;
    }

    public void setIbandType(int ibandType) {
        this.ibandType = ibandType;
    }
}
