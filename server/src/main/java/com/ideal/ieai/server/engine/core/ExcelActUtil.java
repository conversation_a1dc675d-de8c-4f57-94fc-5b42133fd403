package com.ideal.ieai.server.engine.core;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.jobscheduling.bean.HeadJob;
import com.ideal.ieai.server.jobscheduling.repository.createexcel.ActInfoBeanExcel;
import com.ideal.ieai.server.jobscheduling.repository.createexcel.ActInfoDayStartBeanExcel;
import com.ideal.ieai.server.jobscheduling.repository.createexcel.ActInfoPreProBeanExcel;
import com.ideal.ieai.server.jobscheduling.repository.project.*;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.TaskUploadManager;
import com.ideal.ieai.server.jobscheduling.repository.taskupload.UpLoadExcelManager;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.*;
import com.ideal.ieai.server.repository.engine.EngineRepository;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.engine.RepWorkflowInstance;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.importexecl.InfoExeclServicesMultipleForSUS;
import com.ideal.ieai.server.repository.importexecl.InfoExeclUtilBean;
import com.ideal.ieai.server.repository.project.*;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import com.ideal.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <ul>
 * <li>Title: ExcelActUtil.java</li>
 * <li>Description:Excel活动 组织工具类</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 *
 * <AUTHOR>
 *
 *         2015年8月10日
 */
public class ExcelActUtil
{
    private static final Logger       _log  = Logger.getLogger(ExcelActUtil.class);
    static private final ExcelActUtil _inst = new ExcelActUtil();

    /**
     * <li>Description:</li>
     *
     * <AUTHOR> 2015年8月10日
     * @return return ExcelActUtil
     */
    static public ExcelActUtil getInstance ()
    {
        return _inst;
    }

    /**
     *
     */
    private ExcelActUtil()
    {
    }

    /**
     * <li>Description:查询工作流下的参数信息</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param flowid
     * @param con
     * @return return List<ExcelFlowParams>
     */
    public List<ExcelFlowParams> getExcelFlowParam ( long flowid, Connection con )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelFlowParams> list = new ArrayList<ExcelFlowParams>();
        try
        {
            String sql = "SELECT IPARAM_NAME ,IPARAM_TYPE FROM IEAI_FLOW_PARAM WHERE IID=?";
            pres = con.prepareStatement(sql);
            pres.setLong(1, flowid);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelFlowParams param = new ExcelFlowParams();
                param.setParamName(rs.getString("IPARAM_NAME"));
                param.setParamType(rs.getString("IPARAM_TYPE"));
                list.add(param);
            }

        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelFlowParam", _log);
        }
        return list;
    }

    /**
     * <li>Description:</li>
     *
     * <AUTHOR> 2015年10月12日
     * @return return List<ExcelFlowParams>
     */
    public List<ExcelFlowParams> getOneParam ()
    {
        List<ExcelFlowParams> list = new ArrayList<ExcelFlowParams>();
        ExcelFlowParams param = new ExcelFlowParams();
        param.setParamName("数据日期");
        param.setParamType("String");
        list.add(param);

        return list;
    }

    /**
     * <li>Description:</li>
     *
     * <AUTHOR> 2015年10月12日
     * @return return List<ExcelFlowParams>
     */
    public List<ExcelDayStartFlowParams> getDayStartOneParam ()
    {
        List<ExcelDayStartFlowParams> list = new ArrayList<ExcelDayStartFlowParams>();
        ExcelDayStartFlowParams param = new ExcelDayStartFlowParams();
        param.setParamName("数据日期");
        param.setParamType("String");
        list.add(param);

        return list;
    }

    /**
     * <li>Description:组织工作流下的活动信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param flowid
     * @param con
     * @return return List<ExcelAct>
     * @throws SQLException
     */
    public List<ExcelAct> getExcelActForGetActList ( long flowid, Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelAct> list = new ArrayList<ExcelAct>();
        try
        {
            String sql = "SELECT" + "  T1.IACT_ID," + "  T1.IACT_NAME," + "  T1.IFLOWNAME," + "  T1.IPROJECTNAME,"
                    + "  T1.IACT_SUCCID," + "  T1.IACT_DESC," + "  T1.IACT_TYPE," + "  T1.IACT_CONDITIONS " + " FROM"
                    + "  IEAI_SON_PROJECT T1 " + " WHERE" + "  IID=?";
            pres = con.prepareStatement(sql);
            pres.setLong(1, flowid);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelAct act = new ExcelAct();
                act.setActId(rs.getInt("IACT_ID"));
                act.setActName(rs.getString("IACT_NAME"));
                act.setActType(rs.getString("IACT_TYPE"));
                act.setActsuccid(rs.getInt("IACT_SUCCID")); // 触发 的活动ID
                act.setFlowname(rs.getString("IFLOWNAME"));
                act.setProjectName(rs.getString("IPROJECTNAME"));
                if (Constants.EXCEL_ACT_TYPE_START.equals(act.getActType())
                        || Constants.EXCEL_ACT_TYPE_END.equals(act.getActType())
                        || Constants.EXCEL_ACT_TYPE_CALLFLOW.equals(act.getActType()))
                {
                    list.add(act);
                    continue;
                } else
                {
                    getExcelActStartInfo(act, con);
                }
                list.add(act);
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:组织工作流下的活动信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param flowid
     * @param con
     * @return return List<ExcelAct>
     * @throws SQLException
     */
    public List<ExcelDayStartAct> getDayStartExcelActForGetActList ( long flowid, Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelDayStartAct> list = new ArrayList<ExcelDayStartAct>();
        try
        {
            String sql = "SELECT" + "  T1.IACT_ID," + "  T1.IACT_NAME," + "  T1.IFLOWNAME," + "  T1.IPROJECTNAME,"
                    + "  T1.IACT_SUCCID," + "  T1.IACT_DESC," + "  T1.IACT_TYPE," + "  T1.IACT_CONDITIONS " + " FROM"
                    + "  IEAI_SON_PROJECT T1 " + " WHERE" + "  IID=?";
            pres = con.prepareStatement(sql);
            pres.setLong(1, flowid);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelDayStartAct act = new ExcelDayStartAct();
                act.setActId(rs.getInt("IACT_ID"));
                act.setActName(rs.getString("IACT_NAME"));
                act.setActType(rs.getString("IACT_TYPE"));
                act.setActsuccid(rs.getInt("IACT_SUCCID")); // 触发 的活动ID
                act.setFlowname(rs.getString("IFLOWNAME"));
                act.setProjectName(rs.getString("IPROJECTNAME"));
                if (Constants.EXCEL_ACT_TYPE_START.equals(act.getActType())
                        || Constants.EXCEL_ACT_TYPE_END.equals(act.getActType())
                        || Constants.EXCEL_ACT_TYPE_LOOPSTART.equals(act.getActType())
                        || Constants.EXCEL_ACT_TYPE_LOOPEND.equals(act.getActType()))
                {
                    list.add(act);
                    continue;
                } else
                {
                    getDayStartExcelActStartInfo(act, con);
                }
                list.add(act);
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:组织工作流下的活动信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param flowid
     * @param con
     * @return return List<ExcelAct>
     * @throws SQLException
     */
    public List<ExcelDayStartAct> getMainDayStartExcelActForGetActList ( long flowid, Connection con )
            throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelDayStartAct> list = new ArrayList<ExcelDayStartAct>();
        try
        {
            String sql = "SELECT" + "  T1.IACT_ID," + "  T1.IACT_NAME," + "  T1.IFLOWNAME," + "  T1.IPROJECTNAME,"
                    + "  T1.IACT_SUCCID," + "  T1.IACT_DESC," + "  T1.IACT_TYPE," + "  T1.IACT_CONDITIONS " + " FROM"
                    + "  IEAI_SON_PROJECT T1 " + " WHERE" + "  IID=?";
            pres = con.prepareStatement(sql);
            pres.setLong(1, flowid);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelDayStartAct act = new ExcelDayStartAct();
                act.setActId(rs.getInt("IACT_ID"));
                act.setActName(rs.getString("IACT_NAME"));
                act.setActType(rs.getString("IACT_TYPE"));
                act.setActsuccid(rs.getInt("IACT_SUCCID")); // 触发 的活动ID
                act.setFlowname(rs.getString("IFLOWNAME"));
                act.setProjectName(rs.getString("IPROJECTNAME"));
                if (Constants.EXCEL_ACT_TYPE_CALLFLOW.equals(act.getActType()))
                {
                    list.add(act);
                    continue;
                }
                list.add(act);
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getMainDayStartExcelActForGetActList", _log);
        }
        return list;
    }

    /**
     * <li>Description:组织工作流下的活动信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param flowid
     * @param con
     * @return return List<ExcelAct>
     * @throws SQLException
     */
    public int getDayStartExcel ( String projectName, String flowName, Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        int batchType = -1;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT" + "  TT.IBATCHTYPE" + " FROM" + "  IEAI_EXCELMODEL_DAYSTART TT " + " WHERE"
                    + "  TT.IPRONAME=? AND TT.IFLOWNAME=?";
            pres = con.prepareStatement(sql);
            pres.setString(1, projectName);
            pres.setString(2, flowName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                batchType = rs.getInt("IBATCHTYPE");
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getDayStartExcel", _log);
        }
        return batchType;
    }

    /**
     * <li>Description:获取活动启动时所需要的必要信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param act
     * @param con return void
     * @throws SQLException
     */
    public void getDayStartExcelActStartInfo ( ExcelDayStartAct act, Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT" + "  T.IPRONAME ," + "  T.IFLOWNAME ," + "  T.IISPREFLOW ," + "  T.IPREFLOWNAME ,"
                    + "  T.ICALLPRONAME," + "  T.ICALLPREFLOW ," + "  T.ICALLMAINFLOW ," + "  T.ICALLSUCCFLOW ,"
                    + "  T.ICALLUPDATEFLOW ," + "  T.IISSYNC," + "  T.IFREQ ," + "  T.ISTARTTIME," + "  T.IOFFSET,"
                    + "  T.IOFFSETFUN," + "  T.IBATCHTYPE," + "  T.IAGENTGROUPNAME," + "  T.IFILEPATH" + " FROM"
                    + "  IEAI_EXCELMODEL_DAYSTART T " + " WHERE" + "  T.IPRONAME=? AND " + "  T.IFLOWNAME=?";

            pres = con.prepareStatement(sql);
            pres.setString(1, act.getProjectName());
            pres.setString(2, act.getFlowname());
            rs = pres.executeQuery();
            while (rs.next())
            {
                act.setProjectName(rs.getString("IPRONAME"));
                act.setFlowname(rs.getString("IFLOWNAME"));
                act.setIsPreflow(rs.getInt("IISPREFLOW"));
                act.setIsPreflowName(rs.getString("IPREFLOWNAME"));
                act.setCallPrjName(rs.getString("ICALLPRONAME"));
                if (act.getActName().equals(rs.getString("ICALLPREFLOW")))
                {
                    act.setiCallPreFlow(rs.getString("ICALLPREFLOW"));
                }
                if (act.getActName().equals(rs.getString("ICALLMAINFLOW")))
                {
                    act.setiCallMainFlow(rs.getString("ICALLMAINFLOW"));
                }
                if (act.getActName().equals(rs.getString("ICALLSUCCFLOW")))
                {
                    act.setiCallSuccFlow(rs.getString("ICALLSUCCFLOW"));
                }
                if (act.getActName().equals(rs.getString("ICALLUPDATEFLOW")))
                {
                    act.setiCallUpdateFlow(rs.getString("ICALLUPDATEFLOW"));
                }
                act.setIsSync(rs.getInt("IISSYNC"));
                act.setiPreq(rs.getInt("IFREQ"));
                act.setiStartTime(rs.getString("ISTARTTIME"));
                act.setIoffset(rs.getString("IOFFSET"));
                act.setIoffsetFun(rs.getString("IOFFSETFUN"));
                act.setiBatchType(rs.getInt("IBATCHTYPE"));
                act.setiAgentGroupName(rs.getString("IAGENTGROUPNAME"));
                act.setiFilePath(rs.getString("IFILEPATH"));

            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelAct", _log);
        }
    }

    /**
     * <li>Description:获取活动启动时所需要的必要信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param act
     * @param con return void
     * @throws SQLException
     */
    public void getExcelActStartInfo ( ExcelAct act, Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT" + "  T.IOKFILEFINDWEEK ," + "  T.ISHELLHOUSE ," + "  T.ISHELLABSOLUTEPATH ,"
                    + "  T.IAGENTSOURCEGROUP ," + "  T.ILASTLINE," + "  T.IWEIGHTS ," + "  T.IPRIORITY ,"
                    + "  T.APTGROUPNAME ," + "  T.APTFILENAME ," + "  T.APTRESGROUPNAME," + "  T.ISDB2 ,"
                    + "  T.DB2IP," + "  T.ICHECKAGENTGROUP," + "  T.IOKFILEABSOLUTEPATH," + "  T.ISAGENTGROUP,"
                    + "  T.IDELAYTIME," + "  T.IBRANCHCONDITION," + "  T.IRETRYNUM," + "  T.ICALENDNAME" + " FROM"
                    + "  IEAI_EXCELMODEL T " + " WHERE" + "  T.ICHILDPRONAME=? AND " + "  T.IACTNAME=?";

            pres = con.prepareStatement(sql);
            pres.setString(1, act.getProjectName());
            pres.setString(2, act.getFlowname());
            rs = pres.executeQuery();
            while (rs.next())
            {
                act.setIokfileindweek(rs.getInt("IOKFILEFINDWEEK"));
                act.setIshellhouse(rs.getString("ISHELLHOUSE"));
                act.setIshellabspath(rs.getString("ISHELLABSOLUTEPATH"));
                act.setIagentResourceGroup(rs.getString("IAGENTSOURCEGROUP"));
                act.setIlastline(rs.getString("ILASTLINE"));
                act.setIweights(rs.getInt("IWEIGHTS"));
                act.setIprioity(rs.getInt("IPRIORITY"));
                act.setAptgroupname(rs.getString("APTGROUPNAME"));
                act.setAptfilename(rs.getString("APTFILENAME"));
                act.setAptresoupname(rs.getString("APTRESGROUPNAME"));
                act.setDB2(rs.getString("ISDB2"));
                act.setDB2IP(rs.getString("DB2IP"));
                act.setCheckagentgroup(rs.getString("ICHECKAGENTGROUP"));
                act.setOkfileabspath(rs.getString("IOKFILEABSOLUTEPATH"));
                act.setIsAgentGroup(rs.getInt("ISAGENTGROUP"));
                act.setDelayTime(rs.getString("IDELAYTIME"));
                act.setBranchCondition(rs.getString("IBRANCHCONDITION"));
                act.setReTryCount(rs.getInt("IRETRYNUM"));
                act.setCalendName(rs.getString("ICALENDNAME"));
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelAct", _log);
        }
    }

    /**
     * <li>Description:获取活动启动时所需要的必要信息</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param act
     * @param con return void
     * @throws SQLException
     */
    public void getDayStartExcelActStartInfo ( ExcelAct act, Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT" + "  T.IOKFILEFINDWEEK ," + "  T.ISHELLHOUSE ," + "  T.ISHELLABSOLUTEPATH ,"
                    + "  T.IAGENTSOURCEGROUP ," + "  T.ILASTLINE," + "  T.IWEIGHTS ," + "  T.IPRIORITY ,"
                    + "  T.APTGROUPNAME ," + "  T.APTFILENAME ," + "  T.APTRESGROUPNAME," + "  T.ISDB2 ,"
                    + "  T.DB2IP," + "  T.ICHECKAGENTGROUP," + "  T.IOKFILEABSOLUTEPATH" + " FROM"
                    + "  IEAI_EXCELMODEL T " + " WHERE" + "  T.ICHILDPRONAME=? AND " + "  T.IACTNAME=?";

            pres = con.prepareStatement(sql);
            pres.setString(1, act.getProjectName());
            pres.setString(2, act.getFlowname());
            rs = pres.executeQuery();
            while (rs.next())
            {
                act.setIokfileindweek(rs.getInt("IOKFILEFINDWEEK"));
                act.setIshellhouse(rs.getString("ISHELLHOUSE"));
                act.setIshellabspath(rs.getString("ISHELLABSOLUTEPATH"));
                act.setIagentResourceGroup(rs.getString("IAGENTSOURCEGROUP"));
                act.setIlastline(rs.getString("ILASTLINE"));
                act.setIweights(rs.getInt("IWEIGHTS"));
                act.setIprioity(rs.getInt("IPRIORITY"));
                act.setAptgroupname(rs.getString("APTGROUPNAME"));
                act.setAptfilename(rs.getString("APTFILENAME"));
                act.setAptresoupname(rs.getString("APTRESGROUPNAME"));
                act.setDB2(rs.getString("ISDB2"));
                act.setDB2IP(rs.getString("DB2IP"));
                act.setCheckagentgroup(rs.getString("ICHECKAGENTGROUP"));
                act.setOkfileabspath(rs.getString("IOKFILEABSOLUTEPATH"));

            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelAct", _log);
        }
    }

    /**
     * <li>Description:查询工作流下的所有活动信息</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param flowid
     * @param con
     * @return return List<ExcelAct> update 2015-08-11 组织活动信息时 查询活动所有属性
     */
    public List<ExcelAct> getExcelAct ( long flowid, Connection con )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelAct> list = new ArrayList<ExcelAct>();
        try
        {
            String sql = "SELECT " + "T1.IACT_ID," + "T1.IACT_NAME," + "T1.IACT_SUCCID," + "T1.IACT_DESC,"
                    + "T1.IACT_TYPE," + "T1.IACT_CONDITIONS ," + "T2.IOKFILEFINDWEEK ," + "T2.ISHELLHOUSE ,"
                    + "T2.ISHELLABSOLUTEPATH ," + "T2.IAGENTSOURCEGROUP ," + "T2.ILASTLINE," + "T2.IWEIGHTS ,"
                    + "T2.IPRIORITY ," + "T2.APTGROUPNAME ," + "T2.APTFILENAME ," + "T2.APTRESGROUPNAME,"
                    + "T2.ISDB2 ," + "T2.DB2IP," + "T2.ICHECKAGENTGROUP," + "T2.IOKFILEABSOLUTEPATH"
                    + "   FROM  IEAI_SON_PROJECT T1 ,IEAI_EXCELMODEL  T2 " + " WHERE "
                    + "IID= ? AND T1.IFLOWNAME=T2.IACTNAME(+) AND T1.IPROJECTNAME=T2.ICHILDPRONAME(+) ORDER BY IACT_ID";
            pres = con.prepareStatement(sql);
            pres.setLong(1, flowid);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelAct act = new ExcelAct();
                act.setActId(rs.getInt("IACT_ID"));
                act.setActName(rs.getString("IACT_NAME"));
                act.setActType(rs.getString("IACT_TYPE"));
                act.setActsuccid(rs.getInt("IACT_SUCCID")); // 触发 的活动ID
                act.setIokfileindweek(rs.getInt("IOKFILEFINDWEEK"));
                act.setIshellhouse(rs.getString("ISHELLHOUSE"));
                act.setIshellabspath(rs.getString("ISHELLABSOLUTEPATH"));
                act.setIagentResourceGroup(rs.getString("IAGENTSOURCEGROUP"));
                act.setIlastline(rs.getString("ILASTLINE"));
                act.setIweights(rs.getInt("IWEIGHTS"));
                act.setIprioity(rs.getInt("IPRIORITY"));
                act.setAptgroupname(rs.getString("APTGROUPNAME"));
                act.setAptfilename(rs.getString("APTFILENAME"));
                act.setAptresoupname(rs.getString("APTRESGROUPNAME"));
                act.setDB2(rs.getString("ISDB2"));
                act.setDB2IP(rs.getString("DB2IP"));
                act.setCheckagentgroup(rs.getString("ICHECKAGENTGROUP"));
                act.setOkfileabspath(rs.getString("IOKFILEABSOLUTEPATH"));
                list.add(act);
            }

        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:查询主工作流下的所有活动信息</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param flowid
     * @param con
     * @return return List<ExcelAct> update 2015-08-19 fengchun_zhang 修改 查询主流程下的所有工作流调用时。 组织 各活动的
     *         前后继数量 update 2015-08-21 fengchun_zhang 修改方法返回类型
     */
    public List<ExcelAct> getMainFlowExcelAct ( Connection conn, String proName, String mainFlownme )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelAct> list = new ArrayList<ExcelAct>();
        try
        {
            String sql = "SELECT IHEADTAILFLAG, (SELECT COUNT(IOPERATIONID) FROM IEAI_ACTSUCC  WHERE IOPERATIONID=T.IOPERATIONID  AND IMAINLINENAME=?) AS SUCCNUM ,"
                    + " (SELECT COUNT(IOPERATIONID) FROM IEAI_ACTPRE  WHERE IOPERATIONID=T.IOPERATIONID  AND IMAINLINENAME=?) AS PRENUM ,"
                    + " T.IOPERATIONID,T.IMAINPRONAME, T.IMAINLINENAME,T.ICHILDPRONAME,T.IACTNAME,T.ICALENDNAME FROM IEAI_EXCELMODEL  T WHERE T.IMAINPRONAME=? AND T.IMAINLINENAME=?";
            pres = conn.prepareStatement(sql);
            pres.setString(1, mainFlownme);
            pres.setString(2, mainFlownme);
            pres.setString(3, proName);
            pres.setString(4, mainFlownme);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelAct act = new ExcelAct();
                int headflag = rs.getInt("IHEADTAILFLAG");
                if (headflag == 2 || headflag == 1)
                {
                    act.setPreNum(1 + rs.getInt("PRENUM"));
                } else
                {
                    act.setPreNum(rs.getInt("PRENUM"));
                }
                act.setSucNum(rs.getInt("SUCCNUM"));
                act.setActId(rs.getLong("IOPERATIONID"));
                act.setActName(rs.getString("IACTNAME"));
                act.setCallPrjName(rs.getString("ICHILDPRONAME"));
                act.setCallFlowname(rs.getString("IACTNAME"));
                act.setCalendName(rs.getString("ICALENDNAME"));
                list.add(act);
            }

        } catch (Exception e)
        {
            _log.error("getMainFlowExcelAct excure sql  is err :" + e);
        } finally
        {
            DBResource.closePSRS(rs, pres, "getMainFlowExcelAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:查询主工作流下的所有活动信息</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param flowid
     * @param con
     * @return return List<ExcelAct> update 2015-08-19 fengchun_zhang 修改 查询主流程下的所有工作流调用时。 组织 各活动的
     *         前后继数量 update 2015-08-21 fengchun_zhang 修改方法返回类型
     */
    public List<ExcelDayStartAct> getDayStartMainFlowExcelAct ( Connection conn, String proName, String mainFlownme )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelDayStartAct> list = new ArrayList<ExcelDayStartAct>();
        try
        {
            String sql = "SELECT T.IHEADFLAG,(SELECT COUNT(T1.IID) FROM IEAI_ACTSUCC_PREPRO T1 WHERE T1.IID = T.IID ) AS SUCCNUM,"
                    + " (SELECT COUNT(T2.IID) FROM IEAI_ACTPRE_PREPRO T2 WHERE T2.IID = T.IID ) AS PRENUM,T.IID,T.IPRONAME,T.IFLOWNAME,"
                    + " T.ICALLPRONAME,T.IACTNAME FROM IEAI_EXCELMOLDE_PREPRO T WHERE T.IPRONAME = ? AND T.IFLOWNAME = ?";
            pres = conn.prepareStatement(sql);
            pres.setString(1, proName);
            pres.setString(2, mainFlownme);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelDayStartAct act = new ExcelDayStartAct();
                int headflag = rs.getInt("IHEADFLAG");
                if (headflag == 1)
                {
                    act.setPreNum(1 + rs.getInt("PRENUM"));
                } else
                {
                    act.setPreNum(rs.getInt("PRENUM"));
                }
                act.setSucNum(rs.getInt("SUCCNUM"));
                act.setActId(rs.getLong("IID"));
                act.setActName(rs.getString("IACTNAME"));
                act.setCallPrjName(rs.getString("ICALLPRONAME"));
                act.setCallFlowname(rs.getString("IACTNAME"));
                list.add(act);
            }

        } catch (SQLException e)
        {
            _log.error("getMainFlowExcelAct excure sql  is err :" + e);
        } finally
        {
            DBResource.closePSRS(rs, pres, "getMainFlowExcelAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:查询主线中的活动 的触发活动</li>
     *
     * <AUTHOR> 2015年9月18日
     * @param proName
     * @param mainFlownme
     * @return return List<ExcelAct> update 2015-09-19 fengchun_zhang SQL变更添加条件。处理跨工程触发
     */
    public List<ExcelAct> getMainActSuccAct ( Connection conn, String proName, String mainLineName, String actName )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelAct> list = new ArrayList<ExcelAct>();
        try
        {
            String sql = "SELECT S.ISUCCACTNAME,  S.ICHILDPROJECTNAME FROM IEAI_ACTSUCC  S ,IEAI_EXCELMODEL  T WHERE   T.IMAINPRONAME=? AND T.IMAINLINENAME=? AND T.IACTNAME=? "
                    + " AND T.IOPERATIONID=S.IOPERATIONID AND T.IMAINLINENAME=S.IMAINLINENAME ";
            pres = conn.prepareStatement(sql);
            pres.setString(1, proName);
            pres.setString(2, mainLineName);
            pres.setString(3, actName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelAct act = new ExcelAct();
                act.setActName(rs.getString("ISUCCACTNAME"));
                act.setCallPrjName(rs.getString("ICHILDPROJECTNAME"));
                act.setCallFlowname(rs.getString("ISUCCACTNAME"));
                list.add(act);
            }

        } catch (SQLException e)
        {
            _log.error("getMainActSuccAct excure sql  is err :" + e);
        } finally
        {
            DBResource.closePSRS(rs, pres, "getMainActSuccAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:查询主线中的活动 的触发活动</li>
     *
     * <AUTHOR> 2015年9月18日
     * @param proName
     * @param mainFlownme
     * @return return List<ExcelAct> update 2015-09-19 fengchun_zhang SQL变更添加条件。处理跨工程触发
     */
    public List<ExcelDayStartAct> getDayStartMainActSuccAct ( Connection conn, String proName, String mainLineName,
                                                              String actName )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelDayStartAct> list = new ArrayList<ExcelDayStartAct>();
        try
        {
            String sql = "  SELECT TW.IACTNAME,TW.ICALLPRONAME  FROM IEAI_EXCELMOLDE_PREPRO TW WHERE TW.IID IN (select TT.ISELFID from IEAI_ACTSUCC_PREPRO  tt where tt.iid =(SELECT AR.IID FROM IEAI_EXCELMOLDE_PREPRO AR WHERE AR.IACTNAME = ? AND AR.IPRONAME = ?   AND AR.IFLOWNAME = ?))";
            pres = conn.prepareStatement(sql);
            pres.setString(1, actName);
            pres.setString(2, proName);
            pres.setString(3, mainLineName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelDayStartAct act = new ExcelDayStartAct();
                act.setActName(rs.getString("IACTNAME"));
                act.setCallPrjName(rs.getString("ICALLPRONAME"));
                act.setCallFlowname(rs.getString("IACTNAME"));
                list.add(act);
            }

        } catch (SQLException e)
        {
            _log.error("getMainActSuccAct excure sql  is err :" + e);
        } finally
        {
            DBResource.closePSRS(rs, pres, "getMainActSuccAct", _log);
        }
        return list;
    }

    /**
     * <li>Description:组织Excelproject对像属性</li>
     *
     * <AUTHOR> 2015年8月25日
     * @param prjName
     * @return return ExcelProject
     * @throws SQLException
     */
    public ExcelProject getExcelProject ( String prjName, int dbType ) throws SQLException, DBException
    {
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        ExcelProject pro = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE "
                    + " FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC )"
                    + " WHERE ROWNUM=1";
            if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                sql = "SELECT IID,IUUID,IMAINPRONAME,PROTYPE FROM ( SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E "
                        + "WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE,row_number() over() AS ROWNUM "
                        + "FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC )  ) TT where TT.ROWNUM = 1";
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                sql = "SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE "
                        + "FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC  ) TT  LIMIT 1";
            }
            pres = con.prepareStatement(sql);
            pres.setString(1, prjName);
            pres.setString(2, prjName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                pro = new ExcelProject();
                pro.setIid(rs.getLong("IID"));
                pro.setUuid(rs.getString("IUUID"));
                pro.setPrjName(rs.getString("IMAINPRONAME"));
                pro.setProjectType(rs.getString("IMAINPRONAME"));
                // pro.setProjectType(rs.getString("IMAINPRONAME"));
                pro.setProType(rs.getDouble("PROTYPE"));
                pro.setFlows(getExcelFlows(pro.getProjectType(), pro.getIid(), prjName, con));

            }

        } catch (SQLException e)
        {
            _log.error("getExcelProject execute sql error is :", e);
            throw new SQLException("getExcelProject");
        } catch (DBException e)
        {
            _log.error("getExcelProject execute db error is :", e);
            throw new SQLException("getExcelProject");
        } finally
        {
            DBResource.closeConn(con, rs, pres, "getExcelProject", _log);
        }
        return pro;
    }

    /**
     * <li>Description:组织Excelproject对像属性</li>
     *
     * <AUTHOR> 2015年8月25日
     * @param prjName
     * @return return ExcelProject
     * @throws SQLException
     */
    public ExcelProject getExcelProject ( String prjName, Connection con ) throws SQLException, DBException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        ExcelProject pro = null;
        try
        {
            String sql = "SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME, PROTYPE"
                    + " FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC )"
                    + " WHERE ROWNUM=1";
            if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                sql = "SELECT IID,IUUID,IMAINPRONAME,PROTYPE FROM ( SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E "
                        + "WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE,row_number() over() AS ROWNUM "
                        + "FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC )  ) TT where TT.ROWNUM = 1";
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                sql = "SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE "
                        + "FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC  ) TT  LIMIT 1";
            }
            pres = con.prepareStatement(sql);
            pres.setString(1, prjName);
            pres.setString(2, prjName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                pro = new ExcelProject();
                pro.setIid(rs.getLong("IID"));
                pro.setUuid(rs.getString("IUUID"));
                pro.setPrjName(rs.getString("IMAINPRONAME"));
                pro.setProjectType(rs.getString("IMAINPRONAME"));
                pro.setProType(rs.getDouble("PROTYPE"));
                pro.setFlows(getExcelFlows(pro.getProjectType(), pro.getIid(), prjName, con));

            }

        } catch (SQLException e)
        {
            _log.error("getExcelProject execute sql error is :", e);
            throw new SQLException("getExcelProject");
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelProject", _log);
        }
        return pro;
    }

    /**
     * <li>Description:组织Excelproject对像属性</li>
     *
     * <AUTHOR> 2015年8月25日
     * @param prjName
     * @return return ExcelProject
     * @throws SQLException
     */
    public ExcelDayStartProject getDayStartExcelProject ( String prjName, String uuid, Connection con )
            throws SQLException, DBException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        ExcelDayStartProject pro = null;
        try
        {
            String sql = "SELECT IID ,IUUID , (SELECT distinct(E.IPRONAME) FROM ieai_excelmodel_daystart E WHERE  E.IPREFLOWNAME=?) AS IPRONAME,PROTYPE "
                    + " FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? AND P.IUUID=? )"
                    + " WHERE ROWNUM=1";
            if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                sql = "SELECT IID,IUUID,IPRONAME,PROTYPE FROM (SELECT IID ,IUUID , (SELECT distinct(E.IPRONAME) FROM ieai_excelmodel_daystart E WHERE "
                        + " E.IPREFLOWNAME=?) AS IPRONAME,PROTYPE,row_number() over() AS ROWNUM FROM "
                        + " (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? AND P.IUUID=? ))  TT where TT.ROWNUM = 1";
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                sql = "SELECT IID ,IUUID , (SELECT distinct(E.IPRONAME) FROM ieai_excelmodel_daystart E WHERE  E.IPREFLOWNAME=?) AS IPRONAME,PROTYPE FROM "
                        + " (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? AND P.IUUID=? ) TT LIMIT 1 ";
            }
            pres = con.prepareStatement(sql);
            pres.setString(1, prjName);
            pres.setString(2, prjName);
            pres.setString(3, uuid);
            rs = pres.executeQuery();
            while (rs.next())
            {
                pro = new ExcelDayStartProject();
                pro.setIid(rs.getLong("IID"));
                pro.setUuid(rs.getString("IUUID"));
                pro.setPrjName(rs.getString("IPRONAME"));
                pro.setProjectType(rs.getString("IPRONAME"));
                pro.setProType(rs.getDouble("PROTYPE"));
                pro.setFlows(getDayStartExcelFlows(pro.getProjectType(), pro.getIid(), prjName, con));

            }

        } catch (SQLException e)
        {
            _log.error("getExcelProject execute sql error is :", e);
            throw new SQLException("getExcelProject");
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelProject", _log);
        }
        return pro;
    }

    /**
     *
     * @Title: getExcelProjectByOneFlow
     * @Description: TODO(组织Excelproject对像属性(一个工作流))
     * @param prjName
     * @param flowid
     * @return
     * @return ExcelProject 返回类型
     * @throws @变更记录 2015-9-29 yunpeng_zhang
     */
    public ExcelProject getExcelProjectByOneFlow ( String prjName, String iflowname, int dbType )
    {
        Connection con = null;
        PreparedStatement pres = null;
        ResultSet rs = null;
        ExcelProject pro = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "SELECT IID ,IUUID,(SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE"
                    + " FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC )"
                    + " WHERE ROWNUM=1";
            if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                sql = "SELECT IID,IUUID,IMAINPRONAME,PROTYPE FROM ( SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E "
                        + "WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE,row_number() over() AS ROWNUM "
                        + "FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC )  ) TT where TT.ROWNUM = 1";
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                sql = "SELECT IID ,IUUID , (SELECT distinct(E.IMAINPRONAME) FROM IEAI_EXCELMODEL E WHERE  E.IMAINPRONAME=?) AS IMAINPRONAME,PROTYPE "
                        + "FROM (SELECT IID ,IUUID,PROTYPE FROM IEAI_PROJECT P WHERE P.INAME=? ORDER BY P.IUPLOADNUM DESC  ) TT  LIMIT 1";
            }

            pres = con.prepareStatement(sql);
            pres.setString(1, prjName);
            pres.setString(2, prjName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                pro = new ExcelProject();
                pro.setIid(rs.getLong("IID"));
                pro.setUuid(rs.getString("IUUID"));
                pro.setPrjName(rs.getString("IMAINPRONAME"));
                pro.setProjectType(rs.getString("IMAINPRONAME"));
                pro.setProType(rs.getDouble("PROTYPE"));
                pro.setFlows(this.getExcelFlowsOfOne(pro.getIid(), pro.getProjectType(), iflowname, con));

            }

        } catch (SQLException e)
        {
            _log.error("getExcelProject execute sql error is :", e);
        } catch (DBException e)
        {
            _log.error("getExcelProject execute db error is :", e);
        } finally
        {
            DBResource.closeConn(con, rs, pres, "getExcelProject", _log);
        }
        return pro;
    }

    /**
     * <li>Description:查询工程下的所有工作流信息及参数信息</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param prjName
     * @return return List<ExcelFlows>
     * @throws SQLException
     */
    public List<ExcelFlows> getExcelFlows ( String projectType, long prjId, String prjName, Connection con )
            throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelFlows> list = new ArrayList<ExcelFlows>();
        try
        {
            String sql = "SELECT   IID,IFLOWDESC, IFLOWNAME FROM IEAI_FLOWDEF T WHERE T.IPRJID=?";

            pres = con.prepareStatement(sql);
            pres.setLong(1, prjId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelFlows flow = new ExcelFlows();
                flow.setFlowName(rs.getString("IFLOWNAME"));
                flow.setFlowDesc(rs.getString("IFLOWDESC"));
                flow.setFlowid(rs.getLong("IID"));
                /* 判断是否是主流程 */
                if (null == projectType || "".equals(projectType))
                {
                    flow.setMainFlow(false);
                } else
                {
                    flow.setMainFlow(true);
                }
                if (!flow.isMainFlow())
                {
                    flow.setActList(ExcelActUtil.getInstance().getExcelActForGetActList(flow.getFlowid(), con)); // 活动集合
                }
                flow.setFlowParamList(ExcelActUtil.getInstance().getOneParam()); // 参数集合
                list.add(flow);
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelFlows", _log);
        }
        return list;
    }

    /**
     * <li>Description:查询工程下的所有工作流信息及参数信息</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param prjName
     * @return return List<ExcelFlows>
     * @throws SQLException
     */
    public List<ExcelDayStartFlows> getDayStartExcelFlows ( String projectType, long prjId, String prjName,
                                                            Connection con ) throws SQLException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelDayStartFlows> list = new ArrayList<ExcelDayStartFlows>();
        try
        {
            String sql = "SELECT T1.IFLOWNAME,T1.IID,T1.IFLOWDESC FROM IEAI_FLOWDEF T1 WHERE T1.IPRJID=? ";

            pres = con.prepareStatement(sql);
            pres.setLong(1, prjId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelDayStartFlows flow = new ExcelDayStartFlows();
                flow.setFlowName(rs.getString("IFLOWNAME"));
                flow.setFlowDesc(rs.getString("IFLOWDESC"));
                flow.setFlowid(rs.getLong("IID"));
                if (!(flow.getFlowName().indexOf("封装流程") > -1))
                {
                    flow.setMainFlow(false);
                } else
                {
                    flow.setMainFlow(true);
                }

                if (flow.isMainFlow())
                {
                    flow.setActList(ExcelActUtil.getInstance().getMainDayStartExcelActForGetActList(flow.getFlowid(),
                            con)); // 活动集合
                } else
                {
                    flow.setActList(ExcelActUtil.getInstance().getDayStartExcelActForGetActList(flow.getFlowid(), con));
                }
                flow.setiBatchType(ExcelActUtil.getInstance().getDayStartExcel(prjName, flow.getFlowName(), con));
                flow.setFlowParamList(ExcelActUtil.getInstance().getDayStartOneParam()); // 参数集合
                list.add(flow);
            }

        } catch (SQLException e)
        {
            throw new SQLException();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelFlows", _log);
        }
        return list;
    }

    /**
     *
     * @Title: getExcelFlowsOfOne
     * @Description: TODO(只获取一个工作流的ExcelFlows对象)
     * @param prjId
     * @param flowid
     * @param con
     * @return
     * @return List<ExcelFlows> 返回类型
     * @throws @变更记录 2015-9-29 yunpeng_zhang
     */
    public List<ExcelFlows> getExcelFlowsOfOne ( long prjId, String proType, String iflowname, Connection con )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        List<ExcelFlows> list = new ArrayList<ExcelFlows>();
        try
        {
            String sql = "SELECT   IID, IFLOWNAME FROM IEAI_FLOWDEF T WHERE T.IPRJID=? and T.IFLOWNAME=?";

            pres = con.prepareStatement(sql);
            pres.setLong(1, prjId);
            pres.setString(2, iflowname);
            rs = pres.executeQuery();
            while (rs.next())
            {
                ExcelFlows flow = new ExcelFlows();
                flow.setFlowName(rs.getString("IFLOWNAME"));
                flow.setFlowid(rs.getLong("IID"));
                if (null == proType || "".equals(proType))
                {
                    flow.setMainFlow(false);
                } else
                {
                    flow.setMainFlow(true);
                }
                flow.setActList(ExcelActUtil.getInstance().getExcelActForGetActList(flow.getFlowid(), con)); // 活动集合
                // 快照 加载不加载参数
                // flow.setFlowParamList(ExcelActUtil.getInstance().getExcelFlowParam(
                // flow.getFlowid(), con)); // 参数集合
                flow.setFlowParamList(ExcelActUtil.getInstance().getOneParam()); // 参数集合
                list.add(flow);
            }

        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getExcelFlows", _log);
        }
        return list;
    }

    /**
     * <li>Description:模板1 开活动活---shellcmd/aptShell 结束活动</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param bean
     * @return return List<ExcelAct>
     */
    private static List<ExcelAct> getFlowOne ( ActInfoBeanExcel bean )
    {
        List<ExcelAct> list = new ArrayList<ExcelAct>();

        ExcelAct startAct = new ExcelAct();
        startAct.setActId(0);
        startAct.setActName(Constants.EXCEL_ACT_NAME_START);
        startAct.setActType(Constants.EXCEL_ACT_TYPE_START);// "开始活动"
        startAct.setActsuccid(1);
        // gg增加延迟器处理
        boolean runPart = true;
        String isDelay = bean.getDelayTime();
        if (null != isDelay && !"".equals(isDelay) && !"null".equals(isDelay))
        {
            runPart = false;
            if (null == bean.getAptGroupName() || "".equals(bean.getAptGroupName()))
            {
                ExcelAct delay_act = new ExcelAct();
                delay_act.setActDesc(bean.getDescribe());
                delay_act.setFlowname(bean.getActName());
                delay_act.setProjectName(bean.getChildProjectName());
                delay_act.setActId(1);
                delay_act.setActName("延时" + bean.getActName() + "_" + isDelay); // 延时器 活动名称 暂时写死
                delay_act.setActType(Constants.EXCEL_ACT_TYPE_DELAYER);
                delay_act.setActsuccid(2);
                ExcelAct act = new ExcelAct();
                act.setProjectName(bean.getChildProjectName());
                act.setFlowname(bean.getActName());
                act.setActId(2);
                act.setActDesc(bean.getDescribe());
                act.setActName(bean.getActName());
                act.setActType(Constants.EXCEL_ACT_TYPE_SHELLCMD); // SHELLCMD
                act.setActsuccid(3);
                ExcelAct endAct = new ExcelAct();
                endAct.setActId(3);
                endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"

                list.add(startAct);
                list.add(delay_act);
                list.add(act);
                list.add(endAct);
            } else
            {
                runPart = true;
            }
        }
        if (runPart)
        {
            ExcelAct act = new ExcelAct();
            act.setProjectName(bean.getChildProjectName());
            act.setFlowname(bean.getActName());
            act.setActId(1);
            act.setActDesc(bean.getDescribe());
            act.setActName(bean.getActName());
            if (null == bean.getAptGroupName() || "".equals(bean.getAptGroupName()))
            {
                act.setActType(Constants.EXCEL_ACT_TYPE_SHELLCMD); // SHELLCMD
            } else
            {
                act.setActType(Constants.EXCEL_ACT_TYPE_APTSHELL);// APTSHELL
            }
            act.setActsuccid(2);

            ExcelAct endAct = new ExcelAct();
            endAct.setActId(2);
            endAct.setActName(Constants.EXCEL_ACT_NAME_END);
            endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"

            list.add(startAct);
            list.add(act);
            list.add(endAct);
        }
        return list;
    }

    /**
     * <li>Description:模板1 开活动活---shellcmd/aptShell 结束活动</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param bean
     * @return return List<ExcelAct>
     */
    private static List<ExcelDayStartAct> getDayStartFlowOne ( ActInfoDayStartBeanExcel bean )
    {
        List<ExcelDayStartAct> list = new ArrayList<ExcelDayStartAct>();

        ExcelDayStartAct startAct = new ExcelDayStartAct();
        startAct.setActId(0);
        startAct.setActName(Constants.EXCEL_ACT_NAME_START);
        startAct.setActType(Constants.EXCEL_ACT_TYPE_START);// "开始活动"
        startAct.setActsuccid(1);

        list.add(startAct);

        ExcelDayStartAct loopstartAct = new ExcelDayStartAct();
        loopstartAct.setActId(1);
        loopstartAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSTART);
        loopstartAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPSTART);// "循环开始"
        loopstartAct.setActsuccid(2);

        list.add(loopstartAct);

        ExcelDayStartAct delay_act = new ExcelDayStartAct();
        delay_act.setActDesc(bean.getIoffset());
        delay_act.setActId(2);
        delay_act.setActName("定时执行"); // 延时器 活动名称 暂时写死
        delay_act.setActType(Constants.EXCEL_ACT_TYPE_DELAYER);
        delay_act.setActsuccid(3); // 触发文件检测
        delay_act.setProjectName(bean.getIproName());
        delay_act.setFlowname(bean.getIflowName());
        list.add(delay_act);

        if ("".equals(bean.getIcallpreFlow()) || null == bean.getIcallpreFlow())
        {

            if ("".equals(bean.getIcallMainFlow()) || null == bean.getIcallMainFlow())
            {

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(3);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(4);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct getIcallUpdateFlow = new ExcelDayStartAct();
                        getIcallUpdateFlow.setProjectName(bean.getIproName());
                        getIcallUpdateFlow.setFlowname(bean.getIflowName());
                        getIcallUpdateFlow.setActId(3);
                        getIcallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        getIcallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(getIcallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(4);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(5);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }

                } else
                {
                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(3);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(4);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(5);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(4);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }
                }

            } else
            {
                ExcelDayStartAct excelCallMainFlow = new ExcelDayStartAct();
                excelCallMainFlow.setProjectName(bean.getIproName());
                excelCallMainFlow.setFlowname(bean.getIflowName());
                excelCallMainFlow.setActId(3);
                excelCallMainFlow.setActName(bean.getIcallMainFlow());
                excelCallMainFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                list.add(excelCallMainFlow);

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(4);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(5);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(4);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }

                } else
                {

                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(4);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(5);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }
                }
            }
        } else
        {
            // 直接依赖主线
            ExcelDayStartAct excelCallPreFlow = new ExcelDayStartAct();
            excelCallPreFlow.setProjectName(bean.getIproName());
            excelCallPreFlow.setFlowname(bean.getIflowName());
            excelCallPreFlow.setActId(3);
            excelCallPreFlow.setActName(bean.getIcallpreFlow());
            excelCallPreFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
            list.add(excelCallPreFlow);

            if ("".equals(bean.getIcallMainFlow()) || null == bean.getIcallMainFlow())
            {

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(4);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(5);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct getIcallUpdateFlow = new ExcelDayStartAct();
                        getIcallUpdateFlow.setProjectName(bean.getIproName());
                        getIcallUpdateFlow.setFlowname(bean.getIflowName());
                        getIcallUpdateFlow.setActId(4);
                        getIcallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        getIcallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(getIcallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }

                } else
                {
                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(4);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(5);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }
                }

            } else
            {
                ExcelDayStartAct excelCallMainFlow = new ExcelDayStartAct();
                excelCallMainFlow.setProjectName(bean.getIproName());
                excelCallMainFlow.setFlowname(bean.getIflowName());
                excelCallMainFlow.setActId(4);
                excelCallMainFlow.setActName(bean.getIcallMainFlow());
                excelCallMainFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                list.add(excelCallMainFlow);

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(5);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }

                } else
                {

                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(5);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(6);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(7);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(8);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }
                }
            }
        }
        return list;
    }

    /**
     * <li>Description:模板2 开活动活---文件检测----延时器 shellcmd/aptShell 结束活动</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param bean
     * @return return List<ExcelAct>
     */
    private static List<ExcelAct> getFlowTwo ( ActInfoBeanExcel bean )
    {
        List<ExcelAct> list = new ArrayList<ExcelAct>();

        ExcelAct startAct = new ExcelAct();
        startAct.setActId(0);
        startAct.setActName(Constants.EXCEL_ACT_NAME_START);
        startAct.setActType(Constants.EXCEL_ACT_TYPE_START);// "开始活动"
        startAct.setActsuccid(1);

        ExcelAct check_act = new ExcelAct();
        check_act.setProjectName(bean.getChildProjectName());
        check_act.setFlowname(bean.getActName());
        check_act.setActId(1);
        check_act.setActName(bean.getActName() + Constants.EXCEL_ACT_NAME_FILECHECK);
        check_act.setActType(Constants.EXCEL_ACT_TYPE_SHELLCMD); // 文件检查活动
        check_act.setActsuccid(2);
        check_act.setActDesc(bean.getDescribe());
        check_act.setSuccConditions("nf"); // 触发延时器 触发条件

        // ExcelAct check_act1 = new ExcelAct();
        // check_act1.setProjectName(bean.getChildProjectName());
        // check_act1.setActId(1);
        // check_act1.setActName(bean.getActName() + Constants.EXCEL_ACT_NAME_FILECHECK);
        // check_act1.setActType(Constants.EXCEL_ACT_TYPE_SHELLCMD);// 文件检查活动
        // check_act1.setActsuccid(3); //
        // check_act1.setSuccConditions("0"); // 触发 业务脚本

        ExcelAct delay_act = new ExcelAct();
        delay_act.setActDesc(bean.getDescribe());
        delay_act.setFlowname(bean.getActName());
        delay_act.setProjectName(bean.getChildProjectName());
        delay_act.setActId(2);
        delay_act.setActName("延时" + bean.getOKFileFindWeek() + "秒"); // 延时器 活动名称 暂时写死
        delay_act.setActType(Constants.EXCEL_ACT_TYPE_DELAYER);
        delay_act.setActsuccid(1); // 触发文件检测

        ExcelAct act = new ExcelAct();
        act.setActDesc(bean.getDescribe());
        act.setFlowname(bean.getActName());
        act.setProjectName(bean.getChildProjectName());
        act.setActId(3);
        act.setActName(bean.getActName());
        if (null == bean.getAptGroupName() || "".equals(bean.getAptGroupName()))
        {
            act.setActType(Constants.EXCEL_ACT_TYPE_SHELLCMD); // SHELLCMD
        } else
        {
            act.setActType(Constants.EXCEL_ACT_TYPE_APTSHELL);// APTSHELL
        }
        act.setActsuccid(4);

        ExcelAct endAct = new ExcelAct();
        endAct.setActId(4);
        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
        endAct.setActsuccid(0);

        list.add(startAct);
        list.add(check_act);
        // list.add(check_act1);
        list.add(delay_act);
        list.add(act);
        list.add(endAct);
        return list;
    }

    /**
     * <li>Description:模板2 开活动活---文件检测----延时器 shellcmd/aptShell 结束活动</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param bean
     * @return return List<ExcelAct>
     */
    private static List<ExcelDayStartAct> getDayStartFlowTwo ( ActInfoDayStartBeanExcel bean )
    {
        List<ExcelDayStartAct> list = new ArrayList<ExcelDayStartAct>();

        ExcelDayStartAct startAct = new ExcelDayStartAct();
        startAct.setActId(0);
        startAct.setActName(Constants.EXCEL_ACT_NAME_START);
        startAct.setActType(Constants.EXCEL_ACT_TYPE_START);// "开始活动"
        startAct.setActsuccid(1);

        list.add(startAct);

        ExcelDayStartAct loopstartAct = new ExcelDayStartAct();
        loopstartAct.setActId(1);
        loopstartAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSTART);
        loopstartAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPSTART);// "循环开始"
        loopstartAct.setActsuccid(2);

        list.add(loopstartAct);

        ExcelDayStartAct readFileDate = new ExcelDayStartAct();
        readFileDate.setActDesc(bean.getIoffset());
        readFileDate.setActId(2);
        readFileDate.setActName("读取数据日期"); // 文件读取 活动名称 暂时写死
        readFileDate.setActType(Constants.EXCEL_ACT_TYPE_FILE);
        // readFileDate.setActsuccid(3); // 触发文件检测
        readFileDate.setProjectName(bean.getIproName());
        readFileDate.setFlowname(bean.getIflowName());
        readFileDate.setSuccConditions("");
        list.add(readFileDate);

        ExcelDayStartAct delay_act = new ExcelDayStartAct();
        delay_act.setActDesc(bean.getIoffset());
        delay_act.setActId(3);
        delay_act.setActName("定时执行"); // 延时器 活动名称 暂时写死
        delay_act.setActType(Constants.EXCEL_ACT_TYPE_DELAYER);
        delay_act.setProjectName(bean.getIproName());
        delay_act.setFlowname(bean.getIflowName());
        // delay_act.setActsuccid(4); // 触发文件检测
        list.add(delay_act);

        if ("".equals(bean.getIcallpreFlow()) || null == bean.getIcallpreFlow())
        {

            if ("".equals(bean.getIcallMainFlow()) || null == bean.getIcallMainFlow())
            {

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(4);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(5);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct getIcallUpdateFlow = new ExcelDayStartAct();
                        getIcallUpdateFlow.setProjectName(bean.getIproName());
                        getIcallUpdateFlow.setFlowname(bean.getIflowName());
                        getIcallUpdateFlow.setActId(4);
                        getIcallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        getIcallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(getIcallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }

                } else
                {
                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(4);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(5);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }
                }

            } else
            {
                ExcelDayStartAct excelCallMainFlow = new ExcelDayStartAct();
                excelCallMainFlow.setProjectName(bean.getIproName());
                excelCallMainFlow.setFlowname(bean.getIflowName());
                excelCallMainFlow.setActId(4);
                if ("无".equals(bean.getPreFlow()))
                {
                    excelCallMainFlow.setActName(bean.getIcallMainFlow());
                } else
                {
                    excelCallMainFlow.setActName(bean.getPreFlow());
                }
                excelCallMainFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                list.add(excelCallMainFlow);

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(5);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }

                } else
                {

                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(5);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);
                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(6);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(7);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(8);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }
                }
            }
        } else
        {
            // 直接依赖主线
            ExcelDayStartAct excelCallPreFlow = new ExcelDayStartAct();
            excelCallPreFlow.setProjectName(bean.getIproName());
            excelCallPreFlow.setFlowname(bean.getIflowName());
            excelCallPreFlow.setActId(4);
            excelCallPreFlow.setActName(bean.getIcallpreFlow());
            excelCallPreFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
            list.add(excelCallPreFlow);

            if ("".equals(bean.getIcallMainFlow()) || null == bean.getIcallMainFlow())
            {

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(5);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(6);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct getIcallUpdateFlow = new ExcelDayStartAct();
                        getIcallUpdateFlow.setProjectName(bean.getIproName());
                        getIcallUpdateFlow.setFlowname(bean.getIflowName());
                        getIcallUpdateFlow.setActId(5);
                        getIcallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        getIcallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(getIcallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }

                } else
                {
                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(5);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(6);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(7);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(8);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }
                }

            } else
            {
                ExcelDayStartAct excelCallMainFlow = new ExcelDayStartAct();
                excelCallMainFlow.setProjectName(bean.getIproName());
                excelCallMainFlow.setFlowname(bean.getIflowName());
                excelCallMainFlow.setActId(5);
                if ("无".equals(bean.getPreFlow()))
                {
                    excelCallMainFlow.setActName(bean.getIcallMainFlow());
                } else
                {
                    excelCallMainFlow.setActName(bean.getPreFlow());
                }
                excelCallMainFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                list.add(excelCallMainFlow);

                if ("".equals(bean.getIcallSuccFlow()) || null == bean.getIcallSuccFlow())
                {

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {
                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(6);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(7);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(6);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(7);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(8);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    }

                } else
                {

                    ExcelDayStartAct excelCallSuccFlow = new ExcelDayStartAct();
                    excelCallSuccFlow.setProjectName(bean.getIproName());
                    excelCallSuccFlow.setFlowname(bean.getIflowName());
                    excelCallSuccFlow.setActId(6);
                    excelCallSuccFlow.setActName(bean.getIcallSuccFlow());
                    excelCallSuccFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                    list.add(excelCallSuccFlow);

                    if ("".equals(bean.getIcallUpdateFlow()) || null == bean.getIcallUpdateFlow())
                    {

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(7);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(8);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);
                    } else
                    {
                        ExcelDayStartAct excelCallUpdateFlow = new ExcelDayStartAct();
                        excelCallUpdateFlow.setProjectName(bean.getIproName());
                        excelCallUpdateFlow.setFlowname(bean.getIflowName());
                        excelCallUpdateFlow.setActId(7);
                        excelCallUpdateFlow.setActName(bean.getIcallUpdateFlow());
                        excelCallUpdateFlow.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        list.add(excelCallUpdateFlow);

                        ExcelDayStartAct loopEndAct = new ExcelDayStartAct();
                        loopEndAct.setActId(8);
                        loopEndAct.setActName(Constants.EXCEL_ACT_NAME_LOOPSEND);
                        loopEndAct.setActType(Constants.EXCEL_ACT_TYPE_LOOPEND);// "结束活动"
                        // loopEndAct.setActsuccid(6);
                        list.add(loopEndAct);

                        ExcelDayStartAct endAct = new ExcelDayStartAct();
                        endAct.setActId(9);
                        endAct.setActName(Constants.EXCEL_ACT_NAME_END);
                        endAct.setActType(Constants.EXCEL_ACT_TYPE_END);// "结束活动"
                        list.add(endAct);

                    }
                }
            }
        }

        return list;
    }

    /**
     * <li>Description:上传Excel活动信息组织</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param object
     * @param user
     * @param conn return boolean update 2015-08-18 解决上传Excel时 解析工作流重复问题 update 2015-08-21
     * zhangfengchun 修改方法返回类型 update 2015-09-17 zhangfengchun 修改上传Excel时获取服务器组信息
     * @throws RepositoryException
     */
    public boolean saveExcel ( Object[] object, UserInfo user, Connection conn ) throws RepositoryException
    {
        boolean flag = true;
        long startime = System.currentTimeMillis();
        _log.info("开始组织工程信息入库");
        long igroupid = 0;
        try
        {
            /* 查询当前数据库中的分组信息，如果有多个组返回 0.否则返回一个组的ID */
            igroupid = getGroupId(conn);
        } catch (ServerException e2)
        {
            igroupid = 0;
        }
        if (object.length == 2)
        {
            List mainList = (List) object[0];
            List childList = (List) object[1];

            List<ExcelProject> mainExcelList = new ArrayList<ExcelProject>(); // 主工程封装

            for (Object prjName : mainList)
            {
                if (igroupid == 0)
                {
                    igroupid = EngineRepository.getInstance().getGroupId(String.valueOf(prjName), conn);
                }

                List<ExcelFlows> mainProjectList = new ArrayList<ExcelFlows>(); // 存储主工程下的主工作流
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找主工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfo(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }

                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(String.valueOf(dbMianProFLowlist.get(i)));
                    // 查询工作流下的活动
                    List actList = null;
                    try
                    {
                        actList = EngineRepositotyJdbc.getInstance().getActInfo(projectName, flows.getFlowName(), conn);
                    } catch (RepositoryException e)
                    {
                        _log.error("获取主工作流下的活动 异常. 工程名为:" + projectName + " and mes:" + e.getMessage());
                    }
                    // 活动集合转换
                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    for (int a = 0; a < actList.size(); a++)
                    {
                        ActInfoBeanExcel actBean = (ActInfoBeanExcel) actList.get(a);
                        ExcelAct excelact = new ExcelAct();
                        excelact.setActName(actBean.getActName());
                        excelact.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        excelact.setActId(actBean.getActID());
                        _excelactlist.add(excelact);

                    }
                    flows.setActList(_excelactlist);

                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    mainProjectList.add(flows);
                }
                project.setFlows(mainProjectList);

                mainExcelList.add(project);

            }

            // 遍历子系统

            for (Object prjName : childList)
            {
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找子工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfoChild(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }
                List<ExcelFlows> sonProjectList = new ArrayList<ExcelFlows>(); // 存储子工程下的主工作流
                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ActInfoBeanExcel bean = (ActInfoBeanExcel) dbMianProFLowlist.get(i);
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(bean.getActName());
                    flows.setFlowDesc(bean.getDescribe());
                    flows.setSystem(bean.getSystem());

                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    if (bean.getFlag() == 2)
                    {
                        // 模板1 apt或shellcmd
                        _excelactlist = getFlowOne(bean);

                    } else
                    {
                        // 模板带文件检测 apt或shellcmd
                        _excelactlist = getFlowTwo(bean);
                    }
                    flows.setActList(_excelactlist);
                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    sonProjectList.add(flows);
                }
                project.setFlows(sonProjectList);

                mainExcelList.add(project);
            }

            // TODO 测试数据准备是否正确 输出到控制台 确认。 提交代码后进行删除 该段调试代码
            /*
             * for (ExcelProject pro : mainExcelList) { System.out.println(pro.getPrjName());
             * List<ExcelFlows> listf = pro.getFlows(); for (ExcelFlows flow : listf) {
             * System.out.println( pro.getPrjName()+"=="+"\t" + flow.getFlowName());
             * List<ExcelFlowParams> listp = flow.getFlowParamList(); for (ExcelFlowParams act :
             * listp) { System.out.println("参数" + "\t\t" + act.getParamName() + "===" +
             * act.getParamDesc() + "==" + act.getParamType()); } List<ExcelAct> lista =
             * flow.getActList(); for (ExcelAct act : lista) { System.out.println("活动" + "\t\t\t" +
             * act.getActId() + "===" + act.getActName() + "==" + act.getActType() + "==" +
             * act.getActsuccid()); }
             *
             * } }
             */
            long endTime = System.currentTimeMillis();
            _log.info("组织工程信息耗时：" + (endTime - startime) + "毫秒");
            try
            {
                long updatStateTime = System.currentTimeMillis();
                flag = saveExcelProject(conn, mainExcelList);
                long updateendTime = System.currentTimeMillis();
                _log.info("工程信息入库共耗时：" + (updateendTime - updatStateTime) + "毫秒");
            } catch (RepositoryException e)
            {
                flag = false;
                _log.error("组织工程信息入库过程中出现错误 异常信息为:" + e);
            }
        }
        return flag;
    }

    /**
     * <li>Description:上传Excel活动信息组织</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param object
     * @param user
     * @param conn return boolean update 2015-08-18 解决上传Excel时 解析工作流重复问题 update 2015-08-21
     * zhangfengchun 修改方法返回类型 update 2015-09-17 zhangfengchun 修改上传Excel时获取服务器组信息
     * @throws RepositoryException
     */
    public boolean saveExcelNewMainLine ( Object[] object, UserInfo user, String projName, Connection conn,
                                          int basicType ) throws RepositoryException
    {
        boolean flag = true;
        long startime = System.currentTimeMillis();
        _log.info("开始组织工程信息入库");
        long igroupid = 0;
        try
        {
            /* 查询当前数据库中的分组信息，如果有多个组返回 0.否则返回一个组的ID */
            igroupid = getGroupId(conn);
        } catch (ServerException e2)
        {
            igroupid = 0;
        }
        if (object.length == 2)
        {
            List mainList = (List) object[0];
            List childList = (List) object[1];

            List<ExcelProject> mainExcelList = new ArrayList<ExcelProject>(); // 主工程封装

            for (Object prjName : mainList)
            {
                if (igroupid == 0)
                {
                    igroupid = EngineRepository.getInstance().getGroupId(String.valueOf(prjName), conn);
                }

                List<ExcelFlows> mainProjectList = new ArrayList<ExcelFlows>(); // 存储主工程下的主工作流
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找主工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfoCopy(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }

                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(String.valueOf(dbMianProFLowlist.get(i)));
                    // 查询工作流下的活动
                    List actList = null;
                    try
                    {
                        actList = EngineRepositotyJdbc.getInstance().getActInfoCopy(projectName, flows.getFlowName(),
                                conn);
                    } catch (RepositoryException e)
                    {
                        _log.error("获取主工作流下的活动 异常. 工程名为:" + projectName + " and mes:" + e.getMessage());
                    }
                    // 活动集合转换
                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    for (int a = 0; a < actList.size(); a++)
                    {
                        ActInfoBeanExcel actBean = (ActInfoBeanExcel) actList.get(a);
                        ExcelAct excelact = new ExcelAct();
                        excelact.setActName(actBean.getActName());
                        excelact.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        excelact.setActId(actBean.getActID());
                        _excelactlist.add(excelact);

                    }
                    flows.setActList(_excelactlist);

                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    mainProjectList.add(flows);
                }
                project.setFlows(mainProjectList);

                mainExcelList.add(project);

            }

            // 遍历子系统

            for (Object prjName : childList)
            {
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找子工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfoChildCopy(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }
                List<ExcelFlows> sonProjectList = new ArrayList<ExcelFlows>(); // 存储子工程下的主工作流
                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ActInfoBeanExcel bean = (ActInfoBeanExcel) dbMianProFLowlist.get(i);
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(bean.getActName());
                    flows.setFlowDesc(bean.getDescribe());
                    flows.setSystem(bean.getSystem());

                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    if (bean.getFlag() == 2)
                    {
                        // 模板1 apt或shellcmd
                        _excelactlist = getFlowOne(bean);

                    } else
                    {
                        // 模板带文件检测 apt或shellcmd
                        _excelactlist = getFlowTwo(bean);
                    }
                    flows.setActList(_excelactlist);
                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    sonProjectList.add(flows);
                }
                project.setFlows(sonProjectList);

                mainExcelList.add(project);
            }

            // TODO 测试数据准备是否正确 输出到控制台 确认。 提交代码后进行删除 该段调试代码
            /*
             * for (ExcelProject pro : mainExcelList) { System.out.println(pro.getPrjName());
             * List<ExcelFlows> listf = pro.getFlows(); for (ExcelFlows flow : listf) {
             * System.out.println( pro.getPrjName()+"=="+"\t" + flow.getFlowName());
             * List<ExcelFlowParams> listp = flow.getFlowParamList(); for (ExcelFlowParams act :
             * listp) { System.out.println("参数" + "\t\t" + act.getParamName() + "===" +
             * act.getParamDesc() + "==" + act.getParamType()); } List<ExcelAct> lista =
             * flow.getActList(); for (ExcelAct act : lista) { System.out.println("活动" + "\t\t\t" +
             * act.getActId() + "===" + act.getActName() + "==" + act.getActType() + "==" +
             * act.getActsuccid()); }
             *
             * } }
             */
            long endTime = System.currentTimeMillis();
            _log.info("组织工程信息耗时：" + (endTime - startime) + "毫秒");
            try
            {
                long updatStateTime = System.currentTimeMillis();
                flag = saveExcelProjectCopy(conn, mainExcelList, projName, basicType);
                long updateendTime = System.currentTimeMillis();
                _log.info("工程信息入库共耗时：" + (updateendTime - updatStateTime) + "毫秒");
            } catch (RepositoryException e)
            {
                flag = false;
                _log.error("组织工程信息入库过程中出现错误 异常信息为:" + e);
            }
        }
        return flag;
    }

    /**
     * <li>Description:上传Excel活动信息组织</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param object
     * @param user
     * @param conn return boolean update 2015-08-18 解决上传Excel时 解析工作流重复问题 update 2015-08-21
     * zhangfengchun 修改方法返回类型 update 2015-09-17 zhangfengchun 修改上传Excel时获取服务器组信息
     * @throws RepositoryException
     */
    public boolean saveStartflowEndFlowUploadExcel ( Object[] object, UserInfo user, Connection conn )
            throws RepositoryException
    {
        boolean flag = true;
        long startime = System.currentTimeMillis();
        _log.info("开始组织工程信息入库");
        long igroupid = 0;
        try
        {
            /* 查询当前数据库中的分组信息，如果有多个组返回 0.否则返回一个组的ID */
            igroupid = getGroupId(conn);
        } catch (ServerException e2)
        {
            igroupid = 0;
        }
        if (object.length == 2)
        {
            List mainList = (List) object[0];
            List childList = (List) object[1];

            List<ExcelProject> mainExcelList = new ArrayList<ExcelProject>(); // 主工程封装

            for (Object prjName : mainList)
            {
                if (igroupid == 0)
                {
                    igroupid = EngineRepository.getInstance().getGroupId(String.valueOf(prjName), conn);
                }

                List<ExcelFlows> mainProjectList = new ArrayList<ExcelFlows>(); // 存储主工程下的主工作流
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找主工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfo(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }

                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(String.valueOf(dbMianProFLowlist.get(i)));
                    // 查询工作流下的活动
                    List actList = null;
                    try
                    {
                        actList = EngineRepositotyJdbc.getInstance().getActInfo(projectName, flows.getFlowName(), conn);
                    } catch (RepositoryException e)
                    {
                        _log.error("获取主工作流下的活动 异常. 工程名为:" + projectName + " and mes:" + e.getMessage());
                    }
                    // 活动集合转换
                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    for (int a = 0; a < actList.size(); a++)
                    {
                        ActInfoBeanExcel actBean = (ActInfoBeanExcel) actList.get(a);
                        ExcelAct excelact = new ExcelAct();
                        excelact.setActName(actBean.getActName());
                        excelact.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        excelact.setActId(actBean.getActID());
                        _excelactlist.add(excelact);

                    }
                    flows.setActList(_excelactlist);

                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    mainProjectList.add(flows);
                }
                project.setFlows(mainProjectList);

                mainExcelList.add(project);

            }

            // 遍历子系统

            for (Object prjName : childList)
            {
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找子工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfoChild(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }
                List<ExcelFlows> sonProjectList = new ArrayList<ExcelFlows>(); // 存储子工程下的主工作流
                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ActInfoBeanExcel bean = (ActInfoBeanExcel) dbMianProFLowlist.get(i);
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(bean.getActName());
                    flows.setFlowDesc(bean.getDescribe());
                    flows.setSystem(bean.getSystem());

                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    if (bean.getFlag() == 2)
                    {
                        // 模板1 apt或shellcmd
                        _excelactlist = getFlowOne(bean);

                    } else
                    {
                        // 模板带文件检测 apt或shellcmd
                        _excelactlist = getFlowTwo(bean);
                    }
                    flows.setActList(_excelactlist);
                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    sonProjectList.add(flows);
                }
                project.setFlows(sonProjectList);

                mainExcelList.add(project);
            }

            // TODO 测试数据准备是否正确 输出到控制台 确认。 提交代码后进行删除 该段调试代码
            /*
             * for (ExcelProject pro : mainExcelList) { System.out.println(pro.getPrjName());
             * List<ExcelFlows> listf = pro.getFlows(); for (ExcelFlows flow : listf) {
             * System.out.println( pro.getPrjName()+"=="+"\t" + flow.getFlowName());
             * List<ExcelFlowParams> listp = flow.getFlowParamList(); for (ExcelFlowParams act :
             * listp) { System.out.println("参数" + "\t\t" + act.getParamName() + "===" +
             * act.getParamDesc() + "==" + act.getParamType()); } List<ExcelAct> lista =
             * flow.getActList(); for (ExcelAct act : lista) { System.out.println("活动" + "\t\t\t" +
             * act.getActId() + "===" + act.getActName() + "==" + act.getActType() + "==" +
             * act.getActsuccid()); }
             *
             * } }
             */
            long endTime = System.currentTimeMillis();
            _log.info("组织工程信息耗时：" + (endTime - startime) + "毫秒");
            try
            {
                long updatStateTime = System.currentTimeMillis();
                flag = saveStartFlowEndFlowExcelProject(conn, mainExcelList);
                long updateendTime = System.currentTimeMillis();
                _log.info("工程信息入库共耗时：" + (updateendTime - updatStateTime) + "毫秒");
            } catch (RepositoryException e)
            {
                flag = false;
                _log.error("组织工程信息入库过程中出现错误 异常信息为:" + e);
            }
        }
        return flag;
    }

    /**
     * <li>Description:上传Excel活动信息组织</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param object
     * @param user
     * @param conn return boolean update 2015-08-18 解决上传Excel时 解析工作流重复问题 update 2015-08-21
     * zhangfengchun 修改方法返回类型 update 2015-09-17 zhangfengchun 修改上传Excel时获取服务器组信息
     * @throws RepositoryException
     * @throws ServerException
     */
    public boolean saveDayStartExcel ( List childProjectName, UserInfo user, ProjectSaveUtilBean projectSaveUtilBean,
                                       int basicType ) throws RepositoryException, ServerException
    {
        boolean flag = true;
        long startime = System.currentTimeMillis();
        _log.info("开始组织日启动工程信息入库");
        long igroupid = 0;
        try
        {
            /* 查询当前数据库中的分组信息，如果有多个组返回 0.否则返回一个组的ID */
            igroupid = getGroupId(projectSaveUtilBean.getBasicConnection());
        } catch (ServerException e2)
        {
            igroupid = 0;
        }
        if (!"".equals(childProjectName))
        {
            List childList = childProjectName;

            List<ExcelDayStartProject> mainExcelList = new ArrayList<ExcelDayStartProject>(); // 主工程封装

            // 遍历子系统

            for (Object prjName : childList)
            {
                if (igroupid == 0)
                {
                    igroupid = EngineRepository.getInstance().getGroupId(String.valueOf(prjName),
                            projectSaveUtilBean.getBasicConnection());
                }
                ExcelDayStartProject project = new ExcelDayStartProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找子工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getDayStartWorkflowInfoChild(projectName,
                            projectSaveUtilBean.getBasicConnection());
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }
                List<ExcelDayStartFlows> sonProjectList = new ArrayList<ExcelDayStartFlows>(); // 存储子工程下的主工作流
                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ActInfoDayStartBeanExcel bean = (ActInfoDayStartBeanExcel) dbMianProFLowlist.get(i);
                    ExcelDayStartFlows flows = new ExcelDayStartFlows();
                    flows.setFlowName(bean.getIflowName());
                    flows.setFlowDesc(bean.getIoffset());

                    List<ExcelDayStartAct> _excelactlist = new ArrayList<ExcelDayStartAct>();
                    if (bean.getiBatchType() == 2)
                    {
                        // 日启动模板1 循环结构 延时器
                        _excelactlist = getDayStartFlowOne(bean);

                    } else
                    {
                        // 日启动模板构建 循环结构 延时器 文件接口
                        _excelactlist = getDayStartFlowTwo(bean);
                    }
                    flows.setSystem(bean.getSystem());
                    flows.setActList(_excelactlist);
                    List<ExcelDayStartFlowParams> _flowParamList = new ArrayList<ExcelDayStartFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), projectSaveUtilBean.getBasicConnection());
                    for (Object param : _paramList)
                    {
                        ExcelDayStartFlowParams parmas = new ExcelDayStartFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    sonProjectList.add(flows);
                }

                List<ActInfoDayStartBeanExcel> dbPreProFLowlist = null;
                try
                {
                    // 从数据库中查找主工程下的工作流
                    dbPreProFLowlist = EngineRepositotyJdbc.getInstance().getDayStartMainPositionWorkflowInfo(
                            (String) prjName, projectSaveUtilBean.getBasicConnection());
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }

                for (int i = 0; i < dbPreProFLowlist.size(); i++)
                {
                    ActInfoDayStartBeanExcel actInfoBeanExcel = dbPreProFLowlist.get(i);
                    ExcelDayStartFlows flows = new ExcelDayStartFlows();
                    flows.setFlowName(actInfoBeanExcel.getPreFlow());
                    // 查询工作流下的活动
                    List<ActInfoPreProBeanExcel> actList = null;
                    try
                    {
                        actList = EngineRepositotyJdbc.getInstance().getPreProWorkflowInfo(projectName,
                                actInfoBeanExcel.getPreFlow(), actInfoBeanExcel.getIcallproName(),
                                projectSaveUtilBean.getBasicConnection());
                    } catch (RepositoryException e)
                    {
                        _log.error("获取主工作流下的活动 异常. 工程名为:" + projectName + " and mes:" + e.getMessage());
                    }
                    // 活动集合转换
                    List<ExcelDayStartAct> _excelactlist = new ArrayList<ExcelDayStartAct>();
                    for (int a = 0; a < actList.size(); a++)
                    {
                        ActInfoPreProBeanExcel actBean = (ActInfoPreProBeanExcel) actList.get(a);
                        ExcelDayStartAct excelact = new ExcelDayStartAct();
                        excelact.setActName(actBean.getActName());
                        excelact.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        excelact.setActId(actBean.getActId());
                        _excelactlist.add(excelact);

                    }
                    flows.setActList(_excelactlist);
                    flows.setSystem(actInfoBeanExcel.getSystem());

                    List<ExcelDayStartFlowParams> _flowParamList = new ArrayList<ExcelDayStartFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), projectSaveUtilBean.getBasicConnection());
                    for (Object param : _paramList)
                    {
                        ExcelDayStartFlowParams parmas = new ExcelDayStartFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    sonProjectList.add(flows);
                }

                project.setFlows(sonProjectList);

                mainExcelList.add(project);
            }

            long endTime = System.currentTimeMillis();
            _log.info("组织工程信息耗时：" + (endTime - startime) + "毫秒");
            try
            {
                long updatStateTime = System.currentTimeMillis();
                flag = saveDaySartExcelProject(projectSaveUtilBean.getBasicConnection(), projectSaveUtilBean,
                        mainExcelList, basicType);
                long updateendTime = System.currentTimeMillis();
                _log.info("工程信息入库共耗时：" + (updateendTime - updatStateTime) + "毫秒");
            } catch (RepositoryException e)
            {
                flag = false;
                _log.error("组织工程信息入库过程中出现错误 异常信息为:" + e);
                return flag;
            }
        }
        return flag;
    }

    /**
     * <li>Description:组织工程信息到数据库中</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param con
     * @param proList
     * @throws RepositoryException return void return boolean update 2015-08-21 fengchun_zhang
     * 修改方法返回类型 update 2015-09-18 fengchun_zhang 修改批量上传时 记录条数限制
     */
    public boolean saveExcelProject ( Connection con, List<ExcelProject> proList ) throws RepositoryException
    {
        boolean flag = true;

        // 创建保存工具类对象
        ProjectSaveUtilBean projectSaveUtilBean = new ProjectSaveUtilBean();
        // 获取所有可用数据源
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                .getDBsourceList(Constants.IEAI_IEAI_BASIC);
        Connection connection = null;
        int basicType = -1;
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            connection = DBResource.getConnection("updatePrjAdp", _log, (int) dBSourceMonitor.getGroupId());
            if (dBSourceMonitor.getBasic() == 1)
            {
                basicType =(int) dBSourceMonitor.getGroupId();
                // 保存基线库数据库连接至工具类对象
                projectSaveUtilBean.setBasicConnection(connection);
                projectSaveUtilBean.setBasicConnectionName(dBSourceMonitor.getDbsourceName());
            }
        }

        // 工程
        long ieai_project_id = 0;
        PreparedStatement pres = null;
        String sql = "INSERT INTO IEAI_PROJECT(IID, INAME,  IMAJVER,    IMINVER,    IFREEZED,   IUPLOADUSER, IUPLOADNUM, IUUID,  IUPLOADTIME,    IUPLOADUSERID,  IGROUPID) "
                + "      VALUES ( ?, ?, 0, 0, 0, ?, (select decode( MAX(IUPLOADNUM),null,0,MAX(IUPLOADNUM))+1 from ieai_project where INAME=?), ?, SYSDATE, ?, ?)";
        // +" VALUES ( 3162, 'aaabbccdd', 0, 0, 0, 'zfc', 1, 'uuid', SYSDATE, 4, 0 )";

        // 工作流
        long ieai_flowdef_id = 0;
        PreparedStatement pres_flow = null;
        String sql_flow = "INSERT INTO  IEAI_FLOWDEF(IID, IFLOWDESC, IFLOWNAME, IPRJID)  " + "VALUES(?, ?, ?, ?)";

        // 工作流参数
        PreparedStatement pres_param = null;
        String sql_param = "INSERT INTO  IEAI_FLOW_PARAM( PID,IID,IPARAM_NAME,IPARAM_TYPE,IPARAM_DESC,IREAD_ONLY )  "
                + "VALUES(?, ?, ?, ?, ?, ?)";
        long ieai_flowdparam_id = 0;

        // IEAI_WORKFLOWINSTANCE
        PreparedStatement pres_insert_ws = null;
        // PreparedStatement pres_del_ws = null;
        // String sql_delws =
        // "DELETE FROM IEAI_WORKFLOWINSTANCE WHERE IPROJECTNAME =? AND ISTATUS=55";
        String sql_ins_ws = "INSERT INTO  IEAI_WORKFLOWINSTANCE( IFLOWID, IFLOWNAME,  IPROJECTNAME,IFLOWDES, ISTARTTIME, ISTATUS,IENDTIME,IDELETEFLAG ,ISYSTEM)  "
                + "VALUES(?, ?, ?, ?, ?,?,?,?,?)";
        long ieai_workflowinstance_id = 0;

        // 工作流活动
        PreparedStatement pres_act = null;
        String sql_act = "INSERT INTO  IEAI_SON_PROJECT(AID, IID, IACT_ID, IACT_NAME, IACT_SUCCID, IACT_DESC, IACT_TYPE,IACT_CONDITIONS,IPROJECTNAME,IFLOWNAME)  "
                + "VALUES(?, ?, ?, ?, ?, ?,?,?,?,?)";

        long ieai_flowdact_id = 0;
        try
        {
            pres = con.prepareStatement(sql);
            pres_flow = con.prepareStatement(sql_flow);
            // pres_del_ws = con.prepareStatement(sql_delws);
            pres_insert_ws = con.prepareStatement(sql_ins_ws);
            pres_param = con.prepareStatement(sql_param);
            pres_act = con.prepareStatement(sql_act);
            int index = 1;
            // 主流程所属系统名取子流程中的任意一个 add by yunpeng_zhang at 20150929
            String systemName = "";
            for (ExcelProject prj : proList)
            {
                List<ExcelFlows> flowslist = prj.getFlows();
                for (ExcelFlows excelFlows : flowslist)
                {
                    if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                    {
                        systemName = excelFlows.getSystem();
                        break;
                    }
                }
                if (null != systemName && !"".equals(systemName))
                {
                    break;
                }
            }
            for (ExcelProject prj : proList)
            {
                UpLoadExcelManager.getInstance().deleteExcelModel(String.valueOf(prj.getPrjName()), con);
                ieai_project_id = IdGenerator.createIdForAll(RepProject.class, basicType);
                pres.setLong(1, ieai_project_id);
                pres.setString(2, prj.getPrjName());
                pres.setString(3, prj.getUploadUser());
                pres.setString(4, prj.getPrjName());
                pres.setString(5, UUID.uuid());
                pres.setLong(6, prj.getUploadUserId());
                pres.setLong(7, prj.getIgroupid());
                List<ExcelFlows> flowslist = prj.getFlows();

                // 开始删除ieai_workflowInstance
                // pres_del_ws.setString(1, prj.getPrjName());
                // pres_del_ws.executeUpdate();
                // transferWorkFLowInstance(prj.getPrjName(), con);
                // 通过子系统名获取未结束的工作流名
                List<String> notInFlowList = this.getNotInsertWorkFLowInstance(prj.getPrjName(), con);
                int count = 0;
                String sysName = "";
                for (ExcelFlows excelFlows : flowslist)
                {
                    ieai_flowdef_id = IdGenerator.createIdForAll(RepWorkflow.class, basicType);
                    pres_flow.setLong(1, ieai_flowdef_id);
                    pres_flow.setString(2, excelFlows.getFlowDesc());
                    pres_flow.setString(3, excelFlows.getFlowName());
                    pres_flow.setLong(4, ieai_project_id);
                    // 未结束的工作流名不插入新记录
                    if (!notInFlowList.contains(excelFlows.getFlowName()))
                    {
                        if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                        {
                            sysName = excelFlows.getSystem();
                        } else
                        {
                            sysName = systemName;
                        }
                        count = this.getInsertWorkFLowInstance(sysName, excelFlows.getFlowName(), prj.getPrjName(),
                                con);
                        if (count == 0)
                        {
                            // ieai_workflowinstance
                            ieai_workflowinstance_id = IdGenerator.createIdForAll(RepWorkflowInstance.class,
                                    basicType);
                            pres_insert_ws.setLong(1, ieai_workflowinstance_id);
                            pres_insert_ws.setString(2, excelFlows.getFlowName());
                            pres_insert_ws.setString(3, prj.getPrjName());
                            pres_insert_ws.setString(4, excelFlows.getFlowDesc());
                            pres_insert_ws.setLong(5, 0);
                            pres_insert_ws.setLong(6, Constants.STATE_NEW_CREATE);
                            pres_insert_ws.setLong(7, 0);
                            pres_insert_ws.setLong(8, 0);
                            pres_insert_ws.setString(9,
                                    (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                                            ? excelFlows.getSystem() : systemName);
                            pres_insert_ws.addBatch();
                        }
                    }

                    List<ExcelFlowParams> flowParamlist = excelFlows.getFlowParamList();
                    for (ExcelFlowParams param : flowParamlist)
                    {
                        // ieai_flowdparam_id = IdGenerator.createIdFor(ExcelFlowParams.class);
                        try
                        {
                            ieai_flowdparam_id = getSequequSonProject(projectSaveUtilBean.getBasicConnection(),
                                    basicType);
                        } catch (ServerException e)
                        {
                            _log.error(" ieai_flowdparam_id getSequequSonProjec execute is error :" + e);
                        }

                        pres_param.setLong(1, ieai_flowdparam_id);
                        pres_param.setLong(2, ieai_flowdef_id);
                        pres_param.setString(3, param.getParamName());
                        pres_param.setString(4, param.getParamType());
                        pres_param.setString(5, param.getParamDesc());
                        pres_param.setString(6, param.getParamOnly());
                        pres_param.addBatch();
                    }
                    List<ExcelAct> actlist = excelFlows.getActList();

                    for (ExcelAct act : actlist)
                    {
                        // ieai_flowdact_id = IdGenerator.createIdFor(ExcelAct.class);
                        try
                        {
                            ieai_flowdact_id = getSequequSonProject(projectSaveUtilBean.getBasicConnection(),
                                    basicType);
                        } catch (ServerException e)
                        {
                            _log.error("ieai_flowdact_id getSequequSonProjec execute is error :" + e);
                        }

                        pres_act.setLong(1, ieai_flowdact_id);
                        pres_act.setLong(2, ieai_flowdef_id);
                        pres_act.setLong(3, act.getActId());
                        pres_act.setString(4, act.getActName());
                        pres_act.setLong(5, act.getActsuccid());
                        pres_act.setString(6, act.getActDesc());
                        pres_act.setString(7, act.getActType());
                        pres_act.setString(8, act.getSuccConditions());
                        pres_act.setString(9, act.getProjectName());
                        pres_act.setString(10, act.getFlowname());
                        pres_act.addBatch();
                        if (index % 3000 == 0)
                        {
                            pres_act.executeBatch();
                        }
                        index++;
                    }

                    pres_flow.addBatch();
                }
                pres.addBatch();
            }
            pres_insert_ws.executeBatch(); // ieai_workflowinstance 批量入库
            pres.executeBatch(); // 工程信息批量入库
            pres_flow.executeBatch();// 工作流信息批量入库
            pres_param.executeBatch();// 参数信息批量入库
            pres_act.executeBatch();// 活动信息批量入库

        } catch (SQLException e)
        {
            flag = false;
            try
            {
                con.rollback();
                _log.error("批量组织活动信息失败  已回滚. 异常信息为" + e);
            } catch (SQLException ex)
            {
                _log.error("saveExcelProject 批量组织活动信息 异常信息为：" + e);
                throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
            }
        } finally
        {
            DBResource.closePreparedStatement(pres_insert_ws, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres_flow, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres_param, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres_act, "saveExceProject", _log);
            DBResource.closeConnection(projectSaveUtilBean.getBasicConnection(), "saveExceProject", _log);
            DBResource.closeConnection(connection, "saveExceProject", _log);
        }
        return flag;

    }

    /**
     * <li>Description:组织工程信息到数据库中</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param con
     * @param proList
     * @throws RepositoryException return void return boolean update 2015-08-21 fengchun_zhang
     * 修改方法返回类型 update 2015-09-18 fengchun_zhang 修改批量上传时 记录条数限制
     */
    public boolean saveExcelProjectCopy ( Connection con, List<ExcelProject> proList, String proName, int basicType )
            throws RepositoryException
    {
        boolean flag = true;
        // 工程

        // IEAI_WORKFLOWINSTANCE
        PreparedStatement pres_insert_ws = null;
        // PreparedStatement pres_del_ws = null;
        // String sql_delws =
        // "DELETE FROM IEAI_WORKFLOWINSTANCE WHERE IPROJECTNAME =? AND ISTATUS=55";
        String sql_ins_ws = "INSERT INTO  IEAI_WORKFLOWINSTANCE( IFLOWID, IFLOWNAME,  IPROJECTNAME,IFLOWDES, ISTARTTIME, ISTATUS,IENDTIME,IDELETEFLAG ,ISYSTEM)  "
                + "VALUES(?, ?, ?, ?, ?,?,?,?,?)";
        long ieai_workflowinstance_id = 0;

        long ieai_flowdact_id = 0;
        try
        {
            // pres_del_ws = con.prepareStatement(sql_delws);
            pres_insert_ws = con.prepareStatement(sql_ins_ws);
            int index = 1;
            // 主流程所属系统名取子流程中的任意一个 add by yunpeng_zhang at 20150929
            String systemName = "";
            for (ExcelProject prj : proList)
            {
                List<ExcelFlows> flowslist = prj.getFlows();
                for (ExcelFlows excelFlows : flowslist)
                {
                    if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                    {
                        systemName = excelFlows.getSystem();
                        break;
                    }
                }
                if (null != systemName && !"".equals(systemName))
                {
                    break;
                }
            }
            for (ExcelProject prj : proList)
            {
                List<ExcelFlows> flowslist = prj.getFlows();

                // 开始删除ieai_workflowInstance
                // pres_del_ws.setString(1, prj.getPrjName());
                // pres_del_ws.executeUpdate();
                // transferWorkFLowInstance(prj.getPrjName(), con);
                // 通过子系统名获取未结束的工作流名
                List<String> notInFlowList = this.getNotInsertWorkFLowInstance(prj.getPrjName(), con);
                int count = 0;
                String sysName = "";
                for (ExcelFlows excelFlows : flowslist)
                {
                    // 未结束的工作流名不插入新记录
                    if (!notInFlowList.contains(excelFlows.getFlowName()))
                    {
                        if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                        {
                            sysName = excelFlows.getSystem();
                        } else
                        {
                            sysName = systemName;
                        }
                        count = this.getInsertWorkFLowInstance(sysName, excelFlows.getFlowName(), prj.getPrjName(),
                                con);
                        if (count == 0)
                        {
                            // ieai_workflowinstance
                            ieai_workflowinstance_id = IdGenerator.createIdForAll(RepWorkflowInstance.class,
                                    basicType);
                            pres_insert_ws.setLong(1, ieai_workflowinstance_id);
                            pres_insert_ws.setString(2, excelFlows.getFlowName());
                            pres_insert_ws.setString(3, prj.getPrjName());
                            pres_insert_ws.setString(4, excelFlows.getFlowDesc());
                            pres_insert_ws.setLong(5, 0);
                            pres_insert_ws.setLong(6, Constants.STATE_NEW_CREATE);
                            pres_insert_ws.setLong(7, 0);
                            pres_insert_ws.setLong(8, 0);
                            pres_insert_ws.setString(9,
                                    (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                                            ? excelFlows.getSystem() : systemName);
                            pres_insert_ws.addBatch();
                        }
                    }

                }
            }
            pres_insert_ws.executeBatch(); // ieai_workflowinstance 批量入库

        } catch (SQLException e)
        {
            flag = false;
            try
            {
                con.rollback();
                _log.error("批量组织活动信息失败  已回滚. 异常信息为" + e);
            } catch (SQLException ex)
            {
                _log.error("saveExcelProjectCopy 批量组织活动信息 异常信息为：" + e);
                throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
            }
        } finally
        {
            DBResource.closePreparedStatement(pres_insert_ws, "saveExcelProjectCopy", _log);
        }
        return flag;
    }

    /**
     * <li>Description:组织工程信息到数据库中</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param con
     * @param proList
     * @throws RepositoryException return void return boolean update 2015-08-21 fengchun_zhang
     * 修改方法返回类型 update 2015-09-18 fengchun_zhang 修改批量上传时 记录条数限制
     */
    public boolean saveStartFlowEndFlowExcelProject ( Connection con, List<ExcelProject> proList )
            throws RepositoryException
    {
        boolean flag = true;

        // 创建保存工具类对象
        ProjectSaveUtilBean projectSaveUtilBean = new ProjectSaveUtilBean();
        // 获取所有可用数据源
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance()
                .getDBsourceList(Constants.IEAI_IEAI_BASIC);
        Connection connection = null;
        int basicType = -1;
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            connection = DBResource.getConnection("updatePrjAdp", _log, (int) dBSourceMonitor.getGroupId());
            if (dBSourceMonitor.getBasic() == 1)
            {
                basicType = (int) dBSourceMonitor.getGroupId();
                // 保存基线库数据库连接至工具类对象
                projectSaveUtilBean.setBasicConnection(connection);
                projectSaveUtilBean.setBasicConnectionName(dBSourceMonitor.getDbsourceName());
            }
        }

        // 工程
        long ieai_project_id = 0;
        PreparedStatement pres = null;
        String sql = "INSERT INTO IEAI_PROJECT(IID, INAME,  IMAJVER,    IMINVER,    IFREEZED,   IUPLOADUSER, IUPLOADNUM, IUUID,  IUPLOADTIME,    IUPLOADUSERID,  IGROUPID) "
                + "      VALUES ( ?, ?, 0, 0, 0, ?, (select decode( MAX(IUPLOADNUM),null,0,MAX(IUPLOADNUM))+1 from ieai_project where INAME=?), ?, SYSDATE, ?, ?)";
        // +" VALUES ( 3162, 'aaabbccdd', 0, 0, 0, 'zfc', 1, 'uuid', SYSDATE, 4, 0 )";

        // 工作流
        long ieai_flowdef_id = 0;
        PreparedStatement pres_flow = null;
        String sql_flow = "INSERT INTO  IEAI_FLOWDEF(IID, IFLOWDESC, IFLOWNAME, IPRJID)  " + "VALUES(?, ?, ?, ?)";

        // 工作流参数
        PreparedStatement pres_param = null;
        String sql_param = "INSERT INTO  IEAI_FLOW_PARAM( PID,IID,IPARAM_NAME,IPARAM_TYPE,IPARAM_DESC,IREAD_ONLY )  "
                + "VALUES(?, ?, ?, ?, ?, ?)";
        long ieai_flowdparam_id = 0;

        // IEAI_WORKFLOWINSTANCE
        PreparedStatement pres_insert_ws = null;
        // PreparedStatement pres_del_ws = null;
        // String sql_delws =
        // "DELETE FROM IEAI_WORKFLOWINSTANCE WHERE IPROJECTNAME =? AND ISTATUS=55";
        String sql_ins_ws = "INSERT INTO  IEAI_WORKFLOWINSTANCE( IFLOWID, IFLOWNAME,  IPROJECTNAME,IFLOWDES, ISTARTTIME, ISTATUS,IENDTIME,IDELETEFLAG ,ISYSTEM)  "
                + "VALUES(?, ?, ?, ?, ?,?,?,?,?)";
        long ieai_workflowinstance_id = 0;

        // 工作流活动
        PreparedStatement pres_act = null;
        String sql_act = "INSERT INTO  IEAI_SON_PROJECT(AID, IID, IACT_ID, IACT_NAME, IACT_SUCCID, IACT_DESC, IACT_TYPE,IACT_CONDITIONS,IPROJECTNAME,IFLOWNAME)  "
                + "VALUES(?, ?, ?, ?, ?, ?,?,?,?,?)";

        long ieai_flowdact_id = 0;
        try
        {
            pres = con.prepareStatement(sql);
            pres_flow = con.prepareStatement(sql_flow);
            // pres_del_ws = con.prepareStatement(sql_delws);
            pres_insert_ws = con.prepareStatement(sql_ins_ws);
            pres_param = con.prepareStatement(sql_param);
            pres_act = con.prepareStatement(sql_act);
            int index = 1;
            // 主流程所属系统名取子流程中的任意一个 add by yunpeng_zhang at 20150929
            String systemName = "";
            for (ExcelProject prj : proList)
            {
                List<ExcelFlows> flowslist = prj.getFlows();
                for (ExcelFlows excelFlows : flowslist)
                {
                    if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                    {
                        systemName = excelFlows.getSystem();
                        break;
                    }
                }
                if (null != systemName && !"".equals(systemName))
                {
                    break;
                }
            }
            for (ExcelProject prj : proList)
            {
                ieai_project_id = IdGenerator.createIdForAll(RepProject.class, basicType);
                pres.setLong(1, ieai_project_id);
                pres.setString(2, prj.getPrjName());
                pres.setString(3, prj.getUploadUser());
                pres.setString(4, prj.getPrjName());
                pres.setString(5, UUID.uuid());
                pres.setLong(6, prj.getUploadUserId());
                pres.setLong(7, prj.getIgroupid());
                List<ExcelFlows> flowslist = prj.getFlows();

                // 开始删除ieai_workflowInstance
                // pres_del_ws.setString(1, prj.getPrjName());
                // pres_del_ws.executeUpdate();
                // transferWorkFLowInstance(prj.getPrjName(), con);
                // 通过子系统名获取未结束的工作流名
                List<String> notInFlowList = this.getNotInsertWorkFLowInstance(prj.getPrjName(), con);
                int count = 0;
                String sysName = "";
                for (ExcelFlows excelFlows : flowslist)
                {
                    ieai_flowdef_id = IdGenerator.createIdForAll(RepWorkflow.class, basicType);
                    pres_flow.setLong(1, ieai_flowdef_id);
                    pres_flow.setString(2, excelFlows.getFlowDesc());
                    pres_flow.setString(3, excelFlows.getFlowName());
                    pres_flow.setLong(4, ieai_project_id);
                    // 未结束的工作流名不插入新记录
                    if (!notInFlowList.contains(excelFlows.getFlowName()))
                    {
                        if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                        {
                            sysName = excelFlows.getSystem();
                        } else
                        {
                            sysName = systemName;
                        }
                        count = this.getInsertWorkFLowInstance(sysName, excelFlows.getFlowName(), prj.getPrjName(),
                                con);
                        if (count == 0)
                        {
                            // ieai_workflowinstance
                            ieai_workflowinstance_id = IdGenerator.createIdForAll(RepWorkflowInstance.class,
                                    basicType);
                            pres_insert_ws.setLong(1, ieai_workflowinstance_id);
                            pres_insert_ws.setString(2, excelFlows.getFlowName());
                            pres_insert_ws.setString(3, prj.getPrjName());
                            pres_insert_ws.setString(4, excelFlows.getFlowDesc());
                            pres_insert_ws.setLong(5, 0);
                            pres_insert_ws.setLong(6, Constants.STATE_NEW_CREATE);
                            pres_insert_ws.setLong(7, 0);
                            pres_insert_ws.setLong(8, 0);
                            pres_insert_ws.setString(9,
                                    (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                                            ? excelFlows.getSystem() : systemName);
                            pres_insert_ws.addBatch();
                        }
                    }

                    List<ExcelFlowParams> flowParamlist = excelFlows.getFlowParamList();
                    for (ExcelFlowParams param : flowParamlist)
                    {
                        // ieai_flowdparam_id = IdGenerator.createIdFor(ExcelFlowParams.class);
                        try
                        {
                            ieai_flowdparam_id = getSequequSonProject(projectSaveUtilBean.getBasicConnection(),
                                    basicType);
                        } catch (ServerException e)
                        {
                            _log.error(" ieai_flowdparam_id getSequequSonProjec execute is error :" + e);
                        }

                        pres_param.setLong(1, ieai_flowdparam_id);
                        pres_param.setLong(2, ieai_flowdef_id);
                        pres_param.setString(3, param.getParamName());
                        pres_param.setString(4, param.getParamType());
                        pres_param.setString(5, param.getParamDesc());
                        pres_param.setString(6, param.getParamOnly());
                        pres_param.addBatch();
                    }
                    List<ExcelAct> actlist = excelFlows.getActList();

                    for (ExcelAct act : actlist)
                    {
                        // ieai_flowdact_id = IdGenerator.createIdFor(ExcelAct.class);
                        try
                        {
                            ieai_flowdact_id = getSequequSonProject(projectSaveUtilBean.getBasicConnection(),
                                    basicType);
                        } catch (ServerException e)
                        {
                            _log.error("ieai_flowdact_id getSequequSonProjec execute is error :" + e);
                        }

                        pres_act.setLong(1, ieai_flowdact_id);
                        pres_act.setLong(2, ieai_flowdef_id);
                        pres_act.setLong(3, act.getActId());
                        pres_act.setString(4, act.getActName());
                        pres_act.setLong(5, act.getActsuccid());
                        pres_act.setString(6, act.getActDesc());
                        pres_act.setString(7, act.getActType());
                        pres_act.setString(8, act.getSuccConditions());
                        pres_act.setString(9, act.getProjectName());
                        pres_act.setString(10, act.getFlowname());
                        pres_act.addBatch();
                        if (index % 3000 == 0)
                        {
                            pres_act.executeBatch();
                        }
                        index++;
                    }

                    pres_flow.addBatch();
                }
                pres.addBatch();
            }
            pres_insert_ws.executeBatch(); // ieai_workflowinstance 批量入库
            pres.executeBatch(); // 工程信息批量入库
            pres_flow.executeBatch();// 工作流信息批量入库
            pres_param.executeBatch();// 参数信息批量入库
            pres_act.executeBatch();// 活动信息批量入库

        } catch (SQLException e)
        {
            flag = false;
            try
            {
                con.rollback();
                _log.error("批量组织活动信息失败  已回滚. 异常信息为" + e);
            } catch (SQLException ex)
            {
                _log.error("saveExcelProject 批量组织活动信息 异常信息为：" + e);
                throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
            }
        } finally
        {
            DBResource.closePreparedStatement(pres_insert_ws, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres_flow, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres_param, "saveExceProject", _log);
            DBResource.closePreparedStatement(pres_act, "saveExceProject", _log);
            DBResource.closeConnection(projectSaveUtilBean.getBasicConnection(), "saveExceProject", _log);
            DBResource.closeConnection(connection, "saveExceProject", _log);
        }
        return flag;

    }

    /**
     * <li>Description:组织日启动工程信息到数据库中</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param con
     * @param proList
     * @throws RepositoryException return void return boolean update 2015-08-21 fengchun_zhang
     * 修改方法返回类型 update 2015-09-18 fengchun_zhang 修改批量上传时 记录条数限制
     * @throws ServerException
     */
    public boolean saveDaySartExcelProject ( Connection con, ProjectSaveUtilBean projectSaveUtilBean,
                                             List<ExcelDayStartProject> proList, int basicType ) throws RepositoryException, ServerException
    {
        boolean flag = true;

        // 工程
        long ieai_project_id = 0;
        PreparedStatement pres = null;
        String sql = "INSERT INTO IEAI_PROJECT(IID, INAME,  IMAJVER,    IMINVER,    IFREEZED,   IUPLOADUSER, IUPLOADNUM, IUUID,  IUPLOADTIME,    IUPLOADUSERID,  IGROUPID,PROTYPE, IUPPERID , ILATESTID, IPKGCONTENTID) "
                + "      VALUES ( ?, ?, 0, 0, 0, ?, (select decode( MAX(IUPLOADNUM),null,0,MAX(IUPLOADNUM))+1 from ieai_project where INAME=?), ?, SYSDATE, ?, ?, 1, ?,  ?, -1)";
        // +" VALUES ( 3162, 'aaabbccdd', 0, 0, 0, 'zfc', 1, 'uuid', SYSDATE, 4, 0 )";
        if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
        {
            sql = "INSERT INTO IEAI_PROJECT(IID, INAME,  IMAJVER,    IMINVER,    IFREEZED,   IUPLOADUSER, IUPLOADNUM, IUUID,  IUPLOADTIME,    IUPLOADUSERID,  IGROUPID,PROTYPE, IUPPERID , ILATESTID, IPKGCONTENTID, ISYSTEMCODE) "
                    + "      VALUES ( ?, ?, 0, 0, 0, ?, ?, ?, sysdate(), ?, ?, 1, ?,  ?, -1,?)";
        }

        // 工作流
        long ieai_flowdef_id = 0;
        PreparedStatement pres_flow = null;
        String sql_flow = "INSERT INTO  IEAI_FLOWDEF(IID, IFLOWDESC, IFLOWNAME, IPRJID)  " + "VALUES(?, ?, ?, ?)";

        // 工作流参数
        PreparedStatement pres_param = null;
        String sql_param = "INSERT INTO  IEAI_FLOW_PARAM( PID,IID,IPARAM_NAME,IPARAM_TYPE,IPARAM_DESC,IREAD_ONLY )  "
                + "VALUES(?, ?, ?, ?, ?, ?)";
        long ieai_flowdparam_id = 0;

        // IEAI_WORKFLOWINSTANCE
        PreparedStatement pres_insert_ws = null;
        // PreparedStatement pres_del_ws = null;
        // String sql_delws =
        // "DELETE FROM IEAI_WORKFLOWINSTANCE WHERE IPROJECTNAME =? AND ISTATUS=55";
        String sql_ins_ws = "INSERT INTO  IEAI_WORKFLOWINSTANCE( IFLOWID, IFLOWNAME,  IPROJECTNAME,IFLOWDES, ISTARTTIME, ISTATUS,IENDTIME,IDELETEFLAG ,ISYSTEM)  "
                + "VALUES(?, ?, ?, ?, ?,?,?,?,?)";
        long ieai_workflowinstance_id = 0;

        // 工作流活动
        PreparedStatement pres_act = null;
        String sql_act = "INSERT INTO  IEAI_SON_PROJECT(AID, IID, IACT_ID, IACT_NAME, IACT_SUCCID, IACT_DESC, IACT_TYPE,IACT_CONDITIONS,IPROJECTNAME,IFLOWNAME)  "
                + "VALUES(?, ?, ?, ?, ?, ?,?,?,?,?)";

        long ieai_flowdact_id = 0;
        // 工程信息更新
        PreparedStatement presProjectUpdate = null;
        String sqlProUpdate = "select COUNT(1) from ieai_project";

        try
        {
            pres = con.prepareStatement(sql);
            pres_flow = con.prepareStatement(sql_flow);
            // pres_del_ws = con.prepareStatement(sql_delws);
            pres_insert_ws = con.prepareStatement(sql_ins_ws);
            pres_param = con.prepareStatement(sql_param);
            pres_act = con.prepareStatement(sql_act);
            int index = 1;
            // 主流程所属系统名取子流程中的任意一个 add by yunpeng_zhang at 20150929
            String systemName = "";

            List<ConcurrentHashMap<String, Object>> proUpdateList = new ArrayList<ConcurrentHashMap<String, Object>>();
            for (ExcelDayStartProject prj : proList)
            {

                ieai_project_id = IdGenerator.createIdForAll(RepProject.class, basicType);
                String prjuuid = UUID.uuid();

                pres.setLong(1, ieai_project_id);
                pres.setString(2, prj.getPrjName());
                pres.setString(3, prj.getUploadUser());
                if (JudgeDB.IEAI_DB_TYPE == 3) // mysql数据库不支持insert或update时，不能先select出同一表中的某些值，再insert或update这个表(在同一语句中)，即不能依据某字段值做判断再来更新某字段的值。
                {
                    pres.setLong(4, getPrjMaxUploadNum(prj.getPrjName(), con));
                } else
                {
                    pres.setString(4, prj.getPrjName());
                }
                pres.setString(5, prjuuid);
                pres.setLong(6, prj.getUploadUserId());
                pres.setLong(7, prj.getIgroupid() == 0 ? 1 : prj.getIgroupid());
                /* 添加日启动工作流上传时的工程扁平化存储 by txl 20180517 */
                Long upperPrjId = ProjectManager.getInstance().getMixProjectId(prj.getPrjName(), ieai_project_id, con);
                if (upperPrjId == null || upperPrjId == 0)
                {
                    upperPrjId = ieai_project_id;
                }

                Long iLatestId = ProjectManagerForMultiple.getInstance().queryIlatestId(
                        projectSaveUtilBean.getBasicConnection(), prj.getPrjName(), 1);
                if (iLatestId == null || iLatestId.longValue() == 0)
                {
                    iLatestId = ieai_project_id;
                }
                pres.setLong(8, upperPrjId);
                pres.setLong(9, iLatestId);

                String sysCode = "";
                if (Environment.getInstance().getExportHisExcel()) {
                    sysCode = TaskUploadManager.getInstance().querySysCode(prj.getPrjName(), 1);
                    _log.info("系统编码："  + sysCode);
                }
                if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
                {
                    pres.setString(10, sysCode);
                }
                // 组织存储工程数据信息，给非基线库的其他库使用
                RepProject prjInfo = new RepProject();
                prjInfo.setId(ieai_project_id);
                prjInfo.setName(prj.getPrjName());
                prjInfo.setUuid(prjuuid);
                prjInfo.setUploadNum(
                        TaskUploadManager.getInstance().getPrjMaxUploadNum(prj.getPrjName(), con).intValue());
                prjInfo.setUploadUserFullName(prj.getUploadUser());
                prjInfo.setUploadUserId(prj.getUploadUserId());
                prjInfo.setGroupId(prj.getIgroupid() == 0 ? 1 : prj.getIgroupid());
                prjInfo.setProType(prj.getIgroupid() == 0 ? 1 : prj.getIgroupid());
                prjInfo.setIupperId(upperPrjId);
                prjInfo.setIlatestId(iLatestId);
                prjInfo.setPkgContentId(Long.parseLong(("-1")));
                projectSaveUtilBean.getInserProjectList().add(prjInfo);

                ConcurrentHashMap<String, Object> proMap = new ConcurrentHashMap<String, Object>();
                proMap.put("proName", prj.getPrjName());
                proMap.put("upperPrjId", upperPrjId);
                proMap.put("lastestId", ieai_project_id);
                proMap.put("project", prj);
                proUpdateList.add(proMap);

                List<ExcelDayStartFlows> flowslist = prj.getFlows();

                // 开始删除ieai_workflowInstance
                // pres_del_ws.setString(1, prj.getPrjName());
                // pres_del_ws.executeUpdate();
                // transferWorkFLowInstance(prj.getPrjName(), con);
                // 通过子系统名获取未结束的工作流名
                List<String> notInFlowList = this.getNotInsertWorkFLowInstance(prj.getPrjName(), con);
                int count = 0;
                String sysName = "";
                for (ExcelDayStartFlows excelFlows : flowslist)
                {
                    ieai_flowdef_id = IdGenerator.createIdForAll(RepWorkflow.class, basicType);
                    pres_flow.setLong(1, ieai_flowdef_id);
                    pres_flow.setString(2, excelFlows.getFlowDesc());
                    pres_flow.setString(3, excelFlows.getFlowName());
                    pres_flow.setLong(4, ieai_project_id);

                    // 组织需要保存到IEAI_FLOWDEF表的数据，多写中使用
                    RepWorkflow repFlow = new RepWorkflow();
                    repFlow.setId(ieai_flowdef_id);
                    repFlow.setFlowDesc(excelFlows.getFlowDesc());
                    repFlow.setFlowName(excelFlows.getFlowName());
                    repFlow.setPrjId(ieai_project_id);
                    projectSaveUtilBean.getInserFlowDefList().add(repFlow);

                    // 未结束的工作流名不插入新记录
                    if (!notInFlowList.contains(excelFlows.getFlowName()))
                    {
                        if (null != excelFlows.getSystem() && !"".equals(excelFlows.getSystem()))
                        {
                            sysName = excelFlows.getSystem();
                        } else
                        {
                            sysName = systemName;
                        }
                        count = this.getDayStartInsertWorkFLowInstance(sysName, excelFlows.getFlowName(),
                                prj.getPrjName(), con);
                        if (count == 0)
                        {
                            RepWorkflowInstance flowIns = new RepWorkflowInstance();
                            // ieai_workflowinstance
                            ieai_workflowinstance_id = IdGenerator.createIdForAll(RepWorkflowInstance.class,
                                    basicType);
                            pres_insert_ws.setLong(1, ieai_workflowinstance_id);
                            pres_insert_ws.setString(2, excelFlows.getFlowName());
                            pres_insert_ws.setString(3, prj.getPrjName());
                            pres_insert_ws.setString(4, excelFlows.getFlowDesc());
                            pres_insert_ws.setLong(5, 0);
                            pres_insert_ws.setLong(6, Constants.STATE_NEW_CREATE);
                            pres_insert_ws.setLong(7, 0);
                            pres_insert_ws.setLong(8, 0);
                            pres_insert_ws.setString(9, (null != excelFlows.getSystem() && !"".equals(excelFlows
                                    .getSystem())) ? excelFlows.getSystem() : systemName);
                            pres_insert_ws.addBatch();

                            //加入对workflowinstance表的多写存储
                            flowIns.setFlowId(ieai_workflowinstance_id);
                            flowIns.setFlowName(excelFlows.getFlowName());
                            flowIns.setProjectName(prj.getPrjName());
                            flowIns.setFlowDes(excelFlows.getFlowDesc());
                            flowIns.set_isystem((null != excelFlows.getSystem() && !"".equals(excelFlows
                                    .getSystem())) ? excelFlows.getSystem() : systemName);
                            projectSaveUtilBean.getInsertFlowInsList().add(flowIns);
                        }
                    }

                    List<ExcelDayStartFlowParams> flowParamlist = excelFlows.getFlowParamList();
                    for (ExcelDayStartFlowParams param : flowParamlist)
                    {
                        ieai_flowdparam_id = IdGenerator.createIdNoConnection("IEAI_FLOW_PARAM",
                                basicType);

                        pres_param.setLong(1, ieai_flowdparam_id);
                        pres_param.setLong(2, ieai_flowdef_id);
                        pres_param.setString(3, param.getParamName());
                        pres_param.setString(4, param.getParamType());
                        pres_param.setString(5, param.getParamDesc());
                        pres_param.setString(6, param.getParamOnly());
                        pres_param.addBatch();

                        param.setPid(ieai_flowdparam_id);
                        param.setFid(ieai_flowdef_id);
                        projectSaveUtilBean.getInsertDayStartFlowParams().add(param);
                    }
                    List<ExcelDayStartAct> actlist = excelFlows.getActList();

                    for (ExcelDayStartAct act : actlist)
                    {
                        ieai_flowdact_id = IdGenerator.createIdNoConnection("IEAI_SON_PROJECT",
                                basicType);

                        pres_act.setLong(1, ieai_flowdact_id);
                        pres_act.setLong(2, ieai_flowdef_id);
                        pres_act.setLong(3, act.getActId());
                        pres_act.setString(4, act.getActName());
                        pres_act.setLong(5, act.getActsuccid());
                        pres_act.setString(6, act.getActDesc());
                        pres_act.setString(7, act.getActType());
                        pres_act.setString(8, act.getSuccConditions());
                        pres_act.setString(9, act.getProjectName());
                        pres_act.setString(10, act.getFlowname());
                        act.setAid(ieai_flowdact_id);
                        act.setIid(ieai_flowdef_id);
                        pres_act.addBatch();
                        if (index % 3000 == 0)
                        {
                            pres_act.executeBatch();
                        }
                        index++;
                        projectSaveUtilBean.getInsertDayStartActList().add(act);
                    }

                    pres_flow.addBatch();
                }
                pres.addBatch();
            }
            pres_insert_ws.executeBatch(); // ieai_workflowinstance 批量入库
            pres.executeBatch(); // 工程信息批量入库
            pres_flow.executeBatch();// 工作流信息批量入库
            pres_param.executeBatch();// 参数信息批量入库
            pres_act.executeBatch();// 活动信息批量入库
            // 工程信息更新入库
            updateProjectLastesdId(proUpdateList, con, projectSaveUtilBean.getBasicConnection());
        } catch (SQLException e)
        {
            flag = false;
            try
            {
                con.rollback();
                if (projectSaveUtilBean.getBasicConnection() != null)
                {
                    projectSaveUtilBean.getBasicConnection().rollback();
                }
                _log.error("批量组织日启动活动信息失败  已回滚. 异常信息为" + e);
            } catch (SQLException ex)
            {
                _log.error("saveDaySartExcelProject 批量组织活动信息 异常信息为：" + e);
                throw new RepositoryException(ServerError.ERR_DB_ROLLBACK);
            }
        } finally
        {
            DBResource.closePreparedStatement(pres_insert_ws, "saveDaySartExcelProject", _log);
            DBResource.closePreparedStatement(pres, "saveDaySartExcelProject", _log);
            DBResource.closePreparedStatement(pres_flow, "saveDaySartExcelProject", _log);
            DBResource.closePreparedStatement(pres_param, "saveDaySartExcelProject", _log);
            DBResource.closePreparedStatement(pres_act, "saveDaySartExcelProject", _log);
            DBResource.closePreparedStatement(presProjectUpdate, "saveDaySartExcelProject", _log);
        }
        return flag;
    }

    /**
     * @Title: updateProjectLastesdId
     * @Description: 批量更新工程的扁平化信息
     * @param prolist
     * @param con
     * @author: xinglin_tian
     * @throws RepositoryException
     * @date:   2018年5月22日 上午10:34:57   
     */
    public void updateProjectLastesdId ( List<ConcurrentHashMap<String, Object>> prolist, Connection conn,
                                         Connection baseConn )
            throws RepositoryException
    {
        String sql = "select * from ieai_project t where t.iupperId = ?";
        String sql1 = "update ieai_project t set t.iupperid=? , t.ILATESTID=? where t.iupperid=? or t.iname=?";
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        for (ConcurrentHashMap<String, Object> map : prolist)
        {
            long upperId = (Long) map.get("upperPrjId");
            long lastId = (Long) map.get("lastestId");
            String projectName = (String) map.get("proName");
            ExcelDayStartProject prj = (ExcelDayStartProject) map.get("project");
            try
            {
                ps = conn.prepareStatement(sql);
                ps.setLong(1, upperId);
                rs = ps.executeQuery();
                if (rs.next())
                {
                    ps1 = conn.prepareStatement(sql1);
                    ps1.setLong(1, upperId);
                    ps1.setLong(2, lastId);
                    ps1.setLong(3, upperId);
                    ps1.setString(4, projectName);
                    ps1.executeUpdate();
                } else
                {
                    sql1 = "update ieai_project t set t.iupperid=? , t.ILATESTID=? where t.iname=?";
                    ps1 = conn.prepareStatement(sql1);
                    ps1.setLong(1, upperId);
                    ps1.setLong(2, lastId);
                    ps1.setString(3, projectName);
                    ps1.executeUpdate();
                }
                // 工程赋权
                RepProject rp = new RepProject();
                InfoExeclUtilBean infoExeclUtilBean = new InfoExeclUtilBean();
                rp.setIupperId(upperId);
                infoExeclUtilBean.setInsertProject(rp);
                UserInfo user = new UserInfo();
                user.setId(prj.getUploadUserId());
                InfoExeclServicesMultipleForSUS.getInstance().buildIsextend(baseConn, user,
                        infoExeclUtilBean);
                for (String exeSql : infoExeclUtilBean.getExeSqls())
                {
                    boolean isOk = DBUtil.executeSql(baseConn, exeSql);
                    if (!isOk)
                    {
                        throw new SQLException();
                    }
                }
            } catch (SQLException e)
            {
                _log.error(e.getMessage(), e);
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } finally
            {
                DBResource.closePSRS(rs, ps, "updatePrjUpperAndLastId", _log);
                DBResource.closePreparedStatement(ps1, "updatePrjUpperAndLastId", _log);
            }

        }
    }

    /**
     * @Title: getPrjMaxUploadNum
     * @Description: TODO(新增方法，取出PrjMaxUploadNum)
     * @param prjname
     * @param dbType
     * @return
     * @throws ServerException
     * @author: yue_sun
     * @date:   2018年3月6日 下午3:06:01   
     */
    public long getPrjMaxUploadNum ( String prjname, Connection conn ) throws ServerException
    {
        long result = 0;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            String sql = "select (case when MAX(IUPLOADNUM) is null then 1 else MAX(IUPLOADNUM)+1 end) as count from ieai_project where INAME=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, prjname);
            rset = ps.executeQuery();
            if (rset.next())
            {
                result = rset.getLong("count");
            }
        } catch (SQLException e)
        {
            _log.error("getPrjMaxUploadNum ieai_project_agentinfo IS ERR" + e.getMessage());
        } finally
        {
            DBResource.closePSRS(rset, ps, "getPrjMaxUploadNum", _log);
        }
        return result;
    }

    /**
     * <li>Description:保存活动的lastline</li>
     *
     * <AUTHOR> 2015年8月12日
     * @param lastLine return void
     */
    public void saveActLastLine ( ActLastLine lastLine, Connection con, int basicType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        int count = 0;
        long aid = 0;
        try
        {

            String sql = "";
            String sql1 = "SELECT COUNT(AID) as cou FROM IEAI_ACT_LASTLINE  WHERE   IFLOW_ID=?";
            pres = con.prepareStatement(sql1);
            pres.setLong(1, lastLine.getFlowid());
            rs = pres.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("cou");
            }
            if (count > 0)
            {
                sql = "UPDATE IEAI_ACT_LASTLINE SET  LASTLINE=? WHERE IFLOW_ID=?";
                pres = con.prepareStatement(sql);
                pres.setString(1, lastLine.getLastline());
                pres.setLong(2, lastLine.getFlowid());
                pres.executeUpdate();
            } else
            {
                aid = IdGenerator.createIdForAll(ActLastLine.class, basicType);
                sql = "INSERT INTO IEAI_ACT_LASTLINE( AID, IPROJECT_NAME, IACT_NAME, IFLOW_ID, LASTLINE ) VALUES (?,?,?,?,? )";
                pres = con.prepareStatement(sql);
                pres.setLong(1, aid);
                pres.setString(2, lastLine.getProjectName());
                pres.setString(3, lastLine.getActName());
                pres.setLong(4, lastLine.getFlowid());
                pres.setString(5, lastLine.getLastline());
                pres.executeUpdate();
            }
        } catch (SQLException e)
        {
            e.printStackTrace();
        } catch (RepositoryException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, pres, "saveActLastLine", _log);
        }
    }

    /**
     * <li>Description:获取活动LastLine</li>
     *
     * <AUTHOR> 2015年8月12日
     * @param proName
     * @param mainFlownme
     * @return return List<ExcelAct>
     * @throws RepositoryException
     */
    public String getActLastLine ( long flowId ) throws RepositoryException
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String lastline = "";
        Connection conn = null;
        for (int i = 0;; i++)
        {
            try
            {
                conn = DBResource.getConnection("getActLastLine", _log, Constants.IEAI_IEAI_BASIC);
                try
                {
                    String sql = "SELECT LASTLINE FROM  IEAI_ACT_LASTLINE WHERE IFLOW_ID=?";
                    pres = conn.prepareStatement(sql);
                    pres.setLong(1, flowId);
                    rs = pres.executeQuery();
                    while (rs.next())
                    {
                        lastline = rs.getString("LASTLINE");
                    }
                    return lastline;
                } catch (SQLException e)
                {
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(conn, rs, pres, "getActLastLine", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_UPDATE);
            }

        }
    }
    // {
    // PreparedStatement pres = null;
    // ResultSet rs = null;
    // String lastline = "";
    // Connection conn = null;
    // try
    // {
    // conn = DBManager.getInstance().getJdbcConnection();
    // String sql = "SELECT LASTLINE FROM  IEAI_ACT_LASTLINE WHERE IFLOW_ID=?";
    // pres = conn.prepareStatement(sql);
    // pres.setLong(1, flowId);
    // rs = pres.executeQuery();
    // while (rs.next())
    // {
    // lastline = rs.getString("LASTLINE");
    // }
    //
    // } catch (DBException e)
    // {
    // e.printStackTrace();
    // } catch (SQLException e)
    // {
    // e.printStackTrace();
    // } finally
    // {
    // try
    // {
    // DBResource.closeConn(conn, rs, pres, "getActLastLine", _log);
    // } catch (RepositoryException e)
    // {
    // }
    // }
    // return lastline;
    // }
    //
    /**
     * gg通过callflow查询被调活动的最后一行
     *
     * @param flowId
     * @return
     */
    public String getCallActLastLine ( long flowId, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String lastline = "";
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "SELECT LASTLINE FROM  IEAI_ACT_LASTLINE WHERE IFLOW_ID=(SELECT C.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO C WHERE  C.IMAINFLOWID=?)";
            pres = conn.prepareStatement(sql);
            pres.setLong(1, flowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                lastline = rs.getString("LASTLINE");
            }

        } catch (DBException e)
        {
            e.printStackTrace();
        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getActLastLine", _log);
        }
        return lastline;
    }

    /**
     * gg通过getPreActLastLine查询被调活动的最后一行
     *
     * @param flowId 主线flowid
     * @return
     */
    public Map getPreActLastLine ( long operId, long flowId, String actNames, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        Map map = new HashMap();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = " SELECT AL.IACT_NAME,AL.LASTLINE FROM IEAI_ACT_LASTLINE AL WHERE AL.IFLOW_ID IN (SELECT CA.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CA WHERE CA.IMAINFLOWID=?) AND AL.IACT_NAME IN("
                    + actNames + ")";
            pres = conn.prepareStatement(sql);
            // pres.setLong(1, operId);
            pres.setLong(1, flowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                map.put(rs.getString("IACT_NAME"), rs.getString("LASTLINE"));
            }
        } catch (DBException e)
        {
            e.printStackTrace();
        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getPreActLastLine", _log);
        }
        return map;
    }

    public Map getNewPreActLastLine ( long flowId, String actNames, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        Map map = new HashMap();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String[] actNameArray = actNames.split(",");
            StringBuilder sqlBuilder = new StringBuilder(
                    "SELECT AL.IACT_NAME,AL.LASTLINE FROM IEAI_ACT_LASTLINE AL " +
                            " WHERE AL.IFLOW_ID IN (select ICALLFLOWID from IEAI_CALLWORKFLOW_INFO" +
                            " where IMAINFLOWID in (select IMAINFLOWID from IEAI_CALLWORKFLOW_INFO where ICALLFLOWID = ?)) " +
                            " AND AL.IACT_NAME IN(");
            for (int i = 0; i < actNameArray.length; i++) {
                if (i > 0) {
                    sqlBuilder.append(",");
                }
                sqlBuilder.append("?");
            }
            sqlBuilder.append(")");

            pres = conn.prepareStatement(sqlBuilder.toString());
            pres.setLong(1, flowId);
            for (int i = 0; i < actNameArray.length; i++) {
                pres.setString(i + 2, actNameArray[i].trim());
            }
            rs = pres.executeQuery();
            while (rs.next())
            {
                map.put(rs.getString("IACT_NAME"), rs.getString("LASTLINE"));
            }
        } catch (DBException | SQLException e)
        {
            _log.error("getNewPreActLastLine is error " + e.getMessage(),e);
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getPreActLastLine", _log);
        }
        return map;
    }

    /**
     * 通过当前工作流的id和活动名称获取前继活动的LastLine
     *
     * @param curFlowId 当前活动Id
     * @param actNames
     * @return
     */
    public Map getPreActLastLine ( long curFlowId, String actNames, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        Map map = new HashMap();
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = " SELECT AL.IACT_NAME,AL.LASTLINE FROM IEAI_ACT_LASTLINE AL WHERE AL.IFLOW_ID IN (SELECT ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO"
                    + " WHERE IMAINFLOWID IN (SELECT IMAINFLOWID  FROM IEAI_CALLWORKFLOW_INFO T   WHERE ICALLFLOWID = ?)) AND AL.IACT_NAME ='"
                    + actNames + "'";
            pres = conn.prepareStatement(sql);
            // pres.setLong(1, operId);
            pres.setLong(1, curFlowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                map.put(rs.getString("IACT_NAME"), rs.getString("LASTLINE"));
            }
        } catch (DBException e)
        {
            e.printStackTrace();
        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getPreActLastLine", _log);
        }
        return map;
    }

    /**
     * <li>Description:获取活动LastLine</li>
     *
     * <AUTHOR> 2015年8月12日
     * @param proName
     * @param mainFlownme
     * @return return List<ExcelAct>
     */
    public String getActLastLine ( long flowId, Connection conn )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String lastline = "";
        try
        {
            String sql = "SELECT LASTLINE FROM  IEAI_ACT_LASTLINE WHERE IFLOW_ID=?";
            pres = conn.prepareStatement(sql);
            pres.setLong(1, flowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                lastline = rs.getString("LASTLINE");
            }

        } catch (SQLException e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, pres, "getActLastLine", _log);
        }
        return lastline;
    }

    /**
     * <li>Description:获取工作流数据日期</li>
     *
     * <AUTHOR> 2015年8月13日
     * @param flowId
     * @return return String update 2015-09-24 fengchun_zhang 查询数据日期时 IEAI_WORKFLOWINSTANCE 未查到时
     * 去周期表查
     */
    public String getFlowInsNme ( long flowId, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String lastline = "";
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "select IFLOWINSNAME from IEAI_WORKFLOWINSTANCE where IFLOWID=?";
            pres = conn.prepareStatement(sql);
            pres.setLong(1, flowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                lastline = rs.getString("IFLOWINSNAME");
            }
            if ("".equals(lastline))
            {

                String sql_CICLE = "select IFLOWINSNAME from IEAI_WORKFLOWCICLE where IFLOWID=?";
                pres = conn.prepareStatement(sql_CICLE);
                pres.setLong(1, flowId);
                rs = pres.executeQuery();
                while (rs.next())
                {
                    lastline = rs.getString("IFLOWINSNAME");
                }
            }

        } catch (DBException e)
        {
            _log.error("getFlowInsNme getconnection err is :" + e);
        } catch (SQLException e)
        {
            _log.error("getFlowInsNme execute sql  err is :" + e);
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getFlowInsNme", _log);
        }
        return lastline;
    }

    public String getFlowInsNme ( long flowId, Connection con )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String lastline = "";
        // Connection conn = null;
        try
        {
            // conn = DBManager.getInstance().getJdbcConnection();
            String sql = "select IFLOWINSNAME from IEAI_WORKFLOWINSTANCE where IFLOWID=?";
            pres = con.prepareStatement(sql);
            pres.setLong(1, flowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                lastline = rs.getString("IFLOWINSNAME");
            }
            if ("".equals(lastline))
            {

                String sql_CICLE = "select IFLOWINSNAME from IEAI_WORKFLOWCICLE where IFLOWID=?";
                pres = con.prepareStatement(sql_CICLE);
                pres.setLong(1, flowId);
                rs = pres.executeQuery();
                while (rs.next())
                {
                    lastline = rs.getString("IFLOWINSNAME");
                }
            }

        } catch (SQLException e)
        {
            _log.error("getFlowInsNme execute sql  err is :" + e);
        } finally
        {
            DBResource.closePSRS(rs, pres, "getFlowInsNme", _log);
        }
        return lastline;
    }

    /**
     * <li>Description:替换数据日期 将 脚本内容中 YYYYMMDD 替换成实际数据日期</li>
     *
     * <AUTHOR> 2015年8月13日
     * @param actElement
     * @param flowid
     * @return return BasicActElement
     * @throws ServerException update 2015-09-24 fengchun_zhang 数据日期查询时异常处理
     */
    public BasicActElement replaceYYYYMMDD ( BasicActElement actElement, long flowid, int dbType )
            throws ServerException
    {
        Map map = actElement.getInputExprs();
        if (null != map && map.size() > 0)
        {
            String oldCommand = "";
            Object cmd = actElement.getInputExprs().get(Constants.SHELLCMD_COMMAND);
            if (null != cmd)
            {
                oldCommand = cmd.toString();
            }
            if (null != oldCommand && !"".equals(oldCommand))
            {
                String insName = ExcelActUtil.getInstance().getFlowInsNme(flowid, dbType);
                if (null == insName || "".equals(insName))
                {
                    _log.error(actElement.getName() + " 启动时 获取数据日期失败 ：-90909 ");
                    throw new ServerException(-90909);
                }
                String newcommand = oldCommand.replaceAll(Constants.SHELLCMD_YYYYMMDD, insName == null ? "" : insName);
                _log.info("flowid :" + flowid + "   " + actElement.getName() + "===" + "oldCommand:" + oldCommand);
                _log.info("flowid :" + flowid + "   " + actElement.getName() + "===" + "newcommand:" + newcommand);
                actElement.getInputExprs().put(Constants.SHELLCMD_COMMAND, newcommand);
            }
        }
        return actElement;
    }

    /**
     * <li>Description:替换shellcmd中 YYYYMMMDD 参数 数据日期</li>
     *
     * <AUTHOR> 2015年10月13日
     * @param cmd
     * @param flowid
     * @return
     * @throws ServerException return String
     */
    public String replaceYYYYMMDD ( String cmd, long flowid, int dbType ) throws ServerException
    {
        if (null != cmd && !"".equals(cmd))
        {
            String insName = ExcelActUtil.getInstance().getFlowInsNme(flowid, dbType);
            if (null == insName || "".equals(insName))
            {
                _log.error("flowID: " + flowid + " 获取数据日期失败 ：-90909 ");
                throw new ServerException(-90909);
            }//cmd 是否包含YYYY-MM-DD日期格式
            //cmd 是否包含%日期
            if (insName.length()!=8 && insName.length()==12){
                insName = insName.substring(0,8);
            }            boolean statusCmd = cmd.contains("%");

            if(statusCmd){
                Map returnMap = new HashMap();
                returnMap=   replaceInsName( cmd,insName);
                cmd=(String) returnMap.get("cmd");
                insName=(String) returnMap.get("insName");
            }
            boolean status = cmd.contains(Constants.SHELLCMD_YYYY_MM_DD);
            if(status){
                String insNameYmd="";
                if(insName.length()==8){
                    insNameYmd=   insName.substring(0,4)+"-"+insName.substring(4,6)+"-"+insName.substring(6,8);

                }else {
                    insNameYmd=insName;
                }
                cmd = cmd.replaceAll(Constants.SHELLCMD_YYYY_MM_DD, insNameYmd == null ? "" : insNameYmd);
            }else {
                cmd = cmd.replaceAll(Constants.SHELLCMD_YYYYMMDD, insName == null ? "" : insName);
            }

        }
        return cmd;
    }

    public String replaceLastLine (String cmd, long flowid){
        //poc功能支持传参，替换命令行中的参数
        List<String> result = extractMiddlePartsFromDollarLastLineStrings(cmd);

        if(result.isEmpty()){
            _log.info("flowID: " + flowid + " 活动中不涉及$lastline参数");
        }else{
            Pattern pattern = Pattern.compile("\\$(\\w+)\\.lastline");
            Matcher matcher = pattern.matcher(cmd);
            //通过flowid获取参数
            Map mapPre = getNewPreActLastLine(flowid,
                    StringUtils.join(result,","), Constants.IEAI_IEAI);

            StringBuffer sb = new StringBuffer();
            matcher.reset();
            while (matcher.find()) {
                String actName = matcher.group(1); // 提取活动名称部分
                String replacement = (String)mapPre.get(actName);
                // 如果活动名称有值就替换，没有值就替换为空串
                matcher.appendReplacement(sb, replacement != null ? replacement : "");
            }
            matcher.appendTail(sb);
            cmd = sb.toString();
            _log.info("flowID: " + flowid + " 替换$lastline参数后的命令: " + cmd);
        }
        return cmd;
    }


    public List<String> extractMiddlePartsFromDollarLastLineStrings(String cmd) {
        List<String> middleParts = new ArrayList<>();
        if (cmd == null || cmd.isEmpty()) {
            return middleParts; // 空值保护
        }

        String[] cmds = cmd.split(" ");
        for (String s : cmds) {
            if (s.startsWith("$") && s.contains(Constants.LAST_LINE)) {
                if (s.length() > ".lastLine".length() + 1) { // 确保长度足够
                    String middle = s.substring(1, s.indexOf(".lastLine"));
                    middleParts.add(middle);
                }
            }
        }

        return middleParts;
    }

    //根据模板添加或减少insName天数
    public Map replaceInsName ( String cmd,String  insName){
        Map returnMap = new HashMap();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String[] arrayDay ;
        int start=0;
        boolean statusjj=false;
        boolean status = cmd.contains("%YYYYMMDD");
        boolean status5 = cmd.contains("%YYYY-MM-DD");
        if(status){
            boolean status1 = cmd.contains("%YYYYMMDD+");
            if(status1){
                start=  cmd.indexOf("%YYYYMMDD+");
                statusjj=true;
            }else{
                start=  cmd.indexOf("%YYYYMMDD-");
                statusjj=true;
            }
        }else if(status5) {
            boolean status2 = cmd.contains("%YYYY-MM-DD+");
            if(status2){
                start=  cmd.indexOf("%YYYY-MM-DD+");
                statusjj=true;
            }else{
                start=  cmd.indexOf("%YYYY-MM-DD-");
                statusjj=true;
            }

        }
        String day="";
        start= start+1;
        String ufunc="";
        int   ufuncWay=0;
        int end =  cmd.indexOf("%",start);
        if(end!=-1&&statusjj){
            day=  cmd.substring(start, end);
            if(day.contains("+")){//加日期
                arrayDay = day.split("\\+");
                if(arrayDay.length==2){
                    for (int i = 0; i < arrayDay.length; i++) {
                        if(i==1){
                            ufunc= "+"+  arrayDay[i];
                            _log.info(i+"、加天数："+arrayDay[i]);
                            ufuncWay=1;
                        }
                    }
                }
            }else if(day.contains("-")){//减日期
                arrayDay = day.split("-");
                if(arrayDay.length==4){
                    for (int i = 0; i < arrayDay.length; i++) {
                        if(i==3){
                            ufunc= "-"+  arrayDay[i];
                            _log.info(i+"、减天数："+arrayDay[i]);
                            ufuncWay=2;
                        }
                    }
                }else if(arrayDay.length==2){
                    for (int i = 0; i < arrayDay.length; i++) {
                        if(i==1){
                            ufunc= "-"+ arrayDay[i];
                            _log.info(i+"、减天数："+arrayDay[i]);
                            ufuncWay=2;
                        }
                    }
                }
            }

            if(!"".equals(ufunc)&&ufuncWay!=0){
                _log.info("insName日期转换前："+insName);

                DateFormat format = new SimpleDateFormat("yyyyMMdd"); //定义日期格式化的格式
                try {
                    Date classDate = format.parse(insName);//把字符串转化成指定格式的日期
                    Calendar calendar = Calendar.getInstance(); //使用Calendar日历类对日期进行加减
                    calendar.setTime(classDate);

                    int  Jj=Integer.parseInt(ufunc);
                    calendar.add(Calendar.DAY_OF_MONTH,Jj);
                    classDate = calendar.getTime();//获取加减以后的Date类型日期
                    insName= format.format(classDate);
                    _log.info("insName日期转换后："+insName);
                } catch (ParseException e) {
                    _log.error(method,e);
                }


            }
        }
        if(ufunc.contains("+")){
            ufunc = ufunc.replaceAll("\\+","-");
            day = day.replaceAll("\\+","-");
            cmd = cmd.replaceAll("\\+","-");
        }
        day = day.replaceAll(ufunc,"");
        String replace ="%"+day+ufunc+"%";
        boolean  YlbankYYYYMMDD = Environment.getInstance().getYlbankYYYYMMDDSwitch();
        if(YlbankYYYYMMDD){//亿联替换%内YYYYMMDD需求

            _log.info("YlbankYYYYMMDD："+YlbankYYYYMMDD);

            boolean status111 = cmd.contains(Constants.SHELLCMD_YYYY_MM_DD);
            if(status111){
                String insNameYmd="";
                if(insName.length()==8){
                    insNameYmd=   insName.substring(0,4)+"-"+insName.substring(4,6)+"-"+insName.substring(6,8);

                }else {
                    insNameYmd=insName;
                }
                cmd = cmd.replaceAll(replace, insNameYmd == null ? "" : insNameYmd);
            }else {
                cmd = cmd.replaceAll(replace, insName == null ? "" : insName);
            }

        }else {

            cmd=cmd.replaceAll(replace,day);
        }
        _log.info("cmd："+cmd);
        returnMap.put("cmd", cmd);
        returnMap.put("insName", insName);
        return returnMap;
    }

    public String replaceYYYYMMDD ( String cmd, long flowid, Connection con ) throws ServerException
    {
        if (null != cmd && !"".equals(cmd))
        {
            String insName = ExcelActUtil.getInstance().getFlowInsNme(flowid, con);
            if (null == insName || "".equals(insName))
            {
                _log.error("flowID: " + flowid + " 获取数据日期失败 ：-90909 ");
                throw new ServerException(-90909);
            }
            cmd = cmd.replaceAll(Constants.SHELLCMD_YYYYMMDD, insName == null ? "" : insName);
        }
        return cmd;
    }

    /**
     * <li>Description:工作流实列表数据转移</li>
     *
     * <AUTHOR> 2015年8月14日
     * @param proName
     * @param con return void
     */
    // public void transferWorkFLowInstance ( String proName, Connection con )
    // {
    // PreparedStatement prewaitdel = null;
    // PreparedStatement preinsertHis = null;
    // PreparedStatement predelData = null;
    // // 将正在运行的工作流打上删除标记
    // String sqlWaitDelData = " update ieai_workflowinstance w2 set w2.IDELETEFLAG = 1 "
    // + "where w2.iflowid in (select distinct t.iflowid from ieai_workflowinstance t where
    // t.istatus not in ("
    // + Constants.STATE_LISTENING + "," + Constants.STATE_RUNNING + "," + Constants.STATE_PAUSED +
    // ","
    // + Constants.STATE_RECOVERING + "," + Constants.STATE_FAIL + "," + Constants.STATE_NEW_CREATE
    // + ","
    // + Constants.STATE_FAIL_BUSSINESS + "," + Constants.STATE_QUEUEUP + ")" + " and
    // t.IPROJECTNAME=? )";
    // // 非运行的工作流转移到 周期表中
    // String sqlinsertHis = " insert into IEAI_WORKFLOWCICLE(IFLOWID ,ISTARTUSERFULLNAME
    // ,IISRECOVERYANALYZED , IISAUTOSTART ,"
    // + " IFLOWCOMMENT , IFLOWDEFID , IFLOWDES , IFLOWINSNAME ,IFLOWNAME , IFLOWPRIOR , IHOSTNAME ,
    // IISSAFEFIRST , "
    // + " IISMAINBRANCHEND , IMAINSCOPEID , IPRJUUID ,IPROJECTNAME , ISTARTTIME , ISTARTUSERID ,
    // ISTATUS , IENDTIME ,"
    // + " RECOVERTIME , RECOVERPERSON , ISCHECK , IPAUSETIME , IPAUSEENDTIME
    // ,IDELETEFLAG,IACTQUEUEUPTIME ,"
    // + " IACTRUNNINGTIME,IACTENDTIME ,ISYSTEM,IPRJUPPERID,IPRJID,IFLOWUPPERID,IFLOWOWNID) select
    // w1.IFLOWID ,w1.ISTARTUSERFULLNAME ,w1.IISRECOVERYANALYZED ,"
    // + " w1.IISAUTOSTART , w1.IFLOWCOMMENT , w1.IFLOWDEFID , w1.IFLOWDES , w1.IFLOWINSNAME
    // ,w1.IFLOWNAME , "
    // + " w1.IFLOWPRIOR , w1.IHOSTNAME , w1.IISSAFEFIRST , w1.IISMAINBRANCHEND , w1.IMAINSCOPEID ,
    // w1.IPRJUUID ,"
    // + " w1.IPROJECTNAME , w1.ISTARTTIME , w1.ISTARTUSERID , w1.ISTATUS , w1.IENDTIME ,
    // w1.RECOVERTIME , "
    // + " w1.RECOVERPERSON , w1.ISCHECK , w1.IPAUSETIME , w1.IPAUSEENDTIME
    // ,w1.IDELETEFLAG,w1.IACTQUEUEUPTIME ,"
    // + " w1.IACTRUNNINGTIME,w1.IACTENDTIME,
    // W1.ISYSTEM,W1.IPRJUPPERID,W1.IPRJID,W1.IFLOWUPPERID,W1.IFLOWOWNID from ieai_workflowinstance
    // w1 where w1.IPROJECTNAME=?"
    // + " and w1.IDELETEFLAG = 1 ";
    // if (PersonalityEnv.isAutoflowFlagDefault())
    // {
    // sqlinsertHis = " insert into IEAI_WORKFLOWCICLE(IFLOWID ,ISTARTUSERFULLNAME
    // ,IISRECOVERYANALYZED , IISAUTOSTART ,"
    // + " IFLOWCOMMENT , IFLOWDEFID , IFLOWDES , IFLOWINSNAME ,IFLOWNAME , IFLOWPRIOR , IHOSTNAME ,
    // IISSAFEFIRST , "
    // + " IISMAINBRANCHEND , IMAINSCOPEID , IPRJUUID ,IPROJECTNAME , ISTARTTIME , ISTARTUSERID ,
    // ISTATUS , IENDTIME ,"
    // + " RECOVERTIME , RECOVERPERSON , ISCHECK , IPAUSETIME , IPAUSEENDTIME
    // ,IDELETEFLAG,IACTQUEUEUPTIME ,"
    // + " IACTRUNNINGTIME,IACTENDTIME
    // ,ISYSTEM,IPRJUPPERID,IPRJID,IFLOWUPPERID,IFLOWOWNID,IHANGFLAG) select w1.IFLOWID
    // ,w1.ISTARTUSERFULLNAME ,w1.IISRECOVERYANALYZED ,"
    // + " w1.IISAUTOSTART , w1.IFLOWCOMMENT , w1.IFLOWDEFID , w1.IFLOWDES , w1.IFLOWINSNAME
    // ,w1.IFLOWNAME , "
    // + " w1.IFLOWPRIOR , w1.IHOSTNAME , w1.IISSAFEFIRST , w1.IISMAINBRANCHEND , w1.IMAINSCOPEID ,
    // w1.IPRJUUID ,"
    // + " w1.IPROJECTNAME , w1.ISTARTTIME , w1.ISTARTUSERID , w1.ISTATUS , w1.IENDTIME ,
    // w1.RECOVERTIME , "
    // + " w1.RECOVERPERSON , w1.ISCHECK , w1.IPAUSETIME , w1.IPAUSEENDTIME
    // ,w1.IDELETEFLAG,w1.IACTQUEUEUPTIME ,"
    // + " w1.IACTRUNNINGTIME,w1.IACTENDTIME,
    // W1.ISYSTEM,W1.IPRJUPPERID,W1.IPRJID,W1.IFLOWUPPERID,W1.IFLOWOWNID,W1.IHANGFLAG from
    // ieai_workflowinstance w1 where w1.IPROJECTNAME=?"
    // + " and w1.IDELETEFLAG = 1 ";
    // }
    // // 删除已经转移的数据
    // String sqlDelData = " delete from ieai_workflowinstance w1 where w1.IPROJECTNAME=? and
    // w1.IDELETEFLAG = 1 ";
    // try
    // {
    // prewaitdel = con.prepareStatement(sqlWaitDelData);
    // prewaitdel.setString(1, proName);
    // prewaitdel.executeUpdate();
    //
    // preinsertHis = con.prepareStatement(sqlinsertHis);
    // preinsertHis.setString(1, proName);
    // preinsertHis.executeUpdate();
    //
    // predelData = con.prepareStatement(sqlDelData);
    // predelData.setString(1, proName);
    // predelData.executeUpdate();
    // } catch (SQLException e)
    // {
    // _log.error("ieai_workflowinstance 数据表转换过程中出错:" + e);
    //
    // } finally
    // {
    // DBResource.closePreparedStatement(prewaitdel, "transferWorkFLowInstance", _log);
    // DBResource.closePreparedStatement(preinsertHis, "transferWorkFLowInstance", _log);
    // DBResource.closePreparedStatement(predelData, "transferWorkFLowInstance", _log);
    // }
    // }

    /**
     * <li>Description:获取工程下有几个主线头和总头</li>
     *
     * <AUTHOR> 2015年8月19日
     * @param projecName
     * @param flowName
     * @return return long
     */
    public List<HeadJob> getMainLineHeadSize ( String projecName, String flowName, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        Connection conn = null;
        List<HeadJob> list = new ArrayList<HeadJob>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "SELECT ICHILDPRONAME,IACTNAME FROM IEAI_EXCELMODEL  T WHERE T.IMAINPRONAME=?  AND T.IMAINLINENAME=?  AND ( T.IHEADTAILFLAG=2 OR T.IHEADTAILFLAG=1)";
            pres = conn.prepareStatement(sql);
            pres.setString(1, projecName);
            pres.setString(2, flowName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                HeadJob job = new HeadJob();
                job.setPrjName(rs.getString("ICHILDPRONAME"));
                ;
                job.setFlowName(rs.getString("IACTNAME"));
                list.add(job);
            }

        } catch (DBException e)
        {
            _log.error("getMainLineHeadSize getconnection err is :" + e);
        } catch (SQLException e)
        {
            _log.error("getMainLineHeadSize execute sql  err is :" + e);
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getMainLineHeadSize", _log);
        }
        return list;
    }

    /**
     * <li>Description:获取工程下有几个主线头和总头</li>
     *
     * <AUTHOR> 2015年8月19日
     * @param projecName
     * @param flowName
     * @return return long
     */
    public List<HeadJob> getDayStartMainLineHeadSize ( String projecName, String flowName, int dbType )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        Connection conn = null;
        List<HeadJob> list = new ArrayList<HeadJob>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "SELECT TT.IACTNAME,TT.ICALLPRONAME  FROM IEAI_EXCELMOLDE_PREPRO TT WHERE TT.IPRONAME = ?  AND TT.IFLOWNAME = ?  AND TT.IHEADFLAG=1";
            pres = conn.prepareStatement(sql);
            pres.setString(1, projecName);
            pres.setString(2, flowName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                HeadJob job = new HeadJob();
                job.setPrjName(rs.getString("ICALLPRONAME"));
                ;
                job.setFlowName(rs.getString("IACTNAME"));
                list.add(job);
            }

        } catch (DBException e)
        {
            _log.error("getDayStartMainLineHeadSize getconnection err is :" + e);
        } catch (SQLException e)
        {
            _log.error("getDayStartMainLineHeadSize execute sql  err is :" + e);
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "getDayStartMainLineHeadSize", _log);
        }
        return list;
    }

    /**
     * <li>Description:获取序列</li>
     *
     * <AUTHOR> 2015年8月26日
     * @param conn
     * @return
     * @throws ServerException return long
     */
    public long getSequequSonProject ( Connection conn, int basicType ) throws ServerException
    {
        long id = 1;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    String sql = "select SEQ_SON_PROJECT.nextVal COUNT from dual";
                    if (JudgeDB.IEAI_DB_TYPE == 2)
                    {
                        sql = "SELECT NEXT VALUE FOR SEQ_SON_PROJECT AS COUNT FROM SYSIBM.SYSDUMMY1";
                    }

                    if (JudgeDB.IEAI_DB_TYPE != 3)
                    {
                        ps = conn.prepareStatement(sql);
                        rs = ps.executeQuery();
                        if (rs.next())
                        {
                            id = rs.getLong("COUNT");
                        }
                    } else
                    {
                        id = IdGenerator.createIdNoConnection("SEQ_SON_PROJECT", basicType);
                    }

                } catch (SQLException e)
                {
                    _log.error("getSequequSonProjec EXEC select SEQ_SON_PROJECT IS ERR" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getSequequSonProjec", _log);

                }
                break;
            } catch (RepositoryException e)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e1)
                {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                    throw new ServerException(ServerError.ERR_DB_QUERY);
                }
            }
        }
        return id;
    }
    /**
     *<li>Description:判断是否需要重新加载工程</li>**

     <AUTHOR> 2015年8月28日
      * @param prjName
     * @param uuid
     * @return return boolean true 需要重新加载 false 不需要重新加载
     * @throws RepositoryException
     */
    public boolean isRelaodProject ( String prjName, String uuid ) throws RepositoryException
    {

        PreparedStatement pres = null;
        ResultSet rs = null;
        String newUUID = "";
        Connection conn = null;
        boolean flag = false; //
        for (int i = 0;; i++)
        {
            try
            {
                try
                {
                    conn = DBResource.getJDBCConnection("isRelaodProject", _log, Constants.IEAI_IEAI_BASIC);
                    String sql = "SELECT  INAME,IUUID FROM ("
                            + " SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC"
                            + ")  WHERE ROWNUM=1 ";
                    if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
                    {
                        sql = "SELECT INAME,IUUID FROM ( SELECT  INAME,IUUID,row_number() over() AS ROWNUM FROM "
                                + "(SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC ) TD )  TT  where TT.ROWNUM = 1";
                    } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
                    {
                        sql = "SELECT INAME,IUUID FROM ( SELECT  INAME,IUUID FROM "
                                + "(SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC ) TD )  TT  LIMIT 1";
                    }
                    pres = conn.prepareStatement(sql);
                    pres.setString(1, prjName);
                    rs = pres.executeQuery();
                    while (rs.next())
                    {
                        newUUID = rs.getString("IUUID");
                    }
                    if (null != newUUID && !newUUID.equals(uuid))
                    {
                        flag = true;
                    }
                } catch (SQLException e)
                {
                    _log.error("isRelaodProject execute sql  err is :" + e);
                    throw new RepositoryException(ServerError.ERR_DB_UNKNOW);
                } finally
                {
                    DBResource.closeConn(conn, rs, pres, "isRelaodProject", _log);
                }
                return flag;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <li>Description:判断是否需要重新加载工程</li>
     *
     * <AUTHOR> 2015年8月28日
     * @param prjName
     * @param uuid
     * @return return boolean true 需要重新加载 false 不需要重新加载
     */
    public boolean isRelaodProject ( String prjName, String uuid, int dbType )
    {

        PreparedStatement pres = null;
        ResultSet rs = null;
        String newUUID = "";
        Connection conn = null;
        boolean flag = false; //
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(dbType);
            String sql = "SELECT  INAME,IUUID FROM ("
                    + " SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC"
                    + ")  WHERE ROWNUM=1 ";
            if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                sql = "SELECT INAME,IUUID FROM ( SELECT  INAME,IUUID,row_number() over() AS ROWNUM FROM "
                        + "(SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC ) TD )  TT  where TT.ROWNUM = 1";
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                sql = "SELECT INAME,IUUID FROM ( SELECT  INAME,IUUID FROM "
                        + "(SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC ) TD )  TT  LIMIT 1";
            }
            pres = conn.prepareStatement(sql);
            pres.setString(1, prjName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                newUUID = rs.getString("IUUID");
            }
            if (null != newUUID && !newUUID.equals(uuid))
            {
                flag = true;
            }
        } catch (DBException e)
        {
            _log.error("isRelaodProject getconnection err is :" + e);
        } catch (SQLException e)
        {
            _log.error("isRelaodProject execute sql  err is :" + e);
        } finally
        {
            DBResource.closeConn(conn, rs, pres, "isRelaodProject", _log);
        }
        return flag;
    }

    /**
     * <li>Description:判断是否需要重新加载工程</li>
     *
     * <AUTHOR> 2015年8月28日
     * @param prjName
     * @param uuid
     * @return return boolean true 需要重新加载 false 不需要重新加载
     */
    public boolean isRelaodProject ( String prjName, String uuid, Connection conn )
    {

        PreparedStatement pres = null;
        ResultSet rs = null;
        String newUUID = "";
        // Connection conn = null;
        boolean flag = false; //
        try
        {
            // conn = DBManager.getInstance().getJdbcConnection();
            String sql = "SELECT  INAME,IUUID FROM ("
                    + " SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC"
                    + ")  WHERE ROWNUM=1 ";
            if (JudgeDB.IEAI_DB_TYPE == JudgeDB.DB2)
            {
                sql = "SELECT INAME,IUUID FROM ( SELECT  INAME,IUUID,row_number() over() AS ROWNUM FROM "
                        + "(SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC ) TD )  TT  where TT.ROWNUM = 1";
            } else if (JudgeDB.IEAI_DB_TYPE == JudgeDB.MYSQL)
            {
                sql = "SELECT INAME,IUUID FROM ( SELECT  INAME,IUUID FROM "
                        + "(SELECT INAME,IUUID FROM IEAI_PROJECT WHERE INAME=? ORDER BY IUPLOADNUM DESC ) TD )  TT  LIMIT 1";
            }
            pres = conn.prepareStatement(sql);
            pres.setString(1, prjName);
            rs = pres.executeQuery();
            while (rs.next())
            {
                newUUID = rs.getString("IUUID");
            }
            if (null != newUUID && !newUUID.equals(uuid))
            {
                flag = true;
            }
        } catch (SQLException e)
        {
            _log.error("isRelaodProject execute sql  err is :" + e);
        } finally
        {
            DBResource.closePSRS(rs, pres, "isRelaodProject", _log);
        }
        return flag;
    }

    /**
     * <li>Description:获取当前数据库中集群组ID</li>
     *
     * <AUTHOR> 2015年9月17日
     * @param conn
     * @return
     * @throws ServerException return long
     */
    public long getGroupId ( Connection conn ) throws ServerException
    {
        long id = 0;
        List list = new ArrayList();
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    String sql = "SELECT GROUPID FROM IEAI_GROUPMESSAGE";
                    ps = conn.prepareStatement(sql);
                    rs = ps.executeQuery();
                    while (rs.next())
                    {
                        id = rs.getLong("GROUPID");
                        list.add(id);
                    }
                    if (list.size() > 1)
                    {
                        id = 0;
                    }

                } catch (SQLException e)
                {
                    _log.error("getGroupId EXEC SELECT GROUPID FROM IEAI_GROUPMESSAGET IS ERR" + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs, ps, "getGroupId", _log);
                }
                break;
            } catch (RepositoryException e)
            {
                try
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
                } catch (RepositoryException e1)
                {
                    // TODO Auto-generated catch block
                    e1.printStackTrace();
                    throw new ServerException(ServerError.ERR_DB_QUERY);
                }
            }
        }
        return id;
    }

    /**
     * <li>Description:</li>
     *
     * <AUTHOR> 2015年9月24日
     * @param flowIds
     * @param conn return void
     */
    public void removeAct ( List<Long> flowIds, Connection conn )
    {
        PreparedStatement pres1 = null;
        PreparedStatement pres2 = null;
        try
        {
            String sql1 = "DELETE FROM IEAI_SON_PROJECT WHERE IID=?";
            String sql2 = "DELETE FROM IEAI_FLOW_PARAM WHERE IID=?";
            pres1 = conn.prepareStatement(sql1);
            pres2 = conn.prepareStatement(sql2);
            int index = 0;
            for (Long flwoid : flowIds)
            {
                pres1.setLong(1, flwoid);
                pres2.setLong(1, flwoid);
                pres1.addBatch();
                pres2.addBatch();
                if (index % 3000 == 0)
                {
                    pres1.executeBatch();
                    pres2.executeBatch();
                }
                index++;
            }
            pres1.executeBatch();
            pres2.executeBatch();

        } catch (SQLException e)
        {
            _log.error("removeAct execute sql error is " + e);
        } finally
        {
            DBResource.closePreparedStatement(pres1, "removeAct", _log);
            DBResource.closePreparedStatement(pres2, "removeAct", _log);
        }
    }

    /**
     * <li>Description:判断是否是主流程</li>
     *
     * <AUTHOR> 2015年10月12日
     * @param prjName
     * @param flowName
     * @param con
     * @return return boolean
     * @throws SQLException
     */
    public static boolean isMainFlow ( String prjName, String flowName, Connection con ) throws SQLException
    {
        boolean isMain = false;
        for (int i = 0; i < 10; i++)
        {
            PreparedStatement ps = null;
            ResultSet rs = null;
            int runningFlowNum = 0;
            String sql = "select count(1) from IEAI_EXCELMODEL WHERE IMAINPRONAME =? AND  IMAINLINENAME=?";
            try
            {
                ps = con.prepareStatement(sql);
                ps.setString(1, prjName);
                ps.setString(2, flowName);
                rs = ps.executeQuery();
                if (rs.next())
                {
                    runningFlowNum = rs.getInt(1);
                }
                if (runningFlowNum != 0)
                {
                    isMain = true;
                }
                break;
            } catch (SQLException e)
            {
                throw new SQLException();
            } finally
            {
                try
                {
                    if (rs != null)
                    {
                        rs.close();
                        rs = null;
                    }
                    if (ps != null)
                    {
                        ps.close();
                        ps = null;
                    }
                } catch (Exception ex)
                {
                    _log.error("error while close jdbc connection", ex);
                }
            }
        }
        return isMain;
    }

    /**
     *
     * @Title: getNotInsertWorkFLowInstance
     * @Description: TODO(通过子系统名获取未结束的工作流名)
     * @param proName
     * @param con
     * @return
     * @return List<String> 返回类型
     * @throws @变更记录 2016年6月13日 yunpeng_zhang
     */
    public List<String> getNotInsertWorkFLowInstance ( String proName, Connection con )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<String> flowNameList = new ArrayList<String>();
        String sql = "SELECT TT.IFLOWNAME FROM IEAI_WORKFLOWINSTANCE  TT WHERE TT.ISTATUS IN ("
                + Constants.STATE_LISTENING + "," + Constants.STATE_RUNNING + "," + Constants.STATE_PAUSED + ","
                + Constants.STATE_RECOVERING + "," + Constants.STATE_FAIL + "," + Constants.STATE_FAIL_BUSSINESS + ","
                + Constants.STATE_QUEUEUP + "," + Constants.STATE_NEW_CREATE + ") AND TT.IPROJECTNAME = ?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, proName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                flowNameList.add(rs.getString("IFLOWNAME"));
            }

        } catch (SQLException e)
        {
            _log.error("method getNotInsertWorkFLowInstance of ExcelActUtil.class SQLException");

        } finally
        {
            DBResource.closePSRS(rs, ps, "getNotInsertWorkFLowInstance", _log);
        }
        return flowNameList;
    }

    /**
     *
     * @Title: getNotInsertWorkFLowInstance
     * @Description: TODO(通过子系统名获取未结束的工作流名)
     * @param proName
     * @param con
     * @return
     * @return List<String> 返回类型
     * @throws @变更记录 2016年6月13日 yunpeng_zhang
     */
    public int getInsertWorkFLowInstance ( String sysName, String flowName, String projectName, Connection con )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        String sql = "SELECT COUNT(TT.IFLOWID) AS COU FROM IEAI_WORKFLOWINSTANCE  TT WHERE  TT.IFLOWNAME=? AND TT.ISYSTEM=? AND IPROJECTNAME=?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, flowName);
            ps.setString(2, sysName);
            ps.setString(3, projectName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("COU");
            }

        } catch (SQLException e)
        {
            _log.error("method getInsertWorkFLowInstance of ExcelActUtil.class SQLException");

        } finally
        {
            DBResource.closePSRS(rs, ps, "getInsertWorkFLowInstance", _log);

        }
        return count;
    }

    /**
     *
     * @Title: getNotInsertWorkFLowInstance
     * @Description: TODO(通过子系统名获取未结束的工作流名)
     * @param proName
     * @param con
     * @return
     * @return List<String> 返回类型
     * @throws @变更记录 2016年6月13日 yunpeng_zhang
     */
    public int getInsertWorkFLowInstanceForStudio ( String flowName, String projectName, Connection con )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        String sql = "SELECT COUNT(TT.IFLOWID) AS COU FROM IEAI_WORKFLOWINSTANCE  TT WHERE  TT.IFLOWNAME=? AND IPROJECTNAME=?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, flowName);
            ps.setString(2, projectName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("COU");
            }

        } catch (SQLException e)
        {
            _log.error("method getInsertWorkFLowInstance of ExcelActUtil.class SQLException");

        } finally
        {
            DBResource.closePSRS(rs, ps, "getInsertWorkFLowInstance", _log);
        }
        return count;
    }

    /**
     *
     * @Title: getNotDayStartInsertWorkFLowInstance
     * @Description: TODO(通过子系统名获取未结束的工作流名)
     * @param proName
     * @param con
     * @return
     * @return List<String> 返回类型
     * @throws @变更记录 2016年10月20日 licheng_zhao
     */
    public int getDayStartInsertWorkFLowInstance ( String sysName, String flowName, String projectName, Connection con )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        String sql = "SELECT COUNT(TT.IFLOWID) AS COU FROM IEAI_WORKFLOWINSTANCE  TT WHERE  TT.IFLOWNAME=?  AND IPROJECTNAME=?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, flowName);
            ps.setString(2, projectName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("COU");
            }

        } catch (SQLException e)
        {
            _log.error("method getDayStartInsertWorkFLowInstance of ExcelActUtil.class SQLException");

        } finally
        {
            DBResource.closePSRS(rs, ps, "getDayStartInsertWorkFLowInstance", _log);
        }
        return count;
    }

    /**
     *
     * @Title: getNotInsertWorkFLowInstance
     * @Description: TODO(通过子系统名获取未结束的工作流名)
     * @param proName
     * @param con
     * @return
     * @return List<String> 返回类型
     * @throws @变更记录 2016年6月13日 yunpeng_zhang
     */
    public int getInsertIeaiExcelModle ( String sysName, String flowName, Connection con )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        String sql = "SELECT COUNT(TT.IFLOWID) AS COU FROM IEAI_WORKFLOWINSTANCE  TT WHERE  TT.IFLOWNAME=? AND TT.ISYSTEM=?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, flowName);
            ps.setString(2, sysName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("COU");
            }

        } catch (SQLException e)
        {
            _log.error("method getInsertWorkFLowInstance of ExcelActUtil.class SQLException");

        } finally
        {
            DBResource.closePSRS(rs, ps, "getInsertWorkFLowInstance", _log);

        }
        return count;
    }

    /* 删除工程时删除agent信息 */
    public void removeAgent ( String projectName, Connection conn )
    {
        PreparedStatement pres1 = null;
        try
        {
            String sql = "DELETE FROM ieai_project_agentinfo WHERE IPRJNAME=?";
            pres1 = conn.prepareStatement(sql);
            pres1.setString(1, projectName);
            pres1.addBatch();
            pres1.executeBatch();

        } catch (SQLException e)
        {
            _log.error("removeAgent execute sql error is " + e);
        } finally
        {
            DBResource.closePreparedStatement(pres1, "removeAgent", _log);
        }
    }

    /**
     * <li>Description:上传Excel活动信息组织</li>
     *
     * <AUTHOR> 2015年8月10日
     * @param object
     * @param user
     * @param conn return boolean
     * update 2015-08-18 解决上传Excel时 解析工作流重复问题
     * update 2015-08-21 zhangfengchun 修改方法返回类型
     * update 2015-09-17 zhangfengchun 修改上传Excel时获取服务器组信息
     * @throws RepositoryException
     */
    public boolean saveStartflowEndFlowUploadExcelEnd ( Object[] object, UserInfo user, Connection conn )
            throws RepositoryException
    {
        boolean flag = true;
        long startime = System.currentTimeMillis();
        _log.info("开始组织工程信息入库");
        long igroupid = 0;
        try
        {
            /* 查询当前数据库中的分组信息，如果有多个组返回 0.否则返回一个组的ID */
            igroupid = getGroupId(conn);
        } catch (ServerException e2)
        {
            igroupid = 0;
        }
        if (object.length == 2)
        {
            List mainList = (List) object[0];
            List childList = (List) object[1];

            List<ExcelProject> mainExcelList = new ArrayList<ExcelProject>(); // 主工程封装

            List dbNotMainProFlowlist = null;
            for (Object prjName : mainList)
            {
                if (igroupid == 0)
                {
                    igroupid = EngineRepository.getInstance().getGroupId(String.valueOf(prjName), conn);
                }

                List<ExcelFlows> mainProjectList = new ArrayList<ExcelFlows>(); // 存储主工程下的主工作流
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    dbNotMainProFlowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfoNotRun(projectName, conn);
                    // 从数据库中查找主工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getWorkflowInfo(projectName, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }

                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(String.valueOf(dbMianProFLowlist.get(i)));
                    // 查询工作流下的活动
                    List actList = null;
                    try
                    {
                        actList = EngineRepositotyJdbc.getInstance().getActNotMainInfo(projectName, flows.getFlowName(),
                                dbNotMainProFlowlist, conn);
                    } catch (RepositoryException e)
                    {
                        _log.error("获取主工作流下的活动 异常. 工程名为:" + projectName + " and mes:" + e.getMessage());
                    }
                    // 活动集合转换
                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    for (int a = 0; a < actList.size(); a++)
                    {
                        ActInfoBeanExcel actBean = (ActInfoBeanExcel) actList.get(a);
                        ExcelAct excelact = new ExcelAct();
                        excelact.setActName(actBean.getActName());
                        excelact.setActType(Constants.EXCEL_ACT_TYPE_CALLFLOW);
                        excelact.setActId(actBean.getActID());
                        _excelactlist.add(excelact);

                    }
                    flows.setActList(_excelactlist);

                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    mainProjectList.add(flows);
                }
                project.setFlows(mainProjectList);

                mainExcelList.add(project);

            }

            // 遍历子系统

            for (Object prjName : childList)
            {
                ExcelProject project = new ExcelProject();
                String projectName = String.valueOf(prjName);
                project.setPrjName(projectName);
                project.setUploadUser(user.getFullName());
                project.setUploadUserId(user.getId());
                project.setIgroupid(igroupid);
                List dbMianProFLowlist = null;
                try
                {
                    // 从数据库中查找子工程下的工作流
                    dbMianProFLowlist = EngineRepositotyJdbc.getInstance().getNotWorkflowInfoChild(projectName,
                            dbNotMainProFlowlist, conn);
                } catch (RepositoryException e1)
                {
                    _log.error("组织主流程信息失败  err is :" + e1);
                }
                List<ExcelFlows> sonProjectList = new ArrayList<ExcelFlows>(); // 存储子工程下的主工作流
                for (int i = 0; i < dbMianProFLowlist.size(); i++)
                {
                    ActInfoBeanExcel bean = (ActInfoBeanExcel) dbMianProFLowlist.get(i);
                    ExcelFlows flows = new ExcelFlows();
                    flows.setFlowName(bean.getActName());
                    flows.setFlowDesc(bean.getDescribe());
                    flows.setSystem(bean.getSystem());

                    List<ExcelAct> _excelactlist = new ArrayList<ExcelAct>();
                    if (bean.getFlag() == 2)
                    {
                        // 模板1 apt或shellcmd
                        _excelactlist = getFlowOne(bean);

                    } else
                    {
                        // 模板带文件检测 apt或shellcmd
                        _excelactlist = getFlowTwo(bean);
                    }
                    flows.setActList(_excelactlist);
                    List<ExcelFlowParams> _flowParamList = new ArrayList<ExcelFlowParams>();
                    List _paramList = WorkflowManager.getInstance().getFlowParamForExcelProject(projectName,
                            flows.getFlowName(), conn);
                    for (Object param : _paramList)
                    {
                        ExcelFlowParams parmas = new ExcelFlowParams();
                        parmas.setParamName(String.valueOf(param));
                        parmas.setParamOnly("false");
                        parmas.setParamDesc("请传输入8位数据日期。此参数为自动生成。");
                        parmas.setParamType("String");
                        _flowParamList.add(parmas);
                    }
                    flows.setFlowParamList(_flowParamList);
                    sonProjectList.add(flows);
                }
                project.setFlows(sonProjectList);

                mainExcelList.add(project);
            }

            // TODO 测试数据准备是否正确 输出到控制台 确认。 提交代码后进行删除 该段调试代码
            /*
             * for (ExcelProject pro : mainExcelList)
             * {
             * System.out.println(pro.getPrjName());
             * List<ExcelFlows> listf = pro.getFlows();
             * for (ExcelFlows flow : listf)
             * {
             * System.out.println( pro.getPrjName()+"=="+"\t" + flow.getFlowName());
             * List<ExcelFlowParams> listp = flow.getFlowParamList();
             * for (ExcelFlowParams act : listp)
             * {
             * System.out.println("参数" + "\t\t" + act.getParamName() + "==="
             * + act.getParamDesc() + "==" + act.getParamType());
             * }
             * List<ExcelAct> lista = flow.getActList();
             * for (ExcelAct act : lista)
             * {
             * System.out.println("活动" + "\t\t\t" + act.getActId() + "==="
             * + act.getActName() + "==" + act.getActType() + "=="
             * + act.getActsuccid());
             * }
             *
             * }
             * }
             */
            long endTime = System.currentTimeMillis();
            _log.info("组织工程信息耗时：" + (endTime - startime) + "毫秒");
            try
            {
                long updatStateTime = System.currentTimeMillis();
                flag = saveStartFlowEndFlowExcelProject(conn, mainExcelList);
                long updateendTime = System.currentTimeMillis();
                _log.info("工程信息入库共耗时：" + (updateendTime - updatStateTime) + "毫秒");
            } catch (RepositoryException e)
            {
                flag = false;
                _log.error("组织工程信息入库过程中出现错误 异常信息为:" + e);
            }
        }
        return flag;
    }

    /**
     * <li>Description:替换shellcmd中 YYYYMMMDD 参数 数据日期</li> 
     * <AUTHOR>
     * 2015年10月13日 
     * @param cmd
     * @param flowid
     * @return
     * @throws ServerException
     * return String
     */
    public  String replaceYYYYMMDD (String cmd,long flowid ) throws  ServerException
    {
        if (null != cmd && !"".equals(cmd))
        {
            String insName = ExcelActUtil.getInstance().getFlowInsNme(flowid);
            if (null == insName || "".equals(insName))
            {
                _log.error("flowID: "+flowid + " 获取数据日期失败 ：-90909 ");
                throw new ServerException(-90909);
            }
            //cmd 是否包含%日期
            boolean statusCmd = cmd.contains("%");

            if(statusCmd){
                Map returnMap = new HashMap();
                returnMap=   replaceInsName( cmd,insName);
                cmd=(String) returnMap.get("cmd");
                insName=(String) returnMap.get("insName");
            }
            boolean status = cmd.contains(Constants.SHELLCMD_YYYY_MM_DD);
            if(status){
                String insNameYmd="";
                if(insName.length()==8){
                    insNameYmd=   insName.substring(0,4)+"-"+insName.substring(4,6)+"-"+insName.substring(6,8);

                }else {
                    insNameYmd=insName;
                }
                cmd = cmd.replaceAll(Constants.SHELLCMD_YYYY_MM_DD, insNameYmd == null ? "" : insNameYmd);
            }else {
                cmd = cmd.replaceAll(Constants.SHELLCMD_YYYYMMDD, insName == null ? "" : insName);
            }
        }
        return cmd;
    }

    /**
     * <li>Description:获取工作流数据日期</li> 
     * <AUTHOR>
     * 2015年8月13日 
     * @param flowId
     * @return
     * return String
     * update 2015-09-24 fengchun_zhang 查询数据日期时 IEAI_WORKFLOWINSTANCE 未查到时 去周期表查
     */
    public String getFlowInsNme ( long flowId )
    {
        PreparedStatement pres = null;
        ResultSet rs = null;
        String lastline="";
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            String sql = "select IFLOWINSNAME from IEAI_WORKFLOWINSTANCE where IFLOWID=?";
            pres = conn.prepareStatement(sql);
            pres.setLong(1, flowId);
            rs = pres.executeQuery();
            while (rs.next())
            {
                lastline=rs.getString("IFLOWINSNAME");
            }
            if ("".equals(lastline))
            {

                String sql_CICLE = "select IFLOWINSNAME from IEAI_WORKFLOWCICLE where IFLOWID=?";
                pres = conn.prepareStatement(sql_CICLE);
                pres.setLong(1, flowId);
                rs = pres.executeQuery();
                while (rs.next())
                {
                    lastline = rs.getString("IFLOWINSNAME");
                }
            }

        } catch (DBException e)
        {
            _log.error("getFlowInsNme getconnection err is :"+e);
        } catch (SQLException e)
        {
            _log.error("getFlowInsNme execute sql  err is :"+e);
        } finally
        {
            try
            {
                DBResource.closeConn(conn, rs, pres, "getFlowInsNme", _log);
            } catch (Exception e)
            {
                _log.error("getFlowInsNme  err is :"+e);
            }
        }
        return lastline;
    }


    public Map<String,Long> removeExcelWithTableNew(String projectName, Connection conn,int listrepFlow)
            throws RepositoryException {
        Map<String,Long> allActMap = new HashMap<>();
        PreparedStatement presmodeal = null;
        PreparedStatement presmodelcopy = null;
        PreparedStatement pressucc = null;
        PreparedStatement prespre = null;
        PreparedStatement presSuccCopy = null;
        PreparedStatement presPreCopy = null;
        PreparedStatement presdel = null;

        PreparedStatement ps = null;
        ResultSet rs = null;

        String sqlsucc="";
        String sqlppre="";
        String sqlmodelCopy="";
        String sqlsuccCopy="";
        String sqlppreCopy="";
        String sqlmodel="";
        String sqldeloption="";
        String querySql = "";

        try {

            boolean isExcelProject = com.ideal.ieai.server.repository.project.ProjectManager.getInstance()
                    .isPkgExistExcel(projectName);
            if (isExcelProject) {
                querySql = "select ie.ioperationid,ie.imainproname,ie.imainlinename,ie.iactname from ieai_excelmodel ie left join ieai_excelmodel_copy iec on ie.IOPERATIONID = iec.IOPERATIONID"
                        + " where ie.IMAINPRONAME=? ";
                ps = conn.prepareStatement(querySql);
                ps.setString(1, projectName);
                rs = ps.executeQuery();
                while(rs.next()){
                    allActMap.put(rs.getString("imainproname").trim()+rs.getString("imainlinename").trim()+rs.getString("iactname").trim(), rs.getLong("ioperationid"));
                }

                sqlsuccCopy = "DELETE FROM IEAI_ACTSUCC_COPY WHERE ISELFOPERATIONID in(select IOPERATIONID from IEAI_EXCELMODEL WHERE IMAINPRONAME=?) ";
                presSuccCopy = conn.prepareStatement(sqlsuccCopy);
                presSuccCopy.setString(1, projectName);
                presSuccCopy.execute();

                sqlppreCopy = "DELETE FROM IEAI_ACTSUCC_COPY WHERE IPROJECTNAME = ? ";
                presPreCopy = conn.prepareStatement(sqlppreCopy);
                presPreCopy.setString(1, projectName);
                presPreCopy.execute();

                sqlmodelCopy = "DELETE FROM IEAI_ACTPRE_COPY WHERE IPROJECTNAME = ? ";
                presmodelcopy = conn.prepareStatement(sqlmodelCopy);
                presmodelcopy.setString(1, projectName);
                presmodelcopy.execute();

                sqldeloption = "DELETE FROM  IEAI_DELOPTIONID WHERE  IMAINPRONAME=?";
                presdel = conn.prepareStatement(sqldeloption);
                presdel.setString(1, projectName);
                presdel.execute();

                if(listrepFlow == 0){
                    sqlsucc = "DELETE FROM IEAI_ACTSUCC WHERE IPROJECTNAME = ? ";
                    pressucc = conn.prepareStatement(sqlsucc);
                    pressucc.setString(1, projectName);
                    pressucc.execute();

                    sqlppre = "DELETE FROM IEAI_ACTPRE WHERE IPROJECTNAME = ? ";
                    prespre = conn.prepareStatement(sqlppre);
                    prespre.setString(1, projectName);
                    prespre.execute();

                    sqlmodel= "DELETE FROM  IEAI_EXCELMODEL WHERE  IMAINPRONAME=? ";
                    presmodeal = conn.prepareStatement(sqlmodel);
                    presmodeal.setString(1, projectName);
                    presmodeal.execute();
                }
            }
        } catch (SQLException e) {
            allActMap.clear();
            _log.info("removeExcelWithTableNew",e);
        } finally {
            DBResource.closePreparedStatement(presmodeal, "removeExcelModeal", _log);
            DBResource.closePreparedStatement(presmodelcopy, "removeExcelModeal", _log);
            DBResource.closePreparedStatement(pressucc, "removeExcelModeal", _log);
            DBResource.closePreparedStatement(prespre, "removeExcelModeal", _log);
            DBResource.closePreparedStatement(presSuccCopy, "removeExcelModeal", _log);
            DBResource.closePreparedStatement(presPreCopy, "removeExcelModeal", _log);
            DBResource.closePreparedStatement(presdel, "removeExcelModeal", _log);
            DBResource.closePSRS(rs, ps, "removeExcelModeal", _log);
        }
        return allActMap;
    }
}
