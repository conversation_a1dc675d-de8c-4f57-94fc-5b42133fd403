/*******************************************************************************
 * ODS活动监控
 ******************************************************************************/
var oam_alarm_store = null;
var oam_home_store = null;
var oam_alarmPanel;
var oam_dataNum_date;
var oam_autoFreshObject;
var oam_autoFreshObject_alarm;
var oam_act_form;
var oam_alarm_fresh_form;
var oam_pageBar_home;
var queryForm;
var home_grid = null;
Ext.onReady(function() {
//	$("body").unbind();
			var itemsPerPage = 20;
			
			var selModel = null;
			var selHomeModel = null;
			var combo = null;
			var home_grid = null;
			var home_state;
			var jobPanel;
			var zixitongming="工程名称";
			var hidekey=true;
			if(jobSchedulingQuerySystemSwitch){
				zixitongming="工程名";
			}
			if(odsYiLianFDSwitch){
				hidekey=false;
			}else {
				hidekey=true;
			}



			oam_act_form = Ext.create('Ext.form.Panel', {
				frame : true,
				border : false,
				bodyCls:'fm-spinner',
				//baseCls:'customize_gray_back',
				layout : {
					type : 'hbox',
					align : 'middle'
				},
				defaults : {
					anchor : '100%'
				},
				items : [
						{
							fieldLabel : '自动刷新',
							xtype : 'checkbox',
							name : 'isAutoRefresh',
							id : 'isAutoRefresh',
							padding : '0 5 0 0',
							checked : true,
							labelWidth : 60,
							handler : function() {
								refreshClick(this.value)
							}
						}, {
							fieldLabel : '  ',
							hideLabel : true,
							labelWidth : 60,
							width : 50,
							name : 'refreshTime',
							padding : '0 5 0 0',
							id : 'refreshTime',
							value : '1',
							editable : false,
							xtype : 'numberfield',
							allowDecimals : true,
							step : 1,
							minValue : 1,
							maxValue : 10
						}, {
							xtype : 'label',
							forId : 'myFieldId',
							text : '分',
							padding : '0 10 0 0'
						} ]
			});

			// alarm自动刷新区域
			oam_alarm_fresh_form = Ext.create('Ext.form.Panel', {
				frame : true,
				border : false,
				bodyCls:'fm-spinner',
				layout : {
					type : 'hbox',
					align : 'middle'
				},
				defaults : {
					anchor : '100%'
				},
				items : [
						{
							fieldLabel : '自动刷新',
							xtype : 'checkbox',
							name : 'isAutoRefresh_alarm',
							id : 'isAutoRefresh_alarm',
							padding : '0 10 0 0',
							checked : true,
							labelWidth : 60,
							handler : function() {
								if (!oam_alarm_fresh_form.getForm().isValid()) {
									Ext.Msg.alert('提示', "自动刷新时间不符合要求!");
									return;
								}
								if (this.value == true) {
									var refreshTime = oam_alarm_fresh_form
											.queryById('refreshTime_alarm')
											.getValue();
									autoFresh_alarm(refreshTime);
								} else {
									stopAutoFresh_alarm();
								}
							}
						}, {
							fieldLabel : '  ',
							hideLabel : true,
							labelWidth : 60,
							width : 50,
							name : 'refreshTime_alarm',
							padding : '0 10 0 0',
							id : 'refreshTime_alarm',
							value : '1',
							editable : false,
							xtype : 'numberfield',
							allowDecimals : true,
							step : 1,
							minValue : 1,
							maxValue : 10
						}, {
							xtype : 'label',
							forId : 'myFieldId_alarm',
							text : '分',
							padding : '0 10 0 0'
						} ]
			});


			// 所属系统 数据源
			Ext.define('systemModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'isystemId',
					type : 'string'
				} ]
			});
			var cstore_isystem_url = 'querySystemInfo.do';
			if(jobSchedulingQuerySystemSwitch){
				cstore_isystem_url = 'jzQuerySystemInfo.do';
			}
			var cstore_isystem = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'systemModel',
				proxy : {
					type : 'ajax',
					url : cstore_isystem_url,
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			if (OdsYLHDJKGLSwitch){
				var combobox_isystem = Ext.create('Ext.form.field.ComboBox', {
					fieldLabel : '所属系统',
					displayField : 'isystemId',
					valueField : 'isystemId',
					emptyText : '--请选择所属系统--',
					name : 'isystem',
					labelWidth : 65,
					labelAlign: 'right',
					width : contentPanel.getWidth()*0.333,
					editable : true,
					store : cstore_isystem,
					queryMode : 'local',
					listeners : {
						select : function ()
						{
							Ext.getCmp("iprjName").clearValue();
							var inSystemName=this.getRawValue();
							// 参数信息列表grid重新加载
							cstore_iprjName.load(
								{
									params :
										{
											inSystemName : inSystemName
										}
								});
						},
						beforequery : function(e) {
							var combo = e.combo;
							if (!e.forceAll) {
								var reg = /^[^\%]*$/;
								var value = e.query;
								if (value.match(reg)) {
									combo.store.filterBy(function(record, id) {
										var text = record.get(combo.displayField);
										if (text.toLowerCase() == value
											.toLowerCase()) {
											return true;
										} else {
											return false;
										}
									});
								} else {
									value = value.replace("%", "");
									if (!(value.match(reg))) {
										value = value.replace("%", "");
									}
									combo.store.filterBy(function(record, id) {
										var text = record.get(combo.displayField);
										return (text.toLowerCase().indexOf(
											value.toLowerCase()) != -1);
									});
								}
								combo.expand();
								return false;
							}
						}
					}
				});

			}else {
				var combobox_isystem = Ext.create('Ext.form.field.ComboBox', {
					fieldLabel : '所属系统',
					displayField : 'isystemId',
					valueField : 'isystemId',
					emptyText : '--请选择所属系统--',
					name : 'isystem',
					labelWidth : 65,
					labelAlign: 'right',
					width : contentPanel.getWidth()*0.333,
					editable : true,
					typeAhead : true,
					store : cstore_isystem,
					queryMode : 'local',
					listeners : {
						beforequery : function(e) {
							var combo = e.combo;
						if (!e.forceAll)
						{
								var value = e.query;
									combo.store.filterBy(function(record, id) {
										var text = record.get(combo.displayField);
								return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
									});
								combo.expand();
								return false;
							}
						}
					}
				});

			}
	var cstore_labelWidth =  65;
	if(jobSchedulingQuerySystemSwitch){
		cstore_labelWidth = 71;
	}
			if(jobSchedulingQuerySystemSwitch){
				combobox_isystem = Ext.create('Ext.form.field.ComboBox', {
					fieldLabel : '所属系统',
					displayField : 'isystemId',
					emptyText : '--请选择所属系统--',
					valueField : 'isystemId',
					name : 'isystem',
					labelWidth : 65,
	                typeAhead : true,
					labelAlign: 'right',
					width : contentPanel.getWidth()*0.33,
					editable : true,
					store : cstore_isystem,
					queryMode : 'local',
					listeners :
				    {
						select : function() {
							Ext.getCmp("iprjName").clearValue();
							var sysName = this.value;
							// 参数信息列表grid重新加载
							cstore_iprjName.load({
									params : {
										inSystemName : sysName
									}
							});
						},
						beforequery : function (e)
				        {
					        var combo = e.combo;
					        if (!e.forceAll)
					        {
						        var value = e.query;
						        combo.store.filterBy (function (record, id)
						        {
							        var text = record.get (combo.displayField);
							        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
						        });
						        combo.expand ();
						        return false;
					        }
				        }
				    }
				});
			}

			var cstore_istatus;
			if(isJHSwitch){
				 cstore_istatus = new Ext.data.ArrayStore({
						fields : [ 'id', 'name' ],
						data : [ [ 'All', '全部' ], [ 'Running', '运行' ],
								[ 'QueueUp', '排队' ], [ 'Fail', '通信异常' ],
								[ 'Continue', '继续' ], [ 'HangUp', '挂起' ],
								[ 'Fail:Business', '作业异常' ],[ 'ManualRunning', '异常处理中' ] ]
					});

			}else{
				cstore_istatus = new Ext.data.ArrayStore({
					fields : [ 'id', 'name' ],
					data : [ [ 'All', '全部' ], [ 'Running', '运行' ],
						[ 'QueueUp', '排队' ], [ 'Fail', '失败' ],
						[ 'Continue', '继续' ], [ 'HangUp', '挂起' ],
						[ 'Fail:Business', '失败：业务异常' ],[ 'ManualRunning', '异常处理中' ] ]
				});
			}


			// 创建Combobox
			var combobox_istatus = new Ext.form.ComboBox({
				fieldLabel : '状态',
				name : 'istatus',
				store : cstore_istatus,
				emptyText : '--请选择状态--',
				value:v_act_state,
                labelWidth : cstore_labelWidth,
                width : contentPanel.getWidth()*0.331,
				displayField : 'name',
				valueField : 'id',
				triggerAction : 'all',
				mode : 'local',
				labelAlign: 'right'
			});

			oam_dataNum_date = Ext.create('Ext.form.field.Date', {
				labelWidth : cstore_labelWidth,
				width : contentPanel.getWidth()*0.331,
//                margin:'0 0 5 0',
//                padding : '10 10 0 0',
                emptyText : '--请选择数据日期--',
				name : 'idataNum',
				value:v_insName,
				fieldLabel : '数据日期',
				format : 'Ymd',
				labelAlign: 'right'
			});
			// 子系统名
			Ext.define('prjNameModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'iprjname',
					type : 'string'
				}, {
					name : 'iprjid',
					type : 'string'
				} ]
			});
			var cstore_iprjName = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'prjNameModel',
				proxy : {
					type : 'ajax',
					url : 'queryPrjInfo.do',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
	var prj_Name = Ext.create('Ext.form.field.ComboBox', {
		fieldLabel : zixitongming,
		displayField : 'iprjname',
		emptyText : '--请选择工程名称--',
		valueField : 'iprjid',
		id : 'iprjName',
		name : 'iprjName',
		labelWidth : 65,
		width : contentPanel.getWidth()*0.333,
		editable : true,
		typeAhead : true,
		store : cstore_iprjName,
		queryMode : 'local',
		labelAlign: 'right',
		listeners : {
			beforequery : function (e)
			{
				var combo = e.combo;
				if (!e.forceAll)
				{
					var value = e.query;
					combo.store.filterBy (function (record, id)
					{
						var text = record.get (combo.displayField);
						return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					});
					combo.expand ();
					return false;
				}
			},   select: function() {
				isFirstTimeFlag=false;
			}
		}
	});

			if(jobSchedulingQuerySystemSwitch){
				prj_Name = Ext.create('Ext.form.field.ComboBox', {
					fieldLabel : zixitongming,
					displayField : 'iprjname',
					emptyText : '--请选择'+zixitongming+'--',
					valueField : 'iprjid',
					id : 'iprjName',
					name : 'iprjName',
					labelWidth : 65,
					width : contentPanel.getWidth()*0.33,
					editable : true,
					typeAhead : true,
					store : cstore_iprjName,
					queryMode : 'local',
					labelAlign: 'right',
					listeners :
				    {
						beforequery : function (e)
				        {
					        var combo = e.combo;
					        if (!e.forceAll)
					        {
						        var value = e.query;
						        combo.store.filterBy (function (record, id)
						        {
							        var text = record.get (combo.displayField);
							        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
						        });
						        combo.expand ();
						        return false;
					        }
				        }
				    }
				});
			}
			
			if(null!=v_filterPrjName){
				prj_Name.select(v_filterPrjName);
			}
			
			// 活动类型数据源
			var cstore_iacttype = new Ext.data.ArrayStore({
				fields : [ 'name', 'value' ],
				data : [ 
				         [ 'FTP Upload File', '数据传输' ], [ 'StoreProcedure', '数据加工' ],
						[ 'new unloaddata', '数据卸载' ], [ 'loaddata', '数据装载' ]
					   ]
			});
			// 创建Combobox
			var combobox_iacttype = new Ext.form.ComboBox({
				fieldLabel : '活动类型',
				name : 'iacttype',
				store : cstore_iacttype,
				//value:v_act_state,
				labelWidth : cstore_labelWidth,
				width : contentPanel.getWidth()*0.331,
                hidden : true,

				displayField : 'value',
				valueField : 'name',
				triggerAction : 'all',
				mode : 'local',
				labelAlign: 'right' 
			});
			
			var job_Name = Ext.create('Ext.form.field.Text', {
                labelWidth : 65,
                width : contentPanel.getWidth()*0.33, 
                emptyText : '--请输入作业名--',
				name : 'ijobName',
				fieldLabel : '作业名',
				labelAlign: 'right'
			});
			if(jobSchedulingQuerySystemSwitch){
				job_Name = Ext.create('Ext.form.field.Text', {
					labelWidth : 65,
	                width : contentPanel.getWidth()*0.33, 
					name : 'ijobName',
					fieldLabel : '作业名',
					emptyText : '--请输入作业名--',
					labelAlign: 'right'
				});
			}
			var isDispay = Ext.create('Ext.form.field.Checkbox', {
                labelWidth : 65,
                width : contentPanel.getWidth()*0.19,
                margin : '0 26 0 6',
				name : 'isDis',
				checked:true,
				fieldLabel : '隐藏活动'
			});
			var isKeyFlow = Ext.create('Ext.form.field.Checkbox', {
                labelWidth : 65,
                width : 75,
                padding : '10 10 0 20',
				name : 'isKeyFlow',
				value:v_keyFlow,
				hidden:true,
				fieldLabel : '关键流程'
			});
			home_state = Ext.create('Ext.Button', {
				text : '',
				margin : '5 0 0 -20'
			});
			var clera_btn = Ext.create('Ext.Button', {
				margin :'0 0 0 5',
				text : '清空',
				// icon: 'images/delete.png',
				//baseCls:'Common_Btn',
				listeners : {
					click : function() {
						Ext.getCmp('queryform').getForm().findField("iprjName")
								.setValue('');
						Ext.getCmp('queryform').getForm().findField("isystem")
								.setValue('');
						Ext.getCmp('queryform').getForm().findField("ijobName")
								.setValue('');
						Ext.getCmp('queryform').getForm().findField("isDis")
								.setValue('');
						Ext.getCmp('queryform').getForm().findField("idataNum")
								.setValue('');
						Ext.getCmp('queryform').getForm().findField("istatus")
								.setValue('');
						Ext.getCmp('queryform').getForm().findField("isKeyFlow")
						.setValue('');
						Ext.getCmp('queryform').getForm().findField("iacttype")
						.setValue('');
					}
				}
			});
			var search_btn = Ext.create('Ext.Button', {
				text : '查询',
				//margin :'0 0 0 5',
				//baseCls:'Common_Btn',
				// icon: 'images/search.png',
				handler : function() {
					search();
				}
			});

			var batchTryAgain_btn = Ext.create('Ext.Button', {
				text : '批量重试',
//				iconCls : 'monitor_execute2',
				baseCls : 'Common_Btn',
				handler : function() {
					if(jobscDoubleCheck=='true'){
		        		//flowDoubleCheck(1);
						doubleCheckBatch('1');
		        	}else{
		        		batchOperationTask('1');
		        	}
				}
			});

			var batchIgnore_btn = Ext.create('Ext.Button', {
				text : '批量略过',
//				iconCls : 'monitor_skip2',
				baseCls : 'Common_Btn',
				handler : function() {
					if(jobscDoubleCheck=='true'){
		        		//flowDoubleCheck(2);
						doubleCheckBatch('2');
		        	}else{
		        		batchOperationTask('2');
		        	}
				}
			});

			if(jobSchedulingQuerySystemSwitch){
				queryForm = Ext.create('Ext.ux.ideal.form.Panel',
						{
							id : 'queryform',
							border : false,
							region : 'north',
							iqueryFun : search,
							iselect : false,
							collapsible : true,// 可收缩
							collapsed : true,// 默认收缩
							titleCollapse : true,
							//baseCls:'customize_gray_back',
				            bodyPadding : 5,
				            layout : 'form',
				            items : [
					                    {
					                        layout : 'column',
					                        border : false,
					    					items : [ combobox_isystem,
														combobox_istatus]
					                    },{
					                        layout : 'column',
					                        border : false,
					    					items : [ prj_Name,
														oam_dataNum_date]
					                    },{
					                        layout : 'column',
					                        border : false,
					    					items : [ job_Name,combobox_iacttype, isDispay,isKeyFlow,
														search_btn ,clera_btn ]
					                    }
					            ]
						});
			}else{
				queryForm = Ext.create('Ext.ux.ideal.form.Panel',
						{
							id : 'queryform',
							border : false,
							region : 'north',
							iqueryFun : search,
							collapsible : true,// 可收缩
							collapsed : true,// 默认收缩
							//baseCls:'customize_gray_back',
				            bodyPadding : 5,
				            layout : 'form',
				            items : [
					                    {
					                        layout : 'column',
					                        border : false,
					    					items : [ combobox_isystem,
														combobox_istatus,oam_dataNum_date]
					                    },{
					                        layout : 'column',
					                        border : false,
					    					items : [ prj_Name,job_Name, isDispay,isKeyFlow,
														search_btn ,clera_btn ]
					                    }
					            ]
						});
			}
			// 报警
			 var aram_grid_columns = [ {
				 text : '序号',
				 xtype : 'rownumberer',
				 align:'left',
				 width : 40
				 }, {
				 text : 'ID',
				 dataIndex : 'id',
				 hidden : true
				 }, {
			     text : zixitongming,
				 dataIndex : 'prjName'
				 }, {
				 text : '工作流名',
				 dataIndex : 'flowName'
				 }, {
				 text : '作业名称',
				 dataIndex : 'actName',
				 width : 290,
				 locked : true
				 }, {
				 text : '报警类型',
				 dataIndex : 'type',
				 renderer : function(value, cellmeta, record) {
				 var v = getType(value);
				 return v;
				 }
				 }, {
				 text : '报警时间',
				 dataIndex : 'warnTime',
				 width : 160
				 }, {
				 text : '报警描述',
				 dataIndex : 'warnDes',
				 flex : 1
				 }, {
				 text : '报警处理',
				 dataIndex : 'oper'
				 } ];
	
			 Ext.define('aramData', {
				 extend : 'Ext.data.Model',
				 fields : [ {
						 name : 'id',
						 type : 'string'
					 }, {
						 name : 'prjName',
						 type : 'string'
					 }, {
						 name : 'flowName',
						 type : 'string'
					 }, {
						 name : 'actName',
						 type : 'string'
					 }, {
						 name : 'type',
						 type : 'string'
					 }, {
						 name : 'warnTime',
						 type : 'string'
					 }, {
						 name : 'warnDes',
						 type : 'string'
					 }, {
						 name : 'oper',
						 type : 'string'
					 }
				 ]
			 });
			 oam_alarm_store = Ext.create('Ext.data.Store', {
				 autoLoad : false,
				 model : 'aramData',
				 pageSize : 8,
				 proxy : {
					 type : 'ajax',
					 url : 'queryAlarmList.do',
					 reader : {
						 type : 'json',
						 root : 'dataList',
						 totalProperty : 'total'
					 }
				 	}
			 });
			var batckDealAlarmbutn = Ext.create('Ext.Button', {
				text : '批量处理',
//				iconCls : 'batch_process',
				baseCls:'Common_Btn',
				handler : function() {
					actMonitorClbj();
				}
			});
			selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true,
				selType : 'cellmodel'
			});
			selHomeModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true,
				selType : 'cellmodel',
				renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
					if (!bohaiBank) {
						if (record.data.status.indexOf("失败")>0 ||
							record.data.status.indexOf("异常")>0 ||
							(record.data.status == "就绪" || record.data.status.indexOf("就绪")>0) ) {
							return '<div class="x-grid-row-checker">';
						}else {
							return  "";
						}
					}else {
						return '<div class="x-grid-row-checker">';
					}

				}
			});
			 var alarm_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
				 store : oam_alarm_store,
				 columnLines : true,
				 selModel : selModel,
				 columns : aram_grid_columns,
				 id : 'alarm_grid',
				 height : 251,
				 padding : grid_space,
				 cls:'customize_panel_back',
				 ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',     
				 viewConfig : {
					 stripeRows : true,// 在表格中显示斑马线
					 enableTextSelection : true
					// 可以复制单元格文字
				 },

				 ipageItems : [ 
						batckDealAlarmbutn, 
						'-', 
						oam_alarm_fresh_form 
					]
//				 dockedItems : [ {
//				 id : 'pagingbar_alarm',
//				 xtype : 'pagingtoolbar',
//				 store : oam_alarm_store,
//				 dock : 'bottom',
//				 displayInfo : true,
//				 autoScroll : true,
//				 items : [ '-', '&nbsp&nbsp&nbsp&nbsp&nbsp',
//				 batckDealAlarmbutn, '&nbsp&nbsp&nbsp&nbsp&nbsp',
//				 '-', oam_alarm_fresh_form ]
//				} ]
			 });
			// 报警信息结束
			// 首页作业信息数据源Model
			Ext.define('homeData', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'taskId',
					type : 'string'
				}, {
					name : 'checkstr',
					type : 'string'
				}, {
					name : 'flowid',
					type : 'string'
				}, {
					name : 'status',
					type : 'string'
				}, {
					name : 'jobName',
					type : 'string'
				}, {
					name : 'prjName',
					type : 'string'
				}, {
					name : 'flowName',
					type : 'string'
				}, {
					name : 'flowName2',
					type : 'string'
				}, {
					name : 'system',
					type : 'string'
				}, {
					name : 'dataNum',
					type : 'string'
				}, {
					name : 'pdsj',
					type : 'string'
				}, {
					name : 'kssj',
					type : 'string'
				}, {
					name : 'yxsj',
					type : 'string'
				}, {
					name : 'user',
					type : 'string'
				}, {
					name : 'agentIp',
					type : 'string'
				}, {
					name : 'agentPort',
					type : 'string'
				}, {
					name : 'shellPath',
					type : 'string'
				}, {
					name : 'agenthostname',
					type : 'string'
				}, {
					name : 'yl',
					type : 'string'
				}, {
					name : 'cf',
					type : 'string'
				}, {
					name : 'his',
					type : 'string'
				}, {
					name : 'mainLineName',
					type : 'string'
				}, {
					name : 'i_taskExceLevel',
					type : 'string'
				}, {
					name : 'i_actStates',
					type : 'string'
				}, {
					name : 'i_changeColor',
					type : 'string'
				}, {
					name : 'i_showColor',
					type : 'string'
				}
				,
				{
					name : 'pic',
					type : 'string'
				},{
						name : 'topoPic',
						type : 'string'
				},
				{
					name : 'beginNextTime',
					type : 'string'
				},{
					name : 'yesterdayBeginNextTime',
					type : 'string'
				},{
					name : 'toDayPlanBeginTime',
					type : 'string'
				},{
					name : 'toDayPlanEndTime',
					type : 'string'
				},{
					name : 'iactDesc',
					type : 'string'
				},{
						name : 'isDayStart',
						type : 'string'
					}
				]
			});
			// 首页作业信息数据源
			oam_home_store = Ext.create('Ext.data.Store', {
				autoLoad : false,
				model : 'homeData',
				pageSize : itemsPerPage,
				proxy : {
					type : 'ajax',
					timeout : 1800000,
					url : 'queryHomeData.do?action=queryHomeData',
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			oam_home_store.on('beforeload', function(s) {
				var params = s.getProxy().extraParams;
				var isystem = Ext.getCmp('queryform').getForm().findField(
						"isystem").getValue();
				var istatus = Ext.getCmp('queryform').getForm().findField(
						"istatus").getValue();
				var iprjName = Ext.getCmp('queryform').getForm().findField(
						"iprjName").getRawValue();
				var iprjid = Ext.getCmp('queryform').getForm().findField(
						"iprjName").getValue();
				if(jobSchedulingQuerySystemSwitch){
					var iacttype = Ext.getCmp('queryform').getForm().findField("iacttype")
					.getValue();
				}
				if(isFirstTimeFlag){
					iprjid=prjId;
				}
				if (iprjName == '' || iprjName == null || iprjName == undefined
						|| iprjName == 'undefined' || iprjName == 'null') {
					iprjName = '';
				}
				var ijobName = Ext.getCmp('queryform').getForm().findField(
						"ijobName").getValue();
				var isDis = Ext.getCmp('queryform').getForm()
						.findField("isDis").getValue();
				var idataNum = Ext.util.Format.date(oam_dataNum_date.getValue(),
						'Ymd');
				var b_keyflow =Ext.getCmp('queryform').getForm().findField("isKeyFlow").getValue();
				
				if(fromFlag=='1'){
					if(iprjName==''){
						iprjName=v_filterPrjName;
					}
					if(ijobName==''){
						ijobName=v_filterActName;
					}
					if(idataNum==''){
						idataNum = v_insName;
					}
				}
				
				
				Ext.apply(params, {
					system : isystem,
					status : istatus,
					dataNum : idataNum,
					prjName : iprjName,
					prjId : iprjid,
					jobName : ijobName,
					actType : iacttype,
					dis : !isDis,//是否显示，不勾选才显示
					keyflow:b_keyflow ? 1 : 0
				});
			});
			oam_home_store.on('load', function(r, options, success) {
				var reader = oam_home_store.getProxy().getReader();
				if (success) {
					if (null != reader.jsonData.msg) {
						home_state.setText(reader.jsonData.msg, true);
					}
				} else {
					Ext.Msg.alert('提示', "超时!");
				}
			});

			// 首页作业信息数列模式{text: '序号', xtype: 'rownumberer', width: 40
			var job_list_columns = [
					{
						text : '序号',
						xtype : 'rownumberer',
						align : 'left',
						width : 50
					},
					// {
					// 	text : '<input type=\"checkbox\"  id=\"taskItemAll\" value=\"\" onclick=gridCheckAll(this) /> ',
					// 	dataIndex : 'checkstr',
					//
					// 	menuDisabled : true,
					// 	sortable : false,
					// 	locked : true,
					// 	width : 50
					// },
					{
						text : '',
						width : 30,
						locked : true,
						hidden:true,
						hidable:false,
						renderer : function(value, metaData, record, rowIndex,
								store, view) {
							return '<a href=\'javascript:showFlowInfoWinPic('
									+ record.data.flowid
									+ ',\"'
									+ record.data.prjName
									+ '\",\"'
									+ record.data.flowName2
									+ '\",\"'
									+ record.data.dataNum
									+ '\")\'><image class=\'snapshot\' src=\'images/monitor_bg.png\' border=\'0\' class=\'snapshot_level\'/></a>';
						}
					},
					{
						text : '快照',
						dataIndex : 'pic',
						width: 60, 
						
						locked : true
					}, {
					text : 'topo依赖',
					dataIndex : 'topoPic',
					width: 60,

					locked : true
				},
				{
						text : '状态',
						dataIndex : 'status',
						locked : true
					}, {
						text : '工作流ID',
						dataIndex : 'flowid',
						
						hidden : true,
						locked : true
					}, {
						text : '作业名',
						dataIndex : 'jobName',
						width : 290,
						locked : true/*,
						renderer: actMonitorStateJobName*/
					}, {
						text : '任务ID',
						dataIndex : 'taskId',
						
						hidden : true
					}, {
						text : zixitongming,
						dataIndex : 'prjName',
						
						width : 250
					}, {
						text : '流程名称',
						dataIndex : 'flowName',
						
						width : 250
					}, {
						text : '数据日期',
						dataIndex : 'dataNum',
						
						width : 80
					}, {
						text : '作业描述',
						dataIndex : 'iactDesc',
						hidden : hidekey,
						width : 100
					},{
						text : '所属系统',
						dataIndex : 'system',
						
						width : 150
					}, {
						text : '排队时间',
						dataIndex : 'pdsj',
						
						width : 140
					}, {
						text : '开始时间',
						dataIndex : 'kssj',
						
						width : 140
					}, {
						text : '运行时长',
						
						dataIndex : 'yxsj',
						width : 160
					}, {
						text : '经办人',
						
						dataIndex : 'user'
					}, {
						text : '依赖',
						
						dataIndex : 'yl',
						width : 50
					}, {
						text : '触发',
						
						dataIndex : 'cf',
						width : 50
					}, {
						text : '历史',
						
						dataIndex : 'his',
						width : 50
					}, {
						text : '主机名',
						
						dataIndex : 'agenthostname'
					}, {
						text : '脚本路径',
						
						dataIndex : 'shellPath',
						width : 290
					}, {
						text : 'IP',
						
						dataIndex : 'agentIp'
					}, {
						text : '端口',
						
						dataIndex : 'agentPort'
					}, {
						text : '主线名',
						
						dataIndex : 'mainLineName'
					}, {
						text : '昨日活动执行开始时间',
						width : 170,
						
						hidden : true,
						dataIndex : 'yesterdayBeginNextTime'
					}, {
						text : '今日活动预计开始执行时间',
						width : 190,
						
						hidden : true,
						dataIndex : 'toDayPlanBeginTime'
					}, {
						text : '今日活动执行开始时间',
						width : 170,
						
						hidden : true,
						dataIndex : 'beginNextTime'
					}, {
						text : '今日活动预计执行结束时间',
						width : 190,
						
						hidden : true,
						dataIndex : 'toDayPlanEndTime'
					} , {
					text : '是否日启',
					width : 190,
					hidden : true,
					dataIndex : 'isDayStart'
				}];
			// 分页的combobox下拉选择显示条数
			combo = Ext.create('Ext.form.ComboBox', {
				name : 'pagesize',
				hiddenName : 'pagesize',
				store : new Ext.data.ArrayStore({
					fields : [ 'text', 'value' ],
					data : [ [ '30', 30 ], [ '50', 50 ], [ '100', 100 ],
							[ '150', 150 ] ]
				}),
				valueField : 'value',
				displayField : 'text',
				emptyText : 30,
				width : 60
			});
			// 添加下拉显示条数菜单选中事件
			combo.on("select", function(comboBox) {
				var pagingToolbar = Ext.getCmp('pagingbar');
				pagingToolbar.pageSize = parseInt(comboBox.getValue());
				itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
				oam_home_store.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
				search();
			});
			combo.on("blur", function(comboBox) {
				var pagingToolbar = Ext.getCmp('pagingbar');
				pagingToolbar.pageSize = parseInt(comboBox.getValue());
				itemsPerPage = parseInt(comboBox.getValue());// 更改全局变量itemsPerPage
				oam_home_store.pageSize = itemsPerPage;// 设置store的pageSize，可以将工具栏与查询的数据同步。
				search();
			});
			
			if(jobSchedulingQuerySystemSwitch){
				oam_pageBar_home = Ext.create('Ext.PagingToolbar', {
					id : 'pagingbar',
					height : 90,
					store : oam_home_store,
					dock : 'bottom',
					border:false,
					displayInfo : true,
					baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
					items : [  '-',combo, batchTryAgain_btn, batchIgnore_btn,
								oam_act_form]//, '->', home_state ]
				});
			}else{
				oam_pageBar_home = Ext.create('Ext.PagingToolbar', {
					id : 'pagingbar',
					store : oam_home_store,
					dock : 'bottom',
					border:false,
					displayInfo : true,
					baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
					items : [  '-',combo, batchTryAgain_btn, batchIgnore_btn,
						oam_act_form, '->', home_state ]

				});
			}
			if(jobSchedulingQuerySystemSwitch){
				home_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
					store : oam_home_store,
					columnLines : true,
					border: false,
					id:'home_grid',
					columns : job_list_columns,
					cls:'customize_panel_back',
					padding : grid_space,
					ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',  
					viewConfig : {
						selType : 'cellmodel',
						stripeRows : true,// 在表格中显示斑马线
						enableTextSelection : true,
						/*getRowClass : changeRowClass*/
						getRowClass : changeRowClassJobMonitor
					// 可以复制单元格文字
					},
					ipageItems : [ 
						batchTryAgain_btn, 
						batchIgnore_btn ,
						oam_act_form
					]
	//				bbar : oam_pageBar_home
				});
			}else{

				home_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
					store : oam_home_store,
					columnLines : true,
					border: false,
					id:'home_grid',
					selModel : selHomeModel,
					columns : job_list_columns,
					cls:'customize_panel_back',
					padding : grid_space,
//					ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
					forceFit: true,
					stripeRows: true,
					viewConfig : {
						selType : 'cellmodel',
						stripeRows : true,// 在表格中显示斑马线
						enableTextSelection : true,
						getRowClass : changeRowClassJobMonitor
					// 可以复制单元格文字
					},

					ipageItems : [ 
						batchTryAgain_btn, 
						batchIgnore_btn ,
						oam_act_form,
						'->',
						home_state
					]
	//				bbar : oam_pageBar_home
				});

				/*home_grid.getStore().on('load',function(s,records){

					var girdcount=0;

					s.each(function(r){

						if(r.get('status').indexOf("失败")>0){

							home_grid.getView().addRowCls(girdcount, 'x-grid-Yellow');
						}

						girdcount=girdcount+1;

					});

				});*/

			}

			if(jobSchedulingQuerySystemSwitch){
				jobPanel = Ext.create('Ext.Panel', {
					region : 'center',
					border : true,
					margins:grid_margin,
					witdh : '100%',
					height : '70%',
					split : true,
				 	dockedItems:[{
				 		xtype:'toolbar',
				 		dock:'bottom',
				 		items:['->',home_state]
				 	}],
					items : [ home_grid ],
					listeners : {
						resize : function(a) {
							home_grid.setHeight(jobPanel.getHeight()-24);
						}
					}
				});
			}else{
				jobPanel = Ext.create('Ext.Panel', {
					region : 'center',
					border : true,
					margins:grid_margin,
					witdh : '100%',
					height : '70%',
					split : true,
					items : [ home_grid ],
					listeners : {
						resize : function(a) {
							home_grid.setHeight(jobPanel.getHeight()-24);
						}
					}
				});
			}
			
			oam_alarmPanel = Ext.create('Ext.Panel', {
			 title : '报警信息',
			 collapsible : true,
			 witdh : '100%',
			 height : 251,
			 split : true,
			 region : 'south',
			 layout : 'fit',
			 collapsed : true,
			 border : true,
			 margins:grid_margin,
			 titleAlign : 'left',
			 titleCollapse : true,
			 items : [ alarm_grid ]
			 });

			if(jobSchedulingQuerySystemSwitch){
				home_grid.down('gridcolumn[text=昨日活动执行开始时间]').show();
				home_grid.down('gridcolumn[text=今日活动执行开始时间]').show();
				home_grid.down('gridcolumn[text=今日活动预计开始执行时间]').show();
				home_grid.down('gridcolumn[text=今日活动预计执行结束时间]').show();
				home_grid.down('gridcolumn[text=经办人]').hide();
				home_grid.down('gridcolumn[text=主机名]').hide();
				home_grid.down('gridcolumn[text=IP]').hide();
				home_grid.down('gridcolumn[text=端口]').hide();
				home_grid.down('gridcolumn[text=主线名]').hide();
				combobox_iacttype.show();
			}
			
			contentPanel.getHeader().hide();// 设置contentPanel标题头隐藏
			queryForm.setTitle(contentPanel.title);
			var mainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : 'newHomeGrid',
				bodyCls: 'service_platform_bodybg',
				cls:'customize_panel_header_arrow act_discolor',
				layout : 'border',
				header : false,
				border : false,
				width : contentPanel.getWidth(),
//				cls:'customize_panel_back', 
				height : contentPanel.getHeight(),
				items : [ queryForm, jobPanel , oam_alarmPanel
				]
			});
			contentPanel.on('resize', function() {
				//mainPanel.setWidth(contentPanel.getWidth());
				home_grid.setWidth(contentPanel.getWidth());
				queryForm.setWidth(contentPanel.getWidth());
				jobPanel.setWidth(contentPanel.getWidth());
				oam_alarmPanel.setWidth (contentPanel.getWidth ());
				oam_pageBar_home.setWidth(contentPanel.getWidth());
			});
			Ext.Ajax.on('requestexception', function(conn, response, options) {
				if (response.status == "999") {
					window.location.href = "login.do";
				}
			});
			startAutoFreshAlarm();
			refreshClick(true);
			
			contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
		    	Ext.destroy(mainPanel);
		    	stopAutoFresh();
		    	stopAutoFresh_alarm();
				if(Ext.isIE){
		        	CollectGarbage(); 
		    	}
			 });
			
//			$("body").keydown(function(event) {
//		 	    if (event.keyCode == "13") { 
//		 	    	search();
//		 	    }
//		 	});
});

// 根据报警等级，改变行的颜色状态
function changeRowClass(record, rowIndex, rowParams, store) {
	var i_showColor = record.get('i_showColor');
	var isShow = i_showColor.split(';');
	if(isShow[0]=='true'){
		var color = isShow[1];
		if(color){
			if(color=='red'){
				return 'x-grid-red';
			}
			if(color=='yellow'){
				return 'x-grid-yellow';
			}
		}
	}
	var i_changeColor = record.get('i_changeColor');
	if (i_changeColor == '1') {
		if (record.get('i_taskExceLevel') == '5') {
			return 'x-grid-red';
		}
		if (record.get('i_taskExceLevel') == '4') {
			return 'x-grid-orange';
		}
		if (record.get('i_taskExceLevel') == '3') {
			return 'x-grid-yellow';
		}
	}
	var actName = record.get("jobName");
	if(actName.indexOf('开始跑批') > 1 && record.get("status").indexOf('就绪') > 1){
		
		return 'x-grid-blue';
	}

}

 function getType(type) {
	 if ("0" == type) {
	 return "启动时间";
	 } else if ("1" == type) {
	 return "结束时间";
	 } else if ("2" == type) {
	 return "设置耗时";
	 } else if ("3" == type) {
	 return "平均耗时";
	 } else if ("4" == type) {
	 return "批量执行中断";
	 } else if ("5" == type) {
	 return ("Agent通信异常");
	 } else if ("6" == type) {
	 return "Server宕机";
	 } else if ("7" == type) {
	 return "Agent性能异常";
	 } else if ("8" == type) {
	 return "系统数据库异常";
	 } else if ("9" == type) {
	 return "超时异常";
	 } else {
	 return "";
	 }
 }
 function startAutoFreshAlarm() {
 // 自动刷新查询报警方法
 var refreshTime = oam_alarm_fresh_form.queryById('refreshTime_alarm').getValue();
 autoFresh_alarm(refreshTime);
 }
 function actMonitorClbj(iid) {
	 var jsonData = getJSON(iid);
	 if (jsonData == "") {
	 Ext.Msg.alert('提示', "请选择要处理报警！");
	 return;
	 }
	 Ext.MessageBox.confirm('提示', '确认要处理报警信息吗', callBack);
	 function callBack(id) {
		 if (id == 'yes') {
			 Ext.Ajax.request({
			 url : 'delAlarm.do',
			 method : 'POST',
			 params : {
			 jsonData : jsonData
			 },
			 success : function(response, opts) {
				 var success = Ext.decode(response.responseText).success;
				 var message = Ext.decode(response.responseText).message;
				 if (success) {
					 Ext.MessageBox.show({
					 title : "提示",
					 msg : message, 
					 buttonText : {
						 yes : '确定'
					 },
					 buttons : Ext.Msg.YES
					 });
					 loadAlarmImgAndData();
				 } else {
					 Ext.MessageBox.show({
					 title : "提示",
					 msg : message,
					 buttonText : {
						 yes : '确定'
					 },
					 buttons : Ext.Msg.YES
					 });
				 }
			 },
//			 failure : function(response, opts) {
//				 Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
//			 }
			 failure : function(result, request) {
				 secureFilterRs(result,"操作失败！");
			 } 
		   });
		 } else {
			 return;
		 }
	 }
 }
 function getJSON(alarmid) {
	 if (alarmid != undefined) {
		 return '[{"id":"' + alarmid + '"}]';
	 }
	 var m = Ext.getCmp('alarm_grid').getSelectionModel().getSelection();
	 if (m.length < 1) {
		 return "";
	 } else {
	 var jsonData = "[";
	 for (var i = 0, len = m.length; i < len; i++) {
		 if (i == 0)
		 jsonData = jsonData + '{"id":"' + parsIIDJson('id', m[i].data) + '"}';
		 else {
		 jsonData = jsonData + "," + '{"id":"'
		 + parsIIDJson('id', m[i].data) + '"}';
		 }
	 }
	 jsonData = jsonData + "]";
	 return jsonData;
	 }
 }
// 从一个json对象中，解析出key=iid的value,返回改val
function parsIIDJson(key, jsonObj) {
	var eValue = eval('jsonObj.' + key);
	return jsonObj['' + key + ''];
}

function search() {
	var isystem = Ext.getCmp('queryform').getForm().findField("isystem")
			.getValue();
	var istatus = Ext.getCmp('queryform').getForm().findField("istatus")
			.getValue();
	var iprjName = Ext.getCmp('queryform').getForm().findField("iprjName")
			.getRawValue();
	if(jobSchedulingQuerySystemSwitch){
		var iacttype = Ext.getCmp('queryform').getForm().findField("iacttype")
		.getValue();
	}
	if (iprjName == '' || iprjName == null || iprjName == undefined
			|| iprjName == 'undefined' || iprjName == 'null') {
		iprjName = '';
	}
	var ijobName = Ext.getCmp('queryform').getForm().findField("ijobName")
			.getValue();
	var isDis = Ext.getCmp('queryform').getForm().findField("isDis").getValue();
	var idataNum = Ext.util.Format.date(oam_dataNum_date.getValue(), 'Ymd');
	oam_pageBar_home.moveFirst();
	
}


String.prototype.endWith = function(endStr) {
	var d = this.length - endStr.length;
	return (d >= 0 && this.lastIndexOf(endStr) == d)
}
// ShellCmd执行日志
function openShellInfoWindow(obj, actName, flowId, actId, reqId) {

	theLeft = (screen.width - 600) / 2;
	theTop = (screen.height - 500) / 2;
	
	/*if (actName.endWith("_CHECK")) {
		open("page/jobScheduling/shellWindowHisMain/shellWindowHisMain.jsp?reqId=" + reqId + "&flowId=" + flowId
				+ "&actId=" + actId + "&flowInsName=" + encodeURI(obj)
				+ "flowInsName" + "&actName=" + encodeURI(actName) + "actName",
				'', 'scrollbars,status=yes,top=' + theTop + ',left=' + theLeft
						+ ',width=650,height=500,resizable=yes');
	} else {*/
		openWindowsRunning('getShellOutPutRunning_ieai.do', flowId, actName,
				encodeURI(obj), null, reqId);
		// open("shellWindow.do?reqId=" + reqId + "&flowId=" + flowId +
		// "&actId="
		// + actId + "&flowInsName=" + encodeURI(obj) + "flowInsName"
		// + "&actName=" + encodeURI(actName) + "actName", '',
		// 'scrollbars,status=yes,top=' + theTop + ',left=' + theLeft
		// + ',width=650,height=500,resizable=yes');
	//}
	// window.open("shellWindow.jsp?actName="+actName+"&flowId="+flowId,'','scrollbars,status=yes,top='+theTop+',left='+theLeft+',width=415,height=370,resizable=yes');
}

function openTn5250foWindow(obj, actName, flowId, actId, reqId) {

	openWindowsRunning_Tn('getTn5250OutPutRunning_ieai.do', flowId, actName,
			encodeURI(obj), null, reqId);

}
//操作双人复核
function flowDoubleCheck(type){
	var el = document.getElementsByTagName('input');
	var len = el.length;
	var tasks = '';
	for (var i = 0; i < len; i++) {
		if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
				&& (el[i].disabled == false)) {
			if (el[i].checked && el[i].value != '') {
				tasks = tasks + el[i].value + ",";
				//el[i].disabled = true;
			}
		}
	}
    if (tasks =='' ) {
        Ext.MessageBox.alert("提示", "请选择要处理的异常作业！");
        return;
    }
    Ext.create("Ext.window.Window", {
        id: 'flowDoubleCheckWin',
        title: '操作复核',
        width: contentPanel.getWidth() / 3,
        height: contentPanel.getHeight() / 5,
        layout: 'fit',
        closeAction: 'destroy',
        modal: true,
        autoShow: false,
        items: [{
            xtype: 'form',
            defaultType: 'textfield',
            defaults: {
                anchor: '100%',
            },
            fieldDefaults: {
                labelWidth: 40,
                labelAlign: 'left',
                flex: 1,
                margin: 5
            },
            items: [{
                xtype: 'container',
                layout: 'hbox',
                items: [{
                    xtype: 'textfield',
                    name: 'checkuser',
                    id: 'flowDoubleCheckUser',
                    labelWidth: 90,
                    width: '50%',
                    fieldLabel: '审核人登录名',
                    allowBlank: false,
                    emptyText: '请输入审核人用户名'
                },
                {
                    xtype: 'textfield',
                    name: 'checkuserpass',
                    id: 'flowDoubleCheckUserPass',
                    fieldLabel: '密码',
                    width: '50%',
                    allowBlank: false,
                    inputType: 'password',
                    emptyText: '请输入审核人密码'
                }]
            }]
        }],
        buttons: [{
            xtype: 'button',
            text: '确定',
            handler: function() {
                var dUserName = Ext.getCmp('flowDoubleCheckUser').value;
                var dUserPass = Ext.getCmp('flowDoubleCheckUserPass').value;
                if (dUserName == null || dUserName == '' || dUserName == 'undefined') {
                    Ext.MessageBox.alert("提示", "请填写审核用户名称！");
                    Ext.getCmp('flowDoubleCheckUser').focus();
                    return;
                }
                if (dUserPass == null || dUserPass == '' || dUserPass == 'undefined') {
                    Ext.MessageBox.alert("提示", "请填写审核用户密码！");
                    return;
                }
                Ext.Ajax.request({
                    url: 'flowOrActDoubleCheck.do',
                    method: 'POST',
                    params: {
                        dUserName : dUserName,
                        dUserPass : dUserPass,
                        type : type,
                        tasks : tasks
                    },
                    success: function(response, opts) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        if (success) {
                            Ext.MessageBox.show({
                                title: "提示",
                                msg: message,
                                buttonText: {
                                    yes: '确定'
                                },
                                buttons: Ext.Msg.YES
                            });
                            Ext.getCmp('flowDoubleCheckWin').close();
                            if (type == 1) {
                            	batchOperationTask('1');
                            } else if (type == 2) {
                            	batchOperationTask('2');
                            }
                        } else {
                            Ext.MessageBox.show({
                                title: "提示",
                                msg: message,
                                buttonText: {
                                    yes: '确定'
                                },
                                buttons: Ext.Msg.YES
                            });
                        }
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }
        },
        {
            xtype: 'button',
            text: '取消',
            handler: function() {
            	
            	var el = document.getElementsByTagName('input');
    			var len = el.length;
    			var tasks = '';
    			for (var i = 0; i < len; i++) {
    				if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
    						&& (el[i].disabled == true)) {
    					if (el[i].checked && el[i].value != '') {
    						tasks = tasks + el[i].value + ",";
    						el[i].disabled = false;
    					}
    				}
    			}
                this.up("window").close();
            }
        }]
    }).show();
}
function batchOperationTask(oper) {
	// var el = document.getElementsByTagName('input');
	// var len = el.length;
	var tasks = '';
	// for (var i = 0; i < len; i++) {
	// 	if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
	// 			&& (el[i].disabled == false)) {
	// 		if (el[i].checked && el[i].value != '') {
	// 			tasks = tasks + el[i].value + ",";
	// 			el[i].disabled = true;
	// 		}
	// 	}
	// }
	var grid = Ext.getCmp('home_grid')
	var selectedRecords = grid.getSelectionModel().getSelection();
	for (var i = 0 ; i < selectedRecords.length; i++) {
		var selectedRecord = selectedRecords[i];
		var taskid = selectedRecord.raw.flowId;
		tasks = tasks + taskid + ",";
	}
	batchOper(tasks, oper);
}
function checkAll(name) {
	var el = document.getElementsByTagName('input');
	var len = el.length;
	for (var i = 0; i < len; i++) {
		if ((el[i].type == "checkbox") && (el[i].name == name)
				&& (el[i].disabled == false)) {
			el[i].checked = true;
		}
	}
}

function clearAll(name) {
	var el = document.getElementsByTagName('input');
	var len = el.length;
	for (var i = 0; i < len; i++) {
		if ((el[i].type == "checkbox") && (el[i].name == name)
				&& (el[i].disabled == false)) {
			el[i].checked = false;
		}
	}
}

function gridCheckAll(check) {
	if (check.checked == true) {
		checkAll('taskItem');
	} else {
		clearAll('taskItem');
	}
}

/*
 * 
 * 批量操作
 * 
 * 
 */
function batchOper(taskidlist, oper) {
	/*批量略过不可包含日启校验 - start*/
	var grid = Ext.getCmp('home_grid')
	var selectedRecords = grid.getSelectionModel().getSelection();
	// 存放行序号
	var rowArray = [];
	var messageText = '';
	for (var i = 0 ; i < selectedRecords.length; i++) {
		var selectedRecord = selectedRecords[i];
		// 是否日启字段属性
		var isDayStart = selectedRecord.raw.dayStart;
		var index = grid.store.indexOf(selectedRecord)
		var rownumber = index+1;
		if (isDayStart) {
			rowArray.push(rownumber);
		}
	}
	if (rowArray.length > 0 && oper == '2') {
		for (var i = 0; i < rowArray.length; i++) {
			messageText += rowArray[i];
			if (i < rowArray.length - 1) {
				messageText += '、';
			}
		}
		Ext.MessageBox.show({
			title : "提示",
			msg : messageText + " 是日启动工程,禁止略过",
			buttonText : {
				yes : '确定'
			},
			buttons : Ext.Msg.YES
		});
		return;
	}

	/*批量略过不可包含日启校验 - end*/

	if (taskidlist == "") {
		Ext.Msg.alert('提示', "请选择要处理的异常作业！");
		return;
	}
	
	var batchUrl = '';
	
	if(bhPerSwitch)
	{
		batchUrl = 'batchOperBh.do';
	}else
	{
		batchUrl = 'queryHomeData.do?action=batchOper';
	}
	//alter(batchUrl);
	Ext.MessageBox.confirm('提示', '确定要处理选中的异常作业吗?', callBack);
	function callBack(id) {
		if (id == 'yes') {
			Ext.Ajax.request({
				// url : 'homeAction.do?action=batchOper',
				url : batchUrl,
				method : 'POST',
				params : {
					errorTaskIdList : taskidlist,
					oper : oper
				},
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;
					if (success) {
						Ext.MessageBox.show({
							title : "提示",
							msg : message,
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
						var obj = document.getElementById('taskItemAll');
						obj.checked = false;
						reloadGroup();
					} else {
						Ext.MessageBox.show({
							title : "提示",
							msg : message,
							buttonText : {
								yes : '确定'
							},
							buttons : Ext.Msg.YES
						});
					}

				},
				failure : function(response, opts) {
					Ext.Msg.alert('提示', "批量操作请求未成功,请重新操作");
				}
			});
		} else {
			
			var el = document.getElementsByTagName('input');
			var len = el.length;
			var tasks = '';
			for (var i = 0; i < len; i++) {
				if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
						&& (el[i].disabled == true)) {
					if (el[i].checked && el[i].value != '') {
						tasks = tasks + el[i].value + ",";
						el[i].disabled = false;
					}
				}
			}
			return;
		}
	}

}

function autoFresh(t) {
	search();
	oam_autoFreshObject = setTimeout("autoFresh(" + t + ")", 1000 * t * 60); // 以分为单位
}
function stopAutoFresh() {
	clearTimeout(oam_autoFreshObject);

}

function autoFresh_alarm(t) {
	loadAlarmImgAndData();//test.暂时注释掉
	var refreshTime = oam_act_form.queryById('refreshTime').getValue();
	oam_autoFreshObject_alarm = setTimeout("autoFresh_alarm(" + refreshTime + ")", 1000 * refreshTime * 60); // 以分为单位
}
function loadAlarmImgAndData() {
	oam_alarm_store.loadPage(1);// 显示第一页
	Ext.Ajax.request({
		url : 'queryAlarmNum.do',
		method : 'POST',
		success : function(response, opts) {
			var success = Ext.decode(response.responseText).success;
			var isalarm = Ext.decode(response.responseText).isalarm;
			var alarmNumByPermit = Ext.decode(response.responseText).alarmNumByPermit;
			if (success) {
				oam_alarmPanel.setTitle(isalarm);
			}
			var audio_player_actMonitor = document.getElementById("audio_player_actMonitor");
			if(audio_player_actMonitor!=null && typeof(audio_player_actMonitor)!="undefined"){
				setTimeout(function(){audio_player_actMonitor.play()},1500);
			}
		},
		failure : function(response, opts) {
			 Ext.Msg.alert('提示', "查询请求未成功,请重新查询。");
		}
	});
}
function stopAutoFresh_alarm() {
	clearTimeout(oam_autoFreshObject_alarm);
}

function openErrorTaskWindow(errorTaskId) {
	theLeft = (screen.width - 600) / 2;
	theTop = (screen.height - 500) / 2;
	window.open("errortaskDetail.do?fromMonitoringList=true&errorTaskId="
			+ errorTaskId, '', 'scrollbars,top=' + theTop + ',left=' + theLeft
			+ ',width=600,height=500,resizable=yes');
}
function openTaskWindow(taskId, actId) {
	theLeft = (screen.width - 600) / 2;
	theTop = (screen.height - 500) / 2;
	window.open("taskdetail.do?fromMonitoringList=true&taskid=" + taskId
			+ "&actId=" + actId, '', 'scrollbars,top=' + theTop + ',left='
			+ theLeft + ',width=600,height=500,resizable=yes');
}


function reloadGroup() {
	search();
}


// 触发：
/**
 * @desc 【aoms作业调度——首页】单击'触发'，弹出窗体
 * @param flowId
 * @param flowInsName
 * @param flowName
 * @param prjName
 * @param actName
 */
function OpenSuccActInfo(flowId, flowInsName, flowName, prjName, actName) {
	Ext.define('OpenPreModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'string'
		}, {
			name : 'comment',
			type : 'string'
		}, {
			name : 'instanceName',
			type : 'string'
		}, {
			name : 'flowName',
			type : 'string'
		}, {
			name : 'ipri',
			type : 'string'
		}  , {
			name : 'iweight',
			type : 'string'
		}  , {
			name : 'ibegintime',
			type : 'string'
		}  , {
			name : 'iendtime',
			type : 'string'
		}   ]
	});

	var SuccActInfoStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'OpenPreModel',
		pageSize : 25,
		proxy : {
			type : 'ajax',
			url : 'ActAroundRelation_Data.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	SuccActInfoStore.on('beforeload', function(s) {
		var params = s.getProxy().extraParams;
		Ext.apply(params, {
			flowId : flowId,
			flowInsName : flowInsName,
			flowName : flowName,
			prjName : prjName,
			actName : actName,
			action : 'succAct'
		});
	});
	var SuccShowFlowpageBar = Ext.create('Ext.PagingToolbar', {
		store : SuccActInfoStore,
		dock : 'bottom',
		displayInfo : true,
		hidden : jobSchedulingQuerySystemSwitch?true:false,
		border : false,
		baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
	});

	var SuccActInfoGrid = Ext.create('Ext.grid.Panel', {
		title : '触发关系展示',
		split : true,
		multiSelect : true,
		emptyText : '没有流程详细信息',
		width : contentPanel.getWidth() - 350,
		height : contentPanel.getHeight(),
		columnLines : true,
		store : SuccActInfoStore,
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			align : 'left',
			width : 40
		}, {
			text : '工作流名',
			dataIndex : 'flowName',
			
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '数据日期',
			dataIndex : 'instanceName',
			
			flex : 1
		}, {
			text : '状态',
			dataIndex : 'comment',
			
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		},{
			text : '开始时间',
			dataIndex : 'ibegintime',
			
			width : 150
		}, {
			text : '结束时间',
			dataIndex : 'iendtime',
			
			width : 150
		}, {
			text : '优先级',
			dataIndex : 'ipri',
			
			flex : 1
		}, {
			text : '权重',
			dataIndex : 'iweight',
			
			flex : 1
		} ],
		bbar : SuccShowFlowpageBar
	});

	var SuccActInfoWin = null;
	if (SuccActInfoWin == null || !SuccActInfoWin.isVisible()) {
		SuccActInfoWin = Ext.create('Ext.window.Window', {
			title : '流程信息展示',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			items : [ SuccActInfoGrid ]
		});
	}
	SuccActInfoWin.show();
}

// 依赖
/**
 * @desc 【aoms作业调度——首页】单击'依赖'，弹出窗体
 * @param flowId
 * @param flowInsName
 * @param flowName
 * @param prjName
 * @param actName
 */
function OpenPreActInfo(flowId, flowInsName, flowName, prjName, actName) {

	Ext.define('OpenPreModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'string'
		}, {
			name : 'comment',
			type : 'string'
		}, {
			name : 'instanceName',
			type : 'string'
		}, {
			name : 'flowName',
			type : 'string'
		}, {
			name : 'ipri',
			type : 'string'
		}  , {
			name : 'iweight',
			type : 'string'
		}  , {
			name : 'ibegintime',
			type : 'string'
		}  , {
			name : 'iendtime',
			type : 'string'
		} ]
	});

	var RelyActInfoStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'OpenPreModel',
		pageSize : 25,
		proxy : {
			type : 'ajax',
			url : 'ActAroundRelation_Data.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	RelyActInfoStore.on('beforeload', function(s) {
		var params = s.getProxy().extraParams;
		Ext.apply(params, {
			flowId : flowId,
			flowInsName : flowInsName,
			flowName : flowName,
			prjName : prjName,
			actName : actName,
			action : 'preAct'
		});
	});
	var showFlowpageBar = Ext.create('Ext.PagingToolbar', {
		store : RelyActInfoStore,
		dock : 'bottom',
		displayInfo : true,
		hidden : jobSchedulingQuerySystemSwitch?true:false,
		baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
	});

	var RelyActInfoGrid = Ext.create('Ext.grid.Panel', {
		title : '依赖关系展示',
		split : true,
		multiSelect : true,
		emptyText : '没有流程详细信息',
		width : contentPanel.getWidth() - 350,
		height : contentPanel.getHeight(),
		columnLines : true,
		store : RelyActInfoStore,
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			align : 'left',
			width : 40
		}, {
			text : '工作流名',
			dataIndex : 'flowName',
			
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '数据日期',
			dataIndex : 'instanceName',
			
			flex : 1
		}, {
			text : '状态',
			dataIndex : 'comment',
			
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		},{
			text : '开始时间',
			dataIndex : 'ibegintime',
			
			width : 150
		}, {
			text : '结束时间',
			dataIndex : 'iendtime',
			
			width : 150
		}, {
			text : '优先级',
			dataIndex : 'ipri',
			
			flex : 1
		}, {
			text : '权重',
			dataIndex : 'iweight',
			
			flex : 1
		} ],
		bbar : showFlowpageBar
	});
	var RelyActInfoWin = null;
	if (RelyActInfoWin == null || !RelyActInfoWin.isVisible()) {
		RelyActInfoWin = Ext.create('Ext.window.Window', {
			title : '流程信息展示',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			items : [ RelyActInfoGrid ]
		});
	}
	RelyActInfoWin.show();

}

/**
 * @desc 【aoms作业调度——首页】单击'工作流名称'，弹出窗体
 * @PARAM flowId,prjName,flowName,InsName
 */
function showFlowInfoWin(flowId, prjName, flowName, InsName, sysName) {
	Ext.define('showFlowModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'status',
			type : 'string'
		}, {
			name : 'endTime',
			type : 'string'
		}, {
			name : 'preAct',
			type : 'string'
		}, {
			name : 'actName',
			type : 'string'
		}, {
			name : 'startTime',
			type : 'string'
		}, {
			name : 'actType',
			type : 'string'
		}, {
			name : 'succAct',
			type : 'string'
		} ]
	});

	var showFlowStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'showFlowModel',
		pageSize : 25,
		proxy : {
			type : 'ajax',
			url : 'showflowInfo.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	// flowId,prjName,flowName,InsName
	showFlowStore.on('beforeload', function(s) {
		var params = s.getProxy().extraParams;
		Ext.apply(params, {
			flowId : flowId,
			prjName : prjName,
			flowName : flowName,
			InsName : flowName,
			sysName : sysName
		});
	});
	var showFlowpageBar = Ext.create('Ext.PagingToolbar', {
		store : showFlowStore,
		dock : 'bottom',
		displayInfo : true,
		baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
	});

	var showFlowHisotryGrid = Ext.create('Ext.grid.Panel', {
		title : '流程详细列表',
		split : true,
		multiSelect : true,
		emptyText : '没有流程详细信息',
		width : contentPanel.getWidth() - 350,
		height : contentPanel.getHeight(),
		columnLines : true,
		store : showFlowStore,
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			align:'left',
			width : 40
		}, {
			text : '活动名称',
			dataIndex : 'actName',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '状态',
			dataIndex : 'status',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '开始时间',
			dataIndex : 'startTime',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : true
			}
		}, {
			text : '结束时间',
			dataIndex : 'endTime',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : true
			}
		} ],
		bbar : showFlowpageBar
	});
	var showFlowInfoWin = null;
	if (showFlowInfoWin == null || !showFlowInfoWin.isVisible()) {
		showFlowInfoWin = Ext.create('Ext.window.Window', {
			title : '流程信息展示',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			padding:'20',
			autoScroll : true,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			items : [ showFlowHisotryGrid ]
		});
	}
	showFlowInfoWin.show();
}

// 快照弹出功能
function showFlowInfoWinPic(flowId, prjName, flowName, InsName, sysName) {
	var title = '流程详细列表';
	var showFlowHisotryGrid;
	Ext.define('showFlowModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'status',
			type : 'string'
		}, {
			name : 'endTime',
			type : 'string'
		}, {
			name : 'preAct',
			type : 'string'
		}, {
			name : 'actName',
			type : 'string'
		// renderer : function(value, metaData, record, rowIndex, store, view) {
		// return '<a href=\'javascript:showFlowInfoWin('
		// // + record.data.id
		// // + ',\"'
		// // + record.data.projectName
		// // + '\",\"'
		// // + record.data.flowName
		// // + '\",\"'
		// // + record.data.instanceName
		// + '\")\'><image src=\'images/snapshot.gif\' border=\'0\'/></a>';
		// }
		}, {
			name : 'startTime',
			type : 'string'
		}, {
			name : 'actType',
			type : 'string'
		}, {
			name : 'succAct',
			type : 'string'
		} ]
	});

	var showFlowStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'showFlowModel',
		pageSize : 25,
		proxy : {
			type : 'ajax',
			url : 'showflowInfo.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	// flowId,prjName,flowName,InsName
	showFlowStore.on('beforeload', function(s) {
		var params = s.getProxy().extraParams;
		Ext.apply(params, {
			flowId : flowId,
			prjName : prjName,
			flowName : flowName,
			InsName : flowName,
			sysName : sysName
		});
	});

	var showFlowpageBar = Ext.create('Ext.PagingToolbar', {
		store : showFlowStore,
		dock : 'bottom',
		displayInfo : true,
		baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
	});

	showFlowHisotryGrid = Ext.create('Ext.grid.Panel', {
		title : title,
		split : true,
		multiSelect : true,
		emptyText : '没有流程详细信息',
		width : contentPanel.getWidth() - 350,
		height : contentPanel.getHeight(),
		columnLines : true,
		store : showFlowStore,
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			align:'left',
			width : 40
		}, {
			text : '活动名称',
			dataIndex : 'actName',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '状态',
			dataIndex : 'status',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '开始时间',
			dataIndex : 'startTime',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : true
			}
		}, {
			text : '结束时间',
			dataIndex : 'endTime',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : true
			}
		}, {
			text : '依赖',
			dataIndex : 'preAct',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : true
			}
		}, {
			text : '触发',
			dataIndex : 'succAct',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : true
			}
		} ],
		bbar : showFlowpageBar
	});

	showFlowStore
			.on(
					'load',
					function(r, options, success) {
						var reader = showFlowStore.getProxy().getReader();
						if (success) {
							var mainflow = reader.jsonData.metaData.isMain;
							if (mainflow == false) {
								title = '流程详细列表&nbsp;&nbsp;&nbsp;&nbsp;<a href=\'#\' onclick="showFlowState('
										+ flowId
										+ ');" >'
										+ '<image class=\'snapshot\'  src=\'images/monitor_bg.png\' border=\'0\'/>'
										+ '</a>';
								showFlowHisotryGrid.setTitle(title);
							}
						} else {
							Ext.Msg.alert('提示', "超时!");
						}
					});
	var showFlowInfoWin = null;
	if (showFlowInfoWin == null || !showFlowInfoWin.isVisible()) {
		showFlowInfoWin = Ext.create('Ext.window.Window', {
			title : '流程信息展示',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : contentPanel.getWidth() - 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			items : [ showFlowHisotryGrid ]
		});
	}
	showFlowInfoWin.show();
}

// 历史记录
function showflowHistoryListForHis(flowid, name, endtime, status, prjName,
		flowName, insDate, isSystem) {
	// open('showflowHistoryList.do?action=actretryHis&flowid='+flowid+'&endtime='+endtime+'&actname='+name+'&status='+status+'&prjName='+prjName+'&flowName='+flowName+'&insDate='+insDate+'&isSystem='+isSystem,''
	// , 'scrollbars=yes,top=170,left=30,width=1280,height=400,resizable=yes');
	Ext.define('showFlowHistoryModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'id',
			type : 'string'
		}, {
			name : 'shellPath',
			type : 'string'
		}, {
			name : 'projectName',
			type : 'string'
		}, {
			name : 'description',
			type : 'string'
		}, {
			name : 'flowName',
			type : 'string'
		}, {
			name : 'actname',
			type : 'string'
		}, {
			name : 'system',
			type : 'string'
		}, {
			name : 'instanceName',
			type : 'string'
		}, {
			name : 'status',
			type : 'string'
		}, {
			name : 'strStartTime',
			type : 'string'
		}, {
			name : 'strEndTime',
			type : 'string'
		}, {
			name : 'startUserName',
			type : 'string'
		}, {
			name : 'flowState',
			type : 'string'
		} ]
	});

	var showFlowHisStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'showFlowHistoryModel',
		pageSize : 25,
		proxy : {
			type : 'ajax',
			url : 'showflowHistoryListForHis.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});

	showFlowHisStore.on('beforeload', function(s) {
		var params = s.getProxy().extraParams;
		Ext.apply(params, {
			action : 'actretryHis',
			flowid : flowid,
			actname : name,
			endtime : endtime,
			status : status,
			prjName : prjName,
			flowName : flowName,
			insDate : insDate,
			isSystem : isSystem
		});
	});
	var showFlowpageBar = Ext.create('Ext.PagingToolbar', {
		store : showFlowHisStore,
		dock : 'bottom',
		displayInfo : true
		,baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
	});

	var showFlowGrid = Ext.create('Ext.grid.Panel', {
		title : '历史展示',
		split : true,
		multiSelect : true,
		emptyText : '没有流程详细信息',
		width : contentPanel.getWidth() - 100,
		height : contentPanel.getHeight(),
		columnLines : true,
		store : showFlowHisStore,
		columns : [ {
			text : '序号',
			xtype : 'rownumberer',
			align:'left',
			width : 40
		}, {
			text : '工作流ID',
			dataIndex : 'id',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '启动用户',
			dataIndex : 'startUserName',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '脚本路径',
			dataIndex : 'shellPath',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '业务系统',
			dataIndex : 'projectName',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '流程名称',
			dataIndex : 'flowName',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '活动名',
			dataIndex : 'actname',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '系统名',
			dataIndex : 'system',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '数据日期',
			dataIndex : 'instanceName',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '状态',
			dataIndex : 'flowState',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '开始时间',
			dataIndex : 'strStartTime',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		}, {
			text : '结束时间',
			dataIndex : 'strEndTime',
			flex : 1,
			sortable : true,
			editor : {
				allowBlank : false
			}
		} ],
		bbar : showFlowpageBar
	});
	var showFlowHistoryInfoWin = null;
	if (showFlowHistoryInfoWin == null || !showFlowHistoryInfoWin.isVisible()) {
		showFlowHistoryInfoWin = Ext.create('Ext.window.Window', {
			title : '流程信息展示',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : contentPanel.getWidth() - 100,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			layout : 'fit',
			items : [ showFlowGrid ]
		});
	}
	showFlowHistoryInfoWin.show();
}

function refreshClick(val){

	if (!oam_act_form.getForm().isValid()) {
		Ext.Msg.alert('提示', "自动刷新时间不符合要求!");
		return;
	}

	if (val == true) {
		var refreshTime = oam_act_form.queryById(
			'refreshTime').getValue();
		autoFresh(refreshTime);
	} else {
		// Ext.Msg.alert('提示', "已取消自动刷新"); //取消自动刷新
		// 不提示
	stopAutoFresh();
	}
}

function openMainFlowState(flowId,flowName,prjName,isExcelDayStartUpload){
	if(isExcelDayStartUpload == "true"){
		Ext.MessageBox.alert("提示", "该流程为日启动流程，不可查看!");
	} else {
		var upldMainFlowWin = Ext.create('Ext.window.Window', {
			id : 'upldMainFlowWinId',
			title : '工作流执行状态图',
			width : contentPanel.getWidth(),
			height : contentPanel.getHeight(),
			maximizable : true,
			layout : 'border',
			modal : true,
			closeAction : 'destroy',
			loader: { 
	            url: 'flow/showMainFlowState.do ',
	            autoLoad: true, 
	            scripts: true,
	            params : {
	            	flowId : flowId,
	            	flowName : flowName,
	            	prjName : prjName
	            }
	        } 
		}).show();
	}
	
}

function doubleCheckBatch(type){
	var doubleCheckBatchWin = null;
	var flowids='';
	var taskids='';
	var isHidden = false;
	// var el = document.getElementsByTagName('input');
	// var len = el.length;
	var grid = Ext.getCmp('home_grid')
	var selectedRecords = grid.getSelectionModel().getSelection();
	for (var i = 0 ; i < selectedRecords.length; i++) {
		var selectedRecord = selectedRecords[i];
		var taskid = selectedRecord.raw.flowId;
		taskids = taskids + taskid + ",";
	}
	// for (var i = 0; i < len; i++) {
	// 	if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
	// 			&& (el[i].disabled == false)) {
	// 		if (el[i].checked && el[i].value != '') {
	// 			taskids = taskids + el[i].value + ",";
	// 			el[i].disabled = true;
	// 		}
	// 	}
	// }
	if (taskids=="") {
        Ext.MessageBox.alert("提示", "请选择要操作的作业记录");
        return;
    }
//	 if(type==1 || type==2){
//	 //重试和略过
//		 var actJson = getSelectedActKeyInfoList(1);
//		 	if(actJson=='false'){
//				Ext.MessageBox.alert("提示", "请选择状态为异常或者挂起的活动记录");
//				return;
//		 	}else{
//		 		 flowids= actJson.split("#@#")[0];
//				 taskids=actJson.split("#@#")[1];
//		 	}
	/** 审核人列表model* */
	Ext.define ('AuditorModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'loginName',
	                type : 'string'
	            },
	            {
	                name : 'fullName',
	                type : 'string'
	            }
	    ]
	});
		var auditorStore = Ext.create ('Ext.data.Store',
		{
		    autoLoad : true,
		    model : 'AuditorModel',
		    proxy :
		    {
		        type : 'ajax',
		        url : 'getAuditorListForExecmonitor.do',
		        reader :
		        {
		            type : 'json',
		            root : 'dataList'
		        }
		    }
		});
	//审核人列表combo
		var auditorComBox = Ext.create ('Ext.form.ComboBox',
		{
			padding : '0 0 0 5',
		    editable : false,
		    fieldLabel : "审核人",
		    labelWidth : 50,
		    store : auditorStore,
		    queryMode : 'local',
		    width : 250,
		    displayField : 'fullName',
		    valueField : 'loginName' ,
		    listeners :
		    {
		        beforequery : function (e)
		        {
			        var combo = e.combo;
			        if (!e.forceAll)
			        {
				        var value = e.query;
				        combo.store.filterBy (function (record, id)
				        {
					        var text = record.get (combo.displayField);
					        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
				        });
				        combo.expand ();
				        return false;
			        }
		        }
		    }
		});
	 /** 双人复核发起审核按钮* */
		var doubleCheckButton = Ext.create ("Ext.Button",
		{
		    cls : 'Common_Btn',
		    textAlign : 'center',
		    text : "发起审核",
		    handler : doubleCheckFunction
		});
	// 双人复核发起审核按钮
		function doubleCheckFunction(btn){
			Ext.MessageBox.buttonText.yes = "确定";
			Ext.MessageBox.buttonText.no = "取消";
			Ext.Msg.confirm ("确认发起审核", "是否确认发起审核?", function (id)
			{
				if (id == 'yes')
					doubleCheckIns (type);
			});
		}
		function doubleCheckIns(type){
			var auditorValue = auditorComBox.getValue ();
			if (trim (auditorValue) == '' || (null == auditorValue))
			{
				Ext.MessageBox.alert ("提示", "请选择审核人");
				return false;
			}
			Ext.Ajax.request (
					{
					    url : 'execActmonitorDoublecheck.do',
					    method : 'POST',
					    params :
					    {
					    	type:type,
					    	flowids:taskids,
					    	taskids : taskids,
					    	auditUser:auditorValue
					    	 
					    },
					    success : function (response, options)
					    {
						    var success = Ext.decode (response.responseText).success;
						    var message = Ext.decode (response.responseText).message;
						    if (success==true)
						    {
						    	Ext.Msg.alert ('提示', message,function(){
						    		for (var i = 0; i < len; i++) {
						    			if ((el[i].type == "checkbox") && (el[i].name == 'taskItem')
						    					&& (el[i].disabled == false)) {
						    				if (el[i].checked && el[i].value != '') {
						    					el[i].disabled = true;
						    				}
						    			}
						    		}
						    		if(doubleCheckBatchWin != null){
						    			doubleCheckBatchWin.close();
						    		}
						    	});
						    }
						    else
						    {
							    Ext.MessageBox.show (
							    {
							        width : 300,
							        title : "提交失败",
							        msg : message,
							        buttonText :
							        {
								        yes : '确定'
							        },
							        buttons : Ext.Msg.YES
							    });
						    }
					    },
					    failure : function(result, request) {//alert(result);
							secureFilterRs(result,"操作失败！");

						}
					
					});
		}
		Ext.define('doubleCheckBatchModel', {
			extend : 'Ext.data.Model',
			fields : [  {
				name : 'DATADATE',
				type : 'string'
			}, {
				name : 'PRJNAME',
				type : 'string'
			}, {
				name : 'ACTNAME',
				type : 'string'
			}, {
				name : 'SYSTEM',
				type : 'string'
			}, {
				name : 'STATE',
				type : 'string'
			}, {
				name : 'NODEID',
				type : 'string'
			}, {
				name : 'DESC',
				type : 'string'
			}, {
				name : 'MAINPRJNAME',
				type : 'string'
			}  ]
		});

		var doubleCheckBatchStore = Ext.create('Ext.data.Store', {
			autoLoad : true,
			autoDestroy : true,
			model : 'doubleCheckBatchModel',
			pageSize : 100,
			proxy : {
				type : 'ajax',
				url : 'queryDoubleCheckBatch.do',
				reader : {
					type : 'json',
					root : 'dataList',
					totalProperty : 'total'
				}
			}
		});
		doubleCheckBatchStore.on('beforeload', function(s) {
			var params = s.getProxy().extraParams;
			Ext.apply(params, {
				flowids:taskids,
				type : type
			});
		});
	 
		var doubleCheckBatchGrid  = Ext.create('Ext.grid.Panel', {
				cls:'customize_panel_back',
				ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
				padding : panel_margin,
				border:false,
				region : 'center',
				columnLines : true,
				store : doubleCheckBatchStore,
				columns : [ {
					text : '序号',
					align : 'left',
					xtype : 'rownumberer',
					width : '5%'
				}, 
				{
					text : '数据日期',
					align : 'left',
					dataIndex : 'DATADATE',
					width: '10%',
					sortable : true,
					hidden :type == 6 || type == 7
				}, {
					text : '工程名',
					align : 'left',
					dataIndex : 'MAINPRJNAME',
					width:  '20%',
					hidden :!isHidden||type == 5,
					sortable : true,
					editor : {
						allowBlank : false
					}
				}, {
					text : '所属分类',
					align : 'left',
					dataIndex : 'SYSTEM',
					width: isHidden?'16%':'12%',
							sortable : true
				}, {
					text : '系统名称',
					align : 'left',
					dataIndex : 'PRJNAME',
					width: isHidden?'16%':'12%',
							sortable : true
				}, {
					text : '作业名称',
					align : 'left',
					dataIndex : 'ACTNAME',
					width: isHidden?'30%':'15%',
							sortable : true
				}, {
					text : '作业状态',
					align : 'left',
					dataIndex : 'STATE',
					width: '10%',
					sortable : true,
					hidden :isHidden
				}, {
					text : 'NODEID',
					align : 'left',
					dataIndex : 'NODEID',
					flex : 1,
					width:'10%',
					sortable : true,
					hidden :isHidden
				}, {
					text : '作业描述',
					align : 'left',
					dataIndex : 'DESC',
					width:'10%',
					flex : 1,
					sortable : true,
					hidden :isHidden
				}]
			
			});

		var dTitle = '';
		if(type==1){
			dTitle = '重试操作复核';
		}else if(type==2){
			dTitle = '跳过操作复核';
		}
		if (doubleCheckBatchWin == null || !doubleCheckBatchWin.isVisible()) {
			doubleCheckBatchWin = Ext.create('Ext.window.Window', {
				title : dTitle,
				modal : true,
				closeAction : 'destroy',
				constrain : true,
				autoScroll : true,
				 
				width : contentPanel.getWidth() /2+200,
				height : contentPanel.getHeight()/2+200,
				draggable : false,// 禁止拖动
				resizable : false,// 禁止缩放
				layout : 'border',
				items : [doubleCheckBatchGrid ],
			    dockedItems : [
				    {
				        dock : 'bottom',
				        xtype : 'toolbar',
				        border : false,
				        items : [
				                 {
				                	 xtype: 'tbseparator'
				 		        },	       
				 		        {
				 		            xtype: 'tbfill'
				 		        },auditorComBox,doubleCheckButton
				        ]
				    }
			    ]
			});
		}
		doubleCheckBatchWin.show();
}

//根据状态，改变行的颜色
function changeRowClassJobMonitor(record, rowIndex, rowParams, store) {
	if (record.data.status.indexOf("失败")>0 ||
		record.data.status.indexOf("异常")>0 ||
		(record.data.status == "就绪" || record.data.status.indexOf("就绪")>0) ) {
		return 'x-grid-Yellow';
	}
}

//根据状态，改变单元格的颜色
function actMonitorStateJobName(value, metaData, record) {
	if (record.data.status.indexOf("失败")>0 ||
		record.data.status.indexOf("异常")>0) {
		metaData.tdCls = 'x-grid-td-yellow';
	}
	return value
}

function showTopo(projectName,actName,queryDate){
	console.log(projectName+":"+actName+":"+queryDate);
	//跳转topo依赖展示图
	var url = 'newTopoScreenDisplayGanttPic.do?projectName='+projectName+'&firstFlowName='+actName+'&queryDate=' + queryDate + '&refreshTime=600';
	console.log('跳转URL:', url);
	window.open(url, '_blank'); // 在新标签页中打开
}
