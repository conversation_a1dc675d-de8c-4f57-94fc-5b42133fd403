Ext.onReady(function() {
	//清理主面板的各种监听时间
	destroyRubbish();
	var pagelimit = 30;
	
	Ext.define('orderModel', {
	    extend : 'Ext.data.Model',
	    fields : [ {
	      name : 'templateType',
	      type : 'string'
	    }, {
	      name : 'templateName',
	      type : 'string'
	    },
	    {
	    	name : 'orderName',
	    	type : 'string'
	    },{
	    	name : 'createUser',
	    	type : 'string'
	    },{
	    	name :'createTime',
	    	type:'string'
	    },{
	    	name :'status',
	    	type:'string'
	    }
	    
	    ]
	});

   var orderStore = Ext.create('Ext.data.Store',{
	  autoLoad : true,
	  autoDestroy : true,
	  model : 'orderModel',
	  proxy : {
		  type : 'ajax',
		  url : 'getOrderHistoryList.do',
		  reader : {
			  type : 'json',
			  root : 'dataList'
		  }
	  }
  });
  
  var pagebar = Ext.create('Ext.PagingToolbar', {
		pageSize:pagelimit, 
		dock: 'bottom',
		id: 'pageBarId',
		store:orderStore,  
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		displayInfo: true,     
		displayMsg: '显示 {0}-{1}条记录，共 {2} 条',     
		emptyMsg: "没有记录"
	});
  
  var orderColumns = [
	  {text : '模板类型', dataIndex : 'templateType', width:170,editor:{allowBlank:false}},
	  {text : '模板名称', dataIndex : 'templateName', width:350,editor:{allowBlank:false}},
	  {text : '工单名称',dataIndex : 'orderName', width:350,editor:{allowBlank:false}},
	  {text : '创建人',dataIndex : 'createUser', width:200,editor:{allowBlank:false}},
	  {text : '创建时间',dataIndex : 'createTime', width:200,editor:{allowBlank:false}},
	  {text : '状态',dataIndex : 'status', width:200,editor:{allowBlank:false}},
	  {
			text : '操作',
			xtype : 'actiontextcolumn',
			width : 120,
			align : 'left',
			items : [
					{
						text : '查看',
						iconCls : 'monitor_search',
						handler : function(grid, rowIndex) {
							if(rowIndex == 4){
								var url = "initCloudMonitorPage2.do";
								contentPanel.getLoader().load({
									url : url,
									scripts : true,
									params : {
										
									},
									callback : function(records, operation, success) {
										showImg(url);
									}
								});

							}
							var templateVal = grid.getStore().data.items[rowIndex].data.templateType;
							forwardDeatil(templateVal);
						}
					}]
	    }
  ];

  
  var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
	    clicksToEdit: 2
  });
  
  /** 导入按钮* */
	var importButton = Ext.create ("Ext.Button",
	{
		id : 'importBtn_id',
	    cls : 'Common_Btn',
	    text : "导入",
	    hidden:true,
	    handler : function(){
	    	Ext.Msg.alert('提示', 'Excel模板将通过弹出框进行导入！');
	    }
	});
	
	/** 左侧导出按钮* */
	var exportTM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '导出',
	    handler : function(){
	    	Ext.Msg.alert('提示', 'Excel模板文件将通过浏览器下载至本地！');
	    }
	});
	
  var formMainPanel = Ext.create('Ext.form.Panel', {

		region : 'north',
	  	layout : 'anchor',
	  	buttonAlign : 'center',
	  	iselect : false,
	  	 bodyCls : 'x-docked-noborder-top',
		    baseCls:'customize_gray_back',
	    border: false,
	    dockedItems : [
	    	{
	  		  xtype : 'toolbar',
	  		  baseCls:'customize_gray_back',
	  		  items : [
	  		        {	
	  	            	fieldLabel: '模板名称',
	  		            name:'templateName',
	  		            labelAlign : 'right',
	  		            width : '23%',
	  		            labelWidth : 70,
	  		            xtype: 'textfield'
	  	            },
	  	          {
	  		            text : '查询',
	  		            cls : 'Common_Btn',
	  		            handler : function(){
	  		            	//orderStore.load();
	  		            	queryBtnFun();
	  		            }
	  		      },
	  		      '->',
	  		      //importButton,
	  		      exportTM
	  		  ]
	  	  }
		]
	});
  
  var orderGrid = Ext.create('Ext.grid.Panel',{
	  width: contentPanel.getWidth(),
	  cls:'customize_panel_back',
	  region : 'center',
	  forceFit: true,
	  store : orderStore,
	  selModel : Ext.create('Ext.selection.CheckboxModel',{checkOnly:true}),
	  plugins: [cellEditing],
	  border : false,
	  columnLines : true,
	  columns : orderColumns,
	  animCollapse : false,
	  autoScroll:true,
	  bbar : pagebar,
	  listeners : {
		  beforeedit : function(editor, e, eOpts){
			  /*if(e.field == 'errcode' && e.record.data.errcode!=""){
				  return false;
			  }else{
				  return true;
			  }*/
			  return true;
		  }
	  }
  });
  orderStore.on('beforeload',function (store,options){
	  var templateName = formMainPanel.getForm().findField("templateName").getValue();
	  Ext.apply(orderStore.proxy.extraParams,{
		  templateName:templateName
	  });
  });
  
 
  var mainPanel =  Ext.create('Ext.panel.Panel',{
      renderTo : "order_grid_area",
      border : false,
      layout : 'border',
      bodyPadding : 0,
      width : contentPanel.getWidth (),
	  height : contentPanel.getHeight ()-modelHeigth,
      items : [formMainPanel,orderGrid]
  });
  
  contentPanel.on('resize',function(){
	  mainPanel.setHeight(contentPanel.getHeight()-5);
  })
  
  orderGrid.getSelectionModel().on('selectionchange', function(selModel, selections) {
	  mainPanel.down('#delete').setDisabled(selections.length === 0);
  });


  /* 解决IE下trim问题 */
  String.prototype.trim = function() {
	  return this.replace(/(^\s*)|(\s*$)/g, "");
  };
 
  // 当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
	  contentPanel.getHeader().show();
	  Ext.destroy(orderGrid);
	  if(Ext.isIE){
		  CollectGarbage(); 
	  }
  });
    
  /* 解决IE下trim问题 */
  String.prototype.trim=function(){
      return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  	
  	
  function queryBtnFun() {
	if (Ext.isIE) {
		CollectGarbage();
	}
	pagebar.moveFirst();
  }
  
  function forwardDeatil(templateVal) {
	
		contentPanel.getLoader().load(
		{
			url : "forwardDetail.do",
			scripts : true,
			params : {
				templateVal :templateVal
			}
		});
	
	

}
  
});
