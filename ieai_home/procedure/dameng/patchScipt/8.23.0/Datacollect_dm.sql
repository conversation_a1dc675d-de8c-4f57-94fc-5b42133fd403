DECLARE
LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_OBJECTS WHERE OBJECT_NAME = 'IEAI_CMDB_SYNC_DATA' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
                    LS_SQL := 'CREATE TABLE IEAI_CMDB_SYNC_DATA  (SYNC_TIME varchar2(16) NOT NULL,CMDBID varchar2(128) NOT NULL,HOST_NAME varchar2(64),IP varchar2(64),SERVER_TYPE varchar2(64) ,GATEWAY varchar2(64) ,OS_VERSION varchar2(64) ,OS_TYPE varchar2(64) ,KERNEL_VERSION varchar2(64) ,TAGS CLOB NULL,SYSTEM_NAMES CLOB ,STAFFS CLOB ,SYNC_TYPE decimal(1, 0) ,ENVIRONMENT VARCHAR2(20),CONSTRAINT PK_IEAI_CMDB_SYNC_DATA PRIMARY KEY (SYNC_TIME, CMDBID))';
EXECUTE IMMEDIATE LS_SQL;
END IF;
COMMIT;
END;
/

DECLARE
LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDB_SYNC_DATA' AND COLUMN_NAME='ENVIRONMENT';
IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_CMDB_SYNC_DATA ADD ENVIRONMENT VARCHAR2(20) ';
EXECUTE IMMEDIATE LS_SQL;
END IF;
COMMIT;
END;
/


