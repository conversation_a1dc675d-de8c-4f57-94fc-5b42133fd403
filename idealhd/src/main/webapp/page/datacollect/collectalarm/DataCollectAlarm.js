Ext.onReady(function(){
	Ext.define('projectModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'iwarnmsg',
            type: 'string'
        },
        {
            name: 'warntime',
            type: 'string'
        },
        {
            name: 'iwarntype',
            type: 'string'
        },
        {
            name: 'iwarnip',
            type: 'string'
        }]
	});
        var gridColumns = [ {
        	text : '序号',
        	width : 60,
        	xtype : 'rownumberer'
        }, {
        	text : 'iid',
        	dataIndex : 'iid',
        	hidden:true,
        	width : 100
        }, {
        	text : '告警时间',
        	dataIndex : 'warntime',
        	width : 200
        }, {
        	text : '告警信息',
        	dataIndex : 'iwarnmsg',
        	renderer :function(value, metaData, record){
      		  metaData.tdAttr = 'data-qtip="' + value + '"';
        		  return value;
 			},
        	flex:1
        }, {
        	text : '告警类型',
        	dataIndex : 'iwarntype',
        	hidden : true,
        	width : 200,
			flex:0.5
        }, {
        	text : '告警ip',
        	dataIndex : 'iwarnip',
        	width : 200,
			hidden : true,
			flex:1
        }];


        var projectStore = Ext.create('Ext.data.Store', {
        	autoLoad : true,
        	autoDestroy : true,
        	model : 'projectModel',
        	proxy : {
    			type : 'ajax',
    			url : 'getDataCollectAlarmList.do',
    			reader : {
    				type : 'json',
    				root : 'dataList'
    			}
    		}
        });
        var delBtn = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '处理',
        	handler : onDeleteListener
        });
        var form = Ext.create('Ext.form.Panel', {
        	buttonAlign : 'right',
        	region : 'north',
        	border : false,
        	baseCls : 'customize_gray_back',
        	items : [ {
        		xtype : 'toolbar',
        		baseCls : 'customize_gray_back',
        		items : [ '->',delBtn]
        	} ]
        });
        var grid_panel = Ext.create('Ext.ux.ideal.grid.Panel',
		{
			store : projectStore,
			region : 'center',
			ipageBaseCls : Ext.baseCSSPrefix+ 'toolbar customize_toolbar',
			selModel : Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : false,
				mode : "MULTI"
			}),
			cls : 'customize_panel_back',
			border : true,
			columnLines : true,
			columns : gridColumns,
			collapsible : false
		});

        function onDeleteListener() {
        	var record = grid_panel.getSelectionModel().getSelection();
        	if (record.length == 0) {
        		Ext.Msg.alert('提示', '请先选择您要操作的行!');
        		return
        	}
        	Ext.MessageBox.confirm('提示','是否删除选中数据!',
					function(btn) {
						if (btn == 'no') {
							return
						}
						if (btn == 'yes') {
							Ext.MessageBox.wait('数据处理中...', '进度条');
							var ids = [];
							Ext.Array.each(record, function(recordObj) {
								var cpId = recordObj.get('iid');
								if (-1 != cpId) {
									ids.push(cpId)
								}
							});
							Ext.Ajax.request({
										url : 'deleteDataCollectAlarm.do',
										params : {
											deleteIds : ids.join(',')
										},
										method : 'POST',
										success : function(response, opts) {
											var success = Ext
													.decode(response.responseText).success;
											if (success) {
												grid_panel.down(
														'pagingtoolbar')
														.moveFirst()
											}
											Ext.Msg.alert('提示',Ext.decode(response.responseText).message)
										},
										failure : function(response,ooptions) {
											Ext.MessageBox.hide();
											Ext.Msg.alert('提示', '请求超时！')
										}
									})
						}
					})
        }
        var mainPanel = Ext.create('Ext.panel.Panel', {
        	renderTo : 'DataCollectAlarm_div',
        	layout : 'border',
        	border : false,
        	width : '100%',
        	height : '100%',
        	items : [form,grid_panel ]
        });
});
