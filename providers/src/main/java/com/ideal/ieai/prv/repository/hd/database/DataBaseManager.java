package com.ideal.ieai.prv.repository.hd.database;

import com.ideal.dubbo.models.DataBaseModel;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

public class DataBaseManager
{

    private static final Logger _log = Logger.getLogger(DataBaseManager.class);

    public DataBaseManager()
    {

    }

    static private DataBaseManager _intance = new DataBaseManager();

    static public DataBaseManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new DataBaseManager();
        }
        return _intance;
    }

    class com<T> implements Comparator<T>
    {
        public int compare ( T o1, T o2 )
        {
            int i = Integer.parseInt(String.valueOf(o1));
            int j = Integer.parseInt(String.valueOf(o2));
            if (i > j)
                return 1;
            if (i < j)
                return -1;
            return 0;
        }
    }

    /**
     * 
     * <li>Description:保存</li> 
     * <AUTHOR>
     * 2017年7月13日 
     * @param list
     * @return
     * return Map
     */
    public boolean saveDataBase ( List<DataBaseModel> list ) throws RepositoryException, ServerException
    {

        Connection conn = null;
        PreparedStatement savePst = null;
        PreparedStatement updPst = null;
        try
        {
            conn = DBResource.getConnection("saveDataBase", _log, Constants.IEAI_SCRIPT_SERVICE);
        } catch (Exception e)
        {
            throw new RepositoryException(ServerError.ERR_DB_INIT);
        }

        try
        {
            String saveSql = "insert into IEAI_SCRIPT_DATABASE(IDATABASEID, IDATABASENAME, IDATABASEMEMO, IDATABASEIP, IDATABASEDISK, IDATABASEDIRECTORY,IDATABASETYPE,IDATABASEVERSION) values(?,?,?,?,?,?,?,?)";

            String updSql = "update IEAI_SCRIPT_DATABASE a set a.IDATABASENAME=? , a.IDATABASEMEMO=?, a.IDATABASEIP=? , a.IDATABASEDISK=? , a.IDATABASEDIRECTORY=?, a.IDATABASETYPE=?, a.IDATABASEVERSION=?  where   a.IDATABASEID=?";
            savePst = conn.prepareStatement(saveSql);
            updPst = conn.prepareStatement(updSql);

            for (DataBaseModel dataBaseModel : list)
            {
                long id = IdGenerator.createId("IEAI_SCRIPT_DATABASE", conn);
                if (dataBaseModel.getIdatabaseid() > 0)
                {
                    updPst.setString(1, dataBaseModel.getIdatabasename());
                    updPst.setString(2, dataBaseModel.getIdatabasememo());
                    updPst.setString(3, dataBaseModel.getIdatabaseip());
                    updPst.setString(4, dataBaseModel.getIdatabasedisk());
                    updPst.setString(5, dataBaseModel.getIdatabasedirectory());
                    updPst.setString(6, dataBaseModel.getIdatabasetype());
                    updPst.setString(7, dataBaseModel.getIdatabaseversion());
                    updPst.setLong(8, dataBaseModel.getIdatabaseid());
                    updPst.addBatch();
                } else
                {
                    savePst.setLong(1, id);
                    savePst.setString(2, dataBaseModel.getIdatabasename());
                    savePst.setString(3, dataBaseModel.getIdatabasememo());
                    savePst.setString(4, dataBaseModel.getIdatabaseip());
                    savePst.setString(5, dataBaseModel.getIdatabasedisk());
                    savePst.setString(6, dataBaseModel.getIdatabasedirectory());
                    savePst.setString(7, dataBaseModel.getIdatabasetype());
                    savePst.setString(8, dataBaseModel.getIdatabaseversion());
                    savePst.addBatch();
                }
            }

            savePst.executeBatch();
            updPst.executeBatch();
            conn.commit();
            return true;
        } catch (Exception ex)
        {
            ex.printStackTrace();
            return false;
        } finally
        {
            DBResource.closePSConn(conn, savePst, "saveDataBase", _log);
            DBResource.closePSConn(conn, updPst, "saveDataBase", _log);
        }

    }

    public boolean deleteDataBase ( Long[] deleteIds ) throws RepositoryException
    {

        boolean res = true;
        if (deleteIds.length == 0)
        {
            res = false;
        } else
        {
            String sql1 = " delete from IEAI_SCRIPT_DATABASE where  IDATABASEID in (" + StringUtils.join(deleteIds, ",")
                    + ")";

            PreparedStatement actStat = null;
            Connection con = null;
            try
            {
                con = DBResource.getConnection("deleteDataBase", _log, Constants.IEAI_SCRIPT_SERVICE);
                actStat = con.prepareStatement(sql1);
                actStat.executeUpdate();

                con.commit();
            } catch (Exception e)
            {
                e.printStackTrace();
                res = false;
            } finally
            {
                DBResource.closePSConn(con, actStat, "saveDataBase", _log);
            }

        }
        return res;
    }

    public Map listDataBase ( String page, String limit, String baseName ) throws RepositoryException
    {
        Map mapBack = new HashMap();
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement totalPs = null;
        ResultSet rset = null;
        ResultSet totalRset = null;
        List resultList = new ArrayList();
        try
        {
            conn = DBResource.getConnection("listDataBase", _log, Constants.IEAI_SCRIPT_SERVICE);
            int total = 0;
            long fromNum = 0;
            long toNum = 0;
            fromNum = ((Integer.parseInt(page) - 1) * Integer.parseInt(limit)) + 1;
            toNum = Integer.parseInt(page) * Integer.parseInt(limit);
            String totalSql = "";
            String sql = "";
            if (!"".equals(baseName) && null != baseName)
            {
                totalSql = "select count(1) from IEAI_SCRIPT_DATABASE where IDATABASENAME like '%" + baseName + "%'";

                sql = "SELECT * from (select ROW_NUMBER() OVER(order by t.IDATABASEID) AS RN , t.IDATABASEID, t.IDATABASENAME, t.IDATABASEMEMO,"
                        + " cp.AGENTIP AS IDATABASEIP, t.IDATABASEDISK, "
                        + " t.IDATABASEDIRECTORY,t.IDATABASETYPE,t.IDATABASEVERSION  FROM IEAI_SCRIPT_DATABASE t"
                        + " left join IEAI_COMPUTER_LIST cp on t.ICPID=cp.CPID " + " where t.IDATABASENAME like '%"
                        + baseName + "%'" + " ORDER BY t.IDATABASEID DESC) where  RN  BETWEEN  " + fromNum + "  AND  "
                        + toNum;
            } else
            {
                totalSql = "select count(1) from IEAI_SCRIPT_DATABASE ";

                sql = "SELECT * from (select ROW_NUMBER() OVER(order by t.IDATABASEID) AS RN , t.IDATABASEID, t.IDATABASENAME, t.IDATABASEMEMO,"
                        + " cp.AGENTIP AS IDATABASEIP, t.IDATABASEDISK, "
                        + " t.IDATABASEDIRECTORY,t.IDATABASETYPE,t.IDATABASEVERSION  FROM IEAI_SCRIPT_DATABASE t "
                        + " left join IEAI_COMPUTER_LIST cp on t.ICPID=cp.CPID "
                        + " ORDER BY t.IDATABASEID DESC) where  RN  BETWEEN  " + fromNum + "  AND  " + toNum;
            }

            totalPs = conn.prepareStatement(totalSql);
            totalRset = totalPs.executeQuery();
            while (totalRset.next())
            {
                total = totalRset.getInt(1);
                mapBack.put("total", total);
            }

            ps = conn.prepareStatement(sql);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("IDATABASEID", rset.getLong("IDATABASEID"));
                map.put("IDATABASENAME", rset.getString("IDATABASENAME"));
                map.put("IDATABASEMEMO", rset.getString("IDATABASEMEMO"));
                map.put("IDATABASEIP", rset.getString("IDATABASEIP"));
                map.put("IDATABASEDISK", rset.getString("IDATABASEDISK"));
                map.put("IDATABASEDIRECTORY", rset.getString("IDATABASEDIRECTORY"));
                map.put("IDATABASETYPE", rset.getString("IDATABASETYPE"));
                map.put("IDATABASEVERSION", rset.getString("IDATABASEVERSION"));

                resultList.add(map);
            }
            mapBack.put("dataList", resultList);
        } catch (Exception e)
        {
            _log.error("DataBaseManager.listDataBase Error", e);
        } finally
        {
            DBResource.closePSConn(conn, ps, "listDataBase", _log);
            DBResource.closePSConn(conn, totalPs, "listDataBase", _log);
        }
        return mapBack;
    }

    /**
     * 
     * <li>Description:根据数据库名判断数据库是否存在</li> 
     * <AUTHOR>
     * 2017年7月13日 
     * @param serverName
     * @return
     * return int
     */
    public boolean isExistDataBaseByName ( String name )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select count(1) from IEAI_SCRIPT_DATABASE where IDATABASENAME  in( " + name + ")";
        int count = 0;
        try
        {
            conn = DBResource.getConnection("isExistScript", _log, Constants.IEAI_SCRIPT_SERVICE);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt(1);
            }
        } catch (Exception e)
        {
            _log.error("selPlanname method of SwitchPlanManager.class SQLException:" + e.getMessage());
            e.printStackTrace();
            try
            {
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            } catch (RepositoryException e1)
            {
                // TODO Auto-generated catch block
                e1.printStackTrace();
            }
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "isExistScript", _log);
        }
        return count > 0 ? true : false;
    }

    /**
     * 
     * <li>Description:数据库服务器管理--ip选择窗口数据</li> 
     * <AUTHOR>
     * 2017年7月25日 
     * @param dsidStr
     * @param ipBeginStr
     * @param ipEndStr
     * @param startStr
     * @param limitStr
     * @return
     * return Map
     * @throws RepositoryException 
     */
    public Map getCpIpInfoList ( String dsidStr, String ipBeginStr, String ipEndStr, String startStr, String limitStr )
            throws RepositoryException
    {
        Map mapBack = new HashMap();
        // 处理参数
        long dsid = (null == dsidStr || "".equals(dsidStr)) ? 0 : Long.parseLong(dsidStr);
        int start = (null == startStr || "".equals(startStr)) ? 0 : Integer.parseInt(startStr);
        int limit = (null == limitStr || "".equals(limitStr)) ? 30 : Integer.parseInt(limitStr);

        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement totalPs = null;
        ResultSet rset = null;
        ResultSet totalRset = null;
        PreparedStatement chkPs = null;
        ResultSet chkRs = null;

        List resultList = new ArrayList();
        try
        {
            conn = DBResource.getConnection("getCpIpInfoList", _log, Constants.IEAI_SCRIPT_SERVICE);
            int total = 0;
            String checkedSql = " select ICPID from IEAI_SCRIPT_DATABASE where IDATABASEID=" + dsidStr;
            String sql = "select * from IEAI_COMPUTER_LIST cp where 1=1 ";

            // 查询条件处理
            String sqlWhere = "";
            // 起始IP(模糊匹配)
            String ipCon = "";
            if (null != ipBeginStr && !"".equals(ipBeginStr))
            {
                ipCon = " AND cp.AGENTIP LIKE '%" + ipBeginStr + "%' ";
            }
            sqlWhere += ipCon;
            sql += sqlWhere;

            String totalSql = "select count(*) cou from (" + sql + ") tmp ";
            // bagen.added by rongli_wang 2017/8/16
            String pageSQL = "";
            if (DBManager.Orcl_Faimily()) {
                pageSQL = SQLUtil.getQueryPageSQL("oracle", sql);
            } else if (DBManager.DB2_Faimily()) {
                    pageSQL = SQLUtil.getQueryPageSQLNew("db2", sql);
            }
            // end.added by rongli_wang 2017/8/16

            // 查询选中
            long checkCpId = -1;
            chkPs = conn.prepareStatement(checkedSql);
            chkRs = chkPs.executeQuery();
            if (chkRs.next())
            {
                checkCpId = chkRs.getLong("ICPID");
            }
            // 查询总数
            totalPs = conn.prepareStatement(totalSql);
            totalRset = totalPs.executeQuery();
            while (totalRset.next())
            {
                total = totalRset.getInt(1);
                mapBack.put("total", total);
            }
            // 查询数据
            ps = conn.prepareStatement(pageSQL);
            // bagen.added by rongli_wang 2017/8/16
            if (DBManager.Orcl_Faimily()) {
                    ps.setLong(1, start + limit);
                    ps.setLong(2, start);
            } else if (DBManager.DB2_Faimily()) {
                    ps.setLong(1, start);
                    ps.setLong(2, start + limit);
            }
            // end.added by rongli_wang 2017/8/16
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                long cpid = rset.getLong("CPID");
                map.put("icpId", cpid);
                map.put("icpName", rset.getString("CPNAME"));
                map.put("icpIp", rset.getString("IP"));
                map.put("iagentIp", rset.getString("AGENTIP"));
                map.put("icpPort", rset.getLong("CPPORT"));
                map.put("popular", rset.getLong("IFLAG"));
                map.put("checked", false);
                if (checkCpId == cpid)
                {
                    map.put("checked", true);
                }
                resultList.add(map);
            }
            mapBack.put("dataList", resultList);
        } catch (Exception e)
        {
            _log.error("DataBaseManager.getCpIpInfoList Error", e);
        } finally
        {
            DBResource.closePSRS(chkRs, chkPs, "getCpIpInfoList", _log);
            DBResource.closePSRS(totalRset, totalPs, "getCpIpInfoList", _log);
            DBResource.closePSConn(conn, totalPs, "getCpIpInfoList", _log);
        }
        return mapBack;
    }

    public boolean selectCpIpAndPort ( long dsid, long cpid ) throws RepositoryException
    {
        boolean returnValue = false;
        String sql1 = " update IEAI_SCRIPT_DATABASE set ICPID=" + cpid + " where IDATABASEID=" + dsid;

        PreparedStatement actStat = null;
        Connection con = null;
        try
        {
            con = DBResource.getConnection("selectCpIpAndPort", _log, Constants.IEAI_SCRIPT_SERVICE);
            actStat = con.prepareStatement(sql1);
            actStat.executeUpdate();
            con.commit();
            returnValue = true;
        } catch (Exception e)
        {
            e.printStackTrace();
        } finally
        {
            DBResource.closePSConn(con, actStat, "selectCpIpAndPort", _log);
        }
        return returnValue;
    }

}
