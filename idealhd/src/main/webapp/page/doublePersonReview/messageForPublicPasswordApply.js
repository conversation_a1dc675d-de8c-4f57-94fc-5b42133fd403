Ext.onReady(function() {
	destroyRubbish();
	Ext.QuickTips.init();
	
	Ext.define('iEmailModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iId',
			type : 'string'
		}, {
			name : 'iName',
			type : 'string'
		}, {
			name : 'iPassword',
			type : 'string'
		}, {
			name :'IEMAIL',
			type :'string'
		}]
	});
	
	// 数据存储 类似集合
	var iEmailStore=Ext.create('Ext.data.Store', {
//		autoLoad : true,
		autoDestroy : true,
		model : 'iEmailModel',   // 存储用户模型
		pageSize : 50,
		proxy : {   // 请求数据用
			type : 'ajax',
			url : 'getPublicEmail.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProterty : 'total'
			}
		}
	});
	
	// 数据加载之前加上查询条件
	iEmailStore.on('beforeload', (store, options)=> {
		var new_params = {
				selectName : "public"
	};
		Ext.apply(iEmailStore.proxy.extraParams, new_params);
	});
	
	iEmailStore.load({
		callback:function(r,options,success){
			var record = iEmailStore.getAt(0);//--显示第几行
			Ext.getCmp("newIEmail").setValue(record.get("IEMAIL"));
	  }});
	
	var formPanel = Ext.create('Ext.form.Panel',{
		border : false,
		region : 'north',
		bodyPadding : 5,
		store:iEmailStore,
		buttonAlign : 'center',
		width : 500,
		fieldDefaults : {
			labelWidth : 70,
			allowBlank : false,
			msgTarget : 'side',
			autoFitErrors : false
		},
		defaultType : 'textfield',
		items : [
			{
				xtype : 'displayfield',
				value : '公共账号public用户密码申请，点击申请按钮后，密码会发送到用户邮箱中',
				padding : 6,
			},{
				xtype : 'button',
				text : '申请',
				formBind : true, // only enabled once
				disabled : true,
				cls : 'Common_Btn',
				itemId:'applyButton',
				handler : function() {
					Ext.Msg.alert('提示',"邮件发送中 请稍等");
					Ext.Ajax.request( {
						url : 'publicPasswordApply.do',
						method : 'post',
						params : {
							selectName : "public",
							newIEmail : formPanel.getForm().findField('newIEmail').getValue(),
						},
						success : function(response,options) {
							var SUCCESS = Ext.decode(response.responseText).SUCCESS;
							var MESSAGE = Ext.decode(response.responseText).MESSAGE;
							if (SUCCESS) {
								Ext.Msg.alert('提示','申请成功!<br>密码已发送到邮箱 '+formPanel.getForm().findField('newIEmail').getValue()+" 中，请注意查收");
							} else {
								Ext.Msg.alert('提示',MESSAGE);
							}
						},
						failure : function(result, request) {
							Ext.Msg.alert('提示',MESSAGE);
						}
					});
				}
			},{
				xtype : 'displayfield',
				value : '<br>密码发送邮箱:',
				padding : 6				
			},{
				id : 'newIEmail',
				dateIndex:'iEmail',
				padding : 10,
				store:iEmailStore,
				width : 430,
				emptyText:'请输入邮箱',
				inputType : 'text',										
				maxLength : 40,
				listeners:{
					blur:checkEmail					
				}
			},{
				xtype : 'button',
				text : '保存',
				cls : 'Common_Btn',
				formBind : true, // only enabled once
				disabled : true,
				handler : function() {
						checkEmail();
						Ext.Ajax.request( {
							url : 'updateUserEmail.do',
							method : 'post',
							params : {
								newIEmail : formPanel.getForm().findField('newIEmail').getValue(),
								selectName : "public"
							},
							success : function(response,options) {
								var SUCCESS = Ext.decode(response.responseText).SUCCESS;
								var MESSAGE = Ext.decode(response.responseText).MESSAGE;
								if (SUCCESS) {
									Ext.Msg.alert('提示',"更新邮箱成功");
								} else {
									Ext.Msg.alert('提示',MESSAGE);
								}
							},
							failure : function(result, request) {
								Ext.Msg.alert('提示',MESSAGE);
							}
						});
	
				}
			
			} ],
		});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		renderTo: "messageForPublicPasswordApply",
		layout: 'border',
		width : contentPanel.getWidth(),
		height :contentPanel.getHeight() - modelHeigth,
		border: false,
		cls:'customize_panel_back chagedbpwd_label_space',
		items: [formPanel]
	});
	
	function checkEmail(){
		var reg=/^[a-zA-Z0-9]+([._\\-]*[a-zA-Z0-9])*@([a-zA-Z0-9]+[-a-zA-Z0-9]*[a-zA-Z0-9]+.){1,63}[a-zA-Z0-9]+$/;
		if(!reg.test(formPanel.getForm().findField('newIEmail').getValue())){
			Ext.Msg.alert("提示","邮箱输入格式不正确，请重新输入");
			this.up('form').getForm().reset();
		}
		
	}
	console.log(iEmailStore.getCount()+"++");
});