INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6014, '应急处置_预案管理', 6, 'planlist.do', 102, '应急操作','images/info98.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6016, '应急处置_应急监控', 6, 'forwardPlanEmMonitor.do', 100, '应急操作','images/info20.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6019, '应急处置_应急报表', 6, 'emreport.do',100, '应急操作','images/info26.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6021, '应急处置_场景管理', 6, 'scenelistnew.do',102, '应急操作','images/info99.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6022, '应急处置_预案发起', 6, 'emPlanStart.do',101, '应急操作','images/info62.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6023, '应急处置_预案执行', 6, 'dailyServicesPlanTaskExec.do',101, '应急操作','images/info37.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6020, '应急处置_预案启动', 6, 'emStartNew.do',101, '应急操作','images/info38.png');


INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6022, '保存', 'saveplan.do', '预案管理预案列表保存按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6023, '删除', 'deletePlanData.do', '预案管理预案列表删除按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6024, '保存', 'saveRelationData.do', '预案管理场景选定列表保存按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6025, '删除', 'deleteRelationData.do', '预案管理场景选定列表删除按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6018, '保存', 'savescene.do', '场景管理页面保存按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6019, '删除', 'deleteSceneData.do', '场景管理页面删除按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6020, '删除', 'deleteSsgridData.do', '场景管理页面应急服务页面删除按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6021, '保存', 'saveSSData.do', '场景管理页面应急服务页面保存按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6026, '保存', 'saveSSDataForInitiate.do', '预案发起常用任务列表保存按钮');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6022, 6014, 6022, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6023, 6014, 6023, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6024, 6014, 6024, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6025, 6014, 6025, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6027, 6021, 6018, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6028, 6021, 6019, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6029, 6021, 6020, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6030, 6021, 6021, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6031, 6022, 6026, '');