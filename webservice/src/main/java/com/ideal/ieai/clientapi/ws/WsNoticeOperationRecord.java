/**
 * WsNoticeOperationRecord.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.ideal.ieai.clientapi.ws;

public class WsNoticeOperationRecord  implements java.io.Serializable {
    private java.lang.String feedback;

    private java.lang.String flowInstanceName;

    private java.lang.String flowName;

    private long id;

    private java.util.Calendar occurTime;

    private int operation;

    private com.ideal.ieai.clientapi.ws.WsUserInfo operator;

    private java.lang.String taskName;

    public WsNoticeOperationRecord() {
    }

    public WsNoticeOperationRecord(
           java.lang.String feedback,
           java.lang.String flowInstanceName,
           java.lang.String flowName,
           long id,
           java.util.Calendar occurTime,
           int operation,
           com.ideal.ieai.clientapi.ws.WsUserInfo operator,
           java.lang.String taskName) {
           this.feedback = feedback;
           this.flowInstanceName = flowInstanceName;
           this.flowName = flowName;
           this.id = id;
           this.occurTime = occurTime;
           this.operation = operation;
           this.operator = operator;
           this.taskName = taskName;
    }


    /**
     * Gets the feedback value for this WsNoticeOperationRecord.
     * 
     * @return feedback
     */
    public java.lang.String getFeedback() {
        return feedback;
    }


    /**
     * Sets the feedback value for this WsNoticeOperationRecord.
     * 
     * @param feedback
     */
    public void setFeedback(java.lang.String feedback) {
        this.feedback = feedback;
    }


    /**
     * Gets the flowInstanceName value for this WsNoticeOperationRecord.
     * 
     * @return flowInstanceName
     */
    public java.lang.String getFlowInstanceName() {
        return flowInstanceName;
    }


    /**
     * Sets the flowInstanceName value for this WsNoticeOperationRecord.
     * 
     * @param flowInstanceName
     */
    public void setFlowInstanceName(java.lang.String flowInstanceName) {
        this.flowInstanceName = flowInstanceName;
    }


    /**
     * Gets the flowName value for this WsNoticeOperationRecord.
     * 
     * @return flowName
     */
    public java.lang.String getFlowName() {
        return flowName;
    }


    /**
     * Sets the flowName value for this WsNoticeOperationRecord.
     * 
     * @param flowName
     */
    public void setFlowName(java.lang.String flowName) {
        this.flowName = flowName;
    }


    /**
     * Gets the id value for this WsNoticeOperationRecord.
     * 
     * @return id
     */
    public long getId() {
        return id;
    }


    /**
     * Sets the id value for this WsNoticeOperationRecord.
     * 
     * @param id
     */
    public void setId(long id) {
        this.id = id;
    }


    /**
     * Gets the occurTime value for this WsNoticeOperationRecord.
     * 
     * @return occurTime
     */
    public java.util.Calendar getOccurTime() {
        return occurTime;
    }


    /**
     * Sets the occurTime value for this WsNoticeOperationRecord.
     * 
     * @param occurTime
     */
    public void setOccurTime(java.util.Calendar occurTime) {
        this.occurTime = occurTime;
    }


    /**
     * Gets the operation value for this WsNoticeOperationRecord.
     * 
     * @return operation
     */
    public int getOperation() {
        return operation;
    }


    /**
     * Sets the operation value for this WsNoticeOperationRecord.
     * 
     * @param operation
     */
    public void setOperation(int operation) {
        this.operation = operation;
    }


    /**
     * Gets the operator value for this WsNoticeOperationRecord.
     * 
     * @return operator
     */
    public com.ideal.ieai.clientapi.ws.WsUserInfo getOperator() {
        return operator;
    }


    /**
     * Sets the operator value for this WsNoticeOperationRecord.
     * 
     * @param operator
     */
    public void setOperator(com.ideal.ieai.clientapi.ws.WsUserInfo operator) {
        this.operator = operator;
    }


    /**
     * Gets the taskName value for this WsNoticeOperationRecord.
     * 
     * @return taskName
     */
    public java.lang.String getTaskName() {
        return taskName;
    }


    /**
     * Sets the taskName value for this WsNoticeOperationRecord.
     * 
     * @param taskName
     */
    public void setTaskName(java.lang.String taskName) {
        this.taskName = taskName;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof WsNoticeOperationRecord)) return false;
        WsNoticeOperationRecord other = (WsNoticeOperationRecord) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.feedback==null && other.getFeedback()==null) || 
             (this.feedback!=null &&
              this.feedback.equals(other.getFeedback()))) &&
            ((this.flowInstanceName==null && other.getFlowInstanceName()==null) || 
             (this.flowInstanceName!=null &&
              this.flowInstanceName.equals(other.getFlowInstanceName()))) &&
            ((this.flowName==null && other.getFlowName()==null) || 
             (this.flowName!=null &&
              this.flowName.equals(other.getFlowName()))) &&
            this.id == other.getId() &&
            ((this.occurTime==null && other.getOccurTime()==null) || 
             (this.occurTime!=null &&
              this.occurTime.equals(other.getOccurTime()))) &&
            this.operation == other.getOperation() &&
            ((this.operator==null && other.getOperator()==null) || 
             (this.operator!=null &&
              this.operator.equals(other.getOperator()))) &&
            ((this.taskName==null && other.getTaskName()==null) || 
             (this.taskName!=null &&
              this.taskName.equals(other.getTaskName())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getFeedback() != null) {
            _hashCode += getFeedback().hashCode();
        }
        if (getFlowInstanceName() != null) {
            _hashCode += getFlowInstanceName().hashCode();
        }
        if (getFlowName() != null) {
            _hashCode += getFlowName().hashCode();
        }
        _hashCode += new Long(getId()).hashCode();
        if (getOccurTime() != null) {
            _hashCode += getOccurTime().hashCode();
        }
        _hashCode += getOperation();
        if (getOperator() != null) {
            _hashCode += getOperator().hashCode();
        }
        if (getTaskName() != null) {
            _hashCode += getTaskName().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(WsNoticeOperationRecord.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "WsNoticeOperationRecord"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("feedback");
        elemField.setXmlName(new javax.xml.namespace.QName("", "feedback"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flowInstanceName");
        elemField.setXmlName(new javax.xml.namespace.QName("", "flowInstanceName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("flowName");
        elemField.setXmlName(new javax.xml.namespace.QName("", "flowName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("id");
        elemField.setXmlName(new javax.xml.namespace.QName("", "id"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("occurTime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "occurTime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("operation");
        elemField.setXmlName(new javax.xml.namespace.QName("", "operation"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("operator");
        elemField.setXmlName(new javax.xml.namespace.QName("", "operator"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "WsUserInfo"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("taskName");
        elemField.setXmlName(new javax.xml.namespace.QName("", "taskName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
