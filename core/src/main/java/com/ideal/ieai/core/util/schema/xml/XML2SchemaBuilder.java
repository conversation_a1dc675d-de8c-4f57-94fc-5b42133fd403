package com.ideal.ieai.core.util.schema.xml;

import com.ideal.ieai.core.basis.InvalidNameException;
import com.ideal.ieai.core.basis.InvalidTypeException;
import com.ideal.ieai.core.data.DataTypeNames;
import com.ideal.ieai.core.element.IEAISchemaDef;
import com.ideal.ieai.core.element.ISchemaNodeDef;
import com.ideal.ieai.core.element.SchemaLeafNodeDef;
import com.ideal.ieai.core.util.schema.ISchemaBuilder;
import com.ideal.ieai.core.util.schema.ImportException;
import com.ideal.ieai.core.util.schema.SchemaDirector;
import org.apache.log4j.Logger;
import org.jdom2.Attribute;
import org.jdom2.Document;
import org.jdom2.Element;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <p>Title: IEAI Core </p>
 * <p>Description: accept a document and convert to a schema</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 3.0
 */

public class XML2SchemaBuilder
    implements ISchemaBuilder
{
    /**
     * Logger for this class
     */
    private static final Logger _log = Logger
                                             .getLogger(XML2SchemaBuilder.class);

    public XML2SchemaBuilder()
    {
    }

    /**
     * translate xml documenet to ieai schema ,when error occurs,throw import importexception
     * @param schema
     * @param document
     * @param options
     * @return
     */
    public IEAISchemaDef buildSchema(IEAISchemaDef schema, Object document) throws
        ImportException
    {
        Document mydocument = (Document) document;
        Element elRoot = mydocument.getRootElement();

        Iterator it = elRoot.getAttributes().iterator();
        /**
         * add all attribute to the schema's child
         */while (it.hasNext())
        {
            Attribute att = (Attribute) it.next();
            String sName = att.getName();
            ISchemaNodeDef _node = null;
            try
            {
                //to all attribute ,create name start with __ ,and string type
                _node = new SchemaLeafNodeDef(schema, SchemaDirector.ATTIBUTE_FLAG + sName,
                                              DataTypeNames.TYPE_STRING);
            }
            catch (InvalidTypeException ex1)
            { //here not catch exception,type is 2
            }
            catch (InvalidNameException ex1)
            {
                _log.error(ex1.getMessage(), ex1);
                throw new ImportException(ImportException.INVALID_NAME, "invalid name");
            }
            schema.add(_node);
        }

        /**
         * Parse nodes, and record if a node is an array type in this map
         */
        Map isArrayNodeTypeMap = new HashMap();
        Iterator itRootChild = elRoot.getChildren().iterator();
        /**
         * add the childrens name to a map,if map contains the name ,we set the map value is true,sign a array type
         */while (itRootChild.hasNext())
        {
            Element elChild = (Element) itRootChild.next();
            String sName = elChild.getName();

            if (null != isArrayNodeTypeMap.get(sName))
            {
                //the sName appear one more time,we set map value true,it's a array type
                isArrayNodeTypeMap.put(sName, Boolean.TRUE);
            }
            else
            {
                isArrayNodeTypeMap.put(sName, Boolean.FALSE);
            }
        }

        ArrayTypeXMLDeserializer arrayTypeDe = new ArrayTypeXMLDeserializer();
        BasicTypeXMLDeserializer basicTypeDe = new BasicTypeXMLDeserializer();
        InnerSchemaXMLDeserizlier innerSchemaDe = new InnerSchemaXMLDeserizlier();
        MapTypeXMLDeserizlier mapTypeDe = MapTypeXMLDeserizlier.getInstance();

        Iterator itKey = isArrayNodeTypeMap.keySet().iterator();
        /**
         * according the map's value(true or false),if true,use ArrayTypeXMLDeserializer ,
         * if false use BasicTypeXMLDeserializer or InnerSchemaXMLDeserizlier
         */while (itKey.hasNext())
        {
            Object key = itKey.next();
            Boolean value = (Boolean)isArrayNodeTypeMap.get(key);
            //get the element according the key
            Element elChild  = elRoot.getChild( (String) key, elRoot.getNamespace());

            if (value.booleanValue())
            {
                //it is a array  type
                if (arrayTypeDe.canDeserialize(elChild))
                {
                    schema.add(arrayTypeDe.xmlToSchemaNode(elChild));
                }
            }
            else
            {
                if (basicTypeDe.canDeserialize(elChild))
                {
                    //it is a basic type
                    schema.add(basicTypeDe.xmlToSchemaNode(elChild));
                }
                else if(mapTypeDe.canDeserialize(elChild))
                {
                    // it is a map type
                    schema.add(mapTypeDe.xmlToSchemaNode(elChild));
                }
                else if (innerSchemaDe.canDeserialize(elChild))
                {
                    schema.add(innerSchemaDe.xmlToSchemaNode(elChild));
                }
            }
        }
        return schema;
    }

}
