package com.ideal.ieai.server.dm.bean;

import java.util.List;

/**
 * @ClassName:  CollLogCenInRequestMessage
 * @Description: 日志中心采集任务启动接口返回类
 * @author: ming_jia
 * @date:   2022年8月26日 下午3:32:06
 *
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved.
 *
 */
public class CollLogCenInRequestMessage {

    private boolean isOk;            // 是否成功
    private String  message;      // 错误提示
    private List<?> resultList;     // 返回的list列表数据结果集合
    private String collectName;     //采集名称
    private String  token;
    private String  user;
    private Long  nodeIid;       //节点id
    private Integer   total;//总条数
    private String sysname;//系统名称
    private String topic;//主题


    public boolean getIsOk ()
    {
        return isOk;
    }

    public void setIsOk ( boolean isOk )
    {
        this.isOk = isOk;
    }


    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List getResultList() {
        return resultList;
    }

    public void setResultList(List<?> resultList) {
        this.resultList = resultList;
    }

    public String getCollectName() {
        return collectName;
    }

    public void setCollectName(String collectName) {
        this.collectName = collectName;
    }


    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }


    public Long getNodeIid() {
        return nodeIid;
    }

    public void setNodeIid(Long nodeIid) {
        this.nodeIid = nodeIid;
    }

    public boolean isOk() {
        return isOk;
    }

    public void setOk(boolean ok) {
        isOk = ok;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getSysname() {
        return sysname;
    }

    public void setSysname(String sysname) {
        this.sysname = sysname;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }
}
