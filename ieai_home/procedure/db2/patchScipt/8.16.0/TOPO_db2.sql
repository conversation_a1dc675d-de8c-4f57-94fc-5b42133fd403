-- 4.7.17 version does not have patches for timed tasks
-- 4.7.18 version does not have patches for timed tasks

-- 4.7.19 version does not have patches for timed tasks

-- 4.7.20 version TOPO patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_SYS_AVGRECORD';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_SYS_AVGRECORD 				(				IPRJNAME     VARCHAR(255),				IAVGTIME     DECIMAL(19),				IT<PERSON>EMON<PERSON>   DECIMAL(19),				ITIMEDAY     DECIMAL(19),				ITIMEWEEK    DECIMAL(19)				)';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_ANALY_MODEL';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_TOPO_ANALY_MODEL(IID DECIMAL(19,0) NOT NULL,IPRJNAME VARCHAR(255),ISYSNAME VARCHAR(255),IINSTANCENAME VARCHAR(255),ISUBSYSNAME  VARCHAR(255),IANALYTIME DECIMAL(19),CONSTRAINT PK_IEAI_TOPO_ANALY_MODEL PRIMARY KEY (IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_SCREEN_AVGCFG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'CREATE TABLE IEAI_TOPO_SCREEN_AVGCFG(IID DECIMAL(19,0) NOT NULL,IDATADATE VARCHAR(25),ICFGDATE  VARCHAR(25),ISYSNAME  VARCHAR(255),CONSTRAINT PK_IEAI_TOPO_SCREEN_AVGCFG PRIMARY KEY(IID))';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;	
	
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_WARNING' AND COLNAME='IMSG';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_TOPO_WARNING ADD IMSG VARCHAR(1000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
END
	$
	CREATE	OR REPLACE	PROCEDURE PROC_COUNT_AVG_SYS (IN STARTTIME DECIMAL(19))
   LANGUAGE SQL
    BEGIN
	DECLARE PRJ_NAME VARCHAR(255); --工程名
	DECLARE SUMTIME VARCHAR(19); --平均耗时
    DECLARE LV_COUNT DECIMAL(19);
    DECLARE sqlcode integer default 0;
    DECLARE KEYSOR CURSOR FOR SELECT DISTINCT IPROJECTNAME  FROM IEAI_ACT_RELATION_MESS;
	--清理数据表--
    DELETE FROM IEAI_SYS_AVGRECORD;
     COMMIT;
    BEGIN
    DECLARE KEYSOR CURSOR FOR SELECT DISTINCT IPROJECTNAME  FROM IEAI_ACT_RELATION_MESS;
	--插入临时表--1、计算所有记录无规则数据的耗时，2、计算所有有规则记录耗时。
	--轮询名称--
     OPEN KEYSOR;
          cursorLoop:
      LOOP
            FETCH KEYSOR INTO PRJ_NAME;
             IF SQLCODE = 100 THEN
                    LEAVE cursorLoop;
             END IF;
    SET SUMTIME = 0;
	--查询该条件下是否有记录，否则INTO有问题
     SELECT COUNT(1)  INTO	LV_COUNT FROM
        (	SELECT *  FROM
                (	SELECT
                        COUNT(1)             AS TOTAL,
                        COUNT((CASE WHEN T.IACTSTATE IS NULL OR
                            T.IACTSTATE=1 THEN 1 END))             AS NRUN,
                        MAX(T.IREALENDTIME)   AS ENDTIME,
                        MIN(T.IREALBEGINTIME) AS STARTTIME,
                        T.IDATADATE 
                    FROM
                        IEAI_ACT_TOPO_INSTANCE T 
                    WHERE
                        T.IPROJECTNAME= PRJ_NAME 
                    GROUP BY
                        T.IDATADATE
                )
            WHERE
                NRUN=0 AND
                STARTTIME>=STARTTIME
        );
    IF LV_COUNT > 0 THEN 
        SELECT 	ROUND(AVG(ENDTIME - STARTTIME), 0)  INTO SUMTIME FROM
        (	SELECT *  FROM
                (SELECT
                        COUNT(1)             AS TOTAL,
                        COUNT((CASE WHEN T.IACTSTATE IS NULL OR
                            T.IACTSTATE=1 THEN 1 END))             AS NRUN,
                        MAX(T.IREALENDTIME)   AS ENDTIME,
                        MIN(T.IREALBEGINTIME) AS STARTTIME,
                        T.IDATADATE 
                    FROM
                        IEAI_ACT_TOPO_INSTANCE T 
                    WHERE
                        T.IPROJECTNAME= PRJ_NAME 
                    GROUP BY
                        T.IDATADATE
                )
            WHERE
                NRUN=0 AND
                STARTTIME>= STARTTIME
        );
    END IF; 
    INSERT INTO 
        IEAI_SYS_AVGRECORD 
        (
            IPRJNAME,
            IAVGTIME
        )
    VALUES
        (
            PRJ_NAME,
            SUMTIME
        );
       END LOOP;
    CLOSE KEYSOR;
	commit;
	END;
	END
$
	-- 创建存储过程

-- 4.7.21 version does not have patches for timed tasks
-- 4.7.22 version does not have patches for timed tasks
	
-- 4.7.23 version does not have patches for timed tasks

-- 4.7.24 version TOPO patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_RELATION_COMMAND';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TOPO_RELATION_COMMAND (IID DECIMAL(19)  NOT NULL, ITASKID DECIMAL(19) , INEXTTASKID DECIMAL(19) ,  INEXTMODELNAME VARCHAR(255) , CONSTRAINT PK_IEAI_TOPO_RELATION_COMMAND PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ABSGRAPH_CELL';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_ABSGRAPH_CELL (IID DECIMAL(19) NOT NULL,  IGRAPGXML  CLOB, INAME VARCHAR(255), IUSERID DECIMAL(19), IFLAG INTEGER, CONSTRAINT PK_IEAI_ABSGRAPH_CELL PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_SUB_COMMAND';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TOPO_SUB_COMMAND ( IID DECIMAL(19)  NOT NULL, IINSTRUCTNO VARCHAR(255), IIDENTIFIEDNO  VARCHAR(255), IGROUPNAME VARCHAR(255), IINSYSNAME VARCHAR(255),  IINFLOWNAME VARCHAR(255), ITASKCATEGORY  VARCHAR(255),  ISYSTEMNAME VARCHAR(255), ISYSTEMABBR VARCHAR(255), ITASKNAME VARCHAR(255), IRELEVANTSYSTEM VARCHAR(255), IREVIEWRECORD  VARCHAR(255),  IWORKTASK VARCHAR(255), ICOMMENTS VARCHAR(2000),  IPREPTASK VARCHAR(255), ISUCCESSORTASK VARCHAR(255),  IPRJTIMES VARCHAR(255), IBEGINTIME VARCHAR(255),  IFINISHTIME VARCHAR(255), IREALBEGINTIME DECIMAL(19), IREALFINISHTIME DECIMAL(19),  IRESCOMPANY VARCHAR(255), IRESPERSON VARCHAR(255),  ITASKEXECUTOR  VARCHAR(255), ISTATUS VARCHAR(255), IISFAIL INTEGER, INSTANCENAME VARCHAR(10) NOT NULL, IEXECNUM INTEGER DEFAULT 0, CONSTRAINT PK_IEAI_TOPO_SUB_COMMAND PRIMARY KEY (IID,INSTANCENAME))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_MAIN_COMMAND';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TOPO_MAIN_COMMAND (IID DECIMAL(19)  NOT NULL,IINSTRUCTNO VARCHAR(255),IIDENTIFIEDNO VARCHAR(255),IGROUPNAME VARCHAR(255),IINSYSNAME VARCHAR(255),IINFLOWNAME VARCHAR(255),ITASKCATEGORY VARCHAR(255),ISYSTEMNAME VARCHAR(255),ISYSTEMABBR VARCHAR(255),ITASKNAME VARCHAR(255),IRELEVANTSYSTEM VARCHAR(255),IREVIEWRECORD  VARCHAR(255),IWORKTASK VARCHAR(255),ICOMMENTS VARCHAR(2000),IPREPTASK VARCHAR(255),ISUCCESSORTASK VARCHAR(255),IPRJTIMES VARCHAR(255),IBEGINTIME VARCHAR(255),IFINISHTIME VARCHAR(255),IREALBEGINTIME DECIMAL(19),IREALFINISHTIME DECIMAL(19),IRESCOMPANY VARCHAR(255),IRESPERSON VARCHAR(255),ITASKEXECUTOR  VARCHAR(255), ISTATUS VARCHAR(255),IISFAIL INTEGER,INSTANCENAME VARCHAR(10),IEXECNUM INTEGER DEFAULT 0, CONSTRAINT PK_IEAI_TOPO_MAIN_COMMAND PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPOINFO_COMMAND';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TOPOINFO_COMMAND (IID DECIMAL(19)  NOT NULL,IINSTRUCTNO VARCHAR(255),IIDENTIFIEDNO  VARCHAR(255),IGROUPNAME VARCHAR(255),IINSYSNAME VARCHAR(255),IINFLOWNAME VARCHAR(255),ITASKCATEGORY  VARCHAR(255),ISYSTEMNAME VARCHAR(255),ISYSTEMABBR VARCHAR(255),ITASKNAME VARCHAR(255),IRELEVANTSYSTEM VARCHAR(255),IREVIEWRECORD  VARCHAR(255),IWORKTASK VARCHAR(255),ICOMMENTS VARCHAR(2000),IPREPTASK VARCHAR(255),ISUCCESSORTASK VARCHAR(255),IPRJTIMES VARCHAR(255),IBEGINTIME VARCHAR(255),IFINISHTIME VARCHAR(255),IREALBEGINTIME DECIMAL(19),IREALFINISHTIME DECIMAL(19),IRESCOMPANY VARCHAR(255),IRESPERSON VARCHAR(255),ITASKEXECUTOR  VARCHAR(255), ISTATUS VARCHAR(255), CONSTRAINT PK_IEAI_TOPOINFO_COMMAND PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_SUB_COMMAND_CIR';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TOPO_SUB_COMMAND_CIR(IID DECIMAL(19)  NOT NULL,IINSTRUCTNO VARCHAR(255),IIDENTIFIEDNO VARCHAR(255),IGROUPNAME VARCHAR(255),IINSYSNAME VARCHAR(255),IINFLOWNAME VARCHAR(255),ITASKCATEGORY VARCHAR(255),ISYSTEMNAME VARCHAR(255),ISYSTEMABBR VARCHAR(255),ITASKNAME VARCHAR(255),IRELEVANTSYSTEM VARCHAR(255),IREVIEWRECORD   VARCHAR(255),IWORKTASK VARCHAR(255),ICOMMENTS VARCHAR(2000),IPREPTASK VARCHAR(255),ISUCCESSORTASK  VARCHAR(255),IPRJTIMES VARCHAR(255),IBEGINTIME VARCHAR(255),IFINISHTIME VARCHAR(255),IREALBEGINTIME  DECIMAL(19),IREALFINISHTIME DECIMAL(19),IRESCOMPANY VARCHAR(255),IRESPERSON VARCHAR(255),ITASKEXECUTOR   VARCHAR(255),ISTATUS VARCHAR(255),IISFAIL INTEGER,INSTANCENAME VARCHAR(10),IEXECNUM INTEGER)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TOPO_SHOWSCREEN_DE_NUM';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TOPO_SHOWSCREEN_DE_NUM(ID DECIMAL(19,0) NOT NULL,ILEVEL  DECIMAL(19,0),CONSTRAINT PK_IEAI_TOPO_SHOWSCREEN_DE_NUM PRIMARY KEY (ID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
		
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_NJ_RESULT';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_NJ_RESULT(IID  DECIMAL(19) NOT NULL,IUSERID DECIMAL(19) NOT NULL, CONSTRAINT PK_IEAI_NJ_RESULT PRIMARY KEY (IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_HISWARN' AND COLNAME = 'CONFIRMWAY';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_HISWARN  ADD  CONFIRMWAY  DECIMAL(10) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_HISWARN' AND COLNAME = 'ICONFIRMRING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_HISWARN  ADD  ICONFIRMRING  INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_HISWARN' AND COLNAME = 'IMSG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_HISWARN  ADD  IMSG VARCHAR(1000) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_WARNING' AND COLNAME = 'CONFIRMWAY';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_WARNING  ADD  CONFIRMWAY  DECIMAL(10) DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_WARNING' AND COLNAME = 'ICONFIRMRING';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_WARNING  ADD  ICONFIRMRING  INTEGER DEFAULT 0 ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_WARNING' AND COLNAME = 'IMSG';
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_WARNING  ADD  IMSG VARCHAR(2000) ';
		   PREPARE	SQLA FROM LS_SQL; 
		   EXECUTE SQLA;
	END	IF;

END
$
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_TOPO_SHOWSCREEN_DE_NUM WHERE ID=1;
			IF LI_EXISTS = 0 THEN	
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (1, 1);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (2, 2);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (3, 3);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (4, 4);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (5, 5);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (6, 6);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (7, 7);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (8, 8);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (9, 9);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (10, 10);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (11, 11);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (12, 12);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (13, 13);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (14, 14);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (15, 15);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (16, 16);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (17, 17);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (18, 18);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (19, 19);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (20, 20);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (21, 21);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (22, 22);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (23, 23);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (24, 24);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (25, 25);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (26, 26);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (27, 27);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (28, 28);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (29, 29);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (30, 30);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (31, 31);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (32, 32);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (33, 33);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (34, 34);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (35, 35);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (36, 36);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (37, 37);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (38, 38);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (39, 39);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (40, 40);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (41, 41);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (42, 42);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (43, 43);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (44, 44);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (45, 45);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (46, 46);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (47, 47);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (48, 48);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (49, 49);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (50, 50);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (51, 51);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (52, 52);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (53, 53);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (54, 54);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (55, 55);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (56, 56);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (57, 57);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (58, 58);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (59, 59);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (60, 60);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (61, 61);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (62, 62);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (63, 63);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (64, 64);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (65, 65);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (66, 66);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (67, 67);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (68, 68);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (69, 69);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (70, 70);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (71, 71);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (72, 72);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (73, 73);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (74, 74);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (75, 75);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (76, 76);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (77, 77);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (78, 78);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (79, 79);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (80, 80);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (81, 81);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (82, 82);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (83, 83);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (84, 84);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (85, 85);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (86, 86);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (87, 87);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (88, 88);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (89, 89);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (90, 90);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (91, 91);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (92, 92);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (93, 93);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (94, 94);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (95, 95);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (96, 96);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (97, 97);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (98, 98);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (99, 99);
					insert into IEAI_TOPO_SHOWSCREEN_DE_NUM (id, ilevel)values (100, 100);
					COMMIT;
			END IF; 
END
$
CREATE OR REPLACE PROCEDURE PROC_TOPO_ABSCHLUSS( IN QUERYDATE VARCHAR(255), IN FLAG INT )
LANGUAGE SQL
BEGIN
    DECLARE  LV_QUERYDATE DECIMAL(19);
    BEGIN
    SET LV_QUERYDATE = FUN_GET_DATE_NUMBER_NEW(TO_DATE(QUERYDATE, 'YYYY-MM-DD'),8);
	IF FLAG = 0 THEN
    INSERT INTO IEAI_TOPO_SUB_COMMAND
      ( IID,
        IINSTRUCTNO,
        IIDENTIFIEDNO,
        IGROUPNAME,
        IINSYSNAME,
		 IINFLOWNAME,
        ITASKCATEGORY,
        ISYSTEMNAME,
        ISYSTEMABBR,
        ITASKNAME,
        IRELEVANTSYSTEM,
        IREVIEWRECORD,
        IWORKTASK,
        ICOMMENTS,
        IPREPTASK,
        ISUCCESSORTASK,
        IPRJTIMES,
        IBEGINTIME,
        IFINISHTIME,
        IRESCOMPANY,
        IRESPERSON,
        ITASKEXECUTOR,
        ISTATUS,
		IISFAIL,
		IREALBEGINTIME,
		IREALFINISHTIME,
        INSTANCENAME,
        IEXECNUM
        )
      (SELECT IID,
        IINSTRUCTNO,
        IIDENTIFIEDNO,
        IGROUPNAME,
        IINSYSNAME,
		 IINFLOWNAME,
        ITASKCATEGORY,
        ISYSTEMNAME,
        ISYSTEMABBR,
        ITASKNAME,
        IRELEVANTSYSTEM,
        IREVIEWRECORD,
        IWORKTASK,
        ICOMMENTS,
        IPREPTASK,
        ISUCCESSORTASK,
        IPRJTIMES,
        IBEGINTIME,
        IFINISHTIME,
        IRESCOMPANY,
        IRESPERSON,
        ITASKEXECUTOR,
        1,
		0,
		0,
		0,
        QUERYDATE,
        1
        FROM IEAI_TOPO_MAIN_COMMAND   
        WHERE IID NOT IN (SELECT IID
                                FROM IEAI_TOPO_SUB_COMMAND B
                               WHERE B.INSTANCENAME = QUERYDATE));

   END IF;
   COMMIT  WORK;
    --异常处理
    END;
END
$

CREATE OR REPLACE FUNCTION F_INSTANCENAME_CHECK_DATE(IN I_STR VARCHAR(255) )
 RETURNS INTEGER
 BEGIN
   DECLARE V_DATE     TIMESTAMP;
   DECLARE V_LEN      DECIMAL( 2 );
   DECLARE SQLCODE    INTEGER;
   DECLARE CONTINUE  HANDLER FOR SQLEXCEPTION
   BEGIN
      RETURN 1;
   END;
      IF I_STR IS NULL THEN
        RETURN 1;
      ELSE
          IF length(I_STR) <> 8 THEN
             RETURN 1;
          END IF;
       SET V_DATE = TO_DATE(I_STR,'YYYYMMDD');
       return 0;
    END IF;
 END
$
CREATE OR REPLACE PROCEDURE PROC_GANT_WORKFLOW_STATE(IN IN_IPROJECTNAME  VARCHAR(255),
                                                     IN IN_IFLOWNAME     VARCHAR(255),
                                                     IN IN_IFLOWINSNAME  VARCHAR(255),
                                                     IN IN_ISTATUS       INT,
                                                     IN IN_ISTARTTIME    DECIMAL(19),
                                                     IN IN_IENDTIME      DECIMAL(19),
                                                     IN IN_FLAG          INT,
                                                     IN IN_FLOWID       DECIMAL(19))
	BEGIN
    DECLARE V_IDATADATE   VARCHAR(255);
    DECLARE V_IFLAG     DECIMAL(10);
    DECLARE V_ICALLACTNAME  VARCHAR(255);
    DECLARE V_ICALLACTNAMECOUNT DECIMAL(10);
    DECLARE V_IEXECDATE    DECIMAL(19);
		-- db2　　
		--  DECLARE EXIT HANDLER FOR SQLWARNING,NOT FOUND,SQLEXCEPTION set V_IFLAG=-1;
	BEGIN
    SELECT F_INSTANCENAME_CHECK_DATE(IN_IFLOWINSNAME) INTO V_IFLAG   FROM IDUAL;
       IF V_IFLAG = 0 THEN
	SET V_IDATADATE = TO_CHAR(TO_DATE(IN_IFLOWINSNAME, 'YYYYMMDD'),	'YYYY-MM-DD');

        SELECT COUNT(A.IID)
        INTO V_ICALLACTNAMECOUNT
        FROM IEAI_CALLWORKFLOW_INFO C, IEAI_ACTRUNTIME A
       WHERE C.IMAINFLOWID = A.IFLOWID
         AND C.ICALLFLOWACTID = A.IACTID
         AND C.ICALLFLOWID = IN_FLOWID;
      IF (V_ICALLACTNAMECOUNT > 0) THEN
        SELECT A.IACTNAME
          INTO V_ICALLACTNAME
          FROM IEAI_CALLWORKFLOW_INFO C, IEAI_ACTRUNTIME A
         WHERE C.IMAINFLOWID = A.IFLOWID
           AND C.ICALLFLOWACTID = A.IACTID
           AND C.ICALLFLOWID = IN_FLOWID;
      END IF;
      IF V_ICALLACTNAME = '' OR V_ICALLACTNAME IS NULL THEN
        SET V_ICALLACTNAME = IN_IFLOWNAME;
      END IF;

		IF IN_ISTATUS = 2 OR IN_ISTATUS = 4 OR IN_ISTATUS = 25 THEN

        UPDATE IEAI_ACT_TOPO_INSTANCE
           SET IFAILSTATE = 1
         WHERE (SELECT COUNT(*)
                  FROM IEAI_ACT_TOPO_INSTANCE
                 WHERE IPROJECTNAME = IN_IPROJECTNAME
                   AND IFLOWNAME = IN_IFLOWNAME
                   AND IDATADATE = V_IDATADATE
                   AND (IISFAIL >= 0 OR IISWARN >= 0)
                   AND IACTSTATE = '0') > 0
           AND IPROJECTNAME = IN_IPROJECTNAME
           AND IFLOWNAME = IN_IFLOWNAME
           AND IDATADATE = V_IDATADATE
           AND IACTSTATE = '0';

		UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE = '2', B.IISFAIL = 0, B.IISWARN = 0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IDATADATE = V_IDATADATE
           AND B.IACTSTATE = '0';
      
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE = '2'
         WHERE B.IRUNSTATE = '1'
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IDATADATE = V_IDATADATE;
           
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IRUNSTATE = '1'
         WHERE (B.IACTSTATE IS NULL OR B.IACTSTATE=1)
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IDATADATE = V_IDATADATE;
      
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE   = '2',
               B.IISFAIL     = -1,
               B.IISWARN     = -1,
               B.IEXPENDTIME = 0,
               B.IFLOWSTATE  = IN_ISTATUS
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IDATADATE = V_IDATADATE
           AND B.IACTSTATE = '0';
           
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IFLOWSTATE = 1
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IDATADATE = V_IDATADATE;
      
        UPDATE IEAI_TOPO_SUB_COMMAND B

           SET B.ISTATUS         = 2,
               B.IISFAIL         = 0,
         B.IREALBEGINTIME = IN_ISTARTTIME,
               B.IREALFINISHTIME = IN_IENDTIME
         WHERE B.IINSYSNAME = IN_IPROJECTNAME
           AND B.IWORKTASK = IN_IFLOWNAME
           AND B.INSTANCENAME = V_IDATADATE;
      
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IREALENDTIME = IN_IENDTIME
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IREALENDTIME <> 0
           AND B.IREALENDTIME IS NOT NULL
           AND B.IDATADATE = V_IDATADATE;
           
        UPDATE IEAI_TOPO_WARNING A
           SET A.ICONFIRMRING = 1
         WHERE A.IPRJNAME = IN_IPROJECTNAME
           AND A.IFLOWNAME = IN_IFLOWNAME
           AND A.IDATADATE = V_IDATADATE;
      END IF;
    
      IF IN_ISTATUS = 0 AND IN_FLAG = 0 THEN
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE     = '1',
               B.IFLOWSTATE    = '0',
               B.IEXPSTARTTIME = 0,
               B.IEXPENDTIME  = 0,
               B.IISFAIL       = 0,
               B.IREALBEGINTIME = 0,
               B.IREALENDTIME=0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IDATADATE = V_IDATADATE;
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IREALBEGINTIME = 0,
               B.IREALENDTIME=0,
               B.IACTSTATE     = '1',
               B.IFLOWSTATE    = '0',
               B.IEXPSTARTTIME = 0,
               B.IEXPENDTIME  = 0,
               B.IISFAIL       = 0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IREALBEGINTIME <> 0
           AND B.IREALBEGINTIME IS NOT NULL
           AND B.IDATADATE = V_IDATADATE;
      END IF;
    
      IF IN_ISTATUS = 0 AND IN_FLAG = 1 THEN
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE = '0', B.IFLOWSTATE = 0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IDATADATE = V_IDATADATE;
      END IF;
    IF IN_ISTATUS = 0 THEN
      UPDATE IEAI_TOPO_SUB_COMMAND B
       SET B.ISTATUS        = IN_ISTATUS,
         B.IISFAIL        = 0,
         B.IREALBEGINTIME = IN_ISTARTTIME
       WHERE B.IINSYSNAME = IN_IPROJECTNAME
       AND B.IWORKTASK = IN_IFLOWNAME
       AND B.INSTANCENAME = V_IDATADATE;
    END IF;
    END IF;
	END;
	END
$
CREATE OR REPLACE PROCEDURE PROC_GANT_INSTANCE_DATA(IN  QUERYDATE VARCHAR(255),
                                                    IN  FLAG  DECIMAL(10))
			LANGUAGE SQL
			BEGIN
			  
			   DECLARE LV_QUERYDATE DECIMAL(19);
			   DECLARE LV_IID       DECIMAL(19);
			  BEGIN
			  
				SET LV_QUERYDATE = (TO_DATE(QUERYDATE, 'YYYY-MM-DD') - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 24 * 60 * 60 * 1000;
			  
				IF FLAG = 0 THEN
				CALL PROC_GET_NEXT_PK ('TMP_IEAI_TOPO_INSTANCE_QUERY', LV_IID); 
			  INSERT INTO TMP_IEAI_TOPO_INSTANCE_QUERY
				(OPT_ID, IID)
				SELECT LV_IID, A.IID
				  FROM IEAI_ACT_RELATION_MESS A
				 WHERE A.IID NOT IN (SELECT IID
									   FROM IEAI_ACT_TOPO_INSTANCE B
									  WHERE B.IDATADATE = QUERYDATE);
			  INSERT INTO IEAI_ACT_TOPO_INSTANCE
				(IID,
				 IDATADATE,
				 IPROJECTNAME,
				 IFLOWNAME,
				 IFIRSTFLOWNAME,
				 IPROJECTID,
				 IACTNAME,
				 IACTDES,
				 IAGENTIP,
				 IAGENTPORT,
				 IBATCHNAME,
				 IBATCHPATH,
				 ITIMEOUT,
				 IREALBEGINTIME,
				 IREALENDTIME,
				 IACTSTATE,
				 IFLOWSTATE,
				 IAVGTIME,
				 IISFAIL,
				 IISWARN,
				 IUPSYSNAME,
				 IUPACTNAME,
				 IUPACTSTATE,
				 IDOWNSYSNAME,
				 IDOWNACTNAME,
				 IDOWNACTSTATE,
				 IBUSINESSBEGINTIME,
				 IBUSINESSBEGINTIMETYPE,
				 IARRIVETIME,
				 IARRIVETIMETYPE,
				 IMAINFLOWNAME,
				 IEXPSTARTTIME,
				 IEXPENDTIME,
				 IRUNTIME,
				 IACROSSDAY_NUM,
				 IAVGRULE,
				 IISSYNCCALL,
				 IPARALLELFLAG)
				(SELECT IID,
						QUERYDATE,
						IPROJECTNAME,
						IFLOWNAME,
						IFIRSTFLOWNAME,
						IPROJECTID,
						IACTNAME,
						IACTDES,
						IAGENTIP,
						IAGENTPORT,
						IBATCHNAME,
						IBATCHPATH,
						ITIMEOUT,
						IREALBEGINTIME,
						IREALENDTIME,
						IACTSTATE,
						IFLOWSTATE,
						IAVGTIME,
						IISFAIL,
						IISWARN,
						IUPSYSNAME,
						IUPACTNAME,
						IUPACTSTATE,
						IDOWNSYSNAME,
						IDOWNACTNAME,
						IDOWNACTSTATE,
						IBUSINESSBEGINTIME,
						IBUSINESSBEGINTIMETYPE,
						IARRIVETIME,
						IARRIVETIMETYPE,
						IMAINFLOWNAME,
						IEXPSTARTTIME,
						IEXPENDTIME,
						IRUNTIME,
						IACROSSDAY_NUM,
						NULL,
						IISSYNCCALL,
						IPARALLELFLAG
				   FROM IEAI_ACT_RELATION_MESS A
				  WHERE A.IID IN (SELECT IID
									FROM TMP_IEAI_TOPO_INSTANCE_QUERY B
								   WHERE B.OPT_ID = LV_IID));
			  END IF;
			--到达
			  UPDATE IEAI_TOPO_WARNING A
			   SET A.IWARNINGSTATE = 1, A.ICONFIRMTYPE = 1
			 WHERE A.IWARNINGSTATE != 1
			   AND A.IDATADATE = QUERYDATE
			   AND A.IACTMESID IN
				   (SELECT B.IID
					  FROM IEAI_ACT_TOPO_INSTANCE B
					 WHERE B.IDATADATE = QUERYDATE
					   AND B.IACTSTATE IN ('2', '3', '-2'));
			-- 将警报插入历史表中
			INSERT INTO IEAI_TOPO_HISWARN
			  (IID,
			   IDATADATE,
			   IWARNTIME,
			   IWARNTIMETYPE,
			   IACTNAME,
			   IWARNINGTYPE,
			   IWARNINGSTATE,
			   ICONFIRMTYPE,
			   ISOURCEACTNAME,
			   IACTMESID,
			   ISOURCEACTMESID,
			   IPRJNAME,
			   ISOURCEPRJNAME,
			   CONFIRMWAY)
			  SELECT AA.IID,
					 AA.IDATADATE,
					 AA.IWARNTIME,
					 AA.IWARNTIMETYPE,
					 AA.IACTNAME,
					 AA.IWARNINGTYPE,
					 AA.IWARNINGSTATE,
					 AA.ICONFIRMTYPE,
					 T.IACTNAME         AS SOURCEACTNAME,
					 AA.IACTMESID,
					 AA.ISOURCEACTMESID,
					 AA.IPROJECTNAME,
					 T.IPROJECTNAME     AS SOURCEPROJECTNAME,
					 1
				FROM (SELECT W.IID,
							 W.IDATADATE,
							 W.IWARNTIME,
							 W.IWARNTIMETYPE,
							 A.IACTNAME,
							 W.IWARNINGTYPE,
							 W.IWARNINGSTATE,
							 W.ICONFIRMTYPE,
							 W.ISOURCEACTMESID,
							 W.IACTMESID,
							 A.IPROJECTNAME
						FROM IEAI_TOPO_WARNING W, IEAI_ACT_TOPO_INSTANCE A
					   WHERE A.IDATADATE = W.IDATADATE
						 AND W.IACTMESID = A.IID
						 AND W.IWARNINGSTATE = 1
						 AND A.IID IN
							 (SELECT B.IID
								FROM IEAI_ACT_TOPO_INSTANCE B
							   WHERE B.IDATADATE = QUERYDATE
								 and B.IACTSTATE in ('2', '-2', '3') -- 完成
							 AND not exists (select iid
									from IEAI_TOPO_HISWARN
								   where iid = w.IID))) AA
			LEFT JOIN IEAI_ACT_TOPO_INSTANCE T
			  ON AA.ISOURCEACTMESID = T.IID;
			--更新超时时间

			 UPDATE IEAI_ACT_TOPO_INSTANCE B
				SET B.ITIMEOUTTIME = (decode(B.irealendtime,
													   0,
													   FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8),
													   B.irealendtime) -
									 B.IFIRSTTIMEOUTTIME)
				WHERE B.ITIMEOUTNUM > 0
			   AND B.IFIRSTTIMEOUTTIME > 0
			   AND B.IFIRSTTIMEOUTTIME IS NOT NULL
			   AND B.IACTSTATE = '0'
			   AND B.IDATADATE = QUERYDATE;

			  END;
			END
$
CREATE OR REPLACE TRIGGER ITR_WORKFLOWINSTANCE_INSERT 
    AFTER INSERT ON IEAI_WORKFLOWINSTANCE
    REFERENCING NEW AS N OLD AS O 
    FOR EACH ROW 
    MODE DB2SQL 
    BEGIN
        DECLARE FLAGS NUMERIC(10); 
        SELECT IVALUE  INTO FLAGS  FROM IEAI_ACT_TPINS_TEST WHERE	IID = 1; 
        IF FLAGS > 0 THEN 
			 CALL PROC_GANT_WORKFLOW_STATE (N.IPROJECTNAME,N.IFLOWNAME,N.IFLOWINSNAME,N.ISTATUS,N.ISTARTTIME,N.IENDTIME,0,N.IFLOWID);
        END IF; 
    END
$ 
CREATE OR REPLACE TRIGGER ITR_WORKFLOWINSTANCE_UPDATE
	AFTER UPDATE ON IEAI_WORKFLOWINSTANCE
	REFERENCING NEW AS N OLD AS O
	FOR EACH ROW
	MODE DB2SQL
	BEGIN
	DECLARE FLAGS NUMERIC(10);
	SELECT IVALUE INTO FLAGS FROM IEAI_ACT_TPINS_TEST WHERE IID = 1;
	IF FLAGS > 0 THEN
        IF N.ISTATUS <> O.ISTATUS THEN
         IF N.ISTATUS = 2 OR N.ISTATUS = 4 OR N.ISTATUS = 25 THEN
 				CALL PROC_GANT_WORKFLOW_STATE (N.IPROJECTNAME,N.IFLOWNAME,N.IFLOWINSNAME,N.ISTATUS,N.ISTARTTIME,N.IENDTIME,1,N.IFLOWID);
        END IF;
     END IF;
    END IF;
	END
$

-- 4.7.25 version does not have patches for timed tasks
	
-- 4.7.26 version TOPO patch is as follows



BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IEAI_TOPO_AVGRECODER_QUERY';
    IF	LI_EXISTS = 1 THEN
     SET	LS_SQL = 'DROP TABLE TMP_IEAI_TOPO_AVGRECODER_QUERY';
             PREPARE	SQLA FROM LS_SQL; 
             EXECUTE  SQLA;
  END	IF;
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IEAI_TOPO_AVGRECODER_QUERY';
    IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_IEAI_TOPO_AVGRECODER_QUERY (OPT_ID   decimal (19) not null, PRJNAME  VARCHAR (255), FLOWNAME  VARCHAR (255), ACTNAME   VARCHAR (255), ACTTYPE   VARCHAR (255), ACTDESC   VARCHAR (255), AMONTH    VARCHAR (10), ADAY      VARCHAR (10), AWEEK     VARCHAR (10), AOFFSET  decimal (5, 2),ADATAFLAG INTEGER) on commit delete rows';
    PREPARE	SQLA FROM LS_SQL; 
    EXECUTE  SQLA;
  END	IF;
  END
$



CREATE OR REPLACE  PROCEDURE PROC_COUNT_AVG_ACTIVITY ( IN STARTTIME NUMERIC (19))
LANGUAGE SQL
BEGIN
DECLARE PRJ_NAME VARCHAR(255);
-- 工程名
DECLARE FLOW_NAME VARCHAR(255);
-- 工作流名
DECLARE ACT_NAME VARCHAR(255);
-- 工作流名
DECLARE ACT_DES VARCHAR(255);
-- 活动名
DECLARE AWEEKS VARCHAR(255);
-- ZHOU
DECLARE BWEEKS VARCHAR(255);
-- ZHOU
DECLARE AMONTHS VARCHAR(255);
-- YUE
DECLARE ADAYS VARCHAR(255);
-- RI
DECLARE PFANAME VARCHAR(255);
-- 名称连接字符串
DECLARE SUMTIME DECIMAL(19);
-- 平均耗时
DECLARE START_TIME VARCHAR(19);
-- 预计开始时间
DECLARE RECORDID DECIMAL(19);
DECLARE LV_SQL VARCHAR(4000);
DECLARE LVL_SQL VARCHAR(4000);
DECLARE LV_WHERE VARCHAR(4000);
DECLARE LV_WHERES VARCHAR(4000);
DECLARE GZFLAG DECIMAL(1);
DECLARE LV_NOTWHERE VARCHAR(4000);
DECLARE TMPSYSNAME VARCHAR(4000);
DECLARE LV_COUNT DECIMAL(19);
DECLARE PART_NUM DECIMAL(5);
DECLARE OPT_ID DECIMAL(19);
DECLARE DATA_FLAG   INTEGER;
DECLARE DEL_TIME    VARCHAR(19);
-- 清理数据表-- 
select to_char(current timestamp, 'yyyymmdd')
  into START_TIME
  from idual;
select FUN_GET_DATE_STRING(STARTTIME, 8, 'yyyymmdd')
  into DEL_TIME
  from idual;
DELETE FROM IEAI_ACTRUNTIME_AVGRECORD where IEXPECTTIME < DEL_TIME;
-- 插入临时表-- 1、计算所有记录无规则数据的耗时，2、计算所有有规则记录耗时。
CALL PROC_GET_NEXT_PK('TMP_IEAI_TOPO_AVGRECODER_QUERY', RECORDID);
INSERT INTO TMP_IEAI_TOPO_AVGRECODER_QUERY
(OPT_ID, PRJNAME, FLOWNAME, ACTNAME, ACTDESC, AMONTH, ADAY, AWEEK,ADATAFLAG)
  SELECT DISTINCT
	RECORDID,
	T.IPROJECTNAME,
	T.IFLOWNAME,
	T.ITASK_NAME,
	T.ITASK_DESC,
	T.ITIMEOUT_MONTH,
	T.ITIMEOUT_DAY,
	T.ITIMEOUT_WEEK,
	T.DATAFLAG
  FROM (SELECT MM.IPROJECTNAME,
				 MM.IFLOWNAME,
				 TT.ITASK_NAME,
				 TT.ITASK_DESC,
				 TT.ITIMEOUT_MONTH,
				 TT.ITIMEOUT_DAY,
				 TT.ITIMEOUT_WEEK,
				 1 as DATAFLAG
			FROM IEAI_ACT_RELATION_MESS MM, IEAI_ACT_TIMEOUTCONFIG TT
		   WHERE MM.IPROJECTNAME = TT.ISYSTEM_NAME
			 AND MM.IACTNAME = TT.ITASK_NAME
		  UNION ALL
		  SELECT M.IPROJECTNAME,
				 M.IFLOWNAME,
				 M.IACTNAME,
				 M.IACTDES,
				 '' AS ITIMEOUT_MONTH,
				 '' AS ITIMEOUT_DAY,
				 '' AS ITIMEOUT_WEEK,
				 0 as DATAFLAG
			FROM IEAI_ACT_RELATION_MESS M
		  UNION ALL
		  SELECT M.IPROJECTNAME,
				 M.IFLOWNAME,
				 M.IACTNAME,
				 M.IACTDES,
				 '' AS ITIMEOUT_MONTH,
				 '' AS ITIMEOUT_DAY,
				 '' AS ITIMEOUT_WEEK,
				 2 as DATAFLAG
			FROM IEAI_ACT_RELATION_MESS M
	   ) T
  ORDER BY ITIMEOUT_WEEK DESC;
SET TMPSYSNAME = '';
-- 轮询名称-- 
FOR ALLUPFLOW AS CURSOR1 CURSOR FOR SELECT  PRJNAME,  FLOWNAME,  ACTNAME,  ACTDESC,  AMONTH,  ADAY,  AWEEK , ADATAFLAG FROM TMP_IEAI_TOPO_AVGRECODER_QUERY WHERE OPT_ID = RECORDID
DO
SET LV_WHERE = '';
SET LV_WHERES = '';
SET GZFLAG = 0;
SET SUMTIME = -1;
SET LV_COUNT = 0;
SET PRJ_NAME = PRJNAME;
SET FLOW_NAME = FLOWNAME;
SET ACT_NAME = ACTNAME;
SET ACT_DES = ACTDESC;
SET AMONTHS = AMONTH;
SET ADAYS = ADAY;
SET AWEEKS = AWEEK;
SET BWEEKS = AWEEKS;
SET DATA_FLAG = ADATAFLAG;
--  6.1 工程名
IF LENGTH(PRJ_NAME) > 0  THEN
	SET LV_WHERE = LV_WHERE || '  AND AA.IPROJECTNAME = ' || CHR(39) ||  PRJ_NAME || CHR(39);
END IF;
IF LENGTH(FLOW_NAME) > 0  THEN
	SET LV_WHERE = LV_WHERE || '  AND AA.IFLOWNAME = ' || CHR(39) ||  FLOW_NAME || CHR(39);
END IF;
IF LENGTH(ACT_NAME) > 0  THEN
	SET LV_WHERE = LV_WHERE || '  AND  AA.IACTNAME = ' || CHR(39) ||  ACT_NAME || CHR(39);
END IF;

IF TMPSYSNAME = PRJ_NAME || FLOW_NAME || ACT_NAME  THEN
	SET TMPSYSNAME = PRJ_NAME || FLOW_NAME || ACT_NAME;
ELSE
	IF TMPSYSNAME = 'NULL' OR TMPSYSNAME = '' OR TMPSYSNAME IS NULL  THEN
	  SET TMPSYSNAME = PRJ_NAME || FLOW_NAME || ACT_NAME;
	ELSE
	 SET TMPSYSNAME = PRJ_NAME || FLOW_NAME || ACT_NAME;
	 SET LV_NOTWHERE = NULL;
	END IF;
END IF;
-- 规则计算
IF LENGTH(AMONTHS) > 0  THEN
	 IF LENGTH(AMONTHS) = 1   THEN
		SET AMONTHS = '0' || AMONTHS;
	END IF;
		SET LV_WHERES = LV_WHERES ||  '  AND  FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME),0) = ' ||  CHR(39) || AMONTHS || CHR(39);
		SET LV_NOTWHERE = LV_NOTWHERE || '  AND  FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME),0) <> '  ||  CHR(39) || AMONTHS || CHR(39);
END IF;

IF DATA_FLAG = 2 THEN
  --计算历史数据中，符合当前日期耗时
  select to_char(current timestamp, 'dd') into ADAYS from idual;
END IF;
IF LENGTH(ADAYS) > 0   THEN
	IF LENGTH(ADAYS) = 1  THEN
		SET ADAYS = '0' || ADAYS;
	END IF;

	IF LENGTH(ADAYS) > 0   THEN
		SET LV_WHERES = LV_WHERES || '  AND (FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME),1) = '|| CHR(39) || ADAYS || CHR(39) || ')';
		SET LV_NOTWHERE = LV_NOTWHERE ||'  AND (FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME),1) <> ' || CHR(39) || ADAYS || CHR(39) || ')';
	ELSE
		SET LV_WHERES = LV_WHERES ||'  AND (FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,NULL,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME),2) = '|| CHR(39) || ADAYS || CHR(39) || ')';
		SET LV_NOTWHERE = LV_NOTWHERE ||'  AND (FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME),2) <> '|| CHR(39) || ADAYS || CHR(39) || ')';
	END IF;
END IF;
IF LENGTH(AWEEKS) > 0 THEN
	IF AWEEKS = 'Mon' THEN
		SET AWEEKS = '1';
	ELSEIF AWEEKS = 'Tues' THEN
		SET AWEEKS = '2';
	ELSEIF AWEEKS = 'Wed' THEN
		SET AWEEKS = '3';
	ELSEIF AWEEKS = 'Thur' THEN
		SET AWEEKS = '4';
	ELSEIF AWEEKS = 'Fri' THEN
		SET AWEEKS = '5';
	ELSEIF AWEEKS = 'MonToFri' THEN
		SET AWEEKS = CHR(39) || '1' || CHR(39) || ',' || CHR(39) || '2' || CHR(39) || ',' || CHR(39) || '3' || CHR(39) || ',' || CHR(39) || '4' || CHR(39) || ',' || CHR(39) || '5' || CHR(39);
	ELSEIF AWEEKS = 'SatSun' 	THEN
		SET AWEEKS = CHR(39) || '6' || CHR(39) || ',' || CHR(39) || '7' || CHR(39);
	END IF;

	IF AWEEKS = 'Mon' THEN
		SET AWEEKS = '1';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'Tues' THEN
		SET AWEEKS = '2';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'Wed' THEN
		SET AWEEKS = '3';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'Thur' 	THEN
		SET AWEEKS = '4';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'Fri' THEN
		SET AWEEKS = '5';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'Sat' THEN
		SET AWEEKS = '6';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'Sun' THEN
		SET AWEEKS = '7';
		SET BWEEKS = AWEEKS;
	ELSEIF AWEEKS = 'MonToFri' THEN
		SET AWEEKS = CHR(39) || '1' || CHR(39) || ',' || CHR(39) || '2' || 	CHR(39) || ',' || CHR(39) || '3' || CHR(39) || ',' || CHR(39) || '4' || CHR(39) || ',' || CHR(39) || '5' || CHR(39);
		SET BWEEKS = '12345';
	ELSEIF AWEEKS = 'SatSun' THEN
		SET AWEEKS = CHR(39) || '6' || CHR(39) || ',' || CHR(39) || '7' || CHR(39);
		SET BWEEKS = '67';
	END IF;

	SET LV_WHERES = LV_WHERES || ' AND 	FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME), 3) in (' || AWEEKS || ') ';
	SET LV_NOTWHERE = LV_NOTWHERE || ' AND 	FUN_DATE_TRANCE(DECODE(AA.IMAINFLOWTIME,null,AA.IBEGINEXCTIME,AA.IMAINFLOWTIME), 3) NOT in (' || AWEEKS || ') ';
END IF;
  --  CALL PROC_GET_NEXT_PK('TMP_AVG_PRE', OPT_ID);
-- 若该记录没有规则，则查询该记录非规则的对应数据的耗时，否则查询规则内对应的有效数据的耗时
IF LV_WHERES = 'NULL' OR LV_WHERES = '' OR LV_WHERES IS NULL THEN
-- 查询该条件下是否有记录，否则into有问题
	SET LV_SQL='INSERT INTO TMP_AVG_PRE(ICOUNT) SELECT COUNT(AA.IID) FROM   (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IDELFLAG,P.IENDTIME,W.IPROJECTNAME,W.IFLOWNAME,P.IMAINFLOWTIME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME_PART P WHERE W.IFLOWID=P.IFLOWID AND W.ISTATUS = 2 and (P.IDELFLAG<>1 OR P.IDELFLAG IS NULL)) AA WHERE  AA.IENDTIME<>0 AND (AA.IDELFLAG <> 1 OR AA.IDELFLAG IS NULL)   AND AA.IACTTYPE=' ||  CONCAT('''Common''',CONCAT('  AND  AA.IBEGINEXCTIME >= ',CONCAT(STARTTIME , CONCAT((case when LV_WHERE is null then  '' else LV_WHERE end) , (case when LV_NOTWHERE is null  then '' else LV_NOTWHERE end)))));
	PREPARE SQLA FROM LV_SQL;
	EXECUTE SQLA ;
	SELECT ICOUNT INTO  LV_COUNT FROM TMP_AVG_PRE;
	DELETE FROM TMP_AVG_PRE;
	IF LV_COUNT > 0 THEN
	SET LV_SQL = 'INSERT INTO TMP_AVG_PRE(ITIME)  SELECT ROUND(AVG(AA.IENDTIME - AA.IBEGINEXCTIME), 0)  FROM   (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IDELFLAG,P.IENDTIME,W.IPROJECTNAME,W.IFLOWNAME,P.IMAINFLOWTIME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME_PART P WHERE W.IFLOWID=P.IFLOWID AND W.ISTATUS = 2 and (P.IDELFLAG<>1 OR P.IDELFLAG IS NULL)) AA WHERE  AA.IENDTIME<>0 AND (AA.IDELFLAG <> 1 OR AA.IDELFLAG IS NULL)  AND AA.IACTTYPE='||CONCAT('''Common''',CONCAT(' AND AA.IBEGINEXCTIME >= ',CONCAT(STARTTIME , CONCAT((CASE 	WHEN LV_WHERE IS NULL 	THEN '' 	ELSE LV_WHERE END) , (CASE 	WHEN LV_NOTWHERE IS NULL 	THEN '' 	ELSE LV_NOTWHERE END)))));
		PREPARE SQLA FROM LV_SQL;
		EXECUTE SQLA;
		SELECT ITIME  INTO  SUMTIME FROM TMP_AVG_PRE;
		DELETE FROM TMP_AVG_PRE;
	END IF;
ELSE
-- 查询该条件下是否有记录，否则into有问题
	SET LV_SQL ='INSERT INTO TMP_AVG_PRE(ICOUNT)  SELECT COUNT(AA.IID) FROM   (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IDELFLAG,P.IENDTIME,W.IPROJECTNAME,W.IFLOWNAME,P.IMAINFLOWTIME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME_PART P WHERE W.IFLOWID=P.IFLOWID AND W.ISTATUS = 2 and (P.IDELFLAG<>1 OR P.IDELFLAG IS NULL)) AA WHERE  AA.IENDTIME<>0  AND (AA.IDELFLAG <> 1 OR AA.IDELFLAG IS NULL)  AND AA.IACTTYPE='||CONCAT('''Common''',CONCAT(' AND  AA.IBEGINEXCTIME >= ',CONCAT(STARTTIME , CONCAT((case when LV_WHERE is null then  '' else LV_WHERE end) , (case when LV_WHERES is null  then '' else LV_WHERES end))))); 
	PREPARE SQLA FROM LV_SQL;
	EXECUTE SQLA ;
	SELECT ICOUNT INTO  LV_COUNT FROM TMP_AVG_PRE;
	DELETE FROM TMP_AVG_PRE;
	IF LV_COUNT > 0 THEN
		SET LV_SQL = 'INSERT INTO TMP_AVG_PRE(ITIME)  SELECT ROUND(AVG(AA.IENDTIME - AA.IBEGINEXCTIME), 0) FROM   (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IDELFLAG,P.IENDTIME,W.IPROJECTNAME,W.IFLOWNAME,P.IMAINFLOWTIME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME_PART P WHERE W.IFLOWID=P.IFLOWID AND W.ISTATUS = 2 and (P.IDELFLAG<>1 OR P.IDELFLAG IS NULL)) AA WHERE AA.IENDTIME<>0  AND (AA.IDELFLAG <> 1 OR AA.IDELFLAG IS NULL)    AND AA.IACTTYPE='||CONCAT('''Common''', CONCAT(' AND   AA.IBEGINEXCTIME >= ',CONCAT(STARTTIME , CONCAT((case when LV_WHERE is null then  '' else LV_WHERE end) , (case when LV_WHERES is null  then '' else LV_WHERES end))))); 
		PREPARE SQLA FROM LV_SQL;
		EXECUTE SQLA;
		SELECT ITIME  INTO  SUMTIME FROM TMP_AVG_PRE;
		DELETE FROM TMP_AVG_PRE;
	END IF;
END IF;
IF SUMTIME = -1 OR SUMTIME = '' OR SUMTIME IS NULL THEN
-- 查询该条件下是否有记录，否则into有问题
	SET LV_SQL = 'INSERT INTO TMP_AVG_PRE(ICOUNT)  SELECT COUNT(BB.IID) FROM (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IENDTIME,W.IPROJECTNAME,W.IFLOWNAME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME P WHERE W.IFLOWID=P.IFLOWID   AND P.IENDTIME <> 0 AND P.IACTTYPE = ''Common'' AND W.ISTATUS = 2) BB  WHERE  BB.IID=(SELECT MAX(AA.IID) FROM (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IENDTIME,P.IERRORTASKID,W.IPROJECTNAME,W.IFLOWNAME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME P WHERE W.IFLOWID=P.IFLOWID   AND P.IENDTIME <> 0 AND P.IACTTYPE = ''Common'' AND W.ISTATUS = 2) AA WHERE  1=1 '|| LV_WHERE || ' AND (AA.IERRORTASKID IS NULL OR AA.IERRORTASKID = 0 OR (AA.IERRORTASKID IS NOT NULL AND AA.IERRORTASKID > 0   AND   AA.IID IN (SELECT A.IID  FROM IEAI_ERRORTASK          T, IEAI_ACTRUNTIME         A,   IEAI_ERRORTASKOPERATION E  WHERE A.IERRORTASKID = T.IID  AND T.IOPERATIONID = E.IID AND E.IHANDLMETHOD = 0   AND A.IACTNAME = '''||  ACT_NAME || '''))))';  
	PREPARE SQLA FROM LV_SQL;
	EXECUTE SQLA ;
	SELECT ICOUNT INTO  LV_COUNT FROM TMP_AVG_PRE;
	DELETE FROM TMP_AVG_PRE;
	IF LV_COUNT > 0 THEN
		SET LV_SQL ='INSERT INTO TMP_AVG_PRE(ITIME) SELECT ROUND((BB.IENDTIME - BB.IBEGINEXCTIME), 0) FROM (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IENDTIME,W.IPROJECTNAME,W.IFLOWNAME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME P WHERE W.IFLOWID=P.IFLOWID   AND P.IENDTIME <> 0 AND P.IACTTYPE = ''Common'' AND W.ISTATUS = 2) BB  WHERE   BB.IID=(SELECT MAX(AA.IID) FROM (SELECT  P.IID, P.IACTNAME, P.IBEGINEXCTIME,P.IACTTYPE,P.IENDTIME,W.IPROJECTNAME,P.IERRORTASKID,W.IFLOWNAME  FROM  IEAI_WORKFLOWINSTANCE W , IEAI_ACTRUNTIME P WHERE W.IFLOWID=P.IFLOWID   AND P.IENDTIME <> 0 AND P.IACTTYPE = ''Common''  AND W.ISTATUS = 2) AA WHERE  1=1 '||LV_WHERE ||'  AND (AA.IERRORTASKID IS NULL OR AA.IERRORTASKID = 0 OR (AA.IERRORTASKID IS NOT NULL AND AA.IERRORTASKID > 0   AND   AA.IID IN (SELECT A.IID  FROM IEAI_ERRORTASK          T, IEAI_ACTRUNTIME         A,   IEAI_ERRORTASKOPERATION E  WHERE A.IERRORTASKID = T.IID  AND T.IOPERATIONID = E.IID AND E.IHANDLMETHOD = 0   AND A.IACTNAME = '''|| ACT_NAME || '''))))'; 
		PREPARE SQLA FROM LV_SQL;
		EXECUTE SQLA;
		SELECT ITIME  INTO  SUMTIME FROM TMP_AVG_PRE;
		DELETE FROM TMP_AVG_PRE;   
	END IF;
END IF;
IF SUMTIME = -1  OR SUMTIME IS NULL  THEN
	SET SUMTIME = 0;
END IF;
IF LV_WHERES IS NULL  THEN
	SET GZFLAG = 0;
ELSE
	SET GZFLAG = 1;
END IF;
IF DATA_FLAG = 2 THEN
	SET GZFLAG = 2;
END IF;
CALL PROC_GET_NEXT_PK('IEAI_ACTRUNTIME_AVGRECORD', RECORDID);
INSERT INTO IEAI_ACTRUNTIME_AVGRECORD
(IID,
 IPRJNAME,
 IFLOWNAME,
 IACTNAME,
 IACTDESC,
 ITIMEMONTH,
 ITIMEDAY,
 ITIMEWEEK,
 IAVGTIME,
 IEXPECTTIME,
 IISRULE)
VALUES
  (RECORDID,
	PRJ_NAME,
	FLOW_NAME,
	ACT_NAME,
	ACT_DES,
	AMONTHS,
	ADAYS,
	BWEEKS,
	SUMTIME,
	START_TIME,
	GZFLAG);
END FOR;
END
$

CREATE OR REPLACE PROCEDURE PROC_TOPO_SYSTEM( IN QUERYDATE VARCHAR (255),IN GROUPNAME VARCHAR (255)) 
		LANGUAGE SQL
		BEGIN
		DECLARE PRJ_NAME VARCHAR(255);
		DECLARE NSTATE DECIMAL(19);
		DECLARE RSTATE DECIMAL(19);
		DECLARE FSTATE DECIMAL(19);
		DECLARE NCOUNT DECIMAL(19);
		DECLARE ACTCOUNT DECIMAL(19);
		DECLARE FINISHSTATE DECIMAL(19);
		DECLARE FLOWINSTANCE DECIMAL(19);
		DECLARE MESS DECIMAL(19);
		DECLARE EXCEPAFTERFINISH DECIMAL(19);
		-- 清理数据表-- 
		DELETE FROM IEAI_TOPO_RECORD;
		DELETE FROM TMP_TOPO_RECORD;
		IF LENGTH(GROUPNAME) > 0 THEN
		    INSERT INTO TMP_TOPO_RECORD (SYSTEMNAME) SELECT DISTINCT SYSTEMNAME FROM (SELECT SYSTEMNAME   FROM IEAI_TOPOINFO   WHERE GRO = GROUPNAME  UNION   SELECT LASTSYSNAME AS SYSTEMNAME  FROM IEAI_TOPOINFO  WHERE GRO = GROUPNAME) WHERE SYSTEMNAME IS NOT NULL ORDER BY SYSTEMNAME;
		ELSE
		    INSERT INTO TMP_TOPO_RECORD (SYSTEMNAME)   SELECT DISTINCT SYSTEMNAME   FROM (SELECT SYSTEMNAME 	FROM IEAI_TOPOINFO UNION SELECT LASTSYSNAME AS SYSTEMNAME 	FROM IEAI_TOPOINFO)  WHERE SYSTEMNAME IS NOT NULL  ORDER BY SYSTEMNAME;
		END IF;
		BEGIN
		FOR V AS MYCURSOR CURSOR FOR SELECT SYSTEMNAME  FROM TMP_TOPO_RECORD
		DO
		SET PRJ_NAME = V.SYSTEMNAME;
		SELECT COUNT(AT.IID)  INTO FSTATE  FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IISFAIL IS NOT NULL  AND AT.IISFAIL <> 0  AND AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		SELECT MIN(AT.IISWARN) INTO NSTATE FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IISWARN IS NOT NULL   AND AT.IISWARN <> 0   AND AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		IF NSTATE IS NULL THEN
		    SET NCOUNT = 0;
		ELSEIF NSTATE = 1 THEN
		    SET NCOUNT = 1;
		ELSEIF NSTATE = 2 THEN
		    SET NCOUNT = 2;
		END IF;
		IF FSTATE > 0 THEN
		    SET NCOUNT = 1;
		END IF;
		SELECT COUNT(AT.IID) INTO ACTCOUNT FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		IF ACTCOUNT > 0 THEN
		 SELECT CASE
                   WHEN COUNT(CASE WHEN AT.IACTSTATE=0 THEN 0 ELSE NULL END ) > 0 THEN
                   -- 运行
                    0
                   WHEN COUNT(CASE WHEN AT.IACTSTATE IS NULL THEN 1 WHEN AT.IACTSTATE=1 THEN 1 ELSE NULL END  ) = COUNT(*) THEN
                   -- 未运行
                    1
                   WHEN COUNT(CASE WHEN AT.IACTSTATE IS NULL THEN 1 WHEN AT.IACTSTATE=1 THEN 1 ELSE NULL END) < COUNT(*) AND
                        COUNT(CASE WHEN AT.IACTSTATE IS NULL THEN 1 WHEN AT.IACTSTATE=1 THEN 1 ELSE NULL END) > 0 AND
                        COUNT(CASE WHEN AT.IACTSTATE=0 THEN 0 ELSE NULL END) = 0 THEN
                   -- 部分完成
                    2
                   ELSE
                   -- 完成
                    3
                 END INTO RSTATE FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE GROUP BY AT.IPROJECTNAME;
		ELSE
		    SET RSTATE = 1;
		END IF;
		INSERT INTO IEAI_TOPO_RECORD (SYSTEMNAME, EXCEPTIONTYPE, ACTTYPE, GROUPNAME) VALUES  (PRJ_NAME, NCOUNT, RSTATE, GROUPNAME);
		SELECT (CASE  WHEN COUNT(DECODE(A.IFLOWSTATE, 1, 1, NULL)) = COUNT(*) THEN  0  ELSE 1 END) INTO FINISHSTATE FROM IEAI_ACT_TOPO_INSTANCE A WHERE A.IPROJECTNAME = PRJ_NAME  AND A.IDATADATE = QUERYDATE;
		IF FINISHSTATE = 0 THEN
		    UPDATE IEAI_TOPO_RECORD SET ACTTYPE = 3 WHERE SYSTEMNAME = PRJ_NAME  AND ACTTYPE = 2;
		SELECT COUNT(A.IID) INTO EXCEPAFTERFINISH FROM IEAI_ACT_TOPO_INSTANCE A WHERE A.IFAILSTATE = 1 AND A.IPROJECTNAME = PRJ_NAME  AND A.IDATADATE = QUERYDATE;
		    IF EXCEPAFTERFINISH > 0 THEN
		        UPDATE IEAI_TOPO_RECORD SET ACTTYPE = 4 WHERE SYSTEMNAME = PRJ_NAME;
		    END IF;
		END IF;
		END FOR;
		END;
END
$
-- 8.1.0 version TOPO patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ACT_TOPO_INSTANCE' AND COLNAME='IFIRSTEXCEPTIONTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_ACT_TOPO_INSTANCE ADD IFIRSTEXCEPTIONTIME  DECIMAL(19) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
	 
	 	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ACT_TOPO_INSTANCE_HIS' AND COLNAME='IFIRSTEXCEPTIONTIME';
	 IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_ACT_TOPO_INSTANCE_HIS ADD IFIRSTEXCEPTIONTIME  DECIMAL(19) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	 END IF;
END
	$
	
CREATE	OR REPLACE	PROCEDURE PROC_GANT_STATE 
		(
			IN IN_IACTDEFNAME VARCHAR (255),
			IN IN_IFLOWID INTEGER,
			IN IN_ISTATE VARCHAR (255),
			IN IN_IDESC VARCHAR (255),
			IN IN_IACTNAME VARCHAR (255),
			IN IN_IBEGINEXCTIME VARCHAR (255),
			IN IN_IREADYTIME VARCHAR (255),
			IN IN_IENDTIME VARCHAR (255) 
		)
	BEGIN
		DECLARE V_IPROJECTNAME VARCHAR(255); DECLARE V_IFLOWNAME VARCHAR(255); DECLARE V_IFLOWINSNAME VARCHAR(255); DECLARE V_IACTSTATE VARCHAR(255); DECLARE V_IISFAIL DECIMAL(1); 
		DECLARE V_IFAILSTATE DECIMAL(1); DECLARE V_IISWARN DECIMAL(1); DECLARE V_IFLAG DECIMAL(1);DECLARE V_COUNT DECIMAL(1); DECLARE LN_OPT_ID NUMERIC( 19,0); 
	BEGIN
	SET V_IFAILSTATE = 0; 
	SELECT	COUNT(T.IADAPTORID) INTO	V_IFLAG FROM	IEAI_TOPO_ADAPTOR T WHERE T.IADAPTORSTATE=1 AND T.IADAPACTNAME=IN_IACTDEFNAME; 
	IF V_IFLAG>0 THEN 
	SELECT
		A.IPROJECTNAME,
		A.IFLOWNAME,
		A.IFLOWINSNAME 
	INTO
		V_IPROJECTNAME,
		V_IFLOWNAME,
		V_IFLOWINSNAME 
	FROM
		IEAI_WORKFLOWINSTANCE A 
	WHERE
		A.IFLOWID = IN_IFLOWID; 
	SELECT		F_INSTANCENAME_CHECK_DATE(V_IFLOWINSNAME) 
	INTO		V_IFLAG 
	FROM		IDUAL; 
	IF V_IFLAG = 0 THEN 
	SET		V_IFLOWINSNAME = TO_CHAR(TO_DATE(V_IFLOWINSNAME, 'YYYYMMDD'), 'YYYY-MM-DD'); 
	IF IN_ISTATE = 'Fail:DisConnectAgent' THEN 
	SET		V_IISFAIL = 1; 
	SET		V_IFAILSTATE = 1; ELSEIF IN_ISTATE = 'Fail:Timeoff' THEN 
	SET		V_IISFAIL = 2; 
	SET		V_IFAILSTATE = 1; ELSEIF IN_ISTATE = 'Fail' THEN 
	SET		V_IISFAIL = 4; 
	SET		V_IFAILSTATE = 1; ELSEIF IN_ISTATE = 'Fail:Business' THEN 
	SET		V_IISFAIL = 3; 
	SET		V_IFAILSTATE = 1; ELSE 
	SET		V_IISFAIL = 0; 
	END		IF; 
	IF IN_ISTATE = 'Null' THEN 
	SET		V_IACTSTATE = '1'; ELSEIF IN_ISTATE = 'Finished' THEN 
	SET		V_IACTSTATE = '2'; 
	SET		V_IISFAIL = 0; 
	SET		V_IISWARN = 0; ELSEIF IN_ISTATE = 'Skipped' THEN 
	SET		V_IACTSTATE = '2'; 
	SET		V_IISFAIL = 0; 
	SET		V_IISWARN = 0; ELSE 
	SET		V_IACTSTATE = '0'; 
	END		IF; 
	IF IN_IACTDEFNAME = 'UserTask' AND (IN_IDESC LIKE '异常_%' OR IN_IDESC LIKE '%_异常%' OR IN_IACTNAME LIKE '异常_%' OR IN_IACTNAME LIKE '_异常%') THEN IF IN_IACTNAME LIKE '异常_%' 
		THEN 
		
	IF IN_ISTATE = 'Finished' OR IN_ISTATE = 'ManualFinish' THEN 
		UPDATE IEAI_ACT_TOPO_INSTANCE B 
		SET
			B.IISFAIL = 0,
			B.IACTSTATE = '2',
			B.IFAILSTATE = 1 
		WHERE
			B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 
			
		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 

		UPDATE
			IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = 2,
			B.IREALFINISHTIME = IN_IENDTIME,
			B.IISFAIL = 1 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.INSTANCENAME = V_IFLOWINSNAME; 
		UPDATE
			IEAI_TOPO_WARNING 
		SET
			ICONFIRMRING = 1 
		WHERE
			IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)); 		
	ELSE 
		UPDATE
			IEAI_ACT_TOPO_INSTANCE B 
		SET
			B.IISFAIL = 3,
			B.IACTSTATE = '0',
			B.IFAILSTATE = V_IFAILSTATE 
		WHERE
			B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 

		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND	B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.IDATADATE = V_IFLOWINSNAME; 

		UPDATE
			IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = 3,
			B.IISFAIL = 1 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK = SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)) AND B.INSTANCENAME = V_IFLOWINSNAME; 

		CALL  PROC_GET_NEXT_PK ('ieai_topo_warning', LN_OPT_ID); 
		
		SELECT COUNT(1) INTO V_COUNT
				FROM IEAI_ACT_TOPO_INSTANCE B
				WHERE B.IPROJECTNAME = V_IPROJECTNAME
				 AND B.IFLOWNAME = V_IFLOWNAME
				 AND B.IACTNAME =
					 SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME))
				 AND B.IDATADATE = V_IFLOWINSNAME;
				IF V_COUNT > 0 THEN
				INSERT
					INTO
						IEAI_TOPO_WARNING 
						(
							IID,
							IDATADATE,
							IWARNTIME,
							IPRJNAME,
							IFLOWNAME,
							IACTNAME,
							IWARNINGTYPE,
							ICONFIRMRING 
						)
					VALUES
						(
							LN_OPT_ID,
							V_IFLOWINSNAME,
							FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP, 8),
							V_IPROJECTNAME,
							V_IFLOWNAME,
							SUBSTR(IN_IACTNAME, 4, LENGTH(IN_IACTNAME)),
							3,
							0 
						); 
				END IF;
	END	IF; 
	ELSE 
	IF IN_ISTATE = 'Finished' OR IN_ISTATE = 'ManualFinish' THEN 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IISFAIL = 0,
		B.IACTSTATE = '2',
		B.IFAILSTATE = 1 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
	UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND	B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
	UPDATE
		IEAI_TOPO_SUB_COMMAND B 
	SET
		B.ISTATUS = 2,
		B.IISFAIL = 0 
	WHERE
		B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.INSTANCENAME = V_IFLOWINSNAME; 
	
	UPDATE
		IEAI_TOPO_WARNING 
	SET
		ICONFIRMRING = 1 
	WHERE
		IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3); 
	ELSE 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IISFAIL = 3,
		B.IACTSTATE = '0',
		B.IFAILSTATE = V_IFAILSTATE 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND	B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.IDATADATE = V_IFLOWINSNAME; 
		
	UPDATE
		IEAI_TOPO_SUB_COMMAND B 
	SET
		B.ISTATUS = 0,
		B.IISFAIL = 3 
	WHERE
		B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3) AND B.INSTANCENAME = V_IFLOWINSNAME; 
	CALL  PROC_GET_NEXT_PK ('ieai_topo_warning', LN_OPT_ID); 
	IF V_IACTSTATE = '2' THEN 
	UPDATE
		IEAI_TOPO_WARNING 
	SET
		ICONFIRMRING = 1 
	WHERE
		IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = SUBSTR(IN_IACTNAME, 1, LENGTH(IN_IACTNAME) - 3); 
	END	IF; 
	END	IF; 
	END	IF; 
	ELSE 
	IF IN_IACTDEFNAME = 'UserTask' THEN 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IREALBEGINTIME = IN_IREADYTIME,
		B.IREALENDTIME = IN_IENDTIME,
		B.IACTSTATE = V_IACTSTATE,
		B.IISFAIL = V_IISFAIL,
		B.IFAILSTATE = DECODE(B.IFAILSTATE, 1, B.IFAILSTATE, V_IFAILSTATE),
		B.IISWARN = DECODE(V_IISWARN, NULL, B.IISWARN, V_IISWARN) 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	UPDATE
		IEAI_TOPO_SUB_COMMAND B 
	SET
		B.ISTATUS = V_IACTSTATE,
		B.IREALBEGINTIME= IN_IBEGINEXCTIME,
		B.IREALFINISHTIME = IN_IENDTIME,
		B.IISFAIL = 0 
	WHERE
		B.IINSYSNAME = V_IPROJECTNAME
		-- AND B.IINFLOWNAME = V_IFLOWNAME
		AND B.IWORKTASK =IN_IACTNAME AND B.INSTANCENAME = V_IFLOWINSNAME; 
	ELSE 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.IREALBEGINTIME = IN_IBEGINEXCTIME,
		B.IREALENDTIME = IN_IENDTIME,
		B.IACTSTATE = V_IACTSTATE,
		B.IISFAIL = V_IISFAIL,
		B.IFAILSTATE = DECODE(B.IFAILSTATE, 1, B.IFAILSTATE, V_IFAILSTATE),
		B.IISWARN = DECODE(V_IISWARN, NULL, B.IISWARN, V_IISWARN) 
	WHERE
		B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 

	IF V_IISFAIL>0 THEN 
	
		UPDATE IEAI_ACT_TOPO_INSTANCE B
      SET B.Ifirstexceptiontime = fun_get_date_number_new(CURRENT TIMESTAMP, 8)
               WHERE B.Ifirstexceptiontime = 0 AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	
		UPDATE	IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = 3,
			B.IREALBEGINTIME= IN_IBEGINEXCTIME,
			B.IREALFINISHTIME = IN_IENDTIME,
			B.IISFAIL = V_IISFAIL 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =IN_IACTNAME AND B.INSTANCENAME = V_IFLOWINSNAME; 
	ELSE 
		UPDATE 	IEAI_TOPO_SUB_COMMAND B 
		SET
			B.ISTATUS = V_IACTSTATE,
			B.IREALBEGINTIME= IN_IBEGINEXCTIME,
			B.IREALFINISHTIME = IN_IENDTIME,
			B.IISFAIL = V_IISFAIL 
		WHERE
			B.IINSYSNAME = V_IPROJECTNAME AND B.IWORKTASK =IN_IACTNAME AND B.INSTANCENAME = V_IFLOWINSNAME; 
	END IF;
	END IF; 
	END IF; 

	IF V_IISFAIL!=0 THEN IF IN_ISTATE = 'Fail:Timeoff' THEN 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.ITIMEOUTNUM = B.ITIMEOUTNUM + 1 
	WHERE
		B.ITIMEOUTNUM > 0 AND B.ITIMEOUTNUM IS NOT NULL AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	UPDATE
		IEAI_ACT_TOPO_INSTANCE B 
	SET
		B.ITIMEOUTTIME = 0,
		B.IFIRSTTIMEOUTTIME = FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8),
		B.ITIMEOUTNUM = 1 
	WHERE
		(B.ITIMEOUTNUM = 0 OR B.ITIMEOUTNUM IS NULL) AND B.IPROJECTNAME = V_IPROJECTNAME AND B.IFLOWNAME = V_IFLOWNAME AND B.IACTNAME = IN_IACTNAME AND B.IDATADATE = V_IFLOWINSNAME; 
	END		IF;
	
	SELECT COUNT(1) INTO V_COUNT
			FROM IEAI_ACT_TOPO_INSTANCE B
			WHERE B.IPROJECTNAME = V_IPROJECTNAME
					 AND B.IFLOWNAME = V_IFLOWNAME
					 AND B.IACTNAME = IN_IACTNAME
					 AND B.IDATADATE = V_IFLOWINSNAME;
	IF V_COUNT > 0 THEN
		IF V_IISFAIL != 0 THEN 

		CALL PROC_GET_NEXT_PK ('ieai_topo_warning', LN_OPT_ID); 
		
		INSERT
		INTO
			IEAI_TOPO_WARNING 
			(
				IID,
				IDATADATE,
				IWARNTIME,
				IPRJNAME,
				IFLOWNAME,
				IACTNAME,
				IWARNINGTYPE,
				ICONFIRMRING 
			)
		VALUES
			(
				LN_OPT_ID,
				V_IFLOWINSNAME,
				FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP,8),
				V_IPROJECTNAME,
				V_IFLOWNAME,
				IN_IACTNAME,
				V_IISFAIL,
				0 
			)
			; 
		END		IF; 
	END		IF; 
	IF V_IACTSTATE = '2' THEN 
	UPDATE
		IEAI_TOPO_WARNING 
	SET
		ICONFIRMRING = 1 
	WHERE
		IDATADATE = V_IFLOWINSNAME AND IPRJNAME = V_IPROJECTNAME AND IACTNAME = IN_IACTNAME; 
	END	IF; 
	END	IF; 
	END	IF; 
	END	IF; 
	END	; 
END
$

---8.9--
CREATE OR REPLACE PROCEDURE PROC_GANT_WORKFLOW_STATE(IN IN_IPROJECTNAME  VARCHAR(255),
                                                     IN IN_IFLOWNAME     VARCHAR(255),
                                                     IN IN_IFLOWINSNAME  VARCHAR(255),
                                                     IN IN_ISTATUS       INT,
                                                     IN IN_ISTARTTIME    DECIMAL(19),
                                                     IN IN_IENDTIME      DECIMAL(19),
                                                     IN IN_FLAG          INT,
                                                     IN IN_FLOWID       DECIMAL(19))
	BEGIN
    DECLARE V_IDATADATE   VARCHAR(255);
    DECLARE V_IFLAG     DECIMAL(10);
    DECLARE V_ICALLACTNAME  VARCHAR(255);
    DECLARE V_ICALLACTNAMECOUNT DECIMAL(10);
    DECLARE V_IEXECDATE    DECIMAL(19);
		-- db2　　
		--  DECLARE EXIT HANDLER FOR SQLWARNING,NOT FOUND,SQLEXCEPTION set V_IFLAG=-1;
	BEGIN
    SELECT F_INSTANCENAME_CHECK_DATE(IN_IFLOWINSNAME) INTO V_IFLAG   FROM IDUAL;
       IF V_IFLAG = 0 THEN
	SET V_IDATADATE = TO_CHAR(TO_DATE(IN_IFLOWINSNAME, 'YYYYMMDD'),	'YYYY-MM-DD');

        SELECT COUNT(A.IID)
        INTO V_ICALLACTNAMECOUNT
        FROM IEAI_CALLWORKFLOW_INFO C, IEAI_ACTRUNTIME A
       WHERE C.IMAINFLOWID = A.IFLOWID
         AND C.ICALLFLOWACTID = A.IACTID
         AND C.ICALLFLOWID = IN_FLOWID;
      IF (V_ICALLACTNAMECOUNT > 0) THEN
        SELECT A.IACTNAME
          INTO V_ICALLACTNAME
          FROM IEAI_CALLWORKFLOW_INFO C, IEAI_ACTRUNTIME A
         WHERE C.IMAINFLOWID = A.IFLOWID
           AND C.ICALLFLOWACTID = A.IACTID
           AND C.ICALLFLOWID = IN_FLOWID;
      END IF;
      IF V_ICALLACTNAME = '' OR V_ICALLACTNAME IS NULL THEN
        SET V_ICALLACTNAME = IN_IFLOWNAME;
      END IF;

		IF IN_ISTATUS = 2 OR IN_ISTATUS = 4 OR IN_ISTATUS = 25 THEN

        UPDATE IEAI_ACT_TOPO_INSTANCE
           SET IFAILSTATE = 1
         WHERE (SELECT COUNT(*)
                  FROM IEAI_ACT_TOPO_INSTANCE
                 WHERE IPROJECTNAME = IN_IPROJECTNAME
                   AND IFLOWNAME = IN_IFLOWNAME
                   AND IDATADATE = V_IDATADATE
                   AND (IISFAIL >= 0 OR IISWARN >= 0)
                   AND IACTSTATE = '0') > 0
           AND IPROJECTNAME = IN_IPROJECTNAME
           AND IFLOWNAME = IN_IFLOWNAME
           AND IDATADATE = V_IDATADATE
           AND IACTSTATE = '0';

		UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE = '2', B.IISFAIL = 0, B.IISWARN = 0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IDATADATE = V_IDATADATE
           AND B.IACTSTATE = '0';
      
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE = '2'
         WHERE B.IRUNSTATE = '1'
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IDATADATE = V_IDATADATE;
           
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IRUNSTATE = '1'
         WHERE (B.IACTSTATE IS NULL OR B.IACTSTATE=1)
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IDATADATE = V_IDATADATE;
      
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE   = '2',
               B.IISFAIL     = -1,
               B.IISWARN     = -1,
               B.IEXPENDTIME = 0,
               B.IFLOWSTATE  = IN_ISTATUS
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IDATADATE = V_IDATADATE
           AND B.IACTSTATE = '0';
           
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IFLOWSTATE = 1
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IDATADATE = V_IDATADATE;
      
        UPDATE IEAI_TOPO_SUB_COMMAND B

           SET B.ISTATUS         = 2,
               B.IISFAIL         = 0,
         B.IREALBEGINTIME = IN_ISTARTTIME,
               B.IREALFINISHTIME = IN_IENDTIME
         WHERE B.IINSYSNAME = IN_IPROJECTNAME
           AND B.IWORKTASK = IN_IFLOWNAME
           AND B.INSTANCENAME = V_IDATADATE;
      
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IREALENDTIME = IN_IENDTIME
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND (B.IREALENDTIME = 0
           OR B.IREALENDTIME IS NULL)
           AND B.IDATADATE = V_IDATADATE;
           
        UPDATE IEAI_TOPO_WARNING A
           SET A.ICONFIRMRING = 1
         WHERE A.IPRJNAME = IN_IPROJECTNAME
           AND A.IFLOWNAME = IN_IFLOWNAME
           AND A.IDATADATE = V_IDATADATE;
      END IF;
    
      IF IN_ISTATUS = 0 AND IN_FLAG = 0 THEN
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE     = '1',
               B.IFLOWSTATE    = '0',
               B.IEXPSTARTTIME = 0,
               B.IEXPENDTIME  = 0,
               B.IISFAIL       = 0,
               B.IREALBEGINTIME = 0,
               B.IREALENDTIME=0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IDATADATE = V_IDATADATE;
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IREALBEGINTIME = 0,
               B.IREALENDTIME=0,
               B.IACTSTATE     = '1',
               B.IFLOWSTATE    = '0',
               B.IEXPSTARTTIME = 0,
               B.IEXPENDTIME  = 0,
               B.IISFAIL       = 0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IFLOWNAME = IN_IFLOWNAME
           AND B.IREALBEGINTIME <> 0
           AND B.IREALBEGINTIME IS NOT NULL
           AND B.IDATADATE = V_IDATADATE;
      END IF;
    
      IF IN_ISTATUS = 0 AND IN_FLAG = 1 THEN
        UPDATE IEAI_ACT_TOPO_INSTANCE B
           SET B.IACTSTATE = '0', B.IFLOWSTATE = 0
         WHERE B.IPROJECTNAME = IN_IPROJECTNAME
           AND B.IMAINFLOWNAME = V_ICALLACTNAME
           AND B.IDATADATE = V_IDATADATE;
      END IF;
    IF IN_ISTATUS = 0 THEN
      UPDATE IEAI_TOPO_SUB_COMMAND B
       SET B.ISTATUS        = IN_ISTATUS,
         B.IISFAIL        = 0,
         B.IREALBEGINTIME = IN_ISTARTTIME
       WHERE B.IINSYSNAME = IN_IPROJECTNAME
       AND B.IWORKTASK = IN_IFLOWNAME
       AND B.INSTANCENAME = V_IDATADATE;
    END IF;
    END IF;
	END;
	END
$

CREATE OR REPLACE PROCEDURE PROC_GANT( IN QUERYDATE VARCHAR (255), IN FLAG INT )
	LANGUAGE SQL
	BEGIN
	DECLARE LV_QUERYDATE DECIMAL(19);
	DECLARE LV_IID DECIMAL(19);
	BEGIN
	SET LV_QUERYDATE = FUN_GET_DATE_NUMBER_NEW(TO_DATE(QUERYDATE, 'YYYY-MM-DD'),8);
	IF FLAG = 0	THEN
	CALL PROC_GET_NEXT_PK('TMP_IEAI_TOPO_INSTANCE_QUERY', LV_IID);
	INSERT INTO TMP_IEAI_TOPO_INSTANCE_QUERY (OPT_ID, IID)   SELECT LV_IID, 	A.IID   FROM IEAI_ACT_RELATION_MESS A  WHERE A.IID NOT IN (SELECT IID   FROM IEAI_ACT_TOPO_INSTANCE B   WHERE B.IDATADATE = QUERYDATE);
	INSERT INTO IEAI_ACT_TOPO_INSTANCE
	(IID,
	 IDATADATE,
	 IPROJECTNAME,
	 IFLOWNAME,
	 IFIRSTFLOWNAME,
	 IPROJECTID,
	 IACTNAME,
	 IACTDES,
	 IAGENTIP,
	 IAGENTPORT,
	 IBATCHNAME,
	 IBATCHPATH,
	 ITIMEOUT,
	 IREALBEGINTIME,
	 IREALENDTIME,
	 IACTSTATE,
	 IFLOWSTATE,
	 IAVGTIME,
	 IISFAIL,
	 IISWARN,
	 IUPSYSNAME,
	 IUPACTNAME,
	 IUPACTSTATE,
	 IDOWNSYSNAME,
	 IDOWNACTNAME,
	 IDOWNACTSTATE,
	 IBUSINESSBEGINTIME,
	 IBUSINESSBEGINTIMETYPE,
	 IARRIVETIME,
	 IARRIVETIMETYPE,
	 IMAINFLOWNAME,
	 IEXPSTARTTIME,
	 IEXPENDTIME,
	 IRUNTIME,
	 IACROSSDAY_NUM,
	 IAVGRULE,
	 IISSYNCCALL,
	 IPARALLELFLAG)
	  (SELECT
		 IID,
		 QUERYDATE,
		 IPROJECTNAME,
		 IFLOWNAME,
		 IFIRSTFLOWNAME,
		 IPROJECTID,
		 IACTNAME,
		 IACTDES,
		 IAGENTIP,
		 IAGENTPORT,
		 IBATCHNAME,
		 IBATCHPATH,
		 ITIMEOUT,
		 IREALBEGINTIME,
		 IREALENDTIME,
		 '1',
		 IFLOWSTATE,
		 IAVGTIME,
		 IISFAIL,
		 IISWARN,
		 IUPSYSNAME,
		 IUPACTNAME,
		 IUPACTSTATE,
		 IDOWNSYSNAME,
		 IDOWNACTNAME,
		 IDOWNACTSTATE,
		 IBUSINESSBEGINTIME,
		 IBUSINESSBEGINTIMETYPE,
		 IARRIVETIME,
		 IARRIVETIMETYPE,
		 IMAINFLOWNAME,
		 IEXPSTARTTIME,
		 IEXPENDTIME,
		 IRUNTIME,
		 IACROSSDAY_NUM,
		 NULL,
		 IISSYNCCALL,
		 IPARALLELFLAG
	   FROM IEAI_ACT_RELATION_MESS A   WHERE A.IID IN (SELECT IID   FROM TMP_IEAI_TOPO_INSTANCE_QUERY B   WHERE B.OPT_ID = LV_IID));
	-- 活动跨天规则
	UPDATE IEAI_ACT_TOPO_INSTANCE AA 
       SET (AA.IACROSSDAY_NUM) = ( SELECT A.IACROSSDAY_NUM FROM IEAI_ACROSSDAY_RULE A WHERE A.ISYSTEM_NAME = AA.IPROJECTNAME AND A.IFLOW_NAME = AA.IMAINFLOWNAME)
	   WHERE AA.IID IN ( SELECT IID FROM TMP_IEAI_TOPO_INSTANCE_QUERY B WHERE B.OPT_ID = LV_IID)
	         AND AA.IDATADATE = QUERYDATE;
	-- 活动平均耗时
    UPDATE IEAI_ACT_TOPO_INSTANCE AA
       SET (AA.IAVGTIME) =
           (SELECT MAX(A.IAVGTIME)
              FROM IEAI_ACTRUNTIME_AVGRECORD A
             WHERE A.IPRJNAME = AA.IPROJECTNAME
               AND A.IFLOWNAME = AA.IFLOWNAME
               AND A.IACTNAME = AA.IACTNAME
               AND (A.ITIMEMONTH = FUN_DATE_TRANCE(LV_QUERYDATE +
                                                   DECODE(AA.IACROSSDAY_NUM,
                                                          NULL,
                                                          0,
                                                          AA.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                   0) OR A.ITIMEMONTH IS NULL)
               AND (A.ITIMEDAY = FUN_DATE_TRANCE(LV_QUERYDATE +
                                                 DECODE(AA.IACROSSDAY_NUM,
                                                        NULL,
                                                        0,
                                                        AA.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                 1) OR
                   A.ITIMEDAY = FUN_DATE_TRANCE(LV_QUERYDATE +
                                                 DECODE(AA.IACROSSDAY_NUM,
                                                        NULL,
                                                        0,
                                                        AA.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                 2) OR A.ITIMEDAY IS NULL)
               AND (WEEKFORMAT(A.ITIMEWEEK) LIKE
                   '%' || FUN_DATE_TRANCE(LV_QUERYDATE +
                                           DECODE(AA.IACROSSDAY_NUM,
                                                  NULL,
                                                  0,
                                                  AA.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                           3) || '%' OR A.ITIMEWEEK IS NULL)
               AND A.IISRULE = 1)
     WHERE AA.IID IN (SELECT IID
                        FROM TMP_IEAI_TOPO_INSTANCE_QUERY B
                       WHERE B.OPT_ID = LV_IID)
       AND AA.IDATADATE = QUERYDATE;

	UPDATE IEAI_ACT_TOPO_INSTANCE AA
	SET (AA.IAVGTIME) = ( SELECT DECODE(AA.IAVGTIME, NULL, MAX(A.IAVGTIME), AA.IAVGTIME) FROM IEAI_ACTRUNTIME_AVGRECORD A
        WHERE A.IPRJNAME = AA.IPROJECTNAME
        AND A.IFLOWNAME = AA.IFLOWNAME
        AND A.IACTNAME = AA.IACTNAME
        AND A.ITIMEMONTH IS NULL
        AND A.ITIMEDAY IS NULL
        AND A.ITIMEWEEK IS NULL
        AND A.IISRULE = 0)
	WHERE AA.IAVGTIME IS NULL
        AND AA.IID IN ( SELECT IID  FROM TMP_IEAI_TOPO_INSTANCE_QUERY B  WHERE B.OPT_ID = LV_IID)
        AND AA.IDATADATE = QUERYDATE;
	-- 超时阀值
	UPDATE IEAI_ACT_TOPO_INSTANCE AA
	SET AA.ITIMEOUT = (SELECT (ITASK_TIMEOUTNUM * 60)
					   FROM (SELECT
							   B.IID,
							   MAX(A.ITASK_TIMEOUTNUM) AS ITASK_TIMEOUTNUM
							 FROM IEAI_ACT_TIMEOUTCONFIG A,
							   IEAI_ACT_RELATION_MESS B
							 WHERE A.ITIMEOUT_MONTH IS NULL
								   AND A.ITIMEOUT_DAY IS NULL
								   AND A.ITIMEOUT_WEEK IS NULL
								   AND A.ITASK_NAME = B.IACTNAME
								   AND A.ISYSTEM_NAME = B.IPROJECTNAME
							 GROUP BY B.IID) AAA
					   WHERE AAA.IID = AA.IID)
	WHERE AA.IID IN (SELECT IID
					 FROM TMP_IEAI_TOPO_INSTANCE_QUERY B
					 WHERE B.OPT_ID = LV_IID)
		  AND AA.IDATADATE = QUERYDATE;

	UPDATE IEAI_ACT_TOPO_INSTANCE AA
       SET (AA.ITIMEOUT, AA.IAVGRULE) =
           (SELECT DECODE(MAX(ITASK_TIMEOUTNUM),
                          NULL,
                          AA.ITIMEOUT,
                          MAX(ITASK_TIMEOUTNUM) * 60),
                   MAX('月:' || DECODE(ITIMEOUT_MONTH, NULL, '无', ITIMEOUT_MONTH) ||
                       ',日:' || DECODE(ITIMEOUT_DAY, NULL, '无', ITIMEOUT_DAY) ||
                       ',周:' || DECODE(ITIMEOUT_WEEK, NULL, '无', ITIMEOUT_WEEK))
              FROM (SELECT B.IID,
                           A.ITASK_TIMEOUTNUM,
                           A.ITIMEOUT_MONTH,
                           A.ITIMEOUT_DAY,
                           A.ITIMEOUT_WEEK,
                           ROW_NUMBER() OVER(PARTITION BY B.IID ORDER BY A.ITASK_TIMEOUTNUM DESC) AS RO
                      FROM IEAI_ACT_TIMEOUTCONFIG A, IEAI_ACT_RELATION_MESS B
                     WHERE A.ISYSTEM_NAME = B.IPROJECTNAME
                       AND A.ITASK_NAME = B.IACTNAME
                       AND (A.ITIMEOUT_MONTH =
                           TO_NUMBER(FUN_DATE_TRANCE(LV_QUERYDATE +
                                                      DECODE(B.IACROSSDAY_NUM,
                                                             NULL,
                                                             0,
                                                             B.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                      0)) OR
                           A.ITIMEOUT_MONTH IS NULL)
                       AND (A.ITIMEOUT_DAY =
                           TO_NUMBER(FUN_DATE_TRANCE(LV_QUERYDATE +
                                                      DECODE(B.IACROSSDAY_NUM,
                                                             NULL,
                                                             0,
                                                             B.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                      1)) OR
                           A.ITIMEOUT_DAY =
                           TO_NUMBER(FUN_DATE_TRANCE(LV_QUERYDATE +
                                                      DECODE(B.IACROSSDAY_NUM,
                                                             NULL,
                                                             0,
                                                             B.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                      2)) OR
                           A.ITIMEOUT_DAY IS NULL)
                       AND (WEEKFORMAT(A.ITIMEOUT_WEEK) LIKE
                           '%' || FUN_DATE_TRANCE(LV_QUERYDATE +
                                                   DECODE(B.IACROSSDAY_NUM,
                                                          NULL,
                                                          0,
                                                          B.IACROSSDAY_NUM) * 24 * 60 * 60 * 1000,
                                                   3) || '%' OR
                           A.ITIMEOUT_WEEK IS NULL)
                       AND A.ITASK_TIMEOUTNUM IS NOT NULL
                       AND A.IID NOT IN
                           (SELECT A.IID
                              FROM IEAI_ACT_TIMEOUTCONFIG A
                             WHERE A.ITIMEOUT_MONTH IS NULL
                               AND A.ITIMEOUT_DAY IS NULL
                               AND A.ITIMEOUT_WEEK IS NULL)) AAA
             WHERE AAA.RO = 1
               AND AAA.IID = AA.IID)
     WHERE AA.IID IN (SELECT IID
                        FROM TMP_IEAI_TOPO_INSTANCE_QUERY B
                       WHERE B.OPT_ID = LV_IID)
       AND AA.IDATADATE = QUERYDATE;


	-- 删除关联信息
	DELETE FROM IEAI_ACT_TOPO_INS_UPDOWN
	WHERE IDATADATE = QUERYDATE;
	-- 上关联信息
	INSERT INTO IEAI_ACT_TOPO_INS_UPDOWN
	(IACTMESSID,
	 IDATADATE,
	 ISYSTEMNAME,
	 IACTNAME,
	 IACTDES,
	 IUPDOWNTYPE)
	  (SELECT DISTINCT
		 AA.IID,
		 AA.IDATADATE,
		 A.LASTSYSNAME,
		 A.LASTTASKNAME,
		 A.LASTTASKDES,
		 0
	   FROM IEAI_TOPOINFO A, IEAI_ACT_TOPO_INSTANCE AA
	   WHERE A.SYSTEMNAME = AA.IPROJECTNAME
			 AND A.TASKNAME = AA.IACTNAME
			 AND A.LASTSYSNAME IS NOT NULL
			 AND AA.IDATADATE = QUERYDATE);

	-- 下关联信息
	INSERT INTO IEAI_ACT_TOPO_INS_UPDOWN (IACTMESSID, IDATADATE,  ISYSTEMNAME,  IACTNAME,  IACTDES,  IUPDOWNTYPE)
	  (SELECT DISTINCT  AA.IID,  AA.IDATADATE,  A.SYSTEMNAME,  A.TASKNAME,  A.TASKDES,  1
	   FROM IEAI_TOPOINFO A, IEAI_ACT_TOPO_INSTANCE AA
	   WHERE A.LASTSYSNAME = AA.IPROJECTNAME
			 AND A.LASTTASKNAME = AA.IACTNAME
			 AND A.SYSTEMNAME IS NOT NULL
			 AND AA.IDATADATE = QUERYDATE);
	END IF;
	-- 上下关联状态

	UPDATE IEAI_ACT_TOPO_INS_UPDOWN AA
	SET (AA.IACTSTATE) = ( SELECT CASE
            WHEN A.IISFAIL = 1 THEN
            'AGENT连不上'
            WHEN A.IISFAIL = 2 THEN
            '超时'
            WHEN A.IISFAIL = 3 THEN
            '业务异常'
            WHEN A.IISFAIL = 4 THEN
            '其他异常'
            WHEN A.IISWARN = 1 THEN
            '时点报警'
            WHEN A.IISWARN = 2 THEN
            '时点预警'
            WHEN A.IACTSTATE = '0' THEN
            '运行中'
            WHEN A.IACTSTATE = '1' OR A.IACTSTATE IS NULL THEN
            '未运行'
            WHEN A.IACTSTATE = '2' THEN
            '已完成'
            WHEN A.IACTSTATE = '3' THEN
            '略过'
       END AS IUPACTSTATE
       FROM IEAI_ACT_TOPO_INSTANCE A
       WHERE A.IPROJECTNAME = AA.ISYSTEMNAME
              AND A.IACTNAME = AA.IACTNAME
              AND A.IDATADATE = QUERYDATE fetch first 1 rows only)
    WHERE AA.IDATADATE = QUERYDATE;

	-- 警报清除
	UPDATE IEAI_TOPO_WARNING A
	SET A.IWARNINGSTATE = 1
	WHERE A.IDATADATE = QUERYDATE   
    AND A.IACTMESID IN (SELECT B.IID   FROM IEAI_ACT_TOPO_INSTANCE B  WHERE B.IDATADATE = QUERYDATE AND B.IACTSTATE = '2' OR B.IACTSTATE = '3');

	-- 更新超时时间
	UPDATE IEAI_ACT_TOPO_INSTANCE B
	SET B.ITIMEOUTTIME = (decode(B.irealendtime,
										   0,
										   FUN_GET_DATE_NUMBER_NEW(current timestamp,8),
										   B.irealendtime) -
						 B.IFIRSTTIMEOUTTIME)
	WHERE B.ITIMEOUTNUM > 0
		  AND B.IACTSTATE = '0'
		  AND B.IDATADATE = QUERYDATE;
	
      INSERT INTO IEAI_TOPOINFO_BUSS_G_INS
        (IID,
         SYSTEMNAME,
         GRO,
         IPGROUPNUM,
         IPGROUPSORT,
         IPGROUPDES,
         ISTATE,
         IPGROUPTYPE,
         ISTARTTIME,
         IENDTIME,
         IDATADATE)
        (SELECT IID,
                SYSTEMNAME,
                GRO,
                IPGROUPNUM,
                IPGROUPSORT,
                IPGROUPDES,
                ISTATE,
                IPGROUPTYPE,
                ISTARTTIME,
                IENDTIME,
                QUERYDATE
           FROM IEAI_TOPOINFO_BUSS_GROUP A
          WHERE A.IID NOT IN (SELECT IID
	                               FROM IEAI_TOPOINFO_BUSS_G_INS B
	                              WHERE B.IDATADATE = QUERYDATE));

	 UPDATE IEAI_TOPOINFO_BUSS_G_INS G
       SET G.ISTATE   = 2,
           G.IENDTIME =FUN_GET_DATE_NUMBER_NEW(TO_DATE((SELECT QUERYDATE || ' ' || TO_CHAR(SYSDATE, 'HH24:MI:SS') FROM SYSIBM.SYSDUMMY1),'YYYY-MM-DD HH24:MI:SS'),8)
     WHERE G.IDATADATE = QUERYDATE
       AND G.IPGROUPNUM NOT IN
           (SELECT DISTINCT IPGROUPNUM
              FROM (SELECT M.ISYSNAME,
                           M.IJOBNAME,
                           M.IDATATIME,
                           M.IPGROUPNUM,
                           DECODE(N.ISTATE, NULL, -1, N.ISTATE) AS ISTATE
                      FROM (SELECT A.IPGROUPNUM,
                                   B.IJOBNAME,
                                   B.ISYSNAME,
                                   B.IDATATIME
                              FROM IEAI_TOPOINFO_BUSS A, IEAI_BATCH_INI_INFO B
                             WHERE A.SYSTEMNAME = B.ISYSNAME
                               AND A.TASKNAME = B.IJOBNAME
                               AND B.IDATATIME = TO_CHAR(TO_DATE(QUERYDATE, 'YYYY-MM-DD'), 'YYYYMMDD')) M
                      LEFT JOIN IEAI_DATAMAPPING_OUTPUT N
                        ON M.IJOBNAME = N.IACTNAME
                       AND M.ISYSNAME = N.IPRJNAME
                       AND M.IDATATIME =TO_CHAR(TO_DATE(N.IINSTANCENAME, 'YYYY-MM-DD'),'YYYYMMDD')
                       AND M.IDATATIME =TO_CHAR(TO_DATE(QUERYDATE, 'YYYY-MM-DD'),'YYYYMMDD'))
             WHERE ISTATE IN (-1, 0, 1, 3))
       AND G.ISTATE <> 2;
       DELETE FROM TMP_IEAI_TOPO_INSTANCE_QUERY WHERE OPT_ID=LV_IID;
	END;
	END
$

BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_SYSNAME_ABB' AND COLNAME='ITXRULE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'ALTER TABLE IEAI_SYSNAME_ABB ADD ITXRULE DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$
--8.11.0
BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_RECORD' AND COLNAME = 'ITOTAL';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_TOPO_RECORD ADD ITOTAL  DECIMAL(10) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_RECORD' AND COLNAME = 'IFINSH_TOTAL';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_TOPO_RECORD  ADD IFINSH_TOTAL  DECIMAL(10) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_RECORD' AND COLNAME = 'IFINISH_NUM';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_TOPO_RECORD  ADD IFINISH_NUM  DECIMAL(10) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
		
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_TOPO_RECORD' AND COLNAME = 'IALL_NUM';
		IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = '	ALTER TABLE  IEAI_TOPO_RECORD  ADD IALL_NUM  DECIMAL(10) DEFAULT 0';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
	END	IF;
	
END
$
CREATE OR REPLACE PROCEDURE PROC_TOPO_SYSTEM( IN QUERYDATE VARCHAR (255),IN GROUPNAME VARCHAR (255)) 
		LANGUAGE SQL
		BEGIN
		DECLARE PRJ_NAME VARCHAR(255);
		DECLARE NSTATE DECIMAL(19);
		DECLARE RSTATE DECIMAL(19);
		DECLARE FSTATE DECIMAL(19);
		DECLARE NCOUNT DECIMAL(19);
		DECLARE ACTCOUNT DECIMAL(19);
		DECLARE FINISHSTATE DECIMAL(19);
		DECLARE FLOWINSTANCE DECIMAL(19);
		DECLARE MESS DECIMAL(19);
		DECLARE EXCEPAFTERFINISH DECIMAL(19);
		DECLARE FINCOUNT         DECIMAL(19);
    DECLARE ALLCOUNT         DECIMAL(19);
    DECLARE ALLFINCOUNT      DECIMAL(19);
		-- 清理数据表-- 
		DELETE FROM IEAI_TOPO_RECORD;
		DELETE FROM TMP_TOPO_RECORD;
		IF LENGTH(GROUPNAME) > 0 THEN
		    INSERT INTO TMP_TOPO_RECORD (SYSTEMNAME) SELECT DISTINCT SYSTEMNAME FROM (SELECT SYSTEMNAME   FROM IEAI_TOPOINFO   WHERE GRO = GROUPNAME  UNION   SELECT LASTSYSNAME AS SYSTEMNAME  FROM IEAI_TOPOINFO  WHERE GRO = GROUPNAME) WHERE SYSTEMNAME IS NOT NULL ORDER BY SYSTEMNAME;
		ELSE
		    INSERT INTO TMP_TOPO_RECORD (SYSTEMNAME)   SELECT DISTINCT SYSTEMNAME   FROM (SELECT SYSTEMNAME 	FROM IEAI_TOPOINFO UNION SELECT LASTSYSNAME AS SYSTEMNAME 	FROM IEAI_TOPOINFO)  WHERE SYSTEMNAME IS NOT NULL  ORDER BY SYSTEMNAME;
		END IF;
		BEGIN
		FOR V AS MYCURSOR CURSOR FOR SELECT SYSTEMNAME  FROM TMP_TOPO_RECORD
		DO
		SET PRJ_NAME = V.SYSTEMNAME;
		SELECT COUNT(AT.IID)  INTO FSTATE  FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IISFAIL IS NOT NULL  AND AT.IISFAIL <> 0  AND AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		SELECT MIN(AT.IISWARN) INTO NSTATE FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IISWARN IS NOT NULL   AND AT.IISWARN <> 0   AND AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		IF NSTATE IS NULL THEN
		    SET NCOUNT = 0;
		ELSEIF NSTATE = 1 THEN
		    SET NCOUNT = 1;
		ELSEIF NSTATE = 2 THEN
		    SET NCOUNT = 2;
		END IF;
		IF FSTATE > 0 THEN
		    SET NCOUNT = 1;
		END IF;
		SELECT COUNT(AT.IID) INTO ACTCOUNT FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		IF ACTCOUNT > 0 THEN
		 SELECT CASE
                   WHEN COUNT(CASE WHEN AT.IACTSTATE=0 THEN 0 ELSE NULL END ) > 0 THEN
                   -- 运行
                    0
                   WHEN COUNT(CASE WHEN AT.IACTSTATE IS NULL THEN 1 WHEN AT.IACTSTATE=1 THEN 1 ELSE NULL END  ) = COUNT(*) THEN
                   -- 未运行
                    1
                   WHEN COUNT(CASE WHEN AT.IACTSTATE IS NULL THEN 1 WHEN AT.IACTSTATE=1 THEN 1 ELSE NULL END) < COUNT(*) AND
                        COUNT(CASE WHEN AT.IACTSTATE IS NULL THEN 1 WHEN AT.IACTSTATE=1 THEN 1 ELSE NULL END) > 0 AND
                        COUNT(CASE WHEN AT.IACTSTATE=0 THEN 0 ELSE NULL END) = 0 THEN
                   -- 部分完成
                    2
                   ELSE
                   -- 完成
                    3
                 END INTO RSTATE FROM IEAI_ACT_TOPO_INSTANCE AT WHERE AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE GROUP BY AT.IPROJECTNAME;
		ELSE
		    SET RSTATE = 1;
		END IF;
		SELECT COUNT(AT.IID)  INTO FINCOUNT  FROM IEAI_ACT_TOPO_INSTANCE AT WHERE IACTSTATE=2  AND AT.IPROJECTNAME = PRJ_NAME  AND AT.IDATADATE = QUERYDATE;
		INSERT INTO IEAI_TOPO_RECORD (SYSTEMNAME, EXCEPTIONTYPE, ACTTYPE, GROUPNAME,IALL_NUM,IFINISH_NUM) VALUES  (PRJ_NAME, NCOUNT, RSTATE, GROUPNAME,ACTCOUNT,FINCOUNT);
		SELECT (CASE  WHEN COUNT(DECODE(A.IFLOWSTATE, 1, 1, NULL)) = COUNT(*) THEN  0  ELSE 1 END) INTO FINISHSTATE FROM IEAI_ACT_TOPO_INSTANCE A WHERE A.IPROJECTNAME = PRJ_NAME  AND A.IDATADATE = QUERYDATE;
		IF FINISHSTATE = 0 THEN
		    UPDATE IEAI_TOPO_RECORD SET ACTTYPE = 3 WHERE SYSTEMNAME = PRJ_NAME  AND ACTTYPE = 2;
		SELECT COUNT(A.IID) INTO EXCEPAFTERFINISH FROM IEAI_ACT_TOPO_INSTANCE A WHERE A.IFAILSTATE = 1 AND A.IPROJECTNAME = PRJ_NAME  AND A.IDATADATE = QUERYDATE;
		    IF EXCEPAFTERFINISH > 0 THEN
		        UPDATE IEAI_TOPO_RECORD SET ACTTYPE = 4 WHERE SYSTEMNAME = PRJ_NAME;
		    END IF;
		END IF;
		END FOR;
		SELECT COUNT(1) INTO ALLFINCOUNT FROM IEAI_TOPO_RECORD AT WHERE ACTTYPE NOT IN (0,1,2);
    SELECT COUNT(1) INTO ALLCOUNT FROM IEAI_TOPO_RECORD AT;
		UPDATE IEAI_TOPO_RECORD SET ITOTAL=ALLCOUNT,IFINSH_TOTAL=ALLFINCOUNT;
 	  COMMIT WORK;
		END;
END
$
-- 解决暂停自动结束 修改存储过程
CREATE OR REPLACE PROCEDURE PROC_UPDATE_RUN_INSSTATE   (IN SERVERIP VARCHAR(255),OUT AN_OPT_ID NUMERIC(19,0))
LANGUAGE SQL
BEGIN
UPDATE IEAI_ITSM_AUTO_CHILD  SET ISTATE=1,IENDTIME=FUN_GET_DATE_NUMBER_NEW(CURRENT timestamp, 8) WHERE IINSTANCEID  IN (SELECT  IA.IINSTANCEID   FROM IEAI_ITSM_AUTO_CHILD IA,IEAI_RUN_INSTANCE RI,(SELECT  A.IID   FROM IEAI_RUN_INSTANCE A,  IEAI_RUNINFO_INSTANCE B  WHERE  A.IID=B.IRUNINSID   AND   A.ISTATE=0    AND    A.ISERVERIP=''||SERVERIP||''  GROUP BY    A.IID HAVING   COUNT(DECODE(B.ISTATE,0,1,1,1,NULL))=0) C WHERE RI.IID=C.IID AND IA.IINSTANCEID=RI.ISYSID  AND  ISERVERIP=''||SERVERIP||'');
UPDATE  IEAI_WORKFLOWINSTANCE  SET ISTATUS=4  ,IENDTIME=FUN_GET_DATE_NUMBER_NEW(CURRENT timestamp, 8) WHERE ISTATUS=0  AND  IFLOWID IN ( SELECT IFLOWID  FROM IEAI_RUNINFO_INSTANCE   WHERE IRUNINSID IN ( SELECT A.IID  FROM IEAI_RUN_INSTANCE A   , IEAI_RUNINFO_INSTANCE B   WHERE A.IID=B.IRUNINSID AND  A.ISTATE=0   AND  A.ISERVERIP=''||SERVERIP||''  GROUP BY  A.IID  HAVING  COUNT(DECODE(B.ISTATE,0,1,1,1,NULL))=0) );
UPDATE  IEAI_RUNINFO_INSTANCE  SET ISTATE=2 ,  IENDTIME=FUN_GET_DATE_NUMBER_NEW(CURRENT timestamp, 8)  WHERE ICHILDINSTANCEID IN (SELECT B.IRUNINSID  FROM IEAI_RUNINFO_INSTANCE B ,(SELECT  RI.ICHILDINSTANCEID from IEAI_RUNINFO_INSTANCE RI,IEAI_RUN_INSTANCE R  WHERE RI.IRUNINSID=R.IID AND   R.ISTATE=0  AND  RI.ICHILDINSTANCEID<>-1  AND R.ISERVERIP=''||SERVERIP||''  GROUP BY    RI.ICHILDINSTANCEID) A  WHERE A.ICHILDINSTANCEID=B.IRUNINSID GROUP BY B.IRUNINSID  HAVING   COUNT(DECODE(B.ISTATE,0,1,1,1,NULL))=0)  AND ISTATE<>2;
UPDATE  IEAI_RUN_INSTANCE   SET IENDTIME=FUN_GET_DATE_NUMBER_NEW(current timestamp,8) ,ISTATE=1   WHERE  IID IN (SELECT  A.IID FROM IEAI_RUN_INSTANCE A,  IEAI_RUNINFO_INSTANCE B WHERE  A.IID=B.IRUNINSID   AND   A.ISTATE=0    AND    A.ISERVERIP=''||SERVERIP||'' GROUP BY    A.IID HAVING   COUNT(DECODE(B.ISTATE,0,1,1,1,4,1,NULL))=0);
END
$
-- 顺德大屏总耗时
BEGIN
 DECLARE LS_SQL VARCHAR(4000);
 DECLARE LI_EXISTS NUMERIC(5);
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROPERTYCONFIG WHERE  IID=1004;
	IF LI_EXISTS = 0 THEN
	    SET LS_SQL ='INSERT INTO IEAI_PROPERTYCONFIG(IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) VALUES(1004,''setSDTime'',''2021-1-1 00:00:00'',''时间配置格式(年-月-日 时:分:秒)'')';
	    PREPARE	SQLA FROM LS_SQL;
	    EXECUTE SQLA;
	END IF;	
END 
$