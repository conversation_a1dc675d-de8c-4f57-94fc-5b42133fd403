package com.ideal.ieai.prv.engine.common;

public class Convert {
    public static String getChineseByNumber(Integer arabNum) {
        if (arabNum == null) {
            return "";
        }
        if (arabNum == 0) {
            return "零";
        }

        String[] unitArray= {"","十","百","千","万","十万","百万","千万","亿","十亿","百亿","千亿","万亿"};
        String[] numArray = {"零","一","二","三","四","五","六","七","八","九"};

        char[] charArray= String.valueOf(arabNum).toCharArray();
        int arrayLength = charArray.length;
        StringBuilder stringBuilder = new StringBuilder();

        for(int i = 0;i<arrayLength;i++	){
            int num = Integer.valueOf(charArray[i] + "");
            boolean isZero = num == 0;
            String unit = unitArray[(arrayLength - 1) - i];
            if (isZero) {
                continue;
            }else {
                if(i==0){
                    stringBuilder.append(numArray[num]);
                    stringBuilder.append(unit);
                }else{
                    if(charArray[i-1]=='0'){
                        stringBuilder.append("零");
                        stringBuilder.append(numArray[num]);
                        stringBuilder.append(unit);
                    }else{
                        stringBuilder.append(numArray[num]);
                        stringBuilder.append(unit);
                    }
                }
            }
        }
        String numString=stringBuilder.toString();
        if(numString.length()>1){
            if(numString.toCharArray()[0]=='一' && numString.toCharArray()[1]=='十'){
                return numString.substring(1);
            }
        }
        return numString;
    }
}
