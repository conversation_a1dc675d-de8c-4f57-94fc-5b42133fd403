package com.ideal.ieai.commons;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * A PermissionSet object represents a set of permissions assigned to a project
 * or adaptor, permissions are represented with String objects.
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: Ideal Technologies
 * </p>
 * 
 * <AUTHOR> Fan edit by xy ming
 * @version 3.0
 */

public class PermissionSet implements Serializable
{

    /**
     * get all adaptor names that can be operated
     * 
     * @param allAdaptorNames
     *            names of all the projects a set of String instances
     * @return set of all String type.elements is an adaptor name
     */
    public Set getRelatedAdaptorNames ( Collection allAdaptorNames )
    {
        Set result = new HashSet();
        // first check permission that can operate all adaptors
        for (Iterator iter = _allAdpPerms.iterator(); iter.hasNext();)
        {
            String perm = (String) iter.next();
            if (!_fbdAllAdpPerms.contains(perm))
            {
                // only add adaptors that doesn't forbidden
                for (Iterator iterator = allAdaptorNames.iterator(); iterator
                        .hasNext();)
                {
                    String adaptorName = (String) iterator.next();
                    List adapForbiddenPerms = (List) _fbdAdpsPerms
                            .get(adaptorName);
                    if (adapForbiddenPerms == null
                            || !adapForbiddenPerms.contains(adaptorName))
                    {
                        result.add(adaptorName);
                    }
                }
            }
        }
        // check in every adaptor permissions
        for (Iterator iter = _adpsPerms.keySet().iterator(); iter.hasNext();)
        {
            String adaptorName = (String) iter.next();
            if (!allAdaptorNames.contains(adaptorName))
            {
                continue;
            }
            List adaptorPerms = (List) _adpsPerms.get(adaptorName);
            if (adaptorPerms == null || adaptorPerms.size() == 0)
            {
                continue;
            }

            if (!_fbdAdpsPerms.containsKey(adaptorName))
            {
                result.add(adaptorName);
                continue;
            }

            // if not all of the permissions assigned to the adaptor are
            // forbidden,then add it to result
            List forbiddenPerms = (List) _fbdAdpsPerms.get(adaptorName);
            if (forbiddenPerms == null
                    || !forbiddenPerms.containsAll(adaptorPerms))
            {
                result.add(adaptorName);
            }
        }
        return result;
    }

    /**
     * get all project names that can be operated
     * 
     * @param allProjectsNames
     *            names of all the projects ,a set of String instances
     * @return set of String type. elements is an project name
     */
    public Set getRelatedProjectNames ( Collection allProjectNames )
    {
        Set result = new HashSet();

        // first check permissions that can operate all Projects

        for (Iterator iter = _allPrjPerms.iterator(); iter.hasNext();)
        {
            String perm = (String) iter.next();
            if (!_fbdAllPrjPerms.contains(perm))
            {
                // only add projects that doesn't forbidden
                for (Iterator iterator = allProjectNames.iterator(); iterator
                        .hasNext();)
                {
                    String projectName = (String) iterator.next();
                    List prjForbiddenPerms = (List) _fbdPrjsPerms
                            .get(projectName);
                    if (prjForbiddenPerms == null
                            || !prjForbiddenPerms.contains(perm))
                    {
                        result.add(projectName);
                    }
                }
            }
        }

        // check in every project permissions
        for (Iterator iter = _prjsPerms.keySet().iterator(); iter.hasNext();)
        {
            String projectName = (String) iter.next();
            if (!allProjectNames.contains(projectName))
            {
                continue;
            }
            List projectPerms = (List) _prjsPerms.get(projectName);
            if (projectPerms == null || projectPerms.size() == 0)
            {
                continue;
            }

            if (!_fbdPrjsPerms.containsKey(projectName))
            {
                result.add(projectName);
                continue;
            }

            // if not all of the permissions assigned to the project are
            // forbidden,then add it to result
            List forbiddenPerms = (List) _fbdPrjsPerms.get(projectName);
            if (forbiddenPerms == null
                    || !forbiddenPerms.containsAll(projectPerms))
            {
                result.add(projectName);
            }
        }

        return result;
    }

    /**
     * Instantiate an empty PermissionSet.
     */
    public PermissionSet()
    {
    }

    /**
     * Return all project names associated with this PermissionSet
     * 
     * @return a Set of String objects
     */
    public Set getProjectNames ( boolean isEnable )
    {
        return isEnable ? _prjsPerms.keySet() : _fbdPrjsPerms.keySet();
    }

    /**
     * Return all adaptor names associated with this PermissionSet
     * 
     * @return a Set of String objects
     */
    public Set getAdaptorNames ( boolean isEnable )
    {
        return isEnable ? _adpsPerms.keySet() : _fbdAdpsPerms.keySet();
    }

    /**
     * Assigns permission to the specified project.
     * 
     * @param prjName
     *            String
     * @param permId
     *            String
     */
    public void assignsPermToPrj ( String prjName, String permId )
    {
        List prjPerms = (List) _prjsPerms.get(prjName);
        if (null == prjPerms)
        {
            prjPerms = new ArrayList();
            _prjsPerms.put(prjName, prjPerms);
        }
        prjPerms.add(permId);
    }

    /**
     * Assigns permission to the specified adaptor.
     * 
     * @param adpName
     *            String
     * @param permId
     *            String
     */
    public void assignsPermToAdp ( String adpName, String permId )
    {
        List adpPerms = (List) _adpsPerms.get(adpName);
        if (null == adpPerms)
        {
            adpPerms = new ArrayList();
            _adpsPerms.put(adpName, adpPerms);
        }
        adpPerms.add(permId);
    }

    /**
     * Assigns permssion to project which represents all of project.
     * 
     * @param permId
     *            String
     */
    public void assignsPermToAllPrj ( String permId )
    {
        _allPrjPerms.add(permId);
    }

    /**
     * Assigns permssion to adaptor which represents all of adaptor.
     * 
     * @param permId
     *            String
     */
    public void assignsPermToAllAdp ( String permId )
    {
        _allAdpPerms.add(permId);
    }

    /**
     * Assigns permssion of message
     * 
     * @param permId
     */
    public void assignsPermToMessage ( String permId )
    {
        _messagePerms.add(permId);
    }

	// update start by yan_wang
	/**
	 * Assigns permssion of calendar
	 * 
	 * @param permId
	 */
	public void assignsPermToCalendar(String permId) {
		_calendarPerms.add(permId);
	}

	// update end by yan_wang
    
    /**
     * Assigns permssion of notice
     * 
     * @param permId
     */
    public void assignsPermToNotice ( String permId )
    {
        _noticePerms.add(permId);
    }
    
    /**
     * Assigns permssion of online
     * 
     * @param permId
     */
    public void assignsPermToOnline(String permId)
    {
        _onlinePerms.add(permId);
    }

    public void assignsPermToHC(String permId)
    {
    	_HCPerms.add(permId);
    }
    /**
     * Forbids permission to the specified project.
     * 
     * @param prjName
     *            String
     * @param permId
     *            String
     */
    public void forbidsPermToPrj ( String prjName, String permId )
    {
        List fbdPrjPerms = (List) _fbdPrjsPerms.get(prjName);
        if (null == fbdPrjPerms)
        {
            fbdPrjPerms = new ArrayList();
            _fbdPrjsPerms.put(prjName, fbdPrjPerms);
        }
        fbdPrjPerms.add(permId);
    }

    /**
     * forbids permission to the specified adaptor.
     * 
     * @param adpName
     *            String
     * @param permId
     *            String
     */
    public void forbidsPermToAdp ( String adpName, String permId )
    {
        List fbdAdpPerms = (List) _fbdAdpsPerms.get(adpName);
        if (null == fbdAdpPerms)
        {
            fbdAdpPerms = new ArrayList();
            _fbdAdpsPerms.put(adpName, fbdAdpPerms);
        }
        fbdAdpPerms.add(permId);
    }

    /**
     * forbids permssion to project which represents all of project.
     * 
     * @param permId
     *            String
     */
    public void forbidsPermToAllPrj ( String permId )
    {
        _fbdAllPrjPerms.add(permId);
    }

    /**
     * forbids permssion to adaptor which represents all of adaptor.
     * 
     * @param permId
     *            String
     */
    public void forbidsPermToAllAdp ( String permId )
    {
        _fbdAllAdpPerms.add(permId);
    }

    /**
     * fobids permission to message
     * 
     * @param permId
     */
    public void forbidsPermToMessage ( String permId )
    {
        _fbdMessagePerms.add(permId);
    }

	// update start by yan_wang
	/**
	 * fobids permission to calendar
	 * 
	 * @param permId
	 */
	public void forbidsPermToCalendar(String permId) {
		_fbdCalendarPerms.add(permId);
	}

	// update end by yan_wang
    
    /**
     * forbids permission to notice
     * 
     * @param permId
     */
    public void forbidsPermToNotice ( String permId )
    {
        _fbdNoticePerms.add(permId);
    }
    
    
    
    /**
     * forbids permission to online
     * 
     * @param permId
     */
    public void forbidsPermToOnline ( String permId )
    {
        _fbdOnlinePerms.add(permId);
    }

    public void forbidsPermToHC ( String permId )
    {
    	_fbdHCPerms.add(permId);
    }
    
    public List getPrjPerms ( String prjName )
    {
        Object prjPerms = _prjsPerms.get(prjName);
        return (null == prjPerms) ? new ArrayList() : (List) prjPerms;
    }
    
    public void removePrjPerms ( String prjName )
    {
    	_prjsPerms.keySet().remove(prjName);
    }

    public List getAdpPerms ( String adpName )
    {
        Object adpPerms = _adpsPerms.get(adpName);
        return (null == adpPerms) ? new ArrayList() : (List) adpPerms;
    }

    public List getAllPrjPerms ()
    {
        return _allPrjPerms;
    }

    public List getAllAdpPerms ()
    {
        return _allAdpPerms;
    }

    public List getMessagePerms ()
    {
        return _messagePerms;
    }

	// update start by yan_wang
	public List getCalendarPerms() {
		return _calendarPerms;
	}

	// update end by yan_wang
    
    public List getNoticePerms ()
    {
        return _noticePerms;
    }
    
    public List getOnlinePerms()
    {
        return _onlinePerms;
    }

    public List getHCPerms()
    {
    	return _HCPerms;
    }
    
    public List getForbiddenPrjPerms ( String prjName )
    {
        List fbdPrjPerms = (List) _fbdPrjsPerms.get(prjName);
        return (null == fbdPrjPerms) ? new ArrayList() : fbdPrjPerms;
    }

    public List getForbiddenAdpPerms ( String adpName )
    {
        List fbdAdpPerms = (List) _fbdAdpsPerms.get(adpName);
        return (null == fbdAdpPerms) ? new ArrayList() : fbdAdpPerms;
    }

    public List getForbiddenAllPrjPerms ()
    {
        return _fbdAllPrjPerms;
    }

    public List getForbiddenAllAdpPerms ()
    {
        return _fbdAllAdpPerms;
    }

    public List getForbiddenMessagePerms ()
    {
        return _fbdMessagePerms;
    }

	// update start by yan_wang
	public List getForbiddenCalendarPerms() {
		return _fbdCalendarPerms;
	}

	// update end by yan_wang
    
    public List getForbiddenNoticePerms ()
    {
        return _fbdNoticePerms;
    }
    
    public List getForbiddenOnlinePerms()
    {
        return _fbdOnlinePerms; 
    }

    public List getForbiddenHCPerms()
    {
    	return _fbdHCPerms; 
    }
    /**
     * Merges the specified Permission object into the current permission set.
     * 
     * @param permSet
     *            PermissionSet the permission set which will be merged.
     */
    public void merge ( PermissionSet permSet )
    {
        if (null == permSet)
        {
            return;
        }

        Set keys = null;
        String name = null;
        List ids = null;

        // _prjsPerms
        keys = permSet._prjsPerms.keySet();
        for (Iterator iter = keys.iterator(); iter.hasNext();)
        {
            name = (String) iter.next();
            ids = (List) permSet._prjsPerms.get(name);
            List prjPerms = (List) this._prjsPerms.get(name);
            if (null == prjPerms)
            {
                this._prjsPerms.put(name, ids);
            }
            else
            {
                prjPerms.addAll(ids);
            }
        }

        // _adpsPerms
        keys = permSet._adpsPerms.keySet();
        for (Iterator iter = keys.iterator(); iter.hasNext();)
        {
            name = (String) iter.next();
            ids = (List) permSet._adpsPerms.get(name);
            List adpPerms = (List) this._adpsPerms.get(name);
            if (null == adpPerms)
            {
                this._adpsPerms.put(name, ids);
            }
            else
            {
                adpPerms.addAll(ids);
            }
        }

        // _allPrjPerms
        this._allPrjPerms.addAll(permSet._allPrjPerms);

        // _allAdpPerms
        this._allAdpPerms.addAll(permSet._allAdpPerms);

        // _messagePerms
        this._messagePerms.addAll(permSet._messagePerms);

        this._noticePerms.addAll(permSet._noticePerms);
        
        this._onlinePerms.addAll(permSet._onlinePerms);
        
		// update start by yan_wang
		this._calendarPerms.addAll(permSet._calendarPerms);
		// update end by yan_wang
        
        this._HCPerms.addAll(permSet._HCPerms);

        // _fbdPrjsPerms
        keys = permSet._fbdPrjsPerms.keySet();
        for (Iterator iter = keys.iterator(); iter.hasNext();)
        {
            name = (String) iter.next();
            ids = (List) permSet._fbdPrjsPerms.get(name);
            List fbdPrjPerms = (List) this._fbdPrjsPerms.get(name);
            if (null == fbdPrjPerms)
            {
                this._fbdPrjsPerms.put(name, ids);
            }
            else
            {
                fbdPrjPerms.addAll(ids);
            }
        }

        // _fbdAdpsPerms
        keys = permSet._fbdAdpsPerms.keySet();
        for (Iterator iter = keys.iterator(); iter.hasNext();)
        {
            name = (String) iter.next();
            ids = (List) permSet._fbdAdpsPerms.get(name);
            List fbdAdpPerms = (List) this._fbdAdpsPerms.get(name);
            if (null == fbdAdpPerms)
            {
                this._fbdAdpsPerms.put(name, ids);
            }
            else
            {
                fbdAdpPerms.addAll(ids);
            }
        }

        // _fbdAllPrjPerms
        this._fbdAllPrjPerms.addAll(permSet._fbdAllPrjPerms);
        // _fbdAllAdpPerms
        this._fbdAllAdpPerms.addAll(permSet._fbdAllAdpPerms);
        // _fbmessagePerms
        this._fbdMessagePerms.addAll(permSet._fbdMessagePerms);
        //_fbdNoticePerms
        this._fbdNoticePerms.addAll(permSet._fbdNoticePerms);
        //_fbdOnlinePerms
        this._fbdOnlinePerms.addAll(permSet._fbdOnlinePerms);
        
		// update start by yan_wang
		this._fbdCalendarPerms.addAll(permSet._fbdCalendarPerms);
		// update end by yan_wang

        this._fbdHCPerms.addAll(permSet._fbdHCPerms);
    }

    public boolean allProjectForbid ( String permId )
    {
        if (_fbdAllPrjPerms.contains(permId))
        {
            return true;
        }
        return false;
    }

    /**
     * Check status of a given permission.
     * 
     * @param type
     *            int
     * @param prjAdpName
     *            String
     * @param permId
     *            String
     * @return int PERM_ENABLED, PERM_DISABLED, PERM_UNDEFINED
     */
    public int checkPermission ( int type, String prjAdpName, String permId )
    {
        if (Constants.PRJ == type)
        {
            // check disabled permission of the specific project
            List prjDisabledPerms = (List) _fbdPrjsPerms.get(prjAdpName);
            if (null != prjDisabledPerms && prjDisabledPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            // check disabled all permisson
            if (_fbdAllPrjPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            // check all allowed permission
            if (_allPrjPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }

            List prjPerms = (List) _prjsPerms.get(prjAdpName);
            if (null != prjPerms && prjPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
            return Constants.PERM_UNDEFINED;
        }
        else if (Constants.ADP == type)
        {
            // check disabled permission of the specific adp
            List adpDisabledPerms = (List) _fbdAdpsPerms.get(prjAdpName);
            if (null != adpDisabledPerms && adpDisabledPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            // check disabled all permisson
            if (_fbdAllAdpPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            // check all allowed permission
            if (_allAdpPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }

            List adpPerms = (List) _prjsPerms.get(prjAdpName);
            if (null != adpPerms && adpPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
            return Constants.PERM_UNDEFINED;
        }
        else if (Constants.ALL_PRJ == type)
        {
            if (_fbdAllPrjPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            if (_allPrjPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
            return Constants.PERM_UNDEFINED;
        }
        else if (Constants.ALL_ADP == type)
        {
            if (_fbdAllAdpPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            if (_allAdpPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
            return Constants.PERM_UNDEFINED;

        }
        else if (Constants.MESSAGE == type)
        {
            if (_fbdMessagePerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            if (_messagePerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
            return Constants.PERM_UNDEFINED;
        }
        else if (Constants.NOTICE == type)
        {
            if (_fbdNoticePerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            if (_noticePerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
        }
        else if(Constants.ONLINE==type)
        {
            if (_fbdOnlinePerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            if (_onlinePerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
        }
        
		// update start by yan_wang
		else if (Constants.CALENDAR == type) {
			if (_fbdCalendarPerms.contains(permId)) {
				return Constants.PERM_DISABLED;
			}
			if (_calendarPerms.contains(permId)) {
				return Constants.PERM_ENABLED;
			}
		}
		// update end by yan_wang
        
        else if(Constants.HC==type)
        {
            if (_fbdHCPerms.contains(permId))
            {
                return Constants.PERM_DISABLED;
            }
            if (_HCPerms.contains(permId))
            {
                return Constants.PERM_ENABLED;
            }
        }
        
        return Constants.PERM_UNDEFINED;
    }

    /**
     * @param prjName
     *            String
     * @param permId
     *            String
     * @return boolean
     */
    public boolean hasPermToPrj ( String prjName, String permId )
    {
        List prjPerms = (List) _prjsPerms.get(prjName);
        if (null == prjPerms)
        {
            return false;
        }
        return prjPerms.contains(permId);
    }

    public boolean hasPermToAdp ( String adpName, String permId )
    {
        List adpPerms = (List) _adpsPerms.get(adpName);
        if (null == adpPerms)
        {
            return false;
        }
        return adpPerms.contains(permId);
    }

    /**
     * check the project this perm can not be used
     * 
     * @param permId
     * @return
     */
    public Collection projectCanNotCharge ( String permId )
    {

        Collection result = new HashSet();
        if (_fbdAllPrjPerms.contains(permId))
        {
            result.addAll(_prjsPerms.keySet());
            return result;
        }
        for (Iterator iter = _fbdPrjsPerms.entrySet().iterator(); iter
                .hasNext();)
        {
            Map.Entry entry = (Map.Entry) iter.next();
            String prjName = (String) entry.getKey();
            if (((List) entry.getValue()).contains(permId))
            {
                result.add(prjName);
            }
        }
        return result;
    }

    public boolean isTaskAdmin ()
    {
        String permId = Constants.PERM_TASK_ADMIN;
        if (_fbdAllPrjPerms.contains(permId))
        {
            return false;
        }
        if (_allPrjPerms.contains(permId))
        {
            return true;
        }
        for (Iterator iter = _prjsPerms.entrySet().iterator(); iter.hasNext();)
        {
            Map.Entry entry = (Map.Entry) iter.next();
            String prjName = (String) entry.getKey();
            List value = (List) entry.getValue();
            if (value.contains(permId))
            {
                List fbdPerms = (List) _fbdPrjsPerms.get(prjName);
                if (fbdPerms == null || !fbdPerms.contains(permId))
                {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean hasPermToAllPrj ( String permId )
    {
        return _allPrjPerms.contains(permId);
    }

    public boolean hasPermToAllAdp ( String permId )
    {
        return _allAdpPerms.contains(permId);
    }

    public boolean hasPermToMessage ( String permId )
    {
        return _messagePerms.contains(permId);
    }

	// update start by yan_wang
	public boolean hasPermToCalendar(String permId) {
		return _calendarPerms.contains(permId);
	}

	// update end by yan_wang
    
    public boolean hasPermToNotice ( String permId )
    {
        return _noticePerms.contains(permId);
    }
    
    
    public boolean hasPermToOnline(String permId )
    {
        return _onlinePerms.contains(permId);
    }
    
    public boolean hasPermToHC(String permId )
    {
    	return _HCPerms.contains(permId);
    }

    public boolean isPermFbddenToPrj ( String prjName, String permId )
    {
        List fbdPrjPerms = (List) _fbdPrjsPerms.get(prjName);
        if (null == fbdPrjPerms)
        {
            return false;
        }
        return fbdPrjPerms.contains(permId);
    }

    public boolean isPermFbddenToAdp ( String adpName, String permId )
    {
        List fbdAdpPerms = (List) _fbdAdpsPerms.get(adpName);
        if (null == fbdAdpPerms)
        {
            return false;
        }
        return fbdAdpPerms.contains(permId);
    }

    public boolean isPermFbddenToAllPrj ( String permId )
    {
        return _fbdAllPrjPerms.contains(permId);
    }

    public boolean isPermFbddenToAllAdp ( String permId )
    {
        return _fbdAllAdpPerms.contains(permId);
    }

    public boolean isPermFbddenToMessage ( String permId )
    {
        return _fbdMessagePerms.contains(permId);
    }

    public boolean isPermFbdenToNotice ( String permId )
    {
        return _fbdNoticePerms.contains(permId);
    }
    
	// update start by yan_wang
	public boolean isPermFbddenToCalendar(String permId) {
		return _fbdCalendarPerms.contains(permId);
	}

	// update end by yan_wang
    
    /**
     * is online permmission is fordbidden
     * @param permId
     * @return
     */
    public boolean isPermFbddenToOnline(String permId)
    {
        return _fbdOnlinePerms.contains(permId);
    }
    
    public boolean isPermFbddenToHC(String permId)
    {
    	return _fbdHCPerms.contains(permId);
    }

    /** override toStrng method */
    public String toString ()
    {
        /** output all permissions */
        StringBuffer buffer = new StringBuffer();
        buffer.append("enabled perms:\n");
        buffer.append(mapToString(_prjsPerms, true));
        buffer.append(mapToString(_adpsPerms, false));
        buffer.append(listToString(_allPrjPerms, true, false));
        buffer.append(listToString(_allAdpPerms, false, false));
        buffer.append(listToString(_messagePerms, false, true));
        buffer.append(listToString(_noticePerms, false, true));
        buffer.append(listToString(_onlinePerms, false, true));
		// update start by yan_wang
		buffer.append(listToString(_calendarPerms, false, true));
		// update end by yan_wang
        buffer.append(listToString(_HCPerms, false, true));
        
        buffer.append("disabled:\n");
        buffer.append(mapToString(_fbdPrjsPerms, true));
        buffer.append(mapToString(_fbdAdpsPerms, false));
        buffer.append(listToString(_fbdAllPrjPerms, true, false));
        buffer.append(listToString(_fbdAllAdpPerms, false, false));
        buffer.append(listToString(_fbdMessagePerms, false, true));
        buffer.append(listToString(_fbdNoticePerms, false, true));
        buffer.append(listToString(_fbdOnlinePerms,false,true));
		// update start by yan_wang
		buffer.append(listToString(_fbdCalendarPerms, false, true));
		// update end by yan_wang
        buffer.append(listToString(_fbdHCPerms,false,true));
        return buffer.toString();
    }

    /**
     * change a prj or adp's permission to string
     * 
     * @param map
     * @param isPrj
     * @return
     */
    private String mapToString ( Map map, boolean isPrj )
    {
        StringBuffer buffer = new StringBuffer();
        if (null == map)
        {
            return "";
        }
        String title = null;
        if (isPrj)
        {
            buffer.append("single project permission :\n");
            title = "project: ";
        }
        else
        {
            buffer.append("single adaptor's permission :\n");
            title = "adaptor: ";
        }

        for (Iterator iter = map.keySet().iterator(); iter.hasNext();)
        {
            Object key = iter.next();
            buffer.append(title).append(key);
            for (Iterator inner = ((List) (map.get(key))).iterator(); inner
                    .hasNext();)
            {
                buffer.append(inner.next()).append(":");
            }
            buffer.append("\n");
        }
        return buffer.toString();
    }

    /**
     * change all project or all adaptor's perms to string
     * 
     * @param list
     * @param isPrj
     * @return
     */
    public String listToString ( List list, boolean isPrj, boolean isMessage )
    {
        if (null == list)
        {
            return "";
        }
        StringBuffer buffer = new StringBuffer();
        String title = null;
        if (isMessage)
        {
            title = "message";
        }
        else
        {
            title = isPrj ? "project" : "adaptor";
        }

        buffer.append("all ").append(title).append("permissions : \n");
        for (Iterator iter = list.iterator(); iter.hasNext();)
        {
            buffer.append(iter.next()).append(":");
        }
        buffer.append("\n");
        return buffer.toString();
    }

    /**
     * 
     *
     */
    public void checkPrjPermission() {
        List temp = new ArrayList();
        for(Iterator iter = getProjectNames(true).iterator(); iter.hasNext();){
            String prjName = (String) iter.next();
            temp.add(prjName);
        
        }
        
        for(Iterator iterTemp = temp.iterator();iterTemp.hasNext();){
            String prjName = (String) iterTemp.next();
            List prjPerms = getPrjPerms(prjName);
            boolean checkedTrue = false;
            boolean checkedAdd = false;
            for (Iterator iterPrj = prjPerms.iterator(); iterPrj.hasNext();) {
                String name = (String) iterPrj.next();
                if (name.endsWith(Constants.PERM_FLOW_STOP)
                        || name.endsWith(Constants.PERM_FLOW_PAUSE)
                        || name.endsWith(Constants.PERM_FLOW_KILL)
                        || name.endsWith(Constants.PERM_RECOVER))
                    checkedTrue = true;
                else if (name.endsWith(Constants.PERM_FLOW_GETINFO))
                    checkedAdd = true;
            }
            if (checkedTrue == true && checkedAdd == false) {
                prjPerms.add(Constants.PERM_FLOW_GETINFO);
                _prjsPerms.keySet().remove(prjName);
                _prjsPerms.put(prjName, prjPerms);
            
            }
        }
        

    }

    /**
     */
    public void checkAllPrjPermission() {
        boolean checkedTrue = false;
        boolean checkedAdd = false;
        for (Iterator iter = getAllPrjPerms().iterator(); iter.hasNext();) {
            String perName = (String) iter.next();
            if (perName.endsWith(Constants.PERM_FLOW_STOP)
                    || perName.endsWith(Constants.PERM_FLOW_PAUSE)
                    || perName.endsWith(Constants.PERM_FLOW_KILL)
                    || perName.endsWith(Constants.PERM_RECOVER))
                checkedTrue = true;
            else if (perName.endsWith(Constants.PERM_FLOW_GETINFO))
                checkedAdd = true;

        }

        if (checkedTrue == true && checkedAdd == false ) {
            getAllPrjPerms().add(Constants.PERM_FLOW_GETINFO);
        }

    }
    
    
    /**
     * Key: Project Name Value: list of permissions which have been assigned to
     * the project specified by the project name. Each element of List is a
     * permission id.
     */
    public Map  _prjsPerms       = new HashMap();

    /**
     * Key: Adaptor Name Value: list of permissions which have been assigned to
     * the adaptor specified by the adaptor name. Each element of List is a
     * permission id.
     */
    public Map  _adpsPerms       = new HashMap();

    /**
     * permission ids which have been assigned to All Projects.
     */
    public List _allPrjPerms     = new ArrayList();

    /**
     * permission ids which have been assigned to All Adaptors.
     */
    public List _allAdpPerms     = new ArrayList();

    /**
     * permission ids relate message.
     */
    public List _messagePerms    = new ArrayList();

	// update start by yan_wang
	public List _calendarPerms = new ArrayList();
	// update end by yan_wang
    
    public List _noticePerms     = new ArrayList();
    
    
    /**
     * permission ids relate online management.
     */
    public List _onlinePerms=new ArrayList();
    
    public List _HCPerms=new ArrayList();

    /** forbidden permission List */
    public Map  _fbdPrjsPerms    = new HashMap();

    public Map  _fbdAdpsPerms    = new HashMap();

    public List _fbdAllPrjPerms  = new ArrayList();

    public List _fbdAllAdpPerms  = new ArrayList();

    public List _fbdMessagePerms = new ArrayList();

	// update start by yan_wang
	public List _fbdCalendarPerms = new ArrayList();
	// update end by yan_wang
    
    public List _fbdNoticePerms  = new ArrayList();
    
    public List _fbdOnlinePerms=new ArrayList();

    public List _fbdHCPerms=new ArrayList();
}
