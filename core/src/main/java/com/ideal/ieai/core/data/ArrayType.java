package com.ideal.ieai.core.data;

import com.ideal.ieai.core.basis.InvalidNameException;
import com.ideal.ieai.core.basis.InvalidTypeException;
import com.ideal.ieai.core.element.IEAISchema;
import com.ideal.ieai.core.element.IEAISchemaDef;
import com.ideal.ieai.core.element.IEAISchemaRef;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 * <p>
 * Title: iEAI Core3.0
 * </p>
 * <p>
 * Description: At present, the element of iEAI array type can't be a iEAI array
 * type.<br>
 * <strong>Note:</strong>The schema array, whose base item type is an
 * IEAISchemaRef, not a real schema.
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: Ideal Info
 * </p>
 *
 * <AUTHOR>
 * @version 3.0
 */

public class ArrayType implements IDataArrayType
{
    public final static int  DEFAULT_DIMENSION = 1;

    /**
     * Type of array base item <br>
     * type[][]'s baseItemType is type
     */
    private IDataType        _baseItemType;
    private String           _typeName         = "";
    private int              _dimension;
    private static List      _allArrayType     = new ArrayList();

    /**
     * Private constructor.
     */

    private ArrayType(IDataType BaseType, int Dimension)
            throws InvalidTypeException
    {
        if (Dimension < 1)
        {
            throw new InvalidTypeException();
        }
        _dimension = Dimension;
        _baseItemType = BaseType;
        _typeName = _baseItemType.getTypeName();
        for (int i = 0; i < _dimension; i++)
        {
            _typeName += "[" + "]";
        }
    }

    /**
     * Get the unique instance of array type.
     *
     * @return
     */
    public static synchronized ArrayType getInstance ( IDataType BaseType,
            int Dimension ) throws InvalidTypeException
    {
        if (null == BaseType || BaseType instanceof ArrayType
                || BaseType instanceof VoidType)
        {
            throw new InvalidTypeException();
        }
        // DataTypeNames.TYPE_NAME_SCHEMA is the key word
        if (BaseType instanceof IEAISchema
                && !"".equals(BaseType.getTypeName())
                && !DataTypeNames.TYPE_NAME_SCHEMA.equals(BaseType
                        .getTypeName()))
        { // Schema array BaseType is IEAISchemaRef.
            try
            {
                BaseType = new IEAISchemaRef(BaseType.getTypeName());
            } catch (InvalidNameException ex)
            {
                throw new InvalidTypeException();
            }
        }

        ArrayType type;
        for (int i = 0, size = _allArrayType.size(); i < size; i++)
        {
            type = (ArrayType) _allArrayType.get(i);
            if (type.getBaseItemType() instanceof IEAISchemaRef
                    && BaseType instanceof IEAISchemaRef)
            {
                String typeName = type.getBaseItemType().getTypeName();
                StringTokenizer strToken = new StringTokenizer(typeName,
                        IEAISchemaDef.NAME_SPACE_SEPARATOR);
                if (2 != strToken.countTokens())
                { // A full name is formed by two parts. for example:
                    // Test.test.
                    throw new InvalidTypeException();
                }
                if (typeName.equals(BaseType.getTypeName())
                        && type.getDimension() == Dimension)
                {
                    return type;
                }
            } else if (type.getBaseItemType() instanceof IEAISchema
                    && BaseType instanceof IEAISchema)
            {
               type.getBaseItemType();

            } else
            {
                if (type.getBaseItemType() == BaseType
                        && type.getDimension() == Dimension)
                {
                    return type;
                }
            }
        }
        type = new ArrayType(BaseType, Dimension);
        _allArrayType.add(type);
        return type;
    }

    /**
     * Check if the specified object is a valid instance of current data type
     *
     * @param TheObj
     * @return
     */
    public boolean verify ( Object TheObj )
    {
        if (null == TheObj)
        {
            return true;
        }
        if (TheObj instanceof ArrayObject)
        {
            return true;
        }
        return false;
    }

    /**
     * Get the type name of data type, for a array its name may be like this:<br>
     * Integer[][]
     *
     * @return
     */
    public String getTypeName ()
    {
        return _typeName;
    }

    /**
     * Gets the dimension.
     *
     * @return
     */
    public int getDimension ()
    {
        return _dimension;
    }

    /**
     * Gets the basic type of an array.For example:<br>
     * Integer. The basic type is Integer.
     *
     * @return
     */
    public IDataType getBaseItemType ()
    {
        return _baseItemType;
    }

    /**
     * The type name format:<iEAI type name>[][][]...<br>
     * for example: "Integer[][][]"
     *
     * @param typeName
     * @return
     * @throws InvalidTypeException
     */
    public static ArrayType parseArrayType ( String typeName )
            throws InvalidTypeException
    {
        int index = typeName.indexOf('[');
        if (-1 == index)
        {
            throw new InvalidTypeException();
        }
        String bracket = typeName.substring(index);
        boolean leftBracket = false;
        int dim = 0;
        for (int i = 0, size = bracket.length(); i < size; i++)
        {
            char ch = bracket.charAt(i);
            if (ch == '[' && leftBracket == false)
            {
                leftBracket = true;
            } else if (ch == ']' && leftBracket == true)
            {
                leftBracket = false;
                dim++;
            } else
            {
                throw new InvalidTypeException();
            }
        }
        DataTypeFactory factory = DataTypeFactory.getInstance();
        String baseTypeName = typeName.substring(0, index);
        IDataType baseType = null;
        if (baseTypeName.indexOf(IEAISchemaDef.NAME_SPACE_SEPARATOR) != -1)
        { // It is a schema array, the baseType uses IEAISchemaRef.
            try
            {
                baseType = new IEAISchemaRef(baseTypeName);
            } catch (InvalidNameException ex1)
            {
                throw new InvalidTypeException();
            }
        } else
        {
            if (DataTypeNames.TYPE_NAME_SCHEMA.equals(baseTypeName))
            {
                // return an empry IEAISchema base type Item ,to actual use the
                // type need the actual base type item
                baseType = new IEAISchema();
                try
                {
                    ((IEAISchema) baseType)
                            .setTypeName(DataTypeNames.TYPE_NAME_SCHEMA);
                } catch (Exception e)
                {
                    throw new InvalidTypeException();
                }

            } else
            {
                try
                {
                    baseType = factory.getDataTypeInstance(baseTypeName);
                } catch (DataTypeException ex)
                {
                    throw new InvalidTypeException();
                }
            }
        }
        return factory.getArrayTypeInstance(baseType, dim);
    }

    public static int parseArrayTypeDimension ( String typeName )
            throws InvalidTypeException
    {
        int index = typeName.indexOf('[');
        if (-1 == index)
        {
            throw new InvalidTypeException();
        }
        String bracket = typeName.substring(index);
        boolean leftBracket = false;
        int dim = 0;
        for (int i = 0, size = bracket.length(); i < size; i++)
        {
            char ch = bracket.charAt(i);
            if (ch == '[' && leftBracket == false)
            {
                leftBracket = true;
            } else if (ch == ']' && leftBracket == true)
            {
                leftBracket = false;
                dim++;
            } else
            {
                throw new InvalidTypeException();
            }
        }
        return dim;
    }

    /**
     * check the type is a Array Type and the base type is IEAISchema
     *
     * @param type
     *            IDataType
     * @return boolean
     */
    public static boolean isArraySchemaType ( IDataType type )
    {
        if (type instanceof ArrayType
                && ((ArrayType) type).getBaseItemType() instanceof IEAISchema)
        {
            return true;
        }
        return false;
    }

    /**
     * return the or operation of the two method
     *
     * @param type
     *            IDataType
     * @return boolean
     */
    public static boolean isArraySchemaorSchemaRefType ( IDataType type )
    {
        return isArraySchemaType(type) || isArraySchemaRefType(type);
    }

    /**
     * check the type is a Array Type and the base type is IEAISchemaRef
     *
     * @param type
     *            IDataType
     * @return boolean
     */
    public static boolean isArraySchemaRefType ( IDataType type )
    {
        if (type instanceof ArrayType
                && ((ArrayType) type).getBaseItemType() instanceof IEAISchemaRef)
        {
            return true;
        }
        return false;
    }

    /**
     * if the base type is IEAISchema or IEAISchemaRef ,then get or tansform to
     * the IEAISchema
     *
     * @return IEAISchema
     */
    public IEAISchema getBaseSchemaType ()
    {
        if (_baseItemType instanceof IEAISchema)
        {
            return (IEAISchema) _baseItemType;
        } else if (_baseItemType instanceof IEAISchemaRef)
        {
            return ((IEAISchemaRef) _baseItemType).getSchema();
        }
        return null;
    }

    /**
     * getType
     *
     * @return int
     */
    public int getType ()
    {
        return DataTypeNames.TYPE_ARRAY;
    }

    public boolean equalTo ( Object another )
    {
        if (!(another instanceof ArrayType))
        {
            return false;
        }
        ArrayType anoArrayType = (ArrayType) another;
        if (this.getDimension() != anoArrayType.getDimension())
        {
            return false;
        }
        if (this.getBaseItemType() != null
                && anoArrayType.getBaseSchemaType() != null)
        {
            return true;
        }
        return this.getBaseItemType().getClass().isAssignableFrom(
                anoArrayType.getBaseItemType().getClass());
    }
}
