package com.ideal.ieai.server.operationToolbox.combobox;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.operationToolbox.toolbox.ToolsAgentIns;
import com.ideal.ieai.server.operationToolbox.toolbox.ToolsManager;
import com.ideal.ieai.server.operationToolbox.toolbox.ToolsModel;
import com.ideal.ieai.server.operationToolbox.toolbox.ToolsParamIns;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.webstudio.model.ProjectPropertyInfo;
import com.ideal.ieai.server.util.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;

public class ComboToolsManager {

    private Logger              log     = Logger.getLogger(ComboToolsManager.class);

    private static ComboToolsManager intance = new ComboToolsManager();

    public static ComboToolsManager getInstance ()
    {
        if (intance == null)
            intance = new ComboToolsManager();
        return intance;
    }

    /**
     * 转义sql特殊字符匹配_和%
     * @param str
     * @return
     */
    public static String escapeSqlSpecialChar ( String str )
    {
        if (JudgeDB.IEAI_DB_TYPE == 3){
            return str.trim().replace("_", "\\_").replace("'", "''").replace("%", "\\");
        }else{
            return str.trim().replace("'", "''").replace("%", "\\");
        }
    }

    public Map<String, Object> getToolsList (String userId, ToolsModel tools, Long start, Long limit ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<>();
        List<ToolsModel> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement psCount = null;
        ResultSet rsCount = null;
        //获取用户系统权限
        Map<String,Object> mapSystem=getSystemList(userId);
        List<Map<String,Object>> listSystem= (List<Map<String, Object>>) mapSystem.get("dataList");
        StringBuffer allSystem=new StringBuffer();
        for(Map<String,Object> mp:listSystem) {
            allSystem.append(mp.get("iid") + ",");
        }
        String allSystemString=allSystem.substring(0,allSystem.length()-1);
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sqlWhere = " ";
            sqlWhere = getToolsListSqlWhere(sqlWhere, tools);
            String sql = " SELECT TI.IID, TI.ITOOL_CODE, TI.ITOOL_NAME, TI.ISYSTEM_ID, P .INAME AS ISYSTEM, "
                    + " TI.N_OSTYPE, TI.IONETYPE_ID, TT1.ILEVELNAME AS IONETYPE, TI.ITWOTYPE_ID, TT2.ILEVELNAME AS ITWOTYPE, "
                    + " TI.ITHREETYPE_ID, TT3.ILEVELNAME AS ITHREETYPE, TI.IUSER_TYPE, TI.IUSER_NAME, TI.ITOOL_TYPE_ID, "
                    + " TGT.ITYPE_NAME AS ITOOL_TYPE, TI.ITOOL_SCRIPT_ID, TI.ITOOL_SCRIPT_NAME, TI.ITOOL_SCRIPT_TYPE, "
                    + " TI.ITOOL_STATUS_ID, TS.ISTATUS_NAME AS ITOOL_STATUS, TI.ITOOL_RETURN_CAUSE, TI.ITOOL_KEYWORD, TI.ITOOL_FIRST_DESCRIBE, "
                    + " TI.ITOOL_MATCH_IP, TI.ITOOL_DESCRIBE , TI.ICREATETIME, TI.ITOOL_AUDITOR_ID, U.IFULLNAME AS ITOOL_AUDITOR "
                    + " FROM IEAI_TOOLS_INFO TI " + " LEFT JOIN IEAI_PROJECT P ON P .IID = TI.ISYSTEM_ID "
                    + " LEFT JOIN IEAI_TOOLS_TYPE TT1 ON TT1.IID = TI.IONETYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_TYPE TT2 ON TT2.IID = TI.ITWOTYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_TYPE TT3 ON TT3.IID = TI.ITHREETYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_GROUP_TYPE TGT ON TGT.IID = TI.ITOOL_TYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_STATUS TS ON TS.IID = TI.ITOOL_STATUS_ID"
                    + " LEFT JOIN IEAI_USER U ON U.IID = TI.ITOOL_AUDITOR_ID WHERE 1=1 and itool_type_id=2 " + sqlWhere
                    + " and TI.ISYSTEM_ID in ("+allSystemString+")"
                    + " ORDER BY TI.ICREATETIME DESC ";
            String sqlCount = "";
            if (JudgeDB.IEAI_DB_TYPE == 3){
                sqlCount = "select count(*) from (" + sql + ")" +"tia";
            }else{
                sqlCount = "select count(*) from (" + sql + ")";
            }
            switch (JudgeDB.IEAI_DB_TYPE)
        {
            case 1:
            case 4:
                    sql = SQLUtil.getQueryPageSQLNew("oracle", sql);
                    break;
                case 2:
                    sql = SQLUtil.getQueryPageSQLNew("db2", sql);
                    break;
                case 3:
                    sql = SQLUtil.getQueryPageSQLNew("mysql", sql);
                    break;
                default:
                    break;
            }
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3 || JudgeDB.IEAI_DB_TYPE == 2)
            {
                ps.setLong(1, start);
                ps.setLong(2, limit);
            } else
            {
                ps.setLong(1, start + limit);
                ps.setLong(2, start);
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                ToolsModel tm = new ToolsModel();
                tm.setIid(rs.getLong("IID"));
                tm.setItoolCode(rs.getString("ITOOL_CODE"));
                tm.setItoolName(rs.getString("ITOOL_NAME"));
                tm.setIsystemId(rs.getLong("ISYSTEM_ID"));
                tm.setIsystem(rs.getLong("ISYSTEM_ID") == -1 ? "公共" : rs.getString("ISYSTEM"));
                tm.setnOsType(rs.getString("N_OSTYPE"));
                tm.setIonetypeId(rs.getLong("IONETYPE_ID"));
                tm.setIonetype(rs.getString("IONETYPE"));
                tm.setItwotypeId(rs.getLong("ITWOTYPE_ID"));
                tm.setItwotype(rs.getString("ITWOTYPE"));
                tm.setIthreetypeId(rs.getLong("ITHREETYPE_ID"));
                tm.setIthreetype(rs.getString("ITHREETYPE"));
                tm.setIuserType(rs.getString("IUSER_TYPE"));
                tm.setIuserName(rs.getString("IUSER_NAME"));
                tm.setItoolTypeId(rs.getLong("ITOOL_TYPE_ID"));
                tm.setItoolType(rs.getString("ITOOL_TYPE"));
                tm.setItoolScriptId(rs.getString("ITOOL_SCRIPT_ID"));
                tm.setItoolScriptName(rs.getString("ITOOL_SCRIPT_NAME"));
                tm.setItoolScriptType(rs.getString("ITOOL_SCRIPT_TYPE"));
                tm.setItoolStatusId(rs.getLong("ITOOL_STATUS_ID"));
                tm.setItoolStatus(rs.getString("ITOOL_STATUS"));
                tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));
                tm.setItoolKeyword(rs.getString("ITOOL_KEYWORD"));
                tm.setItoolMatchIp(rs.getString("ITOOL_MATCH_IP"));
                tm.setItoolDescribe(rs.getString("ITOOL_DESCRIBE"));
                tm.setIcreatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rs.getLong("ICREATETIME")));
                tm.setItoolAuditorId(rs.getLong("ITOOL_AUDITOR_ID"));
                tm.setItoolAuditor(rs.getString("ITOOL_AUDITOR"));
                tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                tm.setLoginUserId(userId);
                list.add(tm);
            }

            int total = 0;
            psCount = conn.prepareStatement(sqlCount);
            rsCount = psCount.executeQuery();
            while (rsCount.next()) {
                total = rsCount.getInt(1);
            }
            map.put("total", total);
            map.put("dataList", list);
        } catch (Exception e)
        {
            log.error("ComboToolsManager.getToolsList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rsCount, psCount, "getToolsList", log);
            DBResource.closeConn(conn, rs, ps, "getToolsList", log);
        }
        return map;
    }

    private String getToolsListSqlWhere ( String sqlWhere, ToolsModel tools )
    {
        if (tools.getItoolCode() != null && !"".equals(tools.getItoolCode())) {
            sqlWhere += " AND TI.ITOOL_CODE LIKE '%" + escapeSqlSpecialChar(tools.getItoolCode()) + "%' escape '\\'";
        }
        if (tools.getItoolName() != null && !"".equals(tools.getItoolName())) {
            sqlWhere += " AND TI.ITOOL_NAME LIKE '%" + escapeSqlSpecialChar(tools.getItoolName()) + "%' escape '\\'";
        }
        if (tools.getIsystemId() != null) {
            sqlWhere += " AND TI.ISYSTEM_ID = " + tools.getIsystemId();
        }
        if (tools.getIonetypeId() != null) {
            sqlWhere += " AND TI.IONETYPE_ID = " + tools.getIonetypeId();
        }
        if (tools.getItwotypeId() != null) {
            sqlWhere += " AND TI.ITWOTYPE_ID = " + tools.getItwotypeId();
        }
        if (tools.getIthreetypeId() != null) {
            sqlWhere += " AND TI.ITHREETYPE_ID = " + tools.getIthreetypeId();
        }
        if (tools.getItoolStatusId() != null) {
            sqlWhere += " AND TI.ITOOL_STATUS_ID = " + tools.getItoolStatusId();
        }
        if (StringUtils.isNotBlank(tools.getIuserType())) {
            sqlWhere += " AND TI.IUSER_TYPE = " + tools.getIuserType();
        }
        if (tools.getItoolAuditorId() != null) {
            sqlWhere += " AND TI.ITOOL_AUDITOR_ID = " + tools.getItoolAuditorId();
        }
        if (tools.getnOsType() != null && !"".equals(tools.getnOsType())) {
            sqlWhere += " AND TI.N_OSTYPE = '" + tools.getnOsType() + "'";
        }
        return sqlWhere;
    }

    public ToolsModel getToolsById (Connection conn, String iid ) throws RepositoryException
    {
        ToolsModel tm = new ToolsModel();
//        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = " SELECT TI.IID, TI.IPROJECTID, TI.ITOOL_CODE, TI.ITOOL_NAME, TI.ISYSTEM_ID, P .INAME AS ISYSTEM, "
                    + " TI.N_OSTYPE, TI.IONETYPE_ID, TT1.ILEVELNAME AS IONETYPE, TI.ITWOTYPE_ID, TT2.ILEVELNAME AS ITWOTYPE, "
                    + " TI.ITHREETYPE_ID, TT3.ILEVELNAME AS ITHREETYPE, TI.IUSER_TYPE, TI.IUSER_NAME, TI.ITOOL_TYPE_ID, "
                    + " TGT.ITYPE_NAME AS ITOOL_TYPE, TI.ITOOL_SCRIPT_ID, TI.ITOOL_SCRIPT_NAME, TI.ITOOL_SCRIPT_TYPE, TI.ISHIGHRISK, "
                    + " TI.ITOOL_STATUS_ID, TS.ISTATUS_NAME AS ITOOL_STATUS, TI.ITOOL_RETURN_CAUSE, TI.ITOOL_KEYWORD,TI.ITOOL_FIRST_DESCRIBE, "
                    + " TI.ITOOL_MATCH_IP, TI.ITOOL_DESCRIBE, TI.ICREATETIME , TI.ITOOL_AUDITOR_ID, U.IFULLNAME AS ITOOL_AUDITOR,TI.IRETURN_REASON "
                    + " FROM IEAI_TOOLS_INFO TI " + " LEFT JOIN IEAI_PROJECT P ON P .IID = TI.ISYSTEM_ID "
                    + " LEFT JOIN IEAI_TOOLS_TYPE TT1 ON TT1.IID = TI.IONETYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_TYPE TT2 ON TT2.IID = TI.ITWOTYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_TYPE TT3 ON TT3.IID = TI.ITHREETYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_GROUP_TYPE TGT ON TGT.IID = TI.ITOOL_TYPE_ID "
                    + " LEFT JOIN IEAI_TOOLS_STATUS TS ON TS.IID = TI.ITOOL_STATUS_ID "
                    + " LEFT JOIN IEAI_USER U ON U.IID = TI.ITOOL_AUDITOR_ID WHERE TI.IID=? ";

            ps = conn.prepareStatement(sql);
            ps.setString(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                tm.setIid(rs.getLong("IID"));
                tm.setProjectid(rs.getLong("IPROJECTID"));
                tm.setItoolCode(rs.getString("ITOOL_CODE"));
                tm.setItoolName(rs.getString("ITOOL_NAME"));
                tm.setIsystemId(rs.getLong("ISYSTEM_ID"));
                tm.setIsystem(rs.getLong("ISYSTEM_ID") == -1 ? "公共" : rs.getString("ISYSTEM"));
                tm.setnOsType(rs.getString("N_OSTYPE"));
                tm.setIonetypeId(rs.getLong("IONETYPE_ID"));
                tm.setIonetype(rs.getString("IONETYPE"));
                tm.setItwotypeId(rs.getLong("ITWOTYPE_ID"));
                tm.setItwotype(rs.getString("ITWOTYPE"));
                tm.setIthreetypeId(rs.getLong("ITHREETYPE_ID"));
                tm.setIthreetype(rs.getString("ITHREETYPE"));
                tm.setIuserType(rs.getString("IUSER_TYPE"));
                tm.setIuserName(rs.getString("IUSER_NAME"));
                tm.setItoolTypeId(rs.getLong("ITOOL_TYPE_ID"));
                tm.setItoolType(rs.getString("ITOOL_TYPE"));
                tm.setItoolScriptId(rs.getString("ITOOL_SCRIPT_ID"));
                tm.setItoolScriptName(rs.getString("ITOOL_SCRIPT_NAME"));
                tm.setItoolScriptType(rs.getString("ITOOL_SCRIPT_TYPE"));
                tm.setItoolStatusId(rs.getLong("ITOOL_STATUS_ID"));
                tm.setItoolStatus(rs.getString("ITOOL_STATUS"));
               // tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));
                if (JudgeDB.IEAI_DB_TYPE == 3) {
                    if (rs.getString("ITOOL_DESCRIBE") != null && !rs.getString("ITOOL_DESCRIBE").equals("")) {
                        byte[] describe = rs.getBytes("ITOOL_DESCRIBE");
                        String result = new String(describe, "UTF-8");
                        tm.setItoolDescribe(result);
                    } else {

                        tm.setItoolDescribe(FileUtils.ClobToString(rs.getClob("ITOOL_DESCRIBE")));
                    }
                    if (rs.getString("ITOOL_FIRST_DESCRIBE") != null && !rs.getString("ITOOL_FIRST_DESCRIBE").equals("")) {
                        byte[] toolfirstdescribe = rs.getBytes("ITOOL_FIRST_DESCRIBE");
                        String firstdescriberesult = new String(toolfirstdescribe, "UTF-8");
                        tm.setItoolFirstDescribe(firstdescriberesult);
                    } else {

                        tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                    }
                    if (rs.getString("ITOOL_RETURN_CAUSE") != null && !rs.getString("ITOOL_RETURN_CAUSE").equals("")) {
                        byte[] toolreturn = rs.getBytes("ITOOL_RETURN_CAUSE");
                        String returnresult = new String(toolreturn, "UTF-8");
                        tm.setItoolReturnCause(returnresult);
                    } else {

                        tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));// 回退原因
                    }

                } else {
                    tm.setItoolDescribe(FileUtils.ClobToString(rs.getClob("ITOOL_DESCRIBE")));
                    tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                    tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));// 回退原因
                }
                tm.setItoolKeyword(rs.getString("ITOOL_KEYWORD"));
                tm.setItoolMatchIp(rs.getString("ITOOL_MATCH_IP"));
               // tm.setItoolDescribe(rs.getString("ITOOL_DESCRIBE"));
                tm.setIcreatetime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(rs.getLong("ICREATETIME")));
                tm.setItoolAuditorId(rs.getLong("ITOOL_AUDITOR_ID"));
                tm.setItoolAuditor(rs.getString("ITOOL_AUDITOR"));
              //  tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                tm.setIreturnReason(rs.getString("IRETURN_REASON"));
                String isHighRisk = rs.getString("ISHIGHRISK");
                if(StringUtils.isEmpty(isHighRisk) || isHighRisk == "null" || isHighRisk == "NULL"){
                    isHighRisk = "0";
                }
                tm.setIsHighRisk(isHighRisk);
            }

            if(tm.getItoolTypeId()!=null){
                PreparedStatement ps1 = null;
                ResultSet rs1 = null;
                try{
                    //描述工具查询附件
                    if(tm.getItoolTypeId()==1){
                        sql="select t.IID,t.ITOOLS_FILE_NAME,t.ITOOLS_FILE_PATH from IEAI_TOOLS_FILES t where t.itools_id="+tm.getIid();
                        ps1 = conn.prepareStatement(sql);
                        rs1 = ps1.executeQuery();
                        List<Map<String,Object>> toolFile=new ArrayList<>();
                        while (rs1.next()){
                            Map<String,Object> map=new HashMap<>();
                            map.put("fileName",rs1.getString("ITOOLS_FILE_NAME"));
                            map.put("filePath",rs1.getString("ITOOLS_FILE_PATH"));
                            map.put("fileId",rs1.getString("IID"));
                            toolFile.add(map);
                        }
                        //附件列表不为空
                        if(!toolFile.isEmpty()) tm.getParams().put("fileList", toolFile);
                    }

                }catch (Exception e)
                {
                    log.error("ComboToolsManager.getToolsById is error", e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(rs1, ps1, "getToolsById", log);
                }
            }

        } catch (Exception e)
        {
            log.error("ComboToolsManager.getToolsById is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getToolsById", log);
        }
        return tm;
    }

    public Map<String, Object> getSystemList ( String userId ) throws RepositoryException
    {
        Map<String, Object> resultMap = new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "SELECT DISTINCT (PJ.INAME),IID FROM IEAI_PROJECT PJ,"
                + "(SELECT IPROID FROM IEAI_USERINHERIT UR, IEAI_ROLE R, IEAI_SYS_PERMISSION SP"
                + " WHERE UR.IROLEID = R.IID AND R.IID = SP.IROLEID AND UR.IUSERID = ?"
                + " AND SP.IPERMISSION = 1) QX WHERE /*(PJ.IUPPERID = QX.IPROID OR"
                + " PJ.PROTYPE = (DECODE(-QX.IPROID, 2, 7, -QX.IPROID)) OR"
                + " PJ.PROTYPE = DECODE(-QX.IPROID, 2, 8, -QX.IPROID))"
                + " AND*/ PJ.IID > 0 AND PJ.IID = PJ.ILATESTID AND PJ.PROTYPE = 3";
        List<Map<String, Object>> list = new ArrayList<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, userId);
            rs = ps.executeQuery();
            Map<String, Object> map = new HashMap<>();
            map.put("iid", -1);
            map.put("iname", "公共");
            list.add(map);
            while (rs.next())
            {
                map = new HashMap<>();
                map.put("iid", rs.getLong("IID"));
                map.put("iname", rs.getString("INAME"));
                list.add(map);
            }
            resultMap.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getSystemList", log);
        }
        return resultMap;
    }

    public Map<String, Object> getStatusList () throws RepositoryException
    {
        Map<String, Object> resultMap = new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String, Object>> list = new ArrayList<>();
        try
        {
            String sql = "select iid, istatus_name from ieai_tools_status order by iid";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            Map<String, Object> map = new HashMap<>();
            while (rs.next())
            {
                map = new HashMap<>();
                map.put("iid", rs.getLong("IID"));
                map.put("istatusName", rs.getString("ISTATUS_NAME"));
                list.add(map);
            }
            resultMap.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getStatusList", log);
        }
        return resultMap;
    }

    public Map<String, Object> getToolTypeList () throws RepositoryException
    {
        Map<String, Object> resultMap = new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        List<Map<String, Object>> list = new ArrayList<>();
        try
        {
            String sql = "select iid, itype_name from ieai_tools_group_type where iid=2";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            Map<String, Object> map = new HashMap<>();
            while (rs.next())
            {
                map = new HashMap<>();
                map.put("iid", rs.getLong("IID"));
                map.put("itypename", rs.getString("ITYPE_NAME"));
                list.add(map);
            }
            resultMap.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getStatusList", log);
        }
        return resultMap;
    }

    public void insertComboTools (Connection conn, ToolsModel tools, String[] agentUserList ) throws RepositoryException
    {
//        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            Long iid = tools.getIid()==null?0:tools.getIid();
            if(iid==0) iid = IdGenerator.createIdForGroupMessage("IEAI_TOOLS_INFO", conn);
            String sql = "INSERT INTO IEAI_TOOLS_INFO (IID, ITOOL_CODE, ITOOL_NAME, ISYSTEM_ID, N_OSTYPE, "
                    + " IONETYPE_ID, ITWOTYPE_ID, ITHREETYPE_ID, IUSER_NAME, ITOOL_TYPE_ID, ITOOL_SCRIPT_TYPE, "
                    + " ITOOL_SCRIPT_NAME, ITOOL_SCRIPT_ID, ITOOL_KEYWORD, ITOOL_MATCH_IP, ITOOL_DESCRIBE, "
                    + " ITOOL_STATUS_ID, IUSER_TYPE, ITOOL_AUDITOR_ID, ICREATETIME,ITYPEID,IVERSION, "
                    + " DAILYTYPE,ISYSTEMTYPEUUID,ISWHITEANDIP,IEDITUSER,IREMARKS,ITOOL_FIRST_DESCRIBE,ISHIGHRISK,IPROJECTID) "
                    + " VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8),1,1,0,?,?,-1,?,?,?,?) ";
            Long itoolTypeId=tools.getItoolTypeId()==null?0:tools.getItoolTypeId();
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setString(2, tools.getItoolCode());
            ps.setString(3, tools.getItoolName());
            ps.setObject(4, tools.getIsystemId());
            ps.setString(5, tools.getnOsType());
            ps.setObject(6, tools.getIonetypeId());
            ps.setObject(7, tools.getItwotypeId());
            ps.setObject(8, tools.getIthreetypeId());
            ps.setString(9, tools.getIuserName());
            ps.setObject(10, itoolTypeId);
            ps.setString(11, tools.getItoolScriptType());
            ps.setString(12, tools.getItoolScriptName());
            ps.setString(13, tools.getItoolScriptId());
            ps.setString(14, tools.getItoolKeyword());
            ps.setString(15, tools.getItoolMatchIp());
            ps.setString(16, tools.getItoolDescribe());
            ps.setObject(17, tools.getItoolStatusId());
            ps.setString(18, tools.getIuserType());
            ps.setObject(19, tools.getItoolAuditorId());
            // 发布用
            ps.setObject(20, tools.getIsystemTypeuuid());
            ps.setObject(21, tools.getIswhiteandip());
            ps.setObject(22, tools.getIremarks());
            ps.setString(23, tools.getItoolFirstDescribe());
            ps.setString(24, tools.getIsHighRisk());
            ps.setLong(25, null == tools.getProjectid() ? 0l : tools.getProjectid());

            ps.executeUpdate();
            conn.commit();
            insertComboToolsAgent(iid, agentUserList,conn);
            insertToolParam(iid, tools,conn);
            //非描述工具 清理用户操作产生的垃圾附件
            if(itoolTypeId!=1) deleteAllFile(String.valueOf(iid));

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "insertComboTools", log);
        }

    }

    public void deleteToolByRemake (String remake) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;//删除数据
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String deletesql = "delete from IEAI_TOOLS_INFO where iremarks = ?";
            ps = conn.prepareStatement(deletesql);
            ps.setString(1, remake);
            ps.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "deleteToolByRemake", log);
        }

    }

    public void insertComboToolsAgent ( Long itoolId, String[] agentUserList,Connection conn ) throws RepositoryException
    {
        PreparedStatement ps = null;
        try
        {
            if (agentUserList == null || agentUserList.length == 0)
                return;
            String sql = "INSERT INTO IEAI_TOOLS_AGENT (IID, ITOOL_ID, IAGENT_ID, IUSER_NAME) VALUES(?,?,?,?)";
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < agentUserList.length; i++)
            {
                ps.setLong(1, IdGenerator.createIdForGroupMessage("IEAI_TOOLS_AGENT", conn));
                ps.setLong(2, itoolId);
                ps.setString(3, agentUserList[i].split("@@@@")[0]);
                if(agentUserList.length==2){
                    ps.setString(4, agentUserList[i].split("@@@@")[1]);

                }else{
                    ps.setString(4, "");

                }
                ps.addBatch();
            }
            ps.executeBatch();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement( ps, "insertComboToolsAgent", log);

        }

    }

    public void insertToolParam (Long itoolId, ToolsModel tools, Connection conn ) throws RepositoryException
    {
        PreparedStatement dps = null;//先删除数据，再插入
        PreparedStatement ps = null;
        try
        {
            String deletesql = "delete from IEAI_TOOLS_PARAM where ITOOLS_ID = ?";
            dps = conn.prepareStatement(deletesql);
            dps.setLong(1, itoolId);
            dps.executeUpdate();
            conn.commit();
            String scriptName = tools.getIscriptName();
            String scriptValue = tools.getIscriptValue();
            String scriptParaDesc = tools.getIscriptParaDesc();
            if(StringUtils.isNotBlank(scriptName)|| StringUtils.isNotBlank(scriptValue)|| StringUtils.isNotBlank(scriptParaDesc)){
                String sql = "INSERT INTO IEAI_TOOLS_PARAM  (IID, ITOOLS_ID, IPARAM_NUM, IPARAM_NAME, IPARAM_VALUE, IPARAM_DES) VALUES(?,?,?,?,?,?)";
                ps = conn.prepareStatement(sql);
                String []sname = scriptName.split("@@script@@service@@");
                String []sValue = scriptValue.split("@@script@@service@@");
                String []sdesc = scriptParaDesc.split("@@script@@service@@");
                for (int i = 0; i < sname.length; i++)
                {
                    ps.setLong(1, IdGenerator.createIdForGroupMessage("IEAI_TOOLS_PARAM", conn));
                    ps.setLong(2, itoolId);
                    ps.setLong(3, i+1);
                    ps.setString(4, sname[i]);
                    if(sValue.length==0) {
                        ps.setString(5, "");
                    } else if(sValue.length>i) {
                        ps.setString(5, sValue[i]);
                    } else {
                        ps.setString(5, "");
                    }
                    if(sdesc.length==0) {
                        ps.setString(6, "");
                    } else if(sdesc.length>i) {
                        ps.setString(6, sdesc[i]);
                    } else {
                        ps.setString(6, "");
                    }
                    ps.addBatch();
                }
                ps.executeBatch();
                conn.commit();
            }

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(dps, "insertComboToolsAgent", log);
            DBResource.closePreparedStatement( ps, "insertComboToolsAgent", log);
        }

    }

    /**
     * 修改工具信息
     * @param tools
     * @param agentList
     * @param agentUserList
     * @throws RepositoryException
     */
    public void updateComboTools (Connection conn, ToolsModel tools, String[] agentList, String[] agentUserList, Long count )
            throws RepositoryException
    {

//        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            String toolSystem = ToolsManager.getInstance().getIsysTemCode(tools.getIsystemId());
            //修改当前工具
            if(tools.getItoolCode()!=null){
                String[] str = tools.getItoolCode().split("_");

                if((str[0].indexOf("JB")!=-1) || (str[0].indexOf("MS")!=-1) || (str[0].indexOf("ZH")!=-1)){
                    if(tools.getIsystemId()==-1){
                        tools.setItoolCode(str[0]+"_COMMEN_"+str[str.length-1]);
                    }else{
                        if(toolSystem==null || toolSystem.isEmpty()){
                            tools.setItoolCode(str[0]+"_"+str[str.length-1]);
                        }else{
                            tools.setItoolCode(str[0]+"_"+toolSystem+"_"+str[str.length-1]);
                        }
                    }
                }

            }else{

                String itoolCode;

                if (tools.getIsystemId()==-1){
                    itoolCode =  "ZH_" + "COMMEN_" + count;
                }else{
                    if(toolSystem==null || toolSystem.isEmpty()){
                        itoolCode = "ZH_" + count;
                    }else{
                        itoolCode = "ZH_" + toolSystem + "_" + count;
                    }
                }
                tools.setItoolCode(itoolCode);
            }


//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "UPDATE IEAI_TOOLS_INFO SET ITOOL_CODE=?, ITOOL_NAME=?, ISYSTEM_ID=?, N_OSTYPE=?, "
                    + " IONETYPE_ID=?, ITWOTYPE_ID=?, ITHREETYPE_ID=?, IUSER_NAME=?, ITOOL_TYPE_ID=?, ITOOL_SCRIPT_TYPE=?, "
                    + " ITOOL_SCRIPT_NAME=?, ITOOL_SCRIPT_ID=?, ITOOL_KEYWORD=?, ITOOL_MATCH_IP=?, ITOOL_DESCRIBE=?, "
                    + " ITOOL_RETURN_CAUSE=?, ITOOL_STATUS_ID=? ,ITOOL_AUDITOR_ID=?,ITOOL_FIRST_DESCRIBE=?,IUPDATE_TIME=FUN_GET_DATE_NUMBER_NEW("+ Constants.getCurrentSysDate() + ",8)," +
                    " CHILDIDS=?,IDELIVERY_STATUS=?, IRECEPTION_TIME=?,IDELIVERY_AUDITOR=?,IRECEPTION_AUDITOR=?,ISHIGHRISK=? WHERE IID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, tools.getItoolCode());
            ps.setString(2, tools.getItoolName());
            ps.setObject(3, tools.getIsystemId());
            ps.setString(4, tools.getnOsType());
            ps.setObject(5, tools.getIonetypeId());
            ps.setObject(6, tools.getItwotypeId());
            ps.setObject(7, tools.getIthreetypeId());
            ps.setString(8, tools.getIuserName());
            ps.setObject(9, tools.getItoolTypeId());
            ps.setString(10, tools.getItoolScriptType());
            ps.setString(11, tools.getItoolScriptName());
            ps.setString(12, tools.getItoolScriptId());
            ps.setString(13, tools.getItoolKeyword());
            ps.setString(14, tools.getItoolMatchIp());
            ps.setString(15, tools.getItoolDescribe());
            ps.setString(16, tools.getItoolReturnCause());
            ps.setObject(17, tools.getItoolStatusId());
            ps.setObject(18, tools.getItoolAuditorId());
            ps.setString(19, tools.getItoolFirstDescribe());
            ps.setString(20,tools.getChildIds());
            ps.setObject(21,tools.getIdeliveryStatus());
            ps.setString(22, tools.getIreceptionTime()==""?null:tools.getIreceptionTime());
            ps.setObject(23, tools.getIdeliveryAuditor());
            ps.setObject(24, tools.getIreceptionAuditor());
            ps.setString(25, tools.getIsHighRisk());
            ps.setLong(26, tools.getIid());
            ps.executeUpdate();
//            conn.commit();
            updateComboToolsAgent(tools.getIid(), agentList, agentUserList,conn);
            insertToolParam(tools.getIid(), tools,conn);
            //非描述工具 清理垃圾附件
            if(tools.getItoolTypeId()!=1) deleteAllFile(String.valueOf(tools.getIid()));

            if(tools.getItoolStatusId()==5){
                ToolsManager.getInstance().getDeliveryComboTools(conn,String.valueOf(tools.getIid()));
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateComboTools", log);
        }
    }

    public void updateComboToolsAgent ( Long itoolId, String[] agentList, String[] agentUserList,Connection conn )
            throws RepositoryException
    {
        PreparedStatement queryPs = null;
        PreparedStatement updatePs = null;
        ResultSet rs = null;
        try
        {
            String querySql = "SELECT IAGENT_ID FROM IEAI_TOOLS_AGENT WHERE ITOOL_ID = ?";
            queryPs = conn.prepareStatement(querySql);
            queryPs.setLong(1, itoolId);
            rs = queryPs.executeQuery();
            List<String> bindList = new ArrayList<>();
            while (rs.next())
                bindList.add(rs.getString("IAGENT_ID"));
            if (agentList == null || agentList.length == 0)
            {
                if (!bindList.isEmpty())
                {
                    String removeSql = "DELETE IEAI_TOOLS_AGENT WHERE ITOOL_ID = ?";
                    updatePs = conn.prepareStatement(removeSql);
                    updatePs.setLong(1, itoolId);
                    updatePs.executeUpdate();
                    conn.commit();
                }

            } else
            {
                List<String> aList = new ArrayList<>(Arrays.asList(agentList));
                if (bindList.size() == agentList.length && !bindList.retainAll(aList) && !aList.retainAll(bindList))
                {
                    // 绑定Agent本身没有发生变化，仅需修改对应执行用户
                    String removeSql = "UPDATE IEAI_TOOLS_AGENT SET IUSER_NAME=? WHERE ITOOL_ID = ? AND IAGENT_ID = ? ";
                    updatePs = conn.prepareStatement(removeSql);
                    for (int i = 0; i < agentUserList.length; i++)
                    {
                        if(agentUserList.length==2){
                            updatePs.setString(1, agentUserList[i].split("@@@@")[1]);
                        }else{
                            updatePs.setString(1,"");
                        }

                        updatePs.setLong(2, itoolId);
                        updatePs.setString(3, agentUserList[i].split("@@@@")[0]);
                        updatePs.addBatch();
                    }
                    updatePs.executeBatch();
                    conn.commit();
                } else
                {
                    // 删除原有绑定
                    String removeSql = "DELETE IEAI_TOOLS_AGENT WHERE ITOOL_ID = ?";
                    updatePs = conn.prepareStatement(removeSql);
                    updatePs.setLong(1, itoolId);
                    updatePs.executeUpdate();
                    conn.commit();
                    // 重新插入绑定
                    insertComboToolsAgent(itoolId, agentUserList,conn);
                }
            }

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(updatePs, "updateComboToolsAgent", log);
            DBResource.closePSRS( rs, queryPs, "updateComboToolsAgent", log);
        }
    }

    public Map<String, Object> getScriptList () throws RepositoryException
    {
        Map<String, Object> resultMap = new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "SELECT " +
                " * " +
                "FROM " +
                " ( " +
                " SELECT " +
                "  T.* , " +
                "  ROWNUM AS RN " +
                " FROM " +
                "  ( " +
                "  SELECT " +
                "   D.SUCCESSTIMES, " +
                "   D.TOTALTIMES, " +
                "   D.SUCCESSRATE, " +
                "   A.IID, " +
                "   A.IFUNCDESC," +
                "   A.ICONTENT," +
                "   A.ISCRIPTUUID," +
                "   A.ISERVICESNAME," +
                "   A.IISSHARE," +
                "   A.IPLATFORM," +
                "   A.ISYSNAME," +
                "   A.IBUSSNAME," +
                "   A.ISCRIPTTYPE || '' AS ISCRIPTTYPE," +
                "   A.ISCRIPTNAME," +
                "   A.ISCRIPTPARA," +
                "   A.ISTATUS," +
                "   A.IBUSSID," +
                "   A.IBUSSTYPEID," +
                "   A.THREETYPEID," +
                "   A.THREETYPENAME," +
                "   A.IISFLOW," +
                "   A.IISTOP," +
                "   A.ITOPTIME," +
                "   A.IVERSION," +
                "   A.ILEVEL," +
                "   ( SELECT IFULLNAME FROM ieai_user WHERE IID = A.IUSERID ) AS USERNAME," +
                "   ( SELECT IFULLNAME FROM IEAI_USER A WHERE A.ILOGINNAME = A.CREATEUSERNAME ) AS CREATEUSER," +
                "   ( SELECT IFULLNAME FROM IEAI_USER A WHERE A.IID = A.IUPDATEUSERID ) AS UPDATEUSER," +
                "   B.BSNAME," +
                "   C.TYPENAME," +
                "   A.ISEMSCRIPT  " +
                "  FROM" +
                "   IEAI_SCRIPT_TEST A," +
                "   IEAI_SCRIPT_BS B," +
                "   IEAI_SCRIPT_BSTYPE C," +
                "   ("+viwsSQL+") d," +
                "   (" +
                "   SELECT" +
                "    B.ILASTID," +
                "    MAX( B.IID ) MAXIID " +
                "   FROM" +
                "    IEAI_SCRIPT_TEST B " +
                "   WHERE" +
                "   B.ISDELETE = 0 " +
                "   and  b.IID  IN ( SELECT G.IID FROM IEAI_SCRIPT_SERVICES G WHERE G.ISTATUS = 1 ) " +
                "   GROUP BY" +
                "    B.ILASTID " +
                "   ) E " +
                "  WHERE" +
                "   A.IID = e.MAXIID " +
                "   AND A.IBUSSID = B.IID " +
                "   AND A.IBUSSTYPEID = C.iid " +
                "   AND d.ilastid = a.ILASTID " +
                "   AND A.ISCRIPTTYPE IS NOT NULL " +
                "   AND ( ISAUTOSUB IS NULL OR ISAUTOSUB = 0 ) " +
                "  ) T " +
                " ORDER BY " +
                "  T.IISTOP," +
                "  T.ITOPTIME DESC," +
                "  T.IID DESC " +
                " ) TABLE_ALIAS " +
                "ORDER BY " +
                " TABLE_ALIAS.ISTATUS";
        List<Map<String, Object>> list = new ArrayList<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> map = new HashMap<>();
                map.put("iid", rs.getString("ISCRIPTUUID"));
                map.put("iservicesname", rs.getString("ISERVICESNAME"));
                list.add(map);
            }
            resultMap.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getScriptList", log);
        }
        return resultMap;
    }

    public String removeComboTools ( String[] ids ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement psCount = null;
        ResultSet rsCount = null;
        String result="";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            for(String id:ids){
                String str="\"childId\":\""+id+"\"";
                String sqlCount="select ITOOL_NAME  from IEAI_TOOLS_INFO t where childids like '%"+str+"%'";
                String  toolCode = "";
                psCount = conn.prepareStatement(sqlCount);
                rsCount = psCount.executeQuery();
                while (rsCount.next())
                {
                    toolCode = rsCount.getString(1);
                    result+=toolCode+",";
                }
                if(StringUtils.isNotEmpty(result)){
                    result=result.substring(0,result.length()-1);
                    sqlCount="select ITOOL_NAME  from IEAI_TOOLS_INFO t where iid ="+id;
                    psCount = conn.prepareStatement(sqlCount);
                    rsCount = psCount.executeQuery();
                    while (rsCount.next()){
                        String tCode=rsCount.getString(1);
                        result= tCode+"工具已被"+result+"引用,不可删除 ";
                    }
                } else{
                    String sql = "delete from IEAI_TOOLS_INFO where iid ='"+id+"'";
                    ps = conn.prepareStatement(sql);
                    ps.executeUpdate();
                    conn.commit();
                    //删除工作流相关信息
                    delWorkflow(Long.valueOf(id));

                }
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "removeToolbox", log);
            DBResource.closePSRS(rsCount, psCount, "removeToolbox", log);
        }
        if(StringUtils.isEmpty(result)){
            result="删除成功！";
        }
        return result;
    }

    private void removeComboToolsAgent ( String[] ids ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from IEAI_TOOLS_AGENT where itool_id in (" + StringUtils.join(ids, ",") + ")";
            if (null != ids && ids.length > 0)
            {
                ps = conn.prepareStatement(sql);
                ps.executeUpdate();
                conn.commit();
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "removeComboTools", log);
        }
    }

    public Map<String, Object> ComboToolsGetAgentList ( String isystemId, String ip, String status, String itoolId,
                                                        Long start, Long limit ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement psCount = null;
        ResultSet rsCount = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sqlWhere = "";
            if (StringUtils.isNotBlank(isystemId))
                if (isystemId.equals("-1"))
                    sqlWhere += " AND SR.SYSTEMID IN (SELECT IID FROM IEAI_PROJECT WHERE PROTYPE = 3)";
                else
                    sqlWhere += " AND SR.SYSTEMID = " + isystemId;
            if (StringUtils.isNotBlank(ip))
                sqlWhere += " AND AI.IAGENT_IP LIKE '%" + ip + "%' ";
            if (StringUtils.isNotBlank(status))
                sqlWhere += " AND AI.IAGENT_STATE =" + Integer.parseInt(status);
            if (StringUtils.isBlank(itoolId))
                itoolId = "-1";
            String sql = "SELECT DISTINCT AI.IAGENTINFO_ID, AI.IAGENT_NAME, AI.IAGENT_IP, AI.IAGENT_PORT, AI.IAGENT_STATE, TA.IUSER_NAME "
                    + " FROM IEAI_AGENTINFO AI LEFT JOIN IEAI_COMPUTER_LIST CT ON AI.IAGENTINFO_ID = CT.IAGENTINFO_ID "
                    + " LEFT JOIN IEAI_SYS_RELATION SR ON CT.CPID = SR.COMPUTERID "
                    + " LEFT JOIN IEAI_TOOLS_AGENT TA ON AI.IAGENTINFO_ID = TA.IAGENT_ID AND TA.ITOOL_ID = " + itoolId
                    + " WHERE 1=1 " + sqlWhere;
            String sqlCount = "select count(*) from (" + sql + ")";

            sql = SQLUtil.getQueryPageSQL("oracle", sql);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, start + limit);
            ps.setLong(2, start);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> m = new HashMap<>();
                m.put("iagentid", rs.getString("IAGENTINFO_ID"));
                m.put("iagentname", rs.getString("IAGENT_NAME"));
                m.put("iagentip", rs.getString("IAGENT_IP"));
                m.put("iagentport", rs.getString("IAGENT_PORT"));
                m.put("iagentstate", rs.getString("IAGENT_STATE"));
                m.put("iusername", rs.getString("IUSER_NAME"));
                list.add(m);
            }
            int total = 0;
            psCount = conn.prepareStatement(sqlCount);
            rsCount = psCount.executeQuery();
            while (rsCount.next())
                total = rsCount.getInt(1);
            map.put("total", total);
            map.put("dataList", list);

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rsCount, psCount, "ComboToolsGetAgentList", log);
            DBResource.closeConn(conn, rs, ps, "ComboToolsGetAgentList", log);
        }
        return map;
    }

    public Map<String, Object> ComboToolsGetBindAgentList ( Map sortMap, long itoolId, String ip, String status,String igroupToolsId,String ititleName,String isystem,String userId,String iuserName,String fisystem)
            throws RepositoryException
    {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sqlWhere = "";
            if (StringUtils.isNotBlank(ip)) {
                sqlWhere += " AND AI.IAGENT_IP LIKE '%" + escapeSqlSpecialChar(ip) + "%' escape '\\'";
            }
            if (StringUtils.isNotBlank(status)) {
                sqlWhere += " AND AI.IAGENT_STATE =" + Integer.parseInt(status);
            }
            String sql ="";
//                String orderSql = setOrder(sortMap,isystem,fisystem);
            /**
             * 此处的查询逻辑：
             * 组合工具【公共系统】 调用工具【公共系统】 查询：业务系统下应用变更的登录人权限系统配置的设备
             * 组合工具【公共系统】 调用工具【非公共系统】 查询：登录人权限系统的工具
             * 组合工具【非公共系统】 调用工具【非公共系统】 查询：登录人权限系统的工具
             */
            if("-1".equals(isystem) && "-1".equals(fisystem)){
//                    log.info("公共调公共-------"+"【"+isystem + "】【" + fisystem+"】");
                List<String> rlist = ToolsManager.getInstance().getRoleId(userId);
                String us = ToolsManager.getInstance().getUserSysById(rlist);
                List<Long> slist = ToolsManager.getInstance().getRoleSystemId(rlist,us);


                List<Long> cplist = ToolsManager.getInstance().getCpid(slist);

                List<Long> alist = ToolsManager.getInstance().getIagentinfoid(cplist);

                //list分割多个list
                List<List<Long>> lists = ToolsManager.getInstance().groupList(alist);

                sql=" SELECT AI.IAGENTINFO_ID AS IID, AI.IAGENTINFO_ID AS IAGENT_ID, AI.IAGENT_NAME, AI.IAGENT_IP, AI.IAGENT_PORT, AI.IAGENT_STATE, ( SELECT COUNT(1) FROM ieai_tools_agent_ins T WHERE T .ITOOLS_ID = ? AND T .IAGENT_ID = AI.IAGENTINFO_ID AND T .IGROUP_TOOLS_ID = ? AND T .ITITLE_NAME = ? ) AS num FROM IEAI_AGENTINFO AI WHERE 1 = 1 "+ sqlWhere;;

                if(lists.size()>0){
                    sql += " AND ( ";
                    for (int i = 0; i < lists.size(); i++) {
                        if(i==0){
                            sql += " IAGENTINFO_ID in('" + StringUtils.join(lists.get(i), "','") + "')";
                        }else{
                            sql += " or IAGENTINFO_ID in('" + StringUtils.join(lists.get(i), "','") + "')";
                        }
                    }
                    sql += " ) ";
                }

                if (sortMap.get("column") != null && !"".equals(sortMap.get("column")))
                {
                    sql +=" ORDER BY "+sortMap.get("column")+" "+sortMap.get("sortorder");
                }

                ps = conn.prepareStatement(sql);
                ps.setLong(1, itoolId);
                ps.setString(2, igroupToolsId);
                ps.setString(3, ititleName);

            }else {
//                    log.info("【"+isystem + "】【" + fisystem+"】");
                sql = "SELECT TA.IID, TA.ITOOL_ID, TA.IAGENT_ID, TA.IUSER_NAME, AI.IAGENT_NAME, AI.IAGENT_IP, "
                        + " AI.IAGENT_PORT, AI.IAGENT_STATE ," +
                        "(select count(1) from ieai_tools_agent_ins t where t.ITOOLS_ID=? and t.IAGENT_ID=TA.IAGENT_ID and t.IGROUP_TOOLS_ID=? and t.ITITLE_NAME=?) as num " +
                        " FROM IEAI_TOOLS_AGENT TA LEFT JOIN IEAI_AGENTINFO AI "
                        + " ON TA.IAGENT_ID = AI.IAGENTINFO_ID WHERE TA.ITOOL_ID = ? " + sqlWhere;

                if (sortMap.get("column") != null && !"".equals(sortMap.get("column")))
                {
                    sql +=" ORDER BY "+sortMap.get("column")+" "+sortMap.get("sortorder");
                }

                ps = conn.prepareStatement(sql);
                ps.setLong(1, itoolId);
                ps.setString(2, igroupToolsId);
                ps.setString(3, ititleName);
                ps.setLong(4, itoolId);

            }


            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> m = new HashMap<>();
                m.put("iid", rs.getLong("IID"));
                if("-1".equals(isystem) && "-1".equals(fisystem)){
                    m.put("itoolid", itoolId);
                    m.put("iusername", iuserName);
                }else{
                    m.put("itoolid", rs.getString("ITOOL_ID"));
                    m.put("iusername", rs.getString("IUSER_NAME"));

                }
                m.put("iagentid", rs.getString("IAGENT_ID"));
                m.put("iagentname", rs.getString("IAGENT_NAME"));
                m.put("iagentip", rs.getString("IAGENT_IP"));
                m.put("iagentport", rs.getString("IAGENT_PORT"));
                m.put("iagentstate", rs.getString("IAGENT_STATE"));
                m.put("num", rs.getLong("num"));

                list.add(m);
            }
            map.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "ComboToolsGetBindAgentList", log);
        }
        return map;
    }


    public String setOrder ( Map sortMap,String isystem,String fisystem)
    {
        StringBuilder order = new StringBuilder();
        String property = sortMap.get("property") == null ? "" : sortMap.get("property").toString();
        String direction = sortMap.get("direction") == null ? "" : sortMap.get("direction").toString();
        if (org.apache.commons.lang.StringUtils.isNotBlank(property) && org.apache.commons.lang.StringUtils.isNotBlank(direction))
        {
            order.append(" order by ");
            if (org.apache.commons.lang.StringUtils.equals(property, "iagentip"))
            {
                if("-1".equals(isystem) && "-1".equals(fisystem)){
                    order.append("AI.IAGENTINFO_ID");
                }else{
                    order.append("TA.IAGENT_ID");
                }
            }
            order.append(" ");
            order.append(direction);
            return order.toString();
        } else
        {
            return "";
        }
    }

    public boolean checkRole ( String userId, String role ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "SELECT COUNT(0) AS COUNT FROM IEAI_USER A LEFT JOIN IEAI_USERINHERIT B ON A .IID = B.IUSERID "
                    + " LEFT JOIN IEAI_ROLE C ON B.IROLEID = C.IID  WHERE A .IID = ? AND C.INAME = ? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, userId);
            ps.setString(2, role);
            rs = ps.executeQuery();
            while (rs.next())
                count = rs.getInt("COUNT");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "checkRole", log);
        }
        return count > 0;
    }

    public void backComboTools ( String iid, String itoolReturnCause ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "UPDATE IEAI_TOOLS_INFO SET ITOOL_RETURN_CAUSE=?, ITOOL_STATUS_ID=3 WHERE IID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, itoolReturnCause);
            ps.setString(2, iid);

            ps.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "backComboTools", log);
        }
    }

    public String ComboToolsGetScirptContent ( String uuid )
    {
        String s = "";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);

            String sql = "SELECT   " +
                    " *    " +
                    "FROM   " +
                    " (   " +
                    " SELECT   " +
                    "  T.* ,   " +
                    "  ROWNUM AS RN    " +
                    " FROM   " +
                    "  (   " +
                    "  SELECT   " +
                    "   D.SUCCESSTIMES,   " +
                    "   D.TOTALTIMES,   " +
                    "   D.SUCCESSRATE,   " +
                    "   A.IID,   " +
                    "   A.IFUNCDESC,   " +
                    "   A.ICONTENT,   " +
                    "   A.ISCRIPTUUID,   " +
                    "   A.ISERVICESNAME,   " +
                    "   A.IISSHARE,   " +
                    "   A.IPLATFORM,   " +
                    "   A.ISYSNAME,   " +
                    "   A.IBUSSNAME,   " +
                    "   A.ISCRIPTTYPE || '' AS ISCRIPTTYPE,   " +
                    "   A.ISCRIPTNAME,   " +
                    "   A.ISCRIPTPARA,   " +
                    "   A.ISTATUS,   " +
                    "   A.IBUSSID,   " +
                    "   A.IBUSSTYPEID,   " +
                    "   A.THREETYPEID,   " +
                    "   A.THREETYPENAME,   " +
                    "   A.IISFLOW,   " +
                    "   A.IISTOP,   " +
                    "   A.ITOPTIME,   " +
                    "   A.IVERSION,   " +
                    "   A.ILEVEL,   " +
                    "   ( SELECT IFULLNAME FROM ieai_user WHERE IID = A.IUSERID ) AS USERNAME,   " +
                    "   ( SELECT IFULLNAME FROM IEAI_USER A WHERE A.ILOGINNAME = A.CREATEUSERNAME ) AS CREATEUSER,   " +
                    "   ( SELECT IFULLNAME FROM IEAI_USER A WHERE A.IID = A.IUPDATEUSERID ) AS UPDATEUSER,   " +
                    "   B.BSNAME,   " +
                    "   C.TYPENAME,   " +
                    "   A.ISEMSCRIPT    " +
                    "  FROM   " +
                    "   IEAI_SCRIPT_TEST A,   " +
                    "   IEAI_SCRIPT_BS B,   " +
                    "   IEAI_SCRIPT_BSTYPE C,   " +
                    "   ("+viwsSQL+") d,   " +
                    "   (   " +
                    "   SELECT   " +
                    "    B.ILASTID,   " +
                    "    MAX( B.IID ) MAXIID    " +
                    "   FROM   " +
                    "    IEAI_SCRIPT_TEST B    " +
                    "   WHERE   " +
                    "    B.ISDELETE = 0    " +
                    "    AND b.IID NOT IN ( SELECT G.IID FROM IEAI_SCRIPT_SERVICES G WHERE G.ISTATUS = 2 )    " +
                    "   GROUP BY   " +
                    "    B.ILASTID    " +
                    "   ) E    " +
                    "  WHERE   " +
                    "   A.IID = e.MAXIID    " +
                    "   AND A.IBUSSID = B.IID    " +
                    "   AND A.IBUSSTYPEID = C.iid    " +
                    "   AND d.ilastid = a.ILASTID    " +
                    "   AND A.ISCRIPTTYPE IS NOT NULL    " +
                    "   AND ( ISAUTOSUB IS NULL OR ISAUTOSUB = 0 )    " +
                    "  ) T    " +
                    " ORDER BY   " +
                    "  T.IISTOP,   " +
                    "  T.ITOPTIME DESC,   " +
                    "  T.IID DESC    " +
                    " ) TABLE_ALIAS    " +
                    " where ISCRIPTUUID = ?  " +
                    "ORDER BY   " +
                    " TABLE_ALIAS.ISTATUS";
            ps = conn.prepareStatement(sql);
            ps.setString(1, uuid);
            rs = ps.executeQuery();
            while (rs.next())
                s = rs.getString("ICONTENT");

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "ComboToolsGetScirptContent", log);
        }
        return s;
    }

    public Map<String, Object> ComboToolsGetAuditorList ( String roleName ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;

        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = " SELECT A.IID,A.IFULLNAME,A.ILOGINNAME FROM IEAI_USER A LEFT JOIN IEAI_USERINHERIT B "
                    + " ON A .IID = B.IUSERID LEFT JOIN IEAI_ROLE C ON B.IROLEID = C.IID WHERE C.INAME = ? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, roleName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> m = new HashMap<>();
                m.put("iid", rs.getLong("IID"));
                m.put("iname", rs.getString("IFULLNAME"));
                list.add(m);
            }
            map.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "ComboToolsGetAuditorList", log);
        }
        return map;
    }

    public boolean ComboToolsCheckCodeqUnique ( String itoolCode )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "SELECT COUNT(0) AS COUNT FROM IEAI_TOOLS_INFO WHERE ITOOL_CODE = ? ";
            ps = conn.prepareStatement(sql);
            ps.setString(1, itoolCode);
            rs = ps.executeQuery();
            while (rs.next())
                count = rs.getInt("COUNT");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "ComboToolsCheckCodeqUnique", log);
        }
        return count <= 0;
    }
    /**
     　　* @description: 附件上传
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/29 8:24
     　　*/
    public Map<String, Object> importFile(CommonsMultipartFile files, String  reuserCode, String toolId) {
        Map<String,Object> reMap=new HashMap<>();
        OutputStream os = null;
        InputStream inputStream=null;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        //存储路径
        String filePathDefault= ServerEnv.getInstance().getIEAIHome() + File.separator + "tools_file" + File.separator;
        String filePath= Environment.getInstance().getSysConfig("everbright.bank.tool.path",filePathDefault);
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            if(StringUtils.isEmpty(toolId))
                toolId = String.valueOf(IdGenerator.createIdForGroupMessage("IEAI_TOOLS_INFO", conn));
            reMap.put("toolId",toolId);
            Map<String,Object> mmp=new HashMap<>();
            String time=String.valueOf(new Date().getTime());
            String fileName=files.getOriginalFilename();
            if(checkFile(toolId,fileName)) {


                //取时间戳后4位标记附件 区别多人 重复上传一个附件
                fileName = time.substring(9, 13) + "-" + fileName;
                File file = new File(filePath);
                if (!file.exists()) file.mkdirs();
                os = new FileOutputStream(filePath + File.separator + fileName);
                byte[] bs = new byte[1024];
                int len;
                inputStream = files.getInputStream();
                while ((len = inputStream.read(bs)) != -1) os.write(bs, 0, len);
                Long iid = IdGenerator.createIdForGroupMessage("IEAI_TOOLS_FILES", conn);

                String sql = "insert into IEAI_TOOLS_FILES (IID, ITOOLS_ID, ITOOLS_FILE_NAME, ITOOLS_FILE_PATH, ITOOLS_CREATE_TIME, ITOOLS_CREATE_USERID)" +
                        "values (" + iid + ",'" + toolId + "','" + fileName + "','" + filePath + "','" + time + "','" + reuserCode + "')";
                ps = conn.prepareStatement(sql);
                ps.executeUpdate();
                conn.commit();
                reMap.put("fileId", iid);
                reMap.put("fileName", fileName);
                reMap.put("success", 1);
            }else {
                reMap.put("success",2);
                reMap.put("msg","该附件已存在！");
            }
        }catch (Exception e){
            e.printStackTrace();
            reMap.put("success",2);
            reMap.put("msg",e.getMessage());
        }
        finally {
            if(os !=null) try {
                os.close();
                inputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            DBResource.closeConn(conn, rs, ps, "importFile", log);
        }
        return reMap;
    }
    /**
     　　* @description: 获取附件
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/29 8:12
     　　*/
    public Map<String, Object> getFile(String fileId) {
        Map<String,Object> reMap=new HashMap<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "select *from IEAI_TOOLS_FILES where IID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, fileId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                reMap.put("IID",rs.getString("IID"));
                reMap.put("toolId",rs.getString("ITOOLS_ID"));
                reMap.put("fileName",rs.getString("ITOOLS_FILE_NAME"));
                reMap.put("fileTime",rs.getString("ITOOLS_CREATE_TIME"));
                reMap.put("fileUser",rs.getString("ITOOLS_CREATE_USERID"));
                reMap.put("filePath",rs.getString("ITOOLS_FILE_PATH"));

            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getFile", log);
        }
        return reMap;
    }
    /**
     　　* @description: 根据工具ID获取所有附件
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/29 8:12
     　　*/
    public List<Map<String, Object>> getFileToolId(String toolId) {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String,Object>> list=new ArrayList<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "select *from IEAI_TOOLS_FILES where ITOOLS_ID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, toolId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("IID",rs.getString("IID"));
                mp.put("toolId",rs.getString("ITOOLS_ID"));
                mp.put("fileName",rs.getString("ITOOLS_FILE_NAME"));
                mp.put("fileTime",rs.getString("ITOOLS_CREATE_TIME"));
                mp.put("fileUser",rs.getString("ITOOLS_CREATE_USERID"));
                mp.put("filePath",rs.getString("ITOOLS_FILE_PATH"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getFileToolId", log);
        }
        return list;
    }
    /**
     　　* @description: 删除指定附件
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/29 8:13
     　　*/
    public Map<String, Object> deleteFile(String fileId) {
        Map<String,Object> reMap=new HashMap<>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            Map<String,Object> files=this.getFile(fileId);
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from IEAI_TOOLS_FILES where IID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1,fileId);
            ps.executeUpdate();
            conn.commit();
            //文件
            File file = new File(files.get("filePath").toString()+files.get("fileName").toString());
            if(file.exists()) file.delete();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            reMap.put("success",2);
            reMap.put("msg","删除失败！");
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "deleteFile", log);
        }
        reMap.put("success",1);
        reMap.put("msg","删除成功！");
        return reMap;
    }
    /**
     　　* @description: 改变工具类型清除已上传的附件 删除所有附件
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/29 8:05
     　　*/
    public boolean deleteAllFile(String toolId) {
        boolean flag=true;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            List<Map<String,Object>> files=this.getFileToolId(toolId);
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from IEAI_TOOLS_FILES where ITOOLS_ID=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1,toolId);
            ps.executeUpdate();
            conn.commit();
            for(Map<String,Object> map:files){
                //文件
                File file = new File(map.get("filePath").toString()+map.get("fileName").toString());
                if(file.exists()) file.delete();
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            flag=false;
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "deleteAllFile", log);
        }
        return flag;
    }
    public String getIId() {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String  toolId="";
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            toolId =String.valueOf(IdGenerator.createIdForGroupMessage("IEAI_TOOLS_INFO", conn));

        }catch (Exception e){
            e.printStackTrace();
        }
        finally {
            DBResource.closeConn(conn, rs, ps, "getIId", log);
        }
        return toolId;
    }
    /**
     　　* @description: 检查附件是否已存在
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/29 8:13
     　　*/
    public boolean checkFile ( String toolId,String fileName )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        int count = 0;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "SELECT COUNT(0) AS COUNT FROM IEAI_TOOLS_FILES WHERE ITOOLS_ID = ?  and ITOOLS_FILE_NAME like '%-"+fileName+"'";
            ps = conn.prepareStatement(sql);
            ps.setString(1, toolId);
            rs = ps.executeQuery();
            while (rs.next())
                count = rs.getInt("COUNT");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "checkFile", log);
        }
        return count <= 0;
    }

    public Map<String, Object> getToolsCodeList() throws RepositoryException
    {
        Map<String, Object> resultMap = new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select i.iid,i.itool_code from ieai_tools_info i where i.itool_status_id=2 order by i.icreatetime desc";
        List<Map<String, Object>> list = new ArrayList<>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Object> map = new HashMap<>();
                map.put("iid", rs.getLong("iid"));
                map.put("itoolCode", rs.getString("itool_code"));
                list.add(map);
            }
            resultMap.put("dataList", list);
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getToolsCodeList", log);
        }
        return resultMap;
    }

    /**
     * 发布
     * @param tools
     * @param agentList
     * @param agentUserList
     */
    public void updateRelease(Connection conn, ToolsModel tools, String[] agentList, String[] agentUserList) throws RepositoryException
    {
//        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "UPDATE IEAI_TOOLS_INFO SET ITOOL_STATUS_ID=? ,ITOOL_AUDITOR_ID=?,CHILDIDS=?  WHERE IID=?";
            ps = conn.prepareStatement(sql);
            ps.setObject(1, tools.getItoolStatusId());
            ps.setObject(2, tools.getItoolAuditorId());
            ps.setObject(3, tools.getChildIds());
            ps.setLong(4, tools.getIid());
            ps.executeUpdate();
//            conn.commit();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateRelease", log);
        }

    }

    /**
     * 查询工作流内容
     * @param iid
     * @return
     * @throws RepositoryException
     */
    public String getFlowxml(Long iid) throws RepositoryException {
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String str = "";
        String sql = "select fclob.icontent as icontent from ieai_project_property_info ppi " +
                " left join ieai_studio_flow_xml fxml on ppi.iid=fxml.flowid" +
                " left join ieai_webstudio_flow_clob fclob on fclob.iid=fxml.xmlid" +
                " where ppi.iprojectid=? and ppi.iname='flowTools'";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
                str = rs.getString("icontent");

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getFlowxml", log);
        }
        return str;
    }
    /**
     　　* @description: 获取系统编码和工具编号
     　　* @param ${tags}
     　　* @return ${return_type}
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/3/24 13:36
     　　*/
    public Map<String, Object> getSysnumber(String toolId) {
        Map<String,Object> mp=new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select t1.ITOOL_CODE ,t2.ISYSTEMCODE from ieai_tools_info t1 left join IEAI_PROJECT t2 on t1.isystem_id =t2.iid where t1.iid=?";

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, toolId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                mp.put("toolCode",rs.getString("ITOOL_CODE"));
                String isystemCode=rs.getString("ISYSTEMCODE");
                //待定
                if(StringUtils.isEmpty(isystemCode)) isystemCode = "";
                mp.put("isystemCode",isystemCode);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getSysnumber", log);
        }
        return mp;
    }

    public void saveToolsAgentIns(ToolsAgentIns toolsAgentIns) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            Long iid = toolsAgentIns.getIid()==null?0:toolsAgentIns.getIid();
            if(iid==0) iid = IdGenerator.createIdForGroupMessage("IEAI_TOOLS_AGENT_INS", conn);
            String sql = "INSERT INTO IEAI_TOOLS_AGENT_INS(IID, IGROUP_TOOLS_ID, ITOOLS_ID, IAGENT_ID, IUSER_NAME,ITITLE_NAME) VALUES (?, ?, ?, ?, ?,?) ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setLong(2, toolsAgentIns.getIgroupToolsId());
            ps.setLong(3, toolsAgentIns.getItoolsId());
            ps.setLong(4, toolsAgentIns.getIagentId());
            ps.setString(5, toolsAgentIns.getIuserName());
            ps.setString(6, toolsAgentIns.getItitleName());
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "saveToolsAgentIns", log);
        }

    }

    public void saveToolsParamIns(ToolsParamIns toolsParamIns) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            Long iid = toolsParamIns.getIid()==null?0:toolsParamIns.getIid();
            if(iid==0) iid = IdGenerator.createIdForGroupMessage("IEAI_TOOLS_PARAM_INS", conn);
            String sql = "INSERT INTO IEAI_TOOLS_PARAM_INS(IID, IGROUP_TOOLS_ID, ITOOLS_ID, IPARAM_NUM, IPARAM_NAME, IPARAM_VALUE, IPARAM_DESCRIBE, IPARAM_VALUE_INS,ITITLE_NAME,IPARAM_IID) VALUES (?, ?, ?, ?, ?, ?, ?, ?,?,?) ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setLong(2, toolsParamIns.getIgroupToolsId());
            ps.setLong(3, toolsParamIns.getItoolsId());
            ps.setLong(4, toolsParamIns.getIparamNum());
            ps.setString(5, toolsParamIns.getIparamName());
            ps.setString(6, toolsParamIns.getIparamValue());
            ps.setString(7, toolsParamIns.getIparamDescribe());
            ps.setString(8, toolsParamIns.getIparamValueIns());
            ps.setString(9, toolsParamIns.getItitleName());
            ps.setLong(10, toolsParamIns.getIparamIid());
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "saveToolsParamIns", log);
        }

    }

    public void delToolsParamIns(long igroupToolsId, long itoolsId,String ititleName) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from IEAI_TOOLS_PARAM_INS where IGROUP_TOOLS_ID = ? and ITOOLS_ID=? and ITITLE_NAME=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, igroupToolsId);
            ps.setLong(2, itoolsId);
            ps.setString(3,ititleName);
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "delToolsParamIns", log);
        }
    }

    public void delToolsAgentIns(long igroupToolsId, long itoolsId,String ititleName) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from IEAI_TOOLS_AGENT_INS where IGROUP_TOOLS_ID = ? and ITOOLS_ID=? AND ITITLE_NAME=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, igroupToolsId);
            ps.setLong(2, itoolsId);
            ps.setString(3, ititleName);
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "delToolsAgentIns", log);
        }
    }

    public void delePrparamIns(Long toolsId, List<String> nList) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            String str = "and ititle_name not in(";
            for (int i = 0; i < nList.size(); i++){
                if(i==nList.size()-1){
                    str+= StringUtils.join("'"+nList.get(i)+"'", "");
                }else{
                    str+= StringUtils.join("'"+nList.get(i)+"'", ",");
                }

            }
            str+= ")";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from ieai_tools_param_ins where  igroup_tools_id=? "+ str;
            ps = conn.prepareStatement(sql);
            ps.setLong(1, toolsId);

            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "delePrparamIns", log);
        }
    }

    public void delePrparamInsAll(Long toolsId) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from ieai_tools_param_ins where  igroup_tools_id=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, toolsId);
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "delePrparamInsAll", log);
        }
    }

    public void deleAgentIns(Long toolsId, List<String> nList) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            String str = "and ititle_name not in(";
            for (int i = 0; i < nList.size(); i++){
                if(i==nList.size()-1){
                    str+= StringUtils.join("'"+nList.get(i)+"'", "");
                }else{
                    str+= StringUtils.join("'"+nList.get(i)+"'", ",");
                }

            }
            str+= ")";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from ieai_tools_agent_ins where  igroup_tools_id=? " + str;
            ps = conn.prepareStatement(sql);
            ps.setLong(1, toolsId);
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "deleAgentIns", log);
        }
    }

    public void deleAgentInsAll(Long toolsId) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from ieai_tools_agent_ins where  igroup_tools_id=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, toolsId);
            ps.executeUpdate();
            conn.commit();

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "deleAgentInsAll", log);
        }
    }

    /**
     * 组合工具审核前校验
     * @param iid
     * @return
     */
    public int getToolsDetailCheck(Long iid,int type) throws RepositoryException {
        int runNum = 0;
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select count(*) as count from ieai_tools_info where iid = ? and ITOOL_STATUS_ID in(0,3,4,5)";
        //查询待审批工具
        if(type==2) sql = "select count(*) as count from ieai_tools_info where iid = ? and ITOOL_STATUS_ID=1";
        //查询引用的工具是不是脚本工具，如果是脚本工具是不是未审批的
        if(type==3) sql = "select count(*) as count from ieai_tools_info where iid = ? and ITOOL_STATUS_ID !=2 and ITOOL_TYPE_ID=3";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
                runNum = rs.getInt("count");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getToolsDetailCheck", log);
        }

        return runNum;
    }

    /**
     * 批量修改工具状态
     * @param list
     * @throws RepositoryException
     */
    public void updateToolsDownAll(Connection conn,List<Long> list,Long status) throws RepositoryException {
        PreparedStatement updatePs = null;
        String whereSql = "";
        /*提交只针对(已审批和待审批的）改为待确认*/
        if(status==4){
            whereSql = " and itool_status_id in(1,2) ";
        }
        try {
            String removeSql = "UPDATE ieai_tools_info SET itool_status_id=? WHERE iid = ? " + whereSql;
            updatePs = conn.prepareStatement(removeSql);
            for (int i = 0; i < list.size(); i++) {
                updatePs.setLong(1, status);
                updatePs.setLong(2, (Long) list.get(i));
                updatePs.addBatch();
            }
            updatePs.executeBatch();
//            conn.commit();
        } catch (Exception e) {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally {
            DBResource.closePreparedStatement( updatePs, "updateToolsDownAll", log);
        }
    }

    /**
     * 根据id查询自身数据
     * @param iid
     * @return
     */
    public ToolsModel getComboToolsById(Long iid) throws RepositoryException {
        ToolsModel tm = new ToolsModel();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = " SELECT IID, ITHREETYPE_ID, ISYSTEM_ID, ITOOL_CODE, ITOOL_NAME, IUSER_TYPE, IUSER_NAME, ITOOL_TYPE_ID, ITOOL_SCRIPT_TYPE, ITOOL_SCRIPT_NAME, ITOOL_STATUS_ID, ITOOL_RETURN_CAUSE, ITOOL_KEYWORD, ITOOL_MATCH_IP, ITOOL_DESCRIBE, N_OSTYPE, IONETYPE_ID, ITWOTYPE_ID, ITOOL_SCRIPT_ID, ICREATETIME, ITOOL_AUDITOR_ID, ITYPEID, IVERSION, DAILYTYPE, ISYSTEMTYPEUUID, ISWHITEANDIP, IREMARKS, IEDITUSER, CHILDIDS, IPROJECTNAME,ITOOL_FIRST_DESCRIBE FROM IEAI_TOOLS_INFO WHERE IID=? ";

            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                tm.setIid(rs.getLong("IID"));
                tm.setIthreetypeId(rs.getLong("ITHREETYPE_ID"));
                tm.setIsystemId(rs.getLong("ISYSTEM_ID"));
                tm.setItoolCode(rs.getString("ITOOL_CODE"));
                tm.setItoolName(rs.getString("ITOOL_NAME"));
                tm.setIuserType(rs.getString("IUSER_TYPE"));
                tm.setIuserName(rs.getString("IUSER_NAME"));
                tm.setItoolTypeId(rs.getLong("ITOOL_TYPE_ID"));
                tm.setItoolScriptType(rs.getString("ITOOL_SCRIPT_TYPE"));
                tm.setItoolScriptName(rs.getString("ITOOL_SCRIPT_NAME"));
                tm.setItoolStatusId(rs.getLong("ITOOL_STATUS_ID"));
                //tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));
                if (JudgeDB.IEAI_DB_TYPE == 3) {
                    if (rs.getString("ITOOL_DESCRIBE") != null && !rs.getString("ITOOL_DESCRIBE").equals("")) {
                        byte[] describe = rs.getBytes("ITOOL_DESCRIBE");
                        String result = new String(describe, "UTF-8");
                        tm.setItoolDescribe(result);
                    } else {

                        tm.setItoolDescribe(FileUtils.ClobToString(rs.getClob("ITOOL_DESCRIBE")));
                    }
                    if (rs.getString("ITOOL_FIRST_DESCRIBE") != null && !rs.getString("ITOOL_FIRST_DESCRIBE").equals("")) {
                        byte[] toolfirstdescribe = rs.getBytes("ITOOL_FIRST_DESCRIBE");
                        String firstdescriberesult = new String(toolfirstdescribe, "UTF-8");
                        tm.setItoolFirstDescribe(firstdescriberesult);
                    } else {

                        tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                    }
                    if (rs.getString("ITOOL_RETURN_CAUSE") != null && !rs.getString("ITOOL_RETURN_CAUSE").equals("")) {
                        byte[] toolreturn = rs.getBytes("ITOOL_RETURN_CAUSE");
                        String returnresult = new String(toolreturn, "UTF-8");
                        tm.setItoolReturnCause(returnresult);
                    } else {

                        tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));// 回退原因
                    }

                } else {
                    tm.setItoolDescribe(FileUtils.ClobToString(rs.getClob("ITOOL_DESCRIBE")));
                    tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                    tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));// 回退原因
                }
                tm.setItoolKeyword(rs.getString("ITOOL_KEYWORD"));
                tm.setItoolMatchIp(rs.getString("ITOOL_MATCH_IP"));
                //tm.setItoolDescribe(rs.getString("ITOOL_DESCRIBE"));
                tm.setnOsType(rs.getString("N_OSTYPE"));
                tm.setIonetypeId(rs.getLong("IONETYPE_ID"));
                tm.setItwotypeId(rs.getLong("ITWOTYPE_ID"));
                tm.setItoolScriptId(rs.getString("ITOOL_SCRIPT_ID"));
                tm.setIcreatetime(rs.getString("ICREATETIME"));
                tm.setItoolAuditorId(rs.getLong("ITOOL_AUDITOR_ID"));
                tm.setItypeId(rs.getLong("ITYPEID"));
                tm.setIversion(rs.getLong("IVERSION"));
                tm.setDailyType(rs.getLong("DAILYTYPE"));
                tm.setIsystemTypeuuid(rs.getString("ISYSTEMTYPEUUID"));
                tm.setIswhiteandip(rs.getString("ISWHITEANDIP"));
                tm.setIremarks(rs.getString("IREMARKS"));
                tm.setIeditUser(rs.getLong("IEDITUSER"));
                tm.setChildIds(rs.getString("CHILDIDS"));
                //tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
            }

        } catch (Exception e)
        {
            log.error("ComboToolsManager.getComboToolsById is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getComboToolsById", log);
        }
        return tm;
    }

    public String getRunStatus(String iid) {
        String runStatus="";
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;

        String str="nvl";
        if(JudgeDB.IEAI_DB_TYPE == 3){
            str="ifnull";
        }
        String sql = "SELECT "+str+"(RUN_STATUS, 1) as run_status,PARENTID " +
                "  FROM IEAI_TOOLS_RESULT " +
                " WHERE  itool_id = ? " +
                "   and iexc_time = (select MAX(IEXC_TIME) " +
                "                      from IEAI_TOOLS_RESULT t " +
                "                     where itool_id = ? and PARENTID='-1')";


        //查询历史表中 主的组合工具运行状态
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, iid);
            ps.setString(2, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                runStatus=rs.getString("RUN_STATUS");
                if(StringUtils.isNotEmpty(runStatus)&&runStatus.equals("1")){
                    String parentId=rs.getString("PARENTID");
                    if(StringUtils.isNotEmpty(parentId)){
                        String sql1="SELECT "+str+"(RUN_STATUS, 1) as RUN_STATUS,PARENTID  FROM IEAI_TOOLS_RESULT  WHERE  iid = ? ";
                        ps1 = conn.prepareStatement(sql1);
                        ps1.setString(1, parentId);
                        rs1 = ps1.executeQuery();
                        while (rs1.next()){
                            runStatus=rs1.getString("RUN_STATUS");
                        }
                    }
                }
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs1, ps1, "getRunStatus", log);
            DBResource.closeConn(conn, rs, ps, "getRunStatus", log);
        }
        return runStatus;
    }

    /**
     * 获取xml信息
     * @param iid
     * @return
     */
    public Map<String, Object> getToolsXml(Connection conn,long iid) {
        Map<String,Object> mp=new HashMap<>();
        PreparedStatement ps = null;
//        Connection conn = null;
        ResultSet rs = null;
        String sql = "SELECT fclob.iid AS fclobiid, fclob.icontent AS icontent FROM ieai_project_property_info ppi " +
                " LEFT JOIN ieai_studio_flow_xml fxml ON ppi.iid = fxml.flowid LEFT JOIN ieai_webstudio_flow_clob fclob ON fclob.iid = fxml.xmlid " +
                " WHERE ppi.iprojectid =? and ppi.iname='flowTools'";
        try
        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                mp.put("iid",iid);
                mp.put("fclobiid",rs.getString("fclobiid"));
                mp.put("icontent",rs.getString("icontent"));

            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, "getToolsXml", log);
        }
        return mp;
    }


    /**
     * 更新替换后的xml内容
     * @param fclobiid
     * @param icontent
     */
    public void updateXml(Connection conn,String fclobiid, String icontent) throws RepositoryException {
//        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            String sql = "UPDATE ieai_webstudio_flow_clob SET icontent=? WHERE iid=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, icontent);
            ps.setString(2, fclobiid);
            ps.executeUpdate();
//            conn.commit();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, "updateXml", log);
        }
    }

    public List<Map<String, Object>> getAllstatus(String resultId) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select CELLNAME ,RUN_STATUS from IEAI_TOOLS_RESULT where parentid=? order by iid asc";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, resultId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("iactname",rs.getString("CELLNAME"));
                mp.put("istate",rs.getString("RUN_STATUS"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getAllstatus", log);
        }
        return list;
    }

    public List<Map<String, Object>> getComAllstatus(String resultId, String cellName) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        Connection conn = null;
        ResultSet rs = null;
        ResultSet rs2 = null;
        String sql = "select CELLNAME ,RUN_STATUS from IEAI_TOOLS_RESULT where parentid=(select iid from IEAI_TOOLS_RESULT where parentid = ? and cellname='-1' ) order by iid asc ";
        try
        {
            String ssql = "select iid from IEAI_TOOLS_RESULT where parentid=? and cellname=? order by iid asc";
            try
            {
                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
                ps2 = conn.prepareStatement(ssql);
                ps2.setString(1, resultId);
                ps2.setString(2, cellName);
                rs2 = ps2.executeQuery();
                String iid = "";
                while (rs2.next())
                {
                    Map<String,Object> mp=new HashMap<>();
                    iid = rs2.getString("iid");
                }
                ps = conn.prepareStatement(sql);
                ps.setString(1, iid);
                rs = ps.executeQuery();
                while (rs.next())
                {
                    Map<String,Object> mp=new HashMap<>();
                    mp.put("iactname",rs.getString("CELLNAME"));
                    mp.put("istate",rs.getString("RUN_STATUS"));
                    list.add(mp);
                }
            } catch (Exception e)
            {
                log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
                e.printStackTrace();
            } finally
            {
                DBResource.closePSRS(rs2, ps2, "getAllstatus", log);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getComAllstatus", log);
        }
        return list;
    }

    /**
     * 查询工作流信息
     * @param iid
     * @return
     */
    public ProjectPropertyInfo getProjectPropertyInfo(Long iid, String iname) throws RepositoryException {
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        ProjectPropertyInfo projectPropertyInfo = new ProjectPropertyInfo();
        try
        {
            String sql = "select iid,iprojectid,iname from ieai_project_property_info where iprojectid=? and iname=?";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setString(2, iname);
            rs = ps.executeQuery();
            while (rs.next())
            {
                projectPropertyInfo.setIid(rs.getLong("iid"));
                projectPropertyInfo.setIprojectid(rs.getLong("iprojectid"));
                projectPropertyInfo.setINAME(rs.getString("iname"));
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getProjectPropertyInfo", log);
        }
        return projectPropertyInfo;
    }

    /**
     * 查询数据是否存在
     * @param iid
     * @return
     */
    public int getToolsInfoCount(Long iid) {
        int runNum = 0;
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select count(*) as count from ieai_tools_info where iid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
                runNum = rs.getInt("count");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getToolsInfoCount", log);
        }

        return runNum;
    }

    /**
     * 查询xml信息
     * @param iid
     * @return
     */
    public Map<String, Object> getIeaiStudioFlowXml(long iid) {
        Map<String,Object> map=new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = " select iid,flowid,xmlid from ieai_studio_flow_xml where flowid=? ";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("iid",rs.getLong("iid"));
                map.put("flowid",rs.getLong("flowid"));
                map.put("xmlid",rs.getLong("xmlid"));
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getIeaiStudioFlowXml", log);
        }
        return map;
    }

    /**
     * 删除未保存工具的工作流相关信息
     * @param iid
     */
    public void delWorkflow(Long iid) {
        Map<String,Object> map=new HashMap<>();
        PreparedStatement ps = null;
        PreparedStatement dps1 = null;
        PreparedStatement dps2 = null;
        PreparedStatement dps3 = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = " select p.iid as pid,f.iid as fid,c.iid as cid from ieai_project_property_info p left join ieai_studio_flow_xml f on f.flowid=p.iid \n" +
                "left join ieai_webstudio_flow_clob c on c.iid = f.xmlid where p.iprojectid=? and p.iname='flowTools'";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("pid",rs.getLong("pid"));
                map.put("fid",rs.getLong("fid"));
                map.put("cid",rs.getLong("cid"));

            }
            if(map.get("pid")!=null && !"".equals( map.get("pid"))){
                String sql1 = "delete from ieai_project_property_info where iid = ? and iname='flowTools'";
                dps1 = conn.prepareStatement(sql1);
                dps1.setLong(1, (Long) map.get("pid"));
                dps1.executeUpdate();
                conn.commit();
            }

            if(map.get("fid")!=null && !"".equals( map.get("fid"))) {
                String sql2 = "delete from ieai_studio_flow_xml where iid = ?";
                dps2 = conn.prepareStatement(sql2);
                dps2.setLong(1, (Long) map.get("fid"));
                dps2.executeUpdate();
                conn.commit();
            }
            if(map.get("cid")!=null && !"".equals( map.get("cid"))) {
                String sql3 = "delete from ieai_webstudio_flow_clob where iid = ?";
                dps3 = conn.prepareStatement(sql3);
                dps3.setLong(1, (Long) map.get("cid"));
                dps3.executeUpdate();
                conn.commit();
            }

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePreparedStatement(dps3,"delWorkflow--> ieai_webstudio_flow_clob",log);
            DBResource.closePreparedStatement(dps2,"delWorkflow--> ieai_studio_flow_xml",log);
            DBResource.closePreparedStatement(dps1,"delWorkflow--> ieai_project_property_info",log);
            DBResource.closeConn(conn, rs, ps, "delWorkflow", log);
        }
    }

    /**
     * 查询节点是否有选中的agent
     * @param iid
     * @param ititleName
     * @return
     */
    public int getieaiToolsAgentInsCount(Long iid,String ititleName) {
        int runNum = 0;
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select count(*) as count from ieai_tools_agent_ins where igroup_tools_id=? and ititle_name=? ";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setString(2, ititleName);
            rs = ps.executeQuery();
            while (rs.next())
                runNum = rs.getInt("count");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getieaiToolsAgentInsCount", log);
        }

        return runNum;
    }

    /**
     * 删除工具信息
     * @param toolsId
     */
    public void delTools(Long toolsId) {
        Map<String,Object> map=new HashMap<>();
        PreparedStatement ps = null;
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            String sql = "delete from ieai_tools_info where iid = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, toolsId);
            ps.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSConn(conn, ps, "delTools", log);
        }
    }

    public List<Map<String, String>> refreshData() {
        List<Map<String,String>> list=new ArrayList<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select iid,icontent from ieai_webstudio_flow_clob where icontent like '%itoolCode=%'";

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,String> mp=new HashMap<>();
                mp.put("iid",rs.getString("iid"));
                mp.put("icontent",rs.getString("icontent"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "refreshData", log);
        }
        return list;
    }

    public Map<String, Object> getSysId(String code) {
        Map<String, Object> map = new HashMap<>();
        String isysId="";
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select isystem_id,itool_code,itool_name from ieai_tools_info where iid="+code;
//        System.out.println("SQL......."  + sql);
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("isystemId",rs.getString("isystem_id"));
                map.put("itoolCode",rs.getString("itool_code"));
                map.put("itoolName",rs.getString("itool_name"));
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getSysId", log);
        }
        return map;
    }

    /**
     * 查询调用的工具是否存在未审核的
     * @param idlist
     * @return
     */
    public int getQueryToolsCount(List idlist) {
        int runNum = 0;
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = " select count(1) as count from ieai_tools_info where itool_Status_Id not in (1,2) and iid in ('"+ StringUtils.join(idlist, "','")+"')";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
                runNum = rs.getInt("count");
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getQueryToolsCount", log);
        }

        return runNum;
    }

    public List<Map<String,String>> getQueryToolsList(List idlist) {
        List<Map<String,String>> list=new ArrayList<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = " select itool_code,itool_name from ieai_tools_info where itool_status_id not in (1,2) and iid in ('"+ StringUtils.join(idlist, "','")+"')";

        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,String> mp=new HashMap<>();
                mp.put("itoolCode",rs.getString("itool_code"));
                mp.put("itoolName",rs.getString("itool_name"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getQueryToolsList", log);
        }
        return list;
    }

    /**
     * 更新工具下所有引用工具
     * @param list
     * @param str
     * @throws RepositoryException
     */
    public void updateToolsDownChildsAll(Connection conn,List<Long> list, String str) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        String sql = "UPDATE IEAI_TOOLS_INFO SET ITOOL_STATUS_ID = 3 ,itool_return_cause =? WHERE IID = ?";
        try
        {
            ps = conn.prepareStatement(sql);
            for (int i = 0; i < list.size(); i++)
            {
                ps.setString(1, str);
                ps.setLong(2, list.get(i));
                ps.addBatch();
            }
            ps.executeBatch();
//            conn.commit();
        } catch (Exception e)
        {
            log.error(method + " is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, method, log);
        }
    }

    /**
     * 工具交付
     * @param ids
     * @param status
     * @param returnreason
     * @throws RepositoryException
     */
    public void deliveryComboTools ( String[] ids ,String status,String returnreason,String userId) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        try
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String now = sdf.format(new Date());
            Date time = sdf.parse(now);
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);

            if(status.equals("3")){
                String sql1 =  "update  IEAI_TOOLS_INFO  set IDELIVERY_STATUS = ?,IRETURN_REASON = ? where iid =?";
                for(String iid:ids){
                    ps = conn.prepareStatement(sql1);
                    ps.setString(1, status);
                    ps.setString(2, returnreason);
                    ps.setString(3, iid);
                    ps.executeUpdate();
                    conn.commit();
                }
            }else if(status.equals("2")){
                String sql2 ="update  IEAI_TOOLS_INFO  set IDELIVERY_STATUS = ?, IRECEPTION_AUDITOR=?, IRECEPTION_TIME= '" + time.getTime() + "'  where iid =?";
                for(String iid:ids){
                    ps = conn.prepareStatement(sql2);
                    ps.setString(1, status);
                    ps.setString(2, userId);
                    ps.setString(3, iid);
                    ps.executeUpdate();
                    conn.commit();
                }
            }else{
                String sql = "update  IEAI_TOOLS_INFO  set IDELIVERY_STATUS = ?, IDELIVERY_AUDITOR=?,IRETURN_REASON = ?  where iid =?";
                for(String iid:ids){
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, status);
                    ps.setString(2, userId);
                    ps.setString(3, "");
                    ps.setString(4, iid);
                    ps.executeUpdate();
                    conn.commit();
                }
            }

        } catch (Exception e)
        {
            log.error("deliveryComboTools" + " is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSConn(conn, ps, "deliveryComboTools", log);
        }
    }

    public List<Map<String, Object>> getToolsParamIns(long igroupToolsId, long itoolsId, String ititleName) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;

        String sql = "select IID, IGROUP_TOOLS_ID, ITOOLS_ID, IPARAM_NUM, IPARAM_NAME, IPARAM_VALUE, IPARAM_DESCRIBE, IPARAM_VALUE_INS, ITITLE_NAME, IPARAM_IID" +
                " from IEAI_TOOLS_PARAM_INS where IGROUP_TOOLS_ID = ? and ITOOLS_ID=? and ITITLE_NAME=?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, igroupToolsId);
            ps.setLong(2, itoolsId);
            ps.setString(3,ititleName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("iid",rs.getString("IID"));
                mp.put("igroupToolsId",rs.getString("IGROUP_TOOLS_ID"));
                mp.put("itoolsId",rs.getString("ITOOLS_ID"));
                mp.put("iparamNum",rs.getString("IPARAM_NUM"));
                mp.put("iparamName",rs.getString("IPARAM_NAME"));
                mp.put("iparamValue",rs.getString("IPARAM_VALUE"));
                mp.put("iparamDescribe",rs.getString("IPARAM_DESCRIBE"));
                mp.put("iparamValueIns",rs.getString("IPARAM_VALUE_INS"));
                mp.put("ititleName",rs.getString("ITITLE_NAME"));
                mp.put("iparamIid",rs.getString("IPARAM_IID"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getQueryToolsList", log);
        }
        return list;
    }

    public List<Map<String, Object>> getToolsAgentIns(long igroupToolsId, long itoolsId, String ititleName) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;

        String sql = "select IID, IGROUP_TOOLS_ID, ITOOLS_ID, IAGENT_ID, IUSER_NAME, ITITLE_NAME from IEAI_TOOLS_AGENT_INS where IGROUP_TOOLS_ID = ? and ITOOLS_ID=? and ITITLE_NAME=?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, igroupToolsId);
            ps.setLong(2, itoolsId);
            ps.setString(3,ititleName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("iid",rs.getString("IID"));
                mp.put("igroupToolsId",rs.getString("IGROUP_TOOLS_ID"));
                mp.put("itoolsId",rs.getString("ITOOLS_ID"));
                mp.put("iagentId",rs.getString("IAGENT_ID"));
                mp.put("iuserName",rs.getString("IUSER_NAME"));
                mp.put("ititleName",rs.getString("ITITLE_NAME"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getQueryToolsList", log);
        }
        return list;
    }

    /**
     * 查询组合工具本身的节点
     * @param iid
     * @return
     */
    public String getChildids(Long iid) {
        String str="";
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sql = "select CHILDIDS from IEAI_TOOLS_INFO WHERE IID = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                str=rs.getString("CHILDIDS");
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getChildids", log);
        }
        return str;
    }

    /**
     * 查询组合工具是否被引用
     * @param aLong
     * @return
     */
    public List<Map<String, Object>> getCallChildids(Connection conn,Long aLong) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
//        Connection conn = null;
        ResultSet rs = null;
        String str="\"childId\":\""+aLong+"\"";
        String sql = "select IID,CHILDIDS from IEAI_TOOLS_INFO WHERE CHILDIDS LIKE  '%"+ str +"%'";
        try
        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("iid",rs.getString("IID"));
                mp.put("childids",rs.getString("CHILDIDS"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCallChildids", log);
        }
        return list;
    }

//    public List<Map<String, Object>> getAgentIns(Long zid, Long jid, String title) {
//        List<Map<String,Object>> list=new ArrayList<>();
//        PreparedStatement ps = null;
//        Connection conn = null;
//        ResultSet rs = null;
//
//        String sql = "select IID, IGROUP_TOOLS_ID, ITOOLS_ID, IAGENT_ID, IUSER_NAME, ITITLE_NAME from IEAI_TOOLS_AGENT_INS where IGROUP_TOOLS_ID=? and ITOOLS_ID=? and ITITLE_NAME=?";
//        try
//        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
//            ps = conn.prepareStatement(sql);
//            ps.setLong(1,zid);
//            ps.setLong(2,jid);
//            ps.setString(3,title);
//            rs = ps.executeQuery();
//            while (rs.next())
//            {
//                Map<String,Object> mp=new HashMap<>();
//                mp.put("iid",rs.getString("IID"));
//                mp.put("igroupToolsId",rs.getString("IGROUP_TOOLS_ID"));
//                mp.put("itoolsId",rs.getString("ITOOLS_ID"));
//                mp.put("iagentId",rs.getString("IAGENT_ID"));
//                mp.put("iuserName",rs.getString("IUSER_NAME"));
//                mp.put("ititleName",rs.getString("ITITLE_NAME"));
//                list.add(mp);
//            }
//        } catch (Exception e)
//        {
//            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
//            e.printStackTrace();
//        } finally
//        {
//            DBResource.closeConn(conn, rs, ps, "getAgentIns", log);
//        }
//        return list;
//    }

    /**
     *
     * @param zid
     * @param jid
     * @param title
     * @return
     */
//    public List<Map<String, Object>> getParamIns(Long zid, Long jid, String title) {
//        List<Map<String,Object>> list=new ArrayList<>();
//        PreparedStatement ps = null;
//        Connection conn = null;
//        ResultSet rs = null;
//
//        String sql = "select IID, IGROUP_TOOLS_ID, ITOOLS_ID, IPARAM_NUM, IPARAM_NAME, IPARAM_VALUE, IPARAM_DESCRIBE, IPARAM_VALUE_INS, ITITLE_NAME, IPARAM_IID from IEAI_TOOLS_PARAM_INS where IGROUP_TOOLS_ID=? and ITOOLS_ID=? and ITITLE_NAME=?";
//        try
//        {
//            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TOOLS);
//            ps = conn.prepareStatement(sql);
//            ps.setLong(1,zid);
//            ps.setLong(2,jid);
//            ps.setString(3,title);
//            rs = ps.executeQuery();
//            while (rs.next())
//            {
//                Map<String,Object> mp=new HashMap<>();
//                mp.put("iid",rs.getString("IID"));
//                mp.put("igroupToolsId",rs.getString("IGROUP_TOOLS_ID"));
//                mp.put("itoolsId",rs.getString("ITOOLS_ID"));
//                mp.put("iparamNum",rs.getString("IPARAM_NUM"));
//                mp.put("iparamName",rs.getString("IPARAM_NAME"));
//                mp.put("iparamValue",rs.getString("IPARAM_VALUE"));
//                mp.put("iparamDescribe",rs.getString("IPARAM_DESCRIBE"));
//                mp.put("ititleName",rs.getString("ITITLE_NAME"));
//                mp.put("iparamValueIns",rs.getString("IPARAM_VALUE_INS"));
//                mp.put("iparamIid",rs.getString("IPARAM_IID"));
//                list.add(mp);
//            }
//        } catch (Exception e)
//        {
//            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
//            e.printStackTrace();
//        } finally
//        {
//            DBResource.closeConn(conn, rs, ps, "getParamIns", log);
//        }
//        return list;
//    }

    /***********************************************************************************************/
    public ToolsModel getComboToolsByIdCheck(Connection conn, Long iid) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        ToolsModel tm = new ToolsModel();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = " SELECT IID, ITHREETYPE_ID, ISYSTEM_ID, ITOOL_CODE, ITOOL_NAME, IUSER_TYPE, IUSER_NAME, ITOOL_TYPE_ID, ITOOL_SCRIPT_TYPE, ITOOL_SCRIPT_NAME, ITOOL_STATUS_ID, ITOOL_RETURN_CAUSE, ITOOL_KEYWORD, ITOOL_MATCH_IP, ITOOL_DESCRIBE, N_OSTYPE, IONETYPE_ID, ITWOTYPE_ID, ITOOL_SCRIPT_ID, ICREATETIME, ITOOL_AUDITOR_ID, ITYPEID, IVERSION, DAILYTYPE, ISYSTEMTYPEUUID, ISWHITEANDIP, IREMARKS, IEDITUSER, CHILDIDS, IPROJECTNAME,ITOOL_FIRST_DESCRIBE FROM IEAI_TOOLS_INFO WHERE IID=? ";

            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                tm.setIid(rs.getLong("IID"));
                tm.setIthreetypeId(rs.getLong("ITHREETYPE_ID"));
                tm.setIsystemId(rs.getLong("ISYSTEM_ID"));
                tm.setItoolCode(rs.getString("ITOOL_CODE"));
                tm.setItoolName(rs.getString("ITOOL_NAME"));
                tm.setIuserType(rs.getString("IUSER_TYPE"));
                tm.setIuserName(rs.getString("IUSER_NAME"));
                tm.setItoolTypeId(rs.getLong("ITOOL_TYPE_ID"));
                tm.setItoolScriptType(rs.getString("ITOOL_SCRIPT_TYPE"));
                tm.setItoolScriptName(rs.getString("ITOOL_SCRIPT_NAME"));
                tm.setItoolStatusId(rs.getLong("ITOOL_STATUS_ID"));
                tm.setItoolKeyword(rs.getString("ITOOL_KEYWORD"));
                tm.setItoolMatchIp(rs.getString("ITOOL_MATCH_IP"));
                tm.setnOsType(rs.getString("N_OSTYPE"));
                tm.setIonetypeId(rs.getLong("IONETYPE_ID"));
                tm.setItwotypeId(rs.getLong("ITWOTYPE_ID"));
                tm.setItoolScriptId(rs.getString("ITOOL_SCRIPT_ID"));
                tm.setIcreatetime(rs.getString("ICREATETIME"));
                tm.setItoolAuditorId(rs.getLong("ITOOL_AUDITOR_ID"));
                tm.setItypeId(rs.getLong("ITYPEID"));
                tm.setIversion(rs.getLong("IVERSION"));
                tm.setDailyType(rs.getLong("DAILYTYPE"));
                tm.setIsystemTypeuuid(rs.getString("ISYSTEMTYPEUUID"));
                tm.setIswhiteandip(rs.getString("ISWHITEANDIP"));
                tm.setIremarks(rs.getString("IREMARKS"));
                tm.setIeditUser(rs.getLong("IEDITUSER"));
                tm.setChildIds(rs.getString("CHILDIDS"));

                if (JudgeDB.IEAI_DB_TYPE == 3) {
                    if(rs.getBytes("ITOOL_DESCRIBE")!=null){
                        byte[] tooldescribe = rs.getBytes("ITOOL_DESCRIBE");
                        String itooldescribe = new String(tooldescribe, "UTF-8");
                        tm.setItoolDescribe(itooldescribe);
                    }else{
                        tm.setItoolDescribe("");
                    }
                    if(rs.getBytes("ITOOL_RETURN_CAUSE")!=null){
                        byte[] itoolReturnCause = rs.getBytes("ITOOL_RETURN_CAUSE");
                        String itoolReturnCauses = new String(itoolReturnCause, "UTF-8");
                        tm.setItoolReturnCause(itoolReturnCauses);
                    }else{
                        tm.setItoolReturnCause("");
                    }
                    if(rs.getBytes("ITOOL_FIRST_DESCRIBE")!=null){
                        byte[] itoolFirstDescribe = rs.getBytes("ITOOL_FIRST_DESCRIBE");
                        String itoolFirstDescribes = new String(itoolFirstDescribe, "UTF-8");
                        tm.setItoolFirstDescribe(itoolFirstDescribes);
                    }else{
                        tm.setItoolFirstDescribe("");
                    }
                }else{
                    tm.setItoolDescribe(rs.getString("ITOOL_DESCRIBE"));
                    tm.setItoolReturnCause(rs.getString("ITOOL_RETURN_CAUSE"));
                    tm.setItoolFirstDescribe(rs.getString("ITOOL_FIRST_DESCRIBE"));
                }
            }

        } catch (Exception e)
        {
            log.error("ComboToolsManager.getComboToolsById is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, method, log);
        }
        return tm;
    }

    public List<Map<String, Object>> getAgentInsCheck(Connection conn,Long zid, Long jid, String title) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select IID, IGROUP_TOOLS_ID, ITOOLS_ID, IAGENT_ID, IUSER_NAME, ITITLE_NAME from IEAI_TOOLS_AGENT_INS where IGROUP_TOOLS_ID=? and ITOOLS_ID=? and ITITLE_NAME=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1,zid);
            ps.setLong(2,jid);
            ps.setString(3,title);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("iid",rs.getString("IID"));
                mp.put("igroupToolsId",rs.getString("IGROUP_TOOLS_ID"));
                mp.put("itoolsId",rs.getString("ITOOLS_ID"));
                mp.put("iagentId",rs.getString("IAGENT_ID"));
                mp.put("iuserName",rs.getString("IUSER_NAME"));
                mp.put("ititleName",rs.getString("ITITLE_NAME"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return list;
    }

    /**
     *
     * @param zid
     * @param jid
     * @param title
     * @return
     */
    public List<Map<String, Object>> getParamInsCheck(Connection conn,Long zid, Long jid, String title) {
        List<Map<String,Object>> list=new ArrayList<>();
        PreparedStatement ps = null;
        ResultSet rs = null;

        String sql = "select IID, IGROUP_TOOLS_ID, ITOOLS_ID, IPARAM_NUM, IPARAM_NAME, IPARAM_VALUE, IPARAM_DESCRIBE, IPARAM_VALUE_INS, ITITLE_NAME, IPARAM_IID from IEAI_TOOLS_PARAM_INS where IGROUP_TOOLS_ID=? and ITOOLS_ID=? and ITITLE_NAME=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1,zid);
            ps.setLong(2,jid);
            ps.setString(3,title);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String,Object> mp=new HashMap<>();
                mp.put("iid",rs.getString("IID"));
                mp.put("igroupToolsId",rs.getString("IGROUP_TOOLS_ID"));
                mp.put("itoolsId",rs.getString("ITOOLS_ID"));
                mp.put("iparamNum",rs.getString("IPARAM_NUM"));
                mp.put("iparamName",rs.getString("IPARAM_NAME"));
                mp.put("iparamValue",rs.getString("IPARAM_VALUE"));
                mp.put("iparamDescribe",rs.getString("IPARAM_DESCRIBE"));
                mp.put("ititleName",rs.getString("ITITLE_NAME"));
                mp.put("iparamValueIns",rs.getString("IPARAM_VALUE_INS"));
                mp.put("iparamIid",rs.getString("IPARAM_IID"));
                list.add(mp);
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return list;
    }

    public Map<String, Object> getSysIdCheck(Connection conn,String code) {
        Map<String, Object> map = new HashMap<>();
        String isysId="";
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select isystem_id,itool_code,itool_name from ieai_tools_info where iid="+code;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("isystemId",rs.getString("isystem_id"));
                map.put("itoolCode",rs.getString("itool_code"));
                map.put("itoolName",rs.getString("itool_name"));
            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return map;
    }

    public String getFlowxmlCheck(Connection conn,Long iid) throws RepositoryException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String str = "";
        String sql = "select fclob.icontent as icontent from ieai_project_property_info ppi " +
                " left join ieai_studio_flow_xml fxml on ppi.iid=fxml.flowid" +
                " left join ieai_webstudio_flow_clob fclob on fclob.iid=fxml.xmlid" +
                " where ppi.iprojectid=? and ppi.iname='flowTools'";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
                str = rs.getString("icontent");

        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return str;
    }

    public Map<String, Object> getToolsXmlCheck(Connection conn,long iid) {
        Map<String,Object> mp=new HashMap<>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "SELECT fclob.iid AS fclobiid, fclob.icontent AS icontent FROM ieai_project_property_info ppi " +
                " LEFT JOIN ieai_studio_flow_xml fxml ON ppi.iid = fxml.flowid LEFT JOIN ieai_webstudio_flow_clob fclob ON fclob.iid = fxml.xmlid " +
                " WHERE ppi.iprojectid =? and ppi.iname='flowTools'";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                mp.put("iid",iid);
                mp.put("fclobiid",rs.getString("fclobiid"));
                mp.put("icontent",rs.getString("icontent"));

            }
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            e.printStackTrace();
        } finally
        {
            DBResource.closePSRS(rs, ps, "getToolsXml", log);
        }
        return mp;
    }

    public void updateXmlCheck(Connection conn,String fclobiid, String icontent) throws RepositoryException {
        PreparedStatement ps = null;
        try
        {
            String sql = "UPDATE ieai_webstudio_flow_clob SET icontent=? WHERE iid=?";
            ps = conn.prepareStatement(sql);
            ps.setString(1, icontent);
            ps.setString(2, fclobiid);
            ps.executeUpdate();
//            conn.commit();
        } catch (Exception e)
        {
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
    }
    
    private final String viwsSQL="SELECT T.ILASTID,  SUM(CASE  WHEN D.SUCCESSTIMES IS NULL THEN  0  ELSE  D.SUCCESSTIMES  END) AS SUCCESSTIMES,  SUM(CASE  WHEN D.TOTALTIMES IS NULL THEN  0  ELSE  D.TOTALTIMES  END) AS TOTALTIMES,  (CASE  WHEN COUNT(D.IID) = 0 OR SUM(D.TOTALTIMES) = 0 THEN  0  ELSE  ROUND(SUM(CASE  WHEN D.SUCCESSTIMES IS NULL THEN  0  ELSE  D.SUCCESSTIMES  END) * 100 / SUM(D.TOTALTIMES))  END) AS SUCCESSRATE  FROM IEAI_SCRIPT_TEST T  LEFT JOIN IEAI_SCRIPT_EXECTIME D  ON T.ISCRIPTUUID = D.SCRIPTUUID  AND T.ISDELETE = 0  GROUP BY T.ILASTID";

}

