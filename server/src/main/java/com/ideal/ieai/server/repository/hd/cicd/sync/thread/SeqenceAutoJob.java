package com.ideal.ieai.server.repository.hd.cicd.sync.thread;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.repository.hd.cicd.project.manager.SusCiCdManager;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 *
 * <ul>
 * <li>Title: SeqenceAutoJob.java</li>
 * <li>Description:定时发送本周投产系统job</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 *
 * <AUTHOR>
 *
 * 2024-5-14
 */
public class SeqenceAutoJob implements Job {
    private static Logger _log   = Logger.getLogger(SeqenceAutoJob.class);
    private static final String CHARSET = "UTF-8";
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException
    {
        int type = Constants.IEAI_SUS;
        try {
            LocalDate curr = LocalDate.now();
            LocalDate startDay = curr.with(DayOfWeek.MONDAY);
            LocalDate endtDay = curr.with(DayOfWeek.SUNDAY);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            String startTime = startDay.atStartOfDay().format(formatter);
            String endTime = endtDay.atTime(23,59).format(formatter);
            String sysName = SusCiCdManager.getInstance().QueryThisWeekSystem(startTime,endTime,type);
            _log.info("定时发送本周投产系统开始....");
            requestPost(sysName);
            _log.info("定时发送本周投产系统结束....");
        } catch (Exception e) {
            _log.error("SeqenceAutoJob execute is error!", e);
        }

    }

    /**
     * 请求接口
     * @param sysName
     */
    private void requestPost(String sysName) throws IOException {
        String url = Environment.getInstance().getSysConfig(com.ideal.ieai.core.Environment.CICD_SYNCSEQUENCE_AUTOSYSTEM_URL, "");
        _log.info("本周投产系统测管url："+url);
        _log.info("本周投产系统测管参数："+sysName);
        String result = doPost(url,sysName);
        _log.info("本周投产系统测管返回结果："+result);

    }

    /**
     * 发送请求
     * @param url
     * @param params
     * @return
     */
    private String doPost(String url, String params) throws IOException {
        DefaultHttpClient httpClient = new DefaultHttpClient();
        HttpPost httpPost = null;
        String result = null;
        httpPost = new HttpPost(url);
        httpPost.addHeader("Content-type", "application/x-www-form-urlencoded; charset=" + CHARSET);
        httpPost.setHeader("Accept", "application/json");
        // 设置参数
        StringEntity entity = new StringEntity("systems="+ URLEncoder.encode(params,CHARSET), CHARSET);
        httpPost.setEntity(entity);
        HttpResponse response = httpClient.execute(httpPost);
        if (response != null)
        {
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null)
            {
                result = EntityUtils.toString(resEntity, CHARSET);
            }
        }
        return result;
    }
}
