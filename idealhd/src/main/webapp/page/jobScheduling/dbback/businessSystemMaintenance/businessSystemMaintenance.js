
/*******************************************************************************
 * 业务系统维护
 ******************************************************************************/
Ext.onReady (function ()
{
	//清理主面板的各种监听时间
	destroyRubbish();
	var isystemName='业务系统名称';
	/** *********************Model********************* */
	/** 系统名下拉框数据Model* */
	Ext.define ('businessSystemMaintenanceModel',
	{
	    extend : 'Ext.data.Model',
	   // remoteSort : true,
	    fields : [
	            {
	                name : 'iid',
	 	            type : 'long'
	            },
	            {
	                name : 'isystemname',
		            type : 'string'
	            },
	            {
	            	name : 'istates',
	                type : 'long'
	            },
	            {
	            	name : 'icomments',
	                type : 'string'
	            }
	    ]
	});
	/** 业务系统级别Model* */
	Ext.define ('syslvModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'applvlId',
	                type : 'long'
	            },
	            {
	                name : 'applvl',
	                type : 'string'
	            }
	    ]
	});
	/** *********************Store********************* */
	
	var businessSystemMaintenanceStore = Ext.create ('Ext.data.Store',
		    {
		        autoLoad : true,
		        autoDestroy : true,
			    remoteSort : true,
		        pageSize : 20,
		        model : 'businessSystemMaintenanceModel',
		        proxy :
		        {
		            type : 'ajax',
		            url : 'businessSystemMaintenanceList.do',
		            reader :
		            {
		                type : 'json',
		                root : 'dataList',
		                totalProperty : 'total'
		            }
		        }
			 });

	
	businessSystemMaintenanceStore.on ('beforeload', function (store, options)
	{
		
		var new_params =
		{
				isystemname : sysNameForQuery.getValue ().trim ()
		};
		Ext.apply (businessSystemMaintenanceStore.proxy.extraParams, new_params);
	});
	
	/** *********************组件********************* */
	/** 查询业务系统* */
	var queryButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '查询',
	    handler : queryWhere
	});
	/** 新建* */
	var addButtonForBSM = Ext.create ("Ext.Button",
			{
			    cls : 'Common_Btn',
			    text : '新建',
			    handler : onAddListener
			});
	/** 保存业务系统* */
	var saveButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '保存',
	    handler : onSaveListener
	});
	/** 删除业务系统* */
	var deleteButtonForBSM = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : '删除',
	 //   disabled : true,
	    handler : onDeleteListener
	});
//	/** 导入业务系统* */
//	var importButtonForBSM = Ext.create ("Ext.Button",
//	{
//	    cls : 'Common_Btn',
//	    text : '导入',
//	    handler : onImportListener
//	});
//	/** 导出业务系统* */
//	var exportButtonForBSM = Ext.create ("Ext.Button",
//	{
//	    cls : 'Common_Btn',
//	    text : '导出',
//	    handler : onExportListener
//	});
	/**导入导出按钮**/
	var importOrExportButtonForBSM = Ext.create("Ext.Button",{
            text: '导入/导出',
            cls:'Common_Btn',
            menu: {
                xtype: 'menu',
                plain: true,
                items: {
                    xtype: 'buttongroup',
                    columns: 2,
                    defaults: {
                        xtype: 'button'
                    },
                    items: [ {
						text : '导入',
						iconCls : 'import_configure',
						cls:'Common_Btn',
						handler : uploadSystem
					},
					{
						text : '导出',
						iconCls : 'server_export',
						cls:'Common_Btn',
						handler : exportSystem
					}]
                }
            }
	});
	/**导出业务系统**/
	function exportSystem(){
		window.location.href = 'exportSystem.do';
	}
	function uploadSystem(){
    	var uploadWindows;
    	var uploadForm = Ext.create('Ext.form.FormPanel',{
        	border : false,
        	items : [{
            	xtype: 'filefield',
    			name: 'fileName', // 设置该文件上传空间的name，也就是请求参数的名字
    			fieldLabel: '选择文件',
    			labelWidth: 80,
    			anchor: '90%',
//    			labelAlign: 'right',
    			margin: '10 10 0 40',
    			buttonText: '浏览',
    			labelStyle:'margin-top:12px;'
            }],
            buttonAlign : 'center',
            buttons :[{
            	text : '确定',
            	handler :upExeclData
            },{
            	text : '取消',
            	handler : function(){
            		uploadWindows.close();
            	}
            }]
        });
        /**
         * Excel导入信息窗体
         */
        uploadWindows = Ext.create('Ext.window.Window', {
    		title : 'Excel导入',
    		layout : 'fit',
    		height : 150,
    		width : 600,
    		modal : true,
    		items : [ uploadForm ],
    		listeners : {
    			close : function(g, opt) {
    				uploadForm.destroy();
    			}
    		}
    	});
        uploadWindows.show();
        function upExeclData(){
        	var form = uploadForm.getForm();
    		var hdupfile=form.findField("fileName").getValue();
    		if(hdupfile==''){
    			Ext.Msg.alert('提示',"请选择文件...");
    			return ;
    		}
    		uploadTemplate(form);

    		 
        }
        var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"导入中..."});
        function uploadTemplate(form) {
        	checkMask.show();
        	uploadWindows.hide();
      	   if (form.isValid()) {
             form.submit({
               url: 'uploadSystemInfo.do',
                 success: function(form, action) {
                 var sumsg = Ext.decode(action.response.responseText).message;
                    Ext.Msg.alert('提示',sumsg);
                    checkMask.hide();
                    queryWhere();
                    return;
                 },
                 failure: function(form, action) {
                     var msg = Ext.decode(action.response.responseText).message;
                       Ext.create('Ext.window.MessageBox', {
                     minHeight : 110,
                     minWidth : 500,
                     resizable : false
                   });
                     Ext.Msg.alert('提示',msg);
                     checkMask.hide();
                     queryWhere();
                   return;
                 }
                 
             });
             
      	   }
      	 
      	 }
    }
	/** 业务系统名查询输入框* */
	var sysNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    margin : '5',
	    // fieldLabel : '业务系统名称',
	    emptyText : '--请输入'+isystemName+'--',
	    labelWidth : 80,
	    width : 350,
	    xtype : 'textfield'
	// padding : '0 10 0 0'
	});


	/** 业务系统列表分页工具栏* */
	var bsPageBar = Ext.create('Ext.PagingToolbar', {
    	pageSize: 20,
        store: businessSystemMaintenanceStore,
        displayInfo: true,
        dock : 'bottom',
        border : false,
        displayMsg: '显示{0}-{1}条，共{2}条',
        emptyMsg: "没有记录",
        baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar'
    })
	var cellEditing = Ext.create ('Ext.grid.plugin.CellEditing',
	{
		clicksToEdit : 2
	});
	/** 业务系统列表Columns* */
	var gridColumns = [
	        {
	            text : '序号',
	            width : 80,
	            xtype : 'rownumberer'
	        },
	        /*{
                text : 'iid',
                dataIndex : 'iid',
                hidden : false
            },*/
	        {
	            text : '业务系统名称',
	            sortable : true,
	            dataIndex : 'isystemname',
	            hideable: false,
	            flex : 2,
	            editor :
	            {
		            allowBlank : true
	            },
                renderer : function (value, metaData, record, colIndex, store, view)
                {
                    metaData.tdAttr = 'data-qtip="' + value + '"';
                    return value;
                
                }
	        },
	        {
	            text : '关联数据库',
	            sortable : true,
	            dataIndex : 'istates',
	            hidden:true,
	            hideable: false, 
	            flex : 1,
	            editor :
	            {
		            allowBlank : true
	            }
	        },
	        {
	            text : '备注',
	            sortable : true,
	            dataIndex : 'icomments',
	            hidden : true,
	            hideable: false, 
	            flex : 1,
	            editor :
	            {
		            allowBlank : true
	            }
	        }
	];

	/** 业务系统列表panel* */
	var grid_panel = Ext.create ('Ext.grid.Panel',
	{
	    region : 'center',
	    plugins : [
		    cellEditing
	    ],
	    store : businessSystemMaintenanceStore,
	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    {
	    checkOnly : true
	    }),
	    margins : grid_margin,
        border : true,
	    columnLines : true,
	    columns : gridColumns,
	    collapsible : false,
	    dockedItems : [
		    {
		        xtype : 'toolbar',
		        items : [
		                sysNameForQuery, queryButtonForBSM,'->',addButtonForBSM,
		                saveButtonForBSM, deleteButtonForBSM,importOrExportButtonForBSM
		        ]
		    }
	    ]
	});
	contentPanel.getHeader().hide();// 设置contentPanel标题头隐藏
	grid_panel.setTitle(contentPanel.title);
	/** 主Panel* */
	var businessSystemMaintenance_mainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "businessSystemMaintenanceDiv",
	    height : contentPanel.getHeight()-modelHeigth,
		width : contentPanel.getWidth(),
		layout : 'border',
		bbar : bsPageBar,
		 bodyCls:'service_platform_bodybg',
	    border : false,
	    items : [
		    grid_panel
	    ]
	});

	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		businessSystemMaintenance_mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		businessSystemMaintenance_mainPanel.setWidth (contentPanel.getWidth());
		win.center();  
	});
	//当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy(businessSystemMaintenance_mainPanel);
    	Ext.destroy(win);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
    });
	/** *********************方法********************* */
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};

	/** 查询业务系统* */
	function queryWhere ()
	{
		bsPageBar.moveFirst ();
	}	/** 保存业务系统* */
	function onSaveListener (btn)
	{
		try
		{
			btn.setDisabled (true);
			var m = businessSystemMaintenanceStore.getModifiedRecords ();
			if (m.length < 1)
			{
				Ext.Msg.alert ('提示', '无需要增加或者修改的数据！');
				return;
			}
			var jsonData = "[";
			for (var i = 0, len = m.length; i < len; i++)
			{
				var isystemname = m[i].get ("isystemname").trim ();
				if ('' == isystemname)
				{
					Ext.Msg.alert ('提示', '业务系统名称不能为空！');
					businessSystemMaintenanceStore.reload();
					return;
				}
				if (fucCheckLength (isystemname) > 200)
				{
					Ext.Msg.alert ('提示', '业务系统名称不能超过200字符！');
					businessSystemMaintenanceStore.reload();
					return;
				}
				
				var ss = Ext.JSON.encode (m[i].data);
				if (i == 0)
					jsonData = jsonData + ss;
				else
					jsonData = jsonData + "," + ss;
			}
			jsonData = jsonData + "]";
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request (
			{
			    url : 'saveBusinessSystemMaintenance.do',
			    timeout: 30000,  
			    params :
			    {
				    incdata : jsonData
			    },
			    method : 'POST',
			    success : function (response, opts)
			    {
				    var success = Ext.decode (response.responseText).success;
				    // 当后台数据同步成功时
				    if (success)
				    {
					    bsPageBar.moveFirst ();
				    }
				    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
			    },
			    failure : function(result, request) {
					secureFilterRs(result,"操作失败！");
				}
			});
		}
		catch (e)
		{
		}
		finally
		{
			btn.setDisabled (false);
		}
	}
	/** 增加业务系统* */
	function onAddListener ()
	{
		var p = Ext.create ('businessSystemMaintenanceModel',
		{
			iid:-1,
			 isystemname : "" ,
             icomments : ""  
		});
		businessSystemMaintenanceStore.insert (0, p);// 在第一个位置插入
		cellEditing.startEdit(0, 2);// 指定的行/列，进行单元格内容的编辑
	}
	
	/** 删除业务系统* */
	function onDeleteListener (btn)
	{
		var record = grid_panel.getSelectionModel().getSelection();
		if (record.length == 0)
		{
			Ext.Msg.alert ('提示', "请先选择您要操作的行!");
			return;
		}
		var goBack = false;// 判断是否需要删除数据库数据
		for (var i = 0; i < record.length; i++)
		{
			if (-1 != record[i].get ('iid'))// 如果预删除数据id不为-1，则需要删除数据库数据
			{
				goBack = true;
				break;
			}
		}
		if (goBack)// 通过数据库删除数据
		{
			Ext.MessageBox.confirm ('提示', "是否删除选中数据!", function (btn)
			{
				if (btn == 'no')
				{
					return;
				}
				if (btn == 'yes')
				{
					
				    Ext.MessageBox.wait("数据处理中...", "进度条");
				    var ids = [];
					Ext.Array.each (record, function (recordObj)
					{
						var cpId = recordObj.get ('iid');
						ids.push(cpId);

					});
					Ext.Ajax.request (
					{
					    url : 'deleteBusinessSystemMaintenance.do',
					    timeout: 30000,  
					    params :
					    {
						    deleteIds : ids.join(',')
					    },
					    method : 'POST',
					    success : function (response, opts)
					    {
						    var success = Ext.decode (response.responseText).success;
						    
						    if (success)
						    {
							    // 当后台数据同步成功时刷新列表
							    bsPageBar.moveFirst ();
						    }
						    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
					    },

					    failure : function(result, request) {
							secureFilterRs(result,"操作失败！");
						}
					});
					
				}
			});
		}
		else
		{
			for (var j = 0; j < record.length; j++)
			{
				// 如果不需要删除数据库数据，则不刷新列表，仅移除store数据
				businessSystemMaintenanceStore.remove (record[j]);
				
			}
		}
	}
});
