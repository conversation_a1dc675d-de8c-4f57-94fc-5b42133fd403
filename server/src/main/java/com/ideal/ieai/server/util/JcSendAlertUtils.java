package com.ideal.ieai.server.util;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.log4j.Logger;

import com.ideal.ieai.server.ieaikernel.ServerEnv;

public class JcSendAlertUtils
{
    private static final Logger _log = Logger.getLogger(JcSendAlertUtils.class);
    private static final JcSendAlertUtils _ins = new JcSendAlertUtils();

    public static JcSendAlertUtils getInstance ()
    {

        return _ins;
    }
    public void sendJcAlert ( String message )
    {
        try
        {
            HttpClient httpClient = new DefaultHttpClient();
            String alertUrl = ServerEnv.getInstance().getSysConfig("jc.alerturl");

            HttpPost httpPost = new HttpPost(alertUrl);
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");

            StringEntity strentity = new StringEntity(message, "utf-8");// 解决中文乱码问题
            strentity.setContentEncoding("UTF-8");
            strentity.setContentType("application/json");
            httpPost.setEntity(strentity);
            HttpResponse response = httpClient.execute(httpPost);

            if (response.getStatusLine().getStatusCode() == 200)
            {
                _log.info("JC发送告警信息成功。");
            } else
            {
                _log.error("JC发送告警信息失败。");
                HttpEntity entity = response.getEntity();
                if (entity != null)
                {
                    _log.info("JC发送告警信息失败，返回信息：" + entity.toString());
                } else
                {
                    _log.info("JC发送告警信息失败。 返回信息null");
                }
            }
        } catch (Exception e)
        {
            _log.error("JC发送告警信息时，发送异常。");
        }
    }
}
