Ext.onReady(function() {
	
	destroyRubbish();
	Ext.define('sysTaskStatModel', {
		extend : 'Ext.data.Model',
		fields : [
/*		          {
			name : 'iid',
			type : 'long'
		}, */
		{
			name : 'systemName',
			type : 'string'
		}, {
			name : 'taskDesc',
			type : 'string'
		}, {
			name : 'startTime',
			type : 'string'
		}, {
			name : 'endTime',
			type : 'string'
		}, {
			name : 'taskCount',
			type : 'string'
		}, {
			name : 'artiCount',
			type : 'string'
		}, {
			name : 'successCount',
			type : 'string'
		}, {
			name : 'failCount',
			type : 'string'
		}]
	});
	
	Ext.define('projectInfoModel', {
		extend : 'Ext.data.Model',
		fields : [
		{
			name : 'prjUpperId',
			type : 'string'
		}, {
			name : 'prjOwnId',
			type : 'string'
		}, {
			name : 'uuid',
			type : 'string'
		}, {
			name : 'prjName',
			type : 'string'
		}, {
			name : 'prjId',
			type : 'string'
		}]
	});
	
	var prjColumn = [ {
		text : '系统名称',
		dataIndex : 'prjName',
		align : 'left',
		flex : 1
	} ];

    
	var columns = [
//	{
//		text : 'iid',
//		dataIndex : 'iid',
//		hidden:true
//	},
	{
		text : '系统名称',
		dataIndex : 'systemName',
		align: 'left',
		flex : 1
	}, {
		text : '批量执行次数',
		dataIndex : 'taskCount',
		align: 'left',
		flex : 1
	}, {
		text : '人工次数',
		dataIndex : 'artiCount',
		align: 'left',
		flex : 1
	}, {
		text : '成功任务总次数',
		dataIndex : 'successCount',
		align: 'left',
		flex : 1
	}, {
		text : '异常任务总次数',
		dataIndex : 'failCount',
		align : 'left',
		flex : 1
	}];
	
	var myMask = new Ext.LoadMask(Ext.getBody(), {
		msg    : "读取中...",
		msgCls : 'z-index:10000;'
	});

	var store = Ext.create('Ext.data.Store', {
		pageSize: 25,
		autoLoad : false,
		model : 'sysTaskStatModel',
		proxy : {
			type : 'ajax',
			url : 'systemTaskStat.do',
			reader : {
				type : 'json',
				root : 'systemTaskList',
				totalProperty: 'totalProperty'
			}
		} 
    
	});
	
	Ext.define('systemTimeActPrjModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'prjUpperId',
			type : 'long'

		}, {
			name : 'prjName',
			type : 'String'
		} ]
	});

	//下拉列表
	var store1 = Ext.create('Ext.data.Store', {
		autoLoad : true,
		sorters :  [{
	         property: 'prjName',
	         direction: 'ASC'
	     }],
		model : 'systemTimeActPrjModel',
		proxy : {
			type : 'ajax',
			url : 'querySystemTimeActProInfo.do',
			reader : {
				type : 'json',
				root : 'projectInfo'
			}
		}

	});

	var pageBar = Ext.create('Ext.PagingToolbar', {
    	pageSize: 25,
        store: store,
        displayInfo: true,
        displayMsg: '显示{0}-{1}条，共{2}条',
        emptyMsg: "没有数据"
    })
    
	var startTime = Ext.create('Go.form.field.DateTime', {
		columnWidth : .21,
		labelAlign : 'right',
		id : 'startTime',
		padding : '5 10 0 0',
		fieldLabel : '开始时间',
//		value : new Date(),
//		maxValue : Ext.Date.add(new Date(), Ext.Date.DAY, 1),
		editable : false,
		name : 'startTime',
		format : 'Y-m-d H:i:s'
	});

	var endTime = Ext.create('Go.form.field.DateTime', {
		columnWidth : .21,
		labelAlign : 'right',
		id : 'endTime',
		padding : '5 10 0 0',
		fieldLabel : '结束时间',
//		value : new Date(),
//		maxValue : Ext.Date.add(new Date(), Ext.Date.DAY, 1),
		editable : false,
		name : 'endTime',
		format : 'Y-m-d H:i:s'
	});
	
	var queryBtn = Ext.create('Ext.Button', {
		columnWidth:.11,
		margin:'5 0 0 50',
        text : '查询',
        handler :function(){
        	var startDate = form.getForm().findField("startTime").getValue();
    		var endDate = form.getForm().findField("endTime").getValue();
    		var dt1 = null;
    		var value1 = null;
    		var dt2 = null;
    		var value2 = null;
			if (startDate == null || startDate == '') {
				Ext.Msg.alert('错误', '开始时间不能为空！');
				return;
			}else if (endDate == null || endDate == '') {
				var curDate = new Date();
				var time=Ext.Date.format(curDate, 'Y-m-d H:i:s');
				form.getForm().findField("endTime").setValue(time);
			}
			
    		if (startDate != null && startDate != '') {
    			dt1 = new Date(startDate);
    			value1 = Date.parse(dt1);
    			if (value1 > Date.parse(new Date)
    					|| value2 > Date.parse(new Date)) {
    				Ext.Msg.alert('错误', '开始时间不能大于当前时间！');
    				return;
    			}
    		}
    		if (endDate != null && endDate != '') {
    			dt2 = new Date(endDate);
    			value2 = Date.parse(dt2);
    			if (value2 > Date.parse(new Date)
    					|| value2 > Date.parse(new Date)) {
    				Ext.Msg.alert('错误', '结束时间不能大于当前时间！');
    				return;
    			}
    		}
    		if (value1 != null && value2 != null) {
    			if (value1 > value2 || value1 == value2) {
    				Ext.Msg.alert('错误', '开始时间不能大于等于结束时间！');
    				return;
    			}
    		}
			pageBar.moveFirst();
        	
        }
	});
	
	var windowPrjName = Ext.create('Ext.form.field.Text', {
		fieldLabel : '系统名称',
		allowBlank : true,
		height : 25,
		width : 300,
		margin : '5 0 5 5',
		flex : 1,
		name : 'windowPrjName',
		editable : true
	});
	
	var windowQueryBtn = Ext.create('Ext.Button', {
		height : 25,
		width : 50,
		margin : '5 0 5 5',
		text : '查询',
		handler : function() {
			var input = windowPrjName.value;
			// 检索的正则
			var regExp = new RegExp(".*" + input + ".*");
			// 执行检索
			index = store1.findBy(function(record, id) {
				var text = record.get('prjName');
				return regExp.test(text);
			});
			// 内置的滚动方法
			windowPrjGrid.getView().focusRow(index);
		}
	});

	var checkModel = new Ext.selection.CheckboxModel({
		mode : "SIMPLE"
	});

	var windowPrjGrid = Ext.create('Ext.grid.Panel', {
		height : 300,
		width : 380,
		autoScroll : true,
		border : false,
		stripeRows : true,
		columns : prjColumn,
		store : store1,
		selModel : checkModel
	});

	var windowBtn = Ext.create('Ext.Button', {
		height : 22,
		width : 110,
		margin : '5 0 5 70',
		text : '确定',
		handler : function() {
			var record = windowPrjGrid.getSelectionModel().getSelection();
			var recordStr = '';
			for (var i = 0; i < record.length; i++) {
				if (i == (record.length - 1)) {
					recordStr += record[i].get('prjName');
				} else {
					recordStr += record[i].get('prjName') + ',';
				}
			}
			prjTextField.setValue(recordStr);
			prjWindow.close();
		}
	});

	var windowCancelBtn = Ext.create('Ext.Button', {
		height : 22,
		width : 110,
		margin : '5 0 5 20',
		text : '取消',
		handler : function() {
			prjWindow.close();
		}
	});

	var prjWindow = Ext.create('Ext.Window', {
		title : '选择工程',
		height : 400,
		width : 400,
		closeAction : 'hide',
		layout : 'column',
		items : [ windowPrjName,windowQueryBtn, windowPrjGrid, windowBtn, windowCancelBtn ]
	});
	
	var prjTextField = Ext.create('Ext.form.field.Text', {
		allowBlank : true,
		columnWidth : .87,
		labelAlign : 'right',
		fieldLabel : '系统名称',
		margin : '5 10 0 0',
		name : 'prjName'
	});

	var showWindowBtn = Ext.create('Ext.Button', {
		columnWidth : .10,
		margin : '5 10 0 0',
		text : '选择工程',
		handler : function() {
			prjWindow.show();
		}
	});

	var form = Ext.create('Ext.form.Panel', {
		border: false,
		width : contentPanel.getWidth(),
		height: 70,
		bodyPadding: 5,
	    layout: 'form',
	    items: [{
			    layout: 'column',
			    border: false,
			    items: [prjTextField,showWindowBtn,{
//					layout: 'form',
	    	    	columnWidth:.21,
	    	    	id : 'descId',
	    	    	multiSelect:true,
	    	    	padding : '5 10 0 0',
	    	    	xtype: 'combo',
	    	        fieldLabel: '任务类型',
	    	        labelAlign : 'right',
	    	        name: 'taskDesc',
	    	        store: new Ext.data.ArrayStore({
	    				fields : ['value', 'text'],
	    				data : [/*["0", ''],*/["1", '备份类'], ["2", '轮询类'], ["3", '非备份类']]
	    			}),
	    	        // displayField:'taskDesc',
	    	        editable:false
			    },startTime,endTime,queryBtn, {
					xtype : 'button',
					columnWidth : .11,
					margin : '5 0 0 50',
					text : '重置',
					handler : function() {							
						prjTextField.setValue("");
						Ext.getCmp('descId').setValue("");
						Ext.getCmp('startTime').setValue("");
						Ext.getCmp('endTime').setValue("");
					}
				}, { 
			        xtype:'button', 
					xtype: 'button',
					columnWidth:.11,
					margin:'5 0 0 50',
			        text:'导出', 
			        handler:function() { 
			        	var startDate = form.getForm().findField("startTime").getValue();
			    		var endDate = form.getForm().findField("endTime").getValue();
			    		var dt1 = null;
			    		var value1 = null;
			    		var dt2 = null;
			    		var value2 = null;
						if (startDate == null || startDate == '') {
							Ext.Msg.alert('错误', '开始时间不能为空！');
							return;
						}else if (endDate == null || endDate == '') {
							var curDate = new Date();
							var time=Ext.Date.format(curDate, 'Y-m-d H:i:s');
							form.getForm().findField("endTime").setValue(time);
						}
						
			    		if (startDate != null && startDate != '') {
			    			dt1 = new Date(startDate);
			    			value1 = Date.parse(dt1);
			    			if (value1 > Date.parse(new Date)
			    					|| value2 > Date.parse(new Date)) {
			    				Ext.Msg.alert('错误', '开始时间不能大于当前时间！');
			    				return;
			    			}
			    		}
			    		if (endDate != null && endDate != '') {
			    			dt2 = new Date(endDate);
			    			value2 = Date.parse(dt2);
			    			if (value2 > Date.parse(new Date)
			    					|| value2 > Date.parse(new Date)) {
			    				Ext.Msg.alert('错误', '结束时间不能大于当前时间！');
			    				return;
			    			}
			    		}
			    		if (value1 != null && value2 != null) {
			    			if (value1 > value2 || value1 == value2) {
			    				Ext.Msg.alert('错误', '开始时间不能大于等于结束时间！');
			    				return;
			    			}
			    		}
			            //grid 为Extjs grid对象 
			            //"导出文件名"  为导出的Excel文件的名称 
//			            grid2Excel(grid,"导出文件"); //IE不兼容
			        	var systemName = prjTextField.getValue();
			        	var taskDesc = Ext.getCmp('descId').value;
			        	var startTime = Ext.getCmp('startTime').getRawValue();
			        	var endTime = Ext.getCmp('endTime').getRawValue();
			        	window.location.href =  'sysStatExport.do?systemName='+systemName+'&taskDesc='+taskDesc+'&startTime='+startTime+'&endTime='+endTime+'&start=0&limit=1000';
			            
			        } 
			    }]
	    }]
	});

    store.on('beforeload', function (store, options) {
	    var new_params = {  
	    		systemName: form.getForm().findField('prjName').getValue(),
	    		taskDesc: form.getForm().findField('taskDesc').getValue(),
	    		startTime: form.getForm().findField('startTime').getValue(),
	    		endTime: form.getForm().findField('endTime').getValue()
	    };
	    Ext.apply(store.proxy.extraParams, new_params);
	    queryBtn.setDisabled(true);
	    myMask.show();
    });
    
    store.on("load", function(store) {
		myMask.hide();
		queryBtn.setDisabled(false);
	});
	
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	
	var grid = Ext.create('Ext.grid.Panel', {
		height : contentPanel.getHeight()-100,
		width : contentPanel.getWidth(),
		autoScroll : true,
		plugins : [ cellEditing ],
		store : store,
		border : true,
		columns : columns,
//		selType: "checkboxmodel",
        bbar: pageBar
	});
	grid.view.loadMask = false;
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		items: [form,grid],
	    renderTo : "systemtaskstat_grid_area"
	});
	
	contentPanel.on("resize",function(){
		form.setWidth(contentPanel.getWidth());
		grid.setHeight(contentPanel.getHeight()-100);
		grid.setWidth(contentPanel.getWidth());
	});
	contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy(mainPanel);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
	 });
});
