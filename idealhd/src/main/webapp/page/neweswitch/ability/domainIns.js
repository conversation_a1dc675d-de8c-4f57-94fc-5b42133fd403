Ext.onReady(function() {

	



Ext.define("DomainModel", {
        extend: "Ext.data.Model",
        fields: [
	        		{name: "iid", type: "long"}, 
		            { name: "iscopename", type: "string"},
		            { name: "iprocessdesc", type: "string"},
		            { name: "imodel", type: "string"}

        		]
    });

	/**  过程域保存列表数据源* */
	var DomainStore = Ext.create ('Ext.data.Store',
		{
			autoLoad : false,
			remoteSort : false,
			model : 'DomainModel',
			pageSize : 30,
			proxy :
				{
					type : 'ajax',
					timeout: 600000,
					url : "DomainIndex.do",
					reader :
						{
							type : 'json',
							root : 'dataList',
							totalProperty : 'total'
						}
				},
		});

	DomainStore.on ('beforeload', function (store, options)
	{
		var new_params =
			{
				//iid : taskId
			};
		Ext.apply (DomainStore.proxy.extraParams, new_params);
	});

//	DomainStore.load({
//		callback : function(record, options, success) {
//			/** tab页跳转重新插入已选择数据 */
//			insertChoosePoints();
//		}
//	});



	/** 过程域Columns* */
	var DomainColumns = [
        {
            text : '序号',
            width : 35,
            xtype : 'rownumberer'
        },
        {
            text : '域维ID',
            dataIndex : 'iid',
            width : 100,
            hidden : true
        },       
        {
        	text : '域维名称',
        	dataIndex : 'iscopename',
        	minWidth : 300
          //  flex:1
        }, {
        	    text : '过程域',
        	    dataIndex : 'iprocessdesc',
        	    width : 300
          }, {
        	    text : '工作产品示例',
        	    dataIndex : 'imodel',
        	    width : 1100
          }, {
			xtype:'actiontextcolumn',
			text : '操作',
			dataIndex : 'operation',
			minWidth : 100,
			flex : 1,
			align : 'left',
			sortable : true,
			items: [{
				tooltip: '删除',
				text : '删除',
				handler: function(grid, rowIndex, colIndex) {
					Ext.MessageBox.confirm ('提示', "确定要删除选中记录吗？", function (btn) {
						if (btn == 'no') {
							return;
						}
						if (btn == 'yes') {
							var record = grid.getStore().getAt(rowIndex);
							DomainStore.remove(record);
//							choosePoints.splice(rowIndex, 1);
//							choosePointIds.remove(record.data.icheckpointid);
						}
					});
				}
			}]}
	];

	

	
	
	var grid_panel_a = Ext.create ('Ext.ux.ideal.grid.Panel',
	{
	    store : DomainStore,
	    region : 'center',
	    ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    cls:'customize_panel_back',
	    border : true,
	    columnLines : true,
	    padding : grid_space,
	    columns : DomainColumns,
	    cellTip : true,
		height: '99%',
		width:'99%',
	    animCollapse : false,
	    selModel : Ext.create('Ext.selection.CheckboxModel', {checkOnly: false}),
	    dockedItems : [
	    	{
	    		xtype : 'toolbar',
	    		items : [{
							cls: 'Common_Btn',
							text: '增加',
							handler: addDomainInsItem
						}, 
						{
							cls: 'Common_Btn',
							text: '删除',
							handler:delCheckItem11
						}
						
						]
	    	}
	    ]
	});
	
	

	grid_panel_a.getSelectionModel().on('selectionchange', function(selModel, selections) {
		delCheckItemButton.setDisabled (selections.length === 0);
	});
	
	
	/** 添加巡检项弹出页 */
	function addDomainInsItem() {

		//alert(1);
		var record = DomainStore.getRange(0, DomainStore.getCount());
		//console.log(record);
		var chkPointIds = [];
		Ext.Array.each (record, function (recordObj)
		{
			//domainIid.push(recordObj.get('iid'));
		});




		/** 确定按钮 */
		var addButton = Ext.create("Ext.Button", {
			cls: 'Common_Btn',
			text: '确定',
			handler: addDomainPoint
		});

		/** 选择列表数据源* */
		var domainChooseStore1 = Ext.create ('Ext.data.Store',
			{
				autoLoad : true,
				remoteSort : false,
				model : 'DomainModel',
				pageSize : 30,
				proxy :
					{
						type : 'ajax',
						timeout: 600000,
						url : 'getDomainIndexList.do',
						reader :
							{
								type : 'json',
								root : 'dataList',
								totalProperty : 'total'
							}
					},
			});

		domainChooseStore1.on ('beforeload', function (store, options)
		{
			var new_params =
				{
//					isystemid : sysNameForQueryRelation.getValue() == null ? -1 : sysNameForQueryRelation.getValue(),
//					icomputerid : computerForQueryRelation.getValue() == null ? -1 : computerForQueryRelation.getValue(),
//					icheckitemid : itemForQuery.getValue() == null ? -1 : itemForQuery.getValue(),
//					icheckpointname : chkPointNameQuery.getValue(),
//					chkpointids : chkPointIds
				};
			Ext.apply (domainChooseStore1.proxy.extraParams, new_params);
		});

	
	var waitDomainColumns = [
	        {
	            text : '序号',
	            width : 35,
	            xtype : 'rownumberer'
	        },
	        {
	            text : '域维ID',
	            dataIndex : 'iid',
	            width : 100,
	            hidden : true
	        },       
	        {
	        	text : '域维名称',
	        	dataIndex : 'iscopename',
	        	minWidth : 300
	          //  flex:1
	        }, {
	        	    text : '过程域',
	        	    dataIndex : 'iprocessdesc',
	        	    width : 300
	          }, {
	        	    text : '工作产品示例',
	        	    dataIndex : 'imodel',
	        	    width : 1100
	          }
		];

	
		function addDomainPoint() {
			var record = DomainInsGrid.getSelectionModel().getSelection();
			if (record.length == 0) {
				Ext.Msg.alert('提示', "请先选择您要操作的行!");
				return;
			}

			for (var i = 0; i < record.length; i++) {
				DomainStore.add(record[i])
//				choosePoints.push(record[i]);
//				choosePointIds.push(record[i].data.icheckpointid);
			}

			Ext.Msg.alert('提示', "添加成功");

			showDomainWin.close();
		}

		var DomainInsGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
			width : '100%',
			region:'center',
			store : domainChooseStore1,
			ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
			selModel : Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
			border : true,
			viewConfig:{
				enableTextSelection:true
			},
			columnLines : true,
			columns : waitDomainColumns,
			cls:'customize_panel_back',
			padding : grid_space,
			 height: contentPanel.getHeight() - modelHeigth,
			cellTip : true,
			animCollapse : false,
			dockedItems : [ {
				xtype : 'toolbar',
				items : [ '->', addButton]
			}]
		});

		DomainInsGrid.getSelectionModel().on('selectionchange', function (selModel, selections) {
			addButton.setDisabled(selections.length === 0);
		});

//		function queryChkPointWhere (){
//			chkPointGrid.ipage.moveFirst();
//		}

		var showMainPanel = Ext.create('Ext.panel.Panel', {
			border : false,
			region: 'center',
			height: contentPanel.getHeight() * 0.7-25,
			layout : 'border',
			cls:'panel_space_top',
			items : [DomainInsGrid]
		});

		let showDomainWin = Ext.create('widget.window', {
			id : "cmdAndScript",
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			modal : true,
			title : '选择检查项',
			closable : true,
			closeAction : 'destroy',
			width : "75%",
			height : "90%",
			layout : 'fit',
			bodyCls: 'service_platform_bodybg',
			items : [showMainPanel]
		});

		showDomainWin.show();
	}

	/** 删除已选择巡检项 */
	function delCheckItem11() {
		Ext.MessageBox.confirm ('提示', "确定要删除选中记录吗？", function (btn) {
			if (btn == 'no') {
				return;
			}
			if (btn == 'yes') {
				var record = grid_panel_a.getSelectionModel().getSelection();
				for (var i = 0;i < record.length; i++) {
					DomainInsSaveStore.remove(record[i]);
//					choosePoints.splice(i, 1);
//					choosePointIds.remove(record[i].data.icheckpointid);
				}
			}
		});
	}

	/** tab页跳转重新插入已选择数据 */
	function insertChoosePoints1() {
		if (pointTabFlag) {
			var record = DomainInsSaveStore.getRange(0, DomainInsSaveStore.getCount());
			choosePoints = record;
			// for (var i = 0; i < choosePoints.length; i++) {
			// 	choosePointIds.push(choosePoints[i].data.icheckpointid);
			// }
			pointTabFlag = false;
		} else {
			DomainInsSaveStore.removeAll();
			//choosePointIds = [];
			for (var i = 0; i < choosePoints.length; i++) {
				DomainInsSaveStore.add(choosePoints[i])
				//choosePointIds.push(choosePoints[i].data.icheckpointid);
			}
		}
	}
	
	var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "domainInsMainDiv",
        layout: 'border',
        width: contentPanel.getWidth(),
        cls: 'customize_panel_back', //引入圆角
        bodyCls: 'service_platform_bodybg',
        height: '100%',
		width:'100%',
        border: true,
        bodyPadding: grid_margin,
        items: [grid_panel_a]
    });

 	contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
		Ext.destroy(mainPanel);
		
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
});
