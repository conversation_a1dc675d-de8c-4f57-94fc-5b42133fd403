 CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE PROC_UPDATE_ACTMONITOR (IN HOSTIP VARCHAR(50))
	LANGUAGE SQL
		BEGIN
			DECLARE INSTANCETYPE NUMERIC(2);
			DECLARE     FLOWID       NUMERIC(19,0);
			DECLARE     TEMPNUM          NUMERIC(19,0);
			DECLARE     OVERNUM      NUMERIC(6);
			begin
				FOR <PERSON>LUPFLOW AS CURSOR1 CURSOR FOR
					SELECT
					t.IMONITORINFOID AS MID,
					t.ILASTINSTANCE,
					t.IPRJNAME AS PNAME,
					t.IFLOWNAME AS FNAME,
					t.IINS<PERSON>NCENAME AS INANAME
					FROM IEAI_ACTMONITOR_RUNINFO T WHERE T.IFLOWID = 0
				DO
					SET INSTANCETYPE = ILASTINSTANCE;
					IF INSTANCETYPE = 2 THEN
							--指定的实例
							SELECT COUNT(1)
							INTO TEMPNUM
							FROM IEAI_WORKFLOWINSTANCE FLOW
							WHERE FLOW.IPROJECTNAME = PNAME
							AND FLOW.IFLOWNAME = FNAME
							AND FLOW.IFL<PERSON>INSNAME = INANAME;
							IF TEMPNUM > 0 AND TEMPNUM < 2 THEN
								SELECT FLOW.IFLOWID
								INTO FLOWID
								FROM IEAI_WORKFLOWINSTANCE FLOW
								WHERE FLOW.IPROJECTNAME = PNAME
								AND FLOW.IFLOWNAME = FNAME
								AND FLOW.IFLOWINSNAME = INANAME;
								UPDATE IEAI_ACTMONITOR_RUNINFO
								SET IFLOWID = FLOWID
								WHERE IMONITORINFOID = MID;
							ELSEIF TEMPNUM > 1 THEN
								SELECT MAX(FLOW.IFLOWID)
								INTO FLOWID
								FROM IEAI_WORKFLOWINSTANCE FLOW
								WHERE FLOW.IPROJECTNAME = PNAME
								AND FLOW.IFLOWNAME = FNAME
								AND FLOW.IFLOWINSNAME = INANAME;
								UPDATE IEAI_ACTMONITOR_RUNINFO
								SET IFLOWID = FLOWID
								WHERE IMONITORINFOID = MID;
							END IF;
					ELSEIF INSTANCETYPE = 1 THEN
							--上一个实例
							SELECT COUNT(1)
							INTO TEMPNUM
							FROM (SELECT FLOW.IFLOWID,  ROW_NUMBER() OVER (ORDER BY FLOW.IFLOWID DESC) AS RN
									FROM IEAI_WORKFLOWINSTANCE FLOW
									WHERE FLOW.IPROJECTNAME = PNAME
									AND FLOW.IFLOWNAME = FNAME
									--AND FLOW.IFLOWSTATUS = 0
									ORDER BY FLOW.IFLOWID DESC) S
							WHERE S.RN = 2;
							IF TEMPNUM > 0 THEN
								SELECT S.IFLOWID
								INTO FLOWID
								FROM (SELECT FLOW.IFLOWID, ROW_NUMBER() OVER (ORDER BY FLOW.IFLOWID DESC) AS RN
										FROM IEAI_WORKFLOWINSTANCE FLOW
										WHERE FLOW.IPROJECTNAME = PNAME
										AND FLOW.IFLOWNAME = FNAME
										--AND FLOW.IFLOWSTATUS = 0
										ORDER BY FLOW.IFLOWID DESC) S
								WHERE S.RN = 2;
								IF FLOWID IS NOT NULL AND FLOWID > 0 THEN
									UPDATE IEAI_ACTMONITOR_RUNINFO
									SET IFLOWID = FLOWID
									WHERE IMONITORINFOID = MID;
								END IF;
							END IF;
						ELSE
							--最新的实例
							SELECT COUNT(1)
							INTO TEMPNUM
							FROM (SELECT FLOW.IFLOWID
									FROM IEAI_WORKFLOWINSTANCE FLOW
									WHERE FLOW.IPROJECTNAME = PNAME
									AND FLOW.IFLOWNAME = FNAME);
							IF TEMPNUM > 0 THEN
								SELECT S.IFLOWID
								INTO FLOWID
								FROM (SELECT FLOW.IFLOWID, ROW_NUMBER() OVER (ORDER BY FLOW.IFLOWID DESC) AS RN
										FROM IEAI_WORKFLOWINSTANCE FLOW
										WHERE FLOW.IPROJECTNAME = PNAME
										AND FLOW.IFLOWNAME = FNAME
										--AND FLOW.ISTATUS = 0
										ORDER BY FLOW.IFLOWID DESC) S
								WHERE S.RN = 1;
								IF FLOWID IS NOT NULL AND FLOWID > 0 THEN
									UPDATE IEAI_ACTMONITOR_RUNINFO
									SET IFLOWID = FLOWID
									WHERE IMONITORINFOID = MID;
								END IF;
							END IF;
						END IF;
				END FOR;
				--更新已经运行完成的工作流或活动的监控状态
					SELECT COUNT(FLOW.IFLOWID)
					INTO OVERNUM
					FROM IEAI_WORKFLOWINSTANCE FLOW, IEAI_ACTMONITOR_RUNINFO INFO
					WHERE FLOW.IFLOWID = INFO.IFLOWID
					AND (FLOW.ISTATUS = 2 OR FLOW.ISTATUS = 4)
					AND INFO.ISTATUS = 0;
					IF OVERNUM > 0 THEN
						UPDATE IEAI_ACTMONITOR_RUNINFO T
						SET IACTSTATUS = 0, ISTATUS = 1
						WHERE T.IFLOWID IN
							(SELECT FLOW.IFLOWID
								FROM IEAI_WORKFLOWINSTANCE FLOW, IEAI_ACTMONITOR_RUNINFO INFO
								WHERE FLOW.IFLOWID = INFO.IFLOWID
								AND (FLOW.ISTATUS = 2 OR FLOW.ISTATUS = 4)
								AND INFO.ISTATUS = 0);
					END IF;
			end;
		END
		$