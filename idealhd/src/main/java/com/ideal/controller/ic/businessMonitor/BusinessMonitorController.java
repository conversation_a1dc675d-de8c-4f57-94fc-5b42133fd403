package com.ideal.controller.ic.businessMonitor;

import com.ideal.common.utils.ParseJson;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.mail.MailManager;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.ic.businessMonitor.BusinessMonitorBeanForQuery;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBean;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemManager;
import com.ideal.service.ic.businessMonitor.BusinessMonitorService;
import com.ideal.service.ic.businessMonitor.JBusinessMonitorService;
import com.ideal.util.StringUtil;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.JspException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Controller
public class BusinessMonitorController {

    private static final Logger log = Logger.getLogger(BusinessMonitorController.class);
    private static final String CONST_TS_CZZXCG = "操作执行成功！";
    private static final String CONST_RES_SUCC = "success";
    private static final String CONST_RES_MSG = "message";
    private static final String CONST_RES_PRJLIST = "prjList"; 
    private static final String CONST_RES_LISTCOL = "listCol"; 
    private static final String CONST_RES_WHATDO = "whatDo"; 
    private static final String CONST_VAR_SYSID = "sysid"; 
    private static final String CONST_BACK_URL = "businessAndCpmonitor.do"; 

    @RequestMapping("businessMonitor.do")
    public String businessMonitor ( HttpServletRequest request,String cpNameValue)
    {
        request.setAttribute("cpNameValue",cpNameValue);
        return "businessMonitor/businessMonitor";
    }

    /***
     * 
     * <li>Description:巡检业务监控点击检查项弹出窗口</li> 
     * <AUTHOR>
     * 2016年3月3日
     * @param cpid
     * @param request
     * @return
     * return String
     */
    @RequestMapping("serverMonitorOpen.do")
    public String serverMonitorOpen(String cpid,String ciid, HttpServletRequest request) {
        //增加查询巡检项规则，拼接扩展model和column
        BusinessMonitorService bms = new BusinessMonitorService();
        try {
            List list = bms.queryExtendColumns(ciid);
            request.setAttribute("ExtendColumns", list);
        } catch (Exception e) {
            log.error("请求【serverMonitorOpen】打开检查点值页面异常", e);
        }
        return "businessMonitor/serverMonitorOpen";
    }


    /**
     * 
     * <li>Description:业务监控</li>
     * 
     * <AUTHOR> hui 2015年10月26日
     * @return return String
     */
    @RequestMapping("queryList.do")
    @ResponseBody
    public Map<String, Object> queryListInfo(HttpServletRequest request, String prjName, String start, String limit, String page) throws Exception {
        /**Map<String, Object> map = new HashMap<String, Object>();*/
        BusinessMonitorService bms = new BusinessMonitorService();
        String sort = request.getParameter("sort");
        Map sortMap = getSortMap(sort);

        if(Environment.getInstance().getBooleanConfigNew2("hc.check.monitor.java.model.switch",true)){
            return JBusinessMonitorService.getInstance().queryListInfoOfJava( request, prjName, sortMap, start, limit);
        }else{
            return bms.queryListInfo(request, prjName.trim(), sortMap, start, limit, page);
        }

    }
    
    
    @RequestMapping("queryListInfoOfClassify.do")
    @ResponseBody
    public Map<String, Object> queryListInfoOfClassify(HttpServletRequest request, String prjName, String start, String limit, String page) throws Exception {
        /**Map<String, Object> map = new HashMap<String, Object>();*/
        BusinessMonitorService bms = new BusinessMonitorService();
        String sort = request.getParameter("sort");
        Map sortMap = getSortMap(sort);
        
        
        return bms.queryListInfoOfClassify(request, prjName.trim(), sortMap, start, limit, page);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private Map getSortMap ( String sort )
    {
        Map sortMap = new HashMap();
        if (sort != null)
        {
            JSONArray jsonArr = JSONArray.fromObject(sort);
            for(int i=0;i<jsonArr.size();i++) {
                Map sortMapinner = new HashMap();
                String sortName = jsonArr.getJSONObject(i).optString("property");
                String sortOrder = jsonArr.getJSONObject(i).optString("direction");
                if (sortName.equals("state"))
                {
                    sortName = "CPSTATUS";
                }
                if (sortName.equals("prjName"))
                {
                    sortName = "SYSNAME";
                }
                sortMapinner.put("column", sortName);
                sortMapinner.put("sortorder", sortOrder);
                sortMap.put(sortName, sortMapinner);
            }
            
            /*String sortName = jsonArr.getJSONObject(0).optString("property");
            String sortOrder = jsonArr.getJSONObject(0).optString("direction");
            if (sortName.equals("state"))
            {
                sortName = "CPSTATUS";
            }
            if (sortName.equals("prjName"))
            {
                sortName = "SYSNAME";
            }
            sortMap.put("column", sortName);
            sortMap.put("sortorder", sortOrder);*/
        }
        return sortMap;
    }

    /**
     * 
     * <li>Description:邮件发送入口方法</li>
     * 
     * <AUTHOR> 2016年6月12日
     * @param request
     * @param response
     * @param sysId
     * @param prjName return void
     */
    @RequestMapping("sendExcelMail.do")
    @ResponseBody
    public Object sendExcelMail(HttpServletRequest request, HttpServletResponse response, String sysId, String prjName) {
        Boolean ret = Boolean.TRUE;
        String msg = CONST_TS_CZZXCG;
        Map<String, Object> resp = new HashMap<String, Object>();
        File tempExcel = null;
        try {
            BusinessMonitorService bms = new BusinessMonitorService();
            List list = bms.getCheckIPList(sysId);
            String exportTime = formatDate(new Date(), "yyyyMMdd-HHmmss");

           /** String plath = "";*/
            String tmpPath = MailManager.getInstance().makeTempPath(prjName, exportTime);
            FileOutputStream fileOut = new FileOutputStream(tmpPath);

            sendExcelMailChildOne(bms, request, fileOut, list, sysId, exportTime, prjName);
            
            BusinessSystemBean bean = null;
            
            if(ServerEnv.getServerEnv().isSendIEmailSwitch()){
                SessionData sessionData = SessionData.getSessionData(request);
                String loginName = sessionData.getLoginName();
                bean = BusinessSystemManager.getInstance().queryLoginNameEmail(loginName,Constants.IEAI_HEALTH_INSPECTION);
            }else {
                bean = BusinessSystemManager.getInstance().queryOneBusiness(Long.valueOf(sysId),Constants.IEAI_HEALTH_INSPECTION);
            }
            
            if (StringUtil.isEmptyStr(bean.getMails())) {
                resp.put(CONST_RES_SUCC, false);
                resp.put(CONST_RES_MSG, "尚未配置email地址！");
                return resp;
            }

            tempExcel = new File(tmpPath);
            Boolean initRet = MailManager.getInstance().initProperties();

            if (!initRet) {
                ret = Boolean.FALSE;
                msg = "邮件服务器配置信息不完整！";
                resp.put(CONST_RES_SUCC, ret);
                resp.put(CONST_RES_MSG, msg);
                return resp;
            }
            MailManager.getInstance().sendMail(bean.getMails(), prjName, prjName, tempExcel);
            
           

            resp.put(CONST_RES_SUCC, ret);
            resp.put(CONST_RES_MSG, msg);
        }catch (NumberFormatException e1) {
            log.error("获取业务系统信息出现NumberFormatException", e1);
            resp.put(CONST_RES_SUCC, false);
            resp.put(CONST_RES_MSG, "获取业务系统信息异常！");
        }catch (RepositoryException e1) {
            resp.put(CONST_RES_SUCC, false);
            resp.put(CONST_RES_MSG, "获取业务系统信息异常！");
            log.error("获取业务系统信息出现RepositoryException", e1);
        }catch (AddressException e) {
            resp.put(CONST_RES_SUCC, Boolean.FALSE);
            resp.put(CONST_RES_MSG, "邮件地址异常！");
            log.error( "邮件地址异常！", e);
        }  catch (UnsupportedEncodingException e) {
            resp.put(CONST_RES_SUCC, Boolean.FALSE);
            resp.put(CONST_RES_MSG, "不支持的邮件格式");
            log.error("不支持的邮件格式", e);
        } catch (MessagingException e) {
            resp.put(CONST_RES_SUCC, Boolean.FALSE);
            resp.put(CONST_RES_MSG, e.getMessage());
            log.error(e.getMessage(), e);
        } catch (Exception e) {
            resp.put(CONST_RES_SUCC, false);
            resp.put(CONST_RES_MSG, e.getMessage());
            log.error("发送邮件异常", e);
        }finally {
            if(tempExcel!=null) {
                boolean delete = tempExcel.delete();
                if(!delete) {
                    log.error("邮件临时文件清理失败");
                }
            }
        }
        return resp;
    }

    private boolean  sendExcelMailChildOne(BusinessMonitorService bms,HttpServletRequest request,FileOutputStream fileOut,List list,String sysId,String exportTime,String prjName) {
        boolean flag = true;
        try {
            bms.doExport2(request, fileOut, list, sysId, exportTime, prjName);
        } catch (JspException e) {
            flag = Boolean.FALSE;
            log.error("业务系统监控发送邮件出现JspException", e);
        }
        return flag;
    }
    
    /**
     * 
     * <li>Description:业务监控业务系统查询</li>
     * 
     * <AUTHOR> hui 2015年10月26日
     * @return return String
     */
    @RequestMapping("queryPrjname.do")
    @ResponseBody
    public Map<String, Object> queryProname(HttpServletRequest request) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        List prjNames = new ArrayList();
        List list = new ArrayList();
        SessionData sessionData = SessionData.getSessionData(request);
        String userId1 = sessionData.getUserInnerCode();
        String username = sessionData.getLoginName();
        BusinessMonitorService bms = new BusinessMonitorService();
        try {
            list = bms.queryProname(userId1, username);
            if (list!=null && !list.isEmpty()) {
                prjNames.addAll(list);
                map.put(CONST_RES_PRJLIST, prjNames);
            } else {
                    /**list = new ArrayList();*/
                map.put(CONST_RES_PRJLIST, prjNames);
            }
        } catch (Exception e) {
            map.put(CONST_RES_PRJLIST, new ArrayList());
            log.error("业务监控业务系统查询异常", e);
        }
        return map;
    }

    /**
     * 
     * <li>Description:查询服务器作为表格的列</li>
     * 
     * <AUTHOR> 2015年10月30日
     * @param sysid
     * @param request
     * @return return String
     */
    @RequestMapping("queryServerBySys.do")
    public String queryServerBySys(String sysid,String cpNameValue, String computerip,String status, HttpServletRequest request) {
        BusinessMonitorService bms = new BusinessMonitorService();
        try {
            List list = bms.queryServerBySys(sysid, computerip,status);
            request.setAttribute("resstatus",status);
            request.setAttribute("cpNameValue",cpNameValue);
            request.setAttribute(CONST_RES_LISTCOL, list);
            request.setAttribute(CONST_RES_WHATDO, request.getParameter(CONST_RES_WHATDO));
        } catch (Exception e) {
           log.error("方法【queryServerBySysInner】功能【查询服务器】异常", e);
        }
        return "businessMonitor/serverMonitor";
    }
    
    
    
    /**
     * 
     * @Title: queryServerBySyspage   
     * @Description: 业务系统监控页面   
     * @param sysid
     * @param request
     * @return      
     * @author: changhai_lu 
     * @date:   2019年7月25日 上午9:35:54
     */
    @RequestMapping("queryServerBySyspage.do")
    public String queryServerBySyspage(String sysid, HttpServletRequest request) {
        request.setAttribute(CONST_VAR_SYSID, sysid);
        request.setAttribute(CONST_RES_WHATDO, request.getParameter(CONST_RES_WHATDO));
        BusinessMonitorService bms = new BusinessMonitorService();
        List list = new ArrayList();
        try {
           // list = bms.queryCIByServer(sysid);
            list = bms.queryCIByServerOfNew(sysid);
            request.setAttribute(CONST_RES_LISTCOL, list);
        } catch (Exception e) {
            log.error("获取业务系统下设备检查项错误", e);
        }
        
        return "businessMonitor/serverMonitor2";
    }
    
    /**
     * 
     * @Title: queryServerBySysNew   
     * @Description: 业务系统监控数据获取 
     * @param sysid
     * @param cpname
     * @param start
     * @param limit
     * @param request
     * @return      
     * @author: changhai_lu 
     * @date:   2019年7月25日 上午9:36:25
     */
    @RequestMapping("queryServerBySysNew.do")
    @ResponseBody
    public Map<String, Object> queryServerBySysNew (String sysid,String cpname,String start,String limit, HttpServletRequest request) {
        BusinessMonitorService bms = new BusinessMonitorService();
        List list = new ArrayList();
        List list1 = new ArrayList();
        Map<String, Object> pagemap = new HashMap<String, Object>();
        try {
            pagemap = bms.queryServerBySysPage(sysid, cpname, start, limit);
            list = (List) pagemap.get("queryList");
            list1 = bms.queryCIByServerNM(sysid, list);
            for(int i=0;i<list.size();i++) {
                Map ipmap = (Map) list.get(i);
                ipmap.put("isysid", sysid);
                for(int j=0;j<list1.size();j++) {
                    Map ciimap = (Map) list1.get(j);
                    ipmap.put("ciid"+ciimap.get("ciid"), ciimap.get("meid"+ipmap.get("meid")));
                }
            }
            request.setAttribute(CONST_RES_WHATDO, request.getParameter(CONST_RES_WHATDO));
            pagemap.put("queryList", list);
        } catch (Exception e) {
            log.error("设备检查项状态列表获取错误", e);
        }
        return pagemap;
    }

    /***
     * 
     * <li>Description:巡检业务监控点击检查项弹出窗口列表数据</li> 
     * <AUTHOR>
     * 2016年3月9日
     * @param request
     * @param businessMonitorBeanForQuery
     * @return
     * return Map
     */
    @RequestMapping("getMonitorOpenGridDatas.do")
    @ResponseBody
    public Map getMonitorOpenGridDatas(HttpServletRequest request, BusinessMonitorBeanForQuery businessMonitorBeanForQuery) {

        Map res = null;
        SessionData sessionData = SessionData.getSessionData(request);
        businessMonitorBeanForQuery.setUserName(sessionData.getLoginName());
        try {
            res = BusinessMonitorService.getInstance().getMonitorOpenGridDatas(businessMonitorBeanForQuery);

        } catch (RepositoryException e) {
           log.error("请求【getMonitorOpenGridDatas.do】处理异常", e);
        }
        return res;
    }

    /**
     * 
     * <li>Description:查询服务器对应检查项结果</li>
     * 
     * <AUTHOR> 2015年10月30日
     * @param sysid
     * @return return List
     */
    @RequestMapping("queryCIByServer.do")
    @ResponseBody
    public List queryCIByServer(String sysid) {
        BusinessMonitorService bms = new BusinessMonitorService();
        List list = new ArrayList();
        try {
          //  list = bms.queryCIByServer0(sysid);

            //重新优化业务系统监控查询，避免order by超长设定
            list = bms.queryCIByServerOfNew(sysid);
        } catch (Exception e) {
            log.error("请求【queryCIByServer】异常", e);
        }
        return list;
    }

    /**
     * 
     * <li>Description:检查点页面初始化</li>
     * 
     * <AUTHOR> 2015年10月31日
     * @return return String
     */
    @RequestMapping("forwardCP.do")
    public String forwardCP ( HttpServletRequest request,String meid,String ciid,String sysid,String cpnameshow,String cinameshow,String cpNameValue,String whatDo)
    {
        request.setAttribute(CONST_RES_WHATDO, request.getParameter(CONST_RES_WHATDO));
        request.setAttribute("meid", meid);
        request.setAttribute("ciid", ciid);
        request.setAttribute(CONST_VAR_SYSID, sysid);
        request.setAttribute("cpnameshow", cpnameshow);
        request.setAttribute("cinameshow", cinameshow);
        request.setAttribute("cpNameValue", cpNameValue);
        request.setAttribute("CONST_RES_WHATDO", whatDo);
        return "businessMonitor/serverCPMonitor";
    }

    /**
     * 
     * <li>Description:检查点监控查询</li>
     * 
     * <AUTHOR> 2015年10月31日
     * @param sysid
     * @param meid
     * @param ciid
     * @return return List
     */
    @RequestMapping("queryCP.do")
    @ResponseBody
    public List queryCP(String sysid, String meid, String ciid) {
        BusinessMonitorService bms = new BusinessMonitorService();
        List list = new ArrayList();
        try {
            list = bms.queryCP(sysid, meid, ciid);
        } catch (Exception e) {
            log.error("请求【queryCP】检查点监控查询异常", e);
        }
        return list;
    }

    /**
     * 
     * <li>Description:检查点值页面</li>
     * 
     * <AUTHOR> 2015年11月2日
     * @return return String
     */
    @RequestMapping("forwardCPValue.do")
    public String forwardCPValue(String cpid, HttpServletRequest request) {
        
        BusinessMonitorService bms = new BusinessMonitorService();
        String pointdesc = bms.getDesc(cpid);
        request.setAttribute("pointdesc", pointdesc);
        try {
            List list = bms.queryCPCol(cpid);
            request.setAttribute(CONST_RES_LISTCOL, list);
        } catch (Exception e) {
            log.error("请求【forwardCPValue】打开检查点值页面异常", e);
        }
        return "businessMonitor/serverCPValueMonitor";
    }

    /**
     * 
     * <li>Description:检查点值查询</li>
     * 
     * <AUTHOR> 2015年11月2日
     * @param cpid
     * @return return List
     */
    @RequestMapping("queryCPValue.do")
    @ResponseBody
    public List queryCPValue(String cpid,String rsdid) {
        BusinessMonitorService bms = new BusinessMonitorService();
        List list = new ArrayList();
        try {
            if(StringUtils.isNotBlank(rsdid) &&!"null".equals(rsdid) && !"NULL".equals(rsdid)) {
                list =  bms.queryCPValueOfHis(cpid, rsdid);
            }
            else {
                list = bms.queryCPValue(cpid);
            }
        } catch (Exception e) {
            log.error("请求【queryCPValue】检查点值查询异常", e);
        }
        /**System.out.println(list);*/
        return list;
    }

    /**
     * 导出入口方法
     * <li>Description:</li>
     * 
     * <AUTHOR> 2015年10月31日
     * @param request
     * @param response
     * @param sysId return void
     * @throws UnsupportedEncodingException 
     */
    @RequestMapping("exportCheckPointExcel.do")
    public void exportCheckPointExcel(HttpServletRequest request, HttpServletResponse response, String sysId, String prjName) throws UnsupportedEncodingException {
        BusinessMonitorService bms = new BusinessMonitorService();
        List list = bms.getCheckIPList(sysId);
        String finameTime = formatDate(new Date(), "yyyyMMddHHmm");
        String exportTime = formatDate(new Date(), "yyyyMMdd");
        response.setContentType("application/vnd.ms-excel; GBK");
        boolean isExportEt = Environment.getInstance().getBooleanConfigNew2("excel.export.et.switch",false);
        if(isExportEt) {
            response.setHeader("Content-disposition", "attachment;filename=" + new String(prjName.getBytes("UTF-8"), "iso-8859-1") + "-" + finameTime
                    + ".et");
        }else{
            response.setHeader("Content-disposition", "attachment;filename=" + new String(prjName.getBytes("UTF-8"), "iso-8859-1") + "-" + finameTime
                    + ".xls");
        }
        response.setCharacterEncoding("UTF-8");
        try {
             bms.doExport2(request, response.getOutputStream(), list, sysId, exportTime, prjName);
        } catch (IOException e) {
            log.error("请求【exportCheckPointExcel】导出IOException异常", e);
        } catch (JspException e) {
            log.error("请求【exportCheckPointExcel】导出JspException异常", e);
        }
    }

    /**
     * 
     * @Title: exportAllMonitor   
     * @Description: 巡检监控导出所有业务系统巡检数据   
     * @param request
     * @param response
     * @param sysId
     * @param prjName      
     * @author: haijiao_dong 
     * @throws Exception 
     * @date:   2018年8月29日
     */
    @RequestMapping("exportAllMonitor.do")
    public void exportAllMonitor ( HttpServletRequest request, HttpServletResponse response, String prjName )
            throws Exception
    {
        BusinessMonitorService bms = BusinessMonitorService.getInstance();
        Map<String, Object> map = bms.queryListInfo(request, prjName.trim(), null, String.valueOf(0),
            String.valueOf(65530),
            String.valueOf(1));

        String finameTime = formatDate(new Date(), "yyyyMMddHHmm");

        if ("".equals(prjName.trim()))
        {
            prjName = "巡检监控报表导出";
        }
        response.setContentType("application/octet-stream; GBK");
        boolean isExportEt = Environment.getInstance().getBooleanConfigNew2("excel.export.et.switch",false);
        if(isExportEt) {
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(prjName.getBytes("gb2312"), "iso-8859-1") + "-" + finameTime + ".et");
        }else{
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(prjName.getBytes("gb2312"), "iso-8859-1") + "-" + finameTime + ".xls");
        }
        response.setCharacterEncoding("UTF-8");
        try
        {
            bms.exportAllMonitor(request, response.getOutputStream(), map, prjName);
        } catch (Exception e)
        {
            log.info("exportAllMonitor method : ", e);
        }

    }
    
    /**
     * 
     * @Title: exportAllNmnxMonitor   
     * @Description: (内蒙农信)巡检监控导出巡检数据   
     * @param request
     * @param response
     * @param sysId
     * @param prjName      
     * @author: haijiao_dong 
     * @throws Exception 
     * @date:   2021年2月25日
     */
    @RequestMapping("exportAllNmnxMonitor.do")
    public void exportAllNmnxMonitor ( HttpServletRequest request, HttpServletResponse response, String prjName )
            throws Exception
    {
        BusinessMonitorService bms = BusinessMonitorService.getInstance();
        Map<String, Object> map = bms.queryListInfo(request, prjName.trim(), null, String.valueOf(0),
            String.valueOf(65530),
            String.valueOf(1));

        String finameTime = formatDate(new Date(), "yyyyMMddHHmm");

        if ("".equals(prjName.trim()))
        {
            prjName = "巡检监控报表导出";
        }
        response.setContentType("application/octet-stream; GBK");
        boolean isExportEt = Environment.getInstance().getBooleanConfigNew2("excel.export.et.switch",false);
        if(isExportEt) {
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(prjName.getBytes("gb2312"), "iso-8859-1") + "-" + finameTime + ".et");
        }else{
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(prjName.getBytes("gb2312"), "iso-8859-1") + "-" + finameTime + ".xls");
        }
        response.setCharacterEncoding("UTF-8");
        try
        {
            bms.exportAllNmnxMonitor(request, response.getOutputStream(), map, prjName);
        } catch (Exception e)
        {
            log.info("exportAllNmnxMonitor method : ", e);
        }

    }

    @RequestMapping("exportSdMonitor.do")
    public void exportSdMonitor ( HttpServletRequest request, HttpServletResponse response, String prjName,String sysName,String recentNum,String starttime,String endtime )
            throws Exception
    {
        BusinessMonitorService bms = BusinessMonitorService.getInstance();
        Map<String, Object> map = bms.queryListInfo(request, prjName.trim(), null, String.valueOf(0),
            String.valueOf(65530),
            String.valueOf(1));

        String finameTime = formatDate(new Date(), "yyyyMMddHHmm");

        if ("".equals(prjName.trim()))
        {
            prjName = "巡检监控报表导出";
        }
        response.setContentType("application/octet-stream; GBK");
        boolean isExportEt = Environment.getInstance().getBooleanConfigNew2("excel.export.et.switch",false);
        if(isExportEt) {
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(prjName.getBytes("gb2312"), "iso-8859-1") + "-" + finameTime + ".et");
        }else{
            response.setHeader("Content-disposition",
                    "attachment;filename=" + new String(prjName.getBytes("gb2312"), "iso-8859-1") + "-" + finameTime + ".xls");
        }
        response.setCharacterEncoding("UTF-8");
        try
        {
            boolean isSdExportSwitch = ServerEnv.getInstance().getHcMonitorSdExportSwitch();

            bms.exportAllMonitor(request, response.getOutputStream(), map, prjName,isSdExportSwitch,sysName,recentNum,starttime,endtime);
        } catch (Exception e)
        {
            log.info("exportAllMonitor method : ", e);
        }

    }
    
    /**
     * 日期格式化
     * <li>Description:</li>
     * 
     * <AUTHOR> 2015年11月2日
     * @param date
     * @return return String
     */
    private String formatDate(Date date, String format) {
        SimpleDateFormat f = new SimpleDateFormat(format);
        if (null != date) {
            return f.format(date);
        }
        return "";
    }
    
    /**
     * 
     * <li>Description:巡检业务监控行排序保存</li> 
     * <AUTHOR>
     * 2016年3月1日
     * @param request
     * @param incdata
     * @return
     * return Object
     */
    @RequestMapping("saveSortForRows.do")
    @ResponseBody
    public Object saveSortForRows ( HttpServletRequest request, String incdata )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        SessionData sessionData = SessionData.getSessionData(request);
        List<Map<String, Object>> intervalConfigs;
        try
        {
            int type = Constants.IEAI_HEALTH_INSPECTION;
            intervalConfigs = ParseJson.JSON2List(incdata);
            boolean returnValue = BusinessMonitorService.getInstance().saveSortForRows(
                intervalConfigs, sessionData.getLoginName(), type);
            if (returnValue)
            {
                resp.put(CONST_RES_SUCC, true);
                resp.put(CONST_RES_MSG, CONST_TS_CZZXCG);
            } else
            {
                resp.put(CONST_RES_SUCC, false);
                resp.put(CONST_RES_MSG, "执行操作失败！");
            }
        } catch (Exception e)
        {
            resp.put(CONST_RES_SUCC, false);
            resp.put(CONST_RES_MSG, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * <li>Description:巡检业务监控列排序保存</li> 
     * <AUTHOR>
     * 2016年3月1日
     * @param request
     * @param incdata
     * @return
     * return Object
     */
    @RequestMapping("saveSortForColumns.do")
    @ResponseBody
    public Object saveSortForColumns ( HttpServletRequest request, String incdata )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        SessionData sessionData = SessionData.getSessionData(request);
        List<Map<String, Object>> intervalConfigs;
        try
        {
            int type = Constants.IEAI_HEALTH_INSPECTION;
            intervalConfigs = ParseJson.JSON2List(incdata);
            boolean returnValue = BusinessMonitorService.getInstance().saveSortForColumns(
                intervalConfigs, sessionData.getLoginName(), type);
            if (returnValue)
            {
                resp.put(CONST_RES_SUCC, true);
                resp.put(CONST_RES_MSG, CONST_TS_CZZXCG);
            } else
            {
                resp.put(CONST_RES_SUCC, false);
                resp.put(CONST_RES_MSG, "执行操作失败！");
            }
        } catch (Exception e)
        {
            resp.put(CONST_RES_SUCC, false);
            resp.put(CONST_RES_MSG, e.getMessage());
        }
        return resp;
    }

    /**
     * 
     * @Title: businessAndCpmonitor   
     * @Description: 巡检监控整合  
     * @return      
     * @author: haijiao_dong 
     * @date:   2018年9月5日
     */
    @RequestMapping("businessAndCpmonitor.do")
    public String businessAndCpmonitor ( HttpServletRequest request,String starttime,String endtime,String osName,String dbType,String sysinfoname,String startIp,String endIp,String area,String cpName,String cpNameValue )
    {
        request.setAttribute("icSwitch", true);
        request.setAttribute("emSwitch", true);
        request.setAttribute("startIp",startIp);
        request.setAttribute("endIp",endIp);
        request.setAttribute("cpName",cpName);
        request.setAttribute("cpNameValue",cpNameValue);
        request.setAttribute(CONST_RES_WHATDO, CONST_BACK_URL);
        request.setAttribute("activeTabNum", request.getParameter("activeTabNum"));
        
        boolean ispermission = Environment.getInstance().isHcBussinessmonitorPermissionSwitch();
        boolean isBusiness = true;
        boolean isComputer = true;
        if(ispermission) {
            //查询权限
            SessionData sessionData = SessionData.getSessionData(request);
            if(sessionData!=null) {
                String userid = sessionData.getUserInnerCode();
                Map<String, Map<String, String>> maprole = sessionData.getMapRoleUrl();
                ConcurrentHashMap<String, String> role = (ConcurrentHashMap<String, String>) maprole.get(userid);
                if(role!=null) {
                    if (role.containsKey("integraBusiness.do"))
                    {
                        if (role.get("integraBusiness.do").equals("1"))
                        {
                            isBusiness = true;
                        } else
                        {
                            isBusiness = false;
                        }
                    }
                    
                    if (role.containsKey("integraCpmonitor.do"))
                    {
                        if (role.get("integraCpmonitor.do").equals("1"))
                        {
                            isComputer = true;
                        } else
                        {
                            isComputer = false;
                        }
                    }
                    
                }
                
            }
        }
        
       
        request.setAttribute("isComputer", isComputer);
        request.setAttribute("isBusiness", isBusiness);
        
        return "businessMonitor/businessAndCpmonitor";
    }

    /**
     * 
     * @Title: integraBusiness   
     * @Description: 巡检监控整合   
     * @param request
     * @return      
     * @author: haijiao_dong 
     * @date:   2018年9月5日 下午1:46:11
     */
    @RequestMapping("integraBusiness.do")
    public String integraBusiness ( HttpServletRequest request ,String cpNameValue)
    {
        request.setAttribute(CONST_RES_WHATDO, CONST_BACK_URL);
        request.setAttribute("cpNameValue",cpNameValue);
        boolean issdnsbusiness = ServerEnv.getInstance().isHcBussinessmonitorBusinessindexSDNSSwitch();
        if(!issdnsbusiness) {
            return "businessMonitor/businessMonitor";
        }else {
            //添加开关控制返回界面是否为顺德农商的定制界面
            return "businessMonitor/businessMonitorOfClassify";
        }
      
      
    }

    /**
     * 
     * @Title: integraCpmonitor   
     * @Description: 设备监控整合   
     * @param request
     * @return      
     * @author: haijiao_dong 
     * @date:   2018年9月5日 下午1:46:11
     */
    @RequestMapping("integraCpmonitor.do")
    public String integraCpmonitor ( HttpServletRequest request ,String starttime,String endtime,String osName,String dbType,String sysinfoname,String startIp,String endIp,String area,String cpName)
    {
        request.setAttribute(CONST_RES_WHATDO, CONST_BACK_URL);
        request.setAttribute("startIp",startIp);
        request.setAttribute("endIp",endIp);
        request.setAttribute("cpName",cpName);
        boolean isNew = ServerEnv.getInstance().getBooleanConfig(Environment.HC_MONITOR_CPMONITOR_NEW_PAGE_SWITCH, false);
        if(isNew) {
            return "cpmonitor/businessMonitor2";
        }else {
            return "cpmonitor/businessMonitor";  
        }
    }
    /***
     * 
     * <li>Description:巡检配置界面</li> 
     * <AUTHOR>
     * 2019年6月24日 
     * @param sysid
     * @param cpnameshow
     * @param meid
     * @param request
     * @return
     * return String
     */
    @RequestMapping("businessMonitorNew.do")
    public String businessMonitorNew ( String sysid,String cpnameshow,String cinameshow,String meid,String ciid,String cpNameValue,String whatDo,HttpServletRequest request )
    {
        String idString=meid.substring(4, meid.length());
        request.setAttribute("idString", idString);
        request.setAttribute(CONST_VAR_SYSID, sysid);
        request.setAttribute("meid", meid);
        request.setAttribute("ciid", ciid);
        request.setAttribute("cpnameshow", cpnameshow);
        request.setAttribute("cinameshow", cinameshow);
        request.setAttribute("cpNameValue", cpNameValue);
        request.setAttribute("whatDo", whatDo);
        return "businessInsConfig/businessMonitorConfig";
    }
    @RequestMapping("exportCheckItem.do")
    @ResponseBody
    public void exportCheckItem ( HttpServletResponse response, HttpServletRequest request, String sysId, String prjName ) throws RepositoryException
    {
       
        BusinessMonitorService service = new BusinessMonitorService();
        Map<String, Object> res = service.exportsql(sysId);
        List<Map> list = (List<Map>) res.get("dataList");
        service.exportExcel(list, response,sysId,prjName);

    }
    
    /***
     * 
     * <li>Description:沧州巡检监控导出</li> 
     * <AUTHOR>
     * 2020年12月18日 
     * @param request
     * @param response
     * return void
     */
    @RequestMapping("exportCheckPointExcelCz.do")
    public void  itemExport(HttpServletRequest request,HttpServletResponse response) {
        BusinessMonitorService service = new BusinessMonitorService();
        SessionData sessionData = SessionData.getSessionData(request);
        String userId = sessionData.getUserInnerCode();
        String userName = sessionData.getUserName();
        
        ServletOutputStream outputStream = null;
        try
        {
            outputStream = response.getOutputStream();
            String finameTime = formatDate(new Date(), "yyyyMMddHHmm");
            response.setContentType("application/vnd.ms-excel; GBK");
            boolean isExportEt = Environment.getInstance().getBooleanConfigNew2("excel.export.et.switch",false);
            if(isExportEt) {
                response.setHeader("Content-disposition", "attachment;filename=" + finameTime + ".et");
            }else {
                response.setHeader("Content-disposition", "attachment;filename=" + finameTime + ".xls");
            }
            response.setCharacterEncoding("UTF-8");
            service.export(outputStream,userId,userName); 
        } catch (Exception e)
        {
            log.error(e);
        }finally {
            try
            {
                if(outputStream!=null) {
                    outputStream.close();
                }
            } catch (IOException e)
            {
                log.error(e);
            }
        }
    }
}
