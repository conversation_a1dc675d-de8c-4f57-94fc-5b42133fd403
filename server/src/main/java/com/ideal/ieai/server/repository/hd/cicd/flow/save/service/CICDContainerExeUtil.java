package com.ideal.ieai.server.repository.hd.cicd.flow.save.service;

import com.ideal.ieai.server.ifc.bussiness.IfcContainer;
import com.ideal.ieai.server.repository.hd.cicd.flow.task.CICDFlowTask;
import com.ideal.ieai.server.repository.hd.cicd.flow.task.CICDTaskParam;
import com.ideal.ieai.server.repository.hd.cicd.flow.task.CICDVersion;
import com.ideal.ieai.server.repository.hd.cicd.flow.task.CICDVersionContent;

import java.sql.Connection;
import java.util.*;

@SuppressWarnings("serial")
public class CICDContainerExeUtil implements IfcContainer
{
    private Connection                          basicConnection;                                                           //   基线数据库连接
    private String                              basicConnectionName;                                                       //   基线数据库连接名称
    private Map<String, Connection>             connectionMap               = new LinkedHashMap<String, Connection>();     //   数据库连接集合
    private Map<String, Connection>             comitConnectionMap          = new LinkedHashMap<String, Connection>();     //   已提交的数据库连接集合
    private CICDVersionContent                  cicdversioncontent          = new CICDVersionContent();                    //  version_conten流程图表
    private List<CICDVersion>                   cicdVersion                 = new ArrayList<CICDVersion>();                //   新增IEAI_INSTANCE_VERSION表数据
    private List<CICDTaskParam>                 CICDTaskParam               = new ArrayList<CICDTaskParam>();              //   新增IEAI_CICD_TASKPARAM自定义参数
    private CICDFlowTask                        cicdFlowTask;                                                              //   新增cicd任务表信息
    private List<String>                        exeSqls                     = new ArrayList<String>();                     //   执行sql(后期扩展用)
    private List<String>                        rollbackSqls                = new ArrayList<String>();                     //   回滚sql(后期扩展用)
    private Map<String, Object>                 idMap                       = new HashMap<String, Object>();               //   存入version表的id跟页面id对也值
    public Connection getBasicConnection ()
    {
        return basicConnection;
    }
    public void setBasicConnection ( Connection basicConnection )
    {
        this.basicConnection = basicConnection;
    }
    public String getBasicConnectionName ()
    {
        return basicConnectionName;
    }
    public void setBasicConnectionName ( String basicConnectionName )
    {
        this.basicConnectionName = basicConnectionName;
    }
    public Map<String, Connection> getConnectionMap ()
    {
        return connectionMap;
    }
    public void setConnectionMap ( Map<String, Connection> connectionMap )
    {
        this.connectionMap = connectionMap;
    }
    public Map<String, Connection> getComitConnectionMap ()
    {
        return comitConnectionMap;
    }
    public void setComitConnectionMap ( Map<String, Connection> comitConnectionMap )
    {
        this.comitConnectionMap = comitConnectionMap;
    }
    public List<String> getExeSqls ()
    {
        return exeSqls;
    }
    public void setExeSqls ( List<String> exeSqls )
    {
        this.exeSqls = exeSqls;
    }
    public List<String> getRollbackSqls ()
    {
        return rollbackSqls;
    }
    public void setRollbackSqls ( List<String> rollbackSqls )
    {
        this.rollbackSqls = rollbackSqls;
    }
    public List<CICDTaskParam> getCICDTaskParam ()
    {
        return CICDTaskParam;
    }
    public void setCICDTaskParam ( List<CICDTaskParam> cICDTaskParam )
    {
        CICDTaskParam = cICDTaskParam;
    }
    
    public CICDFlowTask getCicdFlowTask ()
    {
        return cicdFlowTask;
    }
    public void setCicdFlowTask ( CICDFlowTask cicdFlowTask )
    {
        this.cicdFlowTask = cicdFlowTask;
    }
    public List<CICDVersion> getCicdVersion ()
    {
        return cicdVersion;
    }
    public void setCicdVersion ( List<CICDVersion> cicdVersion )
    {
        this.cicdVersion = cicdVersion;
    }
    public CICDVersionContent getCicdversioncontent ()
    {
        return cicdversioncontent;
    }
    public void setCicdversioncontent ( CICDVersionContent cicdversioncontent )
    {
        this.cicdversioncontent = cicdversioncontent;
    }
    public Map<String, Object> getIdMap ()
    {
        return idMap;
    }
    public void setIdMap ( Map<String, Object> idMap )
    {
        this.idMap = idMap;
    }
}
