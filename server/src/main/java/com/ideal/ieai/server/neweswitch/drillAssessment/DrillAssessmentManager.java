package com.ideal.ieai.server.neweswitch.drillAssessment;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DrillAssessmentManager {
    private static final Logger _log = Logger.getLogger(DrillAssessmentManager.class);
    private static final String DATEFORMAT = "yyyy-MM-dd HH:mm:ss";

    private DrillAssessmentManager() {
    }

    private static DrillAssessmentManager instance = null;


    public static DrillAssessmentManager getInstance() {
        if (null == instance) {
            synchronized (DrillAssessmentManager.class) {
                if (null == instance) {
                    instance = new DrillAssessmentManager();
                }
            }
        }
        return instance;
    }

    public Map getDrillAssessmentManager(String state, String planName, String planType, String startTime, String endTime, int start, int limit, int appType) throws ParseException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        // 数据库连接对象
        Connection conn = null;
        Map map = new HashMap<String, Object>();
        List list = new ArrayList();
        int total = 0;
        SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT);
        String sqlWhere = "";
        if (StringUtils.isNotEmpty(startTime)) {
            //时间格式转换成时间戳
            sqlWhere = sqlWhere + "and CC.ISTARTTIME >=" + sdf.parse(startTime.replace("T", " ")).getTime();
        }
        if (StringUtils.isNotEmpty(endTime)) {
            //时间格式转换成时间戳
            sqlWhere = sqlWhere + " and CC.IENDTIME <= " + sdf.parse(endTime.replace("T", " ")).getTime();
        }
        switch (state) {
            case "0":
                sqlWhere = sqlWhere + " and cc.ISCORESTATE is null ";
                break;
            case "1":
                sqlWhere = sqlWhere + " and cc.ISCORESTATE =1 ";
                break;
            default:
                ;
        }
        if (StringUtils.isNotEmpty(planType)) {
            if (planType.equals("0")) {
                sqlWhere = sqlWhere + " and cc.IPLANTYPE =0 ";
            } else if (planType.equals("1")) {
                sqlWhere = sqlWhere + " and cc.IPLANTYPE =1 ";
            } else {

            }
        }
        if (StringUtils.isNotEmpty(planName)) {
            sqlWhere = sqlWhere + " and UPPER(CC.IPLANNAME) like '%" + planName.toUpperCase() + "%'";
        }
        try {
            conn = DBManager.getInstance().getJdbcConnection(appType);
            String sql = "";
            switch (JudgeDB.IEAI_DB_TYPE) {
                case 1:
                case 4:
                    sql = " SELECT * FROM (SELECT CC.*, ROWNUM AS RN FROM IEAI_ESWITCH_PLAN_HIS CC  WHERE 1=1   " + sqlWhere + "  "
                            + " order by CC.IID DESC) WHERE RN<=? AND RN>?";
                    break;
                case 2:
                    sql = " SELECT * FROM (SELECT CC.*, ROWNUMBER() OVER(order by CC.IID DESC) AS RN FROM IEAI_ESWITCH_PLAN_HIS CC  WHERE 1=1  " + sqlWhere + " "
                            + ") WHERE RN>? AND RN<=?";
                    break;
                case 3:
                    sql = "SELECT * FROM (SELECT CC.* FROM IEAI_ESWITCH_PLAN_HIS CC   WHERE 1=1  " + sqlWhere + " "
                            + " order by CC.IID DESC) T limit ? , ?";
                    break;
            }
            ps = conn.prepareStatement(sql);
            switch (JudgeDB.IEAI_DB_TYPE) {
                case 1:
                case 4:
                    ps.setInt(1, start + limit);
                    ps.setInt(2, start);
                    break;
                case 2:
                    ps.setInt(1, start);
                    ps.setInt(2, start + limit);
                    break;
                case 3:
                    ps.setInt(1, start);
                    ps.setInt(2, start + limit);
                    break;
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                Map<String, Object> bean = new HashMap<String, Object>();
                bean.put("iid", rs.getInt("IID"));
                List<String> sceneName = new ArrayList<>();
                String sceneSql = "SELECT A.*,B.ISCENENAME FROM IEAI_ESWITCH_PLAN_RELATION A LEFT JOIN IEAI_INSTANCE_VERSION_DTO B ON A.ISCENEID = B.IID  WHERE A.IPLANID = ?  and itype=1 union \n" +
                        "SELECT A.*,B.iproject_logic_name as ISCENENAME FROM IEAI_ESWITCH_PLAN_RELATION A LEFT JOIN IEAI_PROJECT_LOGIC B ON A.ISCENEID = B.ilogic_id  WHERE A.IPLANID = ? and itype=2";
                ps1 = conn.prepareStatement(sceneSql);
                ps1.setLong(1, rs.getInt("IID"));
                ps1.setLong(2, rs.getInt("IID"));
                rs1 = ps1.executeQuery();
                while (rs1.next()) {
                    sceneName.add(rs1.getString("iscenename"));
                }
                bean.put("sceneName", String.join(",", sceneName));
                bean.put("iPlanName", rs.getString("IPLANNAME"));
                bean.put("iStartTime", sdf.format(new Date(Long.valueOf(rs.getLong("ISTARTTIME")))));
                bean.put("iEndTime", sdf.format(new Date(Long.valueOf(rs.getLong("IENDTIME")))));
                bean.put("iRealStartTime", sdf.format(new Date(Long.valueOf(rs.getLong("IREALSTARTTIME")))));
                bean.put("iRealRndTime", sdf.format(new Date(Long.valueOf(rs.getLong("IREALENDTIME")))));
                bean.put("iStartTimeLong", rs.getLong("ISTARTTIME"));
                bean.put("iEndTimeLong", rs.getLong("IENDTIME"));
                bean.put("iRealStartTimeLong", rs.getLong("IREALSTARTTIME"));
                bean.put("iRealEndTimeLong", rs.getLong("IREALENDTIME"));
                bean.put("iPlanType", rs.getString("IPLANTYPE"));
                bean.put("iScoreState", rs.getString("ISCORESTATE"));
                list.add(bean);
            }
            sql = " SELECT COUNT(*) FROM IEAI_ESWITCH_PLAN_HIS cc WHERE 1=1   " + sqlWhere + "";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                total = rs.getInt(1);
                break;
            }
            map.put("dataList", list);
            map.put("total", total);
        } catch (Exception e) {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + e.getMessage());
        } finally {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public void saveDrillAssessmentManager(String id, String user, List<DrillAssessmentBean> drillAssessmentBeans, int appType)
            throws RepositoryException {
        PreparedStatement actStat = null;
        PreparedStatement update = null;
        Connection con = null;
        for (int i = 0; i < drillAssessmentBeans.size(); i++) {
            con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log, appType);
            String sql = "INSERT INTO IEAI_ESWITCH_PLAN_RESULT(iid,ISCOREID,IEVALUATE,ISUGGEST,PLANHIDID,ICREATOR,ICREATETIME) VALUES (?,?,?,?,?,?,?)";
            String updateSql = "update  IEAI_ESWITCH_PLAN_HIS set ISCORESTATE=? where iid=? ";
            long iid = IdGenerator.createId("IEAI_ESWITCH_PLAN_RESULT", con);
            try {
                actStat = con.prepareStatement(sql);
                actStat.setLong(1, iid);
                actStat.setLong(2, drillAssessmentBeans.get(i).getIid());
                actStat.setString(3, drillAssessmentBeans.get(i).getEvaluate());
                actStat.setString(4, drillAssessmentBeans.get(i).getSuggest());
                actStat.setString(5, id);
                actStat.setString(6, user);
                actStat.setLong(7, System.currentTimeMillis());
                actStat.executeUpdate();

                update = con.prepareStatement(updateSql);
                update.setLong(1, 1);
                update.setString(2, id);
                update.executeUpdate();
                con.commit();
            } catch (SQLException e) {
                _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + e.getMessage());
                throw new RepositoryException(ServerError.ERR_DB_UPDATE);
            } finally {
                DBResource.closePSConn(con, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            }
        }
    }

    public Map getDrillResultManager(String iid, int start, int limit, int appType) throws ParseException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        // 数据库连接对象
        Connection conn = null;
        Map map = new HashMap<String, Object>();
        List list = new ArrayList();
        int total = 0;
        SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT);
        String sqlWhere = " and cc.PLANHIDID =" + iid + "";
        try {
            conn = DBManager.getInstance().getJdbcConnection(appType);
            String sql = "";
            switch (JudgeDB.IEAI_DB_TYPE) {
                case 1:
                case 4:
                    sql = " SELECT * FROM (SELECT CC.*, m.ISCORENAME,m.ISCOREDESC,ROWNUM AS RN FROM IEAI_ESWITCH_PLAN_RESULT CC     left join IEAI_ESWITCH_PLAN_HIS h on cc.PLANHIDID = h.iid left join IEAI_ESWITCH_PLAN_SCORE_MODEL m on cc.ISCOREID=m.IID  WHERE 1=1   " + sqlWhere + "  "
                            + " order by m.IID asc) WHERE RN<=? AND RN>?";
                    break;
                case 2:
                    sql = " SELECT * FROM (SELECT CC.*, m.ISCORENAME,m.ISCOREDESC, ROWNUMBER() OVER(order by CC.IID DESC) AS RN FROM IEAI_ESWITCH_PLAN_RESULT CC      left join IEAI_ESWITCH_PLAN_HIS h on cc.PLANHIDID = h.iid left join IEAI_ESWITCH_PLAN_SCORE_MODEL m on cc.ISCOREID=m.IID  WHERE 1=1  " + sqlWhere + " "
                            + "order by m.IID asc) WHERE RN>? AND RN<=?";
                    break;
                case 3:
                    sql = "SELECT * FROM (SELECT CC.*, m.ISCORENAME,m.ISCOREDESC FROM IEAI_ESWITCH_PLAN_RESULT CC     left join IEAI_ESWITCH_PLAN_HIS h on cc.PLANHIDID = h.iid left join IEAI_ESWITCH_PLAN_SCORE_MODEL m on cc.ISCOREID=m.IID  WHERE 1=1  " + sqlWhere + " "
                            + " order by m.IID asc) T limit ? , ?";
                    break;
            }
            ps = conn.prepareStatement(sql);
            switch (JudgeDB.IEAI_DB_TYPE) {
                case 1:
                case 4:
                    ps.setInt(1, start + limit);
                    ps.setInt(2, start);
                    break;
                case 2:
                    ps.setInt(1, start);
                    ps.setInt(2, start + limit);
                    break;
                case 3:
                    ps.setInt(1, start);
                    ps.setInt(2, start + limit);
                    break;
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                Map<String, Object> bean = new HashMap<String, Object>();
                bean.put("iid", rs.getInt("IID"));
                bean.put("iScoreName", rs.getString("ISCORENAME"));
                bean.put("iScoreDesc", rs.getString("ISCOREDESC"));
                bean.put("iCreator", rs.getString("ICREATOR"));
                bean.put("iCreateTime", sdf.format(new Date(Long.valueOf(rs.getLong("ICREATETIME")))));
                bean.put("evaluate", rs.getLong("IEVALUATE"));
                bean.put("iSuggest", rs.getString("ISUGGEST"));
                list.add(bean);
            }
            sql = " SELECT COUNT(*) FROM IEAI_ESWITCH_PLAN_RESULT cc WHERE 1=1   " + sqlWhere + "";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                total = rs.getInt(1);
                break;
            }
            map.put("dataList", list);
            map.put("total", total);
        } catch (Exception e) {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + e.getMessage());
        } finally {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public Map exportBaseinfoDetailManager(String iid, int appType) throws ParseException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        PreparedStatement ps2 = null;
        ResultSet rs2 = null;
        // 数据库连接对象
        Connection conn = null;
        Map bsaeMap = new HashMap();
        SimpleDateFormat sdf = new SimpleDateFormat(DATEFORMAT);
        String dataformat = "yyyy-MM-dd";
        SimpleDateFormat sdf1 = new SimpleDateFormat(dataformat);
        String sqlWhere = " and h.iid =" + iid + "";
        try {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.MONTH, +1);
            String year2 = String.valueOf(cal.get(Calendar.YEAR)).replace("，", "");// 当前年
            String month2 = String.valueOf(cal.get(Calendar.MONTH));// 当前月份
            String day2 = String.valueOf(cal.get(Calendar.DAY_OF_MONTH));// 当前日期

            conn = DBManager.getInstance().getJdbcConnection(appType);
            String sql = "SELECT CC.ICREATETIME,h.* FROM IEAI_ESWITCH_PLAN_HIS h left join IEAI_ESWITCH_PLAN_RESULT CC on cc.PLANHIDID = h.iid  left join IEAI_ESWITCH_PLAN_SCORE_MODEL m on cc.ISCOREID = m.IID WHERE 1 = 1 " + sqlWhere + " order by CC.IID DESC";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();

            while (rs.next()) {
                Map<String, Object> bean = new HashMap<String, Object>();
                List<String> sceneName = new ArrayList<>();
                Set<String> businessName = new HashSet<>();
                String sceneSql = "SELECT A.*,B.ISCENENAME, B.IINSTANCENAME  FROM IEAI_ESWITCH_PLAN_RELATION A LEFT JOIN IEAI_INSTANCE_VERSION_DTO B ON A.ISCENEID = B.IID  WHERE A.IPLANID = ?";
                ps1 = conn.prepareStatement(sceneSql);
                ps1.setLong(1, rs.getInt("IID"));
                rs1 = ps1.executeQuery();
                while (rs1.next()) {
                    sceneName.add(rs1.getString("iscenename"));
                    businessName.add(rs1.getString("IINSTANCENAME"));
                }

//                List<Long> pFileIds = new ArrayList<>();
                List<String> pFileName = new ArrayList<>();
                String pFileSql = "SELECT A.IPFILEID, B.IPLANNAME FROM IEAI_ESWITCH_PLAN_PFILE A, IEAI_EMERGENCY_PLAN B WHERE A.IPLANID = ? AND A.IPFILEID = B.IID";
                ps2 = conn.prepareStatement(pFileSql);
                ps2.setLong(1, rs.getLong("IID"));
                rs2 = ps2.executeQuery();
                while (rs2.next()){
//                    pFileIds.add(rs.getLong("ipFileId"));
                    pFileName.add(rs2.getString("IPLANNAME"));
                }
//                bean.put("iPlanPfileName",StringUtils.isNotEmpty(String.join(",", pFileName)) ? String.join(",", pFileName) : "");//预案名称
//                bean.put("iplanPfile",pFileIds.toArray(new Long[0]));//预案id

                bean.put("iBusinessName", StringUtils.isNotEmpty(String.join(",", businessName)) ? String.join(",", businessName) : "");//场景业务名称
                bean.put("iSceneName", StringUtils.isNotEmpty(String.join(",", sceneName)) ? String.join(",", sceneName) : "");
                bean.put("iPlanName", StringUtils.isNotEmpty(rs.getString("IPLANNAME")) ? rs.getString("IPLANNAME") : "");
                bean.put("iAuditUserId",rs.getLong("iauditUserId"));//发布审批人id
                bean.put("iRemark", StringUtils.isNotEmpty(rs.getString("iremark")) ? rs.getString("iremark") : "");//备注
                bean.put("year", year2);
                bean.put("date", year2 + "年" + month2 + "月" + day2 + "日");
//                bean.put("iInstanceId", rs.getLong("IINSTANCEID"));
                bean.put("iPlace", StringUtils.isNotEmpty(rs.getString("IPLACE")) ? rs.getString("IPLACE") : "");
                bean.put("iPlanScope", StringUtils.isNotEmpty(rs.getString("IPLANSCOPE")) ? rs.getString("IPLANSCOPE") : "");
                bean.put("iStartTime", StringUtils.isNotEmpty(sdf.format(new Date(Long.valueOf(rs.getLong("ISTARTTIME"))))) ? sdf.format(new Date(Long.valueOf(rs.getLong("ISTARTTIME")))) : "");
                bean.put("iEndTime", StringUtils.isNotEmpty(sdf.format(new Date(Long.valueOf(rs.getLong("IENDTIME"))))) ? sdf.format(new Date(Long.valueOf(rs.getLong("IENDTIME")))) : "");
                bean.put("iRealStartTime", StringUtils.isNotEmpty(sdf.format(new Date(Long.valueOf(rs.getLong("IREALSTARTTIME"))))) ? sdf.format(new Date(Long.valueOf(rs.getLong("IREALSTARTTIME")))) : "");
                bean.put("iRealEndTime", StringUtils.isNotEmpty(sdf.format(new Date(Long.valueOf(rs.getLong("IREALENDTIME"))))) ? sdf.format(new Date(Long.valueOf(rs.getLong("IREALENDTIME")))) : "");
                bean.put("iCreateTime", StringUtils.isNotEmpty(sdf1.format(new Date(Long.valueOf(rs.getLong("ICREATETIME"))))) ? sdf1.format(new Date(Long.valueOf(rs.getLong("ICREATETIME")))) : "");
                long nd = 1000 * 24 * 60 * 60;
                long nh = 1000 * 60 * 60;
                long nm = 1000 * 60;
                long ns = 1000;
                // 获得两个时间的毫秒时间差异
                long diff = rs.getLong("IENDTIME") - rs.getLong("ISTARTTIME");
                if (diff > 0) {
                    // 计算差多少天
                    long day = diff / nd;
                    // 计算差多少小时
                    long hour = diff % nd / nh;
                    // 计算差多少分钟
                    long min = diff % nd % nh / nm;
                    // 计算差多少秒//输出结果
                    long sec = diff % nd % nh % nm / ns;
                    bean.put("iTime", day + "天" + hour + "小时" + min + "分钟" + sec + "秒");
                }else {
                    bean.put("iTime", "");
                }
                long diff1 = rs.getLong("IREALENDTIME") - rs.getLong("IREALSTARTTIME");
                if (diff1 > 0) {
                    long day1 = diff1 / nd;
                    // 计算差多少小时
                    long hour1 = diff1 % nd / nh;
                    // 计算差多少分钟
                    long min1 = diff1 % nd % nh / nm;
                    // 计算差多少秒//输出结果
                    long sec1 = diff1 % nd % nh % nm / ns;
                    bean.put("iRealTime", day1 + "天" + hour1 + "小时" + min1 + "分钟" + sec1 + "秒");
                }else{
                    bean.put("iRealTime", "");
                }
                String iplantype = rs.getString("IPLANTYPE");
                if (iplantype.equals("0")) {
                    iplantype = "桌面演练";
                }
                if (iplantype.equals("1")) {
                    iplantype = "真实切换";
                }
                bean.put("iPlanType", StringUtils.isNotEmpty(iplantype) ? iplantype : "");
                bsaeMap.put("baseInfo", bean);
                bsaeMap.put("iPlanName", rs.getString("IPLANNAME"));
            }
        } catch (Exception e) {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + e.getMessage());
        } finally {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return bsaeMap;
    }

    public Map exportDetailManager(String iid, int appType) throws ParseException {
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement userPs = null;
        ResultSet userRs = null;
        PreparedStatement orgPs = null;
        ResultSet orgRs = null;
        // 数据库连接对象
        Connection conn = null;
        Map bsaeMap = new HashMap();
        List list = new ArrayList();
        List userList = new ArrayList();
        String dataformat = "yyyy-MM-dd";
        SimpleDateFormat sdf = new SimpleDateFormat(dataformat);
        String sqlWhere = " and cc.PLANHIDID =" + iid + "";
        try {
            conn = DBManager.getInstance().getJdbcConnection(appType);
            String sql = "SELECT CC.*, m.ISCORENAME,m.ISCOREDESC FROM IEAI_ESWITCH_PLAN_RESULT CC  left join IEAI_ESWITCH_PLAN_HIS h on cc.PLANHIDID = h.iid left join IEAI_ESWITCH_PLAN_SCORE_MODEL m on cc.ISCOREID=m.IID  WHERE 1=1 " + sqlWhere + " order by m.IID asc";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            int i = 0;
            while (rs.next()) {
                Map<String, Object> bean = new HashMap<String, Object>();
                bean.put("order", ++i);
                bean.put("iScoreName", StringUtils.isNotEmpty(rs.getString("ISCORENAME")) ? rs.getString("ISCORENAME") : "");
                bean.put("iScoreDesc", StringUtils.isNotEmpty(rs.getString("ISCOREDESC")) ? rs.getString("ISCOREDESC") : "");
                bean.put("iEvaluate", rs.getLong("IEVALUATE") != 0 ? rs.getLong("IEVALUATE") : 0);
                bean.put("iSuggest", StringUtils.isNotEmpty(rs.getString("ISUGGEST")) ? rs.getString("ISUGGEST") : "");
//                bean.put("iCreateTime", sdf.format(new Date(Long.valueOf(rs.getLong("ICREATETIME")))));
                list.add(bean);
            }
            if (list.size() == 0) {
                Map<String, Object> bean = new HashMap<String, Object>();
                bean.put("order", "");
                bean.put("iScoreName", "");
                bean.put("iScoreDesc", "");
                bean.put("iEvaluate", "");
                bean.put("iSuggest", "");
                list.add(bean);
            }
            String userSql = "SELECT A.IID, B.IFULLNAME, B.ITELEPHONE,B.IEMAIL,A.ISIGNSTATE, A.ISIGNTIME, A.IUSERID, A.IORMID, C.IPOST, C.IDIVISIONLABOR  FROM IEAI_ESWITCH_PLAN_SIGN A LEFT JOIN IEAI_ORGMANAGEMENT_USER C ON C.MANAGEMENTID = A.IORMID AND A.IUSERID = C.USERID, IEAI_USER B WHERE A.IUSERID = B.IID AND A.IPLANID = ?";
            String orgSql = "SELECT INAME,IPARENTID FROM IEAI_ORGMANAGEMENT WHERE IID = ?";
            userPs = conn.prepareStatement(userSql);
            userPs.setString(1, iid);
            userRs = userPs.executeQuery();
            int j = 0;
            while (userRs.next()) {
                Map<String, Object> bean = new HashMap<String, Object>();
                bean.put("order", ++j);// 序号
                bean.put("iUserName", StringUtils.isNotEmpty(userRs.getString("IFULLNAME")) ? userRs.getString("IFULLNAME") : "");// 用户名称
                bean.put("iPost", userRs.getString("IPOST") == null ? "" : userRs.getString("IPOST"));// 岗位
                bean.put("iDivisionLabor", userRs.getString("IDIVISIONLABOR") == null ? "" : userRs.getString("IDIVISIONLABOR"));// 分工
                orgPs = conn.prepareStatement(orgSql);
                orgPs.setLong(1, userRs.getLong("IORMID"));
                List<String> orgs = new ArrayList<>();
                orgRs = orgPs.executeQuery();
                while (orgRs.next()) {
                    List<String> orgList = new ArrayList<>();
                    orgList.add(orgRs.getString("INAME"));
                    List<String> descSort = new ArrayList<>();
                    getFirstLevelORG(conn, userRs.getLong("IORMID"), orgList);
                    for (int k = orgList.size() - 1; k > 0; k--) {
                        descSort.add(orgList.get(k));
                    }
                    orgs.add(String.join("->", descSort));
                }
                bean.put("iOrg", StringUtils.isNotEmpty(String.join(",", orgs)) ? String.join(",", orgs) : "");// 组织

                userList.add(bean);
            }
            if (userList.size() == 0) {
                Map<String, Object> bean = new HashMap<String, Object>();
                bean.put("order", "");
                bean.put("iUserName", "");
                bean.put("iPost", "");
                bean.put("iDivisionLabor", "");
                bean.put("iOrg", "");
                userList.add(bean);
            }
            bsaeMap.put("table", list);
            bsaeMap.put("user", userList);
        } catch (Exception e) {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + e.getMessage());
        } finally {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConn(conn, userRs, userPs, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closeConn(conn, orgRs, orgPs, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return bsaeMap;
    }

    public void getFirstLevelORG(Connection conn, long iid, List<String> orgList) {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT INAME,IPARENTID FROM IEAI_ORGMANAGEMENT WHERE IID = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next()) {
                orgList.add(rs.getString("iname"));
                if (rs.getLong("iparentid") == 0) {

                } else {
                    getFirstLevelORG(conn, rs.getLong("iparentid"), orgList);
                }
            }
        } catch (Exception e) {
            _log.error("获取所属组织", e);
        } finally {
            DBResource.closePSRS(rs, ps, "getFirstLevelORG", _log);
        }
    }
}
