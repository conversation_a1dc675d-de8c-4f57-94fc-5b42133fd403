var winalarm ;
var actWarningStore ;
var warnTabPanel;
var actWarningGrid;
var batchNumber;
var pid;
Ext.onReady (function ()
{
	var newAlarmDealMsgForm;
	var alarmDealMsgComBoxStore;
	var alarmBatchComBoxStore;
	var dealAlarmBtn;
	var dealMsgWidow;
	var batchAlarmBtn;
	var batchWidow;
	var alarmDealMsgForm;
	var alarmBatchMsgForm;
	var aid;
//	var warnTabPanel;
	var alarmDealMsgComBox;
	var alarmBatchComBox;
	Ext.define('actWarningModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'sid',
			type : 'string'
		}, {
			name : 'wDate',
			type : 'string'
		}, {
			name : 'lDate',
			type : 'string'
		}, {
			name : 'sysName',
			type : 'string'
		}, {
			name : 'warningIP',
			type : 'string'
		}, {
			name : 'warningType',
			type : 'string'
		}, {
			name : 'warningLevel',
			type : 'string'
		}, {
			name : 'warningCode',
			type : 'string'
		}, {
			name : 'warningMsg',
			type : 'string'
		}, {
			name : 'warningCount',
			type : 'string'
		} , {
			name : 'prgName',
			type : 'string'
		} , {
			name : 'cpname',
			type : 'string'
		} , {
			name : 'disposeType',
			type : 'string'
		}, {
			name : 'taskname',
			type : 'string'
		}, {
			name : 'sysusers',
			type : 'string'
		}, {
			name : 'batchNumberUp',
			type : 'string'
		}, {
			name : 'pointid',
			type : 'string'
		}
		]
	});
	// 异常活动列
	var actWarningColumns = [ {
		text : '序号',
		xtype : 'rownumberer',
		width : 45
	},{
		xtype : 'actioncolumn',
		text : '操作',
		align : 'center',
		width : 50,
		// flex : 1,
		sortable : false,
		menuDisabled : true,
		/*locked:true,*/
		items : [
			{
				icon : 'images/pencil.png',
				tooltip : '处理报警',
				handler : function(grid, rowIndex) {
					var sid = grid.getStore().getAt(rowIndex).get('sid');
					var sysName = grid.getStore().getAt(rowIndex).get('sysName');
					if (isgfswitch) {
						var pointId = grid.getStore().getAt(rowIndex).get('pointid');
						var batchId = grid.getStore().getAt(rowIndex).get('batchNumberUp');
						showDealMsgWinNew(sid,sysName,pointId, batchId);
					} else {
						showDealMsgWin(sid,sysName);
					}
				}
			} ]
	}, {
		text : 'batchNumberUp',
		dataIndex : 'batchNumberUp',
		hidden : true
	}, {
		text : 'sid',
		dataIndex : 'sid',
		hidden : true
	}, {
		text : '巡检任务名称',
		dataIndex : 'taskname'
		,width:100,
		hidden:!isgfflag,
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		},
		menuDisabled : true
	},{
		text : isgfflag?'业务系统':'业务系统名称',
		dataIndex : 'sysName',
		width:100,
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		}
		,menuDisabled : true,
	},{
		text : '服务器IP',
		dataIndex : 'warningIP',
		width:120,
		/*locked:true,*/
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		}
	},{
		text : '负责人',
		dataIndex : 'sysusers',
		width:100,
		hidden:notisbhnsflag,
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		}
	},{
		text : '主机名称',
		dataIndex : 'cpname',
		width:110,
		locked:true,
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		}
	}, {
		dataIndex : 'warningCount',
		text : '报警次数',
		sortable : true,
		width:80
	}, {
		dataIndex : 'warningLevel',
		text : '异常等级',
		sortable : true,
		width:80,
		renderer : getWcodeValue
		/*locked:true*/
	}, {
		dataIndex : 'prgName',
		text : '脚本名称',
		width:130,
		/*locked:true,*/
		hidden : true,
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		}
	}, {
		dataIndex : 'warningMsg',
		text : '异常说明',
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		}
		,
		flex : 1
	} ,  {
		dataIndex : 'warningCode',
		text : '错误码',
		hidden : true,
		renderer:function(value,p,record){
			p.tdAttr = " data-qtip = '"+value+"'";
			return value;
		},
		width:80
	}, {
		dataIndex : 'warningType',
		text : '巡检项分类',
		width:80,
		/*locked:true*/
	}, {
		text : '首次报警时间',
		dataIndex : 'wDate'
		,width:160,
		menuDisabled : true
	}, {
		text : '最近报警时间',
		dataIndex : 'lDate'
		,width:160,
		menuDisabled : true
	}, {
		dataIndex : 'disposeType',
		text : '处理方式',
		width:80,
		renderer:function(value,p,record){
			if(value=="1"){
				return (notisbhnsflag?"已知待处理":"人工确认");
			}
			return "未处理";
		}
	}];
	if(isFjnxflag){
		actWarningColumns = [ {
			text : '序号',
			xtype : 'rownumberer',
			width : 45
		
		},{
			text : '业务系统名称',
					dataIndex : 'sysName',
					width:100,
					renderer:function(value,p,record){
						p.tdAttr = " data-qtip = '"+value+"'";
						return value;
					}
					,menuDisabled : true,
		},{
			text : '服务器IP',
			dataIndex : 'warningIP',
			width:120,
			/*locked:true,*/
			renderer:function(value,p,record){
				p.tdAttr = " data-qtip = '"+value+"'";
				return value;
			}
		}, {
			dataIndex : 'warningCount',
			text : '告警次数',
			sortable : true,
			width:80
		}, {
			dataIndex : 'warningLevel',
			text : '异常等级',
			sortable : true,
			width:80,
			renderer : getWcodeValue
			/*locked:true*/
		}, {
			dataIndex : 'warningMsg',
			text : '异常信息',
			renderer:function(value,p,record){
				p.tdAttr = " data-qtip = '"+value+"'";
				return value;
			}
			,
			flex : 1
		}, {
			text : '首次报警时间',
			dataIndex : 'wDate'
			,width:160,
			menuDisabled : true
		}, {
			text : '最近报警时间',
			dataIndex : 'lDate'
			,width:160,
			menuDisabled : true
		}, {
			dataIndex : 'disposeType',
			text : '处理方式',
			width:80,
			renderer:function(value,p,record){
				if(value=="1"){
					return (notisbhnsflag?"已知待处理":"人工确认");
				}
				return "未处理";
			}
		},{
			xtype : 'actioncolumn',
			text : '操作',
			align : 'center',
			width : 50,
			// flex : 1,
			sortable : false,
			menuDisabled : true,
			/*locked:true,*/
			items : [
				{
					icon : 'images/pencil.png',
					tooltip : '处理报警',
					handler : function(grid, rowIndex) {
						var sid = grid.getStore().getAt(rowIndex).get('sid');
						var sysName = grid.getStore().getAt(rowIndex).get('sysName');
						if (isgfswitch) {
							var pointId = grid.getStore().getAt(rowIndex).get('pointid');
							var batchId = grid.getStore().getAt(rowIndex).get('batchNumberUp');
							showDealMsgWinNew(sid,sysName,pointId, batchId);
						} else {
							showDealMsgWin(sid,sysName);
						}
					}
				} ]
		}];
	}

	//异常等级

	var errLevelStore = Ext.create('Ext.data.Store', {
		fields: ['dcode', 'dname'],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'queryWarnigLevelList.do',
			actionMethods : 'post',
			reader : {
				type : 'json',
				root : 'warningLevelList'
			}
		}
	});


	var errLevelComboBox = Ext.create('Ext.form.field.ComboBox', {
		fieldLabel:'异常等级:',
		name : 'errLevel',
		labelWidth : 60,
		padding : 5,
		store : errLevelStore,
		queryMode : 'local',
		width : 150,
		value : '-1',
		editable:false,
		displayField : 'dname',
		valueField : 'dcode',
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					queryWarning();
				}
			}
		}
	});
	//异常类型
	var errTypeStore = Ext.create('Ext.data.Store', {
		fields: ['dcode', 'dname'],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'queryWarnigTypeList.do',
			actionMethods : 'post',
			reader : {
				type : 'json',
				root : 'warningTypeList'
			}
		}
	});

	var errTypeComboBox = Ext.create('Ext.form.field.ComboBox', {
		fieldLabel:'巡检项分类:',
		name : 'errType',
		labelWidth : 80,
		padding : 5,
		store : errTypeStore,
		hidden :true,
		queryMode : 'local',
		width : 170,
		value : '0',
		editable:false,
		displayField : 'dname',
		valueField : 'dcode',
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					queryWarning();
				}
			}
		}
	});



	/** 业务系统名称*/
	var sysName = Ext.create ('Ext.form.TextField',
		{
			margin : '5',
			emptyText : '--请输入业务系统名称--',
			width : 160,
			xtype : 'textfield',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						queryWarning();
					}
				}
			}
		});

	/** 错误码查询输入框* */
	var errCode = Ext.create ('Ext.form.TextField',
		{
			margin : '5',
			emptyText : '--请输入错误码--',
			width : 130,
			hidden : true,
			xtype : 'textfield',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						queryWarning();
					}
				}
			}
		});

	/** 开始时间* */
	var beginTime = Ext.create ('Ext.form.DateField',
		{
			labelWidth : 70,
			emptyText : '--请输入开始时间--',
			name : 'beginTime',
			hidden :isFjnxflag ,
			width : 165,
			margin : '5',
			format : 'Y-m-d H:i:s',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						queryWarning();
					}
				}
			}
			//padding : '0 10 0 0'
		});
	/** 结束时间* */
	var endTime = Ext.create ('Ext.form.DateField',
		{
			name : 'endTime',
			emptyText : '--请输入结束时间--',
			hidden :isFjnxflag ,
			labelWidth : 19,
			width : 190,
			margin : '5',
			format : 'Y-m-d H:i:s',
			fieldLabel : '至',
			labelSeparator : '',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						queryWarning();
					}
				}
			}
			//padding : '0 10 0 0'
		});
	
	/** 开始时间* */
	var beginTimeF = Ext.create ('Ext.form.DateField',
		{
			labelWidth : 70,
			emptyText : '--请输入开始时间--',
			name : 'beginTimeF',
			width : 165,
			margin : '5',
			hidden : !isFjnxflag ,
			value : '',
			format : 'Y-m-d H:i:s',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						queryWarning();
					}
				}
			}
			//padding : '0 10 0 0'
		});
	/** 结束时间* */
	var endTimeF = Ext.create ('Ext.form.DateField',
		{
			name : 'endTimeF',
			emptyText : '--请输入结束时间--',
			labelWidth : 19,
			hidden :!isFjnxflag ,
			width : 190,
			margin : '5',
			value : '',
			format : 'Y-m-d H:i:s',
			fieldLabel : '至',
			labelSeparator : '',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						queryWarning();
					}
				}
			}
			//padding : '0 10 0 0'
		});
	var ipText = Ext.create('Ext.form.TextField', {
		margin : '5',
		emptyText : '---请输入IP---',
		enableKeyEvents : true,
		width : 130,
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					queryWarning();
				}
			}
		}
	});

	var queryBtn = Ext.create('Ext.Button',{
		text : '查询',
		icon : '',
		baseCls : 'Common_Btn',
		handler : queryWarning
	});

	var exportBtn = Ext.create('Ext.Button',{
		text : '导出',
		icon : '',
		baseCls : 'Common_Btn',
		handler : exportWarning
	});

	var batchBtn = Ext.create('Ext.Button',{
		text : '批处理',
		icon : '',
		baseCls : 'Common_Btn',
		//handler : exportWarning
		handler : showBatchWin
	});

	var timePanel = Ext.create('Ext.form.Panel', {
		layout : 'column',
		border : false,
		items : [beginTime, endTime,errTypeComboBox,errLevelComboBox]});

	var selectPanel = Ext.create('Ext.form.Panel', {
		layout : 'column',
		border : false,//exportBtn,handleBtn
		items : [sysName,ipText,errTypeComboBox,errLevelComboBox,errCode,beginTime,endTime,beginTimeF,endTimeF,queryBtn,exportBtn,batchBtn]});
	// 定义活动异常store


	actWarningStore = Ext.create('Ext.data.Store', {
		storeId : 'actWarningStore',
		autoLoad : false,
		remoteSort: true,
		model : 'actWarningModel',
		//pageSize : warPageNum,
		pageSize : 30,
		proxy : {
			type : 'ajax',
			url : (isgfflag || !notisbhnsflag)?'hcInspWarnQueryActWarningForGFmonitor.do':'queryActWarning.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		},
		listeners: {
			'beforeload': function (store, op, options) {
				var params = {
					beginTime: beginTime.value,
					endTime: endTime.value,
					ipText: ipText.value,
					errType:  errTypeComboBox.value,
					errLevel: errLevelComboBox.value,
					errCode: errCode.value,
					sysName: sysName.value
				};
				if(isFjnxflag){
					params = {
							beginTimeF: beginTimeF.value,
							endTimeF: endTimeF.value,
							ipText: ipText.value,
							errType:  errTypeComboBox.value,
							errLevel: errLevelComboBox.value,
							errCode: errCode.value,
							sysName: sysName.value
						};
				}
				Ext.apply(store.proxy.extraParams, params);
			}
		}
	});
	// 异常活动数据源传参
	actWarningStore.on('beforeload', function(store, options) {
		var newparams = {
			/**start : 0,
			 limit : warPageNum*/
		};
		Ext.apply(actWarningStore.proxy.extraParams, newparams);
	});

	// 分页工具
	var actpageBar = Ext.create('Ext.PagingToolbar', {
		//pageSize : warPageNum,
		store : actWarningStore,
		displayInfo : true,
		displayMsg : '显示{0}-{1}条，共{2}条',
		emptyMsg : "没有异常信息"
	});

	var formPanel = Ext.create('Ext.ux.ideal.form.Panel',{
		border : false,
		height : 50,
		bodyPadding : 5,
		items : [selectPanel]
	});
	// 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
//		id:'selModel',
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});

	actWarningGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		columnLines : true,
		height : 430,
		width : 1280,
		columns : actWarningColumns,
		store : actWarningStore,
		//bbar : actpageBar,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		loadMask : {
			msg : " 数据加载中，请稍等 "
		},
		selModel:selModel,
		dockedItems : [ formPanel ]
	});
//  var actWarningPanel = Ext.create('Ext.panel.Panel', {
//		border : true,
//		layout : "border",
//		height : contentPanel.getHeight()-450,
//		width  : contentPanel.getWidth(),
//		items: [actWarningGrid]
//	});



	batchAlarmBtn= Ext.create('Ext.Button',{
		text : '提交',
		style : 'margin-left:265px',
		handler : batchActWarning
	});

	warnTabPanel =  Ext.create('Ext.tab.Panel',{
		/*height : '100%',
        width : '100%',*/
		items:[
//    		 {
//	    		 title: '活动系统异常',
//	    		 items: actWarningGrid
//	    	 }
		]
	});
	// 处理异常活动记录
	function dealActWarning() {
		var disposeType = alarmDealMsgComBox.getValue();
		var msg =alarmDealMsgForm.getForm().findField('smessgae').getValue();
		if(trim(disposeType)==''||disposeType==null){
			Ext.Msg.show({
				title : '提示',
				msg : '处理方式不能为空！',
				buttons:Ext.MessageBox.OK
			});
			return;
		}
		if(trim(msg)==''){
			Ext.Msg.show({
				title : '提示',
				msg : '处理意见不能为空！',
				buttons:Ext.MessageBox.OK
			});
			return;
		}
		if(msg.length>100){
			Ext.Msg.show({
				title : '提示',
				msg : '处理意见不能超过100',
				buttons:Ext.MessageBox.OK
			});
			return;
		}
		Ext.Ajax.request({
			url : 'dealActWarning.do',
			params : {
				aid : aid,
				msg:msg,
				disposeType:disposeType
			},
			method : 'post',
			success : function(response, text) {
				if (response.responseText) {
					Ext.Msg.show({
						title : '提示',
						msg : '报警处理成功！',
						buttons:Ext.MessageBox.OK
					});
					alarmDealMsgForm.getForm().findField('smessgae').setValue('');
					actWarningStore.load();
					showOrHiddenForActWarning();
					dealMsgWidow.close();
				} else {
					Ext.Msg.show({
						title : '提示',
						msg : '报警处理失败！',
						buttons:Ext.MessageBox.OK
					});
				}
			}
		});
		operAlarmWin(5,"DEALALARM");
	}

	function batchActWarning(){

		batchAlarmBtn.setDisabled(true);
		var disposeType = alarmBatchComBox.getValue();
		var msg =alarmBatchMsgForm.getForm().findField('bmessgae').getValue();
		var data = actWarningGrid.getView().getSelectionModel().getSelection();
		if (data.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条记录!');
			batchAlarmBtn.setDisabled(false);
			return;
		}

		if(trim(disposeType)==''||disposeType==null){
			Ext.Msg.show({
				title : '提示',
				msg : '处理方式不能为空！',
				buttons:Ext.MessageBox.OK
			});
			batchAlarmBtn.setDisabled(false);
			return;
		}
		if(trim(msg)==''){
			Ext.Msg.show({
				title : '提示',
				msg : '处理意见不能为空！',
				buttons:Ext.MessageBox.OK
			});
			batchAlarmBtn.setDisabled(false);
			return;
		}
		if(msg.length>100){
			Ext.Msg.show({
				title : '提示',
				msg : '处理意见不能超过100',
				buttons:Ext.MessageBox.OK
			});
			batchAlarmBtn.setDisabled(false);
			return;
		}

		var ids = [];
		for (var i = 0; i < data.length; i++) {
			ids.push(data[i].get('sid'));
		}
		//console.log('--->',ids)
		Ext.Ajax.request({
			url : 'batchActWarning.do',
			params : {
				batchIds : ids,
				msg:msg,
				disposeType:disposeType
			},
			method : 'post',
			success : function(response, text) {
				if (response.responseText) {
					Ext.Msg.show({
						title : '提示',
						msg : '报警处理成功！',
						buttons:Ext.MessageBox.OK
					});
					alarmBatchMsgForm.getForm().findField('bmessgae').setValue('');
					actWarningStore.load();
					showOrHiddenForActWarning();
					batchWidow.close();
				} else {
					Ext.Msg.show({
						title : '提示',
						msg : '报警处理失败！',
						buttons:Ext.MessageBox.OK
					});
				}
				batchAlarmBtn.setDisabled(false);
			}
		});
	}

	winalarm = Ext.create('Ext.Window', {
		title : '异常信息展示',
		height : 550,
		width : 1300,
		constrain : true,
		border : false,
		modal : true,
		closeAction : 'hide',
		maximizable : true,
		autoScroll : true,
		layout : 'fit',
		items : [warnTabPanel],//actWarningGrid
		listeners : {
			minimize : function() {
				var me = this;
				me.hide();
			}
		}

	});
	winalarm.on("close",function(){
		operAlarmWin(4,"OUTDISPLAYALARM");

	});

	function exportWarning(){
		if(!beginTime.isValid()){
			Ext.Msg.alert('提示', "开始时间格式错误,请重新输入。");
			return ;
		}
		if(!endTime.isValid()){
			Ext.Msg.alert('提示', "结束时间格式错误,请重新输入。");
			return ;
		}
		if(beginTime.getRawValue().length>0 &&endTime.getRawValue().length){
			if(beginTime.getRawValue()>endTime.getRawValue())
			{
				Ext.Msg.alert('提示', '开始时间不能大于结束时间');
				return;
			}
		}

		var beginTimeExp=beginTime.getRawValue();
		var endTimeExp=endTime.getRawValue();
		var ipTextExp=ipText.value;
		var errTypeExp=errTypeComboBox.value;
		var errLeveExp=errLevelComboBox.value;
		var errCodeExp=errCode.value;
		var sysNameExp=sysName.value;
		var startExp = 0;
		var limitExp = 65530;


		if (typeof(beginTimeExp) == "undefined" || beginTimeExp == "null") {
			beginTimeExp = "";
		}

		if (typeof(endTimeExp) == "undefined" || endTimeExp == "null") {
			endTimeExp = "";
		}

		if (typeof(ipTextExp) == "undefined" || ipTextExp == "null") {
			ipTextExp = "";
		}

		if (typeof(errTypeExp) == "undefined" || errTypeExp == "null" ) {
			errTypeExp = "";
		}
		if (typeof(errLeveExp) == "undefined" || errLeveExp == "null" ) {
			errLeveExp = "";
		}
		if (typeof(errCodeExp) == "undefined"  || errCodeExp == "null" ) {
			errCodeExp = "";
		}
		if (typeof(sysNameExp) == "undefined" || sysNameExp == "null" ) {
			sysNameExp = "";
		}

		var url ='exportWarning.do?beginTime=' +beginTimeExp
			+'&endTime='+ endTimeExp
			+'&ipText='+ipTextExp
			+'&errType='+errTypeExp
			+'&errLevel='+errLeveExp
			+'&errCode='+errCodeExp
			+'&sysName='+encodeURI(sysNameExp)
			+'&start='+startExp
			+'&limit='+limitExp;
		window.location.href=url;

		//window.location.href =`exportWarning.do?beginTime=${beginTime1}&endTime=${endTim1e}`;
	}

	function queryWarning()
	{
		if(!beginTime.isValid()){
			Ext.Msg.alert('提示', "开始时间格式错误,请重新输入。");
			return ;
		}
		if(!endTime.isValid()){
			Ext.Msg.alert('提示', "结束时间格式错误,请重新输入。");
			return ;
		}
		if(beginTime.getRawValue().length>0 &&endTime.getRawValue().length){
			if(beginTime.getRawValue()>endTime.getRawValue())
			{
				Ext.Msg.alert('提示', '开始时间不能大于结束时间');
				return;
			}
		}
		actpageBar.moveFirst();
	}

	function getWcodeValue(value, metaData, record) {
		if(isLevelConvertSwitch){
			if ("0" == value) {
				return "正常("+value+")";
			} else if ("1" == value) {
				return "关注(5)";
			} else if ("2" == value) {
				return "一般(4)";
			} else if ("3" == value) {
				return "次要("+value+")";
			} else if ("4" == value) {
				return "警告(2)";
			} else if ("5" == value) {
				return "严重(1)";
			}
		}else{
			if ("0" == value) {
				return "正常("+value+")";
			} else if ("1" == value) {
				return "关注("+value+")";
			} else if ("2" == value) {
				return "一般("+value+")";
			} else if ("3" == value) {
				return "次要("+value+")";
			} else if ("4" == value) {
				return "警告("+value+")";
			} else if ("5" == value) {
				return "严重("+value+")";
			}
		}
	}


	// refreshHtml();

	function showDealMsgWin(alarmId,systemName){
		aid="";
		aid=alarmId;
		if (dealMsgWidow == undefined || !dealMsgWidow.isVisible ())
		{
			alarmDealMsgComBoxStore = Ext.create('Ext.data.Store',  {
				fields: ['key','value'],
				data: [
					{key : '1', value: (notisbhnsflag?'已知待处理':'人工确认')},
					{key : '2', value: (notisbhnsflag?'立即处理':'恢复正常')}
				]
			});

			alarmDealMsgComBox = Ext.create('Ext.form.field.ComboBox', {
				name : 'alarmDealMsgComBox',
				labelWidth : 60,
				anchor : '100%',
				fieldLabel : "处理方式",
				emptyText : '--请选择处理方式--',
				//allowBlank : false,
				triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
				editable : false,// 是否可输入编辑
				store: alarmDealMsgComBoxStore,
				displayField : 'value',
				valueField : 'key',
				queryMode : 'local'
			});

			Ext.Ajax.request({
				url : 'getActWarningDispose.do',
				method : 'post',
				sync:true,
				params : {
					aid : aid
				},
				success : function(response, options) {
					var success = Ext.decode(response.responseText).success;
					var disposeType = Ext.decode(response.responseText).disposeType;
					var suggestion = Ext.decode(response.responseText).suggestion;
					console.log(success+"||"+disposeType+"||"+suggestion);
					if(success){
						alarmDealMsgComBox.setValue(disposeType);
						alarmDealMsgForm.getForm().findField('smessgae').setValue(suggestion);
					}
				}
			});

			dealAlarmBtn = Ext.create('Ext.Button',{
				text : '提交',
				style : 'margin-left:470px',
				handler : dealActWarning
			});

			alarmDealMsgForm = Ext.create('Ext.form.FormPanel', {
				border : false,
				bodyStyle : 'padding:5px 5px 0px 5px',
				items : [alarmDealMsgComBox, {
					xtype : 'textarea',
					name : 'smessgae',
					height : 220,
					anchor : '100%'
				} ]

			});

			dealMsgWidow = Ext.create ('Ext.window.Window',
				{
					title : '处理意见',
					buttonAlign : 'center',
					modal : true,
					closeAction : 'hide',
					constrain : true,
					autoScroll : true,
					width : 650,
					height : 390,
					draggable : true,// 禁止拖动
					resizable : false,// 禁止缩放
					items : [alarmDealMsgForm,dealAlarmBtn]
				});
		}
		dealMsgWidow.show ();
	}

	function showDealMsgWinNew(alarmId,systemName,pointId, batchId){
		aid="";
		aid=alarmId;
		pid = "";
		pid = pointId;
		batchNumber = ""
		batchNumber = batchId
		if (dealMsgWidow == undefined || !dealMsgWidow.isVisible ())
		{
			alarmDealMsgComBoxStore = Ext.create('Ext.data.Store',  {
				fields: ['key','value'],
				data: [
					{key : '1', value: (notisbhnsflag?'已知待处理':'人工确认')},
					{key : '2', value: (notisbhnsflag?'立即处理':'恢复正常')}
				]
			});

			var uplodaButton = Ext.create ("Ext.Button",
				{
					cls : 'Common_Btn',
					text : '上传',
					margin : '2 5 0 5',
					width: 80,
					handler : uploadFile
				});

			var newAlarmDealMsgForm = Ext.create('Ext.form.FormPanel', {
				id: "formPanel",
				border : false,
				bodyStyle : 'padding:5px 5px 5px 5px',
				items: [{
					layout:'column',
					items : [ {
						columnWidth:0.8,
						layout: 'form',
						xtype : 'filefield',
						name : 'file', // 设置该文件上传空间的name，也就是请求参数的名字
						fieldLabel : '选择文件',
						labelWidth : 80,
						msgTarget : 'side',
						anchor : '80%',
						buttonText : '浏览...',
						width : 300
					},uplodaButton,
						{
							html:"<div style='margin-top: 10px'>(仅支持50M以内的文件大小,最多上传5个文件)</div>"
						},
						{
							columnWidth:1.75,
							// layout: 'form',
							html:""
						}
					]
				} ]
			});

			alarmDealMsgComBox = Ext.create('Ext.form.field.ComboBox', {
				name : 'alarmDealMsgComBox',
				labelWidth : 60,
				anchor : '100%',
				fieldLabel : "处理方式",
				emptyText : '--请选择处理方式--',
				//allowBlank : false,
				triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
				editable : false,// 是否可输入编辑
				store: alarmDealMsgComBoxStore,
				displayField : 'value',
				valueField : 'key',
				queryMode : 'local'
			});

			Ext.Ajax.request({
				url : 'getActWarningDispose.do',
				method : 'post',
				sync:true,
				params : {
					aid : aid
				},
				success : function(response, options) {
					var success = Ext.decode(response.responseText).success;
					var disposeType = Ext.decode(response.responseText).disposeType;
					var suggestion = Ext.decode(response.responseText).suggestion;
					console.log(success+"||"+disposeType+"||"+suggestion);
					if(success){
						alarmDealMsgComBox.setValue(disposeType);
						alarmDealMsgForm.getForm().findField('smessgae').setValue(suggestion);
					}
				}
			});

			alarmDealMsgForm = Ext.create('Ext.form.FormPanel', {
				border : false,
				bodyStyle : 'padding:0px 5px 0px 5px',
				items : [alarmDealMsgComBox, {
					xtype : 'textarea',
					emptyText: '请输入处理意见...',
					name : 'smessgae',
					height : 220,
					anchor : '100%'
				} ]

			});

			var hcalarmDealMsgForm = Ext.create('Ext.form.FormPanel', {
				border : false,
				bodyStyle : 'padding:5px 5px 5px 5px',
				items: [{
					columnWidth:1.75,
					layout: 'form',
					xtype : 'textarea',
					name : 'smessgae',
					height : 220,
					anchor : '100%'
				} ]
			});

			dealAlarmBtn = Ext.create('Ext.Button',{
				text : '提交',
				style : 'margin-left:390px',
				handler : dealActWarning
			});

			dealMsgWidow = Ext.create ('Ext.window.Window',
				{
					title : '处理意见',
					buttonAlign : 'center',
					modal : true,
					closeAction : 'destroy',
					listeners   : {'destroy':{fn: refreshHtml()}},//关闭时清理搜索框数据和datagrid的数据
					constrain : true,
					autoScroll : true,
					width : 900,
					height : 580,
					draggable : true,// 禁止拖动
					resizable : false,// 禁止缩放
					items : [newAlarmDealMsgForm,alarmDealMsgForm,dealAlarmBtn]
				});
		}
		dealMsgWidow.show ();
	}

	function showBatchWin(){
		var data = actWarningGrid.getView().getSelectionModel().getSelection();

		if (data.length == 0) {
			Ext.Msg.alert('提示', '请至少选择一条记录!');
			return;
		}
		if (batchWidow == undefined || !batchWidow.isVisible ())
		{
			alarmBatchComBoxStore = Ext.create('Ext.data.Store',  {
				fields: ['key','value'],
				data: [
					{key : '1', value: (notisbhnsflag?'已知待处理':'人工确认')},
					{key : '2', value: (notisbhnsflag?'立即处理':'恢复正常')}
				]
			});

			alarmBatchComBox = Ext.create('Ext.form.field.ComboBox', {
				name : 'alarmDealMsgComBox',
				labelWidth : 60,
				anchor : '100%',
				fieldLabel : "处理方式",
				emptyText : '--请选择处理方式--',
				//allowBlank : false,
				triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
				editable : false,// 是否可输入编辑
				store: alarmBatchComBoxStore,
				displayField : 'value',
				valueField : 'key',
				queryMode : 'local'
			});

			alarmBatchMsgForm = Ext.create('Ext.form.FormPanel', {
				border : false,
				bodyStyle : 'padding:5px 5px 0px 5px',
				items : [alarmBatchComBox, {
					xtype : 'textarea',
					emptyText: '请输入处理意见...',
					name : 'bmessgae',
					height : 220,
					anchor : '100%'
				} ]

			});

			batchWidow = Ext.create ('Ext.window.Window',
				{
					title : '处理意见',
					buttonAlign : 'center',
					modal : true,
					closeAction : 'hide',
					constrain : true,
					autoScroll : true,
					width : 650,
					height : 390,
					draggable : true,// 禁止拖动
					resizable : false,// 禁止缩放
					items : [alarmBatchMsgForm,batchAlarmBtn]
				});
		}
		batchWidow.show ();
	}

	warnTabPanel.on('resize', function() {
		actWarningGrid.setWidth(warnTabPanel.getWidth());
		actWarningGrid.setHeight(warnTabPanel.getHeight()-modelHeigth-25);
	});

	function uploadFile() {
		var form = this.up('form').getForm();
		var upfile = form.findField("file").getValue();
		if (upfile == '') {
			Ext.Msg.alert('提示', "请选择文件...");
			return;
		}
		if (fucCheckLength (upfile) > 100)
		{
			Ext.Msg.alert ('提示', '文件名称不能超过100字符！');
			return;
		}
		if (form.isValid()) {

			Ext.MessageBox.show(
				{
					title:'请稍候',
					msg:'文件上传中......',
					progressText:'',    //进度条文本
					width:300,
					progress:true,
					closable:false
				}
			);
			//控制进度条速度
			var f=function(v){
				return function(){
					if(v==49)
					{
						Ext.MessageBox.hide();
					}
					else
					{
						var i=v/48;
						Ext.MessageBox.updateProgress(i,Math.round(100*i)+"% 完成");
					}
				}
			}
			for(var i=1;i<50;i++)
			{
				setTimeout(f(i),i*500);//从点击时就开始计时，所以500*i表示每500ms就执行一次
			}

			form.submit({
				url : 'gfUploadFile.do',
				params :
					{
						file : upfile,
						pointId : pid,
						batchNumber: batchNumber,
						type : "warn"
					},
				method : 'POST',
				success : function(form, action) {
					var msg = Ext.decode(action.response.responseText).message;
					var mess = Ext.create('Ext.window.MessageBox', {
						minHeight : 169,
						minWidth : 250,
						resizable : false
					})
					Ext.MessageBox.hide();
					mess.alert('提示', msg);
					refreshHtml();

				},
				failure : function(form, action) {
					secureFilterRsFrom(form,action);
				}
			});
		}
	}

});
function showMessageWindowForActWarning ()
{
	operAlarmWin(3,"INSPLAYALARM");
	var tp_showmess_flag="";
	var check_showmess_flag="";
	var strategy_flag = "";
	var strategy_Warn_flag = "";
	var strategy_Message_flag = "";
	Ext.Ajax.request (
		{
			url : 'checkShowMessageForActWarning.do',
			method : 'POST',
			async: false,   //ASYNC 是否异步( TRUE 异步 , FALSE 同步)
			success : function (response, opts)
			{
				// 当后台数据同步成功时
				tp_showmess_flag=Ext.decode (response.responseText).tpFlag;
				check_showmess_flag=Ext.decode (response.responseText).checkFlag;
				strategy_flag=Ext.decode (response.responseText).strategyFlag;
				strategy_Warn_flag=Ext.decode (response.responseText).strategyWarnFlag;
				strategy_Message_flag=Ext.decode (response.responseText).strategyMessageFlag;
			}
		});
	winalarm.show();
	winalarm.center();
	actWarningStore.load();
	var notab=false;
	if(check_showmess_flag==true || check_showmess_flag=='true'){
		notab=true;
		var tabCount=0;
		warnTabPanel.items.each(function(item) {
			if (item.title == "活动系统异常") {
				tabCount = 1;
			}
		});
		if(tabCount==0){
			warnTabPanel.add({
				title: '活动系统异常',
				items: actWarningGrid
			});
		}
	}
	if(tp_showmess_flag==true || tp_showmess_flag=='true'){
		notab=true;
		var tabCount1=0;
		warnTabPanel.items.each(function(item) {
			if (item.title == "拓扑图报警信息") {
				tabCount1 = 1;
			}
		});
		if(tabCount1==0){
			warnTabPanel.add({
				title: '拓扑图报警信息',
				height : 430,
				width : 1280,
				loader : {
					url : 'warnPageMain.do',
					scripts : true,
					autoLoad : true
				},
				listeners: {
					activate: function(tab) {
						tab.loader.load();
//		            	warnTabPanel.setActiveTab(tab);
					}
				}
			});
		}
	}
	if(!notab){
		var tabCount2=0;
		warnTabPanel.items.each(function(item) {
			if (item.title == "活动系统异常") {
				tabCount2 = 1;
			}
		});
		if(tabCount2==0){
			warnTabPanel.add({
				title: '活动系统异常',
				items: actWarningGrid
			});
		}
	}

	if(strategy_flag){
		var tabCount3=0;
		var tabCount4=0;
		warnTabPanel.items.each(function(item) {
			if (item.title == "巡检系统异常") {
				tabCount3 = 1;
			}
			if (item.title == "巡检系统通知") {
				tabCount4 = 1;
			}
		});
		if(tabCount3==0 && strategy_Warn_flag) {
			warnTabPanel.add({
				title: '巡检系统异常',
				height: 430,
				width: 1280,
				loader: {
					url: 'inspectionTemplate/exception.do',
					scripts: true,
					autoLoad: true
				},
				listeners: {
					activate: function (tab) {
						tab.loader.load();
					}
				}
			});
		}
		if (tabCount4==0 && strategy_Message_flag) {
			warnTabPanel.add({
				title: '巡检系统通知',
				height: 430,
				width: 1280,
				loader: {
					url: 'inspectionTemplate/message.do',
					scripts: true,
					autoLoad: true
				},
				listeners: {
					activate: function (tab) {
						tab.loader.load();
					}
				}
			});
		}
	}
}
function showDataCollectAlarmWin ()
{
	var dataCollectAlarm_window = Ext.create('Ext.window.Window',
		{
			title : '采集报警',
			modal : true,
			closeAction : 'destroy',
			autoScroll : true,
			width : '50%',
			height : '50%',
			resizable : false,// 禁止缩放
			layout : 'fit',
			loader :
				{
					url : 'initDataCollectAlarmPage.do',
					autoLoad : true,
					scripts : true
				}
		}).show();
}
function operAlarmWin(type,model){
//	alert(type+" " + model);
//	Ext.Ajax.request({
//		url : 'operAlarmWin.do',
//		params : {
//			type : type,
//			model:model
//		},
//		method : 'post',
//		success : function(response, text) {}
//	});
}

function deleteFile(e) {
	var iId = $(e).prev().text();
	Ext.Ajax.request(
		{
			url: 'deleteUploadFile.do',
			params:
				{
					iId : iId
				},
			method: 'POST',
			success: function (response, opts) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					// 当后台数据同步成功时刷新列表
					refreshHtml();
				}
				var msg = Ext.decode(response.responseText).message;
				var mess = Ext.create('Ext.window.MessageBox', {
					minHeight : 169,
					minWidth : 250,
					resizable : false
				})
				mess.alert('提示', msg);
			},
			failure: function (result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
}


function refreshHtml() {
	Ext.Ajax.request(
		{
			url: 'getUploadFile.do',
			params:
				{
					batchNumber : batchNumber,
					type : "warn"
				},
			method: 'POST',
			success: function (response, opts) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					var newHtml = "<div id='mydiv' style=''><table>";
					var data = Ext.decode(response.responseText).data;
					for (var i = 0;i < data.length;i++) {
						newHtml +=
							"<tr>" +
							"<td style='text-align: left;width: 600px''><span style='margin-left:10px;'>" + data[i].filename + "</span></td>" +
							"<td style='text-align: right;width: 50px'><span hidden>" + data[i].iid + "</span>" +
							"<span style='text-decoration:underline;color: dodgerblue;' onclick='deleteFile(this)'>删除</span></td>" +
							"</tr>";
					}
					newHtml+= "</table></div>"
					Ext.getCmp('formPanel').update(newHtml);
				}
			},
			failure: function (result, request) {
				secureFilterRs(result, "操作失败！");
			}
		});
}
