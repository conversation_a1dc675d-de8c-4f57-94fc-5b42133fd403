package com.ideal.ieai.agent.healthcheck.model;

import java.util.ArrayList;
import java.util.List;


/**   
 * @ClassName:  ComputerBean   
 * @Description:设备 
 * @author: hongji_shang 
 * @date:   2019年7月23日 下午2:02:09   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class ComputerBean
{
    private Long    cpid;
    private String  ip;
    private String  cpname;
    private Integer cpport;
    private String  cpuser;
    private String  cppasswd;
    private String  protocol;
    private String  endstring;
    private String  serverIp;
    private Long  agentId;
    private Long  cpGroupId;
    private String fortkeyip;
    private String fortkeyuser;
    private String expectCmd;
    private List<HcAgreementAgentModel> agreements = new ArrayList<HcAgreementAgentModel>(); 

    public String getExpectCmd ()
    {
        return expectCmd;
    }

    public void setExpectCmd ( String expectCmd )
    {
        this.expectCmd = expectCmd;
    }

    public String getFortkeyip ()
    {
        return fortkeyip;
    }

    public void setFortkeyip ( String fortkeyip )
    {
        this.fortkeyip = fortkeyip;
    }

    public String getFortkeyuser ()
    {
        return fortkeyuser;
    }

    public void setFortkeyuser ( String fortkeyuser )
    {
        this.fortkeyuser = fortkeyuser;
    }

    public Long getCpid ()
    {
        return cpid;
    }

    public void setCpid ( Long cpid )
    {
        this.cpid = cpid;
    }

    public String getIp ()
    {
        return ip;
    }

    public void setIp ( String ip )
    {
        this.ip = ip;
    }

    public String getCpname ()
    {
        return cpname;
    }

    public void setCpname ( String cpname )
    {
        this.cpname = cpname;
    }

    public Integer getCpport ()
    {
        return cpport;
    }

    public void setCpport ( Integer cpport )
    {
        this.cpport = cpport;
    }

    public String getCpuser ()
    {
        return cpuser;
    }

    public void setCpuser ( String cpuser )
    {
        this.cpuser = cpuser;
    }

    public String getCppasswd ()
    {
        return cppasswd;
    }

    public void setCppasswd ( String cppasswd )
    {
        this.cppasswd = cppasswd;
    }

    public String getProtocol ()
    {
        return protocol;
    }

    public void setProtocol ( String protocol )
    {
        this.protocol = protocol;
    }

    public String getEndstring ()
    {
        return endstring;
    }

    public void setEndstring ( String endstring )
    {
        this.endstring = endstring;
    }

    public String getServerIp ()
    {
        return serverIp;
    }

    public void setServerIp ( String serverIp )
    {
        this.serverIp = serverIp;
    }

    public Long getAgentId ()
    {
        return agentId;
    }

    public void setAgentId ( Long agentId )
    {
        this.agentId = agentId;
    }

    public Long getCpGroupId ()
    {
        return cpGroupId;
    }

    public void setCpGroupId ( Long cpGroupId )
    {
        this.cpGroupId = cpGroupId;
    }

    public List<HcAgreementAgentModel> getAgreements ()
    {
        return agreements;
    }

    public void setAgreements ( List<HcAgreementAgentModel> agreements )
    {
        this.agreements = agreements;
    }

    
}
