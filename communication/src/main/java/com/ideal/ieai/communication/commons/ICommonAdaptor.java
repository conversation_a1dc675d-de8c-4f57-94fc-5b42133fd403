package com.ideal.ieai.communication.commons;

/**
 * to offer a templeate that easliy to add a new
 * communication way (ssl socket,xml-rpc etc)
 * using factory method design pattern as an abstract factory
 *
 * <p>Title: communication</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: ideal</p>
 * <AUTHOR>
 * @version 3.0
 */


public interface ICommonAdaptor
    extends ICommAdaptor
{
    /**
     * to code a passable object to string
     * @param obj
     * @return
     */
    public String marshallPassableObject(Object obj) throws MarshalException;

    /**
     *
     * @param orgin:string receive
     * @return:a request or a response
     */
    public Object unmarshallPassableObject(String orgin) throws UnmarshalException;

    /**
     * to encrypt a string
     * @param marshall
     * @return
     */
    public byte[] encrypt(String marshall);

    /**
     * to decrypt a byte[] to string
     * @param <any>
     * @return
     */
    public String decrypt(byte[] bytes);

}
