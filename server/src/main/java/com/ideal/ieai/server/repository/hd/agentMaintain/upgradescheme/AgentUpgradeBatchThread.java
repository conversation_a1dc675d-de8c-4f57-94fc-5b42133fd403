package com.ideal.ieai.server.repository.hd.agentMaintain.upgradescheme;

import com.ideal.ieai.commons.agent.upgrade.StatusCode;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.repository.hd.agentMaintain.damonrpc.AgentDamonRpc;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Agent批量升级线程
 * 注：只能在主机运行，防止重复
 */
public class AgentUpgradeBatchThread extends Thread
{
    private static final Logger logger = Logger.getLogger(AgentUpgradeBatchThread.class);

    private final int     BATCH_NUM    = Environment.getInstance()
            .getIntConfig(Environment.AGENT_UPDATE_NUM_PERBATCH, 30);//并发量
    private final int     INTERVAL     =
            Environment.getInstance().getIntConfig(Environment.AGENT_UPDATE_TIME_INTERVAL, 30) * 1000;//间隔时间
    private       String  serverIP     = "127.0.0.1";       //本机ip
    private       String  mainServerIp = "127.0.0.1";   //主serverIP
    private       boolean runFlag;                      //是否满足运行条件
    private       int     agentUpgrading;               //升级/取消并发数量

    public AgentUpgradeBatchThread()
    {
        super("agentUpgradeV2");
        try
        {
            serverIP = Environment.getInstance().getServerIP();
            //获取主server的ip
            mainServerIp = Environment.getInstance().getServerIP();
            logger.debug("serverIP=" + serverIP + ",mainServerIp=" + mainServerIp);
        } catch (Exception e)
        {
            logger.error("", e);
        } finally
        {
            //判断本机是否为主SERVER
            if (serverIP != null && mainServerIp != null && serverIP.equals(mainServerIp))
            {
                runFlag = true;
                logger.info("本机为主server，启动AgentUpgradeBatchThread！");
            } else
            {
                logger.info("非主server，无法启动AgentUpgradeBatchThread！");
            }
        }
    }

    @Override public void run ()
    {
        //启动方案运行表中“待升级”和“待回退”方案
        while (runFlag)
        {
            try
            {
                //计算并发数量，更新变量：agentUpgrading
                countUpgrading();
                if (agentUpgrading > BATCH_NUM)
                    continue;
                //“待回退”方案处理，更新变量：agentUpgrading
                cancelTask();
                //“待升级”方案处理，更新变量：agentUpgrading
                upgradeTask();
                //更新方案状态
                updatePlanStatus();
            } catch (Exception e)
            {
                logger.error("agent批量升级线程异常！", e);
            }finally {
                //时间间隔
                logger.info("睡眠-->" + INTERVAL);
                try{
                    Thread.sleep(INTERVAL);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    //计算并发量
    private void countUpgrading ()
    {
        //查询回退已发送（202）和升级已发送（102）的并发数量，更新变量：agentUpgrading
        try {
            int count = AgentUpgradeSchemeManager
                    .getInstance().getAgentMaintainCount("102,202",0);
            agentUpgrading=count;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("countUpgrading Exception " + e);
            agentUpgrading = agentUpgrading-5;
        }

        logger.info("agent批量升级线程当前并发量-->" + agentUpgrading);
    }

    //取消方案
    private void cancelTask ()
    {
        //获取“待回退”方案
        List<Map> lists=new ArrayList<>();
        try{
            lists = AgentUpgradeSchemeManager
                    .getInstance().getAgentMaintainParameter("201","");
        }catch (Exception e){
            logger.error("获取运行任务异常");
        }

//        //遍历“待回退”方案所有agent
        for (int p = 0; ((p < lists.size()) && (agentUpgrading < BATCH_NUM)); p++)
        {
            //调用回退agent接口
            try {
                //调用Rpc 发送到守护进程 并修改agent升级状态
                AgentDamonRpc.getInstance().agentUpgrade(lists.get(p),"rollback");
            }catch (Exception ew){
                logger.info("AgentUpgradeBatchThread AgentDamonRpc Exception" + ew);
            }
            agentUpgrading++;
            logger.info(p + "-回退agent[" + p + "]，并发+1-->" + agentUpgrading);
        }
    }

    //升级方案（有并发数限制）
    private void upgradeTask ()
    {
        //查询“待升级”方案
        List<Map> lists=new ArrayList<>();
        try{
            lists = AgentUpgradeSchemeManager
                    .getInstance().getAgentMaintainParameter("101","");
        }catch (Exception e){
            logger.error("获取运行任务异常");
        }
        //遍历“升级开始”agent
        for (int i = 0; ((i < lists.size()) && (agentUpgrading < BATCH_NUM)); i++)
        {
            //调用升级agent接口
            try {
                AgentDamonRpc.getInstance().agentUpgrade(lists.get(i),"upgrade");
            }catch (Exception ew){
                logger.info("AgentUpgradeBatchThread AgentDamonRpc Exception" + ew);
            }
            agentUpgrading++;
            logger.info("-升级agent[" + i + "]，并发+1-->" + agentUpgrading);
        }
    }

    //更新方案状态
    private void updatePlanStatus ()
    {
        //根据运行id查询agent任务表，计算已完成（包括成功、失败、超时）数量与升级agent数量是否相同，如果相同，则升级/取消完成
        List<AgentUpgradePlanModel> upgradePlanRun=null;
        try{
            //更新升级方案 任务状态
            upgradePlanRun = AgentUpgradeSchemeManager
                    .getInstance().getUpgradePlanRun("", "11,21,19,29");
            for (AgentUpgradePlanModel model:upgradePlanRun)
            {
                //151 升级超时 152 升级失败 200 升级成功 154 发送失败 251 回退超时  252 回退失败 300 回退完成
                String sta="151,152,141,251,252";
                String sta1="200,300";
                int count = AgentUpgradeSchemeManager
                        .getInstance().getAgentMaintainCount(sta,model.getRunId());

                int count1 = AgentUpgradeSchemeManager
                        .getInstance().getAgentMaintainCount(sta1,model.getRunId());

                int schemeStatus= 0;
                int status=0;
                if (StatusCode.SCHEME_UPGRADE_START==model.getSchemeStatus()||19==model.getSchemeStatus()){
                    status=19;
                    schemeStatus=StatusCode.SCHEME_UPGRADE_COMPLETE;
                }
                if (StatusCode.SCHEME_WAIT_CANCEL==model.getSchemeStatus()||29==model.getSchemeStatus()){
                    status=29;
                    schemeStatus=StatusCode.SCHEME_CANCEL_COMPLETE;
                }


                if (count>0&&(model.getSchemeStatus()!=19 ||model.getSchemeStatus()!=29)){
                    AgentUpgradeSchemeManager
                            .getInstance().updateAgentUpgradePlanRun(status,model.getRunId());
                }

                if(count1==model.getAgentQuantity()){
                    logger.info("更新方案 状态-->" + 20);
                    AgentUpgradeSchemeManager
                            .getInstance().updateAgentUpgradePlanRun(schemeStatus,model.getRunId());
                }
            }
        }catch (Exception e){
            logger.info("更新方案[方案Exception  ]状态--> "+e);
        }
    }



}
