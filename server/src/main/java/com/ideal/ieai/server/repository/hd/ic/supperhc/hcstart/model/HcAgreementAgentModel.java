package com.ideal.ieai.server.repository.hd.ic.supperhc.hcstart.model;

import java.util.ArrayList;
import java.util.List;

import com.ideal.ieai.server.repository.hd.ic.supperhc.support.HcAgreementTypeBean;

public class HcAgreementAgentModel 
{
    private Long iagreementId;
    
    private String iagreementName;
    
    private Long icomputerId;
    
    private String iagreementType;
    
    private String iagreementIp;
    
    private String iagreementDomain;
    
    private Integer iagreementPort;
    
    private Integer iipordoman;
    
    private String iusername;
    
    private String ipassword;
    
    private String version;
    
    private String contextname;//上下文名称
    
    private String safename;//安全名称
    
    private String safelevel;//安全级别
    
    private String vftageeement;//验证协议
    
    private String vftpassword;     //验证口令 
    
    private String privacyagreement;//隐私协议
    private String privatekey;      //私钥
    private String community;       //团体名称
    
    
    private List<HcAgreementTypeBean> agreementTypeBeans = new ArrayList<HcAgreementTypeBean>();

    public Long getIagreementId ()
    {
        return iagreementId;
    }

    public void setIagreementId ( Long iagreementId )
    {
        this.iagreementId = iagreementId;
    }

    public String getIagreementName ()
    {
        return iagreementName;
    }

    public void setIagreementName ( String iagreementName )
    {
        this.iagreementName = iagreementName;
    }

    public Long getIcomputerId ()
    {
        return icomputerId;
    }

    public void setIcomputerId ( Long icomputerId )
    {
        this.icomputerId = icomputerId;
    }

    public String getIagreementType ()
    {
        return iagreementType;
    }

    public void setIagreementType ( String iagreementType )
    {
        this.iagreementType = iagreementType;
    }

    public String getIagreementIp ()
    {
        return iagreementIp;
    }

    public void setIagreementIp ( String iagreementIp )
    {
        this.iagreementIp = iagreementIp;
    }

    public String getIagreementDomain ()
    {
        return iagreementDomain;
    }

    public void setIagreementDomain ( String iagreementDomain )
    {
        this.iagreementDomain = iagreementDomain;
    }

    public Integer getIagreementPort ()
    {
        return iagreementPort;
    }

    public void setIagreementPort ( Integer iagreementPort )
    {
        this.iagreementPort = iagreementPort;
    }

    public Integer getIipordoman ()
    {
        return iipordoman;
    }

    public void setIipordoman ( Integer iipordoman )
    {
        this.iipordoman = iipordoman;
    }

    public String getIusername ()
    {
        return iusername;
    }

    public void setIusername ( String iusername )
    {
        this.iusername = iusername;
    }

    public String getIpassword ()
    {
        return ipassword;
    }

    public void setIpassword ( String ipassword )
    {
        this.ipassword = ipassword;
    }

    public String getVersion ()
    {
        return version;
    }

    public void setVersion ( String version )
    {
        this.version = version;
    }

    public String getContextname ()
    {
        return contextname;
    }

    public void setContextname ( String contextname )
    {
        this.contextname = contextname;
    }

    public String getSafename ()
    {
        return safename;
    }

    public void setSafename ( String safename )
    {
        this.safename = safename;
    }

    public String getSafelevel ()
    {
        return safelevel;
    }

    public void setSafelevel ( String safelevel )
    {
        this.safelevel = safelevel;
    }

    public String getVftageeement ()
    {
        return vftageeement;
    }

    public void setVftageeement ( String vftageeement )
    {
        this.vftageeement = vftageeement;
    }

    public String getVftpassword ()
    {
        return vftpassword;
    }

    public void setVftpassword ( String vftpassword )
    {
        this.vftpassword = vftpassword;
    }

    public String getPrivacyagreement ()
    {
        return privacyagreement;
    }

    public void setPrivacyagreement ( String privacyagreement )
    {
        this.privacyagreement = privacyagreement;
    }

    public String getPrivatekey ()
    {
        return privatekey;
    }

    public void setPrivatekey ( String privatekey )
    {
        this.privatekey = privatekey;
    }

    public String getCommunity ()
    {
        return community;
    }

    public void setCommunity ( String community )
    {
        this.community = community;
    }

    public List<HcAgreementTypeBean> getAgreementTypeBeans ()
    {
        return agreementTypeBeans;
    }

    public void setAgreementTypeBeans ( List<HcAgreementTypeBean> agreementTypeBeans )
    {
        this.agreementTypeBeans = agreementTypeBeans;
    }   
    
    

}
