/**
 * 发布类型控制弹出窗_宝兰德专属_2023_06_29---------------->xuhang
 */
var required = '<span style="color:red;font-weight:bold" data-qtip="Required">*</span>';
Ext.define('page.webStudio.project.publicWinBld', {
    extend: 'Ext.window.Window',
    title: '确认工程发布方式',
    modal: true,
    cibbank: false,
    projecttype: 1,
    closeAction: 'destroy',
    buttonAlign: 'center',
    draggable: true,
    resizable: false,
    width: 630,
    reqText: 'save',
    height: 400,
    record: {},
    winRet: false,

    initComponent: function () {
        var me = this;
        var record = me.record;
        var bankcib = me.cibbank;
        var protype = me.projecttype;
        var states = Ext.create('Ext.data.Store', {
            fields: ['ibusname', 'iid'],
            proxy: {
                type: 'ajax',
                url: 'getBusinessList.do',
                reader: {
                    type: 'json',
                }
            },
            autoLoad: true
        });

        var sysclassify = Ext.create('Ext.form.ComboBox', {
            fieldLabel: '系统分类',
            name: 'classify',
            padding: '15 15 15 15',
            store: states,
            queryMode: 'local',
            displayField: 'ibusname',
            valueField: 'iid',
        });

        var projectNameText = Ext.create('Ext.form.field.Text', {
            name: 'remoteIp',
            allowBlank: false,
            padding: '15 15 15 15',
            fieldLabel: ' 工程名',
            readOnly: true,
            value: record.raw.iprojectname
        });

        var remoteiplist = Ext.create('Ext.data.Store', {
            proxy: {
                type: 'ajax',
                url: 'queryremoteip.do',
                reader: {
                    type: 'json',
                    root: 'list'
                }
            },
            fields: [{name: 'ip', type: 'string'}]
        });
        remoteiplist.load();

        var remoteIpText = Ext.create('Ext.form.field.ComboBox', {
            name: 'remoteIp',
            store: remoteiplist,
            labelAlign: "right",
            allowBlank: false,
            padding: '15 15 15 15',
            fieldLabel: ' 远程地址',
            regex: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            regexText: '请输入正确的IP地址',
            disabled: true,
            displayField: 'ip',
            valueDield: 'ip'
        });
        remoteIpText.disable();

        Ext.define('AModel', {
            extend: 'Ext.data.Model',
            fields: [{name: 'loginName', type: 'string'},
                {name: 'fullName', type: 'string'},
                {name: 'phoneNum', type: 'string'
            }]
        });

        var startUserStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            model: 'AModel',
            proxy: {
                type: 'ajax',
                url: 'getAuditRootUserList.do',
                reader: {
                    type: 'json',
                    root: 'dataList'
                }
            }
        });

        //TODO 流程编排审核人下拉选
        var remoteAuditText = Ext.create('Ext.form.ComboBox', {
            editable: true,
            fieldLabel: "审核人",
            store: startUserStore,
            queryMode: 'local',
            displayField: 'fullName',
            valueField: 'loginName',
            afterLabelTextTpl: required,
            labelAlign: 'right'
        });

        var userNameText = Ext.create('Ext.form.field.Text', {
            name: 'userName',
            allowBlank: false,
            padding: '15 15 15 15',
            fieldLabel: ' 用户名称',
            labelAlign: "right",
            disabled: true,
            value: me.release_loginName
        });

        var passwdText = Ext.create('Ext.form.field.Text', {
            name: 'passwd',
            allowBlank: false,
            padding: '15 15 15 15',
            fieldLabel: ' 用户密码',
            disabled: true,
            labelAlign: "right",
            inputType: "password",
            value: ''
        });

        var queryForm = Ext.create('Ext.form.Panel', {
            border: false,
            frame: true,
            layout: 'form',
            items: [projectNameText, {
                xtype: 'radiogroup',
                fieldLabel: '发布方式',
                columns: 2,
                vertical: true,
                labelAlign: "right",
                items: [{
                    boxLabel: '本地发布',
                    name: 'releaseType',
                    inputValue: 'local',
                    checked: true
                }, {boxLabel: '远程发布',
                    name: 'releaseType',
                    inputValue: 'remote'}
                ],
                listeners: {
                    change: function (radiogroup, newValue, oldValue) {
                        if (newValue.releaseType == 'remote') {
                            remoteIpText.enable();
                            userNameText.enable();
                            passwdText.enable();
                        }
                        if (newValue.releaseType == 'local') {
                            remoteIpText.disable();
                            userNameText.disable();
                            passwdText.disable();
                        }
                    }
                }
            }, {
                xtype: 'tbtext',
                text: '<span style="color:#ff001c;padding-left:44px;position:relative;top: 5px">提示:审核人为校验执行人为root用户或者执行人为空的情况的流程审核人</span>',
                height: 30,
            },remoteAuditText, remoteIpText, userNameText, passwdText]
        });

        var zbqueryForm = Ext.create('Ext.form.Panel', {
            border: false,
            frame: true,
            layout: 'form',
            width: 450,
            height: 400,
            padding: '0 10 0 10',
            items: [{
                xtype: 'toolbar',
                layout: 'fit',
                items: [projectNameText]
            }, {
                xtype: 'toolbar',
                layout: 'fit',
                items: [sysclassify]
            }, {
                xtype: 'radiogroup',
                fieldLabel: '发布方式',
                columns: 2,
                hidden: true,
                vertical: true,
                items: [{
                    boxLabel: '本地发布',
                    name: 'releaseType',
                    inputValue: 'local',
                    checked: true
                }, {boxLabel: '远程发布',
                    name: 'releaseType',
                    inputValue: 'remote'}
                ], listeners: {
                    change: function (radiogroup, newValue, oldValue) {
                        if (newValue.releaseType == 'remote') {
                            remoteIpText.enable();
                            userNameText.enable();
                            passwdText.enable();
                        }
                        if (newValue.releaseType == 'local') {
                            remoteIpText.disable();
                            userNameText.disable();
                            passwdText.disable();
                        }
                    }
                }
            }]
        });

        if (protype == 4) {
            me.setHeight(230);
            Ext.applyIf(me, {
                items: [zbqueryForm],
                buttons: [{
                    xtype: 'button',
                    text: '确定', //    	            cls: 'Common_Btn',
                    handler: function (meBtn) {
                        if (!queryForm.isValid()) return;
                        meBtn.disable();
                        var releaseType = queryForm.getForm().getValues().releaseType;
                        if (releaseType == 'local') {
                            release('');
                        } else {
                            release(remoteIpText.getValue(), userNameText.getValue(), passwdText.getValue());
                        }
                    }
                }, {
                    xtype: 'button',
                    text: '取消',
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            });
        } else {
            Ext.applyIf(me, {
                items: [queryForm],
                buttons: [{
                    xtype: 'button',
                    text: '确定', //    	            cls: 'Common_Btn',
                    handler: function (meBtn) {
                        if (!queryForm.isValid()) return;
                        meBtn.disable();
                        var releaseType = queryForm.getForm().getValues().releaseType;
                        if (releaseType == 'local') {
                            release('');
                        } else {
                            release(remoteIpText.getValue(), userNameText.getValue(), passwdText.getValue());
                        }
                    }
                }, {
                    xtype: 'button',
                    text: '取消',
                    handler: function () {
                        this.up("window").close();
                    }
                }]
            });
        }

        function release(remoteIp, loginName, passwd) {

            if (remoteAuditText.getValue() == '' || remoteAuditText.getValue() == null) {
                Ext.Msg.alert('提示', "请选择流程审批人！");
                return;
            }

            Ext.Ajax.request({
                url: me.prefix + 'releaseProject.do',
                timeout: 30000, params: {
                    projectId: record.raw.iid,
                    projectTypeId: record.raw.itypeid,
                    loginName: loginName,
                    passwd: passwd,
                    remoteIp: remoteIp,
                    projectname: record.raw.iprojectname,
                    sysclassify: sysclassify.getRawValue(),
                    sysclassifyid: sysclassify.getValue(),
                    auditer: remoteAuditText.getValue()
                },
                method: 'POST',
                success: function (response, opts) {
                    Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                    me.winRet = true;
                    me.close();
                }, failure: function (response, options) {
                    Ext.MessageBox.hide();
                    Ext.Msg.alert('提示', '请求超时！');
                    me.close();
                }
            });
        }

        me.callParent(arguments);
    }
});