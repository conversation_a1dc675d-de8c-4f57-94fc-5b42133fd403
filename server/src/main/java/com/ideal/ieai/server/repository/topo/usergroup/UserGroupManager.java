package com.ideal.ieai.server.repository.topo.usergroup;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;

public class UserGroupManager
{

    private static final Logger _log = Logger.getLogger("topoLog");

    public List<userGroupModel> queryProname ( String first ) throws DBException
    {
        List<userGroupModel> list1 = new ArrayList<userGroupModel>();
        Connection conn1 = null;
        Statement stmt1 = null;
        ResultSet rset1 = null;
        try
        {
            conn1 = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            stmt1 = conn1.createStatement();
            if ("".equals(first) || first == null)
            {
                rset1 = stmt1.executeQuery("select distinct systemname from ieai_topoinfo order by systemname");
            } else
            {
                rset1 = stmt1.executeQuery("select distinct systemname from ieai_topoinfo where systemname like '%"
                        + first + "%' order by systemname");
            }
            userGroupModel pname = new userGroupModel();
            pname.setProname("");
            list1.add(pname);
            while (rset1.next())
            {
                userGroupModel projectname = new userGroupModel();
                projectname.setProname(rset1.getString("systemname"));
                list1.add(projectname);
            }
        } catch (SQLException e)
        {
            _log.error(e);
        } finally
        {
            DBResource.closeConn(conn1, rset1, stmt1, "queryProname ", _log);
        }
        return list1;

    }

    // public List<userGroupModel> queryGroup ( String first ) throws DBException
    // {
    // List<userGroupModel> list1 = new ArrayList<userGroupModel>();
    // List list = new ArrayList();
    // GroupProjectRelationService getGroup = new GroupProjectRelationService();
    // list = getGroup.getGroupInfo();
    // for (int i = 0; i < list.size(); i++)
    // {
    // userGroupModel groupname = new userGroupModel();
    // groupname.setGroup((String) list.get(i));
    // list1.add(groupname);
    // }
    // return list1;
    // }
    //
    // public List<userGroupModel> queryPrjGroup ( String start, String limit, String prjname,
    // String groname ) throws DBException
    // {
    // List list = new ArrayList();
    // List<userGroupModel> list1 = new ArrayList<userGroupModel>();
    // GroupProjectRelationService getProname = new GroupProjectRelationService();
    // list = getProname.getPrjNameByGroup(groname);
    // for (int i = 0; i < list.size(); i++)
    // {
    // userGroupModel projname = new userGroupModel();
    // projname.setProname((String) list.get(i));
    // list1.add(projname);
    // }
    // return list1;
    // }

}
