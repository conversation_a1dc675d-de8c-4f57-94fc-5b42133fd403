package com.ideal.ieai.studio.util.adaptor;

import com.ideal.ieai.core.adaptor.Adaptor;

/**
 * <p>
 * Title: iEAI Studio
 * </p>
 * <p>
 * Description: Interface of listener of adaptor.All changes of adaptor will be
 * notified to these listeners.
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: IdealInfo
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */
public interface IAdaptorListener
{
    /**
     * add adaptor to studio environment
     * 
     * @param adp
     */
    public void addAdaptor ( Adaptor adp );

    /**
     * remove adaptor from studio environment
     * 
     * @param adp
     */
    public void removeAdaptor ( Adaptor adp );
}
