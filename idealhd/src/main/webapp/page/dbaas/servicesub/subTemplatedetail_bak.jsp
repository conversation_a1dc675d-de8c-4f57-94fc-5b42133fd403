<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page isELIgnored="false"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>数据分析</title>
<script type="text/javascript" src="js/layui/layui.js"></script>
<script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
<link rel="stylesheet" href="css/table.css" />
<link rel="stylesheet" href="js/layui/css/layui.css" />
<script type="text/javascript"
	src="<%=request.getContextPath()%>/page/dbaas/report/echarts.min.js"></script>
<%
    String tmpId = request.getParameter("tmpId");
%>
<script type="text/javascript">
var tmpId="<%=tmpId%>";
	var keydata = new Array();
	$(function() {
		$.ajax({
			type : "post",
			url : "getTemplateDetail.do",
			data : {
				tmpId : tmpId
			},
			dataType : "json",
			success : function(data) {
				$("#contains").html('');
				$.each(data, function(i, resource) {
					//每个服务的表格
					var table='';
					var tablelist = resource.tablelist;
					var title=resource.title;
					var resId=resource.resId;
					$.each(tablelist, function(index, item) {
						var table;
						if(index==0){
							table ="<h2>"+title+"</h2>"+item.table[0];
						}else{
							table =item.table[0];
						}
						var divId = "div" + index+"_"+resId;
						var charthtml = "<div id='" + divId + "' style='width:100%;height:400px;display:none;'></div>";
						if(index==tablelist.length-1){
							$("#contains").append(table).append(charthtml);
						}else{
							$("#contains").append(table).append(charthtml);
						}
						layui.use(['form','laydate'], function() {
							var form = layui.form;
							var laydate = layui.laydate;
							form.on('select(changeSelect)', function(data) {
								tableName = data.value;
								var x = data.elem.getAttribute("id");
								//执行一个laydate实例
								laydate.render({
									elem: "#"+x.replace("table","date"),
									type:'date',
									value:new Date(),
								    isInitValue: true
								});
								//var x= "select_table_zhang3_2";
								$.get("getGroupCountByTableName.do?tableName=" + tableName, function(data) {
									var group = document.getElementById(x.replace("table","group")); //add_role_name给select定义的id
									var count = document.getElementById(x.replace("table","count"));
									group.innerHTML = "";
									count.innerHTML = "";
									for ( var i in data) {
										var option = document.createElement("option"); // 创建添加option属性
										option.setAttribute("value", data[i]); // 给option的value添加值
										option.innerText = data[i];
										group.appendChild(option);
										form.render('select'); // 刷性select，显示出数据
										var option = document.createElement("option"); // 创建添加option属性
										option.setAttribute("value", data[i]); // 给option的value添加值
										option.innerText = data[i];
										count.appendChild(option);
										form.render('select'); // 刷性select，显示出数据
									}
								})
							});
						});
					});
				});
			}
		});
	});
	function getChart(tableName, groupName, countName, chartType,data,tableId,resid) {
		var table = document.getElementById(tableName);
		var group = document.getElementById(groupName);
		var count = document.getElementById(countName);
		var chart = document.getElementById(chartType);
		var datadate = document.getElementById(data).value;
		var arr = document.getElementsByTagName('layui-btn');
		if (table == null || table.value == '') {
			layer.msg('请选择表名', {icon: 5});
			return;
		}
		if (group == null || group.value == '') {
			layer.msg('请选择分组', {icon: 5});
			return;
		}
		if (count == null || count.value == '') {
			layer.msg('请选择统计', {icon: 5});
			return;
		}
		if (chart == null || chart.value == '') {
			//layer.msg('请选择生成图表类型', {icon: 5});
			//return;
		}
		table = table.value;
		group = group.value;
		count = count.value;
		chart = chart.value;
		var url_lo = 'getGraphByTable.do?table=' + table + '&group=' + group + '&count=' + count + '&chart=' + chart+ '&datadate=' + datadate+ '&tmpId=' + tmpId+'&resid=' + resid;
		$.get(url_lo, function(data) {
			var div = document.getElementById("div" + tableId+"_"+resid).style.display;
			if(chart ==  ''){
				document.getElementById("div" + tableId+"_"+resid).style.display='none';
			}else if(div=='none'){
                document.getElementById("div" + tableId+"_"+resid).style.display='';
            }
			if (data.chartType == 2) {
				var series = new Array();
				series.push({
					name : data.series.name,
					type : 'bar',
					barWidth : '60%',
					data : data.series.data
				});
				var overTimeChart = echarts.init(document.getElementById("div" + tableId+"_"+resid));
				var option = {
					xAxis : {
						type : 'category',
						data : data.xAxisData
					},
					yAxis : {
						type : 'value'
					},
					series : series,
					grid : {
						left : '0%',
						right : '20%',
						bottom : '0%',
						top : '50',
						containLabel : true
					}
				};
				overTimeChart.setOption(option, true);
			} else if (data.chartType == 3) {
				var series = new Array();
				series = data.series;
				var overTimeChart = echarts.init(document.getElementById("div" + tableId+"_"+resid));
				option = {
					xAxis : {
						type : 'category',
						data : data.xAxisData
					},
					tooltip : {
						trigger : 'axis'
					},
					yAxis : {
						type : 'value'
					},
					series :series,
					grid : {
						left : '0%',
						right : '20%',
						bottom : '0%',
						top : '50',
						containLabel : true
					}
				}
				overTimeChart.setOption(option, true);
			} else if (data.chartType == 1) {
				var series = new Array();
				series.push({
					type : 'pie',
					data : data.series.seriesData
				});
				var overTimeChart = echarts.init(document.getElementById("div" + tableId+"_"+resid));
				option = {
					tooltip : {
						trigger : 'item',
						formatter : "{a} <br/> {b} :{c} ({d}%)"
					},
					legend : {
						orient : 'vertical',
						x : 'left',
						data : data.legendData
					},
					series : series,
					grid : {
						left : '0%',
						right : '20%',
						bottom : '0%',
						top : '50',
						containLabel : true
					},
				};
				overTimeChart.setOption(option, true);
			}
			var div="#"+table+"_"+resid;
			$(div).html("");
			$(div).html(data.table);
		})
	};
	function showDetail(id) {
		if (document.getElementById(id).style.display == "none") {
			document.getElementById(id).style.display = "";
		} else {
			document.getElementById(id).style.display = "none";
		}
	}
</script>
</head>
<body>
	<div id="contains"></div>
</body>
</html>