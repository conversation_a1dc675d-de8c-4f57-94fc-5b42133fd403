-- 4.7.17 version does not have patches for timed tasks  

-- 4.7.18 version timing task patch is as follows
	DE<PERSON><PERSON>ITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'PAUSE') THEN
		ALTER TABLE IEAI_TIMETASK_INFO ADD COLUMN PAUSE INTEGER DEFAULT 0;
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'TASKTYPE') THEN
		ALTER TABLE IEAI_TIMETASK_INFO ADD COLUMN TASKTYPE INTEGER DEFAULT -1;
	END IF;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.19 version timing task patch is as follows
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
		CREATE PROCEDURE UPPRDE()
		BEGIN
			CREATE TABLE IF NOT EXISTS IEAI_TASKDATERECORD(IID DECIMAL(19) NOT NULL,TASKNAME VARCHAR (255) ,TASKRUNTIME VARCHAR (255),TASKCOMMAND VARCHAR(1000),TASKGROUP VARCHAR (255),ACTIONTIME TIMESTAMP,FLAG VARCHAR(2) ,CONSTRAINT PK_IEAI_TASKDATERECORD PRIMARY KEY(IID));	
			
		END;;
		DELIMITER ;
		CALL UPPRDE();
		-- DELIMITER ;
		
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
			CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_IMMED_INFO (ID  DECIMAL(19,0) not null,TASK_ID  DECIMAL(19,0) not null,SERVERIP   VARCHAR(255),EXECSTATUS INTEGER default -1,CONSTRAINT PK_IEAI_TIMETASK_IMMED_INFO PRIMARY KEY(ID));
			
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
	DELIMITER ;;
		DROP PROCEDURE IF EXISTS UPPRDE;;
	  	create PROCEDURE UPPRDE()
		BEGIN
			CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_APPOINTMENT_INFO (ID DECIMAL(19,0) not null,TASK_ID  DECIMAL(19,0) not null,EXPECTEDTIME VARCHAR(22),EXPECT_TYPE INTEGER,TASKSTATUS INTEGER default 0,SERVERIP VARCHAR(255),CONSTRAINT PK_IEAI_TT_APPOINTMENT_INFO PRIMARY KEY(ID));
			
		END;;
		DELIMITER ;
		CALL UPPRDE();
		
-- 4.7.20 version timing task patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN

	IF EXISTS (SELECT * FROM information_schema.table_constraints WHERE table_name = upper('IEAI_AGENTINFO_MID') AND CONSTRAINT_TYPE='PRIMARY KEY') THEN
		ALTER TABLE IEAI_AGENTINFO_MID DROP PRIMARY KEY;
	END IF;
		
	IF EXISTS (SELECT * FROM information_schema.table_constraints WHERE table_name = upper('IEAI_PROJECT_MID') AND CONSTRAINT_TYPE='PRIMARY KEY') THEN
		ALTER TABLE IEAI_PROJECT_MID  DROP PRIMARY KEY;
	END IF;

	IF EXISTS (SELECT * FROM information_schema.table_constraints WHERE table_name = upper('IEAI_SYS_RELATION_MID') AND CONSTRAINT_TYPE='PRIMARY KEY') THEN
		ALTER TABLE IEAI_SYS_RELATION_MID  DROP PRIMARY KEY;
	END IF;
	
		

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.21 version does not have patches for timed tasks
-- 4.7.22 version does not have patches for timed tasks

-- 4.7.23 version timing task patch is as follows
DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_STOPFORCE_IP;;
	CREATE PROCEDURE PROC_STOPFORCE_IP(IN AV_TASKID NUMERIC(19,0),IN AV_REQID VARCHAR(50),OUT TEST_RESULT VARCHAR(50))
		BEGIN
			DECLARE	LN_TASKID NUMERIC(19,0);
			
			SET LN_TASKID = AV_TASKID;
			
			UPDATE IEAI_TIMETASK_IP SET TASKIPSTATE = -1 WHERE UUID= AV_REQID ;
			SET TEST_RESULT = LN_TASKID;
			COMMIT;
		END;;
		
	DROP PROCEDURE IF EXISTS PROC_AGENT_UPDATE_TASK_STATS;;
	CREATE PROCEDURE PROC_AGENT_UPDATE_TASK_STATS(IN AV_IP_STATS NUMERIC(2,0),IN AV_REQID VARCHAR(50),OUT TEST_RESULT VARCHAR(50) )
	BEGIN
		DECLARE	LN_TASKID NUMERIC(19,0);
		DECLARE	LN_IP_ID NUMERIC(19,0);
		DECLARE TEMPX NUMERIC(19,0);
				
		SELECT COUNT(1) INTO TEMPX FROM IEAI_TIMETASK_IP WHERE UUID=AV_REQID;
		IF TEMPX>0 THEN
			SELECT TASKINSID INTO LN_TASKID FROM IEAI_TIMETASK_IP WHERE UUID=AV_REQID  LIMIT 1;
			IF LN_TASKID >0 THEN 
				UPDATE IEAI_TIMETASK_IP SET TASKIPSTATE = AV_IP_STATS WHERE UUID= AV_REQID AND TASKIPSTATE!=-1 AND TASKIPSTATE!=2;
				SELECT ID INTO LN_IP_ID FROM IEAI_TIMETASK_IP WHERE UUID=AV_REQID;
				SET TEST_RESULT = CONCAT (LN_TASKID , ',' ,  LN_IP_ID);
				IF AV_IP_STATS = -5 THEN
					UPDATE IEAI_TIMETASK_RUNTIME SET RUNSTATE=1	WHERE  TASKID=LN_TASKID AND RUNSTATE < 2;
				END IF;
				COMMIT;
			END IF;
		END IF;
	END ;;
	
	DROP PROCEDURE IF EXISTS PROC_RECOVER_UPDATE_TASK_STATS;;
	CREATE PROCEDURE PROC_RECOVER_UPDATE_TASK_STATS(IN AV_IP_STATS NUMERIC(2,0),IN AV_REQID VARCHAR(50),OUT TEST_RESULT VARCHAR(50) )
	BEGIN
		DECLARE	LN_TASKID NUMERIC(19,0);
		DECLARE	LN_IP_ID NUMERIC(19,0);
		DECLARE TEMPX NUMERIC(19,0);
				
		SELECT COUNT(1) INTO TEMPX FROM IEAI_TIMETASK_IP WHERE UUID=AV_REQID;
		IF TEMPX>0 THEN
			SELECT TASKINSID INTO LN_TASKID FROM IEAI_TIMETASK_IP WHERE UUID=AV_REQID  LIMIT 1;
			IF LN_TASKID >0 THEN 
				UPDATE IEAI_TIMETASK_IP SET TASKIPSTATE = AV_IP_STATS WHERE UUID= AV_REQID AND TASKIPSTATE IN (5,-6);
				SELECT ID INTO LN_IP_ID FROM IEAI_TIMETASK_IP WHERE UUID=AV_REQID;
				SET TEST_RESULT = CONCAT (LN_TASKID , ',' ,  LN_IP_ID);
				COMMIT;
			END IF;
		END IF;
	END ;;

	DELIMITER ;;
	
-- 4.7.24 version timing task patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	

		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_AUDIT (IID DECIMAL(19,0) NOT NULL, IPROJECTNAME VARCHAR(255), ITASKNAME VARCHAR(255), ITASKRUNTIME VARCHAR(255), ITASKCOMMAND VARCHAR(1000), ITASKGROUP VARCHAR(255), ITASKDESC VARCHAR(255), IRESOURCES VARCHAR(255), IIP VARCHAR(1800), IENABLE INTEGER, ISERVERIP VARCHAR(255),IGID DECIMAL(19,0), IOLDSERVERIP VARCHAR(255), IRECOVERYTIME TIMESTAMP, ISTOPINSFLAG INTEGER, ITIMEOUTMINS DECIMAL(19,0), ISTARTFORBIDSTATUS INTEGER, IPAUSE INTEGER DEFAULT 0, ITASKTYPE INTEGER DEFAULT -1, ISTATUS DECIMAL(1,0) DEFAULT 0,CONSTRAINT PK_IEAI_TIMETASK_AUDIT PRIMARY KEY(IID));

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.25 version does not have patches for timed tasks
-- 4.7.26 version timing task patch is as follows

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_TIMETASK_OUTPUT' AND INDEX_NAME = 'IDX_TIMETASK_OUTPUT_IPID') THEN
			create index IDX_TIMETASK_OUTPUT_IPID on IEAI_TIMETASK_OUTPUT (IPID);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_TIMETASK_IP_HIS' AND INDEX_NAME = 'IDX_TIMETASK_IP_HIS_IP') THEN
			create index IDX_TIMETASK_IP_HIS_IP on IEAI_TIMETASK_IP_HIS (IP ASC);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_TIMETASK_HIS' AND INDEX_NAME = 'IDX_TIMETASK_HIS_TASKID') THEN
			create index IDX_TIMETASK_HIS_TASKID on IEAI_TIMETASK_HIS (TASKID ASC);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
 	CREATE PROCEDURE UPPRDE()
	BEGIN	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE  TABLE_NAME = 'IEAI_TIMETASK_IP_HIS' AND INDEX_NAME = 'IDX_TIMETASK_IP_HIS_TASKHISID') THEN
			create index IDX_TIMETASK_IP_HIS_TASKHISID on IEAI_TIMETASK_IP_HIS (TASKHISID ASC);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 4.7.27 version timing task patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	

		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_INSTANCE_HIS ( ID DECIMAL(19,0) NOT NULL,TASKINFOID DECIMAL(19,0) NOT NULL,TASKNAME VARCHAR(255),TASKRUNTIME  VARCHAR(255),TASKCOMMAND  VARCHAR(1000),IP VARCHAR(1800),GID   DECIMAL(19,0),TASKGROUPNAME VARCHAR(255),SERVERIP VARCHAR(255),TASKDESC VARCHAR(255),TIMEOUTMINS DECIMAL(19,0),CREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,CONSTRAINT PK_IEAI_TIMETASK_INSTANCE_HIS PRIMARY KEY(ID)  ) ;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	

		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_CONFIG(ID DECIMAL(19,0) NOT NULL,TEXT VARCHAR(100), FLAG DECIMAL(19,0),TEXTVALUE VARCHAR(100),TYPE VARCHAR(100) );

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	

		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_INSTANCE_CACH ( ID DECIMAL(19,0) NOT NULL,TASKINFOID DECIMAL(19,0) NOT NULL,TASKNAME VARCHAR(255),TASKRUNTIME  VARCHAR(255),TASKCOMMAND  VARCHAR(1000),IP VARCHAR(1800),GID   DECIMAL(19,0),TASKGROUPNAME VARCHAR(255),SERVERIP VARCHAR(255),TASKDESC VARCHAR(255),TIMEOUTMINS DECIMAL(19,0),CONSTRAINT PK_IEAI_TIMETASK_INSTANCE_CACH PRIMARY KEY(ID)  ) ;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	

		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_RUNTIME_CACH ( TASKHISID DECIMAL(19,0) NOT NULL,TASKID DECIMAL(19,0) NOT NULL,STARTTIME TIMESTAMP NOT NULL , ENDTIME TIMESTAMP NOT NULL,INSRESULT INTEGER NOT NULL,IMMED INTEGER ,CONSTRAINT IEAI_TIMETASK_RUNTIME_CACH PRIMARY KEY(TASKHISID)  ) ;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	

		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_IP_CACH ( ID   DECIMAL(19,0) NOT NULL,TASKHISID  DECIMAL(19,0),IP   VARCHAR(30),TASKIPSTATE INTEGER,CONSTRAINT IEAI_TIMETASK_IP_CACH PRIMARY KEY(ID)  ) ;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	CREATE PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM IEAI_TIMETASK_CONFIG  P WHERE P.FLAG='1') THEN
			INSERT INTO IEAI_TIMETASK_CONFIG (ID,TEXT,FLAG,TEXTVALUE,TYPE) VALUES ('5','定时任务历史数据转移','1','2','定时任务') ;
		END IF;		
	END;;
    DELIMITER ;
    CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_TIMETASK_MOVE_HIS;;
	CREATE PROCEDURE PROC_TIMETASK_MOVE_HIS (IN AV_TASKID NUMERIC(19,0),OUT TEST_RESULT VARCHAR(50))
		BEGIN
		DECLARE	LN_TASKID NUMERIC(19,0);
		DECLARE	LN_IP_NUM NUMERIC(19,0);
		DECLARE	LN_IP_OK_NUM NUMERIC(19,0);
		DECLARE LN_KILL_NUM NUMERIC(19,0);
		DECLARE LN_INSTANCE_STATE NUMERIC(2,0) DEFAULT 0;
		
		SET LN_TASKID = AV_TASKID;
		SET TEST_RESULT='false';
		
		SELECT COUNT(ID) INTO LN_IP_OK_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND (TASKIPSTATE =0 OR TASKIPSTATE=-1);
		SELECT COUNT(ID) INTO LN_IP_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID;
		IF LN_IP_OK_NUM = LN_IP_NUM THEN
			SELECT COUNT(1) INTO LN_KILL_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND TASKIPSTATE=-1;
			IF LN_KILL_NUM >0 THEN 
				SET LN_INSTANCE_STATE=2;
			END IF;
			INSERT INTO IEAI_TIMETASK_HIS (TASKHISID,TASKID,STARTTIME,ENDTIME,INSRESULT) SELECT A.TASKINSID,A.TASKID,A.STARTTIME,CURRENT_TIMESTAMP,LN_INSTANCE_STATE FROM IEAI_TIMETASK_RUNTIME A WHERE A.TASKID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_RUNTIME WHERE TASKID = LN_TASKID ;
			INSERT INTO IEAI_TIMETASK_IP_HIS (ID,TASKHISID,IP,TASKIPSTATE) SELECT B.ID,B.TASKINSID,B.IP,B.TASKIPSTATE FROM IEAI_TIMETASK_IP B WHERE B.TASKINSID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_IP WHERE TASKINSID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_TIMEOUTINS WHERE INSTANCEID in (SELECT TASKID FROM IEAI_TIMETASK_HIS WHERE TASKHISID=LN_TASKID);		
			SET TEST_RESULT='true';
		END IF;
		COMMIT;
	END;;
	DELIMITER ;;
	
-- 8.0.0 version timing task patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN

	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_IP' AND COLUMN_NAME = 'ICOM_NAME') THEN
		ALTER TABLE IEAI_TIMETASK_IP ADD COLUMN ICOM_NAME VARCHAR (255);
	END IF;
	
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_IP_HIS' AND COLUMN_NAME = 'ICOM_NAME') THEN
		ALTER TABLE IEAI_TIMETASK_IP_HIS ADD COLUMN ICOM_NAME VARCHAR (255);
	END IF;

	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_TIMETASK_MOVE_HIS;;
	CREATE PROCEDURE PROC_TIMETASK_MOVE_HIS (IN AV_TASKID NUMERIC(19,0),OUT TEST_RESULT VARCHAR(50))
		BEGIN
		DECLARE	LN_TASKID NUMERIC(19,0);
		DECLARE	LN_IP_NUM NUMERIC(19,0);
		DECLARE	LN_IP_OK_NUM NUMERIC(19,0);
		DECLARE LN_KILL_NUM NUMERIC(19,0);
		DECLARE LN_INSTANCE_STATE NUMERIC(2,0) DEFAULT 0;
		
		SET LN_TASKID = AV_TASKID;
		SET TEST_RESULT='false';
		
		SELECT COUNT(ID) INTO LN_IP_OK_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND (TASKIPSTATE =0 OR TASKIPSTATE=-1);
		SELECT COUNT(ID) INTO LN_IP_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID;
		IF LN_IP_OK_NUM = LN_IP_NUM THEN
			SELECT COUNT(1) INTO LN_KILL_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND TASKIPSTATE=-1;
			IF LN_KILL_NUM >0 THEN 
				SET LN_INSTANCE_STATE=2;
			END IF;
			INSERT INTO IEAI_TIMETASK_HIS (TASKHISID,TASKID,STARTTIME,ENDTIME,INSRESULT) SELECT A.TASKINSID,A.TASKID,A.STARTTIME,CURRENT_TIMESTAMP,LN_INSTANCE_STATE FROM IEAI_TIMETASK_RUNTIME A WHERE A.TASKID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_RUNTIME WHERE TASKID = LN_TASKID ;
			INSERT INTO IEAI_TIMETASK_IP_HIS (ID,TASKHISID,IP,TASKIPSTATE,ICOM_NAME) SELECT B.ID,B.TASKINSID,B.IP,B.TASKIPSTATE,B.ICOM_NAME  FROM IEAI_TIMETASK_IP B WHERE B.TASKINSID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_IP WHERE TASKINSID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_TIMEOUTINS WHERE INSTANCEID in (SELECT TASKID FROM IEAI_TIMETASK_HIS WHERE TASKHISID=LN_TASKID);		
			SET TEST_RESULT='true';
		END IF;
		COMMIT;
	END;;
	DELIMITER ;;
	
-- 8.2.0 version timing task patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'IP' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_TIMETASK_INFO  ADD IP_TMP VARCHAR(4000);
			UPDATE IEAI_TIMETASK_INFO SET IP_TMP=IP;
			UPDATE IEAI_TIMETASK_INFO SET IP=NULL;
			ALTER TABLE IEAI_TIMETASK_INFO MODIFY IP LONGTEXT;
			UPDATE IEAI_TIMETASK_INFO SET IP=IP_TMP;
			ALTER TABLE IEAI_TIMETASK_INFO DROP COLUMN IP_TMP;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_AUDIT' AND COLUMN_NAME = 'IIP' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_TIMETASK_AUDIT  ADD IIP_TMP VARCHAR(4000);
			UPDATE IEAI_TIMETASK_AUDIT SET IIP_TMP=IIP;
			UPDATE IEAI_TIMETASK_AUDIT SET IIP=NULL;
			ALTER TABLE IEAI_TIMETASK_AUDIT MODIFY IIP LONGTEXT;
			UPDATE IEAI_TIMETASK_AUDIT SET IIP=IIP_TMP;
			ALTER TABLE IEAI_TIMETASK_AUDIT DROP COLUMN IIP_TMP;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME = 'IP' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_TIMETASK_INSTANCE  ADD IP_TMP VARCHAR(4000);
			UPDATE IEAI_TIMETASK_INSTANCE SET IP_TMP=IP;
			UPDATE IEAI_TIMETASK_INSTANCE SET IP=NULL;
			ALTER TABLE IEAI_TIMETASK_INSTANCE MODIFY IP LONGTEXT;
			UPDATE IEAI_TIMETASK_INSTANCE SET IP=IP_TMP;
			ALTER TABLE IEAI_TIMETASK_INSTANCE DROP COLUMN IP_TMP;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_AUDIT' AND COLUMN_NAME = 'TASKLEVEL') THEN
			ALTER TABLE IEAI_TIMETASK_AUDIT  ADD TASKLEVEL NUMERIC(2) DEFAULT 1;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'TASKLEVEL') THEN
			ALTER TABLE IEAI_TIMETASK_INFO  ADD TASKLEVEL NUMERIC(2) DEFAULT 1;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
-- 8.3.0 version timing task patch is as follows	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
  	create PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_RUNDATA_STORAGE (IID DECIMAL(19,0) not null,TASKID VARCHAR(255),ITIME VARCHAR(255), GID VARCHAR(255),GNAME VARCHAR(255),TASKSUM VARCHAR(255),TASKNAME VARCHAR(255),CRONDAYTOTALNUM VARCHAR(255),EFFECTNUM VARCHAR(255),INVALIDNUM VARCHAR(255),NORMALNUM VARCHAR(255),EXCEPTIONNUM VARCHAR(255),FORCESTORNUM VARCHAR(255),EXECTOTALNUM VARCHAR(255),TASKENABLE VARCHAR(255),CONSTRAINT PK_TT_RUNDATA_STORAGE PRIMARY KEY(IID));
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
  	create PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_TIMETASK_LOAD_SWITCH (IID DECIMAL(19,0) not null,TASKID DECIMAL(19,0),ITIME VARCHAR(19), SWITCHUSER VARCHAR(50),OLDSERVERIP VARCHAR(20),SERVERIP VARCHAR(20),SERVERID DECIMAL(19,0),STATUS INTEGER default 0,OLDENABLE VARCHAR(50),CONSTRAINT PK_TT_RUNDATA_STORAGE PRIMARY KEY(IID));
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_TIMETASK_MOVE_HIS;;
	CREATE PROCEDURE PROC_TIMETASK_MOVE_HIS (IN AV_TASKID NUMERIC(19,0),OUT TEST_RESULT VARCHAR(50))
		BEGIN
		DECLARE	LN_TASKID NUMERIC(19,0);
		DECLARE	LN_IP_NUM NUMERIC(19,0);
		DECLARE	LN_IP_OK_NUM NUMERIC(19,0);
		DECLARE LN_KILL_NUM NUMERIC(19,0);
		DECLARE LN_INSTANCE_STATE NUMERIC(2,0) DEFAULT 0;
		
		SET LN_TASKID = AV_TASKID;
		SET TEST_RESULT='false';
		
		SELECT COUNT(ID) INTO LN_IP_OK_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND (TASKIPSTATE =0 OR TASKIPSTATE=-1);
		SELECT COUNT(ID) INTO LN_IP_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID;
		IF LN_IP_OK_NUM = LN_IP_NUM THEN
			SELECT COUNT(1) INTO LN_KILL_NUM FROM IEAI_TIMETASK_IP WHERE TASKINSID =LN_TASKID AND TASKIPSTATE=-1;
			IF LN_KILL_NUM >0 THEN 
				SET LN_INSTANCE_STATE=2;
			END IF;
			INSERT INTO IEAI_TIMETASK_HIS (TASKHISID,TASKID,STARTTIME,ENDTIME,INSRESULT,IMMED) SELECT A.TASKINSID,A.TASKID,A.STARTTIME,CURRENT_TIMESTAMP,LN_INSTANCE_STATE,A.IMMED FROM IEAI_TIMETASK_RUNTIME A WHERE A.TASKID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_RUNTIME WHERE TASKID = LN_TASKID ;
			INSERT INTO IEAI_TIMETASK_IP_HIS (ID,TASKHISID,IP,TASKIPSTATE,ICOM_NAME) SELECT B.ID,B.TASKINSID,B.IP,B.TASKIPSTATE,B.ICOM_NAME  FROM IEAI_TIMETASK_IP B WHERE B.TASKINSID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_IP WHERE TASKINSID = LN_TASKID;
			DELETE FROM IEAI_TIMETASK_TIMEOUTINS WHERE INSTANCEID in (SELECT TASKID FROM IEAI_TIMETASK_HIS WHERE TASKID=LN_TASKID);
			SET TEST_RESULT='true';
		END IF;
		COMMIT;
	END;;
	DELIMITER ;
-- 8.4.0 version does not have patches for timed tasks
-- 8.5.0 version does not have patches for timed tasks
-- 8.6.0 version timing task patch is as follows
    DELIMITER ;;
          DROP PROCEDURE IF EXISTS UPPRDE;;
          CREATE PROCEDURE UPPRDE()
          BEGIN

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'IREDOTIME') THEN
                ALTER TABLE IEAI_TIMETASK_INFO ADD IREDOTIME NUMERIC(19) DEFAULT 0;
            END IF;

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'IREDONUM') THEN
                ALTER TABLE IEAI_TIMETASK_INFO ADD IREDONUM NUMERIC(19) DEFAULT 0;
            END IF;
          END;;
    DELIMITER ;
    CALL UPPRDE();
    DELIMITER ;;
          DROP PROCEDURE IF EXISTS UPPRDE;;
          CREATE PROCEDURE UPPRDE()
          BEGIN

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_IP' AND COLUMN_NAME = 'IREDONUM') THEN
                ALTER TABLE IEAI_TIMETASK_IP ADD IREDONUM NUMERIC(19) DEFAULT 0;
            END IF;
            
            IF NOT EXISTS (SELECT * FROM IEAI_SCRIPT_AUDITING WHERE IID=15) THEN
							INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(15,'定时任务发布专审权限');
						END IF;
            COMMIT;
          END;;
    DELIMITER ;
    CALL UPPRDE();
	
-- 8.8.0 version timing task patch is as follows
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_AUDIT' AND COLUMN_NAME = 'EXECUTE_CENTER') THEN
			ALTER TABLE IEAI_TIMETASK_AUDIT  ADD EXECUTE_CENTER VARCHAR(10) ;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INFO' AND COLUMN_NAME = 'EXECUTE_CENTER') THEN
			ALTER TABLE IEAI_TIMETASK_INFO  ADD EXECUTE_CENTER VARCHAR(10) ;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_TIMETASK_INSTANCE' AND COLUMN_NAME = 'EXECUTE_CENTER') THEN
			ALTER TABLE IEAI_TIMETASK_INSTANCE  ADD EXECUTE_CENTER VARCHAR(10) ;
		END IF; 
		END;;
	DELIMITER ;
	CALL UPPRDE();
