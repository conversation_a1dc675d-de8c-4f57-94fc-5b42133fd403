package com.ideal.service.neweswitch.assessplan;

import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.neweswitch.assessplan.AssessPlanFileSmallModel;
import com.ideal.ieai.server.neweswitch.assessplan.AssessPlanManager;
import com.ideal.ieai.server.neweswitch.assessplan.AssessPlanModel;
import com.ideal.ieai.server.neweswitch.assessplan.AssessResearchReqVo;
import com.ideal.ieai.server.neweswitch.capability.DomainIndexModel;
import com.ideal.ieai.server.neweswitch.capability.PowerAbilityModel;
import com.ideal.ieai.server.neweswitch.drillManage.DrillManageManager;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import net.sf.hibernate.Hibernate;
import org.apache.log4j.Logger;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class AssessPlanService
{

    private static final Logger log = Logger.getLogger(AssessPlanService.class);
    private static final String MESSAGE = "message";
    private static final String SUCCESS = "success";
    
    
    private static final String COUNT = "count";
    private static final String LIST = "list";
        
    
    public AssessPlanService()
    {
    }

    private static AssessPlanService intance = new AssessPlanService();
    public static AssessPlanService getInstance ()
    {
        if (intance == null)
        {
            intance = new AssessPlanService();
        }
        return intance;
    }


    
    
    public Map getAssessPlanList ( AssessPlanModel model, int sysType,long userId )
    {
        

        String method="getAssessPlanList";
        Map map = new HashMap();
        AssessPlanManager apm = AssessPlanManager.getInstance();
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection(method, log, sysType);

                map = apm.getAssessPlanList(conn,model,userId); 
            
            
        } catch (Exception e)
        {
            log.error("获取评估计划失败",e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "获取评估计划失败！");
        }finally{
            DBResource.closeConnection(conn, method, log);
        }
        return map;
    
    
    }
//保存
    public Map save(AssessPlanModel bean, String userId) {
        Map res = new HashMap();
        res.put(SUCCESS, false);
        res.put(MESSAGE, "保存失败");

        if (DrillManageManager.getInstance().isExistPlanName(bean.getIplanname(), 0, 0)) {
            res.put(MESSAGE, "计划名称已存在");
        } else {
            if (AssessPlanManager.getInstance().save(bean, userId)) {
                res.put(SUCCESS, true);
                res.put(MESSAGE, "保存成功");
            }
        }

        return res;
    }



    public List<PowerAbilityModel> getpowerlist() throws RepositoryException {
        return AssessPlanManager.getInstance().getpowerlist();
    }

    public List<DomainIndexModel> getsurveylist() throws RepositoryException {
        return AssessPlanManager.getInstance().getsurveylist();
    }
    
    
    public List<Map> getPowerScoreCombox() throws RepositoryException {
        return AssessPlanManager.getInstance().getPowerScoreCombox();
    }
    
    public Map getAssessResearchList(AssessResearchReqVo vo,int dbtype,Long userId) throws RepositoryException{
        return AssessPlanManager.getInstance().getAssessResearchList (vo,dbtype,userId);
    }

    
    public Map<String,Object> uploadAssessFile(CommonsMultipartFile file,Long planviewid,Long planid,int systype) throws SQLException{
        String method = "uploadAssessFile";
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement psUpdate = null;
        PreparedStatement psviewUpdate = null;
        ResultSet rs = null;
        
        PreparedStatement psPlan = null;
        PreparedStatement psUpdatePlan = null;
        ResultSet rsPlan = null;
        
        Map<String,Object> map = new HashMap<String, Object>();
        String sql = " insert into IEAI_POWER_SCORE_FILE  (iid , ISCOREPVIEWID ,IFILENAME, IFILE) values(?,?,?,?) ";
//        if (DBManager.Orcl_Faimily())
//        {
//            sql = " insert into IEAI_POWER_SCORE_FILE ar (ar.iid , ar.ISCOREPVIEWID ,ar.IFILENAME, ar.IFILE) values(?,?,?,empty_blob()) ";
//        } else
//        {
//            sql = " insert into IEAI_POWER_SCORE_FILE  (iid , ISCOREPVIEWID ,IFILENAME, IFILE) values(?,?,?,?) ";
//        }
        String sqlUpdate = "select a.IFILE from IEAI_POWER_SCORE_FILE a  where a.iid = ? for update ";
        
        String viewUpdate ="UPDATE IEAI_SCORE_PLAN_VIEW SET STATUS=? WHERE IID=?";
        
        String plansql = "SELECT COUNT(1) AS CNT FROM IEAI_SOCRE_PLAN p,IEAI_SCORE_PLAN_VIEW PV WHERE P.IID=PV.ISCOREPLANID AND P.IID=? AND PV.STATUS<>2";
        String updateplanSql = "UPDATE IEAI_SOCRE_PLAN SET ISTATE=? WHERE IID=? ";
        try
        {
            byte[] filebytes = file.getBytes();
            Blob fileblob = Hibernate.createBlob(filebytes);
            conn = DBResource.getJDBCConnection(method, log, systype);
           
            Long iid = IdGenerator.createId("IEAI_POWER_SCORE_FILE", conn);
            ps = conn.prepareStatement(sql);
            ps.setLong(1,iid);
            ps.setLong(2, planviewid);
            ps.setString(3, file.getOriginalFilename());
//            if (JudgeDB.IEAI_DB_TYPE==2||JudgeDB.IEAI_DB_TYPE==3)
//            {
                InputStream inputBlob = new ByteArrayInputStream(filebytes);
                ps.setBinaryStream(4, inputBlob, filebytes.length);
//            }
            
           
            
            ps.execute();
            
//            psUpdate = conn.prepareStatement(sqlUpdate);
//            psUpdate.setLong(1, iid);
//            rs = psUpdate.executeQuery();
//            OutputStream blobOutputStream = null;
//            while (rs.next())
//            {
//                Blob blob = (Blob) rs.getBlob(1);
//                if (DBManager.Orcl_Faimily())
//                {
//                    blobOutputStream = ((oracle.sql.BLOB) blob).getBinaryOutputStream();
//                } else
//                {
//                    blobOutputStream = new ByteArrayOutputStream();
//                }
//                try
//                {
//                    FileCopyUtils.copy(fileblob.getBinaryStream(), blobOutputStream);
//                } catch (IOException e)
//                {
//                    log.error("Blob存储失败", e);
//                }
//            }
//            blobOutputStream.close();
            
            psviewUpdate = conn.prepareStatement(viewUpdate);
            psviewUpdate.setLong(1, 2);
            psviewUpdate.setLong(2, planviewid);
            psviewUpdate.executeUpdate();
            
            psPlan = conn.prepareStatement(plansql);
            psPlan.setLong(1, planid);
            rsPlan = psPlan.executeQuery();
            int  count = -1;
            if(rsPlan.next()) {
                count = rsPlan.getInt("CNT");
            }else {
                log.info("计划ID【"+planid+"】存在未调研的过程域，故本次调研不改变计划状态。。。");
            }
            
            if( count==0) {
                //更新计划状态为6
                psUpdatePlan = conn.prepareStatement(updateplanSql);
                psUpdatePlan.setString(1, "6");
                psUpdatePlan.setLong(2, planid);
                psUpdatePlan.executeUpdate();
            }
            
            conn.commit();
            map.put(SUCCESS, true);
            map.put(MESSAGE, "调研文档上传成功！");
        }   catch (Exception e)
        {
            conn.rollback();
            log.error("调研文档上传出现异常", e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "调研文档上传出现异常;");
        }finally {
            DBResource.closePreparedStatement(psUpdatePlan, method, log);
            DBResource.closePreparedStatement(psviewUpdate, method, log);
            DBResource.closePreparedStatement(psUpdate, method, log);
            DBResource.closePSRS(rsPlan, psPlan, method, log);
            DBResource.closePSRS(rs, ps, method, log);
            DBResource.closeConnection(conn, method, log);
        }
        return map;
    }

    public List<AssessPlanFileSmallModel> getAssessFiles(String  planviewid,int dbtype) throws RepositoryException {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<AssessPlanFileSmallModel> list = new ArrayList<AssessPlanFileSmallModel>();
        String sql = "SELECT IFILENAME,IFILE FROM IEAI_POWER_SCORE_FILE WHERE ISCOREPVIEWID=?";
        try
        {
            conn = DBResource.getConnection("getAssessFilename", log, dbtype);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.parseLong(planviewid));
            rs = ps.executeQuery();
            while (rs.next())
            {
                AssessPlanFileSmallModel model = new AssessPlanFileSmallModel();
                model.setIfilename(rs.getString("IFILENAME"));
                Blob blob = rs.getBlob("IFILE");
                
                if (blob == null)
                {
                    model.setIfile( new byte[0]);
                }else {
                    model.setIfile(transBlobtoByte(blob));
                }
                list.add(model);
               
            }
            
        } catch (RepositoryException e)
        {
            
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } catch (Exception e)
        {
            log.error("获取调研文档名称出错！", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally {
            
            DBResource.closeConn(conn, rs, ps, "getAssessFiles", log);
        }
        return list;
        
    }
    
    
    private byte[]  transBlobtoByte(Blob blob) throws RepositoryException {
        byte[] result = null;
        if(blob!=null) {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            InputStream in = null;
            try
            {
                in = blob.getBinaryStream();
            } catch (SQLException e2)
            {
                log.error("调研文档blob转换二进制流失败！ ", e2);
                throw new RepositoryException(ServerError.ERR_DB_ERROR);
            }
            byte buff[] = new byte[128];

            try
            {
                while (true)
                {
                    int c = in.read(buff);
                    if (c <= 0)
                    {
                        break;
                    }
                    out.write(buff, 0, c);
                }
                result = out.toByteArray();

            } catch (IOException e)
            {
                log.error("调研文档blob写入二进制流失败! ", e);
                throw new RepositoryException(ServerError.ERR_DB_ERROR);

            } finally
            {
                try
                {
                    in.close();
                    out.close();
                } catch (IOException e1)
                {
                    log.error("调研文档blob写入二进制流关闭资源失败! ", e1);
                    throw new RepositoryException(ServerError.ERR_DB_ERROR);
                }
            }
            
            
        }
        
        
        return result;
    }
}
