package com.ideal.ieai.server.repository.monitor;

import com.ideal.ieai.core.Environment;
import com.ideal.util.StringUtil;
import org.apache.commons.net.telnet.TelnetClient;
import org.apache.log4j.Logger;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SendCibSyslog
{
    private static final Logger log = Logger.getLogger(SendCibSyslog.class);

    public void sendsysLog ( String msg )
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String path =Environment.getInstance().getCibTivoliPath();
//        String ip = Environment.getInstance().getCibTivoliIp();
//        int port = Environment.getInstance().getCibTivoliPort();
//        String bkip = Environment.getInstance().getCibTivoliBkip();
//        int bkport = Environment.getInstance().getCibTivoliBkport();
//        String cmd = "";

        if(path==null) {// || ip==null || bkip==null
            log.error("tivoli告警配置文件缺少配置项");
            return;
        }
        StringBuilder cmdSb = new StringBuilder(path + File.separator);
        int sysType = Environment.getInstance().getSys_type();
        if(sysType==Environment.OS_TYPE_CODE_WIN){
            cmdSb.append("posteifmsg");
        }else{
            cmdSb.append("posteifmsg_linux");
        }
        String serverIp="";
        try{
            serverIp=Environment.getInstance().getServerIP();
        }catch (Exception e){
            log.error("get serverIp is error",e);
        }
        cmdSb.append(" -f ");
        cmdSb.append(path).append(File.separator).append("eif.conf");
        cmdSb.append(" -r ").append("${severity}");
        cmdSb.append(" -m \"").append("${msgInfo}").append("\"");
        cmdSb.append(" \"hostname=").append("${hostName}").append("\"");
        cmdSb.append(" \"origin=").append(serverIp).append("\"");
        cmdSb.append(" YWLXXErr");
        cmdSb.append(" \"YWLXX").append(sdf.format(new Date())).append("\"");

        String msgNew = cmdSb.toString();

        msg=msg.replaceAll("\\s+", " ");
        String[] midMes = msg.split(" ");

        for (String midStr : midMes)
        {
            Pattern pattern = Pattern.compile("severity\\s*=\\s*(\\S+)");
            Matcher matcher = pattern.matcher(midStr);
            if (matcher.find())
            {
                String severity = matcher.group(1);
                severity = severity.replaceAll("\"", "");
//                cmdSb.append(" -r ").append(severity);
                msgNew=msgNew.replace("${severity}",severity);
            }

            Pattern patternHost = Pattern.compile("hostname\\s*=\\s*(\\S+)");
            Matcher matcherHost = patternHost.matcher(midStr);
            if (matcherHost.find())
            {
                String hostName = matcherHost.group(1);
                hostName = hostName.replaceAll("\"", "");
//                cmdSb.append(" \"hostname=").append(hostName).append("\"");
                msgNew=msgNew.replace("${hostName}",hostName);
            }
            Pattern patternMsg = Pattern.compile("msg\\s*=\\s*(\\S+)");
            Matcher matcherMsg = patternMsg.matcher(midStr);
            if (matcherMsg.find())
            {
                String msgInfo = matcherMsg.group(1);
                msgInfo = msgInfo.replaceAll("\"", "");
//                cmdSb.append(" -m \"").append(msgInfo).append("\"");
                msgNew=msgNew.replace("${msgInfo}",msgInfo);
            }

//            Pattern patternIp = Pattern.compile("ip\\s*=\\s*(\\S+)");
//            Matcher matcherIp = patternIp.matcher(midStr);
//            if (matcherIp.find())
//            {
//                String ipInfo = matcherIp.group(1);
//                ipInfo = ipInfo.replaceAll("\"", "");
////                cmdSb.append(" \"origin=").append(ipInfo).append("\"");
//                msgNew=msgNew.replace("${ipInfo}",ipInfo);
//            }
        }
        Pattern pattern = Pattern.compile("\\$\\{[^}]+}");
        Matcher matcher = pattern.matcher(msgNew);
        msgNew = matcher.replaceAll("");

//        boolean isconnect = getHostConnect(ip,port);
//
//        if(isconnect) {
////            cmdSb.append(" \"origin="+ip+"\"");
//            cmd = path + File.separator+"posteifmsg -S " + ip + " -p " + port + " " + msg;
//        }else {
//            boolean isbkconnect = getHostConnect(bkip,bkport);
//            if(isbkconnect) {
////                cmdSb.append("\"origin="+bkip+"\"");
//                cmd = path + File.separator+"posteifmsg -S " + bkip + " -p " + bkport + " " + msg;
//            }else {
//                log.error("tivoli告警主备ip连接不通");
//                return;
//            }
//        }

//        log.info("tivoli send oldcmd:"+cmd);
        log.info("tivoli send newcmd:"+msgNew);
        BufferedReader br = null;
        List<String> list = new ArrayList<String>();
        String result = "";
        try
        {
            Process ps = Runtime.getRuntime().exec(msgNew);
            br = new BufferedReader(new InputStreamReader(ps.getInputStream()));
            String line;
            while ((line = br.readLine()) != null)
            {
                list.add(line);
                try
                {
                    Thread.sleep(500);
                } catch (Exception e)
                {
                    log.error("SendCibSyslog.Thread sleep is error",e);
                }
            }
            ps.waitFor();
            int exitValue = ps.exitValue();
            if (exitValue == 0 && !list.isEmpty())
            {
                result = list.get(list.size() - 1);
            }
            log.info("tivoli命令退出码："+exitValue+"，tivoli返回信息："+ StringUtil.joinForQuotationMarks(list));
        } catch (Exception e)
        {
            log.error("执行命令出错！" + e.getMessage());
        } finally
        {
            try
            {
                if (br != null)
                {
                    br.close();
                }
            } catch (Exception e2)
            {
                log.error(e2.getMessage());
            }
        }
    }

    //判断ip、端口是否连通
    private boolean getHostConnect ( String ip, int port )
    {
        TelnetClient telnet = new TelnetClient();
        try
        {
            telnet.connect(ip);//Integer.valueOf(port)按照现场要求去掉端口号
            telnet.disconnect();
            return true;
        } catch (Exception e)
        {
            log.error("tivoli告警ip："+ip+",连接失败");//端口号："+port+"
            return true;
        }
    }
}
