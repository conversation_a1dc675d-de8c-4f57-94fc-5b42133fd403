package com.ideal.ieai.studio.view.cmdui;

import java.awt.Image;
import java.awt.Toolkit;
import java.net.URL;
import java.net.URLClassLoader;

import javax.swing.ImageIcon;

/**
 * <p>
 * Title: IEAI Studio
 * </p>
 * <p>
 * Description: The class define some constant icons.
 * </p>
 * <p>
 * Copyright: Copyright (c) 2002
 * </p>
 * <p>
 * Company: Ideal Technologies, Inc.
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */

public class CmdIcons
{

    static final public ImageIcon imageProject         = loadImageIcon("project.gif");
    static final public ImageIcon imageImport          = loadImageIcon("import.gif");
    static final public ImageIcon imageStructDef       = loadImageIcon("struct.gif");
    static final public ImageIcon imageIntFunc         = loadImageIcon("function.gif");
    static final public ImageIcon imageOpen            = loadImageIcon("open.gif");
    static final public ImageIcon imageNewProject      = loadImageIcon("new_proj.gif");
    static final public ImageIcon imageSave            = loadImageIcon("save.gif");
    static final public ImageIcon imageApply           = loadImageIcon("apply.gif");
    static final public ImageIcon imageSaveAs          = loadImageIcon("saveas.gif");
    static final public ImageIcon imageRemove          = loadImageIcon("remove_item.gif");
    static final public ImageIcon imageEdit            = loadImageIcon("edit.gif");
    static final public ImageIcon imageNewImport       = loadImageIcon("new_import.gif");

    static final public ImageIcon imageAdaptor         = loadImageIcon("adaptor.gif");

    static final public ImageIcon imageNewEnt          = loadImageIcon("new_entity.gif");
    static final public ImageIcon imageNewVar          = loadImageIcon("new_var.gif");
    static final public ImageIcon imageNewIntFunc      = loadImageIcon("new_function.gif");
    static final public ImageIcon imageNewExFucn       = loadImageIcon("new_exfunction.gif");
    static final public ImageIcon imageNewWorkflow     = loadImageIcon("new_workflow.gif");
    static final public ImageIcon imageExit            = loadImageIcon("exit.gif");
    static final public ImageIcon imageCompile         = loadImageIcon("compile.gif");
    static final public ImageIcon imageAbout           = loadImageIcon("about.gif");
    static final public Image     imageIdealLogo       = loadImage("ideal.gif");
    static final public ImageIcon imageIdealLogoLarge  = loadImageIcon("ideal_large.gif");
    static final public ImageIcon imageSwitchState     = loadImageIcon("switch_state.gif");
    static final public Image     imageStudio          = loadImage("ieaistudio.gif");

    static final public ImageIcon imageUpload          = loadImageIcon("upload.gif");
    static final public ImageIcon imageDownload        = loadImageIcon("download.gif");
    static final public ImageIcon imagePack            = loadImageIcon("pack.gif");
    static final public ImageIcon imageUnpack          = loadImageIcon("unpack.gif");
    
//add 命令行上载工程;菜单变更; 为导入、导出、ETG导出功能增加新图标
    static final public ImageIcon imageMExportFile     = loadImageIcon("expfile.gif");
    static final public ImageIcon imageMExportEnv      = loadImageIcon("expenv.gif");
    static final public ImageIcon imageMImport         = loadImageIcon("impfile.gif");
//end
  
    static final public ImageIcon iconNew              = loadImageIcon("new.gif");

    static final public ImageIcon iconRemove           = loadImageIcon("remove.gif");

    static final public ImageIcon imageToggleTree      = loadImageIcon("toggle_tree.gif");
    static final public ImageIcon imageToggleMsg       = loadImageIcon("toggle_msg.gif");
    static final public ImageIcon imageFullScreen      = loadImageIcon("fullscreen.gif");

    static final public ImageIcon imageUndo            = loadImageIcon("undo.gif");
    static final public ImageIcon imageRedo            = loadImageIcon("redo.gif");
    static final public ImageIcon imageCut             = loadImageIcon("cut.gif");
    static final public ImageIcon imageCopy            = loadImageIcon("copy.gif");
    static final public ImageIcon imagePaste           = loadImageIcon("paste.gif");
    static final public ImageIcon imageDelete          = loadImageIcon("delete.gif");
    static final public ImageIcon imageSearch          = loadImageIcon("find.gif");
    static final public ImageIcon imageFindNext        = loadImageIcon("findnext.gif");
    static final public ImageIcon imageReplace         = loadImageIcon("replace.gif");
    static final public ImageIcon imageFunCompiler     = loadImageIcon("compile.gif");

    static final public ImageIcon imageBlank           = loadImageIcon("blank.gif");

    static final public ImageIcon imageActManager      = loadImageIcon("actmanager.gif");

    /**
     * for expression edit panel button swith icon
     */
    static final public ImageIcon imageStringEntry     = loadImageIcon("string_entry.gif");
    static final public ImageIcon imageExpressionEntry = loadImageIcon("expression_entry.gif");

    public static ImageIcon loadImageIcon ( String name )
    {
        URLClassLoader urlLoader = (URLClassLoader) CmdIcons.class.getClassLoader();
        URL fileLoc = urlLoader.findResource("resource/images/cmdimages/" + name);
        ImageIcon ret = new ImageIcon(fileLoc);
        return ret;
    }

    public static Image loadImage ( String name )
    {
        URLClassLoader urlLoader = (URLClassLoader) CmdIcons.class.getClassLoader();
        URL fileLoc = urlLoader.findResource("resource/images/cmdimages/" + name);
        Image ret = Toolkit.getDefaultToolkit().createImage(fileLoc);
        return ret;
    }
}
