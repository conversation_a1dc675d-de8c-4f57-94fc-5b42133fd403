package com.ideal.service.topo.keyactwaittime;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.ideal.ieai.server.repository.topo.keyactwaittime.KeyActWaitTimeManager;
import com.ideal.ieai.server.repository.topo.keyactwaittime.KeyActWaitTimeModel;

public class KeyActWaitTimeServer implements IKeyActWaitTimeServer
{

    @Override
    public List<KeyActWaitTimeModel> quertKeyActWaitTime ( KeyActWaitTimeModel model )
    {
        KeyActWaitTimeManager manager = new KeyActWaitTimeManager();
        if (null == model.getStartTime() || "".equals(model.getStartTime()) || "null".equals(model.getStartTime()))
        {
            model.setStartTime("");
        }
        if (null == model.getEndTime() || "".equals(model.getEndTime()) || "null".equals(model.getEndTime()))
        {
            model.setEndTime("");
        }
        return manager.quertKeyActWaitTime(model);
    }

    @Override
    public int quertKeyActWaitTimeCount ( KeyActWaitTimeModel model )
    {
        KeyActWaitTimeManager manager = new KeyActWaitTimeManager();
        if (null == model.getStartTime() || "".equals(model.getStartTime()) || "null".equals(model.getStartTime()))
        {
            model.setStartTime("");
        }
        if (null == model.getEndTime() || "".equals(model.getEndTime()) || "null".equals(model.getEndTime()))
        {
            model.setEndTime("");
        }
        return manager.quertKeyActWaitTimeCount(model);
    }

    @Override
    public List<KeyActWaitTimeModel> querykeyActWaitTimeSysStore ( KeyActWaitTimeModel model )
    {
        KeyActWaitTimeManager manager = new KeyActWaitTimeManager();
        return manager.querykeyActWaitTimeSysStore(model);
    }

    @Override
    public List<KeyActWaitTimeModel> querykeyActWaitTimeActStore ( KeyActWaitTimeModel model )
    {
        KeyActWaitTimeManager manager = new KeyActWaitTimeManager();
        return manager.querykeyActWaitTimeActStore(model);
    }

    @Override
    public List<KeyActWaitTimeModel> querykeyActWaitTime4Excel ( KeyActWaitTimeModel model )
    {
        KeyActWaitTimeManager manager = new KeyActWaitTimeManager();
        return manager.querykeyActWaitTime4Excel(model);
    }

    @Override
    public void exportkeyActWaitTime ( HttpServletResponse response, KeyActWaitTimeModel model )
    {
        KeyActWaitTimeManager manager = new KeyActWaitTimeManager();
        manager.exportkeyActWaitTime(response, model, manager.querykeyActWaitTime4Excel(model));
    }

}
