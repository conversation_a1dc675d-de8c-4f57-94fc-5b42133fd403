package com.ideal.scriptService.bean.commonTask;

import lombok.Data;

/**
 * <pre>
 *     启动常用任务参数对象bean
 * </pre>
 */
@Data
public class StartCommonTaskBean {
    /**
     * 常用任务主键id 必传
     */
    private int commonTaskId;
    /**
     * 启动类型 默认0，代表是脚本服务化任务启动，如果传 8 代表标准运维，在脚本服务化系统中的执行历史中查询不到启动的该任务
     */
    private int startType;
    /**
     * 本次任务启动的任务名称 可以为空，如果为空，那么本次任务启动的任务名为“远程启动任务”
     */
    private String taskName;
}
