package com.ideal.controller.poc.projectcommit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.common.utils.SessionData;
import com.ideal.controller.ControlConstants;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.pack.PackException;
import com.ideal.ieai.server.poc.project.ProjectBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.poc.projectcommit.ProjectCommitService;

/**
 * 
 * <ul>
 * <li>Title: CollectItemController.java</li>
 * <li>Description:poc 审计功能</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2017年6月18日
 */
@Controller
public class ProjectCommitController
{
    private static final Logger _log = Logger.getLogger(ProjectCommitController.class);

    @RequestMapping("projectcommit.do")
    public String itemManager ( String flowId, HttpServletRequest request )
    {
        return "/poc/projectcommit/projectcommit";
    }

    @RequestMapping("/makesurecommit.do")
    @ResponseBody
    public Map<String, Object> computerSave ( HttpServletResponse response, HttpServletRequest request, Long[] iids )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        String userName = SessionData.getSessionData(request).getUserName();
        long userId = 0;
        try
        {
            userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        } catch (Exception e)
        {
            map.put(ControlConstants.MAP_SUCCESS, false);
            map.put(ControlConstants.MAP_MESSAGE, "查询失败");
            _log.error(e);
            return map;
        }
        UserInfo user = new UserInfo();
        user.setFullName(userName);
        user.setId(userId);
        ProjectCommitService service = new ProjectCommitService();
        for (Long long1 : iids)
        {
            try
            {
                service.commit(long1, user, Constants.IEAI_IEAI);
                map.put(ControlConstants.MAP_SUCCESS, true);
                map.put(ControlConstants.MAP_MESSAGE, "提交成功");
            } catch (PackException e)
            {
                map.put(ControlConstants.MAP_SUCCESS, false);
                map.put(ControlConstants.MAP_MESSAGE, "提交失败");
                _log.error(e);
            } catch (RepositoryException e)
            {
                map.put(ControlConstants.MAP_SUCCESS, false);
                map.put(ControlConstants.MAP_MESSAGE, "提交失败");
                _log.error(e);
            }
        }
        return map;
    }

    @RequestMapping("/makesuredelete.do")
    @ResponseBody
    public Map<String, Object> computerDelete ( HttpServletResponse response, HttpServletRequest request, Long[] iids )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        ProjectCommitService service = new ProjectCommitService();
        service.deletes(iids, Constants.IEAI_IEAI);
        map.put(ControlConstants.MAP_SUCCESS, true);
        map.put(ControlConstants.MAP_MESSAGE, "提交成功");
        return map;
    }

    @RequestMapping("/makesurecommitforone.do")
    @ResponseBody
    public void computerSaveForOne ( HttpServletResponse response, HttpServletRequest request, Long iid )
    {
        String userName = SessionData.getSessionData(request).getUserName();
        long userId = 0;
        try
        {
            userId = Long.parseLong(SessionData.getSessionData(request).getUserInnerCode());
        } catch (Exception e)
        {
            _log.error(e);
        }
        UserInfo user = new UserInfo();
        user.setFullName(userName);
        user.setId(userId);
        ProjectCommitService service = new ProjectCommitService();
        try
        {
            service.commit(iid, user, Constants.IEAI_IEAI);
        } catch (PackException e)
        {
            _log.error(e);
        } catch (RepositoryException e)
        {
            _log.error(e);
        }

    }

    @RequestMapping("projectpagelist.do")
    @ResponseBody
    public Map<String, Object> listpage ( Long start, Long limit, Long page, String operations,
            HttpServletRequest request, HttpServletResponse response ) throws Exception
    {
        if (operations == null)
            operations = "";
        Map<String, Object> map = new HashMap<String, Object>();
        ProjectCommitService service = new ProjectCommitService();
        List<ProjectBean> list = new ArrayList<ProjectBean>();
        Long total = 0L;
        try
        {
            list = service.pagelist(start + (page - 1) * limit, limit, operations, Constants.IEAI_IEAI);
            total = service.count(operations, Constants.IEAI_IEAI);
            if (!list.isEmpty())
            {
                map.put(ControlConstants.MAP_DATALIST, list);
            } else
            {
                list = new ArrayList<ProjectBean>();
                map.put(ControlConstants.MAP_DATALIST, list);
            }
            map.put(ControlConstants.MAP_SUCCESS, true);
            map.put(ControlConstants.MAP_TOTAL, total);
            map.put(ControlConstants.MAP_MESSAGE, "查询成功");
        } catch (Exception e)
        {
            map.put(ControlConstants.MAP_TOTAL, total);
            map.put(ControlConstants.MAP_SUCCESS, false);
            map.put(ControlConstants.MAP_MESSAGE, "查询失败");
            _log.error(e);
        }
        return map;
    }
}
