

-- V8.8.0 version PROC_GET_RESULT_ALL_REPORT 提升查询效率
CREATE OR REPLACE PROCEDURE PROC_GET_RESULT_ALL_REPORT  (IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_CPID	NUMERIC(19,0);
			DECLARE	LN_SYSID	NUMERIC(19,0);
			DECLARE	LN_NUM	INTEGER;

			-- GET System ID
			SELECT	IID
			INTO		LN_SYSID
			FROM		IEAI_PROJECT
			WHERE		INAME = AV_SYS_NAME
			AND		PROTYPE = 7;

			SET		LN_NUM = AV_END - AV_START;
			
			IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
                        )t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND   t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID	
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


			ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
								AND		t1.CPTIME <= AV_TIME_END
						)t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND		t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		t1.CPTIME <= AV_TIME_END
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


			ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
								AND		t1.CPTIME >= AV_TIME_START
						)t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND		t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		t1.CPTIME >= AV_TIME_START
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


			ELSE

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
                        (
                                SELECT t1.RSDID,t1.CPID,t1.MEID,t1.CPTIME
                                FROM		HD_CHECK_RESULT_DATA_CACHE t1
                                WHERE	EXISTS 
                                ( 
                                        SELECT 1
                                        FROM IEAI_SYS_RELATION  t2,
                                        IEAI_COMPUTER_LIST t3,
                                        IEAI_PROJECT       t4
                                        WHERE t3.CPID = t2.COMPUTERID
                                        AND t2.SYSTEMID = t4.IID
                                        AND t4.INAME = AV_SYS_NAME
                                        AND t4.IID = LN_SYSID
                                        AND t4.PROTYPE = 7
                                        AND t4.IPKGCONTENTID = 0
                                        AND t1.MEID = t2.COMPUTERID
                                )
                                AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
                                AND		t1.CIID = AV_CHECK_ITEM_ID
								AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
						)t5,HD_CHECK_STATUS_CACHE t6
                        WHERE   t5.RSDID=t6.RSDID
                        AND		t5.CPID=t6.CPID
                        ;
				
				INSERT  INTO  TMP_RESULT_ALL_REPORT (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,MEID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
				SELECT  
						t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,t5.MEID,
						t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
						t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
				FROM(			
						SELECT	ROW_NUMBER() OVER() AS RECID,
								t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.MEID,t1.CPID
						FROM    HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	EXISTS 
								( 
										SELECT 1
										FROM IEAI_SYS_RELATION  t2,
										IEAI_COMPUTER_LIST t3,
										IEAI_PROJECT       t4
										WHERE t3.CPID = t2.COMPUTERID
										AND t2.SYSTEMID = t4.IID
										AND t4.INAME = AV_SYS_NAME
										AND t4.IID = LN_SYSID
										AND t4.PROTYPE = 7
										AND t4.IPKGCONTENTID = 0
										AND t2.COMPUTERID = t1.MEID
								)
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
						ORDER BY t1.RSDID DESC,t1.CPTIME DESC,t1.MEID DESC
				)t5,HD_CHECK_STATUS_CACHE t6
				WHERE
						t5.RSDID=t6.RSDID
				AND   	t5.CPID=t6.CPID
				AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;
			END	IF;
		END
		$
-- V8.8.0 version PROC_GET_RESULT_REPORT_GRID 巡检面板提升查询效率
CREATE OR REPLACE PROCEDURE PROC_GET_RESULT_REPORT_GRID  (IN AV_SYS_NAME VARCHAR(255), IN AV_IP VARCHAR(25), IN AV_CHECK_ITEM_ID NUMERIC(12,0), IN AV_START INTEGER, IN AV_END INTEGER, IN AV_TIME_START NUMERIC(19,0), IN AV_TIME_END NUMERIC(19,0), OUT RECORDCOUNT INTEGER)
	LANGUAGE SQL
	BEGIN
		DECLARE	LN_CPID	NUMERIC(19,0);
		DECLARE	LN_SYSID	NUMERIC(19,0);
		DECLARE	LN_NUM	INTEGER;
		SELECT	CPID
		INTO		LN_CPID
		FROM		IEAI_COMPUTER_LIST
		WHERE		IP = AV_IP;
		
		
		SELECT	IID
		INTO		LN_SYSID
		FROM		IEAI_PROJECT
		WHERE		INAME = AV_SYS_NAME
		AND		PROTYPE = 7;

		SET		LN_NUM = AV_END - AV_START;
		
		IF	AV_TIME_START < 0	AND AV_TIME_END < 0 THEN

				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;
		ELSEIF AV_TIME_START < 0 AND AV_TIME_END > 0 THEN
				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND	 t1.CPTIME <= AV_TIME_END
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID
							AND		t1.CPTIME <= AV_TIME_END
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
					AND t5.RECID > AV_START
		       ;


		ELSEIF AV_TIME_START > 0 AND AV_TIME_END < 0 THEN
				SELECT	COUNT(1)
				INTO	RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND	 t1.CPTIME >= AV_TIME_START
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID	    
							AND		t1.CPTIME >= AV_TIME_START
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;


		ELSE
				SELECT	COUNT(1)
				INTO		RECORDCOUNT
				FROM
				(
						SELECT t1.RSDID,t1.CPID
						FROM		HD_CHECK_RESULT_DATA_CACHE t1
						WHERE	t1.MEID = LN_CPID
						AND     (t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
						AND		t1.CIID = AV_CHECK_ITEM_ID
						AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
				)t2,HD_CHECK_STATUS_CACHE t3
				WHERE   t2.RSDID=t3.RSDID
				AND   t2.CPID=t3.CPID
                        ;
			
			INSERT  INTO  TMP_RESULT_REPORT_GRID (CRID,CPID,RSDID,CPTEXT,CPTIME,ISYSID,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,I14,I15,I16,I17,I18,I19,I20)
			SELECT  
							t6.CRID,t6.CPID,t5.RSDID,t5.CPTEXT,t5.CPTIME,t5.ISYSID,
							t6.I1,t6.I2,t6.I3,t6.I4,t6.I5,t6.I6,t6.I7,t6.I8,t6.I9,t6.I10,t6.I11,
							t6.I12,t6.I13,t6.I14,t6.I15,t6.I16,t6.I17,t6.I18,t6.I19,t6.I20
					FROM(			
							SELECT	ROW_NUMBER() OVER() AS RECID,
									t1.RSDID,t1.CPTEXT,t1.CPTIME,t1.ISYSID,t1.CPID
							FROM    HD_CHECK_RESULT_DATA_CACHE t1
							WHERE	t1.MEID = LN_CPID
							AND		(t1.ISYSID = LN_SYSID OR t1.ISYSID = -1)
							AND		t1.CIID = AV_CHECK_ITEM_ID    
							AND		(t1.CPTIME BETWEEN AV_TIME_START AND AV_TIME_END)
							ORDER BY t1.RSDID DESC,t1.CPTIME DESC
					)t5,HD_CHECK_STATUS_CACHE t6
					WHERE
							t5.RSDID=t6.RSDID
					AND   	t5.CPID=t6.CPID
					AND t5.RECID < AV_END
				AND t5.RECID > AV_START
		       ;
		END	IF;
	END
	$
	
CREATE OR REPLACE PROCEDURE PROC_SAVE_WARNING_MESSAGE 
		(
			IN AI_WCODE SMALLINT,
			IN AV_WDATE VARCHAR(23),
			IN AV_WSOURCE VARCHAR(20),
			IN AV_PRGNAME VARCHAR(50),
			IN AN_ERRCODE NUMERIC(12,0),
			IN AV_HOST VARCHAR(40),
			IN AV_IP VARCHAR(15),
			IN AV_CVALUE VARCHAR(128),
			IN AV_THREADHOLD VARCHAR(128),
			IN AV_AMSG VARCHAR(128),
			IN AN_CPID NUMERIC(19,0),
			IN AV_WARN_CODE_LIST VARCHAR(50),
			OUT AN_KEY NUMERIC(19,0),
			OUT AV_RETURN VARCHAR(128)
		)
		LANGUAGE SQL
	BEGIN
		DECLARE	LI_EXISTS	SMALLINT;
		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;
		DECLARE	LI_COUNT		INTEGER;
		DECLARE	LN_KEY		NUMERIC(19,0);
		DECLARE	LI_LOOP		INTEGER;
		DECLARE	LI_LENGTH	INTEGER;
		DECLARE	LV_DATA		VARCHAR(200);
		DECLARE	LI_DATA		INTEGER;
		DECLARE	LI_SWITCH	SMALLINT;
		
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET	RETSQLCODE = SQLCODE;
		
		SET		AN_KEY = -1;
		
		SELECT	NVL(MAX(IID), -1)
		INTO		LN_KEY
		FROM		IEAI_HC_ACTWARNING
		WHERE		IP = AV_IP
		AND		ERRCODE = AN_ERRCODE
		AND		PRGNAME = AV_PRGNAME
		AND		WCODE = AI_WCODE
		AND		THREADHOLD = AV_THREADHOLD
		AND		AMESSAGE = AV_AMSG;
		
		
		IF LN_KEY < 0 THEN
		  INSERT INTO IEAI_HC_ACTWARNING_HIS
				  (
					IID,
					CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					DELUSER,
					DELTIME,
					SUGGESTION,
					DISPOSETYPE
				  )
			  SELECT  IID,
					  CPID,
					WTYPE,
					WCODE,
					WDATE,
					LDATE,
					WSOURCE,
					PRGNAME,
					ERRCODE,
					HOSTNAME,
					W.IP,
					CHKVALUE,
					THREADHOLD,
					AMESSAGE,
					WCOUNT,
					TFLAG,
					WMESSAGE,
					SYSNAME,
					'Auto change Close',
					CURRENT_TIMESTAMP,
					'告警改变或恢复',
					'2'
			  FROM   IEAI_HC_ACTWARNING W
			  WHERE	 W.IP = AV_IP
				AND	 W.AMESSAGE = AV_AMSG
				AND  W.CPID = AN_CPID;
				
				
			  DELETE	FROM	IEAI_HC_ACTWARNING WW
			  WHERE	 WW.IP = AV_IP
				AND	 WW.AMESSAGE = AV_AMSG
				AND  WW.CPID = AN_CPID;
				
		END IF;
		
		IF	LN_KEY >= 0 THEN
			UPDATE	IEAI_HC_ACTWARNING
			SET		WCOUNT = WCOUNT + 1,
						LDATE = TIMESTAMP(AV_WDATE),
						CHKVALUE = AV_CVALUE
			WHERE		IID = LN_KEY;
		ELSE
			CALL PROC_GET_NEXT_PK('IEAI_HC_ACTWARNING', LN_KEY);
			INSERT INTO IEAI_HC_ACTWARNING
					(
						IID,
						WCODE,
						WDATE,
						LDATE,
						WSOURCE,
						PRGNAME,
						ERRCODE,
						HOSTNAME,
						IP,
						CHKVALUE,
						THREADHOLD,
						AMESSAGE,
						WCOUNT,
						TFLAG,
						WMESSAGE,
						SYSNAME,
						CPID
					)
			SELECT	LN_KEY,
						AI_WCODE,
						TIMESTAMP(AV_WDATE),
						TIMESTAMP(AV_WDATE),
						AV_WSOURCE,
						AV_PRGNAME,
						AN_ERRCODE,
						AV_HOST,
						AV_IP,
						AV_CVALUE,
						AV_THREADHOLD,
						AV_AMSG,
						1,
						0,
						NULL,
						FUN_GET_APPS_NAME(AN_CPID, '运维服务自动化系统'),
						AN_CPID
			FROM		IDUAL;
			
			SELECT	WSWITCH
			INTO		LI_SWITCH
			FROM		IEAI_COMPUTER_LIST
			WHERE		IP = AV_IP;
			
			IF LI_SWITCH = 1 THEN
				SET		LI_LOOP = 1;
				SET		LI_DATA = 0;
				
				WHILE	LI_DATA IS NOT NULL
				DO
					SET	LI_DATA = FUN_GET_STRING_NUMBER(AV_WARN_CODE_LIST, LI_LOOP);
					
					IF	AI_WCODE >= LI_DATA THEN
						SET	AN_KEY = LN_KEY;
						SET	LI_DATA = NULL;
					END IF;
					
					SET	LI_LOOP = LI_LOOP + 1;
				END WHILE;
			END IF;
		END IF;
		
		IF	RETSQLCODE = 0 OR RETSQLCODE=100 THEN
			SET	AV_RETURN = 'SCUCCESS';
			COMMIT WORK;
		ELSE
			SET	AV_RETURN = 'FAILURE';
			ROLLBACK WORK;
		END IF;
	END 
	$
