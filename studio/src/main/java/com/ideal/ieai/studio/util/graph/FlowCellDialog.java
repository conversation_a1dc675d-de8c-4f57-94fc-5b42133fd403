package com.ideal.ieai.studio.util.graph;

import java.awt.BorderLayout;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowEvent;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.border.Border;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;

import org.jgraph.JGraph;
import org.jgraph.graph.DefaultGraphCell;

import com.ideal.ieai.studio.StudioRes;
import com.ideal.ieai.studio.view.editorui.acteditor.FlowCellEditor;
import com.ideal.ieai.studio.view.editorui.flowchart.BasicCellObject;
import com.ideal.ieai.studio.view.editorui.flowchart.FlowChartPane;
import com.ideal.ieai.studio.view.editorui.flowchart.FlowGraphHelper;

/**
 * <p>Title: IEAI Studio</p>
 * <p>Description:  The class defint a JDialog object that is display when user double clicks a jgraph cell.</p>
 * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR>
 * @version 3.0
 */

public class FlowCellDialog
    extends com.ideal.util.gui.EscapeDialog
    implements ChangeListener, ActionListener
{

    public FlowCellDialog(Frame p0, DefaultGraphCell Cell, JGraph Graph)
    {
        super(p0, true);
        _cell = Cell;
        _editor = new FlowCellEditor(_cell);
        bottomPane.setBorder(null);
        _editor.startEditing();
        _graph = Graph;
        try
        {
            jbInit();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

//        this.getContentPane().setLayout(new BorderLayout());
//        this.getContentPane().add(getPane(), BorderLayout.CENTER);
//        btnApply.setEnabled(false);
//        btnOK.setEnabled(false);
    }

//    private JPanel getPane()
//    {
//        cellPane.setLayout(borderLayout1);
//        bottomPane.setLayout(borderLayout2);
//        btnOK.setText(StudioRes.ok);
//        btnApply.setText(StudioRes.getString("view.editorui.DefaultEditorPanel.apply"));
//        btnCancel.setText(StudioRes.close);
//        cellPane.add(_editor.getEditingComponent(), BorderLayout.CENTER);
//        cellPane.add(bottomPane, BorderLayout.SOUTH);
//        bottomPane.add(buttonPane, BorderLayout.EAST);
//        buttonPane.add(btnOK, null);
//        buttonPane.add(btnApply, null);
//        buttonPane.add(btnCancel, null);
//        btnOK.addActionListener(this);
//        btnApply.addActionListener(this);
//        btnCancel.addActionListener(this);
//        _editor.addChangedListener(this);
//        border1 = border1 = BorderFactory.createEtchedBorder(Color.white, new Color(148, 145, 140));
//        cellPane.setBorder(border1);
//        this.getContentPane().setLayout(new BorderLayout());
//        this.getContentPane().add(cellPane, BorderLayout.CENTER);
//        btnApply.setEnabled(false);
//        btnOK.setEnabled(false);
//
//    }

    private void jbInit() throws Exception
    {
        border = BorderFactory.createEmptyBorder(8, 8, 8, 8);
        cellPane.setBorder(border);
        cellPane.setLayout(borderLayout1);
        bottomPane.setLayout(borderLayout3);
        btnOK.setText(StudioRes.ok);
        btnApply.setText(StudioRes.getString("view.editorui.DefaultEditorPanel.apply"));
        btnClose.setText(StudioRes.close);
        buttonPane.setLayout(gridLayout2);
        gridLayout2.setColumns(0);
        gridLayout2.setHgap(4);
        gridLayout2.setVgap(4);
        jPanel1.setLayout(gridBagLayout1);
        cellPane.add(_editor.getEditingComponent(), BorderLayout.CENTER);
        cellPane.add(bottomPane, BorderLayout.SOUTH);
        buttonPane.add(btnOK, null);
        buttonPane.add(btnApply, null);
        buttonPane.add(btnClose, null);
        bottomPane.add(jPanel1, BorderLayout.EAST);
        jPanel1.add(buttonPane, new GridBagConstraints(0, 0, 1, 1, 1.0, 1.0
            , GridBagConstraints.CENTER, GridBagConstraints.BOTH, new Insets(4, 4, 4, 4), 0, 0));
        btnOK.addActionListener(this);
        btnApply.addActionListener(this);
        btnClose.addActionListener(this);
        _editor.addChangedListener(this);
        this.getContentPane().setLayout(new BorderLayout());
        this.getContentPane().add(cellPane, BorderLayout.CENTER);
        btnApply.setEnabled(false);
        btnOK.setEnabled(false);
        this.setTitle(StudioRes.getString("view.editorui.actconfig"));
    }

    /**
     * When some activity property's editor has changed ,update three buttons and workflow chart panel's state.
     * @param parm1
     */
    public void stateChanged(ChangeEvent parm1)
    {
        btnOK.setEnabled(true);
        btnApply.setEnabled(true);
        btnClose.setEnabled(true);
        if (_cell.getUserObject() instanceof BasicCellObject)
        {
            BasicCellObject obj = (BasicCellObject) _cell.getUserObject();
            FlowChartPane pane = obj.getFlowChartPane();
            pane.setStateChanged();
        }
    }

    /**
     * The method deals with three button's click event.
     * @param e
     */
    public void actionPerformed(ActionEvent e)
    {
        //OK button is pressed,close the editor.
        if (e.getSource() == btnOK)
        {
            if (_editor.canSave())
            {
                _editor.stopEditing();
                _editor.updateObject();
                this.dispose();
                _graph.cancelEditing();
                FlowGraphHelper.updateCellSize(_graph, _cell);
            }
        }
        //Apply button is pressed ,update userobject.
        else if (e.getSource() == btnApply)
        {
            if (_editor.canSave())
            {
                _editor.updateObject();
                btnApply.setEnabled(false);
                btnOK.setEnabled(false);
            }
        }
        //Close button is pressed,display a  JOptionPane jpanel.
        else if (e.getSource() == btnClose)
        {
            if (btnApply.isEnabled())
            {
                switch (JOptionPane.showConfirmDialog(JOptionPane.getFrameForComponent(this),
                    StudioRes.getString(
                    "view.editorui.DefaultEditorPanel.changedWarning"),
                    StudioRes.warn,
                    JOptionPane.YES_NO_CANCEL_OPTION))
                {
                    case JOptionPane.YES_OPTION:
                        if (!_editor.canSave())
                        {
                            return;
                        }
                        _editor.stopEditing();
                        this.dispose();
                        _graph.cancelEditing();
//                        _graph.getModel().remove(new Object[]
//                                                 {});

                        FlowGraphHelper.updateCellSize(_graph, _cell);
                        break;
                    case JOptionPane.NO_OPTION:
                        _editor.cancelEditing();
                        this.dispose();
                        _graph.cancelEditing();
//                        _graph.getModel().remove(new Object[]
//                                                 {});

                        break;
                    default:
                        return;
                }
            }
            else
            {
                _editor.cancelEditing();
                this.dispose();
                _graph.cancelEditing();
                FlowGraphHelper.updateCellSize(_graph, _cell);
//                _graph.getModel().remove(new Object[]
//                                         {});
            }
        }
    }

    protected void processWindowEvent(WindowEvent e)
    {
        if (e.getID() == WindowEvent.WINDOW_CLOSING)
        {
            if (btnApply.isEnabled())
            {
                switch (JOptionPane.showConfirmDialog(JOptionPane.getFrameForComponent(this),
                    StudioRes.getString(
                    "view.editorui.DefaultEditorPanel.changedWarning"),
                    StudioRes.warn,
                    JOptionPane.YES_NO_CANCEL_OPTION))
                {
                    case JOptionPane.YES_OPTION:
                        if (!_editor.canSave())
                        {
                            return;
                        }
                        _editor.stopEditing();
                        this.dispose();
                        _graph.cancelEditing();
                        FlowGraphHelper.updateCellSize(_graph, _cell);
                        break;
                    case JOptionPane.NO_OPTION:
                        _editor.cancelEditing();
                        this.dispose();
                        _graph.cancelEditing();
                        break;
                    default:
                        return;
                }
            }
            else
            {
                _editor.cancelEditing();
                this.dispose();
                _graph.cancelEditing();
            }
        }
    }

    JPanel cellPane = new JPanel();
    BorderLayout borderLayout1 = new BorderLayout();
    JPanel bottomPane = new JPanel();
    JPanel buttonPane = new JPanel();
    JButton btnOK = new JButton();
    JButton btnApply = new JButton();
    JButton btnClose = new JButton();
    GridLayout gridLayout1 = new GridLayout();
    Border border;
    private DefaultGraphCell _cell;

    /**
     * The manager all activity property' editor.
     */
    private FlowCellEditor _editor;
    private JGraph _graph;
    GridLayout gridLayout2 = new GridLayout();
    JPanel jPanel1 = new JPanel();
    BorderLayout borderLayout3 = new BorderLayout();
    GridBagLayout gridBagLayout1 = new GridBagLayout();
}
