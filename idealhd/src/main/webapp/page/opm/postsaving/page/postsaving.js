	var yzWalyPieData=[
		{value: 76,name: '超时工单数量'},
	    {value: 42,name: '异常工单数量'},
	    {value: 76,name: '已生成的工单'},
	    {value: 35,name: '已派发工单'},
	    {value: 40,name: '已正常回单的工单'}
	];

	
//var workOrderAnalysisList=[76,36,76,35,20];
var pageRefreshTask; //页面动态刷新
var pageTimeTask;
var refreshTime=5;
var chartArray = {};
$(function() {
	//pageRefreshTask=setInterval("initPage()" , refreshTime*1000);
	nowTime();
	pageTimeTask=setInterval( "nowTime()", 1000);
	initPage();
});
function initPage(){
	//个人工单分析图
	personalWorkOrderAnalysisChart();
	//工单分析饼图
	yzWalyPie();
	//作业调度折线图
	jobSchedulingPolyline();
	//日均警告处理类型
	averageDailyTreatmentWarningType();
	//日均告警事件趋势分析
	averageDailyAlarmEventTrendAnalysis();
	//变更操作中间曲线阴影图
	modifyOperatingShadowCurve();
	//工单分析进度条饼图
	yzWalyProgress();
	//工单趋势对比分析
	workOrderTrendCompare();
	showliquidfill();
}
var fy1=60;
var fy2=58;
var fy3=58;
var fy4=58;
function toLeft(){
	fy1=30;
	fy2=28;
	fy3=28;
	fy4=28;
	$("#second_screen").hide();
	$("#first_screen").show();
	showliquidsusfill();
}
function toRight(){
	fy1=60;
	fy2=58;
	fy3=58;
	fy4=58;
	$("#first_screen").hide();
	$("#second_screen").show();
	showliquidsusfill();
}
//日均警告处理类型
function averageDailyTreatmentWarningType(){
	var echartData = [
        {value: 36,name: '网络中断'},
        {value: 24,name: '延迟丢包'},
        {value: 8,name: '服务中断'},
        {value: 22,name: '系统故障'},
        {value: 10,name: '服务器故障'}
    ];
	var option = {
	   color: ["#fd5a5a", "#f39800", "#dce817", "#0cf7b5", "#097cfe"],
	   series: [
	       {
	           type: 'pie',
	           hoverAnimation:false,  
	           startAngle:230,
	           radius: ['55%', '85%'],
	           center :['50%', '50%'],
	           data: [20,20,20,40,58],
	           labelLine: {
	               normal: {
	                  show :false
	               }
	           },
	           label: {
	               normal: {
	                  show :false
	               }
	           }
	       }
	   ]
	};
	var histogram = echarts.init(document.getElementById('cr_pie'));
	histogram.setOption(option);
}

function showliquidsusfill(){
	var colorf1 = "#097cfe";
	var colorf2 = "#0cf7b5";
	var colorf3 = "#f39800";
	var colorf4 = "#fd5a5a";
	var ychart="y_oc_pie";
	var mchart="m_oc_pie";
	var wchart="w_oc_pie";
	var dchart="d_oc_pie";
	if (chartArray["sysCanvas_"+ychart]) {
		var canvas = document.createElement('canvas');
		canvas.width = 84;
		canvas.height = 84;
		var opts = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf1, // Colors
			colorStop : colorf1, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354',
			minAngle: 0,//最小角度
			startAngle:180 //起始角度
		};
		var canvasList = document
				.getElementById(ychart);
		canvasList.appendChild(chartArray["sysCanvas_"+ychart]);
		var gauge = chartArray[ychart];
		gauge.setOptions(opts);
		gauge.ctx.clearRect(0, 0,
				gauge.ctx.canvas.width,
				gauge.ctx.canvas.height);
		gauge.render();
		gauge.maxValue = 100;
		gauge.set(fy1);
	} else {
		var canvas1 = document.createElement('canvas');
		canvas1.width = 84;
		canvas1.height = 84;
		var opts1 = {
			angle :  0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf1, // Colors
			colorStop : colorf1, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354',
			minAngle: 0,//最小角度
            startAngle:180 //起始角度
		};
		var gauge1 = new Donut(canvas1)
				.setOptions(opts1); // create
		// sexy
		// gauge!
		gauge1.maxValue = 100; // set max gauge
		// value
		gauge1.setMinValue(0); // Prefer setter over
		// gauge.minValue = 0
		gauge1.animationSpeed = 32; // set animation
									// speed
		// (32 is default value)
		gauge1.set(fy1); // set actual value
		var canvasList1 = document
				.getElementById(ychart);
		canvasList1.appendChild(canvas1);
		chartArray["sysCanvas_"+ychart] = canvas1;
		chartArray[ychart] = gauge1;
	}
	if (chartArray["sysCanvas_"+mchart]) {
		var canvas = document.createElement('canvas');
		canvas.width = 84;
		canvas.height = 84;
		var opts = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf2, // Colors
			colorStop : colorf2, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354'
		};
		var canvasList = document
				.getElementById(mchart);
		canvasList.appendChild(chartArray["sysCanvas_"+mchart]);
		var gauge = chartArray[mchart];
		gauge.setOptions(opts);
		gauge.ctx.clearRect(0, 0,
				gauge.ctx.canvas.width,
				gauge.ctx.canvas.height);
		gauge.render();
		gauge.maxValue = 100;
		gauge.set(fy2);
	} else {
		var canvas1 = document.createElement('canvas');
		canvas1.width = 84;
		canvas1.height = 84;
		var opts1 = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf2, // Colors
			colorStop : colorf2, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354'
		};
		var gauge1 = new Donut(canvas1)
				.setOptions(opts1); // create
		// sexy
		// gauge!
		gauge1.maxValue = 100; // set max gauge
		// value
		gauge1.setMinValue(0); // Prefer setter over
		// gauge.minValue = 0
		gauge1.animationSpeed = 32; // set animation
									// speed
		// (32 is default value)
		gauge1.set(fy2); // set actual value
		var canvasList1 = document
				.getElementById(mchart);
		canvasList1.appendChild(canvas1);
		chartArray["sysCanvas_"+mchart] = canvas1;
		chartArray[mchart] = gauge1;
		}
	if (chartArray["sysCanvas_"+wchart]) {
		var canvas = document.createElement('canvas');
		canvas.width = 84;
		canvas.height = 84;
		var opts = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf3, // Colors
			colorStop : colorf3, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354'
		};
		var canvasList = document
				.getElementById(wchart);
		canvasList.appendChild(chartArray["sysCanvas_"+wchart]);
		var gauge = chartArray[wchart];
		gauge.setOptions(opts);
		gauge.ctx.clearRect(0, 0,
				gauge.ctx.canvas.width,
				gauge.ctx.canvas.height);
		gauge.render();
		gauge.maxValue = 100;
		gauge.set(fy3);
	} else {
		var canvas1 = document.createElement('canvas');
		canvas1.width = 84;
		canvas1.height = 84;
		var opts1 = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf3, // Colors
			colorStop : colorf3, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354'
		};
		var gauge1 = new Donut(canvas1)
				.setOptions(opts1); // create
		// sexy
		// gauge!
		gauge1.maxValue = 100; // set max gauge
		// value
		gauge1.setMinValue(0); // Prefer setter over
		// gauge.minValue = 0
		gauge1.animationSpeed = 32; // set animation
									// speed
		// (32 is default value)
		gauge1.set(fy3); // set actual value
		var canvasList1 = document
				.getElementById(wchart);
		canvasList1.appendChild(canvas1);
		chartArray["sysCanvas_"+wchart] = canvas1;
		chartArray[wchart] = gauge1;
		}
	if (chartArray["sysCanvas_"+dchart]) {
		var canvas = document.createElement('canvas');
		canvas.width = 84;
		canvas.height = 84;
		var opts = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf4, // Colors
			colorStop : colorf4, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354'
		};
		var canvasList = document
				.getElementById(dchart);
		canvasList.appendChild(chartArray["sysCanvas_"+dchart]);
		var gauge = chartArray[dchart];
		gauge.setOptions(opts);
		gauge.ctx.clearRect(0, 0,
				gauge.ctx.canvas.width,
				gauge.ctx.canvas.height);
		gauge.render();
		gauge.maxValue = 100;
		gauge.set(fy4);
	} else {
		var canvas1 = document.createElement('canvas');
		canvas1.width = 84;
		canvas1.height = 84;
		var opts1 = {
			angle : 0.5, // The span of the gauge arc
			lineWidth : 0.18, // The line thickness
			radiusScale : 1, // Relative radius
			pointer : {
				length : 0.64, // // Relative to gauge
				// radius
				strokeWidth : 0.205, // The thickness
				color : '#000000' // Fill color
			},
			limitMax : false, // If false, max value
			// increases automatically
			// if value > maxValue
			limitMin : false, // If true, the min
								// value of
			// the gauge will be fixed
			colorStart : colorf4, // Colors
			colorStop : colorf4, // just
										// experiment
										// with them
			strokeColor : '#064354', // to see which
										// ones
			// work best for you
			shadowColor : '#064354'
		};
		var gauge1 = new Donut(canvas1)
				.setOptions(opts1); // create
		// sexy
		// gauge!
		gauge1.maxValue = 100; // set max gauge
		// value
		gauge1.setMinValue(0); // Prefer setter over
		// gauge.minValue = 0
		gauge1.animationSpeed = 32; // set animation
									// speed
		// (32 is default value)
		gauge1.set(fy4); // set actual value
		var canvasList1 = document
				.getElementById(dchart);
		canvasList1.appendChild(canvas1);
		chartArray["sysCanvas_"+dchart] = canvas1;
		chartArray[dchart] = gauge1;
		}
}
function showliquidfill(){
	var center_div = document.getElementById("container_center");
	var value1 = 0.4;
	var data1 = [value1, value1, value1];
	var container = document.getElementById("container");
	var value = 0.5;
	var data = [value, value, value ];
	var myChart_center = echarts.init(center_div);
	var myChart = echarts.init(container);
    var option_div = {
         backgroundColor: new echarts.graphic.RadialGradient(0.4, 0.4, 0.8, [{
            offset: 1,
            color: 'rgba(4, 25, 42, 0)'
        }, {
            offset: 1,
            color: 'rgba(4, 25, 42, 0)'
        }]),
         title: {
             text: (value1 * 100).toFixed(0) + '{a|%}',
             textStyle: {
                 fontSize: 34,
                 fontFamily: 'Microsoft Yahei',
                 fontWeight: 'bold',
                 color: '#ffffff',
                 rich: {
                     a: {
                         fontSize: 34,
                     }
                 }
             },
             x: 'center',
             y: '35%'
         },
         graphic: [{
             type: 'group',
             left: 'center',
             top: '60%',
             children: [{
                 type: 'text',
                 z: 100,
                 left: '10',
                 top: 'middle',
                 style: {
                     fill: '#aab2fa',
                     font: '34px Microsoft YaHei bold'
                 }
             }]
         }],
         series: [{
             type: 'liquidFill',
             radius: '98%',
             center: ['50%', '50%'],
             data: data1,
             backgroundStyle: {
                 color: {
                     type: 'linear',
                     x: 1,
                     y: 0,
                     x2: 0.5,
                     y2: 1,
                     colorStops: {
                         offset: 1,
                         color: 'rgba(4, 25, 42, 1)'
                     },
                     globalCoord: false
                 },
             },
             outline: {
                 borderDistance: 0,
                 itemStyle: {
                     borderWidth: 1,
                     borderColor: {
                         type: 'linear',
                         x: 0,
                         y: 0,
                         x2: 0,
                         y2: 1,
                         colorStops: [{
                             offset: 0,
                             color: 'rgba(3, 229, 236, 1)'
                         }],
                         globalCoord: false
                     },
                     shadowBlur: 0,
                     shadowColor: '#000',
                 }
             },
             color: {
                 type: 'linear',
                 x: 0,
                 y: 0,
                 x2: 0,
                 y2: 1,
                 colorStops: [{
                     offset: 1,
                     color: 'rgba(3, 250, 252, 0)'
                 }, {
                     offset: 0.5,
                     color: 'rgba(3, 250, 252, .7)'
                 }, {
                     offset: 0,
                     color: 'rgba(3, 250, 252, 1)'
                 }],
                 globalCoord: false
             },
             label: {
                 normal: {
                     formatter: '',
                 }
             }
         }, ]
     };
    myChart_center.setOption(option_div, true);
    var option = {
         backgroundColor: new echarts.graphic.RadialGradient(0.3, 0.3, 0.8, [{
            offset: 1,
            color: 'rgba(4, 25, 42, 0)'
        }, {
            offset: 1,
            color: 'rgba(4, 25, 42, 0)'
        }]),
         title: {
             text: (value * 100).toFixed(0) + '{a|%}',
             textStyle: {
                 fontSize: 20,
                 fontFamily: 'Microsoft Yahei',
                 fontWeight: 'bold',
                 color: '#031c2e',
                 rich: {
                     a: {
                         fontSize: 13,
                         fontWeight: 'bold',
                     }
                 }
             },
             x: 'center',
             y: '65%'
         },
         graphic: [{
             type: 'group',
             left: 'center',
             top: '60%',
             children: [{
                 type: 'text',
                 z: 100,
                 left: '10',
                 top: 'middle',
                 style: {
                     fill: '#aab2fa',
                    //  text: '流量统计',
                     font: '14px Microsoft YaHei'
                 }
             }]
         }],
         series: [{
             type: 'liquidFill',
             radius: '98%',
             center: ['50%', '50%'],
             data: data,
             backgroundStyle: {
                 color: {
                     type: 'linear',
                     x: 1,
                     y: 0,
                     x2: 0.5,
                     y2: 1,
                     colorStops: {
                         offset: 1,
                         color: 'rgba(4, 25, 42, 1)'
                     },
                     globalCoord: false
                 },
             },
             outline: {
                 borderDistance: 0,
                 itemStyle: {
                     borderWidth: 1,
                     borderColor: {
                         type: 'linear',
                         x: 0,
                         y: 0,
                         x2: 0,
                         y2: 1,
                         colorStops: [{
                             offset: 0,
                             color: 'rgba(3, 229, 236, 1)'
                         }],
                         globalCoord: false
                     },
                     shadowBlur: 0,
                     shadowColor: '#000',
                 }
             },
             color: {
                 type: 'linear',
                 x: 0,
                 y: 0,
                 x2: 0,
                 y2: 1,
                 colorStops: [{
                     offset: 1,
                     color: 'rgba(3, 250, 252, 0)'
                 }, {
                     offset: 0.5,
                     color: 'rgba(3, 250, 252, .7)'
                 }, {
                     offset: 0,
                     color: 'rgba(3, 250, 252, 1)'
                 }],
                 globalCoord: false
             },
             label: {
                 normal: {
                     formatter: '',
                 }
             }
         }, ]
     };
        myChart.setOption(option, true);
//    window.onresize = function(){
//        myChart_center.resize();
//        myChart.resize();
//    }
}

//个人工单分析柱图
function personalWorkOrderAnalysisChart(){
	//个人工单分析图
	var personalWorkOrderData={
		name:['万更才', '毛乐进', '王建', '马林', '吴必材'],
		finished:[22, 34, 24, 36, 28],
		running:[40, 60, 36, 30,36],
		nofinish:[6, 26, 4, 10, 24],
		percent:[60, 98, 60, 80,60]
	};
	var histogram = echarts.init(document.getElementById('personalWorkOrderAnalysis'));
	var options = 
	    {
//	        tooltip: { // 提示框组件
//	            trigger: 'axis'
//	        },
	        grid: {
	            left: '0%',
	            right: '0%',
	            bottom: '10%',
	            top: '10%',
	            containLabel: true,
	            show: false // 网格边框是否显示，上和右边框 
	        },
	        calculable: true,
	        xAxis: {
	            type: 'category',
	            boundaryGap: true, // 坐标轴两边留白
	            splitLine: { // 网格线 x轴对应的是否显示
	                show: false
	            },
	             axisTick: {
	                    show: false
	            },
	            axisLabel: {
	                show: true,
	                textStyle: {
	                    color: "#03fafc"
	                }
	            },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#03fafc'
                    }
                },
	            data: personalWorkOrderData.name
	        },
	        yAxis: [ // 双y坐标轴
	            {
	                type: 'value',
	                splitLine: { // 网格线 y轴对应的是否显示
	                    show: true,
		                lineStyle:{
		                    color: ['#042D3E'],
		                    width: 1,
		                    type: 'solid'
		               }
	                },
	                axisTick: {
	                    show: false
	                },
	                axisLabel: {
	                    formatter: '{value}',
	                    textStyle: {
		                    color: "#03fafc"
		                }
	                },
	                axisLine: {
	                    show: false
	                }
	            },
	            {
	                min: 0,
	                max: 100, // growing rate upper limit
	                type: 'value',
	                inverse: false,
	                  splitLine: {
	                show: false
	                },
	                axisTick: {
	                    show: false
	                },
	                axisLine: {
	                    show: false
	                },
	                axisLabel: {
	                    show: true,
	                    formatter: "{value}%", //右侧Y轴文字显示
	                    textStyle: {
		                    color: "#0978f5"
		                }
	                }
	            }
	        ],
	        series: [
	        	{
	                name: '已完成',
	                type: 'bar',
	                barWidth :12,
	                  itemStyle: {
	                    normal: {
	                        color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [{
	                              offset: 0,
	                              color: "#0cf7b5" //03F6F9
		                          },
		                          {
		                              offset: 1,
		                              color: "#051527" //051527
		                          }
	                          ]),
	                        barBorderRadius: 0,
	                        opacity: .9,
	                        label: {
	                            show: true,
	                            position: "top",
	                            color: "#0cf7b5" //03F6F9
	                        }
	                    }
	                },
	                data: personalWorkOrderData.finished
	            },{
	                name: '进行中',
	                type: 'bar',
	                barWidth :12,
	                itemStyle: {
	                    normal: {
	                        barBorderRadius: 0,
	                        color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [{
	                              offset: 0,
	                              color: "#0df0fc" //03F6F9
		                          },
		                          {
		                              offset: 1,
		                              color: "#051527" //051527
		                          }
	                          ]),
	                        opacity: .9,
	                        label: {
	                            show: true,
	                            position: "top",
	                            color: "#0df0fc" //051527
	                        }
	                    }
	                },
	                data: personalWorkOrderData.running
	            },
	            {
	                name: '未完成',
	                type: 'bar',
	                barWidth :12,
	                      itemStyle: {
	                    normal: {
	                        color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [{
	                              offset: 0,
	                              color: "#dce817" //03F6F9
		                          },
		                          {
		                              offset: 1,
		                              color: "#051527" //051527
		                          }
	                          ]),
	                        opacity: 1,
	                        barBorderRadius: 0,
	                        label: {
	                            show: true,
	                            position: "top",
	                            color: "#dce817" //051527
	                        }
	                    }
	                },
	                data: personalWorkOrderData.nofinish
	            },
	            {
	                name: '成功率',
	                type: 'line',
	                symbolSize: 2,
	                symbol: 'circle',
	                yAxisIndex: 0, // yAxisIndex 1 表示第二个y轴，默认为0
	                lineStyle: {
	                    normal: {
	                        width: 2,
	                        color: '#0978f5'
	                    }
	                },
	                itemStyle: {
	                    normal: {
	                        color: '#0978f5',
	                        borderWidth: 3,
	                        borderColor: "#0978f5"
	                    }
	                },
	                data: personalWorkOrderData.percent
	            }
	        ]
	    };
	histogram.setOption(options);
}
//工单分析饼图
function yzWalyPie(){
	$("#workOrderTimeout").html(yzWalyPieData[0].value);
	$("#workOrderAbnormal").html(yzWalyPieData[1].value);
	$("#workOrderGenerated").html(yzWalyPieData[2].value);
	$("#workOrderDistributed").html(yzWalyPieData[3].value);
	$("#workOrderReceipt").html(yzWalyPieData[4].value);
	var option = {
			series : [ {
				type : 'pie',
				hoverAnimation:false,   
				radius : [ '30%', '80%' ],
				center : [ '50%', '50%' ],
				roseType : 'radius',
				color : [ '#dce817', '#f39800', 'green', '#03fafc','#1967f7'],
				labelLine : {
					normal : {
						 show:false
					}
				},
				label : {
					normal : {
					 show:false
					}
				},
				data : yzWalyPieData
			} ]
		};
	var stepInfoPie = echarts.init(document.getElementById('yz_waly_pie'));
		stepInfoPie.setOption(option);
}
//作业调度折线图
function jobSchedulingPolyline(){
	var options = 
    {
//        tooltip: { // 提示框组件
//            trigger: 'axis',
//            axisPointer: { // 坐标轴指示器，坐标轴触发有效
//                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
//            }
//        },
        grid: {
            left: '0%',
            right: '0%',
            bottom: '5%',
            top: '10%',
            containLabel: true,
            show: false // 网格边框是否显示，上和右边框 
        },
        xAxis: {
            type: 'category',
            boundaryGap: true, // 坐标轴两边留白
            splitLine: { // 网格线 x轴对应的是否显示
                show: false
            },
             axisTick: {
                    show: false,
            },
            axisLabel: {
                show: true,
                interval:0,
                fontSize:10,
                textStyle: {
                    color: "#03fafc",
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#03fafc'
                }
            },
            data: ['核心日终...', '核心日终...', '核心日终...', '核心日终...', '核心日终...', '核心日终...', 
            		   '核心日终...', '核心日终...', '核心日终...', '核心日终...', '核心日终...', '核心日终...', '核心日终...']
        },

        yAxis: [ // 双y坐标轴
            {
                type: 'value',
                splitLine: { // 网格线 y轴对应的是否显示
                    show: true,
	                lineStyle:{
	                    color: ['#042d3f'],
	                    width: 1,
	                    type: 'solid'
	               }
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    formatter: '{value}',
                    textStyle: {
	                    color: "#03fafc"
	                }
                },
                axisLine: {
                    show: false
                }
            }
        ],
        series: [
            {
                name: '本次耗时',
                type: 'line',
                symbolSize: 5,
                symbol: 'circle',
                lineStyle: {
                    normal: {
                        width: 2,
                        color: '#03fafc'
                    }
                },
                itemStyle: {
                    normal: {
                        color: '#03fafc',
                        borderWidth: 1,
                        borderColor: "#03fafc"
                    }
                },
                data: [4, 7, 6, 2, 4,2,6,4,8,2,4,2,6]
            },
            {
               
            	  name: '历史耗时',
            	  symbolSize: 5,
	              symbol: 'circle',
                  type: 'line',
                  lineStyle: {
	                    normal: {
	                        width: 2,
	                        color: '#0978f5'
	                    }
	                },
	                itemStyle: {
	                    normal: {
	                        color: '#0978f5',
	                        borderWidth: 1,
	                        borderColor: "#0978f5"
	                    }
	                },
                 
                  data: [3, 2, 4, 8, 2,4,4,2,4,6,2,4,8]
            }
        ]
    };
	var polyline = echarts.init(document.getElementById('jobSchedulingPolyline'));
	polyline.setOption(options);
}
//日均告警事件趋势分析
function averageDailyAlarmEventTrendAnalysis(){
	var histogram = echarts.init(document.getElementById('averageDailyAlarmEventTrendAnalysis'));
	var options = 
	    {
	        grid: {
	            left: '0%',
	            right: '3%',
	            bottom: '15%',
	            top: '10%',
	            containLabel: true,
	            show: false // 网格边框是否显示，上和右边框 
	        },
	        calculable: true,
	        xAxis: {
	        	name:'日',
	            type: 'category',
	            nameGap:3, 
	            nameTextStyle:{
            		color: '#03fafc',
            		padding: [25, 0, 0, 0]
            	},
	            boundaryGap: true, // 坐标轴两边留白
	            splitLine: { // 网格线 x轴对应的是否显示
	                show: false
	            },
	             axisTick: {
	                    show: false
	            },
	            axisLabel: {
	                show: true,
	                interval:0,
	                fontSize:10,
	                textStyle: {
	                    color: "#03fafc"
	                }
	            },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#03fafc'
                    }
                },
	            data: [ '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15',
	            			'16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29','30'
	            		]
	        },

	        yAxis: [ // 双y坐标轴
	            {
	            	name:'个数',
	            	nameLocation:'start',
	            	nameGap:3, 
	            	nameTextStyle:{
	            		color: '#03fafc',
	            		padding: [3, 22, 0, 0]
	            	},
	                type: 'value',
	                splitLine: { // 网格线 y轴对应的是否显示
	                    show: true,
		                lineStyle:{
		                    color: ['#042d3f'],
		                    width: 1,
		                    type: 'solid'
		               }
	                },
	                axisTick: {
	                    show: false
	                },
	                axisLabel: {
	                    formatter: '{value}',
	                    textStyle: {
		                    color: "#03fafc"
		                }
	                },
	                axisLine: {
	                    show: true
	                }
	            }
	        ],
	        series: [{
	                name: '进行中',
	                type: 'bar',
	                barWidth :6,
	                itemStyle: {
	                    normal: {
	                    	  color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [{
	                              offset: 0,
	                              color: "#03F6F9"
		                          },
		                          {
		                              offset: 1,
		                              color: "#051527"
		                          }
	                          ]),
	                          opacity: .9,
		                        label: {
		                            show: false
		                        }
	                    }
	                },
	                data: [45, 44, 40, 36, 32,30,28,30,32, 36,40, 44, 50,55,50,
	                		   44, 40, 36, 32, 30,28,30,28,32, 36,36, 40,44,50,50
	                	]
	            }
	        ]
	    };
	histogram.setOption(options);
}

function modifyOperatingShadowCurve(){
	var histogram = echarts.init(document.getElementById('jobCenterShadowCurve'));
	var data={
			date:[ "14天前",  "13天前", "12天前", "11天前", "10天前", "9天前", "8天前", "7天前", "6天前", "5天前", "4天前", "3天前", "2天前", "1天前", "今天"],
//			data1:[10,35, 26,20, 15,  22, 39, 36, 25, 20, 18,12,30,35,30],
//			data2:[35,15, 20, 30, 36, 28, 15, 17, 19, 25,36,33,28,20,12]
	//data1:[10,35, 26,20, 15,  22, 39, 36, 25, 20, 18,12,30,35,30],
	  data1: [15.0, 60, 20.3, 70, 20.3, 10.2, 20.3, 33.4, 40, 20.6,20,2.0, 20.3, 60, 30.3],
	  data2: [60,2.0, 4.3, 11, 20.3, 75, 54.3, 30.4, 20, 10.6,30,60.0, 30.3, 20, 30.3]
	//data2:[40, 23, 23, 35, 45, 50, 50,45, 30, 20,18, 30, 50, 55, 45, 30]
	}
	var option = {
			grid: {
		        top: "15%",
		        left: "5%",
		        bottom: "10%",
		        right: "5%",
		        containLabel: true
		    },
		    xAxis: [{
		        type: "category",
		        data: data.date,
		        axisTick: {
		            show:false,
		        },
		        splitLine: {
	                show: false
	            },
		        axisLine: {
		            lineStyle: {
		                color: "#0df0fc"
		            }
		        },
		        axisLabel: {
		        	 rotate : -50,
		            textStyle: {
		                color: "#0df0fc"
		            }
		        }
		    }],
		    yAxis: [
		         {
		                min: 0,
		                type: 'value',
		                inverse: false,
		                splitLine: {
		                	show: false
		                },
		                axisTick: {
		                    show: false
		                },
		                axisLine: {
		                    show: false
		                },
		                axisLabel: {
		                    show: true,
		                    formatter: "{value} %", //左侧Y轴文字显示
		                    textStyle: {
			                    color: "#0df0fc"
			                }
		                }
		            },
		         {
		            	min:0,
		            	max:60,
		                type: 'value',
		                axisTick: {
		                    show: false
		                },
		                splitLine: {
			                show: false
			                },
		                axisLabel: {
		                    formatter: '{value}',
		                    textStyle: {
			                    color: "#e70cff"
			                }
		                },
		                axisLine: {
		                    show: false
		                }
		            }
		         ],
		    series: 
		        [{
		            name: '成功率',
		            type: 'line',
		           smooth: 0.7,
		           symbolSize: 0,
		            lineStyle: {
		                normal: {
		                    width: 1,
		                    color:"#0df0fc"
		                }
		            },
		            areaStyle: {
		                normal: {
		                    color: {
		                        type: 'linear',
		                        x:0,
		                        y:0,
		                        x2: 0,
		                        y2: 1,
		                        colorStops: [{
		                            offset: 0.4, color: '#0df0fc' // 0% 处的颜色
		                        }, {
		                            offset: 1, color: 'rgba(8,20,59,0)' // 100% 处的颜色
		                        }],
		                    }
		                }
		            },
		            data: data.data1
		           },
		           {
			            name: '数量',
			            type: 'line',
			           smooth: true,
			           symbolSize: 0,
			            lineStyle: {
			                normal: {
			                    width: 1,
			                    color:"#e70cff"
			                }
			            },
			            areaStyle: {
			                normal: {
			                    color: {
			                        type: 'linear',
			                        x:0,
			                        y:0,
			                        x2: 0,
			                        y2: 1,
			                        colorStops: [{
			                            offset: 0.4, color: '#e70cff' // 0% 处的颜色
			                        }, {
			                            offset: 1, color: 'rgba(8,20,59,0)' // 100% 处的颜色
			                        }],
			                    }
			                }
			            },
			            yAxisIndex: 0,
			            data: data.data2,
			           }
		           ]
		};
	histogram.setOption(option);
}

function yzWalyProgress(){
	var pie;
	var option;
	for(var i=1;i<=2;i++){
		var value;
		if(i==1){
			value=yzWalyPieData[3].value/yzWalyPieData[2].value*100;
		}
		if(i==2){
			value=yzWalyPieData[4].value/yzWalyPieData[2].value*100;
		}
		value=percentage(value);
		$("#yz_value_num_"+i).html("75%");
		pie = echarts.init(document.getElementById("yz_value_"+i));
		option= {
				series : [ {
					type : 'pie',
					hoverAnimation : false,
					radius : [ '70%', '100%' ],
					center : [ '50%', '50%' ],
					avoidLabelOverlap : false,
					color : ['#00fdfc', "transparent"],
					label : {
						normal : {
							show : true,
							position : 'center'
						},
						emphasis : {
							show : false,
							textStyle : {
								fontSize : '30',
								fontWeight : 'bold'
							}
						}
					},
					labelLine : {
						normal : {
							show : false
						}
					},
					data:[
					{
						value: 75,//value
						label: {
							normal: {
								show: false
							}
						}
					},{
						value: 100-75,
						label: {
							normal: {
								show: true
							}
						}
					}
					]
				} ]
		};				
		pie.setOption(option);
	}
}


function workOrderTrendCompare(){
	
	var data={
			date:[ 
	        	"26天前", "25天前", "24天前", "23天前", "22天前", "21天前", "20天前",
	        	"19天前", "18天前", "17天前", "16天前", "15天前", "14天前", "13天前", "12天前", "11天前", "10天前",
	        	"9天前", "8天前", "7天前", "6天前", "5天前", "4天前", "3天前", "2天前", "1天前", "今天"
	        	],
			data1:[5,  9,6,10, 4,    20, 10,     8,   6, 8,     5,11,7,11,3,       19,7,3,5,7,        7,4,15,7,9,             5, 7],
		    data2:[12,11,5,15,15,   8,   30,   10, 6, 20,   10,11,7,9,13,     5,12,13,10,15,  12,11,7,30,13,       6, 15],
			data3:[6,  10,11, 24,12,  21,  8,   7,  13, 20, 7,11,11,21,10,   22,10,9,10,5,    15,9,18, 5, 7,         18,15],
		    data4:[26,28,30,32,34,36,38,     36, 34,32,  30,27,34,41,47, 50,33,32, 23,22,25,31,37,43,50,45,42]
	};
	
	var histogram=echarts.init(document.getElementById("workOrderTrendCompare"));
	var option = {
			grid: {
		        top: "20%",
		        left: "5%",
		        bottom: "10%",
		        right: "0%",
		        containLabel: true
		    },
		    tooltip : {
				show : true,
				trigger : 'axis',
				showDelay : 0,// 显示延时，添加显示延时可以避免频繁切换
				hideDelay : 50,// 隐藏延时
				transitionDuration : 0,// 动画变换时长
				backgroundColor : 'transparent',// 背景颜色（此时为默认色）
				borderRadius : 8,// 边框圆角
				padding : 1,
				formatter : function(param) {
					var showdata = " <div class='yz_tip'><p class='yz_tip_row01 yz_tip_common' >"
										            	+"<span style='font-size:12px;line-height:12px;margin-top:3px;'>"+param[2].value+"</span>"
										            	+"<span style='font-size:12px;line-height:12px;margin-top:3px;'>"+param[2].seriesName+"</span>"
									            	+"</p><p class='yz_tip_row02 yz_tip_common' >"
										            	+"<span style='font-size:12px;line-height:12px;margin-top:3px;'>"+param[1].value+"</span>"
										            	+"<span style='font-size:12px;line-height:12px;margin-top:3px;'>"+param[1].seriesName+"</span>"
										            	+"</p><p class='yz_tip_row03 yz_tip_common'  >"
										            	+"<span style='font-size:12px;line-height:12px;margin-top:3px;'>"+param[0].value+"</span>"
										            	+"<span style='font-size:12px;line-height:12px;margin-top:3px;'>"+param[0].seriesName+"</span>"
									                +"</p></div>";
					return showdata;
				}
			},

//		    tooltip: {
//		        trigger: "axis",
//		        axisPointer: {
//		            "type": "shadow",
//		            textStyle: {
//		                color: "#fff"
//		            }
//
//		        },
//		    },
		    calculable: true,
		    xAxis: [{
		        type: "category",
		        data: data.date,
		        axisTick: {
		            show:false,
		        },
		        splitLine: {
	                show: false
	            },
		        axisLine: {
		            lineStyle: {
		                color: "#0df0fc"
		            }
		        },
		        axisLabel: {
		        	 rotate : -50,
		            textStyle: {
		                color: "#0df0fc"
		            }
		        }
		    }],
		    yAxis: [
		         {
		        	 	name:"工单数量",
		        	 	nameTextStyle: {
		                    color: "#0df0fc"
		                },
		                min: 0,
		                type: 'value',
		                inverse: false,
		                splitLine: { // 网格线 y轴对应的是否显示
		                    show: true,
			                lineStyle:{
			                    color: ['#042D3E'],
			                    width: 1,
			                    type: 'solid'
			               }
		                },
		                axisTick: {
		                    show: false
		                },
		                axisLine: {
		                    show: false
		                },
		                axisLabel: {
		                    show: true,
		                    formatter: "{value}", //左侧Y轴文字显示
		                    textStyle: {
			                    color: "#0df0fc"
			                }
		                }
		            },
		         {
		                type: 'value',
		                axisTick: {
		                    show: false
		                },
		                splitLine: {
			                show: false
			                },
		                axisLabel: {
		                    formatter: '{value}',
		                    textStyle: {
			                    color: "#dce817"
			                }
		                },
		                axisLine: {
		                    show: false
		                }
		            }
		         ],
		    series: [{
		            name: "超时工单数量",
		            type: "bar",
		            stack: "总量",
		            barWidth: 12,
		            itemStyle: {
		                normal: {
		                    color: "#dce817",
		                   label: {
		                        show: false,
		                    }
		                }
		            },
		            data: data.data1
		        },

		        {
		            name: "已处理工单数量",
		            type: "bar",
		            barWidth: 12,
		            stack: "总量",
		            itemStyle: {
		                normal: {
		                    color: "#0df0fc",
		                    barBorderRadius: 0,
		                    label: {
		                        show: false
		                    }
		                }
		            },
		            data:  data.data2
		        },{
		            name: "新增工单数量",
		            type: "bar",
		            barWidth: 12,
		            stack: "总量",
		            itemStyle: {
		                normal: {
		                    color: "#0978f5",
		                    barBorderRadius: 0,
		                    label: {
		                        show: false,
		                    }
		                }
		            },
		            data:data.data3
		        }, {
		            name: "超时工单趋势",
		            type: "line",
		            symbolSize:5,
		            symbol:'circle',
		            itemStyle: {
		                normal: {
		                    color: "#dce817",
		                    barBorderRadius: 0,
		                    label: {
		                        show: false,
		                    }
		                }
		            },
		            yAxisIndex: 0, 
		            data: data.data4
		        },
		    ]
		};
		histogram.setOption(option);
}

function percentage(value){
	value=value.toFixed(1);
	return value;
}
function nowTime(){
  
	var time=new Date();
    //获取时分秒
    var h=time.getHours();
    var m=time.getMinutes();
    var s=time.getSeconds();
    
    //检查是否小于10
    h=check(h);
    m=check(m);
    s=check(s);
    $("#time_var").html(h+":"+m+":"+s);
    
    //获取年月日
    var year=time.getFullYear();
    var month=time.getMonth();
    var day=time.getDate();
    //获取星期几

    var a = new Array("日", "一", "二", "三", "四", "五", "六"); 
    var week = time.getDay();  
    var str = "星期"+ a[week];  
    $("#date_var").html(year+"/"+(month+1)+"/"+day+"<br/>"+str);
}
//时间数字小于10，则在之前加个“0”补位。
function check(i){
    if(i<10){
        i="0"+i;
    }
    return i;
}