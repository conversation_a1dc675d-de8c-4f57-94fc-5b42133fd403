package com.ideal.sql;

import java.util.LinkedList;
/**
 * 队列类
 * <AUTHOR>
 *
 */
public class Queue
{

    private LinkedList ll = new LinkedList();

    public void put ( Object o )
    {
        ll.addLast(o);
    }

    // 使用removeFirst()方法，返回队列中第一个数据，然后将它从队列中删除
    public Object get ()
    {
        return ll.removeFirst();
    }
    public Object getLast ()
    {
        return ll.removeLast();
    }

    public boolean empty ()
    {
        return ll.isEmpty();
    }

    public static void main ( String[] args )
    {

    }
}
