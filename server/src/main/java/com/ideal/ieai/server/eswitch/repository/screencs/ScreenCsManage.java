package com.ideal.ieai.server.eswitch.repository.screencs;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

public class ScreenCsManage
{
    private static final Logger _log = Logger.getLogger(ScreenCsManage.class);

    private static final String   ISON_VERSION_ID = "ison_version_id";
    private static final String   SYSSTATENUM = "sysstatenum";
    private static final String   SYSSTATE    = "sysstate";
    private static final String   MYSYSDATE   = "mysysdate";
    private static final String   IENDTIME    = "iendtime";
    private static final String   SQLONE      = " b where a.IID=b.IRUNINSID and a.ISYSTYPE=";

    private static final String   STEP_RUNNIG     = "0";
    private static final String   STEP_NOSTART    = "1";
    private static final String   STEP_FINISH     = "2";
    private static final String   STEP_RUNNIGFAIL = "4";
    private static final String   CONNERNAME      = "connername";
    private static final String   ISHELLPATH      = "ishellpath";
    private static final String   CONNER          = "conner";
    private static final String   IACTDES         = "iactdes";
    private static final String   IISFAIL         = "iisfail";
    private static final String   IACTNAME        = "iactname";
    private static final String   ISTARTTIME      = "istarttime";
    private static final String   ISTATE          = "istate";
    private static final String   IPARAMETER      = "iparameter";
    private static final String   IREMINFO        = "ireminfo";
    private static final String   ISHELLSCRIPT    = "ishellscript";
    private static final String   ISYSNAME        = "isysname";
    private static final String   FORMAT          = "HH:mm:ss";
    private static final String   ICENTER         = "icenter";
    private static final String   ICONNER         = "iconner";
    private static final String   ICONNERNAME     = "iconnername";
    private static final String   SCENETYPE       = "scenetype";
    private static final String   RUNNINGCOUNT    = "runningCount";
    private static final String   SWITCHTO        = "iswitchto";
    private static final String   SERNER          = "iserner";
    private static final String   RUNTIME         = "iruntime";
    private static final String   RUNINSNAME      = "iruninsname";
    private static final String   RETURNFLAG      = "irerunflag";
    private static final String   PRENER          = "iprener";
    private static final String   FINISHEDCOUNT   = "finishedCount";
    private static final String   ACTTYPE         = "iacttype";
    private static final String   EXECUSER        = "iexecuser";
    private static final String   FLOWID          = "iflowid";
    private static final String   FAILCOUNT       = "failCount";
    private static final String   IPCOUNT         = "ipCount";
    private static final String   NOBEGINCOUNT    = "noBeginCount";
    private static final String   STEPCOUNT       = "stepCount";


    private static ScreenCsManage intance = new ScreenCsManage();

    public static ScreenCsManage getInstance ()
    {
        if (intance == null)
        {
            intance = new ScreenCsManage();
        }
        return intance;
    }

    public int getRunNum ( int systype, Connection con ) throws RepositoryException
    {
        int runNum = 0;
        StringBuilder sql = new StringBuilder(
                " SELECT COUNT(*) AS SYSTEMNUM  FROM IEAI_RUN_INSTANCE where ISYSTYPE=" + systype);
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(sql.toString());
            rs = ps.executeQuery();
            while (rs.next())
            {
                runNum = rs.getInt("SYSTEMNUM");
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getRunNum Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getRunNum", _log);
        }
        return runNum;
    }


    public String getNewSwitch ( String runInstanceTable, Connection con, int systype ) throws RepositoryException
    {

        String runinsname = "";
        String sql = "select IRUNINSNAME from " + runInstanceTable + " where ISYSTYPE=" + systype
                + " order by ISTARTTIME desc";
        PreparedStatement ps = null;
        ResultSet rs = null;

        try
        {
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                runinsname = rs.getString("IRUNINSNAME");
                break;
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getNewSwitch Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getNewSwitch", _log);
        }

        return runinsname;
    }

    public List getCenterMessage ( Connection con ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select a.centerid,a.centername,a.centerident from ieai_dc a where centerid >0 order by a.centerid";
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map map = new HashMap();
                map.put("centerid", rs.getLong("centerid"));
                map.put("centername", rs.getString("centername"));
                map.put("centerident", rs.getString("centerident"));
                list.add(map);
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getCenterMessage Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCenterMessage", _log);
        }
        return list;
    }

    public List getSystemList ( String tableName1, String tableName2, String runinsname, Connection con, int systype )
            throws RepositoryException
    {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List list = new ArrayList();
        String sql = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select * from (select a.IWORKITEMID,a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.istartuser,a.iswitchto,a.IRUNINSNAME,a.ISTARTTIME as s,a.IENDTIME as e,count(if(b.ISTATE=2,1,if(b.ISTATE=3,1,null))) as wcbs,count(b.IID) as zbs,a.istate as xtzt,count(if(b.istate=1,null,1)) as bzzt,count(if(b.istate=0,if(b.IISFAIL=0, null, if(b.IISFAIL=NULL,null,if(b.IISFAIL=-1,null,1))),null)) as iisfail,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8) as mysysdate,max(b.imodeltype) as imodeltype from "
                    + tableName1 + " a, " + tableName2 + SQLONE + systype + " and a.IRUNINSNAME='" + runinsname
                    + "' group by a.IWORKITEMID,a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.istartuser,a.iswitchto,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.ISTATE order by a.ISTARTTIME desc) table_a where 1=1";
        } else
        {
            sql = "select * from (select a.IWORKITEMID,a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.istartuser,a.iswitchto,a.IRUNINSNAME,a.ISTARTTIME as s,a.IENDTIME as e,count(decode(b.ISTATE,2,1,3,1,null)) as wcbs,count(b.IID) as zbs,a.istate as xtzt,count(decode(b.istate,1,null,1)) as bzzt,count(decode(b.istate,0,decode(b.IISFAIL, 0, null, null, null, -1, null, 1),null)) as iisfail,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate() + ",8) as mysysdate,max(b.imodeltype) as imodeltype from "
                    + tableName1 + " a, " + tableName2 + SQLONE + systype + " and a.IRUNINSNAME='" + runinsname
                    + "' group by a.IWORKITEMID,a.IID,a.ISYSID,a.ISYSCLASSID,a.ISYSNAME,a.istartuser,a.iswitchto,a.IRUNINSNAME,a.IINSDES,a.ISTARTTIME,a.IENDTIME,a.ISTATE order by a.ISTARTTIME desc) where 1=1";
        }

        PreparedStatement ps = null;
        ResultSet rs = null;

        try
        {
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map map = new HashMap();
                map.put("iworkitemid", rs.getLong("iworkitemid"));
                String isysname = rs.getString("ISYSNAME");
                map.put("iid", rs.getLong("iid"));
                map.put("isysid", rs.getLong("ISYSID"));
                map.put("isysname", isysname);
                map.put("istartuser", rs.getString("istartuser"));
                String nowtime = rs.getLong(MYSYSDATE) == 0 ? "" : sdf.format(new Date(rs.getLong(MYSYSDATE)));
                String starttime = rs.getLong("S") == 0 ? "" : sdf.format(new Date(rs.getLong("S")));
                String endtime = rs.getLong("E") == 0 ? "" : sdf.format(new Date(rs.getLong("E")));
                getSystemListPartOne(starttime, endtime, nowtime, map);
                map.put("imodeltype", rs.getString("imodeltype"));
                String iswitchto = rs.getString("ISWITCHTO");
                String[] iswitchtos = (iswitchto!=null?iswitchto:"").split("#");
                getSystemListPartTwo(iswitchtos, iswitchto, map);
                map.put("starttime", starttime);
                map.put("endtime", endtime);
                map.put("nowtime", nowtime);
                int sysstate = rs.getInt("XTZT");
                getSystemListPartThree(sysstate, rs, map);
                String sysstepnum = rs.getString("zbs");
                String sysfinishednum = rs.getString("wcbs");
                map.put("sysfinishednum", sysfinishednum);
                map.put("sysstepnum", sysstepnum);
                String iisfail = rs.getString("IISFAIL");
                int stepstate = rs.getInt("bzzt");
                getSystemListPartFour(sysstate, iisfail, stepstate, map);
                getExpectTime(rs.getLong("iworkitemid"), map, con);
                list.add(map);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSystemList Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getSystemList", _log);
        }
        return list;
    }

    public void getExpectTime ( long workItemId, Map reMap, Connection conn )
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String getSql = "select icolheader,icolvalue from ieai_doublecheck_colvalue_his where (icolheader='iwindowStart' or icolheader='iwindowEnd') and iworkItemid=?";
        try
        {
            ps = conn.prepareStatement(getSql);
            ps.setLong(1, workItemId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                String name = rs.getString("icolheader");
                if ("iwindowStart".equals(name))
                {
                    reMap.put("expectStart", rs.getLong("icolvalue"));
                } else
                {
                    reMap.put("expectEnd", rs.getLong("icolvalue"));
                }
            }
        } catch (Exception e)
        {
            _log.error("获取预计时间发生异常");
            reMap.put("expectStart", 0);
            reMap.put("expectEnd", 0);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getExpectTime", _log);
        }
    }
    public void getSystemListPartFour ( int sysstate, String iisfail, int stepstate, Map map )
    {
        if (sysstate == 0)
        {
            map.put(SYSSTATE, "运行中");
            map.put(SYSSTATENUM, 1);
        }
        if (sysstate == 1 || sysstate == 2)
        {
            map.put(SYSSTATE, "已完成");
            map.put(SYSSTATENUM, 3);
        }
        if (stepstate == 0)
        {
            map.put(SYSSTATE, "未开始");
            map.put(SYSSTATENUM, 2);
        }
        if (!"0".equals(iisfail) && sysstate == 0)
        {
            map.put(SYSSTATE, "异常");
            map.put(SYSSTATENUM, 0);
        }
    }

    public void getSystemListPartThree ( int sysstate, ResultSet rs, Map map ) throws SQLException
    {
        long thetime = 0;
        if (sysstate == 0)
        {
            thetime = rs.getLong(MYSYSDATE) - rs.getLong("s");
        } else
        {
            thetime = rs.getLong("e") - rs.getLong("s");
        }

        map.put("thetime", thetime);
    }

    public void getSystemListPartTwo ( String[] iswitchtos, String iswitchto, Map map )
    {
        if (iswitchtos != null && iswitchtos.length == 3)
        {
            map.put("iswitchto", iswitchtos[2]);
            map.put("switchinfo", iswitchtos[1]);
        } else
        {
            map.put("iswitchto", iswitchto);
            map.put("switchinfo", iswitchto);
        }
    }

    public void getSystemListPartOne ( String starttime, String endtime, String nowtime, Map map )
    {
        if ("".equals(starttime))
        {
            map.put("estimatedstarttime", nowtime);
        } else
        {
            map.put("estimatedstarttime", starttime);
        }

        if ("".equals(endtime))
        {
            map.put("estimatedendtime", nowtime);
        } else
        {
            map.put("estimatedendtime", endtime);
        }
    }

    public String getActNames ( Connection conn, String instanceIid ) throws RepositoryException
    {
        StringBuilder result = new StringBuilder();
        String[] tabNames = { "IEAI_RUNINFO_INSTANCE", "IEAI_RUNINFO_INSTANCE_HIS" };
        try
        {
            for (int i = 0; i < tabNames.length; i++)
            {
                getActNamesPartOne(result, tabNames[i], instanceIid, conn);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getActNames Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
        return result.toString();
    }

    public void getActNamesPartOne ( StringBuilder result, String tabName, String instanceIid, Connection conn )
            throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select IACTNAME from " + tabName + " WHERE IRUNINSID = ? and istate=0";
            ps = conn.prepareStatement(sql);
            ps.setString(1, instanceIid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                if ("".equals(result.toString()))
                {
                    result.append(rs.getString("IACTNAME"));
                } else
                {
                    result.append("," + rs.getString("IACTNAME"));
                }
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getActNamesPartOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getActNamesPartOne", _log);
        }
    }

    public String getSysPerson ( Connection con, String sysName ) throws RepositoryException
    {
        StringBuilder str = new StringBuilder();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select s.ipersonname,s.idepartment,s.itelephone,s.iemail from IEAI_PROJECT p,IEAI_SYS_PERSON s where p.iid=s.iprojectid and p.iname=?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                String ipersonname = rs.getString("ipersonname") == null ? "" : rs.getString("ipersonname");
                String idepartment = rs.getString("idepartment") == null ? "" : rs.getString("idepartment");
                String itelephone = rs.getString("itelephone") == null ? "" : rs.getString("itelephone");
                String iemail = rs.getString("iemail") == null ? "" : rs.getString("iemail");
                str.append(
                    "负责人:" + ipersonname + ",部门:" + idepartment + ",电话:" + itelephone + ",邮箱:" + iemail + "\r\n");
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSysPerson Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getSysPerson", _log);
        }
        return str.toString();
    }

    public List getInstanceRelation ( Long iworkitemid, int sysType ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "select b.iversion_id,b.ison_version_id from IEAI_RUNLOGIC_INSTANCE b where b.iworkitemid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iworkitemid);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iversion_id", rset.getLong("iversion_id"));
                map.put(ISON_VERSION_ID, rset.getLong(ISON_VERSION_ID));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getInstanceRelation Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultList;
    }

    public List getCenterSystem ( String centerident, String runInstanceTable, String runInfoInstacenTable,
            String runinsname, Connection con, int systype ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select distinct a.ISYSNAME,a.ISWITCHTO from " + runInstanceTable + " a," + runInfoInstacenTable + SQLONE
                    + systype + " and a.IRUNINSNAME=? and b.ICENTER=?";
            ps = con.prepareStatement(sql);
            ps.setString(1, runinsname);
            ps.setString(2, centerident);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map map = new HashMap();
                map.put("sysname", rs.getString("ISYSNAME"));
                map.put("switchto", rs.getString("ISWITCHTO"));
                list.add(map);
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getCenterSystem Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCenterSystem", _log);
        }
        return list;
    }

    public int getCenterComputerCount ( String centerident, String runInstanceTable, String runInfoInstacenTable,
            String runinsname, Connection con, int systype ) throws RepositoryException
    {
        int computerCount = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select count(distinct b.iip) as computerCount from " + runInstanceTable + " a,"
                    + runInfoInstacenTable + " b where a.iid=b.iruninsid and a.ISYSTYPE=" + systype
                    + " and b.iacttype<>2 and a.IRUNINSNAME=? and b.icenter = ?";
            ps = con.prepareStatement(sql);
            ps.setString(1, runinsname);
            ps.setString(2, centerident);
            rs = ps.executeQuery();
            while (rs.next())
            {
                computerCount = rs.getInt("computerCount");
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getCenterComputerCount Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCenterComputerCount", _log);
        }
        return computerCount;
    }
    
    public int getCenterComputerCount ( String centerident, String runinsname, Connection con )
            throws RepositoryException
    {
        int computerCount = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT COUNT(DISTINCT E.IIP) computerCount FROM "
                    + " (SELECT B.IIP FROM IEAI_RUN_INSTANCE_HIS A,IEAI_RUNINFO_INSTANCE_HIS B WHERE A.IID = B.IRUNINSID AND A.ISYSTYPE = 4 AND B.IACTTYPE <>2 AND A.IRUNINSNAME=? AND B.ICENTER = ?"
                    + " UNION ALL"
                    + " SELECT D.IIP FROM IEAI_RUN_INSTANCE C,IEAI_RUNINFO_INSTANCE D WHERE C.IID = D.IRUNINSID AND C.ISYSTYPE = 4 AND D.IACTTYPE <>2 AND C.IRUNINSNAME=? AND D.ICENTER = ?) E";
            ps = con.prepareStatement(sql);
            ps.setString(1, runinsname);
            ps.setString(2, centerident);
            ps.setString(3, runinsname);
            ps.setString(4, centerident);
            rs = ps.executeQuery();
            while (rs.next())
            {
                computerCount = rs.getInt("computerCount");
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getCenterComputerCount Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCenterComputerCount", _log);
        }
        return computerCount;
    }
    public int getAgentCount ( Connection con, String sysname, long centerid ) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select count(distinct IAGENT_IP) as computerCount from IEAI_AGENTINFO WHERE IAGENT_NAME=? AND IDCID=?";
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, sysname);
            ps.setLong(2, centerid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("computerCount");
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getAgentCount Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getAgentCount", _log);
        }
        return count;
    }

    public List getSwitchMonitorConnerScreen ( Long runInsID, int systype ) throws RepositoryException
    {
        List<Map<String, String>> slist = new ArrayList<Map<String, String>>();
        String sql = "";
        String runInfoCol="ISTATE,IISFAIL,iconner,iconnername,IRUNINSID";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "  select iconner,min(iconnername) connername,count(if(b.ISTATE=1,1,null)) as noBeginCount,count(if(b.ISTATE=0,(if(b.IISFAIL=-1 or b.IISFAIL=0,1,null)),null)) as runningCount,"
                    + "  count(if(b.ISTATE=2 or b.ISTATE=3,1,null)) as finishedCount,count(if(b.ISTATE=0,(if(b.IISFAIL=1 or b.IISFAIL=2 or b.IISFAIL=3 or b.IISFAIL=4,1,null)),null)) as failCount,count(*) totalCount "
                    + "  from (select "+runInfoCol+" from IEAI_RUNINFO_INSTANCE union select "+runInfoCol+" from IEAI_RUNINFO_INSTANCE_HIS) b where b.IRUNINSID=? group by b.iconner order by b.iconner";
        } else
        {
            sql = "  select iconner,min(iconnername) connername,count(decode(b.ISTATE,1,1,null)) as noBeginCount,count(decode(b.ISTATE,0,(decode(b.IISFAIL,-1,1,0,1,null)),null)) as runningCount,"
                    + "  count(decode(b.ISTATE,2,1,3,1,null)) as finishedCount,count(decode(b.ISTATE,0,(decode(b.IISFAIL,1,1,2,1,3,1,4,1,null)))) as failCount,count(*) totalCount "
                    + "  from (select "+runInfoCol+" from IEAI_RUNINFO_INSTANCE union select "+runInfoCol+" from IEAI_RUNINFO_INSTANCE_HIS) b where b.IRUNINSID=? group by b.iconner order by b.iconner";
        }
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        String str = "state";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(systype);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, runInsID);
            rset = ps.executeQuery();
            while (rset.next())
            {
                String conner = rset.getString(ICONNER);
                String connername = rset.getString(CONNERNAME) == null ? "" : rset.getString(CONNERNAME);
                long noBeginCount = rset.getLong(NOBEGINCOUNT);
                long runningCount = rset.getLong(RUNNINGCOUNT);
                long finishedCount = rset.getLong(FINISHEDCOUNT);
                long failCount = rset.getLong(FAILCOUNT);
                long totalCount = rset.getLong("totalCount");

                Map<String, String> mapStatus = new LinkedHashMap<String, String>();
                mapStatus.put(CONNERNAME, connername);
                mapStatus.put(CONNER, conner);
                if (failCount > 0)
                {
                    mapStatus.put(str, STEP_RUNNIGFAIL);
                } else if (runningCount > 0)
                {
                    mapStatus.put(str, STEP_RUNNIG);
                } else if (noBeginCount == totalCount)
                {
                    mapStatus.put(str, STEP_NOSTART);
                } else if (finishedCount == totalCount)
                {
                    mapStatus.put(str, STEP_FINISH);
                } else
                {
                    mapStatus.put(str, STEP_RUNNIG);
                }
                slist.add(mapStatus);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSwitchMonitorConner Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return slist;
    }

    public Map getSwitchMonitorSysMessScreen ( Long runisid, int sysType ) throws RepositoryException
    {
        Map mapOther = new HashMap();
        if (runisid == null)
        {
            return mapOther;
        }
        String sql = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sql = "select a.ISWITCHTO,count(distinct if(b.IIP='',null,b.IIP)) as ipCount,count(b.IID) as stepCount,if(a.istate=0,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate()
                    + ",8)-a.istarttime , a.IENDTIME-a.ISTARTTIME) as paidtime from ( select * from ieai_run_instance union select * from ieai_run_instance_his) a,  (select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b  where a.IID=b.IRUNINSID and a.IID=? group by a.IID,a.ISWITCHTO,a.istate,a.ISTARTTIME,a.IENDTIME";
        } else
        {
            sql = "select a.ISWITCHTO,count(distinct b.IIP) as ipCount,count(b.IID) as stepCount,decode(a.istate,0,FUN_GET_DATE_NUMBER_NEW("
                    + Constants.getCurrentSysDate()
                    + ",8)-a.istarttime , a.IENDTIME-a.ISTARTTIME) as paidtime from ( select * from ieai_run_instance union select * from ieai_run_instance_his) a,  (select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b  where a.IID=b.IRUNINSID and a.IID=? group by a.IID,a.ISWITCHTO,a.istate,a.ISTARTTIME,a.IENDTIME";
        }
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, runisid);
            rset = ps.executeQuery();
            while (rset.next())
            {
                String iswitchto = rset.getString(SWITCHTO);
                String[] iswitchtos = iswitchto.split("#");
                if (iswitchtos != null && iswitchtos.length == 3)
                {
                    mapOther.put(SWITCHTO, iswitchtos[2]);
                    mapOther.put(SCENETYPE, iswitchtos[1]);
                } else
                {
                    mapOther.put(SWITCHTO, iswitchto);
                    mapOther.put(SCENETYPE, iswitchto);
                }
                mapOther.put(IPCOUNT, rset.getInt(IPCOUNT));
                mapOther.put(STEPCOUNT, rset.getInt(STEPCOUNT));
                long paidTime = rset.getLong("paidTime") / 1000;
                long hour = paidTime / 3600;
                long minute = paidTime % 3600 / 60;
                long second = paidTime % 3600 % 60;
                String paidstring = String.format("%02d:%02d:%02d", hour, minute, second);
                mapOther.put("paidtime", paidstring);
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSwitchMonitorSysMess Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return mapOther;
    }

    public List getSysProgressDataScreen ( Long iid, int sysType ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = "";
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                sql = "select count(if(b.istate=2 or b.istate=3,1,null)) as finishedCount,count(if(b.istate=1,1,null)) as noFinishedCount, "
                        + "count(if(b.istate=0,if(b.istate=-1 or b.istate=0,null,1),null)) as erroCount,"
                        + "count(if(b.istate=0,if(b.istate=-1 or b.istate=0,1,null),null)) as runingCount "
                        + "from ( select * from ieai_run_instance union select * from ieai_run_instance_his) a,(select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b where a.iid=b.iruninsid and a.iid=?";
            } else
            {
                sql = "select count(decode(b.istate,2,1,3,1)) as finishedCount,count(decode(b.istate,1,1,null)) as noFinishedCount, "
                        + "count(decode(b.istate,0,decode(b.iisfail,-1,null,0,null,1),null)) as erroCount,"
                        + "count(decode(b.istate,0,decode(b.iisfail,-1,1,0,1,null),null)) as runingCount "
                        + "from ( select * from ieai_run_instance union select * from ieai_run_instance_his) a,(select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b where a.iid=b.iruninsid and a.iid=?";
            }
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("name", "已完成");
                map.put("data", rset.getLong(FINISHEDCOUNT));
                resultList.add(map);

                Map map1 = new HashMap();
                map1.put("name", "未开始");
                map1.put("data", rset.getLong("noFinishedCount"));
                resultList.add(map1);

                Map map2 = new HashMap();
                map2.put("name", "运行中");
                map2.put("data", rset.getLong("runingCount"));
                resultList.add(map2);

                Map map3 = new HashMap();
                map3.put("name", "异常");
                map3.put("data", rset.getLong("erroCount"));
                resultList.add(map3);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSysProgressData Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultList;
    }

    public List getSwitchStepScreen ( long iruninsid, long connerNum, int sysType ) throws RepositoryException
    {
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT);
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = " select b.iexecuser,b.ishellscript,b.iid,b.icenter,b.iconnername,b.iflowid,a.isysname,a.iruninsname,b.iserner,"
                    + " b.iprener,a.iswitchto,b.iip,b.iconner,b.iactname,b.iactdes,b.ishellpath,b.iparameter,b.istarttime,b.iendtime, "
                    + " b.istate,b.iisfail,b.iacttype,(select ifullname from ieai_user  where iloginname=b.iowner) as username,fun_get_date_number_new(" + Constants.getCurrentSysDate()
                    + ",8) as mysysdate,b.irerunflag "
                    + " from  (select * from ieai_run_instance union select * from ieai_run_instance_his) a,(select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b "
                    + " where a.iid=b.iruninsid " + " and b.iruninsid=? " + " and b.iconner=? "
                    + " order by b.istate,b.iserner,b.iconner,b.irerunflag";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iruninsid);
            ps.setLong(2, connerNum);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put(FLOWID, rset.getLong(FLOWID));
                map.put(ISYSNAME, rset.getString(ISYSNAME));
                map.put(RUNINSNAME, rset.getString(RUNINSNAME));
                map.put("iip", rset.getString("iip"));
                map.put(SERNER, rset.getDouble(SERNER));
                map.put(PRENER, rset.getString(PRENER));
                map.put("iowner",rset.getString("username")==null?"":rset.getString("username"));
                String iswitchto = rset.getString(SWITCHTO);
                String[] iswitchtos = iswitchto.split("#");
                if (iswitchtos != null && iswitchtos.length == 3)
                {
                    map.put(SWITCHTO, iswitchtos[2]);
                } else
                {
                    map.put(SWITCHTO, iswitchto);
                }
                map.put(ICONNER, rset.getLong(ICONNER));
                map.put(IACTNAME, rset.getString(IACTNAME));
                map.put(IACTDES, rset.getString(IACTDES));
                map.put(ISHELLPATH, rset.getString(ISHELLPATH) + " "
                        + (rset.getString(IPARAMETER) == null ? "" : rset.getString(IPARAMETER)));
                map.put(ISTARTTIME,
                    rset.getLong(ISTARTTIME) == 0 ? "" : sdf.format(new Date(rset.getLong(ISTARTTIME))));
                map.put(IENDTIME, rset.getLong(IENDTIME) == 0 ? "" : sdf.format(new Date(rset.getLong(IENDTIME))));
                map.put(EXECUSER, rset.getString(EXECUSER));
                map.put(ISHELLSCRIPT, rset.getString("ISHELLSCRIPT"));
                this.getSwitchStepSon(map, rset.getLong(ISTATE), rset.getLong(ISTARTTIME), rset.getLong(IENDTIME),
                    rset.getLong(MYSYSDATE));
                map.put(ISTATE, rset.getLong(ISTATE));
                map.put(IISFAIL, rset.getLong(IISFAIL));
                map.put(ACTTYPE, rset.getLong(ACTTYPE));
                map.put(RETURNFLAG, rset.getLong(RETURNFLAG));
                map.put(ICENTER, rset.getString(ICENTER));
                map.put(ICONNERNAME, rset.getString(ICONNERNAME));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSwitchStepScreen Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultList;
    }
    public List getSwitchStepScreenAll ( long iruninsid, int sysType ) throws RepositoryException
    {
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT);
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            String sql = " select b.iexecuser,b.ishellscript,b.iid,b.icenter,b.iconnername,b.iflowid,a.isysname,a.iruninsname,b.iserner,"
                    + " b.iprener,a.iswitchto,b.iip,b.iconner,b.iactname,b.iactdes,b.ishellpath,b.iparameter,b.istarttime,b.iendtime, "
                    + " b.istate,b.iisfail,b.iacttype,(select ifullname from ieai_user  where iloginname=b.iowner) as username,fun_get_date_number_new(" + Constants.getCurrentSysDate()
                    + ",8) as mysysdate,b.irerunflag "
                    + " from  (select * from ieai_run_instance union select * from ieai_run_instance_his) a,(select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b "
                    + " where a.iid=b.iruninsid " + " and b.iruninsid=? "
                    + " order by b.istate,b.iserner,b.iconner,b.irerunflag";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iruninsid);
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put(FLOWID, rset.getLong(FLOWID));
                map.put(ISYSNAME, rset.getString(ISYSNAME));
                map.put(RUNINSNAME, rset.getString(RUNINSNAME));
                map.put("iip", rset.getString("iip"));
                map.put(SERNER, rset.getDouble(SERNER));
                map.put(PRENER, rset.getString(PRENER));
                map.put("iowner",rset.getString("username"));
                String iswitchto = rset.getString(SWITCHTO);
                String[] iswitchtos = iswitchto.split("#");
                if (iswitchtos != null && iswitchtos.length == 3)
                {
                    map.put(SWITCHTO, iswitchtos[2]);
                } else
                {
                    map.put(SWITCHTO, iswitchto);
                }
                map.put(ICONNER, rset.getLong(ICONNER));
                map.put(IACTNAME, rset.getString(IACTNAME));
                map.put(IACTDES, rset.getString(IACTDES));
                map.put(ISHELLPATH, rset.getString(ISHELLPATH) + " "
                        + (rset.getString(IPARAMETER) == null ? "" : rset.getString(IPARAMETER)));
                map.put(ISTARTTIME,
                    rset.getLong(ISTARTTIME) == 0 ? "" : sdf.format(new Date(rset.getLong(ISTARTTIME))));
                map.put(IENDTIME, rset.getLong(IENDTIME) == 0 ? "" : sdf.format(new Date(rset.getLong(IENDTIME))));
                map.put(EXECUSER, rset.getString(EXECUSER));
                map.put(ISHELLSCRIPT, rset.getString("ISHELLSCRIPT"));
                this.getSwitchStepSon(map, rset.getLong(ISTATE), rset.getLong(ISTARTTIME), rset.getLong(IENDTIME),
                    rset.getLong(MYSYSDATE));
                map.put(ISTATE, rset.getLong(ISTATE));
                map.put(IISFAIL, rset.getLong(IISFAIL));
                map.put(ACTTYPE, rset.getLong(ACTTYPE));
                map.put(RETURNFLAG, rset.getLong(RETURNFLAG));
                map.put(ICENTER, rset.getString(ICENTER));
                map.put(ICONNERNAME, rset.getString(ICONNERNAME));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSwitchStepScreen Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultList;
    }
    

    public void getSwitchStepSon ( Map map, long state, long start, long end, long mysysdate )
            throws RepositoryException
    {
        try
        {
            String paidstring = "";
            long paidtime = 0;
            if ((state == 0 || state == 4) && start > 0)
            {
                paidtime = (mysysdate - start) / 1000;
            } else
            {
                if (start > 0)
                {
                    paidtime = (end - start) / 1000;
                }
            }
            if (paidtime > 3600)
            {
                paidstring = paidtime / 3600 + "时";
                if (paidtime % 3600 > 60)
                {
                    paidstring = paidstring + (paidtime % 3600) / 60 + "分" + paidtime % 60 + "秒";
                } else
                {
                    paidstring = paidstring + paidtime % 60 + "秒";
                }
            } else
            {
                if (paidtime > 60)
                {
                    paidstring = paidtime / 60 + "分" + paidtime % 60 + "秒";
                } else
                {
                    paidstring = paidtime + "秒";
                }
            }
            map.put(RUNTIME, paidstring);
            map.put("stepStartLong", start);
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSwitchStepSon Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
    }

    public List getSwitchMonitorStepByStepIdScreen ( String iruninfoinsid, String flag, int sysType )
            throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        List resultList = new ArrayList();
        try
        {

            String sql = "select b.IID,b.iflowid,b.ISTATE,b.IISFAIL,b.iacttype,b.IREMINFO from  (select * from IEAI_RUNINFO_INSTANCE union select * from IEAI_RUNINFO_INSTANCE_HIS) b where b.IID=?";
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(iruninfoinsid));
            rset = ps.executeQuery();
            while (rset.next())
            {
                Map map = new HashMap();
                map.put("iid", rset.getLong("iid"));
                map.put(FLOWID, rset.getLong(FLOWID));
                map.put(ISTATE, rset.getLong(ISTATE));
                map.put(IISFAIL, rset.getLong(IISFAIL));
                map.put(ACTTYPE, rset.getLong(ACTTYPE));
                map.put(IREMINFO, rset.getString(IREMINFO));
                resultList.add(map);
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getSwitchMonitorStepByStepIdScreen Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultList;
    }

    public List<String> getNewSwitchList ( String runInstanceTable, Connection con, int systype ) throws RepositoryException
    {
        List reList = new ArrayList();
        String runinsname = "";
        String sql = "select distinct IRUNINSNAME  from " + runInstanceTable + " where ISYSTYPE=" + systype;
        PreparedStatement ps = null;
        ResultSet rs = null;

        try
        {
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                runinsname = rs.getString("IRUNINSNAME");
                reList.add(runinsname);
                //                break;
            }
        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getNewSwitch Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getNewSwitch", _log);
        }

        return reList;
    }

    public List getCenterSystemForRun ( String centerident, String runInstanceTable, String runInfoInstacenTable,
            Connection con, int systype ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select distinct a.ISYSNAME,a.ISWITCHTO from " + runInstanceTable + " a," + runInfoInstacenTable + SQLONE
                    + systype + " and b.ICENTER=?";
            ps = con.prepareStatement(sql);
            ps.setString(1, centerident);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map map = new HashMap();
                map.put("sysname", rs.getString("ISYSNAME"));
                map.put("switchto", rs.getString("ISWITCHTO"));
                list.add(map);
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getCenterSystem Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCenterSystem", _log);
        }
        return list;
    }

    public int getCenterComputerCountForRun ( String centerident, Connection con )
            throws RepositoryException
    {
        int computerCount = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "SELECT COUNT(DISTINCT E.IIP) computerCount FROM "
                    + " ( "
                    + " SELECT D.IIP FROM IEAI_RUN_INSTANCE C,IEAI_RUNINFO_INSTANCE D WHERE C.IID = D.IRUNINSID AND C.ISYSTYPE = 4 AND D.IACTTYPE <>2 AND D.ICENTER = ?) E";
            ps = con.prepareStatement(sql);
            ps.setString(1, centerident);
            rs = ps.executeQuery();
            while (rs.next())
            {
                computerCount = rs.getInt("computerCount");
            }

        } catch (Exception e)
        {
            _log.error("ScreenCsManage.getCenterComputerCount Error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCenterComputerCount", _log);
        }
        return computerCount;
    }
}
