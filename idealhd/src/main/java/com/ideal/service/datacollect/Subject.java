package com.ideal.service.datacollect;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 被观察者
 * 【注意】:在receiveData()方法中通知所有观察者
 */
public class Subject<T> {
    //观察者集合
    private List<Observer> observerList = new ArrayList<Observer>();
    //变化的数据（**根据需要修改**）
    private T data;
    //常驻线程数：根据计算机的实际能力，默认采用核数的一半
    private int corePoolSize = ObserverConstants.dynamicThreadNum;
    //最大线程数：根据计算机的实际能力，默认采用核数的一半
    private int maximumPoolSize = ObserverConstants.dynamicThreadNum;

    //线程
    private ExecutorService pool = null;

    /**
     * 构造方法1
     */
    public Subject() {
        pool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>()
                /*new ThreadFactoryBuilder().setNameFormat("observer-%d-pool").build()*/);
    }

    /**
     * 构造方法2
     */
    public Subject(List<Observer> observerList) {
        this();
        this.observerList = observerList;
    }

    /**
     * 构造方法3
     */
    public Subject(List<Observer> observerList, int corePoolSize,
                   int maximumPoolSize) {
        this(corePoolSize, maximumPoolSize);
        this.observerList = observerList;
    }

    /**
     * 构造方法4
     */
    public Subject(int corePoolSize,
                   int maximumPoolSize) {
        pool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>()
                /*new ThreadFactoryBuilder().setNameFormat("observer-%d-pool").build()*/);
    }

    /**
     * 添加观察者
     *
     * @param observer
     */
    public void attach(Observer observer) {
        observerList.add(observer);
    }

    public void notifyObservers(final T data) {
        for (final Observer observer : observerList) {
            //启动线程池通知观察者
            pool.execute(new Runnable() {
                @Override
                public void run() {
                    observer.update(data);
                }
            });
        }
    }

    public T getData() {
        return data;
    }

    public void receiveData(T data) {
        this.data = data;
        //通知所有观察者
        notifyObservers(data);
    }
}
