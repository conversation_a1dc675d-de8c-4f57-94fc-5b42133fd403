/**
 * SpecialWorkDayInfo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.ideal.ieai.clientapi.ws;

public class SpecialWorkDayInfo  implements java.io.Serializable {
    private long calId;

    private java.lang.String description;

    private long id;

    private java.util.Calendar workDay;

    private com.ideal.ieai.clientapi.ws.WorkingTimeInfo[] workingTimes;

    public SpecialWorkDayInfo() {
    }

    public SpecialWorkDayInfo(
           long calId,
           java.lang.String description,
           long id,
           java.util.Calendar workDay,
           com.ideal.ieai.clientapi.ws.WorkingTimeInfo[] workingTimes) {
           this.calId = calId;
           this.description = description;
           this.id = id;
           this.workDay = workDay;
           this.workingTimes = workingTimes;
    }


    /**
     * Gets the calId value for this SpecialWorkDayInfo.
     * 
     * @return calId
     */
    public long getCalId() {
        return calId;
    }


    /**
     * Sets the calId value for this SpecialWorkDayInfo.
     * 
     * @param calId
     */
    public void setCalId(long calId) {
        this.calId = calId;
    }


    /**
     * Gets the description value for this SpecialWorkDayInfo.
     * 
     * @return description
     */
    public java.lang.String getDescription() {
        return description;
    }


    /**
     * Sets the description value for this SpecialWorkDayInfo.
     * 
     * @param description
     */
    public void setDescription(java.lang.String description) {
        this.description = description;
    }


    /**
     * Gets the id value for this SpecialWorkDayInfo.
     * 
     * @return id
     */
    public long getId() {
        return id;
    }


    /**
     * Sets the id value for this SpecialWorkDayInfo.
     * 
     * @param id
     */
    public void setId(long id) {
        this.id = id;
    }


    /**
     * Gets the workDay value for this SpecialWorkDayInfo.
     * 
     * @return workDay
     */
    public java.util.Calendar getWorkDay() {
        return workDay;
    }


    /**
     * Sets the workDay value for this SpecialWorkDayInfo.
     * 
     * @param workDay
     */
    public void setWorkDay(java.util.Calendar workDay) {
        this.workDay = workDay;
    }


    /**
     * Gets the workingTimes value for this SpecialWorkDayInfo.
     * 
     * @return workingTimes
     */
    public com.ideal.ieai.clientapi.ws.WorkingTimeInfo[] getWorkingTimes() {
        return workingTimes;
    }


    /**
     * Sets the workingTimes value for this SpecialWorkDayInfo.
     * 
     * @param workingTimes
     */
    public void setWorkingTimes(com.ideal.ieai.clientapi.ws.WorkingTimeInfo[] workingTimes) {
        this.workingTimes = workingTimes;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof SpecialWorkDayInfo)) return false;
        SpecialWorkDayInfo other = (SpecialWorkDayInfo) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            this.calId == other.getCalId() &&
            ((this.description==null && other.getDescription()==null) || 
             (this.description!=null &&
              this.description.equals(other.getDescription()))) &&
            this.id == other.getId() &&
            ((this.workDay==null && other.getWorkDay()==null) || 
             (this.workDay!=null &&
              this.workDay.equals(other.getWorkDay()))) &&
            ((this.workingTimes==null && other.getWorkingTimes()==null) || 
             (this.workingTimes!=null &&
              java.util.Arrays.equals(this.workingTimes, other.getWorkingTimes())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        _hashCode += new Long(getCalId()).hashCode();
        if (getDescription() != null) {
            _hashCode += getDescription().hashCode();
        }
        _hashCode += new Long(getId()).hashCode();
        if (getWorkDay() != null) {
            _hashCode += getWorkDay().hashCode();
        }
        if (getWorkingTimes() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getWorkingTimes());
                 i++) {
                java.lang.Object obj = java.lang.reflect.Array.get(getWorkingTimes(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(SpecialWorkDayInfo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "SpecialWorkDayInfo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("calId");
        elemField.setXmlName(new javax.xml.namespace.QName("", "calId"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("description");
        elemField.setXmlName(new javax.xml.namespace.QName("", "description"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("id");
        elemField.setXmlName(new javax.xml.namespace.QName("", "id"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("workDay");
        elemField.setXmlName(new javax.xml.namespace.QName("", "workDay"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("workingTimes");
        elemField.setXmlName(new javax.xml.namespace.QName("", "workingTimes"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "WorkingTimeInfo"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
