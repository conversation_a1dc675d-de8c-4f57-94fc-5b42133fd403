#!/bin/sh
############################################################
#程序名称：Auto_Call_Sql_ideal.sh                                
#程序功能：调用SQL文件   ##                                     
#程序描述：#该脚本用来执行db2或mysql或oracle的sql         
#time  ：20171030 
#update : dan_shi                         
############################################################
export LANG=zh_CN.utf8
export LC_ALL=zh_CN.utf8
############################################################
  localPath=$1        #该变量表示投产包下载到目标服务器上的路径  #
packageName=$2      #该变量表示投产包名#
 moduleName=$3       #该变量表示应用类型 #
 backupPath=$4       #该变量表示数据库备份文件目录#
    db_name=$5
    db_user=$6
    db_pswd=$7
      db_ip=$8
    db_port=$9
#############################################################
shellpath=`dirname $0`   #脚本路径#
dbtype=${moduleName}
shellname=`basename $0`  #脚本名称#
cd ${shellpath}
workdir=`pwd`
config=${workdir}/config
lib=${workdir}/lib
log=${workdir}/log
temp=${workdir}/temp
javaPath=/usr/java5/bin
packageType=`echo ${packageName##*.}`
nowtime=`date +'%Y%m%d%T'`
dbtime=`echo $nowtime|tr -d ":"`
dbpackageName=`echo $packageName|awk -F . '{ print $1 }'`
#加载函数列表#
. ${lib}/function.lib
TOOL=${TAR}
##############################################################
#错误输出函数#
ERROR()
{
msg=$1
now=`date +'%Y-%m-%d %T'`
echo -e "\n"
echo "${now} ERROR ${msg}"
exit
}

#标准输出函数#
INFO()
{
msg=$1
now=`date +'%Y-%m-%d %T'`
echo -e "\n"
echo "${now} INFO ${msg}"
}

SUCC()
{
echo "0"
}

#检查文件存在函数#
checkFileExist()
{
filename=$1
if [ ! -e ${filename} ];then
	ERROR "${filename} is not exist,no such file or directory."
	exit
fi
}
#检查执行结果函数#
checkRetExit()
{
if [ $1 -ne 0 ];then
        exit
fi
}

checkRet()
{
error=0
if [ $1 -ne 0 ];then
        let error+=1
fi
}
########################################
#参数检查#
if [ $# -ne 9 ];then
	log "9 parameters needed!" 
	exit
fi

#备份目录检查#
if [ ! -d "${backupPath}" ];then
	mkdir -p ${backupPath}
fi

checkFileExist ${localPath}${packageName}

#为避免数据库备份文件过大，转移到备份路径执行sh#
#cd ${backupPath}
cd ${localPath}
checkRetExit $?


#解压缩类型目录到备份目录#
${TOOL} -xf ${localPath}${packageName} ${packageName%.tar}/${moduleName}
if [ $? -ne 0 ];then
        ERROR "get ${moduleName} from ${packageName} failure." 
       	exit	
fi

#类型下面固定目录patchScript(增量SQL)#
cd ${packageName%.tar}/${moduleName}/patchScript/
filePath=${localPath}${packageName%.tar}/${moduleName}/patchScript/

checkRetExit $?

if [ ! -d log ];then
	mkdir log
	checkRetExit $?
fi

#INFO "all check is through,begin call db command."

#计算sql文件的总数#
filenum=`ls -l|grep -wi "sql"|awk '{print $NF}'|sort -g|wc -l`
# filenum=`cat ${filePath}/update.txt`


if [ ${filenum} -eq 0 ];then
	ERROR "no sql/SQL file is found."
fi

#执行mysql脚本的函数#
succflag=0
exeMysqlSql()
{

for file in `cat ${filePath}/update.txt`

do
  mysql -h ${db_ip} -P ${db_port} -u ${db_user} -p${db_pswd} -D${db_name} -e"source ${file}" >${filePath}/log/${file}.log 2>&1 
#	mysql -u${db_user} -p${db_pswd} -D${db_name} -e"source ${file}" >${filePath}/log/${file}.log 2>&1 
  
	checkFileExist ${filePath}/log/${file}.log
	#查找错误关键字，判断执行结果#
	
	grep "ERROR" ${filePath}/log/${file}.log
	if [ $? -eq 0 ];then
		ERROR "sql "${file}" execute with error,script interrupt!!"
		cat ${filePath}/log/${file}.log
		#有错误退出#
		exit
	else
		INFO "sql "${file}" execute end successfully."
		let succflag+=1
		SUCC
	fi	
done

}

#执行oracle脚本的函数#
exeOraSql()
{

ret=0

cd ${filePath}
export ORACLE_SID=${db_name}
for file in `cat update.txt`

do
sqlplus ${db_user}/${db_pswd}@${db_ip}/${db_name} <<EOF
set pagesize 500
set echo on
spool ${filePath}/log/${file}.log
@${file}
whenever sqlerror exit
exit
EOF
#检查错误关键字个数#
errornum=`grep -cE "ORA|ERROR" ${filePath}/log/${file}.log`
if [[ ${errornum} -gt 0 ]];then
        echo "sql excute with error!"
else
	let succflag+=1	
fi
done
#判断最终执行结果#
if [[ -e ${errornum} ]] && [[ ${errornum} -gt 0 ]];then
	ERROR "sql excute with error!"
else
	echo "sql excute end successfully."
	SUCC
fi
}

#执行db2脚本的函数#
exeDB2Sql()
{

#为保证连接数据库session，初始化临时执行脚本#
:>autorun.sh
echo "#!/bin/bash" >> autorun.sh
echo "db2 connect to ${db_name} user ${db_user} using ${db_pswd}" > autorun.sh 
for file in `cat ${filePath}/update.txt`
do
	echo "echo begin excute ${file}" >>autorun.sh
	      echo "db2 -td$ -f ${file}" >>autorun.sh
#	      echo "db2 -t -f ${file}" >>autorun.sh
	  echo "echo excute ${file} end" >>autorun.sh
done
echo "sh ${filePath}/update.sh ${db_name}" >>autorun.sh
sh autorun.sh|tee ${filePath}/log/autorun.log 2>&1	

#cd ${workdir}
#. ./sql_db2.sh ${db_user} ${db_pswd} ${db_ip} ${backupPath} ${filePath}|tee ${filePath}/log/autorun.log 2>&1	
checkFileExist "${filePath}/log/autorun.log"
#检查错误关键字个数#
errornum=`grep -cE "SQL*N|DB[0-9][0-9][0-9][0-9][0-9]E|SQLSTATE=[0-9][0-9][0-9][0-9][0-9]" ${filePath}/log/autorun.log`
if [ ${errornum} -gt 0 ];then
	ERROR "sql excute with error!"
else
	INFO "sql excute end successfully."
	SUCC
fi
}



function del_cache
{
package=$1
if [ -e ${package} ];then
rm -rf ${package}
fi

}

#main
cd $filePath
case ${dbtype} in 
mysql)
exeMysqlSql ${db_user} ${db_pswd} ${db_name} ${db_ip} ${db_port}
	;;
db2)
exeDB2Sql ${db_user} ${db_pswd} ${db_name} ${db_ip} ${filePath}
	;;
oracle)
exeOraSql ${db_user} ${db_pswd} ${db_name} ${db_ip} ${db_port} ${filePath}
# sh ${shellpath}/update_oracle.sh ${db_user} ${db_pswd} ${db_name} ${db_ip} ${db_port} ${filePath} ${moduleName}
	;;
	
*)
	ERROR "db type unmatched."
	;;
esac
del_cache ${localPath}/${packageName%.tar}