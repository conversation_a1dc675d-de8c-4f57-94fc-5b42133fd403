package com.ideal.ieai.server.repository.customReport;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.mail.MyAuthenticator;
import com.ideal.ieai.server.repository.db.DBException;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

// import com.ideal.ieai.server.mail.MailSenderBean;


/**
 * <p>
 * Title: DailyCheckThread
 * </p>
 * <p>
 * Description: 日报检测线程
 * </p>
 * <p>
 * Copyright: Copyright (c) 2015
 * </p>
 * <p>
 * Company: Ideal Technologies Inc.
 * </p>
 * 
 * <AUTHOR>
 */
public class CustomReportDailyCheckThread extends Thread // implements Runnable
{
    private static final Logger            _log          = Logger.getLogger(CustomReportDailyCheckThread.class);
    // 线程开关
    private boolean                        dailyCheck    = true;
    // 线程轮询时间
    private long                           checkTime     = 2 * 60 * 1000;

    private static CustomReportSendManager manager       = CustomReportSendManager.getInstance();

    private static MailSenderBean          mailInfo      = new MailSenderBean();
    private final static int               MAIL_SEND     = 1;
    private final static int               MAIL_SEND_NOT = 0;

    public void run ()
    {
        while (true)
        {
            try
            {
                _log.info("focus check is " + dailyCheck);
                // 发送邮件
                checkMail();
            } catch (

            DBException e)
            {
                _log.error("start checkDailyDetection error!", e);
            } catch (Exception e)
            {
                _log.error("start checkDailyDetection error!", e);
                e.printStackTrace();
            }
            try
            {
                Thread.sleep(checkTime);
            } catch (InterruptedException e)
            {
                _log.error("Sleep DailyCheckThread Error");
            }
        }
    }

    /**
     * 轮询需要发送邮件的检测表
     * 
     * @throws Exception
     * 
     */
    public static void checkMail () throws Exception
    {
        long currentTime = System.currentTimeMillis();
        // 可以发送邮件的配置并且已经到达发送时间
        List<DailyInfo> list = manager.getDailyDetection(MAIL_SEND, currentTime, Constants.IEAI_IEAI);
        // _log.info("get mail data!");
        // JFreeChartdDao dao = null;
        // 检查方案，发送邮件
        if (list.size() > 0)
        {
            _log.info("now send mail start,mail num is " + list.size());
        }
        for (DailyInfo info : list)
        {
            mailInfo = getManager(info);

            // MailSenderBean

            // String str = dao.toStr(info);

            // 发送邮件
            setMail(info);
            // System.out.println(str.toString());
            _log.info("send mail start,mail subject is " + info.getIsubject());
            sendTextMail(info.getIconfigid());
            manager.removeDetectionToHis(info, Constants.IEAI_IEAI);
        }

        // _log.info("send mail!");
    }

    /**
     * mail信息的初始化
     * 
     * @param info
     * @param mailInfo2 
     */
    public static void setMail ( DailyInfo info )// , MailSenderBean mailInfo
    {
        // DailyManager manager = DailyManager.getInstance();
        Map<String, Object> map = new HashMap<String, Object>();

        map = manager.getServerMailInfo(Constants.IEAI_IEAI);

        int iverifyuser = (Integer) map.get("iverifyuser");
        String serveraddr = (String) map.get("serveraddr");
        int serverport = (Integer) map.get("serverport");
        String username = (String) map.get("username");
        String password = (String) map.get("password");
        String fromuser = (String) map.get("fromuser");
        String smtp = (String) map.get("smtp");
        mailInfo.setContent(info.getIsubject());
        mailInfo.setValidate(1 == iverifyuser ? true : false);
        mailInfo.setMailServerHost(serveraddr);
        mailInfo.setMailServerPort(Integer.toString(serverport));
        mailInfo.setUserName(username);
        mailInfo.setPassword(password);
        mailInfo.setFromAddress(fromuser);
        mailInfo.setSmtp(smtp);
        mailInfo.setSubject(info.getIsubject());
    }

    public static boolean sendTextMail ( long configId )// , MailSenderBean mailInfo
    {
        // String cid = mailInfo.getPath().substring(mailInfo.getPath().lastIndexOf("\\") + 1);
        try
        {
            _log.info("send mail begin,plan id is " + configId);
            Session sendMailSession = null;
            Authenticator authenticator = null;
            Properties properties = new Properties();
            properties.put("mail.smtp.host", mailInfo.getMailServerHost());
            properties.put("mail.smtp.port", mailInfo.getMailServerPort());
            properties.put("mail.transport.protocol", "smtp");

            if (mailInfo.isValidate())
            {

                properties.put("mail.smtp.auth", "true");
                authenticator = new MyAuthenticator(mailInfo.getUserName(), mailInfo.getPassword());
                sendMailSession = Session.getInstance(properties, authenticator);

            } else
            {
                properties.put("mail.smtp.auth", "false");
                sendMailSession = Session.getInstance(properties);
            }

            // 邮件发送
            sendMailSession.setDebug(false);
            MimeMessage mailMessage = new MimeMessage(sendMailSession);
            mailMessage.setFrom(new InternetAddress(mailInfo.getFromAddress()));
            List<InternetAddress> list = mailInfo.getToAddressList();

            // System.out.println(list.size() + "+123123131312");
            InternetAddress[] iAddress = revertIEAIObjToAddress(list);
            _log.info("send mail begin,Address num is " + iAddress.length);
            if (null != iAddress && iAddress.length > 0)
            {
                mailMessage.setRecipients(Message.RecipientType.TO, iAddress);
            }

            mailMessage.setSubject(mailInfo.getSubject(), "UTF-8");
            mailMessage.setSentDate(new Date());
            MimeMultipart mainPart = new MimeMultipart();

            mailMessage.setText(mailInfo.getContent());
            mailMessage.setSentDate(new Date());

/*
 * String mailFolder = ServerEnv.getServerEnv().getMailFolder();
 * 
 * File file = new File(mailFolder);
 * if (!file.exists())
 * {
 * file.mkdir();
 * }
 */
            String filename = (new String((mailInfo.getSubject() + ".xls").getBytes("GBK")));
            // String mailFileEml = mailFolder + File.separator + filename;

            HSSFWorkbook bk = getReportListForSend(configId);

/*
 * FileOutputStream out = new FileOutputStream(mailFileEml);
 * File mailFile = new File(mailFileEml);
 * mailFile.createNewFile();
 * bk.write(out);
 * out.flush();
 * out.close();
 */

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            bk.write(baos);
            baos.flush();
            byte[] bt = baos.toByteArray();
            InputStream is = new ByteArrayInputStream(bt, 0, bt.length);
            baos.close();

            MimeBodyPart textBody = new MimeBodyPart();
            textBody.setContent(mailInfo.getContent(), "text/html; charset=utf-8");
            /* 添加附件 */
            MimeBodyPart fileBody = new MimeBodyPart();
            DataSource source = new ByteArrayDataSource(is, "application/msexcel");
            // DataSource source = new FileDataSource(new File(mailFileEml));
            // String fileName = "物料列表.xls";
            fileBody.setDataHandler(new DataHandler(source));

            // 中文乱码问题
            fileBody.setFileName(MimeUtility.encodeText(filename));

            mainPart.addBodyPart(textBody);
            mainPart.addBodyPart(fileBody);

/*
 * 
 * 
 * BodyPart attachmentBodyPart = new MimeBodyPart();
 * DataSource source = new FileDataSource(file);
 * attachmentBodyPart.setDataHandler(new DataHandler(source));
 * 
 * // MimeUtility.encodeWord可以避免文件名乱码
 * attachmentBodyPart.setFileName(MimeUtility.encodeWord(file.getName()));
 * mainPart.addBodyPart(attachmentBodyPart);
 */
            mailMessage.setContent(mainPart);
            mailMessage.saveChanges();

            /*
             * Transport ts = sendMailSession.getTransport();
             * ts.connect(mailInfo.getSmtp(), mailInfo.getUserName(), mailInfo.getPassword());
             * ts.sendMessage(mailMessage, mailMessage.getAllRecipients());
             */
            Transport.send(mailMessage);
            _log.info("send mail successfully!");

            // mailFile.delete();
            return true;

        } catch (Exception ex)
        {
            _log.info("send mail error!", ex);
        }
        return false;
    }

    /**
     * 初始化发件信息
     * 
     * @param prjName
     * @param info
     * @return
     * @throws DBException
     */
    public static MailSenderBean getManager ( DailyInfo info ) throws DBException
    {
        MailSenderBean mailInfo = new MailSenderBean();
        long configid = info.getIconfigid();
        try
        {
            List<InternetAddress> list = manager.getLeadingOfficialByConfigId(configid, Constants.IEAI_IEAI);
            mailInfo.setToAddressList(list);

        } catch (Exception e1)
        {
            // Repository
            e1.printStackTrace();
        }
/*
 * try
 * {
 * if (info.getCcPeople() == 1)
 * {
 * mailInfo.setCc(true);
 * List<InternetAddress> list1 = manager.getConcernedPeopleByPrjName(prjName);
 * mailInfo.setToCCList(list1);
 * } else
 * mailInfo.setCc(false);
 * } catch (RepositoryException e)
 * {
 * e.printStackTrace();
 * }
 */
        return mailInfo;
    }

    public static String formatDate ( long dateMillis )
    {
        String formatStr = "yyyy-MM-dd HH:mm:ss";
        return formatDate(dateMillis, formatStr);
    }

    public static String formatDate ( long dateMillis, String formatStr )
    {
        SimpleDateFormat format = new SimpleDateFormat(formatStr);
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(dateMillis);
        Date date = new Date();
        date = cal.getTime();
        return format.format(date);
    }

    private static InternetAddress[] revertIEAIObjToAddress ( List<InternetAddress> addressList )
    {
        InternetAddress[] address = new InternetAddress[addressList.size()];
        for (int i = 0, size = addressList.size(); i < size; i++)
        {
            address[i] = (InternetAddress) addressList.get(i);
        }
        return address;
    }

    private static HSSFWorkbook getReportListForSend ( long configId )
    {

        // String reportId = request.getParameter("reportId");
        Map<String, Object> map = new HashMap<String, Object>();
        // CustomReportService customReportService = new CustomReportService();
        // map = customReportService.getReportNameList(Constants.IEAI_IEAI, reportTypeId);

        map = manager.getReportListForSend(Constants.IEAI_IEAI, configId);
        List<String> headList = (List<String>) map.get("columns");
        List<Map> resultList = (List<Map>) map.get("dataList");

        List<List<String>> datas = new ArrayList<List<String>>();
        for (int j = 0; j < resultList.size(); j++)
        {
            List<String> data = new ArrayList<String>();
            for (String key : headList)
            {
                String dvalue = (String) resultList.get(j).get(key);
                if (StringUtils.isNotBlank(dvalue))
                {
                    data.add(dvalue);
                } else
                {
                    data.add("");
                }
            }
            datas.add(data);
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddmmss");
        Date nowDate = new Date();
        String nowTile = format.format(nowDate);
        return exportToExcel(headList, datas, "报表导出内容", nowTile);

    }

    private static HSSFWorkbook exportToExcel ( List<String> heads, List<List<String>> data, String sheetName,
            String filename )
    {
        HSSFWorkbook wb = new HSSFWorkbook();
        HSSFSheet sheet = wb.createSheet(sheetName);
        HSSFCellStyle style = wb.createCellStyle();
        HSSFRow row = sheet.createRow(0);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.CORNFLOWER_BLUE.getIndex());
        style.setFillPattern(SOLID_FOREGROUND);

        HSSFFont font2 = wb.createFont();
        font2.setFontName("仿宋_GB2312");
        font2.setFontHeightInPoints((short) 13);// 设置字体大小
        font2.setBold(true);// 粗体显示
        style.setFont(font2);// 选择需要用到的字体格式
        // 给表头第一行一次创建单元格

        for (int i = 0; heads != null && i < heads.size(); i++)
        {
            HSSFCell cell = row.createCell((short) i);
            cell.setCellValue(heads.get(i));
            cell.setCellStyle(style);
            sheet.setColumnWidth(i, 5000);
        }

        // 向单元格里填充数据
        for (int i = 0; data != null && i < data.size(); i++)
        {
            row = sheet.createRow(i + 1);
            List<String> _rowData = data.get(i);
            for (int j = 0; j < _rowData.size(); j++)
            {
                row.createCell(j).setCellValue(_rowData.get(j));
            }
        }

        return wb;
    }
}
