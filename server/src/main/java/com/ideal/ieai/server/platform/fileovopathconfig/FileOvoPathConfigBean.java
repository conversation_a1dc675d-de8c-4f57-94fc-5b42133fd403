/**  
 * All rights Reserved, Designed By www.idealinfo.com
 * @Title:  FileOvoPathConfigBean.java   
 * @Package com.ideal.ieai.server.platform.fileovopathconfig   
 * @Description:   Ovo 文件路径对象  
 * @author: 理想科技     
 * @date:   2019年3月11日 下午1:56:33   
 * @version V1.0 
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
package com.ideal.ieai.server.platform.fileovopathconfig;

/**   
 * @ClassName:  FileOvoPathConfigBean   
 * @Description:Ovo 文件路径对象  
 * @author: junyu_zhang 
 * @date:   2019年3月11日 下午1:56:33   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */
public class FileOvoPathConfigBean
{

    private Long iid;
    
    private String ivalue;
    
    private String idesc;

    public Long getIid ()
    {
        return iid;
    }

    public void setIid ( Long iid )
    {
        this.iid = iid;
    }

    public String getIvalue ()
    {
        return ivalue;
    }

    public void setIvalue ( String ivalue )
    {
        this.ivalue = ivalue;
    }

    public String getIdesc ()
    {
        return idesc;
    }

    public void setIdesc ( String idesc )
    {
        this.idesc = idesc;
    }
}
