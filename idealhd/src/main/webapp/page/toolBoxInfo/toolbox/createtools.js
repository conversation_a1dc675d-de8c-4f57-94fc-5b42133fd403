var openCreatetoolsAddWindowEdit;
var openCreatetoolsEditWindowEdit;
var openDetailWindowEdit;
var openBatchWindowEdit;
var openYxmsEditWindowEdit;

Ext.onReady (function ()
{
	destroyRubbish ();
	Ext.tip.QuickTipManager.init();
	
	Ext.define ('cToolsModel',{
		extend : 'Ext.data.Model',
		fields:[{
			name : 'iid',
            type : 'string'
		},{
			name : 'itoolCode',
            type : 'string'
		},{
			name : 'ionetypeId',
            type : 'string'
		},{
			name : 'ionetype',
            type : 'string'
		},{
			name : 'itwotypeId',
            type : 'string'
		},{
			name : 'itwotype',
            type : 'string'
		},{
			name : 'ithreetypeId',
            type : 'string'
		},{
			name : 'ithreetype',
            type : 'string'
		},{
			name : 'isystemId',
            type : 'string'
		},{
			name : 'isystem',
            type : 'string'
		},{
		// 	name : 'nOsType',
        //     type : 'string'
		// },{
			name : 'itoolName',
			type : 'string'
		},{
			name : 'itoolStatusId',
            type : 'string'
		},{
			name : 'itoolStatus',
            type : 'string'
		},{
			name : 'icreatetime',
            type : 'string'
		},{
			name : 'itoolAuditorId',
            type : 'string'
		},{
			name : 'itoolAuditor',
            type : 'string'
		},{
			name : 'iuserType',
            type : 'string'
		},{
			name : 'loginUserId',
            type : 'string'
		},{
			name : 'itoolType',
			type : 'string'
		},{
			name : 'itoolTypeId',
			type : 'Long'
		},{
			name : 'itoolScriptName',
			type : 'string'
		},{
			name : 'creater',
			type : 'string'
		},{
			name : 'ideliveryStatus',
			type : 'Long'
		},{
			name : 'ireceptionTime',
			type : 'Long'
		},{
			name : 'iupdateTime',
			type : 'string'
        },{
            name : 'isHighRisk',
            type : 'string'
		}]
	});
	var cstore = Ext.create ('Ext.data.Store',{
		model: 'cToolsModel',
		autoLoad:true,
		proxy: {
		    type: 'ajax',
		    url: 'getToolsListNo.do',
		    reader: {
		        type: 'json',
		        root: 'dataList',
		        totalProperty: 'total'
		    }
		}
	});
	cstore.on ('beforeload', function (store, options){
		var new_params ={
				isystemId:csystemCom.getValue(),
				ionetypeId: coneTypeCom.getValue(),
				itwotypeId: ctwoTypeCom.getValue(),
				ithreetypeId: cthreeTypeCom.getValue(),
				itoolType: citoolType.getValue(),
				itoolCode:ccodeField.getValue().substring(0,1000),
				itoolName:cnameField.getValue().substring(0,1000),
				itoolScriptName:snameField.getValue().substring(0,1000),
				itoolStatusId:cstatusCom.getValue(),
                isHighRisk:isHighRiskSea.getValue(),
			    ideliveryStatus:cdeliveryCom.getValue(),
			    ireceptionTimeStart:ireceptionTimeStart.rawValue,
			    ireceptionTimeEnd:ireceptionTimeEnd.rawValue,
				// nOsType:nOsTypeCom.getValue()
		}
		Ext.apply (cstore.proxy.extraParams, new_params);
	});
	
	// 查询
	var cqueryButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '查询',
	    handler : cqueryWhere
	});
	
	// 重置
	var cresetButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '重置',
	    handler : cresetWhere
	});

	// 增加
	var caddButton = Ext.create("Ext.Button", {
		// renderTo: Ext.get("div2"),
		// id: "bt4",
		text: "增加",
		cls : 'Common_Btn',
		menu:
			{
				items: [
					{
						text: '描述工具',
						handler: function () {
							add(1);
						}
					}, {
						text: '脚本工具',
						handler: function () {
							add(3);
						}
					}, {
						text: '组合工具',
						handler: function () {
							add(2);
						}
					}
				]
			}
	}).showMenu();

	// 编辑
	var ceditButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '编辑',
	    handler : edit
	});
	// 删除
	var cdeleteButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
	    textAlign : 'center',
	    text : '删除',
//	    disabled : true,
	    handler : remove
	});
	//脚本同步
	var csyncButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '脚本同步',
		handler : syncBtn
	});
	var cselfButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '自愈策略',
		handler : selfFuction
	});

	var cbatchButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '批量提交',
		handler : batchBtn
	});

	var cwithdrawButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '撤回',
		handler : withdrawBtn
	});

	var cdeliveryButton = Ext.create ("Ext.Button",{
		cls : 'Common_Btn',
		textAlign : 'center',
		text : '工具交付',
		handler : deliveryBtn
	});
	// 导出
	var exportButton = Ext.create("Ext.Button", {
		cls: 'Common_Btn',
		textAlign: 'center',
		text: '导出',
		handler: exportList
	});
	//数据导出
	function exportList() {
		Ext.MessageBox.confirm(
			'提示',
			"是否导出全部数据!",
			function (btn) {
				if (btn == 'no') {
					return;
				}
				if (btn == 'yes') {
					//应用系统
					var isystemIds = csystemCom.getValue()==null?'':csystemCom.getValue();
					//一级分类
					var ionetypeId = coneTypeCom.getValue()==null?'':coneTypeCom.getValue();
					//二级分类
					var itwotypeId = ctwoTypeCom.getValue()==null?'':ctwoTypeCom.getValue();
					//三级分类
					var ithreetypeId = cthreeTypeCom.getValue()==null?'':cthreeTypeCom.getValue();
					//工具状态
					var itoolStatusId = cstatusCom.getValue()==null?'':cstatusCom.getValue();
					//是否高危
					var isHighRisk = isHighRiskSea.getValue()==null?'':isHighRiskSea.getValue();
					//工具编号
					var itoolCode = ccodeField.getValue()==null?'':ccodeField.getValue();
					//工具名称
					var itoolName = cnameField.getValue()==null?'':cnameField.getValue();
					//脚本名称
					var snameName = snameField.getValue()==null?'':snameField.getValue();
					//工具类型
					var itoolTypeId = citoolType.getValue()==null?'':citoolType.getValue();
					//接收时间
					var ireceptionTimeStart1 = ireceptionTimeStart.getRawValue();
					var ireceptionTimeEnd1 = ireceptionTimeEnd.getRawValue();
					//交付状态
					var ideliveryStatus = cdeliveryCom.getValue()==null?'':cdeliveryCom.getValue();
					var url= 'toolboxInfo/exportToolsEditExecList.do?isystemId='+isystemIds+'&ionetypeId='+ionetypeId+'&itwotypeId='+itwotypeId+'&ithreetypeId='+ithreetypeId
						+'&itoolCode='+itoolCode+'&itoolName='+itoolName+'&itoolStatusId='+itoolStatusId+'&itoolType='+itoolTypeId+"&ideliveryStatus="+ideliveryStatus
						+'&ireceptionTimeStart='+ireceptionTimeStart1+'&ireceptionTimeEnd='+ireceptionTimeEnd1+'&isHighRisk='+isHighRisk
						+'&itoolScriptName='+snameName;
					console.log(url);
					window.location.href = url;
				}
			});
	}
	// 应用系统下拉框
	Ext.define ('csystemModel',{
		extend : 'Ext.data.Model',
		fields : [
		{
			name : 'iid',
			type : 'long'
		},
		{
			name : 'iname',
		    type : 'string'
		}]
	});
	var csystemStore = Ext.create ('Ext.data.Store',{
		autoLoad : true,
		model : 'csystemModel',
		proxy :
		{
			type : 'ajax',
			url : 'toolboxInfo/toolboxgetsystemlist.do',
			reader :
			{
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var csystemCom = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择应用系统--',
		typeAhead: true,  
	    triggerAction: 'all',  
		labelWidth : 65,
		labelAlign : 'right',
		store : csystemStore,
		width : 220,
		fieldLabel: '应用系统',
		displayField : 'iname',
	    valueField : 'iid',
		name : 'isystemId',
		queryMode : 'local',
		padding : '15 10 0 5',
		anyMatch : true,
		forceSelection:true,
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            },
			beforequery : function (e)
			{
				var combo = e.combo;
				if (!e.forceAll)
				{
					var value = e.query;
					combo.store.filterBy (function (record, id)
					{
						var text = record.get (combo.displayField);
						return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					});
					combo.expand ();
					return false;
				}
			}
        }
	});
	// 工具分类下拉框
	Ext.define ('ctypeModel',{
		extend : 'Ext.data.Model',
		fields : [
		{
			name : 'iid',
			type : 'long'
		},
		{
			name : 'ilevelname',
		    type : 'string'
		}]
	});
	var coneTypeStore = Ext.create ('Ext.data.Store',{
		autoLoad : true,
		model : 'ctypeModel',
		proxy :
		{
			type : 'ajax',
			url : 'typemanagementInfo/getTypeManagementListByParentId.do',
			reader :
			{
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var ctwoTypeStore = Ext.create ('Ext.data.Store',{
		autoLoad : false,
		model : 'ctypeModel',
		proxy :
		{
			type : 'ajax',
			url : 'typemanagementInfo/getTypeManagementListByParentId.do',
			reader :
			{
				type : 'json',
				root : 'dataList'
			}
		}
	});
	var cthreeTypeStore = Ext.create ('Ext.data.Store',{
		autoLoad : false,
		model : 'ctypeModel',
		proxy :
		{
			type : 'ajax',
			url : 'typemanagementInfo/getTypeManagementListByParentId.do',
			reader :
			{
				type : 'json',
				root : 'dataList'
			}
		}
	});

	Ext.define ('ctoolTypeModel',{
		extend : 'Ext.data.Model',
		fields : [
			{
				name : 'iid',
				type : 'long'
			},
			{
				name : 'itypename',
				type : 'string'
			}]
	});
	var citoolTypeStore = Ext.create ('Ext.data.Store',{
		autoLoad : true,
		model : 'ctoolTypeModel',
		proxy :
			{
				type : 'ajax',
				url : 'toolboxInfo/toolboxgettooltypelist.do?sendType=combination',
				reader :
					{
						type : 'json',
						root : 'dataList'
					}
			}
	});
	var citoolType = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择工具类型--',
		labelWidth : 65,
		labelAlign : 'right',
		store : citoolTypeStore,
		editable : false,
		width : 220,
		fieldLabel: '工具类型',
		displayField : 'itypename',
		valueField : 'iid',
		name : 'itoolType',
		queryMode : 'local',
		padding : '5 0 5 5',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            }
		}
	});

	coneTypeStore.on ('beforeload', function (store, options){
		Ext.apply(coneTypeStore.proxy.extraParams, {iparentId : 0});
	});
	var coneTypeCom = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择一级分类--',
		typeAhead: true,  
	    triggerAction: 'all',  
		labelWidth : 65,
		labelAlign : 'right',
		store : coneTypeStore,
		width : 220,
		fieldLabel: '一级分类',
		displayField : 'ilevelname',
	    valueField : 'iid',
		name : 'ionetypeId',
		padding : '15 10 0 5',
		queryMode : 'local',
		forceSelection:true,
		listeners: {
			'select': function(){ 
				ctwoTypeCom.setValue('');
				cthreeTypeCom.setValue('');
				ctwoTypeStore.load({
					params : {
						iparentId : this.value
					}
				});
			},
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            },
			beforequery : function (e)
			{
				var combo = e.combo;
				if (!e.forceAll)
				{
					var value = e.query;
					combo.store.filterBy (function (record, id)
					{
						var text = record.get (combo.displayField);
						return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					});
					combo.expand ();
					return false;
				}
			}
		}
	});
	var ctwoTypeCom = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择二级分类--',
		typeAhead: true,  
	    triggerAction: 'all',  
		labelWidth : 65,
		labelAlign : 'right',
		store : ctwoTypeStore,
		width : 220,
		fieldLabel: '二级分类',
		displayField : 'ilevelname',
	    valueField : 'iid',
		name : 'itwotypeId',
		padding : '15 10 0 5',
		queryMode : 'local',
		forceSelection:true,
		listeners: {
			'select': function(){ 
				cthreeTypeCom.setValue('');
				cthreeTypeStore.load({
					params : {
						iparentId : this.value
					}
				});
			},
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            },
			beforequery : function (e)
			{
				var combo = e.combo;
				if (!e.forceAll)
				{
					var value = e.query;
					combo.store.filterBy (function (record, id)
					{
						var text = record.get (combo.displayField);
						return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					});
					combo.expand ();
					return false;
				}
			}
		}
	});
	var cthreeTypeCom = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择三级分类--',
		typeAhead: true,  
	    triggerAction: 'all',  
		labelWidth : 65,
		labelAlign : 'right',
		store : cthreeTypeStore,
		width : 220,
		fieldLabel: '三级分类',
		displayField : 'ilevelname',
	    valueField : 'iid',
		name : 'ihreetypeId',
		queryMode : 'local',
		padding : '15 10 0 5',
		forceSelection:true,
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            },
			beforequery : function (e)
			{
				var combo = e.combo;
				if (!e.forceAll)
				{
					var value = e.query;
					combo.store.filterBy (function (record, id)
					{
						var text = record.get (combo.displayField);
						return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
					});
					combo.expand ();
					return false;
				}
			}
		}
	});
	
	// 工具编号输入框
	var ccodeField = Ext.create ('Ext.form.TextField',
	{
		id : 'itoolcode1',
		padding : '10 10 0 5',
		labelAlign : 'right',
	    fieldLabel : "工具编号",
	    labelWidth : 65,
	    width: 220,
	    maxLength : 1000,
	    emptyText : '--请输入工具编号--',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            }
		}
	});
	// 工具名称输入框
	var cnameField = Ext.create ('Ext.form.TextField',
	{
		id : 'itoolname1',
		padding : '10 10 0 5',
		labelAlign : 'right',
	    fieldLabel : "工具名称",
	    labelWidth : 65,
	    width: 220,
	    maxLength : 1000,
	    emptyText : '--请输入工具名称--',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            }
		}
	});
	// 脚本名称输入框
	var snameField = Ext.create ('Ext.form.TextField',
		{
			id : 'scriptidcom',
			padding : '10 10 0 5',
			labelAlign : 'right',
			fieldLabel : "脚本名称",
			labelWidth : 65,
			width: 220,
			maxLength : 1000,
			emptyText : '--请输入脚本名称--',
			listeners: {
				specialkey: function(field, e){
					if (e.getKey() == e.ENTER) {
						cqueryWhere();
					}
				}
			}
		});
	// 工具状态下拉框
	Ext.define ('cstatusModel',{
		extend : 'Ext.data.Model',
		fields : [
		{
			name : 'iid',
			type : 'long'
		},
		{
			name : 'istatusName',
		    type : 'string'
		}]
	});
	var cstatusStore = Ext.create ('Ext.data.Store',{
		autoLoad : true,
		model : 'cstatusModel',
		proxy :
		{
			type : 'ajax',
			url : 'toolboxgetstatuslist.do',
			reader :
			{
				type : 'json',
				root : 'dataList'
			}
		}
	});
    //是否高危
    var isHighRiskSea = Ext.create ('Ext.form.ComboBox',{
        emptyText : '--请选择是否高危--',
        labelWidth : 65,
        labelAlign : 'right',
        store : new Ext.data.ArrayStore (
            {
                fields : [
                    'hrId', 'hrValue'
                ],
                data : [
                    [
                        "1", "是"
                    ],[
                        "0", "否"
                    ]
                ]
            }),
        editable : false,
        width : 220,
        fieldLabel: '是否高危',
        displayField : 'hrValue',
        valueField : 'hrId',
        // name : 'isHighRisk',
        padding : '15 10 0 5',
        listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                    cqueryWhere();
                }
            }
        }
    });
	var cstatusCom = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择工具状态--',
		labelWidth : 65,
		labelAlign : 'right',
		store : cstatusStore,
		editable : false,
		width : 220,
		fieldLabel: '工具状态',
		displayField : 'istatusName',
	    valueField : 'iid',
		name : 'istatusId',
		queryMode : 'local',
		padding : '15 10 0 5',
		listeners: {
            specialkey: function(field, e){
                if (e.getKey() == e.ENTER) {
                	cqueryWhere();
                }
            }
		}
	});
	// 交付状态下拉框
	var cdeliveryStore = Ext.create ('Ext.data.Store',{
		fields : [ 'iid', 'ideliveryStatusName' ],
		data : [ {
			"iid" : "0",
			"ideliveryStatusName" : "未交付"
		}, {
			"iid" : "1",
			"ideliveryStatusName" : "待接收"
		}, {
			"iid" : "2",
			"ideliveryStatusName" : "已交付"
		}, {
			"iid" : "3",
			"ideliveryStatusName" : "已退回"
		}]
	});
	//交付状态
	var cdeliveryCom = Ext.create ('Ext.form.ComboBox',{
		emptyText : '--请选择交付状态--',
		labelWidth : 65,
		labelAlign : 'right',
		store : cdeliveryStore,
		editable : false,
		width : 220,
		fieldLabel: '交付状态',
		displayField : 'ideliveryStatusName',
		valueField : 'iid',
		name : 'ideliveryStatusId',
		queryMode : 'local',
		padding : '10 10 0 5',
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					cqueryWhere();
				}
			}
		}
	});
	var ireceptionTimeStart = Ext.create('Go.form.field.DateTime', {
		labelWidth: 65,
		labelAlign: 'right',
		width: 235,
		format: 'Y-m-d H:i:s',
		fieldLabel: '接收时间',
		emptyText: '开始时间',
		//此为之前默认开始时间设置
		//value:startTimeQueryReturn,
		//value:pfFlag?default_startTime:startTimeQueryReturn,
		name: 'params[ireceptionTimeStart]',
		padding: '10 10 0 15',
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					cqueryWhere();
				}
			}
		}
	});
	var ireceptionTimeEnd = Ext.create('Go.form.field.DateTime', {
		labelWidth: 25,
		labelAlign: 'right',
		width: 195,
		format: 'Y-m-d H:i:s',
		fieldLabel: '至',
		emptyText: '结束时间',
		//此为之前默认开始时间设置
		//value:startTimeQueryReturn,
		//value:pfFlag?default_startTime:startTimeQueryReturn,
		name: 'params[ireceptionTimeEnd]',
		padding: '10 10 0 15',
		listeners: {
			specialkey: function(field, e){
				if (e.getKey() == e.ENTER) {
					cqueryWhere();
				}
			}
		}
	});
	//操作系统下拉框
	// var nOsTypeStore = Ext.create('Ext.data.Store', {
	// 	fields : ['nOsType'],
	// 	data : [ {"nOsType" : "windows"},{"nOsType" : "linux"},{"nOsType" : "hpux"},{"nOsType" : "aix"}]
	// });
	// var nOsTypeCom = Ext.create ('Ext.form.ComboBox',{
	// 	emptyText : '--请选择操作系统--',
	// 	labelWidth : 65,
	// 	labelAlign : 'right',
	// 	store : nOsTypeStore,
	// 	editable : false,
	// 	width : 220,
	// 	fieldLabel: '操作系统',
	// 	displayField : 'nOsType',
	//     valueField : 'nOsType',
	// 	name : 'nOsType',
	// 	queryMode : 'local',
	// 	padding : '0 10 0 5'
	// });
	var itemsBtn=[];
		itemsBtn=[{
			layout: 'column',
			border: false,
			items: [csystemCom,coneTypeCom,ctwoTypeCom,cthreeTypeCom,cstatusCom,isHighRiskSea]
		}, {
			layout: 'column',
			border: false,
			items: [ccodeField,cnameField,snameField,citoolType,ireceptionTimeStart,ireceptionTimeEnd, ]
		}, {
			layout: 'column',
			border: false,
			items: [cdeliveryCom,cqueryButton, cresetButton,exportButton,caddButton,ceditButton,cdeleteButton, csyncButton,cselfButton,cbatchButton,cwithdrawButton,cdeliveryButton]
		}];
	var cform = Ext.create('Ext.form.FormPanel', {
		buttonAlign : 'right', // 按钮对齐方式
		dockedItems : [{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			dock : 'top',
			layout:'column',
		    border : false,
			items : [csystemCom,coneTypeCom,ctwoTypeCom,cthreeTypeCom,cstatusCom,isHighRiskSea]
		},{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [ccodeField,cnameField,citoolType,ireceptionTimeStart,ireceptionTimeEnd, ]
		},{
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items : [cdeliveryCom,cqueryButton, cresetButton,exportButton,caddButton,ceditButton,cdeleteButton, csyncButton,cselfButton,cbatchButton,cwithdrawButton,cdeliveryButton]
		}]
	});
	
	/*queryForm = Ext.create('Ext.form.Panel', {
		region : 'north',
		collapsible : true,//可收缩
		collapsed : false,//默认收缩
		border: false,
		layout: 'form',
		items : [cform]
	});*/
	queryForm = Ext.create('Ext.ux.ideal.form.Panel', {
		region: 'north',
		layout: 'anchor',
		collapsible: true,
		collapsed: false,
		buttonAlign: 'right',
		margin: '0 0 0 0',
		border: false,
		baseCls: 'customize_gray_back',
		//iselect: false,
		items: itemsBtn
	});


	var ccolumns = [
        {
        	text : '当前登录用户id',
			dataIndex : 'loginUserId',
			hidden:true,
			width : 50
		},{
        	text : '创建用户id',
			dataIndex : 'iuserType',
			hidden:true,
			width : 50
		},{
        	text : '主键',
			dataIndex : 'iid',
			hidden:true,
			width : 50
		},{
        	text : '工具编号',
			dataIndex : 'itoolCode',
			width : 150,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},{
        	text : '工具名称',
			dataIndex : 'itoolName',
			width : 150,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},{
			text : '应用系统id',
			dataIndex : 'isystemId',
			hidden:true,
			width : 80
		},{
			text : '应用系统',
			dataIndex : 'isystem',
			width : 120,
			renderer : function(value, metadata) {
				metadata.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		// },{
		// 	text : '操作系统',
		// 	dataIndex : 'nOsType',
		// 	width : 120
		},{
			text : '一级分类id',
			dataIndex : 'ionetypeId',
			hidden:true,
			width : 80
		},{
			text : '一级分类',
			dataIndex : 'ionetype',
			width : 120
		},{
			text : '二级分类id',
			dataIndex : 'itwotypeId',
			hidden:true,
			width : 80
		},{
			text : '二级分类',
			dataIndex : 'itwotype',
			width : 120
		},{
			text : '三级分类id',
			dataIndex : 'ithreetypeId',
			hidden:true,
			width : 80
		},{
			text : '三级分类',
			dataIndex : 'ithreetype',
			width : 100
		},{
            text : '是否高危',
            dataIndex : 'isHighRisk',
            hidden:true,
            width : 80
        },{
			text : '创建时间',
			dataIndex : 'icreatetime',
			width : 160
		},{
			text : '工具状态id',
			dataIndex : 'itoolStatusId',
			hidden:true,
			width : 80
		},{
			text : '工具状态',
			dataIndex : 'itoolStatus',
			width : 80
		},{
			text : '工具类型',
			dataIndex : 'itoolType',
			width : 80
		},{
			text : '工具类型ID',
			dataIndex : 'itoolTypeId',
			width : 80,
			hidden: true
		},{
			text : '脚本名称',
			dataIndex : 'itoolScriptName',
			width : 80,
			hidden: true
		},{
			text : '创建人',
			dataIndex : 'creater',
			width : 100
		},{
			text : '交付状态',
			dataIndex : 'ideliveryStatus',
			width : 100,
			renderer: function (value, metadata, record, rowIndex, colIndex, store, view) {
				var cls = "";
				if (value == '0') {
					cls = "未交付";
				} else if (value == '1') {
					cls = "待接收";
				} else if (value == '2') {
					cls = "已交付";
				} else if (value == '3') {
					cls = "已退回";
				}
				return cls;
			}
		},{
			text : '接收时间',
			dataIndex : 'ireceptionTime',
			width : 150
		},{
			text : '修改时间',
			dataIndex : 'iupdateTime',
			width : 150
		},{
			text: '操作',
			width : 160,
			renderer:function(value,p,record,rowIndex){
				const iid = record.get('iid');
				var itoolType = record.get('itoolType');
				var iuserType = record.get('iuserType');
				var loginUserId = record.get('loginUserId');
				var isystemId = record.get('isystemId');
				var itoolStatusId = record.get('itoolStatusId');
				var itoolTypeId = record.get('itoolTypeId');
				var itoolName = record.get('itoolName');
				// return '<span class="switch_span"><a href="javascript:void(0)" onclick="detail('+iid+',\''+itoolType+'\')"><img src="images/monitor_bg.png"  align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;</span>';

				return '<span class="switch_span"><a href="javascript:void(0)" onclick="detail2('+iid+',\''+itoolType+'\',\''+escape(itoolName)+'\')"><img src="images/monitor_bg.png"  align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;</span>'+
					'&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="firstDescribe('+iid+',\''+iuserType+'\',\''+loginUserId+'\','+isystemId+','+itoolStatusId+',\''+itoolType+'\','+itoolTypeId+')">一线描述</a>&nbsp;&nbsp;</span>';

				// return '<span class="switch_span"><a href="javascript:void(0)" onclick="detail2('+iid+',\''+itoolType+'\')"><img src="images/monitor_bg.png"  align="absmiddle" class="monitor_search"></img>&nbsp;详情</a>&nbsp;&nbsp;</span>' +
				// 	'&nbsp;&nbsp;<span class="switch_span"><a href="javascript:void(0)" onclick="firstDescribe('+iid+',\''+iuserType+'\',\''+loginUserId+'\','+isystemId+','+itoolStatusId+',\''+itoolType+'\','+itoolTypeId+')">一线描述</a>&nbsp;&nbsp;</span>';
			}
		},{
			text: '编辑',
			width : 80,
			renderer:function(value,p,record,rowIndex){
				const iid = record.get('iid');
				var iuserType = record.get('iuserType');
				var loginUserId = record.get('loginUserId');
				var isystemId = record.get('isystemId');
				var itoolStatusId = record.get('itoolStatusId');
				var itoolType = record.get('itoolType');
				var itoolName = record.get('itoolName');
				return '<span class="switch_span"><a href="javascript:void(0)" onclick="editCheck('+iid+',\''+iuserType+'\',\''+loginUserId+'\','+isystemId+','+itoolStatusId+',\''+itoolType+'\')">&nbsp;编辑</a>&nbsp;&nbsp;</span>';
			}
		}
	];
	
	/*var cgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		id:'createtoolsgird',
		border:true,
		region: 'center',
		cls:'customize_panel_back',
		forceFit: true,
		autoScroll:false,
	    store: cstore,	
	    columns: ccolumns,
	    columnLines : true,
	    cellTip : true,
	    ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    loadMask: {
	        msg: ' 数据加载中，请稍等 ',
	        removeMask: true
	    },
	    selModel: Ext.create('Ext.selection.CheckboxModel', {
	        //checkOnly: true
	    })
	});*/
	var cgrid = Ext.create('Ext.ux.ideal.grid.Panel', {
		id: 'createtoolsgird',
		border: true,
		region: 'center',
		cls: 'customize_panel_back',
		ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		forceFit: true,
		autoScroll: false,
		store: cstore,
		columns: ccolumns,
		viewConfig : {
			getRowClass: function (record, rowIndex, p, ds) {
				var cls = '';
				var isHighRisk = record.get('isHighRisk');
				if ("是" == isHighRisk) {
					cls = 'cgrid-row-red';
				}
				return cls;
			}
		},
		columnLines: true,
		cellTip: true,
		selType: 'checkboxmodel',
		loadMask: {
			msg: ' 数据加载中，请稍等 ',
			removeMask: true
		},
		selModel: Ext.create('Ext.selection.CheckboxModel', {
			injectCheckbox: 1,
			checkOnly: false,
			allowDeselect: true,
			enableKeyNav: true,
			listeners: {
				selectionchange: function (selModel, selectedRecords) {
					// 在选择发生变化时触发此事件
					var selectedCount = selectedRecords.length;
					// 调用函数来更新文本字段的值
					updateTextValue(selectedCount);
					console.log($('.lb20231107').css('marginTop'),selectedCount,3333333333)
					if(selectedCount!=0){
						console.log(1, $('.lb20231107 tbody tr .x-form-display-field-body div'))
						$('.lb20231107 tbody tr .x-form-display-field-body div').css('marginTop','5px')
					}
					if(selectedCount==0){console.log(12)
						$('.lb20231107 tbody tr .x-form-display-field-body div').css('marginTop','10px')
					}
					if (selectedCount > 0) {
						// 显示按钮
						cacceptButton.setVisible(true);
						creturnButton.setVisible(true);
						cbatchButton.setVisible(true);
						cdeliveryButton.setVisible(true);
					} else if (selectedCount === 0) {
						// 隐藏按钮
						cacceptButton.setVisible(false);
						creturnButton.setVisible(false);
						cbatchButton.setVisible(false);
						cdeliveryButton.setVisible(false);
					}
					var record = cgrid.getSelectionModel().getSelection();
					if (record.length > 1 ||  record.length == 0 ) {
						Ext.getCmp('editComButton').disable();
					}
					else {
						if (record[0].data.itoolType == '组合工具') {
							Ext.getCmp('editComButton').enable();
						} else {
							Ext.getCmp('editComButton').disable();
						}
					}
				},
				'deselect': function () {
					var record = cgrid.getSelectionModel().getSelection();
					if (record.length > 1) {
						Ext.getCmp('editComButton').disable();
					} else if (record.length == 1) {
						if (record[0].data.itoolType == '组合工具') {
							Ext.getCmp('editComButton').enable();
						} else {
							Ext.getCmp('editComButton').disable();
						}
					} else {
						Ext.getCmp('editComButton').disable();
					}
				}

			}
		})
	});
	// 判断删除按钮是否可用
//	grid.getSelectionModel ().on ('selectionchange', function (selModel, selections)
//	{
//		cdeleteButton.setDisabled (selections.length === 0);
//	});
	if(isTabSwitch){
		var cmainPanel = Ext.create ('Ext.panel.Panel',{
			renderTo : "createtoolsDiv"+createtoolsnow,
			layout : 'border',
			width : contentPanel.getWidth (),
			height : contentPanel.getHeight ()- modelHeigth,
			border : true,
			bodyPadding : grid_margin,
			bodyCls: 'service_platform_bodybg',
			items : [queryForm,cgrid]
		});
	}else{
		var cmainPanel = Ext.create ('Ext.panel.Panel',{
			renderTo : "createtoolsDiv",
			layout : 'border',
			width : contentPanel.getWidth (),
			height : contentPanel.getHeight ()- modelHeigth,
			border : true,
			bodyPadding : grid_margin,
			bodyCls: 'service_platform_bodybg',
			items : [queryForm,cgrid]
		});
	}

	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		cmainPanel.setWidth(contentPanel.getWidth ());
		cmainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (cmainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	function cqueryWhere (){
		//cqueryButton.setDisabled(true);
		cgrid.ipage.moveFirst();
		// store.load();
	}
	
	function cresetWhere (){
		csystemCom.setValue(null);
		coneTypeCom.setValue(null);
		ctwoTypeCom.setValue(null);
		ctwoTypeStore.removeAll();
		citoolType.setValue('');
		cthreeTypeCom.setValue(null);
		cthreeTypeStore.removeAll();
		ccodeField.setValue('');
		cnameField.setValue('');
		cstatusCom.setValue('');
		snameField.setValue('');
		cdeliveryCom.setValue('');
		ireceptionTimeStart.setValue('');
		ireceptionTimeEnd.setValue('');
        isHighRiskSea.setValue('');
		// nOsTypeCom.setValue('');
	}

	function add(type){
		let title = "";
		let width = contentPanel.getWidth()*0.6;
		let height = contentPanel.getHeight();
		var btn=[];
		if(type==2){
			title = "新增组合工具";
			width = contentPanel.getWidth();
			btn=[ {
					xtype : 'button',
					textAlign : 'center',
					cls : 'Common_Btn',
					text : '保存',
					handler : function(){
						save();
					}
				},
				{
					xtype: 'button',
					textAlign: 'center',
					cls: 'Common_Btn',
					text: '提交',
					handler: function () {
						submit();
					}
				}
			];
		}else if(type==3){
			title = "新增脚本工具";
		}else{
			title = "新增描述工具";
		}
		let bodyStyle='overflow-x:hidden;overflow-y:scroll';
		if(type==2){
			bodyStyle='overflow-x:hidden;overflow-y:hidden';
		}
		openCreatetoolsAddWindowEdit = Ext.create('Ext.window.Window', {
			id:'addWindow',
			title : title,
			// autoScroll : true,
			bodyStyle :bodyStyle,
			modal : true,
			closeAction : 'destroy',
			buttonAlign : 'center',
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			width : width,
			height : height,
			listeners : {
				'show':function(){
					Ext.EventManager.on(window,'beforeunload',function(){
						//浏览器刷新或关闭执行
						if(type==2){
							//delFlow();
						}

					})
				},
				// 'close':{fn:delFlow}
				'close':function () {
					var win1 = Ext.getCmp('jbIdAddOpenWindow');
                    if (win1) {win1.destroy();}
					if(type==2){
						//delFlow();
					}
				}
				},
			loader : {
				url : "toolboxInfo/toToolsAdd.do",
				params : {
					type : type
				},
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			},
			buttons : btn

		}).show();
	}

	//监听右上角关闭事件
	/*function delFlow(){
		Ext.Ajax.request({
			url : 'combobox/deleteFlow.do',
			method : 'POST',
			params : {
				toolsId:IID
			},
			success : function(response, request) {

			}
		});
	}*/

	//编辑
	function edit(){
		var records = cgrid.getSelectionModel().getSelection();
		console.log(records);
		if(records.length!=1){
		    Ext.Msg.alert('提示', "请选择一条数据");
		}else{
			//判断当前操作用户是否为该“公共”工具的创建人，是，则可操作
			var iuserType = records[0].data.iuserType;
			var loginUserId = records[0].data.loginUserId;
			var isystemId = records[0].data.isystemId;
			let itoolType = records[0].data.itoolType;
			var itoolName = records[0].data.itoolName;
			if("-1" == isystemId && loginUserId != iuserType && ggtools != 'true'){
                Ext.Msg.alert('提示', "该工具只有创建人可以编辑");
                return;
            }
			var iid = records[0].data.iid;
			var itoolStatusId = records[0].data.itoolStatusId;
			if(itoolStatusId=='1'){
				Ext.Msg.alert('提示', "该状态无法编辑");
				return;
			}

				let title = "";
				let width = contentPanel.getWidth() * 0.6;
				let height = contentPanel.getHeight();
				var btn = [];
				if (itoolType == "组合工具") {
					title = "编辑组合工具";
					width = contentPanel.getWidth();
					btn = [{
						xtype: 'button',
						textAlign: 'center',
						cls: 'Common_Btn',
						text: '保存',
						handler: function () {
							save();
						}
					},
						{
							xtype: 'button',
							textAlign: 'center',
							cls: 'Common_Btn',
							text: '提交',
							handler: function () {
								submit();
							}
						}
					];
				} else if (itoolType == "脚本工具") {
					title = "编辑脚本工具";
				} else {
					title = "编辑描述工具";
				}

			Ext.Ajax.request({
				url : 'toolboxInfo/getToolsResultStatus.do',
				params : {
					iid : iid
				},
				method : 'post',
				success : function(response) {
					const success = Ext.decode(response.responseText).success;
					if (success) {
						openCreatetoolsEditWindowEdit = Ext.create('Ext.window.Window', {
							id:'editWindow',
							title : title,
							// autoScroll : true,
							// bodyStyle :'overflow-x:hidden;overflow-y:scroll',
							modal : true,
							closeAction : 'destroy',
							buttonAlign : 'center',
							draggable : false,// 禁止拖动
							resizable : false,// 禁止缩放
							width : width,
							height : height,
							listeners : {
								'show':function(){
									Ext.EventManager.on(window,'beforeunload',function(){
										//浏览器刷新或关闭执行
										if(itoolType=="组合工具"){
											//delFlow();
										}

									})
								},
								// 'close':{fn:delFlow}
								'close':function () {
									var win2 = Ext.getCmp('jbIdEditOpenWindow');
									if (win2) {win2.destroy();}
									if(itoolType=="组合工具"){
										//delFlow();
									}
								}
							},
							loader : {
								url : "toolboxInfo/toToolsEdit.do",
								params : {
									iid : iid,
									itoolStatusId:itoolStatusId,
									itoolType:itoolType
								},
								autoLoad : true,
								autoDestroy : true,
								scripts : true
							},
							buttons : btn

						}).show();
					} else {
						var message = Ext.decode(response.responseText).message;
						Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
					}
				},
				failure : function(result, request) {
					secureFilterRs(result,'操作失败！');
				}
			});

		}
	}

	//删除
	function remove(){
		const m = cgrid.getSelectionModel().getSelection();
		if (m.length == 0){
			Ext.Msg.alert ('提示', "请先选择您要删除的数据");
			return;
		}else{
			Ext.Msg.confirm("请确认","是否确认删除选中数据？",
			function(button, text) {
				if (button == "yes") {
					var ids = [];
					Ext.Array.each(m, function(record) {
						const iid = record.get('iid');
						if(record.get('itoolStatusId')!=0&&record.get('itoolStatusId')!=5){
							Ext.Msg.alert ('提示', "仅能删除状态为草稿或者已修改的数据");
							return;
						}
						if (iid != "" && null != iid&&iid>0) {
							ids.push(iid);
						}
					});
					if (ids.length <= 0) {
						return;
					}
					Ext.Ajax.request({
						url : 'comboboxInfo/removeComboTools.do',
						params : {
							ids : ids.join(',')
						},
						method : 'POST',
						success : function(response, opts) {
							const success = Ext.decode(response.responseText).success;
							var msg= Ext.decode(response.responseText).message;
							cstore.reload();
							if (success) {
								Ext.Msg.alert('提示',msg);
							} else {
								Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
							}
						},
						failure : function(result, request) {
							cstore.reload();
							secureFilterRs(result,'操作失败！');
						}
					});
				}
		});

		}
	}

	//脚本同步按钮，触发定时任务逻辑
	function syncBtn(){
		Ext.Msg.confirm("请确认","是否确认脚本同步？",
			function(button, text) {
				if (button == "yes") {
					Ext.MessageBox.wait ("脚本同步中...", "进度条");
					Ext.Ajax.request({
						url : 'toolboxInfo/syncJBList.do',
						params : {},
						method : 'POST',
						success : function(response, opts) {
							cstore.reload();
							const success = Ext.decode(response.responseText).success;
							const msg = Ext.decode(response.responseText).msg;
							Ext.MessageBox.hide();
							if (success) {
								Ext.Msg.alert('提示',msg);
							} else {
								Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
							}
						},
						failure : function(result, request) {
							cstore.reload();
							secureFilterRs(result,'操作失败！');
						}
					});
				}
			});
	}

	//自愈策略
	function selfFuction(){
		var m = cgrid.getSelectionModel().getSelection();
		if (m.length == 0){
			Ext.Msg.alert ('提示', "请先选择组合工具!");
			return;
		}
		if(m.length>1){
			Ext.Msg.alert ('提示', "请先选择一条组合工具！");
			return;
		}
		var toolId;
		var tName;
		var itoolStatus;
		var ionetype;
		var itwotype;
		var systemName;
		var systemNameId;
		var itoolType;
		Ext.Array.each(m, function(record) {
			toolId = record.get('iid');
			itoolStatus=record.get('itoolStatus');
			ionetype=record.get('ionetype');
			itwotype=record.get('itwotype');
			tName=record.get('itoolName');
			systemName=record.get('isystem');
			systemNameId=record.get('isystemId');
			itoolType = record.get('itoolType');
		});

		if(itoolStatus!='生效' || itoolType!='组合工具'){
			Ext.Msg.alert('提示','只有生效的组合工具可配置自愈！')
			return;
		}
		if(ionetype!='应用'&&itwotype!='自愈类'){
			Ext.Msg.alert('提示','只有应用-自愈类（一级分类-二级分类）类型工具可配置自愈策略');
			return;
		}
		var openSelfViewWindow = Ext.create('Ext.window.Window', {
			id:'selfWindow',
			title :tName+ ' 自愈策略',
			autoScroll : true,
			modal : true,
			closeAction : 'destroy',
			buttonAlign : 'center',
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			width : contentPanel.getWidth()*0.6,
			height : contentPanel.getHeight()*0.71,
			loader : {
				url : "everbrightbankToolInfo/goSelfView.do",
				params : {
					toolId : toolId,
					systemName : systemName,
					systemNameId : systemNameId
				},
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			}
		}).show();
	}

	//批量提交
	function batchBtn() {
		const m = cgrid.getSelectionModel().getSelection();
		if (m.length == 0){
			Ext.Msg.alert ('提示', "请先选择您要批量提交的数据");
			return;
		}else{
			var ids = [];
			var falg=true;
			Ext.Array.each(m, function(record) {
				const iid = record.get('iid');
				if(record.get('itoolStatusId')==1 || record.get('itoolStatusId')==2){
					falg=false;
					Ext.Msg.alert ('提示', "只能提交草稿、重审、待确认、已修改状态的数据");
					return;
				}
				if (iid != "" && null != iid&&iid>0) {
					ids.push(iid);
				}
			});
			if(falg){
				openBatchWindowEdit = Ext.create('Ext.window.Window', {
					id:'batchWindow',
					title : '批量提交',
					autoScroll : true,
					modal : true,
					closeAction : 'destroy',
					buttonAlign : 'center',
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					width : contentPanel.getWidth()*0.36,
					height : contentPanel.getHeight()*0.3,
					loader : {
						url : "toolboxInfo/getOpenBatch.do",
						params : {
							ids : ids.join(','),
							type:1
						},
						autoLoad : true,
						autoDestroy : true,
						scripts : true
					}

				}).show();
			}

		}
	}

	/**
	 * 撤回
	 */
	function withdrawBtn(){
		const m = cgrid.getSelectionModel().getSelection();
		if (m.length == 0){
			Ext.Msg.alert ('提示', "请先选择您要撤回的数据");
			return;
		}else{
			var ids = [];
			var falg=true;
			Ext.Array.each(m, function(record) {
				const iid = record.get('iid');
				if(record.get('itoolStatusId')!=1){
					falg=false;
					Ext.Msg.alert ('提示', "只能撤回待审批状态的数据");
					return;
				}
				if (iid != "" && null != iid&&iid>0) {
					ids.push(iid);
				}
			});


			if(falg){

				Ext.Ajax.request({
					url : 'toolboxInfo/queryStateTools.do',
					params : {
						ids : ids.join(','),
						type : 1
					},
					method : 'POST',
					success : function(response, opts) {
						const success = Ext.decode(response.responseText).success;
						const msg= Ext.decode(response.responseText).message;
						if (!success) {
							Ext.Msg.alert('提示',msg);
						} else {
							Ext.Msg.confirm("请确认","确定要撤回选中的工具吗？",
								function(button, text) {
									if (button == "yes") {
										Ext.Ajax.request({
											url : 'toolboxInfo/updateWithdrawTools.do',
											params : {
												ids : ids.join(',')
											},
											method : 'POST',
											success : function(response, opts) {
												const success = Ext.decode(response.responseText).success;
												var msg= Ext.decode(response.responseText).message;
												cstore.reload();
												if (success) {
													Ext.Msg.alert('提示',msg);
												} else {
													Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
												}
											},
											failure : function(result, request) {
												cstore.reload();
												secureFilterRs(result,'操作失败！');
											}
										});
									}
								});
						}
					},
					failure : function(result, request) {
						cstore.reload();
						secureFilterRs(result,'操作失败！');
					}
				});


			}

		}
	}
	//工具交付
	function deliveryBtn(){
		var m = cgrid.getSelectionModel().getSelection();
		if (m.length == 0){
			Ext.Msg.alert ('提示', "请先选择您要交付的数据!");
			return;
		}else{
			Ext.Msg.confirm("提示","是否将选中工具交付给一线人员？",
				function(button, text) {
					if (button == "yes") {
						var ids = [];
						var flag=true;
						Ext.Array.each(m, function(record) {
							const iid = record.get('iid');
							const itoolStatus = record.get('itoolStatus');
							const ideliveryStatus = record.get('ideliveryStatus');
							if(itoolStatus!='生效'|| ideliveryStatus=='1'||ideliveryStatus=='2'){
								flag=false;
								Ext.Msg.alert('提示','只有选中工具状态为生效，交付状态为未交付或已退回的工具才可交付！')
								return;
							}
							if (iid != "" && null != iid&&iid>0) {
								ids.push(iid);
							}
						});
						if (ids.length <= 0) {
							return;
						}
						if(flag){
							Ext.Ajax.request({
								url : 'comboboxInfo/deliveryComboTools.do',
								params : {
									ids : ids.join(','),
									status : 1
								},
								method : 'POST',
								success : function(response, opts) {
									const success = Ext.decode(response.responseText).success;
									var msg= Ext.decode(response.responseText).message;
									cstore.reload();
									if (success) {
										Ext.Msg.alert('提示',"交付成功！");
									} else {
										Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
									}
								},
								failure : function(result, request) {
									cstore.reload();
									secureFilterRs(result,'操作失败！');
								}
							});
						}

					}
				});

		}
	}

});

/**
 * 详情
 * @param iid
 */
function detail2(iid,itoolType,itoolName){

	if(isTabSwitch) {
		menuClick_gjx(this,unescape(itoolName)+'详情',"toolboxInfo/toToolsDetail.do",iid,iid,{
			iid: iid,
			itoolType: itoolType,
			itoolStatusId: ''
		});
	}else{

		let title = "";
		let width = contentPanel.getWidth()*0.6;
		let height = contentPanel.getHeight();
		var btn=[];
		if(itoolType=="组合工具"){
			width = contentPanel.getWidth()*0.9;
			btn=[ {
				xtype : 'button',
				textAlign : 'center',
				cls : 'Common_Btn',
				text : '退出',
				handler : function(){
					openDetailWindowEdit.close();
				}
			}];
		}
		openDetailWindowEdit = Ext.create('Ext.window.Window', {
			id:'detailWindow',
			title : itoolType+'详情',
			autoScroll : true,
			bodyStyle :'overflow-x:hidden;overflow-y:scroll',
			modal : true,
			closeAction : 'destroy',
			buttonAlign : 'center',
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			width : width,
			height : height,
			loader : {
				url : "toolboxInfo/toToolsDetail.do",
				params : {
					iid : iid,
					itoolType:itoolType
				},
				autoLoad : true,
				autoDestroy : true,
				scripts : true
			},
			buttons : btn

		}).show();
	}


}

/**
 * 一线描述
 * @param iid
 * @param type
 */
function firstDescribe(iid,iuserType,loginUserId,isystemId,itoolStatusId,itoolType,itoolTypeId){
	let width = contentPanel.getWidth()*0.6;
	let height = contentPanel.getHeight();
	openYxmsEditWindowEdit = Ext.create('Ext.window.Window', {
		id:'yxmsEditWindow',
		title : '一线工具描述编辑',
		autoScroll : true,
		// bodyStyle :'overflow-x:hidden;overflow-y:scroll',
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : width,
		height : height,
		loader : {
			url : "toolboxInfo/openYxmsEdit.do",
			params : {
				iid : iid,
				itoolStatusId:itoolStatusId,
				itoolType:itoolType,
				itoolTypeId:itoolTypeId
			},
			autoLoad : true,
			autoDestroy : true,
			scripts : true
		},
		// buttons : btn

	}).show();
}




//编辑
function editCheck(iid,iuserType,loginUserId,isystemId,itoolStatusId,itoolType){
	//判断当前操作用户是否为该“公共”工具的创建人，是，则可操作
	if("-1" == isystemId && loginUserId != iuserType && ggtools != 'true'){
		Ext.Msg.alert('提示', "该工具只有创建人可以编辑");
		return;
	}
	if(itoolStatusId=='1'){
		Ext.Msg.alert('提示', "该状态无法编辑");
		return;
	}

		let width = contentPanel.getWidth()*0.6;
		let height = contentPanel.getHeight();
		var btn=[];
		if(itoolType=="组合工具"){
			 width = contentPanel.getWidth();
			btn=[ {
				xtype : 'button',
				textAlign : 'center',
				cls : 'Common_Btn',
				text : '保存',
				handler : function(){
					save();
				}
			},
				{
					xtype: 'button',
					textAlign: 'center',
					cls: 'Common_Btn',
					text: '提交',
					handler: function () {
						submit();
					}
				}
			];
		}

	Ext.Ajax.request({
		url : 'toolboxInfo/getToolsResultStatus.do',
		params : {
			iid : iid
		},
		method : 'post',
		success : function(response) {
			const success = Ext.decode(response.responseText).success;
			if (success) {
				openCreatetoolsEditWindowEdit = Ext.create('Ext.window.Window', {
					id:'editWindow',
					title : '编辑'+itoolType,
					// autoScroll : true,
					// bodyStyle :'overflow-x:hidden;overflow-y:scroll',
					modal : true,
					closeAction : 'destroy',
					buttonAlign : 'center',
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					width : width,
					height : height,
					listeners : {
						'show':function(){
							Ext.EventManager.on(window,'beforeunload',function(){
								//浏览器刷新或关闭执行
								if(itoolType=="组合工具"){
									//delRowFlow();
								}

							})
						},
						// 'close':{fn:delFlow}
						'close':function () {
							var win3 = Ext.getCmp('jbIdEditOpenWindow');
							if (win3) {win3.destroy();}
							if(itoolType=="组合工具"){
								//delRowFlow();
							}
						}
					},
					loader : {
						url : "toolboxInfo/toToolsEdit.do",
						params : {
							iid : iid,
							itoolStatusId:itoolStatusId,
							itoolType:itoolType
						},
						autoLoad : true,
						autoDestroy : true,
						scripts : true
					},
					buttons : btn

				}).show();
			} else {
				var message = Ext.decode(response.responseText).message;
				Ext.Msg.alert('提示',Ext.decode(response.responseText).message);
			}
		},
		failure : function(result, request) {
			secureFilterRs(result,'操作失败！');
		}
	});

}

/**
 * 编辑行关闭事件
 */
/*function delRowFlow() {
	Ext.Ajax.request({
		url : 'combobox/deleteFlow.do',
		method : 'POST',
		params : {
			toolsId:IID
		},
		success : function(response, request) {

		}
	});
}*/