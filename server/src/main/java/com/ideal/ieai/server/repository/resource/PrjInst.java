package com.ideal.ieai.server.repository.resource;

/**
 * <ul>
 * <li>Title: PrjInst.java</li>
 * <li>Description:工程名和实例名属性bean</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2015-9-16
 */
public class PrjInst {

	public PrjInst(String prjname, String tmpltInstId, String instanceName) {
		super();
		this.prjname = prjname;
		this.tmpltInstId = tmpltInstId;
		this.instanceName = instanceName;
	}
	
	private String prjname =null;
	private String tmpltInstId=null;
	private String instanceName=null;
	
	
	public String getPrjname() {
		return prjname;
	}
	public void setPrjname(String prjname) {
		this.prjname = prjname;
	}
	public String getTmpltInstId() {
		return tmpltInstId;
	}
	public void setTmpltInstId(String tmpltInstId) {
		this.tmpltInstId = tmpltInstId;
	}
	public String getInstanceName() {
		return instanceName;
	}
	public void setInstanceName(String instanceName) {
		this.instanceName = instanceName;
	}
}
