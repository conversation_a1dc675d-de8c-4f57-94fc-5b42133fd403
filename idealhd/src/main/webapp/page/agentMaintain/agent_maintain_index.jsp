<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv"%>
<%@ page import="com.ideal.ieai.server.ieaikernel.PersonalityEnv"%>

<script type="text/javascript" >
//Agent管理集成发送信息开关
var isOpen=<%=ServerEnv.getInstance().getBooleanConfig(Environment.SEND_WEBSERVICE_OPM_AGENT, false)%>;
//是否为兴业银行
var iscib = <%= Environment.getInstance().getBankSwitchIscib()%>;
var isght = <%= Environment.getInstance().getGhtBankSwitch()%>;
var ispf = <%= Environment.getInstance().getPfBankSwitch()%>;
var isghtorispf = <%= Environment.getInstance().getGhtBankSwitch()||Environment.getInstance().getPfBankSwitch()%>;

var isignornewagentinfo = <%= Environment.getInstance().getBooleanConfig("is.ignore.newagent.agent", false)%>;
var isignoreagentdesc = <%= Environment.getInstance().getBooleanConfig("is.ignore.agentdesc", false)%>;
var agentmaintenance = <%= Environment.getInstance().getBooleanConfig("agent.manager.show.dbconfig.switch", false)%>;
var isignoreibusiness = <%= Environment.getInstance().getBooleanConfig("is.ignore.ibusiness", false)%>;
//是否显示网段扫描开关
var isNetScanSwitch = <%= Environment.getInstance().getBooleanConfig("isNetScanSwitch", false)%>;
//是否脚本看板跳转过来的请求 
var requestFromC3Char = <%=request.getParameter("requestFromC3Char")==null?false:request.getParameter("requestFromC3Char")%>;

var agentPauseRecoverSwitch=<%=ServerEnv.getInstance().getAgentPauseRecoverSwitch()%>;
var jobSchedulingQuerySystemSwitch=<%=ServerEnv.getInstance().getJobSchedulingQuerySystemSwitch()%>;
//光大银行
var jobSchedulingDbbackSwitch=<%=Environment.getInstance().isJobschedulingDbback()%>;
var agentDnsSwitchValue=<%=PersonalityEnv.isAgentDnsSwitchValue()%>;
var suppercheck = <%=ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false)%>;

var ismustdcid = <%=ServerEnv.getInstance().getBooleanConfig(Environment.IS_MUST_DATACENTER, false)%>;

var isGetHostList=<%=ServerEnv.getInstance().getBooleanConfig("agent.zjns.autosearch", false)%>;
 
var dowonLoadLongRangeLog=<%=Environment.getInstance().isDownLoadLongRangeLog()%>; 
var iworkItemid = 0;
var daemonsSwitch=<%=ServerEnv.getInstance().getBooleanConfig("daemonsSwitch", false)%>;
var oneClickOffline=<%=ServerEnv.getInstance().getBooleanConfig("one.click.offline", false)%>;
var notDisplayPassword=<%=ServerEnv.getInstance().getBooleanConfig("not.display.password", false)%>;

var cmdbAgentMess=<%=ServerEnv.getInstance().getBooleanConfig(Environment.AGENT_CMDB_MESSAGE_SWITCH, false)%>;
var upgradePostalSwitch=<%=ServerEnv.getInstance().getBooleanConfig("upgrade.postal.switch", false)%>;
var lnzgyhfhSwitch=<%=ServerEnv.getInstance().getBooleanConfig("ln.bank.of.china.switch", false)%>;
/**
 * Agent管理增加字段业务系统  业务系统：新增采集业务系统字段，字段添加到端口号后面，数据来源于节点绑定页面（系统名称）。
 * 导出功能增加此字段，导入不需要增加。此功能为光大特有功能，通过开关控制 jiaMing 2023-8-24
 *是否显示系统名称 agent.manager.displaySystemName
 */
var displaySystemName=<%=ServerEnv.getInstance().getBooleanConfig("agent.manager.displaySystemName", false)%>;
//民生银行agent管理页面显示开关
var isMinShengBank = <%=ServerEnv.getInstance().getBooleanConfig("minsheng.agentManagePage.display", false)%>;

//福建农信银行
var isFJNX = <%= Environment.getInstance().getBankSwitchIsFjnx()%>;
//徽商银行
var isHSBank = <%= Environment.getInstance().getHuiShangBankSwitch()%>;
</script>

<html>
<head>
    <script type="text/javascript">
        var agent_maintain_area_now="";
        if(isTabSwitch){
            $(document).ready(function(){
                $("#agent_maintain_area").attr('id',$("#agent_maintain_area").attr('id')+agent_maintain_area_now)
            });
        }
    </script>
<script type="text/javascript" src="<%=request.getContextPath()%>/page/agentMaintain/agent_maintain_index.js"></script>
</head>
<body>
<div id="agent_maintain_area" style="width: 100%;height: 100%">
</div>
</body>
</html>