/*******************************************************************************
 * 流程定制列表
 ******************************************************************************/
var scriptNameObj;
var scriptNameStore;
var scriptContentObj;
var stepNameObj;
var flowStore;
var planDescWin;
var bsPageBar=null;
var queryForm = null;
var flowGrid=null;
var MainPanel=null;
Ext
        .onReady (function ()
        {
	        // 主panel高度减掉的定值
	        var staticHeight = 45;
	        // 清理主面板的各种监听时间
	        destroyRubbish ();
	        /** 树数据Model* */
	        Ext.define ('flowModel',
	        {
	            extend : 'Ext.data.Model',
	            fields : [
	                    {
	                        name : 'instanceName',
	                        type : 'string'
	                    },
	                    {
	                        name : 'iid',
	                        type : 'long'
	                    },
	                    {
	                        name : 'ibusnesSysIid',
	                        type : 'long'
	                    },
	                    {
	                        name : 'issusflow',
	                        type : 'int'
	                    },
	                    {
	                        name : 'isbackinstance',
	                        type : 'int'
	                    },
	                    {
	                        name : 'ibusnesSysName',
	                        type : 'string'
	                    },
	                    {
	                        name : 'ideployName',
	                        type : 'string'
	                    },
	                    {
	                        name : 'iversionType',
	                        type : 'string'
	                    },
	                    {
	                        name : 'iplanDesc',
	                        type : 'string'
	                    },
	                    {
	                        name : 'iinstanceType2',
	                        type : 'int'
	                    }
	            ]
	        });

	        //hzg begin

			var typeStore = Ext.create('Ext.data.JsonStore', {
				fields : [ 'deployType','deployId' ],
				autoLoad : true,
				proxy : {
					type : 'ajax',
					url : 'getDeployType.do',
					reader : {
						type : 'json',
						root : 'deployType'
					}
				}
			});

			var typeComboX = Ext.create('Ext.form.field.ComboBox', {
				fieldLabel: '部署类型',
				name:'deployType',
				labelWidth : 69,
				labelAlign : 'right',
				padding : '0 10 0 0',
				store : typeStore,
				queryMode : 'local',
				width : 300,
				forceSelection : true, // 要求输入值必须在列表中存在
				typeAhead : true, // 允许自动选择
				displayField : 'deployType',
				valueField : 'deployId',
				triggerAction : "all",
				emptyText : "--请选择部署类型--",
				listeners: {
					specialkey: function(field, e){
						if (e.getKey() == e.ENTER) {
							grid.ipage.moveFirst();
						}
					}
				}
			});

			//hzg end

	         queryForm = Ext.create('Ext.form.Panel', {
	        	region: 'north',
	    		//height: 90,
	    		bodyCls: 'service_platform_bodybg',
	    		border : true,
	    		collapsible : true,//可收缩
	    		collapsed : false,//默认收缩
//	    	    border: false,
	    	    width : '100%',
//	    	    height: 65,
	    	    bodyPadding: 5,
	    	    layout: 'form',
	    	    items: [{
	    	      layout:'column',
	    	      border : false,
	    	      items: [
	              {
	    	        labelWidth : 51,
	    	        labelAlign : 'right',
	    	        width: 300,
	    	        xtype: 'textfield',
	    	        fieldLabel: '方案名',
	    	        name: 'instanceName',
	    	        padding : '0 10 0 0' 
	    	      },{
	    	        labelWidth : 79,
	    	        labelAlign : 'right',
	    	        width: 300,
	    	        xtype: 'textfield',
	    	        fieldLabel: '业务系统名',
	    	        name: 'busSysName',
	    	        padding : '0 10 0 0' 
	    	      },typeComboX,{
	    	        xtype: 'button',
	    	        text: '查询',
	    	        //width:60,
	    	        margin : '0 10 0 0',
	    	        handler: function() {
	    	          queryWhere();
	    	        }
	    	      },{
	    	        xtype: 'button',
	    	        text: '重置',
	    	        //width:60,
	    	        margin : '0 10 0 0',
	    	        handler: function() {
	    	          resetWhere();
	    	        }
	    	      }]
	    	    }]
	    	  });
	     	contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
	    	queryForm.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
//	       queryFormPanel=Ext.create('Ext.Panel', {
//	    	  	hidden:false,
//	    	  	region : 'north',
//	    		witdh:'100%',
//	    		layout:'fit',
//	    		border : false,	
//	    		items: [queryForm]
//	    	});
	        /** 树数据源* */
	        flowStore = Ext.create ('Ext.data.Store',
	        {
	            autoLoad : true,
	            autoDestroy : true,
	            model : 'flowModel',
	            pageSize : 50,
	            proxy :
	            {
	                type : 'ajax',
	                url : 'getFlowList.do',
	                reader :
	                {
	                    type : 'json',
	                    root : 'dataList'
	                }
	            }
	        });
	        
	        flowStore.on ('beforeload', function (store, options)
       		 {
       			 var instanceName = queryForm.getForm().findField("instanceName").getValue();
       			 var busSysName = queryForm.getForm().findField("busSysName").getValue();
       			 //hzg begin
				 var deployType = queryForm.getForm().findField("deployType").getValue();
				 if(deployType==null){
					 deployType="";
				 }
				 //hzg end
       			 var new_params =
       			 {
       					 instanceName : trim(instanceName),
       					 busSysName : trim(busSysName),
					     deployType : trim(deployType)
       			 };
       			 Ext.apply (flowStore.proxy.extraParams, new_params);
       		 });
	        /** 树列表columns* */
	        var flowColumns = [
	                {
	                    text : '序号',
	                    // align : 'center',
	                    width : 65,
	                    xtype : 'rownumberer'
	                },
	                {
	                    text : '方案名',
	                    dataIndex : 'instanceName',
	                    flex : 1,
	                    renderer : function (value, metaData, record)
	                    {
		                    return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlow('
		                            + record.get ('iid')
		                            + ',\''
		                            + value
		                            + '\',1,'
		                            + record.get ('ibusnesSysIid')
		                            + ','
		                            + record.get ('issusflow')
		                            + ','
		                            + record.get ('isbackinstance')
		                            + ','
		                            + record.get ('iversionType')
		                            + ','
		                            + record.get ('iinstanceType2')
		                            + ')">'
		                            + value
		                            + '</a>';
	                    }
	                },
	                {
	                    text : '业务系统',
	                    dataIndex : 'ibusnesSysName',
	                    flex : 1
	                },
	                {
	                    text : '部署类型',
	                    dataIndex : 'ideployName',
	                    flex : 1
	                },
	                {
	                    text : '方案版本',
	                    dataIndex : 'iversionType',
	                    hidden:true,
	                    flex : 1
	                },
	                {
	                    text : '流程类别',
	                    dataIndex : 'isbackinstance',
	                    flex : 1,
	                    renderer : function (value, metaData, record)
	                    {
		                    switch (value)
		                    {
			                    case 0:
				                    return '部署流程';
			                    case 1:
				                    return '回切流程';
			                    default:
				                    return '部署流程';
		                    }
	                    }
	                },
	                {
	                    text : '方案说明',
	                    dataIndex : 'iplanDesc',
	                    flex : 1,
	                    renderer : function (value, metaData, record)
	                    {
		                    return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="planDescFun('
		                            + record.get ('iid')
		                            + ',\''
		                            + record.get ('iplanDesc')
		                            +'\''
		                            + ')">'
		                            + '方案说明'
		                            + '</a>';
	                    }
	                },
	                {
	                    text : '操作',
	                    dataIndex : 'instanceName',
	                    sortable : false,
	                    hideable : false,// 是否可以手动隐藏
	                    // flex : 1,
	                    width : 150,
	                    align : 'center',
	                    renderer : function (value, metaData, record)
	                    {
//		                    return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="openCopyWindow('
//                                    + record.get ('iid')
//                                    + ',\''
//		                            + value
//		                            + '\')">复制</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlow('
//		                            + record.get ('iid')
//		                            + ',\''
//		                            + value
//		                            + '\',1,'
//		                            + record.get ('ibusnesSysIid')
//		                            + ','
//		                            + record.get ('issusflow')
//		                            + ','
//		                            + record.get ('isbackinstance')
//		                            + ')">查看</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlowForUpdate('
//		                            + record.get ('iid')
//		                            + ',\''
//		                            + value
//		                            + '\',0,'
//		                            + record.get ('ibusnesSysIid')
//		                            + ','
//		                            + record.get ('issusflow')
//		                            + ','
//		                            + record.get ('isbackinstance')
//		                            + ')">编辑</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="deleteFun('
//		                            + record.get ('iid') + ')">删除</a>';
	                    	var copyString="";
	                    	//判断复制方案开关
	                    	if(susCopyprogrammeSwitch)
	                    		{
	                    		copyString='&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="openCopyWindow('
	                                + record.get ('iid')
	                                + ',\''
	                                + value
	                                + '\')">复制</a>';
	                    		}
	                    	return '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlow('
                            + record.get ('iid')
                            + ',\''
                            + value
                            + '\',1,'
                            + record.get ('ibusnesSysIid')
                            + ','
                            + record.get ('issusflow')
                            + ','
                            + record.get ('isbackinstance')
                            + ','
		                    + record.get ('iversionType')
		                    + ','
		                    + record.get ('iinstanceType2')
                            + ')">查看</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="forwardRMFlowForUpdate('
                            + record.get ('iid')
                            + ',\''
                            + value
                            + '\',0,'
                            + record.get ('ibusnesSysIid')
                            + ','
                            + record.get ('issusflow')
                            + ','
                            + record.get ('isbackinstance')
                            + ','
		                    + record.get ('iversionType')
		                    + ','
		                    + record.get ('iinstanceType2')
                            + ')">编辑</a>&nbsp;&nbsp;<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="deleteFun('
                            + record.get ('iid') + ')">删除</a>'+copyString;
	                    }
	                }
	        ];
	        bsPageBar = Ext.create ('Ext.PagingToolbar',
	        {
	            store : flowStore,
	            dock : 'bottom',
	            baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	            border : false,
	            displayInfo : true,
	            emptyMsg : "没有记录"
	        });
	        /** 树列表panel* */
	        flowGrid = Ext.create ('Ext.ux.ideal.grid.Panel',
	        {
	            extend : 'Ext.grid.Panel',
	            alias : 'widget.ideapanel',
	            ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	            cls:'customize_panel_back',
	            store : flowStore,
	            border : true,
	            padding : grid_margin,
	            columns : flowColumns,
	            //bbar : bsPageBar,
	            collapsible : false,
	    	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    	    {
	    		    checkOnly : true
	    	    }),
	            dockedItems : [
		            {
		                xtype : 'toolbar',
		                //height : 40,
		                items : ['->', {
				                	id : 'checkFlowId', 
				                	xtype : 'button',
		                            text : '方案检查',
		                            cls : 'Common_Btn',
		                            handler : checkFlow
                        		},
		                        {
		                            xtype : 'button',
//		                            text : '<u>增加</u>',
		                            text : '增加',
//		                            iconCls : 'add',
		                            hidden:arrangeFlowSwitch,
		                            cls : 'Common_Btn',
		                            handler : function ()
		                            {
			                            forwardRMFlowForCreate (0, '', 0, 0, 1, 0,'0');
		                            }
		                        },{
						            text: '增加',
						            cls:'Common_Btn',
						            hidden:!arrangeFlowSwitch,
						            width : 120,
						            menu: [{
				                        text:'增加方案',
				                        handler : function ()
			                            {
				                            forwardRMFlowForCreate (0, '', 0, 0, 1, 0,'0');
			                            }
				                    },{
				                        text:'增加编排方案',
				                        handler : function ()
			                            {
				                        	forwardRMFlow_arrange(0,0);
			                            }
				                    }]
				},
		                        {
		                            xtype : 'button',
		                            text : '导出',
		                            cls : 'Common_Btn',
		                            handler : exportFlowXML
		                        },
		                        {
		                            xtype : 'button',
		                            text : '导入',
		                            cls : 'Common_Btn',
		                            handler : openImportWindowsXML
		                        }
		                ]
		            }
	            ]
	        });
	        // 主Panel
	        MainPanel = Ext.create ('Ext.panel.Panel',
	        {
	            renderTo : "mainpanelE",
	            width : '100%',
	            height : contentPanel.getHeight (),
	            // overflowY:'scroll',
	            border : true,
	            //bodyCls: 'service_platform_bodybg',
	            cls:'customize_panel_header_arrow',
//	            bodyPadding : 5,
	            layout : 'border',
	            items : [
		            queryForm,flowGrid
	            ]
	        });
	        
	        $("body").off('keydown').on('keydown',function(event) {
	     	    if (event.keyCode == "13") { 
	     	    	queryWhere();
	     	    }
	     	});
	        
//	        finalQueryPanelObj=queryFormPanel;
//	        finalTitlePanelObj=contentPanel;
//	        finalTitle=contentPanel.title;
//	        if(contentPanel.title=='流程定制'){
//	        	contentPanel.setTitle(getTabForOverrideTitle(false));
//	        }
	        //查询条件面板伸缩时，调整grid面板自适应
//	        queryFormPanel.on ('hide', function (){
//	       	 autoResize(flowGrid,queryFormPanel,MainPanel,contentPanel);
//	        });
//	        queryFormPanel.on ('show', function (){
//	       	 autoResize(flowGrid,queryFormPanel,MainPanel,contentPanel);
//	        });
		           
		   	contentPanel.on ('resize', function ()
		   	{
//		   		autoResize(flowGrid,queryFormPanel,MainPanel,contentPanel);
		   		//MainPanel.setWidth ('100%');
		   		//MainPanel.setHeight (contentPanel.getHeight () - staticHeight);
		   		MainPanel.setWidth(contentPanel.getWidth ());
		   		MainPanel.setHeight (contentPanel.getHeight());
		   	});
	        contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	        {
		        Ext.destroy (MainPanel);
		        if (Ext.isIE)
		        {
			        CollectGarbage ();
		        }
	        });
	        function trim (t)
	        {
		        t = t.replace (/(^\s*)|(\s*$)/g, "");
		        return t.replace (/(^ *)|( *$)/g, "");
	        }
	        
	        /**查询条件清空*/
	    	function resetWhere(){
	    		queryForm.getForm().findField("instanceName").setValue('');
	    		queryForm.getForm().findField("busSysName").setValue('');
	    		//hzg begin
				queryForm.getForm().findField("deployType").setValue('');
				//hzg end
	    	};
	    	
	    	function queryWhere(){
	    		bsPageBar.moveFirst();
	    	};
	        
	        /** 弹出上传窗口* */
	        function openImportWindows (iid)
	        {
		        configwindow = Ext.create ('Ext.window.Window',
		        {
		            title : '流程导入',
		            autoScroll : true,
		            modal : true,
		            closeAction : 'destroy',
		            buttonAlign : 'center',
		            draggable : false,// 禁止拖动
		            resizable : false,// 禁止缩放
		            width : 500,
		            height : 100,
		            loader :
		            {
		                url : "page/resourcemanager/static/assets/js/importWindow.jsp",
		                params :
		                {
			                importUrl : 'importFlowExcel.do',
		                	fileType:'ZIP'
		                },
		                autoLoad : true,
		                autoDestroy : true,
		                scripts : true
		            },
		            listeners :
		            {
			            close : function ()
			            {
				            flowStore.reload ();
			            }
		            }
		        }).show ();
	        }
	        /** 弹出上传窗口* */
	        function openImportWindowsXML ()
	        {
		        configwindow = Ext.create ('Ext.window.Window',
		        {
		            title : '流程导入',
		            autoScroll : true,
		            modal : true,
		            closeAction : 'destroy',
		            buttonAlign : 'center',
		            draggable : false,// 禁止拖动
		            resizable : false,// 禁止缩放
		            width : 800,
		            height : 600,
		            loader :
		            {
		                url : "page/resourcemanager/static/assets/js/importWindow.jsp",
		                params :
		                {
			                importUrl : 'importFlowXML.do',
		                	fileType:'XML'
		                },
		                autoLoad : true,
		                autoDestroy : true,
		                scripts : true
		            },
		            listeners :
		            {
			            close : function ()
			            {
				            flowStore.reload ();
			            }
		            }
		        }).show ();
	        }
	        
        });
/**
 * 编辑或查看流程
 * @param iidIn
 * @param instanceNameIn
 * @param showOnly
 */
function forwardRMFlow (iidIn, instanceNameIn, showOnly, ibusnesSysIid, issusflow, isbackinstance,iversiontype,iinstanceType2)
{
	destroyRubbish ();
	contentPanel.setTitle (queryForm.title);
	contentPanel.getHeader().show();//让contentPanel显示标题头
	var url='flowCustomizedInit.do';
	if(iversiontype=='1')
	{
		url='flowCustomizedInitWS.do'
	}
	if(iinstanceType2==1)
	{
		url='arrangeFlowCustomizedInit.do';
	}
	contentPanel.getLoader ().load (
	{
	    url : url,
	    params :
	    {
	        iid : iidIn,
	        instanceName : instanceNameIn,
	        showOnly : showOnly,
	        ibusnesSysIid : ibusnesSysIid,
	        issusflow : issusflow,
	        isbackinstance : isbackinstance,
	        iversiontype : '0'
	    },
	    scripts : true
	});
	if (Ext.isIE)
	{
		CollectGarbage ();
	}
}
/**
 * 编辑或查看流程(编排方案)
 * @param iidIn
 * @param instanceNameIn
 * @param showOnly
 */
function forwardRMFlow_arrange (iidIn,showOnly)
{
	destroyRubbish ();
	contentPanel.setTitle (queryForm.title);
	contentPanel.getHeader().show();//让contentPanel显示标题头
	var url='arrangeFlowCustomizedInit.do';
	contentPanel.getLoader ().load (
	{
	    url : url,
	    params :
	    {
	        iid : iidIn,
//	        instanceName : instanceNameIn,
	        showOnly : showOnly,
//	        ibusnesSysIid : ibusnesSysIid,
//	        issusflow : issusflow,
//	        isbackinstance : isbackinstance,
//	        iversiontype : '0'
	    },
	    scripts : true
	});
	if (Ext.isIE)
	{
		CollectGarbage ();
	}
}
/**
 * 判断是否有新建模板权限
 * @param iidIn
 * @param instanceNameIn
 * @param showOnly
 */
function forwardRMFlowForCreate (iidIn, instanceNameIn, showOnly, ibusnesSysIid, issusflow, isbackinstance,iversiontype)
{
	Ext.Ajax.request (
	{
	    url : 'checkCreateFlowRole.do',
	    params :
	    {
		    instanceId : iidIn
	    },
	    method : 'POST',
	    async : false,
	    success : function (response, options)
	    {
		    if (Ext.decode (response.responseText).success == true)
		    {
			    forwardRMFlow (iidIn, instanceNameIn, showOnly, ibusnesSysIid, issusflow, isbackinstance,iversiontype);
		    }
		    
	    },
	    failure : function (result, request)
	    {
		    secureFilterRs (result, "保存失败！");
	    }
	});
}
/**
 * 判断是否有编辑模板权限
 * @param iidIn
 * @param instanceNameIn
 * @param showOnly
 */
function forwardRMFlowForUpdate (iidIn, instanceNameIn, showOnly, ibusnesSysIid, issusflow, isbackinstance,iversiontype,iinstanceType2)
{

			Ext.Ajax.request (
			{
			    url : 'checkModifyFlowRole.do',
			    params :
			    {
				    instanceId : iidIn
			    },
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
				    if (Ext.decode (response.responseText).success == true)
				    {
					    forwardRMFlow (iidIn, instanceNameIn, showOnly, ibusnesSysIid, issusflow, isbackinstance,iversiontype,iinstanceType2);
				    }
				    
			    },
			    failure : function (result, request)
			    {
				    secureFilterRs (result, "保存失败！");
			    }
			});

}

/**
 * 删除流程
 * @param iidIn
 */
function deleteFun (iidIn)
{
    Ext.Msg.confirm("请确认", "是否真的要删除数据？", function(button, text) {
        if (button == "yes") {
			Ext.Ajax.request (
			{
			    url : 'deleteFlowInstanceVersionForSUS.do',
			    params :
			    {
				    instanceId : iidIn
			    },
			    method : 'POST',
			    async : false,
			    success : function (response, options)
			    {
				    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
				    if (Ext.decode (response.responseText).success == true)
				    {
					    flowStore.reload ();
				    }
				    
			    },
			    failure : function (result, request)
			    {
				    secureFilterRs (result, "保存失败！");
			    }
			});
        }
    });	
}
/** 弹出复制窗口* */
function openCopyWindow(iid,instanceName) {
	copyConfigWindow = Ext.create ('Ext.window.Window',
			{
			    title : '方案复制',
			    modal : true,
			    closeAction : 'destroy',
			    constrain : true,
			    autoScroll : true,
			    width : 600,
			    height : 150,
			    draggable : false,// 禁止拖动
			    resizable : false,// 禁止缩放
			    layout : 'fit',
			    loader :
			    {
			        url : 'flowCopyinit.do',
			        params :
	                 {
			        	instanceName : instanceName,
			        	iid:iid
	                 },
			        autoLoad : true,
			        scripts : true
			    },
			    listeners: {
			    	"close":function(){
			    		bsPageBar.moveFirst ();// 刷新左列表
			    	}
			    }
			    		
			}).show ();
}
/**导出**/
function exportFlow() {
	var m = flowGrid.getSelectionModel ().getSelection ();
	if(m.length<1)
		{
		 Ext.Msg.alert ('提示', '请选择要导出的流程!');
		 return ;
		}
	var selecttionId='';
	for (var i = 0, len = m.length; i < len; i++)
	{
		var ss = Ext.JSON.encode (m[i].data.iid);
		if (i == 0)
			selecttionId = selecttionId + ss;
		else
			selecttionId = selecttionId + "," + ss;
	}
	window.location.href = 'exportFlowExcel.do?selecttionId='+selecttionId;
}
/**导出-XML**/
function exportFlowXML() {
	var m = flowGrid.getSelectionModel ().getSelection ();
	if(m.length<1)
		{
		 Ext.Msg.alert ('提示', '请选择要导出的流程!');
		 return ;
		}
	var selecttionId='';
	for (var i = 0, len = m.length; i < len; i++)
	{
		var ss = Ext.JSON.encode (m[i].data.iid);
		if (i == 0)
			selecttionId = selecttionId + ss;
		else
			selecttionId = selecttionId + "," + ss;
	}
	window.location.href = 'exportFlowXML.do?selecttionId='+selecttionId;
}

/**方案检查**/
function checkFlow ()
{
		 Ext.getCmp('checkFlowId').disable();
		Ext.Ajax.request (
		{
		    url : 'checkFlow.do',
		    method : 'POST',
		    async : false,
		    success : function (response, options)
		    {
			    if (Ext.decode (response.responseText).success == true)
			    {
			    	Ext.Msg.alert ('提示', Ext.decode(response.responseText).message+""+Ext.decode(response.responseText).num);
			    	flowStore.reload ();
			    }else{
			    	Ext.Msg.alert ('提示', Ext.decode(response.responseText).message);
			    }
			    Ext.getCmp('checkFlowId').enable();
		    },
		    failure : function (result, request)
		    {
			    secureFilterRs (result, "检查失败！");
			    Ext.getCmp('checkFlowId').enable();
		    }
		});
}


/**弹出方案说明窗口**/
function planDescFun(id,storePlanDesc){
	if (Ext.isIE) {
		CollectGarbage();
	}
	var oldPlanDesc = Ext.create('Ext.form.TextArea', {
		fieldLabel : '方案说明',
		labelWidth : 93,
		readOnly : true,
		xtype : 'textarea',
		grow : true,
		height : 220,
		width  : 490,
		name : 'oldPlanDesc'
	});
	
	oldPlanDesc.setValue(storePlanDesc);
	
	var newPlanDesc = Ext.create('Ext.form.TextArea', {
		fieldLabel : '新增方案说明',
		labelWidth : 93,
		xtype : 'textarea',
		grow : false,
		height : 220,
		width  : 490,
		name : 'newPlanDesc'
	});
	
	var saveBtn = Ext.create("Ext.Button", {
		text : '保存',
		cls : 'Common_Btn',
		handler : function() {
			savePlanDesc(id,oldPlanDesc.getValue( ),newPlanDesc.getValue( ),storePlanDesc);
		}
	});
	var closeBtn = Ext.create("Ext.Button", {
		text : '关闭',
		cls : 'Common_Btn',
		handler : function() {
			planDescWin.close();
		}
	});


	/*var planDescFormPanel = Ext.create('Ext.form.FormPanel',{
	      border : false,
	      width : 800,
	      height: 610,
	    region : 'center',
	    items : {
	    	layout:'vbox',
			align: 'center',
	    	items:[
				oldPlanDesc,newPlanDesc,
	    		{
	    		layout:'hbox',
	  	      	border:true,
	    		width : 800,
	  	      	height: 50,
	  	      	items:[saveBtn,closeBtn]}, 

	    	]
	    }
	  });
	*/
	var planDescFormPanel = Ext.create('Ext.form.FormPanel', {
		border: false,
		width: 800,
		height: 610,
		region: 'center',
		layout: {
			type: 'vbox',
		},
		items: [
			oldPlanDesc,
			newPlanDesc,
			{
				layout: {
					type: 'hbox',
					align: 'middle',
					pack: 'center'
				},
				border: true,
				width: 600,
				height: 50,
				items: [
					saveBtn,
					closeBtn
				]
			}
		]
	});
	planDescWin = new Ext.Window({
	      title : '方案说明',  
	      layout:'border',
	      autoScroll : false,
	      autoDestroy : true,
	      closeAction : 'destroy',
	      closeable : true,  
	      modal : true,  
	      width : 650,
	      height: 610,
	      resizable:false,
	      plain : true,  
	      layout : 'border',  
	      draggable:true,  
	      items : [planDescFormPanel]
		});  
	planDescWin.show();
	
}

/**保存方案说明**/
function savePlanDesc(id,oldPlanDesc,newPlanDesc,storePlanDesc){
	
	if (((newPlanDesc == null) || (newPlanDesc == '')) && (oldPlanDesc == storePlanDesc))
	{
		Ext.MessageBox.alert ("提示", "方案说明无变化，请修改后再保存！");
		return;
	}
	
	if((oldPlanDesc.indexOf("'") != -1) || (oldPlanDesc.indexOf('"') != -1) || (newPlanDesc.indexOf("'") != -1) || (newPlanDesc.indexOf('"') != -1))
	{
		Ext.MessageBox.alert ("提示", "方案说明中禁止包含'和\"字符");
		return;
	}
	
	var planDesc = '';
	if((newPlanDesc == null) || (newPlanDesc == ''))
	{	
		planDesc = oldPlanDesc.replace(/\n/g,'\\n') + '\\n--------------------------------------------------------------------------------------   ';
	}else if((oldPlanDesc == null) || (oldPlanDesc == ''))
	{
		planDesc = newPlanDesc.replace(/\n/g,'\\n') + '\\n--------------------------------------------------------------------------------------   ';
	}else
	{
		planDesc = oldPlanDesc.replace(/\n/g,'\\n') + '\\n' + newPlanDesc.replace(/\n/g,'\\n') + '\\n--------------------------------------------------------------------------------------   '; 
		
	}
	

	if(fucCheckLength(planDesc) > 3999)
	{
		Ext.MessageBox.alert ("提示", "方案说明字符个数禁止超过4000");
		return;
	}
	
	Ext.Ajax.request({
		url : 'savePlanDesc.do',
		method : 'post',
		params : {
			iid : id,
			iplanDesc : planDesc
		},
		success : function(response, request) {
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (success) {
				flowStore.reload();
				planDescWin.close();
				Ext.Msg.alert('提示', message);
			} else {
				Ext.Msg.alert('提示', message);
			}
		},
		failure : function(result, request) {
			secureFilterRs(result, "请求返回失败！");
		}
	});
	
}


