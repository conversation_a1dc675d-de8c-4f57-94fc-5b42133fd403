Ext.onReady(function(){
	Ext.define('projectModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'pic',
            type: 'string'
        },
        {
            name: 'iprojectname',
            type: 'string'
        },
        {
            name: 'status',
            type: 'string'
        },
        {
            name: 'icreateuser',
            type: 'string'
        },
        {
            name: 'icreatetime',
            type: 'string'
        },
        {
            name: 'iendtime',
            type: 'string'
        },
        {
            name: 'timeuse',
            type: 'string'
        },
        {
            name: 'iremarks',
            type: 'string'
        }]
	})
        if (Ext.getCmp('lbMainPanel')) {
        	Ext.getCmp('lbMainPanel').destroy();
        }
        function systemTypeWinclose(me) {
        	console.log('me.winRet', me.winRet)
        }
        function retTypeWin(ret) {
        	systemTypeUUIDForQuery.setValue(ret.isystemtypeuuid);
        	grid_panel.down('pagingtoolbar').moveFirst()
        }
        function getRelease(value, p, record) {
        }
        function getSystemType(value, p, record) {
        }
        function releaseProject(record) {
        }
        function getProperty(value, p, record) {
        }
        var gridColumns = [ {
        	text : '序号',
        	width : 60,
        	xtype : 'rownumberer'
        },{ text: '快照', dataIndex: 'pic', width : 40 }, {
        	text : '流程名',
        	dataIndex : 'iprojectname',
        	width : 200
        }, {
        	text : '状态',
        	dataIndex : 'status',
        	width : 120,
        	renderer : function (value,metaData,record)
            {
        		return "<span class='Green_color State_Color'>已完成</span>";
            }
        }, {
        	text : '发起人',
        	dataIndex : 'icreateuser',
        	width : 100
        }, {
        	text : '开始时间',
        	dataIndex : 'icreatetime',
        	width : 180
        }, {
        	text : '结束时间',
        	dataIndex : 'iendtime',
        	width : 180
        }, {
        	text : '耗时',
        	dataIndex : 'timeuse',
        	width : 100
        }, {
        	text : '备注',
        	dataIndex : 'iremarks',
        	flex : 1
        } ];


        var projectStore = Ext.create('Ext.data.Store', {
        	autoLoad : false,
        	autoDestroy : true,
        	model : 'projectModel',
        	data:{'items':[
                { 'pic': "<a href='#' onclick=\"showFlowState2('10123');\" > <image class='snapshot'  src='images/monitor_bg.png' border='0'/></a>",  'iprojectname':'云管创建虚拟机流程',
                	'status':'完成',
                		'icreateuser':'ideal',
                		'icreatetime':'2020-07-14 11:11:11',
                		'iendtime':'2020-07-14 11:11:41',
                		'timeuse':'30s',
                		'iremarks':'创建虚拟机流程,ip:xxx.xxx.xxx.xxx'  }
            ]},
        	proxy : {
        		 type: 'memory',
        	        reader: {
        	            type: 'json',
        	            root: 'items'
        	        }
        	}
        });
        
        
        var grid_panel = Ext
        		.create('Ext.ux.ideal.grid.Panel',
        				{
        					store : projectStore,
        					ipageBaseCls : Ext.baseCSSPrefix
        							+ 'toolbar customize_toolbar',
        					iqueryFun : queryWhere,
        					cls : 'customize_panel_back',
        					selModel : Ext.create('Ext.selection.CheckboxModel', {
        						checkOnly : true
        					}),
        					region : 'center',
        					border : true,
        					columnLines : true,
        					columns : gridColumns,
        					listeners : {
        						beforeedit : function(editor, context, eOpts) {
        							if (context.column.dataIndex == 'iprojectname'
        									&& context.record.data.iid != -1) {
        								return false
        							}
        							return true
        						},
        						cellclick : function(me, td, cellIndex, record, tr,
        								rowIndex, e, eOpts) {
        							if (td.innerText.trim() == '工程发布') {
        								if (record.raw.iid <= 0) {
        									Ext.Msg.alert('提示', '该记录尚未保存！');
        									return
        								}
        								releaseProject(record)
        							}
        						},
        						celldblclick : function(me, td, cellIndex, record, tr,
        								rowIndex, e, eOpts) {
        							if (this.columns[cellIndex - 1].initialConfig.text
        									.trim() == '分类') {
        								var systemType = Ext.create(
        										'page.webStudio.project.systemTypeWin',
        										{
        											record : record,
        											retWin : retWin,
        											rowIndex : rowIndex,
        											listeners : {
        												'close' : {
        													fn : systemTypeWinclose
        												}
        											}
        										});
        								systemType.show()
        							}
        						}
        					},
        					collapsible : false,
        				});

        var projectNameForQuery = Ext.create('Ext.form.TextField', {
        	margin : '5',
        	emptyText : '--请输入流程名--',
        	labelWidth : 80,
        	width : 180,
        	xtype : 'textfield'
        });
        function queryWhere() {
        	grid_panel.down('pagingtoolbar').moveFirst()
        }
        function queryType() {
        	var queryTypeWin = Ext.create('page.webStudio.project.systemTypeWin', {
        		retWin : retTypeWin,
        		listeners : {
        			'close' : {
        				fn : systemTypeWinclose
        			}
        		}
        	});
        	queryTypeWin.show()
        }
        function reset() {
        	projectNameForQuery.setValue('');
        	systemTypeNameForQuery.setValue('');
        	systemTypeUUIDForQuery.setValue('');
        	queryWhere()
        }
        function onAddListener() {
        	console.log('ddddd:' + projectTypeId);
        	var p = {};
        	if (projectTypeId == 8) {
        		p = Ext.create('projectModel', {
        			iid : -1,
        			dailytype : 2,
        			systemid : -1,
        			iversion : '1',
        			irepoertid : ''
        		});
        		projectStore.insert(0, p)
        	} else {
        		p = Ext.create('projectModel', {
        			iid : -1,
        			dailytype : 0,
        			systemid : -1,
        			iversion : '1',
        			irepoertid : ''
        		});
        		projectStore.insert(0, p)
        	}
        }
        function onSaveAsListener() {
        	var records = grid_panel.getSelectionModel().getSelection();
        	if (records.length == 0) {
        		Ext.Msg.alert('提示', '请先选择您要操作的行!');
        		return
        	} else if (records.length > 1) {
        		Ext.Msg.alert('提示', '只能选择一个工程进行另存为!');
        		return
        	}
        	if (records[0].raw.iid <= 0) {
        		Ext.Msg.alert('提示', '该记录尚未保存');
        		return
        	}
        	console.log('records[0].data:', records[0].data);
        	var saveAsWin = Ext.create('page.webStudio.project.saveAsWin', {
        		record : records[0].data,
        		listeners : {
        			'close' : {
        				fn : winclose
        			}
        		}
        	});
        	saveAsWin.show()
        }
        function onImportListener() {
        	var uploadWindows;
        	var uploadForm = Ext.create('Ext.form.FormPanel', {
        		border : false,
        		items : [ {
        			xtype : 'filefield',
        			name : 'fileName',
        			fieldLabel : '选择文件',
        			labelWidth : 80,
        			anchor : '90%',
        			margin : '10 10 0 40',
        			buttonText : '浏览'
        		} ],
        		buttonAlign : 'center',
        		buttons : [ {
        			text : '确定',
        			handler : upJsonData
        		}, {
        			text : '取消',
        			handler : function() {
        				uploadWindows.close()
        			}
        		} ]
        	});
        	uploadWindows = Ext.create('Ext.window.Window', {
        		title : '工程json文件导入，请选择编码格式为UTF-8的.json文件',
        		layout : 'fit',
        		height : 150,
        		width : 600,
        		modal : true,
        		items : [ uploadForm ],
        		listeners : {
        			close : function(g, opt) {
        				uploadForm.destroy()
        			}
        		}
        	});
        	uploadWindows.show();
        	function upJsonData() {
        		var form = uploadForm.getForm();
        		var hdupfile = form.findField('fileName').getValue();
        		if (hdupfile == '') {
        			Ext.Msg.alert('提示', '请选择文件...');
        			return
        		}
        		;
        		Ext.MessageBox.confirm('提示', '导入时会覆盖同名工程，是否继续导入？', function(btn) {
        			if (btn == 'no') {
        				return
        			} else {
        				uploadTemplate(form)
        			}
        		})
        	}
        	function uploadTemplate(form) {
        		if (form.isValid()) {
        			form
        					.submit({
        						url : 'importProjects.do',
        						success : function(form, action) {
        							var success = Ext
        									.decode(action.response.responseText).success;
        							var message = Ext
        									.decode(action.response.responseText).message;
        							if (success) {
        								uploadWindows.close();
        								projectStore.reload();
        								Ext.Msg.alert('提示', message)
        							} else {
        								Ext.Msg.alert('提示', message)
        							}
        							return
        						},
        						failure : function(form, action) {
        							Ext.MessageBox.hide();
        							var message = Ext
        									.decode(action.response.responseText).message;
        							if (message != null && message != ''
        									&& typeof (message) != 'undefined') {
        								Ext.Msg.alert('提示', message)
        							} else {
        								Ext.Msg.alert('提示', '请求超时！')
        							}
        							return
        						}
        					})
        		}
        	}
        }
        function onExportListener(btn) {
        	var record = grid_panel.getSelectionModel().getSelection();
        	if (record.length == 0) {
        		Ext.Msg.alert('提示', '请先选择您要操作的行!');
        		return
        	}
        	var goBack = false;
        	for (var i = 0; i < record.length; i++) {
        		if (-1 != record[i].get('iid')) {
        			goBack = true;
        			break
        		}
        	}
        	if (goBack) {
        		Ext.MessageBox.confirm('提示', '是否导出选中数据!', function(btn) {
        			if (btn == 'no') {
        				return
        			}
        			if (btn == 'yes') {
        				var ids = [];
        				Ext.Array.each(record, function(recordObj) {
        					var cpId = recordObj.get('iid');
        					if (-1 != cpId) {
        						ids.push(cpId)
        					}
        				});
        				window.location.href = 'exportProjects.do?exportIds='
        						+ ids.join(',')
        			}
        		})
        	}
        }
        function onSaveListener(btn) {
        	var m = projectStore.getModifiedRecords();
        	if (m.length < 1) {
        		Ext.Msg.alert('提示', '无需要增加或者修改的数据！');
        		return
        	}
        	var jsonData = '[';
        	var hasNew = false;
        	var ismodel = false;
        	for (var i = 0, len = m.length; i < len; i++) {
        		if (m[i].data.iid < 0) {
        			hasNew = true
        		}
        		if (m[i].data.dailytype == 2) {
        			ismodel = true
        		}
        		if (m[i].data.iprojectname.length == 0
        				|| m[i].data.iprojectname.trim() == '') {
        			Ext.Msg.alert('提示', '工程名不能为空！');
        			return
        		}
        		if (m[i].data.iversion.length == 0 || m[i].data.iversion.trim() == '') {
        			Ext.Msg.alert('提示', '版本号非法！');
        			return
        		}
        		if (m[i].data.itypeid.length == 0 || m[i].data.itypeid == '') {
        		}
        		var ss = Ext.JSON.encode(m[i].data);
        		if (i == 0)
        			jsonData = jsonData + ss;
        		else
        			jsonData = jsonData + ',' + ss
        	}
        	jsonData = jsonData + ']';
        	Ext.MessageBox.wait('数据处理中...', '进度条');
        	if (hasNew) {
        		var saveWin = Ext.create('page.webStudio.project.saveWin', {
        			record : m,
        			jsonData : jsonData,
        			saveProjets : saveProjets,
        			ismodel : ismodel,
        			sourceProjectName : sourceProjectName,
        			listeners : {
        				'close' : {
        					fn : winclose
        				}
        			}
        		});
        		saveWin.show()
        	} else {
        		saveProjets(jsonData, false)
        	}
        }
        function onDeleteListener(btn) {
        	var record = grid_panel.getSelectionModel().getSelection();
        	if (record.length == 0) {
        		Ext.Msg.alert('提示', '请先选择您要操作的行!');
        		return
        	}
        	var goBack = false;
        	var notCreatorRecords = new Array();
        	for (var i = 0; i < record.length; i++) {
        		if (userName != record[i].get('icreateuser')) {
        			console.log(record[i]);
        			notCreatorRecords.push(record[i])
        		}
        		if (-1 != record[i].get('iid')) {
        			goBack = true;
        			break
        		}
        	}
        	if (notCreatorRecords.length > 0) {
        		console.log(notCreatorRecords);
        		Ext.Msg.alert('提示', '所选记录包含非当前用户创建的工程，不能进行删除操作！');
        		return
        	}
        	if (goBack) {
        		Ext.MessageBox
        				.confirm(
        						'提示',
        						'是否删除选中数据!',
        						function(btn) {
        							if (btn == 'no') {
        								return
        							}
        							if (btn == 'yes') {
        								Ext.MessageBox.wait('数据处理中...', '进度条');
        								var ids = [];
        								Ext.Array.each(record, function(recordObj) {
        									var cpId = recordObj.get('iid');
        									if (-1 != cpId) {
        										ids.push(cpId)
        									}
        								});
        								Ext.Ajax
        										.request({
        											url : 'deleteProject.do',
        											timeout : 30000,
        											params : {
        												deleteIds : ids.join(',')
        											},
        											method : 'POST',
        											success : function(response, opts) {
        												var success = Ext
        														.decode(response.responseText).success;
        												if (success) {
        													grid_panel.down(
        															'pagingtoolbar')
        															.moveFirst()
        												}
        												Ext.Msg
        														.alert(
        																'提示',
        																Ext
        																		.decode(response.responseText).message)
        											},
        											failure : function(response,
        													ooptions) {
        												Ext.MessageBox.hide();
        												Ext.Msg.alert('提示', '请求超时！')
        											}
        										})
        							}
        						})
        	} else {
        		for (var i = 0; i < record.length; i++) {
        			projectStore.remove(record[i])
        		}
        	}
        }
        var deleteButtonForPRJ = Ext.create('Ext.Button', {
        	cls : 'Gray_Btn',
        	style : {
        		'margin-right' : '0px'
        	},
        	margin : '5 0 5 5',
        	text : '删除',
        	disabled : true,
        	handler : onDeleteListener
        });
        var saveButtonForPRJ = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '保存',
        	handler : onSaveListener
        });
        var exportButtonForPRJ = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '导出',
        	handler : onExportListener
        });
        var importButtonForPRJ = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '刷新',
        	handler : onImportListener
        });
        var addButtonForSaveAs = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '全部终止',
        	handler : onSaveAsListener
        });
        var addButtonForPRJ = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '终止',
        	handler : onAddListener
        });
        var resetBtn = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '清空',
        	handler : reset
        });
        var queryButtonForPRJ = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '查询',
        	handler : queryWhere
        });
        var queryButtonForType = Ext.create('Ext.Button', {
        	cls : 'Common_Btn',
        	text : '分类',
        	handler : queryType
        });
        var systemTypeUUIDForQuery = Ext.create('Ext.form.TextField', {
        	margin : '5',
        	hidden : true,
        	readOnly : true,
        	labelWidth : 80,
        	width : 160,
        	xtype : 'textfield'
        });
     // The data store containing the list of states
        var states = Ext.create('Ext.data.Store', {
            fields: ['abbr', 'name'],
            data : [
                {"abbr":"运行", "name":"Alabama"},
                {"abbr":"完成", "name":"Alaska"},
                {"abbr":"终止", "name":"Arizona"}
                //...
            ]
        });

        // Create the combo box, attached to the states data store
        var comb = Ext.create('Ext.form.ComboBox', {
            store: states,
            queryMode: 'local',
            displayField: 'abbr',
            valueField: 'name',
            emptyText:'--请选择状态--'
        });
        var form = Ext.create('Ext.form.Panel', {
        	buttonAlign : 'right',
        	region : 'north',
        	border : false,
        	baseCls : 'customize_gray_back',
        	items : [ {
        		xtype : 'toolbar',
        		baseCls : 'customize_gray_back',
        		items : [ projectNameForQuery, comb,
        				queryButtonForPRJ, resetBtn, '->', addButtonForPRJ,
        				addButtonForSaveAs, importButtonForPRJ ]
        	} ]
        });
        var mainPanel = Ext.create('Ext.panel.Panel', {
        	id : 'lbMainPanel',
        	renderTo : Ext.get('project_grid_area'),
        	layout : 'border',
        	border : false,
        	width : '100%',
        	height : $('body').height() - 100,
        	items : [ form, grid_panel ]
        });
});
