package com.ideal.service.stepInstanceConfig.systemtemplate;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.ideal.common.utils.Tools;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.eswitch.common.EswitchException;
import com.ideal.ieai.server.eswitch.repository.esbusinesstype.EsbusinesstypeModel;
import com.ideal.ieai.server.eswitch.repository.essysexcel.EsExeclInfo;
import com.ideal.ieai.server.eswitch.repository.essysexcel.EsParamBean;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.stepInstanceConfig.connertemplate.ConnerTemplateManage;
import com.ideal.ieai.server.repository.hd.stepInstanceConfig.sharetemplate.ShareTemplateManage;
import com.ideal.ieai.server.repository.hd.stepInstanceConfig.systemtemplate.SysModel;
import com.ideal.ieai.server.repository.hd.stepInstanceConfig.systemtemplate.SystemTemplateManage;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.rollBackTool.RollBackTool;
import com.ideal.ieai.server.util.BeanFormatter;
import com.ideal.service.eswitch.essysexcel.NewEsexcelService;
import com.ideal.util.CollectionUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class SystemTemplateService
{
    private Logger log = Logger.getLogger(SystemTemplateService.class);
    private static final Logger dbsRollbacklog  = Logger.getLogger("dbSRblog");
    private static final String MESSAGE = "message";
    private static final String SUCCESS = "success";
    private static final String BASECONN = "baseConn";
    private static final String DBCONNS = "dbConns";
    private static final String UPDATEERROR = "执行信息变更sql时出现错误！";
    private static final String SAVESUCCESS = "数据保存成功！";
    private static final String NOBASECONN = "没有基线源, 无法保存！";
    private static final String ORGANIZEERROR = "组织信息sql时出现错误！";
    private static final String EXESQLS = "exeSqls";
    private static final String ROLLBACKSQLS = "rollbackSqls";
    private static final String DATALIST = "dataList";
    private static final String TOTAL = "total";
    private static final String IPARENTID = "iparentid";
    private static final String UNION = " UNION ";
    private static final String MXCELL        = "mxCell";
    private static final String PARENT        = "parent";
    private static final String SOURCE        = "source";
    private static final String TARGET        = "target";
    private static final String VERTEX        = "vertex_";
    private static final String EDGE          = "edge";
    private static final String IEAI_SYSTEM_TEMPLATE = "IEAI_SYSTEM_TEMPLATE";
    private static final String IEAI_SYS_CONNER = "IEAI_SYS_CONNER";
    private static final String IEAI_SYS_XML = "IEAI_SYS_XML";
    private static final String SYSID = "SYSID";
    private static final String CONNERID = "connerid";
    private static final String DELETE = "delete";
    private static final String INSERT = "insert";
    private static final String UPDATE = "update";
    private static final String OPTTYPE = "operationType";
    private static final String TABLENAME = "tableName";
    private static final String DASECONN = "baseConn";
    private static final String DBCONN = "dbConns";
    
    //查询系统切换方向树信息
    public List getSysTemplateList ( String queryString, int sysType )
    {
        Connection conn = null;
        SystemTemplateManage manage = new SystemTemplateManage();
        List list = new ArrayList();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List<Map> rootList = manage.getRootNodeList(queryString,conn);
            if(rootList!=null && !rootList.isEmpty()){
                for(int i=0;i<rootList.size();i++){
                    Map manaMap = rootList.get(i);
                    manaMap.put("checked", false);
                    this.getChildren(conn,(Long)manaMap.get("iid"),manaMap);
                    list.add(manaMap);
                }
            }
        } catch (Exception e)
        {
            log.error("SystemTemplateService.getSysTemplateList is error", e);
        }finally{
            DBResource.closeConnection(conn, "getSysTemplateList", log);
        }
        return list;
    }
    //获取子节点信息
    private void getChildren ( Connection conn, Long iid, Map map )
    {
        SystemTemplateManage manage = new SystemTemplateManage();
        List<Map> list = new ArrayList();
        try
        {
            list = manage.getChildren(conn,iid);
            map.put("children", list);
            if(list!=null && !list.isEmpty()){
                for(int j=0;j<list.size();j++){
                    Map  maps = list.get(j);
                    this.getChildren(conn, (Long)maps.get("iid"), maps);
                }
            }
        } catch (Exception e)
        {
            log.error("SystemTemplateService.getChildren is error", e);
        }
    }
    
    //保存系统名称或切换方向
    public Map saveSystemTemplate ( Long iid, String iname, Long iparentid ) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = null;
        try
        {
            connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get(BASECONN);
            if (null != baseConn)
            {
                SystemTemplateManage manage = new SystemTemplateManage();
                //判断名称是否重复
                int count = manage.getInameCount(baseConn,iname,iparentid,iid);
                if(count>0) {
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, iid==0?"该系统:":"该切换方向:"+iname+"已存在，请修改！");
                    return res;
                }

                List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONNS);
                Map orgSql = organizeSaveSystemTemplateSql(iid, iname, iparentid, baseConn);
                if ((Boolean) orgSql.get(SUCCESS))
                {
                    if (DBUtil.hitDatabase(dbConns,(List<String>) orgSql.get(EXESQLS),(List<String>) orgSql.get(ROLLBACKSQLS)))
                    {
                        DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                        res.put(SUCCESS, true);
                        res.put(MESSAGE, SAVESUCCESS);
                        return res;
                    } else
                    {
                        DBResource.closeConnection(baseConn,Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                        res.put(SUCCESS, false);
                        res.put(MESSAGE, UPDATEERROR);
                        return res;
                    }
                } else
                {
                    DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                    }
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, ORGANIZEERROR);
                    return res;
                }
            } else
            {
                res.put(SUCCESS, false);
                res.put(MESSAGE, NOBASECONN);
                return res;
            }
        } catch (Exception e)
        {
            log.error("SystemTemplateService.saveSystemTemplate is error", e);
        }finally {
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONNS);
            for (Connection conn : dbConns)
            {
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            }
        }
        return res;
    }

    //保存系统名称或切换方向多写
    private Map organizeSaveSystemTemplateSql ( Long iid, String iname, Long iparentid, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList();
        List<String> rollbackSqls = new ArrayList();
        SystemTemplateManage manage = new SystemTemplateManage();
        
        String insertManaSql = "insert into ieai_system_template( IID, INAME,IPARENTID ) values ({iid},'{iname}',{iparentid})";
        String deleteManaSql = "delete from ieai_system_template where IID=";
        String updateManaSql = "update ieai_system_template set iid={iid},iname='{iname}',istate={istate} where iid={iid}";
        try
        {
            if(iid==0){
                long id = IdGenerator.createId(IEAI_SYSTEM_TEMPLATE, baseConn);
                Map beanMap = new HashMap();
                beanMap.put("iid", id);
                beanMap.put("iname", iname);
                beanMap.put(IPARENTID, iparentid);
                BeanFormatter bf = new BeanFormatter(insertManaSql);
                String s = bf.formatMap(beanMap);
                exeSqls.add(s);
                rollbackSqls.add(deleteManaSql + id);
            }else {
                Map beanMap = new HashMap();
                beanMap.put("iid", iid);
                beanMap.put("iname", iname);
                beanMap.put(IPARENTID, iparentid);
                beanMap.put("istate", 1);
                BeanFormatter bf = new BeanFormatter(updateManaSql);
                String s = bf.formatMap(beanMap);
                exeSqls.add(s);
                Map returnMap = manage.getSystemTemplateOne(Long.parseLong(String.valueOf(beanMap.get("iid"))), baseConn);
                if (null != returnMap)
                {
                    BeanFormatter rbf = new BeanFormatter(updateManaSql);
                    String r = rbf.formatMap(returnMap);
                    rollbackSqls.add(r);
                }
            }
        } catch (Exception e)
        {
            isSuccess = false;
            log.error("SystemTemplateService.saveSystemTemplate is error", e);
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    //删除系统或方向
    public Map deleteSysTemplate ( Long iid,Long parentiid ) throws RepositoryException, DBException
    {
        int exceptionPosition=0;
        SystemTemplateManage manage = new SystemTemplateManage();
        Map res = new HashMap();
        List rollback = new ArrayList();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(BASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONNS);
            try
            {
                List templataList = new ArrayList();
                templataList.add(iid);
                if(parentiid==0) {//父节点
                   manage.getChildrenIds(iid,templataList,baseConn);
                }
                List connerList = manage.getListInfo(templataList,baseConn,IEAI_SYS_CONNER);
                List xmlList = manage.getListInfo(templataList,baseConn,IEAI_SYS_XML);
                
                rollback = getRollBack(templataList,connerList,xmlList,baseConn);
                
                deleteSysTemplateOne(exceptionPosition,dbConns,templataList,connerList,xmlList);
                
                res.put(SUCCESS, true);
                res.put(MESSAGE,"删除成功！");
            } catch (Exception e)
            {
                res.put(SUCCESS, false);
                res.put(MESSAGE, e.getMessage());
                
                List<Object> loglist = new ArrayList();
                try
                {
                    RollBackTool.getInstance().dbRollBack(rollback, exceptionPosition, loglist, "");
                } catch (Exception e1)
                {
                    for (int logi = 0; logi < loglist.size(); logi++)
                    {
                        String rRoll = (String) loglist.get(logi);
                        dbsRollbacklog.error(rRoll);
                    }
                    log.error("SystemTemplateService.deleteSysTemplate error!", e1);
                }
                log.error("SystemTemplateService.deleteSysTemplate is error", e);
                throw new RepositoryException(ServerError.ERR_DB_DELETE);
            }finally {
                DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
            }
        } else
        {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有基线源, 无法删除！");
            return res;
        }
        return res;
    }
    
    private void deleteSysTemplateOne (int exceptionPosition, List<Connection> dbConns, List templataList, List connerList, List xmlList) throws RepositoryException
    {
        SystemTemplateManage manage = new SystemTemplateManage();
        for(Connection conn : dbConns) {
            try
            {
                manage.deleteSysTemplate(conn,templataList,IEAI_SYSTEM_TEMPLATE);
                manage.deleteSysTemplate(conn,connerList,IEAI_SYS_CONNER);
                manage.deleteSysTemplate(conn,xmlList,IEAI_SYS_XML);
                conn.commit();
                exceptionPosition++;
            } catch (Exception e)
            {
                log.error(Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                try
                {
                    DBResource.rollback(conn, ServerError.ERR_DB_ROLLBACK, e, "deleteSysTemplate", log);
                } catch (Exception e2)
                {
                    log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e2);
                }
                throw new RepositoryException(ServerError.ERR_DB_DELETE);
            }finally {
                DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            }
        }
    }
    
    private List getRollBack (List templataList, List connerList, List xmlList, Connection baseConn) throws RepositoryException
    {
        List rollback = new ArrayList();
        if(CollectionUtils.isNotEmpty(templataList)) {
            for(int i=0;i<templataList.size();i++) {
                long iid = (long) templataList.get(i);
                List tempList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYSTEM_TEMPLATE, "IID",iid, DELETE);
                rollback.addAll(tempList);
            }
        }
        if(CollectionUtils.isNotEmpty(connerList)) {
            for(int i=0;i<connerList.size();i++) {
                long iid = (long) connerList.get(i);
                List tempList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYS_CONNER, SYSID,iid, DELETE);
                rollback.addAll(tempList);
            }
        }
        if(CollectionUtils.isNotEmpty(xmlList)) {
            for(int i=0;i<xmlList.size();i++) {
                long iid = (long) xmlList.get(i);
                List tempList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYS_XML, SYSID,iid, DELETE);
                rollback.addAll(tempList);
            }
        }
        return rollback;
    }
    //查询引用左侧顺序信息（我的和共享的）
    public Map getAllConnerList ( Integer start, Integer limit,String queryString, Long userId,int sysType )
    {
        Connection conn = null;
        ShareTemplateManage manage = new ShareTemplateManage();
        ConnerTemplateManage ctm = new ConnerTemplateManage();
        Map map = new HashMap();
        
        StringBuilder sqlCount = new StringBuilder();
        String sqlWhere = "";
        if (queryString != null && !"".equals(queryString))
        {
            sqlWhere = "  where (iconnername like '%"+queryString.trim()+"%' or iconnerdes like '%"+queryString.trim()+"%') ";
        }
        
        String sqlOne = "select A.IID,A.ICONNERNAME,A.ICONNERDES,A.ICLASSIDONE,A.ICLASSIDTWO,B.IFULLNAME " + 
                "from IEAI_MYTEMPLATE A LEFT JOIN IEAI_USER B ON A.ICREATERID=B.IID  where ICREATERID="+userId +" and ISHARESTATE=0 ";
        
        String sqlTwo = "SELECT A.IID,A.ICONNERNAME,A.ICONNERDES,A.ICLASSIDONE,A.ICLASSIDTWO,U.IFULLNAME " + 
                "FROM IEAI_MYTEMPLATE A LEFT JOIN IEAI_MYTEMPLATE_USER_REALATION R ON R.ICONNERID = A.IID LEFT JOIN IEAI_USER U ON A.ICREATERID=U.IID " + 
                "WHERE R.IUSERID = "+userId+" OR R.IUSERID = 0 ";
        
        StringBuilder sqlList = new StringBuilder();
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList.append("select IID,ICONNERNAME,ICONNERDES,ICLASSIDONE,ICLASSIDTWO,IFULLNAME from (");
            sqlList.append(sqlOne);
            sqlList.append(UNION);
            sqlList.append(sqlTwo);
            sqlList.append(") as temp");
            sqlList.append(sqlWhere);
            sqlList.append(" order by ICONNERNAME LIMIT ?,? ");
            
            sqlCount.append("select count(*) as countNum from (");
            sqlCount.append(sqlOne);
            sqlCount.append(UNION);
            sqlCount.append(sqlTwo);
            sqlCount.append(") as temp");
            sqlCount.append(sqlWhere);
        } else
        {
            sqlList.append("select * from (");
            sqlList.append("select row_number() over (order by ICONNERNAME) AS ro,IID,ICONNERNAME,ICONNERDES,ICLASSIDONE,ICLASSIDTWO,IFULLNAME from (");
            sqlList.append(sqlOne);
            sqlList.append(UNION);
            sqlList.append(sqlTwo);
            sqlList.append(")");
            sqlList.append(sqlWhere);
            sqlList.append(") where ro>? and ro<=?");
            
            sqlCount.append("select count(*) as countNum from (");
            sqlCount.append(sqlOne);
            sqlCount.append(UNION);
            sqlCount.append(sqlTwo);
            sqlCount.append(")");
            sqlCount.append(sqlWhere);
        }
        
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = manage.getShareTemplateList(conn, sqlList.toString(), start, limit);
            int count = ctm.getCount(conn, sqlCount.toString());
            map.put(DATALIST, list);
            map.put(TOTAL, count);
        }catch (Exception e)
        {
            log.error("SystemTemplateService.getAllConnerList is error", e);
        }finally{
            DBResource.closeConnection(conn, "getAllConnerList", log);
        }
        return map;
    }

    //查询选中的顺序
    public Map getSelectConnerList ( Long iid,int sysType)
    {
        Connection conn = null;
        SystemTemplateManage manage = new SystemTemplateManage();
        ConnerTemplateManage ctm = new ConnerTemplateManage();
        Map map = new HashMap();
        String sqlList = "select a.iid,a.CONNERID,b.ICONNERNAME,b.ICONNERDES,b.ICLASSIDONE,b.ICLASSIDTWO,a.IORDER from IEAI_SYS_CONNER a,IEAI_MYTEMPLATE b where a.CONNERID=b.IID and a.SYSID="+iid+" ORDER BY IORDER";
        String sqlCount = "select count(*) as countNum from IEAI_SYS_CONNER a,IEAI_MYTEMPLATE b where a.CONNERID=b.IID and a.SYSID="+iid;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = manage.getSelectConnerList(conn, sqlList);
            int count = ctm.getCount(conn, sqlCount);
            map.put(DATALIST, list);
            map.put(TOTAL, count);
        }catch (Exception e)
        {
            log.error("SystemTemplateService.getSelectConnerList is error", e);
        }finally{
            DBResource.closeConnection(conn, "getSelectConnerList", log);
        }
        return map;
    }

    //读取xml信息
    public String getSysXML ( String iid, int sysType)
    {
        Connection conn = null;
        SystemTemplateManage manage = new SystemTemplateManage();
        String xml = "";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            xml = manage.getSysXML(conn,iid);
            if (xml == null || "".equals(xml))
            {
                xml = "<mxGraphModel><root><mxCell id=\"0\"/><mxCell id=\"1\" parent=\"0\"/></root></mxGraphModel>";
                return xml;
            }
        } catch (Exception e)
        {
            log.error("SystemTemplateService.getSysXML is error", e);
        }finally {
            DBResource.closeConnection(conn, "getSysXML", log);
        }
        return xml;
    }

    public Map saveConerRelation ( String jsonData, Long dirid ) throws RepositoryException, DBException
    {
        List<SysModel> list = jsonTranslate(jsonData,dirid);
        Map map = new HashMap();
        List rollback = new ArrayList();
        
        int exceptionPosition = 0;
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(BASECONN);
        
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONNS);
            try
            {
                //记录IEAI_SYS_CONNER下的信息
                  List tempList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYS_CONNER, SYSID,dirid, DELETE);
                  rollback.addAll(tempList);
                //记录IEAI_SYS_XML下的信息
                  List insertList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYS_XML, SYSID,dirid, DELETE);
                  rollback.addAll(insertList); 
                  //记录IEAI_SYSTEM_TEMPLATE下的信息
                  List sysList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYSTEM_TEMPLATE, "IID",dirid, UPDATE);
                  rollback.addAll(sysList); 
                  
                  for(SysModel model:list) {
                      long id = IdGenerator.createId(IEAI_SYS_CONNER, baseConn);
                      model.setIid(id);
                      
                      Map tmap = new HashMap();
                      tmap.put(TABLENAME, IEAI_SYS_CONNER);
                      tmap.put(OPTTYPE, INSERT);
                      tmap.put("IID", id);
                      rollback.add(tmap);
                  }
                  //生成xml图形
                  String xml = createXml(list);
                  long xmlid = IdGenerator.createId(IEAI_SYS_XML, baseConn);
                  
                  Map tmap = new HashMap();
                  tmap.put(TABLENAME, IEAI_SYS_XML);
                  tmap.put(OPTTYPE, INSERT);
                  tmap.put("IID", xmlid);
                  rollback.add(tmap);
              
                saveMethod(exceptionPosition,dbConns,dirid,xml,xmlid,list);

                map.put(SUCCESS, true);
                map.put(MESSAGE, "保存成功");
            } catch (Exception e)
            {
                map.put(SUCCESS, false);
                map.put(MESSAGE, e.getMessage());
                List<Object> loglist = new ArrayList();
                try
                {
                    RollBackTool.getInstance().dbRollBack(rollback, exceptionPosition, loglist, "");
                } catch (Exception e1)
                {
                    for (int logi = 0; logi < loglist.size(); logi++)
                    {
                        String rRoll = (String) loglist.get(logi);
                        dbsRollbacklog.error(rRoll);
                    }
                    log.error("SystemTemplateService.saveConerRelation error!", e1);
                }
                log.error("SystemTemplateService.saveConerRelation is error", e);
                throw new RepositoryException(ServerError.ERR_DB_INSERT);
            }
        }else{
            map.put(SUCCESS, false);
            map.put(MESSAGE, "没有基线数据源");
            return map;
        }
        return map;
    }
    private void saveMethod (int exceptionPosition, List<Connection> dbConns, Long dirid, String xml, long xmlid, List<SysModel> list) throws RepositoryException
    {
        SystemTemplateManage manage = new SystemTemplateManage();
        for(Connection con:dbConns)
        {
            try
            {
                //先删除IEAI_SYS_CONNER再insert
                manage.operateSysConner(con,dirid,list);
                //先删除再保存IEAI_SYS_XML图形
                manage.saveSysXml(con,dirid,xmlid,xml);
                //更新IEAI_SYSTEM_TEMPLATE状态
                manage.updateTemplateState(con,dirid);
                con.commit();
                exceptionPosition++;
            } catch (Exception e)
            {
                log.error(Thread.currentThread().getStackTrace()[1].getMethodName(),e);
                try
                {
                    DBResource.rollback(con, ServerError.ERR_DB_ROLLBACK, e, "saveMethod", log);
                } catch (Exception e2)
                {
                    log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e2);
                }
                throw new RepositoryException(ServerError.ERR_DB_INSERT);
            }finally {
                DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            }
        }
    }
    //生成xml
    private String createXml ( List<SysModel> list )
    {
        List mapList = reList(list);
        String backXml = "<mxGraphModel><root><mxCell id=\"-1\"/><mxCell id=\"0\" parent=\"-1\"/></root></mxGraphModel>";
        try
        {
            String xml = "<mxGraphModel><root></root></mxGraphModel>";
            Document document = DocumentHelper.parseText(xml);
            Element element = document.getRootElement();
            Iterator<Element> iterator = element.elementIterator();
            while (iterator.hasNext())
            {
                Element e = iterator.next();
                Element eSon1 = e.addElement(MXCELL);
                eSon1.addAttribute("id", "-1");
                Element eSon2 = e.addElement(MXCELL);
                eSon2.addAttribute("id", "0");
                eSon2.addAttribute(PARENT, "-1");
                int y = 20;
                if(!CollectionUtils.isEmpty(list)) {
                    for(SysModel model:list) {
                        Element eSon = e.addElement(MXCELL);
                        eSon.addAttribute("id", VERTEX + model.getIid());
                        eSon.addAttribute(PARENT, "0");
                        eSon.addAttribute("value", model.getIconnername());
                        eSon.addAttribute("vertex", "1");
                        eSon.addAttribute(CONNERID, String.valueOf(model.getConnerid()));

                        Element eSonSon = eSon.addElement("mxGeometry");
                        eSonSon.addAttribute("x", "50");
                        eSonSon.addAttribute("y", String.valueOf(y));
                        eSonSon.addAttribute("width", "160");
                        eSonSon.addAttribute("height", "60");
                        eSonSon.addAttribute("as", "geometry");
                        y = y+100;
                    }
                }
                for(int j=0;j<mapList.size();j++) {
                    Map map = (Map) mapList.get(j);
                    Element eaSon = e.addElement(MXCELL);
                    eaSon.addAttribute("id", "edge_" + map.get(SOURCE) + "_" + map.get(TARGET));
                    eaSon.addAttribute(SOURCE, VERTEX + map.get(SOURCE));
                    eaSon.addAttribute(TARGET, VERTEX + map.get(TARGET));
                    eaSon.addAttribute("edge", "1");
                    eaSon.addAttribute(PARENT, "0");
    
                    Element ebSonSon = eaSon.addElement("mxGeometry");
                    ebSonSon.addAttribute("relative", "1");
                    ebSonSon.addAttribute("as", "geometry");
                }    
            }
            backXml = document.asXML();
        } catch (Exception e)
        {
            log.error("SystemTemplateService.createXml is error", e);
        }
       return backXml;
    }
    
    private List reList ( List<SysModel> list )
    {
        List mapList = new ArrayList();
        long iid = -1;
        for(int i=0;i<list.size();i++) {
            SysModel model = list.get(i);
            if(i==0) {
                iid =  model.getIid(); 
            }else {
                Map map = new HashMap();
                map.put(SOURCE, iid);
                map.put(TARGET, model.getIid());
                mapList.add(map);
                iid = model.getIid();
            }
        }
        return mapList;
    }
    //解析数据
    private List<SysModel> jsonTranslate ( String jsonData,Long dirid )
    {
        List<SysModel> dataList = new ArrayList();
        JSONArray jsonArr = JSONArray.fromObject(jsonData);
        SysModel model = null;
        String connerid = "";
        String iorder = "";
        String connerName = "";

        for (int i = 0; i < jsonArr.size(); i++)
        {
            model = new SysModel();
            connerid = jsonArr.getJSONObject(i).optString(CONNERID);
            iorder = jsonArr.getJSONObject(i).optString("iorder");
            connerName = jsonArr.getJSONObject(i).optString("iconnername");

            model.setSysid(dirid);
            model.setConnerid(Long.valueOf(connerid));
            model.setIorder(Long.valueOf(iorder));
            model.setIconnername(connerName);
            dataList.add(model);
        }
        return dataList;
    }
    
    //保存修改后的图形及顺序之间的关系
    public Map sysXmlUpdate ( Long iid, String sysxml ) throws RepositoryException, DBException
    {
        Map map = new HashMap();
        Map<String,Object> resMap = new HashMap();
        List rollback = new ArrayList();
        
        int exceptionPosition = 0;
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(BASECONN);
        
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONNS);
            //解析xml
            readXml(sysxml,resMap);
            List connerList = (List) resMap.get(DATALIST);
            List modelList = new ArrayList();
            String xml = "";
            long xmlid = -1;
            try
            {
                //记录IEAI_SYS_CONNER下的信息
                List tempList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYS_CONNER, SYSID,iid, DELETE);
                rollback.addAll(tempList);
                
                List insertList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYS_XML, SYSID,iid, DELETE);
                rollback.addAll(insertList); 
                
                //记录IEAI_SYSTEM_TEMPLATE下的信息
                List sysList = RollBackTool.getInstance().getDataModel(baseConn, IEAI_SYSTEM_TEMPLATE, "IID",iid, UPDATE);
                rollback.addAll(sysList); 
                
                for(int j=0;j<connerList.size();j++) {
                    Map conmap = (Map) connerList.get(j);
                    SysModel model = new SysModel();
                    long id = IdGenerator.createId(IEAI_SYS_CONNER, baseConn);
                    model.setIid(id);
                    model.setSysid(iid);
                    model.setConnerid(Long.valueOf(String.valueOf(conmap.get(CONNERID))));
                    model.setIconnername(String.valueOf(conmap.get("connername")));
                    model.setIorder(j+1L);
                    modelList.add(model);
                    
                    Map tmap = new HashMap();
                    tmap.put(TABLENAME, IEAI_SYS_CONNER);
                    tmap.put(OPTTYPE, INSERT);
                    tmap.put("IID", id);
                    rollback.add(tmap);
                }
                //重新组织xml图形
                xml = createXml(modelList);
                xmlid = IdGenerator.createId(IEAI_SYS_XML, baseConn);
                
                Map tmap = new HashMap();
                tmap.put(TABLENAME, IEAI_SYS_XML);
                tmap.put(OPTTYPE, INSERT);
                tmap.put("IID", xmlid);
                rollback.add(tmap);
                
                saveMethod(exceptionPosition,dbConns,iid,xml,xmlid,modelList);
               
                map.put(SUCCESS, true);
                map.put(MESSAGE, "保存成功");
            } catch (Exception e)
            {
                map.put(SUCCESS, false);
                map.put(MESSAGE, e.getMessage());
                List<Object> loglist = new ArrayList();
                try
                {
                    RollBackTool.getInstance().dbRollBack(rollback, exceptionPosition, loglist, "");
                } catch (Exception e1)
                {
                    for (int logi = 0; logi < loglist.size(); logi++)
                    {
                        String rRoll = (String) loglist.get(logi);
                        dbsRollbacklog.error(rRoll);
                    }
                    log.error("SystemTemplateService.sysXmlUpdate error!", e1);
                }
                log.error("SystemTemplateService.sysXmlUpdate is error", e);
                throw new RepositoryException(ServerError.ERR_DB_INSERT);
            }finally {
                DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
            }
        }else{
            map.put(SUCCESS, false);
            map.put(MESSAGE, "没有基线数据源");
            return map;
        }
        return map;
    }
    
    //解析xml
    private void readXml ( String sysxml,Map<String,Object> resMap )
    {
        List sList = new ArrayList();
        List tList = new ArrayList();
        List relation = new ArrayList();
        Map<String,Object> cmap = new HashMap();
        Map<String,Object> remap = new HashMap();
        try
        {
            Document document = DocumentHelper.parseText(sysxml);
            Element element = document.getRootElement();
            Iterator<Element> iterator = element.elementIterator();
            while (iterator.hasNext())
            {
                Element e = iterator.next();
                Iterator<Element> ite = e.elementIterator();
                while (ite.hasNext())
                {
                    Element eSon = ite.next();
                    if ("1".equals(eSon.attributeValue("vertex")))
                    {
                        // 处理节点list
                        Map maps = new HashMap();
                        maps.put(CONNERID, eSon.attributeValue(CONNERID));
                        maps.put("connername", eSon.attributeValue("value"));
                        cmap.put(eSon.attributeValue("id"), maps);
                    }
                    
                    if ("1".equals(eSon.attributeValue(EDGE)))
                    {
                        // 处理关系list
                        remap.put(eSon.attributeValue(SOURCE), eSon.attributeValue(TARGET));   // 线的起点-终点
                        relation.add(remap);
                        sList.add(eSon.attributeValue(SOURCE));
                        tList.add(eSon.attributeValue(TARGET));
                    }
                }
            }
            sList.removeAll(tList);
            resMap.put(DATALIST, orgRelationList(sList,cmap,remap));
        } catch (Exception e)
        {
            log.error("SystemTemplateService.readXml is error", e);
        }
    }
    
    //重新组织顺序步骤
    private List orgRelationList ( List sList, Map<String, Object> cmap,Map<String, Object> remap )
    {
        List list = new ArrayList();
        String str = "";
        for(int i=0;i<sList.size();i++) {
            str = String.valueOf(sList.get(i));//起始点    
        }
        
        while(true){
            if("".equals(str) || list.size()==cmap.size()) {
                break;
            }
            if(cmap.containsKey(str)) {
                list.add(cmap.get(str));
                str = String.valueOf(remap.get(str)==null?"":remap.get(str));
            }
        }
        return list;
    }
    
    //系统发布
    public Map submitSysTemplate ( String json,Long classid,UserInfo user,String classname )
    {
        Map resmap = new HashMap();
        NewEsexcelService ns = new NewEsexcelService();
        List idList = new ArrayList();
        List resList = new ArrayList();
        
        try
        {
          //组织系统、步骤信息
          List allList = orgSystempInfo(json,classid,idList);
          if(CollectionUtil.isNotEmpty(allList)) {
              Map resMap = new HashMap();
              EsbusinesstypeModel model = new EsbusinesstypeModel(); 
              model.setIsflag(true);
              //系统分类
              EsbusinesstypeModel es = new EsbusinesstypeModel();
              es.setIbusname(classname);
              es.setIid(classid);
              resMap.put("business", es);
              resMap.put(DATALIST, allList);
              resList.add(resMap);
              resmap = ns.hdsaveExeclZip(resList,user);
              if((boolean) resmap.get(SUCCESS)) {
                resmap.put(MESSAGE,"发布成功！");
                //更新状态
                updateState(idList);
              }
          }
          resmap.put(SUCCESS, true);
          resmap.put(MESSAGE, "发布成功！");
        } catch (Exception e)
        {
            log.error("SystemTemplateService.submitSysTemplate is error",e);
            resmap.put(SUCCESS, false);
            resmap.put(MESSAGE, "发布失败！"+e.getMessage());
        }
        return resmap;
    }
    
    private void updateState ( List idList ) throws RepositoryException, DBException, EswitchException
    {
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(DASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN);
            Map orgSql = organizeUpdateSql(idList, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),(List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                    log.info("状态更新成功！");
                } else {
                    DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                    log.error(UPDATEERROR);
                    throw new EswitchException(UPDATEERROR);
                }
            } else {
                DBResource.closeConnection(baseConn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
                }
                log.error(ORGANIZEERROR);
                throw new EswitchException(ORGANIZEERROR);
            }
        } else {
            log.error(NOBASECONN);
            throw new EswitchException(NOBASECONN);
        }
    }
    
    private Map organizeUpdateSql ( List idList, Connection baseConn ) 
    {
        SystemTemplateManage manage = new SystemTemplateManage();
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<>();
        List<String> rollbackSqls = new ArrayList<>();
        
        String updateSqlTemplate = " update IEAI_SYSTEM_TEMPLATE set istate= {istate} where iid={iid}";
        String updateSqlConner = " update IEAI_SYS_CONNER set istate= {istate} where SYSID={iid}";
        try
        {
            if(CollectionUtils.isNotEmpty(idList)) {
                for(int i=0;i<idList.size();i++) {
                    long dirid = (long) idList.get(i);
                    
                    Map map = new HashMap();
                    map.put("iid",dirid);
                    map.put("istate",0);
                    BeanFormatter bf = new BeanFormatter(updateSqlTemplate);
                    String s = bf.formatMap(map);
                    exeSqls.add(s);
                    Map infoMap = manage.getSystemTemplateOne(dirid, baseConn);
                    BeanFormatter bff = new BeanFormatter(updateSqlTemplate);
                    String ss = bff.formatMap(infoMap);
                    rollbackSqls.add(ss);
                    
                    BeanFormatter ba = new BeanFormatter(updateSqlConner);
                    String sc = ba.formatMap(map);
                    exeSqls.add(sc);
                    List<Map> conList = manage.getConnerList(dirid, baseConn);
                    for(int j=0;j<conList.size();j++) {
                        Map maps = conList.get(j);
                        BeanFormatter bb = new BeanFormatter(updateSqlConner);
                        String sb = bb.formatMap(maps);
                        rollbackSqls.add(sb);
                    }
                }
            }
        } catch (Exception e)
        {
            log.error("SystemTemplateService.organizeUpdateSql is error",e);
            isSuccess = false;
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    
    private List orgSystempInfo ( String json, Long classid,List idList ) throws RepositoryException, EswitchException
    {
        List allList = new ArrayList();
        int sysType = Constants.IEAI_EMERGENCY_SWITCH;
        Date date = new Date(); 
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMDDHHmmss");  
        String versioninfo = formatter.format(date);
        
        SystemTemplateManage manage = new SystemTemplateManage();
        NewEsexcelService ns = new NewEsexcelService();
        
        JSONArray js=JSONArray.fromObject(json);
        JSONObject jo=null;
        Iterator it=js.iterator();
        while(it.hasNext()){
            Map allMap = new HashMap();
            jo=(JSONObject)it.next();
            String proName = jo.getString("sysname");
            List list = jo.getJSONArray("children");
            if(CollectionUtils.isEmpty(list)) {
                continue;
            }
            
            EsParamBean info = new EsParamBean();
            long ciid = manage.getClasssId(sysType,proName,classid);
            info.setSysName(proName);
            info.setVersioninfo(versioninfo);//版本说明
            info.setSysType(sysType);
            info.setSysId(ciid);//分类id
            info.setIscib(ns.getIscib());
            info.setVersionNum(manage.getVersionNum(proName));//版本号
            
            List dirList = orgStepList(list,idList);
            allMap.put(DATALIST, dirList);
            allMap.put("model", info);
            allList.add(allMap);
        }
        return allList;
    }
    
    private List orgStepList ( List list,List idList ) throws EswitchException
    {
        List resList = new ArrayList();
        List infoList = null;
        
        JSONArray js=JSONArray.fromObject(list);
        JSONObject jo=null;
        Iterator it=js.iterator();
        while(it.hasNext()) {
            jo=(JSONObject)it.next();
            String dirName = jo.getString("sysname");
            long dirid = Long.parseLong(jo.getString("iid"));
            Map resmap = getInfoList(dirid,dirName,idList);
            if((boolean) resmap.get(SUCCESS)) {
                infoList = (List) resmap.get(DATALIST);
            }else {
                throw new EswitchException(String.valueOf(resmap.get(MESSAGE)));
            }
                    
            Map map = new HashMap();
            map.put("iswitchdes", dirName);
            map.put(DATALIST, infoList);
            map.put("xmliid", 0);
            resList.add(map);
        }
        return resList;
    }
    
    private Map getInfoList ( long dirid,String dirName,List idList)
    {
        NewEsexcelService ns = new NewEsexcelService();
        int conner = 0;
        int senner = 1;
        List orList = new ArrayList();
        Map map = new HashMap();
        SystemTemplateManage manage = new SystemTemplateManage();
        Map resmap = new HashMap();
        List list = new ArrayList();
        try
        {
            List infoList = manage.getInfoList(dirid);
            if(CollectionUtils.isNotEmpty(infoList)) {
                idList.add(dirid);
                for(int i=0;i<infoList.size();i++) {
                    EsExeclInfo info = (EsExeclInfo) infoList.get(i);
                    long iorder = info.getConNo();
                    if(CollectionUtils.isEmpty(infoList) || !infoList.contains(iorder)) {
                        orList.add(iorder);
                        map = new HashMap();
                        conner++;
                    }
                    info.setConNo(conner);//顺序步骤
                    map.put(info.getActNo(), senner);
                    info.setActNo(senner);//子步骤
                    senner++;
                    //依赖
                    String perner = info.getPreNo().replace("，", ",");
                    String newPer = "";
                    if(perner.contains(",")) {
                        StringBuilder newPerner = new StringBuilder();
                       String[] str = perner.split(",");
                       for(int j=0;j<str.length;j++) {
                           newPerner.append(String.valueOf(map.get(str[j]))).append(","); 
                       }
                       newPer = newPerner.toString().substring(0, newPerner.length());
                    }else {
                        newPer = String.valueOf(map.get(perner)==null?"":map.get(perner));
                    }
                    info.setPreNo(newPer);
                    //多ip
                    ns.beanToList(dirName, list, info);
                }
                
                resmap.put(SUCCESS,true );
                resmap.put(DATALIST,list);
            }
        } catch (Exception e)
        {
            resmap.put(SUCCESS,false);
            resmap.put(MESSAGE,e.getMessage());
        }
        return resmap;
    }
}
