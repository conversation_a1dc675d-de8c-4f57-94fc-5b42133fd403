package com.ideal.ieai.server.webservice.agent;

import java.util.Iterator;

import net.sf.hibernate.collection.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.ieaikernel.ServerEnv;

/**
 * <ul>
 * <li>Title: RemoteMainThread.java</li>
 * <li>Description:远程发送独立主线程</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020-6-17
 */
public class RemoteMainThread extends Thread
{
    private Logger log = Logger.getLogger(RemoteMainThread.class);

    public RemoteMainThread()
    {
    }

    String         ReuqestUUID = null;
    int            Status;
    StringMap      Output;
    byte[]         exceptionSerialize;
    int            version;
    WSActStateData stateData;
    Map            map;
    int            poolsize    = ServerEnv.getServerEnv().getReomteThreadNum();

    public void run ()
    {
        while (true)
        {
            try
            {
                if (ServerEnv.getServerEnv().getReomteThreadLogSwitch())
                {
                    if (AgentService._updateActMap.size() > 0)
                    {
                        log.info("RemoteMainThread map.size():" + AgentService._updateActMap.size());
                    }
                }
                for (Iterator iter = AgentService._updateActMap.values().iterator(); iter.hasNext();)
                {
                    if (AgentService.numjishu == poolsize)
                    {
                        if (ServerEnv.getServerEnv().getReomteThreadLogSwitch())
                        {
                            log.info("RemoteMainThread num=poolsize:" + AgentService.numjishu );
                        }
                        break;
                    }
                    Object[] oo = (Object[]) iter.next();
                    ReuqestUUID = (String) oo[5];
                    Status = (Integer) oo[0];
                    Output = (StringMap) oo[1];
                    exceptionSerialize = (byte[]) oo[2];
                    version = (Integer) oo[3];
                    stateData = (WSActStateData) oo[4];

                    Thread task = new RemoteexecThread(ReuqestUUID, Status, Output, exceptionSerialize, version,
                            stateData);
                    task.start();
                    try
                    {
                        AgentService._updateActMap.remove(ReuqestUUID);
                        AgentService.numjishu = AgentService.numjishu + 1;
                    } catch (Exception e)
                    {
                        AgentService._updateActMap.remove(ReuqestUUID);
                    }
                    if (ServerEnv.getServerEnv().getReomteThreadLogSwitch())
                    {
                        log.info("RemoteMainThread numjishu:" + AgentService.numjishu + " ReuqestUUID:" + ReuqestUUID);
                    }
                }
            } catch (Exception e)
            {
                log.error("RemoteMainThread Exception and reboot! info:" + e,e);
            }
            try
            {
                Thread.sleep(500);
            } catch (InterruptedException e)
            {
                e.printStackTrace();
            }
        }
    }

}
