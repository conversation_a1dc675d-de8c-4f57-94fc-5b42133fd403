package com.ideal.sscontroller.dubbo.scriptcompare;

import com.alibaba.fastjson.JSONObject;
import com.ideal.common.utils.SessionData;
import com.ideal.dubbo.interfaces.scriptcompare.IScriptCompare;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

@Controller
public class ScriptCompareController
{

    @DubboReference(timeout = 60000000, retries = 0)
    IScriptCompare scriptCompare;

    private static final Logger logger              = Logger.getLogger(ScriptCompareController.class);
    /**
     * 页面跳转
     * @param request
     * @return
     */
    @RequestMapping("productDisRecoveryExcelCompare.do")
    public String scriptServicesTaskExec ( HttpServletRequest request )
    {
        return "dubbo/scriptcompare/scriptcompare";
    }

    /**
     * 数据查询
     * @param request
     * @param start
     * @param limit
     * @return
     * @throws Exception
     */
    @RequestMapping("getScriptCompareList.do")
    @ResponseBody
    public Map getScriptCompareList (HttpServletRequest request, Integer start, Integer limit) throws Exception
    {
        SessionData sd = SessionData.getSessionData(request);
        String sort = request.getParameter("sort");
        Map map = scriptCompare.getScriptCompareList(start, limit);
        return map;
    }


    /**
     * 接收一致性比对文件
     * @param file
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("getCompareTitle.do")
    public void getCompareTitle (@RequestParam("fileName") CommonsMultipartFile file,
                                        HttpServletRequest request, HttpServletResponse response ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        InputStream fis = null;
        //文件名称
        String filename = file.getOriginalFilename();
        fis = file.getInputStream();
        //将excel转换成比特数组
        byte[] excelByte = transByte(fis);
        //获取表头
        resp = scriptCompare.getExcelTitle(excelByte,filename);
        response.getWriter().write(JSONObject.toJSON(resp).toString());
    }

    //将excel转换成比特数组
    private final byte[] transByte ( InputStream inStream ) throws IOException
    {
        ByteArrayOutputStream swapSteam = null;
        swapSteam = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inStream.read(buff, 0, 100)) > 0)
        {
            swapSteam.write(buff, 0, rc);
        }
        return swapSteam.toByteArray();
    }

    /**
     * 上传提交文件
     * @param file
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("uploadCompareFile.do")
    @ResponseBody
    public Map uploadCompareFile (@RequestParam("fileName") CommonsMultipartFile file,String [] titles,
                                        HttpServletRequest request, HttpServletResponse response ) throws Exception
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        InputStream fis = null;
        //文件名称
        String filename = file.getOriginalFilename();
        fis = file.getInputStream();
        //将excel转换成比特数组
        byte[] excelByte = transByte(fis);
        //获取当前登录用户
        SessionData session = SessionData.getSessionData(request);
        String loginName = session.getLoginName();
        //比对、上传excel
        resp = scriptCompare.uploadCompareFile(excelByte,filename,titles,loginName);
        return resp;
    }

    /**
     * 下载excel
     * @param iid
     * @param isTempFlag
     * @param request
     * @param response
     */
    @RequestMapping("downloadExcelAttachment.do")
    @ResponseBody
    public void downloadScriptAttachment ( HttpServletRequest request, HttpServletResponse response , Long iid , String type)
    {
        try
        {
            Map map = scriptCompare.downloadExcelAttachment(iid,type);
            InputStream fis = new ByteArrayInputStream((byte[]) map.get("icontent"));
            // 以流的形式下载文件
            String downloadFileName = "";
            if("new".equals(type)){
                downloadFileName = "比对结果_"+String.valueOf(map.get("fileName"));
            }else{
                downloadFileName = String.valueOf(map.get("fileName"));
            }
            response.setContentType("application/file;charset=UTF-8");
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + new String(downloadFileName.getBytes("GB2312"), "ISO8859-1") + "\"");
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            byte[] buf = new byte[2048];
            int c = 0;
            while ((c = fis.read(buf)) != -1)
            {
                toClient.write(buf, 0, c);
            }
            fis.close();
            toClient.flush();
            toClient.close();
        } catch (IOException e)
        {
            logger.error("downloadScriptAttachment error" + " : ", e);
        }
    }


    @RequestMapping("deleteCompareFile.do")
    @ResponseBody
    public Object deleteCompareFile ( Long iid )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            boolean isOK = scriptCompare.deleteCompareFile(iid);
            if (isOK)
            {
                resp.put("success", true);
                resp.put("message", "删除成功!");
            } else
            {
                resp.put("success", false);
                resp.put("message", "删除失败!");
            }
        } catch (Exception e)
        {
            resp.put("success", false);
            resp.put("message", "删除失败");
            logger.error("deleteCompareFile error : "+e);
        }
        return resp;
    }
}
