Ext.onReady(function() {
	var sysID;
	var busID;
	var scriptViewWin2;
    // 清理主面板的各种监听时间
    destroyRubbish();
    
    var attachmentIds = [];
    var chosedIps = chosedIps_hspe.split(',');
    var startData={};
    var globalParams={};
    var finalChosedAgentsAndDbSources = {};
    var selCpId = -1;
    var chosedAgentWin;
	var chosedAgentIds = chosedIps;
	var chosedAgentObjects = new Array();
	var upldWin;
	var upLoadformPane = '';
	var cpdsMap = cpdsMap1;//<cpid,dsid>
    Ext.tip.QuickTipManager.init();
	
	Ext.Ajax.request({
		url : 'getAllAgentListForSsExec.do',
		async: false,
		method : 'POST',
		params : {
			workItemId : workItemId_hspe,
			dbaasSwitch : 0,
            start:0,
            limit:99999999,
		},
		success : function(response, request) {
			var data = Ext.decode(response.responseText);
			Ext.each(data,function(record){
				var agentId = record.iid;
				var dbId = record.dsId;
				if(dbId>0) {
					finalChosedAgentsAndDbSources[agentId] = dbId;
				}
			});
		},
		failure : function(result, request) {
			Ext.Msg.alert('提示', '请求失败！');
		}
	}); 
    
    
    var bussData = Ext.create('Ext.data.Store', {
        fields: ['iid', 'bsName'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsAll.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussTypeData = Ext.create('Ext.data.Store', {
        fields: ['sysTypeId', 'sysType'],
        autoLoad: false,
        proxy: {
            type: 'ajax',
            url: 'bsManager/getBsTypeByFk.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });

    var bussCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'sysName',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '一级分类',
        padding: '0 5 0 0',
        displayField: 'bsName',
        valueField: 'iid',
        editable: false,
        readOnly: true,
        emptyText: '--请选择一级分类--',
        store: bussData,
        listeners: {
            change: function() { // old is keyup
                bussTypeCb.clearValue();
                bussTypeCb.applyEmptyText();
                bussTypeCb.getPicker().getSelectionModel().doMultiSelect([], false);
                bussTypeData.load({
                    params: {
                        fk: this.value
                    }
                });
            }
        }
    });

    /** 工程类型下拉框* */
    var bussTypeCb = Ext.create('Ext.form.field.ComboBox', {
        name: 'bussType',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '业务类型',
        displayField: 'sysType',
        valueField: 'sysTypeId',
        editable: false,
        readOnly: true,
        emptyText: '--请选择业务类型--',
        store: bussTypeData
    });

    bussData.on('load', function(store, options) {
        bussCb.setValue(sysID);
        bussTypeData.load({
            params: {
                fk: sysID
            }
        });

    });

    bussTypeData.on('load', function(store, options) {
        bussTypeCb.setValue(busID);
    });

    Ext.define('editScriptModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        },
        {
            name: 'serviceName',
            type: 'string'
        },
        {
            name: 'sysName',
            type: 'string'
        },
        {
            name: 'bussName',
            type: 'string'
        },
        {
            name: 'scriptType',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'servicePara',
            type: 'string'
        },
        {
            name: 'serviceState',
            type: 'string'
        },
        {
            name: 'excepResult',
            type: 'string'
        },
        {
            name: 'errExcepResult',
            type: 'string'
        },
        {
            name: 'content',
            type: 'string'
        }]
    });
    
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'scriptService/queryOneService.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
            iid: iid_hspe,
            fromType: 1 // 查ieai_script_services表
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
        var reader = store.getProxy().getReader();
        scName.setValue(reader.jsonData.scriptName);
        sName.setValue(reader.jsonData.serviceName);
        excepResult.setValue(reader.jsonData.excepResult);
        errExcepResult.setValue(reader.jsonData.errExcepResult);

        usePlantForm.setValue(reader.jsonData.platForm);
        funcDesc.setValue(reader.jsonData.funcDesc);

       // var scriptT = reader.jsonData.scriptType;
//        console.log(scriptT);
//        if (scriptT == 'sh') {
//        	FieldContainer.items.items[0].setValue(true);
//            checkRadio = 0;
//            editor.setOption("mode", 'shell');
//        } else if (scriptT == 'bat') {
//        	FieldContainer.items.items[1].setValue(true);
//            checkRadio = 1;
//            editor.setOption("mode", 'bat');
//        } else if (scriptT == 'py') {
//        	FieldContainer.items.items[3].setValue(true);
//            checkRadio = 3;
//            editor.setOption("mode", 'python');
//        } else if (scriptT == 'sql') {
//        	FieldContainer.items.items[4].setValue(true);
//            checkRadio = 4;
//            editor.setOption("mode", 'sql');
//        } else if (scriptT == 'perl') {
//        	FieldContainer.items.items[2].setValue(true);
//            checkRadio = 2;
//            editor.setOption("mode", 'text/x-perl');
//        }
//        editor.setOption('value', reader.jsonData.content);
        sysID = parseInt(reader.jsonData.sysName);
        busID = parseInt(reader.jsonData.bussName);
        bussData.load();
    });
    /** *********************Panel********************* */
    var sName = new Ext.form.TextField({
        name: 'serverName',
        fieldLabel: '服务名称',
        displayField: 'serverName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var scName = new Ext.form.TextField({
        name: 'scriptName',
        fieldLabel: '脚本名称',
        displayField: 'scriptName',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });

    var usePlantForm = Ext.create('Ext.form.field.ComboBox', {
        name: 'useplantform',
        padding: '0 5 0 0',
        labelWidth: 65,
        columnWidth: 1,
        queryMode: 'local',
        fieldLabel: '适用平台',
        displayField: 'text',
        valueField: 'value',
        editable: false,
        readOnly: true,
        emptyText: '--请选择平台--',
        store: new Ext.data.SimpleStore({
            fields: ['value', 'text'],
            data: [['Windows', 'Windows'], ['Linux', 'Linux'], ['Unix', 'Unix'], ['Linux/Unix', 'Linux/Unix']]
        })
    });
    var excepResult = new Ext.form.TextField({
        name: 'excepResult',
        fieldLabel: '预期结果',
        displayField: 'excepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var errExcepResult = new Ext.form.TextField({
        name: 'errExcepResult',
        fieldLabel: '异常结果',
        displayField: 'errExcepResult',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1
    });
    var funcDesc = Ext.create('Ext.form.field.TextArea', {
        name: 'funcdesc',
        fieldLabel: '功能概述',
        displayField: 'funcdesc',
        emptyText: '',
        labelWidth: 65,
        readOnly: true,
        padding: '0 5 0 0',
        columnWidth: 1,
        height: 130,
        autoScroll: true
    });
    Ext.define('paramModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramType',
            type: 'string'
        },
        {
            name: 'paramDefaultValue',
            type: 'string'
        },
        {
            name: 'paramDesc',
            type: 'string'
        },
        {
            name: 'paramOrder',
            type: 'int'
        }]
    });

    paramStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'paramModel',
        proxy: {
            type: 'ajax',
            url: 'getAudiScriptParamsForReview.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    paramStore.on('beforeload', function(store, options) {
        var new_params = {
        		workItemId: workItemId_hspe
        };

        Ext.apply(paramStore.proxy.extraParams, new_params);
    });

    var paramColumns = [/*{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },*/
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '类型',
        dataIndex: 'paramType',
        width: 60,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '参数值',
        dataIndex: 'paramDefaultValue',
        width: 70,
        editor: {
            allowBlank: true
        },
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '描述',
        dataIndex: 'paramDesc',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '顺序',
        dataIndex: 'paramOrder',
        width: 50,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    }];
    
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    
    var paramGrid = Ext.create('Ext.grid.Panel', {
    	title: "参数",
        width: '40%',
        height: 190,
        cls:'window_border panel_space_top panel_space_left panel_space_right',
//        margin: 10,
//        collapsible : true,
        store: paramStore,
        plugins: [cellEditing],
        border: true,
        columnLines: true,
        columns: paramColumns
    });
    
    Ext.define('attachmentModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'attachmentName',
            type: 'string'
        },
        {
            name: 'attachmentSize',
            type: 'string'
        },
        {
            name: 'attachmentUploadTime',
            type: 'string'
        }]
    });
    
    var attachmentStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'attachmentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptAttachment.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    attachmentStore.on('beforeload', function(store, options) {
        var new_params = {
            scriptId: scriptuuid,
            ids: attachmentIds
        };

        Ext.apply(attachmentStore.proxy.extraParams, new_params);
    });
    attachmentStore.on('load', function(me, records, successful, eOpts) {
   	 attachmentIds = []
    	$.each(records, function(index, record){
   		attachmentIds.push(record.get('iid'));
   	  });
	});
     function removeByValue(arr, val) {
	  for(var i=0; i<arr.length; i++) {
	    if(arr[i] == val) {
	      arr.splice(i, 1);
	      break;
	    }
	  }
    }
    var attachmentColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    },
    {
        text: '主键',
        dataIndex: 'iid',
        width: 40,
        hidden: true
    },
    {
        text: '附件名称',
        dataIndex: 'attachmentName',
        flex: 1,
        renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    /*,
    {
        text: '附件大小',
        dataIndex: 'attachmentSize',
        width: 200
    },
    {
        text: '上传时间',
        dataIndex: 'attachmentUploadTime',
        flex: 1
    }*/
     {
        menuDisabled: true,
        sortable: false,
        xtype: 'actioncolumn',
        width: 50,
        items: [{
            iconCls: 'attachment_delete',
            tooltip: '删除',
            handler: function(grid, rowIndex, colIndex) {
                var rec = attachmentStore.getAt(rowIndex);
                 Ext.Msg.confirm("请确认", "是否真的要删除附件？", function(button, text) {
	                 	 if (button == "yes") { 
				                var a = [];
				                a.push(rec.get('iid'));
				                Ext.Ajax.request({
				                    url: 'deleteScriptAttachment.do',
				                    method: 'POST',
				                    sync: true,
				                    params: {
				                        iids: a
				                    },
				                    success: function(response, request) {
				                        var success = Ext.decode(response.responseText).success;
				                        if (success) {
				                            Ext.Msg.alert('提示', '删除成功！');
				                            removeByValue(attachmentIds, rec.get('iid'));
				                            attachmentStore.load();
				                        } else {
				                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
				                        }
				                    },
				                    failure: function(result, request) {
				                        secureFilterRs(result, "保存失败！");
				                    }
				                  });
	                 	 }
		             });
		}
        },
        {
            iconCls: 'script_download',
            tooltip: '下载',
            handler: function(grid, rowIndex, colIndex) {
                  var rec = attachmentStore.getAt(rowIndex);
                  //window.open('downloadScriptAttachment.do?iid='+rec.get('iid'));
            	  window.location.href = 'downloadScriptAttachment.do?iid='+rec.get('iid');
            }
        }
        ]
    }];
    var selectedAttachmentButton = Ext.create ("Ext.Button",
			{
				cls : 'Common_Btn',
				disabled : false,
				text : '添加附件',
				handler : selectAttachmentFun
			});
    var attachmentGrid = Ext.create('Ext.grid.Panel', {
    	title: "附件",
        width: '40%',
        height: 190,
        region: 'center',
//        margin: 10,
        store: attachmentStore,
       cls:'window_border panel_space_top panel_space_left panel_space_right attachments customize_panel_back',
        border: true,
        columnLines: true,
        emptyText: '没有附件',
        columns: attachmentColumns,
        dockedItems : [{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items:[
				'->',selectedAttachmentButton//添加附件按钮
			]
		}]
    });
    
    var attachmentUploadWin = null;
    function selectAttachmentFun(){
		var uploadForm;
		uploadForm = Ext.create('Ext.form.FormPanel',{
			border : false,
			items : [{
				xtype: 'filefield',
				name: 'files', // 设置该文件上传空间的name，也就是请求参数的名字
				id : 'attachment_idbasic',
				fieldLabel: '选择文件',
				labelWidth: 65,
				anchor: '90%',
				margin: '10 10 0 40',
				buttonText: '浏览',
				multipleFn: function($this){

			         var typeArray = ["application/x-shockwave-flash","audio/MP3","image/*","flv-application/octet-stream"];

			         var fileDom = $this.getEl().down('input[type=file]');

			         fileDom.dom.setAttribute("multiple","multiple");

			         fileDom.dom.setAttribute("accept",typeArray.join(","));

			},
			    listeners:{
			    	   afterrender: function(){
			    		this.multipleFn(this);
			    		},
			    		change: function(){
			    			var fileDom = this.getEl().down('input[type=file]'); 
			    			var files = fileDom.dom.files; 
			    			var str = ''; 
			    			for(var i = 0;  i < files.length;  i++){
			    			 str += files[i].name;
			    			 str += ' ';
			    			} 
			    			 Ext.getCmp('attachment_idbasic').setRawValue(str);    //files为组件的id
			    			 this.multipleFn(this);
			    			}
			    	}
			}],
			buttonAlign : 'center',
			buttons :[{
				text : '确定',
				handler :upExeclData
			},{
				text : '取消',
				handler : function(){
					this.up("window").close();
				}
			}]
		});

		attachmentUploadWin = Ext.create('Ext.window.Window', {
			title : '附件信息',
			modal : true,
			closeAction : 'destroy',
			constrain : true,
			autoScroll : true,
			width : 600,
			height: 160,
			items : [ uploadForm ],
			listeners : {
				close : function(g, opt) {
					uploadForm.destroy();
				}
			},
			/*
			 * draggable : false,// 禁止拖动 resizable : false,// 禁止缩放
			 */layout : 'fit'
		});

		function upExeclData(){
			var form = uploadForm.getForm();
			var hdupfile=form.findField("files").getValue();
			if(hdupfile==''){
				Ext.Msg.alert('提示',"请选择文件...");
				return ;
			}
			uploadTemplate(form);
		}

		/** 自定义遮罩效果* */
		var myUploadMask = new Ext.LoadMask (contentPanel,
		{
			msg : "附件上传中..."
		});
		function uploadTemplate(form) {
			if (form.isValid()) {
				form.submit({
					url: 'uploadScriptAttachmentFile.do',
					success: function(form, action) {
						var success=Ext.decode(action.response.responseText).success;
						var msg = Ext.decode(action.response.responseText).message;
                      if(success){
                    	  var ids = Ext.decode(action.response.responseText).ids;
                    	  attachmentIds.push.apply(attachmentIds,ids.split(","));
                      }else{
                    	  Ext.Msg.alert('提示',msg);
                      }
                      attachmentUploadWin.close();
				      myUploadMask.hide();
				      attachmentStore.load();
					},
					failure: function(form, action) {
						var msg = Ext.decode(action.response.responseText).message;
						Ext.Msg.alert('提示',msg);
						myUploadMask.hide();
					}
				});
			}
		}
		attachmentUploadWin.show();
	}		
    
    var execDesc = Ext.create('Ext.form.field.TextArea', {
    	name: 'funcdesc',
        displayField: 'funcdesc',
        emptyText: '',
        columnWidth: 1,
        maxLength: 255,
        margin:'0 10 0 10',
        height: 150,
        value: publishDesc_hspe,
        autoScroll: true
    });
    
    var execDescForm = Ext.create('Ext.form.Panel', {
        width: '20%',
        height: 180,
        border: false,
        layout: 'anchor',
        cls:'window_border panel_space_top',
        bodyCls : 'x-docked-noborder-top',
        collapsible : false,
        title: '执行描述',
        items: [{
            layout: 'column',
            border: false,
//            margin: '5',
            items: [execDesc]
        }]
    });
    var paramsAndAttachmentsPanel = Ext.create('Ext.panel.Panel', {
        width: '100%',
         collapsible : false,
        border: false,
//        title: "参数与附件",
        height: 200,
        layout: {
            type: 'hbox',
            align : 'stretch'
        },
        items: [paramGrid, attachmentGrid, execDescForm]
    });

    Ext.define('resourceGroupModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'id',
	      type : 'int',
	      useNull : true
	    }, {
	      name : 'name',
	      type : 'string'
	    }, {
	      name : 'description',
	      type : 'string'
	    }]
	  });
	
	var resourceGroupStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'resourceGroupModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getResGroupForScriptService.do',
	      reader: {
	        type: 'json',
	        root: 'dataList',
	        totalProperty: 'totalCount'
	      }
	    }
	  });
	resourceGroupStore.on('load', function() { 
		/*var ins_rec = Ext.create('resourceGroupModel',{
            id : '-1',
            name : '未分组',
            description : ''
        }); 
		resourceGroupStore.insert(0,ins_rec);*/
		/*var dataStrArr = resourceGroups_hspe.split(',');
		var dataIntArr=[];
	    dataStrArr.forEach(function(data,index,arr){  
	        dataIntArr.push(+data);  
	    });  
		resourceGroupObj.setValue(dataIntArr);
		console.log(dataIntArr);*/
	});  
	var resourceGroupObj=Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '分组',
			    labelAlign : 'right',
			    labelWidth : 65,
			    width : '23.6%',
	            columnWidth:1,
			    multiSelect: true,
			    hidden:removeAgentSwitch,
			    store : resourceGroupStore,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    mode : 'local',
		    	listeners: {
	    	      change: function( comb, newValue, oldValue, eOpts ) {
	    	    	  agent_store.load();
	    	      }
		    	}
	});
    
	var app_name = new Ext.form.TextField({
		name : 'appname',
		fieldLabel : '应用名称',
		displayField : 'appname',
		emptyText : '--请输入应用名称--',
		hidden: !CMDBflag,
		labelWidth : 65,
		hidden : true,
		labelAlign : 'right',
		width : '20%'
	});
	var agent_ip = new Ext.form.TextField({
		name : 'agentip',
		fieldLabel : 'AgentIp',
		displayField : 'agentip',
		emptyText : '--请输入agentip--',
		labelWidth : 65,
		hidden :true,
		labelAlign : 'right',
		width : '23%'
	});
	 var ipStart = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 65,
	     fieldLabel : '起始IP',
	    emptyText : '--请输入开始IP--',
	    //labelSeparator : '',
	    width : '23%',
	    labelAlign : 'right'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipStart.getValue().trim()) && !isYesIp (ipStart.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法开始IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	/** 结束ip* */
	var ipEnd = Ext.create ('Ext.form.TextField',
	{
	    labelWidth : 65,
	    fieldLabel : '终止IP',
	    emptyText : '--请输入截止IP--',
	    //labelSeparator : '',
	    labelAlign : 'right',
	    width : '23%'
//	    listeners:{
//	    	blur:function(t,e,o){
//	    		if (checkIsNotEmpty (ipEnd.getValue().trim()) && !isYesIp (ipEnd.getValue().trim()))
//				{
//					Ext.Msg.alert ('提示', '请输入合法结束IP!');
//					t.setValue('');
//					return;
//				}
//	    	}
//	    }
	// padding : '0 10 0 0'
	});
	var host_name = new Ext.form.TextField({
		name : 'hostname',
		fieldLabel : '计算机名',
		displayField : 'hostname',
		emptyText : '--请输入计算机名--',
		labelWidth : 65,
		labelAlign : 'right',
		width : '23%'
	});
	var sys_name = new Ext.form.TextField({
		name : 'sysname',
		fieldLabel : '系统名称',
		hidden: !CMDBflag,
		displayField : 'sysname',
		emptyText : '--请输入系统名称--',
		labelWidth : 65,
		labelAlign : 'right',
		width : '23%'
	});
	var os_type = new Ext.form.TextField({
		name : 'ostype',
		fieldLabel : '操作系统',
		displayField : 'ostype',
		emptyText : '--操作系统--',
		labelWidth : 93,
		labelAlign : 'right',
		width : CMDBflag?'25.5%':'22%'
	});
	var agentStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data : [
			{"id":"-10000", "name":"全部"},
			{"id":"0", "name":"正常"},
			{"id":"1", "name":"异常"},
			{"id":"2", "name":"升级中"}
		]
	});
	
	var agentStatusCb = Ext.create('Ext.form.field.ComboBox', {
		name : 'agentStatus',
		labelWidth : 93,
		queryMode : 'local',
		fieldLabel : 'Agent状态',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		emptyText : '--请选择Agent状态--',
		store : agentStatusStore,
		width : '22.9%',
		labelAlign : 'right'
	});
	var search_ip_form = Ext.create('Ext.form.Panel', {
		region : 'north',
		border : false,
		cls:'window_border panel_space_top panel_space_left panel_space_right',
		dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [ sys_name, app_name, host_name, os_type,ipStart,ipEnd
			]
		},
		{
			xtype : 'toolbar',
			dock : 'top',
			border: false,
			items : [/*agent_ip,*/ resourceGroupObj, agentStatusCb,
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '查询',
					handler : function(){
						pageBar.moveFirst();
					}
				},
				{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '清空',
					handler : function(){
						agent_ip.setValue('');
						ipEnd.setValue('');
						ipStart.setValue('');
				    	app_name.setValue('');
						sys_name.setValue('');
						host_name.setValue('');
						os_type.setValue('');
				    	resourceGroupObj.setValue('');
				    	agentStatusCb.setValue('');
					}
				},{
					xtype : 'button',
					cls : 'Common_Btn',
					text : '导入',
					handler : importExcel
				}
			]
		}]
	});
	
	 function checkFile(fileName){
		    var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;  
		    if(!file_reg.test(fileName)){  
		    	 Ext.Msg.alert('提示','文件类型错误,请选择Excel文件'); 
		    	//Ext.Msg.alert('提示','文件类型错误,请选择Excel文件或者Zip压缩文件(xls/xlsx/zip)'); 
		        return false;
		    }
		    return true;
		}
	function importExcel() {
		//销毁win窗口
		if(!(null==upldWin || undefined==upldWin || ''==upldWin)){
			upldWin.destroy();
			upldWin = null;
		}
		
		if(!(null==upLoadformPane || undefined==upLoadformPane || ''==upLoadformPane)){
			upLoadformPane.destroy();
			upLoadformPane = null;
		}
		//导入文件Panel
		upLoadformPane =Ext.create('Ext.form.Panel', {
	        width:370,
	        height:100,
		    frame: true,
			items: [
				{
					xtype: 'filefield',
					name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
					fieldLabel: '选择文件',
					labelWidth: 65,
					msgTarget: 'side',
					anchor: '100%',
					buttonText: '浏览...',
					width:370
				}
			],
			buttonAlign: 'left',
			buttons: [
					{
						id:'upldBtnIdAudi',
						text: '导入Agent文件',
						handler: function() {
							var form = this.up('form').getForm();
							var upfile=form.findField("file").getValue();
			    			if(upfile==''){
			    				Ext.Msg.alert('提示',"请选择文件...");
			    				return ;
			    			}
			    			
			    			var hdtmpFilNam=form.findField("file").getValue();
			    			if(!checkFile(hdtmpFilNam)){
				    			  form.findField("file").setRawValue('');
				    			  return;
				    		}

							if (form.isValid()) {
								 Ext.MessageBox.wait("数据处理中...", "进度条");
								form.submit({
									url: 'importAgentForStart.do',
									params:{
										envType:1
				                	},
								    success: function(form, action) {
								       var msg = Ext.decode(action.response.responseText).message;
								       
								    	   var status = Ext.decode(action.response.responseText).status;
								    	   var matchAgentIds = Ext.decode(action.response.responseText).matchAgentIds;
								    	   
								    	   if(status==1) {
								    		   if(matchAgentIds && matchAgentIds.length>0) {
								    			   Ext.MessageBox.buttonText.yes = "确定"; 
								    				Ext.MessageBox.buttonText.no = "取消"; 
								    			   Ext.Msg.confirm("请确认", msg, function(id){
										  				 if(id=='yes'){
									  						Ext.Msg.alert('提示', "导入成功！");
									  						agent_ip.setValue('');
													    	app_name.setValue('');
															sys_name.setValue('');
															host_name.setValue('');
															os_type.setValue('');
													    	resourceGroupObj.setValue('');
													    	agentStatusCb.setValue('');
													    	chosedAgentIds = matchAgentIds;
												    	   pageBar.moveFirst();
										  				 }
										    		   });
								    		   } else {
								    			   Ext.Msg.alert('提示-没有匹配项', msg);
								    		   }
								    		   
								    	   } else {
								    		   Ext.Msg.alert('提示', "导入成功！");
									    	   agent_ip.setValue('');
										    	app_name.setValue('');
												sys_name.setValue('');
												host_name.setValue('');
												os_type.setValue('');
										    	resourceGroupObj.setValue('');
										    	agentStatusCb.setValue('');
										    	chosedAgentIds =  matchAgentIds;
									    	   pageBar.moveFirst();
								    	   }
								    	   
								       upldWin.close();
								       return;
								    },
								    failure: function(form, action) {
								    	 secureFilterRsFrom(form, action);
								    }
								});
					         }
						}
					}, {
						text: '下载模板',
						handler: function() {
							window.location.href = 'downloadAgentTemplate.do?fileName=AgentStartImoprtMould.xls';
						}
					}
				]
		});
		//导入窗口
		upldWin = Ext.create('Ext.window.Window', {
		    title: '设备信息批量导入',
		    width: 400,
		    height: 140,
		    modal:true,
		    resizable: false,
		    closeAction: 'destroy',
		    items:  [upLoadformPane]
		}).show();
		upldWin.on("beforeshow",function(self, eOpts){
			var form = Ext.getCmp("upldBtnIdAudi").up('form').getForm();
			form.reset();
		});
		
		upldWin.on("destroy",function(self, eOpts){
			upLoadformPane.destroy();
		});
	}

	Ext.define('agentModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',     type: 'string'},
            {name: 'agentIp',     type: 'string'},
            {name: 'agentPort',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentDesc',     type: 'string'},
            {name: 'agentState',     type: 'int'}
        ]
    });
	var agent_store_chosed = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 30,
		model: 'agentModel',
		proxy: {
            type: 'ajax',
            url: 'getAgentChosedList.do',
            actionMethods: {  
                create : 'POST',  
                read   : 'POST', // by default GET  
                update : 'POST',  
                destroy: 'POST'  
            },
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
	});


	var agent_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'agentModel',
        proxy: {
            type: 'ajax',
            url: 'getAllAgentList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
	Ext.define('dbModel', {
        extend: 'Ext.data.Model',
        idProperty: 'iid',
        fields: [
            {name: 'iid',        type: 'string'},
            {name: 'driverClass',type: 'string'},
            {name: 'dbUrl',      type: 'string'},
            {name: 'dbUser',     type: 'string'},
            {name: 'dbType',     type: 'string'}
        ]
    });
    var dbinfo_store = Ext.create('Ext.data.Store', {
    	model:'dbModel',
    	autoLoad: false,
    	proxy: {
    		type: 'ajax',
    		url: 'getDbSqlDriverInfo.do',
    		reader: {
    			type: 'json',
    			root: 'dataList'
    		}
    	}
    });
    dbinfo_store.on('load', function() { 
//    	  var selDsid = cpdsMap[selCpId];
//		  var records=[];//存放选中记录
//		  for(var i=0;i<dbinfo_store.getCount();i++){
//		      var record = dbinfo_store.getAt(i);
//		      if(selDsid==record.data.iid)
//		      {
//		    	  records.push(record);
//		      }
//		  }
//		  dbsource_grid.getSelectionModel().select(records, false, false);//选中记录
    	var agentsId =chosedAgentIds[0];
		var	singleId = agentsId.split(",");
		for(var k=0;k<singleId.length;k++){
			var singleIid = singleId[k];
			if(finalChosedAgentsAndDbSources.hasOwnProperty(singleIid)) {
        		dbinfo_store.each(function(record) {   
 			       if(finalChosedAgentsAndDbSources[singleIid]==record.get('iid')) {
 			    	  selModel2.select(record,false, true);
 		    	   }
 			    });
        	
	        }else{
	        	dbsource_grid.getSelectionModel().select(0);  
			}
		}
	 });  
    
    var agent_columns = [{ text: '序号', xtype:'rownumberer', width: 40 },
                        { text: '主键',  dataIndex: 'iid',hidden:true},
                        { text: '名称',  dataIndex: 'sysName',flex:1},
            	        { text: '计算机名',  dataIndex: 'hostName',flex:1},
                        { text: 'IP',  dataIndex: 'agentIp',width:110},
                        { text: '端口号',  dataIndex: 'agentPort',width:60},
		                { text: '描述',  dataIndex: 'agentDesc',flex:1,
                        	renderer:function (value, metaData, record, rowIdx, colIdx, store){  
                                metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
                                return value;  
                            }
                        },
		                { text: '状态',  dataIndex: 'agentState',width:80,renderer:function(value,p,record){
		                	var backValue = "";
		                	if(value==0){
		                		backValue = "Agent正常";
		                	}else if(value==1){
		                		backValue = "Agent异常";
		                	}
		                	return backValue;
		                }}
		               ];
    
    var dbsource_columns = [/*{ text: '序号', xtype:'rownumberer', width: 40 },*/
        { text: '主键',  dataIndex: 'iid',hidden:true},
        { text: '驱动类',  dataIndex: 'driverClass',width:200,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }},
        { text: 'DBURL',  dataIndex: 'dbUrl',flex:1,width:80,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }},
        { text: 'DB用户',  dataIndex: 'dbUser',width:150,hidden:true},
        { text: 'DB类型',  dataIndex: 'dbType',width:110}
    ];

    agent_store.on('beforeload', function (store, options) {
    	var new_params = {  
			agentIp : agent_ip.getValue(),
	 		startIp :ipStart.getValue().trim(),
	    	endIp :ipEnd.getValue().trim(),
	    	appName : app_name.getValue(),
			sysName : sys_name.getValue(),
			hostName : host_name.getValue(),
			osType : os_type.getValue(),
	    	rgIds:resourceGroupObj.getValue(),
	    	flag: 1
	    };
	    
	    Ext.apply(agent_store.proxy.extraParams, new_params);
    });
    
    agent_store.on('load', function() { 
    	if(chosedAgentIds) {
			  var records=[];//存放选中记录
			  for(var i=0;i<agent_store.getCount();i++){
			      var record = agent_store.getAt(i);
			      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
		    	    {   
			    	  
			    	  if((+chosedAgentIds[ii])==record.data.iid)
			    		  {
			    		  records.push(record);
			    		  }
		    	    }   
			  }
			  agent_grid.getSelectionModel().select(records, false, false);//选中记录
		  }
		
	}); 
    agent_store_chosed.on('beforeload', function (store, options) {
    	var new_params = {  
    			agentIds : JSON.stringify(chosedAgentIds)
    	};
    	
    	Ext.apply(agent_store_chosed.proxy.extraParams, new_params);
    });
    
    agent_store_chosed.on('load', function (store, options) {
    	var records=[];//存放选中记录
	  for(var i=0;i<agent_store_chosed.getCount();i++){
	      var record = agent_store_chosed.getAt(i);
	      for (var ii=0;ii<chosedAgentIds.length;ii++ )   
    	    {   
	    	  
	    	  if((+chosedAgentIds[ii])==record.data.iid)
	    		  {
	    		  records.push(record);
	    		  }
    	    }   
	  }
	  agent_grid_chosed.getSelectionModel().select(records, false, false);//选中记录
    });
    var pageBar = Ext.create('Ext.PagingToolbar', {
		store : agent_store,
		baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
		dock : 'bottom',
		displayInfo : true
	});

    // 定义复选框
	var selModel = Ext.create('Ext.selection.CheckboxModel', {
//		checkOnly : true,
		listeners : {
//			selectionchange : function(selModel, selections) {
//			},
			select:function(selModel, record, index, eOpts) {
				 dbinfo_store.load({
			            params: {
			            	agentId: record.get("iid"),
			                agentIp: record.get("agentIp"),
			                agentPort: record.get("agentPort")
			            }
		         });
				 //当前选中cpid
				 selCpId = record.get("iid");
			 },
			 deselect:function(selModel, record, index, eOpts) {
				 dbinfo_store.removeAll();
				 var cpid = record.get("iid");
				 cpdsMap[cpid] = -1;//清空
//				 delete cpdsMap[cpid];
				 selCpId = -1;
			 }
		}
	});
    var agent_grid = Ext.create('Ext.grid.Panel', {
    	region: 'center',
    	width: '50%',
	    store:agent_store,
	    border:true,
	    columnLines : true,
	    bbar : pageBar,
	    cls:'window_border panel_space_top panel_space_left panel_space_right',
	    columns:agent_columns,
//	    selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})
	    selModel:selModel,
	    listeners: {
	        select: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.push(record.get('iid'));
            	}
            },
	        deselect: function( e, record, index, eOpts ){ 
            	if(chosedAgentIds.indexOf(record.get('iid'))>-1) {
            		chosedAgentIds.remove(record.get('iid'));
            	}
            }
	    }
	});
    var pageBarForAgentChosedGrid = Ext.create('Ext.PagingToolbar', {
    	store : agent_store_chosed,
    	baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
    	dock : 'bottom',
    	displayInfo : true
    });
    
    var selModelForagent_grid_chosed = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			select:function(selModel, record, index, eOpts) {
				 dbinfo_store.load({
			            params: {
			            	agentId: record.get("iid"),
			                agentIp: record.get("agentIp"),
			                agentPort: record.get("agentPort")
			            }
			        });
				 //当前选中cpid
				 selCpId = record.get("iid");
				 if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.push(record.get('iid'));
            	}
            	if(chosedAgentObjects.indexOf(record)==-1) {
            		chosedAgentObjects.push(record);
            	}
			 },
			 deselect:function(selModel, record, index, eOpts) {
				 dbinfo_store.removeAll();
				 var cpid = record.get("iid");
				 cpdsMap[cpid] = -1;//清空
				 selCpId = -1;
				 if(chosedAgentIds.indexOf(record.get('iid'))==-1) {
            		chosedAgentIds.remove(record.get('iid'));
            	}
            	if(chosedAgentObjects.indexOf(record)>-1) {
            		chosedAgentObjects.remove(record);
            	}
			 }
		}
	});
    var agent_grid_chosed = Ext.create('Ext.grid.Panel', {
    	title: '已选服务器',
    	region : 'west',
    	store:agent_store_chosed,
    	border:true,
    	width:'60%',
    	columnLines : true,
    	cls:'window_border panel_space_top panel_space_left panel_space_right',
    	height: 300,
    	emptyText: '没有选择服务器',
    	columns:agent_columns,
    	selModel:selModelForagent_grid_chosed,
    	bbar : pageBarForAgentChosedGrid,
    	dockedItems : [ {
			xtype : 'toolbar',
			dock : 'top',
			items : [{
				xtype : 'button',
				cls :'Common_Btn',
				text : '删除',
				handler : function() {
					var records = agent_grid_chosed.getSelectionModel().getSelection();
					if(records.length>0) {
	  					for(var i = 0, len = records.length; i < len; i++){
	  						chosedAgentIds.remove(records[i].get('iid'));
	  					}
	  					pageBarForAgentChosedGrid.moveFirst();
	  					pageBar.moveFirst();
	  				} else {
	  					Ext.Msg.alert('提示', "请选择服务器！");
                        return;
	  				}
				}
			},
			{
				xtype : 'button',
				cls : 'Common_Btn',
				text : '增加服务器',
				handler : function(){
					if(!chosedAgentWin) {
						chosedAgentWin = Ext.create('Ext.window.Window', {
					  		title : '增加服务器',
					  		autoScroll : true,
					  		modal : true,
					  		resizable : false,
					  		closeAction : 'hide',
					  		layout: 'border',
					  		width : contentPanel.getWidth()-190,
					  		height : contentPanel.getHeight(),
					  		items:[search_ip_form, agent_grid],
					  		dockedItems: [{
					            xtype: 'toolbar',
					            dock:'bottom',
					            layout: {pack: 'center'},
						        items: [{ 
						  			xtype: "button",
						  			text: "确定", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				agent_store_chosed.load();
						  				this.up("window").close();
						  			}
						  		 },{ 
						  			xtype: "button",
						  			text: "关闭", 
						  			cls:'Common_Btn',
						  			margin:'6',
						  			handler: function () {
						  				this.up("window").close();
						  			}
						  		 }]
					  		}]
					  	});
					}
					chosedAgentWin.show();
					agent_store.load();
				}
			} ]
		}]
    });

    //////////////////////////////////////////
    // 定义复选框
	var selModel2 = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		mode : "SINGLE",
		listeners : {
			select:function(selModel2, record, index, eOpts) {
				var dsid = record.get("iid");
				if(selCpId != -1)
				{
					cpdsMap[selCpId] = dsid;//绑定
				}
				finalChosedAgentsAndDbSources[selCpId] = dsid;//绑定
			},
			deselect:function(selModel2, record, index, eOpts) {
			    cpdsMap[selCpId] = -1;//清空
				delete finalChosedAgentsAndDbSources[selCpId]; 
			}
		}
	});
    var dbsource_grid = Ext.create('Ext.grid.Panel', {
    	store:dbinfo_store,
//    	 region: 'center',
    	height:300,
    	 width: '40%',
    	border:true,
    	columnLines : true,
    	cls:'window_border panel_space_top panel_space_right',
    	columns:dbsource_columns,
//    	selModel:Ext.create('Ext.selection.CheckboxModel', {checkOnly : true})
    	selModel:selModel2
    });
    
    var agentAndSourcePanel = Ext.create('Ext.panel.Panel', {
        collapsible : false,
        region: 'center',
       border: false,
       height: 300,
       layout: {
           type: 'hbox',
           align:'stretch'
       },
       items: [agent_grid_chosed, dbsource_grid]
    });
    
//    var chooseAgentPanel = Ext.create('Ext.panel.Panel', {
//        border: false,
//        layout:'border',
//        title: "选择服务器",
//        collapsible : false,
//        height: 350,
//        items: [search_ip_form, agentAndSourcePanel/*,execDescForm*/]
//    });
    
    Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	
	var auditorStore_tap = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecAuditorList.do?scriptLevel='+scriptLevel_hspe,
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	var auditorComBox_tap = Ext.create('Ext.form.ComboBox', {
	    editable: true,
	    fieldLabel: "审核人",
	    labelWidth: 65,
	    store: auditorStore_tap,
	    queryMode: 'local',
	    width: '17%',
	    displayField: 'fullName',
	    valueField: 'loginName',
	    listeners:{
	    	select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
//			blur:function(combo, records, eOpts){
//				var displayField =auditorComBox_tap.getRawValue();
//				if(!Ext.isEmpty(displayField)){
//					//判断输入是否合法标志，默认false，代表不合法
//					var flag = false;
//					//遍历下拉框绑定的store，获取displayField
//					auditorStore_tap.each(function (record) {
//						//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
//					    var data_fullName = record.get('fullName');
//					    if(data_fullName == displayField){
//					    	flag =true;
//					    	combo.setValue(record.get('loginName'));
//					    }
//					});
//					if(!flag){
//					 	Ext.Msg.alert('提示', "输入的审核人非法");
//					 	auditorComBox_tap.setValue("");
//					 	return;
//					} 
//				}
//				
//			},
			beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = Ext.util.Format.trim(e.query);
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                    });
                    combo.expand();
                    return false;
                }
            }
	    }
    });
	
	var execStore = Ext.create('Ext.data.Store', {
	    autoLoad: false,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecUserList.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	var execComBox_tap = Ext.create('Ext.form.ComboBox', {
	    fieldLabel: "执行人",
	    store: execStore,
	    queryMode: 'local',
	    width: '17%',
	    displayField: 'fullName',
	    valueField: 'loginName',
	    labelWidth : 65,
	    editable : true,
	    hidden:!execUserSwitch||scriptLevel_hspe==0?true:false,
		labelAlign : 'right'
	  });
	execStore.load({
	    callback : function (records, operation, success)
	    {
	    	execComBox_tap.setValue (loginUser_hspe);
	    }
    });
    
    var isTimerTask = Ext.create('Ext.form.field.Checkbox', {
    	    checked : isTimetask,
        	boxLabel: '定时任务',
    		//margin:'3 5 0 5',
    		name      : 'isDelay',
	        id : 'isDelay',
	        labelAlign : 'right',
    		listeners: { //监听 
				       change:function(el,checked){
				             if(checked){
				             	cycleExecCronText.show(); 
				             	selectCronButton.show();
				             }else{
				             	cycleExecCronText.hide();
				             	selectCronButton.hide();
				             }
				        }
		    }
        });
    
    
     /** 选择生成周期表达式按钮 **/
		var selectCronButton = Ext.create ("Ext.Button",
		{
			id : 'selectCronButton_id',
		    cls : 'Common_Btn',
		    text : "选择",
		    hidden: !(isTimerTask.getValue()),
		    handler : selectExecCron
		});
		
		function selectExecCron()
		{
			var creatCronWin;
			if (creatCronWin == undefined || !creatCronWin.isVisible()) {
				creatCronWin = Ext.create('Ext.window.Window', {
					title : '定时任务参数设置',
					modal : true,
					id : 'creatCronWin',
					closeAction : 'destroy',
					constrain : true,
					autoScroll : true,
					upperWin : creatCronWin,
					width : contentPanel.getWidth() - 350,
					height : contentPanel.getHeight() - 30,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					loader : {
						url : 'cronMainForSpdb.do',
	//					params : {
	//						sysType : sysType,
	//						state : state,
	//						errorTaskId : errorTaskId,
	//						pageType : pageType
	//					},
						autoLoad : true,
						autoDestroy : true,
						scripts : true
					}
				});
			}
			creatCronWin.show();
		}    
    
	var cycleExecCronText = new Ext.form.TextField (
			{
			    fieldLabel : '执行时间',
			    labelWidth : 65,
			    labelAlign : 'right',
			    id : 'cycleExecCronText',
				name: 'cycleExecCronText',
			    width : '17%',
			    readOnly : true,
			    value:taskTime,
			    hidden:   !(isTimerTask.getValue())
	});     
        
    
	var butterflyVerison = new Ext.form.TextField({
		name: 'butterflyversion',
		fieldLabel: '单号',
		hidden:!scriptOddNumberSwitch,
		emptyText: '',
		labelWidth : 65,
		labelAlign : 'right',
		value:butterflyV,
		editable : false,
		width: '17%'
	});
	var checkVersion=new  Ext.form.Checkbox({   
        id:"checkVersion",               
	    name:"checkVersion",
	    boxLabel:"单号补添:" ,
	    boxLabelAlign:"before",
	    hidden:!scriptOddNumberSwitch,
	    labelWidth:65,
	    checked: butterflyV==''?true:false,
	    padding: '0 2 0 10'
	});
//	var execTime_sm = Ext.create('Go.form.field.DateTime',{
//	    fieldLabel:'执行时间',
//	    format:'Y-m-d H:i:s',
//	    labelWidth : 58,
//	    width:'23%',
//	    value: (taskTime==null||taskTime=="")?null:new Date(taskTime),
////	    margin : '10 0 0 0',
//	    labelAlign : 'right'
//    });
//	var execTime_sm = new Ext.form.TextField (
//	{
//	    fieldLabel : '执行时间',
//	    labelWidth : 58,
//	    labelAlign : 'right',
//	    value:taskTime,
//	    width : '23%'
//	});
	
	var execUser = new Ext.form.TextField({
        name: 'execUser',
        fieldLabel: '用户',
        emptyText: '',
        value: execUser_hspe,
        labelWidth: 65,
        width: '17%'
    });
	var taskName = new Ext.form.TextField({
		name: 'taskName',
		fieldLabel: '任务名称',
		emptyText: '',
		value: taskName_hspe,
		labelWidth: 65,
		width: '17%'
	});
	var eachNum = new Ext.form.NumberField({
		name: 'eachNum',
		fieldLabel: '并发数量',
		emptyText: '',
		value: eachNum_hspe,
		labelWidth: 65,
		width: '10%'
	});
	
	function setMessage(msg) {
        Ext.Msg.alert('提示', msg);
    }
	 function updateStatus(timeTaskid,status){
		  Ext.Ajax.request({
				url : 'updateTimeTaskStatus.do',
				async: false,
				method : 'POST',
				params : {
					timeTaskid:timeTaskid,
					status :status
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					if (!success) {
						Ext.MessageBox.alert("提示", "执行更新定时任务状态失败!id："+timeTaskid);
					}
				},
				failure : function(result, request) {
					Ext.Msg.alert('提示', '执行更新定时任务状态失败！');
				}
			});
	  }
	function reExamination() {
		var isFromCustom = 0;
    	if(showConfigSwitch  || scriptConvertFlowSwitch){
    		isFromCustom=1;
    	}
    	paramStore.sort('paramOrder', 'ASC');
    	var m = paramStore.getRange(0, paramStore.getCount()-1);
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            //var n = 0;
            var paramType = m[i].get("paramType") ? m[i].get("paramType").trim() : '';
            var paramDefaultValue = m[i].get("paramDefaultValue") ? m[i].get("paramDefaultValue").trim() : '';
            var paramDesc = m[i].get("paramDesc") ? m[i].get("paramDesc").trim() : '';
            if ("" == paramType) {
                setMessage('参数类型不能为空！');
                return;
            }
            if (fucCheckLength(paramDesc) > 250) {
                setMessage('参数描述不能超过250字符！');
                return;
            }

            if (paramType == 'int') {
                if (!checkIsInteger(paramDefaultValue)) {
                    setMessage('参数类型为int，但参数默认值不是int类型！');
                    return;
                }
            }
            if (paramType == 'float') {
                if (!checkIsDouble(paramDefaultValue)) {
                    setMessage('参数类型为float，但参数默认值不是float类型！');
                    return;
                }
            }
            if (!Ext.isEmpty(Ext.util.Format.trim(paramDefaultValue))) {
                globalParams[m[i].get('iid')] = paramDefaultValue;
            } else {
            	delete globalParams[m[i].get('iid')];
            }
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0) jsonData = jsonData + ss;
            else jsonData = jsonData + "," + ss;
        }

        jsonData = jsonData + "]";
        
//        var agents = [];
//        var records = agent_grid.getSelectionModel().getSelection();
//			if(records.length>0) {
//				for(var i = 0, len = records.length; i < len; i++){
//					agents.push(records[i].get('iid'));
//				}
//			} else {
//				Ext.Msg.alert('提示', "请选择服务器！");
//            return;
//			}
	     	if(chosedAgentIds.length<=0) {
				Ext.Msg.alert('提示', "请选择服务器！");
                return;
			} 
			 var agents = [];
	       if( chosedAgentIds.length >0) {
	  				for(var j = 0;  j < chosedAgentIds.length; j++){
	  					var cpidTmp = chosedAgentIds[j];
	  					var dsidTmp = finalChosedAgentsAndDbSources[cpidTmp];
	  					if(null==dsidTmp||undefined==dsidTmp||""==dsidTmp||-1==dsidTmp)
	  					{
	  						var agentIp = record.get('agentIp');
	  			            var agentPort = record.get('agentPort');
	  						var dsErrMsg = "服务器【"+agentIp+":"+agentPort+"】没有选择数据源！";
	  						Ext.Msg.alert('提示', dsErrMsg);
	                        return;
	  					}
	  					var tmpRec = {};
	  					tmpRec.cpid=cpidTmp;
	  					tmpRec.dsid=dsidTmp;
	  					agents.push(tmpRec);
	  				}
	  			} else {
	  				Ext.Msg.alert('提示', "提交失败，请选择设备！！");
	              return;
	  			}
	  			if(agents.length<=0)
	  			{
	  				Ext.Msg.alert('提示', "请选择服务器和数据源！");
	              return;
	  			}
			
			var auditor = auditorComBox_tap.getValue();
			if(!auditor) {
				Ext.Msg.alert('提示', "没有选择审核人！");
				return;
			}
        	//判断输入的审核人是否合法 start
        	var displayField =auditorComBox_tap.getRawValue();
			if(!Ext.isEmpty(displayField)){
				//判断输入是否合法标志，默认false，代表不合法
				var flag = false;
				//遍历下拉框绑定的store，获取displayField
				auditorStore_tap.each(function (record) {
					//获取数据集里的 fullName 进行比较 如果有匹配的，那么标志为true，代表合法
				    var data_fullName = record.get('fullName');
				    if(data_fullName == displayField){
				    	flag =true;
				    	//combo.setValue(record.get('loginName'));
				    }
				});
				if(!flag){
				 	Ext.Msg.alert('提示', "输入的审核人非法");
				 	auditorComBox_tap.setValue("");
				 	return;
				} 
			}
			//判断输入的审核人是否合法  end
			if(ssTimerTaskSwitch) {
				//执行时间校验,勾选了就必须填
				var isdelay = Ext.getCmp("isDelay").getValue();
				var execT = cycleExecCronText.getRawValue();
				if(isdelay && ""==execT)
				{
					Ext.Msg.alert('提示', "没有填写执行时间！");
					return;
				}
			} else {
				var isdelay = false;
				var execT = "";
			}
			
			var execDescForExec = Ext.util.Format.trim(execDesc.getValue());
			if(Ext.isEmpty(execDescForExec)) {
				Ext.Msg.alert('提示', "没有填写执行描述！");
				return;
			}
			
			var taskN = Ext.util.Format.trim(taskName.getValue());
			if(Ext.isEmpty(taskN)) {
				Ext.Msg.alert('提示', "任务命名不能为空！");
				return;
			}
			
			if (fucCheckLength(taskN) > 255) {
            setMessage('任务命名不能超过255字符！');
            return;
        }
			
			var en = eachNum.getValue();
			if(!Ext.isEmpty(en) && checkIsInteger(en) && isNotNegativeInteger(en)) {
				if(en>eachNumForH) {
					setMessage('并发数量不能超过'+eachNumForH);
                return;
				}
			} else {
				setMessage('并发数量不合法！');
            return;
			}
			
			if(Ext.isEmpty(en)) {
				en = eachNumForH;
			}
			var performUser = execComBox_tap.getValue();
			var butterflyV = "";
			var check =Ext.getCmp("checkVersion").getValue();
			if( scriptOddNumberSwitch &&!check){
				butterflyV=Ext.util.Format.trim(butterflyVerison.getValue());
				if(butterflyV==null||butterflyV==""){
					Ext.Msg.alert('提示', "单号不能为空！");
  					return;
				}
				if (fucCheckLength(butterflyV) > 255) {
                    Ext.Msg.alert('提示', "单号不能超过255字符！");
                    return;
                }
			}
			var rgIds = resourceGroupObj.getValue();
			var mxIid = iid_hspe+":"+4;
				startData[mxIid] = {
					'actNo': 4,
					'actType': 0,
					'actName': '基础脚本1',
					'isShutdown':false
			};
			startData[mxIid]['chosedResGroups'] = rgIds;
		    startData[mxIid]['chosedAgentIds'] = chosedAgentIds;
		    startData[mxIid]['globalParams'] = globalParams;
		    startData[mxIid]['globalConfigParams'] = {};
		    startData[mxIid]['globalStartUser'] = Ext.util.Format.trim(execUser.getValue());
		    startData[mxIid]['globalConfigStartUser'] = {};
		    startData[mxIid]['finalChosedAgentsAndDbSources'] = finalChosedAgentsAndDbSources;
		    startData[mxIid]['eachNum'] = en;
			Ext.Ajax.request({
			    url : 'scriptExecAuditingDelete.do',
			    method : 'POST',
			    params : {
			    	workItemId: workItemId_hspe
			    },
			    success: function(response, opts) {
			        var success = Ext.decode(response.responseText).success;
			        if(success) {
			        	Ext.Ajax.request({
//		  				    url : 'scriptExecAuditing.do',
			        		url : 'scriptExecAuditingForSQL.do',
		  				    method : 'POST',
		  				    params : {
		  				    	serviceId: iid_hspe,
		  				    	execUser: execUser.getValue(),
//		  				    	agents: agents,
		  				    	agentsJson: Ext.encode(agents),
		  				    	params: jsonData,
		  				    	rgIds: rgIds,
		  				    	execDesc:execDescForExec,
		  				    	auditor: auditor,
			  				    isDelay:isdelay,
		  				    	execTime:execT,
		  				    	execStrategy:'0',
			  				    taskName: taskN,
			  				    performUser:performUser,
		  				    	butterflyversion:butterflyV,
		  				    	data: JSON.stringify(startData),
		  				    	eachNum: en,
		  				    	scriptLevel: scriptLevel_hspe,
		  				    	isFromCustom:isFromCustom,
		  				    	timeTaskid:timeTaskid
		  				    },
		  				    success: function(response, opts) {
		  				        var success = Ext.decode (response.responseText).success;
		  				        if(success) {
		  				        	if(timeTaskid>0){
			  				        	updateStatus(timeTaskid,1);
			  				        	}
		  				        	Ext.Msg.alert ('提示', '请求已经发送给审核人！', function ()
		          					    {
		          					    	if(from_hspe==1) {
		          					    		messageWindow2.close();
		          					    		destroyRubbish(); //销毁本页垃圾
		          					    		contentPanel.getLoader().load({
		          					    			url: 'forwardScriptLibrary.do',
		          					    			scripts: true});
		          					    	}  else if(from_hspe==66 ){ // 代表从菜单模块  脚本服务化双人复核查询  功能发出的请求 
		        					    		messageWindow_ssq.close();
		        					    		//forword('initScriptDoublePersonQueryMenu.do',titleFordoublePersonSSQuery);
		        					    	}else {
		          					    		messageWindow.setWidth(contentPanel.getWidth ());
		          					    		messageWindow.setHeight(contentPanel.getHeight ());
		          					    		messageWindow.center();
		          					    		messageWindow.getLoader ().load (
		          									{
		          									    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
		          									    autoLoad : true,
		          									    scripts : true
		          									});
		          									messageWindow.setTitle ('待办事项');
		          					    	}
		          					    });
		  				        } else {
		  				        	Ext.MessageBox.alert("提示", Ext.decode (response.responseText).message);
		  				        
		  				        }
      					    
		  				      
		  				    },
		  				    failure: function(result, request) {
		  				    	secureFilterRs(result,"操作失败！");
		  				    	auditingWinTi.close();
		  				    }
		  			    });
			        } else {
			        	Ext.MessageBox.alert("提示", "重新审核操作失败!");
			        }
			      
			    },
			    failure: function(result, request) {
			    	secureFilterRs(result,"操作失败！");
			    	auditingWinTi.close();
			    }
		    });
	}
	
	function teminate() {
    	Ext.Ajax.request (
		{
		    url : 'operWorkitemByiidForSsPublish.do',
		    method : 'POST',
		    params :
		    {
		        istateForQuery : 6,
		        scriptAudiFrom:2, // 执行审核
		        iidForQuery : workItemId_hspe
		    },
		    success : function (response, opts)
		    {
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
			    {
			    	if(from_hspe==1) {
			    		messageWindow2.close();
			    		destroyRubbish(); //销毁本页垃圾
			    		contentPanel.getLoader().load({
			    			url: 'forwardScriptLibrary.do',
			    			scripts: true});
			    	} else {
			    		messageWindow.setWidth(contentPanel.getWidth ());
			    		messageWindow.setHeight(contentPanel.getHeight ());
			    		messageWindow.center();
			    		messageWindow.getLoader ().load (
							{
							    url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
							    autoLoad : true,
							    scripts : true
							});
							messageWindow.setTitle ('待办事项');
			    	}
			    });
			    if(timeTaskid>0){
			        	updateStatus(timeTaskid,4);
			   }
		    }
		});
	}
	
	function showBackReason() {
    	Ext.MessageBox.show({
    	    title : '打回原因',
    	    msg : '',
    	    width : 500,
    	    buttons : Ext.MessageBox.OK,
    	    multiline : true,
    	    scope : this,
    	    fn : function(btn, reason){},
    		value: backInfo_hspe
    	});
        Ext.Msg.textArea.setReadOnly(true); // make textfield readonly 
        Ext.Msg.textArea.selectText();//call this if you want to pre-selected text in the box
	}
	
	function backTo() {
    	messageWindow.setWidth(contentPanel.getWidth ());
		messageWindow.setHeight(contentPanel.getHeight ());
		messageWindow.center();
		messageWindow.getLoader ().load (
				{
					url : 'initGetWorkitemRecordsList.do?activeTabNum=6',
					autoLoad : true,
					scripts : true
				});
		messageWindow.setTitle ('待办事项');
	}
	var topBar;
	if(ssTimerTaskSwitch) {
		topBar = Ext.create('Ext.form.Panel', {
	        region: 'west',
	        layout: 'anchor',
	        width: '50%',
	        buttonAlign: 'center',
	        cls:'window_border panel_space_left panel_space_right',
	        border: false,
	        dockedItems: [{
	        xtype: 'toolbar',
	        dock: 'top',
	        items: [taskName, eachNum, execUser,
	        	{
	        	xtype : 'button',
	            text: '重新审核',
	            cls: 'Common_Btn',
	            handler: reExamination
	        },
	        {

	            text: '终止',
	            cls: 'Common_Btn',
	            handler: teminate
	        },{
	            text: '查看脚本详情',
	            cls: 'Common_Btn',
	            handler: viewScriptDetail
	        }
	        ]
	    },{
	        xtype: 'toolbar',
	        dock: 'top',
	        border:false,
//	        padding:'5 5 5 0',
	        items: [
	        	auditorComBox_tap,butterflyVerison,checkVersion,execComBox_tap,isTimerTask,cycleExecCronText,selectCronButton,//执行时间
	        	{
	                text: '打回原因',
	                cls: 'Common_Btn',
	                handler: showBackReason
	            },
	            {
	                text: '返回',
	                cls: 'Common_Btn',
	                hidden:  from_hspe ==66?true:false,
	                handler: backTo
	            }
	    	]
	    }]
		});
	} else  {
		topBar = Ext.create('Ext.form.Panel', {
	        region: 'west',
	        layout: 'anchor',
	        width: '50%',
	        buttonAlign: 'center',
	        cls:'window_border panel_space_left panel_space_right',
	        border: false,
	        dockedItems: [{
	        xtype: 'toolbar',
	        dock: 'top',
	        padding:'5',
	        items: [taskName, eachNum, execUser,auditorComBox_tap,butterflyVerison,execComBox_tap]
	    }, {
	        xtype: 'toolbar',
	        dock: 'bottom',
	        border:false,
	        items: [{
	        	xtype : 'button',
	            text: '重新审核',
	            cls: 'Common_Btn',
	            handler: reExamination
	        },
	        {
	            text: '终止',
	            cls: 'Common_Btn',
	            handler: teminate
	        },{
	            text: '查看脚本详情',
	            cls: 'Common_Btn',
	            handler: viewScriptDetail
	        },{
                text: '打回原因',
                cls: 'Common_Btn',
                handler: showBackReason
            },
            {
                text: '返回',
                cls: 'Common_Btn',
                handler: backTo
            
            }]
	    
	    }]
		});
	}
	
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "hspe_area",
        layout: {
            type: 'vbox',
            align : 'stretch'
        },
        border: false,
        autoScroll: true,
        height: contentPanel.getHeight()-38,
        items: [topBar,agentAndSourcePanel, paramsAndAttachmentsPanel],
    });
    contentPanel.on('resize', function() {
        mainPanel.setHeight(contentPanel.getHeight() - 40);
        mainPanel.setWidth(contentPanel.getWidth());
    });
     function viewScriptDetail () {
    	scriptViewWin2 = Ext.create('widget.window', {
			title : '脚本详情',
			closable : true,
			closeAction : 'destroy',
			width : contentPanel.getWidth(),
			minWidth : 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			modal : true,
			loader : {
				url: 'queryOneServiceForView.do',
				params: {
					iid:iid_hspe,
					flag: 0,
					hideReturnBtn: true
				},
				autoLoad : true,
				scripts : true
			},
			buttonAlign: 'center',
			buttons: [{ 
	  			xtype: "button", 
	  			text: "关闭", 
	  			handler: function () {
	  				this.up("window").close();
	  			}
	  		}]
		});
		scriptViewWin2.show();
    }
});