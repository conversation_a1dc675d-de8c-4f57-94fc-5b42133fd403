package com.ideal.service.ic.hcstrategy.agencyTransit;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.ic.hcstrategy.agencyTransit.*;
import org.apache.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 中转机管理
 *
 * <AUTHOR>
 */
public class AgencyTransitService {
    public static final Logger log = Logger.getLogger(AgencyTransitService.class);

    private AgencyTransitService() {
    }

    private static AgencyTransitService instance = null;

    public static AgencyTransitService getInstance() {
        if (null == instance) {
            synchronized (AgencyTransitService.class) {
                if (null == instance) {
                    instance = new AgencyTransitService();
                }
            }
        }
        return instance;
    }

    /**
     * 中转机列表的查询
     *
     * @param centerId
     * @param start
     * @param limit
     * @return Map
     * @throws RepositoryException
     */
    public Map query(String centerId, Integer start, Integer limit, String userId) throws RepositoryException {
        return AgencyTransitManager.getInstance().query(centerId, start, limit, userId);
    }

    /**
     * 查询中心名称的返回数据
     *
     * @return List<IealDcModel>
     * @throws RepositoryException
     */
    public List<IealDcModel> queryIealDC(String userId) throws RepositoryException {
        return AgencyTransitManager.getInstance().queryIealDC(userId);
    }

    /**
     * 通过中心ID 查询中转机信息
     *
     * @param iCenterId 中心ID
     * @return List<IealTransferMachineModel>
     * @throws RepositoryException e
     */
    public List<IealTransferMachineModel> queryIealTransferMachineByICenterId(Integer iCenterId) throws RepositoryException {
        return AgencyTransitManager.getInstance().queryIealTransferMachineByICenterId(iCenterId);
    }

    /**
     * 批量添加Agent信息的数据
     *
     * @param jsonData
     * @param iEquipmentNum
     * @throws RepositoryException
     */
    public void insetAgentList(String jsonData, Long iEquipmentNum, Long iCenterId) throws RepositoryException {
        Gson gson = new Gson();
        List<IealAgentInfoModel> iealAgentInfoModels = gson.fromJson(jsonData, new TypeToken<List<IealAgentInfoModel>>() {
        }.getType());
        if (!CollectionUtils.isEmpty(iealAgentInfoModels)) {
            for (IealAgentInfoModel iealAgentInfoModel : iealAgentInfoModels) {
                iealAgentInfoModel.setiEquipmentNumMax(iEquipmentNum);
                iealAgentInfoModel.setiCenterId(iCenterId);
                AgencyTransitManager.getInstance().insetAgentList(iealAgentInfoModel);
            }
        }
    }

    /**
     * 查询中转机下所有的设备信息
     *
     * @param iAgentInfoId
     * @return Map
     * @throws RepositoryException
     */
    public Map queryIealEquipmentModelList(Long iAgentInfoId, String iName, String systemInfo, Long start, Long limit) throws RepositoryException {
        return AgencyTransitManager.getInstance().queryIealEquipmentModelList(iAgentInfoId, iName, systemInfo, start, limit);
    }

    /**
     * 删除 代理中转机
     *
     * @param iAgentInfoId
     * @throws RepositoryException
     */
    public Map delete(Long iAgentInfoId, Long iCenterId, String userName) throws RepositoryException {
        return AgencyTransitManager.getInstance().delete(iAgentInfoId, iCenterId, userName);
    }

    /**
     * 修改设备数量阈值
     *
     * @param iAgentInfoId     中转机ID
     * @param iCenterId        中心ID
     * @param iEquipmentNumMax 设备数量阈值
     * @param iEquipmentNum    当前设备数
     * @throws RepositoryException
     */
    public void update(Long iAgentInfoId, Long iCenterId, Long iEquipmentNumMax, Long iEquipmentNum) throws RepositoryException {
        if (iEquipmentNumMax >= iEquipmentNum) {
            AgencyTransitManager.getInstance().update(iAgentInfoId, iCenterId, iEquipmentNumMax);
        }
    }

    /**
     * 添加中转机之前的查询
     *
     * @param startIP
     * @param endIP
     * @param iAgentName
     * @param centerLocation
     * @param start
     * @param limit
     * @return Map
     * @throws RepositoryException
     */
    public Map queryByIPAndSome(String startIP, String endIP, String iAgentName, String centerLocation, Long start, Long limit, String userId) throws RepositoryException {
        return AgencyTransitManager.getInstance().queryByIPAndSome(startIP, endIP, iAgentName, centerLocation, start, limit, userId);
    }
}
