package com.ideal.controller.uploadrelyexcel;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ClientSessionHelper;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.uploadrelyexcel.DbUtilUpLoadRelyExcel;
/**
 * 
 * <ul>
 * <li>Title: UploadRelyExcelController.java</li>
 * <li>Description:迁移锦州银行userTasks依赖关系导入至主线作业调度</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2018年12月13日
 */
@Controller
public class UploadRelyExcelController
{

    @RequestMapping("uploadRelyExcelPage.do")
    public String getUploadRelyExcelPage(){
        return "importdependencyrelation/uploadRelyExcel";
    }
    
    /**
     * 
     * <li>Description:依赖关系excel导入</li> 
     * <AUTHOR> 2018年12月13日 迁移锦州银行userTasks依赖关系导入至主线作业调度
     * 2018年12月13日
     * @param file
     * @param request
     * @return
     * return Map
     */
    @RequestMapping("uploadRelyExcel.do")
    @ResponseBody
    public Map uploadRelyExcel(@RequestParam("file") CommonsMultipartFile file,
            HttpServletRequest request){
        Map map = new HashMap();
        UserInfo user = ClientSessionHelper.getUser(SessionData.getSessionData(request).getSessionId()); 
        try
        {
            String message = DbUtilUpLoadRelyExcel.getInstance().conlockTable(file, file.getOriginalFilename(), user);
            map.put("success", true);
            map.put("message", message);
        } catch (ServerException e)
        {
            e.printStackTrace();
        } catch (RepositoryException e)
        {
            e.printStackTrace();
        }
        return map;
    }
    
}
