//折线图初始化数据
//var line_store_initData = [];

Ext.Loader.setConfig({
  enabled:true,
  disableCaching:false,
  paths:{
    'Go':'js/ux/gooo'
  }
});

Ext.onReady(function() {
  destroyRubbish();
  Ext.tip.QuickTipManager.init ();
  var conponentHeight =  (contentPanel.getHeight()-20) * 0.5;
  var conponentWidth =  (contentPanel.getWidth()-20) /2;

  //--------part1 chars -----------------------------------
	
  	var donut = false;
	var bggl_colors = [ '#25ae5c','#dc5058' , '#b5b5b6' ];
	Ext.define('Ext.chart.theme.bgglFancy', {
		extend : 'Ext.chart.theme.Base',
		constructor : function(config) {
			this.callParent([ Ext.apply({
				colors : bggl_colors
			}, config) ]);
		}
	});

	Ext.define('Ext.chart.theme.CustomBlue', {
		extend : 'Ext.chart.theme.Base',
		constructor : function(config) {
			var titleLabel = {
				font : '14px 微软雅黑',
				fill : '#71ade1'
			}, axisLabel = {
				fill : '#6c7c90',
				font : '14px 微软雅黑',
				spacing : 2,
				padding : 5
			};

			this.callParent([ Ext.apply({
				axis : {
					stroke : '#16557b',
					'stroke-width' : 1
				},
				axisLabelLeft : axisLabel,
				axisLabelBottom : axisLabel,
				axisTitleLeft : titleLabel,
				axisTitleBottom : titleLabel
			}, config) ]);
		}
	});
	
// begin.added by manxi_zhao.2016-06-13.
	// 饼图数据模型
	Ext.define('pieModel', {
		extend : 'Ext.data.Model',
		fields : [ 
		    { name:'data', type:'long' }, 
		    { name:'state',type:'string' } 
		]
	});
	
	//条形图数据模型--年度TOP5
	Ext.define('barModel', {
		extend: 'Ext.data.Model',
		fields: [
		     { name:'TopName', type:'string' },
		     { name:'name', type:'string' },
		     { name:'total', type:'long' }
		]
	});
	
	//折线图数据模型--应急次数分布
	Ext.define('lineModel', {
		extend: 'Ext.data.Model',
		fields: [
		     { name:'months', type:'long' },
		     { name:'nums', type:'long' }
		]
	});
	
	//饼图数据源--应急饼图
	var pie_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'pieModel',
		proxy : {
			type : 'ajax',
			url : 'pieChartForEM.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
//    var pie_store = Ext.create('Ext.data.JsonStore', {
//    	fields: ['state', 'data'],
//		data: [
//		      { 'state': '正在应急',   'data':10},
//		      { 'state': '应急出错', 'data': 30 },
//		      { 'state': '未运行',  'data': 40}
//		]
//	});
    
	//条形图数据源--年度TOP5
	var bar_chart_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'barModel',
		proxy : {
			type : 'ajax',
			url : 'barChartForEM.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	//加载时查询top1的折线数据，重画折线图
	bar_chart_store.on ('load', function (store, options){
		store.each(function(record) {
			if(record.get('TopName')=='Top1'){
				var name = record.get('name');
				ss3.setTitle('应急次数分布图：'+name);
				//重新加载折线图数据
				line_store.load({
			    	params : {
			    		sysName: name
			    	}
				});
//				line_chart.redraw();
			}
		});
	 });
	
	//折线图数据源(初始)--应急次数分布
	var line_store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'lineModel',
		proxy : {
			type : 'ajax',
			url : 'lineChartForEM.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
//	var line_store = Ext.create('Ext.data.Store', {
//		model: 'lineModel',
//		data:[
//			{ months: 1, nums: 0 },
//			{ months: 2, nums: 0 },
//			{ months: 3, nums: 0 },
//			{ months: 4, nums: 0 },
//			{ months: 5, nums: 0 },
//			{ months: 6, nums: 0 },
//			{ months: 7, nums: 0 },
//			{ months: 8, nums: 0 },
//			{ months: 9, nums: 0 },
//			{ months: 10, nums: 0 },
//			{ months: 11, nums: 0 },
//			{ months: 12, nums: 0 }
//		]
//	});
	
	//应急操作--饼图
	var pie_chart=	Ext.create('Ext.chart.Chart', {
	  theme: 'bgglFancy', 
	  width: conponentWidth   *0.7,
	  height: conponentHeight *0.8,
	  animate: true,
	  store: pie_store,
	  shadow: true,
	  autoScroll : false,
	  legend: {
	      position: 'bottom',
	      //itemSpacing :0
	      boxFill :'#0d1a2d', 
	      boxStroke:'#d1a2d', 
	      labelFont :'14px 微软雅黑',
	      labelColor:'#6c7c90'
	  },
	    series: [{
	        type: 'pie',
	        angleField: 'data',
	        showInLegend: true,
	        donut: donut,
	        tips: {
	            trackMouse: true,
	            width: 140,
	            height: 28,
	            style:{background:'#26354c'},
	            renderer: function(storeItem, item) {
	                var total = 0;
	                pie_store.each(function(rec) {
	                    total += rec.get('data');
	                });
//	                var total =0;
//	                total =storeItem.get('total');
	                this.setTitle(storeItem.get('state') + ': ' + Math.round(((storeItem.get('data')/total))*100) + '%');
	            }
	        },
	        listeners : {  
	            itemclick : function(o) {  
	                var rec = pie_store.getAt(o.index); 	
	                var stateName = rec.get('state');
	                var state="-1";
	                
	        		if(stateName=="正在应急"){
	        			state="0";
	        		}
	        		else if(stateName=='应急出错'){
	        	        state="0";
	        	    }
	        		if(state!=-1){
	        			forword_switch('report/emMonitor.do','应急监控',state);
	        		}
	            }  
	        } ,
	        highlight: {
	            segment: {
	                margin: 50
	            }
	        },
	        label: {
	            field: 'state',
	            display: 'rotate',
	            contrast: true,
	            font: '14px Arial',
	            style :
		        {
			        fill : '#fff'
		        },
				 renderer : function(a, b, c, d, e, f, g, h) {
					return c.get('data');
				}
	        }, renderer: function(sprite, record, attr, index, store) {
//	        	var bggl_colors = [ '#0488cd','#b22463' , '#f7a42d' ];
	            var color = "";
	            if(record.get('state')=='正在应急'){
	              color = "#25ae5c";
	            }
	            else if(record.get('state')=='应急出错'){
//	              color = "#f7a42d";
	            	color = "#dc5058";
	            }
	            else if(record.get('state')=='未运行'){
//	              color = "#b22463";
	            	color = "#b5b5b6";
	            }
	            
	            return Ext.apply(attr, {
	              fill: color,
	              "stroke-width": 3,
	              "stroke-opacity": 0.2,
	              stroke: "#101315"
	            });
	          }
	    }]
	});
	
	// 应急次数分布图
	var line_chart = Ext.create('Ext.chart.Chart', {
       // style: 'background:#fff',
        width: conponentWidth   *1.92,
	    height: conponentHeight *0.9,
	    insetPadding: 20,
        animate: false,// 动画效果
        store: line_store,
        theme : 'CustomBlue',
        shadow: false,// 阴影
// theme: 'Category4',
//        legend: {
//            position: 'bottom'
//        },
        axes: [{
            type: 'Numeric',
            minimum: 0,
            position: 'left',
            fields: ['nums'],
            title: '次数',
            minorTickSteps: 1,
            grid:true,
            grid: {
                odd: {
                    opacity: 1,
                    fill: '#0d1a2d',
                    stroke: '#16557b',
                    'stroke-width': 0.5
                }
            },
            label: {
                renderer: Ext.util.Format.numberRenderer('0,0'),
//            	renderer: function(value) {
//                    var temValue = Math.round(value);
//                    if (temValue >= value) {
//                        if ((temValue - value) > 0.00001) {
//                            return "";
//                        } else {
//                            return temValue;
//                        }
//                    } else {
//                        return "";
//                    }
//                },
                font: '14px Arial',
                fill:'#6c7c90'
            }
        }, {
            type: 'Category',
            position: 'bottom',
            fields: ['months'],
            title: '月份'
        }],
        series: [{
            type: 'line',
            axis: 'left',
            xField: 'months',
            yField: 'nums',
            style: {
                fill: '#d69905',
                stroke: '#d69905',
                'stroke-width': 1
            },
	        tips: {
	            trackMouse: true,
	            width: 140,
	            height: 28,
	            style:{background:'#26354c'},
	            renderer: function(storeItem, item) {
	                // calculate and display percentage on hover
//	                var total = 0;
//	                line_store.each(function(rec) {
//	                    total = rec.get('nums');
//	                });
//	                total = item.value[1];
	                var total = storeItem.get('nums');
	                this.setTitle('本系统' + storeItem.get('months') + '月应急次数: ' + total);
	            }
	        },
            markerConfig: {
                type: 'circle',
                size: 3,
                radius: 3,
                'stroke-width': 0,
                fill: '#d69905',
                stroke: '#d69905'
            }
        }]
    });
	 
	//年度TOP5--条形图             
	var bar_chart = Ext.create('Ext.chart.Chart', {
		width : conponentWidth * 0.9,
		height : conponentHeight * 0.85,
		animate : true,
		shadow : true,
		store : bar_chart_store,
		axes : [ {
			type : 'Numeric',
			position : 'bottom',
			fields : [ 'total' ],
			label : {
				renderer : Ext.util.Format.numberRenderer('0,0')
			},
			title : '应急次数',
			grid : true,
			grid: {
                odd: {
                    opacity: 1,
                    fill: '#0d1a2d',
                    stroke: '#16557b',
                    'stroke-width': 0.5
                }
            },
			minimum : 0
		}, {
			type : 'Category',
			position : 'left',
//			fields : [ 'name' ],
			fields : [ 'TopName' ],
			title : '排行'
		} ],
		theme : 'CustomBlue',
//		 background : {
//					gradient : {
//						id : 'backgroundGradient',
//						angle : 45,
//						stops : {
//							0 : {
//								color : '#ffffff'
//							},
//							100 : {
//								color : '#eaf1f8'
//							}
//						}
//					}
//				},
		series : [ {
			type : 'bar',
			axis : 'bottom',
			renderer: function(sprite, storeItem, barAttr, i, store) {  
	            barAttr.fill = '#099be8';
	            return barAttr;  
	        },
			style :
	        {
		        fill : '#15181b',
		        stroke: '#15181b'
	        },
			highlight : true,
			highlightCfg :
	        {
		        fill : '#69fffa',
		        'stroke-width':1,
		        stroke: '#69fffa'
	        },
			tips : {
				trackMouse : true,
				width : 140,
				height : 28,
				style:{background:'#26354c'},
				renderer : function(storeItem, item) {
					this.setTitle(storeItem.get('name') + ' : '
							+ storeItem.get('total') + '次');
				}
			},
			listeners : {
				'itemclick' : function(item) {
					var storeItem = item.storeItem;
					var name = storeItem.get('name');
					ss3.setTitle('应急次数分布图：'+name);
					
//	                line_store.each(function(rec) {
//	                	var month = rec.get('months');
//	                	alert(month);
//	                });
					
					//重新加载折线图数据
					line_store.load({
				    	params : {
				    		sysName: name
				    	}
					});
					line_chart.redraw();
				}
			},
			label : {
				display : 'insideEnd',
				field : 'total',
				renderer : Ext.util.Format.numberRenderer('0'),
				orientation : 'horizontal',
				color : '#dcdddd',
				'text-anchor' : 'middle'
			},
			xField : 'name',
			yField : [ 'total' ]
		} ]
	});
//end.added by manxi_zhao.2016-06-13.

//--------part2 conponents -----------------------------------
  
  	//应急次数分布图	 Point(0,1)
	var linePanel =Ext.create('Ext.Panel', {
	    layout:'fit',
	    border: false,
	    items: [{
	    	border: false,
	        width: conponentWidth,
	        height:conponentHeight,
	        items:[line_chart]
	    }]
	});
	
	
	//年度TOP5 Point(1,0)
	var columnPanel =Ext.create('Ext.Panel', {
	    layout:'fit',
	    border: false,
	    items: [{
	    	border: false,
	        width: conponentWidth,
	        height:conponentHeight,
	        items : [bar_chart]
	    }]
	});
	
	//应急操作	Point(0,0)
	var piePanel =Ext.create('Ext.Panel', {
	    layout:'column',
	    border: false,
	    items: [{
	    	layout: {
	    	    type: 'vbox',
	    	    align: 'center'
	    	},
	    	//bodyPadding:'15 0 0 40',
	        columnWidth: 0.23,
	        border: false,
	        height:conponentHeight-10,
			loader : {
				url : 'pandectProgressForEM.do',
				autoLoad :true
			}
	    },{
	        columnWidth: 0.77,
	        height:conponentHeight-10,
	        items : [pie_chart]
	    }]
	});
		
//--------part3 layoutPane-----------------------------------	
	var ss1 = Ext.create('Ext.panel.Panel', {
		title : '应急操作',
		padding:5,
		width : (contentPanel.getWidth()-20) /2,
		height : conponentHeight,
		items : [ piePanel  ]
	});
	var ss2 = Ext.create('Ext.panel.Panel', {
//		title : '作业出错',
//		title : getTab('年度Top5','more','forwardSwitchMonitorGraphStarry.do','图形化监控'),//test.需要换成应急的***********************************************************
		title : '年度Top5',
		padding:5,
		width : (contentPanel.getWidth()-20) /2,
		height : conponentHeight,
		items : [ columnPanel ]
	});
	var ss3 = Ext.create('Ext.panel.Panel', {
//		title : '系统自动化灾备次数',
		title : '应急次数分布图',
		colspan:2,
		padding:5,
		width : (contentPanel.getWidth()-20),
		height : conponentHeight,
		items : [ linePanel ]
	});

	var layoutPane = Ext.create('Ext.panel.Panel', {
		width : '100%',
		height: contentPanel.getHeight(),
		border : false,
		layout : {
			type : 'table', 
			reserveScrollbar: true,
			columns : 2
		},
//		defaults : {
//			//bodyPadding : '10 15 105 15',
//			padding:5,
//			width : (contentPanel.getWidth()-20) /2,
//			height : conponentHeight
//		},
		autoScroll:true,
		items : [ss1, ss2, ss3]
	});

	
//--------part4 mainPane-----------------------------------
	
	  var MainPanel = Ext.create('Ext.panel.Panel', {
		renderTo : "disasterReport_area",
		width : '100%',
		height : '100%',
//		overflowY:'scroll',
		border : false,
		bodyPadding : 5,
		items : [ layoutPane ]
	});

	  
//-------part5   自适应----------------------------------------	  
	contentPanel.on('resize', function() {
		
		//lvl:1 mainPanel resize();
		conponentHeight =  (contentPanel.getHeight()-20) * 0.5;
		conponentWidth =  (contentPanel.getWidth()-20) /2;
		
		//lvl:2 layout Panel resize();
		layoutPane.setHeight(contentPanel.getHeight());
		
		//lvl:3 blockPanel resize();
		ss1.setHeight(conponentHeight);
		ss1.setWidth(conponentWidth);
		ss2.setHeight(conponentHeight);
		ss2.setWidth(conponentWidth);
		ss3.setHeight(conponentHeight);
		ss3.setWidth(contentPanel.getWidth()-20);
		
		
		//lvl:4 Graphical Panel resize();
		columnPanel.setHeight(conponentHeight);
		columnPanel.setWidth(conponentWidth);
		piePanel.setHeight(conponentHeight);
		piePanel.setWidth(conponentWidth);
		linePanel.setHeight(conponentHeight);
		linePanel.setWidth(contentPanel.getWidth()-20);

		
		//lvl:5 Gripch resize
		pie_chart.setHeight(conponentHeight *0.8);
		pie_chart.setWidth(conponentWidth   *0.8);

		//lvl:5 进度条高度的修改
		var bbgl_process_div = document.getElementsByClassName("progress");
		processHeightResizePX(bbgl_process_div,conponentHeight);
	});
	
	/**
	 * @desc 进度条div  的高度的动态修改
	 * **/
	function processHeightResizePX(arrProceeObj ,conponentHeight){
		for(var i=0;i<arrProceeObj.length;i++){
			var processHeight =arrProceeObj[i].style.height;
			processHeight =processHeight.replace("px","");
			arrProceeObj[i].style.height = (conponentHeight * 0.6) +"px" ; //进度条的自适应，应该依赖于所在panel的相对高度。
		}
	}
	
	function getTab(title,more,forword_url,targetContentTiel){
		var tab ="<table width='100%'border=0 >";
		    tab +=" <tr width='100%'>";
		    tab +="      <td align='left'>"+title+"</td> ";
		    tab +="      <td  align='right'><a href='javascript:void(0)' style='color:#b5b5b6; text-decoration:underline;' onclick='forword(\""+forword_url + "\"," +"\""+targetContentTiel + "\")'>"+more+"</a></td> ";
		    tab +=" </tr>";
		    tab +="</table>";
	     return tab;
	}
	function forword_switch(forword_url,targetContentTiel,state){
		contentPanel.setTitle(targetContentTiel);
	    destroyRubbish(); //销毁本页垃圾
		contentPanel.getLoader().load({url: forword_url,params:{istate:state},scripts: true});
		if(Ext.isIE){
			CollectGarbage(); 
		}
	}
	function forword_switchruninfo(forword_url,targetContentTiel,istarttime){
		contentPanel.setTitle(targetContentTiel);
	    destroyRubbish(); //销毁本页垃圾
		contentPanel.getLoader().load({url: forword_url,params:{istarttime:istarttime},scripts: true});
		if(Ext.isIE){
			CollectGarbage(); 
		}
	}
});

function forword(forword_url,targetContentTiel){
	contentPanel.setTitle(targetContentTiel);
    destroyRubbish(); //销毁本页垃圾
	contentPanel.getLoader().load({url: forword_url,scripts: true});
	if(Ext.isIE){
		CollectGarbage(); 
	}
}

////创建折线图
//function createLineChart(){
//	var chart = Ext.create('Ext.chart.Chart', {
//	       // style: 'background:#fff',
//	        width: conponentWidth   *1.92,
//		    height: conponentHeight *0.9,
//		    insetPadding: 20,
//	        animate: false,// 动画效果
//	        store: line_store,
//	        theme : 'CustomBlue',
//	        shadow: false,// 阴影
//	// theme: 'Category4',
////	        legend: {
////	            position: 'bottom'
////	        },
//	        axes: [{
//	            type: 'Numeric',
//	            minimum: 0,
//	            position: 'left',
//	            fields: ['nums'],
//	            title: '次数',
//	            minorTickSteps: 1,
//	            grid:true,
//	            grid: {
//	                odd: {
//	                    opacity: 1,
//	                    fill: '#2d3236',
//	                    stroke: '#15181b',
//	                    'stroke-width': 0.5
//	                }
//	            },
//	            label: {
//	                renderer: Ext.util.Format.numberRenderer('0,0'),
//	                font: '12px Arial',
//	                fill:'#b5b5b6'
//	            }
//	        }, {
//	            type: 'Category',
//	            position: 'bottom',
//	            fields: ['months'],
//	            title: '月份'
//	        }],
//	        series: [{
//	            type: 'line',
//	            axis: 'left',
//	            xField: 'months',
//	            yField: 'nums',
//	            style: {
//	                fill: '#00adfc',
//	                stroke: '#00adfc',
//	                'stroke-width': 1
//	            },
//		        tips: {
//		            trackMouse: true,
//		            width: 140,
//		            height: 28,
//		            renderer: function(storeItem, item) {
//		                // calculate and display percentage on hover
////		                var total = 0;
////		                line_store.each(function(rec) {
////		                    total = rec.get('nums');
////		                });
////		                total = item.value[1];
//		                var total = storeItem.get('nums');
//		                this.setTitle(storeItem.get('months') + '月应急次数: ' + total);
//		            }
//		        },
//	            markerConfig: {
//	                type: 'circle',
//	                size: 3,
//	                radius: 3,
//	                'stroke-width': 0,
//	                fill: '#00adfc',
//	                stroke: '#00adfc'
//	            }
//	        }]
//	    });
//	return chart;
//}


