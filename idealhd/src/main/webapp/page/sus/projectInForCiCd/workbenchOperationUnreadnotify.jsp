<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv" %>
<%@page contentType="text/html; charset=utf-8"%>

<html>
<head>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/sus/projectInForCiCd/noticeForwardCiCd.js"></script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/sus/projectInForCiCd/workbenchOperationUnreadnotify.js"></script>
    <script type="text/javascript">
        var title='<%=request.getParameter("title") %>';
        var fjnxBankSwitch= <%=ServerEnv.getInstance().getBankSwitchIsFjnx()%>;
        var zhuyeWin = '2';//该标识为了区分关闭弹窗时是未读
    </script>
</head>
<body>

<div id="workbenchOperationUnreadnotify_div">
</div>
</body>
</html>