var switchTaskAutoStartGrid1Store;
var switchTaskAutoStartWin;
Ext.onReady(function() {
	var switchTaskAutoStartForm1 = Ext.create('Ext.form.Panel', {
		region : 'north',
		bodyCls:'x-docked-noborder-top',
		baseCls:'customize_gray_back',
		border : false,
		dockedItems : [ {
			baseCls:'customize_gray_back',
			xtype : 'toolbar',
			dock : 'top',
			border : false,
			items : [ {
				xtype : 'textfield',
				name : 'queryString',
				emptyText : '请输入任务名称',
				 listeners: {
				    	specialkey: function(field,e){    
				        	if (e.getKey()==Ext.EventObject.ENTER){  
				        		switchTaskAutoStartGrid1.ipage.moveFirst();
				        	}  
				    	}
					}
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '查询',
				handler : querySwitchTaskAutoStart
			}, {
				xtype : 'button',
				cls : 'Common_Btn',
				text : '重置',
				handler : cancleSwitchTaskAutoStart
			},'->',{
                xtype: 'button',
                cls : 'Common_Btn',
                text: '停止',
                handler : function(){
                	updateSwitchTaskAutoStart('2');
                }
            },
            {
                xtype: 'button',
                cls : 'Common_Btn',
                text: '恢复',
                handler : function(){
                	updateSwitchTaskAutoStart('0');
                }
            } ]
		} ]
	});
	Ext.define('switchTaskAutoStartGrid1Model', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iswitchtaskautostartid',
			type : 'string'
		}, {
			name : 'iworkitemid',
			type : 'string'
		}, {
			name : 'taskName',
			type : 'string'
		}, {
			name : 'switchDir',
			type : 'string'
		}, {
			name : 'switchType',
			type : 'string'
		}, {
			name : 'switchDesc',
			type : 'string'
		}, {
			name : 'iexecuser',
			type : 'string'
		}, {
			name : 'istartuser',
			type : 'string'
		}, {
			name : 'iwindowStart',
			type : 'string'
		}, {
			name : 'iwindowEnd',
			type : 'string'
		},{
			name : 'istate',
			type : 'string'
		}, {
			name : 'taskid',
			type : 'string'
		} ]
	});
	switchTaskAutoStartGrid1Store = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 30,
		model : 'switchTaskAutoStartGrid1Model',
		proxy : {
			type : 'ajax',
			url : 'getSwitchTaskAutoStartList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
	switchTaskAutoStartGrid1Store.on('beforeload', function (store, options) {
	    var new_params = {  
	    	queryString:switchTaskAutoStartForm1.getForm().findField("queryString").getValue()
	    };
	    Ext.apply(switchTaskAutoStartGrid1Store.proxy.extraParams, new_params);
    });

	var switchTaskAutoStartGrid1 = Ext.create('Ext.ux.ideal.grid.Panel', {
		cls:'customize_panel_back',
		ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		padding : grid_space,
		columnLines : true,
		columns : [ {
            xtype: 'rownumberer',
            text : '序号',
            width : 50
        },{
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iswitchtaskautostartid',
			text : '预启动任务主键'
		}, {
			xtype : 'gridcolumn',
			hidden : true,
			dataIndex : 'iworkitemid',
			text : '双人复核任务主键'
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'taskName',
			text : '任务名称',
			width : 120,
			renderer: function(value,metaData,record,colIndex,store,view) {
	               metaData.tdAttr = 'data-qtip="' + value + '"';
	            return value;  
	        }
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'switchDir',
			text : '切换方向',
			width : 100,
			renderer: function(value,metaData,record,colIndex,store,view) {
	               metaData.tdAttr = 'data-qtip="' + value + '"';
	            return value;  
	        }
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'switchType',
			text : '切换类型',
			width : 100,
			renderer: function(value,metaData,record,colIndex,store,view) {
	               metaData.tdAttr = 'data-qtip="' + value + '"';
	            return value;  }
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'switchDesc',
			text : '切换内容描述',
			flex : 1,
			renderer: function(value,metaData,record,colIndex,store,view) {
	               metaData.tdAttr = 'data-qtip="' + value + '"';
	            return value;  }
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'iexecuser',
			text : '审核人',
			width : 140
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'istartuser',
			text : '发起人',
			width : 140
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'iwindowStart',
			text : '切换窗口：开始',
			width : 160
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'iwindowEnd',
			text : '切换窗口：结束',
			width : 160
		}, {
			xtype : 'gridcolumn',
			dataIndex : 'istate',
			text : '状态',
			width : 70,
			renderer : function(value){
				var backValue = "";
				if(value=='0'){
					backValue = "<span class='Blue_color State_Color'>未启动</span>";
				}else if(value=='1'){
					backValue = "<span class='Green_color State_Color'>已启动</span>";
				}else if(value=='2'){
					backValue = "<span class='Gray_color State_Color'>已停止</span>";
				}
				return backValue
			}
			
		}, {
			xtype : 'gridcolumn',
			text : '操作',
			width : 200,
			renderer : function(value, p, record, rowIndex) {
				var taskId = record.get('iworkitemid');
				return '<span class="switch_span"><a href="javascript:void(0)" onclick="getInstanceinfoSwitchTaskAutoStart('+ taskId+ ','+ rowIndex+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_search"></img>查看</a>&nbsp;&nbsp;<a href="javascript:void(0)" onclick="startTaskSwitchTaskAutoStart('+ taskId+ ','+ rowIndex+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_execute"></img>启动</a>&nbsp;&nbsp;<a href="javascript:void(0)" onclick="cleanTaskSwitchTaskAutoStart('+ taskId+ ','+ rowIndex+ ')"><img src="images/monitor_bg.png" align="absmiddle" class="monitor_delete"></img>取消</a></span>';
			}
		} ],
		store : switchTaskAutoStartGrid1Store
	});
	var switchTaskAutoStartPanel1 = Ext.create('Ext.panel.Panel', {
		renderTo : 'switchTaskAutoStart_div',
		border : true,
		height : contentPanel.getHeight() - modelHeigth,
		width : contentPanel.getWidth(),
		layout : 'border',
		bodyPadding : grid_margin,
		bodyCls: 'service_platform_bodybg',
		items : [ switchTaskAutoStartForm1, switchTaskAutoStartGrid1 ]
	});
	contentPanel.on('resize', function() {
		switchTaskAutoStartPanel1.setHeight(contentPanel.getHeight()
				- modelHeigth);
		switchTaskAutoStartPanel1.setWidth(contentPanel.getWidth());
	});
	
	function querySwitchTaskAutoStart(){
		if (Ext.isIE) {
			CollectGarbage();
		}
		switchTaskAutoStartGrid1.ipage.moveFirst();
	}
	
	function cancleSwitchTaskAutoStart(){
		switchTaskAutoStartForm1.getForm().findField("queryString").setValue('');
	}
	
	function updateSwitchTaskAutoStart(istate){
		var data = switchTaskAutoStartGrid1.getView().getSelectionModel().getSelection();
		if (data.length != 1) {
			Ext.Msg.alert('提示', '请选择一条记录进行此操作！');
			return;
		}
		if(data[0].get('istate')!='0'&&istate=='2'){
			Ext.Msg.alert('提示', '只能对状态为未启动的数据进行此操作！');
			return;
		}
		if(data[0].get('istate')!='2'&&istate=='0'){
			Ext.Msg.alert('提示', '只能对状态为已停止的数据进行此操作！');
			return;
		}
		Ext.Ajax.request( {
			url : 'updateSwitchTaskAutoStart.do',
			method : 'post',
			params : {
				iswitchtaskautostartid : data[0].get('iswitchtaskautostartid'),
				istate : istate
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				if (success) {
					switchTaskAutoStartGrid1Store.load();
					Ext.Msg.alert('提示', '操作成功！');
				} else {
					Ext.Msg.alert('提示', '操作失败！');
				}
			},
			failure : function(result, request) {
		    	secureFilterRs(result,"操作失败！");
			}
		});
	}

});
//查看
function getInstanceinfoSwitchTaskAutoStart(taskId, rowIndex) {
	var taskName = switchTaskAutoStartGrid1Store.getAt(rowIndex).get('taskName');
	switchTaskAutoStartWin = Ext.create('Ext.window.Window', {
		title : taskName,
		autoScroll : true,
		modal : true,
		resizable : false,
		border : false,
		closeAction : 'destroy',
		width : contentPanel.getWidth() - 50,
		height : contentPanel.getHeight() - 20,
		loader : {
			url : "getTaskinfo.do",
			params : {
				taskId : taskId
			},
			autoLoad : true,
			autoDestroy : true,
			scripts : true
		}
	}).show();
}
//启动
function startTaskSwitchTaskAutoStart(taskId, rowIndex) {
	var taskName = switchTaskAutoStartGrid1Store.getAt(rowIndex).get('taskName');
	Ext.Msg.confirm("确认启动", "任务名称:"+taskName+",确定启动此切换吗？", function(id) {
		if (id == 'yes'){
			Ext.Ajax.request( {
				url : 'startTaskInfo.do',
				method : 'post',
				params : {
					taskId : taskId,
					isForce : 0
				},
				success : function(response, request) {
					var success = Ext.decode(response.responseText).success;
					if (success) {
						switchTaskAutoStartGrid1Store.load();
						Ext.Msg.alert('提示', '操作成功！');
					} else {
						Ext.Msg.alert('提示', '操作失败！');
					}
				},
				failure : function(result, request) {
			    	secureFilterRs(result,"操作失败！");
				}
			});
		}
	});
}
//取消
function cleanTaskSwitchTaskAutoStart(taskId, rowIndex) {
	var taskName = switchTaskAutoStartGrid1Store.getAt(rowIndex).get('taskName');
	Ext.MessageBox.buttonText.yes = "确定";
	Ext.MessageBox.buttonText.no = "取消";
	Ext.Msg.confirm("确认取消", "任务名称:"+taskName+",确定取消此任务", function(id) {
		if (id == 'yes')
			delteTaskInfoSwitchTaskAutoStart(taskId);
	});
}
function delteTaskInfoSwitchTaskAutoStart(taskId){
	Ext.Ajax.request( {
		url : 'delteTaskInfo.do',
		method : 'post',
		params : {
			taskId : taskId,
			cancel:true
		},
		success : function(response, request) {
			var success = Ext.decode(response.responseText).success;
			if (success) {
				switchTaskAutoStartGrid1Store.load();
				Ext.Msg.alert('提示', '取消成功！');
			} else {
				Ext.Msg.alert('提示', '取消失败！');
			}
		},
		failure : function(result, request) {
	    	secureFilterRs(result,"操作失败！");
		}
	});
}