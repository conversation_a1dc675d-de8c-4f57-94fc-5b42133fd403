package com.ideal.ieai.server.repository.warning;

public class RepNodeWarningHistory
{
    public long   id;
    public String prjName;
    public String flowName;
    public String actName;
    public long   type;
    public long   warnTime;
    public long   delTime;
    public String delUser;
    public String delDes;
    public String warnDes;

    public long getDelTime ()
    {
        return delTime;
    }

    public void setDelTime ( long delTime )
    {
        this.delTime = delTime;
    }

    public String getDelUser ()
    {
        return delUser;
    }

    public void setDelUser ( String delUser )
    {
        this.delUser = delUser;
    }

    public String getDelDes ()
    {
        return delDes;
    }

    public void setDelDes ( String delDes )
    {
        this.delDes = delDes;
    }

    public String getWarnDes ()
    {
        return warnDes;
    }

    public void setWarnDes ( String warnDes )
    {
        this.warnDes = warnDes;
    }

    public long getId ()
    {
        return id;
    }

    public void setId ( long id )
    {
        this.id = id;
    }

    public String getPrjName ()
    {
        return prjName;
    }

    public void setPrjName ( String prjName )
    {
        this.prjName = prjName;
    }

    public String getFlowName ()
    {
        return flowName;
    }

    public void setFlowName ( String flowName )
    {
        this.flowName = flowName;
    }

    public String getActName ()
    {
        return actName;
    }

    public void setActName ( String actName )
    {
        this.actName = actName;
    }

    public long getType ()
    {
        return type;
    }

    public void setType ( long type )
    {
        this.type = type;
    }

    public long getWarnTime ()
    {
        return warnTime;
    }

    public void setWarnTime ( long warnTime )
    {
        this.warnTime = warnTime;
    }
}
