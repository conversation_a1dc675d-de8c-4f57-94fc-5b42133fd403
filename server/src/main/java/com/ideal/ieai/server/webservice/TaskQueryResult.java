package com.ideal.ieai.server.webservice;

import java.util.List;

import com.ideal.ieai.commons.task.QueryTaskResult;
import com.ideal.ieai.commons.task.TaskDetail;
import com.ideal.ieai.server.engine.util.WorkflowInstance;

/**
 * <p>
 * Title: TaskQueryResult
 * </p>
 * <p>
 * Description: Class to hold result of a task query, including result tasks and
 * jobs those tasks belong to.
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003, 2004
 * </p>
 * <p>
 * Company: Ideal Technologies Inc.
 * </p>
 * 
 * <AUTHOR> attributable
 * @version 3.0
 */
public class TaskQueryResult
{
    private TaskInfo[]     _taskInfos;
    private WorkflowInfo[] _flowInfos;
    
    private int                   totalRecord=0;


    public TaskInfo[] getTaskInfos ()
    {
        return _taskInfos;
    }

    public void setFlowInfos ( WorkflowInfo[] flowInfos )
    {
        this._flowInfos = flowInfos;
    }

    public void setTaskInfos ( TaskInfo[] taskInfos )
    {
        this._taskInfos = taskInfos;
    }

    public WorkflowInfo[] getFlowInfos ()
    {
        return _flowInfos;
    }

    public TaskQueryResult()
    {
    }

    static public TaskQueryResult newInstance ( QueryTaskResult qtr )
    {
        if (null == qtr)
        {
            return null;
        }
        List taskDetails = qtr.getTaskDetails();
        TaskInfo[] taskInfos = new TaskInfo[taskDetails.size()];
        for (int i = 0; i < taskDetails.size(); i++)
        {
            taskInfos[i] = TaskInfo
                    .newInstance((TaskDetail) taskDetails.get(i));
        }

        List flowInfoList = qtr.getFlowInfos();
        WorkflowInfo[] flowInfos = new WorkflowInfo[flowInfoList.size()];
        for (int i = 0; i < flowInfoList.size(); i++)
        {

            flowInfos[i] = WorkflowInfo
                    .newInstance((WorkflowInstance) flowInfoList
                            .get(i));
        }
        
      
        TaskQueryResult result = new TaskQueryResult();
        result.setFlowInfos(flowInfos);
        result.setTaskInfos(taskInfos);
        result.setTotalRecord(qtr.getTotalRecord());
        return result;
    }

    public int getTotalRecord ()
    {
        return totalRecord;
    }

    public void setTotalRecord ( int totalRecord )
    {
        this.totalRecord = totalRecord;
    }
}
