/*
 * Created on 2006-4-26
 */
package com.ideal.ieai.commons.notice;

/**
 * notices that take effect when the notice is in it's acting time. take a look at the requirement
 * of notice.
 * 
 * <AUTHOR> Shi
 */
public class TimeNotice extends UserNotice
{

    public TimeNotice()
    {
        super();
    }

    private ShortTime _begin;
    private ShortTime _end;

    /**
     * @return Returns the begin.
     */
    public ShortTime getBegin ()
    {
        return _begin;
    }

    /**
     * @param begin The begin to set.
     */
    public void setBegin ( ShortTime begin )
    {
        _begin = begin;
    }

    /**
     * @return Returns the end.
     */
    public ShortTime getEnd ()
    {
        return _end;
    }

    /**
     * @param end The end to set.
     */
    public void setEnd ( ShortTime end )
    {
        _end = end;
    }

}
