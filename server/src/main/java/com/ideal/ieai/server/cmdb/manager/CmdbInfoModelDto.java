package com.ideal.ieai.server.cmdb.manager;

/**
 * <AUTHOR>
 */
public class CmdbInfoModelDto {

    private Long id;

    private String ip;

    private String hostName;

    private String systemInfo;

    private String  centerName;

    private String  sysAdmin;

    private String appAdmin;

    private String  collectResult;

    private String  keyName;

    private String cmdbUpdateLasttime;

    private String  cmdbUpdateDesc;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getSystemInfo() {
        return systemInfo;
    }

    public void setSystemInfo(String systemInfo) {
        this.systemInfo = systemInfo;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public String getSysAdmin() {
        return sysAdmin;
    }

    public void setSysAdmin(String sysAdmin) {
        this.sysAdmin = sysAdmin;
    }

    public String getAppAdmin() {
        return appAdmin;
    }

    public void setAppAdmin(String appAdmin) {
        this.appAdmin = appAdmin;
    }

    public String getCollectResult() {
        return collectResult;
    }

    public void setCollectResult(String collectResult) {
        this.collectResult = collectResult;
    }

    public String getKeyName() {
        return keyName;
    }

    public void setKeyName(String keyName) {
        this.keyName = keyName;
    }

    public String getCmdbUpdateDesc() {
        return cmdbUpdateDesc;
    }

    public String getCmdbUpdateLasttime() {
        return cmdbUpdateLasttime;
    }

    public void setCmdbUpdateLasttime(String cmdbUpdateLasttime) {
        this.cmdbUpdateLasttime = cmdbUpdateLasttime;
    }

    public void setCmdbUpdateDesc(String cmdbUpdateDesc) {
        this.cmdbUpdateDesc = cmdbUpdateDesc;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

}
