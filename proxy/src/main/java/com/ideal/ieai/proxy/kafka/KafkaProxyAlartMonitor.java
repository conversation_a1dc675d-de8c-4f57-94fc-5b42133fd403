package com.ideal.ieai.proxy.kafka;

import java.util.Hashtable;

import org.apache.log4j.Logger;

import com.ideal.ieai.proxy.utils.SendMessageUtile;
import com.ideal.util.UUID;

public class KafkaProxyAlartMonitor implements Runnable
{

    private static final Logger log      = Logger.getLogger(KafkaProxyAlartMonitor.class);
    
        
    
    private boolean alarmSwitch=false;
    private String proxyIp;
    private String proxyName;
    private String maxretrytimes;
    
    
    /**
     * 
     * @param alarmServer 报警服务器IP
     * @param alarmPort   报警服务器端口 
     * @param proxyIp       当前proxyIP
     * @param proxyName     当前proxy名称
     * @param retryTimes    当前proxy最大重试次数
     */
    public KafkaProxyAlartMonitor(String alarmServer,Integer alarmPort,String proxyIp,String proxyName,String retryTimes) {
        this.proxyIp=proxyIp;
        this.proxyName=proxyName;
        this.maxretrytimes=retryTimes;
        
    }
    
    @Override
    public void run ()
    {
        while(true) {
            try
            {
                this.getAlarmSwitch();
                if(alarmSwitch) {
                    sendAlarmMsg(); //告警
                    
                }else {
                    Thread.sleep(3000);
                }
                
            } catch (InterruptedException e)
            {
                log.info(Thread.currentThread().getName() + " has been Interrupted:" + e);
                Thread.currentThread().interrupt();
            }
        }
        
    }
    
    private synchronized void getAlarmSwitch() {
        this.alarmSwitch = KafkaProxyLauncher.getInstance().getAlarSwitch();
    }
    
    
   private void sendAlarmMsg() {
       
       KafkaStatusModel kafkaStatus = KafkaProxyLauncher.getInstance().statstisNum(new KafkaStatusModel(), "ADD"); // 获取当前proxy状态数据
        
        Hashtable output = new Hashtable();
        
        output.put("iip", proxyIp);
        output.put("iname", proxyName);
        output.put("ilostnum", kafkaStatus.getFailcount().toString());
        output.put("ialarmmsg", "proxy has retried "+maxretrytimes+" times");
        
        
        SendMessageUtile.sendToServer("KAFKA-ALARM"+UUID.uuid(), output);
        
        KafkaProxyLauncher.getInstance().changeAlarSwitch(false);
    }

}
