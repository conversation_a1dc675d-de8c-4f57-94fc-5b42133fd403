-- 4.7.21
CREATE TABLE IF NOT EXISTS IEAI_STRING_QUENY (IID NUMERIC(19) NOT NULL ,IUUID VARCHAR(255) NOT NULL ,ICONTENT LONGTEXT );
-- 4.7.23
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISWHITEANDIP') THEN
        ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISWHITEANDIP VARCHAR(30);
        END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISDELETE') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISDELETE NUMERIC(1) DEFAULT 0 ;
END IF;
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'ISDELETEINFO') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISDELETEINFO VARCHAR(255) ;
END IF;
END;;
	DELIMITER ;
	CALL UPPRDE();

CREATE TABLE IF NOT EXISTS IEAI_ACTIVITY_MANAGE (IID NUMERIC(19)NOT NULL,IACTNAME VARCHAR(50), IACTDES VARCHAR(1000), IACTTYPE NUMERIC(2),ISVALID NUMERIC(2) DEFAULT 0,CONSTRAINT PK_IEAI_ACTIVITY_MANAGE PRIMARY KEY (IID));
CREATE TABLE IF NOT EXISTS IEAI_PORT (IID DECIMAL(19)NOT NULL,IPORTNAME VARCHAR(50),IPORTDES VARCHAR(1000),CONSTRAINT PK_IEAI_PORT PRIMARY KEY (IID));
CREATE TABLE IF NOT EXISTS IEAI_ACTIVITY_PORT (IID NUMERIC(19)NOT NULL,ACTID NUMERIC(19),PORTID NUMERIC(19),FOREIGN KEY(ACTID)REFERENCES IEAI_ACTIVITY_MANAGE(IID),FOREIGN KEY(PORTID)REFERENCES IEAI_PORT(IID),CONSTRAINT PK_IEAI_ACTIVITY_PORT PRIMARY KEY(IID));
-- 4.7.27
CREATE TABLE IF NOT EXISTS IEAI_EXECACT_PROGRESS (IID NUMERIC(19) NOT NULL ,IFLOWID NUMERIC(19) DEFAULT 0 ,ICREATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ,IACTID NUMERIC(10) DEFAULT 0 ,BHIACTID VARCHAR(255) ,IACTNAME VARCHAR(255) ,BHIACTNAME VARCHAR(255) ,CONSTRAINT PK_IEAI_EXECACT_PROGRESS PRIMARY KEY (IID));
ALTER  TABLE ieai_project_info_ws MODIFY COLUMN IRELEASETIME  datetime ;

DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT_INFO_WS' AND COLUMN_NAME = 'IEDITUSER') THEN
    ALTER TABLE IEAI_PROJECT_INFO_WS ADD  IEDITUSER NUMERIC(19,0) default -1;
    update IEAI_PROJECT_INFO_WS set IEDITUSER = ICREATEUSRID where IEDITUSER = -1;
END IF;	
END;;
	DELIMITER ;
	CALL UPPRDE();
update IEAI_PROJECT_TYPE_WS set IPROPERTYIDS='3,5' where IID =4;

delete from IEAI_PROJECT_TYPE_WS where IID in (300,400);
INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (300, 'IEAI_SUS_GROUP', '变更组合编排' , '5');
INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (400, 'IEAI_AZ_GROUP', 'Az组合编排' , '5');

delete from IEAI_DBSOURCE where IDBSOURCEID in (300,400);
INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (300,300,'变更组合编排源','','','','',0);
INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (400,400,'Az组合编排源','','','','',0);

delete from IEAI_PROJECT where IID in (-300,-400);
INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-400, 0, '所有Az组合编排业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 400, -400, -400, -400);
INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES (-300, 0, '所有变更组合编排业务系统', 0, 0, 0, '', '', 0, '', '', 0, 0, 300, -300, -300, -300);
delete from IEAI_GROUPMESSAGE where GROUPID in (300,400);

INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (300, '变更组合编排', '变更组合编排模块组', 300, 'images/info82.png');
INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (400, 'Az组合编排', 'Az组合编排模块组', 400, 'images/info82.png');

CREATE TABLE IF NOT EXISTS IEAI_TEMPLATE_CALL_ACT (IID DECIMAL(19,0) NOT NULL, IORDERID DECIMAL(19,0), IFLOWID DECIMAL(19,0), ISTATE  DECIMAL(19,0), ITIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP, IEXECACTIID DECIMAL(19,0),IENVID DECIMAL(19,0),NODENAME VARCHAR(255), CONSTRAINT PK_IEAI_TEMPLATE_CALL_ACT PRIMARY KEY(IID));
CREATE TABLE IF NOT EXISTS IEAI_TEMPLATE_CALL_REL (IID DECIMAL(19,0) NOT NULL, ICALLACTID DECIMAL(19,0) NOT NULL, IORDERID DECIMAL(19,0), ITEMPLATEUUID VARCHAR(255), ISTATE  DECIMAL(19,0), ITIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP, IFLOWID DECIMAL(19,0), CONSTRAINT PK_IEAI_TEMPLATE_CALL_REL PRIMARY KEY(IID));
DELIMITER ;;
	DROP PROCEDURE IF EXISTS PROC_WORKFLOW_QUERY_GROUP_JOB;;
	CREATE PROCEDURE PROC_WORKFLOW_QUERY_GROUP_JOB  (IN AN_USERID decimal(19,0), OUT AN_OPT_ID decimal(19,0), IN AV_PRJ_NAME varchar(255), IN AV_FLOW_NAME varchar(255), IN AV_START_USER varchar(255), IN AV_INSTANCE_NAME varchar(255), IN AI_START_TIME_FROM bigint(20), IN AI_START_TIME_TO bigint(20), IN AI_END_TIME_FROM bigint(20), IN AI_END_TIME_TO bigint(20), IN AI_SHOW_ACTIVE smallint(6), IN AI_SHOW_RUN_SELF smallint(6), IN AI_NUM_OF_PAGE smallint(6), IN AI_PAGE_ID int(11), OUT AI_REC_COUNT int(11), IN AV_ORDER_ITEM varchar(80), IN AI_ORDER_TYPE smallint(6), IN AN_CHECK_DATE decimal(19,0), IN AV_HOST_NAME varchar(50), IN AN_FLOW_ID decimal(19,0),IN AN_PROTYPE decimal,IN  AN_STATUS  SMALLINT)
		SQL SECURITY DEFINER
		NOT DETERMINISTIC
		CONTAINS SQL
	BEGIN
		DECLARE	LN_OPT_ID	NUMERIC(19,0);	
		DECLARE	LV_SQL		VARCHAR(4000);	
		DECLARE	LI_ZONE		SMALLINT;	
		DECLARE	LV_FORMAT	VARCHAR(50);	
		DECLARE	LV_ORDER_BY	VARCHAR(100);
		DECLARE	LN_MILLISEC	NUMERIC(19);	
		DECLARE	LV_WHERE	VARCHAR(4000);	
		DECLARE	LI_PAGE_ID	INTEGER;	
		DECLARE	LI_MAX_RECID	INTEGER;	
		DECLARE	LI_MIN_RECID	INTEGER;	
		DECLARE	LI_MAX_PAGEID	INTEGER;	
		DECLARE	LI_PAGE_COUNT	INTEGER;	
		
		DECLARE	SI_TIMEZONE_CUR SMALLINT DEFAULT 8;	
		DECLARE	SI_TIMEZONE_GMT SMALLINT DEFAULT 0;	
		
		DECLARE	LI_RETURN	SMALLINT;
		
		DECLARE LN_ISALL    NUMERIC(2,0);
			
		
		IF	AI_NUM_OF_PAGE IS NULL OR AI_NUM_OF_PAGE <= 0	THEN
			SET LI_RETURN = 0;
		ELSE 		CALL	PROC_GET_NEXT_ID('TMP_USER_VALIDATE', LN_OPT_ID);
				
			SET	AN_OPT_ID = LN_OPT_ID;
			
			SELECT COUNT(*)  INTO LN_ISALL
			FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
			WHERE T.IROLEID = T2.IROLEID   
			  AND T2.IPERMISSION = 1
			  AND T.IUSERID =AN_USERID AND T2.IPROID =-AN_PROTYPE ;   
				
			SET	LI_ZONE = 0;				
			SET	LV_FORMAT = 'YYYY-MM-DD HH24:MI:SS';	
				
			
			
			
			IF  LN_ISALL>0 THEN 
				INSERT	INTO	TMP_USER_VALIDATE
					(
						OPT_ID,
						PRJ_ID,
						PRJ_NAME,
						PERMIT_ID,
						PERMIT_NAME
					)
				SELECT LN_OPT_ID AS OPT_ID,IID AS PRJ_ID,INAME AS PRJ_NAME, 0 AS PERMIT_ID,1 AS PERMIT_NAME
				FROM IEAI_PROJECT 
				WHERE PROTYPE=AN_PROTYPE 
				  AND (IPKGCONTENTID<>0 OR IPKGCONTENTID IS NULL)  
				  AND IUPPERID=IID ;
			ELSE 
				INSERT	INTO	TMP_USER_VALIDATE
					(
						OPT_ID,
						PRJ_ID,
						PRJ_NAME,
						PERMIT_ID,
						PERMIT_NAME
					)
				SELECT LN_OPT_ID AS OPT_ID,PRJ.IID AS PRJ_ID,PRJ.INAME AS PRJ_NAME, 0 AS PERMIT_ID,1 AS PERMIT_NAME
				FROM IEAI_PROJECT PRJ,IEAI_SYS_PERMISSION ISP,IEAI_USERINHERIT IU
				WHERE IU.IUSERID=AN_USERID
				AND ISP.IROLEID=IU.IROLEID
				AND PRJ.IID=ISP.IPROID
				AND ISP.IPROID>0
				AND ISP.IPERMISSION=1
				AND PRJ.PROTYPE=AN_PROTYPE AND (PRJ.IPKGCONTENTID<>0 OR PRJ.IPKGCONTENTID IS NULL);
			END IF;		SET	LN_MILLISEC = AN_CHECK_DATE;
				
			
			SET	LV_WHERE = '';
				
			
			IF	LENGTH(AV_PRJ_NAME) > 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IPROJECTNAME LIKE (' , CHAR(39) , '%' , AV_PRJ_NAME , '%' , CHAR(39) , ')');
			END	IF;
			
			
			IF	LENGTH(AV_FLOW_NAME) > 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWNAME LIKE (' , CHAR(39) , '%' , AV_FLOW_NAME , '%' , CHAR(39) , ')');
			END	IF;
			
			
			IF	LENGTH(AV_START_USER) > 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTARTUSERFULLNAME LIKE (' , CHAR(39) , '%' , AV_START_USER , '%' , CHAR(39) , ')');
			END	IF;
			
			
			IF	LENGTH(AV_INSTANCE_NAME) > 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWINSNAME LIKE (' , CHAR(39) , '%' , AV_INSTANCE_NAME , '%' , CHAR(39) , ')');
			END	IF;
			
			
			IF	AI_START_TIME_FROM > 0	THEN
				IF	AI_START_TIME_TO > 0	THEN
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND (C.ISTARTTIME BETWEEN ' , AI_START_TIME_FROM , ' AND ' , AI_START_TIME_TO , ')');
				ELSE
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTARTTIME >= ' , AI_START_TIME_FROM);
				END	IF;
			ELSE
				IF	AI_START_TIME_TO > 0	THEN
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTARTTIME <= ' , AI_START_TIME_TO);
				END	IF;	
			END	IF;
			
			
			IF	AI_END_TIME_FROM > 0	THEN
				IF	AI_END_TIME_TO > 0	THEN
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND (C.IENDTIME BETWEEN ' , AI_END_TIME_FROM , ' AND ' , AI_END_TIME_TO , ')');
				ELSE
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IENDTIME >= ' , AI_END_TIME_FROM);
				END	IF;
			ELSE
				IF	AI_END_TIME_TO > 0	THEN
					SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IENDTIME <= ' , AI_END_TIME_TO);
				END	IF;
			END	IF;
			
			
			IF	AI_SHOW_ACTIVE = 1	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTATUS IN (0, 6, 8, 15, 30)');
			END	IF;
			
			
			IF	AI_SHOW_RUN_SELF = 1	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IISAUTOSTART = 1');
			END	IF;
			
			
			IF	LENGTH(AV_HOST_NAME) > 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IHOSTNAME LIKE (' , CHAR(39) , '%' , AV_HOST_NAME , '%' , CHAR(39) , ')');
			END	IF;
			
			
			IF	AN_FLOW_ID > 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.IFLOWID = ' , AN_FLOW_ID);
			END	IF;
			IF	AN_STATUS >= 0	THEN
				SET	LV_WHERE = CONCAT(LV_WHERE , ' AND C.ISTATUS = ' , AN_STATUS);
			END	IF;
				
			
			DELETE	FROM	TMP_USER_INVALIDATE
				WHERE	OPT_ID = LN_OPT_ID;
				
			
			SET	@LV_SQL = CONCAT('INSERT INTO TMP_USER_INVALIDATE (OPT_ID, PRJ_ID, PERMIT_ID) SELECT ' , LN_OPT_ID , ', 0, 1 FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C WHERE A.OPT_ID = ' , LN_OPT_ID , ' AND C.IPRJUPPERID = A.PRJ_ID' , LV_WHERE);
			PREPARE	SQLA FROM @LV_SQL;
			EXECUTE SQLA;
			
			GET DIAGNOSTICS LI_MAX_RECID = ROW_COUNT;
			
			SET	LI_PAGE_COUNT	= ceil(LI_MAX_RECID / AI_NUM_OF_PAGE);
			IF LI_PAGE_COUNT = 0 THEN
				SET LI_PAGE_COUNT = 1;
			END IF;
			
			IF	MOD(LI_MAX_RECID, AI_NUM_OF_PAGE) > 0	THEN
				SET	LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
			END	IF;
			
			IF	AI_PAGE_ID > LI_PAGE_COUNT	THEN
				SET	LI_PAGE_ID = LI_PAGE_COUNT;
			ELSE
				SET	LI_PAGE_ID = AI_PAGE_ID;
			END	IF;
			
			IF	LI_PAGE_ID IS NULL OR LI_PAGE_ID < 0	THEN
				SET	LI_PAGE_ID = 1;
			END	IF;
			
			
			SET	AI_REC_COUNT = LI_MAX_RECID;
			
			
			SET	LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
			SET	LI_MAX_RECID = LI_PAGE_ID * AI_NUM_OF_PAGE;
			
			
			SET	LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID FROM (SELECT A.OPT_ID AS OPTID, ';
			IF	AI_ORDER_TYPE = 1	THEN
				SET	LV_ORDER_BY = ' ORDER BY 2 ASC';
			ELSE
				SET	LV_ORDER_BY = ' ORDER BY 2 DESC';
			END	IF;
			
			IF	UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'FLOW_ID'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'STATUS'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'START_USER'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'START_TIME'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.ISTARTTIME AS ISTART_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'END_TIME'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'PAID_TIME'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30) THEN ' , LN_MILLISEC , ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ');
			ELSEIF	UPPER(AV_ORDER_ITEM) = 'TASK_NUM'	THEN
				SET	LV_SQL = CONCAT(LV_SQL , 'IFNULL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ');
			ELSE
				SET	LV_ORDER_BY = ' ORDER BY 2 DESC';
				SET	LV_SQL = CONCAT(LV_SQL , 'C.IFLOWID AS FLOW_ID, IFNULL(D.TASK_NUM, 0) AS TASK_NUM ');
			END	IF;
			
			SET	LV_SQL = CONCAT(LV_SQL , 'FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' , LN_OPT_ID , ' AND C.IPRJUPPERID = A.PRJ_ID');
			
			IF LI_MIN_RECID > 0 THEN
				SET LI_MIN_RECID = LI_MIN_RECID -1;
			END IF;
			
			SET	LV_SQL =CONCAT( LV_SQL , LV_WHERE , LV_ORDER_BY , ') AA ) BB limit ' , LI_MIN_RECID , ' , ' , LI_MAX_RECID);        SET @LV_SQL = LV_SQL;
			PREPARE	SQLA FROM @LV_SQL;
			EXECUTE SQLA;
			
			INSERT	INTO	TMP_WORKFLOW_QUERY
				(
					OPTID,
					FLOW_ID,
					FLOW_NAME,
					INSTANCE_NAME,
					PROJECT_NAME,
					STATUS,
					START_USER,
					START_TIME,
					END_TIME,
					PAID_TIME,
					HOST_NAME,
					TASK_NUM
				)
			SELECT	A.OPTID,
				A.FLOW_ID,
				B.IFLOWNAME,
				B.IFLOWINSNAME,
				B.IPROJECTNAME,
				concat(B.ISTATUS,''),
				B.ISTARTUSERFULLNAME,
				FUN_GET_DATE_STRING(B.ISTARTTIME, SI_TIMEZONE_CUR, '%Y-%m-%d %T'),
				(CASE WHEN B.IENDTIME > 0 THEN FUN_GET_DATE_STRING(B.IENDTIME, SI_TIMEZONE_CUR, '%Y-%m-%d %T') ELSE '' END),
				(
					CASE	WHEN	B.ISTATUS IN (0, 1, 6, 8, 10, 15, 30) THEN concat(floor(LN_MILLISEC - B.ISTARTTIME) ,'')
						WHEN	B.ISTATUS IN (2, 4, 7) THEN concat(floor(B.IENDTIME - B.ISTARTTIME),'')
						ELSE	'0'
					END
				),
				B.IHOSTNAME,
				A.TASK_NUM
			FROM	TMP_WORKFLOW_QUERY_TEMP A,
				IEAI_WORKFLOWINSTANCE B
			WHERE	A.OPTID = LN_OPT_ID
			AND	B.IFLOWID = A.FLOW_ID  limit 0,AI_NUM_OF_PAGE;
		END	IF;
	END;;
	DELIMITER ;
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_WORKFLOWINSTANCE' AND COLUMN_NAME = 'PRESTART') THEN
        ALTER TABLE IEAI_WORKFLOWINSTANCE ADD PRESTART SMALLINT default 0 ;
        END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IAUDITOR') THEN
        ALTER TABLE IEAI_PROJECT ADD IAUDITOR VARCHAR(255);
        END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IRELEASESTATE') THEN
        ALTER TABLE IEAI_PROJECT ADD IRELEASESTATE NUMERIC(19,0);
        END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'IBACKINFO') THEN
        ALTER TABLE IEAI_PROJECT ADD IBACKINFO VARCHAR(255);
        END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	CREATE TABLE IF NOT EXISTS IEAI_RUN_GROUP_ACTINFO (IFLOWID DECIMAL(19,0) NOT NULL,    IACTNAME VARCHAR(255),IPARAMTYPE VARCHAR(255),IPARAMNAME VARCHAR(255)  ,IPARAMVALUE VARCHAR(255), ITIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP );
	CREATE TABLE IF NOT EXISTS IEAI_PROJECT_ACT_INFOMATION (IID DECIMAL(19,0) NOT NULL, IPROJECTID DECIMAL(19,0) NOT NULL, IPROTYPE DECIMAL(19,0), IFLOWNAME VARCHAR(255), IPRONAME VARCHAR(255),IACTNAME VARCHAR(255),IPARAMTYPE VARCHAR(255),IPARAMNAME VARCHAR(255), IACTTYPE VARCHAR(255) ,IPARAMVALUE VARCHAR(255), ITIMESTAMP TIMESTAMP DEFAULT CURRENT_TIMESTAMP, CONSTRAINT PK_IEAI_PROJECT_ACT_INFOMATION PRIMARY KEY(IID));
	
	delete from IEAI_PROJECT_PROPERTY_TYPE_DC where IID in (6,7);
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(6,4,'TelnetConnection');
insert into IEAI_PROJECT_PROPERTY_TYPE_DC(IID,IGROUPID,ITYPENAME) values(7,4,'SshConnection');
delete from IEAI_PROJECT_PROPERTY_KEY_DC;
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(1,'funid');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(2,'isusecalendar');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(3,'flowcalendar');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(4,'inputparameter');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(5,'outputparameter');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(6,'vartype');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(7,'varvalue');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(8,'ftpserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(9,'ftpport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(10,'ftpuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(11,'ftppassword');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(12,'ftpovertime');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(13,'ftpencode');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(14,'isuseproxy');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(15,'ftpproxyserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(16,'ftpproxyport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(17,'ftpproxyuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(18,'ftpproxyowd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(19,'useagentgroup');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(20,'agentip');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(21,'agentport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(22,'connecttype');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(23,'agentgroup');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(24,'sshserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(25,'sshport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(26,'sshuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(27,'sshtimeout');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(28,'sshauthgroup');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(29,'sshkeyaddress');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(30,'sshkeypasswd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(31,'sshloginpasswd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(32,'sshdocprompt');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(33,'sshagenttype');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(34,'sshagentserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(35,'sshagentport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(36,'sshagentuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(37,'sshagentpasswd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(38,'telnetgridData');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(39,'telnetserver');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(40,'telnetport');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(41,'telnetuser');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(42,'telnettimeout');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(43,'telnetpwd');
INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME) VALUES(44,'telnetendSign');


	
	






