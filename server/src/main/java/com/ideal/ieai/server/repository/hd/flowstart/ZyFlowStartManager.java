package com.ideal.ieai.server.repository.hd.flowstart;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.commons.shellcmd.ShellCmdOutput;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.emergency.repository.doublereview.EmDoublePersonReviewZBeanForQuery;
import com.ideal.ieai.server.emergency.repository.doublereview.EmDoublePersonReviewZManager;
import com.ideal.ieai.server.emergency.repository.start.EmFlowstartinstManager;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.poc.DBUtilsNew;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.hd.ic.supperhc.hcstart.model.ComputerBean;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.warning.WorkflowInstance;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <ul>
 * <li>Title: ZyFlowStartManager.java</li>
 * <li>Description:中原银行任务启动持久层</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020年2月13日
 */
public class ZyFlowStartManager
{
    
    static Logger _log = Logger.getLogger(ZyFlowStartManager.class);
    //单例模式
    private ZyFlowStartManager(){}
    static private ZyFlowStartManager _intance = new ZyFlowStartManager();
    static public ZyFlowStartManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new ZyFlowStartManager();
        }
        return _intance;
    }
    /**
     * 
     * <li>Description:获取设备分组</li> 
     * <AUTHOR>
     * 2020年2月13日 
     * @return
     * @throws RepositoryException
     * return List<ComputerGroupModel>
     */
    public List<TreeModel> getComputerGroupList () throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        List<TreeModel> list = new ArrayList<TreeModel>(16);;
        String sql = "SELECT iid,igroupName,igroupDes,groupType FROM IEAI_COMPUTER_GROUP";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while(rs.next()) {
                TreeModel model = new TreeModel();
                Long iid = rs.getLong("iid");
                String igroupName = rs.getString("igroupName");
//                String igroupDes = rs.getString("igroupName");
//                Long groupType = rs.getLong("groupType");
                model.setId(iid);
                model.setText(igroupName);
                list.add(model);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getComputerGroupList", _log);
        }
        return list;
    }
    /**
     * 
     * <li>Description:根据设备组id获取绑定设备集合</li> 
     * <AUTHOR>
     * 2020年2月13日 
     * @param groupId
     * @return
     * @throws RepositoryException
     * return List<ComputerModel>
     */
    public List<TreeModel> getComputerList ( Long groupId ) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        List<TreeModel> list = new ArrayList<TreeModel>(16);;
        String sql = "SELECT cpId, ip, cpName\r\n" + 
                "  FROM IEAI_COMPUTER_GROUP_MAPPING m, IEAI_COMPUTER_LIST l\r\n" + 
                " WHERE m.icpid = l.cpid\r\n" + 
                "   AND igid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, groupId);
            rs = ps.executeQuery();
            while(rs.next()) {
                TreeModel model = new TreeModel();
                Long cpId = rs.getLong("cpId");
                String ip = rs.getString("ip");
//                String cpName = rs.getString("cpName");
//                model.setId(cpId);
                model.setCpid(cpId);
                model.setText(ip);
                model.setLeaf(true);
                model.setChecked(false);
//                model.setLoaded(true);
                list.add(model);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getComputerList", _log);
        }
        return list;
    }
    public Map<String, Object> insertFlowStartConfigBean ( ZyFlowStartModel model ) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        Map<String, Object> map = new HashMap<String, Object>(1);;
        String sql = "INSERT INTO IEAI_DM_MODEL\r\n" + 
                "  (iid, modeltype, modelname, itemname)\r\n" + 
                "VALUES\r\n" + 
                "  (?, ?, ?, ?)";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, IdGenerator.createId("IEAI_DM_MODEL", conn));
            ps.setLong(2, model.getModelType());
            ps.setString(3, model.getModelName());
            ps.setString(4, model.getItemName());
            ps.execute();
            conn.commit();
            map.put("message", "成功");
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, null, ps, "insertFlowStartConfigBean", _log);
        }
        return map;
    }
    public Map<String, Object> updateFlowStartConfigBean ( ZyFlowStartModel model ) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        Map<String, Object> map = new HashMap<String, Object>(1);;
        String sql = "UPDATE IEAI_DM_MODEL\r\n" + 
                "   SET modeltype = ?, modelname = ?, itemname = ?\r\n" + 
                " WHERE iid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, model.getModelType());
            ps.setString(2, model.getModelName());
            ps.setString(3, model.getItemName());
            ps.setLong(4, model.getIid());
            ps.executeUpdate();
            conn.commit();
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, null, ps, "updateFlowStartConfigBean", _log);
        }
        return map;
    }
    public Map<String, Object> getFlowStartConfigBeanPage ( ZyFlowStartModel model, Long start, Long limit ) throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sqlWhere = "";
        if(model!=null) {
//            if(model.getModelType()!= null && model.getModelType()!=0) {
//                sqlWhere += " and modeltype = "  + model.getModelType();
//            }
//            if(StringUtils.isNoneEmpty(model.getModelName())) {
//                sqlWhere += " and modelname like " + "'%" + model.getModelName().trim() + "%' ";
//            }
//            if(StringUtils.isNoneEmpty(model.getItemName())) {
//                sqlWhere += " and itemname like " + "'%" + model.getItemName().trim() + "%' ";
//            }

            if(model.getModelType()!= null && model.getModelType()!=0) {
                sqlWhere += " and modeltype = ?";
            }
            if(StringUtils.isNoneEmpty(model.getModelName())) {
                sqlWhere += " and modelname like ?";
            }
            if(StringUtils.isNoneEmpty(model.getItemName())) {
                sqlWhere += " and itemname like ?";
            }
        }
        String orderBy = " ORDER BY iid DESC";
        String sql = "select *\r\n" + 
                "  from (select ROW_NUMBER() OVER(" + orderBy + ") AS RN,\r\n" + 
                "               iid,\r\n" + 
                "               modeltype,\r\n" + 
                "               modelname,\r\n" + 
                "               itemname\r\n" + 
                "          FROM IEAI_DM_MODEL\r\n" + 
                "         WHERE 1 = 1 " + sqlWhere + ")\r\n" + 
                " WHERE RN <= ?\r\n" + 
                "   AND RN > ?";
        if (JudgeDB.IEAI_DB_TYPE == 3) {
            sql = "select *\r\n" + 
                    "  from (select iid,\r\n" + 
                    "               modeltype,\r\n" + 
                    "               modelname,\r\n" + 
                    "               itemname\r\n" +
                    "          FROM IEAI_DM_MODEL t\r\n" + 
                    "         WHERE 1 = 1 "+sqlWhere+") as A\r\n" + orderBy+
                    "  limit ?, ?";
        }
        String sqlCount = "select count(*) from IEAI_DM_MODEL t WHERE 1=1 "
                + sqlWhere;
        List<ZyFlowStartModel> list = new ArrayList<ZyFlowStartModel>();
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            int index = 0;
            if(model!=null) {
                if(model.getModelType()!= null && model.getModelType()!=0) {
                    ps.setLong(++index, model.getModelType());
                }
                if(StringUtils.isNoneEmpty(model.getModelName())) {
                    ps.setString(++index, "%" + model.getModelName() + "%");
                }
                if(StringUtils.isNoneEmpty(model.getItemName())) {
                    ps.setString(++index, "%" + model.getItemName() + "%");
                }
            }
            if (JudgeDB.IEAI_DB_TYPE == 3) {
                ps.setLong(++index, start);
                ps.setLong(++index, limit);
            } else {
                ps.setLong(++index, start + limit);
                ps.setLong(++index, start);
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                ZyFlowStartModel bean = new ZyFlowStartModel();
                bean.setIid(rs.getLong("iid"));
                bean.setModelType(rs.getLong("modeltype"));
                bean.setModelName(rs.getString("modelname"));
                bean.setItemName(rs.getString("itemname"));
                list.add(bean);
            }
            int total = 0;
            ps = conn.prepareStatement(sqlCount);
            index = 0;
            if(model!=null) {
                if(model.getModelType()!= null && model.getModelType()!=0) {
                    ps.setLong(++index, model.getModelType());
                }
                if(StringUtils.isNoneEmpty(model.getModelName())) {
                    ps.setString(++index, "%" + model.getModelName() + "%");
                }
                if(StringUtils.isNoneEmpty(model.getItemName())) {
                    ps.setString(++index, "%" + model.getItemName() + "%");
                }
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                total = rs.getInt(1);
            }
            res.put("dataList", list);
            res.put("total", total);
        } catch (Exception e) {
            res.put("messsage", "查询失败!!!");
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getFlowStartConfigBeanPage", _log);
        }
        return res;
    }
    public Map<String, Object> getFlowStartConfigBean ( ZyFlowStartModel model ) throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String sqlWhere = "";
        String orderBy = " ORDER BY iid DESC";
        if(model!=null) {
//            if(model.getModelType()!= null && model.getModelType()!=0) {
//                sqlWhere += " and modeltype = "  + model.getModelType();
//            }
//            if(StringUtils.isNoneEmpty(model.getModelName())) {
//                sqlWhere += " and modelname like " + "'%" + model.getModelName().trim() + "%' ";
//            }
//            if(StringUtils.isNoneEmpty(model.getItemName())) {
//                sqlWhere += " and itemname like " + "'%" + model.getItemName().trim() + "%' ";
//            }

            if(model.getModelType()!= null && model.getModelType()!=0) {
                sqlWhere += " and modeltype = ?";
            }
            if(StringUtils.isNoneEmpty(model.getModelName())) {
                sqlWhere += " and modelname like ?";
            }
            if(StringUtils.isNoneEmpty(model.getItemName())) {
                sqlWhere += " and itemname like ?";
            }
        }
        String sql = "SELECT iid, modeltype, modelname, itemname FROM IEAI_DM_MODEL WHERE 1 = 1 "+sqlWhere+orderBy;
        List<ZyFlowStartModel> list = new ArrayList<ZyFlowStartModel>();
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            if(model!=null) {
                int index = 0;
                if(model.getModelType()!= null && model.getModelType()!=0) {
                    ps.setLong(++index, model.getModelType());
                }
                if(StringUtils.isNoneEmpty(model.getModelName())) {
                    ps.setString(++index, "%" + model.getModelName() + "%");
                }
                if(StringUtils.isNoneEmpty(model.getItemName())) {
                    ps.setString(++index, "%" + model.getItemName() + "%");
                }
            }
            rs = ps.executeQuery();
            while (rs.next()) {
                ZyFlowStartModel bean = new ZyFlowStartModel();
                bean.setIid(rs.getLong("iid"));
                bean.setModelType(rs.getLong("modeltype"));
                bean.setModelName(rs.getString("modelname"));
                bean.setItemName(rs.getString("itemname"));
                list.add(bean);
            }
            res.put("dataList", list);
        } catch (Exception e) {
            res.put("messsage", "查询失败!!!");
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getFlowStartConfigBean", _log);
        }
        return res;
    }
    
    public Map<String, Object> getCheckList () throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        String orderBy = " ORDER BY iid DESC";
        String sql = "SELECT distinct itemname FROM IEAI_DM_MODEL ";
        List<ZyFlowStartModel> list = new ArrayList<ZyFlowStartModel>();
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                ZyFlowStartModel bean = new ZyFlowStartModel();
                bean.setItemName(rs.getString("itemname"));
                list.add(bean);
            }
            res.put("dataList", list);
        } catch (Exception e) {
            res.put("messsage", "查询检查项失败!!!");
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getCheckList", _log);
        }
        return res;
    }
    
    public Map<String, Object> saveModelParamList ( List<ParamModel> paramList, Long id ) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        Map<String, Object> map = new HashMap<String, Object>(1);
        String sql = "insert into IEAI_MODEL_PARAM\r\n" + 
                "values\r\n" + 
                "  (?,?,?,?,?,?)";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            for(ParamModel model:paramList) {
                ps.setLong(1, IdGenerator.createId("IEAI_MODEL_PARAM", conn));
                ps.setString(2, model.getIparamName());
                ps.setString(3, model.getIparamType());
                ps.setString(4, model.getIparamValue());
                ps.setString(5, model.getIparamDesc());
                ps.setLong(6, id);
                ps.addBatch();
                
            }
            ps.executeBatch();
            conn.commit();
            map.put("message", "成功");
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, null, ps, "saveModelParamList", _log);
        }
        return map;
    }
    public Map<String, Object> updateModelParamList ( List<ParamModel> paramList, Long id ) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        Map<String, Object> map = new HashMap<String, Object>(1);
        String sql = "UPDATE IEAI_MODEL_PARAM\r\n" + 
                "     SET ivalue = ? \r\n" + 
                "   WHERE iname = ? AND ireviewid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            for(ParamModel model:paramList) {
                ps.setString(1, model.getIparamValue());
                ps.setString(2, model.getIparamName());
                ps.setLong(3, id);
                ps.addBatch();
            }
            ps.executeBatch();
            conn.commit();
            map.put("message", "修改成功!!");
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, null, ps, "updateModelParamList", _log);
        }
        return map;
    }
    
    public Map<String, Object> getModelHistoryParamList ( Long id ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>(2);;
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        List<ParamModel> list = new ArrayList<ParamModel>(16);;
        String sql = " SELECT iid, iname, itype, ivalue, ides, ireviewid\r\n" + 
                "   FROM IEAI_MODEL_PARAM\r\n" + 
                "  WHERE ireviewid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, id);
            rs = ps.executeQuery();
            while(rs.next()) {
                ParamModel model = new ParamModel();
                model.setIid(rs.getLong("iid"));
                model.setIparamName(rs.getString("iname"));
                model.setIparamType(rs.getString("itype"));
                model.setIparamValue(rs.getString("ivalue"));
                model.setIparamDesc(rs.getString("ides"));
                list.add(model);
            }
            if(list.isEmpty()) {
                map.put("success", "true");
            }else {
                map.put("dataList",list);
            }
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getModelHistoryParamList", _log);
        }
        return map;
    }
    public Map<String, Object> deleteModelParam ( List<String> idList ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>(2);;
        PreparedStatement ps = null;
        Connection conn = null;
        String sql = "DELETE FROM IEAI_DOUBLECHECK_IC_PARAM\r\n" + 
                " WHERE ireviewid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            for(String idStr :idList) {
                Long id = Long.valueOf(idStr);
                ps.setLong(1, id);
                ps.addBatch();
            }
            ps.executeBatch();
            conn.commit();
            map.put("success", true);
            map.put("message", "成功!");
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, null, ps, "deleteModelParam", _log);
        }
        return map;
    }
    public Map<String, Object> deleteModel ( List<String> idList ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>(2);;
        PreparedStatement ps = null;
        Connection conn = null;
        String sql = " DELETE FROM IEAI_DM_MODEL\r\n" + 
                " WHERE iid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            for(String idStr :idList) {
                Long id = Long.valueOf(idStr);
                ps.setLong(1, id);
                ps.addBatch();
            }
            ps.executeBatch();
            conn.commit();
            map.put("success", true);
            map.put("message", "成功!");
            
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, null, ps, "deleteModel", _log);
        }
        return map;
    }
    
    public Map<String, Object> startFlow(String startUserName,String usergroupId,String itemname,Long modelType,String idStr) throws RepositoryException{
        Map<String, Object> map = new HashMap<String, Object>(2);
        List<IcParamInfoBean> paramList = new ArrayList<IcParamInfoBean>();
        ZyFlowStartModel model = new ZyFlowStartModel();
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        //无单号
        String butterflyVerion = "";
        //  获取检查项id   
        String hcitemId = "";
        //默认执行人
        String exector = startUserName;
        //判断任务名 是否重复    自动生成唯一任务名不存在重复  略过此步
        String instName = "中原任务启动"+System.currentTimeMillis();
        String sql = "SELECT iid, modeltype, modelname, itemname\r\n" + 
                "  FROM IEAI_DM_MODEL\r\n" + 
                " WHERE itemname = ?\r\n" + 
                "   AND modeltype = ?";
        String paramSql = "SELECT iid, iname, itype, ivalue, ides, ireviewid\r\n" + 
                "  FROM IEAI_MODEL_PARAM\r\n" + 
                " WHERE ireviewid = ?";
        String modelIdSql = "SELECT IID, INAME, IFROM, IUPPERID\r\n" + 
                "  FROM IEAI_PROJECT\r\n" + 
                " WHERE PROTYPE = 8\r\n" + 
                "   AND IPKGCONTENTID <> 0\r\n" + 
                "   AND DAILYTYPE = 1\r\n" + 
                "   AND ILATESTID = IID AND INAME=?";
        String hcitemIdSql ="select iid from IEAI_HC_CHECKINFO WHERE ihcname = ?";
        String workitemIdSql = "select iworkitemid\r\n" + 
                "  from IEAI_DOUBLECHECK_COLVALUE\r\n" + 
                " WHERE icolheader = 'iinsName'\r\n" + 
                "   AND icolvalue = ?";
        String updatState = "UPDATE IEAI_DOUBLECHECK_WORKITEM SET istate = 7 WHERE iid = ?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, itemname);
            ps.setLong(2, modelType);
            rs = ps.executeQuery();
            while(rs.next()) {
                model.setIid(rs.getLong("iid"));
                model.setModelType(rs.getLong("modeltype"));
                model.setModelName(rs.getString("modelname"));
                model.setItemName(rs.getString("itemname"));
            }
            if(model.getIid()==null) {
                map.put("success", false);
                if(modelType==1) {
                    map.put("message","检查项  :"+itemname+" 启动检查模板不存在!");
                }else if(modelType==2) {
                    map.put("message","检查项  :"+itemname+" 启动服务模板不存在!");
                }else {
                    map.put("message","检查项  :"+itemname+" 停止服务模板不存在!"); 
                }
                return map;
            }
            ps = conn.prepareStatement(hcitemIdSql);
            ps.setString(1, itemname);
            rs = ps.executeQuery();
            while(rs.next()) {
                hcitemId = rs.getLong("iid")+"";
            }
            IcParamInfoBean bean = new IcParamInfoBean();
            bean.setIparamName("checkconfig");
            bean.setIparamType("String");
            bean.setIparamValue(hcitemId);
            bean.setIparamDes("检查规则配置ID");
            paramList.add(bean);
            ps = conn.prepareStatement(paramSql);
            ps.setString(1, model.getIid()+"");
            rs = ps.executeQuery();
            while(rs.next()) {
                bean = new IcParamInfoBean();
                bean.setIparamName(rs.getString("iname"));
                bean.setIparamType(rs.getString("itype"));
                bean.setIparamValue(rs.getString("ivalue"));
                bean.setIparamDes(rs.getString("ides"));
                paramList.add(bean);
            }  
            List resultList = IcFlowstartService.getInstance().getPrjXmlParam("2222", model.getModelName());
            if(!resultList.isEmpty()) {
                int paramCount = resultList.size();
                if(paramCount==(paramList.size()-1)) {
                    for(IcParamInfoBean paramBean : paramList ) {
                        if(paramBean.getIparamValue().isEmpty()) {
                            map.put("message", "请设置模板["+paramBean.getIparamName()+"]参数!");
                            map.put("success",false);
                            return map;
                        }
                    }
                }else {
                    map.put("success",false);
                    map.put("message", "请设置模板参数!");
                    return map;
                }
                
            }
            ps = conn.prepareStatement(modelIdSql);
            ps.setString(1, model.getModelName());
            rs = ps.executeQuery();
            Long isystemId = -1L;
            while(rs.next()) {
                isystemId = rs.getLong("iid");
            }
            String iflowName = "healthMain";
            int sysType = Constants.IEAI_DAILY_OPERATIONS;
            // 获取Ip信息
            List<IcIpInfoBean> ipList = IcFlowstartService.getInstance().getComputerListByIdStr(idStr, sysType);
            // 提交任务
            IcFlowstartService.getInstance().saveWorkItem(isystemId, model.getModelName(), instName, iflowName, "中原任务启动",
                exector, exector, exector, 1,
                ipList, paramList, butterflyVerion, sysType,"0" );
            ps = conn.prepareStatement(workitemIdSql);
            ps.setString(1, instName);
            rs = ps.executeQuery();
            Long workitemId = 0L ;
            while(rs.next()) {
                workitemId = rs.getLong("iworkitemid");
            }
            ps = conn.prepareStatement(updatState);
            ps.setLong(1, workitemId);
            rs = ps.executeQuery();
            conn.commit();
            map = startFlowHandler(startUserName,usergroupId,model.getModelName(),iflowName,instName,workitemId,butterflyVerion);
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "startFlow", _log);
        }
        
        return map;
    }
    private Map<String, Object> startFlowHandler (String startUserName, String usergroupId,String isysName, String iflowName, String iinsName, Long workItemid,
            String butterflyversion )
    {
        Map<String, Object> map = new HashMap<String, Object>(2);
        try
        {
            // begin.
            // 这里是启动任务方法...
            UserInfo userInfo = new UserInfo();
            userInfo.setFullName(startUserName);
            userInfo.setId(Long.valueOf(usergroupId));
            HashMap evn = new HashMap();
            evn.put("butterflyversion",butterflyversion);
            List args = EngineRepositotyJdbc.getInstance().getMainFlowParamInfo(workItemid, Constants.IEAI_DAILY_OPERATIONS);
            // 第八个参数双人复核 iworkItemid 任务查询用
            long flowId = Engine.getInstance().startFlow(userInfo, isysName, iflowName, args, evn, null, iinsName,
                workItemid+"",null, false, null, null,
                Constants.IEAI_DAILY_OPERATIONS, Constants.IEAI_DAILY_OPERATIONS,false);
            
            int sysType = Constants.IEAI_DAILY_OPERATIONS;
            String icheckId = IcFlowstartService.getInstance().getHcitemIdForReview(workItemid,sysType);
            IcFlowstartService.getInstance().insertFlowResouce(flowId, isysName, iflowName, Long.parseLong(icheckId), Constants.IEAI_DAILY_OPERATIONS, sysType);
            
            if (/*Constants.MAINPRJNAME.equals(isysName) && */Constants.MAINFLOWNAME.equals(iflowName))
            {
                submitInsgdbForICFun(flowId, workItemid);
            }

            if (null != workItemid && workItemid.longValue() != 0)
            {
                // 切换执行后转移双人复核数据至历史表

                Connection conn = DBResource.getConnection("submitInsgdbForIC", _log,
                    Constants.IEAI_DAILY_OPERATIONS);
                EmDoublePersonReviewZBeanForQuery doublePersonReviewZBeanForQuery = new EmDoublePersonReviewZBeanForQuery();
                doublePersonReviewZBeanForQuery.setIstateForQuery(5);
                doublePersonReviewZBeanForQuery.setIidForQuery(workItemid);
                EmDoublePersonReviewZManager.getInstance().endWorkitemByiidForDIS(conn, doublePersonReviewZBeanForQuery);
                // 迁移双人复核任务相关表数据1
                EmFlowstartinstManager.getInstance().removeWorkItem(workItemid, conn);
                // 清理 双人复核 IP 表和参数表
                EmFlowstartinstManager.getInstance().removeWorkItemIPAndPrarm(workItemid, conn,"");
                conn.commit();
                DBResource.closeConnection(conn, "submitInsgdbForIC", _log);
            }

            map.put("message", true);
            map.put("message", "启动成功,工作流ID :  " + flowId);
            map.put("flowId", flowId);
            map.put("success", true);
            // end.
        } catch (Exception e)
        {
            map.put("message", false);
            map.put("message", "启动失败");
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            _log.error(isysName + ",启动失败,异常信息为:" + e.getMessage());
        }

        return map;
    }
    
    private void submitInsgdbForICFun ( long flowId, long workItemid) 
    {
        try
        {
            EngineRepositotyJdbc.getInstance().saveMainFlowAgentinfo(flowId, workItemid,
                Constants.IEAI_DAILY_OPERATIONS);
        } catch (RepositoryException e)
        {
            _log.error("保存Agent执行信息失败  :"+e,e);
        }
    }
    public List<Long> getAllStartFlowIdList ( Long flowId ) throws RepositoryException
    {
        List<Long> list = new ArrayList<Long>(16);
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        String sql = " SELECT t.icallflowid FROM ieai_callworkflow_info t \r\n" + 
                "WHERE t.imainflowid= ? \r\n" + 
                "ORDER BY iid desc\r\n" ;
        String agentIpSize = "SELECT count(1) FROM IEAI_CALL_AGENINFO i WHERE i.imainflowid = ?";
        Long ipSize = -1L;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(agentIpSize);
            ps.setLong(1, flowId-1);
            rs = ps.executeQuery();
            while(rs.next()) {
                ipSize = rs.getLong(1);
            }
            for(int i=0;i<100;i++) {
                ps = conn.prepareStatement(sql);
                ps.setLong(1, flowId);
                rs = ps.executeQuery();
                while(rs.next()) {
                    Long id = rs.getLong("icallflowid");
                    list.add(id);
                }
                if(list.size()==ipSize) {
                    break;
                }
                Thread.sleep(1000);
            }
            
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getAllStartFlowIdList", _log);
        }
        return list;
    }
    public Map<Long, String> getAgentIpList ( List<Long> flowIdList ) throws RepositoryException
    {
        Map<Long, String> map = new HashMap<Long, String>(16);
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        String sql = " SELECT iflowinsname From IEAI_WORKFLOWINSTANCE WHERE iflowid = ? " ;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            for(Long flowId : flowIdList) {
                ps = conn.prepareStatement(sql);
                ps.setLong(1, flowId);
                rs = ps.executeQuery();
                while(rs.next()) {
                    String ip = rs.getString("iflowinsname");
                    map.put(flowId, ip);
//                    System.out.println("flowid :"+flowId+" IP :"+ip);
                }
            }
            
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getAgentIpList", _log);
        }
        return map;
    }
    public Map<String, Object> getZyStartFlowResult ( List<Long> flowIdList, Map<Long, String> ipMap ,ZyStartFlowResult model) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>(2);
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        String ids = flowIdList.toString();
        ids = ids.substring(1, ids.length()-1);
//        String sql = " SELECT istdout,iflowid FROM Ieai_Shellcmd_Output WHERE iflowid in (?) " ;
        String sql = "select *\r\n" + 
                "  from (select " +
                // "ROW_NUMBER() OVER( order by iflowid ) AS RN,\r\n" +
                // " istdout,\r\n" +
                "               iflowid\r\n" + 
                "          FROM Ieai_Shellcmd_Output\r\n" + 
                "         WHERE iflowid in (" + ids + ") )\r\n";
        // " WHERE RN <= ?\r\n" +
        // " AND RN > ?";
        if (JudgeDB.IEAI_DB_TYPE == 3) {
            sql = "select *\r\n" + 
                    "  from (SELECT istdout,iflowid FROM Ieai_Shellcmd_Output WHERE iflowid in (?) )\r\n" +
                    " ) aa ";
            // + "limit ?, ?";
        }
        StringBuilder builder = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            builder = new StringBuilder(200);
            ps = conn.prepareStatement(sql);
            /*
             * Long startLine = model.getStartLine();
             * Long pageSize = model.getPageSize();
             * String type = model.getType();
             * if("2".equals(type)){
             * startLine = startLine - pageSize;
             * }else if("3".equals(type)){
             * startLine = startLine + pageSize;
             * }
             * if (JudgeDB.IEAI_DB_TYPE == 3) {
             * ps.setLong(1, startLine);
             * ps.setLong(2, pageSize);
             * } else {
             * ps.setLong(1, startLine + pageSize);
             * ps.setLong(2, startLine);
             * }
             */
            rs = ps.executeQuery();
            while (rs.next())
            {
                // Clob out = rs.getClob("istdout");
                // String reuslt = out.getSubString(1, (int) out.length());
                Long flowId = rs.getLong("iflowid");
                // builder.append(ipMap.get(flowId));
                // builder.append(" : ");
                // builder.append(reuslt);
                // builder.append("\n");
                flowIdList.remove(flowId);
            }
            map.put("flowIdList", flowIdList);
            map.put("success", true);
            map.put("result", builder.toString());
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getZyStartFlowResult", _log);
        }
        return map;
    }
    public Map<String, Object> getModelNameList (Long modelType) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        Map<String, Object> map = new HashMap<String, Object>(1);;
        String sql = "SELECT distinct modelname FROM IEAI_DM_MODEL\r\n" ;
        if(modelType != null && modelType != 0) {
            sql += "WHERE modeltype = "+ modelType;
        }
        List<Map<String,String>> list = new ArrayList<Map<String,String>>(16);
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while(rs.next()) {
                String modelName = rs.getString("modelname");
                Map<String,String> modelNameMap = new HashMap<String,String>(1);
                modelNameMap.put("modelName",modelName);
                list.add(modelNameMap);
            }
            map.put("dataList",list);
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getModelNameList", _log);
        }
        return map;
    }
    public Map<String, Object> getitemNameList ( Long modelType, String modelName ) throws RepositoryException
    {
        PreparedStatement ps = null;
        Connection conn = null;
        ResultSet rs = null;
        Map<String, Object> map = new HashMap<String, Object>(1);
        String sql = "SELECT distinct itemname FROM IEAI_DM_MODEL WHERE modelname = ?\r\n" ;
        if(modelType != null && modelType != 0) {
            sql += "AND modeltype = "+ modelType;
        }
        List<Map<String,String>> list = new ArrayList<Map<String,String>>(16);
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DAILY_OPERATIONS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, modelName);
            rs = ps.executeQuery();
            while(rs.next()) {
                String itemName = rs.getString("itemname");
                Map<String,String> itemNameMap = new HashMap<String,String>(1);
                itemNameMap.put("itemName",itemName);
                list.add(itemNameMap);
            }
            map.put("dataList",list);
        } catch (Exception e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : " + e.getMessage(),e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally {
            DBResource.closeConn(conn, rs, ps, "getitemNameList", _log);
        }
        return map;
    }
  
    public List<WorkflowInstance> getStatusList ( List<Long> flowIdList, String status )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        StringBuilder sb = new StringBuilder("");
        DBUtilsNew dBUtilsNew = new DBUtilsNew(Constants.IEAI_DAILY_OPERATIONS);
        List<WorkflowInstance> list = new ArrayList<WorkflowInstance>();
        List<WorkflowInstance> listN = new ArrayList<WorkflowInstance>();
        String queryWhere = "";
        if (null != status && !"".equals(status) && !"全部".equals(status))
        {
            queryWhere = " WHERE STATUS  = " + status;
        }

        for (Long flowId : flowIdList)
        {
            sb.append(flowId).append(",");
        }
        if (sb.length() != 0)
        {
            String sql1 = " SELECT FLOWID,FLOWINSNAME, WM_CONCAT(STATUS) STATUSSTR FROM ";

            if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.MYSQL))
            {
                sql1 = " SELECT FLOWID,FLOWINSNAME, GROUP_CONCAT(STATUS) STATUSSTR FROM ";
            }
            if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.DB2))
            {
                sql1 = " SELECT FLOWID,FLOWINSNAME, LISTAGG(STATUS) STATUSSTR FROM ";
            }
            String sql = sql1 + " ( SELECT DISTINCT A.IFLOWID FLOWID , A.IFLOWINSNAME FLOWINSNAME , "
                    + " (CASE WHEN  T.ISTATE='Running' THEN '运行' WHEN T.ISTATE='Finished' THEN '完成' ELSE '异常' END) STATUS "
                    + " FROM IEAI_WORKFLOWINSTANCE A JOIN IEAI_ACTRUNTIME T ON  T.IFLOWID=A.IFLOWID   WHERE T.ISTATE<> 'Null' AND A.IFLOWID IN ( "
                    + sb.toString().substring(0, sb.toString().length() - 1) + " ))  X  " + queryWhere
                    + " GROUP BY  FLOWID,FLOWINSNAME  ORDER BY FLOWID DESC ";
            list = dBUtilsNew.list(sql, new WorkflowInstance());
            WorkflowInstance ins = new WorkflowInstance();
            for (int i = 0; i < list.size(); i++)
            {
                ins = list.get(i);
                String statusStr = ins.getStatusStr();
                ins.setStatusStr(changeState(statusStr));
                listN.add(ins);
            }
        }
        return listN;
    }

    private String changeState ( String statusStr )
    {
        String state = "";
        if (statusStr.contains("异常"))
        {
            state = "异常";
        } else if (statusStr.contains("运行"))
        {
            state = "运行";
        } else if (statusStr.contains("完成"))
        {
            state = "完成";
        }
        return state;
    }

    public Long getStatusListCount ( List<Long> flowIdList, String status )
    {
        StringBuilder sb = new StringBuilder("");
        long count = 0;
        DBUtilsNew dBUtilsNew = new DBUtilsNew(Constants.IEAI_DAILY_OPERATIONS);
        String queryWhere = "";
        if (null != status && !"".equals(status) && !"全部".equals(status))
        {
            queryWhere = " WHERE STATUS = " + status;
        }
        for (Long flowId : flowIdList)
        {
            sb.append(flowId).append(",");
        }
        if (sb.length() > 0)
        {
            String sql1 = " SELECT FLOWID,FLOWINSNAME, WM_CONCAT(STATUS) STATUSSTR FROM ";

            if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.MYSQL))
            {
                sql1 = " SELECT FLOWID,FLOWINSNAME, GROUP_CONCAT(STATUS) STATUSSTR FROM ";
            }
            if (JudgeDB.IEAI_DB_TYPE.equals(JudgeDB.DB2))
            {
                sql1 = " SELECT FLOWID,FLOWINSNAME, LISTAGG(STATUS) STATUSSTR FROM ";
            }
            String sql = sql1 + " ( SELECT DISTINCT A.IFLOWID FLOWID , A.IFLOWINSNAME FLOWINSNAME , "
                    + " (CASE WHEN  T.ISTATE='Running' THEN '运行' WHEN T.ISTATE='Finished' THEN '完成' ELSE '异常' END) STATUS "
                    + " FROM IEAI_WORKFLOWINSTANCE A JOIN IEAI_ACTRUNTIME T ON  T.IFLOWID=A.IFLOWID   WHERE T.ISTATE<> 'Null' AND A.IFLOWID IN ( "
                    + sb.toString().substring(0, sb.toString().length() - 1)
                    + " ) )  X " + queryWhere + " GROUP BY  FLOWID,FLOWINSNAME  ORDER BY FLOWID DESC ";
            count = dBUtilsNew.count(sql);
        }
        return count;

    }
    public String getZyStartFlowResultByIp ( List<Long> flowIdList, ZyStartFlowResult model )
    {
        ShellCmdOutput sco = new ShellCmdOutput();
        DBUtilsNew dBUtilsNew = new DBUtilsNew(Constants.IEAI_DAILY_OPERATIONS);
        String ids = flowIdList.toString();
        ids = ids.substring(1, ids.length() - 1);
        String ip = model.getIp();
        Object[] parms = { ip };
        String sql = "SELECT T.ISTDOUT STDOUTLOG FROM IEAI_WORKFLOWINSTANCE A, IEAI_SHELLCMD_OUTPUT T "
                + " WHERE A.IFLOWID = T.IFLOWID AND A.IFLOWINSNAME = ?  AND T.IFLOWID IN ( " + ids + ")";
        List<ShellCmdOutput> list = dBUtilsNew.list(sql, parms, new ShellCmdOutput());
        StringBuilder builder = new StringBuilder("");

        for(int i = 0;i<list.size();i++){
            sco = list.get(i);
            String log = sco.getStdOutLog();
            builder.append(ip);
            builder.append(" : ");
            builder.append(log);
            builder.append("\n");
        }
        return builder.toString();
    }

    public String getZyStartFlowResultByIpN ( ZyStartFlowResult model )
    {
        ShellCmdOutput sco = new ShellCmdOutput();
        DBUtilsNew dBUtilsNew = new DBUtilsNew(Constants.IEAI_DAILY_OPERATIONS);
        String ip = model.getIp();
        Long flowId = model.getFlowId();
        Object[] parms = { ip, flowId };
        String sql = "SELECT T.ISTDOUT STDOUTLOG FROM IEAI_WORKFLOWINSTANCE A, IEAI_SHELLCMD_OUTPUT T "
                + " WHERE A.IFLOWID = T.IFLOWID AND A.IFLOWINSNAME = ?  AND T.IFLOWID = ?  ";
        List<ShellCmdOutput> list = dBUtilsNew.list(sql, parms, new ShellCmdOutput());
        StringBuilder builder = new StringBuilder("");

        for (int i = 0; i < list.size(); i++)
        {
            sco = list.get(i);
            String log = sco.getStdOutLog();
            builder.append(ip);
            builder.append(" : ");
            builder.append(log);
            builder.append("\n");
        }
        return builder.toString();
    }
    
    public List<computerGroupModel> getComputerList (int type){
        DBUtilsNew db = new DBUtilsNew(type);
        String sql = "SELECT IID,IGROUPNAME,IGROUPDES,GROUPTYPE FROM IEAI_COMPUTER_GROUP";
        return db.list(sql, new computerGroupModel());
    }
    
    public List<ComputerBean> getComputByGid (int type,long groupId){
        String sql = "SELECT CPID,IP, CPNAME,M.IGID CPGROUPID FROM IEAI_COMPUTER_GROUP_MAPPING M, IEAI_COMPUTER_LIST L WHERE M.ICPID = L.CPID  AND IGID = ?";
        Object[] obj = {groupId};
        DBUtilsNew db = new DBUtilsNew(type);
        return db.list(sql,obj, new ComputerBean());
    }
    
    public Long getComputByGidCount (int type,long groupId){
        String sql = "SELECT COUNT(*) FROM IEAI_COMPUTER_GROUP_MAPPING M, IEAI_COMPUTER_LIST L WHERE M.ICPID = L.CPID  AND IGID = ?";
        Object[] obj = {groupId};
        DBUtilsNew db = new DBUtilsNew(type);
        return db.count(sql, obj);
    }
}
