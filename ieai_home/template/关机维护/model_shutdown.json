[{"itypeid": 28, "iremarks": "", "itypename": "IEAI_SHUTDOWN", "iswhiteandip": "", "dailytype": 2, "iid": 65, "isystemtypeuuid": "", "iprojectname": "model_shutdown", "icreateusrid": 0, "iversion": "1", "propertyslist": {"envvar": [], "workflow": [{"itypeid": 3, "iprojectid": 65, "itypename": "WorkFlow", "iid": 77, "valuelistmap": {"flowcalendar": {"ipropertyid": 77, "ipropertykeyname": "flowcalendar", "igoupid": 0, "iid": 263, "ipropertykeyid": 3}, "isusecalendar": {"ipropertyid": 77, "ipropertykeyname": "isusecalendar", "igoupid": 0, "ipropertyvalue": "false", "iid": 262, "ipropertykeyid": 2}, "inputparameter": {"ipropertyid": 77, "ipropertykeyname": "inputparameter", "igoupid": 0, "ipropertyvalue": "[{\"id\":\"\",\"name\":\"DBType\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"数据库类型\",\"parameter\":\"数据库类型\"},{\"id\":\"\",\"name\":\"OS\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"操作系统类型\",\"parameter\":\"操作系统类型\"},{\"id\":\"\",\"name\":\"maintainType\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"维护类（默认输入参数）\",\"parameter\":\"维护类（默认输入参数）\"},{\"id\":\"\",\"name\":\"maintainInfo\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"维护标识(默认输入参数）\",\"parameter\":\"维护标识(默认输入参数）\"},{\"id\":\"\",\"name\":\"agentip\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"agent信息\",\"parameter\":\"agent信息\"},{\"id\":\"\",\"name\":\"primaryName\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"主库名称\",\"parameter\":\"主库名称\"},{\"id\":\"\",\"name\":\"prSid\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"主库SID\",\"parameter\":\"主库SID\"},{\"id\":\"\",\"name\":\"primaryIp\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"主库IP\",\"parameter\":\"主库IP\"},{\"id\":\"\",\"name\":\"backName\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"备库名称\",\"parameter\":\"备库名称\"},{\"id\":\"\",\"name\":\"baSid\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"备库SID\",\"parameter\":\"备库SID\"},{\"id\":\"\",\"name\":\"backIp\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"备库IP\",\"parameter\":\"备库IP\"},{\"id\":\"\",\"name\":\"relation\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"切换前关系\",\"parameter\":\"切换前关系\"}]", "iid": 264, "ipropertykeyid": 4}, "outputparameter": {"ipropertyid": 77, "ipropertykeyname": "outputparameter", "igoupid": 0, "ipropertyvalue": "[]", "iid": 265, "ipropertykeyid": 5}}, "valuelist": [{"ipropertyid": 77, "ipropertykeyname": "isusecalendar", "igoupid": 0, "ipropertyvalue": "false", "iid": 262, "ipropertykeyid": 2}, {"ipropertyid": 77, "ipropertykeyname": "flowcalendar", "igoupid": 0, "iid": 263, "ipropertykeyid": 3}, {"ipropertyid": 77, "ipropertykeyname": "inputparameter", "igoupid": 0, "ipropertyvalue": "[{\"id\":\"\",\"name\":\"DBType\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"数据库类型\",\"parameter\":\"数据库类型\"},{\"id\":\"\",\"name\":\"OS\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"操作系统类型\",\"parameter\":\"操作系统类型\"},{\"id\":\"\",\"name\":\"maintainType\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"维护类（默认输入参数）\",\"parameter\":\"维护类（默认输入参数）\"},{\"id\":\"\",\"name\":\"maintainInfo\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"维护标识(默认输入参数）\",\"parameter\":\"维护标识(默认输入参数）\"},{\"id\":\"\",\"name\":\"agentip\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"agent信息\",\"parameter\":\"agent信息\"},{\"id\":\"\",\"name\":\"primaryName\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"主库名称\",\"parameter\":\"主库名称\"},{\"id\":\"\",\"name\":\"prSid\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"主库SID\",\"parameter\":\"主库SID\"},{\"id\":\"\",\"name\":\"primaryIp\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"主库IP\",\"parameter\":\"主库IP\"},{\"id\":\"\",\"name\":\"backName\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"备库名称\",\"parameter\":\"备库名称\"},{\"id\":\"\",\"name\":\"baSid\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"备库SID\",\"parameter\":\"备库SID\"},{\"id\":\"\",\"name\":\"backIp\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"备库IP\",\"parameter\":\"备库IP\"},{\"id\":\"\",\"name\":\"relation\",\"type\":\"String\",\"readonly\":false,\"remarks\":\"切换前关系\",\"parameter\":\"切换前关系\"}]", "iid": 264, "ipropertykeyid": 4}, {"ipropertyid": 77, "ipropertykeyname": "outputparameter", "igoupid": 0, "ipropertyvalue": "[]", "iid": 265, "ipropertykeyid": 5}], "map": {}, "ienclosure": "<mxGraphModel dx=\"1452\" dy=\"720\" grid=\"1\" gridSize=\"10\" guides=\"1\" tooltips=\"1\" connect=\"1\" arrows=\"1\" fold=\"1\" page=\"1\" pageScale=\"1\" pageWidth=\"827\" pageHeight=\"1169\" background=\"#f3f5f8\">\n  <root>\n    <mxCell id=\"0\"/>\n    <mxCell id=\"1\" parent=\"0\"/>\n    <mxCell id=\"2\" value=\"开始活动\" style=\"shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#f3f5f8;verticalAlign=top;imageAspect=1;aspect=fixed;image=/editor/images/adaptor/01.png\" adapter=\"start\" vertex=\"1\" parent=\"1\">\n      <mxGeometry x=\"60\" y=\"60\" width=\"42\" height=\"42\" as=\"geometry\"/>\n      <StartConfigBean as=\"bean\">\n        <Array as=\"pages\"/>\n        <InputSupper as=\"input\">\n          <Array as=\"children\">\n            <Object name=\"normal\" text=\"$normal\" leaf=\"0\" expanded=\"1\">\n              <Array as=\"children\">\n                <Object name=\"agentip\" text=\"agentip:String\" type=\"String\" value=\"\" leaf=\"1\"/>\n              </Array>\n            </Object>\n            <Object name=\"warn\" text=\"$warn\" leaf=\"0\" expanded=\"0\">\n              <Array as=\"children\">\n                <Object name=\"warnip\" text=\"warnip:String\" type=\"String\" value=\"\" leaf=\"1\"/>\n              </Array>\n            </Object>\n            <Object id=\"ActivityElement\" name=\"ActivityElement\" text=\"$ActivityElement\" leaf=\"0\" expanded=\"0\"/>\n          </Array>\n        </InputSupper>\n        <OutputSupper as=\"output\"/>\n      </StartConfigBean>\n    </mxCell>\n    <mxCell id=\"3\" value=\"结束活动\" style=\"shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#f3f5f8;verticalAlign=top;imageAspect=1;aspect=fixed;image=/editor/images/adaptor/02.png\" adapter=\"end\" vertex=\"1\" parent=\"1\">\n      <mxGeometry x=\"270\" y=\"60\" width=\"42\" height=\"42\" as=\"geometry\"/>\n      <EndConfigBean as=\"bean\">\n        <Array as=\"pages\"/>\n        <Object text=\"$\" expanded=\"1\" as=\"input\">\n          <Array as=\"children\">\n            <Object name=\"normal\" text=\"$normal\" leaf=\"0\" expanded=\"1\">\n              <Array as=\"children\"/>\n            </Object>\n            <Object name=\"warn\" text=\"$warn\" leaf=\"0\" expanded=\"0\">\n              <Array as=\"children\"/>\n            </Object>\n            <Object name=\"EndActElement\" text=\"$EndActElement\" leaf=\"0\" expanded=\"0\">\n              <Array as=\"children\"/>\n            </Object>\n          </Array>\n        </Object>\n        <Object text=\"$\" expanded=\"1\" as=\"output\">\n          <Array as=\"children\">\n            <Object name=\"EndActElement\" text=\"$EndActElement\" leaf=\"0\" expanded=\"0\">\n              <Array as=\"children\"/>\n            </Object>\n          </Array>\n        </Object>\n      </EndConfigBean>\n    </mxCell>\n  </root>\n</mxGraphModel>\n", "iname": "standard"}], "function": [], "publicres": []}}]