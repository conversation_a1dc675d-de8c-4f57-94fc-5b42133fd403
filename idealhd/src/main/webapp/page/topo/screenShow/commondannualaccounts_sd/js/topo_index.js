var refreshT;
var showFlag=false;
var autoLayout;
var coreFlag;
var actTimeCk_window;
var ganttCell="";
var highlightObject = [];
var graph;
var selCell=null;
var refreshTime=300;
var autoRefreshCheckShow;
//var rectUrl="url("+urlPath+proPath+"/page/topology/topoScreenDisplay/topoScreenDisplaySvg.html#radialGradient_r1)";
//var rectUrl="url(#radialGradient_r1)";
var rectUrl="url(#Gaussian_Blur)";
var browseType="mozilla";
Ext.onReady(function() {
    allinfo();
    var container = document.getElementById('topo_cn_area');

    container.style.height = document.documentElement.clientHeight - 200;
    // container.style.width = document.documentElement.clientWidth;
    // Checks if the browser is supported
    if (!mxClient.isBrowserSupported())
    {
        // Displays an error message if the browser is not supported.
        mxUtils.error('Browser is not supported!', 200, false);
    }
    else
    {
        // Enables crisp rendering of rectangles in SVG
        mxRectangleShape.prototype.crisp = true;

        // Creates the graph inside the given container
        graph = new mxGraph(container);
        graph.setEnabled(true);
        //移动
        mxGraphHandler.prototype.setMoveEnabled(false);
        graph.setConnectable(false);//cell是否可以连线
        graph.setCellsResizable(false);
        var style = graph.getStylesheet().getDefaultVertexStyle();
        style["fontSize"] = 14;
        var edgStyle=graph.getStylesheet().getDefaultEdgeStyle();
        edgStyle[mxConstants.STYLE_ROUNDED] = true;//圆角连线
        graph.setHtmlLabels(true);
        graph.view.setScale(0.15);
        graph.addListener(mxEvent.CLICK, function(sender, evt) {
            var cell = evt.getProperty('cell');
            selCell=cell;
            if (cell != null  && graph.getModel().isVertex(cell)){
                graph.setEnabled(true);
                hlDestroy();
                groupModel(ganttCell);
            }else if(cell != null  && graph.getModel().isEdge(cell)){
                graph.setEnabled(false);
            } else{
                graph.setEnabled(true);
            }
        });
        graph.addListener(mxEvent.DOUBLE_CLICK, function(sender, evt) {
            graph.setEnabled(false);
            var cell = evt.getProperty('cell');
/*            if(cell){
                if (graph.getModel().isVertex(cell)) {
                    if(showDateText=='true'){
                        var url = 'topoMainPage.do?filterPrjName='+ encodeURI(projectName)+ '&insName=' + queryDate+ '&flowname='+'&actName='+encodeURI(cell.realvalue);
                        window.location.href=url;
                    }
                }
            }*/
        });
        var parent = graph.getDefaultParent();

        // Adds cells to the model in a single step
        loadGraph();

        // Adds zoom buttons in top, left corner
        var buttons = document.createElement('div');
        buttons.style.position = 'absolute';
        buttons.style.overflow = 'visible';

        var bs = graph.getBorderSizes();
        buttons.style.top = (container.offsetTop + bs.y) + 'px';
        buttons.style.left = (container.offsetLeft + bs.x) + 'px';

        var left = 0;
        var bw = 70;
        var bh = 26;

        if (mxClient.IS_QUIRKS)
        {
            bw -= 1;
            bh -= 1;
        }

        function addButton(label, funct ,types)
        {
            if (types=='0'){
                var btn = document.createElement('div');
                mxUtils.write(btn, label);
                btn.style.width = bw + 'px';
                btn.style.height = bh + 'px';
                btn.style.left = left + 'px';
                btn.style.top = '0px';
                btn.setAttribute('class','btn_style');
                mxEvent.addListener(btn, 'click', function(evt)
                {
                    funct();
                    mxEvent.consume(evt);
                });
                left += bw+10;
                buttons.appendChild(btn);
            }else if (types=='2'){
                var btn1 = document.createElement('div');
                btn1.style.witdh= bw + 'px';
                btn1.style.height= bh + 'px';
                btn1.style.left=left;
                btn1.style.fontSize = '16px';
                btn1.style.color='#bbdceb';
                btn1.style.margin='5px 0px 0 5px';
                mxUtils.write(btn1, label);
                left += bw+25;
                buttons.appendChild(btn1);
            }else{
                var btn = document.createElement('div');
                btn.style.width = 90 + 'px';
                btn.style.height = bh + 'px';
                btn.style.left = left + 'px';
                btn.style.top = '0px';
                btn.setAttribute('class','btn_style');
                var checkbox = document.createElement('input');
                checkbox.setAttribute('type', 'checkbox');
                //checkbox.setAttribute('background-color', '#011661');
                //checkbox.setAttribute('border', '1px solid #034bb6');
                //checkbox.setAttribute('color', '#99a1a7');
                checkbox.style.backgroundColor="transparent";
                checkbox.style.border="1px solid rgb(16 207 172)";
                checkbox.style.color="#99a1a7";
                checkbox.style.display="inline-block";
                checkbox.style.width="15px";
                checkbox.style.height="15px";
                checkbox.style.verticalAlign="text-top";
                checkbox.setAttribute('name', 'chk1');
                checkbox.setAttribute('id', "chk1");
                checkbox.style.margin='0 5';
                btn.appendChild(checkbox);
                checkbox.checked=true;
                checkbox.setAttribute('checked', true);
                mxUtils.write(btn, label);
                mxEvent.addListener(checkbox, 'click', function(evt)
                {
                        if (this.checked) {
                            refreshTopoGanttShow = setInterval(function(){loadGraph(0);},parseInt(refreshTime)*1000);
                        } else {
                            clearInterval(refreshTopoGanttShow);
                        }
                });
                left += bw+25;
                buttons.appendChild(btn);
            }
        };
        addButton('放大', function()
        {
            graph.zoomIn();
            groupModel(ganttCell);
        },0);

        addButton('缩小', function()
        {
            graph.zoomOut();
            groupModel(ganttCell);
        },0);

        addButton('刷新', function()
        {
            loadGraph(1);
        },0);
        addButton('自动刷新', null,1);
        if (container.nextSibling != null)
        {
            container.parentNode.insertBefore(buttons, container.nextSibling);
        }
        else
        {
            container.appendChild(buttons);
        }
    };
    // 首次加载定时刷新
     autoRefreshCheckShow=document.getElementById("chk1")
    if (autoRefreshCheckShow.checked == true) {
        refreshTopoGanttShow = setInterval(function(){loadGraph(0);},parseInt(refreshTime)*1000);
    }

});
function groupModel(runstate){
    if (runstate != null && runstate != '') {
        for (var i = 0; i < runstate.length; i++) {
            var key = runstate[i].id;
            var keygroup=key+"___"+key;
            var cell = graph.getModel().getCell(key);
            if (cell != null && key!=-2 && key!=-3) {
                var cellState = graph.view.getState(cell);
                if(cellState){
                    var node =(cellState.shape).node;
                    var rect = node.firstChild;
                    var rectid=rect.getAttribute("id");
                    var x=rect.getAttribute("x");
                    var y=rect.getAttribute("y");
                    var w=rect.getAttribute("width");
                    var h=rect.getAttribute("height");
                    var rx=rect.getAttribute("rx");
                    var ry=rect.getAttribute("ry");
                    var stroke=rect.getAttribute("stroke");
                    var n = document.createElementNS( "http://www.w3.org/2000/svg", "rect" );
                    n.setAttribute('id', keygroup);
//								n.setAttribute('fill', rectUrl);
                    n.setAttribute('filter', rectUrl);
                    n.setAttribute('stroke', "");
                    n.setAttribute('x', (parseInt(x)-5));
                    n.setAttribute('y', (parseInt(y)-5));
                    n.setAttribute('width', (parseInt(w)+10));
                    n.setAttribute('height', (parseInt(h)+10));
                    if(rx=='' || rx==null || rx=='null'){
                        rx=0;
                    }
                    if(ry=='' || ry==null || ry=='null'){
                        ry=0;
                    }
                    n.setAttribute('rx', rx);
                    n.setAttribute('ry', ry);
                    n.setAttribute('stroke-width', 0);
                    n.setAttribute('pointer-events', "all");
                    if(parseInt(runstate[i].iisfail)>0){
                        if(runstate[i].istate=='0'&&runstate[i].iactType!='2'){
                            rect.setAttribute("stroke","#D28F33");
                            n.setAttribute("fill","#D28F33");
                            n.setAttribute("class","thum_03 thum_common");
                        }
                    }else {
                        n.setAttribute("class","thum_02 thum_common");
                    }
                    node.insertBefore(n, node.firstChild);
                }
            }
        }
    }
}
function hlDestroy(){
    for(var i=0;i<highlightObject.length;i++){
        highlightObject[i].destroy();
        highlightObject[i] = null;
    }
    highlightObject = [];
}
function loadGraph(){
    hlDestroy();
    graph.clearSelection();
    graph.setTooltips(true);
    var getTooltipForCell = graph.getTooltipForCell;
    graph.getTooltipForCell = function(cell) {
        if (cell != null && graph.getModel().isEdge(cell)) {
            return "";
        }
        if (cell != null  && cell.id=='-3') {
            return "";
        }
        var actState = "";
        if(cell.istate=='0'){
            actState = "运行中";
            if(parseInt(cell.iisfail)>0){
                console.log("cell---==::::"+cell);
                if(cell.iactType!='2'){
                    actState="异常";
                }
            }
        }else if(cell.istate=='1'||cell.istate=='32'){
            actState = "未运行";
        }else if(cell.istate=='2'){
                actState = "已完成";
        }else if(cell.istate=='3'){
            actState = "已完成";
        }
        var businessBeginTime =cell.istarttime;
        if(businessBeginTime!=null){
            businessBeginTime = businessBeginTime.toString().replace(/\|\|/g,"<br>");
        }
        var arriveTime =cell.iendtime;
        if(arriveTime!=null){
            arriveTime = arriveTime.toString().replace(/\|\|/g,"<br>");
        }

        var getArriveTime =cell.casttime;
        if(getArriveTime!=null){
            getArriveTime = getArriveTime.toString().replace(/\|\|/g,"<br>");
        }
        var wid='150px';
        return "<table id='focusTable' style='font-size:12px;color:#ffffff;border-radius:4px;line-height:1.5;background-color:#011a76;border:1px solid #00f0ff;width: 410px;padding: 10px;'>" +
            "<tr>" +
            "<td>" +
            "步骤标识:" +
            "</td>" +
            "<td>" +
            ""+cell.iserner+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "系统名称:" +
            "</td>" +
            "<td>" +
            ""+cell.iprojectname+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "步骤内容:" +
            "</td>" +
            "<td style='overflow-wrap: break-word;word-wrap: break-word;word-break: break-all;'>" +
            ""+cell.iactdes+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "主责部门:" +
            "</td>" +
            "<td>" +
            ""+cell.maindepartment+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "业务人员:" +
            "</td>" +
            "<td>" +
            ""+cell.businesspersonnel+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "技术人员:" +
            "</td>" +
            "<td>" +
            ""+cell.artisan+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "计划时间:" +
            "</td>" +
            "<td>" +
            ""+cell.plantime+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "开始时间:" +
            "</td>" +
            "<td>" +
            ""+businessBeginTime+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "结束时间:" +
            "</td>" +
            "<td>" +
            ""+arriveTime+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "耗时:" +
            "</td>" +
            "<td>" +
            ""+getArriveTime+"" +
            "</td>" +
            "</tr>" +
            "<tr>" +
            "<td>" +
            "状态:" +
            "</td>" +
            "<td>" +
            ""+actState+"" +
            "</td>" +
            "</tr>" +
            "</table>";
    };

    graph.getModel().beginUpdate();
    try
    {
        var doc = mxUtils.load(encodeURI("sdScreenTopoListCell.do"));
        var dec = new mxCodec(doc);
        dec.decode(doc.getDocumentElement(),graph.getModel());
        var vertices=graph.getChildVertices();
        ganttCell=vertices;
        var picP = "thum_09 thum_common";
        for(var i=0;i<vertices.length;i++){
            showFlag=vertices[i].showFlag;
            if (vertices[i].istate == '0') {
                // 运行中
                if(parseInt(vertices[i].iisfail)>0){
                    if(vertices[i].iactType!='2'){
                        picP ="thum_06 thum_common";
                    }
                }else {
                    picP = 'thum_08 thum_common';
                }
            } else if (vertices[i].istate == '1'||vertices[i].istate=='32') {
                // 未运行
                picP = 'thum_09 thum_common';
            } else if (vertices[i].istate == '2') {
                // 已完成
                picP = 'thum_07 thum_common';
            } else if (vertices[i].istate == '3') {
                // 完成
                picP = 'thum_07 thum_common';
            }
            // imageVerticalAlign=center;verticalAlign=middle;imageAlign=left;align=left;
            graph.removeCellOverlays(vertices[i]);
            vertices[i].setValue("<span class=\""+picP+"\" width=\"28\" height=\"28\" style=\"margin:0 0 -5px 0;display: block;background-repeat: no-repeat;background-size: 32px 32px;\"></span><span style='" +
                "    position: absolute;" +
                "    top: 36px;" +
                "    display:block;left:-18px;font-size: 12px;margin:0 auto;width:70px;'>"+vertices[i].iactname+"</span>" +
                "    <span style='position: absolute;" +
                "    top: -28px;" +
                "    left: -24px;" +
                "    background: #00f0ff;" +
                "    border-radius: 0 0 10px 0;" +
                "    padding: 2px 4px;'>"+vertices[i].value+"</span>");
            if(vertices[i].irunstate=='1'||vertices[i].irunstate=='2'){
                if(vertices[i].getStyle()){
                    vertices[i].setStyle(vertices[i].getStyle()+"fillColor=#2c4372;");//
                }else{
                    vertices[i].setStyle("fillColor=#2c4372;");
                }
            }
        }
        var firstCell = graph.getModel().getCell(-2);
        if(firstCell){
            autoLayout=firstCell.autoLayout;
            firstCell.setVisible(false);
        }
    }
    finally
    {
        // Updates the display
        graph.getModel().endUpdate();
    }
   // graph.graphToCenter();
    groupModel(ganttCell);
}
function allinfo(){
    var ua = navigator.userAgent;
    ua = ua.toLowerCase();
    var match = /(webkit)[ \/]([\w.]+)/.exec(ua) ||	/(opera)(?:.*version)?[ \/]([\w.]+)/.exec(ua) ||/(msie) ([\w.]+)/.exec(ua) ||!/compatible/.test(ua) && /(mozilla)(?:.*? rv:([\w.]+))?/.exec(ua) || [];
    // 如果需要获取浏览器版本号：match[2]
    switch(match[1]){
        case "msie": // ie
            if (parseInt(match[2]) === 6){ // ie6
                browseType="IE6";
            }else if (parseInt(match[2]) === 7) { // ie7
                browseType="IE7";
            }else if (parseInt(match[2]) === 8){ // ie8
                browseType="IE8";
            }else if(parseInt(match[2]) === 9){
                browseType="IE9";
            }else if(parseInt(match[2]) === 10){
                browseType="IE10";
            }else if(parseInt(match[2]) === 11){
                browseType="IE11";
            }
            break;
        case "webkit": // safari or chrome
            browseType="webkit";
            break;
        case "opera": // opera
            browseType="opera";
            break;
        case "mozilla": // Firefox
            browseType="mozilla";
            break;
        default:
            break;
    }
}