package com.ideal.dubbo.models.product;

import java.io.Serializable;



/**
 * The persistent class for the IEAI_SCRIPT_ATTACHMENT database table.
 * 
 */

public class IeaiScriptAttachment implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long iid;

	private String icontent;

    private String          icontents;

	private String iname;

	private String iscriptiid;

    private Long              isize;

    private Long              iuploadtime;
    private Long              iworkitemid;
    private int isTempFlag;

	public int getIsTempFlag() {
		return isTempFlag;
	}

	public void setIsTempFlag(int isTempFlag) {
		this.isTempFlag = isTempFlag;
	}

	public Long getIworkitemid() {
		return iworkitemid;
	}

	public void setIworkitemid(Long iworkitemid) {
		this.iworkitemid = iworkitemid;
	}

	public IeaiScriptAttachment() {
	}

	public Long getIid() {
		return this.iid;
	}

	public void setIid(Long iid) {
		this.iid = iid;
	}

	public String getIcontent() {
		return this.icontent;
	}

	public void setIcontent(String icontent) {
		this.icontent = icontent;
	}

    public String getIcontents ()
    {
         return this.icontents;
	}

    public void setIcontents ( String icontents )
    {
		this.icontents = icontents;
	}

	public String getIname() {
		return this.iname;
	}

	public void setIname(String iname) {
		this.iname = iname;
	}

	public String getIscriptiid() {
		return this.iscriptiid;
	}

	public void setIscriptiid(String iscriptiid) {
		this.iscriptiid = iscriptiid;
	}

    public Long getIsize ()
    {
		return this.isize;
	}

    public void setIsize ( Long isize )
    {
		this.isize = isize;
	}

    public Long getIuploadtime ()
    {
		return this.iuploadtime;
	}

    public void setIuploadtime ( Long iuploadtime )
    {
		this.iuploadtime = iuploadtime;
	}

}