package com.ideal.ieai.prv.provider.utils.espackage;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.WriteTypeHint;

@Document(indexName = "#{@dynamicIndexUtil.getIndex()}", createIndex = false, writeTypeHint = WriteTypeHint.FALSE)
public class StdoutAndStderrDocument {

    @Id
    @Field(type = FieldType.Keyword,docValues = false,norms = false)
    private Long instanceId;

    @Field(type = FieldType.Keyword, index = false,docValues = false,norms = false)
    private String stdout;

    @Field(type = FieldType.Keyword, index = false,docValues = false,norms = false)
    private String stderr;

    public Long getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    public String getStdout() {
        return stdout;
    }

    public void setStdout(String stdout) {
        this.stdout = stdout;
    }

    public String getStderr() {
        return stderr;
    }

    public void setStderr(String stderr) {
        this.stderr = stderr;
    }

    public StdoutAndStderrDocument(Long instanceId, String stdout, String stderr) {
        this.instanceId = instanceId;
        this.stdout = stdout;
        this.stderr = stderr;
    }

    @Override
    public String toString() {
        return "StdoutAndStderrDocument{" +
                "instanceId=" + instanceId +
                ", stdout='" + stdout + '\'' +
                ", stderr='" + stderr + '\'' +
                '}';
    }
}
