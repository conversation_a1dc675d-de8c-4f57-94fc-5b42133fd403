package com.ideal.controller.routinetasks.emergencyStart;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.ideal.common.utils.ParseJson;
import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.EMConstants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.emergency.repository.start.EmFlowstartinstversionService;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.agencytaskmanage.AgencyTaskManageManager;
import com.ideal.ieai.server.routinetasks.repository.emins.RoutineTasksInstanceConfigBeanForQuery;
import com.ideal.ieai.server.routinetasks.repository.start.RoutineTasksFlowStartInstVersionBean;
import com.ideal.ieai.server.routinetasks.repository.start.RoutineTasksFlowstartinstversionService;
import com.ideal.ieai.server.routinetasks.repository.start.RoutineTasksInstanceinfoBean;
import com.ideal.ieai.server.routinetasks.repository.start.SelectIpInfoBean;
import com.ideal.service.routinetasks.emstart.RoutineTasksStartService;
import com.ideal.service.routinetasks.insconfig.RoutineTasksInsConfigService;

/**
 * <ul>
 * <li>Title: FlowstartController.java</li>
 * <li>Description:上线发起处理类</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2016年05月30日
 */
@Controller
@RequestMapping(value = "routineTasksFlowstart")
public class RoutineTasksFlowstartController
{
    static Logger logger = Logger.getLogger(RoutineTasksFlowstartController.class);
    public static final String THE       = " The ";
    public static final String METHOD_OF = " method  of ";
    public static final String IS_ERROR  = " is error ：";
    @RequestMapping("flowstartworkflowForEM.do")
    public String flowstartworkflow ( HttpServletRequest request )
    {
        return "routinetasks/emStart/eminstanceConfigHome";
    }
    
    /**
     * <li>Description:EM 应急方案 列表首页</li> 
     * <AUTHOR>
     * 2017年3月26日 
     * @return
     * return String
     */
    @RequestMapping("emstart/getInstanceInstanceInfoForEM.do")
    public String getInstanceInstanceInfoForEM(HttpServletRequest request)
    {
        String instanceId = request.getParameter("instanceId");
        String scenetype=RoutineTasksFlowstartinstversionService.getInstance().getSceneType(instanceId);
        request.setAttribute("scenetype", scenetype);
        return "routinetasks/emStart/eminstance_step_info_instance";
    }
    
    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2017年3月26日 
     * @return
     * return String
     */
    @RequestMapping("emstart/getInstanceBasicInfoForEM.do")
    public String getInstanceBasicInfoForEM ()
    {
        return "routinetasks/emStart/eminstance_step_info_basic";
    }
    
    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2016年5月26日 
     * @param request
     * @return
     * @throws RepositoryException
     * return Map<String,List<?>>
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping("flowstart/getIIP.do")
    @ResponseBody
    public Map queryIpById ( HttpServletRequest request )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String errorMsg = "";
        Map map = new HashMap();
        try
        {
            Map coumputerNameMap = RoutineTasksFlowstartinstversionService.getInstance().getComputeName();
            List res = new ArrayList();
            /**
            //            //系统信息，步骤信息，instanceinfo表iid
            //            String instanceinfoIID = request.getParameter("instanceinfoIID");
            //            System.out.println("###########\ninstanceinfoIID="+instanceinfoIID);
            //            long iid = Long.parseLong(instanceinfoIID);
            //            //通过iid查iip
            //            RoutineTasksFlowstartinstversionService service = new RoutineTasksFlowstartinstversionService();
            //            map = service.queryIpById(iid);
            */
            //处理iip串
            String localstr = request.getParameter("localStr");
            String instanceIID = request.getParameter("iinstanceid");
            String iipStr = request.getParameter("iip").trim();
            String convertflag = request.getParameter("convertflag");
            String seliip = request.getParameter("seliip");
            String unseliip = request.getParameter("unseliip");
            List ipList = new ArrayList();
            if("0".equals(convertflag)){
                ipList = RoutineTasksFlowstartinstversionService.getInstance().getIpListFromIpStrForDoubleCheckHandlePage(instanceIID,iipStr,
                    coumputerNameMap,localstr,seliip,unseliip);
            }else{
                ipList = RoutineTasksFlowstartinstversionService.getInstance().getAllStepsIp(instanceIID);
            }
            res.addAll(ipList);
            map.put("total", res.size());
            map.put("dataList", res);
            
            map.put(Constants.STR_SUCCESS, true);
        } catch (Exception e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, errorMsg);
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }
        return map;
    }

    /**
     * <li>Description:上线发起页面，获得全部"系统名称"</li>
     * 
     * <AUTHOR> 2016-05-30
     * @param request
     * @param paramData
     * @throws RepositoryException
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping("flowstart/getAllSysNameForEM.do")
    @ResponseBody
    public Map getAllSysName ( HttpServletRequest request,
            RoutineTasksFlowStartInstVersionBean flowstartinstversion, String paramData )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String errorMsg = "";
        Map map = new HashMap();
        try
        {
            /**
            ////            Map<String, String> filterMap = getSysNameFilter(request);
            //            Map<String, String> filterMap =this.getSysNameFilterForEM(request);
            //            List<RoutineTasksFlowStartInstVersionBean> flStartInstVerList = new ArrayList<RoutineTasksFlowStartInstVersionBean>();
            //            RoutineTasksFlowstartinstversionService service = new RoutineTasksFlowstartinstversionService();
            //            map = service.queryAllSysName(flowstartinstversion, filterMap);
            */
            RoutineTasksFlowstartinstversionService service = new RoutineTasksFlowstartinstversionService();
            String startUserid = SessionData.getSessionData(request).getUserInnerCode();
            map = service.queryAllSysName(flowstartinstversion, Long.parseLong(startUserid),
                Constants.IEAI_ROUTINE_TASKS);
            
            map.put(Constants.STR_SUCCESS, true);
        } catch (Exception e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, errorMsg);
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }
        return map;
    }

    /**
     * <li>Description:上线发起页面，获得全部"版本信息"</li>
     * 
     * <AUTHOR> 2016-05-30
     * @param request
     * @param paramData
     * @throws RepositoryException
     */
    @RequestMapping("flowstart/getAllVersionForEM.do")
    @ResponseBody
    public Map getAllVersion ( HttpServletRequest request,
            RoutineTasksFlowStartInstVersionBean flowstartinstversion, String sysnam, String paramData ,Long iupperId)
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String errorMsg = "";
        Map map = new HashMap();
        try
        {
            RoutineTasksFlowstartinstversionService service = new RoutineTasksFlowstartinstversionService();
            /**
            //            map = service.getAllVersion(flowstartinstversion, sysnam, Constants.TASK_CHANGE_AUTO,iupperId);
            //            map = service.getAllVersion(flowstartinstversion, sysnam, Constants.TASK_EMERGENCY_OPER,iupperId);
            */
            map = service.getAllVersion(flowstartinstversion, sysnam, Constants.IEAI_ROUTINE_TASKS,iupperId);
            map.put(Constants.STR_SUCCESS, true);
        } catch (Exception e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, errorMsg);
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }
        return map;
    }

    /**
     * <li>Description:查询表格数据，查询实例信息</li>
     * 
     * <AUTHOR> 2016-1-12
     * @param request
     * @param instanceinfo
     * @param start
     * @param limit return Map
     */
    @RequestMapping("flowstart/queryInstanceinfo_ep.do")
    @ResponseBody
    public Map queryInstanceinfo ( HttpServletRequest request, RoutineTasksInstanceinfoBean instanceinfo,
            String sysnam, String iinstanceid, int start, int limit )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new LinkedHashMap();
        RoutineTasksFlowstartinstversionService instanceinfoService = new RoutineTasksFlowstartinstversionService();
        try
        {
            map = instanceinfoService.queryInstanceinfo(instanceinfo, sysnam, iinstanceid, start,
                limit);
            map.put(Constants.STR_SUCCESS, true);
        } catch (RepositoryException repositoryexception)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, repositoryexception.getMessage());
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, repositoryexception);
        }
        return map;
    }

    public String string2Json ( String s )
    {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < s.length(); i++)
        {
            char c = s.charAt(i);
            switch (c)
            {
                case '\"':
                    sb.append("\\\"");
                    break;
                case '\\':
                    sb.append("\\\\");
                    break;
                case '/':
                    sb.append("\\/");
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                default:
                    sb.append(c);
            }
        }
        return sb.toString();
    }

    @RequestMapping("submitInsgdb_ep.do")
    @ResponseBody
    public Map submitIns ( HttpServletRequest request, String jsonData,
            String sysName, String instName_input, String version, String isysid, String isystype,
            long iworkItemid ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map map = new HashMap();
        List<RoutineTasksInstanceinfoBean> instList = new ArrayList<RoutineTasksInstanceinfoBean>();
        JSONArray jsonArr;
        RoutineTasksFlowstartinstversionService servi = new RoutineTasksFlowstartinstversionService();

        SessionData sessionData = SessionData.getSessionData(request);
        String startUserName = sessionData.getUserName();
        String startUserid = sessionData.getUserInnerCode();

        try
        {
            jsonArr = new JSONArray(jsonData);
            JSONObject json2 = null;
            RoutineTasksInstanceinfoBean instBean = null;
            for (int i = 0; i < jsonArr.length(); i++)
            {
                json2 = jsonArr.getJSONObject(i);
                instBean = new RoutineTasksInstanceinfoBean();
                if (json2.getBoolean("icurDisable"))
                {
                    continue;
                }
                instBean.setIinstanceid(json2.getString("iinstanceid"));
                instBean.setIinstancename(json2.getString("iinstancename"));
                instBean.setIserner(json2.getString("iserner"));
                instBean.setIconner(json2.getString("iconner"));
                instBean.setIprener(json2.getString("iprener"));

                instBean.setIactname(json2.getString("iactname"));
                instBean.setIactdes(json2.getString("iactdes"));
                instBean.setIacttype(json2.getString("iacttype"));
                instBean.setIreminfo(json2.getString("ireminfo"));
                instBean.setIip(json2.getString("iip"));
                instBean.setIport(json2.getString("iport"));

                instBean.setIsystype(json2.getString("isystype"));
                instBean.setIshellpath(json2.getString("ishellpath"));
                instBean.setIswichtype(json2.getString("iswichtype"));
                instBean.setIparameter(json2.getString("iparameter"));
                instBean.setIparacheck(json2.getString("iparacheck"));

                instBean.setIparaswitch(json2.getString("iparaswitch"));
                instBean.setIexecuser(json2.getString("iexecuser"));
                instBean.setIshellscript(json2.getString("ishellscript"));
                instBean.setIexpeceinfo(json2.getString("iexpeceinfo"));
                instBean.setIisloadenv(json2.getString("iisloadenv"));
                instBean.setIconnerName(json2.getString("iconnerName"));
                instBean.setIcenter(json2.getString("icenter"));

                instList.add(instBean);
            }
            String startFlowServerIP = servi.saveSubmitInfo(instList, sysName, instName_input,
                version, isysid, isystype, startUserName, startUserid, iworkItemid);
            logger.info("Instance " + instName_input + " start success at Serverip : "
                    + startFlowServerIP + " , Start User : " + startUserName);
            map.put(Constants.STR_SUCCESS, true);
            map.put(Constants.STR_MESSAGE, "启动成功");
        } catch (JSONException e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, "启动失败");
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        } catch (Exception e)
        {
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }

        return map;
    }

    /**
     * <li>Description:业务系统名称进行过滤</li>
     * 
     * @param request
     * @throws RepositoryException return void
     */
    /**
    //    private Map<String, String> getSysNameFilter ( HttpServletRequest request )
    //            throws RepositoryException
    //    {
    //        SessionData sessionData = SessionData.getSessionData(request);
    //        String start_userName = sessionData.getUserName();
    //        String start_userid = sessionData.getUserInnerCode();
    //
    //        List<BusinessSystem> busSysNameList = null; // 业务系统名称列表
    //        long userid = Long.parseLong(start_userid);
    //        busSysNameList = DataCenterOperationManage.getInstance().getSysNam(userid,
    //            DataCenterOperationManage.SYSTYP_SUS_AUTO); // 自动化应用变更(-8)
    //
    //        Map<String, String> filterMap = new LinkedHashMap<String, String>();
    //        String sysName = "";
    //        for (int i = 0; i < busSysNameList.size(); i++)
    //        {
    //            sysName = busSysNameList.get(i).getSysName();
    //            filterMap.put(sysName, sysName);
    //        }
    //        return filterMap;
    //    }
    */
    /**
     * 
     * @Title: getSysNameFilterForEM 
     * @Description: 业务系统名称进行过滤【从ieai_sys迁移至ieai_project表后】
     * @param request
     * @return
     * @throws RepositoryException
     * @return Map<String,String>    返回类型 
     * @throws 
     * @变更记录 2016年5月24日  manxi_zhao.
     */
    /**
    private Map<String, String> getSysNameFilterForEM ( HttpServletRequest request )
            throws RepositoryException
    {
        SessionData sessionData = SessionData.getSessionData(request);
        String startUserid = sessionData.getUserInnerCode();
    
        List<ProjectBean> projectList = null; 
        long userid = Long.parseLong(startUserid);
    //        projectList=DataCenterOperationManage.getInstance().getProject(null, userid, 3);// 自动化应用变更(3)
        projectList=DataCenterOperationManage.getInstance().getProject(null, userid, Constants.IEAI_ROUTINE_TASKS);// 应急(6)
        Map<String, String> filterMap = new LinkedHashMap<String, String>();
        for(ProjectBean projectBean:projectList)
        {
            filterMap.put(projectBean.getiName(), projectBean.getiName());
        }
        return filterMap;
    }
    */
    /**
     * 
     * @Title: creatInsgdb
     * @Description:【应急操作】创建双人复核任务
     * @param request
     * @param jsonData
     * @param sysName
     * @param instName
     * @param version
     * @param istate
     * @param iinstanceid
     * @param execUser
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年4月1日 manxi_zhao.
     */
    @RequestMapping("creatInsgdbForEM.do")
    @ResponseBody
    public Map creatInsgdb ( HttpServletRequest request, String jsonData,String jsonParamData, String sysName,
            String instName, String version, int istate, long iinstanceid, String execUser,String butterflyVersion,String selIpFlagArr,String unselIpFlagArr    )
            throws RepositoryException, JSONException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String errMsg = "";
        Map map = new HashMap();
        JSONArray jsonArr = new JSONArray();
        JSONArray selIpFlagArrTwo = new JSONArray();
        JSONArray unselIpFlagArrTwo = new JSONArray();
        JSONArray jsonArrOne = new JSONArray();
        SessionData sessionData = SessionData.getSessionData(request);
        List selectids = new ArrayList();
        String convertflag = request.getParameter("convertflag");
        //如果切换为ip选择步骤模式
        if("1".equals(convertflag)){
            try
            {
                jsonData = this.dealIp(iinstanceid,jsonData);
               
            } catch (JSONException e)
            {
                logger.error("EmStartDirectlyController.startDirectlyForEM JSONException error:",e);
            }
        }
        try
        {
            jsonArr = new JSONArray(jsonData);
            if(StringUtils.isNotBlank(selIpFlagArr) && StringUtils.isNotBlank(selIpFlagArr)){
                selIpFlagArrTwo = new JSONArray(selIpFlagArr);
                unselIpFlagArrTwo = new JSONArray(unselIpFlagArr);
                
            }
            for (int i = 0; i < jsonArr.length(); i++)
            {
                jsonArrOne.put(jsonArr.getJSONObject(i));
            }
        } catch (JSONException e1)
        {
            logger.error("EmStartDirectlyController.startDirectlyForEM JSONException error:",e1);
        }
        List<Map> stepids =RoutineTasksFlowstartinstversionService.getInstance().getAllStepId(iinstanceid);
        boolean ycShowFilterIpByFlagSwitch = Environment.getInstance().getBooleanConfigNew2("em.yc.filter.ip.switch", false);
        if(ycShowFilterIpByFlagSwitch ){
            /*jsonArr = new JSONArray();
            for (int i = 0; i < jsonArrOne.length(); i++){
                JSONObject json3 = jsonArrOne.getJSONObject(i);
                String iipflag = EmFlowstartinstversionService.getInstance().getIIpFlag(json3.get("iid").toString());
                List iipstrone = new ArrayList();
                for(int j = 0;j<selIpFlagArrTwo.length();j++){
                    JSONObject json5 = selIpFlagArrTwo.getJSONObject(j);
                    if(iipflag.equals(json5.get("iid").toString())){
                        String flagiiparr = json5.get("iip").toString();
                        flagiiparr=flagiiparr.substring(1);
                        flagiiparr=flagiiparr.substring(0,flagiiparr.length()-1);
                        flagiiparr = flagiiparr.replaceAll("\"","");
                        List list=Arrays.asList(flagiiparr.split(","));
                        String[] iiparr = json3.get("iip").toString().split(" ");
                        for(String ipn :iiparr){
                            if(list.contains(ipn)){  
                                iipstrone.add(ipn);
                            }
                        }
                        JSONObject json4 = new JSONObject();
                        json4.put("iid",json3.get("iid").toString());
                        json4.put("iip",StringUtils.join(iipstrone, " "));
                        jsonArr.put(json4);
                        break;
                    }
                } 
            }
            jsonData=jsonArr.toString();*/
            jsonArr = new JSONArray();
            //遍历每个步骤
            for(Map mapone : stepids){
                //获取步骤里的所有ip
                String iiplocal = mapone.get("iip").toString();
                iiplocal = iiplocal.replaceAll("\n", " ");
                String[] iiplocalarr=iiplocal.split(" ");
                //将步骤里的ip转化成list
                List list=Arrays.asList(iiplocalarr);
                List seliplist = new ArrayList();
                //获取步骤里的标识
                //String stepipflag =mapone.get("ipflag").toString();
                //如果标识不为空
                if(mapone.get("ipflag") != null && StringUtils.isNotBlank(mapone.get("ipflag").toString())){
                    //遍历标识ip组
                    String extraiip = "";
                    String finaliip = "";
                    String stepipflag = mapone.get("ipflag").toString();
                    for(int j = 0;j<selIpFlagArrTwo.length();j++){
                        JSONObject json5 = selIpFlagArrTwo.getJSONObject(j);
                        //如果步骤标识与遍历的标识相等
                        if(stepipflag.equals(json5.get("iid").toString())){
                            //取出对应标识的iip,转换成list
                            String flagiiparr = json5.get("iip").toString();
                            flagiiparr=flagiiparr.substring(1);
                            flagiiparr=flagiiparr.substring(0,flagiiparr.length()-1);
                            flagiiparr = flagiiparr.replaceAll("\"","");
                            List<String> flagiiplist=Arrays.asList(flagiiparr.split(","));
                            //遍历标识里的flagiiplist
                            for (int i = 0; i < jsonArrOne.length(); i++)
                            {
                                JSONObject json6 = jsonArrOne.getJSONObject(i);
                                if(json6.get("iid").toString().equals(mapone.get("iid").toString())){
                                    String[] iiparr = json6.get("iip").toString().split(" ");
                                    for(String ip : iiparr){
                                        seliplist.add(ip);
                                    }
                                }
                                
                            }
                            
                            for(String localip : flagiiplist){
                                if(StringUtils.isNotBlank(localip)){
                                    //如果标识里的ip不为空，则看选中的ip组里包不包含这个ip,不包含则装进新的jsonArr里
                                    
                                    if( list.contains(localip) && !seliplist.contains(localip)){
                                        seliplist.add(localip);
                                        
                                    }
                                }
                            }
                        }
                    }
                    for(int j = 0;j<unselIpFlagArrTwo.length();j++){
                        JSONObject json5 = unselIpFlagArrTwo.getJSONObject(j);
                        //如果步骤标识与遍历的标识相等
                        if(stepipflag.equals(json5.get("iid").toString())){
                            //取出对应标识的iip,转换成list
                            String flagiiparr = json5.get("iip").toString();
                            flagiiparr=flagiiparr.substring(1);
                            flagiiparr=flagiiparr.substring(0,flagiiparr.length()-1);
                            flagiiparr = flagiiparr.replaceAll("\"","");
                            List<String> flagiiplist=Arrays.asList(flagiiparr.split(","));
                            
                            //遍历标识里的flagiiplist
                            for(String localip : flagiiplist){
                                if(StringUtils.isNotBlank(localip)){
                                    //如果标识里的ip不为空，则看选中的ip组里包不包含这个ip,不包含则装进新的jsonArr里
                                    if(seliplist.contains(localip)){
                                        int num = seliplist.indexOf(localip);
                                        seliplist.remove(num);
                                    }
                                }
                            }
                            
                        }
                    }
                    JSONObject object = new JSONObject(); 
                    object.put("iid", mapone.get("iid").toString());
                    object.put("iip", StringUtils.join(seliplist, " "));
                    jsonArr.put(object);
                      
                }else{
                    //如果标识为空 遍历选中的步骤
                    for (int h = 0; h < jsonArrOne.length(); h++)
                    {
                        JSONObject json6 = jsonArrOne.getJSONObject(h);
                        //如果选中的步骤与遍历的步骤相等，就将选中的步骤里的iip存进新的jsonArr
                        if(json6.get("iid").toString().equals(mapone.get("iid").toString())){
                            jsonArr.put(json6);
                        }
                    }
                }
            }
            jsonData=jsonArr.toString();
        }
        boolean check =Environment.getInstance().getBooleanConfig("em.check.ipbind.switch",false);
        
        if(check){
            Map checkmap = RoutineTasksFlowstartinstversionService.getInstance().checkStepidNotInJson(stepids,jsonData);
            if(!(Boolean)checkmap.get("success")){
                
                String notBindNameSteps =StringUtils.join((List)checkmap.get("notBindIpSteps"),",");
                map.put(Constants.STR_SUCCESS, false);
                map.put(Constants.STR_MESSAGE, "预案步骤 "+notBindNameSteps+" 没有绑定IP！");
                return map;
            }
        }else{
            if(jsonArr.length() !=stepids.size() ){
                map.put(Constants.STR_SUCCESS, false);
                map.put(Constants.STR_MESSAGE, "请等待系统信息步骤列表加载后再启动！");
                return map;
            }
        }
        //begin.
        //选择的服务器Ip信息
        List<SelectIpInfoBean> ipList = new ArrayList<SelectIpInfoBean>();
        SelectIpInfoBean bean = null;
        //end.

        List<Map<String, Object>> emParams = new ArrayList<Map<String, Object>>();
        try
        {
            if(StringUtils.isNotBlank(jsonParamData)){
                emParams = ParseJson.JSON2List(jsonParamData);
            }
            StringBuilder iidString = new StringBuilder();
            jsonArr = new JSONArray(jsonData);
            JSONObject json2 = null;
            for (int i = 0; i < jsonArr.length(); i++)
            {
                json2 = jsonArr.getJSONObject(i);
/**
//                if (json2.getBoolean("icurDisable"))
//                {
//
//                    if (iidString.length() > 0)
//                    {
//                        iidString.append(",");
//                    }
//                    iidString.append(json3.getString("iid"));
//                }
*/               
                //组织选择的服务器Ip信息列表
                long iinfoId = json2.getLong("iid");
                String iipStr = json2.getString("iip");
                String allIpStr = RoutineTasksFlowstartinstversionService.getInstance().getIpStrByInfoId(iinfoId,Constants.IEAI_ROUTINE_TASKS);
                /**
                //                System.out.println("<"+iinfoId+","+allIpStr+","+iipStr+">");
                */
                /**iipStr.replaceAll("\n", " ");*/
                String[] ipArr = iipStr.split(EMConstants.IP_SPLIT);//editorIp.js中，分隔符为空格" "
                allIpStr = allIpStr.replaceAll("\n", EMConstants.IP_SPLIT);
                String[] allIpArr = allIpStr.split(EMConstants.IP_SPLIT);
                for(int m=0; m<allIpArr.length; m++){
                    String oneIp = allIpArr[m];
                    bean = new SelectIpInfoBean();
                    bean.setIip(oneIp);
                    bean.setIinfoId(iinfoId);
                    bean.setChecked(0);//默认没选中
                    //匹配，看是否选中
                    for(int n=0; n<ipArr.length; n++){
                        String iip = ipArr[n];
                        if(!(null==iip || "".equals(iip.trim())) 
                                && iip.equals(oneIp)){
                            bean.setChecked(1);//选中
                        }
                    }
                    // begin.added 2017-10-10.
                    // 处理空IP
                    if ("".equals(bean.getIip()))
                    {
                        continue;
                    }
                    // end.added 2017-10-10.
                    //组织ipList
                    ipList.add(bean);
                }
            }
            //begin.added by manxi_zhao.2017-03-27.
            //校验是否有agent正在执行该系统
            //不确定需不需要校验，暂时注释
            /*boolean isSysRunOnAgent = RoutineTasksFlowstartinstversionService.getInstance().checkSysRunOnAgent(ipList,Constants.IEAI_ROUTINE_TASKS);
            if (!isSysRunOnAgent)
            {
                errMsg = "所选IP中存在正在运行应急操作的Agent!";
                map.put(Constants.STR_SUCCESS, false);
            }*/
            //end.added by manxi_zhao.2017-03-27.
            if("".equals(errMsg))
            {
             // 校验动态参数
                String mess = "";
                if (null != emParams && emParams.size() > 0)
                {

                    for (int z = 0; z < emParams.size(); z++)
                    {
                        Map<String, Object> bsMap = emParams.get(z);
                        String parameterkey = bsMap.get("parameterkey").toString().trim();
                        String parameterValue = bsMap.get("parameterValue").toString().trim();
                        if (parameterValue.getBytes().length > 255)
                        {
                            mess += "<p>" + parameterkey + "对应的值超过255字符";
                        }
                    }
                }
                if (null != mess && !"".equals(mess))
                {
                    map.put("success", false);
                    map.put("message", mess);
                    return map;
                }
                
                Object[] workObj = RoutineTasksFlowstartinstversionService.getInstance().saveWorkItem(iidString.toString(),
                    sysName, instName, version, sessionData.getLoginName(), execUser, istate, iinstanceid, ipList, butterflyVersion,
                    Constants.IEAI_ROUTINE_TASKS,"dynamicParameter",emParams);
                
                //打印日志
                /**
                //            SessionData sessionData = SessionData.getSessionData(request);
                */
                String userName = sessionData.getUserName();
                logger.info("用户 [" + userName + "] 发起 [" + sysName + "] 系统下的 [" + version + "] 预案。");
                errMsg = "提交成功";
                map.put(Constants.STR_SUCCESS, true);
                map.put("workItemId", workObj[1]);
            }
            //4.7.29 海峡银行 关联单号修改代办状态
            boolean numShowSwitch = Environment.getInstance().getemAssociatedSwitch();
            if(numShowSwitch){
                AgencyTaskManageManager.getInstance().stopAgencyTask(butterflyVersion,1,Constants.IEAI_OPM);
            }
            map.put(Constants.STR_MESSAGE, errMsg);
        } catch (JSONException e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, "提交失败");
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        } catch (Exception e)
        {
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }

        return map;
    }
    
    private String dealIp ( long iinstanceid,String jsonData ) throws JSONException
    {
        List<String> infoidlist = RoutineTasksFlowstartinstversionService.getInstance().getAllInfoid(iinstanceid); 
        List listnew = new ArrayList();
        for(String infoid : infoidlist){
            JSONArray jsonArr = new JSONArray(jsonData);
            JSONObject json2 = null;
            List ips = new ArrayList();
            Map map = new HashMap();
            map.put("iid", infoid);
            for (int i = 0; i < jsonArr.length(); i++)
            {
                json2 = jsonArr.getJSONObject(i);
                String iids = json2.get("iid").toString();
                List iidlist = Arrays.asList(iids.split(" "));
                if(iidlist.contains(infoid)){
                    boolean isContains = RoutineTasksFlowstartinstversionService.getInstance().isStepContainsIp(infoid,json2.get("iip").toString());
                    if(isContains){
                        ips.add(json2.get("iip").toString());
                    }
                }
            }
            String ipstr="";
            if(ips.size() != 0){
                ipstr=listToString(ips," ");
            }
            map.put("iip", ipstr);
            listnew.add(map);
        }
        String jsonstr=JSON.toJSONString(listnew);
        return jsonstr;
    }
    
    public String listToString(List list, String separator) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            sb.append(list.get(i)).append(separator);
        }
        return sb.toString().substring(0, sb.toString().length() - 1);
    }

    /**
     * 
     * @Title: getAuditorList
     * @Description: 【应急操作】获得可操作指定业务系统所有用户
     * @param request
     * @param loginName
     * @param sysId
     * @return
     * @throws RepositoryException
     * @return List<Map> 返回类型
     * @throws
     * @变更记录 2016年5月27日 manxi_zhao.
     */
    @RequestMapping("getAuditorListForEM.do")
    @ResponseBody
    public Object getAuditorListForEM ( HttpServletRequest request, String loginName,
            String sysName,Long iupperId ) throws RepositoryException
    {
        List<Map> returnList = null;
        try
        {
            /**
            //            long sysId = RoutineTasksFlowstartinstversionService.getInstance().getSysIdBySysName(sysName,
            //                3);
            //            returnList = RoutineTasksFlowstartinstversionService.getInstance().getAuditorList(
            //                SessionData.getSessionData(request).getLoginName(), iupperId, -3);
            */
            int allSysId = -1 * Constants.IEAI_ROUTINE_TASKS;
            returnList = RoutineTasksFlowstartinstversionService.getInstance().getAuditorList(
              SessionData.getSessionData(request).getLoginName(), iupperId, allSysId,Constants.IEAI_ROUTINE_TASKS);
        } catch (Exception e)
        {
            logger.info("getAuditorList error :" + e.getMessage(),e);

        }
        return returnList;
    }
    @RequestMapping("getAuditorListForYc.do")
    @ResponseBody
    public Object getAuditorListForYc ( HttpServletRequest request, Long sysId ,int modelType) throws RepositoryException
    {
        List<Map> returnList = null;
        try
        {
            int allmodel = -1 * modelType;
            returnList = RoutineTasksFlowstartinstversionService.getInstance().getAuditorList(
              SessionData.getSessionData(request).getLoginName(), sysId, allmodel,modelType);
        } catch (Exception e)
        {
            logger.info("getAuditorListForYc error :" + e.getMessage(),e);
        }
        return returnList;
    }
    /**
     * 
     * @Title: submitInsgdbForSUS
     * @Description: 【应急操作】双人复核，启动
     * @param request
     * @param sysName
     * @param instName_input
     * @param version
     * @param isysid
     * @param isystype
     * @param iworkItemid
     * @return
     * @throws RepositoryException
     * @return Map<String,List<?>> 返回类型
     * @throws
     * @变更记录 2016年5月30日 manxi_zhao.
     */
    @RequestMapping("submitInsgdbForEM.do")
    @ResponseBody
    public Map submitInsgdbForEM ( HttpServletRequest request, String sysName,
            String instName_input, String version, String isysid, Long iworkItemid,String butterflyVersion )
            throws RepositoryException
    {
        String errMsg = "";
        Map map = new HashMap();
        Map<String, String> paramMap = new HashMap<String, String>();
        SessionData sessionData = SessionData.getSessionData(request);
        String startUserName = sessionData.getUserName();
        try
        {
            //begin.added by manxi_zhao.2017-03-27.
            //校验是否有agent正在执行该系统
            List<SelectIpInfoBean> ipList = RoutineTasksFlowstartinstversionService.getInstance().getIpListByWorkitemid(iworkItemid,Constants.IEAI_ROUTINE_TASKS);
            //不确定需不需要校验，暂时注释
           /* boolean isSysRunOnAgent = RoutineTasksFlowstartinstversionService.getInstance().checkSysRunOnAgent(ipList,Constants.IEAI_ROUTINE_TASKS);
            if (!isSysRunOnAgent)
            {
                errMsg = "所选IP中存在正在运行应急操作的Agent!";
                map.put(Constants.STR_SUCCESS, false);
                map.put(Constants.STR_MESSAGE, errMsg);
            }*/
            //end.added byzhao.2017-03-27.
            if("".equals(errMsg))
            {
                boolean isDynamic = RoutineTasksStartService.getInstance().isDynamic(iworkItemid);
                if (isDynamic)
                {
                    // 根据workItemid查找是否有动态参数
                    paramMap = RoutineTasksStartService.getInstance().getParamMap(iworkItemid);

                    // if (null != paramMap && !paramMap.isEmpty())
                    // {
                    // 核心应急启动
                    List<Map<String, Object>> emParams = RoutineTasksStartService.getInstance().getListParam(paramMap);
                    map = RoutineTasksStartService.getInstance().startEMSystem(request, sysName, version, iworkItemid,butterflyVersion,
                        Constants.IEAI_ROUTINE_TASKS,"dynamicParameter",  emParams);

                    // }

                    if (map.containsKey("valSucess"))
                    {
                        boolean flag = (Boolean) map.get("valSucess");
                        if (!flag)
                        {
                            map.put("success", false);
                            map.put("message", map.containsKey("valMessage") ? map.get("valMessage") : "");
                            return map;
                        }
                    }

                }else{
                    map=   RoutineTasksStartService.getInstance().startEMSystem(request, sysName,version,iworkItemid, butterflyVersion,Constants.IEAI_ROUTINE_TASKS,null,null);
                }
                //打印日志
                logger.info("用户 [" + startUserName + "] 启动 [" + sysName + "] 系统下的 [" + version + "] 预案。");
            }
        } catch (Exception e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, "启动失败");
            logger.error(sysName + ",启动失败,异常信息为:" + e,e);
        }

        return map;
    }
    
    /**
     * 
     * @Title: getInstanceBasicInfoList
     * @Description: 【应急操作】获取基础信息列表
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年5月27日  manxi_zhao
     */
    @RequestMapping("flowstart/getInstanceBasicInfoListForEM.do")
    @ResponseBody
    public Map getInstanceBasicInfoList ( RoutineTasksInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map res = new HashMap();
        try
        {

            if (instanceConfigBeanForQuery.getIidForQuery() > 0)
            {
                /** 
                //                res = InstanceConfigService.getInstance().getInstanceBasicInfoList(
                //                    instanceConfigBeanForQuery);
                */
                int type = Constants.IEAI_ROUTINE_TASKS;
                res = RoutineTasksInsConfigService.getInstance().getInstanceBasicInfoList(
                    instanceConfigBeanForQuery, type);
            }

        } catch (RepositoryException e)
        {
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }
        return res;
    }

    /**
     * 
     * @Title: getInstanceInsInfoList
     * @Description: 【应急操作】获取系统信息列表
     * @param instanceConfigBeanForQuery
     * @return
     * @throws RepositoryException
     * @return Map 返回类型
     * @throws
     * @变更记录 2016年5月27日  manxi_zhao
     */
    @RequestMapping("flowstart/getInstanceInsInfoListForEM.do")
    @ResponseBody
    public Map getInstanceInsInfoList ( RoutineTasksInstanceConfigBeanForQuery instanceConfigBeanForQuery )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map res = new HashMap();
        try
        {

            if (instanceConfigBeanForQuery.getIidForQuery() > 0)
            {
                int systemType = Constants.IEAI_ROUTINE_TASKS;
                res = RoutineTasksInsConfigService.getInstance().getInstanceInsInfoList(null,null,null,
                    instanceConfigBeanForQuery,systemType);
            }

        } catch (RepositoryException e)
        {
            logger.info(THE + method + METHOD_OF + this.getClass().getName() + IS_ERROR, e);
        }
        return res;
    }
    /**
     * 
     * <li>邮政发短信启动应急</li> 
     * <AUTHOR>
     * 2021年4月9日 
     * @param request
     * @return
     * @throws RepositoryException
     * return Map
     */
    @RequestMapping("sendToVerifyUser.do")
    @ResponseBody
    public Map sendToVerifyUser ( HttpServletRequest request )
            throws RepositoryException
    {
        Map map = new HashMap();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String auditorname = request.getParameter("auditorname");
        String phone = RoutineTasksInsConfigService.getInstance().getAuditorPhoneNum(auditorname);
        if(StringUtils.isBlank(phone)){
            map.put(Constants.STR_SUCCESS, true);
            map.put(Constants.STR_MESSAGE, "审核人没有设置电话，无法发送信息！");
            return map;
        }
        SimpleDateFormat formatter= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss ");
        String currentdate = formatter.format(new Date());
        SessionData sessionData = SessionData.getSessionData(request);
        String startUserName = sessionData.getUserName();
        String startUserPhone = RoutineTasksInsConfigService.getInstance().getStartUserPhone(startUserName);
        String planId = request.getParameter("planId");
        String noteWorkItemID = request.getParameter("noteWorkItemID");
        Map namesmap = RoutineTasksInsConfigService.getInstance().getNamebyPlanid(planId);
        String iinstancename = namesmap.get("iinstancename").toString();
        String iversionalias = namesmap.get("iversionalias").toString();
        String content = "auto+"+noteWorkItemID+"+47";
        String outtime=Environment.getInstance().getSysConfig("task.out.autoback.time", "30");
        String message = "例行任务任务启动执行审批，例行任务系统名称："+iinstancename+"，预案名称："+iversionalias+"，发起用户："+startUserName+"-"+startUserPhone+"，发起时间："+currentdate+"，确认审批通过请在"+outtime+"分钟内回复短信："+content+"（重要提示：回复审批信息后，预案立即启动）。";
        logger.info(message);
        boolean result = RoutineTasksInsConfigService.getInstance().sendToVerifyUser(phone,message,Long.parseLong(noteWorkItemID));
        if(result){
            map.put(Constants.STR_SUCCESS, true);
            map.put(Constants.STR_MESSAGE, "已发送短信给审核人");
        }else{
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, "发送短信失败");
        }
        return map;
    }
}
