package com.ideal.ieai.server.repository.hd.ic.supperhc.hcstart.immemodel;

public class HcStartImmeOfMonitorModel
{
    
    private Long pointid;
    
    
    private Long chkitemid;
    
    
    private Long computerid;
    
    
    private Long sysid;
    
    
    private Long scid;
    
    private Long  applogo;


    public Long getPointid ()
    {
        return pointid;
    }


    public void setPointid ( Long pointid )
    {
        this.pointid = pointid;
    }


    public Long getChkitemid ()
    {
        return chkitemid;
    }


    public void setChkitemid ( Long chkitemid )
    {
        this.chkitemid = chkitemid;
    }


    public Long getComputerid ()
    {
        return computerid;
    }


    public void setComputerid ( Long computerid )
    {
        this.computerid = computerid;
    }


    public Long getSysid ()
    {
        return sysid;
    }


    public void setSysid ( Long sysid )
    {
        this.sysid = sysid;
    }


    public Long getScid ()
    {
        return scid;
    }


    public void setScid ( Long scid )
    {
        this.scid = scid;
    }


    public Long getApplogo ()
    {
        return applogo;
    }


    public void setApplogo ( Long applogo )
    {
        this.applogo = applogo;
    }
    
    
    

}
