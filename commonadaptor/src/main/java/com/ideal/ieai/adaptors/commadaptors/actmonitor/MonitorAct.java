/*
 * Created on 2005-9-14
 */
package com.ideal.ieai.adaptors.commadaptors.actmonitor;  

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.MonitorActConfigCore;
import com.ideal.ieai.commons.MonitorActModel;
import com.ideal.ieai.commons.WorkflowFilter;
import com.ideal.ieai.commons.WorkflowInfo;
import com.ideal.ieai.core.IEAIRuntime;
import com.ideal.ieai.core.activity.ActStateData;
import com.ideal.ieai.core.activity.ActivityException;
import com.ideal.ieai.core.activity.ActivityValidateTimeConfig;
import com.ideal.ieai.core.activity.DefaultConfig;
import com.ideal.ieai.core.activity.IConfig;
import com.ideal.ieai.core.activity.IMonitorActivity;

/**
 * <li>Title: MonitorAct.java</li>
 * <li>Project: adaptor_common</li>
 * <li>Package: com.ideal.ieai.adaptors.commadaptors.actmonitor</li>
 * <li> Description: This class is monitor the other activity which belows some
 * workflows, or some workflow instances. Such as workflow where the monitor in,
 * and not in.
 * <li>
 * <li>Copyright: Copyright (c) 2006</li>
 * <li>Company: IdealTechnologies </li>
 * <li>Created on 2006-7-10, 13:28:28</li>
 * 
 * <AUTHOR>
 * @version iEAI v3.5
 */
public class MonitorAct implements IMonitorActivity {
    /**
     * Default Configure of the monitor activity. This parameter will be use to
     * tell monitor activity which activity to monite.
     */
    private DefaultConfig _defaultCfg;

    public void init(IConfig TheCfgs) {
        if(null==TheCfgs)
        {
            _defaultCfg=new DefaultConfig();
        }
        else
        {
            _defaultCfg = (DefaultConfig) TheCfgs;
        }
    }

    public int execute(ActStateData actStateData, Map inputs, Map outputs){
        if (null == _defaultCfg) {
            return Constants.END;
        }

        List actConfigList = getAllMonitorActConfigs();

        if (actConfigList == null || actConfigList.isEmpty()) {
            outputs.put("isTimeout", false);
            return Constants.END;
        }

        if (actStateData.getIntData() == -1) {
            actStateData.setLongData(System.currentTimeMillis());
            actStateData.setIntData(0);
        }

        List configs = getValidateMonitorActCfg();

        for (Iterator it = configs.iterator(); it.hasNext();) {
            MonitorConfig config = (MonitorConfig) it.next();

            // if the activity is alive ,the value of this parameter will be
            // true
            // else it will be false.
            boolean isDisappear = false;

            // if the state of the activity which was monitored by monitor
            // activity is in the monitor-state,
            // the value of this parameter will be true, else it will be false;
            boolean isCurState = false;

            int actId = config.getActId();
            if (config.isCurFlow()) {
                List needStates = config.getMonitorStates();
                List realStates = IEAIRuntime.current()
                        .getActivityStates(actId);
                isDisappear = IEAIRuntime.current().isDisappear(actId);
                isCurState = containsAnyOne(realStates, needStates);
            } else {
                long flowId = -1;
                WorkflowFilter filter = new WorkflowFilter(config.getPrjName(),
                        config.getFlowName(), config.getFlowInsName());
                List flowList = IEAIRuntime.current().queryFlow(filter);
                if (flowList.isEmpty()) {
                    configs.remove(config);
                    it = configs.iterator();
                    continue;
                }
                switch (config.getFlowInsType()) {
                    case MonitorActModel.INS_CUR:
                    case MonitorActModel.INS_SPE:
                        flowId = ((WorkflowInfo) flowList.get(0)).getId()
                                .longValue();
                        break;
                    case MonitorActModel.INS_PRE:
                        if (flowList.size() >= 1) {
                            flowId = ((WorkflowInfo) flowList.get(1)).getId().longValue();
                        }
                        break;
                    default:flowId = config.getFlowId();
                            if (!IEAIRuntime.current().isFlowRunning(flowId)) {
                                configs.remove(config);
                                it = configs.iterator();
                                continue;
                            }
                }
                List needStates = config.getMonitorStates();
                List realStates = IEAIRuntime.current().getActivityStates(
                        flowId, actId);
                isDisappear = IEAIRuntime.current().isDisappear(flowId, actId);
                isCurState = containsAnyOne(realStates, needStates);
            }

            if (isDisappear || isCurState) {
                configs.remove(config);
                it = configs.iterator();
            }
        }

        if (configs.isEmpty()) {
            outputs.put("isTimeout", false);
            return Constants.END;
        }

        String hour = (String) _defaultCfg.get(MonitorActConfigCore.HOUR);
        String minute = (String) _defaultCfg.get(MonitorActConfigCore.MINUTE);
        String second = (String) _defaultCfg.get(MonitorActConfigCore.SECOND);
        if ("".equals(hour)) {
            hour = "0";
        }
        if ("".equals(minute)) {
            minute = "0";
        }
        if ("".equals(second)) {
            second = "0";
        }
        int total = (Integer.parseInt(hour) * 60 * 60
                + Integer.parseInt(minute) * 60 + Integer.parseInt(second)) * 1000;

        if (total != 0) {
            long curMillSec = System.currentTimeMillis();
            if (curMillSec - actStateData.getLongData() > total) {
                outputs.put("isTimeout", true);
                return Constants.END;
            }
        }
        return Constants.REPEAT;
    }

    public List getInputDef() {
        return new ArrayList();
    }

    public List getOutputDef(){
        return Helper.getOutputParam(this._defaultCfg);
    }

    public void dispose() {
        // no code.
    }

    public boolean validate() {
        return true;
    }

    public int compensate(ActStateData actStateData, Map input, Map output)
            throws ActivityException {
        return 0;
    }

    public List getMonitorActivity ()
    {
        List actConfigList = getValidateMonitorActCfg();
        List actIdList = new ArrayList();
        if (null == actConfigList)
        {
            return new ArrayList();
        }
        for (Iterator it = actConfigList.iterator(); it.hasNext(); ) {
            MonitorConfig config = (MonitorConfig)it.next();
            actIdList.add(config.getActId());
        }
        return actIdList;
    }

    private List getValidateMonitorActCfg() {

        List actConfigList = getAllMonitorActConfigs();

        if (null == actConfigList || actConfigList.isEmpty()) {
            return null;
        }
        Date cureDate = new Date();
        List results = new ArrayList();
        for (Iterator it = actConfigList.iterator(); it.hasNext();) {
            MonitorActModel actModel = (MonitorActModel) it.next();
            MonitorConfig config = new MonitorConfig(actModel);
            if (null == config.getRules() | config.getRules().isEmpty()) {
                if (!results.contains(config)) {
                    results.add(config);
                } else {
                    int index = results.indexOf(config);
                    ((MonitorConfig) results.get(index)).getMonitorStates()
                            .addAll(config.getMonitorStates());
                }
                continue;
            } else {
                if (isValidate(cureDate, config.getRules())) {
                    if (!results.contains(config)) {
                        results.add(config);
                    } else {
                        int index = results.indexOf(config);
                        ((MonitorConfig) results.get(index)).getMonitorStates()
                                .addAll(config.getMonitorStates());
                    }
                }
            }
        }
        return results;
    }

    /**
     * This method is getting the all of configs of monitor act.
     * 
     * @return
     */
    private List getAllMonitorActConfigs() {
        return (List) _defaultCfg.get(MonitorActConfigCore.MONITOR_ACTS);
    }

    /**
     * This method is check the date of the activity which was monited. If the
     * date of the activity is allow to run, it will be return true.
     * 
     * @param time
     * @param rules
     * @return boolean
     */
    private boolean isValidate(Date time, List rules) {
        if (null == rules | rules.isEmpty()) {
            return true;
        }
        for (Iterator it = rules.iterator(); it.hasNext();) {
            ActivityValidateTimeConfig rule = (ActivityValidateTimeConfig) it
                    .next();
            if (IEAIRuntime.current().isValidTime(rule, time)) {
                return true;
            }
        }
        return false;
    }

    /**
     * This method is check the state of activity which is being monitored. If the
     * state of activity is allow to run, it will be return true.
     * 
     * @param realStates: All states the activity has gone through so far
     * @param needStates: states to be monitored on
     * @return boolean
     */
    private boolean containsAnyOne(final List realStates, final List needStates) {
        if ( null == realStates || null == needStates )
            return false;
        for (int j = 0, size = needStates.size(); j < size; j++) {
            if (realStates.contains(needStates.get(j))) {
                return true;
            }
        }
        return false;
    }
}
