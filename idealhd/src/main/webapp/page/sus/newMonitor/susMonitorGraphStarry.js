var personExcute_window;

  destroyRubbish();
  

contentPanel.setAutoScroll(false);
  var globalProjectInstanceId = 0;//刷新前的业务系统
  var globalConcurrentStep = 0;
  var globalStepNew = 0;//定位刷新前的步骤
  var globalPakgNew = 0;//定位刷新前的包名
  
  var autoRefreshTime=null;
  var autoRefreshCheck=null;
  var R_TIME = 30;
  var refreshButton_new=null;
  var pauseButton=null;
  var continueButton=null;
  var failureButton=null;
  var manualButton=null;
  var actObjectGlobal=null;
  Ext.onReady(function() {
	  	// 刷新时间输入框
	    autoRefreshTime = Ext.create ('Ext.form.NumberField',
	    {
	    	renderTo : "refresh_bottom",
	        fieldLabel : '刷新时间（秒）',
	        margin : '5',
	        labelWidth : 110,
	        width : 170,
	        value : '30',
	        allowDecimals : false,
	        minValue : R_TIME,
	        listeners :
	        {
	            blur : function ()
	            {
		            refreshTime = this.getValue ();
		            refreshTime = (refreshTime == '' || refreshTime == null) ? R_TIME : refreshTime;
		            try
		            {
			            refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
			            this.setValue (refreshTime);
		            }
		            catch (e)
		            {
			            refreshTime = R_TIME;
			            this.setValue (refreshTime);
		            }
		            if (autoRefreshCheck.checked)
		            {
			            clearInterval (refreshObj);
			            refreshObj = setInterval (refreshProjectList, refreshTime * 1000);
		            }
	            }
	        }
	    });
	    // 是否自动刷新复选框
	    autoRefreshCheck = Ext.create ('Ext.form.Checkbox',
	    {
	        fieldLabel : '自动刷新',
	        margin : '5',
	        labelWidth : 70,
	        width : 90,
	        checked : true,
	        listeners :
	        {
	            change : function ()
	            {
		            if (this.checked)
		            {
			            refreshTime = autoRefreshTime.getValue ();
			            refreshTime = refreshTime == '' ? R_TIME : refreshTime;
			            try
			            {
				            refreshTime = refreshTime < R_TIME ? R_TIME : refreshTime;
				            autoRefreshTime.setValue (refreshTime);
			            }
			            catch (e)
			            {
				            refreshTime = R_TIME;
				            autoRefreshTime.setValue (refreshTime);
			            }
			            clearInterval (refreshObj);
			            refreshObj = setInterval (refreshProjectList, refreshTime * 1000);
		            }
		            else
		            {
			            clearInterval (refreshObj);
		            }
	            }
	        }
	    });
	    //首次加载定时刷新
	    if (autoRefreshCheck.checked == true)
	    {
	    	clearInterval (refreshObj);
	        refreshObj = setInterval (refreshProjectList, autoRefreshTime.getValue () * 1000);
	    }
	    /** 刷新按钮* */
	    refreshButton_new = Ext.create ("Ext.Button",
		{
			 text : '刷新',
	         cls : 'Common_Btn',
	         listeners: {
	             "click": function() {
	            	 reloadGroup_new();
	             }
	         }
		});
		
		pauseButton = Ext.create ("Ext.Button",
				{
	        text : '暂停',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	                var data = getCHKBoxIds();
	                if (data.length == 0) {
	                  Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                  return;
	                } else {
	                  Ext.Msg.confirm("请确认", "是否真的要进行<暂停>操作？", function(button, text) {
	                    if (button == "yes") {
	                      Ext.Ajax.request({
	                        url : 'rmPause.do',
	                        params : {
	                          insIds : data
	                        },
	                        method : 'POST',
	                        success : function(response, opts) {
	                          var success = Ext.decode(response.responseText).success;
	                          Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          // 当后台数据同步成功时
	                          if (success) {
//	                            reload();
	                        	  reloadGroup_new();
	                          }
	                        }
	                      });
	                    }
	                  });
	                }
	            }
	        }
	      
		});
		continueButton = Ext.create ("Ext.Button",
				{
	        text : '继续',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	                var data = getCHKBoxIds();
	                  if (data.length == 0) {
	                       Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                       return;
	                  } else {
	                    Ext.Msg.confirm("请确认", "是否真的要进行<继续>操作？", function(button, text) {
	                      if (button == "yes") {
	                        Ext.Ajax.request({
	                          url : 'rmResume.do',
	                          params : {
	                            insIds : data
	                          },
	                          method : 'POST',
	                          success : function(response, opts) {
	                            var success = Ext.decode(response.responseText).success;
	                            Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                            // 当后台数据同步成功时
	                            if (success) {
//	                            	 reload();
	                            	reloadGroup_new();
	                            }
	                          }
	                        });
	                      }
	                    });
	                  }
	            }}});
		
		failureButton = Ext.create ("Ext.Button",
				{
	        text : '部署失败',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	                var data = getCHKBoxIds();
	                  if (data.length == 0) {
	                       Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                       return;
	                  } else {
	                    Ext.Msg.confirm("请确认", "是否真的要进行<部署失败>操作，部署失败后整个部署将会被停止？", function(button, text) {
	                      if (button == "yes") {
	                        Ext.Ajax.request({
	                          url : 'rmKill.do?flag=4',
	                          params : {
	                            insIds : data,
	                            type : 'sus'
	                          },
	                          method : 'POST',
	                          success : function(response, opts) {
	                            var success = Ext.decode(response.responseText).success;
	                            // 当后台数据同步成功时
	                            //Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                            if (success) {
	                            	globalProjectInstanceId=0;
	                            	globalStepNew=0;
	                            	globalPakgNew=0;
	                        		setTimeout(reloadGroup_new, 1000);
	                            }else
	                            {
	                          	  Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          	}
	                          }
	                        });
	                      }
	                    });
	                  }
	            }}});
		manualButton= Ext.create ("Ext.Button",
				{
	        text : '手工部署',
	        textAlign : 'center',
	        cls : 'Common_Btn',
	        listeners: {
	            "click": function() {

	            	var data = getCHKBoxIds();
	                if (data.length == 0) {
	                    Ext.Msg.alert('提示', '请先选择您要操作的记录!');
	                    return;
	                } else {
	                  Ext.Msg.confirm("请确认", "是否真的要进行<手工部署>操作？", function(button, text) {
	                    if (button == "yes") {
	                      Ext.Ajax.request({
	                        url : 'rmKill.do?flag=2',
	                        params : {
	                          insIds : data,
	                          type : 'sus'
	                        },
	                        method : 'POST',
	                        success : function(response, opts) {
	                          var success = Ext.decode(response.responseText).success;
	                          // 当后台数据同步成功时
	                          //Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          if (success) {
	                        	  globalProjectInstanceId=0;
	                        	  globalStepNew=0;
	                        	  globalPakgNew=0;
	                        		setTimeout(reloadGroup_new, 1000);
	                          }
	                          else
	                          {
	                        	  Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
	                          }
	                        }
	                      });
	                    }
	                  });
	                }
	              
	            }}});
});
  //TODO Ext结束  
  
  

  
  
  
  var templates = {
      'projects': ''+
        '<ul>' +
          '<% _.each(projects, function (project) { %>' +
            '<li class="DR_Leftmenu_btn kl_list_default" data-instance-id="<%= project.iid %>"  data-pack-flag="0" title="实例名：<%= project.iruninsname %>">' +
              '<span class="kl_list_text kl_check_box " id="checkboxForLeftSpan<%= project.iid %>"><input type="checkbox" class="kl_selectAllChild" name="checkboxForLeft" value="<%= project.iid %>" onclick="testClickBigCheckBox(this,<%= project.iid %>);"/></span>' +
              '<span class="kl_list_name"><%= project.isysname %></span>'+
              '<% if (project.stateColor) { %>' +
              '<span class="kl_list_<%= project.stateColor %> kl_list_status"></span>' +
            '<% } %>' +
            
              '</li>' +
              '<div class="kl_list_secondary">'+
              '<% _.each(project.packetNameItem, function (packetNameItem) { %>' +
              '<li  pakg-id="<%= project.iid %>_<%=packetNameItem%>" data-parent-id="<%= project.iid %>"  data-pack-flag="1" data-instance-pkgname="<%=packetNameItem%>" style="display:none">' +
              '<span title="包:<%=packetNameItem%>&nbsp;&nbsp;&nbsp;&nbsp;变更单号:【<%= project.iversion %>】" pkgspanid="<%= project.iid %><%=packetNameItem%>">&nbsp;&nbsp;&nbsp;&nbsp;<%=packetNameItem%></br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;【<%= project.iversion %>】</span>' +
              '</li>' +
              '<% }); %>' +
              '</div>'+
          '<% }); %>' +
        '</ul>',
        
	    'pakages': ''+
	    '<ul>' +
	      '<% _.each(pakages, function (pkg) { %>' +
	        '<li class="DR_Leftmenu_btn kl_list_default" data-instance-id="<%= pkg.iruninsid %>" data-instance-pkgname="<%= pkg.ipkgname %>" >' +
	          '<span class="kl_list_text">&nbsp;&nbsp;<%= pkg.ipkgname %></span>' +
	        '</li>' +
	      '<% }); %>' +
	    '</ul>',
        
	    
      'runInstanceConcurrent': '' +
        '<div class="kl_sys_stage_cn" data-instance-id="<%= instanceId %>">' +
          '<div class="Disaster_Recovery_List2">' +'</div>' +
          
            //'<div class="Package_start Package">开始</div>'+
			//循环步骤
			'<% $.each(runInstanceConcurrents, function (index, runInstanceConcurrent) { %>' +
			'<%if( index>0 ){%><div class="klstage_arrow_show"></div><%}%>' +  //箭头
			'<div data-step5="<%= cap(runInstanceConcurrent.stateColor ) %>"  data-step4="<%= instanceId %>_<%= runInstanceConcurrent.conner %>" class="klstage_<%= cap(runInstanceConcurrent.stateColor ) %> klstage_color   concurrent-step" data-step="<%= runInstanceConcurrent.conner %>" data-step2="<%= runInstanceConcurrent.ichildinstanceid %>" data-step3="<%= runInstanceConcurrent.pkgName %>">' +
				'<div class="klstage_Font">' +
					'<span>(<%= runInstanceConcurrent.conner %>) 服务器 <%= runInstanceConcurrent.serverCount %> 已完成<%= runInstanceConcurrent.finishCount %></span> <span><%= runInstanceConcurrent.actNamesSub %></span>' +
				'</div>' +
				'<div class="kl_list_<%= runInstanceConcurrent.stateColor %> stage_list_status02">'+
				'</div>'+
			'</div>' +
			 '<% }); %>' +
			//循环步骤结束
			'<div class="Package_arrow"></div>' +
			//'<div class="Package_end Package">结束</div>' +
          //滚动按钮
		  //'<div class="Package_Page">'+
				//'<img src="images/Package_Pre.png" onclick="scrollUp(\'div\'+<%= 1 %>)" />'+
				//'<img src="images/Package_Next.png"'+'onclick="scrollDown(\'div\'+<%= 1 %>)" />'+
		  //'</div>'+
			
          '<div class="Disaster_Recovery_List3">' +
          '</div>' +
        '</div>'
        
        
            
  };
  
  function template(templateName, data) {
    var html = templates[templateName];
    return data ? _.template(html, data) : html;
  }
  
  
  
  //监听(一)
  // 业务系统 中的 li 被单机
  $('body').off('click', '.kl_sys_part li').on('click', '.kl_sys_part li', function(e){
    var $this = $(this);
    var $allLi = $('.kl_sys_part li[data-pack-flag="0"]'); 	// 全部的业务系统  列表
    var instanceId = $this.data('instance-id');
    if($this.data('pack-flag')=='1')
    	{
    	instanceId = $this.data('parent-id');
    	var pkgname=$this.data('instance-pkgname');
    	 loadStep(instanceId,pkgname); 	
    	 globalPakgNew=instanceId+"_"+pkgname;
    	 $('span[pkgspanid!=""]').css('color',''); 
    	$('span[pkgspanid="'+instanceId+pkgname+'"]').css('color','#01bc3f'); 
    	return;
    	}
    var target0 = $('.kl_sys_part li[data-pack-flag="1"]');
    target0.hide();
    var target = $('.kl_sys_part li[data-parent-id="'+instanceId+'"]');
//    alert(target);
    target.show();
    if(globalProjectInstanceId!=instanceId) {
      globalProjectInstanceId = instanceId;
      globalConcurrentStep = 0;
    }
    $allLi.removeClass('DR_Leftmenu_over_btn').removeClass('kl_list_over').addClass('DR_Leftmenu_btn').addClass('kl_list_default');
    $this.removeClass('DR_Leftmenu_btn').removeClass('kl_list_default').addClass('DR_Leftmenu_over_btn').addClass('kl_list_over');
    
    $('.kl_sys_stage').html("");
    var fisttarget = $('.kl_sys_part li[data-parent-id="'+instanceId+'"][pakg-id="'+globalPakgNew+'"]:first');
    if(fisttarget.length) {
    	fisttarget.trigger('click');
    }
    else
    	{
    	fisttarget = $('.kl_sys_part li[data-parent-id="'+instanceId+'"]:first');
    	 fisttarget.trigger('click');
    	}
    //alert(fisttarget.data('instance-pkgname'));
   
    //$('.kl_sys_part li[data-parent-id="'+instanceId+'"]:first-child').trigger('click');
   // loadPkgs(instanceId);

  });
  
  function loadPkgs(instanceId){
	    $.getJSON('getSUSMonitorInstancePkgnames.do', {instanceId:instanceId}, function(res){
	      if(res.length>0) {
	        $('.packgepackge').show();
	        $('.Disaster_Recovery_Packge').html(template("pakages", {'pakages':res, 'instanceId':instanceId}));
	        $(".Disaster_Recovery_Packge").niceScroll({cursorborder: "1px solid #9fa0a0"});
	        if(globalConcurrentStep!=0){
	          var target = $('.concurrent-step[data-step="'+globalConcurrentStep+'"]');
	          if(target.length) {
	            target.trigger('click');
	            if(target.position().top-60<0){
	            	$(".Disaster_Recovery_Packge").getNiceScroll(0).doScrollTop(-60, 100)
	            }else if((target.position().top-60)>$(".Disaster_Recovery_Packge").height()){
	            	$(".Disaster_Recovery_Packge").getNiceScroll(0).doScrollTop(target.position().top-60, 100);
	            }
	          }
	        }
	      } else {
	        $('.Disaster_Recovery_Packge').html("");
	      }
	    });
}
  
  
  //监听(二) 单击  包 信息
  $('body').off('click', '.Disaster_Recovery_Packge li').on('click', '.Disaster_Recovery_Packge li', function(e){
	  var $this = $(this);
	    var $allLi = $('.Disaster_Recovery_Packge li'); 	// 全部的包 列表
	    var instanceId = $this.data('instance-id');
	    var pkgname = $this.data('instance-pkgname'); 
	    loadStep(instanceId,pkgname); 				//加载补丁包信息
	  });
  
  	  /**
  	   * @desc 加载步骤
  	   * */
	  function loadStep(instanceId,pkgname){
		    //加载步骤
		    $.getJSON('getSUSInstanceConcurrentNew.do', {instanceId:instanceId,pkgname:pkgname}, function(res){
		    	for(var i=0;i<res.length;i++){
		    		if(res[i].stateColor=='blue'){
		    			globalConcurrentStep=res[i].conner;
		    		}
		    	}
		      if(res.length>0) {
		        $('.twotwo').show();
		        $('.kl_sys_stage').html(template("runInstanceConcurrent", {'runInstanceConcurrents':res, 'instanceId':instanceId}));
		        $('.kl_sys_stage').mouseenter (function(){
   				 windowAddMouseWheel($(this).attr('id'));});
		        //$(".kl_sys_stage").niceScroll({cursorborder: "1px solid #9fa0a0"});
		        if(globalStepNew!=0){
			          var target = $('.concurrent-step[data-step4="'+globalStepNew+'"]');
			          if(target.length) {
				            target.trigger('click');
				            if(target.position().top-60<0){
				            	$(".kl_sys_stage").scrollTop(-60, 100)
				            }else if((target.position().top-60)>$(".kl_sys_stage").height()){
				            	$(".kl_sys_stage").scrollTop (target.position().top-60, 100);
				            }else
				            	{
				            	$(".kl_sys_stage").scrollTop (target.position().top-60, 100);
				            	}
				            }else
				            	{
				            	//默认第一个步骤被点击
					        	 $('.concurrent-step:first').trigger('click');
				            	}
		        }else if(globalConcurrentStep!=0){
		          var target = $('.concurrent-step[data-step="'+globalConcurrentStep+'"]');
		          if(target.length) {
		            target.trigger('click');
		            if(target.position().top-60<0){
		            	$(".kl_sys_stage").getNiceScroll(0).doScrollTop(-60, 100)
		            }else if((target.position().top-60)>$(".kl_sys_stage").height()){
		            	$(".kl_sys_stage").getNiceScroll(0).doScrollTop(target.position().top-60, 100);
		            }
		          }
		        }else
		        	{
		        	//默认第一个步骤被点击
		        	 $('.concurrent-step:first').trigger('click');
		        	}
		      } else {
		        $('.kl_sys_stage').html("");
		      }
		    });
	  }
  
  
	  //监听(三) 步骤被单击了，弹出详情页面
	  $('body').off('click', '.concurrent-step').on('click', '.concurrent-step', function(e){
		  var $this = $(e.currentTarget);
		     var step = $this.attr("data-step");
		     var step2 = $this.attr("data-step2");
		     var step3 = $this.attr("data-step3");
		     var stepTemp=step;
		     var projectInstanceIdTemp=globalProjectInstanceId;
		     globalStepNew=$this.attr("data-step4");
		     if(step2>0)
		    	 {
		    	 stepTemp=1;
		    	 projectInstanceIdTemp=step2;
		    	 }
		   //清除掉步骤上的所有颜色
		     $('.klstage_click_green').removeClass('klstage_click_green'); 
		      $('.klstage_click_yellow').removeClass('klstage_click_yellow'); 
		      $('.klstage_click_blue').removeClass('klstage_click_blue'); 
		      $('.klstage_click_red').removeClass('klstage_click_red'); 
		      //选中的步骤上色
		     var step5 = $this.attr("data-step5");
		     if(step5=='Green')
		    	 {
		    	  $this.addClass('klstage_click_green');
		    	 }else if(step5=='Yellow')
		    	 {
			    	  $this.addClass('klstage_click_yellow');
			    	 }
		    	 else if(step5=='Blue')
		    	 {
			    	  $this.addClass('klstage_click_blue');
			    	 }
		    	 else if(step5=='Red')
		    	 {
			    	  $this.addClass('klstage_click_red');
			    	 }
		   
		   
			    
		     var instanceId = $this.closest(".monitorGraph").attr("data-instance-id");
		     $('#sss').load("initGraphForSUSwindowNew.do",{insId : projectInstanceIdTemp, step : stepTemp,pkgName:step3},function(){});    
		     //		     winStep = Ext.create('Ext.window.Window', {
//		 		    title : '步骤详情',
//		 		    modal : true,
//		 		    closeAction : 'destroy',
//		 		    constrain : true,
//		 		    autoScroll : true,
//		 		    width : contentPanel.getWidth (),
//		 		    height : contentPanel.getHeight (),
//		 		    minWidth: 350,
//		 		    draggable : false,// 禁止拖动
//		 		    resizable : false,// 禁止缩放
//		 		    layout : 'fit',
//		 		   listeners:{"beforedestroy":function(obj) 
//		               {   
////		 			  globalProjectInstanceId=0;
//		 			  reload();
//		               }
//		               } ,
//		 		    loader :
//		 		    {
//		 		        url : 'initGraphForSUSwindow.do',
//		 		       params: {
//		 			         insId : projectInstanceIdTemp,
//		 			         step : stepTemp,
//		 			        pkgName:step3
//		 			       },
//		 		        autoLoad : true,
//		 		        scripts : true
//		 		    }
//		 		}).show();

	  });
  
  
  
 /**	
    @Div结构
 		kl_sys_part  业务系统     oneone   
		kl_sys_stage   步骤		  twotwo
		Disaster_Recovery_Two	详情		  threethree
  */
  

  /**  
   * TODO 加载数据
   * @desc 
   * 		1.数据展示:
   * 				 先让 业务系统的 中的 'scroll层'的div进行显示。使用的的是 'oneone这个类型选择器'。
   * 		2.数据填充：
   * 				 只对 '业务系统' 块进行展示填充。 填充时，先project进行填充
   * 		3.事件触发:
   *				 业务系统列出低一级列表,每一个业务系统展示一条数据。默认单击第一条数据。 				
   * */
  function refreshProjectList() {
	  //清空页面元素
	  resetElem();
    $.getJSON('getSUSSysInstanceList.do?sysType=3', function(res){
      if(res.length>0) {
        $('.oneone').show();
        $('.kl_sys_part').html(template("projects", {'projects':res})); //业务系统填充
        $('.kl_sys_part').mouseenter (function(){
				 windowAddMouseWheel($(this).attr('id'));});
        
        //触发单击事件,默认单击第一个业务系统
        if(globalProjectInstanceId==0) {
          $('.kl_sys_part li[data-pack-flag="0"]:first-child').trigger('click');
        } else {
          var target = $('.kl_sys_part li[data-instance-id="'+globalProjectInstanceId+'"]');
          if(target.length) {
            target.trigger('click');
          } else {
            $('.kl_sys_part li:first-child').trigger('click');
          }
        }
        
        //$(".kl_sys_part").niceScroll({cursorborder: "1px solid #9fa0a0"});
        
      } else {
        $('.kl_sys_part').html("");
      }
    });
  }

  refreshProjectList();
  //var timer = setInterval(refreshProjectList,60000);
  
  //当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts) {
	  contentPanel.setAutoScroll(false);
    //clearInterval(timer);
  });
  
  contentPanel.on('resize', function() {
  //  alert(contentPanel.getHeight());
//    
    
  });
  
  function reloadGroup_new(){
	 
	  //清除定时刷新，重新设置
	  clearInterval (refreshObj);
      refreshObj = setInterval (refreshProjectList, autoRefreshTime.getValue () * 1000);
    refreshProjectList();
  }
  
  
  function cap(colorString)
  {
  	if(colorString=='orange')
  		{
  		colorString='yellow';
  		}
  	return colorString.replace(/(\w)/,function(v){return v.toUpperCase()});
  }
  /**
   * 点击包名链接，将该包下的所有步骤都略过
   */
  function skipoverAll (pkgName)
  {
  	Ext.Msg.confirm ("请确认", "是否真的要将该包下的所有步骤进行<略过>操作？", function (button, text)
  	{
  		if (button == "yes")
  		{
  			Ext.Ajax.request (
  			{
  			    url : 'skipStepAll.do',
  			    params :
  			    {
  			        runInsId : globalProjectInstanceId,
  			        pageName : pkgName
  			    },
  			    method : 'POST',
  			    success : function (response, opts)
  			    {
  				    var success = Ext.decode (response.responseText).success;
  				    if (!success)
  				    {
  					    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message);
  				    }
  				  refreshButton_new.fireEvent ('click');
  			    }
  			});
  		}
  	});
  }
  /**
   * 获得左侧树选中的复选框id
   * @returns {String}
   */
  function getCHKBoxIds() {
  	var ids = "";
  	var ck = document.getElementsByName("checkboxForLeft");
  	var num = 0;
  	for (i = 0; i < ck.length; i++) {
  		if (ck[i].checked) {
  			if (num == 0) {
  				ids = ck[i].value;
  			} else {
  				ids = ids + "," + ck[i].value;
  			}
  			num = num + 1;
  		}
  	}
  	return ids;
  }
  /**
   * 页面刷新复选框选中
   * 
   * @param idsIn
   */
  function cHKBoxIdsSetChecked (idsIn)
  {
  	var ck = document.getElementsByName ("checkboxForLeft");
  	for (i = 0; i < ck.length; i++)
  	{
  		if (idsIn.indexOf (ck[i].value) > -1)
  		{
  			ck[i].checked = true;
  		}
  	}
  }
  /**
   * 根据全局map中的数据将滚动条位置重新定位
   */
  function scrollReset (divId)
  {
  	//遍历map
  	for(var prop in scrollMap){
  	    if(scrollMap.hasOwnProperty(prop)){
  	    	//正在运行的包不重新定位滚动条
  	    	if(divId!=prop)
  	    		{
  	    		if($('#' + prop)!=null)
  	    		{
  	    		$('#' + prop).scrollTop(scrollMap[prop]);
  	    		}
  	    		}
  	    	
  	    }
  	}
  }
  /**
   * 获取页面滚动条坐标并保存至全局map
   */
  function getScrollPosition ()
  {
  	scrollMap = {};
  	for (i = 1; i < packageCount+1; i++)
  	{
  		scrollMap['div'+i] = $ ('#div' + i).scrollTop();
  	}  
  	
  }
  //向上滚动
  function scrollUp (divId)
  {
//      var scrollTopNum = document.getElementById (divId).scrollTop;
  	 isFinish = false;
  	var scrollTopNum =$ ('#' + divId).scrollTop();
      if (scrollTopNum > 0)
      {
  	    $ ('#' + divId).animate (
  	    {
  		    scrollTop : scrollTopNum - 200
  	    }, "fast");
      }
      setTimeout(function(){isFinish = true;}, 500);    
  }
  //向下滚动
  function scrollDown (divId)
  {
//      var scrollTopNum = document.getElementById (divId).scrollTop;
  	 isFinish = false;
  	var scrollTopNum =$ ('#' + divId).scrollTop();
      //document.getElementById(divId).scrollTop=scrollTopNum+70;
      $ ('#' + divId).animate (
      {
  	    scrollTop : scrollTopNum + 200
      }, "fast");
      setTimeout(function(){isFinish = true;}, 500);    
  }
  //向右滚动
  function scrollRight (divId)
  {
      
//      var scrollLeftNum = document.getElementById (divId).scrollLeft;
      var scrollLeftNum =$ ('#' + divId).scrollLeft();
      $ ('#' + divId).animate (
      {
  	    scrollLeft : scrollLeftNum + 300
      }, 300);
  }
  //向左滚动
  function scrollLeftF (divId)
  {
      
//      var scrollLeftNum = document.getElementById (divId).scrollLeft;
      var scrollLeftNum =$ ('#' + divId).scrollLeft();
      $ ('#' + divId).animate (
      {
  	    scrollLeft : scrollLeftNum - 300
      }, 300);
  }
  
  var isFinish = true;
  var divIdGlobal = null;
  function windowAddMouseWheel(divId) {
  	divIdGlobal=divId;
      var scrollFunc = function (e) {
    	  if(isFinish){
    		e = e || window.event;
          if (e.wheelDelta) {  //判断浏览器IE，谷歌滑轮事件
              if (e.wheelDelta > 0) { //当滑轮向上滚动时
              	scrollUp(divIdGlobal);
              }
              if (e.wheelDelta < 0) { //当滑轮向下滚动时
              	scrollDown(divIdGlobal);
              }
          } else if (e.detail) {  //Firefox滑轮事件
              if (e.detail> 0) { //当滑轮向上滚动时
              	scrollUp(divIdGlobal);
              }
              if (e.detail< 0) { //当滑轮向下滚动时
              	scrollDown(divIdGlobal);
              }
          }  
    	  }
          
      };
      //给页面绑定滑轮滚动事件
      if (document.addEventListener) {
          document.removeEventListener('DOMMouseScroll', scrollFunc, false);
          document.addEventListener('DOMMouseScroll', scrollFunc, false);
      }
      //滚动滑轮触发scrollFunc方法
      window.onmousewheel = document.onmousewheel = scrollFunc;
  }
  
function reload(){
	refreshProjectList();
  }
//点击步骤详情触发事件
function backFun1(insId,step,state ,stepName,insName,flowId,sysName,stepId,singleRollback,redoable,ip,shellPath,actType)
{
	if(actObjectGlobal==null)
		{
		actObjectGlobal=new Object();
		}
	actObjectGlobal.state=state;
	actObjectGlobal.stepName=stepName;
	actObjectGlobal.insName=insName;
	actObjectGlobal.flowId=flowId;
	actObjectGlobal.sysName=sysName;
	actObjectGlobal.stepId=stepId;
	actObjectGlobal.singleRollback=singleRollback;
	actObjectGlobal.redoable=redoable;
	actObjectGlobal.ip=ip;
	actObjectGlobal.shellPath=shellPath;
	actObjectGlobal.actType=actType;
	actObjectGlobal.insId=insId;
	actObjectGlobal.step=step;
	//alert(stepName);alert(insName);alert(flowId);
    if (state == '异常') {
    	$('#rengongchuliid').html("代理日志输出监控");
    	 $('#sss2').load("initShellinfoerroperSUSNew.do",{state:state ,stepName:stepName,insName:insName,flowId:flowId,sysName:sysName,stepId:stepId,singleRollback:singleRollback,redoable:redoable,ip:ip,shellPath:shellPath},function(){});   
    	//按钮全部置为灰
 		setButtonClass('shougongbushu_tool',1);
 		setButtonClass('chongshi_tool',1);
 		setButtonClass('lueguo_tool',1);
 		setButtonClass('ut_tool',0);
 		//setButtonClass('zhongzhibushu_tool',1);
    }else  if (state == '运行中') 
    	{
    	//按钮全部置为灰
		setButtonClass('shougongbushu_tool',1);
		setButtonClass('chongshi_tool',0);
		setButtonClass('lueguo_tool',0);
		setButtonClass('ut_tool',0);
		//setButtonClass('zhongzhibushu_tool',1);
    	if (actType == 2) { // 人工
			//detailFunc = "openUTWindow(" + flowId + ","+stepId+")";
    		$('#rengongchuliid').html("人工处理");
			$('#sss2').load("switchUT_SUSNew.do",{iflowid : flowId, stepId:stepId},function(){});  
			setButtonClass('ut_tool',1);
		}else
			{
			$('#rengongchuliid').html("代理日志输出监控");
	    	$('#sss2').load("initShellinforuningNew.do",{flowId : flowId, stepName : stepName,insName:insName,ip:ip,shellPath:shellPath},function(){});   
			}
    	
    	} else if (state == '已完成' || state == '异常略过') {
    		//按钮全部置为灰
    		setButtonClass('shougongbushu_tool',0);
    		setButtonClass('chongshi_tool',0);
    		setButtonClass('lueguo_tool',0);
    		setButtonClass('ut_tool',0);
    		//setButtonClass('zhongzhibushu_tool',0);
    		if (actType == 2) { // 人工
    			//detailFunc = "openUTWindow(" + flowId + ","+stepId+")";
        		$('#rengongchuliid').html("人工处理");
    			$('#sss2').load("switchUT_SUSNew.do",{iflowid : flowId, stepId:stepId},function(){});   
    		}else
    			{
    			$('#rengongchuliid').html("代理日志输出监控");
        		$('#sss2').load("initShellinfofinishLinkNew.do",{flowId : flowId, stepName : stepName,insName:insName,stepId:stepId,ip:ip,shellPath:shellPath},function(){});   
    			}
    		
    	}else   if (state == '未开始') 
    		{
    		$('#rengongchuliid').html("无输出");
    		$('#sss2').html("");
    		//按钮全部置为灰
    		setButtonClass('shougongbushu_tool',0);
    		setButtonClass('chongshi_tool',0);
    		setButtonClass('lueguo_tool',1);
    		setButtonClass('ut_tool',0);
    		//setButtonClass('zhongzhibushu_tool',0);
    		}else
    		{
        		$('#rengongchuliid').html("无输出");
        		$('#sss2').html("");
        		actObjectGlobal=null;
        		//按钮全部置为灰
        		setButtonClass('shougongbushu_tool',0);
        		setButtonClass('chongshi_tool',0);
        		setButtonClass('lueguo_tool',0);
        		setButtonClass('ut_tool',0);
        		//setButtonClass('zhongzhibushu_tool',0);
        		}
   
}
//复选框勾选变色
function testClickBigCheckBox(thisobj,prjid){
	if(thisobj.checked)
		{
		$('#checkboxForLeftSpan'+prjid).removeClass('kl_on_check').addClass('kl_on_check');
		}
	else
		{
		$('#checkboxForLeftSpan'+prjid).removeClass('kl_on_check');
		}
}
//设置步骤4个操作的按钮样式
function setButtonClass(thisid,flag)
{
	if(flag==1)
		{
		$('#'+thisid).removeClass('public_btn_gray').addClass('public_btn');
		}else
			{
			$('#'+thisid).removeClass('public_btn').addClass('public_btn_gray');
			}
	
}
//步骤的4个操作
function toolclick(thisid){
	
	var value =$('#'+thisid).attr("class");
	if(value=='public_btn_gray')
		{
		return;
		}else
			{
//			alert(actObjectGlobal.state);
//			alert(actObjectGlobal.stepName);
//			alert(actObjectGlobal.insName);
//			alert(actObjectGlobal.flowId);
//			alert(actObjectGlobal.sysName);
//			alert(actObjectGlobal.stepId);
//			alert(actObjectGlobal.singleRollback);
//			alert(actObjectGlobal.redoable);
//			alert(actObjectGlobal.ip);
//			alert(actObjectGlobal.shellPath);
//			alert(actObjectGlobal.actStepState);
//			return;
			if('chongshi_tool'==thisid)
				{
				//重试
				errorOper_act(1);
				}else if('lueguo_tool'==thisid)
				{
					if (actObjectGlobal.state == '未开始') 
						{
						skipStep(actObjectGlobal.flowId,actObjectGlobal.stepId);
						}else
							{
							//略过
							errorOper_act(2);
							}
					} 
				else if('shougongbushu_tool'==thisid)
				{
					//手工部署
					DeploymentOK_act(actObjectGlobal.flowId,actObjectGlobal.stepId);
					} 
				else if('ut_tool'==thisid)
				{
					//人工提醒处理
					utconfirmFun();
					} 
//				else if('zhongzhibushu_tool'==thisid)
//				{
//					//终止部署
//					DeploymentFail_act(actObjectGlobal.flowId,actObjectGlobal.stepId);
//					} 
			}
	   
}


/* 普通活动执行情况信息 */
function skipStep(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<略过>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'rmSkipStep.do',
				params : {
					flowId :flowId,
					stepId : stepId,
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}

/**步骤重试略过**/
function errorOper_act(execModel) {
	Ext.Ajax.request({
		url : 'exceptionOperSUS.do',
		params : {
			flowId : actObjectGlobal.flowId,
			stepId : actObjectGlobal.stepId,
			execModel : execModel,
			actId : '1',
			actName : 'ShellCmd',
			sts : actObjectGlobal.state,
			actStepState : actObjectGlobal.actStepState,
			systemType:'3'

		},
		success : function(response, request) {
			var msg = Ext.decode(response.responseText).msg;
			if (msg != undefined) {
				Ext.Msg.alert('提示', msg);
				return;
			}
			var success = Ext.decode(response.responseText).success;
			if (success) {
				//errWindow.close();
				//errWindow.destroy();
				Ext.Msg.alert('提示', '操作成功!');
				//reloadGroup();
				reloadGroup_new();
			} else {
				Ext.Msg.alert('提示', '操作失败!');
			}
		},
		failure : function(result, request) {
			secureFilterRs(result,"操作失败！");
		}
	});
}
//手工部署
function DeploymentOK_act(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<手工部署>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'deploymentOKStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						//releaseMonitorStepStore.reload();
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
//部署失败
function DeploymentFail_act(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<部署失败>操作，部署失败后整个部署将会被停止？", function(
			button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'deploymentFailStep.do',
				params : {
					flowId : flowId,
					stepId : stepId,
					insId:insId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						//releaseMonitorStepStore.reload();
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
/** 终止操作* */
function stopStepFun_act(flowId, stepId) {
	Ext.Msg.confirm("请确认", "是否确定要对该步进行<终止>操作？", function(button, text) {
		if (button == "yes") {
			Ext.MessageBox.wait("数据处理中...", "进度条");
			Ext.Ajax.request({
				url : 'stopStep.do',
				params : {
					flowId : flowId,
					stepId : stepId
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					// 当后台数据同步成功时
					if (success) {
						//releaseMonitorStepStore.reload();
						reloadGroup_new();
					}
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				}
			});
		}
	});
}
//页面内容情况
function resetElem()
{
	$('#sss').html("");//步骤详情
	$('#rengongchuliid').html("无输出");//输出标题
	$('#sss2').html("");//输出
	 $('.kl_sys_stage').html("");//活动
	//按钮全部置为灰
		setButtonClass('shougongbushu_tool',0);
		setButtonClass('chongshi_tool',0);
		setButtonClass('lueguo_tool',0);
		//setButtonClass('zhongzhibushu_tool',0);
	}