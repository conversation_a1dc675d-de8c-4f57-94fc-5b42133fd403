package com.ideal.controller.ic.hcpub;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.ieai.server.repository.hd.ic.hcpub.ComInfoBean;
import com.ideal.ieai.server.repository.hd.ic.hcpub.HcAutoStartManager;
import com.ideal.ieai.server.repository.hd.ic.hcpub.SysBindComBean;
import com.ideal.ieai.server.repository.hd.ic.supperhc.common.SupperHcConstants;
import com.ideal.ieai.server.repository.hd.ic.supperhc.exception.SupperhcException;
import com.ideal.service.ic.hcpub.HcPubicInfoService;


@Controller
@RequestMapping("/hcpub")
public class HcPublicInfoController
{
    private static final Logger log = Logger.getLogger(HcPublicInfoController.class);

    @RequestMapping("/getLogPage.do")
    public String getLogPage(HttpServletRequest request, String pointid,String rsdid,Integer height) {
        request.setAttribute("pointid", pointid);
        request.setAttribute("rsdid", rsdid);
        request.setAttribute("height", height==null?500:height);
        
        return "hcpub/hcMonitorOfLog";
        
    }

    @RequestMapping("/showPointLogOfWarn.do")
    @ResponseBody
    public Map<String,Object> showPointLogOfWarn(HttpServletRequest request, HttpServletResponse response,String pointid){
        String content="";
        Map<String,Object> map = new HashMap<String, Object>();
        try
        {
            content = HcPubicInfoService.getInstance().showPointLogOfWarn(pointid);
            map.put(SupperHcConstants.RES_SUCC, true);
            map.put(SupperHcConstants.RES_MSG, "操作成功！");
            map.put(SupperHcConstants.RES_DATALIST, content);
        } catch (SupperhcException e)
        {
            log.error("活动输出查询出错！", e);
            map.put(SupperHcConstants.RES_SUCC, false);
            map.put(SupperHcConstants.RES_MSG, "操作失败！");
            map.put(SupperHcConstants.RES_DATALIST, content);
        }
        return map;
        
    }
    
    @RequestMapping("/showPointLog.do")
    @ResponseBody
    public Map<String,Object> showPointLog(HttpServletRequest request, HttpServletResponse response,String rsdid,String pointid){
        String content="";
        Map<String,Object> map = new HashMap<String, Object>();
        try
        {
            content = HcPubicInfoService.getInstance().showPointLog(rsdid,pointid);
            map.put(SupperHcConstants.RES_SUCC, true);
            map.put(SupperHcConstants.RES_MSG, "操作成功！");
            map.put(SupperHcConstants.RES_DATALIST, content);
        } catch (SupperhcException e)
        {
            log.error("活动输出查询出错！", e);
            map.put(SupperHcConstants.RES_SUCC, false);
            map.put(SupperHcConstants.RES_MSG, "操作失败！");
            map.put(SupperHcConstants.RES_DATALIST, content);
        }
        return map;
        
    }
    
    @RequestMapping("/testhcbind.do")
    @ResponseBody
    public Map<String,Object> testhcbind(HttpServletRequest request, HttpServletResponse response,String rsdid,String pointid){
        HcAutoStartManager manager = new HcAutoStartManager();
        List<SysBindComBean> bindcoms = new ArrayList();
        SysBindComBean onebean = new SysBindComBean();
        onebean.setOstype("Linux");
        List<ComInfoBean> combeanlist = new ArrayList();
        ComInfoBean twobean = new ComInfoBean();
        twobean.ComInfoBean(20L);
        combeanlist.add(twobean);
        ComInfoBean threebean = new ComInfoBean();
        threebean.ComInfoBean(5L);
        combeanlist.add(threebean);
        onebean.setBeanlist(combeanlist);
        bindcoms.add(onebean);
        try
        {
            manager.sysBindComFunc(bindcoms);
        } catch (SupperhcException e)
        {
            e.printStackTrace();
        }
        return null;
    }
}
