package com.ideal.ieai.server.repository.hd.ic.alarmconfighis;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.ieaikernel.ServerEnv;

public class HcAlarmSwitchThread extends Thread
{
    private static final Logger log            = Logger.getLogger(HcAlarmSwitchThread.class);
    HcAlarmSwitchHis hcAlarmSwitchHis=new HcAlarmSwitchHis();
    
    @Override
    public void run ()
    {
        while(true) {
            
            if (ServerEnv.getServerEnv().isHcAlarmThreadSwtich())
            {
                hcAlarmSwitchHis.sendAlarmSwitchHisTalk();
            } else {
                log.info("自动关闭告警开关未开启");
                break;
            }
        }
    }
}
