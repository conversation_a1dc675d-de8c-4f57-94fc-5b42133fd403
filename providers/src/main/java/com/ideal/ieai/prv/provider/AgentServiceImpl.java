package com.ideal.ieai.prv.provider;

import com.ideal.dubbo.interfaces.IAgentService;
import com.ideal.dubbo.models.AgentModel;
import com.ideal.dubbo.models.AgentParamBean;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.prv.repository.hd.agent.AgentManager;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBeanForQuery;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemManager;
import com.ideal.ieai.server.repository.project.ProjectManager;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@DubboService
public class AgentServiceImpl implements IAgentService
{
    private static final Logger logger = Logger.getLogger(AgentServiceImpl.class);

    @Override
    public Map checkAgentState ( Long[] agentIds )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().checkAgentState(agentIds, cmdbFlag);
        } catch (Exception e)
        {
            res.put("isOk", false);
            res.put("agentStateMsg", "checkAgentState出错！" + e.getMessage());
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAgentList ( int start, int limit, AgentModel filter, String flag, Long[] rgIds )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentList(start, limit, filter, flag, rgIds, "getAgentList", CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAgentList is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map getAllCanChooseTryAgentList ( AgentModel filter, int start, int limit )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllCanChooseTryAgentList(filter, start, limit, CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAllCanChooseTryAgentList出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map<String,Object> getTryAgentList ()
    {
        Map<String,Object> res = new HashMap<String,Object>();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getTryAgentList(cmdbFlag);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getTryAgentConfig ( AgentModel filter, int start, int limit )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getTryAgentConfig(filter, start, limit, cmdbFlag);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Boolean delTryAgentInfo ( Long[] ids )
    {
        Boolean res = false;
        try
        {
            res = AgentManager.getInstance().delTryAgentInfo(ids);
        } catch (Exception e)
        {
            logger.error("delTryAgentInfo出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map saveTryAgentConfig ( Map customNameMap )
    {
        Map res = new HashMap();
        try
        {
            res = AgentManager.getInstance().saveTryAgentConfig(customNameMap);
        } catch (Exception e)
        {
            logger.error("saveTryAgentConfig出错！" + e.getMessage());
            res.put("success", false);
            res.put("message", e.getMessage());
        }
        return res;
    }

    @Override
    public Boolean addTryAgentConfig ( Long[] ids )
    {
        Boolean res = false;
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().addTryAgentConfig(ids, CMDBflag);
        } catch (Exception e)
        {
            logger.error("addTryAgentConfig出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map getAgentChosedList ( int start, int limit, String[] chosedIds, long workItemId, boolean isCommonTasks,
            String customName )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentChosedList(start, limit, chosedIds, workItemId, cmdbFlag,
                isCommonTasks, customName);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    public Map<String, Object> getSumpAgentUserList ( long agentId )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        try
        {
            res = AgentManager.getInstance().getSumpAgentUserList(agentId);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAudiChoseAgentdList ( int start, int limit, long workItemId )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAudiChoseAgentdList(start, limit, workItemId, CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAudiChoseAgentdList is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public List<AgentModel> getAgentListForWS ( int start, int limit, AgentModel filter, String flag )
    {
        List<AgentModel> res = new ArrayList<AgentModel>();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentListForWS(start, limit, filter, flag, CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAgentListForWS is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public Integer getAgentCountForWS ( AgentModel filter, String flag )
    {
        Integer res = 0;
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentCountForWS(filter, flag, CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAgentCountForWS is error:" + e);
        }
        return res;
    }

    @Override
    public Map getAllAgentListOld ( Long[] rgIds, Long[] agentIds, AgentModel filter, String flag, Boolean hasPage,
            Integer start, Integer limit )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllAgentListOld(rgIds, agentIds, filter, flag, hasPage, start, limit,
                CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAllAgentListOld is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map getAllAgentList (String computerNames,String uuid,boolean enableFlag, Long[] rgIds, AgentModel filter, String flag, int start, int limit, long userid, Long business )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            //值班人员获取所有agent
            if(!filter.isHeightPermissionFlag()){
                res = AgentManager.getInstance().getAllAgentList(computerNames,uuid,enableFlag, rgIds, filter, flag, start, limit, cmdbFlag, userid, business);
            }else{
                res = AgentManager.getInstance().getAllAgentListByDuty(computerNames, rgIds, filter, flag, start, limit, cmdbFlag, userid);
            }
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAllAgentForIpSearch (String computerNames,String uuid,boolean enableFlag, Long[] rgIds, AgentModel filter, String flag, int start, int limit, long userid )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                    Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllAgentForIpSearch(computerNames,uuid,enableFlag, rgIds, filter, flag, start, limit, cmdbFlag, userid);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAllAgentListScript (boolean enableFlag, Long[] rgIds, AgentModel filter, String flag, int start, int limit, long userid )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                    Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllAgentListScript(enableFlag, rgIds, filter, flag, start, limit, cmdbFlag, userid);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAgentListWithOutSys (long userid,Integer start,Integer limit,String agentGroup)
    {
        Map res = new HashMap();
        try
        {
            //定义agent存储list
            //List<Integer> agentIds = new ArrayList();
            List<String> agentIps = new ArrayList();
            //获取业务系统数据
            Map sysMap = ScriptServiceManage.getInstance().getSystem("",String.valueOf(userid),0,100000);
            //获取agent
            List<Map> dataMap = (List<Map>) sysMap.get("dataList");
            for(Map oMap : dataMap){
                BusinessSystemBeanForQuery bsbfq = new BusinessSystemBeanForQuery();
                bsbfq.setSysIdForQuery(Long.valueOf(String.valueOf(oMap.get("iid"))));
                bsbfq.setStart(0);
                bsbfq.setLimit(100000);
                Map agentMap = BusinessSystemManager.getInstance().getComputerList(bsbfq, Integer.valueOf(String.valueOf(oMap.get("itype"))));
                //获取agentid
                List<Map> agentList = (List) agentMap.get("dataList");
                for(Map sonSon : agentList){
                    //agentIds.add(Integer.valueOf(String.valueOf(sonSon.get("cpId"))));
                    //根据ip过滤agent
                    agentIps.add(String.valueOf(sonSon.get("ip")));
                }
            }
            String [] ips = new String[agentIps.size()];
            if(agentIps.size() > 0){
                for(int i=0 ; i<agentIps.size() ; i++){
                    ips[i] = String.valueOf(agentIps.get(i));
                }
            }
            //查询数据
            res = AgentManager.getInstance().getAgentListWithOutSys(ips,userid,start,limit,agentGroup);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }



    @Override
    public Map getAllUsersToChangeUser (Integer start,Integer limit,String userid)
    {
        Map res = new HashMap();
        try
        {
            res = AgentManager.getInstance().getAllUsersToChangeUser(start,limit,userid);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public boolean getAgentByIp (String agentIp)
    {
        boolean res = false;
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                    Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentByIp(agentIp);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAllAgentListForWhite ( Long[] rgIds, AgentModel filter, String flag, int start, int limit,
            long userid )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllAgentListForWhite(rgIds, filter, flag, start, limit, CMDBflag,
                userid);
        } catch (Exception e)
        {
            logger.error("getAllAgentList出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map getAllAgentListNoPage ( String flag )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllAgentListNoPage(flag, cmdbFlag);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAllAgentListForSsExec ( String workItemId, int start ,int limit, String scriptType )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAllAgentListForSsExec(workItemId,start ,limit,CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAllAgentListForSsExec is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public List getAllAgentListForSsExec ( String workItemId, String dbaasSwitch, String scriptType )
    {
        List res = new ArrayList();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                    Environment.CMDB_FLAG_DEFAULT);
            if ("1".equals(dbaasSwitch))
            {
                if ("sql".equals(scriptType))
                {
                    res = AgentManager.getInstance().getAllAgentListForSsExec(workItemId);
                } else
                {
                    res = AgentManager.getInstance().getAllAgentListForSsExec(workItemId, CMDBflag);
                }
            } else
            {
                res = AgentManager.getInstance().getAllAgentListForSsExec(workItemId, CMDBflag);
            }
        } catch (Exception e)
        {
            logger.error("getAllAgentListForSsExec is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public List getAllAgentListForSsExecDbaas ( String workItemId,   String scriptType )
    {
        List res = new ArrayList();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            if ("sql".equals(scriptType))
            {
                res = AgentManager.getInstance().getAllAgentListForSsExec(workItemId);
            } else
            {
                res = AgentManager.getInstance().getAllAgentListForSsExecDbaas(workItemId, CMDBflag);
            }
        } catch (Exception e)
        {
            logger.error("getAllAgentListForSsExec is error:" + e.getMessage());
        }
        return res;
    }

    @Override
    public String getAgentOs ( String agentIp, long agentPort )
    {
        String res = "";
        try
        {
            res = AgentManager.getInstance().getAgentOs(agentIp, agentPort);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public List getOsType ()
    {
        List res = new ArrayList();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getOsType(CMDBflag);
        } catch (Exception e)
        {
            logger.error("getOsType出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public AgentModel getAgentInfo ( String iid )
    {
        AgentModel res = null;
        try
        {
            res = AgentManager.getInstance().getAgentInfo(iid);
        } catch (Exception e)
        {
            logger.error("getAgentInfo出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public boolean checkAllEquipsEnable(String iids){
        boolean res = false;
        try
        {
            res = AgentManager.getInstance().checkAllEquipsEnable(iids);
        } catch (Exception e)
        {
            logger.error("getAgentInfo_ids出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public AgentModel getAgentInfo ( String agentIp, String agentPort, int envType )
    {
        AgentModel res = null;
        try
        {
            res = AgentManager.getInstance().getAgentInfo(agentIp, agentPort, envType);
        } catch (Exception e)
        {
            logger.error("getAgentInfo出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map getAgentListForRest ( int start, int limit, AgentModel filter )
    {
        Map res = new HashMap();
        try
        {
            boolean CMDBflag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentListForRest(start, limit, filter, CMDBflag);
        } catch (Exception e)
        {
            logger.error("getAgentListForRest出错！" + e.getMessage());
        }
        return res;
    }

    public Map getExecuteHis ( int start, int limit, String startTime,String endTime ){
        Map res = new HashMap();
        try
        {
            res = AgentManager.getInstance().getExecuteHis(start, limit, startTime, endTime);
        } catch (Exception e)
        {
            logger.error("getExecuteHis出错！" + e.getMessage());
        }
        return res;
    }

    @Override
    public Map getAgentSysNameList ( int envType, String dbaasSwitch )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentSysNameList(envType, cmdbFlag, dbaasSwitch);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map getAgentAppNameList ( int envType )
    {
        Map res = new HashMap();
        try
        {
            boolean cmdbFlag = ServerEnv.getInstance().getBooleanConfig(Environment.CMDB_FLAG,
                Environment.CMDB_FLAG_DEFAULT);
            res = AgentManager.getInstance().getAgentAppNameList(envType, cmdbFlag);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }

    @Override
    public Map<String, Object> importAgentForStart ( Long[] serviceId, String state, String permGidStr,
            List<AgentParamBean> list,
            int envType, long userid, String userName, String scriptType, String scriptPlatmFrom, 
            String inumber ) throws Exception
    {
        return AgentManager.getInstance().importAgentForStart(serviceId, state, permGidStr, list, envType, userid,
            userName,
            scriptType, scriptPlatmFrom, inumber);
    }

    @Override
    public Map<String, Object> importAgentForWhitelistExec ( List<AgentParamBean> list, int envType ) throws Exception
    {
        return AgentManager.getInstance().importAgentForWhitelistExec(list, envType);
    }

    @Override
    public Map<String, Object> importAgentForFileIssued ( List<AgentParamBean> list, long userid ) throws Exception
    {
        return AgentManager.getInstance().importAgentForFileIssued(list, userid);
    }

    @Override
    public Map<String, Object> getAllAgentListForSPDB (String batchComputerName,boolean enableFlag, AgentModel filter, String flag, int start, int limit,
            Long[] importAgentIds, Long[] agentIds, String from, String userName )
    {
        Map<String, Object> res = new HashMap<String, Object>();

        try
        {
            res = AgentManager.getInstance().getAllAgentListForSPDB(batchComputerName,enableFlag,filter, flag, start, limit, importAgentIds,
                agentIds, from, userName);
        } catch (Exception e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        return res;
    }
    public byte[] remoteDownloadAdaptor ( String RequestUUID, String adaptorName, String AdaptorUUID )
            throws ServerException
    {
        try
        {
            return ProjectManager.getInstance().downloadAdpByUuid(AdaptorUUID);
        } catch (RepositoryException e)
        {
            throw new ServerException(e.getServerError().getErrCode());
        }
    }
}
