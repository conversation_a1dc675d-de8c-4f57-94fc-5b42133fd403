/**
 * 查询条件
 */
var iprjName = "";
Ext.define('projectModel', {
	extend : 'Ext.data.Model',
	fields : [ {
		name : 'iprjName', // 系统名称
		type : 'string'
	}, {
		name : 'iprjId', // 系统ID
		type : 'long'
	}, {
		name : 'iupperId',
		type : 'long'
	} ]
});
// 工程名称下拉列表stor
var getPrjNameStore = Ext.create('Ext.data.Store', {
	autoLoad : true,
	model : 'projectModel',
	proxy : {
		type : 'ajax',
		url : 'actmonitor/getPrjName.do',
		reader : {
			type : 'json',
			root : 'dataList'
		}
	}
});

function selectFunction(btn) {
	submitIns();
	if (Ext.isIE) {
		CollectGarbage();
	}
}

function submitIns() {
	iprjName = Ext.getCmp("prjNameComBox_id").getValue();
	projectName = Ext.getCmp("prjNameComBox_id").getRawValue();
	actListStroe.load();
}
Ext.define('Aoms.view.Condition', {
	extend : 'Ext.panel.Panel',
	alias : 'widget.condition',

	requires : [ 'Ext.form.Panel', 'Ext.form.field.ComboBox',
			'Ext.form.field.Date', 'Ext.button.Button' ],
	border : false,
	//    height: 37,
	height : 45,
	layout : 'column',
	initComponent : function() {
		var me = this;
		var stateStore = Ext.create('Ext.data.Store', {
			fields : [ 'id', 'name' ],
			data : [ {
				"id" : "Ready",
				"name" : "就绪"
			}, {
				"id" : "Running",
				"name" : "运行"
			}, {
				"id" : "Fail",
				"name" : "失败"
			}, {
				"id" : "ManualRunning",
				"name" : "异常处理中"
			} ]
		});

		var insNameBox = Ext.widget('combobox', {
			id : 'insNameBox',
			padding : '8 8 8 8',
			width : 600,
			emptyText : '实例名称',
			labelWidth : 0,
			hidden : true
		});
		var actTypeBox = Ext.widget('combobox', {
			padding : '8 8 8 8',
			width : 100,
			emptyText : '活动状态',
			labelWidth : 0,
			store : stateStore,
			displayField : 'name',
			hidden : true,
			valueField : 'id'
		});
		var freshBtn = Ext.create("Ext.Button", {
			id : 'freshBtn',
			text : '刷新',
			handler : me.funFresh
		});
		var freshTxt = Ext.widget('textfield', {
			padding : '5 5 5 5',
			labelWidth : 90,
			width : 140,
			labelAlign : 'right',
			fieldLabel : '刷新时间(秒)',
			value : '60'
		});
		//  var projectTxt = Ext.widget('combo',{padding: '5 5 5 5',labelWidth : 90,width:240,labelAlign : 'right',fieldLabel: '工程名称',value: '',store : getPrjNameStore});
		var prjNameComBox = Ext.create('Ext.form.field.ComboBox', {
			id : 'prjNameComBox_id',
			name : 'form_prjName',
			fieldLabel : '工程名称',
			displayField : 'iprjName',
			valueField : 'iprjId',
			emptyText : '',
			width : 500,
			labelWidth : 70,
			padding : '5 5 5 5',
			editable : true,
			typeAhead : true,
			store : getPrjNameStore
		});
		var selectButton = Ext.create("Ext.Button", {
			textAlign : 'center',
			text : "查询",
			handler : selectFunction
		});
		Ext.applyIf(me, {
			items : [ insNameBox, actTypeBox, prjNameComBox, selectButton,
					freshTxt, freshBtn ]
		});
		me.callParent(arguments);
	}
});
