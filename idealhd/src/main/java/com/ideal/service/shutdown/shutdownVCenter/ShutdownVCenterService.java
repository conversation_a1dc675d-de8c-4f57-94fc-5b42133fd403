package com.ideal.service.shutdown.shutdownVCenter;


import com.alibaba.fastjson.JSON;
import com.ideal.common.utils.Tools;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.communication.api.APIClient;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.element.ParameterDef;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.util.Jcrypt;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.compare.common.CompareException;
import com.ideal.ieai.server.compare.repository.comparebase.CompareBaseManage;
import com.ideal.ieai.server.datacollect.repository.collectclass.CollectClassManage;
import com.ideal.ieai.server.datacollect.repository.collectclass.CollectClassModel;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.ActBean;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.flowstart.WorkflowColumnGenerator;
import com.ideal.ieai.server.repository.hd.ic.supperhc.exception.SupperhcException;
import com.ideal.ieai.server.repository.hd.platform.ftpinfo.model.FtpInfo;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoBean;
import com.ideal.ieai.server.repository.hd.shellinfo.ShellInfoManager;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.importexecl.InfoExeclServices;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import com.ideal.ieai.server.shutdown.ShutDownCommon;
import com.ideal.ieai.server.shutdown.shutdownPlan.ShutdownClusterGather;
import com.ideal.ieai.server.shutdown.shutdownPlan.ShutdownPlanManager;
import com.ideal.ieai.server.shutdown.shutdownPlan.ShutdownPlanUpModel;
import com.ideal.ieai.server.shutdown.shutdownPlan.ShutdownResourceModel;
import com.ideal.ieai.server.shutdown.shutdownSchedule.ScheduleManage;
import com.ideal.ieai.server.shutdown.shutdownSchedule.bean.ShutdownInputParam;
import com.ideal.ieai.server.shutdown.shutdownSchedule.bean.ShutdownTaskModel;
import com.ideal.ieai.server.shutdown.shutdownVCenter.ShutdownVCenterManager;
import com.ideal.ieai.server.shutdown.shutdownVCenter.ShutdownVCenterModel;
import com.ideal.ieai.server.util.BeanFormatter;
import com.ideal.service.platform.ftpinfo.FtpInfoService;
import com.ideal.service.shutdown.action.ShutdownActionService;
import com.ideal.service.shutdown.shutdownApplication.ShutdownApplicationService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.json.JSONException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

public class ShutdownVCenterService {
    private static final Logger log = Logger.getLogger(ShutdownVCenterService.class);
    private ShutdownVCenterManager manager = new ShutdownVCenterManager();
    private static final String CHARSET = "UTF-8";
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";
    private static final String EXESQLS = "exeSqls";
    private static final String ROLLBACKSQLS = "rollbackSqls";
    private static final String SAVECOLLECTCLASS = "saveCollectClass";
    private static final String BASECONN = "baseConn";
    private static final String DBCONNS = "dbConns";
    private static final String DELETECOMPAREBASE = "deleteCompareBase";
    private static final String ORGSQL = "组织信息sql时出现错误！";
    private static final String DATEFORMAT = "yyyy-MM-dd HH:mm:ss";

    public Map getShutDownVCenterData(String ip, int start, int limit) throws RepositoryException {
        Connection conn = null;
        String sql = "";
        String sqlWhere = "";
        Map map = new HashMap();

        if (!StringUtils.isEmpty(ip)) {
            sqlWhere += " and ( p.IP LIKE '%" + ip + "%' or p.IP LIKE '%" + ip + "%' )";
        }

        switch (JudgeDB.IEAI_DB_TYPE) {
                case 1:
                case 4:
                sql = " SELECT * FROM (SELECT DISTINCT P.*,ROW_NUMBER() OVER (ORDER BY P.IID desc) AS RN FROM IEAI_SHUTDOWN_VCENTER P WHERE 1=1 " + sqlWhere
                        + " order by P.IID DESC) WHERE RN<=? AND RN>?";
                break;
            case 2:
                sql = " SELECT * FROM ( SELECT DISTINCT P.*,ROWNUMBER() OVER(order by P.IID DESC) FROM "
                        + "IEAI_SHUTDOWN_VCENTER P WHERE 1=1 "
                        + sqlWhere + ") WHERE RN>? AND RN<=?";
                break;
            case 3:
                sql = "SELECT * FROM ( SELECT DISTINCT * FROM IEAI_SHUTDOWN_VCENTER P WHERE 1=1 " + sqlWhere
                        + " order P.IID DESC) T limit ? , ?";
                break;
        }
        String sqlOne = " SELECT COUNT(*) as countNum FROM IEAI_SHUTDOWN_VCENTER P WHERE 1=1 " + sqlWhere;
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SHUTDOWN_MAINTAIN);
            List list = manager.getShutDownVCenterDataList(conn, sql, start, limit);
            int total = manager.getInfoCount(conn, sqlOne);
            map.put("dataList", list);
            map.put("total", total);
        } catch (Exception e) {
            log.error("ShutdownVCenterService.getShutDownVCenterData is error", e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally {
            DBResource.closeConnection(conn, "getShutDownVCenterData", log);
        }
        return map;
    }

    /**
     * 保存方法解析json
     *
     * @param jsonData
     * @return
     */
    public List<ShutdownVCenterModel> jsonTranslateToBeanForSave(String jsonData) {
        List dataList = new ArrayList<>();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData) {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++) {
                ShutdownVCenterModel model = new ShutdownVCenterModel();
                model.setIid(jsonArr.getJSONObject(i).optLong("iid"));
                model.setName(jsonArr.getJSONObject(i).optString("name"));
                String password = jsonArr.getJSONObject(i).optString("password");
                if(password.indexOf("*")!=-1){
                    model.setPassword("");
                }else{
                    Jcrypt j = new Jcrypt();
                    //加密 存储
                    String password1=j.encrypt(password);
                    model.setPassword(password1);
                }

                model.setIp(jsonArr.getJSONObject(i).optString("ip"));
                model.setType(jsonArr.getJSONObject(i).optString("type"));
                model.setVcname(jsonArr.getJSONObject(i).optString("vcname"));
                dataList.add(model);
            }
        }
        return dataList;
    }

     public Map saveVCenterData(List<ShutdownVCenterModel> modelList) throws RepositoryException {
        Map map = new HashMap();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SHUTDOWN_MAINTAIN);
            for (ShutdownVCenterModel model : modelList) {
                manager.saveAllVCenterData(conn,model);
            }
            conn.commit();
            map.put(SUCCESS, true);
            map.put(MESSAGE, "保存成功");
        } catch (Exception e) {
            // 失败后数据回滚
            DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e, method, log);
            log.error(method, e);
        } finally {
            DBResource.closeConnection(conn, method, log);
        }
        return map;
    }

    public Map deleteScheduleData(String deleteIds) throws RepositoryException {
        Map map = new HashMap();
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        try {

            String[] iid = deleteIds.split(",");
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_SHUTDOWN_MAINTAIN);
            if (iid != null && iid.length > 0) {
                for (int i = 0; i < iid.length; i++) {
                    manager.deleteAllPlanData(conn,Long.valueOf(iid[i]));
                }
            }
            conn.commit();
            map.put(SUCCESS, true);
            map.put(MESSAGE, "删除成功");
        } catch (Exception e) {
            log.error(method, e);
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } finally {
            DBResource.closeConnection(conn, method, log);
        }
        return map;
    }
}
