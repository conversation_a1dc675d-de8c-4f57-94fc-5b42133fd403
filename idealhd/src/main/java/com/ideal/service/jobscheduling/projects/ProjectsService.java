package com.ideal.service.jobscheduling.projects;

import com.ideal.ieai.server.jobscheduling.repository.projects.ProjectsManager;
import com.ideal.ieai.server.repository.RepositoryException;

import java.util.Map;

/**
 * 
 * @ClassName:  ProjectsService   
 * @Description:工程查詢service 
 * @author: Administrator 
 * @date:   2017年7月25日 下午5:07:31   
 *     
 * @Copyright: 2017-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
public class ProjectsService
{
    private static ProjectsService intance = new ProjectsService();

    public static ProjectsService getInstance ()
    {
        if (intance == null)
        {
            intance = new ProjectsService();
        }
        return intance;
    }

    /**
     * 
     * @Title: listProjects   
     * @Description: 获取列表数据 
     * @param: @param userId
     * @param: @param page
     * @param: @param limit
     * @param: @param proName
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:09:46
     */
    public Map listProjects ( String userId, String page, String limit, String proName ) throws RepositoryException
    {
        return ProjectsManager.getInstance().listProjects(userId, page, limit, proName);
    }

    /**
     * 
     * @Title: getProjectDetail   
     * @Description: 获取详细信息   
     * @param: @param IID
     * @param: @param sessionId
     * @param: @param type
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:09:55
     */
    public Map getProjectDetail ( long iid ) throws RepositoryException
    {
        return ProjectsManager.getInstance().getProjectDetail(iid);

    }

    /**
     * 
     * @Title: getProjectHistory   
     * @Description: 获取历史版本
     * @param: @param iid
     * @param: @param userId
     * @param: @param page
     * @param: @param limit
     * @param: @param proName
     * @param: @return
     * @param: @throws RepositoryException      
     * @return: Map      
     * @throws   
     * @author: Administrator 
     * @date:   2017年7月25日 下午5:09:59
     */
    public Map getProjectHistory ( long iid, int isExcel ,String proName) throws RepositoryException
    {
        return ProjectsManager.getInstance().getProjectHistory(iid, isExcel, proName);
    }
}
