package com.ideal.ieai.server.hd.thread;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.ieai.server.engine.agent.ActivityRExecHelper;
import com.ideal.ieai.server.engine.agent.AgentXmlRpcClient;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.proxy.execremote.PerformDataProcessService;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.engine.EngineRepositotyJdbc;
import com.ideal.ieai.server.repository.hd.agentCheck.AgentCheckData;
import com.ideal.ieai.server.repository.hd.agentCheck.AgentCheckRepository;
import com.ideal.ieai.server.repository.hd.agentMaintain.RpcSslType;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.util.StringUtil;
import org.apache.log4j.Logger;
import org.apache.xmlrpc.XmlRpcClient;

import java.util.*;

public class AgentCheckThread extends Thread
{

    private Logger  _log           = Logger.getLogger(AgentCheckThread.class);
    // Agent监控周期
    private int     agentcheckpoll = Environment.AGENT_CHECK_POLL_NUM * 60 * 1000;
    // Agent监控开关
    private boolean agentcheck     = Environment.IS_AGENT_CHECK;
    // 检测文件名称
    private String  fileName       = Environment.AGENT_CHECK_FILE_NAME;

    public AgentCheckThread()
    {
        init();
    }

    public void init ()
    {
        Map map = null;
        try
        {
            map = EngineRepositotyJdbc.getInstance().getSysDictionary();
            if (null != map && map.size() > 0)
            {
                agentcheckpoll = (Integer.valueOf((String) map.get(Environment.AGENT_CHECK_POLL)).intValue()) * 60
                        * 1000;
                // 如果间隔周期小于5分钟，则改为5分钟
                if (agentcheckpoll < 300000)
                {
                    agentcheckpoll = 300000;
                }
                if ("true".equals(map.get(Environment.AGENT_CHECK)) || "TRUE".equals(map.get(Environment.AGENT_CHECK)))
                {
                    agentcheck = true;
                } else
                {
                    agentcheck = false;
                }
                fileName = (String) map.get(Environment.AGENT_CHECK_FILE);
            }
        } catch (Exception e)
        {
        }
    }

    public void run ()
    {
        while (true)
        {
            try
            {
                // 间隔周期
                init();
                Thread.sleep(agentcheckpoll);
                // Thread.sleep(30 * 1000);
            } catch (InterruptedException e)
            {
                _log.error("AgentCheckThread sleep error!", e);
            }
            // 判断死否需要监控
            if (agentcheck && ischeck())
            {
                // 监控设备上的业务系统状态
                checkComputerList();
                // 监控巡检的agent状态
                checkAgentList();
            }
        }

    }

    private void checkComputerList ()
    {
        // 获取所有的满足条件的设备
        List checklist = new ArrayList();
        try
        {
            checklist = AgentCheckRepository.getInstance().getCheckList();
        } catch (RepositoryException e)
        {
            _log.error("AgentCheckThread getCheckList error!", e);
        }
        if (checklist.size() != 0)
        {
            // 发起设备监控
            computerCheck(checklist);
        }
    }

    private void checkAgentList ()
    {
        // 获取所有的满足条件的agent
        List agentlist = new ArrayList();
        try
        {
            agentlist = AgentCheckRepository.getInstance().getCheckAgentList();
        } catch (RepositoryException e)
        {
            _log.error("AgentCheckThread getCheckagentlist error!", e);
        }
        if (agentlist.size() != 0)
        {
            // 发起agent监控
            agentCheck(agentlist);
        }
    }

    /**
     * 
     * <li>Description:判断自身是否是监控server</li>
     * 
     * <AUTHOR> 2015年10月14日
     * @return return boolean
     */
    public boolean ischeck ()
    {
        try
        {
            // 本机IP
            String serverIp = Environment.getInstance().getServerIP();
            // serverIp = "************";
            if (!StringUtil.isEmptyStr(serverIp))
            {
                // 判断是否是执行监控的server
                if (serverIp.equals(AgentCheckRepository.getInstance().getFirstServerIp()))
                {
                    return true;
                }
            }
            return false;

        } catch (Exception e)
        {
            _log.error("AgentCheckThread get serverinfo error!", e);
            return false;
        }
    }

    /**
     * 
     * <li>Description:检测所有符合条件的agent</li>
     * 
     * <AUTHOR> 2015年10月15日
     * @param agentList return void
     */
    public void agentCheck ( List agentList )
    {
        try
        {
            for (int i = 0; i < agentList.size(); i++)
            {
                AgentCheckData data = (AgentCheckData) agentList.get(i);
                // System.out.println("AgentIp:" + data.getAgentIp() + ";AgentPort:"
                // + data.getAgentPort() );
                // long computerId = data.getComputerId();
                // 监控aentIP
                String agentIp = data.getAgentIp();
                // Agent开发的端口
                int port = data.getAgentPort();
                // 如果端口小于等于0，则直接将设备的agent状态置为未开启
                if (port <= 0)
                {
                    try
                    {
                        AgentCheckRepository.getInstance().setAgentState(agentIp, port, 1);
                    } catch (RepositoryException e1)
                    {
                        _log.error("AgentCheckThread setAgentFail1 error!", e1);
                    }
                    continue;
                }

                // 对监控agent连接时，采用重试机制避免网络闪断
                ActivityRExecHelper.connectAgent(agentIp, port);
            }
        } catch (Throwable e)
        {
            _log.error("AgentCheckThread agentCheck error!", e);
        }
    }

    /**
     * 
     * <li>Description:检测所有符合条件的设备上的业务系统状态</li>
     * 
     * <AUTHOR> 2015年11月4日
     * @param agentList return void
     */
    public void computerCheck ( List agentList )
    {
        try
        {
            String serverIp = Environment.getInstance().getServerIP();
            int serverPort = Environment.getInstance().getIntConfig(Environment.TOM_PORT, Environment.TOM_PORT_DEFAULT);
            for (int i = 0; i < agentList.size(); i++)
            {
                AgentCheckData data = (AgentCheckData) agentList.get(i);
                // System.out.println("AgentIp:" + data.getAgentIp() + ";AgentPort:"
                // + data.getAgentPort() + ";Filename:" + data.getFileName() + ";SysParam:"
                // + data.getSysParam() + ";SysId:" + data.getSysId().longValue());

                // 监控aentIP
                long computerId = data.getComputerId();
                String agentIp = data.getAgentIp();
                // Agent开发的端口
                int port = data.getAgentPort();
                // 如果端口小于等于0，则直接将设备的agent状态置为未开启
                if (port <= 0)
                {
                    try
                    {
                        AgentCheckRepository.getInstance().setAgentState(computerId, 1);
                    } catch (RepositoryException e1)
                    {
                        _log.error("AgentCheckThread setAgentFail1 error!", e1);
                    }
                    continue;
                }

                // 对监控agent连接时，采用重试机制避免网络闪断
                for (int j = 0; j < 3; j++)
                {
                    try
                    {
                        XmlRpcClient _rpcClient = null;

                        // 获取连接类型
                        // _rpcClient = new XmlRpcClient("http://" + agentIp + ':' + port +
                        // "/RPC2");
                        Vector params = new Vector();
                        params.addElement(data.getSysId().toString());
                        params.addElement(data.getComputerIp());
                        params.addElement(new Integer(port));
                        params.addElement(fileName);
                        params.addElement(data.getSysParam());
                        params.addElement(serverIp);
                        params.addElement(new Integer(serverPort));
                        params.addElement(agentIp);
                        boolean proxySwitch = ServerEnv.getInstance()
                                .getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH, Environment.FALSE_BOOLEAN);
                        int ssltype= RpcSslType.getInstance().exec(agentIp, port);
                        if (proxySwitch)
                        {
                            PerformDataProcessService ps = new PerformDataProcessService();
                            Hashtable ha = new Hashtable();

                            ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, port, Constants.IEAI_IEAI_BASIC);
                            // 设置传递的参数
                            if (pm.isProxy())
                            {
                                params.addElement(ha);
                                _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:"
                                        + agentIp + ":" + port + " proxySwitch is true ");
                            }
                            _rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
                        } else
                        {
                            _rpcClient = new AgentXmlRpcClient(agentIp, port, ssltype);
                        }
                        // 调用agent端对应的方法
                        _rpcClient.execute("IEAIAgent.checkSysState", params);
                        break;
                    } catch (Throwable e)
                    {
                        if (j != 2)
                        {
                            try
                            {
                                Thread.sleep(1000);
                            } catch (InterruptedException ex)
                            {
                            }
                        } else
                        {
                            _log.info("connect Agent fail . AgentIp=" + agentIp + ";");
                            // 设置agent的状态
                            try
                            {
                                AgentCheckRepository.getInstance().setAgentState(computerId, 1);
                                // 保存系统信息
                                AgentCheckRepository.getInstance().saveSysState(computerId, data.getSysId(), 2);
                            } catch (RepositoryException e1)
                            {
                                _log.error("AgentCheckThread setAgentFail2 error!", e1);
                            }
                            break;
                        }
                    }
                }
            }
        } catch (Throwable e)
        {
            _log.error("AgentCheckThread agentCheck error!", e);
        }
    }

    public void checkModelType ( List modelTypeList, int sysType )
    {
        try
        {
            String serverIp = Environment.getInstance().getServerIP();
            int serverPort = Environment.getInstance().getIntConfig(Environment.TOM_PORT, Environment.TOM_PORT_DEFAULT);
            for (int i = 0; i < modelTypeList.size(); i++)
            {
                Map data = (Map) modelTypeList.get(i);

                // 系统id
                Long sysId = (Long) data.get("isysid");

                // Model类型
                String type = (String) data.get("itype");

                // 监控aentIP
                String agentIp = (String) data.get("iip");

                // Agent开发的端口
                Integer port = (Integer) data.get("iport");

                // 检测脚本
                String checkfilename = (String) data.get("ipath");
                // 信息验证

                // 对监控agent连接时，采用重试机制避免网络闪断
                // 如果重试会影响效率，此处先不进行重试。add by liyang
                // for (int j = 0; j < 3; j++)
                // {

                try
                {
                    XmlRpcClient _rpcClient = null;

                    // 获取连接类型
                    // _rpcClient = new XmlRpcClient("http://" + agentIp + ':' + port +
                    // "/RPC2");
                    Vector params = new Vector();
                    params.addElement(sysId.toString());
                    params.addElement(type);
                    params.addElement(port);
                    params.addElement(checkfilename);
                    params.addElement("");
                    params.addElement(serverIp);
                    params.addElement(new Integer(serverPort));
                    params.addElement(agentIp);
                    Hashtable ha = new Hashtable();
                    boolean proxySwitch = ServerEnv.getInstance()
                            .getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH, Environment.FALSE_BOOLEAN);
                    int ssltype= RpcSslType.getInstance().exec(agentIp, port);
                    if (proxySwitch)
                    {

                        PerformDataProcessService ps = new PerformDataProcessService();
                        ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, port, sysType);
                        if (pm.isProxy())
                        {
                            params.addElement(ha);
                            _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:"
                                    + agentIp + ":" + port + " proxySwitch is true ");
                        }
                        _rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
                    } else
                    {
                        _rpcClient = new AgentXmlRpcClient(agentIp, port, ssltype);
                    }
                    // 设置传递的参数

                    // 调用agent端对应的方法
                    _rpcClient.execute("IEAIAgent.checkSysState", params);
                    // break;
                } catch (Throwable e)
                {
                    // if (j != 2)
                    // {
                    // try
                    // {
                    // Thread.sleep(1000);
                    // } catch (InterruptedException ex)
                    // {
                    // }
                    // } else
                    // {
                    _log.info("connect Agent fail . AgentIp=" + agentIp + ";");
                    DataCenterOperationManage.getInstance().setModelState(type, String.valueOf(sysId),
                        String.valueOf(-1), sysType);
                }
                // }
            }
        } catch (Throwable e)
        {
            _log.error("AgentCheckThread agentCheck error!", e);
        }
    }
}
