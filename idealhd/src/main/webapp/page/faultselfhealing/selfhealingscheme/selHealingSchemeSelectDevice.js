var sysStorageStore;
var sysStorageGrid;
//function selfHealingSchemeTestPage(){
//	var testPut = Ext.create ('Ext.form.field.TextArea',
//			{
//				fieldLabel : '报文内容',
//				width : contentPanel.getWidth()*4/6,
//				height : contentPanel.getWidth()*4/8,
////				id:'remark',
//				padding : '5 5 5 5',
////				name : 'actStdOut'
//			});
//	
//	testForm = Ext.create('Ext.form.Panel', {
//	    region: 'north',
//	    //renderTo : divid,
//    	border : false,
//		buttonAlign : 'right',
//		width : contentPanel.getWidth()*4/6,
//		cls:'customize_panel_back',
//		height : '100%',
//  	    layout: 'form',
//  	    dockedItems : [{
//				xtype : 'toolbar',
//	    	border : false,
//				dock : 'top',
//   	  		items:[testPut]
//	        }],
//		buttons : [/*waitTimeText, continueBtn, skipBtn, stopBtn*/]
//	});
//	var win = Ext.create('widget.window', {
//		draggable : false,// 禁止拖动
//		resizable : false,// 禁止缩放
//		modal : true,
//		title : '报警类型',
//		closable : true,
//		//			    closeAction : 'hide',
//		closeAction : 'destroy',
//		// animateTarget: this,
//		width : contentPanel.getWidth ()2/3,
//		height : contentPanel.getHeight ()/2,
//		layout : 'border',
//		items : [ testForm ]
//	});
//	win.show();
//	
//}
function showTask(sysId) {
	Ext.define('sysStorageModel', {
		extend : 'Ext.data.Model',
		fields : [ 
			{name : 'iid',type : 'string'},
			{name: 'sysName',  type: 'string'},
			{name: 'centerName',  type: 'string'},
			{name: 'igroupid', type: 'string'},
	        {name: 'iagentid', type: 'string'},
	        {name: 'sysAdmin', type: 'string'},
	        //{name: 'appAdmin', type: 'string'},
	        {name: 'systemInfo', type: 'string'},
	        {name: 'ctype', type: 'string'},
	        {name: 'middlewareType', type: 'string'},
	        { name: 'appName',  type: 'string'},
	        { name: 'hostName', type: 'string'},
	        { name: 'osType',type: 'string'},
	        { name: 'osTypeAgentinfo',type: 'string'},
	        { name: 'agentIp', type: 'string'},
	        {name: 'agentPort',type: 'string'},
	        {name: 'envType',type: 'string'},
	        { name: 'agentState', type: 'int'},
	        {name: 'osVersion',type: 'string'},//系统版本
	        {name: 'runDays',type: 'string'},//运行天数
	        {name: 'isHa',type: 'string'},//是否双机
	        {name: 'dbType',type: 'string'},//数据库类型
	        {name: 'dbVersion',type: 'string'},//数据库版本
	        {name: 'middlewareVersion',type: 'string'},//中间件版本
	        {name: 'icreateTime',type: 'string'},//纳管时间
	        {name: 'startUser',type: 'string'},//纳管启动人
	        {name: 'isid',type: 'string'},//服务名
	        {name: 'resourceIid',type: 'String'}//纳管id
	        ,{name: 'checked', type:'boolean'}
     	]
	});

	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',
    {
	    checkOnly : true
//		mode : 'SIMPLE',
//		listeners : {
//			beforeselect:function( me, record, index, eOpts ){
//				me.deselectAll();
//			}
//		}
    });
	sysStorageStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		//			    remoteSort: true,
		model : 'sysStorageModel',
//		pageSize : 30,
		proxy : {
			type : 'ajax',
			//url : 'infoSysCapacity/querySysStoreList.do',
			url : 'faultSelfHealing/getSelHealingSchemeSelectDeviceList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			} 
		}
	});
	
	var middleware_version_q = new Ext.form.TextField({
		fieldLabel: '中间件版本',
        labelAlign : 'right',
        labelWidth : 100,
        name: 'middleware_version_q',
        width:'33.3%',
        xtype: 'textfield'
	});
	sysStorageStore.on('beforeload', function(store, options) {
		var serverIids = "";
		var newparams = {
				sysId : sysId,
				hostName: Ext.getCmp("hostNameId").getValue(),
				agentIps: Ext.getCmp("agentIp").getValue(),
				iagentPort: agentPort.value,
				appliType:applicationType.value,
				sysAdmin:Ext.getCmp("sysAdminId1").getValue(),
				centerName:Ext.getCmp("centerName").getValue(),
				systemInfo:Ext.getCmp("system4").getValue(),
				middleType:Ext.getCmp("middleType").getValue(),
				icreateTime:  icreateTime_q.getRawValue(),
				osType:Ext.getCmp("osType_q").getValue(),
				osVersion : osVersion_q.getValue(),
				dbtype : Ext.getCmp("dbType_q").getValue(),
			    db_version : dbVersion_q.getValue(),
			    equiSelectedIds : serverIids,
			    middleware_version:middleware_version_q.getValue(),
			    opType:2,
			    opTypeNum:""
		};

		Ext.apply(store.proxy.extraParams, newparams);

	});
	sysStorageStore.on ('load', function (store, options)
			{
				//选中ip，复选框勾选
				for (var i = 0; i < store.getCount (); i++)
				{
					var record = store.getAt (i);
					if(true==record.data.checked){
						selModel.select(record,true,false);
					}
				}
			});
	/** 组名查询输入框* */
	var taskNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    emptyText : '--请输入任务名称--',
	    labelWidth : 80,
	    width : '30%',
	    xtype : 'textfield'
	});
	/** 查询按钮* */
	var taskQryButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "查询",
//	    handler : query
	    handler : function()
	    {
	    	groupGrid.ipage.moveFirst();
	    }
	});
	var deleteButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		text : '移除',
		handler : function(){
			saveSysRecords(sysId);
		} 
	});
	var saveButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		text : '选择',
		handler : function(){
			saveSysRecords(sysId);
		} 
	});
	sysStorageGrid = Ext
			.create(
					'Ext.ux.ideal.grid.Panel',
					{
						width : '100%',
						height : '100%',
						cls:'customize_panel_back',
						ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',   
						store : sysStorageStore,
						selModel : selModel,
						border : true,
						columnLines : true,
						flex : 2,
						columns : [
							{
								text : '序号',
								width : 50,
								xtype : 'rownumberer'
							}, {
								text : 'iid',
								dataIndex : 'iid',
								flex : 1,
								hidden : true
							},
							{ text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
					        { text: 'iagentid',  dataIndex: 'iagentid',hidden:true},
					        {
					            text: '主机名称',
					            dataIndex: 'hostName',
					            width: 100
					        },{
					            text: 'IP',
					            dataIndex: 'agentIp',
					            width: 120
					        },{
					            text: '所属区域',
					            dataIndex: 'centerName',
					            width: 100
					        },{
					        	text : '服务名',  
					        	dataIndex: 'isid', 
					        	width: 100
//					        	hidden: flSwitch
					    	},{
					        	text : '纳管资源id',  
					        	dataIndex: 'resourceIid', 
					        	width: 100,
					        	hidden: false
					    	},
					        {text: '系统管理员',  dataIndex: 'sysAdmin',width: 100},
					        {text: '应用管理员',  dataIndex: 'appAdmin',width: 100},
					        {text: '信息系统名称',  dataIndex: 'systemInfo',width: 100},
					        {
					            text: '运行天数',
					            dataIndex: 'runDays',
					            width: 100
					        },
					        {
					            text: '是否双机',
					            dataIndex: 'isHa',
					            width: 100
					        },
					        {
					            text: 'AgentInfo系统类型',
					            dataIndex: 'osTypeAgentinfo',
					            width: 100,
					            hidden:true
					        },
					        {
					            text: '操作系统类型',
					            dataIndex: 'osType',
					            width: 100
					        },
					         {
					            text: '操作系统版本',
					            dataIndex: 'osVersion',
					            width: 100
					        }, 
					        {
					            text: '数据库类型',
					            dataIndex: 'dbType',
					            width: 100
					        },
					        {
					            text: '数据库版本',
					            dataIndex: 'dbVersion',
					            width: 100
					        },
					        {text: '中间件类型',  dataIndex: 'middlewareType',width: 100},
					        {
					            text: '中间件版本',
					            dataIndex: 'middlewareVersion',
					            width: 100
					        },
					        {
					            text: '纳管时间',
					            dataIndex: 'icreateTime',
					            width: 100
					        }, 
					         {
					            text: '纳管用户',
					            dataIndex: 'startUser',
					            width: 100
					        }, 
					        {text: '应用类型',  dataIndex: 'ctype',hidden:true,width: 100},
					        {
					            text: '状态',
					            dataIndex: 'agentState',
					            //flex: 1,
					            width: 110,
					            renderer: function(value, p, record) {
					                var backValue = "";
					                if (value == 0) {
					                    backValue = "Agent正常";
					                } else if (value == 1) {
					                    backValue = "Agent异常";
					                }
					                return backValue;
					            }
					        },
					        {
					            text: 'Agent端口',
					            dataIndex: 'agentPort',
					            width: 100,
					            hidden:true
					        },
					        {
					            text: '环境',
					            dataIndex: 'envType',
					            hidden: true,
					            width: 100,
					            renderer: function(value, p, record) {
					                var backValue = "";
					                if (value == 0) {
					                    backValue = '<font">测试</font>';
					                } else if (value == 1) {
					                    backValue = '<font >生产</font>';
					                }
					                return backValue;
					            }
					        }],
//						dockedItems : [ {
//							xtype : 'toolbar',
//							items : ['->', saveButton,deleteButton ]
//						} ],
						collapsible : false
					});
	/**主机名称**/
    var getHostNameStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'hostName', 'hostName' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getHostName.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
    

	 getHostNameStore.on('beforeload', function(store, options) {
	 var new_params = {
			ienv_type : 1,// 生产
			state : 0// 状态
		
	 };
		Ext.apply(getHostNameStore.proxy.extraParams, new_params);
	 });
    
	var hostName = Ext.create('Ext.form.field.ComboBox', {
		id:'hostNameId',
		store : getHostNameStore,
        anyMatch : true,
		queryMode : 'local',
		width :'25%',
		fieldLabel : '主机名称',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'hostName',
		valueField : 'hostName',
		labelWidth : 93,
		labelAlign : 'right',
		value:hostName,
		emptyText :'--请选择主机名称--',
		triggerAction : "all"
	});
	
	var getIpStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'ip', 'ip' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getIp.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getIpStore.on ('beforeload', function (store, options)
	{ 
	var new_params = {
		ienv_type : 1,//生产
		state : 0//状态
	};
	Ext.apply (getIpStore.proxy.extraParams, new_params);
	});
	
	var agentIp = Ext.create('Ext.form.field.ComboBox', {
		id:'agentIp',
		store : getIpStore,
		queryMode : 'local',
		 anyMatch : true,
		width :'25%',
		fieldLabel : 'IP',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'ip',
		valueField : 'ip',
		labelWidth : 93,
		labelAlign : 'right',
		value :agentIp,
		emptyText :'--请选择IP--',
		triggerAction : "all"
	});
	
	var getsysAdminStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'sysAdmin', 'sysAdmin' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdmin.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getsysAdminStore.on ('beforeload', function (store, options)
	{ 
	var new_params = {
		ienv_type : 1,//生产
		state : 0//状态
	};
	Ext.apply (getsysAdminStore.proxy.extraParams, new_params);
	});
	
	var sysAdmin1 = Ext.create('Ext.form.field.ComboBox', {
		id:'sysAdminId1',
		store : getsysAdminStore,
		 anyMatch : true,
		queryMode : 'local',
		width :'25%',
		fieldLabel : '系统管理员',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'sysAdmin',
		valueField : 'sysAdmin',
		labelWidth : 93,
		labelAlign : 'right',
		value : sysAdmin1,
		emptyText :'--请选择系统管理员--',
		triggerAction : "all"
	});
	
	var centerStore = Ext.create('Ext.data.Store', {
		fields : [ 'centername' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'centerList.do',
			reader : {
				type : 'json',
				root : 'centerlist'
			}
		}
	});
	
	var centerCombo = Ext.create('Ext.form.field.ComboBox', {
		//margin : '5',
   		id:'centerName',
		store : centerStore,
		fieldLabel : '所属区域',  
		queryMode : 'local',
	    width : '25%',
	    labelWidth : 93,
	    labelAlign : 'right',
		forceSelection : true, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'centername',
		valueField : 'centername',
		triggerAction : "all",
		emptyText : "--请选择所属区域--"
	});
	
	
	var getSystemNameStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'systemName', 'systemName' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSystemName.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	getSystemNameStore.on ('beforeload', function (store, options)
			{ 
			var new_params = {
				ienv_type : 1,//生产
				state : 0//状态
			};
			Ext.apply (getSystemNameStore.proxy.extraParams, new_params);
	});
	
	var system4 = Ext.create('Ext.form.field.ComboBox', {
	 	id:'system4',
		store : getSystemNameStore,
		 anyMatch : true,
		queryMode : 'local',
		width :'25%',
		fieldLabel : '信息系统名称',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'systemName',
		valueField : 'systemName',
		labelWidth : 93,
		labelAlign : 'right',
		value:system4,
		emptyText :'--请选择信息系统名称--',
		triggerAction : "all"
	});
	
	var middlewareTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=middleware_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	 var osTypeStore = Ext.create('Ext.data.JsonStore', {
			fields : [ 'collectresult' ],
			autoDestroy : true,
			autoLoad : true,
			proxy : {
				type : 'ajax',
				url : 'getCollectResult.do?keyname=OS_type',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});
	 
	var middleType = Ext.create('Ext.form.field.ComboBox', {
		id:'middleType',
		store : middlewareTypeStore,
		queryMode : 'local',
		fieldLabel :'中间件类型',
		labelAlign : 'right',
		labelWidth:   93,
		width:'25%',
		name : 'middleware_type',
		displayField : 'collectresult',
		valueField : 'collectresult',
		triggerAction : "all",
		emptyText : "--请选择中间件类型--"
	});
	
	var dbTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=db_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var dbType_q = Ext.create('Ext.form.field.ComboBox', {
		id:'dbType_q',
		store : dbTypeStore,
		queryMode : 'local',
		fieldLabel :'数据库类型',
		labelAlign : 'right',
		labelWidth:93,
		width:'25%',
		name : 'db_type',
		displayField : 'collectresult',
		valueField : 'collectresult',
		triggerAction : "all",
		emptyText : "--请选择数据库类型--" 
	});
	
	
	var osType_q = Ext.create('Ext.form.field.ComboBox', {
		id:'osType_q',
		store : osTypeStore,
		queryMode : 'local',
		name : 'OS_type',
		labelAlign : 'right',
		labelWidth: 93,
		width:'25%',
		fieldLabel :'操作系统类型',
		displayField : 'collectresult',
		valueField : 'collectresult',
		triggerAction : "all",
		emptyText : "--请选择操作系统类型--",
		multiSelect : true 
	});
		
	var agentPort = new Ext.form.NumberField({
		
		fieldLabel : 'Agent端口',
		//emptyText : '--请输入主机名--',
		labelWidth : 100,
		width :'33.3%',
        labelAlign : 'right',
        allowDecimals : false,
        //value: '',
        allowNegative : false,
        maxValue : 65535,
        maxText:'值太大',
        minValue : 1,
        minText:'值太小'
	}); 
	
	/**应用类型* */
	var ctStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'ctid', 'ctype' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'ctList.do',
			reader : {
				type : 'json',
				root : 'ctlist'
			}
		}
	});
	
	/** 应用类型条件 */
	var applicationType = Ext.create('Ext.form.field.ComboBox', {
		name : 'applicationType',
		fieldLabel : '应用类型',
		store : ctStore,
		hidden:true,
		queryMode : 'local',
		labelWidth : 100,
		emptyText : '---请选择应用类型---',
		width : '33.3%',
		labelAlign : 'right',
		forceSelection : true, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'ctype',
		valueField : 'ctid',
		listeners : {
			'beforequery' : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var input = e.query;
					// 检索的正则
					var regExp = new RegExp(".*" + input + ".*");
					// 执行检索
					combo.store.filterBy(function(record, id) {
						// 得到每个record的项目名称值
						var text = record.get(combo.displayField);
						return regExp.test(text);
					});
					combo.expand();
					return false;
				}
			}
		}
	});
	
	var icreateTime_q = new Ext.form.field.Date ({
		fieldLabel: '纳管时间',
		labelAlign : 'right',
		labelWidth : 100,
		width:'33.3%',
		name: 'icreateTime_q',
		format : 'Y-m-d'
		//value: ''
	});
	
    var osVersion_q = new Ext.form.TextField(
    		{
    	        fieldLabel: '操作系统版本',
    	        labelAlign : 'right',
    	        labelWidth : 93,
    	        name: 'osVersion_q',
    	        width:'25%',
    	        xtype: 'textfield'
    	    });
    
    var dbVersion_q = new Ext.form.TextField(
    		{
    	        fieldLabel: '数据库版本',
    	        labelAlign : 'right',
    	        labelWidth : 93,
    	        name: 'dbVersion_q',
    	        width:'33.3%',
    	        xtype: 'textfield'
    	    });
	
	function clearQueryWhere1(){
	 	editingChosedAgentIds = new Array();
	 	sysStorageGrid.ipage.moveFirst();
		content.setValue('');
		execuser.setValue('');
		execparams.setValue('');
	}
	
	function queryWhere(){
		console.log("agentListStore.pageSize  :"+agentListStore.pageSize);
		if(agentListStore.pageSize==1000){
			agentListStore.pageSize = 30;
		}
		checkFlag = 0;
		parmFlag = 0;
		if(!(search_form_hidden.getForm().isValid())){
			Ext.Msg.alert('提示', '查询条件存在非法输入！');
			return;
		}
		clearQueryWhere1();
		sysStorageGrid.ipage.moveFirst();
	}
	
	/** form **/
	var search_form_center = Ext.create('Ext.ux.ideal.form.Panel', {
		//id : 'queryform',
		region : 'north',
		iselect : false,
		//layout : 'anchor',
		buttonAlign : 'center',
		width : '100%',
		collapsible : true,//可收缩 
		collapsed : false,//默认收缩
		border : false,
		iqueryFun :queryWhere,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [/**applicationType**/hostName,agentIp,sysAdmin1,centerCombo]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [system4,middleType,osType_q,dbType_q/**middleType,appAdmin4,agentPort**/] //信息采集项
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [
		        '->',
				{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler :function() {
//						if(agentListStore.pageSize==1000){
//							agentListStore.pageSize = 30;
//						}
//						checkFlag = 0;
//						parmFlag = 0;
//						if(!(search_form_hidden.getForm().isValid())){
//							Ext.Msg.alert('提示', '查询条件存在非法输入！');
//	  						return;
//						}
						clearQueryWhere1();
						sysStorageGrid.ipage.moveFirst();
						//agentListStore.load();
					}
				},
				{
					xtype : 'button',
					text : '重置',
					cls : 'Common_Btn',
					handler : function() {
						search_form_center.getForm().reset();
					 	search_form_hidden.getForm().reset();
//					 	agentIpValueS=new Array();
//					 	serverRec = {};
//						serverEquiArr = [];//选中服务器
//						editingChosedAgentIds = new Array();
//					 	agentIpValueCheck="";
					}
				},saveButton,deleteButton
			] 
		}]
	});
	
	var win = Ext.create('widget.window', {
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		modal : true,
		title : '选择设备',
		closable : true,
		//			    closeAction : 'hide',
		closeAction : 'destroy',
		// animateTarget: this,
		width : contentPanel.getWidth ()-200,
		height : contentPanel.getHeight ()-150,
		layout : 'border',
		items : [ search_form_center,sysStorageGrid ]
	});
	win.show();
}

function addSysCapacityRow() {
	sysStorageStore.insert(0, new sysStorageModel());
	sysStorageGrid.getView().refresh();
}

function queryWhereSys() {
	sysStorageGrid.ipage.moveFirst();
}

function saveSysRecords(sysId) {
//	alert(sysId);
	var m = sysStorageGrid.getSelectionModel ().getSelection ();
	if (m.length == '0') {
		Ext.Msg.alert('提示', '没有需要保存的数据！');
		return;
	}

//	var jsonData = "[";
	for (var i = 0, len = m.length; i < len; i++) {
		/*var iname = m[i].get("iname").trim();
		if ('' == iname) {
			Ext.Msg.alert('提示', '存储类型不能为空！');
			return;
		}
		if (fucCheckLength(iname) > 100) {
			Ext.Msg.alert('提示','存储类型不能超过100字符！');
			return;
		}*/

		/*var n = 0;
		for ( var k = 0; k < store.getCount(); k++) {
			var record = store.getAt(k);
			var cname = record.data.iname.trim();
			if (cname == iname) {
				n = n + 1;
			}
		}
		if (n > 1) {
			Ext.MessageBox.alert("提示", "存储类型不能重复！");
			return;
		}*/
//		m[i].data.isysid = sysId;
		var id = m[i].data.iid;
		var ip = m[i].data.agentIp;
		var checkIds ;
		if(i==0){
			checkIds = id
		}else{
			checkIds = checkIds+","+id
		}
		
//		var ss = Ext.JSON.encode(m[i].data);
//		if (i == 0)
//			jsonData = jsonData + ss+"sysId:"+sysId;
//		else
//			jsonData = jsonData + "," + ss;
	}
//	alert(sysId+"%%%%%%%%%"+checkIds);
//	jsonData = jsonData + "]";
	Ext.Ajax.request({
		url : 'faultSelfHealing/saveSelHealingSchemeSelectDeviceList.do',
		method : 'post',
		params : {
			sysId : sysId,
			checkIds : checkIds
		},
		success : function(response, request) {
			var success = Ext.decode(response.responseText).success;
			var message = Ext.decode(response.responseText).message;
			if (success) {
				queryWhereSys();
				Ext.Msg.alert("提示", message);
			} else {
				Ext.Msg.alert("提示", message);
			}
		},
		failure : function(result, request) {
			secureFilterRs(result, "操作失败！", request);

		}
	});
}

/**删除*/
function deleteSysRecords() {
	var data = sysStorageGrid.getView().getSelectionModel().getSelection();
	if (data.length == 0) {
		Ext.Msg.alert('提示', '请至少选择一条记录!');
		return;
	}

	Ext.Msg.confirm("请确认", "确定删除吗？", function(button, text) {
		if (button == "yes") {
			var ids = [];
			for (var i = 0; i < data.length; i++) {
				ids.push(data[i].get('iid'));
			}
			Ext.Ajax.request({
				url : 'infoSysCapacity/deleteSysCapacity.do',
				timeout : 30000,
				params : {
					deleteIds : ids
				},
				method : 'POST',
				success : function(response, opts) {
					var success = Ext.decode(response.responseText).success;
					var message = Ext.decode(response.responseText).message;

					queryWhereSys();
					Ext.Msg.alert('提示',
							Ext.decode(response.responseText).message);
				},
				failure : function(result, request) {
					secureFilterRs(result, "请求返回失败！", request);
				}
			});
		}
	});
}

function showCheckTask(sysId) {
	Ext.define('sysStorageModel', {
		extend : 'Ext.data.Model',
		fields : [ 
			{name : 'iid',type : 'string'},
			{name: 'sysName',  type: 'string'},
			{name: 'centerName',  type: 'string'},
			{name: 'igroupid', type: 'string'},
	        {name: 'iagentid', type: 'string'},
	        {name: 'sysAdmin', type: 'string'},
	        //{name: 'appAdmin', type: 'string'},
	        {name: 'systemInfo', type: 'string'},
	        {name: 'ctype', type: 'string'},
	        {name: 'middlewareType', type: 'string'},
	        { name: 'appName',  type: 'string'},
	        { name: 'hostName', type: 'string'},
	        { name: 'osType',type: 'string'},
	        { name: 'osTypeAgentinfo',type: 'string'},
	        { name: 'agentIp', type: 'string'},
	        {name: 'agentPort',type: 'string'},
	        {name: 'envType',type: 'string'},
	        { name: 'agentState', type: 'int'},
	        {name: 'osVersion',type: 'string'},//系统版本
	        {name: 'runDays',type: 'string'},//运行天数
	        {name: 'isHa',type: 'string'},//是否双机
	        {name: 'dbType',type: 'string'},//数据库类型
	        {name: 'dbVersion',type: 'string'},//数据库版本
	        {name: 'middlewareVersion',type: 'string'},//中间件版本
	        {name: 'icreateTime',type: 'string'},//纳管时间
	        {name: 'startUser',type: 'string'},//纳管启动人
	        {name: 'isid',type: 'string'},//服务名
	        {name: 'resourceIid',type: 'String'}//纳管id
	        ,{name: 'checked', type:'boolean'}
     	]
	});

	/** 复选框 * */
	var selModel = Ext.create ('Ext.selection.CheckboxModel',
    {
	    checkOnly : true
//		mode : 'SIMPLE',
//		listeners : {
//			beforeselect:function( me, record, index, eOpts ){
//				me.deselectAll();
//			}
//		}
    });
	sysStorageStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		//			    remoteSort: true,
		model : 'sysStorageModel',
//		pageSize : 30,
		proxy : {
			type : 'ajax',
			//url : 'infoSysCapacity/querySysStoreList.do',
			url : 'faultSelfHealing/getCheckedSelHealingSchemeSelectDeviceList.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			} 
		}
	});
	
	var middleware_version_q = new Ext.form.TextField({
		fieldLabel: '中间件版本',
        labelAlign : 'right',
        labelWidth : 100,
        name: 'middleware_version_q',
        width:'33.3%',
        xtype: 'textfield'
	});
	sysStorageStore.on('beforeload', function(store, options) {
		var serverIids = "";
		var newparams = {
				sysId : sysId,
				hostName: Ext.getCmp("hostNameId").getValue(),
				agentIps: Ext.getCmp("agentIp").getValue(),
				iagentPort: agentPort.value,
				appliType:applicationType.value,
				sysAdmin:Ext.getCmp("sysAdminId1").getValue(),
				centerName:Ext.getCmp("centerName").getValue(),
				systemInfo:Ext.getCmp("system4").getValue(),
				middleType:Ext.getCmp("middleType").getValue(),
				icreateTime:  icreateTime_q.getRawValue(),
				osType:Ext.getCmp("osType_q").getValue(),
				osVersion : osVersion_q.getValue(),
				dbtype : Ext.getCmp("dbType_q").getValue(),
			    db_version : dbVersion_q.getValue(),
			    equiSelectedIds : serverIids,
			    middleware_version:middleware_version_q.getValue(),
			    opType:2,
			    opTypeNum:""
		};

		Ext.apply(store.proxy.extraParams, newparams);

	});
	sysStorageStore.on ('load', function (store, options)
			{
				//选中ip，复选框勾选
				for (var i = 0; i < store.getCount (); i++)
				{
					var record = store.getAt (i);
					if(true==record.data.checked){
						selModel.select(record,true,false);
					}
				}
			});
	/** 组名查询输入框* */
	var taskNameForQuery = Ext.create ('Ext.form.TextField',
	{
	    emptyText : '--请输入任务名称--',
	    labelWidth : 80,
	    width : '30%',
	    xtype : 'textfield'
	});
	/** 查询按钮* */
	var taskQryButton = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "查询",
//	    handler : query
	    handler : function()
	    {
	    	groupGrid.ipage.moveFirst();
	    }
	});
	var deleteButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		text : '移除',
		handler : function(){
			saveSysRecords(sysId);
		} 
	});
	var saveButton = Ext.create("Ext.Button", {
		cls : 'Common_Btn',
		text : '选择',
		handler : function(){
			saveSysRecords(sysId);
		} 
	});
	var gridselModel = Ext.create('Ext.selection.CheckboxModel', {
//		id:'selModel',
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
	sysStorageGrid = Ext
			.create(
					'Ext.ux.ideal.grid.Panel',
					{
						width : '100%',
						height : '100%',
						cls:'customize_panel_back',
						ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',   
						store : sysStorageStore,
//						selModel : gridselModel,
						border : true,
						columnLines : true,
						flex : 2,
						columns : [
							{
								text : '序号',
								width : 50,
								xtype : 'rownumberer'
							}, {
								text : 'iid',
								dataIndex : 'iid',
								flex : 1,
								hidden : true
							},
							{ text: 'igroupid',  dataIndex: 'igroupid',hidden:true},
					        { text: 'iagentid',  dataIndex: 'iagentid',hidden:true},
					        {
					            text: '主机名称',
					            dataIndex: 'hostName',
					            width: 100
					        },{
					            text: 'IP',
					            dataIndex: 'agentIp',
					            width: 120
					        },{
					            text: '所属区域',
					            dataIndex: 'centerName',
					            width: 100
					        },{
					        	text : '服务名',  
					        	dataIndex: 'isid', 
					        	width: 100
//					        	hidden: flSwitch
					    	},{
					        	text : '纳管资源id',  
					        	dataIndex: 'resourceIid', 
					        	width: 100,
					        	hidden: false
					    	},
					        {text: '系统管理员',  dataIndex: 'sysAdmin',width: 100},
					        {text: '应用管理员',  dataIndex: 'appAdmin',width: 100},
					        {text: '信息系统名称',  dataIndex: 'systemInfo',width: 100},
					        {
					            text: '运行天数',
					            dataIndex: 'runDays',
					            width: 100
					        },
					        {
					            text: '是否双机',
					            dataIndex: 'isHa',
					            width: 100
					        },
					        {
					            text: 'AgentInfo系统类型',
					            dataIndex: 'osTypeAgentinfo',
					            width: 100,
					            hidden:true
					        },
					        {
					            text: '操作系统类型',
					            dataIndex: 'osType',
					            width: 100
					        },
					         {
					            text: '操作系统版本',
					            dataIndex: 'osVersion',
					            width: 100
					        }, 
					        {
					            text: '数据库类型',
					            dataIndex: 'dbType',
					            width: 100
					        },
					        {
					            text: '数据库版本',
					            dataIndex: 'dbVersion',
					            width: 100
					        },
					        {text: '中间件类型',  dataIndex: 'middlewareType',width: 100},
					        {
					            text: '中间件版本',
					            dataIndex: 'middlewareVersion',
					            width: 100
					        },
					        {
					            text: '纳管时间',
					            dataIndex: 'icreateTime',
					            width: 100
					        }, 
					         {
					            text: '纳管用户',
					            dataIndex: 'startUser',
					            width: 100
					        }, 
					        {text: '应用类型',  dataIndex: 'ctype',hidden:true,width: 100},
					        {
					            text: '状态',
					            dataIndex: 'agentState',
					            //flex: 1,
					            width: 110,
					            renderer: function(value, p, record) {
					                var backValue = "";
					                if (value == 0) {
					                    backValue = "Agent正常";
					                } else if (value == 1) {
					                    backValue = "Agent异常";
					                }
					                return backValue;
					            }
					        },
					        {
					            text: 'Agent端口',
					            dataIndex: 'agentPort',
					            width: 100,
					            hidden:true
					        },
					        {
					            text: '环境',
					            dataIndex: 'envType',
					            hidden: true,
					            width: 100,
					            renderer: function(value, p, record) {
					                var backValue = "";
					                if (value == 0) {
					                    backValue = '<font">测试</font>';
					                } else if (value == 1) {
					                    backValue = '<font >生产</font>';
					                }
					                return backValue;
					            }
					        }],
//						dockedItems : [ {
//							xtype : 'toolbar',
//							items : ['->', saveButton,deleteButton ]
//						} ],
						collapsible : false
					});
	/**主机名称**/
    var getHostNameStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'hostName', 'hostName' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getHostName.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
    });
    

	 getHostNameStore.on('beforeload', function(store, options) {
	 var new_params = {
			ienv_type : 1,// 生产
			state : 0// 状态
		
	 };
		Ext.apply(getHostNameStore.proxy.extraParams, new_params);
	 });
    
	var hostName = Ext.create('Ext.form.field.ComboBox', {
		id:'hostNameId',
		store : getHostNameStore,
        anyMatch : true,
		queryMode : 'local',
		width :'25%',
		fieldLabel : '主机名称',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'hostName',
		valueField : 'hostName',
		labelWidth : 93,
		labelAlign : 'right',
		value:hostName,
		emptyText :'--请选择主机名称--',
		triggerAction : "all"
	});
	
	var getIpStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'ip', 'ip' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getIp.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getIpStore.on ('beforeload', function (store, options)
	{ 
	var new_params = {
		ienv_type : 1,//生产
		state : 0//状态
	};
	Ext.apply (getIpStore.proxy.extraParams, new_params);
	});
	
	var agentIp = Ext.create('Ext.form.field.ComboBox', {
		id:'agentIp',
		store : getIpStore,
		queryMode : 'local',
		 anyMatch : true,
		width :'25%',
		fieldLabel : 'IP',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'ip',
		valueField : 'ip',
		labelWidth : 93,
		labelAlign : 'right',
		value :agentIp,
		emptyText :'--请选择IP--',
		triggerAction : "all"
	});
	
	var getsysAdminStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'sysAdmin', 'sysAdmin' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSysAdmin.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	getsysAdminStore.on ('beforeload', function (store, options)
	{ 
	var new_params = {
		ienv_type : 1,//生产
		state : 0//状态
	};
	Ext.apply (getsysAdminStore.proxy.extraParams, new_params);
	});
	
	var sysAdmin1 = Ext.create('Ext.form.field.ComboBox', {
		id:'sysAdminId1',
		store : getsysAdminStore,
		 anyMatch : true,
		queryMode : 'local',
		width :'25%',
		fieldLabel : '系统管理员',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'sysAdmin',
		valueField : 'sysAdmin',
		labelWidth : 93,
		labelAlign : 'right',
		value : sysAdmin1,
		emptyText :'--请选择系统管理员--',
		triggerAction : "all"
	});
	
	var centerStore = Ext.create('Ext.data.Store', {
		fields : [ 'centername' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'centerList.do',
			reader : {
				type : 'json',
				root : 'centerlist'
			}
		}
	});
	
	var centerCombo = Ext.create('Ext.form.field.ComboBox', {
		//margin : '5',
   		id:'centerName',
		store : centerStore,
		fieldLabel : '所属区域',  
		queryMode : 'local',
	    width : '25%',
	    labelWidth : 93,
	    labelAlign : 'right',
		forceSelection : true, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'centername',
		valueField : 'centername',
		triggerAction : "all",
		emptyText : "--请选择所属区域--"
	});
	
	
	var getSystemNameStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'systemName', 'systemName' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'getSystemName.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	getSystemNameStore.on ('beforeload', function (store, options)
			{ 
			var new_params = {
				ienv_type : 1,//生产
				state : 0//状态
			};
			Ext.apply (getSystemNameStore.proxy.extraParams, new_params);
	});
	
	var system4 = Ext.create('Ext.form.field.ComboBox', {
	 	id:'system4',
		store : getSystemNameStore,
		 anyMatch : true,
		queryMode : 'local',
		width :'25%',
		fieldLabel : '信息系统名称',
		forceSelection : false, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'systemName',
		valueField : 'systemName',
		labelWidth : 93,
		labelAlign : 'right',
		value:system4,
		emptyText :'--请选择信息系统名称--',
		triggerAction : "all"
	});
	
	var middlewareTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=middleware_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});

	 var osTypeStore = Ext.create('Ext.data.JsonStore', {
			fields : [ 'collectresult' ],
			autoDestroy : true,
			autoLoad : true,
			proxy : {
				type : 'ajax',
				url : 'getCollectResult.do?keyname=OS_type',
				reader : {
					type : 'json',
					root : 'dataList'
				}
			}
		});
	 
	var middleType = Ext.create('Ext.form.field.ComboBox', {
		id:'middleType',
		store : middlewareTypeStore,
		queryMode : 'local',
		fieldLabel :'中间件类型',
		labelAlign : 'right',
		labelWidth:   93,
		width:'25%',
		name : 'middleware_type',
		displayField : 'collectresult',
		valueField : 'collectresult',
		triggerAction : "all",
		emptyText : "--请选择中间件类型--"
	});
	
	var dbTypeStore = Ext.create('Ext.data.JsonStore', {
		fields : [ 'collectresult' ],
		autoDestroy : true,
		autoLoad : true,
		proxy : {
			type : 'ajax',
			url : 'getCollectResult.do?keyname=db_type',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	
	var dbType_q = Ext.create('Ext.form.field.ComboBox', {
		id:'dbType_q',
		store : dbTypeStore,
		queryMode : 'local',
		fieldLabel :'数据库类型',
		labelAlign : 'right',
		labelWidth:93,
		width:'25%',
		name : 'db_type',
		displayField : 'collectresult',
		valueField : 'collectresult',
		triggerAction : "all",
		emptyText : "--请选择数据库类型--" 
	});
	
	
	var osType_q = Ext.create('Ext.form.field.ComboBox', {
		id:'osType_q',
		store : osTypeStore,
		queryMode : 'local',
		name : 'OS_type',
		labelAlign : 'right',
		labelWidth: 93,
		width:'25%',
		fieldLabel :'操作系统类型',
		displayField : 'collectresult',
		valueField : 'collectresult',
		triggerAction : "all",
		emptyText : "--请选择操作系统类型--",
		multiSelect : true 
	});
		
	var agentPort = new Ext.form.NumberField({
		
		fieldLabel : 'Agent端口',
		//emptyText : '--请输入主机名--',
		labelWidth : 100,
		width :'33.3%',
        labelAlign : 'right',
        allowDecimals : false,
        //value: '',
        allowNegative : false,
        maxValue : 65535,
        maxText:'值太大',
        minValue : 1,
        minText:'值太小'
	}); 
	
	/**应用类型* */
	var ctStore = Ext.create('Ext.data.JsonStore', {
		// fields : [ 'ctype' ],
		fields : [ 'ctid', 'ctype' ],
		autoLoad : true,
		autoDestroy : true,
		proxy : {
			type : 'ajax',
			url : 'ctList.do',
			reader : {
				type : 'json',
				root : 'ctlist'
			}
		}
	});
	
	/** 应用类型条件 */
	var applicationType = Ext.create('Ext.form.field.ComboBox', {
		name : 'applicationType',
		fieldLabel : '应用类型',
		store : ctStore,
		hidden:true,
		queryMode : 'local',
		labelWidth : 100,
		emptyText : '---请选择应用类型---',
		width : '33.3%',
		labelAlign : 'right',
		forceSelection : true, // 要求输入值必须在列表中存在
		//typeAhead : true, // 允许自动选择
		displayField : 'ctype',
		valueField : 'ctid',
		listeners : {
			'beforequery' : function(e) {
				var combo = e.combo;
				if (!e.forceAll) {
					var input = e.query;
					// 检索的正则
					var regExp = new RegExp(".*" + input + ".*");
					// 执行检索
					combo.store.filterBy(function(record, id) {
						// 得到每个record的项目名称值
						var text = record.get(combo.displayField);
						return regExp.test(text);
					});
					combo.expand();
					return false;
				}
			}
		}
	});
	
	var icreateTime_q = new Ext.form.field.Date ({
		fieldLabel: '纳管时间',
		labelAlign : 'right',
		labelWidth : 100,
		width:'33.3%',
		name: 'icreateTime_q',
		format : 'Y-m-d'
		//value: ''
	});
	
    var osVersion_q = new Ext.form.TextField(
    		{
    	        fieldLabel: '操作系统版本',
    	        labelAlign : 'right',
    	        labelWidth : 93,
    	        name: 'osVersion_q',
    	        width:'25%',
    	        xtype: 'textfield'
    	    });
    
    var dbVersion_q = new Ext.form.TextField(
    		{
    	        fieldLabel: '数据库版本',
    	        labelAlign : 'right',
    	        labelWidth : 93,
    	        name: 'dbVersion_q',
    	        width:'33.3%',
    	        xtype: 'textfield'
    	    });
	
	function clearQueryWhere1(){
	 	editingChosedAgentIds = new Array();
	 	sysStorageGrid.ipage.moveFirst();
		content.setValue('');
		execuser.setValue('');
		execparams.setValue('');
	}
	
	function queryWhere(){
		console.log("agentListStore.pageSize  :"+agentListStore.pageSize);
		if(agentListStore.pageSize==1000){
			agentListStore.pageSize = 30;
		}
		checkFlag = 0;
		parmFlag = 0;
		if(!(search_form_hidden.getForm().isValid())){
			Ext.Msg.alert('提示', '查询条件存在非法输入！');
			return;
		}
		clearQueryWhere1();
		sysStorageGrid.ipage.moveFirst();
	}
	
	/** form **/
	var search_form_center = Ext.create('Ext.ux.ideal.form.Panel', {
		//id : 'queryform',
		region : 'north',
		iselect : false,
		//layout : 'anchor',
		buttonAlign : 'center',
		width : '100%',
		collapsible : true,//可收缩 
		collapsed : false,//默认收缩
		border : false,
		iqueryFun :queryWhere,
		dockedItems : [ {
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [/**applicationType**/hostName,agentIp,sysAdmin1,centerCombo]
		},{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [system4,middleType,osType_q,dbType_q/**middleType,appAdmin4,agentPort**/] //信息采集项
		}
		,
		{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items: [
		        '->',
				{
					xtype : 'button',
					text : '查询',
					cls : 'Common_Btn',
					handler :function() {
//						if(agentListStore.pageSize==1000){
//							agentListStore.pageSize = 30;
//						}
//						checkFlag = 0;
//						parmFlag = 0;
//						if(!(search_form_hidden.getForm().isValid())){
//							Ext.Msg.alert('提示', '查询条件存在非法输入！');
//	  						return;
//						}
						clearQueryWhere1();
						sysStorageGrid.ipage.moveFirst();
						//agentListStore.load();
					}
				},
				{
					xtype : 'button',
					text : '重置',
					cls : 'Common_Btn',
					handler : function() {
						search_form_center.getForm().reset();
					 	search_form_hidden.getForm().reset();
//					 	agentIpValueS=new Array();
//					 	serverRec = {};
//						serverEquiArr = [];//选中服务器
//						editingChosedAgentIds = new Array();
//					 	agentIpValueCheck="";
					}
				}
//				,saveButton,deleteButton
			] 
		}
		]
	});
	
	var win = Ext.create('widget.window', {
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		modal : true,
		title : '选择设备',
		closable : true,
		//			    closeAction : 'hide',
		closeAction : 'destroy',
		// animateTarget: this,
		width : contentPanel.getWidth ()-200,
		height : contentPanel.getHeight ()-150,
		layout : 'border',
		items : [ search_form_center,sysStorageGrid ]
	});
	win.show();
}
