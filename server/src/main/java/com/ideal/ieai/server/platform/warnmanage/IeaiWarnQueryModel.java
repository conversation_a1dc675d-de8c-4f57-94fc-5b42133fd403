package com.ideal.ieai.server.platform.warnmanage;


public class IeaiWarnQueryModel
{

    private String imodulecode;
    private String itypecode;
    private String ilevelcode;
    private String iwarnmsg;
    private String istarttime;
    private String iendtime;
    private String queryiip;
    private String querysystem;
    
    private Integer start;
    private Integer limit;
    public String getImodulecode ()
    {
        return imodulecode;
    }
    public void setImodulecode ( String imodulecode )
    {
        this.imodulecode = imodulecode;
    }
    public String getItypecode ()
    {
        return itypecode;
    }
    public void setItypecode ( String itypecode )
    {
        this.itypecode = itypecode;
    }
    public String getIlevelcode ()
    {
        return ilevelcode;
    }
    public void setIlevelcode ( String ilevelcode )
    {
        this.ilevelcode = ilevelcode;
    }
    public String getIwarnmsg ()
    {
        return iwarnmsg;
    }
    public void setIwarnmsg ( String iwarnmsg )
    {
        this.iwarnmsg = iwarnmsg;
    }
    public Integer getStart ()
    {
        return start;
    }
    public String getQueryiip ()
    {
        return queryiip;
    }
    public void setQueryiip ( String queryiip )
    {
        this.queryiip = queryiip;
    }
    public String getQuerysystem ()
    {
        return querysystem;
    }
    public void setQuerysystem ( String querysystem )
    {
        this.querysystem = querysystem;
    }
    public void setStart ( Integer start )
    {
        this.start = start;
    }
    public Integer getLimit ()
    {
        return limit;
    }
    public void setLimit ( Integer limit )
    {
        this.limit = limit;
    }
    public String getIstarttime ()
    {
        return istarttime;
    }
    public void setIstarttime ( String istarttime )
    {
        this.istarttime = istarttime;
    }
    public String getIendtime ()
    {
        return iendtime;
    }
    public void setIendtime ( String iendtime )
    {
        this.iendtime = iendtime;
    }
    
    
    
    
}
