/*******************************************************************************
 * 流程启动列表
 ******************************************************************************/
var scriptNameObj;
var scriptNameStore;
var scriptContentObj;
var stepNameObj;
var flowStore;
var queryForm = null;
var FlowMainPanel=null;
var templateTyeObj=null;
var level1Combo=null;
var level2Combo=null;
Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	/** 树数据Model* */
	Ext.define ('flowModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'iid',
	                type : 'string'
	            },{
	                name : 'INAME',
	                type : 'string'
	            },	            {
	                name : 'itype',
	                type : 'string'
	            },	            {
	                name : 'ILEVEL1TYPE',
	                type : 'string'
	            },	            {
	                name : 'IINSTANCEID',
	                type : 'string'
	            },	            {
	                name : 'a6',
	                type : 'string'
	            },	            {
	                name : 'forwordType',
	                type : 'string'
	            }
	            
	            
	    ]
	});
	
	var templateTyeStore = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "变更管理",
			"name" : "变更管理"
		}, {
			"id" : "灾备切换",
			"name" : "灾备切换"
		}, {
			"id" : "应急操作",
			"name" : "应急操作"
		}, {
			"id" : "云管",
			"name" : "云管"
		} , {
			"id" : "网络自动化变更",
			"name" : "网络自动化变更"
		}//,
		//{
		//	"id" : "网络自动化应急",
		//	"name" : "网络自动化应急"
		//}    
		]
	});
	
	var templateTyeStoreSave = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : "变更管理",
			"name" : "变更管理"
		}, {
			"id" : "灾备切换",
			"name" : "灾备切换"
		}, {
			"id" : "应急操作",
			"name" : "应急操作"
		}, {
			"id" : "云管",
			"name" : "云管"
		} , {
			"id" : "网络自动化变更",
			"name" : "网络自动化变更"
		}   
		]
	});
	var l1_Store = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : 3,
			"name" : "变更一级"
		}, {
			"id" : 4,
			"name" : "灾备一级"
		}, {
			"id" : 6,
			"name" : "应急一级"
		}  ]
	});
	
	var l2_Store = Ext.create('Ext.data.Store', {
		fields : [ 'id', 'name' ],
		data : [ {
			"id" : 3,
			"name" : "变更二级"
		}, {
			"id" : 4,
			"name" : "灾备二级"
		}, {
			"id" : 6,
			"name" : "应急二级"
		}  ]
	});
	
	templateTyeObj = Ext.create ('Ext.form.field.ComboBox',
	{
	    fieldLabel : '适用范围',
	    labelWidth : 65,
	    width : 300,
	    height:34,
	    store : templateTyeStore,
	    displayField : 'name',
	    valueField : 'id',
	    triggerAction : 'all',
	    editable : false,
	    padding : '10 10 0 10' ,
	    mode : 'local'
	});
	
	
	level1Combo = Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '一级分类',
			    labelWidth : 65,
			    hidden:true,
			    width : '15%',
			    height:34,
			    store : l1_Store,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    padding : '10 10 0 10' ,
			    mode : 'local'
			});
	
	level2Combo = Ext.create ('Ext.form.field.ComboBox',
			{
			    fieldLabel : '二级分类',
			    hidden:true,
			    labelWidth : 65,
			    width : '15%',
			    height:34,
			    store : l2_Store,
			    displayField : 'name',
			    valueField : 'id',
			    triggerAction : 'all',
			    editable : false,
			    padding : '10 10 0 10' ,
			    mode : 'local'
			});
	 queryForm = Ext.create('Ext.form.Panel', {
		region: 'north',
		//height: 90,
		border : true,
		//bodyCls: 'service_platform_bodybg',
		collapsible : true,//可收缩
		collapsed : false,//默认收缩
	    layout: 'form',
	    items: [{
	      layout:'column',
	      border : false,
	      items: [
          {
	        labelWidth : 80,
	        labelAlign : 'right',
	        width: 300,
	        xtype: 'textfield',
	        fieldLabel: '环境名称',
	        name: 'envName',
	        padding : '10 10 0 10' 
	      },templateTyeObj,
	      	level1Combo,level2Combo
	    ]
	    }  	,{
			xtype : 'toolbar',
			border : false,
			dock : 'top',
			items:['->',
			      	
				      {
				    	name: 'queryButton',
				        xtype: 'button',
				        text: '查询',
				        cls : 'Common_Btn',
				        margin : '10 10 0 0',
				        handler: function() {
				          queryWhere();
				        }
				      },{
				    		xtype: 'button',
			    	        text: '新增',
			    	        cls : 'Common_Btn',
			    	        margin : '10 10 0 0',
							handler : function() {
								var p = Ext.create ('flowModel',
									{
									    iid:0,
									    INAME:""
									});									
									flowStore.insert (0, p);// 在第一个位置插入
									//cellEditing.startEdit (0, 2);// 指定的行/列，进行单元格内容的编辑
								return;
							}
				    	},{
				    		xtype: 'button',
				    		cls : 'Common_Btn',
							text : '删除',
							 margin : '10 10 0 0',
							handler : function(){
								//Ext.Msg.alert("提示","表格中选中记录被删除!");
								deleteEnv();
								return;
							}
				    	},{
				    		xtype: 'button',
				    		cls : 'Common_Btn',
							text : '保存',
							 margin : '10 10 0 0',
							handler : function(){
								saveEnv();
								return;
							}
				    	},{
				    		xtype: 'button',
							text : '返回',
							cls : 'Common_Btn',
							hidden:backFlag?false:true,
							 margin : '10 10 0 0',
							handler : function(){
								destroyRubbish ();
								Ext.destroy (FlowMainPanel);
								contentPanel.getHeader().show();//让contentPanel显示标题头
								
								contentPanel.setTitle ('模板维护');
								contentPanel.getLoader ().load (
								{
									url : 'accessPaasSysList.do',
								    params :
								    {
								    	 insName:insName,
										 mtype:mtype,
										 itypeId:itypeId,
										 itype:itype
								    },
								    scripts : true
								});
								
								if (Ext.isIE)
								{
									CollectGarbage ();
								}
							}
				    	}]
      }]
	  });
	contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
	queryForm.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
//	   queryFormPanel=Ext.create('Ext.Panel', {
//		  	hidden:false,
//		  	//region : 'north',
//			witdh:'100%',
//			layout:'fit',
//			border : false,	
//			items: [queryForm]
//		});
//	
	/** 树数据源* */
	flowStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'flowModel',
	    pageSize : 50,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getPaasEnvList.do',//'getPassTestData.do?dataType=getEnvList'
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	flowStore.on ('beforeload', function (store, options)
	 {
		 var envname = queryForm.getForm().findField("envName").getValue();//环境名称
		 var envtype= ''//templateTyeObj.getValue();//环境类别
		 var iscope = templateTyeObj.getValue();//适用范围
		 if (iscope== "变更管理") {
			   iscope=3;
			}else if (iscope== "灾备切换") {
				iscope=4;
			}else if (iscope== "应急操作"){
				iscope=6;
			}else if (iscope== "云管")
			{
				iscope=10;
			}else if (iscope== "网络自动化变更")
			{
				iscope=11;
			}else if (iscope== "网络自动化应急")
			{
				iscope=12;
			}
		 var new_params =
		 {
			 envname:envname,
			 envtype:envtype,
			 scope:iscope	 
		 };
		 Ext.apply (flowStore.proxy.extraParams, new_params);
	 });
	/** 树列表columns* */
	var flowColumns = [
	        {
	            text : '序号',
	            // align : 'center',
	            width : 65,
	            xtype : 'rownumberer'
	        },
	        {
	            text : 'iid',
	            dataIndex : 'iid',
	            flex : 1,
	            hidden:true
	        },
	        {
	            text : '环境名称',
	            dataIndex : 'INAME',
	            flex : 1,
	            editor : {
	    			allowBlank : true
	    		}
	        },{
	            text : '环境分类',
	            dataIndex : 'ILEVEL1TYPE',
	            flex : 1,
	            editor : {
	    			allowBlank : true
	    		}
	        },
	        {
	            text : '适用范围',
	            dataIndex : 'itype',
	            flex : 1,
	            editor : new Ext.form.field.ComboBox({
	    			triggerAction : 'all',// 用all表示把下拉框列表框的列表值全部显示出来
	    			editable : false,// 是否可输入编辑
	    			store : templateTyeStoreSave,
	    			displayField : 'name',
	    			valueField : 'id'
	    		}),
	    		 renderer : function(value, metaData, record) {
						if (value == 3) {
							return "变更管理";
						} else if (value == 4) {
							return "灾备切换";
						}else if (value == 6){
							return "应急操作";
						}else if (value == 10)
						{
							return "云管";
						}else if (value == 11)
						{
							return "网络自动化变更";
						}else if (value == 12)
						{
							return "网络自动化应急";
						}else
						{
							return value;
						}
					}
	        },
	        /*
	        {
                text : '二级分类',
                dataIndex : 'a4',
                flex : 1
	        },*/
	        {
	            text : '操作',
	            dataIndex : 'instanceName',
	            sortable : false,
	            width : 150,
	            align : 'center',
	            renderer : function (value, metaData, record)
	            {
	            	var iid = record.get("iid");
		            return  '<a href="javascript:void(0);"  style="text-decoration: underline;" onclick="onViewHostWin('+iid+')">绑定设备</a>';
                            
	            }
	        }
	];
	var bsPageBar = Ext.create ('Ext.PagingToolbar',
	{
	    store : flowStore,
	    dock : 'bottom',
	    displayInfo : true,
        //baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        border : false,
	    emptyMsg : "没有记录"
	});
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		//clicksToEdit : 0
	});
	/** 树列表panel* */
	var flowGrid = Ext.create ('Ext.grid.Panel',
	{
		region : 'center',
//		width : '100%',
//	    height : contentPanel.getHeight() - 115,
	    //extend : 'Ext.grid.Panel',
	    //alias : 'widget.ideapanel',
	    //ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    //cls:'customize_panel_back',
	    store : flowStore,
	    selModel : Ext.create ('Ext.selection.CheckboxModel',
	    {
	    	checkOnly : true
	    }),
	    border : true,
	    //padding : grid_margin,
	    columns : flowColumns,
	   // animCollapse : false,
	    plugins : [ cellEditing ],
	    bbar : bsPageBar,
		listeners : {}
	});
	// 主Panel
 FlowMainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "mainpanelForStart",
	    layout : 'border',
	    //width : '100%',
	    width : contentPanel.getWidth (),//'100%',
	    height : contentPanel.getHeight (),
	    border : true,
	   // bodyCls: 'service_platform_bodybg',
	    //cls:'customize_panel_header_arrow',
//	    bodyPadding : 5,
	    //layout : 'border',
	    items : [
		    queryForm,flowGrid
	    ]
	});
	
 $("body").off('keydown').on('keydown',function(event) {
	    if (event.keyCode == "13") { 
	    	queryWhere();
	    }
	});
 
	contentPanel.getLoader ().on ('beforeload', function (obj, options, eOpts)
	{
		Ext.destroy (FlowMainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	/** 查询条件清空 */
	function resetWhere(){
		queryForm.getForm().findField("instanceName").setValue('');
	}
	
	function queryWhere(){
		bsPageBar.moveFirst();
	}
	function saveEnv()
	{
		var m = flowStore.getModifiedRecords();
		if (m.length < 1) {
			Ext.Msg.alert('提示', '无修改数据！');
			return;
		}
		var jsonData = "[";
		for (var i = 0, len = m.length; i < len; i++) {
			
			var envname= m[i].get("INAME").trim();

			var itype = m[i].get("itype").trim();

			var ILEVEL1TYPE = m[i].get("ILEVEL1TYPE").trim();
			if(envname=='')
		    {
				Ext.Msg.alert('提示', '环境名称不能为空！');
				return;
		    }
			if (fucCheckLength(envname)> 255) {
				Ext.Msg.alert('提示', '环境名称不能超过255字符！');
				return;
			}
			if (itype=='') {
				Ext.Msg.alert('提示', '请选择适用范围！');
				return;
			}
			if (itype== "变更管理") {
				m[i].set("itype",3)
			}else if (itype== "灾备切换") {
				m[i].set("itype",4)
			}else if (itype== "应急操作"){
				m[i].set("itype",6)
			}else if (itype== "云管")
			{
				m[i].set("itype",10)
			}else if (itype== "网络自动化变更")
			{
				m[i].set("itype",11)
			}else if (itype== "网络自动化应急")
			{
				m[i].set("itype",12)
			}
			if (fucCheckLength(ILEVEL1TYPE) > 255) {
				Ext.Msg.alert('提示', '分类不能超过255字符！');
				return;
			}
			var ss = Ext.JSON.encode(m[i].data);			
			if (i == 0)
				jsonData = jsonData + ss;
			else
				jsonData = jsonData + "," + ss;
		}
		jsonData = jsonData + "]";
        var saveUrl = "savePaasEnv.do";
        Ext.MessageBox.wait("数据处理中...", "进度条");
		Ext.Ajax.request({
			url : saveUrl,
			timeout : 30000,
			params : {
				incdata : jsonData,
			},
			method : 'POST',
			success : function(response, opts) {
				var success = Ext.decode(response.responseText).success;
				// 当后台数据同步成功时
				if (success) {
					bsPageBar.moveFirst();
				}
				Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
			},
			failure : function(response, ooptions) {
				Ext.MessageBox.hide();
		    	secureFilterRs(response,"操作失败！");
			}
		});
	}
	function deleteEnv()
	{
		var record = flowGrid.getSelectionModel().getSelection();
		if(record.length==0)
		{
			Ext.MessageBox.alert("提示", "请选择要删除的数据");
			return;
		}else{
            Ext.Msg.confirm('请确认', '是否真的要删除数据？',
                    function(button, text) {
                    if (button == 'yes') {
                        	Ext.Msg.alert('提示', '删除成功！')
							var iidStr = "";
							Ext.Array.each (record, function (recordObj)
							{
								iidStr+=recordObj.get ('iid')+",";
							});
							Ext.Ajax.request({
							    url : 'deletePaasEnv.do',//scriptService/cancelShare.do
							    method : 'POST',
							    params : {
							  	  iidStr:iidStr
							    },
							    success: function(response, opts) {
						    		var message = '操作成功！';
						    		Ext.MessageBox.show({
						                title : "提示",
						                msg : message,
						                buttonText: {
						                    yes: '确定'
						                },
						                buttons: Ext.Msg.YES
						              });
						    		flowStore.reload();
							        
							    },
							    failure: function(result, request) {
							    	secureFilterRs(result,"操作失败！");
							    }
						    });
                 }
            })
		 }
	}
	contentPanel.on ('resize', function ()
	{
		FlowMainPanel.setWidth(contentPanel.getWidth ());
		FlowMainPanel.setHeight (contentPanel.getHeight()-2);
//		autoResize(flowGrid,queryFormPanel,FlowMainPanel,contentPanel);
		//FlowMainPanel.setWidth ('100%');
		//FlowMainPanel.setHeight (contentPanel.getHeight () - staticHeight);
	});
	
	function trim (t)
	{
		t = t.replace (/(^\s*)|(\s*$)/g, "");
		return t.replace (/(^ *)|( *$)/g, "");
	}
	
	
});

function publish2(){
	Ext.Msg.alert("提示","功能开发中,敬请期待!");
	return;
}

function publish(){
	Ext.Msg.alert ('提示', "发布成功!");
	return;
}

	function forward(modelType)
	{
		var _title="";
		destroyRubbish ();
		Ext.destroy (FlowMainPanel);
		contentPanel.getHeader().show();//让contentPanel显示标题头
		var url='';
		
		
		if(modelType==3)
		{
			_title="应用变更系统维护";
			url='accesspaasExcelShow_SUSPage2.do'
		}else if(modelType==4)
		{
			_title="灾备切换系统维护";
			url='accesspaasExcelShow_ZBPage.do';
		}else if(modelType==6)
		{
			_title="应急操作系统维护";
			url='accesspaasExcelShow_YJPage.do';
		}
		
		contentPanel.setTitle (_title);
		contentPanel.getLoader ().load (
		{
			url : url,
		    params :
		    {
		    },
		    scripts : true
		});
		
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
  }
	
	function onViewHostWin(iid){
		var forword_url='paasnewBER.do?envid='+iid;
		var targetContentTiel=("设备信息：");
		win = Ext.create('widget.window', {
			title : targetContentTiel,
			closable : true,
			closeAction : 'destroy',
			width : contentPanel.getWidth(),
			minWidth : 350,
			height : contentPanel.getHeight(),
			draggable : false,// 禁止拖动
			resizable : false,// 禁止缩放
			modal : true,
			loader : {}
		});

		win.getLoader().load(
				{
					url : forword_url,
					params : {

					},
					autoLoad : true,
					scripts : true
				});
		pubResName = "";
		win.setTitle(targetContentTiel);
		win.show();
//		win.show(this, function() {
//		});
	}