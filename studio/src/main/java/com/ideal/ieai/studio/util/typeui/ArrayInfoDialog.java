package com.ideal.ieai.studio.util.typeui;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.GridLayout;
import java.awt.Insets;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.ItemEvent;
import java.awt.event.ItemListener;
import java.util.HashMap;
import java.util.Map;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JSpinner;
import javax.swing.JTextField;
import javax.swing.SpinnerNumberModel;
import javax.swing.event.ChangeEvent;
import javax.swing.event.ChangeListener;

import com.ideal.ieai.core.basis.InvalidTypeException;
import com.ideal.ieai.core.data.ArrayType;
import com.ideal.ieai.core.data.DataTypeFactory;
import com.ideal.ieai.core.data.IDataType;
import com.ideal.ieai.core.element.IEAISchemaDef;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.util.SchemaHelper;
import com.ideal.ieai.studio.StudioRes;
import com.ideal.ieai.studio.core.ProjectManager;
import com.ideal.ieai.studio.core.StudioException;
import org.apache.log4j.Logger;
/**
 * <p>Title: </p>
 * <p>Description:It is a dialog for selecting the type and dimension of
 * an Array. Then, construct an ArrayType instance.</p>
 * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR>
 * @version 3.0
 */
public class ArrayInfoDialog
    extends com.ideal.util.gui.EscapeDialog
    implements ActionListener
{
    JPanel jPanelAll = new JPanel();
    JPanel jPanelAllButton = new JPanel();
    JPanel jPanelTwoButton = new JPanel();
    BorderLayout borderLayout1 = new BorderLayout();
    JButton jButtonCancel = new JButton();
    JButton jButtonOK = new JButton();
    BorderLayout borderLayout2 = new BorderLayout();
    JPanel jPanelInput = new JPanel();
    BorderLayout borderLayout3 = new BorderLayout();
    JComboBox jComboBoxType = new JComboBox();
    GridLayout gridLayout1 = new GridLayout();
    JSpinner jSpinnerArrayType = new JSpinner();
    JLabel jLabel1 = new JLabel();
    JLabel jLabel2 = new JLabel();
    GridBagLayout gridBagLayout1 = new GridBagLayout();
    //--------------private field-----------------------
    private ArrayType _arrayType;
    static private final Logger _log = Logger.getLogger(ArrayInfoDialog.class);
    //Key-Value like this:
    //"Test.TestSchema"--An instance of class IEAISchemaDef
    private Map _iEAISchemaDefMap = new HashMap();
    /**
     * public constructor
     * @param frame
     */
    public ArrayInfoDialog(Frame frame)
    {
        super(frame, StudioRes.getString("view.title"), true);
        try
        {
            jbInit();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        //Add ActionListener to jButtons.
        jButtonOK.addActionListener(this);
        jButtonCancel.addActionListener(this);
        //Initialize the jComboBoxType
        InitjComboBoxType();
        //Sets the model of jSpinnerArrayType.
        jSpinnerArrayType.setModel(new SpinnerNumberModel());
        //atf is a subclass of JTextField defined by myself. At the same time,
        //It is ChangeListener and ItemListener.
        //Sets atf as the editor of jSpinnerArrayType.
        ArrayTypeTextField atf = new ArrayTypeTextField(jSpinnerArrayType);
        jSpinnerArrayType.setEditor(atf);
        //atf is a ChangeListener, Adds it to jSpinnerArrayType.
        jSpinnerArrayType.addChangeListener(atf);
        //atf is a ItemListener, Adds it to jComboBoxType.
        jComboBoxType.addItemListener(atf); //@for typeundo
        //Initialize the ArrayTypeTextField
        atf.stateChanged(null);
        //Sets size of dialog.
        this.setSize(350, 400);
        //Sets position of dialog.
        Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
        this.setLocation( (int) ( (screenSize.getWidth() - this.getSize().getWidth()) / 2),
                         (int) ( (screenSize.getHeight() - this.getSize().getHeight()) / 2));
        show();
    }

    private void jbInit() throws Exception
    {
        this.getContentPane().setLayout(borderLayout2);
        jPanelAllButton.setLayout(borderLayout1);
        jButtonCancel.setText(StudioRes.cancel );
        jButtonOK.setText(StudioRes.ok );
        jPanelAll.setLayout(borderLayout3);
        jPanelInput.setLayout(gridBagLayout1);
        jPanelInput.setBorder(BorderFactory.createEtchedBorder());
        jPanelTwoButton.setLayout(gridLayout1);
        gridLayout1.setHgap(4);
        gridLayout1.setRows(1);
        jSpinnerArrayType.setBorder(null);
        jSpinnerArrayType.setDebugGraphicsOptions(0);
        jSpinnerArrayType.setPreferredSize(new Dimension(27, 21));
        jLabel1.setText(StudioRes.getString("common_type"));
        jLabel2.setPreferredSize(new Dimension(52, 15));
        jLabel2.setRequestFocusEnabled(true);
        jLabel2.setToolTipText("");
        jLabel2.setText(StudioRes.getString("view.title.arryType"));
        this.setResizable(true);
        jComboBoxType.setBorder(BorderFactory.createLoweredBevelBorder());
        jPanelTwoButton.add(jButtonOK, null);
        jPanelTwoButton.add(jButtonCancel, null);
        jPanelAll.add(jPanelInput, BorderLayout.CENTER);
        jPanelInput.add(jComboBoxType, new GridBagConstraints(1, 0, 1, 1, 1.0, 0.0
            , GridBagConstraints.WEST, GridBagConstraints.HORIZONTAL, new Insets(4, 4, 4, 4), 0,
            0));
        jPanelInput.add(jLabel1, new GridBagConstraints(0, 0, 1, 1, 0.0, 0.0
            , GridBagConstraints.WEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 44, 0));
        jPanelInput.add(jLabel2, new GridBagConstraints(0, 1, 1, 1, 0.0, 0.0
            , GridBagConstraints.NORTHWEST, GridBagConstraints.NONE, new Insets(4, 4, 4, 4), 19,
            0));
        jPanelInput.add(jSpinnerArrayType, new GridBagConstraints(1, 1, 1, 1, 1.0, 1.0
            , GridBagConstraints.NORTH, GridBagConstraints.HORIZONTAL, new Insets(4, 4, 4, 4),
            0, 0));
        this.getContentPane().add(jPanelAll, BorderLayout.CENTER);
        jPanelAll.add(jPanelAllButton, BorderLayout.SOUTH);
        jPanelAllButton.add(jPanelTwoButton, BorderLayout.EAST);
    }

    private void InitjComboBoxType()
    {
        //----------------------------------------------
        jComboBoxType.addItem("Integer"); //---//
        jComboBoxType.addItem("Float"); ////////
        jComboBoxType.addItem("String"); ///////
        jComboBoxType.addItem("Boolean"); ///////--If these items swap their place,
        jComboBoxType.addItem("Datetime"); //////--getIndexOfComboBoxByType and
        jComboBoxType.addItem("Binary"); ////////--getTypeOfSelectionByComboBoxIndex
        jComboBoxType.addItem("Map"); //////////--need modify.
        //------------------------------------------------
        jComboBoxType.setMaximumRowCount(7);
        //---------------------Schema---------------------

        _iEAISchemaDefMap.clear();
        Project prj = ProjectManager.getInstance().getProject();
        java.util.List list = SchemaHelper.loadAllSchemaDefs(prj);

        for (int i = 0, size = list.size(); i < size; i++)
        {
            IEAISchemaDef def = (IEAISchemaDef) list.get(i);
            jComboBoxType.addItem(def.getFullName());
            _iEAISchemaDefMap.put(def.getFullName(), def);
        }
    }

    /**
     *
     * @param index
     * @return
     */
    private int getTypeOfSelectionByComboBoxIndex(int index)
    {
        return index > 6 ? -1 : index;
    }

    /**
     *
     * @param actionEvent
     */
    public void actionPerformed(ActionEvent actionEvent)
    {
        if (actionEvent.getSource() == jButtonCancel)
        {
            _arrayType = null;
        }
        dispose();
    }

    /**
     *
     * @return
     */
    public ArrayType getArrayType()
    {
        return _arrayType;
    }

    /**
     * If jButtonCancel had been pressed, the method would return null.
     */

    public static ArrayType showDialog(Frame frame)
    {
        ArrayInfoDialog dialog = new ArrayInfoDialog(frame);

        return dialog.getArrayType();
    }

    /**
     *
     * <p>Title: </p>
     * <p>Description:It is an editor of spinner. </p>
     * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
     * <p>Company: Ideal Technologies Inc.</p>
     * <AUTHOR>
     * @version 3.0
     */

    class ArrayTypeTextField
        extends JTextField
        implements ChangeListener, ItemListener
    {
        private JSpinner _jSpinner;
        
        public ArrayTypeTextField(JSpinner jSpinner)
        {
            _jSpinner = jSpinner;
            this.setEditable(false);
        }

        public void stateChanged(ChangeEvent changeEvent)
        {
            int dimension = ( (Integer) _jSpinner.getValue()).intValue();
            int selectIndex = jComboBoxType.getSelectedIndex();
            int selectType = getTypeOfSelectionByComboBoxIndex(selectIndex);
            if (dimension < 1)
            {
                _jSpinner.setValue(new Integer(1));
                return;
            }

            IDataType baseType = null;
            DataTypeFactory typeFactory = DataTypeFactory.getInstance();
            if (selectType == -1)
            { //if selectType==-1, selection is a schema.
                String fullName = (String) jComboBoxType.getSelectedItem();
                IEAISchemaDef def = (IEAISchemaDef) _iEAISchemaDefMap.get(fullName);
                try
                {
                    baseType = def.createSchema();
                    _arrayType = typeFactory.getArrayTypeInstance(baseType, dimension);
                }
                catch (Exception ex1)
                {
                    StudioException.showErr(StudioRes.getString(
                        "view.title.updateThird.errorOncreatingSchemaArray"), this);
                    _log.debug(ex1.getMessage(), ex1);
                }
            }
            else
            { //selectType is a general type.
                try
                {
                    baseType = typeFactory.getDataTypeInstance(selectType);
                    _arrayType = typeFactory.getArrayTypeInstance(baseType, dimension);
                }
                catch (InvalidTypeException ex)
                {
                    _log.debug(ex.getMessage(), ex);
                }
            }
            this.setText(_arrayType.getTypeName());
        }

        public void itemStateChanged(ItemEvent itemEvent)
        {
            if (itemEvent.getStateChange() == ItemEvent.DESELECTED)
            {
                return;
            }
            stateChanged(null);
        }
    }
}
