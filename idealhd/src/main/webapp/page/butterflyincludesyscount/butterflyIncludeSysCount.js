Ext.Loader.setConfig({
  enabled:true,
  disableCaching:false,
  paths:{
    'Go':'js/ux/gooo'
  }
});
var queryFormPanel=null;
Ext.onReady(function() {
  // 清理主面板的各种监听时间
  destroyRubbish();
//主panel高度减掉的定值
	var staticHeight = 37;
  Ext.tip.QuickTipManager.init ();
  Ext.define('butterflyIncludeSysCountModel', {
    extend : 'Ext.data.Model',
		    fields : [ {
			name : 'biid',
			type : 'long'
		},{
			name : 'viid',
			type : 'long'
		}, {
			name : 'prjName',
			type : 'string'
		}, {
			name : 'sysName',
			type : 'string'
		}, {
			name : 'isAoms',
			type : 'string'
		}, {
			name : 'includeSysTime',
			type : 'string'
		}]
  });
  
  var queryForm = Ext.create('Ext.form.Panel', {
	  
	  region : 'north',
	  collapsible : true,//可收缩
	  collapsed : false,//默认收缩
	  border: false,
	  bodyCls: 'service_platform_bodybg',
	  layout: 'form',
	    items: [{
	      layout:'column',
	      border : false,
	      items: [{
		        labelWidth : 66,
		        labelAlign : 'left',
		        width: 280,
		        xtype: 'textfield',
		        fieldLabel: '工程名称',
		        name: 'prjName',
		        padding : '0 10 0 0' 
	      },
	      {
		        labelWidth : 66,
		        labelAlign : 'left',
		        width: 280,
		        xtype: 'textfield',
		        fieldLabel: '业务系统',
		        name: 'sysName',
		        padding : '0 10 0 0' 
	      },{
		      layout:'column',
		      border : false,
		      items: [ {
		        xtype: 'button',
		        text: '查询',
		        margin : '0 10 14 0',
		        handler: function() {
		          queryWhere();
		        }
		      },{
		        xtype: 'button',
		        margin : '0 10 14 0',
		        text: '重置',
		        handler: function() {
		          resetWhere();
		        }
		      }, {
	              xtype : 'button',
	              text : '保存',
	              margin : '0 10 14 0',
	              handler : function ()
	              {
	              	saveIsAoms ();
	              }
			   }, {
		              xtype : 'button',
		              text : '推送数据到生产',
		              margin : '0 10 14 0',
		              handler : function ()
		              {
		              	syncBfIncludeSysProc ();
		              }
			   }]
		   }]
	    }]
	  });
  contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
  queryForm.setTitle("平台自动化纳管系统统计");//将contentPanel标题显示在查询Form上
  var butterflyIncludeSysCountStore = Ext.create('Ext.data.Store', {
    autoLoad : true,
    autoDestroy : true,
    model : 'butterflyIncludeSysCountModel',
    proxy : {
      type : 'ajax',
      url : 'getBfIncludeSysCountList.do',
      reader : {
        type : 'json',
        root : 'dataList',
        totalProperty: 'total'
      }
    }
  });
  
  butterflyIncludeSysCountStore.on('beforeload', function(store, options) {
		var sysName = queryForm.getForm().findField("sysName")
				.getValue();
		var prjName = queryForm.getForm().findField("prjName")
		.getValue();
		var new_params = {
			sysName : sysName,
			prjName : prjName
		};
		Ext.apply(butterflyIncludeSysCountStore.proxy.extraParams, new_params);
	});
  
  Ext.define('systemComboxModel', {
	    extend: 'Ext.data.Model',
	    fields: [
	             {name: 'siid',  type: 'long'},
	             {name: 'prjName',  type: 'string'},
	             {
	     		    name : 'isystemId',
	    		    type : 'string'
	             }
	            ]
		});

 /**系统名称下拉选**/
 var systemComboxStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'systemComboxModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getSystemInfo.do',
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
 systemComboxStore.on('beforeload', function(store, options) {
		var new_params = {
			sysName : "",
			insName : "",
			version : "",
			startUser : "",
			startTime : "",
			endTime : "",
			workOrderNum:"",
			targetFlag:"",
			isHistory : 1
		};
		Ext.apply(systemComboxStore.proxy.extraParams, new_params);
	});

 var systemCombo = Ext.create('Ext.form.field.ComboBox',{
	  	typeAhead : true,
		triggerAction : 'all',
		lazyRender : true,
		editable : true,
		queryMode: 'local',
	    anyMatch : true,
		allowBlank : true,
		valueNotFoundText : '',
		store : systemComboxStore,
		valueField : 'prjName',
		displayField : 'prjName'
  });
  
  var isAomsCombo = Ext.create('Ext.form.field.ComboBox',{
  	typeAhead: true,
  	triggerAction: 'all',
  	lazyRender: true,
  	mode: 'local',
  	editable: false,
  	allowBlank: false,
  	valueNotFoundText : '否',
  	store: new Ext.data.ArrayStore({
       	 id:'sTCombo',
       	 fields: ['name', 'value'],
       	 data: [
       	        ['是','0'], 
       	        ['否', '1']
       	       ]
        }),
      valueField: 'value',
      displayField: 'name'
  });
  
  var butterflyIncludeSysCountColumns = [{
	    text : '序号',
	    xtype : 'rownumberer',
	    flex : 1
	  }, {
	    text : 'BID',
	    dataIndex : 'biid',
	    width : 40,
	    hidden : true
	  }, {
	    text : 'VID',
	    dataIndex : 'viid',
	    width : 40,
	    hidden : true
	  }, {
	    text: '工程名称',  
	    dataIndex: 'prjName',
	    flex : 2,
	    align : 'left',
		   editor:systemCombo,
		   renderer:function(value, metaData, record){
		       if(record.data.prjName==value){
		    	   return value;
		       }else{
		    	   return '';
		       }
		   }

	  }, {
	    text : '业务系统(IPMP)',
	    dataIndex : 'sysName',
	    flex : 2
	  }, {
	    text : '是否自动化',
	    dataIndex : 'isAoms',
	    flex : 1,
	    editor: isAomsCombo,
		   renderer:function(value){
			   var record = isAomsCombo.findRecord(isAomsCombo.valueField,value);
		       return record? record.get(isAomsCombo.displayField)
			   :isAomsCombo.valueNotFoundText;
		   }
	  }, {
	    text : '纳入时间',
	    dataIndex : 'includeSysTime',
	    flex : 1
	  }
	 ];
  
  var pageBar = Ext.create('Ext.PagingToolbar', {
    store: butterflyIncludeSysCountStore, 
    dock: 'bottom',
    baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    border : false,
    displayInfo: true
  });
  
  var butterflyIncludeSysCountGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
    extend : 'Ext.grid.Panel',
    alias : 'widget.ideapanel',
   // bodyPadding : grid_space,
	cls:'customize_grid_back',
	padding : grid_space,
    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    cls:'customize_panel_back',
    store : butterflyIncludeSysCountStore,
    //padding : grid_margin,
    columns : butterflyIncludeSysCountColumns,
    animCollapse : false
  });
  
  var butterflyIncludeSysCountPanel = Ext.create('Ext.panel.Panel',{
    renderTo : "butterflyIncludeSysCountArea",
    width : contentPanel.getWidth (),
    height : contentPanel.getHeight ()-2,
    border : false,
    cls:'customize_panel_header_arrow',
    layout : 'border',
    items : [queryForm,butterflyIncludeSysCountGrid]
  });

  
  contentPanel.on('resize', function() {
    butterflyIncludeSysCountPanel.setWidth (contentPanel.getWidth ());
    butterflyIncludeSysCountPanel.setHeight (contentPanel.getHeight ()-2);
  });
  
  $("body").off('keydown').on('keydown',function(event) {
	    if (event.keyCode == "13") { 
	    	queryWhere();
	    }
	});
  
  function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
  }

  /* 解决IE下trim问题 */
  String.prototype.trim = function() {
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  
  // 当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
    Ext.destroy(butterflyIncludeSysCountGrid);
    if (Ext.isIE) {
      CollectGarbage();
    }
  });
  
  function queryWhere(){
    pageBar.moveFirst();
  }
  
  function resetWhere(){
    queryForm.getForm().findField("prjName").setValue('');
    queryForm.getForm().findField("sysName").setValue('');
  }
  
  function saveIsAoms(){
	  var records = butterflyIncludeSysCountStore.getModifiedRecords();
	  /*if(records.length==0){
			Ext.Msg.alert("提示","<font>请将数据填写完整</font>");
	  }else{*/
			var flag = true;
			Ext.each(records, function(record) {
				if(record.data.isAoms.trim()==""){
					Ext.Msg.show({
					     title:'提示',
					     msg: '请选择是否自动化',
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.WARNING
					});
					flag = false;
					return;
				}
				if(record.data.sysName==""){
					Ext.Msg.show({
					     title:'提示',
					     msg: 'butterfly上未登记的系统无法标记是否自动化!',
					     buttons: Ext.Msg.OK,
					     icon: Ext.Msg.WARNING
					});
					flag = false;
					return;
				}
			});
			
			var jsonData = "[";
		    for ( var i = 0, len = records.length; i < len; i++) {      
		      var ss = Ext.JSON.encode(records[i].data);
		      if (i == 0)
		        jsonData = jsonData + ss;
		      else
		        jsonData = jsonData + "," + ss;
		    }
		    jsonData = jsonData + "]";
		    
			if(flag){
				Ext.Ajax.request({  
					url : 'saveIsAoms.do',  
					method : 'post',  
					params : {  
						jsonData : jsonData
					},				
				    success: function(response, opts) {
				        var success = Ext.decode(response.responseText).success;
				        if (success) {
				        	butterflyIncludeSysCountStore.reload();
				        	Ext.Msg.alert('提示','保存成功!');
				        }else{
				        	Ext.Msg.alert('提示','保存失败!');
				        }
				    },
				    failure: function(result, opts) {
				    	Ext.Msg.alert("提示","请求返回失败！");
				    }
				});
			}
	  //}
  }
  
  function syncBfIncludeSysProc(){
	  
	  Ext.Ajax.request({  
			url : 'syncBfIncludeSysProc.do',  
			method : 'post',  
			params : {  
			},				
		    success: function(response, opts) {
		        var success = Ext.decode(response.responseText).success;
		        if (success) {
		        	Ext.Msg.alert('提示','推送成功!');
		        }else{
		        	Ext.Msg.alert('提示','推送失败!');
		        }
		    },
		    failure: function(result, opts) {
		    	Ext.Msg.alert("提示","请求返回失败！");
		    }
		});
  }
});

