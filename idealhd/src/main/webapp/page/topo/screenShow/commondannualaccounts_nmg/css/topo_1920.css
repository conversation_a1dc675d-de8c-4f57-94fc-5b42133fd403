@charset "utf-8";
/* CSS Document */

body {
  background-color: #000e6b;
  font-family: "Microsoft YaHei";
  padding: 0;
  margin: 0;
}
.number_bg{
	width:1001px;
	height:369px;
	margin-top:126px;
	margin-left:10px;
}
.processBar {
    width: auto;
    height: auto;
    display: flex;
    position:relative;
    left:-22px;
    top:-6px;
}
.container {
    width: 253px;
    height: auto;
    display: inline-flex;
    margin: 0px 2px;
}
.column {
    display: block;
}
.square {
    width: 36px;
    height: 35px;
    margin: 3px 2px;
    display: block;
}
.square-hide {
    visibility: hidden;
}
/*main*/
/* 头部 */
.sd_screen {
  background-image: url(../images_1920/body_bg.jpg);
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 0px;
  z-index: 9999;
}
.sd_header {
  width: 100%;
  height: 120px;
  background: url(../images_1920/sd_header_bg.png);
  position: absolute;
  top:0

}
.hed_con {
  width: 963px;
  height: 100%;
  margin: 0 auto;
  position: relative;
}
.logo {
  position: absolute;
  width: 100%;
  height: 100%;
  background: url(../images_1920/logo.png) no-repeat left center;
  left: 22px;
}
.hed_con h2 {
  padding-left: 380px;
  font-size: 80px;
  color: #fff;
  line-height: 110px;
  font-weight: bold;
  letter-spacing: 13px;
  text-shadow: #00f0ff 0 0 18px;
  -moz-text-shadow: #00f0ff 0 0 18px;
  -webkit-text-shadow: #00f0ff 0 0 18px;
}
.sd_header .data {
  position: absolute;
  right: 30px;
  top: 13px;
  color: #fff;
  font-size: 28px;
  text-shadow: #00f0ff 0 0 18px;
  -moz-text-shadow: #00f0ff 0 0 18px;
  -webkit-text-shadow: #00f0ff 0 0 18px;
}
/* 公共背景边角 */
.bg_group span {
  display: block;
  position: absolute;
  width: 20px;
  height: 20px;
}
.bg_group span:nth-child(1) {
  background: url(../images_1920/bg_common_1.png) no-repeat;
  top: 0px;
  left: 0px;
}
.bg_group span:nth-child(2) {
  background: url(../images_1920/bg_common_2.png) no-repeat;
  top: 0px;
  right: 0px;
}
.bg_group span:nth-child(3) {
  background: url(../images_1920/bg_common_3.png) no-repeat;
  bottom: 0px;
  left: 0px;
}
.bg_group span:nth-child(4) {
  background: url(../images_1920/bg_common_4.png) no-repeat;
  bottom: 0px;
  right: 0px;
}
/* 系统进度 */
.system_prog {
  width: 400px;
  height: 635px;
  float: left;
  margin: 95px 0 20px 30px;
  padding: 20px 20px;
  position: relative;
  box-shadow: inset #33e1f5 0 0 18px;
  -moz-box-shadow: inset #33e1f5 0 0 18px;
  -webkit-box-shadow: inset #33e1f5 0 0 18px;
}
.tit_box {
  position: relative;
}
.tit_box h3 {
  color: #fff;
  font-size: 25px;
  font-weight: bold;
  padding-bottom: 3px;
}
.tit_box h3::before {
  content: "";
  position: absolute;
  top: 8px;
  left: -10px;
  width: 2px;
  height: 14px;
  background: #00f0ff;
}
.tit_box span {
  display: inline-block;
  color: #00f0ff;
  font-size: 14px;
  text-transform: uppercase;
}
.tit_box em {
  display: inline-block;
  width: 216px;
  height: 1px;
  background: #00f0ff;
  margin-left: 9px;
}
.system_prog .system_con {
  margin-top: 20px;
  height: 545px;
  overflow: hidden;
}
.system_con li {
  width: 100%;
  height: 71px;
  margin-bottom: 10px;
  position: relative;
  list-style:none;
}
.system_con li.lion::before{
  content: "";
  position: absolute;
  right: 12px;
  top: 50%;
  margin-top: -7px;
  width: 11px;
  height: 14px;
  background: url(../images_1920/lion_tag.png) no-repeat ;
  z-index: 999;
}
.sys_progress {
  width: 100%;
  height: 70px;
  position: absolute;
  left: 0;
  top: 0;
}
.li_finish {
  background: rgba(3, 196, 163, .1);
  box-shadow: inset #04a688 0 0 18px;
  -moz-box-shadow: inset #04a688 0 0 18px;
  -webkit-box-shadow: inset #04a688 0 0 18px;
}
.li_running {
  background: rgba(0, 240, 255, .1);
  box-shadow: inset #33e1f5 0 0 18px;
  -moz-box-shadow: inset #33e1f5 0 0 18px;
  -webkit-box-shadow: inset #33e1f5 0 0 18px;
}
.li_nostarted {
  box-shadow: inset #ffffff 0 0 18px;
  -moz-box-shadow: inset #ffffff 0 0 18px;
  -webkit-box-shadow: inset #ffffff 0 0 18px;
}
.li_total {
  background: rgba(248, 192, 40, .1);
  box-shadow: inset #f8c028 0 0 18px;
  -moz-box-shadow: inset #f8c028 0 0 18px;
  -webkit-box-shadow: inset #f8c028 0 0 18px;
}
.system_con li .sys_progress-done {
  height: 71px;
}
.li_finish .sys_progress-done {
  background: url(../images_1920/sys_finish_bg.png);
  background-size: 100% 100%;
  border-right: 1px solid #05ebba;
}
.li_running .sys_progress-done {
  background: url(../images_1920/sys_running_bg.png);
  background-size: 100% 100%;
  border-right: 1px solid #00f0ff;
}
.li_total .sys_progress-done {
  background: url(../images_1920/sys_total_bg.png);
  background-size: 100% 100%;
  border-right: 1px solid #f69c00;
}
.sys_text {
  color: #fff;
  padding: 0 15px;
  position: relative;
  display:flex;
  align-content: center;
  height: 70px;
  flex-wrap: wrap;
  width:360px;
}
.sys_text h2 {
  font-size: 20px;
  font-weight: bold;
  width:270px;
}
.sys_text h2 em {
  display: block;
  font-size: 16px;
}
.sys_text span {
  font-size: 20px;
  display: block;
  position: absolute;
  height: 71px;
  line-height: 71px;
  right: 15px;
  top: 0;
  font-weight: bold;
}

/* 运行步骤 */
.opera_steps {
  width: 1022px;
  height: 610px;
  float: left;
  margin: 120px 0 0 20px;
  position: relative;
}
.steps_mapbg {
  position: absolute;
  left: 8px;
  top: -116px;
  width: 1003px;
  height: 848px;
  background: url(../images_1920/opera_steps_bg.png) left center no-repeat;
}
.steps_t {
  width: 100%;
  height: 370px;
  position: relative;
  top: 126px;
}
.steps_t span {
  display: block;
  position: absolute;
  width: 36px;
  height: 35px;
  padding: 9px 0 0 12px;
  background: url(../images_1920/step_lumpbg.png) no-repeat;
}
.steps_t span em {
  width: 100%;
  line-height: 24px;
  text-align: center;
  display: none;
  color: #fff;
}

.site_1 {
  top: 0;
  left: 10px;
}
.site_2 {
  top: 0;
  left: 49px;
}
.site_3 {
  top: 0;
  left: 88px;
}
.site_4 {
  top: 0;
  left: 127px;
}
.site_5 {
  top: 0;
  left: 166px;
}
.site_6 {
  top: 0;
  left: 205px;
}
.site_7 {
  top: 37px;
  left: 10px;
}
.site_8 {
  top: 37px;
  left: 49px;
}
.site_9 {
  top: 37px;
  left: 88px;
}
.site_10 {
  top: 37px;
  left: 127px;
}
.site_11 {
  top: 37px;
  left: 166px;
}
.site_12 {
  top: 37px;
  left: 205px;
}
.site_13 {
  top: 74px;
  left: 166px;
}
.site_14 {
  top: 74px;
  left: 205px;
}
.site_15 {
  top: 111px;
  left: 166px;
}
.site_16 {
  top: 111px;
  left: 205px;
}
.site_17 {
  top: 148px;
  left: 10px;
}
.site_18 {
  top: 148px;
  left: 49px;
}
.site_19 {
  top: 148px;
  left: 88px;
}
.site_20 {
  top: 148px;
  left: 127px;
}
.site_21 {
  top: 148px;
  left: 166px;
}
.site_22 {
  top: 148px;
  left: 205px;
}
.site_23 {
  top: 185px;
  left: 10px;
}
.site_24 {
  top: 185px;
  left: 49px;
}
.site_25 {
  top: 185px;
  left: 88px;
}
.site_26 {
  top: 185px;
  left: 127px;
}
.site_27 {
  top: 185px;
  left: 166px;
}
.site_28 {
  top: 185px;
  left: 205px;
}
.site_29 {
  top: 222px;
  left: 10px;
}
.site_30 {
  top: 222px;
  left: 49px;
}
.site_31 {
  top: 259px;
  left: 10px;
}
.site_32 {
  top: 259px;
  left: 49px;
}
.site_33 {
  top: 296px;
  left: 10px;
}
.site_34 {
  top: 296px;
  left: 49px;
}
.site_35 {
  top: 296px;
  left: 88px;
}
.site_36 {
  top: 296px;
  left: 127px;
}
.site_37 {
  top: 296px;
  left: 166px;
}
.site_38 {
  top: 296px;
  left: 205px;
}
.site_39 {
  top: 333px;
  left: 10px;
}
.site_40 {
  top: 333px;
  left: 49px;
}
.site_41 {
  top: 333px;
  left: 88px;
}
.site_42 {
  top: 333px;
  left: 127px;
}
.site_43 {
  top: 333px;
  left: 166px;
}
.site_44 {
  top: 333px;
  left: 205px;
}
.site_45 {
  top: 0;
  left: 266px;
}
.site_46 {
  top: 0;
  left: 305px;
}
.site_47 {
  top: 0;
  left: 344px;
}
.site_48 {
  top: 0;
  left: 383px;
}
.site_49 {
  top: 0;
  left: 422px;
}
.site_50 {
  top: 0;
  left: 461px;
}
.site_51 {
  top: 37px;
  left: 266px;
}
.site_52 {
  top: 37px;
  left: 305px;
}
.site_53 {
  top: 37px;
  left: 344px;
}
.site_54 {
  top: 37px;
  left: 383px;
}
.site_55 {
  top: 37px;
  left: 422px;
}
.site_56 {
  top: 37px;
  left: 461px;
}
.site_57 {
  top: 74px;
  left: 266px;
}

.site_58 {
  top: 74px;
  left: 305px;
}
.site_59 {
  top: 74px;
  left: 422px;
}

.site_60 {
  top: 74px;
  left: 461px;
}
.site_61 {
  top: 111px;
  left: 266px;
}

.site_62 {
  top: 111px;
  left: 305px;
}
.site_63 {
  top: 111px;
  left: 422px;
}

.site_64 {
  top: 111px;
  left: 461px;
}
.site_65 {
  top: 148px;
  left: 266px;
}

.site_66 {
  top: 148px;
  left: 305px;
}
.site_67 {
  top: 148px;
  left: 422px;
}

.site_68 {
  top: 148px;
  left: 461px;
}
.site_69 {
  top: 185px;
  left: 266px;
}

.site_70 {
  top: 185px;
  left: 305px;
}
.site_71 {
  top: 185px;
  left: 422px;
}

.site_72 {
  top: 185px;
  left: 461px;
}
.site_73 {
  top: 222px;
  left: 266px;
}

.site_74 {
  top: 222px;
  left: 305px;
}
.site_75 {
  top: 222px;
  left: 422px;
}

.site_76 {
  top: 222px;
  left: 461px;
}
.site_77 {
  top: 259px;
  left: 266px;
}

.site_78 {
  top: 259px;
  left: 305px;
}
.site_79 {
  top: 259px;
  left: 422px;
}

.site_80 {
  top: 259px;
  left: 461px;
}
.site_81 {
  top: 296px;
  left: 266px;
}
.site_82 {
  top: 296px;
  left: 305px;
}
.site_83 {
  top: 296px;
  left: 344px;
}
.site_84 {
  top: 296px;
  left: 383px;
}
.site_85 {
  top: 296px;
  left: 422px;
}
.site_86 {
  top: 296px;
  left: 461px;
}
.site_87 {
  top: 333px;
  left: 266px;
}
.site_88 {
  top: 333px;
  left: 305px;
}
.site_89 {
  top: 333px;
  left: 344px;
}
.site_90 {
  top: 333px;
  left: 383px;
}
.site_91 {
  top: 333px;
  left: 422px;
}
.site_92 {
  top: 333px;
  left: 461px;
}
.site_93 {
  top: 0;
  left: 523px;
}
.site_94 {
  top: 0;
  left: 562px;
}
.site_95 {
  top: 0;
  left: 601px;
}
.site_96 {
  top: 0;
  left: 640px;
}
.site_97 {
  top: 0;
  left: 679px;
}
.site_98 {
  top: 0;
  left: 718px;
}
.site_99 {
  top: 37px;
  left: 523px;
}
.site_100 {
  top: 37px;
  left: 562px;
}
.site_101 {
  top: 37px;
  left: 601px;
}
.site_102 {
  top: 37px;
  left: 640px;
}
.site_103 {
  top: 37px;
  left: 679px;
}
.site_104 {
  top: 37px;
  left: 718px;
}
.site_105 {
  top: 74px;
  left: 679px;
}
.site_106 {
  top: 74px;
  left: 718px;
}
.site_107 {
  top: 111px;
  left: 679px;
}
.site_108 {
  top: 111px;
  left: 718px;
}
.site_109 {
  top: 148px;
  left: 523px;
}
.site_110 {
  top: 148px;
  left: 562px;
}
.site_111 {
  top: 148px;
  left: 601px;
}
.site_112 {
  top: 148px;
  left: 640px;
}
.site_113 {
  top: 148px;
  left: 679px;
}
.site_114 {
  top: 148px;
  left: 718px;
}
.site_115 {
  top: 185px;
  left: 523px;
}
.site_116 {
  top: 185px;
  left: 562px;
}
.site_117 {
  top: 185px;
  left: 601px;
}
.site_118 {
  top: 185px;
  left: 640px;
}
.site_119 {
  top: 185px;
  left: 679px;
}
.site_120 {
  top: 185px;
  left: 718px;
}
.site_121 {
  top: 222px;
  left: 523px;
}
.site_122 {
  top: 222px;
  left: 562px;
}
.site_123 {
  top: 261px;
  left: 523px;
}
.site_124 {
  top: 259px;
  left: 562px;
}

.site_125 {
  top: 296px;
  left: 523px;
}
.site_126 {
  top: 296px;
  left: 562px;
}
.site_127 {
  top: 296px;
  left: 601px;
}
.site_128 {
  top: 296px;
  left: 640px;
}
.site_129 {
  top: 296px;
  left: 679px;
}
.site_130 {
  top: 296px;
  left: 718px;
}
.site_131 {
  top: 333px;
  left: 523px;
}
.site_132 {
  top: 333px;
  left: 562px;
}
.site_133 {
  top: 333px;
  left: 601px;
}
.site_134 {
  top: 333px;
  left: 640px;
}
.site_135 {
  top: 333px;
  left: 679px;
}
.site_136 {
  top: 333px;
  left: 718px;
}
.site_137 {
  top: 0;
  left: 779px;
}
.site_138 {
  top: 0;
  left: 818px;
}
.site_139 {
  top: 74;
  left: 896px;
}
.site_140 {
  top: 0;
  left: 896px;
}
.site_141 {
  top: 0;
  left: 935px;
}
.site_142 {
  top: 74;
  left: 935px;
}
.site_143 {
  top: 37px;
  left: 779px;
}
.site_144 {
  top: 37px;
  left: 818px;
}
.site_145 {
  top: 111px;
  left: 896px;
}
.site_146 {
  top: 37px;
  left: 896px;
}
.site_147 {
  top: 37px;
  left: 935px;
}
.site_148 {
  top:111px;
  left: 935px;
}
.site_149 {
  top: 74px;
  left: 779px;
}
.site_150 {
  top: 74px;
  left: 818px;
}
.site_151 {
  top: 111px;
  left: 779px;
}
.site_152 {
  top: 111px;
  left: 818px;
}
.site_153 {
  top: 148px;
  left: 779px;
}
.site_154 {
  top: 148px;
  left: 818px;
}
.site_155 {
  top: 148px;
  left: 857px;
}
.site_156 {
  top: 148px;
  left: 896px;
}
.site_157 {
  top: 148px;
  left: 935px;
}
.site_158 {
  top: 148px;
  left: 974px;
}
.site_159 {
  top: 185px;
  left: 779px;
}
.site_160 {
  top: 185px;
  left: 818px;
}
.site_161 {
  top: 185px;
  left: 857px;
}
.site_162 {
  top: 185px;
  left: 896px;
}
.site_163 {
  top: 185px;
  left: 935px;
}
.site_164 {
  top: 185px;
  left: 974px;
}
.site_165 {
  top: 222px;
  left: 896px;
}
.site_166 {
  top: 222px;
  left: 935px;
}
.site_167 {
  top: 261px;
  left: 896px;
}
.site_168 {
  top: 261px;
  left: 935px;
}
.site_169 {
  top: 296px;
  left: 779px;
 opacity:0;
}
.site_170 {
  top: 296px;
  left: 818px;
  opacity:0;
}
.site_171 {
  top: 296px;
  left: 857px;
  opacity:0;
}
.site_172 {
  top: 296px;
  left: 896px;
}
.site_173 {
  top: 296px;
  left: 935px;
}
.site_174 {
  top: 296px;
  left: 974px;
  opacity:0;
}
.site_175 {
  top: 333px;
  left: 779px;
  opacity:0;
}
.site_176 {
  top: 333px;
  left: 818px;
  opacity:0;
}
.site_177 {
  top: 333px;
  left: 857px;
  opacity:0;
}
.site_178 {
  top: 333px;
  left: 896px;
}
.site_179 {
  top: 333px;
  left: 935px;
}
.site_180 {
  top: 333px;
  left: 974px;
  opacity:0;
}
.st_finish {
  background: url(../images_1920/step_lump_finish.png) no-repeat !important;
}
.st_running {
  width: 36px;
  height: 35px;
  background: url(../images_1920/step_lump_running.gif) no-repeat !important;
  background-size: 100% 100% !important;
  color: #fff !important;
}
.st_running em {
  font-weight: bold;
  text-shadow: #017cc3 0 0 5px;
  -moz-text-shadow: #017cc3 0 0 5px;
  -webkit-text-shadow: #017cc3 0 0 5px;
}

/* 核心步骤 */
.steps_b {
  padding: 90px 30px 0 40px;
}
.kernel_demo {
  width: 75px;
  height: 76px;
  position: relative;
  float: left;
  margin-right: 39px;
}
.rotate_run {
  background: url(../images_1920/rotate_run.png) no-repeat;
}
.rotate_finish {
  background: url(../images_1920/rotate_finish.png) no-repeat;
}
.rotate_unplayed {
  background: url(../images_1920/rotate_unplayed.png) no-repeat;
}
.rotate_bg {
  width: 62px;
  height: 62px;
  position: absolute;
  top: 7px;
  left: 6px;
  background: url(../images_1920/rotate_bg.png) no-repeat;
}

.kernel_demo:last-child {
  margin-right: 0;
}
.rotate_run::after {
  content: "";
  position: absolute;
  right: -32px;
  top: 50%;
  margin-top: -7px;
  width: 27px;
  height: 14px;
  background: url(../images_1920/rotate_run_jt.png);
}
.rotate_finish::after {
  content: "";
  position: absolute;
  right: -32px;
  top: 50%;
  margin-top: -7px;
  width: 27px;
  height: 14px;
  background: url(../images_1920/rotate_finish_jt.png);
}
.rotate_unplayed::after {
  content: "";
  position: absolute;
  right: -32px;
  top: 50%;
  margin-top: -7px;
  width: 27px;
  height: 14px;
  background: url(../images_1920/rotate_unplayed_jt.png);
}
.kernel_demo:last-child::after {
  width: 0;
}
.kernel_demo h2 {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  line-height: 76px;
  text-shadow: #00f0ff 0 0 8px;
  -moz-text-shadow: #00f0ff 0 0 8px;
  -webkit-text-shadow: #00f0ff 0 0 8px;
}
.demo_text {
  position: absolute;
  bottom: 100px;
  width: 150px;
  text-align: center;
  left: -40px;
  z-index:999999
}
.kernel_demo:nth-child(2n + 2) .demo_text {
  position: absolute;
  top: 100px;
  width: 150px;
  text-align: center;
}
.demo_text::before {
  content: "";
  position: absolute;
  width: 11px;
  height: 9px;
  left: 50%;
  margin-left: -4px;
  bottom: -10px;
}
.rotate_finish  .demo_text::before {
  background: url(../images_1920/kernel_jt_finish.png);
}
.rotate_run  .demo_text::before {
  background: url(../images_1920/kernel_jt_run.png);
}
.rotate_unplayed  .demo_text::before {
  background: url(../images_1920/kernel_jt_no.png);
}
.kernel_demo:nth-child(2n + 2) .demo_text::before {
  top: -8px;
  transform: rotateX(180deg);
}
.demo_text h3 {
  font-weight: bold;
  font-size: 16px;
  padding-bottom: 6px;
}
.rotate_finish .demo_text h3{
  color: #04ebb4;
}
.rotate_run .demo_text h3{
  color: #00f0ff;
}
.rotate_unplayed .demo_text h3{
  color: #c6c7dc;
}
.demo_text span {
  color: #fff;
  font-size: 16px;
}
.kernel_demo:nth-child(2n + 2) .demo_text h3 {
  margin-top: 6px;
}
.rotate_bg_running {
  position: absolute;
  width: 62px;
  height: 62px;
  top: 7px;
  left: 6px;
  animation: turn 1s linear infinite;
  background: url(../images_1920/rotate_bg.png) no-repeat !important;
  background-size: 100% 100% !important;
  animation: turn 1.2s linear infinite;
}

/* 决算进度 */
.final_prog em {
  width: 267px !important;
}
.system_prog.final_prog {
  margin-left: 20px !important;
}
.system_prog.final_prog .sys_text h2 {
  width:245px;
  line-height: 25px;
  font-size:18px
}
.system_prog.final_prog .sys_text span {
  font-size: 16px;
}
.system_prog.final_prog .sys_progress {
  width: 100%;
  padding: 0 15px;
  height: 10px;
  position: absolute;
  left: 0;
  top: 44px;
}
.system_prog.final_prog .system_con li .sys_progress-done {
  position: relative;
  height: 10px;
}
.system_prog.final_prog .li_finish .sys_progress-done {
  background: #05ebba;
  border-radius: 120px;
}
.system_prog.final_prog .li_running .sys_progress-done {
  background: #00f0ff;
  border-radius: 120px;
}
.system_prog.final_prog .li_finish .sys_text span {
  color: #05ebba;
}
.system_prog.final_prog .li_running .sys_text span {
  color: #00f0ff;
}
.final_prog .li_finish::before {
  content: "";
  position: absolute;
  width: 14px;
  height: 8px;
  background: url(../images_1920/li_finish_jt.png) no-repeat;
  bottom: -14px;
  left: 50%;
  margin-left: -7px;
}
.final_prog .li_running::before {
  content: "";
  position: absolute;
  width: 14px;
  height: 8px;
  background: url(../images_1920/li_running_jt.png) no-repeat;
  bottom: -14px;
  left: 50%;
  margin-left: -7px;
}
.final_prog .system_con {
  height: 530px;
}
.final_prog .system_con li {
  margin-bottom: 20px;
}
.final_prog .system_con li:last-child::before {
  width: 0;
}
.final_prog .li_finish .sys_text span {
  background: url(../images_1920/li_finish_zt.png) no-repeat left center;
  padding-left: 30px;
}
.final_prog .li_running .sys_text span {
  background: url(../images_1920/li_running_zt.png) no-repeat left center;
  padding-left: 30px;
}
.final_prog .li_finish .sys_text {
  color: #04ebb4;
}
.final_prog .li_running .sys_text {
  color: #00f0ff;
}
.roll-wrap {
  position: relative;
  width: 100%;
  height: 545px;
  overflow: hidden;
}
.roll_col .roll__list {
  width: 100%;
}
/* 完成情况图表 */
.stat_chart {
  width: 400px;
  height: 300px;
  float: left;
  margin: 0 20px 0 30px;
  position: relative;
  padding: 30px;
  box-shadow: inset #33e1f5 0 0 18px;
  -moz-box-shadow: inset #33e1f5 0 0 18px;
  -webkit-box-shadow: inset #33e1f5 0 0 18px;
}
.chart_boxbg {
  position: relative;
  width: 191px;
  height: 190px;
  background: url(../images_1920/chart_box_bg.png);
  background-size: 100% 100%;
  float: left;
}
.chart_box {
  height: 170px;
  width: 170px;
  position: absolute;
  top: 11px;
  left: 12px;
}
.chart_boxbg h2 {
  text-align: center;
  line-height: 190px;
  font-size: 30px;
  color: #fff;
  font-weight: bold;
}
.chart_details {
  width: 130px;
  float: right;
  text-align: right;
}
.chart_details li {
  height: 58px;
  background: url(../images_1920/chart_dline.png) left bottom no-repeat;
  margin-bottom: 8px;
}
.chart_details li h2 {
  display: inline-block;
  background: url(../images_1920/chart_zt_tag1.png) left center no-repeat;
  padding-left: 16px;
  font-size: 18px;
  color: #fff;
}
.chart_details li:nth-child(2) h2 {
  background: url(../images_1920/chart_zt_tag2.png) left center no-repeat;
}
.chart_details li:nth-child(3) h2 {
  background: url(../images_1920/chart_zt_tag3.png) left center no-repeat;
}
.chart_details li span {
  display: block;
  font-size: 24px;
  color: #00f0ff;
  font-weight: bold;
  line-height: normal;
}
.chart_sum {
  width: 100%;
  height: 45px;
  float: left;
  box-shadow: inset #33e1f5 0 0 13px;
  -moz-box-shadow: inset #33e1f5 0 0 13px;
  -webkit-box-shadow: inset #33e1f5 0 0 13px;
  margin-top: 4px;
  position: relative;
}
.chart_sum h2 {
  font-size: 20px;
  color: #fff;
  line-height: 45px;
  padding-left: 20px;
}
.chart_sum span {
  display: block;
  position: absolute;
  top: 0;
  right: 20px;
  height: 45px;
  line-height: 45px;
  font-size: 20px;
  color: #00f0ff;
  font-weight: bold;
}
/* 年决步骤详情 */
.steps_details {
  width: 1440px;
  height: 300px;
  padding: 20px 20px;
  float: left;
  box-shadow: inset #33e1f5 0 0 18px;
  -moz-box-shadow: inset #33e1f5 0 0 18px;
  -webkit-box-shadow: inset #33e1f5 0 0 18px;
  position: relative;
}
.steps_details .tit_box em {
  width: 1096px;
}
.details_table {
  width: 100%;
  margin-top: 20px;
}
.details_table thead {
  display: inline-table;
  font-size: 12px;
  color: #00f0ff;
  background: #024651;
  line-height: 30px;
  font-weight: bold;
}

.details_table tbody {
  height: 184px;
  overflow: hidden;
  display: inline-block;
  font-size: 14px;
}
.td_line_1 {
  width: 62px;
}
.td_line_2 {
  width: 229px;
}
.td_line_3 {
  width: 203px;
}
.td_line_4 {
  width: 155px;
}
.td_line_5 {
  width: 141px;
}
.td_line_6 {
  width: 239px;
}
.td_line_7 {
  width: 170px;
}
.td_line_8 {
  width: 85px;
}
.td_line_9 {
  width: 90px;
  padding-left: 30px;
}
.t_number {
  display: inline-block;
  width: 33px;
  height: 23px;
  line-height: 23px;
  border-radius: 120px;
  text-align: center;
  color: #00003d;
}
.t_running {
  background: #00f0ff;
}
.t_await {
  background: #04ffc1;
}
.t_finish {
  background: #6eff6b;
}
.details_table tbody {
  margin-top: 10px;
}
.details_table tbody td {
  line-height: 36px;
  color: #fff;
}
.td_line_1 {
  text-align: center;
}
.td_line_2 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.td_running {
  background: url(../images_1920/td_zx.png) no-repeat left center;
}
.td_await {
  background: url(../images_1920/td_dd.png) no-repeat left center;
}
.td_finish {
  background: url(../images_1920/td_wc.png) no-repeat left center;
}
/* 弹出层 */
.popup_box{
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, .95);
  z-index: 99999;
}
.popup_con{
  width: 100%;
  height:100%;
  background: url(../images_1920//popup_con.png )center 180px no-repeat;
  padding-top: 356px;
}
.popup_con .p_logo{
  width: 437px;
  height: 91px;
  background: url(../images_1920/popup_logo.png) no-repeat;
  margin: 0 auto;
}
.p_text{
  width: 1289px;
  height: 151px;
  background: url(../images_1920/popup_text.png) center top no-repeat;
  margin: 0 auto;
  margin-top: 62px;
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}
@-webkit-keyframes bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.3);
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    -webkit-transform: scale(1.1);
    transform: scale(1.05);
  }
  70% {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes bounceIn {
  0% {
    opacity: 0;
    -webkit-transform: scale(0.3);
    -ms-transform: scale(0.3);
    transform: scale(0.3);
  }
  50% {
    opacity: .3;
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
  }
  70% {
    -webkit-transform: scale(0.9);
    -ms-transform: scale(0.9);
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
  }
}
.bounceIn {
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn;
}
/* 旋转 */
@keyframes turn {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@-moz-keyframes turn /* Firefox */ {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@-webkit-keyframes turn /* Safari 和 Chrome */ {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@-o-keyframes turn /* Opera */ {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}
.picMarquee-left{
	width:1400px;
	height:250px;
	overflow:hidden;
	display:flex;
}
.picMarquee-left .steps_b{
	display:flex;
}
.rotate_unplayed .rotate_bg {
	display:none
}

