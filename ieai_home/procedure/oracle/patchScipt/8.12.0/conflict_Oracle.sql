
-- 4.7.28	
CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE PROC_RUN_INSTANCE(FLAGTYPE  IN NUMBER, SERVERIP  IN VARCHAR2, AN_OPT_ID OUT NUMBER) AS
	BEGIN
	  DECLARE
		IS_COUNT  NUMBER(19);
		LN_OPT_ID NUMBER(19);
	  BEGIN
	  
		LN_OPT_ID := FUN_GET_NEXT_ID_ONE('TMP_RUN_INSTANCE');
		AN_OPT_ID := LN_OPT_ID;
		SELECT COUNT(*)
		  INTO IS_COUNT
		  FROM IEAI_RUN_INSTANCE RI, IEAI_RUNINFO_INSTANCE R
		 WHERE RI.IID = R.IRUNINSID
		   AND RI.IMXGRAPHID IS NULL
		   AND R.IRERUNFLAG = 0
		   AND (RI.ISTATE = 0 OR RI.ISTATE = -6)
		   AND R.ISTATE IN (0, 1)
		   AND RI.ISERVERIP = SERVERIP;
	  
		IF IS_COUNT > 0 THEN
		
		  INSERT INTO TMP_RUN_INSTANCE
			(OPI<PERSON>,
			 <PERSON><PERSON><PERSON>,
			 <PERSON><PERSON><PERSON><PERSON>,
			 <PERSON><PERSON><PERSON>INSNAM<PERSON>,
			 ISERNER,
			 ICONNER,
			 <PERSON>RENER,
			 IACTNAME,
			 IACTDES,
			 IACTTYPE,
			 IREMINFO,
			 IIP,
			 IPORT,
			 ISYSTYPE,
			 ISHELLPATH,
			 ITIMEOUT,
			 ISWITCHSYSTYPE,
			 IPARAMETER,
			 IPARACHECK,
			 IPARASWITCH,
			 IPARASWITCHFORCE,
			 IUSERNAME,
			 ISHELLSCRIPT,
			 IUSERID,
			 IOPERTYPE,
			 IEXECUSER,
			 IEXPECEINFO,
			 IISLOADENV,
			 ICONNERNAME,
			 ICENTER,
			 IMODELTYPE,
			 IRETNVALEXCEPION,
			 IREDOABLE,
			 IPKGNAME,
			 IVERSION,
			 IPRESYSNAME,
			 IPREACTNAME,
       ISPACEIID,
       IGOURPIID,ISTATE)
			SELECT DISTINCT LN_OPT_ID,
							BB.IID,
							AA.IID AS ID,
							BB.IRUNINSNAME,
							AA.ISERNER,
							AA.ICONNER,
							AA.IPRENER,
							AA.IACTNAME,
							AA.IACTDES,
							AA.IACTTYPE,
							AA.IREMINFO,
							AA.IIP,
							AA.IPORT,
							BB.ISYSTYPE,
							AA.ISHELLPATH,
							AA.ITIMEOUT,
							AA.ISWITCHSYSTYPE,
							AA.IPARAMETER,
							AA.IPARACHECK,
							AA.IPARASWITCH,
							AA.IPARASWITCHFORCE,
							BB.ISTARTUSER,
							AA.ISHELLSCRIPT,
							BB.ISTARTUSERID,
							AA.ISYSTYPE,
							AA.IEXECUSER,
							AA.IEXPECEINFO,
							AA.IISLOADENV,
							AA.ICONNERNAME,
							AA.ICENTER,
							AA.IMODELTYPE,
							AA.IRETNVALEXCEPION,
							AA.IREDOABLE,
							AA.IPKGNAME,
							BB.IVERSION,
							AA.IPRESYSNAME,
							AA.IPREACTNAME,
              AA.ISPACEIID,
              AA.IGOURPIID,
              BB.ISTATE
			  FROM IEAI_RUNINFO_INSTANCE AA,
				   (SELECT RI.IID,
						   MIN(R.ICONNER) AS ICONNER,
						   RI.IRUNINSNAME,
						   RI.ISYSTYPE,
						   RI.ISTARTUSER,
						   RI.ISERVERIP,
						   RI.ISTARTUSERID,
						   RI.IVERSION,
						   RI.ISTATE
					  FROM IEAI_RUN_INSTANCE RI, IEAI_RUNINFO_INSTANCE R
					 WHERE RI.IID = R.IRUNINSID
					   AND RI.IMXGRAPHID IS NULL
					   AND (RI.ISTATE = 0 OR RI.ISTATE = -6)
					   AND RI.ISERVERIP = concat(SERVERIP, '')
					   AND R.ISTATE = 1
					 GROUP BY RI.IID,
							  RI.IRUNINSNAME,
							  RI.ISYSTYPE,
							  RI.ISTARTUSER,
							  RI.ISERVERIP,
							  RI.ISTARTUSERID,
							  RI.IVERSION,
							  RI.ISTATE) BB
			 WHERE AA.IRUNINSID = BB.IID
			   AND AA.ICONNER = BB.ICONNER
			   AND AA.ISTATE = 1
			 ORDER BY AA.ICONNER ASC, AA.IPRENER DESC;
		
		END IF;
	  END;
	END PROC_RUN_INSTANCE;

/
-- 8.1.0
	
CREATE OR REPLACE PROCEDURE PROC_MV_RUN_INSTANCE(WORKID IN NUMBER,AN_OPT_ID IN NUMBER) AS
     BEGIN
       DECLARE	
            LV_SQL		VARCHAR2(4000);
       BEGIN
 				INSERT INTO IEAI_RUNINFO_INSTANCE_HIS(IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ISRTO,IINFORTO,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,IAZNAME,IPROXYIP,IPROBLEM) SELECT IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ISRTO,IINFORTO,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,IAZNAME,IPROXYIP,IPROBLEM FROM IEAI_RUNINFO_INSTANCE WHERE IRUNINSID = WORKID;
				INSERT INTO IEAI_RUN_INSTANCE_HIS(IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IIPMPCACHECODE,ILCBP,IXMLID,IBGTYPE,ICQTASKID) SELECT IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IIPMPCACHECODE,ILCBP,IXMLID,IBGTYPE,ICQTASKID FROM IEAI_RUN_INSTANCE WHERE IID = WORKID;
				DELETE FROM IEAI_RUNINFO_INSTANCE WHERE IRUNINSID = WORKID;
				DELETE FROM IEAI_RUN_INSTANCE WHERE IID = WORKID; 
				COMMIT;
       END;
     END PROC_MV_RUN_INSTANCE;
/

-- 8.11.0
CREATE OR REPLACE 
PROCEDURE PROC_MV_RUN_INSTANCE(WORKID IN NUMBER,AN_OPT_ID IN NUMBER) AS
     BEGIN
       DECLARE
            LV_SQL		VARCHAR2(4000);
       BEGIN
 				INSERT INTO IEAI_RUNINFO_INSTANCE_HIS(IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ISRTO,IINFORTO,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME) SELECT IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ISRTO,IINFORTO,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME FROM IEAI_RUNINFO_INSTANCE WHERE IRUNINSID = WORKID;
 				INSERT INTO IEAI_RUN_INSTANCE_HIS(IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID) SELECT IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID FROM IEAI_RUN_INSTANCE WHERE IID = WORKID;
 				DELETE FROM IEAI_RUNINFO_INSTANCE WHERE IRUNINSID = WORKID;
 				DELETE FROM IEAI_RUN_INSTANCE WHERE IID = WORKID;
       END;
END PROC_MV_RUN_INSTANCE;
/