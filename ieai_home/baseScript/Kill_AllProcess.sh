#!/bin/sh
########################################################################
#程序名称：Kill_AllProcess.sh                                          #
#程序功能：在目标服务器上终止所有由Agent调起的子进程                   #
#程序参数：sh Kill_AllProcess.sh [PID]                                 #
#Date:20220426                                                         #
#Author:wangqing                                                       #
########################################################################

#定义参数
#主脚本进程PID
parentPID=$1
#所有进程PID数组
declare -a ProPIDArr
ProPIDArr[0]=${parentPID}

#查询由主脚本拉起的所有子脚本进程PID
function QueryAllProcessPID()
{
  ProcessPID=$@
  #对父进程PID进行循环获取子进程PID
  for i in ${ProcessPID}
  do
    #查询进程信息
    ps -ef | grep ${i} | awk '$2=="'${i}'" {print}' | awk -F "environmentVal=" '{print $1}'
    #获取子进程PID
    SubProcessPID=`ps --ppid ${i} -o pid=`
    #判断子进程PID是否为空，不为空时将子进程PID加入数组中，并执行函数获取子子进程PID
    if [[ ${SubProcessPID} != "" ]];then
      ProPIDArr=("${ProPIDArr[@]}" ${SubProcessPID})
      QueryAllProcessPID ${SubProcessPID}
    fi
  done
}

ps --pid ${parentPID} -o ppid= >> /dev/null
if [ $? -ne 0 ];then
  echo "Command Not Found,Kill Failed!"
  exit
fi

echo "===============Killed Process Infos==============="
#执行函数QueryAllProcessPID
QueryAllProcessPID ${parentPID}
#echo ${ProPIDArr[@]}


#终止所有进程PID
kill -9 ${ProPIDArr[@]}
