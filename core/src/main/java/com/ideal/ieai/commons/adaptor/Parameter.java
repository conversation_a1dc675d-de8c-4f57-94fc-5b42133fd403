package com.ideal.ieai.commons.adaptor;

import java.io.Serializable;

public class Parameter implements Cloneable, Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private int id;
	private String name;
	private String Key;
	private String KeyDes;
	private String WaitTime;
	private String TimeOut;
	private String IsScreenBack;
	private String DumpScreenFile;
	
	
	public String getName ()
    {
        return name;
    }
    public void setName ( String name )
    {
        this.name = name;
    }
    public String getIsScreenBack() {
		return IsScreenBack;
	}
	public void setIsScreenBack(String isScreenBack) {
		IsScreenBack = isScreenBack;
	}
	public String getTimeOut() {
		return TimeOut;
	}
	public void setTimeOut(String timeOut) {
		TimeOut = timeOut;
	}
	private String SucceedFlag;
	private String target;
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public int getType() {
		return type;
	}
	public void setType(int type) {
		this.type = type;
	}
	public int getLength() {
		return length;
	}
	public void setLength(int length) {
		this.length = length;
	}
	public int getIo() {
		return io;
	}
	public void setIo(int io) {
		this.io = io;
	}
	public int getPrecision() {
		return precision;
	}
	public void setPrecision(int precision) {
		this.precision = precision;
	}
	private String value;
	private int type;
	private int length;
	private int io;
	private int precision;

	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getKey() {
		return Key;
	}
	public void setKey(String key) {
		Key = key;
	}
	public String getKeyDes() {
		return KeyDes;
	}
	public void setKeyDes(String keyDes) {
		KeyDes = keyDes;
	}
	public String getWaitTime() {
		return WaitTime;
	}
	public void setWaitTime(String waitTime) {
		WaitTime = waitTime;
	}
	public String getSucceedFlag() {
		return SucceedFlag;
	}
	public void setSucceedFlag(String succeedFlag) {
		SucceedFlag = succeedFlag;
	}
	public String getTarget() {
		return target;
	}
	public void setTarget(String target) {
		this.target = target;
	}
	public void setDumpScreenFile(String dumpScreenFile) {
        DumpScreenFile = dumpScreenFile;
    }

    public String getDumpScreenFile() {
        return DumpScreenFile;
    }
}
