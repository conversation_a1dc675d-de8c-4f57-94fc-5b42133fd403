package com.ideal.ieai.server.platform.repository.computerManage;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;




import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.platform.repository.computerManage.shell.ComputerManageService;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.ic.hccomputeroperate.HcComputerForDelAgentModel;
import com.ideal.ieai.server.repository.hd.ic.hccomputeroperate.HcComputerOperateManager;
import com.ideal.ieai.server.repository.sus.flowstart.prepare.StartPrepareStartMg;


public class ComputerShellMain
{
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";
    private static final String ISCONTAIN = "isContain";
    private static final String ISHAVE = "ishave";
    private static final Logger       _log           = Logger.getLogger(ComputerShellMain.class);
    
    
    public void exeMain(List<Map<String, Object>> dataList){
        Map<String,Object> resp = new HashMap<String,Object>();
        try
        {
            this.validateDeleteAgentPartOne(dataList, resp);
            if(resp.isEmpty()){
                String strDel = "";
                for(int i=0;i<dataList.size();i++){
                    Map bean= dataList.get(i);
                    String iid=bean.get("iid").toString();
                    if(i==0){
                        strDel = iid;
                    }else{
                        strDel = strDel+","+iid;
                    }
                }
                String[] deleteIds = strDel.split(",");
                Long[] answerIds = (Long[])ConvertUtils.convert(deleteIds,Long.class);
                Map<String, Object> deMap = (Map<String, Object>) this.deleteAgentValidForHcPF(answerIds);
                boolean flagg = (Boolean) deMap.get(SUCCESS);
                if(flagg){
                    ComputerManageService service = new ComputerManageService();
                    service.deleteComputerManage(answerIds, "", "异常时间已到自动下线！");
                }else{
                    _log.error(deMap.get(MESSAGE));
                }
            }else{
                _log.error(resp.get(MESSAGE));
            }
           
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        
        
    }
    
    /**
     * 
     * <li>Description:删除前校验,校验是否绑定了默认参数</li> 
     * <AUTHOR>
     * 2020年3月16日 
     * @param dataList
     * @param resp
     * @throws RepositoryException
     * return void
     */
    public void validateDeleteAgentPartOne(List<Map<String, Object>> dataList,Map<String, Object> resp) throws RepositoryException {
        boolean isContain=false;
        Map bean=null;
        String ip="";
        for (int i = 0; i < dataList.size(); i++)
        {
            bean= dataList.get(i);
            Object objIp=bean.get("ip");
            if (null!=objIp)
            {
                ip=objIp.toString();
                isContain=StartPrepareStartMg.getSUS_agentHost_ByIP(ip);
                if (isContain)
                {
                    resp.put(MESSAGE, (ip+"已经被添加至应用变更服务器，删除会级联删除变更服务器，同时会解绑与资源组的绑定关系"));
                    resp.put(SUCCESS, true);
                    resp.put(ISCONTAIN, isContain);
                    return;
                }
                
                isContain=StartPrepareStartMg.getDefualtDateByIP(ip);
                if (isContain)
                {
                    resp.put(MESSAGE, (ip+"已经绑定了默认参数"));
                    resp.put(SUCCESS, true);
                    resp.put(ISCONTAIN, isContain);
                    return;
                }
            }
        }
    }
    
    /**
     * 
     * <li>Description:本次删除的agent存在关联以下设备是否已启动巡检</li> 
     * <AUTHOR>
     * 2020年3月17日 
     * @param deleteIds
     * @param request
     * @return
     * return Object
     */
    public Object deleteAgentValidForHcPF( Long[] deleteIds) {
        Map<String, Object> resp = new HashMap<String, Object>();
        resp.put(SUCCESS, true);
        resp.put(ISHAVE, false);
        try
        {
            Boolean pfHcDelAgent = ServerEnv.getInstance().getBooleanConfigNew(Environment.HC_DEL_COMPUTER_OF_AGENT_SWITCH, true);
            if(pfHcDelAgent) {
                resp = this.deleteComputerManageValidForHc(deleteIds);
                if(resp!=null && !(Boolean)resp.get(ISHAVE)  ) {
                    boolean  suppercheck = ServerEnv.getInstance().getBooleanConfig(Environment.HC_SUPPERCHECK_SWITCH, false);
                    if(suppercheck) {
                        resp = this.deleteComputerManageValidForNewHc(deleteIds);
                    }
                }
            } 
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(ISHAVE, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }
    public Map deleteComputerManageValidForHc(Long[] deleteIds) {
        Map res = new HashMap();
        HcComputerOperateManager instance = HcComputerOperateManager.getInstance();
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection("deleteComputerManageValidForHc", _log, Constants.IEAI_HEALTH_INSPECTION);
            List<HcComputerForDelAgentModel> models =  instance.getHcStartComputers(deleteIds, conn);
            
            List<Long> flowids = new ArrayList<Long>();
            List<Long> cpids = new ArrayList<Long>();
            List<String> ips = new ArrayList<String>();
            boolean ishave = false;
            if(models!=null && !models.isEmpty()) {
                ishave = true;
                for(HcComputerForDelAgentModel model:models) {
                    flowids.add(model.getFlowid());
                    cpids.add(model.getCpid());
                    ips.add(model.getIp());
                }
            }
            
            String flowidsStr = flowids.isEmpty()?"":StringUtils.join(flowids, ",");
            String cpidsStr = cpids.isEmpty()?"":StringUtils.join(cpids, ",");
            String ipsStr = ips.isEmpty()?"":StringUtils.join(ips, ",");
            
            res.put(Constants.STR_SUCCESS, true);
            res.put("flowids", flowidsStr);
            res.put("cpids", cpidsStr);
            res.put("ips", ipsStr); 
            res.put("ishave", ishave); 
            res.put(Constants.STR_MESSAGE, "查询成功！");
        } catch (RepositoryException e)
        {
            _log.error("验证删除的agent是否关联已启动巡检设备出错", e);
            res.put(Constants.STR_SUCCESS, false);
            res.put(Constants.STR_MESSAGE, "验证出错！");
        }finally {
            DBResource.closeConnection(conn, "deleteComputerManageValidForHc", _log);
        }
        return res;
        
    }
    public Map deleteComputerManageValidForNewHc(Long[] deleteIds) {
        Map res = new HashMap();
        HcComputerOperateManager instance = HcComputerOperateManager.getInstance();
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection("deleteComputerManageValidForNewHc", _log, Constants.IEAI_HEALTH_INSPECTION);
            List<HcComputerForDelAgentModel> models =  instance.getNewHcStartComputers(deleteIds, conn);
            
            List<Long> cpids = new ArrayList<Long>();
            List<String> ips = new ArrayList<String>();
            boolean ishave = false;
            if(models!=null && !models.isEmpty()) {
                ishave = true;
                for(HcComputerForDelAgentModel model:models) {
                    cpids.add(model.getCpid());
                    ips.add(model.getIp());
                }
            }
            
            String cpidsStr = cpids.isEmpty()?"":StringUtils.join(cpids, ",");
            String ipsStr = ips.isEmpty()?"":StringUtils.join(ips, ",");
            
            res.put(Constants.STR_SUCCESS, true);
            res.put("cpids", cpidsStr);
            res.put("ips", ipsStr); 
            res.put("ishave", ishave); 
            res.put(Constants.STR_MESSAGE, "查询成功！");
        } catch (RepositoryException e)
        {
            _log.error("验证删除的agent是否关联已启动巡检设备出错", e);
            res.put(Constants.STR_SUCCESS, false);
            res.put(Constants.STR_MESSAGE, "验证出错！");
        }finally {
            DBResource.closeConnection(conn, "deleteComputerManageValidForNewHc", _log);
        }
        return res;
        
    }
    

}
