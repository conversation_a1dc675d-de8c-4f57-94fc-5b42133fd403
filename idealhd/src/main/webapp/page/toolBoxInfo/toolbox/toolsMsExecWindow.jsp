<%@page contentType="text/html; charset=utf-8"%>

<html>
<head>
    <script type="text/javascript">
        var toolsMsIID = '<%=request.getAttribute("iid")%>';
        var pageType = '<%=request.getAttribute("pageType")%>';
        var toolsMsExecWindowItoolTypeId= '<%=request.getAttribute("itoolTypeId")%>';
        var toolsMsExecWindowItoolName = '<%=request.getAttribute("itoolName")%>';
        var toolsMsExecWindowIsHighRisk = '<%=request.getAttribute("isHighRisk")%>';
        var relid = '<%=request.getAttribute("relid")%>';
        var nEventid = '<%=request.getAttribute("nEventid")%>';
        var toolsMsExecWindowIdeliveryStatus = '<%=request.getAttribute("ideliveryStatus")%>';
        var toolsMsExecWindowFlag=true;
        if(toolsMsExecWindowIdeliveryStatus=='1' || toolsMsExecWindowIdeliveryStatus=='2'){
            toolsMsExecWindowFlag=false;
        }
        var toolsMsExecWindownow=Date.now();
        $(document).ready(function(){
            $("#toolsMsExecWindowDiv").attr('id',$("#toolsMsExecWindowDiv").attr('id')+toolsMsExecWindownow)
        });
    </script>
    <script type="text/javascript" src="<%=request.getContextPath()%>/page/toolBoxInfo/toolbox/toolsMsExecWindow.js"></script>
</head>
<body>
<div id="toolsMsExecWindowDiv" style="width: 100%;height: 100%">
</div>
</body>
</html>