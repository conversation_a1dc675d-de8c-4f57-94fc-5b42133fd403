package com.ideal.ieai.proxy.engine;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.ideal.util.StringUtil;

public class ReLoadProxyEnv
{
    public static Properties      properties;

    private static ReLoadProxyEnv configReader;

    public static ReLoadProxyEnv getInstance ()
    {
        if (null == configReader)
        {
            configReader = new ReLoadProxyEnv();
        }
        return configReader;
    }

    public ReLoadProxyEnv()
    {
        try
        {
            init();
        } catch (IOException e)
        {
            e.printStackTrace();
        }
    }

    public Properties getProperties () throws IOException
    {
        Properties pro = new Properties();
        File file = null;
        file = new File(ProxyEnv.getInstance().getProxyConfigFileName());
        FileInputStream fis = new FileInputStream(file);
        InputStreamReader in = new InputStreamReader(fis, "GBK");
        pro.load(in);
        fis.close();
        return pro;
    }

    public void init () throws IOException
    {
        properties = getProperties();
    }

    public String getProperties ( String key )
    {
        return properties.getProperty(key);
    }

    public String getProperties ( String key, String defaultValue )
    {
        return properties.getProperty(key, defaultValue);
    }

    static final String[] Ys = { "Y", "YES", "TRUE", "T" };

    public boolean getBooleanProperties ( String key, boolean defaultValue )
    {
        String val = properties.getProperty(key);
        if (StringUtil.isEmptyStr(val))
            return defaultValue;
        val = val.toUpperCase();
        for (int i = 0; i < Ys.length; i++)
        {
            if (Ys[i].equals(val))
                return true;
        }
        return false;

    }

    public boolean isNumeric ( String str )
    {
        Pattern pattern = Pattern.compile("[0-9]*");
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches())
        {
            return false;
        }
        return true;

    }

    public String getOneDay ( String cfgYear )
    {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // Calendar calendar = Calendar.getInstance();
        long stime = 0;
        long etime = 0;
        if (!"".equals(cfgYear) && cfgYear.length() == 8 && this.isNumeric(cfgYear))
        {
            // calendar.set(calendar.YEAR, Integer.parseInt(cfgYear.substring(0, 4)));
            // calendar.set(calendar.MONTH, (Integer.parseInt(cfgYear.substring(4, 6)) - 1));
            // calendar.set(calendar.DAY_OF_MONTH, Integer.parseInt(cfgYear.substring(6, 8)));
        } else
        {
            cfgYear = sdf.format(new Date());
        }
        try
        {
            stime = formatter.parse(cfgYear + "000000").getTime();
            etime = formatter.parse(cfgYear + "235959").getTime();
        } catch (ParseException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        // calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 1);
        // stime = calendar.getTimeInMillis();

        // cfgYear = String.valueOf(calendar.get(Calendar.YEAR));
        // }
        return stime + ":" + etime;
    }

    /*
     * name of config properties
     */
    public static final String DAILYCHECK     = "dailycheck";
    public static final String AVGTIMECLOSE   = "avgTimeClose";
    public static final String DAILYCHECKTIME = "dailychecktime";
    public static final String SETTIME        = "setTime";
    public static final String ACTRUNMONTH    = "actRunMonth";
    public static final String MONTHNUM       = "monthnum";
    public static final String WEEKNUM        = "weeknum";
    public static final String DAYNUM         = "daynum";
    public static final String DEFNUM         = "defnum";
    public static final String KEYFLOWCHECK   = "keyflowswitch";

    public String getStringConfig ( String Key, String Default )
    {
        String ret = properties.getProperty(Key);
        return isEmptyStr(ret) ? Default : ret.trim();
    }

    public int getIntConfig ( String Key, int Default )
    {
        try
        {
            return Integer.parseInt(properties.getProperty(Key));
        } catch (NumberFormatException ex)
        {
            return Default;
        }
    }

    public static final boolean isEmptyStr ( String Str )
    {
        return null == Str || Str.length() == 0;
    }
}
