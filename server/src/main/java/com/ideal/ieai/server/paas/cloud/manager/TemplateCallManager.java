package com.ideal.ieai.server.paas.cloud.manager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.paas.cloud.service.bean.TemplateCallBean;
import com.ideal.ieai.server.poc.DBUtilsNew;
import com.ideal.ieai.server.poc.audit.AuditBean;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;

/**
 * <ul>
 * <li>Title: TemplateCallManager.java</li>
 * <li>Description: 模板调用活动涉及到的持久层</li>
 * <li>Copyright: Copyright 2020</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020-10-15
 */
public class TemplateCallManager
{
    private Logger                     log     = Logger.getLogger(this.getClass());
    private static TemplateCallManager intance = new TemplateCallManager();

    public static TemplateCallManager getInstance ()
    {
        if (intance == null)
        {
            intance = new TemplateCallManager();
        }
        return intance;
    }

    /**
     * <li>Description:根据订单id获取模板调用活动调起的模板实例数量</li> 
     * <AUTHOR>
     * 2020-10-17 
     * @return
     * return int
     */
    public Long getCallTemplateNumbers ( String orderuuid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "SELECT COUNT(IID) FROM IEAI_TEMPLATE_CALL_REL WHERE ITEMPLATEUUID = ? ";
        Object[] params = { orderuuid };
        return db.count(sql, params);
    }
    
    
    public TemplateCallBean getCallTemplateRel ( Long iorderid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "SELECT * FROM IEAI_TEMPLATE_CALL_REL WHERE IORDERID = ? ";
        Object[] params = { iorderid };
        return db.get(sql, params,new TemplateCallBean());
    }
    
    /**
     * <li>Description: 根据orderuuid更新远程返回的被调模板实例对应数据的状态。</li> 
     * <AUTHOR>
     * 2020-10-17 
     * @param orderuuid
     * @param state
     * @return
     * return int
     */
    public int updateCallTemplateStatue ( String orderuuid, Integer state )
    {
        Connection conn = null;
        PreparedStatement ps = null;
        String sql = "UPDATE IEAI_TEMPLATE_CALL_REL SET ISTATE = ? WHERE ITEMPLATEUUID = ? ";
        int res = 0;
        
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_PAAS);
            ps =   conn.prepareStatement(sql);
            ps.setInt(1, state);
            ps.setString(2, orderuuid);
            res =   ps.executeUpdate();
            conn.commit();
        } catch (DBException e)
        {
            e.printStackTrace();
        } catch (SQLException e)
        {
            e.printStackTrace();
        }finally {
            DBResource.closePSConn(conn, ps, "updateCallTemplateStatue", log);
        }
        
        return res;
        
                
//        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
//        String sql = "UPDATE IEAI_TEMPLATE_CALL_REL SET ISTATE = ? WHERE ITEMPLATEUUID = ? ";
//        Object[] params = { state, orderuuid };
//        return db.update(sql, params);
    }

    /**
     * <li>Description: 用于中台获取模板调用活动调起实例数据。
     * 目的是基于给多个或单个模板实例，对模板调用活动进行状态更新，以进行后续活动的驱动。</li> 
     * 根据模板实例的状态，查询需要进行状态更新的模板调用活动。
     * 逻辑是：当前活动对应模板实例是都是完成和终止状态时，这个活动需要进入到结束状态。
     * <AUTHOR>
     * 2020-10-17 
     * @param ip 
     * @return
     * return List<TemplateCallRetBean>
     */
    public List<TemplateCallBean> listNeedUpdateState (String ip)
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "SELECT * FROM (SELECT A.IID, A.IORDERID,A.IEXECACTIID,A.IFLOWID,  (SELECT COUNT(1) AS ALLNUM FROM IEAI_TEMPLATE_CALL_REL R1 WHERE A.IID = R1.ICALLACTID) AS ALLNUM, (SELECT COUNT(1) AS OVERNUM FROM IEAI_TEMPLATE_CALL_REL R2 WHERE A.IID = R2.ICALLACTID AND R2.ISTATE IN (5, 6)) AS OVERNUM FROM IEAI_TEMPLATE_CALL_ACT A WHERE A.ISTATE <> -1) NEED INNER JOIN ieai_workflowinstance AB on AB.iflowid = NEED.iflowid where AB.IHOSTNAME = ? and  NEED.ALLNUM = NEED.OVERNUM AND NEED.ALLNUM <> 0 ";
        return db.list(sql,new Object[] {ip}, new TemplateCallBean());
    }

    /**
     * <li>Description: 根据iid删除运行时中的调用活动</li> 
     * <AUTHOR>
     * 2020-10-17 
     * @param iid
     * @return
     * return int
     */
    public int deleteTemplateCallRuntime ( Long iid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        return db.deleteByIid("IEAI_TEMPLATE_CALL_ACT", iid);
    }

    public TemplateCallBean get ( Long iid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        return db.getTableBean("IEAI_TEMPLATE_CALL_ACT", new TemplateCallBean(), iid);
    }
    
    public Long insertTemplateCallRel(TemplateCallBean bean)
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        Long iid = db.createID("IEAI_TEMPLATE_CALL_REL");
        String sql = "INSERT INTO IEAI_TEMPLATE_CALL_REL(IID,ICALLACTID, IORDERID, IFLOWID, ITEMPLATEUUID,ISTATE)  VALUES(?,?,?,?,?,?)";
        Object[] insertdata = { iid, bean.getIcallactid(), bean.getIorderid(), 0L,  bean.getItemplateuuid(), bean.getIstate()};
        db.insert(sql, insertdata, new TemplateCallBean());
        return iid;
    }
    
    public Long insertTemplateCallAct(TemplateCallBean bean)
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        Long iid = db.createID("IEAI_TEMPLATE_CALL_ACT");
        String sql = "INSERT INTO IEAI_TEMPLATE_CALL_ACT(IID, IORDERID, IFLOWID, ISTATE,IEXECACTIID,NODENAME,ITEMPLATETYPE)  VALUES(?,?,?,?,?,?,?)";
        Object[] insertdata = { iid, bean.getIorderid(), bean.getIflowid(),  4, bean.getIexecactiid(),bean.getNodename(),bean.getItemplatetype()};
        db.insert(sql, insertdata, new AuditBean());
        return iid;
    }
    
    public Integer update(TemplateCallBean bean)
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "UPDATE IEAI_TEMPLATE_CALL_ACT SET ISTATE = ? WHERE IID = ?";
        Object[] insertdata = {  bean.getIstate(), bean.getIid()};
        return db.update(sql, insertdata);
    }
    
    public Integer updateRel(TemplateCallBean bean)
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "UPDATE IEAI_TEMPLATE_CALL_REL SET IFLOWID = ? ,ISTATE=? WHERE IID = ?";
        Object[] insertdata = {  bean.getIflowid(), bean.getIstate(), bean.getIid()};
        return db.update(sql, insertdata);
    }
    
    
    public TemplateCallBean getByExecactiid ( Long execactiid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "SELECT * FROM IEAI_TEMPLATE_CALL_ACT WHERE IEXECACTIID = ?";
        Object[] param = { execactiid };
        return db.get(sql, param, new TemplateCallBean());
    }
    
    public TemplateCallBean getRelByExecactiid ( Long execactiid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "SELECT R.IID,R.IFLOWID FROM IEAI_TEMPLATE_CALL_ACT A,IEAI_TEMPLATE_CALL_REL R WHERE A.IID = R.ICALLACTID AND IEXECACTIID = ?";
        Object[] param = { execactiid };
        return db.get(sql, param, new TemplateCallBean());
    }
    
    public int deleteTemplateCallRelByIcallactid ( Long icallactid )
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "DELETE FROM  IEAI_TEMPLATE_CALL_REL WHERE ICALLACTID = ?";
        Object[] param = {icallactid};
        return db.delete(sql, param);
    }
    
    /**
     * <li>Description: 实在找不到订单表的单条数据查找持久层方法，写在模板调用层了</li> 
     * <AUTHOR>
     * 2020-10-20 
     * @param iid
     * @return
     * return PaasOrderBean
     */
    public PaasOrderBean getPaasOrder(Long iid)
    {
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        return db.getTableBean("IEAI_PAAS_ORDER_CACHE", new PaasOrderBean(), iid);
    }
    
    
    public List<TemplateCallBean> getZHFlowList(long orderid , String nodename){
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "SELECT A.IENVID, B.IFLOWID,B.ITEMPLATEUUID ,A.IORDERID,B.ISTATE  FROM  IEAI_TEMPLATE_CALL_ACT A "
                + "LEFT JOIN IEAI_TEMPLATE_CALL_REL B ON A.IID = B.ICALLACTID "
                + "WHERE A.IORDERID=? AND A.NODENAME = ? and B.ITEMPLATEUUID is not null";
        Object[] obj = {orderid,nodename};
        return db.list(sql, obj,new TemplateCallBean());
    }
    
    public long getZHFlowCount(long orderid , String nodename){
        DBUtilsNew db = new DBUtilsNew(Constants.IEAI_PAAS);
        String sql = "select  count(*)  from  IEAI_TEMPLATE_CALL_ACT a "
                + "left join IEAI_TEMPLATE_CALL_REL b on a.IID = b.ICALLACTID "
                + "where a.IORDERID=? and a.NODENAME = ?";
        Object[] obj = {orderid,nodename};
        return db.count(sql, obj);
    }
}
