package com.ideal.service.platform.ftpinfo;

import com.ideal.common.utils.ParseJson;
import com.ideal.common.utils.Tools;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.util.Jcrypt;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.hd.platform.ftpinfo.FtpInfoManager;
import com.ideal.ieai.server.repository.hd.platform.ftpinfo.model.FtpInfo;
import com.ideal.ieai.server.repository.hd.platform.ftpinfo.model.FtpParamInfo;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONException;
import org.json.JSONObject;

import java.sql.Connection;
import java.util.*;

/**
 * <ul>
 * <li>Title: FtpInfoService.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2015年11月9日
 */
public class FtpInfoService
{

    private static final Logger logger            = Logger.getLogger(FtpInfoService.class);
    private FtpInfoManager      ftpInfoManager    = FtpInfoManager.getInstance();

    private static final long   CHECKITEM_ID_NULL = -1;

    /**
     * <li>Description:查询所有FTP信息</li>
     * 
     * <AUTHOR> 2015年11月10日
     * @return
     * @throws RepositoryException return List
     */
    public List getAllFtpInfo ( int type ) throws RepositoryException
    {
        return ftpInfoManager.getAllFtpInfo(type);
    }

    /**   
     * @Title: getAllFtpInfoForCollection   
     * @Description: V4.7.22 信息采集,获取服务信息配置信息查询sql   
     * @param sysType
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yue_sun
     * @date:   2019-10-21 11:31:59  
     */
    public Map<String, Object> getAllFtpInfoForCollection ( FtpParamInfo paramInfo, int type )
            throws RepositoryException
    {
        return ftpInfoManager.getAllFtpInfoForCollection(paramInfo, type);
    }

    /**
     * 
     * @Title: getFtpInfoByFtpType   
     * @Description: 根据服务分类，获取服务数据   
     * @param type
     * @param ftpType
     * @return
     * @throws RepositoryException      
     * @author: Sayai 
     * @date:   2018年10月20日 下午5:01:06
     */
    public List<FtpInfo> getFtpInfoByFtpType ( int type, int ftpType ) throws RepositoryException
    {
        return ftpInfoManager.getFtpInfoByFtpType(type, ftpType);
    }

    /**
     * <li>Description:获取FTP类型</li>
     * 
     * <AUTHOR> 2015年11月10日
     * @return
     * @throws RepositoryException return List
     */
    public List getFtpTypeList () throws RepositoryException
    {
        return ftpInfoManager.getFtptypeList();
    }

    /**
     * <li>Description:保存FTP信息</li>
     * 
     * <AUTHOR> 2015年11月10日
     * @param jsonData
     * @return
     * @throws Exception return boolean
     */
    public boolean saveFtpInfo ( String jsonData ) throws Exception
    {
        List<Map<String, Object>> checkItems = null;
        try
        {
            checkItems = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            logger.error("解析服务器配置数据出错");
            throw new Exception(e);
        }
        List<FtpInfo> ftpInfos = new ArrayList<FtpInfo>();
        for (int i = 0; i < checkItems.size(); i++)
        {
            Map<String, Object> checkItemMap = checkItems.get(i);
            ftpInfos.add(convertToCheckItem(checkItemMap));
        }
        boolean res = false;
        try
        {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get("baseConn");
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                orgSql = ftpInfoManager.saveFtpInfo(ftpInfos, baseConn);

                if ((Boolean) orgSql.get("success"))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                        (List<String>) orgSql.get("rollbackSqls"));
                }
            }
        } catch (DBException e)
        {
            e.printStackTrace();
        } catch (RepositoryException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return res;
    }

    /**   
     * @Title: saveFtpInfoForIcCollection   
     * @Description: V4.7.22 保存信息采集服务信息配置相关信息
     * @param jsonData
     * @return
     * @throws Exception      
     * @author: yue_sun
     * @date:   2019-10-21 08:56:47  
     */
    public boolean saveFtpInfoForIcCollection ( String jsonData ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        List<Map<String, Object>> checkItems = null;
        try
        {
            checkItems = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            logger.error("解析服务器配置数据出错");
            throw new Exception(e);
        }
        List<FtpInfo> ftpInfos = new ArrayList<FtpInfo>();
        for (int i = 0; i < checkItems.size(); i++)
        {
            Map<String, Object> checkItemMap = checkItems.get(i);
            ftpInfos.add(convertToCheckItem(checkItemMap));
        }
        boolean res = false;
        try
        {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get("baseConn");
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                // V4.7.22 信息采集，服务信息配置页面保存功能
                orgSql = ftpInfoManager.saveFtpInfoForIcCollection(ftpInfos, baseConn);

                if ((Boolean) orgSql.get("success"))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                        (List<String>) orgSql.get("rollbackSqls"));
                }
            }
        } catch (DBException e)
        {
            logger.error(method, e);
        } catch (RepositoryException e)
        {
            logger.error(method, e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } catch (Exception e)
        {
            logger.error(method, e);
        }
        return res;
    }

    /**   
     * @Title: getFtpInfos   
     * @Description: 获取保存信息的jsonDate并将信息转换为list  
     * @param jsonData
     * @return
     * @throws JSONException      
     * @author: yue_sun 
     * @date:   2019年1月25日 上午10:25:53   
     */
    public List<FtpInfo> getFtpInfos ( String jsonData ) throws JSONException
    {
        List<Map<String, Object>> checkItems = null;
        try
        {
            checkItems = ParseJson.JSON2List(jsonData);
        } catch (JSONException e)
        {
            throw new JSONException(e);
        }
        List<FtpInfo> ftpInfos = new ArrayList<FtpInfo>();
        for (int i = 0; i < checkItems.size(); i++)
        {
            Map<String, Object> checkItemMap = checkItems.get(i);
            ftpInfos.add(convertToCheckItem(checkItemMap));
        }
        return ftpInfos;
    }

    /**   
     * @Title: validateFtpFileIssuanceError   
     * @Description: 校验文件下发相关的配置错误信息
     * （目前校验以下两个配置，后续可修改增加）
     * 校验问题：
     * 1.校验每一个服务类型下，主服务只能选择一个
     * 2.校验配置了中转平台的FTP服务器只能设置一台 
     * @param ftpInfos
     * @return      
     * @author: yue_sun 
     * @date:   2019年1月25日 上午10:32:57   
     */
    @SuppressWarnings("unchecked")
    public Object validateFtpFileIssuanceError ( String jsonData, int sysType ) throws JSONException
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            List<FtpInfo> ftpInfos = getFtpInfos(jsonData);
            // 校验文件下发相关的配置错误信息
            resp = (Map<String, Object>) FtpInfoManager.getInstance().validateFtpFileIssuanceError(ftpInfos, sysType);
        } catch (JSONException e)
        {
            logger.error("解析服务器配置数据出错");
            throw new JSONException(e);
        }

        return resp;
    }

    /**
     * <li>Description:数据转换</li>
     * 
     * <AUTHOR> 2015年11月10日
     * @param rgMap
     * @return return FtpInfo
     */
    private FtpInfo convertToCheckItem ( Map<String, Object> rgMap )
    {
        FtpInfo res = new FtpInfo();
        Object idd = rgMap.get("fid");
        if (idd == JSONObject.NULL || "".equals(String.valueOf(idd)))
        {
            res.setFid(CHECKITEM_ID_NULL);
        } else
        {
            res.setFid(Long.parseLong(String.valueOf(rgMap.get("fid"))));
        }
        res.setScripttag(((String) rgMap.get("scripttag")).trim());
        res.setScriptname(((String) rgMap.get("scriptname")).trim());
        res.setFtpip(String.valueOf(String.valueOf(rgMap.get("ftpip"))));
        res.setFtpport(Integer.parseInt(rgMap.get("ftpport").toString().trim()));
        res.setFtpuser(((String) rgMap.get("ftpuser")).trim());
        String password = (String) rgMap.get("ftppassword");
        if (password.contains("Shenandoah")){
            String encodedPassword = password.substring(password.indexOf(":") + 1); // 获取密码部分并去掉冒号
            byte[] decodedBytes = Base64.getDecoder().decode(encodedPassword); // 解码为字节数组
            password = new String(decodedBytes); // 将字节数组转换为字符串
        }
        password = Jcrypt.encrypt(password);
        res.setFtppassword(password);
        res.setRootpath((String) rgMap.get("rootpath"));
        res.setFtptype((String) rgMap.get("ftptype"));
        res.setNetworksegment((String) rgMap.get("networksegment"));
        res.setIsMajor(Integer.parseInt(String.valueOf(rgMap.get("isMajor"))));
        if (null != rgMap.get("sysType"))
        {
            res.setSysType(Integer.parseInt(String.valueOf(rgMap.get("sysType"))));
        }
        if (null != rgMap.get("deviceType"))
        {
            res.setDeviceType((String) rgMap.get("deviceType"));
        }
        res.setDbInsName((String) rgMap.get("dbInsName"));
        res.setUserPermission((String) rgMap.get("userPermission"));
        res.setGroupPermission((String) rgMap.get("groupPermission"));
        res.setParameterPermission((String) rgMap.get("parameterPermission"));
        return res;
    }

    /**
     * <li>Description:删除FTP信息</li>
     * 
     * <AUTHOR> 2015年11月10日
     * @param deleteIds
     * @return
     * @throws Exception return boolean
     */
    public boolean deleteFtpinfo ( Long[] deleteIds ) throws Exception
    {
        String fids = StringUtils.join(deleteIds, ",");
        boolean res = false;
        try
        {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get("baseConn");
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                orgSql = ftpInfoManager.delFtpInfo(fids, baseConn);

                if ((Boolean) orgSql.get("success"))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"),
                        (List<String>) orgSql.get("rollbackSqls"));
                }
            }
        } catch (DBException e)
        {
            e.printStackTrace();
        } catch (RepositoryException e)
        {
            throw new RepositoryException(ServerError.ERR_DB_DELETE);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return res;
    }
}
