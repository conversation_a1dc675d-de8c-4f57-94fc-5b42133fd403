package com.ideal.ieai.commons.task;

/**
 * This class is for task act keys for act config map key.
 * 
 * <AUTHOR>
 */
public class TaskKeys
{
    /**
     * One of activity's output parameter represents task's name.
     */
    public static final String TaskName               = "taskName";

    /**
     * One of activity's output parameter represents task's id.
     */
    public static final String TaskId                 = "taskId";

    /**
     * One of activity's output parameter represents task's description.
     */
    public static final String Description            = "description";

    /**
     * One of activity's output parameter represents task's assignees.
     */
    public static final String Assignees              = "assignees";

    /**
     * One of activity's output parameter represents task's assigneeGroups.
     */
    public static final String AssigneeGroups         = "assigneeGroups";

    /**
     * One of activity's output parameter represents task's dueTime.
     */
    public static final String DueTime                = "deadLine";

    /**
     * One of activity's output parameter represents task's createTime.
     */
    public static final String CreateTime             = "createTime";

    /**
     * One of activity's output parameter represents task's takeoverTime.
     */
    public static final String TakeoverTime           = "acquireTime";

    /**
     * One of activity's output parameter represents task's delegateTime.
     */
    public static final String DelegateTime           = "delegateTime";

    /**
     * One of activity's output parameter represents task's forwardTime.
     */
    public static final String ForwardTime            = "forwardTime";

    /**
     * One of activity's output parameter represents task's overtaker.
     */
    public static final String Overtaker              = "taskOwner";

    /**
     * One of activity's output parameter represents task's delegater.
     */
    public static final String Delegater              = "delegateUser";

    /**
     * One of activity's output parameter represents task's taskstatus.
     */
    public static final String Taskstatus             = "taskState";
    /**
     * Task properties
     */
    public static final String Properties             = "properties";

    /**
     * The corresponding key name of a task infos in Config.
     */
    public static final String Data                   = "Data";

    public static final String Id                     = "id";
    public static final String Name                   = "name";
    /**
     * this is a key in config map,its value denote the name referenced task activity ;if the value
     * is "null",the activity is a task created actvity.
     */
    public static final String RefActName             = "RefActName";
    public static final String IDManager              = "IDManager";
    public static final String LastOperation          = "lastOperation";

    /**
     * Next all are constants.
     */

    public static final String TRUE                   = "true";
    public static final String FALSE                  = "false";
    public static final String ABSTIME                = "absolute due time";
    public static final String RelativeToFlowStart    = "due time form flow startting";
    public static final String RelativeToTaskCreation = "due time form task created";
    public static final String NoLimit                = "no due time";
    
    public final static String AttachmentSchemaName                   = "attachment";

    public final static String AttachmentSchema_AttachName            = "attachmentName";

    public final static String AttachmentSchema_AttachContent         = "attachmentContent";

    public final static String DueTimeSchemaName                      = "dueTime";

    public final static String SchemaFieldName_Prolong_Time           = "prolongTime";

    public final static String SchemaFieldName_Absdeadline            = "absDeadLine";

    public final static String SchemaFieldName_RelativeToFlowStart    = "relativeToFlowStart";

    public final static String SchemaFieldName_RelativeToTaskCreation = "relativeToTaskCreation";

    public final static String SchemaFieldName_NoLimit                = "noLimit";
    
    //add by gang_guo for mailStressConfig
    public static final String   CBX_YORNWARN                = "cbxYorNWarn";
    public static final String   WARN_CODE_TXTPANE           = "warnCodeTxtPane";
    public static final String   WARN_INFO_TXTPANE           = "warnInfoTxtPane";
    public static final String   DISPOSE_TIME_TXTPANE        = "disposeTimeTxtPane";

       public static final String   CBX_WARN                   = "yornWarn";
    public static final String   WARN_CODE                   = "warnCode";
    public static final String   WARN_INFO                   = "warnInfo";
    public static final String   DISPOSE_TIME                = "disposeTime";
  //add by gang_guo for mailStressConfig
}
