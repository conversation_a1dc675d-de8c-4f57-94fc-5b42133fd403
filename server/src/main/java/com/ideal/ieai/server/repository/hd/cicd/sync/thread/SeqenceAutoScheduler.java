package com.ideal.ieai.server.repository.hd.cicd.sync.thread;

import com.ideal.ieai.core.Environment;
import org.apache.log4j.Logger;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;

/**
 *发送本周投产系统定时任务
 * <AUTHOR>
 */
public class SeqenceAutoScheduler
{
    private  Scheduler  scheduler        = getSchedule();
    private  Logger     _log             = Logger.getLogger(SeqenceAutoScheduler.class);

    public void startAutoSeqenceJob () throws Exception
    {
        // 定时任务表达式
        String cron          = "";
        String triggerName      = "SeqenceAuto";
        String triggerGroupName = "SeqenceAutoGroup";
        // 4、将调度器与任务绑定,开始执行任务
        try
        {
            cron = Environment.getInstance().getSysConfig(Environment.FJNX_SYNC_AUTOSYSTEM_CRON,"");
            TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, triggerGroupName);
            CronTrigger trigger   = (CronTrigger) scheduler.getTrigger(triggerKey);
            try{
                if (CronExpression.isValidExpression(cron)) {
                    if (trigger == null) {
                        // 2、创建JobDetail实例，并与SynchronizationCmdbJob类绑定(Job执行内容),name是它们在这个sheduler里面的唯一标识。
                        // 要更新一个JobDetail定义，只需要设置一个name相同的JobDetail实例即可,group是一个组织单元，sheduler会提供一些对整组操作的API，比如
                        JobDetail jobDetail = JobBuilder.newJob(SeqenceAutoJob.class).withIdentity(triggerName, triggerGroupName)
                                .build();
                        // cron表达式//每天
                        CronScheduleBuilder cronSchedule = CronScheduleBuilder.cronSchedule(cron);
                        // 3、构建Trigger实例
                        Trigger trigger1 = TriggerBuilder.newTrigger().withIdentity(triggerName, triggerGroupName).withSchedule(cronSchedule).build();  // 一直执行
                        // 立即生效
                        scheduler.scheduleJob(jobDetail, trigger1);
                        if (!scheduler.isStarted())
                        {
                            scheduler.start();
                        }
                        _log.info("发送本周投产系统周期执行启动成功:"+ triggerName+",corn表达式为:" + cron);
                    }
                }else{
                    _log.error("发送本周投产系统周期执行时间配置有误:"+ triggerName+",corn表达式为:" + cron);
                }

            }catch(Exception e){
                _log.error("发送本周投产系统周期执行失败:"+ triggerName+"表达式:"+ cron);
            }
        } catch (SchedulerException e)
        {
            _log.error("startAutoSeqenceJob() is error："+e.getMessage(),e);
        }
    }


    private static Scheduler getSchedule ()
    {
        StdSchedulerFactory stdSchedulerFactory = new StdSchedulerFactory();
        try
        {
            Scheduler scheduler1 = stdSchedulerFactory.getScheduler();
            return scheduler1;
        } catch (SchedulerException e)
        {
            e.printStackTrace();
            return null;
        }
    }

}
