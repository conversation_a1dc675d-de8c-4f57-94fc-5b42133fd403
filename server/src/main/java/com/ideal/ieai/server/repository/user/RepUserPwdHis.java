package com.ideal.ieai.server.repository.user;

public class RepUserPwdHis
{
    private long iid;
    private long createTime;
    private String oldPassword;
    private String newPassword;
    private String loginName;
    private String sql;
    private int dbType;
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public long getCreateTime ()
    {
        return createTime;
    }
    public void setCreateTime ( long createTime )
    {
        this.createTime = createTime;
    }
    public String getOldPassword ()
    {
        return oldPassword;
    }
    public void setOldPassword ( String oldPassword )
    {
        this.oldPassword = oldPassword;
    }
    public String getNewPassword ()
    {
        return newPassword;
    }
    public void setNewPassword ( String newPassword )
    {
        this.newPassword = newPassword;
    }
    public String getLoginName ()
    {
        return loginName;
    }
    public void setLoginName ( String loginName )
    {
        this.loginName = loginName;
    }
    public String getSql ()
    {
        return sql;
    }
    public void setSql ( String sql )
    {
        this.sql = sql;
    }
    public int getDbType ()
    {
        return dbType;
    }
    public void setDbType ( int dbType )
    {
        this.dbType = dbType;
    }
    public String getFlagType ()
    {
        return flagType;
    }
    public void setFlagType ( String flagType )
    {
        this.flagType = flagType;
    }
    private String flagType;
}
