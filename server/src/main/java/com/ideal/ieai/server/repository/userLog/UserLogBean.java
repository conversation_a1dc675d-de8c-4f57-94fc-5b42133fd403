package com.ideal.ieai.server.repository.userLog;

public class UserLogBean
{

    private String id;
    private String loginName;
    private String fullName;
    private String operatType;
    private String pageName;
    private String operatTime;
    private String queryCondition;

    
   

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getId ()
    {
        return id;
    }

    public void setId ( String id )
    {
        this.id = id;
    }

    public String getLoginName ()
    {
        return loginName;
    }

    public void setLoginName ( String loginName )
    {
        this.loginName = loginName;
    }

    public String getOperatType ()
    {
        return operatType;
    }

    public void setOperatType ( String operatType )
    {
        this.operatType = operatType;
    }

    public String getPageName ()
    {
        return pageName;
    }

    public void setPageName ( String pageName )
    {
        this.pageName = pageName;
    }

    public String getOperatTime ()
    {
        return operatTime;
    }

    public void setOperatTime ( String operatTime )
    {
        this.operatTime = operatTime;
    }

    public String getQueryCondition ()
    {
        return queryCondition;
    }

    public void setQueryCondition ( String queryCondition )
    {
        this.queryCondition = queryCondition;
    }

}
