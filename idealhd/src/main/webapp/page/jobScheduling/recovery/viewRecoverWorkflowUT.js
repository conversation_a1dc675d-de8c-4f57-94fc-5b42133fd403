var limit = 10;//每页显示30条记录

Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.tip.QuickTipManager.init ();
	
	/** 恢复工作流按钮* */
	var recoverBtn = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    text : "恢复工作流",
	    handler : recoverBtnHandler
	});
	/** 保存按钮* */
	var saveBtn = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
	    textAlign : 'center',
	    width : 70,
	    height : 22,
	    text : "保存",
	    handler : saveBtnHanlder
	});
	/** 按钮栏Panel* */
	var viewRecoverWfl_form = Ext.create('Ext.form.Panel', {
		border : false,
    	buttonAlign : 'right',
	    buttons: [saveBtn , recoverBtn,
	    {
	        text: '返回',
	        handler: returnBtnHandler
	    }]
	});
	//返回按钮处理
	function returnBtnHandler() {
    	var myurl = "utFlowRecover.do";
    	var title = "恢复与调整工作流";
    	forword(myurl,title);
//    	contentPanel.getLoader().load({url: myurl,scripts: true,/*params : {sysid:sysid},*/callback: function(records, operation, success) {
//	    	showImg(myurl);
//	    }});
    }
	
	/** Recover列表model* */
	Ext.define ('recoverModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
//				{
//	                name : 'flowId',
//	                type : 'long'
//	            },
	            {
	                name : 'serialNo',
	                type : 'long'
	            },
	            {
	                name : 'recoverPointName',
	                type : 'string'
	            },
	            {
	                name : 'recoverPointType',
	                type : 'string'
	            },
	            {
	                name : 'recoverPolicy',
	                type : 'string'
	            },
	            {
	                name : 'recoverActDesc',
	                type : 'string'
	            },
	            {
	                name : 'recoverDesc',
	                type : 'string'
	            },
	            {
	                name : 'remoteActState',
	                type : 'string'
	            },
	            {
	                name : 'recoverResult',
	                type : 'string'
	            }
	    ]
	});
	
	// 选择恢复方法，下拉框--Model
	Ext.define ('selMethodModel',
	{ 
	    extend : 'Ext.data.Model',
	    fields : [
				{
				    name : 'imethodId', // 恢复方法id
				    type : 'long'
				},
	            {
	                name : 'imethodName', // 恢复方法name
	                type : 'string'
	            }
	    ]
	});
	// 选择恢复方法，下拉框--Store
	var selMethodStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    model : 'selMethodModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'recover/getSelMethodsForUT.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	
	/** autoRecover列表store* */
	var autoRecoverStore = Ext.create ('Ext.data.Store',
	{
		autoLoad : true,
	    autoDestroy : true,
	    remoteSort: true,
	    model : 'recoverModel',
	    pageSize : limit,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'recover/getAutoRecoverList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	autoRecoverStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			flowId:flowId
		};
		Ext.apply (autoRecoverStore.proxy.extraParams, new_params);
	});
	/** manualRecover列表store* */
	var manualRecoverStore = Ext.create ('Ext.data.Store',
	{
		autoLoad : true,
	    autoDestroy : true,
	    remoteSort: true,
	    model : 'recoverModel',
	    pageSize : limit,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'recover/getManualRecoverList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});
	manualRecoverStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			flowId:flowId
		};
		Ext.apply (manualRecoverStore.proxy.extraParams, new_params);
	});
//	manualRecoverStore.on ('load', function (store, options)
//	{
//		//map[key] = value; 
//		//为备注信息赋值	
//		for (var i = 0; i < store.getCount (); i++)
//		{
//			var record = store.getAt (i);
//			var serialNo = record.get('');
//			var policy = record.get('');
//			map[serialNo] = policy;
//		}
//	});
	
	/** recover列表column* */
	var RecoverColumns = [
	        {
	            text : '序号',
	            width : 40,
//	            locked : true,
	            xtype : 'rownumberer'
	        },
//	        {
//	            text : 'flowID',
//	            dataIndex : 'flowId',
//	            remoteSort: true,
//	            hidden: true,
//	            width : 100
//	        },
	        {
	            text : 'serialNo',
	            dataIndex : 'serialNo',
	            remoteSort: true,
	            hidden: true,
	            width : 100
	        },
	        {
	            text : '恢复点名称',
	            dataIndex : 'recoverPointName',
	            remoteSort: true,
	            width : 150
	        },
	        {
	            text : '类型',
	            dataIndex : 'recoverPointType',
	            remoteSort: true,
	            width : 150
	        },
	        {
	            text : '恢复方法',
	            dataIndex : 'recoverPolicy',
	            remoteSort: true,
	            width : 150
//	            ,renderer:function(value, metaData, record){
//	            	var policy = record.get('recoverPolicy');
//	            	var iid = record.get('serialNo');
////	            	alert('<radioName,policy,value>=<'+radioName+','+policy+','+value+'>');
//	            	//点击事件
//	            	var redoOnclickStr = " onclick=\"changePolicy('"+iid+"','"+1+"');\" ";
//	            	var skipOnclickStr = " onclick=\"changePolicy('"+iid+"','"+2+"');\" ";
//	            	
//	                return "<input name='"+iid+"' type='radio' value='1' "+(policy==1?"checked='checked'":"") +redoOnclickStr+ ">重做</input> "
//	                +"<input name='"+iid+"' type='radio' value='2' "+(policy==2?"checked='checked'":"")+skipOnclickStr+">略过</input> ";
//	            }
	        },
	        {
	            text : '活动说明',
	            dataIndex : 'recoverActDesc',
	            remoteSort: true,
	            width : 200
	        },
	        {
	            text : '恢复说明',
	            dataIndex : 'recoverDesc',
	            remoteSort: true,
//	            width : 200
	            flex:1
	        },
	        {
	            text : '远程活动情况',
	            dataIndex : 'remoteActState',
	            remoteSort: true,
	            width : 150
	        },
	        {
	            text : '恢复结果',
	            dataIndex : 'recoverResult',
	            remoteSort: true,
	            width : 150
	        }
	];
	/** manualRecover列表column* */
	var manualRecoverColumns = [
	        {
	            text : '序号',
	            width : 40,
//	            locked : true,
	            xtype : 'rownumberer'
	        },
//	        {
//	            text : 'flowID',
//	            dataIndex : 'flowId',
//	            remoteSort: true,
//	            hidden: true,
//	            width : 100
//	        },
	        {
	            text : 'serialNo',
	            dataIndex : 'serialNo',
	            remoteSort: true,
	            hidden: true,
	            width : 100
	        },
	        {
	            text : '恢复点名称',
	            dataIndex : 'recoverPointName',
	            remoteSort: true,
	            width : 150
	        },
	        {
	            text : '类型',
	            dataIndex : 'recoverPointType',
	            remoteSort: true,
	            width : 150
	        },
	        {
	            text : '恢复方法',
	            dataIndex : 'recoverPolicy',
	            remoteSort: true,
	            width : 150
	            ,renderer:function(value, metaData, record){
	            	var policy = record.get('recoverPolicy');
	            	var iid = record.get('serialNo');
//	            	alert('<radioName,policy,value>=<'+radioName+','+policy+','+value+'>');
	            	//点击事件
	            	var redoOnclickStr = " onclick=\"changePolicy('"+iid+"','"+1+"');\" ";
	            	var skipOnclickStr = " onclick=\"changePolicy('"+iid+"','"+2+"');\" ";
	            	
	                return "<input name='"+iid+"' type='radio' value='1' "+(policy==1?"checked='checked'":"") +redoOnclickStr+ ">重做</input> "
	                +"<input name='"+iid+"' type='radio' value='2' "+(policy==2?"checked='checked'":"")+skipOnclickStr+">略过</input> ";
	            }
	        },
	        {
	            text : '活动说明',
	            dataIndex : 'recoverActDesc',
	            remoteSort: true,
	            width : 200
	        },
	        {
	            text : '恢复说明',
	            dataIndex : 'recoverDesc',
	            remoteSort: true,
//	            width : 200
	            flex:1
	        },
	        {
	            text : '远程活动情况',
	            dataIndex : 'remoteActState',
	            remoteSort: true,
	            width : 150
	        },
	        {
	            text : '恢复结果',
	            dataIndex : 'recoverResult',
	            remoteSort: true,
	            width : 150
	        }
	];
	
	// 定义复选框
	var autoSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
	// 定义复选框
	var manualSelModel = Ext.create('Ext.selection.CheckboxModel', {
		checkOnly : true,
		listeners : {
			selectionchange : function(selModel, selections) {
			}
		}
	});
//	var pageBar = getBbar(ds,pagelimit);
	/** 带选择设备列表分页工具栏* */
	var autoPageBar = Ext.create ('Ext.PagingToolbar',
	{
		id: 'auto_pageBar',
		pageSize: limit,
	    store : autoRecoverStore,
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	/** 带选择设备列表分页工具栏* */
	var manualPageBar = Ext.create ('Ext.PagingToolbar',
	{
		id: 'manual_pageBar',
		pageSize: limit,
	    store : manualRecoverStore,
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
//	// 定义可编辑grid组件
//	var cellEditing2 = Ext.create('Ext.grid.plugin.CellEditing', {
//		clicksToEdit : 2 
//	});
	/**recover列表gridpanel* */
	var autoRecoverGrid = Ext.create ('Ext.grid.Panel',
	{
		id:'auto_recover_grid',
		height : (contentPanel.getHeight()-25)/2 - 30,
//		selModel : autoSelModel,
		store : autoRecoverStore,
		autoScroll : true,
		columnLines : true,
//		loadMask : {
//			msg : " 数据加载中，请稍等 "
//		},
		columns : RecoverColumns,
//		plugins : [ cellEditing2 ],
   	    bbar:autoPageBar
	});
	
	//选择恢复方法，--下拉框
	var selMethodComBox = Ext.create ('Ext.form.field.ComboBox',
	{
		id:'selMethodComBox',
	    name : 'form_selMethod',
	    fieldLabel : '选择恢复方法',
	    displayField : 'imethodName',
	    valueField : 'imethodId',
//	    emptyText : '--请选择恢复方法--',
	    width:250,
	    labelWidth : 100,
	    padding : '0 0 5 5',
	    editable : true,
	    forceSelection:true,
//	    typeAhead : true,
	    store : selMethodStore,
	    queryMode : 'local',
	    listeners :
	    {
	    	select : function ()
	        {
//	    		alert('select...');
	        }
	    }
	});
//	// 定义可编辑grid组件
//	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
//		clicksToEdit : 2 
//	});
	/**manualRecover列表gridpanel* */
	var manualRecoverGrid = Ext.create ('Ext.grid.Panel',
	{
		id:'manual_recover_grid',
		height : (contentPanel.getHeight()-25)/2 - 65,
//		selModel : manualSelModel,
		store : manualRecoverStore,
		autoScroll : true,
		columnLines : true,
//		loadMask : {
//			msg : " 数据加载中，请稍等 "
//		},
		columns : manualRecoverColumns,
//		plugins : [ cellEditing ],
		dockedItems : [ {
	        xtype : 'toolbar',
	        items : [ {text:'请选择恢复方法：',height:40}/*selMethodComBox, '->', saveBtn , recoverBtn*/ ]
	    }],
   	    bbar:manualPageBar
	});
	
	//上半部分Panel
	var upPanel = Ext.create ("Ext.panel.Panel",
	{
	    width : contentPanel.getWidth (),
	    height : (contentPanel.getHeight ()-25)/2,
	    items : [
	            viewRecoverWfl_form,
	            autoRecoverGrid
	    ]
	});
	// 界面主Panel
	var rightPanel = Ext.create ("Ext.panel.Panel",
	{
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight () - 25,
	    layout : 'border',
	    items : [
	            {
	                region : 'north',
	                xtype : 'panel',
	                width : 200,
//	                collapsible:true,
	                id : 'west-region-container',
	                layout : 'fit',
//	                items : [ /*viewRecoverWfl_form,*/autoRecoverGrid ]
	                items : [ upPanel ]
	            },
	            {
	                region : 'center', // 必须指定中间区域
	                xtype : 'panel',
	                layout : 'fit',
//	                collapsible:true,
	                items : [ manualRecoverGrid ]
	            }
	    ],
	    renderTo : "grid_area"
	});
	
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (rightPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function ()
	{
		rightPanel.setHeight (contentPanel.getHeight () - 25);
		rightPanel.setWidth (contentPanel.getWidth () );
	});
	
	//////////////////////////////////////////////////////////////////////
	//保存按钮方法
	function saveBtnHanlder()
    {
//    	alert('保存。。。');
    	//处理参数
    	var jsonArr = [];
//    	var store = Ext.getCmp("auto_recover_grid").store;
    	var store = Ext.getCmp("manual_recover_grid").store;
    	for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			var myRec = {};
			myRec.serialNo = record.get('serialNo');
			myRec.recoverPolicy = record.get('recoverPolicy');
			jsonArr.push(myRec);
		}
    	
    	//请求后台
		Ext.Ajax.request (
		{
		    url : 'recover/viewSave.do',
		    method : 'POST',
		    params :
		    {
		    	flowId : flowId,
		    	jsonData:Ext.encode(jsonArr)
		    },
		    success : function (response, options)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    
			    if (success)
			    {
			    	Ext.Msg.alert ('提示', "保存成功", function (){
			    		//重新加载数据
			    		store.reload();
			    		//返回
			    		returnBtnHandler();
				    });
			    }
			    else
			    {
				    Ext.MessageBox.show (
				    {
				        width : 300,
				        title : "保存失败",
				        msg : message,
				        buttonText :
				        {
					        yes : '确定'
				        },
				        buttons : Ext.Msg.YES
				    });
			    }
		    },
		    failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
    }
	
	//恢复工作流按钮方法
	function recoverBtnHandler()
    {
//    	alert('恢复工作流。。。');
    	//处理参数
    	var jsonArr = [];
//    	var store = Ext.getCmp("auto_recover_grid").store;
    	var store = Ext.getCmp("manual_recover_grid").store;
    	for (var i = 0; i < store.getCount (); i++)
		{
			var record = store.getAt (i);
			var myRec = {};
			myRec.serialNo = record.get('serialNo');
			myRec.recoverPolicy = record.get('recoverPolicy');
			jsonArr.push(myRec);
		}
    	
    	//请求后台
		Ext.Ajax.request (
		{
		    url : 'recover/viewRecoverFlw.do',
		    method : 'POST',
		    params :
		    {
		    	flowId : flowId,
		    	jsonData:Ext.encode(jsonArr)
		    },
		    success : function (response, options)
		    {
			    var success = Ext.decode (response.responseText).success;
			    var message = Ext.decode (response.responseText).message;
			    
			    if (success)
			    {
			    	Ext.Msg.alert ('提示', "恢复成功", function (){
			    		//重新加载数据
			    		store.reload();
				    });
			    }
			    else
			    {
				    Ext.MessageBox.show (
				    {
				        width : 300,
				        title : "保存失败",
				        msg : message,
				        buttonText :
				        {
					        yes : '确定'
				        },
				        buttons : Ext.Msg.YES
				    });
			    }
		    },
		    failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
    }
	
});


/**
 * 跳转
 * @param forword_url
 * @param targetContentTiel
 */
function forword(forword_url,targetContentTiel){
	showImg(forword_url);
	contentPanel.setTitle(targetContentTiel);
    destroyRubbish(); // 销毁本页垃圾
	contentPanel.getLoader().load({url: forword_url, scripts: true});
	if(Ext.isIE){
		CollectGarbage(); 
	}
}

//单选框点击事件处理
function changePolicy(iid,policy)
{
//	alert('changePolicy...');
//	var store = Ext.getCmp("auto_recover_grid").store;
	var store = Ext.getCmp("manual_recover_grid").store;
	for (var i = 0; i < store.getCount (); i++)
	{
		var record = store.getAt (i);
		var id = record.get('serialNo');
		if(iid==id)
		{
			record.set('recoverPolicy',policy);
//			record.commit();
//			alert('<serialNo,policy>=<'+iid+','+policy+'>');
		}
	}
}

