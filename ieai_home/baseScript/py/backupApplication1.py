#---------------------------------------------------------------------
#       Name: backupApplication.py
#     Author: YJ
import sys, getopt;

#---------------------------------------------------------------------
# Name: backupWasApplication()
# Role: Placeholder for routine to perform the desired action
#---------------------------------------------------------------------

def backupWasApplication( cmdName = 'backupWasApplication' ) :
  missingParms = '%(cmdName)s: Insufficient parameters provided.\n';
    
  #-------------------------------------------------------------------
  # How many user command line parameters were specified?
  #-------------------------------------------------------------------
  argc = len( sys.argv );                   # Number of arguments
  if ( argc < 1 ) :                         # If too few are present,
    print missingParms % locals();          #   tell the user, and
    Usage( cmdName );                       #   provide the Usage info
  else :                                    # otherwise
   # Opts = parseOpts( cmdName );           #  parse the command line
     appName=sys.argv[1]
     filePath=sys.argv[3]

 #------------------------------------------------------------------------
 # badReqdParam = '%(cmdName)s: Missing/Invalid required parameter: %(key)s.\n';
 # for key in Opts.keys() :
 #   val = Opts[ key ];
 #   if ( not val ) or ( not val.strip() ) :
 #     print badReqdParam % locals();
 #     Usage();
 #   cmd = '%s=Opts["%s"]' % ( key, key );
 #   exec( cmd );
  #-------------------------------------------------------------------
  # Call the AdminControl.invoke() method using the user
  # specified command line parameter values.
  #-------------------------------------------------------------------
  message =  '%(cmdName)s --appName %(appName)s '
  message += '--filePath %(filePath)s '
  
  print message % locals();
  
  try :
  	
  	AdminApp.export('%(appName)s' % locals(),'%(filePath)s' % locals())
  	AdminConfig.save()
  
  except :
    #-----------------------------------------------------------------
    # An exception occurred. Convert the exception value to a string
    # using the backtic operator, then look for the presence of one of
    # the WebSphere message number, which install with 'ADMG'.  If one
    # is found, only display the last part of the value string.
    #-----------------------------------------------------------------
    val = `sys.exc_info()[ 1 ]`;
    pos = val.rfind( 'ADMG' )
    if pos > -1 :
      val = val[ pos: ]
    print '%(cmdName)s Error. %(val)s' % locals();
#---------------------------------------------------------------------
# Name: parseOpts()
# Role: Process the user specified (command line) options
#---------------------------------------------------------------------
def parseOpts( cmdName ) :
  shortForm = 'A:P:';
  longForm  = 'appName=,filePath='.split( ',' );
  badOpt    = '%(cmdName)s: Unknown/unrecognized parameter%(plural)s: %(argStr)s\n';
  optErr    = '%(cmdName)s: Error encountered processing: %(argStr)s\n';

  try :
    opts, args = getopt.getopt( sys.argv, shortForm, longForm );
  except getopt.GetoptError :
    argStr = ' '.join( sys.argv );
    print optErr % locals();
    Usage( cmdName );

  #-------------------------------------------------------------------
  # Initialize the Opts dictionary using the longForm key identifiers
  #-------------------------------------------------------------------
  Opts = {};
  for name in longForm :
    if name[ -1 ] == '=' :
      name = name[ :-1 ]
    Opts[ name ] = None;

  #-------------------------------------------------------------------
  # Process the list of options returned by getopt()
  #-------------------------------------------------------------------
  for opt, val in opts :
    if opt in ( '-A', '--appName' ) : Opts[ 'appName' ] = val
    elif opt in ( '-P', '--filePath'   ) : Opts[ 'filePath'   ] = val

  #-------------------------------------------------------------------
  # Check for unhandled/unrecognized options
  #-------------------------------------------------------------------
  if ( args != [] ) :        # If any unhandled parms exist => error
    argStr = ' '.join( args );
    plural = '';
    if ( len( args ) > 1 ) : plural = 's';
    print badOpt % locals();
    Usage( cmdName );

  #-------------------------------------------------------------------
  # Return a dictionary of the user specified command line options
  #-------------------------------------------------------------------
  return Opts;

#---------------------------------------------------------------------
# Name: listAppServerTemplates()
# Role: Return a list of the available Application Server Template names
#---------------------------------------------------------------------
def listAppServerTemplates() :
  result = [];
  for server in AdminConfig.listTemplates( 'Server' ).splitlines() :
    if server.find( 'APPLICATION_SERVER' ) > -1 :
      result.append( server.split( '(', 1 )[ 0 ] );
  return result;
  
#---------------------------------------------------------------------
# Name: Usage()
# Role: Routine used to provide user with information necessary to
#       use the script.
#---------------------------------------------------------------------
def Usage( cmdName = None ) :
  if not cmdName :
    cmdName = 'backupWasApplication'

  print '''Command: %(cmdName)s Error\n
Purpose: wsadmin script used to backup websphere application 
  Usage: %(cmdName)s [options]\n
Required switches/options:
  -A | --appName <name> = Name of Application  to be used.
  -b | --backupPath<name>=Path of updatefile to be used.
\nNotes:
- Long form option values may be separated/delimited from their associated
  identifier using either a space, or an equal sign ('=').\n
- Short form option values may be sepearated from their associated letter
  using an optional space.\n
- Text containing blanks must be enclosed in double quotes.\n
Examples:
  wsadmin -f %(cmdName)s.py --appName default --filePath path
  wsadmin -f %(cmdName)s.py -default -path''' % locals();
  print '\nAvailable Application Server Template Names:';
  print '  ' + '\n  '.join( listAppServerTemplates() );
  sys.exit();

#---------------------------------------------------------------------
# This is the point at which execution begins
#---------------------------------------------------------------------
if ( __name__ == '__main__') or ( __name__ == 'main' ) :
  backupWasApplication();
else :
  print 'Error: this script must be executed, not imported.\n';
  Usage();
