package com.ideal.ieai.server.timetask.common;


public class TimetaskConstants
{
    public static final int     DEFALUT_POT               = 15000;

    public static final String  IP_SPLIT                  = " ";

    public static final String  IP_SPLIT_ANOTHER          = "\n";

    public static final Integer TT_INS_STATS_UNRUN        = 0;
    public static final Integer TT_INS_STATS_RUN          = 1;
    public static final Integer TT_INS_STATS_END          = 2;

    public static final Integer TT_RUN_STATS_RUN          = 0;
    public static final Integer TT_RUN_STATS_EXCEPTION    = 1;
    public static final Integer TT_RUN_STATS_STOP         = 2;

    public static final Integer TT_STATS_STOP             = 0;// 停止
    public static final Integer TT_STATS_START            = 1;// 运行
    public static final Integer TT_STATS_STARTING         = 2;// 正在启动
    public static final Integer TT_STATS_STOPING          = 3;// 正在停止
    public static final Integer TT_STATS_FAILOVER         = 10;// 失效备援
    public static final Integer TT_STATS_ERRFINISH        = -5;// 异常终止

}
