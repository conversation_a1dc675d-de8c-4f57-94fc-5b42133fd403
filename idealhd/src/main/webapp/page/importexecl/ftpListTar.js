var queryFormPanel = null;
var curFtpFileName = null;
var selectLength = null;
Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	var titleHeight = 35;
	var titleWight = 20;
	
	 Ext.define('ftpGridPanelModel',{
         extend: 'Ext.data.Model',
         fields: [{
            name: 'genFTPFile',
            type: 'String'
         },{
            name: 'curFtpFileName',
            type: 'String'
         }]
      });
	
		/** 系统名下拉框数据Model* */
	Ext.define ('businessSystemModel',
	{
	    extend : 'Ext.data.Model',
	    remoteSort : true,
	    fields : [
	            {
	                name : 'sysName',
	                type : 'string'
	            }
	    ]
	});
	

	/** 业务系统列表数据源* */
	var businessSystemStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    remoteSort : true,
	    model : 'businessSystemModel',
	    pageSize : 10000,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'businessSystemList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList',
	            totalProperty : 'total'
	        }
	    }
	});


	businessSystemStore.on ('beforeload', function (store, options)
	{	
		var new_params =
		{
		    prjType : 3
		};
		Ext.apply (businessSystemStore.proxy.extraParams, new_params);
	});

	var queryForm = Ext.create('Ext.form.Panel', {
	    border: false,
	    width : '100%',
	    height: 65,
	    bodyPadding: 5,
	    layout: 'form',
	    items: [{
	      layout:'column',
	      border : false,
	      items: [
		  {
			xtype : 'combo',
			labelWidth: 100,
			name:'fullName',    
			anchor: '120%',
			fieldLabel: '业务系统名称',
            store: businessSystemStore,
		    queryMode: 'local',
		    displayField: 'sysName',
		    valueField: 'sysName',
			emptyText : '--请选择业务系统--',
			padding : '5 10 0 0' ,
			width : 400,
			listeners:{
				select:function(a,b){
					/*a.up("form").down('textfield[name=isvnpassword]').setValue(b[0].get("isvnpassword"));*/
				}
			}
					
		  },
		  {
	        xtype: 'button',
	        text: '查询',
			cls : 'Common_Btn',
	        handler: function() {
	          queryWhere();
	        }
	      },{
	        xtype: 'button',
	        text: '重置',
			cls : 'Common_Btn',
			hidden : false ,
	        handler: function() {
	          resetWhere();
	        }
	      }]
	    }]
	  });
    
	
	

   queryFormPanel=Ext.create('Ext.Panel', {
	  	hidden:false,
	  	region : 'north',
		witdh:'100%',
		layout:'fit',
		border : false,	
		items: [queryForm]
	});
	
	
	  var ftpGridPanelStore =Ext.create('Ext.data.Store', {
         pageSize: 25,
         autoDestroy: true,
         autoLoad: true,
         model: 'ftpGridPanelModel',
         proxy: {
            type: 'ajax',
            url: 'getFtpFileList.do',
            reader: {
               type: 'json',
               root: 'dataList',
               totalProperty: 'total'
            }
         }
      });
	
	
	ftpGridPanelStore.on ('beforeload', function (store, options)
     {
	   	 var fullName = queryForm.getForm().findField("fullName").getValue();
	        var new_params =
	        {
	        		ifullName : fullName
	        };
	        Ext.apply (ftpGridPanelStore.proxy.extraParams, new_params);
    });
	
	var bsPageBar =  Ext.create ('Ext.PagingToolbar',{
        xtype : 'pagingtoolbar',
        dock : 'bottom',
        store : ftpGridPanelStore,
        displayInfo : true
    });

	var cellEditing = Ext.create ('Ext.grid.plugin.CellEditing',
	{
		clicksToEdit : 1
	});
	
	var ftpPanelSelModel = Ext.create('Ext.selection.CheckboxModel',{
         clicksToEdit: 2,
         checkOnly: false,
         listeners: {
            selectionchange: function (selModel, selections){
               }
         }
      });

	

	
	var ftpGridPanel = Ext.create ('Ext.grid.Panel',
	{
	    region : 'center',
	    store : ftpGridPanelStore,
	    title : '',
		selModel: ftpPanelSelModel,
	    columnLines : true,
	    split : true,
	    multiSelect : true,
	    columns : [{
            xtype: 'rownumberer',
            header: '序号',
            width: 180
         },{
            text: 'ftp根路径',
            dataIndex: 'genFTPFile',
            sortable: true,
            flex: 1,
            hidden: true,
            editor: {
               allowBlank: false
            },
            renderer: function (value, metaData, record, colIndex, store, view)            {
               metaData.tdAttr = 'data-qtip="' + value + '"';
               return value;
            }
         },{
            text: '压缩包名',
            dataIndex: 'curFtpFileName',
            sortable: true,
            flex: 1,
            hidden: false,
            editor: {
               allowBlank: false
            },
            renderer: function (value, metaData, record, colIndex, store, view)            {
               metaData.tdAttr = 'data-qtip="' + value + '"';
               return value;
            }
         }  ],
	    bbar : bsPageBar,
		buttonAlign : 'center',
	    buttons : [
		    {
		        xtype : 'button',
		        text : '确定',
				hidden : false,
		        handler : function ()
		        {
			        var data = ftpGridPanel.getView().getSelectionModel().getSelection();
					if (data.length == 0) {
						Ext.Msg.alert('提示', '请先选择您要操作的行!');
						return;
					}else{
						var allFtpFileName = "";
						for(var i = 0 ; i< data.length ; i++){														
							allFtpFileName += data[i].data.curFtpFileName+",";
						}
						var lastFileName =allFtpFileName.substring(0, allFtpFileName.lastIndexOf(","));															
						parent.upLoadTarPane.getForm().findField("ysbFileName").setValue(lastFileName);
						parent.window_test.close(this);
					}					
		        }
		    },
			{
		        xtype : 'button',
		        text : '关闭',
				hidden : false,
		        handler : function ()
		        {
			        destroyRubbish ();
			        parent.window_test.close(this);
		        }
		    }
	    ],
 		listeners: {
            itemclick: function (dv, record, item, index, e)            {
               // record获得属性 样例;
				/*record.get('genFTPFile')*/
                curFtpFileName = record.get('curFtpFileName');       
            }
         }
	});
	
	
	/*ftpGridPanel.getSelectionModel ().on ('selectionchange', function (selModel, selections)
	{
		if (selections.length === 0)
		{
			selectLength = 0;
		}
	});*/
	
	var ftpPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : 'ftplist_div',
	    height : contentPanel.getHeight () - 160,
	    width : '100%',
	    layout : 'border',
	    bodyPadding : 5,
	    title : '',
	    items : [
		    queryFormPanel,ftpGridPanel
	    ]
	});
	
	
	finalQueryPanelObj=queryFormPanel;
    finalTitlePanelObj=contentPanel;
    finalTitle=contentPanel.title;
   //查询条件面板伸缩时，调整grid面板自适应
    queryFormPanel.on ('hide', function (){
   	 autoResize(ftpGridPanel,queryFormPanel,ftpPanel,contentPanel);
    });
    queryFormPanel.on ('show', function (){
   	 autoResize(ftpGridPanel,queryFormPanel,ftpPanel,contentPanel);
    });
       
	contentPanel.on ('resize', function ()
	{
		//autoResize(templateGrid,queryFormPanel,ftpPanel,contentPanel);
		ftpPanel.setHeight (contentPanel.getHeight () - titleHeight);
		ftpPanel.setWidth ('100%');
	});
	
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (ftpPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	function setMessage (msg)
	{
		Ext.Msg.alert ('提示', msg);
	}
	
	function queryWhere(){
		
		bsPageBar.moveFirst();
	};
	
	function resetWhere(){
		
		queryForm.getForm().findField("fullName").setValue('');
	};
	
	
	/* 解决IE下trim问题 */
	String.prototype.trim = function ()
	{
		return this.replace (/(^\s*)|(\s*$)/g, "");
	};
	
	/**
	 * 校验特殊字符
	 * 
	 * @param str
	 * @returns {Boolean}
	 */
	function illegalChar_SysShortName(str) {
		var pattern = /[`~!@#\$%\^\&\*\(\)\<>\?:"\{\},\.\\\/;'\[\]]/im;
		if (pattern.test(str)) {
			return true;
		}
		return false;
	}
	
});
