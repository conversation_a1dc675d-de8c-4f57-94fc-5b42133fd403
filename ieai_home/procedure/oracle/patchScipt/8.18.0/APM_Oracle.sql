
-- 4.7.17
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=16;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (16, ''应用维护'', ''应用维护模块组'', 16, ''images/info82.png'')';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF; 	
	     
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-16;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVE<PERSON>, <PERSON>REEZED, <PERSON>UPLOADUSER, ICOMMENT, IUPLOADNU<PERSON>, IUUID, IFREEZEUSER, IF<PERSON>EZEUSERID, I<PERSON>LOADUSERID, IGRO<PERSON>ID, PROTYPE, IUPPERID, ILATESTID) VALUES (-16, 0, ''所有应用维护业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 16, -16, -16, -16)';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF; 		
	     
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=16;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (16,16,''应用维护源'','''','''','''','''',0)';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF; 
	     
	    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='IAPMTYPE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD IAPMTYPE INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_APM_CODE' AND OBJECT_TYPE = 'TABLE';
		IF	LI_EXISTS = 0 THEN
		    LS_SQL := '	CREATE TABLE IEAI_APM_CODE(IID   NUMBER(19,0) NOT NULL, IAPMTYPE INTEGER, IAPMNAME VARCHAR2(255), CONSTRAINT PK_IEAI_APM_CODE  PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
		SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_APM_CODE WHERE IID=1;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_APM_CODE(IID,IAPMTYPE,IAPMNAME) VALUES(1,0,''应用启停'')';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF;
	     
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_APM_CODE WHERE IID=2;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_APM_CODE(IID,IAPMTYPE,IAPMNAME) VALUES(2,1,''数据获取'')';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF;
	     
	    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IAPMTYPE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE ADD IAPMTYPE INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='ISYSTYPE';
		IF	LI_EXISTS != 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE MODIFY  ISYSTYPE NUMBER(2,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IAPMTYPE';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS ADD IAPMTYPE INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='ISYSTYPE';
		IF	LI_EXISTS != 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUN_INSTANCE_HIS MODIFY  ISYSTYPE NUMBER(2,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/

-- 4.7.18
DECLARE
	LI_SIGN SMALLINT;
	LS_SQL  VARCHAR2(2000);
BEGIN
	SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAISUSITSMTASKIDREL_01' AND OBJECT_TYPE = 'INDEX';
	IF	LI_SIGN = 0 THEN
		LS_SQL := 'CREATE INDEX IDX_IEAISUSITSMTASKIDREL_01 ON IEAI_SUS_ITSM_TASKID_REL (IITSM_TASKID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
END;					
/


-- 4.7.23
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	    
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION' AND COLUMN_NAME='ISSELFHEALING';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION ADD ISSELFHEALING INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_INSTANCE_VERSION_HIS' AND COLUMN_NAME='ISSELFHEALING';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD ISSELFHEALING INTEGER DEFAULT 0';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ISELFHEALING';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD ISELFHEALING INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ISELFHEALING';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD ISELFHEALING INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS_DATA' AND COLUMN_NAME='ISELFHEALING';
		IF	LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD ISELFHEALING INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_RUN_INSTANCE' AND COLUMN_NAME='ISELFHEALING';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE TMP_RUN_INSTANCE ADD ISELFHEALING INTEGER DEFAULT 0';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLUMN_NAME='ISELFHEALING';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO ADD ISELFHEALING INTEGER DEFAULT 0';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
END;
/

-- 4.7.24
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM' AND COLUMN_NAME='CPFLAG';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD CPFLAG INTEGER DEFAULT 0';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLUMN_NAME='CPFLAG';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD CPFLAG INTEGER DEFAULT 0';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
END;
/


-- 4.7.25
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO_CHILD' AND COLUMN_NAME='IERROR';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO_CHILD ADD IERROR VARCHAR2(800) ';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSM_AUTO' AND COLUMN_NAME='ORDERNUM';
		IF	LI_EXISTS = 0 THEN
         	LS_SQL := 'ALTER TABLE IEAI_ITSM_AUTO ADD ORDERNUM VARCHAR2(2000) ';
          	EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_APP_STARTUP_INFO' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE  IEAI_APP_STARTUP_INFO ( IID NUMBER(19) NOT NULL, IAPMTYPE NUMBER(19), IINSTANCENAME VARCHAR2(255), IVERSIONALIAS VARCHAR2(255), IRUNINSNAME VARCHAR2(255), CONSTRAINT PK_IEAI_APP_STARTUP_INFO PRIMARY KEY (IID) )';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/

