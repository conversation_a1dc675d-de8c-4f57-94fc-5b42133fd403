/**事件基础信息展示窗口**/
//operType:0 增加       1：编辑  
//state: 状态   401 草稿    402 待处置     403 处置中    404 处置打回        405 执行中   407 完成      406 审核打回
//新增  operType:0  eventid:0  state:401
function viewEventWinShow(operType,eventid,state){
	var labelEdit;
	var addEventWin;
	/*状态*/
	var addEventComboStore = new Ext.data.ArrayStore({
	    fields: ['id', 'name'],
	    data: [[401, '草稿'], [402, '待处置'], [403, '处置中'], [404, '处置打回'], [405,'执行中'], [406,'审核打回'], [407,'完成']]
	});
	/*状态下拉框下拉框 */
	var addEventCombState =	Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'add_stateName',
		fieldLabel: '事件单状态',
		anchor : '90%',
		labelWidth : 80,
		padding : '5 5 5 5',
	    store: addEventComboStore,
	    displayField: 'name',
	    valueField: 'id',
	    triggerAction: 'all',
	    emptyText: '请选择...',
	    allowBlank: true,
	    mode: 'local',
	    labelAlign : 'right',
	    value : 401,
	    readOnly:true
	    
	});
	/**事件等级*/
	//var addEventLevelStore = new Ext.data.ArrayStore({
	//    fields: ['id', 'name'],
	//    data: [[1, '一等'], [2, '二等'], [3, '三等'], [4, '四等']]
	//});
	/*处置人*/
	Ext.define('eventLevelModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iparamname',
			type : 'string'
		},{
			name : 'iparamvalue',
			type : 'string'
		},{
			name : 'istate',
			type : 'string'
		},{
			name : 'iupdatetime',
			type : 'string'
		},{
			name : 'iupdateuserid',
			type : 'string'
		}]
	});
	
	var addEventLevelStore = Ext.create('Ext.data.Store', {
		autoDestroy : true,
		autoLoad : true,
		model : 'eventLevelModel',
		pageSize : 30,
		proxy : {
			type : 'ajax',
			url : 'event/getEventLevelList.do'
		}
	});
	/*事件等级下拉框 */
	var addEventLevelCom =	Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'add_eventLevelName',
		fieldLabel: '事件等级',
		anchor : '90%',
		labelWidth : 80,
		padding : '5 5 5 5',
	    store: addEventLevelStore,
	    displayField: 'iparamname',
	    valueField: 'iparamvalue',
	    triggerAction: 'all',
	    emptyText: '请选择事件等级',
	    allowBlank: true,
	    mode: 'local',
	    labelAlign : 'right',
	    value : '',
	    readOnly:true
	    
	});
	
	
	//事件标签   
	var la = new Ext.util.HashMap();
	var label = Ext.create('Ext.panel.Panel', {
		// region: 'south',
		border: false,
		labelWidth : 80,
		labelAlign : 'right',
		width:'80%',
		padding : '-5 5 5 5',
		allowBlank : false,
		maxLength : 100,
		html: '<div class="report_box">' +
			'<div class="tagsinput-primary" id="signDiv">' +
			'<label class="s_tit" ><span>标签:</span></label>' +
			'<input name="tagsinput" id="tagsinputval" class="tagsinput" data-role="tagsinput" value=""   >' +
			'</div>' +
			'</div>'
	});
	label.on("afterrender", function () {
		//console.log($("#tagsinputval"),$("#tagsinputval").siblings('.bootstrap-tagsinput'));
		if ($("#tagsinputval").siblings('.bootstrap-tagsinput').length > 0) {
			$("#tagsinputval").siblings('.bootstrap-tagsinput').remove();
			$('#tagsinputval').remove();
	
		}
		if ($.fn.tagsinput) {
	
			$("#tagsinputval").tagsinput();
		}
	
		function setLabel() {
			if (labelEdit != null && labelEdit != '') {
				var labs = labelEdit.split(",")
				for (var i = 0; i < labs.length; i++) {
					la.add(labs[i], i);
				}
				addTags(la)
			}
		}
		setLabel();
		
		$("#tagsinputval").attr('disabled',"disabled");
	}, this);
	
	/*处置人*/
	Ext.define('userModel', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'iid',
			type : 'int'
		},{
			name : 'iusername',
			type : 'string'
		},{
			name : 'idepartment',
			type : 'string'
		},{
			name : 'itelephone',
			type : 'string'
		},{
			name : 'iemail',
			type : 'string'
		}]
	});
	
	var handleUserIdComboStore = Ext.create('Ext.data.Store', {
		autoDestroy : true,
		autoLoad : true,
		model : 'userModel',
		pageSize : 30,
		proxy : {
			type : 'ajax',
			url : 'event/getManaUserInfoList.do'
		}
	});
	
	/*处置人下拉框 */
	var handleUserIdComb=	Ext.create('Ext.ux.ideal.form.ComboBox', {
		name : 'add_handleUser',
		fieldLabel: '处置人',
		anchor : '90%',
		labelWidth : 80,
		padding : '5 5 5 5',
	    store: handleUserIdComboStore,
	    displayField : 'iusername',
		valueField : 'iid',
	    triggerAction: 'all',
	    emptyText: '请选择...',
	    allowBlank: true,
	    mode: 'local',
	    labelAlign : 'right',
	    value : "",
	    readOnly:true
	    
	});
	//事件分类树
	var eventTypeTreeStore = Ext.create('Ext.data.TreeStore', {
		fields : [ 'id', 'name' ],
		root : {
			expanded : true
		},
		proxy : {
			type : 'ajax',
			url : 'emergencyevent/getEventTypeManagementList.do'
		}
	});
	
	var addEventTypeTree = Ext.create('Ext.ux.TreePicker',{
		fieldLabel : '事件分类',
		emptyText : '请选择事件分类',
		labelWidth : 80,
		labelAlign : 'right',
		anchor : '90%',
		padding : '5 5 5 5',
		minPickerHeight : 10,
		name : 'add_eventTypeTree',
		displayField : 'name',
		valueField : 'id',
		editable : false,
		forceSelection : true,
		store : eventTypeTreeStore,
		value : ieventtypeid,
		readOnly:  true
	});
	var addEventForm = Ext.create('Ext.form.FormPanel',{
		border : false,
		items: [
		    addEventCombState,
			{
				xtype : 'textfield',
				fieldLabel : '事件单号',
				labelWidth : 80,
				labelAlign : 'right',
				name : 'add_ieventNum',
				anchor : '90%',
				padding : '5 5 5 5',
				allowBlank : false,
				maxLength : 100,
				value : getieventNum(),
				readOnly : (state == 401 || state == 404 ) ? false : true  //如果是草稿、打回状态可以编辑，其它状态不可以编辑
			},{
				xtype : 'textfield',
				fieldLabel : '事件名称',
				labelWidth : 80,
				labelAlign : 'right',
				name : 'add_ieventName',
				anchor : '90%',
				padding : '5 5 5 5',
				allowBlank : false,
				maxLength : 100,
				value :"",
				readOnly :  true
			},
			addEventLevelCom,
			addEventTypeTree,
			{
				xtype : 'textarea',
				fieldLabel : "事件描述",
				labelWidth : 80,
				labelAlign : 'right',
				name : 'add_ieventDesc',
				padding : '0 5 0 5',
				allowBlank : false,
				height : 100,
				maxLength : 100,
				anchor : '90%',
				value :"",
				readOnly:true
			},
			label,
			handleUserIdComb
		],
		buttonAlign: 'center',
		buttons : [{  
		      text : '确定', 
		      handler : function() {  
		    	  addEventWin.close(this);  
		      }  
		   },{  
		      text : '取消', 
		      handler : function() {  
		    	  addEventWin.close(this);  
		      }  
		   }]
	});
	//添加事件
	addEventWin = new Ext.Window({
		title : '查看事件基础信息',
		closeAction : 'destroy',
		autoScroll : true,
		autoDestroy : true,
		closeable : true,
		modal : true,
		width : '35.5%',
		//height : '68%',
		height : 640,
		resizable : false,
		plain : true,
		layout : 'form',
		draggable : true,
		items : [ addEventForm ]
	});
	addEventWin.show();
	
	
	//如果是修改操作
	if(operType==1){
		
        Ext.Ajax.request({
            url: 'event/getEventInfo.do',
            method: 'post',
            params: {
                iid: eventid
            },
            success: function (response) {
                var result = Ext.decode(response.responseText);
                var form = addEventForm.getForm();
                addEventCombState.setValue(result.istate);
                form.findField("add_ieventNum").setValue(result.ieventNum);
                form.findField("add_ieventName").setValue(result.ieventName);
                addEventTypeTree.setValue(result.ieventtypeid);
                addEventLevelCom.setValue(result.ieventLevel);
                form.findField("add_ieventDesc").setValue(result.ieventDesc);
                $("#tagsinputval").val(result.ieventTag);
                var labs = result.ieventTag.split(",")
				for (var i = 0; i < labs.length; i++) {
					la.add(labs[i], i);
				}
				addTags(la);
                handleUserIdComb.setValue(result.handleUserId);
            },
            failure: function () {
                Ext.Msg.alert('提示', '事件基本信息回显失败');
            }
        })
	}
	
}


//事件单号默认时间戳
function getieventNum() {
	return new Date().getTime();
}

	
