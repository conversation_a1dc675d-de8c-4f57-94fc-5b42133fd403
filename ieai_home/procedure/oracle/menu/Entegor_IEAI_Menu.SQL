INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (1,'作业调度','作业调度模块组',1,'images/info86.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1001, '活动监控', 1, 'actMonitorIndex.do', 1, '','images/info20.png');      
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1002, '工作流启动', 1, 'forwardStartFlow.do', 2, '','images/info37.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1003, '工作流查询', 1, 'forwardFlowQuery.do', 3, '','images/info23.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1500, '配置', '1', '', 300, '作业调度', 'images/info39.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1027, '作业依赖关系上传', 1, 'taskUpload.do', 304, '配置', 'images/info129.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1028, '作业依赖关系下载', 1, 'taskDownload.do', 305, '配置', 'images/info130.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1032, '主线定时配置列表上传', 1, 'timeConfigurationUpload.do', 306, '配置', 'images/info131.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1033, '主线定时配置列表导出', 1, 'timeConfigurationDwon.do', 307, '配置', 'images/info132.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1082, '关键节点配置', 1, 'configInfoView.do', 102, '配置', 'images/info88.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1007, '批量监控查询', 1, 'queryMonitorBatch.do', 103, '配置', 'images/info115.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1008, '查询报警信息', 1, 'queryAlarmInfoPage.do', 104, '配置', 'images/info116.png');


INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1001, '启动', 'startFlow/startFlow.do', '作业调度--工作流启动--启动');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1002, '继续', 'flowQuery/resumeFlow.do', '作业调度--工作流查询--继续');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1003, '强行终止', 'flowQuery/killFlow.do', '作业调度--工作流查询--强行终止');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1004, '暂停', 'flowQuery/pauseFlow.do', '作业调度--工作流查询--暂停');
--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1024, '异常操作', 'jobSchedulingRepeat.do', '作业调度-活动监控-shellcmd异常(重试|略过|继续执行|终止工作流)');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1025, '执行', 'utRecordOptionForJob.do', '作业调度-活动监控-UT执行操作');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1014, '保存启动时间标准|结束时间标准', 'configInfoView/saveAlarmInfo.do', '作业调度-关键节点配置-启动时间标准|结束时间标准-保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1015, '保存运行时间标准', 'configInfoView/saveRunTime.do', '作业调度-关键节点配置-运行时间标准-保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1017, '恢复', 'configInfoView/recoveryAlarmInfo.do', '作业调度-批量监控查询-恢复');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1018, '删除', 'configInfoView/deleteAlarmInfo.do', '作业调度-批量监控查询-删除');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1019, '处理', 'saveNodeWarnInfo.do', '作业调度-查询报警信息-处理');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1120, '执行','utRecordOptionForOdsJob.do', '作业调度-活动监控-UT执行操作');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1124, 'tn5250执行', 'getTn5250OutPutRunning_ieai.do', '作业调度-活动监控-tn5250执行');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1127, 'tn5250Kill-Job', 'getTn5250OutPutRunning_ieai.do', '作业调度-活动监控-tn5250-Kill-Job');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1128, 'tn5250确认输入', 'getTn5250OutPutRunning_ieai.do', '作业调度-活动监控-tn5250-确认输入');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1129, 'tn5250重新执行', 'getTn5250OutPutRunning_ieai.do', '作业调度-活动监控-tn5250-重新执行');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1130, 'tn5250跳过执行', 'getTn5250OutPutRunning_ieai.do', '作业调度-活动监控-tn5250-跳过执行');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1131, 'tn5250取消', 'getTn5250OutPutRunning_ieai.do', '作业调度-活动监控-tn5250-取消');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1001, 1002, 1001, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1002, 1003, 1002, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1003, 1003, 1003, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1004, 1003, 1004, '');
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1005, 1005, 1005, '');
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1024, 1001, 1024, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1025, 1001, 1025, '');
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1026, 1005, 1026, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1014, 1006, 1014, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1015, 1006, 1015, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1017, 1007, 1017, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1018, 1007, 1018, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1019, 1008, 1019, '');
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1020, 1010, 1020, '');
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1021, 1010, 1021, '');
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1119, 1074, 1119, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1120, 1074, 1120, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1124, 1001, 1124, '');