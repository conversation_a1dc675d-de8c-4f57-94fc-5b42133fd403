-- 4.7.17 version <PERSON><PERSON>t patch is as follows 
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PLATFORM_CODE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_PLATFORM_CODE( IID NUMBER(19)  , INAME VARCHAR2(30), ICODEVALUE VARCHAR2(30), CONSTRAINT PK_IEAI_PLATFORM_CODE  PRIMARY KEY(IID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;

	
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	 
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=1;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(1,''全部'',''全部'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF; 
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=2;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(2,''HP-UX'',''HP-UX'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=3;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(3,''Unix'',''Unix'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=4;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(4,''Windows'',''Windows'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=5;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(5,''SUSE'',''SUSE'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=6;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(6,''RedHat'',''RedHat'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=7;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(7,''CentOS'',''CentOS'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=8;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(8,''Linux'',''Linux'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PLATFORM_CODE WHERE IID=9;
  	IF  LI_EXISTS = 0 THEN
      LS_SQL :='INSERT INTO IEAI_PLATFORM_CODE(IID,INAME,ICODEVALUE) values(9,''OracleLinux'',''OracleLinux'')';
    EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	COMMIT;
END;
/

-- 4.7.18 version SCRIPT patch is as follows
	DECLARE
		 LS_SQL VARCHAR2(4000);
		 LI_EXISTS SMALLINT;
	BEGIN 
	
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_CONFIG_SWITCH WHERE IID=2;
	     IF LI_EXISTS = 0 THEN
		      LS_SQL := 'INSERT INTO IEAI_CONFIG_SWITCH (IID, INAME, IDES, ISTATE, ITYPE)VALUES (2, ''ISRELEASETOPRODUCT'', NULL, 1, 9)';
			  EXECUTE IMMEDIATE LS_SQL;
			  COMMIT;
	    END IF; 
	     
	     
	   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_LOGANALIZE' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		     LS_SQL := 'CREATE TABLE IEAI_SCRIPT_LOGANALIZE  ( IID  NUMBER(19) NOT NULL,IOPERID    	NUMBER(19) NULL,IKEY       	VARCHAR2(200) NULL,IVALUE     	VARCHAR2(200) NULL,IFLOWID    	NUMBER(19) NULL,ICOATID    	NUMBER(19) NULL,IINSTANCEID	NUMBER(19) NULL,ISCRIPTID  	NUMBER(19) NULL,IAGENTID   	NUMBER(19) NULL,CONSTRAINT IEAI_SCRIPT_LOGANALIZE PRIMARY KEY(IID))';
		     EXECUTE IMMEDIATE LS_SQL;
	   END IF;

	   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_AG_BS' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		     LS_SQL := 'CREATE TABLE IEAI_SCRIPT_AG_BS(IID NUMBER(15) NOT NULL,BSNAME     VARCHAR2(255), IISEXTEND  INTEGER,ISHOWORDER INTEGER,CONSTRAINT  PK_AG_BS PRIMARY KEY(IID))';
		     EXECUTE IMMEDIATE LS_SQL;
	   END IF;
		
	   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_AG_BSTYPE' AND OBJECT_TYPE = 'TABLE';
	   IF LI_EXISTS = 0 THEN
		     LS_SQL := 'CREATE TABLE IEAI_SCRIPT_AG_BSTYPE(  IID      NUMBER(14) NOT NULL,  TYPENAME VARCHAR2(255),  BSID     NUMBER(14),  CONSTRAINT  PK_AG_BSTYPE PRIMARY KEY(IID))';
		     EXECUTE IMMEDIATE LS_SQL;
	   END IF;
	     
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	     
	     SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IEXECUSER';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  IEXECUSER VARCHAR2(255)';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='IEXECUSER';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD  IEXECUSER VARCHAR2(255)';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
			
			  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='IEXECUSER';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD  IEXECUSER VARCHAR2(255)';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;	
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AGENT_GROUP' AND COLUMN_NAME='BSID';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP ADD   BSID NUMBER(15)';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;	
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AGENT_GROUP' AND COLUMN_NAME='BSTYPEID';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP ADD   BSTYPEID NUMBER(15)';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;	
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	     
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_ROOTCHANGE_WINDOWS_VIEW' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_ROOTCHANGE_WINDOWS_VIEW(CHG_WINDOWNAME VARCHAR2(255),CHG_WINDOWTYPE VARCHAR2(255),CHG_START_DATE VARCHAR2(255),CHG_END_DATE   VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SUS_CONFIG_PARAM' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_SUS_CONFIG_PARAM(IID          NUMBER(19) not null,IPARAM_NAME  VARCHAR2(255),IPARAM_VALUE VARCHAR2(255),IDESC        VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'TMP_IEAI_RUN_SEQUENTIAL_INFO' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create global temporary table TMP_IEAI_RUN_SEQUENTIAL_INFO(IID                NUMBER(19),IMAINID            NUMBER(19),IORGANIZATION      VARCHAR2(255),IAPPLICATIONNAME   VARCHAR2(255),IDETAILS           VARCHAR2(2000),IOUTOFBUSINESS     INTEGER,ISUCCESSIVELY      INTEGER,IPRENERREMARKS     VARCHAR2(255),ITRIGGERREMARKS    VARCHAR2(255),IDEVELOPERNAMETEL  VARCHAR2(255),ITCSJOPINION       INTEGER,ITYSJOPINION       INTEGER,IOPERATOR          NUMBER(19),IREMARKS           VARCHAR2(255),ISTATE             INTEGER default 0,IMODIFYTIME_LAST   NUMBER(19),IMODIFYUSER_LAST   NUMBER(19),ITYSHD             VARCHAR2(255),ITYSHD2            VARCHAR2(255),IJYTCSH            VARCHAR2(255),IJYTCSH2           VARCHAR2(255),IJHKSTIME          NUMBER(19),IJHJSTIME          NUMBER(19),ITIME1             NUMBER(19),ITIME2             NUMBER(19),ITIME3             NUMBER(19),ITIMEOUT           INTEGER default 0,IDELAY             INTEGER default 0,ITYPE              INTEGER default 1,IGROUP             VARCHAR2(255),ISUSFENLEI         INTEGER,CHG_CCHR_RELEASEID VARCHAR2(255),CMDBCLOSENAME      VARCHAR2(255),IEXECUTIVE         NUMBER(19),IAUTOCREATE        INTEGER default 0,ITCWCQK            INTEGER)on commit delete rows';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUN_SEQUENTIAL_INFO' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_RUN_SEQUENTIAL_INFO(IID                   NUMBER(19) not null,IMAINID               NUMBER(19),IORGANIZATION         VARCHAR2(255),IAPPLICATIONNAME      VARCHAR2(255),IDETAILS              VARCHAR2(4000),IOUTOFBUSINESS        INTEGER,ISUCCESSIVELY         INTEGER,IPRENERREMARKS        VARCHAR2(255),ITRIGGERREMARKS       VARCHAR2(255),IDEVELOPERNAMETEL     VARCHAR2(255),ITCSJOPINION          INTEGER,ITYSJOPINION          INTEGER,IOPERATOR             NUMBER(19),IREMARKS              VARCHAR2(255),ISTATE                INTEGER default 0,IMODIFYTIME_LAST      NUMBER(19),IMODIFYUSER_LAST      NUMBER(19),ITYSHD                VARCHAR2(255),ITYSHD2               VARCHAR2(255),IJYTCSH               VARCHAR2(255),IJYTCSH2              VARCHAR2(255),IJHKSTIME             NUMBER(19),IJHJSTIME             NUMBER(19),ITIME1                NUMBER(19),ITIME2                NUMBER(19),ITIME3                NUMBER(19),ITIMEOUT              INTEGER default 0,IDELAY                INTEGER default 0,ITYPE                 INTEGER default 1,IGROUP                VARCHAR2(255),ISUSFENLEI            INTEGER,CHG_CCHR_RELEASEID    VARCHAR2(255),CMDBCLOSENAME         VARCHAR2(255),ITCWCQK               INTEGER,IAUTOCREATE           INTEGER default 0,IEXECUTIVE            NUMBER(19),IAPPLICATIONNAME_STEP VARCHAR2(255),CREATEUSERID          NUMBER(19),TINGYEYINGXIANG       VARCHAR2(255),TINGYEYINGXIANG_YW    VARCHAR2(255),SUGGESTTIME_BEGINYW   NUMBER(19),SUGGESTTIME_ENDYW     NUMBER(19),ICONFIRMED            INTEGER default 0)';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUN_SEQUENTIAL_INFO_C' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_RUN_SEQUENTIAL_INFO_C(IID                      NUMBER(19) not null,INFOID                   NUMBER(19),CHG_DETAILED_DESCRIPTION CLOB)';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUN_SEQUENTIAL_VERSION' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_RUN_SEQUENTIAL_VERSION(IID          NUMBER(19) not null,ISEQFLOWNAME VARCHAR2(255),IUPLOADTIME  NUMBER(19),IUPLOADUSER  NUMBER(19),IMODIFYTIME  NUMBER(19),IMODIFYUSER  NUMBER(19),ISTATE       INTEGER default 1,INSTANCENAME VARCHAR2(255),ISTARTTIME   NUMBER(19),ISTARTUSER   NUMBER(19),ISERVERIP    VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUN_SEQUENTIAL_INFO_RELY' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_RUN_SEQUENTIAL_INFO_RELY(IID            NUMBER(19) not null,IMAINID        NUMBER(19),IINFOID        NUMBER(19),ISELFNAME      VARCHAR2(255),IRELYNAME      VARCHAR2(255),IDESC          VARCHAR2(255),ISELFNAME_STEP VARCHAR2(255),IRELYNAME_STEP VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SEQUENTIAL_ABOUT' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_SEQUENTIAL_ABOUT(IID                   NUMBER(19) not null,ISEQFLOWNAME          VARCHAR2(255),IAPPLICATIONNAME      VARCHAR2(255),CHG_CCHR_RELEASEID    VARCHAR2(255),IAPPLICATIONNAME_STEP VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUN_SEQUENTIAL_INFO_TRI' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'create table IEAI_RUN_SEQUENTIAL_INFO_TRI(IID               NUMBER(19) not null,IMAINID           NUMBER(19),IINFOID           NUMBER(19),ISELFNAME         VARCHAR2(255),ITRIGGERNAME      VARCHAR2(255),IDESC             VARCHAR2(255),ISELFNAME_STEP    VARCHAR2(255),ITRIGGERNAME_STEP VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='log.clearTime';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES (3, 'log.clearTime', '02:00', '日志清理时间');
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='seqSyncSwitch_Thread';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'seqSyncSwitch_Thread', 'true', '时序管理同步工单线程开关'); 
		COMMIT;
	END	IF;
	
		SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='seqSyncSwitch_Frequency';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'seqSyncSwitch_Frequency', '1', '时序管理同步工单线程频率(分钟)'); 
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_RUN_SEQUENTIAL_VERSION  P WHERE P.IID=-999999999;  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_RUN_SEQUENTIAL_VERSION  (IID,   ISEQFLOWNAME,   IUPLOADTIME,   IUPLOADUSER,   IMODIFYTIME,   IMODIFYUSER,   ISTATE,   INSTANCENAME,   ISTARTTIME,   ISTARTUSER,   ISERVERIP)VALUES  (-999999999,   '非变更窗口工单',   1529028263468,   4,   NULL,   NULL,   1,   '非变更窗口工单实例',   1529028448535,   4,   NULL);
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='cpTimeBegin';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'cpTimeBegin', '08:00',  '变更时段开始时间'); 
		COMMIT;
	END	IF;
	
	SELECT COUNT(IID) INTO LI_EXISTS  FROM IEAI_SUS_CONFIG_PARAM  P WHERE P.IPARAM_NAME='cpTimeEnd';  
	IF	LI_EXISTS = 0 THEN
		INSERT INTO IEAI_SUS_CONFIG_PARAM(IID, IPARAM_NAME, IPARAM_VALUE, IDESC) VALUES ((SELECT MAX (IID)+1 FROM IEAI_SUS_CONFIG_PARAM), 'cpTimeEnd', '18:00', '变更时段结束时间');
		COMMIT;
	END	IF;
	
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ISSUERECORD' AND COLUMN_NAME='ISENDPATH';
	IF	LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD  ISENDPATH VARCHAR2(255)'; 
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN

	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_USER' AND COLUMN_NAME = 'ORG_CENTER';
	IF  LI_EXISTS = 0  THEN
		LS_SQL := 'ALTER TABLE IEAI_USER ADD  (ORG_CENTER VARCHAR2(255)) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SUS_BUSNES_SYS' AND COLUMN_NAME = 'DEPARTMENTID';
  	IF  LI_EXISTS = 0  THEN
    LS_SQL := 'ALTER TABLE IEAI_SUS_BUSNES_SYS ADD  (DEPARTMENTID NUMBER(19)) ';
    EXECUTE IMMEDIATE LS_SQL;
  END IF;
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RUN_SEQUENTIAL_OPERATION' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_RUN_SEQUENTIAL_OPERATION(IID  NUMBER(19) PRIMARY KEY,ISTARTTIME  VARCHAR2(255),IENDTIME          VARCHAR2(4000),CHG_CCHR_RELEASEID    VARCHAR2(255))';
    	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
END;
/
DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
    SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_DOUBLECHECK_WORKITEM' AND COLUMN_NAME = 'IMODELTYPE';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD  (IMODELTYPE INTEGER DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLUMN_NAME = 'IMODELTYPE';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD  (IMODELTYPE INTEGER DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_INSTANCE' AND COLUMN_NAME = 'ISERVICEGID';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE ADD  (ISERVICEGID DECIMAL(19,0) DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_USER_GROUP_RELATION' AND COLUMN_NAME = 'IROLE';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_USER_GROUP_RELATION ADD  (IROLE INTEGER DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;

  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SERVICE_VERRES' AND COLUMN_NAME = 'IFROM';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SERVICE_VERRES ADD  (IFROM INTEGER DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_INSTANCE_TEST' AND COLUMN_NAME = 'ISERVICEGID';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE_TEST ADD  (ISERVICEGID DECIMAL(19,0) DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPTS_COAT' AND COLUMN_NAME = 'ISERVICEGID';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPTS_COAT ADD (ISERVICEGID DECIMAL(19,0) DEFAULT 0,IDBSTATE INTEGER DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_FLOW' AND COLUMN_NAME = 'ISERVICEGID';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_FLOW ADD  (ISERVICEGID  DECIMAL(19,0) DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_OF_MY' AND COLUMN_NAME = 'ISERVICEGID';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD  (IEXECTIMENEXT VARCHAR(255),ISERVICEGID DECIMAL(19,0) DEFAULT 0,ISERVICEID DECIMAL(19,0) DEFAULT 0,IDELETED DECIMAL(1,0),IANALYZEFUNTYPE varchar(255))';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;

  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SERVICESUB_USER' AND COLUMN_NAME = 'IDEL';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER ADD  (IDEL  INTEGER DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_USER_GROUP' AND COLUMN_NAME = 'IUSERID';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_USER_GROUP ADD  (IUSERID DECIMAL(19,0) DEFAULT 0)';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;  	
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_VERSION_MANAGER' AND COLUMN_NAME = 'IBASELINEPATH';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_VERSION_MANAGER ADD  (IBASELINEPATH VARCHAR2(255))';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;  	
  	
  	SELECT SIGN(COUNT(*))  INTO LI_EXISTS  FROM OBJ  WHERE OBJECT_NAME = 'IEAI_SCRIPT_COLLECT_RESULT_COL' AND OBJECT_TYPE = 'TABLE';
   IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_SCRIPT_COLLECT_RESULT_COL(IID NUMERIC(19) NOT NULL,ITEMPLATEID NUMERIC(19), ICOLNAME VARCHAR(255),ICOL INTEGER,ICLLECTTIME TIMESTAMP(6),CONSTRAINT PK_IEAI_S_C_RESULT_COL PRIMARY KEY (IID))';
    EXECUTE IMMEDIATE LS_SQL;
   END IF;
 		SELECT SIGN(COUNT(*))  INTO LI_EXISTS  FROM OBJ   WHERE OBJECT_NAME = 'IEAI_SCRIPT_COLLECT_RESULT' AND OBJECT_TYPE = 'TABLE';
   IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_SCRIPT_COLLECT_RESULT(IID NUMERIC(19) NOT NULL,ITEMPLATEID NUMERIC(19),I1 VARCHAR(255), I2 VARCHAR(255), I3 VARCHAR(255), I4 VARCHAR(255), I5 VARCHAR(255), I6 VARCHAR(255), I7 VARCHAR(255), I8 VARCHAR(255), I9 VARCHAR(255), I10 VARCHAR(255), I11 VARCHAR(255), I12 VARCHAR(255), I13 VARCHAR(255), I14 VARCHAR(255), I15 VARCHAR(255), I16 VARCHAR(255), I17 VARCHAR(255), I18 VARCHAR(255), I19 VARCHAR(255), I20 VARCHAR(255), I21 VARCHAR(255), I22 VARCHAR(255), I23 VARCHAR(255), I24 VARCHAR(255), I25 VARCHAR(255), I26 VARCHAR(255), I27 VARCHAR(255), I28 VARCHAR(255), I29 VARCHAR(255), I30 VARCHAR(255), ICLLECTTIME TIMESTAMP(6),CONSTRAINT PK_IEAI_S_C_RESULT PRIMARY KEY (IID))';
    EXECUTE IMMEDIATE LS_SQL;
   END IF;
  	SELECT SIGN(COUNT(*))  INTO LI_EXISTS FROM OBJ  WHERE OBJECT_NAME = 'IEAI_SCRIPT_ANALYSE_RESULT' AND OBJECT_TYPE = 'TABLE';
     IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ANALYSE_RESULT (IID NUMERIC (19) NOT NULL,SCRIPT_SERVICES_IID NUMERIC (19),WORK_ITEM_ID NUMERIC (19),PROCEDURE_NAME VARCHAR2(255),ANALYSE_RESULT VARCHAR2 (4000),CREATE_DATE NUMBER (19),CONSTRAINT PK_IEAI_SCRIPT_ANALYSE_RESULT PRIMARY KEY (IID))';
      EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/
-- 4.7.19 version SCRIPT patch is as follows

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
  	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_TEST' AND COLUMN_NAME = 'ISAUTOSUB';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD  (ISAUTOSUB NUMBER(1,0))';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;

	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_TEST' AND COLUMN_NAME = 'MANUALSTART';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD  (MANUALSTART NUMBER(1,0))';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;  	
	
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_SERVICES' AND COLUMN_NAME = 'ISAUTOSUB';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  (ISAUTOSUB NUMBER(1,0))';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;

	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_SERVICES' AND COLUMN_NAME = 'MANUALSTART';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD  (MANUALSTART NUMBER(1,0))';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;  	
  	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DATABASE_TYPE' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_DATABASE_TYPE (IID NUMBER(19) NOT NULL, IDBTYPE VARCHAR(255) NOT NULL, IORDER NUMBER(19), CONSTRAINT PK_IEAI_DATABASE_TYPE PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IANALYZEFUNFLAG';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IANALYZEFUNFLAG INTEGER  DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IANALYZEFUNTEXT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IANALYZEFUNTEXT CLOB';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='ISQLEXECMODEL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD ISQLEXECMODEL INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='ISQLEXECMODEL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD ISQLEXECMODEL INTEGER DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICESUB_USER' AND COLUMN_NAME='ISAUTOFLAG';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER ADD  ISAUTOFLAG NUMBER(1,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICESUB_USER' AND COLUMN_NAME='ISERVICEGROUPID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER ADD  ISERVICEGROUPID NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICESUB_USER' AND COLUMN_NAME='IUSERGROUPID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER ADD  IUSERGROUPID NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SERVICESUB_USER_REL' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
      LS_SQL := ' CREATE TABLE IEAI_SERVICESUB_USER_REL (IID NUMBER(19,0) NOT NULL ,ISERVICEGROUPID NUMBER(19,0),IUSERID NUMBER(19,0),ISAUTOFLAG NUMBER(1,0) NOT NULL,ISERVICEID NUMBER(19,0) NOT NULL, ISCRIPTTYPE NUMBER(5,0),CONSTRAINT PK_IEAI_SERVICESUB_USER_REL PRIMARY KEY (IID))';
      EXECUTE IMMEDIATE LS_SQL;
    END IF;
    
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='ISDELETE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD ISDELETE SMALLINT default 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TOOL_BOX' AND COLUMN_NAME='ISDELETE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TOOL_BOX ADD ISDELETE SMALLINT default 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='ISDELETE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD ISDELETE SMALLINT default 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='ISDELETE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD ISDELETE SMALLINT default 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_SHARE_RELATION' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_SHARE_RELATION ( IID  NUMBER(19) NOT NULL, ISHAREID   NUMBER(19) NOT NULL,  ISHAREOBJECTID NUMBER(19) NOT NULL, ISHARETYPE     NUMBER(1) NOT NULL,ISFLOW   NUMBER(1),ISCUSTOMTASK   NUMBER(1),CONSTRAINT PK_SCRIPT_SHARE_RELATION PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICESUB_USER_REL' AND COLUMN_NAME='ISTATUS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER_REL ADD  ISTATUS VARCHAR2 (50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICESUB_USER_REL' AND COLUMN_NAME='ISTARTTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER_REL ADD  ISTARTTIME NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICESUB_USER_REL' AND COLUMN_NAME='IEXECTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICESUB_USER_REL ADD  IEXECTIME NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
--	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_RULEOUTPARAMETER' AND COLUMN_NAME='ISCRIPTID';
--	IF LI_EXISTS = 1 THEN
--		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RULEOUTPARAMETER DROP COLUMN ISCRIPTID';
--		EXECUTE IMMEDIATE LS_SQL;
--	END IF;
--	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_RULEOUTPARAMETER' AND COLUMN_NAME='ISCRIPTID';
--	IF LI_EXISTS = 0 THEN
--		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RULEOUTPARAMETER  ADD ISCRIPTID VARCHAR2 (50)';
--		EXECUTE IMMEDIATE LS_SQL;
--	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_RULEOUTPARAMETER' AND COLUMN_NAME='ISCRIPTID' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=50  ;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RULEOUTPARAMETER ADD DES VARCHAR(50)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_RULEOUTPARAMETER SET DES=ISCRIPTID';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_RULEOUTPARAMETER SET ISCRIPTID=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RULEOUTPARAMETER MODIFY  ISCRIPTID VARCHAR(50)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_RULEOUTPARAMETER SET ISCRIPTID=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RULEOUTPARAMETER DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;
	
		
			
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AUDITING_IPS' AND COLUMN_NAME='IOPERID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_IPS ADD IOPERID INTEGER  DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_FLOW' AND COLUMN_NAME='IPARTEXEC';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_FLOW ADD IPARTEXEC VARCHAR2 (2)  DEFAULT ''0''';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_FLOW' AND COLUMN_NAME='IIGNORE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_FLOW ADD IIGNORE VARCHAR2 (2)  DEFAULT ''0''';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_EMER_SCENE_SS_RELATION' AND COLUMN_NAME='IISSCRIPT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_EMER_SCENE_SS_RELATION ADD  IISSCRIPT INTEGER default 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLE_AUTH_CODE' AND COLUMN_NAME='IISSCRIPT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_DOUBLE_AUTH_CODE ADD  IISSCRIPT INTEGER default 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	
	
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DC_LOGIN' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DC_LOGIN(IID NUMBER(19) PRIMARY KEY, ILOGINNAME VARCHAR2(255), ISEI NUMBER(19),ISVALIDATE NUMBER(2))';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SEQUENTIAL_APP_GROUP' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'CREATE TABLE IEAI_SEQUENTIAL_APP_GROUP(IID  NUMBER(19)  PRIMARY KEY, IAPPLICATIONNAME VARCHAR2(255), IGROUP   VARCHAR2(255),  ISUPPORLINE    VARCHAR2(255),  USEROFFICE  VARCHAR2(255))';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='seqSyncSwitch_Thread';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''seqSyncSwitch_Thread'', ''true'',''时序管理同步工单线程开关'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='seqSyncSwitch_Frequency';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''seqSyncSwitch_Frequency'', ''1'', ''时序管理同步工单线程频率(分钟)'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='cpTimeBegin';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''cpTimeBegin'', ''08:00'', ''变更时段开始时间'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='cpTimeEnd';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''cpTimeEnd'', ''18:00'', ''变更时段结束时间'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='CAMA_IP';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''CAMA_IP'', '''',''CAMA服务IP'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='CAMA_PORT';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''CAMA_PORT'', '''', ''CAMA服务PORT'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 select SIGN(COUNT(*)) INTO LI_EXISTS from IEAI_SUS_CONFIG_PARAM a  where  a.iparam_name ='seqSyncSwitch_stateSync';
	 IF LI_EXISTS = 0 THEN
         LS_SQL := 'insert into IEAI_SUS_CONFIG_PARAM (IID, IPARAM_NAME, IPARAM_VALUE, IDESC) values ((select (case when max(iid) is null then 0 else max(iid) end)+1 from IEAI_SUS_CONFIG_PARAM  ), ''seqSyncSwitch_stateSync'', ''true'', ''变更工作台同步工单是否同步状态'')';
     	 EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
	
	
	 LS_SQL := 'CREATE OR REPLACE VIEW V_SCRIPT_TASK_EXECTIME AS SELECT T.IID, SUM(CASE WHEN D.ISTATUS = 20 THEN 1  ELSE 0 END) AS SUCCESSTIMES, COUNT(D.IID) TOTALTIMES, (CASE  WHEN COUNT(D.IID) = 0 THEN 0  ELSE ROUND(SUM(CASE WHEN D.IID IS NOT NULL AND D.ISTATUS = 20 THEN 1  ELSE  0 END) * 100 / COUNT(D.IID)) END) AS SUCCESSRATE FROM IEAI_SCRIPT_TEST T LEFT JOIN IEAI_SCRIPT_FLOW D ON T.ISCRIPTUUID = D.IMXSERVICEID WHERE T.IISFLOW = 1  AND T.ISDELETE=0  GROUP BY T.IID UNION ALL SELECT T.IID, SUM(CASE WHEN D.ISTATE = 20 THEN 1 ELSE  0  END) AS SUCCESSTIMES, COUNT(D.IID) TOTALTIMES,  (CASE WHEN COUNT(D.IID) = 0 THEN  0 ELSE  ROUND(SUM(CASE WHEN D.IID IS NOT NULL AND D.ISTATE = 20 THEN 1 ELSE 0 END) * 100 / COUNT(D.IID))  END) AS SUCCESSRATE  FROM IEAI_SCRIPT_TEST T LEFT JOIN IEAI_SCRIPT_INSTANCE D ON T.ISCRIPTUUID = D.ISERVICEID WHERE T.IISFLOW = 0  AND T.ISDELETE=0  GROUP BY T.IID';
	 EXECUTE IMMEDIATE LS_SQL;
 
	 LS_SQL := 'CREATE OR REPLACE VIEW V_SCRIPT_EXECTIME AS SELECT T.ILASTID,SUM(CASE WHEN D.ISTATE = 20 THEN  1 ELSE  0 END) AS SUCCESSTIMES,  COUNT(D.IID) TOTALTIMES,   (CASE WHEN COUNT(D.IID) = 0 THEN 0    ELSE ROUND(SUM(CASE WHEN D.IID IS NOT NULL AND D.ISTATE = 20 THEN   1 ELSE   0 END) * 100 / COUNT(D.IID)) END) AS SUCCESSRATE FROM IEAI_SCRIPT_TEST T  LEFT JOIN IEAI_SCRIPT_INSTANCE D ON T.ISCRIPTUUID = D.ISERVICEID  WHERE T.ISDELETE=0 GROUP BY T.ILASTID';
	 EXECUTE IMMEDIATE LS_SQL;

	 LS_SQL := 'CREATE OR REPLACE VIEW V_SCRIPT_SHAREUSERID AS SELECT A.ISHAREID, B.IID AS IUSERID  FROM IEAI_SCRIPT_SHARE_RELATION A, IEAI_USER B, IEAI_USER_GROUP_RELATION  C WHERE A.ISHAREOBJECTID = C.IGROUPID  AND C.IUSERID = B.IID  AND A.ISHARETYPE = 2 AND A.ISCUSTOMTASK=0 UNION SELECT ISHAREID,ISHAREOBJECTID AS IUSERID  FROM IEAI_SCRIPT_SHARE_RELATION WHERE ISHARETYPE in( 1,0) AND  ISCUSTOMTASK=0';
	 EXECUTE IMMEDIATE LS_SQL;

	 LS_SQL := 'CREATE OR REPLACE VIEW V_SCRIPT_CUS_SHAREUSERID AS SELECT A.ISHAREID, B.IID AS IUSERID FROM IEAI_SCRIPT_SHARE_RELATION A, IEAI_USER B,  IEAI_USER_GROUP_RELATION  C WHERE A.ISHAREOBJECTID = C.IGROUPID AND C.IUSERID = B.IID AND A.ISHARETYPE = 2 AND A.ISCUSTOMTASK=1 UNION SELECT ISHAREID,ISHAREOBJECTID AS IUSERID  FROM IEAI_SCRIPT_SHARE_RELATION WHERE ISHARETYPE in( 1,0) AND  ISCUSTOMTASK=1';
	 EXECUTE IMMEDIATE LS_SQL;
	 
	 LS_SQL := 'insert into IEAI_SCRIPT_SHARE_RELATION select FUN_GET_NEXT_PK(''IEAI_SCRIPT_SHARE_RELATION''), iid, -1, 0, (select iisflow from ieai_script_test where iid = a.iid), 0 from ieai_script_share a where not exists (select 1 from IEAI_SCRIPT_SHARE_RELATION B WHERE A.IID=B.ISHAREID)';
	 EXECUTE IMMEDIATE LS_SQL;
	 commit;
END;
/
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 

  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_APPLY' AND COLUMN_NAME='SETMODEL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY ADD SETMODEL VARCHAR2(50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_APPLY' AND COLUMN_NAME='ENV';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY ADD ENV VARCHAR2(50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_APPLY' AND COLUMN_NAME='USEDATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY ADD USEDATE VARCHAR2(50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
 	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IANALYZEFUNTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IANALYZEFUNTYPE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

 	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_REPORT_TEMPLATE' AND COLUMN_NAME='IUSERID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_REPORT_TEMPLATE ADD IUSERID DECIMAL(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
 	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IMODEL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IMODEL DECIMAL(2)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IREAPPSID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IREAPPSID DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='ICREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD ICREATETIME DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IUPDATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IUPDATETIME DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IDISK';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IDISK DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='ICPU';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD ICPU DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IMEMORY';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IMEMORY DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_APPLY' AND COLUMN_NAME='IRMANAGE_ID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY MODIFY IRMANAGE_ID DECIMAL(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TMP_SERVICE' AND COLUMN_NAME='SERVICEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TMP_SERVICE MODIFY SERVICEID VARCHAR(100)';
			EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ASM' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ASM( IID NUMBER(19,0) NOT NULL, ISYSNAME VARCHAR(255), INAME VARCHAR(255), IRACVERSION VARCHAR(50), ISTATUS VARCHAR(50), IRACIP VARCHAR(16), IRACUSER VARCHAR(50), IAGENTID NUMBER(19,0), IOPERSYS VARCHAR(255), constraint PK_IEAI_SCRIPT_ASM PRIMARY KEY (IID) ) ';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ASM_INFO' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ASM_INFO( IID NUMBER(19,0) NOT NULL, IMAINID NUMBER(19,0), STATE  VARCHAR(50), TYPE  VARCHAR(50),  REBAL  VARCHAR(50), SECTOR VARCHAR(50), BLOCK   VARCHAR(50), AU  VARCHAR(50), TOTAL_MB  VARCHAR(50), FREE_MB  VARCHAR(50),  REQ_MIR_FREE_MB  VARCHAR(50), USABLE_FILE_MB  VARCHAR(50), OFFLINE_DISKS  VARCHAR(50), VOTING_FILES  VARCHAR(50), NAME  VARCHAR(50), constraint PK_IEAI_SCRIPT_ASM_INFO PRIMARY KEY (IID) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_RULE_SERVICES' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_RULE_SERVICES(IID DECIMAL(19,0) NOT NULL,ISERVICEUUID VARCHAR(255),ICOLNAME VARCHAR(50),IRULEID DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_RULE_SERVICES PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_RESAPP_PARAM' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_RESAPP_PARAM(  IID   DECIMAL(19) NOT NULL,  INAME VARCHAR2(255),  IDESC VARCHAR2(255),  ITYPE DECIMAL(2),  CONSTRAINT PK_IEAI_SCRIPT_RESAPP_PARAM PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_RESAPP_PARAM_SUB' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_RESAPP_PARAM_SUB(  IID DECIMAL(19,0) NOT NULL,  INAME   VARCHAR2(255),  IVALUE  VARCHAR2(255),  IORDER  INTEGER,  IMAINID DECIMAL(19),  IDESC   VARCHAR2(255),  IFLAG   DECIMAL(1),  CONSTRAINT PK_IEAI_SCRIPT_RESAPP_P_S PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_RESAPP_PARAM_VALUE' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_RESAPP_PARAM_VALUE(  IID DECIMAL(19,0) NOT NULL,  INAME VARCHAR2(255),  IVALUE VARCHAR2(255),  IORDER INTEGER,  IMAINID DECIMAL(19),  IWORKITEMID DECIMAL(19),  IRESID DECIMAL(19),  IFLAG DECIMAL(1),  CONSTRAINT PK_IEAI_SCRIPT_RESAPP_P_V PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SERVICE_ANA_COLUMNS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SERVICE_ANA_COLUMNS (IID NUMBER (19, 0) NOT NULL,ISERVICEID NUMBER (19, 0),ICOLUMNNAME VARCHAR2 (50),ICOLUMNTYPE NUMBER (1, 0),ICOLUMNORDER NUMBER (11, 0),ICOLUMNLEN NUMBER (5, 0),ICOLUMNDESC VARCHAR2 (500),TABLENAME VARCHAR2 (50),MCFLAG NUMBER (1, 0) ,CONSTRAINT PK_IEAI_SERVICE_ANA_COLUMNS PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SERVICE_ANA_TABLE_REL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SERVICE_ANA_TABLE_REL (IID NUMBER(19,0) NOT NULL ,IORIGINAL VARCHAR2(50) ,IMAIN VARCHAR2(50) ,ICHILD VARCHAR2(50),IANALYZETEXT VARCHAR2(4000) ,ISTATUS NUMBER(1,0),ISERVICEID NUMBER(19,0),IVALUE VARCHAR2 (255),IOPERATOR NUMBER (1,0), CONSTRAINT PK_IEAI_SERVICE_ANA_TABLE_REL PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_COLUMNS' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_COLUMNS DROP COLUMN ISERVICEID';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_COLUMNS' AND COLUMN_NAME='ISCRIPTUUID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_COLUMNS  ADD ISCRIPTUUID VARCHAR2 (50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL DROP COLUMN ISERVICEID';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLUMN_NAME='ISCRIPTUUID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL  ADD ISCRIPTUUID VARCHAR2 (50)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID=1;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'insert into IEAI_DATABASE_TYPE (iid, idbtype, iorder) values (1, ''ORACLE'', 1)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID=2;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'insert into IEAI_DATABASE_TYPE (iid, idbtype, iorder) values (2, ''DB2'', 2)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID=3;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'insert into IEAI_DATABASE_TYPE (iid, idbtype, iorder) values (3, ''MYSQL'', 3)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	COMMIT WORK;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TMP_S_R' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_TMP_S_R ( IID NUMBER (19) NOT NULL, RID NUMBER (19), SID NUMBER (19) , CONSTRAINT PK_IEAI_IEAI_TMP_S_R PRIMARY KEY (IID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IMPAGENTIP' AND COLUMN_NAME='CONFIGPARAMS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE TMP_IMPAGENTIP ADD CONFIGPARAMS VARCHAR2(200)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	LS_SQL := 'select ips.iworkitemid,nvl(ca.iid, 0) coatid,ips.iid ipsiid,agi.iagentinfo_id agentid,agi.iagent_name as iagentname,agi.iagent_ip iagentip,agi.iagent_port agentport,agi.icom_name icomname,col.icolvalue as taskName,ins.iid as ins_iid,ins.istate ins_state,ins.iparameters,ins.istarttime,ins.iendtime,w.performuser,w.istartuser,(select c.icolvalue from ieai_doublecheck_colvalue c where c.iworkitemid = w.iid and c.icolheader = ''shutDownUUID'') shutdownuuid from IEAI_SCRIPT_AUDITING_ips ips left join ieai_doublecheck_workitem w on ips.iworkitemid = w.iid left join ieai_doublecheck_colvalue col on col.iworkitemid = ips.iworkitemid left join ieai_agentinfo agi on ips.icpid = agi.iagentinfo_id left join ieai_scripts_coat ca on ca.iworkitemid = ips.iworkitemid left join ieai_script_instance ins on ca.iid = ins.imainid and ins.iagentip = agi.iagent_ip where col.icolheader = ''taskName'' and exists (select 1 from ieai_doublecheck_colvalue cc where cc.iworkitemid = w.iid and cc.icolheader = ''shutDownUUID'')';
	 EXECUTE IMMEDIATE LS_SQL;
END;
/

DECLARE
LS_SQL  VARCHAR2(4000);
LI_EXISTS  SMALLINT;
BEGIN
  	UPDATE IEAI_SCRIPT_TEST 
	SET ICONTENT=REPLACE(ICONTENT,'<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="200" as="view"/>','<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="237" as="view"/>')  
	WHERE IISFLOW=1 ;
	 
	UPDATE IEAI_SCRIPT_SERVICES 
	SET ICONTENT=REPLACE(ICONTENT,'<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="200" as="view"/>','<Object url="page/dubbo/scriptService/flowDsg/GFSSVIEW/flowCustomizedEditScriptWindow.jsp" width="600" height="237" as="view"/>') 
	WHERE IISFLOW=1 ;
	
	COMMIT;
END;
/
-- 4.7.20 version Script patch is as follows
CREATE OR REPLACE PROCEDURE PROC_SCRIPT_TURNOFF_PROCESS(SHUTDOWNNAME IN VARCHAR2,
                                                        CHECKNAME    IN VARCHAR2,
                                                        RETURNVALUE  OUT NUMBER,
                                                        PRM_ERRORMSG OUT VARCHAR2) AS
BEGIN

  DECLARE
  
    LV_SQL VARCHAR2(32767);
		LV_SQL1 VARCHAR2(32767);
		LV_SQL2 VARCHAR2(32767);
		LV_SQL3 VARCHAR2(32767);
	
  BEGIN
  
    LV_SQL := 'grant CREATE view to ' ||  sys_context('USERENV', 'CURRENT_USER');
    EXECUTE IMMEDIATE LV_SQL;
    LV_SQL1 := 'CREATE OR REPLACE VIEW  V_SCRIPT_SHUTDOWNPROCESSINFO  AS  SELECT (SELECT DD.ICOLVALUE FROM IEAI_DOUBLECHECK_COLVALUE DD WHERE DD.IWORKITEMID = AA.SHUTDOWNWORKITEMID AND DD.ICOLHEADER = ''fromId'') SHUTDOWNSCRIPTIID, (SELECT SER.ISCRIPTUUID FROM IEAI_DOUBLECHECK_COLVALUE DD, IEAI_SCRIPT_SERVICES SER WHERE DD.IWORKITEMID = AA.SHUTDOWNWORKITEMID AND SER.IID = DD.ICOLVALUE AND DD.ICOLHEADER = ''fromId'') SHUTDOWNSCRIPTUUID, (SELECT DD.ICOLVALUE FROM IEAI_DOUBLECHECK_COLVALUE DD WHERE DD.IWORKITEMID = AA.CHECKWORKITEMID AND DD.ICOLHEADER = ''fromId'') STARTCHECKSCRIPTIID, (SELECT SER.ISCRIPTUUID FROM IEAI_DOUBLECHECK_COLVALUE DD, IEAI_SCRIPT_SERVICES SER WHERE DD.IWORKITEMID = AA.CHECKWORKITEMID AND SER.IID = DD.ICOLVALUE AND DD.ICOLHEADER = ''fromId'') STARTCHECKSCRIPTUUID, (SELECT LISTAGG(IVALUE, '','') WITHIN GROUP(ORDER BY IWORKITEMID, IORDER) IPARAMETERS FROM IEAI_SCRIPT_AUDITING_PARAMS WHERE IWORKITEMID = AA.SHUTDOWNWORKITEMID AND IAUDIIPID = AA.SHUTDOWNIPSIID) IPARAMETERS,';
	  LV_SQL2 := ' AA.*  FROM (SELECT A.ISTARTUSER, A.PERFORMUSER, A.SHUTDOWNUUID, A.AGENTID, A.IAGENTNAME, A.IAGENTIP, A.AGENTPORT, A.ICOMNAME, A.BUTTERFLYVERSION, MAX((CASE WHEN A.TASKNAME LIKE ''' || SHUTDOWNNAME || '%'' THEN IWORKITEMID ELSE 0  END)) AS SHUTDOWNWORKITEMID, MAX((CASE WHEN A.TASKNAME LIKE ''' || CHECKNAME || '%'' THEN IWORKITEMID ELSE 0 END)) AS CHECKWORKITEMID, MAX((CASE WHEN A.TASKNAME LIKE  ''' || SHUTDOWNNAME || '%'' THEN A.COATID ELSE 0 END)) AS SHUTDOWNCOATID,';
	  LV_SQL2 := ' MAX((CASE WHEN A.TASKNAME LIKE  ''' || CHECKNAME || '%'' THEN A.COATID ELSE 0 END)) AS CHECKCOATID, MAX((CASE WHEN A.TASKNAME LIKE  ''' || SHUTDOWNNAME || '%'' THEN INS_IID ELSE 0 END)) AS INSSHUTDOWNIID,MAX((CASE WHEN A.TASKNAME LIKE ''' || SHUTDOWNNAME || '%'' THEN INS_STATE ELSE 0 END)) AS INSSHUTDOWNSTATE, MAX((CASE WHEN A.TASKNAME LIKE  ''' || CHECKNAME || '%'' THEN INS_IID ELSE 0 END)) AS INSCHECKIID, MAX((CASE WHEN A.TASKNAME LIKE  ''' || CHECKNAME || '%'' THEN INS_STATE ELSE 0 END)) AS INSCHECKSTATE, MAX((CASE WHEN A.TASKNAME LIKE  ''' || SHUTDOWNNAME || '%'' THEN A.ISTARTTIME ELSE 0 END)) AS INSISTARTTIME, MAX((CASE WHEN A.TASKNAME LIKE  ''' || CHECKNAME || '%'' THEN A.IENDTIME ELSE 0 END)) AS INSIENDTIME, MAX((CASE WHEN A.TASKNAME LIKE  ''' || SHUTDOWNNAME || '%'' THEN a.ipsiid ELSE 0 END)) AS SHUTDOWNIPSIID, MAX((CASE WHEN A.TASKNAME LIKE  ''' || CHECKNAME || '%'' THEN a.ipsiid ELSE 0 END)) AS CHECKIPSIID, FUN_GET_DATE_NUMBER_NEW(current_timestamp, 8) as CURRENTTIME FROM V_SCRIPT_SHUTDOWNAGENTLIST A WHERE A.ISTATE IN (5,10) GROUP BY A.ISTARTUSER, A.PERFORMUSER,  A.SHUTDOWNUUID,  A.IAGENTNAME, A.IAGENTIP, A.AGENTPORT, A.BUTTERFLYVERSION, A.AGENTID, A.ICOMNAME) AA';
    LV_SQL := LV_SQL1||LV_SQL2||LV_SQL3;
	EXECUTE IMMEDIATE LV_SQL;
    RETURNVALUE := 1;
  EXCEPTION
    WHEN OTHERS THEN
      PRM_ERRORMSG := SQLERRM;
      RETURNVALUE  := 0;
  END;
END PROC_SCRIPT_TURNOFF_PROCESS;
/



create or replace view v_script_shutdownagentlist as
select ips.iworkitemid,
       nvl(ca.iid, 0) coatid,
       ips.iid ipsiid,
       agi.iagentinfo_id agentid,
       agi.iagent_name as iagentname,
       agi.iagent_ip iagentip,
       agi.iagent_port agentport,
       agi.icom_name icomname,
       col.icolvalue as taskName,
       ins.iid as ins_iid,
       ins.istate ins_state,
       ins.iparameters,
       ins.istarttime,
       ins.iendtime,
       w.performuser,
       w.istartuser,
       w.butterflyversion,
       w.istate,
       (select c.icolvalue
          from ieai_doublecheck_colvalue c
         where c.iworkitemid = w.iid
           and c.icolheader = 'shutDownUUID') shutdownuuid
  from IEAI_SCRIPT_AUDITING_ips ips
  left join ieai_doublecheck_workitem w
    on ips.iworkitemid = w.iid
  left join ieai_doublecheck_colvalue col
    on col.iworkitemid = ips.iworkitemid
  left join ieai_agentinfo agi
    on ips.icpid = agi.iagentinfo_id
  left join ieai_scripts_coat ca
    on ca.iworkitemid = ips.iworkitemid
  left join ieai_script_instance ins
    on ca.iid = ins.imainid
   and ins.iagentip = agi.iagent_ip
 where col.icolheader = 'taskName'
   and exists (select 1
          from ieai_doublecheck_colvalue cc
         where cc.iworkitemid = w.iid and cc.icolheader = 'shutDownUUID');
/ 
 
 
 
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_SYSTEMTYPE' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := ' CREATE TABLE IEAI_RESOURCE_SYSTEMTYPE( IID NUMBER(19,0) NOT NULL,  INAME  VARCHAR2(50), constraint PK_IEAI_RESOURCE_SYSTEMTYPE PRIMARY KEY (IID) )';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_MANUAL_START_RESULT' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := '  CREATE TABLE IEAI_MANUAL_START_RESULT( IID NUMBER(19,0) NOT NULL,  ISTARTTIME  DATE,IUSERID NUMBER(19,0),IRESID NUMBER(19,0),   ISERVICEUUID  VARCHAR2(50) , IGROUPID  NUMBER(19,0),IRESULT1  VARCHAR2(255),IRESULT2  VARCHAR2(255),IRESULT3  VARCHAR2(255),IRESULT4  VARCHAR2(255),  IRESULT5  VARCHAR2(255),IRESULT6  VARCHAR2(255),IRESULT7  VARCHAR2(255),IRESULT8  VARCHAR2(255),IRESULT9  VARCHAR2(255),IRESULT10  VARCHAR2(255),  IRESULT11  VARCHAR2(255),IRESULT12  VARCHAR2(255),IRESULT13  VARCHAR2(255),IRESULT14  VARCHAR2(255),IRESULT15  VARCHAR2(255),IRESULT16  VARCHAR2(255),  IRESULT17  VARCHAR2(255),IRESULT18  VARCHAR2(255),IRESULT19  VARCHAR2(255),IRESULT20  VARCHAR2(255),IRESULT21  VARCHAR2(255),IRESULT22  VARCHAR2(255),  IRESULT23  VARCHAR2(255),IRESULT24  VARCHAR2(255),IRESULT25  VARCHAR2(255),IRESULT26  VARCHAR2(255),IRESULT27  VARCHAR2(255),IRESULT28  VARCHAR2(255),IRESULT29  VARCHAR2(255),IRESULT30  VARCHAR2(255),   constraint PK_IEAI_MANUAL_START_RESULT PRIMARY KEY (IID) )';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_S_G_RELATION' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := ' CREATE TABLE IEAI_SCRIPT_S_G_RELATION (IID DECIMAL(19,0) NOT NULL,ISERVICEGID DECIMAL(19,0),ISERVICEID  DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_S_G_RELATION PRIMARY KEY(IID))';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_SERVICE_GROUP' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := 'CREATE TABLE IEAI_SCRIPT_SERVICE_GROUP (ICREATETIME DECIMAL(19,0),ICREATEUSERID DECIMAL(19,0),IFLAG INTEGER DEFAULT 0,IID DECIMAL(19,0) NOT NULL,INAME VARCHAR(255),INAMEDESC VARCHAR(255),IUPDATETIME DECIMAL(19,0),IUPDATEUSERID DECIMAL(19,0),CONSTRAINT PK_IEAI_SCRIPT_SERVICE_GROUP PRIMARY KEY(IID))';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DOUBLECHECK_SCRIPT_S_G' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := ' CREATE TABLE IEAI_DOUBLECHECK_SCRIPT_S_G(IID DECIMAL(19,0) NOT NULL,ISERVICEGID DECIMAL(19,0),ISERVICEID  DECIMAL(19,0),ISTATUS INTEGER DEFAULT 0,ITYPE   INTEGER DEFAULT 0,IWORKITEMID DECIMAL(19,0),CONSTRAINT PK_IEAI_DOUBLEC_SCRIPT_S_G PRIMARY KEY(IID))';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IBUSINESSTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD IBUSINESSTYPE VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='ISERVICEAUTO';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES  ADD ISERVICEAUTO NUMBER(1,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICE_GROUP' AND COLUMN_NAME='ISERVICEAUTO';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICE_GROUP  ADD ISERVICEAUTO NUMBER(1,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_COLLECTION' AND COLUMN_NAME='IGROUPID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_COLLECTION  ADD IGROUPID NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	


   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPTS_COAT' AND COLUMN_NAME='IDBSTATE';
   IF LI_EXISTS = 0 THEN
       LS_SQL := 'ALTER TABLE IEAI_SCRIPTS_COAT ADD IDBSTATE INTEGER DEFAULT 10';
       EXECUTE IMMEDIATE LS_SQL;
   END IF;
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OF_MY' AND COLUMN_NAME='IEXECTIMENEXT';
  IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD IEXECTIMENEXT VARCHAR(255)';
      EXECUTE IMMEDIATE LS_SQL;
  END IF;    
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OF_MY' AND COLUMN_NAME='ISERVICEID';
  IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD ISERVICEID DECIMAL(19) DEFAULT 0';
      EXECUTE IMMEDIATE LS_SQL;
  END IF;
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OF_MY' AND COLUMN_NAME='IDELETED';
  IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD IDELETED DECIMAL(1,0)';
      EXECUTE IMMEDIATE LS_SQL;
  END IF;
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OF_MY' AND COLUMN_NAME='IANALYZEFUNTYPE';
  IF LI_EXISTS = 0 THEN
      LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OF_MY ADD IANALYZEFUNTYPE VARCHAR(255)';
      EXECUTE IMMEDIATE LS_SQL;
  END IF;
  
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IALTERLOGNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD IALTERLOGNAME VARCHAR(300)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
  
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER' AND COLUMN_NAME='IMODIFYTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_USER  ADD IMODIFYTIME NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='ICONTINUECOUNT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD ICONTINUECOUNT NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='ICUMULATECOUNT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD ICUMULATECOUNT  NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_CHECK_RESULT' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := ' CREATE TABLE IEAI_RESOURCE_CHECK_RESULT( IID NUMBER(19,0) NOT NULL, IREID  NUMBER(19,0), ICHECKTIME NUMBER(19,0),ICHECKRESULT  NUMBER(1,0),IMESS  CLOB, constraint PK_IEAI_RESOURCE_CHECK_RESULT PRIMARY KEY (IID) )';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST  ADD ISERVICEID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES  ADD ISERVICEID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_S_G_RELATION' AND COLUMN_NAME='IORDER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_S_G_RELATION  ADD IORDER NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
-- 4.7.21 version Script patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ANALYALGOR' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
       LS_SQL := ' CREATE TABLE IEAI_SCRIPT_ANALYALGOR (IID NUMBER (19, 0) NOT NULL, ISERVICEID VARCHAR2 (255), IVERSION VARCHAR2 (255), ICREATETIME NUMBER (19, 0), ICONTENT CLOB, CONSTRAINT PK_IEAI_SCRIPT_ANALYALGOR PRIMARY KEY (IID) ) ';
       EXECUTE IMMEDIATE LS_SQL;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES  ADD ISERVICEID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_COLUMNS' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_COLUMNS  ADD ISERVICEID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL  ADD ISERVICEID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='ITIMEOUT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST  ADD ITIMEOUT NUMBER (19, 0) DEFAULT -1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='ITIMEOUT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES  ADD ITIMEOUT NUMBER (19, 0) DEFAULT -1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_INSTANCE' AND COLUMN_NAME='ITIMEOUT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE  ADD ITIMEOUT NUMBER (1, 0) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
    BEGIN 

	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='IDBTYPE' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=255 ;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD DES VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TEST SET DES=IDBTYPE';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TEST SET IDBTYPE=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST MODIFY  IDBTYPE VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TEST SET IDBTYPE=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IDBTYPE' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=255;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD DES VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_SERVICES SET DES=IDBTYPE';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_SERVICES SET IDBTYPE=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES MODIFY  IDBTYPE VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_SERVICES SET IDBTYPE=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TOOL_BOX' AND COLUMN_NAME='IDBTYPE' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=255;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TOOL_BOX ADD DES VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TOOL_BOX SET DES=IDBTYPE';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TOOL_BOX SET IDBTYPE=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TOOL_BOX MODIFY  IDBTYPE VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TOOL_BOX SET IDBTYPE=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TOOL_BOX DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;
	
	
END;
/


DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
    BEGIN 
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_APPSYSTEM_PROP' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
          LS_SQL := 'CREATE TABLE IEAI_SCRIPT_APPSYSTEM_PROP(IID NUMBER(19,0) NOT NULL,IPROPNAME VARCHAR2(255),IORDER INTEGER,ICREATETIME NUMBER(19,0),IMODIFYTIME NUMBER(19,0),IDEL INTEGER, CONSTRAINT PK_IEAI_SCRIPT_APPSYSTEM_PROP PRIMARY KEY (IID))';
          EXECUTE IMMEDIATE LS_SQL;
    END IF; 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SERVICE_BIND_CONDITION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SERVICE_BIND_CONDITION (ISERVICEID NUMBER(19),IORDER NUMBER(19),ITYPE NUMBER(1),ICONDITION VARCHAR2(125),IWORKITEMID NUMBER(19))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_USR_SYS_RELATION' AND OBJECT_TYPE = 'TABLE';
      IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEAI_USR_SYS_RELATION(IID  NUMERIC(19) NOT NULL, IUSERID NUMERIC(19) NOT NULL,ISYSID NUMERIC(19) NOT NULL, IMANAGEFLAG NUMERIC(19) DEFAULT 0, CONSTRAINT PK_IEAI_USR_SYS_RELATION PRIMARY KEY(IID))';
      EXECUTE IMMEDIATE LS_SQL;
     END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_S_G_RELATION' AND COLUMN_NAME='IORDER';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_S_G_RELATION ADD IORDER NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_VERRES' AND COLUMN_NAME='IWORKITEMID';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_VERRES ADD IWORKITEMID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_VERSION_TREE' AND COLUMN_NAME='IPROTYPE';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_VERSION_TREE ADD IPROTYPE varchar2(255)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='IUPID';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD IUPID NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IUPID';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IUPID NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_APPSYSTEM_PROP' AND COLUMN_NAME='IDEL';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_APPSYSTEM_PROP ADD IDEL INTEGER';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;  
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_OS' AND COLUMN_NAME='ICREATETIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_OS ADD ICREATETIME NUMBER(19)';
	     EXECUTE IMMEDIATE LS_SQL;
   END IF; 
	   
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_OS' AND COLUMN_NAME='IMODIFYTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_OS ADD IMODIFYTIME NUMBER(19)';
	     EXECUTE IMMEDIATE LS_SQL;
   END IF; 
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_OS' AND COLUMN_NAME='IDEL';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_OS ADD IDEL INTEGER';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATABASE_TYPE' AND COLUMN_NAME='ICREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_DATABASE_TYPE ADD ICREATETIME NUMBER(19)';
     EXECUTE IMMEDIATE LS_SQL;
   END IF; 
	   
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATABASE_TYPE' AND COLUMN_NAME='IMODIFYTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_DATABASE_TYPE ADD IMODIFYTIME NUMBER(19)';
	     EXECUTE IMMEDIATE LS_SQL;
   END IF;
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATABASE_TYPE' AND COLUMN_NAME='IURL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_DATABASE_TYPE ADD IURL VARCHAR2(255)';
     EXECUTE IMMEDIATE LS_SQL;
   END IF; 
	   
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATABASE_TYPE' AND COLUMN_NAME='IDRIVER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_DATABASE_TYPE ADD IDRIVER VARCHAR2(255)';
     EXECUTE IMMEDIATE LS_SQL;
   END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DATABASE_TYPE' AND COLUMN_NAME='IDEL';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_DATABASE_TYPE ADD IDEL INTEGER';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_SYSTEMTYPE' AND COLUMN_NAME='ICREATETIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_SYSTEMTYPE ADD ICREATETIME NUMBER(19)';
     EXECUTE IMMEDIATE LS_SQL;
   END IF; 
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_SYSTEMTYPE' AND COLUMN_NAME='IMODIFYTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_SYSTEMTYPE ADD IMODIFYTIME NUMBER(19)';
     EXECUTE IMMEDIATE LS_SQL;
   END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_SYSTEMTYPE' AND COLUMN_NAME='IDEL';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_SYSTEMTYPE ADD IDEL INTEGER';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
    BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_WORKRES_RECORD' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_WORKRES_RECORD (IID NUMBER (19, 0) NOT NULL, IWORKITEMID NUMBER (19, 0), ISID NUMBER (19, 0), ISERVICEID VARCHAR2 (255), IRID NUMBER (19, 0), IEXECUSER VARCHAR2 (255), IUPDATETIME NUMBER (19, 0), ISTATE NUMBER(1,0), IDESC VARCHAR2 (255), CONSTRAINT PK_IEAI_SCRIPT_WORKRES_RECORD PRIMARY KEY (IID) )';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/
-- 4.7.22 version Script patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
	BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='IUPDATEDATE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD IUPDATEDATE NUMBER(19)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='IUPDATEUSER';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD IUPDATEUSER VARCHAR2(200)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='IUPDATEUSERID';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD IUPDATEUSERID NUMBER(19)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='IUPDATEUSERID';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE  IEAI_SCRIPT_TEST  ADD IUPDATEUSERID NUMBER(19)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='ISYSNAMEABB';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE  IEAI_SCRIPT_BUSINESS_SYSTEM  ADD ISYSNAMEABB  VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 
END;
/
-- 4.7.23 version Script patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
	BEGIN 
	
	--该列给现场发时候需要注意--IANALYZETEXT--
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLUMN_NAME='IANALYZETEXT';
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL 	DROP COLUMN IANALYZETEXT';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SERVICE_ANA_TABLE_REL' AND COLUMN_NAME='IANALYZETEXT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SERVICE_ANA_TABLE_REL ADD IANALYZETEXT CLOB';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='IPROPNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD IPROPNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPTS_FLOW' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPTS_FLOW (FLOWID NUMBER(19),INSTANCEID NUMBER(19),RESID NUMBER(19))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_TIMECONFIG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_TIMECONFIG ( IID     	NUMBER(19) NOT NULL, INAME VARCHAR2(50), CREATETIME	VARCHAR2(50),  CREATEUSER    	NUMBER(19), CORNTIME VARCHAR2(50) ,SERVICEID NUMBER(19) ,  SERVICENAME VARCHAR2(255),  IDESC VARCHAR2(255),  STATUS NUMBER(1) default 0 ,  CORNTYPE NUMBER(1) default 0 , CONSTRAINT IEAI_SCRIPT_TIMECONFIG PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IRAC';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IRAC VARCHAR(20)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='IPROJECTFLAG';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD IPROJECTFLAG NUMBER(1)';
   	 	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_TEMPLATE_ANALY' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_TEMPLATE_ANALY (IID NUMBER(19) NOT NULL,INAME VARCHAR2(255),ISERVICEID NUMBER(19),IAGENTID NUMBER(19),IPARAMETER VARCHAR2(255),CONSTRAINT PK_SCRIPT_TEMPLATE_ANALY PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_PARAMETER_WORKITEM' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_PARAMETER_WORKITEM (IID NUMBER(19) NOT NULL,ISERVICEID NUMBER(19),IPARAM_TYPE VARCHAR2(500),IPARAM_DEFAULT_VALUE VARCHAR2(500),IPARAM_DESC VARCHAR2(500),IPARAM_ORDER NUMBER(10),IWORKITEMID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPT_PARAMETER_W PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	COMMIT;
	
END;
/
CREATE OR REPLACE FUNCTION FUN_GET_RUNTIME(starttime IN NUMBER,
                                           endtime   in number)
  RETURN varchar2 IS
BEGIN
  DECLARE
    LV_RETURN     VARCHAR2(30); --返回值
    LI_BETWEENSEC number(13, 3); --秒差
    LV_NOWDATENUMBER number(16);
    LI_SECONDS    SMALLINT; --秒数
    LI_MINUTES    SMALLINT; --分钟数
    LI_HOURS      SMALLINT; --小时数
    LI_DAYS       INT; --日数
  
    SI_SECONDSMILLISEC CONSTANT INT := 1000;
    si_day             CONSTANT VARCHAR2(2) := '天';
    si_sec             CONSTANT VARCHAR2(2) := '秒';
    si_hour            CONSTANT VARCHAR2(4) := '小时';
    si_minute          CONSTANT VARCHAR2(2) := '分';
  BEGIN
    LV_RETURN := '';
    LV_nowdateNumber:=TO_NUMBER(sysdate - TO_DATE('1970-01-01 8:0:0', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60 * 1000;
    --计算时间差   秒
    LI_BETWEENSEC := (nvl(endtime, LV_nowdateNumber) - nvl(starttime, 0)) /
                     SI_SECONDSMILLISEC;
  
    /*
     天：( 总秒数 /60/60/24)
    小时：(( 总秒数-60*60*24*天)/60*60)
    分钟：( 总秒数-天*24*60*60-小时*60*60)/60
    秒：总秒数 - 天*24*60*60-小时*60*60-分钟*60
    */
  
    LI_DAYS    := trunc(LI_BETWEENSEC / 60 / 60 / 24, 0);
    LI_HOURS   := trunc((LI_BETWEENSEC - 60 * 60 * 24 * LI_DAYS) / 60 / 60,
                        0);
    LI_MINUTES := trunc((LI_BETWEENSEC - LI_DAYS * 60 * 60 * 24 -
                        LI_HOURS * 60 * 60) / 60,
                        0);
    LI_SECONDS := trunc((LI_BETWEENSEC - LI_DAYS * 60 * 60 * 24 -
                        LI_HOURS * 60 * 60 - LI_MINUTES * 60),
                        0);
    if LI_DAYS != 0 then
      LV_RETURN := LV_RETURN || LI_DAYS || si_day;
    end if;
    if LI_HOURS != 0 then
      LV_RETURN := LV_RETURN || LI_HOURS || si_hour;
    end if;
    if LI_MINUTES != 0 then
      LV_RETURN := LV_RETURN || LI_MINUTES || si_minute;
    end if;
    if LI_SECONDS != 0 then
      LV_RETURN := LV_RETURN || LI_SECONDS || si_sec;
    end if;
  
    RETURN LV_RETURN;
  END;
END FUN_GET_RUNTIME;
/



-- 4.7.24 version Script patch is as follows
DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM' AND COLUMN_NAME='IAUDITPASSTIME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM ADD IAUDITPASSTIME TIMESTAMP';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_DOUBLECHECK_WORKITEM_HIS' AND COLUMN_NAME='IAUDITPASSTIME';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_DOUBLECHECK_WORKITEM_HIS ADD IAUDITPASSTIME TIMESTAMP';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_INSTANCE_TEST' AND COLUMN_NAME='ITIMEOUT';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE_TEST  ADD ITIMEOUT NUMBER (1, 0) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='ISTARTUPTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE  ADD ISTARTUPTIME VARCHAR2 (255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_TMP_SERVICE' AND COLUMN_NAME='IORDER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_TMP_SERVICE  ADD IORDER NUMBER (19,0) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING where iid=8;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(8,''管理员'')';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AUDITING_PARAMS' AND COLUMN_NAME='IVALUE';
	IF LI_EXISTS =0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS ADD IVALUE VARCHAR2(4000)';
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AUDITING_PARAMS' AND COLUMN_NAME='IVALUE' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=4000;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS ADD DES VARCHAR(4000)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_AUDITING_PARAMS SET DES=IVALUE';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_AUDITING_PARAMS SET IVALUE=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS MODIFY  IVALUE VARCHAR2(4000)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_AUDITING_PARAMS SET IVALUE=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PARAMS DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;

	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_INSTANCE' AND COLUMN_NAME='ISYSDATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE  ADD ISYSDATE TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF; 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_INSTANCE_TEST' AND COLUMN_NAME='ISYSDATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE_TEST  ADD ISYSDATE TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF; 
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_INSTANCE' AND COLUMN_NAME='IPARAMETERS' and data_type='VARCHAR2' ;
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER  TABLE  IEAI_SCRIPT_INSTANCE  ADD  IPARAMETERS1  LONG';
		EXECUTE IMMEDIATE LS_SQL;
		LS_SQL := 'UPDATE  IEAI_SCRIPT_INSTANCE   SET  IPARAMETERS1= IPARAMETERS';
		EXECUTE IMMEDIATE LS_SQL;
		LS_SQL := 'ALTER  TABLE  IEAI_SCRIPT_INSTANCE   MODIFY(IPARAMETERS1  CLOB)';
		EXECUTE IMMEDIATE LS_SQL;
		LS_SQL := 'ALTER  TABLE  IEAI_SCRIPT_INSTANCE  DROP COLUMN  IPARAMETERS';
		EXECUTE IMMEDIATE LS_SQL;
		LS_SQL := 'ALTER  TABLE  IEAI_SCRIPT_INSTANCE  RENAME  COLUMN  IPARAMETERS1  TO  IPARAMETERS';
		EXECUTE IMMEDIATE LS_SQL;
		LS_SQL := 'ALTER INDEX PK_IEAI_SCRIPT_INSTANCE REBUILD';
		EXECUTE IMMEDIATE LS_SQL;
--		commit;
	END IF;
END;
/

DECLARE
	LS_SQL VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN 
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DOUBLECHECK_WORKITEM where  iauditpasstime is null and istate  not in(1,2,8,6)  and iitemtype=10;
	IF LI_EXISTS > 0 THEN
		LS_SQL := 'update IEAI_DOUBLECHECK_WORKITEM set iauditpasstime= icreatetime  where iauditpasstime is null and istate  not in(1,2,8,6)  and iitemtype=10';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

END;
/

create or replace view v_script_monitor_todotask as
	select te.taskid,
			  te.taskName,
			  te.taskType,
			  se.iid scriptiid,
			  se.iscriptuuid scriptuuid,
			  se.iservicesname serviceName,
			  se.iscripttype scriptType,
			   se.iisflow isflow,
			  te.iignore,
			  te.coatid,
			  te.status,
			  te.istate,
			  te.startUser,
			  te.execuser,
			  te.butterflyVersion,
			  te.iistimetask,
			  te.execStrategy,
			  te.createTime,
			  te.auditPassTime, 
			  te.starttime
	 from (select dw.iid as taskId,
		   '自定义运维'       taskType,
		   (select icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'fromId') scriptiid,
		   nvl(fl.iignore,0) iignore,
		   nvl(cc.iid,0) coatid,
		   case  cc.istate when 11 then '部分运行' else '未运行' end as status,
			nvl(cc.istate,0) istate,
		   u.ifullname as startUser,
		   (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) EXECUSER,
		   dc.ICOLVALUE as taskName,
		   dw.butterflyversion as butterflyVersion,
		   dw.iistimetask iistimetask,
		   case dw.iistimetask
			 when 0 then
			  1
			 when 2 then --定时
			  2
		   end as execStrategy,
		   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
		   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
		   case dw.iistimetask
			 when 0 then
				to_char(dw.iexectime, 'yyyy-mm-dd hh24:mi:ss')
			 when 2 then --定时
				 (select distinct icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'taskTimeForDispaly')
		   end  starttime
	  from IEAI_DOUBLECHECK_WORKITEM dw
	  left join IEAI_DOUBLECHECK_COLVALUE dc
		on dw.IID = dc.IWORKITEMID
	  left join ieai_user u
		on u.iloginname = dw.istartuser
	  left join IEAI_SCRIPTS_COAT cc
		on cc.iworkitemid = dw.iid
	  left join ieai_script_flow fl
		on fl.iid=cc.iflowid
	 where dc.ICOLHEADER = 'taskName'
	   and dw.iistimetask in (0, 2) --触发，定时
	   and (dw.istate = 10 or
		   (dw.istate = 5 and cc.istate = 11 AND CC.FLAG = 1))
	   and dw.IITEMTYPE = 10
	   and dw.IAUDITYPE = 1) te
	  left join ieai_script_services se
		on se.iid=te.scriptiid
/
create or replace view v_script_monitor_runningtask as
	select te.taskid,
			  te.taskName,
			  te.taskType,
			  se.iid scriptiid,
			  se.iscriptuuid scriptuuid,
			  se.iservicesname serviceName,
			  se.iscripttype scriptType,
			  se.iisflow isflow,
			  te.iignore,
			  te.flowid,
			  te.coatid,
			  te.status,
			  te.istate,
			  te.startUser,
			  te.performUser,
			  te.butterflyVersion,
			  te.iistimetask,
			  te.execStrategy,
			  te.createTime,
			  te.auditPassTime, 
			  te.starttime,
			  te.runTime
	 from (select dw.iid as taskId,
		   '自定义运维'       taskType,
		   (select icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'fromId') scriptiid,
		   nvl(fl.iignore,0) iignore,
		   nvl(cc.iid,0) coatid,
		   nvl(fl.iid,0) flowid,
		   case  cc.istate when 10 then '运行' when 30 then '异常' when 60 then '终止' end as status,
			nvl(cc.istate,0) istate,
		   u.ifullname as startUser,
			(SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
		   dc.ICOLVALUE as taskName,
		   dw.butterflyversion as butterflyVersion,
		   dw.iistimetask iistimetask,
		   case dw.iistimetask
			 when 0 then
			  1
			 when 2 then --定时
			  2
		   end as execStrategy,
		   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
		   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
		   FUN_GET_DATE_STRING(fl.istarttime,8,'yyyy-mm-dd hh24:mi:ss') as starttime,
		   FUN_GET_RUNTIME(fl.istarttime,fl.iendtime) as runTime
	  from IEAI_DOUBLECHECK_WORKITEM dw
	  left join IEAI_DOUBLECHECK_COLVALUE dc
		on dw.IID = dc.IWORKITEMID
	  left join ieai_user u
		on u.iloginname = dw.istartuser
	  left join IEAI_SCRIPTS_COAT cc
		on cc.iworkitemid = dw.iid
	  left join ieai_script_flow fl
		on fl.iid=cc.iflowid
	 where dc.ICOLHEADER = 'taskName'
	   and dw.iistimetask in (0, 2) --触发，定时
	   and  dw.istate in(5,6)
	   and   fl.istart_type= 0
	   and fl.istatus in(10,30) --运行，部分运行 ，异常
	   and dw.IITEMTYPE = 10
	   and dw.IAUDITYPE = 1) te
	  left join ieai_script_services se
		on se.iid=te.scriptiid
	union all
	select te.taskid,
			  te.taskName,
			  te.taskType,
			  se.iid scriptiid,
			  se.iscriptuuid scriptuuid,
			  se.iservicesname serviceName,
			  se.iscripttype scriptType,
			  se.iisflow isflow,
			  te.iignore,
			  te.coatid,
			  te.flowid,
			  te.status,
			  te.istate,
			  te.startUser,
			  te.performUser,
			  te.butterflyVersion,
			  te.iistimetask,
			  te.execStrategy,
			  te.createTime,
			  te.auditPassTime, 
			  te.starttime,
			  te.runTime
	 from (select dw.iid as taskId,
		   '自定义运维'       taskType,
		   (select icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'fromId') scriptiid,
		   nvl(fl.iignore,0) iignore,
		   nvl(cc.iid,0) coatid,
		   nvl(fl.iid,0) flowid,
		   case  cc.istate when 10 then '运行' when 30 then '异常' when 60 then '终止' end as status,
		   nvl(cc.istate,0) istate,
		   u.ifullname as startUser,
			(SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
		   dc.ICOLVALUE as taskName,
		   dw.butterflyversion as butterflyVersion,
		   dw.iistimetask iistimetask,
		   case dw.iistimetask
			 when 0 then
			  1
			 when 1 then --周期
			  3
		   end as execStrategy,
		   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
		   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
			FUN_GET_DATE_STRING(fl.istarttime,8,'yyyy-mm-dd hh24:mi:ss') as starttime,
		   FUN_GET_RUNTIME(fl.istarttime,fl.iendtime) as runTime
	   from IEAI_DOUBLECHECK_WORKITEM dw
	  left join IEAI_DOUBLECHECK_COLVALUE dc
		on dw.IID = dc.IWORKITEMID
	  left join ieai_user u
		on u.iloginname = dw.istartuser
	  left join (select dw.iid as itaskid, max(co.iid) coatid
				   from IEAI_DOUBLECHECK_WORKITEM dw
				   left join IEAI_SCRIPTS_COAT co
					 on co.iworkitemid = dw.iid
				  where dw.iistimetask = 1 --周期
					and dw.istate =12
					and co.istate in(10,30)
					and dw.IITEMTYPE = 10
					and dw.IAUDITYPE = 1
				  group by dw.iid) coo
		on coo.itaskid = dw.iid
	  left join IEAI_SCRIPTS_COAT cc
		on cc.iworkitemid = dw.iid
	   and cc.iid = coo.coatid
	  left join ieai_script_flow fl
		on fl.iid=cc.iflowid
	   left join ieai_script_services se
		on se.iscriptuuid=fl.imxserviceid
	 where dc.ICOLHEADER = 'taskName'
	   and dw.iistimetask = 1 --周期
	   and dw.istate =12
	   and   fl.istart_type= 0
	   and fl.istatus in(10,30)
	   and dw.IITEMTYPE = 10
	   and dw.IAUDITYPE = 1) te
	  left join ieai_script_services se
		on se.iid=te.scriptiid
/
create or replace view v_script_monitor_historytask as
	select te.taskid,
			  te.taskName,
			  te.taskType,
			  se.iid scriptiid,
			  se.iscriptuuid scriptuuid,
			  se.iservicesname serviceName,
			  se.iscripttype scriptType,
			  se.iisflow isflow,
			  te.iignore,
			  te.coatid,
			  te.status,
			  te.istate,
			  te.startUser,
			   te.performUser,
			  te.butterflyVersion,
			  te.iistimetask,
			  te.execStrategy,
			  te.createTime,
			  te.auditPassTime, 
			  te.starttime,
			  te.endTime,
			  te.runTime
	 from (select dw.iid as taskId,
		   '自定义运维'       taskType,
		   (select icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'fromId') scriptiid,
		   nvl(fl.iignore,0) iignore,
		   nvl(cc.iid,0) coatid,
			case  cc.istate when 20 then '完成' when 5 then '忽略' when 60 then '终止' end as status,
			nvl(cc.istate,0) istate,
		   u.ifullname as startUser,
			 (SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
		   dc.ICOLVALUE as taskName,
		   dw.butterflyversion as butterflyVersion,
		   dw.iistimetask iistimetask,
		   case dw.iistimetask
			 when 0 then
			  1
			 when 2 then --定时
			  2
		   end as execStrategy,
		   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
		   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
			FUN_GET_DATE_STRING(fl.istarttime,8,'yyyy-mm-dd hh24:mi:ss') as starttime,
		   FUN_GET_DATE_STRING(fl.iendtime,8,'yyyy-mm-dd hh24:mi:ss') as endTime,
		  FUN_GET_RUNTIME(fl.istarttime,fl.iendtime) as runTime
	  from IEAI_DOUBLECHECK_WORKITEM dw
	  left join IEAI_DOUBLECHECK_COLVALUE dc
		on dw.IID = dc.IWORKITEMID
	  left join ieai_user u
		on u.iloginname = dw.istartuser
	  left join IEAI_SCRIPTS_COAT cc
		on cc.iworkitemid = dw.iid
	  left join ieai_script_flow fl
		on fl.iid=cc.iflowid
	 where dc.ICOLHEADER = 'taskName'
	   and dw.iistimetask in (0, 2) --触发，定时
	   and  dw.istate in(5,6)
	   and fl.istatus in(20,60,5) --完成，终止，忽略
	   and dw.IITEMTYPE = 10
	   and fl.istart_type= 0
	   and dw.IAUDITYPE = 1) te
	  left join ieai_script_services se
		on se.iid=te.scriptiid
	union all
	select te.taskid,
			  te.taskName,
			  te.taskType,
			  se.iid scriptiid,
			  se.iscriptuuid scriptuuid,
			  se.iservicesname serviceName,
			  se.iscripttype scriptType,
			  se.iisflow isflow,
			  te.iignore,
			  te.coatid,
			  te.status,
			  te.istate,
			  te.startUser,
			   te.performUser,
			  te.butterflyVersion,
			  te.iistimetask,
			  te.execStrategy,
			  te.createTime,
			  te.auditPassTime, 
			  te.starttime,
			  te.endTime,
			  te.runTime
	 from (select dw.iid as taskId,
		   '自定义运维'       taskType,
		   (select icolvalue from IEAI_DOUBLECHECK_COLVALUE co1 where co1.iworkitemid=dw.iid and co1.ICOLHEADER = 'fromId') scriptiid,
		   nvl(fl.iignore,0) iignore,
		   nvl(cc.iid,0) coatid,
		   case  cc.istate when 20 then '完成' when 5 then '忽略' when 60 then '终止' end as status,
		   nvl(cc.istate,0) istate,
		   u.ifullname as startUser,
			(SELECT US.IFULLNAME FROM IEAI_USER US WHERE US.ILOGINNAME=DW.PERFORMUSER) performUser,
		   dc.ICOLVALUE as taskName,
		   dw.butterflyversion as butterflyVersion,
		   dw.iistimetask iistimetask,
		   case dw.iistimetask
			 when 0 then
			  1
			 when 1 then --周期
			  3
		   end as execStrategy,
		   to_char(dw.icreateTime, 'yyyy-mm-dd hh24:mi:ss') as createTime,
		   to_char(dw.IAUDITPASSTIME, 'yyyy-mm-dd hh24:mi:ss') as auditPassTime,
		   FUN_GET_DATE_STRING(fl.istarttime,8,'yyyy-mm-dd hh24:mi:ss') as starttime,
		   FUN_GET_DATE_STRING(fl.iendtime,8,'yyyy-mm-dd hh24:mi:ss') as endTime,
		   FUN_GET_RUNTIME(fl.istarttime,fl.iendtime) as runTime
	   from IEAI_DOUBLECHECK_WORKITEM dw
	  left join IEAI_DOUBLECHECK_COLVALUE dc
		on dw.IID = dc.IWORKITEMID
	  left join ieai_user u
		on u.iloginname = dw.istartuser
	  left join (select dw.iid as itaskid, max(co.iid) coatid
				   from IEAI_DOUBLECHECK_WORKITEM dw
				   left join IEAI_SCRIPTS_COAT co
					 on co.iworkitemid = dw.iid
				  where dw.iistimetask = 1 --周期
					and dw.istate =12
					and co.istate in(20,60,5)--完成，终止，忽略
					and dw.IITEMTYPE = 10
					and dw.IAUDITYPE = 1
				  group by dw.iid) coo
		on coo.itaskid = dw.iid
	  left join IEAI_SCRIPTS_COAT cc
		on cc.iworkitemid = dw.iid
	   and cc.iid = coo.coatid
	  left join ieai_script_flow fl
		on fl.iid=cc.iflowid
	   left join ieai_script_services se
		on se.iscriptuuid=fl.imxserviceid
	 where dc.ICOLHEADER = 'taskName'
	   and dw.iistimetask = 1 --周期
	   and dw.istate =12
	   and fl.istatus in(20,60,5)--完成，终止，忽略
	   and dw.IITEMTYPE = 10
	   and fl.istart_type= 0
	   and dw.IAUDITYPE = 1) te
	  left join ieai_script_services se
		on se.iid=te.scriptiid
/
-- 4.7.25 version Script patch is as follows
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IQDBUSER';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IQDBUSER VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IQDBPWD';
	IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IQDBPWD VARCHAR2(255)';
        EXECUTE IMMEDIATE LS_SQL;
	END IF;
	 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_LABLEANDCATE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	   LS_SQL := 'CREATE TABLE IEAI_SCRIPT_LABLEANDCATE( IID NUMBER (19,0) NOT NULL,   IFROM INTEGER,  ILABLEVALUE VARCHAR2(100),  ILABLERELATIONID NUMBER(19,0) NOT NULL,     ICATAVALUE VARCHAR2(100),   ICATARELATIONID NUMBER(19,0) NOT NULL,  ISERVICEID NUMBER(19,0) NOT NULL,   CONSTRAINT PK_IEAI_SCRIPT_LABLEANDCATE PRIMARY KEY(IID))';
	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_TOPRODUCT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_TOPRODUCT (IID NUMBER(19) NOT NULL,ICONTENT BLOB NOT NULL,IDATETIME NUMBER(19) NOT NULL,IUSERID NUMBER(19) NOT NULL,ISIZE NUMBER(19) NOT NULL,ISCRIPTUSERID NUMBER(19) NOT NULL,FILENAME VARCHAR2(255) NOT NULL,IDESCRIPTION VARCHAR2(255) NOT NULL,CONSTRAINT PK_IEAI_SCRIPT_TOPRODUCT PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_BS_ATOMIC' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_BS_ATOMIC (IID NUMBER(19) NOT NULL,IATOMICBSNAME VARCHAR2(255) NOT NULL,IUPERID NUMBER(19),IBSID NUMBER(19),IBSTYPEID NUMBER(19),IISATOMIC NUMBER(1), CONSTRAINT PK_IEAI_SCRIPT_BS_ATOMIC PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DBAAS_KEYWORD' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
   		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DBAAS_KEYWORD (IID NUMBER(19) NOT NULL,INAME VARCHAR2(255),IDESC VARCHAR2(255),ITYPE NUMBER(19), CONSTRAINT PK_IEAI_SCRIPT_DBAAS_KEYWORD PRIMARY KEY (IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_OPERRECORD' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_SCRIPT_OPERRECORD (IID NUMBER(19) NOT NULL,IRESID NUMBER(19),ISCRIPTUUID VARCHAR2(255),IOUTCONTENT CLOB,IOPERUSER NUMBER(19),ICHECKUSER NUMBER(19),ISTATUS NUMBER(1),IOPERTIME NUMBER(19),IRESERVEPLANID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPT_OPERRECORD PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;

	

END;
/



DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEMPLATE_ANALY' AND COLUMN_NAME='IPARAMETER' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=4000;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEMPLATE_ANALY ADD DES VARCHAR(4000)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TEMPLATE_ANALY SET DES=IPARAMETER';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TEMPLATE_ANALY SET IPARAMETER=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEMPLATE_ANALY MODIFY  IPARAMETER VARCHAR(4000)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_TEMPLATE_ANALY SET IPARAMETER=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEMPLATE_ANALY DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;

END;
/


-- 4.7.26 version Script patch is as follows

DECLARE
     LI_SIGN SMALLINT;
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	
	SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_ST_SRTUUID' AND OBJECT_TYPE = 'INDEX';
	IF	LI_SIGN = 0 THEN
		LS_SQL := 'create index IDX_ST_SRTUUID ON IEAI_SCRIPT_TEST(ISCRIPTUUID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SC_SU_FROM_FLAG' AND OBJECT_TYPE = 'INDEX';
	IF	LI_SIGN = 0 THEN
		LS_SQL := 'create index IDX_SC_SU_FROM_FLAG ON IEAI_SCRIPTS_COAT(ISTART_USER,IFROM,FLAG)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_CHECK_RESULT' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_RESOURCE_CHECK_RESULT( IID NUMBER(19,0) NOT NULL, IREID  NUMBER(19,0), ICHECKTIME NUMBER(19,0),ICHECKRESULT  NUMBER(1,0),IMESS  CLOB, constraint PK_IEAI_RESOURCE_CHECK_RESULT PRIMARY KEY (IID) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SYSTEM_CLASS_ROLE' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := ' CREATE TABLE IEAI_SYSTEM_CLASS_ROLE(IROLEID DECIMAL(19,0),ICLASSID DECIMAL(19,0),ITYPE INTEGER)';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RULES' AND COLUMN_NAME='IEXPRESSION';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RULES ADD IEXPRESSION VARCHAR(5) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	

	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='IGROUPS';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM MODIFY IGROUPS NUMBER(19) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_USER_GROUP' AND COLUMN_NAME='IUSERID';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'ALTER TABLE IEAI_USER_GROUP MODIFY IUSERID NUMBER(19) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS  FROM USER_CONSTRAINTS T WHERE T.TABLE_NAME = UPPER('IEAI_TMP_S_R') AND T.CONSTRAINT_TYPE = 'P' AND T.CONSTRAINT_NAME = 'PK_IEAI_IEAI_TMP_S_R';
	IF LI_EXISTS = 1 THEN
		LS_SQL := 'alter table IEAI_TMP_S_R drop constraint PK_IEAI_IEAI_TMP_S_R';
		EXECUTE IMMEDIATE LS_SQL;
		LS_SQL := 'alter table IEAI_TMP_S_R add constraint PK_IEAI_TMP_S_R';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_FLOW_TEMPLATE' AND COLUMN_NAME='DEVICETYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_FLOW_TEMPLATE ADD DEVICETYPE NUMBER(1) DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	  	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME = 'IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME = 'IGROUPS';
  	IF  LI_EXISTS = 0  THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_BUSINESS_SYSTEM ADD  (IGROUPS  DECIMAL(19,0) )';
	    EXECUTE IMMEDIATE LS_SQL;
  	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AGENT_GROUP_BUSINE' AND COLUMN_NAME='IEXECUSERNAME';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP_BUSINE ADD IEXECUSERNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AGENT_GROUP' AND COLUMN_NAME='IEXECUSERNAME';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AGENT_GROUP ADD IEXECUSERNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ANALYSE_RESULT' AND COLUMN_NAME='IFLOWID';
		IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ANALYSE_RESULT ADD IFLOWID NUMBER(19,0)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_OPER_PARAMS' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_SCRIPT_OPER_PARAMS (IID NUMBER(19) NOT NULL,IOPERID NUMBER(19),IPARAM_TYPE VARCHAR2(500),IPARAM_DEFAULT_VALUE VARCHAR2(4000),IPARAM_DESC VARCHAR2(500),IPARAM_ORDER NUMBER(10),CONSTRAINT PK_IEAI_SCRIPT_OPER_PARAMS PRIMARY KEY (IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_OPER_RESULT' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_SCRIPT_OPER_RESULT (IID NUMBER(19) NOT NULL,IOPERID NUMBER(19) ,IRESULT CLOB ,IKEY NUMBER(10),CONSTRAINT PK_IEAI_SCRIPT_OPER_RESULT PRIMARY KEY (IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_RESERVE_PLAN' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_RESERVE_PLAN (IID NUMBER(19) NOT NULL,INAME VARCHAR2(100),ICREATEUSER VARCHAR2(100), ICREATETIME NUMBER(19),IMODIFYUSER VARCHAR2(100),IMODIFYTIME NUMBER(19), IPARENTID NUMBER(19),ISERVICEID NUMBER(19) DEFAULT 0,CONSTRAINT PK_IEAI_SCRIPT_RESERVE_PLAN PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_RESERVE_PLAN' AND COLUMN_NAME='IDBTYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RESERVE_PLAN ADD IDBTYPE NUMBER(19) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_RESERVE_PLAN' AND COLUMN_NAME='IORDER';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_RESERVE_PLAN ADD IORDER NUMBER(19)  ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_CATALOING' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_CATALOING (IID NUMBER(19) NOT NULL,INAME VARCHAR2(100),ICREATEUSER VARCHAR2(100), ICREATETIME NUMBER(19),IMODIFYUSER VARCHAR2(100),IMODIFYTIME NUMBER(19), IPARENTID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPT_CATALOING PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_LABEL' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_SCRIPT_LABEL (IID NUMBER(19) NOT NULL,INAME VARCHAR2(100),ICREATEUSER  VARCHAR2(100), ICREATETIME NUMBER(19),IMODIFYUSER VARCHAR2(100),IMODIFYTIME NUMBER(19), IPARENTID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPT_LABEL PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
		
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID = 1;
	IF LI_EXISTS = 1 THEN
    	LS_SQL := 'UPDATE IEAI_DATABASE_TYPE SET IURL = ''jdbc:oracle:thin:@//{IP}:{PORT}/{NAME}'' ,IDRIVER=''oracle.jdbc.driver.OracleDriver'' WHERE IID = 1';
    	EXECUTE IMMEDIATE LS_SQL;
    END IF;
      
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID = 2;
	IF LI_EXISTS = 1 THEN
    	LS_SQL := 'UPDATE IEAI_DATABASE_TYPE SET IURL = ''jdbc:db2://{IP}:{PORT}/{NAME}'' ,IDRIVER=''com.ibm.db2.jcc.DB2Driver'' WHERE IID = 2';
    	EXECUTE IMMEDIATE LS_SQL;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DATABASE_TYPE WHERE IID = 3;
	IF LI_EXISTS = 1 THEN
    	LS_SQL := 'UPDATE IEAI_DATABASE_TYPE SET IURL = ''jdbc:mysql://{IP}:{PORT}/{NAME}?characterEncoding=utf8' || CHR(38) || 'zeroDateTimeBehavior=convertToNull' || CHR(38) || 'useSSL=false' || CHR(38) || 'autoReconnect=true' || CHR(38) || 'failOverReadOnly=false'' ,IDRIVER=''com.mysql.jdbc.Driver'' WHERE IID = 3';
    	EXECUTE IMMEDIATE LS_SQL;
    END IF;
    
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING where iid=10;
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(10,''脚本服务化高权限'')';
		EXECUTE IMMEDIATE LS_SQL;
		COMMIT;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING  WHERE IID=9;
	IF  LI_EXISTS = 0 THEN
		LS_SQL :='INSERT INTO IEAI_SCRIPT_AUDITING (IID, INAME) VALUES (9, ''查询脚本服务化复核任务查询的所有数据 '')';
		EXECUTE IMMEDIATE LS_SQL;
	END  IF;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AUDITING_PERM' AND COLUMN_NAME='IAUDITINGCODE' AND DATA_TYPE='NUMBER' AND DATA_LENGTH=2;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PERM ADD DES NUMBER(2)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_AUDITING_PERM SET DES=IAUDITINGCODE';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_AUDITING_PERM SET IAUDITINGCODE=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PERM MODIFY  IAUDITINGCODE NUMBER(2)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_SCRIPT_AUDITING_PERM SET IAUDITINGCODE=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_PERM DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_APPLY' AND COLUMN_NAME='IRMANAGE_ID' AND DATA_TYPE='NUMBER' AND DATA_LENGTH=19;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY ADD DES NUMBER(19)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE_APPLY SET DES=IRMANAGE_ID';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE_APPLY SET IRMANAGE_ID=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY MODIFY  IRMANAGE_ID NUMBER(19,0)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE_APPLY SET IRMANAGE_ID=DES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_APPLY DROP COLUMN DES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;


  SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_SINSTANCE_MAINID' AND OBJECT_TYPE = 'INDEX';
	IF	LI_SIGN = 0 THEN
		LS_SQL := 'create index IDX_SINSTANCE_MAINID on IEAI_SCRIPT_INSTANCE (IMAINID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SERVICE_MONITOR' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_SERVICE_MONITOR( IID NUMBER(19) NOT NULL,ICOLUMNNAME VARCHAR2(255) NOT NULL,ISYMBOL VARCHAR2(255) NOT NULL,	ITHRESHOLD  VARCHAR2(255) NOT NULL,ITYPE   NUMBER(19) NOT NULL, ISERVICEID   VARCHAR2(255) NOT NULL,ISERVICEUUID   VARCHAR2(255),CONSTRAINT PK_IEAI_SERVICE_MONITOR PRIMARY KEY (IID))';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SERVICE_MONITOR_BIND' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_SERVICE_MONITOR_BIND(IID NUMBER(19) NOT NULL,ISERVICEUUID VARCHAR2(36)  NOT NULL,	IMONITORID NUMBER(19) NOT NULL,CONSTRAINT PK_IEAI_SERVICE_MONITOR_BIND PRIMARY KEY(IID))';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
  		
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN
	
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPER_RESULT' AND COLUMN_NAME='IKEY';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD IKEY NUMBER(10)';
    EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IPLATEFORM';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IPLATEFORM VARCHAR(100)';
    EXECUTE IMMEDIATE LS_SQL;
END IF;
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IRUNDAY';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IRUNDAY VARCHAR(25)';
    EXECUTE IMMEDIATE LS_SQL;
END IF;	

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_FLOW' AND COLUMN_NAME='IIEXISTSCFG';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_FLOW ADD IIEXISTSCFG INTEGER DEFAULT 0';
    EXECUTE IMMEDIATE LS_SQL;
END IF;	
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_FLOW' AND COLUMN_NAME='IISWARNDBMONITOR';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_FLOW ADD IISWARNDBMONITOR INTEGER DEFAULT 0';
    EXECUTE IMMEDIATE LS_SQL;
END IF;	
	  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEIA_SCRIPT_SERVICE_RELATION' AND OBJECT_TYPE = 'TABLE';
  IF LI_EXISTS = 0 THEN
      LS_SQL := 'CREATE TABLE IEIA_SCRIPT_SERVICE_RELATION(IID NUMERIC(19,0) NOT NULL,IUUID VARCHAR(255),IFLOWID NUMERIC(19,0)NOT NULL,ISERVICENO VARCHAR (255),IRESID NUMERIC(19,0), ISWARN INTEGER DEFAULT 0,CONSTRAINT PK_IEIA_SCR_SER_REL PRIMARY KEY(IID))';
    EXECUTE IMMEDIATE LS_SQL;
  END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ASM_RESULT' AND OBJECT_TYPE = 'TABLE';
 		 IF LI_EXISTS = 0 THEN
	     LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ASM_RESULT(IID  NUMBER(19) NOT NULL PRIMARY KEY,IRESID NUMBER(19),ITOTAL VARCHAR2(255),IFREE  VARCHAR2(255),IUSABLE VARCHAR2(255),IDG_NAME VARCHAR2(255),IDG_TYPE VARCHAR2(255),ID_NUMBER NUMBER(19),IDISK_NAME VARCHAR2(255),ISTATE NUMBER(19),ID_TOTAL VARCHAR2(255),ID_FREE VARCHAR2(255),ITIME  NUMBER(19))';
	EXECUTE IMMEDIATE LS_SQL;
  	END IF;
END;
/
-- 4.7.27 new
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ASM' AND COLUMN_NAME='IRACPORT';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ASM  ADD IRACPORT VARCHAR2(255)';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ASM' AND COLUMN_NAME='IRACPASSWORD';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ASM  ADD IRACPASSWORD VARCHAR2(255)';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ASM' AND COLUMN_NAME='IRACSID';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ASM  ADD IRACSID VARCHAR2(255)';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_LABLE_RELATION' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_SCRIPT_LABLE_RELATION (IID NUMBER(19) NOT NULL,ILABLEID NUMBER(19),ISERVICEID NUMBER(19) , CONSTRAINT PK_IEAI_SCRIPT_LABLE_RELATION PRIMARY KEY(IID))';
    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPERRECORD' AND COLUMN_NAME='ISCRIPTCONTENT';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPERRECORD  ADD ISCRIPTCONTENT CLOB';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPTS_UNMONITORED' AND OBJECT_TYPE = 'TABLE';
  	IF LI_EXISTS = 0 THEN
	LS_SQL := 'CREATE TABLE IEAI_SCRIPTS_UNMONITORED(IID NUMBER(19),IRESID NUMBER(19),IFLOWID NUMBER(19),ICOATID NUMBER(19),ISCRIPTUUID VARCHAR2(255),IPARENTFLOWID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPTS_UNMONITORED PRIMARY KEY(IID))';
	EXECUTE IMMEDIATE LS_SQL;
  	END IF;	
	
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='IRACGROUP';
	IF LI_EXISTS = 0 THEN
    LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD IRACGROUP VARCHAR2(255)';
    EXECUTE IMMEDIATE LS_SQL;
END IF;
END;
/

-- 4.7.28 version Script patch is as follows
DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TOPRODUCT' AND COLUMN_NAME='IUUID';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TOPRODUCT ADD IUUID VARCHAR2(100)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPER_RESULT' AND COLUMN_NAME='ISQLKEY';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD ISQLKEY VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPER_RESULT' AND COLUMN_NAME='IKEY';
	IF LI_EXISTS = 1 THEN
    	LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPER_RESULT DROP COLUMN IKEY';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/
DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DANGER_CMD' AND COLUMN_NAME='CHECKTYPE';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DANGER_CMD ADD CHECKTYPE VARCHAR2(2)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_AUDITING_DANGER' AND OBJECT_TYPE = 'TABLE';
  IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_AUDITING_DANGER( IID NUMBER(19) NOT NULL,ISCRIPTNAME  VARCHAR2(255),ITASKNAME VARCHAR2(255),ICOMMAND VARCHAR2(255),IIP VARCHAR2(255),IEXECUSER VARCHAR2(255),IEXECTIME TIMESTAMP(6),IBUTTERFLYVERSION VARCHAR2(255),ITASKTYPE NUMBER(19),CONSTRAINT PK_IEAI_SCRIPT_AUDITING_DANGER PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
END IF;
END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
	
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPTS_RELATON' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_SCRIPTS_RELATON (IID NUMBER(19)  NOT NULL,INAME  VARCHAR2(255), ISERVICEID VARCHAR2(255),IPARENTID NUMBER(19),IPARENTSERVICEID VARCHAR2(255),IROOTID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPTS_RELATON PRIMARY KEY (IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPTS_REL_COLUMN' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_SCRIPTS_REL_COLUMN (IID NUMBER(19)  NOT NULL, IRELATIONID NUMBER(19),IPARAMORDER NUMBER(10), ISERVICEID VARCHAR2(255),IRELSERVICEID VARCHAR2(255),IRELCOLUMN VARCHAR2(255),CONSTRAINT PK_IEAI_SCRIPTS_REL_COLUMN PRIMARY KEY (IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPERRECORD' AND COLUMN_NAME='IOPERKEY';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPERRECORD ADD IOPERKEY NUMBER(19)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPERRECORD' AND COLUMN_NAME='ISERVICEID';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPERRECORD ADD ISERVICEID VARCHAR2(255)';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
    
END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DBAAS_CENTER_DBINFO' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := '  CREATE TABLE IEAI_DBAAS_CENTER_DBINFO( IID DECIMAL(19,0) NOT NULL, ISYSNAME VARCHAR2(255), ISYSNAME_VERSION VARCHAR2(25), IDBUSERNAME VARCHAR2(50), IDBPAWD VARCHAR2(50), ICREATTIME DECIMAL(19,0), ICREATUSERID DECIMAL(19,0), ISTATE DECIMAL(2) DEFAULT 0, CONSTRAINT PK_IEAI_DBAAS_CENTER_DBINFO PRIMARY KEY(IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
    
END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
	

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_LASTTABLENAME' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	    LS_SQL := 'CREATE TABLE IEAI_SCRIPT_LASTTABLENAME (IID NUMBER(19)  NOT NULL,ISCRIPTUUID VARCHAR2(255),ILASTOUT_TABLENAME VARCHAR2(255),CONSTRAINT PK_IEAI_SCRIPT_LASTTABLENAME PRIMARY KEY (IID))';
	    EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='DBNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD DBNAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='INSTANCE_NAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD INSTANCE_NAME VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='NLS_CHARACTERSET';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD NLS_CHARACTERSET VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='NLS_NCHAR_CHARACTERSET';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD NLS_NCHAR_CHARACTERSET VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
    
END;
/


DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DBAAS_CHECK_RULE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_DBAAS_CHECK_RULE(IID NUMBER(19) NOT NULL,ICODE VARCHAR2(255) NULL,
		IDES VARCHAR2(255) NULL,CONSTRAINT PK_IEAI_DBAAS_CHECK_RULE PRIMARY KEY(IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
    
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_DBAAS_LOCAL_RESOURCE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
    	LS_SQL := 'CREATE TABLE IEAI_DBAAS_LOCAL_RESOURCE(IID NUMBER(19) NOT NULL,INAME VARCHAR2(255) NULL,IRSTYPE VARCHAR2(100) NULL,
		IIP VARCHAR2(15) NULL,IBUSINESSID NUMBER(19) NOT NULL,IBUSINESSNAME VARCHAR2(255) NULL,IDBUSER VARCHAR2(255) NULL,
		IDBPWD VARCHAR2(50) NULL,IAPPLYUSER VARCHAR2(255) NULL,IUSERID NUMBER(19) NULL,ISID VARCHAR2(255) NULL,
		IDBPORT NUMBER(10) NULL,IVERSION VARCHAR2(255) NULL,ICREATETIME NUMBER(19) NULL,ISYSUSESR VARCHAR2(255) NULL,
		ISYSPWD VARCHAR2(50) NULL,ISYSPORT NUMBER(10) NULL,CONSTRAINT PK_IEAI_DBAAS_LOCAL_RESOURCE PRIMARY KEY(IID))';
    	EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	
END;
/

-- 4.7.29 version Script patch is as follows
DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_UPDATE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_RESOURCE_UPDATE(IID DECIMAL(19,0) NOT NULL,IOPERID DECIMAL(19,0) NOT NULL,IRESID DECIMAL(19,0) NOT NULL,ISTATUS INTEGER DEFAULT 0,CONSTRAINT PK_IEAI_RESOURCE_UPDATE  PRIMARY KEY (IID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_UPDATE_MSG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
	  LS_SQL := 'CREATE TABLE IEAI_RESOURCE_UPDATE_MSG(IID DECIMAL(19,0) NOT NULL,IOPERID DECIMAL(19,0) NOT NULL,IMESS varchar2(2000),CONSTRAINT PK_IEAI_RESOURCE_UPDATE_MSG  PRIMARY KEY (IID))'; 
	  EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_REL_BUSINESS' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_RESOURCE_REL_BUSINESS(IRESID DECIMAL(19,0) NOT NULL,ISYSID DECIMAL(19,0) NOT NULL,PRIMARY KEY(IRESID,ISYSID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE_MANAGE' AND COLUMN_NAME='INAME' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=25  ;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE ADD TMPDES VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE_MANAGE SET TMPDES=INAME';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE_MANAGE SET INAME=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE MODIFY  INAME VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE_MANAGE SET INAME=TMPDES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE_MANAGE DROP COLUMN TMPDES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;
					SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RESOURCE' AND COLUMN_NAME='INAME' AND DATA_TYPE='VARCHAR2' AND DATA_LENGTH=25  ;
	IF LI_EXISTS = 0 THEN
	     LS_SQL := 'ALTER TABLE IEAI_RESOURCE ADD TMPDES VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE SET TMPDES=INAME';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE SET INAME=NULL';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE MODIFY  INAME VARCHAR(255)';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'UPDATE IEAI_RESOURCE SET INAME=TMPDES';
        EXECUTE IMMEDIATE LS_SQL;
        LS_SQL := 'ALTER TABLE IEAI_RESOURCE DROP COLUMN TMPDES';
		EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ISSUE_CONFIG' AND COLUMN_NAME='PROTOCOL';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_ISSUE_CONFIG ADD PROTOCOL VARCHAR2(5) DEFAULT ''FTP''';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_F_EXTRACT_CONFIG' AND COLUMN_NAME='PROTOCOL';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_F_EXTRACT_CONFIG ADD PROTOCOL VARCHAR2(5) DEFAULT ''FTP''';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_EDIT_TEMPLATE' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_EDIT_TEMPLATE (IID NUMBER(19,0) NOT NULL,ICONTENT CLOB NOT NULL,ITYPE VARCHAR2(255) NOT NULL,INAME VARCHAR2(255) NOT NULL,ICREATEUSERID NUMBER(19,0) NOT NULL,ISCRIPTTYPE VARCHAR2(10) NOT NULL,CONSTRAINT PK_IEAI_SCRIPT_EDIT_TEMPLATE PRIMARY KEY(IID))';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
  	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BILOCALCHECK_DATA' AND OBJECT_TYPE = 'TABLE';
	 IF LI_EXISTS = 0 THEN
        LS_SQL := ' CREATE TABLE IEAI_BILOCALCHECK_DATA (IID NUMBER(19) NOT NULL, VERSIONSTATE VARCHAR2(16),IWORKITEMID NUMBER(19) ,STARTTIME NUMBER(19), ENDTIME NUMBER(19),TASK_TYPE NUMBER(2),CONSTRAINT PK_IEAI_BILOCALCHECK_DATA PRIMARY KEY (IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
END;
/

-- 8.0 version Script patch is as follows
DECLARE
		LI_SIGN SMALLINT;
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AUDITING_IPS' AND COLUMN_NAME='ISHUTDOWNUSERID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_IPS ADD ISHUTDOWNUSERID NUMBER(19) DEFAULT -1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IMPAGENTIP' AND COLUMN_NAME='ISHUTDOWNUSERNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE TMP_IMPAGENTIP ADD ISHUTDOWNUSERNAME VARCHAR2(100)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='ISAUTOSUB';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD (ISAUTOSUB NUMBER(1,0))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT	SIGN(COUNT(*)) INTO LI_SIGN FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCRIPT_FLOW' AND OBJECT_TYPE = 'INDEX';
	IF	LI_SIGN = 0 THEN
		LS_SQL := 'create index IDX_IEAI_SCRIPT_FLOW on IEAI_SCRIPT_FLOW (IWORKITEMID)';
		EXECUTE IMMEDIATE LS_SQL;
	END	IF;
END;
/

create or replace view v_script_shutdownagentlist as
select ips.iworkitemid,
       nvl(ca.iid, 0) coatid,
       ips.iid ipsiid,
       agi.iagentinfo_id agentid,
       agi.iagent_name as iagentname,
       agi.iagent_ip iagentip,
       agi.iagent_port agentport,
       agi.icom_name icomname,
       col.icolvalue as taskName,
       ins.iid as ins_iid,
       ins.istate ins_state,
       ins.iparameters,
       ins.istarttime,
       ins.iendtime,
       w.performuser,
       w.istartuser,
       w.butterflyversion,
       w.istate,
       (select c.icolvalue
          from ieai_doublecheck_colvalue c
         where c.iworkitemid = w.iid
           and c.icolheader = 'shutDownUUID') shutdownuuid,
       ips.ishutdownuserid
  from IEAI_SCRIPT_AUDITING_ips ips
  left join ieai_doublecheck_workitem w
    on ips.iworkitemid = w.iid
  left join ieai_doublecheck_colvalue col
    on col.iworkitemid = ips.iworkitemid
  left join ieai_agentinfo agi
    on ips.icpid = agi.iagentinfo_id
  left join ieai_scripts_coat ca
    on ca.iworkitemid = ips.iworkitemid
  left join ieai_script_instance ins
    on ca.iid = ins.imainid
   and ins.iagentip = agi.iagent_ip
 where col.icolheader = 'taskName'
   and exists (select 1
          from ieai_doublecheck_colvalue cc
         where cc.iworkitemid = w.iid and cc.icolheader = 'shutDownUUID');
/

-- 8.1.0 version Script patch is as follows
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING where iid=11;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(11,''数据库服务化控制服务启动方式权限'')';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
		END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_APPSYSTEM_PROP where iid=1;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'INSERT INTO IEAI_SCRIPT_APPSYSTEM_PROP(IID,IPROPNAME,IORDER,ICREATETIME,IMODIFYTIME,IDEL)  VALUES(1,''DBA-A'',0,to_number(to_char(sysdate,''YYYYMMDDHH24MISS'')), to_number(to_char(sysdate,''YYYYMMDDHH24MISS'')),0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_APPSYSTEM_PROP where iid=2;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'INSERT INTO IEAI_SCRIPT_APPSYSTEM_PROP(IID,IPROPNAME,IORDER,ICREATETIME,IMODIFYTIME,IDEL)  VALUES(2,''DBA-B'',0,to_number(to_char(sysdate,''YYYYMMDDHH24MISS'')), to_number(to_char(sysdate,''YYYYMMDDHH24MISS'')),0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
  	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_SQL_RESULT' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := ' CREATE TABLE IEAI_SCRIPT_SQL_RESULT(IID  NUMBER(19) NOT NULL, IFOREIGNID VARCHAR2(100), IFOREIGNHEADID NUMBER(19), IRESULT CLOB, ISQLKEY VARCHAR2(255), IFROM NUMBER(1),ISQL CLOB, CONSTRAINT PK_IEAI_SCRIPT_SQL_RESULT PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		 	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_SQL_HEAD' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_SQL_HEAD( IID NUMBER(19) NOT NULL, IFOREIGNID VARCHAR2(100), IRESULT CLOB,IFROM NUMBER(1),ISQLKEY VARCHAR2(255),CONSTRAINT PK_IEAI_SCRIPT_SQL_HEAD PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
  	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_DEVICE' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := 'CREATE TABLE IEAI_RESOURCE_DEVICE(IID  NUMBER(19) NOT NULL,  IRESOURCEID NUMBER(19), IDEVICENAME VARCHAR2(255), CONSTRAINT PK_IEAI_RESOURCE_DEVICE PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_FAILOPER_CONN' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_FAILOPER_CONN(IID DECIMAL(19,0) NOT NULL,IRESID DECIMAL(19,0),IRESMAINPID VARCHAR2(255),IAPPIP VARCHAR2(25),IUSERID DECIMAL(19,0),ICREATETIME DECIMAL(19,0),ILASTUPDATETIME DECIMAL(19,0),IKILLFLAG INTEGER DEFAULT 0,CONSTRAINT PK_IEAI_SCRIPT_FAIL_CONN PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_OPER_RESULT' AND COLUMN_NAME='ISQL';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_SCRIPT_OPER_RESULT ADD (ISQL CLOB)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_FAILOPER_PARA' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_FAILOPER_PARA(IID DECIMAL(19,0) NOT NULL,IUSERID DECIMAL(19,0),IMAINPID VARCHAR(255),IUUID VARCHAR(255),IPARAMVALUE VARCHAR(255),IPARAORDER INTEGER,CONSTRAINT PK_IEAI_SCRIPT_FAILOPER_PARA PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 	
  	 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_RESOURCE_AWR' AND OBJECT_TYPE = 'TABLE';
		 IF LI_EXISTS = 0 THEN
	        LS_SQL := 'CREATE TABLE IEAI_RESOURCE_AWR (IRESID NUMBER(19) NOT NULL,AWR CLOB,CONSTRAINT PK_IEAI_RESOURCE_AWR PRIMARY KEY (IRESID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

END;
/

-- 8.2.0 version Script patch is as follows
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_AUDITING_DANGER' AND COLUMN_NAME='IFULLCONTENT';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_AUDITING_DANGER ADD IFULLCONTENT VARCHAR2(1000)';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;

END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	     
	     SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_BILOCALCHECK_DATA' AND COLUMN_NAME='VERSIONS';
			IF LI_EXISTS = 0 THEN
				LS_SQL := 'ALTER TABLE IEAI_BILOCALCHECK_DATA ADD  VERSIONS VARCHAR2(50)';
				EXECUTE IMMEDIATE LS_SQL;
			END IF;
END;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_INSTANCE_HISTORY' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_INSTANCE_HISTORY (IID NUMBER(19) PRIMARY KEY,ISTATUS VARCHAR2(50),ITASKNAME VARCHAR2(200),IAGENTIP VARCHAR2(20),IHOSTNAME VARCHAR2(500),ISYSINFONAME VARCHAR2(500),ISTARTUSER VARCHAR2(100),IAUDITUSER VARCHAR2(100),IBUTTERFLYVERSION VARCHAR2(300),ISTRATEGY VARCHAR2(50),ISTARTTIME VARCHAR2(100),IENDTIME VARCHAR2(100),IRUNTIME VARCHAR2(100))';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/

CREATE OR REPLACE FUNCTION FUN_GET_RUNTIME_BYNUMBER(V_STARTDATENUMBER IN NUMBER,
                                                    V_ENDDATENUMBER   IN NUMBER,
                                                    V_TIMEZONE        IN SMALLINT)
  RETURN varchar2 IS

BEGIN
  DECLARE
    dayvalue    varchar2(30);
    hourvalue   varchar2(30);
    minutevalue varchar2(30);
    secondvalue varchar2(30);
    res         varchar2(100);

  BEGIN
    SELECT EXTRACT(DAY FROM(to_date(fun_get_date_string(V_ENDDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss') -
                        to_date(fun_get_date_string(V_STARTDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss')) DAY TO SECOND) || '天'
      into dayvalue
      FROM DUAL;
    if dayvalue <> '0天' then
      res := dayvalue;
    end if;

    select EXTRACT(HOUR FROM(to_date(fun_get_date_string(V_ENDDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss') -
                        to_date(fun_get_date_string(V_STARTDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss')) DAY TO SECOND) || '小时'
      into hourvalue
      FROM DUAL;
    if hourvalue <> '0小时' then
      res := res || hourvalue;
    end if;
    select EXTRACT(MINUTE FROM(to_date(fun_get_date_string(V_ENDDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss') -
                        to_date(fun_get_date_string(V_STARTDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss')) DAY TO SECOND) || '分'
      into minutevalue
      FROM DUAL;
    if minutevalue <> '0分' then
      res := res || minutevalue;
    end if;
    select EXTRACT(SECOND FROM(to_date(fun_get_date_string(V_ENDDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss') -
                        to_date(fun_get_date_string(V_STARTDATENUMBER,
                                                    V_TIMEZONE,
                                                    'YYYY-MM-DD HH24:MI:ss'),
                                'YYYY-MM-DD HH24:MI:ss')) DAY TO SECOND) || '秒'
      into secondvalue
      FROM DUAL;
    if secondvalue <> '0秒' then
      res := res || secondvalue;
    end if;
    RETURN res;
  END;
END FUN_GET_RUNTIME_BYNUMBER;
/

DECLARE
    LS_SQL VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_EXEC_EMAIL' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE IEAI_SCRIPT_EXEC_EMAIL (IID NUMBER(19)  ,IWORKITEMID NUMBER(19),IUSERID NUMBER(19),CONSTRAINT PK_IEAI_SCRIPT_EXEC_EMAIL PRIMARY KEY(IID))';
        EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/

-- 8.3.0 version Script patch is as follows
 DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;

BEGIN
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='IISSYNC';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST  ADD IISSYNC  NUMBER(1) default 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_AUDI_ATTACHMENT' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_AUDI_ATTACHMENT  (IID  NUMBER(19) NOT NULL,	IWORKITEMID NUMBER(19) ,	INAME      	VARCHAR2(500) ,	ISIZE      	NUMBER(19) ,	IUPLOADTIME	NUMBER(19) ,	ICONTENTS  	BLOB ,	CONSTRAINT PK_IEAI_SCRIPT_AUDI_ATTACHMENT PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ATTACH_REL' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ATTACH_REL  (IID  NUMBER(19) NOT NULL,	IWORKITEMID NUMBER(19) ,	IATTACHID     NUMBER(19) ,CONSTRAINT PK_IEAI_SCRIPT_ATTACH_REL PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_TEST_TRY' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_TEST_TRY(IID NUMBER(19,0) NOT NULL,ISCRIPTUUID  VARCHAR2(50),IISTRY INTEGER DEFAULT 0,ISCHANGE INTEGER DEFAULT 0,ITRYGROUPID NUMBER(19,0),ITRYUSERID NUMBER(19,0),ICREATEUSERID NUMBER(19,0),RETURNTEXT CLOB,ICREATETIME NUMBER(19,0),CONSTRAINT PK_IEAI_SCRIPT_TEST_TRY PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=12;
        IF  LI_EXISTS = 0 THEN
            LS_SQL :='insert into IEAI_SCRIPT_AUDITING (IID,INAME)  VALUES(12,''同步到生产审核权限'')';
          EXECUTE IMMEDIATE LS_SQL;
          COMMIT;
        END  IF;
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='IFROM';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD (ISBLJ INTEGER,IFROM INTEGER default 0,ISYSID DECIMAL(19,0))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DATA_CHANGE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DATA_CHANGE(IID DECIMAL(19,0) NOT NULL,IDATA_TASK_NO VARCHAR2(255),IDATA_NO VARCHAR2(255),IDATA_CONTENT VARCHAR2(4000),ISYSTEM_NAME VARCHAR2(255),ISYSTEM_ID DECIMAL(19,0),IDBTYPE INTEGER,ISQL_CONTENT CLOB,ISYS_ORG_ID DECIMAL(19,0),ISYS_ORG_NAME VARCHAR2(255),IUSERID DECIMAL(19,0),IAUDI_USERID VARCHAR2(255),IEXECTIME VARCHAR2(25),ICREATETIME DECIMAL(19,0),ISTATUS INTEGER,IMODIFYTIME DECIMAL(19,0),ISUBMITTIME DECIMAL(19,0),IISAUDI INTEGER,IREVIEWTIME DECIMAL(19,0),IREVIEW_OPINIONS VARCHAR2(255),IREFUSETIME DECIMAL(19,0),IREFUSE_REASON VARCHAR2(255),ISERVERIP VARCHAR2(25),ISERVERPORT INTEGER, ISTARTTIME DECIMAL(19,0), IENDTIME DECIMAL(19,0),IAUDITIME DECIMAL(19,0),IAUDINAME VARCHAR2(50),CONSTRAINT PK_IEAI_SCRIPT_DATA_CHANGE PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DATA_CHANGE_INS' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DATA_CHANGE_INS ( IID DECIMAL(19,0) NOT NULL, ITASKID DECIMAL(19,0), IAGENT_IP VARCHAR2(25), IGANET_PORT DECIMAL(10), IDBTYPE INTEGER, IDBDRIVER VARCHAR2(100), IDBNAME VARCHAR2(50), IDBUSER VARCHAR2(50), IDBURL VARCHAR2(255), IBLJ INTEGER, IDBPD VARCHAR2(100), IUUID VARCHAR2(60), ISTARTTIME DECIMAL(19,0), IENDTIME DECIMAL(19,0), ISTATUS INTEGER DEFAULT 0, ISTDOUT CLOB, ISTDLASTLINE CLOB, CONSTRAINT PK_IEAI_SCRIPT_DATA_CHANGE_INS PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DATACHANGE_NO' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DATACHANGE_NO(IID  DECIMAL(19,0) NOT NULL,ITABLENAME VARCHAR(50),IDATE DECIMAL(8) NOT NULL,INO DECIMAL(10),CONSTRAINT PK_IEAI_SCRIPT_DATACHANGE_NO PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;

BEGIN
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DATACHANGE_NO WHERE IID=1;
		IF  LI_EXISTS = 0 THEN
		  LS_SQL :='INSERT INTO IEAI_SCRIPT_DATACHANGE_NO(IID,ITABLENAME,IDATE,INO) VALUES(1,''SJBG'',20201225,1)';
		EXECUTE IMMEDIATE LS_SQL;
		END  IF; 
			SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_DATACHANGE_NO WHERE IID=2;
		IF  LI_EXISTS = 0 THEN
		  LS_SQL :='INSERT INTO IEAI_SCRIPT_DATACHANGE_NO(IID,ITABLENAME,IDATE,INO) VALUES(2,''SJBGRW'',20201225,1)';
		EXECUTE IMMEDIATE LS_SQL;
		END  IF; 
		
		SELECT  COUNT(*) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TOPRODUCT' AND COLUMN_NAME='IDESCRIPTION' and data_length=255;
		IF LI_EXISTS=1 THEN
			LS_SQL :='ALTER TABLE IEAI_SCRIPT_TOPRODUCT MODIFY IDESCRIPTION VARCHAR2(1000)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
END;
/
-- 8.4.0 version Script patch is as follows
DECLARE
LS_SQL VARCHAR2(2000);
    LI_EXISTS SMALLINT;

BEGIN
SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_AUDIT_SYS_AGENT' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
        LS_SQL := 'CREATE TABLE  IEAI_SCRIPT_AUDIT_SYS_AGENT (IID NUMBER(19) not null,IWORKITEMID NUMBER(19),ISYSID NUMBER(19),IAGENTID NUMBER(19),constraint PK_IEAI_SCRIPT_AUDIT_SYS_AGENT primary key (IID))';
EXECUTE IMMEDIATE LS_SQL;
END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_SYS_CP_RELATION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SCRIPT_SYS_CP_RELATION (IID NUMBER(19) not null,ISCRIPTUUID VARCHAR2(255),ISYSTEMID NUMBER(19),ICPID NUMBER(19), CONSTRAINT PK_IEAI_SCRIPT_SYS_CP_RELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_SYSTEM_RELATION' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SCRIPT_SYSTEM_RELATION (IID NUMBER(19) not null,ISCRIPTUUID VARCHAR2(255),ISYSTEMID NUMBER(19), CONSTRAINT PK_IEAI_SCRIPT_SYSTEM_RELATION PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

END;
/
DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;

BEGIN
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='ISTATE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD ISTATE INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='ICOLLECT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD ICOLLECT VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='ISBZ';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISBZ INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='BZ';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD BZ CLOB';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
-- 8.5.0 version Script patch is as follows
DECLARE
    LS_SQL VARCHAR2(2000);
    LI_EXISTS SMALLINT;

BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ISSUERECORD' AND COLUMN_NAME='PERMISSION';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD PERMISSION VARCHAR2(10)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ISSUERECORD' AND COLUMN_NAME='USERPERMISSION';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD USERPERMISSION VARCHAR2(50)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_ISSUERECORD' AND COLUMN_NAME='GROUPPERMISSION';
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'ALTER TABLE IEAI_SCRIPT_ISSUERECORD ADD GROUPPERMISSION VARCHAR2(50)';
    EXECUTE IMMEDIATE LS_SQL;
    END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='ITIMEOUT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD ITIMEOUT NUMBER(19) DEFAULT -1'; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_INSTANCE' AND COLUMN_NAME='ITIMEOUTVALUE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_INSTANCE ADD ITIMEOUTVALUE NUMBER(5) DEFAULT -1';
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_BSTYPETHREELEVEL' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SCRIPT_BSTYPETHREELEVEL (IID NUMBER (19)   NOT NULL,THREETYPENAME VARCHAR2 (255)  ,TWOBSTYPEID NUMBER (19)  ,IDEL NUMBER (19) DEFAULT 0 , CONSTRAINT PK_SCRIPT_BSTYPETHREELEVEL PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='THREETYPEID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD THREETYPEID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='THREETYPENAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD THREETYPENAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='THREETYPEID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD THREETYPEID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='THREETYPENAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD THREETYPENAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='THREETYPEID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD THREETYPEID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='THREETYPENAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD THREETYPENAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;

BEGIN
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DBBINDSERVICE' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DBBINDSERVICE( IID  NUMBER(19) NOT NULL,  ISERUUID VARCHAR2(255),  IDBTYPE  NUMBER(19),  IOSTYPE  VARCHAR2(10),  CONSTRAINT PK_IEAI_SCRIPT_DBBINDSERVICE PRIMARY KEY(IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='ISJDBC';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISJDBC INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='ISQLBAK';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISQLBAK  clob';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='ISQLROLLBACK';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISQLROLLBACK  clob';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='ISBAK';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISBAK INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IMODELFLAG';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IMODELFLAG INTEGER DEFAULT 0';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='ISCRIPTUUID';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD ISCRIPTUUID VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IBAKSTATUS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBAKSTATUS INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IBACKSTATUS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBACKSTATUS INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IBAKSTARTTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBAKSTARTTIME NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IBAKENDTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBAKENDTIME NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IBACKSTARTTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBACKSTARTTIME NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IBACKENDTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IBACKENDTIME NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IEXECSTATUS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IEXECSTATUS INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IEXECSTARTTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD  IEXECSTARTTIME NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE' AND COLUMN_NAME='IEXECENDTIME';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE  ADD IEXECENDTIME NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;		

 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND COLUMN_NAME='IDBENCODE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBENCODE varchar2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND COLUMN_NAME='IDBPARAMS';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBPARAMS varchar2(3000)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND COLUMN_NAME='IDBIP';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBIP VARCHAR2(25)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND COLUMN_NAME='IDBPORT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IDBPORT NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND COLUMN_NAME='ISTEPID';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD ISTEPID NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		 		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATA_CHANGE_INS' AND COLUMN_NAME='IRESID';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATA_CHANGE_INS  ADD IRESID NUMBER(19,0)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='ISTATE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD ISTATE  INTEGER';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='IICOLLECT';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD IICOLLECT VARCHAR2(2000)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='IDBENCODE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD IDBENCODE VARCHAR2(255)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='IDBIP';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD IDBIP VARCHAR2(25)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='ISU_USER';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE  ADD ISU_USER VARCHAR2(25)';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DC_PARAM' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DC_PARAM(IID NUMBER(19) NOT NULL,  ITASKID NUMBER(19),  ITYPE VARCHAR2(25),IVALUE VARCHAR2(255),  IORDER INTEGER,ISCRIPTUUID VARCHAR2(255),CONSTRAINT PK_IEAI_SCRIPT_DC_PARAM PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_USER_DEPARTMENT' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_USER_DEPARTMENT(IID NUMBER(19,0) NOT NULL,IDEPNAME VARCHAR2(64),IORDER INTEGER,CONSTRAINT PK_IEAI_USER_DEPARTMENT PRIMARY KEY (IID))';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;

END;
/
DECLARE
		 LS_SQL VARCHAR2(2000);
		 LI_EXISTS SMALLINT;

BEGIN
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID = 4;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_SCRIPT_DBBINDSERVICE (IID, ISERUUID, IDBTYPE, IOSTYPE) values (4, null, 1, ''win'')';
			EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID = 5;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_SCRIPT_DBBINDSERVICE (IID, ISERUUID, IDBTYPE, IOSTYPE) values (5, null, 2, ''win'')';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID = 6;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_SCRIPT_DBBINDSERVICE (IID, ISERUUID, IDBTYPE, IOSTYPE) values (6, null, 3, ''win'')';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID = 1;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_SCRIPT_DBBINDSERVICE (IID, ISERUUID, IDBTYPE, IOSTYPE) values (1, null, 1, ''linux'')';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID = 2;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_SCRIPT_DBBINDSERVICE (IID, ISERUUID, IDBTYPE, IOSTYPE) values (2, null, 2, ''linux'')';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DBBINDSERVICE WHERE IID = 1;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_SCRIPT_DBBINDSERVICE (IID, ISERUUID, IDBTYPE, IOSTYPE) values (3, '', 3, ''linux'')';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_USER_DEPARTMENT WHERE IID = 1;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_USER_DEPARTMENT (IID, IDEPNAME, IORDER) values (1, ''应用管理一处'', 1)';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_USER_DEPARTMENT WHERE IID = 2;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_USER_DEPARTMENT (IID, IDEPNAME, IORDER) values (2, ''应用管理二处'', 2)';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_USER_DEPARTMENT WHERE IID = 3;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'insert into IEAI_USER_DEPARTMENT (IID, IDEPNAME, IORDER) values (3, ''应用管理三处'', 3)';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DATACHANGE_NO WHERE IID = 1;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'INSERT INTO IEAI_SCRIPT_DATACHANGE_NO(IID,ITABLENAME,IDATE,INO) VALUES(1,''SJBG'',20201225,1)';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_DATACHANGE_NO WHERE IID = 2;
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'INSERT INTO IEAI_SCRIPT_DATACHANGE_NO(IID,ITABLENAME,IDATE,INO) VALUES(2,''SJBGRW'',20201225,1)';
					EXECUTE IMMEDIATE LS_SQL;
		END IF;
		commit;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TRY_TRY' AND COLUMN_NAME='ICONTENT'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TRY_TRY ADD ICONTENT CLOB '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
--8.6.0 
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_DATABASE_ENV' AND OBJECT_TYPE = 'TABLE';
		IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_SCRIPT_DATABASE_ENV(IID    DECIMAL(19) NOT NULL,  IDSID  DECIMAL(19) NOT NULL,  IORDER DECIMAL(19),  INAME  VARCHAR2(255),  IVALUE VARCHAR2(255),  CONSTRAINT PK_IEAI_SCRIPT_DATABASE_ENV PRIMARY KEY (IID))';
						EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_DATASOURCE' AND COLUMN_NAME='IMSG';
		IF	LI_EXISTS = 0 THEN
					LS_SQL := 'ALTER TABLE IEAI_SCRIPT_DATASOURCE ADD IMSG CLOB ';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_BUSINESS_SYSTEM' AND COLUMN_NAME='ISYSIDEN';
		IF	LI_EXISTS = 0 THEN
					LS_SQL := 'ALTER TABLE ieai_script_business_system  ADD ISYSIDEN VARCHAR2(25)';
		EXECUTE IMMEDIATE LS_SQL;
		END IF;
END;
/
--8.7--------------
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_TEST' AND COLUMN_NAME='CREATEUSERNAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_TEST ADD CREATEUSERNAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='CREATEUSERNAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD CREATEUSERNAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='IUPDATEUSERID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD IUPDATEUSERID NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SHARE' AND COLUMN_NAME='CREATEUSERNAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SHARE ADD CREATEUSERNAME VARCHAR2(255) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SCRIPT_SERVICES' AND COLUMN_NAME='IUPDATEUSERID'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_SCRIPT_SERVICES ADD IUPDATEUSERID NUMBER(19)';
			EXECUTE IMMEDIATE LS_SQL; 
		END IF;

        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_FLOW_SCRIPTUUID' AND OBJECT_TYPE = 'INDEX';
        IF	LI_EXISTS = 0 THEN
                LS_SQL := 'CREATE INDEX IDX_SCRIPT_FLOW_SCRIPTUUID ON IEAI_SCRIPT_FLOW (IMXSERVICEID)';
        EXECUTE IMMEDIATE LS_SQL;
        END	IF;
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ITSMPUBLISH' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ITSMPUBLISH (IID NUMBER(19)   NOT NULL,WORKITEMIID NUMBER(19)  ,SCRIPTIID NUMBER(19)  ,SCRIPTUUID VARCHAR2(50)  ,SCRIPTLEVEL NUMBER(1) ,TASKUUID VARCHAR2(50)  , CONSTRAINT PK_IEAI_SCRIPT_ITSMPUBLISH PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_ITSM_TASKUUID' AND OBJECT_TYPE = 'INDEX';
        IF	LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE INDEX IDX_SCRIPT_ITSM_TASKUUID ON IEAI_SCRIPT_ITSMPUBLISH (TASKUUID)';
        EXECUTE IMMEDIATE LS_SQL;
        END	IF;
        COMMIT;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_ITSMTASK' AND COLUMN_NAME='ISCRIPTBUSSTYPE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_ITSMTASK ADD ISCRIPTBUSSTYPE NUMBER(1)';
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IUNUSERD'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IUNUSERD NUMBER(19) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_AGENTINFO' AND COLUMN_NAME='IAUDITSTATE'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE IEAI_AGENTINFO ADD IAUDITSTATE NUMBER(19) DEFAULT -1';
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/

DECLARE
LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN
        SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_MENU WHERE IID=50005;
        IF  LI_EXISTS = 0 THEN
                  LS_SQL :='INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(50005,''未启用设备管理'',10,''notEnableEquipList.do'',61,''脚本服务化'','''')';
                    EXECUTE IMMEDIATE LS_SQL;
        COMMIT;
        END  IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_CIB_HTMLFILE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SCRIPT_CIB_HTMLFILE (IID NUMBER (19)   NOT NULL,ITASKUUID VARCHAR2 (50)  ,IFILENAME VARCHAR2 (100)  ,IHTMLFILE CLOB   , CONSTRAINT PK_IEAI_SCRIPT_CIB_HTMLFILE PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;

        SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_SCRIPT_ITSMHTML_TASKUUID' AND OBJECT_TYPE = 'INDEX';
        IF	LI_EXISTS = 0 THEN
                    LS_SQL := 'CREATE INDEX IDX_SCRIPT_ITSMHTML_TASKUUID ON IEAI_SCRIPT_CIB_HTMLFILE (ITASKUUID)';
        EXECUTE IMMEDIATE LS_SQL;
        END	IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_SCRIPT_ATTACHMENT_TEMP' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
            LS_SQL := 'CREATE TABLE IEAI_SCRIPT_ATTACHMENT_TEMP (IID NUMBER (19)   NOT NULL,ISCRUPTUUID VARCHAR2 (50)  ,INAME VARCHAR2 (200)  ,ISIZE NUMBER (19)  ,IUPLOADTIME NUMBER (19)  ,ICONTENTS BLOB   ,IWORKITEMID NUMBER (19)  ,ISERVERIP VARCHAR(100), CONSTRAINT PK_IEAI_SCRIPT_ATTACHMENT_TEMP PRIMARY KEY (IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
         SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IDX_IEAI_SCRIPT_FUJIAN_TEMP' AND OBJECT_TYPE = 'INDEX';
         IF	LI_EXISTS = 0 THEN
             LS_SQL := 'CREATE INDEX IDX_IEAI_SCRIPT_FUJIAN_TEMP ON IEAI_SCRIPT_ATTACHMENT_TEMP (IWORKITEMID)';
             EXECUTE IMMEDIATE LS_SQL;
         END	IF;
END;
/
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_IMPAGENTIP' AND COLUMN_NAME='COMPUTERNAME'; 
		IF	LI_EXISTS = 0 THEN 
			LS_SQL := 'ALTER TABLE TMP_IMPAGENTIP ADD COMPUTERNAME VARCHAR2(200) '; 
			EXECUTE IMMEDIATE LS_SQL; 
		END IF; 
		COMMIT; 
END;
/
