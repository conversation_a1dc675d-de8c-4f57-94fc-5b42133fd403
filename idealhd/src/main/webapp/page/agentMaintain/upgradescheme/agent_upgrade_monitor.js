var upgrade_strategy_store;
var upgrade_program_store;
var rowExpander;
Ext.onReady(function(){

	Ext.override(Ext.grid.plugin.RowExpander, {
		setup: function(rows, rowValues){
			var me = this;
			me.self.prototype.setup.apply(me, arguments);

			if (!me.grid.ownerLockable) {
				rowValues.rowBodyColspan -= 0;
			}
		},
		toggleRow: function(rowIdx, record) {

			var me = this,
				view = me.view,
				rowNode = view.getNode(rowIdx),
				row = Ext.fly(rowNode, '_rowExpander'),
				nextBd = row.down(me.rowBodyTrSelector, true),
				isCollapsed = row.hasCls(me.rowCollapsedCls),
				addOrRemoveCls = isCollapsed ? 'removeCls' : 'addCls',
				ownerLock, rowHeight, fireView;


			Ext.suspendLayouts();
			row[addOrRemoveCls](me.rowCollapsedCls);
			Ext.fly(nextBd)[addOrRemoveCls](me.rowBodyHiddenCls);
			me.recordsExpanded[record.internalId] = isCollapsed;
			view.refreshSize();


			if (me.grid.ownerLockable) {
				ownerLock = me.grid.ownerLockable;
				fireView = ownerLock.getView();
				view = ownerLock.lockedGrid.view;
				rowHeight = row.getHeight();
				row = Ext.fly(view.getNode(rowIdx), '_rowExpander');
				row.setHeight(rowHeight);
				row[addOrRemoveCls](me.rowCollapsedCls);
				view.refreshSize();
			} else {
				fireView = view;
			}

			fireView.fireEvent(isCollapsed ? 'expandbody' : 'collapsebody', row.dom, record, nextBd);

			Ext.resumeLayouts(true);
		},
		getHeaderConfig: function() {
			var me = this;

			return {
				width: 24,
				lockable: false,
				sortable: false,
				resizable: false,
				draggable: false,
				hideable: true,
				hidden: true,
				menuDisabled: true,
				tdCls: Ext.baseCSSPrefix + 'grid-cell-special',
				innerCls: Ext.baseCSSPrefix + 'grid-cell-inner-row-expander',
				renderer: function(value, metadata) {

					if (!me.grid.ownerLockable) {
						metadata.tdAttr += ' rowspan="2"';
					}
					return '<div class="' + Ext.baseCSSPrefix + 'grid-row-expander"></div>';
				},
				processEvent: function(type, view, cell, rowIndex, cellIndex, e, record) {
					if (type == "mousedown" && e.getTarget('.x-grid-row-expander')) {
						me.toggleRow(rowIndex, record);
						return me.selectRowOnExpand;
					}
				}
			};
		}
	});
	//清理各种监听
	destroyRubbish();
	Ext.tip.QuickTipManager.init ();
	Ext.QuickTips.init();

	// Ext.override(Ext.grid.GridPanel, {
	// 	afterRender : Ext.Function.createSequence(
	// 		Ext.grid.GridPanel.prototype.afterRender,
	// 		function() {
	// 			// 默认显示提示
	// 			if (!this.cellTip) {
	// 				return;
	// 			}
	//
	// 			var view = this.getView();
	//
	// 			this.tip = new Ext.ToolTip({
	// 				target: view.el,
	// 				delegate : '.x-grid-cell-inner',
	// 				trackMouse: true,
	// 				renderTo: Ext.getBody()  ,
	// 				ancor : 'top',
	// 				style : 'background-color: #FFFFCC;',
	// 				listeners: {
	// 					beforeshow: function updateTipBody(tip) {
	//
	// 						var tipText = (tip.triggerElement.innerText || tip.triggerElement.textContent);
	//
	// 						if (Ext.isEmpty(tipText) || Ext.isEmpty(tipText.trim()) ) {
	// 							return false;
	// 						}
	// 						tip.update(tipText);
	// 					}
	// 				}
	// 			});
	// 		})
	// });
	// Ext.override(Ext.grid.plugin.RowExpander, {
	// 	setup: function(rows, rowValues){
	// 		var me = this;
	// 		me.self.prototype.setup.apply(me, arguments);
	//
	// 		if (!me.grid.ownerLockable) {
	// 			rowValues.rowBodyColspan -= 0;
	// 		}
	// 	},
	// 	toggleRow: function(rowIdx, record) {
	// 		var me = this,
	// 			view = me.view,
	// 			rowNode = view.getNode(rowIdx),
	// 			row = Ext.fly(rowNode, '_rowExpander'),
	// 			nextBd = row.down(me.rowBodyTrSelector, true),
	// 			isCollapsed = row.hasCls(me.rowCollapsedCls),
	// 			addOrRemoveCls = isCollapsed ? 'removeCls' : 'addCls',
	// 			ownerLock, rowHeight, fireView;
	//
	//
	// 		Ext.suspendLayouts();
	// 		row[addOrRemoveCls](me.rowCollapsedCls);
	// 		Ext.fly(nextBd)[addOrRemoveCls](me.rowBodyHiddenCls);
	// 		me.recordsExpanded[record.internalId] = isCollapsed;
	// 		view.refreshSize();
	//
	//
	// 		if (me.grid.ownerLockable) {
	// 			ownerLock = me.grid.ownerLockable;
	// 			fireView = ownerLock.getView();
	// 			view = ownerLock.lockedGrid.view;
	// 			rowHeight = row.getHeight();
	// 			row = Ext.fly(view.getNode(rowIdx), '_rowExpander');
	// 			row.setHeight(rowHeight);
	// 			row[addOrRemoveCls](me.rowCollapsedCls);
	// 			view.refreshSize();
	// 		} else {
	// 			fireView = view;
	// 		}
	// 		fireView.fireEvent(isCollapsed ? 'expandbody' : 'collapsebody', row.dom, record, nextBd);
	//
	// 		Ext.resumeLayouts(true);
	// 	},
	// 	getHeaderConfig: function() {
	// 		var me = this;
	// 		return {
	// 			width: 24,
	// 			lockable: false,
	// 			sortable: false,
	// 			resizable: false,
	// 			draggable: false,
	// 			hideable: true,
	// 			hidden: true,
	// 			menuDisabled: true,
	// 			tdCls: Ext.baseCSSPrefix + 'grid-cell-special',
	// 			innerCls: Ext.baseCSSPrefix + 'grid-cell-inner-row-expander',
	// 			renderer: function(value, metadata) {
	//
	// 				if (!me.grid.ownerLockable) {
	// 					metadata.tdAttr += ' rowspan="2"';
	// 				}
	// 				return '<div class="' + Ext.baseCSSPrefix + 'grid-row-expander"></div>';
	// 			},
	// 			processEvent: function(type, view, cell, rowIndex, cellIndex, e, record) {
	// 				if (type == "mousedown" && e.getTarget('.x-grid-row-expander')) {
	// 					me.toggleRow(rowIndex, record);
	// 					return me.selectRowOnExpand;
	// 				}
	// 			}
	// 		};
	// 	}
	// });



	Ext.define('AgentModel', {
		extend: 'Ext.data.Model',
		fields: [
			{name: 'taskId',     type: 'string'},
			{name: 'runid',     type: 'string'},
			{name: 'irequestid',     type: 'string'},
			{name: 'agentIP',     type: 'string'},
			{name: 'agentPort',     type: 'string'},
			{name: 'startTime',     type: 'string'},
			{name: 'endTime',     type: 'string'},
			{name: 'timeOut',     type: 'string'},
			{name: 'agentVersion',     type: 'string'},
			{name: 'upgradeVersion',     type: 'string'},
			{name: 'state',     type: 'string'},
			{name: 'planName',     type: 'string'},
			{name: 'ilog',     type: 'string'},
		]
	});

	Ext.define('planModel', {
		extend: 'Ext.data.Model',
		fields: [
			{name: 'runId',     type: 'string'},
			{name: 'schemeId',     type: 'string'},
			{name: 'serverIp',     type: 'string'},
			{name: 'startName',     type: 'string'},
			{name: 'anticipateStartTime',     type: 'string'},
			{name: 'schemeStatus',     type: 'string'},
			{name: 'startTiem',     type: 'string'},
			{name: 'endTime',     type: 'string'},
			{name: 'agentQuantity',     type: 'string'},
			{name: 'planName',     type: 'string'},
		]
	});

	upgrade_strategy_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 50,
		model: 'planModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentUpgradePlanRun.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});

	var upgrade_strategy = [{ text: '序号', xtype:'rownumberer', width: 80 },
		{ text: 'IID',  dataIndex: 'runId',hidden: true},
		{ text: '方案id',  dataIndex: 'schemeId',hidden: true},
		{ text: '方案名',  dataIndex: 'planName',flex : 1},
		{ text: '操作人',  dataIndex: 'startName',flex : 1},
		{ text: '启动时间',  dataIndex: 'startTiem',flex : 1},
		{ text: '结束时间',  dataIndex: 'endTime',flex : 1},

		{ text: 'Agent数量',  dataIndex: 'agentQuantity',flex : 1},

		{ text: '预计启动时间',  dataIndex: 'anticipateStartTime',flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var cls = "";
				if(value=='0') {
					cls = "<span class=''></span>";
				} else {
					cls = "<span class=''>"+value+"</span>";
				}
				return cls;
			}
		},
		{ text: '升级状态',  dataIndex: 'schemeStatus',flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var cls = "";
				if(value=='10') {
					cls = "<span >方案待升级</span>";
				} else if(value=='11') {
					cls = "<span class='Run_Green State_Color'>方案升级开始</span>";
				}else if(value=='20') {
					cls = "<span class='Green_color State_Color'>方案升级完成</span>";
				}else if(value=='19') {
					cls = "<span class='Red_color State_Color'>方案升级完成</span>";
				}else if(value=='21') {
					cls = "<span class='Run_Green State_Color'>方案取消开始</span>";
				}else if(value=='32') {
					cls = "<span class='Run_Green State_Color'>方案取消开始</span>";
				}else if(value=='30') {
					cls = "<span class='Green_color State_Color'>取消完成</span>";
				}else if(value=='29') {
					cls = "<span class='Red_color State_Color'>取消完成</span>";
				}
				return cls;
			}
		},

		{
			dataIndex: 'schemeStatus',
			text : '操作',
			flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var	runId=record.data.runId
				var url ="";
				if('20' ==value ||'19'==value||'29'==value){
					url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"agentUpgrade('rollback',"+runId+")\">回退</a>";
				}else if('10' == value ||'11'==value){
					url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"agentUpgrade('cancel',"+runId +")\">取消</a>";
				}
				var tj="<span>  </span><a href='#' class='handleA' script='javascript:void(0)' onclick=\"statisticalUpgrade("+runId+")\">统计信息</a>";
				return url+tj;
			},
			// items : [{
			// 	iconCls: 'monitor_termination',
			// 	text : '取消',
			// 	handler : function(value, meta, record) {
			//
			// 		alert("取消")
			// 	}
			// },{
			// 	iconCls: 'monitor_execute',
			// 	text : '统计信息',
			// 	handler : function(grid, rowIndex) {
			// 		alert("统计信息")
			//
			// 	}
			// }]
		}
	];

	upgrade_strategy_store.on('beforeload', function (store, options) {
		var new_params = {
			isysclassid:''
		};

		Ext.apply(upgrade_strategy_store.proxy.extraParams, new_params);
	});

	var planNamefrom = Ext.create('Ext.form.TextField', {
		fieldLabel : '方案名',
		emptyText : '--请输入方案名--',
		labelAlign:'right',
		labelWidth : 55,
		xtype : 'textfield',
		name : 'planName'
	});

	var taskStatusStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data: [{
			"id": "0",
			"name": "未执行"
		}, {
			"id": "1",
			"name": "异常"
		}, {
			"id": "2",
			"name": "运行中"
		},{
			"id": "3",
			"name": "完成"
		},{
			"id": "4",
			"name": "全部状态"
		}]
	});


	var taskAgentStore = Ext.create('Ext.data.Store', {
		fields: ['id', 'name'],
		data: [ {
			"id": "1",
			"name": "升级失败"
		},{
			"id": "3",
			"name": "升级完成"
		},{
			"id": "0",
			"name": "全部状态"
		}]
	});

	var upgradState = Ext.create('Ext.form.field.ComboBox', {
		name: 'status',
		labelWidth: 70,
		queryMode: 'local',
		fieldLabel: ' 升级状态',
		displayField: 'name',
		valueField: 'id',
		editable: false,
		emptyText: '--请选择运行状态--',
		store: taskStatusStore,
		width: '20%',
		value: '4'
	});

	var upgradAgentState = Ext.create('Ext.form.field.ComboBox', {
		labelAlign:'right',
		name: 'status',
		labelWidth: 120,
		queryMode: 'local',
		fieldLabel: 'Agent升级状态',
		displayField: 'name',
		valueField: 'id',
		editable: false,
		emptyText: '--请选择升级状态--',
		store: taskAgentStore,
		width: '25%',
		value: '0'
	});

	var sciptParaCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 2
	});
	var runId=0;
	var upgrade_strategy_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		region : 'north',
		title : '方案信息',
		height :'50%',
		split : true,
		multiSelect : true,
		columnLines : true,
		border:true,
		plugins : [ sciptParaCellEditing ],
		store : upgrade_strategy_store,
		columns : upgrade_strategy,
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		cls:'customize_grid_back panel_space_top',
		iconCls:'monitor_systemIcon',
		//padding : grid_space,
		cellTip : true,
		forceFit: false,
		collapsible : true,
		stateful: false,
		margin: '0 0 0 0',
		selModel: { mode: "SINGLE" },//模式是单个，表明只能选择一条数据，
		selType: 'checkboxmodel',//必须要写，声明该列是复选框
		listeners: {
			select: function (combo, record) {
				runId=record.data.runId;
				upgrade_program_store.load();
			}
		},
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [planNamefrom ,upgradState, {
				xtype : 'button',
				margin : '5',
				text : '查询',
				baseCls : 'Common_Btn',
				handler : function() {
					runId=0;
					upgrade_strategy_store.load();
					upgrade_program_store.load();
				}
			} ]
		}],
	});



	upgrade_strategy_store.on('beforeload', function (store, options) {
		var planName= planNamefrom.getValue().trim();
		var status= upgradState.getValue().trim();
		var new_params = {
			planName:planName,
			iagentState:status
		};
		Ext.apply(upgrade_strategy_store.proxy.extraParams, new_params);
	});



	upgrade_program_store = Ext.create('Ext.data.Store', {
		autoLoad: true,
		pageSize: 30,
		model: 'AgentModel',
		proxy: {
			type: 'ajax',
			url: 'getAgentMaintainUpgrade.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'total'
			}
		}
	});


	var upgrade_program_columns = [
		{ text: '序号', xtype:'rownumberer', width: 80},
		{ text: 'taskId',  dataIndex: 'taskId',hidden:true},
		{ text: 'irequestid',  dataIndex: 'irequestid',hidden:true},
		{ text: 'runid',  dataIndex: 'runid',hidden:true},
		{ text: 'agent地址',  dataIndex: 'agentIP',flex : 1},
		{ text: 'agent端口',  dataIndex: 'agentPort',flex : 1},
		{ text: '开始时间',  dataIndex: 'startTime',flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var date = new Date(parseInt(value/1000)*1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
				var Y = date.getFullYear() + '-';
				var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1)+ '-';
				var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate())+ ' ' ;
				var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
				var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes())+ ':';
				var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());

				let strDate = Y+M+D+h+m+s;
				return strDate;
			}},
		{ text: '结束时间',  dataIndex: 'endTime',flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				if(value==0){
					return "";
				}
				var date = new Date(parseInt(value/1000)*1000);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
				var Y = date.getFullYear() + '-';
				var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1)+ '-';
				var D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate())+ ' ' ;
				var h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
				var m = (date.getMinutes() < 10 ? '0'+date.getMinutes() : date.getMinutes())+ ':';
				var s = (date.getSeconds() < 10 ? '0'+date.getSeconds() : date.getSeconds());
				let strDate = Y+M+D+h+m+s;
				return strDate;
			}
		},
		{ text: '耗时(秒)',  dataIndex: 'timeOut',flex : 1},
		{ text: '当前版本',  dataIndex: 'agentVersion',flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var cls = "";
				if(value=='null') {
					cls = "<span></span>";
				} else{
					cls = "<span>"+value+"</span>";
				}
				return cls;
			}
		},
		{ text: '升级版本',  dataIndex: 'upgradeVersion',flex : 1},
		{ text: '执行状态',  dataIndex: 'state',flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var cls = "";
				if(value=='200') {
					cls = "<span class='Green_color State_Color'>升级成功</span>";
				} else if(value=='101') {
					cls = "<span class='Green_color State_Color'>升级开始</span>";
				}else if(value=='102') {
					cls = "<span class='Green_color State_Color'>升级已发送</span>";
				}else if(value=='151') {
					cls = "<span class='Red_color State_Color'>升级超时</span>";
				}else if(value=='152') {
					cls = "<span class='Red_color State_Color'>升级失败</span>";
				}else if(value=='141') {
					cls = "<span class='Red_color State_Color'>升级发送失败</span>";
				}else if(value=='201') {
					cls = "<span class='Green_color State_Color'>回退开始</span>";
				}else if(value=='202') {
					cls = "<span class='Green_color State_Color'>回退已发送</span>";
				}else if(value=='251') {
					cls = "<span class='Red_color State_Color'>回退超时</span>";
				}else if(value=='252') {
					cls = "<span class='Red_color State_Color'>回退失败</span>";
				}else if(value=='300') {
					cls = "<span class='Green_color State_Color'>回退成功</span>";
				}
				return cls;
			}
		},
		{
			dataIndex: 'state',
			text : '操作',
			flex : 1,
			renderer: function(value, metadata, record, rowIndex, colIndex, store, view) {
				var	runId=record.data.runid;
				var	taskId=record.data.taskId;
				var url ="";
				if(200==value ||151==value || 152==value||141==value||251==value||252==value){
					url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"agentOperation('rollback',"+taskId+","+value+")\">回退</a>";
				}
				else if(101==value || 102==value){
					url="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"agentOperation('cancel',"+taskId+","+value +")\">取消</a>";
				}
				var tj="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"viewPolicy("+runId+")\"> 查看 </a><span>  </span>";
				var rz="<a href='#' class='handleA' script='javascript:void(0)' onclick=\"viewlog("+rowIndex+")\"> 日志 </a><span>  </span>";
				return tj+url+rz;
			}
		}
	];
	/** * 数据转化 * @param {*} n * @returns */
	function formatNumber(n) {

		n = n.toString()
		return n[1] ? n : '0' + n
	}
	function formatTime(number,format){

		let formateArr = ['Y', 'M', 'D', 'h', 'm', 's'];
		let returnArr = [];
		let date = new Date(number * 1000);
		if (typeof (number) == 'object') {

			date = new Date();
		}
		returnArr.push(date.getFullYear());
		returnArr.push(formatNumber(date.getMonth() + 1));
		returnArr.push(formatNumber(date.getDate()));
		returnArr.push(formatNumber(date.getHours()));
		returnArr.push(formatNumber(date.getMinutes()));
		returnArr.push(formatNumber(date.getSeconds()));
		for (var i in returnArr) {

			format = format.replace(formateArr[i], returnArr[i]);
		}
	}

	rowExpander = Ext.create('Ext.grid.plugin.RowExpander', {
		expandOnDblClick : false,
		expandOnEnter : false,
		rowBodyTpl :[
			'<div id="step{iid}">',
			'<textarea id="steptextarea{iid}" class="monitor_desc"></textarea>',
			'<input type="button" value="刷新" onclick="loadShelloutput({iid},{iflowid})" class="Common_Btn Monitor_Btn">',
			'</div>'
		]
	})
	var gridUpgradeProgram = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 1
	});
	var upgrade_program_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
		iconCls:'monitor_descIcon',
		cls:'customize_grid_back panel_space_top',
		ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
		title:'Agent信息',
		region : 'west',
		width:'100%',
		cellTip : true,
		//padding : grid_space,
		store:upgrade_program_store,
		border:true,
		columnLines : true,
		columns:upgrade_program_columns,
		stateful: false,
		selModel: Ext.create('Ext.selection.CheckboxModel', {}),
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			dock : 'top',
		}],
		dockedItems : [ {
			xtype : 'toolbar',
			baseCls:'customize_gray_back',
			dock : 'top',
			items : [upgradAgentState, {
				xtype : 'button',
				margin : '5',
				text : '查询',
				baseCls : 'Common_Btn',
				handler : function() {
					upgrade_program_store.load();
				}
			} ]
		}],
	});


	upgrade_program_store.on('beforeload', function (store, options) {
		var status= upgradAgentState.getValue();
		var new_params = {
			runId:runId,
			iagentState:status
		};
		Ext.apply(upgrade_program_store.proxy.extraParams, new_params);
	});

	upgrade_program_grid.view.on('expandBody', function (rowNode, record, expandRow, eOpts) {
		//loadShelloutput(record.get('iid'),record.get('iflowid'))
	});


	var emMonitorGridpanel = Ext.create('Ext.panel.Panel', {
		region : 'center',
		border : false,
		layout : 'border',
		items : [upgrade_strategy_grid,upgrade_program_grid]
	});


	var mainPanel = Ext.create('Ext.panel.Panel',{
		renderTo : "upgrade_monitor",
		bodyCls: 'service_platform_bodybg',
		height : contentPanel.getHeight()-modelHeigth,
		width : contentPanel.getWidth()-300,
		layout : 'border',
		border : true,
		items : [emMonitorGridpanel]
	});





	function clearQueryWhere(){

		centernameCombo.select(stateStore.getAt(1));
	}

	contentPanel.on('resize',function(){

		mainPanel.setWidth(contentPanel.getWidth());
		mainPanel.setHeight(contentPanel.getHeight()-modelHeigth);
	})




});


function agentOperation(startName,irequestid,state){
	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
		if (btn == 'yes') {
			Ext.Ajax.request({
				url: 'updateAgentMaintainTask.do',
				async:false,
				params: {
					irequestid:irequestid,
					startName:startName,
					taskId:irequestid,
					schemeStatus:state
				},
				method: 'POST',
				success: function(response, opts) {
					Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
					upgrade_strategy_store.load();
					upgrade_program_store.load();
				},
				failure: function(result, request) {
					Ext.Msg.alert('提示', "请求失败");
				}
			});
		}
	});

}
//统计
function statisticalUpgrade(runId){
	Ext.Ajax.request({
		url: 'agentUpgradeStatistical.do',
		async:false,
		params: {
			runId:runId
		},
		method: 'POST',
		success: function(response, opts) {
			var json = response.responseText;
			if(json){
				var respText = Ext.decode(json);
				var total=respText.total;
				var runtotal=respText.runtotal;
				var rollbackRunTotal=respText.rollbackRunTotal;
				var upgradeExceptionTotal=respText.upgradeExceptionTotal;
				var rollbackExceptionTotal=respText.rollbackExceptionTotal;
				var upgradeSuccessTotal=respText.upgradeSuccessTotal;
				var rollbackSuccessTotal=respText.rollbackSuccessTotal;
				transmissionview();
				monitorform.getForm().findField('total').setValue(total);
				monitorform.getForm().findField('runtotal').setValue(runtotal);
				monitorform.getForm().findField('rollbackRunTotal').setValue(rollbackRunTotal);
				monitorform.getForm().findField('upgradeExceptionTotal').setValue(upgradeExceptionTotal);
				monitorform.getForm().findField('rollbackExceptionTotal').setValue(rollbackExceptionTotal);
				monitorform.getForm().findField('upgradeSuccessTotal').setValue(upgradeSuccessTotal);
				monitorform.getForm().findField('rollbackSuccessTotal').setValue(rollbackSuccessTotal);
			}
		},
		failure: function(result, request) {
			Ext.Msg.alert('提示', "请求失败");
		}
	});
}
//升级和取消
function agentUpgrade(startName,runId){
	Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?', function(btn) {
		if (btn == 'yes') {
			Ext.Ajax.request({
				url: 'upgradeSchemeOperate.do',
				async:false,
				params: {
					runId:runId,
					startName:startName
				},
				method: 'POST',
				success: function(response, opts) {
					Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
					upgrade_strategy_store.load();
					upgrade_program_store.load();
				},
				failure: function(result, request) {
					Ext.Msg.alert('提示', "请求失败");
				}
			});
		}
	});
}
//查看页面
function viewPolicy(runid){
	Ext.Ajax.request({
		url: 'getAgentUpgradePlanTask.do',
		async:false,
		params: {
			runId: runid
		},
		method: 'POST',
		success: function(resp, opts) {
			var json = resp.responseText;
			if(json){
				var respText = Ext.decode(json);
				var tacticsName=respText.tacticsName;
				var planName=respText.planName;
				var agentVersion=respText.agentVersion;
				var itargetPath=respText.itargetPath;
				transmissionStrategy();
				transmissionStrategyFrom.getForm().findField('agentVersion').setValue(agentVersion);
				transmissionStrategyFrom.getForm().findField('planName').setValue(planName);
				transmissionStrategyFrom.getForm().findField('tacticsName').setValue(tacticsName);
				transmissionStrategyFrom.getForm().findField('itargetPath').setValue(itargetPath);
			}
		},
		failure: function(result, request) {
			Ext.Msg.alert('提示', "请求失败");
		}
	});
}


function viewlog(rowIndex){
	var record = upgrade_program_store.getAt(rowIndex);
	var funcDesc = Ext.create('Ext.form.field.TextArea', {
		columnWidth: 1,
		labelWidth: 65,
		padding: '0 4 0 8',
		height: 300,
		maxLength: 20000,
		name: 'iinstallMsg',
		autoScroll: true,
		value:record.data.ilog
	});

	var installmsgform = Ext.create('Ext.form.FormPanel', {
		border: false,
		bodyPadding: 10,
		bodyCls: 'x-docked-noborder-top',
		autoScroll: true,
		autoDestroy: false,
		buttonAlign: 'center',
		items:
			[{
				layout: "column", // 从左往右的布局column,从上往下form
				border: false,
				columnWidth: 1,
				items:
					[funcDesc]
			}],
	});
	var win1 = Ext.create('Ext.window.Window', {
		title : '安装日志信息',
		autoScroll : false,
		autoDestroy : true,
		closeable : true,
		modal : true,
		width : 600,
		height: 470,
		resizable : false,
		plain : true,
		layout : 'fit',
		draggable:true,
		items : [installmsgform]
	}).show();



}
//统计视图
var monitorform;
function transmissionview(){
	monitorform = Ext.create('Ext.form.FormPanel', {
		border: false,
		bodyPadding: 10,
		bodyCls: 'x-docked-noborder-top',
		autoScroll: true,
		autoDestroy: false,
		buttonAlign: 'center',
		items:
			[{
				layout: "column", // 从左往右的布局column,从上往下form
				border: false,
				columnWidth: 1,
				items:
					[{
						layout: "column", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									readOnly: true,
									fieldLabel: '执行总数',
									name: 'total',
									xtype: 'field',
									editable: false,
									typeAhead: false,
									anchor: '100%',
								}]
							}]
					},{
						layout: "column", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									readOnly: true,
									fieldLabel: '升级运行数',
									name: 'runtotal',
									xtype: 'field',
									editable: false,
									typeAhead: false,
									anchor: '100%',
								}]
							},
								{
									columnWidth: .5,
									border: false,
									items: [{
										fieldLabel: '回退运行数',
										name: 'rollbackRunTotal',
										xtype: 'field',
										typeAhead: false,
										anchor: '100%',
									}]
								}]

					},{
						layout: "column", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,

						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '升级异常数',
									name: 'upgradeExceptionTotal',
									xtype: 'field',
									typeAhead: false,
									anchor: '100%',
								}]
							},{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '回退异常数',
									name: 'rollbackExceptionTotal',
									xtype: 'field',
									typeAhead: false,
									anchor: '100%',
								}]
							}]
					},{
						layout: "column", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,

						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '升级完成数',
									name: 'upgradeSuccessTotal',
									xtype: 'field',
									typeAhead: false,
									anchor: '100%',
								}]
							},{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '回退完成数',
									name: 'rollbackSuccessTotal',
									xtype: 'field',
									typeAhead: false,
									anchor: '100%',
								}]
							}]
					}]
			}],
		buttons: [{
			text : '取消',
			handler : function() {
				var form = monitorform.getForm();
				form.owner.up('window').close();
			}
		}]
	});
	var win1 = Ext.create('Ext.window.Window', {
		title : '脚本管理',
		autoScroll : false,
		autoDestroy : true,
		closeable : true,
		modal : true,
		width : 700,
		height: 350,
		resizable : false,
		plain : true,
		layout : 'fit',
		draggable:true,
		items : [monitorform]
	}).show();
}
//查看按钮连接
function transmissionStrategy() {
	transmissionStrategyFrom = Ext.create('Ext.form.FormPanel', {
		border: false,
		bodyPadding: 10,
		bodyCls: 'x-docked-noborder-top',
		autoScroll: true,
		autoDestroy: false,
		buttonAlign: 'center',
		items:
			[{
				layout: "column", // 从左往右的布局column,从上往下form
				border: false,
				columnWidth: 1,
				items:
					[{
						layout: "form", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '方案名',
									width:400,
									name: 'tacticsName',
									xtype: 'field',
									editable: false,
									typeAhead: false,
									anchor: '100%',
									readOnly:true,
								}]
							}]
					},{
						layout: "form", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{

								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '升级策略',
									width:400,
									name: 'planName',
									xtype: 'field',
									editable: false,
									typeAhead: false,
									anchor: '100%',
									readOnly:true,
								}]
							}]
					},{
						layout: "form", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '升级包地址',
									name: 'itargetPath',
									width:400,
									xtype: 'field',
									editable: false,
									typeAhead: false,
									anchor: '100%',
									readOnly:true,
								}]
							}]

					},{
						layout: "form", // 从左往右的布局column,从上往下form
						border: false,
						columnWidth: 1,
						items:
							[{
								columnWidth: .5,
								border: false,
								items: [{
									fieldLabel: '升级版本',
									name: 'agentVersion',
									width:400,
									xtype: 'field',
									typeAhead: false,
									anchor: '100%',
									readOnly:true,
								}]
							}]
					}]
			}],
		buttons: [{
			text : '取消',
			handler : function() {
				var form = transmissionStrategyFrom.getForm();
				form.owner.up('window').close();
			}
		}]
	});
	var win1 = Ext.create('Ext.window.Window', {
		title : '查看策略',
		autoScroll : false,
		autoDestroy : true,
		closeable : true,
		modal : true,
		width : 600,
		height: 350,
		resizable : false,
		plain : true,
		layout : 'fit',
		draggable:true,
		items : [transmissionStrategyFrom]
	}).show();

}
