<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<script type="text/javascript">
	var taskid = '<%=request.getParameter("taskid")%>';
	// let CodeMirror={}
</script>
	<link href="<%=request.getContextPath()%>/js/cicd_flow/version_compare/codemirror.min.css" rel="stylesheet" type="text/css"/>
	<link href="<%=request.getContextPath()%>/js/cicd_flow/version_compare/merge.css" rel="stylesheet" type="text/css"/>
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/cicd_flow/version_compare/merge.min.js"></script>
	<!--代码变色-->
	<link href="<%=request.getContextPath()%>/js/cicd_flow/version_compare/codemirror-changecolor.css" rel="stylesheet" type="text/css"/>
	<!-- 引入 diff_match_patch.js 文件 -->
	<script type="text/javascript" src="<%=request.getContextPath()%>/js/cicd_flow/version_compare/diff_match_patch.js"></script>
</head>

<body>


  <div class="layui-row reset_row">
	      <div class="layui-col-md4 reset_row">
		    <div class="col-xs-4">
				<div class="layout_bg01">
					<div id="CIVersionCompare_div"></div>
				</div>
			</div>
		  </div>

			<div class="layui-col-md8 reset_row">
				<div class="col-xs-4">
				    <div class="layout_bg01" >
						<div style="width:100%;">
							<span style="width:48%;display: inline-block;margin-left: 10px;margin-top: 10px;margin-bottom: 5px;"id="leftVersion"></span>
							<span style="width:48%;display: inline-block;margin-left: 15px;margin-top: 10px;margin-bottom: 5px;"id="rightVersion"></span>
						</div>

						<div class="layout_cn">
							<div id="compare_div" style="width:100%;"></div>
						</div>
				    </div>
				</div>
			</div>
  </div>
  <script>

	  var mirrorGlobal='';
	  function initMirror(){
		  var a=document.getElementById('compare_div')
		  mirrorGlobal=CodeMirror.MergeView(a,{
			  value:'',
			  origLeft:null,
			  orig:'',
			  lineNumbers:true,
			  mode:'java',
			  highlightDifference:'highlight',
			  connect:'align',
			  readOnly:true,
			  theme:'lesser-dark'
		  })
	  }

	  function leftFunc(e){
		  let letfData = e
		  mirrorGlobal.edit.doc.setValue(letfData);
		  CodesStyleLb()
	  }
	  function rightFunc(e){
		  let rightData = e
		  mirrorGlobal.right.orig.doc.setValue(rightData);
		  delCodesStyleLb()
	  }
	  function CodesStyleLb(){
		  var styleElementS = document.createElement('style');console.log(33)
		  styleElementS.className='lbSEee'
		  // 在 <style> 元素中添加 CSS 样式规则
		  styleElementS.innerHTML = `
		   .CodeMirror-merge-r-inserted, .CodeMirror-merge-l-inserted {
		      background-image:none !important;
		   }
		   .CodeMirror-merge-r-deleted, .CodeMirror-merge-l-deleted{
		 			   background-image:none !important;
		   }

		 `;
		  var styleElement = document.createElement('style');console.log(33)
		  styleElement.className='lbEee'
		  // 在 <style> 元素中添加 CSS 样式规则
		  styleElement.innerHTML = `

		   .CodeMirror-merge-r-chunk.CodeMirror-linebackground{
			   background:transparent !important;
		   }
		 `;

		  // 将 <style> 元素添加到页面的 <head> 部分
		  document.head.appendChild(styleElementS);
		  document.head.appendChild(styleElement);
	  }
	  function delCodesStyleLb(){
		  // 获取所有具有 class="lbEee" 的元素
		  var elementsToRemove = document.querySelectorAll('.lbEee');

		  // 遍历所有匹配的元素并删除它们
		  for (var i = 0; i < elementsToRemove.length; i++) {
			  var element = elementsToRemove[i];
			  element.parentNode.removeChild(element);
		  }
	  }
  </script>
  <script type="text/javascript" src="<%=request.getContextPath()%>/page/sus/CICD_FlowTask/CICD_VersionCompare.js"></script>
</body>
</html>