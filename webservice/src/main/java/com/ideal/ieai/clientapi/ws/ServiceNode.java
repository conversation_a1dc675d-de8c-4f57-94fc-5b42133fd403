/**
 * ServiceNode.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.ideal.ieai.clientapi.ws;

public class ServiceNode  implements java.io.Serializable {
    private com.ideal.ieai.clientapi.ws.Address address;

    private int currentState;

    private java.lang.String domainName;

    private java.lang.String host;

    private com.ideal.ieai.clientapi.ws.Load load;

    private int previousState;

    private int runningFlows;

    private long uptime;

    private com.ideal.ieai.clientapi.ws.Address _address;

    private int _currState;

    private int _previousState;

    private com.ideal.ieai.clientapi.ws.Load _load;

    private long _createTime;

    private int _runningFlows;

    private java.lang.String _domainName;

    public ServiceNode() {
    }

    public ServiceNode(
           com.ideal.ieai.clientapi.ws.Address address,
           int currentState,
           java.lang.String domainName,
           java.lang.String host,
           com.ideal.ieai.clientapi.ws.Load load,
           int previousState,
           int runningFlows,
           long uptime,
           com.ideal.ieai.clientapi.ws.Address _address,
           int _currState,
           int _previousState,
           com.ideal.ieai.clientapi.ws.Load _load,
           long _createTime,
           int _runningFlows,
           java.lang.String _domainName) {
           this.address = address;
           this.currentState = currentState;
           this.domainName = domainName;
           this.host = host;
           this.load = load;
           this.previousState = previousState;
           this.runningFlows = runningFlows;
           this.uptime = uptime;
           this._address = _address;
           this._currState = _currState;
           this._previousState = _previousState;
           this._load = _load;
           this._createTime = _createTime;
           this._runningFlows = _runningFlows;
           this._domainName = _domainName;
    }


    /**
     * Gets the address value for this ServiceNode.
     * 
     * @return address
     */
    public com.ideal.ieai.clientapi.ws.Address getAddress() {
        return address;
    }


    /**
     * Sets the address value for this ServiceNode.
     * 
     * @param address
     */
    public void setAddress(com.ideal.ieai.clientapi.ws.Address address) {
        this.address = address;
    }


    /**
     * Gets the currentState value for this ServiceNode.
     * 
     * @return currentState
     */
    public int getCurrentState() {
        return currentState;
    }


    /**
     * Sets the currentState value for this ServiceNode.
     * 
     * @param currentState
     */
    public void setCurrentState(int currentState) {
        this.currentState = currentState;
    }


    /**
     * Gets the domainName value for this ServiceNode.
     * 
     * @return domainName
     */
    public java.lang.String getDomainName() {
        return domainName;
    }


    /**
     * Sets the domainName value for this ServiceNode.
     * 
     * @param domainName
     */
    public void setDomainName(java.lang.String domainName) {
        this.domainName = domainName;
    }


    /**
     * Gets the host value for this ServiceNode.
     * 
     * @return host
     */
    public java.lang.String getHost() {
        return host;
    }


    /**
     * Sets the host value for this ServiceNode.
     * 
     * @param host
     */
    public void setHost(java.lang.String host) {
        this.host = host;
    }


    /**
     * Gets the load value for this ServiceNode.
     * 
     * @return load
     */
    public com.ideal.ieai.clientapi.ws.Load getLoad() {
        return load;
    }


    /**
     * Sets the load value for this ServiceNode.
     * 
     * @param load
     */
    public void setLoad(com.ideal.ieai.clientapi.ws.Load load) {
        this.load = load;
    }


    /**
     * Gets the previousState value for this ServiceNode.
     * 
     * @return previousState
     */
    public int getPreviousState() {
        return previousState;
    }


    /**
     * Sets the previousState value for this ServiceNode.
     * 
     * @param previousState
     */
    public void setPreviousState(int previousState) {
        this.previousState = previousState;
    }


    /**
     * Gets the runningFlows value for this ServiceNode.
     * 
     * @return runningFlows
     */
    public int getRunningFlows() {
        return runningFlows;
    }


    /**
     * Sets the runningFlows value for this ServiceNode.
     * 
     * @param runningFlows
     */
    public void setRunningFlows(int runningFlows) {
        this.runningFlows = runningFlows;
    }


    /**
     * Gets the uptime value for this ServiceNode.
     * 
     * @return uptime
     */
    public long getUptime() {
        return uptime;
    }


    /**
     * Sets the uptime value for this ServiceNode.
     * 
     * @param uptime
     */
    public void setUptime(long uptime) {
        this.uptime = uptime;
    }


    /**
     * Gets the _address value for this ServiceNode.
     * 
     * @return _address
     */
    public com.ideal.ieai.clientapi.ws.Address get_address() {
        return _address;
    }


    /**
     * Sets the _address value for this ServiceNode.
     * 
     * @param _address
     */
    public void set_address(com.ideal.ieai.clientapi.ws.Address _address) {
        this._address = _address;
    }


    /**
     * Gets the _currState value for this ServiceNode.
     * 
     * @return _currState
     */
    public int get_currState() {
        return _currState;
    }


    /**
     * Sets the _currState value for this ServiceNode.
     * 
     * @param _currState
     */
    public void set_currState(int _currState) {
        this._currState = _currState;
    }


    /**
     * Gets the _previousState value for this ServiceNode.
     * 
     * @return _previousState
     */
    public int get_previousState() {
        return _previousState;
    }


    /**
     * Sets the _previousState value for this ServiceNode.
     * 
     * @param _previousState
     */
    public void set_previousState(int _previousState) {
        this._previousState = _previousState;
    }


    /**
     * Gets the _load value for this ServiceNode.
     * 
     * @return _load
     */
    public com.ideal.ieai.clientapi.ws.Load get_load() {
        return _load;
    }


    /**
     * Sets the _load value for this ServiceNode.
     * 
     * @param _load
     */
    public void set_load(com.ideal.ieai.clientapi.ws.Load _load) {
        this._load = _load;
    }


    /**
     * Gets the _createTime value for this ServiceNode.
     * 
     * @return _createTime
     */
    public long get_createTime() {
        return _createTime;
    }


    /**
     * Sets the _createTime value for this ServiceNode.
     * 
     * @param _createTime
     */
    public void set_createTime(long _createTime) {
        this._createTime = _createTime;
    }


    /**
     * Gets the _runningFlows value for this ServiceNode.
     * 
     * @return _runningFlows
     */
    public int get_runningFlows() {
        return _runningFlows;
    }


    /**
     * Sets the _runningFlows value for this ServiceNode.
     * 
     * @param _runningFlows
     */
    public void set_runningFlows(int _runningFlows) {
        this._runningFlows = _runningFlows;
    }


    /**
     * Gets the _domainName value for this ServiceNode.
     * 
     * @return _domainName
     */
    public java.lang.String get_domainName() {
        return _domainName;
    }


    /**
     * Sets the _domainName value for this ServiceNode.
     * 
     * @param _domainName
     */
    public void set_domainName(java.lang.String _domainName) {
        this._domainName = _domainName;
    }

    private java.lang.Object __equalsCalc = null;
    public synchronized boolean equals(java.lang.Object obj) {
        if (!(obj instanceof ServiceNode)) return false;
        ServiceNode other = (ServiceNode) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.address==null && other.getAddress()==null) || 
             (this.address!=null &&
              this.address.equals(other.getAddress()))) &&
            this.currentState == other.getCurrentState() &&
            ((this.domainName==null && other.getDomainName()==null) || 
             (this.domainName!=null &&
              this.domainName.equals(other.getDomainName()))) &&
            ((this.host==null && other.getHost()==null) || 
             (this.host!=null &&
              this.host.equals(other.getHost()))) &&
            ((this.load==null && other.getLoad()==null) || 
             (this.load!=null &&
              this.load.equals(other.getLoad()))) &&
            this.previousState == other.getPreviousState() &&
            this.runningFlows == other.getRunningFlows() &&
            this.uptime == other.getUptime() &&
            ((this._address==null && other.get_address()==null) || 
             (this._address!=null &&
              this._address.equals(other.get_address()))) &&
            this._currState == other.get_currState() &&
            this._previousState == other.get_previousState() &&
            ((this._load==null && other.get_load()==null) || 
             (this._load!=null &&
              this._load.equals(other.get_load()))) &&
            this._createTime == other.get_createTime() &&
            this._runningFlows == other.get_runningFlows() &&
            ((this._domainName==null && other.get_domainName()==null) || 
             (this._domainName!=null &&
              this._domainName.equals(other.get_domainName())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getAddress() != null) {
            _hashCode += getAddress().hashCode();
        }
        _hashCode += getCurrentState();
        if (getDomainName() != null) {
            _hashCode += getDomainName().hashCode();
        }
        if (getHost() != null) {
            _hashCode += getHost().hashCode();
        }
        if (getLoad() != null) {
            _hashCode += getLoad().hashCode();
        }
        _hashCode += getPreviousState();
        _hashCode += getRunningFlows();
        _hashCode += new Long(getUptime()).hashCode();
        if (get_address() != null) {
            _hashCode += get_address().hashCode();
        }
        _hashCode += get_currState();
        _hashCode += get_previousState();
        if (get_load() != null) {
            _hashCode += get_load().hashCode();
        }
        _hashCode += new Long(get_createTime()).hashCode();
        _hashCode += get_runningFlows();
        if (get_domainName() != null) {
            _hashCode += get_domainName().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ServiceNode.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "ServiceNode"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("address");
        elemField.setXmlName(new javax.xml.namespace.QName("", "address"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "Address"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("currentState");
        elemField.setXmlName(new javax.xml.namespace.QName("", "currentState"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("domainName");
        elemField.setXmlName(new javax.xml.namespace.QName("", "domainName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("host");
        elemField.setXmlName(new javax.xml.namespace.QName("", "host"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("load");
        elemField.setXmlName(new javax.xml.namespace.QName("", "load"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "Load"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("previousState");
        elemField.setXmlName(new javax.xml.namespace.QName("", "previousState"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("runningFlows");
        elemField.setXmlName(new javax.xml.namespace.QName("", "runningFlows"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("uptime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "uptime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_address");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_address"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "Address"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_currState");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_currState"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_previousState");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_previousState"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_load");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_load"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://webservice.v30.ieai.ideal.com", "Load"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_createTime");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_createTime"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "long"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_runningFlows");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_runningFlows"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("_domainName");
        elemField.setXmlName(new javax.xml.namespace.QName("", "_domainName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           java.lang.String mechType, 
           java.lang.Class _javaType,  
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
