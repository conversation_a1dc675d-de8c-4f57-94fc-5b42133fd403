package com.ideal.controller.webstudio;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.webstudio.commons.WebStudioConstants;
import com.ideal.ieai.server.repository.webstudio.model.WebStudioReleaseParam;
import com.ideal.ieai.server.repository.webstudio.project.DailyProjectDtoManager;
import com.ideal.ieai.server.repository.webstudio.project.ProjectInfoBean;
import com.ideal.ieai.server.repository.webstudio.project.WebStudioProjectPageBean;
import com.ideal.service.shutdown.webstudio.ShutdownService;
import com.ideal.service.webstudio.ProjectService;
import net.sf.json.JSONArray;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * @ClassName: ShutdownController
 * @Description:关机维护 webstudio
 * @author: lei_wang
 * @date: 2021年11月16日15:57:10
 * @Copyright: 2021-2027 www.idealinfo.com Inc. All rights reserved.
 */
@Controller
public class ShutdownController {

    private static final String PROJECTNAME = "projectname";
    private static final String PROJECTTYPEID = "projectTypeId";
    private static final String JSONDATA = "jsonData";
    private ProjectController pc = new ProjectController();
    private ProjectService service = new ProjectService();
    private DailyProjectDtoManager pm = new DailyProjectDtoManager();

    @RequestMapping("shutdownProjectList.do")
    public String shutdownProjectList() {
        return "shutdown/templateWS/shutdownProjectList";
    }

    @RequestMapping("shutdown/deleteProject.do")
    @ResponseBody
    public Map<String, Object> dailydeleteProject(HttpServletRequest request) {
        ProjectService service = new ProjectService();
        String idlist = request.getParameter("deleteIds");
        String deleteinfo = request.getParameter("deleteinfo");
        return service.fakeDelProjects(idlist, deleteinfo, Constants.IEAI_SHUTDOWN_MAINTAIN);
    }

    @RequestMapping("shutdown/getProjectInfoList.do")
    @ResponseBody
    public Map getAllConfigs(HttpServletRequest request) throws RepositoryException {

        SessionData sessionData = SessionData.getSessionData(request);
        String userid = sessionData.getUserInnerCode();
        int curPage = 1;
        int pageSize = 50;

        String strJson = request.getParameter(PROJECTNAME);

        long projectTypeId = Long.parseLong(request.getParameter(PROJECTTYPEID));
        String sort = request.getParameter("sort");
        String limit = request.getParameter("limit");
        String start = request.getParameter("start");
        String acurPage = request.getParameter("acurPage");
        String framework = request.getParameter("framework");

        if (limit != null && start != null) {
            if (!"".equals(acurPage)) {
                curPage = Integer.parseInt(acurPage);
            } else {
                curPage = Integer.parseInt(start) / Integer.parseInt(limit) + 1;
            }
            pageSize = Integer.parseInt(limit);
        }

        if (strJson.contains("'")) {
            if (strJson.equals("'")) {
                strJson = "''";
            } else {
                strJson = strJson.replaceAll("'", "''");
            }
        }

        Map<String, String> sortMap = getSortMap(sort);
        WebStudioProjectPageBean webStudioProjectPageBean = new WebStudioProjectPageBean();
        webStudioProjectPageBean.setCurPage(curPage);
        webStudioProjectPageBean.setSearchstr(strJson);
        webStudioProjectPageBean.setIeaiIeai(Constants.IEAI_SHUTDOWN_MAINTAIN);
        webStudioProjectPageBean.setPageSize(pageSize);
        webStudioProjectPageBean.setProjectTypeId(projectTypeId);
        webStudioProjectPageBean.setSortMap(sortMap);
        webStudioProjectPageBean.setUserid(userid);

        return ShutdownService.getInstance().getProjectList(webStudioProjectPageBean, framework);

    }

    @RequestMapping("shutdown/saveProjectInfo.do")
    @ResponseBody
    public Map<String, Object> saveProjectsRecord(HttpServletRequest request, HttpServletResponse response) {
        String strJson = request.getParameter(JSONDATA);
        SessionData sessionData = SessionData.getSessionData(request);
        String userName = sessionData.getLoginName();
        List<ProjectInfoBean> projectList = new ArrayList<>();

        if (strJson.length() > 0) {
            projectList = jsonTranslateToBean(strJson);
        }

        Map<String, Object> ret = null;

        /*
         * 1、首先区分新增和更新。因为更新不涉及快捷创建。只有新增涉及到快捷创建。
         * 2、新增部分后续需要进入到模板创建还是普通创建
         * 3、更新部分直接使用原创建逻辑
         */

        List<ProjectInfoBean> insertList = new ArrayList<>();
        List<ProjectInfoBean> updateList = new ArrayList<>();

        for (ProjectInfoBean project : projectList) {
            if (project.getIid() == WebStudioConstants.INITID) {
                insertList.add(project);
            } else {
                updateList.add(project);
            }
        }

        if (!insertList.isEmpty()) {
            String modelShutdown = Environment.getInstance().getShutdownDefaultProject();
            ret = service.createByCopy(userName, modelShutdown,
                    insertList, Constants.IEAI_SHUTDOWN_MAINTAIN);
            if (!ret.isEmpty() && (boolean) ret.get(Constants.COMMON_SUCCESS)) {
                insertList.forEach(r ->
                {
                    ShutdownService.getInstance().updateiremakesByName(r.getItypeid(), r.getIprojectname(), r.getIremarks(), r.getIframework(),
                            Constants.IEAI_SHUTDOWN_MAINTAIN);
                });
            }
        }
        if (!updateList.isEmpty()) {
            ret = ShutdownService.getInstance().saveProjectInfos(userName, updateList, Constants.IEAI_SHUTDOWN_MAINTAIN);
        }
        return ret;

    }

    /**
     * @param
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @Title: shutdownreleaseProject
     * @Description 关机维护批量发布工程
     * <AUTHOR>
     * @date 2021年12月13日 16:27:07
     **/
    @RequestMapping("shutdown/releaseProject.do")
    @ResponseBody
    public Map<String, Object> shutdownreleaseProject(HttpServletRequest request, HttpServletResponse response) {

        List<Map<String, Object>> res = new ArrayList<>();

        String jsonData = request.getParameter(JSONDATA);
        SessionData sessionData = SessionData.getSessionData(request);
        String projectTypeId = request.getParameter("projectTypeId");
        List<WebStudioReleaseParam> paralist = jsonTranslateToBeanParam(sessionData, projectTypeId, jsonData);
        for (WebStudioReleaseParam para : paralist) {
            Map<String, Object> map = new HashMap<>();
            if (!sessionData.getUserName().equals(para.getUserName())) {
                map.put(Constants.COMMON_SUCCESS, false);
                map.put(Constants.COMMON_MESSAGE, "非当前用户创建,无法发布");
            } else {
                map = service.releaseProject(para, Constants.IEAI_SHUTDOWN_MAINTAIN,1);
            }
            map.put("projectName", para.getProjectname());

            /**
             * 根据map中的数据来判断工程是否发布成功，如果成功，则更新更新工程的发布时间，否则，则不更新工程的发布时间
             */
            boolean flag = (Boolean) map.get(Constants.COMMON_SUCCESS);

            if (flag) {
                service.updatereleasetime(para.getProjectname(), para.getProjectTypeId(), Constants.IEAI_SHUTDOWN_MAINTAIN);
            }
            res.add(map);
        }
        Map<String, Object> resMap = new HashMap();
        StringBuilder sbf = new StringBuilder();
        for (Map<String, Object> re : res) {
            boolean flag = (Boolean) re.get(Constants.COMMON_SUCCESS);
            if (!flag) {
                sbf.append(re.get("projectName") + "_" + re.get(Constants.COMMON_MESSAGE) + "</br>");
            }
        }
        if (sbf.length() == 0) {
            resMap.put(Constants.COMMON_SUCCESS, true);
            resMap.put(Constants.COMMON_MESSAGE, "发布成功！");

        } else {
            resMap.put(Constants.COMMON_SUCCESS, false);
            resMap.put(Constants.COMMON_MESSAGE, sbf);
        }
        return resMap;
    }


    @RequestMapping("shutdown/exportProjects.do")
    @ResponseBody
    public Map exportProject(HttpServletRequest request, HttpServletResponse response) {
        String exportIds = request.getParameter("exportIds");
        String check = request.getParameter("check");
        if (check.equals("0")) {
            Map map = new HashMap();
            map.put("success", true);
            return map;
        }
        service.exportProjectInfosIf(response, exportIds, Constants.IEAI_SHUTDOWN_MAINTAIN);
        Map map = new HashMap();
        map.put("success", true);
        return map;
    }

    @RequestMapping("shutdown/importProjects.do")
    @ResponseBody
    public Map<String, Object> uploadFunctionJson(@RequestParam("fileName") CommonsMultipartFile file,
                                                  HttpServletRequest request, HttpServletResponse response) {
        SessionData sessionData = SessionData.getSessionData(request);
        String userName = sessionData.getLoginName();
        return service.importProjectInfos(userName, file, Constants.IEAI_SHUTDOWN_MAINTAIN);
    }

    private List<WebStudioReleaseParam> jsonTranslateToBeanParam(SessionData sessionData, String projectTypeId, String jsonData) {
        List<WebStudioReleaseParam> res = new LinkedList();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData) {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++) {
                WebStudioReleaseParam param = new WebStudioReleaseParam();
                param.setProjectId(jsonArr.getJSONObject(i).optString("iid"));
                param.setProjectTypeId(projectTypeId);
                param.setProjectname(jsonArr.getJSONObject(i).optString("iprojectname"));
                param.setReleaseMode("本地");
                param.setUserName(jsonArr.getJSONObject(i).optString("icreateuser"));
                param.setUserId(sessionData.getUserInnerCode());
                res.add(param);
            }
        }
        return res;

    }

    private List<ProjectInfoBean> jsonTranslateToBean(String jsonData) {
        List<ProjectInfoBean> dataList = new ArrayList<ProjectInfoBean>();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData) {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            long id;
            long typeid;
            Long dailytype;
            String iid;
            String projectname;
            String version;
            String remark;
            String type;
            String isystemtypeuuid;
            String iframework;

            for (int i = 0; i < jsonArr.size(); i++) {
                ProjectInfoBean u = new ProjectInfoBean();
                projectname = jsonArr.getJSONObject(i).optString("iprojectname");
                version = jsonArr.getJSONObject(i).optString("iversion");
                remark = jsonArr.getJSONObject(i).optString("iremarks");
                type = jsonArr.getJSONObject(i).optString("itypeid");
                dailytype = jsonArr.getJSONObject(i).optLong("dailytype");
                isystemtypeuuid = jsonArr.getJSONObject(i).optString("isystemtypeuuid");
                iframework = jsonArr.getJSONObject(i).optString("iframework");

                iid = jsonArr.getJSONObject(i).optString("iid");
                if (iid == null || "".equals(iid)) {
                    id = -1;
                } else {
                    id = Long.parseLong(iid);
                }
                typeid = Long.parseLong(type);
                u.setIid(id);
                u.setDailytype(dailytype);
                u.setIprojectname(projectname);
                u.setIversion(version);
                u.setIremarks(remark);
                u.setItypeid(typeid);
                u.setIsystemtypeuuid(isystemtypeuuid);
                u.setIframework(iframework);
                dataList.add(u);
            }
        }
        return dataList;
    }

    public Map<String, String> getSortMap(String sort) {
        Map<String, String> sortMap = new HashMap<>();
        if (sort != null) {
            JSONArray jsonArr = JSONArray.fromObject(sort);
            String sortName = jsonArr.getJSONObject(0).optString("property");
            String sortOrder = jsonArr.getJSONObject(0).optString("direction");
            if (sortName.equals("isendtime")) {
                sortName = "ISENDTIME";
            } else if (sortName.equals("irepoertid")) {
                sortName = "IREPOERTID";
            } else {
                sortName = "IID";
            }
            sortMap.put("column", sortName);
            sortMap.put("sortorder", sortOrder);
        } else {
            sortMap.put("column", "Iiusername");
            sortMap.put("sortorder", "ASC");
        }
        return sortMap;
    }
}
