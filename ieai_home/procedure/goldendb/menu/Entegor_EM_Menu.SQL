INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (6,'应急操作','应急操作模块组',6,'images/info84.png') ;
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6001, '应急发起', 6, 'flowstartworkflowForEM.do', 1, '应急操作', 'images/info37.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6009, '应急监控', 6, 'emMonitor.do', 2, '应急操作','images/info20.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6008, '图形化应急监控', 6, 'forwardSwitchMonitorGraph_ep.do', 4, '应急操作','images/info22.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6005, '系统录入', 6, 'eminfoexecl.do', 5, '应急操作','images/info149.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6004, '系统维护', 6, 'eminstanceConfigHome.do', 6, '应急操作','images/info282.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6007, '历史查询', 6, 'forwardswitchrunins_ep.do', 7, '应急操作','images/info37.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6011, '应急分类', 6, 'instanceClassify.do', 7, '应急操作','images/info37.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(6056, '应急接口API', 6, 'emInterfaceAPI.do', 7, '应急操作','images/info37.png');


INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6003, '提交', 'creatInsgdbForEM.do', '应急发起页面提交按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6004, '打回', 'backWorkitemByiidForEM.do', '应急任务审核页面打回按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6005, '启动', 'submitInsgdbForEM.do', '应急任务审核页面启动按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6006, '提交', 'createWorkitemByiidForEM.do', '复核编辑页面提交按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6007, '终止', 'stopWorkitemByiidForEM.do', '复核编辑页面终止按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6008, '删除', 'deleteInstanceForEm.do', '应急系统维护删除业务系统按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6009, '保存', 'updateInstanceBasicInfoForEM.do', '应急系统维护删除业务系统按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6010, '保存', 'updateInstanceInsInfo_ep.do', '应急系统维护保存系统信息');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6001, '上传', 'uploadEmExecl.do', '系统录入 上传按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6011, '重试', 'errorOperRepeatEmRetry.do', '应急系统监控异常处理重试按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6012, '略过', 'errorOperRepeatEmSkip.do', '应急系统监控异常处理略过按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6013, '重试', 'errorOperRepeatEmGraphRetry.do', '应急图形系统监控异常处理重试按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6014, '略过', 'errorOperRepeatEmGraphSkip.do', '应急图形系统监控异常处理略过按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6027, '查询应急预案实例详情接口', 'getEMInstanceDetail.do', '查询应急预案实例详情接口');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(6028, '查询应急预案实例下步骤详情接口', 'getEMInstanceStepInfo.do', '查询应急预案实例下步骤详情接口');


INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6003, 6001, 6003, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6004, 6001, 6004, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6005, 6001, 6005, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6006, 6001, 6006, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6007, 6001, 6007, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6008, 6004, 6008, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6009, 6004, 6009, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6010, 6004, 6010, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6001, 6005, 6001, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6013, 6008, 6013, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6014, 6008, 6014, '');

INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6011, 6009, 6011, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6012, 6009, 6012, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6033, 6056, 6027, '');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(6034, 6056, 6028, '');