package com.ideal.ieai.server.repository.monitor;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.SendFailedException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;

import org.apache.log4j.Logger;

public class MailWarnClass
{
    private static final Logger _log        = Logger.getLogger(MailWarnClass.class);
    private int                 _smtpPort;
    private String              _TsmtpPort;
    private String              _smtpHost;
    private List                _mailStress = new ArrayList();
    private String              _iactName;
    private String              _SmtpUser;
    private String              _Info;
    private String              _authInfo;
    private long                _actId;
    private String              authuser;
    private String              authpwd;

    public MailWarnClass(List mailStress, String iactName, String smtpPort, String smtpAddr, String smtpUser, String info, long actId, String authInfo)
    {
        super();
        _TsmtpPort = smtpPort;
        _smtpHost = smtpAddr;
        _mailStress = mailStress;
        _SmtpUser = smtpUser;
        _iactName = iactName;
        _Info = info;
        _actId = actId;
        _authInfo = authInfo;
    }

    public void run ()
    {

        try
        {
            Long port = new Long(_TsmtpPort.trim());
            if (null != port)
            {
                this._smtpPort = port.intValue();
            }
        } catch (Exception ex)
        {
            _log.warn("Act input config invalid port,use default port 25.");
            _smtpPort = 25;
        }
        Session session = null;
        try
        {
            session = getSmtpSession();
        } catch (MessagingException e4)
        {
            // TODO Auto-generated catch block
            e4.printStackTrace();
        }
        // create a message
        Message msg = new MimeMessage(session);
        // set the from and to address
        InternetAddress addressFrom = InternetAddress.getLocalAddress(session);
        try
        {
            msg.setFrom(addressFrom);
            String fromuser = "";
            String mailUser = "";
            try
            {
                if (_SmtpUser.indexOf("[") != -1)
                {
                    int m = _SmtpUser.indexOf("[");
                    mailUser = _SmtpUser.substring(0, m);
                    fromuser = _SmtpUser.substring(m + 1, _SmtpUser.length());
                }
            } catch (Exception e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            try
            {
                msg.setFrom(new InternetAddress("\"" + MimeUtility.encodeText(mailUser) + "\" <" + fromuser + ">"));
            } catch (UnsupportedEncodingException e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        } catch (MessagingException e3)
        {
            _log.warn("Set mail from failed!", e3);
        }

        // set to
        if (_mailStress != null)
        {
            InternetAddress[] addressTo = revertIEAIObjToAddress(_mailStress);
            for (int i = 0; i < addressTo.length; i++)
            {
                List tempList = new ArrayList();
                tempList.add(addressTo[i]);
                InternetAddress[] tmpaddressTo = revertIEAIObjToAddress(tempList);
                try
                {
                    msg.setRecipients(Message.RecipientType.TO, tmpaddressTo);
                    // Setting the Subject
                    try
                    {
                        if (_actId == 1)
                        {

                            msg.setSubject((String) _iactName + " : 业务发生异常报警!");
                        } else
                        {
                            msg.setSubject(" 系统发生异常报警!");
                        }
                    } catch (MessagingException e2)
                    {
                        _log.warn("Set mail subject failed!");
                    }
                    // set body
                    MimeMultipart mp = new MimeMultipart();
                    MimeBodyPart bpBody = new MimeBodyPart();
                    try
                    {
                        String bodyType;
                        String charset = "GBK";
                        if (null == charset || charset.equals(""))
                        {
                            bodyType = "text/plain";
                        } else
                        {
                            bodyType = "text/plain" + ";charset=" + charset;
                        }
                        if (null == _Info)
                        {
                            bpBody.setContent("", bodyType);
                        } else
                        {
                            bpBody.setContent(_Info, bodyType);
                        }
                    } catch (MessagingException e1)
                    {
                        _log.warn("Set mail body content failed!", e1);
                    }
                    try
                    {
                        mp.addBodyPart(bpBody);
                    } catch (MessagingException e1)
                    {
                        _log.warn("Add body part failed!", e1);
                    }

                    try
                    {
                        msg.setContent(mp);
                    } catch (MessagingException ex)
                    {
                        _log.warn("Set mail body failed!", ex);
                    }
                    try
                    {
                        Transport.send(msg);
                        tempList = null;
                        _log.info("Send mail succeed.");
                    } catch (MessagingException e)
                    {
                        if (e instanceof SendFailedException)
                        {
                            int m = 0;
                            for (int j = 0; j < 3; j++)
                            {
                                try
                                {
                                    if (m >= 3)
                                    {
                                        return;
                                    } else
                                    {
                                        Transport.send(msg);
                                    }
                                } catch (MessagingException e1)
                                {
                                    m++;
                                }
                            }
                        }
                        _log.info(_iactName + " :  Send mail fail.", e);
                    }
                } catch (MessagingException e)
                {
                    _log.warn("Set mail to failed!", e);
                }
            }
        }
        // 暂时业务异常不调用Tivoli集成模块
        // MonitorSyslog monitor = new MonitorSyslog();
        // monitor.getInfoOper(_actId, 4, System
        // .currentTimeMillis(), _iactName+":"+_Info);

    }

    private Session getSmtpSession () throws MessagingException
    {
        Properties props = System.getProperties();
        props.put("mail.smtp.host", this._smtpHost);
        if (this._smtpPort != -1)
        {
            props.put("mail.smtp.port", String.valueOf(_smtpPort));
        }
        if (!"".equals(_authInfo) && _authInfo != null)
        {
            String[] currentauth = _authInfo.split(";");
            authuser = String.valueOf(currentauth[0]);
            authpwd = String.valueOf(currentauth[1]);
        }
        props.put("mail.smtp.auth", "true");
        Authenticator auth = null;
        auth = new PlainAuthenticator(this.authuser, this.authpwd);
        Session session = Session.getInstance(props, auth);

        // Session session = Session.getInstance(props);
        // session.setDebug(true);
        // try {
        // transport = session.getTransport( "smtp");
        // transport.connect("mail.smtp.host", this.authuser, this.authpwd );
        // } catch (NoSuchProviderException e) {
        // e.printStackTrace();
        // }
        return session;
    }

    public static InternetAddress[] revertIEAIObjToAddress ( List addressList )
    {
        InternetAddress[] address = new InternetAddress[addressList.size()];
        for (int i = 0, size = addressList.size(); i < size; i++)
        {
            address[i] = (InternetAddress) addressList.get(i);
        }
        return address;
    }
}
