CREATE OR <PERSON><PERSON><PERSON><PERSON> PROCEDURE PROC_SAVE_WARNING_MESSAGE
 		(
 			AI_WCODE IN SMALLINT,
 			AV_WDATE IN VARCHAR2,
 			AV_WSOURCE IN VARCHAR2,
 			<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON> IN VARCHAR2,
 			AN_ERRCODE IN NUMBER,
 			AV_HOST IN VARCHAR2,
 			AV_<PERSON> IN VARCHAR2,
 			AV_CVALUE IN VARCHAR2,
 			AV_THREADHOLD IN VARCHAR2,
 			AV_AMSG IN VARCHAR2,
 			AN_CPID IN NUMBER,
 			AV_WARN_CODE_LIST IN VARCHAR2,
 			AN_KEY OUT NUMBER,
 			AV_RETURN OUT VARCHAR2
 		)
 		AS
 	    BEGIN
 		DECLARE	
             LI_EXISTS	SMALLINT;
             LI_COUNT	INTEGER;
 			LN_KEY		NUMERIC(19);
 			LI_LOOP		INTEGER;
 			LI_LENGTH	INTEGER;
 			LV_DATA		VARCHAR2(500);
 			LI_DATA		INTEGER;
 			LI_SWITCH	SMALLINT;
             LI_COM_COUT NUMERIC(19);
 	  BEGIN	
 		AN_KEY := -1;
 		
 		SELECT	NVL(MAX(IID), -1)
 		INTO		LN_KEY
 		FROM		IEAI_HC_ACTWARNING
 		WHERE		IP = AV_IP
 		AND		ERRCODE = AN_ERRCODE
 		AND		PRGNAME = AV_PRGNAME
 		AND		WCODE = AI_WCODE
 		AND		THREADHOLD = AV_THREADHOLD
 		AND		AMESSAGE = AV_AMSG;
 		
 		IF	LN_KEY >= 0 THEN
 			UPDATE	IEAI_HC_ACTWARNING
 			SET		WCOUNT = WCOUNT + 1,
 						LDATE =TO_DATE(AV_WDATE,'YYYY-MM-DD HH24:mi:ss'),
 						CHKVALUE = AV_CVALUE
 			WHERE	IID = LN_KEY;
 
 		ELSE
 			PROC_GET_NEXT_PK('IEAI_HC_ACTWARNING', LN_KEY);
 			INSERT INTO IEAI_HC_ACTWARNING
 					(
 						IID,
 						WCODE,
 						WDATE,
 						LDATE,
 						WSOURCE,
 						PRGNAME,
 						ERRCODE,
 						HOSTNAME,
 						IP,
 						CHKVALUE,
 						THREADHOLD,
 						AMESSAGE,
 						WCOUNT,
 						TFLAG,
 						WMESSAGE,
 						SYSNAME,
 						CPID
 					)
 			SELECT	LN_KEY,
 						AI_WCODE,					
                         TO_DATE(AV_WDATE,'YYYY-MM-DD HH24:mi:ss'),						
                         TO_DATE(AV_WDATE,'YYYY-MM-DD HH24:mi:ss'),
 						AV_WSOURCE,
 						AV_PRGNAME,
 						AN_ERRCODE,
 						AV_HOST,
 						AV_IP,
 						AV_CVALUE,
 						AV_THREADHOLD,
 						AV_AMSG,
 						1,
 						0,
 						NULL,
 						FUN_GET_APPS_NAME(AN_CPID, '运维服务自动化系统'),
 						AN_CPID
 			FROM		IDUAL;
              SELECT		NVL(MAX(WSWITCH), -1)
                 INTO		LI_SWITCH
                 FROM		IEAI_COMPUTER_LIST
                 WHERE		IP = AV_IP;
 
                 IF LI_SWITCH = 1 THEN
                             LI_LOOP := 1;
                             LI_DATA := 0;
 
                     WHILE	LI_DATA IS NOT NULL
                     LOOP
                              LI_DATA := FUN_GET_STRING_NUMBER(AV_WARN_CODE_LIST, LI_LOOP);
                              IF	AI_WCODE >= LI_DATA THEN
 
                                     AN_KEY := LN_KEY;
                                     LI_DATA := NULL;
                             END IF;
                             LI_LOOP := LI_LOOP + 1;
                     END LOOP;
                 END IF;
           END IF;
 
 
 	--	IF	RETSQLCODE = 0 THEN
 	--			AV_RETURN := 'SCUCCESS';
 	--		COMMIT WORK;
 	--	ELSE
 	--			AV_RETURN := 'FAILURE';
 	--		ROLLBACK WORK;
 	--	END IF;
 	        END ;
	END PROC_SAVE_WARNING_MESSAGE;
/


CREATE OR REPLACE PROCEDURE PROC_SET_CHK_DATA_LAST_STATUS   (AV_IP IN VARCHAR2)
		AS
		BEGIN
			DECLARE	
				LD_NOW	NUMERIC(19,0);
				LI_CNT	INTEGER;
			BEGIN
				LD_NOW := FUN_GET_DATE_NUMBER_NEW(CURRENT_TIMESTAMP, 8);
		
				UPDATE	HD_CHECK_RESULT_DATA_LAST
				SET		CPSTATUS =	(
												SELECT	NVL(MAX(CAST(B1.I1 AS INTEGER)), -1)
												FROM		HD_CHECK_STATUS_LAST B1
												WHERE		B1.RSDID = HD_CHECK_RESULT_DATA_LAST.RSDID
											),
							CPTIME = LD_NOW
				WHERE		IP = AV_IP
				AND		CPSTATUS = -1;
				
				UPDATE	HD_CHECK_RESULT_DATA_LAST
				SET		CPSTATUS =	(
												SELECT	NVL(MAX(B1.ALARMCODE), -1)
												FROM		TMP_EXCEPTION_CHECK_POINT B1
												WHERE		B1.IP = AV_IP
												AND		B1.CHKITEMID = HD_CHECK_RESULT_DATA_LAST.CIID
												AND		B1.CHKPOINTID = HD_CHECK_RESULT_DATA_LAST.CPID
											),
							CPTIME = LD_NOW
				WHERE		IP = AV_IP
				AND		CPSTATUS = -1;
				
				
				UPDATE	HD_CHECK_RESULT_DATA_LAST
				SET		CPSTATUS =	FUN_GET_ALARM_CODE (CPTEXT),
							CPTIME = LD_NOW
				WHERE		IP = AV_IP
				AND		CPSTATUS = -1;
				
				INSERT	INTO	HD_CHECK_RESULT_DATA_CACHE
						(
							RSDID,
							ISYSID,
							ISYSNAME,
							CIID,
							CINAME,
							CIPARSRULE,
							CISTATUS,
							MEID,
							IP,
							PORT,
							CPID,
							CPTEXT,
							CPTIME,
							COMCHKID,
							CPSTATUS,
							CPTIMESTAMP
						)
				SELECT	RSDID,
							ISYSID,
							ISYSNAME,
							CIID,
							CINAME,
							CIPARSRULE,
							CISTATUS,
							MEID,
							IP,
							PORT,
							CPID,
							CPTEXT,
							CPTIME,
							COMCHKID,
							CPSTATUS,
							CPTIMESTAMP
				FROM		HD_CHECK_RESULT_DATA_LAST
				WHERE		IP = AV_IP
				AND		CISTATUS = 1;
				
				
				INSERT	INTO	HD_CHECK_STATUS_CACHE
						(
							CRID,
							CPID,
							RSDID,
							I1,
							I2,
							I3,
							I4,
							I5,
							I6,
							I7,
							I8,
							I9,
							I10,
							I11,
							I12,
							I13,
							I14,
							I15,
							I16,
							I17,
							I18,
							I19,
							I20
						)
				SELECT	B.CRID,
							B.CPID,
							B.RSDID,
							B.I1,
							B.I2,
							B.I3,
							B.I4,
							B.I5,
							B.I6,
							B.I7,
							B.I8,
							B.I9,
							B.I10,
							B.I11,
							B.I12,
							B.I13,
							B.I14,
							B.I15,
							B.I16,
							B.I17,
							B.I18,
							B.I19,
							B.I20
				FROM		HD_CHECK_RESULT_DATA_LAST A,
							HD_CHECK_STATUS_LAST B
				WHERE		A.IP = AV_IP
				AND		A.CISTATUS = 1
				AND		B.RSDID = A.RSDID;
				
				SELECT	COUNT(DID)
				INTO		LI_CNT
				FROM		IEAI_SYS_DICTIONARY
				WHERE		DID = 100
				AND		DNAME = 'AutoCloseWarningEvent'
				AND		DINTVALUE = '1';
				
				IF LI_CNT > 0 THEN
					INSERT INTO TMP_AUTO_CLOSE_WARNING_EVENT
							(
								RID
							) 
					SELECT	DISTINCT  W.IID
					FROM		IEAI_HC_ACTWARNING W,
								IEAI_CHKITEM I,
					            (
										SELECT	MAX(D.CPSTATUS) AS WCODE,
													D.IP,
													D.CIID
										FROM		HD_CHECK_RESULT_DATA_LAST D
										WHERE		NOT EXISTS (SELECT 1 FROM (SELECT MAX(CPSTATUS) AS CPSTATUS, MEID, CIID FROM HD_CHECK_RESULT_DATA_LAST WHERE CPSTATUS > 2  GROUP BY MEID, CIID) B1 WHERE B1.MEID = D.MEID AND B1.CIID = D.CIID)
										GROUP	BY D.IP,D.CIID
					            ) B
					WHERE		W.PRGNAME = I.ISHELLNAME
					AND 		B.IP = W.IP
          AND     B.IP = AV_IP 
					AND 		B.CIID = I.ICHKITEMID;
					
					INSERT INTO IEAI_HC_ACTWARNING_HIS
							(
								IID,
								WTYPE,
								WCODE,
								WDATE,
								LDATE,
								WSOURCE,
								PRGNAME,
								ERRCODE,
								HOSTNAME,
								IP,
								CHKVALUE,
								THREADHOLD,
								AMESSAGE,
								WCOUNT,
								TFLAG,
								WMESSAGE,
								SYSNAME,
								DELUSER,
								DELTIME
							)
					SELECT	IID,
								WTYPE,
								WCODE,
								WDATE,
								LDATE,
								WSOURCE,
								PRGNAME,
								ERRCODE,
								HOSTNAME,
								W.IP,
								CHKVALUE,
								THREADHOLD,
								AMESSAGE,
								WCOUNT,
								TFLAG,
								WMESSAGE,
								SYSNAME,
								'Auto Close',
								CURRENT_TIMESTAMP
					FROM		IEAI_HC_ACTWARNING W,
								TMP_AUTO_CLOSE_WARNING_EVENT A
					WHERE		W.IID = A.RID;
					
					DELETE
					FROM		IEAI_HC_ACTWARNING W
					WHERE		EXISTS (SELECT 1 FROM TMP_AUTO_CLOSE_WARNING_EVENT A WHERE A.RID = W.IID);
				END IF;
			END;
		END PROC_SET_CHK_DATA_LAST_STATUS ;
/