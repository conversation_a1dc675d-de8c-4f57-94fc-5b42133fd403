#!/bin/sh
#this script is used to restart was service
#该脚本用于启动was服务进程，不包含nodeagent、dmgr
was_path=$1
service_name=$2
was=yes
##################--add by <PERSON><PERSON><PERSON><PERSON><PERSON> @2014/07/03
shellpath=`dirname $0`   #脚本路径
shellname=`basename $0`  #脚本名称
if [ $# -lt 2 ];then
	echo "parameter is lost"
	exit
fi
cd ${shellpath}

stop_succ_flag=0  #停止was服务进程成功标志
start_succ_flag=0 #启动was服务进程成功标志

profilepath=`dirname ${was_path}`
workdir=`pwd`
config=${workdir}/config
lib=${workdir}/lib
pyPath=${workdir}/py/
log=${profilepath}/logs

. lib/function.lib
#this function is used to start was service
#启动was服务进程
start_was_service ()
{
_was_path=$1
_service_name=$2

cd ${_was_path}
sh startServer.sh ${_service_name} |tee ${log}/startWas_${_service_name}.log
succnum=`grep -c "ADMU3000I" ${log}/startWas_${_service_name}.log`
if [ ${succnum} -eq 0 ]
then 
    	exit
else
	echo "0"
fi
}
echo "--------$service_name starting--------"
start_was_service $was_path $service_name
