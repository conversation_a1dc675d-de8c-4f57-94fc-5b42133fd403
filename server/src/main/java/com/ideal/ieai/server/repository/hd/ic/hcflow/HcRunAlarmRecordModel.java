package com.ideal.ieai.server.repository.hd.ic.hcflow;

public class HcRunAlarmRecordModel
{
    
    /**
     * 记录id
     */
    private Long iid;
    
    /**
     * 流程id
     */
    private Long iflowid;
    
    /**
     * 巡检设备ip
     */
    private String iip;
    
    /**
     * 巡检流程名称
     */
    private String iflowname;
    
    /**
     * 活动名称
     */
    private String iactname;
    
    /**
     * 首次出现告警时间
     */
    private Long bdate;
    
    /**
     * 最近告警时间
     */
    private Long ldate;
    
    
    /**
     * 连续出现告警时间
     */
    private Integer frequency;

    public Long getIid ()
    {
        return iid;
    }

    public void setIid ( Long iid )
    {
        this.iid = iid;
    }

    public Long getIflowid ()
    {
        return iflowid;
    }

    public void setIflowid ( Long iflowid )
    {
        this.iflowid = iflowid;
    }

    public String getIip ()
    {
        return iip;
    }

    public void setIip ( String iip )
    {
        this.iip = iip;
    }

    public String getIflowname ()
    {
        return iflowname;
    }

    public void setIflowname ( String iflowname )
    {
        this.iflowname = iflowname;
    }


    public String getIactname ()
    {
        return iactname;
    }

    public void setIactname ( String iactname )
    {
        this.iactname = iactname;
    }

    public Long getBdate ()
    {
        return bdate;
    }

    public void setBdate ( Long bdate )
    {
        this.bdate = bdate;
    }


    public Long getLdate ()
    {
        return ldate;
    }

    public void setLdate ( Long ldate )
    {
        this.ldate = ldate;
    }

    public Integer getFrequency ()
    {
        return frequency;
    }

    public void setFrequency ( Integer frequency )
    {
        this.frequency = frequency;
    }
    
    

}
