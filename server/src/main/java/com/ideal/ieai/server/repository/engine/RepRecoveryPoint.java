/**
 * 
 */
package com.ideal.ieai.server.repository.engine;

import java.io.Serializable;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.recoveryflow.RecoveryPoint;

/**
 * <li>Title: RepRecoveryPoint.java</li>
 * <li>Project: server</li>
 * <li>Package: com.ideal.ieai.server.repository.engine</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright (c) 2006</li>
 * <li>Company: IdealTechnologies </li>
 * <li>Created on 2006-5-23, 9:46:16</li>
 * 
 * <AUTHOR>
 * @version iEAI v3.5
 * @hibernate.class table="ieai_recoverypoint"
 */
public class RepRecoveryPoint implements Serializable {

    /**
     * db primery key and increment
     */
    private long _serialNo = -1;

    private long _flowId = -1;

    private long _scopeId = -1;

    private long    _actId               = -1;

    private String _actName = null;

    /**
     * It is the recovery policy definition.
     */
    private int _recoveryPolicyDef = -1;

    /**
     * It is really used recovery policy.
     */
    private int _recoveryPolicy = Constants.RECOVERY_POLICY_UNKNOWN;

    private String _actDesc = null;

//    private String _recoveryDesc = null;

    /**
     * Its value is RECOVERY_POINT_*
     */
    private int _recoveryPointType = -1;

    /**
     * It is true, if recovered struct is a transaction sruct.
     */
    private boolean _isTrStruct = false;

    /**
     * The field is used to indicate result of current ExecAct recovered. The
     * value is Constants.EXECACT_RECOVERY_RESULT_*.
     */
    private int _recoverResult = Constants.EXECACT_RECOVERY_RESULT_UNKNOWN;

    /**
     * When _recoverResult is Constants.EXECACT_RECOVERY_RESULT_FAILED, the
     * filed is error message about failing.
     */
    private String _errMsg = null;

    private int _trState = Constants.TRANSACTION_UNKNOWN;

    private boolean _trStructOutputSaved = false;

    //for recover of delete sign  update by tao_ding on 2009-03-16
    private int _ideleteflag = 0;
    /**
     * @return Returns the actDesc.
     * @hibernate.property column="iactdesc"
     */
    public String getActDesc() {
        return _actDesc;
    }

    /**
     * @param actDesc
     *            The actDesc to set.
     */
    public void setActDesc(String actDesc) {
        _actDesc = actDesc;
    }

    /**
     * @return Returns the actId.
     * @hibernate.property column="iactid"
     * @hibernate.column name="iactid" index="inx_recovery_point_actid"
     */
    public long getActId ()
    {
        return _actId;
    }

    /**
     * @param actId
     *            The actId to set.
     */
    public void setActId ( long actId )
    {
        _actId = actId;
    }

    /**
     * @return Returns the actName.
     * @hibernate.property column="iactname"
     */
    public String getActName() {
        return _actName;
    }

    /**
     * @param actName
     *            The actName to set.
     */
    public void setActName(String actName) {
        _actName = actName;
    }

    /**
     * @return Returns the errMsg.
     * @hibernate.property column="ierrmsg"
     */
    public String getErrMsg() {
        return _errMsg;
    }

    /**
     * @param errMsg
     *            The errMsg to set.
     */
    public void setErrMsg(String errMsg) {
        _errMsg = errMsg;
    }

    /**
     * @return Returns the flowId.
     * @hibernate.property column="iflowid"
     * @hibernate.column name="iflowid" index="inx_recovery_point_flowid"
     */
    public long getFlowId() {
        return _flowId;
    }

    /**
     * @param flowId
     *            The flowId to set.
     */
    public void setFlowId(long flowId) {
        _flowId = flowId;
    }

    /**
     * @return Returns the isTrStruct.
     * @hibernate.property column="iistrstruct"
     */
    public boolean isTrStruct() {
        return _isTrStruct;
    }

    /**
     * @param isTrStruct
     *            The isTrStruct to set.
     */
    public void setTrStruct(boolean isTrStruct) {
        _isTrStruct = isTrStruct;
    }

    /**
     * @return Returns the recoverResult.
     * @hibernate.property column="irecoverresult"
     */
    public int getRecoverResult() {
        return _recoverResult;
    }

    /**
     * @param recoverResult
     *            The recoverResult to set.
     */
    public void setRecoverResult(int recoverResult) {
        _recoverResult = recoverResult;
    }

    /**
     * @return Returns the recoveryDesc.
     */
//    public String getRecoveryDesc() {
//        return _recoveryDesc;
//    }

    /**
     * @param recoveryDesc
     *            The recoveryDesc to set.
     */
//    public void setRecoveryDesc(String recoveryDesc) {
//        _recoveryDesc = recoveryDesc;
//    }

    /**
     * @return Returns the recoveryPointType.
     * @hibernate.property column="irecoverypointtype"
     */
    public int getRecoveryPointType() {
        return _recoveryPointType;
    }

    /**
     * @param recoveryPointType
     *            The recoveryPointType to set.
     */
    public void setRecoveryPointType(int recoveryPointType) {
        _recoveryPointType = recoveryPointType;
    }

    /**
     * @return Returns the recoveryPolicy.
     * @hibernate.property column="irecoverypolicy"
     */
    public int getRecoveryPolicy() {
        return _recoveryPolicy;
    }

    /**
     * @param recoveryPolicy
     *            The recoveryPolicy to set.
     */
    public void setRecoveryPolicy(int recoveryPolicy) {
        _recoveryPolicy = recoveryPolicy;
    }

    /**
     * @return Returns the recoveryPolicyDef.
     * @hibernate.property column="irecoverypolicydef"
     */
    public int getRecoveryPolicyDef() {
        return _recoveryPolicyDef;
    }

    /**
     * @param recoveryPolicyDef
     *            The recoveryPolicyDef to set.
     */
    public void setRecoveryPolicyDef(int recoveryPolicyDef) {
        _recoveryPolicyDef = recoveryPolicyDef;
    }

    /**
     * @return Returns the scopeId.
     * @hibernate.property column="iscopeid"
     * @hibernate.column name="iscopeid" index="inx_recovery_point_scopeid"
     */
    public long getScopeId() {
        return _scopeId;
    }

    /**
     * @param scopeId
     *            The scopeId to set.
     */
    public void setScopeId(long scopeId) {
        _scopeId = scopeId;
    }

    /**
     * @return Returns the serialNo.
     * @hibernate.id generator-class="com.ideal.ieai.server.repository.idgenerator.IdGenerator" column="iid"
     */
    public long getSerialNo() {
        return _serialNo;
    }

    /**
     * @param serialNo
     *            The serialNo to set.
     */
    public void setSerialNo(long serialNo) {
        _serialNo = serialNo;
    }

    /**
     * @return Returns the trState.
     * @hibernate.property column="itrstate"
     */
    public int getTrState() {
        return _trState;
    }

    /**
     * @param trState
     *            The trState to set.
     */
    public void setTrState(int trState) {
        _trState = trState;
    }

    /**
     * @return Returns the trStructOutputSaved.
     * @hibernate.property column="itrstructoutputsaved"
     */
    public boolean isTrStructOutputSaved() {
        return _trStructOutputSaved;
    }

    /**
     * @param trStructOutputSaved
     *            The trStructOutputSaved to set.
     */
    public void setTrStructOutputSaved(boolean trStructOutputSaved) {
        _trStructOutputSaved = trStructOutputSaved;
    }
    
    
    /**
     * @return
     */
    public int getIdeleteflag(){
         return _ideleteflag;
    }

    public void setIdeleteflag(int ideleteflag){
        _ideleteflag =  ideleteflag;        
    }
    
    public static RepRecoveryPoint newInstance(RecoveryPoint recovery) {
        if (recovery == null) {
            return null;
        }
        RepRecoveryPoint repRecovery = new RepRecoveryPoint();
        repRecovery.setActDesc(recovery.getActDesc());
        repRecovery.setActId(recovery.getActId());
        repRecovery.setActName(recovery.getActName());
        repRecovery.setErrMsg(recovery.getErrMsg());
        repRecovery.setFlowId(recovery.getFlowId());
        repRecovery.setRecoverResult(recovery.getRecoverResult());
//        repRecovery.setRecoveryDesc(recovery.getRecoveryDesc());
        repRecovery.setRecoveryPointType(recovery.getRecoveryPointType());
        repRecovery.setRecoveryPolicy(recovery.getRecoveryPolicy());
        repRecovery.setRecoveryPolicyDef(recovery.getRecoveryPolicyDef());
        repRecovery.setScopeId(recovery.getScopeId());
        repRecovery.setSerialNo(recovery.getSerialNo());
        repRecovery.setTrState(recovery.getTrState());
        repRecovery.setTrStruct(recovery.isTrStruct());
        repRecovery.setTrStructOutputSaved(recovery.isTrStructOutputSaved());
        repRecovery.setIdeleteflag(recovery.getRecoverDelSign());  //添加软删除标志
        return repRecovery;
    }

    public RecoveryPoint toCommonts() {
        RecoveryPoint recovery = new RecoveryPoint();
        recovery.setActDesc(getActDesc());
        recovery.setActId(getActId());
        recovery.setActName(getActName());
        recovery.setErrMsg(getErrMsg());
        recovery.setFlowId(getFlowId());
        recovery.setRecoverResult(getRecoverResult());
//        recovery.setRecoveryDesc(getRecoveryDesc());  
        recovery.setRecoveryPointType(getRecoveryPointType());
        recovery.setRecoveryPolicy(getRecoveryPolicy());
        recovery.setRecoveryPolicyDef(getRecoveryPolicyDef());
        recovery.setScopeId(getScopeId());
        recovery.setSerialNo(getSerialNo());
        recovery.setTrState(getTrState());
        recovery.setTrStruct(isTrStruct());
        recovery.setTrStructOutputSaved(isTrStructOutputSaved());
        recovery.setRecoverDelSign(getIdeleteflag());   //添加软删除标识
        return recovery;
    }
}
