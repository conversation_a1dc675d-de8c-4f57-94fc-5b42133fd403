package com.ideal.ieai.server.eswitch.repository.noticecontent;
public class ContentModel{
    private long iid;
    private String ititle;
    private String icontent;
    private long icreatetime;
    private String icreateuser;
    
    public long getIid (){
        return iid;
    }
    public void setIid ( long iid ){
        this.iid = iid;
    }
    public String getItitle (){
        return ititle;
    }
    public void setItitle ( String ititle ){
        this.ititle = ititle;
    }
    public String getIcontent (){
        return icontent;
    }
    public void setIcontent ( String icontent ){
        this.icontent = icontent;
    }
    public long getIcreatetime (){
        return icreatetime;
    }
    public void setIcreatetime ( long icreatetime ){
        this.icreatetime = icreatetime;
    }
    public String getIcreateuser ()
    {
        return icreateuser;
    }
    public void setIcreateuser ( String icreateuser )
    {
        this.icreateuser = icreateuser;
    }
    
}