Ext.onReady(function() {
	
	destroyRubbish();

	Ext.define('topoEdgeRelationInfoModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'sourceSystem',
			type : 'string'
		}, {
			name : 'sourceTaskname',
			type : 'string'
		}, {
			name : 'targetSystem',
			type : 'string'
		}, {
			name : 'targetTaskname',
			type : 'string'
		}, {
			name : 'relationFile',
			type : 'string'
		}, {
			name : 'fileResourse',
			type : 'string'
		}, {
			//活动状态  0、运行中   2、已完成     1、未运行     3、略过
			name : 'actState',
			type : 'string'
		}]
	});

    
	var columns = [{
		xtype : 'rownumberer',
		header:'序号',
		align: 'center',
		width : 70
	},{
		text : '上游系统',
		dataIndex : 'sourceSystem',
		align: 'left',
		flex :1
	},{
		text : '上游任务',
		dataIndex : 'sourceTaskname',
		align: 'left',
		flex :1
	},{
		text : '下游系统',
		dataIndex : 'targetSystem',
		align: 'left',
		flex :1
	},{
		text : '下游任务',
		dataIndex : 'targetTaskname',
		align: 'left',
		flex :1
	},{
		text : '文件',
		dataIndex : 'relationFile',
		align: 'left',
		flex :1
	},{
		text : '渠道',
		dataIndex : 'fileResourse',
		align: 'left',
		flex :1
	},{
		text : '状态',
		dataIndex : 'actState',
		align: 'left',
		flex :1,
		renderer: function(value,metaData,record){
			//活动状态  0、运行中   2、已完成     1、未运行     3、略过
			var status1 = "";
			var type = record.get("actState");
			alert(type);
			if(type=='0'){
				status1="运行中";
			}else if(type=='2'){
				status1="已完成";
			}else if(type=='1'){
				status1="未运行";
			}else if(type=='3'){
				status1="略过";
			}
			return status1;
		}
	}];

	var store = Ext.create('Ext.data.Store', {
		pageSize: 25,
		autoLoad : true,
		model : 'topoEdgeRelationInfoModel',
		proxy : {
			type : 'ajax',
			url : 'queryTopoEdgeRelationInfoList_nx.do',
			reader : {
				type : 'json',
				root : 'topoEdgeRelationInfoList',
				totalProperty: 'totalProperty'
			}
		} 
    
	});
	store.on('beforeload', function (store, options) {
	    var new_params = {  
	    		sourceId: sourceId,
	    		targetId: targetId,
	    		queryDate: queryDate,
	    		groupName: groupName
	    };
	    
	    Ext.apply(store.proxy.extraParams, new_params);
	    
    });
	

	var pageBar = Ext.create('Ext.PagingToolbar', {
    	pageSize: 25,
        store: store,
        displayInfo: true,
        displayMsg: '显示{0}-{1}条，共{2}条',
        emptyMsg: "没有数据"
    })
	/*var form = Ext.create('Ext.form.Panel', {
		border: false,
		width : contentPanel.getWidth(),
		height: 80,
		layout : "column",
		bodyPadding: 25,
	    items: [{
	    	labelWidth : 80,
	    	columnWidth:.7,
	    	xtype: 'textfield',
	        fieldLabel: '系统名称',
	        name: 'proname',
	        displayField:'proname'
	    },{
	    	layout: 'anchor',
	    	columnWidth:.3,
	    	padding: '0 0 0 20',
	    	border: false,
	    	items:[{
	    		//anchor:'30%',
		    	xtype: 'button',
		    	width:60,
		    	height:30,
		    	text: '查询',
		    	allowBlank: true,
		    	handler: function() {
		    	pageBar.moveFirst();
		    	store.load({
		    		params: {start: 0, limit: 50}
		    	});
		    }
	    	}]
	    	
	    }]
	});*/

	
	var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit: 2
	});
	
	var grid = Ext.create('Ext.grid.Panel', {
		height : contentPanel.getHeight()-80,
		width : contentPanel.getWidth(),
//		autoScroll : false,
		plugins : [ cellEditing ],
		store : store,
		border : false,
		columns : columns,
        bbar: pageBar
	});

	var mainPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		items: [/*form,*/ grid],
	    renderTo : "topoEdgeRelationInfo_grid_area"
	});
	
	contentPanel.on("resize",function(){
		//form.setWidth(contentPanel.getWidth());
		grid.setHeight(contentPanel.getHeight()-80);
		grid.setWidth(contentPanel.getWidth());
	});
	
    contentPanel.getLoader().on("beforeload",function(obj, options, eOpts){
    	Ext.destroy(mainPanel);
		if(Ext.isIE){
        	CollectGarbage(); 
    	}
    });
	
});
/*function forwardFlowMessage(projectId,projectName,uuid){
	
	contentPanel.getLoader().load({url: 'batchConfig_gf.do?tmp='+new Date().getTime(),params: {projectId: projectId, projectName:projectName,uuid:uuid},scripts: true});
}*/
