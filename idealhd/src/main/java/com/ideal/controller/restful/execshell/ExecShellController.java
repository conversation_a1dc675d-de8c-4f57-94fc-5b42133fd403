package com.ideal.controller.restful.execshell;

import com.alibaba.fastjson.JSON;
import com.ideal.controller.showActInfo.ShowActInfoController;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.DesUtils;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentMaintainManager;
import com.ideal.ieai.server.repository.hd.ic.dailyOperinfo.HcDailyOperationManager;
import com.ideal.ieai.server.repository.user.RepUser;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import com.ideal.ieai.server.restfulapi.execshell.model.execShellParamsModel;
import com.ideal.ieai.server.timetask.repository.restfulapi.execshell.execShellapiResultMesage;
import com.ideal.ieai.usertask.UTConfigHD;
import com.ideal.service.restfulapi.ExecshellService;
import com.ideal.util.ListUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.web.bind.annotation.*;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/execShell")
public class ExecShellController
{
    private static volatile boolean upload = false;
    static Logger log = Logger.getLogger(ExecShellController.class);
    @RequestMapping(value = "token.do", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    public void token ( @RequestParam String userName, @RequestParam String passwd, HttpServletRequest request,
            HttpServletResponse response )
    {
        execShellapiResultMesage result = new execShellapiResultMesage();
        if (!StringUtils.isBlank(userName) && !StringUtils.isBlank(passwd))
        {
            execShellParamsModel execModel = new execShellParamsModel();
            boolean passmessage = false;
            try
            {
                DesUtils des = new DesUtils("@Ideal#$");
                passwd = des.decrypt(passwd);
                passmessage = true;
            } catch (IllegalBlockSizeException e)
            {

            } catch (BadPaddingException e)
            {

            }
            if (!passmessage)
            {
                result.setStatus(false);
                result.setMessages("密码解析失败!");
                log.info("getToken method:" + JSON.toJSON(result).toString());
                responseData(response, JSON.toJSON(result).toString());
                return;
            }
            execModel.setUserName(userName);
            execModel.setPasswd(passwd);
            if (null == UTConfigHD.getInstance())
            {
                if (!UTConfigHD.init(request))
                {
                    log.info("Error loading UserTask configuration file!");
                    result.setStatus(false);
                    result.setMessages("Error loading UserTask configuration file!");
                    log.info("getToken method:" + JSON.toJSON(result).toString());
                    responseData(response, JSON.toJSON(result).toString());
                    return;

                }
            }
            result = ExecshellService.getInstance().getTokenForHcDailyOper(execModel);
            responseData(response, JSON.toJSON(result).toString());
            log.info("getToken method:" + JSON.toJSON(result).toString());
            return;
        }

        else if (StringUtils.isBlank(userName))
        {
            result.setStatus(false);
            result.setMessages("没有传递用户名!");
            log.info("getToken method:" + JSON.toJSON(result).toString());
            responseData(response, JSON.toJSON(result).toString());
            return;

        } else if (StringUtils.isBlank(passwd))
        {
            result.setStatus(false);
            result.setMessages("没有传递密码!");
            log.info("getToken method:" + JSON.toJSON(result).toString());
            responseData(response, JSON.toJSON(result).toString());
            return;
        }

    }

    private void responseData ( HttpServletResponse response, String jsonStr )
    {
        try
        {
            response.getOutputStream().write(jsonStr.getBytes("UTF-8"));
            response.setContentType("text/json; charset=UTF-8");
            response.addHeader("Content-type", "application/json");
            response.addHeader("Accept", "application/json");
        } catch (UnsupportedEncodingException e)
        {
            log.error("responseData is error ! " + e.getMessage());
        } catch (IOException e)
        {
            log.error("responseData is error ! " + e.getMessage());
        }
    }

    @RequestMapping(value = "uploadpkg.do", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public void uploadpkg ()
    {

    }

    @RequestMapping(value = "exec.do", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public void exec ( @RequestBody String str, HttpServletRequest request, HttpServletResponse response )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName() + ":";
        execShellapiResultMesage result = new execShellapiResultMesage();
        String user = "";
        String token = "";
        String taskName = "";
        String nextStartTimeBegin = "";// 下次启动开始时间
        String nextStartTimeEnd = "";// 下次启动结束时间
        String paramHeader = request.getHeader("paramsHeader");
        Integer enable = null;
        if (!StringUtils.isEmpty(paramHeader))
        {
            Map map = (Map) JSON.parse(paramHeader);
            user = (String) map.get("userName");
            // 获取token串
            if (!map.containsKey("tokenValue"))
            {
                result.setStatus(false);
                result.setMessages("没有传递token!");
                log.info(this.getClass().getName() + " of " + method + "没有传递token!");
                responseData(response, JSON.toJSON(result).toString());
                return;
            }
            token = (String) map.get("tokenValue");

        } else
        {
            result.setStatus(false);
            result.setMessages("请求头paramsHeader为空！");
            log.info(this.getClass().getName() + " of " + method + "请求头paramsHeader为空！");
            responseData(response, JSON.toJSON(result).toString());
            return;
        }

        if (StringUtils.isEmpty(user))
        {
            result.setStatus(false);
            result.setMessages("请求缺少userName参数或者userName参数为空！");
            log.info(this.getClass().getName() + " of " + method + "请求缺少userName参数或者userName参数为空！");
            responseData(response, JSON.toJSON(result).toString());
            return;
        }
        execShellParamsModel hcdobean = new execShellParamsModel();
        hcdobean.setUserName(user);
        hcdobean.setTokenValue(token);
        RepUser repuser =  ExecshellService.getInstance().getUserinfo(hcdobean);
        UserInfo userinfo = new UserInfo();
        userinfo.setFullName(repuser.getFullName());
        userinfo.setId(repuser.getIid());

        execShellapiResultMesage hr = ExecshellService.getInstance().validateToken(hcdobean);
        if (hr.isStatus())
        {
            execShellParamsModel ohu = JSON.parseObject(str, execShellParamsModel.class);
            execShellapiResultMesage api =  ExecshellService.getInstance().validatedhParam(ohu);
            if(!api.isStatus()) {
                responseData(response, JSON.toJSON(api).toString());
                log.info("getDailyOperSysName method:" + JSON.toJSON(hr).toString());
                return;
            }
            
       exectask(ohu,userinfo,request,response);
        } else
        {
            responseData(response, JSON.toJSON(hr).toString());
            log.info("getDailyOperSysName method:" + JSON.toJSON(hr).toString());
            return;
        }

    }

    private void exectask ( execShellParamsModel ohu, UserInfo userinfo, HttpServletRequest request, HttpServletResponse response )
    {
        Map resss = new HashMap();
        try
        {
            ExecshellService.getInstance().uploadTemplate(userinfo,ohu.getTimeout());
        } catch (Exception e1)
        {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        
        List args = new ArrayList();
        String agentip = "";
        String port = "";
        String ios_name= "";

        if (ohu.getIP().contains(":"))
        {
            agentip = ohu.getIP().split(":")[0];
           String port_1 = ohu.getIP().split(":")[1];
            try
            {
                Map map = AgentMaintainManager.getInstance().getAgentMaintainList(null, agentip, null, null, null,
                    null, null, null, null, 0, 30, -1, null, null, null, null, null, null, true, null, null, null, null,null,null,null,null,"");
                List<Map> list = (List) map.get("dataList");
                if (ListUtil.isNotEmptyList(list))
                {
                    for(int i=0;i<list.size();i++) {
                         if(port_1.equals(list.get(i).get("iagentport").toString())) {
                             port = port_1;
                             ios_name = (String) list.get(i).get("ios_name");
                             break;
                         }
                        
                        
                    }
                }

            } catch (Exception e)
            {
                e.printStackTrace();
            }
        } else
        {
            try
            {
                Map map = AgentMaintainManager.getInstance().getAgentMaintainList(null, ohu.getIP(), null, null, null,
                    null, null, null, null, 0, 30, -1, null, null, null, null, null, null, true, null, null, null, null,null,null,null,null,"");
                List<Map> list = (List) map.get("dataList");
                if (ListUtil.isNotEmptyList(list))
                {
                    agentip = (String) list.get(0).get("iagentip");
                    ios_name = (String) list.get(0).get("ios_name");
                    port = list.get(0).get("iagentport").toString();
                } 

            } catch (Exception e)
            {
                e.printStackTrace();
            }
            
        }
        if(StringUtils.isBlank(port)) {
            resss.put("status", "false");
            resss.put("message", "未找到对应的agent");
            responseData(response, JSON.toJSONString(resss));
            return;
            
        }
        args.add(ohu.getTimeout());
        if(ios_name.toLowerCase().contains("window")) {
            args.add(ohu.getExecCmd());
        }else {
            args.add("./baseScript/SU.sh "+ ohu.getExecUser() + "  "+ ohu.getExecCmd());
        }
       
        args.add(agentip+":"+port);
        long flowId = 0l;
        try
        {
             flowId = Engine.getInstance().startFlow(userinfo, "execshell_template", "standard", args, null, null, "",
                null, false, null, null, 23, 23,false);
        } catch (ServerException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (RepositoryException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        resss.put("status", true);
        resss.put("message", "启动成功!");
        resss.put("flowid", flowId);
        responseData(response, JSON.toJSONString(resss));
        return;
        
        

    }
    @RequestMapping(value = "uploadtemplate.do", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public void uploadtemplate(  HttpServletRequest request, HttpServletResponse response ) {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName() + ":";
        execShellapiResultMesage result = new execShellapiResultMesage();
        String user = "";
        String token = "";
        String taskName = "";
        String nextStartTimeBegin = "";// 下次启动开始时间
        String nextStartTimeEnd = "";// 下次启动结束时间
        String paramHeader = request.getHeader("paramsHeader");
        Integer enable = null;
        if (!StringUtils.isEmpty(paramHeader))
        {
            Map map = (Map) JSON.parse(paramHeader);
            user = (String) map.get("userName");
            // 获取token串
            if (!map.containsKey("tokenValue"))
            {
                result.setStatus(false);
                result.setMessages("没有传递token!");
                log.info(this.getClass().getName() + " of " + method + "没有传递token!");
                responseData(response, JSON.toJSON(result).toString());
                return;
            }
            token = (String) map.get("tokenValue");

        } else
        {
            result.setStatus(false);
            result.setMessages("请求头paramsHeader为空！");
            log.info(this.getClass().getName() + " of " + method + "请求头paramsHeader为空！");
            responseData(response, JSON.toJSON(result).toString());
            return;
        }

        if (StringUtils.isEmpty(user))
        {
            result.setStatus(false);
            result.setMessages("请求缺少userName参数或者userName参数为空！");
            log.info(this.getClass().getName() + " of " + method + "请求缺少userName参数或者userName参数为空！");
            responseData(response, JSON.toJSON(result).toString());
            return;
        }
        execShellParamsModel hcdobean = new execShellParamsModel();
        hcdobean.setUserName(user);
        hcdobean.setTokenValue(token);
        RepUser repuser =  ExecshellService.getInstance().getUserinfo(hcdobean);
        UserInfo userinfo = new UserInfo();
        userinfo.setFullName(repuser.getFullName());
        userinfo.setId(repuser.getIid());
       

        execShellapiResultMesage hr = ExecshellService.getInstance().validateToken(hcdobean);
        
        
        
        try
        {
            ExecshellService.getInstance().uploadTemplate(userinfo,"5555");
        } catch (Exception e)
        {
            e.printStackTrace();
        }
    }
    public boolean uploadTemplate() {
        
        
        
        
        return false;
        
    }
    

    @RequestMapping(value = "query.do", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public void query ( @RequestBody String str, HttpServletRequest request, HttpServletResponse response )
    {
        ShowActInfoController sc = new ShowActInfoController();

        String method = Thread.currentThread().getStackTrace()[1].getMethodName() + ":";
        execShellapiResultMesage result = new execShellapiResultMesage();
        String user = "";
        String token = "";
        String taskName = "";
        String nextStartTimeBegin = "";// 下次启动开始时间
        String nextStartTimeEnd = "";// 下次启动结束时间
        String paramHeader = request.getHeader("paramsHeader");
        Integer enable = null;
        if (!StringUtils.isEmpty(paramHeader))
        {
            Map map = (Map) JSON.parse(paramHeader);
            user = (String) map.get("userName");
            // 获取token串
            if (!map.containsKey("tokenValue"))
            {
                result.setStatus(false);
                result.setMessages("没有传递token!");
                log.info(this.getClass().getName() + " of " + method + "没有传递token!");
                responseData(response, JSON.toJSON(result).toString());
                return;
            }
            token = (String) map.get("tokenValue");

        } else
        {
            result.setStatus(false);
            result.setMessages("请求头paramsHeader为空！");
            log.info(this.getClass().getName() + " of " + method + "请求头paramsHeader为空！");
            responseData(response, JSON.toJSON(result).toString());
            return;
        }

        if (StringUtils.isEmpty(user))
        {
            result.setStatus(false);
            result.setMessages("请求缺少userName参数或者userName参数为空！");
            log.info(this.getClass().getName() + " of " + method + "请求缺少userName参数或者userName参数为空！");
            responseData(response, JSON.toJSON(result).toString());
            return;
        }
        execShellParamsModel hcdobean = new execShellParamsModel();
        hcdobean.setUserName(user);
        hcdobean.setTokenValue(token);
        String userId = String
                .valueOf(HcDailyOperationManager.getInstance().getUserid(user, Constants.IEAI_IEAI_BASIC));

        execShellapiResultMesage hr = ExecshellService.getInstance().validateToken(hcdobean);
        if (hr.isStatus())
        {
            execShellParamsModel ohu = JSON.parseObject(str, execShellParamsModel.class);
            try
            {
                Map map = new HashMap();
                String actState = WorkflowManager.getInstance().getActStatus(Long.parseLong(ohu.getIflowid()), 2, Constants.IEAI_DATA_COLLECT);
                if("Running".equals(actState)) {
                    Map res = sc.treatCorrectEndShellCmdActInfo(request,Long.parseLong(ohu.getIflowid()), 2, Constants.IEAI_DATA_COLLECT);
                    map.put("status", true);
                    map.put("isFinished", false);
                    map.put("messages", "脚本正在运行");
                  
                    responseData(response,JSON.toJSONString(map));
                    return;
                }
                if("Finished".equals(actState)) {
                   Map res = sc.treatCorrectEndShellCmdActInfo(request,Long.parseLong(ohu.getIflowid()), 2, Constants.IEAI_DATA_COLLECT);
                    map.put("isFinished", true);
                    map.put("status", true);
                    map.put("messages", "脚本执行成功");
                    map.put("ret", res.get("ret"));
                    if(!StringUtils.isBlank((String) res.get("stderr"))){
                        map.put("isFinished", false);
                        map.put("output", res.get("stderr"));
                    }else {
                        map.put("output", res.get("stdout"));
                    }
                    
                    responseData(response,JSON.toJSONString(map));
                    return;
                    
                }
                if("Fail".equals(actState)) {
                    
                    Map res = sc.treatCorrectEndShellCmdActInfo(request,Long.parseLong(ohu.getIflowid()), 2, Constants.IEAI_DATA_COLLECT);
                    map.put("isFinished", true);
                    map.put("status", true);
                    map.put("messages", "脚本执行失败");
                    map.put("ret", res.get("ret"));
                    if(!StringUtils.isBlank((String) res.get("stderr"))){
                        map.put("isFinished", false);
                        map.put("output", res.get("stderr"));
                    }else {
                        map.put("output", res.get("stdout"));
                    }
                    
                    responseData(response,JSON.toJSONString(map));
                    return;
                }
                if("Timeout".equals(actState)) {
                    Map res = sc.treatCorrectEndShellCmdActInfo(request,Long.parseLong(ohu.getIflowid()), 2, Constants.IEAI_DATA_COLLECT);
                    map.put("isFinished", false);
                    map.put("status", true);
                    map.put("messages", "脚本超时");
             
                    
                    responseData(response,JSON.toJSONString(map));
                    return;
                }
//                map = sc.treatCorrectEndShellCmdActInfo(request,Long.parseLong(ohu.getIflowid()), 2, Constants.IEAI_DATA_COLLECT);
//                map.put("cdwerdqewdew",actState);
//                responseData(response,map.toString());
//                return;
//                ErrorTask error = ErrorTaskReository.getInstance().getErrorTaskByFlowIdAndActId(Long.parseLong(ohu.getIflowid()), new Long(2), Constants.IEAI_DATA_COLLECT);
            } catch (NumberFormatException e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (RepositoryException e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (RemoteException e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (ServerException e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            
            
        } else
        {
            responseData(response, JSON.toJSON(hr).toString());
            log.info("getDailyOperSysName method:" + JSON.toJSON(hr).toString());
            return;
        }
    }
    public static void main ( String[] args )
    {
        try
        {
            String ideal = DesUtils.getInstance().encrypt("ideal");
            System.out.println(ideal);
        } catch (IllegalBlockSizeException | BadPaddingException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        
    }
    
}
