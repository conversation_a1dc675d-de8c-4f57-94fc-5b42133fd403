package com.ideal.ieai.server.datacollect.repository.collectinfo;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.datacollect.repository.collectclass.ClassAttributeModel;
import com.ideal.ieai.server.datacollect.repository.collectmodel.CollectModelModel;
import com.ideal.ieai.server.datacollect.repository.collectstart.CollectRunInstModel;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CollectInfoManage
{
    private Logger log = Logger.getLogger(CollectInfoManage.class);

    public List getCollectInfoList ( Connection conn, String sql, Integer start, Integer limit )
            throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else
            {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                CollectInfoModel model = new CollectInfoModel();
                model.setIid(rs.getLong("iid"));
                model.setIsarelationid(rs.getLong("isarelationid"));
                model.setIclassid(rs.getLong("iclassid"));
                model.setIcollectname(rs.getString("icollectname"));
                model.setIcreatetime(rs.getLong("icreatetime"));
                model.setIcreateuser(rs.getString("icreateuser"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectInfoList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectInfoList", log);
        }
        return list;
    }

    public int getCountBySql ( Connection conn, String sql ) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("countNum");
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCountBySql is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCountBySql", log);
        }
        return count;
    }

    public CollectInfoModel getCollectInfoOne ( long iid ) throws RepositoryException
    {
        CollectInfoModel model = null;
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select iid, isarelationid, iclassid, icollectname, icreatetime, icreateuser from IEAI_UNIFYAGENT_COLLECTINFO where iid=?";
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DATA_COLLECT);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                model = new CollectInfoModel();
                model.setIid(rs.getLong("iid"));
                model.setIsarelationid(rs.getLong("isarelationid"));
                model.setIclassid(rs.getLong("iclassid"));
                model.setIcollectname(rs.getString("icollectname"));
                model.setIcreatetime(rs.getLong("icreatetime"));
                model.setIcreateuser(rs.getString("icreateuser"));
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectInfoOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getCollectInfoOne", log);
        }
        return model;
    }

    public List getCollectSystemTree ( Connection conn, String sql ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map map = new HashMap();
                map.put("id", rs.getLong("iid"));
                map.put("text", rs.getString("iname"));
                map.put("expanded", false);
                map.put("children", new ArrayList());
                list.add(map);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectSystemTree is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectSystemTree", log);
        }
        return list;
    }

    public List getCollectAgentTreeSon ( Connection conn, String sql ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map map = new HashMap();
                map.put("id", rs.getLong("iid"));
                map.put("text", rs.getString("iagent_ip"));
                map.put("expanded", false);
                map.put("children", new ArrayList());
                list.add(map);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectAgentTreeSon is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectAgentTreeSon", log);
        }
        return list;
    }

    public List getCollectCmInstList ( Connection conn, String sql, long collectInfoId ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                CollectModelModel model = new CollectModelModel();
                model.setImodelid(rs.getLong("iid"));
                model.setIcollectinfoid(collectInfoId);
                model.setImodelname(rs.getString("imodelname"));
                model.setIcollecttype(rs.getLong("icollecttype"));
                model.setIpersonal(rs.getLong("ipersonal"));
                model.setItopic(rs.getString("itopic"));
                model.setIcreatetime(rs.getLong("icreatetime"));
                model.setIcheckscript(rs.getString("icheckscript"));
                model.setIrunuser(rs.getString("irunuser"));
                model.setIoutputformat(rs.getString("ioutputformat"));
                model.setIfilesource(rs.getString("ifilesource"));
                model.setIintervaltime(rs.getString("iintervaltime"));
                model.setIreadposition(rs.getString("ireadposition"));
                model.setIcharset(rs.getString("icharset"));
                model.setImultiple(rs.getString("imultiple"));
                model.setIjdbcurl(rs.getString("ijdbcurl"));
                model.setIconnuser(rs.getString("iconnuser"));
                model.setIconnpassword(rs.getString("iconnpassword"));
                model.setImode(rs.getString("imode"));
                model.setIincrementingcol(rs.getString("iincrementingcol"));
                model.setItimestampcol(rs.getString("itimestampcol"));
                model.setIquery(rs.getString("iquery"));
                model.setItimingtype(rs.getLong("itimingtype"));
                model.setItimingexpression(rs.getString("itimingexpression"));
                model.setIsendkfk(rs.getLong("isendkfk"));
                model.setIcustomer(rs.getString("icustomer"));
                model.setImodifytime(rs.getLong("imodifytime"));
                model.setImainbakfilesize(rs.getString("IMAINBAKFILESIZE"));
                model.setIstransferblank(rs.getInt("ISTRANSFERBLANK"));
                model.setPostscript(rs.getString("postscript"));
                //新增加Excel 采集
                model.setExceladdress(rs.getString("exceladdress"));
                model.setExcelname(rs.getString(   "excelname"));
                model.setSheetname(rs.getString(   "sheetname"));
                model.setExcelkey(rs.getString(   "excelkey"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectModelModelList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectModelModelList", log);
        }
        return list;
    }

    public List getCollectCmInstList ( String sql ) throws RepositoryException
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DATA_COLLECT);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                CollectModelModel model = new CollectModelModel();
                model.setIid(rs.getLong("iid"));
                model.setImodelid(rs.getLong("imodelid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setImodelname(rs.getString("imodelname"));
                model.setIcollecttype(rs.getLong("icollecttype"));
                model.setIpersonal(rs.getLong("ipersonal"));
                model.setItopic(rs.getString("itopic"));
                model.setIcreatetime(rs.getLong("icreatetime"));
                model.setIcheckscript(rs.getString("icheckscript"));
                model.setIrunuser(rs.getString("irunuser"));
                model.setIoutputformat(rs.getString("ioutputformat"));
                model.setIfilesource(rs.getString("ifilesource"));
                model.setIintervaltime(rs.getString("iintervaltime"));
                model.setIreadposition(rs.getString("ireadposition"));
                model.setIcharset(rs.getString("icharset"));
                model.setImultiple(rs.getString("imultiple"));
                model.setIjdbcurl(rs.getString("ijdbcurl"));
                model.setIconnuser(rs.getString("iconnuser"));
                model.setIconnpassword(rs.getString("iconnpassword"));
                model.setImode(rs.getString("imode"));
                model.setIincrementingcol(rs.getString("iincrementingcol"));
                model.setItimestampcol(rs.getString("itimestampcol"));
                model.setIquery(rs.getString("iquery"));
                model.setItimingtype(rs.getLong("itimingtype"));
                model.setItimingexpression(rs.getString("itimingexpression"));
                model.setIsendkfk(rs.getLong("isendkfk"));
                model.setIcustomer(rs.getString("icustomer"));
                model.setImodifytime(rs.getLong("imodifytime"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectModelModelList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getCollectModelModelList", log);
        }
        return list;
    }

    public List getCollectClassAttrlList ( Connection conn, String sql, long collectInfoId )
            throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                ClassAttributeModel model = new ClassAttributeModel();
                model.setIclassattrid(rs.getLong("iid"));
                model.setIcollectinfoid(collectInfoId);
                model.setIattrname(rs.getString("iattrname"));
                model.setIattrcode(rs.getString("iattrcode"));
                model.setIattrvalue(rs.getString("idefaultvalue"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectClassAttrlList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectClassAttrlList", log);
        }
        return list;
    }

    public List getCollectCaInstListAll ( Connection conn, String sql ) throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                ClassAttributeModel model = new ClassAttributeModel();
                model.setIid(rs.getLong("iid"));
                model.setIclassattrid(rs.getLong("iclassattrid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setIattrname(rs.getString("iattrname"));
                model.setIattrcode(rs.getString("iattrcode"));
                model.setIattrvalue(rs.getString("iattrvalue"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectCaInstListAll is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectCaInstListAll", log);
        }
        return list;
    }

    public List getCollectCaInstList ( Connection conn, String sql, Integer start, Integer limit )
            throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else
            {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                ClassAttributeModel model = new ClassAttributeModel();
                model.setIid(rs.getLong("iid"));
                model.setIclassattrid(rs.getLong("iclassattrid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setIattrname(rs.getString("iattrname"));
                model.setIattrcode(rs.getString("iattrcode"));
                model.setIattrvalue(rs.getString("iattrvalue"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getClassAttributeList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getClassAttributeList", log);
        }
        return list;
    }

    public boolean isChangeClass ( CollectInfoModel model ) throws RepositoryException
    {
        List list = new ArrayList();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select * from ieai_unifyagent_collectinfo where iid=? and iclassid=?";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DATA_COLLECT);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, model.getIid());
            ps.setLong(2, model.getIclassid());
            rs = ps.executeQuery();
            while (rs.next())
            {
                return false;
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.isChangeClass is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "isChangeClass", log);
        }
        return true;
    }

    public ClassAttributeModel getCollectCaInstOne ( long iid, Connection conn ) throws RepositoryException
    {
        ClassAttributeModel model = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select iid, iclassattrid, icollectinfoid, iattrname, iattrcode, iattrvalue from IEAI_UNIFYAGENT_CA_INST where iid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                model = new ClassAttributeModel();
                model.setIid(rs.getLong("iid"));
                model.setIclassattrid(rs.getLong("iclassattrid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setIattrname(rs.getString("iattrname"));
                model.setIattrcode(rs.getString("iattrcode"));
                model.setIattrvalue(rs.getString("iattrvalue"));
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectCaInstOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectCaInstOne", log);
        }
        return model;
    }

    public List getCollectCmInstList ( Connection conn, String sql, String sql1, Integer start, Integer limit )
            throws RepositoryException
    {
        List list = new ArrayList();
        PreparedStatement ps = null;
        ResultSet rs = null;
        PreparedStatement ps1 = null;
        ResultSet rs1 = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps1 = conn.prepareStatement(sql1);
            if (JudgeDB.IEAI_DB_TYPE == 3)
            {
                ps.setInt(1, start);
                ps.setInt(2, limit);
            } else
            {
                ps.setInt(1, start);
                ps.setInt(2, start + limit);
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                CollectModelModel model = new CollectModelModel();
                model.setIid(rs.getLong("iid"));
                model.setImodelid(rs.getLong("imodelid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setImodelname(rs.getString("imodelname"));
                model.setIcollecttype(rs.getLong("icollecttype"));
                model.setIpersonal(rs.getLong("ipersonal"));
                model.setItopic(rs.getString("itopic"));
                model.setIcreatetime(rs.getLong("icreatetime"));
                model.setIcheckscript(rs.getString("icheckscript"));
                model.setIrunuser(rs.getString("irunuser"));
                model.setIoutputformat(rs.getString("ioutputformat"));
                model.setIfilesource(rs.getString("ifilesource"));
                model.setIintervaltime(rs.getString("iintervaltime"));
                model.setIreadposition(rs.getString("ireadposition"));
                model.setIcharset(rs.getString("icharset"));
                model.setImultiple(rs.getString("imultiple"));
                model.setIjdbcurl(rs.getString("ijdbcurl"));
                model.setIconnuser(rs.getString("iconnuser"));
                model.setIconnpassword(rs.getString("iconnpassword"));
                model.setImode(rs.getString("imode"));
                model.setIincrementingcol(rs.getString("iincrementingcol"));
                model.setItimestampcol(rs.getString("itimestampcol"));
                model.setIquery(rs.getString("iquery"));
                model.setItimingtype(rs.getLong("itimingtype"));
                model.setItimingexpression(rs.getString("itimingexpression"));
                model.setIsendkfk(rs.getLong("isendkfk"));
                model.setIcustomer(rs.getString("icustomer"));
                model.setImodifytime(rs.getLong("imodifytime"));
                model.setImainbakfilesize(rs.getString("imainbakfilesize"));
                model.setIstransferblank(rs.getInt("istransferblank"));
                //新增加后置脚本采集字段
                model.setPostscript(rs.getString("postscript"));
                //新增加Excel 采集
                model.setExceladdress(rs.getString("exceladdress"));
                model.setExcelname(rs.getString(   "excelname"));
                model.setSheetname(rs.getString(   "sheetname"));
                model.setNameFormat(rs.getString("itimestampcol"));
                //查询新增加excelkey、excelchar  字段  jiaMing 2022-12-14
                model.setExcelkey(rs.getString(   "excelkey"));
                List<String> stateList = new ArrayList<>();
                if(rs.getLong("iid") != 0){
                    ps1.setLong(1, rs.getLong("iid"));
                    rs1 = ps1.executeQuery();
                    while(rs1.next()){
                        stateList.add(rs1.getString("irunstate"));
                    }
                    model.setStateList(stateList);
                }
                if(stateList.isEmpty()){// 如果没有状态值，默认赋值为0（未运行）
                    stateList.add("0");
                }
                
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectCmInstList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectCmInstList", log);
            DBResource.closePSRS(rs1, ps1, "getCollectCmInstList", log);
        }
        return list;
    }

    public int getCollectCmInstCount ( Connection conn, String sql ) throws RepositoryException
    {
        int count = 0;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                count = rs.getInt("countNum");
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectCmInstCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectCmInstCount", log);
        }
        return count;
    }

    public CollectModelModel getCollectCmInstOne ( long iid, Connection conn ) throws RepositoryException
    {
        CollectModelModel model = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            String sql = "select iid, imodelid, icollectinfoid, imodelname, icollecttype, ipersonal, itopic, icreatetime, icheckscript, irunuser, ioutputformat, ifilesource, iintervaltime, ireadposition, icharset, imultiple, ijdbcurl, iconnuser, iconnpassword, imode, iincrementingcol, itimestampcol, iquery, itimingtype, itimingexpression,isendkfk,icustomer,imodifytime from IEAI_UNIFYAGENT_CM_INST where iid=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                model = new CollectModelModel();
                model.setIid(rs.getLong("iid"));
                model.setImodelid(rs.getLong("imodelid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setImodelname(rs.getString("imodelname"));
                model.setIcollecttype(rs.getLong("icollecttype"));
                model.setIpersonal(rs.getLong("ipersonal"));
                model.setItopic(rs.getString("itopic"));
                model.setIcreatetime(rs.getLong("icreatetime"));
                model.setIcheckscript(rs.getString("icheckscript"));
                model.setIrunuser(rs.getString("irunuser"));
                model.setIoutputformat(rs.getString("ioutputformat"));
                model.setIfilesource(rs.getString("ifilesource"));
                model.setIintervaltime(rs.getString("iintervaltime"));
                model.setIreadposition(rs.getString("ireadposition"));
                model.setIcharset(rs.getString("icharset"));
                model.setImultiple(rs.getString("imultiple"));
                model.setIjdbcurl(rs.getString("ijdbcurl"));
                model.setIconnuser(rs.getString("iconnuser"));
                model.setIconnpassword(rs.getString("iconnpassword"));
                model.setImode(rs.getString("imode"));
                model.setIincrementingcol(rs.getString("iincrementingcol"));
                model.setItimestampcol(rs.getString("itimestampcol"));
                model.setIquery(rs.getString("iquery"));
                model.setItimingtype(rs.getLong("itimingtype"));
                model.setItimingexpression(rs.getString("itimingexpression"));
                model.setIsendkfk(rs.getLong("isendkfk"));
                model.setIcustomer(rs.getString("icustomer"));
                model.setImodifytime(rs.getLong("imodifytime"));
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectCmInstOne is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "getCollectCmInstOne", log);
        }
        return model;
    }

    public void checkCollectInfoById ( Connection conn, long id, StringBuilder sb, List<String> strList )
            throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select a.icollectname from ieai_unifyagent_collectinfo a,ieai_unifyagent_run_inst b where a.iid=b.icollectinfoid and b.irunstate=1 and a.iid=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, id);
            rs = ps.executeQuery();
            while (rs.next())
            {
                String icollectname = rs.getString("icollectname");
                if (!strList.contains(icollectname))
                {
                    strList.add(icollectname);
                    sb.append("采集名称：【" + icollectname + "】,有正在运行的采集任务，无法删除。</br>");
                }
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.checkCollectInfoById is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "checkCollectInfoById", log);
        }
    }

    public List<CollectRunInstModel> getCollectRunInstList ( String sql ) throws RepositoryException
    {
        List<CollectRunInstModel> list = new ArrayList<CollectRunInstModel>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DATA_COLLECT);
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                CollectRunInstModel model = new CollectRunInstModel();
                model.setIid(rs.getLong("iid"));
                model.setIcollectinfoid(rs.getLong("icollectinfoid"));
                model.setIcminstid(rs.getLong("icminstid"));
                model.setIagentid(rs.getLong("iagentid"));
                model.setIagentip(rs.getString("iagentip"));
                model.setIagentport(rs.getLong("iagentport"));
                model.setIruntime(rs.getLong("iruntime"));
                model.setIrunstate(rs.getLong("irunstate"));
                model.setIreturnid(rs.getLong("ireturnid"));
                model.setIscriptinfo(rs.getString("iscriptinfo"));
                model.setImodelname(rs.getString("imodelname"));
                model.setIcollecttype(rs.getLong("icollecttype"));
                model.setIpersonal(rs.getLong("ipersonal"));
                model.setItimingtype(rs.getLong("itimingtype"));
                model.setItimingexpression(rs.getString("itimingexpression"));
                list.add(model);
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.getCollectRunInstList is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getCollectRunInstList", log);
        }
        return list;
    }

    public void validateModelInfo ( long iid, long icollectinfoid, String imodelname, StringBuilder sb )
            throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_DATA_COLLECT);
            String sql = "select imodelname from ieai_unifyagent_cm_inst where imodelname=? and icollectinfoid=? and iid !=?  group by imodelname";
            ps = conn.prepareStatement(sql);
            ps.setString(1, imodelname);
            ps.setLong(2, icollectinfoid);
            ps.setLong(3, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                sb.append("模板名称：【" + imodelname + "】，重复！！！</br>");
            }
        } catch (Exception e)
        {
            log.error("CollectInfoManage.validateModelInfo is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "validateModelInfo", log);
        }

    }

}
