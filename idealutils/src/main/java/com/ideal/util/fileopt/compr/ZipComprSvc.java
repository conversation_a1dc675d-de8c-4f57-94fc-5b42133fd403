package com.ideal.util.fileopt.compr;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import org.apache.log4j.Logger;
import org.apache.tools.ant.Project;
import org.apache.tools.ant.taskdefs.Zip;
import org.apache.tools.ant.types.FileSet;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;

import com.ideal.util.fileopt.FileOptException;

/**
 * <ul>
 * <li>Title: ZipComprSvc.java</li>
 * <li>Description:zip包的压缩与解压</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2017-5-17
 */
public class ZipComprSvc implements IfcCompr
{
    private static final Logger _log     = Logger.getLogger(ZipComprSvc.class);
    private static final int    iBufSize = 1024 * 10;

    public ZipComprSvc()
    {
    }

    // TODO 有需要时再进行补充
    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * 2017年10月19日 
     * @param srcPath
     * @param targetPath
     * @return
     * @throws FileOptException
     */
    @Override
    public boolean compress ( String srcPath, String targetPath ) throws FileOptException
    {
        return true;
    }

    @Override
    public File compress ( String srcPath, String targetPaht, String compressFiledName ) throws FileOptException
    {
        File targetdir = new File(targetPaht);
        File targetFile = new File(targetPaht + File.separator + compressFiledName);
        File srcFile = new File(srcPath);

        if (!srcFile.exists())  // When save btn clicked sometimes that saved some times. we should
                                // be decompress firstly..
        {
            decompress(targetPaht + compressFiledName, targetPaht);
        }

        if (!targetdir.exists())
        {
            throw new RuntimeException(srcPath + "不存在！");
        }
        if (targetFile.exists())
        {
            targetFile.delete();
        }

        Project prj = new Project();
        Zip zip = new Zip();
        zip.setProject(prj);
        zip.setDestFile(targetFile);
        FileSet fileSet = new FileSet();
        fileSet.setProject(prj);
        fileSet.setDir(targetdir);
        zip.addFileset(fileSet);
        zip.execute();

        File unUserdFile = new File(srcPath);
        deleteDir(unUserdFile);             // delete dirty data
        return targetFile;
    }

    // TODO 有需要时再进行补充
    /**
     * 
     * <li>Description:</li> 
     * <AUTHOR>
     * 2017年10月19日 
     * @param srcFilePath
     * @param targetDir
     * @return
     */
    @Override
    public List<String> decompressToTargetDir ( String srcFilePath, String targetDir )
    {
        return null;
    }

    @Override
    public List<String> decompress ( String srcFilePath, String targetFilePath )
    {

        byte[] bufs = new byte[iBufSize];
        int len = -1;

        File targetFile = null;
        File srcFile = null;
        ZipFile zipFile = null;
        File tmpdir = null;

        InputStream is = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;

        ZipEntry zipEnt = null;
        String rootPath = null;
        String zipEntryName = null;
        String outPutPath = null;

        List<String> list = new ArrayList<String>();

        try
        {
            targetFile = new File(targetFilePath);
            if (!targetFile.exists())
                targetFile.mkdirs();

            srcFile = new File(srcFilePath);
            if ((!srcFile.exists()) && (srcFile.length() <= 0))
            {
                throw new FileOptException("要解压的文件不存在!");
            }

            rootPath = targetFile.getAbsolutePath();
            zipFile = new ZipFile(srcFilePath, "GBK");  // 处理中文文件名乱码的问题

            Enumeration<ZipEntry> em = zipFile.getEntries();
            while (null != em && em.hasMoreElements())
            {
                String fullPath = "";
                zipEnt = em.nextElement();
                zipEntryName = zipEnt.getName();
                fullPath = rootPath + File.separator + zipEntryName;
                list.add(fullPath);

                if (zipEnt.isDirectory())
                {
                    outPutPath = rootPath + File.separator + zipEntryName;
                    tmpdir = new File(outPutPath);
                    tmpdir.mkdirs();
                } else
                {
                    // 建目录
                    zipEntryName = zipEnt.getName();
                    String strsubdir = zipEntryName;
                    for (int i = 0; i < strsubdir.length(); i++)
                    {
                        if ((strsubdir.substring(i, i + 1).equalsIgnoreCase("\\"))
                                || (strsubdir.substring(i, i + 1).equalsIgnoreCase("/")))
                        {
                            String tempFilName = rootPath + File.separator + strsubdir.substring(0, i);
                            File subdir = new File(tempFilName);
                            if (!subdir.exists())
                                subdir.mkdir();
                        }
                    }

                    outPutPath = rootPath + File.separator + zipEntryName;
                    is = zipFile.getInputStream(zipEnt);
                    bis = new BufferedInputStream(is, iBufSize);
                    fos = new FileOutputStream(outPutPath);
                    bos = new BufferedOutputStream(fos);
                    len = 0;
                    while ((len = bis.read(bufs, 0, iBufSize)) != -1)
                    {
                        bos.write(bufs, 0, len);
                    }

                    bos.flush();
                    is.close();
                    bis.close();
                    fos.close();
                    bos.close();
                }
            }
            zipFile.close();
        } catch (Exception e)
        {
            e.printStackTrace();
        } finally
        {
            try
            {
                if (null != is)
                {
                    is.close();
                }
            } catch (IOException e)
            {
                e.printStackTrace();
                _log.error(e.toString());
            }
            try
            {
                if (null != bis)
                {
                    bis.close();
                }
            } catch (IOException e)
            {
                e.printStackTrace();
                _log.error(e.toString());
            }
            try
            {
                if (null != fos)
                {
                    fos.close();
                }
            } catch (IOException e)
            {
                e.printStackTrace();
                _log.error(e.toString());
            }
            try
            {
                if (null != bos)
                {
                    bos.close();
                }
            } catch (IOException e)
            {
                e.printStackTrace();
                _log.error(e.toString());
            }
            try
            {
                if (null != zipFile)
                {
                    zipFile.close();
                }
            } catch (IOException e)
            {
                e.printStackTrace();
                _log.error(e.toString());
            }
        }
        return list;
    }

    /**
     * <li>Description:删除目录</li> 
     * <AUTHOR>
     * 2017-5-24 
     * @param dir
     * @return
     * return boolean
     */
    public static boolean deleteDir ( File dir )
    {
        boolean isSuccess = false;
        try
        {
            if (null != dir && dir.isDirectory())
            {
                String[] arrFile = dir.list();
                File fTmp = null;
                if (null != arrFile)
                {
                    for (int i = 0; i < arrFile.length; i++)
                    {
                        fTmp = new File(dir, arrFile[i]); // the Ponit
                        isSuccess = deleteDir(fTmp);
                    }
                }
            }
            isSuccess = dir.delete();
        } catch (Exception e)
        {
            _log.error(e.toString());
        }
        return isSuccess;
    }

}
