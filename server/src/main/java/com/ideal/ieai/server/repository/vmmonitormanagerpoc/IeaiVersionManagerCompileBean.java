package com.ideal.ieai.server.repository.vmmonitormanagerpoc;

public class IeaiVersionManagerCompileBean
{
    private long iid;//IID NUMBER(19) NOT NULL,
    private long mainid;//MAINID NUMBER(19),--主表id
    private String itargetserver;//ITARGETSERVER  VARCHAR2(255),--目标服务器
    private int icheckflag;//ICHECKFLAG  INTEGER,--代码提取标识  1最新 2指定版本号
    private String iassginversion;//IASSGINVERSION VARCHAR2(255),--提取版本号
    private String  ibranchpath;//IBRANCHPATH  VARCHAR2(255),--分支
    private String  icodesavepath;//ICODESAVEPATH VARCHAR2(255),--代码存放路径
    private int itools;//ITOOLS INTEGER,--构建工具  1ANT 2MAVEN
    private int imergerflag;//IMERGEFLAG  INTEGER,--是否合并基线  0否 1是
    private String icompilefilepath;//ICOMPILEFILEPATH  VARCHAR2(255),--编译文件路径
    private int icompileflag;//ICOMPILEFLAG INTEGER,--构建频率  1定时构建 2周期构建
    private long icompiletime;//ICOMPILETIME NUMBER(19),--构建时间
    private String icompilecycle;//ICOMPILECYCLE VARCHAR2(255),--构建周期
    private int iisocheckflag;//IISOCHECKFLAG   INTEGER,--是否创建镜像  0否 1是
    private String idockerpushpath;//IDOCKERPUSHPATH VARCHAR2(255),--DOCKER推送
    private String ipretask;//IPRETASK VARCHAR2(255),--前置任务
    private String iposttask;//IPOSTTASK VARCHAR2(255),--后置任务
    private int istestcheckflag;//ISTESTCHECKFLAG   INTEGER,--测试  0否 1是
    private String itestcontent;//ITESTCONTENT VARCHAR2(4000),--测试内容
    private int compflag;
    private String  ibussname;//业务系统名
    private String  ibussno;//业务系统简称
    private String  ibaselinepath;//基线url
    private String iusername;//用户名
    private String ipassword;//密码
    private String uploadFilelist;  // 上传文件清单
    private String   iprijectnamepoc;//项目名称
    private String    imodeltypename;//模块类型
    private String irequirementnumber;//需求编号
    public String getIprijectnamepoc ()
    {
        return iprijectnamepoc;
    }
    public void setIprijectnamepoc ( String iprijectnamepoc )
    {
        this.iprijectnamepoc = iprijectnamepoc;
    }
    public String getImodeltypename ()
    {
        return imodeltypename;
    }
    public void setImodeltypename ( String imodeltypename )
    {
        this.imodeltypename = imodeltypename;
    }

    public String getUploadFilelist ()
    {
        return uploadFilelist;
    }
    public void setUploadFilelist ( String uploadFilelist )
    {
        this.uploadFilelist = uploadFilelist;
    }
    public String getIbussname ()
    {
        return ibussname;
    }
    public void setIbussname ( String ibussname )
    {
        this.ibussname = ibussname;
    }
    public String getIbussno ()
    {
        return ibussno;
    }
    public void setIbussno ( String ibussno )
    {
        this.ibussno = ibussno;
    }
    public String getIbaselinepath ()
    {
        return ibaselinepath;
    }
    public void setIbaselinepath ( String ibaselinepath )
    {
        this.ibaselinepath = ibaselinepath;
    }
    public String getIusername ()
    {
        return iusername;
    }
    public void setIusername ( String iusername )
    {
        this.iusername = iusername;
    }
    public String getIpassword ()
    {
        return ipassword;
    }
    public void setIpassword ( String ipassword )
    {
        this.ipassword = ipassword;
    }
    public int getCompflag ()
    {
        return compflag;
    }
    public void setCompflag ( int compflag )
    {
        this.compflag = compflag;
    }
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public long getMainid ()
    {
        return mainid;
    }
    public void setMainid ( long mainid )
    {
        this.mainid = mainid;
    }
    public String getItargetserver ()
    {
        return itargetserver;
    }
    public void setItargetserver ( String itargetserver )
    {
        this.itargetserver = itargetserver;
    }
    public int getIcheckflag ()
    {
        return icheckflag;
    }
    public void setIcheckflag ( int icheckflag )
    {
        this.icheckflag = icheckflag;
    }
    public String getIassginversion ()
    {
        return iassginversion;
    }
    public void setIassginversion ( String iassginversion )
    {
        this.iassginversion = iassginversion;
    }
    public String getIbranchpath ()
    {
        return ibranchpath;
    }
    public void setIbranchpath ( String ibranchpath )
    {
        this.ibranchpath = ibranchpath;
    }
    public String getIcodesavepath ()
    {
        return icodesavepath;
    }
    public void setIcodesavepath ( String icodesavepath )
    {
        this.icodesavepath = icodesavepath;
    }
    public int getItools ()
    {
        return itools;
    }
    public void setItools ( int itools )
    {
        this.itools = itools;
    }
    public int getImergerflag ()
    {
        return imergerflag;
    }
    public void setImergerflag ( int imergerflag )
    {
        this.imergerflag = imergerflag;
    }
    public String getIcompilefilepath ()
    {
        return icompilefilepath;
    }
    public void setIcompilefilepath ( String icompilefilepath )
    {
        this.icompilefilepath = icompilefilepath;
    }
    public int getIcompileflag ()
    {
        return icompileflag;
    }
    public void setIcompileflag ( int icompileflag )
    {
        this.icompileflag = icompileflag;
    }
    public long getIcompiletime ()
    {
        return icompiletime;
    }
    public void setIcompiletime ( long icompiletime )
    {
        this.icompiletime = icompiletime;
    }
    public String getIcompilecycle ()
    {
        return icompilecycle;
    }
    public void setIcompilecycle ( String icompilecycle )
    {
        this.icompilecycle = icompilecycle;
    }
    public int getIisocheckflag ()
    {
        return iisocheckflag;
    }
    public void setIisocheckflag ( int iisocheckflag )
    {
        this.iisocheckflag = iisocheckflag;
    }
    public String getIdockerpushpath ()
    {
        return idockerpushpath;
    }
    public void setIdockerpushpath ( String idockerpushpath )
    {
        this.idockerpushpath = idockerpushpath;
    }
    public String getIpretask ()
    {
        return ipretask;
    }
    public void setIpretask ( String ipretask )
    {
        this.ipretask = ipretask;
    }
    public String getIposttask ()
    {
        return iposttask;
    }
    public void setIposttask ( String iposttask )
    {
        this.iposttask = iposttask;
    }
    public int getIstestcheckflag ()
    {
        return istestcheckflag;
    }
    public void setIstestcheckflag ( int istestcheckflag )
    {
        this.istestcheckflag = istestcheckflag;
    }
    public String getItestcontent ()
    {
        return itestcontent;
    }
    public void setItestcontent ( String itestcontent )
    {
        this.itestcontent = itestcontent;
    }

    public String getIrequirementnumber() {
        return irequirementnumber;
    }

    public void setIrequirementnumber(String irequirementnumber) {
        this.irequirementnumber = irequirementnumber;
    }
}
