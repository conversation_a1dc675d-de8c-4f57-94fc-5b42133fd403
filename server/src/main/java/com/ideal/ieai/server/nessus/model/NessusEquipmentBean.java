package com.ideal.ieai.server.nessus.model;

import java.sql.Timestamp;

public class NessusEquipmentBean
{
    private Long   iid;
    private Long   inessusid;
    private String ip;
    private Long   cpid;
    private Long   istatus;
    private String icreatetime;
    private Timestamp createtime;
    
    //必须缺陷类型
    private String mustdefecttype;
    //可选缺陷类型
    private String nomustdefecttype;
    //必须缺陷个数
    private String mustdefectnum;
    //可选缺陷个数
    private String nomustdefectnum;
    //操作系统
    private String osname;
    //是否符合基线标准
    private String basestandard;
    //是否修复
    private String isrepair;
    
    //待修复漏洞个数
    private int repairnessusnum;
    //无需修复漏洞个数
    private int norepairnessusnum;
    
    private int imustcount;
    
    private int iscreate;
    private String iscreatename;
    
    private String isnanotube;
    //,T.SYSTEMINFO,AC.ICHARGEUSER
    private String systemInfo;//信息系统名称
    private String iChargeUser;//负责人
    public String getSystemInfo ()
    {
        return systemInfo;
    }

    public void setSystemInfo ( String systemInfo )
    {
        this.systemInfo = systemInfo;
    }

    public String getiChargeUser ()
    {
        return iChargeUser;
    }

    public void setiChargeUser ( String iChargeUser )
    {
        this.iChargeUser = iChargeUser;
    }

    public int getImustcount ()
    {
        return imustcount;
    }

    public void setImustcount ( int imustcount )
    {
        this.imustcount = imustcount;
        if(imustcount>0){
            this.isrepair = "否";
        }else{
            this.isrepair = "是";
        }
    }

    public void setBasestandard ( String basestandard )
    {
        this.basestandard = basestandard;
    }

    public void setIsrepair ( String isrepair )
    {
        this.isrepair = isrepair;
    }

    public String getBasestandard ()
    {
        return basestandard;
    }

    public String getIsrepair ()
    {
        return isrepair;
    }

    public Timestamp getCreatetime ()
    {
        return createtime;
    }

    public void setCreatetime ( Timestamp createtime )
    {
        this.createtime = createtime;
    }

    private String inessusplus;

    public Long getIid ()
    {
        return iid;
    }

    public void setIid ( Long iid )
    {
        this.iid = iid;
    }

    public Long getInessusid ()
    {
        return inessusid;
    }

    public void setInessusid ( Long inessusid )
    {
        this.inessusid = inessusid;
    }

    public String getIp ()
    {
        return ip;
    }

    public void setIp ( String ip )
    {
        this.ip = ip;
    }

    public Long getCpid ()
    {
        return cpid;
    }

    public void setCpid ( Long cpid )
    {
        this.cpid = cpid;
    }

    public Long getIstatus ()
    {
        return istatus;
    }

    public void setIstatus ( Long istatus )
    {
        this.istatus = istatus;
    }

    public String getIcreatetime ()
    {
        return icreatetime;
    }

    public void setIcreatetime ( String icreatetime )
    {
        this.icreatetime = icreatetime;
    }

    public String getInessusplus ()
    {
        return inessusplus;
    }

    public void setInessusplus ( String inessusplus )
    {
        this.inessusplus = inessusplus;
    }

    

    public String getMustdefecttype ()
    {
        return mustdefecttype;
    }

    public void setMustdefecttype ( String mustdefecttype )
    {
        this.mustdefecttype = mustdefecttype;
    }

    public String getNomustdefecttype ()
    {
        return nomustdefecttype;
    }

    public void setNomustdefecttype ( String nomustdefecttype )
    {
        this.nomustdefecttype = nomustdefecttype;
    }

    public String getMustdefectnum ()
    {
        return mustdefectnum;
    }

    public void setMustdefectnum ( String mustdefectnum )
    {
        this.mustdefectnum = mustdefectnum;
        if(Integer.valueOf(mustdefectnum)>0){
            this.basestandard = "否";
        }else{
            this.basestandard = "是";
        }
    }

    public String getNomustdefectnum ()
    {
        return nomustdefectnum;
    }

    public void setNomustdefectnum ( String nomustdefectnum )
    {
        this.nomustdefectnum = nomustdefectnum;
    }

    public String getOsname ()
    {
        return osname;
    }

    public void setOsname ( String osname )
    {
        this.osname = osname;
    }

    public int getRepairnessusnum ()
    {
        return repairnessusnum;
    }

    public void setRepairnessusnum ( int repairnessusnum )
    {
        this.repairnessusnum = repairnessusnum;
    }

    public int getNorepairnessusnum ()
    {
        return norepairnessusnum;
    }

    public void setNorepairnessusnum ( int norepairnessusnum )
    {
        this.norepairnessusnum = norepairnessusnum;
    }

    public int getIscreate ()
    {
        return iscreate;
    }

    public void setIscreate ( int iscreate )
    {
        if(iscreate>0){
            this.iscreatename = "是";
        }else{
            this.iscreatename = "否";
        }
        this.iscreate = iscreate;
    }

    public String getIscreatename ()
    {
        return iscreatename;
    }

    public void setIscreatename ( String iscreatename )
    {
        this.iscreatename = iscreatename;
    }

    public String getIsnanotube ()
    {
        return isnanotube;
    }

    public void setIsnanotube ( String isnanotube )
    {
        this.isnanotube = isnanotube;
    }
    
}
