package com.ideal.ieai.communication.marshall.datahelper;

import java.io.*;



/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR>
 * @version 1.0
 */

public class WBXMLDecoder
    implements IDecoder
{
    public WBXMLDecoder()
    {
    }

    /**
     * to set content of decoder
     * @param content
     */
    public void setDecoderContent(Object content)
    {
        if (null == content || ! (content instanceof byte[]))
        {
            return;
        }

        /** init content of wbxml input stream */
        this.content = new ByteArrayInputStream( (byte[]) content);
    }

    /**
     * prepare to parse:to read information of byte stream
     * and parser header info in it
     */
    public void prepare() throws StreamFormatException
    {


        try
        {
            /** skip version info */
            DeserializeUtil.readByte(this.content);

            /** skip the public ID id info */
            int publicIdentifierId = DeserializeUtil.readInt(this.content);
            if (publicIdentifierId == 0)
            {
                DeserializeUtil.readInt(this.content);
            }

            /** skip charSet info */
            DeserializeUtil.readInt(this.content);

            /** to get the string table information and init string table*/
            int strTabSize = DeserializeUtil.readInt(this.content);
            stringTable = new char[strTabSize];

            for (int i = 0; i < strTabSize; i++)
            {
                stringTable[i] = (char) DeserializeUtil.readByte(this.content);
            }
        }
        catch (Exception ex)
        {
            throw new StreamFormatException();
        }
    }

    public void readContent() throws StreamFormatException
    {

        try
        {
            int id = content.read();
            String tag = DeserializeUtil.resolveId(tagTable, id & 0x03f, this.content,
                this.stringTable);

            this.returnValue = ObjectDeserializer.deserialize(this.stringTable, this.tagTable,
                this.content, tag);
        }
        catch (IOException ex)
        {
            //no code
        }

    }

    public void end()
    {
    }

    public Object getResult()
    {
        return returnValue;
    }

    /** content of a wbxml byte array */
    private ByteArrayInputStream content;

    /** string table of header of wbxml,store element tag in it */
    private char[] stringTable;

    /** tag table of element */
    private String[] tagTable;

    /**return value */
    private Object returnValue;

}
