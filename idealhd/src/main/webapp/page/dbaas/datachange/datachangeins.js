Ext.onReady(function() {
    destroyRubbish();
    Ext.define('insData', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        },
        {
            name: 'scriptName',
            type: 'string'
        },
        {
            name: 'agentIp',
            type: 'string'
        },
        {
            name: 'agentPort',
            type: 'string'
        },
        {
            name: 'startTime',
            type: 'string'
        },
        {
            name: 'endTime',
            type: 'string'
        },
        {
            name: 'state',
            type: 'int'
        },
        {
            name: 'runTime',
            type: 'string'
        },
        {name: 'sysName',     type: 'string'},
        {name: 'dbType',     type: 'string'},
        {name: 'dbName',     type: 'string'},
        {name: 'dbuser',     type: 'string'},
        {name: 'dbdriver',   type: 'string'},
        {name: 'stepId',   type: 'string'},
        {name: 'stepName',   type: 'string'},
        {name: 'dbUrl',     type: 'string'}]
    });

    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
   var insStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 50,
        model: 'insData',
        proxy: {
            type: 'ajax',
            url: 'getDataChangeExecList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
   insStore.on('beforeload', function(store, options) {
	      var new_params = {
	    		taskId: flowId
	      };
	      Ext.apply(insStore.proxy.extraParams, new_params);
	  });
    var columns = [{
        text: '步骤主键',
        dataIndex: 'iid',
        hidden: true
    },
    {
        text: 'stepId',
        dataIndex: 'stepId',
        hidden: true
    },
    {
        text: '执行状态',
        dataIndex: 'state',
        width: 80,
        renderer: function(value, p, record) {
            var backValue = "未运行";
            //// -1 草稿，0作廢，1待审核，2审核通过，3运行中，4运行完成，5终止，6退回，7退回待修改，8执行异常，9agent连接不上,10 删除
            if (value == 9) {
                backValue = '<span class="Ignore State_Color">Agent异常</span>';
            } else if (value == 3) {
                backValue = '<span class="Run_Green State_Color">运行</span>';
            } else if (value == 4) {
                backValue = '<span class="Complete_Green State_Color">完成</span>';
            } else if (value == 8) {
                backValue = '<span class="Abnormal_yellow State_Color">异常</span>';
            } else if (value == 5) {
                backValue = '<span class="Complete_Green State_Color">已确认</span>';
            } else if (value == 11) {
                backValue = '<span class="Kill_red State_Color">已确认</span>';
            }
            return backValue;
        }
    },
    { text: '步骤名称',  dataIndex: 'stepName',width:80},
    { text: '系统名称',  dataIndex: 'sysName',minWidth:80,flex:1},
    {
        text: 'Agent地址',
        dataIndex: 'agentIp',
        width:100
    },
    {
        text: 'Agent端口',
        dataIndex: 'agentPort',
        width: 80
    },
    {
        text: '开始时间',
        dataIndex: 'startTime',
        width: 160
    },
    {
        text: '结束时间',
        dataIndex: 'endTime',
        width: 160
    },
    {
        text: '耗时（秒）',
        dataIndex: 'runTime',
        width: 100
    },
    {
    	text: '数据类型',
    	dataIndex: 'dbType',
    	width: 80
    },
    {
    	text: '服务名',
    	dataIndex: 'dbName',
    	width: 80,
    	hidden:true
    },
    {
    	text: '数据库用户名',
    	dataIndex: 'dbuser',
    	width: 80
    },
    {
    	text: '数据库Url参数',
    	dataIndex: 'dbUrl',
    	minWidth: 300,flex:1,renderer:function (value, metaData, record, rowIdx, colIdx, store){  
            metaData.tdAttr = 'data-qtip="' + Ext.String.htmlEncode(value) + '"';  
            return value;  
        }
    },
    {
        text: '操作',
        xtype : 'actiontextcolumn',
        width: (showFlag=='his' || showFlag=='permhis')?60:160,
		align : 'left',
        items : [{
			text : '详情',
			iconCls : 'monitor_search',
			handler : function(grid, rowIndex) {
				var iid = gridForIns.getStore().data.items[rowIndex].data.iid;
        		var actOutPut = Ext.create ('Ext.form.field.TextArea',
        				{
//        					fieldLabel : '活动日志',
//        					labelWidth : 70,
        					grow : true,
        					height : 500,
        					name : 'actOutPut',
        					margin :'0 0 0 0',
        					anchor : '100%'
        				});
        		var fp2 = new Ext.form.Panel({
        				border : false,
        				height : '100%',
        				padding : 5,
        				fieldDefaults : {
        					labelWidth : 60,
        					labelAlign : 'right'
        				},
        				defaultType : 'textfield',
        				items : [actOutPut ]
        			});
        			var runningWindow;
        			if (runningWindow == undefined || !runningWindow.isVisible()) {
        				runningWindow = Ext.create('Ext.window.Window', {
        					title : '输出详情',
        					modal : true,
        					closeAction : 'destroy',
        					constrain : true,
        					autoScroll : true,
        					width : contentPanel.getWidth(),
        					height : contentPanel.getHeight(),
        					items : [ fp2 ],
        					layout : 'fit'
        				});
        				Ext.Ajax.request({
        					url : 'dataChangeInsClob.do',
        					params : {
        						requestId : iid,
        					},
        					success : function(response, opts) {
        						var msg = Ext.decode(response.responseText);
        						var output = msg.message;
        						actOutPut.getEl().dom.innerHTML='<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:5px; left:5px;bottom:5px;right:0px; width:98%;overflow:auto;white-space:pre-wrap;margin-top:10px;"><pre>'+output+'</pre></div>';
        					},
        					failure : function(response, opts) {
        					}
        				});
        			}
        			runningWindow.show();
			}
		},{
			text : '重试',
			iconCls : 'monitor_search',
			hidden:true,
			handler : function(grid, rowIndex) {
				var iid = grid.getStore().data.items[rowIndex].data.iid;
            	var state =  grid.getStore().data.items[rowIndex].data.state;
            	reTrySExec(iid,state);
			},
			getClass : function(v, metadata, record) {
				var state=record.data.state;
				if (state == '3' || state == '11') {
					return 'x-hidden';
				}
				if (showFlag=='his') {
					return 'x-hidden';
				}
			}
		}
//		,{
//			text : '确认',
//			iconCls : 'monitor_search',
//			handler : function(grid, rowIndex) {
//				var iid = grid.getStore().data.items[rowIndex].data.iid;
//            	var state =  grid.getStore().data.items[rowIndex].data.state;
//            	skipSExec(iid,state);
//			},
//			getClass : function(v, metadata, record) {
//				var state=record.data.state;
//				if (state == '3' || state == '11') {
//					return 'x-hidden';
//				}
//				if (showFlag=='his') {
//					return 'x-hidden';
//				}
//			}
//		}
		]
    }];
    function reTrySExec(requestId, state) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
        function(btn) {
            if (btn == 'yes') {
                Ext.MessageBox.wait("数据处理中...", "提示");
                Ext.Ajax.request({
                    url: 'retryDataChangeIns.do',
                    method: 'POST',
                    params: {
                    	requestId: requestId,
                        state: 3
                    },
                    success: function(response, request) {
                        var message = Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', message);
                        insStore.reload();
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }
        })
    }
    function skipSExec(requestId, state) {
        Ext.Msg.confirm('系统提示', '您确定要进行此操作吗?',
        function(btn) {
            if (btn == 'yes') {
                Ext.MessageBox.wait("数据处理中...", "提示");
                Ext.Ajax.request({
                    url: 'skipDataChangeIns.do',
                    method: 'POST',
                    params: {
                    	requestId: requestId,
                        state: 11
                    },
                    success: function(response, request) {
                        var message = Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', message);
                        insStore.reload();
                    },
                    failure: function(result, request) {
                        secureFilterRs(result, "操作失败！");
                    }
                });
            }
        });
    }
    function getCHKBoxIds() {
        var ids = "";
        var records = gridForIns.getView().getSelectionModel().getSelection();
        var cnum = 0;
        Ext.Array.each(records,
        function(rec) {
            cnum = 1;
            var state = rec.get('state'); // -1 60 20
            if (state != '-1' && state != '60' && state != '20' && state != '5') {
                if (ids == '') {
                    ids = rec.get('iid');
                } else {
                    ids = ids + "," + rec.get('iid');
                }
            }
        });
        if (cnum == 1 && ids == '') {
            ids = '-1';
        }
        return ids;
    }
    var rowExpander = Ext.create('Ext.grid.plugin.RowExpander', {
    	expandOnDblClick : false,
    	expandOnEnter : false,
        rowBodyTpl :[
                     '<div id="step{iid}">',
                     '<textarea id="steptextarea{iid}" class="monitor_desc"></textarea>',
                     '<input type="button" value="刷新" onclick="loadShelloutput({iid})" class="Common_Btn Monitor_Btn">',
                     '</div>'
                     ]
    })
    var gridForIns = Ext.create('Ext.ux.ideal.grid.Panel', {
    	region: 'center',
    	store: insStore,
    	autoScroll: true,
        border: false,
        columnLines: true,
        columns: columns,
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        ipageItems : [
        {
            xtype: 'button',
            cls : 'Common_Btn',
            hidden:true,
            text: '终止',
            listeners: {
                click: function() {
                    var data = getCHKBoxIds();
                    if (data.length == 0) {
                        Ext.Msg.alert('提示', '请先选择您要操作的记录!');
                        return;
                    } else {
                        Ext.Msg.confirm("请确认", "是否真的要进行<终止>操作？",
                        function(button, text) {
                            if (button == "yes") {
                                if (data == '-1') {
                                    Ext.Msg.alert('提示', '操作执行成功!');
                                    insStore.reload();
                                    return;
                                }
                                Ext.MessageBox.wait("数据处理中...", "提示");
                                Ext.Ajax.request({
                                    url: 'scriptServiceShellKill.do',
                                    params: {
                                        insIds: data
                                    },
                                    method: 'POST',
                                    success: function(response, opts) {
                                        var success = Ext.decode(response.responseText).success;
                                        Ext.Msg.alert('提示', Ext.decode(response.responseText).message);
                                        // 当后台数据同步成功时
                                        if (success) {
                                            insStore.reload();
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }
        },
        {
            xtype: 'button',
            cls : 'Common_Btn',
            text: '返回',
            listeners: {
                click: function() {
                	var url="dataCmonitor.do";
                	if(showFlag=='his'){
                		 url="dataChis.do";
                	}else if(showFlag=='permhis'){
                		url="dataCPermhis.do";
                	}else if('start'==showFlag){
                		url="dataChangeCollect.do";
                	}
                	if(auditingWinDc){
                		auditingWinDc.close();
                	}
            		contentPanel.getLoader().load({
            			url: url,
            			scripts: true,
            			params: {
            			}
            		});
                }
            }
        }],
        selModel: selModel,
	    plugins: [rowExpander]
    });
    gridForIns.view.on('expandBody', function (rowNode, record, expandRow, eOpts) {
    	if(record.get('state')=='3'){
    		refreshObjShellOutput = setInterval(function(){
    			loadShelloutput(record.get('iid'));
    		},1000);
    	}else{
    		loadShelloutput(record.get('iid'));
    	}
    });  
    
    gridForIns.view.on('collapsebody', function (rowNode, record, expandRow, eOpts) {
    	if(refreshObjShellOutput){
    		clearInterval(refreshObjShellOutput);
    	}
//    	agent_store.reload({callback:mainLoadAfter});
    });
    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "datachangeins_div",
        cls:'customize_panel_back',
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        border: false,
        bodyPadding: 5,
        items: [gridForIns]
    });

    contentPanel.on('resize', function() {
    	mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
		mainPanel.setWidth (contentPanel.getWidth () );
    });

});
function loadShelloutput(instanceId){
	Ext.Ajax.request({
		url : 'dataChangeInsClob.do',
		params : {
			taskId: flowId,
			requestId : instanceId
		},
		success : function(response, opts) {
			var msg = Ext.decode(response.responseText);
			if(Ext.isIE){
				if(msg.success){
					document.getElementById('steptextarea'+instanceId).innerText=msg.message;
				}else{
					document.getElementById('steptextarea'+instanceId).innerText=msg.message;
				}
			}else{
				if(msg.success){
					document.getElementById('steptextarea'+instanceId).innerHTML=msg.message;
				}else{
					document.getElementById('steptextarea'+instanceId).innerHTML=msg.message;
				}
			}
		},
		failure : function(response, opts) {
			if(Ext.isIE){
				document.getElementById('steptextarea'+instanceId).innerText='获取执行信息失败';
			}else{
				document.getElementById('steptextarea'+instanceId).innerHTML='获取执行信息失败';
			}
		}

	});
}