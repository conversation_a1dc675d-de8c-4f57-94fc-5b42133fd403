var preparedStepStore= null;//步骤信息数据
var ipStore=null;//服务器信息数据
var ipGrid=null;//服务器信息
var pkgName=null;//变更文件
var instName = null;//变更说明
var versionInfo = null;//变更单号
var sysNameTextFiled = null;//系统名称
var arrEvnids;//选择环境集合
var instance_env_info_gridNew=null;//选择环境
var tabPanelForICFS;//tab
var ipMap = new Ext.util.HashMap();//服务器信息
var autoFlag=false;//自动选择服务器数据标识
var swichFlag=false;
var isWarning = 0;  //是否告警值 0:不告警,-1:告警
var skepallut=-1;//是否忽略所有人工步骤 0忽略 -1不忽略
var skeputEnv=[];//忽略人工步骤的环境id
var skeputEnvStr="ut";//传递到后台的环境参数
var instanceInsInfoStore=null;//系统信息列表stor
var instance_Ins_info_grid = null;//系统信息
var hostTab=null;//服务器
var showAlarmBox = null;//是否抑制告警
var ialarmrulename = '';//是否抑制告警
var ialarmsuppression = '否';//是否抑制告警
Ext.onReady (function () {
	// 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.define('ipModel', {
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'ienvid',
			type : 'string'
		}, {
			name : 'ename',
			type : 'string'
		}, {
			name : 'ihostname',
			type : 'string'
		}, {
			name : 'hostiid',
			type : 'string'
		}, {
			name : 'iip',
			type : 'string'
		}, {
			name : 'iport',
			type : 'string'
		}, {
			name : 'igroupid',
			type : 'string'
		}, {
			name : 'iresname',
			type : 'string'
		}, {
			name : 'iappype',
			type : 'string'
		}, {
			name : 'actiid',
			type : 'string'
		}, {
			name : 'iinstanceid',
			type : 'string'
		}, {
			name : 'iinstancename',
			type : 'string'
		}, {
			name : 'iserner',
			type : 'string'
		}, {
			name : 'istepname',
			type : 'string'
		}, {
			name : 'imodelname',
			type : 'string'
		}, {
			name : 'istepname',
			type : 'string'
		}, {
			name : 'imodelInstName',
			type : 'string'
		}, {
			name : 'modelShellid',
			type : 'string'
		}, {
			name : 'modelShellName',
			type : 'string'
		}, {
			name : 'checked',
			type : 'boolean'
		}, {
			name : 'iconnername',
			type : 'string'
		} ]
	});

	/** 基础信息列表model* */
	Ext.define ('instanceBasicInfoModel', {
	    extend : 'Ext.data.Model',
	    fields : [
			{
				name : 'iid',
				type : 'long'
			},
			{
				name : 'iinstanceId',
				type : 'long'
			},
			{
				name : 'iinstanceName',
				type : 'string'
			},
			{
				name : 'iparameter',
				type : 'string'
			},
			{
				name : 'iparaValue',
				type : 'string'
			},
			{
				name : 'gdcattimes',
				type : 'string'
			},
			{
				name : 'ides',
				type : 'string'
			}
	    ]
	});
	/** 系统信息列表model* */
	Ext.define ('instanceInfoModel', {
	    extend : 'Ext.data.Model',
	    fields : [
			{
				name : 'iid',
				type : 'long'
			},
			{
				name : 'iinstanceId',
				type : 'long'
			},
			{
				name : 'iinstanceName',
				type : 'string'
			},
			{
				name : 'iserner',
				type : 'long'
			},
			{
				name : 'iconner',
				type : 'long'
			},
			{
				name : 'iconnerName',
				type : 'string'
			},
			{
				name : 'iprener',
				type : 'string'
			},
			{
				name : 'iactName',
				type : 'string'
			},
			{
				name : 'iactDes',
				type : 'string'
			},
			{
				name : 'iactType',
				type : 'string'
			},
			{
				name : 'iremInfo',
				type : 'string'
			},
			{
				name : 'ipName',
				type : 'string'
			},
			{
				name : 'imodelType',
				type : 'string'
			},
			{
				name : 'iip',
				type : 'string'
			},
			{
				name : 'iprot',
				type : 'int'
			},
			{
				name : 'isysType',
				type : 'string'
			},
			{
				name : 'iexecUser',
				type : 'string'
			},
			{
				name : 'ishellScript',
				type : 'string'
			},
			{
				name : 'iisloadenv',
				type : 'string'
			},
			{
				name : 'ishellPath',
				type : 'string'
			},
			{
				name : 'itimeOut',
				type : 'long'
			},
			{
				name : 'iparameter',
				type : 'string'
			},
			{
				name : 'iexpeceInfo',
				type : 'string'
			},
			{
				name : 'iexceptinfo',
				type : 'string'
			},
			{
				name : 'iredoable',
				type : 'string'
			},
			{
				name : 'iisDisable',
				type : 'boolean'
			},
			{
				name : 'iprenerString',
				type : 'string'
			},
			{
				name : 'ipresysname',
				type : 'string'
			},
			{
				name : 'ipreactname',
				type : 'string'
			}, {
				name : 'isystemtypestep',
				type : 'string'
			}, {
				name : 'imodeltypestep',
				type : 'string'
			},
			{
				name : 'singleRollback',
				type : 'string'
			},
			{
				name : 'ihightrisk',
				type : 'string'
			},
			{
				name : 'ipair',
				type : 'string'
			},{
				name : 'iconcurrency',
				type : 'string'
			},{
				name : 'iintervaltime',
				type : 'string'
			},{
				name : 'isRollBack',
				type : 'string'
			}, {
			  name : 'checked',
			  type : 'boolean'
			}
	    ]
	});

	/** 环境信息列表model    已停用* */
	Ext.define ('instanceEnvInfoModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
			{
				name : 'iid',
				type : 'long'
			},
			{
				name : 'iinstanceId',
				type : 'long'
			},
			{
				name : 'iinstanceName',
				type : 'string'
			},
			{
				name : 'iip',
				type : 'string'
			},
			{
				name : 'iipName',
				type : 'string'
			},
			{
				name : 'iport',
				type : 'int'
			},
			{
				name : 'imoduleType',
				type : 'string'
			},
			{
				name : 'ides',
				type : 'string'
			}
	    ]
	});

	/** 环境信息列表model  新增json型数据 * */
	Ext.define ('instanceEnvInfoModelNew', {
	    extend : 'Ext.data.Model',
	    fields : [
			{
				name : 'iid',
				type : 'long'
			},
			{
				name : 'iname',
				type : 'string'
			}
	    ]
	});

	/** 系统信息列表column* */
	var instacneInfoColumns = [
		{
			text : 'ID',
			dataIndex : 'iid',
			flex : 1,
			hidden : true
		},
		{
			text : '步骤标识',
			dataIndex : 'iserner',
			width : 70
		},
		{
			text : '阶段名称',
			dataIndex : 'iconnerName',
			width : 70
		},
		{
			text : '顺序步骤',
			dataIndex : 'iconner',
			width : 70
		},
		{
			text : '子步骤名称',
			dataIndex : 'iactName',
			width : 350
		},
		{
			text : '目标组件',
			dataIndex : 'ipName',
			width : 85,
			renderer: function(value,metaData,record){
				return '<a href="javascript:void(0);" style="text-decoration:underline;" onclick="openResourceGroupWindowNew('+record.get('iid')+','+record.get('iinstanceId')+')"> 详细 </a>';
			}
		},
		{
			text : '模块类型',
			dataIndex : 'imodeltypestep',
			width : 75
		},{
			text : '应用标识',
			dataIndex : 'isystemtypestep',
			width : 75
		},
		{
			text : '提醒内容',
			dataIndex : 'iremInfo',
			hidden : true,
			width : 150
		},
		{
			text : '依赖',
			dataIndex : 'iprenerString',
			width : 100,
			renderer : function (value, metaData, record, colIndex, store, view)
			{
				metaData.tdAttr = 'data-qtip="' + value + '"';
				return value;
			}
		},
		{
			text : '系统类型',
			dataIndex : 'isysType',
			hidden : true,
			width : 100
		},
		{
			text : '任务类型',
			dataIndex : 'iactType',
			width : 100
		},
		{
			text : '执行用户',
			dataIndex : 'iexecUser',
			width : 100
		},
		{
			text : '壳脚本',
			dataIndex : 'ishellScript',
			width : 100
		},
		{
			text : '环境变量',
			dataIndex : 'iisloadenv',
			hidden : true,
			width : 200
		},
		{
			text : '执行脚本',
			dataIndex : 'ishellPath',
			flex : 1
		},
		{
			text : '成功返回值',
			dataIndex : 'iexpeceInfo',
			hidden : true,
			width : 200
		},
		{
			text : '异常返回值',
			dataIndex : 'iexceptinfo',
			hidden : true,
			width : 200
		},
		{
			text : '是否可重做',
			dataIndex : 'iredoable',
			hidden : true,
			width : 200
		},
		{
			text : '依赖系统',
			dataIndex : 'ipresysname',
			width : 200
		},
		{
			text : '依赖步骤名称',
			dataIndex : 'ipreactname',
			width : 200
		},
		{
			text : '单步回退',
			dataIndex : 'singleRollback',
			width : 200
		},
		{
			text : '是否高风险',
			dataIndex : 'ihightrisk',
			hidden : true,
			width : 200
		},
		{
			text : '配对',
			dataIndex : 'ipair',
			hidden : true,
			width : 200
		},{
			text : '并发数',
			dataIndex : 'iconcurrency',
			width : 150
		},{
			text : '间隔时间',
			dataIndex : 'iintervaltime',
			width : 150
		},{
			text : '是否支持回退',
			dataIndex : 'isRollBack',
			width : 150
		}
	];
	/** 环境信息列表column 新增json型数据* */
	var instanceEnvInfoColumnsNew = [
		{
			text : '序号',
			width : 65,
			xtype : 'rownumberer'
		},
		{
			text : '环境ID',
			dataIndex : 'iid',
			flex : 1,
			hidden : true
		},
		{
			text : '环境名',
			dataIndex : 'iname',
			flex : 1
		}
	];

	/** 环境信息列表store 新增json型数据* */
	instanceEnvInfoStoreNew = Ext.create ('Ext.data.Store', {
	    autoLoad : false,
	    autoDestroy : true,
	    model : 'instanceEnvInfoModelNew',
	    proxy :  {
	        type : 'ajax',
	        url : 'queryEnvListByInstanceId.do',
	        reader :  {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	instanceEnvInfoStoreNew.on ('beforeload', function (store, options) {
		var new_params = {
				instanceId : versionId
		};
		Ext.apply (instanceEnvInfoStoreNew.proxy.extraParams, new_params);
	});

	instanceEnvInfoStoreNew.load (
		{callback:function(){
			instance_env_info_gridNew.getSelectionModel().selectAll(true);//选择所有行
		}}
	);
	instanceEnvInfoStoreNew.addListener('load',function(){
		var activeTab=null;
		try{
			activeTab=tabPanelForICFS.getActiveTab();			
		}catch(e){
			alert(e);
		}
		tabPanelForICFS.setActiveTab(instance_env_info_gridNew);
		tabPanelForICFS.setActiveTab(activeTab);
		if(false==swichFlag){
			var doNotFlshFlag=1;
			swtichEnv(doNotFlshFlag);
		}
	});

	/** 基础信息列表store* */
	var instanceBasicInfoStore = Ext.create ('Ext.data.Store', {
	    autoLoad : false,
	    autoDestroy : true,
	    pageSize : 1000,
	    model : 'instanceBasicInfoModel',
	    proxy : {
	        type : 'ajax',
	        url : 'getInstanceBasicInfoList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	instanceBasicInfoStore.on ('beforeload', function (store, options){
		var new_params ={
			iidForQuery : versionId
		};
		Ext.apply (instanceBasicInfoStore.proxy.extraParams, new_params);
	});
	instanceBasicInfoStore.on ('load', function (store, options){
		//为备注信息赋值
		for (var i = 0; i < store.getCount (); i++) {
			var record = store.getAt (i);
			if('变更说明'==record.get ('iparameter')) {
				instName.setValue(record.get ('iparaValue'));
			}
		}
	});
	/** 系统信息列表store* */
	instanceInsInfoStore = Ext.create ('Ext.data.Store',{
	    autoLoad : true,
	    autoDestroy : true,
	    pageSize : 1000,
	    model : 'instanceInfoModel',
	    proxy : {
	        type : 'ajax',
	        url : 'getInstanceInsInfoList.do?systemType=3',
	        reader :{
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	instanceInsInfoStore.on ('beforeload', function (store, options){
		var new_params ={
		    iidForQuery : versionId,
		    iisDisable:"0",
		    isysType : 1
		};
		Ext.apply (instanceInsInfoStore.proxy.extraParams, new_params);
	});

	/** 环境信息列表store* */
	var instanceEnvInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : false,
	    autoDestroy : true,
	    pageSize : 1000,
	    model : 'instanceEnvInfoModel',
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getInstanceEvnInfoList.do?systemType=3',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	instanceEnvInfoStore.on ('beforeload', function (store, options){
		var new_params ={
			iidForQuery : versionId
		};
		Ext.apply (instanceEnvInfoStore.proxy.extraParams, new_params);
	});


	
	//-----------------------------预启动服务器信息-------------------------------------------------
	var sm_OfEnv = Ext.create('Ext.selection.CheckboxModel', {
	});
	var sm_OfIp = Ext.create('Ext.selection.CheckboxModel', {	
	    checkOnly : true
	});
	var stepColum = [ {
		xtype : 'checkcolumn',
		disabled:false,
		header : '',
		dataIndex : 'checked',
		width : 50,
		stopSelection : false,
		listeners : {
			checkchange : function(column, recordIndex, checked) {
				var _recoard =preparedStepStore.getAt(recordIndex);
				var _ienvid=null;
				var _igroupid=null;
				var _hostiid=null;

				var _istepname=null;
				var _imodelInstName=null;
				var _modelShellName=null;
				var sp="&&**";
				var key="";
				_ienvid=_recoard.get('ienvid');
				_igroupid=_recoard.get('igroupid');
				_hostiid=_recoard.get('hostiid');
				_istepname=_recoard.get('istepname');
				_imodelInstName=_recoard.get('imodelInstName');
				_modelShellName=_recoard.get('modelShellName');
				key= (_ienvid+sp) +(_igroupid+sp) +(_hostiid+sp) +(_istepname+sp) +(_imodelInstName+sp) +(_modelShellName+sp);
				var curRecord=ipMap.get(key);
				if(null!=curRecord){
				  curRecord.checked=checked;
				  ipMap.add(key,curRecord);
				}
				_recoard.set('checked', checked);
				_recoard.commit();
				//判断服务器列表是否选中
				checkIpGrid();
			}
		}
	}, {
		text : '阶段名称',
		dataIndex : 'iconnername',
		flex: 2
	},{
		text : '子步骤名称',
		dataIndex : 'istepname',
		flex: 2
	} ];
	
	var ipColum = [{
		text : '序号',
		xtype : 'rownumberer',
		width : 65
	}, {
		text : 'envid',
		dataIndex : 'ienvid',
		hidden : true,
		width : 70
	}, {
		text : '环境',
		dataIndex : 'ename',
		flex: 2
	}, {
		text : '服务器名称',
		dataIndex : 'ihostname',
		flex: 2
	}, {
		text : 'hostiid',
		dataIndex : 'hostiid',
		hidden : true,
		flex: 2
	}, {
		text : 'IP',
		dataIndex : 'iip',
		flex: 2
	}, {
		text : '端口',
		dataIndex : 'iport',
		flex: 2
	}, {
		text : '应用标识',
		dataIndex : 'iappype',
		flex: 2
	}, {
		text : 'resid',
		dataIndex : 'igroupid',
		hidden : true,
		flex: 2
	}, {
		text : '资源组',
		dataIndex : 'iresname',
		flex: 2
	} ];


	preparedStepStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		model : 'ipModel',
		proxy : {
			type : 'ajax',
			url : 'getStartPrepSteps.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});	
	preparedStepStore.on('load',function(store,options){
		for ( var i = 0; i < preparedStepStore.getCount(); i++) {
			var _ienvid=null;
			var _igroupid=null;
			var _hostiid=null;
			var _istepname=null;
			var _imodelInstName=null;
			var _modelShellName=null;
			var sp="&&**";
			var key="";
			var _recoard =null;
			_recoard = preparedStepStore.getAt(i);
			_ienvid=_recoard.get('ienvid');
			_igroupid=_recoard.get('igroupid');
			_hostiid=_recoard.get('hostiid');
			_istepname=_recoard.get('istepname');
			_imodelInstName=_recoard.get('imodelInstName');
			_modelShellName=_recoard.get('modelShellName');
			key= (_ienvid+sp) +(_igroupid+sp) +(_hostiid+sp) +(_istepname+sp) +(_imodelInstName+sp) +(_modelShellName+sp);
			var curRecord=ipMap.get(key);
			var isChecked =curRecord.checked;
			_recoard.set('checked', isChecked);
			_recoard.commit();
		}
	});

	
   ipStore = Ext.create('Ext.data.Store', {
		autoLoad : false,
		model : 'ipModel',
	   	timeout: 100000000,
		proxy : {
			type : 'ajax',
			url : 'getStartPrepIps.do',
			reader : {
				type : 'json',
				root : 'dataList'
			}
		}
	});
	ipStore.on('beforeload', function(store, options) {
		ipStore.getProxy().setExtraParam('startInstid',versionId);
		ipStore.getProxy().setExtraParam('strEnvid', arrEvnids);
		ipStore.getProxy().setExtraParam('queryType', 'excelQuery');
	});
	ipStore.on('load',function(store, records){
		try{
			if(null!=records){
				//获取当前激活的tab
				var at=tabPanelForICFS.getActiveTab();
				//将服务器tab激活
				tabPanelForICFS.setActiveTab(hostTab);
				ipGrid.getSelectionModel().select(records);
				//将之前激活的tab重新激活
				tabPanelForICFS.setActiveTab(at);
			}
		}catch(e){
			alert(e);
		}
	});
	//服务器TabFolder页面，左右侧面板
	ipGrid = Ext.create('Ext.grid.Panel', {
		store : ipStore,
		height : (contentPanel.getHeight()) / 2,
		autoScroll : true,
		border : false,
		columnLines : true,
		forceFit : true,
		columns : ipColum,
		selModel : sm_OfIp,
		padding: grid_space,
		listeners: {
	        itemclick: function(dv, record, item, index, e) {
	  			var curhostiid = record.get('hostiid');
	  			var _envid = record.get('ienvid');
	  			preparedStepStore.getProxy().setExtraParam('startInstid',versionId);
	  			preparedStepStore.getProxy().setExtraParam('hostiid', curhostiid);
	  			preparedStepStore.getProxy().setExtraParam('envid', _envid);
	  			preparedStepStore.getProxy().setExtraParam('optTpye', 'excel');
	  			preparedStepStore.load();
	        },
			beforedeselect: function(selModel, record, index) {
				if (ipMap.length == 0) {
				  Ext.MessageBox.alert ("提示", "服务器信息尚未加载完成，请稍后进行操作");
				  return false;
				}
			}
		},
		dockedItems : [ {
			xtype: 'component',
			html: '<h2>服务器信息</h2>'
		}]
	});

	var prepardStepsGrid = Ext.create('Ext.grid.Panel', {
		store : preparedStepStore,
		height : (contentPanel.getHeight()) / 2,
		autoScroll : true,
		border : false,
		columnLines : true,
		forceFit : true,
		columns : stepColum
	});

	var hostPanel = Ext.create("Ext.panel.Panel", {
		region : 'center',
	    layout : 'border',
	    border : false,
	    bodyPadding : 5,
		items : [ {
			// title : '服务器信息',
			region : 'center',
			xtype : 'panel',
			width : contentPanel.getWidth() * 0.65,
			layout : 'fit',
			border : true,
			items : [ ipGrid ]
		}, {
			title : '步骤信息',
			region : 'east',
			collapsible : true,
			collapsed : true,
			xtype : 'panel',
			layout : 'fit',
			width : contentPanel.getWidth() * 0.25,
			border : true,
			hidden:false,
			items : [ prepardStepsGrid ]
		} ]
	});
	/** 服务器gridpanel* */
	hostTab = Ext.create('Ext.Panel', {
		cls:'customize_panel_back',
		title : '服务器',
	    layout : 'border',
	    border : false,
		items:[hostPanel]
	});
	//------------------------------------------------------------------------------

	/** 系统信息列表gridpanel* */
	instance_Ins_info_grid = Ext.create ('Ext.grid.Panel', {
		title : '系统信息',
		cls:'customize_panel_back',
		padding: grid_space,
	    store : instanceInsInfoStore,
	    border : false,
	    columnLines : true,
	    columns : instacneInfoColumns
	});
	/** 环境信息列表gridpanel 新增json型数据* */
	 instance_env_info_gridNew = Ext.create ('Ext.grid.Panel', {
		title : '选择环境',
		cls:'customize_panel_back',
	    store : instanceEnvInfoStoreNew,
	    border : false,
	    columnLines : true,
	    columns : instanceEnvInfoColumnsNew,
	    selModel : sm_OfEnv,
		padding: grid_space,
	    height : contentPanel.getHeight () - 210,
		listeners: {
			beforedeselect: function(selModel, record, index) {
				if (ipMap.length == 0) {
					Ext.MessageBox.alert ("提示", "服务器信息尚未加载完成，请稍后进行操作");
					return false;
				}
			}
	    },
		dockedItems : [ {
			xtype : 'toolbar',
			items : [ {
				itemId : 'save',
				name:'changeEnvBtn',
				text : '切换环境',
				cls : 'Common_Btn',
				handler : function() {
					swichFlag=true;
					swtichEnv();
					reInitSkepUTEnv();//切换环境后掠过ut环境初始化
				}
			} ]
		} ]
	});

	sysNameTextFiled = Ext.create('Ext.form.TextField', {
		padding : '5 0 10 0',
		labelWidth : 70,
		xtype: 'textfield',
		width: '100%',
		fieldLabel: '系统名称:',
		name: 'form_sysName',
		labelAlign : 'right',
		readOnly: true,
		value : systemName,
	});


	versionInfo = Ext.create('Ext.form.TextField', {
		padding : '5 0 10 0',
		labelWidth : 70,
		xtype: 'textfield',
		width: '100%',
		fieldLabel: '变更单号:',
		name: 'orderNumber',
		labelAlign : 'right',
		readOnly: true,
		value : orderNumber,
	});


	instName = Ext.create('Ext.form.TextField', {
		padding : '5 0 10 0',
		labelWidth : 70,
		xtype: 'textfield',
		width: '100%',
		fieldLabel: '变更说明:',
		name: 'form_instName_input',
		labelAlign : 'right',
		readOnly: true,
		value : changeDesc,
	});

	pkgName = Ext.create('Ext.form.TextField', {
		padding : '5 0 10 0',
		labelWidth : 70,
		xtype: 'textfield',
		width: '100%',
		fieldLabel: '变更文件:',
		name: 'path',
		labelAlign : 'right',
		readOnly: true,
		value : path,
	});



	showAlarmBox = Ext.create ('Ext.form.field.Checkbox', {
		labelAlign : "left",
		labelWidth : 90,
		padding : '5 0 0 10',
	    fieldLabel : '是否抑制告警',
	    checked : false,
	    inputValue : '1',
		checked: true
	});
	
	var mainForm = new Ext.form.FormPanel ({
		cls:'customize_panel_back panel_space_bottom',
		region : 'north',
	    width : contentPanel.getWidth () - 2,
	    frame : true,
	    defaultType : 'textfield',
	    defaults : {
		    anchor : '99%'
	    },
	    items : [sysNameTextFiled , versionInfo, instName,pkgName,showAlarmBox]
	});

	 /**直接发起任务启动按钮**/
	 var startButton = Ext.create("Ext.Button",{
		 cls : 'Common_Btn',
	     text : "启动",
	     handler : doStartIns
	 });

	var goBack = Ext.create("Ext.Button",{
		cls : 'Common_Btn',
		text : "返回",
		handler : function (){
			flowTaskJumpFunction("susOrderManagementNJPage.do",'');
		}
	});

	 /**全局预览按钮**/
	 var globalShowButton = Ext.create("Ext.Button",{
		 cls : 'Common_Btn',
	     text : "全局预览",
	     handler : flowGlobalShowfun
	 });

	/** tabPanel* */
	tabPanelForICFS = Ext.create ('Ext.tab.Panel', {
		cls:'customize_panel_back',
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : 1,
	    border : false,
	    defaults : {
		    autoScroll : true
	    },
	    items : [{
			title : '基础信息',
			border : false,
			cls:'customize_panel_back',
			loader : {
				url : 'getStartFlowForSUS.do',
				contentType : 'html',
				width : 300,
				autoLoad : false,
				loadMask : true,
				scripts : true
			},
			listeners : {
				activate : function (tab) {
					tab.loader.load ( {
						params :
						{
							instanceId : versionId
						}
					});
				}
			}},instance_Ins_info_grid,instance_env_info_gridNew,hostTab
	    ]
	});
	
	var bottomPanel = Ext.create ("Ext.panel.Panel",{
		baseCls:'customize_gray_back',
		region : 'south',
		border : false,
		dockedItems : [{
				baseCls:'customize_gray_back',
				dock : 'bottom',
				xtype : 'toolbar',
				items : [
					globalShowButton,startButton,goBack
				]
		}]
	});
	
	var rightPanel = Ext.create ("Ext.panel.Panel",{
		width : "100%",
		height : "100%",
	    layout : 'border',
	    border : false,
	    cls:'customize_panel_header_arrow',
	    items : [mainForm,tabPanelForICFS,bottomPanel],
	    renderTo : "orderstartflow_dev"
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts){
		Ext.destroy (rightPanel);
		if (Ext.isIE){
			CollectGarbage ();
		}
	});
	/** 窗口尺寸调节* */
	contentPanel.on ('resize', function (){
		rightPanel.setHeight (contentPanel.getHeight () - 37);
		rightPanel.setWidth (contentPanel.getWidth());
	})
	
	function swtichEnv(doNotFlshFlag){
		var data = instance_env_info_gridNew.getSelectionModel().getSelection();
		var envid=null;
		var tempEnvArr=[];
	    for(var i=0;i<data.length;i++){
	    	envid =data[i].get('iid');
	    	tempEnvArr.push(envid);
	    }
		if (tempEnvArr.length == 0 && doNotFlshFlag!=1) {
			Ext.MessageBox.alert("提示信息", "至少选择一个环境再进行切换");
		} else {
			//切换环境后，重新加载Map /重新刷新 IP面板
			if(doNotFlshFlag!=1){
				instanceEnvInfoStoreNew.load ({
					callback:function(){
						var checkAr=[];
						for ( var k = 0; k < instanceEnvInfoStoreNew.getCount(); k++) {
								var record =instanceEnvInfoStoreNew.getAt(k);
								var _iid = record.data.iid;
								for(var z=0;z<tempEnvArr.length;z++){
								if(tempEnvArr[z] == _iid){
									instance_env_info_gridNew.getSelectionModel().select(record,true);
								}
							}
						}

					}
				});
			}
			var repush=2;
			if(doNotFlshFlag==1){
				repush=1;
			}
			ipGridLoad(repush);
		}
	}

	//获取选择的环境id
	function pushEnvid(){
		var data = instance_env_info_gridNew.getSelectionModel().getSelection();
		var envid=null;
		arrEvnids=[];
        for(var i=0;i<data.length;i++){
        	envid =data[i].get('iid');
        	arrEvnids.push(envid);
        }
	}
});

function openResourceGroupWindowNew (istepId,iinstanceId) {
	configwindowFlow = Ext.create ('Ext.window.Window', {
	    title : '详细配置',
	    autoScroll : true,
	    modal : true,
	    closeAction : 'destroy',
	    buttonAlign : 'center',
	    draggable : false,// 禁止拖动
	    resizable : false,// 禁止缩放
	    width : contentPanel.getWidth (),
	    height : contentPanel.getHeight (),
	    loader :
	    {
	        url : "page/sus/flowstart/servers_config.jsp",
	        params :
		    {
	        	istepId:istepId,
	        	vinstanceId:iinstanceId
		    },
	        autoLoad : true,
	        autoDestroy : true,
	        scripts : true
	    }
	}).show ();
}

function flowGlobalShowfun(){
	if(instanceEnvInfoStoreNew.data.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return;
	}
	var env_rows = instance_env_info_gridNew.getSelectionModel().getSelection();
	if(env_rows.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return false;
	}
	var envids = "";
	for(var i=0;i<env_rows.length;i++){
		envids += env_rows[i].get("iid")+",";
	}
	if(envids.length>0){
		envids=envids.substring(0,envids.length-1);
	}
	var ipIds = "";
	var ip_rows = ipGrid.getSelectionModel().getSelection();
	for(var j=0;j<ip_rows.length;j++){
		var ipstr =ip_rows[j].get("ienvid")+"||"+ip_rows[j].get("hostiid")+"||"+ip_rows[j].get("igroupid");
		if(ipIds.indexOf(ipstr)<0){
		    ipIds += ipstr+",";
		}
	}
	var ipList = "";
	for(var j=0;j<ip_rows.length;j++){
		if(j==0){
			ipList = ipList + ip_rows[j].get("hostiid");
		}else{
			ipList = ipList + "," + ip_rows[j].get("hostiid");
		}
	}
	if(ipIds.length>0){
		ipIds=ipIds.substring(0,ipIds.length-1);
	}
	configwindowFlow = Ext.create ('Ext.window.Window',{
		title : '全局预览',
		autoScroll : true,
		modal : true,
		closeAction : 'destroy',
		buttonAlign : 'center',
		draggable : false,// 禁止拖动
		resizable : false,// 禁止缩放
		width : contentPanel.getWidth (),
		height : contentPanel.getHeight (),
		loader :
		{
			url : "flowGlobalShow.do",
			params :
			{
				instanceId:versionId,
				iidForQuery:versionId,
				envids:envids,
				ipIds:ipIds,
				sysType:3,
				ipList:ipList
			},
			autoLoad : true,
			autoDestroy : true,
			scripts : true
		}
	}).show ();
}

function ipGridLoad(flag){
	if(2==flag){
		pushEnvid();		
	}else{
		arrEvnids = [];
		for ( var k = 0; k < instanceEnvInfoStoreNew.getCount(); k++) {
			var record =instanceEnvInfoStoreNew.getAt(k);
			var _iid = record.data.iid;
			arrEvnids.push(_iid);
		}
	}
	fillMap();
	ipStore.load();
	preparedStepStore.getProxy().setExtraParam('startInstid',0);
	preparedStepStore.getProxy().setExtraParam('hostiid', 0);
	preparedStepStore.getProxy().setExtraParam('envid', 0);
	preparedStepStore.getProxy().setExtraParam('optTpye', 'excel');
	preparedStepStore.load();
}


function pushEnvid(){
	var data = instance_env_info_gridNew.getSelectionModel().getSelection();
	var envid=null;
	arrEvnids=[];
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
    	arrEvnids.push(envid);
    }
}
/**
 * @desc step2:环境加载后，初始化步骤的使用的ipMap
 */
function fillMap(){
	console.log('loadip envid:'+arrEvnids.length +'');
	Ext.MessageBox.wait('正在进行数据加载', "进度条");
    Ext.Ajax.request({
        url : 'getAllStartPrepIps.do',
        method : 'POST',
		timeout: 1800000,
        params : {
        	startInstid : versionId,
        	strEnvid:arrEvnids,
        	optType:'excel'
        },
        success : function(response, request) {
			Ext.MessageBox.close();;
			var success = Ext.decode(response.responseText).success;
			if (success) {
			  var _dataList=Ext.decode(response.responseText).dataList;
			  var ipRecd=null;
			  var _ienvid=null;
			  var _igroupid=null;
			  var _hostiid=null;
			  var _istepname=null;
			  var _imodelInstName=null;
			  var _modelShellName=null;
			  var sp="&&**";
			  var key="";
			  ipMap.clear();//重新装载之前将map清空
			  if(null!=_dataList){
				  for(var i=0;i<_dataList.length;i++){
					  ipRecd =_dataList[i];
					  _ienvid=ipRecd.ienvid;
					  _igroupid=ipRecd.igroupid;
					  _hostiid=ipRecd.hostiid;
					  _istepname=ipRecd.istepname;
					  _imodelInstName=ipRecd.imodelInstName;
					  _modelShellName=ipRecd.modelShellName;
					  key= (_ienvid+sp) +(_igroupid+sp) +(_hostiid+sp) +(_istepname+sp) +(_imodelInstName+sp) +(_modelShellName+sp);
					  ipMap.add(key,ipRecd);
				  }
			  }
			  initFlag=false;
			  console.log('加载环境后，加载该环境下的各个步骤的服务器信息');
			  console.log(ipMap.length);
			} else {
				Ext.Msg.alert('提示', '加载预启动服务器失败');
			}
        },
		failure : function(result, request) {
			Ext.MessageBox.close();
			Ext.Msg.alert('提示', '加载预启动服务器，通信失败.'+result);
		}
    });
}

/**判断服务器列表书否选中**/			
function checkIpGrid() {
	for(i=0;i<ipStore.getCount();i++) {
		var checkedFlag=false;
		var _ienvid = ipStore.getAt(i).get ('ienvid');
		var _igroupid = ipStore.getAt(i).get ('igroupid');
		var _hostiid = ipStore.getAt(i).get ('hostiid') ;
		var sp="&&**";
		//遍历所有服务器的所有步骤
		ipMap.each(function(key, value, length) {
			var keyitem=key.split(sp);
			if(keyitem[0]==_ienvid&&keyitem[1]==_igroupid&&keyitem[2]==_hostiid) {
				var curRecord=ipMap.get(key);
				var isChecked =curRecord.checked;
				if(isChecked) {
					//如果有一个步骤选中了，那么所属服务器就选中
					checkedFlag=true;
				}
			}
		});
		//是否自动选择标识，如果自动选中，则不触发级联时间
		autoFlag=true;
		//获取当前激活的tab
		var at=tabPanelForICFS.getActiveTab();
		//将服务器tab激活
		tabPanelForICFS.setActiveTab(hostTab);
		if(checkedFlag){
			ipGrid.getSelectionModel().select(ipStore.getAt(i), checkedFlag);
		}else{
			ipGrid.getSelectionModel().deselect(ipStore.getAt(i));
		}
		//将之前激活的tab重新激活
		tabPanelForICFS.setActiveTab(at);
		//一秒钟后将自动选择标识重置
		window.setTimeout(function(){autoFlag=false;},1000);
	}
}
/**判断步骤列表书否选中**/			
function checkStepGrid( record,checkedFlagIn) {
	//非自动选择的情况，触发级联事件
	if(autoFlag==false) {
		var sp="&&**";
		ipMap.each(function(key, value, length) {
			var keyitem=key.split(sp);
			var _ienvid = record.data.ienvid;
			var _igroupid = record.data.igroupid;
			var _hostiid =record.data.hostiid;
			if(keyitem[0]==_ienvid&&keyitem[1]==_igroupid&&keyitem[2]==_hostiid) {
				if(null!=value){
					value.checked=checkedFlagIn;
					ipMap.add(key,value);
				}
			}
		});
	}
}


function doStartIns(btn) {
	var ip_flag =checkIpList();
	if(!ip_flag){
		return;
	}
	Ext.Msg.confirm ("确认启动", "是否确认启动此任务?", function (button){
		if (button == 'yes'){
			if(showAlarmBox.getValue()){
				alarmSuppression();
			}else{
				finalStart();
			}
		}
	});
}

//单人发起最终向后台发送数据启动方法
function finalStart(){
	Ext.MessageBox.wait("数据处理中...", "进度条");
	//禁用步骤
	let ignoreStepIdArr = [];
    instanceInsInfoStore.each(function(record) {
      if(record.get('checked') && record.get('iid')){
        ignoreStepIdArr.push(record.get('iid'));
      }
    });
	pushEnvid();
	
	if (ipMap.length == 0) {
		  Ext.MessageBox.alert ("提示", "服务器信息尚未加载完成，请稍后进行操作");
		  return false;
	}
	var selectedRecords = ipGrid.getSelectionModel().getSelection();
	var store = ipGrid.getStore();
	store.each(function(record) {
		if (!selectedRecords.includes(record)) {
			// 未选中的记录
			checkStepGrid(record,false);
		}
	});
	//遍历map，将页面选择的服务器信息后
	var ipArr =new Array();
	ipMap.each(function(key, value, length)
	{
		ipArr.push(value);
	});
	//略过ut环境
    if(skeputEnv.length==0){
    	skeputEnvStr="ut";
    }else{
    	skeputEnvStr="";
    	for(var num=0;num<skeputEnv.length;num++){
    		if(arrEvnids.indexOf(skeputEnv[num])!=-1){
    			console.log(arrEvnids+'-----'+skeputEnv[num]);
    			skeputEnvStr=skeputEnvStr+skeputEnv[num]+",";
    		}
    	}
    }
	Ext.Ajax.request ({
	    url : 'submitInsgdbForSUSCeb.do',
	    timeout: 1800000,
	    method : 'POST',
		dataType: 'json',
		jsonData: Ext.JSON.encode(ipArr),
	    params :
	    {
	        sysName : sysNameTextFiled.rawValue,
	        instName_input : instName.rawValue,
	        version : versionInfo.rawValue,
	        isysid : versionId,
	        isystype : 1,
	        evnIds:arrEvnids,
	        istartUserFullName:current_userName,
            warning:isWarning,
	        startUserLoginName:current_userLoginName,
			ignoreStepids:ignoreStepIdArr,
			skepallut:skepallut,
			skeputEnv:skeputEnvStr,
			path : pkgName.getRawValue(),
			ialarmrulename : ialarmrulename,
			ialarmsuppression : ialarmsuppression
	    },
	    success : function (response, options){
			Ext.MessageBox.close();
		    var success = Ext.decode (response.responseText).success;
			var message = Ext.decode (response.responseText).message;
		    if (success){
				skipGraphForSus(message);
		    }else {
				Ext.Msg.alert ('提示',message);
				return;
		    }
	    },
	    failure : function (result, request) {
			Ext.MessageBox.close();
		    Ext.Msg.alert ('请求失败');
	    }
	});
}
function checkIpList(){
	var iprows = ipGrid.getSelectionModel().getSelection();
	if(iprows&&iprows.length==0){
		Ext.MessageBox.alert ("提示", "请选择服务器");
		return false;
	}
	for(var i=0;i<iprows.length;i++){
		var iips = iprows[i].get("iip");
		if(iips==null||iips==""){
			Ext.MessageBox.alert ("提示", "服务器IP不能为空!");
			return false;
		}
	}
	var envrows = instance_env_info_gridNew.getSelectionModel().getSelection();
	if(envrows.length==0){
		Ext.MessageBox.alert ("提示", "请选择环境!");
		return false;
	}
	for(var i=0;i<envrows.length;i++){
		var evnId = envrows[i].get("iid");
		var evnName = envrows[i].get("iname");
		var ipcount = 0;
		for(var j=0;j<iprows.length;j++){
			var ip_envid = iprows[j].get('ienvid');
			if(ip_envid == evnId){
				ipcount++;
			}
		}
		if(ipcount==0){
			Ext.MessageBox.alert ("提示", "环境"+evnName+"下未选择服务器!");
			return false;
		}
	}
	return true;
}

function skipGraphForSus(message){
	Ext.Msg.alert ('提示', message, function (){
		destroyRubbish();
		contentPanel.setTitle ('变更监控');
		contentPanel.getLoader().load({
			url: "initGraphForSUS.do",
			params : {
				contentPanelHeight:contentPanel.getHeight(),
				windowScHeight:window.screen.height
			}
		});
		if (Ext.isIE) {
			CollectGarbage();
		}
	});
}

function reInitSkepUTEnv(){
	skeputEnv=[];
	var data = instance_env_info_gridNew.getSelectionModel().getSelection();
	var envid=null;
    for(var i=0;i<data.length;i++){
    	envid =data[i].get('iid');
    	skeputEnv.push(envid);
    }
	
}

function alarmSuppression(){
	Ext.MessageBox.wait("数据处理中...", "进度条");
	Ext.Ajax.request ({
		url : 'alarmSuppression.do',
		method : 'POST',
		params :
		{
			projectName : sysNameTextFiled.rawValue
		},
		success : function (response, options)
		{
			var nameValue = Ext.decode (response.responseText).ialarmrulename;
			var suppressionValue = Ext.decode (response.responseText).ialarmsuppression;
			if('是' == suppressionValue){
				ialarmrulename = nameValue;
				ialarmsuppression = suppressionValue;
				finalStart();
			}else{
				Ext.MessageBox.buttonText.yes = "确定";
				Ext.MessageBox.buttonText.no = "取消";
				Ext.Msg.confirm ("抑制告警", "抑制告警失败，是否继续部署流程", function (id){
					if (id == 'yes'){
						finalStart();
					}
				});
			}
		},
		failure : function (result, request)  {
			Ext.MessageBox.buttonText.yes = "确定";
			Ext.MessageBox.buttonText.no = "取消";
			Ext.Msg.confirm ("抑制告警", "抑制告警失败，是否继续部署流程", function (id){
				if (id == 'yes'){
					finalStart();
				}
			});
		}
	});
}