//启动ExtJs4动态加载特性  
Ext.Loader.setConfig({  
	enabled:true,  
	disableCaching:false,  
	paths:{  
		'Go':'js/ux/gooo'  
	}  
});   
Ext.onReady(function() {
	
	var datetimefield= Ext.create('Go.form.field.DateTime',{  
        fieldLabel:'预计开始时间',
        editable:false,
        name:'expStartTime',
        format:'Y-m-d H:i:s'  
    });
	var datetimefield2= Ext.create('Go.form.field.DateTime',{  
        fieldLabel:'预计结束时间',
        editable:false,
        name:'expEndTime',
        format:'Y-m-d H:i:s'  
    });
	var form = Ext.create('Ext.form.Panel', {
		border: false,
		//height:380,
		layout : 'form',
		buttonAlign:'center',
		defaultType: 'textfield',
	    items: [{
            fieldLabel: '系统名称',
            value:projectName,
            readOnly:true
       //     disabled:true,
       //     labelStyle: 'filter:alpha(opacity=100);',
       //     fieldStyle: 'filter:alpha(opacity=100);border:0;color:#000;background: transparent;background-image:none;'
        },{
            fieldLabel: '流程名称',
            value:flowName,
            readOnly:true
        },{
            fieldLabel: '任务名称',
            value:actName,
            readOnly:true
        },{
            fieldLabel: '数据日期',
            value:queryDate,
            readOnly:true
        },{
        	fieldLabel: '配置日期',
            name:'cfgTime',
            disabled:true,
            labelStyle: 'filter:alpha(opacity=100);',
            fieldStyle: 'filter:alpha(opacity=100);border:0;color:#000;background: transparent;background-image:none;'
        },datetimefield,datetimefield2,{
            fieldLabel: '历史平均耗时（分钟）',
            name: 'havgTime'
        }],
        buttons: [{
        	//margin :'120 60 0 0',
	        text: '保存',
	        handler :function(){
	        	saveActTime();
	        }
	    },{
	    	//margin :'120 60 0 0',
	        text: '失效',
	        handler :function(){
	        	Ext.Msg.confirm("消息提醒","是否删除配置信息！",callback);
	        	function callback(flag){
	        		if(flag=="yes"){
	        			delData()
	        		}else{
	        			return false;
	        		}
	        	}
	        }
	    }]
	});
	function loadForm(){
		form.getForm().load({
		    url: 'queryAvgTimeCk.do',
		    params: {
		    	projectName:projectName,
	    		actName: actName,
	    		queryDate:queryDate
		    },
		    failure: function(form, action) {
		        Ext.Msg.alert("信息提示", action.result.errorMessage);
		    }
		});
	}
	loadForm();
	var mainPanel = Ext.create('Ext.panel.Panel', {
		border : false,
		items: [form],
	    renderTo : "toactTimePage_area"
	});
	function delData(){
		Ext.Ajax.request({
			url    :'delActTimeCheck.do',
			method :'post',
			params :{
				systemName:projectName,
				actName:actName,
				queryDate:queryDate
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.alert('提示', message + '操作成功');
					loadForm();
				} else {
					Ext.Msg.alert('提示', message + '操作失败');
				}
			},
			failure : function(result, request) {
				Ext.Msg.alert('提示', message + '操作失败');
			}
		});
	}
	function saveActTime(){
		var havgtime=null;
		var expstartTime=null;
		var expendTime=null;
		havgtime=form.getForm().findField("havgTime").getValue();
		var re=/^[0-9]+[0-9]*]*$/;
		expstartTime=datetimefield.value;
		expendTime=datetimefield2.value;
		if((expendTime==null||expendTime=='')&&(expstartTime==null||expstartTime=='') && (havgtime==null||havgtime=='')){
			Ext.Msg.alert('信息提示', '你未填写任何信息，请填写后再保存！');
			return false;
		}
		
		if(expstartTime!=null&&expstartTime!=''&&expendTime!=null&&expendTime!=''){
			if(expstartTime>expendTime){
				Ext.Msg.alert('信息提示', '预计开始时间不能大于预计结束时间！');
				return false;
			}
		}
		
		if(expstartTime!=null&&expstartTime!=''){
			expstartTime=expstartTime.format("yyyy-MM-dd hh:mm:ss");
		}
		if(expendTime!=null&&expendTime!=''){
			expendTime=expendTime.format("yyyy-MM-dd hh:mm:ss");
		}
		if(havgtime!=null&&havgtime!=''){
			if(isNaN(havgtime)){
				Ext.Msg.alert('信息提示', '历史平均耗时不是数字类型，请重新填写！');
				return false;
			}
			if(!re.test(havgtime)){
				Ext.Msg.alert('信息提示', '历史平均耗时不是整数，请重新填写！');
				return false;
			}
			if(havgtime.length>10){
				Ext.Msg.alert('信息提示', '历史平均耗时不能超过10位数字！');
				return false;
			}
		}
		Ext.Ajax.request({
			url : 'saveActTime.do',
			method : 'POST',
			params : {
				expStartTime : expstartTime,
				expEndTime : expendTime,
				hAvgTime : havgtime,
				systemName : projectName,
				flowName : flowName,
				actName : actName,
				dataTime:queryDate
			},
			success : function(response, request) {
				var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				if (success) {
					Ext.Msg.alert('提示', message + '操作成功');
					loadForm();
				} else {
					Ext.Msg.alert('提示', message + '操作失败');
				}
			},
			failure : function(result, request) {
				Ext.Msg.alert('提示', message + '操作失败');
			}
		});
		
	}
	
});