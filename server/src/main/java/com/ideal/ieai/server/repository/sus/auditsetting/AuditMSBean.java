package com.ideal.ieai.server.repository.sus.auditsetting;

public class AuditMSBean {

    private long menuId = -1;

    private String menuName;

    private String menuOneName;

    private String buttonDes;

    private long menuType = -1;

    private long menuOrder = -1;

    private String menuUrl;

    private String value;

    private long groupId = -1;

    private String flagType;

    private String systemName;

    private String sql;

    private long iid = -1;

    private String img;

    private String iauditmodel;

    private String auditenble;

    //private boolean auditenble = false;

    private long ibuttionid = -1;
    private String ibuttonName;
    private String ibuttonUrl;
    private String iauditArgument;

    public String getAuditenble() {
        return auditenble;
    }

    public void setAuditenble(String auditenble) {
        this.auditenble = auditenble;
    }

    public String getSystemName() {
        return systemName;
    }

    public String getButtonDes() {
        return buttonDes;
    }

    public void setButtonDes(String buttonDes) {
        this.buttonDes = buttonDes;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public long getIbuttionid() {
        return ibuttionid;
    }

    public void setIbuttionid(long ibuttionid) {
        this.ibuttionid = ibuttionid;
    }

    public String getIbuttonName() {
        return ibuttonName;
    }

    public void setIbuttonName(String ibuttonName) {
        this.ibuttonName = ibuttonName;
    }

    public String getIbuttonUrl() {
        return ibuttonUrl;
    }

    public void setIbuttonUrl(String ibuttonUrl) {
        this.ibuttonUrl = ibuttonUrl;
    }

    public String getIauditArgument() {
        return iauditArgument;
    }

    public void setIauditArgument(String iauditArgument) {
        this.iauditArgument = iauditArgument;
    }

    public String getIauditmodel() {
        return iauditmodel;
    }

    public void setIauditmodel(String iauditmodel) {
        this.iauditmodel = iauditmodel;
    }

    public long getMenuId() {
        return menuId;
    }

    public void setMenuId(long menuId) {
        this.menuId = menuId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuOneName() {
        return menuOneName;
    }

    public void setMenuOneName(String menuOneName) {
        this.menuOneName = menuOneName;
    }

    public long getMenuType() {
        return menuType;
    }

    public void setMenuType(long menuType) {
        this.menuType = menuType;
    }

    public long getMenuOrder() {
        return menuOrder;
    }

    public void setMenuOrder(long menuOrder) {
        this.menuOrder = menuOrder;
    }

    public String getMenuUrl() {
        return menuUrl;
    }

    public void setMenuUrl(String menuUrl) {
        this.menuUrl = menuUrl;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public long getGroupId() {
        return groupId;
    }

    public void setGroupId(long groupId) {
        this.groupId = groupId;
    }

    public String getFlagType() {
        return flagType;
    }

    public void setFlagType(String flagType) {
        this.flagType = flagType;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public long getIid() {
        return iid;
    }

    public void setIid(long iid) {
        this.iid = iid;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }
}
