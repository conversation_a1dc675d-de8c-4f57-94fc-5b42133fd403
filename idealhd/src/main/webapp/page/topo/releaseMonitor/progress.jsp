<%@page contentType="text/html; charset=utf-8"%>
<html>
<head>
<link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<div style="position:relative;top:50px">
<table cellpadding="0" cellspacing="0" border="0" height="180" width="100%">
	<tr>
    	<td height="100%">
        	<div class="progress">
			<%
			    //定义 进度条的高度 ，不能为 0%，0%等价于100%，所以当为0%给一个极小值0.01%
			    Object obj = request.getAttribute("persent");
			    String persentHeight = null != obj ? (String) obj : "0.1%";
			    if ("0%".equals(persentHeight) || null == persentHeight)
			    {
			        persentHeight = "0.1%";
			    }
			    //获取显示标签
			    Object numeratorName = request.getAttribute("numeratorName");
			    Object denominatorName = request.getAttribute("denominatorName");
			    String name1 = (null==numeratorName?"运行完成":numeratorName.toString());
			    String name2 = (null==numeratorName?"总步数":denominatorName.toString());
			%>
			<div class="Progress_Bar"
				style="height:<%=persentHeight%>"></div>
			<span><%=request.getAttribute("persent")%> </span>
			</div>
        </td>
       <td>
        	<div class="Progress_Detail">
				<div class="Progress_D_column">
					<span class="Progress_circle1"></span><%=name1 %>
				</div>
				<div class="Progress_D_column">
					<span class="Progress_circle2"></span><%=name2 %>
				</div>
			</div>
        </td>
    </tr>
</table>
</div>
</body>
</html>
