package com.ideal.ieai.server.repository.topo.batchrunviewer;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;

/**
 * 农信拓扑图首页报表数据获取
 * <AUTHOR>
 * @date 20171101
 */
public class BatchRunViewerNxManager
{

    private static BatchRunViewerNxManager intance = new BatchRunViewerNxManager();
    private static final Logger            _log    = Logger.getLogger("topoLog");

    public static BatchRunViewerNxManager getInstance ()
    {
        if (intance == null)
        {
            intance = new BatchRunViewerNxManager();
        }
        return intance;
    }

    /**
     * 
     * @return
     * @throws DBException 
     * @throws RepositoryException 
     * @throws Exception
     */
    public boolean isTest ()
    {
        return ServerEnv.getInstance().getBooleanConfig("testshow", false);
    }

    public Map<String, Object> getMainPathViewerInfoNx () throws DBException, RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        ResultSet rset1 = null;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        String dataDate = null;
        int daynum = 0;

        String startprjname = null;
        String startflowname = null;
        String endflowname = null;
        Long avgtime = null;
        Long starttime = null;
        for (int i = 0;; i++)
        {
            try
            {

                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

                String sqlGetMainFlow = "select t.startprjname,t.startflowname,t.endprjname,t.endflowname,t.avgtime "
                        + "from IEAI_CALCULATION_RECORD t where t.isshow=1";

                String sqlGetStartTime = "select t.istarttime from IEAI_TOPOINFO_BUSS_G_INS t "
                        + "where t.systemname=? and t.ipgroupnum=? and t.idatadate=?";

                daynum = getDataDateDiff();
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, daynum);
                dataDate = format.format(calendar.getTime());

                ps = conn.prepareStatement(sqlGetMainFlow);
                rset = ps.executeQuery();
                if (rset.next())
                {
                    startprjname = rset.getString("STARTPRJNAME");
                    startflowname = rset.getString("STARTFLOWNAME");
                    endflowname = rset.getString("endflowname");
                    avgtime = rset.getLong("avgtime");

                    map.put("mainPathStartNode", startflowname);
                    map.put("mainPathEndNode", endflowname);

                } else
                {
                    map.put("mainPathStartNode", "尚未设置展示关键路径");
                    map.put("mainPathEndNode", "尚未设置展示关键路径");
                    map.put("mainPathStartTime", "尚未设置展示关键路径");
                    map.put("mainPathEndTime", "尚未设置展示关键路径");
                }

                if (startprjname != null && startflowname != null)
                {
                    ps = conn.prepareStatement(sqlGetStartTime);
                    ps.setString(1, startprjname);
                    ps.setString(2, startflowname);
                    ps.setString(3, dataDate);
                    rset1 = ps.executeQuery();
                    if (rset1.next())
                    {
                        starttime = rset1.getLong("istarttime");
                        if (starttime != null && starttime > 0)
                        {
                            map.put("mainPathStartTime", format1.format(new Date(starttime)));
                            map.put("mainPathEndTime", format1.format(new Date(starttime + avgtime)));
                        } else
                        {
                            map.put("mainPathStartTime", dataDate + "关键路径尚未开始执行");
                            map.put("mainPathEndTime", dataDate + "关键路径尚未开始执行");
                        }
                    } else
                    {
                        map.put("mainPathStartTime", "尚未设置展示关键路径");
                        map.put("mainPathEndTime", "尚未设置展示关键路径");
                    }
                }

                getActNum(dataDate, map, null);
                break;
            } catch (SQLException e)
            {
                _log.error("getMainPathViewerInfo_nx() is error" + e.getMessage());
                DBRetryUtil.waitForNextTry(i, new RepositoryException(ServerError.ERR_DB_QUERY));
            } catch (DBException e)
            {
                _log.error("getMainPathViewerInfo_nx() is error" + e.getMessage());
                throw e;
            } finally
            {
                DBResource.closeConn(conn, rset, ps, "getMainPathViewerInfo_nx", _log);
                DBResource.closeResultSet(rset1, "getMainPathViewerInfo_nx", _log);
            }

        }

        return map;
    }

    public Map<String, Object> getMainPathViewerInfoNxTest ()
    {
        Map<String, Object> map = new HashMap<String, Object>();

        map.put("mainPathStartNode", "数据平台");
        map.put("mainPathEndNode", "核心系统");
        map.put("mainPathStartTime", "2017/06/01 20:00:00");
        map.put("mainPathEndTime", "2017/06/02 06:20:00");
        map.put("finishTaskCount", new Random().nextInt(500) + 1);
        map.put("unFinishTaskCount", new Random().nextInt(1000) + 1);
        map.put("exceptionTaskCount", new Random().nextInt(100) + 1);
        return map;

    }

    /**
     * 批量执行总览——近7日关键路径批量作业数 查询
     * @throws DBException 
     * @throws RepositoryException 
     */
    public Map<String, Object> getMainPathBatchTaskCountViewerNx () throws DBException, RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ViewData> list = new ArrayList<ViewData>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;

        String dataDateStr = null;
        List<String> dataDateList = new ArrayList<String>();
        int daynum = 0;
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {

                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

                String sql = "select COUNT(1) as actnum from IEAI_CALCULATION_RECORD showrcd,IEAI_CRITICAL_PATH_RECORD pathrcd,"
                        + "IEAI_TOPO_INI_INFO actinfo where showrcd.isshow=1 and pathrcd.calculationid=showrcd.iid and "
                        + "actinfo.isystemname=pathrcd.prjname and actinfo.igropname=pathrcd.flowname and actinfo.idatadate in(?) "
                        + "group by actinfo.idatadate order by actinfo.idatadate";

                daynum = getDataDateDiff();
                dataDateStr = "";
                getDatelist(7, daynum, dataDateStr, dataDateList, format);

                if (!("").equals(dataDateStr))
                {
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, dataDateStr);
                    rset = ps.executeQuery();

                    while (rset.next())
                    {
                        ViewData data = new ViewData();
                        data.setTime(dataDateList.get(count));
                        data.setData(rset.getLong("ACTNUM"));
                        list.add(data);
                        count++;
                    }
                    for (; count < 7; count++)
                    {
                        ViewData data = new ViewData();
                        data.setTime(dataDateList.get(count));
                        data.setData(0);
                        list.add(data);
                    }
                } else
                {
                    for (int j = 0; j < 7; j++)
                    {
                        ViewData data = new ViewData();
                        data.setTime(dataDateList.get(j));
                        data.setData(0);
                        list.add(data);
                    }
                }
                map.put("batchTaskCountList", list);
                map.put("totalProperty", 0);
                return map;
            } catch (SQLException e)
            {
                _log.error("getMainPathBatchTaskCountViewer_nx() is error" + e.getMessage());
                DBRetryUtil.waitForNextTry(i, new RepositoryException(ServerError.ERR_DB_QUERY));
            } catch (DBException e)
            {
                _log.error("getMainPathBatchTaskCountViewer_nx() is error" + e.getMessage());
                throw e;
            } finally
            {
                DBResource.closeConn(conn, rset, ps, "getMainPathBatchTaskCountViewer_nx", _log);
            }
        }
    }

    public Map<String, Object> getMainPathBatchTaskCountViewerNxTest ()
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ViewData> list = new ArrayList<ViewData>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        for (int i = 7; i > 0; i--)
        {
            Calendar calendar1 = Calendar.getInstance();
            calendar1.add(Calendar.DATE, -i);
            ViewData data = new ViewData();
            data.setTime(format.format(calendar1.getTime()));
            data.setData(new Random().nextInt(100));
            list.add(data);
        }
        map.put("batchTaskCountList", list);
        map.put("totalProperty", 0);
        return map;
    }

    /**
     * 获取各类作业数饼图数据方法
     * @return
     * @throws RepositoryException 
     * @throws Exception
     */

    public Map<String, Object> getBatchSuccessRateViewerNx () throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ViewData> list = new ArrayList<ViewData>();

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        int daynum = getDataDateDiff();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, daynum);
        String dataDate = format.format(calendar.getTime());

        getActNum(dataDate, null, list);

        map.put("batchSuccessRateList", list);
        map.put("totalProperty", 0);
        return map;

    }

    public Map<String, Object> getBatchSuccessRateViewerNxTest ()
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ViewData> list = new ArrayList<ViewData>();

        ViewData data1 = new ViewData();
        data1.setName("已完成作业数");
        data1.setData(new Random().nextInt(100));
        list.add(data1);
        ViewData data2 = new ViewData();
        data2.setName("未完成作业数");
        data2.setData(new Random().nextInt(100));
        list.add(data2);
        ViewData data3 = new ViewData();
        data3.setName("异常作业数");
        data3.setData(new Random().nextInt(100));
        list.add(data3);

        map.put("batchSuccessRateList", list);
        map.put("totalProperty", 0);
        return map;
    }

    /**
     * 批量执行总览——近7日关键路径耗时趋势 查询
     * @throws RepositoryException 
     */
    public Map<String, Object> getMainPathTimeTrendViewerNx () throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List list = new ArrayList();
        int count = 0;

        Map dataMap = new HashMap();
        List fieldList = new ArrayList();
        mainPathTimeTrendViewer(dataMap, fieldList);
        Iterator it = dataMap.keySet().iterator();
        while (it.hasNext())
        {
            String key = (String) it.next();
            if (list.isEmpty())
            {
                list = (List) dataMap.get(key);
            } else
            {
                list.addAll((List) dataMap.get(key));
            }
        }
        map.put("fields", fieldList);
        count = 10;

        map.put("mainPathTimeTrendList", list);
        map.put("totalProperty", count);

        return map;
    }

    public Map<String, Object> getMainPathTimeTrendViewerNxTest ()
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List list = new ArrayList();
        int count = 0;
        Map dataMap = new HashMap();
        List fieldList = new ArrayList();
        testMainPathTimeTrendViewer(dataMap, fieldList);
        Iterator it = dataMap.keySet().iterator();
        while (it.hasNext())
        {
            String key = (String) it.next();
            if (list.isEmpty())
            {
                list = (List) dataMap.get(key);
            } else
            {
                list.addAll((List) dataMap.get(key));
            }
        }
        map.put("fields", fieldList);
        count = 10;

        map.put("mainPathTimeTrendList", list);
        map.put("totalProperty", count);
        return map;
    }

    private void testMainPathTimeTrendViewer ( Map dataMap, List fieldList )
    {
        // 0
        String[] a = null;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String testData = "";
        for (int i = 7; i > 0; i--)
        {
            if (i == 7)
            {
                Calendar calendar1 = Calendar.getInstance();
                calendar1.add(Calendar.DATE, -i);
                testData += format.format(calendar1.getTime()) + ",耗时趋势," + new Random().nextInt(100);
            } else
            {
                Calendar calendar1 = Calendar.getInstance();
                calendar1.add(Calendar.DATE, -i);
                testData += "||" + format.format(calendar1.getTime()) + ",耗时趋势," + new Random().nextInt(100);
            }
        }
        a = testData.split("\\|\\|");

        for (int i = 0; i < a.length; i++)
        {
            String[] b = a[i].split(",");

            Map map1 = null;
            List list1 = null;
            if (dataMap.containsKey(b[0]))
            {
                list1 = (List) dataMap.get(b[0]);
                map1 = (Map) list1.get(0);
            } else
            {
                map1 = new HashMap();
                list1 = new ArrayList();
            }
            if (!map1.containsKey("time"))
            {
                map1.put("time", b[0]);
            }
            map1.put(b[1], Double.valueOf(b[2]));
            list1.clear();
            list1.add(map1);
            dataMap.put(b[0], list1);
            if (!fieldList.contains(b[1]))
            {
                fieldList.add(b[1]);
            }

        }
    }

    private void mainPathTimeTrendViewer ( Map dataMap, List fieldList ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        ResultSet rset1 = null;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        // String dataDate = null
        int daynum = 0;

        String startprjname = null;
        String startflowname = null;
        String endprjname = null;
        String endflowname = null;

        String[] a = null;
        String dataDateStr = null;
        List<String> dataDateList = new ArrayList<String>();
        String testData = "";
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {

                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

                String sql = "select t.startprjname,t.startflowname,t.endprjname,t.endflowname "
                        + "from IEAI_CALCULATION_RECORD t where t.isshow=1";

                String sql1 = "select tend.endtime-tstart.starttime as costtime from "
                        + "(select tend.idatadate,t1.iendtime as endtime from IEAI_TOPOINFO_BUSS_G_INS t1  where t1.systemname=? and t1.ipgroupnum=? and t1.idatadate in (?)) tend ,"
                        + "(select tstart.idatadate,t2.istarttime as starttime from IEAI_TOPOINFO_BUSS_G_INS t2  where t2.systemname=? and t2.ipgroupnum=? and t2.idatadate in (?)) tstart "
                        + "where tend.idatadate=tstart.idatadate order by tend.idatadate";

                ps = conn.prepareStatement(sql);

                daynum = getDataDateDiff();

                dataDateStr = "";
                getDatelist(7, daynum, dataDateStr, dataDateList, format);

                ps = conn.prepareStatement(sql);
                rset = ps.executeQuery();
                if (rset.next())
                {
                    startprjname = rset.getString("STARTPRJNAME");
                    startflowname = rset.getString("STARTFLOWNAME");
                    endprjname = rset.getString("ENDPROJNAME");
                    endflowname = rset.getString("ENDFLOWNAME");
                }

                if (dataDateStr != null && !("").equals(dataDateStr))
                {
                    ps = conn.prepareStatement(sql1);
                    ps.setString(1, endprjname);
                    ps.setString(2, endflowname);
                    ps.setString(3, dataDateStr);
                    ps.setString(4, startprjname);
                    ps.setString(5, startflowname);
                    ps.setString(6, dataDateStr);

                    rset1 = ps.executeQuery();
                    while (rset1.next())
                    {
                        long min = 0;
                        Long tmptime = rset1.getLong("COSTTIME");
                        if (tmptime != null)
                        {
                            min = tmptime / 1000 / 60;
                        }
                        if (count == 0)
                        {
                            testData += dataDateList.get(count) + ",耗时趋势," + min;
                        } else
                        {
                            testData += "||" + dataDateList.get(count) + ",耗时趋势," + min;
                        }
                        count++;
                    }
                    for (; count < 7; count++)
                    {
                        if (count == 0)
                        {
                            testData += dataDateList.get(count) + ",耗时趋势," + 0;
                        } else
                        {
                            testData += "||" + dataDateList.get(count) + ",耗时趋势," + 0;
                        }
                    }
                } else
                {
                    for (int j = 0; j < 7; j++)
                    {
                        if (j == 0)
                        {
                            testData += dataDateList.get(j) + ",耗时趋势," + 0;
                        } else
                        {
                            testData += "||" + dataDateList.get(j) + ",耗时趋势," + 0;
                        }

                    }
                }

                a = testData.split("\\|\\|");

                for (int j = 0; j < a.length; j++)
                {
                    String[] b = a[i].split(",");

                    Map map1 = null;
                    List list1 = null;
                    if (dataMap.containsKey(b[0]))
                    {
                        list1 = (List) dataMap.get(b[0]);
                        map1 = (Map) list1.get(0);
                    } else
                    {
                        map1 = new HashMap();
                        list1 = new ArrayList();
                    }
                    if (!map1.containsKey("time"))
                    {
                        map1.put("time", b[0]);
                    }
                    map1.put(b[1], Double.valueOf(b[2]));
                    list1.clear();
                    list1.add(map1);
                    dataMap.put(b[0], list1);
                    if (!fieldList.contains(b[1]))
                    {
                        fieldList.add(b[1]);
                    }

                }
                break;
            } catch (Exception e)
            {
                _log.error("MainPathTimeTrendViewer() is error" + e.getMessage());
                DBRetryUtil.waitForNextTry(i, new RepositoryException(ServerError.ERR_DB_QUERY));
            } finally
            {
                DBResource.closeConn(conn, rset, ps, "MainPathTimeTrendViewer", _log);
                DBResource.closeResultSet(rset1, "MainPathTimeTrendViewer", _log);
            }

        }

    }

    /**
     * 批量执行总览——近7日异常作业趋势 查询
     * @throws DBException 
     * @throws RepositoryException 
     */
    public Map<String, Object> getErrorTaskTrendViewerNx () throws DBException, RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ViewData> list = new ArrayList<ViewData>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String dataDateStr = null;
        List<String> dataDateList = new ArrayList<String>();
        int daynum = 0;
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {

                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

                String sql = "select errort.errornum,overt.overnum from "
                        + "(select t3.iinstancename,count(1) as errornum from IEAI_TOPO_BUSS_EXCEPTION t3 where t3.istate=1 and t3.iinstancename in (?)  group by t3.iinstancename) errort ,"
                        + "(select t4.iinstancename,count(1) as overnum from IEAI_TOPO_BUSS_EXCEPTION t4 where t4.istate=3 and t4.iinstancename in (?)  group by t4.iinstancename) overt "
                        + " where errort.iinstancename=overt.iinstancename order by errort.iinstancename";

                daynum = getDataDateDiff();
                dataDateStr = "";
                getDatelist(7, daynum, dataDateStr, dataDateList, format);

                if (!("").equals(dataDateStr))
                {
                    ps = conn.prepareStatement(sql);
                    ps.setString(1, dataDateStr);
                    ps.setString(2, dataDateStr);
                    rset = ps.executeQuery();
                    while (rset.next())
                    {
                        Long tmpErrorNum = null;
                        tmpErrorNum = rset.getLong("ERRORNUM");
                        ViewData data = new ViewData();
                        if (tmpErrorNum != null)
                        {
                            data.setTime(dataDateList.get(count));
                            data.setData(tmpErrorNum);
                        } else
                        {
                            data.setTime(dataDateList.get(count));
                            data.setData(0);
                        }
                        list.add(data);
                        count++;

                    }
                    for (; count < 7; count++)
                    {
                        ViewData data = new ViewData();
                        data.setTime(dataDateList.get(count));
                        data.setData(0);
                        list.add(data);
                    }
                } else
                {
                    for (int j = 0; j < 7; j++)
                    {
                        ViewData data = new ViewData();
                        data.setTime(dataDateList.get(j));
                        data.setData(0);
                        list.add(data);
                    }
                }
                map.put("errorTaskTrendViewerList", list);
                map.put("totalProperty", 0);
                return map;

            } catch (SQLException e)
            {
                _log.error("getErrorTaskTrendViewerNx() is error" + e.getMessage());
                DBRetryUtil.waitForNextTry(i, new RepositoryException(ServerError.ERR_DB_QUERY));
            } catch (DBException e)
            {
                _log.error("getErrorTaskTrendViewerNx() is error" + e.getMessage());
                throw e;
            } finally
            {
                DBResource.closeConn(conn, rset, ps, "getErrorTaskTrendViewerNx", _log);
            }

        }

    }

    public Map<String, Object> getErrorTaskTrendViewerNxTest ()
    {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ViewData> list = new ArrayList<ViewData>();

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 7; i > 0; i--)
        {
            Calendar calendar1 = Calendar.getInstance();
            calendar1.add(Calendar.DATE, -i);
            ViewData data = new ViewData();
            data.setTime(format.format(calendar1.getTime()));
            data.setData(new Random().nextInt(100));
            list.add(data);
        }
        map.put("errorTaskTrendViewerList", list);
        map.put("totalProperty", 0);
        return map;
    }

    /**
     * 
     * @param dateNum
     * @param dayNum
     * @param dataDateStr
     * @param dataDateList
     * @param format
     */
    private void getDatelist ( int dateNum, int dayNum, String dataDateStr, List<String> dataDateList,
            SimpleDateFormat format )
    {
        for (int j = dateNum; j > 0; j--)
        {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -j + dayNum);
            if (j == 7)
            {
                dataDateStr += "'" + format.format(calendar.getTime()) + "'";
            } else
            {
                dataDateStr += ",'" + format.format(calendar.getTime()) + "'";
            }
            dataDateList.add(format.format(calendar.getTime()));
        }

    }

    /**
     * 获取各种活动状态数的函数
     * @param map
     * @param dataDate
     * @param list
     * @return
     * @throws RepositoryException
     */

    private void getActNum ( String dataDate, Map<String, Object> map, List<ViewData> list ) throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;
        ResultSet rset1 = null;

        int totalactnum = 0;
        int errornum = 0;
        int finishnum = 0;
        int overtimenum = 0;
        for (int i = 0;; i++)
        {
            try
            {

                String sqlGetActnum = "select count(1) as actcount from IEAI_TOPO_INI_INFO t where t.idatadate=?";

                String sqlGetSActnum = "select t.istate,count(1) as actnum from IEAI_DATAMAPPING_OUTPUT t "
                        + "where t.iinstancename=? and t.istate in (1,2,3) group by t.istate ";

                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);

                ps = conn.prepareStatement(sqlGetActnum);
                ps.setString(1, dataDate);
                rset = ps.executeQuery();
                if (rset.next())
                {
                    totalactnum = rset.getInt("ACTCOUNT");
                }

                ps = conn.prepareStatement(sqlGetSActnum);
                ps.setString(1, dataDate);
                rset1 = ps.executeQuery();
                while (rset.next())
                {
                    String state = rset.getString("ISTATE");
                    int actnum = rset.getInt("ACTNUM");

                    if (state.equals("2"))
                    {
                        finishnum = actnum;
                    } else if (state.equals("1"))
                    {
                        errornum += actnum;
                    } else if (state.equals("3"))
                    {
                        if (map != null)
                            errornum += actnum;
                        if (list != null)
                            overtimenum = actnum;
                    } else
                    {
                        continue;
                    }

                }
                if (map != null)
                {
                    map.put("finishTaskCount", finishnum);
                    map.put("unFinishTaskCount", totalactnum - finishnum);
                    map.put("exceptionTaskCount", errornum);
                    return;
                }
                if (list != null)
                {
                    ViewData data1 = new ViewData();
                    data1.setName("已完成作业数");
                    data1.setData(finishnum);
                    list.add(data1);

                    ViewData data2 = new ViewData();
                    data2.setName("未完成作业数");
                    data2.setData((long) totalactnum - (long) finishnum);
                    list.add(data2);

                    ViewData data3 = new ViewData();
                    data3.setName("异常作业数");
                    data3.setData(errornum);
                    list.add(data3);

                    ViewData data4 = new ViewData();
                    data4.setName("超时作业数");
                    data4.setData(overtimenum);
                    list.add(data4);
                    return;
                }
                break;
            } catch (Exception e)
            {
                _log.error("getActNum() is error" + e.getMessage());
                DBRetryUtil.waitForNextTry(i, new RepositoryException(ServerError.ERR_DB_QUERY));
            } finally
            {
                DBResource.closeConn(conn, rset, ps, "getActNum", _log);
                DBResource.closeResultSet(rset1, "getActNum", _log);
            }
        }
    }

    /**
     * 计算数据日期和当前日期差
     * @return
     * @throws RepositoryException 
     */
    private int getDataDateDiff () throws RepositoryException
    {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rset = null;

        Date nowtime = null;
        String nowtimestr = null;
        String changeTime = null;
        SimpleDateFormat format1 = new SimpleDateFormat("HHmm");
        for (int i = 0;; i++)
        {
            try
            {

                conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
                String sql = "select t.ipropertyvalue from IEAI_PROPERTYCONFIG t  where t.ipropertyname='changeDateTime'";

                ps = conn.prepareStatement(sql);
                rset = ps.executeQuery();

                nowtime = new Date();

                if (rset.next())
                {
                    changeTime = rset.getString("ipropertyvalue");
                } else
                {
                    changeTime = "2200";
                }
                nowtimestr = format1.format(nowtime);

                if (changeTime == null || changeTime.equals(""))
                {
                    return 0;
                }

                if (Integer.parseInt(nowtimestr) > Integer.parseInt(changeTime))
                {
                    return 0;
                } else
                {
                    return -1;
                }

            } catch (Exception e)
            {
                _log.error("getDataDateDiff() is error" + e.getMessage());
                DBRetryUtil.waitForNextTry(i, new RepositoryException(ServerError.ERR_DB_QUERY));
            } finally
            {
                DBResource.closeConn(conn, rset, ps, "getDataDateDiff", _log);

            }

        }
    }

}
