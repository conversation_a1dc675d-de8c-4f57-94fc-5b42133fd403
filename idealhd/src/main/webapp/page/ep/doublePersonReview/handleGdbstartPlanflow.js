/*******************************************************************************
 * 【应急操作】双人复核修改提交页面
 ******************************************************************************/

Ext.onReady (function ()
{
	// 清理主面板的各种监听时间
	destroyRubbish ();
	Ext.tip.QuickTipManager.init ();
	/** *********************Model********************* */
	/** 基础信息列表model* */
	Ext.define ('instanceBasicInfoModel',
	{
		extend : 'Ext.data.Model',
		fields : [ {
			name : 'iid',
			type : 'int'
		}, {
			name : 'stepname',
			type : 'string'
		},  {
			name : 'iplanname',
			type : 'string'
		}, {
			name : 'iscenename',
			type : 'string'
		}, {
			name : 'projectname',
			type : 'string'
		},{
			name : 'iservicesname',
			type : 'string'
		} ,{
			name:'sid',
			type:'long'
		}
		,{
			name:'icommontaskname',
			type:'string'
		},{
			name:'itasktime',
			type:'string'
		},{
			name:'iistimetask',
			type:'int'
		},{
			name:'icommontaskid',
			type:'long'
		},{
			name:'isysname',
			type:'string'
		}
		,{
			name:'iscripttype',
			type:'string'
		},{
			name:'iscriptname',
			type:'string'
		},{
			name:'iscriptpara',
			type:'string'
		},{
			name:'icontent',
			type:'string'
		},{
			name:'iplatform',
			type:'string'
		},{
			name:'istatus',
			type:'string'
		},{
			name:'ibussid',
			type:'long'
		},{
			name:'ibusstypeid',
			type:'long'
		},{
			name:'iisflow',
			type:'int'
		},{
			name:'ilevel',
			type:'int'
		},{
			name:'iversion',
			type:'string'
		},{
			name:'ibussname',
			type:'string'
		}
		]
	});


	/** 审核人下拉框model* */
	Ext.define ('reviewAuditorModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'loginName',
	                type : 'string'
	            },
	            {
	                name : 'fullName',
	                type : 'string'
	            }
	    ]
	});
	/** *********************Store********************* */
	/** 基础信息列表store* */
	var instanceBasicInfoStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    pageSize : 1000,
	    model : 'instanceBasicInfoModel',
	    proxy :
	    {
	        type : 'ajax',
//	        url : 'getInstanceBasicInfoList_ep.do',
	        url : 'doublePersonReview/getInstancePlanBasicInfoList_ep.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	instanceBasicInfoStore.on ('beforeload', function (store, options)
	{
		var new_params =
		{
			iidForQuery : iinstanceid,
			iworkItemid : iworkItemid,
			iplanId 	: iplanId,
			ifromId		: ifromId,
			iscenceId   : iscenceId,
			istepId	    : istepId,
			isysId	    : isysId,
			itaskName   : itaskName,
			iisscript	: iisscript,
			customId : customId
		};
		Ext.apply (instanceBasicInfoStore.proxy.extraParams, new_params);
	});

	Ext.define('AuditorModel', {
	    extend: 'Ext.data.Model',
	    fields : [ {
	      name : 'loginName',
	      type : 'string'
	    }, {
	      name : 'fullName',
	      type : 'string'
	    }]
	  });
	/** 审核人下拉框Store* */
	/**var reviewAuditorStore = Ext.create ('Ext.data.Store',
	{
		fields : ['id', 'name'],
		data : data
	});*/
	
	var reviewAuditorStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    model: 'AuditorModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getExecAuditorList.do?scriptLevel='+scriptLevel,
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
	
	
	/** *********************组件********************* */
	/** 系统名显示* */
	var isysNameForShow = Ext.create ('Ext.form.DisplayField',
	{
	    margin : '5',
	    fieldLabel : '系统名称',
	    labelWidth : 80,
	    fieldCls : 'x-form-item-label',
	    value : isysName
	
	});
	/** 版本信息显示* */
	var iversionForShow = Ext.create ('Ext.form.DisplayField',
	{
	    margin : '5',
//	    fieldLabel : '变更单号',
	    fieldLabel : '预案名称',
	    labelWidth : 80,
	    fieldCls : 'x-form-item-label',
	    value : iversion
	
	});
	/** 实例显示* */
	var iinsNameForShow = Ext.create ('Ext.form.DisplayField',
	{
	    margin : '5',
//	    fieldLabel : '变更说明',
	    fieldLabel : '应急说明',
	    labelWidth : 80,
	    fieldCls : 'x-form-item-label',
	    value : iinsName
	
	});
	/** 基础信息列表column* */
	var instanceBasicInfoColumns = [
        /**{
        text : '序号',
        width : 40,
        locked : true,
        xtype : 'rownumberer'
    },*/
    {
        text : 'ID',
        dataIndex : 'iid',
        flex : 1,
        hidden : true
    },
    {
        text : '预案名',
        dataIndex : 'iplanname',
        flex : 1,
    }, {
        text : '场景名',
        dataIndex : 'iscenename',
        flex : 1,
    },
    /*{
        text : '系统名',
        dataIndex : 'projectname',
        flex : 1,
        editor :
        {
            allowBlank : false
        }
    },*/
    { header: '步骤名',  dataIndex: 'stepname' ,field: 'textfield',flex : 1},
   	{ header: '任务名',  dataIndex: 'icommontaskname',editor : false,field: 'textfield',flex : 1},
    { header: '任务ID',  dataIndex: 'icommontaskid' ,hidden:false,field: 'textfield',flex : 1},
    { header: '脚本服务名',  dataIndex: 'iservicesname' ,field: 'textfield',flex : 1},
   	{ header: 'sid', dataIndex: 'sid', hidden:true,flex : 1}, 
   	{ header: 'iistimetask', dataIndex: 'iistimetask', hidden:true,flex : 1}, 
   	{ header: 'itasktime', dataIndex: 'itasktime', hidden:true,flex : 1}, 
    { header: '脚本名称',  dataIndex: 'iscriptname' ,field: 'textfield',flex : 1},
	    { header: '适用平台',  dataIndex: 'iplatform' ,field: 'textfield',flex : 1},
	    { header: '一级分类',  dataIndex: 'isysname' ,field: 'textfield',flex : 1},
	    { header: '二级分类',  dataIndex: 'ibussname' ,field: 'textfield',flex : 1},
    { header: 'ibussid',  dataIndex: 'ibussid' ,field: 'textfield',hidden: true,flex : 1},
    { header: 'ilevel',  dataIndex: 'ilevel' ,field: 'textfield',hidden: true,flex : 1},
    { header: 'ibusstypeid',  dataIndex: 'ibusstypeid' ,field: 'textfield',hidden: true,flex : 1},
    { header: '脚本类型',  dataIndex: 'iscripttype' ,field: 'textfield',renderer: function(value, p, record) {
         var backValue = "";
         if (value == "sh") {
             backValue = "shell";
         } else if (value == "perl") {
             backValue = "perl";
         } else if (value == "py") {
             backValue = "python";
         } else if (value == "bat") {
             backValue = "bat";
         } else if (value == "sql") {
             backValue = "sql";
         }
         if (record.get('isFlow') == '1') {
             backValue = "组合";
         }
         return backValue;
	    },flex : 1},
	   {
		text: '版本',
		dataIndex: 'iversion',
		flex : 1
	}
    
];

	/** 审核人下拉框* */
	var reviewAuditorComBox = Ext.create ('Ext.form.ComboBox',
	{
	    editable : false,
	    fieldLabel : "审核人",
	    labelWidth : 50,
	    padding : 5,
	    store : reviewAuditorStore,
	    queryMode : 'local',
	    width : 250,
	    displayField: 'fullName',
	    valueField: 'loginName',
	    listeners:{
	    	 //监听 
	        render : function(combo) {//渲染
	        	/**if(scriptLevelForTaskAudi==0){
	        		combo.setValue(groupLeader);
	        	}else{
	        		combo.getStore().on("load", function(s, r, o) { 
	        	    combo.setValue(r[0].get('loginName'));//第一个值 
	              }); 
	        	}*/
	        },
	        select : function(combo, records, eOpts){ 
				var fullName = records[0].raw.fullName;
				combo.setRawValue(fullName);
			},
			blur:function(combo, records, eOpts){

			},
			beforequery: function(e) {
               var combo = e.combo;
               if (!e.forceAll) {
                   var value = Ext.util.Format.trim(e.query);
                   combo.store.filterBy(function(record, id) {
                       var text = record.get(combo.displayField);
                       return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                   });
                   combo.expand();
                   return false;
               }
           } 

	    
	    }
	});
	/** 打回按钮* */
	var backButtonForDPR = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 70,
//	    height : 22,
	    text : '提交',
	    // style : 'margin-left:65px',// 离左边一个按钮的距离多加65px
	    handler : backFunction
	});
	/** 终止按钮* */
	var stopButtonForDPR = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 70,
//	    height : 22,
	    text : '终止',
	    // style : 'margin-left:65px',// 离左边一个按钮的距离多加65px
	    handler : stopFunction
	});
	/** 返回按钮* */
	var cancelButtonForDPR = Ext.create ("Ext.Button",
	{
	    cls : 'Common_Btn',
//	    textAlign : 'center',
//	    width : 70,
//	    height : 22,
	    text : '返回',
	    handler : cancelFunction
	});
	/** 打回原因输入框* */
	var backInfo = Ext.create ('Ext.form.field.TextArea',
	{
		height : 80
	});
	backInfo.setValue (ibackInfoString);
	backInfo.readOnly = true;
	/** *********************Panel********************* */
	/** 顶部信息panel* */
	var reviewFormPanel = Ext.create ('Ext.form.Panel',
	{
	    width : contentPanel.getWidth () - 2,
	    frame : true,
	    defaultType : 'textfield',
	    defaults :
	    {
		    anchor : '99%'
	    },
	    items : [
	            isysNameForShow, iversionForShow /**, iinsNameForShow*/
	    ]
	});
	/** 基础信息列表gridpanel* */
	var instance_basic_info_grid = Ext.create ('Ext.grid.Panel',
	{
	    store : instanceBasicInfoStore,
	    border : false,
	    columnLines : true,
	    columns : instanceBasicInfoColumns,
	    height : contentPanel.getHeight () - 403
	});
	
	/** tabPanel* */
	var tabPanelForICFS = Ext.create ('Ext.tab.Panel',
	{
	    // title : '业务系统名称：无',
	    tabPosition : 'top',
	    region : 'center',
	    activeTab : 1,
	    // width : '100%',
	    // height : contentPanel.getHeight (),
	    border : true,
	    // plain : true,
	    defaults :
	    {
//		    autoScroll : true
	    },
	    items : [
	            {
	                title : '基础信息',
	                items : instance_basic_info_grid
	            }
	          
	    ]
	});
	/** 打回原因panel* */
	var backInfopanel = Ext.create ('Ext.form.Panel',
	{
	    region : 'south',
	    fieldDefaults :
	    {
	        labelAlign : 'left',
	        labelWidth : 60,
	        anchor : '100%'
	    },
	    title : '打回原因',
	    padding : '5 0 0 0',
	    // bodyPadding : 5,
	    width : '100%',
	    height : 130,
	    collapsible : false,
	    // border : false,
	    items : [
		    backInfo
	    ]
	});
	/** 主panel* */
	var reviewrightPanel = Ext.create ("Ext.panel.Panel",
	{
	    width : '100%',
	    height : contentPanel.getHeight () - 38 -50,
	    layout : 'border',
	    items : [
	            {
	                region : 'north',
	                xtype : 'panel',
	                width : 200,
	                // collapsible:true,
	                // id : 'west-region-container',
	                layout : 'fit',
	                items : [
		                reviewFormPanel
	                ]
	            },
	            {
	                region : 'center', // 必须指定中间区域
	                xtype : 'panel',
	                layout : 'fit',
	                items : [
		                tabPanelForICFS
	                ]
	            }, backInfopanel
	    ],
	    
	    renderTo : "handleGdbstartPlanflowDiv",
	    dockedItems : [
		    {
		        dock : 'bottom',
		        xtype : 'toolbar',
		        items : [
		                reviewAuditorComBox, backButtonForDPR, stopButtonForDPR, cancelButtonForDPR
		        ]
		    }
	    ]
	});
	if (istate == 0)
	{
		backInfopanel.hide ();
		tabPanelForICFS.setHeight ((contentPanel.getHeight ()) / 2);
	}
	else
	{
		tabPanelForICFS.setHeight ((contentPanel.getHeight ()) / 2 - 10);
	}
	// 页面自动加载审核人下拉框store
	reviewAuditorStore.load (
	{
	    callback : function (records, operation, success)
	    {
		    initFunction ();
	    }
	});
	// 当页面即将离开的时候清理掉自身页面生成的组建
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		Ext.destroy (reviewrightPanel);
		
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	/** *********************方法********************* */
	/** 初始化方法* */
	function initFunction ()
	{
		for (var i = 0; i < reviewAuditorStore.getCount (); i++)
		{
			if (iexecUserString == reviewAuditorStore.getAt (i).data.loginName)
			{
				reviewAuditorComBox.setValue (iexecUserString);
//				reviewAuditorComBox.disable(true);//不允许修改审核人
			}
		}
	}
	/** 返回按钮触发方法* */
	function cancelFunction ()
	{
		if (messageWindow != undefined)
		{
			messageWindow.getLoader ().load (
			{
			    url : 'initGetWorkitemRecordsList_ep.do?activeTabNum=1',
			    autoLoad : true,
			    scripts : true
			});
			messageWindow.setTitle ('待办事项');
		}
		closeWinReload ();
	}
	
	function closeWinReload ()
	{
		if (workItemRecordStoreGPS != undefined)
		{
			workItemRecordStoreGPS.reload ();
		}
		if (messageWindow_gps != undefined)
		{
			messageWindow_gps.close ();
		}
		if (undefined != messageWindow_gps)
		{
			messageWindow_gps.close ();
		}
	}
	
	/** 提交按钮触发方法* */
	function backFunction (btn)
	{
		try
		{
			var auditorValue = reviewAuditorComBox.getValue ();
			if (trim (auditorValue) == '' || (null == auditorValue))
			{
				Ext.MessageBox.alert ("提示", "请选择审核人");
				return false;
			}
			disabledButtonFunction (true);
			Ext.MessageBox.wait ("数据处理中...", "进度条");
			
			Ext.Ajax.request({
				 url : 'backWorkitem.do',
				    method : 'POST',
				    params : {
				    	istateForQuery : 1,
				        iidForQuery : iworkItemid,
				        execUser : auditorValue
				    },
				    success: function(response, opts) {
				    	var success = Ext.decode(response.responseText).success;
						var message = Ext.decode(response.responseText).message;
						if(success) {
							Ext.MessageBox.alert("提示", "请求已经发送到审核人");
							cancelFunction();
						}else{
							Ext.MessageBox.alert("提示", message);
						}
						
				    },
				    failure: function(result, request) {
				    	secureFilterRs(result,"操作失败！");
				    	cancelFunction ();
				    }
			});
			
		}
		catch (e)
		{
		}
		finally
		{
			// btn.setDisabled (false);
			disabledButtonFunction (false);
		}
	}
	/** 终止按钮触发方法* */
	function stopFunction (btn)
	{
		Ext.MessageBox.buttonText.yes = "确定";
		Ext.MessageBox.buttonText.no = "取消";
		Ext.Msg.confirm ("确认终止", "是否终止此任务?", function (id)
		{
			if (id == 'yes')
				stopFlowExecute (btn);
		});
	}
	function stopFlowExecute (btn)
	{
		
		try
		{
			disabledButtonFunction (true);
			Ext.MessageBox.wait ("数据处理中...", "进度条");

	    	Ext.Ajax.request (
			{
			    url : 'backWorkitem.do',
			    method : 'POST',
			    params :
			    {
			        istateForQuery : 6,
			        iidForQuery : iworkItemid
			    },
			    success : function (response, opts)
			    {
				    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
				    {
				    	 cancelFunction ();
				    });
				    
			    },
			    failure : function(result, request) {
					secureFilterRs(result,"操作失败！");
				}
			});
		}
		catch (e)
		{
		}
		finally
		{
			// btn.setDisabled (false);
			disabledButtonFunction (false);
		}
	}
	/** 禁用或取消禁用页面的按钮* */
	function disabledButtonFunction (flag)
	{
		backButtonForDPR.setDisabled (flag);
		cancelButtonForDPR.setDisabled (flag);
	}
	
	//begin.added by manxi_zhao.2016-06-22.
	function getIp(value, p, record) {
		//如果任务类型是SHELL,显示‘IP’超链接
		if("SHELL"==record.get("iactType")){
			return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"displayIp('"
			+record.get("iid")+"','"+record.get("iip")+"');\"><span class='abc' style='color:#0ab5fd;'>IP</span></a>";
		}
	} 
	//end.added by manxi_zhao.2016-06-22.
	
});
