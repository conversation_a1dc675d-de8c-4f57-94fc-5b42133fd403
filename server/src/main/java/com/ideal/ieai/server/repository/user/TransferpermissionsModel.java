package com.ideal.ieai.server.repository.user;

import java.sql.Date;

public class TransferpermissionsModel
{
    private long  iid;
    private long   iLoginId;
    private String iloginname;
    private long   iTransferuser_Id;
    private String iTransferUser;
    private long   iApprovalUser_Id;
    private String iApprovalUser;
    private Date iTransferdeadLine;

    private Date iCreatTime;
    private Date iUpdateTime;

    private long   iIs_Auditing;
    private long   iIs_Term ;

    public long getIid() {
        return iid;
    }

    public void setIid(long iid) {
        this.iid = iid;
    }

    public Date getiCreatTime() {
        return iCreatTime;
    }

    public void setiCreatTime(Date iCreatTime) {
        this.iCreatTime = iCreatTime;
    }

    public Date getiUpdateTime() {
        return iUpdateTime;
    }

    public void setiUpdateTime(Date iUpdateTime) {
        this.iUpdateTime = iUpdateTime;
    }

    public long getiLoginId() {
        return iLoginId;
    }

    public void setiLoginId(long iLoginId) {
        this.iLoginId = iLoginId;
    }


    public String getIloginname() {
        return iloginname;
    }

    public void setIloginname(String iloginname) {
        this.iloginname = iloginname;
    }

    public long getiTransferuser_Id() {
        return iTransferuser_Id;
    }

    public void setiTransferuser_Id(long iTransferuser_Id) {
        this.iTransferuser_Id = iTransferuser_Id;
    }

    public String getiTransferUser() {
        return iTransferUser;
    }

    public void setiTransferUser(String iTransferUser) {
        this.iTransferUser = iTransferUser;
    }

    public long getiApprovalUser_Id() {
        return iApprovalUser_Id;
    }

    public void setiApprovalUser_Id(long iApprovalUser_Id) {
        this.iApprovalUser_Id = iApprovalUser_Id;
    }

    public String getiApprovalUser() {
        return iApprovalUser;
    }

    public void setiApprovalUser(String iApprovalUser) {
        this.iApprovalUser = iApprovalUser;
    }

    public Date getiTransferdeadLine() {
        return iTransferdeadLine;
    }

    public void setiTransferdeadLine(Date iTransferdeadLine) {
        this.iTransferdeadLine = iTransferdeadLine;
    }

    public long getiIs_Auditing() {
        return iIs_Auditing;
    }

    public void setiIs_Auditing(long iIs_Auditing) {
        this.iIs_Auditing = iIs_Auditing;
    }

    public long getiIs_Term() {
        return iIs_Term;
    }

    public void setiIs_Term(long iIs_Term) {
        this.iIs_Term = iIs_Term;
    }
}
