/**
 * 
 */
package com.ideal.ieai.server.engine.agent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ideal.dubbo.interfaces.IScriptInstance;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.DesUtils;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.commons.agent.RemoteActivityExecRequest;
import com.ideal.ieai.commons.shellcmd.ShellCmdOutput;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.IEAIRuntime;
import com.ideal.ieai.core.activity.*;
import com.ideal.ieai.core.data.ArrayObject;
import com.ideal.ieai.core.data.IEAIObject;
import com.ideal.ieai.core.data.MapObject;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.ActivityElement;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.core.io.XStreamUtil;
import com.ideal.ieai.core.util.IDataMarshallerHelper;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.common.DNSAnalyze;
import com.ideal.ieai.server.compare.repository.comparebase.CompareBaseManage;
import com.ideal.ieai.server.compare.repository.syncresult.SyncResultManage;
import com.ideal.ieai.server.compare.repository.syncstrategy.SyncSystemManage;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.engine.execactivity.ExecAct;
import com.ideal.ieai.server.engine.util.*;
import com.ideal.ieai.server.idealcmdb.repository.checkModel.CommandRuleModel;
import com.ideal.ieai.server.idealcmdb.repository.cmdbInfo.CmdbInfoManage;
import com.ideal.ieai.server.idealcmdb.repository.cmdbInfo.CmdbInfoModel;
import com.ideal.ieai.server.ieaikernel.ConfigReader;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.jobscheduling.repository.AgentResourceWebConfig.AgentResourceWebConfigBean;
import com.ideal.ieai.server.jobscheduling.repository.AgentResourceWebConfig.AgentResourceWebConfigManager;
import com.ideal.ieai.server.jobscheduling.repository.apt.AptGroupLoadManager;
import com.ideal.ieai.server.jobscheduling.repository.empmodelscan.EmpModelScanManager;
import com.ideal.ieai.server.jobscheduling.thread.ActRuntimeValThread;
import com.ideal.ieai.server.jobscheduling.thread.TopoLogicalThread;
import com.ideal.ieai.server.jobscheduling.util.DateUtil;
import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.proxy.execremote.PerformDataProcessService;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.commons.mail.SendEmailForTTManager;
import com.ideal.ieai.server.repository.data.SqlAdaptorResultManager;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.engine.*;
import com.ideal.ieai.server.repository.hd.AgentGroup.AgentGroupInfo;
import com.ideal.ieai.server.repository.hd.actExcelToRun.ActExcelToRunData;
import com.ideal.ieai.server.repository.hd.agentCheck.AgentCheckRepository;
import com.ideal.ieai.server.repository.hd.agentMaintain.RpcSslType;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.Agent;
import com.ideal.ieai.server.repository.hd.cicd.configitem.CI_CONFIGFILEService;
import com.ideal.ieai.server.repository.hd.releaseMonitor.ReleaseMonitorManager;
import com.ideal.ieai.server.repository.hd.resGrpState.ResGrpStateManager;
import com.ideal.ieai.server.repository.hd.userCertificate.UserCertificateManage;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.installServer.IInstallServerManager;
import com.ideal.ieai.server.repository.installServer.InstallServerManager;
import com.ideal.ieai.server.repository.lob.LobStorer;
import com.ideal.ieai.server.repository.monitor.MonitorSyslog;
import com.ideal.ieai.server.repository.ods.ProjectManagerOds;
import com.ideal.ieai.server.repository.ods.TaskDb;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.resource.HCManager;
import com.ideal.ieai.server.repository.sus.installserver.InstallServerBean;
import com.ideal.ieai.server.repository.sus.instanceConfig.ResultBean;
import com.ideal.ieai.server.repository.sus.pkganalysis.imp.ParseJson;
import com.ideal.ieai.server.repository.workflow.WorkflowManager;
import com.ideal.ieai.server.springContext.IeaiSpringContextUtil;
import com.ideal.ieai.server.timetask.repository.executetask.CustomizationBean;
import com.ideal.ieai.server.timetask.repository.executetask.TaskInstanceDao;
import com.ideal.ieai.server.timetask.repository.executetask.TimeTaskManager;
import com.ideal.ieai.server.timetask.repository.manage.TTManageManager;
import com.ideal.ieai.server.timetask.thread.TimetaskSendAlarmThread;
import com.ideal.ieai.server.toolBoxInfo.alarmInfo.AlarmtoolInfoManager;
import com.ideal.ieai.server.toposerver.thread.DataMappingAnalyThread;
import com.ideal.ieai.server.util.PrintTimeLog;
import com.ideal.ieai.server.util.WarningInterfaceUtilsPlatform;
import com.ideal.ieai.server.util.WarningInterfaceUtilsSus;
import com.ideal.ieai.server.webservice.agent.GetServerAct;
import com.ideal.util.UUID;
import com.ideal.util.*;
import com.ideal.util.log.LogPrintUtil;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.xmlrpc.XmlRpcClient;
import org.apache.xmlrpc.XmlRpcException;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The class is used to send activity invoking request to remote agent.
 * 
 * <AUTHOR>
 */
public class ActivityRExecHelper
{
    public static final int                            BASE_NETWORK_INTERRUPT_TIME = 300000;
    public static final int                            CHECK_RESULT_TIME           = 60000;
    private static final Logger                        _log                        = Logger
            .getLogger(ActivityRExecHelper.class);
    private static final IRexecRequestWraperRepository _memRexecReqWraperRep       = new MemoryRexecRequestWrapperRepository();
    private static final IRexecRequestWraperRepository _dbRexecReqWraperRep        = new DBRexecRequestWrapperRepository();

    private static final String                        OUTPUT_RESULT               = "result";
    private static final String                        NULL_STRING                 = "";
    private static final String                        VERTICAL_LINE               = "|";
    private static final String                        LINE_SEPARATOR              = "line.separator";
    static String                                      serr                        = "stderr";
    static String                                      sout                        = "stdout";
    static String                                      lline                       = "lastLine";
    static String                                      acn                         = " actname=";
    static String                                      ieaiExe                     = "IEAIAgent.executeAct";
    static String                                      realStdOut                  = "realStdOut";
    public static final String                         COMMUNICATION_AGENT_ERROR   = "Communication Agent Error!";
    public static final String                         NORMAL_AGENTIP              = "$normal.agentip";

    public static byte[] getAgentLog ( String agentIP, int agentPort, String fileName )
            throws AgentCommunicationException, XmlRpcException, IOException
    {
        Vector params = new Vector();
        params.addElement(fileName);
        XmlRpcClient rpcClient = null;
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        int ssltype= RpcSslType.getInstance().exec(agentIP, agentPort);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();

            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agentIP, agentPort, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                params.addElement(ha);
                _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIP + ":"
                        + agentPort + " proxySwitch is true ");
            }
            agentIP = pm.getIp();
            agentPort = pm.getPort();
            ssltype=pm.getSsl();
        }
        rpcClient = new AgentXmlRpcClient(agentIP, agentPort, ssltype);
        Object obj = rpcClient.execute("IEAIAgent.getAgentLogByName", params);
        return (byte[]) obj;
    }

    public static Hashtable execAgentLog ( String agentIP, int agentPort )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {
        Vector params = new Vector();
        params.addElement(agentIP);
        params.addElement(agentPort);
        XmlRpcClient rpcClient = null;
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        int ssltype= RpcSslType.getInstance().exec(agentIP, agentPort);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();

            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agentIP, agentPort, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                params.addElement(ha);
                _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIP + ":"
                        + agentPort + " proxySwitch is true ");
            }
            agentIP = pm.getIp();
            agentPort = pm.getPort();
            ssltype=pm.getSsl();
        }
        rpcClient = new AgentXmlRpcClient(agentIP, agentPort, ssltype);
        return (Hashtable) rpcClient.execute("IEAIAgent.getAgentLog", params);
    }

    public static RemoteActivityExecRequest getRExecRequest ( String requestId, int type ) throws ServerException
    {
        return getReqWraper(requestId, type).getRexecRequest();
    }

    private static RExecRequestWrapper getReqWraper ( String requestId, int type ) throws ServerException
    {
        RExecRequestWrapper reqWrapper = _dbRexecReqWraperRep.getRexecReqWrapper(requestId, type);
        if (null == reqWrapper)
        {
            throw new ServerException(ServerError.ERR_REXEC_REQUEST_NOT_FOUND);
        }
        return reqWrapper;
    }

    public static IExecContext getExecContext ( String requestId, int type ) throws ServerException
    {
        RExecRequestWrapper reqWrapper = getReqWraper(requestId, type);
        return reqWrapper.getCtx();
    }

    /**
     * The method is invoked by remote agent, to update request state.
     * 
     * @param reuqestUUID
     * @param status
     * @param output
     * @param exception
     * @throws ServerException
     */
    public static void updateRequestStatus ( String reuqestUUID, int status, Hashtable output, Throwable exception )
            throws ServerException
    {
        ConfigReader cfg = ConfigReader.getInstance();
        boolean islog = cfg.getBooleanProperties("is.test.log", false);
        _log.info("reuqestUUID:" + reuqestUUID + ",status:" + status);
        ActivityElement actElem = null;
        RemoteActivityExecRequest request = null;
        RExecRequestWrapper wrapper = null;
        Hashtable ha = null;
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (reuqestUUID.indexOf("timetask") > -1)
        {
            RequestTimeTaskVO vo = new RequestTimeTaskVO();
            vo.setReqId(reuqestUUID);
            vo.setStatus(status);
            vo.setOutput(output);
            vo.setException(exception);
            AgentRequestServer.getInstance().putAlarmIntoQueue(vo);
        } else if (reuqestUUID.indexOf("ieai-vmVersion-") > -1)
        {
            wrapper = getReqWraper(reuqestUUID, Constants.IEAI_IEAI_BASIC);
            request = wrapper.getRexecRequest();
            request.setOutput(output);
            request.setException(exception);
            request.setLastUpdateTime(Calendar.getInstance());
            String lastLine = "";
            StringBuffer standardoutput = new StringBuffer();
            if (null != output.get("stderr"))
            {
                output.put("stderr", CLSBase64.getFromBASE64((String) output.get("stderr")));
                standardoutput.append((String) output.get("stderr"));
            }
            if (null != output.get("stdout"))
            {
                output.put("stdout", CLSBase64.getFromBASE64((String) output.get("stdout")));
                standardoutput.append((String) output.get("stdout"));
            }
            if (null != output.get("lastLine"))
            {
                output.put("lastLine", CLSBase64.getFromBASE64((String) output.get("lastLine")));
                lastLine = (String) output.get("lastLine");
            }
            saveVmManagerOutPut(reuqestUUID, output);
            _dbRexecReqWraperRep.updateRexecRequest(request, actElem);
        } else if (reuqestUUID.startsWith("maop_")){
            if (null != output.get("stderr"))
            {
                output.put("stderr", CLSBase64.getFromBASE64((String) output.get("stderr")));
            }
            if (null != output.get("stdout"))
            {
                output.put("stdout", CLSBase64.getFromBASE64((String) output.get("stdout")));
            }
            if (null != output.get("lastLine"))
            {
                output.put("lastLine", CLSBase64.getFromBASE64((String) output.get("lastLine")));
            }
            AlarmtoolInfoManager.getInstance().updateToolsResult(reuqestUUID,status,output);
        }else if (reuqestUUID.startsWith("ieai-ems-")){
            try
            {
                wrapper = getReqWraper(reuqestUUID, Constants.IEAI_IEAI_BASIC);
                actElem = wrapper.getActElem(Constants.IEAI_IEAI_BASIC);
                request = wrapper.getRexecRequest();
            } catch (ServerException e)
            {
                _log.info("Can find out remote request!", e);
                return;
            }
            request.setStatus(status);
            String className = actElem.getActDef().getImpClassName();
            String lastLine = "";
            StringBuffer standardoutput = new StringBuffer();
            try
            {
                if (null != output.get(serr))
                {
                    output.put(serr, CLSBase64.getFromBASE64((String) output.get(serr)));
                    standardoutput.append((String) output.get(serr));
                }
                if (null != output.get(sout))
                {
                    output.put(sout, CLSBase64.getFromBASE64((String) output.get(sout)));
                    standardoutput.append((String) output.get(sout));
                }
                if (null != output.get(lline))
                {
                    output.put(lline, CLSBase64.getFromBASE64((String) output.get(lline)));
                    lastLine = (String) output.get(lline);
                }
                // 新增加 保存 活动标准输出
                saveOutPut(request, actElem, reuqestUUID, output);
                String info = " agentinfo: ";
                if (!"".equals((String) output.get(lline)) && null != (String) output.get(lline))
                {
                    _log.info("**************" + request.getProjectName() + ":" + request.getFlowName()
                            + ":" + request.getActName() + info + request.getAgentHost() + ":"
                            + request.getAgentPort());
                    HCManager.getInstance().saveHCResult(output, reuqestUUID, actElem, request);

                } else
                {
                    if (null != (String) output.get(serr) && !"".equals(output.get(serr).toString().trim()))
                    {

                        _log.info("***************" + request.getProjectName() + ":"
                                + request.getFlowName() + ":" + request.getActName() + info
                                + request.getAgentHost() + ":" + request.getAgentPort());
                        output.put(lline, output.get(serr).toString().replace("'", "").replace("?", ""));
                        // 旧版本 健康检查 处理的代码 暂时注释 fengchun_zhang
                        HCManager.getInstance().saveHCResultError(output, reuqestUUID, actElem, request);
                    } else
                    {
                        _log.info("***************" + request.getProjectName() + ":"
                                + request.getFlowName() + ":" + request.getActName() + info
                                + request.getAgentHost() + ":" + request.getAgentPort());
                        output.put(lline, "lastLine return nothing");
                        // 旧版本 健康检查 处理的代码 暂时注释 fengchun_zhang
                        HCManager.getInstance().saveHCResultError(output, reuqestUUID, actElem, request);
                    }
                }
                LobStorer ls = new LobStorer();
                Date endtime = new Date();
                // 读取开关，只有当分区表开关开启时，对actoutput历史表进行存储
                String flagswitch = cfg.getProperties(Environment.CHECK_OKFILE_SWITCH,
                    String.valueOf(Environment.CHECK_OKFILE_SWITCH_DEFAULT));
                if ("true".equals(flagswitch))
                {
                    ls.saveActOutputClob(request.getFlowId(), actElem.getName(), request.getStartTime().getTime(),
                        endtime, standardoutput, lastLine, reuqestUUID, request);
                }
            } catch (Exception e)
            {
                _log.info("stdout is not String!", e);
            }
            request.setOutput(output);
            request.setException(exception);
            request.setLastUpdateTime(Calendar.getInstance());
            _dbRexecReqWraperRep.updateRexecRequest(request, actElem);
            _log.info("Agent update request status: reqid=" + reuqestUUID + acn + actElem.getName() + " status="
                + status + " ex=" + (exception != null ? exception.getMessage() : "no") + " flowid="
                + request.getFlowId());
             EmpModelScanManager.getInstance().updateRunResult(reuqestUUID,status,output);
        } else{
            try
            {
                if (islog)
                {
                    _log.info("reuqestUUID:" + reuqestUUID + ",getReqWraper start");
                }
                wrapper = getReqWraper(reuqestUUID, Constants.IEAI_IEAI_BASIC);
                if (islog)
                {
                    _log.info("reuqestUUID:" + reuqestUUID + ",getReqWraper end and getActElem start");
                }
                actElem = wrapper.getActElem(Constants.IEAI_IEAI_BASIC);
                if (islog)
                {
                    _log.info("reuqestUUID:" + reuqestUUID + ",getActElem end and getRexecRequest is start");
                }
                request = wrapper.getRexecRequest();
                if (null == output)
                {
                    _log.info("reuqestUUID:" + reuqestUUID + ",output is null");
                }
            } catch (ServerException e)
            {
                _log.info("Can find out remote request!", e);
                return;
            }

            if (null == actElem)
            {
                _log.info("the remote act info is null,requestid is " + reuqestUUID);
            } else
            {
                request.setStatus(status);
                if (PersonalityEnv.isRedoStartSwitchValue())
                {
                    if (request.getStatus() == (-5))
                    {
                        try
                        {
                            List<RedoRuleBean> listRedoBean = EngineRepositotyJdbc.getInstance()
                                    .getIsNotRedoRule(request.getFlowId(), request.getFlowName());
                            int count = 0;
                            boolean flagredo = false;
                            if (listRedoBean.size() > 0)
                            {
                                for (int k = 0; k < listRedoBean.size(); k++)
                                {
                                    RedoRuleBean redoRuleBean = listRedoBean.get(k);
                                    if (1 == redoRuleBean.getIisautoredo())
                                    {
                                        count = redoRuleBean.getRedoNumFreq();
                                        flagredo = true;
                                    } else
                                    {
                                        flagredo = false;
                                        break;
                                    }
                                }

                            } else
                            {
                                count = -1;
                            }
                            if (flagredo)
                            {
                                EngineRepositotyJdbc.getInstance().saveRedoActName(request.getFlowId(),
                                    request.getFlowName(), request.getProjectName(), listRedoBean);
                            }
                        } catch (Exception e)
                        {
                            _log.info("getIsNotRedoRule is get error:" + e);
                            e.printStackTrace();
                        }
                    }
                }
                String className = actElem.getActDef().getImpClassName();
                String lastLine = "";
                StringBuffer standardoutput = new StringBuffer();
                try
                {
                    if (className.equals("com.ideal.ieai.scriptserviceadaptor.taskcall.TaskCallAct")
                            || className.equals("com.ideal.ieai.scriptserviceadaptor.scriptcall.TaskCallAct"))
                    {
                        if (null != output.get(sout))
                        {
                            output.put(sout, CLSBase64.getFromBASE64((String) output.get(sout)));
                            standardoutput.append((String) output.get(sout));
                        }
                        if (null != output.get("out_json"))
                        {
                            output.put("out_json", CLSBase64.getFromBASE64((String) output.get("out_json")));
                            standardoutput.append((String) output.get("out_json"));
                        }
                        if (null != output.get(serr))
                        {
                            output.put(serr, CLSBase64.getFromBASE64((String) output.get(serr)));
                            standardoutput.append((String) output.get(serr));
                        }
                    } else if (className.equals("com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct")
                            || className.equalsIgnoreCase("com.ideal.ieai.adaptors.dbadaptor.sqlquery.SQLQueryAct")
                            || className.equalsIgnoreCase("com.ideal.ieai.adaptors.dbadaptor.sqlupdate.SQLUpdateAct")
                            || className.equals("com.ideal.ieai.adaptors.aptshelladaptor.aptshell.APTShellAct")
                            || className.equals("com.ideal.ieai.loaddataadaptor.loaddata.LoadDataAct")
                            || className
                                    .equals("com.ideal.ieai.adaptors.dbproadaptor.procedure.ProCallStoreProcedureAct")
                            || className.equals("com.ideal.ieai.unloaddataadaptor.unloaddata.UnLoadDataAct")
                            || className.equals(
                                "com.ideal.ieai.adaptors.dbproadaptor.procedure.ProCallStoreProcedurenNewAct")
                            || className.equals("com.ideal.ieai.unloaddataadaptor.unloaddata.UnLoadDataNewAct")
                            || className.equals("com.ideal.ieai.loaddataadaptor.loaddata.LoadDataNewAct")
                            || className.equals("com.ideal.ieai.adaptors.fileadaptors.synccontent.SyncContentAct"))
                    {
                        if (null != output.get(serr))
                        {
                            output.put(serr, CLSBase64.getFromBASE64((String) output.get(serr)));
                            standardoutput.append((String) output.get(serr));
                        }
                        if (null != output.get(sout))
                        {
                            output.put(sout, CLSBase64.getFromBASE64((String) output.get(sout)));
                            standardoutput.append((String) output.get(sout));
                        }
                        if (null != output.get(lline))
                        {
                            output.put(lline, CLSBase64.getFromBASE64((String) output.get(lline)));
                            lastLine = (String) output.get(lline);
                        }
                        if (className.equalsIgnoreCase("com.ideal.ieai.adaptors.dbadaptor.sqlquery.SQLQueryAct"))
                        {
                            output.put(sout, map2String4SQLQuery(output, standardoutput));
                        }
                        if(className.equalsIgnoreCase("com.ideal.ieai.adaptors.dbadaptor.sqlupdate.SQLUpdateAct")) {
                            //output.put(sout, (String)output.get(OUTPUT_RESULT)); 
                        }
                        // 新增加 保存 活动标准输出
                        saveOutPut(request, actElem, reuqestUUID, output);
                        String info = " agentinfo: ";
                        /*
                         * <AUTHOR>
                         * 
                         * @Date 2012-10-11
                         * 
                         * @Description 健康检查结果解析
                         */
                        if (!"ExcelActExecModelSUS".equals(request.getProjectName()))
                        {
                            if (!"".equals((String) output.get(lline)) && null != (String) output.get(lline))
                            {
                                _log.info("**************" + request.getProjectName() + ":" + request.getFlowName()
                                        + ":" + request.getActName() + info + request.getAgentHost() + ":"
                                        + request.getAgentPort());
                                HCManager.getInstance().saveHCResult(output, reuqestUUID, actElem, request);

                            } else
                            {
                                if (null != (String) output.get(serr) && !"".equals(output.get(serr).toString().trim()))
                                {

                                    _log.info("***************" + request.getProjectName() + ":"
                                            + request.getFlowName() + ":" + request.getActName() + info
                                            + request.getAgentHost() + ":" + request.getAgentPort());
                                    output.put(lline, output.get(serr).toString().replace("'", "").replace("?", ""));
                                    // 旧版本 健康检查 处理的代码 暂时注释 fengchun_zhang
                                    HCManager.getInstance().saveHCResultError(output, reuqestUUID, actElem, request);
                                } else
                                {
                                    _log.info("***************" + request.getProjectName() + ":"
                                            + request.getFlowName() + ":" + request.getActName() + info
                                            + request.getAgentHost() + ":" + request.getAgentPort());
                                    output.put(lline, "lastLine return nothing");
                                    // 旧版本 健康检查 处理的代码 暂时注释 fengchun_zhang
                                    HCManager.getInstance().saveHCResultError(output, reuqestUUID, actElem, request);
                                }
                            }
                        }
                        if ("CICD_ConfigItemTemplate".equals(request.getProjectName())) {
                            if("ActExec_Write_ItemFile".equals(request.getFlowName())){
                                CI_CONFIGFILEService.getInstance().CI_PushItemEndShell(request,output);
                            }
                        }
                        try
                        {
                            if (ServerEnv.getInstance().getBooleanConfig(Environment.IBRANCH_SUS_SWITCH, false))
                            {
                                // 保存工作流输出用于驱动判断是否执行用
                                saveOutputForBranch(request.getFlowId(), lastLine);
                            }
                        } catch (Exception e)
                        {
                            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                        }
                    } else if (className.equals("com.ideal.ieai.adaptors.fileadaptors.readfile.ReadFileAct"))
                    {
                        String fileCon = "fileContent";
                        if (null != output.get(fileCon))
                        {
                            IEAIObject ob = (IEAIObject) output.get(fileCon);

                            ob.set("textContent", CLSBase64.getFromBASE64((String) ob.get("textContent")));
                            output.put(fileCon, ob);

                            if ("CICD_ConfigItemTemplate".equals(request.getProjectName())) {
                                if("ActExec_Read_ItemFile".equals(request.getFlowName())){
                                    CI_CONFIGFILEService.getInstance().CI_PullItemEnd(request, ob);
                                }else if ("ActExec_Compare_ItemFile".equals(request.getFlowName())) {
                                    CI_CONFIGFILEService.getInstance().compareAndSyncItemEnd(request,ob);
                                }
                            }

                            if ("CompareTemplate".equals(request.getProjectName())
                                    && "ActExec_GetFile_Content".equals(request.getFlowName()))
                            {
                                CompareBaseManage manage = new CompareBaseManage();
                                try
                                {
                                    String str = "";
                                    if (null == (String) ob.get("textContent")
                                            || "".equals((String) ob.get("textContent")))
                                    {
                                        str = "获取失败,可能Agent连接失败，可能文件或脚本路径不正确或文件无内容！";
                                    } else
                                    {
                                        str = (String) ob.get("textContent");
                                    }
                                    manage.updateCompareBaseContext(request.getFlowId(), str, Constants.IEAI_COMPARE);
                                } catch (Exception e)
                                {
                                    _log.error("read config is error!", e);
                                }
                            }

                            String readFileContent = "";
                            if (null != ob)
                            {
                                if (null == ob.get("textContent"))
                                {

                                } else
                                {
                                    readFileContent = (String) ob.get("textContent");
                                    LobStorer ls = new LobStorer();
                                    try
                                    {
                                        if (readFileContent.length() < 1000)
                                        {
                                            ls.saveReadFileActContent(request.getFlowId(), actElem.getName(),
                                                readFileContent);
                                        }
                                    } catch (Exception e)
                                    {
                                        _log.error("ActivityRExecHelper.saveActOutputClob error:" + e + "\n");
                                    }
                                }
                            }
                        }
                    } else if ("com.ideal.ieai.adaptor.sshadaptor.activity.src.SRCAct".equals(className)||"com.ideal.ieai.adaptor.sshadaptor.activity.telnet.TelnetAct".equals(className))
                    {
                        String successFlag = "";
                        String results = "";
                        if (output.containsKey("isSucceed"))
                        {
                            successFlag = String.valueOf((Boolean) output.get("isSucceed"));
                        }
                        if (output.containsKey("result"))
                        {
                            results = (String) output.get("result");
                        }

                        saveOutPut(request, actElem, reuqestUUID, output);

                        if ("true".equals(successFlag))
                        {
                            updateRunStepState(actElem.getFlowId(), actElem.getName(), results, 0);
                        } else
                        {
                            updateRunStepState(actElem.getFlowId(), actElem.getName(), results, 1);
                        }
                    } else if (className.equals("com.ideal.ieai.adaptors.tn5250jadaptors.tn5250j.tn5250Act"))
                    {
                        saveOutPut(request, actElem, reuqestUUID, output);

                    } else if (className.equals("com.ideal.ieai.adaptors.db2adaptors.drda.DrdaAct"))
                    {
                        saveOutPut(request, actElem, reuqestUUID, output);

                    } else if (className.equals("com.ideal.ieai.adptor.snmpadaptor.SnmpAct"))
                    {

                        if (null != output.get("snmpresult") && output.get("snmpresult") instanceof String)
                        {
                            standardoutput.append((String) output.get("snmpresult"));
                        }
                        if (null != output.get("succeed") && (Boolean) output.get("succeed"))
                        {
                            CmdbInfoManage manage = new CmdbInfoManage();
                            manage.parseSNMPSelfFoundInfo(request, standardoutput.toString());
                        }
                    } else if (className.equals("com.ideal.sql.ExecSqlAct"))
                    {
                        if (null != output.get(serr))
                        {
                            output.put(serr, CLSBase64.getFromBASE64((String) output.get(serr)));
                            standardoutput.append((String) output.get(serr));
                        }
                        if (null != output.get(sout))
                        {
                            output.put(sout, CLSBase64.getFromBASE64((String) output.get(sout)));
                            standardoutput.append((String) output.get(sout));
                        }
                        if (null != output.get(lline))
                        {
                            output.put(lline, CLSBase64.getFromBASE64((String) output.get(lline)));
                            lastLine = (String) output.get(lline);
                        }
                        if (className.equalsIgnoreCase("com.ideal.ieai.adaptors.dbadaptor.sqlquery.SQLQueryAct"))
                        {
                            output.put(sout, map2String4SQLQuery(output, standardoutput));
                        }
                        // 新增加 保存 活动标准输出
                        saveOutPut(request, actElem, reuqestUUID, output);
                        if (null != output.get(sout))
                        {
                            SqlAdaptorResultManager.getInstance().saveSqlOperResultDetailToMap(
                                "sqlserver_" + request.getFlowId(), String.valueOf(output.get(sout)), 1,
                                Constants.IEAI_IEAI_BASIC);
                        }
                    }else if (className.equals("com.ideal.ieai.adaptor.http.HttpAct"))
                    {
                        Map midMap = new HashMap();
                        if (null != output.get("outputHeader"))
                        {
                            MapObject miob = (MapObject) output.get("outputHeader");
                            midMap.put("outputHeader", JSONObject.toJSONString(miob.getNormalMap()));
//                            standardoutput.append((String) output.get("outputHeader"));
                        }
                        if (null != output.get("responseString"))
                        {
                            midMap.put("responseString", (String) output.get("responseString"));
//                            standardoutput.append((String) output.get("responseString"));
                        }
                        output.put(sout, JSONObject.toJSONString(midMap));
                        saveOutPut(request, actElem, reuqestUUID, output);
                    } else if (className.equals("com.ideal.ieai.adaptors.fileadaptors.writefile.WriteFileAct"))
                    {
                        if ("CICD_ConfigItemTemplate".equals(request.getProjectName()) && "ActExec_Write_ItemFile".equals(request.getFlowName())) {
                            CI_CONFIGFILEService.getInstance().CI_PushItemEnd(request);
                        }
                    }

                    boolean operswitch = ServerEnv.getInstance().getBooleanConfig(Environment.DATAMAPPING_ANALY_SWITCH,
                        Environment.DATAMAPPING_ANALY_SWITCH_DEFAULT);
                    if (operswitch
                            && "com.ideal.ieai.adaptors.generaladaptors.datatransform.DataHandlerAct".equals(className))
                    {
                        ExecAct execAct = new ExecAct();
                        execAct.setProjectName(request.getProjectName());
                        execAct.setActId(Integer.parseInt(actElem.getActID()));
                        execAct.setActName(actElem.getName());
                        execAct.setFlowId(request.getFlowId());
                        DataMappingAnalyThread daThread = new DataMappingAnalyThread(execAct, output);
                        daThread.start();
                    }
                    /*
                     * 按flowid保存SH作业的标准输出信息_CGB_ODS update start by hao_niu date 2013.12.14
                     * 2017-12-12 11:37 yue_sun 从作业调度中迁移到本版本中
                     */
                    LobStorer ls = new LobStorer();
                    Date endtime = new Date();
                    // 读取开关，只有当分区表开关开启时，对actoutput历史表进行存储
                    String flagswitch = cfg.getProperties(Environment.CHECK_OKFILE_SWITCH,
                        String.valueOf(Environment.CHECK_OKFILE_SWITCH_DEFAULT));
                    if ("true".equals(flagswitch))
                    {
                        ls.saveActOutputClob(request.getFlowId(), actElem.getName(), request.getStartTime().getTime(),
                            endtime, standardoutput, lastLine, reuqestUUID, request);
                    }
                    // update end by hao_niu
                } catch (Exception e)
                {
                    _log.info("stdout is not String!", e);
                }

                List list = null;
                if (className.equals("com.ideal.ieai.adaptors.aptadaptor.apt.APTAct"))
                {
                    String agentInfo = (String) output.get("agentOut");
                    String agentIp = agentInfo.split(":")[0];
                    int agentport = Integer.parseInt(agentInfo.split(":")[1]);

                    AptGroupLoadManager.getInstance().updateAgentReqIdInfo(2, reuqestUUID, agentIp, agentport);
                    request.setOutput(output);
                    request.setException(exception);
                    request.setLastUpdateTime(Calendar.getInstance());
                    list = AptGroupLoadManager.getInstance().groupAgentReqIdList(reuqestUUID, 2);
                    if (list.isEmpty())
                    {
                        _dbRexecReqWraperRep.updateRexecRequest(request, actElem);
                    }
                } else
                {
                    request.setOutput(output);
                    request.setException(exception);
                    request.setLastUpdateTime(Calendar.getInstance());
                    _dbRexecReqWraperRep.updateRexecRequest(request, actElem);
                }
                // AgentServiceImpl.getInstance().proxyModelExecRemove(reuqestUUID, output);
                _log.info("Agent update request status: reqid=" + reuqestUUID + acn + actElem.getName() + " status="
                        + status + " ex=" + (exception != null ? exception.getMessage() : "no") + " flowid="
                        + request.getFlowId());
            }
        }
    }

    /**
     * 
     * @Title: map2String4SQLQuery   
     * @Description: 将sqlquery查询结果集的处理方法提炼出来，简化代码复杂度。   
     * @param output
     * @param standardoutput
     * @return      
     * @author: Sayai 
     * @date:   2018年7月25日 下午4:50:51
     */
    private static String map2String4SQLQuery ( Hashtable output, StringBuffer standardoutput )
    {
        ArrayObject result = (ArrayObject) output.get(OUTPUT_RESULT);
        if (result == null)
            return NULL_STRING;
        for (int i = 0; i < result.length(); i++)
        {
            MapObject map = (MapObject) result.get(i);
            if (i == 0)
            {
                for (Iterator iterator = map.keySet().iterator(); iterator.hasNext();)
                {
                    String key = (String) iterator.next();
                    standardoutput.append(key).append(VERTICAL_LINE);
                }
                standardoutput.append(System.getProperty(LINE_SEPARATOR));
            }
            for (Iterator iterator = map.keySet().iterator(); iterator.hasNext();)
            {
                String key = (String) iterator.next();
                standardoutput.append(map.get(key)).append(VERTICAL_LINE);
            }
            standardoutput.append(System.getProperty(LINE_SEPARATOR));
        }
        return standardoutput.toString();
    }

    /**
     * @update tao_ding
     * @des:减少数据库连接数，采用事务处理
     * @datea:2010-1-28
     * @param flowId
     * @param execCtx
     * @param actElem
     * @param input
     * @return
     * @throws ActivityException
     * @throws ServerException
     */
    public static String createRExecRequest ( long flowId, IExecContext execCtx, ActivityElement actElem, Map input,
            Connection con ) throws ActivityException, ServerException
    {
        String uuid = UUID.uuid();
        // 查询一下接口所有名称与_actElem.getactID 修改requestId ===wg
        ActivityElement a = (ActivityElement) actElem;
        DefaultConfig ba = (DefaultConfig) a.getActConfig();
        String type = (String) ba.get("shelltype");
        if ("rpa".equals(type))
        {
            uuid = "ieai-rpa-" + uuid;
        }
        //判断是否为邮储外管报送系统任务
        if(Constants.IEAI_EMS == (short)input.get("prjType")){
            uuid = "ieai-ems-" + uuid;
        }
        String agentHost = null;
        int agentPort = -1;
        // update by xibin_gong create request时去掉agentip获取，send时获取agentip。
        // if (actElem.isRemoteExec())
        // {
        // if (actElem.getRemoteAgent() == null)
        // {
        // agentHost = actElem.getRemoteHost();
        // agentPort = actElem.getRemotePort();
        // } else
        // {
        // IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
        // .getSharedResInsByName(actElem.getRemoteAgent());
        // if (null == agentResource)
        // {
        // agentExp(actElem);
        // }
        // if (agentResource.isRemoteGroup())
        // {
        //
        // agentHost = "***************";
        // agentPort = 0;
        // } else
        // {
        // agentHost = agentResource.getAgentHost();
        // agentPort = agentResource.getAgentPort();
        // }
        // }
        // } else if (actElem.isRemoteGroupExec())
        // {

        agentHost = "***************";
        agentPort = 0;
        // }
        DbOpScheduler scheduler = new DbOpScheduler();
        WorkflowInstance flowIns;
        try
        {
            flowIns = scheduler.getPrjAndFlowName(flowId, con);
        } catch (RepositoryException e)
        {
            throw new ServerException(ServerError.ERR_DB_INSERT);
        }

        String adpDefUuid = null;
        try
        {
            if (null == actElem.getActDef())
            {
                Map id = new HashMap();
                try
                {
                    id = EngineRepositotyJdbc.getInstance().getprjUuidAndflowDefId(flowId, con);
                } catch (RepositoryException e)
                {
                    throw new ServerException(e.getServerError());
                }
                String prjUuid = (String) id.get("prjUuid");
                int flowDefId = ((Integer) id.get("flowDefId")).intValue();
                ProjectManager.getInstance().reloadProjectByUuid(prjUuid);
                Engine.getInstance().reLoadWorkflow(prjUuid);
                Workflow flow = Engine.getInstance().getWorkfow(flowDefId, prjUuid);
                actElem = (ActivityElement) flow.getActivity(actElem.getID());
            }
            adpDefUuid = ProjectManager.getInstance().getLatestAdaptorInfo(actElem.getActDef().getAdaptorName(), con);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }

        RemoteActivityExecRequest request = new RemoteActivityExecRequest();
        request.setScopeId(execCtx.getScopeId());
        request.setActId(actElem.getActID());
        request.setActName(actElem.getActDef().getName());
        request.setAdaptorConfig(actElem.getActConfig());
        request.setAdaptorDefName(actElem.getActDef().getAdaptorName());
        request.setLevelOfPRI(actElem.getLevelOfPRI());
        request.setLevelOfWeight(actElem.getLevelOfWeight());

        request.setAdaptorDefUUID(adpDefUuid);

        // String remoteip = (String) actElem.getInputExprs().get("$normal.agentip");
        // if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
        // {
        // if (remoteip.startsWith("$"))
        // {
        // Object ret = com.ideal.ieai.server.engine.expreval.ExprEvaluator.eval(remoteip);
        // remoteip = (String) ret;
        // }
        // if (remoteip.startsWith("\""))
        // {
        // remoteip = remoteip.substring(1, remoteip.length() - 1);
        // }
        // String[] ipandport = remoteip.split(":");
        // if (ipandport.length == 2)
        // {
        // agentHost = remoteip.split(":")[0];
        // agentPort = Integer.parseInt(remoteip.split(":")[1]);
        // } else if (ipandport.length == 1)
        // {
        // agentHost = remoteip.split(":")[0];
        // agentPort = 15000;
        // }
        // }

        request.setAgentHost(agentHost);
        request.setAgentPort(agentPort);

        request.setFlowId(flowId);
        request.setFlowName(flowIns.getFlowName());
        request.setId(uuid);

        if (input != null)
        {
            request.setInput(new Hashtable(input));
        }

        request.setProjectName(flowIns.getProjectName());

        int intPorts = Environment.getInstance().getIntConfig(ServerEnv.TOM_PORT, ServerEnv.TOM_PORT_DEFAULT);

        String serverIp = "";
        try
        {
            serverIp = Environment.getInstance().getServerIPList();
        } catch (UnknownHostException ex)
        {
            throw new ActivityException(ex.getMessage());
        }
//        serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP, serverIp);
//        serverIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST, serverIp);
        request.setServerHost(serverIp);
        request.setServerPort(intPorts);

        request.setStartTime(Calendar.getInstance());
        request.setStatus(Constants.REXEC_REQ_STATE_NEW);
        //POC增加试跑
        if(flowIns.getIsCheck()==1){
            request.setStatus(Constants.REXEC_REQ_STATE_TEST);
        }
        request.setTimeout((long) (actElem.getTimeout() * 1000));
        request.setSafeFirst(true);

        if(Environment.getInstance().getDGBankSwitch()){
            request.getInput().put("command", ProjectManager.getInstance().getFlwoParamsCommand(request.getInput().get("command")+"",request.getFlowId()
                    ,request.getProjectName(),request.getFlowName()));
        }





        RExecRequestWrapper reqWrapper = new RExecRequestWrapper(request);

        _dbRexecReqWraperRep.saveRExecRequestWrapper(reqWrapper, con);

        reqWrapper.setCtx(execCtx);
        reqWrapper.setActElem(actElem);

        return uuid;
    }

    public static void stopShellCmdProcess ( String requestId, UserInfo user, int type ) throws ServerException

    {
        Vector params = new Vector();
        params.addElement(requestId);

        RExecRequestWrapper requestWrapper = getReqWraper(requestId, type);
        ActivityElement actElem = requestWrapper.getActElem(type);
        if (null == actElem)
        {
            return;
        }
        XmlRpcClient rpcClient;
        try
        {
            rpcClient = getXmlRpcClient(actElem, requestId, type);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                Hashtable ha = new Hashtable();
                ProxyModel pms = getXmlRpcClientProxyModel(ha, actElem, requestId, type);
                if (pms.isProxy())
                {
                    _log.info("proxy- stopShellCmdProcess:" + rpcClient.getURL() + ",requestId:" + requestId
                            + " proxySwitch is true ");
                    ha.put("proxyJson", pms.getProxyJsonData());
                    ha.put("level", pms.getLevel());
                    ha.put("isProxy", pms.isProxy());
                    params.addElement(ha);
                } else
                {
                    _log.info("agent- stopShellCmdProcess:" + rpcClient.getURL() + ",requestId:" + requestId
                            + " proxySwitch is true ");
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }
        try
        {
            rpcClient.execute("IEAIAgent.stopShellCmdProcess", params);
        } catch (XmlRpcException e)
        {
            _log.info(e);
            throw new ServerException(ServerError.ERR_XMLRPC_FAILED, e);
        } catch (IOException e)
        {
            throw new ServerException(ServerError.ERR_IOEXCEPTION, e);
        }
    }

    public static int updateFlowPoolNum ( String prjFlowName, String flowPoolNum, String remoteHost, int remotePort,
            int sslMode )
    {
        Vector params = new Vector();
        params.addElement(prjFlowName);
        params.addElement(flowPoolNum);

        XmlRpcClient rpcClient;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();
                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                if (pm.isProxy())
                {
                    params.addElement(ha);
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + " proxySwitch is true ");
                }
                remoteHost = pm.getIp();
                remotePort = pm.getPort();
                ssltype=pm.getSsl();
            }
            rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
        } catch (MalformedURLException ex)
        {
            return 0;
        }
        for (int i = 0; i < 2; i++)
        {
            try
            {
                rpcClient.execute("IEAIAgent.updateFlowPoolNum", params);
                // 正确返回
                return 1;
            } catch (Exception e)
            {
                // 错误返回
                return 0;
            }
        }
        // 错误返回
        return 0;
    }

    public static void mockXmlRpc ( ActivityElement actElem, String shellName,ExecAct execAct, int dbType  )
            throws AgentCommunicationException, ServerException, RepositoryException {
        //POC项目修改，目的是为了引擎提速，从而做了server挡板功能
        AgentBean agent = new AgentBean();
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                agent.setRemoteHost(actElem.getRemoteHost());
                agent.setRemotePort(actElem.getRemotePort());
                agent.setSslMode(actElem.getConnectType());
            } else
            {
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByNameType(actElem.getRemoteAgent(), dbType);
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
//                    ret = getAgentInfoByReqid(actElem, reqId, 0);
                } else
                {
                    agent.setRemoteHost(agentResource.getAgentHost());
                    agent.setRemotePort(agentResource.getAgentPort());
                    agent.setSslMode(agentResource.getConnType());
                }
            }
        }

        if (null == agent.getRemoteHost())
        {
            // 获得agentip的对象并转换为string
            Object objreip = com.ideal.ieai.server.engine.expreval.ExprEvaluator
                    .eval((String) actElem.getInputExprs().get(NORMAL_AGENTIP));

            String remoteip = (String) objreip;
            if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
            {
                actElem.setRemoteExec(true);
                if(remoteip.contains("[")&&remoteip.contains("]")&&remoteip.indexOf("[")<remoteip.indexOf("]")&&remoteip.startsWith("[")) {
                    //ipv6
                    String [] ipv6String=remoteip.split("]:");//根据以前写的可以判断长度一定是2
                    agent.setRemoteHost(ipv6String[0]+"]");
                    agent.setRemotePort(Integer.parseInt(ipv6String[1]));
                    _log.info("IPV6:"+agent.getRemoteHost()+":"+agent.getRemotePort());
                }else {//默认iPv4
                    agent.setRemoteHost(remoteip.split(":")[0]);
                    agent.setRemotePort(Integer.parseInt(remoteip.split(":")[1]));
                }
            }
        }
        if (null != agent.getRemoteHost() && !("").equals(agent.getRemotePort()))
        {
            EngineRepositotyJdbc.getInstance().saveMockShellCmdInfo(agent.getRemoteHost(), agent.getRemotePort(), shellName,execAct,dbType);
        }
    }

    public static void mockXmlRpcConn ( ActivityElement actElem, String shellName,ExecAct execAct, int dbType , Connection conn ,Long flowId)
            throws AgentCommunicationException, ServerException, RepositoryException {
        //POC项目修改，目的是为了引擎提速，从而做了server挡板功能
        long start = System.currentTimeMillis();
        AgentBean agent = new AgentBean();
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                agent.setRemoteHost(actElem.getRemoteHost());
                agent.setRemotePort(actElem.getRemotePort());
                agent.setSslMode(actElem.getConnectType());
            } else
            {
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByNameType(actElem.getRemoteAgent(), dbType);
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
//                    ret = getAgentInfoByReqid(actElem, reqId, 0);
                } else
                {
                    agent.setRemoteHost(agentResource.getAgentHost());
                    agent.setRemotePort(agentResource.getAgentPort());
                    agent.setSslMode(agentResource.getConnType());
                }
            }
        }

        if (null == agent.getRemoteHost())
        {
            // 获得agentip的对象并转换为string
            Object objreip = com.ideal.ieai.server.engine.expreval.ExprEvaluator
                    .eval((String) actElem.getInputExprs().get(NORMAL_AGENTIP));

            String remoteip = (String) objreip;
            if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
            {
                actElem.setRemoteExec(true);
                if(remoteip.contains("[")&&remoteip.contains("]")&&remoteip.indexOf("[")<remoteip.indexOf("]")&&remoteip.startsWith("[")) {
                    //ipv6
                    String [] ipv6String=remoteip.split("]:");//根据以前写的可以判断长度一定是2
                    agent.setRemoteHost(ipv6String[0]+"]");
                    agent.setRemotePort(Integer.parseInt(ipv6String[1]));
                    _log.info("IPV6:"+agent.getRemoteHost()+":"+agent.getRemotePort());
                }else {//默认iPv4
                    agent.setRemoteHost(remoteip.split(":")[0]);
                    agent.setRemotePort(Integer.parseInt(remoteip.split(":")[1]));
                }
            }
        }
        _log.info("写入agent表的耗时1：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
        start = System.currentTimeMillis();
        if (null != agent.getRemoteHost() && !("").equals(agent.getRemotePort()))
        {
            EngineRepositotyJdbc.getInstance().saveMockShellCmdInfoConn(agent.getRemoteHost(), agent.getRemotePort(), shellName,execAct,dbType,conn);
        }
        _log.info("写入agent表的耗时2：" + (System.currentTimeMillis() - start)+" ; flowid : "+flowId);
    }

    private static XmlRpcClient getXmlRpcClient ( ActivityElement actElem, String reqId, int type )
            throws AgentCommunicationException, ServerException
    {
        RuntimeEnv.setExecContext(getExecContext(reqId, type));
        AgentBean agent = new AgentBean();
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                agent.setRemoteHost(actElem.getRemoteHost());
                agent.setRemotePort(actElem.getRemotePort());
                agent.setSslMode(actElem.getConnectType());
            } else
            {
                agent = damageRemoteGroup(actElem, reqId, type);
                Boolean xianBankAgentip =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.XIAN_BANK_AGENTIP_MAIN_BACKUPS_SWITCHOVER, false);
                if(xianBankAgentip){//主ip手动切换远程执行AgentIP开关
                    AgentIpMainBackupsBean agentIpMainBackupsBean = new AgentIpMainBackupsBean();
                    agentIpMainBackupsBean= AgentipMainBackups(reqId,actElem);
                    if(agentIpMainBackupsBean!=null){
                        if(!"".equals(agentIpMainBackupsBean.getAgentHost())&&null!=agentIpMainBackupsBean.getAgentHost()){
                            agent.setRemoteHost(agentIpMainBackupsBean.getAgentHost());
                            agent.setRemotePort(agentIpMainBackupsBean.getAgentPort());
                        }
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {
            agent = getAgentInfoByReqid(actElem, reqId, 0);
        }

        if (null == agent.getRemoteHost())
        {
            // 获得agentip的对象并转换为string
            Object objreip = com.ideal.ieai.server.engine.expreval.ExprEvaluator
                    .eval((String) actElem.getInputExprs().get(NORMAL_AGENTIP));

            String remoteip = (String) objreip;
            if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
            {
                actElem.setRemoteExec(true);
                if(remoteip.contains("[")&&remoteip.contains("]")&&remoteip.indexOf("[")<remoteip.indexOf("]")&&remoteip.startsWith("[")) {
                    //ipv6
                    String [] ipv6String=remoteip.split("]:");//根据以前写的可以判断长度一定是2
                    agent.setRemoteHost(ipv6String[0]+"]");
                    agent.setRemotePort(Integer.parseInt(ipv6String[1]));
                    _log.info("IPV6:"+agent.getRemoteHost()+":"+agent.getRemotePort());
                }else {//默认iPv4
                    agent.setRemoteHost(remoteip.split(":")[0]);
                    agent.setRemotePort(Integer.parseInt(remoteip.split(":")[1]));
                }
            }
        }
        return makeRpcClient(actElem, proxySwitch(agent, type));
    }

    /**   
     * @Title: damageRemoteGroup   
     * @Description: 降低复杂度拆出
     * 专门用于处理远程执行资源组
     * @param actElem
     * @param reqId
     * @param type
     * @return
     * @throws AgentCommunicationException      
     * @author: Sayai 
     * @date:   2019年1月15日 下午12:00:22   
     */
    private static AgentBean damageRemoteGroup ( ActivityElement actElem, String reqId, Integer type )
            throws AgentCommunicationException
    {
        IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                .getSharedResInsByNameType(actElem.getRemoteAgent(), type);
        if (null == agentResource)
        {
            agentExp(actElem);
        }
        AgentBean ret = new AgentBean();
        if (agentResource.isRemoteGroup())
        {
            ret = getAgentInfoByReqid(actElem, reqId, 0);
        } else
        {
            ret.setRemoteHost(agentResource.getAgentHost());
            ret.setRemotePort(agentResource.getAgentPort());
            ret.setSslMode(agentResource.getConnType());
        }
        return ret;
    }

    /**   
     * @Title: makeRpcClient   
     * @Description: 降低复杂度拆出
     * 该方法仅用于直接创建rpc客户端对象。   
     * @param actElem
     * @param agent
     * @return
     * @throws AgentCommunicationException      
     * @author: Sayai 
     * @date:   2019年1月15日 上午11:51:03   
     */
    private static XmlRpcClient makeRpcClient ( ActivityElement actElem, AgentBean agent )
            throws AgentCommunicationException
    {
        XmlRpcClient rpcClient = null;
        try
        {
            int ssltype= RpcSslType.getInstance().exec(agent.getRemoteHost(), agent.getRemotePort());
            rpcClient = new AgentXmlRpcClient(agent.getRemoteHost(), agent.getRemotePort(), ssltype);
        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        return rpcClient;
    }

    /**   
     * @Title: getAgentInfoByReqid   
     * @Description: 降低方法复杂度拆出该方法。
     * 改方法调用远程执行持久层方法，依据reqid获取agent信息
     * @param actElem
     * @param reqId
     * @param type
     * @return
     * @throws AgentCommunicationException      
     * @author: Sayai 
     * @date:   2019年1月15日 上午11:37:51   
     */
    private static AgentBean getAgentInfoByReqid ( ActivityElement actElem, String reqId, int sslMode )
            throws AgentCommunicationException
    {
        String[] nodeInfo = new String[2];
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            agentExp(actElem);
        }
        AgentBean ret = new AgentBean();
        ret.setRemoteHost(nodeInfo[0]);
        ret.setRemotePort(Integer.parseInt(nodeInfo[1]));
        ret.setSslMode(sslMode);
        return ret;
    }

    /**   
     * @Title: proxySwitch   
     * @Description: 降低复杂度，独立proxy开关及赋值的处理   
     * @param bean
     * @param type
     * @return      
     * @author: Sayai 
     * @date:   2019年1月15日 上午11:29:29   
     */
    private static AgentBean proxySwitch ( AgentBean bean, int type )
    {
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        int ssltype= RpcSslType.getInstance().exec(bean.getRemoteHost(), bean.getRemotePort());
        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            Hashtable ha = new Hashtable();

            ProxyModel pm = ps.getPerformDataJsonData(ha, bean.getRemoteHost(), bean.getRemotePort(), type);
            if (pm.isProxy())
            {
                _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:"
                        + bean.getRemoteHost() + ":" + bean.getRemotePort() + " proxySwitch is true ");
            }
            bean.setRemoteHost(pm.getIp());
            bean.setRemotePort(pm.getPort());
        }
        return bean;
    }

    private static ProxyModel getXmlRpcClientProxyModel ( Hashtable ha, ActivityElement actElem, String reqId,
            int type ) throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        RuntimeEnv.setExecContext(getExecContext(reqId, type));
        ProxyModel pm = null;
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                remoteHost = actElem.getRemoteHost();
                remotePort = actElem.getRemotePort();
                sslMode = actElem.getConnectType();
            } else
            {
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByNameType(actElem.getRemoteAgent(), type);
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
                    String[] nodeInfo = null;
                    try
                    {
                        nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
                    } catch (RepositoryException e)
                    {
                        _log.info(e);
                    }
                    if (null == nodeInfo || null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1]
                            || "".equals(nodeInfo[1]))
                    {
                        AgentCommunicationException agentExp = new AgentCommunicationException();
                        agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                        agentExp.setMessage("Communication Agent Error!");
                        agentExp.setTime(new Date());
                        throw agentExp;
                    }
                    remoteHost = nodeInfo[0];
                    remotePort = Integer.parseInt(nodeInfo[1]);
                    sslMode = 0;
                } else
                {
                    remoteHost = agentResource.getAgentHost();
                    remotePort = agentResource.getAgentPort();
                    sslMode = agentResource.getConnType();
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {
            String[] nodeInfo = new String[2];
            try
            {
                nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
            } catch (RepositoryException e)
            {
                _log.info(e);
            }
            if (null == nodeInfo || null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1]
                    || "".equals(nodeInfo[1]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
            remoteHost = nodeInfo[0];
            remotePort = Integer.parseInt(nodeInfo[1]);
            sslMode = 0;
        }
        String remoteip = (String) actElem.getInputExprs().get("$normal.agentip");
        if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
        {
            if (remoteip.startsWith("$"))
            {
                Object ret = com.ideal.ieai.server.engine.expreval.ExprEvaluator.eval(remoteip);
                remoteip = (String) ret;
            }
            if (remoteip == null || "".equals(remoteip))
            {
                _log.info("Now agent:host is " + remoteHost + " ## port is " + remotePort + ",requestid=" + reqId);
                try
                {
                    String[] node = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
                    if (null != node[0] || !("".equals(node[0])))
                    {
                        remoteHost = node[0];
                        remotePort = Integer.parseInt(node[1]);
                    }
                    _log.info("Find agent info from remoteexecact and use it:host is " + remoteHost + " ## port is "
                            + remotePort + ",requestid=" + reqId);
                } catch (RepositoryException e1)
                {
                    _log.error("get agent info from IEAI_REMOTEEXECACT error!", e1);
                }
                ActivityElement ae = actElem;
                ae.setRemoteExec(true);
            } else
            {

                if (remoteip.startsWith("\""))
                {
                    remoteip = remoteip.substring(1, remoteip.length() - 1);
                }

                ActivityElement ae = actElem;
                ae.setRemoteExec(true);
                remoteHost = remoteip.split(":")[0];
                remotePort = Integer.parseInt(remoteip.split(":")[1]);
            }
        }
        PerformDataProcessService ps = new PerformDataProcessService();
        pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, type);
        return pm;
    }

    /**
     * <AUTHOR>
     * @des:建立与agent的xmlrpc连接
     * @datea:2011-7-21
     * @param agentIp
     * @param agentPort
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException
     */
    private static XmlRpcClient getXmlRpcClient ( String agentIp, int agentPort ) throws AgentCommunicationException
    {

        XmlRpcClient rpcClient;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(agentIp, agentPort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, agentPort, Constants.IEAI_IEAI_BASIC);
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIp
                            + ":" + agentPort + " proxySwitch is true ");
                }
                agentIp = pm.getIp();
                agentPort = pm.getPort();
                ssltype=pm.getSsl();
            }
            rpcClient = new AgentXmlRpcClient(agentIp, agentPort, ssltype);
        } catch (MalformedURLException ex)
        {
            AgentCommunicationException agentExp = new AgentCommunicationException(ex);
            agentExp.setAgentPort(String.valueOf(agentPort));
            agentExp.setAgentHost(agentIp);
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error for AgentAutoRenew!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        return rpcClient;
    }

    private static XmlRpcClient getXmlRpcClient ( String agentIp, int agentPort, int type )
            throws AgentCommunicationException
    {

        XmlRpcClient rpcClient;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(agentIp, agentPort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, agentPort, type);
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIp
                            + ":" + agentPort + " proxySwitch is true ");
                }
                agentIp = pm.getIp();
                agentPort = pm.getPort();
                ssltype=pm.getSsl();
            }
            rpcClient = new AgentXmlRpcClient(agentIp, agentPort, ssltype);
        } catch (MalformedURLException ex)
        {
            AgentCommunicationException agentExp = new AgentCommunicationException(ex);
            agentExp.setAgentPort(String.valueOf(agentPort));
            agentExp.setAgentHost(agentIp);
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error for AgentAutoRenew!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        return rpcClient;
    }

    public static int exec ( ExecAct execAct, String reqId ) throws ActivityException, ServerException
    {

        RExecRequestWrapper requestWrapper = getReqWraper(reqId, Constants.IEAI_IEAI_BASIC);

        GetServerAct act = new GetServerAct();

        Vector params = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        params.addElement(request.getId());
        params.addElement(request.getServerHost());
        params.addElement(Integer.valueOf(request.getServerPort()));
        /*
         * update by lei_wang on 2009.08.19 Add params with transfer GetServerAct for getting
         * parameter of activity.
         */
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        Hashtable ha = (Hashtable) act.getActRemAct(requestWrapper.getRexecRequest().getId());
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, reqId, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
                _log.info("proxy- actElem:" + execAct.getFlowId() + " remoteIP: " + actElem.getRemoteHost());
                _log.info("proxy- request：" + execAct.getFlowId() + " remoteIP: " + request.getAgentHost());
            }
        } else
        {
            _log.info("actElem:" + execAct.getFlowId() + " remoteIP: " + actElem.getRemoteHost());
            _log.info("request：" + execAct.getFlowId() + " remoteIP: " + request.getAgentHost());
        }
        ha.put("serverHost", request.getServerHost());
        ha.put("serverPort", Integer.valueOf(request.getServerPort()));
        params.addElement(ha);

        XmlRpcClient rpcClient = getXmlRpcClient(actElem, reqId, Constants.IEAI_IEAI_BASIC);
        for (int i = 0; i < 2; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                rpcClient.execute(ieaiExe, params);
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                try
                {
                    Double prjTypeD = IEAIRuntime.current().getProject().getProjectTpye();
                    try
                    {
                        int dbType = (new Double(prjTypeD)).intValue();
                        if (prjTypeD == Constants.IEAI_DAILY_OPERATIONS)
                        {
                            // dbType = Constants.IEAI_INFOCOLLECTION;
                            dbType = Constants.IEAI_DAILY_OPERATIONS;
                        }
                        EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(),
                            request.getAgentHost(), request.getAgentPort(), actElem, requestWrapper.getRexecRequest(),
                            execAct, dbType);
                    } catch (RepositoryException e)
                    {
                        _log.info("Error when saveAgentRunActNumAdd error!");
                    }
                } catch (Exception e)
                {

                    _log.info("Error when get project type!");
                }
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName() + " RpcClientAgentIP:" + rpcClient.getURL());

                return Constants.END;
            } catch (Exception e)
            {
                if (i != 1)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                    }
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    agentExp(actElem);
                }
            }
        }
        return Constants.END;

    }

    /**
     * <AUTHOR>
     * @des:活动重定向处理
     * @datea:2010-4-8
     * @param execAct
     * @param reqId
     * @param elem
     * @return
     * @throws ActivityException
     * @throws ServerException
     */
    public static int reSetAct ( ExecAct execAct, String reqId ) throws ActivityException, ServerException
    {
        RExecRequestWrapper requestWrapper = getReqWraper(reqId, Constants.IEAI_IEAI_BASIC);

        GetServerAct act = new GetServerAct();
        Vector params = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        params.addElement(request.getId());
        params.addElement(request.getServerHost());
        params.addElement(Integer.valueOf(request.getServerPort()));
        /*
         * update by lei_wang on 2009.08.19 Add params with transfer GetServerAct for getting
         * parameter of activity.
         */
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        Hashtable ha = (Hashtable) act.getActRemAct(requestWrapper.getRexecRequest().getId());
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, reqId, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
            }
            if (pm.isProxy())
            {
                _log.info("proxy- requestId:" + reqId + " proxySwitch is true ");
            } else
            {
                _log.info("agent- requestId:" + reqId + " proxySwitch is true ");
            }
        }
        ha.put("serverHost", request.getServerHost());
        ha.put("serverPort", Integer.valueOf(request.getServerPort()));
        _log.info(
            "reSetAct: reqId:serverip:port: " + reqId + ":" + request.getServerHost() + ":" + request.getServerPort());
        params.addElement(ha);
        XmlRpcClient rpcClient = getXmlRpcClient(actElem, reqId, Constants.IEAI_IEAI_BASIC);
        for (int i = 0; i < 2; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                rpcClient.execute("IEAIAgent.reSetAct", params);
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                try
                {
                    _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                            + execAct.getActName());
                } catch (Exception e)
                {
                    _log.info("Error when log for Send Message to Agent!");
                }
                return Constants.END;
            } catch (Exception e)
            {
                if (i != 1)
                {
                    _log.error(
                        "It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn + actElem.getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info("Time out is change to:" + newTimeout + " reqid="
                                + requestWrapper.getRexecRequest().getId() + acn + actElem.getName());
                    }
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    agentExp(actElem);
                }
            }
        }
        return Constants.END;
    }

    /**
     * <li>Description:备援任务重定向</li>
     * 
     * <AUTHOR> 2015年12月1日
     * @param reqId
     * @param serverIp
     * @return return int
     */
    public static int reSetAct ( String reqId, String serverIp, String serverPort, String agentIp, String agentPort,String taskname,String starttime,int taskinsid )
            throws ActivityException, ServerException, RepositoryException
    {

        String remoteHost = agentIp;
        int remotePort = Integer.parseInt(agentPort);
        int sslMode = 0;// 不加密
        XmlRpcClient rpcClient = null;
        Vector params = new Vector();
        Vector params1 = new Vector();
        Hashtable ha = new Hashtable();
        ProxyModel pm = new ProxyModel();
        boolean gdswitch = Environment.getInstance().getBooleanConfig("timetask.gd.special.switch", false);
        boolean isFunVriableSwitch = Environment.getInstance().getBooleanConfigNew("tt.script.funandvariable.switch", false);
        boolean timetaskJoinExcuteUserSwitch = Environment.getInstance().getTimetaskJoinExcuteUserSwitch();
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        try
        {
           
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);

            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI_BASIC);
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + " proxySwitch is true ");
                }
                remoteHost = pm.getIp();
                remotePort = pm.getPort();
            }
            _log.info("remoteHost : " + remoteHost + ", remotePort : " + remotePort + ",sslMode : " + sslMode);
            rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
        } catch (MalformedURLException e)
        {
            AgentCommunicationException agentExp = new AgentCommunicationException(e);
            agentExp.setAgentPort(String.valueOf(remotePort));
            agentExp.setAgentHost(remoteHost);
            agentExp.setConnectionType(sslMode);
            agentExp.setActivityName("定时任务重定向");
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        Connection con = null;
        con = DBResource.getRConnection("execTimeTask", _log, Constants.IEAI_TIMINGTASK);
        String adpDefUuid = ProjectManager.getInstance().getLatestAdaptorInfo("shellcmd", con);// 获取接口UUID
        try
        {
            con.close();
        } catch (SQLException e)
        {
            LogPrintUtil.logPrintErr(_log, e);
            e.printStackTrace();
        }
        Hashtable timetaskHashtable =getTimetaskInsInfo(taskinsid);
        String execUser = timetaskHashtable.get("execUser") != null ? timetaskHashtable.get("execUser").toString():"";
//        ha.put("executeUsername", execUser );
        if(gdswitch && !timetaskJoinExcuteUserSwitch) {
            ha.put("executeUsername", "" );
        }else {
            ha.put("executeUsername", execUser != null ? execUser :  "" );
        }
        
        ha.put("isFunVriableSwitch", isFunVriableSwitch);
        timetaskHashtable.put("isFunVriableSwitch", isFunVriableSwitch);
        if (isFunVriableSwitch){
            try
            {
                IScriptInstance instance = IeaiSpringContextUtil.getDubboBean(IScriptInstance.class);
                //根据agent查询其所绑定的函数与变量  
                Map<String,Object> funAndVar = instance.getFunctionAndVariableByAgentId(remoteHost,remotePort);
                if(funAndVar!=null || funAndVar.size()>0){
                      //函数、变量，函数使用加密形式传递
                    if(funAndVar.get("variable") != null){
                        ha.put("envparam",funAndVar.get("variable"));  
                        timetaskHashtable.put("envparam",funAndVar.get("variable"));
                    }else{
                        ha.put("envparam",""); 
                        timetaskHashtable.put("envparam",""); 
                    }
                    
                    Object funcObj = funAndVar.get("funcData");
                    if(funcObj!=null){
                        funcObj = (Map)funcObj;
                    }
                    net.sf.json.JSONObject jsonobject = net.sf.json.JSONObject.fromObject(funcObj);
                    String jsonStr= jsonobject.toString();//json字符串
                    DesUtils desUtils = new DesUtils("functionJsonStr");
                    ha.put(Constants.SERVER_PUSH_FUNCS_KEY,desUtils.encrypt(jsonStr));
                    timetaskHashtable.put(Constants.SERVER_PUSH_FUNCS_KEY,desUtils.encrypt(jsonStr));
                }
            } catch (BadPaddingException e) {
                e.printStackTrace();
                _log.error("tryATry error"+e.getMessage());
            } catch (IllegalBlockSizeException e) {
                e.printStackTrace();
            }
        }
        ha.put("input", timetaskHashtable);
        ha.put("flowId", String.valueOf(taskinsid));// 表IEAI_TIMETASK_RUNTIME字段TASKINSID实例ID
        ha.put("adaptorConfig", 1);
        ha.put("adaptorDefUUID", adpDefUuid);
        ha.put("projectName", "shell_test");
        ha.put("_scopeId", "162865");// ?
        ha.put("_actStateDataVersion", -1);// ?
        ha.put("timeout", "0");// ?
        ha.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
        ha.put("actName", "ShellCmd"); // 默认值
        ha.put("adaptorDefName", "shellcmd");// shellcmd
        ha.put("status", 2);// ? 默认值
        ha.put("flowPoolNum", "0");// ?默认值
        ha.put("levelOfWeight", "1");// ? 默认值
        ha.put("isSafeFirst", true); // ?默认值
        ha.put("levelOfPRI", "5");// ?默认值
        ha.put("agentHost", remoteHost);
        ha.put("agentPort", remotePort);
        ha.put("Id", reqId);
        if(StringUtils.isBlank(starttime)){
            ha.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));           
        }else{
            ha.put("startTime", starttime);
        }
        // 定时任务实例启动时间
        if(StringUtils.isBlank(taskname)){
            ha.put("flowName", "");
        }else{
            ha.put("flowName", taskname);
        }
        params.addElement(reqId);
        params.addElement(serverIp);
        params.addElement(Integer.parseInt(serverPort));
        ha.put("serverHost", serverIp);
        ha.put("serverPort", Integer.parseInt(serverPort));
        params.addElement(ha);

        params1.addElement(reqId);

        for (int i = 0; i < 10; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                _log.info("Send Message to Agent ......! ");
                rpcClient.execute("IEAIAgent.reSetAct", params);
                if (SystemConfig.isElapsedTime())
                {
                    _log.info("server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                }
                _log.info("Send Message to Agent Successsful! ");
                try
                {
                    if (ServerEnv.getInstance().getTimetaskRecoverUpdateStateSwitch() && reqId.indexOf("timetask") > -1)
                    {
                        Thread.sleep(1000);
                        if (proxySwitch && pm.isProxy()) {
                            params1.addElement(ha); 
                        }
                        Integer returnValue = (Integer) rpcClient.execute("IEAIAgent.recoverReq", params1);
                        if (returnValue == 0)
                        {
                            _log.info("act exec is end ");
                            TimeTaskManager.getInstance().recoverUpdateTaskState(0, reqId, Constants.IEAI_TIMINGTASK);
                            _log.info("recover update timetask ip state end!" + reqId);
                        }
                    }
                } catch (Exception e)
                {
                    _log.info("recover update timetask ip state is error : " + e.getMessage(), e);
                }

                return Constants.END;
            } catch (Exception e)
            {
                _log.error("reSetAct is Exception" + e.getMessage(), e);
                AgentCommunicationException agentExp = new AgentCommunicationException(e);
                agentExp.setAgentPort(String.valueOf(remotePort));
                agentExp.setAgentHost(remoteHost);
                agentExp.setConnectionType(sslMode);
                agentExp.setActivityName("定时任务重定向");
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
        }

        return Constants.END;
    }
    
    public static Hashtable getTimetaskInsInfo ( long taskInsId )
    {
        Hashtable infoHashtable = new Hashtable();

        String sql = "SELECT A.ID,A.TASKNAME,A.TASKINFOID,A.TASKRUNTIME,A.TASKCOMMAND,A.GID,A.SERVERIP,A.GID,INFO.EXECUSER FROM IEAI_TIMETASK_INSTANCE A,IEAI_TIMETASK_INFO INFO WHERE A.TASKINFOID = INFO.ID AND A.ID = ?";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                PreparedStatement ps = null;
                ResultSet rs = null;
                try
                {
                    con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_TIMINGTASK);

                    ps = con.prepareStatement(sql);
                    ps.setLong(1, taskInsId);
                    rs = ps.executeQuery();

                    while (rs.next())
                    {
                        infoHashtable.put("command", rs.getString("TASKCOMMAND"));
                        infoHashtable.put("execUser", rs.getString("EXECUSER") != null? rs.getString("EXECUSER"):"");
                    }

                } catch (SQLException e)
                {

                    _log.error("getTimetaskInsInfo error： " , e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);

                } catch (DBException e)
                {
                    _log.error("getTimetaskInsInfo error: " , e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, rs, ps, "getTimetaskInsInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                try
                {
                    Thread.sleep(1000);
                } catch (InterruptedException e)
                {
                }
                if (i == 9)
                {
                    _log.error("getShellContent error: " + ex.getMessage(), ex);
                    return null;
                }
            }
        }

        return infoHashtable;
    }

    public static void updateRequestStatusTimetaskRebuild ( String reuqestUUID, int status, Hashtable output,
            Throwable exception ) throws RepositoryException
    {
        String lastLine = null;
        int ipState = -1;
        if (null != output.get(lline))
        {
            lastLine = CLSBase64.getFromBASE64((String) output.get(lline));
        }

        if (Environment.getInstance().getTimeTaskResponseExpectType() && status == 1) {
            ipState = 0;
        //下面为原有逻辑
        } else if (status == 1 && "0".equals(lastLine))
        {
            ipState = 0;
        } else
        {
            ipState = -5;
            // 定时任务应用异常日志打印
            MonitorSyslog.getInstance().sendTimeTaskExpLog(reuqestUUID, lastLine);
        }
        String taskInsId = TimeTaskManager.getInstance().agentUpdateTaskState(ipState, reuqestUUID,
            Constants.IEAI_TIMINGTASK);
        _log.info("Agent update request status: reqid=" + reuqestUUID + " status=" + status + " ex="
                + (exception != null ? exception.getMessage() : "no") + " taskinsid=" + taskInsId+" lastline="+lastLine);

        if (null != taskInsId && !"".equals(taskInsId))
        {
            // save timetask output info
            String ids[] = taskInsId.split(",");
            if (null != ids[1] && !"".equals(ids[1]))
            {
                String stdout = "";
                LobStorer ls = new LobStorer();
                //输出日志过长导致存储不到output表，开启开关后截取日志存储
                boolean isSubInfo=Environment.getInstance().getSubInfoLongToShortSwitch();
                if(true){
                    if (null != output.get(sout))
                    {
                        stdout += CLSBase64.getFromBASE64((String) output.get(sout));
                    }
                    stdout += "\r\n";
                    if (null != output.get(serr))
                    {
                        stdout += CLSBase64.getFromBASE64((String) output.get(serr));
                    }
                    
                    try {
                        String desc = "...日志因超过最大字符长度限制被截断";
                        //默认设置编码为GBK
                        String typecode = Environment.getInstance().getSysConfig("timetask.set.subinfo.code","GBK");
                        //默认设置数据库中ISTDOUT大小为2M
                        String size = Environment.getInstance().getSysConfig("timetask.set.clob.size","2048");
                        int sizekb=Integer.valueOf(size)*1024;
                        if(stdout.getBytes(typecode).length > sizekb){
                            _log.info("截取前stdout getbytes length:"+stdout.getBytes(typecode).length);
                            String chartset = "GBK";
                            int limitsize = Integer.valueOf(sizekb)-desc.getBytes(typecode).length;
                            stdout=desc+StringCutByCharset(stdout,Integer.valueOf(limitsize),chartset);
                            _log.info("截取后stdout getbytes length:"+stdout.getBytes(typecode).length);
                        }
                    } catch (UnsupportedEncodingException e) {
                        _log.error("ActivityRExecHelper.updateRequestStatusTimetaskRebuild UnsupportedEncodingException error:",e);
                    }
                }else{
                    if (null != output.get(serr))
                    {
                        stdout += CLSBase64.getFromBASE64((String) output.get(serr));
                    }
                    if (null != output.get(sout))
                    {
                        stdout += CLSBase64.getFromBASE64((String) output.get(sout));
                    }
                }
                try
                {
                    _log.info(stdout);
                    long ipid = Long.parseLong(ids[1]);
                    ls.saveTimeTaskOutput(ipid, stdout, Constants.IEAI_TIMINGTASK);
                } catch (RepositoryException e)
                {
                    _log.error("保存TimeTaskIP活动输出信息失败:" + e);
                }
                boolean             isWaring = ServerEnv.getInstance().isTimetaskWarning();// 定时任务报警平台集成开关
                if (isWaring)
                {
                    // 东莞农商取消agent级别告警
                    if (!Environment.getInstance().getDGNSTimeTaskSwitch()) {
                        // 告警平台集成 发送告警
                        if (StringUtils.isBlank(lastLine)) {
                            lastLine = stdout;

                            if (lastLine.length() > 150) {
                                lastLine = lastLine.substring(0, 150);
                            }
                        }
                        Long ipid = Long.parseLong(ids[1]);
                        Long insId = Long.parseLong(ids[0]);
                        // 判断是否为终止操作的返回，此场景不发送告警，由终止线程发送
                        if (TimeTaskManager.getInstance().ExistException(ipid) == 0) {
                            // 超时->执行完成（0）
                            // 异常->重试->执行完成（0）
                            // 执行完成（0）
                            if (ipState == 0) {
                                // 完成,业务异常状态下 自动判断是否发送恢复告警
                                new TimetaskSendAlarmThread(Long.parseLong(ids[1])).start();
                            } else {
                                // 超时->执行完成（1）
                                // 执行完成（1）
                                _log.info("执行业务异常，向告警平台发送信息,lastline为：" + lastLine);
                                new TimetaskSendAlarmThread(ipid, lastLine).start();
                            }
                        }
                    }
                }
                //自动重试功能 ，任务异常进行重试 （根据任务管理配置的属性重试，不是所有异常任务都需要自动重试）
                boolean  isRetry = ServerEnv.getInstance().getBooleanConfig(Environment.TIMETASK_ERRORTASK_SELF_RETRY_SWITCH, false);
                if(isRetry&& ipState == -5)
                {
                    errorTaskSelfRetry(reuqestUUID, ipState, ids);
                }
                
                boolean pfWarnNoticeSwitch = ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_TT_WARN_NOTICE_SWITCH, false);
                if (pfWarnNoticeSwitch && ipState == -5)
                {
                    //浦发定时任务异常且重要程度为重要需发送通知邮件
                    SendEmailForTTManager.getInstance().sendFlowExceptionEmail(Long.parseLong(ids[1]), stdout, true);
                }
                
                boolean pfWarnNoticeCallSwitch = ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_TT_WARN_NOTICE_CALL_SWITCH, false);
                if (pfWarnNoticeCallSwitch && ipState == -5)
                {
                    //浦发定时任务异常且重要程度为重要需发送通知邮件和告警平台
                    SendEmailForTTManager.getInstance().sendExceptionWarnNotice(Long.parseLong(ids[1]), stdout);
                }

                //南京定制报表（大表大查询、容量分析、健康度）
                boolean njswitch = Environment.getInstance().getBooleanConfigNew2("nj.task.special.switch", false);

                if(njswitch){
                    long ipid = Long.parseLong(ids[1]);
                    try
                    {
                        stdout = new String(stdout.getBytes(),  Environment.getInstance().getSysConfig("timetask.set.subinfo.code","GBK"));
                    } catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                    TimeTaskManager.getInstance().resultDistributeForNj(stdout,ipid);
                }
            }

        }
    }
    
    private static String StringCutByCharset(String str,int len,String charset){

        byte[] bs = null;
        String s = "";
        try {
            int count = 0;  
            bs = str .getBytes(charset);
            for(int i=len;i>0;i--){   
                if(bs[bs.length-i]<0){   
                    count++;    
                }else{        
                    break;        
                }
            }
            if("GBK".equals(charset)){
                if(count==1 && len==1){
                    _log.error("截取少于3个字符且有字符为汉字，返回空");
                    return str;
                }
                if(count%2==0){
        
                    s=new String(bs, bs.length-len, len, "GBK");
                    _log.info("截取"+len+"个字符："+s);
        
                }else{
        
                    s=new String(bs, bs.length-len+1, len-1, "GBK");
                    _log.info("截取"+len+"个字符："+s);
        
                }
            }else if("UTF-8".equals(charset)){
                if(count>0&& len<3 ){
                    _log.error("截取少于3个字符且有字符为汉字，返回空");
                    return str;
                }
                if(count%3==0){    
                    s=new String(bs, bs.length-len, len, "UTF-8");        
                    _log.info("截取"+len+"个字符："+s);    
                }else{
                    s=new String(bs, bs.length-len+(count%3), len-count%3, "UTF-8");        
                    _log.info("截取"+len+"个字符："+s);    
                }
            }

        } catch (UnsupportedEncodingException e) {

            _log.error("传递了不识别的字符集！",e);

        }

        return s;

    }

    /**
     * @Title: errorTaskSelfRetry
     * @Description 任务异常自动重试
     * @param
     * @return void
     * <AUTHOR>
     * @date  2021年04月09日 15:36:03
     **/
    public static void errorTaskSelfRetry(String reuqestUUID, int ipState, String[] ids) throws RepositoryException
    {
        //根据uuid 查询组织数据 判断是否需要重试
        Map data1 = TTManageManager.getInstance().disposeRedo(ids[1]);
        if(!data1.containsKey("SUCCESS"))
        {
            return;
        }
        if((Boolean)data1.get("SUCCESS")) //需要重试
        {
            //醒了在确定状态 ，然后进行修改
            int finalIpState = ipState;
            new Thread(new Runnable()
            {
                @Override
                public void run()
                {
                    try
                    {
                        if((Boolean)data1.get("SUCCESS"))
                        {
                            _log.info("ipState = " + finalIpState + "; reqId = " + reuqestUUID +" 任务异常，自动重试线程启动，等待："+data1.get("TIMEC")+"分钟后自动重试！");
                            Thread.sleep(Long.parseLong(String.valueOf(data1.get("TIMEC")))*1000*60);
                            Map data2 = TTManageManager.getInstance().disposeRedo(ids[1]);
                            if(!data2.containsKey("SUCCESS"))
                            {
                                _log.info(" 线程等待时间任务状态更改自动重试取消！");
                                return;
                            }
                            if(-5 == (Integer) data2.get("TASKIPSTATE"))
                            {
                                if((Boolean)data2.get("SUCCESS"))
                                {
                                    //更新IP表重试次数
                                    boolean fa = TTManageManager.getInstance().updateipIredonum(Long.parseLong(ids[1]));
                                    if(fa)
                                    {
                                        CustomizationBean cb = new CustomizationBean();
                                        cb.setIid(Long.parseLong(String.valueOf(data2.get("INSID"))));
                                        cb.setRequestId(String.valueOf(data2.get("UUID")));
                                        cb.setTaskName(String.valueOf(data2.get("TASKNAME")));
                                        cb.setIip(String.valueOf(data2.get("IP")));
                                        cb.setPeriod(String.valueOf(data2.get("TASKRUNTIME")));
                                        cb.setScript(String.valueOf(data2.get("TASKCOMMAND")));
                                        cb.setGroupId(Long.parseLong(String.valueOf(data2.get("GID"))));
                                        TaskInstanceDao.getInstance().redoTimeTask(cb);
                                    }
                            }
                            }else
                            {
                                _log.info(" 线程等待时间任务状态更改，不是异常状态！"+"ipState = " + data2.get("TASKIPSTATE") + "; reqId = " + data2.get("UUID") +" 自动重试取消！");
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }) {
            }.start();
        }else
        {
            _log.info("ipState = " + data1.get("TASKIPSTATE") + "; reqId = " + data1.get("UUID") +" 任务自动重试完成！");
        }
    }

    /**
     * The method wait until activity output is sent by remote agent.
     * 
     * @param output
     * @param reqWrapper
     * @throws ServerException
     */
    public static int checkOutput ( ExecAct execAct, String reqId, Map output ) throws ServerException
    {
        RExecRequestWrapper reqWrapper = getReqWraper(reqId, Constants.IEAI_IEAI_BASIC);

        if (null == reqWrapper.getActElem(Constants.IEAI_IEAI_BASIC))
        {
            return Constants.END;
        }

        RemoteActivityExecRequest request = reqWrapper.getRexecRequest();
        if (request.getStatus() == Constants.REXEC_REQ_STATE_NEW
                || request.getStatus() == Constants.REXEC_REQ_STATE_RUNNING
                || request.getStatus() == Constants.REXEC_REQ_STATE_NETWORK_RECOVERING)
        {
            if (SystemConfig.isElapsedTime())
            {
                _log.info("Check Out Put : 工作流Id " + execAct.getFlowId() + " 活动名称：" + execAct.getActName() + "  活动状态："
                        + request.getStatus());
            }
            // process network interrupt.
            if (isNetworkInterrupt(request))
            {
                ActivityElement actElem = reqWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
                if (actElem.getTimeout() > 0)
                {
                    Long timeout = request.getTimeout();
                    long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                    if (null != timeout && timeout.longValue() != newTimeout)
                    {
                        _dbRexecReqWraperRep.setTimeOut(reqId, newTimeout);
                        _log.info("Time out is change to:" + newTimeout + " reqid=" + request.getId());
                    }
                }
            }
            return Constants.REPEAT;
        }

        if (request.getOutput() != null)
        {
            output.putAll(request.getOutput());
        }

        if (request.getStatus() == Constants.REXEC_REQ_STATE_FAILED)
        {
            throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
        }
        if (request.getStatus() == Constants.REXEC_REQ_STATE_TIMEOUT)
        {
            throw new ServerException(ServerError.ERR_REMOTE_ACT_TIME_OUT);
        }
        return Constants.END;
    }

    /**
     * The method is used to recover activity by redo recovery policy.
     * 
     * @param requestId
     * @throws ServerException
     */
    public static void redoActivity ( String requestId ) throws ServerException
    {
        RExecRequestWrapper wrapper = getReqWraper(requestId, Constants.IEAI_IEAI_BASIC);
        try
        {
            RemoteActivityExecRequest request = wrapper.getRexecRequest();
            ActivityElement actElem = wrapper.getActElem(Constants.IEAI_IEAI_BASIC);
            XmlRpcClient rpcClient = getXmlRpcClient(actElem, requestId, Constants.IEAI_IEAI_BASIC);
            GetServerAct act = new GetServerAct();
            Vector params = new Vector();
            params.add(requestId);
            params.addElement(request.getServerHost());
            params.addElement(Integer.valueOf(request.getServerPort()));
            /*
             * update by lei_wang on 2009.08.19 Add params with transfer GetServerAct for getting
             * parameter of activity.
             */
            Hashtable ha = (Hashtable) act.getActRemAct(requestId);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, requestId, Constants.IEAI_IEAI_BASIC);
                if (pm.isProxy())
                {
                    ha.put("proxyJson", pm.getProxyJsonData());
                    _log.info(
                        "proxy- remoteURL:" + rpcClient.getURL() + ",requestId:" + requestId + " proxySwitch is true ");
                } else
                {
                    _log.info(
                        "agent- remoteURL:" + rpcClient.getURL() + ",requestId:" + requestId + " proxySwitch is true ");
                }
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
            } else
            {
                _log.info(
                    "agent- remoteURL:" + rpcClient.getURL() + ",requestId:" + requestId + " proxySwitch is false ");
            }
            ha.put("serverHost", request.getServerHost());
            ha.put("serverPort", Integer.valueOf(request.getServerPort()));
            params.addElement(ha);
            rpcClient.execute("IEAIAgent.reExecuteAct", params);
        } catch (Exception e)
        {
            _log.error(e.getMessage(), e);
            throw new ServerException(ServerError.ERR_XMLRPC_FAILED);
        }

    }

    public static void updateRequestStateData ( String requestId, int version, ActStateData stateData )
            throws ServerException
    {
        try
        {
            EngineRepository.getInstance().saveOrUpdateRExecRequestActStateData(requestId, version, stateData);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    /**
     * check if network is interrupted.
     */
    private static boolean isNetworkInterrupt ( RemoteActivityExecRequest request )
    {
        long curTime = System.currentTimeMillis();
        if (request.getLastUpdateTime() == null)
        {
            if (curTime - request.getStartTime().getTimeInMillis() > BASE_NETWORK_INTERRUPT_TIME)
            {
                return true;
            }
        } else
        {
            if (curTime - request.getLastUpdateTime().getTimeInMillis() > BASE_NETWORK_INTERRUPT_TIME)
            {
                return true;
            }
        }
        return false;
    }

    private static long getNetworkRecoverWaitTime ()
    {
        return Environment.getInstance().getLongConfig(Environment.NETWORK_RECOVERY_WAIT_TIME,
            Environment.NETWORK_RECOVERY_WAIT_TIME_DEFAULT) * 1000;
    }

    private static IRexecRequestWraperRepository getRexecReqWrapperRep ( boolean isSafeFirst )
    {
        return isSafeFirst ? _dbRexecReqWraperRep : _memRexecReqWraperRep;
    }

    public static long getFlowPoolNum ( String agentHost, String prjFlowName ) throws ServerException
    {
        try
        {
            // 修改为jdbc方式实现 tao_ding on 2010-1-13
            return EngineRepositotyJdbc.getInstance().getFlowPoolNum(agentHost, prjFlowName);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    /**
     * Get output of remote ShellCmd activity.
     * 
     * @param requestId
     * @param stdErrStartLine
     * @param stdOutStartLine
     * @param maxLines
     * @return
     * @throws ServerException
     * @throws AgentCommunicationException
     * @throws XmlRpcException
     * @throws IOException
     */
    public static ShellCmdOutput getShellCmdOutput ( String requestId, int stdErrStartLine, int stdOutStartLine,
            int maxLines, int countOfLastLineForStdErr, int countOfLastLineForStd, int type )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {

        Vector params = new Vector();
        params.addElement(requestId);
        params.addElement(Integer.valueOf(stdErrStartLine));
        params.addElement(Integer.valueOf(stdOutStartLine));
        params.addElement(Integer.valueOf(maxLines));
        params.addElement(Integer.valueOf(countOfLastLineForStdErr));
        params.addElement(Integer.valueOf(countOfLastLineForStd));

        try
        {
            long time00_begin = System.currentTimeMillis();
            RemoteActivityExecRequest raer = EngineRepository.getInstance().getRexecRequest(requestId, type);
            long time00_end = System.currentTimeMillis();
            PrintTimeLog.printLong(_log, time00_begin, time00_end, "getShellCmdOutput|step1.0|requestid=" + requestId);
            String ageip = raer.getAgentHost();
            int aprot = raer.getAgentPort();
            if (StringUtils.isEmpty(ageip))
                return new ShellCmdOutput(null, 0, null, 0, ShellCmdOutput.STATE_FINISHED, -1, -1);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(ageip, aprot);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, ageip, aprot, type);
                if (pm.isProxy())
                {
                    params.addElement(ha);
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + ageip + ":"
                            + aprot + " proxySwitch is true ");
                }
                ageip = pm.getIp();
                aprot = pm.getPort();
                ssltype=pm.getSsl();
            }
            XmlRpcClient rpcClient = new AgentXmlRpcClient(ageip, aprot,ssltype);
            long time01_begin = System.currentTimeMillis();
            String xml = (String) rpcClient.execute("IEAIAgent.getShellCmdOutput", params);
            long time01_end = System.currentTimeMillis();
            PrintTimeLog.printLong(_log, time01_begin, time01_end, "getShellCmdOutput|step1.1|requestid=" + requestId);
            return (ShellCmdOutput) XStreamUtil.xmlToJava(xml);
        } catch (UnMarshallingException e)
        {
            _log.info(e);
            throw new ServerException(ServerError.ERR_UNMARSHAL, e);
        } catch (RepositoryException e1)
        {
            _log.info(e1);
            throw new ServerException(ServerError.ERR_UNMARSHAL, e1);
        }
    }

    /**
     * 通过首页行数限制获取agent端最近的一部分数据
     * 
     * @param requestId
     * @param line
     * @return
     * @throws ServerException
     * @throws AgentCommunicationException
     * @throws XmlRpcException
     * @throws IOException
     */
    public static ShellCmdOutput getShellCmdOutputNLine ( String requestId, int line, int type )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {

        Vector params = new Vector();
        params.addElement(requestId);
        params.addElement(Integer.valueOf(line));

        RExecRequestWrapper requestWrapper = getReqWraper(requestId, type);

        ActivityElement actElem = requestWrapper.getActElem(type);

        if (null == actElem)
            return new ShellCmdOutput(null, 0, null, 0, ShellCmdOutput.STATE_FINISHED, -1, -1);
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, requestId, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
            }
            ha.put("level", pm.getLevel());
            ha.put("isProxy", pm.isProxy());
            if (pm.isProxy())
            {
                params.addElement(ha);
                _log.info("proxy- requestId:" + requestId + " proxySwitch is true ");
            } else
            {
                _log.info("agent- requestId:" + requestId + " proxySwitch is true ");
            }
        }
        XmlRpcClient rpcClient = getXmlRpcClient(actElem, requestId, type);

        String xml = (String) rpcClient.execute("IEAIAgent.getShellCmdOutputNLine", params);
        return (ShellCmdOutput) XStreamUtil.xmlToJava(xml);
    }

    /**
     * tn5250接口在首页弹出窗部分，用于实时展示agent端活动执行状态。
     * 
     * @param requestId
     * @return
     * @throws ServerException
     * @throws AgentCommunicationException
     * @throws XmlRpcException
     * @throws IOException
     */
    public static String getTN5250Screen ( String requestId, int type )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {

        Vector params = new Vector();
        params.addElement(requestId);

        RExecRequestWrapper requestWrapper = getReqWraper(requestId, type);

        ActivityElement actElem = requestWrapper.getActElem(type);
        if (null == actElem)
            return "Act is not running  or  finish!";
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            Hashtable ha = new Hashtable();
            ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, requestId, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
                params.addElement(ha);
                _log.info("proxy- requestId:" + requestId + " proxySwitch is true ");
            } else
            {
                _log.info("agent- requestId:" + requestId + " proxySwitch is true ");
            }
        }
        XmlRpcClient rpcClient = getXmlRpcClient(actElem, requestId, type);
        return (String) rpcClient.execute("IEAIAgent.getTN5250Screen", params);
    }

    public static String SendTn5250Com ( String requestId, String input, int type )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {

        Vector params = new Vector();
        params.addElement(requestId);
        params.addElement(input);

        RExecRequestWrapper requestWrapper = getReqWraper(requestId, type);

        ActivityElement actElem = requestWrapper.getActElem(type);
        long id = requestWrapper.getRexecRequest().getFlowId();

        DbOpScheduler scheduler = new DbOpScheduler();
        try
        {
            scheduler.Settn5250AlterServerStatusRecover(id, requestId, Constants.IEAI_IEAI_BASIC);
        } catch (RepositoryException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        if (null == actElem)
            return "Act is not running  or  finish!";
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();
            ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, requestId, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
                params.addElement(ha);
                _log.info("proxy- requestId:" + requestId + " proxySwitch is true ");
            } else
            {
                _log.info("agent- requestId:" + requestId + " proxySwitch is true ");
            }
        }
        XmlRpcClient rpcClient = getXmlRpcClient(actElem, requestId, type);
        long t1 = new Date().getTime();
        String xml = (String) rpcClient.execute("IEAIAgent.Tn5250Send", params);
        long t2 = new Date().getTime();
        _log.info("+++++++++++++++" + (t2 - t1));
        return xml;
    }

    public static void inputForShellCmd ( String requestId, String input, String remoteHost, String remotePort )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {
        Vector params = new Vector();
        params.addElement(requestId);
        params.addElement(input);

        XmlRpcClient rpcClient = null;
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        int ssltype= RpcSslType.getInstance().exec(remoteHost, Integer.parseInt(remotePort));
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();

            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, Integer.parseInt(remotePort),
                Constants.IEAI_SCRIPT_SERVICE);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
                params.addElement(ha);
                _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost + ":"
                        + remotePort + " proxySwitch is true ");
                remoteHost = pm.getIp();
                remotePort = String.valueOf(pm.getPort());
                ssltype=pm.getSsl();
            }
        }
        rpcClient = new AgentXmlRpcClient(remoteHost, Integer.parseInt(remotePort), ssltype);

        rpcClient.execute("IEAIAgent.inputForShellCmd", params);
    }

    /**
     * Input for remote ShellCmd activity.
     * 
     * @param requestId
     * @param input
     * @throws ServerException
     * @throws AgentCommunicationException
     * @throws XmlRpcException
     * @throws IOException
     */
    public static void inputForShellCmd ( String requestId, String input, UserInfo info, int type )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {
        Vector params = new Vector();
        params.addElement(requestId);
        params.addElement(input);

        RExecRequestWrapper requestWrapper = getReqWraper(requestId, type);
        ActivityElement actElem = requestWrapper.getActElem(type);

        if (null == actElem)
            return;
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();
            ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, requestId, type);
            if (pm.isProxy())
            {
                ha.put("proxyJson", pm.getProxyJsonData());
                ha.put("level", pm.getLevel());
                ha.put("isProxy", pm.isProxy());
                params.addElement(ha);
                _log.info("proxy- requestId:" + requestId + " proxySwitch is true ");
            } else
            {
                _log.info("agent- requestId:" + requestId + " proxySwitch is true ");
            }
        }
        XmlRpcClient rpcClient;
        rpcClient = getXmlRpcClientRead(actElem, requestId, type);
        rpcClient.execute("IEAIAgent.inputForShellCmd", params);
    }

    /**
     * <AUTHOR>
     * @des:执行agent升级钱的检测处理
     * @datea:2011-7-21
     * @param agentIP
     * @param agentPort
     * @throws ServerException
     * @throws AgentCommunicationException
     * @throws XmlRpcException
     * @throws IOException
     */
    public static String execAgentAuto ( String agentIP, int agentPort )
            throws ServerException, AgentCommunicationException, XmlRpcException, IOException
    {
        Vector params = new Vector();

        XmlRpcClient rpcClient;
        rpcClient = getXmlRpcClient(agentIP, agentPort);
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agentIP, agentPort, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                params.addElement(ha);
                _log.info("proxy- remoteIP:" + agentIP + ",remotePort:" + agentPort + " proxySwitch is true ");
            } else
            {
                _log.info("agent- remoteIP:" + agentIP + ",remotePort:" + agentPort + " proxySwitch is true ");
            }
        }
        String info = (String) rpcClient.execute("IEAIAgent.getVersion", params);
        _log.info("CheckInfo Successfully, and AgentIP:" + agentIP + ";AgentPort:" + agentPort);
        return info;
    }

    /**
     * <AUTHOR>
     * @des:发送升级指令
     * @datea:2011-7-25
     * @param agentIp
     * @param agentPort
     * @param serverIp
     * @param serverPort
     * @param renewTable
     * @param otherServer
     * @param isConsole
     * @throws AgentCommunicationException
     * @throws XmlRpcException
     * @throws IOException
     */
    public static void execAgentAutoSend ( String agentIp, int agentPort, String serverIp, int serverPort,
            Hashtable renewTable, Hashtable otherServer, int isConsole )
            throws AgentCommunicationException, XmlRpcException, IOException
    {
        Vector params = new Vector();
        params.addElement(serverIp);
        params.addElement(Integer.valueOf(serverPort));
        params.addElement(Integer.valueOf(isConsole));
        params.addElement((Hashtable) renewTable);

        XmlRpcClient rpcClient;
        rpcClient = getXmlRpcClient(agentIp, agentPort);
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(otherServer, agentIp, agentPort, Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                _log.info("proxy- remoteIP:" + agentIp + ",remotePort:" + agentPort + " proxySwitch is true ");
            } else
            {
                _log.info("agent- remoteIP:" + agentIp + ",remotePort:" + agentPort + " proxySwitch is true ");
            }
        }
        params.addElement(otherServer);
        rpcClient.execute("IEAIAgent.updateAgents", params);
        _log.info("SendExecAgentAutoInfo Successfully, and AgentIP:" + agentIp + ";AgentPort:" + agentPort);
    }

    public static RemoteActivityExecRequest getCmRExecRequest ( String requestId, String serverip, int serverPort,
            String cmsuscrinfo, String cmscriptinfo, String icmscripttype, String agentIp, int agentProt, String proId,
            int type ) throws ServerException
    {
        return getReqWraper(requestId, type).getCmRexecRequest(requestId, serverip, serverPort, cmsuscrinfo,
            cmscriptinfo, icmscripttype, agentIp, agentProt, proId);
    }

    /**
     * 
     * <li>Description:连接agent，用于判断agent是否存活</li>
     * 
     * <AUTHOR> 2015年10月26日
     * @param agentIp
     * @param port
     * @return
     * @throws RepositoryException return boolean
     */
    public static void connectAgent ( long computerId, String agentIp, int port ) throws RepositoryException
    {
        // 对监控agent连接时，采用重试机制避免网络闪断
        for (int j = 0; j < 3; j++)
        {
            try
            {
                Vector params = new Vector();
                boolean proxySwitch = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH, Environment.FALSE_BOOLEAN);
                int ssltype= RpcSslType.getInstance().exec(agentIp, port);
                if (proxySwitch)
                {
                    PerformDataProcessService ps = new PerformDataProcessService();
                    Hashtable ha = new Hashtable();

                    ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, port, Constants.IEAI_IEAI_BASIC);
                    if (pm.isProxy())
                    {
                        params.addElement(ha);
                        _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIp
                                + ":" + port + " proxySwitch is true ");
                    }
                    agentIp = pm.getIp();
                    port = pm.getPort();
                    ssltype=pm.getSsl();
                }
                XmlRpcClient rpcClient = null;
                // 获取连接类型
                rpcClient = new AgentXmlRpcClient(agentIp, port, ssltype);
                // 调用agent端对应的方法
                rpcClient.execute("IEAIAgent.getVersion", params);
                AgentCheckRepository.getInstance().setAgentState(computerId, 0);
                break;
            } catch (Exception e)
            {
                if (j != 2)
                {
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    // 设置agent的状态
                    AgentCheckRepository.getInstance().setAgentState(computerId, 1);
                }
            }
        }
    }

    public static void connectAgent ( String agentIp, int port ) throws RepositoryException
    {
        // 对监控agent连接时，采用重试机制避免网络闪断
        for (int j = 0; j < 3; j++)
        {
            try
            {
                // 设置传递的参数
                Vector params = new Vector();
                XmlRpcClient rpcClient = null;
                // 获取连接类型
                boolean proxySwitch = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH, Environment.FALSE_BOOLEAN);
                int ssltype= RpcSslType.getInstance().exec(agentIp, port);
                if (proxySwitch)
                {
                    PerformDataProcessService ps = new PerformDataProcessService();
                    Hashtable ha = new Hashtable();

                    ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, port, Constants.IEAI_IEAI_BASIC);
                    if (pm.isProxy())
                    {
                        params.addElement(ha);
                        _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIp
                                + ":" + port + " proxySwitch is true ");
                    }
                    agentIp = pm.getIp();
                    port = pm.getPort();
                    ssltype=pm.getSsl();
                }
                rpcClient = new AgentXmlRpcClient(agentIp, port, ssltype);

                // 调用agent端对应的方法
                rpcClient.execute("IEAIAgent.getVersion", params);
                AgentCheckRepository.getInstance().setAgentState(agentIp, port, 0);
                break;
            } catch (Exception e)
            {
                if (j != 2)
                {
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    // 设置agent的状态
                    AgentCheckRepository.getInstance().setAgentState(agentIp, port, 1);
                }
            }
        }
    }

    /**
     * <li>Description:保存步骤运行信息及异常信息</li>
     * 
     * <AUTHOR> 2016年1月13日
     * @param request
     * @param actElem
     * @param requestUUid
     * @param Output return void
     */
    private static void saveOutPut ( RemoteActivityExecRequest request, ActivityElement actElem, String requestUUid,
            Hashtable<String, String> Output )
    {
        LobStorer ls = new LobStorer();
        try
        {
            // 标准输出
            int status = request.getStatus();
            String lastLine = "";
            String ret = "";
            String compareSout = "";
            StringBuffer standardoutput = new StringBuffer();
            StringBuffer successoutput = new StringBuffer();//正确日志
            StringBuffer erroroutput = new StringBuffer();//错误日志
            if (null != Output.get(serr))
            {
                standardoutput.append((String) Output.get(serr));
                erroroutput.append((String) Output.get(serr));
            }
            if (null != Output.get(sout))
            {

                standardoutput.append((String) Output.get(sout));
                compareSout = (String) Output.get(sout);
                successoutput.append((String) Output.get(sout));
            }
            if (null != Output.get(lline))
            {

                lastLine = Output.get(lline);
            }

            if (null != Output.get("screen"))
            {
                standardoutput.append((String) Output.get("screen"));
            }
            if (null != Output.get("result") && Output.get("result") instanceof String)
            {
                standardoutput.append((String) Output.get("result"));
            }

            long timeout = request.getTimeout();

            Date endtime = new Date();
            if (StringUtils.isBlank(lastLine))
            {
                lastLine = "";
            }
            ConfigReader cfg = ConfigReader.getInstance();
            cfg.init();
            // 读取开关，只有当分区表开关开启时，对actoutput历史表进行存储
            String flagswitch = cfg.getProperties(Environment.CHECK_OKFILE_SWITCH,
                String.valueOf(Environment.CHECK_OKFILE_SWITCH_DEFAULT));
            if ("true".equals(flagswitch))
            {
                ls.saveActOutputClob(request.getFlowId(), actElem.getName(), request.getStartTime().getTime(), endtime,
                    standardoutput, lastLine, requestUUid, request);
            }

            if (ServerEnv.getServerEnv().isFlowNameError(request.getFlowName()))
            {
                String flowInsName = WorkflowManager.getInstance().getflowInsName(request.getFlowId(),
                    Constants.IEAI_IEAI_BASIC);
                ls.saveJxSimulationProcessOutPutClob(request.getProjectName(), request.getFlowId(),
                    request.getFlowName(), flowInsName, lastLine, Constants.IEAI_IEAI_BASIC);
            }
            ls.saveActOutputClob(request.getFlowId(), 0, actElem.getName(), status, ret, timeout,
                request.getStartTime().getTime(), endtime, standardoutput.toString(), lastLine,
                Constants.IEAI_IEAI_BASIC,successoutput.toString(),erroroutput.toString());
            /**南京银行增加文件比对返回保存结果 2024年10月14日15:15:40 张桂彬*/
            ResGrpStateManager.getInstance().saveFileCompleResult(request.getFlowId(),standardoutput.toString(),Constants.IEAI_SUS);
            int lastlineBack = 2;
            try
            {
                lastlineBack = Integer.parseInt(lastLine);
            } catch (Exception e)
            {
                lastlineBack = 2;
            }
            if ("ExcelActExecModelDR".equals(request.getProjectName()) && "ActExec_Check".equals(request.getFlowName()))
            {
                ResGrpStateManager.getInstance().updateResState(request.getFlowId(), lastlineBack,
                    Constants.IEAI_EMERGENCY_SWITCH);
                new UserCertificateManage().updateUserAgentState(request.getFlowId(), lastlineBack);
            }
            if ("CompareTemplate".equals(request.getProjectName())
                    && "ActExec_GetShell_Content".equals(request.getFlowName()))
            {
                CompareBaseManage manage = new CompareBaseManage();
                try
                {
                    String str = "";
                    if (null == compareSout || "".equals(compareSout))
                    {
                        str = "获取失败,可能Agent连接失败，可能文件或脚本路径不正确或文件无内容！";
                    } else
                    {
                        str = compareSout;
                    }
                    manage.updateCompareBaseContext(request.getFlowId(), str, Constants.IEAI_COMPARE);
                } catch (Exception e)
                {
                    _log.error("read config is error!", e);
                }
            }
            if ("CMDBTemplate".equals(request.getProjectName()) && "ActExec_Cmdb".equals(request.getFlowName()))
            {
                CmdbInfoManage manage = new CmdbInfoManage();
                try
                {
                    // 解析格式开关 默认false：ideal方式 true:json方式
                    boolean analysiseSwitch = ServerEnv.getInstance()
                            .getBooleanConfig(Environment.AGENT_MESSAGE_ANALYSISE_SWITCH, Environment.FALSE_BOOLEAN);
                    if (analysiseSwitch)
                    {
                        manage.getCmdbMessageFromJson(standardoutput.toString());
                    } else
                    {
                        String scriptName = Environment.getInstance().getCmdbDiscoverRelationScriptName();
                        boolean isTypeRelation = CastUtil.castString(request.getInput().get("command")).contains(scriptName);
                        manage.getCmdbMessage(standardoutput.toString(),isTypeRelation);
                    }

                } catch (Exception e)
                {
                    _log.error("get cmdb message is error!", e);
                }
            }
            if ("CMDBTemplate".equals(request.getProjectName()) && "ActExec_Cmdb_SSH".equals(request.getFlowName()))
            {
                CmdbInfoManage manage = new CmdbInfoManage();
                try
                {
                    manage.parseSSHSelfFoundInfo(request, standardoutput.toString());
                } catch (Exception e)
                {
                    _log.error("parse ssh found is error!", e);
                }
            }
            if ("CompareTemplate".equals(request.getProjectName())
                    && "ActExec_Start_Socket".equals(request.getFlowName()))
            {
                new SyncSystemManage().updateSocketFlowResult(request, Output);
            }
            new SyncResultManage().updateSyncDirResult(request, Output);

        } catch (Exception e)
        {
            _log.error("ActivityRExecHelper.saveActOutputClob error:" + e + "\n");
        }
    }

    /**
     * 
     * @Title: parseOutput   
     * @Description: TODO(这里用一句话描述这个方法的作用)   
     * @param outRet
     * @param rule
     * @return      
     * @author: jinchao_duan 
     * @date:   2019年1月24日 上午11:20:43
     */
    public static String parseOutput ( String outRet, CommandRuleModel rule )
    {
        String parseResult = null;
        int ruleType = rule.getIruletype();
        String ruleText = rule.getIruletext();

        String[] aOut = outRet.split("\\\n");
        String[] bOut = new String[aOut.length - 1];
        System.arraycopy(aOut, 1, bOut, 0, bOut.length);

        String out = StringUtils.join(bOut, '\n');

        if (ruleType == 1)
        {
            // 正则
            Pattern p = Pattern.compile(ruleText);
            Matcher m = p.matcher(out);
            while (m.find())
            {
                parseResult = m.group(1);
            }

        } else if (ruleType == 0)
        {
            // 截取
            int lineNum = Integer.valueOf(out.split(":")[0]);
            int beginIndex = Integer.valueOf(out.split(":")[1].split("-")[0]);
            int endIndex = Integer.valueOf(out.split(":")[1].split("-")[2]);
            String[] lines = out.split("\\\\n");

            parseResult = lines[lineNum - 1].substring(beginIndex, endIndex);
        }

        return parseResult;
    }

    public static CmdbInfoModel getAttributeData ( Connection conn, String data )
    {
        String cmdbPa = Environment.getInstance().getSysConfig(Environment.CMDB_STRING_PATTERN);
        if (cmdbPa == null)
        {
            cmdbPa = Environment.CMDB_STRING_PATTERN_DEFAULT;
        }
        String format = "params=" + cmdbPa;
        CmdbInfoManage manage = new CmdbInfoManage();
        CmdbInfoModel model = new CmdbInfoModel();
        try
        {
            String itypeName = getIValue(data, "itypename", format);
            String xml = "<attributeModel></attributeModel>";
            Document document = DocumentHelper.parseText(xml);
            Element root = document.getRootElement();
            if (!"".equals(itypeName) && itypeName != null)
            {
                List<String> list = manage.getAttributeNameByN(conn, itypeName);
                if (list.size() > 0)
                {
                    StringBuffer p = new StringBuffer();
                    for (int i = 0; i < list.size(); i++)
                    {
                        String attibute = list.get(i);
                        String param = getIValue(data, attibute, format);
                        long itypeid = manage.getTypeIid(conn, itypeName);
                        String icolumtype = manage.getIcolumtype(itypeid, attibute, conn);
                        if ("表格".equals(icolumtype))
                        {
                            List<String> sList = manage.getAttributeG(conn, attibute, itypeid);
                            Element element = root.addElement(attibute);
                            for (int m = 0; m < sList.size(); m++)
                            {
                                Element elementSon = element.addElement("attributeSonModel");
                                String sonAttribute = sList.get(m);
                                Element elementSonKey = elementSon.addElement(sonAttribute);
                                String sonValue = getIValue(param, sonAttribute, "\\[(.*?)\\]");
                                elementSonKey.setText(sonValue);
                            }
                        } else
                        {
                            Element element = root.addElement(attibute);
                            element.setText(param);
                        }
                    }
                    model.setIcontent(document.asXML());
                    model.setIid(0);
                    model.setItypename(itypeName);
                    model.setItypeid(manage.getTypeIid(conn, itypeName));
                }
            }
        } catch (Exception e)
        {
            e.printStackTrace();
            _log.error("ActivityRExecHelper.getAttributeData has an error:" + e.getMessage());
        }
        return model;
    }

    /**
     * 
     * <li>Description:查看类型模块属性里面order 最小的属性</li> 
     * <AUTHOR>
     * 2018年12月26日 
     * @param conn
     * @param data
     * @return
     * return long
     */
    public static List<Long> checkAttributeData ( Connection conn, String data )
    {
        String cmdbPa = Environment.getInstance().getSysConfig(Environment.CMDB_STRING_PATTERN);
        if (cmdbPa == null)
        {
            cmdbPa = Environment.CMDB_STRING_PATTERN_DEFAULT;
        }
        String format = "params=" + cmdbPa;
        CmdbInfoManage manage = new CmdbInfoManage();
        Map<String, Object> rest = new HashMap<String, Object>();
        CmdbInfoModel model = new CmdbInfoModel();
        List<Long> reList = new ArrayList<Long>();
        try
        {
            String itypeName = getIValue(data, "itypename", format);
            String xml = "<attributeModel></attributeModel>";
            Document document = DocumentHelper.parseText(xml);
            Element root = document.getRootElement();
            if (!"".equals(itypeName) && itypeName != null)
            {
                List<String> list = manage.getColumnName(conn, itypeName);
                List<Object> contentList = manage.getCmdbInfoContent(conn, itypeName);
                if (list.size() > 0)
                {
                    StringBuffer p = new StringBuffer();
                    for (int i = 0; i < list.size(); i++)
                    {
                        String attibute = list.get(i);
                        String param = getIValue(data, attibute, format);
                        List<Long> liid = checkAttributeValue(attibute, param, contentList);
                        if (liid.size() > 0)
                        {
                            for (int m = 0; m < liid.size(); m++)
                            {
                                long iid = liid.get(m);
                                if (!reList.contains(iid))
                                {
                                    reList.add(iid);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
            _log.error("ActivityRExecHelper.getAttributeData has an error:" + e.getMessage());
        }
        return reList;
    }

    /**
     * 
     * <li>Description:检查属性值是否在icontent中存在</li> 
     * <AUTHOR>
     * 2018年12月26日 
     * @param attributeKey
     * @param keyValue
     * @param contentValue
     * @return
     * return long
     */
    private static List<Long> checkAttributeValue ( String attributeKey, String keyValue, List<Object> contentValue )
    {
        List list = new ArrayList();
        List<Long> rList = new ArrayList<Long>();
        try
        {
            if (contentValue.size() > 0)
            {
                for (int i = 0; i < contentValue.size(); i++)
                {
                    Map<String, Object> map = (Map<String, Object>) contentValue.get(i);
                    String icontent = map.get("icontent").toString();
                    Document document = DocumentHelper.parseText(icontent);
                    Element element = document.getRootElement();
                    Iterator<Element> iterator = element.elementIterator();
                    while (iterator.hasNext())
                    {
                        Element e = iterator.next();
                        String key = e.getName();
                        String value = e.getText();
                        if (attributeKey.equals(key) && value.equals(keyValue))
                        {
                            if (!rList.contains(Long.valueOf(map.get("iid").toString())))
                            {
                                rList.add(Long.valueOf(map.get("iid").toString()));
                            }
                        }
                    }
                }
            }
        } catch (Exception e)
        {
            _log.error("ActivityRExecHelper.getAttributeData has an error:", e);
        }
        return rList;
    }

    public static long getparInfoIid ( Connection conn, String data )
    {
        long iId = -1;
        CmdbInfoManage manage = new CmdbInfoManage();
        String cmdbPa = Environment.getInstance().getSysConfig(Environment.CMDB_STRING_PATTERN);
        if (cmdbPa == null)
        {
            cmdbPa = Environment.CMDB_STRING_PATTERN_DEFAULT;
        }
        String format = "params=" + cmdbPa;
        String parTypeName = getIValue(data, "iparenttypename", format);
        try
        {
            if (!"".equals(parTypeName))
            {
                List<String> list = manage.getAttributeNameByN(conn, parTypeName);
                if (list.size() > 0)
                {
                    for (int i = 0; i < list.size(); i++)
                    {
                        String attribute = list.get(i);
                        String parAttributeKey = "parent_" + list.get(i);
                        String parAttributeValue = getIValue(data, parAttributeKey, format);
                        if (!"".equals(parAttributeValue))
                        {
                            List reList = manage.getIcontentMessage(conn, parTypeName);
                            for (int m = 0; m < reList.size(); m++)
                            {
                                Map map = (Map) reList.get(m);
                                String icontent = (String) map.get("icontent");
                                Document document = DocumentHelper.parseText(icontent);
                                Element root = document.getRootElement();
                                String value = root.elementText(attribute);
                                if (parAttributeValue.equals(value))
                                {
                                    iId = Long.valueOf(map.get("iid").toString());
                                }
                            }
                        }
                    }

                }
            }
        } catch (Exception e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return iId;
    }

    public static String getIValue ( String data, String key, String format )
    {
        String npattern = format.replaceAll("params", key);
        Pattern pattern = Pattern.compile(npattern);
        Matcher matcher = pattern.matcher(data);
        String value = "";
        if (matcher.find())
        {
            value = matcher.group(1);
        }
        return value;
    }

    public static void updateRunStepState ( long flowId, String actName, String results, int flag )
            throws ServerException
    {
        try
        {
            EngineRepository.getInstance().updateRunStepState(flowId, actName, results, flag);
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        }
    }

    public static String getPubTypeInfo ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, Connection con,
            Map paramMap ) throws ServerException, RepositoryException
    {
        Object xml = getXml(serverIp, adpDefUuid, requestid, serverPort, agentIp, agentPort, sysType, script, pkgInfo,
            paramMap);
        String lastLine = "";
        if ("true".equals(xml.toString()))
        {
            Map pkgs = null;
            int n = 0;
            while (true)
            {
                init();
                pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                if (null != pkgs && pkgs.size() > 0)
                {
                    String state = (String) pkgs.get("state");
                    if ("1".equals(state))
                    {
                        _log.info("脚本执行异常,不能返回正确信息!");
                        throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
                    }
                    lastLine = (String) pkgs.get(sout);
                    break;
                } else
                {
                    if (n > waitNum)
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                    }
                    try
                    {
                        Thread.sleep(2000);
                    } catch (InterruptedException e)
                    {
                        Thread.currentThread().interrupt();
                    }
                }
                n++;
            }
        }

        return lastLine;
    }

    /**
     * <li>Description:pub类型返回值，将会以一种复合的形式进行返回，所以用map进行装载</li> 
     * <AUTHOR>
     * 2018年12月10日 
     * @param agentIp
     * @param agentPort
     * @param script
     * @param pkgInfo
     * @param adpDefUuid
     * @param requestid
     * @param serverIp
     * @param serverPort
     * @param sysType
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return Map<String,String>
     */
    public static Map<String, String> getPubTypeInfoMap ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, Connection con,
            Map paramMap ) throws ServerException, RepositoryException
    {
        Map<String, String> rsMap = new LinkedHashMap<String, String>();
        long time1_begin = System.currentTimeMillis();
        Object xml = getXml(serverIp, adpDefUuid, requestid, serverPort, agentIp, agentPort, sysType, script, pkgInfo,
            paramMap);
        long time1_end = System.currentTimeMillis();
        PrintTimeLog.printLong(_log, time1_begin, time1_end, "getPubTypeInfoMap_1" + "|requestid=" + requestid);
        String lastLine = "";
        if ("true".equals(xml.toString()))
        {
            Map pkgs = null;
            int n = 0;
            int readLineCnt = 2;
            if (Environment.getInstance().getBooleanConfig(PersonalityEnv.PF_SUS_DANGER_SQL_CHECK_SWITCH, false))
            {
                readLineCnt = 3;
            }
            while (true)
            {
                long time2_begin = System.currentTimeMillis();
                init();
                long time2_end = System.currentTimeMillis();
                PrintTimeLog.printLong(_log, time2_begin, time2_end, "getPubTypeInfoMap_2" + "|requestid=" + requestid);
                long time3_begin = System.currentTimeMillis();
                pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                long time3_end = System.currentTimeMillis();
                PrintTimeLog.printLong(_log, time3_begin, time3_end, "getPubTypeInfoMap_3" + "|requestid=" + requestid);
                if (null != pkgs && pkgs.size() > 0)
                {
                    String state = (String) pkgs.get("state");
                    rsMap.put("state", state);
                    long time4_begin = System.currentTimeMillis();
                    lastLine = (String) pkgs.get(sout);
                    long time4_end = System.currentTimeMillis();
                    PrintTimeLog.printLong(_log, time4_begin, time4_end,
                        "getPubTypeInfoMap_4" + "|requestid=" + requestid);
                    lastLine = cleanStr4ProcessUsing(lastLine);
                    rsMap.put("lastLine", lastLine);
                    rsMap.put("cfgFileChange", "");
                    Object objRealStdout = pkgs.get("realStdOut");
                    if (null != objRealStdout && objRealStdout instanceof String)
                    {
                        rsMap.put("realStdOut", (String) objRealStdout);
                    }
                    try
                    {
                        long time5_begin = System.currentTimeMillis();
                        String strStdout = (String) pkgs.get(realStdOut);
                        if ("1".equals(state))// 当pub脚本执行的进程发生过异常，导致erroStdout流中有值时，仍旧往realStdout中放入值。保证后续能正常运行cfgFileChange
                        {
                            strStdout = lastLine;
                        }
                        long time5_end = System.currentTimeMillis();
                        PrintTimeLog.printLong(_log, time5_begin, time5_end,
                            "getPubTypeInfoMap_5" + "|requestid=" + requestid);
                        _log.info("在pub服务器上执行Analysis_pakge(解包操作)返回结果:" + strStdout);
                        long time6_begin = System.currentTimeMillis();
                        String stdArr[] = readFileNLine(strStdout, readLineCnt);
                        long time6_end = System.currentTimeMillis();
                        PrintTimeLog.printLong(_log, time6_begin, time6_end,
                            "getPubTypeInfoMap_6" + "|requestid=" + requestid);
                        if (null != stdArr && stdArr.length == readLineCnt)
                        {
                            rsMap.put("cfgFileChange", stdArr[1]);
                        }
                        if (Environment.getInstance().getBooleanConfig(PersonalityEnv.PF_SUS_DANGER_SQL_CHECK_SWITCH,
                            false) && stdArr.length >= 3)
                        {
                            rsMap.put("dangerSql", stdArr[2]);
                        }
                    } catch (Exception e)
                    {
                        _log.error(e.toString());
                    }

                    break;
                } else
                {
                    if (n > waitNum)
                    {
                        _log.error("变更pub执行"+waitNum+"次后，每次等待2秒，但文件服务器上的Agent始终无返回，启动环节失败在pub文服不返回值");
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                    }
                    try
                    {
                        long time7_begin = System.currentTimeMillis();
                        Thread.sleep(2000);
                        long time7_end = System.currentTimeMillis();
                        PrintTimeLog.printLong(_log, time7_begin, time7_end,
                            "getPubTypeInfoMap_7" + "|requestid=" + requestid);
                    } catch (InterruptedException e)
                    {
                        Thread.currentThread().interrupt();
                    }
                }
                n++;
            }
        }

        return rsMap;
    }

    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2019年11月21日 
     * return void
     */
    public static String cleanStr4ProcessUsing ( String stdout )
    {
        String rs = stdout;
        String beginMark = "<b>标准输出：</b><br/>";
        String endMark = "<br/><b>错误输出：</b><br/>";
        if (null != stdout)
        {
            int beginIndex = stdout.indexOf(beginMark);
            int endIndex = stdout.indexOf(endMark);
            if ((-1 != beginIndex) && (-1 != endIndex))
            {
                beginIndex = beginIndex + beginMark.length();
                rs = stdout.substring(beginIndex, endIndex);
            }
        }
        return rs;
    }

    public static void main ( String[] args )
    {
        String stdout = "<b>标准输出：</b><br/>" + "aabbccddd" + "<br/><b>错误输出：</b><br/>";
        String rs = ActivityRExecHelper.cleanStr4ProcessUsing(stdout);
        System.out.println(rs);
    }

    public static String[] readFileNLine ( String content, int line )
    {
        String[] rsArr = {};
        BufferedReader in = null;
        StringReader sr = null;
        try
        {
            sr = new StringReader(content);
            in = new BufferedReader(sr);

            StringBuffer buffer = new StringBuffer();
            int i = 0;// 用于保存本次读取的临时文件总行数

            String strLine = null;
            Queue queue = new Queue();
            while (in.ready())
            {
                strLine = in.readLine();
                if (null == strLine)
                {
                    break;
                }
                // end of the file
                if (i < line)
                {
                    queue.put(strLine);
                } else
                {
                    if (!queue.empty())
                    {
                        queue.get();
                    }
                    queue.put(strLine);
                }
                i++;
            }

            String temp = "";
            rsArr = new String[line];
            for (int m = 0; m < line && !queue.empty(); m++)
            {
                temp = queue.get().toString();
                rsArr[line - m - 1] = temp; // 倒序放入数据
            }
            return rsArr;
        } catch (IOException e)
        {
            _log.info(e);
            return rsArr;
        } catch (Exception e1)
        {
            _log.info("remove list error");
            return rsArr;
        } finally
        {
            if (null != in)
            {
                try
                {
                    in.close();
                } catch (IOException e)
                {
                    _log.info(e);
                }
            }
            if (null != sr)
            {
                try
                {
                    sr.close();
                } catch (Exception e)
                {
                    _log.info(e);
                }
            }
        }

    }

    public static void init ()
    {
        ConfigReader cr = ConfigReader.getInstance();
        try
        {
            cr.init();
            waitNum = Long.parseLong(cr.getProperties("waitNum", String.valueOf(waitNum)));

        } catch (Exception e)
        {
            waitNum = 900;
        }
    }

    public static long waitNum = 900;

    /**
     * <li>Description:变更管理_图形化_百信_获得 真正的标准输出，而不是lastline</li> 
     * <AUTHOR>
     * 2017-6-1 
     * @param agentIp
     * @param agentPort
     * @param script
     * @param pkgInfo
     * @param adpDefUuid
     * @param requestid
     * @param serverIp
     * @param serverPort
     * @param sysType
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return String
     */
    public static String getPubTypeInfo_Retn_StdOutGraph ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, Connection con,
            Map paramMap ) throws ServerException, RepositoryException
    {
        Object xml = getXml(serverIp, adpDefUuid, requestid, serverPort, agentIp, agentPort, sysType, script, pkgInfo,
            paramMap);
        String stdout = "";
        if ("true".equals(xml.toString()))
        {
            Map pkgs = null;
            int n = 0;
            init();
            while (true)
            {
                pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                if (null != pkgs && pkgs.size() > 0)
                {
                    try
                    {
                        stdout = (String) pkgs.get("stdout");
                        ParseJson.JSON2List(stdout);
                    } catch (Exception e)
                    {
                        stdout = (String) pkgs.get("realStdOut");
                        stdout = cleanStr4ProcessUsing(stdout);
                    }
                    break;
                } else
                {
                    if (n > waitNum)
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                    }
                    try
                    {
                        Thread.sleep(2000);
                    } catch (InterruptedException e)
                    {
                        Thread.currentThread().interrupt();
                    }
                }
                n++;
            }
        }

        return stdout;
    }

    public static Object getXml(String serverIp, String adpDefUuid, String requestid, String serverPort,
                                String agentIp, int agentPort, String sysType, String script, String pkgInfo, Map paramMap)
            throws ServerException
    {
        Object xml = "false";
        try
        {
            Vector params = new Vector();
            String serIp = Environment.getInstance().getSysConfig(Environment.SERVER_IP_LIST, serverIp);
            params.addElement(requestid);
            params.addElement(serIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = creatTable(adpDefUuid, requestid, serIp, serverPort, agentIp, agentPort, sysType, script);
            params.addElement(table);
            EngineRepository.getInstance().insertintoAutosingleState(requestid, table, script, pkgInfo);
            XmlRpcClient rpcClient;
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            boolean pfMuchPubSwitch = ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_SUS_MUCH_PUB_SWITCH, false);
            if (pfMuchPubSwitch && proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonDataForMuchPub(table, agentIp, agentPort, Constants.IEAI_SUS);
                if (pm.isProxy())
                {
                    _log.info("-----------------------------------");
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + agentIp
                            + ":" + agentPort + " proxySwitch is true ");
                    agentIp = pm.getIp();
                    agentPort = pm.getPort();
                }
            }
            rpcClient = getXmlRpcClient(agentIp, agentPort, Constants.IEAI_SUS);
            _log.info("pub_agent:" + agentIp + ":" + agentPort);
            try
            {
                _log.info("pub command:" + script);
                _log.info("pub command:" + ((Hashtable) table.get("input")).get("command"));
            } catch (Exception e)
            {
                _log.info("pub command error");
            }
            for (int i = 0; i <= 2; i++)
            {
                try
                {
                    xml = rpcClient.execute(ieaiExe, params);
                    break;
                } catch (Exception e)
                {
                    if (i != 2)
                    {
                        _log.error("It will retry. pubType", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            Thread.currentThread().interrupt();
                        }
                    } else
                    {
                        if (null != e.getMessage())
                        {
                            // if ("Connection refused: connect".equals(e.getMessage()) ||
                            // -1!="ConnectionTimeoutException".indexOf(e.getMessage()))
                            if (e.getMessage().contains("Connection refused")
                                    || -1 != "ConnectionTimeoutException".indexOf(e.getMessage())
                                    || e.getMessage().contains("拒绝连接")
                                    || e.getMessage().contains("java.net.ConnectException"))
                            {
                                String std = "";
                                String error = "Agent连接异常，请检查Agent是否正常运行！" + e.toString();
                                String rs = "<b>标准输出：</b><br/>" + std + "<br/>";
                                rs += "<b>错误输出：</b><br/>" + error;
                                AgentServiceImpl.getInstance().resultForAuto(requestid, rs, 1);
                                // 记录告警信息
                                IeaiWarnModel iwm = new IeaiWarnModel();
                                iwm.setImodulecode("SUS");
                                iwm.setItypecode("SUS_PUBAGENTREFUSE");
                                iwm.setIlevelcode("five");
                                iwm.setIip(agentIp);
                                iwm.setIwarnmsg("应用变更PubAgent无法正常连接异常");
                                iwm.setIhappentime(new Date());
                                iwm.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
                                if (paramMap != null)
                                {
                                    String ibusinessname = (String) paramMap.get("ibusinessname");
                                    String iruninsname = (String) paramMap.get("iruninsname");
                                    iwm.setIbusinessName(ibusinessname == null ? "" : ibusinessname);
                                    iwm.setIsystemname(ibusinessname == null ? "" : ibusinessname);
                                    iwm.setIswitchName(iruninsname == null ? "" : iruninsname);
                                }
                                iwm.setIsonStepName("PUB步骤");
                                WarningInterfaceUtilsSus.callWarning(iwm);
                                // WarningInterfaceUtilsSus.callWarning("SUS", "SUS_PUBAGENTREFUSE",
                                // "five", agentIp, "应用变更PubAgent无法正常连接异常", new Date());
                                throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE,
                                        "Agent连接异常，请检查Agent是否正常运行！");
                            }
                        }
                        String std = "";
                        String error = e.toString();
                        String rs = "<b>标准输出：</b><br/>" + std + "<br/>";
                        rs += "<b>错误输出：</b><br/>" + error;
                        AgentServiceImpl.getInstance().resultForAuto(requestid, rs, 1);
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (RepositoryException ex)
        {
            throw new ServerException(ex.getServerError().getErrCode());
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }
        return xml;
    }

    private static Hashtable creatTable ( String adpDefUuid, String requestid, String serIp, String serverPort,
            String agentIp, int agentPort, String sysType, String script )
    {
        Hashtable table = new Hashtable();
        String scmd = "ShellCmd";
        table.put("adaptorDefUUID", adpDefUuid);
        table.put("_scopeId", "162865");// ?
        table.put("adaptorConfig", 1);// shellcmd
        table.put("adaptorDefName", "shellcmd");// shellcmd
        table.put("Id", requestid);
        table.put("serverHost", serIp);
        table.put("serverPort", Integer.parseInt(serverPort));
        table.put("_actStateDataVersion", -1);// ?
        table.put("timeout", "0");// ?
        table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
        table.put("agentHost", agentIp);
        table.put("agentPort", agentPort);
        table.put("actId", scmd);// 是否是固定值ShellCmd?
        table.put("adaptorDefName", "shellcmd");// shellcmd
        table.put("actName", scmd); // 默认值
        table.put("status", 2);// ? 默认值
        table.put("levelOfWeight", "1");// ? 默认值
        table.put("Id", requestid);
        table.put("flowId", "-1");// 使用taskName可否?可以
        table.put("projectName", "shell_test");// ?
        table.put("flowName", scmd);// 使用taskName可否?可以
        table.put("flowPoolNum", "0");// ?默认值
        table.put("isSafeFirst", true); // ?默认值
        table.put("levelOfPRI", "5");// ?默认值
        Hashtable paramTab = new Hashtable();
        if ("1".equals(sysType))
        {
            String scripta = "cmd.exe /c " + script;
            paramTab.put("command", "cmd.exe /c " + scripta);
        } else if ("2".equals(sysType))
        {
            String scripta = "sh " + script;
            paramTab.put("command", scripta);
        }
        table.put("input", paramTab);
        return table;
    }

    /**
     * 
     * @Title: exec   
     * @Description: 远程发送线程执行活动主入口   
     * @param execAct
     * @param reqId
     * @param agentGroupid
     * @param agentIp
     * @param agentPort
     * @param proType
     * @param groupName
     * @return
     * @throws ActivityException
     * @throws ServerException      
     * @author: licheng_zhao
     * @date:   2017年11月23日 上午8:17:52
     */
    public static int exec ( ExecAct execAct, String reqId, long agentGroupid, String agentIp, int agentPort,
            long proType, String groupName ) throws ActivityException, ServerException
    {
        String[] info = null;
        RExecRequestWrapper requestWrapper = getReqWraper(reqId, Constants.IEAI_IEAI_BASIC);
        GetServerAct act = new GetServerAct();

        Vector params = new Vector();
        Vector paramsExp = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        String agenthost = "";
        int agentport = 0;
        Hashtable ha = (Hashtable) act.getActRemAct(requestWrapper.getRexecRequest().getId());
        Agent agent = new Agent();
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                params.addElement(request.getId());
                params.addElement(request.getServerHost());
                params.addElement(Integer.valueOf(request.getServerPort()));
                // params.addElement(ha);

            } else
            {
                RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI_BASIC));
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByName(actElem.getRemoteAgent());
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
                    try
                    {
                        info = EngineRepositotyJdbc.getInstance().saveServerIpPortInfo(execAct.getFlowId(), reqId);
                    } catch (RepositoryException e1)
                    {
                        throw new ActivityException(e1);
                    }
                    if ("0".equals(info[2]))
                    {
                        return Constants.QUEUEUP;
                    }
                    params.addElement(request.getId());
                    params.addElement(info[0]);
                    params.addElement(Integer.valueOf(info[1]));
                    // params.addElement(ha);
                } else
                {
                    params.addElement(request.getId());
                    params.addElement(request.getServerHost());
                    params.addElement(Integer.valueOf(request.getServerPort()));
                    // params.addElement(ha);
                }
            }

            Object[] object = getXmlRpcClientWithAgent(actElem, reqId);
            if (Constants.QUEUEUP == (((Integer) object[0]).intValue()))
            {
                return Constants.QUEUEUP;
            }

            agenthost = (String) object[2];
            agentport = ((Integer) object[3]).intValue();
            agent.setIagentip(agenthost);
            agent.setIagentport(Long.valueOf(agentport));

            /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
            try
            {

                int dbType = Integer.parseInt(String.valueOf(proType));
                EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(), agenthost,
                    agentPort, actElem, requestWrapper.getRexecRequest(), execAct, dbType);

            } catch (Exception e)
            {
                _log.info("Error when get project type!", e);
            }
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, agenthost, agentport, Constants.IEAI_IEAI_BASIC);
            }
            paramsExp = params;
            params.addElement(ha);

            /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/
            for (int i = 0; i < 10; i++)
            {
                //并发数是否已增加标识（用于异常回退）
                int agentRunActNumAdd = 0;
                try
                {
                    long start = System.currentTimeMillis();
                    if (requestWrapper.getRexecRequest().getAdaptorDefName().equals("unloaddata")
                            || requestWrapper.getRexecRequest().getAdaptorDefName().equals("unloadnewdataadaptor"))
                    {
                        TaskDb taskDb = ProjectManagerOds.getInstance().getDbServerInfo(agenthost,
                            Constants.IEAI_IEAI_BASIC);
                        String password = "";
                        try
                        {
                            password = DesUtils.getInstance().decrypt(taskDb.getDbpasswd());
                        } catch (Exception e)
                        {
                            _log.info("decrypt is error:" + e);
                        }
                        DefaultConfig defaultConfig = (DefaultConfig) requestWrapper.getRexecRequest()
                                .getAdaptorConfig();
                        defaultConfig.put("dbuser", taskDb.getDbuser());
                        defaultConfig.put("dbpassword", password);
                        requestWrapper.getRexecRequest().setAdaptorConfig(defaultConfig);

                    } else if (requestWrapper.getRexecRequest().getAdaptorDefName().equals("loaddata")
                            || requestWrapper.getRexecRequest().getAdaptorDefName().equals("loaddatanewadpter"))
                    {
                        TaskDb taskDb = ProjectManagerOds.getInstance().getDbServerInfo(agenthost,
                            Constants.IEAI_IEAI_BASIC);
                        String password = "";
                        try
                        {
                            password = DesUtils.getInstance().decrypt(taskDb.getDbpasswd());
                        } catch (Exception e)
                        {
                            _log.info("decrypt is error:" + e);
                        }
                        DefaultConfig defaultConfig = (DefaultConfig) requestWrapper.getRexecRequest()
                                .getAdaptorConfig();
                        defaultConfig.put("dbuser", taskDb.getDbuser());
                        defaultConfig.put("dbpassword", password);
                        requestWrapper.getRexecRequest().setAdaptorConfig(defaultConfig);
                    } else if (requestWrapper.getRexecRequest().getAdaptorDefName().equals("dbproadaptor")
                            || requestWrapper.getRexecRequest().getAdaptorDefName().equals("dbpronewadaptor"))
                    {
                        TaskDb taskDb = ProjectManagerOds.getInstance().getDbServerInfo(agenthost,
                            Constants.IEAI_IEAI_BASIC);
                        String password = "";
                        try
                        {
                            password = DesUtils.getInstance().decrypt(taskDb.getDbpasswd());
                        } catch (Exception e)
                        {
                            _log.info("decrypt is error:" + e);
                        }
                        DefaultConfig defaultConfig = (DefaultConfig) requestWrapper.getRexecRequest()
                                .getAdaptorConfig();
                        defaultConfig.put("dbuser", taskDb.getDbuser());
                        defaultConfig.put("dbpassword", password);
                        requestWrapper.getRexecRequest().setAdaptorConfig(defaultConfig);
                    }
                    _log.info("执行保存Agent实时并发数方法3840: flowId:agentip:agentport:weight:" +  execAct.getFlowId() + ":" + agenthost + ":"
                            + agentport + ":" + actElem.getLevelOfWeight());
                    agentRunActNumAdd = EngineRepositotyJdbc.getInstance().increaseAgentGroupRunActNum(reqId, agenthost, agentport,
                            actElem.getLevelOfWeight(), agentGroupid, actElem, execAct, proType, groupName);
                    //判断并发数是否超限，执行远程调用 --add by sunxf on 2023.4.3
                    if(agentRunActNumAdd > 0) {
                        EngineRepositotyJdbc.getInstance().saveSentHostInfo(agenthost, agentport, reqId);
                        ((XmlRpcClient) object[1]).execute(ieaiExe, params);
                    } else {
                        return Constants.QUEUEUP;
                    }
//                    EngineRepositotyJdbc.getInstance().saveSentHostInfo(agenthost, agentport, reqId);
//                    ((XmlRpcClient) object[1]).execute(ieaiExe, params);
//                    if (actElem.isRemoteExec())
//                    {
//                        if (actElem.getRemoteAgent() != null)
//                        {
//                            IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
//                                    .getSharedResInsByName(actElem.getRemoteAgent());
//                            if (null != agentResource && agentResource.isRemoteGroup())
//                            {
//                                _log.info("执行保存Agent实时并发数方法3840: flowId:agentip:agentport:weight:" +  execAct.getFlowId() + ":" + agenthost + ":"
//                                        + agentport + ":" + actElem.getLevelOfWeight());
//                                EngineRepositotyJdbc.getInstance().saveAgentRunActNumAdd(reqId, agenthost, agentport,
//                                        actElem.getLevelOfWeight(), agentGroupid, actElem, execAct, proType, groupName);
//                            }
//                        }
//                    }
                    WorkflowManager.getInstance().updateActSendTime(execAct.getActName(),execAct.getFlowId());

                    if (SystemConfig.isElapsedTime())
                        _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                                + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: "
                                + (System.currentTimeMillis() - start) + " 毫秒");

                    _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                            + execAct.getActName());
                    return Constants.END;
                } catch (Exception e)
                {
                    //远程调用agent失败后，判断并发数是否已增加，减去agent的并发数量 --add by sunxf on 2023.4.3
                    if(agentRunActNumAdd > 0) {
                        try {
                            EngineRepositotyJdbc.getInstance().saveAgentRunActNumDel(reqId, agenthost, agentport,
                                    actElem.getLevelOfWeight(), null);
                        } catch (RepositoryException ex) {
                            ex.printStackTrace();
                        }
                    }
                    // add by tao_ding 13-11-6 共享资源处配置集群组
                    if (actElem.isRemoteExec())
                    {
                        if (actElem.getRemoteAgent() != null)
                        {
                            IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                                    .getSharedResInsByName(actElem.getRemoteAgent());
                            if (null == agentResource)
                            {
                                agentExp(actElem);
                            }
                            if (agentResource.isRemoteGroup())
                            {
                                return reexecRemoteGroupActRes(groupName, actElem, execAct, params, requestWrapper,
                                    agentGroupid, reqId, proType);
                            }
                        }
                    }
                    if (i != 9)
                    {
                        _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                                + requestWrapper.getActElem(Constants.IEAI_IEAI).getName(),
                            e);

                        if (actElem.getTimeout() > 0)
                        {
                            long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                            getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                                newTimeout);

                            _log.info("Time out is change to:" + newTimeout + " reqid="
                                    + requestWrapper.getRexecRequest().getId() + acn
                                    + requestWrapper.getActElem(Constants.IEAI_IEAI).getName());
                        }
                        try
                        {
                            Thread.sleep(2000);
                        } catch (InterruptedException ex)
                        {
                            Thread.currentThread().interrupt();
                        }
                    } else
                    {
                        agentExp(actElem);
                        //发告警平台 add by sunxf 2023.2.8
                        warnAgentOutTime(agent);
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {// add by tao_ding
         // 远程活动执行的集群组配置处理 13-10-30
            try
            {
                info = EngineRepositotyJdbc.getInstance().saveServerIpPortInfo(execAct.getFlowId(), reqId);
            } catch (RepositoryException e1)
            {
                throw new ActivityException(e1);
            }
            if ("0".equals(info[2]))
            {
                return Constants.QUEUEUP;
            }
            params.addElement(request.getId());
            params.addElement(info[0]);
            params.addElement(Integer.valueOf(info[1]));
            // params.addElement((Hashtable)
            // act.getActRemAct(requestWrapper.getRexecRequest().getId()));
            _log.info("sendSetAct: reqId:serverip:port: " + reqId + ":" + info[0] + ":" + info[1]);
            return execRemoteGroupAct(ha, actElem, execAct, params, requestWrapper, agentGroupid, reqId, agentIp,
                agentPort, proType, groupName);
        }
        return Constants.END;
    }

    public static int exec ( ExecAct execAct, String reqId, String agentIp, int agentPort )
            throws ActivityException, ServerException
    {
        String[] info = null;
        RExecRequestWrapper requestWrapper = getReqWraper(reqId, Constants.IEAI_IEAI_BASIC);
        GetServerAct act = new GetServerAct();

        long agentNum = PersonalityEnv.getAgentNumDefault();
        Vector params = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        String agenthost = "";
        int agentport = 0;
        int agentPortBak = 0;
        Hashtable ha = null;
        if (actElem.isRemoteExec())
        {
            ha = (Hashtable) act.getActRemAct(requestWrapper.getRexecRequest().getId());
            if (actElem.getRemoteAgent() == null)
            {
                params.addElement(request.getId());
                params.addElement(request.getServerHost());
                params.addElement(Integer.valueOf(request.getServerPort()));
            } else
            {
                RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI_BASIC));
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByName(actElem.getRemoteAgent());
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
                    try
                    {
                        info = EngineRepositotyJdbc.getInstance().saveServerIpPortInfo(execAct.getFlowId(), reqId);
                    } catch (RepositoryException e1)
                    {
                        throw new ActivityException(e1);
                    }
                    if ("0".equals(info[2]))
                    {
                        return Constants.QUEUEUP;
                    }
                    params.addElement(request.getId());
                    params.addElement(info[0]);
                    params.addElement(Integer.valueOf(info[1]));
                } else
                {
                    params.addElement(request.getId());
                    params.addElement(request.getServerHost());
                    params.addElement(Integer.valueOf(request.getServerPort()));
                }
            }
            XmlRpcClient rpcClient = getXmlRpcClient(actElem, reqId, Constants.IEAI_IEAI_BASIC);
            Object[] object = getXmlRpcClientWithAgent(actElem, reqId);
            if (Constants.QUEUEUP == (((Integer) object[0]).intValue()))
            {
                return Constants.QUEUEUP;
            }

            agenthost = (String) object[2];
            if (ValidateUtil.validateIp(agenthost))
            {
                agenthost = DNSAnalyze.getIPByName(agenthost);
            }
            agentport = ((Integer) object[3]).intValue();
            agentPortBak = ((Integer) object[5]).intValue();
            /*boolean isFunVriableSwitch = Environment.getInstance().getBooleanConfigNew("tt.script.funandvariable.switch", false);
            ha.put("isFunVriableSwitch", isFunVriableSwitch);
            if (isFunVriableSwitch){
                Hashtable vo = new Hashtable();
                try
                {
                    IScriptInstance instance = IeaiSpringContextUtil.getDubboBean(IScriptInstance.class);
                    //根据agent查询其所绑定的函数与变量  
                    Map<String,Object> funAndVar = instance.getFunctionAndVariableByAgentId(agenthost,agentport);
                    if(funAndVar!=null || funAndVar.size()>0){
                          //函数、变量，函数使用加密形式传递
                        if(funAndVar.get("variable") != null){
                            ha.put("envparam",funAndVar.get("variable"));  
                            vo.put("envparam",funAndVar.get("variable"));
                        }else{
                            ha.put("envparam",""); 
                            vo.put("envparam",""); 
                        }
                        
                        Object funcObj = funAndVar.get("funcData");
                        if(funcObj!=null){
                            funcObj = (Map)funcObj;
                            net.sf.json.JSONObject jsonobject = net.sf.json.JSONObject.fromObject(funcObj);
                            String jsonStr= jsonobject.toString();//json字符串
                            DesUtils desUtils = new DesUtils("functionJsonStr");
                            ha.put(Constants.SERVER_PUSH_FUNCS_KEY,desUtils.encrypt(jsonStr));
                            vo.put(Constants.SERVER_PUSH_FUNCS_KEY,desUtils.encrypt(jsonStr));
                        }else {
                            vo.put(Constants.SERVER_PUSH_FUNCS_KEY,"");
                        }
                    }
                } catch (BadPaddingException e) {
                    e.printStackTrace();
                    _log.error("tryATry error"+e.getMessage());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ha.put("input", vo);
            }*/
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, agenthost, agentport, Constants.IEAI_IEAI_BASIC);
                // agenthost = pm.getIp();
                // agentport = pm.getPort();
            }

            params.addElement(ha);
            DbOpScheduler sch = new DbOpScheduler();
            for (int i = 0; i < agentNum; i++)
            {
                try
                {
                    // if (actElem.isRemoteGroupExec())
                    // {
                    // 屏蔽无用代码 代码与下方代码内容重复。
                    // sch.updateRemoteExecactByAgentRes(rpcClient.getURL().getHost(),
                    // rpcClient.getURL().getPort(),
                    // reqId, Constants.IEAI_IEAI_BASIC);
                    if (null != agenthost && !("").equals(agenthost))
                    {
                        EngineRepositotyJdbc.getInstance().saveSentHostInfo(agenthost, agentport, reqId);
                    }
                    // }
                    long start = System.currentTimeMillis();

                    rpcClient.execute(ieaiExe, params);

                    /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                    try
                    {
                        Double prjTypeD = IEAIRuntime.current().getProject().getProjectTpye();
                        try
                        {
                            int dbType = (new Double(prjTypeD)).intValue();
                            if (prjTypeD == Constants.IEAI_DAILY_OPERATIONS)
                            {
                                // dbType = Constants.IEAI_INFOCOLLECTION;
                                dbType = Constants.IEAI_DAILY_OPERATIONS;
                            }
                            EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(),
                                agenthost, agentport, actElem, requestWrapper.getRexecRequest(), execAct, dbType);
                        } catch (RepositoryException e)
                        {
                            _log.info("Error when saveAgentRunActNumAdd error!");
                        }
                    } catch (Exception e)
                    {
                        _log.info("Error when get project type!");
                    }
                    /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/
                    if (SystemConfig.isElapsedTime())
                        _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                                + " server向Agent发送任务耗时（ActivityRExecHelper.exec）: "
                                + (System.currentTimeMillis() - start) + " 毫秒");
                    if (PersonalityEnv.ACT_BASEDATA_VALUE)
                    {
                        // 更新有效活动表中的agent信息
                        RepSendRemote bean = new RepSendRemote();
                        bean.setIflowId(execAct.getFlowId());
                        bean.setIexecActId(execAct.getActId());
                        bean.setIagentIp(agenthost);
                        ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                        partThread.setSendRemote(bean);
                        partThread.start();
                    }
                    _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + "ActName："
                            + execAct.getActName());

                    return Constants.END;
                } catch (Exception e)
                {
                    // add by tao_ding 13-11-6 共享资源处配置集群组
                    if (actElem.isRemoteExec() && actElem.getRemoteAgent() != null)
                    {
                        IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                                .getSharedResInsByName(actElem.getRemoteAgent());
                        if (null == agentResource)
                        {
                            agentExp(actElem);
                        }
                        if (agentResource.isRemoteGroup())
                        {
                            return reexecRemoteGroupActRes(agentResource.getAgentGroup(), actElem, execAct, params,
                                requestWrapper, reqId);
                        }
                    }
                    if (i != (agentNum - 1))
                    {
                        _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                                + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                            e);

                        if (actElem.getTimeout() > 0)
                        {
                            long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                            getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                                newTimeout);

                            _log.info("Time out is change to:" + newTimeout + " reqid="
                                    + requestWrapper.getRexecRequest().getId() + acn
                                    + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                        }
                        try
                        {
                            Thread.sleep(2000);
                        } catch (InterruptedException ex)
                        {
                            Thread.currentThread().interrupt();
                        }
                    } else
                    {
                        // 主机IP重试重试10次后，继续连接备机IP
                        execBak(execAct, reqId, agentIp, agentPortBak);
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {// add by tao_ding
         // 远程活动执行的集群组配置处理 13-10-30
            try
            {
                info = EngineRepositotyJdbc.getInstance().saveServerIpPortInfo(execAct.getFlowId(), reqId);
            } catch (RepositoryException e1)
            {
                throw new ActivityException(e1);
            }
            if ("0".equals(info[2]))
            {
                return Constants.QUEUEUP;
            }
            params.addElement(request.getId());
            params.addElement(info[0]);
            params.addElement(Integer.valueOf(info[1]));
            // params.addElement((Hashtable)
            // act.getActRemAct(requestWrapper.getRexecRequest().getId()));
            return execRemoteGroupAct(ha, actElem, execAct, params, requestWrapper, reqId, agentIp, agentPort);
        }
        return Constants.END;
    }

    /**
     * <li>Description:构建xmlrpc连接并且获取agent的ip和port</li>
     * 
     * <AUTHOR> Nov 6, 2013
     * @param actElem
     * @param reqId
     * @param remoteHost
     * @param remotePort
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException return XmlRpcClient
     */
    private static Object[] getXmlRpcClientWithAgent ( ActivityElement actElem, String reqId )
            throws AgentCommunicationException, ServerException
    {
        int ret = Constants.END;
        String remoteHost = "";
        int remotePort = 0;
        String remoteHostBak = "";
        int remotePortBak = 0;
        int sslMode = -1;
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                remoteHost = actElem.getRemoteHost();
                remotePort = actElem.getRemotePort();
                sslMode = actElem.getConnectType();
            } else
            {
                RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI_BASIC));
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByName(actElem.getRemoteAgent());
                if (null == agentResource)
                {
                    AgentCommunicationException agentExp = new AgentCommunicationException();
                    agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
                    agentExp.setAgentHost(actElem.getRemoteHost());
                    agentExp.setAgentHostBak(actElem.getRemoteHostBak());
                    agentExp.setAgentPortBak(String.valueOf(actElem.getRemotePortBak()));
                    agentExp.setConnectionType(actElem.getConnectType());
                    agentExp.setActivityName(actElem.getName());
                    agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                    agentExp.setMessage("Communication Agent Error!");
                    agentExp.setTime(new Date());
                    throw agentExp;
                }
                if (agentResource.isRemoteGroup())
                {
                    String[] nodeInfo = null;
                    try
                    {
                        if(Environment.getInstance().isDgnsAgentGroupResourceSwitch()){
                            //查询组id
                            long groupId = EngineRepositotyJdbc.getInstance().getGroupIdByGroupName(agentResource.getAgentGroup());
                            //查询agent组中agent cpu负载，内存占用
                            List<AgentGroupInfo> agentGroupInfoList = EngineRepositotyJdbc.getInstance().getAgentListByGroupName(agentResource.getAgentGroup());
                            nodeInfo = EngineRepositotyJdbc.getInstance().dgnsGetAgentRunActNum(groupId,agentResource.getAgentGroup(),
                                    reqId,agentGroupInfoList);
                        }else{
                            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(agentResource.getAgentGroup(),
                                    reqId);
                        }
                    } catch (RepositoryException e)
                    {
                        _log.info(e);
                    }
                    if (nodeInfo == null || "0".equals(nodeInfo[2]))
                    {
                        AgentCommunicationException agentExp = new AgentCommunicationException();
                        agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                        agentExp.setMessage("Communication Agent Error!");
                        agentExp.setTime(new Date());
                        throw agentExp;
                    }
                    if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
                    {
                        ret = Constants.QUEUEUP;
                        Object[] object = new Object[4];
                        object[0] = Integer.valueOf(ret);
                        return object;
                    }
                    remoteHost = nodeInfo[0];
                    remotePort = Integer.parseInt(nodeInfo[1]);
                    sslMode = 0;
                } else
                {
                    Boolean xianBankAgentip =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.XIAN_BANK_AGENTIP_MAIN_BACKUPS_SWITCHOVER, false);
                    if(xianBankAgentip){//主ip手动切换远程执行AgentIP开关
                        AgentIpMainBackupsBean agentIpMainBackupsBean = new AgentIpMainBackupsBean();
                        agentIpMainBackupsBean= AgentipMainBackups(reqId,actElem);
                        if(agentIpMainBackupsBean!=null){
                            if(!"".equals(agentIpMainBackupsBean.getAgentHost())&&null!=agentIpMainBackupsBean.getAgentHost()){
                                remoteHost = agentIpMainBackupsBean.getAgentHost();
                                remotePort = agentIpMainBackupsBean.getAgentPort();
                                remoteHostBak = agentIpMainBackupsBean.getAgentHostBak();
                                remotePortBak = agentIpMainBackupsBean.getAgentPortBak();
                                sslMode = agentResource.getConnType();
                            }else {
                                remoteHost = agentResource.getAgentHost();
                                remotePort = agentResource.getAgentPort();
                                remoteHostBak = agentResource.getAgentHostBak();
                                remotePortBak = agentResource.getAgentPortBak();
                                sslMode = agentResource.getConnType();
                            }
                        }else {
                            remoteHost = agentResource.getAgentHost();
                            remotePort = agentResource.getAgentPort();
                            remoteHostBak = agentResource.getAgentHostBak();
                            remotePortBak = agentResource.getAgentPortBak();
                            sslMode = agentResource.getConnType();
                        }
                    }else {
                    remoteHost = agentResource.getAgentHost();
                    remotePort = agentResource.getAgentPort();
                    remoteHostBak = agentResource.getAgentHostBak();
                    remotePortBak = agentResource.getAgentPortBak();
                    sslMode = agentResource.getConnType();
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {
            String[] nodeInfo = new String[2];
            try
            {
                if(Environment.getInstance().isDgnsAgentGroupResourceSwitch()){
                    //查询组id
                    long groupId = EngineRepositotyJdbc.getInstance().getGroupIdByGroupName(actElem.getRemoteAgentGroup());
                    //查询agent组中agent cpu负载，内存占用
                    List<AgentGroupInfo> agentGroupInfoList = EngineRepositotyJdbc.getInstance().getAgentListByGroupName(actElem.getRemoteAgentGroup());
                    nodeInfo = EngineRepositotyJdbc.getInstance().dgnsGetAgentRunActNum(groupId,actElem.getRemoteAgentGroup(),
                            reqId,agentGroupInfoList);
                }else{
                    nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(actElem.getRemoteAgentGroup(), reqId);
                }
            } catch (RepositoryException e)
            {
                _log.info(e);
            }
            if ("0".equals(nodeInfo[2]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
            if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                ret = Constants.QUEUEUP;
                Object[] object = new Object[3];
                object[0] = Integer.valueOf(ret);
                return object;
            }
            remoteHost = nodeInfo[0];
            remotePort = Integer.parseInt(nodeInfo[1]);
            sslMode = 0;
        }

        XmlRpcClient rpcClient = null;
        try
        {
            String remoteip = (String) actElem.getInputExprs().get("$normal.agentip");
            if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
            {
                {
                    if (remoteip.startsWith("\""))
                    {
                        remoteip = remoteip.substring(1, remoteip.length() - 1);
                    } else
                    {
                        Object ret1 = com.ideal.ieai.server.engine.expreval.ExprEvaluator.eval(remoteip);
                        remoteip = (String) ret1;
                    }
                }
                ActivityElement ae = actElem;
                ae.setRemoteExec(true);
                // 获取远程ip地址和端口号
                if(remoteip.contains("[")&&remoteip.contains("]")&&remoteip.indexOf("[")<remoteip.indexOf("]")&&remoteip.startsWith("[")) {
                    //ipv6
                      String [] ipv6String=remoteip.split("]:");//根据以前写的可以判断长度一定是2
                      remoteHost=ipv6String[0]+"]";
                      remotePort=Integer.parseInt(ipv6String[1]);
                      _log.info("IPV6:"+remoteHost+":"+remotePort);
                  }else {
                      remoteHost = remoteip.split(":")[0];
                      remotePort = Integer.parseInt(remoteip.split(":")[1]);
                  }

            }
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + " proxySwitch is true ");
                }
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }
        } catch (MalformedURLException ex)
        {
            AgentCommunicationException agentExp = new AgentCommunicationException(ex);
            agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
            agentExp.setAgentHost(actElem.getRemoteHost());
            agentExp.setAgentHostBak(actElem.getRemoteHostBak());
            agentExp.setAgentPortBak(String.valueOf(actElem.getRemotePortBak()));
            agentExp.setConnectionType(actElem.getConnectType());
            agentExp.setActivityName(actElem.getName());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        Object[] object = new Object[6];
        object[0] = Integer.valueOf(ret);
        object[1] = rpcClient;
        object[2] = remoteHost;
        object[3] = Integer.valueOf(remotePort);
        object[4] = remoteHostBak;
        object[5] = Integer.valueOf(remotePortBak);
        return object;
    }

    /**
     * 
     * @Title: execRemoteGroupAct   
     * @Description: 远程组执行时需要对集群中的agent服务器都进行尝试
     * @param ha
     * @param actElem
     * @param execAct
     * @param params
     * @param requestWrapper
     * @param agentGroupid
     * @param reqId
     * @param agentIp
     * @param agentPort
     * @param proType
     * @param groupName
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException      
     * @author: licheng_zhao
     * @date:   2017年11月24日 下午3:32:01
     */
    private static int execRemoteGroupAct ( Hashtable ha, ActivityElement actElem, ExecAct execAct, Vector params,
            RExecRequestWrapper requestWrapper, long agentGroupid, String reqId, String agentIp, int agentPort,
            long proType, String groupName ) throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        String[] nodeInfo = null;
        Agent agent = new Agent();
        try
        {
//            //agent组没有agent产生告警 变为有agent时告警恢复开关
//            boolean flag=ServerEnv.getInstance().getBooleanConfig("bh.agentgroup.noagent.warn.recoverwarn.swith", false);
//            if(flag) {
//              //agent组没有agent产生告警 有agent时告警恢复
//                sendWarnOrNot(agentGroupid);  //sendWarnOrNotNew
//            }
            if(Environment.getInstance().isDgnsAgentGroupResourceSwitch()) {
                //查询agent组中agent cpu负载，内存占用
                List<AgentGroupInfo> agentGroupInfoList = EngineRepositotyJdbc.getInstance().getAgentListByGroupName(groupName);
                nodeInfo = EngineRepositotyJdbc.getInstance().dgnsGetAgentRunActNum(agentGroupid,groupName,
                        reqId,agentGroupInfoList);
            }else {
                nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(groupName, reqId, execAct.getFlowId(),
                        execAct.getActName(), actElem, agentIp, agentPort, agentGroupid, proType);
            }
            sendWarnOrNotNew(agentGroupid);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if (nodeInfo == null || "0".equals(nodeInfo[2]))
        {
            AgentCommunicationException agentExp = new AgentCommunicationException();
            agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            return Constants.QUEUEUP;
        }

        try
        {
            if ("com.ideal.ieai.adaptors.aptshelladaptor.aptshell.APTShellActView"
                    .equals(actElem.getActDef().getConfigViewClassName())
                    || "com.ideal.ieai.adaptors.aptadaptor.apt.APTActView"
                            .equals(actElem.getActDef().getConfigViewClassName()))
            {
                Map map = requestWrapper.getRexecRequest().getInput();

                replaceAptGroup(reqId, map, nodeInfo[0], actElem, execAct, nodeInfo[3]);

            }
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        remoteHost = nodeInfo[0];
        remotePort = Integer.parseInt(nodeInfo[1]);
        agent.setIagentip(remoteHost);
        agent.setIagentport(Long.valueOf(remotePort));
        sslMode = 0;

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);

            if (proxySwitch)
            {

                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + " proxySwitch is true ");
                }
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }
            params.addElement(ha);
        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        for (int i = 0; i < 3; i++)
        {
            //并发数是否已增加标识（用于异常回退）
            int agentRunActNumAdd = 0;
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(remoteHost, remotePort, reqId);


                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                try
                {
                    try
                    {
                        int dbType = Integer.parseInt(String.valueOf(proType));
                        EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(), remoteHost,
                            remotePort, actElem, requestWrapper.getRexecRequest(), execAct, dbType);
                    } catch (RepositoryException e)
                    {
                        _log.info("Error when saveAgentRunActNumAdd error!");
                    }
                } catch (Exception e)
                {
                    _log.info("Error when get project type!");
                }
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/
                _log.info("执行保存Agent实时并发数方法4519: flowId:agentip:agentport:weight:" +  execAct.getFlowId() + ":" + remoteHost + ":"
                        + remotePort + ":" + actElem.getLevelOfWeight());
                agentRunActNumAdd = EngineRepositotyJdbc.getInstance().increaseAgentGroupRunActNum(reqId, remoteHost, remotePort,
                    actElem.getLevelOfWeight(), agentGroupid, actElem, execAct, proType, groupName);
                //判断并发数是否超限，执行远程调用 --add by sunxf on 2023.4.3
                if(agentRunActNumAdd > 0) {
                    rpcClient.execute(ieaiExe, params);
                } else {
                    return Constants.QUEUEUP;
                }

                WorkflowManager.getInstance().updateActSendTime(execAct.getActName(),execAct.getFlowId());

                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName() + " requestid:" + reqId);
                for (Object obj : params) {
                    _log.info("------------requestid:" + reqId +" FlowID：" + execAct.getFlowId() + "  param: " + obj);
                }
                return Constants.END;
            } catch (Exception e)
            {
                //远程调用agent失败后，判断并发数是否已增加，减去agent的并发数量 --add by sunxf on 2023.4.3
                if(agentRunActNumAdd > 0) {
                    try {
                        EngineRepositotyJdbc.getInstance().saveAgentRunActNumDel(reqId, remoteHost, remotePort,
                                actElem.getLevelOfWeight(), null);
                    } catch (RepositoryException ex) {
                        ex.printStackTrace();
                    }
                }
                if (i != 2)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI).getName());
                    }
                    try
                    {
                        Thread.sleep(3000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveAgentRunactnum("0:0:0", remoteHost, remotePort,
                            Constants.REMOTE_AGENT_STATE_EXCEPTION, agentGroupid, proType, groupName);
                        //发告警平台 add by sunxf 2023.2.8
                        warnAgentOutTime(agent);
                    } catch (RepositoryException e1)
                    {
                        _log.error("execRemoteGroupAct:" + e1.getMessage());
                    }
                    return reexecRemoteGroupAct(actElem, execAct, params, requestWrapper, reqId, agentGroupid, proType,
                        groupName);
                }
            }
        }

        return Constants.END;
    }

    /**
     * <li>Description:远程组执行时需要对集群中的agent服务器都进行尝试</li>
     * 
     * <AUTHOR> Nov 4, 2013
     * @param ha
     * @param actElem
     * @param execAct
     * @param params
     * @param requestWrapper
     * @param reqId
     * @param agentIp
     * @param agentPort
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException return int
     */
    private static int execRemoteGroupAct ( Hashtable ha, ActivityElement actElem, ExecAct execAct, Vector params,
            RExecRequestWrapper requestWrapper, String reqId, String agentIp, int agentPort )
            throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        String[] nodeInfo = null;
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(actElem.getRemoteAgentGroup(), reqId,
                execAct.getFlowId(), execAct.getActName(), actElem, agentIp, agentPort);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if ("0".equals(nodeInfo[2]))
        {
            AgentCommunicationException agentExp = new AgentCommunicationException();
            agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            return Constants.QUEUEUP;
        }
        try
        {
            if ("com.ideal.ieai.adaptors.aptshelladaptor.aptshell.APTShellActView"
                    .equals(actElem.getActDef().getConfigViewClassName())
                    || "com.ideal.ieai.adaptors.aptadaptor.apt.APTActView"
                            .equals(actElem.getActDef().getConfigViewClassName()))
            {
                Map map = requestWrapper.getRexecRequest().getInput();

                replaceAptGroup(reqId, map, nodeInfo[0], actElem, execAct, nodeInfo[3]);

            }
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        remoteHost = nodeInfo[0];
        remotePort = Integer.parseInt(nodeInfo[1]);
        sslMode = 0;

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);

            if (proxySwitch)
            {

                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                // remoteHost = pm.getIp();
                // remotePort = pm.getPort();
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }
            params.addElement(ha);
        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        for (int i = 0; i < 3; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(remoteHost, remotePort, reqId);
                if (SystemConfig.isOther())
                {
                    for (int it = 0; it < params.size(); it++)
                    {
                        _log.info("execRemoteGroupAct is execute params=================" + params.get(it));
                    }
                }
                rpcClient.execute(ieaiExe, params);

                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                try
                {
                    Double prjTypeD = IEAIRuntime.current().getProject().getProjectTpye();
                    try
                    {
                        int dbType = (new Double(prjTypeD)).intValue();
                        if (prjTypeD == Constants.IEAI_DAILY_OPERATIONS)
                        {
                            // dbType = Constants.IEAI_INFOCOLLECTION;
                            dbType = Constants.IEAI_DAILY_OPERATIONS;
                        }
                        EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(), remoteHost,
                            remotePort, actElem, requestWrapper.getRexecRequest(), execAct, dbType);
                    } catch (RepositoryException e)
                    {
                        _log.info("Error when saveAgentRunActNumAdd error!");
                    }
                } catch (Exception e)
                {

                    _log.info("Error when get project type!");
                }
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/

                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                if (PersonalityEnv.ACT_BASEDATA_VALUE)
                {
                    // 更新有效活动表中的agent信息
                    RepSendRemote bean = new RepSendRemote();
                    bean.setIflowId(execAct.getFlowId());
                    bean.setIexecActId(execAct.getSerialNo());
                    bean.setIagentIp(remoteHost);
                    ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                    partThread.setSendRemote(bean);
                    partThread.start();
                }
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName() + " requestid:" + reqId);
                return Constants.END;
            } catch (Exception e)
            {
                if (i != 2)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                    }
                    try
                    {
                        Thread.sleep(3000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveAgentRunactnum("0:0:0", remoteHost, remotePort,
                            Constants.AGENTSTATE_DEAD);
                    } catch (RepositoryException e1)
                    {
                        _log.error("execRemoteGroupAct:" + e1.getMessage());
                    }
                    return reexecRemoteGroupAct(actElem, execAct, params, requestWrapper, reqId);
                }
            }
        }

        return Constants.END;
    }

    /**
     * 
     * @Title: reexecRemoteGroupActRes   
     * @Description: 负载的agent不能使用，重新获取agent信息(共享资源中配置集群组)   
     * @param groupName
     * @param actElem
     * @param execAct
     * @param params
     * @param requestWrapper
     * @param agentGroupId
     * @param reqId
     * @param proType
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException      
     * @author: licheng_zhao
     * @date:   2017年11月24日 下午3:31:28
     */
    private static int reexecRemoteGroupActRes ( String groupName, ActivityElement actElem, ExecAct execAct,
            Vector params, RExecRequestWrapper requestWrapper, long agentGroupId, String reqId, long proType )
            throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        String[] nodeInfo = null;
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(groupName, reqId, execAct.getFlowId(),
                execAct.getActName(), actElem, "", 0, agentGroupId, proType);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if ("0".equals(nodeInfo[2]))
        {
            AgentCommunicationException agentExp = new AgentCommunicationException();
            agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            return Constants.QUEUEUP;
        }

        remoteHost = nodeInfo[0];
        remotePort = Integer.parseInt(nodeInfo[1]);
        sslMode = 0;

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                Hashtable ha = new Hashtable();

                Object obj = params.get(params.size());
                if (obj instanceof Hashtable)
                {
                    ha = (Hashtable) obj;
                    params.remove(params.size());
                }
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                // if (pm.isProxy())
                // {
                // params.addElement(ha);
                // }
                // remoteHost = pm.getIp();
                // remotePort = pm.getPort();
                if (obj instanceof Hashtable)
                {
                    params.addElement(ha);
                }
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }

        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        for (int i = 0; i < 3; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(remoteHost, remotePort, reqId);
                rpcClient.execute(ieaiExe, params);
                _log.info("执行保存Agent实时并发数方法4860: flowId:agentip:agentport:weight:" +  execAct.getFlowId() + ":" + remoteHost + ":"
                        + remotePort + ":" + actElem.getLevelOfWeight());
                EngineRepositotyJdbc.getInstance().saveAgentRunActNumAdd(reqId, remoteHost, remotePort,
                    actElem.getLevelOfWeight(), agentGroupId, actElem, execAct, proType, groupName);
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName());

                return Constants.END;
            } catch (Exception e)
            {
                if (i != 2)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI).getName());
                    }
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveAgentRunactnum("0:0:0", remoteHost, remotePort,
                            Constants.REMOTE_AGENT_STATE_EXCEPTION, agentGroupId, proType, groupName);
                    } catch (RepositoryException e1)
                    {
                        _log.error("execRemoteGroupAct:" + e1.getMessage());
                    }
                    return reexecRemoteGroupActRes(groupName, actElem, execAct, params, requestWrapper, agentGroupId,
                        reqId, proType);
                }
            }
        }

        return Constants.END;
    }

    /**
     * 
     * @Title: reexecRemoteGroupAct   
     * @Description:负载的agent不能使用，重新获取agent信息   
     * @param actElem
     * @param execAct
     * @param params
     * @param requestWrapper
     * @param reqId
     * @param agentGroupid
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException      
     * @author: licheng_zhao
     * @date:   2017年11月23日 上午9:11:05
     */
    private static int reexecRemoteGroupAct ( ActivityElement actElem, ExecAct execAct, Vector params,
            RExecRequestWrapper requestWrapper, String reqId, long agentGroupid, long proType, String groupName )
            throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        String[] nodeInfo = null;
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(groupName, reqId, execAct.getFlowId(),
                execAct.getActName(), actElem, "", 0, agentGroupid, proType);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if ("0".equals(nodeInfo[2]))
        {
            AgentCommunicationException agentExp = new AgentCommunicationException();
            agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            return Constants.QUEUEUP;
        }

        remoteHost = nodeInfo[0];
        remotePort = Integer.parseInt(nodeInfo[1]);
        sslMode = 0;

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);

            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                // remoteHost = pm.getIp();
                // remotePort = pm.getPort();
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(),pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }
        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        for (int i = 0; i < 3; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(remoteHost, remotePort, reqId);
                rpcClient.execute(ieaiExe, params);
                _log.info("执行保存Agent实时并发数方法4997: flowId:agentip:agentport:weight:" +  execAct.getFlowId() + ":" + remoteHost + ":"
                        + remotePort + ":" + actElem.getLevelOfWeight());
                EngineRepositotyJdbc.getInstance().saveAgentRunActNumAdd(reqId, remoteHost, remotePort,
                    actElem.getLevelOfWeight(), agentGroupid, actElem, execAct, proType, groupName);
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName());
                return Constants.END;
            } catch (Exception e)
            {
                if (i != 2)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI).getName());
                    }
                    try
                    {
                        Thread.sleep(3000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveAgentRunactnum("0:0:0", remoteHost, remotePort,
                            Constants.REMOTE_AGENT_STATE_EXCEPTION, agentGroupid, proType, groupName);
                    } catch (RepositoryException e1)
                    {
                        _log.error("execRemoteGroupAct:" + e1.getMessage());
                    }
                    return reexecRemoteGroupAct(actElem, execAct, params, requestWrapper, reqId, agentGroupid, proType,
                        groupName);
                }
            }
        }

        return Constants.END;
    }

    /**
     * 
     * @Title: saveOutputForBranch   
     * @Description: 保存工作流输出   
     * @param flowId
     * @param lastLine
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2018年1月27日 下午4:00:54
     */
    private static void saveOutputForBranch ( Long flowId, String lastLine ) throws RepositoryException
    {
        String querySql = "SELECT T1.IID AS IID,T2.ISERNER AS IID2,T1.IISMAININS AS IID3, T1.IRUNINSNAME AS NAME, T2.IACTNAME AS NAME2,T2.IPKGNAME AS NAME3\n"
                + "  FROM IEAI_RUN_INSTANCE T1, IEAI_RUNINFO_INSTANCE T2\n" + " WHERE T1.IID = T2.IRUNINSID\n"
                + "   AND T1.ISTATE = 0\n" + "   AND T2.IFLOWID = " + flowId;

        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        Constants.IEAI_IEAI_BASIC);
                    List<ResultBean> returnList = SQLUtil.executeSqlReturnList(con, querySql, new ResultBean());
                    for (ResultBean rb : returnList)
                    {
                        // 非模板调用产生的流程
                        if (rb.getIid3() == 1l)
                        {
                            // 删除已经保存过
                            String deleteSql = "DELETE FROM  IEAI_SUS_RUNINFO_OUTPUT  WHERE IRUNINSID=" + rb.getIid()
                                    + "\n" + "AND IACTNAME='" + rb.getName2() + "' AND ISERNER=" + rb.getIid2()
                                    + " AND IPKGNAME='" + rb.getName3() + "'";

                            SQLUtil.executeUpdateSql(con, deleteSql);
                            long iid = IdGenerator.createId("IEAI_SUS_RUNINFO_OUTPUT", con);
                            String insertSql = "INSERT INTO IEAI_SUS_RUNINFO_OUTPUT\n"
                                    + "  (IID, IRUNINSID, IRUNINSNAME, IACTNAME, ISERNER, IPKGNAME, ILASTLINE) VALUES\n"
                                    + "  (" + iid + "," + rb.getIid() + ",'" + rb.getName() + "','" + rb.getName2()
                                    + "'," + rb.getIid2() + ",'" + rb.getName3() + "','" + lastLine + "')";
                            SQLUtil.executeUpdateSql(con, insertSql);
                        } else
                        {
                            // 模板调用产生的流程
                            String querySql2 = "SELECT T1.IID AS IID,T2.ISERNER AS IID2, T1.IISMAININS AS IID3,T1.IRUNINSNAME AS NAME, T2.IACTNAME AS NAME2,T2.IPKGNAME AS NAME3\n"
                                    + "  FROM IEAI_RUN_INSTANCE T1, IEAI_RUNINFO_INSTANCE T2\n"
                                    + " WHERE T1.IID = T2.IRUNINSID\n" + "   AND T1.ISTATE = 0\n"
                                    + "   AND T2.ICHILDINSTANCEID=" + rb.getIid();
                            List<ResultBean> returnList2 = SQLUtil.executeSqlReturnList(con, querySql2,
                                new ResultBean());
                            for (ResultBean rb2 : returnList2)
                            {
                                // 删除已经保存过
                                String deleteSql = "DELETE FROM  IEAI_SUS_RUNINFO_OUTPUT  WHERE IRUNINSID="
                                        + rb2.getIid() + "\n" + "AND IACTNAME='" + rb2.getName2() + "' AND ISERNER="
                                        + rb2.getIid2() + " AND IPKGNAME='" + rb2.getName3() + "'";
                                SQLUtil.executeUpdateSql(con, deleteSql);
                                long iid = IdGenerator.createId("IEAI_SUS_RUNINFO_OUTPUT", con);
                                String insertSql = "INSERT INTO IEAI_SUS_RUNINFO_OUTPUT\n"
                                        + "  (IID, IRUNINSID, IRUNINSNAME, IACTNAME, ISERNER, IPKGNAME, ILASTLINE) VALUES\n"
                                        + "  (" + iid + "," + rb2.getIid() + ",'" + rb2.getName() + "','"
                                        + rb2.getName2() + "'," + rb2.getIid2() + ",'" + rb2.getName3() + "','"
                                        + lastLine + "')";
                                SQLUtil.executeUpdateSql(con, insertSql);
                            }
                        }
                    }
                    con.commit();
                    break;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
    }

    /**
     * 
     * @Title: queryOutputForBranch   
     * @Description: 根据运行表子表主键查询依赖作业的输出   
     * @param iidIn
     * @return
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2018年1月28日 上午9:03:13
     */
    public static List<String> queryOutputForBranch ( ActExcelToRunData bean ) throws RepositoryException
    {
        List<String> ilastlines = null;
        for (int i = 0;; i++)
        {
            ilastlines = new ArrayList();
            String ipkgname = bean.getPkgName();
            String iprener = bean.getPrener();
            Long iruninsid = bean.getMainId();
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        Constants.IEAI_IEAI_BASIC);
                    if (iprener != null && !"".equals(iprener))
                    {
                        String[] prenerItem = iprener.split(",");
                        for (String prener : prenerItem)
                        {
                            String querySql = "SELECT T2.ILASTLINE AS NAME FROM IEAI_SUS_RUNINFO_OUTPUT T2\n"
                                    + "WHERE T2.IRUNINSID=" + iruninsid + " AND T2.ISERNER=" + prener
                                    + " AND T2.IPKGNAME='" + ipkgname + "'";
                            List<ResultBean> returnList = SQLUtil.executeSqlReturnList(con, querySql, new ResultBean());
                            for (ResultBean rb : returnList)
                            {
                                ilastlines.add(rb.getName());
                            }
                        }
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
        return ilastlines;
    }

    /**
     * 
     * @Title: checkOutputForBranch   
     * @Description: 根据父节点的返回结果过滤启动活动   
     * @param execActs
     * @param iidIn
     * @return
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2018年1月28日 上午9:43:00
     */
    public static List checkOutputForBranch ( List execActs ) throws RepositoryException
    {
        List returnList = new ArrayList();
        for (int i = 0; i < execActs.size(); i++)
        {
            Boolean checkSucc = true;
            ActExcelToRunData bean = (ActExcelToRunData) execActs.get(i);
            // 如果本身没有分支条件，或者无父节点，则不用判断父节点返回值
            if ("-1".equals(bean.getPrener()))
            {
                // 裁剪掉的步骤状态修改为9 不放入轮询list中
                if ("1".equals(bean.getIdisable()))
                {

                    String updateSql1 = "UPDATE IEAI_RUNINFO_INSTANCE SET  ISTATE=" + Constants.STATE_CUTOFF
                            + " WHERE IID=" + bean.getIid() + " AND ISTATE!=" + Constants.STATE_IBRANCH;
                    SQLUtil.executeUpdateSqlLoop(updateSql1, Constants.IEAI_IEAI_BASIC);
                    continue;
                }
                returnList.add(bean);
                continue;
            } else
            {
                Boolean returnValue = checkPrenerState(bean);
                if (returnValue)
                {
                    // 裁剪掉的步骤状态修改为9 不放入轮询list中
                    if ("1".equals(bean.getIdisable()))
                    {

                        String updateSql1 = "UPDATE IEAI_RUNINFO_INSTANCE SET  ISTATE=" + Constants.STATE_CUTOFF
                                + " WHERE IID=" + bean.getIid() + " AND ISTATE!=" + Constants.STATE_IBRANCH;
                        SQLUtil.executeUpdateSqlLoop(updateSql1, Constants.IEAI_IEAI_BASIC);
                        continue;
                    }
                    // 如果本作业未配置分支条件，可直接运行
                    if (bean.getIbranch() == null || "".equals(bean.getIbranch()))
                    {
                        returnList.add(bean);
                        continue;
                    }
                } else
                {
                    // 判断依赖作业的状态，如果未运行，本条作业也不运行
                    continue;
                }

            }
            // 根据运行表子表主键查询依赖作业的输出
            List<String> ilastlines = queryOutputForBranch(bean);

            for (String ilastline : ilastlines)
            {
                if (ilastline != null && !"".equals(ilastline))
                {
                    try
                    {
                        // 执行表达式
                        checkSucc = (Boolean) scriptExec(bean.getIbranch(), ilastline);
                    } catch (ScriptException e)
                    {
                        _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    }

                    if (checkSucc)
                    {
                        String updateSql1 = "UPDATE IEAI_RUNINFO_INSTANCE SET  ISTATE=" + Constants.STATE_IBRANCH
                                + " WHERE IID=" + bean.getIid();
                        SQLUtil.executeUpdateSqlLoop(updateSql1, Constants.IEAI_IEAI_BASIC);
                        break;
                    }
                }
            }
            if (checkSucc)
            {
                returnList.add(bean);
            }

        }
        return returnList;
    }

    /**
     * 
     * @Title: scriptExec   
     * @Description: 执行表达式 
     * @param instr
     * @return
     * @throws ScriptException      
     * @author: yunpeng_zhang 
     * @date:   2018年1月28日 上午11:03:59
     */
    public static Object scriptExec ( String instr, Object inValue ) throws ScriptException
    {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        engine.put("$", inValue);
        return engine.eval(instr);
    }

    /**
     * 
     * @Title: checkPrenerState   
     * @Description: 判断依赖作业的状态，如果未运行，本条作业也不运行   
     * @param bean
     * @return
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2018年1月28日 下午2:21:42
     */

    public static Boolean checkPrenerState ( ActExcelToRunData bean ) throws RepositoryException
    {
        Boolean returnValue = true;
        for (int i = 0;; i++)
        {
            String ipkgname = bean.getPkgName();
            String iprener = bean.getPrener();
            Long iruninsid = bean.getMainId();
            List state = new ArrayList();

            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        Constants.IEAI_IEAI_BASIC);
                    if (iprener != null && !"".equals(iprener))
                    {
                        String[] prenerItem = iprener.split(",");
                        for (String prener : prenerItem)
                        {
                            String querySql1 = "SELECT T.ISTATE AS IID FROM IEAI_RUNINFO_INSTANCE T\n"
                                    + "WHERE T.IRUNINSID=" + iruninsid + " AND T.ISERNER=" + prener
                                    + " AND T.IPKGNAME='" + ipkgname + "'";

                            List<ResultBean> returnList1 = SQLUtil.executeSqlReturnList(con, querySql1,
                                new ResultBean());
                            for (ResultBean rb : returnList1)
                            {
                                state.add(String.valueOf(rb.getIid()));
                            }
                        }

                        // 如果依赖步骤有条件不满足未执行的，则本步骤也不执行
                        if ((state.contains("2") || state.contains("8")
                                || state.contains(String.valueOf(Constants.STATE_CUTOFF))) && (!state.contains("1")))
                        {
                            // 父节点状态为已完成或者是裁剪，返回可执行
                            returnValue = true;
                            return returnValue;
                        } else if (state.size() == 1 && state.contains(String.valueOf(Constants.STATE_IBRANCH)))
                        {
                            String updateSql1 = "UPDATE IEAI_RUNINFO_INSTANCE SET  ISTATE=" + Constants.STATE_IBRANCH
                                    + " WHERE IID=" + bean.getIid();
                            SQLUtil.executeUpdateSql(con, updateSql1);
                            con.commit();
                            returnValue = false;
                            return returnValue;
                        } else
                        {
                            // 返回可执行
                            returnValue = false;
                            return returnValue;
                        }

                    }
                    break;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);

                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_INSERT);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
        return returnValue;
    }

    /**
     * <li>Description:根据requstid获取agent资源组名称</li>
     * 
     * <AUTHOR> Dec 9, 2013
     * @param requestId
     * @return
     * @throws ServerException return String
     */
    public static String getRemoteGroupName ( String requestId ) throws ServerException
    {
        RExecRequestWrapper requestWrapper = getReqWraper(requestId);
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        if (actElem != null)
            return actElem.getRemoteAgentGroup();
        return "";
    }

    private static RExecRequestWrapper getReqWraper ( String requestId ) throws ServerException
    {
        RExecRequestWrapper reqWrapper = _dbRexecReqWraperRep.getRexecReqWrapper(requestId);
        if (null == reqWrapper)
        {
            _log.error("ERR_REXEC_REQUEST_NOT_FOUND and requestid:" + requestId);
            throw new ServerException(ServerError.ERR_REXEC_REQUEST_NOT_FOUND);
        }
        return reqWrapper;
    }

    /** 数据自清理功能所需方法 by yue_sun on 20180504 start **/
    public static void stopShellCmdProcess ( String requestId, int dbType ) throws ServerException
    {
        Vector params = new Vector();
        params.addElement(requestId);

        RExecRequestWrapper requestWrapper = getReqWraper(requestId);
        ActivityElement actElem = requestWrapper.getActElem(dbType);
        if (null == actElem)
        {
            return;
        }
        XmlRpcClient rpcClient;
        try
        {
            rpcClient = getXmlRpcClientRead(actElem, requestId, Constants.IEAI_IEAI);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                Hashtable ha = new Hashtable();
                ProxyModel pm = getXmlRpcClientProxyModel(ha, actElem, requestId, Constants.IEAI_IEAI_BASIC);
                if (pm.isProxy())
                {
                    ha.put("proxyJson", pm.getProxyJsonData());
                    ha.put("level", pm.getLevel());
                    ha.put("isProxy", pm.isProxy());
                    params.addElement(ha);
                    _log.info(
                        "proxy- remoteURL:" + rpcClient.getURL() + ",requestId:" + requestId + " proxySwitch is true ");
                } else
                {
                    _log.info(
                        "agent- remoteURL:" + rpcClient.getURL() + ",requestId:" + requestId + " proxySwitch is true ");
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }

        try
        {
            // 终止shellcmd活动时 需要做到终止进程并清空list对应文件
            rpcClient.execute("IEAIAgent.stopShellCmdProcess", params);
            rpcClient.execute("IEAIAgent.removeExecActList", params);
        } catch (XmlRpcException e)
        {
            // xmlRPC在调用远程执行方法时必须有返回值，执行方法不能为VOID，否则报错。该处屏蔽掉错误.
            if (-1 == e.getMessage().indexOf("void return types for handler methods not supported"))
            {
                _log.info(e);
                throw new ServerException(ServerError.ERR_XMLRPC_FAILED, e);
            }
        } catch (IOException e)
        {
            _log.info(e);
            throw new ServerException(ServerError.ERR_IOEXCEPTION, e);
        }

    }

    /**
     * @des 为交互需要获取agent连接
     * @param actElem
     * @param reqId
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException
     * @throws RepositoryException
     */
    private static XmlRpcClient getXmlRpcClientRead ( ActivityElement actElem, String reqId, int dbType )
            throws AgentCommunicationException, ServerException
    {
        String remoteHost = "";
        int remotePort = -1;
        int sslMode = -1;
        // add for agent backup
        String remoteHostBak = "";
        int remotePortBak = -1;
        AgentResource agentRes = null;

        HashMap remoteInfo = new HashMap();

        if (actElem.getRemoteAgent() == null)
        {
            remoteHost = actElem.getRemoteHost();
            remotePort = actElem.getRemotePort();
            sslMode = actElem.getConnectType();
        } else
        {
            RuntimeEnv.setExecContext(getExecContext(reqId, dbType));
            IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                    .getSharedResInsByNameType(actElem.getRemoteAgent(), dbType);
            if (null == agentResource)
            {
                agentExp(actElem);
            }
            remoteHost = agentResource.getAgentHost();
            remotePort = agentResource.getAgentPort();
            sslMode = agentResource.getConnType();
            actElem.setRemoteHost(remoteHost);
            actElem.setRemotePort(remotePort);
            actElem.setConnectType(sslMode);
        }
        remoteInfo.put("remoteHost", remoteHost);
        remoteInfo.put("remotePort", Integer.toString(remotePort));
        remoteInfo.put("remoteHostBak", remoteHostBak);
        remoteInfo.put("remotePortBak", Integer.toString(remotePortBak));

        // update by txl 20130909 for agent backup
        try
        {
            agentRes = compareAgent(actElem, remoteInfo, reqId, dbType);
        } catch (RepositoryException rex)
        {
            agentExp(actElem);
        }

        XmlRpcClient rpcClient = null;
        try
        {
            boolean b = false;
            if (agentRes.getId() != -1)
            {
                if (!actElem.isRemoteSwitch())
                {
                    if (agentRes.getSwitchStatus() == 1 && agentRes.getAgentUse() == Constants.AGENTRESOURCE_USE_TWO)
                    {
                        b = true;
                    }
                } else if (actElem.isRemoteSwitch())
                {
                    RExecRequestWrapper requestWrapper = getReqWraper(reqId);
                    String currHost = requestWrapper.getRexecRequest().getAgentHost();
                    int currPort = requestWrapper.getRexecRequest().getAgentPort();
                    if (remoteHost.equals(currHost) && remotePort == currPort)
                    {
                        b = true;
                        agentRes.setAgentUse(Constants.AGENTRESOURCE_USE_TWO);
                    } else if (remoteHostBak.equals(currHost) && remotePortBak == currPort)
                    {
                        agentRes.setAgentUse(Constants.AGENTRESOURCE_USE_ONE);
                    }
                }
            }
            if (b)
            {
                remoteHost = remoteHostBak;
                remotePort = remotePortBak;
            }
            _log.info("Now agent:host is " + remoteHost + " ## port is " + remotePort + ",requestid=" + reqId);
            try
            {
                String[] node = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequestN(reqId, dbType);
                if (null != node[0] || !("".equals(node[0])))
                {
                    remoteHost = node[0];
                    remotePort = Integer.parseInt(node[1]);
                }
                _log.info("Find agent info from remoteexecact and use it:host is " + remoteHost + " ## port is "
                        + remotePort + ",requestid=" + reqId);
            } catch (RepositoryException e1)
            {
                _log.error("get agent info from IEAI_REMOTEEXECACT error!", e1);
            }
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, dbType);
                remoteHost = pm.getIp();
                remotePort = pm.getPort();
                ssltype=pm.getSsl();
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + remoteHost + ",remotePort:" + remotePort + ",requestId:" + reqId
                            + " proxySwitch is true ");
                } else
                {
                    _log.info("agent- remoteIP:" + remoteHost + ",remotePort:" + remotePort + ",requestId:" + reqId
                            + " proxySwitch is true ");
                }
            }
            rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            try
            {
                if (actElem.isRemoteSwitch())
                {
                    EngineRepositotyJdbc.getInstance().updateAgentResource(agentRes, dbType);
                }
            } catch (Exception e)
            {
                _log.error("save agent error from getXmlRpcClientRead", e);
            }

        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        // end update for agent backup
        return rpcClient;
    }

    /**
     * 搜索数据库查询是否匹配并返回应该使用的agent
     * 
     * @param actElem
     * @param agentInfo
     * @return
     * @throws RepositoryException
     * @throws ServerException
     */
    public static AgentResource compareAgent ( ActivityElement actElem, HashMap remoteInfo, String reqId, int dbType )
            throws RepositoryException, ServerException
    {
        _log.info("Start compare agentResource for act: " + actElem.getActDef().getName());
        RExecRequestWrapper requestWrapper = getReqWraper(reqId, dbType);
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest(dbType);
        // Project prj = Engine.getInstance().getProjectNameByFlowId(request.getFlowId());
        String prjName = request.getProjectName();
        AgentResource agentRes = EngineRepositotyJdbc.getInstance().getAgentResource(prjName, actElem.getRemoteAgent(),
            dbType);
        boolean needUpdate = false;
        if (agentRes.getId() == -1)
        {
            // need insert
            _log.info("No Agent for act: " + actElem.getActDef().getName() + " in db!");
            agentRes.setPrjName(prjName);
            agentRes.setAgentName(actElem.getRemoteAgent());
            agentRes.setAgentIpFirst(actElem.getRemoteHost());
            agentRes.setAgentPortFirst(actElem.getRemotePort());
            agentRes.setAgentIpSecond(actElem.getRemoteHostBak());
            agentRes.setAgentPortSecond(actElem.getRemotePortBak());
            agentRes.setAgentType(actElem.getConnectType());
            agentRes.setCreateTime(DBUtil.getTime(new Date()));
            agentRes.setFlowId(request.getFlowId());
            agentRes.setAgentUse(Constants.AGENTRESOURCE_USE_ONE);
            EngineRepositotyJdbc.getInstance().saveAgentResource(agentRes, dbType);
        } else
        {
            _log.info("Find use agent for act: " + actElem.getActDef().getName() + " in db!");
            String remoteHost = remoteInfo.get("remoteHost") == null ? "" : remoteInfo.get("remoteHost").toString();
            String remotePort = remoteInfo.get("remotePort") == null ? "0" : remoteInfo.get("remotePort").toString();
            String remoteHostBak = remoteInfo.get("remoteHostBak") == null ? ""
                    : remoteInfo.get("remoteHostBak").toString();
            String remotePortBak = remoteInfo.get("remotePortBak") == null ? "0"
                    : remoteInfo.get("remotePortBak").toString();
            if (!remoteHost.equals(agentRes.getAgentIpFirst().trim()))
            {
                agentRes.setAgentIpFirst(remoteHost);
                needUpdate = true;
            }
            if (Integer.parseInt(remotePort) != agentRes.getAgentPortFirst())
            {
                agentRes.setAgentPortFirst(Integer.parseInt(remotePort));
                needUpdate = true;
            }
            if (!remoteHostBak.equals(agentRes.getAgentIpSecond().trim()))
            {
                agentRes.setAgentIpSecond(remoteHostBak);
                needUpdate = true;
            }
            if (Integer.parseInt(remotePortBak) != agentRes.getAgentPortSecond())
            {
                agentRes.setAgentPortSecond(Integer.parseInt(remotePortBak));
                needUpdate = true;
            }

            // update agentresource
            if (needUpdate)
            {
                _log.info("Update db agentResource!");
                agentRes.setAgentUse(Constants.AGENTRESOURCE_USE_ONE);
                EngineRepositotyJdbc.getInstance().updateAgentResource(agentRes, dbType);
            }
        }
        return agentRes;
    }

    /** 数据自清理功能所需方法 by yue_sun on 20180504 end **/

    public static int execAgentReq ( ExecAct execAct, String reqId, String nextReqId, String agentIp, String agentPort )
            throws ActivityException, ServerException
    {
        RExecRequestWrapper requestWrapper = getReqWraper(reqId);

        GetServerAct act = new GetServerAct();

        Vector params = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI);
        actElem.setRemoteHost(agentIp);
        actElem.setRemotePort(Integer.valueOf(agentPort));
        String agenthost = "";
        int agentport = 0;
        Map actMap = new HashMap();
        actMap = act.getActRemAct(requestWrapper.getRexecRequest().getId());
        actMap.put("agentHost", agentIp);
        actMap.put("agentPort", Integer.valueOf(agentPort));
        actElem.setRemoteHost(agentIp);
        actElem.setRemotePort(Integer.valueOf(agentPort));
        if (actElem.getRemoteAgent() == null)
        {
            params.addElement(reqId);
            params.addElement(request.getServerHost());
            params.addElement(Integer.valueOf(request.getServerPort()));
            params.addElement(actMap);

        } else
        {
            RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI));
            IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                    .getSharedResInsByName(actElem.getRemoteAgent());
            if (null == agentResource)
            {
                agentExp(actElem);
            }
            params.addElement(reqId);
            params.addElement(request.getServerHost());
            params.addElement(Integer.valueOf(request.getServerPort()));
            params.addElement(actMap);
        }

        Object[] object = getXmlRpcClientWithAgentReq(actElem, nextReqId);

        agenthost = (String) object[2];
        agentport = ((Integer) object[3]).intValue();
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            Hashtable ha = new Hashtable();
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agenthost, agentport, Constants.IEAI_IEAI);
            if (pm.isProxy())
            {
                params.addElement(ha);
                _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",requestId:" + reqId
                        + " proxySwitch is true ");
            } else
            {
                _log.info("agent- remoteIP:" + agenthost + ",remotePort:" + agentport + ",requestId:" + reqId
                        + " proxySwitch is true ");
            }
            // agenthost = pm.getIp();
            // agentport = pm.getPort();
        }
        for (int i = 0; i < 10; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(agenthost, agentport, reqId);
                ((XmlRpcClient) object[1]).execute(ieaiExe, params);
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                try
                {
                    Double prjTypeD = IEAIRuntime.current().getProject().getProjectTpye();
                    try
                    {
                        int dbType = (new Double(prjTypeD)).intValue();
                        if (prjTypeD == Constants.IEAI_DAILY_OPERATIONS)
                        {
                            dbType = Constants.IEAI_DAILY_OPERATIONS;
                        }
                        EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(), agenthost,
                            agentport, actElem, requestWrapper.getRexecRequest(), execAct, dbType);
                    } catch (RepositoryException e)
                    {
                        _log.info("Error when saveAgentRunActNumAdd error!");
                    }
                } catch (Exception e)
                {
                    _log.info("Error when get project type!");
                }
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                if (PersonalityEnv.ACT_BASEDATA_VALUE)
                {
                    // 更新有效活动表中的agent信息
                    RepSendRemote bean = new RepSendRemote();
                    bean.setIflowId(execAct.getFlowId());
                    bean.setIexecActId(execAct.getSerialNo());
                    bean.setIagentIp(agenthost);
                    ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                    partThread.setSendRemote(bean);
                    partThread.start();
                }
                try
                {
                    AptGroupLoadManager.getInstance().updateAgentReqId(1, nextReqId);
                    _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                            + execAct.getActName() + "  IP:" + agentIp + "  port:" + agentPort);
                } catch (Exception e)
                {
                    _log.info("Error when log for Send Message to Agent!");
                }
                return Constants.END;
            } catch (Exception e)
            {
                // add by tao_ding 13-11-6 共享资源处配置集群组
                if (actElem.isRemoteExec() && actElem.getRemoteAgent() != null)
                {
                    IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                            .getSharedResInsByName(actElem.getRemoteAgent());
                    if (null == agentResource)
                    {
                        agentExp(actElem);
                    }
                }
                if (i != 9)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + " nextreqid="
                            + nextReqId + " agent:" + agentIp + ":" + agentPort + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI).getName());
                    }
                    try
                    {
                        Thread.sleep(2000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    agentExp(actElem);
                }
            }
        }
        return Constants.END;
    }

    private static Object[] getXmlRpcClientWithAgentReq ( ActivityElement actElem, String reqId )
            throws AgentCommunicationException
    {
        int ret = Constants.END;
        String remoteHost = "";
        int remotePort = 0;
        int sslMode = -1;
        remoteHost = actElem.getRemoteHost();
        remotePort = actElem.getRemotePort();
        sslMode = actElem.getConnectType();
        Object[] object = null;
        int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
        for (int i = 0; i < 3; i++)
        {
            object = new Object[4];
            XmlRpcClient rpcClient = null;
            try
            {
                boolean proxySwitch = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH, Environment.FALSE_BOOLEAN);
                if (proxySwitch)
                {
                    PerformDataProcessService ps = new PerformDataProcessService();
                    Hashtable ha = new Hashtable();

                    ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                    // remoteHost = pm.getIp();
                    // remotePort = pm.getPort();
                    rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
                } else
                {
                    rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
                }
                object[0] = Integer.valueOf(ret);
                object[1] = rpcClient;
                object[2] = remoteHost;
                object[3] = Integer.valueOf(remotePort);
                break;
            } catch (MalformedURLException ex)
            {
                if (i != 2)
                {
                    _log.error("It will retry rpcClient.  ", ex);
                    try
                    {
                        Thread.sleep(3000);
                    } catch (InterruptedException e1)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    agentExp(actElem);
                }
            }
        }
        return object;
    }

    /**
     * <li>Description:负载的agent不能使用，重新获取agent信息(共享资源中配置集群组)</li>
     * 
     * <AUTHOR> Nov 4, 2013
     * @param groupName
     * @param actElem
     * @param execAct
     * @param params
     * @param requestWrapper
     * @param reqId
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException return int
     */
    private static int reexecRemoteGroupActRes ( String groupName, ActivityElement actElem, ExecAct execAct,
            Vector params, RExecRequestWrapper requestWrapper, String reqId )
            throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        String[] nodeInfo = null;
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(actElem.getRemoteAgentGroup(), reqId,
                execAct.getFlowId(), execAct.getActName(), actElem, "", 0);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if ("0".equals(nodeInfo[2]))
        {
            AgentCommunicationException agentExp = new AgentCommunicationException();
            agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            return Constants.QUEUEUP;
        }

        remoteHost = nodeInfo[0];
        remotePort = Integer.parseInt(nodeInfo[1]);
        sslMode = 0;

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                Hashtable ha = new Hashtable();

                Object obj = params.get(params.size());
                if (obj instanceof Hashtable)
                {
                    ha = (Hashtable) obj;
                    params.remove(params.size());
                }
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + " proxySwitch is true ");
                }
                if (obj instanceof Hashtable)
                {
                    params.addElement(ha);
                }
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }

        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        for (int i = 0; i < 3; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(remoteHost, remotePort, reqId);
                rpcClient.execute(ieaiExe, params);
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                if (PersonalityEnv.ACT_BASEDATA_VALUE)
                {
                    // 更新有效活动表中的agent信息
                    RepSendRemote bean = new RepSendRemote();
                    bean.setIflowId(execAct.getFlowId());
                    bean.setIexecActId(execAct.getSerialNo());
                    bean.setIagentIp(remoteHost);
                    ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                    partThread.setSendRemote(bean);
                    partThread.start();
                }
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName());
                return Constants.END;
            } catch (Exception e)
            {
                if (i != 2)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                    }
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveAgentRunactnum("0:0:0", remoteHost, remotePort,
                            Constants.AGENTSTATE_DEAD);
                    } catch (RepositoryException e1)
                    {
                        _log.error("execRemoteGroupAct:" + e1.getMessage());
                    }
                    return reexecRemoteGroupActRes(groupName, actElem, execAct, params, requestWrapper, reqId);
                }
            }
        }

        return Constants.END;
    }

    /**
     * <li>Description:负载的agent不能使用，重新获取agent信息</li>
     * 
     * <AUTHOR> Nov 4, 2013
     * @param actElem
     * @param execAct
     * @param params
     * @param requestWrapper
     * @param reqId
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException return int
     */
    private static int reexecRemoteGroupAct ( ActivityElement actElem, ExecAct execAct, Vector params,
            RExecRequestWrapper requestWrapper, String reqId ) throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        String[] nodeInfo = null;
        try
        {
            nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(actElem.getRemoteAgentGroup(), reqId,
                execAct.getFlowId(), execAct.getActName(), actElem, "", 0);
        } catch (RepositoryException e)
        {
            _log.info(e);
        }
        if ("0".equals(nodeInfo[2]))
        {
            AgentCommunicationException agentExp = new AgentCommunicationException();
            agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
        {
            return Constants.QUEUEUP;
        }

        remoteHost = nodeInfo[0];
        remotePort = Integer.parseInt(nodeInfo[1]);
        sslMode = 0;

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                // remoteHost = pm.getIp();
                // remotePort = pm.getPort();
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + " proxySwitch is true ");
                }
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }

        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        for (int i = 0; i < 3; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(remoteHost, remotePort, reqId);
                rpcClient.execute(ieaiExe, params);
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                if (PersonalityEnv.ACT_BASEDATA_VALUE)
                {
                    // 更新有效活动表中的agent信息
                    RepSendRemote bean = new RepSendRemote();
                    bean.setIflowId(execAct.getFlowId());
                    bean.setIexecActId(execAct.getSerialNo());
                    bean.setIagentIp(remoteHost);
                    ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                    partThread.setSendRemote(bean);
                    partThread.start();
                }
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName());
                return Constants.END;
            } catch (Exception e)
            {
                if (i != 2)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI).getName());
                    }
                    try
                    {
                        Thread.sleep(3000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        EngineRepositotyJdbc.getInstance().saveAgentRunactnum("0:0:0", remoteHost, remotePort,
                            Constants.AGENTSTATE_DEAD);
                    } catch (RepositoryException e1)
                    {
                        _log.error("execRemoteGroupAct:" + e1.getMessage());
                    }
                    return reexecRemoteGroupAct(actElem, execAct, params, requestWrapper, reqId);
                }
            }
        }

        return Constants.END;
    }

    /**
     * <li>Description:</li>
     * 
     * <AUTHOR> 2015年12月9日
     * @param agentip return void
     * @throws RepositoryException
     * @throws DBException
     * @throws MarshallingException
     */
    private static void replaceAptGroup ( String reqid, Map aptInput, String agentip, ActivityElement actElem,
            ExecAct execAct, String groupNameNew ) throws ServerException, RepositoryException
    {
        Connection con = null;
        try
        {
            String busGroupName = "";
            if ("".equals(groupNameNew))
            {
                busGroupName = actElem.getRemoteAgentGroup();
            } else
            {
                busGroupName = groupNameNew;
            }
            int agentnum = EngineRepositotyJdbc.getInstance().getAgentNumByGroupName(busGroupName);
            String aptGroup = "";
            if (agentnum == 1)
            {
                aptGroup = "";
            } else
            {
                aptGroup = EngineRepositotyJdbc.getInstance().getAptGroupByIp(agentip);
            }

            apt(actElem, aptInput, execAct, aptGroup);

            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_IEAI);
            Long inputId = LobStorer.saveBinaryNoCommit(
                SerializationUtils.serialize((HashMap) IDataMarshallerHelper.translateValueMap(aptInput)), con);
            DbOpScheduler dbOpScheduler = new DbOpScheduler();
            dbOpScheduler.updateRemoteExecActForApt(reqid, inputId, con);
            con.commit();
        } catch (MarshallingException e)
        {
            _log.error("apt input save  translateValueMap err:" + e);
            throw new ServerException(-900900);

        } catch (SQLException ee)
        {
            _log.error(" getAptGroupByIp 获取数据库连接失败");
            throw new ServerException(-900901);
        } catch (DBException e)
        {
            _log.error(" getAptGroupByIp 获取数据库连接失败");
            throw new ServerException(-900902);
        } finally
        {
            DBResource.closeConnection(con, "replaceAptGroup", _log);
        }

    }

    /**
     * <li>Description:动态替换APT组</li>
     * 
     * <AUTHOR> 2015年12月11日
     * @param actElem
     * @param input
     * @param execAct
     * @param realationAptGroupName
     * @return
     * @throws ServerException return Map
     */
    private static Map apt ( ActivityElement actElem, Map input, ExecAct execAct, String realationAptGroupName )
            throws ServerException
    {
        try
        {
            String dB2 = "";
            String isDB2 = "isDB2";
            DefaultConfig dc = (DefaultConfig) actElem.getActConfig();
            if (null == input || input.size() == 0)
            {
                dB2 = String.valueOf(dc.get(isDB2));
            } else
            {
                if (null == input.get(isDB2).toString() || "".equals(input.get(isDB2).toString()))
                {
                    dB2 = String.valueOf(dc.get(isDB2));
                } else
                {
                    dB2 = input.get(isDB2).toString();
                }
            }
            /*
             * 增加生成apt文件时，针对pools为DB2的处理，在apt接口中设置apt接口所使用的DB2的Ip，以便更具该IP生成apt文件中关于db2的数据信息
             */
            _log.info(" executeAct aptshell step2: " + execAct.getActName() + " flowid: " + execAct.getFlowId());
            String dbIp = "";
            String dB2IP = "DB2_IP";
            if ("true".equals(dB2))
            {
                if (null == input || input.size() == 0)
                {
                    dbIp = String.valueOf(dc.get(dB2IP));
                } else
                {
                    if (null == input.get(dB2IP).toString() || "".equals(input.get(dB2IP).toString()))
                    {
                        dbIp = String.valueOf(dc.get(dB2IP));
                    } else
                    {
                        dbIp = input.get(dB2IP).toString();
                    }
                }
            }

            String[] aptGroup = null;
            String groupName = "";
            String groupServer = null;
            if (realationAptGroupName == null || "".equals(realationAptGroupName))
            {

                aptGroup = EngineRepositotyJdbc.getInstance().getAptActGroupName(execAct.getFlowId(),
                    execAct.getActName());
                groupName = aptGroup[1];

            }

            // 獲取apt文件內容信息

            // 获取真正的APT组
            if (null != realationAptGroupName && !"".equals(realationAptGroupName))
            {
                groupName = realationAptGroupName;
            }
            input = AptGroupLoadManager.getInstance().generateAptFileContent(groupName, dB2, input, dbIp);
            groupServer = (String) input.get("serverNodeList");
            String aptFile = "";
            _log.info("  executeAct aptshell step3: " + execAct.getActName() + " flowid:" + execAct.getFlowId());
            if (null != groupServer && !"".equals(groupServer))
            {
                // 獲取执行組內所有可用服務器(apt配置的远程执行组和业务组不相同时执行)
                aptFile = (String) input.get("apt_file");
                if (null == aptFile || "".equals(aptFile) || "null".equals(aptFile))
                {
                    // apt组为设置存放路径时报错
                    _log.error("该远程执行组:" + groupName + "未配置APT文件存放路径!");
                    throw new ServerException(ServerError.FAILED_APT_NOAPTFLIEPATH);
                }
            } else
            {
                // apt组为设置存放路径时报错
                throw new ServerException(ServerError.FAILED_APT_NOAPTFLIEPATH);
            }
            _log.info("actTempName :" + input.get("actTempName") + " isDeleteAptFile :" + input.get("isDeleteAptFile")
                    + " command :" + input.get("command") + " isDB2 :" + input.get(isDB2) + " serverNodeList :"
                    + input.get("serverNodeList") + " apt_groupName :" + input.get("apt_groupName") + " apt_fileName :"
                    + input.get("apt_fileName") + " apt_file :" + input.get("apt_file"));
            return input;
        } catch (RepositoryException e)
        {
            _log.info("Get groupName Error! and actname:" + execAct.getActName() + " flowid:" + execAct.getFlowId());
            throw new ServerException(ServerError.ERR_DB_QUERY);
        } catch (ServerException e)
        {
            _log.info("serverException aptshell");
            throw new ServerException(e.getErrCode());
        }

    }

    /** 主备IP切换 新增方法 by yue_sun on 20180517 start **/
    /**
     * @des agent 无法连接 异常
     * @param actElem
     * @throws AgentCommunicationException
     */
    private static void agentExp ( ActivityElement actElem ) throws AgentCommunicationException
    {
        AgentCommunicationException agentExp = new AgentCommunicationException(new Exception());
        agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
        agentExp.setAgentHost(actElem.getRemoteHost());
        agentExp.setConnectionType(actElem.getConnectType());
        agentExp.setActivityName(actElem.getName());
        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
        agentExp.setMessage("Communication Agent Error!");
        agentExp.setTime(new Date());
        throw agentExp;
    }

    /**
     * @Title: execBak   
     * @Description: 主机重试10次后，切换备机连接
     * @param execAct
     * @param reqId
     * @param agentIp
     * @param agentPort
     * @return
     * @throws ActivityException
     * @throws ServerException      
     * @author: yue_sun 
     * @date:   2018年5月17日 下午4:35:08
     */
    public static int execBak ( ExecAct execAct, String reqId, String agentIp, int agentPort )
            throws ActivityException, ServerException
    {
        String[] info = null;
        RExecRequestWrapper requestWrapper = getReqWraper(reqId);
        GetServerAct act = new GetServerAct();

        long agentNum = PersonalityEnv.getAgentNumDefault();
        Vector params = new Vector();
        Vector paramsExp = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        String agentHostBak = "";
        int agentPortBak = 0;
        Hashtable ha = (Hashtable) act.getActRemAct(requestWrapper.getRexecRequest().getId());
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                params.addElement(request.getId());
                params.addElement(request.getServerHost());
                params.addElement(Integer.valueOf(request.getServerPort()));
                // params.addElement((Hashtable)
                // act.getActRemAct(requestWrapper.getRexecRequest().getId()));

            } else
            {
                RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI_BASIC));
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByName(actElem.getRemoteAgent());
                if (null == agentResource)
                {
                    AgentCommunicationException agentExp = new AgentCommunicationException();
                    agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
                    agentExp.setAgentHost(actElem.getRemoteHost());
                    agentExp.setConnectionType(actElem.getConnectType());
                    agentExp.setActivityName(actElem.getName());
                    agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                    agentExp.setMessage("Communication Agent Error!");
                    agentExp.setTime(new Date());
                    throw agentExp;
                }
                if (agentResource.isRemoteGroup())
                {
                    try
                    {
                        info = EngineRepositotyJdbc.getInstance().saveServerIpPortInfo(execAct.getFlowId(), reqId);
                    } catch (RepositoryException e1)
                    {
                        throw new ActivityException(e1);
                    }
                    if ("0".equals(info[2]))
                    {
                        return Constants.QUEUEUP;
                    }
                    params.addElement(request.getId());
                    params.addElement(info[0]);
                    params.addElement(Integer.valueOf(info[1]));
                    // params.addElement((Hashtable)
                    // act.getActRemAct(requestWrapper.getRexecRequest().getId()));
                } else
                {
                    params.addElement(request.getId());
                    params.addElement(request.getServerHost());
                    params.addElement(Integer.valueOf(request.getServerPort()));
                    // params.addElement((Hashtable)
                    // act.getActRemAct(requestWrapper.getRexecRequest().getId()));
                }
            }

            XmlRpcClient rpcClient = getXmlRpcClientBak(actElem, reqId, Constants.IEAI_IEAI_BASIC);
            Object[] object = getXmlRpcClientWithAgentBak(actElem, reqId);
            if (Constants.QUEUEUP == (((Integer) object[0]).intValue()))
            {
                return Constants.QUEUEUP;
            }
            agentHostBak = (String) object[4];
            agentPortBak = ((Integer) object[5]).intValue();
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                ProxyModel pm = ps.getPerformDataJsonData(ha, agentHostBak, agentPortBak, Constants.IEAI_IEAI_BASIC);
                // agentHostBak = pm.getIp();
                // agentPortBak = pm.getPort();
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIPBak:" + pm.getIp() + ",remotePortBak:" + pm.getPort() + ",requestId:"
                            + reqId + " proxySwitch is true ");
                } else
                {
                    _log.info("agent- remoteIPBak:" + pm.getIp() + ",remotePortBak:" + pm.getPort() + ",requestId:"
                            + reqId + " proxySwitch is true ");
                }
            }
            paramsExp = params;
            params.addElement(ha);
            DbOpScheduler sch = new DbOpScheduler();
            for (int i = 0; i < agentNum; i++)
            {
                try
                {
                    // sch.updateRemoteExecactByAgentRes(((XmlRpcClient)
                    // object[1]).getURL().getHost(),
                    // ((XmlRpcClient) object[1]).getURL().getPort(), reqId,
                    // Constants.IEAI_IEAI_BASIC);
                    long start = System.currentTimeMillis();
                    // add by yuxh 20181113 增加判断 备机IP为空不更新
                    if (null != agentHostBak && !("").equals(agentHostBak))
                    {
                        EngineRepositotyJdbc.getInstance().saveSentHostInfo(agentHostBak, agentPortBak, reqId);
                    }
                    /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                    try
                    {
                        Double prjTypeD = IEAIRuntime.current().getProject().getProjectTpye();
                        try
                        {
                            int dbType = (new Double(prjTypeD)).intValue();
                            if (prjTypeD == Constants.IEAI_DAILY_OPERATIONS)
                            {
                                dbType = Constants.IEAI_DAILY_OPERATIONS;
                            }
                            if (null == agentHostBak || agentPortBak == 0)
                            {
                                EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(),
                                    request.getAgentHost(), request.getAgentPort(), actElem,
                                    requestWrapper.getRexecRequest(), execAct, dbType);
                            } else
                            {
                                EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(),
                                    agentHostBak, agentPortBak, actElem, requestWrapper.getRexecRequest(), execAct,
                                    dbType);
                            }
                        } catch (RepositoryException e)
                        {
                            _log.info("Error when saveAgentRunActNumAdd error!");
                        }
                    } catch (Exception e)
                    {

                        _log.info("Error when get project type!");
                    }
                    /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/

                    rpcClient.execute(ieaiExe, params);
                    if (SystemConfig.isElapsedTime())
                        _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                                + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: "
                                + (System.currentTimeMillis() - start) + " 毫秒");
                    if (PersonalityEnv.ACT_BASEDATA_VALUE)
                    {
                        // 更新有效活动表中的agent信息
                        RepSendRemote bean = new RepSendRemote();
                        bean.setIflowId(execAct.getFlowId());
                        bean.setIexecActId(execAct.getActId());
                        bean.setIagentIp(agentHostBak);
                        ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                        partThread.setSendRemote(bean);
                        partThread.start();
                    }
                    _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                            + execAct.getActName());
                    return Constants.END;
                } catch (Exception e)
                {
                    // add by tao_ding 13-11-6 共享资源处配置集群组
                    if (actElem.isRemoteExec() && actElem.getRemoteAgent() != null)
                    {
                        IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                                .getSharedResInsByName(actElem.getRemoteAgent());
                        if (null == agentResource)
                        {
                            AgentCommunicationException agentExp = new AgentCommunicationException();
                            agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
                            agentExp.setAgentHost(actElem.getRemoteHost());
                            agentExp.setConnectionType(actElem.getConnectType());
                            agentExp.setActivityName(actElem.getName());
                            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                            agentExp.setMessage("Communication Agent Error!");
                            agentExp.setTime(new Date());
                            throw agentExp;
                        }
                        if (agentResource.isRemoteGroup())
                        {
                            return reexecRemoteGroupActRes(agentResource.getAgentGroup(), actElem, execAct, params,
                                requestWrapper, reqId);
                        }
                    }
                    if (i != (agentNum - 1))
                    {
                        _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                                + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                            e);

                        if (actElem.getTimeout() > 0)
                        {
                            long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                            getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                                newTimeout);

                            _log.info("Time out is change to:" + newTimeout + " reqid="
                                    + requestWrapper.getRexecRequest().getId() + acn
                                    + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                        }
                        try
                        {
                            Thread.sleep(2000);
                        } catch (InterruptedException ex)
                        {
                            Thread.currentThread().interrupt();
                        }
                    } else
                    {
                        agentExp(actElem);
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {// add by tao_ding
         // 远程活动执行的集群组配置处理 13-10-30
            try
            {
                info = EngineRepositotyJdbc.getInstance().saveServerIpPortInfo(execAct.getFlowId(), reqId);
            } catch (RepositoryException e1)
            {
                throw new ActivityException(e1);
            }
            if ("0".equals(info[2]))
            {
                return Constants.QUEUEUP;
            }
            params.addElement(request.getId());
            params.addElement(info[0]);
            params.addElement(Integer.valueOf(info[1]));
            // params.addElement((Hashtable)
            // act.getActRemAct(requestWrapper.getRexecRequest().getId()));
            return execRemoteGroupAct(ha, actElem, execAct, params, requestWrapper, reqId, agentIp, agentPort);
        }
        return Constants.END;
    }

    /**
     * <li>Description:构建xmlrpc连接并且获取agent的备机ip和备机port</li>
     * 
     * <AUTHOR> Nov 6, 2013
     * @param actElem
     * @param reqId
     * @param remoteHost
     * @param remotePort
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException return XmlRpcClient
     */
    private static Object[] getXmlRpcClientWithAgentBak ( ActivityElement actElem, String reqId )
            throws AgentCommunicationException, ServerException
    {
        int ret = Constants.END;
        String remoteHost = "";
        int remotePort = 0;
        String remoteHostBak = "";
        int remotePortBak = 0;
        int sslMode = -1;
        RExecRequestWrapper requestWrapper = getReqWraper(reqId);
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        String prjName = request.getProjectName();
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                remoteHost = actElem.getRemoteHost();
                remotePort = actElem.getRemotePort();
                sslMode = actElem.getConnectType();
            } else
            {
                RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI_BASIC));
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByName(actElem.getRemoteAgent());
                if (null == agentResource)
                {
                    AgentCommunicationException agentExp = new AgentCommunicationException();
                    agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
                    agentExp.setAgentHost(actElem.getRemoteHost());
                    agentExp.setAgentHostBak(actElem.getRemoteHostBak());
                    agentExp.setAgentPortBak(String.valueOf(actElem.getRemotePortBak()));
                    agentExp.setConnectionType(actElem.getConnectType());
                    agentExp.setActivityName(actElem.getName());
                    agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                    agentExp.setMessage("Communication Agent Error!");
                    agentExp.setTime(new Date());
                    throw agentExp;
                }
                if (agentResource.isRemoteGroup())
                {
                    AgentResource agentRes = null;
                    String[] nodeInfo = new String[4];
                    try
                    {
                        agentRes = EngineRepositotyJdbc.getInstance().getAgentResource(prjName,
                            actElem.getRemoteAgent(), Constants.IEAI_IEAI_BASIC);
                        if (StringUtils.isNotEmpty(agentRes.getAgentIpSecond()) && 0 != agentRes.getAgentPortSecond())
                        {
                            nodeInfo[0] = agentRes.getAgentIpSecond();
                            nodeInfo[1] = String.valueOf(agentRes.getAgentPortSecond());
                            nodeInfo[2] = "1";
                        }
                    } catch (RepositoryException e)
                    {
                        _log.info(e);
                    }
                    if ("0".equals(nodeInfo[2]))
                    {
                        AgentCommunicationException agentExp = new AgentCommunicationException();
                        agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                        agentExp.setMessage("Communication Agent Error!");
                        agentExp.setTime(new Date());
                        throw agentExp;
                    }
                    if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
                    {
                        ret = Constants.QUEUEUP;
                        Object[] object = new Object[4];
                        object[0] = Integer.valueOf(ret);
                        return object;
                    }
                    remoteHost = nodeInfo[0];
                    remotePort = Integer.parseInt(nodeInfo[1]);
                    sslMode = 0;
                } else
                {
                    Boolean xianBankAgentip =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.XIAN_BANK_AGENTIP_MAIN_BACKUPS_SWITCHOVER, false);
                    if(xianBankAgentip){//主ip手动切换远程执行AgentIP开关
                        AgentIpMainBackupsBean agentIpMainBackupsBean = new AgentIpMainBackupsBean();
                        agentIpMainBackupsBean= AgentipMainBackups(reqId,actElem);
                        if(agentIpMainBackupsBean!=null){
                            if(!"".equals(agentIpMainBackupsBean.getAgentHost())&&null!=agentIpMainBackupsBean.getAgentHost()){
                                remoteHost = agentIpMainBackupsBean.getAgentHost();
                                remotePort = agentIpMainBackupsBean.getAgentPort();
                                remoteHostBak = agentIpMainBackupsBean.getAgentHostBak();
                                remotePortBak = agentIpMainBackupsBean.getAgentPortBak();
                                sslMode = agentResource.getConnType();
                            }else {
                                remoteHost = agentResource.getAgentHost();
                                remotePort = agentResource.getAgentPort();
                                remoteHostBak = agentResource.getAgentHostBak();
                                remotePortBak = agentResource.getAgentPortBak();
                                sslMode = agentResource.getConnType();
                            }
                        }else {
                            remoteHost = agentResource.getAgentHost();
                            remotePort = agentResource.getAgentPort();
                            remoteHostBak = agentResource.getAgentHostBak();
                            remotePortBak = agentResource.getAgentPortBak();
                            sslMode = agentResource.getConnType();
                        }
                    }else {
                        remoteHost = agentResource.getAgentHost();
                        remotePort = agentResource.getAgentPort();
                        remoteHostBak = agentResource.getAgentHostBak();
                        remotePortBak = agentResource.getAgentPortBak();
                        sslMode = agentResource.getConnType();
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {
            String[] nodeInfo = new String[2];
            try
            {
                nodeInfo = EngineRepositotyJdbc.getInstance().getAgentRunActNum(actElem.getRemoteAgentGroup(), reqId);
            } catch (RepositoryException e)
            {
                _log.info(e);
            }
            if ("0".equals(nodeInfo[2]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
            if (null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                ret = Constants.QUEUEUP;
                Object[] object = new Object[3];
                object[0] = Integer.valueOf(ret);
                return object;
            }
            remoteHost = nodeInfo[0];
            remotePort = Integer.parseInt(nodeInfo[1]);
            sslMode = 0;
        }

        XmlRpcClient rpcClient;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch && null != remoteHost && !"".equals(remoteHost))
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI);
                // remoteHost = pm.getIp();
                // remotePort = pm.getPort();
                if (pm.isProxy())
                {
                    _log.info("proxy- remoteIP:" + pm.getIp() + ",remotePort:" + pm.getPort() + ",agent:" + remoteHost
                            + ":" + remotePort + ",requestId:" + reqId + " proxySwitch is true ");
                }
                rpcClient = new AgentXmlRpcClient(pm.getIp(), pm.getPort(), pm.getSsl());
            } else
            {
                rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
            }
        } catch (MalformedURLException ex)
        {
            AgentCommunicationException agentExp = new AgentCommunicationException(ex);
            agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
            agentExp.setAgentHost(actElem.getRemoteHost());
            agentExp.setAgentHostBak(actElem.getRemoteHostBak());
            agentExp.setAgentPortBak(String.valueOf(actElem.getRemotePortBak()));
            agentExp.setConnectionType(actElem.getConnectType());
            agentExp.setActivityName(actElem.getName());
            agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
            agentExp.setMessage("Communication Agent Error!");
            agentExp.setTime(new Date());
            throw agentExp;
        }
        Object[] object = new Object[6];
        object[0] = Integer.valueOf(ret);
        object[1] = rpcClient;
        object[2] = remoteHost;
        object[3] = Integer.valueOf(remotePort);
        object[4] = remoteHostBak;
        object[5] = Integer.valueOf(remotePortBak);
        return object;
    }

    /**   
     * @Title: getXmlRpcClientBak   
     * @Description: 切换备机IP
     * @param actElem
     * @param reqId
     * @param type
     * @return
     * @throws AgentCommunicationException
     * @throws ServerException      
     * @author: yue_sun 
     * @date:   2018年5月18日 上午10:26:02   
     */
    private static XmlRpcClient getXmlRpcClientBak ( ActivityElement actElem, String reqId, int type )
            throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        RuntimeEnv.setExecContext(getExecContext(reqId, type));
        int remotePort = -1;
        int sslMode = -1;
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                remoteHost = actElem.getRemoteHost();
                remotePort = actElem.getRemotePort();
                sslMode = actElem.getConnectType();
            } else
            {
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByNameType(actElem.getRemoteAgent(), type);
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
                    String[] nodeInfo = null;
                    try
                    {
                        nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
                    } catch (RepositoryException e)
                    {
                        _log.info(e);
                    }
                    if (null == nodeInfo || null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1]
                            || "".equals(nodeInfo[1]))
                    {
                        AgentCommunicationException agentExp = new AgentCommunicationException();
                        agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                        agentExp.setMessage("Communication Agent Error!");
                        agentExp.setTime(new Date());
                        throw agentExp;
                    }
                    remoteHost = nodeInfo[0];
                    remotePort = Integer.parseInt(nodeInfo[1]);
                    sslMode = 0;
                } else
                {
                    Boolean xianBankAgentip =  ServerEnv.getInstance().getBooleanConfig(ServerEnv.XIAN_BANK_AGENTIP_MAIN_BACKUPS_SWITCHOVER, false);
                    if(xianBankAgentip){//主ip手动切换远程执行AgentIP开关
                        AgentIpMainBackupsBean agentIpMainBackupsBean = new AgentIpMainBackupsBean();
                        agentIpMainBackupsBean= AgentipMainBackups(reqId,actElem);
                        if(agentIpMainBackupsBean!=null){
                            if(!"".equals(agentIpMainBackupsBean.getAgentHost())&&null!=agentIpMainBackupsBean.getAgentHost()){
                                remoteHost = agentIpMainBackupsBean.getAgentHost();
                                remotePort = agentIpMainBackupsBean.getAgentPort();
                                sslMode = agentResource.getConnType();
                            }else {
                                remoteHost = agentResource.getAgentHostBak();
                                remotePort = agentResource.getAgentPortBak();
                                sslMode = agentResource.getConnType();
                            }
                        }else {
                            remoteHost = agentResource.getAgentHostBak();
                            remotePort = agentResource.getAgentPortBak();
                            sslMode = agentResource.getConnType();
                        }
                    }else {
                        remoteHost = agentResource.getAgentHostBak();
                        remotePort = agentResource.getAgentPortBak();
                        sslMode = agentResource.getConnType();
                    }
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {
            String[] nodeInfo = new String[2];
            try
            {
                nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
            } catch (RepositoryException e)
            {
                _log.info(e);
            }
            if (null == nodeInfo || null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1]
                    || "".equals(nodeInfo[1]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
            remoteHost = nodeInfo[0];
            remotePort = Integer.parseInt(nodeInfo[1]);
            sslMode = 0;
        }
        XmlRpcClient rpcClient = null;
        try
        {
            String remoteip = (String) actElem.getInputExprs().get("$normal.agentip");
            if (!StringUtil.isEmptyStr(remoteip) && actElem instanceof ActivityElement)
            {
                if (remoteip.startsWith("$"))
                {
                    Object ret = com.ideal.ieai.server.engine.expreval.ExprEvaluator.eval(remoteip);
                    remoteip = (String) ret;
                }
                if (remoteip.startsWith("\""))
                {
                    remoteip = remoteip.substring(1, remoteip.length() - 1);
                }
                remoteHost = remoteip.split(":")[0];
                remotePort = Integer.parseInt(remoteip.split(":")[1]);
            }
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, type);
                remoteHost = pm.getIp();
                remotePort = pm.getPort();
                ssltype=pm.getSsl();
            }
            rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        return rpcClient;
    }

    /** 主备IP切换 新增方法 by yue_sun on 20180517 end **/

    /**
     * gg增加agent执行方式
     * 
     * @param execAct
     * @param reqId
     * @param agentIp
     * @param agentPort
     * @return
     * @throws ActivityException
     * @throws ServerException
     */
    public static int execSign ( ExecAct execAct, String reqId, String agentIp, int agentPort )
            throws ActivityException, ServerException
    {
        RExecRequestWrapper requestWrapper = getReqWraper(reqId);
        GetServerAct act = new GetServerAct();
        Vector params = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        Hashtable ha = (Hashtable) act.getActRemAct(requestWrapper.getRexecRequest().getId());
        params.addElement(request.getId());
        params.addElement(request.getServerHost());
        params.addElement(Integer.valueOf(request.getServerPort()));

        for (int i = 0; i < 10; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                EngineRepositotyJdbc.getInstance().saveSentHostInfo(agentIp, agentPort, reqId);
                XmlRpcClient rpcClient = null;
                boolean proxySwitch = ServerEnv.getInstance()
                        .getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH, Environment.FALSE_BOOLEAN);
                if (proxySwitch)
                {
                    PerformDataProcessService ps = new PerformDataProcessService();
                    ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, agentPort, Constants.IEAI_IEAI_BASIC);
                    // agentIp = pm.getIp();
                    // agentPort = pm.getPort();
                    rpcClient = getXmlRpcClient(pm.getIp(), pm.getPort(), Constants.IEAI_IEAI_BASIC);
                } else
                {
                    rpcClient = getXmlRpcClient(agentIp, agentPort, Constants.IEAI_IEAI_BASIC);
                }
                params.addElement(ha);
                rpcClient.execute(ieaiExe, params);
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 start **/
                try
                {
                    Double prjTypeD = IEAIRuntime.current().getProject().getProjectTpye();
                    try
                    {
                        int dbType = (new Double(prjTypeD)).intValue();
                        if (prjTypeD == Constants.IEAI_DAILY_OPERATIONS)
                        {
                            dbType = Constants.IEAI_DAILY_OPERATIONS;
                        }
                        EngineRepositotyJdbc.getInstance().saveShellCmdInfo(reqId, actElem.getRemoteAgent(), agentIp,
                            agentPort, actElem, requestWrapper.getRexecRequest(), execAct, dbType);
                        // 在这里根据flowId查询启动线程，查询相关数据
                        if (Environment.getInstance().getDgProjectParamSwitch()) {
                            TopoLogicalThread topoLogicalThread = new TopoLogicalThread(execAct.getFlowId(), TopoLogicalThread.STARTTYPE);
                            topoLogicalThread.start();
                        }
                    } catch (RepositoryException e)
                    {
                        _log.info("Error when saveAgentRunActNumAdd error!");
                    }
                } catch (Exception e)
                {
                    _log.info("Error when get project type!");
                }
                /** yue_sun 一体化运维******* 迁移保存shellcmd的agent ip、端口、shellpath 、shellname等信息的功能 on 2018-03-21 end **/
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                if (PersonalityEnv.ACT_BASEDATA_VALUE)
                {
                    // 更新有效活动表中的agent信息
                    RepSendRemote bean = new RepSendRemote();
                    bean.setIflowId(execAct.getFlowId());
                    bean.setIexecActId(execAct.getSerialNo());
                    bean.setIagentIp(agentIp);
                    ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                    partThread.setSendRemote(bean);
                    partThread.start();
                }
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName());
                return Constants.END;
            } catch (Exception e)
            {
                if (i != 9)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);
                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                    }
                    try
                    {
                        Thread.sleep(2000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    try
                    {
                        if ("Communication Agent Error for AgentAutoRenew!".equals(e.getMessage()))
                        {
                            EngineRepositotyJdbc.getInstance().saveAgentRunActNumExp(request.getId(),
                                request.getAgentHost(), request.getAgentPort(), request.getLevelOfWeight());
                        }
                    } catch (Exception e1)
                    {
                        _log.info(e);
                    }
                    agentExp(actElem);
                }
            }
        }

        return Constants.END;
    }

    public static int reSetReqAct ( ExecAct execAct, String reqId, String nextReqId, String agentIp, String agentPort )
            throws ActivityException, ServerException
    {
        RExecRequestWrapper requestWrapper = getReqWraper(reqId);
        GetServerAct act = new GetServerAct();
        Vector params = new Vector();
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        ActivityElement actElem = requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC);
        actElem.setRemoteHost(agentIp);
        actElem.setRemotePort(Integer.valueOf(agentPort));
        Map actMap = new HashMap();
        actMap = act.getActRemAct(requestWrapper.getRexecRequest().getId());
        actMap.put("agentHost", agentIp);
        actMap.put("agentPort", Integer.valueOf(agentPort));
        actElem.setRemoteHost(agentIp);
        actElem.setRemotePort(Integer.valueOf(agentPort));
        params.addElement(reqId);
        params.addElement(request.getServerHost());
        params.addElement(Integer.valueOf(request.getServerPort()));
        params.addElement(actMap);

        Hashtable ha = new Hashtable();
        boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
            Environment.FALSE_BOOLEAN);
        if (proxySwitch)
        {
            PerformDataProcessService ps = new PerformDataProcessService();
            ProxyModel pm = ps.getPerformDataJsonData(ha, agentIp, Integer.valueOf(agentPort),
                Constants.IEAI_IEAI_BASIC);
            if (pm.isProxy())
            {
                params.addElement(ha);
                _log.info("proxy- remoteIP:" + agentIp + ",remotePort:" + agentPort + ",requestId:" + reqId
                        + " proxySwitch is true ");
            } else
            {
                _log.info("agent- remoteIP:" + agentIp + ",remotePort:" + agentPort + ",requestId:" + reqId
                        + " proxySwitch is true ");
            }
        }
        XmlRpcClient rpcClient = getXmlRepRpcClient(actElem, reqId, nextReqId, agentIp, agentPort);
        for (int i = 0; i < 10; i++)
        {
            try
            {
                long start = System.currentTimeMillis();
                rpcClient.execute("IEAIAgent.reSetAct", params);
                if (SystemConfig.isElapsedTime())
                    _log.info("工作流ID：" + execAct.getFlowId() + " 活动名称：" + execAct.getActName()
                            + "  server向Agent发送任务耗时（ActivityRExecHelper.exec）: " + (System.currentTimeMillis() - start)
                            + " 毫秒");
                if (PersonalityEnv.ACT_BASEDATA_VALUE)
                {
                    // 更新有效活动表中的agent信息
                    RepSendRemote bean = new RepSendRemote();
                    bean.setIflowId(execAct.getFlowId());
                    bean.setIexecActId(execAct.getSerialNo());
                    bean.setIagentIp(agentIp);
                    ActRuntimeValThread partThread = new ActRuntimeValThread(Constants.PART_UPDATE_REMOTE);
                    partThread.setSendRemote(bean);
                    partThread.start();
                }
                _log.info("Send Message to Agent Successsful! and FlowID：" + execAct.getFlowId() + " ActName："
                        + execAct.getActName());

                return Constants.END;
            } catch (Exception e)
            {
                if (i != 9)
                {
                    _log.error("It will retry. reqid=" + requestWrapper.getRexecRequest().getId() + acn
                            + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName(),
                        e);

                    if (actElem.getTimeout() > 0)
                    {
                        long newTimeout = getNetworkRecoverWaitTime() + actElem.getTimeout() * 1000;
                        getRexecReqWrapperRep(requestWrapper.getRexecRequest().isSafeFirst()).setTimeOut(reqId,
                            newTimeout);

                        _log.info(
                            "Time out is change to:" + newTimeout + " reqid=" + requestWrapper.getRexecRequest().getId()
                                    + acn + requestWrapper.getActElem(Constants.IEAI_IEAI_BASIC).getName());
                    }
                    try
                    {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex)
                    {
                        Thread.currentThread().interrupt();
                    }
                } else
                {
                    agentExp(actElem);
                }
            }
        }
        return Constants.END;
    }

    private static XmlRpcClient getXmlRepRpcClient ( ActivityElement actElem, String reqId, String nextReqId,
            String agentIp, String agentPort ) throws AgentCommunicationException, ServerException
    {
        String remoteHost = null;
        int remotePort = -1;
        int sslMode = -1;
        // add by tao_ding 13-11-6 共享资源处配置集群组
        if (actElem.isRemoteExec())
        {
            if (actElem.getRemoteAgent() == null)
            {
                remoteHost = agentIp;
                remotePort = Integer.parseInt(agentPort);
                sslMode = actElem.getConnectType();
            } else
            {
                RuntimeEnv.setExecContext(getExecContext(reqId, Constants.IEAI_IEAI_BASIC));
                IAgentConnectResource agentResource = (IAgentConnectResource) IEAIRuntime.current()
                        .getSharedResInsByName(actElem.getRemoteAgent());
                if (null == agentResource)
                {
                    agentExp(actElem);
                }
                if (agentResource.isRemoteGroup())
                {
                    String[] nodeInfo = null;
                    try
                    {
                        nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
                    } catch (RepositoryException e)
                    {
                        _log.info(e);
                    }
                    if (null == nodeInfo || null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1]
                            || "".equals(nodeInfo[1]))
                    {
                        AgentCommunicationException agentExp = new AgentCommunicationException();
                        agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                        agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                        agentExp.setMessage("Communication Agent Error!");
                        agentExp.setTime(new Date());
                        throw agentExp;
                    }
                    remoteHost = agentIp;
                    remotePort = Integer.parseInt(agentPort);
                    sslMode = 0;
                } else
                {
                    remoteHost = agentIp;
                    remotePort = Integer.parseInt(agentPort);
                    sslMode = agentResource.getConnType();
                }
            }
        } else if (actElem.isRemoteGroupExec())
        {
            String[] nodeInfo = new String[2];
            try
            {
                nodeInfo = EngineRepositotyJdbc.getInstance().getAgentInfoOfRequest(reqId);
            } catch (RepositoryException e)
            {
                _log.info(e);
            }
            if (null == nodeInfo || null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1]
                    || "".equals(nodeInfo[1]))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException();
                agentExp.setAgentGroup(actElem.getRemoteAgentGroup());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                agentExp.setMessage("Communication Agent Error!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
            remoteHost = agentIp;
            remotePort = Integer.parseInt(agentPort);
            sslMode = 0;
        }

        XmlRpcClient rpcClient = null;
        try
        {
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            int ssltype= RpcSslType.getInstance().exec(remoteHost, remotePort);
            if (proxySwitch)
            {
                PerformDataProcessService ps = new PerformDataProcessService();
                Hashtable ha = new Hashtable();

                ProxyModel pm = ps.getPerformDataJsonData(ha, remoteHost, remotePort, Constants.IEAI_IEAI_BASIC);
                remoteHost = pm.getIp();
                remotePort = pm.getPort();
                ssltype=pm.getSsl();
            }
            rpcClient = new AgentXmlRpcClient(remoteHost, remotePort, ssltype);
        } catch (MalformedURLException ex)
        {
            agentExp(actElem);
        }
        return rpcClient;
    }

    /**
     * It is invoked when workflow end.
     * 
     * @param flowId
     * @throws ServerException
     */
    static public void workflowStopped ( long flowId, boolean isSafeFirst ) throws ServerException
    {
        implStopWorkflow(false, flowId, isSafeFirst, false);
        _log.info("info agent workflow end. flowid=" + flowId);
    }

    private static void implStopWorkflow ( boolean isStopWorkflow, long flowId, boolean isSafeFirst, boolean force )
            throws ServerException
    {
        _dbRexecReqWraperRep.removeFlowAllRexecReqWrapper(flowId);
    }

    /**
     * <li>Description:裸机安装,第一步调用的脚本</li> 
     * <AUTHOR>
     * 2018年8月9日 
     * @param agentIp
     * @param agentPort
     * @param script
     * @param pkgInfo
     * @param adpDefUuid
     * @param requestid
     * @param serverIp
     * @param serverPort
     * @param sysType
     * @param iid
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return Object
     */
    public static Object intall_Retn_StdOut ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, long iid,
            Connection con ) throws ServerException, RepositoryException
    {
        String SUCCESS = "1";    // 脚本返回值1是成功
        PreparedStatement ps = null;
        Process process = null;
        String state_fail = null;
        // InputStream stdin = null;
        // BufferedReader stdReader = null;
        String stdout = "";
        Object xml = "false";
        IInstallServerManager installServerManager = InstallServerManager.getInstance();

        try
        {
            String line = null;
            XmlRpcClient rpcClient;
            Vector params = new Vector();
            params.addElement(requestid);
            params.addElement(serverIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = new Hashtable();
            table.put("adaptorDefUUID", adpDefUuid);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("Id", requestid);
            table.put("serverHost", serverIp);
            table.put("serverPort", Integer.parseInt(serverPort));
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", agentPort);
            table.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("actName", "ShellCmd"); // 默认值
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", requestid);
            table.put("flowId", "-1");// 使用taskName可否?可以
            table.put("projectName", "shell_test");// ?
            table.put("flowName", "ShellCmd");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            Hashtable a = new Hashtable();
            if ("1".equals(sysType))
            {
                script = "cmd.exe /c " + script;
            } else if ("2".equals(sysType))
            {
                script = "sh " + script;
            }
            a.put("command", script);
            // a.put("params", pkgInfo);
            table.put("input", a);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                PerformDataProcessService pps = new PerformDataProcessService();
                ProxyModel pm = pps.getPerformDataJsonData(table, agentIp, agentPort, Constants.IEAI_IEAI_BASIC);
            }
            params.addElement(table);
            try
            {
                EngineRepository.getInstance().insertintoAutosingleState(requestid, table, script, pkgInfo);
            } catch (RepositoryException ex)
            {
                throw new ServerException(ex.getServerError().getErrCode());
            }
            rpcClient = getXmlRpcClient(agentIp, agentPort);
            // _log.info("pub_agent:" + agentIp + ":" + agentPort);

            for (int i = 0; i <= 2; i++)
            {
                try
                {
                    long start = System.currentTimeMillis();
                    xml = rpcClient.execute("IEAIAgent.executeAct", params);

                    if ("true".equals(xml.toString()))
                    {
                        Map pkgs = null;
                        int n = 0;
                        init();
                        while (true)
                        {
                            pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                            if (null != pkgs && pkgs.size() > 0)
                            {
                                String state = (String) pkgs.get("state");
                                if ("1".equals(state))
                                {
                                    _log.info("脚本执行异常,不能返回正确信息!");
                                    throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
                                }
                                stdout = (String) pkgs.get("realStdOut");

                                String state_success = "1";
                                state_fail = "2";
                                String rsstate = "";
                                if (SUCCESS.equals(stdout))
                                {
                                    rsstate = state_success;
                                } else
                                {
                                    // 于子洋调用，按照iid更新状态，状态是失败 =》状态是2
                                    rsstate = state_fail;
                                    // installServerManager.updateServerState(3, iid,
                                    // Constants.IEAI_SUS);
                                }
                                // 调用于子洋的方法更新状态
                                break;
                            } else
                            {
                                if (n > waitNum)
                                {
                                    throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                                }
                                try
                                {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e)
                                {
                                    Thread.currentThread().interrupt();
                                }
                            }
                            n++;
                        }
                    }
                } catch (Exception e)
                {
                    // 于子洋调用，按照iid更新状态，状态是失败 =》状态是2
                    // installServerManager.updateServerState(Integer.parseInt(state_fail), iid,
                    // Constants.IEAI_SUS);
                    if (i != 2)
                    {
                        _log.error("It will retry. pubType", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            // no code
                        }
                    } else
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }

        return xml;
    }

    /**
     * <li>Description:裸机安装，第二步检查 checkInit </li> 
     * <AUTHOR>
     * 2018年8月9日 
     * @param agentIp
     * @param agentPort
     * @param script
     * @param pkgInfo
     * @param adpDefUuid
     * @param requestid
     * @param serverIp
     * @param serverPort
     * @param sysType
     * @param iid
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return Object
     */
    public static String checkInit_Retn_StdOut ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, long iid,
            Connection con ) throws ServerException, RepositoryException
    {
        String SUCCESS = "1";    // 脚本返回值1是成功
        PreparedStatement ps = null;
        Process process = null;
        // InputStream stdin = null;
        // BufferedReader stdReader = null;
        String stdout = "";
        Object xml = "false";
        try
        {
            String line = null;
            XmlRpcClient rpcClient;
            Vector params = new Vector();
            params.addElement(requestid);
            params.addElement(serverIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = new Hashtable();
            table.put("adaptorDefUUID", adpDefUuid);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("Id", requestid);
            table.put("serverHost", serverIp);
            table.put("serverPort", Integer.parseInt(serverPort));
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", agentPort);
            table.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("actName", "ShellCmd"); // 默认值
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", requestid);
            table.put("flowId", "-1");// 使用taskName可否?可以
            table.put("projectName", "shell_test");// ?
            table.put("flowName", "ShellCmd");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            Hashtable a = new Hashtable();
            if ("1".equals(sysType))
            {
                script = "cmd.exe /c " + script;
            } else if ("2".equals(sysType))
            {
                script = "sh " + script;
            }
            a.put("command", script);
            // a.put("params", pkgInfo);
            table.put("input", a);
            boolean proxySwitch = ServerEnv.getInstance().getBooleanConfig(Environment.PROXY_START_WEBSERVICE_SWITCH,
                Environment.FALSE_BOOLEAN);
            if (proxySwitch)
            {
                PerformDataProcessService pps = new PerformDataProcessService();
                ProxyModel pm = pps.getPerformDataJsonData(table, agentIp, agentPort, Constants.IEAI_IEAI_BASIC);
            }
            params.addElement(table);
            try
            {
                EngineRepository.getInstance().insertintoAutosingleState(requestid, table, script, pkgInfo);
            } catch (RepositoryException ex)
            {
                throw new ServerException(ex.getServerError().getErrCode());
            }
            rpcClient = getXmlRpcClient(agentIp, agentPort);
            // _log.info("pub_agent:" + agentIp + ":" + agentPort);

            for (int i = 0; i <= 2; i++)
            {
                try
                {
                    long start = System.currentTimeMillis();
                    xml = rpcClient.execute("IEAIAgent.executeAct", params);

                    if ("true".equals(xml.toString()))
                    {
                        Map pkgs = null;
                        int n = 0;
                        init();
                        while (true)
                        {
                            pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                            if (null != pkgs && pkgs.size() > 0)
                            {
                                String state = (String) pkgs.get("state");
                                if ("1".equals(state))
                                {
                                    _log.info("脚本执行异常,不能返回正确信息!");
                                    throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
                                }
                                stdout = (String) pkgs.get("stdout");
                                break;
                            } else
                            {
                                if (n > waitNum)
                                {
                                    throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                                }
                                try
                                {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e)
                                {
                                    Thread.currentThread().interrupt();
                                }
                            }
                            n++;
                        }
                    }
                } catch (Exception e)
                {
                    if (i != 2)
                    {
                        _log.error("checkInit_Retn_StdOut error", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            // no code
                        }
                    } else
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }

        return stdout;
    }

    /**
     * <li>Description:裸机安装，第三步异在agent1上，异步执行的检查操作</li> 
     * <AUTHOR>
     * 2018年8月9日 
     * @param agentIp
     * @param agentPort
     * @param script
     * @param pkgInfo
     * @param adpDefUuid
     * @param requestid
     * @param serverIp
     * @param serverPort
     * @param sysType
     * @param iid
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return Object
     */
    public static String check_StdOut ( String agentIp, int agentPort, String script, String pkgInfo, String adpDefUuid,
            String requestid, String serverIp, String serverPort, String sysType, long iid,
            InstallServerBean installServerBean, Connection con ) throws ServerException, RepositoryException
    {
        String SUCCESS = "1";    // 脚本返回值1是成功
        PreparedStatement ps = null;
        Process process = null;
        // InputStream stdin = null;
        // BufferedReader stdReader = null;
        String stdout = "";
        Object xml = "false";
        try
        {
            String line = null;
            XmlRpcClient rpcClient;
            Vector params = new Vector();
            params.addElement(requestid);
            params.addElement(serverIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = new Hashtable();
            table.put("adaptorDefUUID", adpDefUuid);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("Id", requestid);
            table.put("serverHost", serverIp);
            table.put("serverPort", Integer.parseInt(serverPort));
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", agentPort);
            table.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("actName", "ShellCmd"); // 默认值
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", requestid);
            table.put("flowId", "-1");// 使用taskName可否?可以
            table.put("projectName", "shell_test");// ?
            table.put("flowName", "ShellCmd");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            Hashtable a = new Hashtable();
            if ("1".equals(sysType))
            {
                script = "cmd.exe /c " + script;
            } else if ("2".equals(sysType))
            {
                script = "sh " + script;
            }
            a.put("command", script);
            // a.put("params", pkgInfo);
            table.put("input", a);
            params.addElement(table);
            try
            {
                EngineRepository.getInstance().insertintoAutosingleState(requestid, table, script, pkgInfo);
            } catch (RepositoryException ex)
            {
                throw new ServerException(ex.getServerError().getErrCode());
            }
            rpcClient = getXmlRpcClient(agentIp, agentPort);
            // _log.info("pub_agent:" + agentIp + ":" + agentPort);

            for (int i = 0; i <= 2; i++)
            {
                try
                {
                    long start = System.currentTimeMillis();
                    xml = rpcClient.execute("IEAIAgent.executeAct", params);

                    if ("true".equals(xml.toString()))
                    {
                        Map pkgs = null;
                        int n = 0;
                        init();
                        while (true)
                        {
                            pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                            if (null != pkgs && pkgs.size() > 0)
                            {
                                String state = (String) pkgs.get("state");
                                if ("1".equals(state))
                                {
                                    _log.info("脚本执行异常,不能返回正确信息!");
                                    throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
                                }
                                stdout = (String) pkgs.get("stdout");

                                String state_success = "1";
                                String state_fail = "2";
                                String rsstate = "";
                                break;
                            } else
                            {
                                // 只要不超时，就用永远重试
                                /*
                                 * if (n > waitNum)
                                 * {
                                 * throw new ServerException(ServerError.
                                 * AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                                 * }
                                 */

                                try
                                {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e)
                                {
                                    Thread.currentThread().interrupt();
                                }
                            }
                            n++;
                        }
                    }
                } catch (Exception e)
                {
                    if (i != 2)
                    {
                        _log.error("It will retry. pubType", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            // no code
                        }
                    } else
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }

        return stdout;
    }

    public static Object divideVLAN_StdOut ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, long iid,
            Connection con ) throws ServerException, RepositoryException
    {
        String SUCCESS = "1";    // 脚本返回值1是成功
        PreparedStatement ps = null;
        Process process = null;
        // InputStream stdin = null;
        // BufferedReader stdReader = null;
        String stdout = "";
        Object xml = "false";
        try
        {
            String line = null;
            XmlRpcClient rpcClient;
            Vector params = new Vector();
            params.addElement(requestid);
            params.addElement(serverIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = new Hashtable();
            table.put("adaptorDefUUID", adpDefUuid);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("Id", requestid);
            table.put("serverHost", serverIp);
            table.put("serverPort", Integer.parseInt(serverPort));
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", agentPort);
            table.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("actName", "ShellCmd"); // 默认值
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", requestid);
            table.put("flowId", "-1");// 使用taskName可否?可以
            table.put("projectName", "shell_test");// ?
            table.put("flowName", "ShellCmd");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            Hashtable a = new Hashtable();
            if ("1".equals(sysType))
            {
                script = "cmd.exe /c " + script;
            } else if ("2".equals(sysType))
            {
                script = "sh " + script;
            }
            a.put("command", script);
            // a.put("params", pkgInfo);
            table.put("input", a);
            params.addElement(table);
            try
            {
                EngineRepository.getInstance().insertintoAutosingleState(requestid, table, script, pkgInfo);
            } catch (RepositoryException ex)
            {
                throw new ServerException(ex.getServerError().getErrCode());
            }
            rpcClient = getXmlRpcClient(agentIp, agentPort);
            // _log.info("pub_agent:" + agentIp + ":" + agentPort);

            for (int i = 0; i <= 2; i++)
            {
                try
                {
                    long start = System.currentTimeMillis();
                    xml = rpcClient.execute("IEAIAgent.executeAct", params);

                    if ("true".equals(xml.toString()))
                    {
                        Map pkgs = null;
                        int n = 0;
                        init();
                        while (true)
                        {
                            pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                            if (null != pkgs && pkgs.size() > 0)
                            {
                                String state = (String) pkgs.get("state");
                                if ("1".equals(state))
                                {
                                    _log.info("脚本执行异常,不能返回正确信息!");
                                    throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
                                }
                                stdout = (String) pkgs.get("realStdOut");

/*
 * String state_success="1";
 * String state_fail ="2";
 * String rsstate="";
 * if (SUCCESS.equals(stdout))
 * {
 * rsstate=state_success;
 * }else {
 * rsstate=state_fail;
 * }
 * InstallInfoManager.getInstance().updateServerStatus(rsstate,iid,Constants.IEAI_SUS);
 */
                                break;
                            } else
                            {
                                if (n > waitNum)
                                {
                                    throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                                }
                                try
                                {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e)
                                {
                                    Thread.currentThread().interrupt();
                                }
                            }
                            n++;
                        }
                    }
                } catch (Exception e)
                {
                    if (i != 2)
                    {
                        _log.error("It will retry. pubType", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            // no code
                        }
                    } else
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }

        return xml;
    }

    public static Object vmInstall_StdOut ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, long iid,
            Connection con ) throws ServerException, RepositoryException
    {
        String SUCCESS = "1";    // 脚本返回值1是成功
        PreparedStatement ps = null;
        Process process = null;
        // InputStream stdin = null;
        // BufferedReader stdReader = null;
        String stdout = "";
        Object xml = "false";
        try
        {
            String line = null;
            XmlRpcClient rpcClient;
            Vector params = new Vector();
            params.addElement(requestid);
            params.addElement(serverIp);
            params.addElement(Integer.parseInt(serverPort));
            Hashtable table = new Hashtable();
            table.put("adaptorDefUUID", adpDefUuid);
            table.put("_scopeId", "162865");// ?
            table.put("adaptorConfig", 1);// shellcmd
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("Id", requestid);
            table.put("serverHost", serverIp);
            table.put("serverPort", Integer.parseInt(serverPort));
            table.put("_actStateDataVersion", -1);// ?
            table.put("timeout", "0");// ?
            table.put("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((new Date()).getTime()));
            table.put("agentHost", agentIp);
            table.put("agentPort", agentPort);
            table.put("actId", "ShellCmd");// 是否是固定值ShellCmd?
            table.put("adaptorDefName", "shellcmd");// shellcmd
            table.put("actName", "ShellCmd"); // 默认值
            table.put("status", 2);// ? 默认值
            table.put("levelOfWeight", "1");// ? 默认值
            table.put("Id", requestid);
            table.put("flowId", "-1");// 使用taskName可否?可以
            table.put("projectName", "shell_test");// ?
            table.put("flowName", "ShellCmd");// 使用taskName可否?可以
            table.put("flowPoolNum", "0");// ?默认值
            table.put("isSafeFirst", true); // ?默认值
            table.put("levelOfPRI", "5");// ?默认值
            Hashtable a = new Hashtable();
            if ("1".equals(sysType))
            {
                script = "cmd.exe /c " + script;
            } else if ("2".equals(sysType))
            {
                script = "sh " + script;
            }
            a.put("command", script);
            // a.put("params", pkgInfo);
            table.put("input", a);
            params.addElement(table);
            try
            {
                EngineRepository.getInstance().insertintoAutosingleState(requestid, table, script, pkgInfo);
            } catch (RepositoryException ex)
            {
                throw new ServerException(ex.getServerError().getErrCode());
            }
            rpcClient = getXmlRpcClient(agentIp, agentPort);
            // _log.info("pub_agent:" + agentIp + ":" + agentPort);

            for (int i = 0; i <= 2; i++)
            {
                try
                {
                    long start = System.currentTimeMillis();
                    xml = rpcClient.execute("IEAIAgent.executeAct", params);

                    if ("true".equals(xml.toString()))
                    {
                        Map pkgs = null;
                        int n = 0;
                        init();
                        while (true)
                        {
                            pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                            if (null != pkgs && pkgs.size() > 0)
                            {
                                String state = (String) pkgs.get("state");
                                if ("1".equals(state))
                                {
                                    _log.info("脚本执行异常,不能返回正确信息!");
                                    throw new ServerException(ServerError.ERR_REMOTE_ACT_EXEC_FAILED);
                                }
                                stdout = (String) pkgs.get("realStdOut");

/*
 * String state_success="1";
 * String state_fail ="2";
 * String rsstate="";
 * if (SUCCESS.equals(stdout))
 * {
 * rsstate=state_success;
 * }else {
 * rsstate=state_fail;
 * }
 * InstallInfoManager.getInstance().updateServerStatus(rsstate,iid,Constants.IEAI_SUS);
 */
                                break;
                            } else
                            {
                                if (n > waitNum)
                                {
                                    throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                                }
                                try
                                {
                                    Thread.sleep(2000);
                                } catch (InterruptedException e)
                                {
                                    Thread.currentThread().interrupt();
                                }
                            }
                            n++;
                        }
                    }
                } catch (Exception e)
                {
                    if (i != 2)
                    {
                        _log.error("It will retry. pubType", e);
                        try
                        {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex)
                        {
                            // no code
                        }
                    } else
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
                    }
                }
            }
        } catch (AgentCommunicationException e)
        {
            throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE, e);
        }

        return xml;
    }

    /**
     * <li>Description:保存步骤运行信息及异常信息</li>
     * 
     * <AUTHOR> 2016年1月13日
     * @param request
     * @param actElem
     * @param requestUUid
     * @param Output return void
     */
    private static void saveVmManagerOutPut ( String requestUUid, Hashtable<String, String> Output )
    {
        LobStorer ls = new LobStorer();
        try
        {
            // 标准输出
            String lastLine = "";
            String ret = "";
            StringBuffer standardoutput = new StringBuffer();
            if (null != Output.get("stderr"))
            {

                standardoutput.append((String) Output.get("stderr"));
            }
            if (null != Output.get("stdout"))
            {

                standardoutput.append((String) Output.get("stdout"));
            }
            if (null != Output.get("lastLine"))
            {

                lastLine = Output.get("lastLine");
            }

            if (requestUUid.indexOf("ieai-vmVersion-svn") > -1)
            {
                ls.saveVmManagerPathOutputClobNew(requestUUid, String.valueOf(standardoutput), lastLine,
                    Constants.IEAI_IEAI_BASIC);
            } else if (requestUUid.indexOf("ieai-vmVersion-download") > -1)
            {
                ls.saveVmManagerUploadCodeOutput(requestUUid, String.valueOf(standardoutput), lastLine,
                    Constants.IEAI_IEAI_BASIC);
            } else if (requestUUid.indexOf("ieai-vmVersion-mergec") > -1)
            {
                ls.saveVmManagerPathOutputClobNew(requestUUid, String.valueOf(standardoutput), lastLine,
                    Constants.IEAI_IEAI_BASIC);
            } else if (requestUUid.indexOf("ieai-vmVersion-logview") > -1)
            {
                ls.saveVmManagerSubmitLogOutput(requestUUid, String.valueOf(standardoutput), lastLine,
                    Constants.IEAI_IEAI_BASIC);
            } else if (requestUUid.indexOf("ieai-vmVersion-mergem") > -1)
            {
                ls.saveVmManagerMergem(requestUUid, String.valueOf(standardoutput), lastLine,
                    Constants.IEAI_IEAI_BASIC);
            }
        } catch (Exception e)
        {
            _log.error("ActivityRExecHelper.saveVmManagerOutPut error:" + e + "\n");
        }
    }

    /**
     * <li>Description:大包构建的返回值</li> 
     * <AUTHOR>
     * 2019年12月19日 
     * @param agentIp
     * @param agentPort
     * @param script
     * @param pkgInfo
     * @param adpDefUuid
     * @param requestid
     * @param serverIp
     * @param serverPort
     * @param sysType
     * @param con
     * @return
     * @throws ServerException
     * @throws RepositoryException
     * return String
     */
    public static String getBigPkg_Retn_StdOut ( String agentIp, int agentPort, String script, String pkgInfo,
            String adpDefUuid, String requestid, String serverIp, String serverPort, String sysType, Connection con,
            Map paramMap ) throws ServerException, RepositoryException
    {
        Object xml = getXml(serverIp, adpDefUuid, requestid, serverPort, agentIp, agentPort, sysType, script, pkgInfo,
            paramMap);
        String stdout = "";
        if ("true".equals(xml.toString()))
        {
            Map pkgs = null;
            int n = 0;
            init();
            while (true)
            {
                pkgs = EngineRepository.getInstance().getAutoActState(requestid, con);
                if (null != pkgs && pkgs.size() > 0)
                {
                    stdout = (String) pkgs.get("realStdOut");
                    break;
                } else
                {
                    if (n > waitNum)
                    {
                        throw new ServerException(ServerError.AGENT_COMMUNICATION_EXCEPTION_ERR_CODE);
                    }
                    try
                    {
                        Thread.sleep(2000);
                    } catch (InterruptedException e)
                    {
                        Thread.currentThread().interrupt();
                    }
                }
                n++;
            }
        }

        return stdout;
    }
    
    /**
     * 
     * <li>Description:网络自动化任务处理</li> 
     * <AUTHOR>
     * 2019年7月6日 
     * @param agentIp
     * @param agentPort
     * @param runInfoId
     * @param shellPath
     * @param shellScript
     * @param type
     * @return
     * @throws ServerException
     * return String
     */
    public static String newWorkActStart ( String agentIp, long agentPort,  long runInfoId,String shellPath,String shellScript,String execUser,String expResult ,int type) throws Exception
    {
        ReleaseMonitorManager.getInstance().deleteNetWorkOutputByStepId(runInfoId,type);
        //工具agent
        String networkAgentIp = ServerEnv.getInstance().getSysConfig(Environment.NEW_WORK_AGENTIP);
        int networkAgentPort=15000;
        Object xml = "";
        boolean flag = true;
        String errorMsg = "";
        String version = ReleaseMonitorManager.getInstance().getVersionByRunInfoId(runInfoId,type);
        for(String agnetip:networkAgentIp.split(","))
        { 
            flag = true;
            errorMsg="";
            try
            {
                String[] ttt = agnetip.split(":");
                if(ttt.length==2) {
                    agnetip=ttt[0];
                    networkAgentPort=Integer.parseInt(ttt[1]);
                }
                XmlRpcClient rpcClient;
                Vector params = new Vector();
                params.addElement(agentIp);//目标agent
                params.addElement((int)agentPort);//目标agent
                params.addElement(String.valueOf(runInfoId));
                params.addElement(shellPath==null?"":shellPath);
                params.addElement(shellScript==null?"":shellScript);//执行命令
                params.addElement("");//用户名
                params.addElement(execUser);//执行用户
                params.addElement(expResult);//预期值
                params.addElement(version);//单号
                _log.info("执行agent:" + agnetip + ":" + networkAgentPort + " runInfoId:"+runInfoId);
                _log.info("目标agent:" + agentIp + ":" + agentPort+ " runInfoId:"+runInfoId);
                rpcClient = getXmlRpcClient(agnetip, networkAgentPort);
                ActivityElement actElem = new ActivityElement(0, null, 0);
                actElem.setRemotePort(networkAgentPort);
                actElem.setRemoteHost(agnetip);
                actElem.setName("ShellCmd");
                actElem.setConnectType(0);
                // Agent当前时间与Server当前时间比对
                ActivityRExecHelper.chechAgentCurrentTime(rpcClient, actElem);
                xml = rpcClient.execute("IEAIAgent.executeAct_netWork", params);
                _log.info("executeAct_netWork return:" + xml.toString()+ " runInfoId:"+runInfoId);
                break;
            } catch (AgentCommunicationException e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("AgentCommunicationException on executeAct_netWork :"+e,e);
            } catch (XmlRpcException e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("XmlRpcException on executeAct_netWork :"+e,e);
            } catch (IOException e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("IOException on executeAct_netWork :"+e,e);
            }catch (Exception e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("Exception on executeAct_netWork :"+e,e);
            }
        }
        if(!flag)
        {
            LobStorer lo = new LobStorer();
            ReleaseMonitorManager.getInstance().updateRunInfoById(runInfoId, 1, type);
            try
            {
                boolean flag1 =lo.saveActOutputClob(0, runInfoId, "network", 3, null, 0l, new Date(), null, errorMsg,
                    null,type,"","");
//                if(Constants.IEAI_EMERGENCY_OPER == type){
//                    String cmdid = map.get("iid").toString();
//                    String stepid = map.get("stepid").toString();
//                    ItsmEmExceptionSendMessagesThread thread= new ItsmEmExceptionSendMessagesThread(cmdid,stepid);
//                    thread.start();
//                }

            } catch (RepositoryException e)
            {
                _log.error(" executeAct_netWork saveActOutputClob error："+e);
            }
            throw new Exception(errorMsg);
        }
        _log.info(" executeAct_netWork return："+xml.toString());
        String mes = "";
        LobStorer lo = new LobStorer();
        try
        {
            JSONObject obj = JSON.parseObject(xml.toString());
            if(obj.getInteger("state")!=0)
            {
                mes=obj.getString("mes");
                ReleaseMonitorManager.getInstance().updateRunInfoById(runInfoId, 1, type);
                boolean flag1 =lo.saveActOutputClob(0, runInfoId, "network", 3, null, 0l, new Date(), null, mes,
                    null,type,"","");
//                if(Constants.IEAI_EMERGENCY_OPER == type){
//                    String cmdid = map.get("iid").toString();
//                    String stepid = map.get("stepid").toString();
//                    ItsmEmExceptionSendMessagesThread thread= new ItsmEmExceptionSendMessagesThread(cmdid,stepid);
//                    thread.start();
//                }
                throw new Exception(mes);
            }
        }catch(Exception e)
        {
            ReleaseMonitorManager.getInstance().updateRunInfoById(runInfoId, 1, type);
            try
            {
                boolean flag1 =lo.saveActOutputClob(0, runInfoId, "network", 3, null, 0l, new Date(), null, e.getMessage(),
                    null,type,"","");
//                if(Constants.IEAI_EMERGENCY_OPER == type){
//                    String cmdid = map.get("iid").toString();
//                    String stepid = map.get("stepid").toString();
//                    ItsmEmExceptionSendMessagesThread thread= new ItsmEmExceptionSendMessagesThread(cmdid,stepid);
//                    thread.start();
//                }
            } catch (RepositoryException e1)
            {
                _log.error(" executeAct_netWork saveActOutputClob error："+e);
            }
            _log.error(" executeAct_netWork return error："+e);
            throw new Exception(errorMsg);
        }
        return mes;
    }
    
    
    /**
     * 
     * @Title: chechAgentCurrentTime   
     * @Description:Agent当前时间与Server当前时间比对   
     * @param: @param rpcClient
     * @param: @param actElem
     * @param: @return
     * @param: @throws XmlRpcException
     * @param: @throws IOException
     * @param: @throws AgentCommunicationException      
     * @return: boolean      
     * @throws   
     * @author: yunpeng_zhang 
     * @date:   2017年11月1日 上午9:04:43
     */
    public static boolean chechAgentCurrentTime ( XmlRpcClient rpcClient, ActivityElement actElem )
            throws XmlRpcException, IOException, AgentCommunicationException
    {
        boolean returnValue = true;
        String actmSwitch = Environment.getInstance().getAgentCurrentTimeMillisSwitch();
        // 判断快关是否打开
        if ("true".equals(actmSwitch))
        {
            // Agent当前时间与Server当前时间比对的阈值
            int actmThreshold = Environment.getInstance().getAgentCurrentTimeMillisThreshold();
            long serverCurrentTime = System.currentTimeMillis();
            // 获取agent当前时间
            String agentCurrentTimeS = (String) rpcClient.execute("IEAIAgent.getAgentCurrentTimeMillis", new Vector());
            long agentCurrentTime = Long.parseLong(agentCurrentTimeS);
            // 如果Agent当前时间与Server当前时间比对的结果的分钟数大于阈值，则报异常
            if (actmThreshold < (Math.abs(serverCurrentTime - agentCurrentTime) / 1000 / 60))
            // test data ↓
            // if (actmThreshold > (Math.abs(serverCurrentTime - agentCurrentTime) / 1000 / 60))
            {
                AgentCommunicationException agentExp = new AgentCommunicationException(new Exception());
                agentExp.setAgentPort(String.valueOf(actElem.getRemotePort()));
                agentExp.setAgentHost(actElem.getRemoteHost());
                agentExp.setConnectionType(actElem.getConnectType());
                agentExp.setActivityName(actElem.getName());
                agentExp.setLocation(ActivityException.AGENT_COMMUNICATION_EXCEPTION);
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy年-MM月dd日-HH时mm分ss秒");
                agentExp.setMessage("Agent当前时间(" + formatter.format(new Date(agentCurrentTime)) + ")与Server当前时间("
                        + formatter.format(new Date(serverCurrentTime))
                        + ")比对的结果超过阈值(" + actmThreshold + ")!不继续执行!");
                agentExp.setTime(new Date());
                throw agentExp;
            }
        }
        return returnValue;
    }
    
    /**
     * 
     * <li>Description:网络自动化批量执行</li> 
     * <AUTHOR>
     * 2020年6月4日 
     * @param agentIp
     * @param agentPort
     * @param runInfoId
     * @param shellPath
     * @param shellScript
     * @param execUser
     * @param expResult
     * @param type
     * @return
     * @throws Exception
     * return String
     */
    public static String entegorNet_Act (  long runInfoId,String actName,String execUser ,int type) throws Exception
    {
        ReleaseMonitorManager.getInstance().deleteNetWorkOutputByStepId(runInfoId,type);
        String sceneNam = "";
        String version = ReleaseMonitorManager.getInstance().getVersionByRunInfoId(runInfoId,type);
        //工具agent
        String networkAgentIp = ServerEnv.getInstance().getSysConfig(Environment.NEW_WORK_AGENTIP);
        int networkAgentPort=15000;
        Object xml = "";
        boolean flag = true;
        String errorMsg = "";
        if( actName!= null && !"".equals(actName)){
            if(actName.contains("-")){
                sceneNam = actName.split("-")[1];
            }else{
                sceneNam = actName;
            }
            
        }
        if(networkAgentIp==null || "".equals(networkAgentIp)){
            String ret = "没有配置网络自动化工具agent!";
            return ret;
        }
        for(String agnetip:networkAgentIp.split(","))
        { 
            flag = true;
            errorMsg="";
            try
            {
                String[] ttt = agnetip.split(":");
                if(ttt.length==2) {
                    agnetip=ttt[0];
                    networkAgentPort=Integer.parseInt(ttt[1]);
                }
                XmlRpcClient rpcClient;
                Vector params = new Vector();
                params.addElement(String.valueOf(runInfoId));
                params.addElement(sceneNam);//场景名称
                params.addElement(type);
                params.addElement(version);//变更单号
                _log.info("执行agent:" + agnetip + ":" + networkAgentPort + " runInfoId:"+runInfoId);
                rpcClient = getXmlRpcClient(agnetip, networkAgentPort);
                ActivityElement actElem = new ActivityElement(0, null, 0);
                actElem.setRemotePort(networkAgentPort);
                actElem.setRemoteHost(agnetip);
                actElem.setName("ShellCmd");
                actElem.setConnectType(0);
                // Agent当前时间与Server当前时间比对
                ActivityRExecHelper.chechAgentCurrentTime(rpcClient, actElem);
                xml = rpcClient.execute("IEAIAgent.entegorNet_Act", params);
                _log.info("entegorNet_Act return:" + xml.toString()+ " runInfoId:"+runInfoId);
                break;
            } catch (AgentCommunicationException e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("AgentCommunicationException on executeAct_netWork :"+e,e);
            } catch (XmlRpcException e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("XmlRpcException on executeAct_netWork :"+e,e);
            } catch (IOException e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("IOException on executeAct_netWork :"+e,e);
            }catch (Exception e)
            {
                flag=false;
                errorMsg=e.getMessage();
                _log.error("Exception on executeAct_netWork :"+e,e);
            }
        }
        if(!flag)
        {
            LobStorer lo = new LobStorer();
            ReleaseMonitorManager.getInstance().updateRunInfoById(runInfoId, 1, type);
            try
            {
                boolean flag1=lo.saveActOutputClob(0, runInfoId, "networkbatch", 3, null, 0l, new Date(), null, errorMsg,
                    null,type,"","");
//                if(Constants.IEAI_EMERGENCY_OPER == type){
//                    String cmdid = map.get("iid").toString();
//                    String stepid = map.get("stepid").toString();
//                    ItsmEmExceptionSendMessagesThread thread= new ItsmEmExceptionSendMessagesThread(cmdid,stepid);
//                    thread.start();
//                }

            } catch (RepositoryException e)
            {
                _log.error(" entegorNet_Act saveActOutputClob error："+e);
            }
            throw new Exception(errorMsg);
        }
        _log.info(" entegorNet_Act return："+xml.toString());
        String mes = "";
        LobStorer lo = new LobStorer();
        try
        {
            JSONObject obj = JSON.parseObject(xml.toString());
            if(obj.getInteger("state")!=0)
            {
                mes=obj.getString("mes");
                ReleaseMonitorManager.getInstance().updateRunInfoById(runInfoId, 1, type);
                boolean flag1=lo.saveActOutputClob(0, runInfoId, "networkbatch", 3, null, 0l, new Date(), null, mes,
                    null,type,"","");
//                if(Constants.IEAI_EMERGENCY_OPER == type){
//                    String cmdid = map.get("iid").toString();
//                    String stepid = map.get("stepid").toString();
//                    ItsmEmExceptionSendMessagesThread thread= new ItsmEmExceptionSendMessagesThread(cmdid,stepid);
//                    thread.start();
//                }
                throw new Exception(mes);
            }
        }catch(Exception e)
        {
            ReleaseMonitorManager.getInstance().updateRunInfoById(runInfoId, 1, type);
            try
            {
                boolean flag1=lo.saveActOutputClob(0, runInfoId, "networkbatch", 3, null, 0l, new Date(), null, e.getMessage(),
                    null,type,"","");
//                if(Constants.IEAI_EMERGENCY_OPER == type){
//                    String cmdid = map.get("iid").toString();
//                    String stepid = map.get("stepid").toString();
//                    ItsmEmExceptionSendMessagesThread thread= new ItsmEmExceptionSendMessagesThread(cmdid,stepid);
//                    thread.start();
//                }
            } catch (RepositoryException e1)
            {
                _log.error(" entegorNet_Act saveActOutputClob error："+e);
            }
            _log.error(" entegorNet_Act return error："+e);
            throw new Exception(errorMsg);
        }
        return mes;
    }
    
    public static AgentIpMainBackupsBean   AgentipMainBackups(String reqId, ActivityElement actElem) throws AgentCommunicationException, ServerException{
        AgentIpMainBackupsBean agentIP = new AgentIpMainBackupsBean();
        AgentResourceWebConfigBean agentResourceWebConfigBeanHD=new AgentResourceWebConfigBean();
        AgentResourceWebConfigBean agentResourceWebConfigBean=new AgentResourceWebConfigBean();
        Map<String, Object>  resultmap = new HashMap<String, Object>();
        RExecRequestWrapper requestWrapper = getReqWraper(reqId, Constants.IEAI_IEAI_BASIC);
        RemoteActivityExecRequest request = requestWrapper.getRexecRequest();
        agentResourceWebConfigBeanHD.setIprjName(request.getProjectName());
        agentResourceWebConfigBeanHD.setFlowName(request.getFlowName());
        agentResourceWebConfigBeanHD.setActName(actElem.getName());
        agentResourceWebConfigBeanHD.setResourceName(actElem.getRemoteAgent());
        String remoteHost="";
        long AgentPort = 0;
        String agentipback="";
        long agentportback = 0;
        try {
            List dataList1 = new ArrayList();
            resultmap= AgentResourceWebConfigManager.getInstance().getAgentReportInfoAct(agentResourceWebConfigBeanHD);
            dataList1=  (List) resultmap.get("dataList");
            if(resultmap!=null&&dataList1.size()!=0){

                for (int i = 0; i < dataList1.size(); i++){
                    Map map1=(Map) dataList1.get(i);

                    remoteHost= (String) map1.get("agentip") == null ? "" : (String) map1.get("agentip");
                    AgentPort=     (Long) map1.get("agentport") ;
                    agentipback= (String) map1.get("agentipback") == null ? "" : (String) map1.get("agentipback");
                    agentportback=     (Long) map1.get("agentportback") ;


                }
                if(!"".equals(remoteHost)){
                    agentIP.setAgentHost(remoteHost);
                    agentIP.setAgentPort(Math.toIntExact(AgentPort));
                    agentIP.setAgentHostBak(agentipback);
                    agentIP.setAgentPortBak(Math.toIntExact(agentportback));
                    _log.info("Agent MAIN:" + "remotePort:" + AgentPort+ ",agent:"
                            + remoteHost+ "，remoteAgent:" + actElem.getRemoteAgent()+ "  ");
                }
            }else {
                agentResourceWebConfigBean.setIprjName(request.getProjectName());
                agentResourceWebConfigBean.setResourceName(actElem.getRemoteAgent());
                resultmap = AgentResourceWebConfigManager.getInstance().getAgentReportInfoAct(agentResourceWebConfigBean);
                dataList1=  (List) resultmap.get("dataList");
                if (resultmap != null && dataList1.size() != 0) {
                    for (int i = 0; i < dataList1.size(); i++) {
                        Map map2 = (Map) dataList1.get(i);
                        remoteHost=(String) map2.get("agentip") == null ? "" : (String) map2.get("agentip");
                        AgentPort= (Long) map2.get("agentport") ;
                        agentipback= (String) map2.get("agentipback") == null ? "" : (String) map2.get("agentipback");
                        agentportback=     (Long) map2.get("agentportback") ;

                    }
                    if(!"".equals(remoteHost)){
                        agentIP.setAgentHost(remoteHost);
                        agentIP.setAgentPort(Math.toIntExact(AgentPort));
                        agentIP.setAgentHostBak(agentipback);
                        agentIP.setAgentPortBak(Math.toIntExact(agentportback));
                        _log.info("Agent MAIN:" + "remotePort:" + AgentPort+ ",agent:"
                                + remoteHost+ "，remoteAgent:" + actElem.getRemoteAgent()+ "  ");
                    }
                }


            }

        } catch (RepositoryException e) {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return agentIP;
    }

    /**
     * 发告警：agent连接超时
     * @param agent
     */
    public static void warnAgentOutTime ( Agent agent )
    {
        String message = "ip为" + agent.getIagentip() + ",端口为" + agent.getIagentport() + "连接超时。";
        IeaiWarnModel ieaiWarnModel = new IeaiWarnModel();
        ieaiWarnModel.setImodulecode("platform");
        ieaiWarnModel.setItypecode("agentconnectouttime");
        ieaiWarnModel.setIlevelcode("five");
        ieaiWarnModel.setIip(agent.getIagentip());
        ieaiWarnModel.setIwarnmsg(message);
        ieaiWarnModel.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
        WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
        warningInterfaceUtilsPlatform.callWarning(ieaiWarnModel, true);
    }
    
    /**
     * 发告警：agent管理中agent状态由异常变为正常
     * 
     */
    public static void warnAgentAbnormalToNormal ( String agentip,int agentPort)
    {
        String message = "ip为" + agentip + ",端口为" + agentPort + "状态由异常变为正常。";
        IeaiWarnModel ieaiWarnModel = new IeaiWarnModel();
        ieaiWarnModel.setImodulecode("platform");
        ieaiWarnModel.setItypecode("agentreconnect");
        ieaiWarnModel.setIlevelcode("five");
        ieaiWarnModel.setIip(agentip);
        ieaiWarnModel.setIwarnmsg(message);
        ieaiWarnModel.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
        WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
        warningInterfaceUtilsPlatform.callWarning(ieaiWarnModel, true);
    }
    
    public static void sendWarnOrNot(long agentGroupid) {
        
        Connection con = null;
        try
        {
            con = DBResource.getConnection("", _log, Constants.IEAI_IEAI);
            //agent组下是否有agent
            boolean agentGroupHaveAgentFlag=isagentGroupHaveAgent(agentGroupid,con);
            //agent组是否有未恢复的告警
            boolean agentGroupHaveWarnFlag=isagentGroupHaveWarn(agentGroupid,con);
            if(agentGroupHaveAgentFlag) {
                //agent组有agent时判断是否产生恢复告警
                
                if(agentGroupHaveWarnFlag) {
                    //恢复告警
                    warnAgentGroupNoAgentToHave(agentGroupid);  
                }
            }else {
                //判断是否已经产生过告警且未恢复                
                if(!agentGroupHaveWarnFlag) {
                  //agent组没有agent时 告警
                    warnAgentGroupNoAgent(agentGroupid);
                }
              
            }
        }catch(Exception e) {
            _log.error("sendWarnOrNot is error ",e);
        }finally {
            DBResource.closeConnection(con, "sendWarnOrNot", _log);
        }
        
    }
    
    public static boolean isagentGroupHaveAgent (Long groupId,Connection conn)
    {

        PreparedStatement ps = null;
        ResultSet rset = null;
        boolean flag = false;
        try
        {
            String sql = "select count(*) counts from IEAI_AGENTINFO_GROUP where igroupid= ?  ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, groupId);
            rset = ps.executeQuery();

            while (rset.next())
            {
                if( rset.getInt("counts")>0) {
                    flag =  true;
                }
            }
        } catch (Exception e)
        {
            _log.error("ActivityRExecHelper.isagentGroupHaveAgent is error",e);
        }finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return flag;
    }
    
    public static boolean isagentGroupHaveUsefulAgent (Long groupId,Connection conn)
    {

        PreparedStatement ps = null;
        ResultSet rset = null;
        boolean flag = false;
        try
        {         
            
            String sql = "select count(*) counts from IEAI_AGENTINFO_GROUP A left join IEAI_AGENTINFO C on A.INODEID=C.IAGENTINFO_ID  where A.igroupid= ? and C.Iagent_State=?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, groupId);
            ps.setInt(2, Constants.REMOTE_AGENT_STATE_NORMAL);
            rset = ps.executeQuery();

            while (rset.next())
            {
                if( rset.getInt("counts")>0) {
                    flag =  true;
                }
            }
        } catch (Exception e)
        {
            _log.error("ActivityRExecHelper.isagentGroupHaveAgent is error",e);
        }finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return flag;
    }
    
    public static boolean isagentGroupHaveWarn (Long groupId,Connection conn)
    {

        PreparedStatement ps = null;
        ResultSet rset = null;
        boolean flag = false;
        try
        {
            String sql = " select imodulecode,itypecode from IEAI_WARN where iwarnid= (select max(iwarnid) from IEAI_WARN where iip= ? and imodulecode='platform' )  " ;
            ps = conn.prepareStatement(sql);
            ps.setString(1, groupId+"");
            rset = ps.executeQuery();

            while (rset.next())
            {
//                if( rset.getInt("counts")>0) {
//                    flag =  true;
//                }
                String itypecode=rset.getString("itypecode");
                if("agentnotconnect".equals(itypecode)) {
                    return true;
                }else {
                    return false;
                }
            }
        } catch (Exception e)
        {
            _log.error("ActivityRExecHelper.isagentGroupHaveWarn is error",e);
        }finally
        {
            DBResource.closePSRS(rset, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return flag;
    }
    
    /**
     * 发告警：agent组内没有agent
     * 
     */
    public static  void warnAgentGroupNoAgent (long agentGroupid)
    {
        String warnFlag=ServerEnv.getInstance().getSysConfig("agentgroup.warn.recoverwarn", "not.warn");
        String mes="]内无agent。";
        if("warn.no.useful.agent".equals(warnFlag)) {
            mes="]内无可用agent。";
        }
        String message = "Agent连接异常，agent组[" + agentGroupid + mes;
        IeaiWarnModel ieaiWarnModel = new IeaiWarnModel();
        ieaiWarnModel.setImodulecode("platform");
        ieaiWarnModel.setItypecode("agentnotconnect");
        ieaiWarnModel.setIlevelcode("five");
        ieaiWarnModel.setIip(agentGroupid+"");
        ieaiWarnModel.setIflowid(agentGroupid);
        ieaiWarnModel.setIwarnmsg(message);
        ieaiWarnModel.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
        WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
        warningInterfaceUtilsPlatform.callWarning(ieaiWarnModel, true);
    }
    
    /**
     * 发告警：agent组内变为有agent时恢复告警
     * 
     */
    public static  void warnAgentGroupNoAgentToHave (long agentGroupid)
    {
        String warnFlag=ServerEnv.getInstance().getSysConfig("agentgroup.warn.recoverwarn", "not.warn");
        String mes="]内有agent。";
        if("warn.no.useful.agent".equals(warnFlag)) {
            mes="]内有可用agent。";
        }
        String message = "Agent连接恢复正常，agent组[" + agentGroupid + mes;
        IeaiWarnModel ieaiWarnModel = new IeaiWarnModel();
        ieaiWarnModel.setImodulecode("platform");
        ieaiWarnModel.setItypecode("agentreconnect");
        ieaiWarnModel.setIlevelcode("five");        
        ieaiWarnModel.setIip(agentGroupid+"");
        ieaiWarnModel.setIflowid(agentGroupid);
        ieaiWarnModel.setIwarnmsg(message);
        ieaiWarnModel.setIhappentimeStr(DateUtil.formatDateTime(new Date()));
        WarningInterfaceUtilsPlatform warningInterfaceUtilsPlatform = new WarningInterfaceUtilsPlatform();
        warningInterfaceUtilsPlatform.callWarning(ieaiWarnModel, true);
    }
    
    public static  void sendWarnOrNotNew(long agentGroupid) {
               
        try
        {
            
            String warnFlag=ServerEnv.getInstance().getSysConfig("agentgroup.warn.recoverwarn", "not.warn");
//            if("not.warn".equals(warnFlag)) {
//                return;
//            }else 
            if("warn.no.agent".equals(warnFlag)) {
                sendWarnGroupNoAgent(agentGroupid);
            }else if("warn.no.useful.agent".equals(warnFlag)) {
                sendWarnGroupNoUsefulAgent(agentGroupid);
            }
          
        }catch(Exception e) {
            _log.error("sendWarnOrNotNew is error ",e);
        }
    }
    
    
    private static  void sendWarnGroupNoAgent(long agentGroupid) {
        
        
        Connection con = null;
        try
        {
            con = DBResource.getConnection("sendWarnGroupNoAgent", _log, Constants.IEAI_IEAI);
            boolean isGroupHaveAgebtFlag=ActivityRExecHelper.isagentGroupHaveAgent(agentGroupid,con);
          
          //agent组 是否已有告警且未恢复
            boolean agentGroupHaveWarnFlag=isagentGroupHaveWarn(agentGroupid,con);
            if(isGroupHaveAgebtFlag) {
                if(agentGroupHaveWarnFlag) {
                    //恢复告警
                    ActivityRExecHelper.warnAgentGroupNoAgentToHave(agentGroupid);  
                }
              
            }else {
                if(!agentGroupHaveWarnFlag) {
                    //agent组没有agent时且未产生过告警或告警已恢复时 告警
                      ActivityRExecHelper.warnAgentGroupNoAgent(agentGroupid);  
                  }
              
               
            }
        }catch(Exception e) {
            _log.error("sendWarnGroupNoAgent is error ",e);
        }finally {
            DBResource.closeConnection(con, "sendWarnGroupNoAgent", _log);
        }
    }
    
//    private static  void sendWarnGroupNoUsefulAgent(String[] nodeInfo,long agentGroupid) {
//        
//        
//        Connection con = null;
//        try
//        {
//            con = DBResource.getConnection("sendWarnGroupNoUsefulAgent", _log, Constants.IEAI_IEAI);
//           // List list=listmap.get("agentFree");
//            
//          //agent组 是否已有告警且未恢复
//            boolean agentGroupHaveWarnFlag=isagentGroupHaveWarn(agentGroupid,con);
//            if(null == nodeInfo ||null == nodeInfo[0] || "".equals(nodeInfo[0]) || null == nodeInfo[1] || "".equals(nodeInfo[1])) {
//               if(!agentGroupHaveWarnFlag) {
//                 //agent组没有可用agent时且未产生过告警或告警已恢复时 告警
//                   ActivityRExecHelper.warnAgentGroupNoAgent(agentGroupid);  
//               }
//              
//            }else {
//              
//                if(agentGroupHaveWarnFlag) {
//                    //恢复告警
//                    ActivityRExecHelper.warnAgentGroupNoAgentToHave(agentGroupid);  
//                }
//            }
//        }catch(Exception e) {
//            _log.error("sendWarnOrNot is error ",e);
//        }finally {
//            DBResource.closeConnection(con, "sendWarnOrNot", _log);
//        }
//    }
    
    private static  void sendWarnGroupNoUsefulAgent(long agentGroupid) {
        
        
        Connection con = null;
        try
        {
            con = DBResource.getConnection("sendWarnGroupNoAgent", _log, Constants.IEAI_IEAI);
            boolean isGroupHaveAgebtFlag=ActivityRExecHelper.isagentGroupHaveUsefulAgent(agentGroupid,con);
          
          //agent组 是否已有告警且未恢复
            boolean agentGroupHaveWarnFlag=isagentGroupHaveWarn(agentGroupid,con);
            if(isGroupHaveAgebtFlag) {
                if(agentGroupHaveWarnFlag) {
                    //恢复告警
                    ActivityRExecHelper.warnAgentGroupNoAgentToHave(agentGroupid);  
                }
              
            }else {
                if(!agentGroupHaveWarnFlag) {
                    //agent组没有agent时且未产生过告警或告警已恢复时 告警
                      ActivityRExecHelper.warnAgentGroupNoAgent(agentGroupid);  
                  }
              
               
            }
        }catch(Exception e) {
            _log.error("sendWarnOrNot is error ",e);
        }finally {
            DBResource.closeConnection(con, "sendWarnOrNot", _log);
        }
    }
}
