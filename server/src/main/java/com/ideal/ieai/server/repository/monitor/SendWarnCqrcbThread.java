package com.ideal.ieai.server.repository.monitor;

import com.ideal.ieai.server.platform.warnmanage.IeaiWarnModel;
import com.ideal.ieai.server.platform.warnmanage.UserModel;
import com.ideal.ieai.server.repository.sendmedium.bjns.SendWarnMessageToActiveMq;
import org.apache.log4j.Logger;

import java.util.List;
import java.util.ArrayList;
/**
 * 新巡检短信发送到activeMq上
 * @auth cuizhiwei
 * @date 2021-01-27
 * @email <EMAIL>
 */
public class SendWarnCqrcbThread extends Thread {
    private List<UserModel> users = null;
    private String warnmessage = null;
    private IeaiWarnModel model;
    private static final Logger log = Logger.getLogger(SendWarnSmsOfBjNsThread.class);

    public SendWarnCqrcbThread(List<UserModel> users, String warnmessage, IeaiWarnModel model) {
        super("SendWarnLXThread");
        this.users = users;
        this.warnmessage = warnmessage;
        this.model = model;
    }

    private boolean baseParamValid() {
        log.info("进入自定义重庆农商短信接口验证方法！");
        boolean valid = true;
        if (warnmessage == null || "".equals(warnmessage.trim())) {
            log.error("重庆农商 短信发送内容为空!不进行短信发送");
            return false;
        }

        if (users == null || users.isEmpty()) {
            log.error("重庆农商 短信发送接收为空!不进行短信发送");
            return false;
        }

        return valid;
    }

    @Override
    public void run() {
        //验证参数
        boolean baseParamValid = baseParamValid();
        if (!baseParamValid) {
            log.error("重庆农商 短信发送参数验证未通过！");
            return;
        }

        //收件人
        List<String> phones = new ArrayList<String>();
        for (UserModel model : users) {
            String phone = model.getIphone();
            if (phone != null && !"".equals(phone) && !phones.contains(phone)) {
                phones.add(phone);
            }
        }
        log.info("自定义重庆农商短信接口发送线程！本次发送短信接收人：" + phones.size());
        if (phones.isEmpty()) {
            log.error("重庆农商 短信发送入参满足短信接收人为空！不进行短信发送");
            return;
        }

        try {
            //调用公共方法，公共方法中需要 接收人，接收人电话，报警信息
            SendWarnMessageToActiveMq.getInstance().sendWarn(model,new SendWarnCqrcbPackMessage());
        }catch (Exception e) {
            log.error("向监控平台发送数据失败", e);
        }
    }
}