@charset "utf-8";
/* CSS Document */
body{ 
	padding:0; margin:0;
	height:100%;
	background-color:#116cd9;
	font-family:Microsoft YaHei;
	width:2617px;
	height:1482px;
	overflow:auto;
}
.body_bg{ background-image:url(../images_large/body_bg.jpg); width:100%; height:1482px; margin:0 auto;}
.overview_logo{
	height:125px;
	line-height:125px;
	text-align:center;
	border-bottom:1px solid #0079cb;
	font-size:40px;
	color:#fff;
	}
.overview_logo_width{
	width:665px;
	margin:0 auto;
}
.PA_logo{
	background-image:url(../images_large/overvie_logo.png);
	width:272px;
	height:72px;
	margin:27px 20px 0 0;
	}
.overview_logo span{
	float:left;
	}
.overview_title{
	font-size:24px;
	color:#00fffe;
	padding:20px 114px;
	}
.marquee_w{
	width:2390px;
	margin:0 auto;
}
.overview_map{
	background-image:url(../images_large/overview_img1.png);
	width:2390px;
	height:669px;
	margin:0 auto;
	}
.overview_table{
	margin:113px 0 0 134px;
	color:#fff;
	font-size:18px;
	font-weight:bold;
	float:left
	}
.overview_table2{
	color:#fff;
	font-size:18px;
	font-weight:bold;
	float:left;
	margin:50px 0 0 465px;
	}
.overview_table3{
	color:#fff;
	font-size:18px;
	font-weight:bold;
	float:left;
	margin:20px 0 0 465px;
	}
.triangle_body{
	position:relative;
	display:block; 
	width:291px; 
	height:143px;
}
.triangle{
	background:url(../images_large/triangle.png) no-repeat bottom; 
	float:left;
	width:291px;
	height:143px;
	margin:526px 0 0 470px;
	}
.triangle_load{
	background:url(../images_large/loading.png) no-repeat bottom; 
	width:100%;
	height:100%;
	position:relative;
	z-index:0;
	}
.load_value{
	font-size:42px;
	text-align:center;
	color:#fff;
	position:absolute;
	z-index:1000;
	float:left;
	margin:50px 0 0 95px;
	width:100px;
	}
.overview_tab_title{ 
	background-image:url(../images_large/overview_tab_title.png);
	width:446px;
	height:48px;
	}
.overview_tab_title span{
	padding:15px 0 0 42px;
	display:block
	}
.overview_con{
	border-left:1px solid #00b6d6;
	border-right:1px solid #00b6d6;
	border-bottom:1px solid #00b6d6;
	width:444px;
	height:auto;
	}
.overview_con table{
	background-color:#0062b1;
	width:100%;
	font-size:16px;
	color:#fff;
	}
.overview_con table tr{
	background-color:#004591;
	height:55px;
	}
.overview_con table tr td{
	width:50%;
	text-align:center;
	}
.overview_con table tr td span{/* margin:0 0 0 35px;*/}

.overview_con2{
	border-left:1px solid #00b6d6;
	border-right:1px solid #00b6d6;
	border-bottom:1px solid #00b6d6;
	width:444px;
	height:auto;
	}
.overview_con2 table{
	background-color:#0062b1;
	width:100%;
	font-size:16px;
	}
.overview_con2 table tr{
	background-color:#004591;
	height:34px;
	}
.overview_con2 table tr td{
	width:50%
	}
.overview_con2 table tr td span{ margin:0 0 0 35px;}



.overview_green{
	color:#24fc00;
	font-weight:bold
	}
.overview_yellow{
	color:#fff100;
	font-weight:bold
	}
.overview_gray{
	color:#c6c7b9;
	font-weight:bold
	}
.overview_red{
	color:#fe0300;
	font-weight:bold
	}
.overview_blue{
	color:#00fffe;
	font-weight:bold
	}
.overview_chart{
	padding:35px 114px;
	}
.chart_sb{
	background-image:url(../images_large/overview_chart.png);
	width:699px;
	height:188px;
	float:left;
	margin:20px 143px 0 0;
	}
.chart_sb_01{
	width:240px;
	float:left;
	color:#fff;
	font-size:16px;
	}
.chart_list3{
	width:100%;
	height:35px;
	float:left;
	}
.chart_list4{
	width:100%;
	height:30px;
	float:left;
	margin:85px 0 0 40px;
	}
.chart_sb_01 span{
	float:left;
	}
.chart_count{
	margin:0 15px;
	width:15px;
	}
.chart_icon{
	background-position:0 0;
	}
.chart_icon2{
	background-position:0 -13px;
	}
.chart_icon3{
	background-position:0 -26px;
	}
.chart_icon4{
	background-position:0 -39px;
	}
.chart_icon_common{
	background-image:url(../images_large/overview_circle.png);
	width:13px;
	height:13px;
	display:block;
	margin:3px 10px 0 10px;
	float:left;
	}
.chart_sb_02{
	width:242px;
	height:242px;
	float:left;
	margin:-32px 0 0 -12px;
	font-size:18px;
	font-weight:bold;
	text-align:center;
	color:#fff;
	line-height:2;
	}
.chart_sb_02 span{
	display:block;
	margin:40px 0 0 0;
	}
.chart_sb_03{
	height:auto;
	float:left;
	color:#fff;
	font-size:16px;
	padding:70px 0 0 45px;
	width:150px;
	}
.chart_sb_03 span{
	float:left;
	}
.chart_sb_04{
	width:200px;
	height:auto;
	float:left;
	color:#fff;
	font-size:16px;
	padding:70px 0 0 40px;
	}
.chart_list1{
	width:150px;
	height:35px;
	}
.chart_sb_04 span{
	float:left;
	}
.chart_sb_05{
	width:115px;
	height:auto;
	float:left;
	color:#fff;
	font-size:16px;
	padding:70px 0 0 40px;
	}
.chart_sb_05 span{
	float:left;
	}
.chart_name{
	width:50px;
	margin:0 0 0 15px;
	}
.overview_time{
	background-image:url(../images_large/overview_time.png);
	width:700px;
	height:180px;
	float:left;
	margin:28px 0 0 0;
	color:#fff;
	}
.time_text{
	font-size:20px;
	font-weight:bold;
	margin:75px 0 0 110px;
	float:left;
	}
.time_run{
	float:left;
	font-family:Digiface;
	font-size:44px;
	}
.time_run span{ 
	display:block;
	float:left;
	width:24px;
	height:31px;
	text-align:center;
	line-height:28px;
	}
.run_first{
	margin:74px 0 0 113px;
	}
.run_two{
	margin:74px 0 0 25px;
	}
.run_three{
	margin:74px 0 0 45px;
	}
.oview_table_info{
	padding:55px 115px 0 115px;
	color:#fff;
	font-size:14px;
	float:left;
	width:2390px;
	}
.oview_table_list{
	width:100%;
	background-color:#003bad;
	border-top:1px solid #037fcf;
	border-left:1px solid #037fcf;
	border-right:1px solid #037fcf;
	font-size:14px;
	color:#fff;
	}
.oview_table_list tr td{
	padding:0 0 0 10px;
	border-bottom:1px solid #037fcf;
	}
.tab_info_tr1{
	height:40px;
	background-color:#0076c6;
	font-weight:bold;
	}
.tab_info_tr2{
	height:40px;
	}
.chart_Font{
	position:absolute;
	z-index:1000;
	margin:-150px 0 0 82px;
}
.tab_step{
	width: 300px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
@keyframes warn {
    0% {
        transform: scale(0);
        opacity: 0.0;
    }
    25% {
        transform: scale(0);
        opacity: 0.3;
    }
    50% {
        transform: scale(0.1);
        opacity: 0.5;
    }
    75% {
        transform: scale(0.5);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.0;
    }
}
@-webkit-keyframes "warn" {
    0% {
        -webkit-transform: scale(0);
        opacity: 0.0;
    }
    25% {
        -webkit-transform: scale(0);
        opacity: 0.3;
    }
    50% {
        -webkit-transform: scale(0.1);
        opacity: 0.5;
    }
    75% {
        -webkit-transform: scale(0.5);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 0.0;
    }
}
.img1_location{
	margin:360px 0 0 1255px;
	position:absolute
	}
.img2_location{
	margin:250px 0 0 1350px;
	position:absolute
	}
.img3_location{
	margin:265px 0 0 1350px;
	position:absolute
	}

	
.animation {
    position: relative;
	width:80px;
	height:80px;
}
.animation .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.animation .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.animation .pulse {
    position: absolute;
	top:0;
	left:0;
    width: 60px; 
    height: 60px;
	background-color: rgba(255, 255, 255,0);
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border-radius: 100px;
	-webkit-box-shadow:inset 0 0 50px #24fc00;  
 	-moz-box-shadow:inset 0 0 50px #24fc00;  
    box-shadow:inset 0 0 50px #24fc00; 
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.animation02 {
    position: relative;
	width:80px;
	height:80px;
}
.animation02 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.animation02 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.animation02 .pulse {
    position: absolute;
	top:0;
	left:0;
    width: 60px; 
    height: 60px;
	background-color: rgba(255, 255, 255,0);
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border-radius: 100px;
	-webkit-box-shadow:inset 0 0 50px #24fc00;  
 	-moz-box-shadow:inset 0 0 50px #24fc00;  
    box-shadow:inset 0 0 50px #24fc00; 
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

.animation03 {
    position: relative;
	width:80px;
	height:80px;
}
.animation03 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.animation03 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.animation03 .pulse {
    position: absolute;
	top:0;
	left:0;
    width: 60px; 
    height: 60px;
	background-color: rgba(255, 255, 255,0);
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    border-radius: 100px;
	-webkit-box-shadow:inset 0 0 50px #24fc00;  
 	-moz-box-shadow:inset 0 0 50px #24fc00;  
    box-shadow:inset 0 0 50px #24fc00; 
    z-index: 1;
    opacity: 0;
    -webkit-animation: warn 2s ease-out;
    -moz-animation: warn 2s ease-out;
    animation: warn 2s ease-out;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

/*static*/
.static {
    position: relative;
	width:80px;
	height:80px;
}
.static .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.static .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.static .pulse {
}

.static02 {
    position: relative;
	width:80px;
	height:80px;
}
.static02 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.static02 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.static02 .pulse {
}

.static03 {
    position: relative;
	width:80px;
	height:80px;
}
.static03 .dot {
    position: absolute;
    width: 12px;
    height: 12px;
    left:23px;
    top: 23px;
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border: 1px solid #24fc00;
	background-color:#012d76;
    border-radius: 20px;
    z-index: 2;
}
.static03 .dot_02{
	width:6px;
	height:6px;
	background-color:#24fc00;
	-webkit-border-radius: 20px;
    -moz-border-radius: 20px;
	border-radius: 20px;
	margin:3px auto;
	}
.static03 .pulse {
}
.box{ animation: change 1s  ease-in  infinite ; font-size: 13px; color:#fff; font-weight: bold}
@keyframes change {
    0%{ text-shadow: 0 0 4px #fff}
    50%{ text-shadow: 0 0 40px #fff}
    100%{ text-shadow: 0 0 4px #fff}
}