<%@ page language="java" contentType="text/html; charset=UTF-8"  pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
<script type="text/javascript">
	var path = '<%=request.getContextPath()%>';
</script>
<script type="text/javascript">
if (screen.width >= 1920)
{
	document.write('<link rel="stylesheet" href="<%=request.getContextPath()%>/page/switchMonitorOverview/css/screen_1920_ideal.css"/>');
}else if (screen.width >= 1600 && screen.width < 1920)
{
	document.write('<link rel="stylesheet" href="<%=request.getContextPath()%>/page/switchMonitorOverview/css/screen_1920_ideal.css"/>');
}else if (screen.width >= 1024 && screen.width < 1600)
{
	document.write('<link rel="stylesheet" href="<%=request.getContextPath()%>/page/switchMonitorOverview/css/screen_1920_ideal.css"/>');
}
</script>
<link rel="stylesheet" type="text/css" href="<%=request.getContextPath()%>/css/custom.css?_dc=<%=new Date().getTime() %>" />
<script type="text/javascript" src="<%=request.getContextPath()%>/js/jquery-3.4.1.min.js"></script>
<script type="text/javascript" src="<%=request.getContextPath()%>/js/Chart.js"></script>	
<script type="text/javascript" src="<%=request.getContextPath()%>/page/switchMonitorOverview/index.js"></script>
</head>
<body class="big_screen" onload="load()">
<div class="header_cn">
    <span class="header_logo"></span>
    <span class="header_text">自动化运维管理系统</span>
</div>
<div class="left_cn">
	<div class="tree_cn">
    	<div class="tree_header"><span>应用系统</span></div>
        <div class="tree_structure">
            <div class="tree_tab_cn">
                <table cellpadding="0" cellspacing="0" border="0">
                    <tr class="pd_tab_tr1">
                        <td class="pd_td1">系统名称</td>
                        <td class="pd_td2">切换方向</td>
                        <td class="pd_td3">执行状态</td>
                    </tr>
                </table>
             </div>
             <div class="tree_tab_cn2">
             	<table cellpadding="0" cellspacing="0" border="0" id="sysList">
                </table>
             </div>
        </div>
        <div class="tree_line"></div>
    </div>
    <div class="pie_chart">
    	<div class="tree_header"><span>切换步骤</span></div>
        <div class="pie_cn">
        	<div class="pie_width">
	        	<canvas id="chart1" class="pie_padding"></canvas>
        	</div>
        </div>
        <div class="pie_line"></div>
    </div>
    <!--控制台输出-->
    <div class="output_bg">
    	<span class="area_header">控制台输出</span>
       	<textarea class="output_textarea" id="stepOutput"></textarea>
    </div>
</div>
<div class="right_cn">
	<div class="auto_refresh">
    	<input type="checkbox" class="chk_1" id="autoCheck" checked="checked" onclick="checkChange(this)"/><label for="autoCheck"></label>
                          自动刷新
    </div>
	<div class="statistics_cn">
    	<div class="statis_bg">
        	<div class="statis_icon statis_pos"></div>
            <div class="statis_text02">
            	<span class="text_color" id="iswitchto"></span>
                <span class="text_normal">切换方向</span>
            </div>
        </div>
        <div class="statis_bg">
        	<div class="statis_icon statis_pos2"></div>
            <div class="statis_text02">
            	<span class="text3_color" id="ipCount"></span>
                <span class="text_normal">本次切换涉及的服务器</span>
            </div>
        </div>
        <div class="statis_bg">
        	<div class="statis_icon statis_pos3"></div>
            <div class="statis_text02">
            	<span class="text4_color" id="stepCount"></span>
                <span class="text_normal">本次切换涉及到步骤数</span>
            </div>
        </div>
        <div class="statis_normal">
        	<div class="statis_icon statis_pos4"></div>
            <div class="statis_text02">
            	<span class="text5_color" id="paidtime">00:00:00</span>
                <span class="text_normal">切换耗时</span>
            </div>
        </div>
    </div>
    <!---->
    <div class="cn_area_body">  
    	<div class="cn_area_title"></div>
        <div class="cn_area_cn">
        	<div class="cn_area_space">
            	<span class="area_header">切换步骤</span>
            	<div class="step_cn">
                	<div class="step_btn_common step_btn1" onclick="toLeft()"></div>
                    <div class="step_run" id="step_run">
                        <table cellpadding="0" cellspacing="0" border="0" class="step_tab">
                            <tr id="connerGraph">
                            </tr>
                        </table>
                    </div>
                    <div class="step_btn_common step_btn2" onclick="toRight()"></div>
                </div>
                <!--table-->
                <table cellpadding="0" cellspacing="0" border="0" width="100%">
                    <tr>
                        <td>
                            <div class="pd_tab_cn">
                                <table cellpadding="0" cellspacing="0" border="0" id="stepData">
                                    <tr class="pd_tab_tr1">
                                        <td>状态</td>
                                        <td>步骤标识</td>
                                        <td>执行顺序</td>
                                        <td>依赖</td>
                                        <td>子步骤名称</td>
                                        <td>切换方向</td>
                                        <td>脚本命令</td>
                                        <td>执行用户</td>
                                        <td>开始时间</td>
                                        <td>结束时间</td>
                                        <td>耗时</td>
                                        <td>结果</td>
                                    </tr>
                                </table>
                            </div>
                        </td>
                    </tr>
                </table>
                
            </div>
        </div>
        <div class="cn_area_bm"></div>
    </div>
</div>
</body>
</html>
