package com.ideal.ieai.server.repository.resource;

import com.ideal.ieai.commons.*;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.core.data.DataTypeNames;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.core.util.IDataMarshallerHelper;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBRetryUtil;
import com.ideal.ieai.server.repository.engine.BlobUpdateService;
import com.ideal.ieai.server.repository.hd.flowstart.ceb.FlowstartinstManagerCEB;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.resource.models.*;
import com.ideal.ieai.server.repository.sus.resource.models.ResourceGropEnvBean;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * <ul>
 * <li>Title: ResourceGroupManager.java</li>
 * <li>Description:资源组处理类</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         Jul 18, 2012
 */
public class ResourceGroupManager
{

    private static final Logger _log = Logger.getLogger(ResourceGroupManager.class);

    public ResourceGroupManager()
    {

    }

    static private ResourceGroupManager _intance = new ResourceGroupManager();

    /**
     * <li>Description:获取类实例</li>
     * 
     * <AUTHOR> Jul 18, 2012
     * @return return ResourceManager
     */
    static public ResourceGroupManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new ResourceGroupManager();
        }
        return _intance;
    }

    /**
     * <li>Description:初始化查询资源组信息</li>
     * 
     * <AUTHOR> Jul 18, 2012
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getResourceInfo ( int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT TU.IID,TU.IRESNAME,TU.IRESDES,TU.ITYPE FROM IEAI_RESOURCE_GROUP TU WHERE TU.ITYPE=?";
        ResourceGroup resourceGroup = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResourceInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        resourceGroup = new ResourceGroup();
                        resourceGroup.setIid(actRS.getLong("IID"));
                        resourceGroup.setIresName(actRS.getString("IRESNAME"));
                        resourceGroup.setIresDes(actRS.getString("IRESDES"));
                        resourceGroup.setItype(actRS.getInt("ITYPE"));
                        list.add(resourceGroup);
                    }
                } catch (SQLException e)
                {
                    _log.error("getResourceInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getResourceInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    public List getResourceList ( String manageId, int type ) throws RepositoryException
    {
        List list = new ArrayList();
        if ("".equals(manageId) || "null".equals(manageId) || null == manageId || "-1".equals(manageId))
        {
            manageId = "0";
        }
        String sql = " SELECT AA.IID,AA.IRESNAME,AA.IRESDES,AA.ITYPE,BB.IID AS ISID FROM  (SELECT IID,IRESNAME,IRESDES,ITYPE  FROM  IEAI_RESOURCE_GROUP WHERE  ITYPE=? ORDER BY IID ) AA LEFT JOIN (SELECT g.IID  FROM IEAI_RESOURCE_GROUP g,IEAI_GROUP_MANAGE m  WHERE m.ITYPE=? AND g.IID=m.IGROUPID AND m.IMANAGEID=? ) BB ON AA.IID=BB.IID ";
        ResourceGroup resourceGroup = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResourceList", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actStat.setLong(2, type);
                    actStat.setLong(3, Long.valueOf(manageId));
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        resourceGroup = new ResourceGroup();
                        resourceGroup.setIid(actRS.getLong("IID"));
                        resourceGroup.setIresName(actRS.getString("IRESNAME"));
                        resourceGroup.setIresDes(actRS.getString("IRESDES"));
                        resourceGroup.setItype(actRS.getInt("ITYPE"));
                        resourceGroup.setIsIid(actRS.getLong("ISID"));
                        list.add(resourceGroup);
                    }
                } catch (SQLException e)
                {
                    _log.error("getResourceInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getResourceInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    public List getManageList ( String userId, int type ) throws RepositoryException
    {

        String sql = " SELECT IID AS manageId ,IMANGROUPNAME  AS manageName  FROM IEAI_MANAGER_GROUP  WHERE  ITYPE=?";
        List manlist = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                ResourceGroup gbean = null;
                try
                {
                    con = DBResource.getConnection("getManageList", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    // actStat.setLong(1, Long.valueOf(userId));
                    actStat.setInt(1, type);
                    actRS = actStat.executeQuery();
                    manlist = new ArrayList();
                    while (actRS.next())
                    {
                        gbean = new ResourceGroup();
                        gbean.setManageId(actRS.getString("manageId"));
                        gbean.setManageName(actRS.getString("manageName"));
                        manlist.add(gbean);
                    }
                } catch (SQLException e)
                {
                    _log.error("getManageList is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "getManageList", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
        return manlist;
    }

    public void saveManageGroup ( List jsList, String manageId, int type ) throws RepositoryException
    {
        String sqldel = " DELETE FROM IEAI_GROUP_MANAGE WHERE IMANAGEID=" + manageId;
        String sql = " INSERT INTO IEAI_GROUP_MANAGE(IMANAGEID,IGROUPID,ITYPE) VALUES(?,?,?) ";
        if (!"".equals(manageId) && !"-1".equals(manageId) && manageId != null && !"null".equals(manageId))
        {
            if (jsList != null && jsList.size() > 0)
            {
                ResourceGroup resourceGroup = null;
                for (int i = 0;; i++)
                {
                    PreparedStatement actStat = null;
                    Connection con = null;
                    con = DBResource.getConnection("saveManageGroup", _log, Constants.IEAI_SUS);
                    try
                    {
                        actStat = con.prepareStatement(sqldel);
                        actStat.executeUpdate();
                        actStat = null;
                        for (int j = 0; j < jsList.size(); j++)
                        {
                            resourceGroup = (ResourceGroup) jsList.get(j);
                            actStat = con.prepareStatement(sql);
                            actStat.setString(1, manageId);
                            actStat.setLong(2, resourceGroup.getIid());
                            actStat.setInt(3, type);
                            actStat.executeUpdate();
                        }
                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "saveResource", _log);
                    }
                    break;
                }
            }
        }
    }

    /**
     * <li>Description:保存资源组信息</li>
     * 
     * <AUTHOR> Jul 25, 2012
     * @param resourceGroup
     * @param type
     * @throws RepositoryException return void
     */
    public void saveResource ( String userId, ResourceGroup resourceGroup, int type ) throws RepositoryException
    {
        if (!"".equals(userId) && userId != null && !"null".equals(userId))
        {
            String manageId = "-1";
            for (int i = 0;; i++)
            {
                PreparedStatement actStat = null;
                Connection con = null;
                ResultSet actRS = null;
                con = DBResource.getConnection("saveResource", _log, Constants.IEAI_SUS);
                String sqlm = " SELECT IMANGROUPID AS manageId FROM  IEAI_USER_AUTH WHERE IUSERID=? AND ITYPE=?";
                try
                {
                    try
                    {
                        actStat = con.prepareStatement(sqlm);
                        actStat.setLong(1, Long.valueOf(userId));
                        actStat.setInt(2, type);
                        actRS = actStat.executeQuery();
                        while (actRS.next())
                        {
                            manageId = actRS.getString("manageId");
                        }
                    } catch (SQLException e)
                    {
                        _log.error("saveResource is error ! " + e.getMessage());
                        throw new RepositoryException(ServerError.ERR_DB_QUERY);
                    }
                } catch (RepositoryException ex)
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                }
                long tid = resourceGroup.getIid();
                if (tid != -1)
                {
                    String sql = " update  IEAI_RESOURCE_GROUP set iresname=?,iresdes=?,itype=? where iid=?";
                    try
                    {
                        try
                        {
                            actStat = con.prepareStatement(sql);
                            actStat.setString(1, resourceGroup.getIresName());
                            actStat.setString(2, resourceGroup.getIresDes());
                            actStat.setInt(3, type);
                            actStat.setLong(4, tid);
                            actStat.executeUpdate();
                            con.commit();
                        } catch (SQLException e)
                        {
                            DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        } finally
                        {
                            DBResource.closePSConn(con, actStat, "saveResource", _log);
                        }
                    } catch (RepositoryException ex)
                    {
                        DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                    }
                } else
                {
                    String sql = "INSERT INTO IEAI_RESOURCE_GROUP(iid,iresname,iresdes,itype) VALUES (?,?,?,?)";
                    String sqlManage = "INSERT INTO IEAI_GROUP_MANAGE(IMANAGEID,IGROUPID,itype) VALUES (?,?,?)";
                    if (!resourceGroup.getIresName().equals(""))
                    {
                        long iid = IdGenerator.createId("IEAI_RESOURCE_GROUP", con);
                        try
                        {
                            try
                            {
                                actStat = con.prepareStatement(sql);
                                actStat.setLong(1, iid);
                                actStat.setString(2, resourceGroup.getIresName());
                                actStat.setString(3, resourceGroup.getIresDes());
                                actStat.setInt(4, type);
                                actStat.executeUpdate();
                                actStat = null;
                                if (!"-1".equals(manageId) && !"".equals(manageId) && !"null".equals(manageId) && null != manageId)
                                {
                                    actStat = con.prepareStatement(sqlManage);
                                    actStat.setLong(1, Long.valueOf(manageId));
                                    actStat.setLong(2, iid);
                                    actStat.setInt(3, type);
                                    actStat.executeUpdate();
                                }
                                con.commit();
                            } catch (SQLException e)
                            {
                                DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                                    Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                            } finally
                            {
                                DBResource.closePSConn(con, actStat, "saveResource", _log);
                            }
                        } catch (RepositoryException ex)
                        {
                            DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                        }

                    }
                }
                break;
            }
        }
    }

    /**
     * <li>Description:删除资源组信息</li>
     * 
     * <AUTHOR> Jul 25, 2012
     * @param id
     * @return
     * @throws RepositoryException return List
     */
    public void delResource ( long id ) throws RepositoryException
    {
        String sql = "DELETE FROM IEAI_RESOURCE_GROUP WHERE IID=? ";
        String sql2 = "DELETE FROM IEAI_GROUP_BUSINESS WHERE IGROUPID=? ";
        String sql3 = "DELETE FROM IEAI_GROUP_MANAGE WHERE IGROUPID=? ";
        String sql4 = "DELETE FROM IEAI_RESGROUP_EXTENDSPARAMETER_VALUE WHERE IPARAMETERID IN (SELECT IID FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE IRESGROUPID=?) ";
        String sql5 = "DELETE FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE IRESGROUPID=? ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("delResource", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();

                    actStat = null;
                    actStat = con.prepareStatement(sql2);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sql3);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sql4);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sql5);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "delResource", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * <li>Description:查询服务器信息</li>
     * 
     * <AUTHOR> Aug 7, 2012
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getServerInfo ( int type, long groupId ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT AA.IID IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMDTYPE, BB.IID ISIID FROM (SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMDTYPE  FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IIP ) AA LEFT JOIN (SELECT B.IID FROM IEAI_GROUP_BUSINESS A,IEAI_RESOURCE_BUSINESS B,IEAI_RESOURCE_GROUP C WHERE A.IGROUPID=C.IID AND A.IBUSINESSID=B.IID AND C.IID=? ) BB ON AA.IID=BB.IID";
        ServerInfo serverInfo = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getServerInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actStat.setLong(2, groupId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        serverInfo = new ServerInfo();
                        serverInfo.setIid(actRS.getLong("IID"));
                        serverInfo.setIHostName(actRS.getString("IHOSTNAME"));
                        serverInfo.setIip(actRS.getString("IIP"));
                        serverInfo.setIPort(actRS.getInt("IPORT"));
                        serverInfo.setIPriority(actRS.getInt("IPRIORITY"));
                        serverInfo.setIType(actRS.getInt("ITYPE"));
                        serverInfo.setIsIid(actRS.getLong("ISIID"));
                        serverInfo.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        serverInfo.setMdtype(actRS.getString("IMDTYPE"));
                        list.add(serverInfo);
                    }
                } catch (SQLException e)
                {
                    _log.error("getServerInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getServerInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 查询服务器信息 用于分页
     */
    public List getServerInfo ( int type, long groupId, int startrow, int endrow, boolean isSplit ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT AA.IID IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMDTYPE, BB.IID ISIID FROM (SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMDTYPE  FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IIP ) AA LEFT JOIN (SELECT B.IID FROM IEAI_GROUP_BUSINESS A,IEAI_RESOURCE_BUSINESS B,IEAI_RESOURCE_GROUP C WHERE A.IGROUPID=C.IID AND A.IBUSINESSID=B.IID AND C.IID=? ) BB ON AA.IID=BB.IID ORDER BY IID";
        if (isSplit)
        {
            sql = SQLUtil.getQueryPageSQL("db2", sql);
        }
        ServerInfo serverInfo = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getServerInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actStat.setLong(2, groupId);
                    if (isSplit)
                    {
                        actStat.setInt(3, startrow);
                        actStat.setInt(4, endrow);
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        serverInfo = new ServerInfo();
                        serverInfo.setIid(actRS.getLong("IID"));
                        serverInfo.setIHostName(actRS.getString("IHOSTNAME"));
                        serverInfo.setIip(actRS.getString("IIP"));
                        serverInfo.setIPort(actRS.getInt("IPORT"));
                        serverInfo.setIPriority(actRS.getInt("IPRIORITY"));
                        serverInfo.setIType(actRS.getInt("ITYPE"));
                        serverInfo.setIsIid(actRS.getLong("ISIID"));
                        serverInfo.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        serverInfo.setMdtype(actRS.getString("IMDTYPE"));
                        list.add(serverInfo);
                    }
                } catch (SQLException e)
                {
                    _log.error("getServerInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getServerInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 获取服务器记录总数
     */
    public int getTotalCount ( int type, long groupId ) throws RepositoryException
    {
        String sql = "select count(*) as total from (SELECT AA.IID IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMDTYPE, BB.IID ISIID FROM (SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMDTYPE  FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IIP ) AA LEFT JOIN (SELECT B.IID FROM IEAI_GROUP_BUSINESS A,IEAI_RESOURCE_BUSINESS B,IEAI_RESOURCE_GROUP C WHERE A.IGROUPID=C.IID AND A.IBUSINESSID=B.IID AND C.IID=? ) BB ON AA.IID=BB.IID) ";
        int totalCount = 0;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getTotalCount", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actStat.setLong(2, groupId);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        totalCount = actRS.getInt("total");
                    }
                } catch (SQLException e)
                {
                    _log.error("getTotalCount is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getTotalCount", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return totalCount;
    }

    /**
     * <li>Description:服务器信息新建以及更新</li>
     * 
     * <AUTHOR> Aug 7, 2012
     * @param serverInfo
     * @param type
     * @throws RepositoryException return void
     */
    public void saveServerInfo ( ServerInfo serverInfo, int type ) throws RepositoryException
    {
        long tid = serverInfo.getIid();
        if (tid != -1 && tid != 0)
        {
            String sql = " update  IEAI_RESOURCE_BUSINESS set IHOSTNAME=? ,IIP=?,IPORT=?,IPRIORITY=?,ITYPE=?,ISYSTEMTYPE=?,IMDTYPE=?  where iid=?";
            for (int i = 0;; i++)
            {
                try
                {
                    PreparedStatement actStat = null;
                    Connection con = null;
                    try
                    {
                        con = DBResource.getSchedulerConnection("saveResource", _log, Constants.IEAI_SUS);
                        actStat = con.prepareStatement(sql);
                        actStat.setString(1, serverInfo.getIHostName());
                        actStat.setString(2, serverInfo.getIip());
                        actStat.setInt(3, serverInfo.getIPort());
                        actStat.setInt(4, serverInfo.getIPriority());
                        actStat.setInt(5, type);
                        actStat.setString(6, serverInfo.getSystemType());
                        actStat.setString(7, serverInfo.getMdtype());
                        actStat.setLong(8, tid);
                        actStat.executeUpdate();
                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "saveResource", _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                }
            }
        } else
        {
            String sql = "MERGE INTO IEAI_RESOURCE_BUSINESS AS A " + "USING (SELECT ? AS IIP ,? AS IPORT, ? AS TYPE FROM IDUAL ) AS B "
                    + "ON A.IIP=B.IIP AND A.IPORT=B.IPORT AND A.ITYPE = B.TYPE "
                    + "WHEN MATCHED THEN UPDATE SET IHOSTNAME=? ,IPRIORITY=? ,ISYSTEMTYPE=?, IMDTYPE=?"
                    + "WHEN NOT MATCHED THEN INSERT (IID ,IHOSTNAME ,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMDTYPE) VALUES(?,?,?,?,?,?,?,?)";

            if (!serverInfo.getIip().equals("") && !(serverInfo.getIPort() == 0))
            {
                for (int i = 0;; i++)
                {
                    try
                    {
                        PreparedStatement actStat = null;
                        Connection con = null;
                        try
                        {
                            con = DBResource.getConnection("saveServerInfo", _log, Constants.IEAI_SUS);
                            actStat = con.prepareStatement(sql);
                            actStat.setString(1, serverInfo.getIip());
                            actStat.setInt(2, serverInfo.getIPort());
                            actStat.setInt(3, type);
                            actStat.setString(4, serverInfo.getIHostName());
                            actStat.setInt(5, serverInfo.getIPriority());
                            actStat.setString(6, serverInfo.getSystemType());
                            actStat.setString(7, serverInfo.getMdtype());
                            actStat.setLong(8, IdGenerator.createId("IEAI_RESOURCE_BUSINESS", con));
                            actStat.setString(9, serverInfo.getIHostName());
                            actStat.setString(10, serverInfo.getIip());
                            actStat.setInt(11, serverInfo.getIPort());
                            actStat.setInt(12, serverInfo.getIPriority());
                            actStat.setInt(13, type);
                            actStat.setString(14, serverInfo.getSystemType());
                            actStat.setString(15, serverInfo.getMdtype());
                            actStat.executeUpdate();
                            con.commit();
                        } catch (SQLException e)
                        {
                            DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        } finally
                        {
                            DBResource.closePSConn(con, actStat, "saveServerInfo", _log);
                        }
                        break;
                    } catch (RepositoryException ex)
                    {
                        DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                    }
                }
            }
        }
    }

    /**
     * <li>Description:Hc导出服务器方法</li>
     * 
     * @update tao_ding 2014-6-9
     * @param colname1
     * @param colname2
     * @param colname3
     * @param colname4
     * @param type
     * @throws SQLException return void
     * @throws RepositoryException 
     */
    public void saveImportFileForHc ( String colname1, String colname2, int colname3, int colname4, String colname5, String colname6, int type )
            throws SQLException, RepositoryException
    {
        String sql = "INSERT INTO IEAI_RESOURCE_BUSINESS(IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMDTYPE) VALUES(?,?,?,?,?,?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("saveImportFile", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, IdGenerator.createId("IEAI_RESOURCE_BUSINESS", con));
                    actStat.setString(2, colname1);
                    actStat.setString(3, colname2);
                    actStat.setInt(4, colname3);
                    actStat.setInt(5, colname4);
                    actStat.setLong(6, type);
                    actStat.setString(7, colname5);
                    actStat.setString(8, colname6);
                    actStat.executeUpdate();
                    con.commit();
                    _log1.info("resouce save successfull, name is " + colname1);
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "saveServerInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * <li>Description:CM导出服务器方法</li>
     * 
     * @update tao_ding 2014-6-9
     * @param colname1
     * @param colname2
     * @param colname3
     * @param colname4
     * @param type
     * @throws SQLException return void
     * @throws RepositoryException 
     */
    public void saveImportFile ( String colname1, String colname2, int colname3, int colname4, String colname5,
            int type ) throws SQLException, RepositoryException
    {
        String sql = "INSERT INTO IEAI_RESOURCE_BUSINESS(IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMDTYPE) VALUES(?,?,?,?,?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("saveImportFile", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, IdGenerator.createId("IEAI_RESOURCE_BUSINESS", con));
                    actStat.setString(2, colname1);
                    actStat.setString(3, colname2);
                    actStat.setInt(4, colname3);
                    actStat.setInt(5, colname4);
                    actStat.setLong(6, type);
                    actStat.setString(7, colname5);
                    actStat.executeUpdate();
                    con.commit();
                    _log1.info("resouce save successfull, name is " + colname1);
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "saveImportFile", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    public String more_ip_port ( String colname2, int colname3, int type ) throws SQLException, RepositoryException
    {
        String sql = "SELECT * FROM IEAI_RESOURCE_BUSINESS WHERE IIP='" + colname2 + "' AND IPORT=" + colname3 + " AND ITYPE=" + type;
        String mid = "";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                ResultSet actRS = null;
                try
                {
                    con = DBResource.getConnection("saveResource", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        mid = actRS.getString("IIP");
                    }
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
        return mid;
    }

    /**
     * <li>Description:服务器关联信息新建以及更新</li>
     * 
     * <AUTHOR> Aug 7, 2012
     * @param serverInfo
     * @param groupId
     * @param type
     * @throws RepositoryException return void
     */
    public void saveGroupBusiness ( List jsonData, long groupId, int type ) throws RepositoryException
    {
        String sqlDel = " DELETE FROM IEAI_GROUP_BUSINESS WHERE IGROUPID=?  AND ITYPE=?";

        String sql = " INSERT INTO IEAI_GROUP_BUSINESS(IID ,IGROUPID ,IBUSINESSID,ITYPE) VALUES(?,?,?,?)";
        ServerInfo serverInfo = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("saveGroupBusiness", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sqlDel);
                    actStat.setLong(1, groupId);
                    actStat.setInt(2, type);
                    actStat.executeUpdate();
                    actStat.clearBatch();
                    actStat.clearParameters();

                    List<List<Object>> list = new ArrayList<List<Object>>();
                    for (int j = 0; j < jsonData.size(); j++)
                    {
                        serverInfo = (ServerInfo) jsonData.get(j);
                        if (!serverInfo.getIip().equals("") && !(serverInfo.getIPort() == 0))
                        {
                            List<Object> objectList = new ArrayList<Object>();
                            objectList.add(IdGenerator.createId("IEAI_RESOURCE_BUSINESS", con));
                            objectList.add(groupId);
                            objectList.add(serverInfo.getIid());
                            objectList.add(type);
                            list.add(objectList);
                        }
                    }
                    Boolean flag = batchInsert(sql, list, con);
                    if (flag)
                    {
                        con.commit();
                    }
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    public boolean batchInsert ( String sql, List<List<Object>> dataList, Connection conn )
    {
        PreparedStatement perstmt = null;
        try
        {
            perstmt = conn.prepareStatement(sql);
            for (Iterator iterator = dataList.iterator(); iterator.hasNext();)
            {
                List paramList = (List) iterator.next();
                int size = paramList.size();
                for (int i = 1; i <= size; i++)
                {
                    perstmt.setObject(i, paramList.get(i - 1));
                }
                perstmt.addBatch();
            }

            perstmt.executeBatch();
            perstmt.clearBatch();
            return true;
        } catch (Exception ex)
        {
            ex.printStackTrace();
            return false;
        } finally
        {
            DBResource.closePreparedStatement(perstmt, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        // finally {
        // try {
        // // perstmt.close();
        // } catch (Exception localException5) {
        // perstmt = null;
        // }
        // try {
        // // conn.close();
        // } catch (Exception localException6) {
        // conn = null;
        // }
        // }
    }

    /**
     * <li>Description:删除服务器关联信息</li>
     * 
     * <AUTHOR> Jul 25, 2012
     * @param serverInfo
     * @param groupId
     * @param typeT
     * @return
     * @throws RepositoryException return List
     */
    public void delGroupBusiness ( ServerInfo serverInfo, long groupId, int type ) throws RepositoryException
    {
        String sql = "DELETE FROM IEAI_GROUP_BUSINESS WHERE IGROUPID=? AND IBUSINESSID =? AND ITYPE=?";
        String sqlVALUE = "DELETE FROM IEAI_RESGROUP_EXTENDSPARAMETER_VALUE WHERE  ISERVERID =? ";

        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("delServerInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, groupId);
                    actStat.setLong(2, serverInfo.getIid());
                    actStat.setInt(3, type);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sqlVALUE);
                    actStat.setLong(1, serverInfo.getIid());
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "delServerInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_DELETE);
            }
        }
    }

    /**
     * <li>Description:删除服务器信息</li>
     * 
     * <AUTHOR> Jul 25, 2012
     * @param id
     * @return
     * @throws RepositoryException return List
     */
    public void delServerInfo ( long id ) throws RepositoryException
    {
        String sql = "DELETE FROM IEAI_RESOURCE_BUSINESS WHERE IID=?";
        String sql2 = "DELETE FROM IEAI_GROUP_BUSINESS WHERE IBUSINESSID=?";
        String sql3 = "DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE  ISERVERID =? ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("delServerInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();

                    actStat = null;
                    actStat = con.prepareStatement(sql2);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sql3);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "delServerInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_DELETE);
            }
        }
    }

    public void delServerInfoByAgent ( Long [] agentIds ) throws RepositoryException
    {
        String sql = "DELETE FROM IEAI_RESOURCE_BUSINESS WHERE IID in (select b.IID from IEAI_RESOURCE_BUSINESS b where b.IIP in (select a.IAGENT_IP from IEAI_AGENTINFO a where a.IAGENTINFO_ID in ("+StringUtils.join(agentIds, ",")+"))) ";
        String sql2 = "DELETE FROM IEAI_GROUP_BUSINESS WHERE IBUSINESSID in (select b.IID from IEAI_RESOURCE_BUSINESS b where b.IIP in (select a.IAGENT_IP from IEAI_AGENTINFO a where a.IAGENTINFO_ID in ("+StringUtils.join(agentIds, ",")+")))";
        String sql3 = "DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE  ISERVERID in (select b.IID from IEAI_RESOURCE_BUSINESS b where b.IIP in (select a.IAGENT_IP from IEAI_AGENTINFO a where a.IAGENTINFO_ID in ("+StringUtils.join(agentIds, ",")+"))) ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("delServerInfoByAgent", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.executeUpdate();

                    actStat = null;
                    actStat = con.prepareStatement(sql2);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sql3);
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "delServerInfoByAgent", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_DELETE);
            }
        }
    }
    
    /**
     * <li>Description:获取已经配置的补丁分发的资源组</li>
     * 
     * <AUTHOR> Aug 10, 2012
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getResGroup ( int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT IID, IRESNAME FROM IEAI_RESOURCE_GROUP WHERE ITYPE=? ORDER BY IRESNAME";
        String[] result = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResGroup", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        result = new String[2];
                        result[0] = String.valueOf(actRS.getLong("IID"));
                        result[1] = actRS.getString("IRESNAME");
                        list.add(result);
                    }
                } catch (SQLException e)
                {
                    _log.error("getResGroup is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getResGroup", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * <li>Description:</li>
     * 
     * <AUTHOR> Aug 15, 2012
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getServerIp ( int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT DISTINCT(IIP) AS IIP FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IIP";
        String[] result = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getServerIp", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        result = new String[2];
                        result[0] = actRS.getString("IIP");
                        result[1] = actRS.getString("IIP");
                        list.add(result);
                    }
                } catch (SQLException e)
                {
                    _log.error("getServerIp is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getServerIp", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * <li>Description:</li>
     * 
     * <AUTHOR> Aug 15, 2012
     * @param resname
     * @param hostip
     * @param priority
     * @param type
     * @param start
     * @param end
     * @return
     * @throws RepositoryException return List
     */
    public Object[] getResourInfo ( String resname, String hostip, String priority, int type, int start, int end ) throws RepositoryException
    {
        Object[] object = new Object[2];
        List list = new ArrayList();
        int count = 0;
        String sqlres = "SELECT IHOSTNAME,IIP,IPORT,IPRIORITY,IRESNAME FROM( "
                + "SELECT BB.IHOSTNAME AS IHOSTNAME,BB.IIP AS IIP,BB.IPORT AS IPORT,BB.IPRIORITY AS IPRIORITY,CC.IRESNAME AS IRESNAME,ROW_NUMBER() OVER() AS RID ";

        String sqlcount = "SELECT COUNT(*) AS COUNT ";
        String sql = " FROM (SELECT AA.IGROUPID,AA.IBUSINESSID  FROM IEAI_GROUP_BUSINESS AA WHERE AA.ITYPE=?) AAA,IEAI_RESOURCE_BUSINESS BB,IEAI_RESOURCE_GROUP CC "
                + " WHERE AAA.IBUSINESSID=BB.IID AND AAA.IGROUPID=CC.IID AND BB.ITYPE=? AND CC.ITYPE=?";
        if (resname != null && !"".equals(resname))
        {
            sql = sql + " AND CC.IID=" + resname;
        }

        if (hostip != null && !"".equals(hostip))
        {
            sql = sql + " AND BB.IIP='" + hostip + "'";
        }

        if (priority != null && !"".equals(priority))
        {
            sql = sql + " AND BB.IPRIORITY=" + priority;
        }

        String sqlpage = " ORDER BY CC.IRESNAME)WHERE RID BETWEEN ? AND ?";

        sqlres = sqlres + sql + sqlpage;
        sqlcount = sqlcount + sql;
        Map result = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResourInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sqlres);
                    actStat.setLong(1, type);
                    actStat.setLong(2, type);
                    actStat.setLong(3, type);
                    actStat.setLong(4, start);
                    actStat.setLong(5, end);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        result = new Hashtable();
                        result.put("hostname", actRS.getString("IHOSTNAME"));
                        result.put("hostip", actRS.getString("IIP"));
                        result.put("port", actRS.getString("IPORT"));
                        result.put("priority", actRS.getString("IPRIORITY"));
                        result.put("resname", actRS.getString("IRESNAME") == null ? "" : actRS.getString("IRESNAME"));
                        list.add(result);
                    }

                    actRS = null;
                    actStat = null;
                    actStat = con.prepareStatement(sqlcount);
                    actStat.setLong(1, type);
                    actStat.setLong(2, type);
                    actStat.setLong(3, type);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        count = Integer.parseInt(String.valueOf(actRS.getString("COUNT")));
                    }
                } catch (SQLException e)
                {
                    _log.error("getResourInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getResourInfo", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        object[0] = count;
        object[1] = list;
        return object;
    }

    /**
     * <li>Description:终止工作流</li>
     * 
     * <AUTHOR> Sep 27, 2012
     * @param user
     * @param flowid return void
     */
    public void killFlow ( UserInfo user, long flowid, int type )
    {
        try
        {
            Engine.getInstance().killFlow(user, flowid, type);
        } catch (ServerException e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * <li>Description:启动工作流并保存相关数据</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param user
     * @param prjName
     * @param flowName
     * @param args
     * @param envVars
     * @param logConfig
     * @param instanceName
     * @param comment return void
     * @throws ServerException
     * @throws RepositoryException
     */
    public void startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars, WorkflowLogConfig logConfig, String instanceName,
            String comment, List listres, List list, String flowresource, String taskId,int type ) throws RepositoryException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                try
                {
                    conn = DBResource.getConnection("startFlow", _log, Constants.IEAI_SUS);
                    long flowid = Engine.getInstance().startFlow(user, prjName, flowName, args, envVars, logConfig,
                        instanceName, comment, true, null, null, false, 0, conn, Constants.IEAI_HEALTH_INSPECTION,
                        type);

                    insertFlowResouce(flowid, prjName, flowName, conn, Constants.HC_NOHC, Constants.RESOURCE_SUS);
                    saveAllList(flowid, listres, list, flowresource, conn);
                    updateSHWorkItem(flowid, taskId, conn);
                    conn.commit();
                } catch (Exception e)
                {
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(conn, "startFlow", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_INSERT);
            }
        }
    }

    public void startFlow ( UserInfo user, String prjName, String flowName, List args, Map envVars, WorkflowLogConfig logConfig, String instanceName,
            String comment, WorkflowBean wbean, String taskId, int type ) throws RepositoryException, ServerException
    {
        for (int i = 0;; i++)
        {
            try
            {
                Connection conn = null;
                try
                {
                    conn = DBResource.getConnection("startFlow", _log, Constants.IEAI_SUS);
                    long flowid = Engine.getInstance().startFlow(user, prjName, flowName, args, envVars, logConfig,
                        instanceName, comment, true, null, null, false, 0, conn, Constants.IEAI_HEALTH_INSPECTION,
                        type);

                    insertFlowResouce(flowid, prjName, flowName, conn, wbean.getHcCheckId(), wbean.getResourceType());
                    saveAllList(flowid, wbean.getListres(), wbean.getList(), wbean.getFlowresource(), conn);
                    updateSHWorkItem(flowid, taskId, conn);
                    conn.commit();
                } catch (SQLException e)
                {
                    // 失败后数据回滚
                    DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
    }

    /**
     * <li>Description:事物保存工作流信息 -补丁分发健康检查</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param flowId
     * @param prjName
     * @param flowName
     * @param con
     * @param isHc
     * @param type return void
     * @throws RepositoryException
     */
    public void insertFlowResouce ( long flowId, String prjName, String flowName, Connection con, long isHc, int type ) throws RepositoryException
    {
        String sql = "INSERT INTO IEAI_FLOW_RESOURCE(IID, IPROJECTNAME, IFLOWNAME, ICHECKINFO, ITYPE)  VALUES(?, ?, ?, ?, ?)";
        PreparedStatement ps = null;
        try
        {
            ps = con.prepareStatement(sql);

            ps.setLong(1, flowId);
            ps.setString(2, prjName);
            ps.setString(3, flowName);
            ps.setLong(4, isHc);
            ps.setInt(5, type);

            ps.executeUpdate();
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "insertFlowResouce", _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, "insertFlowResouce", _log);
        }
    }

    /**
     * <li>Description:事物更新任务状态为完成</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param flowId
     * @param prjName
     * @param flowName
     * @param con
     * @param isHc
     * @param type return void
     * @throws RepositoryException
     */
    public void updateSHWorkItem ( long flowId, String taskId, Connection con ) throws RepositoryException
    {
        String sql = " UPDATE  IEAI_SH_WORKITEM  SET  IFLOWID=? , ISTATE=0 ,IENDTIME=? WHERE IID=? ";
        PreparedStatement ps = null;
        try
        {
            ps = con.prepareStatement(sql);

            ps.setLong(1, flowId);
            ps.setString(2, Conscommon.getDateTime(System.currentTimeMillis()));
            ps.setLong(3, Long.valueOf(taskId));

            ps.executeUpdate();
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e, "insertFlowResouce", _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, "insertFlowResouce", _log);
        }
    }

    /**
     * <li>Description:保存工作流参数</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param flowId
     * @param list
     * @param con
     * @throws RepositoryException return void
     */
    public void saveFlowPar ( long flowId, List list, Connection con ) throws RepositoryException
    {
        PreparedStatement ps = null;
        String sql = "insert  into  IEAI_SUS_PARAMETERS(IID,IPARANAME,IPARATYPE,IPARAVALUE,IUID) values(?,?,?,?,?)";
        String sql2 = " delete  from  IEAI_SUS_PARAMETERS  where IUID= " + flowId;
        try
        {
            ps = con.prepareStatement(sql2);
            ps.execute();
            ps = null;
        } catch (SQLException e1)
        {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        // String sql2 = " update table IEAI_SUS_PARAMETERS set
        // IPARANAME=?,IPARAVALUE=?,IUID=? where iid=? ";
        try
        {
            String paraName = "";
            String paraType = "";
            String paraValue = "";
            String pid = "";
            JSONArray jsArr = JSONArray.fromObject(list);
            for (int ii = 0; ii < jsArr.size(); ii++)
            {
                // pid=jsArr.getJSONObject(ii).optString("iid");
                // if ("-1".equals(pid) || "".equals(pid) ||
                // "null".equals(pid)){
                long iid = IdGenerator.createId("IEAI_SUS_PARAMETERS", con);
                paraName = jsArr.getJSONObject(ii).optString("paraName");
                paraType = jsArr.getJSONObject(ii).optString("paraType");
                paraValue = jsArr.getJSONObject(ii).optString("paraValue");
                ps = con.prepareStatement(sql);
                ps.setLong(1, iid);
                ps.setString(2, paraName);
                ps.setString(3, paraType);
                ps.setString(4, paraValue);
                ps.setLong(5, flowId);
                // }else{
                // paraName = jsArr.getJSONObject(ii)
                // .optString("paraName");
                // paraValue = jsArr.getJSONObject(ii).optString(
                // "paraValue");
                // ps = con.prepareStatement(sql2);
                // ps.setString(1, paraName);
                // ps.setString(2, paraValue);
                // ps.setLong(3, flowId);
                // ps.setLong(4, Long.valueOf(pid));
                // }
                ps.execute();
                ps = null;
            }

        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "saveFlowPar", _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, "saveFlowPar", _log);
        }
    }

    /**
     * <li>Description:保存工作流执行服务器信息</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param flowId
     * @param groupId
     * @param con return void
     * @throws RepositoryException
     */
    public void saveFlowBus ( long flowId, String groupId, Connection con ) throws RepositoryException
    {
        String sql = "select IRB.IIP , IRB.IHOSTNAME,IRB.IPORT  , IRB.IPRIORITY ,IRB.ITYPE  from IEAI_RESOURCE_BUSINESS IRB , IEAI_GROUP_BUSINESS IR  WHERE  IRB.IID=IR.IBUSINESSID   AND IRB.ITYPE=2   AND IR.IGROUPID="
                + groupId + "   order by IRB.IIP";
        String sql2 = " insert into IEAI_FLOW_BUSINESS(iid,IGROUPID,iip,ihostnamea,iport,ipriority,iieaitype ,isustype,itype)  values(?,?,?,?,?,?,?,?,?)";
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(sql);
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                long iid = IdGenerator.createId("IEAI_FLOW_BUSINESS", con);
                actStat = con.prepareStatement(sql2);

                actStat.setLong(1, iid);
                actStat.setLong(2, Long.parseLong(groupId));
                actStat.setString(3, String.valueOf(actRS.getString("IIP")));
                actStat.setString(4, String.valueOf(actRS.getString("IHOSTNAME")));
                actStat.setLong(5, Long.parseLong(actRS.getString("IPORT")));
                actStat.setLong(6, Long.parseLong(actRS.getString("IPRIORITY")));
                actStat.setLong(7, 0);
                actStat.setLong(8, flowId);
                actStat.setInt(9, Integer.parseInt(String.valueOf(actRS.getString("ITYPE"))));
                actStat.execute();
                actStat = null;
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "saveFlowBus", _log);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, "saveFlowBus", _log);
        }
    }

    /**
     * <li>Description:更新本次升级的服务器</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param flowId
     * @param listres
     * @param con
     * @throws RepositoryException return void
     */
    public void updateFlowBus ( long flowId, List listres, Connection con ) throws RepositoryException
    {
        String sql = "update IEAI_FLOW_BUSINESS a set a.ipriority=?,a.iieaitype=?  where a.iip =?  and a.iport=? and a.isustype=?";
        PreparedStatement ps = null;
        try
        {
            String iip = "";
            String iport = "";
            String ipriority = "";
            JSONArray jsonArr = JSONArray.fromObject(listres);
            for (int ii = 0; ii < jsonArr.size(); ii++)
            {
                iip = jsonArr.getJSONObject(ii).optString("serverIp");
                iport = jsonArr.getJSONObject(ii).optString("serverPort");
                ipriority = jsonArr.getJSONObject(ii).optString("prority");
                ps = con.prepareStatement(sql);
                ps.setInt(1, Integer.parseInt(ipriority));
                ps.setInt(2, 1);
                ps.setString(3, iip);
                ps.setInt(4, Integer.parseInt(iport));
                ps.setLong(5, flowId);

                ps.execute();
                DBResource.closePreparedStatement(ps, "saveFlowPar", _log);
                // ps = null;
            }
        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "saveFlowPar", _log);
        } finally
        {
            DBResource.closePreparedStatement(ps, "saveFlowPar", _log);
        }
    }

    /**
     * <li>Description:保存相关数据</li>
     * 
     * <AUTHOR> 2012-9-27
     * @param flowId
     * @param listres
     * @param list
     * @param flowresourceId
     * @param con return void
     * @throws RepositoryException
     */
    public void saveAllList ( long flowId, List listres, List list, String flowresourceId, Connection con ) throws RepositoryException
    {
        if (flowId != 0)
        {
            try
            {
                saveFlowPar(flowId, list, con);

                if (!"".equals(flowresourceId) && !"null".equals(flowresourceId) && flowresourceId != null)
                {
                    saveFlowBus(flowId, flowresourceId, con);
                }
                updateFlowBus(flowId, listres, con);
            } catch (Exception e)
            {
                _log.error("operation table IEAI_FLOW_BUSINESS is error");
                throw new RepositoryException(ServerError.ERR_DB_QUERY);
            }
        }
    }

    /**
     * <li>Description:保存双人复核的提交信息</li>
     * 
     * <AUTHOR> Aug 7, 2013
     * @param projectname
     * @param flowname
     * @param flowinsname
     * @param startuser
     * @param username
     * @param para
     * @param envs
     * @return
     * @throws RepositoryException return String
     * @throws UnMarshallingException
     * @throws MarshallingException
     */
    public String saveCheckInfo ( String projectname, String flowname, String flowinsname, String startuser, String username, List para, List envs )
            throws RepositoryException, UnMarshallingException, MarshallingException
    {

        String sql = "INSERT INTO IEAI_SUS_WORKITEM (IID,IPROJECTNAME,IWORKFLOWNAME,IFLOWINSTANCE,ISTRATUSER,ISTARTTIME,IEXECUSER,IEXECTIME,IBACKINFO,IBACKTIIME,ISTATE,IFLOWPARAID) VALUES(?,?,?,?,?,?,?,?,?,?,?,?)";
        String sql2 = "INSERT INTO IEAI_SUS_FLOWPARA (IID,IFLOWPARAID,ITYPE,INAME,IVALUE) VALUES(?,?,?,?,?)";
        String sql3 = "INSERT INTO  IEAI_SUS_FLOWENVS (IID,IFLOWENVSID,ITYPE,INAME,IVALUE) VALUES(?,?,?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getSchedulerConnection("saveCheckInfo", _log, Constants.IEAI_SUS);
                    long iid = IdGenerator.createId("IEAI_SUS_WORKITEM", con);
                    long fkflowid = IdGenerator.createId("IEAI_SUS_FLOWPARA_PK", con);

                    for (int i1 = 0; i1 < para.size(); i1++)
                    {
                        Map parameter = (Map) para.get(i1);

                        long iid2 = IdGenerator.createId("IEAI_SUS_FLOWPARA", con);
                        actStat = con.prepareStatement(sql2);

                        actStat.setLong(1, iid2);
                        actStat.setLong(2, fkflowid);
                        actStat.setInt(3, Integer.valueOf(String.valueOf(parameter.get("type"))));
                        actStat.setString(4, String.valueOf(parameter.get("name")));
                        actStat.setString(5, String.valueOf(parameter.get("value")));
                        actStat.execute();
                        actStat = null;
                    }

                    for (int i2 = 0; i2 < envs.size(); i2++)
                    {
                        Map parameter = (Map) envs.get(i2);

                        long iid2 = IdGenerator.createId("IEAI_SUS_FLOWENVS", con);
                        actStat = con.prepareStatement(sql3);

                        actStat.setLong(1, iid2);
                        actStat.setLong(2, fkflowid);
                        actStat.setInt(3, Integer.valueOf(String.valueOf(parameter.get("type"))));
                        actStat.setString(4, String.valueOf(parameter.get("name")));
                        actStat.setString(5, String.valueOf(parameter.get("value")));
                        actStat.execute();
                    }

                    actStat = con.prepareStatement(sql);

                    actStat.setLong(1, iid);
                    actStat.setString(2, projectname);
                    actStat.setString(3, flowname);
                    actStat.setString(4, flowinsname);
                    actStat.setString(5, startuser);
                    actStat.setString(6, Conscommon.getDateTime(System.currentTimeMillis()));
                    actStat.setString(7, username);
                    actStat.setString(8, null);
                    actStat.setString(9, "");
                    actStat.setString(10, null);
                    actStat.setInt(11, ServerEnv.FLOW_ITEM_COMMIT);
                    actStat.setLong(12, fkflowid);
                    actStat.execute();

                    con.commit();
                    break;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "saveCheckInfo", _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "saveCheckInfo", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_INSERT);
            }
        }
        return "ddd";
    }

    /**
     * <li>Description:复核人查询信息</li>
     * 
     * <AUTHOR> Aug 8, 2013
     * @param iid
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException return Map
     */
    public Map getCheckInfo ( long iid ) throws RepositoryException, UnMarshallingException
    {
        Map map = new Hashtable();

        String sql = "SELECT AA.IPROJECTNAME,AA.IWORKFLOWNAME,AA.IFLOWINSTANCE,AA.IEXECUSER,AA.IFLOWPARAID FROM IEAI_SUS_WORKITEM AA WHERE IID=?";
        String sql2 = "SELECT BB.INAME,BB.IVALUE,BB.ITYPE FROM IEAI_SUS_FLOWPARA BB WHERE BB.IFLOWPARAID=?";
        long flowpkid = 0;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getCheckInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, iid);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        map.put("IPROJECTNAME", actRS.getString("IPROJECTNAME"));
                        map.put("IWORKFLOWNAME", actRS.getString("IWORKFLOWNAME"));
                        map.put("IFLOWINSTANCE", actRS.getString("IFLOWINSTANCE"));
                        map.put("IEXECUSER", actRS.getString("IEXECUSER"));
                        flowpkid = actRS.getLong("IFLOWPARAID");
                    }

                    actStat = con.prepareStatement(sql2);
                    actStat.setLong(1, flowpkid);
                    actRS = actStat.executeQuery();
                    List list = new ArrayList();
                    int ap = 0;
                    while (actRS.next())
                    {
                        Map mappara = new Hashtable();

                        mappara.put("index", new Integer(ap));
                        mappara.put("parameterName", actRS.getString("INAME"));
                        mappara.put("type", DataTypeNames.getStringTypeByIntType(Integer.parseInt(actRS.getString("ITYPE"))));
                        mappara.put("value", IDataMarshallerHelper.revertValue(actRS.getString("IVALUE")));
                        mappara.put("desc", "");
                        ap++;
                        list.add(mappara);
                    }
                    map.put("para", list);

                    break;
                } catch (SQLException e)
                {
                    _log.error("getCheckInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getCheckInfo", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    /**
     * <li>Description:提交人查询信息</li>
     * 
     * <AUTHOR> Aug 8, 2013
     * @param iid
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException return Map
     */
    public Map getCheckInfoComm ( long iid ) throws RepositoryException, UnMarshallingException
    {
        Map map = new Hashtable();

        String sql = "SELECT AA.IPROJECTNAME,AA.IWORKFLOWNAME,AA.IFLOWINSTANCE,AA.IEXECUSER,AA.IFLOWPARAID,AA.IBACKINFO FROM IEAI_SUS_WORKITEM AA WHERE IID=?";
        String sql2 = "SELECT BB.INAME,BB.IVALUE,BB.ITYPE FROM IEAI_SUS_FLOWPARA BB WHERE BB.IFLOWPARAID=?";
        long flowpkid = 0;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getCheckInfo", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, iid);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        map.put("IPROJECTNAME", actRS.getString("IPROJECTNAME"));
                        map.put("IWORKFLOWNAME", actRS.getString("IWORKFLOWNAME"));
                        map.put("IFLOWINSTANCE", actRS.getString("IFLOWINSTANCE"));
                        map.put("IEXECUSER", actRS.getString("IEXECUSER"));
                        map.put("IBACKINFO", actRS.getString("IBACKINFO"));
                        flowpkid = actRS.getLong("IFLOWPARAID");
                    }

                    actStat = con.prepareStatement(sql2);
                    actStat.setLong(1, flowpkid);
                    actRS = actStat.executeQuery();
                    List list = new ArrayList();
                    int ap = 0;
                    while (actRS.next())
                    {
                        Map mappara = new Hashtable();

                        mappara.put("index", new Integer(ap));
                        mappara.put("parameterName", actRS.getString("INAME"));
                        mappara.put("type", DataTypeNames.getStringTypeByIntType(Integer.parseInt(actRS.getString("ITYPE"))));
                        mappara.put(
                            "value",
                            getTextArea(i, String.valueOf(IDataMarshallerHelper.revertValue(actRS.getString("IVALUE"))), "paramValue",
                                DataTypeNames.getStringTypeByIntType(Integer.parseInt(actRS.getString("ITYPE")))));
                        mappara.put("desc", "");
                        ap++;
                        list.add(mappara);
                    }
                    map.put("para", list);

                    break;
                } catch (SQLException e)
                {
                    _log.error("getCheckInfo is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getCheckInfo", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    public String getTextArea ( int index, String value, String name, String typeName )
    {
        if (null == value)
        {
            value = "";
        }
        StringBuffer script = new StringBuffer();
        script.append("<input type='text'  name='");
        script.append(name + "[");
        script.append(index).append("]'");
        script.append("value='" + value + "' class='fix_length'");
        script.append(">");
        return script.toString();
    }

    /**
     * <li>Description:获取工作流的输入参数</li>
     * 
     * <AUTHOR> Aug 8, 2013
     * @param iid
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException return List
     */
    public List getFlowpara ( long iid ) throws RepositoryException, UnMarshallingException
    {
        List list = new ArrayList();
        String sql = "SELECT BB.INAME,BB.IVALUE,BB.ITYPE FROM IEAI_SUS_FLOWPARA BB WHERE BB.IFLOWPARAID=(SELECT IFLOWPARAID FROM IEAI_SUS_WORKITEM WHERE IID=?)";
        long flowpkid = 0;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getFlowpara", _log, Constants.IEAI_SUS);

                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, iid);
                    actRS = actStat.executeQuery();
                    int ap = 0;
                    while (actRS.next())
                    {
                        Map mappara = new Hashtable();

                        mappara.put("type", actRS.getString("ITYPE"));
                        mappara.put("name", actRS.getString("INAME"));
                        mappara.put("value", IDataMarshallerHelper.revertValue(actRS.getString("IVALUE")));
                        list.add(mappara);
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("getFlowpara is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getFlowpara", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * <li>Description:获取工作流的环境变量</li>
     * 
     * <AUTHOR> Aug 8, 2013
     * @param iid
     * @return
     * @throws RepositoryException
     * @throws UnMarshallingException return List
     */
    public List getFlowenv ( long iid ) throws RepositoryException, UnMarshallingException
    {
        List list = new ArrayList();
        String sql = "SELECT BB.INAME,BB.IVALUE,BB.ITYPE FROM IEAI_SUS_FLOWENVS BB WHERE BB.IFLOWENVSID=(SELECT IFLOWPARAID FROM IEAI_SUS_WORKITEM WHERE IID=?)";
        long flowpkid = 0;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getFlowenv", _log, Constants.IEAI_SUS);

                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, iid);
                    actRS = actStat.executeQuery();
                    int ap = 0;
                    while (actRS.next())
                    {
                        Map mappara = new Hashtable();

                        mappara.put("type", actRS.getString("ITYPE"));
                        mappara.put("name", actRS.getString("INAME"));
                        mappara.put("value", IDataMarshallerHelper.revertValue(actRS.getString("IVALUE")));
                        list.add(mappara);
                    }
                    break;
                } catch (SQLException e)
                {
                    _log.error("getFlowenv is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getFlowenv", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * <li>Description:执行完成后更改复核流程状态</li>
     * 
     * <AUTHOR> Aug 8, 2013
     * @param iid
     * @param state
     * @throws RepositoryException
     * @throws UnMarshallingException return void
     */
    public void updateItem ( long iid, int state ) throws RepositoryException
    {
        String sql = "UPDATE IEAI_SUS_WORKITEM SET ISTATE=?, IEXECTIME=? WHERE IID=?";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("updateItem", _log, Constants.IEAI_SUS);

                    ps = con.prepareStatement(sql);
                    ps.setInt(1, state);
                    ps.setString(2, Conscommon.getDateTime(System.currentTimeMillis()));
                    ps.setLong(3, iid);

                    ps.execute();
                    ps = null;
                    con.commit();
                    break;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "updateItem", _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, "updateItem", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * <li>Description:任务关闭</li>
     * 
     * <AUTHOR> Aug 9, 2013
     * @param iid
     * @param state
     * @throws RepositoryException return void
     */
    public void updateItemClose ( long iid, int state ) throws RepositoryException
    {
        String sql = "UPDATE IEAI_SUS_WORKITEM SET ISTATE=?, ICLOSETIME=? WHERE IID=?";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("updateItem", _log, Constants.IEAI_SUS);

                    ps = con.prepareStatement(sql);
                    ps.setInt(1, state);
                    ps.setString(2, Conscommon.getDateTime(System.currentTimeMillis()));
                    ps.setLong(3, iid);

                    ps.execute();
                    ps = null;
                    con.commit();
                    break;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e, "updateItem", _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, "updateItem", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * <li>Description:打回信息修改</li>
     * 
     * <AUTHOR> Aug 8, 2013
     * @param iid
     * @param state
     * @param backinfo
     * @throws RepositoryException return void
     */
    public void backItem ( long iid, int state, String backinfo ) throws RepositoryException
    {
        String sql = "UPDATE IEAI_SUS_WORKITEM SET ISTATE=? ,IBACKTIIME=?, IBACKINFO=? WHERE IID=?";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement ps = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("backItem", _log, Constants.IEAI_SUS);

                    ps = con.prepareStatement(sql);
                    ps.setInt(1, state);
                    ps.setString(2, Conscommon.getDateTime(System.currentTimeMillis()));
                    ps.setString(3, backinfo);
                    ps.setLong(4, iid);

                    ps.execute();
                    ps = null;
                    con.commit();
                    break;
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "backItem", _log);
                } finally
                {
                    DBResource.closePSConn(con, ps, "backItem", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    public List getExport ( int type ) throws SQLException, RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT IHOSTNAME ,IIP ,IPORT ,IPRIORITY ,ISYSTEMTYPE,IMDTYPE FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=" + type;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getExport", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        String res = actRS.getString("IHOSTNAME") + "," + actRS.getString("IIP") + "," + actRS.getInt("IPORT") + ","
                                + actRS.getInt("IPRIORITY") + "," + actRS.getString("ISYSTEMTYPE") + "," + actRS.getString("IMDTYPE");
                        list.add(res);
                    }
                } catch (SQLException e)
                {
                    _log.error("getExport is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getExport", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 初始化查询资源组扩展参数信息 <li>Description:</li>
     * 
     * <AUTHOR> 2014-6-10
     * @param type
     * @param resourceGroupId
     * @return
     * @throws RepositoryException return List
     */
    public List getExtendParamete ( int type, long resourceGroupId ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT IID,IRESGROUPID,INAME,IVALUE FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE ITYPE=? AND IRESGROUPID=?  ORDER BY INAME";
        ResourceGroup resourceGroup = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getExtendParamete", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actStat.setLong(2, resourceGroupId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        resourceGroup = new ResourceGroup();
                        resourceGroup.setExtendsId(actRS.getLong("IID"));
                        resourceGroup.setExtendsParaName(actRS.getString("INAME"));
                        resourceGroup.setIid(actRS.getLong("IRESGROUPID"));
                        resourceGroup.setExtendsParaValue(null == actRS.getString("IVALUE") ? "" : actRS.getString("IVALUE"));
                        list.add(resourceGroup);
                    }
                } catch (SQLException e)
                {
                    _log.error("getExtendParamete is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getExtendParamete", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 保存资源组全局属性参数 <li>Description:</li>
     * 
     * <AUTHOR> 2014-6-10
     * @param resourceGroupId
     * @param resourceGroup
     * @param type
     * @throws RepositoryException return void
     */
    public void saveExtendParamete ( String resourceGroupId, ResourceGroup resourceGroup, int type ) throws RepositoryException
    {
        String sqlu = " UPDATE  IEAI_RESGROUP_EXTENDSPARAMETER SET INAME=? ,IVALUE=?  where IID=?";
        String sqli = "INSERT INTO IEAI_RESGROUP_EXTENDSPARAMETER(IID,IRESGROUPID,INAME,IVALUE,ITYPE) VALUES (?,?,?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                con = DBResource.getConnection("saveExtendParamete", _log, Constants.IEAI_SUS);
                long tid = resourceGroup.getExtendsId();
                if (tid != -1)
                {

                    try
                    {
                        actStat = con.prepareStatement(sqlu);
                        actStat.setString(1, resourceGroup.getExtendsParaName());
                        actStat.setString(2, resourceGroup.getExtendsParaValue());
                        actStat.setLong(3, resourceGroup.getExtendsId());
                        actStat.executeUpdate();
                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "saveExtendParamete", _log);
                    }
                } else
                {
                    if (!"".equals(resourceGroup.getExtendsParaName().trim()))
                    {
                        long iid = IdGenerator.createId("IEAI_RESGROUP_EXTENDSPARAMETER", con);
                        try
                        {
                            actStat = con.prepareStatement(sqli);
                            actStat.setLong(1, iid);
                            actStat.setLong(2, Long.parseLong(resourceGroupId));
                            actStat.setString(3, resourceGroup.getExtendsParaName());
                            actStat.setString(4, null == resourceGroup.getExtendsParaValue() ? "" : resourceGroup.getExtendsParaValue());
                            actStat.setInt(5, type);
                            actStat.executeUpdate();
                            actStat = null;
                            con.commit();
                        } catch (SQLException e)
                        {
                            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        } finally
                        {
                            DBResource.closePSConn(con, actStat, "saveExtendParamete", _log);
                        }
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * 服务器级别获取资源组属性参数值 <li>Description:</li>
     * 
     * <AUTHOR> 2014-6-10
     * @param type
     * @param resourceGroupId
     * @param sId
     * @return
     * @throws RepositoryException return List
     */
    public List getExtendParameteValue ( int type, long resourceGroupId, long sId ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT DISTINCT RE.IID,RE.IRESGROUPID,RE.INAME,RV.ISERVERID,RV.IVALUE,RV.IID AS VID,RE.IRESNAME FROM (SELECT RG.IRESNAME,R.IID,R.IRESGROUPID,R.INAME,R.ITYPE FROM IEAI_RESOURCE_GROUP RG, IEAI_RESGROUP_EXTENDSPARAMETER R WHERE RG.IID=R.IRESGROUPID AND R.IRESGROUPID=?) RE LEFT JOIN IEAI_RESGROUP_EXTENDSPARAMETER_VALUE RV ON RE.IID = RV.IPARAMETERID AND  RE.ITYPE=? AND RV.ISERVERID=? AND RE.IRESGROUPID=?  ORDER BY INAME";
        ServerInfo serverInfo = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getExtendParamete", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, resourceGroupId);
                    actStat.setLong(2, type);
                    actStat.setLong(3, sId);
                    actStat.setLong(4, resourceGroupId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        serverInfo = new ServerInfo();
                        serverInfo.setExtendsId(actRS.getLong("IID"));
                        serverInfo.setExtendsParaName(actRS.getString("INAME"));
                        serverInfo.setResGroupId(actRS.getLong("IRESGROUPID"));
                        serverInfo.setIid(actRS.getLong("VID"));
                        serverInfo.setServerId(sId);
                        serverInfo.setExtendsParaValue(null == actRS.getString("IVALUE") ? "" : actRS.getString("IVALUE"));
                        serverInfo.setResGroupName(actRS.getString("IRESNAME"));
                        list.add(serverInfo);
                    }
                } catch (SQLException e)
                {
                    _log.error("getExtendParameteValue is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getExtendParameteValue", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    public List getExtendParameteValueHeads ( int type, long resourceGroupId, long sId ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT DISTINCT RE.IID,RE.IRESGROUPID,RE.INAME,RV.ISERVERID,RV.IVALUE,RE.IVALUE as IVALUEOTHER,RV.IID AS VID,RE.IRESNAME FROM (SELECT RG.IRESNAME,R.IID,R.IRESGROUPID,R.INAME,R.IVALUE,R.ITYPE FROM IEAI_RESOURCE_GROUP RG, IEAI_RESGROUP_EXTENDSPARAMETER R WHERE RG.IID=R.IRESGROUPID AND R.IRESGROUPID=?) RE LEFT JOIN IEAI_RESGROUP_EXTENDSPARAMETER_VALUE RV ON RE.IID = RV.IPARAMETERID AND  RE.ITYPE=? AND RV.ISERVERID=? AND RE.IRESGROUPID=?  ORDER BY INAME";
        ServerInfo serverInfo = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getExtendParamete", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, resourceGroupId);
                    actStat.setLong(2, type);
                    actStat.setLong(3, sId);
                    actStat.setLong(4, resourceGroupId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        serverInfo = new ServerInfo();
                        serverInfo.setExtendsId(actRS.getLong("IID"));
                        serverInfo.setExtendsParaName(actRS.getString("INAME"));
                        serverInfo.setResGroupId(actRS.getLong("IRESGROUPID"));
                        serverInfo.setIid(actRS.getLong("VID"));
                        serverInfo.setServerId(sId);
                        serverInfo.setExtendsParaValue(null == actRS.getString("IVALUE") ? actRS.getString("IVALUEOTHER") : actRS.getString("IVALUE"));
                        serverInfo.setResGroupName(actRS.getString("IRESNAME"));
                        list.add(serverInfo);
                    }
                } catch (SQLException e)
                {
                    _log.error("getExtendParameteValue is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getExtendParameteValue", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 存储资源组全局参数对应组内服务器设置的参数值 <li>Description:</li>
     * 
     * <AUTHOR> 2014-6-10
     * @param resourceGroupId
     * @param serverInfo
     * @param type
     * @throws RepositoryException return void
     */
    public void saveExtendParameteValue ( String resourceGroupId, ServerInfo serverInfo, int type ) throws RepositoryException
    {
        String sqlu = " UPDATE  IEAI_RESGROUP_EXTENDSPARAMETER_VALUE SET IVALUE=? where IID=?";
        String sqli = "INSERT INTO IEAI_RESGROUP_EXTENDSPARAMETER_VALUE(IID,ISERVERID,IPARAMETERID,IVALUE) VALUES (?,?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                con = DBResource.getConnection("saveExtendParameteValue", _log, Constants.IEAI_SUS);
                long tid = serverInfo.getIid();
                if (tid != -1 && tid != 0)
                {
                    try
                    {
                        actStat = con.prepareStatement(sqlu);
                        actStat.setString(1, serverInfo.getExtendsParaValue());
                        actStat.setLong(2, tid);
                        actStat.executeUpdate();
                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "saveExtendParameteValue", _log);
                    }
                } else
                {
                    try
                    {
                        long iid = IdGenerator.createId("IEAI_RESGROUP_EXTENDSPARAMETER_VALUE", con);
                        actStat = con.prepareStatement(sqli);
                        actStat.setLong(1, iid);
                        actStat.setLong(2, serverInfo.getServerId());
                        actStat.setLong(3, serverInfo.getExtendsId());
                        actStat.setString(4, serverInfo.getExtendsParaValue());
                        actStat.executeUpdate();
                        actStat = null;
                        con.commit();
                    } catch (SQLException e)
                    {
                        DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "saveExtendParameteValue", _log);
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * 删除扩展参数属性 <li>Description:</li>
     * 
     * <AUTHOR> 2014-6-10
     * @param id
     * @throws RepositoryException return void
     */
    public void deleteExtendParamete ( long id ) throws RepositoryException
    {
        String sql = "DELETE FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE IID=? ";
        String sqlNext = "DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE IPARAMETERID=? ";
        String resName=getResGroupNameByPid(id);
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                Connection con4UpdateBlob = null;
                try
                {
                    con = DBResource.getConnection("deleteExtendParamete", _log, Constants.IEAI_SUS);
                    con4UpdateBlob = DBResource.getSchedulerConnection("deleteExtendParamete:第二个连接", _log,
                        Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    actStat = null;
                    actStat = con.prepareStatement(sqlNext);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    con.commit();
                    
                    
                    boolean sus_reschange_effect_switch=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RESCHANGE_EFFECT_SWITCH, true);
                    if(sus_reschange_effect_switch){
                        //参数值 落地后，修改blob数据,并且用第二个数据库连接
                        BlobUpdateService.updateBlobs_with_PersonParamChanged(resName, con4UpdateBlob,null);
                        con4UpdateBlob.commit();
                    }
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "deleteExtendParamete", _log);
                    DBResource.closeConnection(con4UpdateBlob,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * 获取设置过的操作系统类型 <li>Description:</li>
     * 
     * <AUTHOR> 2014-6-11
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getModelSystemType () throws RepositoryException
    {
        List list = new ArrayList();
        // String sql =
        // "SELECT DISTINCT ISYSTEMTYPE FROM IEAI_RESOURCE_BUSINESS  where ISYSTEMTYPE !=NULL ORDER BY ISYSTEMTYPE";
        // update fengchun_zhang sql for result is null
        String sql = "SELECT ISYSTEMTYPE FROM (SELECT DISTINCT ISYSTEMTYPE FROM IEAI_RESOURCE_BUSINESS  ORDER BY ISYSTEMTYPE) WHERE ISYSTEMTYPE IS NOT NULL";
        // update end
        String[] result = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getModelSystemType", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        result = new String[2];
                        result[0] = actRS.getString("ISYSTEMTYPE");
                        result[1] = actRS.getString("ISYSTEMTYPE");
                        list.add(result);
                    }
                } catch (SQLException e)
                {
                    _log.error("getModelSystemType is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getModelSystemType", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 获取操作系统类型，用于分页
     */
    public List getModelSystemTypeSplitPage () throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT ISYSTEMTYPE FROM (SELECT DISTINCT ISYSTEMTYPE FROM IEAI_RESOURCE_BUSINESS  ORDER BY ISYSTEMTYPE) WHERE ISYSTEMTYPE IS NOT NULL AND ISYSTEMTYPE !='' ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getModelSystemTypeSplitPage", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        ServerInfo sf = new ServerInfo();
                        sf.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        list.add(sf);
                    }
                } catch (SQLException e)
                {
                    _log.error("getModelSystemTypeSplitPage is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getModelSystemTypeSplitPage", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 获取设置过的中间件类型 <li>Description:</li>
     * 
     * <AUTHOR> 2014-7-31
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getMdype () throws RepositoryException
    {
        List list = new ArrayList();
        // String sql =
        // "SELECT DISTINCT IMDTYPE FROM IEAI_RESOURCE_BUSINESS where IMDTYPE !=NULL ORDER BY IMDTYPE";
        // add by fengchun_zhang sql for result is null
        String sql = "SELECT IMDTYPE FROM (SELECT DISTINCT IMDTYPE FROM IEAI_RESOURCE_BUSINESS  ORDER BY IMDTYPE) WHERE IMDTYPE IS NOT NULL";
        // add end
        String[] result = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getMdype", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        result = new String[2];
                        result[0] = actRS.getString("IMDTYPE");
                        result[1] = actRS.getString("IMDTYPE");
                        list.add(result);
                    }
                } catch (SQLException e)
                {
                    _log.error("getMdype is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getMdype", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 获取中间件，用于分页
     */
    public List getMdypeSplitPage () throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT IMDTYPE FROM (SELECT DISTINCT IMDTYPE FROM IEAI_RESOURCE_BUSINESS  ORDER BY IMDTYPE) WHERE IMDTYPE IS NOT NULL AND IMDTYPE !='' ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getMdypeSplitPage", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        ServerInfo sf = new ServerInfo();
                        sf.setMdtype(actRS.getString("IMDTYPE"));
                        list.add(sf);
                    }
                } catch (SQLException e)
                {
                    _log.error("getMdypeSplitPage is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getMdypeSplitPage", _log);
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * 保存资源组全局属性参数 <li>Description:</li>
     * 为sus服务
     * <AUTHOR> 2014-8-18
     * @param resourceGroupId
     * @param resourceGroup
     * @param type
     * @throws RepositoryException
     *             return void
     */
    public void saveResourceGroupExtendParameter(Long resourceGroupId,
            ResourceGroupParameterDTO resourceGroupParam, int type) throws RepositoryException {
        String sqlu = " UPDATE IEAI_RESGROUP_EXTENDSPARAMETER SET INAME=? ,IVALUE=? ,IPARAMTYPE=? ,IPRAM_CONER=? where IID=?";
        String sqli = "INSERT INTO IEAI_RESGROUP_EXTENDSPARAMETER(IID,IRESGROUPID,INAME,IPARAMTYPE,IVALUE,ITYPE,IPRAM_CONER) VALUES (?,?,?,?,?,?,?)";
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                Connection con = null;
                Connection con4UpdateBlob = null;
                con = DBResource.getConnection("saveResourceGroupExtendParameter", _log,Constants.IEAI_SUS);
                con4UpdateBlob = DBResource.getConnection("saveResourceGroupExtendParameter:第二个连接", _log,Constants.IEAI_SUS);
                
                long tid = resourceGroupParam.getId();
                String resName=getResName(resourceGroupId);
                if (!"".equals(tid) && tid != -1) {

                    try {
                        actStat = con.prepareStatement(sqlu);
                        actStat.setString(1, resourceGroupParam.getName());
                        actStat.setString(2, resourceGroupParam.getValue());
                        actStat.setString(3, resourceGroupParam.getParamType());
                        actStat.setString(4, resourceGroupParam.getIpram_coner());
                        actStat.setLong(5, resourceGroupParam.getId());
                        actStat.executeUpdate();
                        con.commit();
                        
                        
                        boolean sus_reschange_effect_switch=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RESCHANGE_EFFECT_SWITCH, true);
                        if(sus_reschange_effect_switch){
                            //参数值 落地后，修改blob数据,并且用第二个数据库连接
                            BlobUpdateService.updateBlobs_with_PersonParamChanged(resName, con4UpdateBlob,null);
                            con4UpdateBlob.commit();
                        }
                    } catch (SQLException e) {
                        _log.error("saveResourceGroupExtendParameter is error ! "
                                + e.getMessage());
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally {
                        DBResource.closePSConn(con, actStat,
                                "saveResourceGroupExtendParameter", _log);
                        DBResource.closeConnection(con4UpdateBlob,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        if (SystemConfig.isElapsedTime())
                        {
                            _log.info("Jdbc Connection closeConnection and method:saveResourceGroupExtendParameter");
                        }
                    }
                } else {
                    if (!"".equals(resourceGroupParam.getName().trim())) {
                        long iid = IdGenerator.createId(
                                "IEAI_RESGROUP_EXTENDSPARAMETER", con);
                        try {
                            actStat = con.prepareStatement(sqli);
                            actStat.setLong(1, iid);
                            actStat.setLong(2, resourceGroupId);
                            actStat.setString(3, resourceGroupParam.getName());
                            actStat.setString(4, resourceGroupParam.getParamType());
                            actStat.setString(5,
                                    null==resourceGroupParam.getValue()?"":resourceGroupParam.getValue());
                            actStat.setInt(6, type);
                            actStat.setString(7, resourceGroupParam.getIpram_coner());
                            actStat.executeUpdate();
                            actStat = null;
                            con.commit();
                            
                            
                            
                            boolean sus_reschange_effect_switch=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RESCHANGE_EFFECT_SWITCH, true);
                            if(sus_reschange_effect_switch){
                                //参数值 落地后，修改blob数据,并且用第二个数据库连接
                                BlobUpdateService.updateBlobs_with_PersonParamChanged(resName, con4UpdateBlob,null);
                                con4UpdateBlob.commit();
                            }
                        } catch (SQLException e) {
                            DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        } finally {
                            DBResource.closePSConn(con, actStat,
                                    "saveResourceGroupExtendParameter", _log);
                            DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(),
                                _log);
                            if (SystemConfig.isElapsedTime())
                            {
                                _log.info("Jdbc Connection closeConnection and method:saveResourceGroupExtendParameter");
                            }
                        }
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource.throwRepositoryException(i,
                        ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * <li>Description:获取已经配置的补丁分发的资源组</li>
     * 
     * <AUTHOR> Aug 10, 2012
     * @param type
     * @return
     * @throws RepositoryException
     *             return List
     */
    public List getResGroup(int type,String gname) throws RepositoryException {
        List list = new ArrayList();
        //String sql = "SELECT IID, IRESNAME FROM IEAI_RESOURCE_GROUP WHERE ITYPE=? ORDER BY IRESNAME";
        // modified by liyang.
        String sql="";
        if(null==gname||"".equals(gname)){
             sql = "SELECT IID, IRESNAME, IRESDES FROM IEAI_RESOURCE_GROUP WHERE  ITYPE=? ORDER BY IRESNAME";
        }else{
             sql = "SELECT IID, IRESNAME, IRESDES FROM IEAI_RESOURCE_GROUP WHERE IRESNAME!='"+gname+"' AND ITYPE=? ORDER BY IRESNAME";
        }
        String[] result = null;
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getResGroup", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actRS = actStat.executeQuery();

                    while (actRS.next()) {
                        result = new String[3]; // 数组size 由2改为3
                        result[0] = String.valueOf(actRS.getLong("IID"));
                        result[1] = actRS.getString("IRESNAME");
                        result[2] = actRS.getString("IRESDES"); //add by liyang
                        list.add(result);
                    }
                } catch (SQLException e) {
                    _log.error("getResGroup is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getResGroup",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResGroup");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    /**
     * <li>Description:保存资源组信息</li>
     * 为SUS服务
     * <AUTHOR> 2014/08/18
     * @param resourceGroup
     * @param type
     * @throws RepositoryException
     *             return void
     */
    public void saveResourceGroup(ResourceGroupDTO resourceGroup, int type) throws RepositoryException {
        for (int i = 0;; i++)
        {
            PreparedStatement actStat = null;
            PreparedStatement onlineGropStat = null;
            Connection con = null;
            con = DBResource.getConnection("saveResourceGroup", _log ,Constants.IEAI_SUS);
            long tid = resourceGroup.getId();
            if (!"".equals(tid) && tid != -1 && !"".equals(resourceGroup.getName().trim())) {
                String old_ResName=getResName( tid);
                String sql = " update  IEAI_RESOURCE_GROUP set iresname=?,iresdes=?,itype=? where iid=?";
                String OnlineGropSql=" UPDATE IEAI_SUS_ONLINE_RESGROUP SET IRESNAME=?  WHERE IRESID=?";
                try {
                   try {
                        actStat = con.prepareStatement(sql);
                        actStat.setString(1, resourceGroup.getName());
                        actStat.setString(2, resourceGroup.getDescription());
                        actStat.setInt(3, type);
                        actStat.setLong(4, tid);
                        actStat.executeUpdate();
                        
                        onlineGropStat=con.prepareStatement(OnlineGropSql);
                        onlineGropStat.setString(1, resourceGroup.getName());
                        onlineGropStat.setLong(2, tid);
                        onlineGropStat.executeUpdate();
                        con.commit();
                        FlowstartinstManagerCEB.update_RES(old_ResName, resourceGroup.getName(), Constants.IEAI_SUS);
                    } catch (SQLException e) {
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally {
                        DBResource.closePSConn(con, actStat,
                                "saveResourceGroup", _log);
                        if (SystemConfig.isElapsedTime())
                        {
                            _log.info("Jdbc Connection closeConnection and method:saveResourceGroup");
                        }
                    }
                } catch (RepositoryException ex) {
                    DBResource.throwRepositoryException(i,
                            ServerError.ERR_DB_UPDATE);
                }
            } else {
                String sql = "INSERT INTO IEAI_RESOURCE_GROUP(iid,iresname,iresdes,itype) VALUES (?,?,?,?)";
                if (!"".equals(resourceGroup.getName().trim())) {
                    long iid = IdGenerator.createId("IEAI_RESOURCE_GROUP", con);
                    try {
                        try {
                            actStat = con.prepareStatement(sql);
                            actStat.setLong(1, iid);
                            actStat.setString(2, resourceGroup.getName());
                            actStat.setString(3, resourceGroup.getDescription());
                            actStat.setInt(4, type);
                            actStat.executeUpdate();
                            con.commit();
                        } catch (SQLException e) {
                            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        } finally {
                            DBResource.closePSConn(con, actStat,
                                    "saveResourceGroup", _log);
                            if (SystemConfig.isElapsedTime())
                            {
                                _log.info("Jdbc Connection closeConnection and method:saveResourceGroup");
                            }
                        }
                    } catch (RepositoryException ex) {
                        DBResource.throwRepositoryException(i,
                            ServerError.ERR_DB_INSERT);
                    }
                }
            }
            break;
        }
    }
    
    /**
     * <li>Description:查看指定资源组名称是否存在</li>
     * 
     * <AUTHOR> 2014-11-24
     * @param newGroupName,
     * @return
     * @throws RepositoryException
     *             return List
     */
    public String getResName(long resid)
            throws RepositoryException {
        String sql = " select iresname  from IEAI_RESOURCE_GROUP where iid=?";
        String resName="";
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getResName", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, resid);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resName=actRS.getString("iresname");
                        break;
                    }
                } catch (SQLException e) {
                    _log.error("getResName is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getResName",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResName");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resName;
    }
    
    /**
     * <li>Description:查看指定资源组名称是否存在</li>
     * 
     * <AUTHOR> 2014-11-24
     * @param newGroupName,
     * @return
     * @throws RepositoryException
     *             return List
     */
    public boolean existResourceGroupName(String newGroupName, int type)
            throws RepositoryException {
        boolean res = false;
        String sql = " select count(*) as total from IEAI_RESOURCE_GROUP where iresname=? and ITYPE=? ";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("existResourceGroupName", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, newGroupName);
                    actStat.setLong(2, type);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        long total = actRS.getLong("total");
                        if(total>0){
                            res = true;
                        }
                        break;
                    }
                } catch (SQLException e) {
                    _log.error("existResourceGroupName is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "existResourceGroupName",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:existResourceGroupName");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return res;
    }
    
    public void copyExtendParameters(Long oldGroupId, Long newGroupId, int type, Connection con)
            throws RepositoryException {
        String sql1 = " select * from IEAI_RESGROUP_EXTENDSPARAMETER where IRESGROUPID=? and itype=?";
        String sql2 = " insert into IEAI_RESGROUP_EXTENDSPARAMETER(IID ,iresgroupid ,iname,iparamtype,ITYPE) VALUES(?,?,?,?,?) ";
        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(sql1);
            actStat.setLong(1, oldGroupId);
            actStat.setInt(2, type);
            actRS = actStat.executeQuery();
            List<ResourceGroupParameterDTO> tempParams = new ArrayList<ResourceGroupParameterDTO>();
            while (actRS.next())
            {
                ResourceGroupParameterDTO p = new ResourceGroupParameterDTO();
                p.setName(actRS.getString("iname"));
                p.setParamType(actRS.getString("iparamtype"));
                tempParams.add(p);
            }
            for (ResourceGroupParameterDTO rp : tempParams)
            {
                long iid = IdGenerator.createId("IEAI_RESGROUP_EXTENDSPARAMETER", con);
                actStat = con.prepareStatement(sql2);
                actStat.setLong(1, iid);
                actStat.setLong(2, newGroupId);
                actStat.setString(3, rp.getName());
                actStat.setString(4, rp.getParamType());
                actStat.setInt(5, type);
                actStat.executeUpdate();
            }

        } catch (SQLException e)
        {
            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e, "copyExtendParameters", _log);
        } finally
        {
            DBResource.closePreparedStatement(actStat, "copyExtendParameters", _log);
        }
    }
    
    /**
     * <li>Description:复制资源组信息</li>
     * 为SUS服务
     * <AUTHOR> 2014/11/24
     * @param groupId
     * @param newGroupName
     * @throws RepositoryException
     *             return void
     */
    public void copyResourceGroup(Long groupId, String newGroupName, int type) throws RepositoryException {
        for (int i = 0;; i++)
        {
            PreparedStatement actStat = null;
            Connection con = null;
            con = DBResource.getSchedulerConnection("copyResourceGroup", _log, Constants.IEAI_SUS);
            
            String sql = " INSERT INTO IEAI_RESOURCE_GROUP(iid,iresname,itype) VALUES (?,?,?) ";
            long iid = IdGenerator.createId("IEAI_RESOURCE_GROUP", con);
            try
            {
                try
                {
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, iid);
                    actStat.setString(2, newGroupName);
                    actStat.setInt(3, type);
                    actStat.executeUpdate();

                    copyExtendParameters(groupId, iid, type, con);
                    con.commit();
                } catch (SQLException e)
                {
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "copyResourceGroup", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:copyResourceGroup");
                    }
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
            }
            break;
        }
    }
    
    public List<ResPrj> canDeleteGroup(String strDelIds) throws RepositoryException {
        String sql = " select count(*) as TOTAL from IEAI_MAINFLOW_RESOURCE where iresourceid=? ";
        String sqlQuery = " select IRESNAME from IEAI_RESOURCE_GROUP where iid=? ";
        String canNotDeleteName = null;
        StringBuffer buf = new StringBuffer("");
        buf.append(" SELECT R.IRESNAME  ,MR.IPROJECTNAME ,MR.IRESOURCEID");
        buf.append(" FROM IEAI_MAINFLOW_RESOURCE MR ,IEAI_RESOURCE_GROUP R");
        buf.append(" WHERE MR.IRESOURCEID =R.IID");
        buf.append("  AND MR.IRESOURCEID IN (" ).append(strDelIds).append(")");
        buf.append(" ORDER BY R.IRESNAME  ,MR.IPROJECTNAME");
        
        List<ResPrj> resPrjlist =new ArrayList<ResPrj>();
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                ResPrj resPrj =null;
                try {
                    con = DBResource.getConnection("canDeleteGroup", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(buf.toString());
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resPrj = new ResPrj();
                        resPrj.setResName(actRS.getString("IRESNAME"));
                        resPrj.setPrjName(actRS.getString("IPROJECTNAME"));
                        resPrj.setResourceid(actRS.getLong("IRESOURCEID")); 
                        resPrjlist.add(resPrj);
                    }
                } catch (SQLException e) {
                    _log.error("canDeleteGroup is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat,
                            "canDeleteGroup", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:canDeleteGroup");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resPrjlist;
    }
    
    /**
     * <li>Description: 删除资源组</li> 
     * <AUTHOR>
     * 2014-8-22 
     * @param id
     * @throws RepositoryException
     * return void
     */
    public void delResourceGroup(long id, int type) throws RepositoryException {
        String sql = "DELETE FROM IEAI_RESOURCE_GROUP WHERE IID=? and ITYPE=? ";
        String sql2 = "DELETE FROM IEAI_GROUP_BUSINESS WHERE IGROUPID=? and ITYPE=? ";
        String sql3 = "DELETE FROM IEAI_MAINFLOW_RESOURCE WHERE IRESOURCEID=? ";
        String sql4 = "DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE IPARAMETERID IN (SELECT IID FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE IRESGROUPID=?) ";
        String sql5 = "DELETE FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE IRESGROUPID=? and ITYPE=?　";
        String sql7 = "DELETE FROM IEAI_RESGROUP_PREPARE WHERE IRESID=?　";
        //删除已经配置过的资源组
        String sql6 = "DELETE FROM IEAI_SUS_ONLINE_RESGROUP WHERE IRESID=?　";
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("delResourceGroup", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, id);
                    actStat.setInt(2, type);
                    actStat.executeUpdate();

                    actStat = null;
                    actStat = con.prepareStatement(sql2);
                    actStat.setLong(1, id);
                    actStat.setInt(2, type);
                    actStat.executeUpdate();
                    
                    actStat = null;
                    actStat = con.prepareStatement(sql3);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    
                    actStat = null;
                    actStat = con.prepareStatement(sql4);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    
                    actStat = null;
                    actStat = con.prepareStatement(sql5);
                    actStat.setLong(1, id);
                    actStat.setInt(2, type);
                    actStat.executeUpdate();
                    //删除已经配置过的资源组
                    

                    actStat = null;
                    actStat = con.prepareStatement(sql7);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    
                    actStat = null;
                    actStat = con.prepareStatement(sql6);
                    actStat.setLong(1, id);
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e) {
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally {
                    DBResource.closePSConn(con, actStat, "delResourceGroup", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:delResourceGroup");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource.throwRepositoryException(i,
                        ServerError.ERR_DB_UPDATE);
            }
        }
    }

    /**
     * <li>Description:通过ID获取工作流</li>
     * 
     * <AUTHOR> 2014-08-20
     * @param 
     * @return
     * @throws RepositoryException
     *             return List
     */
    public ResourceWorkflowDTO getFlowById(Long workflowId) throws RepositoryException {
        String sql = " SELECT IID, PROJECTNAME, FLOWNAME FROM IEAI_MAINFLOW_DEF where IID=? ";
        ResourceWorkflowDTO resourceWorkflow = null;
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getFlowById", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, workflowId);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceWorkflow = new ResourceWorkflowDTO();
                        resourceWorkflow.setId(actRS.getLong("IID"));
                        resourceWorkflow.setProjectName(actRS.getString("PROJECTNAME"));
                        resourceWorkflow.setFlowName(actRS.getString("FLOWNAME"));
                    }
                } catch (SQLException e) {
                    _log.error("getFlowById is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getFlowById",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getFlowById");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resourceWorkflow;
    }
    /**
     * <li>Description:获取资源组绑定的工作流</li>
     * 
     * <AUTHOR> 2014-08-20
     * @param resourceWorkflow
     * @param flag true 为获得已绑定的资源组 false为获取未绑定的资源组
     * @return
     * @throws RepositoryException
     *             return List
     */
    public List<ResourceGroupDTO> getResourceGroupsByFlow(ResourceWorkflowDTO resourceWorkflow, int type, Boolean flag) throws RepositoryException {
        List<ResourceGroupDTO> list = new ArrayList<ResourceGroupDTO>();
        String sql;
        if(flag){
            sql = "select IID,IRESNAME,IRESDES,ITYPE from IEAI_RESOURCE_GROUP where ITYPE=? and IID in (select IRESOURCEID from IEAI_MAINFLOW_RESOURCE where IPROJECTNAME=? and IFLOWNAME=?)";
        } else {
            sql = "select IID,IRESNAME,IRESDES,ITYPE from IEAI_RESOURCE_GROUP where ITYPE=? and IID not in (select IRESOURCEID from IEAI_MAINFLOW_RESOURCE where IPROJECTNAME=? and IFLOWNAME=?)";
        }
        ResourceGroupDTO resourceGroup = null;
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getResourceGroupsByFlow", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setInt(1, type);
                    actStat.setString(2, resourceWorkflow.getProjectName());
                    actStat.setString(3, resourceWorkflow.getFlowName());
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceGroup = new ResourceGroupDTO();
                        resourceGroup.setId(actRS.getLong("IID"));
                        resourceGroup.setName(actRS.getString("IRESNAME"));
                        resourceGroup.setDescription(actRS.getString("IRESDES"));
                        list.add(resourceGroup);
                    }
                } catch (SQLException e) {
                    _log.error("getResourceGroupsByFlow is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getResourceGroupsByFlow",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResourceGroupsByFlow");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * <li>Description:获取与工程绑定的资源组</li>
     * 
     * <AUTHOR> 2014-08-20
     * @param resourceWorkflow
     * @param flag true 为获得已绑定的资源组 false为获取未绑定的资源组
     * @return
     * @throws RepositoryException
     *             return List
     */
    public List<ResourceGroupDTO> getResourceGroupsByProjectName(String projectName, int type, Boolean flag) throws RepositoryException {
        List<ResourceGroupDTO> list = new ArrayList<ResourceGroupDTO>();
        String sql;
        if(flag){
            sql = "select IID,IRESNAME,IRESDES,ITYPE from IEAI_RESOURCE_GROUP where ITYPE=? and IID in (select IRESOURCEID from IEAI_MAINFLOW_RESOURCE where IPROJECTNAME=?) order by IRESNAME";
        } else {
            sql = "select IID,IRESNAME,IRESDES,ITYPE from IEAI_RESOURCE_GROUP where ITYPE=? and IID not in (select IRESOURCEID from IEAI_MAINFLOW_RESOURCE where IPROJECTNAME=?) order by IRESNAME";
        }
        ResourceGroupDTO resourceGroup = null;
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getResourceGroupsByProjectName", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setInt(1, type);
                    actStat.setString(2, projectName);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceGroup = new ResourceGroupDTO();
                        resourceGroup.setId(actRS.getLong("IID"));
                        resourceGroup.setName(actRS.getString("IRESNAME"));
                        resourceGroup.setDescription(actRS.getString("IRESDES"));
                        list.add(resourceGroup);
                    }
                } catch (SQLException e) {
                    _log.error("getResourceGroupsByFlow is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getResourceGroupsByProjectName",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResourceGroupsByProjectName");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * <li>根据"工程名称" 和"当前所选行的资源组"和"环境id"，查询 该资源组的备选项列表</li>
     * 
     * <AUTHOR> 
     * @date   2015-04-13
     * @param resourceWorkflow
     * @return
     * @throws RepositoryException
     *             return List
     */
    public List<ResourceGroupBean> getResItems (String proName,String resid,String envid) throws RepositoryException {
        List<ResourceGroupBean> list = new ArrayList<ResourceGroupBean>();
        Map<String,String> idNameMap = new LinkedHashMap<String, String>() ;            // resID 和resNAME之间建立映射关系
        Map<String, Set<String>> iresGropSetMap = new LinkedHashMap<String, Set<String>>();// 以“工程名_工作流名_活动名”为键，为资源组分组
        
        Set<String> iresSet  =null;
        Set<String> iresTmpSet  =new HashSet<String>();
        String resGropKey="";
        String resGropKeyTmp="";
        
        
        StringBuffer buff = new StringBuffer();
        buff.append(" SELECT  RESP.IPRONAME  ,RESP.IFLOWNAME ,RESP.IACTNAME ,RESP.IRESID ,R.IRESNAME");
        buff.append("  FROM IEAI_RESGROUP_PREPARE RESP ,IEAI_RESOURCE_GROUP  R ");
        buff.append("  WHERE RESP.IRESID =R.IID AND RESP.IPRONAME='");
        buff.append(proName);
        buff.append("'"); 
        buff.append("   AND RESP.IENVID = '");
        buff.append(envid);
        buff.append("'"); 
        buff.append(" ORDER BY RESP.IPRONAME,RESP.IFLOWNAME ,RESP.IACTNAME ,IRESID"); 

        
        
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                     con = DBResource.getConnection("getResItems", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(buff.toString());
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        
                        // 以“工程名_工作流名_活动名”为key，为资源组id分组.相同键值的 资源组id 放在同一个Set 集合中
                        resGropKeyTmp =actRS.getString("IPRONAME")+actRS.getString("IFLOWNAME")+actRS.getString("IACTNAME");
                        if (!resGropKey.equals(resGropKeyTmp))
                        { 
                            resGropKey =resGropKeyTmp;
                            iresSet = new HashSet<String>();  
                        }else {
                            iresSet = iresGropSetMap.get(resGropKey);
                        }
                        iresSet.add(actRS.getString("IRESID"));
                        iresGropSetMap.put(resGropKey,iresSet);
                        
                        
                        //把每个 "资源组id"  与 "资源组名称"之间的 映射关系保留起来
                        idNameMap.put(actRS.getString("IRESID"), actRS.getString("IRESNAME"));
                    }
                 
                       //遍历 idNameMap .如果key对应的set中值包含 "resid"。 则把set中的项作为资源组的备选项
                    for (Iterator<String> iterator = iresGropSetMap.keySet().iterator(); iterator.hasNext();)
                    {
                        String key = iterator.next();
                         
                        //如果set中包含resid,则该set中的值，可以作为备选项
                        Set tmpSet = iresGropSetMap.get(key);
                        for (Iterator<String> iterator2 = tmpSet.iterator(); iterator2.hasNext();)
                        {
                           String tmpResid= iterator2.next();
                           if (resid.equals(tmpResid))  
                          {
                               iresTmpSet.addAll(tmpSet);
                           }
                        }

                    }
                   
                    iresTmpSet.remove(resid);
                    String iresidItem =null;
                    for (Iterator<String> iterator = iresTmpSet.iterator(); iterator.hasNext();)
                    {
                        iresidItem = iterator.next();
                        list.add(new ResourceGroupBean(iresidItem,idNameMap.get(iresidItem))); 
                    }
                    Collections.sort(list);
                    
                } catch (SQLException e) {
                    _log.error("getResItems is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getResItems",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResItems");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * 更具实例名iid查询 工程名
     * <AUTHOR>
     * 2015-09-19 
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public String getPrjNames ( String tempteIds)throws RepositoryException
    {
        StringBuffer queryDefEnvSql = new StringBuffer(
                "SELECT T.IMAIN_PROJECT_NAME,IID,IONLINE_INS_NAME FROM IEAI_SUS_ONLINE_READY_BASEINFO T  ");
        queryDefEnvSql.append(" WHERE T.IID = "+ tempteIds+"");
        String sql = "";
        String prjName = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement envStat = null;
                Connection con = null;
                ResultSet rs =null;
                try
                {
                    con = DBResource.getConnection("getPrjNames", _log1,Constants.IEAI_SUS);
                    sql = queryDefEnvSql.toString();
                    envStat = con.prepareStatement(sql);
                    rs =envStat.executeQuery();
                    while (rs.next())
                    {
                        prjName =rs.getString("IMAIN_PROJECT_NAME");
                    }

                } catch (SQLException e)
                {
                    _log1.error("getPrjNames is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, null, envStat, "getPrjNames", _log1);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getPrjNames");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return prjName;
    }
    
    /**
     * 根据实例iid来查询实例信息
     * <AUTHOR>
     * 2015-09-19 
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public OnlineInst  getInstInfoById ( String tempteIds)throws RepositoryException
    {
        StringBuffer queryDefEnvSql = new StringBuffer(
                "SELECT O.IID , O.IMAIN_PROJECT_NAME,O.IMAIN_FLOW_NAME,O.IONLINE_INS_NAME FROM IEAI_SUS_ONLINE_READY_BASEINFO O ");
        queryDefEnvSql.append(" WHERE O.IID = "+ tempteIds+"");
        List<OnlineInst> list = new ArrayList<OnlineInst>();
        String sql = "";
        String prjName = null;
        OnlineInst onlineInst = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement envStat = null;
                Connection con = null;
                ResultSet rs =null;
                try
                {
                    con = DBResource.getConnection("getInstInfoById", _log1,Constants.IEAI_SUS);
                    sql = queryDefEnvSql.toString();
                    envStat = con.prepareStatement(sql);
                    rs =envStat.executeQuery();
                    while (rs.next())
                    {
                        onlineInst = new OnlineInst(rs.getString("IID"), rs.getString("IMAIN_PROJECT_NAME"),
                                rs.getString("IMAIN_FLOW_NAME"), rs.getString("IONLINE_INS_NAME"));
                    }

                } catch (SQLException e)
                {
                    _log1.error("getInstInfoById is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, null, envStat, "getInstInfoById", _log1);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getInstInfoById");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return onlineInst;
    }
    
    /**
      * <li>Description:获得当前的应该 处于选中状态的资源组</li> 
      * <AUTHOR>
      * 2015-3-19 
      * @param prjName
      * @param flowName
      * @param actNmame
      * @param resIDPlus          页面新勾选，但是还未保存的 resourceGropID
      * @param resIDSubstract     页面新取消勾选，但是还未保存的 resourceGropID
      * @return
      * return boolean
      */
    public List<ResPreprdCheckedBean> getCurResChecked ( String prjName, String flowName, String actNmame,
            String resIDPlus, String resIDSubstract, Map<String, Object> resp, String envid ) throws RepositoryException
     {
        Map<String, String> rsMap = new LinkedHashMap<String, String>();
        StringBuffer buf = new StringBuffer();
        buf.append(
            "SELECT P.IPRONAME,P.IFLOWNAME,P.IACTNAME,P.IRESID,P.ICHECKED ,P.IENVID FROM IEAI_RESGROUP_PREPARE P");
        buf.append(" WHERE ");// checked
        buf.append("     P.IPRONAME='").append(prjName).append("'");
        buf.append(" AND P.IFLOWNAME='").append(flowName).append("'");
        buf.append(" AND P.IENVID='").append(envid).append("'");        // 查该环境下，的预配置信息
        if ("".equals(actNmame))
         {
            buf.append(" AND P.IACTNAME IS NULL");
        } else
        {
            buf.append(" AND P.IACTNAME='").append(actNmame).append("'");
        }
        /*
         * if (null!=resIDPlus && resIDPlus.length()>0)
         * buf.append(" AND P.ICHECKED IN('").append(resIDPlus).append("')");
         * 
         * if (null!=resIDSubstract && resIDSubstract.length()>0)
         * buf.append(" AND P.ICHECKED NOT IN('").append(resIDSubstract).append("')");
         */

        List<ResPreprdCheckedBean> list = new ArrayList<ResPreprdCheckedBean>();
        String resid = null;

        for (int i = 0;; i++)
        {
                try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                ResultSet rs = null;
                ResPreprdCheckedBean bean = null;

                try
                {
                    con = DBResource.getConnection("getCurResChecked", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(buf.toString());
                    rs = actStat.executeQuery();
                    while (rs.next())
                    {
                        resid = rs.getString("IRESID");
                        rsMap.put(resid, resid);
                    }

                    gainFinalRescIDs(resGropSplit(resIDPlus), rsMap, true);
                    gainFinalRescIDs(resGropSplit(resIDSubstract), rsMap, false);
                    creatObjs(rsMap, list);

                } catch (SQLException e)
                {
                    _log.error("getCurResChecked is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                } finally
                {
                    DBResource.closePSConn(con, actStat, "getCurResChecked", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getCurResChecked");
                    }
                }
                break;
            } catch (RepositoryException ex)
                {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
             }
        }
        return list;

    }
     /**
      * <li>Description:</li> 
      * <AUTHOR>
      * 2015-3-20 
      * @param src
      * @return
      * return String[]
      */
     private String[] resGropSplit (String src) 
     {
         String ids[]={};
         if(null!=src)
         {            
             ids =src.split(",");
             if (ids!=null & ids.length==1 &&"".equals(ids[0]))
             {
                 ids=null;
             }
         }    
         return ids;
     }
     /**
      * <li>Description:获得当前的应该 流程上线发起时的资源组ID </li> 
      * <AUTHOR>
      * 2015-3-19 
      * @param prjName
      * @param flowName
      * @param actNmame
      * @param resIDPlus          页面新勾选，但是还未保存的 resourceGropID
      * @param resIDSubstract     页面新取消勾选，但是还未保存的 resourceGropID
      * @return
      * return boolean
      */
     public void getCurOnlineRes ( String prjName, String flowName, String actNmame, String resIDPlus,
         String resIDSubstract ,Map<String, Object> resp ,String envid) throws RepositoryException
     {
         Map<String, String> rsMap =new LinkedHashMap<String, String>();
         StringBuffer buf = new StringBuffer();
         buf.append("SELECT R.IRESID FROM IEAI_SUS_ONLINE_RESGROUP R");
         buf.append(" WHERE ");//checked
         buf.append("     R.IPRONAME='").append(prjName).append("'");
         buf.append(" AND R.IFLOWNAME='").append(flowName).append("'");
         buf.append(" AND R.IENVID='").append(envid).append("'");        //查该环境下，的预配置信息
         if ("".equals(actNmame))  
         {
             buf.append(" AND R.IACTNAME IS NULL");
         }else {
             buf.append(" AND R.IACTNAME='").append(actNmame).append("'");
         }
         String onlineResid ="-1";
        for (int i = 0;; i++)
         {
             try
             {
                 PreparedStatement actStat = null;
                 Connection con = null;
                 ResultSet rs =null;
                 ResPreprdCheckedBean bean =null;

                 try
                 {
                    con = DBResource.getConnection("getCurOnlineRes", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(buf.toString());
                     rs= actStat.executeQuery();
                     while (rs.next())
                     {
                         onlineResid =rs.getString("IRESID");
                     }                 
                     resp.put("onlineResid", onlineResid);
                 } catch (SQLException e)
                 {
                     _log.error("getCurOnlineRes is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_UPDATE);
                 } finally
                 {
                     DBResource.closePSConn(con, actStat, "getCurOnlineRes", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getCurOnlineRes");
                     }
                 }
                 break;
             } catch (RepositoryException ex)
             {
                 DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
             }
         }

     }

     private void creatObjs(Map<String, String> rsMap,List<ResPreprdCheckedBean> list)
     {
         ResPreprdCheckedBean  bean =null;
         for (Iterator iterator = rsMap.keySet().iterator(); iterator.hasNext();)
         {
            String key =(String) iterator.next();
            bean = new ResPreprdCheckedBean();
            bean.setResid(key);
            bean.setChecked(true); 
            list.add(bean);     
         }
     }
     
     /**
      * <li>Description:</li> 
      * <AUTHOR>
      * @date  2015-3-20 
      * @param src
      * @param rsMap
      * @param opt
      * return void
      */
     private void gainFinalRescIDs(String[] src ,Map<String, String> rsMap ,boolean opt ){
         if (null!=src)
         {
             for (int i = 0; i < src.length; i++)
             {
                 if (opt)
                 {
                     rsMap.put(src[i], src[i]); 
                 }else {
                     rsMap.remove(src[i]); 
                 }
                
             }
         }
         
     }
     
     /**
      * <li>Description:查询服务器的个性化参数</li> 
      * <AUTHOR>
      * 2015年1月15日 
      * @param serverId
      * @return
      * @throws RepositoryException
      * return List
      */
     public List getServerExtendParametersForBind (long serverId) throws RepositoryException
     {
         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
         String sql = "SELECT " + "SP.ISERVERID ," + "P.INAME  , " + "SP.IPARAMETERID ,"
                 + "SP.IVALUE ," + "P.IRESGROUPID " + " FROM "
                 + "IEAI_RESGROUP_EXTENDS_VALUE SP  ," + "IEAI_RESGROUP_EXTENDSPARAMETER P  "
                 + "WHERE " + " SP.ISERVERID=?  AND "
                 + " P.IID =SP.IPARAMETERID ORDER BY  SP.IPARAMETERID DESC  ";
         ResourceGroupParameterDTO resgp = null;
        for (int i = 0;; i++)
         {
             try
             {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try
                 {
                    con = DBResource.getConnection("getServerExtendParametersForBind", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, serverId);
                     actRS = actStat.executeQuery();
                     while (actRS.next())
                     {
                         resgp = new ResourceGroupParameterDTO();
                         resgp.setServerId(actRS.getLong("ISERVERID"));  //服务器_ID
                         resgp.setName(actRS.getString("INAME"));        //参数的_名称
                         resgp.setVid(actRS.getLong("IPARAMETERID"));    //参数的_ID
                         resgp.setValue(actRS.getString("IVALUE"));      //参数的_值
                         resgp.setResId(actRS.getString("IRESGROUPID")); //资源的_ID
                         list.add(resgp);
                     }
                 } catch (SQLException e)
                 {
                     _log.error("getServerExtendParametersForBind is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally
                 {
                     DBResource.closeConn(con, actRS, actStat, "getServerExtendParametersForBind",
                         _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServerExtendParametersForBind");
                     }
                 }
                 break;
             } catch (RepositoryException ex)
             {
                 DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     /**
      * 得到资源组扩展参数信息 <li>Description:</li>
      * 该方法为重写方法，为sus服务
      * <AUTHOR> 2014-8-18
      * @param type
      * @param resourceGroupId
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceGroupParameterDTO> getResourceGroupExtendParameter(int type, long resourceGroupId)
             throws RepositoryException {
         List list = new ArrayList();
         String sql = "SELECT IID,IRESGROUPID,INAME,IPARAMTYPE,IVALUE,IPRAM_CONER FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE ITYPE=? AND IRESGROUPID=?  ORDER BY INAME";
         ResourceGroupParameterDTO groupParameter = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getResourceGroupExtendParameter", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actStat.setLong(2, resourceGroupId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         groupParameter = new ResourceGroupParameterDTO();
                         groupParameter.setId(actRS.getLong("IID"));                   //参数的ID
                         groupParameter.setName(actRS.getString("INAME"));             //参数的名称
                         groupParameter.setParamType(actRS.getString("IPARAMTYPE"));   //参数的类型
                         groupParameter.setTypeprimal(actRS.getString("IPARAMTYPE"));  
                         groupParameter.setGroupId(actRS.getLong("IRESGROUPID"));      //参数的类型
                         groupParameter.setValue(null == actRS
                                 .getString("IVALUE") ? "" : actRS
                                 .getString("IVALUE"));
                         groupParameter.setIpram_coner(actRS.getString("IPRAM_CONER"));
                         list.add(groupParameter);
                     }
                 } catch (SQLException e) {
                     _log.error("getResourceGroupExtendParameter is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getResourceGroupExtendParameter", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getResourceGroupExtendParameter");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     /**
      * 存储资源组全局参数对应组内服务器设置的参数值 <li>Description:</li>
      * For SUS
      * <AUTHOR> 2014-8-19
      * @param resourceGroupId
      * @param serverInfo
      * @param type
      * @throws RepositoryException
      *             return void
      */
     public void saveExtendParameteValueNew(ResourceGroupParameterDTO resourceGroupParam, int type) throws RepositoryException {
         String sqlu = " UPDATE  IEAI_RESGROUP_EXTENDS_VALUE SET IVALUE=? where iid=?";
         String sqli = "INSERT INTO IEAI_RESGROUP_EXTENDS_VALUE(IID,ISERVERID,IPARAMETERID,IVALUE) VALUES (?,?,?,?)";
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 Connection con = null;
                 Connection con4UpdateBlob = null;
                 con = DBResource.getConnection("saveExtendParameteValueNew", _log,Constants.IEAI_SUS);
                 con4UpdateBlob = DBResource.getConnection("saveExtendParameteValueNew:第二个连接", _log,Constants.IEAI_SUS);

                 
                 long vid = resourceGroupParam.getVid();
                 Map<String,String> ipPortMap= new LinkedHashMap<String, String>();
                 if (!"".equals(vid) && vid != -1 && vid != 0) {
                     try {
                         actStat = con.prepareStatement(sqlu);
                         actStat.setString(1, resourceGroupParam.getValue());
                         actStat.setLong(2, vid);
                         actStat.executeUpdate();
                         con.commit();
                         
                         
                         //参数值 落地后，修改blob数据,并且用第二个数据库连接
                         boolean sus_reschange_effect_switch=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RESCHANGE_EFFECT_SWITCH, true);
                         if(sus_reschange_effect_switch){
                             //参数值 落地后，修改blob数据,并且用第二个数据库连接
                             ipPortMap=getIPPort_extendValChaned(vid);
                             BlobUpdateService.updateBlobs_with_PersonParamChanged(ipPortMap.get("agentIP"),
                                 Long.valueOf(ipPortMap.get("agentPORT")), con4UpdateBlob);
                             con4UpdateBlob.commit();
                         }
                     } catch (SQLException e) {
                         _log.error("saveExtendParameteValueNew is error ! IID=("
                                 + resourceGroupParam.getVid() + ") IVALUE=("
                                 + resourceGroupParam.getValue() + "错误信息：("+e.getMessage()+")");
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                     } finally {
                         DBResource.closePSConn(con, actStat,
                                 "saveExtendParameteValueNew1", _log);
                        DBResource.closeConnection(con4UpdateBlob,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                         if (SystemConfig.isElapsedTime())
                         {
                             _log.info("Jdbc Connection closeConnection and method:saveExtendParameteValueNew1");
                         }
                     }
                 } else {
                     try {
                         long iid = IdGenerator.createId(
                                 "IEAI_RESGROUP_EXTENDS_VALUE", con);
                         actStat = con.prepareStatement(sqli);
                         actStat.setLong(1, iid);
                         actStat.setLong(2, resourceGroupParam.getServerId());
                         actStat.setLong(3, resourceGroupParam.getId());
                         actStat.setString(4, resourceGroupParam.getValue());
                         actStat.executeUpdate();
                         con.commit();
                         
                         
                         //参数值 落地后，修改blob数据,并且用第二个数据库连接
                         boolean sus_reschange_effect_switch=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RESCHANGE_EFFECT_SWITCH, true);
                         if(sus_reschange_effect_switch){
                             //参数值 落地后，修改blob数据,并且用第二个数据库连接
                             ipPortMap=getServerId(resourceGroupParam.getServerId());
                             BlobUpdateService.updateBlobs_with_PersonParamChanged(ipPortMap.get("agentIP"),
                                 Long.valueOf(ipPortMap.get("agentPORT")), con4UpdateBlob);
                             con4UpdateBlob.commit();
                         }
                     } catch (SQLException e) {
                         _log.error("saveExtendParameteValueNew is error ! IID=("
                                 + resourceGroupParam.getVid() + ") IVALUE=("
                                 + resourceGroupParam.getValue() + "ISERVERID=("
                                 + resourceGroupParam.getServerId() + ") IPARAMETERID=("
                                 + resourceGroupParam.getId() + ") IVALUE=("
                                 + resourceGroupParam.getValue() + ")" + "错误信息：("+e.getMessage()+")");
                        DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                     } finally {
                         DBResource.closePSConn(con, actStat,
                                 "saveExtendParameteValueNew2", _log);
                        DBResource.closeConnection(con4UpdateBlob,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                         if (SystemConfig.isElapsedTime())
                         {
                             _log.info("Jdbc Connection closeConnection and method:saveExtendParameteValueNew2");
                         }
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource.throwRepositoryException(i,
                         ServerError.ERR_DB_UPDATE);
             }
         }
     }
     
     /**
     * <li>Description:当附加参数值改变时，反查该附加参数对应的ip和端口</li> 
     * <AUTHOR>
     * 2017-2-18 
     * @param extendsValsIID
     * @return
     * @throws RepositoryException
     * return Map<String,String>
     */
    public Map<String,String> getIPPort_extendValChaned(long extendsValsIID)
             throws RepositoryException {
        Map<String, String> map =new LinkedHashMap<String, String>();
        StringBuffer buf=new StringBuffer();
        buf.append("");
        buf.append("        select r.iid resid,r.iresname, ");
        buf.append("        e.iid paramid,e.iname,e.ipram_coner,");
        buf.append("        v.iid vid,v.iserverid,v.ivalue,");
        buf.append("        s.iip,s.iport");
        
        buf.append("   from ieai_resource_group r,");
        buf.append("        ieai_resgroup_extendsparameter e ,");
        buf.append("        IEAI_RESGROUP_EXTENDS_VALUE v,");
        buf.append("        ieai_resource_business s");
        buf.append("  where ");
        buf.append(         "r.iid=e.iresgroupid ");
        buf.append("       and e.iid=v.iparameterid and v.iserverid=s.iid"); 
        buf.append("        and v.iid=?");
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getIPPort_extendValChaned", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(buf.toString());
                     actStat.setLong(1, extendsValsIID);
                     actRS = actStat.executeQuery();
                     map.put("agentIP", "");
                     map.put("agentPORT", "-1");
                     while (actRS.next()) {
                         map.put("agentIP", actRS.getString("iip"));
                         map.put("agentPORT", actRS.getString("iport"));
                     }
                 } catch (SQLException e) {
                     _log.error("getIPPort_extendValChaned is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getIPPort_extendValChaned", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getIPPort_extendValChaned");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return map;
     }
    /**
    * <li>Description:当附加参数值改变时，反查该附加参数对应的ip和端口</li> 
    * <AUTHOR>
    * 2017-2-18 
    * @param extendsValsIID
    * @return
    * @throws RepositoryException
    * return Map<String,String>
    */
   public Map<String,String> getServerId(long agentServerid)
            throws RepositoryException {
       Map<String, String> map =new LinkedHashMap<String, String>();
       StringBuffer buf=new StringBuffer();
       buf.append(" SELECT S.IIP,S.IPORT ");
       buf.append("   FROM IEAI_RESOURCE_BUSINESS S");
       buf.append("  WHERE S.IID=?");
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getSchedulerConnection("getServerId", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(buf.toString());
                    actStat.setLong(1, agentServerid);
                    actRS = actStat.executeQuery();
                    map.put("agentIP", "");
                    map.put("agentPORT", "-1");
                    while (actRS.next()) {
                        map.put("agentIP", actRS.getString("iip"));
                        map.put("agentPORT", actRS.getString("iport"));
                    }
                } catch (SQLException e) {
                    _log.error("getServerId is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat,
                            "getServerId", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getServerId");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }
     
     /**
      * <li>Description: </li> 
      * <AUTHOR>
      * 2014-8-23 
      * @param serverIds
      * @param groupId
      * @param type
      * @throws RepositoryException
      * return void
      */
     public void saveGroupBusinessNew(Long[] serverIds, long groupId, int type) throws RepositoryException {
         String sqlDel = " DELETE FROM IEAI_GROUP_BUSINESS WHERE IGROUPID=?  AND ITYPE=?";

         String sql = " INSERT INTO IEAI_GROUP_BUSINESS(IID ,IGROUPID ,IBUSINESSID,ITYPE) VALUES(?,?,?,?)";
         
         String sqlCase = " DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE IPARAMETERID IN (SELECT IID FROM IEAI_RESGROUP_EXTENDSPARAMETER WHERE IRESGROUPID=? AND ITYPE=?)";
         if(serverIds.length>0){
             String temp = " and ISERVERID not in (" + StringUtils.join(serverIds, ",") + ")";
             sqlCase += temp;
         }
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("saveGroupBusinessNew2", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sqlDel);
                    actStat.setLong(1, groupId);
                    actStat.setInt(2, type);
                    actStat.executeUpdate();
                    for (Long serverId : serverIds)
                    {
                        actStat = null;
                        actStat = con.prepareStatement(sql);
                        actStat.setLong(1, IdGenerator.createId("IEAI_GROUP_BUSINESS", con));
                        actStat.setLong(2, groupId);
                        actStat.setLong(3, serverId);
                        actStat.setInt(4, type);
                        actStat.executeUpdate();
                    }

                    actStat = null;
                    actStat = con.prepareStatement(sqlCase);
                    actStat.setLong(1, groupId);
                    actStat.setInt(2, type);
                    actStat.executeUpdate();
                    con.commit();
                } catch (SQLException e)
                {
                    _log.error("saveGroupBusinessNew is error ! " + e.getMessage());
                    // 失败后数据回滚
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closePSConn(con, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(),
                        _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:saveGroupBusinessNew2");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
     }
     
      /**
      * <li>Description: 新增活解除 服务器与资源组的绑定关系,如果是服务器与资源组解除绑定关系时，则同时把参数也删除掉 override</li> 
      * <AUTHOR>
      * 2015-4-28 
      * @param serverIds
      * @param groupId
      * @param type
      * @throws RepositoryException
      * return void
      */
    public void saveGroupBusinessNew ( String[] serverIds, long groupId, int type ) throws RepositoryException
    {
         String sql = " INSERT INTO IEAI_GROUP_BUSINESS(IID ,IGROUPID ,IBUSINESSID,ITYPE) VALUES(?,?,?,?)";
         for (int i = 0;; i++)
         {
             try
             {
                 PreparedStatement remvRelatnStat = null;
                 PreparedStatement remvParamStat = null;
                 PreparedStatement plusRelatnStat = null;
                 Connection con = null;
                 try
                 {
                    con = DBResource.getConnection("saveGroupBusinessNew", _log,Constants.IEAI_SUS);
                    // for(int j=0;j<serverIds.length;j++){
                    String sqlRemvReltn = " DELETE FROM IEAI_GROUP_BUSINESS WHERE IGROUPID=? AND IBUSINESSID IN("
                            + serverIds[1] + ") AND ITYPE=?";
                    StringBuffer buf = new StringBuffer();
                    if (JudgeDB.IEAI_DB_TYPE == 3)
                    {
                        buf.append(" DELETE IEAI_RESGROUP_EXTENDS_VALUE FROM IEAI_RESGROUP_EXTENDS_VALUE ,(");
                        buf.append(
                            " SELECT  V.IID AS IID FROM IEAI_RESGROUP_EXTENDSPARAMETER E ,IEAI_RESGROUP_EXTENDS_VALUE  V ");
                        buf.append(" WHERE E.IID =V.IPARAMETERID ");
                        buf.append("   AND  E.IRESGROUPID =?");
                        buf.append("   AND  V.ISERVERID IN (" + serverIds[1] + ")");
                        buf.append("   AND  E.ITYPE =? )");
                        buf.append(" B WHERE IEAI_RESGROUP_EXTENDS_VALUE.IID=B.IID");
                    } else
                    {
                        buf.append(" DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE IID IN(");
                        buf.append(" SELECT  V.IID ");
                        buf.append(" FROM IEAI_RESGROUP_EXTENDSPARAMETER E ,IEAI_RESGROUP_EXTENDS_VALUE V");
                        buf.append(" WHERE E.IID =V.IPARAMETERID");
                        buf.append("   AND  E.IRESGROUPID =?");
                        buf.append("   AND  V.ISERVERID IN (" + serverIds[1] + ")");
                        buf.append("   AND  E.ITYPE =? )");
                    }

                    if (!"".equals(serverIds[1]))
                    { // 解除关系
                        remvRelatnStat = con.prepareStatement(sqlRemvReltn);
                        remvRelatnStat.setLong(1, groupId);
                        remvRelatnStat.setInt(2, type);
                        remvRelatnStat.executeUpdate();

                        // 清理曾经 存在关系时 的一些 参数值
                        remvParamStat = con.prepareStatement(buf.toString());
                        remvParamStat.setLong(1, groupId);
                        remvParamStat.setInt(2, type);
                        remvParamStat.executeUpdate();
                    }
                    // }
                    // 新增资源组服务器关系
                    String[] plusReltnSIDS = serverIds[0].split(",");
                    plusRelatnStat = con.prepareStatement(sql);
                    for (String serverId : plusReltnSIDS)
                     {
                        if ("".equals(serverId.trim()))
                        {
                            continue;
                        }
                        plusRelatnStat.setLong(1, IdGenerator.createId("IEAI_GROUP_BUSINESS", con));
                        plusRelatnStat.setLong(2, groupId);
                        plusRelatnStat.setString(3, serverId);
                        plusRelatnStat.setInt(4, type);
                        plusRelatnStat.addBatch();
                     }
                    plusRelatnStat.executeBatch();

                     con.commit();
                 } catch (SQLException e)
                 {
                    _log.error("saveGroupBusinessNew is error ! " + e.getMessage() + e.getStackTrace());
                     // 失败后数据回滚
                     DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                         Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                 } finally
                 {
                    DBResource.closePreparedStatement(remvRelatnStat, "saveGroupBusinessNew", _log);
                    DBResource.closePreparedStatement(remvParamStat, "saveGroupBusinessNew", _log);
                    DBResource.closePSConn(con, plusRelatnStat, "saveGroupBusinessNew", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                        _log.info("Jdbc Connection closeConnection and method:saveGroupBusinessNew");
                     }
                 }
                 break;
             } catch (RepositoryException ex)
             {
                 DBRetryUtil.waitForNextTry(i, ex);
             }
         }
     }
     
     /**
      * <li>Description:导入服务器时，存储服务器信息</li> 
      * <AUTHOR>
      * 2014-12-3 
      * @param resourceServer
      * @param type
      * @throws RepositoryException
      * return void
      */
     public void saveServerInfoForImport(ResourceServerDTO resourceServer, int type) throws RepositoryException {
         String sql = "MERGE INTO IEAI_RESOURCE_BUSINESS  A "
                 + "USING (SELECT ? AS IIP ,? AS ISYSTEMTYPE, ? AS TYPE FROM IDUAL )  B "
                 + "ON (A.IIP=B.IIP AND A.ISYSTEMTYPE=B.ISYSTEMTYPE AND A.ITYPE = B.TYPE) "
                 + "WHEN MATCHED THEN UPDATE SET IHOSTNAME=? ,IPRIORITY=? ,IPORT=?"
                 + "WHEN NOT MATCHED THEN INSERT (IID ,IHOSTNAME ,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE) VALUES(?,?,?,?,?,?,?)";

         if (!resourceServer.getIp().equals("")
                 && !(resourceServer.getSystemType() == "")) {
            for (int i = 0;; i++)
            {
                 try {
                     PreparedStatement actStat = null;
                     Connection con = null;
                     try {
                         con = DBResource.getConnection("saveServerInfoForImport",
                                 _log,Constants.IEAI_SUS);
                        actStat = con.prepareStatement(sql);
                        actStat.setString(1, resourceServer.getIp());
                        actStat.setString(2, resourceServer.getSystemType());
                        actStat.setInt(3, type);
                        actStat.setString(4, resourceServer.getHostName());
                        actStat.setInt(5, resourceServer.getPriority());
                        actStat.setInt(6, resourceServer.getPort());
                        actStat.setLong(7, IdGenerator.createId("IEAI_RESOURCE_BUSINESS", con));
                        actStat.setString(8, resourceServer.getHostName());
                        actStat.setString(9, resourceServer.getIp());
                        actStat.setInt(10, resourceServer.getPort());
                        actStat.setInt(11, resourceServer.getPriority());
                        actStat.setInt(12, type);
                        actStat.setString(13, resourceServer.getSystemType());
                        actStat.executeUpdate();
                         con.commit();
                     } catch (SQLException e) {
                         _log.error("saveServerInfoForImport is error ! "
                                 + e.getMessage());
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                     } finally {
                         DBResource.closePSConn(con, actStat,
                                 "saveServerInfoForImport", _log);
                         if (SystemConfig.isElapsedTime())
                         {
                             _log.info("Jdbc Connection closeConnection and method:saveServerInfoForImport");
                         }
                     }
                     break;
                 } catch (RepositoryException ex) {
                     DBResource.throwRepositoryException(i,
                             ServerError.ERR_DB_UPDATE);
                 }
             }
         }
     }
     /**
      * <li>Description:查询服务器信息</li>
      * 为sus服务
      * <AUTHOR> 2014-08-21
      * @param type
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceServerDTO> getServersByIds(Long[] serverIds, int type) throws RepositoryException {
         List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
         String sql = "SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? and IID in ("+ StringUtils.join(serverIds, ",") +") ORDER BY IID DESC";
         ResourceServerDTO resourceServer = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getServersByIds", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceServer = new ResourceServerDTO();
                         resourceServer.setId(actRS.getLong("IID"));
                         resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                         resourceServer.setIp(actRS.getString("IIP"));
                         resourceServer.setPort(actRS.getInt("IPORT"));
                         resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                         resourceServer.setType(actRS.getInt("ITYPE"));
                         resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                         list.add(resourceServer);
                     }
                 } catch (SQLException e) {
                     _log.error("getServersByIds is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getServersByIds",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServersByIds");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     /**
      * <li>Description:查询服务器信息</li>
      * 为sus服务
      * <AUTHOR> 2014-08-18
      * @param type
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceServerDTO> getServersByType(int type,String curServer,String ip) throws RepositoryException {
         List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
         String proxyIpColumn = "";
         if (DBManager.Orcl_Faimily())
         {
             proxyIpColumn = ",R.IPROXYIP";  
         }
         String pjsql="";
         if(null!=ip&&ip!="") {
             pjsql="AND (R.IID like '%"+ip+"%' or R.IHOSTNAME like '%"+ip+"%' or R.IIP like '%"+ip+"%' or R.IPORT like '%"+ip+"%' or R.IPRIORITY like '%"+ip+"%' or R.ITYPE like '%"+ip+"%' or R.ISYSTEMTYPE like '%"+ip+"%' or R.IMODEL_TYPE like '%"+ip+"%' or R.ICOM_NAME like '%"+ip+"%' or A.ICOM_NAME like '%"+ip+"%' or A.IAGENT_AZNAME like '%"+ip+"%' or A.IAGENT_NETID like '%"+ip+"%') GROUP BY\r\n" + 
                     "    R.IID,\r\n" + 
                     "    R.IHOSTNAME,\r\n" + 
                     "    R.IIP,\r\n" + 
                     "    R.IPORT,\r\n" + 
                     "    R.IPRIORITY,\r\n" + 
                     "    R.ITYPE,\r\n" + 
                     "    R.ISYSTEMTYPE,\r\n" + 
                     "    R.IMODEL_TYPE,\r\n" + 
                     "    R.ICOM_NAME,\r\n" + 
                     "    A.ICOM_NAME,\r\n" + 
                     "    A.IAGENT_AZNAME,\r\n" + 
                     "    A.IAGENT_NETID,\r\n" + 
                     "    R.IPROXYIP ";
         }  
         String sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE ,R.ICOM_NAME ,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A WHERE R.ITYPE=? AND A.IAGENT_IP=R.IIP AND A.IAGENT_PORT = R.IPORT "+pjsql+"ORDER BY R.IHOSTNAME";
         if (null!=curServer && !"".equals(curServer) && !"null".equals(curServer)) { 
             sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE ,R.ICOM_NAME ,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A WHERE R.ITYPE=? AND A.IAGENT_IP=R.IIP AND A.IAGENT_PORT = R.IPORT AND R.IIP='"+curServer+pjsql+"' ORDER BY R.IHOSTNAME";
         }
         if(null!=comName && !"".equals(comName) && !"null".equals(comName)){
             sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE,R.ICOM_NAME,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A  WHERE ITYPE=?  AND A.IAGENT_IP = R.IIP AND A.IAGENT_PORT = R.IPORT AND A.ICOM_NAME LIKE '"+comName+pjsql+"' ORDER BY IHOSTNAME";
         }
         if(null!=curServer && !"".equals(curServer) && !"null".equals(curServer)
                 && null!=comName && !"".equals(comName) && !"null".equals(comName) ){
             sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE,R.ICOM_NAME,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A  WHERE ITYPE=? AND IIP='"+curServer+"' AND A.IAGENT_IP = R.IIP AND A.IAGENT_PORT = R.IPORT AND A.ICOM_NAME LIKE '"+comName+pjsql+"' ORDER BY IHOSTNAME";
         }
         ResourceServerDTO resourceServer = null;
         for (int i = 0;; i++) {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getServersByType", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceServer = new ResourceServerDTO();
                         resourceServer.setId(actRS.getLong("IID"));
                         resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                         resourceServer.setIp(actRS.getString("IIP"));
                         resourceServer.setPort(actRS.getInt("IPORT"));
                         resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                         resourceServer.setType(actRS.getInt("ITYPE"));
                         resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                         resourceServer.setImodeltype(actRS.getString("IMODEL_TYPE"));
                         resourceServer.setIagentcomputername(actRS.getString("ICOM_NAME"));
                         resourceServer.setComName(actRS.getString("ACOMNAME"));
                         resourceServer.setIazName(actRS.getString("IAGENT_AZNAME"));
                         resourceServer.setIazNetId(actRS.getString("IAGENT_NETID"));
                         if (DBManager.Orcl_Faimily())
                         {
                             resourceServer.setIproxyIp(actRS.getString("IPROXYIP"));
                         }
                         list.add(resourceServer);
                     }
                 } catch (SQLException e) {
                     _log.error("getServersByType is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getServersByType",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServersByType");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBRetryUtil.waitForNextTry(i, ex);
//                 DBResource
//                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }

    public List<ResourceServerDTO> getFjnxServersByType(int type,String curServer,String ip,String modulType,String busSysName,Long ibusEnvId) throws RepositoryException {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        String proxyIpColumn = "";
        if (DBManager.Orcl_Faimily())
        {
            proxyIpColumn = ",R.IPROXYIP";
        }
        String pjsql="";
        if(null!=ip&&ip!="") {
            pjsql="AND (R.IID like '%"+ip+"%' or R.IHOSTNAME like '%"+ip+"%' or R.IIP like '%"+ip+"%' or R.IPORT like '%"+ip+"%' or R.IPRIORITY like '%"+ip+"%' or R.ITYPE like '%"+ip+"%' or R.ISYSTEMTYPE like '%"+ip+"%' or R.IMODEL_TYPE like '%"+ip+"%' or R.ICOM_NAME like '%"+ip+"%' or A.ICOM_NAME like '%"+ip+"%' or A.IAGENT_AZNAME like '%"+ip+"%' or A.IAGENT_NETID like '%"+ip+"%' or A.IAGENT_DES like '%"+ip+"%' or A.IBUSINESSSYS like '%"+ip+"%') GROUP BY\r\n" +
                    "    R.IID,\r\n" +
                    "    R.IHOSTNAME,\r\n" +
                    "    R.IIP,\r\n" +
                    "    R.IPORT,\r\n" +
                    "    R.IPRIORITY,\r\n" +
                    "    R.ITYPE,\r\n" +
                    "    R.ISYSTEMTYPE,\r\n" +
                    "    R.IMODEL_TYPE,\r\n" +
                    "    R.ICOM_NAME,\r\n" +
                    "    A.IAGENT_DES,\r\n" +
                    "    A.ICOM_NAME,\r\n" +
                    "    A.IAGENT_AZNAME,\r\n" +
                    "    A.IAGENT_NETID,\r\n" +
                    "    A.IBUSINESSSYS,\r\n" +
                    "    R.IPROXYIP ";
        }
        String sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE ,R.ICOM_NAME ,A.IAGENT_DES,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID,A.IBUSINESSSYS"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A,IEAI_SYS_RELATION S, IEAI_AGENT_ENV_RELATION G WHERE R.ITYPE=? AND A.IAGENT_IP=R.IIP AND A.IAGENT_PORT = R.IPORT AND A.IAGENTINFO_ID = G.IAGENTID AND S.COMPUTERID = A.IAGENTINFO_ID  AND R.IMODEL_TYPE LIKE '%' || REPLACE(?::text, ','::text, '%') || '%' AND G.IENVID = ? AND S.PRJTYPE = 3 "+pjsql+"ORDER BY R.IHOSTNAME";
        if (null!=curServer && !"".equals(curServer) && !"null".equals(curServer)) {
            sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE ,R.ICOM_NAME ,A.IAGENT_DES,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID,A.IBUSINESSSYS"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A,IEAI_SYS_RELATION S, IEAI_AGENT_ENV_RELATION G WHERE R.ITYPE=? AND A.IAGENT_IP=R.IIP AND A.IAGENT_PORT = R.IPORT AND A.IAGENTINFO_ID = G.IAGENTID AND S.COMPUTERID = A.IAGENTINFO_ID  AND R.IMODEL_TYPE LIKE '%' || REPLACE(?::text, ','::text, '%') || '%' AND G.IENVID = ? AND S.PRJTYPE = 3  AND R.IIP='"+curServer+pjsql+"' ORDER BY R.IHOSTNAME";
        }
        if(null!=comName && !"".equals(comName) && !"null".equals(comName)){
            sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE,R.ICOM_NAME,A.IAGENT_DES,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID,A.IBUSINESSSYS"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A,IEAI_SYS_RELATION S, IEAI_AGENT_ENV_RELATION G  WHERE ITYPE=?  AND A.IAGENT_IP = R.IIP AND A.IAGENT_PORT = R.IPORT AND A.IAGENTINFO_ID = G.IAGENTID AND S.COMPUTERID = A.IAGENTINFO_ID  AND R.IMODEL_TYPE LIKE '%' || REPLACE(?::text, ','::text, '%') || '%' AND G.IENVID = ? AND S.PRJTYPE = 3  AND A.ICOM_NAME LIKE '"+comName+pjsql+"' ORDER BY IHOSTNAME";
        }
        if(null!=curServer && !"".equals(curServer) && !"null".equals(curServer)
                && null!=comName && !"".equals(comName) && !"null".equals(comName) ){
            sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE,R.ICOM_NAME,A.IAGENT_DES,A.ICOM_NAME AS ACOMNAME,A.IAGENT_AZNAME,A.IAGENT_NETID,A.IBUSINESSSYS"+proxyIpColumn+" FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A,IEAI_SYS_RELATION S, IEAI_AGENT_ENV_RELATION G  WHERE ITYPE=? AND IIP='"+curServer+"' AND A.IAGENT_IP = R.IIP AND A.IAGENT_PORT = R.IPORT AND A.IAGENTINFO_ID = G.IAGENTID AND S.COMPUTERID = A.IAGENTINFO_ID  AND R.IMODEL_TYPE LIKE '%' || REPLACE(?::text, ','::text, '%') || '%' AND G.IENVID = ? AND S.PRJTYPE = 3  AND A.ICOM_NAME LIKE '"+comName+pjsql+"' ORDER BY IHOSTNAME";
        }
        ResourceServerDTO resourceServer = null;
        for (int i = 0;; i++) {
            int index = 0;
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    //modulType ="%" + modulType + "%";
                    con = DBResource.getConnection("getServersByType", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(++index, type);
                    actStat.setString(++index, modulType);
                    actStat.setLong(++index, ibusEnvId);
                    //actStat.setString(++index, busSysName);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceServer = new ResourceServerDTO();
                        resourceServer.setId(actRS.getLong("IID"));
                        resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                        resourceServer.setIp(actRS.getString("IIP"));
                        resourceServer.setPort(actRS.getInt("IPORT"));
                        resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                        resourceServer.setType(actRS.getInt("ITYPE"));
                        resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
//                        String moType = actRS.getString("IMODEL_TYPE");
//                        if(!isContainsModelType(moType,modulType)){
//                            continue;
//                        }
                        resourceServer.setImodeltype(actRS.getString("IMODEL_TYPE"));
                        resourceServer.setIagentcomputername(actRS.getString("ICOM_NAME"));
                        resourceServer.setComName(actRS.getString("ACOMNAME"));
                        resourceServer.setIazName(actRS.getString("IAGENT_AZNAME"));
                        resourceServer.setIazNetId(actRS.getString("IAGENT_NETID"));
                        resourceServer.setUse(actRS.getString("IAGENT_DES"));
                        resourceServer.setSystemName(actRS.getString("IBUSINESSSYS"));
                        if (DBManager.Orcl_Faimily())
                        {
                            resourceServer.setIproxyIp(actRS.getString("IPROXYIP"));
                        }
                        list.add(resourceServer);
                    }
                } catch (SQLException e) {
                    _log.error("getFjnxServersByType is error ! " + e.getMessage(),e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getServersByType",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getServersByType");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBRetryUtil.waitForNextTry(i, ex);
//                 DBResource
//                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
     
     /**
      * <li>Description:查询全部IP</li>
      * 为sus服务
      * <AUTHOR> 2015-07-08
      * @param type
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceServerDTO> getAllIP(Long ibusSysId) throws RepositoryException {
         List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
         String sql = "SELECT distinct(IIP) FROM IEAI_RESOURCE_BUSINESS ORDER BY IIP ";
         ResourceServerDTO resourceServer = null;
         String sysBindingComputerFlag = Environment.getInstance().getSysConfig(
             Environment.SYS_BINDING_COMPUTER_FLAG);
         // 判断业务系统与设备绑定的开关
         if (Boolean.parseBoolean(sysBindingComputerFlag) && null != ibusSysId && 0 !=ibusSysId )
         {
             sql = "SELECT DISTINCT(N.NAME2) AS IIP FROM (SELECT T2.IAGENT_IP AS NAME2, T2.IAGENT_PORT AS IID2,T2.ICOM_NAME AS NAME3\n"
                     + "  FROM IEAI_SYS_RELATION T, IEAI_COMPUTER_LIST T1, IEAI_AGENTINFO T2\n"
                     + " WHERE T.COMPUTERID = T1.CPID\n" + "   AND T1.IAGENTINFO_ID = T2.IAGENTINFO_ID\n"
                     + "   AND T.SYSTEMID = ? ) N";
         }
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getAllIP", _log,Constants.IEAI_SUS);
                     Set<ResourceServerDTO> set = getSpecialIp(con);
                     actStat = con.prepareStatement(sql);
                     if (Boolean.parseBoolean(sysBindingComputerFlag) && null != ibusSysId && 0 !=ibusSysId ){
                         actStat.setLong(1, ibusSysId);
                     }
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceServer = new ResourceServerDTO();
                         resourceServer.setIp(actRS.getString("IIP"));
                         list.add(resourceServer);
                     }
                     
                     set.addAll(list);
                     list.clear();
                     list.addAll(set);
                 } catch (SQLException e) {
                     _log.error("getServersByType is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getAllIP",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getAllIP");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }

    /**
     * 福建农信-查询全部IP
     * @param modulType
     * @param ibusEnvId
     * @param ibusSysName
     * @return
     * @throws RepositoryException
     */
    public List<ResourceServerDTO> getFjnxAllIP(String modulType, Long ibusEnvId, String ibusSysName) throws RepositoryException {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        int type = Constants.RESOURCE_SUS;
        String sql = "SELECT R.IIP,R.IMODEL_TYPE FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A,IEAI_SYS_RELATION S, IEAI_AGENT_ENV_RELATION G WHERE R.ITYPE=? AND A.IAGENT_IP=R.IIP AND A.IAGENT_PORT = R.IPORT AND A.IAGENTINFO_ID = G.IAGENTID AND S.COMPUTERID = A.IAGENTINFO_ID  AND R.IMODEL_TYPE LIKE '%' || REPLACE(?::text, ','::text, '%') || '%' AND G.IENVID = ? AND S.prjtype = 3 ORDER BY R.IIP ";
        ResourceServerDTO resourceServer = null;
    /*    String sysBindingComputerFlag = Environment.getInstance().getSysConfig(
                Environment.SYS_BINDING_COMPUTER_FLAG);
        // 判断业务系统与设备绑定的开关
        if (Boolean.parseBoolean(sysBindingComputerFlag) && null != ibusSysId && 0 !=ibusSysId )
        {
            sql = "SELECT DISTINCT(N.NAME2) AS IIP FROM (SELECT T2.IAGENT_IP AS NAME2, T2.IAGENT_PORT AS IID2,T2.ICOM_NAME AS NAME3\n"
                    + "  FROM IEAI_SYS_RELATION T, IEAI_COMPUTER_LIST T1, IEAI_AGENTINFO T2\n"
                    + " WHERE T.COMPUTERID = T1.CPID\n" + "   AND T1.IAGENTINFO_ID = T2.IAGENTINFO_ID\n"
                    + "   AND T.SYSTEMID = ? ) N";
        }*/
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getAllIP", _log,Constants.IEAI_SUS);
                    Set<ResourceServerDTO> set = getSpecialIp(con);
                    actStat = con.prepareStatement(sql);
                   /* if (Boolean.parseBoolean(sysBindingComputerFlag) && null != ibusSysId && 0 !=ibusSysId ){
                        actStat.setLong(1, ibusSysId);
                    }*/
                    actStat.setInt(1, type);
                    actStat.setString(2, modulType);
                    actStat.setLong(3, ibusEnvId);
                    //actStat.setString(3, ibusSysName);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        //String moType = actRS.getString("IMODEL_TYPE");
                        resourceServer = new ResourceServerDTO();
                        resourceServer.setIp(actRS.getString("IIP"));
//                        if(!isContainsModelType(moType,modulType)){
//                            continue;
//                        }
                        list.add(resourceServer);
                    }

                    set.addAll(list);
                    list.clear();
                    list.addAll(set);
                } catch (SQLException e) {
                    _log.error("getServersByType is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getAllIP",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getAllIP");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
     
     /**
     * <li>Description:获得全部资源组和服务器，按照资源组名称进行分组</li> 
     * <AUTHOR>
     * 2017-2-14 
     * @return
     * @throws RepositoryException
     * return List<ResourceServerDTO>
     */
    public static Map<String, List<ResourceServerDTO>> getAllGroupServersCEB ()
            throws RepositoryException
    {
        Map<String, List<ResourceServerDTO>> resultMap = new LinkedHashMap<String, List<ResourceServerDTO>>();
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append("         SELECT ");
        buf.append("         BB.IRESNAME,");
        buf.append("         BB.IRESID,");
        buf.append("         AA.IMODEL_TYPE,");
        buf.append("         AA.IID IID,");
        buf.append("         IHOSTNAME,");
        buf.append("         IIP,");
        buf.append("         IPORT,");
        buf.append("         IPRIORITY,");
        buf.append("         ITYPE,");
        buf.append("         ISYSTEMTYPE,");
        buf.append("         BB.IID ISIID");
        buf.append("    FROM (SELECT IID,");
        buf.append("                 IHOSTNAME,");
        buf.append("                 IIP,");
        buf.append("                 IPORT,");
        buf.append("                 IPRIORITY,");
        buf.append("                 ITYPE,");
        buf.append("                 ISYSTEMTYPE,");
        buf.append("                 IMODEL_TYPE");
        buf.append("            FROM IEAI_RESOURCE_BUSINESS");
        buf.append("           ORDER BY IIP) AA");
        buf.append("    JOIN (SELECT B.IID,");
        buf.append("                 C.IID IRESID,");
        buf.append("                 C.IRESNAME IRESNAME");
        buf.append("            FROM IEAI_GROUP_BUSINESS    A,");
        buf.append("                 IEAI_RESOURCE_BUSINESS B,");
        buf.append("                IEAI_RESOURCE_GROUP    C");
        buf.append("           WHERE A.IGROUPID = C.IID");
        buf.append("            AND A.IBUSINESSID = B.IID");
        buf.append("             ) BB ON AA.IID = BB.IID    ");
        buf.append("ORDER BY BB.IRESNAME    ");

        ResourceServerDTO resourceServer = null;
        String curResName = "";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getAllGroupServersCEB", _log,
                        Constants.IEAI_SUS);
                    actStat = con.prepareStatement(buf.toString());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        curResName = actRS.getString("IRESNAME");
                        if (null == resultMap.get(curResName))
                        {
                            list = new ArrayList<ResourceServerDTO>();
                            resultMap.put(curResName, list);
                        }
                        resourceServer = new ResourceServerDTO();
                        resourceServer.setIresid(actRS.getString("IRESID"));
                        resourceServer.setIresname(actRS.getString("IRESNAME"));
                        resourceServer.setImodeltype(actRS.getString("IMODEL_TYPE"));
                        resourceServer.setId(actRS.getLong("IID"));
                        resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                        resourceServer.setIp(actRS.getString("IIP"));
                        resourceServer.setPort(actRS.getInt("IPORT"));
                        resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                        resourceServer.setType(actRS.getInt("ITYPE"));
                        resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        list.add(resourceServer);
                    }
                } catch (SQLException e)
                {
                    _log.error("getAllGroupServersCEB is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getServersByGroupId", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getAllGroupServersCEB");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resultMap;
    }
     
     /**
      * <li>Description:查询服务器信息</li>
      * 
      * <AUTHOR> 2014-08-18
      * @param type, groupId
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceServerDTO> getServersByGroupId(int type, long groupId)
             throws RepositoryException {
         List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
         String sql = "SELECT AA.IMODEL_TYPE,AA.IID IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE, BB.IID ISIID FROM (SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMODEL_TYPE  FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IIP ) AA JOIN (SELECT B.IID FROM IEAI_GROUP_BUSINESS A,IEAI_RESOURCE_BUSINESS B,IEAI_RESOURCE_GROUP C WHERE A.IGROUPID=C.IID AND A.IBUSINESSID=B.IID AND C.IID=? ) BB ON AA.IID=BB.IID";
         ResourceServerDTO resourceServer = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getServersByGroupId", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actStat.setLong(2, groupId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceServer = new ResourceServerDTO();
                         resourceServer.setId(actRS.getLong("IID"));
                         resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                         resourceServer.setIp(actRS.getString("IIP"));
                         resourceServer.setPort(actRS.getInt("IPORT"));
                         resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                         resourceServer.setType(actRS.getInt("ITYPE"));
//                       resourceServer.setIsIid(actRS.getLong("ISIID"));
                         resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
//                       resourceServer.setMdtype(actRS.getString("IMDTYPE"));
                         list.add(resourceServer);
                     }
                 } catch (SQLException e) {
                     _log.error("getServersByGroupId is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getServersByGroupId",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServersByGroupId");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     /**
      * <li>Description:服务器信息 的更新  或者   标记一个服务器 与资源组 绑定关系 都是在本方法中来完成的</li>
      * 
      * <AUTHOR> 2014-08-18
      * @param resourceServer
      * @param type
      * @throws RepositoryException
      * @return 如果返回结果是以 ：（1）返回值："_-"为开头的话，则是需要进行于资源组进行   解绑操作的 ；
      *                     （2） 返回值："_+"为开头的话，则是需要进行于资源组进行    绑定操作的
      *                     （3） 返回值：null ，既不需要绑定也不需要解绑 
      */
     public String saveServerByType(ResourceServerDTO resourceServer, int type,String currentGroupId) throws RepositoryException {
         long tid = resourceServer.getId();
         String strTid =null;
         if (!"".equals(tid) && tid != -1 && tid != 0 && !"".equals(resourceServer.getIp().trim())) {
             String sql = " update  IEAI_RESOURCE_BUSINESS set IHOSTNAME=? ,IIP=?,IPORT=?,IPRIORITY=?,ITYPE=?,ISYSTEMTYPE=?,IMODEL_TYPE=?  where iid=?";
             String existSql ="SELECT COUNT(IID) CNT FROM IEAI_RESOURCE_BUSINESS WHERE IHOSTNAME =? AND IIP =? AND IPORT=? AND IPRIORITY =? AND ITYPE =? AND ISYSTEMTYPE =? AND IMODEL_TYPE=?"; 
            for (int i = 0;; i++)
            {
                 try {
                     PreparedStatement actStat = null;
                     Connection con = null;
                     ResultSet existRS =null;
                     boolean isOnlyUpdateOpt=false;
                     ResourceServerDTO server =getOldHostInfo(resourceServer.getId());
                     
                     con = DBResource.getConnection("update_AgentIP_Port", _log,type);
                     boolean sus_reschange_effect_switch=ServerEnv.getInstance().getBooleanConfig(Environment.SUS_RESCHANGE_EFFECT_SWITCH, true);
                     if(sus_reschange_effect_switch){
                         BlobUpdateService.updateBlobs(server.getIp(), server.getPort(),resourceServer.getIp(),
                             String.valueOf(resourceServer.getPort()), con);
                     }
                     FlowstartinstManagerCEB.update_AgentIP_Port(server.getIp(),
                        String.valueOf(server.getPort()), resourceServer.getIp(),
                        String.valueOf(resourceServer.getPort()), con);
                     try {
                        con = DBResource.getSchedulerConnection("saveServerByType", _log, Constants.IEAI_SUS);
                         //更新之前，判断是否是真的更新 的话，那么本次操作仅仅是 更新操作，而并非是绑定服务器操作
                         actStat = con.prepareStatement(existSql);
                         actStat.setString(1, resourceServer.getHostName());
                         actStat.setString(2, resourceServer.getIp());
                         actStat.setInt(3, resourceServer.getPort());
                         actStat.setInt(4, resourceServer.getPriority());
                         actStat.setInt(5, type);
                         actStat.setString(6, resourceServer.getSystemType());
                         actStat.setString(7, resourceServer.getImodeltype());
                         existRS =actStat.executeQuery();
                         int cnt =-1;
                         while (existRS.next())
                         {
                             cnt= existRS.getInt("CNT");
                             if (0 ==cnt)//数据库中不存在着与更新后 相同的记录，所以判断出是 真的要进行更新操作
                             {
                                 isOnlyUpdateOpt = true;
                                 actStat = null;
                                 actStat = con.prepareStatement(sql);
                                 actStat.setString(1, resourceServer.getHostName());
                                 actStat.setString(2, resourceServer.getIp());
                                 actStat.setInt(3, resourceServer.getPort());
                                 actStat.setInt(4, resourceServer.getPriority());
                                 actStat.setInt(5, type);
                                 actStat.setString(6, resourceServer.getSystemType());
                                 actStat.setString(7, resourceServer.getImodeltype());
                                 actStat.setLong(8, tid);
                                 actStat.executeUpdate();
                                 con.commit();
                             }
                         }
                         
                         
                         //业务逻辑处理已经完成，开始根据操作的类型 来确定返回值
                         strTid =Long.toString(tid);
                         if (false ==resourceServer.getChecked())            //getChecked字段成为一个标示。 =false是从右面板提交来的数据，true是左面板提交来的数据
                         {
                             if(true == resourceServer.isIsleftScreen()){    //从左屏拖拽到右屏，解绑操作                         
                                 strTid+="_-";                                  
                             }else{
                                 strTid=null;                    
                             }
                         }else{
                             if(false == resourceServer.isIsleftScreen()){   //从右屏拖拽到左屏，绑定操作                        
                                 strTid+="_+";                                
                             }else{
                                 strTid=null;                    
                             }
                         }
                         
                         
                         if (null!=existRS)
                         {
                             existRS.close();
                             existRS=null;
                         }
                     } catch (SQLException e) {
                         _log.error("saveServerByType is error ! " + e.getMessage());
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                     } finally {
                         DBResource.closePSConn(con, actStat, "saveServerByType",
                                 _log);
                         if (SystemConfig.isElapsedTime())
                         {
                             _log.info("Jdbc Connection closeConnection and method:saveServerByType");
                         }
                     }
                     break;
                 } catch (RepositoryException ex) {
                     DBResource.throwRepositoryException(i,
                             ServerError.ERR_DB_UPDATE);
                 }
             }
         } else {
             String sql = " INSERT INTO IEAI_RESOURCE_BUSINESS(IID ,IHOSTNAME ,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODEL_TYPE) VALUES(?,?,?,?,?,?,?,?) ";

             if (!"".equals(resourceServer.getIp().trim()) && !(resourceServer.getPort() == 0)) {
                for (int i = 0;; i++)
                {
                     try {
                         PreparedStatement actStat = null;
                         Connection con = null;
                         try {
                             con = DBResource.getConnection("saveServerByType2",
                                     _log,Constants.IEAI_SUS);
                             actStat = con.prepareStatement(sql);
                             tid = IdGenerator.createId(
                                 "IEAI_RESOURCE_BUSINESS", con);
                             actStat.setLong(1, tid);
                             actStat.setString(2, resourceServer.getHostName());
                             actStat.setString(3, resourceServer.getIp());
                             actStat.setInt(4, resourceServer.getPort());
                             actStat.setInt(5, resourceServer.getPriority());
                             actStat.setInt(6, type);
                             actStat.setString(7, resourceServer.getSystemType());
                             actStat.setString(8, resourceServer.getImodeltype());
                             actStat.executeUpdate();
                             con.commit();
                             strTid =Long.toString(tid) +"_+";
                             if (false ==resourceServer.getChecked())
                             {
                                 strTid=null;                   //只是新增一个服务器，无需进行绑定操作
                             }else{
                                 strTid+="_+";                 //新绑定服务的返回值           
                             }
                         } catch (SQLException e) {
                             _log.error("saveServerByType is error ! "
                                     + e.getMessage());
                            DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                         } finally {
                             DBResource.closePSConn(con, actStat,
                                     "saveServerByType2", _log);
                             if (SystemConfig.isElapsedTime())
                             {
                                 _log.info("Jdbc Connection closeConnection and method:saveServerByType2");
                             }
                         }
                         break;
                     } catch (RepositoryException ex) {
                         DBResource.throwRepositoryException(i,
                                 ServerError.ERR_DB_UPDATE);
                     }
                 }
             }
         }
         
         //如果资源id为空时，只进行增加服务操作，而无需绑定操作
         if (currentGroupId==null)
         {
             strTid =null;
         }
         return strTid;
     }
     
     /**
      * <li>Description:获取服务器参数</li>
      * 
      * <AUTHOR>
      * @param 
      * @return
      * @throws RepositoryException
      *             return List
      */
     public ResourceServerDTO getOldHostInfo(long serverid) throws RepositoryException {
         ResourceServerDTO agentServer = new ResourceServerDTO();
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     String sql = "SELECT * from IEAI_RESOURCE_BUSINESS WHERE IID=?";
                     con = DBResource.getConnection("getOldHostInfo", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, serverid);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         agentServer.setId(actRS.getLong("IID"));
                         agentServer.setIp(actRS.getString("IIP"));
                         agentServer.setPort(actRS.getInt("IPORT"));
                     }
                 } catch (SQLException e) {
                     _log.error("getOldHostInfo is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getOldHostInfo",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getOldHostInfo");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return agentServer;
     }
     
     /**
      * <li>Description:获取指定工程的所有工作流</li>
      * 
      * <AUTHOR>
      * @param 
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceWorkflowDTO> getFlowsByProjectName(String projectName) throws RepositoryException {
         List<ResourceWorkflowDTO> list = new ArrayList<ResourceWorkflowDTO>();
         String sql = "SELECT IID, PROJECTNAME, FLOWNAME FROM IEAI_MAINFLOW_DEF where PROJECTNAME=? and FLOWTYPE=3 ORDER BY PROJECTNAME";
         ResourceWorkflowDTO resourceWorkflow = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getFlowsByProjectName", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setString(1, projectName);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceWorkflow = new ResourceWorkflowDTO();
                         resourceWorkflow.setId(actRS.getLong("IID"));
                         resourceWorkflow.setProjectName(actRS.getString("PROJECTNAME"));
                         resourceWorkflow.setFlowName(actRS.getString("FLOWNAME"));
                         list.add(resourceWorkflow);
                     }
                 } catch (SQLException e) {
                     _log.error("getFlowsByProjectName is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getFlowsByProjectName",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getFlowsByProjectName");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     /**
      * <li>Description:获取所有的工作流</li>
      * 
      * <AUTHOR> 2014-08-20
      * @param 
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<ResourceWorkflowDTO> getFlows() throws RepositoryException {
         List<ResourceWorkflowDTO> list = new ArrayList<ResourceWorkflowDTO>();
         String sql = "SELECT IID, PROJECTNAME, FLOWNAME FROM IEAI_MAINFLOW_DEF ORDER BY PROJECTNAME";
         ResourceWorkflowDTO resourceWorkflow = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getFlows", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceWorkflow = new ResourceWorkflowDTO();
                         resourceWorkflow.setId(actRS.getLong("IID"));
                         resourceWorkflow.setProjectName(actRS.getString("PROJECTNAME"));
                         resourceWorkflow.setFlowName(actRS.getString("FLOWNAME"));
                         list.add(resourceWorkflow);
                     }
                 } catch (SQLException e) {
                     _log.error("getFlows is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getFlows",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getFlows");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     /**
      * <li>Description:获取所有的工程</li>
      * 
      * <AUTHOR> 2014-09-10
      * @param 
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List<Map<String, String>> getProjects() throws RepositoryException {
         List<Map<String, String>> list = new ArrayList<Map<String, String>>();
         StringBuffer buf = new StringBuffer("");
         buf.append("SELECT * FROM ");
         buf.append("(SELECT P3.* FROM IEAI_PROJECT P3, ");
         buf.append("             (SELECT  P.INAME ,MAX(P.IUPLOADNUM) IUPLOADNUM FROM IEAI_PROJECT P  GROUP BY  INAME)P2 ");
         buf.append("WHERE P3.INAME =P2.INAME ");
         buf.append("     AND P3.IUPLOADNUM =P2.IUPLOADNUM AND P3.PROTYPE = 3.0 ORDER BY P3.INAME)P4 ");

        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getProjects", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(buf.toString());
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         Map<String, String> temp = new HashMap<String, String>();
                         temp.put("projectName", actRS.getString("INAME"));
                         temp.put("isysid", actRS.getString("iid"));
                         list.add(temp);
                     }
                 } catch (SQLException e) {
                     _log.error("getProjects is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getProjects",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getProjects");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     /**
      * 更新工作流与资源组的绑定关系 <li>Description:</li>
      * 
      * <AUTHOR> 2014-8-20
      * @param type
      * @return
      * @throws RepositoryException
      *             return List
      */
     public void updateWorkflowResourceGroupBinder(ResourceWorkflowDTO workflow, Long[] groupIds, int type) throws RepositoryException {
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("updateWorkflowResourceGroupBinder", _log,Constants.IEAI_SUS);
                     String sqlDelFlowResOther = 
                             "delete from IEAI_MAINFLOW_RESOURCE where IPROJECTNAME=? and IFLOWNAME=?";
                     actStat = con.prepareStatement(sqlDelFlowResOther);
                     actStat.setString(1, workflow.getProjectName());
                     actStat.setString(2, workflow.getFlowName());
                     actStat.executeUpdate();
                     if(groupIds.length>0){
                         String sqlDelExtParamValue = 
                                 " delete from IEAI_RESGROUP_EXTENDS_VALUE where IPARAMETERID in (select IID from IEAI_RESGROUP_EXTENDSPARAMETER where IRESGROUPID in ("+ StringUtils.join(groupIds, ",") +") and ITYPE=?) ";
                         String sqlDelExtParam = 
                                 " delete from IEAI_RESGROUP_EXTENDSPARAMETER where IRESGROUPID in ("+ StringUtils.join(groupIds, ",") +") and ITYPE=? ";
                         String sqlDelFlowRes = 
                                 " delete from IEAI_MAINFLOW_RESOURCE where IRESOURCEID in ("+ StringUtils.join(groupIds, ",") +") ";
                         actStat = null;
                         actStat = con.prepareStatement(sqlDelExtParamValue);
                         actStat.setInt(1, type);
                         actStat.executeUpdate();
                         actStat = null;
                         actStat = con.prepareStatement(sqlDelExtParam);
                         actStat.setInt(1, type);
                         actStat.executeUpdate();
                         actStat = null;
                         actStat = con.prepareStatement(sqlDelFlowRes);
                         actStat.executeUpdate();
                         
                         String sqlFlowPara = 
                                 " select INPUTPARAMNAME, INPUTPARAMTYPE from IEAI_MAINFLOW_PARAM where ISREADYONLY='false' and PROJECTFLWID=(select DISTINCT IID from IEAI_MAINFLOW_DEF where PROJECTNAME=? and FLOWNAME=?) ";
                         actStat = null;
                         actStat = con.prepareStatement(sqlFlowPara);
                         actStat.setString(1, workflow.getProjectName());
                         actStat.setString(2, workflow.getFlowName());
                         actRS = actStat.executeQuery();
                         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
                         List<String[]> params = new ArrayList<String[]>();
                         while (actRS.next()) {
                             String[] para = new String[2];
                             para[0] = actRS.getString("INPUTPARAMNAME");
                             para[1] = actRS.getString("INPUTPARAMTYPE");
                             params.add(para);
                         }
                         String sqli = 
                                 "INSERT INTO IEAI_RESGROUP_EXTENDSPARAMETER(IID,IRESGROUPID,INAME,IPARAMTYPE,IVALUE,ITYPE) VALUES (?,?,?,?,?,?)";
                         for(String[] param : params){
                             for(Long groupid : groupIds){
                                 long iid = IdGenerator.createId(
                                         "IEAI_RESGROUP_EXTENDSPARAMETER", con);
                                 actStat = null;
                                 actStat = con.prepareStatement(sqli);
                                 actStat.setLong(1, iid);
                                 actStat.setLong(2, groupid);
                                 actStat.setString(3, param[0]);
                                 actStat.setString(4, param[1]);
                                 actStat.setString(5,"");
                                 actStat.setInt(6, type);
                                 actStat.executeUpdate();
                             }
                         }
                         String sqlFlowResI = 
                                 "INSERT INTO IEAI_MAINFLOW_RESOURCE(IID,IPROJECTNAME,IFLOWNAME,IRESOURCEID) VALUES (?,?,?,?)";
                         for(Long groupid : groupIds){
                             long iid = IdGenerator.createId(
                                     "IEAI_MAINFLOW_RESOURCE", con);
                             actStat = null;
                             actStat = con.prepareStatement(sqlFlowResI);
                             actStat.setLong(1, iid);
                             actStat.setString(2, workflow.getProjectName());
                             actStat.setString(3, workflow.getFlowName());
                             actStat.setLong(4, groupid);
                             actStat.executeUpdate();
                         }
                     }
                     con.commit();
                     
                 } catch (SQLException e) {
                     _log.error("updateWorkflowResourceGroupBinder is error ! "
                             + e.getMessage());
                    DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "updateWorkflowResourceGroupBinder", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:updateWorkflowResourceGroupBinder");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                 .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
     }

     public Map<String, Object> projectBindGroupValidate(String projectName, Long[] groupIds, int type) throws RepositoryException {
         Map<String, Object> res = new HashMap<String, Object>();
         List<Long> canBindGroupIds = new ArrayList<Long>();
         List<Long> canNotBingGroudIds = new ArrayList<Long>();
         List<String> canNotBingGroudNames = new ArrayList<String>();
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("projectBindGroupValidate", _log,Constants.IEAI_SUS);
                     if(groupIds.length>0){
                             //检查project所有的工作流参数与将要绑定的资源组参数，是不是一致的。
                             String sqlQueryProjectParam = 
                                     "select distinct * from( select INPUTPARAMNAME,INPUTPARAMTYPE from IEAI_MAINFLOW_PARAM where isreadyonly='false' and projectflwid in (select iid from IEAI_MAINFLOW_DEF where PROJECTNAME=?))";
                             actStat = con.prepareStatement(sqlQueryProjectParam);
                             actStat.setString(1, projectName);
                             actRS = actStat.executeQuery();
                             List<String> params = new ArrayList<String>();
                             while (actRS.next()) {
                                 String para = "";
                                 para += actRS.getString("INPUTPARAMNAME") + "@@" + actRS.getString("INPUTPARAMTYPE");
                                 params.add(para);
                             }
                             //先过滤下groundIds，把之前绑定的全部过滤掉，只校验新增加的。
                        actStat.close();
                             actStat = con.prepareStatement(" select IRESOURCEID from IEAI_MAINFLOW_RESOURCE where IPROJECTNAME=? ");
                             actStat.setString(1, projectName);
                             actRS = actStat.executeQuery();
                             List<Long> tempIds = new ArrayList<Long>();
                             while (actRS.next()) {
                                 tempIds.add(actRS.getLong("IRESOURCEID"));
                             }
                             List<Long> filtedIds = new ArrayList<Long>();
                             for(Long groupId:groupIds){
                                 if(!tempIds.contains(groupId)){
                                     filtedIds.add(groupId);
                                 }
                             }
                             for(Long groupId:filtedIds){
                                 List<ResourceGroupParameterDTO> resGroupParams = getResourceGroupExtendParameter(type, groupId);
                                 List<String> resGroupParamsConcat = new ArrayList<String>();
                                 for(ResourceGroupParameterDTO resGroupParam : resGroupParams){
                                     resGroupParamsConcat.add(resGroupParam.getName() +"@@"+resGroupParam.getParamType());
                                 }
                                 boolean flag = true;
                                 for(String param : params){
                                     if(!resGroupParamsConcat.contains(param)){
                                         flag = false;
                                         break;
                                     }
                                 }
                                 if(flag){
                                     canBindGroupIds.add(groupId);
                                 }else{
                                     canNotBingGroudIds.add(groupId);
                                 }
                             }
                         
                        actStat.close();
                     }
                         if(!canNotBingGroudIds.isEmpty()){
                             String sqlFlowPara = 
                                     " select IRESNAME from ieai_resource_group where iid in ("+ StringUtils.join(canNotBingGroudIds, ",") +") ";
                             actStat = con.prepareStatement(sqlFlowPara);
                             actRS = actStat.executeQuery();
                             while (actRS.next()) {
                                 canNotBingGroudNames.add(actRS.getString("IRESNAME"));
                             }
                         }
                     
                     con.commit();
                     
                     
                 } catch (SQLException e) {
                     _log.error("projectBindGroupValidate is error ! "
                             + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "projectBindGroupValidate", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:projectBindGroupValidate");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                 .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }

             res.put("probIds", canNotBingGroudIds);
             res.put("groupNames", canNotBingGroudNames);

         return res;
     }
     
     public List<ResourceGroupOnlineDTO> getProjectWorkflowConfig (String prjName) throws RepositoryException 
     {
         List<ResourceGroupOnlineDTO> list = new ArrayList<ResourceGroupOnlineDTO>();
         String sql = " select IPRONAME, IFLOWNAME, IACTNAME, IRESNAME, ITYPE from IEAI_SUS_ONLINE_RESGROUP WHERE IPRONAME='"+prjName+"'";
         ResourceGroupOnlineDTO rgo = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getProjectWorkflowConfig", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         rgo = new ResourceGroupOnlineDTO();
                         rgo.setProjectName(actRS.getString("IPRONAME"));
                         rgo.setFlowName(actRS.getString("IFLOWNAME"));
                         rgo.setActName(actRS.getString("IACTNAME"));
                         rgo.setResgroupName(actRS.getString("IRESNAME"));
                         rgo.setType(actRS.getString("ITYPE"));
                         list.add(rgo);
                     }
                 } catch (SQLException e) {
                     _log.error("getProjectWorkflowConfig is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getProjectWorkflowConfig",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getProjectWorkflowConfig");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     public boolean groupExists(String groupName, int type) throws RepositoryException {
         String sql = " SELECT count(*) as COUNT FROM IEAI_RESOURCE_GROUP WHERE ITYPE=? and IRESNAME=? ";
         int count = 0;
         boolean res = false;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("groupExists", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actStat.setString(2, groupName);
                     actRS = actStat.executeQuery();

                     while (actRS.next()) {
                         count = Integer.parseInt(String.valueOf(actRS
                             .getString("COUNT")));
                     }
                     if(count>0){
                         res = true;
                     }
                 } catch (SQLException e) {
                     _log.error("groupExists is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "groupExists",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:groupExists");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return res;
     }
     
     public int serverIsBindGroup( Long [] businessid) throws RepositoryException {
         String sql = " SELECT count(*) as COUNT FROM IEAI_GROUP_BUSINESS WHERE IBUSINESSID  in (" + StringUtils.join(businessid, ",") + ")";
         int count = 0;
         int res = 0;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("serverIsBindGroup", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actRS = actStat.executeQuery();

                     while (actRS.next()) {
                         count = Integer.parseInt(String.valueOf(actRS
                             .getString("COUNT")));
                     }
                         res = count;
                 } catch (SQLException e) {
                     _log.error("serverIsBindGroup is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "serverIsBindGroup",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:serverIsBindGroup");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return res;
     }
     
     public int agentIsBindGroup( Long [] agentId) throws RepositoryException {
         //String sql = " SELECT count(*) as COUNT FROM IEAI_GROUP_BUSINESS WHERE IBUSINESSID  in (" + StringUtils.join(agentId, ",") + ")";
         List listIids = new ArrayList();
         String sqlWhere = "";
         if(agentId.length>0){
             for(int i =0;i<agentId.length;i++){
                 long tmpIds = agentId[i];
                 listIids.add(tmpIds);
             }
             sqlWhere = "Where "+DBUtil.getOracleSQLIn(listIids,listIids.size(),"a.IAGENTINFO_ID");
         }
         String sql = "select count(*) as COUNT from IEAI_GROUP_BUSINESS c where c.IBUSINESSID in (select b.IID from IEAI_RESOURCE_BUSINESS b where b.IIP in (select a.IAGENT_IP from IEAI_AGENTINFO a "+sqlWhere+")) ";
         int count = 0;
         int res = 0;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("agentIsBindGroup", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actRS = actStat.executeQuery();

                     while (actRS.next()) {
                         count = Integer.parseInt(String.valueOf(actRS
                             .getString("COUNT")));
                     }
                         res = count;
                 } catch (SQLException e) {
                     _log.error("serverIsBindGroup is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "agentIsBindGroup",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:agentIsBindGroup");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return res;
     }
     
     public void saveProjectWorkflowConfig (ResourceGroupOnlineDTO config) throws RepositoryException 
     {
         if("".equals(config.getActName())){
             String sql = "MERGE INTO IEAI_SUS_ONLINE_RESGROUP  A "
                     + "USING (SELECT ? AS IPRONAME ,? AS IFLOWNAME, ? as ITYPE FROM IDUAL )  B "
                     + "ON (A.IPRONAME=B.IPRONAME AND A.IFLOWNAME=B.IFLOWNAME AND A.IACTNAME is null AND A.ITYPE=B.ITYPE) "
                   + "WHEN MATCHED THEN UPDATE SET IRESNAME=?, IRESID=(select IID from ieai_resource_group where IRESNAME=?) "
                     + "WHEN NOT MATCHED THEN INSERT (RID, IID, IPRONAME, IFLOWNAME, IACTNAME, IRESNAME, IRESID, ITYPE) VALUES(?,0,?,?,?,?,(select IID from ieai_resource_group where IRESNAME=?),?)";
            for (int i = 0;; i++)
             {
                 try
                 {
                     PreparedStatement actStat = null;
                     Connection con = null;
                     try
                     {
                         con = DBResource.getConnection("saveProjectWorkflowConfig", _log,Constants.IEAI_SUS);
                         actStat = con.prepareStatement(sql);
                         actStat.setString(1, config.getProjectName());
                         actStat.setString(2, config.getFlowName());
                         actStat.setInt(3, Integer.parseInt(config.getType()));
                         actStat.setString(4, config.getResgroupName());
                         actStat.setString(5, config.getResgroupName());
                         actStat.setLong(6, IdGenerator.createId("IEAI_SUS_ONLINE_RESGROUP", con));
                         actStat.setString(7, config.getProjectName());
                         actStat.setString(8, config.getFlowName());
                         actStat.setObject(9, null);
                         actStat.setString(10, config.getResgroupName());
                         actStat.setString(11, config.getResgroupName());
                         actStat.setInt(12, Integer.parseInt(config.getType()));
                         actStat.executeUpdate();
                         con.commit();
                     } catch (SQLException e)
                     {
                         _log.error("saveProjectWorkflowConfig is error ! " + e.getMessage());
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                     } finally
                     {
                         DBResource.closePSConn(con, actStat, "saveProjectWorkflowConfig", _log);
                         if (SystemConfig.isElapsedTime())
                         {
                             _log.info("Jdbc Connection closeConnection and method:saveProjectWorkflowConfig");
                         }
                     }
                     break;
                 } catch (RepositoryException ex)
                 {
                     DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                 }
             }
         }else{
             String sql = "MERGE INTO IEAI_SUS_ONLINE_RESGROUP  A "
                     + "USING (SELECT ? AS IPRONAME ,? AS IFLOWNAME, ? AS IACTNAME, ? as ITYPE FROM IDUAL )  B "
                     + "ON (A.IPRONAME=B.IPRONAME AND A.IFLOWNAME=B.IFLOWNAME AND A.IACTNAME = B.IACTNAME AND A.ITYPE=B.ITYPE) "
                     + "WHEN MATCHED THEN UPDATE SET IRESNAME=?, IRESID=(select IID from ieai_resource_group where IRESNAME=?) "
                     + "WHEN NOT MATCHED THEN INSERT (RID, IID, IPRONAME, IFLOWNAME, IACTNAME, IRESNAME, IRESID, ITYPE) VALUES(?,0,?,?,?,?,(select IID from ieai_resource_group where IRESNAME=?),?)";
            for (int i = 0;; i++)
             {
                 try
                 {
                     PreparedStatement actStat = null;
                     Connection con = null;
                     try
                     {
                         con = DBResource.getConnection("saveProjectWorkflowConfig2", _log,Constants.IEAI_SUS);
                         actStat = con.prepareStatement(sql);
                         actStat.setString(1, config.getProjectName());
                         actStat.setString(2, config.getFlowName());
                         actStat.setString(3, config.getActName());
                         actStat.setInt(4, Integer.parseInt(config.getType()));
                         actStat.setString(5, config.getResgroupName());
                         actStat.setString(6, config.getResgroupName());
                         actStat.setLong(7, IdGenerator.createId("IEAI_SUS_ONLINE_RESGROUP", con));
                         actStat.setString(8, config.getProjectName());
                         actStat.setString(9, config.getFlowName());
                         actStat.setString(10, config.getActName());
                         actStat.setString(11, config.getResgroupName());
                         actStat.setString(12, config.getResgroupName());
                         actStat.setInt(13, Integer.parseInt(config.getType()));
                         actStat.executeUpdate();
                         con.commit();
                     } catch (SQLException e)
                     {
                         _log.error("saveProjectWorkflowConfig is error ! " + e.getMessage());
                        DBResource.rollback(con, ServerError.ERR_DB_UPDATE, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                     } finally
                     {
                         DBResource.closePSConn(con, actStat, "saveProjectWorkflowConfig2", _log);
                         if (SystemConfig.isElapsedTime())
                         {
                             _log.info("Jdbc Connection closeConnection and method:saveProjectWorkflowConfig2");
                         }
                     }
                     break;
                 } catch (RepositoryException ex)
                 {
                     DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                 }
             }
         }
         
         
     } 
     public Integer getProjectWorkflowsConfigType (String proName, String flowName,String envid) throws RepositoryException 
     {
         String sql = " select ITYPE from IEAI_RESGROUP_PREPARE where IPRONAME=? and IFLOWNAME=? AND IENVID= ?";
         Integer res = 0;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getProjectWorkflowsConfigType", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setString(1, proName);
                     actStat.setString(2, flowName);
                     actStat.setString(3, envid);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         res = actRS.getInt("ITYPE");
                         break;
                     }
                 } catch (SQLException e) {
                     _log.error("getProjectWorkflowsConfigType is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat, "getProjectWorkflowsConfigType",
                             _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getProjectWorkflowsConfigType");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return res;
     }
     /**
      * 服务器级别获取所有个性化参数值 <li>Description:</li>
      * for SUS
      * <AUTHOR> 2014-9-11
      * @param type
      * @param resourceGroupId
      * @param sId
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List getServerExtendParameters(int type, long sId) throws RepositoryException {
         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
/*         String sql = "SELECT " +
                         "DISTINCT RE.IID, "+
                         "RE.IRESGROUPID, "+
                         "RE.INAME, "+
                         "RE.IPARAMTYPE, "+
                         "RV.ISERVERID, "+
                         "RV.IVALUE, "+
                         "RV.IID AS VID, "+
                         "RE.IRESNAME,  "+
                         "RE.IVALUE AS ORIGVALUE "+
                      "FROM "+
                          "(SELECT "+
                              "RG.IRESNAME, "+
                              "R.IID, "+
                              "R.IRESGROUPID, "+
                              "R.INAME, "+
                              "R.IPARAMTYPE, "+
                              "R.ITYPE,  "+
                              "R.IVALUE "+
                            "FROM "+
                              "IEAI_RESOURCE_GROUP RG, "+
                              "IEAI_RESGROUP_EXTENDSPARAMETER R  "+
                            "WHERE "+
                              "RG.IID=R.IRESGROUPID and R.IRESGROUPID in (select igroupid from IEAI_GROUP_BUSINESS where itype=? and IBUSINESSID=?) "+
                           ") "+
                           "RE   "+
                               "left JOIN IEAI_RESGROUP_EXTENDS_VALUE RV  "+
                               "ON RE.IID = RV.IPARAMETERID AND "+
                               "RE.ITYPE=? AND "+
                               "RV.ISERVERID=?  "+
                               "AND RV.IID IN (SELECT  MAX(RV2.IID)  FROM IEAI_RESGROUP_EXTENDS_VALUE RV2  where RV2.ISERVERID=?  GROUP BY RV2.IPARAMETERID) "+
                           "ORDER BY "+
                               "IRESNAME, INAME";*/
         String sql ="SELECT DISTINCT RE.IID, "+
                                    " RE.IRESGROUPID, "+
                                    " RE.INAME, "+
                                    " RE.IPARAMTYPE, "+
                                    "  RV.ISERVERID, "+
                                    " RV.IVALUE, "+
                                    " RV.IID AS VID, "+
                                    " RE.IRESNAME, "+
                                    " RE.IVALUE AS ORIGVALUE "+
                                    " FROM (SELECT RG.IRESNAME, "+
                                    " R.IID, "+
                                    " R.IRESGROUPID, "+
                                    " R.INAME, "+
                                    " R.IPARAMTYPE, "+
                                    "  R.ITYPE, "+
                                    "  R.IVALUE "+
                                    "  FROM IEAI_RESOURCE_GROUP RG, IEAI_RESGROUP_EXTENDSPARAMETER R "+
                                    " WHERE RG.IID = R.IRESGROUPID "+
                                    " and R.IRESGROUPID in (select igroupid "+
                                    "                         from IEAI_GROUP_BUSINESS "+
                                    "                         where itype = ? "+
                                    "                      and IBUSINESSID = ?)) RE "+
                                    "    left JOIN (SELECT * "+
                                    "    FROM IEAI_RESGROUP_EXTENDS_VALUE "+
                                    "   WHERE IID IN (SELECT MAX(RV2.IID) "+
                                    "               FROM IEAI_RESGROUP_EXTENDS_VALUE RV2 "+
                                    "              where RV2.ISERVERID = ? "+
                                    "              GROUP BY RV2.IPARAMETERID)) RV ON RE.IID = "+
                                    "                                                RV.IPARAMETERID "+
                                    "                                            AND RE.ITYPE = ? "+
                                    "                                            AND RV.ISERVERID = ? "+
                    
                                    "  ORDER BY IRESNAME, INAME ";
         ResourceGroupParameterDTO resourceGroupParameter = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getServerExtendParameters2", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actStat.setLong(2, sId);
                     actStat.setLong(3, sId);
                     actStat.setLong(4, type);
                     actStat.setLong(5, sId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceGroupParameter = new ResourceGroupParameterDTO();
                         resourceGroupParameter.setId(actRS.getLong("IID"));
                         resourceGroupParameter.setName(actRS.getString("INAME"));
                         resourceGroupParameter.setParamType(actRS.getString("IPARAMTYPE"));
                         resourceGroupParameter.setGroupId(actRS.getLong("IRESGROUPID"));
                         resourceGroupParameter.setVid(actRS.getLong("VID"));
                         resourceGroupParameter.setResName(actRS.getString("IRESNAME"));
                         resourceGroupParameter.setValue(null == actRS
                                 .getString("IVALUE") ? "" : actRS
                                 .getString("IVALUE"));
                         resourceGroupParameter.setOriginalValue(null == actRS
                                 .getString("ORIGVALUE") ? "" : actRS
                                 .getString("ORIGVALUE"));
                         list.add(resourceGroupParameter);
                     }
                 } catch (SQLException e) {
                     _log.error("getServerExtendParameters2 is error ! "
                             + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getServerExtendParameters2", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServerExtendParameters2");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     /**
      * 服务器级别获取资源组属性参数值 <li>Description:</li>
      * for SUS
      * <AUTHOR> 2014-6-10
      * @param type
      * @param resourceGroupId
      * @param sId
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List getExtendParameteValueNew(int type, long resourceGroupId, long sId) throws RepositoryException {
         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
         String sql = "SELECT DISTINCT RE.IID,RE.IRESGROUPID,RE.INAME,RE.IPARAMTYPE,RV.ISERVERID,RV.IVALUE,RV.IID AS VID,RE.IRESNAME FROM (SELECT RG.IRESNAME,R.IID,R.IRESGROUPID,R.INAME,R.IPARAMTYPE,R.ITYPE FROM IEAI_RESOURCE_GROUP RG, IEAI_RESGROUP_EXTENDSPARAMETER R WHERE RG.IID=R.IRESGROUPID AND R.IRESGROUPID=?) RE JOIN IEAI_RESGROUP_EXTENDS_VALUE RV ON RE.IID = RV.IPARAMETERID AND  RE.ITYPE=? AND RV.ISERVERID=? AND RE.IRESGROUPID=?  ORDER BY INAME";
         ResourceGroupParameterDTO resourceGroupParameter = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getExtendParameteValueNew", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, resourceGroupId);
                     actStat.setLong(2, type);
                     actStat.setLong(3, sId);
                     actStat.setLong(4, resourceGroupId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceGroupParameter = new ResourceGroupParameterDTO();
                         resourceGroupParameter.setId(actRS.getLong("IID"));
                         resourceGroupParameter.setName(actRS.getString("INAME"));
                         resourceGroupParameter.setParamType(actRS.getString("IPARAMTYPE"));
                         resourceGroupParameter.setGroupId(actRS.getLong("IRESGROUPID"));
                         resourceGroupParameter.setVid(actRS.getLong("VID"));
                         resourceGroupParameter.setServerId(sId);
                         resourceGroupParameter.setResName(actRS.getString("IRESNAME"));
                         resourceGroupParameter.setValue(null == actRS
                                 .getString("IVALUE") ? "" : actRS
                                 .getString("IVALUE"));
                         list.add(resourceGroupParameter);
                     }
                 } catch (SQLException e) {
                     _log.error("getExtendParameteValueNew is error ! "
                             + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getExtendParameteValueNew", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getExtendParameteValueNew");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     /**
      * 服务器级别获取所有个性化参数值 <li>Description:</li>
      * for SUS
      * <AUTHOR> 2014-9-11
      * @param type
      * @param resourceGroupId
      * @param sId
      * @return
      * @throws RepositoryException
      *             return List
      */
     public List getServerExtendParameters(int type, long sId,long resId) throws RepositoryException {
         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
         String sql = "SELECT " +
                         "DISTINCT RE.IID, "+
                         "RE.IRESGROUPID, "+
                         "RE.INAME, "+
                         "RE.IPARAMTYPE, "+
                         "RV.ISERVERID, "+
                         "RV.IVALUE, "+
                         "RV.IID AS VID, "+
                         "RE.IRESNAME,  "+
                         "RE.IVALUE AS ORIGVALUE "+
                      "FROM "+
                          "(SELECT "+
                              "RG.IRESNAME, "+
                              "R.IID, "+
                              "R.IRESGROUPID, "+
                              "R.INAME, "+
                              "R.IPARAMTYPE, "+
                              "R.ITYPE,  "+
                              "R.IVALUE "+
                            "FROM "+
                              "IEAI_RESOURCE_GROUP RG, "+
                              "IEAI_RESGROUP_EXTENDSPARAMETER R  "+
                            "WHERE "+
                              "RG.IID=R.IRESGROUPID and R.IRESGROUPID in (select igroupid from IEAI_GROUP_BUSINESS where itype=? and IBUSINESSID=? and igroupid=?) "+
                           ") "+
                           "RE   "+
                               "left JOIN (SELECT * "+
                                    "    FROM IEAI_RESGROUP_EXTENDS_VALUE "+
                                    "   WHERE IID IN (SELECT MAX(RV2.IID) "+
                                    "               FROM IEAI_RESGROUP_EXTENDS_VALUE RV2 "+
                                    "              where RV2.ISERVERID = ? "+
                                    "              GROUP BY RV2.IPARAMETERID)) RV  "+
                               "ON RE.IID = RV.IPARAMETERID AND "+
                               "RE.ITYPE=? AND "+
                               "RV.ISERVERID=?  "+
                              // "AND RV.IID IN (SELECT  MAX(RV2.IID)  FROM IEAI_RESGROUP_EXTENDS_VALUE RV2  where RV2.ISERVERID=?  GROUP BY RV2.IPARAMETERID) "+
                           "ORDER BY "+
                               "IRESNAME, INAME";
         ResourceGroupParameterDTO resourceGroupParameter = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getServerExtendParameters", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actStat.setLong(2, sId);
                     actStat.setLong(3, resId);
                     actStat.setLong(4, sId);
                     actStat.setLong(5, type);
                     actStat.setLong(6, sId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceGroupParameter = new ResourceGroupParameterDTO();
                         resourceGroupParameter.setId(actRS.getLong("IID"));
                         resourceGroupParameter.setName(actRS.getString("INAME"));
                         resourceGroupParameter.setParamType(actRS.getString("IPARAMTYPE"));
                         resourceGroupParameter.setGroupId(actRS.getLong("IRESGROUPID"));
                         resourceGroupParameter.setVid(actRS.getLong("VID"));
                         resourceGroupParameter.setResName(actRS.getString("IRESNAME"));
                         resourceGroupParameter.setValue(null == actRS
                                 .getString("IVALUE") ? "" : actRS
                                 .getString("IVALUE"));
                         resourceGroupParameter.setOriginalValue(null == actRS
                                 .getString("ORIGVALUE") ? "" : actRS
                                 .getString("ORIGVALUE"));
                         list.add(resourceGroupParameter);
                     }
                 } catch (SQLException e) {
                     _log.error("getServerExtendParameters is error ! "
                             + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getServerExtendParameters", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServerExtendParameters");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     /**
      * 服务器级别获取所有个性化参数值 <li>Description:</li>
      * for SUS
      * <AUTHOR> 2014-9-11
      * @param type
      * @param resourceGroupId
      * @param sId
      * @return
      * @throws RepositoryException
      *             return List
      */
     public Map <String,List<ResourceGroupParameterDTO>> getServerExtendParametersAll(int type) throws RepositoryException {
         Map <String,List<ResourceGroupParameterDTO>> map =new LinkedHashMap<String, List<ResourceGroupParameterDTO>>();
         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
         StringBuffer buf = new StringBuffer();
         buf.append("");
         buf.append("         SELECT T.*,EV.IVALUE SERVER_VAL");
         buf.append("         FROM (      ");
         buf.append("                  SELECT F.*,RN.IBUSINESSID");
         buf.append("                     FROM ");
         buf.append("                           (SELECT RG.IID,RG.IRESNAME, R.IID PRIID,R.INAME,R.IVALUE,R.IPRAM_CONER"); 
         buf.append("                             FROM");
         buf.append("                                 IEAI_RESOURCE_GROUP RG, IEAI_RESGROUP_EXTENDSPARAMETER R");         
         buf.append("                            WHERE ");
         buf.append("                                  RG.IID =R.IRESGROUPID"); 
         buf.append("                            )  F , IEAI_GROUP_BUSINESS RN ");   
         buf.append("                     WHERE F.IID =RN.IGROUPID");
         //buf.append("                  ORDER BY F.IID,RN.IBUSINESSID,F.IPRAM_CONER");                 
         buf.append("                )T  ");
         buf.append("       LEFT JOIN ");
         buf.append("                 IEAI_RESGROUP_EXTENDS_VALUE EV");
         buf.append("              ON T.IBUSINESSID=EV.ISERVERID  AND T.PRIID=EV.IPARAMETERID");   
         //buf.append("        ORDER BY T.IID,T.IBUSINESSID , INAME DESC  ");  
         ResourceGroupParameterDTO resourceGroupParameter = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getServerExtendParametersAll", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(buf.toString());
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         String sResName =actRS.getString("IRESNAME");
                        if (null == map.get(sResName))
                        {
                            list =new ArrayList<ResourceGroupParameterDTO>();
                            map.put(sResName, list);
                        }
                         resourceGroupParameter = new ResourceGroupParameterDTO();
                         resourceGroupParameter.setResName(actRS.getString("IRESNAME"));       
                         resourceGroupParameter.setIpram_coner(actRS.getString("IPRAM_CONER"));
                         resourceGroupParameter.setName(actRS.getString("INAME"));
                         resourceGroupParameter.setGroupId(actRS.getLong("IID"));
                         resourceGroupParameter.setServerId(actRS.getLong("IBUSINESSID"));
                         resourceGroupParameter.setOriginalValue(null == actRS
                                 .getString("IVALUE") ? "" : actRS
                                         .getString("IVALUE"));
                         resourceGroupParameter.setValue(null == actRS
                                 .getString("SERVER_VAL") ? "" : actRS
                                 .getString("SERVER_VAL"));
                         list.add(resourceGroupParameter);
                     }
                 } catch (SQLException e) {
                     _log.error("getServerExtendParametersAll is error ! "
                             + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getServerExtendParametersAll", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getServerExtendParametersAll");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                         .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return map;
     }
     
     public List getUnchoosedParameterValue(int type, long resourceGroupId, long serverId)
             throws RepositoryException {
         List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
         String sql = "select IID, INAME, IPARAMTYPE, IVALUE from IEAI_RESGROUP_EXTENDSPARAMETER where ITYPE=? and IRESGROUPID=? and IID not in (select IPARAMETERID from IEAI_RESGROUP_EXTENDS_VALUE where ISERVERID=?)";
         String sqlTotal = 
                 "select count(iid) as TOTAL from IEAI_GROUP_BUSINESS where IGROUPID=? and IBUSINESSID=?";
         ResourceGroupParameterDTO resourceGroupParameter = null;
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("getUnchoosedParameterValue", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sqlTotal);
                     actStat.setLong(1, resourceGroupId);
                     actStat.setLong(2, serverId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         if(actRS.getLong("TOTAL")<1){
                             return list;
                         }
                     }
                     actStat = null;
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, type);
                     actStat.setLong(2, resourceGroupId);
                     actStat.setLong(3, serverId);
                     actRS = actStat.executeQuery();
                     while (actRS.next()) {
                         resourceGroupParameter = new ResourceGroupParameterDTO();
                         resourceGroupParameter.setId(actRS.getLong("IID"));
                         resourceGroupParameter.setName(actRS.getString("INAME"));
                         resourceGroupParameter.setParamType(actRS.getString("IPARAMTYPE"));
                         resourceGroupParameter.setValue(actRS.getString("IVALUE"));
                         resourceGroupParameter.setGroupId(resourceGroupId);
                         resourceGroupParameter.setServerId(serverId);
                         list.add(resourceGroupParameter);
                     }
                 } catch (SQLException e) {
                     _log.error("getExtendParameteValue is error ! "
                             + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally {
                     DBResource.closeConn(con, actRS, actStat,
                             "getUnchoosedParameterValue", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:getUnchoosedParameterValue");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource
                 .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return list;
     }
     
     /**
      * 删除扩展参数属性 <li>Description:</li>
      * 
      * <AUTHOR> 2014-6-10
      * @param id
      * @throws RepositoryException
      *             return void
      */
     public void deleteServerExtendParameter(Long paramId, Long serverId) throws RepositoryException {
         String sqlNext = "DELETE FROM IEAI_RESGROUP_EXTENDS_VALUE WHERE IPARAMETERID=? and ISERVERID=? ";
        for (int i = 0;; i++)
        {
             try {
                 PreparedStatement actStat = null;
                 Connection con = null;
                 try {
                     con = DBResource.getConnection("deleteServerExtendParameter", _log,Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sqlNext);
                     actStat.setLong(1, paramId);
                     actStat.setLong(2, serverId);
                     actStat.executeUpdate();
                     con.commit();
                 } catch (SQLException e) {
                     _log.error("deleteExtendParamete is error ! "
                             + e.getMessage());
                    DBResource.rollback(con, ServerError.ERR_DB_DELETE, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                 } finally {
                     DBResource.closePSConn(con, actStat,
                             "deleteServerExtendParameter", _log);
                     if (SystemConfig.isElapsedTime())
                     {
                         _log.info("Jdbc Connection closeConnection and method:deleteServerExtendParameter");
                     }
                 }
                 break;
             } catch (RepositoryException ex) {
                 DBResource.throwRepositoryException(i,
                         ServerError.ERR_DB_UPDATE);
             }
         }
     }
     /**
      * 获取设置过的操作系统类型 <li>Description:</li>
      * 
      * <AUTHOR> 2014-6-11
      * @param type
      * @return
      * @throws RepositoryException return List
      */
     public String getResGroupNameByPid (long paramiid) throws RepositoryException
     {
         String sql = "SELECT R.IRESNAME FROM IEAI_RESOURCE_GROUP R,IEAI_RESGROUP_EXTENDSPARAMETER P WHERE R.IID =P.IRESGROUPID AND P.IID=?";
         String resName="";
        for (int i = 0;; i++)
         {
             try
             {
                 PreparedStatement actStat = null;
                 ResultSet actRS = null;
                 Connection con = null;
                 try
                 {
                     con = DBResource.getConnection("getResGroupNameByPid", _log, Constants.IEAI_SUS);
                     actStat = con.prepareStatement(sql);
                     actStat.setLong(1, paramiid);
                     actRS = actStat.executeQuery();
                     while (actRS.next())
                     {
                         resName=actRS.getString("IRESNAME");
                     }
                 } catch (SQLException e)
                 {
                     _log.error("getResGroupNameByPid is error ! " + e.getMessage());
                     throw new RepositoryException(ServerError.ERR_DB_QUERY);
                 } finally
                 {
                     DBResource.closeConn(con, actRS, actStat, "getResGroupNameByPid", _log);
                 }
                 break;
             } catch (RepositoryException ex)
             {
                 DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
             }
         }
         return resName;
     }

    /**
     * 
     * @Title: getResourceGroupByNameLoop
     * @Description: TODO(根据资源组名获取资源组ID-循环重试)
     * @param groupName
     * @param type
     * @return
     * @throws RepositoryException
     * @return Long 返回类型
     * @throws
     * @变更记录 2017年2月27日
     */
    public Long getResourceGroupByNameLoop ( String groupName, int type ) throws RepositoryException
    {
        long returnValue = 0;
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResourceGroupByNameLoop", _log, type);
                    returnValue = this.getResourceGroupByName(con, groupName);
                    break;
                } catch (SQLException e)
                {
                    _log.error("getResourceGroupByNameLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "getResourceGroupByNameLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: getResourceGroupByName
     * @Description: TODO(根据资源组名获取资源组ID)
     * @param con
     * @param groupName
     * @return
     * @throws SQLException
     * @return Long 返回类型
     * @throws
     * @变更记录 2017年2月27日
     */
    public Long getResourceGroupByName ( Connection con, String groupName ) throws SQLException
    {
        Long returnValue = 0L;
        String sqlString = "SELECT T.IID FROM IEAI_RESOURCE_GROUP T WHERE T.IRESNAME=?";

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(sqlString);
            ps.setString(1, groupName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                returnValue = rs.getLong("IID");
                break;
            }

        } catch (SQLException e)
        {
            _log.error("getResourceGroupByName is error ! " + e.getMessage());
            throw e;
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return returnValue;
    }
    /**
     * <li>Description: 更新工程与资源组的绑定关系</li>
     * 
     * <AUTHOR> 2016-11-09
     * @param type
     * @return
     * @return
     * @throws RepositoryException return List
     */
    public void updateIBusSysEnvGroupBinder ( String iBusSysId, Long[] groupIds, int type, String strGrops ) throws RepositoryException
    {
        String deleteRelationSQL = "delete IEAI_GROUP_ENV_IBUSYS B WHERE B.IBUSYS_IID =?";
        String bindSql = "INSERT INTO IEAI_GROUP_ENV_IBUSYS (IID,IBUSYS_IID,IENVID)VALUES(?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("IEAI_GROUP_ENV_IBUSYS", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(deleteRelationSQL);
                    actStat.setString(1, iBusSysId);
                    actStat.executeUpdate();
                    long iid = -1;
                    actStat = con.prepareStatement(bindSql);
                    if (null != groupIds)
                    {
                        for (int j = 0; j < groupIds.length; j++)
                        {
                            iid = IdGenerator.createId("IEAI_SUS_BSYS_RSGROUP", con);
                            actStat.setLong(1, iid);
                            actStat.setString(2, iBusSysId);
                            actStat.setLong(3, groupIds[j]);
                            actStat.executeUpdate();
                        }
                    }

                    con.commit();

                } catch (SQLException e)
                {
                    _log.error("updateIBusSysResourceGroupBinder is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "updateIBusSysResourceGroupBinder", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:updateIBusSysResourceGroupBinder");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
    }
    
    /**
     * @throws RepositoryException 
     * 
     * @Title: organizeBusSysBindEnvSql
     * @Description: TODO(多写业务系统绑定环境,组织执行和回滚语句)
     * @param baseConn
     * @param iBusSysId
     * @param groupIds
     * @return
     * @return Map 返回类型
     * @throws
     */
    public Map organizeBusSysBindEnvSql ( Connection baseConn, String iBusSysId, Long[] groupIds )
            throws RepositoryException
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        try
        {
            List<Map<String, Long>> returnList = this.getGroupEnvBusys(baseConn, iBusSysId);
            for (Map<String, Long> mapObj : returnList)
            {
                // 组织回滚语句
                rollbackSqls.add("INSERT INTO IEAI_GROUP_ENV_IBUSYS (IID,IBUSYS_IID,IENVID)VALUES(" + mapObj.get("iid") + "," + mapObj.get("ibusysIid") + ","
                        + mapObj.get("ienvId") + ")");
                // 组织执行语句
                exeSqls.add("DELETE IEAI_GROUP_ENV_IBUSYS B WHERE B.IID =" + mapObj.get("iid"));
            }
            for (Long groupId : groupIds)
            {
                long iid = IdGenerator.createId("IEAI_GROUP_ENV_IBUSYS", baseConn);
                // 组织回滚语句
                rollbackSqls.add("DELETE IEAI_GROUP_ENV_IBUSYS B WHERE B.IID =" + iid);
                // 组织执行语句
                exeSqls.add("INSERT INTO IEAI_GROUP_ENV_IBUSYS (IID,IBUSYS_IID,IENVID)VALUES(" + iid + "," + iBusSysId + "," + groupId + ")");

            }

        } catch (SQLException e)
        {
            isSuccess = false;
            DBResource.rollback(baseConn, ServerError.ERR_DB_INSERT, e,
                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        } catch (Exception e)
        {
            isSuccess = false;
            DBResource.rollback(baseConn, ServerError.ERR_DB_INSERT, e,
                Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;

    }
    
    /**
     * @throws RepositoryException 
     * 
     * @Title: getGroupEnvBusys
     * @Description: TODO(根据业务系统id查询所有绑定的环境)
     * @param con
     * @param iBusSysId
     * @return
     * @throws SQLException
     * @return List<Map<String,Long>> 返回类型
     * @throws
     */
    public List<Map<String, Long>> getGroupEnvBusys ( Connection con, String iBusSysId )
            throws SQLException, RepositoryException
    {
        String sql = "SELECT T.* FROM IEAI_GROUP_ENV_IBUSYS T WHERE 1=1 ";
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<Map<String, Long>> returnList = new ArrayList<Map<String, Long>>();
        try
        {
            if (iBusSysId != null && !"".equals(iBusSysId))
            {
                sql += " AND  T.IBUSYS_IID=" + iBusSysId;
            }
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map<String, Long> groupEnvBusysMap = new HashMap<String, Long>();
                groupEnvBusysMap.put("iid", rs.getLong("IID"));
                groupEnvBusysMap.put("ibusysIid", rs.getLong("IBUSYS_IID"));
                groupEnvBusysMap.put("ienvId", rs.getLong("IENVID"));
                returnList.add(groupEnvBusysMap);
            }

        } catch (SQLException e)
        {
            _log.error("getGroupEnvBusys() is error：" + e.getMessage());
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return returnList;
    }
    /**
     * <li>Description: 更新工程与资源组的绑定关系</li>
     * 
     * <AUTHOR> 2016-11-09
     * @param type
     * @return
     * @return
     * @throws RepositoryException return List
     */
    public void updateIBusSysResourceGroupBinder ( String iBusSysId, Long[] groupIds, int type, String strGrops ) throws RepositoryException
    {
        String deleteRelationSQL = "delete FROM IEAI_SUS_BSYS_RSGROUP  WHERE IBUSNES_SYS_IID =?";
        String bindSql = "INSERT INTO IEAI_SUS_BSYS_RSGROUP (IID,IBUSNES_SYS_IID,IRESOURCE_GROUP_IID)VALUES(?,?,?)";
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("IEAI_SUS_BSYS_RSGROUP", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(deleteRelationSQL);
                    actStat.setString(1, iBusSysId);
                    actStat.executeUpdate();
                    long iid = -1;
                    actStat = con.prepareStatement(bindSql);
                    if (null != groupIds)
                    {
                        for (int j = 0; j < groupIds.length; j++)
                        {
                            iid = IdGenerator.createId("IEAI_SUS_BSYS_RSGROUP", con);
                            actStat.setLong(1, iid);
                            actStat.setString(2, iBusSysId);
                            actStat.setLong(3, groupIds[j]);
                            actStat.executeUpdate();
                        }
                    }

                    con.commit();

                } catch (SQLException e)
                {
                    _log.error("updateIBusSysResourceGroupBinder is error ! " + e.getMessage());
                    DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                        Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "updateIBusSysResourceGroupBinder", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:updateIBusSysResourceGroupBinder");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
    }
    /**
     * <li>Description:获取已经配置的补丁分发的资源组</li>
     * 
     * <AUTHOR> Aug 10, 2012
     * @param type
     * @return
     * @throws RepositoryException return List
     */
    public List getResGroup ( int type, String gname, Long busnesSysIid ) throws RepositoryException
    {
        List list = new ArrayList();
        // String sql =
        // "SELECT IID, IRESNAME FROM IEAI_RESOURCE_GROUP WHERE ITYPE=? ORDER BY IRESNAME";
        // modified by liyang.
        String sql = " SELECT IID, IRESNAME, IRESDES FROM IEAI_RESOURCE_GROUP WHERE  ITYPE=? ";
        if (null != gname && !"".equals(gname))
        {
            sql += " AND IRESNAME!='" + gname + "'";
        }
        if (null != busnesSysIid && busnesSysIid > 0)
        {
            sql += " AND IID IN(SELECT T.IRESOURCE_GROUP_IID FROM IEAI_SUS_BSYS_RSGROUP T WHERE T.IBUSNES_SYS_IID=" + busnesSysIid + ")";
        }
        sql += " ORDER BY IRESNAME";
        String[] result = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResGroup", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actRS = actStat.executeQuery();

                    while (actRS.next())
                    {
                        result = new String[3]; // 数组size 由2改为3
                        result[0] = String.valueOf(actRS.getLong("IID"));
                        result[1] = actRS.getString("IRESNAME");
                        result[2] = actRS.getString("IRESDES"); // add by liyang
                        list.add(result);
                    }
                } catch (SQLException e)
                {
                    _log.error("getResGroup is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getResGroup", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResGroup");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    /**
     * <li>Description:获取与业务系统绑定的资源组</li>
     * 
     * <AUTHOR> 2016-11-09
     * @param resourceWorkflow
     * @param flag true 为获得已绑定的资源组 false为获取未绑定的资源组
     * @return
     * @throws RepositoryException return List
     */
    public List<ResourceGroupDTO> getResourceGroupsByIBusSysId ( String iBusSysId, int type, Boolean flag ) throws RepositoryException
    {
        List<ResourceGroupDTO> list = new ArrayList<ResourceGroupDTO>();
        String sql;
        if (flag)
        {
            sql = "select IID,IRESNAME,IRESDES,ITYPE from IEAI_RESOURCE_GROUP R"
                    + " WHERE R.IID IN(select B.IRESOURCE_GROUP_IID from IEAI_SUS_BSYS_RSGROUP B WHERE B.IBUSNES_SYS_IID =?) order by IRESNAME ";
        } else
        {
            sql = "select IID,IRESNAME,IRESDES,ITYPE from IEAI_RESOURCE_GROUP R"
                    + " WHERE R.IID NOT IN(select B.IRESOURCE_GROUP_IID from IEAI_SUS_BSYS_RSGROUP B WHERE B.IBUSNES_SYS_IID =?) order by IRESNAME ";
        }
        ResourceGroupDTO resourceGroup = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getResourceGroupsByIBusSysId", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, iBusSysId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        resourceGroup = new ResourceGroupDTO();
                        resourceGroup.setId(actRS.getLong("IID"));
                        resourceGroup.setName(actRS.getString("IRESNAME"));
                        resourceGroup.setDescription(actRS.getString("IRESDES"));
                        list.add(resourceGroup);
                    }
                } catch (SQLException e)
                {
                    _log.error("getResourceGroupsByIBusSysId is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getResourceGroupsByIBusSysId", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResourceGroupsByIBusSysId");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * <li>Description:获取与业务系统绑定的资源组</li>
     * 
     * <AUTHOR> 2016-11-09
     * @param resourceWorkflow
     * @param flag true 为获得已绑定的资源组 false为获取未绑定的资源组
     * @return
     * @throws RepositoryException return List
     */
    public List<ResourceGropEnvBean> getEnvByIBSysId ( String bSysId, int type, Boolean flag ) throws RepositoryException
    {
        List<ResourceGropEnvBean> list = new ArrayList<ResourceGropEnvBean>();
        String sql;
        if (flag)
        {
            sql = " SELECT E.IID,E.IDESCRIPTION,E.INAME FROM IEAI_GROUP_ENV E"
                    + " WHERE E.IID IN(SELECT S.IENVID FROM IEAI_GROUP_ENV_IBUSYS S WHERE S.IBUSYS_IID =?) order by INAME ";
        } else
        {
            sql = " SELECT E.IID,E.IDESCRIPTION,E.INAME FROM IEAI_GROUP_ENV E"
                    + " WHERE E.IID NOT IN(SELECT S.IENVID FROM IEAI_GROUP_ENV_IBUSYS S WHERE S.IBUSYS_IID =?) order by INAME ";
        }
        ResourceGropEnvBean env = null;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getEnvByIBSysId", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, bSysId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        env = new ResourceGropEnvBean();
                        env.setIid(actRS.getString("IID"));
                        env.setName(actRS.getString("INAME"));
                        env.setDescription(actRS.getString("IDESCRIPTION"));
                        list.add(env);
                    }
                } catch (SQLException e)
                {
                    _log.error("getEnvByIBSysId is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getEnvByIBSysId", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getEnvByIBSysId");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    /**
     * 
     * <li>Description:同步CMDB Server信息</li> 
     * <AUTHOR>
     * 2017年5月26日 
     * @param res
     * @param type
     * @return
     * return boolean
     */
    public Map<String, Object>  syncCMDBData(List<ResourceServerDTO> res,int type)throws Exception{
        
        Map<String, Object> result = new HashMap<String, Object>();
        if(res.size() > 0){
            List<Map> list = new ArrayList<Map>();
            
            String sql = "SELECT T.AGENTIP, T.HOSTNAME,T.INSTANCEID  FROM IEAI_CMDB_COMPUTER T WHERE T.AGENTIP = ?";
            
            String updateSql = "UPDATE IEAI_RESOURCE_BUSINESS T  SET  T.IHOSTNAME = ? , T.INSTANCEID = ? WHERE T.IIP = ?";
            
            long s1 = System.currentTimeMillis();
            for (int i = 0;; i++)
            {
                try
                {
                    PreparedStatement pstmt = null;
                    PreparedStatement statement = null;
                    ResultSet rs = null;
                    // 数据库连接对象
                    Connection conn = null;
                    try
                    {
                        conn = DBResource.getConnection("syncCMDBData", _log, type);
                        
                        pstmt = conn.prepareStatement(sql);
                        
                        for (int j = 0; j < res.size(); j++)
                        {

                            ResourceServerDTO dto = res.get(j);

                            pstmt.setString(1, dto.getIp());

                            rs = pstmt.executeQuery();

                            while (rs.next())
                            {
                                Map map = new HashMap();

                                String instanceId = rs.getString("INSTANCEID");

                                String hostName = rs.getString("HOSTNAME");

                                map.put("instanceId", instanceId);

                                map.put("hostName", hostName);

                                map.put("agentIp", dto.getIp());

                                list.add(map);
                            }
                        }
                        
                        if (list.size() > 0)
                        {

                            statement = conn.prepareStatement(updateSql);

                            for (int j = 0; j < list.size(); j++)
                            {
                                Map map = list.get(j);
                                statement.setString(1, String.valueOf(map.get("hostName")));
                                statement.setString(2, String.valueOf(map.get("instanceId")));
                                statement.setString(3, String.valueOf(map.get("agentIp")));

                                statement.executeUpdate();
                            }
                        }
                        
                        long i2 = System.currentTimeMillis();
                        _log.info("======IEAI_RESOURCE_BUSINESS=======time:" + (i2 - s1));
                        conn.commit();
                        // result.put("message", "成功同步"+list.size() + "条数据");
                        result.put("message", "数据同步完成");
                        result.put("success", true);
                    } catch (SQLException e)
                    {
                        result.put("success", false);
                        _log.error("syncCMDBData Exception " + e.getMessage());
                        DBResource.rollback(conn, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePreparedStatement(pstmt,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        DBResource.closePreparedStatement(statement,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        DBResource.closeResultSet(rs, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                        DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(),
                            _log);
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    DBRetryUtil.waitForNextTry(i, ex);
                }
            }
        }
        return result;
    }
    public boolean isExist(ResourceServerDTO resourceServer, int type) throws RepositoryException{
        
        boolean flag = true;
        String countSql = "SELECT COUNT(*) FROM IEAI_RESOURCE_BUSINESS T WHERE  T.IIP = ? AND T.ISYSTEMTYPE = ?";
        int count = 0;
        for (int i = 0;; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;

                try
                {
                    con = DBResource.getConnection("isExist", _log, type);
                    actStat = con.prepareStatement(countSql);
                    actStat.setString(1, resourceServer.getIp());
                    actStat.setString(2, resourceServer.getSystemType());
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        count = actRS.getInt(1);
                    }

                    if (count > 0)
                    {
                        flag = false;
                    }
                } catch (SQLException e)
                {
                    _log.error("isExist is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "isExist", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:isExist");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return flag;
    }
    public String saveCMDBServer ( ResourceServerDTO resourceServer, int type) throws RepositoryException
    {
        long tid = resourceServer.getId();
        String strTid = null;
        

        String sql = " INSERT INTO IEAI_RESOURCE_BUSINESS(IID ,IHOSTNAME ,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODULETYPE,IMODIFYTYPE,ICOVERTYPE,INSTANCEID) VALUES(?,?,?,?,?,?,?,?,?,?,?) ";

        if (!"".equals(resourceServer.getIp().trim()) && !(resourceServer.getPort() == 0))
        {
            for (int i = 0;; i++)
            {
                try
                {
                    PreparedStatement actStat = null;
                    Connection con = null;
                    try
                    {
                        int index = 0;
                        con = DBResource.getConnection("saveServerByType2", _log, Constants.IEAI_SUS);
                        actStat = con.prepareStatement(sql);
                        tid = IdGenerator.createId("IEAI_RESOURCE_BUSINESS", con);
                        actStat.setLong(++index, tid);
                        actStat.setString(++index, resourceServer.getHostName());
                        actStat.setString(++index, resourceServer.getIp());
                        actStat.setInt(++index, resourceServer.getPort());
                        actStat.setInt(++index, resourceServer.getPriority());
                        actStat.setInt(++index, type);
                        actStat.setString(++index, resourceServer.getSystemType());
                        actStat.setString(++index, resourceServer.getIsModuleType());
                        actStat.setString(++index, resourceServer.getIsModifyType());
                        actStat.setString(++index, resourceServer.getIsCoverType());
                        actStat.setString(++index, resourceServer.getInstanceId());
                        actStat.executeUpdate();
                        con.commit();
                        strTid = "";
                        if (false == resourceServer.getChecked())
                        {
                            strTid = "0"; // 只是新增一个服务器，无需进行绑定操作
                        } 
                    } catch (SQLException e)
                    {
                        _log.error("saveServerByType is error ! " + e.getMessage());
                        DBResource.rollback(con, ServerError.ERR_DB_INSERT, e,
                            Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                    } finally
                    {
                        DBResource.closePSConn(con, actStat, "saveServerByType2", _log);
                        if (SystemConfig.isElapsedTime())
                        {
                            _log.info("Jdbc Connection closeConnection and method:saveServerByType2");
                        }
                    }
                    break;
                } catch (RepositoryException ex)
                {
                    DBResource.throwRepositoryException(i, ServerError.ERR_DB_UPDATE);
                }
            }
        }
         
        return strTid;
    }
    
    /**
     * <li>Description:根据ip获取Servers</li> 
     * <AUTHOR>
     * 2017年6月12日 
     * @param type
     * @param ip
     * @return
     * @throws RepositoryException
     * return List<ResourceServerDTO>
     */
    public List<ResourceServerDTO> getServersByIp(int type,String ip, String port ) throws RepositoryException {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();

        StringBuffer sb = new StringBuffer();
        if (port != null && !"null".equals(port) && StringUtils.isNotEmpty(port)){
            sb.append("AND IPORT=?");
        }
        String sql = "SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODEL_TYPE,IPROXYIP FROM IEAI_RESOURCE_BUSINESS WHERE IIP=?  "+sb.toString();
        
        ResourceServerDTO resourceServer = null;
        for (int i = 0;; i++)
        {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getServersByIp", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, ip);
                    if (port != null && !"null".equals(port) && StringUtils.isNotEmpty(port)){
                        actStat.setString(2, port);
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceServer = new ResourceServerDTO();
                        resourceServer.setId(actRS.getLong("IID"));
                        resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                        resourceServer.setIp(actRS.getString("IIP"));
                        resourceServer.setPort(actRS.getInt("IPORT"));
                        resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                        resourceServer.setType(actRS.getInt("ITYPE"));
                        resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        resourceServer.setImodeltype(actRS.getString("IMODEL_TYPE"));
                        String proxyIp = actRS.getString("IPROXYIP");
                        if(org.apache.commons.lang3.StringUtils.isNotBlank(proxyIp) && !"null".equals(proxyIp) ){
                            resourceServer.setIazNetId(actRS.getString("IPROXYIP"));
                        }
                        list.add(resourceServer);
                    }
                } catch (SQLException e) {
                    _log.error("getServersByType is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getServersByIp",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getServersByIp");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 
     * @Title: getServerExtendParametersAllE_SUS   
     * @Description: 根据运行表主表id获取资源组服务器参数
     * @param con
     * @param mainId
     * @return
     * @throws SQLException      
     * @author: yunpeng_zhang 
     * @throws RepositoryException 
     * @date:   2017年11月16日 下午5:03:42
     */
    public Map<String, List<ResourceGroupParameterDTO>> getServerExtendParametersAllE_SUS ( Connection con, Long mainId )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceGroupParameterDTO>> map = new LinkedHashMap<String, List<ResourceGroupParameterDTO>>();
        List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append("SELECT IENV.INAME IENVNAME,IGROUP.IID  IID,IGROUP.IRESNAME IRESNAME,PARAMETER.INAME INAME,PARAMETER.IVALUE IVALUE,VALUEP.IVALUE SERVER_VAL,IBUS.IID IBUSINESSID,PARAMETER.IPRAM_CONER   \n"
                + "FROM IEAI_SUS_BSYS_ENV_RES RES \n"
                + "LEFT JOIN IEAI_RESOURCE_GROUP IGROUP ON RES.IEAI_RESOURCE_GROUP=IGROUP.IID\n"
                + "LEFT JOIN IEAI_GROUP_ENV IENV  ON RES.IEAI_GROUP_ENV=IENV.IID\n"
                + "LEFT JOIN IEAI_GROUP_BUSINESS IGBUSINESS ON IGBUSINESS.IGROUPID=RES.IEAI_RESOURCE_GROUP\n"
                + "LEFT JOIN IEAI_RESOURCE_BUSINESS IBUS ON IBUS.IID=IBUSINESSID\n"
                + "LEFT JOIN IEAI_INSTANCE_VERSION IVER ON IVER.IUPPERID=RES.IEAI_SUS_BUSNES_SYS\n"
                + "LEFT JOIN IEAI_RESGROUP_EXTENDSPARAMETER PARAMETER ON PARAMETER.IRESGROUPID=IGROUP.IID \n"
                + "LEFT JOIN IEAI_RESGROUP_EXTENDS_VALUE VALUEP ON VALUEP.IPARAMETERID=PARAMETER.IID AND VALUEP.ISERVERID=IBUS.IID\n"
                + "LEFT JOIN IEAI_RUN_INSTANCE RINS ON  RINS.ISYSID=IVER.IID\n"
                + "WHERE  RINS.IID="
                + mainId
                + " AND IBUS.IID IS NOT NULL\n"
                + " ORDER BY IENV.INAME,IGROUP.IRESNAME,IGBUSINESS.IBUSINESSID,LENGTH(PARAMETER.INAME) DESC");
        ResourceGroupParameterDTO resourceGroupParameter = null;

        PreparedStatement actStat = null;
        ResultSet actRS = null;
        try
        {
            actStat = con.prepareStatement(buf.toString());
            actRS = actStat.executeQuery();
            while (actRS.next())
            {
                String sResName = actRS.getString("IRESNAME");
                String sEnvName = actRS.getString("IENVNAME");

                if (null == map.get(sEnvName + "||" + sResName))
                {
                    list = new ArrayList<ResourceGroupParameterDTO>();
                    map.put(sEnvName + "||" + sResName, list);
                }
                resourceGroupParameter = new ResourceGroupParameterDTO();
                resourceGroupParameter.setResName(actRS.getString("IRESNAME"));
                resourceGroupParameter.setIpram_coner(actRS.getString("IPRAM_CONER"));
                resourceGroupParameter.setName(actRS.getString("INAME"));
                resourceGroupParameter.setGroupId(actRS.getLong("IID"));
                resourceGroupParameter.setServerId(actRS.getLong("IBUSINESSID"));
                resourceGroupParameter.setOriginalValue(null == actRS.getString("IVALUE") ? "" : actRS
                        .getString("IVALUE"));
                resourceGroupParameter.setValue(null == actRS.getString("SERVER_VAL") ? "" : actRS
                        .getString("SERVER_VAL"));
                list.add(resourceGroupParameter);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(actRS, actStat, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return map;
    }

    /**
     * 
     * @Title: getServerExtendParametersAllE_SUSLoop   
     * @Description:   根据运行表主表id获取资源组服务器参数--循环重试
     * @param mainId
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2017年11月17日 上午8:03:04
     */
    public Map<String, List<ResourceGroupParameterDTO>> getServerExtendParametersAllE_SUSLoop ( Long mainId, int type )
            throws RepositoryException
    {
        Map<String, List<ResourceGroupParameterDTO>> map = new LinkedHashMap<String, List<ResourceGroupParameterDTO>>();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        type);
                    map = this.getServerExtendParametersAllE_SUS(con, mainId);
                    break;
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return map;
    }

    /**
     * 
     * @Title: getAllGroupServersE_SUS   
     * @Description: 根据instanceID获取绑定的所有环境、资源组、服务器
     * @param instanceId
     * @return
     * @throws SQLException      
     * @author: yunpeng_zhang 
     * @throws RepositoryException 
     * @date:   2017年11月16日 上午11:00:01
     */
    public static Map<String, List<ResourceServerDTO>> getAllGroupServersE_SUS ( Connection con, Long instanceId )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceServerDTO>> resultMap = new LinkedHashMap<String, List<ResourceServerDTO>>();
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append("SELECT IENV.IID IENVID,IENV.INAME IENVNAME,IGROUP.IID  IRESID,IGROUP.IRESNAME IRESNAME,IBUS.IID,IBUS.IHOSTNAME,IBUS.IIP,IBUS.IPORT,IBUS.ISYSTEMTYPE,IBUS.IMODEL_TYPE,IBUS.ITYPE ,IBUS.IPRIORITY\n"
                + "FROM IEAI_SUS_BSYS_ENV_RES RES \n"
                + "LEFT JOIN IEAI_RESOURCE_GROUP IGROUP ON RES.IEAI_RESOURCE_GROUP=IGROUP.IID\n"
                + "LEFT JOIN IEAI_GROUP_ENV IENV  ON RES.IEAI_GROUP_ENV=IENV.IID\n"
                + "LEFT JOIN IEAI_GROUP_BUSINESS IGBUSINESS ON IGBUSINESS.IGROUPID=RES.IEAI_RESOURCE_GROUP\n"
                + "LEFT JOIN IEAI_RESOURCE_BUSINESS IBUS ON IBUS.IID=IBUSINESSID\n"
                + "LEFT JOIN IEAI_INSTANCE_VERSION IVER ON IVER.IUPPERID=RES.IEAI_SUS_BUSNES_SYS\n"
                + "WHERE  IVER.IID="
                + instanceId
                + " AND IBUS.IID IS NOT NULL\n"
                +" GROUP BY IENV.IID,IENV.INAME,IGROUP.IID,IGROUP.IRESNAME,IBUS.IID,IBUS.IHOSTNAME,IBUS.IIP,IBUS.IPORT,IBUS.ISYSTEMTYPE,IBUS.IMODEL_TYPE,IBUS.ITYPE ,IBUS.IPRIORITY"
                + " ORDER BY IENV.INAME,IGROUP.IRESNAME");

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(buf.toString());
            rs = ps.executeQuery();
            while (rs.next())
            {
                String curResName = rs.getString("IRESNAME");
                String curEnvName = rs.getString("IENVNAME");
                // 因为是从新资源组关系中获取服务器，map的key值为 环境名||资源组名
                String keyString = curEnvName + "||" + curResName;
                if (null == resultMap.get(keyString))
                {
                    list = new ArrayList<ResourceServerDTO>();
                    resultMap.put(keyString, list);
                }
                ResourceServerDTO resourceServer = new ResourceServerDTO();
                resourceServer.setIresid(rs.getString("IRESID"));
                resourceServer.setIresname(rs.getString("IRESNAME"));
                resourceServer.setImodeltype(rs.getString("IMODEL_TYPE"));
                resourceServer.setId(rs.getLong("IID"));
                resourceServer.setHostName(rs.getString("IHOSTNAME"));
                resourceServer.setIp(rs.getString("IIP"));
                resourceServer.setPort(rs.getInt("IPORT"));
                resourceServer.setPriority(rs.getInt("IPRIORITY"));
                resourceServer.setType(rs.getInt("ITYPE"));
                resourceServer.setSystemType(rs.getString("ISYSTEMTYPE"));
                resourceServer.setIenvname(curEnvName);
                resourceServer.setIenvid(rs.getString("IENVID"));
                list.add(resourceServer);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultMap;
    }

    
    
    /**
     * 
     * @Title: getAllGroupServersE_SUS   
     * @Description: 根据instanceID获取绑定的所有环境、资源组、服务器
     * @param instanceId
     * @return
     * @throws SQLException      
     * @author: yunpeng_zhang 
     * @throws RepositoryException 
     * @date:   2017年11月16日 上午11:00:01
     */
    public static Map<String, List<ResourceServerDTO>> getAllGroupServersE_SUS_Excel ( Connection con, Long instanceId )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceServerDTO>> resultMap = new LinkedHashMap<String, List<ResourceServerDTO>>();
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append(" ");
        buf.append("  SELECT IENVNAME,IRESNAME,IRESID,IMODEL_TYPE,IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IENVID,IAZNAME,IAZNETID,IPROXYIP "); 
        buf.append("  FROM  ");
        buf.append("      (SELECT  A.IEAI_SUS_BUSNES_SYS,E.IID AS IENVID,E.INAME AS IENVNAME,G.IRESNAME AS IRESNAME,G.IID AS IRESID ");
        buf.append(" FROM IEAI_SUS_BSYS_ENV_RES A,IEAI_RESOURCE_GROUP G ,IEAI_GROUP_ENV E ");
        buf.append(" WHERE A.IEAI_RESOURCE_GROUP =G.IID    AND E.IID =A.IEAI_GROUP_ENV )A, ");
        buf.append(" (SELECT B.IGROUPID,BR.IID ,BR.IIP,BR.IPORT,BR.IPRIORITY,BR.ITYPE,BR.ISYSTEMTYPE,BR.IMODEL_TYPE,BR.IHOSTNAME,BR.IAZNAME,BR.IAZNETID,BR.IPROXYIP  FROM IEAI_GROUP_BUSINESS B, IEAI_RESOURCE_BUSINESS BR    WHERE B.IBUSINESSID=BR.IID) B ");
        buf.append(" ,(SELECT S.IPROJECTID FROM IEAI_INSTANCE_VERSION S WHERE S.IID=?  ORDER BY S.IID DESC ) d ");
        buf.append(" WHERE A.IRESID =B.IGROUPID AND A.IEAI_SUS_BUSNES_SYS=D.IPROJECTID       ");
        buf.append(" GROUP BY IENVNAME,IRESNAME,IRESID,IMODEL_TYPE,IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IENVID,IAZNAME,IAZNETID,IPROXYIP ");
        buf.append(" ORDER by IENVNAME,IRESNAME ");

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(buf.toString());
            ps.setLong(1, instanceId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                String curResName = rs.getString("IRESNAME");
                String curEnvName = rs.getString("IENVNAME");
                // 因为是从新资源组关系中获取服务器，map的key值为 环境名||资源组名
                String keyString = curEnvName + "||" + curResName;
                if (null == resultMap.get(keyString))
                {
                    list = new ArrayList<ResourceServerDTO>();
                    resultMap.put(keyString, list);
                }
                ResourceServerDTO resourceServer = new ResourceServerDTO();
                resourceServer.setIresid(rs.getString("IRESID"));
                resourceServer.setIresname(rs.getString("IRESNAME"));
                resourceServer.setImodeltype(rs.getString("IMODEL_TYPE"));
                resourceServer.setId(rs.getLong("IID"));
                resourceServer.setHostName(rs.getString("IHOSTNAME"));
                resourceServer.setIp(rs.getString("IIP"));
                resourceServer.setPort(rs.getInt("IPORT"));
                resourceServer.setPriority(rs.getInt("IPRIORITY"));
                resourceServer.setType(rs.getInt("ITYPE"));
                resourceServer.setSystemType(rs.getString("ISYSTEMTYPE"));
                resourceServer.setIenvname(curEnvName);
                resourceServer.setIenvid(rs.getString("IENVID"));
                resourceServer.setIazName(rs.getString("IAZNAME"));
                resourceServer.setIazNetId(rs.getString("IAZNETID"));
                resourceServer.setIproxyIp(rs.getString("IPROXYIP"));
                list.add(resourceServer);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return resultMap;
    }
    
    /**
     * <li>Description:查询公共虚拟环境下的 资源组服务器</li> 
     * <AUTHOR>
     * 2020年3月31日 
     * @param con
     * @return
     * @throws SQLException
     * @throws RepositoryException
     * return Map<String,List<ResourceServerDTO>>
     */
    public static Map<String, List<ResourceServerDTO>> getPubSysEnvResHost ( Connection con, String sysName )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceServerDTO>> resultMap = new LinkedHashMap<String, List<ResourceServerDTO>>();
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append(" ");
        buf.append("  SELECT * FROM ( ");
        buf.append("  select P.IID AS IBUSYS_IID,P.INAME AS BUSYSNAME ,GEI.IENVID ");
        buf.append("  from(                ");
        buf.append("          SELECT IID ,INAME FROM IEAI_PROJECT ");
        buf.append("  WHERE IID IN (SELECT MAX(IID)  ");
        buf.append("                                    FROM IEAI_PROJECT  ");
        buf.append("                                   WHERE ");
        buf.append("                                        IID IS NOT NULL AND ISVALIDATE=1 AND IGROUPID=3 AND INAME !='所有变更管理业务系统' AND UPPER(INAME) !='EXCELACTEXECMODELSUS'  and INAME=? "); 
        buf.append("                                   GROUP BY INAME)  ");
        buf.append("                  ORDER BY    INAME ASC ");
        buf.append("       ) P , ");
        buf.append("       IEAI_GROUP_ENV_IBUSYS GEI ");
        buf.append("   WHERE P.IID=GEI.IBUSYS_IID ");
        buf.append("  )A, ");
        buf.append("  (SELECT           DISTINCT ");       
        buf.append("                    ER.IEAI_SUS_BUSNES_SYS AS PROJECT_IID, ");
        buf.append("                   E.IID               AS EIID, ");
        buf.append("                    E.INAME            AS ENAME, ");
        buf.append("                    R.GROUPID , ");
        buf.append("                    R.GROUPNAME         , ");
        buf.append("    R.SERVERIID, ");
        buf.append("           R.IHOSTNAME         , ");
        buf.append("            R.IIP, ");
        buf.append("            R.IPORT, ");
        buf.append("            R.ISYSTEMTYPE, ");
        buf.append("             R. IMODEL_TYPE ");
        buf.append("       FROM IEAI_GROUP_ENV E, ");
        buf.append("            IEAI_SUS_BSYS_ENV_RES ER, ");
        buf.append("            (SELECT AA.IID       SERVERIID, ");
        buf.append("                     IHOSTNAME, ");
        buf.append("                     IIP, ");
        buf.append("                     IPORT, ");
        buf.append("                      IPRIORITY, ");
        buf.append("                      ITYPE, ");
        buf.append("                       ISYSTEMTYPE, ");
        buf.append("                       IMODEL_TYPE, ");
        buf.append("                       BB.IID       ISIID, ");
        buf.append("                       BB.GROUPID, ");
        buf.append("                  BB.GROUPNAME ");
        buf.append("              FROM (SELECT IID, ");
        buf.append("                          IHOSTNAME, ");
        buf.append("                          IIP, ");
        buf.append("                          IPORT, ");
        buf.append("                          IPRIORITY, ");
        buf.append("                          ITYPE, ");
        buf.append("                         IMODEL_TYPE, ");
        buf.append("                         ISYSTEMTYPE ");
        buf.append("                   FROM IEAI_RESOURCE_BUSINESS ");
        buf.append("                     ORDER BY IIP) AA ");
        buf.append("               JOIN (SELECT B.IID, C.IID GROUPID, C.IRESNAME GROUPNAME ");
        buf.append("                       FROM IEAI_GROUP_BUSINESS    A, ");
        buf.append("                            IEAI_RESOURCE_BUSINESS B, ");
        buf.append("                           IEAI_RESOURCE_GROUP    C ");
        buf.append("                     WHERE A.IGROUPID = C.IID ");
        buf.append("                        AND A.IBUSINESSID = B.IID) BB ");
        buf.append("                  ON AA.IID = BB.IID) R ");
        buf.append("        WHERE ER.IEAI_GROUP_ENV = E.IID ");
        buf.append("          AND ER.IEAI_RESOURCE_GROUP = R.GROUPID)B ");
        buf.append("  WHERE A.IBUSYS_IID=PROJECT_IID AND A.IENVID=B.EIID ");

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(buf.toString());
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                String curResName = rs.getString("GROUPNAME");              //ok
                String curEnvName = rs.getString("ENAME");                  //ok
                // 因为是从新资源组关系中获取服务器，map的key值为 环境名||资源组名
                String keyString = curEnvName + "||" + curResName;
                if (null == resultMap.get(keyString))
                {
                    list = new ArrayList<ResourceServerDTO>();
                    resultMap.put(keyString, list);
                }
                ResourceServerDTO resourceServer = new ResourceServerDTO();
                resourceServer.setIresid(rs.getString("GROUPID"));          //ok
                resourceServer.setIresname(rs.getString("GROUPNAME"));      //ok
                resourceServer.setImodeltype(rs.getString("IMODEL_TYPE"));  //ok
                resourceServer.setId(rs.getLong("SERVERIID"));              //ok           
                resourceServer.setHostName(rs.getString("IHOSTNAME"));      //ok
                resourceServer.setIp(rs.getString("IIP"));                  //ok
                resourceServer.setPort(rs.getInt("IPORT"));                 //ok
                resourceServer.setSystemType(rs.getString("ISYSTEMTYPE"));  //ok
                resourceServer.setIenvname(curEnvName);                     //ok
                resourceServer.setIenvid(rs.getString("EIID"));           //
                list.add(resourceServer);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return resultMap;
    }
    
    /**
     * <li>Description:查询公共虚拟环境下的 资源组服务器</li> 
     * <AUTHOR>
     * 2020年3月31日 
     * @param con
     * @return
     * @throws SQLException
     * @throws RepositoryException
     * return Map<String,List<ResourceServerDTO>>
     */
    public static Map<String, List<ResourceServerDTO>> getPubSysEnvResHostLong ( Connection con, String sysName )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceServerDTO>> resultMap = new LinkedHashMap<String, List<ResourceServerDTO>>();
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        String sqlString="    SELECT F1.*,F2.*\r\n" + 
                "  FROM(\r\n" + 
                "             SELECT *\r\n" + 
                "               FROM \r\n" + 
                "                        (select P.IID AS IBUSYS_IID, P.INAME AS BUSYSNAME, GEI.IENVID\r\n" + 
                "                           from (SELECT IID, INAME\r\n" + 
                "                                   FROM IEAI_PROJECT\r\n" + 
                "                                  WHERE IID IN\r\n" + 
                "                                        (SELECT MAX(IID)\r\n" + 
                "                                           FROM IEAI_PROJECT\r\n" + 
                "                                          WHERE IID IS NOT NULL\r\n" + 
                "                                            AND ISVALIDATE = 1\r\n" + 
                "                                            AND IGROUPID = 3\r\n" + 
                "                                            AND INAME != '所有变更管理业务系统'\r\n" + 
                "                                            AND UPPER(INAME) != 'EXCELACTEXECMODELSUS'\r\n" + 
                "                                            and INAME = ?\r\n" + 
                "                                          GROUP BY INAME)\r\n" + 
                "                                  ORDER BY INAME ASC) P,\r\n" + 
                "                                IEAI_GROUP_ENV_IBUSYS GEI\r\n" + 
                "                          WHERE P.IID = GEI.IBUSYS_IID) A,\r\n" + 
                "                          \r\n" + 
                "                          \r\n" + 
                "                        (SELECT  ER.IEAI_SUS_BUSNES_SYS AS PROJECT_IID,\r\n" + 
                "                                         E.IID AS EIID,\r\n" + 
                "                                         E.INAME AS ENAME,\r\n" + 
                "                                         G.IID AS  GID,\r\n" + 
                "                                         G.IRESNAME \r\n" + 
                "                           FROM IEAI_GROUP_ENV E,\r\n" + 
                "                                IEAI_RESOURCE_GROUP G,\r\n" + 
                "                                IEAI_SUS_BSYS_ENV_RES ER\r\n" + 
                "                          WHERE ER.IEAI_GROUP_ENV = E.IID AND ER.IEAI_RESOURCE_GROUP =G.IID\r\n" + 
                "                         ) B\r\n" + 
                "                         \r\n" + 
                "              WHERE A.IBUSYS_IID = PROJECT_IID AND A.IENVID=B.EIID\r\n" + 
                "            ORDER BY PROJECT_IID,IBUSYS_IID ,B.EIID\r\n" + 
                "  \r\n" + 
                "         ) F1, \r\n" + 
                "        \r\n" + 
                "         (SELECT GB.IGROUPID,RB.*,RB.IID AS SERVERIID FROM IEAI_GROUP_BUSINESS GB, IEAI_RESOURCE_BUSINESS RB WHERE GB.IBUSINESSID= RB.IID)F2\r\n" + 
                "         ,IEAI_AGENTINFO  AG\r\n" + 
                "         \r\n" + 
                "WHERE \r\n" + 
                "         F1.GID =F2.IGROUPID  AND AG.IAGENT_IP=F2.IIP AND AG.IAGENT_PORT =F2.IPORT\r\n" ;
                
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(sqlString);
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                //String curResName = rs.getString("GROUPNAME");        //ok
                String curEnvName = rs.getString("ENAME");              //ok
                String curResID = rs.getString("GID");              //ok
                String keyString =  curResID;
                if (null == resultMap.get(keyString))
                {
                    list = new ArrayList<ResourceServerDTO>();
                    resultMap.put(keyString, list);
                }else {
                    list =resultMap.get(keyString);
                }
                ResourceServerDTO resourceServer = new ResourceServerDTO();
                resourceServer.setIresid(rs.getString("GID"));          //ok
                resourceServer.setIresname(rs.getString("IRESNAME"));      //ok
                resourceServer.setImodeltype(rs.getString("IMODEL_TYPE"));  //ok
                resourceServer.setId(rs.getLong("SERVERIID"));              //ok           
                resourceServer.setHostName(rs.getString("IHOSTNAME"));      //ok
                resourceServer.setIp(rs.getString("IIP"));                  //ok
                resourceServer.setPort(rs.getInt("IPORT"));                 //ok
                resourceServer.setSystemType(rs.getString("ISYSTEMTYPE"));  //ok
                resourceServer.setIenvname(curEnvName);                     //ok
                resourceServer.setIenvid(rs.getString("EIID"));           //
                list.add(resourceServer);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return resultMap;
    }
    /**
     * <li>Description:查询公共虚拟环境下的 资源组服务器</li> 
     * <AUTHOR>
     * 2020年3月31日 
     * @param con
     * @return
     * @throws SQLException
     * @throws RepositoryException
     * return Map<String,List<ResourceServerDTO>>
     */
    public static Map<String, List<ResourceGroupParameterDTO>> getPubSysEnvResParamVal ( Connection con, String sysName )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceGroupParameterDTO>> resultMap = new LinkedHashMap<String, List<ResourceGroupParameterDTO>>();
        List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append(" ");
        buf.append("  SELECT A.*,B.*,PARAMETER.INAME PARAMNAME,PARAMETER.IVALUE IVALUE, VALUEP.IVALUE SERVER_VAL,PARAMETER.IPRAM_CONER FROM ( ");
        buf.append("  select P.IID AS IBUSYS_IID,P.INAME AS BUSYSNAME ,GEI.IENVID ");
        buf.append("  from(                ");
        buf.append("          SELECT IID ,INAME FROM IEAI_PROJECT ");
        buf.append("  WHERE IID IN (SELECT MAX(IID)  ");
        buf.append("                                    FROM IEAI_PROJECT  ");
        buf.append("                                   WHERE ");
        buf.append("                                        IID IS NOT NULL AND ISVALIDATE=1 AND IGROUPID=3 AND INAME !='所有变更管理业务系统' AND UPPER(INAME) !='EXCELACTEXECMODELSUS'  and INAME=? "); 
        buf.append("                                   GROUP BY INAME)  ");
        buf.append("                  ORDER BY    INAME ASC ");
        buf.append("       ) P , ");
        buf.append("       IEAI_GROUP_ENV_IBUSYS GEI ");
        buf.append("   WHERE P.IID=GEI.IBUSYS_IID ");
        buf.append("  )A, ");
        buf.append("  (SELECT           DISTINCT ");       
        buf.append("                    ER.IEAI_SUS_BUSNES_SYS AS PROJECT_IID, ");
        buf.append("                   E.IID               AS EIID, ");
        buf.append("                    E.INAME            AS ENAME, ");
        buf.append("                    R.GROUPID , ");
        buf.append("                    R.GROUPNAME         , ");
        buf.append("    R.SERVERIID, ");
        buf.append("           R.IHOSTNAME         , ");
        buf.append("            R.IIP, ");
        buf.append("            R.IPORT, ");
        buf.append("            R.ISYSTEMTYPE, ");
        buf.append("             R. IMODEL_TYPE ");
        buf.append("       FROM IEAI_GROUP_ENV E, ");
        buf.append("            IEAI_SUS_BSYS_ENV_RES ER, ");
        buf.append("            (SELECT AA.IID       SERVERIID, ");
        buf.append("                     IHOSTNAME, ");
        buf.append("                     IIP, ");
        buf.append("                     IPORT, ");
        buf.append("                      IPRIORITY, ");
        buf.append("                      ITYPE, ");
        buf.append("                       ISYSTEMTYPE, ");
        buf.append("                       IMODEL_TYPE, ");
        buf.append("                       BB.IID       ISIID, ");
        buf.append("                       BB.GROUPID, ");
        buf.append("                  BB.GROUPNAME ");
        buf.append("              FROM (SELECT IID, ");
        buf.append("                          IHOSTNAME, ");
        buf.append("                          IIP, ");
        buf.append("                          IPORT, ");
        buf.append("                          IPRIORITY, ");
        buf.append("                          ITYPE, ");
        buf.append("                         IMODEL_TYPE, ");
        buf.append("                         ISYSTEMTYPE ");
        buf.append("                   FROM IEAI_RESOURCE_BUSINESS ");
        buf.append("                     ORDER BY IIP) AA ");
        buf.append("               JOIN (SELECT B.IID, C.IID GROUPID, C.IRESNAME GROUPNAME ");
        buf.append("                       FROM IEAI_GROUP_BUSINESS    A, ");
        buf.append("                            IEAI_RESOURCE_BUSINESS B, ");
        buf.append("                           IEAI_RESOURCE_GROUP    C ");
        buf.append("                     WHERE A.IGROUPID = C.IID ");
        buf.append("                        AND A.IBUSINESSID = B.IID) BB ");
        buf.append("                  ON AA.IID = BB.IID) R ");
        buf.append("        WHERE ER.IEAI_GROUP_ENV = E.IID ");
        buf.append("          AND ER.IEAI_RESOURCE_GROUP = R.GROUPID) B ");
        buf.append(" LEFT JOIN IEAI_RESGROUP_EXTENDSPARAMETER PARAMETER ON PARAMETER.IRESGROUPID=B.GROUPID");
        buf.append(" LEFT JOIN IEAI_RESGROUP_EXTENDS_VALUE VALUEP ON VALUEP.IPARAMETERID=PARAMETER.IID AND VALUEP.ISERVERID=B.SERVERIID        ");
        buf.append("  WHERE A.IBUSYS_IID=PROJECT_IID AND A.IENVID=B.EIID ");

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(buf.toString());
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            
            ResourceGroupParameterDTO resourceGroupParameter=null;
            while (rs.next())
            {
                String curResName = rs.getString("GROUPNAME");              //ok
                String curEnvName = rs.getString("ENAME");                  //ok
                // 因为是从新资源组关系中获取服务器，map的key值为 环境名||资源组名
                String keyString = curEnvName + "||" + curResName;
                if (null == resultMap.get(keyString))
                {
                    list = new ArrayList<ResourceGroupParameterDTO>();
                    resultMap.put(keyString, list);
                }else{
                    list = resultMap.get(keyString);
                }
                resourceGroupParameter = new ResourceGroupParameterDTO();
                resourceGroupParameter.setResName(rs.getString("GROUPNAME"));
                resourceGroupParameter.setIpram_coner(rs.getString("IPRAM_CONER"));
                resourceGroupParameter.setName(rs.getString("PARAMNAME"));
                resourceGroupParameter.setGroupId(rs.getLong("GROUPID"));
                resourceGroupParameter.setServerId(rs.getLong("SERVERIID"));
                resourceGroupParameter.setOriginalValue(null == rs.getString("IVALUE") ? "" : rs
                        .getString("IVALUE"));
                resourceGroupParameter.setValue(null == rs.getString("SERVER_VAL") ? "" : rs
                        .getString("SERVER_VAL"));
                list.add(resourceGroupParameter);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return resultMap;
    }
    /**
     * 
     * @Title: getServersByStepId_SUSLoop   
     * @Description: 根据步骤id获取该步骤下所有服务器（新资源组关系） --循环重试  
     * @param istepid
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2017年11月17日 上午9:47:13
     */
    public List<ResourceServerDTO> getServersByStepId_SUSLoop ( Long istepid, int type ) throws RepositoryException
    {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        for (int i = 0;; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), _log,
                        type);
                    list = this.getServersByStepId_SUS(con, istepid);
                    break;
                } catch (SQLException e)
                {
                    _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }

    /**
     * 
     * @Title: getServersByStepId_SUS   
     * @Description: 根据步骤id获取该步骤下所有服务器（新资源组关系）  
     * @param con
     * @param istepid
     * @return
     * @throws SQLException      
     * @author: yunpeng_zhang 
     * @throws RepositoryException 
     * @date:   2017年11月17日 上午9:46:15
     */
    public List<ResourceServerDTO> getServersByStepId_SUS ( Connection con, Long istepid )
            throws SQLException, RepositoryException
    {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append("SELECT IENV.INAME,IGROUP.IRESNAME,IBUS.* FROM IEAI_SUS_RESGROUP RES1 \n"
                + "LEFT JOIN IEAI_GROUP_BUSINESS GB1 ON GB1.IGROUPID=RES1.IGROUPID\n"
                + "LEFT JOIN IEAI_RESOURCE_BUSINESS IBUS ON IBUS.IID=GB1.IBUSINESSID\n"
                + "LEFT JOIN  IEAI_RESOURCE_GROUP IGROUP ON IGROUP.IID=RES1.IGROUPID\n"
                + " LEFT JOIN IEAI_GROUP_ENV IENV  ON RES1.IENVID=IENV.IID\n" + "WHERE  RES1.ISTEPID=" + istepid
                + " ORDER BY IENV.INAME,IGROUP.IRESNAME,IBUS.IIP");

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(buf.toString());
            rs = ps.executeQuery();
            while (rs.next())
            {
                ResourceServerDTO resourceServer = new ResourceServerDTO();
                // resourceServer.setId(rs.getLong("IID"));
                resourceServer.setHostName(rs.getString("IHOSTNAME"));
                resourceServer.setIp(rs.getString("IIP"));
                resourceServer.setPort(rs.getInt("IPORT"));
                resourceServer.setPriority(rs.getInt("IPRIORITY"));
                resourceServer.setType(rs.getInt("ITYPE"));
                resourceServer.setSystemType(rs.getString("ISYSTEMTYPE"));
                resourceServer.setIenvname(rs.getString("INAME"));
                resourceServer.setIresname(rs.getString("IRESNAME"));
                resourceServer.setImodeltype(rs.getString("IMODEL_TYPE"));
                list.add(resourceServer);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return list;
    }
    
    
    /**
     * <li>Description:</li> 
     * <AUTHOR>
     * 2018年12月13日 
     * @param type
     * @param curServer
     * @return
     * @throws RepositoryException
     * return ResourceServerDTO
     */
    public static String  getServerByIid(int type,String curServer) throws RepositoryException {
        String sql = "SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODEL_TYPE FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IHOSTNAME";
        if (null!=curServer && !"".equals(curServer) && !"null".equals(curServer)) { 
            sql = "SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODEL_TYPE FROM IEAI_RESOURCE_BUSINESS WHERE  IID=? ORDER BY IHOSTNAME";
        }
        String modleType="";
        ResourceServerDTO resourceServer = null;
        for (int i = 0;; i++) {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getServersByType", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, curServer);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        modleType=actRS.getString("IMODEL_TYPE");
/*                        resourceServer = new ResourceServerDTO();
                        resourceServer.setId(actRS.getLong("IID"));
                        resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                        resourceServer.setIp(actRS.getString("IIP"));
                        resourceServer.setPort(actRS.getInt("IPORT"));
                        resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                        resourceServer.setType(actRS.getInt("ITYPE"));
                        resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        resourceServer.setImodeltype(actRS.getString("IMODEL_TYPE"));*/
                    }
                } catch (SQLException e) {
                    _log.error("getServersByType is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getServersByType",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getServersByType");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBRetryUtil.waitForNextTry(i, ex);
//                DBResource
//                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return modleType;
    }
    
    /**
     * 
     * <li>Description:获取所有主机名称</li> 
     * <AUTHOR>
     * 2019年5月22日 
     * @return
     * @throws RepositoryException
     * return List<ResourceServerDTO>
     */
    public List<ResourceServerDTO> getAllComName( Long ibusSysId ) throws RepositoryException {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        String sql = " select * from(select distinct(icom_name) from  ieai_agentinfo) q order by lower(icom_name) ";
        String sysBindingComputerFlag = Environment.getInstance().getSysConfig(
            Environment.SYS_BINDING_COMPUTER_FLAG);
        // 判断业务系统与设备绑定的开关
        if (Boolean.parseBoolean(sysBindingComputerFlag) && null != ibusSysId && 0 !=ibusSysId )
        {
            sql = "SELECT DISTINCT(N.NAME3) AS ICOM_NAME FROM (SELECT T2.IAGENT_IP AS NAME2, T2.IAGENT_PORT AS IID2,T2.ICOM_NAME AS NAME3\n"
                    + "  FROM IEAI_SYS_RELATION T, IEAI_COMPUTER_LIST T1, IEAI_AGENTINFO T2\n"
                    + " WHERE T.COMPUTERID = T1.CPID\n" + "   AND T1.IAGENTINFO_ID = T2.IAGENTINFO_ID\n"
                    + "   AND T.SYSTEMID = ? ) N ";
        }
        ResourceServerDTO resourceServer = null;
       for (int i = 0;; i++)
       {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try {
                    con = DBResource.getConnection("getAllComName", _log,Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    if (Boolean.parseBoolean(sysBindingComputerFlag) && null != ibusSysId && 0 !=ibusSysId )
                    {
                        actStat.setLong(1, ibusSysId);
                    }
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceServer = new ResourceServerDTO();
                        String comName = actRS.getString("ICOM_NAME");
                        if(null != comName && !"".equals(comName) && !"null".equals(comName)){
                            resourceServer.setComName(comName);
                            list.add(resourceServer);
                        }
                    }
                } catch (SQLException e) {
                    _log.error("getAllComName is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closeConn(con, actRS, actStat, "getAllComName",
                            _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getAllComName");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    public String getComName ()
    {
        return comName;
    }

    public void setComName ( String comName )
    {
        this.comName = comName;
    }
    
    /**
     * 
     * <li>Description:根据ip和端口号获取主机名</li> 
     * <AUTHOR>
     * 2019年5月23日 
     * @param conn
     * @param resourceServer
     * @return
     * return ResourceServerDTO
     */
    public ResourceServerDTO queryComNameByIpPort(Connection conn,ResourceServerDTO resourceServer){
        String method = "queryMonitorViewCombox";
        PreparedStatement ps = null;
        ResultSet rs = null;
        List list = new ArrayList();
        Map map = new HashMap();
        String sql = " SELECT ICOM_NAME FROM IEAI_AGENTINFO WHERE IAGENT_IP = ? AND IAGENT_PORT = ? ";
        
        for(int i=0,len=10;i<len;i++){
            try{
                ps = conn.prepareStatement(sql);
                ps.setString(1, resourceServer.getIp());
                ps.setLong(2, resourceServer.getPort());
                rs = ps.executeQuery();
                
                while(rs.next()){
                    resourceServer.setComName(rs.getString("ICOM_NAME"));
                }
            }catch (SQLException e)
            {
                _log.error("queryComNameByIpPort has SQLException : "+e.getMessage());
            } catch (Exception e)
            {
                _log.error("queryComNameByIpPort has Exception : "+e.getMessage());
            }finally{
                DBResource.closePSRS(rs, ps, method, _log);
            }
            if(i>=0){
                break;
            }
        }
        return resourceServer;
    }
    
    /**
     * 
     * <li>Description:查询服务器信息(应用维护系统维护导出一页纸优化用)</li> 
     * <AUTHOR>
     * 2019年8月15日 
     * @param type
     * @param groupId
     * @return
     * @throws RepositoryException
     * return List<ResourceServerDTO>
     */
    public List<ResourceServerDTO> getServersByGroupIdForAPMExport(Connection con,int type, long groupId)
            throws RepositoryException {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        String sql = "SELECT AA.IMODEL_TYPE,AA.IID IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE, BB.IID ISIID FROM (SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY ,ITYPE,ISYSTEMTYPE,IMODEL_TYPE  FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IIP ) AA JOIN (SELECT B.IID FROM IEAI_GROUP_BUSINESS A,IEAI_RESOURCE_BUSINESS B,IEAI_RESOURCE_GROUP C WHERE A.IGROUPID=C.IID AND A.IBUSINESSID=B.IID AND C.IID=? ) BB ON AA.IID=BB.IID";
        ResourceServerDTO resourceServer = null;
       for (int i = 0;; i++)
       {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try {
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actStat.setLong(2, groupId);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceServer = new ResourceServerDTO();
                        resourceServer.setId(actRS.getLong("IID"));
                        resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                        resourceServer.setIp(actRS.getString("IIP"));
                        resourceServer.setPort(actRS.getInt("IPORT"));
                        resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                        resourceServer.setType(actRS.getInt("ITYPE"));
                        resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        list.add(resourceServer);
                    }
                } catch (SQLException e) {
                    _log.error("getServersByGroupIdForAPMExport is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closePSRS(actRS, actStat, "getServersByGroupIdForAPMExport", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getServersByGroupIdForAPMExport");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBResource
                        .throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * 
     * <li>Description:查询服务器信息(应用维护系统维护一页纸导出优化用)</li> 
     * <AUTHOR>
     * 2019年8月15日 
     * @param type
     * @param curServer
     * @return
     * @throws RepositoryException
     * return List<ResourceServerDTO>
     */
    public List<ResourceServerDTO> getServersByTypeForAPMExport(Connection con,int type,String curServer) throws RepositoryException {
        List<ResourceServerDTO> list = new ArrayList<ResourceServerDTO>();
        String sql = "SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODEL_TYPE FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? ORDER BY IHOSTNAME";
        if (null!=curServer && !"".equals(curServer) && !"null".equals(curServer)) { 
            sql = "SELECT IID,IHOSTNAME,IIP,IPORT,IPRIORITY,ITYPE,ISYSTEMTYPE,IMODEL_TYPE FROM IEAI_RESOURCE_BUSINESS WHERE ITYPE=? AND IIP='"+curServer+"' ORDER BY IHOSTNAME";
        }
        if(null!=comName && !"".equals(comName) && !"null".equals(comName)){
            sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE,A.ICOM_NAME FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A  WHERE ITYPE=?  AND A.IAGENT_IP = R.IIP AND A.IAGENT_PORT = R.IPORT AND A.ICOM_NAME LIKE '"+comName+"' ORDER BY IHOSTNAME";
        }
        if(null!=curServer && !"".equals(curServer) && !"null".equals(curServer)
                && null!=comName && !"".equals(comName) && !"null".equals(comName) ){
            sql = "SELECT R.IID,R.IHOSTNAME,R.IIP,R.IPORT,R.IPRIORITY,R.ITYPE,R.ISYSTEMTYPE,R.IMODEL_TYPE,A.ICOM_NAME FROM IEAI_RESOURCE_BUSINESS R,IEAI_AGENTINFO A  WHERE ITYPE=? AND IIP='"+curServer+"' AND A.IAGENT_IP = R.IIP AND A.IAGENT_PORT = R.IPORT AND A.ICOM_NAME LIKE '"+comName+"' ORDER BY IHOSTNAME";
        }
        ResourceServerDTO resourceServer = null;
        for (int i = 0;; i++) {
            try {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try {
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, type);
                    actRS = actStat.executeQuery();
                    while (actRS.next()) {
                        resourceServer = new ResourceServerDTO();
                        resourceServer.setId(actRS.getLong("IID"));
                        resourceServer.setHostName(actRS.getString("IHOSTNAME"));
                        resourceServer.setIp(actRS.getString("IIP"));
                        resourceServer.setPort(actRS.getInt("IPORT"));
                        resourceServer.setPriority(actRS.getInt("IPRIORITY"));
                        resourceServer.setType(actRS.getInt("ITYPE"));
                        resourceServer.setSystemType(actRS.getString("ISYSTEMTYPE"));
                        resourceServer.setImodeltype(actRS.getString("IMODEL_TYPE"));
                        resourceServer = queryComNameByIpPort(con,resourceServer);
                        list.add(resourceServer);
                    }
                } catch (SQLException e) {
                    _log.error("getServersByTypeForAPMExport is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally {
                    DBResource.closePSRS(actRS, actStat, "getServersByTypeForAPMExport", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getServersByTypeForAPMExport");
                    }
                }
                break;
            } catch (RepositoryException ex) {
                DBRetryUtil.waitForNextTry(i, ex);
            }
        }
        return list;
    }
    /**
     * 
     * @Title: getDefResParams   
     * @Description: 获取所有资源组参数   
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: yunpeng_zhang 
     * @date:   2019年11月5日 下午7:35:03
     */
    public List<ResourceGroupParameterDTO> getDefResParams ( int type ) throws RepositoryException
    {
        List list = new ArrayList();
        String sql = "SELECT IID,IRESGROUPID,INAME,IPARAMTYPE,IVALUE FROM IEAI_RESGROUP_DEF_PARAM  ORDER BY INAME";
        ResourceGroupParameterDTO groupParameter = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getDefResParams", _log, Constants.IEAI_SUS);
                    actStat = con.prepareStatement(sql);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        groupParameter = new ResourceGroupParameterDTO();
                        groupParameter.setId(actRS.getLong("IID")); // 参数的ID
                        groupParameter.setName(actRS.getString("INAME")); // 参数的名称
                        groupParameter.setParamType(actRS.getString("IPARAMTYPE")); // 参数的类型
                        groupParameter.setTypeprimal(actRS.getString("IPARAMTYPE"));
                        groupParameter.setGroupId(actRS.getLong("IRESGROUPID")); // 参数的类型
                        groupParameter.setValue(null == actRS.getString("IVALUE") ? "" : actRS.getString("IVALUE"));
                        list.add(groupParameter);
                    }
                } catch (SQLException e)
                {
                    _log.error("getDefResParams is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConn(con, actRS, actStat, "getDefResParams", _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getDefResParams");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return list;
    }
    
    /**
     * 
     * <li>Description:获取应用标识AOMS和模块类型COMSERVER设备</li> 
     * <AUTHOR>
     * 2020年4月22日 
     * @param con
     * @return
     * @throws RepositoryException
     * return Set<ResourceServerDTO>
     */
    public Set<ResourceServerDTO> getSpecialIp ( Connection con ) throws RepositoryException
    {
        Set<ResourceServerDTO> set = new HashSet<ResourceServerDTO>();
        String method = "getSpecialIp";
        String sql = "SELECT distinct(IIP) AS IIP FROM IEAI_RESOURCE_BUSINESS WHERE ISYSTEMTYPE = ? AND IMODEL_TYPE = ? ORDER BY IIP ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, "AOMS");
                    actStat.setString(2, "COMSERVER");
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        ResourceServerDTO groupParameter = new ResourceServerDTO();
                        groupParameter.setIp(actRS.getString("IIP"));
                        set.add(groupParameter);
                    }
                } catch (SQLException e)
                {
                    _log.error("getSpecialIp is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(actRS, actStat, method, _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getSpecialIp");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return set;
    }
    
    /**
     * 
     * <li>Description:获取资源组名称</li> 
     * <AUTHOR>
     * 2020年5月9日 
     * @param con
     * @param groupId
     * @return
     * @throws RepositoryException
     * return String
     */
    public String getResourceName ( Connection con,long groupId ) throws RepositoryException
    {
        String resName = "";
        String method = "getResourceName";
        String sql = " SELECT IRESNAME FROM IEAI_RESOURCE_GROUP WHERE IID = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    actStat = con.prepareStatement(sql);
                    actStat.setLong(1, groupId);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        resName = actRS.getString("IRESNAME");
                    }
                } catch (SQLException e)
                {
                    _log.error("getResourceName is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(actRS, actStat, method, _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getResourceName");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return resName;
    }
    
    /**
     * 
     * <li>Description:获取设备id</li> 
     * <AUTHOR>
     * 2020年5月9日 
     * @param con
     * @param ip
     * @param isystemType
     * @param imodelType
     * @return
     * @throws RepositoryException
     * return String
     */
    public String getIpIid ( Connection con,String ip,String isystemType,String imodelType ) throws RepositoryException
    {
        String iid = "";
        String method = "getIpIid";
        String sql = " select iid,iip,isystemtype,imodel_type from IEAI_RESOURCE_BUSINESS where   iip = ? and isystemtype = ? ";
        for (int i = 0; i < 10; i++)
        {
            try
            {
                PreparedStatement actStat = null;
                ResultSet actRS = null;
                try
                {
                    actStat = con.prepareStatement(sql);
                    actStat.setString(1, ip);
                    actStat.setString(2, isystemType);
                    actRS = actStat.executeQuery();
                    while (actRS.next())
                    {
                        String modelType = actRS.getString("imodel_type");
                        String s = "ORA,ORA1";
                        String[] arr = imodelType.split(",");
                        boolean flag = false;
                        for(String r:arr){
                            if(modelType.contains(r)){
                                flag = true;
                            }
                        }
                        if(flag){
                            iid = actRS.getString("IID");
                            break;
                        }
                    }
                } catch (SQLException e)
                {
                    _log.error("getIpIid is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closePSRS(actRS, actStat, method, _log);
                    if (SystemConfig.isElapsedTime())
                    {
                        _log.info("Jdbc Connection closeConnection and method:getIpIid");
                    }
                }
                break;
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            }
        }
        return iid;
    }
    
    /**
     * 
     * <li>Description:获取环境资源组参数for：应用维护</li> 
     * <AUTHOR>
     * 2020年6月9日 
     * @param con
     * @param sysName
     * @return
     * @throws SQLException
     * @throws RepositoryException
     * return Map<String,List<ResourceGroupParameterDTO>>
     */
    public static Map<String, List<ResourceGroupParameterDTO>> getPubSysEnvResParamValForAPM ( Connection con, String sysName )
            throws SQLException, RepositoryException
    {
        Map<String, List<ResourceGroupParameterDTO>> resultMap = new LinkedHashMap<String, List<ResourceGroupParameterDTO>>();
        List<ResourceGroupParameterDTO> list = new ArrayList<ResourceGroupParameterDTO>();
        StringBuffer buf = new StringBuffer();
        buf.append(" ");
        buf.append("  SELECT A.*,B.*,PARAMETER.INAME PARAMNAME,PARAMETER.IVALUE IVALUE, VALUEP.IVALUE SERVER_VAL,PARAMETER.IPRAM_CONER FROM ( ");
        buf.append("  select P.IID AS IBUSYS_IID,P.INAME AS BUSYSNAME ,GEI.IENVID ");
        buf.append("  from(                ");
        buf.append("          SELECT IID ,INAME FROM IEAI_PROJECT ");
        buf.append("  WHERE IID IN (SELECT MAX(IID)  ");
        buf.append("                                    FROM IEAI_PROJECT  ");
        buf.append("                                   WHERE ");
        buf.append("                                        IID IS NOT NULL AND ISVALIDATE=1 AND IGROUPID=16 AND INAME !='所有变更管理业务系统' AND UPPER(INAME) !='EXCELACTEXECMODELSUS'  and INAME=? "); 
        buf.append("                                   GROUP BY INAME)  ");
        buf.append("                  ORDER BY    INAME ASC ");
        buf.append("       ) P , ");
        buf.append("       IEAI_GROUP_ENV_IBUSYS GEI ");
        buf.append("   WHERE P.IID=GEI.IBUSYS_IID ");
        buf.append("  )A, ");
        buf.append("  (SELECT           DISTINCT ");       
        buf.append("                    ER.IEAI_SUS_BUSNES_SYS AS PROJECT_IID, ");
        buf.append("                   E.IID               AS EIID, ");
        buf.append("                    E.INAME            AS ENAME, ");
        buf.append("                    R.GROUPID , ");
        buf.append("                    R.GROUPNAME         , ");
        buf.append("    R.SERVERIID, ");
        buf.append("           R.IHOSTNAME         , ");
        buf.append("            R.IIP, ");
        buf.append("            R.IPORT, ");
        buf.append("            R.ISYSTEMTYPE, ");
        buf.append("             R. IMODEL_TYPE ");
        buf.append("       FROM IEAI_GROUP_ENV E, ");
        buf.append("            IEAI_SUS_BSYS_ENV_RES ER, ");
        buf.append("            (SELECT AA.IID       SERVERIID, ");
        buf.append("                     IHOSTNAME, ");
        buf.append("                     IIP, ");
        buf.append("                     IPORT, ");
        buf.append("                      IPRIORITY, ");
        buf.append("                      ITYPE, ");
        buf.append("                       ISYSTEMTYPE, ");
        buf.append("                       IMODEL_TYPE, ");
        buf.append("                       BB.IID       ISIID, ");
        buf.append("                       BB.GROUPID, ");
        buf.append("                  BB.GROUPNAME ");
        buf.append("              FROM (SELECT IID, ");
        buf.append("                          IHOSTNAME, ");
        buf.append("                          IIP, ");
        buf.append("                          IPORT, ");
        buf.append("                          IPRIORITY, ");
        buf.append("                          ITYPE, ");
        buf.append("                         IMODEL_TYPE, ");
        buf.append("                         ISYSTEMTYPE ");
        buf.append("                   FROM IEAI_RESOURCE_BUSINESS ");
        buf.append("                     ORDER BY IIP) AA ");
        buf.append("               JOIN (SELECT B.IID, C.IID GROUPID, C.IRESNAME GROUPNAME ");
        buf.append("                       FROM IEAI_GROUP_BUSINESS    A, ");
        buf.append("                            IEAI_RESOURCE_BUSINESS B, ");
        buf.append("                           IEAI_RESOURCE_GROUP    C ");
        buf.append("                     WHERE A.IGROUPID = C.IID ");
        buf.append("                        AND A.IBUSINESSID = B.IID) BB ");
        buf.append("                  ON AA.IID = BB.IID) R ");
        buf.append("        WHERE ER.IEAI_GROUP_ENV = E.IID ");
        buf.append("          AND ER.IEAI_RESOURCE_GROUP = R.GROUPID) B ");
        buf.append(" LEFT JOIN IEAI_RESGROUP_EXTENDSPARAMETER PARAMETER ON PARAMETER.IRESGROUPID=B.GROUPID");
        buf.append(" LEFT JOIN IEAI_RESGROUP_EXTENDS_VALUE VALUEP ON VALUEP.IPARAMETERID=PARAMETER.IID AND VALUEP.ISERVERID=B.SERVERIID        ");
        buf.append("  WHERE A.IBUSYS_IID=PROJECT_IID AND A.IENVID=B.EIID ");

        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(buf.toString());
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            
            ResourceGroupParameterDTO resourceGroupParameter=null;
            while (rs.next())
            {
                String curResName = rs.getString("GROUPNAME");              //ok
                String curEnvName = rs.getString("ENAME");                  //ok
                // 因为是从新资源组关系中获取服务器，map的key值为 环境名||资源组名
                String keyString = curEnvName + "||" + curResName;
                if (null == resultMap.get(keyString))
                {
                    list = new ArrayList<ResourceGroupParameterDTO>();
                    resultMap.put(keyString, list);
                }else{
                    list = resultMap.get(keyString);
                }
                resourceGroupParameter = new ResourceGroupParameterDTO();
                resourceGroupParameter.setResName(rs.getString("GROUPNAME"));
                resourceGroupParameter.setIpram_coner(rs.getString("IPRAM_CONER"));
                resourceGroupParameter.setName(rs.getString("PARAMNAME"));
                resourceGroupParameter.setGroupId(rs.getLong("GROUPID"));
                resourceGroupParameter.setServerId(rs.getLong("SERVERIID"));
                resourceGroupParameter.setOriginalValue(null == rs.getString("IVALUE") ? "" : rs
                        .getString("IVALUE"));
                resourceGroupParameter.setValue(null == rs.getString("SERVER_VAL") ? "" : rs
                        .getString("SERVER_VAL"));
                list.add(resourceGroupParameter);
            }
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }

        return resultMap;
    }
    
    /**
     * 
     * <li>Description:根据资源组id查询环境名称</li> 
     * <AUTHOR>
     * 2020年11月10日 
     * @param resourceId
     * @return
     * return String
     */
    public String getEnvNameByGroupId ( long resourceId )
    {
        String envname = "";
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "SELECT INAME FROM IEAI_GROUP_ENV  WHERE IID = (SELECT IEAI_GROUP_ENV FROM  IEAI_SUS_BSYS_ENV_RES  WHERE IEAI_RESOURCE_GROUP = ?)";
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection("getEnvNameByGroupId", _log, Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, resourceId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                envname = rs.getString("INAME");
            }
        } catch (Exception  e)
        {
            envname = "";
            _log.error("getEnvNameByGroupId is error ! " + e.getMessage());
        } finally
        {
            DBResource.closeConn(conn, rs, ps, "getEnvNameByGroupId", _log);
        }
        return envname;
    }
    
    //主机名
    public static String comName;
    
    private static final Logger _log1 = Logger.getLogger(ResourceGroupManager.class);

    public Map queryAgentModuleTypeDown(Long systemId, int type) throws RepositoryException {
        Map<String, Object> map = new HashMap<String, Object>();
        List<ResourceServerDTO> reList = new ArrayList<ResourceServerDTO>();
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        try
        {
            con = DBResource.getConnection(methodName, _log, type);
            String sql = "SELECT DISTINCT(a.iid),a.modeltypename,b.iname FROM IEAI_VERSION_MANAGER_MT a LEFT JOIN IEAI_PROJECT b ON a.ibussname=b.iname WHERE a.iid IS NOT NULL and b.iid=?";
            ps = con.prepareStatement(sql);
            ps.setLong(1,systemId);
            rs = ps.executeQuery();
            while (rs.next())
            {
                ResourceServerDTO bean = new ResourceServerDTO();
                bean.setImodeltype(rs.getString("modeltypename"));
                bean.setId(rs.getLong("iid"));
                reList.add(bean);
            }
            map.put("dataList", reList);
        } catch (Exception e)
        {
            _log.error(methodName + "is Error." + e.getMessage(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(con, rs, ps, methodName, _log);
        }
        return map;
    }

    /**
     * 资源组设备维护查询判断是否是多个模块类型的设备
     * @param moType
     * @param modulType
     * @return
     */
    private boolean isContainsModelType(String moType,String modulType)
    {
        boolean falg = false;
        String[] parts = moType.split(",");
        for (String part : parts) {
            if(part.equals(modulType)){
                falg = true;
                return falg;
            }
        }
        return falg;
    }

    public String queryOrderInfo(String version) {
        String zpkpkginfo="";
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            String sql = "select b.ipath,b.imd5 from ieai_sus_workorder_version a,ieai_sus_workorder_info b where a.iid = b.sequential_version_id and a.iordernumber=?";
            con = DBResource.getConnection("queryOrderInfo",_log,Constants.IEAI_SUS);
            ps = con.prepareStatement(sql);
            ps.setString(1,version);
            rs = ps.executeQuery();
            while (rs.next()){
                zpkpkginfo = rs.getString("ipath")+":"+rs.getString("imd5");
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            DBResource.closeConn(con,rs,ps,"queryOrderInfo",_log);
        }
        return zpkpkginfo;
    }
}
