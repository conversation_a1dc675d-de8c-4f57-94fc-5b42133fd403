package com.ideal.ieai.server.repository.grouptemplate;

public class CloudTubeTemplateBean
{
    private long iid;
    private String iinstancename;  //名称
    private Long   iversion;      //版本号                         
    private String   iversionalias;       //单号
    private String isystype;     //系统类型
    private String itype;       //分类
    private String itype2; 
    private String itemplatetypeuuid;
    private String icreateuser;   //创建人
    private String icreatetime;    //创建时间
    private String iupdatetime;     //修改时间
    private long iisforbidden;      //是否禁用 0是 1否
    private long istatus;           //状态 0新建 1已发布
    private String ides;        //备注
    private long iisshare;          //0未共享 1已共享
    private long iuserid;
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public String getIinstancename ()
    {
        return iinstancename;
    }
    public void setIinstancename ( String iinstancename )
    {
        this.iinstancename = iinstancename;
    }
    public Long getIversion ()
    {
        return iversion;
    }
    public void setIversion ( Long iversion )
    {
        this.iversion = iversion;
    }
    public String getIversionalias ()
    {
        return iversionalias;
    }
    public void setIversionalias ( String iversionalias )
    {
        this.iversionalias = iversionalias;
    }
    public String getIsystype ()
    {
        return isystype;
    }
    public void setIsystype ( String isystype )
    {
        this.isystype = isystype;
    }
    public String getItype ()
    {
        return itype;
    }
    public void setItype ( String itype )
    {
        this.itype = itype;
    }
    public String getItype2 ()
    {
        return itype2;
    }
    public void setItype2 ( String itype2 )
    {
        this.itype2 = itype2;
    }
    
    public String getItemplatetypeuuid ()
    {
        return itemplatetypeuuid;
    }
    public void setItemplatetypeuuid ( String itemplatetypeuuid )
    {
        this.itemplatetypeuuid = itemplatetypeuuid;
    }
    public String getIcreateuser ()
    {
        return icreateuser;
    }
    public void setIcreateuser ( String icreateuser )
    {
        this.icreateuser = icreateuser;
    }
    public String getIcreatetime ()
    {
        return icreatetime;
    }
    public void setIcreatetime ( String icreatetime )
    {
        this.icreatetime = icreatetime;
    }
    public String getIupdatetime ()
    {
        return iupdatetime;
    }
    public void setIupdatetime ( String iupdatetime )
    {
        this.iupdatetime = iupdatetime;
    }
    public long getIisforbidden ()
    {
        return iisforbidden;
    }
    public void setIisforbidden ( long iisforbidden )
    {
        this.iisforbidden = iisforbidden;
    }
    public long getIstatus ()
    {
        return istatus;
    }
    public void setIstatus ( long istatus )
    {
        this.istatus = istatus;
    }
    public String getIdes ()
    {
        return ides;
    }
    public void setIdes ( String ides )
    {
        this.ides = ides;
    }
    public long getIisshare ()
    {
        return iisshare;
    }
    public void setIisshare ( long iisshare )
    {
        this.iisshare = iisshare;
    }
    public long getIuserid ()
    {
        return iuserid;
    }
    public void setIuserid ( long iuserid )
    {
        this.iuserid = iuserid;
    }       
}
