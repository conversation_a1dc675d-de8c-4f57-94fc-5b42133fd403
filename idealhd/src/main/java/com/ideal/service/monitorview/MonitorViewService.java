package com.ideal.service.monitorview;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.monitorview.MonitorViewBean;
import com.ideal.ieai.server.repository.monitorview.MonitorViewManager;

import net.sf.json.JSONArray;
/**
 * 
 * <ul>
 * <li>Title: MonitorViewService.java</li>
 * <li>Description:拓扑监控系统服务层</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2019年2月20日
 */
public class MonitorViewService
{

    private static MonitorViewService instance = null;
    
    private Logger log = Logger.getLogger(MonitorViewService.class);
    
    private MonitorViewService(){
        
    }
    
    public static MonitorViewService getInstance(){
        if(null == instance){
            instance = new MonitorViewService();
        }
        return instance;
    }
    
    /**
     * 
     * <li>Description:监控视图下拉框查询</li> 
     * <AUTHOR>
     * 2019年2月16日 
     * @param type
     * @return
     * return Map
     */
    public Map queryMonitorViewCombox(int type){
        return MonitorViewManager.getInstance().queryMonitorViewCombox(type);
    }
    
    /**
     * 
     * <li>Description:判断监控视图名称是否存在</li> 
     * <AUTHOR>
     * 2019年2月20日 
     * @param bean
     * @param type
     * @return
     * return boolean
     */
    public boolean isMonitorViewNameExist(MonitorViewBean bean,int type){
        return MonitorViewManager.getInstance().isMonitorViewNameExist(bean, type);
    }
    
    /**
     * 
     * <li>Description:监控视图信息查询</li> 
     * <AUTHOR>
     * 2019年2月16日 
     * @param monitorViewName
     * @param type
     * @return
     * return Map
     */
    public Map queryMonitorViewInfo(String monitorViewName,int start,int limit,int type){
        return MonitorViewManager.getInstance().queryMonitorViewInfo(monitorViewName, start, limit, type);
    }
    
    /**
     * 
     * <li>Description:保存新建监控视图信息</li> 
     * <AUTHOR>
     * 2019年2月16日 
     * @param monitorViewName
     * @param monitorViewDesc
     * @param userName
     * @param type
     * @return
     * return boolean
     */
    public boolean saveMonitorViewInfo(MonitorViewBean bean,int type){
        return MonitorViewManager.getInstance().saveMonitorViewInfo(bean, type);
    }
    
    /**
     * 
     * <li>Description:通过id查询监控视图信息</li> 
     * <AUTHOR>
     * 2019年2月16日 
     * @param id
     * @param type
     * @return
     * return Map
     */
    public Map queryMonitorViewById(String id,int type){
        return MonitorViewManager.getInstance().queryMonitorViewById(id, type);
    }
    
    /**
     * 
     * <li>Description:修改监控视图信息</li> 
     * <AUTHOR>
     * 2019年2月16日 
     * @param bean
     * @param type
     * @return
     * return boolean
     */
    public boolean updateMonitorViewInfo(MonitorViewBean bean,int type){
        return MonitorViewManager.getInstance().updateMonitorViewInfo(bean, type);
    }
    
    /**
     * 
     * <li>Description:删除监控视图</li> 
     * <AUTHOR>
     * 2019年2月16日 
     * @param conn
     * @param iid
     * @return
     * @throws SQLException
     * return boolean
     */
    public boolean deleteMonitorView(String ids,int type){
        String method = "deleteMonitorView";
        boolean flag = false;
        Connection conn = null;
        JSONArray jsArray = JSONArray.fromObject(ids);
        try
        {
            conn = DBResource.getConnection(method, log, type);
            for(int i = 0,len = jsArray.size(); i < len; i++){
                String iid = String.valueOf(jsArray.get(i));
                MonitorViewManager.getInstance().deleteActRelationByViewId(conn,iid);
                MonitorViewManager.getInstance().deleteMonitorTaskByViewId(conn,iid);
                MonitorViewManager.getInstance().deleteMonitorViewTaskRelation(conn,iid);
                MonitorViewManager.getInstance().deleteMonitorView(conn, iid);
            }
            conn.commit();
            flag = true;
        } catch (SQLException se){
            log.error("deleteMonitorView at delete method is error : "+se.getMessage());
            try
            {
                DBResource.rollback(conn, type, se, method, log);
            } catch (RepositoryException e)
            {
                log.error("deleteMonitorView at rollback is error : "+e.getMessage());
            }
        } catch (RepositoryException e)
        {
            log.error("deleteMonitorView has a RepositoryException : "+e.getMessage());
        } finally{
            if(null != conn){
                DBResource.closeConnection(conn, method, log);
            }
        }
        return flag;
    
    }
    
    /**
     * 
     * <li>Description:监控任务下拉框查询</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param type
     * @return
     * return Map
     */
    public Map queryMonitorTaskCombox(String monitorViewId,int type){
        return MonitorViewManager.getInstance().queryMonitorTaskCombox(monitorViewId,type);
    }
    
    /**
     * 
     * <li>Description:查询监控视图下监控任务信息</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param bean
     * @param start
     * @param limit
     * @param type
     * @return
     * return Map
     */
    public Map queryMonitorTaskInfo(MonitorViewBean bean,int start,int limit,int type){
        return MonitorViewManager.getInstance().queryMonitorTaskInfo(bean, start, limit, type);
    }
    
    /**
     * 
     * <li>Description:判断监控任务名是否存在</li> 
     * <AUTHOR>
     * 2019年2月20日 
     * @param monitorViewName
     * @param type
     * @return
     * return int
     */
    public boolean isMonitorTaskNameExist(String monitorViewName,int type){
        return MonitorViewManager.getInstance().isMonitorTaskNameExist(monitorViewName, type);
    }
    
    /**
     * 
     * <li>Description:保存新建监控任务信息</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param bean
     * @param type
     * @return
     * return boolean
     */
    public boolean saveMonitorTaskInfo(MonitorViewBean bean,int type){
        return MonitorViewManager.getInstance().saveMonitorTaskInfo(bean, type);
    }
    
    /**
     * 
     * <li>Description:通过id查询监控任务信息</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param id
     * @param type
     * @return
     * return Map
     */
    public Map queryMonitorTaskById(String id,int type){
        return MonitorViewManager.getInstance().queryMonitorTaskById(id, type);
    }
    
    /**
     * 
     * <li>Description:修改监控任务信息</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param bean
     * @param type
     * @return
     * return boolean
     */
    public boolean updateMonitorTaskInfo(MonitorViewBean bean,int type){
        return MonitorViewManager.getInstance().updateMonitorTaskInfo(bean, type);
    }
    
    /**
     * 
     * <li>Description:删除监控任务</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param ids
     * @param type
     * @return
     * return boolean
     */
    public boolean deleteMonitorTask(String ids,int type){
        String method = "deleteMonitorTask";
        boolean flag = false;
        Connection conn = null;
        JSONArray jsArray = JSONArray.fromObject(ids);
        try
        {
            conn = DBResource.getConnection(method, log, type);
            for(int i = 0,len = jsArray.size(); i < len; i++){
                String iid = String.valueOf(jsArray.get(i));
                MonitorViewManager.getInstance().deleteMonitorTask(conn, iid);
                MonitorViewManager.getInstance().deleteMonitorTaskActRelation(conn, iid);
            }
            conn.commit();
            flag = true;
        } catch (SQLException se){
            log.error("deleteMonitorTask at delete method is error : "+se.getMessage());
            try
            {
                DBResource.rollback(conn, type, se, method, log);
            } catch (RepositoryException e)
            {
                log.error("deleteMonitorTask at rollback is error : "+e.getMessage());
            }
        } catch (RepositoryException e)
        {
            log.error("deleteMonitorTask has a RepositoryException : "+e.getMessage());
        } finally{
            if(null != conn){
                DBResource.closeConnection(conn, method, log);
            }
        }
        return flag;
    }
    
    /**
     * 
     * <li>Description:查询要添加的监控活动信息</li> 
     * <AUTHOR>
     * 2019年2月18日 
     * @param MonitorViewBean
     * @param monitorTaskId
     * @param start
     * @param limit
     * @param type
     * @return
     * return Map
     */
    public Map queryAddMonitorAct(MonitorViewBean bean,int start,int limit,int type){
        return MonitorViewManager.getInstance().queryActInfoList(bean, start, limit, type);
    }
    
    /**
     * 
     * <li>Description:保存监控活动信息至监控任务下</li> 
     * <AUTHOR>
     * 2019年2月19日 
     * @param dataList
     * @param tkId
     * @param type
     * @return
     * return boolean
     */
    public boolean saveActInfo(String dataList,String tkId,int type){
        String method = "saveActInfo";
        JSONArray jsArray = JSONArray.fromObject(dataList);
        boolean flag = false;
        Connection conn = null;
        try
        {
            conn = DBResource.getConnection(method, log, type);
            for(int i = 0,len = jsArray.size(); i < len; i++){
                String actiid = String.valueOf(jsArray.get(i));
                MonitorViewManager.getInstance().saveActInfo(conn,tkId,actiid);
            }
            conn.commit();
            if(MonitorViewManager.getInstance().getNum()>0){
                flag = false;
            }else{
                flag = true;
            }
        } catch (SQLException se){
            log.error("saveActInfo at update method is error : "+se.getMessage());
            try
            {
                DBResource.rollback(conn, type, se, method, log);
            } catch (RepositoryException e)
            {
                log.error("saveActInfo at rollback is error : "+e.getMessage());
            }
        } catch (RepositoryException e)
        {
            log.error("saveActInfo has a RepositoryException : "+e.getMessage());
        } finally{
            if(null != conn){
                DBResource.closeConnection(conn, method, log);
            }
        }
        return flag;
    
    }
    
    /**
     * 
     * <li>Description:查看监控任务下监控活动</li> 
     * <AUTHOR>
     * 2019年2月19日 
     * @param MonitorViewBean
     * @param taskId
     * @param start
     * @param limit
     * @return
     * return Map
     */
    public Map showMonitorAct(MonitorViewBean bean,String taskId,int start,int limit,int type){
        return MonitorViewManager.getInstance().showActInfoList(bean,taskId, start, limit, type);
    }
    
    /**
     * 
     * <li>Description:删除监控任务下选择的监控活动</li> 
     * <AUTHOR>
     * 2019年2月19日 
     * @param jsonData
     * @param taskId
     * @param type
     * @return
     * return boolean
     */
    public boolean deleteActForMonitorTask(String jsonData,String taskId,int type){
        String method = "deleteActForMonitorTask";
        boolean flag = false;
        Connection conn = null;
        JSONArray jsArray = JSONArray.fromObject(jsonData);
        try
        {
            conn = DBResource.getConnection(method, log, type);
            for(int i = 0,len = jsArray.size(); i < len; i++){
                String iid = String.valueOf(jsArray.get(i));
                MonitorViewManager.getInstance().deleteActForMonitorTask(conn,iid,taskId);
            }
            conn.commit();
            flag = true;
        } catch (SQLException se){
            log.error("deleteActForMonitorTask at delete method is error : "+se.getMessage());
            try
            {
                DBResource.rollback(conn, type, se, method, log);
            } catch (RepositoryException e)
            {
                log.error("deleteActForMonitorTask at rollback is error : "+e.getMessage());
            }
        } catch (RepositoryException e)
        {
            log.error("deleteActForMonitorTask has a RepositoryException : "+e.getMessage());
        } finally{
            if(null != conn){
                DBResource.closeConnection(conn, method, log);
            }
        }
        return flag;
    }
    
    /**
     * 
     * <li>Description:所属系统下拉框查询</li> 
     * <AUTHOR>
     * 2019年2月22日 
     * @return
     * return Map
     */
    public Map querySystemCombox(){
        return MonitorViewManager.getInstance().querySystemCombox();
    }
    
    /**
     * 
     * <li>Description:工程名称下拉框查询</li> 
     * <AUTHOR>
     * 2019年2月22日 
     * @param prjId
     * @return
     * return Map
     */
    public Map queryPrjNameCombox(String prjId){
        return MonitorViewManager.getInstance().queryPrjNameCombox(prjId);
    }
    
    /**
     * 
     * <li>Description:工作流名称下拉框查询</li> 
     * <AUTHOR>
     * 2019年2月22日 
     * @param prjId
     * @param type
     * @return
     * return Map
     */
    public Map queryFlowNameCombox(String prjId,int type){
        return MonitorViewManager.getInstance().queryFlowNameCombox(prjId, type);
    }
}
