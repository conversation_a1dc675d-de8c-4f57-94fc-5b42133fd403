-- 8.8.0
DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=27;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (27, ''运维工单'', ''运维工单'', 27, ''images/info82.png'')';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	
	    SELECT COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=27;
	    IF	LI_EXISTS = 0 THEN
		    LS_SQL := 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (27,27,''运维工单'','''','''','''','''',0)';
			EXECUTE IMMEDIATE LS_SQL;
			COMMIT;
	    END IF;
END;
/

DECLARE
     LS_SQL VARCHAR2(4000);
     LI_EXISTS SMALLINT;
BEGIN 
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_CACHE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_CACHE(IID NUMBER(19,0) NOT NULL,ORDERID NUMBER(19,0),IORDERNAME VARCHAR2(100),UUID VARCHAR2(100),ITEMPLATEID NUMBER(19,0),ITEMPLATETYPE SMALLINT,ISTATUS SMALLINT,ICREATEUSERID NUMBER(19,0),IUPDATEUSERID  NUMBER(19,0), ICREATETIME TIMESTAMP,IUPDATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,ISERVERIP VARCHAR2(50),INSTANCENAME VARCHAR2(100), APPLYTYPE INTEGER,CONSTRAINT PK_IEAI_PAAS_ORDER_CACH PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
	     
	   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_ITSM_CACHE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_PAAS_ORDER_ITSM_CACHE(IID NUMBER(19,0) NOT NULL,IITSMNO VARCHAR2(100),IITSMDESC VARCHAR2(255),ISTATUS SMALLINT,IMESSAGE VARCHAR2(255),ICREATEUSERID VARCHAR2(255),ICREATETIME TIMESTAMP,ILASTEXECUTETIME TIMESTAMP,ISTARTTIME TIMESTAMP,CONSTRAINT IEAI_PAAS_ORDER_ITSM_CACHE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
	     
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ITSM_RELATION_CACHE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
		 LS_SQL :='CREATE TABLE IEAI_PAAS_ITSM_RELATION_CACHE(IID NUMBER(19,0) NOT NULL,ITSMID  NUMBER(19,0) ,IORDERID  NUMBER(19,0),CONSTRAINT PK_PAAS_ITSM_RELATION_CACHE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ITSM_RELATION_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
		 LS_SQL :='CREATE TABLE IEAI_PAAS_ITSM_RELATION_HIS(IID NUMBER(19,0) NOT NULL,ITSMID  NUMBER(19,0) ,IORDERID  NUMBER(19,0),CONSTRAINT PK_IEAI_PAAS_ITSM_RELATION_HIS PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_ITSM_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_ITSM_HIS(IID NUMBER(19,0) NOT NULL,IITSMNO VARCHAR2(100),IITSMDESC VARCHAR2(255),ISTATUS SMALLINT,IMESSAGE VARCHAR2(255),ICREATEUSERID VARCHAR2(255),ICREATETIME TIMESTAMP,ILASTEXECUTETIME TIMESTAMP,ISTARTTIME TIMESTAMP, CONSTRAINT PK_IEAI_PAAS_ORDER_ITSM_HIS PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_HIS(IID NUMBER(19,0) NOT NULL,ORDERID NUMBER(19,0),IORDERNAME VARCHAR2(100),UUID VARCHAR2(100),ITEMPLATEID NUMBER(19,0),ITEMPLATETYPE SMALLINT,ISTATUS SMALLINT,ICREATEUSERID NUMBER(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID NUMBER(19,0),IUPDATETIME TIMESTAMP,ISERVERIP VARCHAR2(50), INSTANCENAME VARCHAR2(100),APPLYTYPE INTEGER, CONSTRAINT PK_IEAI_PAAS_ORDER_HIS PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_AGENT_CACHE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
		 LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_AGENT_CACHE (IID NUMBER(19,0) NOT NULL,IORDERID  NUMBER(19,0),IAGENTID NUMBER(19,0),IAGENTPORT VARCHAR2(10),ICREATEUSERID NUMBER(19),ICRETATETIEM TIMESTAMP,IUPDATEUSERID NUMBER(19,0), IUPDATETIME TIMESTAMP ,CONSTRAINT PK_IEAI_PAAS_ORDER_AGENT_CACHE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_PARAMARE_CACHE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE (IID NUMBER(19,0) NOT NULL,IORDERID NUMBER(19,0),IPARAMAREKEY VARCHAR2(100),IPARAMAREVALUE VARCHAR2(255),ICREATEUSERID NUMBER(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID NUMBER(19,0), IUPDATETIME TIMESTAMP, CONSTRAINT PK_PAAS_ORDER_PARAMARE_CACHE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_PARAMARE_HIS' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_PARAMARE_HIS (IID NUMBER(19,0) NOT NULL,IORDERID NUMBER(19,0),IPARAMAREKEY VARCHAR2(100),IPARAMAREVALUE VARCHAR2(255),ICREATEUSERID NUMBER(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID NUMBER(19,0), IUPDATETIME TIMESTAMP, CONSTRAINT PK_PAAS_ORDER_PARAMARE_HIS PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_CONFIG' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_CONFIG(IID NUMBER(19,0) NOT NULL,IORDERNAME VARCHAR2(100),UUID VARCHAR2(100),ITEMPLATEID NUMBER(19,0),ITEMPLATETYPE SMALLINT,ISTATUS SMALLINT,ICREATEUSERID NUMBER(19,0),IUPDATEUSERID  NUMBER(19,0), ICREATETIME TIMESTAMP,IUPDATETIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,ISERVERIP VARCHAR2(50),APPLYTYPE INTEGER,CONSTRAINT PK_IEAI_PAAS_ORDER_CONFIG PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_PAAS_ORDER_PARAMARE_CACHE' AND OBJECT_TYPE = 'TABLE';
        IF LI_EXISTS = 0 THEN
			LS_SQL :='CREATE TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE (IID NUMBER(19,0) NOT NULL,IORDERID NUMBER(19,0),IPARAMAREKEY VARCHAR2(100),IPARAMAREVALUE VARCHAR2(255),ICREATEUSERID NUMBER(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID NUMBER(19,0), IUPDATETIME TIMESTAMP, CONSTRAINT PK_PAAS_ORDER_PARAMARE_CACHE PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
        END IF;
		
END;
/

DECLARE
	LS_SQL  VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TEMPLATE_XML' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_TEMPLATE_XML ( IID number(19,0) NOT NULL, ixml clob ,itemplateiid number(19,0) ,ISRELEASE smallint default 0, CONSTRAINT pk_ieai_template_xml PRIMARY KEY(IID) )';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TEMPLATE_PARAM_MG' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE IEAI_TEMPLATE_PARAM_MG ( 
                     IID NUMBER(19,0) NOT NULL, 
                     INAME VARCHAR2(255),      
                     IENMU SMALLINT,           
                     IPARAMNAMETYPE VARCHAR2(255), 
                     IPARAMNAMECLASS SMALLINT, 
                     IINSTANCEID NUMBER(19,0),
                     IPARAMNAME VARCHAR2(255),  
                     IVALUE VARCHAR2(255), 
                     IN_OUT SMALLINT DEFAULT 0, 
    				 DESCRIPTION VARCHAR2(255),
                     CONSTRAINT PK_IEAI_TEMPLATE_PARAM_MG PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
					 
					 
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'PAAS_ORDER_ATTACHMENT_CACHE' AND OBJECT_TYPE = 'TABLE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'CREATE TABLE PAAS_ORDER_ATTACHMENT_CACHE(IID NUMBER(19,0) NOT NULL,IORDERID NUMBER(19,0),ITEMPLATEID NUMBER(19,0),IMATETEMPID NUMBER(19,0),ISERNER NUMBER(19,0) DEFAULT -1,IFILENAME VARCHAR2(100),IFILESIZE NUMBER(19,0),IFILECONTENT BLOB,ICREATEUSERID NUMBER(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID  NUMBER(19,0),IUPDATETIME TIMESTAMP,ISCRIPTTYPE SMALLINT,CONSTRAINT PK_PAAS_ORDER_ATTACHMENT_CACHE PRIMARY KEY(IID))';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
		
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'PAAS_TEMPLATE_ITSM_RELATION' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
		 LS_SQL := 'CREATE TABLE PAAS_TEMPLATE_ITSM_RELATION(IID NUMBER(19,0) NOT NULL,IINSTANCEID NUMBER(19,0),ISTATE SMALLINT,IENVID NUMBER(19,0),IMESSAGE VARCHAR2(255),PERFORMTIME VARCHAR2(50),IITSMNO VARCHAR2(255),CONSTRAINT PK_PAAS_TEMPLATE_ITSM_RELATION PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
    END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_TEMPLATE_VERSION_MG' AND OBJECT_TYPE = 'TABLE';
    IF LI_EXISTS = 0 THEN
		 LS_SQL := 'CREATE TABLE IEAI_TEMPLATE_VERSION_MG(IID NUMBER(19,0) NOT NULL, IINSTANCENAME VARCHAR2(255) NOT NULL, IVERSION NUMBER(18,1), IVERSIONALIAS VARCHAR2(255), ISYSTYPE SMALLINT, ITYPE VARCHAR2(255),ITYPE2 VARCHAR2(255), ICREATEUSER VARCHAR2(255), ICREATETIME TIMESTAMP(6) NOT NULL,
                 IUPDATETIME TIMESTAMP(6) DEFAULT SYSTIMESTAMP, IISFORBIDDEN SMALLINT, ISTATUS SMALLINT, IDES VARCHAR2(2000), IISSHARE SMALLINT, ITEMPLATETYPEUUID VARCHAR2(255), IUSERID NUMBER(19,0) NOT NULL, IUPPERID NUMBER(19,0),IUUID VARCHAR2(255),IRELEASETIME  timestamp(6), CONSTRAINT PK_IEAI_TEMPLATE_VERSION_MG PRIMARY KEY(IID))';
            EXECUTE IMMEDIATE LS_SQL;
    END IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=16;
	IF  LI_EXISTS = 0 THEN
			LS_SQL :='INSERT INTO IEAI_SCRIPT_AUDITING (IID,INAME)  VALUES(16,''运维工单发布专审权限'')';
			EXECUTE IMMEDIATE LS_SQL;
	END  IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_WORKFLOWINSTANCE' AND COLUMN_NAME='IORDERID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERID NUMBER(19)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_WORKFLOWINSTANCE' AND COLUMN_NAME='IORDERUUID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_WORKFLOWINSTANCE  ADD IORDERUUID VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT' AND COLUMN_NAME='IMODELID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PROJECT ADD IMODELID  NUMBER(19,0)  DEFAULT -1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT_INFO_WS' AND COLUMN_NAME='IISFORBIDDEN';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PROJECT_INFO_WS  ADD IISFORBIDDEN NUMBER(19,0) DEFAULT 1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PROJECT_INFO_WS' AND COLUMN_NAME='ISTATUS';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PROJECT_INFO_WS ADD ISTATUS  NUMBER(19,0)  DEFAULT 0';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;					
/
-- 8.9.0
DECLARE
	LS_SQL  VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_CACHE' AND COLUMN_NAME='EXECTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_CACHE  ADD EXECTIME  TIMESTAMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_HIS' AND COLUMN_NAME='EXECTIME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_HIS  ADD EXECTIME  TIMESTAMP';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_CONFIG' AND COLUMN_NAME='IDESCRIBE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_CONFIG  ADD IDESCRIBE  VARCHAR2(2000)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_PARAMARE_CACHE' AND COLUMN_NAME='IPARAMARETYPE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE  ADD IPARAMARETYPE  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_PARAMARE_CACHE' AND COLUMN_NAME='IPARAMAREDESC';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE  ADD IPARAMAREDESC  VARCHAR2(500)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_CONFIG' AND COLUMN_NAME='IINSNAME';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_CONFIG  ADD IINSNAME  VARCHAR2(255)';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;					
/
--8.9.1
DECLARE LS_SQL VARCHAR2 (4000);
		LI_EXISTS SMALLINT;
BEGIN
	SELECT
		SIGN(COUNT(*)) INTO LI_EXISTS
	FROM
		OBJ
	WHERE
		OBJECT_NAME = 'IEAI_PASS_ORDER_STANDARD'
		AND OBJECT_TYPE = 'TABLE';
	IF
		LI_EXISTS = 0 THEN
			LS_SQL := 'CREATE TABLE IEAI_PASS_ORDER_STANDARD  (IID   NUMBER(19,0) NOT NULL,IORDERID   VARCHAR2(255),HCITEMID	VARCHAR2(255),ITASKPATH   VARCHAR2(255)
			,IDSTR   VARCHAR2(255)
			,RESOURCEIID   VARCHAR2(255)
			,JSONDATA   VARCHAR2(2000)
			,EXECSTRATEGY   VARCHAR2(255)
			,EXECTIME   VARCHAR2(255)
			,CORNVALUE   VARCHAR2(255)
			,SERIALNUMNAMEVALUE   VARCHAR2(255)
			,ISTATE   VARCHAR2(255)
			,UPLOADFLAG   VARCHAR2(255)
			,ROOTPATH   VARCHAR2(500)
			,JSONCRONARR   VARCHAR2(2000)
			,ISWHITE   VARCHAR2(255)
			,ISWHITEANDIP   VARCHAR2(255)
			,USERLISTJSON   VARCHAR2(2000)
			,SERVERDATA   VARCHAR2(2000)
			 ,SENDEMAILNAME   VARCHAR2(255)
			, CONSTRAINT PK_IEAI_PASS_ORDER_STANDARD PRIMARY KEY (IID)
			)';
		EXECUTE IMMEDIATE LS_SQL;

	END IF;
END;
/

-- 8.13.0
DECLARE
	LS_SQL  VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_CACHE' AND COLUMN_NAME='MONITORPERFORMSTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_CACHE  ADD MONITORPERFORMSTATE  INTEGER  default  1';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_HIS' AND COLUMN_NAME='MONITORPERFORMSTATE';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_HIS  ADD MONITORPERFORMSTATE  INTEGER   default  1 ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;					
/
-- 8.14.0
DECLARE
	LS_SQL  VARCHAR2(4000);
	LI_EXISTS SMALLINT;
BEGIN

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_ITSM_CACHE' AND COLUMN_NAME='BIZID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_ITSM_CACHE  ADD BIZID   VARCHAR2(50) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_PAAS_ORDER_ITSM_HIS' AND COLUMN_NAME='BIZID';
	IF LI_EXISTS = 0 THEN
		LS_SQL := 'ALTER TABLE IEAI_PAAS_ORDER_ITSM_HIS  ADD BIZID   VARCHAR2(50) ';
		EXECUTE IMMEDIATE LS_SQL;
	END IF;
	
END;
/