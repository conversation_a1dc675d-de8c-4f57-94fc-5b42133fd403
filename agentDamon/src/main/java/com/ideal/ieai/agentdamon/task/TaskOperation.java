package com.ideal.ieai.agentdamon.task;


import com.alibaba.fastjson.JSON;
import com.ideal.ieai.agentdamon.agentinstall.InstallUpgradeAgent;
import com.ideal.ieai.agentdamon.agentinstall.StartStopAgent;
import com.ideal.util.sm4.SM4Utils;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.util.Hashtable;

public class TaskOperation{
    private static final Logger _log = Logger.getLogger(TaskOperation.class);
    private static final String tokenStr = "ideal.info";


    public String taskcommand(String methodname, String token, String postString, Hashtable hashtable) throws Exception {
        _log.info("TaskOperation.taskcommand begin");
        String result = "";
        SM4Utils sm4 = new SM4Utils();
        sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
        if (!tokenStr.equals(sm4.decryptData_ECB(token))) {
            JSONObject reJson = new JSONObject();
            reJson.accumulate("success", false);
            reJson.accumulate("message", "token is not expected!");
            result = reJson.toString();
            return result;
        }
        //Agent监控启动和终止
        if ("startMonitor".equals(methodname)) {
            _log.info("启动Agent监控---开始");
            //启动Agent监控
            String proxyString = null != hashtable && hashtable.size() > 0 ? JSON.toJSONString(hashtable) : null;
            result = AgentMonitorRest.getInstance().excute(postString, "start", proxyString);
        } else if ("stopMonitor".equals(methodname)) {
            _log.info("关闭Agent监控程序启动");
            //关闭Agent监控
            result = AgentMonitorRest.getInstance().excute(postString, "stop", null);
        } else if ("startAgent".equals(methodname)) {
            _log.info("启动Agent----开始");
            //守护线程启动Agent
            result = AgentMonitorRest.getInstance().excuteAgent(postString, "start", null);
        }else if ("stopAgent".equals(methodname)) {
            _log.info("关闭Agent程序启动");
            //守护线程关闭Agent
            result = AgentMonitorRest.getInstance().excuteAgent(postString, "stop", null);
        }else {
            JSONObject reJson = new JSONObject();
            reJson.accumulate("success", false);
            reJson.accumulate("message", "没有找到对应的方法");
            result = reJson.toString();
        }
        return result;
    }

    /**
     * 守护线程安装部署Agent
     *
     * @param methodname 方法名
     * @param token
     * @param postString 封装sftp参数
     * @return 返回值
     * @throws Exception 异常处理
     * create_by wang_biao  2021-07-14
     */
    public String task (String methodname, String token, String postString, Hashtable hashtable) throws Exception {
        String reJson="";
        if("installAgent".equals(methodname)){
            reJson=InstallUpgradeAgent.getInstance().exect(postString,hashtable);
        }else if("stopAgent".equals(methodname)){
            reJson= StartStopAgent.getInstance().stopAgent(postString,hashtable);
        }else if("startMonitor".equals(methodname)){
            _log.info("启动Agent监控---开始");
            //启动Agent监控
            String proxyString = null != hashtable && hashtable.size() > 0 ? JSON.toJSONString(hashtable) : null;
            reJson = AgentMonitorRest.getInstance().excute(postString, "start", proxyString);
        }else if ("stopMonitor".equals(methodname)) {
            _log.info("关闭Agent监控程序启动");
            //关闭Agent监控
            reJson = AgentMonitorRest.getInstance().excute(postString, "stop", null);
        }else {
            JSONObject reJsons=new JSONObject();
            reJsons.accumulate("success", false);
            reJsons.accumulate("message", "下载文件不存在");
            reJson=reJsons.toString();
            _log.info("Agent升级安装-------没有找到对应的方法");
        }
        return reJson;
    }
    /**
     *轮询线程RPC 守护线程是否开启
     */
    public String pollingThread(Hashtable hashtable) throws Exception {
        JSONObject reJson = new JSONObject();
        reJson.accumulate("success", true);
        return reJson.toString();
    }
}
