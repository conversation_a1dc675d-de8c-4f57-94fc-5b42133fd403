
DECLARE
LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN

SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_CMDB_SYNC_DATA' AND COLUMN_NAME='ENVIRONMENT';
IF	LI_EXISTS = 0 THEN
	LS_SQL := 'ALTER TABLE IEAI_CMDB_SYNC_DATA ADD ENVIRONMENT VARCHAR2(20) ';
EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BATCH_OPERATION_RECORD' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_BATCH_OPERATION_RECORD (IID NUMBER(19) NOT NULL,EXCEL_NAME VARCHAR2(255),OPERATION_TYPE  NUMBER(10),BATCH_DIMENSION  NUMBER(10),OPERATION_STATUS NUMBER(10),IMPOR_TTIME NUMBER(19),OPERATION_USER VARCHAR2(255),CONSTRAINT PK_IEAI_BATCH_OPERATION_RECORD PRIMARY KEY (IID))';
    EXECUTE IMMEDIATE LS_SQL;
END IF;

SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM OBJ WHERE OBJECT_NAME = 'IEAI_BATCH_OPERATION_DETAILS' AND OBJECT_TYPE = 'TABLE';
IF LI_EXISTS = 0 THEN
    LS_SQL := 'CREATE TABLE IEAI_BATCH_OPERATION_DETAILS (IID NUMBER(19) NOT NULL, F_IID NUMBER(19) NOT NULL,CM_IID NUMBER(19), RN_IID NUMBER(19),ICOLLECTINFOID  NUMBER(19),IMODELID NUMBER(19),ILEVELID  NUMBER(19), SYS_NAME VARCHAR2(255),IAGENT_IP VARCHAR2(255),IAGENT_PORT  NUMBER(19),FIRST_LEVEL_NAME VARCHAR2(100),SEC_LEVEL_NAME VARCHAR2(100),MODEL_NAME VARCHAR2(100),OBJECT_NAME VARCHAR2(100),OPERATION_STATUS NUMBER(2),IS_EXCEUTED NUMBER(2),IUSERIID  NUMBER(19),IFULLNAME VARCHAR2(255),SERVERIP VARCHAR2(20),ABNORMAL_CAUSE  VARCHAR2(500),START_TIME NUMBER(19),CONSTRAINT PK_IEAI_BATCH_OPERATION_DETAIS PRIMARY KEY (IID))';
    EXECUTE IMMEDIATE LS_SQL;
END IF;

COMMIT;
END;
/
