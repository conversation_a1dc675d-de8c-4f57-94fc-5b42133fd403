@charset "utf-8";
/* CSS Document */
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td{ margin:0; padding:0;}
table{ border-collapse:collapse; border-spacing:0;}
fieldset,img{ border:0;}
address,caption,cite,code,dfn,em,strong,th,var,i{ font-style:normal; font-weight:normal;}
ol,ul{ list-style:none; padding:0;margin:0;}
caption,th{ text-align:left;}
h1,h2,h3,h4,h5,h6{ font-size:100%; font-weight:normal;}
q:before,q:after{ content:''}
abbr,acronym{ border:0;}
img {vertical-align:top;border:none 0;}
body {font-size:12px; text-align:left;}
html{ text-align:center;}
a,area { blr:expression(this.onFocus=this.blur()) } /* for IE7.0及以下版本*/ 
a:focus { outline-style: none; } /* for Firefox，IE8.0等 */
.clear {clear: both;font-size:0;width:0;height:0; visibility: hidden;}
