package com.ideal.ieai.studio.util.expression;

import java.util.ArrayList;
import java.util.List;

import com.ideal.ieai.core.exprparser.IEAIAllocationExpression;
import com.ideal.ieai.core.exprparser.IEAIAmbiguousName;
import com.ideal.ieai.core.exprparser.IEAIArguments;
import com.ideal.ieai.core.exprparser.IEAIArrayDimensions;
import com.ideal.ieai.core.exprparser.IEAIArrayInitializer;
import com.ideal.ieai.core.exprparser.IEAIBinaryExpression;
import com.ideal.ieai.core.exprparser.IEAICastExpression;
import com.ideal.ieai.core.exprparser.IEAIExprParserVisitor;
import com.ideal.ieai.core.exprparser.IEAILiteral;
import com.ideal.ieai.core.exprparser.IEAIMethodInvocation;
import com.ideal.ieai.core.exprparser.IEAIPrimaryExpression;
import com.ideal.ieai.core.exprparser.IEAIPrimarySuffix;
import com.ideal.ieai.core.exprparser.IEAIPrimitiveType;
import com.ideal.ieai.core.exprparser.IEAITernaryExpression;
import com.ideal.ieai.core.exprparser.IEAIType;
import com.ideal.ieai.core.exprparser.IEAIUnaryExpression;
import com.ideal.ieai.core.exprparser.SimpleNode;

/**
 * <p>Title:IEAI Core </p>
 * <p>Description: The class is use to find an identifier DataSource.
 * every visit() method return a AmbiguousName identifier's name list.</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR>
 * @version 3.0
 */

public class DataSourceVisitor
    implements IEAIExprParserVisitor
{

    public DataSourceVisitor()
    {
    }

    public DataSourceVisitor(ExprParseContext Context)
    {
        _context = Context;
    }

    public Object visit(SimpleNode node, Object data)
    {
        return data;
    }

    public Object visit(IEAIArrayInitializer node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIType node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIPrimitiveType node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIAmbiguousName node, Object data)
    {
        String str = node.text;
        str = getValidName(str);
        if (!"".equals(str))
        {
            _dataList.add(str);
        }
        return _dataList;
    }
    /**
     * get the valud Name from the str,arrcording to the _context
     * @param str String
     * @param flag boolean
     * @return String
     */
    private String getValidName(String str)
    {
        boolean flag = true;
        while (flag)
        {
            if (_context.getDataType(str) != null)
            {
                return str;
            }
            int idx = str.lastIndexOf(".");
            if (idx <= 0)
            {
                flag = false;
            }
            else
            {
                str = str.substring(0, idx);
            }
        }
        return "";
    }

//    public Object visit(IEAIAssignment node, Object data)
//    {
//        node.childrenAccept(this, null);
//        return _dataList;
//    }

    public Object visit(IEAITernaryExpression node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;

    }

    public Object visit(IEAIBinaryExpression node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIUnaryExpression node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAICastExpression node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIPrimaryExpression node, Object data)
    {
//        node.childrenAccept(this, null);
//        return _dataList;
        if (node.jjtGetChild(0) instanceof IEAIAmbiguousName)
        {
            String str = ( (IEAIAmbiguousName) node.jjtGetChild(0)).text;
            str = getValidName(str);
            if ("".equals(str))
            {
                return _dataList;
            }
            StringBuffer name = new StringBuffer();
            name.append(str);
            //iterate on all primary suffixes
            for (int i = 1; i < node.jjtGetNumChildren(); i++)
            {
                String suff = doSuffix(str, (IEAIPrimarySuffix) node.jjtGetChild(i), data);
                if (null != suff)
                {
                    name.append(suff);
                    continue;
                }
                break;
            }
            _dataList.add(name.toString());
            return _dataList;
        }
        for (int i = 0; i < node.jjtGetNumChildren(); i++)
        {
            node.jjtGetChild(i).jjtAccept(this, data);
        }
        return _dataList;
    }

    public Object visit(IEAIMethodInvocation node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIPrimarySuffix node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAILiteral node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIArguments node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIAllocationExpression node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    public Object visit(IEAIArrayDimensions node, Object data)
    {
        node.childrenAccept(this, null);
        return _dataList;
    }

    /**
     * treate with primary suffix as the one field of the primary IEAIAmbiguousName
     * such as the primary "$A.B.C[i].D.E",you can get one IEAIAmbiguousName,which is
     * $A.B.C,and three primary suffix ([i],D,E).here we treat "[i]" as "",treat "D" as ".D" and
     * treat "E" as ".C".then with the IEAIAmbiguousName $A.B.C together,we got the name token
     * "$A.B.C.D.E"
     * @param PrefixVal Object
     * @param Suffix IEAIPrimarySuffix
     * @param Data Object
     * @return String
     */
    String doSuffix(Object PrefixVal, final IEAIPrimarySuffix Suffix, Object Data)
    {
        switch (Suffix.operation)
        {
            case IEAIPrimarySuffix.CLASS:
                return null;
            case IEAIPrimarySuffix.INDEX:
                return "";
            case IEAIPrimarySuffix.PROPERTY:
            case IEAIPrimarySuffix.NAME:

                //could be array.length
                //obj.field
                //obj.method(argument)
                if (Suffix.field.equals("length") && PrefixVal.getClass().isArray())
                {
                    return null;
                }

                // field access
                if (Suffix.jjtGetNumChildren() == 0)
                {
                    return ExprParseContext.PATH_SEPARATOR + Suffix.field;
                }
                else
                {
                    return null;
                }
        }
        return "";
    }

    //The context of DataSource
    private ExprParseContext _context;

    //List of all valid identifier one expression
    private List _dataList = new ArrayList();

}
