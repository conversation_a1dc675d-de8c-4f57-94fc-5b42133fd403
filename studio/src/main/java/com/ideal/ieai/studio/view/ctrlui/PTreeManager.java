package com.ideal.ieai.studio.view.ctrlui;

import java.awt.Component;
import java.awt.Frame;
import java.awt.event.ActionEvent;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.JMenu;
import javax.swing.JScrollPane;
import javax.swing.JTree;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;
import javax.swing.tree.TreeSelectionModel;

import org.apache.log4j.Logger;

import com.ideal.ieai.core.adaptor.Adaptor;
import com.ideal.ieai.core.element.AbstractElement;
import com.ideal.ieai.core.element.EnvVariant;
import com.ideal.ieai.core.element.ExternalFunction;
import com.ideal.ieai.core.element.Function;
import com.ideal.ieai.core.element.IEAISchemaDef;
import com.ideal.ieai.core.element.ImpProject;
import com.ideal.ieai.core.element.InternalFunction;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.element.Resource;
import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.studio.StudioRes;
import com.ideal.ieai.studio.core.IProjectListener;
import com.ideal.ieai.studio.core.ProjectManager;
import com.ideal.ieai.studio.util.adaptor.AdaptorManager;
import com.ideal.ieai.studio.util.adaptor.IAdaptorListener;
import com.ideal.ieai.studio.view.coordinator.StudioCoordinator;
import com.ideal.ieai.studio.view.editorui.EditorManager;

/**
 * <p>
 * Title: iEAI Studio
 * </p>
 * <p>
 * Description: The class is used to manager iEAI project tree and transfer all kinds of project
 * element' message between ProjectManager and editors.
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company: IdealInfo
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */

public class PTreeManager implements IProjectListener, IAdaptorListener
{

    private PTreeManager()
    {
    }

    public synchronized static PTreeManager getInstance ()
    {
        if (null == INSTANCE)
        {
            INSTANCE = new PTreeManager();
            ProjectManager.getInstance().addProjectListener(INSTANCE);
            AdaptorManager.getInstance().addAdaptorListener(INSTANCE);
        }
        return INSTANCE;
    }

    /**
     * Tree need changed when project's element changed.
     * 
     * @param OldElement the old schemadef
     * @param TheElement the new schemadef.
     */
    public void elementChanged ( AbstractElement OldElement, AbstractElement TheElement )
    {
        PTreeNode changedNode = getTreeNode(TheElement);
        if (null != changedNode)
        {
            DefaultTreeModel model = (DefaultTreeModel) getCurrentTree().getModel();
            changedNode.setUserObject(TheElement);
            model.nodeChanged(changedNode);
            // sort update node.
            sortNode(changedNode);
            // set select path.
            TreePath path = new TreePath(changedNode.getPath());
            getCurrentTree().getSelectionModel().setSelectionPath(path);
        }
    }

    /**
     * Tree need add a node when a project add a element
     * 
     * @param Parent the parent of the changed element
     * @param TheElement the changed element
     */

    public void elementAdded ( AbstractElement Parent, AbstractElement TheElement, boolean Undoable )
    {
        DefaultTreeModel model = (DefaultTreeModel) getCurrentTree().getModel();
        // every kind of project element has respective constant tree node.
        PTreeNode addNode = null;
        if (TheElement instanceof ImpProject)
        {
            addNode = getImpProjectRoot((ImpProject) TheElement);
            model.insertNodeInto(addNode, _improj, _improj.getChildCount());

        } else if (TheElement instanceof IEAISchemaDef)
        {
            addNode = new PTreeNode(TheElement, CtrIcon.STRICON, CtrIcon.STRICON, true);
            model.insertNodeInto(addNode, _schema, _schema.getChildCount());
        } else if (TheElement instanceof Workflow)
        {
            addNode = new PTreeNode(TheElement, CtrIcon.WORKICON, CtrIcon.WORKICON, false);
            model.insertNodeInto(addNode, _flow, _flow.getChildCount());
        } else if (TheElement instanceof EnvVariant)
        {
            addNode = new PTreeNode(TheElement, CtrIcon.ENVARICON, CtrIcon.ENVARICON, false);
            model.insertNodeInto(addNode, _envar, _envar.getChildCount());
        } else if (TheElement instanceof Function)
        {
            if (TheElement instanceof InternalFunction)
            {
                addNode = new PTreeNode(TheElement, CtrIcon.FUNCICON, CtrIcon.FUNCICON, false);
            } else if (TheElement instanceof ExternalFunction)
            {
                addNode = new PTreeNode(TheElement, CtrIcon.EXTFUNCTION, CtrIcon.EXTFUNCTION, false);
            }
            model.insertNodeInto(addNode, _fun, _fun.getChildCount());
        } else if (TheElement instanceof Resource)
        {
            addNode = new PTreeNode(TheElement, CtrIcon.RESICON, CtrIcon.RESICON, false);
            model.insertNodeInto(addNode, _res, _res.getChildCount());
        }
        // Sort current insert node.
        sortNode(addNode);
        TreePath path = new TreePath(addNode.getPath());
        if (null != path)
        {
            getCurrentTree().getSelectionModel().setSelectionPath(path);
        }
    }

    /**
     * sort updateChild.
     * 
     * @param updateChild. The node which should be sorted.
     */
    private void sortNode ( DefaultMutableTreeNode updateChild )
    {
        DefaultMutableTreeNode parentNode = (DefaultMutableTreeNode) updateChild.getParent();
        if (null == parentNode)
        {
            return;
        }
        List childUserObjectList = new ArrayList();
        int childCount = parentNode.getChildCount();
        for (int i = 0; i < childCount; i++)
        {
            childUserObjectList.add(((DefaultMutableTreeNode) parentNode.getChildAt(i))
                    .getUserObject());
        }
        AbstractElement[] elms = (AbstractElement[]) childUserObjectList
                .toArray(new AbstractElement[] {});
        Arrays.sort(elms);
        // get index of the elms.
        int index = 0;
        for (int i = 0; i < childCount; i++)
        {
            if (elms[i].equals(updateChild.getUserObject()))
            {
                index = i;
                break;
            }
        }
        DefaultTreeModel model = (DefaultTreeModel) getCurrentTree().getModel();
        // remove node from parent node and insert to new index.
        model.removeNodeFromParent(updateChild);
        model.insertNodeInto(updateChild, parentNode, index);
    }

    /**
     * Tree need remove a node when project remove a element
     * 
     * @param TheElement the element was deleted
     */
    public void elementRemoved ( AbstractElement TheElement, boolean Undoable )
    {
        PTreeNode theNode = getTreeNode(TheElement);
        if (null != theNode)
        {
            if (null != theNode.getParent())
            {
                DefaultTreeModel model = (DefaultTreeModel) getCurrentTree().getModel();
                model.removeNodeFromParent(theNode);
            }
        }
    }

    /**
     * Tree changed the corresponding focus when project changed the current editting element
     * 
     * @param OriginElement the old editting element
     * @param CurrentElement the new editting element
     */
    public void elementSelectionChanged ( AbstractElement OriginElement,
            AbstractElement CurrentElement )
    {
        if (null == OriginElement && null != CurrentElement)
        {
            // the condition means a new element is added.
            PTreeNode theNode = getTreeNode(CurrentElement);
            if (null != theNode)
            {
                TreePath path = new TreePath(theNode.getPath());
                if (null != path)
                {
                    getCurrentTree().getSelectionModel().setSelectionPath(path);
                }
            }
        } else if (null != OriginElement && null == CurrentElement)
        {
            // the condition means a element is closed.
            getCurrentTree().getSelectionModel().setSelectionPath(null);
        } else if (null == OriginElement && null == CurrentElement)
        {
            return;
        } else if (OriginElement.getID() != CurrentElement.getID())
        {
            // if two selected elelment have different ID
            PTreeNode theNode = getTreeNode(CurrentElement);
            if (null != theNode)
            {
                TreePath path = new TreePath(theNode.getPath());
                if (null != path)
                {
                    getCurrentTree().getSelectionModel().setSelectionPath(path);
                }
            }
        }
    }

    /**
     * @return return node's AbstractElement Object which is getting the foucs in tree
     */
    public AbstractElement getSelectedComponent ()
    {
        AbstractElement selectedComponent = null;
        PTreeNode selectedNode = (PTreeNode) getCurrentTree().getSelectionPath()
                .getLastPathComponent();
        if (selectedNode.getUserObject() instanceof PConstantNode)
        {
            return selectedComponent;
        }
        if (null != selectedNode)
        {
            selectedComponent = (AbstractElement) selectedNode.getUserObject();
        }
        return selectedComponent;
    }

    /**
     * Set the node of current project selected element selected in tree
     * 
     * @param TheElement the element is selected by project
     */
    public void setSelectedComponent ( Object TheElement )
    {
        if (null != TheElement)
        {
            ProjectManager manager = ProjectManager.getInstance();
            if (manager.contains(TheElement))
            {
                manager.setSelectedComponentCom((AbstractElement) TheElement);
            }
        }
    }

    /**
     * Return JScrollPane object that contain the project's tree of TheProject
     * 
     * @param TheProject the project need to create a project's tree
     * @return
     */
    public JScrollPane getTreeScrollPane ( Project TheProject )
    {
        JTree newTree = getProjectTree(TheProject);
        _treePane.setBorder(BorderFactory.createEmptyBorder());
        if (null != newTree)
        {
            _treePane.setViewportView(newTree);
        }

        StudioCoordinator.getInstance().fireSearchTreeEnable(true);

        return _treePane;
    }

    public void search ( ActionEvent e )
    {
        SearchTreeDlg dlg = new SearchTreeDlg(this.getFrame(), this.pTree);
        dlg.show();
    }

    /**
     * Update the _treePane and delete its old project's tree then add a new project's tree
     * 
     * @param TheProject a new project
     * @return updated _treePane
     */
    public JScrollPane resetTreeScrollPane ( Project TheProject )
    {
        _treePane.removeAll();
        JTree newTree = getProjectTree(TheProject);
        if (null != newTree)
        {
            _treePane.setViewportView(newTree);
        }
        return _treePane;
    }

    public void closeSelectedComponent ( AbstractElement Elm )
    {
        ProjectManager.getInstance().setSelectedComponent(Elm);
        EditorManager.getInstance().getCurSelectedPanel().tabClosed();
    }

    /**
     * Through a project element get the correspond node in tree
     * 
     * @param CurrentElement
     * @return
     */
    private PTreeNode getTreeNode ( AbstractElement CurrentElement )
    {
        if (null != CurrentElement)
        {
            PTreeNode root = (PTreeNode) getCurrentTree().getModel().getRoot();
            Enumeration e = root.breadthFirstEnumeration();
            // search all tree node to find the CurrentElement
            while (e.hasMoreElements())
            {
                PTreeNode theNode = (PTreeNode) e.nextElement();
                Object ob = theNode.getUserObject();
                if (null != theNode && ob instanceof AbstractElement)
                {
                    AbstractElement comp = (AbstractElement) ob;
                    // if two have common ID,they are the same one.
                    if (comp.getID() == CurrentElement.getID())
                    {
                        return theNode;
                    }
                }
            } // end of while
        } // end of if
        return null;
    }

    /**
     * Through the method get atree that present the current project the main project and imported
     * project need dislike icon
     * 
     * @param TheProject the current editting project
     * @param isGray judge the project is a import project or not
     * @return the tree of the project
     */

    PTree pTree = null;
    private JTree getProjectTree ( Project TheProject )
    {
        if (null != TheProject)
        {
            _root = new PTreeNode(TheProject, CtrIcon.ROOTICON, null, true);
            // add all kind of project element's root to tree.
            _improj.removeAllChildren();
            _root.add(_improj);
            _schema.removeAllChildren();
            _root.add(_schema);
            _fun.removeAllChildren();
            _root.add(_fun);
            _envar.removeAllChildren();
            _root.add(_envar);
            _res.removeAllChildren();
            _root.add(_res);
            _flow.removeAllChildren();
            _root.add(_flow);
            pTree = new PTree(_root);
            // add all import project of the project to tree
            ImpProject[] impPrjs = (ImpProject[]) TheProject.getImpProjects().toArray(
                new ImpProject[] {});
            Arrays.sort(impPrjs);
            for (int i = 0, size = impPrjs.length; i < size; i++)
            {
                ImpProject imProject = impPrjs[i];
                PTreeNode impRoot = getImpProjectRoot(imProject);
                _improj.add(impRoot);
            }
            // add all data structure to tree
            IEAISchemaDef[] schemas = (IEAISchemaDef[]) TheProject.getSchemaDefs().toArray(
                new IEAISchemaDef[] {});
            Arrays.sort(schemas);
            for (int i = 0, size = schemas.length; i < size; i++)
            {
                IEAISchemaDef schema = schemas[i];

                PTreeNode strNode = new PTreeNode(schema, CtrIcon.STRICON, CtrIcon.STRICON, true);
                _schema.add(strNode);
            }
            // add all function to tree
            Function[] funcs = (Function[]) TheProject.getFunctions().toArray(new Function[] {});
            Arrays.sort(funcs);
            for (int i = 0, size = funcs.length; i < size; i++)
            {
                Function function = funcs[i];
                PTreeNode funNode;
                if (function instanceof InternalFunction)
                {
                    funNode = new PTreeNode(function, CtrIcon.FUNCICON, CtrIcon.FUNCICON, false);

                } else
                {
                    funNode = new PTreeNode(function, CtrIcon.EXTFUNCTION, CtrIcon.EXTFUNCTION,
                            false);

                }
                _fun.add(funNode);
            }
            // add all variants to tree
            EnvVariant[] vars = (EnvVariant[]) TheProject.getVariants()
                    .toArray(new EnvVariant[] {});
            Arrays.sort(vars);
            for (int i = 0, size = vars.length; i < size; i++)
            {
                EnvVariant env = vars[i];
                PTreeNode envNode = new PTreeNode(env, CtrIcon.ENVARICON, CtrIcon.ENVARICON, false);
                _envar.add(envNode);
            }

            // add all resource to tree
            Resource[] reses = (Resource[]) TheProject.getResources().toArray(new Resource[] {});
            Arrays.sort(reses);
            for (int i = 0, size = reses.length; i < size; i++)
            {
                Resource res = reses[i];
                PTreeNode resNode = new PTreeNode(res, CtrIcon.RESICON, CtrIcon.RESICON, false);
                _res.add(resNode);
            }
            // add all workflow to tree
            Workflow[] flows = (Workflow[]) TheProject.getWorkflows().toArray(new Workflow[] {});
            // sort flows.
            Arrays.sort(flows);
            for (int i = 0, size = flows.length; i < size; i++)
            {
                Workflow flow = flows[i];
                PTreeNode workNode = new PTreeNode(flow, CtrIcon.WORKICON, CtrIcon.WORKICON, false);
                _flow.add(workNode);
            }
            //start zhangiang
            pTree.getSelectionModel().setSelectionMode (TreeSelectionModel.DISCONTIGUOUS_TREE_SELECTION);
            _popMenu.setFrame(frame);
            //end zhangliang
            _currentTree = pTree;
        }
        return pTree;
    }

    /**
     * Construct import project tree .In the tree only display its schema,function and workflow.
     * 
     * @param TheProject an import project.
     * @return import project tree.
     */
    private PTreeNode getImpProjectRoot ( ImpProject TheProject )
    {
        PTreeNode root = new PTreeNode(TheProject, CtrIcon.IMPICON, CtrIcon.IMPICON, true);
        // The root of improt project's schema .
        PTreeNode schema = new PTreeNode(new PConstantNode(DSTRS), CtrIcon.FOLDER,
                CtrIcon.OPENFOLDER, null);
        // The root of improt project's function .
        PTreeNode fun = new PTreeNode(new PConstantNode(FUNCTIONS), CtrIcon.FOLDER,
                CtrIcon.OPENFOLDER, null);

        root.add(schema);
        root.add(fun);
        // add all data structure to tree
        List schemaList = null;
        try
        {
            schemaList = TheProject.getSchemaDefs();
        } catch (FileNotFoundException ex)
        {
            _log.debug(ex.getMessage(), ex);
            schemaList = new ArrayList();
        }
        for (Iterator i = schemaList.iterator(); i.hasNext();)
        {
            IEAISchemaDef schemaDef = (IEAISchemaDef) i.next();
            if (null != schema)
            {
                PTreeNode strNode = new PTreeNode(schemaDef, CtrIcon.STRICON, null, true);
                schema.add(strNode);
            }
        }
        // add all function to tree
        java.util.List funList = null;
        try
        {
            funList = TheProject.getFunctions();
        } catch (FileNotFoundException ex1)
        {
            _log.debug(ex1.getMessage(), ex1);
            funList = new ArrayList();

        }
        for (Iterator i = funList.iterator(); i.hasNext();)
        {
            Function function = (Function) i.next();
            PTreeNode funNode = new PTreeNode(function, CtrIcon.FUNCICON, null, false);
            fun.add(funNode);
        }
        return root;
    }

    /**
     * Return the tree of the current editting project
     * 
     * @return
     */
    private PTree getCurrentTree ()
    {
        return _currentTree;
    }

    /**
     * add adaptor to popmenu _newResMenu item
     */
    public void addAdaptor ( Adaptor adp )
    {
        removeAdaptor(adp);
        JMenu menu = AdaptorManager.getInstance().getHasResAdpMenu(adp);
        if (null != menu)
        {
            _popMenu._newResMenu.add(menu);
        }
        // set item enable
        if (_popMenu._newResMenu.getMenuComponentCount() > 0)
        {
            _popMenu._newResMenu.setEnabled(true);
        }
    }

    /**
     * remove adaptor from popmenu _newResMenu item
     */
    public void removeAdaptor ( Adaptor adp )
    {
        Component[] comps = _popMenu._newResMenu.getMenuComponents();
        if (null != comps)
        {
            for (int i = 0, size = comps.length; i < size; i++)
            {
                JMenu menu = (JMenu) comps[i];
                if (menu.getText().equals(adp.getDisplayName()))
                {
                    _popMenu._newResMenu.remove(menu);
                    break;
                }
            }
        }
        if (_popMenu._newResMenu.getMenuComponentCount() == 0)
        {
            _popMenu._newResMenu.setEnabled(false);
        }
    }

    public PopMenu getPopMenu ()
    {
        return _popMenu;
    }

    // public UndoManager get_undoManager()
    // {
    // return _undoManager;
    // }

    private static PTreeManager INSTANCE     = null;

    /**
     * Below are constant tree node .
     */
    static final String         ENVARS       = StudioRes.getString("menu.environmentvar");
    static final String         FUNCTIONS    = StudioRes.getString("menu.function");
    static final String         WORKFLOWS    = StudioRes.getString("menu.workflow");
    static final String         IMPORTPRJS   = StudioRes.getString("menu.importProject");
    static final String         DSTRS        = StudioRes.getString("menu.struct");
    static final String         RES          = StudioRes.getString("menu.shareresources");

    /**
     * Current project's tree.
     */
    //start zhangliang
    static Frame frame = null;
    //end zhangliang
    
    private PTree               _currentTree = null;

    private PopMenu             _popMenu     = PopMenu.getInstance();

    /**
     * Current project tree' root node.
     */
    private PTreeNode           _root        = null;

    /**
     * The root node of import project.
     */
    private PTreeNode           _improj      = new PTreeNode(new PConstantNode(IMPORTPRJS),
                                                     CtrIcon.FOLDER, CtrIcon.OPENFOLDER, null);

    /**
     * The root of schema .
     */
    private PTreeNode           _schema      = new PTreeNode(new PConstantNode(DSTRS),
                                                     CtrIcon.FOLDER, CtrIcon.OPENFOLDER, null);

    /**
     * The root of iEAI internal function.
     */
    private PTreeNode           _fun         = new PTreeNode(new PConstantNode(FUNCTIONS),
                                                     CtrIcon.FOLDER, CtrIcon.OPENFOLDER, null);

    /**
     * The root of envariant.
     */
    private PTreeNode           _envar       = new PTreeNode(new PConstantNode(ENVARS),
                                                     CtrIcon.FOLDER, CtrIcon.OPENFOLDER, null);

    /**
     * The root of share resource.
     */
    private PTreeNode           _res         = new PTreeNode(new PConstantNode(RES),
                                                     CtrIcon.FOLDER, CtrIcon.OPENFOLDER, null);

    /**
     * The root of workflow.
     */
    private PTreeNode           _flow        = new PTreeNode(new PConstantNode(WORKFLOWS),
                                                     CtrIcon.FOLDER, CtrIcon.OPENFOLDER, null);

    /**
     * The scrollpanel that contain the project tree.
     */
    private static JScrollPane  _treePane    = new JScrollPane();
    static private final Logger _log         = Logger.getLogger(PTreeManager.class);
	public static Frame getFrame() {
		return frame;
	}

	public static void setFrame(Frame frame) {
		PTreeManager.frame = frame;
	}


    // private UndoManager _undoManager = new UndoManager();
}
