-- 8.8.0	
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE  WHERE GROUPID=27;
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_GROUPMESSAGE(GROUPID, GROUPNAME, GROUPDESCRIPTION, IORDER, IIMG)	VALUES(27, ''运维工单'', ''运维工单'', 27, ''images/info82.png'')';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$

BEGIN
DECLARE LS_SQL VARCHAR(4000);
DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IGROUPMESSGEID=27;
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES(27,27,''运维中台'','''','''','''','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ITSM_RELATION_CACHE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ITSM_RELATION_CACHE(IID DECIMAL(19,0) NOT NULL,ITSMID  DECIMAL(19,0) ,IORDERID  DECIMAL(19,0),CONSTRAINT PK_PAAS_ITSM_RELATION_CACHE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_ITSM_CACHE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_ITSM_CACHE(IID DECIMAL(19,0) NOT NULL,IITSMNO VARCHAR(100),IITSMDESC VARCHAR(255),ISTATUS SMALLINT,IMESSAGE VARCHAR(255),ICREATEUSERID VARCHAR(255),ICREATETIME TIMESTAMP,ILASTEXECUTETIME TIMESTAMP,ISTARTTIME TIMESTAMP,CONSTRAINT IEAI_PAAS_ORDER_ITSM_CACHE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_CONFIG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_CONFIG(IID DECIMAL(19,0) NOT NULL,IORDERNAME VARCHAR(100),UUID VARCHAR(100),ITEMPLATEID DECIMAL(19,0),ITEMPLATETYPE SMALLINT,ISTATUS SMALLINT,ICREATEUSERID DECIMAL(19,0),IUPDATEUSERID  DECIMAL(19,0), ICREATETIME TIMESTAMP,IUPDATETIME TIMESTAMP DEFAULT CURRENT TIMESTAMP,ISERVERIP VARCHAR(50),APPLYTYPE INTEGER,CONSTRAINT PK_IEAI_PAAS_ORDER_CONFIG PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE (IID DECIMAL(19,0) NOT NULL,IORDERID DECIMAL(19,0),IPARAMAREKEY VARCHAR(100),IPARAMAREVALUE VARCHAR(255),ICREATEUSERID DECIMAL(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID DECIMAL(19,0), IUPDATETIME TIMESTAMP, CONSTRAINT PK_PAAS_ORDER_PARAMARE_CACHE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_AGENT_CACHE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_AGENT_CACHE (IID DECIMAL(19,0) NOT NULL,IORDERID  DECIMAL(19,0),IAGENTID DECIMAL(19,0),IAGENTPORT VARCHAR(10),ICREATEUSERID DECIMAL(19),ICRETATETIEM TIMESTAMP,IUPDATEUSERID DECIMAL(19,0), IUPDATETIME TIMESTAMP ,CONSTRAINT PK_IEAI_PAAS_ORDER_AGENT_CACHE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_HIS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_HIS(IID DECIMAL(19,0) NOT NULL,ORDERID DECIMAL(19,0),IORDERNAME VARCHAR(100),UUID VARCHAR(100),ITEMPLATEID DECIMAL(19,0),ITEMPLATETYPE SMALLINT,ISTATUS SMALLINT,ICREATEUSERID DECIMAL(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID DECIMAL(19,0),IUPDATETIME TIMESTAMP,ISERVERIP VARCHAR(50), INSTANCENAME VARCHAR(100),APPLYTYPE INTEGER, CONSTRAINT PK_IEAI_PAAS_ORDER_HIS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_AGENT_HIS';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_AGENT_HIS (IID DECIMAL(19,0) NOT NULL,IORDERID  DECIMAL(19,0),IAGENTID DECIMAL(19,0),IAGENTPORT VARCHAR(10),ICREATEUSERID DECIMAL(19),ICRETATETIEM TIMESTAMP,IUPDATEUSERID DECIMAL(19,0), IUPDATETIME TIMESTAMP ,CONSTRAINT PK_PAAS_ORDER_AGENT_CACHE_HIS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
END
$

BEGIN
DECLARE LS_SQL VARCHAR(4000);
DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_ITSM_HIS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_ITSM_HIS(IID DECIMAL(19,0) NOT NULL,IITSMNO VARCHAR(100),IITSMDESC VARCHAR(255),ISTATUS SMALLINT,IMESSAGE VARCHAR(255),ICREATEUSERID VARCHAR(255),ICREATETIME TIMESTAMP,ILASTEXECUTETIME TIMESTAMP,ISTARTTIME TIMESTAMP,CONSTRAINT PK_IEAI_PAAS_ORDER_ITSM_HIS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ITSM_RELATION_HIS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ITSM_RELATION_HIS(IID DECIMAL(19,0) NOT NULL,ITSMID  DECIMAL(19,0) ,IORDERID  DECIMAL(19,0),CONSTRAINT PK_IEAI_PAAS_ITSM_RELATION_HIS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_PARAMARE_HIS';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_PARAMARE_HIS (IID DECIMAL(19,0) NOT NULL,IORDERID DECIMAL(19,0),IPARAMAREKEY VARCHAR(100),IPARAMAREVALUE VARCHAR(255),ICREATEUSERID DECIMAL(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID DECIMAL(19,0), IUPDATETIME TIMESTAMP, CONSTRAINT PK_PAAS_ORDER_PARAMARE_HIS PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='PAAS_ORDER_ATTACHMENT_CACHE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE PAAS_ORDER_ATTACHMENT_CACHE(IID DECIMAL(19,0) NOT NULL,IORDERID DECIMAL(19,0),ITEMPLATEID DECIMAL(19,0),IMATETEMPID DECIMAL(19,0),ISERNER DECIMAL(19,0) DEFAULT -1,IFILENAME VARCHAR(100),IFILESIZE DECIMAL(19,0),IFILECONTENT BLOB,ICREATEUSERID DECIMAL(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID  DECIMAL(19,0),IUPDATETIME TIMESTAMP,ISCRIPTTYPE SMALLINT,CONSTRAINT PK_PAAS_ORDER_ATTACHMENT_CACHE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='PAAS_TEMPLATE_ITSM_RELATION';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE PAAS_TEMPLATE_ITSM_RELATION(IID DECIMAL(19,0) NOT NULL,IINSTANCEID DECIMAL(19,0),ISTATE SMALLINT,IENVID DECIMAL(19,0),IMESSAGE VARCHAR(255),PERFORMTIME VARCHAR(50),IITSMNO VARCHAR(255),CONSTRAINT PK_PAAS_TEMPLATE_ITSM_RELATION PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TEMPLATE_VERSION_MG';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_TEMPLATE_VERSION_MG (IID DECIMAL(19,0) NOT NULL, IINSTANCENAME VARCHAR(255) NOT NULL, IVERSION DECIMAL(18,1), IVERSIONALIAS VARCHAR(255), ISYSTYPE SMALLINT, ITYPE VARCHAR(255), ITYPE2 VARCHAR(255), ICREATEUSER VARCHAR(255), ICREATETIME VARCHAR(255), IUPDATETIME VARCHAR(255), IISFORBIDDEN SMALLINT, ISTATUS SMALLINT, IDES VARCHAR(2000), IISSHARE SMALLINT, ITEMPLATETYPEUUID VARCHAR(255), IUSERID DECIMAL(19,0) NOT NULL, IUPPERID DECIMAL(19,0), IUUID VARCHAR(255), CONSTRAINT PK_IEAI_TEMPLATE_VERSION_MG PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=16;
		 IF	LI_EXISTS = 0 THEN
		 SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(16,''运维工单发布专审权限'')';
		 PREPARE	SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END	IF;
		 COMMIT;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME = 'IEAI_PROJECT' AND COLNAME='IMODELID';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL = 'ALTER TABLE IEAI_PROJECT ADD IMODELID  DECIMAL(19,0)  DEFAULT -1';
	PREPARE SQLA FROM LS_SQL;
	EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IORDERID' AND TABNAME='IEAI_WORKFLOWINSTANCE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERID DECIMAL(19)';
	PREPARE SQLA FROM LS_SQL;
	EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IORDERUUID' AND TABNAME='IEAI_WORKFLOWINSTANCE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='ALTER TABLE IEAI_WORKFLOWINSTANCE ADD IORDERUUID VARCHAR(255)';
	PREPARE SQLA FROM LS_SQL;
	EXECUTE SQLA;
	END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='UUID' AND TABNAME='IEAI_STANDARD_TASK';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='ALTER TABLE IEAI_STANDARD_TASK ADD UUID VARCHAR(255)';
	PREPARE SQLA FROM LS_SQL;
	EXECUTE SQLA;
	END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='UUID' AND TABNAME='IEAI_STANDARD_TASK_HISTORY';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='ALTER TABLE IEAI_STANDARD_TASK_HISTORY ADD UUID VARCHAR(255)';
	PREPARE SQLA FROM LS_SQL;
	EXECUTE SQLA;
	END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TEMPLATEUUID' AND TABNAME='IEAI_WORKFLOWINSTANCE';
	IF LI_EXISTS = 0 THEN
	SET LS_SQL ='ALTER TABLE IEAI_WORKFLOWINSTANCE ADD TEMPLATEUUID VARCHAR(255)';
	PREPARE SQLA FROM LS_SQL;
	EXECUTE SQLA;
	END IF;
		
END
$

BEGIN
DECLARE LS_SQL VARCHAR(4000);
DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAAS_ORDER_CACHE';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_PAAS_ORDER_CACHE(IID DECIMAL(19,0) NOT NULL,ORDERID DECIMAL(19,0),IORDERNAME VARCHAR(100),UUID VARCHAR(100),ITEMPLATEID DECIMAL(19,0),ITEMPLATETYPE SMALLINT,ISTATUS SMALLINT,ICREATEUSERID DECIMAL(19,0),ICREATETIME TIMESTAMP,IUPDATEUSERID DECIMAL(19,0),IUPDATETIME TIMESTAMP,ISERVERIP VARCHAR(50), INSTANCENAME VARCHAR(100),APPLYTYPE INTEGER,EXECTIME TIMESTAMP, CONSTRAINT PK_IEAI_PAAS_ORDER_CACH PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EXECTIME' AND TABNAME='IEAI_PAAS_ORDER_HIS';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PAAS_ORDER_HIS ADD EXECTIME TIMESTAMP';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DESCRIBE' AND TABNAME='IEAI_PAAS_ORDER_CONFIG';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PAAS_ORDER_CONFIG ADD DESCRIBE VARCHAR(2000)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;


	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMARETYPE' AND TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD IPARAMARETYPE VARCHAR(255)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IPARAMAREDESC' AND TABNAME='IEAI_PAAS_ORDER_PARAMARE_CACHE';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PAAS_ORDER_PARAMARE_CACHE ADD IPARAMAREDESC VARCHAR(500)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IINSNAME' AND TABNAME='IEAI_PAAS_ORDER_CONFIG';
		IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_PAAS_ORDER_CONFIG ADD IINSNAME VARCHAR(255)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
END
$