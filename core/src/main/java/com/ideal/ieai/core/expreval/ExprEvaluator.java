package com.ideal.ieai.core.expreval;

import org.apache.log4j.Logger;

import com.ideal.ieai.core.exprparser.*;

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Info</p>
 * <AUTHOR> attributable
 * @version 1.0
 */

public class ExprEvaluator
{
    private ExprEvaluator()
    {
    }

    /**
     * Evalueate value of the given sxpression under current IEAIRuntime
     * @param StrExpr String
     * @throws ExprEvalError
     * @return Object
     */
    static public Object eval(final String StrExpr, IExprContext Context) throws ExprEvalError
    {
        try
        {
            IEAIExprParser parser = new IEAIExprParser(new java.io.StringReader(StrExpr));
            parser.IEAIExpression();
            ExprEvalVisitor visitor = new ExprEvalVisitor(Context);
            return parser.popNode().jjtAccept(visitor, null);
        }
        catch (Throwable ex)
        {
            _log.info("Invalid expression: " + StrExpr, ex);
            throw new ExprEvalError("Invalid expression", ex);
        }
    }

    private static final  Logger _log = Logger.getLogger(ExprEvaluator.class);
}
