// 　　　　　　　　　　
var len = strdate.length;
// 　　　　　　　
if(len < 7 || len > 11)
{
    return "　　　　　　　　　　" + strdate;
}
// 　　　　　　　
var date = strdate.split(" ");
if(date.length !=2)
{
    return "　　　　　　　　　　" + strdate;
}
// 　　　　　　
if(date[0].indexOf("/") == -1)
{
    return "　　　　　　　　　　" + strdate;
}
if(date[1].indexOf(":") == -1)
{
    return "　　　　　　　　　　" + strdate;
}
// 　　　　　　　　С　　　　　　　
var month = date[0].substring(0,date[0].indexOf("/"));
var day = date[0].substring(date[0].indexOf("/") + 1);
var hour = date[1].substring(0,date[1].indexOf(":"));
var min = date[1].substring(date[1].indexOf(":") + 1);
// 　　　　　　　　　ж　　·　　　　　　
var regExMonth = "^(0?[1-9]|1[0-2])$";
var patMonth = java.util.regex.Pattern.compile(regExMonth);
var matMonth = patMonth.matcher(month);
// 　　　　　　　　　ж　　　　　　　　　　
var regExDay = "^((0?[1-9])|((1|2)[0-9])|30|31)$";
var patDay = java.util.regex.Pattern.compile(regExDay);
var matDay = patDay.matcher(day);
// 　　　　　　　　　ж　С　　　　　　
var regExHour = "^(0?[0-9]|((1|2|3|4|5)[0-9]))$";
var patHour = java.util.regex.Pattern.compile(regExHour);
var matHour = patHour.matcher(hour);
// 　　　　　　　　　ж　　　　　　　　　
var regExMin = "^(0?[0-9]|((1|2|3|4|5)[0-9]))$";
var patMin = java.util.regex.Pattern.compile(regExMin);
var matMin = patMin.matcher(min);
// У　　　　
if (!matMonth.find())
{
    if (!("0".equals(month) || "00".equals(month)))
    {
         return "　　　　·　　　　,　　　÷　Χ(0-12):" + month;
    }
}
// У　　　　　　
if (!matDay.find())
{
    if (!("0".equals(day) || "00".equals(day)))
    {
         return "　　　　　　　　　　　　　÷　Χ(0-31):" + day;
    }
}
// У　　С　
if (!matHour.find())
{
    return "　　　С　　　　　　　　÷　Χ(0-23):" + hour;
}
// У　　　　　
if (!matMin.find())
{
    return "　　　　　　　　　　　　　÷　Χ(0-59):" + hour;
}
return "successfull";