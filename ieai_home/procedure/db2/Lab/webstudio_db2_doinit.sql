BEGIN
	DECLARE LS_SQL VARCHAR(3600);
	DECLARE LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_INFO_WS WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_INFO_WS(IID, IPROJECTNAME, IVERSION, IREMARKS, ICREATEUSRID, IUPDATEUSERID, ICREATETI<PERSON>, IUPDATETIME, IEDITSIGN, ITYPEID)  VALUES(1, ''日常维护模板'', 1, '''', 4, 4, ''2018-07-17 04:21:08.0'', ''2018-07-18 02:24:38.0'', 0, 8)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTIONINFO WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTIONINFO(IID, INAME, IREMARKS, IRETURNTYPE, ICONTENTID)  VALUES(1, ''checkDate'', '''', ''String'', 999999999999999990)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTIONINFO WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTIONINFO(IID, INAME, IREMARKS, IRETURNTYPE, ICONTENTID)  VALUES(2, ''getDate'', '''', ''Integer'', 999999999999999991)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTIONINFO WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTIONINFO(IID, INAME, IREMARKS, IRETURNTYPE, ICONTENTID)  VALUES(3, ''getHour'', '''', ''Integer'', 999999999999999992)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTIONINFO WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTIONINFO(IID, INAME, IREMARKS, IRETURNTYPE, ICONTENTID)  VALUES(4, ''getMinute'', '''', ''Integer'', 999999999999999993)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTIONINFO WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTIONINFO(IID, INAME, IREMARKS, IRETURNTYPE, ICONTENTID)  VALUES(5, ''getMonth'', '''', ''Integer'', 999999999999999994)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTIONINFO WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTIONINFO(IID, INAME, IREMARKS, IRETURNTYPE, ICONTENTID)  VALUES(6, ''today'', '''', ''String'', 999999999999999995)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(1, 1, 1, ''checkDate'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(2, 1, 1, ''getDate'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(3, 1, 1, ''getHour'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(4, 1, 1, ''getMinute'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(5, 1, 1, ''getMonth'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(6, 1, 1, ''today'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 7;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(7, 1, 3, ''healthMain'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 8;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(8, 1, 3, ''healthCheck'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_INFO WHERE IID = 9;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_INFO(IID, IPROJECTID, ITYPEID, INAME, IREMARKS, ISAVESIGN)  VALUES(9, 1, 3, ''healthConcurrent'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;                       

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(1, 0, 1, ''1'', '''', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(2, 0, 1, ''2'', '''', 2)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
     SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(3, 0, 1, ''3'', '''', 3)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(4, 0, 1, ''4'', '''', 4)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(5, 0, 1, ''5'', '''', 5)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(6, 0, 1, ''6'', '''', 6)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 7;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(7, 0, 2, ''false'', '''', 7)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 8;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(8, 0, 3, '''', '''', 7)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 9;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(9, 0, 4, ''[{"id":"","name":"检查脚本","type":"String","readonly":false,"remarks":"填写脚本绝对路径"},{"id":"","name":"检查启动时间","type":"String","readonly":false,"remarks":"启动时间格式(MM/DD HH:mm)：00/00 00:05  代表：延时五分钟启动"},{"id":"","name":"循环次数","type":"Integer","readonly":false,"remarks":"不填写为无限循环"}]'', '''', 7)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 10;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(10, 0, 5, ''[]'', '''', 7)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 11;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(11, 0, 2, ''false'', '''', 8)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 12;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(12, 0, 3, '''', '''', 8)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 13;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(13, 0, 4, ''[{"id":"","name":"cmd","type":"String","readonly":false,"remarks":""},{"id":"","name":"agentip","type":"String","readonly":false,"remarks":""}]'', '''', 8)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 14;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(14, 0, 5, ''[]'', '''', 8)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 15;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(15, 0, 2, ''false'', '''', 9)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 16;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(16, 0, 3, '''', '''', 9)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 17;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(17, 0, 4, ''[{"id":"","name":"cmd","type":"String","readonly":false,"remarks":""}]'', '''', 9)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_VALUE WHERE IID = 18;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_VALUE(IID, IGROUPID, IPROPERTYKEYID, IPROPERTYVALUE, IREMARKS, IPROPERTYID)  VALUES(18, 0, 5, ''[]'', '''', 9)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_STUDIO_FLOW_XML WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_STUDIO_FLOW_XML(IID, FLOWID, XMLID)  VALUES(1, 8, 999999999999999996)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;     
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_STUDIO_FLOW_XML WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_STUDIO_FLOW_XML(IID, FLOWID, XMLID)  VALUES(2, 9, 999999999999999997)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;     
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_STUDIO_FLOW_XML WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_STUDIO_FLOW_XML(IID, FLOWID, XMLID)  VALUES(3, 7, 999999999999999998)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;     
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME, IREMARKS)  VALUES(1, ''funid'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME, IREMARKS)  VALUES(2, ''isusecalendar'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;    
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME, IREMARKS)  VALUES(3, ''flowcalendar'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;    
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME, IREMARKS)  VALUES(4, ''inputparameter'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;    
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_KEY_DC WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_KEY_DC(IID, IKEYNAME, IREMARKS)  VALUES(5, ''outputparameter'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;   
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(1, 2, ''InnerFunction'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(2, 3, ''EnvironmentVar'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(3, 5, ''WorkFlow'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(4, 4, ''AgentResource'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;   
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_TYPE_DC WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_TYPE_DC(IID, IGROUPID, ITYPENAME, IREMARKS)  VALUES(5, 4, ''FTPConnection'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTION_PARM WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTION_PARM(IID, IFUNCTIONID, INAME, ITYPE, IREMARKS)  VALUES(1, 1, ''strdate'', ''String'', '''')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTION_PARM WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTION_PARM(IID, IFUNCTIONID, INAME, ITYPE, IREMARKS)  VALUES(2, 2, ''strdate'', ''String'', '''')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTION_PARM WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTION_PARM(IID, IFUNCTIONID, INAME, ITYPE, IREMARKS)  VALUES(3, 3, ''strdate'', ''String'', '''')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTION_PARM WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTION_PARM(IID, IFUNCTIONID, INAME, ITYPE, IREMARKS)  VALUES(4, 4, ''strdate'', ''String'', '''')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FUNCTION_PARM WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_FUNCTION_PARM(IID, IFUNCTIONID, INAME, ITYPE, IREMARKS)  VALUES(5, 5, ''strdate'', ''String'', '''')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_PROJECT_INFO_WS';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_PROJECT_INFO_WS'', 1)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_FUNCTIONINFO';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_FUNCTIONINFO'', 6)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_PROJECT_PROPERTY_INFO';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_PROJECT_PROPERTY_INFO'', 9)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_PROJECT_PROPERTY_VALUE';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_PROJECT_PROPERTY_VALUE'', 18)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_STUDIO_FLOW_XML';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_STUDIO_FLOW_XML'', 3)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_PROJECT_PROPERTY_KEY_DC';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_PROJECT_PROPERTY_KEY_DC'', 5)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_PROJECT_PROPERTY_TYPE_DC';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_PROJECT_PROPERTY_TYPE_DC'', 5)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'IEAI_FUNCTION_PARM';
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME, IVALUE)  VALUES(''IEAI_FUNCTION_PARM'', 5)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$

BEGIN
    DECLARE LS_SQL     VARCHAR(2000);
    DECLARE LI_EXISTS  NUMERIC(2);

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (1, ''IEAI_IEAI'', ''作业调度'', ''1,2,3,4,5'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (2, ''IEAI_INFOCOLLECTION'', ''信息采集'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (3, ''IEAI_SUS'', ''变更管理'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (4, ''IEAI_EMERGENCY_SWITCH'', ''灾备切换'', ''5'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (5, ''IEAI_TIMINGTASK'', ''定时任务'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (6, ''IEAI_EMERGENCY_OPER'', ''应急操作'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 7;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (7, ''IEAI_ROUTING INSPECTION'', ''健康巡检'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_TYPE_WS WHERE IID = 8;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_TYPE_WS (IID, INAME, IREMARKS, IPROPERTYIDS) VALUES (8, ''IEAI_IEAI_BASIC '', ''日常操作'', ''1,2,3,4,5'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_GROUP_DC WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_GROUP_DC (IID, IGROUPNAME, IREMARKS) VALUES (1, ''importprj'', ''引入工程'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_GROUP_DC WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_GROUP_DC (IID, IGROUPNAME, IREMARKS) VALUES (2, ''function'', ''函数'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_GROUP_DC WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_GROUP_DC (IID, IGROUPNAME, IREMARKS) VALUES (3, ''envvar'', ''环境变量'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_GROUP_DC WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_GROUP_DC (IID, IGROUPNAME, IREMARKS) VALUES (4, ''publicres'', ''共享资源'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT_PROPERTY_GROUP_DC WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_PROJECT_PROPERTY_GROUP_DC (IID, IGROUPNAME, IREMARKS) VALUES (5, ''workflow'', ''工作流'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_TYPE_WS WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_TYPE_WS (IID, IPROJECTTYPEID, INAME, IREMARKS) VALUES (1, NULL, ''switchshell'', ''灾备脚本步骤'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_TYPE_WS WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_TYPE_WS (IID, IPROJECTTYPEID, INAME, IREMARKS) VALUES (2, NULL, ''switchusertasks'', ''灾备人工步骤'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_TYPE_WS WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_TYPE_WS (IID, IPROJECTTYPEID, INAME, IREMARKS) VALUES (3, NULL, ''switchssh'', ''灾备ssh步骤'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_TYPE_WS WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_TYPE_WS (IID, IPROJECTTYPEID, INAME, IREMARKS) VALUES (4, NULL, ''switchtelnet'', ''灾备telnet步骤'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_TYPE_WS WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_TYPE_WS (IID, IPROJECTTYPEID, INAME, IREMARKS) VALUES (5, NULL, ''sususertasks'', ''变更UT'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_TYPE_WS WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_TYPE_WS (IID, IPROJECTTYPEID, INAME, IREMARKS) VALUES (6, NULL, ''susdatamapping'', ''变更数据打印'')';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 1;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (1, 1, ''icenter'', ''阶段'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 2;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (2, 1, ''iactname'', ''子步骤名称'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 3;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (3, 1, ''iactdes'', ''子步骤描述'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 4;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (4, 1, ''imodeltype'', ''切换系统'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 5;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (5, 1, ''iip'', ''执行所在设备'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 6;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (6, 1, ''iexecuser'', ''执行用户'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 7;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (7, 1, ''ishellscript'', ''壳脚本'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 8;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (8, 1, ''isystype'', ''系统类型'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 9;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (9, 1, ''ishellpath'', ''执行脚本'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 10;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (10, 1, ''iparacheck'', ''执行检查参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 11;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (11, 1, ''iparaswitch'', ''执行切换参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 12;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (12, 1, ''iparaswitchforce'', ''执行强制切换参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 13;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (13, 1, ''iexceptinfo'', ''预期返回值'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 14;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (14, 2, ''icenter'', ''阶段'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 15;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (15, 2, ''iactname'', ''子步骤名称'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 16;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (16, 2, ''iactdes'', ''子步骤描述'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 17;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (17, 2, ''ireminfo'', ''提醒内容'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 18;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (18, 3, ''icenter'', ''阶段'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 19;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (19, 3, ''iactname'', ''子步骤名称'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 20;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (20, 3, ''iactdes'', ''子步骤描述'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 21;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (21, 3, ''imodeltype'', ''切换系统'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 22;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (22, 3, ''iip'', ''执行所在设备'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 23;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (23, 3, ''iexecuser'', ''执行用户'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 24;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (24, 3, ''ishellscript'', ''壳脚本'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 25;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (25, 3, ''isystype'', ''系统类型'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 26;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (26, 3, ''ishellpath'', ''执行脚本'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 27;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (27, 3, ''iparacheck'', ''执行检查参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 28;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (28, 3, ''iparaswitch'', ''执行切换参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 29;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (29, 3, ''iparaswitchforce'', ''执行强制切换参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 30;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (30, 3, ''iexceptinfo'', ''预期返回值'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 31;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (31, 4, ''icenter'', ''阶段'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 32;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (32, 4, ''iactname'', ''子步骤名称'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 33;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (33, 4, ''iactdes'', ''子步骤描述'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 34;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (34, 4, ''imodeltype'', ''切换系统'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 35;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (35, 4, ''iip'', ''执行所在设备'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 36;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (36, 4, ''iexecuser'', ''执行用户'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 37;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (37, 4, ''ishellscript'', ''壳脚本'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 38;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (38, 4, ''isystype'', ''系统类型'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 39;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (39, 4, ''ishellpath'', ''执行脚本'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 40;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (40, 4, ''iparacheck'', ''执行检查参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 41;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (41, 4, ''iparaswitch'', ''执行切换参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 42;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (42, 4, ''iparaswitchforce'', ''执行强制切换参数'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 43;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (43, 4, ''iexceptinfo'', ''预期返回值'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 44;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (44, 5, ''remindinginfo'', ''提醒内容'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ADAPTOR_PROPERTY_KEY_WS WHERE IID = 45;
    IF LI_EXISTS = 0 THEN
        SET LS_SQL = 'INSERT INTO IEAI_ADAPTOR_PROPERTY_KEY_WS (IID, IADAPTORID, INAME, IDISNAME, IREMARKS)VALUES (45, 6, ''printinfo'', ''打印信息'', NULL)';
        PREPARE SQLA FROM LS_SQL;
        EXECUTE SQLA;
    END IF;
END
$







 








































