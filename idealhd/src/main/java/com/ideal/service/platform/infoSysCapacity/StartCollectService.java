package com.ideal.service.platform.infoSysCapacity;

import com.alibaba.fastjson.JSON;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.datacollect.repository.collectstart.CollectStartManage;
import com.ideal.ieai.server.engine.agent.AgentXmlRpcClient;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.platform.infoSysCapacity.StartCollectManager;
import com.ideal.ieai.server.platform.infoSysCapacity.SysInfoManager;
import com.ideal.ieai.server.platform.infoSysCapacity.bean.AddTask;
import com.ideal.ieai.server.platform.infoSysCapacity.bean.InfoSysCapacityQueryBean;
import com.ideal.ieai.server.platform.infoSysCapacity.bean.SysInfoBean;
import com.ideal.ieai.server.platform.infoSysCapacity.bean.SysInfoCollectConfigBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.GetBaseConnectionUtil;
import com.ideal.ieai.server.util.HttpUtils;
import com.ideal.util.sm4.SM4Utils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.xmlrpc.XmlRpcClient;
import org.json.JSONObject;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

/**   
 * @ClassName:  StartCollectService   
 * @Description:启动采集   
 * @author: hongji_shang 
 * @date:   2019年5月24日 上午8:02:19   
 *     
 * @Copyright: 2019-2027 www.idealinfo.com Inc. All rights reserved. 
 * 
 */  
public class StartCollectService
{
    private Logger                log         = Logger.getLogger(StartCollectService.class);
    private static final String   METHODSTR   = "method : ";
    private static StartCollectService intance    = new StartCollectService();
    private static int AGENT_WEB_PORT = 8080;
    static
    {
        AGENT_WEB_PORT = ServerEnv.getInstance().getIntConfig("shunde.agent.webport", 8080);
    }
    public static StartCollectService getInstance ()
    {
        if (intance == null)
        {
            intance = new StartCollectService();
        }
        return intance;
    }
    
    /**   
     * @Title: startCpuTopCollect   
     * @Description: 启动cputop采集   
     * @param username
     * @param sysId
     * @return
     * @throws Exception      
     * @author: hongji_shang 
     * @date:   2019年6月11日 上午10:07:02   
     */  
    public boolean startCpuTopCollect ( String username, long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag=false;
        boolean httpSendFlag = ServerEnv.getInstance().getBooleanConfig(Environment.SHUNDE_HTTPSERVER_FLAG, false);
        try{
            if (httpSendFlag)
            {
                flag=startCpuTopCollectForXmlrpc(sysId,username);
            } else
            {
                flag=startCpuTopCollectForHttp(sysId,username);
            }
        }catch(Exception e){
            log.error(method,e);
            throw e;
        }
        return flag;
    }
    
    
    public boolean stopCollect ( String username, String types,long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag=false;
        boolean httpSendFlag = ServerEnv.getInstance().getBooleanConfig(Environment.SHUNDE_HTTPSERVER_FLAG, false);
        try{
            if (httpSendFlag)
            {
                flag=stopCollectForXmlrpc(username,types,sysId);
            } else
            {
                flag=stopCollectForHttp(username,types,sysId);
            }
        }catch(Exception e){
            log.error(method,e);
        }
        return flag;
    }
    
    
    public boolean startStorageCollect ( String username, long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag=false;
        boolean httpSendFlag = ServerEnv.getInstance().getBooleanConfig(Environment.SHUNDE_HTTPSERVER_FLAG, false);
        try{
            if (httpSendFlag)
            {
                flag=startStorageCollectForXmlrpc(username,sysId);
            } else
            {
                flag=startStorageCollectForHttp(username,sysId);
            }
        }catch(Exception e){
            log.error(method,e);
            throw e;
        }
        return flag;
    }
    
  
    
    /**   
     * @Title: getToken   
     * @Description: 获取Agent restful token   
     * @param url
     * @return      
     * @author: hongji_shang 
     * @date:   2019年6月10日 下午3:42:39   
     */  
    private static String getToken(String url){
        String token = "";
        try
        {
            token = HttpUtils.doPost(url, "");
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return token;
    }
    
    private AddTask createTask(Connection conn,String taskName,String username,String cronStr,String returnUrl,String runScript) throws RepositoryException{

        AddTask addTask = new AddTask();
        long iid = IdGenerator.createId("IEAI_CAPACITY_TASK", conn);
        addTask.setAgentTaskName(taskName);
        addTask.setCreatetime(System.currentTimeMillis());
        // 脚本路径怎么取
        addTask.setIid(iid);
        addTask.setIsSync(1);
        // 返回结果地址
        addTask.setReturnUrl(returnUrl);
        addTask.setRunScript(runScript);
        addTask.setRunType(1);
        addTask.setRunUser(username);
        addTask.setSender("");
        addTask.setTimingExpression(cronStr);
        addTask.setTimingType(2);
        addTask.setIsysname(taskName+"-Capacity");
        addTask.setIsendkfk("3");
        addTask.setProvrefer("1");
        return addTask;

    }
    
    public static void main ( String[] args )
    {
        System.out.println((int)Math.floor(121/60));
    }
    
    public Map<String, Object> queryStartCollectList ( InfoSysCapacityQueryBean queryBean ) throws RepositoryException
    {
        return StartCollectManager.getInstance().queryStartCollectList(queryBean);
    }
    
    /**   
     * @Title: startCpuTopCollectForHttp   
     * @Description: 启动cputop采集   Http请求
     * @param sysId
     * @return
     * @throws Exception      
     * @author: ymx 
     * @date:   2021年3月18日 上午10:07:02   
     */ 
    public boolean startCpuTopCollectForHttp(long sysId,String username) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        boolean flag=false;
        CollectStartManage manage = new CollectStartManage();
        try
        {
            // 获取connect连接
            conn = GetBaseConnectionUtil.getBaseConnectionInfo();
            // 调用Agent接口启动任务
            // 获取采集配置信息
            SysInfoCollectConfigBean collectConfig = SysInfoService.getInstance().getCollectConfig(sysId);
            if(collectConfig.getIntervalTime()<=0){
                throw new Exception("请配置系统采集间隔时间！");
            }
            int it = collectConfig.getIntervalTime();
            String cronStr = "";
            if(it>59){
                int hour = (int)Math.floor(it/60);
                cronStr = "0 0 0/"+hour+" * * ? ";
            }else{
                cronStr = "0 */" + collectConfig.getIntervalTime() + " * * * ?";
            }
            
            
            // 获取Agent信息
            SysInfoBean agetnInfo = SysInfoManager.getInstance().getSysAgentInfo(sysId, conn);
            if(null==agetnInfo|| StringUtils.isEmpty(agetnInfo.getIagentIp())){
                throw new Exception("请绑定Agent");
            }
            String url = "http://" + agetnInfo.getIagentIp() + ":"+AGENT_WEB_PORT+"/restfulapi";
            String getTokenUrl = url + "?methodname=getToken";
            String toekn = getToken(getTokenUrl);
            String addTaskUrl = url + "?methodname=addtask&token=" + toekn;
            List<AddTask> taskList = new ArrayList<AddTask>();
            String cpuScript = ServerEnv.getInstance().getShundeCapacityCpuscript();
            String topScript = ServerEnv.getInstance().getShundeCapacityTopscript() + " " +collectConfig.getTopProcNum();
            String returnUrl = ServerEnv.getInstance().getShundeCapacityReturnurl();
            Map<String,Object> map=new HashMap<>();
            map.put("runUser", "root-Capacity");
//            map.put("preScript", "sh");
            map.put("script", cpuScript);
            map.put("computerIp", ServerEnv.getInstance().getServerIP()+":"+ServerEnv.getInstance().getServerPort());
            String  cpuScript1=JSON.toJSONString(map);
            Map<String,Object> map1=new HashMap<>();
            map1.put("runUser", "root-Capacity");
//            map1.put("preScript", "sh");
            map1.put("script", topScript);
            map1.put("computerIp", ServerEnv.getInstance().getServerIP()+":"+ServerEnv.getInstance().getServerPort());
            String  topScript1=JSON.toJSONString(map1);
            String ishellInfo = manage.getAdaporInfo(conn,"shellcmd");
            AddTask cpuTask = createTask(conn, "cpu" + sysId, username, cronStr, returnUrl, cpuScript1);
            AddTask topTask = createTask(conn, "top" + sysId, username, cronStr, returnUrl, topScript1);
            taskList.add(cpuTask);
            taskList.add(topTask);
            JSONObject jsSend = new JSONObject();
            String taskJson = JSON.toJSONString(taskList);
            jsSend.accumulate("tasks", taskJson);
            jsSend.accumulate("adaptors", ishellInfo);
            log.info("########容量分析，cpuScript、topScript启动发起！运行脚本："+cpuScript+","+topScript+",任务名称：cpu" + sysId+"top" + sysId+"############");
            String addTaskResult = HttpUtils.doPost(addTaskUrl, jsSend.toString());
            TaskResult taskResult = JSON.parseObject(addTaskResult, TaskResult.class);
            // 添加任务成功将信息入库
            if (taskResult.isSuccess())
            {
                log.info("########容量分析，cpuScript、topScript启动成功！运行脚本："+cpuScript+","+topScript+",任务名称：cpu" + sysId+"top" + sysId+"############");
                //删除以前的任务
                StartCollectManager.getInstance().deleteTaskBySysId(sysId,"1,2",conn);
                //创建新任务
                for (TaskInstance ins : taskResult.getResult())
                {
                    if (ins.getInstid() == topTask.getIid())
                    {
                        StartCollectManager.getInstance().insertTask(topTask.getIid(), sysId, ins.getInstid(), 2, 1,
                            agetnInfo.getIagentIp(),agetnInfo.getIagentPort(), conn);
                    } else if (ins.getInstid() == cpuTask.getIid())
                    {
                        StartCollectManager.getInstance().insertTask(cpuTask.getIid(), sysId, ins.getInstid(), 1, 1,
                            agetnInfo.getIagentIp(),agetnInfo.getIagentPort(),conn);
                    }
                }
                flag = true;
            }else{
                flag = false;
            }
            conn.commit();
           
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                log.error(METHODSTR + method, e1);
            }

            log.error(METHODSTR + method, e);
        } catch (DBException e)
        {
            log.error(METHODSTR + method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return flag;
    }
    
    /**   
     * @Title: startCpuTopCollectForHttp   
     * @Description: 启动cputop采集   XmlRpc请求
     * @param sysId
     * @return
     * @throws Exception      
     * @author: ymx 
     * @date:   2021年3月18日 上午10:07:02   
     */ 
    public boolean startCpuTopCollectForXmlrpc(long sysId,String username) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection conn = null;
        boolean flag=false;
        Vector params = new Vector();
        SM4Utils sm4 = new SM4Utils();
        /**加密*/
        sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
        /**获取token*/
        String token = sm4.encryptData_ECB("ideal.info");
        try
        {
            // 获取connect连接
            conn = GetBaseConnectionUtil.getBaseConnectionInfo();
            // 调用Agent接口启动任务
            // 获取采集配置信息
            SysInfoCollectConfigBean collectConfig = SysInfoService.getInstance().getCollectConfig(sysId);
            if(collectConfig.getIntervalTime()<=0){
                throw new Exception("请配置系统采集间隔时间！");
            }
            int it = collectConfig.getIntervalTime();
            String cronStr = "";
            if(it>59){
                int hour = (int)Math.floor(it/60);
                cronStr = "0 0 0/"+hour+" * * ? ";
            }else{
                cronStr = "0 */" + collectConfig.getIntervalTime() + " * * * ?";
            }
            
            
            // 获取Agent信息
            SysInfoBean agetnInfo = SysInfoManager.getInstance().getSysAgentInfo(sysId, conn);
            if(null==agetnInfo|| StringUtils.isEmpty(agetnInfo.getIagentIp())){
                throw new Exception("请绑定Agent");
            }
            List<AddTask> taskList = new ArrayList<AddTask>();
            String cpuScript = ServerEnv.getInstance().getShundeCapacityCpuscript();
            String topScript = ServerEnv.getInstance().getShundeCapacityTopscript() + " " +collectConfig.getTopProcNum();
            String returnUrl = ServerEnv.getInstance().getShundeCapacityReturnurl();
            AddTask cpuTask = createTask(conn, "cpu" + sysId, username, cronStr, returnUrl, cpuScript);
            AddTask topTask = createTask(conn, "top" + sysId, username, cronStr, returnUrl, topScript);
            taskList.add(cpuTask);
            taskList.add(topTask);
            String taskJson = JSON.toJSONString(taskList);
            params.add("addtask");
            params.add(token);
            params.add(taskJson);
            int ssltype = Environment.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            XmlRpcClient rpcClient = new AgentXmlRpcClient(agetnInfo.getIagentIp(), AGENT_WEB_PORT, ssltype);
            String backMessage = (String) rpcClient.execute("IEAIAgent.taskOperation", params);
            TaskResult taskResult = JSON.parseObject(backMessage, TaskResult.class);
            // 添加任务成功将信息入库
            if (taskResult.isSuccess())
            {
                //删除以前的任务
                StartCollectManager.getInstance().deleteTaskBySysId(sysId,"1,2",conn);
                //创建新任务
                for (TaskInstance ins : taskResult.getResult())
                {
                    if (ins.getInstid() == topTask.getIid())
                    {
                        StartCollectManager.getInstance().insertTask(topTask.getIid(), sysId, ins.getInstid(), 2, 1,
                            agetnInfo.getIagentIp(),agetnInfo.getIagentPort(), conn);
                    } else if (ins.getInstid() == cpuTask.getIid())
                    {
                        StartCollectManager.getInstance().insertTask(cpuTask.getIid(), sysId, ins.getInstid(), 1, 1,
                            agetnInfo.getIagentIp(),agetnInfo.getIagentPort(),conn);
                    }
                }
                flag = true;
            }else{
                flag = false;
            }
            conn.commit();
           
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                log.error(METHODSTR + method, e1);
            }

            log.error(METHODSTR + method, e);
        } catch (DBException e)
        {
            log.error(METHODSTR + method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return flag;
    }
    
    /**
     * 
     * <li>停止:cputop采集 http请求</li> 
     * <AUTHOR>
     * 2021年3月18日 
     * @param username
     * @param types
     * @param sysId
     * @return
     * @throws Exception
     * return boolean
     */
    public boolean stopCollectForHttp ( String username, String types,long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag = false;
        Connection conn = null;
        try
        {
            // 获取connect连接
            conn = GetBaseConnectionUtil.getBaseConnectionInfo();
            // 调用Agent接口启动任务
            // 获取Agent信息
            SysInfoBean agetnInfo = SysInfoManager.getInstance().getSysAgentInfo(sysId, conn);
            if(null==agetnInfo|| StringUtils.isEmpty(agetnInfo.getIagentIp())){
                throw new Exception("请绑定Agent");
            }
            String url = "http://" + agetnInfo.getIagentIp() + ":"+AGENT_WEB_PORT+"/restfulapi";
            String getTokenUrl = url + "?methodname=getToken";
            String toekn = getToken(getTokenUrl);
            String delTaskUrl = url + "?methodname=deltask&token=" + toekn;
            
            List<Map> taskList = new ArrayList<Map>();
            
            List<Long> insList =  StartCollectManager.getInstance().getSysAgentInfo(sysId, types, conn);
            if(insList.size()>0){
                for(Long insId :insList){
                    Map taskMap = new HashedMap();
                    taskMap.put("instId", insId);
                    taskMap.put("runType", 1);
                    taskMap.put("timingType", 1);
                    taskList.add(taskMap);
                }
                // 组织任务参数
                String taskJson = JSON.toJSONString(taskList);
                String delTaskResult = HttpUtils.doPost(delTaskUrl, taskJson);
                //Map resultMap =  (Map) JSON.parse(delTaskResult);
                //删除任务
                StartCollectManager.getInstance().deleteTaskBySysId(sysId,types,conn);
                conn.commit();
                flag = true;
            }
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            log.error(METHODSTR + method, e);
        } catch (DBException e)
        {
            log.error(METHODSTR + method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return flag;
    }
    
    /**
     * 
     * <li>停止:cputop采集 xmlrpc请求</li> 
     * <AUTHOR>
     * 2021年3月18日 
     * @param username
     * @param types
     * @param sysId
     * @return
     * @throws Exception
     * return boolean
     */
    public boolean stopCollectForXmlrpc ( String username, String types,long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag = false;
        Connection conn = null;
        Vector params = new Vector();
        SM4Utils sm4 = new SM4Utils();
        /**加密*/
        sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
        /**获取token*/
        String token = sm4.encryptData_ECB("ideal.info");
        try
        {
            // 获取connect连接
            conn = GetBaseConnectionUtil.getBaseConnectionInfo();
            // 调用Agent接口启动任务
            // 获取Agent信息
            SysInfoBean agetnInfo = SysInfoManager.getInstance().getSysAgentInfo(sysId, conn);
            if(null==agetnInfo|| StringUtils.isEmpty(agetnInfo.getIagentIp())){
                throw new Exception("请绑定Agent");
            }
            
            List<Map> taskList = new ArrayList<Map>();
            
            List<Long> insList =  StartCollectManager.getInstance().getSysAgentInfo(sysId, types, conn);
            if(insList.size()>0){
                for(Long insId :insList){
                    Map taskMap = new HashedMap();
                    taskMap.put("instId", insId);
                    taskMap.put("runType", 1);
                    taskMap.put("timingType", 1);
                    taskList.add(taskMap);
                }
                // 组织任务参数
                String taskJson = JSON.toJSONString(taskList);
                params.add("deltask");
                params.add(token);
                params.add(taskJson);
                int ssltype = Environment.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
                XmlRpcClient rpcClient = new AgentXmlRpcClient(agetnInfo.getIagentIp(), AGENT_WEB_PORT, ssltype);
                String delTaskResult = (String) rpcClient.execute("IEAIAgent.taskOperation", params);
                TaskResult taskResult = JSON.parseObject(delTaskResult, TaskResult.class);
                //String delTaskResult = HttpUtils.doPost(delTaskUrl, taskJson);
                //Map resultMap =  (Map) JSON.parse(delTaskResult);
                //删除任务
                StartCollectManager.getInstance().deleteTaskBySysId(sysId,types,conn);
                conn.commit();
                flag = true;
            }
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }
            log.error(METHODSTR + method, e);
        } catch (DBException e)
        {
            log.error(METHODSTR + method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return flag;
    }
    
    /**
     * 
     * <li>启动存储采集 http请求:</li> 
     * <AUTHOR>
     * 2021年3月18日 
     * @param username
     * @param sysId
     * @return
     * @throws Exception
     * return boolean
     */
    public boolean startStorageCollectForHttp ( String username, long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag = false;
        Connection conn = null;
        CollectStartManage manage = new CollectStartManage();
        try
        {
            // 获取connect连接
            conn = GetBaseConnectionUtil.getBaseConnectionInfo();
            // 调用Agent接口启动任务
            // 获取采集配置信息
            String stroageInterval = ServerEnv.getInstance().getShundeCapacityStorageInterval();
            //String cronStr = "0 * */" + stroageInterval + " * * ?";
            // 获取Agent信息
            SysInfoBean agetnInfo = SysInfoManager.getInstance().getSysAgentInfo(sysId, conn);
            if(null==agetnInfo|| StringUtils.isEmpty(agetnInfo.getIagentIp())){
                throw new Exception("请绑定Agent");
            }
            String url = "http://" + agetnInfo.getIagentIp() + ":"+AGENT_WEB_PORT+"/restfulapi";
            String getTokenUrl = url + "?methodname=getToken";
            String toekn = getToken(getTokenUrl);
            String addTaskUrl = url + "?methodname=addtask&token=" + toekn;
            List<AddTask> taskList = new ArrayList<AddTask>();
            // 组织任务参数
            String storageScript = ServerEnv.getInstance().getShundeCapacityStoragescript();
            String returnUrl = ServerEnv.getInstance().getShundeCapacityReturnurl();
            Map<String,Object> map=new HashMap<>();
            map.put("runUser", "root-Capacity");
//            map.put("preScript", "sh");
            map.put("script", storageScript);
            map.put("computerIp", ServerEnv.getInstance().getServerIP()+":"+ServerEnv.getInstance().getServerPort());
            String  storageScript1=JSON.toJSONString(map);
            String ishellInfo = manage.getAdaporInfo(conn,"shellcmd");
            AddTask storageTask = createTask(conn, "storage" + sysId, username, stroageInterval, returnUrl, storageScript1);
            taskList.add(storageTask);
            JSONObject jsSend = new JSONObject();
            String taskJson = JSON.toJSONString(taskList);
            jsSend.accumulate("tasks", taskJson);
            jsSend.accumulate("adaptors", ishellInfo);
            log.info("########容量分析，storageScript启动发起！运行脚本："+storageScript+",任务名称："+"storage" + sysId+"############");
            String addTaskResult = HttpUtils.doPost(addTaskUrl, jsSend.toString());
            TaskResult taskResult = JSON.parseObject(addTaskResult, TaskResult.class);
            // 添加任务成功将信息入库
            if (taskResult.isSuccess())
            {
                log.info("########容量分析，storageScript成功！运行脚本："+storageScript+",任务名称："+"storage" + sysId+"############");
                //删除以前的任务
                StartCollectManager.getInstance().deleteTaskBySysId(sysId,"3",conn);
                //创建新任务
                for (TaskInstance ins : taskResult.getResult())
                {
                        StartCollectManager.getInstance().insertTask(storageTask.getIid(), sysId, ins.getInstid(), 3, 1,
                            agetnInfo.getIagentIp(),agetnInfo.getIagentPort(),conn);
                }
                flag = true;
            }else{
                flag = false;
            }
            conn.commit();
            
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }

            log.error(METHODSTR + method, e);
        } catch (DBException e)
        {
            log.error(METHODSTR + method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return flag;
    }
    
    /**
     * 
     * <li>启动存储采集 xmlrpc请求:</li> 
     * <AUTHOR>
     * 2021年3月18日 
     * @param username
     * @param sysId
     * @return
     * @throws Exception
     * return boolean
     */
    public boolean startStorageCollectForXmlrpc ( String username, long sysId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean flag = false;
        Connection conn = null;
        Vector params = new Vector();
        SM4Utils sm4 = new SM4Utils();
        /**加密*/
        sm4.setSecretKey("JeF8U9wHFOMfs2Y8");
        /**获取token*/
        String token = sm4.encryptData_ECB("ideal.info");
        try
        {
            // 获取connect连接
            conn = GetBaseConnectionUtil.getBaseConnectionInfo();
            // 调用Agent接口启动任务
            // 获取采集配置信息
            String stroageInterval = ServerEnv.getInstance().getShundeCapacityStorageInterval();
            //String cronStr = "0 * */" + stroageInterval + " * * ?";
            // 获取Agent信息
            SysInfoBean agetnInfo = SysInfoManager.getInstance().getSysAgentInfo(sysId, conn);
            if(null==agetnInfo|| StringUtils.isEmpty(agetnInfo.getIagentIp())){
                throw new Exception("请绑定Agent");
            }
            List<AddTask> taskList = new ArrayList<AddTask>();
            // 组织任务参数
            String storageScript = ServerEnv.getInstance().getShundeCapacityStoragescript();
            String returnUrl = ServerEnv.getInstance().getShundeCapacityReturnurl();
            AddTask storageTask = createTask(conn, "storage" + sysId, username, stroageInterval, returnUrl, storageScript);
            String taskJson = JSON.toJSONString(taskList);
            params.add("addtask");
            params.add(token);
            params.add(taskJson);
            int ssltype = Environment.getInstance().getIntConfig("agent.taskOperation.ssltype", 0);
            XmlRpcClient rpcClient = new AgentXmlRpcClient(agetnInfo.getIagentIp(), AGENT_WEB_PORT, ssltype);
            String backMessage = (String) rpcClient.execute("IEAIAgent.taskOperation", params);
            TaskResult taskResult = JSON.parseObject(backMessage, TaskResult.class);
            // 添加任务成功将信息入库
            if (taskResult.isSuccess())
            {
                //删除以前的任务
                StartCollectManager.getInstance().deleteTaskBySysId(sysId,"3",conn);
                //创建新任务
                for (TaskInstance ins : taskResult.getResult())
                {
                        StartCollectManager.getInstance().insertTask(storageTask.getIid(), sysId, ins.getInstid(), 3, 1,
                            agetnInfo.getIagentIp(),agetnInfo.getIagentPort(),conn);
                }
                flag = true;
            }else{
                flag = false;
            }
            conn.commit();
            
        } catch (SQLException e)
        {
            try
            {
                conn.rollback();
            } catch (SQLException e1)
            {
                e1.printStackTrace();
            }

            log.error(METHODSTR + method, e);
        } catch (DBException e)
        {
            log.error(METHODSTR + method, e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return flag;
    }
}
