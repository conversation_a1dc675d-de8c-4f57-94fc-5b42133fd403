package com.ideal.ieai.prv.repository.hd.dubbo.dbaas;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import com.ideal.dubbo.interfaces.dbaas.ScriptOfMyModel;
import com.ideal.ieai.ExternalSysDBManager;
import com.ideal.ieai.cbb.strategy.StrategyConstants;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;

public class ScriptOfMyManager
{
    private static final Logger _log          = Logger.getLogger(ScriptOfMyManager.class);

    private static final String SUCCESS       = "success";
    private static final String PLANTIMETEXT  = "planTime_text";
    private static final String PLANTIME      = "planTime";
    private static final String MESSAGE       = "message";
    private static final String ISTATE        = "istate";
    private static final String IPLANTIMETEXT = "iplanTime_text";
    private static final String IPLANTIME     = "iplanTime";
    private static final String IEXECTIMETYPE = "iexecTimeType";
    private static final String EXECTIMETYPE  = "execTimeType";

    public Map<String, Object> selectScriptOfMyList ( Map<String, String> args )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "";
        Connection conn = null;
        Map<String, Object> map = new HashMap<String, Object>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ScriptOfMyModel> query = new ArrayList<ScriptOfMyModel>();
        int count = 0;
        try
        {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_IEAI_BASIC);
            sql = "SELECT m.*,s.iserviceid as servicesNo FROM IEAI_SCRIPT_OF_MY m ,ieai_script_services  s where m.iserviceid=s.iid and (m.ideleted= 0 or m.ideleted is null) ";
            if (StringUtils.isNotBlank(args.get("IserviceName")))
            {
                sql += " and m.iserviceName like '%" + args.get("IserviceName") + "%'";
            }
            if (StringUtils.isNotBlank(args.get("iserviceid")))
            {
                sql += " and s.iserviceid like  '%" + args.get("iserviceid") + "%'";
            }
            if (StringUtils.isNotBlank(args.get("istate")))
            {
                sql += " and  m.istate = '" + args.get("istate") + "'";
            }
            Long userId = Long.parseLong(args.get("userid"));
            boolean isManage = ExternalSysDBManager.getInstance().isManageUser(userId, Constants.IEAI_SCRIPT_SERVICE);
            if (!isManage)
            {
                sql += "AND IPUBLISHNAME ='" + args.get("userFullName") + "'";
            }
            sql = sql + " order by m.iid desc";
            count = SQLUtil.toCount(conn, sql);
            sql = SQLUtil.toPage(sql, args.get("start"), args.get("limit"));
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            ScriptOfMyModel bean = null;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            while (rs.next())
            {
                bean = new ScriptOfMyModel();
                bean.setIID(rs.getLong("IID"));
                bean.setServiceNo(rs.getString("servicesNo"));
                bean.setIserviceName(rs.getString("iserviceName"));
                bean.setIserviceType(rs.getString("iserviceType"));
                bean.setIdbtype(rs.getString("idbtype"));
                bean.setIscriptType(rs.getString("iscriptType"));
                bean.setIpublishName(rs.getString("ipublishName"));
                bean.setIversion(rs.getString("iversion"));
                bean.setIexecTime(rs.getString("iexecTime"));
                bean.setIstate(rs.getString(ISTATE));
                bean.setKeyId(rs.getLong("keyId"));
                bean.setIexecTimeText(rs.getString("iexecTimeText"));
                bean.setIexecTimeType(rs.getString(IEXECTIMETYPE));
                bean.setIexecschema(rs.getString("iexecschema"));
                bean.setIplanTime_text(rs.getString(IPLANTIMETEXT));
                bean.setIplanTime(rs.getString(IPLANTIME));
                bean.setIservice_server(rs.getString("iservice_server"));
                bean.setIserviceid(rs.getLong("iserviceid"));
                bean.setIexectimenext(rs.getString("iexectimenext"));
                long times = rs.getLong("iupdatetime");
                if (times > 0)
                {
                    Date date = new Date(times);
                    bean.setIupdatetime(sdf.format(date));
                } else
                {
                    bean.setIupdatetime("");
                }
                bean.setIserviceGId(rs.getLong("ISERVICEGID"));
                query.add(bean);
            }

        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.selectScriptOfMyList", e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, _log);
        }
        map.put("dataList", query);
        map.put("total", count);
        return map;
    }

    public Map<String, Object> selectScriptOfMyForName ()
    {
        String sql;
        Connection conn = null;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        List<ScriptOfMyModel> query = new ArrayList<ScriptOfMyModel>();
        try
        {
            conn = DBResource.getConnection("selectScriptOfMyList", _log, Constants.IEAI_IEAI_BASIC);
            sql = "select distinct iserviceName from ieai_script_of_my";
            query = runner.query(conn, sql, new BeanListHandler<ScriptOfMyModel>(ScriptOfMyModel.class));
        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.selectScriptOfMyForName", e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        map.put("prjList", query);
        return map;
    }

    public Map<String, Object> selectScriptOfMyForGroup ()
    {
        String sql;
        Connection conn = null;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        List<ScriptOfMyModel> query = new ArrayList<ScriptOfMyModel>();
        try
        {
            conn = DBResource.getConnection("selectScriptOfMyList", _log, Constants.IEAI_IEAI_BASIC);
            sql = "select distinct iName igroupName from ieai_SCRIPT_SERVICE_GROUP ";
            query = runner.query(conn, sql, new BeanListHandler<ScriptOfMyModel>(ScriptOfMyModel.class));
        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.selectScriptOfMyForGroup", e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        map.put("prList", query);
        return map;
    }

    public Map<String, Object> scriptOfMyStopOrStart ( Connection conn, Map<String, String> args )
    {
        String sql;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        List<ScriptOfMyModel> query = new ArrayList<ScriptOfMyModel>();
        try
        {
            sql = "update ieai_script_of_my set IUPDATETIME=FUN_GET_DATE_NUMBER_NEW(current_timestamp,8),istate = '"
                    + args.get("Istate") + "' where iid = '" + args.get("IID") + "'";
            runner.update(conn, sql);
        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.scriptOfMyStopOrStart", e);
        }
        map.put("dataList", query);
        return map;
    }

    public Map<String, Object> batchStart ( String satrtIds )
    {
        String sql;
        Connection conn = null;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            conn = DBResource.getConnection("batchStart", _log, Constants.IEAI_SCRIPT_SERVICE);
            sql = "update ieai_script_of_my set istate = '已启动' where iid in(" + satrtIds + ")";
            runner.update(conn, sql);
            conn.commit();
        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.batchStart", e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public Map<String, Object> batchStop ( Connection conn, String stopIds )
    {
        String sql;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            sql = "update ieai_script_of_my set istate = '已停止' where iid in(" + stopIds + ")";
            runner.update(conn, sql);

        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.batchStop", e);
        } 
        return map;
    }

    public Map<String, Object> deleteMyScript ( String delIds )
    {
        String sql;
        Connection conn = null;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            conn = DBResource.getConnection("deleteMyScript", _log, Constants.IEAI_SCRIPT_SERVICE);
            sql = "UPDATE IEAI_SCRIPT_OF_MY set IDELETED = 1 where iid in(" + delIds + ")";
            runner.update(conn, sql);
            conn.commit();
        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.deleteMyScript", e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public List<ScriptOfMyModel> selectScriptOfMyListForRecover ( Connection conn )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        String sql = "";
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<ScriptOfMyModel> query = new ArrayList<ScriptOfMyModel>();
        try
        {
            String serverIp = ServerEnv.getInstance().getServerIP();
            sql = "SELECT m.*,g.iname,(CASE WHEN G.IID IS NULL THEN -1 ELSE G.IID END) GROUPID FROM IEAI_SCRIPT_OF_MY m LEFT JOIN ieai_SCRIPT_SERVICE_GROUP g ON m.iservicegid=g.iid  WHERE m.istate in ('已启动','待启动') and (m.ideleted=0 or m.ideleted is null) AND m.iexectimetype NOT IN ('按计划执行一次','立即执行') and m.iservice_server='"
                    + serverIp + "'";
            ps = conn.prepareStatement(sql);
            rs = ps.executeQuery();
            ScriptOfMyModel bean = null;
            while (rs.next())
            {
                bean = new ScriptOfMyModel();
                bean.setIID(rs.getLong("IID"));
                bean.setIserviceGId(rs.getLong("GROUPID"));
                bean.setIserviceName(rs.getString("iserviceName"));
                bean.setIscriptType(rs.getString("iserviceType"));
                bean.setIdbtype(rs.getString("idbtype"));
                bean.setIscriptType(rs.getString("iscriptType"));
                bean.setIpublishName(rs.getString("ipublishName"));
                bean.setIversion(rs.getString("iversion"));
                bean.setIexecTime(rs.getString("iexecTime"));
                bean.setIstate(rs.getString(ISTATE));
                bean.setKeyId(rs.getLong("keyId"));
                bean.setIexecTimeText(rs.getString("iexecTimeText"));
                bean.setIexecTimeType(rs.getString(IEXECTIMETYPE));
                bean.setIexecschema(rs.getString("iexecschema"));
                bean.setIplanTime_text(rs.getString(IPLANTIMETEXT));
                bean.setIplanTime(rs.getString(IPLANTIME));
                bean.setIservice_server(rs.getString("iservice_server"));
                bean.setIserviceid(rs.getLong("iserviceid"));
                bean.setIexectimenext(rs.getString("iexectimenext"));
                bean.setIgroupName(rs.getString("INAME"));
                query.add(bean);
            }
        } catch (Exception e)
        {
            _log.error(method, e);
        } finally
        {
            DBResource.closePSRS(rs, ps, method, _log);
        }
        return query;
    }

    public Map<String, Object> deleteMyScriptByServiceId ( Long serviceId )
    {
        String sql;
        Connection conn = null;
        QueryRunner runner = new QueryRunner();
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            conn = DBResource.getConnection("deleteMyScriptByServiceId", _log, Constants.IEAI_SCRIPT_SERVICE);
            sql = "UPDATE IEAI_SCRIPT_OF_MY set IDELETED = 1 where ISERVICEID =" + serviceId;
            runner.update(conn, sql);
            conn.commit();
        } catch (Exception e)
        {
            _log.error("ScriptOfMyManager.deleteMyScriptByServiceId", e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public Map<String, Object> updateMyScript ( Map<String, String> param )
    {
        Map<String, Object> map = new HashMap<String, Object>();
        Connection conn = null;
        PreparedStatement ps = null;
        String table = "IEAI_DOUBLECHECK_COLVALUE";
        String iid = param.get("iid");
        String userName = param.get("userName");
        String auditor = param.get("auditor");
        String iplanTime = param.get(IPLANTIME);
        String planTime = param.get(PLANTIME);
        String iplanTimeText = param.get("iplanTimeText");
        String planTimeText = param.get("planTimeText");
        String iexecTimeType = param.get(IEXECTIMETYPE);
        String execTimeType = param.get(EXECTIMETYPE);
        String activeTime = param.get("activeTime");
        String desc = param.get("desc");
        String istate = param.get(ISTATE);
        try
        {
            conn = DBResource.getConnection("updateMyScript", _log, Constants.IEAI_SCRIPT_SERVICE);
            String sql1 = "update ieai_script_of_my set istate = ? where iid = ? ";// 修改记录的状态
            ps = conn.prepareStatement(sql1);
            ps.setString(1, "审核中");
            ps.setLong(2, Long.parseLong(iid));
            ps.executeUpdate();
            ps.close();

            String sql2 = "insert into ieai_doublecheck_workitem(iid, istartuser, icreatetime, iexecuser, iitemtype, istate ) values(?,?,?,?,?,?) ";// 向审核表插入数据
            long workitemId = IdGenerator.createId("IEAI_DOUBLECHECK_WORKITEM", conn);
            ps = conn.prepareStatement(sql2);
            ps.setLong(1, workitemId);
            ps.setString(2, userName);
            ps.setTimestamp(3, new Timestamp(System.currentTimeMillis()));
            ps.setString(4, auditor);
            ps.setLong(5, 11);
            ps.setLong(6, 1);
            ps.executeUpdate();
            ps.close();

            String sql3 = "insert into ieai_doublecheck_colvalue(iid, iworkitemid, iitemtype, icolheader, icolvalue, ides) values(?,?,?,?,?,?)";// 向审核值表插入数据
            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, "scriptid");
            ps.setString(5, iid);
            ps.setString(6, "修改采集任务的id");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, IPLANTIME);
            ps.setString(5, iplanTime);
            ps.setString(6, "修改采集任务原本的计划时间");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, PLANTIME);
            ps.setString(5, planTime);
            ps.setString(6, "修改采集任务修改后的计划时间");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, IPLANTIMETEXT);
            ps.setString(5, iplanTimeText);
            ps.setString(6, "修改采集任务原本的周期");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, PLANTIMETEXT);
            ps.setString(5, planTimeText);
            ps.setString(6, "修改采集任务修改后的周期");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, IEXECTIMETYPE);
            ps.setString(5, iexecTimeType);
            ps.setString(6, "修改采集任务原本的周期类型");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, EXECTIMETYPE);
            ps.setString(5, execTimeType);
            ps.setString(6, "修改采集任务修改后的周期类型");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, "activeTime");
            ps.setString(5, activeTime);
            ps.setString(6, "生效时间");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, "desc");
            ps.setString(5, desc);
            ps.setString(6, "详细描述");
            ps.executeUpdate();
            ps.close();

            ps = conn.prepareStatement(sql3);
            ps.setLong(1, IdGenerator.createId(table, conn));
            ps.setLong(2, workitemId);
            ps.setLong(3, 11);
            ps.setString(4, ISTATE);
            ps.setString(5, istate);
            ps.setString(6, "修改采集任务原本的状态");
            ps.executeUpdate();
            ps.close();

            conn.commit();
            map.put(SUCCESS, true);
            map.put(MESSAGE, "修改成功");
        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
            DBResource.closePreparedStatement(ps, Thread.currentThread().getStackTrace()[1].getMethodName(), _log);
        }
        return map;
    }

    public long getScriptKeyidById ( long iid )
    {
        String method = "getScriptKeyidById";
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        long keyid = 0;
        try
        {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_SCRIPT_SERVICE);
            String sql = "select keyid from ieai_script_of_my where iid = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            rs = ps.executeQuery();
            while (rs.next())
            {
                keyid = rs.getLong("keyid");
            }
        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, _log);
        }
        return keyid;
    }

    public void execDbaasMT ( Map map )
    {
        String method = "execDbaasMT";
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        Long iworkitemid = (Long) map.get("iid");
        Long scriptid = (Long) map.get("scriptid");
        String planTime = (String) map.get(PLANTIME);
        String planTimeText = (String) map.get(PLANTIMETEXT);
        String execTimeType = (String) map.get(EXECTIMETYPE);
        String execTimeText = (String) map.get("execTimeText");
        String istate = (String) map.get(ISTATE);
        try
        {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_SCRIPT_SERVICE);
            String delSql = "delete from ieai_doublecheck_workitem where iid = ?";
            ps = conn.prepareStatement(delSql);
            ps.setLong(1, iworkitemid);
            ps.executeUpdate();

            delSql = "delete from ieai_doublecheck_colvalue where iworkitemid = ?";
            ps2 = conn.prepareStatement(delSql);
            ps2.setLong(1, iworkitemid);
            ps2.executeUpdate();

            String updSql = "update ieai_script_of_my set iplanTime=?, iplanTime_text=?, iexecTimeType=?, iexecTimeText=?, istate=? where iid = ?";
            ps3 = conn.prepareStatement(updSql);
            ps3.setString(1, planTime);
            ps3.setString(2, planTimeText);
            ps3.setString(3, execTimeType);
            ps3.setString(4, execTimeText);
            ps3.setString(5, istate);
            ps3.setLong(6, scriptid);
            ps3.executeUpdate();

            conn.commit();
        } catch (RepositoryException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        // 删除审核表的数据
        // 修改采集任务的数据
        catch (SQLException e)
        {
            _log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        } finally
        {
            DBResource.closePSConn(conn, ps, method, _log);
            DBResource.closePreparedStatement(ps2, method, _log);
            DBResource.closePreparedStatement(ps3, method, _log);
        }
    }

    public void updateScriptStateById ( long iid, Map map )
    {
        String method = "updateScriptStateById";
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps2 = null;
        PreparedStatement ps3 = null;
        PreparedStatement ps4 = null;
        PreparedStatement ps5 = null;
        Long iworkitemid = (Long) map.get("iid");
        String planTime = (String) map.get(PLANTIME);
        String planTimeText = (String) map.get(PLANTIMETEXT);
        String execTimeType = (String) map.get(EXECTIMETYPE);
        String execTimeText = (String) map.get("execTimeText");
        try
        {
            conn = DBResource.getConnection(method, _log, Constants.IEAI_SCRIPT_SERVICE);
            String sql = "update ieai_script_of_my set istate = '待变更' where iid = ?";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.executeUpdate();

            sql = "update ieai_doublecheck_colvalue set icolvalue = ? where iid =? and icolheader = ?";
            ps2 = conn.prepareStatement(sql);
            ps2.setString(1, planTime);
            ps2.setLong(2, iworkitemid);
            ps2.setString(3, PLANTIME);
            ps2.executeUpdate();

            ps3 = conn.prepareStatement(sql);
            ps3.setString(1, planTimeText);
            ps3.setLong(2, iworkitemid);
            ps3.setString(3, "planTimeTestingByCron");
            ps3.executeUpdate();

            ps4 = conn.prepareStatement(sql);
            ps4.setString(1, execTimeText);
            ps4.setLong(2, iworkitemid);
            ps4.setString(3, "planTimeTestingByDate");
            ps4.executeUpdate();

            ps5 = conn.prepareStatement(sql);
            ps5.setString(1, execTimeType);
            ps5.setLong(2, iworkitemid);
            ps5.setString(3, "planTimeTestingByType");
            ps5.executeUpdate();

            conn.commit();
        } catch (RepositoryException e)
        {
            _log.error(method, e);
        } catch (SQLException e)
        {
            _log.error(method, e);
        } finally
        {
            DBResource.closePSConn(conn, ps, method, _log);
            DBResource.closePreparedStatement(ps2, method, _log);
            DBResource.closePreparedStatement(ps3, method, _log);
            DBResource.closePreparedStatement(ps4, method, _log);
            DBResource.closePreparedStatement(ps5, method, _log);
        }

    }

    public void stopDbaasExecutionByWorkItemId ( Connection conn, Long workItemId )
    {
        String sql = "UPDATE IEAI_EXECUTION_STRATEGY SET  ISTATE = " + StrategyConstants.STRATEGY_ISTATE_DESTROY
                + "  WHERE WORKITEMID =? AND (JOBCLASS='com.ideal.ieai.server.quartz.DbaasScriptServiceJob' or JOBCLASS='com.ideal.ieai.server.quartz.DbaasScriptServiceJobPlan')";
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, workItemId);
            ps.executeUpdate();
            _log.info("dbaas workItemId=" + workItemId + "的任务已停止");
        } catch (Exception e)
        {
            _log.error(method, e);
        } finally
        {
            DBResource.closePreparedStatement(ps, method, _log);
        }
    }

    public void stopDbaasPlanExecutionByWorkItemId ( Connection conn, Long workItemId )
    {
        String sql = "UPDATE IEAI_EXECUTION_STRATEGY SET  ISTATE = " + StrategyConstants.STRATEGY_ISTATE_DESTROY
                + "  WHERE WORKITEMID =? AND  JOBCLASS='com.ideal.ieai.server.quartz.DbaasScriptServiceJobPlan'";
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        PreparedStatement ps = null;
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, workItemId);
            ps.executeUpdate();
            _log.info("dbaas workItemId=" + workItemId + "的任务已停止");
        } catch (Exception e)
        {
            _log.error(method, e);
        } finally
        {
            DBResource.closePreparedStatement(ps, method, _log);
        }
    }




}
