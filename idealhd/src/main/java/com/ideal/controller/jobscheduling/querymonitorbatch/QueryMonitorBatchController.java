package com.ideal.controller.jobscheduling.querymonitorbatch;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.*;
import com.ideal.ieai.commons.keynodealarm.AlarmCfgInfo;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.server.jobscheduling.repository.configinfoview.AlarmCfgMessageForm;
import com.ideal.ieai.server.jobscheduling.repository.keynodealarm.AlarmConfigExcelUtil;
import com.ideal.ieai.server.jobscheduling.repository.querymonitorbatch.AlarmCfgMessage;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.jobscheduling.configinfoview.ConfigInfoViewService;
import com.ideal.service.jobscheduling.querymonitorbatch.QueryMonitorBatchService;
import com.ideal.service.jobscheduling.recovery.UTRecoverService;
import com.ideal.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.apache.poi.ss.usermodel.FillPatternType.SOLID_FOREGROUND;

/**
 * <ul>
 * <li>Title: QueryMonitorBatchController.java</li>
 * <li>Description:批量监控查询页面Controller</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * <AUTHOR>
 * 2017年7月6日
 */
@Controller
public class QueryMonitorBatchController
{
    private static final Logger log = Logger.getLogger(QueryMonitorBatchController.class);
    private static final String METHODSTR = "method :";
    private static final String UTF8 = "UTF-8";
    private static final String CACTNAME = "actName";
    private static final String CIJAILMONTH = "iJailMonth";
    private static final String CIJAILSTATUS = "iJailStatus";
    private static final String CIJAILTIME = "iJailTime";
    private static final String CIJAILTIMEHOUR = "iJailTimeHour";
    private static final String CIJAILTIMEMINUTE = "iJailTimeMinute";
    private static final String CIJAILTYPE = "iJailType";
    private static final String CIJAILWEEK = "iJailWeek";
    private static final String CXIJAILSTATUS = "ijailStatus";
    private static final String CXIJAILTIME = "ijailTime";
    private static final String CIJAILTIMETYPE = "ijailTimeType";
    private static final String CXIJAILTYPE = "ijailType";
    private static final String CISYSNAME = "isysName";
    private static final String CJAILTYPECODE = "jailTypeCode";
    private static final String MESSAGE = "message";
    private static final String SUCCESS = "success";
    private static final String CPROJECTNAME = "projectName";
    private static final String CWORKFLOWNAME = "workFlowName";
    private static final String TEXTHTML = "text/html";
    private static final String SUCCESSMESSAGE = "{success:false,message:'";
    
    @RequestMapping("queryMonitorBatch.do")
    public String init ()
    {
        return "jobScheduling/queryMonitorBatch/queryMonitorBatch";
    }
    
    @RequestMapping("getMonitorBatch.do")
    @ResponseBody
    public Map getMonitorBatch(HttpServletRequest request, AlarmCfgMessageForm alarmCfgMessageForm) throws RepositoryException {
        Map map = null;
        

        String prjName = alarmCfgMessageForm.getProjectName();
        String flowName = alarmCfgMessageForm.getFlowName();
        String activityName = alarmCfgMessageForm.getActivityName();
        
        SessionData sessionData = SessionData.getSessionData(request);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());
        
        // 处理权限条件
        StringBuilder relationPrj = new StringBuilder();
        try
        {
            List inpermPrjList = UTRecoverService.getInstance().getBusinessSystemNameList(userInfo.getId(),
                Constants.IEAI_IEAI);
            for (int i = 0; inpermPrjList != null && i < inpermPrjList.size(); i++)
            {
                Map tmpMap = (Map) inpermPrjList.get(i);
                String prjNameStr = tmpMap.get(CISYSNAME).toString();
                relationPrj.append(",'" + prjNameStr + "'");
            }
        } catch (Exception e)
        {
            log.error(e.getMessage());
        }
        if (relationPrj.toString().length() > 1)
        {
            relationPrj = new StringBuilder(relationPrj.toString().substring(1));
        }

        QueryMonitorBatchService queryService = new QueryMonitorBatchService();
        
        List queryList = queryService
                .getMonitorBatch(alarmCfgMessageForm, prjName, flowName, activityName, relationPrj.toString());
        long totalCount = queryService.getMonitorBatchCount(prjName, flowName, activityName, relationPrj.toString());
        map = queryMonitorBatchForm(queryList, alarmCfgMessageForm, totalCount);
        return map;
    }
    
    public Map getMonitorBatchNopage(HttpServletRequest request, AlarmCfgMessageForm alarmCfgMessageForm) throws RepositoryException {
        Map map = null;
        
        String prjName = alarmCfgMessageForm.getProjectName();
        String flowName = alarmCfgMessageForm.getFlowName();
        String activityName = alarmCfgMessageForm.getActivityName();
        
        SessionData sessionData = SessionData.getSessionData(request);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());
        
        // 处理权限条件
        StringBuilder relationPrj = new StringBuilder();
        try
        {
            List inpermPrjList = UTRecoverService.getInstance().getBusinessSystemNameList(userInfo.getId(),
                Constants.IEAI_IEAI);
            for (int i = 0; inpermPrjList != null && i < inpermPrjList.size(); i++)
            {
                Map tmpMap = (Map) inpermPrjList.get(i);
                String prjNameStr = tmpMap.get(CISYSNAME).toString();
                relationPrj.append(",'" + prjNameStr + "'");
            }
        } catch (Exception e)
        {
            log.error(e.getMessage());
        }
        if (relationPrj.toString().length() > 1)
        {
            relationPrj = new StringBuilder(relationPrj.toString().substring(1));
        }

        QueryMonitorBatchService queryService = new QueryMonitorBatchService();
        
        List queryList = queryService
                .getMonitorBatchNopage( prjName, flowName, activityName, relationPrj.toString());
        long totalCount = queryService.getMonitorBatchCount(prjName, flowName, activityName, relationPrj.toString());
        map = queryMonitorBatchForm(queryList, alarmCfgMessageForm, totalCount);
        return map;
    }
    
    
    private String getJailTimeType(String ijailTimeType){
        
        String jailTimeType = "";
        
        if (StringUtils.equals("运行耗时", ijailTimeType))
        {
           jailTimeType = "";
        }else{
            if (StringUtils.equals("启动时间", ijailTimeType))
            {
                jailTimeType = "0";
            }else if (StringUtils.equals("结束时间", ijailTimeType))
            {
                jailTimeType = "1";
            }else {
                jailTimeType = "";
            }
        }
        
        return jailTimeType;
    }
    
    
    private String lookMonitorPageJailTimeType1(HttpServletRequest request,String jailTimeType,String ijailTimeType,AlarmCfgInfo alarmCfgInfo) throws RepositoryException{
        String resultPage = "";
        QueryMonitorBatchService queryService = new QueryMonitorBatchService();
        if (StringUtils.isEmpty(jailTimeType))
        {
            /** get the user name*/
            UserBasicInfo user = queryService.getUserinfo(alarmCfgInfo.getRunTimeSetUser());
            String loginName = user.getLoginName();
            request.setAttribute("runTimeSetUser", loginName);
            request.setAttribute("runTimeProjectName", alarmCfgInfo.getRunTimeProjectName());
            request.setAttribute("runTimeWorkFlowName", alarmCfgInfo.getRunTimeWorkFlowName());
            request.setAttribute("runTimeActName", alarmCfgInfo.getRunTimeActName());
            request.setAttribute("runTimeOverTime", alarmCfgInfo.getRunTimeOverTime());
            request.setAttribute(CIJAILTIMETYPE, ijailTimeType);
            String runTimeSetTime = alarmCfgInfo.getRunTimeSetTime();
            if (StringUtils.isNotEmpty(runTimeSetTime) && (runTimeSetTime.length() > 19 && runTimeSetTime.length() == 21))
            {
                    runTimeSetTime = runTimeSetTime.substring(0, runTimeSetTime.length()-2);
            }
            
            //监控状态
            if ("0".equals(alarmCfgInfo.getIJailStatus()))
            {
                request.setAttribute(CIJAILSTATUS, "正在监控");
            } else if ("4".equals(alarmCfgInfo.getIJailStatus()))
            {
                request.setAttribute(CIJAILSTATUS, "停止监控");
            } else
            {
                request.setAttribute(CIJAILSTATUS, "");
            }
            
            request.setAttribute("runTimeSetTime", runTimeSetTime);
            resultPage =  "jobScheduling/queryMonitorBatch/lookMonitorBatchRunPage";
            
        }
        
        return resultPage;
    }
    
    
    private void lookMonitorPageJailTimeType2(HttpServletRequest request,String jailTimeType,AlarmCfgInfo alarmCfgInfo,String ijailTimeType) throws RepositoryException{
        
        QueryMonitorBatchService queryService = new QueryMonitorBatchService();
         
        request.setAttribute(CPROJECTNAME, alarmCfgInfo.getProjectName());
        request.setAttribute(CWORKFLOWNAME, alarmCfgInfo.getWorkFlowName());
        request.setAttribute("activityName", alarmCfgInfo.getActName());
        request.setAttribute(CIJAILTIMETYPE, ijailTimeType);
        
        /**
         * 1、监控类型： 参数:iJailType  
         * 2、监控日期： 参数:iJaiTime
         * 3、小时： 参数:iJailHour
         * 4、分： 参数:iJailMinute
         */
        if (StringUtils.equals("0", jailTimeType))
        {
            if ("1".equals(alarmCfgInfo.getJailType()))
            {
                request.setAttribute(CIJAILTYPE, "日启动");
                request.setAttribute(CIJAILTIME, "每天");
            } else if ("2".equals(alarmCfgInfo.getJailType()))
            {
                request.setAttribute(CIJAILTYPE, "周启动");
                request.setAttribute(CIJAILTIME, alarmCfgInfo.getWeek());
            } else if ("3".equals(alarmCfgInfo.getJailType()))
            {
                request.setAttribute(CIJAILTYPE, "月启动");
                request.setAttribute(CIJAILTIME, alarmCfgInfo.getMonth());
            } else
            {
                request.setAttribute(CIJAILTYPE, "");
            }
            
            request.setAttribute("iJailHour", alarmCfgInfo.getHour());
            request.setAttribute("iJailMinute", alarmCfgInfo.getMinute());
        }else if (StringUtils.equals("1", jailTimeType))
        {
            if ("1".equals(alarmCfgInfo.getEndJailType()))
            {
                request.setAttribute(CIJAILTYPE, "日启动");
                request.setAttribute(CIJAILTIME, "每天");
            } else if ("2".equals(alarmCfgInfo.getEndJailType()))
            {
                request.setAttribute(CIJAILTYPE, "周启动");
                request.setAttribute(CIJAILTIME, alarmCfgInfo.getEndWeek());
            } else if ("3".equals(alarmCfgInfo.getEndJailType()))
            {
                request.setAttribute(CIJAILTYPE, "月启动");
                request.setAttribute(CIJAILTIME, alarmCfgInfo.getEndMonth());
            } else
            {
                request.setAttribute(CIJAILTYPE, "");
            }
            request.setAttribute("iJailHour", alarmCfgInfo.getEndHour());
            request.setAttribute("iJailMinute", alarmCfgInfo.getEndMinute());
        }
        
        //下次监控时间
        request.setAttribute("iNextJailTime", longTimeFormatToDateString(alarmCfgInfo.getIJailTime()));
        
        //监控状态
        if ("0".equals(alarmCfgInfo.getIJailStatus()))
        {
            request.setAttribute(CIJAILSTATUS, "正在监控");
        } else if ("4".equals(alarmCfgInfo.getIJailStatus()))
        {
            request.setAttribute(CIJAILSTATUS, "停止监控");
        } else
        {
            request.setAttribute(CIJAILSTATUS, "");
        }
        
        //设置人
        UserBasicInfo user = queryService.getUserinfo(alarmCfgInfo.getISetUser());
        String loginName = user.getLoginName();
        request.setAttribute("iSetUser", loginName);
        
        //设置时间
        request.setAttribute("iSetTime", longTimeFormatToDateString(alarmCfgInfo.getISetTime()));
    }
    
    /**
     * <li>Description:查看批量监控详细信息</li> 
     * <AUTHOR>
     * 2017年7月9日 
     * @param request
     * @param alarmCfgMessageForm
     * @throws RepositoryException 
     * @throws Exception
     * return String
     */
    @RequestMapping("lookMonitorBatchPage.do")
    public String lookMonitorBatchPage (HttpServletRequest request , AlarmCfgMessageForm alarmCfgMessageForm) throws RepositoryException 
    {
        String resultPage = "";
        
        String prjName = alarmCfgMessageForm.getProjectName();
        String flowName = alarmCfgMessageForm.getFlowName();
        String activityName = alarmCfgMessageForm.getActivityName();
        String ijailTimeType = alarmCfgMessageForm.getJailTimeType();
        
        SessionData sessionData = SessionData.getSessionData(request);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
        userInfo.setFullName(sessionData.getUserName());
        
        // 处理权限条件
        StringBuilder relationPrj = new StringBuilder();
        try
        {
            List inpermPrjList = UTRecoverService.getInstance().getBusinessSystemNameList(userInfo.getId(),
                Constants.IEAI_IEAI);
            for (int i = 0; inpermPrjList != null && i < inpermPrjList.size(); i++)
            {
                Map tmpMap = (Map) inpermPrjList.get(i);
                String prjNameStr = tmpMap.get(CISYSNAME).toString();
                relationPrj.append(",'" + prjNameStr + "'");
            }
        } catch (Exception e)
        {
            log.error(e.getMessage());
        }
        if (relationPrj.length() > 1)
        {
            relationPrj = new StringBuilder(relationPrj.toString().substring(1));
        }
        
        QueryMonitorBatchService queryService = new QueryMonitorBatchService();
        
        List queryList = queryService.lookMonitorBatch(prjName, flowName, activityName, relationPrj.toString());
        
        String jailTimeType = getJailTimeType(ijailTimeType);
        
        for (int i = 0, j = queryList.size(); i < j; i++)
        {
            AlarmCfgInfo alarmCfgInfo = (AlarmCfgInfo)queryList.get(i);
            
            //判断list中的记录是否运行耗时
            if (StringUtils.isEmpty(alarmCfgInfo.getJailTimeType()))
            {
                //判断前台选中记录是否运行耗时
                resultPage = lookMonitorPageJailTimeType1(request, jailTimeType, ijailTimeType, alarmCfgInfo);
            }else{
                //判断前台选中记录是否运行耗时
                if (StringUtils.isNotEmpty(jailTimeType))
                {
                    if (StringUtils.equals(jailTimeType, alarmCfgInfo.getJailTimeType())){
                        
                        lookMonitorPageJailTimeType2(request, jailTimeType, alarmCfgInfo, ijailTimeType);
                    }
                    
                    resultPage = "jobScheduling/queryMonitorBatch/lookMonitorBatchPage";
                }
                
            }
        }
        return resultPage;
    }
    
    /**
     * <li>Description:批量监控暂停功能</li>
     * <AUTHOR> 2017-07-03.
     * @param request
     * @throws RepositoryException
     */
    @RequestMapping("configInfoView/stopAlarmInfo.do")
    @ResponseBody
    public Map stopAlarmInfo ( HttpServletRequest request,AlarmCfgMessageForm alarmCfgMessageForm) throws RepositoryException
    {
        Map map = new HashMap();
        
        boolean flag = true;
        try
        {
            flag = QueryMonitorBatchService.getInstance().stopAlarmInfo(alarmCfgMessageForm);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        }
        
        
        if (flag)
        {
            map.put(SUCCESS, true);
            map.put(MESSAGE, "修改成功");
        }else{
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        }
        
        return map;
    }
    
    /**
     * <li>Description:批量监控恢复功能</li>
     * <AUTHOR> 2017-07-03.
     * @param request
     * @throws RepositoryException
     */
    @RequestMapping("configInfoView/recoveryAlarmInfo.do")
    @ResponseBody
    public Map recoveryAlarmInfo ( HttpServletRequest request,AlarmCfgMessageForm alarmCfgMessageForm) throws RepositoryException
    {
        Map map = new HashMap();
        
        boolean flag = true;
        try
        {
            flag = QueryMonitorBatchService.getInstance().recoveryAlarmInfo(alarmCfgMessageForm);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        }
        
        
        if (flag)
        {
            map.put(SUCCESS, true);
            map.put(MESSAGE, "修改成功");
        }else{
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        }
        
        return map;
    }
    
    /**
     * <li>Description:批量监控暂停功能</li>
     * <AUTHOR> 2017-07-03.
     * @param request
     * @throws RepositoryException
     */
    @RequestMapping("configInfoView/deleteAlarmInfo.do")
    @ResponseBody
    public Map deleteAlarmInfo ( HttpServletRequest request,AlarmCfgMessageForm alarmCfgMessageForm) throws RepositoryException
    {
        Map map = new HashMap();
        
        boolean flag = true;
        try
        {
            flag = QueryMonitorBatchService.getInstance().deleteAlarmInfo(alarmCfgMessageForm);
        } catch (Exception e)
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        }
        
        
        if (flag)
        {
            map.put(SUCCESS, true);
            map.put(MESSAGE, "修改成功");
        }else{
            map.put(SUCCESS, false);
            map.put(MESSAGE, "修改失败");
        }
        
        return map;
    }
    
    @RequestMapping("uploadMonitorBatch.do")
    public void uploadMonitorBatch ( @RequestParam("file") CommonsMultipartFile file,
            HttpServletRequest request, HttpServletResponse response ) throws  IOException
    {

        String rsStr = "{success:true}";
        String messages = "";
        
        
            String configFileName = file.getOriginalFilename();
            File fileNew = null;

            FileOutputStream outer = null;
            String userId = SessionData.getSessionData(request).getUserInnerCode();
             String userName = SessionData.getSessionData(request).getUserName();
             UserInfo user = new UserInfo();
             user.setFullName(userName);
             user.setId(Long.parseLong(userId));
            try
            {
                if (file.getSize() >= 1048576*10)
                {
                    rsStr = SUCCESSMESSAGE + "上传模板文件不能大于10M" + "'}";
                    response.setContentType(TEXTHTML);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                    response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                    dealOutStream(response, rsStr);
                } else
                {
                    // 上传文件临时目录
                    fileNew = new File(Environment.getInstance().getIEAIHome() + File.separator
                            + UUID.uuid());
                    if (!fileNew.exists())
                    {
                        fileNew.mkdir();
                    }
                    // 上传文件
                    outer = new FileOutputStream(fileNew.getAbsolutePath() + File.separator
                            + configFileName);
                    byte[] buffer = file.getBytes();
                    outer.write(buffer);
                    outer.close();
                    if (configFileName.toLowerCase().endsWith(".xls") || configFileName.toLowerCase().endsWith(".xlsx") || configFileName.toLowerCase().endsWith(".et")) {
                        long a = System.currentTimeMillis();
                        messages = AlarmConfigExcelUtil.getInstance().conlockTable(
                                fileNew.getAbsolutePath() + File.separator + configFileName,
                            user, userId);
                        long b = System.currentTimeMillis();
                        log.info("excel shangchuan:"+(b-a));
                    } else {
                        messages = "请上传合法格式的Excel文件！";
                        rsStr = SUCCESSMESSAGE + messages + "'}";
                        response.setContentType(TEXTHTML);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                        response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                        dealOutStream(response, rsStr);
                    }

                }
            } catch (Exception e)
            {
                messages = "导入失败！:" + e.getMessage();
                rsStr = SUCCESSMESSAGE + "导入失败" + "'}";
                response.setCharacterEncoding("utf-8");
                response.setContentType(TEXTHTML);
                response.setContentType(TEXTHTML);// 必须设置返回类型为text，否则ext无法正确解析json字符串
                response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
                dealOutStream(response, rsStr);
            } finally
            {
                if (null != outer)
                {
                    // 关闭输出流
                    outer.close();
                }
                if (null != fileNew)
                {
                    // 删除临时文件
                    deleteDir(fileNew);
                }

            }
           
            if ("导入成功".equals(messages))
            {
                rsStr = "{success:true,message:'" + messages + "'}";
            } else
            {
                messages = "导入失败 ！ Excel内容不符合模板要求，请仔细核对。";
                rsStr = SUCCESSMESSAGE + messages + "'}";

            }
            response.setContentType(TEXTHTML);// 必须设置返回类型为text，否则ext无法正确解析json字符串
            response.setCharacterEncoding(UTF8);// 设置编码字符集为utf-8，否则ext无法正确解析
            dealOutStream(response, rsStr);
        
    }
    
    
    private void dealOutStream(HttpServletResponse response,String rsStr){
        PrintWriter outs = null;
        try
        {
            outs = response.getWriter();
            outs.write(rsStr);
            outs.close();
        } catch (IOException ee)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),ee);
        }
    }
    @RequestMapping("exportMonitorBatch.do")
    @ResponseBody
    public void export ( HttpServletRequest request, HttpServletResponse response, AlarmCfgMessageForm alarmCfgMessageForm, String exportTypeV )
    {
        Map returnMap = new HashMap();
        try
        {
            returnMap = this.getMonitorBatchNopage(request,alarmCfgMessageForm);
            String exportType = "temp";
            exportMain(exportType, PARAM_MAP, (List) returnMap.get("dataList"), response,  exportTypeV);
        } catch (Exception e)
        {
            log.error(e.toString());
            log.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
    }
    public static void exportMain ( String fileName, Map paramMap, List inList,
             HttpServletResponse response, String exportTypeV ) throws IOException
    {
        String dfileName = "";
        try
        {
            String fileEnd = "";
            if (null != exportTypeV && exportTypeV.equals("et")) {
                fileEnd = ".et";
            }else {
                fileEnd = ".xls";
            }
            dfileName = java.net.URLEncoder.encode(fileName, "utf-8") + fileEnd;
        } catch (UnsupportedEncodingException e2)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e2);
        }
        response.reset();
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-disposition", "attachment; filename=" + dfileName);
        response.setCharacterEncoding("GBK");
        // 解决文件名称中带有[1]等字样
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        doExportXLS(response.getOutputStream(), paramMap, inList, fileName);
    }

    public static void doExportXLS ( OutputStream out,Map paramMap, List<Map> inList,
            String fileName ) throws IOException 
    {

        try
        {
            HSSFSheet sheet = null;
            HSSFWorkbook wb = new HSSFWorkbook();
            sheet = wb.createSheet();
            wb.setSheetName(0, fileName);
            int rowNum = 1;
            for (int i = 0; i < paramMap.size(); i++)
            {
                sheet.setColumnWidth( i, 4000);
            }
            // end update
            HSSFCellStyle headerStyle = null;
            HSSFCellStyle headerStyle1 = null;
            if (true)
            {
                // Create an header row
                HSSFFont font = wb.createFont();
                font.setFontHeightInPoints((short) 10); // 字体大小
                font.setBold(true); // 粗体
                font.setColor(IndexedColors.BLACK.getIndex()); // 绿字

                headerStyle = wb.createCellStyle();
                headerStyle.setFillPattern(SOLID_FOREGROUND);
                headerStyle.setFillForegroundColor(IndexedColors.LAVENDER.getIndex());
                headerStyle.setFont(font);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                headerStyle.setWrapText(true);

                headerStyle1 = wb.createCellStyle();
                headerStyle1.setWrapText(true);

                HSSFRow xlsRow = sheet.createRow(0);
                xlsRow.setHeightInPoints(25); // 设置行高
                int ii = 0;
                // 循环生成列头
                for (Iterator it = paramMap.keySet().iterator(); it.hasNext();)
                {
                    String key = it.next().toString();
                    HSSFCell cell1 = xlsRow.createCell(ii++);
                    cell1.setCellValue(key);
                    cell1.setCellStyle(headerStyle);
                }

            }
            for (int i = 0; i < inList.size(); i++)
            {
                Map dataMap = inList.get(i);
                HSSFRow xlsRow = sheet.createRow(rowNum++);
                xlsRow.setHeightInPoints(25); // 设置行高

                int ii = 0;
                // 循环生成列
                for (Iterator it = paramMap.keySet().iterator(); it.hasNext();)
                {
                    String key = it.next().toString();
                    String value = (String) paramMap.get(key);
                    HSSFCell cell0 = xlsRow.createCell(ii++);
                    cell0.setCellValue(dataMap.get(value).toString());
                    cell0.setCellStyle(headerStyle1);
                }

            }
            wb.write(out);
        } catch (Exception e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        } finally
        {
            if (out != null)
            {
                out.close();
            }
        }
    }
    
    /**
     * 
     * @Title: deleteDir
     * @Description: 递归删除目录下的所有文件及子目录下所有文件
     * @param dir 将要删除的文件目录
     * @return
     * @return boolean 返回类型
     * @throws
     * @变更记录 2015-7-17 yunpeng_zhang
     */
    public static boolean deleteDir ( File dir )
    {
        try
        {
            if (dir.isDirectory())
            {
                String[] children = dir.list();
                for (int i = 0; i < children.length; i++)
                {
                    boolean success = deleteDir(new File(dir, children[i]));
                    if (!success)
                    {
                        return false;
                    }
                }
            }
            // 目录此时为空，可以删除
            return dir.delete();
        } catch (Exception e)
        {
            return false;
        }
    }
    
    
    private Map getAlarmMapIJailTimeType(AlarmCfgMessage alarmCfgMessage){
        Map alarmMap = new HashMap();
        if ("0".equals(alarmCfgMessage.getIJailTimeType())
                || "1".equals(alarmCfgMessage.getIJailTimeType()))
        {
            alarmMap.put(CPROJECTNAME, alarmCfgMessage.getIProjectName());

            if (alarmCfgMessage.getIWorkFlowName() != null)
            {
                alarmMap.put(CWORKFLOWNAME, alarmCfgMessage.getIWorkFlowName());
            } else
            {
                alarmMap.put(CWORKFLOWNAME, "");
            }
            if (alarmCfgMessage.getIActName() != null)
            {
                alarmMap.put(CACTNAME, alarmCfgMessage.getIActName());
            } else
            {
                alarmMap.put(CACTNAME, "");
            }
        } else
        {

            alarmMap.put(CPROJECTNAME, alarmCfgMessage.getProjectName());
            
            if (alarmCfgMessage.getWorkflowName() != null)
            {
                alarmMap.put(CWORKFLOWNAME, alarmCfgMessage.getWorkflowName());
            } else
            {
                alarmMap.put("flowName", "");
            }
            if (alarmCfgMessage.getActName() != null)
            {
                alarmMap.put(CACTNAME, alarmCfgMessage.getActName());
            } else
            {
                alarmMap.put(CACTNAME, "");
            }
        }
        
        return alarmMap;
    } 
    
    private void setAlarmMapIJailTimeType0(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
        //先保存监控类型
        String jailTypeStr = alarmCfgMessage.getIJailType();
        if ("1".equals(alarmCfgMessage.getIJailType()))
        {
            alarmCfgMessage.setIJailType("日启动");
        } else if ("2".equals(alarmCfgMessage.getIJailType()))
        {
            alarmCfgMessage.setIJailType("周启动");
        } else if ("3".equals(alarmCfgMessage.getIJailType()))
        {
            alarmCfgMessage.setIJailType("月启动");
        } else
        {
            alarmCfgMessage.setIJailType("");
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getIJailType()))
        {
            alarmMap.put(CXIJAILTYPE, "");
        } else
        {
            alarmMap.put(CXIJAILTYPE, alarmCfgMessage.getIJailType());
        }

        if (StringUtils.isEmpty(jailTypeStr))
        {
            alarmMap.put(CJAILTYPECODE, "");
        } else
        {
            alarmMap.put(CJAILTYPECODE, jailTypeStr);
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getIJailWeek()))
        {
            alarmMap.put(CIJAILWEEK, "");
        } else
        {
            alarmMap.put(CIJAILWEEK, alarmCfgMessage.getIJailWeek());
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getIJailMonth()))
        {
            alarmMap.put(CIJAILMONTH, "");
        } else
        {
            alarmMap.put(CIJAILMONTH, alarmCfgMessage.getIJailMonth());
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getIJailTimeHou()))
        {
            alarmMap.put(CIJAILTIMEHOUR, "");
        } else
        {
            alarmMap.put(CIJAILTIMEHOUR, alarmCfgMessage.getIJailTimeHou());
        }
        
        setAlarmMapIJailTimeMin(alarmMap, alarmCfgMessage);
    }
    
    
    private void setAlarmMapIJailTimeMin(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
        if (StringUtils.isEmpty(alarmCfgMessage.getIJailTimeMin()))
        {
            alarmMap.put(CIJAILTIMEMINUTE, "");
        } else
        {
            alarmMap.put(CIJAILTIMEMINUTE, alarmCfgMessage.getIJailTimeMin());
        }
    }
    
    private void setAlarmMapIJailTimeType1(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
      //先保存监控类型
        String jailTypeStr = alarmCfgMessage.getEndJailType();
        if ("1".equals(alarmCfgMessage.getEndJailType()))
        {
            alarmCfgMessage.setEndJailType("日启动");
        } else if ("2".equals(alarmCfgMessage.getEndJailType()))
        {
            alarmCfgMessage.setEndJailType("周启动");
        } else if ("3".equals(alarmCfgMessage.getEndJailType()))
        {
            alarmCfgMessage.setEndJailType("月启动");
        } else
        {
            alarmCfgMessage.setEndJailType("");
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getEndJailType()))
        {
            alarmMap.put(CXIJAILTYPE, "");
        } else
        {
            alarmMap.put(CXIJAILTYPE, alarmCfgMessage.getEndJailType());
        }
        if (StringUtils.isEmpty(jailTypeStr))
        {
            alarmMap.put(CJAILTYPECODE, "");
        } else
        {
            alarmMap.put(CJAILTYPECODE, jailTypeStr);
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getEndWeek()))
        {
            alarmMap.put(CIJAILWEEK, "");
        } else
        {
            alarmMap.put(CIJAILWEEK, alarmCfgMessage.getEndWeek());
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getEndMonth()))
        {
            alarmMap.put(CIJAILMONTH, "");
        } else
        {
            alarmMap.put(CIJAILMONTH, alarmCfgMessage.getEndMonth());
        }
        if (StringUtils.isEmpty(alarmCfgMessage.getEndHour()))
        {
            alarmMap.put(CIJAILTIMEHOUR, "");
        } else
        {
            alarmMap.put(CIJAILTIMEHOUR, alarmCfgMessage.getEndHour());
        }
        setAlarmMapEndMinute(alarmMap, alarmCfgMessage);
    }
    
    private void setAlarmMapEndMinute(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
        if (StringUtils.isEmpty(alarmCfgMessage.getEndMinute()))
        {
            alarmMap.put(CIJAILTIMEMINUTE, "");
        } else
        {
            alarmMap.put(CIJAILTIMEMINUTE, alarmCfgMessage.getEndMinute());
        }
    }
    //查询返回报警信息列表给前台
    public Map queryMonitorBatchForm ( List respList, AlarmCfgMessageForm alarmCfgMessageForm, long total ) {
        Map map = new HashMap();
        List alarmCfjMessageList = new ArrayList();
        
        for (int i = 0; i < respList.size(); i++)
        {
            AlarmCfgInfo alarmCfgInfo = (AlarmCfgInfo) respList.get(i);
            AlarmCfgMessage alarmCfgMessage = new AlarmCfgMessage(alarmCfgInfo);
            alarmCfgMessageForm.addAlarmCfgMessage(alarmCfgMessage);
        }
        
        for (Iterator i = alarmCfgMessageForm.getAlarmCfgMessages(); i.hasNext();)
        {
            AlarmCfgMessage alarmCfgMessage = (AlarmCfgMessage) i.next();
            Map alarmMap = getAlarmMapIJailTimeType(alarmCfgMessage);
            
            
            if ("0".equals(alarmCfgMessage.getIJailTimeType()))
            {
                setAlarmMapIJailTimeType0(alarmMap, alarmCfgMessage);
                
            } else if ("1".equals(alarmCfgMessage.getIJailTimeType()))
            {
                setAlarmMapIJailTimeType1(alarmMap, alarmCfgMessage);
            }else
            {
                alarmMap.put(CXIJAILTYPE, "");
            }

            if (StringUtils.isNotEmpty(alarmCfgMessage.getIJailTime()))
            {
                Date currentTime = new Date( Long.parseLong(alarmCfgMessage.getIJailTime()));
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateString = formatter.format(currentTime);
                alarmCfgMessage.setIJailTime(dateString);
                alarmMap.put(CXIJAILTIME, alarmCfgMessage.getIJailTime());
            } else
            {
                alarmMap.put(CXIJAILTIME, "");
            }

            setAlarmMapgetIJailTimeType(alarmMap, alarmCfgMessage);

            setAlarmMapIJailStatus(alarmMap, alarmCfgMessage);

            setAlarmMapOverTime(alarmMap, alarmCfgMessage);

            alarmCfjMessageList.add(alarmMap);
        }
        
        map.put("dataList", alarmCfjMessageList);
        map.put("total", total);
        
        return map;
    }
    
    private void setAlarmMapgetIJailTimeType(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
        if (alarmCfgMessage.getIJailTimeType() != null)
        {
            if ("0".equals(alarmCfgMessage.getIJailTimeType()))
            {
                alarmCfgMessage.setIJailTimeType("启动时间");
                alarmMap.put(CIJAILTIMETYPE, alarmCfgMessage.getIJailTimeType());
            } else if ("1".equals(alarmCfgMessage.getIJailTimeType()))
            {
                alarmCfgMessage.setIJailTimeType("结束时间");
                alarmMap.put(CIJAILTIMETYPE, alarmCfgMessage.getIJailTimeType());
            }
        } else
        {
            alarmCfgMessage.setIJailTimeType("运行耗时");
            alarmMap.put(CIJAILTIMETYPE, alarmCfgMessage.getIJailTimeType());
        }
    }
    private void setAlarmMapIJailStatus(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
        if (alarmCfgMessage.getIJailStatus() != null)
        {
            if ("0".equals(alarmCfgMessage.getIJailStatus()))
            {
                alarmCfgMessage.setIJailStatus("正在监控");
            } else if ("4".equals(alarmCfgMessage.getIJailStatus()))
            {
                alarmCfgMessage.setIJailStatus("停止监控");
            } else
            {
                alarmCfgMessage.setIJailStatus("");
            }
            alarmMap.put(CXIJAILSTATUS, alarmCfgMessage.getIJailStatus());
        } else
        {
            alarmMap.put(CXIJAILSTATUS, "");
        }
    }
    
    private void setAlarmMapOverTime(Map alarmMap,AlarmCfgMessage alarmCfgMessage){
        
        if (alarmCfgMessage.getOverTime() != null)
        {
            alarmMap.put("overTime", alarmCfgMessage.getOverTime());
        } else
        {
            alarmMap.put("overTime", "");
        }
    }
    
    /** 将longTime时间转换成日期字符串,格式:yyyy-MM-dd HH:mm:ss **/
    public String longTimeFormatToDateString(String longTime){
        Date currentTime = new Date(Long.parseLong(longTime));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(currentTime);
    }
    
    public String getRelationProject ( Long userId ) throws RepositoryException
    {
        List resultList = null;
        PermissionSet userPermission = null;
        
        userPermission = ConfigInfoViewService.getRelationProject(userId);
        
        try
        {
            resultList = ConfigInfoViewService.getListProjectMessage();
        } catch (RepositoryException e)
        {
            log.error(METHODSTR+Thread.currentThread().getStackTrace()[1].getMethodName(),e);
        }

        Map proMap = new HashMap();
        StringBuilder adminResult = new StringBuilder();
        int j = 1;
        if(resultList != null){
            for (Iterator iter = resultList.iterator(); iter.hasNext();) {
                ProjectInfo pro = (ProjectInfo) iter.next();
                proMap.put(pro.getName(), pro);
                String projectName = pro.getName();
                if(j==resultList.size()){
                    adminResult.append("'" + projectName + "'");
                }else{
                    adminResult.append("'" + projectName + "',");
                }
                j++;
            }
        }

        // get related project names
            
        Set relatedProjectNames = userPermission.getRelatedProjectNames(proMap.keySet());

        // get related projects
        StringBuilder result = new StringBuilder();
        int i = 1;
        for (Iterator iter = relatedProjectNames.iterator(); iter.hasNext();) {
            String projectName = (String) iter.next();
            if(i==relatedProjectNames.size()){
                result.append("'" + projectName + "'");
            }else{
                result.append("'" + projectName + "',");
            }
            i++;
        }

        if (userId == 3) {
            
            return adminResult.toString();
        } else {
            return result.toString();
        }

    }
    // 导出excel用
    protected static final Map<String, String> PARAM_MAP = new HashMap<String, String>();
    static
    {
        PARAM_MAP.put("工程名称", CPROJECTNAME);
        PARAM_MAP.put("工作流名", CWORKFLOWNAME);
        PARAM_MAP.put("活动名", CACTNAME);
        PARAM_MAP.put("监控标准", CIJAILTIMETYPE);
        PARAM_MAP.put("监控类型", CXIJAILTYPE);
        PARAM_MAP.put("下次监控时间", CXIJAILTIME);
        PARAM_MAP.put("监控状态", CXIJAILSTATUS);
    }

    
}
