package com.ideal.ieai.server.repository.hd.mxgraph;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.SQLUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.importexecl.SusSysInfo;
import com.ideal.ieai.server.util.FileUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * 名称: TemplateForSUSManager.java<br>
 * 描述: 模板步骤数据库操作类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2016年9月24日<br>
 */
public class TemplateForSUSManager
{
    private static final Logger _log = Logger.getLogger(TemplateForSUSManager.class);

    public TemplateForSUSManager()
    {

    }

    private static TemplateForSUSManager _intance = new TemplateForSUSManager();

    public static TemplateForSUSManager getInstance ()
    {
        if (_intance == null)
        {
            _intance = new TemplateForSUSManager();
        }
        return _intance;
    }

    /**
     * 
     * @Title: getTemplateListLoop
     * @Description: TODO(获取模板列表-重试)
     * @param type
     * @param start
     * @param limit
     * @return
     * @throws RepositoryException
     * @return Map<String,Object> 返回类型
     * @throws
     */
    public Map<String, Object> getTemplateListLoop ( TemplateForSUSQueryBean queryBean, int type ) throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getTemplateListLoop", _log, type);
                    res = getTemplateList(con, queryBean, type);
                    // saveBlob(con);
                    break;
                } catch (SQLException e)
                {
                    _log.error("getTemplateListLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "getTemplateListLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return res;
    }

    /**
     * 
     * @Title: getTemplateList
     * @Description: TODO(获取模板列表)
     * @param con
     * @param start
     * @param limit
     * @param type
     * @return
     * @throws SQLException
     * @return Map<String,Object> 返回类型
     * @throws
     */
    public Map<String, Object> getTemplateList ( Connection con, TemplateForSUSQueryBean queryBean, int type ) throws SQLException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        List<Map> list = new ArrayList<Map>();
        String sql = "";
        int pagePara1 = 0;
        int pagePara2 = 0;
        String sqlString = "SELECT IINSTANCENAME,MAX(IID) AS IID FROM IEAI_INSTANCE_VERSION_MODEL T ";
        String sqlWhere = " WHERE T.ISYSTYPE=? ";
        String sqlGroup = "  GROUP BY IINSTANCENAME ";
        String orderString = " ORDER BY  IINSTANCENAME ASC";
        String countString = "SELECT COUNT(1) AS NUM FROM (";
        if (queryBean.getSuspahaseId() > 0)
        {
            sqlWhere += " AND T.ISUSPAHASEID=? ";
        }
        if (DBManager.Orcl_Faimily())
        { // oracle
            sql = SQLUtil.getQueryPageSQLNew("oracle", sqlString + sqlWhere + sqlGroup + orderString);
            pagePara1 = queryBean.getStart() + queryBean.getLimit();
            pagePara2 = queryBean.getStart();
        } else
        {
            sql = SQLUtil.getQueryPageSQLNew("db2", sqlString + sqlWhere + sqlGroup + orderString);
            pagePara1 = queryBean.getStart();
            pagePara2 = queryBean.getStart() + queryBean.getLimit();
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(sql);
            int index = 0;
            ps.setInt(++index, type);
            if (queryBean.getSuspahaseId() > 0)
            {
                ps.setLong(++index, queryBean.getSuspahaseId());
            }
            ps.setInt(++index, pagePara1);
            ps.setInt(++index, pagePara2);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map instanceVersionMap = new HashMap();
                instanceVersionMap.put("instanceName", rs.getString("IINSTANCENAME"));
                instanceVersionMap.put("iid", rs.getLong("IID"));
                list.add(instanceVersionMap);
            }
            int num = 0;
            ps = con.prepareStatement(countString + sqlString + sqlWhere + sqlGroup + ")");
            index = 0;
            ps.setInt(++index, type);
            if (queryBean.getSuspahaseId() > 0)
            {
                ps.setLong(++index, queryBean.getSuspahaseId());
            }
            rs = ps.executeQuery();
            while (rs.next())
            {
                num = rs.getInt("NUM");
            }
            res.put("total", num);
            res.put("dataList", list);
        } catch (SQLException e)
        {
            _log.error("getTemplateList is error ! " + e.getMessage());
            throw e;
        } finally
        {
            if (rs != null)
            {
                rs.close();
            }
            if (ps != null)
            {
                ps.close();
            }
        }
        return res;
    }

    /**
     * 
     * @Title: getTemplateSonListLoop
     * @Description: TODO(获取模板子列表-重试)
     * @param queryBean
     * @param type
     * @return
     * @throws RepositoryException
     * @return Map<String,Object> 返回类型
     * @throws
     */
    public Map<String, Object> getTemplateSonListLoop ( TemplateForSUSQueryBean queryBean, int type ) throws RepositoryException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getTemplateSonListLoop", _log, type);
                    res = getTemplateSonList(con, queryBean);
                    break;
                } catch (SQLException e)
                {
                    _log.error("getTemplateSonListLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "getTemplateSonListLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return res;
    }

    /**
     * 
     * @Title: getTemplateSonList
     * @Description: TODO(获取模板子列表)
     * @param con
     * @param queryBean
     * @return
     * @throws SQLException
     * @return Map<String,Object> 返回类型
     * @throws
     */
    public Map<String, Object> getTemplateSonList ( Connection con, TemplateForSUSQueryBean queryBean ) throws SQLException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        List<Map> list = new ArrayList<Map>();
        String sql = "";
        int pagePara1 = 0;
        int pagePara2 = 0;
        String sqlString = "SELECT T.IID,T.ISERNER,T.IINSTANCENAME,T.IPRENER,T.IACTNAME,T.ISCRIPTID,T2.ISCRIPTNAME FROM IEAI_INSTANCEINFO_MODEL T ,IEAI_SUS_SCRIPT T2  ";
        String sqlWhere = " WHERE  T.ISCRIPTID=T2.IID AND T.IINSTANCEID=? ";
        String orderString = " ORDER BY  T.IID";
        String countString = "SELECT COUNT(1) AS NUM FROM (";
        if (DBManager.Orcl_Faimily())
        { // oracle
            sql = SQLUtil.getQueryPageSQLNew("oracle", sqlString + sqlWhere + orderString);
            pagePara1 = queryBean.getStart() + queryBean.getLimit();
            pagePara2 = queryBean.getStart();
        } else
        {
            sql = SQLUtil.getQueryPageSQLNew("db2", sqlString + sqlWhere + orderString);
            pagePara1 = queryBean.getStart();
            pagePara2 = queryBean.getStart() + queryBean.getLimit();
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            ps = con.prepareStatement(sql);
            int index = 0;
            ps.setLong(++index, queryBean.getInstanceId());
            ps.setInt(++index, pagePara1);
            ps.setInt(++index, pagePara2);
            rs = ps.executeQuery();
            while (rs.next())
            {
                Map instanceVersionMap = new HashMap();
                instanceVersionMap.put("instanceName", rs.getString("IINSTANCENAME"));
                instanceVersionMap.put("iid", rs.getLong("IID"));
                instanceVersionMap.put("iserner", rs.getLong("ISERNER"));
                instanceVersionMap.put("iprener", rs.getString("IPRENER"));
                instanceVersionMap.put("iactName", rs.getString("IACTNAME"));
                instanceVersionMap.put("iscriptName", rs.getString("ISCRIPTNAME"));
                list.add(instanceVersionMap);
            }
            int num = 0;
            ps = con.prepareStatement(countString + sqlString + sqlWhere + ")");
            index = 0;
            ps.setLong(++index, queryBean.getInstanceId());
            rs = ps.executeQuery();
            while (rs.next())
            {
                num = rs.getInt("NUM");
            }
            res.put("total", num);
            res.put("dataList", list);
        } catch (SQLException e)
        {
            _log.error("getTemplateSonList is error ! " + e.getMessage());
            throw e;
        } finally
        {
            if (rs != null)
            {
                rs.close();
            }
            if (ps != null)
            {
                ps.close();
            }
        }
        return res;
    }

    /**
     * 
     * @Title: queryMxgrphXmlClobLoop
     * @Description: TODO(获取clob内容-重试)
     * @param tableName
     * @param instanceID
     * @param type
     * @return
     * @throws RepositoryException
     * @return String 返回类型
     * @throws
     */
    public String queryMxgrphXmlClobLoop ( String tableName, long instanceID, int type ) throws RepositoryException
    {
        String returnValue = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("queryTemplateClobLoop", _log, type);
                    returnValue = this.queryMxgrphXmlClob(con, tableName, instanceID);
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryTemplateClobLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "queryTemplateClobLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: queryMxgrphXmlClob
     * @Description: TODO(获取clob内容)
     * @param conn
     * @param tableName
     * @param instanceID
     * @return
     * @throws SQLException
     * @return String 返回类型
     * @throws
     */
    public String queryMxgrphXmlClob ( Connection conn, String tableName, long instanceID ) throws SQLException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        String returnValue = null;
        String sql = "SELECT T.ICONTENT FROM " + tableName + " T WHERE T.IINSTANCEID=?";

        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, instanceID);
            rs = ps.executeQuery();

            while (rs.next())
            {
                try
                {
                    returnValue = FileUtils.ClobToString(rs.getClob("ICONTENT"));
                } catch (IOException e)
                {
                    throw new SQLException();
                }
            }

        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            try
            {
                if (null != rs)
                {
                    rs.close();
                    rs = null;
                }
                if (null != ps)
                {
                    ps.close();
                    ps = null;
                }
            } catch (SQLException e)
            {
                throw e;
            }
        }

        return returnValue;
    }

    /**
     * 
     * @Title: getCountForInstanceinfoModelLoop
     * @Description: TODO(根据instanceId统计IEAI_INSTANCEINFO_MODEL表数据条数-重试)
     * @param instanceID
     * @param type
     * @return
     * @throws RepositoryException
     * @return int 返回类型
     * @throws
     */
    public int getCountForInstanceinfoModelLoop ( long instanceID, int type ) throws RepositoryException
    {
        int num = 0;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getCountForInstanceinfoModelLoop", _log, type);
                    num = this.getCountForInstanceinfoModel(con, instanceID);
                    break;
                } catch (SQLException e)
                {
                    _log.error("getCountForInstanceinfoModelLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "getCountForInstanceinfoModelLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return num;
    }

    /**
     * 
     * @Title: getCountForInstanceinfoModel
     * @Description: TODO(根据instanceId统计IEAI_INSTANCEINFO_MODEL表数据条数)
     * @param conn
     * @param instanceID
     * @return
     * @throws SQLException
     * @return int 返回类型
     * @throws
     */
    public int getCountForInstanceinfoModel ( Connection conn, long instanceID ) throws SQLException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        int num = 0;
        String sql = "SELECT COUNT(1) AS NUM FROM IEAI_INSTANCEINFO_MODEL T WHERE T.IINSTANCEID=?";

        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, instanceID);
            rs = ps.executeQuery();

            while (rs.next())
            {
                num = rs.getInt("NUM");
            }

        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            try
            {
                if (null != rs)
                {
                    rs.close();
                    rs = null;
                }
                if (null != ps)
                {
                    ps.close();
                    ps = null;
                }
            } catch (SQLException e)
            {
                throw e;
            }
        }

        return num;
    }

    /**
     * 
     * @Title: queryPahaseIdByInstanceIdLoop
     * @Description: TODO(根据instanceid获取阶段ID-重试)
     * @param instanceID
     * @param type
     * @return
     * @throws RepositoryException
     * @return long 返回类型
     * @throws
     */
    public long queryPahaseIdByInstanceIdLoop ( long instanceID, int type ) throws RepositoryException
    {
        long returnValue = 0;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("queryPahaseIdByInstanceIdLoop", _log, type);
                    returnValue = queryPahaseIdByInstanceId(con, instanceID);
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryPahaseIdByInstanceIdLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "queryPahaseIdByInstanceIdLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: queryPahaseIdByInstanceId
     * @Description: TODO(根据instanceid获取阶段ID)
     * @param conn
     * @param instanceID
     * @return
     * @throws SQLException
     * @return long 返回类型
     * @throws
     */
    public long queryPahaseIdByInstanceId ( Connection conn, long instanceID ) throws SQLException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        long returnValue = 0;
        String sql = "SELECT T.ISUSPAHASEID FROM IEAI_INSTANCE_VERSION_MODEL T WHERE T.IID=?";

        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, instanceID);
            rs = ps.executeQuery();

            while (rs.next())
            {
                returnValue = rs.getLong("ISUSPAHASEID");
            }

        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            try
            {
                if (null != rs)
                {
                    rs.close();
                    rs = null;
                }
                if (null != ps)
                {
                    ps.close();
                    ps = null;
                }
            } catch (SQLException e)
            {
                throw e;
            }
        }

        return returnValue;
    }

    /**
     * 
     * @Title: queryInstanceVersionModelByIdLoop
     * @Description: TODO(根据id获取模板主表数据-重试)
     * @param instanceID
     * @param type
     * @return
     * @throws RepositoryException
     * @return SusSysInfo 返回类型
     * @throws
     */
    public SusSysInfo queryInstanceVersionModelByIdLoop ( long instanceID, int type ) throws RepositoryException
    {
        SusSysInfo susSysInfo = null;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("queryInstanceVersionModelByIdLoop", _log, type);
                    susSysInfo = this.queryInstanceVersionModelById(con, instanceID);
                    break;
                } catch (SQLException e)
                {
                    _log.error("queryInstanceVersionModelByIdLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "queryInstanceVersionModelByIdLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return susSysInfo;
    }

    /**
     * 
     * @Title: queryInstanceVersionModelById
     * @Description: TODO(根据id获取模板主表数据)
     * @param conn
     * @param instanceID
     * @return
     * @throws SQLException
     * @return SusSysInfo 返回类型
     * @throws
     */
    public SusSysInfo queryInstanceVersionModelById ( Connection conn, long instanceID ) throws SQLException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        SusSysInfo susSysInfo = null;
        String sql = "SELECT T.IINSTANCENAME,T.IVERSION,T.IUPPERID,T.IPROJECTID,T.ISUSPAHASEID,T.ISYSTYPE,T.ITIME FROM IEAI_INSTANCE_VERSION_MODEL T WHERE T.IID=?";

        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, instanceID);
            rs = ps.executeQuery();

            while (rs.next())
            {
                susSysInfo = new SusSysInfo();
                susSysInfo.setIid(instanceID);
                susSysInfo.setInsName(rs.getString("IINSTANCENAME"));
                susSysInfo.setOldVersion(rs.getLong("IVERSION"));
                susSysInfo.setIupperId(rs.getLong("IUPPERID"));
                susSysInfo.setIprojectid(rs.getLong("IPROJECTID"));
                susSysInfo.setIsuspahaseId(rs.getLong("ISUSPAHASEID"));
                susSysInfo.setIsystype(rs.getInt("ISYSTYPE"));
                susSysInfo.setItime(Long.toString(rs.getLong("ITIME")));
            }

        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            try
            {
                if (null != rs)
                {
                    rs.close();
                    rs = null;
                }
                if (null != ps)
                {
                    ps.close();
                    ps = null;
                }
            } catch (SQLException e)
            {
                throw e;
            }
        }

        return susSysInfo;
    }

    /**
     * 
     * @Title: getTemplateVersionSUSLoop
     * @Description: TODO(获取模板最新版本号-重试)
     * @param sysName
     * @param type
     * @return
     * @throws RepositoryException
     * @return long 返回类型
     * @throws
     */
    public long getTemplateVersionSUSLoop ( String sysName, int type ) throws RepositoryException
    {
        long returnValue = 0;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getTemplateVersionSUSLoop", _log, type);
                    returnValue = getTemplateVersionSUS(con, sysName, Constants.IEAI_SUS);
                    break;
                } catch (SQLException e)
                {
                    _log.error("getTemplateVersionSUSLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "getTemplateVersionSUSLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: getTemplateVersionSUS
     * @Description: TODO(获取最新版本号)
     * @param con
     * @param sysName
     * @param type
     * @return
     * @throws SQLException
     * @return long 返回类型
     * @throws
     */
    public long getTemplateVersionSUS ( Connection con, String sysName, int type ) throws SQLException
    {
        String sql = "SELECT MAX(IVERSION) NUMM FROM IEAI_INSTANCE_VERSION_MODEL WHERE IINSTANCENAME=? AND ISYSTYPE=?";
        PreparedStatement ps = null;
        ResultSet rs = null;
        long oldVersion = 0;
        try
        {
            ps = con.prepareStatement(sql);
            ps.setString(1, sysName);
            ps.setInt(2, type);
            rs = ps.executeQuery();
            while (rs.next())
            {
                oldVersion = rs.getLong("NUMM");
            }

            return oldVersion + 1;
        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            if (ps != null)
            {
                ps.close();
            }
            if (rs != null)
            {
                rs.close();
            }
        }
    }

    /**
     * 
     * @Title: queryInstanceinfoModelById
     * @Description: TODO(根据模板主表ID获取所有子表)
     * @param conn
     * @param instanceID
     * @return
     * @throws SQLException
     * @return List<Map> 返回类型
     * @throws
     */
    public List<Map> queryInstanceinfoModelById ( Connection conn, long instanceID ) throws SQLException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "SELECT t.IID,t.IINSTANCENAME,t.ISERNER,t.IACTNAME FROM IEAI_INSTANCEINFO_MODEL T WHERE T.IINSTANCEID=? ";
        List<Map> returnList = new ArrayList<Map>();
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setLong(1, instanceID);
            rs = ps.executeQuery();

            while (rs.next())
            {
                Map instanceVersionMap = new HashMap();
                instanceVersionMap.put("instanceName", rs.getString("IINSTANCENAME"));
                instanceVersionMap.put("instanceId", instanceID);
                instanceVersionMap.put("iid", rs.getLong("IID"));
                instanceVersionMap.put("iserner", rs.getLong("ISERNER"));
                instanceVersionMap.put("iactName", rs.getString("IACTNAME"));
                returnList.add(instanceVersionMap);
            }

        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            try
            {
                if (null != rs)
                {
                    rs.close();
                    rs = null;
                }
                if (null != ps)
                {
                    ps.close();
                    ps = null;
                }
            } catch (SQLException e)
            {
                throw e;
            }
        }

        return returnList;
    }

    /**
     * 
     * @Title: checkInstanceNameExisitLoop
     * @Description: TODO(判断系统名是否存在-重试)
     * @param con
     * @param sql
     * @return
     * @throws SQLException
     * @return boolean 返回类型
     * @throws
     */
    public boolean checkInstanceNameExisitLoop ( String sql, int type ) throws RepositoryException
    {
        boolean returnValue = false;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("checkInstanceNameExisitLoop", _log, type);
                    returnValue = this.checkInstanceNameExisit(con, sql);
                    break;
                } catch (SQLException e)
                {
                    _log.error("checkInstanceNameExisitLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "checkInstanceNameExisitLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: checkInstanceNameExisit
     * @Description: TODO(判断系统名是否存在)
     * @param con
     * @param sql
     * @return
     * @throws SQLException
     * @return boolean 返回类型
     * @throws
     */
    public boolean checkInstanceNameExisit ( Connection con, String sql ) throws SQLException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean returnValue = false;
        try
        {
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                returnValue = true;
                break;
            }
            return returnValue;
        } catch (SQLException e)
        {
            throw e;
        } finally
        {
            if (ps != null)
            {
                ps.close();
            }
            if (rs != null)
            {
                rs.close();
            }
        }
    }

    /**
     * 
     * @Title: getPhaseBlobLoop
     * @Description: TODO(获取阶段图片-重试)
     * @param type
     * @param url
     * @return
     * @throws RepositoryException
     * @return boolean 返回类型
     * @throws
     */
    public boolean getPhaseBlobLoop ( int type, String url, Boolean forceRefresh ) throws RepositoryException
    {
        boolean returnValue = false;
        for (int i = 0; i < 10; i++)
        {
            try
            {
                Connection con = null;
                try
                {
                    con = DBResource.getConnection("getPhaseBlobLoop", _log, type);
                    returnValue = this.getPhaseBlob(con, url, forceRefresh);
                    break;
                } catch (SQLException e)
                {
                    _log.error("getPhaseBlobLoop is error ! " + e.getMessage());
                    throw new RepositoryException(ServerError.ERR_DB_QUERY);
                } finally
                {
                    DBResource.closeConnection(con, "getPhaseBlobLoop", _log);
                }
            } catch (RepositoryException ex)
            {
                DBResource.throwRepositoryException(i, ex, ServerError.ERR_DB_QUERY);
            }
        }
        return returnValue;
    }

    /**
     * 
     * @Title: getPhaseBlob
     * @Description: TODO(获取阶段图片)
     * @param con
     * @param url
     * @return
     * @throws SQLException
     * @retn boolean 返回类型
     * @throws
     */
    public boolean getPhaseBlob ( Connection con, String url, Boolean forceRefresh ) throws SQLException
    {

        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean isWrite = false;
        try
        {
            String sql = " SELECT IIMG,IID FROM IEAI_SUS_PHASE ";
            ps = con.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next())
            {
                // 读取Blob对象
                Blob blob = (Blob) rs.getBlob(1);
                long iid = rs.getLong(2);
                // Blob对象转化为InputStream流
                java.io.InputStream inputStream = blob.getBinaryStream();
                String imgName = iid + ".jpg";
                // 要写入的文件
                File fileOutput = new File(url + imgName);
                if (forceRefresh)
                {
                    isWrite = true;
                } else if (!fileOutput.exists())
                {
                    isWrite = true;
                }
                if (isWrite)
                {
                    // 文件的写入流的定义
                    FileOutputStream fo = new FileOutputStream(fileOutput);

                    int c;
                    // 读取流并写入到文件中
                    while ((c = inputStream.read()) != -1)
                        fo.write(c);
                    // 流的关闭:
                    fo.close();
                }

            }

        } catch (Exception e)
        {
            throw new SQLException("获取IEAI_SUS_PHASE blob错误！" + e.getMessage());
        } finally
        {
            try
            {
                if (null != ps)
                {
                    ps.close();
                    ps = null;
                }
                if (null != rs)
                {
                    rs.close();
                    rs = null;
                }
            } catch (SQLException e)
            {
                throw e;
            }
        }

        return true;
    }

}
