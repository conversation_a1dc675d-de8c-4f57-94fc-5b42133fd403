DELIMITER ;;
    DROP PROCEDURE IF EXISTS UPPRDE;;
    CREATE PROCEDURE UPPRDE()
    BEGIN
        CREATE TABLE IF NOT EXISTS IEAI_ORG_USER_TIMING (IID NUMERIC (19)   NOT NULL, ORG_NAME VARCHAR (50)  ,ORG_NAME_PARENTS VARCHAR (255)  ,SYNC_TIME VARCHAR (10), SYNC_STATUS NUMERIC (2) DEFAULT 0, CREATE_TIME VARCHAR (20),ORG_IID NUMERIC (20),ORG_DEPART_ID VARCHAR (255), RUN_SERVER_IP VARCHAR (20), CONSTRAINT PK_IEAI_ORG_USER_TIMING PRIMARY KEY (IID));
    END;;
DELIMITER ;
CALL UPPRDE();


DELIMITER ;;
	  DROP PROCEDURE IF EXISTS UPPRDE;;
	  CREATE PROCEDURE UPPRDE()
	  BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_AGENTINFO' AND COLUMN_NAME = 'IHARDWAREINFO') THEN 
			ALTER TABLE IEAI_AGENTINFO ADD IHARDWAREINFO VARCHAR(255) ; 
		END IF;  
	  END;;
DELIMITER ;
CALL UPPRDE();




DELIMITER ;;
DROP PROCEDURE IF EXISTS UPPRDE;;
CREATE PROCEDURE UPPRDE()
BEGIN

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_USER_GROUP' AND COLUMN_NAME = 'ILIMIT') THEN
ALTER TABLE IEAI_USER_GROUP ADD ILIMIT VARCHAR(5);
END IF;
END;;
DELIMITER ;
CALL UPPRDE();
