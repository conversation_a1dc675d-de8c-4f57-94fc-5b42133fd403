package com.ideal.ieai.server.repository.azconfig.thread;

import java.util.HashMap;
import java.util.Map;
import org.apache.log4j.Logger;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.quartz.impl.StdSchedulerFactory;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.azconfig.AzSwitchConfigManager;

/**
 * 
 * <ul>
 * <li>Title: AZSwitchSchedulerThread.java</li>
 * <li>Description:AZ切换定时线程调用</li>
 * <li>Copyright: Copyright 2020</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2020年8月13日
 */
public class AZSwitchSchedulerThread extends Thread
{
    private static Scheduler             scheduler        = getSchedule();
    private static final Logger          _log             = Logger.getLogger(AZSwitchSchedulerThread.class);
    private static AzSwitchConfigManager azManager        = AzSwitchConfigManager.getInstance();
    private static final String          SUCCESS          = "success";
    private static final String          MESSAGE          = "message";
    private static String                triggerName      = "azSwitchStrigger";
    private String                       triggerGroupName = "azSwitchTriggerGroup";

    public Map<String, Object> startAZJob () throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        // 定时任务表达式
        String cron = azManager.getCronValue(Constants.IEAI_AZ, "timingCronValue");
        // 4、将调度器与任务绑定,开始执行任务
        try
        {
            TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, triggerGroupName);
            CronTrigger trigger1 = (CronTrigger) scheduler.getTrigger(triggerKey);
            if (trigger1 == null)
            {
                // 2、创建JobDetail实例，并与SynchronizationCmdbJob类绑定(Job执行内容),name是它们在这个sheduler里面的唯一标识。
                // 要更新一个JobDetail定义，只需要设置一个name相同的JobDetail实例即可,group是一个组织单元，sheduler会提供一些对整组操作的API，比如
                // scheduler.resumeJobs()。 SynchronizationCmdbJob
                JobDetail jobDetail = JobBuilder.newJob(AZSwitchJob.class).withIdentity("azSwitchJob", "azSwitchGroup1")
                        .build();
                // cron表达式//每天
                CronScheduleBuilder cronSchedule = CronScheduleBuilder.cronSchedule(cron);
                // 3、构建Trigger实例
                Trigger trigger = TriggerBuilder.newTrigger().withIdentity(triggerName, triggerGroupName)
                        .withSchedule(cronSchedule).build();  // 一直执行
                // 立即生效
                scheduler.scheduleJob(jobDetail, trigger);
                scheduler.start();
                //_log.info("开始定时AZ切换任务，corn表达式为:" + cron);
                map.put(SUCCESS, true);
                map.put(MESSAGE, "启动成功!");
                return map;
            } else
            {
                scheduler.start();
                //_log.info("唤醒任务,当前任务cron表达式为:" + cron);
                map.put(SUCCESS, true);
                map.put(MESSAGE, "启动成功");
                return map;
            }

        } catch (SchedulerException e)
        {
            //_log.info("AZ切换定时任务异常:" + e.getMessage());
            _log.error("startAZJob() is error："+e.getMessage());
            return map;
        }
    }

    @Override
    public void run ()
    {
        try
        {
            //System.out.println("-------调度任务开始执行AZ切换任务-------");
            
        } catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private static Scheduler getSchedule ()
    {
        StdSchedulerFactory stdSchedulerFactory = new StdSchedulerFactory();
        try
        {
            Scheduler scheduler2 = stdSchedulerFactory.getScheduler();
            return scheduler2;
        } catch (SchedulerException e)
        {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *  
     * <li>Description:修改任务cron表达式</li> 
     * <AUTHOR>
     * 2020年8月13日 
     * @param cronString
     * @return
     * @throws SchedulerException
     * @throws RepositoryException
     * return Map<String,Object>
     */
    public Map<String, Object> modifyCron ( String cronString ) throws SchedulerException, RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        // 如果任务为运行状态
        TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, triggerGroupName);

        CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);

        if (trigger == null)
        {
            _log.info("trigger为空,修改库中cron表达式成功,但任务未启动cron表达式未生效!");
            return map;
        }
        String oldTime = trigger.getCronExpression();
        if (!oldTime.equalsIgnoreCase(cronString))
        {
            _log.info("oldtime与原时间不同,开始修改cron");
            // 修改时间
            // trigger已存在，则更新相应的定时设置
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronString);
            // 按新的cronExpression表达式重新构建trigger
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
            // 重启触发器
            scheduler.rescheduleJob(triggerKey, trigger);
            _log.info("cron表达式修改成功");
            return map;
            // 任务启动但时间相同
        } else
        {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "相同的时间,修改无效");
            return map;
        }
        // 如果任务未启动
    }
}
