Ext.onReady(function() {	
	var exitChild=0;
	var content;
	var version;
    Ext.define('mainTableModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'columnOrder',
            type: 'string'
        },
        {
            name: 'columnName',
            type: 'string'
        },
        {
            name: 'columnType',
            type: 'string'
        },
        {
            name: 'columnLen',
            type: 'int'
        },
        {
            name: 'columnDesc',
            type: 'string'
        },{
            name: 'scriptuuid',
            type: 'string'
        },{
            name: 'tableName',
            type: 'string'
        },{
            name: 'value',
            type: 'string'
        },{
            name: 'operator',
            type: 'int'
        }]
    });
    
    Ext.define('originalModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int'
        },
        {
            name: 'paramRuleOrder',
            type: 'string'
        },
        {
            name: 'paramRuleDesc',
            type: 'string'
        },
        {
            name: 'paramRuleType',
            type: 'string'
        },
        {
            name: 'paramRuleLen',
            type: 'int'
        },
        {
            name: 'paramRuleOut',
            type: 'string'
        },
        {
            name: 'paramRuleIn',
            type: 'string'
        }]
    });
    var publishDesc = Ext.create('Ext.form.field.TextArea', {
		fieldLabel : '申请说明',
		labelWidth : 70,
		padding : 5,
		height : 80,
		value : publishDescText,
		columnWidth:.45,
		readOnly : true,
		autoScroll : true
	});
	var backInfoTA = Ext.create('Ext.form.field.TextArea', {
		fieldLabel: '拒绝原因',
		labelWidth: 70,
		padding: 5,
		height: 80,
		columnWidth:.45,
		autoScroll: true
	});
	var scriptForm = Ext.create('Ext.form.Panel', {
        width: '100%',
//        height: 390,
        border: false,
        layout: 'anchor',
        padding:5, 
        region: 'south',
        items: [        
           {
            border: false,
            layout: 'column',
            items: [publishDesc ,backInfoTA]
        }, {
            border: false,
            layout: 'column',
            height: 40,
    		columnWidth:.45,
            items: [{
                text: '同意申请',
                xtype : 'button',
                cls: 'Common_Btn',
                handler: function(){
                	getVersion(iserviceid);
              }
            },{
                text: '拒绝',
                xtype : 'button',
                cls: 'Common_Btn',
                handler: function(){
                	refuse();
                }
            },
            {
                text: '返回',
                xtype : 'button',
                cls: 'Common_Btn',
                handler: function(){
        		messageWindow.setWidth(contentPanel.getWidth ());
        		messageWindow.setHeight(contentPanel.getHeight ());
        		messageWindow.center();
        		messageWindow.getLoader ().load (
        				{
        					url : 'initGetWorkitemRecordsList.do?activeTabNum=14',
        					autoLoad : true,
        					scripts : true
        				});
        		messageWindow.setTitle ('待办事项');
                }
            }]
           }]
    });
	
      var originalColumns = [
               			{
               				text : '主键',
               				dataIndex : 'iid',
               				width : 40,
               				hidden : true
               			},
               			{
               				text : '顺序',
               				dataIndex : 'paramRuleOrder',
               				width : 50,
               				renderer : function(value, metaData, record, rowIdx,
               						colIdx, store) {
               					metaData.tdAttr = 'data-qtip="'
               							+ Ext.String
               									.htmlEncode(/*
               												 * " 输入：" +
               												 * record.get('paramRuleIn') + "<br>
               												 */"输出列名称："
               											+ record
               													.get('paramRuleOut')
               											+ "<br>排序："
               											+ record
               													.get('paramRuleOrder')
               											+ "<br>描述："
               											+ record
               													.get('paramRuleDesc'))
               							+ '"';
               					return value;
               				}
               			}, {
               				text : '分隔符',
               				dataIndex : 'paramRuleIn',
               				width : 85,
               				editor : {},
               				hidden:true
               			}, {
               				text : '输出列名称',
               				dataIndex : 'paramRuleOut',
               				width : 140,
               				editor : {
               					allowBlank : false
               				},
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					               					
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 输出列名称：" + value) + '"';
               					return value;
               				}
               			},
               			{
               				text : '类型',
               				dataIndex : 'paramRuleType',
               				width : 85,
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
               					if(value==0){
               						value="VARCHAR";
               					}else if(value==1){
               						value="INTEGER";
               					}else if(value==2){
               						value="DECIMAL";
               					}else if(value==3){
               						value="TIMESTAMP";
               					}else if(value==4){
               						value="CLOB";
               					}else{
               						value="VARCHAR"
               					}
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 类型：" + value) + '"';
               					return value;
               				}
               			}, {
               				text : '长度',
               				dataIndex : 'paramRuleLen',
               				width : 85,
               				value:50,
               				editor :  { }
               			}, {
               				text : '别名',
               				dataIndex : 'paramRuleDesc',
               				flex : 1,
               				editor : {
               					allowBlank : true
               				},
               				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					               					
               					metaData.tdAttr = 'data-qtip="'
               						+ Ext.String.htmlEncode(" 别名：" + value) + '"';
               					return value;
               				}
               			}];
    var originalStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'originalModel',
        proxy: {
            type: 'ajax',
            url: 'getAllScriptRuleOutParams.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
   
    originalStore.on('beforeload', function(store, options) {
        var new_params = {
        		scriptId: scriptuuid,
        		iflag:10
        };

        Ext.apply(originalStore.proxy.extraParams, new_params);
    });

    var mainTableStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		pageSize : 10,
		model : 'mainTableModel',
		proxy : {
			type : 'ajax',
			url : 'getColunmsByServiceId.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			}
		}
	});
    mainTableStore.on('beforeload', function(store, options) {
		var new_params = {
			iflag : flag,
			scriptuuid:scriptuuid,
			iserviceid:iserviceid,
			mcFlag:0    //主表标识是0，子表标识是1
		};
		Ext.apply(mainTableStore.proxy.extraParams, new_params);
	});
    var childTableStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 10,
        model: 'mainTableModel',
        proxy: {
            type: 'ajax',
            url: 'getColunmsByServiceId.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    
    childTableStore.on('beforeload', function(store, options) {
        var new_params = {
        		iflag : flag,
        		scriptuuid:scriptuuid,
        		iserviceid:iserviceid,
    			mcFlag:1
        };

        Ext.apply(childTableStore.proxy.extraParams, new_params);
    });
    
    var childtableColumns = [
                   			{
                   				text : '主键',
                   				dataIndex : 'iid',
                   				width : 40,
                   				hidden : true
                   			},
                   			{
                   				text : '顺序',
                   				dataIndex : 'columnOrder',
                   				width : 50,
                   				editor : {},
                   				renderer : function(value, metaData, record, rowIdx,
                   						colIdx, store) {
                   					metaData.tdAttr = 'data-qtip="'
                   							+ Ext.String
                   									.htmlEncode(/*
                   												 * " 输入：" +
                   												 * record.get('paramRuleIn') + "<br>
                   												 */"输出列名称："
                   											+ record
                   													.get('columnName')
                   											+ "<br>排序："
                   											+ record
                   													.get('columnOrder')
                   											+ "<br>描述："
                   											+ record
                   													.get('columnDesc'))
                   							+ '"';
                   					return value;
                   				}
                   			}, {
                   				text : '输出列名称',
                   				dataIndex : 'columnName',
                   				width : 140,
                   				editor : {
                   					allowBlank : false
                   				},
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					               					
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 输出列名称：" + value) + '"';
                   					return value;
                   				}
                   			},
                   			{
                   				text : '类型',
                   				dataIndex : 'columnType',
                   				width : 85,
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
                   					if(value==0){
                   						value="VARCHAR";
                   					}else if(value==1){
                   						value="INTEGER";
                   					}else if(value==2){
                   						value="DECIMAL";
                   					}else if(value==3){
                   						value="TIMESTAMP";
                   					}else if(value==4){
                   						value="CLOB";
                   					}else{
                   						value="VARCHAR"
                   					}
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 类型：" + value) + '"';
                   					return value;
                   				}
                   			}, {
                   				text : '长度',
                   				dataIndex : 'columnLen',
                   				width : 85,
                   				value:50,
                   				editor :  {}
                   			}, {
                   				text : '描述',
                   				dataIndex : 'columnDesc',
                   				flex : 1,
                   				editor : {
                   					allowBlank : true
                   				}
                   			}];
    
    var maintableColumns = [
                   			{
                   				text : '主键',
                   				dataIndex : 'iid',
                   				width : 40,
                   				hidden : true
                   			},
                   			{
                   				text : '顺序',
                   				dataIndex : 'columnOrder',
                   				width : 50,
                   				editor : {},
                   				renderer : function(value, metaData, record, rowIdx,
                   						colIdx, store) {
                   					metaData.tdAttr = 'data-qtip="'
                   							+ Ext.String
                   									.htmlEncode(/*
                   												 * " 输入：" +
                   												 * record.get('paramRuleIn') + "<br>
                   												 */"输出列名称："
                   											+ record
                   													.get('columnName')
                   											+ "<br>排序："
                   											+ record
                   													.get('columnOrder')
                   											+ "<br>描述："
                   											+ record
                   													.get('columnDesc'))
                   							+ '"';
                   					return value;
                   				}
                   			}, {
                   				text : '输出列名称',
                   				dataIndex : 'columnName',
                   				width : 140,
                   				editor : {
                   					allowBlank : false
                   				}
                   			},
                   			{
                   				text : '类型',
                   				dataIndex : 'columnType',
                   				width : 85,
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {					
                   					if(value==0){
                   						value="VARCHAR";
                   					}else if(value==1){
                   						value="INTEGER";
                   					}else if(value==2){
                   						value="DECIMAL";
                   					}else if(value==3){
                   						value="TIMESTAMP";
                   					}else if(value==4){
                   						value="CLOB";
                   					}else{
                   						value="VARCHAR"
                   					}
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 类型：" + value) + '"';
                   					return value;
                   				}
                   			}, {
                   				text : '长度',
                   				dataIndex : 'columnLen',
                   				width : 85,
                   				value:50,
                   				editor :  {}
                   			}, {
                   				text : '别名',
                   				dataIndex : 'columnDesc',
                   				flex : 1,
                   				editor : {
                   					allowBlank : true
                   				}
                   			},{
                   				id:'operatorID',
                   				text : '运算符',
                   				dataIndex : 'operator',
                   				width : 85,
 //                  				editor : operatorCombo,
                   				renderer : function(value, metaData, record, rowIdx, colIdx, store) {
                   					if(value==1){
                   						value="like";
                   					}else if(value==2){
                   						value=">";
                   					}else if(value==3){
                   						value=">=";
                   					}else if(value==4){
                   						value="=";
                   					}else if(value==5){
                   						value="=<";
                   					}else if(value==6){
                   						value="<";
                   					}else{
                   						value=""
                   					}
                   					metaData.tdAttr = 'data-qtip="'
                   						+ Ext.String.htmlEncode(" 运算符：" + value) + '"';
                   					return value;
                   				}               	
                   			},{
                   				text : '值',
                   				id:'valueID',
                   				dataIndex : 'value',
                   				flex : 1,
                   				editor : {
                   					allowBlank : true
                   				}
                   			}];
	  var originalTableName =Ext.create('Ext.form.Panel', {
	        width: '100%',
	        bodyPadding:5,
	        baseCls:'customize_gray_back',
	        items : [{
	            xtype: 'displayfield',
	            fieldLabel: '原始表名',
	            name: 'original'
	        }]
	    });
	    var mainName = new Ext.form.TextField({
	        name: 'mainName',
	        fieldLabel: '主表名',
	        emptyText: '',
	        labelWidth: 70,
	        width:300,
	        regex: /^[0-9a-zA-Z_]{1,}$/,
			regexText:'只允许输入数字、字母、下划线',
//			value: mainTableName,
			readOnly:true
			
	    });
	    var mainTableName =Ext.create('Ext.form.Panel', {
	        width: '100%',
	        bodyPadding: 5,
	        baseCls:'customize_gray_back',
	        items: [mainName]
	    });
	    var childName = new Ext.form.TextField({
	        name: 'childName',
	        fieldLabel: '子表名',
	        emptyText: '',
	        labelWidth: 70,
	        width:300,
	        regex: /^[0-9a-zA-Z_]{1,}$/,
			regexText:'只允许输入数字、字母、下划线',
			value: childTableValue,
			readOnly:true
	    });
	    var childTableName =Ext.create('Ext.form.Panel', {
	        width: '100%',
	        bodyPadding: 5,
	        baseCls:'customize_gray_back',
	        items: [childName]
	    });
	    var originalGrid = Ext.create('Ext.grid.Panel', {
	        width: '100%',
//	        region: 'center',
//	        width: 250,
	        flex: 1,
	        title: "原始表结构",
	        store: originalStore,
	        margin: '0 0 5 0',
//	        selModel: selModel,
	        emptyText: '没有脚本参数',
	        border: true,
	        columnLines: true,
	        columns: originalColumns
	    });
	    
	    var mainTableGrid = Ext.create('Ext.grid.Panel',{
			width: '100%',
//			height : 250,
			flex: 1,
			title : '主表结构',
			store : mainTableStore,
			border : true,
			columnLines : true,
			columns : maintableColumns,
			emptyText : '没有主表结构',
			});
	    var childTableGrid = Ext.create('Ext.grid.Panel',{
			width: '100%',
//			height : 250,
			flex: 1,
			title : '子表结构',
			hidden:true,
			store : childTableStore,
			border : true,
			columnLines : true,
			columns : childtableColumns,
			emptyText : '没有子表结构',

			});
	    var checkbox=Ext.create('Ext.form.field.Checkbox', {
//	    	width: '100%',
//	  		height: 45,
	    	boxLabel  : '是否关联子表',
	        name      : 'topping',
	        inputValue: '1',
	        id        : 'checkbox',
	        readOnly:true
	    })
	  var checkboxFrom=Ext.create('Ext.form.Panel', {
		  		width: '100%',
		  		bodyPadding: 5,
			    baseCls:'customize_gray_back',
	    	    items: [checkbox]
	    });

	   
	var	paramItems = [originalTableName,originalGrid,mainTableName, mainTableGrid,checkboxFrom,childTableName,childTableGrid ];
    var paramsAndFuncDescPanel = Ext.create('Ext.panel.Panel', {
    	region: 'east',
        collapsible : false,
        border: false,
        width: 600,
        layout:'vbox',
        items: paramItems,
   //     cls:'customize_panel_back panel_space_right',
        cls:'window_border panel_space_top panel_space_right'
    });   
    
    var mainP = Ext.create('Ext.panel.Panel', {
//    	minHeight: 80,
        border: true,
        region: 'center',
//        autoScroll: true,
        title: "定义算法",
//        height: contentPanel.getHeight(),
        html: '<textarea id="anaAudicode-edit" value style="height:100%;" placeholder="请输入脚本代码..."></textarea>'
 
    });
    

    var westPanel = Ext.create('Ext.panel.Panel', {
    	region: 'center',
        layout: {
            type: 'border'
        },
        defaults: {
            split: true
        },
        autoScroll: true,
        border: false,
      //  cls:'customize_panel_back panel_space_right',
        cls:'window_border panel_space_top panel_space_left panel_space_right',
        items: [mainP, scriptForm]
    });	
    

	
	 var mainPanel = Ext.create('Ext.panel.Panel', {
		 renderTo: "analyze_area",
	        padding: 5,
	        layout: {
	            type: 'border'
	        },
	        defaults: {
	            split: true
	        },
	        autoScroll: true,
	    	border : true,
	        bodyPadding : 0,
	        bodyCls:'service_platform_bodybg',
	        height: contentPanel.getHeight()-modelHeigth ,
	        items: [westPanel, paramsAndFuncDescPanel]
	    });

	
    var editor = CodeMirror.fromTextArea(document.getElementById('anaAudicode-edit'), {//定义CodeMirror代码编辑器    	
        readOnly:true,
        mode: 'shell',
        lineNumbers: true,
        matchBrackets: true,
        extraKeys: {
            "F11": function(cm) {
              cm.setOption("fullScreen", !cm.getOption("fullScreen"));
            },
            "Esc": function(cm) {
              if (cm.getOption("fullScreen")) cm.setOption("fullScreen", false);
            }
          }
    }

    );
    
    
    
    var editScriptStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 20,
        model: 'editScriptModel',
        proxy: {
            type: 'ajax',
            url: 'queryOneAnalyze.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    editScriptStore.on('beforeload', function(store, options) {
        var queryparams = {
        		scriptuuid: scriptuuid,
        		iserviceid: iserviceid
        };
        Ext.apply(editScriptStore.proxy.extraParams, queryparams);
    });
    editScriptStore.on('load', function(store, options, success) {
            var reader = store.getProxy().getReader();
            mainName.setValue(reader.jsonData.main);
            originalTableName.getForm().findField("original").setValue(reader.jsonData.original);
            var child=reader.jsonData.child;
            console.log("child:"+child)	;
            if(child==null){
            	checkbox.setValue(false);
            	childTableName.hide();
        		childTableGrid.hide();
        		exitChild=0;
            }else{
            	if(child!=reader.jsonData.original){
            		checkbox.setValue(true)
               	 	childName.setValue(child);
            		childTableGrid.setVisible(true);
            		exitChild=1;
            	}else{
            		checkbox.setValue(false);
                	childTableName.hide();
            		childTableGrid.hide();
            		exitChild=0;
            	}            	
            }
            var html= "\n CREATE OR REPLACE PROCEDURE "+reader.jsonData.analyName+" ( AV_FLOW_ID IN NUMBER) AS \n"  ;
            html+=reader.jsonData.analyzeText;
            html+="\n END "+reader.jsonData.analyName+";\n" 
            editor.setOption('value', html);
            status=reader.jsonData.status;          
            content=html;

    });

    editor.setSize(mainP.getWidth()-2, mainP.getHeight()-modelHeigth);  
    contentPanel.on('resize',
    function() {
        editor.getDoc().clearHistory();
        mainPanel.setHeight(contentPanel.getHeight()-modelHeigth );
        mainPanel.setWidth(contentPanel.getWidth());
        editor.setSize(mainP.getWidth()-2, mainP.getHeight()-modelHeigth);
 
    });    
    westPanel.on('resize', function() {
    	editor.getDoc().clearHistory();
    	editor.setSize(mainP.getWidth()-2, mainP.getHeight()-modelHeigth );
    });
    
    function agreeApply() {
    	
    	var mainjsonData = "[";
		var patt1=new RegExp("^[a-zA-Z][a-zA-Z0-9_]*$");
		var m = mainTableStore.getRange();
		for (var i = 0, len = m.length; i < len; i++) {              	
			var ss = Ext.JSON.encode(m[i].data);
			if (i == 0)
				mainjsonData = mainjsonData + ss;
			else
				mainjsonData = mainjsonData + "," + ss;
		}      		
		mainjsonData = mainjsonData + "]";
		
		var childjsonData = "[";
		var child = childTableStore.getRange();
		for (var i = 0, len = child.length; i < len; i++) {        			       			       		
			var ss = Ext.JSON.encode(child[i].data);
			if (i == 0)
				childjsonData = childjsonData + ss;
			else
				childjsonData = childjsonData + "," + ss;
		}
		
		childjsonData = childjsonData + "]";
//		var content = document.getElementById('anaAudicode-edit').value;
		var mainName=mainTableName.getForm().findField("mainName").getRawValue();
		var originalName=originalTableName.getForm().findField("original").getRawValue();
        var childName='';
        if(exitChild==1){
        	childName=childTableName.getForm().findField("childName").getRawValue();
        }
        if(version!=''){
        	
			Ext.Ajax.request (
			{
			    url : 'operWorkitemByiidForAnalyze.do',
			    
			    method : 'POST',
			    params :
			    {
			    	workItemid:workItemid,
			    	scriptuuid : scriptuuid,
					mainData:mainjsonData,
					childData:childjsonData,
					content : content,
					mainName:mainName,
					childName:childName,
					originalName:originalName,
					iserviceid:iserviceid,
					version:version
					
			    },
			    success : function (response, opts)
			    {
				    var success = Ext.decode (response.responseText).success;
				    Ext.Msg.alert ('提示', Ext.decode (response.responseText).messages, function ()
				    {
		    		messageWindow.setWidth(contentPanel.getWidth ());
		    		messageWindow.setHeight(contentPanel.getHeight ());
		    		messageWindow.center();
		    		messageWindow.getLoader ().load (
						{
						    url : 'initGetWorkitemRecordsList.do?activeTabNum=14',
						    autoLoad : true,
						    scripts : true
						});
						messageWindow.setTitle ('待办事项');
				    });
			    }
			});
        }else{
        	Ext.Msg.alert ('提示', '版本信息不允许为空，请填写版本信息');
        }
    }
    
    function refuse(){

    	Ext.MessageBox.wait ("数据处理中...", "进度条");
		var backInfo = backInfoTA.getValue();
		if (fucCheckLength (backInfo.trim ()) < 1)
		{
			Ext.Msg.alert ('提示', "请输入拒绝原因!");
			return;
			
		}
		if (fucCheckLength (backInfo.trim ()) > 2000)
		{
			Ext.Msg.alert ('提示', "拒绝原因长度最大为2000个字符!");
			return;
			
		}
		Ext.Ajax.request (
		{
			url : 'backWorkitemByiidForAnalyze.do',
		    method : 'POST',
		    params :
		    {
		    	workItemid : workItemid,
		        ibackInfo : backInfo.trim(),
		        scriptuuid : scriptuuid
		    },
		    success : function (response, opts)
		    {
			    var success = Ext.decode (response.responseText).success;
			    Ext.Msg.alert ('提示', Ext.decode (response.responseText).message, function ()
			    {
	    		messageWindow.setWidth(contentPanel.getWidth ());
	    		messageWindow.setHeight(contentPanel.getHeight ());
	    		messageWindow.center();
	    		messageWindow.getLoader ().load (
						{
						    url : 'initGetWorkitemRecordsList.do?activeTabNum=14',
						    autoLoad : true,
						    scripts : true
						});
						messageWindow.setTitle ('待办事项');
			    	
			    });
		    }
		});
    
    }
    function getVersion(iserviceid){
    	var v;
    	Ext.Ajax.request (
    			{
    			    url : 'getLastAnalyzeVersion.do',
    			    method : 'POST',
    			    params :
    			    {
    			    	sid:iserviceid
    			    },
    			    success : function (response, opts)
    			    {
    			    	v = Ext.decode (response.responseText).version;
    			    	Ext.MessageBox.prompt("提示", "请输入版本信息，当前版本："+v, function (btnId, text) {  
    			    	    if (btnId == "ok") {  
    			    	        //alert(text);  
    			    	    	version = text;
    			    	    	agreeApply();
    			    	    }  
    			    	    
    			    	},window, true); 
    			    }
    			});
    	
    }
});