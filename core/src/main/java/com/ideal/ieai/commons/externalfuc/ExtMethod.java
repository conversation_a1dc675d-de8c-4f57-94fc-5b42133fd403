package com.ideal.ieai.commons.externalfuc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * The class is for act view method config
 * 
 * <AUTHOR>
 */
public class ExtMethod implements Serializable
{
    public List getExcepList ()
    {
        return excepList;
    }

    public void setExcepList ( List excepList )
    {
        this.excepList = excepList;
    }

    public String getMethodNameContainParam ()
    {
        return methodNameContainParam;
    }

    public void setMethodNameContainParam ( String methodNameContainParam )
    {
        this.methodNameContainParam = methodNameContainParam;
    }

    public List getParamList ()
    {
        return paramList;
    }

    public void setParamList ( List paramList )
    {
        this.paramList = paramList;
    }

    public RetType getRetType ()
    {
        return retType;
    }

    public void setRetType ( RetType retType )
    {
        this.retType = retType;
    }

    public String getMethodName ()
    {
        return methodName;
    }

    public void setMethodName ( String methodName )
    {
        this.methodName = methodName;
    }

    /**
     * method name
     */
    private String  methodName             = "";
    /**
     * method name contain param
     */
    private String  methodNameContainParam = "";
    private RetType retType;
    private List    paramList              = new ArrayList();
    private List    excepList              = new ArrayList();

}

