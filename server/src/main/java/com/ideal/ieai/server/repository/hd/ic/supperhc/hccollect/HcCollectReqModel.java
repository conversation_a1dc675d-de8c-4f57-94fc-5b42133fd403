package com.ideal.ieai.server.repository.hd.ic.supperhc.hccollect;

import com.ideal.ieai.server.repository.hd.ic.supperhc.model.SupperBaseModel;

public class HcCollectReqModel extends SupperBaseModel {

    private String computername;

    private String computerip;

    private String status;

    private String cfgstatus;

    private String checkres;
    private String systemid;

    private boolean flag = false;

    private String querywarn;

    private String querystart;




    public String getComputername() {
        return computername;
    }

    public void setComputername(String computername) {
        this.computername = computername;
    }

    public String getComputerip() {
        return computerip;
    }

    public void setComputerip(String computerip) {
        this.computerip = computerip;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCfgstatus() {
        return cfgstatus;
    }

    public void setCfgstatus(String cfgstatus) {
        this.cfgstatus = cfgstatus;
    }

    public String getCheckres() {
        return checkres;
    }

    public void setCheckres(String checkres) {
        this.checkres = checkres;
    }

    public String getSystemid() {
        return systemid;
    }

    public void setSystemid(String systemid) {
        this.systemid = systemid;
    }

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public String getQuerywarn() {
        return querywarn;
    }

    public void setQuerywarn(String querywarn) {
        this.querywarn = querywarn;
    }

    public String getQuerystart() {
        return querystart;
    }

    public void setQuerystart(String querystart) {
        this.querystart = querystart;
    }
}
