package com.ideal.service.platform.businessSystem;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.common.utils.Tools;
import com.ideal.dubbo.models.AgentModel;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.communication.marshall.Response;
import com.ideal.ieai.core.Environment;
import com.ideal.ieai.core.element.Project;
import com.ideal.ieai.core.io.UnMarshallingException;
import com.ideal.ieai.core.io.project.ProjectReader;
import com.ideal.ieai.core.pack.PackException;
import com.ideal.ieai.core.pack.Package;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.ieaikernel.CommonConfigEnv;
import com.ideal.ieai.server.ieaikernel.PersonalityEnv;
import com.ideal.ieai.server.ieaikernel.ServerEnv;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ClientSessionHelper;
import com.ideal.ieai.server.ieaikernel.kernel.registry.helper.ResponseHelper;
import com.ideal.ieai.server.jobscheduling.repository.batchstart.BatchStartBean;
import com.ideal.ieai.server.jobscheduling.repository.batchstart.BatchStartManager;
import com.ideal.ieai.server.jobscheduling.repository.flowquery.FlowQueryManager;
import com.ideal.ieai.server.poc.project.ProjectBean;
import com.ideal.ieai.server.poc.project.ProjectCommitManager;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.butterfly.ButterflyAbutmentManager;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.cicd.syncsystem.SyncSystem;
import com.ideal.ieai.server.repository.hd.cicd.syncsystem.SyncSystemManager;
import com.ideal.ieai.server.repository.hd.cicd.syncsystem.SyncSystemService;
import com.ideal.ieai.server.repository.hd.ic.businessInsConfig.BusinessInsConfigManager;
import com.ideal.ieai.server.repository.hd.ic.hccomputeroperate.HcComputerForRemoveFromSysModel;
import com.ideal.ieai.server.repository.hd.ic.hccomputeroperate.HcComputerOperateManager;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.AutoEnrollmentAndCloneThread;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBean;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBeanForQuery;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemManager;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BussComputerBean;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.repository.permission.PermissionManager;
import com.ideal.ieai.server.repository.project.ProjectManager;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;
import com.ideal.ieai.server.repository.role.RoleManager;
import com.ideal.ieai.server.repository.role.model.Role;
import com.ideal.ieai.server.repository.syncoperationsystem.SyncOperationSystemManager;
import com.ideal.ieai.server.repository.user.RepRole;
import com.ideal.ieai.server.timetask.repository.manage.TTManageManager;
import com.ideal.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.sql.rowset.serial.SerialException;
import java.io.ByteArrayInputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/**
 * 
 * 名称: BusinessSystemService.java<br>
 * 描述: 业务系统Service<br>
 * 类型: JAVA<br>
 * 最近修改时间:2015年10月13日<br>
 * 
 * <AUTHOR>
 */
public class BusinessSystemService
{
    private static final Logger          log          = Logger.getLogger(BusinessSystemService.class);
    private static final String          BASECONN     = "baseConn";
    private static final String          DBCONN       = "dbConns";
    private static final String          IPERMISSION  = "IPERMISSION";
    private static final String          IPROID       = "IPROID";
    private static final String          IROLEID      = "IROLEID";
    private static final String          ROLLBACKSQLS = "rollbackSqls";
    private static final String          EXECSQLS     = "exeSqls";
    private static final String          SUCCESS      = "success";
    public static final String         MESSAGE        = "message";

    private static BusinessSystemService intance      = new BusinessSystemService();

    public static BusinessSystemService getInstance ()
    {
        if (intance == null)
        {
            intance = new BusinessSystemService();
        }
        return intance;
    }

    public List<BusinessSystemBean> getBusinessSystemNameList ( Long userId, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getBusinessSystemNameList_rebuild(userId, type);
    }

    public List<Map> getSysLvList () throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getSysLvList();
    }

    public Map getBusinessSystemList ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        if (CommonConfigEnv.isSPDBSwitchValue() && StringUtils.isEmpty(businessSystemBeanForQuery.getProperty()))
        {
            log.info(businessSystemBeanForQuery);
            businessSystemBeanForQuery.setProperty("iid");
            businessSystemBeanForQuery.setDirection("DESC");
        }
        return BusinessSystemManager.getInstance().getBusinessSystemListRebuild(businessSystemBeanForQuery, type);
    }

    public Map getComputerList ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getComputerList(businessSystemBeanForQuery, type);
    }

    public Map getComputerListNoSelected ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type,String dataCenter,String batchComputerName )
            throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getComputerListNoSelected(businessSystemBeanForQuery, type,dataCenter,batchComputerName);
    }

    public List<String> checkBusinessSystemName ( List<Map<String, Object>> businessSystems ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().checkBusinessSystemName(businessSystems);
    }

    public List<String> checkBusinessSystemNameAndProtype ( List<Map<String, Object>> businessSystems )
            throws RepositoryException
    {
        return BusinessSystemManager.getInstance().checkBusinessSystemNameAndProtype(businessSystems);
    }

    public List<String> checkTimeTaskSystemNameAndProtype ( List<Map<String, Object>> businessSystems )
            throws RepositoryException
    {
        return BusinessSystemManager.getInstance().checkTimeTaskSystemNameAndProtype(businessSystems);
    }
    
    public List<String> checkSystemCodeNew ( List<Map<String, Object>> businessSystems )
            throws RepositoryException
    {
        return BusinessSystemManager.getInstance().checkSystemCodeNew(businessSystems);
    }

    public boolean deleteBusinessSystem ( Long[] deleteIds, String userName ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        log.debug("user:" + userName + " oper:" + method);
        boolean res = false;
        try
        {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get(BASECONN);
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN);

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                orgSql = BusinessSystemManager.getInstance().organizeBSDELSql(deleteIds, baseConn);

                if ((Boolean) orgSql.get(SUCCESS))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQLS),
                        (List<String>) orgSql.get(ROLLBACKSQLS));
                }else {
                    DBResource.closeConnection(baseConn, method, log);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, method, log);
                    }

                }
            }
        } catch (DBException e)
        {
            log.error(method + " exec is DBException" + e);
        } catch (RepositoryException e)
        {
            log.error(method + " exec is RepositoryException " + e);
        } catch (Exception e)
        {
            log.error(method + " exec is err" + e);
        }

        return res;
    }

    public Boolean saveBusinessSystemIsGD ( List<BusinessSystemBean> businessSystems,  int type ) throws RepositoryException
    {
       
        return BusinessSystemManager.getInstance().insertXmlFile(businessSystems,type);

    }

    public void saveBusinessSystem (List<Map<String, Object>> businessSystems, String userName, long userId) throws Exception
    {
        List<BusinessSystemBean> systemList = transToProjectInfo(businessSystems, userId, userName);
        SyncSystemManager manager = new SyncSystemManager();
        for(BusinessSystemBean systemBean : systemList) {
            SyncSystemService service = new SyncSystemService();
            SyncSystem syncSystem = new SyncSystem();
            syncSystem.setName(systemBean.getSysName());
            syncSystem.setSystemNumber(systemBean.getSystemNumberCicd());
            syncSystem.setSystemAbbreviation(systemBean.getSystemAbbreviation());
            syncSystem.setAsyToUse(systemBean.getAsyToUse());
            syncSystem.setSystemStatus(systemBean.getSystemStatus());
            syncSystem.setIfContinuousIntegration(systemBean.getIfContinuousIntegration());
            syncSystem.setIfAutoDeploy(systemBean.getIfAutoDeploy());
            syncSystem.setAsytouseManager(systemBean.getAsytouseManager());
            syncSystem.setAsytodetOffice(systemBean.getAsytodetOffice());
            List<Long> iIds = manager.getSystemIid(syncSystem.getSystemNumber());
            service.checkSystem(syncSystem);
            if (iIds.size() > 0) {
                for(int i=0;i<iIds.size();i++){
                    service.updateProjectMore(syncSystem, iIds.get(i));
                    service.updateProjectSyncMore(syncSystem, iIds.get(i));
                }
            }else {
                iIds = service.addProjectMore(syncSystem);
                if(iIds.size() > 0) {
                    for(int i=0;i<iIds.size();i++){
                        service.addProjectSyncMore(syncSystem, iIds.get(i));
                    }
                }
            }
        }
    }
    
    public Map<String,Object> saveBusinessSystem ( List<Map<String, Object>> businessSystems, String userName, long userId,
            int type ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean res = false;
        String ipmpErrMessage = "";
        Map<String,Object> respMap = new HashMap();
        try
        {
            respMap.put(Constants.SUS_SUCCESS, res);
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get(BASECONN);
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN);

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                orgSql = BusinessSystemManager.getInstance()
                        .organizeBSSql(transToProjectInfo(businessSystems, userId, userName),userId, baseConn);

                if ((Boolean) orgSql.get(SUCCESS))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQLS),
                        (List<String>) orgSql.get(ROLLBACKSQLS));
                    ipmpErrMessage = String.valueOf(orgSql.get("ipmpNoExistSys"));
                }
            }
            
            respMap.put(Constants.SUS_SUCCESS, res);
            respMap.put(Constants.SUS_MESSAGE, ipmpErrMessage);
        } catch (DBException e)
        {
            log.error(method + " exec is err DBException" + e);
        } catch (RepositoryException e)
        {
            log.error(method + " exec is err RepositoryException " + e);
        } catch (Exception e)
        {
            log.error(method + " exec is err" + e);
        }

        return respMap;

    }

    private List<BusinessSystemBean> transToProjectInfo ( List<Map<String, Object>> projectInfo, long userId,
            String userName )
    {
        List<BusinessSystemBean> res = new ArrayList<BusinessSystemBean>();
        boolean pfIpmpSystemSwitch = ServerEnv.getInstance().getBooleanConfig(PersonalityEnv.PF_IPMP_SYSTEM_BAND_SWITCH, false);
        for (Map<String, Object> bsMap : projectInfo)
        {
            Integer prjType = Integer.parseInt(String.valueOf(bsMap.get("prjType")));
            BusinessSystemBean bsinfo = new BusinessSystemBean();
            bsinfo.setSysName(String.valueOf(bsMap.get("sysName")));
            bsinfo.setPkgid(0);
            bsinfo.setPrjType(prjType);
            bsinfo.setUserId(userId);
            bsinfo.setUserName(userName);
            bsinfo.setSystemCode(String.valueOf(bsMap.get("systemCode")));
            bsinfo.setSystemNumber(String.valueOf(bsMap.get("systemNumber")));
            bsinfo.setSystemId(Long.valueOf(String.valueOf(bsMap.get("systemId"))));
            bsinfo.setDeveloper(String.valueOf(bsMap.get("developer")));
            bsinfo.setDepartment(String.valueOf(bsMap.get("department")));
            bsinfo.setIsysbingagent(String.valueOf(bsMap.get("isysbingagent")));
            bsinfo.setSystemNumberCicd(String.valueOf(bsMap.get("systemNumberCicd")));
//          log.info("icansync:" + ipmpSysNameNoExist);
            
          Integer icansync = 1;
          if((bsMap.get("icansync"))!=null) {
               Boolean bl2 = new Boolean(bsMap.get("icansync").toString());
               if(!bl2) {
                   icansync = 0;
               }
          }
          bsinfo.setIcansync(icansync);
          
            
            if (pfIpmpSystemSwitch && (Constants.IEAI_SUS == prjType ||
                    Constants.IEAI_APM == prjType || Constants.IEAI_AZ == prjType))
            {
                String ipmpSysName = String.valueOf(bsMap.get("ipmpSysName"));
                if (SyncOperationSystemManager.checkEmpty(ipmpSysName))
                {
                    String[] ipmpSysNameArr = ipmpSysName.split(",");
                    StringBuilder ipmpSysNameExist = new StringBuilder();
                    StringBuilder ipmpSysNameNoExist = new StringBuilder();
                    for (int i = 0, len = ipmpSysNameArr.length; i < len; i++)
                    {
                        Map<String, Object> map = new ButterflyAbutmentManager().sendAcquireIPMPDataBySysName(String
                                .valueOf(ipmpSysNameArr[i]));
                        JSONArray jsarry = JSONArray.parseArray(String.valueOf(map.get("data")));
                        log.info("jsarry：" + jsarry);
                        /*
                         * if (!jsarry.isEmpty())
                         * {
                         * bsinfo.setIpmpSysName(String.valueOf(bsMap.get("ipmpSysName")));
                         * bsinfo.setIpmpNoExistSysName("");
                         * 
                         * }else{
                         * bsinfo.setIpmpNoExistSysName(String.valueOf(bsMap.get("ipmpSysName")));
                         * }
                         */
                        if (!jsarry.isEmpty())
                        {
                            if (ipmpSysNameExist.length() > 0 && !"null".equals(ipmpSysNameExist.toString())
                                    && !"".equals(ipmpSysNameExist.toString()))
                            {
                                ipmpSysNameExist.append(",");
                                ipmpSysNameExist.append(ipmpSysNameArr[i]);
                                bsinfo.setSysCode(String.valueOf(JSONObject.parseObject(jsarry.getString(0)).get("sysCode")));
                            } else
                            {
                                ipmpSysNameExist.append(ipmpSysNameArr[i]);
                            }
                        } else
                        {
                            if (ipmpSysNameNoExist.length() > 0 && !"null".equals(ipmpSysNameNoExist.toString())
                                    && !"".equals(ipmpSysNameNoExist.toString()))
                            {
                                ipmpSysNameNoExist.append(",");
                                ipmpSysNameNoExist.append(ipmpSysNameArr[i]);
                            } else
                            {
                                ipmpSysNameNoExist.append(ipmpSysNameArr[i]);
                            }
                        }

                    }
                    log.info("setIpmpSysName:" + ipmpSysNameExist);
                    log.info("setIpmpNoExistSysName:" + ipmpSysNameNoExist);
                    bsinfo.setIpmpSysName(String.valueOf(ipmpSysNameExist));
                    bsinfo.setIpmpNoExistSysName(String.valueOf(ipmpSysNameNoExist));
                } else
                {
                    bsinfo.setIpmpSysName("");
                }
            }

            if(Environment.getInstance().getBooleanConfig(Environment.FJNX_SYNC_SYSTEM_SWITCH, false)) {
                bsinfo.setSystemNumberCicd(String.valueOf(bsMap.get("systemNumberCicd")));
                bsinfo.setSystemAbbreviation(String.valueOf(bsMap.get("systemAbbreviation")));
                bsinfo.setAsyToUse(String.valueOf(bsMap.get("asyToUse")));
                bsinfo.setSystemStatus(String.valueOf(bsMap.get("systemStatus")));
                bsinfo.setIfContinuousIntegration(String.valueOf(bsMap.get("ifContinuousIntegration")));
                bsinfo.setIfAutoDeploy(String.valueOf(bsMap.get("ifAutoDeploy")));
                bsinfo.setAsytouseManager(String.valueOf(bsMap.get("asytouseManager")));
                bsinfo.setAsytodetOffice(String.valueOf(bsMap.get("asytodetOffice")));
            }

            if(Environment.getInstance().getHSBankSwitch()){
                bsinfo.setSystemOwnerTel(String.valueOf(bsMap.get("systemOwnerTel")));
                bsinfo.setAsyToUse(String.valueOf(bsMap.get("asyToUse")));
            }
            
            res.add(bsinfo);

        }

        return res;
    }

    public boolean saveProInfo ( BusinessSystemBean bean, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().saveProInfo(bean, type);
    }

    public BusinessSystemBean getProInfo ( long prjid, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getProInfo(prjid, type);
    }

    public boolean saveBusinessSystemRelation ( List<Map<String, Object>> businessSystems, long systemId,
            String userName, int opersystype ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().saveBusinessSystemRelation_rebuild(businessSystems, systemId,
            userName, opersystype);

    }

    /**   
     * @Title: validBusinessSystemRelationWithTimer   
     * @Description: V4.7.23 校验定时任务下业务系统绑定的ip是否有正在运行的定时任务记录,如存在记录,则不进行删除操作
     * @param deleteIds
     * @param systemId
     * @return      
     * @author: yue_sun
     * @date:   2019-12-04 10:31:28  
     */
    public Map<String, Object> validBusinessSystemRelationWithTimer ( Long[] deleteIds, long systemId )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> resp = new HashMap<String, Object>();
        try
        {
            resp = TTManageManager.getInstance().isContainsRunningTimerTask(deleteIds, systemId);
        } catch (RepositoryException e)
        {
            log.error(method, e);
        }
        return resp;
    }

    public boolean deleteBusinessSystemRelation ( Long[] deleteIds, long systemId, String userName )
            throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean res = false;
        try
        {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get(BASECONN);
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN);

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                orgSql = BusinessSystemManager.getInstance().organizeBSSqlSystemRelation_Del(deleteIds, systemId, userName, baseConn);

                if ((Boolean) orgSql.get(SUCCESS))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQLS),
                        (List<String>) orgSql.get(ROLLBACKSQLS));
                }else {
                    DBResource.closeConnection(baseConn, method, log);
                    for (Connection conn : dbConns)
                    {
                        DBResource.closeConnection(conn, method, log);
                    }

                }

                if (res)
                {
                    // 执行巡检逻辑的删除
                    HcComputerOperateManager computerOperateManager = HcComputerOperateManager.getInstance();
                    /**long serverTypeForSysid = computerOperateManager.getServerTypeForSysid(systemId);*/
                    long serverTypeForSysid = getGroupIdForSysid(systemId);
                    if (serverTypeForSysid == Constants.IEAI_HEALTH_INSPECTION && null != deleteIds
                            && deleteIds.length > 0 && systemId != -1)
                    {
                        for (int mc = 0; mc < deleteIds.length; mc++)
                        {
                            computerOperateManager.deleteForCp(systemId, deleteIds[mc]);
                        }
                    }
                }
            }else {
                DBResource.closeConnection(baseConn, method, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, method, log);
                }
            }
        } catch (RepositoryException e)
        {
            log.error(method + " exec is err RepositoryException " + e);
        } catch (Exception e)
        {
            log.error(method + " exec is err " + e);
        }
        return res;
    }
    
    /**
     * 
     * <li>Description:检查是否存在巡检状态设备</li> 
     * <AUTHOR>
     * 2019年12月12日 
     * @param cpids
     * @param isysid
     * @return
     * @throws RepositoryException
     * return String
     */
    public String checkHcStartForRemoveRelation(Long[] cpids,Long isysid) throws RepositoryException {
        String checkResult = "";
        
        if(cpids!=null && cpids.length>0 && isysid!=null && isysid>0) {
            /**HcComputerOperateManager computerOperateManager = HcComputerOperateManager.getInstance();
            long serverTypeForSysid = computerOperateManager.getServerTypeForSysid(isysid);*/
            long serverTypeForSysid = getGroupIdForSysid(isysid);
            if(serverTypeForSysid== Constants.IEAI_HEALTH_INSPECTION) {
                HcComputerOperateManager instance = HcComputerOperateManager.getInstance();
                List<HcComputerForRemoveFromSysModel> models = instance.getCheckStartHcForComputer(cpids, isysid);
              
                if(models!=null && !models.isEmpty()) {
                    checkResult = buildResStringForCheckHcStart(models);
                }
            }
        }
        return  checkResult;
    }
    
    
    
    public long getGroupIdForSysid ( long iid ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Connection con = null;
        PreparedStatement perstmt = null;
        ResultSet res = null;
        long groupid = -1;
        try
        {

            String sql = "SELECT IGROUPID FROM IEAI_project WHERE IID = ? ";
            con = DBResource.getConnection(method, log, Constants.IEAI_HEALTH_INSPECTION);
            perstmt = con.prepareStatement(sql);
            perstmt.setLong(1, iid);
            res = perstmt.executeQuery();
            while (res.next())
            {
                groupid = res.getLong("IGROUPID");
            }

        } catch (SQLException e)
        {
            log.error("getGroupIdForSysid is error ! " + e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closeConn(con, res, perstmt, method, log);
        }
        return groupid;
    }
    
    
    public String checkNewHcStartForRemoveRelation(Long[] cpids,Long isysid) throws RepositoryException {
        String checkResult = "";
        
        if(cpids!=null && cpids.length>0 && isysid!=null && isysid>0) {
            /** HcComputerOperateManager computerOperateManager = HcComputerOperateManager.getInstance();
            long serverTypeForSysid = computerOperateManager.getServerTypeForSysid(isysid);*/
            long serverTypeForSysid = getGroupIdForSysid(isysid);
            if(serverTypeForSysid== Constants.IEAI_HEALTH_INSPECTION) {
                HcComputerOperateManager instance = HcComputerOperateManager.getInstance();
                List<HcComputerForRemoveFromSysModel> models = instance.getCheckStartNewHcForComputer(cpids, isysid);
                
                if(models!=null && !models.isEmpty()) {
                    checkResult = buildResStringForCheckHcStart(models);
                }
            }
        }
        return  checkResult;
    }
    
    
    private String  buildResStringForCheckHcStart(List<HcComputerForRemoveFromSysModel> models) {
        StringBuilder sbnotice = new StringBuilder();
        for(int i=0;i<models.size();i++) {
            HcComputerForRemoveFromSysModel model = models.get(i);
            if(i==models.size()-1) {
                sbnotice.append(model.getIp());
            }else {
                sbnotice.append(model.getIp()).append(",");
            }
        }
        return sbnotice.toString();
    }
    
    /**
     * 
     * <li>Description:停止移除系统的巡检设备巡检</li> 
     * <AUTHOR>
     * 2019年12月12日 
     * @param cpids
     * @param isysid
     * @param userInfo
     * @return
     * @throws RepositoryException
     * return boolean
     */
    public boolean hcStopForRemoveRelation(Long[] cpids,Long isysid,UserInfo userInfo) throws RepositoryException {
        HcComputerOperateManager instance = HcComputerOperateManager.getInstance();
        Connection conn = null;
        boolean stopResult = false;
        try
        {
            conn = DBResource.getConnection("hcStopForRemoveRelation", log, Constants.IEAI_HEALTH_INSPECTION);
            List<HcComputerForRemoveFromSysModel> models = instance.getCheckStartHcForComputer(cpids, isysid);
            
            if(models!=null && !models.isEmpty()) {
                for(HcComputerForRemoveFromSysModel model:models) {
                    //停止流程
                    Engine.getInstance().killFlow(userInfo, model.getFlowid(), Constants.IEAI_HEALTH_INSPECTION);
                    boolean upStatusFlag = BusinessInsConfigManager.getInstance().updateCpstatusAndCistatus(model.getIp(), conn);
                    if (!upStatusFlag)
                    {
                        throw new Exception("更新巡检状态失败");
                    }
                }
            }
           conn.commit();
           stopResult = true;
        } catch (Exception e)
        {
            log.error("停止需要移除系统的已启动巡检设备出错", e);
        }finally {
            DBResource.closeConnection(conn, "hcStopForRemoveRelation",log);
        }
        return stopResult;
    }
    
    
    

    public Map<String, Object> chekcBusinessSystemRuning ( Long[] deleteIds ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().chekcBusinessSystemRuning(deleteIds);
    }

    public Map<String, Object> chekcBusinessSystemRuningForUT ( Long[] deleteIds, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().chekcBusinessSystemRuningForUT(deleteIds, type);
    }

    public boolean saveBusinessSystemRelationForIC ( List<Map<String, Object>> businessSystems, long systemId,
            String userName ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().saveBusinessSystemRelation_rebuild_ForIC(businessSystems, systemId,
            userName);
    }

    public boolean saveBusinessSystemRelation_wr ( List<Map<String, Object>> businessSystems, long systemId,
            String userName, int opersystype ) throws RepositoryException
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        boolean res = false;
        try
        {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get(BASECONN);
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN);

            if (null != baseConn)
            { // 没有基线源，不可以
                Map orgSql = null;
                orgSql = BusinessSystemManager.getInstance().organizeBSSqlSystemRelation(businessSystems, systemId,
                    userName, opersystype, baseConn);

                if ((Boolean) orgSql.get(SUCCESS))
                {
                    res = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQLS),
                        (List<String>) orgSql.get(ROLLBACKSQLS));
                    boolean cloneHcTemp = Environment.getInstance().getHcBindComAutoStartSwitch();
                    if(cloneHcTemp){
                        toCloneStartHc(systemId, businessSystems, opersystype);
                    }
                }
            }
            
           
            
        } catch (DBException e)
        {
            log.error(method + " exec is error DBException " + e);
        } catch (RepositoryException e)
        {
            log.error(method + " exec is RepositoryException " + e);
        } catch (Exception e)
        {
            log.error(method + " exec is err " + e);
        }

        return res;

    }
    
    
    private void toCloneStartHc(Long systemId, List<Map<String, Object>> businessSystems,int opersystype){
        
        List<Long> hclist = new ArrayList<Long>();
        try{
            for(Map map : businessSystems){
                long cpid = Long.parseLong(map.get("cpId").toString());
                if(!hclist.contains(cpid)){
                    hclist.add(Long.parseLong(map.get("cpId").toString()));
                }
            }
            List<Long> systemIds = new ArrayList();
            systemIds.add(systemId);
            
 
            
            boolean  isCanSync = true;
//            boolean opmBusinessCansyncSHSwitch = ServerEnv.getServerEnv().opmBusinessCansyncSHSwitch();
//            if(opmBusinessCansyncSHSwitch) {
//                Long projectIcanSync = BusinessSystemManager.getInstance(). getProjectIcanSync(systemId);
//                if(projectIcanSync==1) {
//                    isCanSync = true;
//                }else {
//                    isCanSync = false;
//                }
//               
//            }
           
            
            //新增逻辑判断，只有可以被被自动纳管的才可以
            if(opersystype == 7 && isCanSync){
                try{
                    AutoEnrollmentAndCloneThread autoCloneThread = new AutoEnrollmentAndCloneThread(systemIds,StringUtils.join(hclist,","));
                    autoCloneThread.start();
                }catch (Exception e) {
                    log.error("自动克隆巡检模板和启动错误",e);
                }
            }
        }catch (Exception e) {
            log.error("准备克隆巡检启动数据出错！", e);
        }
        
    }

    public Map<String, Object> chekcBusinessSystemUsedForTT ( Long[] deleteIds, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().chekcBusinessSystemUsedForTT(deleteIds, type);
    }

    /**   
     * @Title: freezeProject   
     * @Description: 
     * @param PrjName
     * @param user
     * @param dbType
     * @throws RepositoryException      
     * @author: yue_sun 
     * @date:   2018年3月9日 上午9:43:17   
     */
    public void freezeProject ( String[] prjNames, Boolean isFreeze, UserInfo user, int dbType )
            throws RepositoryException
    {
        for (int i = 0; i < prjNames.length; i++)
        {
            String prjName = prjNames[i];
            BusinessSystemManager.getInstance().freezeProject(prjName, isFreeze, user, dbType);
        }
    }

    public Map<String, Object> getComputerListForSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            AgentModel filter, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getComputerListForSPDB(businessSystemBeanForQuery, filter, type);

    }

    public Map<String, Object> getComputerListNoSelectedForSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            AgentModel filter, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getComputerListNoSelectedForSPDB(businessSystemBeanForQuery, filter,
            type);
    }

    public Map<String, Object> getHostNameForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getHostNameForBusSysSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getIpForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery, int type )
            throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getIpForBusSysSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getSysAdminForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getSysAdminForBusSysSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getSystemNameForBusSysSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getSystemNameForBusSysSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getHostNameForBusSysNoSelectedSPDB (
            BusinessSystemBeanForQuery businessSystemBeanForQuery, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getHostNameForBusSysNoSelectedSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getIpForBusSysNoSelectedSPDB ( BusinessSystemBeanForQuery businessSystemBeanForQuery,
            int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getIpForBusSysNoSelectedSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getSysAdminForBusSysNoSelectedSPDB (
            BusinessSystemBeanForQuery businessSystemBeanForQuery, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getSysAdminForBusSysNoSelectedSPDB(businessSystemBeanForQuery, type);
    }

    public Map<String, Object> getSystemNameForBusSysNoSelectedSPDB (
            BusinessSystemBeanForQuery businessSystemBeanForQuery, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getSystemNameForBusSysNoSelectedSPDB(businessSystemBeanForQuery,
            type);
    }

    private static final String EXECSQL_TEXT     = "exeSqls";
    private static final String ROLLBACKSQL_TEXT = "rollbackSqls";
    private static final String MESSAGE_TEXT     = "message";
    private static final String SUCCESS_TEXT     = "success";

    public List<Role> getRoleByName ( String roleName, Connection baseConn )
    {
        List<Role> list = new ArrayList<Role>();
        try
        {
            list = RoleManager.getInstance().getRoleByName(roleName, baseConn);
        } catch (RepositoryException e)
        {
            log.error("class RoleService method getRoleByName is error:", e);
        }
        return list;
    }

    public int getUserInherit ( String roleId, String userId, Connection baseConn )
    {
        int ret = 0;
        try
        {
            ret = RoleManager.getInstance().getUserInherit(roleId, userId, baseConn);
        } catch (RepositoryException e)
        {
            log.error("class RoleService method getUserInherit is error:", e);
        }
        return ret;
    }

    /**
     * 
     * @Title: saveUserToBussSys   
     * @Description:业务系统绑定用户
     * @param userids
     * @param sysId
     * @return      
     * @author: Administrator 
     * @date:   2019年3月13日 下午4:17:50
     */
    public Map saveUserToBussSys ( String[] userids, String sysId, String tabflag )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map res = new HashMap();
        Connection baseConn = null;
        try
        {
            // 获取数据源信息
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            baseConn = (Connection) connInfo.get(BASECONN);
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN); // 数据库连接集合
            if (baseConn != null)
            {
                Map orgSql = new HashMap();
                if ("1".equals(tabflag))
                {
                    log.debug(method + " bind user tabpanel oper!");
                } else
                {
                    log.debug(method + " bind userGroup tabpanel oper!");
                }
                String ids = org.apache.commons.lang3.StringUtils.join(userids, ",");
                this.organizeUserGroupRoleInfoSql(orgSql, -1, -1, Long.parseLong(sysId), ids, 2, baseConn);
                this.saveUserToBussSysSplit(userids, sysId, ids, orgSql, baseConn);
                if ((Boolean) orgSql.get(SUCCESS_TEXT))
                {
                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQL_TEXT),
                        (List<String>) orgSql.get(ROLLBACKSQL_TEXT)))
                    {
                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "保存成功！");
                    } else
                    {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "保存失败！");
                    }
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "组织sql时出现错误！");
                }
            } else
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            }
        } catch (RepositoryException e)
        {
            log.error(method + " exec is error " + e);
        } catch (DBException e)
        {
            log.error(method + " exec is error DBException " + e);
        } finally
        {
            DBResource.closeConnection(baseConn, method, log);
        }
        return res;
    }

    public void saveUserToBussSysSplit ( String[] userids, String sysId, String ids, Map orgSql, Connection baseConn )
            throws RepositoryException
    {
        for (int i = 0; i < userids.length; i++)
        {
            long roleId = 0;
            String tmpuserId = userids[i];
            // 通过基线数据源获取用户id对应的角色信息，若存在，读取出角色id，角色名称。
            List role = this.getRoleByName(tmpuserId, baseConn);
            if (!role.isEmpty())
            {
                Role r = (Role) role.get(0);
                roleId = r.getRoleId();
                int count = this.getUserInherit(String.valueOf(roleId), tmpuserId, baseConn);
                if (count == 0)
                {
                    // 组织需要绑定关系SQL
                    this.organizeUserGroupRoleInfoSql(orgSql, roleId, Long.parseLong(tmpuserId), Long.parseLong(sysId),
                        ids, 0, baseConn);
                }
            } else
            {
                // 创建角色，同时绑定关系SQL，多写
                roleId = IdGenerator.createIdForExecAct(RepRole.class, baseConn);
                this.organizeUserGroupRoleInfoSql(orgSql, roleId, Long.parseLong(tmpuserId), Long.parseLong(sysId), ids,
                    -1, baseConn);
                // 返回角色信息,组织需要绑定关系SQL
                this.organizeUserGroupRoleInfoSql(orgSql, roleId, Long.parseLong(tmpuserId), Long.parseLong(sysId), ids,
                    0, baseConn);
            }

            // 组织SQL保存角色与业务系统关系到ieai_sys_permission
            this.organizeUserGroupRoleInfoSql(orgSql, roleId, Long.parseLong(tmpuserId), Long.parseLong(sysId), ids, 1,
                baseConn);
        }
    }

    /**
     * 
     * @Title: organizeUserGroupRoleInfoSql   
     * @Description: 業務系統綁定用户sql组织   
     * @param orgSql
     * @param roleId
     * @param userId
     * @param sysId
     * @param ids
     * @param type
     * @param baseConn
     * @return      
     * @author: Administrator 
     * @date:   2019年3月13日 下午4:17:32
     */
    public Map organizeUserGroupRoleInfoSql ( Map orgSql, long roleId, long userId, long sysId, String ids, int type,
            Connection baseConn )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = null;
        List<String> rollbackSqls = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try
        {
            String insertGroupInfoSql = "";
            String rollDeleteSql = "";
            if (orgSql.containsKey(EXECSQL_TEXT))
            {
                exeSqls = (List) orgSql.get(EXECSQL_TEXT);
            } else
            {
                exeSqls = new ArrayList<String>();
            }
            if (orgSql.containsKey(ROLLBACKSQL_TEXT))
            {
                rollbackSqls = (List) orgSql.get(ROLLBACKSQL_TEXT);
            } else
            {
                rollbackSqls = new ArrayList<String>();
            }
            if (type == -1)
            {
                // 创建角色，并返回角色信息
                insertGroupInfoSql = "INSERT INTO IEAI_ROLE (IID,ICALID,INAME,ISHIDDEN) VALUES(" + roleId + ",-1,'"
                        + userId + "'," + "1)";
                rollDeleteSql = " delete from IEAI_ROLE where IID=" + roleId;
                exeSqls.add(insertGroupInfoSql);
                rollbackSqls.add(rollDeleteSql);
            } else if (type == 0)
            {
                // 组织需要绑定关系SQL
                insertGroupInfoSql = "INSERT INTO ieai_userinherit (IROLEID,IUSERID,IGROUPID,IISEXTEND) VALUES("
                        + roleId + "," + userId + ",0,0)";
                rollDeleteSql = " delete from ieai_userinherit where IROLEID=" + roleId + " and IUSERID=" + userId;
                exeSqls.add(insertGroupInfoSql);
                rollbackSqls.add(rollDeleteSql);
            } else if (type == 2)
            {
                this.organizeUserGroupRoleInfoSqlSplit(sysId, ids, exeSqls, rollbackSqls, baseConn);
            } else if (type == 3)
            {
                this.organizeUserGroupRoleInfoSqlSplitNotChk(sysId, ids, exeSqls, rollbackSqls, baseConn);
            } else
            {
                this.organizeUserGroupRoleInfoSqlSplit2(sysId, userId, roleId, exeSqls, rollbackSqls, baseConn);
            }
        } catch (Exception e)
        {
            log.error(method + " is err " + e);
            isSuccess = false;
        } finally
        {
            DBResource.closePSRS(rs, stmt, "organizeUserGroupRoleInfoSql", log);
        }
        if (orgSql.containsKey(SUCCESS_TEXT))
        {
            if (Boolean.parseBoolean(String.valueOf(orgSql.get(SUCCESS_TEXT))))
            {
                orgSql.put(SUCCESS_TEXT, isSuccess);
            }
        } else
        {
            orgSql.put(SUCCESS_TEXT, isSuccess);
        }
        orgSql.put(EXECSQL_TEXT, exeSqls);
        orgSql.put(ROLLBACKSQL_TEXT, rollbackSqls);
        res.put(SUCCESS_TEXT, isSuccess);
        res.put(EXECSQL_TEXT, exeSqls);
        res.put(ROLLBACKSQL_TEXT, rollbackSqls);
        return res;
    }

    public void organizeUserGroupRoleInfoSqlSplit ( long sysId, String ids, List<String> exeSqls,
            List<String> rollbackSqls, Connection baseConn )
    {
        String sqla = "SELECT DISTINCT u.iid as usrid,p.* FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h  WHERE u.iid=h.iuserid AND  p.iroleid=h.iroleid AND P.IPROID="
                + sysId + " AND U.IID NOT IN (" + ids + ")";
        Map map = null;
        List<Map<String, Long>> usr = new ArrayList<Map<String, Long>>();
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try
        {
            stmt = baseConn.prepareStatement(sqla);
            rs = stmt.executeQuery();
            while (rs.next())
            {
                map = new HashMap();
                map.put("uid", rs.getLong("usrid"));
                map.put(IPERMISSION, rs.getLong(IPERMISSION));
                map.put(IPROID, rs.getLong(IPROID));
                map.put(IROLEID, rs.getLong(IROLEID));
                map.put("IID", rs.getLong("IID"));
                usr.add(map);
            }
        } catch (Exception e)
        {
            log.error("organizeUserGroupRoleInfoSqlSplit is err " + e);
        } finally
        {
            DBResource.closePSRS(rs, stmt, "organizeUserGroupRoleInfoSqlSplit", log);
        }
        String insertGroupInfoSql = "";
        String rollDeleteSql = "";
        if (!usr.isEmpty())
        {
            for (int i = 0; i < usr.size(); i++)
            {
                Map<String, Long> uidMap = usr.get(i);
                // 组织需要绑定关系SQL
                insertGroupInfoSql = "delete from ieai_sys_permission where iid=" + uidMap.get("IID");
                rollDeleteSql = " insert into ieai_sys_permission(IPERMISSION,IPROID,IROLEID,IID) values("
                        + uidMap.get(IPERMISSION) + "," + uidMap.get(IPROID) + "," + uidMap.get(IROLEID) + ","
                        + uidMap.get("IID") + ")";
                exeSqls.add(insertGroupInfoSql);
                rollbackSqls.add(rollDeleteSql);
            }
        }
    }

    public void organizeUserGroupRoleInfoSqlSplit2 ( long sysId, long userId, long roleId, List<String> exeSqls,
            List<String> rollbackSqls, Connection baseConn )
    {
        String insertGroupInfoSql = "";
        String rollDeleteSql = "";
        PreparedStatement stmt = null;
        ResultSet rs = null;
        boolean isExists = false;
        String sqla = "SELECT COUNT(1) FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h  WHERE u.iid=h.iuserid AND  p.iroleid=h.iroleid AND P.IPROID="
                + sysId + " AND U.IID =" + userId;
        // 组织需要绑定业务系统关系SQL
        try
        {
            stmt = baseConn.prepareStatement(sqla);
            rs = stmt.executeQuery();
            while (rs.next())
            {
                if (rs.getLong(1) > 0)
                {
                    isExists = true;
                }
            }
            if (!isExists)
            {
                long iid = IdGenerator.createId("IEAI_SYS_PERMISSION", baseConn);
                insertGroupInfoSql = "INSERT INTO ieai_sys_permission (IPERMISSION,IPROID,IROLEID,IID) VALUES(1,"
                        + sysId + "," + roleId + "," + iid + ")";
                rollDeleteSql = " delete from ieai_sys_permission where  iid=" + iid;
                exeSqls.add(insertGroupInfoSql);
                rollbackSqls.add(rollDeleteSql);
            }
        } catch (Exception e)
        {
            log.error("organizeUserGroupRoleInfoSqlSplit2 is err " + e);
        } finally
        {
            DBResource.closePSRS(rs, stmt, "organizeUserGroupRoleInfoSqlSplit2", log);
        }

    }

    public void delUserSysBindMore ( long sysId ) throws Exception
    {
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");

        if (null != baseConn)
        { // 没有基线源，不可以
            Map orgSql = delUserSysBind(baseConn,sysId);
            if ((Boolean) orgSql.get("success"))
            {
                DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"), (List<String>) orgSql.get("rollbackSqls"));
            }
        }
    }

    public Map delUserSysBind ( Connection baseConn, long sysId )
    {
        String sqla = "SELECT DISTINCT u.iid as usrid,p.* FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h  WHERE u.iid=h.iuserid AND  p.iroleid=h.iroleid AND P.IPROID="
                + sysId;
        Map map = null;
        List<Map<String, Long>> usr = new ArrayList<Map<String, Long>>();
        PreparedStatement stmt = null;
        ResultSet rs = null;
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        try
        {
            stmt = baseConn.prepareStatement(sqla);
            rs = stmt.executeQuery();
            while (rs.next())
            {
                map = new HashMap();
                map.put("uid", rs.getLong("usrid"));
                map.put(IPERMISSION, rs.getLong(IPERMISSION));
                map.put(IPROID, rs.getLong(IPROID));
                map.put(IROLEID, rs.getLong(IROLEID));
                map.put("IID", rs.getLong("IID"));
                usr.add(map);
            }
        } catch (Exception e)
        {
            log.error("delUserSysBind is err " + e);
            isSuccess = false;
        } finally
        {
            DBResource.closePSRS(rs, stmt, "delUserSysBind", log);
        }
        String insertGroupInfoSql = "";
        String rollDeleteSql = "";
        if (!usr.isEmpty())
        {
            for (int i = 0; i < usr.size(); i++)
            {
                Map<String, Long> uidMap = usr.get(i);
                // 组织需要绑定关系SQL
                insertGroupInfoSql = "delete from ieai_sys_permission where iid=" + uidMap.get("IID");
                rollDeleteSql = " insert into ieai_sys_permission(IPERMISSION,IPROID,IROLEID,IID) values("
                        + uidMap.get(IPERMISSION) + "," + uidMap.get(IPROID) + "," + uidMap.get(IROLEID) + ","
                        + uidMap.get("IID") + ")";
                exeSqls.add(insertGroupInfoSql);
                rollbackSqls.add(rollDeleteSql);
            }
        }
        res.put("success", isSuccess);
        res.put("exeSqls", exeSqls);
        res.put("rollbackSqls", rollbackSqls);
        return res;
    }

    /**
     * 
     * @Title: getSysConfigUser   
     * @Description: 获取业务系统对应用户 id
     * @param sysId
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: Administrator 
     * @date:   2019年3月13日 下午4:16:56
     */
    public List getSysConfigUser ( String sysId, int type ) throws RepositoryException
    {
        boolean dataFlag = ServerEnv.getInstance().getBooleanConfigNew2(Environment.YC_BUSS_ROLE_RELATION_SWITCH, false);
        if(dataFlag) {
            return BusinessSystemManager.getInstance().getSysConfigUserForUser(sysId, type);
        }else {
            return BusinessSystemManager.getInstance().getSysConfigUser(sysId, type);
        }
    }

    /**
     * 获取业务系统对应用户组 id
     * @Title: getSysConfigGUser   
     * @Description: 获取业务系统对应用户组 id   
     * @param sysId
     * @param type
     * @return
     * @throws RepositoryException      
     * @author: Administrator 
     * @date:   2019年3月13日 下午4:17:15
     */
    public List getSysConfigGUser ( String sysId, int type ) throws RepositoryException
    {
        return BusinessSystemManager.getInstance().getSysConfigGUser(sysId, type);
    }

    /**
     * 
     * @Title: saveUserToBussSysForGroupNotChk   
     * @Description: 业务系统绑用户组内用户，全部不选用户时，清理原有勾选数据入口
     * @param oldUserids
     * @param sysId
     * @return      
     * @author: Administrator 
     * @date:   2019年12月6日 上午7:59:32
     */
    public Map saveUserToBussSysForGroupNotChk ( String[] oldUserids, String sysId )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map res = new HashMap();
        Connection baseConn = null;
        try
        {
            // 获取数据源信息
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            baseConn = (Connection) connInfo.get(BASECONN);
            List<Connection> dbConns = (List<Connection>) connInfo.get(DBCONN); // 数据库连接集合
            if (baseConn != null)
            {
                Map orgSql = new HashMap();
                String ids = org.apache.commons.lang3.StringUtils.join(oldUserids, ",");
                this.organizeUserGroupRoleInfoSql(orgSql, -1, -1, Long.parseLong(sysId), ids, 3, baseConn);
                if ((Boolean) orgSql.get(SUCCESS_TEXT))
                {
                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXECSQL_TEXT),
                        (List<String>) orgSql.get(ROLLBACKSQL_TEXT)))
                    {
                        res.put(SUCCESS_TEXT, true);
                        res.put(MESSAGE_TEXT, "保存成功！");
                    } else
                    {
                        res.put(SUCCESS_TEXT, false);
                        res.put(MESSAGE_TEXT, "保存失败！");
                    }
                } else
                {
                    res.put(SUCCESS_TEXT, false);
                    res.put(MESSAGE_TEXT, "组织sql时出现错误！");
                }
            } else
            {
                res.put(SUCCESS_TEXT, false);
                res.put(MESSAGE_TEXT, "没有基线源, 无法保存！");
            }
        } catch (RepositoryException e)
        {
            log.error(method + " exec is error " + e);
        } catch (DBException e)
        {
            log.error(method + " exec is error DBException " + e);
        } finally
        {
            DBResource.closeConnection(baseConn, method, log);
        }
        return res;
    }

    /**
     * 
     * @Title: organizeUserGroupRoleInfoSqlSplitNotChk   
     * @Description:业务系统绑用户组内用户，全部不选用户时，清理原有勾选数据
     * @param sysId
     * @param ids
     * @param exeSqls
     * @param rollbackSqls
     * @param baseConn      
     * @author: Administrator 
     * @date:   2019年12月6日 上午7:58:33
     */
    public void organizeUserGroupRoleInfoSqlSplitNotChk ( long sysId, String ids, List<String> exeSqls,
            List<String> rollbackSqls, Connection baseConn )
    {
        String sqla = "SELECT DISTINCT u.iid as usrid,p.* FROM  ieai_user u ,ieai_sys_permission p,ieai_userinherit h  WHERE u.iid=h.iuserid AND  p.iroleid=h.iroleid AND P.IPROID="
                + sysId + " AND U.IID IN (" + ids + ")";
        Map map = null;
        List<Map<String, Long>> usr = new ArrayList<Map<String, Long>>();
        PreparedStatement stmt = null;
        ResultSet rs = null;
        try
        {
            stmt = baseConn.prepareStatement(sqla);
            rs = stmt.executeQuery();
            while (rs.next())
            {
                map = new HashMap();
                map.put("uid", rs.getLong("usrid"));
                map.put(IPERMISSION, rs.getLong(IPERMISSION));
                map.put(IPROID, rs.getLong(IPROID));
                map.put(IROLEID, rs.getLong(IROLEID));
                map.put("IID", rs.getLong("IID"));
                usr.add(map);
            }
        } catch (Exception e)
        {
            log.error("organizeUserGroupRoleInfoSqlSplitNotChk is err " + e);
        } finally
        {
            DBResource.closePSRS(rs, stmt, "organizeUserGroupRoleInfoSqlSplitNotChk", log);
        }
        String insertGroupInfoSql = "";
        String rollDeleteSql = "";
        if (!usr.isEmpty())
        {
            for (int i = 0; i < usr.size(); i++)
            {
                Map<String, Long> uidMap = usr.get(i);
                // 组织需要绑定关系SQL
                insertGroupInfoSql = "delete from ieai_sys_permission where iid=" + uidMap.get("IID");
                rollDeleteSql = " insert into ieai_sys_permission(IPERMISSION,IPROID,IROLEID,IID) values("
                        + uidMap.get(IPERMISSION) + "," + uidMap.get(IPROID) + "," + uidMap.get(IROLEID) + ","
                        + uidMap.get("IID") + ")";
                exeSqls.add(insertGroupInfoSql);
                rollbackSqls.add(rollDeleteSql);
            }
        }
    }

    public  Map<String, Object> getBusinessSystemSimpleList (String systemCode ) throws RepositoryException
    {
       
        return BusinessSystemManager.getInstance().getBusinessSystemSimpleList(systemCode);
       
    }

    public String getAgentListBySysId ( String batchComputerName, long systemId, int type ) throws RepositoryException
    {
        String uncheckips = "";
        String[]  computerArr = batchComputerName.split("\n");
        List list =  BusinessSystemManager.getInstance().getAgentListBySysId(systemId,type);
        for(int i = 0; i<computerArr.length;i++){
            if(!list.contains(computerArr[i])){
                if(i==computerArr.length-1){
                    uncheckips+=computerArr[i];
                }else{
                    uncheckips+=computerArr[i]+"\n";
                }
            }
        }
        return uncheckips;
    }

    public String filterAllIp ( String allip, String uncheckip )
    {
        List newlist = new ArrayList();
        String ipnewstr = "";
        String[] allipArr = allip.split("\n");
        String[] uncheckipArr = uncheckip.split("\n");
        List<String> alliplist = Arrays.asList(allipArr);
        List<String> uncheckiplist = Arrays.asList(uncheckipArr);
        for(String str : alliplist){
            if(!uncheckiplist.contains(str)){
                newlist.add(str);
            }
        }
        for(int i = 0;i< newlist.size();i++){
            if(i == newlist.size()-1){
                ipnewstr= ipnewstr+newlist.get(i);
            }else{
                ipnewstr= ipnewstr+newlist.get(i)+"\n";
            }

        }
        return ipnewstr;
    }
    public Map<String, Object> rollbackPKG(BatchStartBean model, byte[] pkg,String _sessionId,String prjName) {
        Map<String, Object> map = new HashMap();
        BatchStartManager instance = BatchStartManager.getInstance();
        boolean state = false;

        try {
            int[] userInfo = FlowQueryManager.getInstance().getDoubleCheckUserInfo(model.getCheckUser(), model.getPassWord());
            if (userInfo[0] == 0) {
                map.put(SUCCESS, false);
                map.put(MESSAGE, "审核用户不存在或密码错误！");
            } else if (userInfo[0] == -1) {
                map.put(SUCCESS, false);
                map.put(MESSAGE, "审核用户已被锁定！");
            }   else if (!model.getLoginUser().equals(model.getCheckUser())){
                map.put(SUCCESS, false);
                map.put(MESSAGE, "输入的用户名密码与当前登录人不同,无法退回到当前版本！");
                return map;
            } else {
                state =importOperationPkgSystem(pkg,_sessionId,prjName);
                long PrjId = ProjectManagerForMultiple.getRepPrjId1();
            if(state){

                map.put("PrjId", PrjId);
                map.put(SUCCESS, true);
                map.put(MESSAGE, "回退版本成功！");
            }else {
                map.put(SUCCESS, false);
                map.put(MESSAGE, "回退版本失败！");
            }
            }
        } catch (RepositoryException  e) {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "回退版本失败！");
            log.error("回退版本失败!"+e);
        }
        return map;
    }
    /**
     *
     * <li>Description:业务系统维护导入pkg文件</li>
     * <AUTHOR>
     * 2022年03月29日
     * @return
     * return boolean
     */
    public boolean importOperationPkgSystem(byte[] pkg,String sessionID,String prjName){
        boolean flag = false;

        UserInfo user= ClientSessionHelper.getUser(sessionID);//获取当前用户根据用户id
        String comment="";
        try
        {
            boolean result = PermissionManager.getInstance()
                    .checkUserPermission(user.getId(), Constants.PRJ, prjName,
                            Constants.PERM_UPLOAD_PROJECT);
            if (!result)
            {
                log.info("Project uploaded successfully:"+  ServerError.ERR_PERMISSION_FORBID);

                return false;
            }
        } catch (RepositoryException ex)
        {
            log.info("Project uploaded successfully:"+  ServerError.ERR_ACCESS_DB);

            return false;
        }
        /** call the service */
        try
        {
            log.info("user upload project " + prjName);
            log.info("user upload project " + prjName);
            // 在此进行控制 如果开启了工程上载审核开关，则使用上载审核过程的程序逻辑进行处理
            ByteArrayInputStream input = Package.getInstance().getPrjAdpInputStream(new ByteArrayInputStream(pkg),
                    prjName, true);

            Project project = ProjectReader.getInstance().loadProjectBaseInfo(input);
            if(ServerEnv.getServerEnv().getJobSchedulerReview())
            {
                if (project.getProjectType().getProjectType() == 1.0)
                {
                    ProjectBean bean = new ProjectBean();
                    bean.setIname(prjName);
                    bean.setImajver(project.getVersion().getMajor());
                    bean.setIminver(project.getVersion().getMinor());
                    bean.setIcomment(comment);
                    bean.setIuuid(UUID.uuid());
                    bean.setIuploaduserid(ClientSessionHelper.getUserId(sessionID));
                    bean.setIgroupid(Math.round(project.getProjectTpye()));
                    bean.setProtype(project.getProjectTpye());
                    try
                    {
                        Long pkgid = ProjectCommitManager.getInstance().insert(Constants.IEAI_IEAI, pkg);
                        bean.setIpkgcontentid(pkgid);
                        ProjectCommitManager.getInstance().insert(Constants.IEAI_IEAI, bean);
                    } catch (SerialException e)
                    {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    } catch (SQLException e)
                    {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                } else
                {
                    //上傳方法
                    ProjectManager.getInstance().updateProject(prjName, user, comment, pkg, project.getProjectTpye());
                    log.info("###########UpdateProject.executeService is start!projectname="+prjName+",user="+user+",pkg="+pkg+",projectTpye="+project.getProjectTpye()+" ################");

                }

            }else
            {
                //上傳方法
                ProjectManager.getInstance().updateProject(prjName, user, comment, pkg, project.getProjectTpye());
                log.info("###########UpdateProject.executeService is start!projectname="+prjName+",user="+user+",pkg="+pkg+",projectTpye="+project.getProjectTpye()+" ################");

            }
            Response resp = ResponseHelper.getReturnValueResponse((Object)(ProjectManager.getInstance().getLastPrjUuidByPrjName(prjName)));
            log.info("Project uploaded successfully");
            flag=true;

        } catch (RepositoryException ex)
        {
            log.info("user upload project unsucceeded");
            /*   return ResponseHelper.getErrorResponse(ex.getServerError());*/
        } catch (PackException ex)
        {
            log.info("user upload project unsucceeded");
            /*        return ResponseHelper.getErrorResponse(ServerError.ERR_INV_PACKAGE);*/
        } catch (UnMarshallingException e)
        {
            log.info( ""+ e.getMessage());
            e.printStackTrace();
            /*    return ResponseHelper.getErrorResponse(ServerError.ERR_INV_PACKAGE);*/
        } catch (ServerException e) {
            e.printStackTrace();
        }
        return flag;

    }

    public Long[] queryBusinessSystemIds(String deleteSysCodess, int itype) throws RepositoryException {
          return  BusinessSystemManager.getInstance().queryBusinessSystemIds(deleteSysCodess,itype);
    }

    /**
     * 根据领导要求，多库多写处理数据
     * @param bussComputerBean
     * @return
     */
    public boolean saveBusinessAndComputerData(BussComputerBean bussComputerBean){
        boolean flag = true;
        try {
            Map<String, Object> connInfo = Tools.getConnectionInfo();
            Connection baseConn = (Connection) connInfo.get("baseConn");
            if (null != baseConn) { // 没有基线源，不可以
                List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
                Map<String,Object> orgSql = BusinessSystemManager.getInstance().saveBusinessAndComputerData(bussComputerBean, baseConn);
                if ((Boolean) orgSql.get(SUCCESS_TEXT)){
                    if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get("exeSqls"), (List<String>) orgSql.get("rollbackSqls"))){
                        DBResource.closeConnection(baseConn, "saveBusinessAndComputerData", log);
                    } else {
                        DBResource.closeConnection(baseConn, "saveBusinessAndComputerData", log);
                        flag=false;
                    }
                } else{
                    DBResource.closeConnection(baseConn, "delBusinessAndComputerData", log);
                    for (Connection conn : dbConns){
                        DBResource.closeConnection(conn, "delBusinessAndComputerData", log);
                    }
                    flag=false;
                }
            } else{
                flag=false;
            }
        }catch (Exception e) {
            flag=false;
            e.printStackTrace();
        }
        return flag;
    }
}
