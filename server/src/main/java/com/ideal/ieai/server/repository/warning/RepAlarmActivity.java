package com.ideal.ieai.server.repository.warning;

public class RepAlarmActivity
{

    public long   iid;
    public long   iflowId;
    public long   ipfjId;
    public String iactName;
    public String iflowName;
    public String iactType;

    public String getIactType() {
		return iactType;
	}

	public void setIactType(String iactType) {
		this.iactType = iactType;
	}

	public String getIactName ()
    {
        return iactName;
    }

    public void setIactName ( String iactName )
    {
        this.iactName = iactName;
    }

    public long getIflowId ()
    {
        return iflowId;
    }

    public void setIflowId ( long iflowId )
    {
        this.iflowId = iflowId;
    }

    public String getIflowName ()
    {
        return iflowName;
    }

    public void setIflowName ( String iflowName )
    {
        this.iflowName = iflowName;
    }

    public long getIid ()
    {
        return iid;
    }

    public void setIid ( long iid )
    {
        this.iid = iid;
    }

    public long getIpfjId ()
    {
        return ipfjId;
    }

    public void setIpfjId ( long ipfjId )
    {
        this.ipfjId = ipfjId;
    }

}
