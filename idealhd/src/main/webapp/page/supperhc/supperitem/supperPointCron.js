
function setSupperPointCronRunTime(hcRuntime,index,chkpoint_grid_panel_param)
		{	
			var secondStepForm = Ext.create('Ext.form.Panel', {
				id : 'secondStepForm',
				region : 'north',
				layout : 'anchor',
				padding : '0 0 0 0',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
//					margin : '-7 0 5 0',
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'secondExpTextAppoint1_supperpoint',
						name : 'secondExpRadio',
						checked : true,
						boxLabel : '每秒 允许的通配符[, - * /]', 
						inputValue : 1,
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		everyTime('secondExpText');
		                    	}
		                    }
		                }
					} ,
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
//						padding : '0 0 0 30',
						items : [ {
							xtype : 'radiofield',
							id : 'secondExpTextAppoint2_supperpoint',
							name : 'secondExpRadio',
							boxLabel : '周期从',
							inputValue : 2,
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		cycle('secondExpText','cycleSecondStart','cycleSecondEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleSecondStart',
							name : 'cycleSecondStart',
							minValue: 1,
							maxValue: 58,
							xtype : 'numberfield',
							allowBlank : false,
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('secondExpTextAppoint2_supperpoint').checked;
			                		if(appointChecked){
			                    		cycle('secondExpText','cycleSecondStart','cycleSecondEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleSecondEnd',
							name : 'cycleSecondEnd',
							minValue: 2,
							maxValue: 59,
							allowBlank : false,
							value: 2,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('secondExpTextAppoint2_supperpoint').checked;
			                		if(appointChecked){
			                    		cycle('secondExpText','cycleSecondStart','cycleSecondEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "秒"
				        } ,
				        
				        ,{
							xtype : 'toolbar',
							border : false,
							dock : 'top',
//							padding : '0 0 0 30',
							items : [ {
								xtype : 'radiofield',
								id : 'secondExpTextAppoint3_supperpoint',
								name : 'secondExpRadio',
								boxLabel : '从',
								inputValue : 3,
								listeners: {
				                    change: function (field, checked) {
				                    	var appointChecked = Ext.getCmp('secondExpTextAppoint3_supperpoint').checked;
				                		if(appointChecked){
				                    		startOn('secondExpText','execSecondStart','execSecondEnd');
				                    	}
				                    }
				                }
							},{
								id : 'execSecondStart',
								name : 'execSecondStart',
								minValue: 0,
								maxValue: 59,
								xtype : 'numberfield',
								allowBlank : false,
								value: 0,
								width : 80,
								listeners: {
				                    change: function (field, checked) {
				                    	var appointChecked = Ext.getCmp('secondExpTextAppoint3_supperpoint').checked;
				                		if(appointChecked){
				                    		startOn('secondExpText','execSecondStart','execSecondEnd');
				                    	}
				                    }
				                }
							},{
					            xtype: "label",
					            text: "秒开始，每"
					        },{
					        	id : 'execSecondEnd',
								name : 'execSecondEnd',
								minValue: 1,
								maxValue: 59,
								allowBlank : false,
								value: 1,
								xtype : 'numberfield',
								width : 80,
								listeners: {
				                    change: function (field, checked) {
				                    	if(checked){
				                    		startOn('secondExpText','execSecondStart','execSecondEnd');
				                    	}
				                    }
				                }
							},{
					            xtype: "label",
					            text: "秒执行一次"
					        }
							]
				        }]
				}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
//					margin : '-10 0 0 0',
					items : [{
						xtype : 'radiofield',
						id : 'secondExpTextAppoint4_supperpoint',
						name : 'secondExpRadio',
						boxLabel : '指定',
						inputValue : 4,
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		Ext.getCmp("firstSencondCheckBox_supperpoint").setValue(true);
		                    		var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                    		if(appointChecked){
		                    			createCheckBoxCron('secondExpText','second');
		                    		}
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'sencondCheckGroup1_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        name : 'secondCheckBox',
		                        id : 'firstSencondCheckBox_supperpoint',
		                        boxLabel: '00',
		                        inputValue: '0',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '01',
		                        inputValue: '1',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '02',
		                        inputValue: '2',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '03',
		                        inputValue: '3',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '04',
		                        inputValue: '4',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '05',
		                        inputValue: '5',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '06',
		                        inputValue: '6',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '07',
		                        inputValue: '7',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '08',
		                        inputValue: '8',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '09',
		                        inputValue: '9',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    }, {
		                        xtype: 'checkboxfield',
		                        boxLabel: '10',
		                        inputValue: '10',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '11',
		                        inputValue: '11',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '12',
		                        inputValue: '12',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '13',
		                        inputValue: '13',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '14',
		                        inputValue: '14',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            } ]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'sencondCheckGroup2_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '15',
		                        inputValue: '15',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '16',
		                        inputValue: '16',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '17',
		                        inputValue: '17',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '18',
		                        inputValue: '18',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '19',
		                        inputValue: '19',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '20',
		                        inputValue: '20',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '21',
		                        inputValue: '21',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '22',
		                        inputValue: '22',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '23',
		                        inputValue: '23',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '24',
		                        inputValue: '24',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '25',
		                        inputValue: '25',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '26',
		                        inputValue: '26',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '27',
		                        inputValue: '27',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '28',
		                        inputValue: '28',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '29',
		                        inputValue: '29',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'sencondCheckGroup3_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '30',
		                        inputValue: '30',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '31',
		                        inputValue: '31',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '32',
		                        inputValue: '32',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '33',
		                        inputValue: '33',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '34',
		                        inputValue: '34',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '35',
		                        inputValue: '35',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '36',
		                        inputValue: '36',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '37',
		                        inputValue: '37',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '38',
		                        inputValue: '38',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '39',
		                        inputValue: '39',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '40',
		                        inputValue: '40',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '41',
		                        inputValue: '41',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '42',
		                        inputValue: '42',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '43',
		                        inputValue: '43',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '44',
		                        inputValue: '44',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'sencondCheckGroup4_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '45',
		                        inputValue: '45',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '46',
		                        inputValue: '46',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '47',
		                        inputValue: '47',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '48',
		                        inputValue: '48',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '49',
		                        inputValue: '49',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '50',
		                        inputValue: '50',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '51',
		                        inputValue: '51',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '52',
		                        inputValue: '52',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '53',
		                        inputValue: '53',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '54',
		                        inputValue: '54',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '55',
		                        inputValue: '55',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '56',
		                        inputValue: '56',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '57',
		                        inputValue: '57',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '58',
		                        inputValue: '58',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '59',
		                        inputValue: '59',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('secondExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('secondExpText','second');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				}]
			});
			var minuteStepForm = Ext.create('Ext.form.Panel', {
				id : 'minuteStepForm',
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'minuteExpTextAppoint1_supperpoint',
						name : 'minuteExpRadio',
						checked : true,
						boxLabel : '分钟 允许的通配符[, - * /]', 
						inputValue : 1,
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		everyTime('minuteExpText');
		                    	}
		                    }
		                }
					} ,
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
//						padding : '0 0 0 30',
						items : [ {
							xtype : 'radiofield',
							id : 'minuteExpTextAppoint2_supperpoint',
							name : 'minuteExpRadio',
							boxLabel : '周期从',
							inputValue : 2,
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		cycle('minuteExpText','cycleminuteStart','cycleminuteEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleminuteStart',
							name : 'cycleminuteStart',
							minValue: 1,
							maxValue: 58,
							xtype : 'numberfield',
							allowBlank : false,
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('minuteExpTextAppoint2_supperpoint').checked;
			                		if(appointChecked){
			                    		cycle('minuteExpText','cycleminuteStart','cycleminuteEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleminuteEnd',
							name : 'cycleminuteEnd',
							minValue: 2,
							maxValue: 59,
							allowBlank : false,
							value: 2,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('minuteExpTextAppoint2_supperpoint').checked;
			                		if(appointChecked){
			                    		cycle('minuteExpText','cycleminuteStart','cycleminuteEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "分钟"
				        } ]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'minuteExpTextAppoint3_supperpoint',
//							margin : '0 0 0 30',
							name : 'minuteExpRadio',
							boxLabel : '从',
							inputValue : 3,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('minuteExpTextAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                    		startOn('minuteExpText','execminuteStart','execminuteEnd');
			                    	}
			                    }
			                }
						},{
							id : 'execminuteStart',
							name : 'execminuteStart',
							minValue: 0,
							maxValue: 59,
							xtype : 'numberfield',
							allowBlank : false,
							value: 0,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('minuteExpTextAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                    		startOn('minuteExpText','execminuteStart','execminuteEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "分钟开始，每"
				        },{
				        	id : 'execminuteEnd',
							name : 'execminuteEnd',
							minValue: 1,
							maxValue: 59,
							allowBlank : false,
							value: 1,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		startOn('minuteExpText','execminuteStart','execminuteEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "分钟执行一次"
				        }
					
					]
				}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
						xtype : 'radiofield',
						id : 'minuteExpTextAppoint4_supperpoint',
						name : 'minuteExpRadio',
						boxLabel : '指定',
						inputValue : 4,
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		Ext.getCmp("firstMinuteCheckBox_supperpoint").setValue(true);
		                    		var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                    		if(appointChecked){
		                    			createCheckBoxCron('minuteExpText','minute');
		                    		}
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'minuteCheckGroup1_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        name : 'minuteCheckBox',
		                        id : 'firstMinuteCheckBox_supperpoint',
		                        boxLabel: '00',
		                        inputValue: '0',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '01',
		                        inputValue: '1',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '02',
		                        inputValue: '2',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '03',
		                        inputValue: '3',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '04',
		                        inputValue: '4',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '05',
		                        inputValue: '5',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '06',
		                        inputValue: '6',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '07',
		                        inputValue: '7',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '08',
		                        inputValue: '8',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '09',
		                        inputValue: '9',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    }, {
		                        xtype: 'checkboxfield',
		                        boxLabel: '10',
		                        inputValue: '10',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '11',
		                        inputValue: '11',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '12',
		                        inputValue: '12',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '13',
		                        inputValue: '13',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '14',
		                        inputValue: '14',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            } ]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'minuteCheckGroup2_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '15',
		                        inputValue: '15',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '16',
		                        inputValue: '16',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '17',
		                        inputValue: '17',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '18',
		                        inputValue: '18',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '19',
		                        inputValue: '19',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '20',
		                        inputValue: '20',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '21',
		                        inputValue: '21',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '22',
		                        inputValue: '22',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '23',
		                        inputValue: '23',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '24',
		                        inputValue: '24',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '25',
		                        inputValue: '25',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '26',
		                        inputValue: '26',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '27',
		                        inputValue: '27',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '28',
		                        inputValue: '28',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '29',
		                        inputValue: '29',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'minuteCheckGroup3_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '30',
		                        inputValue: '30',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '31',
		                        inputValue: '31',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '32',
		                        inputValue: '32',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '33',
		                        inputValue: '33',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '34',
		                        inputValue: '34',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '35',
		                        inputValue: '35',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '36',
		                        inputValue: '36',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '37',
		                        inputValue: '37',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '38',
		                        inputValue: '38',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '39',
		                        inputValue: '39',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '40',
		                        inputValue: '40',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '41',
		                        inputValue: '41',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '42',
		                        inputValue: '42',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '43',
		                        inputValue: '43',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '44',
		                        inputValue: '44',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'minuteCheckGroup4_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '45',
		                        inputValue: '45',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '46',
		                        inputValue: '46',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '47',
		                        inputValue: '47',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '48',
		                        inputValue: '48',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '49',
		                        inputValue: '49',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '50',
		                        inputValue: '50',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '51',
		                        inputValue: '51',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '52',
		                        inputValue: '52',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '53',
		                        inputValue: '53',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '54',
		                        inputValue: '54',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '55',
		                        inputValue: '55',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '56',
		                        inputValue: '56',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '57',
		                        inputValue: '57',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '58',
		                        inputValue: '58',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '59',
		                        inputValue: '59',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('minuteExpTextAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('minuteExpText','minute');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				}]
			});
			var hourStepForm = Ext.create('Ext.form.Panel', {
				id : 'hourStepForm',
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'hourExpRadioAppoint1_supperpoint',
						name : 'hourExpRadio',
						checked : true,
						boxLabel : '小时 允许的通配符[, - * /]',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		everyTime('hourExpText');
		                    	}
		                    }
		                }
					},
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'hourExpRadioAppoint2_supperpoint',
							name : 'hourExpRadio',
							boxLabel : '周期从',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		cycle('hourExpText','cycleHourStart','cycleHourEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleHourStart',
							name : 'cycleHourStart',
							minValue: 0,
							maxValue: 23,
							allowBlank : false,
							xtype : 'numberfield',
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('hourExpRadioAppoint2_supperpoint').checked;
			                		if(appointChecked){
			                    		cycle('hourExpText','cycleHourStart','cycleHourEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleHourEnd',
							name : 'cycleHourEnd',
							minValue: 2,
							maxValue: 23,
							allowBlank : false,
							value: 2,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('hourExpRadioAppoint2_supperpoint').checked;
			                		if(appointChecked){
			                    		cycle('hourExpText','cycleHourStart','cycleHourEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "小时"
				        } ]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'hourExpRadioAppoint3_supperpoint',
							name : 'hourExpRadio',
							boxLabel : '从',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		startOn('hourExpText','execHourStart','execHourEnd');
			                    	}
			                    }
			                }
						},{
							id : 'execHourStart',
							name : 'execHourStart',
							minValue: 0,
							maxValue: 23,
							allowBlank : false,
							xtype : 'numberfield',
							value: 0,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('hourExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                    		startOn('hourExpText','execHourStart','execHourEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "小时开始，每"
				        },{
				        	id : 'execHourEnd',
							name : 'execHourEnd',
							minValue: 1,
							maxValue: 23,
							allowBlank : false,
							value: 1,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('hourExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                    		startOn('hourExpText','execHourStart','execHourEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "小时执行一次"
				        }
					
					]
				}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
						xtype : 'radiofield',
						id : 'hourExpRadioAppoint4_supperpoint',
						name : 'hourExpRadio',
						boxLabel : '指定',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		Ext.getCmp("firstHourCheckBox_supperpoint").setValue(true);
		                    		var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                    		if(appointChecked){
		                    			createCheckBoxCron('hourExpText','minute');
		                    		}
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'hourCheckGroup1_supperpoint',
		                width: '70%',
		                labelWidth : 30,
		                padding : '-5 0 -5 20',
		                fieldLabel: 'AM',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        id : 'firstHourCheckBox_supperpoint',
		                        boxLabel: '00',
		                        inputValue: '0',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '01',
		                        inputValue: '1',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '02',
		                        inputValue: '2',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '03',
		                        inputValue: '3',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '04',
		                        inputValue: '4',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '05',
		                        inputValue: '5',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '06',
		                        inputValue: '6',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '07',
		                        inputValue: '7',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '08',
		                        inputValue: '8',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '09',
		                        inputValue: '9',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    }, {
		                        xtype: 'checkboxfield',
		                        boxLabel: '10',
		                        inputValue: '10',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '11',
		                        inputValue: '11',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            } ]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'hourCheckGroup2_supperpoint',
		                width: '70%',
		                labelWidth : 30,
		                padding : '-5 0 -5 20',
		                fieldLabel: 'PM',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '12',
		                        inputValue: '12',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '13',
		                        inputValue: '13',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '14',
		                        inputValue: '14',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '15',
		                        inputValue: '15',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '16',
		                        inputValue: '16',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '17',
		                        inputValue: '17',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '18',
		                        inputValue: '18',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '19',
		                        inputValue: '19',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '20',
		                        inputValue: '20',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '21',
		                        inputValue: '21',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '22',
		                        inputValue: '22',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '23',
		                        inputValue: '23',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('hourExpRadioAppoint4_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('hourExpText','hour');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				}]
			});
			var dateStepForm = Ext.create('Ext.form.Panel', {
				id : 'dateStepForm',
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'dateExpRadioAppoint1_supperpoint',
						name : 'dateExpRadio',
						checked : true,
						boxLabel : '日 允许的通配符[, - * / L W]',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		everyTime('dateExpText');
		                    	}
		                    }
		                }
					},
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'dateExpRadioAppoint2_supperpoint',
							name : 'dateExpRadio',
							boxLabel : '不指定',
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		unAppoint('dateExpText');
			                    	}
			                    }
			                }
						} ]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'dateExpRadioAppoint3_supperpoint',
							name : 'dateExpRadio',
							boxLabel : '周期从',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		cycle('dateExpText','cycleDateStart','cycleDateEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleDateStart',
							name : 'cycleDateStart',
							minValue: 1,
							maxValue: 31,
							xtype : 'numberfield',
							value: 1,
							width : 80,
							allowBlank : false,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('dateExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('dateExpText','cycleDateStart','cycleDateEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleDateEnd',
							name : 'cycleDateEnd',
							minValue: 2,
							maxValue: 31,
							value: 2,
							xtype : 'numberfield',
							width : 80,
							allowBlank : false,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('dateExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('dateExpText','cycleDateStart','cycleDateEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "日"
				        } ]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'dateExpRadioAppoint4_supperpoint',
							name : 'dateExpRadio',
							boxLabel : '从',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		startOn('dateExpText','execDateStart','execDateEnd');
			                    	}
			                    }
			                }
						},{
							id : 'execDateStart',
							name : 'execDateStart',
							minValue: 1,
							maxValue: 31,
							xtype : 'numberfield',
							value: 1,
							width : 80,
							allowBlank : false,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('dateExpRadioAppoint4_supperpoint').checked;
			                		if(appointChecked){
			                			startOn('dateExpText','execDateStart','execDateEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "日开始，每"
				        },{
				        	id : 'execDateEnd',
							name : 'execDateEnd',
							minValue: 1,
							maxValue: 31,
							value: 1,
							allowBlank : false,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('dateExpRadioAppoint4_supperpoint').checked;
			                		if(appointChecked){
			                			startOn('dateExpText','execDateStart','execDateEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "天执行一次"
				        }
					
					]
				}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'dateExpRadioAppoint5_supperpoint',
						name : 'dateExpRadio',
						boxLabel : '每月',
						listeners: {
		                    change: function (field, checked) {
		                		if(checked){
		                			workDay('dateExpText','someDayStart');
		                    	}
		                    }
		                }
					},{
						id : 'someDayStart',
						name : 'someDayStart',
						minValue: 1,
						maxValue: 31,
						xtype : 'numberfield',
						value: 1,
						width : 80,
						listeners: {
		                    change: function (field, checked) {
		                    	var appointChecked = Ext.getCmp('dateExpRadioAppoint5_supperpoint').checked;
		                		if(appointChecked){
		                			workDay('dateExpText','someDayStart');
		                    	}
		                    }
		                }
					},{
			            xtype: "label",
			            text: "号开始最近的那个工作日"
			        },
			        
			        {
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [{
							xtype : 'radiofield',
							id : 'dateExpRadioAppoint6_supperpoint',
							name : 'dateExpRadio',
							boxLabel : '本月最后一天',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                			lastDay('dateExpText');
			                    	}
			                    }
			                }
						}]
					}
			        
			        ]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
						xtype : 'radiofield',
						id : 'dateExpRadioAppoint7_supperpoint',
						name : 'dateExpRadio',
						boxLabel : '指定',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		Ext.getCmp("firstDayCheckBox_supperpoint").setValue(true);
		                    		var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                    		if(appointChecked){
		                    			createCheckBoxCron('dateExpText','date');
		                    		}
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                width: '80%',
		                id : 'dateCheckGroup1_supperpoint',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        id : 'firstDayCheckBox_supperpoint',
		                        boxLabel: '01',
		                        inputValue: '1',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '02',
		                        inputValue: '2',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '03',
		                        inputValue: '3',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '04',
		                        inputValue: '4',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '05',
		                        inputValue: '5',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '06',
		                        inputValue: '6',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '07',
		                        inputValue: '7',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '08',
		                        inputValue: '8',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '09',
		                        inputValue: '9',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    }, {
		                        xtype: 'checkboxfield',
		                        boxLabel: '10',
		                        inputValue: '10',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '11',
		                        inputValue: '11',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '12',
		                        inputValue: '12',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '13',
		                        inputValue: '13',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '14',
		                        inputValue: '14',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '15',
		                        inputValue: '15',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '16',
		                        inputValue: '16',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            } ]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                width: '80%',
		                id : 'dateCheckGroup2_supperpoint',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '17',
		                        inputValue: '17',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '18',
		                        inputValue: '18',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '19',
		                        inputValue: '19',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '20',
		                        inputValue: '20',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '21',
		                        inputValue: '21',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '22',
		                        inputValue: '22',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '23',
		                        inputValue: '23',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '24',
		                        inputValue: '24',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '25',
		                        inputValue: '25',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '26',
		                        inputValue: '26',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '27',
		                        inputValue: '27',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '28',
		                        inputValue: '28',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '29',
		                        inputValue: '29',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },{
		                        xtype: 'checkboxfield',
		                        boxLabel: '30',
		                        inputValue: '30',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '31',
		                        inputValue: '31',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('dateExpRadioAppoint7_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('dateExpText','date');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            }]
				}]
			});
			var monthStepForm = Ext.create('Ext.form.Panel', {
				id : 'monthStepForm',
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'monthExpRadioAppoint1_supperpoint',
						name : 'monthExpRadio',
						checked : true,
						boxLabel : '月 允许的通配符[, - * /]',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		everyTime('monthExpText');
		                    	}
		                    }
		                }
					},
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'monthExpRadioAppoint2_supperpoint',
							name : 'monthExpRadio',
							boxLabel : '不指定',
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		unAppoint('monthExpText');
			                    	}
			                    }
			                }
						} ]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'monthExpRadioAppoint3_supperpoint',
							name : 'monthExpRadio',
							boxLabel : '周期从',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		cycle('monthExpText','cycleMonthStart','cycleMonthEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleMonthStart',
							name : 'cycleMonthStart',
							minValue: 1,
							maxValue: 12,
							xtype : 'numberfield',
							allowBlank : false,
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('monthExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('monthExpText','cycleMonthStart','cycleMonthEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleMonthEnd',
							name : 'cycleMonthEnd',
							minValue: 2,
							maxValue: 12,
							allowBlank : false,
							value: 2,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('monthExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('monthExpText','cycleMonthStart','cycleMonthEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "月"
				        } ]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'monthExpRadioAppoint4_supperpoint',
							name : 'monthExpRadio',
							boxLabel : '从',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		startOn('monthExpText','execMonthStart','execMonthEnd');
			                    	}
			                    }
			                }
						},{
							id : 'execMonthStart',
							name : 'execMonthStart',
							minValue: 1,
							maxValue: 12,
							allowBlank : false,
							xtype : 'numberfield',
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('monthExpRadioAppoint4_supperpoint').checked;
			                		if(appointChecked){
			                			startOn('monthExpText','execMonthStart','execMonthEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "月开始，每"
				        },{
				        	id : 'execMonthEnd',
							name : 'execMonthEnd',
							minValue: 1,
							maxValue: 12,
							value: 1,
							allowBlank : false,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('monthExpRadioAppoint4_supperpoint').checked;
			                		if(appointChecked){
			                			startOn('monthExpText','execMonthStart','execMonthEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "月执行一次"
				        }
					
					]
				}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
						xtype : 'radiofield',
						id : 'monthExpRadioAppoint5_supperpoint',
						name : 'monthExpRadio',
						boxLabel : '指定',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		Ext.getCmp("firstMonthCheckBox_supperpoint").setValue(true);
		                    		var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                    		if(appointChecked){
		                    			createCheckBoxCron('monthExpText','month');
		                    		}
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'monthCheckGroup1_supperpoint',
		                width: '80%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        id : 'firstMonthCheckBox_supperpoint',
		                        boxLabel: '01',
		                        inputValue: '1',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '02',
		                        inputValue: '2',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '03',
		                        inputValue: '3',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '04',
		                        inputValue: '4',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '05',
		                        inputValue: '5',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '06',
		                        inputValue: '6',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '07',
		                        inputValue: '7',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '08',
		                        inputValue: '8',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '09',
		                        inputValue: '9',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    }, {
		                        xtype: 'checkboxfield',
		                        boxLabel: '10',
		                        inputValue: '10',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '11',
		                        inputValue: '11',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '12',
		                        inputValue: '12',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('monthExpRadioAppoint5_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('monthExpText','month');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            } ]
				}]
			});
			var weekStepForm = Ext.create('Ext.form.Panel', {
				id : 'weekStepForm',
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'weekExpRadioAppoint1_supperpoint',
						name : 'weekExpRadio',
						checked : true,
						boxLabel : '周 允许的通配符[, - * / L #]',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		unAppoint('weekExpText');
		                    	}
		                    }
		                }
					},
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'weekExpRadioAppoint2_supperpoint',
							name : 'weekExpRadio',
							boxLabel : '不指定',
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		unAppoint('weekExpText');
			                    	}
			                    }
			                }
						}]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'weekExpRadioAppoint3_supperpoint',
							name : 'weekExpRadio',
							boxLabel : '周期从星期',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		cycle('weekExpText','cycleWeekStart','cycleWeekEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleWeekStart',
							name : 'cycleWeekStart',
							minValue: 1,
							maxValue: 7,
							xtype : 'numberfield',
							allowBlank : false,
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('weekExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('weekExpText','cycleWeekStart','cycleWeekEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleWeekEnd',
							name : 'cycleWeekEnd',
							minValue: 2,
							maxValue: 7,
							value: 2,
							allowBlank : false,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('weekExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('weekExpText','cycleWeekStart','cycleWeekEnd');
			                    	}
			                    }
			                }
						}]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'weekExpRadioAppoint4_supperpoint',
							name : 'weekExpRadio',
							boxLabel : '第',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                			weekOfDay('weekExpText','execWeek','execWeekDate');
			                    	}
			                    }
			                }
						},{
							id : 'execWeek',
							name : 'execWeek',
							minValue: 1,
							maxValue: 4,
							allowBlank : false,
							xtype : 'numberfield',
							value: 1,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('weekExpRadioAppoint4_supperpoint').checked;
			                		if(appointChecked){
			                			weekOfDay('weekExpText','execWeek','execWeekDate');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "周的星期"
				        },{
				        	id : 'execWeekDate',
							name : 'execWeekDate',
							minValue: 1,
							maxValue: 7,
							value: 1,
							allowBlank : false,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('weekExpRadioAppoint4_supperpoint').checked;
			                		if(appointChecked){
			                			weekOfDay('weekExpText','execWeek','execWeekDate');
			                    	}
			                    }
			                }
						}
					
					]
				}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'weekExpRadioAppoint5_supperpoint',
						name : 'weekExpRadio',
						boxLabel : '本月最后一个星期',
						listeners: {
		                    change: function (field, checked) {
		                		if(checked){
		                			lastWeek('weekExpText','lastWeekDate');
		                    	}
		                    }
		                }
					},{
						id : 'lastWeekDate',
						name : 'lastWeekDate',
						minValue: 1,
						maxValue: 7,
						allowBlank : false,
						xtype : 'numberfield',
						value: 1,
						width : 80,
						listeners: {
		                    change: function (field, checked) {
		                    	var appointChecked = Ext.getCmp('weekExpRadioAppoint5_supperpoint').checked;
		                		if(appointChecked){
		                			lastWeek('weekExpText','lastWeekDate');
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
						xtype : 'radiofield',
						id : 'weekExpRadioAppoint6_supperpoint',
						name : 'weekExpRadio',
						boxLabel : '指定',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		Ext.getCmp("firstWeekCheckBox_supperpoint").setValue(true);
		                    		var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                    		if(appointChecked){
		                    			createCheckBoxCron('weekExpText','week');
		                    		}
		                    	}
		                    }
		                }
					}]
				},{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [{
		                xtype: 'checkboxgroup',
		                id : 'weekCheckGroup_supperpoint',
		                width: '50%',
		                padding : '-5 0 -5 15',
		                items: [
		                    {
		                        xtype: 'checkboxfield',
		                        id : 'firstWeekCheckBox_supperpoint',
		                        boxLabel: '1',
		                        inputValue: 'MON',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '2',
		                        inputValue: 'TUE',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '3',
		                        inputValue: 'WED',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '4',
		                        inputValue: 'THU',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '5',
		                        inputValue: 'FRI',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        }
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '6',
		                        inputValue: 'SAT',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        } 
		                    },
		                    {
		                        xtype: 'checkboxfield',
		                        boxLabel: '7',
		                        inputValue: 'SUN',
		        				listeners: {
		                            change: function (field, checked) {
		                            	var appointChecked = Ext.getCmp('weekExpRadioAppoint6_supperpoint').checked;
		                        		if(appointChecked){
		                        			createCheckBoxCron('weekExpText','week');
		                        		}
		                            }
		                        }
		                    }
		                ]
		            } ]
				}]
			});
			var yearStepFormDate = new Date;
			var minYear = yearStepFormDate.getFullYear(); 
			var maxYear = 3000; 
			var yearStepForm = Ext.create('Ext.form.Panel', {
				id : 'yearStepForm',
				region : 'north',
				layout : 'anchor',
				buttonAlign : 'center',
				collapsible : false,//可收缩 
				collapsed : false,//默认收缩
				border : false,
				bodyBorder : false,
				dockedItems : [{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
					items : [ {
						xtype : 'radiofield',
						id : 'yearExpRadioAppoint1_supperpoint',
						name : 'yearExpRadio',
						checked : true,
						boxLabel : '不指定 允许的通配符[, - * /] 非必填',
						listeners: {
		                    change: function (field, checked) {
		                    	if(checked){
		                    		unAppoint('yearExpText');
		                    	}
		                    }
		                }
					},
					
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'yearExpRadioAppoint2_supperpoint',
							name : 'yearExpRadio',
							boxLabel : '每年',
							listeners: {
			                    change: function (field, checked) {
			                    	if(checked){
			                    		everyTime('yearExpText');
			                    	}
			                    }
			                }
						}]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [ {
							xtype : 'radiofield',
							id : 'yearExpRadioAppoint3_supperpoint',
							name : 'yearExpRadio',
							boxLabel : '周期从年',
							listeners: {
			                    change: function (field, checked) {
			                		if(checked){
			                    		cycle('yearExpText','cycleYearStart','cycleYearEnd');
			                    	}
			                    }
			                }
						},{
							id : 'cycleYearStart',
							name : 'cycleYearStart',
							minValue: minYear,
							maxValue: maxYear,
							xtype : 'numberfield',
							allowBlank : false,
							value: minYear,
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('yearExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('yearExpText','cycleYearStart','cycleYearEnd');
			                    	}
			                    }
			                }
						},{
				            xtype: "label",
				            text: "-"
				        },{
				        	id : 'cycleYearEnd',
							name : 'cycleYearEnd',
							minValue: minYear+1,
							maxValue: maxYear,
							allowBlank : false,
							value: minYear+1,
							xtype : 'numberfield',
							width : 80,
							listeners: {
			                    change: function (field, checked) {
			                    	var appointChecked = Ext.getCmp('yearExpRadioAppoint3_supperpoint').checked;
			                		if(appointChecked){
			                			cycle('yearExpText','cycleYearStart','cycleYearEnd');
			                    	}
			                    }
			                }
						}
					
					]
				}]
				}]
			});
			
			var tabPanelForCron = Ext.create('Ext.tab.Panel', {
				tabPosition : 'top',
				region : 'center',
				activeTab : 0,
				border : false,
				items : [ {
					title : '秒',
					border : false,
					layout : 'fit',
					items : secondStepForm
				}, {
					title : '分钟',
					border : false,
					layout : 'fit',
					items : minuteStepForm
				}, {
					title : '小时',
					border : false,
					layout : 'fit',
					items : hourStepForm
				}, {
					title : '日',
					border : false,
					layout : 'fit',
					items : dateStepForm
				}, {
					title : '月',
					border : false,
					layout : 'fit',
					items : monthStepForm
				}, {
					title : '周',
					border : false,
					layout : 'fit',
					items : weekStepForm
				}, {
					title : '年',
					border : false,
					layout : 'fit',
					items : yearStepForm
				}  ]
			});
			
			var createCronPagePanel = Ext.create('Ext.panel.Panel', {
				region : 'north',
				border : false,
				height : '47%',
				layout : 'fit',
				layout:'border',
				margin : '0 0 -20 0',
				border : false,
				bodyBorder : false,
				items:[tabPanelForCron]
			});
			
			var saveJsonCronButton = Ext.create ("Ext.Button",
					{
					    cls : 'Common_Btn',
					    text : '保存表达式',
					    handler : saveCron
					});
			var resetCronButton = Ext.create ("Ext.Button",
					{
				cls : 'Common_Btn',
				text : '清空',
				handler : resetCron
					});
			function 	resetCron(){
				setCronExpression();
			}	
			
			
			function saveCron(){
				var expression = Ext.getCmp('cronExpText').getValue();
				//var taskid = taskid;
				if(null == expression || "" == expression || expression.indexOf("错误") >= 0)
				{
					Ext.Msg.alert ('提示', "请输入正确的cron表达式！", function ()
				    {
					    if (Ext.isIE)
					    {
						    CollectGarbage ();
					    }
				    });
					return;
				}
				chkpoint_grid_panel_param.getStore().getAt(rownum).set("icron",expression)
				creatCronWin_supperpoint.close();
				Ext.destroy (creatCronWin_supperpoint);
			}
			
			Ext.create('Ext.form.field.TextArea', {
			    labelAlign:'right',
			    width: "100%",
			    id : 'jsonCronTextArea',
			    name: 'jsonCronTextArea',
			    hidden : true,
			});
			
			var cronExpressionPagePanel = Ext.create('Ext.panel.Panel', {
				region : 'center',
				border : false,
				height : '53%',
				layout : 'fit',
				items : [ {
					xtype : 'fieldset',
					title : '表达式',
					height : '100%',
					width : '100%',
					items : [ 
					{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [{
							xtype : 'label',
							text : '表达式字段:'
						},{
							xtype : 'textfield',
							id : 'secondExpText',
							name : 'secondExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 120',
							text : '秒'
						},
						
						{
							xtype : 'textfield',
							id : 'minuteExpText',
							name : 'minuteExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 80',
							text : '分钟'
						},
						{
							xtype : 'textfield',
							id : 'hourExpText',
							name : 'hourExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 80',
							text : '小时'
						},
						
						{
							xtype : 'textfield',
							id : 'dateExpText',
							name : 'dateExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 80',
							text : '日'
						},
						
						{
							xtype : 'textfield',
							id : 'monthExpText',
							name : 'monthExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 80',
							text : '月'
						},
						
						{
							xtype : 'textfield',
							id : 'weekExpText',
							name : 'weekExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 90',
							text : '星期'
						},
						
						{
							xtype : 'textfield',
							id : 'yearExpText',
							name : 'yearExpText',
							readOnly : true,
							flex : 1
						},
						
						{
							xtype : 'label',
//							padding : '0 0 0 80',
							text : '年'
						} 
						
						]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [{
							xtype : 'label',
							text : '表达式:'
						},{
							xtype : 'textfield',
							id : 'cronExpText', 
							name : 'cronExpText',
							//value : hcRuntime,
							readOnly : true,
							width : '68.1%'
						},'->',resetCronButton,saveJsonCronButton]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [{
							xtype : 'label',
							text : '最近5次运行时间'
						}]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
						items : [{
							id : 'expressionHtmlEditor_supperpoint',
							labelAlign : 'right',
							enableFont : false,  //隐藏或显示字体选项
							enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
							enableFormat : false,   //允许字体加粗
							enableLinks : false,  //启用链接创建按钮。
							enableSourceEdit : false,  //允许切换到源码编辑按钮。
							enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
							enableColors : false,  //启用回退/高亮颜色按钮
							enableLists : false, //启用bullet和有限数量的按钮列表。
							border : false,
							readOnly : true,
							width : '100%',
							height : 150,
//							margin : '-10 0 0 0',
							xtype : 'htmleditor',
							listeners:{
								afterrender:function(editor){
								    setTimeout(function(){
						                var body = editor.getEditorBody();
						                body.style.background='black';
						                body.style.color="white";
						            },400);
							    }
							}
						}]
					}]
				}]
				
			});
			
			var hcTimeSetCronPanel = Ext.create('Ext.panel.Panel', {
				
				//id : 'hcTimeSetCronPanel',
				height : '100%',
				width : '100%',
				layout : 'border',
				border : false,
				items : [ createCronPagePanel,cronExpressionPagePanel ]
			});
			
			var creatCronWin_supperpoint = new Ext.window.Window({
	               title : '检查点时间表达式配置',
	               width :  contentPanel.getWidth() - 350,
	               height : contentPanel.getHeight() - 30,
	               id : 'creatCronWin_supperpoint',
	               closeAction : 'destroy',
	               autoScroll : true,
	               modal : true,
	               resizable : false,
	               layout : 'fit',
	               items:  [hcTimeSetCronPanel]
	               /*listeners : {
						"beforedestroy" : function(obj) {
							// globalProjectInstanceId=0;
							Ext.destroy (Ext.getCmp("creatCronWin_supperpoint"));
							Ext.destroy (Ext.getCmp("hcTimeSetCronPanel"));
						}
					}*/
	           }).show();
			
				/*creatCronWin_supperpoint = Ext.create('Ext.window.Window', {
					title : '定时任务参数设置',
					modal : true,
					id : 'creatCronWin_supperpoint',
					closeAction : 'destroy',
					//autoDestroy : true,
					constrain : true,
					autoScroll : true,
					//upperWin : creatCronWin_supperpoint,
					width : contentPanel.getWidth() - 350,
					height : contentPanel.getHeight() - 30,
					draggable : false,// 禁止拖动
					resizable : false,// 禁止缩放
					layout : 'fit',
					items:  [hcTimeSetCronPanel],
					listeners : {
						"beforedestroy" : function(obj) {
							// globalProjectInstanceId=0;
							Ext.destroy (Ext.getCmp("creatCronWin_supperpoint"));
							Ext.destroy (Ext.getCmp("hcTimeSetCronPanel"));
						}
					}
				}).show();*/

			if(hcRuntime != "" && hcRuntime != null){
				var words = hcRuntime.split(' ')
				var list = new Array();
				for ( var i = 0; i <words.length; i++){
				    if(words[i] != ''){
				    	list.push(words[i])
				    }
				}
				$('input[name="secondExpText"]').val(list[0]);
				$('input[name="minuteExpText"]').val(list[1]);
				$('input[name="hourExpText"]').val(list[2]);
				$('input[name="dateExpText"]').val(list[3]);
				$('input[name="monthExpText"]').val(list[4]);
				$('input[name="weekExpText"]').val(list[5]);
				$('input[name="yearExpText"]').val('');
				$('input[name="cronExpText"]').val(hcRuntime);
				generatingExpression(hcRuntime);
				rownum = index;
			}else{
				setCronExpression();
				rownum = index; 
			}
			//Ext.destroy (Ext.getCmp("creatCronWin_supperpoint"));
		} 




function setCronExpression(){
	$('input[name="secondExpText"]').val('*');
	$('input[name="minuteExpText"]').val('*');
	$('input[name="hourExpText"]').val('*');
	$('input[name="dateExpText"]').val('*');
	$('input[name="monthExpText"]').val('*');
	$('input[name="weekExpText"]').val('?');
	$('input[name="yearExpText"]').val('');
	var cronExpTextValue = '* * * * * ?';
	$('input[name="cronExpText"]').val(cronExpTextValue);
}
