package com.ideal.ieai.core.io.project;

import org.jdom2.Element;

import com.ideal.ieai.core.data.ArrayObject;
import com.ideal.ieai.core.data.ArrayType;
import com.ideal.ieai.core.data.DataTypeNames;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.core.io.ProjectConstants;
import com.ideal.ieai.core.io.XMLMarshaller;

/**
 * <p>
 * Title:
 * </p>
 * <p>
 * Description:It converts a array to an element
 * </p>
 * <p>
 * Copyright: Copyright (c) 2003
 * </p>
 * <p>
 * Company:
 * </p>
 * 
 * <AUTHOR>
 * @version 3.0
 */

public class ArrayValueM implements XMLMarshaller
{
    private static ArrayValueM _instance;

    /**
     * 
     */
    private ArrayValueM()
    {
    }

    public static ArrayValueM getInstance ()
    {
        if (null == _instance)
        {
            _instance = new ArrayValueM();
        }
        return _instance;
    }

    /**
     * 
     * @param TheObject ArrayObject instance.
     * @return
     * @throws MarshallingException
     */
    public Element translate ( Object TheObject ) throws MarshallingException
    {
        if (!(TheObject instanceof ArrayObject))
        {
            throw new MarshallingException(MarshallingException.INVALID_OBJECT_TYPE);
        }
        ArrayObject arrayObject = (ArrayObject) TheObject;
        ArrayType arrayType = arrayObject.getType();
        Element retE = new Element(DataTypeNames.TYPE_NAME_ARRAY);
        // type name element.
        Element typeNameE = new Element(ProjectConstants.TYPE_NAME);
        typeNameE.setText(arrayType.getTypeName());
        retE.addContent(typeNameE);
        // values element.
        Element valuesE = new Element(ProjectConstants.VALUES);
        retE.addContent(valuesE);
        int dim[] = arrayObject.getNumberOfDimensions();
        if (dim[0] == 0)
        { // if dim[0]==0, the dim will like this: dim[]={0,0,0,...}.
            // It represents the arrayObject is empty.
            return retE;
        }
        // value element.
        int workDim[] = new int[dim.length]; // workDim={0,0,0,...}
        Element E = null;
        // The element of the iEAI Array has eight kinds.
        // 1.IntegerType 2.FloatType 3.BooleanType 4.StringType
        // 5.BinaryType 6.DatetimeType 7.MapType 8.Schema
        do
        {
            Object obj = arrayObject.getElement(workDim);
            // "4.5.6.7.8. "null is default value, needs not to store.
            if (null == obj)
            {
                continue;
            }
            // For primitive type "1.2.3. 0 and 0.0 and false is default value,
            // They need not to store.
            if (obj instanceof Integer) // 1 IntegerType
            {
                Integer i = (Integer) obj;
                if (i.intValue() == 0)
                {
                    continue;
                }
            } else if (obj instanceof Float) // 2 FloatType
            {
                Float f = (Float) obj;
                if (f.floatValue() == 0)
                {
                    continue;
                }
            } else if (obj instanceof Boolean) // 3 BooleanType
            {
                Boolean b = (Boolean) obj;
                if (b.booleanValue() == false)
                {
                    continue;
                }
            }

            E = IDataValueM.getInstance().translate(obj);
            // Adds attribute
            String path = this.parsePath(workDim);
            if (null != path)
            {
                E.setAttribute(ProjectConstants.PATH, path);
            }
            // Adds E to valuesE.
            valuesE.addContent(E);
        } while (ArrayObject.addAndAdjustSuffix(dim, workDim));
        return retE;
    }

    /**
     * Here, use the policy of schema path.<br>
     * 
     * <pre>
     *   For example:
     *   suffix={3,3,3};
     *   path=[3].[3].[3],
     *   path will be stored to XML.
     *   and we can use NodePathParser to revert it.
     * </pre>
     * 
     * @param suffix
     * @return
     */
    public String parsePath ( int[] suffix )
    {
        String path = "[" + Integer.toString(suffix[0]) + "]";
        for (int i = 1; i < suffix.length; i++)
        {
            path += "." + "[" + Integer.toString(suffix[i]) + "]";
        }
        return path;
    }

    // public static void main(String argvs[])
    // {
    // int a[] = new int[]{1,2,3,4,5};
    // System.out.println(parsePath(a));
    // List l=null;
    // try
    // {
    // l=NodePathParser.parseNodePath(parsePath(a));
    // }
    // catch (Exception ex)
    // {
    // ex.printStackTrace();
    // }
    // for(int i=0,size=l.size();i<size;i++)
    // {
    // System.out.println(((Integer)l.get(i)).intValue());
    // }
    // }
}
