package com.ideal.ieai.server.platform.warnmanage;

public class IeaiWarnModuleModel{
    
    private Long imoduleid;
    private String imodulecode;
    private String imodulename;
    private String iremark;
    private Long ideleteflag;
    private int ieventorder;
    private String ititle;
    public Long getImoduleid ()
    {
        return imoduleid;
    }
    public void setImoduleid ( Long imoduleid )
    {
        this.imoduleid = imoduleid;
    }
    public String getImodulecode ()
    {
        return imodulecode;
    }
    public void setImodulecode ( String imodulecode )
    {
        this.imodulecode = imodulecode;
    }

    public String getImodulename ()
    {
        return imodulename;
    }
    public void setImodulename ( String imodulename )
    {
        this.imodulename = imodulename;
    }
    public String getIremark ()
    {
        return iremark;
    }
    public void setIremark ( String iremark )
    {
        this.iremark = iremark;
    }
    public Long getIdeleteflag ()
    {
        return ideleteflag;
    }
    public void setIdeleteflag ( Long ideleteflag )
    {
        this.ideleteflag = ideleteflag;
    }

    public int getIeventorder() {
        return ieventorder;
    }

    public void setIeventorder(int ieventorder) {
        this.ieventorder = ieventorder;
    }

    public String getItitle() {
        return ititle;
    }

    public void setItitle(String ititle) {
        this.ititle = ititle;
    }
}