<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="java.util.*"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
    List listOper = (List) request.getAttribute("opers");
    List listShow = (List) request.getAttribute("shows");
    boolean isTabSwitch=Environment.getInstance().getMainPageIsTab();
%>
<html>
<head>
    <style>
        .btn-a{
            display:inline-block;
            width:80px;
            height:30px;
            line-height:30px;
            background:#009966;
            position:relative;
            color:#fff;
            text-align:center;
            text-decoration:none;}
        .btn-a:after{
            content: '';
            width: 0;
            height: 0;
            position: absolute;
            right: -30px;
            top: 0;
            border: 15px solid transparent;
            border-left-color: #009966;
        }
        /*.customize_toolbar .x-toolbar-text {*/
        /*    display: none !important;*/
        /*}*/
        .x-tip {
            /*word-wrap:break-word;*/
            white-space:normal;word-break:break-all;
        }
    </style>

<%--    <% if(isTabSwitch){ %>--%>
<%--    <style type="text/css">--%>
<%--        .customize_fywidth{--%>
<%--            width:419px !important;--%>
<%--        }--%>
<%--    </style>--%>
<%--    <%}else{ %>--%>
<%--    <style type="text/css">--%>
<%--        .customize_fywidth{--%>
<%--            width:430px !important;--%>
<%--        }--%>
<%--    </style>--%>
<%--    <%}%>--%>
    <script type="text/javascript">
        <%--var isTabSwitch=<%=isTabSwitch%>;--%>
        var toolsId = '<%=request.getAttribute("iid")%>';
        var itoolScriptId = '<%=request.getAttribute("itoolScriptId")%>';
        var systemName = '<%=request.getAttribute("systemName")%>';
        var systemId = '<%=request.getAttribute("systemId")%>';
        var toolCode = '<%=request.getAttribute("toolCode")%>';
        var toolName = decodeURIComponent('<%=request.getAttribute("toolName")%>');
        var nOstype = '<%=request.getAttribute("nOstype")%>';
        var userName = '<%=request.getAttribute("userName")%>';
        var onetypeName = '<%=request.getAttribute("onetypeName")%>';
        var twotypeName = '<%=request.getAttribute("twotypeName")%>';
        var threetypeName = '<%=request.getAttribute("threetypeName")%>';
        var toolstypeName = '<%=request.getAttribute("toolstypeName")%>';
        var toolScriptType = '<%=request.getAttribute("toolScriptType")%>';
        var toolScriptName = decodeURIComponent('<%=request.getAttribute("toolScriptName")%>');
        var toolDescribe = decodeURIComponent('<%=request.getAttribute("toolDescribe")%>');
        var statusName = '<%=request.getAttribute("statusName")%>';
        var onetypeId = '<%=request.getAttribute("ionetypeId")%>';
        var twotypeId = '<%=request.getAttribute("itwotypeId")%>';
        var threetypeId = '<%=request.getAttribute("ithreetypeId")%>';
        var iuserType = '<%=request.getAttribute("iuserType")%>';
        var toolKeyWord = decodeURIComponent('<%=request.getAttribute("toolKeyWord")%>');
        var toolMatchIp = '<%=request.getAttribute("toolMatchIp")%>';
        var itoolTypeId = '<%=request.getAttribute("itoolTypeId")%>';
        var itoolStatusId = '<%=request.getAttribute("itoolStatusId")%>';
        var detailIp = '<%=request.getAttribute("detailIp")%>';
        var isHighRiskReq = '<%=request.getAttribute("isHighRisk")%>';
        var shows =  [
            {fieldLabel:'工具编号',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'toolCode',xtype:"textfield",value:toolCode},
            {fieldLabel:'工具名称',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'toolName',xtype:"textfield",value:toolName},
            {fieldLabel:'应用系统',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'systemName',xtype:"textfield",value:systemName},
            {fieldLabel:'操作系统',padding: '5 5 5 5',labelAlign:'right',labelWidth:90,readOnly:true,name:'nOstype',xtype:"textfield",value:nOstype},
            {fieldLabel:'一级分类',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'onetypeName',xtype:"textfield",value:onetypeName},
            {fieldLabel:'二级分类',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'twotypeName',xtype:"textfield",value:twotypeName},
            {fieldLabel:'三级分类',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'threetypeName',xtype:"textfield",value:threetypeName},
            {fieldLabel:'是否高危',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'isHighRisk',xtype:"textfield",value:isHighRiskReq},
            {fieldLabel:'操作用户',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'userName',xtype : "textfield",value :userName},
            {fieldLabel:'工具类型',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'toolstypeName',xtype:"textfield",value:toolstypeName},
            {fieldLabel:'脚本来源',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'toolScriptType',xtype:"textfield",value:toolScriptType},
            {fieldLabel:'脚本名称',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'toolScriptName',xtype:"textfield",value:toolScriptName},
            {fieldLabel:'工具状态',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'statusName',xtype:"textfield",value:statusName},
            {fieldLabel:'工具描述',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'toolDescribe',xtype:"textfield",value:toolDescribe}
        ];
        <%if (null != listShow && listShow.size() > 0) {
         for (int i = 0; i < listShow.size(); i++) {
             Map map = (Map) listShow.get(i);%>
        var showInfo = {};
        showInfo.fieldLabel = '描述信息';
        showInfo.value = '<%=(String) map.get("value")%>';
        showInfo.labelWidth = 90;
        showInfo.width = 544;
        showInfo.name='<%=(String) map.get("name")%>';
        showInfo.xtype="textarea";
        //showInfo.disabled=true;
        showInfo.height=100;
        shows.push(showInfo);
        <%}
     }%>

        var toolsExecWindowItoolDescribe = decodeURIComponent('<%=request.getAttribute("itoolDescribe")%>');
        var toolsExecWindowItoolFirstDescribe = decodeURIComponent('<%=request.getAttribute("itoolFirstDescribe")%>');
        var shows2 =  [
            {fieldLabel:'工具描述',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolDescribe',xtype:"textarea",width:1080,height:70,value:toolsExecWindowItoolDescribe},
            {fieldLabel:'一线工具描述',padding: '5 5 5 5',labelWidth:90,labelAlign:'right',readOnly:true,name:'itoolFirstDescribe',xtype:"textarea",width:1080,height:70,value:toolsExecWindowItoolFirstDescribe}
        ];
        var alarmInfoDiagnosisNew=Date.now();
        $(document).ready(function(){
            $("#alarmInfoDiagnosisDiv").attr('id',$("#alarmInfoDiagnosisDiv").attr('id')+alarmInfoDiagnosisNew)
        });
    </script>
    <script type="text/javascript"
            src="<%=request.getContextPath()%>/page/operationToolbox/alarmInfo/alarmInfoDiagnosis.js">
    </script>
   <style type="text/css">
       /*提示弹窗关闭问题*/
		div[class^=x-window-body] .x-box-inner {
			overflow-y:auto !important;
		}
		.x-form-display-field {
			height: auto !important;
		}
    </style>
</head>
<body>
<div id="sss"></div>
<div id="alarmInfoDiagnosisDiv"
     style="width: 100%; height: 100%"></div>
</body>
</html>