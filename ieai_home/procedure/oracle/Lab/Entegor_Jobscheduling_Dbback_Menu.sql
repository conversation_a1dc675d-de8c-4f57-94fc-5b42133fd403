DECLARE
    LS_SQL    VARCHAR2(4000);
    LI_EXISTS SMALLINT;
BEGIN
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE IORDER = 16;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_GROUPMESSAGE (IORDER, GROUPID, GROUPNAME, GROUPDESCRIPTION, IIMG) VALUES (16, 16, ''数据备份恢复'', ''数据备份恢复模块组'', ''images/info84.png'')';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101011;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101011, ''备份恢复任务监控'', 16, ''taskMonitoringQuery.do'', 1, null, ''images/info24.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101022;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101022, ''业务系统维护'', 16, ''businessSystemMaintenance.do'', 1, null, ''images/info24.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101023;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101023, ''数据库对象信息维护'', 16, ''DBOjectMaintenance.do'', 3, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101024;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101024, ''数据库信息维护'', 16, ''initDbInfoMaintenance.do'', 2, null, ''images/info81.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101026;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101026, ''备份恢复规则'', 16, ''DBBackRecoverRule.do'', 4, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101027;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101027, ''上带库操作'', 16, ''DBSdkOpration.do'', 11, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101028;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101028, ''备份策略维护'', 16, ''dbStrategy.do'', 5, null, ''images/info24.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101029;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101029, ''备份恢复信息详情展示'', 16, ''copyRecordInfoShow.do'', 12, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101030;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101030, ''备份任务手工调度'', 16, ''DBTaskBackHandle.do'', 9, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101032;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101032, ''恢复任务手工调度'', 16, ''DBTaskRecoverHandle.do'', 10, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101033;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101033, ''备份任务启动'', 16, ''dbbackStart.do'', 7, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101034;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101034, ''恢复任务启动'', 16, ''dbrecoverStart.do'', 8, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101035;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101035, ''恢复策略维护'', 16, ''dbStrategyRecover.do'', 6, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;

    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101036;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101036, ''备份恢复信息记录'', 16, ''DBBrtRecordRunRecord.do'', 16, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF; 
    
   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101037;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101037, ''密码加密'', 16, ''encryptionpage.do'', 21, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF; 
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101038;
    IF LI_EXISTS = 0 THEN
				LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101038, ''备份变化趋势报表'', 16, ''backupTrendReportjsp.do'', 22, null, ''images/info25.png'', 0)';  
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101043;
    IF LI_EXISTS = 0 THEN
				LS_SQL := 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG,AUDITENBLE) VALUES(101043, ''策略运行记录查询'', 16, ''dbStrategyRunRecord.do'', 23, null,''images/info25.png'',0)';  
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101040;
    IF LI_EXISTS = 0 THEN
				LS_SQL := 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG,AUDITENBLE) VALUES(101040, ''任务运行时间分布报表'', 16, ''dbTaskRunTimeReport.do'', 24, null,''images/info25.png'',0)';  
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 1042;
    IF LI_EXISTS = 0 THEN
				LS_SQL := 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG,AUDITENBLE) VALUES(1042, ''邮件服务器配置'', 100, ''mailServerConfig.do'', 24, null,''images/info25.png'',0)';  
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101044;
    IF LI_EXISTS = 0 THEN
				LS_SQL := 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE) VALUES (101044, ''应用对象信息'', 16, ''applicaobjinit.do'', 20, null,''images/info25.png'',0)';  
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF;
    
    
   
    
    
    SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 101042;
    IF LI_EXISTS = 0 THEN
        LS_SQL := 'INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG, AUDITENBLE)
        VALUES (101042, ''邮件发送配置'', 16, ''dbbackEmailConfigInit.do'', 16, null, ''images/info25.png'', 0)';
        EXECUTE IMMEDIATE LS_SQL;
         COMMIT;
    END IF; 
    
END;
/