package com.ideal.dubbo.interfaces.dbaas;

import java.util.Map;

public interface IScriptOfMyService
{
    public Map<String, Object> selectScriptOfMyList ( Map<String, String> args );

    public Map<String, Object> selectScriptOfMyForName ();

    public Map<String, Object> scriptOfMyStopOrStart ( Map<String, String> args );

    public Map<String, Object> deleteMyScript ( String delIds );

    public Map<String, Object> reTryMyScript ( String userName, String jsonData );

    public Map<String, Object> selectScriptOfMyForGroup ();

    public Map<String, Object> batchStart ( String startIds, String keyIds );

    public Map<String, Object> batchStop ( String stopIds, String keyIds );

    public Map<String, Object> updateMyScript ( Map<String, String> param );

    public Map execDbaasMT ( String iid, String scriptid, String activeTime, Map<String, String> paramMap );

    public Map<String, Object> batchStartServer ( String batchStartIds, String keyIds );
}
