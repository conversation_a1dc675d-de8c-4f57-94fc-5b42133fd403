package com.ideal.util.httpsclient;

import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentProducer;
import org.apache.http.entity.EntityTemplate;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicHeader;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import net.sf.ezmorph.object.DateMorpher;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.util.JSONUtils;

/**
 * 利用HttpClient进行post请求的工具类
 * @ClassName: HttpClientUtil 
 * @Description: 
 * <AUTHOR> 
 * @date 
 *  
 */
public class HttpClientUtil
{
    static Logger               log     = Logger.getLogger(HttpClientUtil.class);
    private static final String CONTENT = "Content-Type";
    private static final String JSON    = "application/json";

    /**
     * http對外請求
     * <li>Description:</li> 
     * <AUTHOR>
     * 2021年6月3日 
     * @param url
     * @param jsonstr
     * @param charset
     * @param timeOut
     * @return
     * return String
     */
    private static String doHttpPost ( String url, final String jsonstr, final String charset, int timeOut )
    {
        DefaultHttpClient httpClient = new DefaultHttpClient();
        HttpPost httpPost = null;
        String result = "";
        for (int i = 0;; i++)
        {
            try
            {
                httpPost = new HttpPost(url);
                ContentProducer cp = new ContentProducer()
                {
                    public void writeTo ( OutputStream outstream ) throws IOException
                    {
                        Writer writer = new OutputStreamWriter(outstream, charset);
                        writer.append(jsonstr);
                        writer.flush();
                    }
                };
                httpPost.setHeader(CONTENT, JSON + "; charset=" + charset);
                // httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, timeOut);
                // StringEntity entity = new StringEntity(jsonstr, charset);
                // entity.setContentEncoding(charset);
                // entity.setContentType("application/json; charset=" + charset);
                // httpPost.setEntity(entity);

                HttpEntity entity = new EntityTemplate(cp);
                httpPost.addHeader(CONTENT, JSON + "; charset=" + charset);
                httpPost.addHeader("Accept", JSON);
                httpPost.addHeader("paramsHeader", jsonstr);
                httpPost.setEntity(entity);
                HttpResponse response = httpClient.execute(httpPost);
                if ((response != null) && (response.getStatusLine().getStatusCode() == 200))
                {
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null)
                    {
                        result = EntityUtils.toString(resEntity, charset);
                    }
                } else
                {
                    if (response == null)
                    {
                        log.error("doHttpPost response is null ");
                    } else
                    {
                        log.error("doHttpPost status: " + response.getStatusLine().getStatusCode());
                    }
                }
                return result;
            } catch (Exception ex)
            {
                if (i == 2)
                {
                    log.error("call http post eroor . reTry stop");
                    return result;
                }
                log.error("call  http post eroor  : and it's will retry. retry num is :" + (i + 1));
            }
        }
    }

    /**
     * https对外请求
     * <li>Description:</li> 
     * <AUTHOR>
     * 2021年6月3日 
     * @param url
     * @param jsonstr
     * @param charset
     * @param timeOut
     * @return
     * return String
     */
    private static String doHttpsPost ( String url, String jsonstr, String charset, int timeOut )
    {
        DefaultHttpClient httpClient = null;
        HttpPost httpPost = null;
        String result = null;
        for (int j = 0;; j++)
        {
            try
            {
                httpClient = new SSLClient();
                httpPost = new HttpPost(url);
                httpPost.addHeader(CONTENT, JSON + "; charset=" + charset);
                httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, timeOut);

                // ContentProducer cp = new ContentProducer()
                // {
                // public void writeTo ( OutputStream outstream ) throws IOException
                // {
                // Writer writer = new OutputStreamWriter(outstream, charset);
                // writer.append(jsonstr);
                // writer.flush();
                // }
                // };
                //
                // HttpEntity entity = new EntityTemplate(cp);
                // httpPost.addHeader("Content-type", "application/json; charset=" + charset);
                // httpPost.addHeader("Accept", "application/json");
                // httpPost.setEntity(entity);

                StringEntity se = new StringEntity(jsonstr, charset);
                se.setContentType(JSON);
                se.setContentEncoding(new BasicHeader(CONTENT, JSON + "; charset=" + charset));
                httpPost.setEntity(se);
                HttpResponse response = httpClient.execute(httpPost);
                if ((response != null) && (response.getStatusLine().getStatusCode() == 200))
                {
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null)
                    {
                        result = EntityUtils.toString(resEntity, charset);
                    }
                } else
                {
                    if (response == null)
                    {
                        log.error("doHttpsPost response is null ");
                    } else
                    {
                        log.error("doHttpsPost status: " + response.getStatusLine().getStatusCode());
                    }
                }
                return result;
            } catch (Exception ex)
            {
                if (j == 2)
                {
                    log.error("call  https get user eroor . reTry stop");
                    return result;
                }
                log.error("call  https get user eroor  : and it's will retry. retry num is :" + (j + 1));
            }
        }
    }

    /**
     * 对外调用入口
     * <li>Description:</li> 
     * <AUTHOR>
     * 2021年6月3日 
     * @param isHttps
     * @param url
     * @param jsonstr
     * @param charset
     * @param timeOut
     * @return
     * return String
     */
    public static String doPost ( int isHttps, String url, String jsonstr, String charset, int timeOut )
    {
        String result = null;
        if (isHttps == 0)
        {
            result = doHttpsPost(url, jsonstr, charset, timeOut);
        } else
        {
            result = doHttpPost(url, jsonstr, charset, timeOut);
        }
        return result;
    }

    /**
     * doGet.model
     * <li>Description:</li> 
     * <AUTHOR>
     * 2021年6月16日 
     * @param isHttps
     * @param url
     * @param jsonstr
     * @param charset
     * @param timeOut
     * @return
     * return String
     */
    public static String doGet ( int isHttps, String url, String jsonstr, String charset, int timeOut )
    {
        String result = null;
        if (isHttps == 0)
        {
            result = doHttpsGet(url, jsonstr, charset, timeOut);
        } else
        {
            result = doHttpGet(url, jsonstr, charset, timeOut);
        }
        return result;
    }

    public static void main ( String[] args )
    {
        String url = "http://*************:9009/itsm/entegor/receive?action=sus";
        String jsonStr = "{\"ISTATE\":\"OK\"}";
        String httpOrgCreateTestRtn = doHttpsPost(url, jsonStr, "utf-8", 30000);
        System.out.println(httpOrgCreateTestRtn);
        url = "http://*************:9009/itsm/entegor/receive?action=sus";
        jsonStr = "{\"ISTATE\":\"OK\"}";
        httpOrgCreateTestRtn = doHttpGet(url, jsonStr, "utf-8", 30000);
        System.out.println(httpOrgCreateTestRtn);
    }

    /**
     * doHttpGet
     * <li>Description:</li> 
     * <AUTHOR>
     * 2021年6月16日 
     * @param url
     * @param jsonstr
     * @param charset
     * @param timeOut
     * @return
     * return String
     */
    private static String doHttpGet ( String url, String jsonstr, String charset, int timeOut )
    {
        DefaultHttpClient httpClient = new DefaultHttpClient();
        HttpGet get = null;
        String result = "";
        HttpResponse response = null;
        for (int i = 0;; i++)
        {
            try
            {
                if (!"".equals(jsonstr) && null != jsonstr)
                {
                    HttpGetWithEntity httpGetWithEntity = new HttpGetWithEntity(url);
                    StringEntity httpEntity = new StringEntity(jsonstr, "application/json; charset=utf-8");
                    httpGetWithEntity.setEntity(httpEntity); // 执行请求操作，并拿到结果（同步阻塞）
                    response = httpClient.execute(httpGetWithEntity);
                } else
                {
                    get = new HttpGet(url);
                    get.addHeader("Content-type", "application/json; charset=utf-8");
                    get.setHeader("Accept", "application/json");
                    response = httpClient.execute(get);
                }
                if ((response != null) && (response.getStatusLine().getStatusCode() == 200))
                {
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null)
                    {
                        result = EntityUtils.toString(resEntity, charset);
                    }
                } else
                {
                    if (response == null)
                    {
                        log.error("doHttpGet response is null ");
                    } else
                    {
                        log.error("doHttpGet status: " + response.getStatusLine().getStatusCode());
                    }
                }
                return result;
            } catch (Exception ex)
            {
                if (i == 2)
                {
                    log.error("call http get user eroor . reTry stop");
                    return result;
                }
                log.error("call  http get user eroor  : and it's will retry. retry num is :" + (i + 1));
            } finally
            {
            }
        }
    }

    /**
     * https对外请求
     * <li>Description:</li> 
     * <AUTHOR>
     * 2021年6月3日 
     * @param url
     * @param jsonstr
     * @param charset
     * @param timeOut
     * @return
     * return String
     */
    private static String doHttpsGet ( String url, String jsonstr, String charset, int timeOut )
    {
        DefaultHttpClient httpClient = null;
        HttpGet get = null;
        String result = null;
        for (int j = 0;; j++)
        {
            try
            {
                httpClient = new SSLClient();
                get = new HttpGet(url);
                HttpResponse response = httpClient.execute(get);
                if ((response != null) && (response.getStatusLine().getStatusCode() == 200))
                {
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null)
                    {
                        result = EntityUtils.toString(resEntity, charset);
                    }
                } else
                {
                    if (response == null)
                    {
                        log.error("doHttpsGet response is null ");
                    } else
                    {
                        log.error("doHttpsGet status: " + response.getStatusLine().getStatusCode());
                    }
                }
                return result;
            } catch (Exception ex)
            {
                if (j == 2)
                {
                    log.error("call  https get user eroor . reTry stop");
                    return result;
                }
                log.error("call  https get user eroor  : and it's will retry. retry num is :" + (j + 1));
            }
        }
    }

    public static List getDTOList ( String jsonString, Class clazz )
    {
        setDataFormat2JAVA();
        JSONArray array = JSONArray.fromObject(jsonString);
        List list = new ArrayList();
        for (Iterator iter = array.iterator(); iter.hasNext();)
        {
            JSONObject jsonObject = (JSONObject) iter.next();
            list.add(JSONObject.toBean(jsonObject, clazz));
        }
        return list;
    }

    private static void setDataFormat2JAVA ()
    {
        JSONUtils.getMorpherRegistry()
                .registerMorpher(new DateMorpher(new String[] { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss" }));
    }

    /**
     * 从json HASH表达式中获取一个map，该map支持嵌套功能 形如：{"id" : "johncon", "name" : "小强"}
     * 注意commons-collections版本，必须包含org.apache.commons.collections.map.MultiKeyMap
     * 
     * @param object
     * @return
     */
    public static Map getMapFromJson ( String jsonString )
    {
        setDataFormat2JAVA();
        JSONObject jsonObject = JSONObject.fromObject(jsonString);
        Map map = new HashMap();
        for (Iterator iter = jsonObject.keys(); iter.hasNext();)
        {
            String key = (String) iter.next();
            map.put(key, jsonObject.get(key));
        }
        return map;
    }

    /**
     * 从一个JSON数组得到一个java对象集合，其中对象中包含有集合属性
     * 
     * @param object
     * @param clazz
     * @param map 集合属性的类型
     * @return
     */
    public static List getDTOList ( String jsonString, Class clazz, Map map )
    {
        setDataFormat2JAVA();
        JSONArray array = JSONArray.fromObject(jsonString);
        List list = new ArrayList();
        for (Iterator iter = array.iterator(); iter.hasNext();)
        {
            JSONObject jsonObject = (JSONObject) iter.next();
            list.add(JSONObject.toBean(jsonObject, clazz, map));
        }
        return list;
    }

    /**
     * 从一个JSON数组得到一个java对象数组，形如： [{"id" : idValue, "name" : nameValue}, {"id" : idValue, "name" :
     * nameValue}, ...]
     * 
     * @param object
     * @param clazz
     * @param map
     * @return
     */
    public static Object[] getDTOArray ( String jsonString, Class clazz, Map map )
    {
        setDataFormat2JAVA();
        JSONArray array = JSONArray.fromObject(jsonString);
        Object[] obj = new Object[array.size()];
        for (int i = 0; i < array.size(); i++)
        {
            JSONObject jsonObject = array.getJSONObject(i);
            obj[i] = JSONObject.toBean(jsonObject, clazz, map);
        }
        return obj;
    }

    /**
     * 从一个JSON数组得到一个java对象数组，形如： [{"id" : idValue, "name" : nameValue}, {"id" : idValue, "name" :
     * nameValue}, ...]
     * 
     * @param object
     * @param clazz
     * @return
     */
    public static Object[] getDTOArray ( String jsonString, Class clazz )
    {
        setDataFormat2JAVA();
        JSONArray array = JSONArray.fromObject(jsonString);
        Object[] obj = new Object[array.size()];
        for (int i = 0; i < array.size(); i++)
        {
            JSONObject jsonObject = array.getJSONObject(i);
            obj[i] = JSONObject.toBean(jsonObject, clazz);
        }
        return obj;
    }

    /**
     * 从一个JSON 对象字符格式中得到一个java对象，其中beansList是一类的集合，形如： {"id" : idValue, "name" : nameValue, "aBean"
     * : {"aBeanId" : aBeanIdValue, ...}, beansList:[{}, {}, ...]}
     * 
     * @param jsonString
     * @param clazz
     * @param map 集合属性的类型 (key : 集合属性名, value : 集合属性类型class) eg: ("beansList" : Bean.class)
     * @return
     */
    public static Object getDTO ( String jsonString, Class clazz, Map map )
    {
        JSONObject jsonObject = null;
        try
        {
            setDataFormat2JAVA();
            jsonObject = JSONObject.fromObject(jsonString);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return JSONObject.toBean(jsonObject, clazz, map);
    }

    /**
     * 从一个JSON 对象字符格式中得到一个java对象，形如： {"id" : idValue, "name" : nameValue, "aBean" : {"aBeanId" :
     * aBeanIdValue, ...}}
     * 
     * @param object
     * @param clazz
     * @return
     */
    public static Object getDTO ( String jsonString, Class clazz )
    {
        JSONObject jsonObject = null;
        try
        {
            setDataFormat2JAVA();
            jsonObject = JSONObject.fromObject(jsonString);
        } catch (Exception e)
        {
            e.printStackTrace();
        }
        return JSONObject.toBean(jsonObject, clazz);
    }

}