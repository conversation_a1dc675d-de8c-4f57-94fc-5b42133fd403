-- 8.11.0 version RT patch is as follows
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=17;
	IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(17,''例行任务用户发起操作系统权限'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_SCRIPT_AUDITING WHERE iid=18;
	IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'INSERT INTO IEAI_SCRIPT_AUDITING(IID,INAME)  VALUES(18,''例行任务用户审核操作系统权限'')';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END	IF;
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE WHERE GROUPID=47;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (47, ''例行任务'', ''例行任务模块组'', 47, ''images/info110.png'')';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID=47;
	IF	LI_EXISTS = 0 THEN
	SET	LS_SQL ='INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (47,47,''例行任务源'','''','''','''','''',0)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$
BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE  LI_EXISTS  NUMERIC(2);

	SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-47;
	IF	LI_EXISTS = 0 THEN
	SET LS_SQL ='INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID,  IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-47, 0, ''所有例行任务业务系统'', 0, 0, 0, '''', '''', 0,  '''', '''', 0, 0, 47, -47, -47, -47)';
		 PREPARE SQLA FROM LS_SQL; 
		 EXECUTE SQLA;
	END	IF;
	
END
$