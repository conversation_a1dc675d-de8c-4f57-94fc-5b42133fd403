---插入中文方便修改编码。误删！！！

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_SUS_RUN_INSTANCEINFO' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_SUS_RUN_INSTANCEINFO MODIFY (ISHELLSCRIPT VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE' AND COLUMN_NAME='IORDERNUM';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_RUN_INSTANCE MODIFY (IORDERNUM VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/

DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_RUNINFO_INSTANCE MODIFY (ISHELLSCRIPT VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUN_INSTANCE_HIS' AND COLUMN_NAME='IORDERNUM';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_RUN_INSTANCE_HIS MODIFY (IORDERNUM VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS MODIFY (ISHELLSCRIPT VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS MODIFY (ISHELLSCRIPT VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/


DECLARE
     LS_SQL VARCHAR2(2000);
     LI_EXISTS SMALLINT;
BEGIN 
	 SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM USER_TAB_COLUMNS WHERE TABLE_NAME='TMP_RUN_INSTANCE' AND COLUMN_NAME='ISHELLSCRIPT';
		IF	LI_EXISTS != 0 THEN
        LS_SQL := '	ALTER TABLE TMP_RUN_INSTANCE MODIFY (ISHELLSCRIPT VARCHAR2(3000) )';
		EXECUTE IMMEDIATE LS_SQL;
	 END IF;
	 commit;
END;
/