package com.ideal.ieai.server.repository.hd.ic.gfcheckhc;

public class GfCheckHcMainIndexModel
{

    private Long iid;
    
    private String iname;
    
    private Long tasktype;
    
    private String tasktypename;
    
    private Integer taskstatus;
    
    private String taskstatusname;

    public Long getIid ()
    {
        return iid;
    }

    public void setIid ( Long iid )
    {
        this.iid = iid;
    }

    public String getIname ()
    {
        return iname;
    }

    public void setIname ( String iname )
    {
        this.iname = iname;
    }

    public Long getTasktype ()
    {
        return tasktype;
    }

    public void setTasktype ( Long tasktype )
    {
        this.tasktype = tasktype;
    }

    public String getTasktypename ()
    {
        return tasktypename;
    }

    public void setTasktypename ( String tasktypename )
    {
        this.tasktypename = tasktypename;
    }

    public Integer getTaskstatus ()
    {
        return taskstatus;
    }

    public void setTaskstatus ( Integer taskstatus )
    {
        this.taskstatus = taskstatus;
    }

    public String getTaskstatusname ()
    {
        return taskstatusname;
    }

    public void setTaskstatusname ( String taskstatusname )
    {
        this.taskstatusname = taskstatusname;
    }
    
    
    
}
