	-- 4.7.17 not exists	
	-- 4.7.18 not exists	
	-- 4.7.19 not exists	
	-- 4.7.20 not exists	
	-- 4.7.21 not exists	
	
	-- 4.7.22	
	BEGIN
        DECLARE LS_SQL VARCHAR(4000);
        DECLARE  LI_EXISTS  NUMERIC(2);

        
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_UPLOAD';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='CREATE TABLE IEAI_COLLECTION_UPLOAD(IID DECIMAL(19) NOT NULL,RECORDID VARCHAR(255),TASKTYPE DECIMAL(4),AGENTIP VARCHAR(30),SAVEFILEPATH VARCHAR(255),FILECONTENT VARCHAR(255),IUNZIP VARCHAR(10),UNZIPPATH VARCHAR(255),ISSUCCESS VARCHAR(10),<PERSON>ER<PERSON>ERMISSION VARCHAR(255),<PERSON><PERSON><PERSON>PERMISSION VARCHAR(255),<PERSON><PERSON><PERSON><PERSON><PERSON>SSION VARCHAR(255),OPDESC VARCHAR(255),OPUS<PERSON> VARCHAR(255),OPTIME DECIMAL(19),CONSTRAINT PK_IEAI_COLLECTION_UPLOAD PRIMARY KEY (IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_PICKUPINFO';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='CREATE TABLE IEAI_COLLECTION_PICKUPINFO(IID DECIMAL(19) NOT NULL, RECORDID VARCHAR(255),TASKTYPE DECIMAL(4), IINSTANCEID DECIMAL(19), SENDURL VARCHAR(255), SENDUSER VARCHAR(255), SENDIP VARCHAR(20), SENDTIME DECIMAL(19), EXECSTATUS DECIMAL(4), IDESC VARCHAR(255), ISSENDSUCSTATUS DECIMAL(2),CONSTRAINT PK_IEAI_COLLECTION_PICKUPINFO PRIMARY KEY (IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
	END
		$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSTYPE' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN ISYSTYPE DECIMAL(19) DEFAULT 0 ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DEVICETYPE' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN DEVICETYPE VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TASKTYPE' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN TASKTYPE DECIMAL(4) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OUTPATH' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN OUTPATH VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GLOBALIP' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN GLOBALIP VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='MOBILEID' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN MOBILEID VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TASKTYPE' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN TASKTYPE DECIMAL(4) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OUTPATH' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN OUTPATH VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GLOBALIP' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN GLOBALIP VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='MOBILEID' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN MOBILEID VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='HD_FTP_SCRIPT';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table HD_FTP_SCRIPT '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_INSTANCE_VERSION_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_INSTANCE_VERSION_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_INSTANCE_VERSION';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_INSTANCE_VERSION '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
			

	END
		 $
	
	-- 4.7.23	
	BEGIN
    DECLARE LS_SQL VARCHAR(4000);
    DECLARE  LI_EXISTS  NUMERIC(2);

        
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_RESCALL';
		IF LI_EXISTS = 0 THEN
		    SET LS_SQL ='CREATE TABLE IEAI_COLLECTION_RESCALL(IID DECIMAL(19) NOT NULL, RECORDID VARCHAR(255), IINSTANCEID DECIMAL(19), STARTTIME DECIMAL(19), ENDTIME DECIMAL(19), STATUS DECIMAL(2), CONSTRAINT PK_IEAI_COLLECTION_RESCALL PRIMARY KEY (IID))';
		    PREPARE	SQLA FROM LS_SQL;
		    EXECUTE SQLA;
		END IF;
		
	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SENDMSG' AND TABNAME='IEAI_COLLECTION_PICKUPINFO'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD COLUMN SENDMSG VARCHAR(4000) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AGENTIP' AND TABNAME='IEAI_COLLECTION_PICKUPINFO'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD COLUMN AGENTIP VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISYSTYPE' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN ISYSTYPE DECIMAL(19) DEFAULT 0 ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DEVICETYPE' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN DEVICETYPE VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DBINSNAME' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN DBINSNAME VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='USERPERMISSION' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN USERPERMISSION VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GROUPPERMISSION' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN GROUPPERMISSION VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PARAMPERMISSION' AND TABNAME='HD_FTP_SCRIPT'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE HD_FTP_SCRIPT ADD COLUMN PARAMPERMISSION VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TASKTYPE' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN TASKTYPE DECIMAL(4) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OUTPATH' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN OUTPATH VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GLOBALIP' AND TABNAME='IEAI_INSTANCE_VERSION'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION ADD COLUMN GLOBALIP VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TASKTYPE' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN TASKTYPE DECIMAL(4) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OUTPATH' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN OUTPATH VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='GLOBALIP' AND TABNAME='IEAI_INSTANCE_VERSION_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_INSTANCE_VERSION_HIS ADD COLUMN GLOBALIP VARCHAR(255) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='NETEQUIIP' AND TABNAME='IEAI_RUNINFO_INSTANCE'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE ADD COLUMN NETEQUIIP VARCHAR(20) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='NETEQUIIP' AND TABNAME='IEAI_RUNINFO_INSTANCE_HIS'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN NETEQUIIP VARCHAR(20) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='NETEQUIIP' AND TABNAME='IEAI_RUNINFO_INSTANCE_HIS_DATA'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS_DATA ADD COLUMN NETEQUIIP VARCHAR(20) ';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_PICKUPINFO';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_COLLECTION_PICKUPINFO '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='HD_FTP_SCRIPT';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table HD_FTP_SCRIPT '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_INSTANCE_VERSION_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_INSTANCE_VERSION_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_INSTANCE_VERSION';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_INSTANCE_VERSION '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
			
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUNINFO_INSTANCE';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RUNINFO_INSTANCE '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RUNINFO_INSTANCE_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS_DATA';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RUNINFO_INSTANCE_HIS_DATA '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;	
			
	END
		 $

	BEGIN
        DECLARE LS_SQL VARCHAR(4000);
        DECLARE  LI_EXISTS  NUMERIC(2);
        
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 19 AND IGROUPMESSGEID=19;
	  IF	LI_EXISTS = 0 THEN
	    	SET	LS_SQL = 'INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,IISBASIC) VALUES (19,19,''信息采集源'',0)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	  END	IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PROJECT WHERE IID=-19;
	  IF	LI_EXISTS = 0 THEN
	    SET	LS_SQL = 'INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID, IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-19, 0, ''所有采集信息业务系统'', 0, 0, 0, '''', '''', 0, '''', '''', 0, 0, 19, -19, -19, -19)';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	  END	IF;
	    
	  SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_GROUPMESSAGE  WHERE GROUPID=19;
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'INSERT INTO IEAI_GROUPMESSAGE(GROUPID, GROUPNAME, GROUPDESCRIPTION, IORDER, IIMG)	VALUES(19, ''信息采集'', ''信息采集模块组'', 19, ''images/info110.png'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_FTPTYPE  WHERE TID=45;
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'INSERT INTO IEAI_FTPTYPE(TID,FTPTYPENAME) VALUES(45,''信息采集'')';
			PREPARE SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
	END
		$
	
	-- 4.7.24			
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AGENTIP' AND TABNAME='IEAI_COLLECTION_PICKUPINFO'; 
		IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_COLLECTION_PICKUPINFO ADD COLUMN AGENTIP	VARCHAR(50)';  
	    	PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END IF;

	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECTION_PICKUPINFO';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_COLLECTION_PICKUPINFO '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
	END
		 $
		
	-- 4.7.25
	BEGIN
    DECLARE LS_SQL VARCHAR(4000);
    DECLARE  LI_EXISTS  NUMERIC(2);
        
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_DBSOURCE WHERE IDBSOURCEID = 19 AND IGROUPMESSGEID=19;
	  IF	LI_EXISTS = 1 THEN
	    	SET	LS_SQL = 'UPDATE IEAI_DBSOURCE SET IDBSOURCENAME = ''递蓝科源'' WHERE IDBSOURCEID = 19';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	  END	IF;
	  
	END
		$
		
	-- 4.7.26
	BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	DECLARE LI_EXISTS1 NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='NETEQUIIP' AND TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND TYPENAME<>'VARCHAR';
		IF LI_EXISTS > 0 THEN 
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS1 FROM SYSCAT.COLUMNS WHERE COLNAME='NETEQUIIPTEMP' AND TABNAME='IEAI_RUNINFO_INSTANCE_HIS';
			IF LI_EXISTS1 = 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ADD COLUMN NETEQUIIPTEMP VARCHAR(20) ';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
		END IF;	
			
	END
		$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RUNINFO_INSTANCE_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
	END
		$
	
	BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'NETEQUIIP' AND TYPENAME<>'VARCHAR';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'UPDATE IEAI_RUNINFO_INSTANCE_HIS SET NETEQUIIPTEMP=NETEQUIIP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'UPDATE IEAI_RUNINFO_INSTANCE_HIS SET NETEQUIIP=NULL';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS ALTER COLUMN NETEQUIIP SET DATA TYPE VARCHAR(20) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RUNINFO_INSTANCE_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
	END
		$
		
	BEGIN
	DECLARE LS_SQL VARCHAR(4000);
	DECLARE LI_EXISTS NUMERIC(2);
	
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'NETEQUIIPTEMP';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'UPDATE IEAI_RUNINFO_INSTANCE_HIS SET NETEQUIIP=NETEQUIIPTEMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
			COMMIT;
		END IF;
			
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS' AND COLNAME = 'NETEQUIIPTEMP';
		IF	LI_EXISTS > 0 THEN
			SET LS_SQL = 'ALTER TABLE IEAI_RUNINFO_INSTANCE_HIS DROP COLUMN NETEQUIIPTEMP';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
	END
		$
		
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_RUNINFO_INSTANCE_HIS';
		IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'CALL SYSPROC.ADMIN_CMD(''REORG  table IEAI_RUNINFO_INSTANCE_HIS '') ';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
		END	IF;
		
	END
		$
		
	
	