Ext.Loader.setConfig({
  enabled:true,
  disableCaching:false,
  paths:{
    'Go':'js/ux/gooo'
  }
});
var queryForm;
var queryFormPanel=null;
var releaseMonitorStore=null;
var targetComboBox=null;
Ext.onReady(function() {
	// 清理主面板的各种监听时间
  destroyRubbish();
//主panel高度减掉的定值
	var staticHeight = 37;
  Ext.tip.QuickTipManager.init ();
	if(susitsmmanualconfirmmerge)
		{
			//中原银行允许包悬浮的提醒信息中包名特别长不换行
			Ext.override(Ext.tip.Tip,{
		       maxWidth:1500
		    })
		}
  Ext.define('ReleaseMonitorModel', {
    extend : 'Ext.data.Model',
    fields : [ {
      name : 'iid',
      type : 'long'
    }, {
      name : 'sysId',
      type : 'string'
    }, {
      name : 'sysName',
      type : 'string'
    }, {
      name : 'runInsName',
      type : 'string'
    }, {
      name : 'insDes',
      type : 'string'
    }, {
      name : 'version',
      type : 'string'
    }, {
      name : 'execType',
      type : 'string'
    }, {
      name : 'startTime',
      type : 'string'
    }, {
      name : 'endTime',
      type : 'string'
    }, {
      name : 'state',
      type : 'string'
    }, {
      name : 'startUser',
      type : 'string'
    }, {
	    name : 'manGroup',
    	type : 'string'
    },{
    	name : 'auditorUser',
    	type : 'string'
    },{
        name : 'pkgNames',
        type : 'string'
    },{
        name : 'time_consuming',
        type : 'long'
    },{
        name : 'workOrderNum',
        type : 'string'        	
    },{
        name : 'versionDesc',
        type : 'string'        	
    },{
        name : 'deployTarget',
        type : 'string'        	
    },{
        name : 'ipmporder',
        type : 'string'        	
    },{
        name : 'ialarmrulename',
        type : 'string'        	
    },{
        name : 'ialarmsuppression',
        type : 'string'        	
    },{
        name : 'ialarmclearing',
        type : 'string'        	
    },{
        name : 'iipmpcachecode',
        type : 'string'        	
    }   ]
  });
  
  Ext.define('userModel', {
    extend : 'Ext.data.Model',
    fields : [{
      name : 'fullName',
      type : 'string'
    }]
  });
  
  var startUserStore = Ext.create('Ext.data.Store', {
    autoLoad: true,
    autoDestroy: true,
    model: 'userModel',
    proxy: {
      type: 'ajax',
      url: 'rmStartUserList.do',
      reader: {
        type: 'json',
        root: 'dataList'
      }
    }
  });
  
  startUserStore.on('beforeload', function(store, options) {
      var new_params =
      {
    		  sysType : 3
      };
      Ext.apply (store.proxy.extraParams, new_params);  
  });
  
  
  Ext.define('systemModel', {
	    extend : 'Ext.data.Model',
	    fields : [{
	      name : 'sysName',
	      type : 'string'
	    }]
	  });
  
  /**系统名称下拉选**/
  var systemStore = Ext.create('Ext.data.Store', {
	    autoLoad: true,
	    autoDestroy: true,
	    model: 'systemModel',
	    proxy: {
	      type: 'ajax',
	      url: 'getReleaseMonitorSysName.do?statState='+statState,
	      timeout:1800000,
	      reader: {
	        type: 'json',
	        root: 'dataList'
	      }
	    }
	  });
  systemStore.on('beforeload', function(store, options) {
		var new_params = {
			sysName : "",
			insName : "",
			version : "",
			startUser : "",
			startTime : "",
			endTime : "",
			workOrderNum:"",
			targetFlag:"",
			isHistory : 1
		};
		Ext.apply(systemStore.proxy.extraParams, new_params);
	});
  
  //设置应用变更->变更历史里筛选条件的默认开始时间为当日3个月之前的时间。杨柳修改于20210621。
  var default_startTime = new Date();
  default_startTime.setMonth(default_startTime.getMonth() - 3);
  default_startTime.setDate(default_startTime.getDate());
  default_startTime.setHours(0);
  default_startTime.setMinutes(0);
  default_startTime.setSeconds(0);
  var startTime = Ext.create('Go.form.field.DateTime',{
    labelWidth : 65,
    labelAlign : 'right',
    width: 285,
    format : 'Y-m-d H:i:s',
    fieldLabel: '开始时间',
    //此为之前默认开始时间设置
    //value:startTimeQueryReturn,
	value:pfFlag?default_startTime:startTimeQueryReturn,
    name: 'startTime',
    padding : '0 10 0 5'
  });
  ////设置应用变更->变更历史里筛选条件的默认结束时间为当日23:59:59。杨柳修改于20210621。
  var default_endTime = new Date();
  default_endTime.setDate(default_endTime.getDate());
  default_endTime.setHours(23);
  default_endTime.setMinutes(59);
  default_endTime.setSeconds(59);
  var endTime = Ext.create('Go.form.field.DateTime',{
    labelWidth : 65,
    labelAlign : 'right',
    width: 285,
    format : 'Y-m-d H:i:s',
    fieldLabel: '结束时间',
    //此为之前默认结束时间设置
    //value:endTimeQueryReturn,
	value:pfFlag?default_endTime:endTimeQueryReturn,
    //value:endTimeQueryReturn,
    name: 'endTime',
    padding : '0 10 0 5' 
  });
  targetComboBox =new Ext.form.ComboBox({
      store :  new Ext.data.SimpleStore({
          fields : ['id', 'target_flag'],
          data : [[0, '测试部署'], [1, '通用部署'], [2, '全部']]
      }), 
      displayField : 'target_flag',
      valueField : 'id',
      typeAhead : true,
      triggerAction : 'all',
      allowBlank : false,
      forceSelection : true,
      fieldLabel: '部署目标',
      labelWidth: 60,
      width : 150,
      name:'deployTargetComboBox',
      id:'deployTargetComboBox',
      hidden:!pfTypeSwitch,
      mode : 'local'
 })
    var allRadio = new Ext.form.Radio({
        name : "bgType",//后台接受的名称
        inputValue : 1,//传后台的值
        width : 55,
        boxLabel : "全部",//页面显示的值
        checked : true//默认选择的值
       });
    var sysRadio = new Ext.form.Radio({
        name : "bgType",//后台接受的名称
        inputValue : 2,//传后台的值
        width : 80,
        boxLabel : "应用变更",//页面显示的值
       });
    var dataRadio = new Ext.form.Radio({
        name : "bgType",
        inputValue : 3,
        width : 80,
        boxLabel : "数据变更"
       });
     var susTypeRadioGroup = new Ext.form.RadioGroup({
	    id:"bgTypeGroup",
        name : "bgTypeGroup",
        items : [ allRadio, sysRadio, dataRadio ],
        hidden:!ycShowSusTypeSwitch,//邮储控件显示开关
       });
 queryForm = Ext.create('Ext.form.Panel', {
	  region : 'north',
	  collapsible : true,//可收缩
	  collapsed : false,//默认收缩
	    border: false,
	    layout: 'form',
	    items : [ {
		    layout:'column',
		    border : false,
			items : [{
		        /*labelWidth : 65,
		        labelAlign : 'right',
		        width: 285,
		        xtype: 'textfield',
		        fieldLabel: '系统名称',
		        name: 'sysName',
		        padding : '0 10 0 5',
		        */
				labelWidth : 65,
		        labelAlign : 'right',
		        width: 285,
		        xtype : 'combo',
		        fieldLabel : '系统名称',
		        queryMode: 'local',
//		          editable : false,
		        name : 'sysName',
		        anyMatch : true,
		        triggerAction : "all",
		        padding : '0 10 0 5',
		        store: systemStore,
		        displayField: 'sysName',
		        valueField: 'sysName',
		        listConfig: {minHeight: 80}
		      },{
		        labelWidth : 65,
		        labelAlign : 'right',
		        width: 285,
		        xtype: 'textfield',
		        fieldLabel: '变更说明',
		        name: 'insName',
		        hidden:false,
		        padding : '0 10 0 5' 
		      },{
		        labelWidth : 65,
		        labelAlign : 'right',
		        width: 285,
		        xtype: 'textfield',
		        fieldLabel: '变更单号',
		        name: 'version',
		        padding : '0 10 0 5'
		      },{
			        labelWidth : 65,
			        labelAlign : 'right',
			        width: 285,
			        xtype: 'textfield',
			        fieldLabel: '工单号',
			        name: 'workOrderNum',
			        padding : '0 10 0 5'
			    },targetComboBox,susTypeRadioGroup]},
			   {
				layout:'column',
				border : false,
				items : [{
			        labelWidth : 65,
			        labelAlign : 'right',
			        width: 285,
			        xtype : 'combo',
			        fieldLabel : '启动用户',
			        queryMode: 'local',
//			          editable : false,
			        name : 'startUser',
			        anyMatch : true,
			        triggerAction : "all",
			        padding : '0 10 0 5',
			        store: startUserStore,
			        displayField: 'fullName',
			        valueField: 'fullName',
			        listConfig: {minHeight: 80}
			      }, startTime, endTime,      
			         {
			        xtype: 'button',
			        cls:'Common_Btn',
			        text: '查询',
//			        width:60,
			        margin : '0 0 0 10',
			        handler: function() {
			          queryWhere();
			        }
			      },{
			        xtype: 'button',
			        margin : '0 0 0 10',
			        cls:'Common_Btn',
			        text: '重置',
//			        width:60,
			        handler: function() {
			          resetWhere();
			        }
			      }, {
		              xtype : 'button',
		              cls:'Common_Btn',
		              text : '导出',
		              //width : 60,
		              margin : '0 0 0 10',
		              handler : function ()
		              {
		              	exportWhere ();
		              }
				   },{
		              xtype : 'button',
		              cls:'Common_Btn',
		              text : '详细导出',
		              //width : 60,
		              margin : '0 0 0 10',
		              hidden : !descExportSwitch,
		              handler : function ()
		              {
		            	  descExportWhere ();
		              }
				   }]}
				   ]//,
//	    items: [{
//	      layout:'column',
//	      border : false,
//	      items: []
//	    },{
//	      layout:'column',
//	      border : false,
//	      items: []
//	    }]
	  });
  contentPanel.getHeader().hide();//设置contentPanel标题头隐藏
  queryForm.setTitle(contentPanel.title);//将contentPanel标题显示在查询Form上
//  queryFormPanel=Ext.create('Ext.Panel', {
////		title:'查询条件1',
////		titleCollapse : true,
////		collapsible:true,
////		collapsed :true,
////	  	animCollapse : true,
//	  	hidden:true,
//		witdh:'100%',
//		layout:'fit',
//		border : false,	
//		items: [queryForm ]
//	});
    releaseMonitorStore = Ext.create('Ext.data.Store', {
    autoLoad : false,
    autoDestroy : true,
    remoteSort: true,
    pageSize: 50,
    model : 'ReleaseMonitorModel',
    proxy : {
      type : 'ajax',
      url : 'getReleaseMonitor.do?statState='+statState,
      timeout:1800000,
      reader : {
        type : 'json',
        root : 'dataList'
      }
    }
  });
  
  releaseMonitorStore.on('beforeload', function(store, options) {
	    var targetFlag = "";
	    if(pfTypeSwitch){
	    	targetFlag = targetComboBox.getValue() ;
	    }
		var sysName = queryForm.getForm().findField("sysName")
				.getValue();
		var insName = queryForm.getForm().findField("insName")
				.getValue();
		var version = queryForm.getForm().findField("version")
				.getValue();
		var startUser = queryForm.getForm().findField("startUser")
				.getValue();
		var startTimes = startTime.getRawValue();
		var endTimes = endTime.getRawValue();
		var bgType = Ext.getCmp("bgTypeGroup").getChecked()[0].boxLabel;
	    var _workOrderNum=queryForm.getForm().findField("workOrderNum").getValue();
		if (isSpecialCharVerificationN(sysName, "系统名称不能含有特殊字符!"))
			return;
		if (isSpecialCharVerificationN(insName, "变更说明不能含有特殊字符!"))
			return;
		if (isSpecialCharVerificationN(version, "变更单号不能含有特殊字符!"))
			return;
		if(!pfFlag){
			if (isSpecialCharVerification(startUser, "启动用户不能含有特殊字符!"))
				return;
		}
		if (!isDateFormat(startTimes))
			return;
		if (!isDateFormat(endTimes))
			return;
		var new_params = {
//		 sysName :queryForm.getForm().findField("sysName")
//				.getValue(),
//		 insName : queryForm.getForm().findField("insName")
//				.getValue(),
//		 version :queryForm.getForm().findField("version")
//				.getValue(),
//		 startUser : queryForm.getForm().findField("startUser")
//				.getValue(),
//		 startTime :startTime.getRawValue(),
//		 endTime : endTime.getRawValue(),
			sysName : sysName,
			insName : insName,
			version : version,
			startUser : startUser,
			startTime : startTimes,
			endTime : endTimes,
			workOrderNum:_workOrderNum,
			targetFlag:targetFlag,
			isHistory : 1,
			bgType : bgType
		};
		Ext.apply(releaseMonitorStore.proxy.extraParams, new_params);
	});
  
  var releaseMonitorColumns = [{
    text : '序号',
    xtype : 'rownumberer',
    width : 65
  }, {
    text : 'ID',
    dataIndex : 'iid',
    width : 40,
    hidden : true
  }, {
    text : '系统名称',
    dataIndex : 'sysName',
    width: 150,
    renderer: function(value,metaData,record){
//    	var state =record.get('state');
//    	if(state =='部署失败'){
//    		return '<a href="javascript:void(0);"  style="color: #e73030;text-decoration: underline;" onclick="forwardRMFlow('+ record.get('iid') +',\''+value+'\')">'+ value +'</a>';
//    	}else{
//    		 return '<a href="javascript:void(0);" style="text-decoration:underline;" onclick="forwardRMFlow('+ record.get('iid') +',\''+value+'\')">'+ value +'</a>';
//    	}
    	 return '<a href="javascript:void(0);" style="text-decoration:underline;" onclick="forwardRMFlow('+ record.get('iid') +',\''+value+'\')">'+ value +'</a>';
    	
    }
  }, {
    text : '变更说明',
    dataIndex : 'runInsName',
    width : 150,
    renderer: function(value,metaData,record){
    	return '<a href="javascript:void(0);" style="text-decoration:underline;"  onclick="forwardPackgeList(\''
		+ record.get('iid')
		+ '\', \''
    	+ record.get('sysName')
		+ '\', \''
		+ record.get('version')
		+ '\', \''
		+ record.get('startTime')
		+ '\', \''
		+ record.get('endTime')
		+ '\', \''
		+ record.get('startUser')
		+ '\', \''
		+ record.get('state')
		+ '\')">'
		+ value
		+ '</a>';
    }
  }, {
    text : '系统说明',
    dataIndex : 'insDes',
    width : 150,
    hidden: true
  }, {
    text : '变更单号',
    dataIndex : 'version',
    width : 100
  },{
	text : '执行方式',
	dataIndex : 'execType',
    width : 100
  },{
	text : '部署目标',
    dataIndex : 'deployTarget',
	width : 100,
	hidden:!pfTypeSwitch
  },{
    text : '开始时间',
    dataIndex : 'startTime',
    width : 150
  }, {
    text : '结束时间',
    dataIndex : 'endTime',
    width : 150
  }, {
	    text : '耗时(s)',
	    dataIndex : 'time_consuming',
	    width : 150
  }, {
    text : '状态',
    dataIndex : 'state',
    width : 90,
    renderer: function(value,metaData,record){
      /*if(value=="异常") {
        return "<span style='background: #fd808a;color:#fff;padding: 2px;'>"+ value +"</span>";
      } else {
        return value;
      }*/
      if(value=="运行中")
    	  {
    	  return "<span class='Blue_color State_Color'>"+ value +"</span>";
    	  }else if(value=="部署成功")
    		  {
    		  return "<span class='Green_color State_Color'>"+ value +"</span>";
    		  }else if(value=="手工部署")
    		  {
        		  return "<span class='Green_color State_Color'>"+ value +"</span>";
        	}
    	  else if(value=="部署失败")
    		  {
    	      return "<span class='Red_color State_Color'>"+ value +"</span>";
    		  }else if(value=="暂停")
    		  {
    		  return "<span class='yellow_color State_Color'>"+ value +"</span>";
    		  }else if(value=="未知")
    		  {
    		  return "<span class='Gray_color State_Color'>"+ value +"</span>";
    		  }
    }
  },{ 
    text: '发起人',
    dataIndex: 'startUser',
    width: 95
  },{
	  text:'启动人',
	  dataIndex:'manGroup',
	  hidden : !(susThreeCheckSwitch&&isGDDownloadLocalPath),
	  width:95
  },{
	  text:'审核人',
	  dataIndex:'auditorUser',
	  width:95
  },{ 
	    text: '包名',
	    dataIndex: 'pkgNames',
	    width: 95,
	 // 鼠标悬浮提醒
        renderer : function (value, metaData, record, colIndex, store, view)
        {
		if(susitsmmanualconfirmmerge)
		{
			var value2=value.replaceAll(",",",<br>");
            metaData.tdAttr = 'data-qtip="' + value2 + '"';
           
		}else
		{
			 metaData.tdAttr = 'data-qtip="' + value + '"';
		}
		 return value;
	 		
        }
  },{
	  text:'告警规则名称',
	  dataIndex:'ialarmrulename',
	  hidden : !alarmNanJingSwitch,
	  width:150
  },{
	  text:'告警抑制',
	  dataIndex:'ialarmsuppression',
	  hidden : !alarmNanJingSwitch,
	  width:65
  },{
	  text:'告警解除',
	  dataIndex:'ialarmclearing',
	  hidden : !alarmNanJingSwitch,
	  width:65
  },{ 
	    text: '工单号',
	    dataIndex: 'workOrderNum',
	    width: 95
	  },{
		    text : '变更说明(详细)',
		    dataIndex : 'versionDesc',
		    //width : 80,
  			flex:1,
		    renderer: function(value,metaData,record){
	            var instanceid = record.get ('sysId');
	            var desc= record.get ('versionDesc');
	            if(""==desc||null==desc){
	            	return '';
	            }else{
	            var func = "openWindowsOperDesc('getOperDesc.do'," + instanceid + ")";
	            return '<a href="javascript:' + func + ';">变更说明(详细)</a>';
	            }
		    }
		  },{
			    text : 'IPMP关联单号',
			    dataIndex : 'ipmporder',
			    width : 140,
				//flex:2,
			    hidden:!pfIPMPOrderNumberSwitch
		},{
		    text : '启动方式',
		    dataIndex : 'iipmpcachecode',
		    width : 120,
			//flex:2,
		    hidden:!pfIPMPCacheStateSwitch,
		    renderer : function (value, metaData, record, colIndex, store, view)
	        {
				if(value==1)
				{
					value='本地缓存启动';
				}else if(value==3)
				{
					value='IPMP平台启动';
				}else if(value==5)
				{
					 value='直接启动';
				}else{
					value='';
				}
				 return value;
	        }
		}];
  var pageBar = Ext.create('Ext.PagingToolbar', {
    store: releaseMonitorStore, 
    dock: 'bottom',
    baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    border : false,
    displayInfo: true
  });
  
  var checkModel = Ext.create('Ext.selection.CheckboxModel', {
      checkOnly : true
  });
  if(!descExportSwitch){
	  checkModel = null;
  }
  
  var releaseMonitorGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
	  cls:'customize_panel_back',
	  region : 'center',
	    extend : 'Ext.grid.Panel',
	    alias : 'widget.ideapanel',
	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//	    width : '100%',
//	    height : contentPanel.getHeight()-45,
	    store : releaseMonitorStore,
	    selModel : checkModel,/*Ext.create('Ext.selection.CheckboxModel', {
	      checkOnly : true
	    }),*/
	    padding : grid_margin,
	    columns : releaseMonitorColumns,
	    animCollapse : false//,
	    //bbar: pageBar
	  });
  
  var hisMainPanel = Ext.create('Ext.panel.Panel',{
    renderTo : "release_monitor_history_area",
    width : contentPanel.getWidth (),
    height : contentPanel.getHeight ()-2,
    border : false,
    //bodyCls: 'service_platform_bodybg',
    cls:'customize_panel_header_arrow',
    layout : 'border',
    items : [queryForm,releaseMonitorGrid]
  });

  
  contentPanel.on('resize', function() {
    hisMainPanel.setWidth (contentPanel.getWidth ());
    hisMainPanel.setHeight (contentPanel.getHeight ()-2);
  });
  
  $("body").off('keydown').on('keydown',function(event) {
	    if (event.keyCode == "13") { 
	    	queryWhere();
	    }
	});
  
  function setMessage(msg) {
    Ext.Msg.alert('提示', msg);
  }
  String.prototype.replaceAll = function(s1,s2){
		  return this.replace(new RegExp(s1,"gm"),s2);
		  }
  /* 解决IE下trim问题 */
  String.prototype.trim = function() {
    return this.replace(/(^\s*)|(\s*$)/g, "");
  };
  
  // 当页面即将离开的时候清理掉自身页面生成的组建
  contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
    Ext.destroy(releaseMonitorGrid);
    if (Ext.isIE) {
      CollectGarbage();
    }
  });
  
  function queryWhere(){
    pageBar.moveFirst();
  }
  
  function resetWhere(){
    queryForm.getForm().findField("sysName").setValue('');
    queryForm.getForm().findField("insName").setValue('');
    queryForm.getForm().findField("version").setValue('');
    queryForm.getForm().findField("startUser").setValue('');
    queryForm.getForm().findField("workOrderNum").setValue('');
    startTime.setValue('');
    endTime.setValue('');
    susTypeRadioGroup.items.get(0).setValue(true);
  }
  
  function exportWhere ()
  {
  	var hasParamsFlag=false;
  	var startTimeFlag = false;
  	var endTimeFlag = false;
      var sysName = queryForm.getForm ().findField ("sysName").getValue ();
      var insName = queryForm.getForm ().findField ("insName").getValue ();
      var version = queryForm.getForm ().findField ("version").getValue ();
      var startUser = queryForm.getForm ().findField ("startUser").getValue ();
      var startTimes = startTime.getRawValue ();
      var endTimes = endTime.getRawValue ();
      if (isSpecialCharVerificationN (sysName, "系统名称不能含有特殊字符!"))
	        return;
      if (isSpecialCharVerificationN (insName, "变更说明不能含有特殊字符!"))
	        return;
     if (isSpecialCharVerificationN (version, "变更单号不能含有特殊字符!"))
	        return;
      if(!pfFlag){
			if (isSpecialCharVerification(startUser, "启动用户不能含有特殊字符!"))
				return;
	  }
      if (!isDateFormat (startTimes))
	        return;
      if (!isDateFormat (endTimes))
	        return;
      
      if(checkIsNotEmpty(sysName))
  	{
  	hasParamsFlag=true;
  	}
      if(checkIsNotEmpty(insName))
  	{
  	hasParamsFlag=true;
  	}
      if(checkIsNotEmpty(version))
  	{
  	hasParamsFlag=true;
  	}
      if(checkIsNotEmpty(startUser)&&startUser!=null)
  	{
  	hasParamsFlag=true;
  	}
      if(checkIsNotEmpty(startTimes))
  	{
  	hasParamsFlag=true;
  	startTimeFlag = true;
  	}
      if(checkIsNotEmpty(endTimes))
  	{
  	hasParamsFlag=true;
  	endTimeFlag = true;
  	}
      
      if(!hasParamsFlag)
      	{
      	Ext.Msg.alert('信息提示', '请至少输入一个查询条件,才能导出！');
            return;
      	}
      if(!startTimeFlag){
      	Ext.Msg.alert('信息提示','请输入开始时间!');
      	return;
      }
      if(!endTimeFlag){
      	Ext.Msg.alert('信息提示','请输入结束时间!');
      	return;
      }
      if(startUser==null)
      {
    	  startUser='';
      }
      //变更类型，“全部”、“应用变更”和“数据变更”
	  var bgTypeValue = Ext.getCmp("bgTypeGroup").getChecked()[0].boxLabel;
      window.location.href = 'exportReleaseMonitor_Excel.do?statState=' + statState+
      '&sysName='+sysName+ '&insName='+insName+'&version='+
      version+'&startUser='+startUser+ '&startTime='+startTimes+
      '&endTime='+endTimes+'&isHistory=1' + '&bgType=' + bgTypeValue;
  }
  
  //详细导出
  function descExportWhere ()
  {
	  var records = releaseMonitorGrid.getSelectionModel().getSelection();
	  if(records.length==0){
			Ext.Msg.show({
			     title:'提示',
			     msg: '请选择要导出的记录',
			     buttons: Ext.Msg.OK,
			     icon: Ext.Msg.INFO 
			});
			return;
	  }
	  var jsonData = "";
	  for ( var i = 0, len = records.length; i < len; i++) {      
	      var ss = Ext.JSON.encode(records[i].data.iid);
	      if (i == 0)
	        jsonData = jsonData + ss;
	      else
	        jsonData = jsonData + "," + ss;
	  }
      window.location.href = 'exportReleaseMonitorDescExcel.do?jsonData='+jsonData;
  }
  
 // alert(sysNameReturn+","+insNameReturn+","+versionReturn+","+workOrderNumReturn+","+startUserReturn+","+startTimeQueryReturn+","+endTimeQueryReturn+","+startReturn+","+pageSizeReturn);
  if("null"==sysNameReturn){
	  queryForm.getForm().findField("sysName").setValue('');
  }else{
	  queryForm.getForm().findField("sysName").setValue(sysNameReturn);
  }
  
  if("null"==insNameReturn){
	  queryForm.getForm().findField("insName").setValue('');
  }else{
	  queryForm.getForm().findField("insName").setValue(insNameReturn);
  }
  
  if("null"==versionReturn){
	  queryForm.getForm().findField("version").setValue('');
  }else{
	  queryForm.getForm().findField("version").setValue(versionReturn);
  }
  
  if("null"==workOrderNumReturn){
	  queryForm.getForm().findField("workOrderNum").setValue('');
  }else{
	  queryForm.getForm().findField("workOrderNum").setValue(workOrderNumReturn);
  }
  
  if("null"==startUserReturn){
	  queryForm.getForm().findField("startUser").setValue('');
  }else{
	  queryForm.getForm().findField("startUser").setValue(startUserReturn);
  }
  
  console.log("---->> 当前返回页数为："+currentPageReturn);
  //页面加载后 如果是从子页面返回的，存在返回的页数，跳到指定页
  if(typeof currentPageReturn === 'number'){
		releaseMonitorStore.start=startReturn;
	  	releaseMonitorStore.currentPage=currentPageReturn;
	  	releaseMonitorStore.load();
	  	console.log("---->> 已执行了load操作,数据加载完毕");
  }else{	  
	  queryWhere();
  }
//  finalQueryPanelObj=queryForm;
//  finalTitlePanelObj=contentPanel;
  
//  finalTitle=contentPanel.title;
//  contentPanel.setTitle(getTabForOverrideTitle(true));
});

function forwardRMFlow(insId,insName){
	destroyRubbish();
	contentPanel.setTitle(finalTitle);
	
	var targetFlag = "";
	if(pfTypeSwitch){
		targetFlag = targetComboBox.getValue() ;
	}
	var sysName = queryForm.getForm().findField("sysName").getValue();
	var insName1 = queryForm.getForm().findField("insName").getValue();
	var version = queryForm.getForm().findField("version").getValue();
	var startUser = queryForm.getForm().findField("startUser").getValue();
	var _workOrderNum=queryForm.getForm().findField("workOrderNum").getValue();
	var startTimeQuery=queryForm.getForm().findField("startTime").getRawValue();
	var endTimeQuery=queryForm.getForm().findField("endTime").getRawValue();
	var currentPage=releaseMonitorStore.currentPage;
	var start=releaseMonitorStore.lastOptions.start;
	var pageSize=releaseMonitorStore.pageSize;
	if (isSpecialCharVerificationN(sysName, "系统名称不能含有特殊字符!"))
		return;
	if (isSpecialCharVerificationN(insName1, "变更说明不能含有特殊字符!"))
		return;
		if (isSpecialCharVerificationN(version, "变更单号不能含有特殊字符!"))
		return;
	if (!isDateFormat(startTimeQuery))
		return;
	if (!isDateFormat(endTimeQuery))
		return;
	
	if(!pfFlag){
		if (isSpecialCharVerification(startUser, "启动用户不能含有特殊字符!"))
			return;
	}
	var pageBackType = 3;
	contentPanel.getLoader().load (
			{
			    url : 'releaseMonitorHistoryFlowGroup.do',
			    params :
			    {
			    	insId : insId,
			    	
			    	sysName : sysName,
			    	insName:insName1,
					insNameNew:insName,
			    	version : version,
			    	workOrderNum:_workOrderNum,
			    	startUser : startUser,
			    	startTimeQuery:startTimeQuery,
			    	endTimeQuery:endTimeQuery,
			    	
			    	pageBackType : pageBackType,
			    	checkFlowFlag : false,
			    	checkHisFlag : false,
					targetFlag:targetFlag,
					isHistory : 1,
					currentPage:currentPage,
					start:start,
					limit:pageSizeReturn,
					pageSize:pageSize
			    },
			    scripts : true
			});
  if(Ext.isIE){
      CollectGarbage(); 
  }
}

//跳转到上线历史>补丁包展示 页面
function forwardPackgeList(iid,sysName, version, startTime, endTime, startUser,
		state) {
	destroyRubbish();
	contentPanel.setTitle(finalTitle);
	
	var targetFlag = "";
	if(pfTypeSwitch){
		targetFlag = targetComboBox.getValue() ;
	}
	var sysName1=queryForm.getForm().findField("sysName").getValue();
	var insName1 = queryForm.getForm().findField("insName").getValue();
	var version1 = queryForm.getForm().findField("version").getValue();
	var startUser1 = queryForm.getForm().findField("startUser").getValue();
	var _workOrderNum=queryForm.getForm().findField("workOrderNum").getValue();
	var startTimeQuery=queryForm.getForm().findField("startTime").getRawValue();
	var endTimeQuery=queryForm.getForm().findField("endTime").getRawValue();
	var currentPage=releaseMonitorStore.currentPage;
	var start=releaseMonitorStore.lastOptions.start;
	var pageSize=releaseMonitorStore.pageSize;
	if (isSpecialCharVerificationN(sysName1, "系统名称不能含有特殊字符!"))
		return;
	if (isSpecialCharVerificationN(insName1, "变更说明不能含有特殊字符!"))
		return;
	/*if (isSpecialCharVerificationN(version, "变更单号不能含有特殊字符!"))
	return;*/
	if(!pfFlag){
		if (isSpecialCharVerification(startUser, "启动用户不能含有特殊字符!"))
			return;
	}
	
	contentPanel.getLoader().load({
		url : 'releaseMonitorFlowGroupHis.do',
		params : {
			iid :iid,
			
			sysName : sysName1,
			insName:insName1,
			version : version1,
			workOrderNum:_workOrderNum,
			startUser : startUser1,
			startTimeQuery:startTimeQuery,
			endTimeQuery:endTimeQuery,
			
			startTime : startTime,
			endTime : endTime,
			status : state,
			checkHisFlag : false,
			targetFlag:targetFlag,
			isHistory : 1,
			currentPage:currentPage,
			start:start,
			limit:pageSizeReturn,
			pageSize:pageSize
		},
		scripts : true
	});
	if (Ext.isIE) {
		CollectGarbage();
	}
}


