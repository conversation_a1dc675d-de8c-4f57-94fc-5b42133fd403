package com.ideal.ieai.server.platform.repository.computerManage.shell;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.log4j.Logger;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.SqlBean;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.db.DBSourceMonitor;
import com.ideal.ieai.server.repository.project.ProjectManagerForMultiple;

public class Tools
{
    private static Logger log = Logger.getLogger(Tools.class);
    public static Map<String, Object> getConnectionInfo() throws RepositoryException, DBException {
        Map<String, Object> res = new HashMap<String, Object>();
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance().getDBsourceList(Constants.IEAI_IEAI_BASIC);
        List<Connection> dbConns = new ArrayList<Connection>();
        Connection baseConn = null; // 基线数据源
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            Connection connection = DBManager.getInstance().getJdbcConnection((int) dBSourceMonitor.getGroupId());
            if (dBSourceMonitor.getBasic() == 1)
            {
                // 保存基线库数据库连接至工具类对象
                baseConn = connection;
            }
            dbConns.add(connection);
        }
        res.put("baseConn", baseConn);
        res.put("dbConns", dbConns);
        return res;
        
    }
    
    public static Connection  getBaseConnectionInfo() throws RepositoryException, DBException {
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance().getDBsourceList(Constants.IEAI_IEAI_BASIC);
        Connection baseConn = null; // 基线数据源
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            Connection connection = DBManager.getInstance().getJdbcConnection((int) dBSourceMonitor.getGroupId());
            if (dBSourceMonitor.getBasic() == 1)
            {
                baseConn= connection;
                break;
            }
            else
            {
                DBResource.closeConnection(connection, "getBaseConnectionInfo", log);
            }
        }
        return baseConn;
    }

    /**
     * 
     * @Title: getConnectionInfoForLinkedMap
     * @Description: TODO(以linkedmap类型获取所有数据源)
     * @return
     * @throws RepositoryException
     * @throws DBException
     * @return Map<String,Object> 返回类型
     * @throws
     */
    public static Map<String, Object> getConnectionInfoForLinkedMap () throws RepositoryException, DBException
    {
        Map<String, Object> res = new HashMap<String, Object>();
        List<DBSourceMonitor> dbList = ProjectManagerForMultiple.getInstance().getDBsourceList(Constants.IEAI_IEAI_BASIC);
        Map<String, Connection> connectionMap = new LinkedHashMap<String, Connection>(); // 数据库连接集合
        Connection baseConn = null; // 基线数据源
        for (DBSourceMonitor dBSourceMonitor : dbList)
        {
            Connection connection = DBManager.getInstance().getJdbcConnection((int) dBSourceMonitor.getGroupId());
            if (dBSourceMonitor.getBasic() == 1)
            {
                // 保存基线库数据库连接至工具类对象
                baseConn = connection;
            }
            // 保存数据库连接集合至工具类对象
            connectionMap.put(dBSourceMonitor.getDbsourceName(), connection);
        }
        res.put("baseConn", baseConn);
        res.put("dbConns", connectionMap);
        return res;

    }

    
    public static String ARRAY_NAME = "Array"; // 资源组与服务器ID节点名
    /***
     * 
     * @Title: buildiexecuserServerArray   
     * @Description:组织服务器的执行用户
     * @param objectElement
     * @param objMap      
     * @author: yunpeng_zhang 
     * @date:   2020年1月2日 下午8:17:37
     */
    public static void buildiexecuserServerArray(Element objectElement,Map<String, Object> objMap)
    {
        List<Element> objectElementLists =objectElement.elements();
        for (Element objectElementSon : objectElementLists)
        {
            String name = objectElementSon.getName();// 属性名称
            // 如果是Array节点，则继续解析
            if (ARRAY_NAME.equals(name))
            {
                List<Attribute> arrsAttrT = objectElementSon.attributes();
                String listName=null;
                for (Attribute sArr : arrsAttrT)
                {
                    listName = sArr.getValue();
                }
                List<Map<String, Object>> iexecuserServerArray=new ArrayList<Map<String, Object>>();
                List<Element> objectElementSonLists =objectElementSon.elements();
                for(Element elementT:objectElementSonLists)
                {
                    Map<String,Object> mapT=new HashMap<String, Object>();
                    List<Attribute> objListAttr = elementT.attributes();// 当前节点的所有属性的list
                    for (Attribute objAttr : objListAttr)
                    {
                        // 遍历当前节点的所有属性
                        mapT.put( objAttr.getName(), objAttr.getValue());
                    }
                    iexecuserServerArray.add(mapT);
                   
                }
                objMap.put(listName,iexecuserServerArray);
            }
        }
    }
    
    /**
     * 
     * @Title: MxgraphXMLStringToMap
     * @Description: TODO(解析mxgraph的xml字符串)
     * @param xmlStr
     * @return
     * @throws Exception
     * @return List<Map<String,String>> 返回类型
     * @throws
     */
    public static List<Map<String, Object>> MxgraphXMLStringToMap ( String xmlStr ) throws Exception
    {
        String arr_val_sp = "scriptParams";
        String arr_val_gs = "groupAndServer";
        String arrType = "";
        List<Map<String, Object>> mxCellList = new ArrayList<Map<String, Object>>();
        Document document = DocumentHelper.parseText(xmlStr);
        Element mxGraphModelElement = document.getRootElement();// <mxGraphModel>
        List<Element> rootElements = mxGraphModelElement.elements();// <root>
        List<Element> cellElements = rootElements.get(0).elements(); // 所有<mxCell>
        for (Element cellElement : cellElements)
        {
            List<Attribute> listAttr = cellElement.attributes();// 当前节点的所有属性的list
            Map<String, Object> mxCellMap = new HashMap<String, Object>();
            for (Attribute attr : listAttr)
            {// 遍历当前节点的所有属性
                String name = attr.getName();// 属性名称
                String value = attr.getValue();// 属性的值
                mxCellMap.put(name, value);
            }
            List<Element> cellSonElements = cellElement.elements();
            if (cellSonElements != null && cellSonElements.size() > 0)
            {
                for (Element cellSonElement : cellSonElements)
                {
                    // 如果是Array节点，则继续解析
                    if (ARRAY_NAME.equals(cellSonElement.getName()))
                    {
                        List<Attribute> arrsAttr = cellSonElement.attributes();
                        for (Attribute sArr : arrsAttr)
                        {
                            String sName = sArr.getName();
                            String sVal = sArr.getValue();
                            arrType = sVal;
                        }
                        // System.out.println(cellSonElement.);
                        List<Element> objectElements = cellSonElement.elements();
                        if (objectElements != null && objectElements.size() > 0)
                        {
                            List objMapList = new ArrayList();
                            for (Element objectElement : objectElements)
                            {
                                List<Attribute> objListAttr = objectElement.attributes();// 当前节点的所有属性的list
                                Map<String, Object> objMap = new HashMap<String, Object>();
                                for (Attribute objAttr : objListAttr)
                                {// 遍历当前节点的所有属性
                                    String name = objAttr.getName();// 属性名称
                                    String value = objAttr.getValue();// 属性的值
                                    objMap.put(name, value);
                                }
                              //组织服务器执行用户的数据
                                Tools.buildiexecuserServerArray(objectElement, objMap);
                                objMapList.add(objMap);
                            }
                            mxCellMap.put(arrType, objMapList);
                        }
                    }

                }
            }
            mxCellList.add(mxCellMap);
        }

        return mxCellList;
    }

    /**
     * 
     * @Title: getRandomFileName
     * @Description: TODO(获取日期时间+5位随机数的串)
     * @return
     * @return String 返回类型
     * @throws
     * @变更记录 2017年2月18日
     */
    public static String getRandomString ()
    {

        SimpleDateFormat simpleDateFormat;

        simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

        Date date = new Date();

        String str = simpleDateFormat.format(date);

        Random random = new Random();

        int rannum = (int) (random.nextDouble() * (99999 - 10000 + 1)) + 10000;// 获取5位随机数

        return str + "_" + rannum;// 当前时间
    }
    /**
     * 
     * @Title: getSqlBeanList
     * @Description: TODO(组织sqlBean对象的List)
     * @param insertSql insert语句模板
     * @param querySql query语句模板
     * @param tList
     * @return
     * @throws IllegalArgumentException
     * @throws IntrospectionException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     * @return List<SqlBean> 返回类型
     * @throws
     */
    public static <T> List<SqlBean> getSqlBeanList ( String insertSql, String querySql, List<T> tList, boolean flag ) throws IllegalArgumentException,
            IntrospectionException, IllegalAccessException, InvocationTargetException
    {
        List<SqlBean> sqlBeanList = new ArrayList<SqlBean>();
        for (T t : tList)
        {
            SqlBean sqlBean = new SqlBean();
            sqlBean.setInsertSql(Tools.renderString(insertSql, t));
            sqlBean.setQuerySql(Tools.renderString(querySql, t));
            if (flag)
            {
                sqlBean.setDataObj(t);
            }
            sqlBeanList.add(sqlBean);
        }
        return sqlBeanList;
    }
    /**
     * 
     * @Title: renderString
     * @Description: TODO(根据键值对填充字符串，如("hello ${name}",{name:"xiaoming"}))
     * @param content 模板字符串
     * @param t 传入解析的对象
     * @return
     * @throws IntrospectionException
     * @throws IllegalArgumentException
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     * @return String 返回类型
     * @throws
     */
    public static <T> String renderString ( String content, T t ) throws IntrospectionException, IllegalArgumentException, IllegalAccessException,
            InvocationTargetException
    {
        Field[] fields = t.getClass().getDeclaredFields();
        for (short i = 0; i < fields.length; i++)
        {
            Field field = fields[i];
            String fieldName = field.getName();
            Class fieldType = field.getType();
            PropertyDescriptor pd = new PropertyDescriptor(fieldName, t.getClass());
            // 获得get方法
            Method getMethod = pd.getReadMethod();
            // 模板替换规则正则表达式
            String regex = "\\$\\{" + fieldName + "\\}";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(content);
            Object val = getMethod.invoke(t, new Object[] {});
            // 如果变量为数字型的，直接替换模板内容
            if (fieldType == long.class || fieldType == Long.class || fieldType == int.class || fieldType == Integer.class)
            {
                content = matcher.replaceAll(val.toString());

            } else if (fieldType == String.class)
            {
                // 如果变量为字符串型的，则增加单引号进行替换
                if (val != null)
                {
                    content = matcher.replaceAll("'" + val.toString() + "'");
                } else
                {
                    content = matcher.replaceAll("NULL");
                }

            }

        }
        return content;
    }
    
    /**
     * 
     * @Title: calculatePlaces
     * @Description: 计算字符串位数
     * @param str
     * @return
     * @return int 返回类型
     * @throws
     */
    public static int calculatePlaces ( String str )
    {
        int m = 0;
        char arr[] = str.toCharArray();
        for (int i = 0; i < arr.length; i++)
        {
            char c = arr[i];
            if ((c >= 0x0391 && c <= 0xFFE5)) // 中文字符
            {
                m = m + 2;
            } else if ((c >= 0x0000 && c <= 0x00FF)) // 英文字符
            {
                m = m + 1;
            }
        }
        return m;
    }
    /**
     * 　　* 判断时间是否在时间段内 *
     * 　　* @param date
     * 　　* 当前时间 yyyy-MM-dd HH:mm:ss
     * 　　* @param strDateBegin
     * 　　* 开始时间 00:00
     * 　　* @param strDateEnd
     * 　　* 结束时间 00:05
     * 　　* @return
     */
    public static boolean isInDate ( Date date, String strDateBegin, String strDateEnd )
    {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String strDate = sdf.format(date);

        // 截取当前时间时分秒

        int strDateH = Integer.parseInt(strDate.substring(11, 13));

        int strDateM = Integer.parseInt(strDate.substring(14, 16));

//        int strDateS = Integer.parseInt(strDate.substring(17, 19));

        // 截取开始时间时分秒

        int strDateBeginH = Integer.parseInt(strDateBegin.substring(0, 2));

        int strDateBeginM = Integer.parseInt(strDateBegin.substring(3, 5));

//        int strDateBeginS = Integer.parseInt(strDateBegin.substring(6, 8));

        // 截取结束时间时分秒

        int strDateEndH = Integer.parseInt(strDateEnd.substring(0, 2));

        int strDateEndM = Integer.parseInt(strDateEnd.substring(3, 5));

//        int strDateEndS = Integer.parseInt(strDateEnd.substring(6, 8));
        if((strDateBeginH*60+strDateBeginM)>(strDateEndH*60+strDateEndM))
        {
            strDateEndH+=24;
        }
        if(((strDateH*60+strDateM)>(strDateBeginH*60+strDateBeginM))
                &&
                ((strDateH*60+strDateM)<(strDateEndH*60+strDateEndM))
                )
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    
}
