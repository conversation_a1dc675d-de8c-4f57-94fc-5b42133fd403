Ext.onReady(function() {

    Ext.tip.QuickTipManager.init();

    Ext.define('model', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IID',
            type: 'long'
        },{
            name: 'NAME',
            type: 'string'
        },{
            name: 'BUSINESSNAME',
            type: 'string'
        },{
            name: 'SCRIPT_TYPE',
            type: 'string'
        },{
            name: 'DBTYPE',
            type: 'string'
        },{
            name: 'CREATE_USER',
            type: 'string'
        },{
            name: 'CREATE_TIME',
            type: 'string'
        },{
            name: 'AUDIT_USER',
            type: 'string'
        },{
            name: 'STATUS',
            type: 'string'
        }]
    });

    var store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'model',
        proxy: {
            type: 'ajax',
            url: 'dbaasObjectCenterSqlAnalyGetData1.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    var columns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 65,
        resizable: true
    },{
        text: '主键',
        dataIndex: 'IID',
        width: 40,
        hidden: true
    },{
        text: '说明',
        dataIndex: 'NAME',
        width: 200,
        flex:1,
        editor: {
            allowBlank: true
        }
    },{
        text: '业务系统',
        dataIndex: 'BUSINESSNAME',
        width: 100,
        flex:1
    },{
        text: '脚本类型',
        dataIndex: 'SCRIPT_TYPE',
        width: 100,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "SQL",
                    value: "1"
                },{
                    name: "DDL",
                    value: "2"
                }]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var type = record.get("SCRIPT_TYPE");
            if(type == "1"){
                return "SQL";
            }else if(type == "2"){
                return "DDL";
            }else {
                return type;
            }
        }
    },{
        text: '数据库类型',
        dataIndex: 'DBTYPE',
        width: 100,
        editor: new Ext.form.field.ComboBox({
            triggerAction: 'all',
            editable: false,
            queryMode: 'local',
            emptyText: "--请选择--",
            displayField: 'name',
            valueField: 'value',
            store: Ext.create('Ext.data.Store', {
                fields: ['name','value'],
                data: [{
                    name: "ORACLE",
                    value: "1"
                },{
                    name: "DB2",
                    value: "2"
                },{
                    name: "MYSQL",
                    value: "3"
                }]
            })
        }),
        renderer: function(value, p, record, rowIndex) {
            var type = record.get("DBTYPE");
            if(type == "1"){
                return "ORACLE";
            }else if(type == "2"){
                return "DB2";
            }else if(type == "3"){
                return "MYSQL";
            }else {
                return type;
            }
        }
    },{
        text: '创建人',
        dataIndex: 'CREATE_USER',
        width: 100
    },{
        text: '创建时间',
        dataIndex: 'CREATE_TIME',
        width: 200
    },{
        text: '审核人',
        dataIndex: 'AUDIT_USER',
        width: 100
    },{
        text: '状态',
        dataIndex: 'STATUS',
        width: 100,
        renderer: function(value, p, record, rowIndex) {
            var status = record.get("STATUS");
            if(status == 0){
                return "待编辑";
            }else if(status == 1){
                return "待审核";
            }else if(status == 2){
                return "待执行";
            }else if(status == 3){
                return "已执行";
            }else if(status == 4){
                return "被打回";
            }else if(status == 5){
                return "运行中";
            }else{
                return status;
            }
        }
    },{
        text: '操作',
        width:120,
        renderer: function(value, p, record, rowIndex) {
            var status = record.get("STATUS");
            if(status == 1){
                return '<a href="javascript:void(0)" onclick="toAuditExec(0)">审核</a>';
            }
        }
    }];

    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: store,
        dock: 'bottom',
        baseCls:Ext.baseCSSPrefix + ' toolbar customize_toolbar',
        displayInfo: true,
        border:false,
        emptyMsg: '找不到任何记录'
    });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 1
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });

    var text= Ext.create("Ext.form.field.Text", {
        fieldLabel: '输入查询条件',
        labelWidth: 100,
        labelAlign: 'right',
        width:200
    });

    var form = Ext.create('Ext.form.Panel', {
        border : false,
        region : 'north',
        bodyCls : 'x-docked-noborder-top',
        baseCls: 'customize_gray_back',
        dockedItems : [{
            xtype : 'toolbar',
            baseCls: 'customize_gray_back',
            dock : 'top',
            border : false,
            items : [text, {
                xtype: 'button',
                cls: 'Common_Btn',
                text: '查询',
                handler: QueryMessage
            },{
                xtype: 'button',
                cls: 'Common_Btn',
                text: '清空',
                handler: QueryMessage
            }]
        }]
    });

    var grid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        store: store,
        selModel: selModel,
        plugins: [cellEditing],
        padding : panel_margin,
        border:true,
        bbar: pageBar,
        columnLines: true,
        columns: columns,
        cls: 'customize_panel_back'
    });

    grid.getSelectionModel().on('selectionchange', function(selModel, selections) {
        form.down('#delete').setDisabled(selections.length === 0);
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "dbaasObjectCenterSqlAnaly_WaitConfirm",
        layout: 'border',
        width : contentPanel.getWidth(),
        height :contentPanel.getHeight() - modelHeigth,
        bodyPadding : grid_margin,
        border : true,
        bodyCls:'service_platform_bodybg',
        items: [form,grid]
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function() {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function() {
        mainPanel.setHeight (contentPanel.getHeight () - modelHeigth);
        mainPanel.setWidth (contentPanel.getWidth () );
    });

    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload",
        function(obj, options, eOpts) {
            Ext.destroy(mainPanel);
            if (Ext.isIE) {
                CollectGarbage();
            }
        });

    function QueryMessage() {
        if (Ext.isIE) {
            CollectGarbage();
        }
        pageBar.moveFirst();
    }
})

function toAuditExec(type){
    contentPanel.getLoader().load({
        url: 'dbaasObjectCenterSqlAnalyAuditExec0.do',
        params : {type:type},
        scripts: true
    });
}
