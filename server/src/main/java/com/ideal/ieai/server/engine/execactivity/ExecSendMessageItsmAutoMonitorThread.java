package com.ideal.ieai.server.engine.execactivity;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.SystemConfig;
import com.ideal.ieai.server.repository.engine.DbOpScheduler;

public class ExecSendMessageItsmAutoMonitorThread extends Thread
{
    DbOpScheduler                               dbOpScheduler = null;
    private static final Logger                 _log          = Logger.getLogger(ExecSendMessageItsmAutoMonitorThread.class);
    private static final ExecSendMessageItsmAutoMonitorThread _instance     = new ExecSendMessageItsmAutoMonitorThread();

    static final public ExecSendMessageItsmAutoMonitorThread getInstance ()
    {
        return _instance;
    }

    public ExecSendMessageItsmAutoMonitorThread()
    {
        super("itsm集成轮询线程");
    }

    public void run ()
    {
        dbOpScheduler = new DbOpScheduler();
        while (true)
        {
            try
            {
                dbOpScheduler.itsmAutoSendFinishedMonitor();
            } catch (Throwable re)
            {
                _log.info("ExecSendMessageItsmAutoMonitorThread run error!", re);
            }
            try
            {
                // 延时器轮询时间为 轮询周期 的5倍
                Thread.sleep((SystemConfig.getSleepTime() * 5));
            } catch (Throwable e)
            {
                _log.info("ExecSendMessageItsmAutoMonitorThread sleep error!", e);
            }
        }
    }
}
