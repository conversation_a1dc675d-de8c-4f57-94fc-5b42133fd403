		--4.7.17 not exists	
		--4.7.18
		
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARNADDRESS_SYSUSER';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_WARNADDRESS_SYSUSER  (USERFLAG	VARCHAR(255),PHONE   	VARCHAR(255))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
				
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARNADDRESS';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_WARNADDRESS  (IID   DECIMAL(19,0) NOT NULL,ISYSTEMUSER	VARCHAR(255),<PERSON><PERSON>P<PERSON><PERSON><PERSON>  VARCHAR(255),IPROJECT  VARCHAR(255) )';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
				
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ACTFINISHED_FLAG_NEW' AND  COLNAME='IDATADATE';
				IF	LI_EXISTS = 1 THEN
				SET	LS_SQL = 'ALTER TABLE  IEAI_ACTFINISHED_FLAG_NEW alter column IDATADATE set data type VARCHAR(255)';
					   PREPARE	SQLA FROM LS_SQL; 
					   EXECUTE SQLA;
				END	IF;
			
		END
			 $
		 
		 
		 
			CREATE OR REPLACE PROCEDURE PROC_GET_FIRST_PAGE_HOME_JZ(IN AN_USERID NUMERIC(19,0), OUT AN_OPT_ID NUMERIC(19,0), OUT AI_REC_COUNT INT, IN AI_SYSTEM VARCHAR(255), IN AI_STATE VARCHAR(255), IN AI_DATADATE VARCHAR(255), IN AI_CHILDPRO VARCHAR(255), IN AI_ACTNAME VARCHAR(255), IN AI_SIHIDD SMALLINT,IN AI_KEYFLOW SMALLINT, IN AI_PAGE_ID SMALLINT, IN AI_NUM_OF_PAGE SMALLINT, IN AI_ACTTYPE VARCHAR(50), IN AI_DATADATE2 VARCHAR(255))
		BEGIN
			DECLARE LN_OPT_ID NUMERIC(19,0);   
			DECLARE LV_SQL    VARCHAR(6000);  
			DECLARE LV_WHERE  VARCHAR(4000);  
			DECLARE LV_ORDER   VARCHAR(4000); 
			DECLARE LV_ORDERCASE VARCHAR(4000); 
			DECLARE LV_IEAI_WORKFLOWINSTANCE     VARCHAR(4000);

			DECLARE LI_PAGE_ID  INTEGER;    
			DECLARE LI_MAX_RECID  INTEGER;    
			DECLARE LI_MIN_RECID  INTEGER;    
			DECLARE LI_PAGE_COUNT  INTEGER;    

			
			CALL	PROC_GET_NEXT_ID ('TMP_USER_VALIDATE', LN_OPT_ID);
			SET	AN_OPT_ID = LN_OPT_ID;

			
			INSERT  INTO  TMP_USER_VALIDATE
			  (
				OPT_ID,
				PRJ_ID,
				PRJ_NAME,
				PERMIT_ID,
				PERMIT_NAME
			  )
			SELECT  LN_OPT_ID,
			  PRJ_ID,
			  PRJ_NAME,
			  0,
			  ''
			FROM  (
				SELECT DISTINCT IID PRJ_ID,IPRJ.INAME PRJ_NAME,IUPPERID,IPRJ.ILATESTID 
				FROM IEAI_PROJECT IPRJ,
				(
				 SELECT PRJ.INAME,IPROID 
				 FROM IEAI_USERINHERIT UR,IEAI_ROLE R,IEAI_SYS_PERMISSION SP ,IEAI_PROJECT PRJ 
				 WHERE UR.IROLEID=R.IID        AND R.IID=SP.IROLEID        AND UR.IUSERID=AN_USERID 
				 AND SP.IPERMISSION=1       AND SP.IPROID=PRJ.IID 
				 AND (PRJ.PROTYPE=1 OR PRJ.PROTYPE=-1)  
				) QX 
				 WHERE IPRJ.IPKGCONTENTID<>0  AND IPRJ.ILATESTID=IPRJ.IID 
				 AND (QX.INAME=IPRJ.INAME OR -QX.IPROID=IPRJ.PROTYPE) ORDER BY IPRJ.INAME
			  ) TBL_A;

			  
			 --SET LV_IEAI_WORKFLOWINSTANCE='(SELECT CC.*,DD.ISTATUS AS IKEY_FLOW  FROM IEAI_WORKFLOWINSTANCE CC LEFT JOIN    (SELECT DISTINCT B.INAME, A.IFLOWNAME, A.ISTATUS FROM IEAI_WORKFLOW A, IEAI_PROJECT B WHERE B.ILATESTID = A.IPRJID  AND B.ILATESTID = B.IID ORDER BY A.IFLOWNAME) DD  ON CC.IPROJECTNAME =DD.INAME AND CC.IFLOWNAME =DD.IFLOWNAME) ';
			SET LV_IEAI_WORKFLOWINSTANCE='(SELECT CC.*  FROM IEAI_WORKFLOWINSTANCE CC ) ';
					 
			 SET LV_WHERE = '';
			 SET LV_WHERE = LV_WHERE  || ' AND C.IACTNAME NOT LIKE ('|| CHR(39)||'%_CHECK%'||CHR(39)||')';
			 SET LV_WHERE = LV_WHERE  || ' AND C.IACTDEFNAMEN <> ('|| CHR(39)||'Delayer'||CHR(39)||')';
			 SET LV_WHERE = LV_WHERE  || ' AND (C.ISTATE IN ('|| CHR(39)||'Disable'||CHR(39)||','|| CHR(39)||'HangUp'||CHR(39)||') OR C.IACTTYPE <> ('|| CHR(39)||'CallFlow'||CHR(39)||'))';
			 IF  LENGTH(AI_SYSTEM) > 0  THEN
			  IF  INSTR(AI_SYSTEM,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND B.ISYSTEM LIKE (' || CHR(39)  || AI_SYSTEM  || CHR(39) || ')';
			  ELSE
				SET LV_WHERE = LV_WHERE || ' AND B.ISYSTEM = (' || CHR(39)  || AI_SYSTEM  || CHR(39) || ')';
			  END IF;
			END  IF;

			IF AI_KEYFLOW =1 THEN
			  -- SET LV_WHERE  =LV_WHERE  || ' AND IKEY_FLOW=1';
			END IF;
			
			IF  LENGTH(AI_STATE) > 0  THEN
			  IF 'Fail'= AI_STATE THEN
				  SET LV_WHERE = LV_WHERE || ' AND( C.ISTATE = (' || CHR(39)  || 'Fail'  || CHR(39) || ') OR' ||' C.ISTATE = (' || CHR(39)  || 'Fail:Business'  || CHR(39) || ') OR' ||' C.ISTATE = (' || CHR(39)  || 'Ready'  || CHR(39) || '))';
			  ELSE
				  SET LV_WHERE = LV_WHERE || ' AND C.ISTATE = (' || CHR(39)  || AI_STATE  || CHR(39) || ')';
			  END IF;
			END  IF;

			IF  LENGTH(AI_DATADATE) > 0  THEN
				IF  LENGTH(AI_DATADATE2) > 0  THEN
				SET LV_WHERE = LV_WHERE || ' AND (B.IFLOWINSNAME LIKE (' || CHR(39)  ||'%'|| AI_DATADATE ||'%' || CHR(39) || ') OR B.IFLOWINSNAME LIKE (' || CHR(39)  ||'%'|| AI_DATADATE2 ||'%' || CHR(39) || ') )';
				ELSE
				SET LV_WHERE = LV_WHERE || ' AND B.IFLOWINSNAME LIKE (' || CHR(39)  ||'%'|| AI_DATADATE ||'%' || CHR(39) || ')';
				END  IF;
			END  IF;

			IF  LENGTH(AI_CHILDPRO) > 0  THEN
			 IF  INSTR(AI_CHILDPRO,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND A.PRJ_NAME LIKE (' || CHR(39)  || AI_CHILDPRO  || CHR(39) || ')';
			 ELSE
				SET LV_WHERE = LV_WHERE || ' AND A.PRJ_NAME = (' || CHR(39)  || AI_CHILDPRO  || CHR(39) || ')';
			 END IF;
			END  IF;

			IF  LENGTH(AI_ACTNAME) > 0  THEN
			 IF  INSTR(AI_ACTNAME,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND C.IACTNAME LIKE ('  || CHR(39) || AI_ACTNAME || CHR(39) || ')';
			 ELSE
				SET LV_WHERE = LV_WHERE || ' AND C.IACTNAME = (' || CHR(39) || AI_ACTNAME || CHR(39) || ')';
			 END IF;
			END  IF;

			--SET LV_SQL = 'INSERT INTO TMP_FIRST_PAGE_STATE_NUM(ISTATE,ACTNUM,IACTNAME) SELECT C.ISTATE,COUNT(C.ISTATE),C.IACTNAME FROM TMP_USER_VALIDATE A, '||LV_IEAI_WORKFLOWINSTANCE||' B, ((SELECT CC.*,nvl(cC.IACTDEFNAME,'||CHR(39)||'1'||CHR(39)||') IACTDEFNAMEN FROM IEAI_ACTRUNTIME CC left join IEAI_REMOTEEXECACT T  on CC.IREXECREQUESTID=T.IID)) C WHERE  A.OPT_ID = '||LN_OPT_ID ||'  ' || LV_WHERE || ' and B.IPROJECTNAME = A.PRJ_NAME AND B.ISTATUS IN (0,8,40,46,47) AND  C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0 GROUP BY C.ISTATE,C.IACTNAME';
			--PREPARE	SQLA FROM LV_SQL;
			--EXECUTE SQLA;
			
			
			SET LV_WHERE = '';
			IF  LENGTH(AI_SYSTEM) > 0  THEN
			 IF  INSTR(AI_SYSTEM,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND B.ISYSTEM LIKE (' || CHR(39)  || AI_SYSTEM  || CHR(39) || ')';
			 ELSE
				SET LV_WHERE = LV_WHERE || ' AND B.ISYSTEM = (' || CHR(39)  || AI_SYSTEM  || CHR(39) || ')';
			 END IF;
			END  IF;

			
			IF  LENGTH(AI_STATE) > 0  THEN
			  IF 'Fail'= AI_STATE THEN
				   SET LV_WHERE = LV_WHERE || ' AND( C.ISTATE = (' || CHR(39)  || 'Fail'  || CHR(39) || ') OR' ||' C.ISTATE = (' || CHR(39)  || 'Fail:Business'  || CHR(39) || ') OR' ||' C.ISTATE = (' || CHR(39)  || 'Ready'  || CHR(39) || '))';
			  ELSE
				   SET LV_WHERE = LV_WHERE || ' AND C.ISTATE = (' || CHR(39)  || AI_STATE  || CHR(39) || ')';
			  END IF;
			END  IF;

			IF  LENGTH(AI_DATADATE) > 0  THEN
			IF  LENGTH(AI_DATADATE2) > 0  THEN
				SET LV_WHERE = LV_WHERE || ' AND (B.IFLOWINSNAME LIKE (' || CHR(39)  ||'%'|| AI_DATADATE ||'%' || CHR(39) || ') OR B.IFLOWINSNAME LIKE (' || CHR(39)  ||'%'|| AI_DATADATE2 ||'%' || CHR(39) || ') )';
				ELSE
				SET LV_WHERE = LV_WHERE || ' AND B.IFLOWINSNAME LIKE (' || CHR(39)  ||'%'|| AI_DATADATE ||'%' || CHR(39) || ')';
				END  IF;
			END  IF;

			IF  LENGTH(AI_CHILDPRO) > 0  THEN
			 IF  INSTR(AI_CHILDPRO,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND A.PRJ_NAME LIKE (' || CHR(39)  || AI_CHILDPRO  || CHR(39) || ')';
			 ELSE
				SET LV_WHERE = LV_WHERE || ' AND A.PRJ_NAME = (' || CHR(39)  || AI_CHILDPRO  || CHR(39) || ')';
			 END IF;
			END  IF;

			IF  LENGTH(AI_ACTNAME) > 0  THEN
			 IF  INSTR(AI_ACTNAME,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND C.IACTNAME LIKE (' || CHR(39)  || AI_ACTNAME || CHR(39) || ')';
			 ELSE
				SET LV_WHERE = LV_WHERE || ' AND C.IACTNAME = (' || CHR(39)  || AI_ACTNAME || CHR(39) || ')';
			 END IF;
			END  IF;

			IF  LENGTH(AI_ACTTYPE) > 0  THEN
			 IF  INSTR(AI_ACTTYPE,'%')>0 THEN
				SET LV_WHERE = LV_WHERE || ' AND C.IACTDEFNAME LIKE (' || CHR(39)  || AI_ACTTYPE  || CHR(39) || ')';
			 ELSE
				SET LV_WHERE = LV_WHERE || ' AND C.IACTDEFNAME = (' || CHR(39)  || AI_ACTTYPE  || CHR(39) || ')';
			 END IF;
			END  IF;

			IF  AI_SIHIDD = 0  THEN
				SET LV_WHERE = LV_WHERE  || ' AND C.ISTATE <> ('|| CHR(39)||'Disable'||CHR(39)||')';
				SET LV_WHERE = LV_WHERE  || ' AND (C.ISTATE IN( ('|| CHR(39)||'Fail:Business'|| CHR(39)||'),('|| CHR(39)||'Fail'|| CHR(39)||')) OR C.IACTNAME NOT LIKE ('|| CHR(39)||'%_CHECK%'||CHR(39)||'))';
				SET LV_WHERE = LV_WHERE  || ' AND (C.ISTATE IN( ('|| CHR(39)||'Fail:Business'|| CHR(39)||'),('|| CHR(39)||'Fail'|| CHR(39)||')) OR C.IACTDEFNAME <> ('|| CHR(39)||'Delayer'||CHR(39)||'))';
			END  IF;

			IF AI_KEYFLOW =1 THEN
			  -- SET LV_WHERE  =LV_WHERE  || ' AND IKEY_FLOW=1';
			END IF;

		   SET LV_ORDER ='';
		   SET LV_ORDER = LV_ORDER || ' ORDER BY  STATEORDER';

		   SET LV_ORDERCASE = '';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' ( CASE WHEN C.ISTATE = ('|| CHR(39)||'Fail'|| CHR(39)||') THEN 0';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'Fail:Business'|| CHR(39)||') THEN  5';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'QueueUp'|| CHR(39)||') THEN  15';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'HangUp'|| CHR(39)||') THEN  20';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'Running'|| CHR(39)||') AND C.IACTDEFNAME=('|| CHR(39)||'Delayer'|| CHR(39)||') THEN  50';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'Running'|| CHR(39)||') AND C.IACTNAME LIKE ('|| CHR(39)||'%_CHECK%'|| CHR(39)||')  THEN 60';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'Running'|| CHR(39)||') AND C.IACTTYPE=('|| CHR(39)||'CallFlow'|| CHR(39)||') THEN  70';
		   SET LV_ORDERCASE = LV_ORDERCASE || ' WHEN C.ISTATE = ('|| CHR(39)||'Disable'|| CHR(39)||') THEN  80  ELSE 10  END )';


			SET LV_SQL = 'INSERT INTO TMP_FIRST_PAGE_STATE_NUM(ISTATE,ACTNUM,IACTNAME) SELECT C.ISTATE,COUNT(C.ISTATE),C.IACTNAME FROM TMP_USER_VALIDATE A, '||LV_IEAI_WORKFLOWINSTANCE||' B, ((SELECT CC.*,nvl(cC.IACTDEFNAME,'||CHR(39)||'1'||CHR(39)||') IACTDEFNAMEN FROM IEAI_ACTRUNTIME CC left join IEAI_REMOTEEXECACT T  on CC.IREXECREQUESTID=T.IID)) C WHERE  A.OPT_ID = '||LN_OPT_ID ||'  ' || LV_WHERE || ' and B.IPROJECTNAME = A.PRJ_NAME AND B.ISTATUS IN (0,8,40,46,47) AND  C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0 GROUP BY C.ISTATE,C.IACTNAME';
			PREPARE	SQLA FROM LV_SQL;
			EXECUTE SQLA;
					
			SET LV_SQL = 'insert into TMP_COUNT(IEAICOUNT) SELECT  COUNT(*) FROM TMP_USER_VALIDATE A, '||LV_IEAI_WORKFLOWINSTANCE||' B, (select CC.* from IEAI_ACTRUNTIME CC left join IEAI_REMOTEEXECACT T on CC.IREXECREQUESTID=T.IID) C WHERE A.OPT_ID = ' || LN_OPT_ID || ' and B.IPROJECTNAME = A.PRJ_NAME AND B.ISTATUS IN (0,8,40,46,47) AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0 ' || LV_WHERE;
			PREPARE	SQLA FROM LV_SQL;
			EXECUTE SQLA;
			
			SELECT IEAICOUNT INTO LI_MAX_RECID FROM TMP_COUNT FETCH FIRST 1 ROWS ONLY;

			
			SET LI_PAGE_COUNT  = TRUNC(LI_MAX_RECID / AI_NUM_OF_PAGE, 0);

			IF  MOD(LI_MAX_RECID, AI_NUM_OF_PAGE) > 0  THEN
			  SET LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
			END  IF;

			IF  AI_PAGE_ID > LI_PAGE_COUNT  THEN
				SET LI_PAGE_ID = LI_PAGE_COUNT;
			ELSE
				SET LI_PAGE_ID = AI_PAGE_ID;
			END  IF;

			IF  LI_PAGE_ID IS NULL OR LI_PAGE_ID < 0  THEN
				SET LI_PAGE_ID = 1;
			END  IF;

			
			SET AI_REC_COUNT = LI_MAX_RECID;

			
			SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
			SET LI_MAX_RECID = LI_PAGE_ID * AI_NUM_OF_PAGE;

			 
			SET LV_SQL = 'INSERT INTO  TMP_FIRST_PAGE_LIST_HOME( OPT_ID, FLOW_ID,ACT_ID,TASK_ID,FLOW_INSTANCE_NAME,ACT_NAME,ACT_DESCRIPTION,SHOULDSTARTTIME,QUEUEUPSTARTTIME,QUEUEUPENDTIME,BEGINEXCTIME,ACT_STATES,IOWNER,PRJ_NAME,ACT_TYPE,STARTUSER_NAME,ACT_ERRORTASKID,EXECACT_IREXECREQUESTID,ACT_DEF_NAME,FLOW_NAME,ISYSTEM,AGENTIP,AGENTPORT,AGENTHOSTNAME,SHELLPATH,ENDTIME)';
			SET LV_SQL =LV_SQL||' SELECT OPT_ID, FLOW_ID,ACT_ID,TASK_ID,FLOW_INSTANCE_NAME,ACT_NAME,ACT_DESCRIPTION,SHOULDSTARTTIME,QUEUEUPSTARTTIME,QUEUEUPENDTIME,BEGINEXCTIME,ACT_STATES,IOWNER,PRJ_NAME,ACT_TYPE,STARTUSER_NAME,ACT_ERRORTASKID,EXECACT_IREXECREQUESTID,ACT_DEF_NAME,FLOW_NAME,ISYSTEM,AGENTIP,AGENTPORT,AGENTHOSTNAME,SHELLPATH,ENDTIME FROM ';
			SET LV_SQL =LV_SQL||' (SELECT OPT_ID, FLOW_ID,ACT_ID,TASK_ID,FLOW_INSTANCE_NAME,ACT_NAME,ACT_DESCRIPTION,SHOULDSTARTTIME,QUEUEUPSTARTTIME,QUEUEUPENDTIME,BEGINEXCTIME,ACT_STATES,IOWNER,PRJ_NAME,ACT_TYPE,STARTUSER_NAME,ACT_ERRORTASKID,EXECACT_IREXECREQUESTID,ACT_DEF_NAME,FLOW_NAME,ISYSTEM,AGENTIP,AGENTPORT,AGENTHOSTNAME,SHELLPATH,ENDTIME,ROW_NUMBER() OVER() AS RID FROM';
			SET LV_SQL =LV_SQL||' (SELECT OPT_ID, FLOW_ID,ACT_ID,TASK_ID,FLOW_INSTANCE_NAME,ACT_NAME,ACT_DESCRIPTION,SHOULDSTARTTIME,QUEUEUPSTARTTIME,QUEUEUPENDTIME,BEGINEXCTIME,ACT_STATES,IOWNER,PRJ_NAME,ACT_TYPE,STARTUSER_NAME,ACT_ERRORTASKID,EXECACT_IREXECREQUESTID,ACT_DEF_NAME,FLOW_NAME,ISYSTEM,AGENTIP,AGENTPORT,AGENTHOSTNAME,SHELLPATH,ENDTIME FROM ';
			SET LV_SQL =LV_SQL||' (SELECT  A.OPT_ID OPT_ID, B.IFLOWID FLOW_ID,C.IACTID ACT_ID,C.ITASKID TASK_ID,B.IFLOWINSNAME FLOW_INSTANCE_NAME,C.IACTNAME ACT_NAME,C.IDESC ACT_DESCRIPTION,C.ISHOULDSTARTTIME SHOULDSTARTTIME,B.IACTQUEUEUPTIME QUEUEUPSTARTTIME,B.IACTRUNNINGTIME QUEUEUPENDTIME,C.IBEGINEXCTIME BEGINEXCTIME,C.ISTATE ACT_STATES,C.IENDTIME ENDTIME,'|| LV_ORDERCASE ||' STATEORDER ,(SELECT T1.IOWNER FROM  IEAI_TASKRUNTIME T1 WHERE T1.IID = C.ITASKID ) AS IOWNER,A.PRJ_NAME PRJ_NAME,C.IACTTYPE ACT_TYPE,B.ISTARTUSERFULLNAME STARTUSER_NAME,C.IERRORTASKID ACT_ERRORTASKID,C.IREXECREQUESTID EXECACT_IREXECREQUESTID,C.IACTDEFNAME ACT_DEF_NAME,B.IFLOWNAME FLOW_NAME,B.ISYSTEM ISYSTEM,T.IAGENTHOST AGENTIP ,T.IAGENTPORT AGENTPORT,( SELECT IAGENT_NAME FROM IEAI_AGENTINFO WHERE IAGENT_IP= T.IAGENTHOST  AND IAGENT_PORT=T.IAGENTPORT FETCH FIRST 1 ROWS ONLY) AGENTHOSTNAME,C.ISHELLPATH SHELLPATH FROM TMP_USER_VALIDATE A, '||LV_IEAI_WORKFLOWINSTANCE||' B, IEAI_ACTRUNTIME C left join IEAI_REMOTEEXECACT T on C.IREXECREQUESTID=T.IID WHERE A.OPT_ID = ' || LN_OPT_ID || ' and B.IPROJECTNAME = A.PRJ_NAME AND B.ISTATUS IN (0,8,40,46,47) AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0 '||LV_WHERE;
			SET LV_SQL = LV_SQL ||')'||LV_ORDER||')) WHERE RID BETWEEN ' || LI_MIN_RECID || ' AND ' || LI_MAX_RECID;
			PREPARE	SQLA FROM LV_SQL;
			EXECUTE SQLA;
		END
		  $
		  
				  CREATE OR REPLACE PROCEDURE PROC_WORKFLOW_QUERY_KL_JZ      (IN AN_USERID  NUMERIC(19,0),OUT AN_OPT_ID  NUMERIC(19,0),IN AV_PRJ_NAME  VARCHAR(255),IN AV_FLOW_NAME  VARCHAR(255),IN AV_START_USER  VARCHAR(255),IN AV_INSTANCE_NAME  VARCHAR(255),IN AI_START_TIME_FROM  BIGINT,IN AI_START_TIME_TO  BIGINT,IN AI_END_TIME_FROM  BIGINT,IN AI_END_TIME_TO  BIGINT,IN AI_SHOW_ACTIVE  SMALLINT,IN AI_SHOW_RUN_SELF  SMALLINT,IN AI_NUM_OF_PAGE  SMALLINT,IN AI_PAGE_ID  INTEGER,OUT AI_REC_COUNT  INTEGER,IN AV_ORDER_ITEM  VARCHAR(80),IN AI_ORDER_TYPE  SMALLINT,IN AN_CHECK_DATE  NUMERIC(19,0),IN AV_HOST_NAME  VARCHAR(50), IN FLOW_ID  NUMERIC(19,0), IN SHOW_FLOW_STATE  SMALLINT,IN AV_WORKFLOW_DESC  VARCHAR(255),IN AV_ISYSTEM  VARCHAR(255),IN AI_SERACH_TYPE SMALLINT, IN AI_EXPROT_SIZE SMALLINT )
			LANGUAGE SQL
			
			  BEGIN
			 
			  DECLARE LN_OPT_ID  NUMERIC(19,0);  
			  DECLARE LV_SQL    VARCHAR(4000);  
			  DECLARE LV_ORDER_BY  VARCHAR(100);  
			  DECLARE LN_MILLISEC  NUMERIC(19,0);  
			  DECLARE LV_WHERE  VARCHAR(4000);  
			  DECLARE LI_PAGE_ID  BIGINT;    
			  DECLARE LI_MAX_RECID  BIGINT;     
			  DECLARE LI_MIN_RECID  BIGINT;    
			  DECLARE LI_PAGE_COUNT  BIGINT;    
			  DECLARE LN_ISALL NUMERIC(19, 0);
			  DECLARE	LI_ZONE		SMALLINT;	
			  DECLARE	LV_FORMAT	VARCHAR(50);	
			
			  DECLARE	SI_TIMEZONE_CUR SMALLINT DEFAULT 8;	
			  DECLARE	SI_TIMEZONE_GMT SMALLINT DEFAULT 0;	
			
			   
			   IF  AI_NUM_OF_PAGE IS NULL OR AI_NUM_OF_PAGE <= 0  THEN
				 RETURN;
			   END  IF;
			
			   CALL PROC_GET_NEXT_ID('TMP_USER_VALIDATE', LN_OPT_ID);
			   SET	AN_OPT_ID = LN_OPT_ID;
			
				SET	LI_ZONE = 0;				
			SET	LV_FORMAT = 'YYYY-MM-DD HH24:MI:SS';	
			   
			
				  SELECT COUNT(*)
					   INTO LN_ISALL
				  FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
				  WHERE T.IROLEID = T2.IROLEID
					AND T2.IPERMISSION = 1
					AND T.IUSERID = AN_USERID
					AND T2.IPROID = -1;
			   
				 IF LN_ISALL > 0 THEN
				   INSERT INTO TMP_USER_VALIDATE
					 (OPT_ID, PRJ_ID, PRJ_NAME, PERMIT_ID, PERMIT_NAME)
					 SELECT LN_OPT_ID AS OPT_ID,
							IID       AS PRJ_ID,
							INAME     AS PRJ_NAME,
							0         AS PERMIT_ID,
							1         AS PERMIT_NAME
					   FROM IEAI_PROJECT
					  WHERE PROTYPE = 1
						AND IPKGCONTENTID <> 0
						AND IUPPERID = IID;
				 ELSE
				   INSERT INTO TMP_USER_VALIDATE
					 (OPT_ID, PRJ_ID, PRJ_NAME, PERMIT_ID, PERMIT_NAME)
					 SELECT LN_OPT_ID AS OPT_ID,
							PRJ.IID   AS PRJ_ID,
							PRJ.INAME AS PRJ_NAME,
							0         AS PERMIT_ID,
							1         AS PERMIT_NAME
					   FROM IEAI_PROJECT        PRJ,
							IEAI_SYS_PERMISSION ISP,
							IEAI_USERINHERIT    IU
					  WHERE IU.IUSERID = AN_USERID
						AND ISP.IROLEID = IU.IROLEID
						AND PRJ.IID = ISP.IPROID
						AND ISP.IPROID > 0
						AND ISP.IPERMISSION = 1
						AND PRJ.PROTYPE = 1
						AND PRJ.IPKGCONTENTID <> 0;
				 END IF;
				
				   SET LN_MILLISEC = AN_CHECK_DATE;
			
				   SET	LV_WHERE = '';
			
				   IF	LENGTH(AV_PRJ_NAME) > 0	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.IPROJECTNAME LIKE (' || CHR(39) || '%' || AV_PRJ_NAME || '%' || CHR(39) || ')';
				   END	IF;
			
			
				   IF	LENGTH(AV_FLOW_NAME) > 0	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWNAME LIKE (' || CHR(39) || '%' || AV_FLOW_NAME || '%' || CHR(39) || ')';
				   END	IF;
			
			
				   IF	LENGTH(AV_START_USER) > 0	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTUSERFULLNAME LIKE (' || CHR(39) || '%' || AV_START_USER || '%' || CHR(39) || ')';
				   END	IF;
			
			
				   IF	LENGTH(AV_INSTANCE_NAME) > 0	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWINSNAME LIKE (' || CHR(39) || '%' || AV_INSTANCE_NAME || '%' || CHR(39) || ')';
				   END	IF;
			
			
				  IF	AI_START_TIME_FROM > 0	THEN
					   IF	AI_START_TIME_TO > 0	THEN
						   SET	LV_WHERE = LV_WHERE || ' AND (C.ISTARTTIME BETWEEN ' || TO_CHAR(AI_START_TIME_FROM) || ' AND ' || TO_CHAR(AI_START_TIME_TO) || ')';
					   ELSE
						   SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTTIME >= ' || TO_CHAR(AI_START_TIME_FROM);
					   END	IF;
				   ELSE
					   IF	AI_START_TIME_TO > 0	THEN
						   SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTTIME <= ' || TO_CHAR(AI_START_TIME_TO);
					   END	IF;	
				   END	IF;
			
			
				   IF	AI_END_TIME_FROM > 0	THEN
					   IF	AI_END_TIME_TO > 0	THEN
						   SET	LV_WHERE = LV_WHERE || ' AND (C.IENDTIME BETWEEN ' || TO_CHAR(AI_END_TIME_FROM) || ' AND ' || TO_CHAR(AI_END_TIME_TO) || ')';
					   ELSE
						   SET	LV_WHERE = LV_WHERE || ' AND C.IENDTIME >= ' || TO_CHAR(AI_END_TIME_FROM);
					   END	IF;
				   ELSE
					   IF	AI_END_TIME_TO > 0	THEN
						   SET	LV_WHERE = LV_WHERE || ' AND C.IENDTIME <= ' || TO_CHAR(AI_END_TIME_TO);
					   END	IF;
				   END	IF;
			
			
				   IF	AI_SHOW_ACTIVE = 1	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.ISTATUS IN (0, 6, 8, 15, 30)';
				   END	IF;
			
				   IF	AI_SHOW_RUN_SELF = 1	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.IISAUTOSTART = 1';
				   END	IF;
			
			
				   IF	LENGTH(AV_HOST_NAME) > 0	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.IHOSTNAME LIKE (' || CHR(39) || '%' || AV_HOST_NAME || '%' || CHR(39) || ')';
				   END	IF;
			
			
				   IF	FLOW_ID > 0	THEN
					   SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWID = ' || TO_CHAR(FLOW_ID);
				   END	IF;
			
				   IF  SHOW_FLOW_STATE >= 0  THEN
					   SET  LV_WHERE = LV_WHERE || ' AND C.ISTATUS = (' || CHR(39)  || SHOW_FLOW_STATE  || CHR(39) || ')';
				   END  IF;
			
			
				   IF  LENGTH(AV_WORKFLOW_DESC) > 0  THEN
					 SET LV_WHERE = LV_WHERE || ' AND C.IFLOWDES LIKE (' || CHR(39) || '%' || AV_WORKFLOW_DESC || '%' || CHR(39) || ')';
				   END  IF;
			
			
				   IF  LENGTH(AV_ISYSTEM) > 0  THEN
					 SET  LV_WHERE = LV_WHERE || ' AND C.ISYSTEM LIKE (' || CHR(39)  || '%' || AV_ISYSTEM  || '%' || CHR(39) || ')';
				   END  IF;
			
				  SET LV_SQL = 'INSERT INTO TMP_FLOW_COUNT(IEAICOUNT) SELECT COUNT(*) FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C WHERE A.OPT_ID = ' || LN_OPT_ID || ' AND C.IPROJECTNAME = A.PRJ_NAME' || LV_WHERE;
			
					PREPARE	SQLA FROM LV_SQL;
				   EXECUTE SQLA;
			
				   SELECT IEAICOUNT INTO LI_MAX_RECID FROM TMP_FLOW_COUNT FETCH FIRST 1 ROWS ONLY;
					
				   
				   
					SET LI_PAGE_COUNT  = TRUNC(LI_MAX_RECID / AI_NUM_OF_PAGE, 0);
			
				   IF  MOD(LI_MAX_RECID, AI_NUM_OF_PAGE) > 0  THEN
					SET LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
				   END  IF;
			
				   IF  AI_PAGE_ID > LI_PAGE_COUNT  THEN
					SET LI_PAGE_ID = LI_PAGE_COUNT;
				   ELSE
					SET LI_PAGE_ID = AI_PAGE_ID;
				   END  IF;
			
				   IF  LI_PAGE_ID IS NULL OR LI_PAGE_ID < 0  THEN
					SET LI_PAGE_ID = 1;
				   END  IF;
			   
					  SET AI_REC_COUNT = LI_MAX_RECID;
			
			
					IF AI_SERACH_TYPE =0 THEN
			
					 SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
					 SET LI_MAX_RECID = LI_PAGE_ID * AI_NUM_OF_PAGE;
					END IF;
			
					IF AI_SERACH_TYPE > 0 THEN
			
					  SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
					  SET  LI_MAX_RECID =AI_EXPROT_SIZE+LI_MIN_RECID;
					END IF;
			
				   IF	AI_ORDER_TYPE = 1	THEN
					   SET	LV_ORDER_BY = ' ASC';
				   ELSE
					   SET	LV_ORDER_BY = ' DESC';
				   END	IF;
			
			
			
				   IF  LENGTH(AV_INSTANCE_NAME) > 0 or FLOW_ID > 0  THEN
			IF  LENGTH(AV_ORDER_ITEM) > 0 THEN 
			SET LV_ORDER_BY = ' ORDER BY 2, ' ||  AV_ORDER_ITEM ||  LV_ORDER_BY;
			ELSE
			  SET LV_ORDER_BY = ' ORDER BY 2 ' ||  LV_ORDER_BY;
			END IF;
					 
					   SET  LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, ROW_NUMBER() OVER('|| LV_ORDER_BY ||')  AS RID FROM (SELECT A.OPT_ID AS OPTID, ';
			  
					   IF  UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'  THEN
						   SET LV_SQL = LV_SQL || 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'  THEN
						   SET LV_SQL = LV_SQL || 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'FLOW_ID'  THEN
						   SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'  THEN
						   SET LV_SQL = LV_SQL || 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'STATUS'  THEN
						   SET LV_SQL = LV_SQL || 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_USER'  THEN
						   SET LV_SQL = LV_SQL || 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_TIME'  THEN
						   SET LV_SQL = LV_SQL || 'C.ISTARTTIME AS START_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'END_TIME'  THEN
						   SET LV_SQL = LV_SQL || 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'PAID_TIME'  THEN
						   SET LV_SQL = LV_SQL || '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN ' || TO_CHAR(LN_MILLISEC) || ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   ELSEIF  UPPER(AV_ORDER_ITEM) = 'TASK_NUM'  THEN
						   SET LV_SQL = LV_SQL || 'NVL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ';
					   ELSE
			
						SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID,NVL(D.TASK_NUM, 0) AS TASK_NUM ';
					   END  IF;
			
					   
					   SET LV_SQL = LV_SQL || 'FROM TMP_USER_VALIDATE A INNER JOIN IEAI_WORKFLOWINSTANCE C ON C.IPROJECTNAME = A.PRJ_NAME LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' || TO_CHAR(LN_OPT_ID) ;
					   SET LV_SQL = LV_SQL || LV_WHERE;
				   
							 
				   
					   SET LV_SQL = LV_SQL ||  ')) WHERE RID BETWEEN ' || LI_MIN_RECID || ' AND ' || LI_MAX_RECID;
			   ELSE
			IF  LENGTH(AV_ORDER_ITEM) > 0 THEN 
			SET LV_ORDER_BY = ' ORDER BY 2, ' ||  AV_ORDER_ITEM ||  LV_ORDER_BY;
			ELSE
			  SET LV_ORDER_BY = ' ORDER BY 2 ' ||  LV_ORDER_BY;
			END IF;
				  SET LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, ROW_NUMBER() OVER('|| LV_ORDER_BY ||') AS RID FROM (SELECT A.OPT_ID AS OPTID, ';
				 
			
				   IF  UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'  THEN
						SET  LV_SQL = LV_SQL || 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'  THEN
					   SET LV_SQL = LV_SQL || 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'FLOW_ID'  THEN
					   SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'  THEN
					   SET LV_SQL = LV_SQL || 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'STATUS'  THEN
					   SET LV_SQL = LV_SQL || 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_USER'  THEN
					   SET LV_SQL = LV_SQL || 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_TIME'  THEN
					   SET LV_SQL = LV_SQL || 'C.ISTARTTIME AS START_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'END_TIME'  THEN
					   SET LV_SQL = LV_SQL || 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'PAID_TIME'  THEN
					   SET LV_SQL = LV_SQL || '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN ' || TO_CHAR(LN_MILLISEC) || ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   ELSEIF  UPPER(AV_ORDER_ITEM) = 'TASK_NUM'  THEN
					   SET LV_SQL = LV_SQL || 'NVL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ';
				   ELSE
					   SET LV_ORDER_BY = ' ORDER BY 2 DESC';
					   SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
				   END  IF;
				   
				   SET LV_SQL = LV_SQL || 'FROM TMP_USER_VALIDATE A INNER JOIN IEAI_WORKFLOWINSTANCE C ON C.IPROJECTNAME = A.PRJ_NAME LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' || TO_CHAR(LN_OPT_ID) ;
				   
				   SET LV_SQL = LV_SQL || LV_WHERE ||  ')) WHERE RID BETWEEN ' || LI_MIN_RECID || ' AND ' || LI_MAX_RECID;
			   END  IF;
			   
			   
			   
			   PREPARE	SQLA FROM LV_SQL;
				EXECUTE SQLA;
			   
			
				   INSERT  INTO  TMP_WORKFLOW_QUERY
				 (
				   OPTID,
				   FLOW_ID,
				   FLOW_NAME,
				   INSTANCE_NAME,
				   PROJECT_NAME,
				   STATUS,
				   START_USER,
				   START_TIME,
				   END_TIME,
				   PAID_TIME,
				   HOST_NAME,
				   TASK_NUM,
				   REC_ID,
				   ISYSTEM,
				   AGENTIP,
				   AGENTPORT,
				   IAGENTHOSTNAME,
				   IAGENT_IPASNAME,
				   ISONLINE
				 )
			 SELECT  A.OPTID,
				 A.FLOW_ID,
				 B.IFLOWNAME,
				 B.IFLOWINSNAME,
				 B.IPROJECTNAME,
				 LTRIM(TO_CHAR(B.ISTATUS)),
				 B.ISTARTUSERFULLNAME,
				 (CASE WHEN B.ISTARTTIME > 0 THEN FUN_GET_DATE_STRING(B.ISTARTTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
				 (CASE WHEN B.IENDTIME > 0 THEN FUN_GET_DATE_STRING(B.IENDTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
				 (
				   CASE  WHEN  B.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN LTRIM(TO_CHAR(TRUNC((LN_MILLISEC - B.ISTARTTIME) / 1000, 0) * 1000))
					 WHEN  B.ISTATUS IN (2, 4, 7) THEN LTRIM(TO_CHAR(TRUNC((B.IENDTIME - B.ISTARTTIME) / 1000, 0) * 1000))
					 ELSE  '0'
				   END
				 ),
				 B.IHOSTNAME,
				 A.TASK_NUM,
				 A.REC_ID ,
				 B.ISYSTEM,
			   '','',
				 (
				 SELECT
				   T2.IAGENT_NAME
				 FROM
				   TEMP_IEAI_REMOTEEXECACT T1 ,
				   IEAI_AGENTINFO T2
				 WHERE
				   T2.IAGENT_IP= T1.IAGENTHOST AND
				   T2.IAGENT_PORT=T1.IAGENTPORT AND
				   T1.IFLOWID=A.FLOW_ID 
					fetch first 1 rows only  
				 ),
				 (
				 SELECT
				   IAGENTHOST
				 FROM
				   TEMP_IEAI_REMOTEEXECACT
				 WHERE
				   IFLOWID=A.FLOW_ID 
				   fetch first 1 rows only  
				 ),
			   FUN_CHECK_ONLINE(B.IFLOWNAME,B.IPROJECTNAME)
			   FROM  TMP_WORKFLOW_QUERY_TEMP A,
				 IEAI_WORKFLOWINSTANCE B
			   WHERE  A.OPTID = LN_OPT_ID
			   AND  B.IFLOWID = A.FLOW_ID
			   ORDER  BY A.REC_ID;
			
			  END
			  $

		CREATE or REPLACE PROCEDURE PROC_GET_PRJCOUNT( IN STARTTIME NUMERIC(19), 
																			IN  ENDTIME NUMERIC(19), 
																			IN PRJNAMES VARCHAR(255), 
																		  IN DESCS VARCHAR(255))
		BEGIN
		  DECLARE LV_SQL VARCHAR(4000);
			DECLARE LV_WHERE1 VARCHAR(2000) default '';
			DECLARE LV_WHERE2 VARCHAR(2000) default '';
		  BEGIN

		  SET LV_WHERE1 = LV_WHERE1 || ' T1.IBEGINEXCTIME >='||  STARTTIME || ' AND T1.IBEGINEXCTIME <='|| ENDTIME;
		  SET LV_WHERE2 = LV_WHERE2 || '  T1.IBEGINTIME >='|| STARTTIME || ' AND T1.IBEGINTIME <='|| ENDTIME;

		  IF LENGTH(PRJNAMES)>0 THEN
			SET LV_WHERE1 =LV_WHERE1 || ' AND '||' T1.IPRJNAME IN ('|| PRJNAMES || ') ';
			SET LV_WHERE2 =LV_WHERE2 || ' AND '||' T1.IPRJNAME IN ('|| PRJNAMES || ') ';
		  END IF;

		  IF LENGTH(DESCS)>0 THEN
			SET LV_WHERE1 =LV_WHERE1||' AND '||' T1.IACTNAMETYPE IN ('||DESCS||') ';
			SET LV_WHERE2 =LV_WHERE2||' AND '||' T1.IACTTYPE IN ('|| DESCS||') ';
		  END IF;
		 
		   SET LV_SQL = 'INSERT INTO TMP_ALLCOUNT(IPROJECTNAME,ITASKCOUNT,IARTICOUNT,ISUCCCOUNT,IERRCOUNT,
											  IBAKCOUNT, IBAKARTICOUNT,IBAKSUCCCOUNT,IBAKERRCOUNT,
											  IPOLLINGCOUNT,IPOLLINGARTICOUNT,IPOLLINGSUCCCOUNT,IPOLLINGERRCOUNT,
											  INOTBAKCOUNT,INOTBAKARTICOUNT,INOTBAKSUCCCOUNT,INOTBAKERRCOUNT)
				 SELECT T1.iprjname AS aaa, COUNT(CASE WHEN T1.iactdefname=''ShellCmd'' THEN 0 ELSE NULL END) AS TASK,
				COUNT(CASE WHEN T1.iactdefname=''UserTask'' THEN 0 ELSE NULL END) AS ARTI,
				COUNT(CASE WHEN T1.idelflag=1 THEN NULL ELSE 0 END) AS SUCC,
				COUNT(CASE WHEN T1.idelflag=1 THEN 0 ELSE NULL END) AS ERR,
				COUNT(CASE WHEN T1.IACTNAMETYPE=1 THEN 0 ELSE NULL END) AS BAK,
				COUNT(CASE WHEN (T1.IACTNAMETYPE=1 AND T1.iactdefname=''UserTask'') THEN 0 ELSE NULL END) AS BAKARTI,
				COUNT(CASE WHEN T1.IACTNAMETYPE=1 AND T1.idelflag!=1 THEN 0 ELSE NULL END) AS BAKSUCC,
				COUNT(CASE WHEN T1.IACTNAMETYPE=1 AND T1.idelflag=1 THEN 0 ELSE NULL END) AS BAKERR,

				COUNT(CASE WHEN T1.IACTNAMETYPE=2 THEN 1 ELSE NULL END) AS POLLING,
				COUNT(CASE WHEN T1.IACTNAMETYPE=2 AND T1.iactdefname=''UserTask'' THEN 0 ELSE NULL END) AS POLLINGARTI,
				COUNT(CASE WHEN T1.IACTNAMETYPE=2 AND T1.idelflag!=1 THEN 0 ELSE NULL END) AS POLLINGSUCC,
				COUNT(CASE WHEN T1.IACTNAMETYPE=2 AND T1.idelflag=1 THEN 0 ELSE NULL END) AS POLLINGERR,

				COUNT(CASE WHEN T1.IACTNAMETYPE=0 THEN 1 ELSE NULL END) AS NOTBAK,
				COUNT(CASE WHEN T1.IACTNAMETYPE=0 AND T1.iactdefname=''UserTask'' THEN 0 ELSE NULL END) AS NOTBAKARTI,
				COUNT(CASE WHEN T1.IACTNAMETYPE=0 AND T1.idelflag!=1 THEN 0 ELSE NULL END) AS NOTBAKSUCC,
				COUNT(CASE WHEN T1.IACTNAMETYPE=0 AND T1.idelflag=1 THEN 0 ELSE NULL END) AS NOTBAKERR

			   FROM ieai_actruntime_validpart T1 WHERE '
			   || LV_WHERE1 || ' GROUP BY T1.iprjname ';
			PREPARE	SQLA FROM  LV_SQL;
			EXECUTE SQLA;
			
		 
		  SET LV_SQL = 'INSERT INTO TMP_OVERTIMECOUNT(ICOUNT,IPROJECTNAME,
			IBAKOVERTIMECOUNT,IPOLLINGOVERTIMECOUNT,INOTBAKOVERTIMECOUNT)
		  SELECT COUNT(T1.IPRJNAME),T1.IPRJNAME,
				COUNT(CASE WHEN T1.IACTTYPE=1 THEN 0 ELSE NULL END) AS BAKOVERTIME,
				COUNT(CASE WHEN T1.IACTTYPE=2 THEN 0 ELSE NULL END) AS POLLINGOVERTIME,
				COUNT(CASE WHEN T1.IACTTYPE=0 THEN 0 ELSE NULL END) AS NOTBAKOVERTIME  FROM
				IEAI_ERRORINFO_PART T1 WHERE  '
			|| LV_WHERE2 || ' AND T1.IERRORTYPE = 2 ' || ' GROUP BY T1.IPRJNAME ';
			
		  PREPARE	SQLA FROM  LV_SQL;
			EXECUTE SQLA;
		  END; 
		END
		$
		--4.7.19 not exists	
		--4.7.20 not exists	
		--4.7.21 
		BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTRUNTIME' AND INDNAME='IDX_IEAI_ACTRUNTIME_07';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_IEAI_ACTRUNTIME_07 ON IEAI_ACTRUNTIME(IERRORTASKID)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'com.ideal.ieai.server.jobscheduling.repository.project.ActLastLine';
		IF	LI_EXISTS = 0 THEN
				SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'com.ideal.ieai.server.jobScheduling.repository.project.ActLastLine';
					IF	LI_EXISTS = 1 THEN
						SET	LS_SQL = 'INSERT INTO IEAI_ID  SELECT  ''com.ideal.ieai.server.jobscheduling.repository.project.ActLastLine'' ,T.IVALUE FROM IEAI_ID T WHERE T.ICLASSNAME = ''com.ideal.ieai.server.jobScheduling.repository.project.ActLastLine''';
							PREPARE	SQLA FROM LS_SQL; 
							EXECUTE SQLA;
							END	IF;
		END	IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'com.ideal.ieai.server.repository.jobscheduling.monitor.RepActMonitor';
		IF	LI_EXISTS = 0 THEN
			  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME = 'com.ideal.ieai.server.repository.jobScheduling.monitor.RepActMonitor';
			  IF	LI_EXISTS = 1 THEN
				SET	LS_SQL = 'INSERT INTO IEAI_ID  SELECT  ''com.ideal.ieai.server.repository.jobscheduling.monitor.RepActMonitor'' ,T.IVALUE FROM IEAI_ID T WHERE T.ICLASSNAME = ''com.ideal.ieai.server.repository.jobScheduling.monitor.RepActMonitor''';
						PREPARE	SQLA FROM LS_SQL; 
						EXECUTE SQLA;
					END	IF;
		END	IF;
		
		END
		$
		--4.7.22 not exists	
		--4.7.23 
		BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_LESBINFO';
			IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_LESBINFO
						(
						  IID         DECIMAL(19) NOT NULL,
						  IFLOWID     DECIMAL(19),
						  LESBIP      VARCHAR(255),
						  LESBPORT    DECIMAL(19),
						  SENDMSG     VARCHAR(4000),
						  RESPMSG     VARCHAR(4000),
						  RESPHEAD    VARCHAR(1000),
						  RESPSTATS   VARCHAR(1000),
						  ACTNAME     VARCHAR(255),
						  ICREATETIME DECIMAL(19),
						  IUPDATETIME DECIMAL(19),
						  GLOSEQNO    VARCHAR(255),
						  IOPTTYPE    DECIMAL(2),
						  ACTID       DECIMAL(19)
						,CONSTRAINT PK_IEAI_LESBINFO PRIMARY KEY (IID))';
					PREPARE	SQLA FROM LS_SQL;
					EXECUTE SQLA;
			END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_LESBQUERYINFO';
			IF LI_EXISTS = 0 THEN
			SET LS_SQL = 'CREATE TABLE IEAI_LESBQUERYINFO
						(
						IID DECIMAL(19) NOT NULL,
						XITJYIRQ VARCHAR(8) ,
						XITONGRQ VARCHAR(8),
						DQIANBUZ DECIMAL(8),
						DQLCBZHA DECIMAL(8),
						JYKSSHIJ VARCHAR(23),
						JYJSSHIJ VARCHAR(23),
						PLJYZUHS VARCHAR(23),
						JYKEYXBZ VARCHAR(2),
						SCJIOYRQ VARCHAR(8),
						SSCIJYRQ VARCHAR(8),
						XCJIOYRQ VARCHAR(8),
						DQPLKYZU VARCHAR(10),
						PILKYBSS VARCHAR(10),
						JIAOYZHT VARCHAR(15),
						TASKPROGRESS VARCHAR(6),
						PLJYPICH VARCHAR(100),
						ICREATETIME DECIMAL(19),
						IUPDATETIME DECIMAL(19),
						GLOSEQNO VARCHAR(225),
						CONSTRAINT PK_IEAI_LESBQUERYINFO PRIMARY KEY (IID))';
					PREPARE	SQLA FROM LS_SQL;
					EXECUTE SQLA;
			END IF;
		END
		$
		--4.7.24 not exists	
		--4.7.25 	
		BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTINFO' AND INDNAME='IDX_IPRJUPPERID';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IPRJUPPERID ON IEAI_ACTINFO (IPRJUPPERID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END	IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTINFO' AND INDNAME='IDX_IACTNAME';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IACTNAME ON IEAI_ACTINFO (IACTNAME) ';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END	IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTINFO' AND INDNAME='IDX_IFLOWUPPERID';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IFLOWUPPERID ON IEAI_ACTINFO (IFLOWUPPERID) ';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END	IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTINFO' AND INDNAME='IDX_ILASTID';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL = 'CREATE INDEX IDX_ILASTID ON IEAI_ACTINFO (ILASTID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END	IF;
		 
		 
		 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='TMP_USER_PRJ_VALIDATE' AND COLNAME = 'PRJNAME';
				IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'DROP TABLE TMP_USER_PRJ_VALIDATE';
					   PREPARE	SQLA FROM LS_SQL; 
					   EXECUTE SQLA;
				SET LS_SQL = '	CREATE GLOBAL TEMPORARY TABLE TMP_USER_PRJ_VALIDATE ( 	PRJID    	DECIMAL(19,0) NOT NULL,	PRJNAME VARCHAR(255))';
					   PREPARE	SQLA FROM LS_SQL; 
					   EXECUTE SQLA;
				END	IF;
		
		
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 1079 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1079, ''新活动监控'', ''1'', ''actSDMonitorIndex.do'', 2, '''', ''images/info25.png'')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;
		  
		  
		  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 1080 ;
		  IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1080, ''新工作流查询'', ''1'', ''forwardSDFlowQuery.do'', 5, '''', ''images/info27.png'')';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		  END	IF;
		  
		  
		  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 1081 ;
		  IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1081, ''新工作流启动'', ''1'', ''forwardSDStartFlow.do'', 3, '''', ''images/info27.png'')';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		  END	IF;
			
	END
	$


	CREATE OR REPLACE PROCEDURE PROC_ACTLIST_SDPAGE         (IN AN_USERID NUMERIC(19,0),IN AN_START NUMERIC(19,0),IN AN_PAGESIZE NUMERIC(19,0),IN AN_PRJNAME VARCHAR(255),OUT AN_TOTAL NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_OPT_ID	NUMERIC(19,0);
			DECLARE	AN_PROTYPE	NUMERIC(19,0);
			DECLARE LN_ISALL    NUMERIC(2,0);
			SET AN_PROTYPE = 1;
			

			SELECT COUNT(1)  INTO LN_ISALL
						FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
						WHERE T.IROLEID = T2.IROLEID   AND T2.IPERMISSION = 1
						AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;
		　　　　　　　　　　　　　　　　　　
			CALL	PROC_GET_NEXT_ID ('TEMP_IEAI_PERMIT_VALIDATE', LN_OPT_ID);
			SET	AN_OPT_ID = LN_OPT_ID;
			IF  LN_ISALL>0 THEN 
					IF (AN_PRJNAME IS NULL) THEN
						SELECT COUNT(1)
						INTO AN_TOTAL
						FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
						WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND C.STATECODE = 0
							AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));
							
							
							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID,FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME
						FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME
											FROM (
									SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
															LN_OPT_ID AS OPT_ID,
															C.IID,
															B.IFLOWID,
															C.IACTID,
															C.ITASKID,
															B.IFLOWINSNAME,
															C.IACTNAME,
															C.IDESC,
															C.ISHOULDSTARTTIME,
															C.IBEGINEXCTIME,
															C.ISTATE,
															(SELECT T1.IOWNER
																FROM IEAI_TASKRUNTIME T1
															WHERE T1.IID = C.ITASKID) AS IOWNER,
															T.INAME,
															C.IACTTYPE,
															B.ISTARTUSERFULLNAME,
															C.IERRORTASKID,
															C.IREXECREQUESTID,
															C.IACTDEFNAME,
															B.IFLOWNAME,
															C.ISHOULDENDTIME
														FROM IEAI_WORKFLOWINSTANCE B,
															IEAI_ACTRUNTIME       C,
															IEAI_PROJECT          T
													WHERE B.IPRJLATESTID = T.IID
														AND T.PROTYPE = 1
														AND B.ISTATUS = 0
														AND C.IFLOWID = B.IFLOWID
														AND C.DISAPPEAR = 0
														AND C.IISMONITORACT = 0
														AND C.ACTTYPECODE = 0
														AND C.STATECODE = 0
													   -- AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
														AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
												) WWW
										) WW
							WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					ELSE 
								SELECT COUNT(1)
								INTO AN_TOTAL
								FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
								WHERE B.IPRJLATESTID = T.IID
								AND T.PROTYPE = 1
								AND B.ISTATUS = 0
								AND C.IFLOWID = B.IFLOWID
								AND C.DISAPPEAR = 0
								AND C.IISMONITORACT = 0
								AND C.ACTTYPECODE = 0
								AND T.INAME like '%'||AN_PRJNAME||'%'
								AND C.STATECODE = 0
							 --   AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
								AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));
								
								INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
								SELECT WW.OPT_ID,
										WW.IID,
										WW.IFLOWID,
										WW.IACTID,
										WW.ITASKID,
										WW.IFLOWINSNAME,
										WW.IACTNAME,
										WW.IDESC,
										WW.ISHOULDSTARTTIME,
										WW.IBEGINEXCTIME,
										WW.ISTATE,
										WW.IOWNER,
										WW.INAME,
										WW.IACTTYPE,
										WW.ISTARTUSERFULLNAME,
										WW.IERRORTASKID,
										WW.IREXECREQUESTID,
										WW.IACTDEFNAME,
										WW.IFLOWNAME,
										WW.ISHOULDENDTIME
									FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME
																			FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
												LN_OPT_ID AS OPT_ID,
												C.IID,
												B.IFLOWID,
												C.IACTID,
												C.ITASKID,
												B.IFLOWINSNAME,
												C.IACTNAME,
												C.IDESC,
												C.ISHOULDSTARTTIME,
												C.IBEGINEXCTIME,
												C.ISTATE,
												(SELECT T1.IOWNER
													FROM IEAI_TASKRUNTIME T1
													WHERE T1.IID = C.ITASKID) AS IOWNER,
												T.INAME,
												C.IACTTYPE,
												B.ISTARTUSERFULLNAME,
												C.IERRORTASKID,
												C.IREXECREQUESTID,
												C.IACTDEFNAME,
												B.IFLOWNAME,
												C.ISHOULDENDTIME
											FROM IEAI_WORKFLOWINSTANCE B,
												IEAI_ACTRUNTIME       C,
												IEAI_PROJECT          T
											WHERE B.IPRJLATESTID = T.IID
											AND T.PROTYPE = 1
											AND B.ISTATUS = 0
											AND C.IFLOWID = B.IFLOWID
											AND C.DISAPPEAR = 0
											AND C.IISMONITORACT = 0
											AND C.ACTTYPECODE = 0
											AND T.INAME like '%'||AN_PRJNAME||'%'
											AND C.STATECODE = 0
										  --  AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
											AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
										) WWW
										) WW
									WHERE WW.ROWN > AN_START
									AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					END IF;
			ELSE
				
				IF (AN_PRJNAME IS NULL) THEN 

				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
					SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
										AND T2.IPERMISSION = 1
										AND T.IUSERID = AN_USERID
										AND T2.IPROID > 0) A,
									IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
								AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
							IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;
				
				

					SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0
						--AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT PRJNAME DISTINCT FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));
						 
						

					INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
					SELECT WW.OPT_ID,
							WW.IID,
							WW.IFLOWID,
							WW.IACTID,
							WW.ITASKID,
							WW.IFLOWINSNAME,
							WW.IACTNAME,
							WW.IDESC,
							WW.ISHOULDSTARTTIME,
							WW.IBEGINEXCTIME,
							WW.ISTATE,
							WW.IOWNER,
							WW.INAME,
							WW.IACTTYPE,
							WW.ISTARTUSERFULLNAME,
							WW.IERRORTASKID,
							WW.IREXECREQUESTID,
							WW.IACTDEFNAME,
							WW.IFLOWNAME,
							WW.ISHOULDENDTIME
						FROM (SELECT WWW.ROWN,
								WWW.OPT_ID,
								WWW.IID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
							FROM IEAI_TASKRUNTIME T1
							WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME
					FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
					WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND C.STATECODE = 0
					--	AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
						AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
						
						) WWW
					) WW
			WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				
			ELSE
			
				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
				SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
									AND T2.IPERMISSION = 1
									AND T.IUSERID = AN_USERID
									AND T2.IPROID > 0) A,
								IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
							AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
						IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;
				
				SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME like '%'||AN_PRJNAME||'%' AND C.STATECODE = 0
					--AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));
					 
					

				INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
				SELECT WW.OPT_ID,
						WW.IID,
						WW.IFLOWID,
						WW.IACTID,
						WW.ITASKID,
						WW.IFLOWINSNAME,
						WW.IACTNAME,
						WW.IDESC,
						WW.ISHOULDSTARTTIME,
						WW.IBEGINEXCTIME,
						WW.ISTATE,
						WW.IOWNER,
						WW.INAME,
						WW.IACTTYPE,
						WW.ISTARTUSERFULLNAME,
						WW.IERRORTASKID,
						WW.IREXECREQUESTID,
						WW.IACTDEFNAME,
						WW.IFLOWNAME,
						WW.ISHOULDENDTIME
					FROM (SELECT WWW.ROWN,
								WWW.IID,
								WWW.OPT_ID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
								LN_OPT_ID AS OPT_ID,
								C.IID,
								B.IFLOWID,
								C.IACTID,
								C.ITASKID,
								B.IFLOWINSNAME,
								C.IACTNAME,
								C.IDESC,
								C.ISHOULDSTARTTIME,
								C.IBEGINEXCTIME,
								C.ISTATE,
								(SELECT T1.IOWNER
									FROM IEAI_TASKRUNTIME T1
									WHERE T1.IID = C.ITASKID) AS IOWNER,
								T.INAME,
								C.IACTTYPE,
								B.ISTARTUSERFULLNAME,
								C.IERRORTASKID,
								C.IREXECREQUESTID,
								C.IACTDEFNAME,
								B.IFLOWNAME,
								C.ISHOULDENDTIME
							FROM TMP_USER_PRJ_VALIDATE PR,
								IEAI_WORKFLOWINSTANCE B,
								IEAI_ACTRUNTIME       C,
								IEAI_PROJECT          T
							WHERE PR.PRJID = T.IUPPERID
							AND B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME like '%'||AN_PRJNAME||'%'
							AND C.STATECODE = 0
							--AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
							) WWW
						) WW
					WHERE WW.ROWN > AN_START
					AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				END IF;
			END IF;
		END

		 $


	CREATE OR REPLACE PROCEDURE PROC_ACTLIST_SDPAGEBYID        (IN AN_USERID NUMERIC(19,0),IN AN_START NUMERIC(19,0),IN AN_PAGESIZE NUMERIC(19,0),IN AN_PRJNAME VARCHAR(255),OUT AN_TOTAL NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0),IN AN_FLOWID NUMERIC(19,0))
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_OPT_ID	NUMERIC(19,0);
			DECLARE	AN_PROTYPE	NUMERIC(19,0);
			DECLARE LN_ISALL    NUMERIC(2,0);
			SET AN_PROTYPE = 1;
			

			SELECT COUNT(1)  INTO LN_ISALL
						FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
						WHERE T.IROLEID = T2.IROLEID   AND T2.IPERMISSION = 1
						AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;
		　　　　　　　　　　　　　　　　　　
			CALL	PROC_GET_NEXT_ID ('TEMP_IEAI_PERMIT_VALIDATE', LN_OPT_ID);
			SET	AN_OPT_ID = LN_OPT_ID;
			IF  LN_ISALL>0 THEN 
					IF (AN_PRJNAME IS NULL) THEN
						SELECT COUNT(1)
						INTO AN_TOTAL
						FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
						WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND C.STATECODE = 0
							AND  B.IFLOWID = AN_FLOWID;
							
							
							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID,FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME
						FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME
											FROM (
									SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
															LN_OPT_ID AS OPT_ID,
															C.IID,
															B.IFLOWID,
															C.IACTID,
															C.ITASKID,
															B.IFLOWINSNAME,
															C.IACTNAME,
															C.IDESC,
															C.ISHOULDSTARTTIME,
															C.IBEGINEXCTIME,
															C.ISTATE,
															(SELECT T1.IOWNER
																FROM IEAI_TASKRUNTIME T1
															WHERE T1.IID = C.ITASKID) AS IOWNER,
															T.INAME,
															C.IACTTYPE,
															B.ISTARTUSERFULLNAME,
															C.IERRORTASKID,
															C.IREXECREQUESTID,
															C.IACTDEFNAME,
															B.IFLOWNAME,
															C.ISHOULDENDTIME
														FROM IEAI_WORKFLOWINSTANCE B,
															IEAI_ACTRUNTIME       C,
															IEAI_PROJECT          T
													WHERE B.IPRJLATESTID = T.IID
														AND T.PROTYPE = 1
														AND B.ISTATUS = 0
														AND C.IFLOWID = B.IFLOWID
														AND C.DISAPPEAR = 0
														AND C.IISMONITORACT = 0
														AND C.ACTTYPECODE = 0
														AND C.STATECODE = 0
														AND  B.IFLOWID = AN_FLOWID
												) WWW
										) WW
							WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					ELSE 
								SELECT COUNT(1)
								INTO AN_TOTAL
								FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
								WHERE B.IPRJLATESTID = T.IID
								AND T.PROTYPE = 1
								AND B.ISTATUS = 0
								AND C.IFLOWID = B.IFLOWID
								AND C.DISAPPEAR = 0
								AND C.IISMONITORACT = 0
								AND C.ACTTYPECODE = 0
								AND T.INAME like '%'||AN_PRJNAME||'%'
								AND C.STATECODE = 0;
								
								INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
								SELECT WW.OPT_ID,
										WW.IID,
										WW.IFLOWID,
										WW.IACTID,
										WW.ITASKID,
										WW.IFLOWINSNAME,
										WW.IACTNAME,
										WW.IDESC,
										WW.ISHOULDSTARTTIME,
										WW.IBEGINEXCTIME,
										WW.ISTATE,
										WW.IOWNER,
										WW.INAME,
										WW.IACTTYPE,
										WW.ISTARTUSERFULLNAME,
										WW.IERRORTASKID,
										WW.IREXECREQUESTID,
										WW.IACTDEFNAME,
										WW.IFLOWNAME,
										WW.ISHOULDENDTIME
									FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME
																			FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
												LN_OPT_ID AS OPT_ID,
												C.IID,
												B.IFLOWID,
												C.IACTID,
												C.ITASKID,
												B.IFLOWINSNAME,
												C.IACTNAME,
												C.IDESC,
												C.ISHOULDSTARTTIME,
												C.IBEGINEXCTIME,
												C.ISTATE,
												(SELECT T1.IOWNER
													FROM IEAI_TASKRUNTIME T1
													WHERE T1.IID = C.ITASKID) AS IOWNER,
												T.INAME,
												C.IACTTYPE,
												B.ISTARTUSERFULLNAME,
												C.IERRORTASKID,
												C.IREXECREQUESTID,
												C.IACTDEFNAME,
												B.IFLOWNAME,
												C.ISHOULDENDTIME
											FROM IEAI_WORKFLOWINSTANCE B,
												IEAI_ACTRUNTIME       C,
												IEAI_PROJECT          T
											WHERE B.IPRJLATESTID = T.IID
											AND T.PROTYPE = 1
											AND B.ISTATUS = 0
											AND C.IFLOWID = B.IFLOWID
											AND C.DISAPPEAR = 0
											AND C.IISMONITORACT = 0
											AND C.ACTTYPECODE = 0
											AND T.INAME like '%'||AN_PRJNAME||'%'
											AND C.STATECODE = 0
											AND  B.IFLOWID = AN_FLOWID
										) WWW
										) WW
									WHERE WW.ROWN > AN_START
									AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					END IF;
			ELSE
				
				IF (AN_PRJNAME IS NULL) THEN 

				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
					SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
										AND T2.IPERMISSION = 1
										AND T.IUSERID = AN_USERID
										AND T2.IPROID > 0) A,
									IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
								AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
							IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;
				
				

					SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0
						 AND  B.IFLOWID = AN_FLOWID;

					INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
					SELECT WW.OPT_ID,
							WW.IID,
							WW.IFLOWID,
							WW.IACTID,
							WW.ITASKID,
							WW.IFLOWINSNAME,
							WW.IACTNAME,
							WW.IDESC,
							WW.ISHOULDSTARTTIME,
							WW.IBEGINEXCTIME,
							WW.ISTATE,
							WW.IOWNER,
							WW.INAME,
							WW.IACTTYPE,
							WW.ISTARTUSERFULLNAME,
							WW.IERRORTASKID,
							WW.IREXECREQUESTID,
							WW.IACTDEFNAME,
							WW.IFLOWNAME,
							WW.ISHOULDENDTIME
						FROM (SELECT WWW.ROWN,
								WWW.OPT_ID,
								WWW.IID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
							FROM IEAI_TASKRUNTIME T1
							WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME
					FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
					WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND C.STATECODE = 0
						 AND  B.IFLOWID = AN_FLOWID
						) WWW
					) WW
			WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				
			ELSE
			
				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
				SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
									AND T2.IPERMISSION = 1
									AND T.IUSERID = AN_USERID
									AND T2.IPROID > 0) A,
								IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
							AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
						IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;
				
				SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME like '%'||AN_PRJNAME||'%' AND C.STATECODE = 0
					 AND  B.IFLOWID = AN_OPT_ID;

				INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME)
				SELECT WW.OPT_ID,
						WW.IID,
						WW.IFLOWID,
						WW.IACTID,
						WW.ITASKID,
						WW.IFLOWINSNAME,
						WW.IACTNAME,
						WW.IDESC,
						WW.ISHOULDSTARTTIME,
						WW.IBEGINEXCTIME,
						WW.ISTATE,
						WW.IOWNER,
						WW.INAME,
						WW.IACTTYPE,
						WW.ISTARTUSERFULLNAME,
						WW.IERRORTASKID,
						WW.IREXECREQUESTID,
						WW.IACTDEFNAME,
						WW.IFLOWNAME,
						WW.ISHOULDENDTIME
					FROM (SELECT WWW.ROWN,
								WWW.IID,
								WWW.OPT_ID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
								LN_OPT_ID AS OPT_ID,
								C.IID,
								B.IFLOWID,
								C.IACTID,
								C.ITASKID,
								B.IFLOWINSNAME,
								C.IACTNAME,
								C.IDESC,
								C.ISHOULDSTARTTIME,
								C.IBEGINEXCTIME,
								C.ISTATE,
								(SELECT T1.IOWNER
									FROM IEAI_TASKRUNTIME T1
									WHERE T1.IID = C.ITASKID) AS IOWNER,
								T.INAME,
								C.IACTTYPE,
								B.ISTARTUSERFULLNAME,
								C.IERRORTASKID,
								C.IREXECREQUESTID,
								C.IACTDEFNAME,
								B.IFLOWNAME,
								C.ISHOULDENDTIME
							FROM TMP_USER_PRJ_VALIDATE PR,
								IEAI_WORKFLOWINSTANCE B,
								IEAI_ACTRUNTIME       C,
								IEAI_PROJECT          T
							WHERE PR.PRJID = T.IUPPERID
							AND B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME like '%'||AN_PRJNAME||'%'
							AND C.STATECODE = 0
							AND  B.IFLOWID = AN_FLOWID					
							) WWW
						) WW
					WHERE WW.ROWN > AN_START
					AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				END IF;
			END IF;
		END
		 $
		 
		 
		 
	CREATE OR REPLACE PROCEDURE PROC_WORKFLOW_QUERY_SDJOB   (IN AN_USERID NUMERIC(19,0),    OUT AN_OPT_ID NUMERIC(19,0),       IN AV_PRJ_NAME VARCHAR(255),    IN  AV_FLOW_NAME VARCHAR(255), 
												 IN AV_START_USER VARCHAR(255), IN  AV_INSTANCE_NAME VARCHAR(255), IN AI_START_TIME_FROM BIGINT, 
												 IN AI_START_TIME_TO BIGINT,    IN  AI_END_TIME_FROM BIGINT,       IN AI_END_TIME_TO BIGINT,       IN  AI_SHOW_ACTIVE SMALLINT, 
												 IN AI_SHOW_RUN_SELF SMALLINT,  IN  AI_NUM_OF_PAGE SMALLINT,       IN AI_PAGE_ID INTEGER,          OUT AI_REC_COUNT INTEGER, 
												 IN AV_ORDER_ITEM VARCHAR(80),  IN  AI_ORDER_TYPE SMALLINT,        IN AN_CHECK_DATE NUMERIC(19,0), IN  AV_HOST_NAME VARCHAR(50), 
												 IN AN_FLOW_ID NUMERIC(19,0))
	LANGUAGE SQL
	BEGIN
		DECLARE	LN_OPT_ID	NUMERIC(19,0);	
		
		DECLARE	LV_SQL		VARCHAR(4000);	
		DECLARE	LI_ZONE		SMALLINT;	
		DECLARE	LV_FORMAT	VARCHAR(50);	
		DECLARE	LV_ORDER_BY	VARCHAR(100);	
		
		DECLARE	LN_MILLISEC	NUMERIC(19);	
		DECLARE	LV_WHERE	VARCHAR(4000);	
		DECLARE	LI_PAGE_ID	INTEGER;	
		DECLARE	LI_MAX_RECID	INTEGER;	
		DECLARE	LI_MIN_RECID	INTEGER;	
		DECLARE	LI_MAX_PAGEID	INTEGER;	
		DECLARE	LI_PAGE_COUNT	INTEGER;	
		
		DECLARE	SI_TIMEZONE_CUR SMALLINT DEFAULT 8;	
		DECLARE	SI_TIMEZONE_GMT SMALLINT DEFAULT 0;	
		
		DECLARE	LI_RETURN	SMALLINT;
		
		DECLARE LN_ISALL    NUMERIC(2,0);

		DECLARE	LV_SQL_WHERE		VARCHAR(4000);	
			
		
		IF	AI_NUM_OF_PAGE IS NULL OR AI_NUM_OF_PAGE <= 0	THEN
			RETURN;
		END	IF;
			
		
		CALL	PROC_GET_NEXT_ID('TMP_USER_VALIDATE', LN_OPT_ID);
			
		SET	AN_OPT_ID = LN_OPT_ID;
		
		SELECT COUNT(*)  INTO LN_ISALL
		FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
		WHERE T.IROLEID = T2.IROLEID   
		  AND T2.IPERMISSION = 1
		  AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;   
			
		SET	LI_ZONE = 0;				
		SET	LV_FORMAT = 'YYYY-MM-DD HH24:MI:SS';	
			
		
		
		
		IF  LN_ISALL>0 THEN 
			INSERT	INTO	TMP_USER_VALIDATE
				(
					OPT_ID,
					PRJ_ID,
					PRJ_NAME,
					PERMIT_ID,
					PERMIT_NAME
				)
			SELECT LN_OPT_ID AS OPT_ID,IID AS PRJ_ID,INAME AS PRJ_NAME, 0 AS PERMIT_ID,1 AS PERMIT_NAME
			FROM IEAI_PROJECT 
			WHERE PROTYPE=1 
			  AND (IPKGCONTENTID<>0 OR IPKGCONTENTID IS NULL)  
			  AND IUPPERID=IID ;
		ELSE 
			INSERT	INTO	TMP_USER_VALIDATE
				(
					OPT_ID,
					PRJ_ID,
					PRJ_NAME,
					PERMIT_ID,
					PERMIT_NAME
				)
			SELECT LN_OPT_ID AS OPT_ID,PRJ.IID AS PRJ_ID,PRJ.INAME AS PRJ_NAME, 0 AS PERMIT_ID,1 AS PERMIT_NAME
			FROM IEAI_PROJECT PRJ,IEAI_SYS_PERMISSION ISP,IEAI_USERINHERIT IU
			WHERE IU.IUSERID=AN_USERID
			AND ISP.IROLEID=IU.IROLEID
			AND PRJ.IID=ISP.IPROID
			AND ISP.IPROID>0
			AND ISP.IPERMISSION=1
			AND PRJ.PROTYPE=1 AND (PRJ.IPKGCONTENTID<>0 OR PRJ.IPKGCONTENTID IS NULL);
		END IF;
		
		
			
		
		
		
			
		
		SET	LN_MILLISEC = AN_CHECK_DATE;
			
		
		SET	LV_WHERE = '';
			
		
		IF	LENGTH(AV_PRJ_NAME) > 0	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.IPROJECTNAME LIKE (' || CHR(39) || '%' || AV_PRJ_NAME || '%' || CHR(39) || ')';
		END	IF;
		
		
		IF	LENGTH(AV_FLOW_NAME) > 0	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWNAME LIKE (' || CHR(39) || '%' || AV_FLOW_NAME || '%' || CHR(39) || ')';
		END	IF;
		
		
		IF	LENGTH(AV_START_USER) > 0	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTUSERFULLNAME LIKE (' || CHR(39) || '%' || AV_START_USER || '%' || CHR(39) || ')';
		END	IF;
		
		
		IF	LENGTH(AV_INSTANCE_NAME) > 0	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWINSNAME LIKE (' || CHR(39) || '%' || AV_INSTANCE_NAME || '%' || CHR(39) || ')';
		END	IF;
		
		
		IF	AI_START_TIME_FROM > 0	THEN
			IF	AI_START_TIME_TO > 0	THEN
				SET	LV_WHERE = LV_WHERE || ' AND (C.ISTARTTIME BETWEEN ' || TO_CHAR(AI_START_TIME_FROM) || ' AND ' || TO_CHAR(AI_START_TIME_TO) || ')';
			ELSE
				SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTTIME >= ' || TO_CHAR(AI_START_TIME_FROM);
			END	IF;
		ELSE
			IF	AI_START_TIME_TO > 0	THEN
				SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTTIME <= ' || TO_CHAR(AI_START_TIME_TO);
			END	IF;	
		END	IF;
		
		
		IF	AI_END_TIME_FROM > 0	THEN
			IF	AI_END_TIME_TO > 0	THEN
				SET	LV_WHERE = LV_WHERE || ' AND (C.IENDTIME BETWEEN ' || TO_CHAR(AI_END_TIME_FROM) || ' AND ' || TO_CHAR(AI_END_TIME_TO) || ')';
			ELSE
				SET	LV_WHERE = LV_WHERE || ' AND C.IENDTIME >= ' || TO_CHAR(AI_END_TIME_FROM);
			END	IF;
		ELSE
			IF	AI_END_TIME_TO > 0	THEN
				SET	LV_WHERE = LV_WHERE || ' AND C.IENDTIME <= ' || TO_CHAR(AI_END_TIME_TO);
			END	IF;
		END	IF;
		
		
		IF	AI_SHOW_ACTIVE = 1	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.ISTATUS IN (0, 6, 8, 15, 30)';
			SET LV_SQL_WHERE = 'AND C.IFLOWID NOT IN (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 )) ';
		ELSE
			SET LV_SQL_WHERE = ' AND   C.IFLOWID NOT IN (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI) ';
		END	IF;
		
		
		IF	AI_SHOW_RUN_SELF = 1	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.IISAUTOSTART = 1';
		END	IF;
		
		
		IF	LENGTH(AV_HOST_NAME) > 0	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.IHOSTNAME LIKE (' || CHR(39) || '%' || AV_HOST_NAME || '%' || CHR(39) || ')';
		END	IF;
		
		
		IF	AN_FLOW_ID > 0	THEN
			SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWID = ' || TO_CHAR(AN_FLOW_ID);
		END	IF;
			
		
		DELETE	FROM	TMP_USER_INVALIDATE
			WHERE	OPT_ID = LN_OPT_ID;
			
		
		SET	LV_SQL = 'INSERT INTO TMP_USER_INVALIDATE (OPT_ID, PRJ_ID, PERMIT_ID) SELECT ' || LN_OPT_ID || ', 0, 1 FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C WHERE A.OPT_ID = ' || LN_OPT_ID || ' AND C.IPRJUPPERID = A.PRJ_ID   AND C.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT UV.PRJ_NAME FROM TMP_USER_VALIDATE UV) ) ' || LV_SQL_WHERE || LV_WHERE;
		PREPARE	SQLA FROM LV_SQL;
		EXECUTE SQLA;
		
		GET DIAGNOSTICS LI_MAX_RECID = ROW_COUNT;
		
		SET	LI_PAGE_COUNT	= TRUNC(LI_MAX_RECID / AI_NUM_OF_PAGE, 0);
		
		IF	MOD(LI_MAX_RECID, AI_NUM_OF_PAGE) > 0	THEN
			SET	LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
		END	IF;
		
		IF	AI_PAGE_ID > LI_PAGE_COUNT	THEN
			SET	LI_PAGE_ID = LI_PAGE_COUNT;
		ELSE
			SET	LI_PAGE_ID = AI_PAGE_ID;
		END	IF;
		
		IF	LI_PAGE_ID IS NULL OR LI_PAGE_ID < 0	THEN
			SET	LI_PAGE_ID = 1;
		END	IF;
		
		
		SET	AI_REC_COUNT = LI_MAX_RECID;
		
		
		SET	LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
		SET	LI_MAX_RECID = LI_PAGE_ID * AI_NUM_OF_PAGE;
		
		
		SET	LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, ROW_NUMBER()OVER() AS RID FROM (SELECT A.OPT_ID AS OPTID, ';
		IF	AI_ORDER_TYPE = 1	THEN
			SET	LV_ORDER_BY = ' ORDER BY 2 ASC';
		ELSE
			SET	LV_ORDER_BY = ' ORDER BY 2 DESC';
		END	IF;
		
		IF	UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'	THEN
			SET	LV_SQL = LV_SQL || 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'	THEN
			SET	LV_SQL = LV_SQL || 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'FLOW_ID'	THEN
			SET	LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'	THEN
			SET	LV_SQL = LV_SQL || 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'STATUS'	THEN
			SET	LV_SQL = LV_SQL || 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'START_USER'	THEN
			SET	LV_SQL = LV_SQL || 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'START_TIME'	THEN
			SET	LV_SQL = LV_SQL || 'C.ISTARTTIME AS ISTART_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'END_TIME'	THEN
			SET	LV_SQL = LV_SQL || 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'PAID_TIME'	THEN
			SET	LV_SQL = LV_SQL || '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30) THEN ' || TO_CHAR(LN_MILLISEC) || ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		ELSEIF	UPPER(AV_ORDER_ITEM) = 'TASK_NUM'	THEN
			SET	LV_SQL = LV_SQL || 'NVL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ';
		ELSE
			SET	LV_ORDER_BY = ' ORDER BY 2 DESC';
			SET	LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
		END	IF;
		
		SET	LV_SQL = LV_SQL || 'FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' || TO_CHAR(LN_OPT_ID) || ' AND C.IPRJUPPERID = A.PRJ_ID  AND C.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT UV.PRJ_NAME FROM TMP_USER_VALIDATE UV) ) ' || LV_SQL_WHERE;
		
		
		
		SET	LV_SQL = LV_SQL || LV_WHERE || LV_ORDER_BY || ')) WHERE RID BETWEEN ' || LI_MIN_RECID || ' AND ' || LI_MAX_RECID;
			
		
		PREPARE	SQLA FROM LV_SQL;
		EXECUTE SQLA;
		
		
		INSERT	INTO	TMP_WORKFLOW_QUERY
			(
				OPTID,
				FLOW_ID,
				FLOW_NAME,
				INSTANCE_NAME,
				PROJECT_NAME,
				STATUS,
				START_USER,
				START_TIME,
				END_TIME,
				PAID_TIME,
				HOST_NAME,
				TASK_NUM,
				REC_ID
			)
		SELECT	A.OPTID,
			A.FLOW_ID,
			B.IFLOWNAME,
			B.IFLOWINSNAME,
			B.IPROJECTNAME,
			LTRIM(TO_CHAR(B.ISTATUS)),
			B.ISTARTUSERFULLNAME,
			FUN_GET_DATE_STRING(B.ISTARTTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS'),
			(CASE WHEN B.IENDTIME > 0 THEN FUN_GET_DATE_STRING(B.IENDTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
			(
				CASE	WHEN	B.ISTATUS IN (0, 1, 6, 8, 10, 15, 30) THEN LTRIM(TO_CHAR(TRUNC((LN_MILLISEC - B.ISTARTTIME) / 1000, 0) * 1000))
					WHEN	B.ISTATUS IN (2, 4, 7) THEN LTRIM(TO_CHAR(TRUNC((B.IENDTIME - B.ISTARTTIME) / 1000, 0) * 1000))
					ELSE	'0'
				END
			),
			B.IHOSTNAME,
			A.TASK_NUM,
			A.REC_ID
		FROM	TMP_WORKFLOW_QUERY_TEMP A,
			IEAI_WORKFLOWINSTANCE B
		WHERE	A.OPTID = LN_OPT_ID
		AND	B.IFLOWID = A.FLOW_ID
		ORDER	BY A.REC_ID;
	END
		$

		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DELETING_FLOWID_TWO';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE  IEAI_DELETING_FLOWID_TWO (ID DECIMAL(19) NOT NULL , FLOWID DECIMAL(19) ,CONSTRAINT PK_DELETING_FLOWID_TWO PRIMARY KEY (ID))';
					PREPARE	SQLA FROM LS_SQL;
					EXECUTE SQLA;
			END IF;
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_CUSTOM_REPORT_INFO_LIST';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE  IEAI_CUSTOM_REPORT_INFO_LIST (IID DECIMAL(19,0) NOT NULL,  IDBID DECIMAL(19,0) NOT NULL,  IREPORTNAME VARCHAR(500) NOT NULL,  IREPORTTYPEID DECIMAL(19,0) NOT NULL,  ICREATE_USERID DECIMAL(19,0)  DEFAULT 4 NOT NULL   ,  ISQL_TEXT CLOB ,  ISTATUS SMALLINT  DEFAULT 0 NOT NULL  ,  ICOMMENT VARCHAR(500),  ICREATE_TIME TIMESTAMP NOT NULL,  ILAST_MODIFIED_TIME TIMESTAMP  DEFAULT CURRENT_TIMESTAMP  NOT NULL ,PRIMARY KEY (IID))';
					PREPARE	SQLA FROM LS_SQL;
					EXECUTE SQLA;
			END IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_WORKFLOWINSTANCE' AND COLNAME = 'ICLEARFLAG';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_WORKFLOWINSTANCE ADD ICLEARFLAG NUMERIC(1) NULL';
				   PREPARE	SQLA FROM LS_SQL; 
				   EXECUTE SQLA;
			END	IF;
			
			SELECT SIGN(COUNT(1)) INTO LI_EXISTS  FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_DELETING_FLOWID_TWO' AND INDNAME='IEAI_IDX_DELETINGTWO_FLOWID';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'create index IEAI_IDX_DELETINGTWO_FLOWID on IEAI_DELETING_FLOWID_TWO (FLOWID)';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			
			SELECT SIGN(COUNT(1)) INTO LI_EXISTS  FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CALLWORKFLOW_INFO' AND INDNAME='IDX_IEAI_CALLWORKFLOW_INFO';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'create index IDX_IEAI_CALLWORKFLOW_INFO on IEAI_CALLWORKFLOW_INFO (IMAINFLOWID)';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
			
		END
		$


		

		
		CREATE  OR REPLACE PROCEDURE PROC_CLEAR_DATA_JOB_TWO ()
			LANGUAGE SQL
		BEGIN
		  DECLARE LN_ID NUMERIC(19,0);
			DECLARE LN_FLOWID NUMERIC(19, 0);
			DECLARE LI_LOOP INTEGER DEFAULT	1; 
			DECLARE DONE DECIMAL(19,0);
			DECLARE	SQLCODE INTEGER	DEFAULT	0;
			DECLARE	RETSQLCODE INTEGER DEFAULT	0;
			DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND SET	RETSQLCODE = SQLCODE,DONE = 1;
		  BEGIN
		  -- 一次最多删除20个已经完成的工作流的相关信息
		  CHK_IP:
			WHILE (LI_LOOP <= 20) DO  
				-- 找出排在最前面的工作流注册标识
				SELECT  MIN(ID)
				INTO  LN_ID
				FROM  IEAI_DELETING_FLOWID_TWO ;
				
				-- 如果没有注册数据，则退出循环
			IF DONE = 1 THEN 
				LEAVE  CHK_IP;  --满足条件，终止循环，跳转到end outer_label标记
			END IF;
			
			
			
			
			-- 提取工作流标识
			SELECT  FLOWID
			INTO  LN_FLOWID
			FROM  IEAI_DELETING_FLOWID_TWO
			WHERE ID = LN_ID ;
			
			-- 清理注册表
			  DELETE FROM IEAI_DELETING_FLOWID_TWO WHERE FLOWID = LN_FLOWID;
			  
			  
				-- 删除数据
			  
				DELETE FROM IEAI_ACTRUNINFO  WHERE IFLOWID = LN_FLOWID;
			  
				DELETE FROM IEAI_ACTRUNTIME  WHERE IFLOWID = LN_FLOWID;
			  
				DELETE FROM IEAI_CALLWORKFLOW_INFO 
				 WHERE IMAINFLOWID = LN_FLOWID;
			  
				DELETE FROM IEAI_BLOB 
				 WHERE IID IN (SELECT B.IFLOWDATAID
								   FROM IEAI_BLOB A, IEAI_FLOWENV B
								  WHERE A.IID = B.IFLOWDATAID
									AND B.IFLOWID = LN_FLOWID);
			  
				DELETE FROM IEAI_FLOWENV  WHERE IFLOWID = LN_FLOWID;
			  
				DELETE FROM IEAI_BLOB 
				 WHERE IID IN (SELECT C.IACTOUTPUTID
								   FROM IEAI_BRANCHSCOPE B, IEAI_ACTOUTPUT C
								  WHERE B.ISCOPEID = C.IACTSCOPEID
									AND B.IFLOWID = LN_FLOWID);
			  
				DELETE FROM IEAI_ACTOUTPUT 
				 WHERE IACTSCOPEID IN
					   (SELECT B.ISCOPEID
						  FROM IEAI_BRANCHSCOPE B
						 WHERE B.IFLOWID = LN_FLOWID);
			  
				DELETE FROM IEAI_BRANCHSCOPE  WHERE IFLOWID = LN_FLOWID;
			  
				DELETE FROM IEAI_WORKFLOWINSTANCE  WHERE IFLOWID = LN_FLOWID;
			  
				
			   SET LI_LOOP = LI_LOOP + 1; 
			END WHILE CHK_IP; 
		  
		  
			 IF	RETSQLCODE = 0 OR RETSQLCODE = 100	THEN
							COMMIT	WORK;
					 ELSE
							ROLLBACK WORK;
							--继续尝试删除不能删除的登记表中的数据
							DELETE  FROM  IEAI_DELETING_FLOWID_TWO WHERE ID = LN_ID ;
							COMMIT	WORK;
					 END	IF;
					 COMMIT;
		END;
		END 
		$
		

		--4.7.26 
		
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
				 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_APP_JAIL' AND  COLNAME='ISTATICDAYS';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_APP_JAIL ADD ISTATICDAYS VARCHAR(255)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;  

			 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HAND_RULE' AND  COLNAME='ISTATICDAYS';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_HAND_RULE ADD ISTATICDAYS VARCHAR(255)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;
				
			 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_APP_JAIL' AND  COLNAME='ISNODATAFIRST';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_APP_JAIL ADD ISNODATAFIRST 	DECIMAL(8,0)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;  

			 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HAND_RULE' AND  COLNAME='ISNODATAFIRST';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_HAND_RULE ADD ISNODATAFIRST 	DECIMAL(8,0)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;

			END
	$
	
	--4.7.27
	
	BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROJECT_USER' AND COLNAME = 'IPROJECTTYPE';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_PROJECT_USER ADD IPROJECTTYPE NUMERIC(1)  DEFAULT 1';
				   PREPARE	SQLA FROM LS_SQL; 
				   EXECUTE SQLA;
			END	IF;
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROJECT_USER' AND COLNAME = 'ISENDTYPE';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE IEAI_PROJECT_USER ADD ISENDTYPE VARCHAR(30)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
            END	IF;
		END
		$
		
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1118 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1118, ''复核'', ''flowOrActDoubleCheck.do'', ''作业调度--工作流查询--复核'')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1118 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1118, 1003, 1118, '''')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;
			  
		SELECT  COUNT(*) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=1056;
		IF  LI_EXISTS = 0 THEN
			SET	LS_SQL ='INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1056, ''一键解析'', ''agentReportAnalyzeOneKey.do'', ''作业调度-配置-客户端维护报表-一键解析'')';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		ELSE
			SET	LS_SQL ='UPDATE IEAI_HIGHOPER SET IBUTTONURL=''agentReportAnalyzeOneKey.do'' WHERE IBUTTONID=1056 ';
			PREPARE	SQLA FROM LS_SQL; 
			EXECUTE SQLA;
		END  IF;
		END
		$

 			BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TRIGGERS WHERE TRIGNAME='TRA_U_EXECACT';
			IF	LI_EXISTS > 0 THEN
			SET	LS_SQL = 'DROP TRIGGER TRA_U_EXECACT';
				   PREPARE	SQLA FROM LS_SQL; 
				   EXECUTE SQLA;
			END	IF;
			
		END
		$
		
		
				BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TEMP_LOOKMONITOR_BASE' AND INDNAME='IDX_TEMP_LOOKMONITOR_BASE_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_01 ON TEMP_LOOKMONITOR_BASE (IPRJNAME)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TEMP_LOOKMONITOR_BASE' AND INDNAME='IDX_TEMP_LOOKMONITOR_BASE_02';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TEMP_LOOKMONITOR_BASE_02 ON TEMP_LOOKMONITOR_BASE (IID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTSUCC' AND INDNAME='IDX_IEAI_ACTSUCC_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_ACTSUCC_01 ON IEAI_ACTSUCC (IOPERATIONID,ISUCCACTNAME)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		 
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCHEDULER_ACT' AND INDNAME='IDX_IEAI_SCHEDULER_ACT_03';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_SCHEDULER_ACT_03 ON IEAI_SCHEDULER_ACT (IFLOWID,IACTNAME,IISSTATE)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;			 

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCHEDULER_ACT' AND INDNAME='IDX_IEAI_SCHEDULER_ACT_04';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_SCHEDULER_ACT_04 ON IEAI_SCHEDULER_ACT (IFLOWID,IPRENUM,IISSTATE)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SCHEDULER_ACT' AND INDNAME='IDX_IEAI_SCHEDULER_ACT_05';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_SCHEDULER_ACT_05 ON IEAI_SCHEDULER_ACT (IFLOWID,IFLOWINS,IPRENUM,IISSTATE,IOPERATIONID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_EXCELMODEL' AND INDNAME='IDX_IEAI_EXCELMODEL_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_EXCELMODEL_01 ON IEAI_EXCELMODEL (IACTNAME,IMAINPRONAME)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTRUNTIME' AND INDNAME='IDX_IEAI_ACTRUNTIME_08';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_ACTRUNTIME_08 ON IEAI_ACTRUNTIME(ISTATE)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTRUNTIME' AND INDNAME='IDX_IEAI_ACTRUNTIME_09';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_ACTRUNTIME_09 ON IEAI_ACTRUNTIME (IFLOWID,IACTNAME,ISTATE)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ODSACTFINISHED_FLAG' AND INDNAME='IDX_ODSACTFINISHED_FLAG_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_ODSACTFINISHED_FLAG_01 ON IEAI_ODSACTFINISHED_FLAG (IPRJID,IDATADATE,IPRONAME,IFLOWNAME,IACTNAME)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTFINISHED_FLAG_NEW' AND INDNAME='IDX_ACTFINISHED_FLAG_NEW_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_01 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IOPERATIONID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_KEYFLOW_INFO' AND INDNAME='IDX_IEAI_KEYFLOW_INFO_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_KEYFLOW_INFO_01 ON IEAI_KEYFLOW_INFO (IFLOWRUNOWNID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WORKFLOW' AND INDNAME='IDX_IEAI_WORKFLOW_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_WORKFLOW_01 ON IEAI_WORKFLOW (IFLOWID,IPRJID,ISTATUS,ILATESTID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;				 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TEMP_IEAI_REMOTEEXECACT' AND INDNAME='IDX_TEMP_IEAI_REMOTEEXECACT_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TEMP_IEAI_REMOTEEXECACT_01 ON TEMP_IEAI_REMOTEEXECACT (IFLOWID,IAGENTHOST,IAGENTPORT)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;			 

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_AGENTINFO' AND INDNAME='IDX_IEAI_AGENTINFO_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_AGENTINFO_01 ON IEAI_AGENTINFO (IAGENT_IP,IAGENT_PORT)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_FLOWDEF' AND INDNAME='IDX_IEAI_FLOWDEF_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_FLOWDEF_01 ON IEAI_FLOWDEF (IFLOWNAME)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_FLOWDEF' AND INDNAME='IDX_IEAI_FLOWDEF_02';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_FLOWDEF_02 ON IEAI_FLOWDEF (IPRJID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_SYS_PERMISSION' AND INDNAME='IDX_IEAI_SYS_PERMISSION_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_SYS_PERMISSION_01 ON IEAI_SYS_PERMISSION (IPROID,IPERMISSION,IROLEID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WORKFLOW_JOBNUM' AND INDNAME='IDX_IEAI_WORKFLOW_JOBNUM_01';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_WORKFLOW_JOBNUM_01 ON IEAI_WORKFLOW_JOBNUM (IDATADATE,IPRJNAME,IQUERY)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WORKFLOW_JOBNUM' AND INDNAME='IDX_IEAI_WORKFLOW_JOBNUM_02';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_WORKFLOW_JOBNUM_02 ON IEAI_WORKFLOW_JOBNUM (IQUERY)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WORKFLOW' AND INDNAME='IDX_IEAI_WORKFLOW_03';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_IEAI_WORKFLOW_03 ON IEAI_WORKFLOW (IPRJID,ISTATUS)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WORKFLOWINSTANCE' AND INDNAME='IDX_WORKFLOWINSTANCE_06';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_WORKFLOWINSTANCE_06 ON IEAI_WORKFLOWINSTANCE (IPROJECTNAME,IFLOWNAME,IFLOWINSNAME,IFLOWID,ISTATUS)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;		
		 	
	END
	$
	
	
	
BEGIN
  DECLARE   LS_SQL VARCHAR(2000);
  DECLARE   LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_EXEC_ACTNEXTINFO_NEW';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL = 'DROP TABLE TMP_EXEC_ACTNEXTINFO_NEW';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_QUERY_ACTNEXTINFO_NEW';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL = 'DROP TABLE TMP_QUERY_ACTNEXTINFO_NEW';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_MUTEX_INFO_NEW';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL = 'DROP TABLE TMP_MUTEX_INFO_NEW';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
		
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_EXECACT_NEW';
	IF LI_EXISTS = 1 THEN
		SET LS_SQL = 'DROP TABLE TMP_EXECACT_NEW';
		PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
END
   $

BEGIN
  DECLARE   LS_SQL VARCHAR(2000);
  DECLARE   LI_EXISTS  NUMERIC(2);
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_EXEC_ACTNEXTINFO_NEW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_EXEC_ACTNEXTINFO_NEW( IPRJID DECIMAL(19), IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255),IPROJECTNAME VARCHAR(255), ISYSTEM VARCHAR(255),IPRJUUID VARCHAR(255),IFLOWNAME VARCHAR(255), IACTNAME VARCHAR(255),IPRENUM DECIMAL(19), ISERVERIP VARCHAR(255),IUUID VARCHAR(255)) ';
    PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_QUERY_ACTNEXTINFO_NEW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_QUERY_ACTNEXTINFO_NEW(IPRJID DECIMAL(19), IFLOWID DECIMAL(19), IFLOWINS VARCHAR(255), IPROJECTNAME VARCHAR(255), ISYSTEM VARCHAR(255), IPRJUUID VARCHAR(255),IFLOWNAME VARCHAR(255),IACTNAME VARCHAR(255),IPRENUM DECIMAL(19),ISERVERIP VARCHAR(255),IUUID VARCHAR(255)) ';
    PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_MUTEX_INFO_NEW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_MUTEX_INFO_NEW(IFLOWID DECIMAL(19),IFLOWINS VARCHAR(255),IACTNAME VARCHAR(255),IMUTEXRULEID DECIMAL(19),ISYSTEM VARCHAR(255),IUUID VARCHAR(255)) ';
    PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
	
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_EXECACT_NEW';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_EXECACT_NEW(IID  DECIMAL(19) NOT NULL,IDELAYTO  DECIMAL(19),ITRRAISEEXCEPTION  DECIMAL(1),ITRSTATE DECIMAL(10),ITRSTRUCTOUTPUTSAVED DECIMAL(1),IACTID  DECIMAL(19),IACTNAME  VARCHAR(255),IFLAG DECIMAL(10),IFLOWID  DECIMAL(19),IISFORRECOVERY DECIMAL(1),IISINTRANSACTION DECIMAL(1),IISSAFEFIRST  DECIMAL(1),IISTIMEOUT DECIMAL(1),IREXECREQUESTID VARCHAR(255),IRUNINGPOSITION DECIMAL(10),ISCOPEID  DECIMAL(19),ISTATE DECIMAL(10),ITIMEOUTTIMES DECIMAL(10),ISTARTERRUNTIMES DECIMAL(10),ISEXCEPTED DECIMAL(1),IUUID VARCHAR(255)) ';
    PREPARE	SQLA FROM LS_SQL; 
		EXECUTE SQLA;
	END IF;
END
   $
   
   	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TMP_EXECACT_NEW' AND INDNAME='IDX_TMP_EXECACT_NEW';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TMP_EXECACT_NEW ON TMP_EXECACT_NEW (IUUID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TMP_EXEC_ACTNEXTINFO_NEW' AND INDNAME='IDX_TMP_EXEC_ACTNEXTINFO_NEW';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TMP_EXEC_ACTNEXTINFO_NEW ON TMP_EXEC_ACTNEXTINFO_NEW (IUUID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;
		 		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TMP_QUERY_ACTNEXTINFO_NEW' AND INDNAME='IDX_TMP_QUERY_ACTNEXTINFO_NEW';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TMP_QUERY_ACTNEXTINFO_NEW ON TMP_QUERY_ACTNEXTINFO_NEW (IUUID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;
		 
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='TMP_MUTEX_INFO_NEW' AND INDNAME='IDX_TMP_MUTEX_INFO_NEW';
			IF	LI_EXISTS = 0 THEN
				SET LS_SQL  = 'CREATE INDEX IDX_TMP_MUTEX_INFO_NEW ON TMP_MUTEX_INFO_NEW (IUUID)';
				PREPARE	SQLA FROM LS_SQL; 
				 EXECUTE SQLA;
		 END IF;
		 
		 
	END
	$
	
	 CREATE OR REPLACE PROCEDURE PROC_ACTNEXTINFO_QUERY_NEW_S (IN AV_IIP VARCHAR(255))
		LANGUAGE SQL
		BEGIN
			begin
						--将可运行活动放入活动查询临时表
						INSERT INTO TMP_QUERY_ACTNEXTINFO
								(SELECT BB.IPRJID,
												BB.IFLOWID,
												BB.IFLOWINS,
												AA.IPROJECTNAME,
												AA.ISYSTEM,
												AA.IPRJUUID,
												AA.IFLOWNAME,
												BB.IACTNAME,
												BB.IPRENUM,
												BB.ISERVERIP
									FROM V_FINISHINFO AA, IEAI_SCHEDULER_ACT BB
									WHERE AA.IOPERATIONID = BB.IOPERATIONID
										AND AA.NUM = BB.IPRENUM
										AND AA.IFLOWINS = BB.IFLOWINS
										AND AA.IFLOWID = BB.IFLOWID
										AND BB.IISSTATE IN (0,1)
										AND BB.ISERVERIP=AV_IIP
								UNION ALL
								SELECT T4.IPRJID,
												T4.IFLOWID,
												T4.IFLOWINS,
												T5.IPROJECTNAME,
												T5.ISYSTEM,
												T5.IPRJUUID,
												T5.IFLOWNAME,
												T4.IACTNAME,
												T4.IPRENUM,
												T4.ISERVERIP
									FROM IEAI_SCHEDULER_ACT T4, IEAI_WORKFLOWINSTANCE T5
									WHERE T4.IFLOWID = T5.IFLOWID
										AND T4.IISSTATE IN (0,1)
										AND T4.IPRENUM = 0
										AND T4.ISERVERIP =AV_IIP
										AND T5.ISTATUS IN (0, 30, 40, 46, 47));
						--没有配置互斥关系的作业存储到活动执行临时表
						INSERT INTO TMP_EXEC_ACTNEXTINFO
								(SELECT T.IPRJID,
												T.IFLOWID,
												T.IFLOWINS,
												T.IPROJECTNAME,
												T.ISYSTEM,
												T.IPRJUUID,
												T.IFLOWNAME,
												T.IACTNAME,
												T.IPRENUM,
												T.ISERVERIP
									FROM (SELECT T8.ISYSTEM, T8.IACTNAME
													FROM IEAI_EXCELMODEL T8
													WHERE T8.IOPERATIONID IN
																(SELECT DISTINCT TTT.IOPERATIONID
																	FROM (SELECT T11.IOPERATIONID
																					FROM TMP_QUERY_ACTNEXTINFO T, IEAI_EXCELMODEL T11
																					WHERE T.ISYSTEM = T11.ISYSTEM
																						AND T.IACTNAME = T11.IACTNAME
																						AND T.ISERVERIP=AV_IIP) TTT
																	WHERE NOT EXISTS
																	(SELECT *
																					FROM IEAI_MUTEX_ACT TT1
																					WHERE TT1.IOPERATIONID = TTT.IOPERATIONID))) T10,
												TMP_QUERY_ACTNEXTINFO T
									WHERE T10.ISYSTEM = T.ISYSTEM
										AND T10.IACTNAME = T.IACTNAME
										 AND T.ISERVERIP=AV_IIP);
						
				end;
		END
			 $
			 
			 
			 CREATE OR REPLACE PROCEDURE PROC_RUN_ACTINFO_NEW(IN AV_PRJ_NAME VARCHAR(255),
                                             IN AV_FLOW_NAME VARCHAR(255),
                                             IN AV_ACT_NAME VARCHAR(255),
                                             IN AV_FLOW_ID NUMERIC(19,0),
                                             IN AV_PRJ_UUID VARCHAR(255),
                                             IN AV_UUIDS  VARCHAR(255),
                                             IN AV_IFLAG   NUMERIC(19,0))
    /*
    
        AV_PRJ_NAME    <IN>  工程名
        AV_FLOW_NAME   <IN>  工作流名
        AV_ACT_NAME    <IN>  活动名
        AV_FLOW_ID     <IN>  工作流ID
        AV_PRJ_UUID    <IN>  工程UUID
    */
	BEGIN
		DECLARE LN_EXEC_ID  NUMERIC(19, 0); --活动驱动表(ieai_execact)主键
			DECLARE LN_COUNT    NUMERIC(19, 0);
			DECLARE LN_COUNTRUN NUMERIC(19, 0);
			DECLARE LN_ACTID    NUMERIC(19, 0);
			DECLARE LN_ZICOUNT  NUMERIC(19, 0);
			DECLARE LV_SQL      VARCHAR(4000);
			DECLARE LI_RETURN   NUMERIC;
		
		BEGIN
			CALL PROC_GET_NEXT_ID ('com.ideal.ieai.server.repository.engine.RepExecAct', LN_EXEC_ID);
			SET LN_COUNTRUN = 0;
			SELECT COUNT(*)
			INTO LN_COUNT
			FROM (SELECT *
					FROM IEAI_ACTPRE GY
					WHERE GY.IOPERATIONID =
						(SELECT GJ.IOPERATIONID
							FROM IEAI_EXCELMODEL GJ
							WHERE GJ.IACTNAME = AV_ACT_NAME
							AND GJ.IMAINLINENAME = AV_FLOW_NAME
							AND GJ.IMAINPRONAME = AV_PRJ_NAME)) AA
			WHERE AA.IPROJECTNAME = AV_PRJ_NAME
			AND AA.IMAINLINENAME = AV_FLOW_NAME;
		
		SET LN_ZICOUNT = 0;
		  IF AV_IFLAG = 1 THEN
			SELECT COUNT(TT.IMUTEXRULEID)
			INTO LN_ZICOUNT
			FROM IEAI_MUTEX_RULE TT
			WHERE TT.IMUTEXRULEID IN
				(SELECT T1.IMUTEXRULEID
					FROM IEAI_MUTEX_ACT T1
					WHERE T1.IOPERATIONID IN
						(SELECT T5.IOPERATIONID
							FROM IEAI_EXCELMODEL T5
							WHERE T5.IACTNAME = AV_ACT_NAME
							AND T5.ISYSTEM = (SELECT T.ISYSTEM
												FROM IEAI_EXCELMODEL T
												WHERE T.IMAINPRONAME = AV_PRJ_NAME
													AND T.IACTNAME = AV_ACT_NAME)))
			AND TT.IRULETYPE = 3;
			END IF;
			
			IF LN_ZICOUNT > 0 THEN
			
				SELECT COUNT(TT.IACTNAME)
				INTO LN_COUNTRUN
				--   TT.ISYSTEM,
				--   TT.IOPERATIONID,
				--  tb.iflowid,
				-- tc.istate
				FROM (SELECT T6.IACTNAME, T6.ISYSTEM, T6.IOPERATIONID
						FROM IEAI_EXCELMODEL T6
						WHERE T6.IOPERATIONID IN
							(SELECT T2.IOPERATIONID
								FROM IEAI_MUTEX_ACT T2
								WHERE T2.IMUTEXRULEID IN
									(SELECT T1.IMUTEXRULEID
										FROM IEAI_MUTEX_ACT T1
										WHERE T1.IOPERATIONID IN
											(SELECT T5.IOPERATIONID
												FROM IEAI_EXCELMODEL T5
												WHERE T5.IACTNAME = AV_ACT_NAME
												AND T5.ISYSTEM =
													(SELECT T.ISYSTEM
														FROM IEAI_EXCELMODEL T
														WHERE T.IMAINPRONAME = AV_PRJ_NAME
														AND T.IACTNAME = AV_ACT_NAME))))) TT,
					IEAI_SCHEDULER_ACT TT1,
					IEAI_WORKFLOWINSTANCE TB,
					IEAI_ACTRUNTIME TC
				WHERE TT1.IISSTATE = 2
				AND TT1.IFLOWID = TB.IFLOWID
				AND TB.ISTATUS IN (15, 0, 8, 30, 46, 47, 40)
				AND TT1.IACTNAME = TT.IACTNAME
				AND TB.IFLOWID = TC.IFLOWID
				AND TC.IACTNAME = TT1.IACTNAME
				AND TC.ISTATE NOT IN ('Finished', 'Skipped', 'ManualFinish', 'Disable');
			
				IF LN_COUNTRUN = 0 THEN
					SELECT COUNT(TT.IACTNAME)
					INTO LN_COUNTRUN
					FROM (SELECT T6.IACTNAME, T6.ISYSTEM, T6.IOPERATIONID
							FROM IEAI_EXCELMODEL T6
							WHERE T6.IOPERATIONID IN
								(SELECT T2.IOPERATIONID
									FROM IEAI_MUTEX_ACT T2
									WHERE T2.IMUTEXRULEID IN
										(SELECT T1.IMUTEXRULEID
											FROM IEAI_MUTEX_ACT T1
											WHERE T1.IOPERATIONID IN
												(SELECT T5.IOPERATIONID
													FROM IEAI_EXCELMODEL T5
													WHERE T5.IACTNAME = AV_ACT_NAME
													AND T5.ISYSTEM =
														(SELECT T.ISYSTEM
															FROM IEAI_EXCELMODEL T
															WHERE T.IMAINPRONAME = AV_PRJ_NAME
															AND T.IACTNAME = AV_ACT_NAME))))) TT,
						IEAI_WORKFLOWINSTANCE TTT
					WHERE TT. IACTNAME = TTT.IFLOWNAME
					AND TT.ISYSTEM = TTT.ISYSTEM
					AND TTT.ISTATUS IN (15, 0, 8, 30, 46, 47, 40);
				
				END IF;
			
			END IF;
			IF LN_COUNTRUN = 0 THEN
			
				SELECT COUNT(TT.IACTNAME)
				INTO LN_COUNTRUN
				--   TT.ISYSTEM,
				--   TT.IOPERATIONID,
				--   tb.iflowid,
				--   tc.istate
				FROM (SELECT T6.IACTNAME, T6.ISYSTEM, T6.IOPERATIONID
						FROM IEAI_EXCELMODEL T6
						WHERE T6.IOPERATIONID IN
							(SELECT T2.IOPERATIONID
								FROM IEAI_MUTEX_ACT T2
								WHERE T2.IMUTEXRULEID IN
									(SELECT T1.IMUTEXRULEID
										FROM IEAI_MUTEX_ACT T1
										WHERE T1.IOPERATIONID IN
											(SELECT T5.IOPERATIONID
												FROM IEAI_EXCELMODEL T5
												WHERE T5.IACTNAME = AV_ACT_NAME
												AND T5.ISYSTEM =
													(SELECT T.ISYSTEM
														FROM IEAI_EXCELMODEL T
														WHERE T.IMAINPRONAME = AV_PRJ_NAME
														AND T.IACTNAME = AV_ACT_NAME)))
								AND T2.IOPERATIONID NOT IN
									(SELECT T5.IOPERATIONID
										FROM IEAI_EXCELMODEL T5
										WHERE T5.IACTNAME = AV_ACT_NAME
										AND T5.ISYSTEM =
											(SELECT T.ISYSTEM
												FROM IEAI_EXCELMODEL T
												WHERE T.IMAINPRONAME = AV_PRJ_NAME
												AND T.IACTNAME = AV_ACT_NAME))
								
								)) TT,
					IEAI_SCHEDULER_ACT TT1,
					IEAI_WORKFLOWINSTANCE TB,
					IEAI_ACTRUNTIME TC
				WHERE TT1.IISSTATE = 2
				AND TT1.IFLOWID = TB.IFLOWID
				AND TB.ISTATUS IN (15, 0, 8, 30, 46, 47, 40)
				AND TT1.IACTNAME = TT.IACTNAME
				AND TB.IFLOWID = TC.IFLOWID
				AND TC.IACTNAME = TT1.IACTNAME
				AND TC.ISTATE NOT IN ('Finished', 'Skipped', 'ManualFinish', 'Disable');
			
				IF LN_COUNTRUN = 0 THEN
				
					SELECT COUNT(TT.IACTNAME)
					INTO LN_COUNTRUN
					FROM (SELECT T6.IACTNAME, T6.ISYSTEM, T6.IOPERATIONID
							FROM IEAI_EXCELMODEL T6
							WHERE T6.IOPERATIONID IN
								(SELECT T2.IOPERATIONID
									FROM IEAI_MUTEX_ACT T2
									WHERE T2.IMUTEXRULEID IN
										(SELECT T1.IMUTEXRULEID
											FROM IEAI_MUTEX_ACT T1
											WHERE T1.IOPERATIONID IN
												(SELECT T5.IOPERATIONID
													FROM IEAI_EXCELMODEL T5
													WHERE T5.IACTNAME = AV_ACT_NAME
													AND T5.ISYSTEM =
														(SELECT T.ISYSTEM
															FROM IEAI_EXCELMODEL T
															WHERE T.IMAINPRONAME = AV_PRJ_NAME
															AND T.IACTNAME = AV_ACT_NAME)))
										
									AND T2.IOPERATIONID NOT IN
										(SELECT T5.IOPERATIONID
											FROM IEAI_EXCELMODEL T5
											WHERE T5.IACTNAME = AV_ACT_NAME
											AND T5.ISYSTEM =
												(SELECT T.ISYSTEM
													FROM IEAI_EXCELMODEL T
													WHERE T.IMAINPRONAME = AV_PRJ_NAME
													AND T.IACTNAME = AV_ACT_NAME))
									
									)) TT,
						IEAI_WORKFLOWINSTANCE TTT
					WHERE TT. IACTNAME = TTT.IFLOWNAME
					AND TT.ISYSTEM = TTT.ISYSTEM
					AND TTT.ISTATUS IN (15, 0, 8, 30, 46, 47, 40);
				
				END IF;
			
			END IF;
		
			IF LN_COUNTRUN > 0 THEN
				RETURN;
			ELSE
				SELECT IACTID
				INTO LN_ACTID
				FROM IEAI_ACTRUNTIME
				WHERE IACTNAME = AV_ACT_NAME
				AND IFLOWID = AV_FLOW_ID;
				INSERT INTO TMP_EXECACT_NEW
					(SELECT * FROM (
                        SELECT IID,
                                        IDELAYTO,
                                        ITRRAISEEXCEPTION,
                                        ITRSTATE,
                                        ITRSTRUCTOUTPUTSAVED,
                                        ACTID,
                                        ACTNAME,
                                        IFLAG,
                                        IFLOWID,
                                        IISFORRECOVERY,
                                        IISINTRANSACTION,
                                        IISSAFEFIRST,
                                        IISTIMEOUT,
                                        IREXECREQUESTID,
                                        IRUNINGPOSITION,
                                        ISCOPEID,
                                        ISTATE,
                                        ITIMEOUTTIMES,
                                        ISTARTERRUNTIMES,
                                        ISEXCEPTED,
                                        AV_UUIDS
                        FROM (SELECT IID,
                                        IDELAYTO,
                                        ITRRAISEEXCEPTION,
                                        ITRSTATE,
                                        ITRSTRUCTOUTPUTSAVED,
                                        LN_ACTID as ACTID,
                                        AV_ACT_NAME as ACTNAME,
                                        IFLAG,
                                        IFLOWID,
                                        IISFORRECOVERY,
                                        IISINTRANSACTION,
                                        IISSAFEFIRST,
                                        IISTIMEOUT,
                                        IREXECREQUESTID,
                                        IRUNINGPOSITION,
                                        ISCOPEID,
                                        ISTATE,
                                        ITIMEOUTTIMES,
                                        ISTARTERRUNTIMES,
                                        ISEXCEPTED,
                                        row_number() over() AS RN
                                FROM IEAI_EXECACT
                                WHERE IFLOWID = AV_FLOW_ID
                                    AND IACTID =
                                        (SELECT YH.IACTID
                                        FROM IEAI_ACTRUNTIME YH
                                        WHERE YH.IACTNAME = (SELECT IPREACTNAME
                                                                FROM (SELECT TP.IACTNAME IPREACTNAME,row_number() over() AS ROWNUM
                                                                        FROM IEAI_ACTRUNTIME TP
                                                                        WHERE TP.IACTNAME IN
                                                                            (SELECT GY.IPREACTNAME
                                                                                FROM IEAI_ACTPRE GY
                                                                                WHERE GY.IOPERATIONID =
                                                                                    (SELECT GJ.IOPERATIONID
                                                                                        FROM IEAI_EXCELMODEL GJ
                                                                                        WHERE GJ.IACTNAME =
                                                                                            AV_ACT_NAME
                                                                                        AND GJ.IMAINLINENAME =
                                                                                            AV_FLOW_NAME
                                                                                        AND GJ.IMAINPRONAME =
                                                                                            AV_PRJ_NAME)
                                                                                AND GY.IMAINLINENAME =
                                                                                    AV_FLOW_NAME)
                                                                        AND TP.IFLOWID = AV_FLOW_ID
                                                                        AND TP.IENDTIME > 0) TC
                                                                WHERE TC.ROWNUM = 1)
                                            AND YH.IFLOWID = AV_FLOW_ID)
                                ORDER BY IID DESC) TX
                        WHERE TX.RN = 1)
                        );
				IF LN_COUNT = 0 THEN
					SELECT IACTID
					INTO LN_ACTID
					FROM IEAI_ACTRUNTIME
					WHERE IACTNAME = AV_ACT_NAME
					AND IFLOWID = AV_FLOW_ID;
					INSERT INTO TMP_EXECACT_NEW
						(SELECT IID,
								IDELAYTO,
								ITRRAISEEXCEPTION,
								ITRSTATE,
								ITRSTRUCTOUTPUTSAVED,
								LN_ACTID,
								AV_ACT_NAME,
								IFLAG,
								IFLOWID,
								IISFORRECOVERY,
								IISINTRANSACTION,
								IISSAFEFIRST,
								IISTIMEOUT,
								IREXECREQUESTID,
								IRUNINGPOSITION,
								ISCOPEID,
								ISTATE,
								ITIMEOUTTIMES,
								ISTARTERRUNTIMES,
								0,
								AV_UUIDS
						FROM IEAI_EXECACT
						WHERE IFLOWID = AV_FLOW_ID
							AND IACTID = 0);
				
					-- UPDATE TMP_EXECACT
					--   SET IACTNAME = AV_ACT_NAME, IACTID = LN_ACTID,IID=LN_EXEC_ID
					-- WHERE IFLOWID = AV_FLOW_ID;
					INSERT INTO IEAI_EXECACT
						(SELECT LN_EXEC_ID,
								IDELAYTO,
								ITRRAISEEXCEPTION,
								ITRSTATE,
								ITRSTRUCTOUTPUTSAVED,
								IACTID,
								IACTNAME,
								IFLAG,
								IFLOWID,
								IISFORRECOVERY,
								IISINTRANSACTION,
								IISSAFEFIRST,
								IISTIMEOUT,
								IREXECREQUESTID,
								100,
								ISCOPEID,
								5,
								ITIMEOUTTIMES,
								ISTARTERRUNTIMES,
								0,
								1
						FROM TMP_EXECACT_NEW  WHERE IUUID=AV_UUIDS);
					INSERT INTO IEAI_ACTSTATEDATA
						(IID, IBYTEDATAID, IINTDATA, ILONGDATA)
					VALUES
						(LN_EXEC_ID, -1, -1, -1);
				
					UPDATE IEAI_SCHEDULER_ACT TS
					SET TS.IISSTATE = 2
					WHERE TS.IACTNAME = AV_ACT_NAME
					AND TS.IFLOWID = AV_FLOW_ID;
				ELSE
				
					--UPDATE TMP_EXECACT
					--   SET IACTNAME = AV_ACT_NAME, IACTID = LN_ACTID, IID = LN_EXEC_ID
					-- WHERE IFLOWID = AV_FLOW_ID;
					INSERT INTO IEAI_EXECACT
						(SELECT LN_EXEC_ID,
								IDELAYTO,
								ITRRAISEEXCEPTION,
								ITRSTATE,
								ITRSTRUCTOUTPUTSAVED,
								IACTID,
								IACTNAME,
								IFLAG,
								IFLOWID,
								IISFORRECOVERY,
								IISINTRANSACTION,
								IISSAFEFIRST,
								IISTIMEOUT,
								IREXECREQUESTID,
								100,
								ISCOPEID,
								5,
								ITIMEOUTTIMES,
								ISTARTERRUNTIMES,
								0,
								1
						FROM TMP_EXECACT_NEW  WHERE IUUID=AV_UUIDS);
					INSERT INTO IEAI_ACTSTATEDATA
						(IID, IBYTEDATAID, IINTDATA, ILONGDATA)
					VALUES
						(LN_EXEC_ID, -1, -1, -1);
				
					UPDATE IEAI_SCHEDULER_ACT TS
					SET TS.IISSTATE = 2
					WHERE TS.IACTNAME = AV_ACT_NAME
					AND TS.IFLOWID = AV_FLOW_ID;
				END IF;
			END IF;
		END;
	END 
		 $

		 BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ACT_TIMEOUTCONFIG' AND  COLNAME='ITHRESHOLD';
				IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'ALTER TABLE  IEAI_ACT_TIMEOUTCONFIG ADD ITHRESHOLD DECIMAL(19,0)';
					   PREPARE	SQLA FROM LS_SQL; 
					   EXECUTE SQLA;
				END	IF;
			
		END
			 $
			 
		BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_CALLWORKFLOW_INFO' AND INDNAME='IDX_IEAI_CALLWORKFLOW_INFO_02';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_IEAI_CALLWORKFLOW_INFO_02 ON IEAI_CALLWORKFLOW_INFO (ICALLFLOWID)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
				
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_ACTFINISHED_FLAG_NEW' AND INDNAME='IDX_ACTFINISHED_FLAG_NEW_02';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_ACTFINISHED_FLAG_NEW_02 ON IEAI_ACTFINISHED_FLAG_NEW (IDATADATE,IPRONAME,IFLOWNAME,IACTNAME)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		
		END
		$

		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID=1079;
			IF  LI_EXISTS > 0 THEN
				SET LS_SQL ='DELETE FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID=1079';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;   
			END  IF;
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID=1080;
			IF  LI_EXISTS > 0 THEN
				SET LS_SQL ='DELETE FROM IEAI_HIGHOPER_PERMISSION WHERE IMENUBUTTONID=1080';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;   
			END  IF;
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID=1080 AND IMENUID=1074;
			IF  LI_EXISTS = 1 THEN
				SET LS_SQL ='DELETE FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID=1080';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;   
			END  IF;
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID=1079 AND IMENUID=1074;
			IF  LI_EXISTS = 1 THEN
				SET LS_SQL ='DELETE FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID=1079';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;    
			END  IF;
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=1079;
			IF  LI_EXISTS = 1 THEN
				SET LS_SQL ='DELETE FROM IEAI_HIGHOPER WHERE IBUTTONID=1079';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;    
			END  IF;
			SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID=1080;
			IF  LI_EXISTS = 1 THEN
				SET LS_SQL ='DELETE FROM IEAI_HIGHOPER WHERE IBUTTONID=1080';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;   
			END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1119 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1119, ''异常操作'',''jobSchedulingRepeatOds.do'', ''作业调度-ODS活动监控-shellcmd异常(重试|略过|继续执行|终止工作流)'')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1119 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1119, 1074, 1119, '''')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1120 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1120, ''执行'',''utRecordOptionForOdsJob.do'', ''作业调度-ODS活动监控-UT执行操作'')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1120 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1120, 1074, 1120, '''')';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			  END	IF;

		END
		$
			 
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.VIEWS WHERE VIEWNAME='V_FINISHINFO';
				IF LI_EXISTS = 1 THEN
				 SET LS_SQL ='DROP VIEW V_FINISHINFO';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				
				
				END IF;
				
				 SET LS_SQL ='CREATE VIEW V_FINISHINFO AS SELECT NUM, IOPERATIONID, IFLOWINS, A.IFLOWID, IPROJECTNAME, ISYSTEM, IPRJUUID, IFLOWNAME FROM IEAI_WORKFLOWINSTANCE A,(SELECT  COUNT(1) AS NUM, TR.IOPERATIONID, TR.IFLOWINS, TR.IFLOWID FROM IEAI_WORKFLOWINSTANCE TY, IEAI_ACTFINISHED_FLAG_NEW T3, IEAI_SCHEDULER_ACT TR WHERE TR.IFLOWINS = T3.IDATADATE AND TR.IOPERATIONID = T3.IOPERATIONID AND TR.IFLOWID = TY.IFLOWID AND TY.ISTATUS IN (0, 30, 40, 46, 47) AND TR.IISSTATE in (0,1) GROUP BY TR.IFLOWINS, TR.IOPERATIONID, TR.IFLOWID) B WHERE A.IFLOWID = B.IFLOWID';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.VIEWS WHERE VIEWNAME='V_FINISHINFO_STATE';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE VIEW V_FINISHINFO_STATE AS SELECT NUM, IOPERATIONID, IFLOWINS, A.IFLOWID, IPROJECTNAME, ISYSTEM, IPRJUUID, IFLOWNAME,IACTNAME FROM IEAI_WORKFLOWINSTANCE A,(SELECT  COUNT(1) AS NUM, TR.IOPERATIONID, TR.IFLOWINS,TR.IACTNAME, TR.IFLOWID FROM IEAI_WORKFLOWINSTANCE TY, IEAI_ACTFINISHED_FLAG_NEW T3, IEAI_SCHEDULER_ACT TR WHERE TR.IFLOWINS = T3.IDATADATE AND TR.IOPERATIONID = T3.IOPERATIONID AND TR.IFLOWID = TY.IFLOWID AND TY.ISTATUS IN (0, 30, 40, 46, 47) AND TR.IISSTATE in(0,1) GROUP BY TR.IFLOWINS, TR.IOPERATIONID,TR.IACTNAME, TR.IFLOWID) B WHERE A.IFLOWID = B.IFLOWID';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
				
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TRIGGERS WHERE TRIGNAME='TRA_I_ACTRUNINFO';
				IF	LI_EXISTS > 0 THEN
				SET	LS_SQL = 'DROP TRIGGER TRA_I_ACTRUNINFO';
					   PREPARE	SQLA FROM LS_SQL; 
					   EXECUTE SQLA;
				END	IF;
			
		END
			 $

			 
			 
			 CREATE OR REPLACE PROCEDURE PROC_ACTNEXTINFO_QUERY_NEW ()
		LANGUAGE SQL
		BEGIN
			begin
						--将可运行活动放入活动查询临时表
						INSERT INTO TMP_QUERY_ACTNEXTINFO
								(SELECT BB.IPRJID,
												BB.IFLOWID,
												BB.IFLOWINS,
												AA.IPROJECTNAME,
												AA.ISYSTEM,
												AA.IPRJUUID,
												AA.IFLOWNAME,
												BB.IACTNAME,
												BB.IPRENUM,
												BB.ISERVERIP
									FROM V_FINISHINFO AA, IEAI_SCHEDULER_ACT BB
									WHERE AA.IOPERATIONID = BB.IOPERATIONID
										AND AA.NUM = BB.IPRENUM
										AND AA.IFLOWINS = BB.IFLOWINS
										AND AA.IFLOWID = BB.IFLOWID
										AND BB.IISSTATE in (0,1)
								UNION ALL
								SELECT T4.IPRJID,
												T4.IFLOWID,
												T4.IFLOWINS,
												T5.IPROJECTNAME,
												T5.ISYSTEM,
												T5.IPRJUUID,
												T5.IFLOWNAME,
												T4.IACTNAME,
												T4.IPRENUM,
												T4.ISERVERIP
									FROM IEAI_SCHEDULER_ACT T4, IEAI_WORKFLOWINSTANCE T5
									WHERE T4.IFLOWID = T5.IFLOWID
										AND T4.IISSTATE in (0,1)
										AND T4.IPRENUM = 0
										AND T5.ISTATUS IN (0, 30, 40, 46, 47));
								
								--	UPDATE
                --            IEAI_ACTRUNTIME TS 
                --        SET
                --            TS.ISTATE = 'HangUp' 
                --        WHERE
                --            EXISTS (SELECT
                --                        1 
                --                    FROM
                --                        V_FINISHINFO_STATE TT 
                --                    WHERE
                --                        TS.IACTNAME=TT.IACTNAME AND
                --                        TS.IFLOWID=TT.IFLOWID AND
                --                        TT.NUM>0
                --           ) AND
                --            TS.ISTATE != 'Disable' AND
                --            TS.ISTATE != 'Finished' AND
                --            TS.ISTATE != 'HangUp' ;
										                                                        
															
						--没有配置互斥关系的作业存储到活动执行临时表
						INSERT INTO TMP_EXEC_ACTNEXTINFO
								(SELECT T.IPRJID,
												T.IFLOWID,
												T.IFLOWINS,
												T.IPROJECTNAME,
												T.ISYSTEM,
												T.IPRJUUID,
												T.IFLOWNAME,
												T.IACTNAME,
												T.IPRENUM,
												T.ISERVERIP
									FROM (SELECT T8.ISYSTEM, T8.IACTNAME
													FROM IEAI_EXCELMODEL T8
													WHERE T8.IOPERATIONID IN
																(SELECT DISTINCT TTT.IOPERATIONID
																	FROM (SELECT T11.IOPERATIONID
																					FROM TMP_QUERY_ACTNEXTINFO T, IEAI_EXCELMODEL T11
																					WHERE T.ISYSTEM = T11.ISYSTEM
																						AND T.IACTNAME = T11.IACTNAME) TTT
																	WHERE NOT EXISTS
																	(SELECT *
																					FROM IEAI_MUTEX_ACT TT1
																					WHERE TT1.IOPERATIONID = TTT.IOPERATIONID))) T10,
												TMP_QUERY_ACTNEXTINFO T
									WHERE T10.ISYSTEM = T.ISYSTEM
										AND T10.IACTNAME = T.IACTNAME);
						--修改作业状态为互斥挂起
						UPDATE IEAI_ACTRUNTIME TG
							SET TG.ISTATE = 'MUTEX_HANGUP'
						WHERE TG.IID IN (SELECT TR.IID
																FROM (SELECT DISTINCT T.IFLOWID, T4.IACTNAME
																				FROM TMP_QUERY_ACTNEXTINFO T,
																						IEAI_MUTEX_RULE       T1,
																						IEAI_MUTEX_ACT        T2,
																						IEAI_SYSTEM_GROUP     T3,
																						IEAI_EXCELMODEL       T4
																			WHERE T1.IMUTEXRULEID = T2.IMUTEXRULEID
																				AND T1.ISYSGROUPID = T3.ISYSGROUPID
																				AND T.ISYSTEM = T3.ISYSGROUPNAME
																				AND T2.IOPERATIONID = T4.IOPERATIONID) TTT,
																		IEAI_ACTRUNTIME TR
															WHERE TTT.IACTNAME = TR.IACTNAME
																AND TTT.IFLOWID = TR.IFLOWID)
							AND TG.ISTATE IN ('HangUp');
						--查询出配置互斥的作业
						INSERT INTO TMP_MUTEX_INFO
								(SELECT DISTINCT IFLOWID, IFLOWINS, IACTNAME, IMUTEXRULEID, ISYSTEM
									FROM (SELECT DISTINCT T.IFLOWID,
																				T.IFLOWINS,
																				T4.IACTNAME,
																				T1.IMUTEXRULEID,
																				T.ISYSTEM,
																				T2.IOPERATIONID
													FROM TMP_QUERY_ACTNEXTINFO T,
																IEAI_MUTEX_RULE       T1,
																IEAI_MUTEX_ACT        T2,
																IEAI_SYSTEM_GROUP     T3,
																IEAI_EXCELMODEL       T4
													WHERE T1.IMUTEXRULEID = T2.IMUTEXRULEID
														AND T1.ISYSGROUPID = T3.ISYSGROUPID
														AND T.ISYSTEM = T3.ISYSGROUPNAME
														AND T2.IOPERATIONID = T4.IOPERATIONID) TT
									WHERE TT.IOPERATIONID NOT IN
												(SELECT T11.IOPERATIONID
													FROM IEAI_EXCELMODEL       T11,
																TMP_QUERY_ACTNEXTINFO T22,
																IEAI_MUTEX_ACT        TR,
																IEAI_MUTEX_RULE       TD
													WHERE T11.ISYSTEM = T22.ISYSTEM
														AND T11.IACTNAME = T22.IACTNAME
														AND TR.IOPERATIONID = T11.IOPERATIONID
														AND TR.IMUTEXRULEID = TD.IMUTEXRULEID
														AND TD.IRULETYPE != 3));
						--取最小id插入执行临时表
						INSERT INTO TMP_EXEC_ACTNEXTINFO
								(SELECT T12.IPRJID,
												T12.IFLOWID,
												T12.IFLOWINS,
												T12.IPROJECTNAME,
												T12.ISYSTEM,
												T12.IPRJUUID,
												T12.IFLOWNAME,
												T12.IACTNAME,
												T12.IPRENUM,
												T12.ISERVERIP
									FROM (SELECT T5.IMUTEXRULEID, MIN(T6.IOPERATIONID) AS IOPERATIONID
													FROM TMP_QUERY_ACTNEXTINFO T4,
																IEAI_MUTEX_RULE       T5,
																IEAI_MUTEX_ACT        T6,
																IEAI_EXCELMODEL       T7,
																IEAI_SYSTEM_GROUP     T8
													WHERE T5.IMUTEXRULEID = T6.IMUTEXRULEID
														AND T5.ISYSGROUPID = T8.ISYSGROUPID
														AND T4.ISYSTEM = T8.ISYSGROUPNAME
														AND T6.IOPERATIONID = T7.IOPERATIONID
															
														AND T4.ISYSTEM IN (SELECT TT.ISYSTEM
																								FROM (SELECT T.ISYSTEM
																												FROM TMP_QUERY_ACTNEXTINFO T,
																															IEAI_MUTEX_RULE       T1,
																															IEAI_MUTEX_ACT        T2,
																															IEAI_SYSTEM_GROUP     T3,
																															IEAI_EXCELMODEL       T4
																												WHERE T1.IMUTEXRULEID = T2.IMUTEXRULEID
																													AND T1.ISYSGROUPID = T3.ISYSGROUPID
																													AND T.ISYSTEM = T3.ISYSGROUPNAME
																													AND T2.IOPERATIONID = T4.IOPERATIONID
																											--AND T.IACTNAME = T4.IACTNAME
																												GROUP BY T.ISYSTEM, T.IFLOWINS) TT
																								GROUP BY TT.ISYSTEM
																							HAVING COUNT(TT.ISYSTEM) = 1)
															
														AND T5.IMUTEXRULEID NOT IN
																(SELECT DISTINCT (T11.IMUTEXRULEID)
																	FROM IEAI_WORKFLOWINSTANCE T9,
																				IEAI_ACTRUNTIME       T10,
																				TMP_MUTEX_INFO        T11
																	WHERE T9.ISYSTEM = T11.ISYSTEM
																		AND T10.IACTNAME = T11.IACTNAME
																		AND T9.IFLOWID = T10.IFLOWID
																		AND T9.ISTATUS IN (15, 0, 8, 30, 46, 47, 40)
																		AND T10.ISTATE IN ('Running',
																											'QueueUp',
																											'Fail',
																											'Fail:Business',
																											'ManualRunning'))
													GROUP BY T5.IMUTEXRULEID) TT,
												TMP_QUERY_ACTNEXTINFO T12,
												IEAI_MUTEX_ACT T13,
												IEAI_EXCELMODEL T14
									WHERE TT.IMUTEXRULEID = T13.IMUTEXRULEID
										AND T13.IOPERATIONID = T14.IOPERATIONID
										AND T12.IACTNAME = T14.IACTNAME);
						--取数据日期最小的数据插入活动执行表
						INSERT INTO TMP_EXEC_ACTNEXTINFO
								(SELECT DISTINCT TTR.IPRJID,
																TTR.IFLOWID,
																TTR.IFLOWINS,
																TTR.IPROJECTNAME,
																TTR.ISYSTEM,
																TTR.IPRJUUID,
																TTR.IFLOWNAME,
																TTR.IACTNAME,
																TTR.IPRENUM,
																TTR.ISERVERIP
								
									FROM (SELECT TTZ.ISYSTEM, TTZ.IACTNAME, MIN(TTZ.IFLOWINS) AS FLOWINS
													FROM (SELECT DISTINCT T.IPRJID,
																								T.IFLOWID,
																								T.IFLOWINS,
																								T.IPROJECTNAME,
																								T.ISYSTEM,
																								T.IPRJUUID,
																								T.IFLOWNAME,
																								T.IACTNAME,
																								T.IPRENUM,
																								T.ISERVERIP
																	FROM TMP_QUERY_ACTNEXTINFO T,
																				IEAI_MUTEX_RULE       T1,
																				IEAI_MUTEX_ACT        T2,
																				IEAI_SYSTEM_GROUP     T3
																	WHERE T1.IMUTEXRULEID = T2.IMUTEXRULEID
																		AND T1.ISYSGROUPID = T3.ISYSGROUPID
																		AND T.ISYSTEM = T3.ISYSGROUPNAME
																		AND T.ISYSTEM IN
																				(SELECT TT.ISYSTEM
																					FROM (SELECT T.ISYSTEM
																									FROM TMP_QUERY_ACTNEXTINFO T,
																												IEAI_MUTEX_RULE       T1,
																												IEAI_MUTEX_ACT        T2,
																												IEAI_SYSTEM_GROUP     T3,
																												IEAI_EXCELMODEL       T4
																									WHERE T1.IMUTEXRULEID = T2.IMUTEXRULEID
																										AND T1.ISYSGROUPID = T3.ISYSGROUPID
																										AND T.ISYSTEM = T3.ISYSGROUPNAME
																										AND T2.IOPERATIONID = T4.IOPERATIONID
																								-- AND T.IACTNAME=T4.IACTNAME
																									GROUP BY T.ISYSTEM, T.IFLOWINS) TT
																					GROUP BY TT.ISYSTEM
																				HAVING COUNT(TT.ISYSTEM) > 1)
																			-- AND T2.IOPERATIONID = T88.IOPERATIONID
																		AND T1.IMUTEXRULEID NOT IN
																				(SELECT DISTINCT (T10.IMUTEXRULEID)
																					FROM IEAI_WORKFLOWINSTANCE T8,
																								IEAI_ACTRUNTIME       T9,
																								TMP_MUTEX_INFO        T10
																					WHERE T8.ISYSTEM = T10.ISYSTEM
																						AND T9.IACTNAME = T10.IACTNAME
																						AND T8.IFLOWID = T9.IFLOWID
																						AND T8.ISTATUS IN (15, 0, 8, 30, 46, 47, 40)
																						AND T9.ISTATE IN ('Running',
																															'QueueUp',
																															'Fail',
																															'Fail:Business',
																															'ManualRunning'))) TTZ
													GROUP BY TTZ.ISYSTEM, TTZ.IACTNAME) TTB,
												TMP_QUERY_ACTNEXTINFO TTR
									WHERE TTB.ISYSTEM = TTR.ISYSTEM
										AND TTB.IACTNAME = TTR.IACTNAME
										AND TTB.FLOWINS = TTR.IFLOWINS);
				end;
		END
		$
		
		 CREATE OR REPLACE PROCEDURE PROC_UPDATE_ACTMONITOR (IN HOSTIP VARCHAR(50))
	LANGUAGE SQL
		BEGIN
			DECLARE INSTANCETYPE NUMERIC(2);
			DECLARE     FLOWID       NUMERIC(19,0);
			DECLARE     TEMPNUM          NUMERIC(19,0);
			DECLARE     OVERNUM      NUMERIC(6);
			begin
				FOR ALLUPFLOW AS CURSOR1 CURSOR FOR
					SELECT
					t.IMONITORINFOID AS MID,
					t.ILASTINSTANCE,
					t.IPRJNAME AS PNAME,
					t.IFLOWNAME AS FNAME,
					t.IINSTANCENAME AS INANAME
					FROM IEAI_ACTMONITOR_RUNINFO T WHERE T.IFLOWID = 0
				DO
					SET INSTANCETYPE = ILASTINSTANCE;
					IF INSTANCETYPE = 2 THEN
							--指定的实例
							SELECT COUNT(1)
							INTO TEMPNUM
							FROM IEAI_WORKFLOWINSTANCE FLOW
							WHERE FLOW.IPROJECTNAME = PNAME
							AND FLOW.IFLOWNAME = FNAME
							AND FLOW.IFLOWINSNAME = INANAME;
							IF TEMPNUM > 0 AND TEMPNUM < 2 THEN
								SELECT FLOW.IFLOWID
								INTO FLOWID
								FROM IEAI_WORKFLOWINSTANCE FLOW
								WHERE FLOW.IPROJECTNAME = PNAME
								AND FLOW.IFLOWNAME = FNAME
								AND FLOW.IFLOWINSNAME = INANAME;
								UPDATE IEAI_ACTMONITOR_RUNINFO
								SET IFLOWID = FLOWID
								WHERE IMONITORINFOID = MID;
							ELSEIF TEMPNUM > 1 THEN
								SELECT MAX(FLOW.IFLOWID)
								INTO FLOWID
								FROM IEAI_WORKFLOWINSTANCE FLOW
								WHERE FLOW.IPROJECTNAME = PNAME
								AND FLOW.IFLOWNAME = FNAME
								AND FLOW.IFLOWINSNAME = INANAME;
								UPDATE IEAI_ACTMONITOR_RUNINFO
								SET IFLOWID = FLOWID
								WHERE IMONITORINFOID = MID;
							END IF;
					ELSEIF INSTANCETYPE = 1 THEN
							--上一个实例
							SELECT COUNT(1)
							INTO TEMPNUM
							FROM (SELECT FLOW.IFLOWID,  ROW_NUMBER() OVER (ORDER BY FLOW.IFLOWID DESC) AS RN
									FROM IEAI_WORKFLOWINSTANCE FLOW
									WHERE FLOW.IPROJECTNAME = PNAME
									AND FLOW.IFLOWNAME = FNAME
									--AND FLOW.IFLOWSTATUS = 0
									ORDER BY FLOW.IFLOWID DESC) S
							WHERE S.RN = 2;
							IF TEMPNUM > 0 THEN
								SELECT S.IFLOWID
								INTO FLOWID
								FROM (SELECT FLOW.IFLOWID, ROW_NUMBER() OVER (ORDER BY FLOW.IFLOWID DESC) AS RN
										FROM IEAI_WORKFLOWINSTANCE FLOW
										WHERE FLOW.IPROJECTNAME = PNAME
										AND FLOW.IFLOWNAME = FNAME
										--AND FLOW.IFLOWSTATUS = 0
										ORDER BY FLOW.IFLOWID DESC) S
								WHERE S.RN = 2;
								IF FLOWID IS NOT NULL AND FLOWID > 0 THEN
									UPDATE IEAI_ACTMONITOR_RUNINFO
									SET IFLOWID = FLOWID
									WHERE IMONITORINFOID = MID;
								END IF;
							END IF;
						ELSE
							--最新的实例
							SELECT COUNT(1)
							INTO TEMPNUM
							FROM (SELECT FLOW.IFLOWID
									FROM IEAI_WORKFLOWINSTANCE FLOW
									WHERE FLOW.IPROJECTNAME = PNAME
									AND FLOW.IFLOWNAME = FNAME);
							IF TEMPNUM > 0 THEN
								SELECT S.IFLOWID
								INTO FLOWID
								FROM (SELECT FLOW.IFLOWID, ROW_NUMBER() OVER (ORDER BY FLOW.IFLOWID DESC) AS RN
										FROM IEAI_WORKFLOWINSTANCE FLOW
										WHERE FLOW.IPROJECTNAME = PNAME
										AND FLOW.IFLOWNAME = FNAME
										--AND FLOW.ISTATUS = 0
										ORDER BY FLOW.IFLOWID DESC) S
								WHERE S.RN = 1;
								IF FLOWID IS NOT NULL AND FLOWID > 0 THEN
									UPDATE IEAI_ACTMONITOR_RUNINFO
									SET IFLOWID = FLOWID
									WHERE IMONITORINFOID = MID;
								END IF;
							END IF;
						END IF;
				END FOR;
				--更新已经运行完成的工作流或活动的监控状态
					SELECT COUNT(FLOW.IFLOWID)
					INTO OVERNUM
					FROM IEAI_WORKFLOWINSTANCE FLOW, IEAI_ACTMONITOR_RUNINFO INFO
					WHERE FLOW.IFLOWID = INFO.IFLOWID
					AND (FLOW.ISTATUS = 2 OR FLOW.ISTATUS = 4)
					AND INFO.ISTATUS = 0;
					IF OVERNUM > 0 THEN
						UPDATE IEAI_ACTMONITOR_RUNINFO T
						SET IACTSTATUS = 0, ISTATUS = 1
						WHERE T.IFLOWID IN
							(SELECT FLOW.IFLOWID
								FROM IEAI_WORKFLOWINSTANCE FLOW, IEAI_ACTMONITOR_RUNINFO INFO
								WHERE FLOW.IFLOWID = INFO.IFLOWID
								AND (FLOW.ISTATUS = 2 OR FLOW.ISTATUS = 4)
								AND INFO.ISTATUS = 0);
					END IF;
			end;
		END
		$
		
			CREATE OR REPLACE PROCEDURE PROC_RUN_ACTINFO(IN AV_PRJ_NAME VARCHAR(255),
                                             IN AV_FLOW_NAME VARCHAR(255),
                                             IN AV_ACT_NAME VARCHAR(255),
                                             IN AV_FLOW_ID NUMERIC(19,0),
                                             IN AV_PRJ_UUID VARCHAR(255))
    /*
    
        AV_PRJ_NAME    <IN>  工程名
        AV_FLOW_NAME   <IN>  工作流名
        AV_ACT_NAME    <IN>  活动名
        AV_FLOW_ID     <IN>  工作流ID
        AV_PRJ_UUID    <IN>  工程UUID
    */
	BEGIN
		DECLARE LN_EXEC_ID  NUMERIC(19, 0); --活动驱动表(ieai_execact)主键
			DECLARE LN_COUNT    NUMERIC(19, 0);
			DECLARE LN_COUNTRUN NUMERIC(19, 0);
			DECLARE LN_ACTID    NUMERIC(19, 0);
			DECLARE LN_ZICOUNT  NUMERIC(19, 0);
			DECLARE LV_SQL      VARCHAR(4000);
			DECLARE LI_RETURN   NUMERIC;
      DECLARE tempCount   INT;
		
		BEGIN
			CALL PROC_GET_NEXT_ID ('com.ideal.ieai.server.repository.engine.RepExecAct', LN_EXEC_ID);
			SET LN_COUNTRUN = 0;
			SELECT COUNT(*)
			INTO LN_COUNT
			FROM (SELECT *
					FROM IEAI_ACTPRE GY
					WHERE GY.IOPERATIONID =
						(SELECT GJ.IOPERATIONID
							FROM IEAI_EXCELMODEL GJ
							WHERE GJ.IACTNAME = AV_ACT_NAME
							AND GJ.IMAINLINENAME = AV_FLOW_NAME
							AND GJ.IMAINPRONAME = AV_PRJ_NAME)) AA
			WHERE AA.IPROJECTNAME = AV_PRJ_NAME
			AND AA.IMAINLINENAME = AV_FLOW_NAME;
		
			SELECT COUNT(TT.IMUTEXRULEID)
			INTO LN_ZICOUNT
			FROM IEAI_MUTEX_RULE TT
			WHERE TT.IMUTEXRULEID IN
				(SELECT T1.IMUTEXRULEID
					FROM IEAI_MUTEX_ACT T1
					WHERE T1.IOPERATIONID IN
						(SELECT T5.IOPERATIONID
							FROM IEAI_EXCELMODEL T5
							WHERE T5.IACTNAME = AV_ACT_NAME
							AND T5.ISYSTEM = (SELECT T.ISYSTEM
												FROM IEAI_EXCELMODEL T
												WHERE T.IMAINPRONAME = AV_PRJ_NAME
													AND T.IACTNAME = AV_ACT_NAME)))
			AND TT.IRULETYPE = 3;
		

			IF LN_COUNTRUN > 0 THEN
				RETURN;
			ELSE
				SELECT IACTID
				INTO LN_ACTID
				FROM IEAI_ACTRUNTIME
				WHERE IACTNAME = AV_ACT_NAME
				AND IFLOWID = AV_FLOW_ID;
				
				INSERT INTO TMP_EXECACT
					(SELECT * FROM (
                        SELECT IID,
                                        IDELAYTO,
                                        ITRRAISEEXCEPTION,
                                        ITRSTATE,
                                        ITRSTRUCTOUTPUTSAVED,
                                        ACTID,
                                        ACTNAME,
                                        IFLAG,
                                        IFLOWID,
                                        IISFORRECOVERY,
                                        IISINTRANSACTION,
                                        IISSAFEFIRST,
                                        IISTIMEOUT,
                                        IREXECREQUESTID,
                                        IRUNINGPOSITION,
                                        ISCOPEID,
                                        ISTATE,
                                        ITIMEOUTTIMES,
                                        ISTARTERRUNTIMES,
                                        ISEXCEPTED
                        FROM (SELECT IID,
                                        IDELAYTO,
                                        ITRRAISEEXCEPTION,
                                        ITRSTATE,
                                        ITRSTRUCTOUTPUTSAVED,
                                        LN_ACTID as ACTID,
                                        AV_ACT_NAME as ACTNAME,
                                        IFLAG,
                                        IFLOWID,
                                        IISFORRECOVERY,
                                        IISINTRANSACTION,
                                        IISSAFEFIRST,
                                        IISTIMEOUT,
                                        IREXECREQUESTID,
                                        IRUNINGPOSITION,
                                        ISCOPEID,
                                        ISTATE,
                                        ITIMEOUTTIMES,
                                        ISTARTERRUNTIMES,
                                        ISEXCEPTED,
                                        row_number() over() AS RN
                                FROM IEAI_EXECACT
                                WHERE IFLOWID = AV_FLOW_ID
                                    AND IACTID =
                                        (SELECT YH.IACTID
                                        FROM IEAI_ACTRUNTIME YH
                                        WHERE YH.IACTNAME = (SELECT IPREACTNAME
                                                                FROM (SELECT TP.IACTNAME IPREACTNAME,row_number() over() AS ROWNUM
                                                                        FROM IEAI_ACTRUNTIME TP
                                                                        WHERE TP.IACTNAME IN
                                                                            (SELECT GY.IPREACTNAME
                                                                                FROM IEAI_ACTPRE GY
                                                                                WHERE GY.IOPERATIONID =
                                                                                    (SELECT GJ.IOPERATIONID
                                                                                        FROM IEAI_EXCELMODEL GJ
                                                                                        WHERE GJ.IACTNAME =
                                                                                            AV_ACT_NAME
                                                                                        AND GJ.IMAINLINENAME =
                                                                                            AV_FLOW_NAME
                                                                                        AND GJ.IMAINPRONAME =
                                                                                            AV_PRJ_NAME)
                                                                                AND GY.IMAINLINENAME =
                                                                                    AV_FLOW_NAME)
                                                                        AND TP.IFLOWID = AV_FLOW_ID
                                                                        AND TP.IENDTIME > 0) TC
                                                                WHERE TC.ROWNUM = 1)
                                            AND YH.IFLOWID = AV_FLOW_ID)
                                ORDER BY IID DESC) TX
                        WHERE TX.RN = 1)
                        );
				IF LN_COUNT = 0 THEN
					SELECT IACTID
					INTO LN_ACTID
					FROM IEAI_ACTRUNTIME
					WHERE IACTNAME = AV_ACT_NAME
					AND IFLOWID = AV_FLOW_ID;
					INSERT INTO TMP_EXECACT
						(SELECT IID,
								IDELAYTO,
								ITRRAISEEXCEPTION,
								ITRSTATE,
								ITRSTRUCTOUTPUTSAVED,
								LN_ACTID,
								AV_ACT_NAME,
								IFLAG,
								IFLOWID,
								IISFORRECOVERY,
								IISINTRANSACTION,
								IISSAFEFIRST,
								IISTIMEOUT,
								IREXECREQUESTID,
								IRUNINGPOSITION,
								ISCOPEID,
								ISTATE,
								ITIMEOUTTIMES,
								ISTARTERRUNTIMES,
								0
						FROM IEAI_EXECACT
						WHERE IFLOWID = AV_FLOW_ID
							AND IACTID = 0);
				
					-- UPDATE TMP_EXECACT
					--   SET IACTNAME = AV_ACT_NAME, IACTID = LN_ACTID,IID=LN_EXEC_ID
					-- WHERE IFLOWID = AV_FLOW_ID;
					INSERT INTO IEAI_EXECACT
						(SELECT LN_EXEC_ID,
								IDELAYTO,
								ITRRAISEEXCEPTION,
								ITRSTATE,
								ITRSTRUCTOUTPUTSAVED,
								IACTID,
								IACTNAME,
								IFLAG,
								IFLOWID,
								IISFORRECOVERY,
								IISINTRANSACTION,
								IISSAFEFIRST,
								IISTIMEOUT,
								IREXECREQUESTID,
								100,
								ISCOPEID,
								5,
								ITIMEOUTTIMES,
								ISTARTERRUNTIMES,
								0,
								1
						FROM TMP_EXECACT);
                     GET DIAGNOSTICS tempCount= ROW_COUNT;
					INSERT INTO IEAI_ACTSTATEDATA
						(IID, IBYTEDATAID, IINTDATA, ILONGDATA)
					VALUES
						(LN_EXEC_ID, -1, -1, -1);
				
                     IF tempCount > 0 THEN
                        UPDATE IEAI_SCHEDULER_ACT TS
                        SET TS.IISSTATE = 2
                        WHERE TS.IACTNAME = AV_ACT_NAME
                        AND TS.IFLOWID = AV_FLOW_ID;
                     END IF;
				ELSE
				
					--UPDATE TMP_EXECACT
					--   SET IACTNAME = AV_ACT_NAME, IACTID = LN_ACTID, IID = LN_EXEC_ID
					-- WHERE IFLOWID = AV_FLOW_ID;
					INSERT INTO IEAI_EXECACT
						(SELECT LN_EXEC_ID,
								IDELAYTO,
								ITRRAISEEXCEPTION,
								ITRSTATE,
								ITRSTRUCTOUTPUTSAVED,
								IACTID,
								IACTNAME,
								IFLAG,
								IFLOWID,
								IISFORRECOVERY,
								IISINTRANSACTION,
								IISSAFEFIRST,
								IISTIMEOUT,
								IREXECREQUESTID,
								100,
								ISCOPEID,
								5,
								ITIMEOUTTIMES,
								ISTARTERRUNTIMES,
								0,
								1
						FROM TMP_EXECACT);
                    GET DIAGNOSTICS tempCount= ROW_COUNT;
					INSERT INTO IEAI_ACTSTATEDATA
						(IID, IBYTEDATAID, IINTDATA, ILONGDATA)
					VALUES
						(LN_EXEC_ID, -1, -1, -1);
				   
                    IF tempCount > 0 THEN
                        UPDATE IEAI_SCHEDULER_ACT TS
                        SET TS.IISSTATE = 2
                        WHERE TS.IACTNAME = AV_ACT_NAME
                        AND TS.IFLOWID = AV_FLOW_ID;
                    END IF;
				END IF;
			END IF;
		END;
	END 
	$
	BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU WHERE IID = 1086 ;
					IF	LI_EXISTS = 0 THEN
							SET	LS_SQL = 'INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1086, ''运行耗时配置查询'', ''1'', ''queryRuntimeBatch.do'', 10, '''', ''images/info27.png'')';
						PREPARE	SQLA FROM LS_SQL; 
						EXECUTE SQLA;
					END	IF;
			
				 SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HAND_RULE' AND  COLNAME='ISEDIT';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_HAND_RULE ADD ISEDIT NUMERIC(2)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_HAND_RULE' AND  COLNAME='IDELAYTIME';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_HAND_RULE ADD IDELAYTIME DECIMAL(19,0)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;  
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_HAND_RULE_CFG';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_HAND_RULE_CFG(IID DECIMAL(19) NOT NULL,IHANDRULEID DECIMAL(19),IBEGINTIME VARCHAR(255),ITYPEDAY VARCHAR(255),ITYPEWEEK VARCHAR(255),ITYPEMONTH VARCHAR(255),IDAYSET VARCHAR(255),IWEEKSET VARCHAR(255),IMONTHSET VARCHAR(255), CONSTRAINT PK_IEAI_HAND_RULE_CFG PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF; 
				COMMIT;
				
				
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WORKFLOW' AND INDNAME='IDX_IEAI_WORKFLOW_PRJID';
				IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'CREATE INDEX IDX_IEAI_WORKFLOW_PRJID ON IEAI_WORKFLOW(IPRJID)';
					 PREPARE	SQLA FROM LS_SQL; 
					 EXECUTE SQLA;
				END	IF;
		

			END
	$
	
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ERRORTASK' AND  COLNAME='USERID';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'ALTER TABLE IEAI_ERRORTASK ADD USERID NUMERIC(19,0)';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF; 
			
		END
			 $
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSIBM.SYSCOLUMNS WHERE TBNAME='IEAI_CUSTOM_REPORT_INFO_LIST' AND NAME='ISQL_TEXT' AND COLTYPE='CLOB';
					IF	LI_EXISTS = 0 THEN
						SET LS_SQL = 'CREATE TABLE TMP_CUSTOM_REPORT_INFO_LIST LIKE IEAI_CUSTOM_REPORT_INFO_LIST';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
						SET LS_SQL = 'INSERT INTO TMP_CUSTOM_REPORT_INFO_LIST SELECT *  FROM IEAI_CUSTOM_REPORT_INFO_LIST';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
						SET LS_SQL = 'DROP TABLE IEAI_CUSTOM_REPORT_INFO_LIST';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
						SET LS_SQL = 'CREATE TABLE IEAI_CUSTOM_REPORT_INFO_LIST (  IID DECIMAL(19,0) NOT NULL,  IDBID DECIMAL(19,0) NOT NULL,  IREPORTNAME VARCHAR(500) NOT NULL ,  IREPORTTYPEID DECIMAL(19,0) NOT NULL,  ICREATE_USERID DECIMAL(19,0)  DEFAULT 4 NOT NULL   ,  ISQL_TEXT CLOB  ,  ISTATUS SMALLINT  DEFAULT 0 NOT NULL  ,  ICOMMENT VARCHAR(500) ,  ICREATE_TIME TIMESTAMP NOT NULL,  ILAST_MODIFIED_TIME TIMESTAMP  DEFAULT CURRENT_TIMESTAMP  NOT NULL ,  PRIMARY KEY (IID))';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
						SET LS_SQL = 'INSERT INTO IEAI_CUSTOM_REPORT_INFO_LIST SELECT *  FROM TMP_CUSTOM_REPORT_INFO_LIST';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF; 
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_CUSTOM_REPORT_INFO_LIST';
					IF	LI_EXISTS = 1 THEN
						SET LS_SQL = 'DROP TABLE TMP_CUSTOM_REPORT_INFO_LIST';
						PREPARE SQLA FROM LS_SQL; 
						EXECUTE SQLA;
				END IF;
		END
			 $
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='TMP_IEAI_EXCELMODEL';
			IF	LI_EXISTS = 1 THEN
				SET LS_SQL = 'DROP TABLE TMP_IEAI_EXCELMODEL';
				PREPARE SQLA FROM LS_SQL; 
				EXECUTE SQLA;
				SET LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE TMP_IEAI_EXCELMODEL(IOPERATIONID VARCHAR(255) NOT NULL,IMAINPRONAME VARCHAR(255),ICHILDPRONAME VARCHAR(255),IACTNAME VARCHAR(200),IACTDESCRIPTION VARCHAR(4000),ICHANGEOPR VARCHAR(255),IOKFILEABSOLUTEPATH VARCHAR(4000),IOKFILEFINDWEEK VARCHAR(255),ISHELLHOUSE VARCHAR(4000),ISHELLABSOLUTEPATH VARCHAR(4000),IINPUTPARAM VARCHAR(200),ILASTLINE VARCHAR(255),IAGENTSOURCEGROUP VARCHAR(255),IMAINLINENAME VARCHAR(255),IHEADTAILFLAG VARCHAR(255),APTGROUPNAME VARCHAR(255),APTFILENAME VARCHAR(255),ISDB2 VARCHAR(255),DB2IP VARCHAR(255),APTRESGROUPNAME VARCHAR(255),DAYS VARCHAR(100),LOGIC VARCHAR(10),WDAYS VARCHAR(100),IMONTH VARCHAR(100))';
				PREPARE SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='DAYS';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL ADD DAYS VARCHAR(100)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='LOGIC';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL ADD LOGIC VARCHAR(10)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='WDAYS';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL ADD WDAYS VARCHAR(100)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='IMONTH';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL ADD IMONTH VARCHAR(100)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='DAYS';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL_COPY ADD DAYS VARCHAR(100)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='LOGIC';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL_COPY ADD LOGIC VARCHAR(10)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='WDAYS';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL_COPY ADD WDAYS VARCHAR(100)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='IMONTH';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_EXCELMODEL_COPY ADD IMONTH VARCHAR(100)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ERRORTASK' AND  COLNAME='USERID';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_ERRORTASK ADD USERID DECIMAL(19,0)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_ERRORTASK' AND  COLNAME='ISTARTUSER';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_ERRORTASK ADD ISTARTUSER DECIMAL(19,0)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
		END
			 $
	    --8.2.0
	    BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROJECT_AGENTINFO' AND  COLNAME='IAGENTIP';
			IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_PROJECT_AGENTINFO alter column IAGENTIP set data type varchar(255)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
		END
			 $
			 
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CUSTOM_REPORT_DATASOURCE' AND  COLNAME='INAME';
			IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_CUSTOM_REPORT_DATASOURCE ALTER COLUMN INAME SET DATA TYPE VARCHAR(200)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
		END
			 $
		
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CUSTOM_REPORT_INFO_LIST' AND  COLNAME='ICUSTUSERNAME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_CUSTOM_REPORT_INFO_LIST ADD ICUSTUSERNAME VARCHAR(255)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CUSTOM_REPORT_INFO_LIST' AND  COLNAME='ISSEND';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_CUSTOM_REPORT_INFO_LIST ADD ISSEND VARCHAR(255)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CUSTOM_REPORT_INFO_LIST' AND  COLNAME='EXECUTIONID';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_CUSTOM_REPORT_INFO_LIST ADD EXECUTIONID DECIMAL(19,0)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_CUSTOM_REPORT_INFO_LIST' AND  COLNAME='PERIOD';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_CUSTOM_REPORT_INFO_LIST ADD PERIOD VARCHAR(255)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
		END
			 $
			 
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_ACTSKIP_CONFIG';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_ACTSKIP_CONFIG (IID DECIMAL(19) NOT NULL,IPRONAME VARCHAR(255),IFLOWNAME VARCHAR(255),IACTNAME VARCHAR(255),ISKIPWAY DECIMAL(19) DEFAULT 0, CONSTRAINT PK_IEAI_ACTSKIP_CONFIG PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
			
		END
			 $
			 
	  BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DELAYER_ACTIVITY';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_DELAYER_ACTIVITY (IID DECIMAL(19) NOT NULL, IFLOWID DECIMAL(19) NOT NULL, IPRJID DECIMAL(19), IACTNAME VARCHAR(255) NOT NULL, IFLOWNAME VARCHAR(255), IACTTYPE VARCHAR(255), IPROJECTNAME VARCHAR(255),ITIMESTR VARCHAR(255),IDELAYERTYPE DECIMAL(19),ISTATUS DECIMAL(19) DEFAULT 0,ISEFFECT DECIMAL(19) DEFAULT 0, CONSTRAINT PK_IEAI_DELAYER_ACTIVITY PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
				
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_DELAYER_ACTIVITY' AND  COLNAME='ITIMESTR_STUDIO';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_DELAYER_ACTIVITY ADD ITIMESTR_STUDIO VARCHAR(255)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
		END
			 $

	    BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
              SELECT COUNT(*) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_FIRST_PAGE_LIST';
                    IF LI_EXISTS > 0 THEN
                        SET LS_SQL = 'DROP TABLE TMP_FIRST_PAGE_LIST';
                        PREPARE SQLA FROM LS_SQL;
                        EXECUTE SQLA;
                    END IF;

              SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME = 'TMP_FIRST_PAGE_LIST';
                    IF LI_EXISTS = 0 THEN
                      SET  LS_SQL = 'CREATE GLOBAL TEMPORARY TABLE "TMP_FIRST_PAGE_LIST"  (
		  "OPT_ID" DECIMAL(19,0) NOT NULL ,
		  "IID" DECIMAL(19,0) ,
		  "FLOW_ID" DECIMAL(19,0) NOT NULL ,
		  "ACT_ID" DECIMAL(19,0) NOT NULL ,
		  "TASK_ID" DECIMAL(19,0) ,
		  "FLOW_INSTANCE_NAME" VARCHAR(255) ,
		  "FLOW_NAME" VARCHAR(255) ,
		  "ACT_NAME" VARCHAR(255) ,
		  "ACT_DESCRIPTION" VARCHAR(255) ,
		  "SHOULDSTARTTIME" DECIMAL(19,0) ,
		  "BEGINEXCTIME" DECIMAL(19,0) ,
		  "ACT_STATES" VARCHAR(50) ,
		  "IOWNER" VARCHAR(255) ,
		  "PRJ_NAME" VARCHAR(255) ,
		  "ACT_TYPE" VARCHAR(255) ,
		  "STARTUSER_NAME" VARCHAR(255) ,
		  "ACT_ERRORTASKID" DECIMAL(19,0) ,
		  "EXECACT_IREXECREQUESTID" VARCHAR(255) ,
		  "ACT_DEF_NAME" VARCHAR(255) ,
		  "SHELLNAME" VARCHAR(255) ,
		  "SHELLPATH" VARCHAR(4000) ,
		  "AGENTIP" VARCHAR(16) ,
		  "SHOULDENDTIME" DECIMAL(19,0) )  ON COMMIT DELETE ROWS ';
                        PREPARE SQLA FROM LS_SQL;
                        EXECUTE SQLA;
                    END IF;
		END
			 $


	CREATE OR REPLACE PROCEDURE PROC_ACTLIST_SDPAGE         (IN AN_USERID NUMERIC(19,0),IN AN_START NUMERIC(19,0),IN AN_PAGESIZE NUMERIC(19,0),IN AN_PRJNAME VARCHAR(255),OUT AN_TOTAL NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_OPT_ID	NUMERIC(19,0);
			DECLARE	AN_PROTYPE	NUMERIC(19,0);
			DECLARE LN_ISALL    NUMERIC(2,0);
			SET AN_PROTYPE = 1;


			SELECT COUNT(1)  INTO LN_ISALL
						FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
						WHERE T.IROLEID = T2.IROLEID   AND T2.IPERMISSION = 1
						AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;

			CALL	PROC_GET_NEXT_ID ('TEMP_IEAI_PERMIT_VALIDATE', LN_OPT_ID);
			SET	AN_OPT_ID = LN_OPT_ID;
			IF  LN_ISALL>0 THEN
					IF (AN_PRJNAME IS NULL) THEN
						SELECT COUNT(1)
						INTO AN_TOTAL
						FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
						WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND C.STATECODE = 0
							AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));


							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID,FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
						FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME,
													WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
											FROM (
									SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
															LN_OPT_ID AS OPT_ID,
															C.IID,
															B.IFLOWID,
															C.IACTID,
															C.ITASKID,
															B.IFLOWINSNAME,
															C.IACTNAME,
															C.IDESC,
															C.ISHOULDSTARTTIME,
															C.IBEGINEXCTIME,
															C.ISTATE,
															(SELECT T1.IOWNER
																FROM IEAI_TASKRUNTIME T1
															WHERE T1.IID = C.ITASKID) AS IOWNER,
															T.INAME,
															C.IACTTYPE,
															B.ISTARTUSERFULLNAME,
															C.IERRORTASKID,
															C.IREXECREQUESTID,
															C.IACTDEFNAME,
															B.IFLOWNAME,
															C.ISHOULDENDTIME,
															C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
														FROM IEAI_WORKFLOWINSTANCE B,
															IEAI_ACTRUNTIME       C,
															IEAI_PROJECT          T
													WHERE B.IPRJLATESTID = T.IID
														AND T.PROTYPE = 1
														AND B.ISTATUS = 0
														AND C.IFLOWID = B.IFLOWID
														AND C.DISAPPEAR = 0
														AND C.IISMONITORACT = 0
														AND C.ACTTYPECODE = 0
														AND C.STATECODE = 0
													   -- AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
														AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
												) WWW
										) WW
							WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					ELSE
								SELECT COUNT(1)
								INTO AN_TOTAL
								FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
								WHERE B.IPRJLATESTID = T.IID
								AND T.PROTYPE = 1
								AND B.ISTATUS = 0
								AND C.IFLOWID = B.IFLOWID
								AND C.DISAPPEAR = 0
								AND C.IISMONITORACT = 0
								AND C.ACTTYPECODE = 0
								AND T.INAME like '%'||AN_PRJNAME||'%'
								AND C.STATECODE = 0
							 --   AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
								AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));

								INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
								SELECT WW.OPT_ID,
										WW.IID,
										WW.IFLOWID,
										WW.IACTID,
										WW.ITASKID,
										WW.IFLOWINSNAME,
										WW.IACTNAME,
										WW.IDESC,
										WW.ISHOULDSTARTTIME,
										WW.IBEGINEXCTIME,
										WW.ISTATE,
										WW.IOWNER,
										WW.INAME,
										WW.IACTTYPE,
										WW.ISTARTUSERFULLNAME,
										WW.IERRORTASKID,
										WW.IREXECREQUESTID,
										WW.IACTDEFNAME,
										WW.IFLOWNAME,
										WW.ISHOULDENDTIME,
										WW.ISHELLNAME,
										WW.IAGENTIP,
										WW.ISHELLPATH
									FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME,
													WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
																			FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
												LN_OPT_ID AS OPT_ID,
												C.IID,
												B.IFLOWID,
												C.IACTID,
												C.ITASKID,
												B.IFLOWINSNAME,
												C.IACTNAME,
												C.IDESC,
												C.ISHOULDSTARTTIME,
												C.IBEGINEXCTIME,
												C.ISTATE,
												(SELECT T1.IOWNER
													FROM IEAI_TASKRUNTIME T1
													WHERE T1.IID = C.ITASKID) AS IOWNER,
												T.INAME,
												C.IACTTYPE,
												B.ISTARTUSERFULLNAME,
												C.IERRORTASKID,
												C.IREXECREQUESTID,
												C.IACTDEFNAME,
												B.IFLOWNAME,
												C.ISHOULDENDTIME,
													C.ISHELLNAME,
													C.IAGENTIP,
													C.ISHELLPATH
											FROM IEAI_WORKFLOWINSTANCE B,
												IEAI_ACTRUNTIME       C,
												IEAI_PROJECT          T
											WHERE B.IPRJLATESTID = T.IID
											AND T.PROTYPE = 1
											AND B.ISTATUS = 0
											AND C.IFLOWID = B.IFLOWID
											AND C.DISAPPEAR = 0
											AND C.IISMONITORACT = 0
											AND C.ACTTYPECODE = 0
											AND T.INAME like '%'||AN_PRJNAME||'%'
											AND C.STATECODE = 0
										  --  AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
											AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
										) WWW
										) WW
									WHERE WW.ROWN > AN_START
									AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					END IF;
			ELSE

				IF (AN_PRJNAME IS NULL) THEN

				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
					SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
										AND T2.IPERMISSION = 1
										AND T.IUSERID = AN_USERID
										AND T2.IPROID > 0) A,
									IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
								AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
							IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;



					SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0
						--AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT PRJNAME DISTINCT FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));



					INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
					SELECT WW.OPT_ID,
							WW.IID,
							WW.IFLOWID,
							WW.IACTID,
							WW.ITASKID,
							WW.IFLOWINSNAME,
							WW.IACTNAME,
							WW.IDESC,
							WW.ISHOULDSTARTTIME,
							WW.IBEGINEXCTIME,
							WW.ISTATE,
							WW.IOWNER,
							WW.INAME,
							WW.IACTTYPE,
							WW.ISTARTUSERFULLNAME,
							WW.IERRORTASKID,
							WW.IREXECREQUESTID,
							WW.IACTDEFNAME,
							WW.IFLOWNAME,
							WW.ISHOULDENDTIME,
								WW.ISHELLNAME,
								WW.IAGENTIP,
								WW.ISHELLPATH
						FROM (SELECT WWW.ROWN,
								WWW.OPT_ID,
								WWW.IID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME,
								WWW.ISHELLNAME,
								WWW.IAGENTIP,
								WWW.ISHELLPATH
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
							FROM IEAI_TASKRUNTIME T1
							WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME,
								C.ISHELLNAME,
							C.IAGENTIP,
							C.ISHELLPATH
					FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
					WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND C.STATECODE = 0
					--	AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
						AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))

						) WWW
					) WW
			WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);

			ELSE

				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
				SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
									AND T2.IPERMISSION = 1
									AND T.IUSERID = AN_USERID
									AND T2.IPROID > 0) A,
								IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
							AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
						IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;

				SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME like '%'||AN_PRJNAME||'%' AND C.STATECODE = 0
					--AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ));



				INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
				SELECT WW.OPT_ID,
						WW.IID,
						WW.IFLOWID,
						WW.IACTID,
						WW.ITASKID,
						WW.IFLOWINSNAME,
						WW.IACTNAME,
						WW.IDESC,
						WW.ISHOULDSTARTTIME,
						WW.IBEGINEXCTIME,
						WW.ISTATE,
						WW.IOWNER,
						WW.INAME,
						WW.IACTTYPE,
						WW.ISTARTUSERFULLNAME,
						WW.IERRORTASKID,
						WW.IREXECREQUESTID,
						WW.IACTDEFNAME,
						WW.IFLOWNAME,
						WW.ISHOULDENDTIME,
						WW.ISHELLNAME,
						WW.IAGENTIP,
						WW.ISHELLPATH
					FROM (SELECT WWW.ROWN,
								WWW.IID,
								WWW.OPT_ID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME,
								WWW.ISHELLNAME,
								WWW.IAGENTIP,
								WWW.ISHELLPATH
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
								LN_OPT_ID AS OPT_ID,
								C.IID,
								B.IFLOWID,
								C.IACTID,
								C.ITASKID,
								B.IFLOWINSNAME,
								C.IACTNAME,
								C.IDESC,
								C.ISHOULDSTARTTIME,
								C.IBEGINEXCTIME,
								C.ISTATE,
								(SELECT T1.IOWNER
									FROM IEAI_TASKRUNTIME T1
									WHERE T1.IID = C.ITASKID) AS IOWNER,
								T.INAME,
								C.IACTTYPE,
								B.ISTARTUSERFULLNAME,
								C.IERRORTASKID,
								C.IREXECREQUESTID,
								C.IACTDEFNAME,
								B.IFLOWNAME,
								C.ISHOULDENDTIME,
								C.ISHELLNAME,
								C.IAGENTIP,
								C.ISHELLPATH
							FROM TMP_USER_PRJ_VALIDATE PR,
								IEAI_WORKFLOWINSTANCE B,
								IEAI_ACTRUNTIME       C,
								IEAI_PROJECT          T
							WHERE PR.PRJID = T.IUPPERID
							AND B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME like '%'||AN_PRJNAME||'%'
							AND C.STATECODE = 0
							--AND  B.IFLOWID NOT IN (SELECT DISTINCT I.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO I, IEAI_WORKFLOWINSTANCE W WHERE I.IMAINFLOWID = W.IFLOWID AND W.Iprojectname NOT IN (SELECT DISTINCT PRJNAME FROM TMP_USER_PRJ_VALIDATE))
							AND  B.IFLOWID NOT IN  (SELECT  CI.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CI WHERE CI.ICALLFLOWID NOT IN (SELECT  CII.ICALLFLOWID FROM IEAI_CALLWORKFLOW_INFO CII , IEAI_WORKFLOWINSTANCE TT WHERE CII.IMAINFLOWID=TT.IFLOWID AND TT.ISTATUS=2 ))
							) WWW
						) WW
					WHERE WW.ROWN > AN_START
					AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				END IF;
			END IF;
		END
        $

CREATE OR REPLACE PROCEDURE PROC_ACTLIST_SDPAGEBYID        (IN AN_USERID NUMERIC(19,0),IN AN_START NUMERIC(19,0),IN AN_PAGESIZE NUMERIC(19,0),IN AN_PRJNAME VARCHAR(255),OUT AN_TOTAL NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0),IN AN_FLOWID NUMERIC(19,0))
		LANGUAGE SQL
		BEGIN
			DECLARE	LN_OPT_ID	NUMERIC(19,0);
			DECLARE	AN_PROTYPE	NUMERIC(19,0);
			DECLARE LN_ISALL    NUMERIC(2,0);
			SET AN_PROTYPE = 1;


			SELECT COUNT(1)  INTO LN_ISALL
						FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
						WHERE T.IROLEID = T2.IROLEID   AND T2.IPERMISSION = 1
						AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;

			CALL	PROC_GET_NEXT_ID ('TEMP_IEAI_PERMIT_VALIDATE', LN_OPT_ID);
			SET	AN_OPT_ID = LN_OPT_ID;
			IF  LN_ISALL>0 THEN
					IF (AN_PRJNAME IS NULL) THEN
						SELECT COUNT(1)
						INTO AN_TOTAL
						FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
						WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND C.STATECODE = 0
							AND  B.IFLOWID = AN_FLOWID;


							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID,FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME,
											WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
						FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME,
														WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
											FROM (
									SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
															LN_OPT_ID AS OPT_ID,
															C.IID,
															B.IFLOWID,
															C.IACTID,
															C.ITASKID,
															B.IFLOWINSNAME,
															C.IACTNAME,
															C.IDESC,
															C.ISHOULDSTARTTIME,
															C.IBEGINEXCTIME,
															C.ISTATE,
															(SELECT T1.IOWNER
																FROM IEAI_TASKRUNTIME T1
															WHERE T1.IID = C.ITASKID) AS IOWNER,
															T.INAME,
															C.IACTTYPE,
															B.ISTARTUSERFULLNAME,
															C.IERRORTASKID,
															C.IREXECREQUESTID,
															C.IACTDEFNAME,
															B.IFLOWNAME,
															C.ISHOULDENDTIME,
																C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
														FROM IEAI_WORKFLOWINSTANCE B,
															IEAI_ACTRUNTIME       C,
															IEAI_PROJECT          T
													WHERE B.IPRJLATESTID = T.IID
														AND T.PROTYPE = 1
														AND B.ISTATUS = 0
														AND C.IFLOWID = B.IFLOWID
														AND C.DISAPPEAR = 0
														AND C.IISMONITORACT = 0
														AND C.ACTTYPECODE = 0
														AND C.STATECODE = 0
														AND  B.IFLOWID = AN_FLOWID
												) WWW
										) WW
							WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					ELSE
								SELECT COUNT(1)
								INTO AN_TOTAL
								FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
								WHERE B.IPRJLATESTID = T.IID
								AND T.PROTYPE = 1
								AND B.ISTATUS = 0
								AND C.IFLOWID = B.IFLOWID
								AND C.DISAPPEAR = 0
								AND C.IISMONITORACT = 0
								AND C.ACTTYPECODE = 0
								AND T.INAME like '%'||AN_PRJNAME||'%'
								AND C.STATECODE = 0;

								INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
								SELECT WW.OPT_ID,
										WW.IID,
										WW.IFLOWID,
										WW.IACTID,
										WW.ITASKID,
										WW.IFLOWINSNAME,
										WW.IACTNAME,
										WW.IDESC,
										WW.ISHOULDSTARTTIME,
										WW.IBEGINEXCTIME,
										WW.ISTATE,
										WW.IOWNER,
										WW.INAME,
										WW.IACTTYPE,
										WW.ISTARTUSERFULLNAME,
										WW.IERRORTASKID,
										WW.IREXECREQUESTID,
										WW.IACTDEFNAME,
										WW.IFLOWNAME,
										WW.ISHOULDENDTIME,
											WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
									FROM (SELECT WWW.ROWN,
													WWW.IID,
													WWW.OPT_ID,
													WWW.IFLOWID,
													WWW.IACTID,
													WWW.ITASKID,
													WWW.IFLOWINSNAME,
													WWW.IACTNAME,
													WWW.IDESC,
													WWW.ISHOULDSTARTTIME,
													WWW.IBEGINEXCTIME,
													WWW.ISTATE,
													WWW.IOWNER,
													WWW.INAME,
													WWW.IACTTYPE,
													WWW.ISTARTUSERFULLNAME,
													WWW.IERRORTASKID,
													WWW.IREXECREQUESTID,
													WWW.IACTDEFNAME,
													WWW.IFLOWNAME,
													WWW.ISHOULDENDTIME,
														WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
																			FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
												LN_OPT_ID AS OPT_ID,
												C.IID,
												B.IFLOWID,
												C.IACTID,
												C.ITASKID,
												B.IFLOWINSNAME,
												C.IACTNAME,
												C.IDESC,
												C.ISHOULDSTARTTIME,
												C.IBEGINEXCTIME,
												C.ISTATE,
												(SELECT T1.IOWNER
													FROM IEAI_TASKRUNTIME T1
													WHERE T1.IID = C.ITASKID) AS IOWNER,
												T.INAME,
												C.IACTTYPE,
												B.ISTARTUSERFULLNAME,
												C.IERRORTASKID,
												C.IREXECREQUESTID,
												C.IACTDEFNAME,
												B.IFLOWNAME,
												C.ISHOULDENDTIME,
													C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
											FROM IEAI_WORKFLOWINSTANCE B,
												IEAI_ACTRUNTIME       C,
												IEAI_PROJECT          T
											WHERE B.IPRJLATESTID = T.IID
											AND T.PROTYPE = 1
											AND B.ISTATUS = 0
											AND C.IFLOWID = B.IFLOWID
											AND C.DISAPPEAR = 0
											AND C.IISMONITORACT = 0
											AND C.ACTTYPECODE = 0
											AND T.INAME like '%'||AN_PRJNAME||'%'
											AND C.STATECODE = 0
											AND  B.IFLOWID = AN_FLOWID
										) WWW
										) WW
									WHERE WW.ROWN > AN_START
									AND WW.ROWN <= (AN_START + AN_PAGESIZE);
					END IF;
			ELSE

				IF (AN_PRJNAME IS NULL) THEN

				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
					SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
										AND T2.IPERMISSION = 1
										AND T.IUSERID = AN_USERID
										AND T2.IPROID > 0) A,
									IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
								AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
							IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;



					SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0
						 AND  B.IFLOWID = AN_FLOWID;

					INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
					SELECT WW.OPT_ID,
							WW.IID,
							WW.IFLOWID,
							WW.IACTID,
							WW.ITASKID,
							WW.IFLOWINSNAME,
							WW.IACTNAME,
							WW.IDESC,
							WW.ISHOULDSTARTTIME,
							WW.IBEGINEXCTIME,
							WW.ISTATE,
							WW.IOWNER,
							WW.INAME,
							WW.IACTTYPE,
							WW.ISTARTUSERFULLNAME,
							WW.IERRORTASKID,
							WW.IREXECREQUESTID,
							WW.IACTDEFNAME,
							WW.IFLOWNAME,
							WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
						FROM (SELECT WWW.ROWN,
								WWW.OPT_ID,
								WWW.IID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME,
										WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
							FROM IEAI_TASKRUNTIME T1
							WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME,
									C.ISHELLNAME,
									C.IAGENTIP,
									C.ISHELLPATH
					FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
					WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND C.STATECODE = 0
						 AND  B.IFLOWID = AN_FLOWID
						) WWW
					) WW
			WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);

			ELSE

				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID,PRJNAME)
				SELECT D.IUPPERID,D.INAME
					FROM (SELECT B.IUPPERID, MAX(B.IID) IID
							FROM (SELECT T2.IPROID AS IPROID
									FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
									WHERE T.IROLEID = T2.IROLEID
									AND T2.IPERMISSION = 1
									AND T.IUSERID = AN_USERID
									AND T2.IPROID > 0) A,
								IEAI_PROJECT B
							WHERE A.IPROID = B.IUPPERID
							AND B.IGROUPID = 1
							GROUP BY IUPPERID) C,
						IEAI_PROJECT D
					WHERE C.IID = D.IID
					ORDER BY D.INAME;

				SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME like '%'||AN_PRJNAME||'%' AND C.STATECODE = 0
					 AND  B.IFLOWID = AN_OPT_ID;

				INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
				SELECT WW.OPT_ID,
						WW.IID,
						WW.IFLOWID,
						WW.IACTID,
						WW.ITASKID,
						WW.IFLOWINSNAME,
						WW.IACTNAME,
						WW.IDESC,
						WW.ISHOULDSTARTTIME,
						WW.IBEGINEXCTIME,
						WW.ISTATE,
						WW.IOWNER,
						WW.INAME,
						WW.IACTTYPE,
						WW.ISTARTUSERFULLNAME,
						WW.IERRORTASKID,
						WW.IREXECREQUESTID,
						WW.IACTDEFNAME,
						WW.IFLOWNAME,
						WW.ISHOULDENDTIME,
								WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
					FROM (SELECT WWW.ROWN,
								WWW.IID,
								WWW.OPT_ID,
								WWW.IFLOWID,
								WWW.IACTID,
								WWW.ITASKID,
								WWW.IFLOWINSNAME,
								WWW.IACTNAME,
								WWW.IDESC,
								WWW.ISHOULDSTARTTIME,
								WWW.IBEGINEXCTIME,
								WWW.ISTATE,
								WWW.IOWNER,
								WWW.INAME,
								WWW.IACTTYPE,
								WWW.ISTARTUSERFULLNAME,
								WWW.IERRORTASKID,
								WWW.IREXECREQUESTID,
								WWW.IACTDEFNAME,
								WWW.IFLOWNAME,
								WWW.ISHOULDENDTIME,
											WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
						FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
								LN_OPT_ID AS OPT_ID,
								C.IID,
								B.IFLOWID,
								C.IACTID,
								C.ITASKID,
								B.IFLOWINSNAME,
								C.IACTNAME,
								C.IDESC,
								C.ISHOULDSTARTTIME,
								C.IBEGINEXCTIME,
								C.ISTATE,
								(SELECT T1.IOWNER
									FROM IEAI_TASKRUNTIME T1
									WHERE T1.IID = C.ITASKID) AS IOWNER,
								T.INAME,
								C.IACTTYPE,
								B.ISTARTUSERFULLNAME,
								C.IERRORTASKID,
								C.IREXECREQUESTID,
								C.IACTDEFNAME,
								B.IFLOWNAME,
								C.ISHOULDENDTIME,
										C.ISHELLNAME,
										C.IAGENTIP,
										C.ISHELLPATH
							FROM TMP_USER_PRJ_VALIDATE PR,
								IEAI_WORKFLOWINSTANCE B,
								IEAI_ACTRUNTIME       C,
								IEAI_PROJECT          T
							WHERE PR.PRJID = T.IUPPERID
							AND B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME like '%'||AN_PRJNAME||'%'
							AND C.STATECODE = 0
							AND  B.IFLOWID = AN_FLOWID
							) WWW
						) WW
					WHERE WW.ROWN > AN_START
					AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				END IF;
			END IF;
		END
		$




	CREATE OR REPLACE PROCEDURE PROC_ACTLIST_PAGE   (IN AN_USERID NUMERIC(19,0),IN AN_START NUMERIC(19,0),IN AN_PAGESIZE NUMERIC(19,0),IN AN_PRJNAME VARCHAR(255),OUT AN_TOTAL NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0))
	LANGUAGE SQL
	BEGIN
		DECLARE	LN_OPT_ID	NUMERIC(19,0);
		DECLARE	AN_PROTYPE	NUMERIC(19,0);
		DECLARE LN_ISALL    NUMERIC(2,0);
		SET AN_PROTYPE = 1;


		SELECT COUNT(1)  INTO LN_ISALL
					FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
					WHERE T.IROLEID = T2.IROLEID   AND T2.IPERMISSION = 1
					AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;

		CALL	PROC_GET_NEXT_ID ('TEMP_IEAI_PERMIT_VALIDATE', LN_OPT_ID);
		SET	AN_OPT_ID = LN_OPT_ID;
		IF  LN_ISALL>0 THEN
				IF (AN_PRJNAME IS NULL) THEN
					SELECT COUNT(1)
					INTO AN_TOTAL
					FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
					WHERE B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND C.STATECODE = 0;


						INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID,FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
						SELECT WW.OPT_ID,
								WW.IID,
								WW.IFLOWID,
								WW.IACTID,
								WW.ITASKID,
								WW.IFLOWINSNAME,
								WW.IACTNAME,
								WW.IDESC,
								WW.ISHOULDSTARTTIME,
								WW.IBEGINEXCTIME,
								WW.ISTATE,
								WW.IOWNER,
								WW.INAME,
								WW.IACTTYPE,
								WW.ISTARTUSERFULLNAME,
								WW.IERRORTASKID,
								WW.IREXECREQUESTID,
								WW.IACTDEFNAME,
								WW.IFLOWNAME,
								WW.ISHOULDENDTIME,
								WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
					FROM (SELECT WWW.ROWN,
												WWW.IID,
												WWW.OPT_ID,
												WWW.IFLOWID,
												WWW.IACTID,
												WWW.ITASKID,
												WWW.IFLOWINSNAME,
												WWW.IACTNAME,
												WWW.IDESC,
												WWW.ISHOULDSTARTTIME,
												WWW.IBEGINEXCTIME,
												WWW.ISTATE,
												WWW.IOWNER,
												WWW.INAME,
												WWW.IACTTYPE,
												WWW.ISTARTUSERFULLNAME,
												WWW.IERRORTASKID,
												WWW.IREXECREQUESTID,
												WWW.IACTDEFNAME,
												WWW.IFLOWNAME,
												WWW.ISHOULDENDTIME,
													WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
										FROM (
								SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
														LN_OPT_ID AS OPT_ID,
														C.IID,
														B.IFLOWID,
														C.IACTID,
														C.ITASKID,
														B.IFLOWINSNAME,
														C.IACTNAME,
														C.IDESC,
														C.ISHOULDSTARTTIME,
														C.IBEGINEXCTIME,
														C.ISTATE,
														(SELECT T1.IOWNER
															FROM IEAI_TASKRUNTIME T1
														WHERE T1.IID = C.ITASKID) AS IOWNER,
														T.INAME,
														C.IACTTYPE,
														B.ISTARTUSERFULLNAME,
														C.IERRORTASKID,
														C.IREXECREQUESTID,
														C.IACTDEFNAME,
														B.IFLOWNAME,
														C.ISHOULDENDTIME,
																C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
													FROM IEAI_WORKFLOWINSTANCE B,
														IEAI_ACTRUNTIME       C,
														IEAI_PROJECT          T
												WHERE B.IPRJLATESTID = T.IID
													AND T.PROTYPE = 1
													AND B.ISTATUS = 0
													AND C.IFLOWID = B.IFLOWID
													AND C.DISAPPEAR = 0
													AND C.IISMONITORACT = 0
													AND C.ACTTYPECODE = 0
													AND C.STATECODE = 0
											) WWW
									) WW
						WHERE WW.ROWN > AN_START
							AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				ELSE
							SELECT COUNT(1)
							INTO AN_TOTAL
							FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
							WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME like '%'||AN_PRJNAME||'%'
							AND C.STATECODE = 0;

							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
								FROM (SELECT WWW.ROWN,
												WWW.IID,
												WWW.OPT_ID,
												WWW.IFLOWID,
												WWW.IACTID,
												WWW.ITASKID,
												WWW.IFLOWINSNAME,
												WWW.IACTNAME,
												WWW.IDESC,
												WWW.ISHOULDSTARTTIME,
												WWW.IBEGINEXCTIME,
												WWW.ISTATE,
												WWW.IOWNER,
												WWW.INAME,
												WWW.IACTTYPE,
												WWW.ISTARTUSERFULLNAME,
												WWW.IERRORTASKID,
												WWW.IREXECREQUESTID,
												WWW.IACTDEFNAME,
												WWW.IFLOWNAME,
												WWW.ISHOULDENDTIME,
															WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
																		FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
											LN_OPT_ID AS OPT_ID,
											C.IID,
											B.IFLOWID,
											C.IACTID,
											C.ITASKID,
											B.IFLOWINSNAME,
											C.IACTNAME,
											C.IDESC,
											C.ISHOULDSTARTTIME,
											C.IBEGINEXCTIME,
											C.ISTATE,
											(SELECT T1.IOWNER
												FROM IEAI_TASKRUNTIME T1
												WHERE T1.IID = C.ITASKID) AS IOWNER,
											T.INAME,
											C.IACTTYPE,
											B.ISTARTUSERFULLNAME,
											C.IERRORTASKID,
											C.IREXECREQUESTID,
											C.IACTDEFNAME,
											B.IFLOWNAME,
											C.ISHOULDENDTIME,
													C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
										FROM IEAI_WORKFLOWINSTANCE B,
											IEAI_ACTRUNTIME       C,
											IEAI_PROJECT          T
										WHERE B.IPRJLATESTID = T.IID
										AND T.PROTYPE = 1
										AND B.ISTATUS = 0
										AND C.IFLOWID = B.IFLOWID
										AND C.DISAPPEAR = 0
										AND C.IISMONITORACT = 0
										AND C.ACTTYPECODE = 0
										AND T.INAME like '%'||AN_PRJNAME||'%'
										AND C.STATECODE = 0
									) WWW
									) WW
								WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				END IF;
		ELSE

			IF (AN_PRJNAME IS NULL) THEN

			INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID)
				SELECT D.IUPPERID
				FROM (SELECT B.IUPPERID, MAX(B.IID) IID
						FROM (SELECT T2.IPROID AS IPROID
								FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
								WHERE T.IROLEID = T2.IROLEID
									AND T2.IPERMISSION = 1
									AND T.IUSERID = AN_USERID
									AND T2.IPROID > 0) A,
								IEAI_PROJECT B
						WHERE A.IPROID = B.IUPPERID
							AND B.IGROUPID = 1
						GROUP BY IUPPERID) C,
						IEAI_PROJECT D
				WHERE C.IID = D.IID
				ORDER BY D.INAME;



				SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0;

				INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
				SELECT WW.OPT_ID,
						WW.IID,
						WW.IFLOWID,
						WW.IACTID,
						WW.ITASKID,
						WW.IFLOWINSNAME,
						WW.IACTNAME,
						WW.IDESC,
						WW.ISHOULDSTARTTIME,
						WW.IBEGINEXCTIME,
						WW.ISTATE,
						WW.IOWNER,
						WW.INAME,
						WW.IACTTYPE,
						WW.ISTARTUSERFULLNAME,
						WW.IERRORTASKID,
						WW.IREXECREQUESTID,
						WW.IACTDEFNAME,
						WW.IFLOWNAME,
						WW.ISHOULDENDTIME,
						WW.ISHELLNAME,
						WW.IAGENTIP,
						WW.ISHELLPATH
					FROM (SELECT WWW.ROWN,
							WWW.OPT_ID,
							WWW.IID,
							WWW.IFLOWID,
							WWW.IACTID,
							WWW.ITASKID,
							WWW.IFLOWINSNAME,
							WWW.IACTNAME,
							WWW.IDESC,
							WWW.ISHOULDSTARTTIME,
							WWW.IBEGINEXCTIME,
							WWW.ISTATE,
							WWW.IOWNER,
							WWW.INAME,
							WWW.IACTTYPE,
							WWW.ISTARTUSERFULLNAME,
							WWW.IERRORTASKID,
							WWW.IREXECREQUESTID,
							WWW.IACTDEFNAME,
							WWW.IFLOWNAME,
							WWW.ISHOULDENDTIME,
							WWW.ISHELLNAME,
							WWW.IAGENTIP,
							WWW.ISHELLPATH
					FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
						LN_OPT_ID AS OPT_ID,
						C.IID,
						B.IFLOWID,
						C.IACTID,
						C.ITASKID,
						B.IFLOWINSNAME,
						C.IACTNAME,
						C.IDESC,
						C.ISHOULDSTARTTIME,
						C.IBEGINEXCTIME,
						C.ISTATE,
						(SELECT T1.IOWNER
						FROM IEAI_TASKRUNTIME T1
						WHERE T1.IID = C.ITASKID) AS IOWNER,
						T.INAME,
						C.IACTTYPE,
						B.ISTARTUSERFULLNAME,
						C.IERRORTASKID,
						C.IREXECREQUESTID,
						C.IACTDEFNAME,
						B.IFLOWNAME,
						C.ISHOULDENDTIME,
							C.ISHELLNAME,
							C.IAGENTIP,
							C.ISHELLPATH
				FROM TMP_USER_PRJ_VALIDATE PR,
						IEAI_WORKFLOWINSTANCE B,
						IEAI_ACTRUNTIME       C,
						IEAI_PROJECT          T
				WHERE PR.PRJID = T.IUPPERID
					AND B.IPRJLATESTID = T.IID
					AND T.PROTYPE = 1
					AND B.ISTATUS = 0
					AND C.IFLOWID = B.IFLOWID
					AND C.DISAPPEAR = 0
					AND C.IISMONITORACT = 0
					AND C.ACTTYPECODE = 0
					AND C.STATECODE = 0
					) WWW
				) WW
		WHERE WW.ROWN > AN_START
			AND WW.ROWN <= (AN_START + AN_PAGESIZE);

		ELSE

			INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID)
			SELECT D.IUPPERID
				FROM (SELECT B.IUPPERID, MAX(B.IID) IID
						FROM (SELECT T2.IPROID AS IPROID
								FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
								WHERE T.IROLEID = T2.IROLEID
								AND T2.IPERMISSION = 1
								AND T.IUSERID = AN_USERID
								AND T2.IPROID > 0) A,
							IEAI_PROJECT B
						WHERE A.IPROID = B.IUPPERID
						AND B.IGROUPID = 1
						GROUP BY IUPPERID) C,
					IEAI_PROJECT D
				WHERE C.IID = D.IID
				ORDER BY D.INAME;

			SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME like '%'||AN_PRJNAME||'%' AND C.STATECODE = 0;

			INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
			SELECT WW.OPT_ID,
					WW.IID,
					WW.IFLOWID,
					WW.IACTID,
					WW.ITASKID,
					WW.IFLOWINSNAME,
					WW.IACTNAME,
					WW.IDESC,
					WW.ISHOULDSTARTTIME,
					WW.IBEGINEXCTIME,
					WW.ISTATE,
					WW.IOWNER,
					WW.INAME,
					WW.IACTTYPE,
					WW.ISTARTUSERFULLNAME,
					WW.IERRORTASKID,
					WW.IREXECREQUESTID,
					WW.IACTDEFNAME,
					WW.IFLOWNAME,
					WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
				FROM (SELECT WWW.ROWN,
							WWW.IID,
							WWW.OPT_ID,
							WWW.IFLOWID,
							WWW.IACTID,
							WWW.ITASKID,
							WWW.IFLOWINSNAME,
							WWW.IACTNAME,
							WWW.IDESC,
							WWW.ISHOULDSTARTTIME,
							WWW.IBEGINEXCTIME,
							WWW.ISTATE,
							WWW.IOWNER,
							WWW.INAME,
							WWW.IACTTYPE,
							WWW.ISTARTUSERFULLNAME,
							WWW.IERRORTASKID,
							WWW.IREXECREQUESTID,
							WWW.IACTDEFNAME,
							WWW.IFLOWNAME,
							WWW.ISHOULDENDTIME,
								WWW.ISHELLNAME,
								WWW.IAGENTIP,
								WWW.ISHELLPATH
					FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
								FROM IEAI_TASKRUNTIME T1
								WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME,
								C.ISHELLNAME,
								C.IAGENTIP,
								C.ISHELLPATH
						FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
						WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND T.INAME like '%'||AN_PRJNAME||'%'
						AND C.STATECODE = 0
						) WWW
					) WW
				WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);
			END IF;
		END IF;
	END
	$

	BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);

			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_IEAI_OVERTIME_FORMAP';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_IEAI_OVERTIME_FORMAP(IID DECIMAL(19) NOT NULL, IWARNID DECIMAL(19), IHANDID DECIMAL(19),CONSTRAINT PK_IEAI_IEAI_OVERTIME_FORMAP PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
	END
	$


	CREATE OR REPLACE PROCEDURE PROC_LOOKMONITOR_BASEQUERY (IN DATADATE VARCHAR(15),IN URERROFILTER VARCHAR(2000))
	LANGUAGE SQL
		BEGIN
			DECLARE	LN_OPT_ID	NUMERIC(19,0);	--　　　　ID
		     DECLARE	TEMPPRJNAME  VARCHAR(255);
		     DECLARE	TEMPFLOWNAME VARCHAR(255);
		     DECLARE	TEMPFLOWID     NUMERIC(19,0);
		     DECLARE	TEMPOVERFLOWID NUMERIC(19,0);
		     DECLARE	TEMPOVERSTATE  NUMERIC(4,0);
		     DECLARE	TEMPCOUNT       NUMERIC(19,0);
		     DECLARE	TEMPSTARTTIME   NUMERIC(19,0);
		     DECLARE	TEMPENDTIME     NUMERIC(19,0);
		     DECLARE	TEMPERRORTIME   NUMERIC(19,0);
		     DECLARE	TEMPFLOWISTATUS NUMERIC(4,0);
		     DECLARE	 MAXFLOWID       NUMERIC(19,0);
		     DECLARE	MINFLOWID       NUMERIC(19,0);
		     DECLARE	I       NUMERIC(19,0);
		     DECLARE	LN_KEY			NUMERIC(19,0);
		     DECLARE	LI_ROWCNT		INTEGER;
		     DECLARE    UTERRORSQL      VARCHAR(4000);
             DECLARE     t_cur       CURSOR FOR t_stmt;

		     select count(1) into LI_ROWCNT FROM IEAI_WORKFLOW FLOW, IEAI_PROJECT P
		       WHERE FLOW.IFLOWID = FLOW.ILATESTID
		         AND FLOW.ISTATUS = 1
		         AND P.IID=P.ILATESTID
		         AND FLOW.IPRJID = P.IID;
		        IF	LI_ROWCNT = 0 THEN
		               RETURN;
		           END IF;
		            CALL	PROC_GET_MULT_ID ('TEMP_LOOKMONITOR_BASE', LI_ROWCNT, LN_KEY);
		           -- get primary key failure
		           IF LN_KEY IS NULL THEN
		               RETURN;
		           END IF;
		     --CALL	PROC_GET_NEXT_ID ('TEMP_LOOKMONITOR_BASE', LN_OPT_ID);
		     INSERT INTO TEMP_LOOKMONITOR_BASE
		       (IID,
		       IFLOWID,
		       IFLOWOWNID,
		       IFLOWUPPERID,
		       IPRJID,
		       IPRJUPPERID,
		       IPRJNAME,
		       IFLOWNAME,
		       ISTARTTIME,
		       IENDTIME,
		       ISTATUS,
		       IDATADATE)
		       SELECT ROW_NUMBER()OVER() + LN_KEY - 1,
		             FLOW.IFLOWID,
		             FLOW.IFLOWID,
		             FLOW.IUPPERID,
		             FLOW.IPRJID,
		             FLOW.IPRJUPPERID,
		             P.INAME,
		             FLOW.IFLOWNAME,
		             0,
		             0,
		             0,
		             DATADATE
		         FROM IEAI_WORKFLOW FLOW, IEAI_PROJECT P
		       WHERE FLOW.IFLOWID = FLOW.ILATESTID
		         AND FLOW.ISTATUS = 1
		         AND P.IID=P.ILATESTID
		         AND FLOW.IPRJID = P.IID;
		     SELECT MAX(IID) INTO MAXFLOWID FROM TEMP_LOOKMONITOR_BASE;
		     SELECT MIN(IID) INTO MINFLOWID FROM TEMP_LOOKMONITOR_BASE;
		     SET I=MINFLOWID;
		     WHILE I<=MAXFLOWID DO
		         SELECT BA.IPRJNAME, BA.IFLOWNAME
		           INTO TEMPPRJNAME, TEMPFLOWNAME
		           FROM TEMP_LOOKMONITOR_BASE BA
		         WHERE BA.IID = I;
		         SET TEMPCOUNT = 0;
		         SELECT COUNT(1)
		           INTO TEMPCOUNT
		           FROM IEAI_WORKFLOWINSTANCE T
		         WHERE T.IFLOWINSNAME = DATADATE
		           AND T.IFLOWNAME = TEMPFLOWNAME
		           AND T.IPROJECTNAME = TEMPPRJNAME
		           AND T.ISTATUS NOT IN (2, 4, 7, 25);
		           --更新为正在运行
		         IF TEMPCOUNT > 0 THEN
		           SELECT IFLOWID, ISTARTTIME, IENDTIME, ISTATUS
		             INTO TEMPFLOWID, TEMPSTARTTIME, TEMPENDTIME, TEMPFLOWISTATUS
		             FROM IEAI_WORKFLOWINSTANCE
		           WHERE IFLOWID =
		                 (SELECT MAX(T.IFLOWID)
		                     FROM IEAI_WORKFLOWINSTANCE T
		                   WHERE T.IPROJECTNAME = TEMPPRJNAME
		                     AND T.IFLOWNAME = TEMPFLOWNAME
		                     AND T.IFLOWINSNAME = DATADATE
		                     AND T.ISTATUS NOT IN (2, 4, 7, 25));
		           IF TEMPFLOWISTATUS = 0 THEN
		             UPDATE TEMP_LOOKMONITOR_BASE JN
		               SET JN.ISTATUS = 1, JN.ISTARTTIME = TEMPSTARTTIME
		             WHERE JN.IID = I;
		           END IF;
		         END IF;

		         --查看是否发生了异常
		         SET TEMPCOUNT = 0;
		         SELECT COUNT(1)
		           INTO TEMPCOUNT
		           FROM IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
		         WHERE (T2.IACTTYPE = 'Common' OR T2.IACTTYPE = 'CallFlow')
		           AND (T2.ISTATE IN ('Fail', 'ManualRunning', 'Fail:Business'))
		           AND T1.ISTATUS NOT IN (2, 4, 7, 25)
		           AND T1.IPROJECTNAME = TEMPPRJNAME
		           AND T1.IFLOWNAME = TEMPFLOWNAME
		           AND T1.IFLOWINSNAME = DATADATE
		           AND T1.IFLOWID = T2.IFLOWID;
		           IF TEMPCOUNT > 0 THEN
		           SELECT MAX(T1.IFLOWID)
		             INTO TEMPFLOWID
		             FROM IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
		           WHERE (T2.IACTTYPE = 'Common' OR T2.IACTTYPE = 'CallFlow')
		             AND (T2.ISTATE IN ('Fail', 'ManualRunning', 'Fail:Business'))
		             AND T1.ISTATUS NOT IN (2, 4, 7, 25)
		             AND T1.IPROJECTNAME = TEMPPRJNAME
		             AND T1.IFLOWNAME = TEMPFLOWNAME
		             AND T1.IFLOWINSNAME = DATADATE
		             AND T1.IFLOWID = T2.IFLOWID;
		           SELECT ER.ICREATEDTIME
		             INTO TEMPERRORTIME
		             FROM IEAI_ACTRUNTIME AC, IEAI_ERRORTASK ER
		           WHERE AC.IID =
		                 (SELECT MAX(T2.IID)
		                     FROM IEAI_ACTRUNTIME T2
		                   WHERE (T2.IACTTYPE = 'Common' OR T2.IACTTYPE = 'CallFlow')
		                     AND (T2.ISTATE IN
		                         ('Fail', 'ManualRunning', 'Fail:Business'))
		                     AND T2.IFLOWID = TEMPFLOWID)
		             AND AC.IERRORTASKID = ER.IID;
		           UPDATE TEMP_LOOKMONITOR_BASE JN
		             SET JN.ISTATUS = 3, JN.IEXCEPTIONTIME = TEMPERRORTIME
		           WHERE JN.IID = I;
		         END IF;
				 SET  TEMPCOUNT = 0;
		         IF URERROFILTER IS NOT NULL AND LENGTH(URERROFILTER) > 0 THEN
		            SET UTERRORSQL = 'SELECT COUNT(1) FROM  IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
		                WHERE T2.IACTDEFNAME = ''UserTask'' ' ||
		                ' AND T1.ISTATUS NOT IN (2, 4, 7, 25)
		                AND T1.IPROJECTNAME = '''  ||
		                TEMPPRJNAME  || '''
		                AND T1.IFLOWNAME = '''  ||
		                TEMPFLOWNAME  || '''
		                AND T1.IFLOWINSNAME = '''  ||
		                DATADATE  || '''
		                                    AND T1.IFLOWID = T2.IFLOWID
		                                    AND ' || URERROFILTER;
                                            PREPARE t_stmt FROM UTERRORSQL;
                                            OPEN t_cur;
                                            FETCH t_cur INTO TEMPCOUNT;
                                            CLOSE t_cur;
		                            IF TEMPCOUNT > 0 THEN
		                                SET UTERRORSQL = 'SELECT IARRIVEDTIME FROM (SELECT IARRIVEDTIME,ROW_NUMBER()OVER() RN FROM  IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
		                                  WHERE T2.IACTDEFNAME =''UserTask'''  ||
		                                              ' AND T1.ISTATUS NOT IN (2, 4, 7, 25)
		                                    AND T1.IPROJECTNAME = '''  ||
		                                              TEMPPRJNAME ||  '''
		                                    AND T1.IFLOWNAME = '''  ||
		                                              TEMPFLOWNAME ||  '''
		                                    AND T1.IFLOWINSNAME = '''  ||
		                                              DATADATE  || '''
		                                    AND T1.IFLOWID = T2.IFLOWID
		                                    AND ' || URERROFILTER ||
		                                              ' ORDER BY T2.IID DESC) WHERE RN=1 ';
                                            PREPARE t_stmt FROM UTERRORSQL;
                                            OPEN t_cur;
                                            FETCH t_cur INTO TEMPERRORTIME;
                                            CLOSE t_cur;
		                                UPDATE TEMP_LOOKMONITOR_BASE JN
		                                  SET JN.ISTATUS = 3, JN.IEXCEPTIONTIME = TEMPERRORTIME
		                                WHERE JN.IID = I;
		                            END IF;
		                        END IF;
		         --查看是否已经结束
		         SET TEMPCOUNT = 0;
		         SELECT COUNT(1)
		           INTO TEMPCOUNT
		           FROM IEAI_WORKFLOWINSTANCE T
		         WHERE T.IFLOWINSNAME = DATADATE
		           AND T.IFLOWNAME = TEMPFLOWNAME
		           AND T.IPROJECTNAME = TEMPPRJNAME
		           AND T.ISTATUS IN (2, 4, 7, 25);
		           IF TEMPCOUNT > 0 THEN
		           SELECT MAX(T.IFLOWID)
		             INTO TEMPOVERFLOWID
		             FROM IEAI_WORKFLOWINSTANCE T
		           WHERE T.IFLOWINSNAME = DATADATE
		             AND T.IFLOWNAME = TEMPFLOWNAME
		             AND T.IPROJECTNAME = TEMPPRJNAME;
		           SELECT T.ISTATUS
		             INTO TEMPOVERSTATE
		             FROM IEAI_WORKFLOWINSTANCE T
		           WHERE T.IFLOWID = TEMPOVERFLOWID;
		           IF TEMPOVERSTATE = 2 OR TEMPOVERSTATE = 4 OR TEMPOVERSTATE = 7 OR
		             TEMPOVERSTATE = 25 THEN
		             SELECT IFLOWID, ISTARTTIME, IENDTIME, ISTATUS
		               INTO TEMPFLOWID, TEMPSTARTTIME, TEMPENDTIME, TEMPFLOWISTATUS
		               FROM IEAI_WORKFLOWINSTANCE T
		             WHERE T.IFLOWID = TEMPOVERFLOWID;
		             UPDATE TEMP_LOOKMONITOR_BASE JN
		               SET JN.ISTATUS    = 2,
		                   JN.ISTARTTIME = TEMPSTARTTIME,
		                   JN.IENDTIME   = TEMPENDTIME
		             WHERE JN.IID = I;
		           END IF;
		         END IF;
		         SET I=I+1;
		     END WHILE;
		END
	$

		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1121 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1121, ''工程暂停'', ''flowQuery/pauseProject.do'', ''作业调度--工作流查询--工程暂停'')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1121 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1121, 1003, 1121, '''')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1122 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1122, ''工程继续'', ''flowQuery/cancelPauseProject.do'', ''作业调度--工作流查询--工程继续'')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1122 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1122, 1003, 1122, '''')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1123 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1123, ''工程终止'', ''flowQuery/killProject.do'', ''作业调度--工作流查询--工程终止'')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1123 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1123, 1003, 1123, '''')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;
		END
		$


		CREATE OR REPLACE PROCEDURE PROC_RECORD_MONITOR(IN DATADATE  VARCHAR(15),IN URERROFILTER VARCHAR(2000))
		LANGUAGE SQL
		BEGIN
		DECLARE	 LN_OPT_ID	NUMERIC(19,0);
			DECLARE TEMPACTID       NUMERIC(19);
			DECLARE TEMPFLOWID      NUMERIC(19);
			DECLARE TEMPCOUNT       NUMERIC(19);
			DECLARE TEMPSTARTTIME   NUMERIC(19);
			DECLARE TEMPENDTIME     NUMERIC(19);
			DECLARE TEMPERRORTIME   NUMERIC(19);
			DECLARE TEMPFLOWISTATUS NUMERIC(4);
			DECLARE	LN_KEY			NUMERIC(19,0);
			DECLARE	LI_ROWCNT		INTEGER;
			DECLARE INSERT_FLAG     INTEGER;
			DECLARE NEWESTFLOWID NUMERIC(19,0);
            DECLARE UTERRORSQL      VARCHAR(2000);
            DECLARE     t_cur       CURSOR FOR t_stmt;
			--TEMPJOBSTATUS   NUMERIC(2); --实际状态,0为为运行，1为正在运行，2为运行结束，3为发生异常
			--CALL	PROC_GET_NEXT_ID ('TEMP_LOOKMONITOR_BASE', LN_OPT_ID);
			SET INSERT_FLAG = 0;
			SELECT count(t.iid) into LI_ROWCNT
					FROM IEAI_WORKFLOW_AVGSTART T
				WHERE T.IPRJNAME || T.IFLOWNAME NOT IN
							(SELECT JN.IPRJNAME || JN.IFLOWNAME
									FROM IEAI_WORKFLOW_JOBNUM JN
								WHERE JN.IDATADATE = DATADATE);
			IF	LI_ROWCNT = 0 THEN
				SET INSERT_FLAG = 1;
			END IF;
			-- get primary key
			CALL	PROC_GET_MULT_ID ('IEAI_WORKFLOW_JOBNUM', LI_ROWCNT, LN_KEY);
			-- get primary key failure
			IF LN_KEY IS NULL THEN
				SET INSERT_FLAG = 1;
			END IF;
			--根据数据日期插入不在监控表中的数据
			IF INSERT_FLAG=0 THEN
				INSERT INTO IEAI_WORKFLOW_JOBNUM
				(IID,
				IFLOWID,
				IFLOWOWNID,
				IFLOWUPPERID,
				IPRJID,
				IPRJUPPERID,
				IPRJNAME,
				IFLOWNAME,
				ISTARTTIME,
				IENDTIME,
				ISTATUS,
				IRECORDSTARTTIME,
				IRECORDENDTIME,
				IDATADATE,
				IHOUR,
				IQUERY,
				IRESTARTTIME)
				SELECT ROW_NUMBER()OVER() + LN_KEY - 1,
							0,
							T.IFLOWOWNID,
							T.IFLOWUPPERID,
							T.IPRJID,
							T.IPRJUPPERID,
							T.IPRJNAME,
							T.IFLOWNAME,
							0,
							0,
							0,
							0,
							0,
							DATADATE,
							TRUNC(T.IRELATIVESTIME / 1000 / 60 / 60),
							1,
							T.IRELATIVESTIME
					FROM IEAI_WORKFLOW_AVGSTART T
				WHERE T.IPRJNAME || T.IFLOWNAME NOT IN
							(SELECT JN.IPRJNAME || JN.IFLOWNAME
									FROM IEAI_WORKFLOW_JOBNUM JN
								WHERE JN.IDATADATE = DATADATE);
			END IF;
			--清理已经取消关键流程设置的数据
			DELETE FROM IEAI_WORKFLOW_JOBNUM T
			WHERE T.IDATADATE = DATADATE
				AND T.IPRJNAME || T.IFLOWNAME NOT IN
						(SELECT S.IPRJNAME || S.IFLOWNAME FROM IEAI_WORKFLOW_AVGSTART S,IEAI_PROJECT P WHERE S.IPRJID = P.IID AND P.IID = P.ILATESTID);
			--开始更新
			FOR TEMPJOB AS CURSOR1 CURSOR FOR
					SELECT IID AS PID,IPRJNAME AS PNAME ,IFLOWNAME AS FNAME FROM IEAI_WORKFLOW_JOBNUM WHERE IDATADATE = DATADATE FOR update
			DO
				SELECT MAX(FLOW.IFLOWID) INTO NEWESTFLOWID FROM IEAI_WORKFLOWINSTANCE FLOW WHERE FLOW.IFLOWINSNAME = DATADATE
						AND FLOW.IFLOWNAME = FNAME
						AND FLOW.IPROJECTNAME = PNAME;
					--未运行，查询是否运行
					SET TEMPCOUNT = 0;
					SELECT COUNT(1)
						INTO TEMPCOUNT
						FROM IEAI_WORKFLOWINSTANCE T
					WHERE T.IFLOWINSNAME = DATADATE
						AND T.IFLOWNAME = FNAME
						AND T.IPROJECTNAME = PNAME
						AND T.ISTATUS NOT IN (2, 4, 7, 25);
					--更新为正在运行
					IF TEMPCOUNT > 0 THEN
						SELECT IFLOWID, ISTARTTIME, IENDTIME, ISTATUS
							INTO TEMPFLOWID, TEMPSTARTTIME, TEMPENDTIME, TEMPFLOWISTATUS
							FROM IEAI_WORKFLOWINSTANCE
						WHERE IFLOWID =
									(SELECT MAX(T.IFLOWID)
											FROM IEAI_WORKFLOWINSTANCE T
										--, IEAI_ACTRUNTIME T2
										WHERE T.IPROJECTNAME = PNAME
											AND T.IFLOWNAME = FNAME
											AND T.IFLOWINSNAME = DATADATE
											AND T.ISTATUS NOT IN (2, 4, 7, 25));
						IF TEMPFLOWISTATUS = 0 THEN
							UPDATE IEAI_WORKFLOW_JOBNUM JN
								SET JN.ISTATUS = 1, JN.ISTARTTIME = TEMPSTARTTIME
							WHERE JN.IID=PID;
						END IF;
					END IF;
					--查看是否发生了异常
					SET TEMPCOUNT = 0;
					SET TEMPFLOWID = 0;
					SELECT COUNT(1)
						INTO TEMPCOUNT
						FROM IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
					WHERE (T2.IACTTYPE = 'Common' OR T2.IACTTYPE = 'CallFlow')
						AND (T2.ISTATE IN ('Fail', 'ManualRunning', 'Fail:Business'))
						AND T1.ISTATUS NOT IN (2, 4, 7, 25)
						AND T1.IPROJECTNAME = PNAME
						AND T1.IFLOWNAME = FNAME
						AND T1.IFLOWINSNAME = DATADATE
						AND T1.IFLOWID = T2.IFLOWID;
					IF TEMPCOUNT > 0 THEN
						SELECT MAX(T1.IFLOWID)
							INTO TEMPFLOWID
							FROM IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
						WHERE (T2.IACTTYPE = 'Common' OR T2.IACTTYPE = 'CallFlow')
							AND (T2.ISTATE IN ('Fail', 'ManualRunning', 'Fail:Business'))
							AND T1.ISTATUS NOT IN (2, 4, 7, 25)
							AND T1.IPROJECTNAME = PNAME
							AND T1.IFLOWNAME = FNAME
							AND T1.IFLOWINSNAME = DATADATE
							AND T1.IFLOWID = T2.IFLOWID;
						SELECT ER.ICREATEDTIME
							INTO TEMPERRORTIME
							FROM IEAI_ACTRUNTIME AC, IEAI_ERRORTASK ER
						WHERE AC.IID =
									(SELECT MAX(T2.IID)
											FROM IEAI_ACTRUNTIME T2
										WHERE (T2.IACTTYPE = 'Common' OR
													T2.IACTTYPE = 'CallFlow' AND
													(T2.ISTATE IN
													('Fail', 'ManualRunning', 'Fail:Business')))
											AND T2.IFLOWID = TEMPFLOWID)
							AND AC.IERRORTASKID = ER.IID;
						UPDATE IEAI_WORKFLOW_JOBNUM JN
							SET JN.ISTATUS = 3, JN.IEXCEPTIONTIME = TEMPERRORTIME
						WHERE JN.IID=PID;
					END IF;
					SET  TEMPCOUNT = 0;
		         IF URERROFILTER IS NOT NULL AND LENGTH(URERROFILTER) > 0 THEN
		            SET UTERRORSQL = 'SELECT COUNT(1) FROM  IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
		                WHERE T2.IACTDEFNAME = ''UserTask'' ' ||
		                ' AND T1.ISTATUS NOT IN (2, 4, 7, 25)
		                AND T1.IPROJECTNAME = '''  ||
		                PNAME  || '''
		                AND T1.IFLOWNAME = '''  ||
		                FNAME  || '''
		                AND T1.IFLOWINSNAME = '''  ||
		                DATADATE  || '''
		                                    AND T1.IFLOWID = T2.IFLOWID
		                                    AND ' || URERROFILTER;
                                            PREPARE t_stmt FROM UTERRORSQL;
                                            OPEN t_cur;
                                            FETCH t_cur INTO TEMPCOUNT;
                                            CLOSE t_cur;
		                            IF TEMPCOUNT > 0 THEN
		                                SET UTERRORSQL = 'SELECT IARRIVEDTIME FROM (SELECT IARRIVEDTIME,ROW_NUMBER()OVER() RN FROM  IEAI_WORKFLOWINSTANCE T1, IEAI_ACTRUNTIME T2
		                                  WHERE T2.IACTDEFNAME =''UserTask'''  ||
		                                              ' AND T1.ISTATUS NOT IN (2, 4, 7, 25)
		                                    AND T1.IPROJECTNAME = '''  ||
		                                              PNAME ||  '''
		                                    AND T1.IFLOWNAME = '''  ||
		                                              FNAME ||  '''
		                                    AND T1.IFLOWINSNAME = '''  ||
		                                              DATADATE  || '''
		                                    AND T1.IFLOWID = T2.IFLOWID
		                                    AND ' || URERROFILTER ||
		                                              ' ORDER BY T2.IID DESC) WHERE RN=1 ';
                                            PREPARE t_stmt FROM UTERRORSQL;
                                            OPEN t_cur;
                                            FETCH t_cur INTO TEMPERRORTIME;
                                            CLOSE t_cur;
		                                UPDATE IEAI_WORKFLOW_JOBNUM JN
		                                  SET JN.ISTATUS = 3, JN.IEXCEPTIONTIME = TEMPERRORTIME
										WHERE JN.IID=PID;
		                            END IF;
		                        END IF;
					--查看是否已经结束
					SET TEMPCOUNT = 0;
					SELECT COUNT(1)
						INTO TEMPCOUNT
						FROM IEAI_WORKFLOWINSTANCE T
					WHERE T.IFLOWINSNAME = DATADATE
						AND T.IFLOWNAME = FNAME
						AND T.IPROJECTNAME = PNAME
						AND T.ISTATUS IN (2, 4, 7, 25);
					IF TEMPCOUNT > 0 THEN
						SELECT IFLOWID, ISTARTTIME, IENDTIME, ISTATUS
							INTO TEMPFLOWID, TEMPSTARTTIME, TEMPENDTIME, TEMPFLOWISTATUS
							FROM IEAI_WORKFLOWINSTANCE T
						WHERE T.IFLOWID =
									(SELECT MAX(T1.IFLOWID)
											FROM IEAI_WORKFLOWINSTANCE T1
										WHERE T1.IFLOWINSNAME = DATADATE
											AND T1.IFLOWNAME = FNAME
											AND T1.IPROJECTNAME = PNAME
											AND T1.ISTATUS IN (2, 4, 7, 25));
						IF NEWESTFLOWID=TEMPFLOWID THEN
							UPDATE IEAI_WORKFLOW_JOBNUM JN
							SET JN.ISTATUS    = 2,
									JN.ISTARTTIME = TEMPSTARTTIME,
									JN.IENDTIME   = TEMPENDTIME
						WHERE JN.IID= PID;
						END IF;
					END IF;
			END FOR;
	END
	$


	 CREATE OR REPLACE PROCEDURE PROC_WORKFLOW_QUERY_KL    (IN AN_USERID  NUMERIC(19,0),OUT AN_OPT_ID  NUMERIC(19,0),IN AV_PRJ_NAME  VARCHAR(255),IN AV_FLOW_NAME  VARCHAR(255),IN AV_START_USER  VARCHAR(255),IN AV_INSTANCE_NAME  VARCHAR(255),IN AI_START_TIME_FROM  BIGINT,IN AI_START_TIME_TO  BIGINT,IN AI_END_TIME_FROM  BIGINT,IN AI_END_TIME_TO  BIGINT,IN AI_SHOW_ACTIVE  SMALLINT,IN AI_SHOW_RUN_SELF  SMALLINT,IN AI_NUM_OF_PAGE  SMALLINT,IN AI_PAGE_ID  INTEGER,OUT AI_REC_COUNT  INTEGER,IN AV_ORDER_ITEM  VARCHAR(80),IN AI_ORDER_TYPE  SMALLINT,IN AN_CHECK_DATE  NUMERIC(19,0),IN AV_HOST_NAME  VARCHAR(50), IN FLOW_ID  NUMERIC(19,0), IN SHOW_FLOW_STATE  SMALLINT,IN AV_WORKFLOW_DESC  VARCHAR(255),IN AV_ISYSTEM  VARCHAR(255),IN AI_SERACH_TYPE SMALLINT, IN AI_EXPROT_SIZE SMALLINT )
	LANGUAGE SQL

    BEGIN

    DECLARE LN_OPT_ID  NUMERIC(19,0);
    DECLARE LV_SQL    VARCHAR(4000);
    DECLARE LV_ORDER_BY  VARCHAR(100);
    DECLARE LN_MILLISEC  NUMERIC(19,0);
    DECLARE LV_WHERE  VARCHAR(4000);
    DECLARE LI_PAGE_ID  BIGINT;
    DECLARE LI_MAX_RECID  BIGINT;
    DECLARE LI_MIN_RECID  BIGINT;
    DECLARE LI_PAGE_COUNT  BIGINT;
    DECLARE LN_ISALL NUMERIC(19, 0);
    DECLARE	LI_ZONE		SMALLINT;
    DECLARE	LV_FORMAT	VARCHAR(50);

    DECLARE	SI_TIMEZONE_CUR SMALLINT DEFAULT 8;
    DECLARE	SI_TIMEZONE_GMT SMALLINT DEFAULT 0;


    IF  AI_NUM_OF_PAGE IS NULL OR AI_NUM_OF_PAGE <= 0  THEN
      RETURN;
    END  IF;

    CALL PROC_GET_NEXT_ID('TMP_USER_VALIDATE', LN_OPT_ID);
    SET	AN_OPT_ID = LN_OPT_ID;

  	SET	LI_ZONE = 0;
	SET	LV_FORMAT = 'YYYY-MM-DD HH24:MI:SS';


       SELECT COUNT(*)
            INTO LN_ISALL
       FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
       WHERE T.IROLEID = T2.IROLEID
         AND T2.IPERMISSION = 1
         AND T.IUSERID = AN_USERID
         AND T2.IPROID = -1;

      IF LN_ISALL > 0 THEN
        INSERT INTO TMP_USER_VALIDATE
          (OPT_ID, PRJ_ID, PRJ_NAME, PERMIT_ID, PERMIT_NAME)
          SELECT LN_OPT_ID AS OPT_ID,
                 IID       AS PRJ_ID,
                 INAME     AS PRJ_NAME,
                 0         AS PERMIT_ID,
                 1         AS PERMIT_NAME
            FROM IEAI_PROJECT
           WHERE PROTYPE = 1
             AND IPKGCONTENTID <> 0
             AND IUPPERID = IID;
      ELSE
        INSERT INTO TMP_USER_VALIDATE
          (OPT_ID, PRJ_ID, PRJ_NAME, PERMIT_ID, PERMIT_NAME)
          SELECT LN_OPT_ID AS OPT_ID,
                 PRJ.IID   AS PRJ_ID,
                 PRJ.INAME AS PRJ_NAME,
                 0         AS PERMIT_ID,
                 1         AS PERMIT_NAME
            FROM IEAI_PROJECT        PRJ,
                 IEAI_SYS_PERMISSION ISP,
                 IEAI_USERINHERIT    IU
           WHERE IU.IUSERID = AN_USERID
             AND ISP.IROLEID = IU.IROLEID
             AND PRJ.IID = ISP.IPROID
             AND ISP.IPROID > 0
             AND ISP.IPERMISSION = 1
             AND PRJ.PROTYPE = 1
             AND PRJ.IPKGCONTENTID <> 0;
      END IF;

        SET LN_MILLISEC = AN_CHECK_DATE;

        SET	LV_WHERE = '';

        IF	LENGTH(AV_PRJ_NAME) > 0	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.IPROJECTNAME LIKE (' || CHR(39) || '%' || AV_PRJ_NAME || '%' || CHR(39) || ')';
        END	IF;


        IF	LENGTH(AV_FLOW_NAME) > 0	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWNAME LIKE (' || CHR(39) || '%' || AV_FLOW_NAME || '%' || CHR(39) || ')';
        END	IF;


        IF	LENGTH(AV_START_USER) > 0	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTUSERFULLNAME LIKE (' || CHR(39) || '%' || AV_START_USER || '%' || CHR(39) || ')';
        END	IF;


        IF	LENGTH(AV_INSTANCE_NAME) > 0	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWINSNAME LIKE (' || CHR(39) || '%' || AV_INSTANCE_NAME || '%' || CHR(39) || ')';
        END	IF;


       IF	AI_START_TIME_FROM > 0	THEN
            IF	AI_START_TIME_TO > 0	THEN
                SET	LV_WHERE = LV_WHERE || ' AND (C.ISTARTTIME BETWEEN ' || TO_CHAR(AI_START_TIME_FROM) || ' AND ' || TO_CHAR(AI_START_TIME_TO) || ')';
            ELSE
                SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTTIME >= ' || TO_CHAR(AI_START_TIME_FROM);
            END	IF;
        ELSE
            IF	AI_START_TIME_TO > 0	THEN
                SET	LV_WHERE = LV_WHERE || ' AND C.ISTARTTIME <= ' || TO_CHAR(AI_START_TIME_TO);
            END	IF;
        END	IF;


        IF	AI_END_TIME_FROM > 0	THEN
            IF	AI_END_TIME_TO > 0	THEN
                SET	LV_WHERE = LV_WHERE || ' AND (C.IENDTIME BETWEEN ' || TO_CHAR(AI_END_TIME_FROM) || ' AND ' || TO_CHAR(AI_END_TIME_TO) || ')';
            ELSE
                SET	LV_WHERE = LV_WHERE || ' AND C.IENDTIME >= ' || TO_CHAR(AI_END_TIME_FROM);
            END	IF;
        ELSE
            IF	AI_END_TIME_TO > 0	THEN
                SET	LV_WHERE = LV_WHERE || ' AND C.IENDTIME <= ' || TO_CHAR(AI_END_TIME_TO);
            END	IF;
        END	IF;


        IF	AI_SHOW_ACTIVE = 1	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.ISTATUS IN (0, 6, 8, 15, 30)';
        END	IF;

        IF	AI_SHOW_RUN_SELF = 1	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.IISAUTOSTART = 1';
        END	IF;


        IF	LENGTH(AV_HOST_NAME) > 0	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.IHOSTNAME LIKE (' || CHR(39) || '%' || AV_HOST_NAME || '%' || CHR(39) || ')';
        END	IF;


        IF	FLOW_ID > 0	THEN
            SET	LV_WHERE = LV_WHERE || ' AND C.IFLOWID = ' || TO_CHAR(FLOW_ID);
        END	IF;

        IF  SHOW_FLOW_STATE >= 0  THEN
            SET  LV_WHERE = LV_WHERE || ' AND C.ISTATUS = (' || CHR(39)  || SHOW_FLOW_STATE  || CHR(39) || ')';
        END  IF;


        IF  LENGTH(AV_WORKFLOW_DESC) > 0  THEN
          SET LV_WHERE = LV_WHERE || ' AND C.IFLOWDES LIKE (' || CHR(39) || '%' || AV_WORKFLOW_DESC || '%' || CHR(39) || ')';
        END  IF;


        IF  LENGTH(AV_ISYSTEM) > 0  THEN
          SET  LV_WHERE = LV_WHERE || ' AND C.ISYSTEM LIKE (' || CHR(39)  || '%' || AV_ISYSTEM  || '%' || CHR(39) || ')';
        END  IF;

       SET LV_SQL = 'INSERT INTO TMP_FLOW_COUNT(IEAICOUNT) SELECT COUNT(*) FROM TMP_USER_VALIDATE A, IEAI_WORKFLOWINSTANCE C WHERE A.OPT_ID = ' || LN_OPT_ID || ' AND C.IPROJECTNAME = A.PRJ_NAME' || LV_WHERE;

       	PREPARE	SQLA FROM LV_SQL;
        EXECUTE SQLA;

        SELECT IEAICOUNT INTO LI_MAX_RECID FROM TMP_FLOW_COUNT FETCH FIRST 1 ROWS ONLY;



         SET LI_PAGE_COUNT  = TRUNC(LI_MAX_RECID / AI_NUM_OF_PAGE, 0);

        IF  MOD(LI_MAX_RECID, AI_NUM_OF_PAGE) > 0  THEN
         SET LI_PAGE_COUNT = LI_PAGE_COUNT + 1;
        END  IF;

        IF  AI_PAGE_ID > LI_PAGE_COUNT  THEN
         SET LI_PAGE_ID = LI_PAGE_COUNT;
        ELSE
         SET LI_PAGE_ID = AI_PAGE_ID;
        END  IF;

        IF  LI_PAGE_ID IS NULL OR LI_PAGE_ID < 0  THEN
         SET LI_PAGE_ID = 1;
        END  IF;

        SET AI_REC_COUNT = LI_MAX_RECID;


         IF AI_SERACH_TYPE =0 THEN

          SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
          SET LI_MAX_RECID = LI_PAGE_ID * AI_NUM_OF_PAGE;
         END IF;

         IF AI_SERACH_TYPE > 0 THEN

           SET LI_MIN_RECID = (LI_PAGE_ID - 1) * AI_NUM_OF_PAGE + 1;
           SET  LI_MAX_RECID =AI_EXPROT_SIZE+LI_MIN_RECID;
         END IF;

        IF	AI_ORDER_TYPE = 1	THEN
            SET	LV_ORDER_BY = ' ASC';
        ELSE
            SET	LV_ORDER_BY = ' DESC';
        END	IF;


        IF  LENGTH(AV_INSTANCE_NAME) > 0 or FLOW_ID > 0  THEN
            SET LV_ORDER_BY = ' ORDER BY 2 ' || LV_ORDER_BY;
            SET  LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, ROW_NUMBER() OVER('|| LV_ORDER_BY ||')  AS RID FROM (SELECT A.OPT_ID AS OPTID, ';

            IF  UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'  THEN
                SET LV_SQL = LV_SQL || 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'  THEN
                SET LV_SQL = LV_SQL || 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'FLOW_ID'  THEN
                SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'  THEN
                SET LV_SQL = LV_SQL || 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'STATUS'  THEN
                SET LV_SQL = LV_SQL || 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_USER'  THEN
                SET LV_SQL = LV_SQL || 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_TIME'  THEN
                SET LV_SQL = LV_SQL || 'C.ISTARTTIME AS ISTART_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'END_TIME'  THEN
                SET LV_SQL = LV_SQL || 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'PAID_TIME'  THEN
                SET LV_SQL = LV_SQL || '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN ' || TO_CHAR(LN_MILLISEC) || ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            ELSEIF  UPPER(AV_ORDER_ITEM) = 'TASK_NUM'  THEN
                SET LV_SQL = LV_SQL || 'NVL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ';
            ELSE

             SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID,NVL(D.TASK_NUM, 0) AS TASK_NUM ';
            END  IF;


            SET LV_SQL = LV_SQL || 'FROM TMP_USER_VALIDATE A INNER JOIN IEAI_WORKFLOWINSTANCE C ON C.IPROJECTNAME = A.PRJ_NAME LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' || TO_CHAR(LN_OPT_ID) ;
            SET LV_SQL = LV_SQL || LV_WHERE;



            SET LV_SQL = LV_SQL ||  LV_ORDER_BY || ')) WHERE RID BETWEEN ' || LI_MIN_RECID || ' AND ' || LI_MAX_RECID;
    ELSE
       SET LV_ORDER_BY = ' ORDER BY 2 ' || LV_ORDER_BY;
       SET LV_SQL = 'INSERT INTO TMP_WORKFLOW_QUERY_TEMP (OPTID, FLOW_ID, TASK_NUM, PAGE_ID, REC_ID) SELECT OPTID, FLOW_ID, TASK_NUM, PID, RID FROM (SELECT OPTID, FLOW_ID, TASK_NUM, 0 AS PID, ROW_NUMBER() OVER('|| LV_ORDER_BY ||') AS RID FROM (SELECT A.OPT_ID AS OPTID, ';


        IF  UPPER(AV_ORDER_ITEM) = 'FLOW_NAME'  THEN
             SET  LV_SQL = LV_SQL || 'C.IFLOWNAME AS FLOW_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'INSTANCE_NAME'  THEN
            SET LV_SQL = LV_SQL || 'C.IFLOWINSNAME AS INSTANCE_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'FLOW_ID'  THEN
            SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'PROJECT_NAME'  THEN
            SET LV_SQL = LV_SQL || 'C.IPROJECTNAME AS PROJECT_NAME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'STATUS'  THEN
            SET LV_SQL = LV_SQL || 'C.ISTATUS AS ISTATUS, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_USER'  THEN
            SET LV_SQL = LV_SQL || 'C.ISTARTUSERFULLNAME AS START_USER, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'START_TIME'  THEN
            SET LV_SQL = LV_SQL || 'C.ISTARTTIME AS ISTART_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'END_TIME'  THEN
            SET LV_SQL = LV_SQL || 'C.IENDTIME AS IEND_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'PAID_TIME'  THEN
            SET LV_SQL = LV_SQL || '(CASE WHEN C.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN ' || TO_CHAR(LN_MILLISEC) || ' - C.ISTARTTIME WHEN C.ISTATUS IN (2, 4, 7) THEN C.IENDTIME - C.ISTARTTIME ELSE 0 END) AS IPAID_TIME, C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        ELSEIF  UPPER(AV_ORDER_ITEM) = 'TASK_NUM'  THEN
            SET LV_SQL = LV_SQL || 'NVL(D.TASK_NUM, 0) AS TASK_NUM, C.IFLOWID AS FLOW_ID ';
        ELSE
            SET LV_ORDER_BY = ' ORDER BY 2 DESC';
            SET LV_SQL = LV_SQL || 'C.IFLOWID AS FLOW_ID, NVL(D.TASK_NUM, 0) AS TASK_NUM ';
        END  IF;

        SET LV_SQL = LV_SQL || 'FROM TMP_USER_VALIDATE A INNER JOIN IEAI_WORKFLOWINSTANCE C ON C.IPROJECTNAME = A.PRJ_NAME LEFT OUTER JOIN V_TASK_COUNT D ON D.IFLOWID = C.IFLOWID WHERE A.OPT_ID = ' || TO_CHAR(LN_OPT_ID) ;

        SET LV_SQL = LV_SQL || LV_WHERE ||  LV_ORDER_BY || ')) WHERE RID BETWEEN ' || LI_MIN_RECID || ' AND ' || LI_MAX_RECID;
    END  IF;



    PREPARE	SQLA FROM LV_SQL;
     EXECUTE SQLA;


        INSERT  INTO  TMP_WORKFLOW_QUERY
      (
        OPTID,
        FLOW_ID,
        FLOW_NAME,
        INSTANCE_NAME,
        PROJECT_NAME,
        STATUS,
        START_USER,
        START_TIME,
        END_TIME,
        PAID_TIME,
        HOST_NAME,
        TASK_NUM,
        REC_ID,
        ISYSTEM,
        AGENTIP,
        AGENTPORT,
        IAGENTHOSTNAME,
        IAGENT_IPASNAME,
        ISONLINE
      )
  SELECT  A.OPTID,
      A.FLOW_ID,
      B.IFLOWNAME,
      B.IFLOWINSNAME,
      B.IPROJECTNAME,
      LTRIM(TO_CHAR(B.ISTATUS)),
      B.ISTARTUSERFULLNAME,
      (CASE WHEN B.ISTARTTIME > 0 THEN FUN_GET_DATE_STRING(B.ISTARTTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
      (CASE WHEN B.IENDTIME > 0 THEN FUN_GET_DATE_STRING(B.IENDTIME, SI_TIMEZONE_CUR, 'YYYY-MM-DD HH24:MI:SS') ELSE '' END),
      (
        CASE  WHEN  B.ISTATUS IN (0, 1, 6, 8, 10, 15, 30,40) THEN LTRIM(TO_CHAR(TRUNC((LN_MILLISEC - B.ISTARTTIME) / 1000, 0) * 1000))
          WHEN  B.ISTATUS IN (2, 4, 7) THEN LTRIM(TO_CHAR(TRUNC((B.IENDTIME - B.ISTARTTIME) / 1000, 0) * 1000))
          ELSE  '0'
        END
      ),
      B.IHOSTNAME,
      A.TASK_NUM,
      A.REC_ID ,
      B.ISYSTEM,
    '','',
      (
      SELECT
        T2.IAGENT_NAME
      FROM
        TEMP_IEAI_REMOTEEXECACT T1 ,
        IEAI_AGENTINFO T2
      WHERE
        T2.IAGENT_IP= T1.IAGENTHOST AND
        T2.IAGENT_PORT=T1.IAGENTPORT AND
        T1.IFLOWID=A.FLOW_ID
         fetch first 1 rows only
      ),
      (
      SELECT
        IAGENTHOST
      FROM
        TEMP_IEAI_REMOTEEXECACT
      WHERE
        IFLOWID=A.FLOW_ID
        fetch first 1 rows only
      ),
    FUN_CHECK_ONLINE(B.IFLOWNAME,B.IPROJECTNAME)
    FROM  TMP_WORKFLOW_QUERY_TEMP A,
      IEAI_WORKFLOWINSTANCE B
    WHERE  A.OPTID = LN_OPT_ID
    AND  B.IFLOWID = A.FLOW_ID
    ORDER  BY A.REC_ID;
  END
  $


  CREATE OR REPLACE PROCEDURE PROC_WORKFLOW_ACT_WARNJAIL (IN AN_JAILTYPE INTEGER, IN AN_TIMETYPE INTEGER, IN AN_TIMEDAYSTART NUMERIC(19,0), IN AN_TIMEDAYEND NUMERIC(19,0))
	LANGUAGE SQL
	BEGIN
		DECLARE	LI_FLOW		VARCHAR(255);
		DECLARE	LI_ACT		VARCHAR(255);
		DECLARE	LI_BEFORE	VARCHAR(255);
		DECLARE	LI_AFTER_SRART	VARCHAR(255);
		DECLARE	LI_AFTER_END	VARCHAR(255);
		DECLARE	LN_CURRENTTIME	NUMERIC(19,0);
		DECLARE	LN_NEXT_ID	NUMERIC(19,0);

		DECLARE	SQLCODE		INTEGER	DEFAULT	0;
		DECLARE	RETSQLCODE	INTEGER	DEFAULT	0;

    --查询记录的数量
    DECLARE	LN_RESULT_COUNT	NUMERIC(19,0) DEFAULT 0;
    --用于插入记录的ID
    DECLARE	LN_RESULT_ID	NUMERIC(19,0);

		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION, SQLWARNING, NOT FOUND
		SET	RETSQLCODE = SQLCODE;

		SET	LI_FLOW = '工作流';
		SET	LI_ACT = '活动';
		SET	LI_BEFORE = '在预设时间';
		SET	LI_AFTER_SRART = '前未开始';
		SET	LI_AFTER_END = '前未结束';

		SET	LN_CURRENTTIME = FUN_GET_DATE_NUMBER_NEW(CURRENT TIMESTAMP, 8);
		--CALL	PROC_GET_NEXT_ID('IEAI_NODE_WARNING', LN_NEXT_ID);

		IF	AN_JAILTYPE = 0 AND AN_TIMETYPE = 0 THEN
      CALL	PROC_GET_NEXT_ID('IEAI_NODE_WARNING', LN_NEXT_ID);
			INSERT	INTO	IEAI_NODE_WARNING
			(
					ID,
					PRJNAME,
					FLOWNAME,
					ACTNAME,
					TYPE,
					WARNTIME,
					WARNDES
			)
			SELECT	LN_NEXT_ID,
							TT.IPROJECTNAME,
							TT.IWORKFLOWNAME,
							TT.IACTNAME,
							TT.IJAILTIMETYPE,
							LN_CURRENTTIME,
							LI_FLOW || LI_BEFORE || TT.IJAILTIMEHOU || ':' || TT.IJAILTIMEMIN || LI_AFTER_SRART
			FROM	IEAI_APP_JAIL TT
			WHERE	(
					TT.IPROJECTNAME NOT IN	(
									SELECT	INFO.IPROJECTNAME
									FROM	IEAI_WORKFLOWINSTANCE INFO
									WHERE	INFO.IPROJECTNAME = TT.IPROJECTNAME
									AND	INFO.IFLOWNAME = TT.IWORKFLOWNAME
									AND	INFO.ISTARTTIME > AN_TIMEDAYSTART
								)
			)
			AND	(
					TT.IWORKFLOWNAME NOT IN	(
									SELECT	INFO.IFLOWNAME
									FROM	IEAI_WORKFLOWINSTANCE INFO
									WHERE	INFO.IPROJECTNAME = TT.IPROJECTNAME
									AND	INFO.IFLOWNAME = TT.IWORKFLOWNAME
									AND	INFO.ISTARTTIME > AN_TIMEDAYSTART
								)
			)
			AND	TT.IISACTJAIL = 0
			AND	TT.IJAILTIMETYPE = 0
			AND	TT.IJAILSTATUS = 0
			AND	TT.IJAILFLAG = 0
			AND	TT.IISCHECK = 0
			AND	TT.IJAILTIME<LN_CURRENTTIME;
		ELSEIF	AN_JAILTYPE = 0 AND AN_TIMETYPE = 1 THEN
      CALL	PROC_GET_NEXT_ID('IEAI_NODE_WARNING', LN_NEXT_ID);
			INSERT	INTO	IEAI_NODE_WARNING
			(
					ID,
					PRJNAME,
					FLOWNAME,
					ACTNAME,
					TYPE,
					WARNTIME,
					WARNDES,
					IFLOWID
			)
			SELECT	LN_NEXT_ID,
							TT.IPROJECTNAME,
							TT.IWORKFLOWNAME,
							TT.IACTNAME,
							TT.IJAILTIMETYPE,
							LN_CURRENTTIME,
							LI_FLOW || LI_BEFORE || TT.IJAILTIMEHOU || ':' || TT.IJAILTIMEMIN || LI_AFTER_END,
							INFO.IFLOWID
			 FROM IEAI_APP_JAIL TT, IEAI_WORKFLOWINSTANCE INFO
			WHERE INFO.IPROJECTNAME = TT.IPROJECTNAME
					           AND INFO.IFLOWNAME = TT.IWORKFLOWNAME
					           AND INFO.ISTATUS = 0
					           AND INFO.ISTARTTIME > AN_TIMEDAYSTART
					           AND TT.IISACTJAIL = 0
					           AND TT.IJAILTIMETYPE = 1
					           AND TT.IJAILSTATUS = 0
					           AND TT.IJAILFLAG = 0
					           AND TT.IISCHECK = 0
			AND	TT.IJAILTIME < LN_CURRENTTIME;
		ELSEIF	AN_JAILTYPE = 1 AND AN_TIMETYPE = 0 THEN
      CALL	PROC_GET_NEXT_ID('IEAI_NODE_WARNING', LN_NEXT_ID);
			INSERT	INTO	IEAI_NODE_WARNING
			(
					ID,
					PRJNAME,
					FLOWNAME,
					ACTNAME,
					TYPE,
					WARNTIME,
					WARNDES
			)
			SELECT	LN_NEXT_ID,
							TA.IPROJECTNAME,
							TA.IWORKFLOWNAME,
							TA.IACTNAME,
							TA.IJAILTIMETYPE,
							LN_CURRENTTIME,
							LI_ACT || LI_BEFORE || TA.IJAILTIMEHOU || ':' || TA.IJAILTIMEMIN || LI_AFTER_SRART
			FROM	IEAI_APP_JAIL TA
			WHERE	NOT EXISTS	(
							SELECT	*
							FROM	IEAI_ACTRUNTIME TT,
								IEAI_WORKFLOWINSTANCE INFO
							WHERE	TT.IFLOWID = INFO.IFLOWID
							AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
							AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
							AND	TT.IACTNAME = TA.IACTNAME
							AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
			)
			AND	TA.IISACTJAIL = 1
			AND	TA.IJAILTIMETYPE = 0
			AND	TA.IJAILSTATUS = 0
			AND	TA.IJAILFLAG = 0
			AND	TA.IISCHECK = 0
			AND	TA.IJAILTIME < LN_CURRENTTIME;
		ELSEIF	AN_JAILTYPE = 1 AND AN_TIMETYPE = 1 THEN
      CALL	PROC_GET_NEXT_ID('IEAI_NODE_WARNING', LN_NEXT_ID);
			INSERT	INTO	IEAI_NODE_WARNING
			(
					ID,
					PRJNAME,
					FLOWNAME,
					ACTNAME,
					TYPE,
					WARNTIME,
					WARNDES,
					IFLOWID
			)
			SELECT	LN_NEXT_ID,
							TA.IPROJECTNAME,
							TA.IWORKFLOWNAME,
							TA.IACTNAME,
							TA.IJAILTIMETYPE,
							LN_CURRENTTIME,
							LI_ACT || LI_BEFORE || TA.IJAILTIMEHOU || ':' || TA.IJAILTIMEMIN || LI_AFTER_END,
							INFO.IFLOWID
			FROM IEAI_WORKFLOWINSTANCE INFO,
					               IEAI_ACTRUNTIME       TT,
					               ieai_app_jail         TA
		 WHERE TT.IFLOWID = INFO.IFLOWID
					           AND INFO.IFLOWNAME = TA.IWORKFLOWNAME
					           AND INFO.IPROJECTNAME = TA.IPROJECTNAME
					           AND TT.IACTNAME = TA.IACTNAME
					           AND INFO.ISTATUS = 0
					           AND TT.ISTATE IN ('Running',
					                             'Ready',
					                             'Waiting',
					                             'Null',
					                             'ManualRunning',
					                             'Continue')
					           AND TT.IBEGINEXCTIME > AN_TIMEDAYSTART
					           AND TA.IISACTJAIL = 1
					           AND TA.IJAILTIMETYPE = 1
					           AND TA.IJAILSTATUS = 0
					           AND TA.IJAILFLAG = 0
					           AND TA.IISCHECK = 0
			AND	TA.IJAILTIME < LN_CURRENTTIME;
		ELSEIF	AN_JAILTYPE = 9 AND AN_TIMETYPE = 9 THEN
	--10
      --查询数量
      SELECT COUNT(1)
      INTO LN_RESULT_COUNT
      FROM	IEAI_APP_JAIL TA
			WHERE	NOT EXISTS	(
					SELECT	*
					FROM	IEAI_ACTRUNTIME TT,
						IEAI_WORKFLOWINSTANCE INFO
					WHERE	TT.IFLOWID = INFO.IFLOWID
					AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
					AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
					AND	TT.IACTNAME = TA.IACTNAME
					AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
			)
			AND	TA.IISACTJAIL = 1
			AND	TA.IJAILTIMETYPE = 0
			AND	TA.IJAILSTATUS = 0
			AND	TA.IJAILFLAG = 0
			AND	TA.IISCHECK = 0
			AND	TA.IJAILTIME < LN_CURRENTTIME;

      --判断如果有数量再生成ID组进行插入操作
      IF LN_RESULT_COUNT > 0 THEN
	    		-- get primary key
	        CALL PROC_GET_MULT_ID ('IEAI_NODE_WARNING', LN_RESULT_COUNT, LN_RESULT_ID);
	        -- get primary key failure
	        IF LN_RESULT_ID IS NULL THEN
	            RETURN;
	        END IF;
	        INSERT	INTO	IEAI_NODE_WARNING
          (
	            ID,
	            PRJNAME,
	            FLOWNAME,
	            ACTNAME,
	            TYPE,
	            WARNTIME,
	            WARNDES
          )
	        SELECT	ROW_NUMBER()OVER(ORDER BY TA.IID) + LN_RESULT_ID - 1,
			            TA.IPROJECTNAME,
			            TA.IWORKFLOWNAME,
			            TA.IACTNAME,
			            TA.IJAILTIMETYPE,
			            LN_CURRENTTIME,
			            LI_ACT || LI_BEFORE || TA.IJAILTIMEHOU || ':' || TA.IJAILTIMEMIN || LI_AFTER_SRART
	        FROM	IEAI_APP_JAIL TA
	        WHERE	NOT EXISTS	(
	            SELECT	*
	            FROM	IEAI_ACTRUNTIME TT,
	                	IEAI_WORKFLOWINSTANCE INFO
	            WHERE	TT.IFLOWID = INFO.IFLOWID
	            AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
	            AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
	            AND	TT.IACTNAME = TA.IACTNAME
	            AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
	        )
	        AND	TA.IISACTJAIL = 1
	        AND	TA.IJAILTIMETYPE = 0
	        AND	TA.IJAILSTATUS = 0
	        AND	TA.IJAILFLAG = 0
	        AND	TA.IISCHECK = 0
	        AND	TA.IJAILTIME < LN_CURRENTTIME;
      END	IF;

	--11
      --查询数量
      SELECT COUNT(1)
      INTO LN_RESULT_COUNT
      FROM	IEAI_WORKFLOWINSTANCE WINFO,IEAI_ACTRUNTIME TT,IEAI_APP_JAIL TA
			WHERE	EXISTS	(
					SELECT	*
					FROM	IEAI_ACTRUNTIME TT,
								IEAI_WORKFLOWINSTANCE INFO
					WHERE	TT.IFLOWID = INFO.IFLOWID
					AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
					AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
					AND	TT.IACTNAME = TA.IACTNAME
					AND	INFO.ISTATUS = 0
					AND	TT.ISTATE IN ('Running', 'Ready', 'Waiting', 'Null', 'ManualRunning', 'Continue')
					AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
			)
			AND TT.IFLOWID = WINFO.IFLOWID
			AND WINFO.IFLOWID IN (
					SELECT DISTINCT INFO.IFLOWID
	        FROM	IEAI_ACTRUNTIME TT,
								IEAI_WORKFLOWINSTANCE INFO
					WHERE	TT.IFLOWID = INFO.IFLOWID
					AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
					AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
					AND	TT.IACTNAME = TA.IACTNAME
					AND	INFO.ISTATUS = 0
					AND	TT.ISTATE IN ('Running', 'Ready', 'Waiting', 'Null', 'ManualRunning', 'Continue')
					AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
			)
			AND WINFO.IFLOWNAME = TA.IWORKFLOWNAME
			AND WINFO.IPROJECTNAME = TA.IPROJECTNAME
			AND TT.IACTNAME = TA.IACTNAME
			AND	TA.IISACTJAIL = 1
			AND	TA.IJAILTIMETYPE = 1
			AND	TA.IJAILSTATUS = 0
			AND	TA.IJAILFLAG = 0
			AND	TA.IISCHECK = 0
			AND	TA.IJAILTIME < LN_CURRENTTIME;

      --判断如果有数量再生成ID组进行插入操作
      IF LN_RESULT_COUNT > 0 THEN
	        -- get primary key
	        CALL PROC_GET_MULT_ID ('IEAI_NODE_WARNING', LN_RESULT_COUNT, LN_RESULT_ID);
	        -- get primary key failure
	        IF LN_RESULT_ID IS NULL THEN
	            RETURN;
	        END IF;
	        INSERT	INTO	IEAI_NODE_WARNING
	        (
	            ID,
	            PRJNAME,
	            FLOWNAME,
	            ACTNAME,
	            TYPE,
	            WARNTIME,
	            WARNDES,
	            IFLOWID
	        )
	        SELECT	ROW_NUMBER()OVER(ORDER BY TA.IID) + LN_RESULT_ID - 1,
			            TA.IPROJECTNAME,
			            TA.IWORKFLOWNAME,
			            TA.IACTNAME,
			            TA.IJAILTIMETYPE,
			            LN_CURRENTTIME,
			            LI_ACT || LI_BEFORE || TA.IJAILTIMEHOU || ':' || TA.IJAILTIMEMIN || LI_AFTER_END,
			            WINFO.IFLOWID
	        FROM	IEAI_WORKFLOWINSTANCE WINFO,IEAI_ACTRUNTIME TT,IEAI_APP_JAIL TA
	        WHERE	EXISTS	(
	            SELECT	*
	            FROM	IEAI_ACTRUNTIME TT,
	                IEAI_WORKFLOWINSTANCE INFO
	            WHERE	TT.IFLOWID = INFO.IFLOWID
	            AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
	            AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
	            AND	TT.IACTNAME = TA.IACTNAME
	            AND	INFO.ISTATUS = 0
	            AND	TT.ISTATE IN ('Running', 'Ready', 'Waiting', 'Null', 'ManualRunning', 'Continue')
	            AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
	        )
	        AND TT.IFLOWID = WINFO.IFLOWID
	        AND WINFO.IFLOWID IN (
	            SELECT DISTINCT INFO.IFLOWID
	            FROM	IEAI_ACTRUNTIME TT,
	                  IEAI_WORKFLOWINSTANCE INFO
	            WHERE	TT.IFLOWID = INFO.IFLOWID
	            AND	INFO.IFLOWNAME = TA.IWORKFLOWNAME
	            AND	INFO.IPROJECTNAME = TA.IPROJECTNAME
	            AND	TT.IACTNAME = TA.IACTNAME
	            AND	INFO.ISTATUS = 0
	            AND	TT.ISTATE IN ('Running', 'Ready', 'Waiting', 'Null', 'ManualRunning', 'Continue')
	            AND	TT.IBEGINEXCTIME > AN_TIMEDAYSTART
	        )
	        AND WINFO.IFLOWNAME = TA.IWORKFLOWNAME
	        AND WINFO.IPROJECTNAME = TA.IPROJECTNAME
	        AND TT.IACTNAME = TA.IACTNAME
	        AND	TA.IISACTJAIL = 1
	        AND	TA.IJAILTIMETYPE = 1
	        AND	TA.IJAILSTATUS = 0
	        AND	TA.IJAILFLAG = 0
	        AND	TA.IISCHECK = 0
	        AND	TA.IJAILTIME < LN_CURRENTTIME;
      END	IF;

	--00
      --查询数量
      SELECT COUNT(1)
      INTO LN_RESULT_COUNT
      FROM	IEAI_APP_JAIL TT
			WHERE	(
					TT.IPROJECTNAME NOT IN	(
							SELECT	INFO.IPROJECTNAME
							FROM	IEAI_WORKFLOWINSTANCE INFO
							WHERE	INFO.IPROJECTNAME = TT.IPROJECTNAME
							AND	INFO.IFLOWNAME = TT.IWORKFLOWNAME
							AND	INFO.ISTARTTIME > AN_TIMEDAYSTART
					)
			)
			AND	(
					TT.IWORKFLOWNAME NOT IN	(
							SELECT	INFO.IFLOWNAME
							FROM	IEAI_WORKFLOWINSTANCE INFO
							WHERE	INFO.IPROJECTNAME = TT.IPROJECTNAME
							AND	INFO.IFLOWNAME = TT.IWORKFLOWNAME
							AND	INFO.ISTARTTIME > AN_TIMEDAYSTART
					)
			)
			AND	TT.IISACTJAIL = 0
			AND	TT.IJAILTIMETYPE = 0
			AND	TT.IJAILSTATUS = 0
			AND	TT.IJAILFLAG = 0
			AND	TT.IISCHECK = 0
			AND	TT.IJAILTIME<LN_CURRENTTIME;

      --判断如果有数量再生成ID组进行插入操作
      IF LN_RESULT_COUNT > 0 THEN
	    		-- get primary key
	        CALL PROC_GET_MULT_ID ('IEAI_NODE_WARNING', LN_RESULT_COUNT, LN_RESULT_ID);
	        -- get primary key failure
	        IF LN_RESULT_ID IS NULL THEN
	            RETURN;
	        END IF;
	        INSERT	INTO	IEAI_NODE_WARNING
          (
              ID,
              PRJNAME,
              FLOWNAME,
              ACTNAME,
              TYPE,
              WARNTIME,
              WARNDES
          )
	        SELECT	ROW_NUMBER()OVER(ORDER BY TT.IID) + LN_RESULT_ID - 1,
			            TT.IPROJECTNAME,
			            TT.IWORKFLOWNAME,
			            TT.IACTNAME,
			            TT.IJAILTIMETYPE,
			            LN_CURRENTTIME,
			            LI_FLOW || LI_BEFORE || TT.IJAILTIMEHOU || ':' || TT.IJAILTIMEMIN || LI_AFTER_SRART
	        FROM	IEAI_APP_JAIL TT
	        WHERE	(
	            TT.IPROJECTNAME NOT IN	(
	                SELECT	INFO.IPROJECTNAME
	                FROM	IEAI_WORKFLOWINSTANCE INFO
	                WHERE	INFO.IPROJECTNAME = TT.IPROJECTNAME
	                AND	INFO.IFLOWNAME = TT.IWORKFLOWNAME
	                AND	INFO.ISTARTTIME > AN_TIMEDAYSTART
	            )
          )
	        AND	(
	            TT.IWORKFLOWNAME NOT IN	(
	                SELECT	INFO.IFLOWNAME
	                FROM	IEAI_WORKFLOWINSTANCE INFO
	                WHERE	INFO.IPROJECTNAME = TT.IPROJECTNAME
	                AND	INFO.IFLOWNAME = TT.IWORKFLOWNAME
	                AND	INFO.ISTARTTIME > AN_TIMEDAYSTART
	            )
          )
	        AND	TT.IISACTJAIL = 0
	        AND	TT.IJAILTIMETYPE = 0
	        AND	TT.IJAILSTATUS = 0
	        AND	TT.IJAILFLAG = 0
	        AND	TT.IISCHECK = 0
	        AND	TT.IJAILTIME<LN_CURRENTTIME;
      END	IF;

	--01
      --查询数量
      SELECT COUNT(1)
      INTO LN_RESULT_COUNT
      FROM IEAI_APP_JAIL TT, IEAI_WORKFLOWINSTANCE INFO
      WHERE INFO.IPROJECTNAME = TT.IPROJECTNAME
     	AND INFO.IFLOWNAME = TT.IWORKFLOWNAME
     	AND INFO.ISTATUS = 0
     	AND INFO.ISTARTTIME > AN_TIMEDAYSTART
     	AND TT.IISACTJAIL = 0
     	AND TT.IJAILTIMETYPE = 1
     	AND TT.IJAILSTATUS = 0
     	AND TT.IJAILFLAG = 0
     	AND TT.IISCHECK = 0
     	AND TT.IJAILTIME < LN_CURRENTTIME;

      --判断如果有数量再生成ID组进行插入操作
      IF LN_RESULT_COUNT > 0 THEN
		        -- get primary key
		        CALL PROC_GET_MULT_ID ('IEAI_NODE_WARNING', LN_RESULT_COUNT, LN_RESULT_ID);
		        -- get primary key failure
		        IF LN_RESULT_ID IS NULL THEN
		            RETURN;
		        END IF;
		        INSERT INTO IEAI_NODE_WARNING
			      (
				        ID,
				        PRJNAME,
				        FLOWNAME,
				        ACTNAME,
				        TYPE,
				        WARNTIME,
				        WARNDES,
				        IFLOWID
			      )
			      SELECT ROW_NUMBER()OVER(ORDER BY TT.IID) + LN_RESULT_ID - 1,
			             TT.IPROJECTNAME,
			             TT.IWORKFLOWNAME,
			             TT.IACTNAME,
			             TT.IJAILTIMETYPE,
			             LN_CURRENTTIME,
			             LI_FLOW || LI_BEFORE || TT.IJAILTIMEHOU || ':' || TT.IJAILTIMEMIN || LI_AFTER_END,
			             INFO.IFLOWID
			      FROM IEAI_APP_JAIL TT, IEAI_WORKFLOWINSTANCE INFO
			      WHERE INFO.IPROJECTNAME = TT.IPROJECTNAME
	         	AND INFO.IFLOWNAME = TT.IWORKFLOWNAME
	         	AND INFO.ISTATUS = 0
	         	AND INFO.ISTARTTIME > AN_TIMEDAYSTART
	         	AND TT.IISACTJAIL = 0
	         	AND TT.IJAILTIMETYPE = 1
	         	AND TT.IJAILSTATUS = 0
	         	AND TT.IJAILFLAG = 0
	         	AND TT.IISCHECK = 0
	         	AND TT.IJAILTIME < LN_CURRENTTIME;
      END	IF;

		END IF;

		--IF	RETSQLCODE = 0	THEN
          COMMIT WORK;
		--ELSE
		--	ROLLBACK WORK;
		--END	IF;
	END
	$

		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_NODE_WARNING' AND COLNAME = 'AGENTIP';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_NODE_WARNING ADD AGENTIP VARCHAR(255)';
				   PREPARE	SQLA FROM LS_SQL;
				   EXECUTE SQLA;
			END	IF;

		END
		$
		
		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WARNOPER';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_WARNOPER(IID DECIMAL(19,0) NOT NULL,IFLOWID DECIMAL(19,0),INODEWARNNINGID DECIMAL(19,0),IOPERNAME VARCHAR(255),IOPERUSER VARCHAR(255),IOPERTIME DECIMAL(19,0),IOPERTYPE DECIMAL(19,0),IOPERDETAIL VARCHAR(255),IDELAYTIME DECIMAL(19,0),IOPERSTATE DECIMAL(19,0),IACTNAME VARCHAR(255),CONSTRAINT PK_IEAI_WARNOPER PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;
				
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_WARNOPER' AND COLNAME = 'IACTNAME';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_WARNOPER ADD IACTNAME VARCHAR(255);';
				   PREPARE	SQLA FROM LS_SQL;
				   EXECUTE SQLA;
			END	IF;
				
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_WARNOPER' AND INDNAME='IDX_IEAI_WARNOPER_OPERTYPE';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_IEAI_WARNOPER_OPERTYPE ON IEAI_WARNOPER (IOPERNAME, IOPERUSER, IOPERTYPE)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
			
		END
			 $

		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_HIGHOPER WHERE IBUTTONID = 1124 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(1124, ''tn5250执行'', ''getTn5250OutPutRunning_ieai.do'', ''作业调度-活动监控-tn5250执行'')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_MENU_BUTTON WHERE IMENUBUTTONID = 1124 ;
			  IF	LI_EXISTS = 0 THEN
					SET	LS_SQL = 'INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(1124, 1001, 1124, '''')';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			  END	IF;
		END
		 $

		BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_STANDARD_TASK' AND COLNAME = 'PREEXEC';
			IF	LI_EXISTS = 0 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_STANDARD_TASK ADD PREEXEC VARCHAR(25)';
				   PREPARE	SQLA FROM LS_SQL;
				   EXECUTE SQLA;
			END	IF;

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='PREEXEC' AND TABNAME='TEMP_IEAI_DM_TASKLIST';
            IF LI_EXISTS = 1 THEN
                SET LS_SQL ='ALTER TABLE TEMP_IEAI_DM_TASKLIST ALTER  COLUMN PREEXEC  set data type varchar(25)';
                PREPARE	SQLA FROM LS_SQL;
                EXECUTE SQLA;
            END IF;
		END
		 $

   CREATE OR REPLACE
   PROCEDURE PROC_GROACTLIST_PAGE  (IN AN_USERID NUMERIC(19,0),IN AN_START NUMERIC(19,0),IN AN_PAGESIZE NUMERIC(19,0),IN AN_PRJNAME VARCHAR(255),OUT AN_TOTAL NUMERIC(19,0),OUT AN_OPT_ID NUMERIC(19,0),AN_PRJNAMES VARCHAR(255))
	LANGUAGE SQL
	BEGIN
		DECLARE	LN_OPT_ID	NUMERIC(19,0);
		DECLARE	AN_PROTYPE	NUMERIC(19,0);
		DECLARE LN_ISALL    NUMERIC(2,0);
		SET AN_PROTYPE = 1;


		SELECT COUNT(1)  INTO LN_ISALL
					FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
					WHERE T.IROLEID = T2.IROLEID   AND T2.IPERMISSION = 1
					AND T.IUSERID =AN_USERID AND T2.IPROID =-1 ;

		CALL	PROC_GET_NEXT_ID ('TEMP_IEAI_PERMIT_VALIDATE', LN_OPT_ID);
		SET	AN_OPT_ID = LN_OPT_ID;
		IF  LN_ISALL>0 THEN
				IF (AN_PRJNAMES IS NULL) THEN
					SELECT COUNT(1)
					INTO AN_TOTAL
					FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
					WHERE B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND C.STATECODE = 0;


						INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID,FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
						SELECT WW.OPT_ID,
								WW.IID,
								WW.IFLOWID,
								WW.IACTID,
								WW.ITASKID,
								WW.IFLOWINSNAME,
								WW.IACTNAME,
								WW.IDESC,
								WW.ISHOULDSTARTTIME,
								WW.IBEGINEXCTIME,
								WW.ISTATE,
								WW.IOWNER,
								WW.INAME,
								WW.IACTTYPE,
								WW.ISTARTUSERFULLNAME,
								WW.IERRORTASKID,
								WW.IREXECREQUESTID,
								WW.IACTDEFNAME,
								WW.IFLOWNAME,
								WW.ISHOULDENDTIME,
								WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
					FROM (SELECT WWW.ROWN,
												WWW.IID,
												WWW.OPT_ID,
												WWW.IFLOWID,
												WWW.IACTID,
												WWW.ITASKID,
												WWW.IFLOWINSNAME,
												WWW.IACTNAME,
												WWW.IDESC,
												WWW.ISHOULDSTARTTIME,
												WWW.IBEGINEXCTIME,
												WWW.ISTATE,
												WWW.IOWNER,
												WWW.INAME,
												WWW.IACTTYPE,
												WWW.ISTARTUSERFULLNAME,
												WWW.IERRORTASKID,
												WWW.IREXECREQUESTID,
												WWW.IACTDEFNAME,
												WWW.IFLOWNAME,
												WWW.ISHOULDENDTIME,
													WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
										FROM (
								SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
														LN_OPT_ID AS OPT_ID,
														C.IID,
														B.IFLOWID,
														C.IACTID,
														C.ITASKID,
														B.IFLOWINSNAME,
														C.IACTNAME,
														C.IDESC,
														C.ISHOULDSTARTTIME,
														C.IBEGINEXCTIME,
														C.ISTATE,
														(SELECT T1.IOWNER
															FROM IEAI_TASKRUNTIME T1
														WHERE T1.IID = C.ITASKID) AS IOWNER,
														T.INAME,
														C.IACTTYPE,
														B.ISTARTUSERFULLNAME,
														C.IERRORTASKID,
														C.IREXECREQUESTID,
														C.IACTDEFNAME,
														B.IFLOWNAME,
														C.ISHOULDENDTIME,
																C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
													FROM IEAI_WORKFLOWINSTANCE B,
														IEAI_ACTRUNTIME       C,
														IEAI_PROJECT          T
												WHERE B.IPRJLATESTID = T.IID
													AND T.PROTYPE = 1
													AND B.ISTATUS = 0
													AND C.IFLOWID = B.IFLOWID
													AND C.DISAPPEAR = 0
													AND C.IISMONITORACT = 0
													AND C.ACTTYPECODE = 0
													AND C.STATECODE = 0
											) WWW
									) WW
						WHERE WW.ROWN > AN_START
							AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				ELSE
				IF (AN_PRJNAME IS NULL) THEN
							SELECT COUNT(1)
							INTO AN_TOTAL
							FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
							WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME in (AN_PRJNAMES)
							AND C.STATECODE = 0;

							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
								FROM (SELECT WWW.ROWN,
												WWW.IID,
												WWW.OPT_ID,
												WWW.IFLOWID,
												WWW.IACTID,
												WWW.ITASKID,
												WWW.IFLOWINSNAME,
												WWW.IACTNAME,
												WWW.IDESC,
												WWW.ISHOULDSTARTTIME,
												WWW.IBEGINEXCTIME,
												WWW.ISTATE,
												WWW.IOWNER,
												WWW.INAME,
												WWW.IACTTYPE,
												WWW.ISTARTUSERFULLNAME,
												WWW.IERRORTASKID,
												WWW.IREXECREQUESTID,
												WWW.IACTDEFNAME,
												WWW.IFLOWNAME,
												WWW.ISHOULDENDTIME,
															WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
																		FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
											LN_OPT_ID AS OPT_ID,
											C.IID,
											B.IFLOWID,
											C.IACTID,
											C.ITASKID,
											B.IFLOWINSNAME,
											C.IACTNAME,
											C.IDESC,
											C.ISHOULDSTARTTIME,
											C.IBEGINEXCTIME,
											C.ISTATE,
											(SELECT T1.IOWNER
												FROM IEAI_TASKRUNTIME T1
												WHERE T1.IID = C.ITASKID) AS IOWNER,
											T.INAME,
											C.IACTTYPE,
											B.ISTARTUSERFULLNAME,
											C.IERRORTASKID,
											C.IREXECREQUESTID,
											C.IACTDEFNAME,
											B.IFLOWNAME,
											C.ISHOULDENDTIME,
													C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
										FROM IEAI_WORKFLOWINSTANCE B,
											IEAI_ACTRUNTIME       C,
											IEAI_PROJECT          T
										WHERE B.IPRJLATESTID = T.IID
										AND T.PROTYPE = 1
										AND B.ISTATUS = 0
										AND C.IFLOWID = B.IFLOWID
										AND C.DISAPPEAR = 0
										AND C.IISMONITORACT = 0
										AND C.ACTTYPECODE = 0
										AND T.INAME in (AN_PRJNAMES)
										AND C.STATECODE = 0
									) WWW
									) WW
								WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
								ELSE
									SELECT COUNT(1)
							INTO AN_TOTAL
							FROM IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T
							WHERE B.IPRJLATESTID = T.IID
							AND T.PROTYPE = 1
							AND B.ISTATUS = 0
							AND C.IFLOWID = B.IFLOWID
							AND C.DISAPPEAR = 0
							AND C.IISMONITORACT = 0
							AND C.ACTTYPECODE = 0
							AND T.INAME like '%'||AN_PRJNAME||'%'
							AND T.INAME in (AN_PRJNAMES)
							AND C.STATECODE = 0;

							INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
							SELECT WW.OPT_ID,
									WW.IID,
									WW.IFLOWID,
									WW.IACTID,
									WW.ITASKID,
									WW.IFLOWINSNAME,
									WW.IACTNAME,
									WW.IDESC,
									WW.ISHOULDSTARTTIME,
									WW.IBEGINEXCTIME,
									WW.ISTATE,
									WW.IOWNER,
									WW.INAME,
									WW.IACTTYPE,
									WW.ISTARTUSERFULLNAME,
									WW.IERRORTASKID,
									WW.IREXECREQUESTID,
									WW.IACTDEFNAME,
									WW.IFLOWNAME,
									WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
								FROM (SELECT WWW.ROWN,
												WWW.IID,
												WWW.OPT_ID,
												WWW.IFLOWID,
												WWW.IACTID,
												WWW.ITASKID,
												WWW.IFLOWINSNAME,
												WWW.IACTNAME,
												WWW.IDESC,
												WWW.ISHOULDSTARTTIME,
												WWW.IBEGINEXCTIME,
												WWW.ISTATE,
												WWW.IOWNER,
												WWW.INAME,
												WWW.IACTTYPE,
												WWW.ISTARTUSERFULLNAME,
												WWW.IERRORTASKID,
												WWW.IREXECREQUESTID,
												WWW.IACTDEFNAME,
												WWW.IFLOWNAME,
												WWW.ISHOULDENDTIME,
															WWW.ISHELLNAME,
													WWW.IAGENTIP,
													WWW.ISHELLPATH
																		FROM (SELECT  ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
											LN_OPT_ID AS OPT_ID,
											C.IID,
											B.IFLOWID,
											C.IACTID,
											C.ITASKID,
											B.IFLOWINSNAME,
											C.IACTNAME,
											C.IDESC,
											C.ISHOULDSTARTTIME,
											C.IBEGINEXCTIME,
											C.ISTATE,
											(SELECT T1.IOWNER
												FROM IEAI_TASKRUNTIME T1
												WHERE T1.IID = C.ITASKID) AS IOWNER,
											T.INAME,
											C.IACTTYPE,
											B.ISTARTUSERFULLNAME,
											C.IERRORTASKID,
											C.IREXECREQUESTID,
											C.IACTDEFNAME,
											B.IFLOWNAME,
											C.ISHOULDENDTIME,
													C.ISHELLNAME,
															C.IAGENTIP,
															C.ISHELLPATH
										FROM IEAI_WORKFLOWINSTANCE B,
											IEAI_ACTRUNTIME       C,
											IEAI_PROJECT          T
										WHERE B.IPRJLATESTID = T.IID
										AND T.PROTYPE = 1
										AND B.ISTATUS = 0
										AND C.IFLOWID = B.IFLOWID
										AND C.DISAPPEAR = 0
										AND C.IISMONITORACT = 0
										AND C.ACTTYPECODE = 0
										AND T.INAME like '%'||AN_PRJNAME||'%'
										AND T.INAME in (AN_PRJNAMES)
										AND C.STATECODE = 0
									) WWW
									) WW
								WHERE WW.ROWN > AN_START
								AND WW.ROWN <= (AN_START + AN_PAGESIZE);
							END IF;
				END IF;
		ELSE

			IF (AN_PRJNAMES IS NULL) THEN

			INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID)
				SELECT D.IUPPERID
				FROM (SELECT B.IUPPERID, MAX(B.IID) IID
						FROM (SELECT T2.IPROID AS IPROID
								FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
								WHERE T.IROLEID = T2.IROLEID
									AND T2.IPERMISSION = 1
									AND T.IUSERID = AN_USERID
									AND T2.IPROID > 0) A,
								IEAI_PROJECT B
						WHERE A.IPROID = B.IUPPERID
							AND B.IGROUPID = 1
						GROUP BY IUPPERID) C,
						IEAI_PROJECT D
				WHERE C.IID = D.IID
				ORDER BY D.INAME;



				SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND C.STATECODE = 0;

				INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID, IID, FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
				SELECT WW.OPT_ID,
						WW.IID,
						WW.IFLOWID,
						WW.IACTID,
						WW.ITASKID,
						WW.IFLOWINSNAME,
						WW.IACTNAME,
						WW.IDESC,
						WW.ISHOULDSTARTTIME,
						WW.IBEGINEXCTIME,
						WW.ISTATE,
						WW.IOWNER,
						WW.INAME,
						WW.IACTTYPE,
						WW.ISTARTUSERFULLNAME,
						WW.IERRORTASKID,
						WW.IREXECREQUESTID,
						WW.IACTDEFNAME,
						WW.IFLOWNAME,
						WW.ISHOULDENDTIME,
						WW.ISHELLNAME,
						WW.IAGENTIP,
						WW.ISHELLPATH
					FROM (SELECT WWW.ROWN,
							WWW.OPT_ID,
							WWW.IID,
							WWW.IFLOWID,
							WWW.IACTID,
							WWW.ITASKID,
							WWW.IFLOWINSNAME,
							WWW.IACTNAME,
							WWW.IDESC,
							WWW.ISHOULDSTARTTIME,
							WWW.IBEGINEXCTIME,
							WWW.ISTATE,
							WWW.IOWNER,
							WWW.INAME,
							WWW.IACTTYPE,
							WWW.ISTARTUSERFULLNAME,
							WWW.IERRORTASKID,
							WWW.IREXECREQUESTID,
							WWW.IACTDEFNAME,
							WWW.IFLOWNAME,
							WWW.ISHOULDENDTIME,
							WWW.ISHELLNAME,
							WWW.IAGENTIP,
							WWW.ISHELLPATH
					FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
						LN_OPT_ID AS OPT_ID,
						C.IID,
						B.IFLOWID,
						C.IACTID,
						C.ITASKID,
						B.IFLOWINSNAME,
						C.IACTNAME,
						C.IDESC,
						C.ISHOULDSTARTTIME,
						C.IBEGINEXCTIME,
						C.ISTATE,
						(SELECT T1.IOWNER
						FROM IEAI_TASKRUNTIME T1
						WHERE T1.IID = C.ITASKID) AS IOWNER,
						T.INAME,
						C.IACTTYPE,
						B.ISTARTUSERFULLNAME,
						C.IERRORTASKID,
						C.IREXECREQUESTID,
						C.IACTDEFNAME,
						B.IFLOWNAME,
						C.ISHOULDENDTIME,
							C.ISHELLNAME,
							C.IAGENTIP,
							C.ISHELLPATH
				FROM TMP_USER_PRJ_VALIDATE PR,
						IEAI_WORKFLOWINSTANCE B,
						IEAI_ACTRUNTIME       C,
						IEAI_PROJECT          T
				WHERE PR.PRJID = T.IUPPERID
					AND B.IPRJLATESTID = T.IID
					AND T.PROTYPE = 1
					AND B.ISTATUS = 0
					AND C.IFLOWID = B.IFLOWID
					AND C.DISAPPEAR = 0
					AND C.IISMONITORACT = 0
					AND C.ACTTYPECODE = 0
					AND C.STATECODE = 0
					) WWW
				) WW
		WHERE WW.ROWN > AN_START
			AND WW.ROWN <= (AN_START + AN_PAGESIZE);

		ELSE
		  IF (AN_PRJNAME IS NULL) THEN
			INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID)
			SELECT D.IUPPERID
				FROM (SELECT B.IUPPERID, MAX(B.IID) IID
						FROM (SELECT T2.IPROID AS IPROID
								FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
								WHERE T.IROLEID = T2.IROLEID
								AND T2.IPERMISSION = 1
								AND T.IUSERID = AN_USERID
								AND T2.IPROID > 0) A,
							IEAI_PROJECT B
						WHERE A.IPROID = B.IUPPERID
						AND B.IGROUPID = 1
						GROUP BY IUPPERID) C,
					IEAI_PROJECT D
				WHERE C.IID = D.IID
				ORDER BY D.INAME;

			SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME in (AN_PRJNAMES) AND C.STATECODE = 0;

			INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
			SELECT WW.OPT_ID,
					WW.IID,
					WW.IFLOWID,
					WW.IACTID,
					WW.ITASKID,
					WW.IFLOWINSNAME,
					WW.IACTNAME,
					WW.IDESC,
					WW.ISHOULDSTARTTIME,
					WW.IBEGINEXCTIME,
					WW.ISTATE,
					WW.IOWNER,
					WW.INAME,
					WW.IACTTYPE,
					WW.ISTARTUSERFULLNAME,
					WW.IERRORTASKID,
					WW.IREXECREQUESTID,
					WW.IACTDEFNAME,
					WW.IFLOWNAME,
					WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
				FROM (SELECT WWW.ROWN,
							WWW.IID,
							WWW.OPT_ID,
							WWW.IFLOWID,
							WWW.IACTID,
							WWW.ITASKID,
							WWW.IFLOWINSNAME,
							WWW.IACTNAME,
							WWW.IDESC,
							WWW.ISHOULDSTARTTIME,
							WWW.IBEGINEXCTIME,
							WWW.ISTATE,
							WWW.IOWNER,
							WWW.INAME,
							WWW.IACTTYPE,
							WWW.ISTARTUSERFULLNAME,
							WWW.IERRORTASKID,
							WWW.IREXECREQUESTID,
							WWW.IACTDEFNAME,
							WWW.IFLOWNAME,
							WWW.ISHOULDENDTIME,
								WWW.ISHELLNAME,
								WWW.IAGENTIP,
								WWW.ISHELLPATH
					FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
								FROM IEAI_TASKRUNTIME T1
								WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME,
								C.ISHELLNAME,
								C.IAGENTIP,
								C.ISHELLPATH
						FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
						WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND T.INAME in (AN_PRJNAMES)
						AND C.STATECODE = 0
						) WWW
					) WW
				WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				ELSE
				INSERT INTO TMP_USER_PRJ_VALIDATE(PRJID)
			SELECT D.IUPPERID
				FROM (SELECT B.IUPPERID, MAX(B.IID) IID
						FROM (SELECT T2.IPROID AS IPROID
								FROM IEAI_USERINHERIT T, IEAI_SYS_PERMISSION T2
								WHERE T.IROLEID = T2.IROLEID
								AND T2.IPERMISSION = 1
								AND T.IUSERID = AN_USERID
								AND T2.IPROID > 0) A,
							IEAI_PROJECT B
						WHERE A.IPROID = B.IUPPERID
						AND B.IGROUPID = 1
						GROUP BY IUPPERID) C,
					IEAI_PROJECT D
				WHERE C.IID = D.IID
				ORDER BY D.INAME;

			SELECT COUNT(1) INTO AN_TOTAL FROM TMP_USER_PRJ_VALIDATE PR, IEAI_WORKFLOWINSTANCE B, IEAI_ACTRUNTIME C, IEAI_PROJECT T WHERE PR.PRJID = T.IUPPERID AND B.IPRJLATESTID = T.IID AND T.PROTYPE = 1 AND B.ISTATUS = 0 AND C.IFLOWID = B.IFLOWID AND C.DISAPPEAR = 0 AND C.IISMONITORACT = 0 AND C.ACTTYPECODE = 0 AND T.INAME like '%'||AN_PRJNAME||'%' AND T.INAME in (AN_PRJNAMES) AND C.STATECODE = 0;

			INSERT INTO TMP_FIRST_PAGE_LIST(OPT_ID,IID,  FLOW_ID, ACT_ID, TASK_ID, FLOW_INSTANCE_NAME, ACT_NAME, ACT_DESCRIPTION, SHOULDSTARTTIME, BEGINEXCTIME, ACT_STATES, IOWNER, PRJ_NAME, ACT_TYPE, STARTUSER_NAME, ACT_ERRORTASKID, EXECACT_IREXECREQUESTID, ACT_DEF_NAME, FLOW_NAME, SHOULDENDTIME,SHELLNAME,AGENTIP,SHELLPATH)
			SELECT WW.OPT_ID,
					WW.IID,
					WW.IFLOWID,
					WW.IACTID,
					WW.ITASKID,
					WW.IFLOWINSNAME,
					WW.IACTNAME,
					WW.IDESC,
					WW.ISHOULDSTARTTIME,
					WW.IBEGINEXCTIME,
					WW.ISTATE,
					WW.IOWNER,
					WW.INAME,
					WW.IACTTYPE,
					WW.ISTARTUSERFULLNAME,
					WW.IERRORTASKID,
					WW.IREXECREQUESTID,
					WW.IACTDEFNAME,
					WW.IFLOWNAME,
					WW.ISHOULDENDTIME,
									WW.ISHELLNAME,
									WW.IAGENTIP,
									WW.ISHELLPATH
				FROM (SELECT WWW.ROWN,
							WWW.IID,
							WWW.OPT_ID,
							WWW.IFLOWID,
							WWW.IACTID,
							WWW.ITASKID,
							WWW.IFLOWINSNAME,
							WWW.IACTNAME,
							WWW.IDESC,
							WWW.ISHOULDSTARTTIME,
							WWW.IBEGINEXCTIME,
							WWW.ISTATE,
							WWW.IOWNER,
							WWW.INAME,
							WWW.IACTTYPE,
							WWW.ISTARTUSERFULLNAME,
							WWW.IERRORTASKID,
							WWW.IREXECREQUESTID,
							WWW.IACTDEFNAME,
							WWW.IFLOWNAME,
							WWW.ISHOULDENDTIME,
								WWW.ISHELLNAME,
								WWW.IAGENTIP,
								WWW.ISHELLPATH
					FROM (SELECT ROW_NUMBER() OVER(ORDER BY decode(C.ISTATE, 'Fail', '1','Fail:Business' , '2' , 'ManualDisconnect', '3', 'Disconnect', '4', 'Timeout', '5', 'Ready', '6', 'Continue', '7', 'Running', '8', 'QueueUp', '9')) AS ROWN,
							LN_OPT_ID AS OPT_ID,
							C.IID,
							B.IFLOWID,
							C.IACTID,
							C.ITASKID,
							B.IFLOWINSNAME,
							C.IACTNAME,
							C.IDESC,
							C.ISHOULDSTARTTIME,
							C.IBEGINEXCTIME,
							C.ISTATE,
							(SELECT T1.IOWNER
								FROM IEAI_TASKRUNTIME T1
								WHERE T1.IID = C.ITASKID) AS IOWNER,
							T.INAME,
							C.IACTTYPE,
							B.ISTARTUSERFULLNAME,
							C.IERRORTASKID,
							C.IREXECREQUESTID,
							C.IACTDEFNAME,
							B.IFLOWNAME,
							C.ISHOULDENDTIME,
								C.ISHELLNAME,
								C.IAGENTIP,
								C.ISHELLPATH
						FROM TMP_USER_PRJ_VALIDATE PR,
							IEAI_WORKFLOWINSTANCE B,
							IEAI_ACTRUNTIME       C,
							IEAI_PROJECT          T
						WHERE PR.PRJID = T.IUPPERID
						AND B.IPRJLATESTID = T.IID
						AND T.PROTYPE = 1
						AND B.ISTATUS = 0
						AND C.IFLOWID = B.IFLOWID
						AND C.DISAPPEAR = 0
						AND C.IISMONITORACT = 0
						AND C.ACTTYPECODE = 0
						AND T.INAME like '%'||AN_PRJNAME||'%'
						AND T.INAME in (AN_PRJNAMES)
						AND C.STATECODE = 0
						) WWW
					) WW
				WHERE WW.ROWN > AN_START
				AND WW.ROWN <= (AN_START + AN_PAGESIZE);
				END IF;
			END IF;
		END IF;
	END
	$
 BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_MOBILE_APPROVAL' AND  COLNAME='ICONTENT';
			IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_MOBILE_APPROVAL ALTER COLUMN ICONTENT SET DATA TYPE VARCHAR(500)';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END	IF;

	END
	$

	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FLOWERROR_TASKINFO';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_FLOWERROR_TASKINFO(IID DECIMAL(19) NOT NULL,TASKID DECIMAL(19),FLOWID DECIMAL(19),ACTNAME VARCHAR(255),ERRORMSG VARCHAR(500),STATE DECIMAL(2),CONSTRAINT IEAI_FLOWERROR_TASKINFO PRIMARY KEY(IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;

	END
	$

	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

				SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_TIME_DELAY_CAOFIG';
				IF LI_EXISTS = 0 THEN
				 SET LS_SQL ='CREATE TABLE IEAI_TIME_DELAY_CAOFIG (IID DECIMAL(19) NOT NULL,IEXCELID DECIMAL(19),IFLOWNAME VARCHAR(255),IACTNAME VARCHAR(255),ISYSTEM VARCHAR(255),IDELAYTIME VARCHAR(255),IWAY DECIMAL(19), CONSTRAINT PK_IEAI_TIME_DELAY_CAOFIG PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
				END IF;

	END
	$

	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

			   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_PAUSE_ACTIVITY';
			   IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_PAUSE_ACTIVITY (IID DECIMAL(19) NOT NULL, IFLOWID DECIMAL(19), IPRJID DECIMAL(19), IACTNAME VARCHAR(255) NOT NULL, IFLOWNAME VARCHAR(255), IPROJECTNAME VARCHAR(255),ITIMESTR VARCHAR(255),ICREATETIME VARCHAR(255),IUSER VARCHAR(255),ISTATUS DECIMAL(19) DEFAULT 0,ISEFFECT DECIMAL(19) DEFAULT 0,ITAKE VARCHAR(255), CONSTRAINT PK_IEAI_PAUSE_ACTIVITY PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
			   END IF;

	END
	$

   BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

			   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_BH_WARNINFO';
			   IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_BH_WARNINFO (IID DECIMAL(19) NOT NULL,IFLOWID DECIMAL(19) NOT NULL,ISOURCEID VARCHAR(255),ISTATUS VARCHAR(255) ,ISOURCEEVENTID VARCHAR(255),ISOURCECINAME VARCHAR(255),ISOURCEALERTKEY VARCHAR(255),ISOURCESEVERITY VARCHAR(255),ISEVERITY VARCHAR(255),ISUMMARY VARCHAR(255),IFIRSTOCCURRENCE VARCHAR(255),ILASTOCCURRENCE VARCHAR(255),ISTATECHANGE VARCHAR(255),ICIOWER VARCHAR(255), CONSTRAINT PK_IEAI_BH_WARNINFO PRIMARY KEY (IID))';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
			   END IF;

	END
	$


   BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='PERFORMUSER';
				IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL_COPY ADD PERFORMUSER VARCHAR(50)';
					   PREPARE	SQLA FROM LS_SQL;
					   EXECUTE SQLA;
				END	IF;

	END
	$

   BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='PERFORMUSER';
				IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL ADD PERFORMUSER VARCHAR(50)';
					   PREPARE	SQLA FROM LS_SQL;
					   EXECUTE SQLA;
				END	IF;

	END
	$
        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='VIRTUALNAME';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL_COPY ADD VIRTUALNAME VARCHAR(250)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$
        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='JOBLIST';
            IF	LI_EXISTS = 1 THEN
            SET	LS_SQL = 'alter table IEAI_EXCELMODEL_COPY alter IACTDESCRIPTION set data type varchar(3000)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$
        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='VIRTUALNAME';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL ADD VIRTUALNAME VARCHAR(250)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$
        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='JOBLIST';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL_COPY ADD JOBLIST VARCHAR(600)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$

        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='JOBLIST';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL ADD JOBLIST VARCHAR(600)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$
        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL' AND  COLNAME='SEQUENCENUMBER';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL ADD SEQUENCENUMBER VARCHAR(10)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$
        BEGIN
            DECLARE LS_SQL VARCHAR(2000);
            DECLARE  LI_EXISTS  NUMERIC(2);
            SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_EXCELMODEL_COPY' AND  COLNAME='SEQUENCENUMBER';
            IF	LI_EXISTS = 0 THEN
            SET	LS_SQL = 'ALTER TABLE  IEAI_EXCELMODEL_COPY ADD SEQUENCENUMBER VARCHAR(10)';
            PREPARE	SQLA FROM LS_SQL;
            EXECUTE SQLA;
        END	IF;

        END
	$
   BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
				SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_BH_WARNINFO' AND  COLNAME='ITYPE';
				IF	LI_EXISTS = 0 THEN
				SET	LS_SQL = 'ALTER TABLE  IEAI_BH_WARNINFO ADD ITYPE VARCHAR(255)';
					   PREPARE	SQLA FROM LS_SQL;
					   EXECUTE SQLA;
				END	IF;

	END
	$


    BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATUS' AND TABNAME='IEAI_USER';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'ALTER TABLE ieai_user add  ISTATUS VARCHAR(255) DEFAULT 0';
		    	PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;

	END
	$

	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DELDES' AND TABNAME='IEAI_NODE_WARNING';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_NODE_WARNING ADD DELDES VARCHAR(4000)';
		    	PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;

	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

			   SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_WORKFLOWTIME_HISSAVE';
			   IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_WORKFLOWTIME_HISSAVE  (ICYCLETIME DECIMAL(19,0) WITH DEFAULT 35 , ISETDATE DECIMAL(19,0) , ICHECKTIME VARCHAR(10) , IFILESIZE DECIMAL(5,0) WITH DEFAULT 6 )';
						PREPARE	SQLA FROM LS_SQL;
						EXECUTE SQLA;
			   END IF;

	END
	$

  
	 
	 BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTARTALARM' AND TABNAME='IEAI_SYSALARM_LEVEL';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_SYSALARM_LEVEL ADD ISTARTALARM VARCHAR(10)';
		    	PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;

	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IENDALARM' AND TABNAME='IEAI_SYSALARM_LEVEL';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'ALTER TABLE IEAI_SYSALARM_LEVEL ADD IENDALARM VARCHAR(10)';
		    	PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;

	END
	$
