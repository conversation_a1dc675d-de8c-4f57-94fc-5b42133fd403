package com.ideal.service.agentMaintain;

import com.ideal.common.utils.Tools;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.common.DNSAnalyze;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.agentMaintain.AgentDomainManager;
import com.ideal.ieai.server.repository.hd.agentMaintain.model.AgentDomain;
import com.ideal.util.ExportUtils;
import com.ideal.util.ImportUtil;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

public class AgentDomainService {

    private static final Logger        logger   = Logger.getLogger(AgentDomainService.class);
    private final AgentDomainManager manager  = new AgentDomainManager();
    private static final String        EXESQLS           = "exeSqls";
    private static final String        ROLLBACKSQLS      = "rollbackSqls";
    private static final String        MESSAGE           = "message";
    private static final String        SUCCESS          = "success";


    public Map getAgentDomainList(HttpServletRequest request, int start, int limit, String idomain, String iaddressanalysis, String isystemname,
                                  String irecord, String iviewname,int sysType) throws RepositoryException {
        return manager.getAgentDomainList(request,start,limit,idomain,iaddressanalysis,isystemname,irecord,iviewname,sysType);
    }

    public List getAllAgentDomainList() throws RepositoryException {
        return manager.getAllAgentDomainList();
    }
    public Map saveAgentDomains(String jsonData, int sysType, HttpServletRequest request) throws Exception {
        Map res = new HashMap();
        List<AgentDomain> list = jsonTranslateAgentDomain(jsonData);
        List saveFailure = new ArrayList();
        StringBuffer buffer = new StringBuffer("<div class=\"win_prompt\">");
        Map<String, String> tmpContains = new HashMap<String, String>();
        Connection checkConn = null;
      
        try {
            
            checkConn = DBResource.getConnection("saveAgentDomains", logger, sysType);
            for (AgentDomain agentDomain : list) {
                String ip = DNSAnalyze.getIPByName(agentDomain.getIdomain());
                if ("127.0.0.1".equals(ip)) {
                    saveFailure.add(agentDomain.getIdomain());
                }
                String key = agentDomain.getIdomain() + agentDomain.getIport();
                if (!tmpContains.containsKey(key))
                {
                    tmpContains.put(key, key);
                } else
                {
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "Agent 域名 有重复：" + agentDomain.getIdomain());
                    return res;
                }
                
                if (manager.isAgentIpExistsAndUpdateClusterId(agentDomain, checkConn, sysType))
                {
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "数据库中已经存在域名为：" + agentDomain.getIdomain() + " 的Agent信息。");
                    return res;
                }
                
            }
            if (!saveFailure.isEmpty()) {
                buffer.append("<p>以下域名解析失败，请检查您的域名是否有误！：</p>");
                buffer.append("<div class=\"pro_ip\">");
                for (int i = 0; i < saveFailure.size(); i++) {
                    buffer.append("<span>" + saveFailure.get(i).toString() + "</span>");
                }
                buffer.append("</div></div>");
                res.put(SUCCESS,false);
                res.put(MESSAGE,buffer.toString());
                return res;
            } 
        }catch (Exception e) {
            logger.info("saveAgentDomains method is err !", e);
            throw e;
        }finally {
            DBResource.closeConnection(checkConn, "saveAgentDomains", logger);
        }
        
        
        Map<String, Object> connInfo = null;
        connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection)connInfo.get("baseConn");
        if (baseConn != null)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = manager.organizeAddAgentDomainSql(list, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                boolean isSuccess = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS));
                if (isSuccess)
                {
                    DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                    res.put(SUCCESS,true);
                    res.put(MESSAGE, "数据保存成功");
                    return res;
                }else
                {
                    DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                    res.put(SUCCESS,false);
                    res.put(MESSAGE, "操作sql时出现错误，数据保存失败!");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                for (Connection dbConn : dbConns) {
                    DBResource.closeConnection(dbConn,"saveAgentDomains",logger);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "操作sql时出现错误，数据保存失败!");
                return res;
            }
        }else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有基线源, 无法保存！");
            return res;
        }
    }

    public List haveAgentInfoIp(String jsonData) {
        ArrayList res = new ArrayList();
        List<AgentDomain> list = jsonTranslateAgentDomain(jsonData);
        for (AgentDomain agentDomain : list) {
            String ip = DNSAnalyze.getIPByName(agentDomain.getIdomain());
            if (manager.getAgentInfoId(ip,agentDomain.getIport()) == null) {
               res.add(agentDomain.getIdomain());
            }
        }
        return res;
    }
    public Map deleteAgentDomain(String ids) throws DBException, RepositoryException {
        HashMap<Object, Object> map = new HashMap<>();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection)connInfo.get("baseConn");
        if (baseConn != null) {
            List<Connection> dbConns = (List<Connection>)connInfo.get("dbConns");
            Map exeSqls = manager.organizeDeleteAgentDomainSql(ids,baseConn);
            if ((Boolean) exeSqls.get(SUCCESS))
            {
                boolean boo = DBUtil.hitDatabase(dbConns, (List<String>) exeSqls.get(EXESQLS), (List<String>) exeSqls.get(ROLLBACKSQLS));
                if (boo) {
                    DBResource.closeConnection(baseConn,"deleteAgentDomain",logger);
                    map.put(SUCCESS,true);
                    map.put(MESSAGE,"数据删除成功");
                    return map;
                }else {
                    DBResource.closeConnection(baseConn,"deleteAgentDomain",logger);
                    map.put(SUCCESS,false);
                    map.put(MESSAGE,"组织sql时出现错误，删除失败");
                    return map;
                }
            }else {
                DBResource.closeConnection(baseConn,"deleteAgentDomain",logger);
                DBResource.closeConnection(baseConn, "deleteAgentDomain", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "deleteAgentDomain", logger);
                }
                map.put(SUCCESS,false);
                map.put(MESSAGE,"组织sql时出现错误，删除失败");
                return map;
            }
        }else {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "没有基线源, 无法删除！");
            return map;
        }
    }
    public List jsonTranslateAgentDomain(String jsonData) {
        List<AgentDomain> agentDomainList = new ArrayList<>();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null
                != jsonData)
        {
            JSONArray jsonArray = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArray.size(); i++)
            {
                AgentDomain agentDomain = new AgentDomain();
                String strIid = jsonArray.getJSONObject(i).optString("iid");
                long iid = (strIid == null || "".equals(strIid)) ? 0L : Long.valueOf(strIid);
                agentDomain.setIid(iid);
                agentDomain.setIAgentinfoid(jsonArray.getJSONObject(i).optLong("iagentinfoid"));
                agentDomain.setIagentupid(jsonArray.getJSONObject(i).optLong("iagentupid"));
                agentDomain.setIdomain(jsonArray.getJSONObject(i).optString("idomain"));
                agentDomain.setIport(jsonArray.getJSONObject(i).optInt("iport"));
                agentDomain.setIaddressanalysis(jsonArray.getJSONObject(i).optString("iaddressanalysis"));
                agentDomain.setIsystemname(jsonArray.getJSONObject(i).optString("isystemname"));
                agentDomain.setIsystemshort(jsonArray.getJSONObject(i).optString("isystemshort"));
                agentDomain.setIservicename(jsonArray.getJSONObject(i).optString("iservicename"));
                agentDomain.setIservicename(jsonArray.getJSONObject(i).optString("iservicename"));
                agentDomain.setIrecord(jsonArray.getJSONObject(i).optString("irecord"));
                agentDomain.setIviewname(jsonArray.getJSONObject(i).optString("iviewname"));
                //是否开启ssl，1开启
                agentDomain.setIsSSL(jsonArray.getJSONObject(i).optInt("isSSL"));
                agentDomainList.add(agentDomain);
            }
        }
        return agentDomainList;
    }

    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {

        OutputStream out = null;
        try {
            HSSFWorkbook hwb = new HSSFWorkbook();
            request.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");

            Date cur = new Date();
            DateFormat df = new SimpleDateFormat("yyyMMdd");
            String dateString = df.format(cur);

            String fileName = dateString + "_agent域名解析导入模板.xls";

            response.addHeader("Content-Disposition","attachment;filename=" + new String(fileName.getBytes("GB2312"),StandardCharsets.ISO_8859_1));
            HSSFSheet sheet = hwb.createSheet("数据信息");

            HSSFRow row = sheet.createRow(0);
            sheet.setDefaultColumnWidth(20);
            sheet.createRow(1);
            sheet.createRow(2);
            sheet.createRow(3);
            sheet.createRow(4);
            sheet.createRow(5);
            sheet.createRow(6);
            sheet.createRow(7);
            sheet.createRow(8);

            HSSFCellStyle style = hwb.createCellStyle();
            row.setHeightInPoints(15);
            HSSFFont font = hwb.createFont();
            font.setBold(true);
            style.setFont(font);
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setFillForegroundColor((short) 0x33);
            style.setAlignment(HorizontalAlignment.CENTER);

            HSSFCell cell = row.createCell(0);
            cell.setCellValue("序号");
            cell.setCellStyle(style);

            cell = row.createCell(1);
            cell.setCellValue("域名");
            cell.setCellStyle(style);

            cell = row.createCell(2);
            cell.setCellValue("端口号");
            cell.setCellStyle(style);

            cell = row.createCell(3);
            cell.setCellValue("地址解析");
            cell.setCellStyle(style);

            cell = row.createCell(4);
            cell.setCellValue("系统名称");
            cell.setCellStyle(style);

            cell = row.createCell(5);
            cell.setCellValue("系统简称");
            cell.setCellStyle(style);

            cell = row.createCell(6);
            cell.setCellValue("服务名称");
            cell.setCellStyle(style);

            cell = row.createCell(7);
            cell.setCellValue("记录模式");
            cell.setCellStyle(style);

            cell = row.createCell(8);
            cell.setCellValue("视图名称");
            cell.setCellStyle(style);

            out = response.getOutputStream();
            hwb.write(out);
        } catch (IOException e) {
            logger.error("AgentDomainService.downloadTemplate is error", e);
        } finally {

            try {
                if (out != null)
                {
                    out.close();
                }
            } catch (IOException e) {
                logger.error("AgentDomainService.downloadTemplate is error");
            }
        }
    }

    public Map importAgentDomainInfo(CommonsMultipartFile file,HttpServletRequest request, int sysType) throws IOException {
        Map map = new HashMap();
        InputStream fis = null;
        try
        {
            fis = file.getInputStream();
            String[][] validateArr = { { "idomain", "" }, { "iport", "" }, { "iaddressanalysis", "" },{ "isystemname", "" },{ "isystemshort", "" },
                    { "iservicename", "" },{ "irecord", "" },{ "iviewname", "" } };
            Map dataMap = ImportUtil.importOnePageExcel(fis, file.getFileItem().getName(), validateArr,
                    AgentDomain.class, "");
            if (null != dataMap && Boolean.valueOf(dataMap.get(SUCCESS).toString()))
            {
                List<AgentDomain> tempList = (List) dataMap.get("list");
                List<AgentDomain> list = tempList.stream().distinct().collect(Collectors.toList());
                String jsonData = com.alibaba.fastjson.JSONArray.toJSONString(list);
                saveAgentDomainForExcel(jsonData);
            }
            map.put(Constants.STR_SUCCESS, true);
            map.put(Constants.STR_MESSAGE, "导入数据采集系统信息，成功！");
        } catch (Exception e)
        {
            map.put(Constants.STR_SUCCESS, false);
            map.put(Constants.STR_MESSAGE, "导入的文件类型不符合要求,请检查后再导入!(仅支持：.xlsx, .xlsm, .xls文件类型)");
            logger.error("导入数据采集系统信息，发生异常！！！", e);
        } finally
        {
            if (fis != null)
            {
                fis.close();
            }
        }
        return map;
    }

    public Map saveAgentDomainForExcel(String jsonData) throws DBException, RepositoryException {
        Map res = new HashMap<>();
        List<AgentDomain> list = jsonTranslateAgentDomain(jsonData);
        /*Map agentMap = new HashMap<>();
        for (AgentDomain agentDomain : list) {
            Agent agent = new Agent();
            String ip = DNSAnalyze.getIPByName(agentDomain.getIdomain());
            agent.setIagentip(ip);
            agent.setIagentport((long) agentDomain.getIport());
            if (!agentMap.containsKey(ip)) {
                agentMap.put(ip,agent);
            }
        }*/
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");

        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = manager.organizeAddAgentDomainSqlForImport(list, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),
                        (List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, "saveAgentDomainForExcel", logger);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, "saveAgentDomainForExcel", logger);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行信息变更sql时出现错误！");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, "saveAgentDomainForExcel", logger);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, "saveAgentDomainForExcel", logger);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织信息sql时出现错误！");
                return res;
            }
        } else
        {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有基线源, 无法保存！");
            return res;
        }
    }
    public void exportAll(HttpServletResponse response, int sysType) {
        List list = new ArrayList();
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);
            String fileName = dateString + "_Agent域名解析表信息";

            list = manager.getAgentDomainAllList(sysType);
            List<Map<String, String>> listMap = (List<Map<String, String>>) com.alibaba.fastjson.JSONArray.parse(com.alibaba.fastjson.JSONArray.toJSONString(list));
            map.put("tabname", "Agent域名解析表信息");
            map.put("list", listMap);
            String[] titles = { "域名", "端口", "地址解析", "系统名称", "系统简称", "服务名称", "记录模式", "视图名称" };
            String[] fields = { "idomain", "iport", "iaddressanalysis", "isystemname", "isystemshort", "iservicename", "irecord", "iviewname" };
            ExportUtils.download(fileName, response, ExportUtils.exportOnePageExcel(map, titles, fields));

        } catch (Exception e)
        {
            logger.error("CollectMonitorService.exportAll is error", e);
        }
    }

    public void exportOne(HttpServletResponse response, String ids, int sysType) {
        List list = new ArrayList();
        Map<String, Object> map = new HashMap<String, Object>();
        try
        {
            Date currentTime = new Date();
            DateFormat format = new SimpleDateFormat("yyyyMMdd");
            String dateString = format.format(currentTime);
            String fileName = dateString + "_Agent域名解析表信息";
            list = manager.getAgentDomainOneList(ids, sysType);
            List<Map<String, String>> listMap = (List<Map<String, String>>) com.alibaba.fastjson.JSONArray.parse(com.alibaba.fastjson.JSONArray.toJSONString(list));
            map.put("tabname", "Agent域名解析表信息");
            map.put("list", listMap);
            String[] titles = { "域名", "端口", "地址解析", "系统名称", "系统简称", "服务名称", "记录模式", "视图名称" };
            String[] fields = { "idomain", "iport", "iaddressanalysis", "isystemname", "isystemshort", "iservicename", "irecord", "iviewname" };
            ExportUtils.download(fileName, response, ExportUtils.exportOnePageExcel(map, titles, fields));

        } catch (Exception e)
        {
            logger.error("CollectMonitorService.exportAll is error", e);
        }
    }

    public Map DNSParseByIp(String jsonData) throws DBException, RepositoryException {
        Map map = new HashMap<>();
        StringBuffer buffer = new StringBuffer("<div class=\"win_prompt\">");
        List parsefailureList = new ArrayList();
        boolean parseSuccess = true;
        if (jsonData == null || "".equals(jsonData)) {
            map.put(SUCCESS,false);
            map.put(MESSAGE,"主键为空域名解析失败！");
            return map;
        }
        List<AgentDomain> list = jsonTranslateAgentDomain(jsonData);
        for (AgentDomain agentDomain : list) {
            String ip = DNSAnalyze.getIPByName(agentDomain.getIdomain());
            if ("127.0.0.1".equals(ip)) {
                parsefailureList.add(agentDomain.getIdomain());
            }
        }
        if (!parsefailureList.isEmpty()) {
            parseSuccess = false;
            buffer.append("<p>以下域名解析失败,已设为默认值(*********)：</p>");
            buffer.append("<div class=\"pro_ip\">");
            for (int i = 0; i < parsefailureList.size(); i++) {
                buffer.append("<span>" + parsefailureList.get(i).toString() + "</span>");
            }
            buffer.append("</div></div>");
        }
        Map<String, Object> connInfo = null;
        connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection)connInfo.get("baseConn");
        if (baseConn != null)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = manager.organizeDNSParseAgentDomainSql(list, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                boolean isSuccess = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS));
                if (isSuccess)
                {
                    DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                    map.put(SUCCESS,true);
                    map.put(MESSAGE,  (parseSuccess ? "域名解析成功！" : buffer.toString()));
                    return map;
                }else
                {
                    DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                    map.put(SUCCESS,false);
                    map.put(MESSAGE, "操作sql时出现错误，数据保存失败!");
                    return map;
                }
            } else {
                DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                for (Connection dbConn : dbConns) {
                    DBResource.closeConnection(dbConn,"saveAgentDomains",logger);
                }
                map.put(SUCCESS, false);
                map.put(MESSAGE, "操作sql时出现错误，数据保存失败!");
                return map;
            }
        }else {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "没有基线源, 无法保存！");
            return map;
        }
    }

    public Map DNSParseByIpAll(List<AgentDomain> list) throws DBException, RepositoryException {
        Map map = new HashMap<>();
        List<AgentDomain> successList = new ArrayList<>();
        List notSuccessList = new ArrayList<>();
        boolean isEmpty = true;
        StringBuffer buffer = new StringBuffer("<div class=\"win_prompt\">");

        for (AgentDomain agentDomain : list) {
            String ip = DNSAnalyze.getIPByName(agentDomain.getIdomain());
            if ("127.0.0.1".equals(ip)) {
                notSuccessList.add(agentDomain.getIdomain());
            } else {
                successList.add(agentDomain);
            }
        }
        if (!notSuccessList.isEmpty()) {
            isEmpty = false;
            buffer.append("<p>以下域名解析失败，已设为默认值(127.0.0.1)：</p>");
            buffer.append("<div class=\"pro_ip\">");
            for (int i = 0; i < notSuccessList.size(); i++) {
                buffer.append("<span>" + notSuccessList.get(i).toString() + "</span>");
            }
            buffer.append("</div></div>");
        }

        Map<String, Object> connInfo = null;
        connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection)connInfo.get("baseConn");
        if (baseConn != null)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = manager.organizeDNSParseAgentDomainSql(successList, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                boolean isSuccess = DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS), (List<String>) orgSql.get(ROLLBACKSQLS));
                if (isSuccess)
                {
                    DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                    map.put(SUCCESS,true);
                    map.put(MESSAGE, (isEmpty ? "域名解析成功" : buffer.toString()));
                    return map;
                }else
                {
                    DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                    map.put(SUCCESS,false);
                    map.put(MESSAGE, "操作sql时出现错误，数据保存失败!");
                    return map;
                }
            } else {
                DBResource.closeConnection(baseConn,"saveAgentDomains",logger);
                for (Connection dbConn : dbConns) {
                    DBResource.closeConnection(dbConn,"saveAgentDomains",logger);
                }
                map.put(SUCCESS, false);
                map.put(MESSAGE, "操作sql时出现错误，数据保存失败!");
                return map;
            }
        }else {
            map.put(SUCCESS, false);
            map.put(MESSAGE, "没有基线源, 无法保存！");
            return map;
        }
    }

    public Map getAgentMaintainUpgradeInfo ( int start, int limit, int sysType ) throws Exception
    {
        return manager.getAgentMaintainUpgradeInfo(start, limit, sysType);
    }
}
