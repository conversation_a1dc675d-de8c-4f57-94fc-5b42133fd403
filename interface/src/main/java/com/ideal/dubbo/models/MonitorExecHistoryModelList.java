package com.ideal.dubbo.models;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class MonitorExecHistoryModelList implements Serializable
{
    List<MonitorExecHistoryModel> result = new ArrayList<MonitorExecHistoryModel>();

    public List<MonitorExecHistoryModel> getResult ()
    {
        return result;
    }

    public void setResult ( List<MonitorExecHistoryModel> result )
    {
        this.result = result;
    }
}
