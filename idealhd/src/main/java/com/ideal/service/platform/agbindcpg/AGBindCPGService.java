package com.ideal.service.platform.agbindcpg;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.common.utils.Tools;
import com.ideal.ieai.core.JudgeDB;
import com.ideal.ieai.server.platform.repository.agbindcpg.AGBindCPGManage;
import com.ideal.ieai.server.platform.repository.agbindcpg.AgentGroupBindCPGModel;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.BeanFormatter;

import net.sf.json.JSONArray;
public class AGBindCPGService
{
    private Logger log = Logger.getLogger(AGBindCPGService.class);
    private static final String SUCCESS = "success";
    private static final String SAVEAGENTGROUPBINDCPG = "saveAgentGroupBindCPG";
    private static final String ROLLBACKSQLS = "rollbackSqls";
    private static final String MESSAGE = "message";
    private static final String EXESQLS = "exeSqls";
    private static final String DELETEAGENTGROUPBINDCPG = "deleteAgentGroupBindCPG";
    public Map getAgentGroupBindCPGList ( Integer start, Integer limit, String queryString, int sysType )
    {
        Connection conn = null;
        AGBindCPGManage fcm = new AGBindCPGManage();
        String sqlWhere = "";
        if (queryString != null && !"".equals(queryString))
        {
            sqlWhere = sqlWhere + " and (a.XXX like '%"+queryString+"%' or a.XXX like '%"+queryString+"%')";
        }
        String sqlList = "";
        if (JudgeDB.IEAI_DB_TYPE == 3)
        {
            sqlList = "select a.iid,a.iagentgroupid,a.icpgroupid from IEAI_AGENTG_BIND_CPG a where 1=1 "+sqlWhere+" order by a.IID LIMIT ?,?";
        } else
        {
            sqlList = "select * from (select row_number() over (order by a.iid desc) AS ro,a.iid,a.iagentgroupid,a.icpgroupid from IEAI_AGENTG_BIND_CPG a where 1=1 "+sqlWhere+") where ro>? and ro<=?";
        }
        String sqlCount = "select count(a.iid) as countNum from IEAI_AGENTG_BIND_CPG a where 1=1 "+sqlWhere;
        Map map = new HashMap();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            List list = fcm.getAgentGroupBindCPGList(conn, sqlList, start, limit);
            int count = fcm.getAgentGroupBindCPGCount(conn, sqlCount);
            map.put("dataList", list);
            map.put("total", count);
        } catch (Exception e)
        {
            log.error("AGBindCPGService.getAgentGroupBindCPGList is error", e);
        } finally
        {
            DBResource.closeConnection(conn, "getAgentGroupBindCPGList", log);
        }
        return map;
    }
    private List jsonTranslateAgentGroupBindCPG ( String jsonData ,String iagentgroupid)
    {
        List dataList = new ArrayList();
        if (!"".equals(jsonData) && !"null".equals(jsonData) && null != jsonData)
        {
            JSONArray jsonArr = JSONArray.fromObject(jsonData);
            for (int i = 0; i < jsonArr.size(); i++)
            {
                AgentGroupBindCPGModel model = new AgentGroupBindCPGModel();
                model.setIid(jsonArr.getJSONObject(i).optString("iid")==null||"".equals(jsonArr.getJSONObject(i).optString("iid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("iid")));
                model.setIagentgroupid(Long.valueOf(iagentgroupid));
                model.setIcpgroupid(jsonArr.getJSONObject(i).optString("icpgroupid")==null||"".equals(jsonArr.getJSONObject(i).optString("icpgroupid"))?0l:Long.valueOf(jsonArr.getJSONObject(i).optString("icpgroupid")));
                dataList.add(model);
            }
        }
        return dataList;
    }
    public Map saveAgentGroupBindCPG ( String jsonData ,String iagentgroupid) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        List list = jsonTranslateAgentGroupBindCPG(jsonData,iagentgroupid);
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeAddAgentGroupBindCPGSql(list, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),(List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, SAVEAGENTGROUPBINDCPG, log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "数据保存成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, SAVEAGENTGROUPBINDCPG, log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行信息变更sql时出现错误！");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, SAVEAGENTGROUPBINDCPG, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, SAVEAGENTGROUPBINDCPG, log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织信息sql时出现错误！");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有基线源, 无法保存！");
            return res;
        }
    }
    public Map organizeAddAgentGroupBindCPGSql ( List<AgentGroupBindCPGModel> list, Connection baseConn)
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        AGBindCPGManage manage = new AGBindCPGManage();
        String insertSqlTemplate = " INSERT INTO IEAI_AGENTG_BIND_CPG (iid,iagentgroupid,icpgroupid) VALUES({iid},{iagentgroupid},{icpgroupid}) ";
        String deleteSqlTemplate = " delete from IEAI_AGENTG_BIND_CPG where IID=";
        String updateSqlTemplate = " update IEAI_AGENTG_BIND_CPG set iid={iid},iagentgroupid={iagentgroupid},icpgroupid={icpgroupid} where iid={iid}";
        try
        {
            for (AgentGroupBindCPGModel model : list)
            {
                if (model.getIid() <= 0)
                {
                    long id = IdGenerator.createId("IEAI_AGENTG_BIND_CPG", baseConn);
                    model.setIid(id);
                    BeanFormatter<AgentGroupBindCPGModel> bf = new BeanFormatter<AgentGroupBindCPGModel>(insertSqlTemplate);
                    String s = bf.format(model);
                    exeSqls.add(s);
                    rollbackSqls.add(deleteSqlTemplate + id);
                } else {
                    BeanFormatter<AgentGroupBindCPGModel> bf = new BeanFormatter<AgentGroupBindCPGModel>(updateSqlTemplate);
                    String s = bf.format(model);
                    exeSqls.add(s);
                    AgentGroupBindCPGModel backModel = manage.getAgentGroupBindCPGOne(model.getIid(), baseConn);
                    if (null != backModel)
                    {
                        BeanFormatter<AgentGroupBindCPGModel> rbf = new BeanFormatter<AgentGroupBindCPGModel>(updateSqlTemplate);
                        String r = rbf.format(backModel);
                        rollbackSqls.add(r);
                    }
                }
            }
        } catch (RepositoryException e) {
            log.error("organizeAddAgentGroupBindCPGSql error",e);
            isSuccess = false;
        } catch (Exception e) {
            log.error("organizeAddAgentGroupBindCPGSql error",e);
            isSuccess = false;
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    public Map deleteAgentGroupBindCPG ( String deleteIds ) throws RepositoryException, DBException
    {
        Map res = new HashMap();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get("baseConn");
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get("dbConns");
            Map orgSql = organizeDeleteAgentGroupBindCPGSql(deleteIds, baseConn);
            if ((Boolean) orgSql.get(SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(EXESQLS),(List<String>) orgSql.get(ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, DELETEAGENTGROUPBINDCPG, log);
                    res.put(SUCCESS, true);
                    res.put(MESSAGE, "数据删除成功！");
                    return res;
                } else {
                    DBResource.closeConnection(baseConn, DELETEAGENTGROUPBINDCPG, log);
                    res.put(SUCCESS, false);
                    res.put(MESSAGE, "执行信息删除sql时出现错误！");
                    return res;
                }
            } else {
                DBResource.closeConnection(baseConn, DELETEAGENTGROUPBINDCPG, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, DELETEAGENTGROUPBINDCPG, log);
                }
                res.put(SUCCESS, false);
                res.put(MESSAGE, "组织信息sql时出现错误！");
                return res;
            }
        } else {
            res.put(SUCCESS, false);
            res.put(MESSAGE, "没有基线源, 无法删除！");
            return res;
        }
    }
    public Map organizeDeleteAgentGroupBindCPGSql ( String deleteIds, Connection baseConn )
    {
        Map res = new HashMap();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        AGBindCPGManage manage = new AGBindCPGManage();
        String deleteSqlTemplate = " delete from IEAI_AGENTG_BIND_CPG where IID=";
        String insertSqlTemplate = " INSERT INTO IEAI_AGENTG_BIND_CPG (iid,iagentgroupid,icpgroupid) VALUES({iid},{iagentgroupid},{icpgroupid}) ";
        try
        {
            String[] deleteId = deleteIds.split(",");
            for(int i=0;i<deleteId.length;i++){
                exeSqls.add(deleteSqlTemplate + deleteId[i]);
                AgentGroupBindCPGModel model = manage.getAgentGroupBindCPGOne(Long.valueOf(deleteId[i]), baseConn);
                if (null != model)
                {
                    BeanFormatter<AgentGroupBindCPGModel> rbf = new BeanFormatter<AgentGroupBindCPGModel>(insertSqlTemplate);
                    String s = rbf.format(model);
                    rollbackSqls.add(s);
                }
            }
        } catch (RepositoryException e)
        {
            log.error("organizeDeleteAgentGroupBindCPGSql error",e);
            isSuccess = false;
        } catch (Exception e)
        {
            log.error("organizeDeleteAgentGroupBindCPGSql error",e);
            isSuccess = false;
        }
        res.put(SUCCESS, isSuccess);
        res.put(EXESQLS, exeSqls);
        res.put(ROLLBACKSQLS, rollbackSqls);
        return res;
    }
    
    
    public Map queryCpGroupList ( AgentGroupBindCPGModel param, int dbType ) throws RepositoryException
    {
        AGBindCPGManage manage = new AGBindCPGManage();
        return manage.queryCpGroupList(param, dbType);
    }
    
    
    public int getCpGroupIsBind ( long agentGroupid, String cpgids, int dbType )
    {
        AGBindCPGManage manage = new AGBindCPGManage();
        return manage.getCpGroupIsBind(agentGroupid, cpgids, dbType);
    }
    
    
    public Map<String ,Object> getagBindCpgSelectList(AgentGroupBindCPGModel model,int systype){
        Connection conn = null;
        Map<String ,Object> map = new HashMap<String, Object>();
        AGBindCPGManage manage = new AGBindCPGManage();
        
        StringBuilder sb = new StringBuilder();
        sb.append(" select CPGROUPID,CPGROUPNAME,B.IID from ");
        sb.append(" ( select  distinct G.IID        AS CPGROUPID, ");
        sb.append("    G.IGROUPNAME AS CPGROUPNAME ");
        sb.append("     FROM IEAI_COMPUTER_GROUP            G, ");
        sb.append("     IEAI_COMPUTERGROUP_PERMISSIONS P, ");
        sb.append("     IEAI_USERINHERIT               UR ");
        sb.append("     WHERE G.IID = P.IGROUPID ");
        sb.append("     AND P.IROLEID = UR.IROLEID ");
        sb.append("      AND P.IPERMISSION = 1 ");
        sb.append("     AND UR.IUSERID =  ").append(model.getUserId());
        sb.append("     ) A , IEAI_AGENTG_BIND_CPG B ");
        sb.append("     where A.CPGROUPID = B.ICPGROUPID and B.IAGENTGROUPID=");
        sb.append(model.getIagentgroupid());
        
        
        try
        {
            conn =  DBResource.getConnection("getagBindCpgSelectList", log, systype);
            map = manage.getAgentGroupBindCPGModelList(sb.toString(), conn, model.getStart(), model.getLimit());
            
        } catch (RepositoryException e)
        {
            log.error("查询已绑定到agent组的设备组失败", e);
            
        }finally {
            DBResource.closeConnection(conn, "getagBindCpgSelectList", log);
        }
        return map;
    }
    
    public Map<String ,Object> getagBindCpgNotSelectList(AgentGroupBindCPGModel model,int systype){
        Connection conn = null;
        Map<String ,Object> map = new HashMap<String, Object>();
        AGBindCPGManage manage = new AGBindCPGManage();
        
        StringBuilder sb = new StringBuilder();
        sb.append(" select CPGROUPID,CPGROUPNAME,-1 as IID from ");
        sb.append(" ( select  distinct G.IID        AS CPGROUPID, ");
        sb.append("    G.IGROUPNAME AS CPGROUPNAME ");
        sb.append("     FROM IEAI_COMPUTER_GROUP            G, ");
        sb.append("     IEAI_COMPUTERGROUP_PERMISSIONS P, ");
        sb.append("     IEAI_USERINHERIT               UR ");
        sb.append("     WHERE G.IID = P.IGROUPID ");
        sb.append("     AND P.IROLEID = UR.IROLEID ");
        sb.append("      AND P.IPERMISSION = 1 ");
        sb.append("     AND UR.IUSERID =  ").append(model.getUserId());
        if(model.getCpGroupName()!=null && !"".equals(model.getCpGroupName().trim())) {
            sb.append("     AND G.IGROUPNAME LIKE '%").append(model.getCpGroupName().trim()).append("%'");
        }
        
        
        sb.append("     ) A where A.CPGROUPID not in (Select B.ICPGROUPID from  IEAI_AGENTG_BIND_CPG B ");
        
       /** 对于原中信逻辑是绑定过agent则不允许在绑定了
        *  sb.append("     where  B.IAGENTGROUPID=");
        sb.append(agentid);*/
        sb.append(" ) ");
        
        try
        {
            conn =  DBResource.getConnection("getagBindCpgNotSelectList", log, systype);
            map = manage.getAgentGroupBindCPGModelList(sb.toString(), conn, model.getStart(), model.getLimit());
            
        } catch (RepositoryException e)
        {
            log.error("查询已绑定到agent组的设备组失败", e);
            
        }finally {
            DBResource.closeConnection(conn, "getagBindCpgNotSelectList", log);
        }
        return map;
        
        
        
        
    }
    
}