package com.ideal.ieai.server.platform.nessus;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;

import com.ideal.ieai.server.nessus.NessusQueryResultManager;
import com.ideal.ieai.server.nessus.model.NessusEquipmentBean;
import com.ideal.ieai.server.nessus.model.NessusQueryResultBean;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;

public class NessusQueryResultHandler
{

    /**
     * <li>Description: 该方法用于整理扫描结果，将结果放在查询结果表里，便于前台查询。 并不作为真实的执行历史数据。</li> 
     * <AUTHOR>
     * 2019年9月3日 
     * @param modelType
     * @param nebs
     * @return
     * return Boolean
     */
    public Boolean damageQueryResult ( int modelType, List<NessusEquipmentBean> nebs )
    {
        Boolean ret = false;
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(modelType);
            for (NessusEquipmentBean neb : nebs)
            {
                int num = nqrm.updateCurToLast(conn, neb.getIp());
                if (num > 0)
                {
                    nqrm.updateCurnessus(conn, neb.getInessusplus(), neb.getInessusid(), neb.getIp());
                } else
                {
                    NessusQueryResultBean bean =nqrm.getByIp(modelType, neb.getIp());
                    if (bean.getIp()!=null && !bean.getIp().isEmpty())
                    {
                        nqrm.insert(conn, neb);
                    }
                }
                conn.commit();
            }
        } catch (DBException e)
        {
            log.error(e);
        } catch (SQLException e)
        {
            log.error(e);
        } finally
        {
            DBResource.closeConnection(conn, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        ret = true;
        return ret;
    }

    private NessusQueryResultManager        nqrm    = NessusQueryResultManager.getInstance();

    private static final Logger             log     = Logger.getLogger(NessusQueryResultHandler.class);
    private static NessusQueryResultHandler intance = new NessusQueryResultHandler();

    public static NessusQueryResultHandler getInstance ()
    {
        if (intance == null)
        {
            intance = new NessusQueryResultHandler();
        }
        return intance;
    }

    private NessusQueryResultHandler()
    {
    }
}
