	-- 4.7.17 version does not have patches for datacollect
	-- 4.7.18 version does not have patches for datacollect
	-- 4.7.19 version does not have patches for datacollect
	-- 4.7.20 version does not have patches for datacollect
	-- 4.7.21 version does not have patches for datacollect
	-- 4.7.22 version does not have patches for datacollect
	-- 4.7.23 version does not have patches for datacollect
	-- 4.7.24 version does not have patches for datacollect
	-- 4.7.25 version does not have patches for datacollect
	-- 4.7.26 version datacollect patch is as follows	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ILEVELDESC' AND TABNAME='IEAI_LEVEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_LEVEL ADD COLUMN ILEVELDESC VARCHAR(255)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ILEVELTYPE' AND TABNAME='IEAI_LEVEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_LEVEL ADD COLUMN ILEVELTYPE VARCHAR(100)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICREATETIME' AND TABNAME='IEAI_LEVEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_LEVEL ADD COLUMN ICREATETIME DECIMAL(19)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_CLASSATTR';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_CLASSATTR(IID DECIMAL(19) NOT NULL ,ICLASSID DECIMAL(19) ,IATTRNAME VARCHAR(100) ,IATTRCODE VARCHAR(100) ,IDEFAULTVALUE VARCHAR(255) ,CONSTRAINT PK_IEAI_UNIFYAGENT_CLASSATTR PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_CA_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_CA_INST(IID DECIMAL(19) NOT NULL ,ICLASSATTRID DECIMAL(19) ,ICOLLECTINFOID DECIMAL(19) ,IATTRNAME VARCHAR(100) ,IATTRCODE VARCHAR(100) ,IATTRVALUE VARCHAR(255) ,CONSTRAINT PK_IEAI_UNIFYAGENT_CA_INST PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_COLLECTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_COLLECTINFO(IID DECIMAL(19) NOT NULL ,ISARELATIONID DECIMAL(19) ,ICLASSID DECIMAL(19) ,ICOLLECTNAME VARCHAR(100) ,ICREATETIME DECIMAL(19) ,ICREATEUSER VARCHAR(255) ,CONSTRAINT PK_IEAI_UNIFYAGENT_COLLECTINFO PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_SA_RELATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_SA_RELATION(IID DECIMAL(19) NOT NULL ,ISYSID DECIMAL(19) ,IAGENTID DECIMAL(19) ,CONSTRAINT PK_IEAI_UNIFYAGENT_SA_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_CM_INST(IID DECIMAL(19) NOT NULL ,IMODELID DECIMAL(19) ,ICOLLECTINFOID DECIMAL(19) ,IMODELNAME VARCHAR(50) ,ICOLLECTTYPE DECIMAL(1) ,IPERSONAL DECIMAL(1) ,ITOPIC VARCHAR(255) ,ICREATETIME DECIMAL(19) ,ICHECKSCRIPT VARCHAR(255) ,IRUNUSER VARCHAR(100) ,IOUTPUTFORMAT VARCHAR(255) ,IFILESOURCE VARCHAR(4000) ,IINTERVALTIME VARCHAR(10) ,IREADPOSITION VARCHAR(10) ,ICHARSET VARCHAR(10) ,IMULTIPLE VARCHAR(10) ,IJDBCURL VARCHAR(4000) ,ICONNUSER VARCHAR(255) ,ICONNPASSWORD VARCHAR(255) ,IMODE VARCHAR(50) ,IINCREMENTINGCOL VARCHAR(4000) ,ITIMESTAMPCOL VARCHAR(4000) ,IQUERY VARCHAR(4000) ,ITIMINGTYPE DECIMAL(1) ,ITIMINGEXPRESSION VARCHAR(255) ,ISENDKFK DECIMAL(1) ,CONSTRAINT PK_IEAI_UNIFYAGENT_CM_INST PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_COLLECTMODEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_COLLECTMODEL(IID DECIMAL(19) NOT NULL ,ICLASSID DECIMAL(19) ,IMODELNAME VARCHAR(50) ,ICOLLECTTYPE DECIMAL(1) ,IPERSONAL DECIMAL(1) ,ITOPIC VARCHAR(255) ,ICREATETIME DECIMAL(19) ,ICHECKSCRIPT VARCHAR(255) ,IRUNUSER VARCHAR(100) ,IOUTPUTFORMAT VARCHAR(255) ,IFILESOURCE VARCHAR(4000) ,IINTERVALTIME VARCHAR(10) ,IREADPOSITION VARCHAR(10) ,ICHARSET VARCHAR(10) ,IMULTIPLE VARCHAR(10) ,IJDBCURL VARCHAR(4000) ,ICONNUSER VARCHAR(255) ,ICONNPASSWORD VARCHAR(255) ,IMODE VARCHAR(50) ,IINCREMENTINGCOL VARCHAR(4000) ,ITIMESTAMPCOL VARCHAR(4000) ,IQUERY VARCHAR(4000) ,ITIMINGTYPE DECIMAL(1) ,ITIMINGEXPRESSION VARCHAR(255) ,ISENDKFK DECIMAL(1) ,CONSTRAINT PK_IEAI_UNIFYAGENT_CM PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_RUN_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_RUN_INST(IID DECIMAL(19) NOT NULL ,ICOLLECTINFOID DECIMAL(19),ICMINSTID DECIMAL(19) ,IAGENTID DECIMAL(19) ,IAGENTIP VARCHAR(20) ,IAGENTPORT DECIMAL(10) ,IRUNTIME DECIMAL(19) ,IRUNSTATE DECIMAL(1) ,IRETURNID DECIMAL(19) ,ISCRIPTINFO CLOB ,IMODELNAME VARCHAR(50),ICOLLECTTYPE DECIMAL(1),IPERSONAL DECIMAL(1),ITIMINGTYPE DECIMAL(1),ITIMINGEXPRESSION VARCHAR(255),CONSTRAINT PK_IEAI_UNIFYAGENT_RUN_INST PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_RESULT(IID DECIMAL(19) NOT NULL ,IRUNINSTID DECIMAL(19) ,IRESULTSTATE DECIMAL(1) ,IRESULTSTR VARCHAR(4000) ,IRETURNTIME DECIMAL(19),CONSTRAINT PK_IEAI_UNIFYAGENT_RESULT PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_MONITOR';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AGENT_MONITOR (IID INTEGER NOT NULL ,IAGENT_ID INTEGER NULL ,IAGENT_IP VARCHAR(20) NULL ,ISTATE INTEGER NOT NULL ,ICPU_THRESHOLD INTEGER NOT NULL ,IMEMORY_THRESHOLD INTEGER NOT NULL  ,INET_THRESHOLD INTEGER NOT NULL ,ICREATE_TIME TIMESTAMP NOT NULL ,ICREATE_USER VARCHAR(40) NOT NULL ,IMONITOR_INTERVAL INTEGER NOT NULL,IIOREAD_THRESHOLD DECIMAL(3,2),IIOWRITE_THRESHOLD DECIMAL(3,2),CONSTRAINT PK_IEAI_AGENT_MONITOR PRIMARY KEY(IID)) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_MONITOR_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_AGENT_MONITOR_RESULT (IID INTEGER NOT NULL, IAGENT_ID INTEGER NOT NULL ,IMONITOR_ID INTEGER NOT NULL ,IMONITOR_TIME TIMESTAMP NOT NULL ,IMONITOR_RESULT VARCHAR(512),CONSTRAINT PK_IEAI_AGENT_MONITOR_RESULT PRIMARY KEY(IID)) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DATACOLLECT_SIZE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_DATACOLLECT_SIZE(IID DECIMAL(19) NOT NULL ,IINSTID DECIMAL(19) ,ISTARTTIME DECIMAL(19) ,IENDTIME DECIMAL(19) ,ISIZE DECIMAL(19) ,CONSTRAINT PK_IEAI_DATACOLLECT_SIZE PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DATACOLLECT_WARN';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_DATACOLLECT_WARN(IID DECIMAL(19) NOT NULL ,IINSTID DECIMAL(19) ,ITIME DECIMAL(19) ,IMESSAGE VARCHAR(512) ,CONSTRAINT PK_IEAI_DATACOLLECT_WARN PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_PROXY_CLUSTER';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_PROXY_CLUSTER(IID DECIMAL(19) NOT NULL ,INAME VARCHAR(255) ,IDESC VARCHAR(255) ,IZK VARCHAR(255) ,ISTATUS DECIMAL(1) DEFAULT 0 ,CONSTRAINT PK_IEAI_KAFKA_PROXY_CLUSTER PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_DATA_LINK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_DATA_LINK(IID DECIMAL(19) NOT NULL ,ITYPE DECIMAL(1) DEFAULT 0 ,IPARENTID DECIMAL(19) ,INAME VARCHAR(255) ,IDESC VARCHAR(255) ,IIMG VARCHAR(255) ,ICODE VARCHAR(255) ,ICLUSTERID DECIMAL(19) NOT NULL ,CONSTRAINT PK_IEAI_KAFKA_DATA_LINK PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_AGENT_RELATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_AGENT_RELATION(IID DECIMAL(19) NOT NULL ,ICLUSTERID DECIMAL(19) ,ILINKID DECIMAL(19) ,IIP VARCHAR(255) ,IPORT VARCHAR(255) ,AGENTID DECIMAL(19) ,IAGENTNAME VARCHAR(255) ,IREMARK VARCHAR(255) ,CONSTRAINT PK_IEAI_KAFKA_AGENT_RELATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_PROXY_STATUS';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_PROXY_STATUS(IID DECIMAL(19) NOT NULL ,IIP VARCHAR(255) ,INAME VARCHAR(255) ,ITOPIC VARCHAR(255) ,ICONSUMENUM DECIMAL(19) ,IMSGNUM DECIMAL(19) ,ISUCCESSNUM DECIMAL(19) ,IFAILNUM DECIMAL(19) ,IRETRYNUM DECIMAL(19) ,ITIME TIMESTAMP ,CONSTRAINT PK_IEAI_KAFKA_PROXY_STATUS PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_PROXY';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_PROXY(IID DECIMAL(19) NOT NULL ,ICLUSTERID DECIMAL(19) ,IINBROKER VARCHAR(255) ,IGROUP VARCHAR(255) ,IIP VARCHAR(255) ,IPROXYNAME VARCHAR(255) ,IAUTH DECIMAL(1) DEFAULT 0 ,IINNAME VARCHAR(50) ,IINPWD VARCHAR(50) ,IMAXRETRY DECIMAL(19) ,IPOLLINTERVAL DECIMAL(19) ,IMAXBLOCK DECIMAL(19) ,IMAXRBLOCK DECIMAL(19) ,IMINBYTE DECIMAL(19) ,IMAXBYTE DECIMAL(19) ,IBROKERTIME DECIMAL(19) ,IINTOPIC VARCHAR(255) ,IOUTBROKER VARCHAR(255) ,IOUTNANME VARCHAR(50) ,IOUTPWD VARCHAR(50) ,IOUTTOPIC VARCHAR(255) ,ITOPICTYPE DECIMAL(1) DEFAULT 0 ,ISERVERIP VARCHAR(255) ,ISERVERPORT DECIMAL(5) ,IREPORTTIME DECIMAL(19) ,IREMARK VARCHAR(255) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_PROXY_ALARM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_PROXY_ALARM(IID DECIMAL(19) NOT NULL ,IIP VARCHAR(255) ,INAME VARCHAR(255) ,ILOSTNUM DECIMAL(19) ,IALARMMSG VARCHAR(255) ,ITIME TIMESTAMP ,CONSTRAINT PK_IEAI_KAFKA_PROXY_ALARM PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_KAFKA_DESTINATION';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_KAFKA_DESTINATION(IID DECIMAL(19) NOT NULL ,IBROKER VARCHAR(255) ,IPORT VARCHAR(255) ,IAUTH DECIMAL(1) ,INAME VARCHAR(50) ,IPASSWORD VARCHAR(50) ,ILINKID DECIMAL(19) ,CONSTRAINT PK_IEAI_KAFKA_DESTINATION PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_BUSY_CONFIG';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL ='CREATE TABLE IEAI_AGENT_BUSY_CONFIG(IID DECIMAL(19) NOT NULL ,IAGENT_ID DECIMAL(19) NOT NULL ,IAGENT_NAME VARCHAR(40) NULL ,IAGENT_IP VARCHAR(40) NULL ,ISTART_TIME VARCHAR(10) NULL ,IEND_TIME VARCHAR(10) NULL ,ICREATE_TIME TIME NULL,CONSTRAINT PK_IEAI_AGENT_BUSY_CONFIG PRIMARY KEY (IID))';
				PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
			END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFYAGENT_LIST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFYAGENT_LIST(IID DECIMAL(19) NOT NULL ,ISLIST DECIMAL(1) NULL ,INSTID DECIMAL(19) NULL,CONSTRAINT PK_IEAI_UNIFYAGENT_LIST PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFY_CLEAN_TASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFY_CLEAN_TASK(IID DECIMAL(19) NOT NULL ,ITABLEID DECIMAL(19) NOT NULL ,IKEEPTIME DECIMAL(19) ,IREMARK VARCHAR(4000),CONSTRAINT PK_IEAI_UNIFY_CLEAN_TASK PRIMARY KEY (IID) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UNIFY_CLEAN_TABLE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_UNIFY_CLEAN_TABLE(IID DECIMAL(19) NOT NULL ,ITABLE VARCHAR(255) ,ICOLUMN VARCHAR(255) ,IREMARK VARCHAR(4000),CONSTRAINT PK_IEAI_UNIFY_CLEAN_TABLE PRIMARY KEY (IID) )';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		COMMIT  WORK;
	END
	$
	-- 4.7.27 version does not have patches for datacollect
	-- 4.7.28 version datacollect patch is as follows
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATE' AND TABNAME='IEAI_DATACOLLECT_WARN';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DATACOLLECT_WARN ADD COLUMN ISTATE DECIMAL(1) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		COMMIT  WORK;
	END
	$
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRESULTSTR' AND TABNAME='IEAI_UNIFYAGENT_RESULT' AND TYPENAME!='CLOB';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_RESULT DROP COLUMN IRESULTSTR';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		COMMIT  WORK;
	END
	$
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IRESULTSTR' AND TABNAME='IEAI_UNIFYAGENT_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_RESULT ADD COLUMN IRESULTSTR CLOB ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		COMMIT  WORK;
	END
	$
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICUSTOMER' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD COLUMN ICUSTOMER VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICUSTOMER' AND TABNAME='IEAI_UNIFYAGENT_COLLECTMODEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD COLUMN ICUSTOMER VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICOUNT' AND TABNAME='IEAI_DATACOLLECT_SIZE';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DATACOLLECT_SIZE ADD COLUMN ICOUNT DECIMAL(10) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		COMMIT  WORK;
	END
	$
	--4.7.29
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMODIFYTIME' AND TABNAME='IEAI_UNIFYAGENT_COLLECTMODEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD COLUMN IMODIFYTIME DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMODIFYTIME' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD COLUMN IMODIFYTIME DECIMAL(19) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.INDEXES WHERE TABNAME='IEAI_DATACOLLECT_SIZE' AND INDNAME='IDX_IEAI_DATACOLLECT_SIZE_01';
		IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'CREATE INDEX IDX_IEAI_DATACOLLECT_SIZE_01 ON IEAI_DATACOLLECT_SIZE (IINSTID)';
			 PREPARE	SQLA FROM LS_SQL; 
			 EXECUTE SQLA;
		END	IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICPU_THRESHOLDTWO' AND TABNAME='IEAI_AGENT_MONITOR';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MONITOR ADD COLUMN ICPU_THRESHOLDTWO DECIMAL(3) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMEMORY_THRESHOLDTWO' AND TABNAME='IEAI_AGENT_MONITOR';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MONITOR ADD COLUMN IMEMORY_THRESHOLDTWO DECIMAL(3) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMAINBAKFILESIZE' AND TABNAME='IEAI_UNIFYAGENT_COLLECTMODEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD COLUMN IMAINBAKFILESIZE VARCHAR(3) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMAINBAKFILESIZE' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD COLUMN IMAINBAKFILESIZE VARCHAR(3) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DATACOLLECT_ALARM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_DATACOLLECT_ALARM(IID DECIMAL(10) NOT NULL ,IWARNMSG VARCHAR(512) ,IWARNTIME DECIMAL(19) ,CONSTRAINT PK_IEAI_DATACOLLECT_ALARM PRIMARY KEY (IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWARNTYPE' AND TABNAME='IEAI_DATACOLLECT_ALARM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DATACOLLECT_ALARM ADD COLUMN IWARNTYPE VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWARNIP' AND TABNAME='IEAI_DATACOLLECT_ALARM';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DATACOLLECT_ALARM ADD COLUMN IWARNIP VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		COMMIT  WORK;
	END
	$ 
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ICPU_VALUE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD COLUMN ICPU_VALUE VARCHAR(10) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMEM_VALUE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD COLUMN IMEM_VALUE VARCHAR(10) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IREADKB_VALUE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD COLUMN IREADKB_VALUE VARCHAR(10) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IWRITEKB_VALUE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MONITOR_RESULT ADD COLUMN IWRITEKB_VALUE VARCHAR(10) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		COMMIT  WORK;
	END
	$
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMESSAGE' AND TABNAME='IEAI_DATACOLLECT_WARN' AND TYPENAME!='CLOB';
		IF LI_EXISTS = 1 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DATACOLLECT_WARN DROP COLUMN IMESSAGE';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		COMMIT  WORK;
	END
	$
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMESSAGE' AND TABNAME='IEAI_DATACOLLECT_WARN';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_DATACOLLECT_WARN ADD COLUMN IMESSAGE CLOB ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		COMMIT  WORK;
	END
	$
	
	-- -- 8.1.0 datacollect patch is as follows
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DATACOLLECT_TIME';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='CREATE TABLE IEAI_DATACOLLECT_TIME(IID DECIMAL(19) NOT NULL ,IINSTID DECIMAL(19) ,MAXTIME DECIMAL(19) ,CONSTRAINT PK_IEAI_DATACOLLECT_TIME PRIMARY KEY(IID))';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		
	END
	$
	
	BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_COLLECT_CHARSET';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='CREATE TABLE IEAI_COLLECT_CHARSET(IID DECIMAL(12) NOT NULL ,INAME VARCHAR(20) ,IDESC VARCHAR(500) ,CONSTRAINT PK_IEAI_IEAI_COLLECT_CHARSET PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;

	
	END
	$
	-- -- 8.3.0 datacollect patch is as follows
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTRANSFERBLANK' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD COLUMN ISTRANSFERBLANK DECIMAL(1) default 1';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTRANSFERBLANK' AND TABNAME='IEAI_UNIFYAGENT_COLLECTMODEL';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_COLLECTMODEL ADD COLUMN ISTRANSFERBLANK DECIMAL(1) default 1';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;

		
	END
	$
	
	
	BEGIN
		DECLARE LS_SQL VARCHAR(2000);
		DECLARE  LI_EXISTS  NUMERIC(2);
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IBUSINESSIP' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IBUSINESSIP VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IBUSINESSSYS' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN IBUSINESSSYS VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='UUID' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN UUID VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SN' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN SN VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OS_TYPE' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN OS_TYPE VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='LOCALIP' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN LOCALIP VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='OS_SMALLTYPE' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN OS_SMALLTYPE VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISONCLOUD' AND TABNAME='IEAI_AGENTINFO';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENTINFO ADD COLUMN ISONCLOUD VARCHAR(3) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SCRIPTPATH' AND TABNAME='IEAI_AGENT_MAINTAIN_TASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD SCRIPTPATH VARCHAR(1024) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='SHEETNAME' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD SHEETNAME VARCHAR(16)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IMAINBAKFILESIZE' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD IMAINBAKFILESIZE VARCHAR(4000)';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EXCELADDRESS' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD EXCELADDRESS VARCHAR(4000) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EXCELNAME' AND TABNAME='IEAI_UNIFYAGENT_CM_INST';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_UNIFYAGENT_CM_INST ADD EXCELNAME VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UPDATE_RESULT';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_UPDATE_RESULT
		(iid           DECIMAL(19) not null,
		uuid          VARCHAR(64),
		icreate_time  TIMESTAMP,
		icreate_user  VARCHAR(128),
		install_state VARCHAR(16),
		msg           VARCHAR(256)
		)';		
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_UPDATE_RESULT_MAIN';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_UPDATE_RESULT_MAIN
		(iid           DECIMAL(19) not null,
		uuid          VARCHAR(64),
		icreate_time  TIMESTAMP,
		icreate_user  VARCHAR(128),
		install_state VARCHAR(16),
		msg           VARCHAR(256)
		)';		
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DAEMONS_MANAGEMENT';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_DAEMONS_MANAGEMENT
		(daemons_id     DECIMAL(19) not null,
		daemons_ip     VARCHAR(255),
		daemons_port   DECIMAL(19),
		idelete_flag   DECIMAL(1) default 0,
		daemons_status VARCHAR(16),
		icreate_time   TIMESTAMP,
		icreate_user   VARCHAR(128),
		uuid           VARCHAR(64)
		)';	
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DAEMONS_PROXY_RELATION';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_DAEMONS_PROXY_RELATION
		(iid          DECIMAL(19) not null,
		daemons_uuid VARCHAR(64),
		proxy_id     DECIMAL(19),
		proxy_type   DECIMAL(19),
		icreate_time TIMESTAMP,
		icreate_user VARCHAR(32)
		)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_DEPLOYMENT_PLAN';
		IF LI_EXISTS = 0 THEN
		SET
			LS_SQL ='create table IEAI_DEPLOYMENT_PLAN
		(iid                          DECIMAL(19) not null,
		uuid                         VARCHAR(64),
		sftp_address                 VARCHAR(1024),
		sftp_password                VARCHAR(64),
		sftp_user                    VARCHAR(64),
		sftp_account_number          VARCHAR(32),
		collection_proxy             VARCHAR(1024),
		installation_package_address VARCHAR(1024),
		time_out                     VARCHAR(64),
		icreate_time                 TIMESTAMP,
		icreate_user                 VARCHAR(128),
		sftp_port                    DECIMAL(19)
		)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_MONITOR_CONFIG';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_MONITOR_CONFIG
		(iid            DECIMAL(19) not null,
		iagent_id      DECIMAL(19),
		daemons_id     VARCHAR(64),
		iagent_name    VARCHAR(64),
		iagent_ip      VARCHAR(64),
		state          VARCHAR(16),
		time_interval  DECIMAL(19),
		task_name      VARCHAR(64),
		script_address VARCHAR(1024),
		icreate_time   TIMESTAMP,
		icreate_user   VARCHAR(64)
		)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_MONITOR_RESULT_MAIN';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_AGENT_MONITOR_RESULT_MAIN
		(iid   DECIMAL(19) not null,
		iagent_id       DECIMAL(19) not null,
		imonitor_id     DECIMAL(19) not null,
		imonitor_time   DATE not null,
		imonitor_result VARCHAR(512),
		idatetime       TIMESTAMP,
		icpu_value      VARCHAR(10),
		imem_value      VARCHAR(10),
		ireadkb_value   VARCHAR(10),
		iwritekb_value  VARCHAR(10),
		flag            VARCHAR(32)
		)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_IINSTALLATION_RECORD';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_IINSTALLATION_RECORD
		(iid                          DECIMAL(19) not null,
		 uuid                         VARCHAR(64),
		sftp_address                 VARCHAR(1024),
		sftp_password                VARCHAR(64),
		sftp_user                    VARCHAR(64),
		sftp_account_number          VARCHAR(32),
		collection_proxy             VARCHAR(1024),
		installation_package_address VARCHAR(1024),
		time_out                     VARCHAR(64),
		icreate_time                 TIMESTAMP,
		icreate_user                 VARCHAR(128),
		install_state                VARCHAR(16),
		del_flag                     VARCHAR(16),
		sftp_port                    DECIMAL(19),
		msg                          VARCHAR(1024),
		iagentup_id                  DECIMAL(19)
		)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_IINSTALLATION_RECORD_MAIN';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='create table IEAI_IINSTALLATION_RECORD_MAIN
		(iid                          DECIMAL(19) not null,
		uuid                         VARCHAR(64),
		sftp_address                 VARCHAR(1024),
		sftp_password                VARCHAR(64),
		sftp_user                    VARCHAR(64),
		sftp_account_number          VARCHAR(32),
		collection_proxy             VARCHAR(1024),
		installation_package_address VARCHAR(1024),
		time_out                     VARCHAR(64),
		icreate_time                 TIMESTAMP,
		icreate_user                 VARCHAR(128),
		install_state                VARCHAR(16),
		del_flag                     VARCHAR(16),
		sftp_port                    DECIMAL(19),
		msg                          VARCHAR(1024),
		iagentup_id                  DECIMAL(19)
		)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_LIVE';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='CREATE TABLE IEAI_AGENT_LIVE(IID DECIMAL(19) NOT NULL,IAGENTIID DECIMAL(19),IAGENTIP VARCHAR(20),STATUS DECIMAL(2),UPDATETIME DECIMAL(19),SERVERIP VARCHAR(20),UUID VARCHAR(255) ,CONSTRAINT PK_IEAI_AGENT_LIVE PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_LIVE_HISTORY';
		IF LI_EXISTS = 0 THEN
		SET
		LS_SQL ='CREATE TABLE IEAI_AGENT_LIVE_HISTORY(IID DECIMAL(19) NOT NULL,IAGENTIID DECIMAL(19),IAGENTIP VARCHAR(20),STATUS DECIMAL(2),UPDATETIME DECIMAL(19),SERVERIP VARCHAR(20),UUID VARCHAR(255) ,CONSTRAINT IEAI_AGENT_LIVE_HISTORY PRIMARY KEY(IID))';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END IF;

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_FILE_UPLOAD_MONITOR';
        IF LI_EXISTS = 0 THEN
		SET
        LS_SQL ='CREATE TABLE IEAI_FILE_UPLOAD_MONITOR (IID DECIMAL(19) NOT NULL, UPLOAD_PATH   VARCHAR(512), AGENT_IP   VARCHAR(32), AGENT_PORT   DECIMAL(19), STATE  VARCHAR(64), MSG  VARCHAR(1024), CREATE_TIME  TIMESTAMP, CREATE_NAME  VARCHAR(32),  FILE_UPLOAD_IID DECIMAL(19) ,CONSTRAINT IEAI_FILE_UPLOAD_MONITOR PRIMARY KEY(IID))';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
        END IF;

        SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.TABLES WHERE TABNAME='IEAI_AGENT_MONITOR';
        IF LI_EXISTS = 0 THEN
			SET
            LS_SQL ='CREATE TABLE IEAI_AGENT_FILE_UPLOAD(IID DECIMAL(19) NOT NULL,FILE_NAME  VARCHAR(512),UPLOAD_NAME VARCHAR(32),UPLOAD_TIME  TIMESTAMP,STORAGE_BLOB  BLOB,STORAGE_TYPE   VARCHAR(12), STORAGE_CONFIG  CLOB,PRE_SCRIPT_NAME    VARCHAR(32),  THE_REAR_SCRIPT_NAME VARCHAR(32), CONSTRAINT IEAI_AGENT_MONITOR PRIMARY KEY(IID));';
        PREPARE	SQLA FROM LS_SQL;
        EXECUTE SQLA;
        END IF;


		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='UPGRADESCRIPTNAME' AND TABNAME='IEAI_AGENT_MAINTAIN_TASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD UPGRADESCRIPTNAME VARCHAR(255) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='UPGRADESCRIPTCONTENT' AND TABNAME='IEAI_AGENT_MAINTAIN_TASK';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD UPGRADESCRIPTCONTENT  CLOB ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FLAG' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
		IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_AGENT_MONITOR_RESULT ADD FLAG VARCHAR(32) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
		END IF;
		
		
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=32;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(32,''stopagentcmd'',''./stopagent.sh'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_PARAMETER_CONFIG  WHERE IID=31;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_PARAMETER_CONFIG (IID, IPARAMETER, IPARAVALUE) VALUES(31,''startagentcmd'',''./startagent.sh'')';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='IHOST_TYPE' AND TABNAME='IEAI_COMPUTER_LIST';
	IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_COMPUTER_LIST ADD IHOST_TYPE VARCHAR(64) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='FILE_UPLOAD_IID' AND TABNAME='IEAI_AGENT_MAINTAIN_TASK';
	IF LI_EXISTS = 0 THEN
		SET LS_SQL ='ALTER TABLE IEAI_AGENT_MAINTAIN_TASK ADD COLUMN FILE_UPLOAD_IID DECIMAL(19)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END IF;
	
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='EXCEPTIONSIZE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
	IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_AGENT_MONITOR_RESULT ADD EXCEPTIONSIZE VARCHAR(64) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='DISK_USAGE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
	IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_AGENT_MONITOR_RESULT ADD DISK_USAGE VARCHAR(64) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='THREADSIZE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
	IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_AGENT_MONITOR_RESULT ADD THREADSIZE VARCHAR(64) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='TASKSIZE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
	IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_AGENT_MONITOR_RESULT ADD TASKSIZE VARCHAR(64) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;
	SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='AGENT_STATE' AND TABNAME='IEAI_AGENT_MONITOR_RESULT';
	IF LI_EXISTS = 0 THEN
			SET LS_SQL ='ALTER TABLE  IEAI_AGENT_MONITOR_RESULT ADD AGENT_STATE VARCHAR(64) ';
			PREPARE	SQLA FROM LS_SQL;
			EXECUTE SQLA;
	END IF;

		

		COMMIT  WORK;
	END
	$