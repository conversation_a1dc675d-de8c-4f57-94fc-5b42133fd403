package com.ideal.ieai.core.io.project;

import org.jdom2.Element;

import com.ideal.ieai.core.basis.InvalidIndexException;
import com.ideal.ieai.core.data.DataTypeNames;
import com.ideal.ieai.core.element.IEAISchema;
import com.ideal.ieai.core.element.IEAISchemaRef;
import com.ideal.ieai.core.element.ISchemaNodeDef;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.core.io.XMLMarshaller;

public class IEAISchemaM implements XMLMarshaller
{
    private static IEAISchemaM _instance;

    private IEAISchemaM()
    {
    }

    public static IEAISchemaM getInstance ()
    {
        if (null == _instance)
        {
            _instance = new IEAISchemaM();
        }
        return _instance;
    }

    public Element translate ( Object TheObject ) throws MarshallingException
    {
        /* beagin update by liang_guo */
        // 该段是修改的地方，原来没有被注释掉，是测试数据库接口的删除，读操作，和写操作时注释掉的,当没有注释掉时远程执行不好使
        // if (!(TheObject instanceof SchemaLeafNodeDef))
        // {
        // throw new MarshallingException(MarshallingException.INVALID_OBJECT_TYPE);
        // }
        /* end update by liang_guo */
        IEAISchema schema = (IEAISchema) TheObject;
        Element rootE = new Element(DataTypeNames.TYPE_NAME_SCHEMA);
        Element schemaE = new Element(schema.getTypeName());
        rootE.addContent(schemaE);
        for (int i = 0; i < schema.getSize(); i++)
        {
            ISchemaNodeDef node;
            try
            {
                node = schema.getChild(i);
            } catch (InvalidIndexException ex)
            {
                throw new MarshallingException("Invalid schma child node!");
            }
            if (node instanceof IEAISchema)
            {
                IEAISchema schema2 = ((IEAISchema) node);
                Element childSchemaElement = translate(schema2);
                schemaE.addContent(childSchemaElement);
            } else if (node instanceof IEAISchemaRef)
            {
                Element refElement = new Element(DataTypeNames.IEAISCHEMA_REF_NAME);
                refElement.setText(node.getName());
                schemaE.addContent(refElement);
            } else
            // basic type,array,map
            {
                Element basicElement = new Element(node.getTypeName());
                basicElement.setText(node.getName());
                schemaE.addContent(basicElement);
            }
        }
        return rootE;
    }
}
