package com.ideal.ieai.server.repository.monitor;

import com.ideal.ieai.core.element.Workflow;
import com.ideal.ieai.core.element.workflow.BasicActElement;
import com.ideal.ieai.server.engine.execactivity.ExecAct;
//yuxh 20180327 福建
public class ZYWarnThread extends Thread
{
    private ExecAct         execAct;
    private BasicActElement actElem;
    private String          method;
    private Workflow         flow;
    
    public ZYWarnThread(Workflow flow,ExecAct execAct, BasicActElement actElem, String method)
    {
        this.execAct = execAct;
        this.actElem = actElem;
        this.method = method;
        this.flow = flow;
    }

    public void run ()
    {
        if ("timeOutWarn".equals(method))
        {
            MonitorSystem ms = MonitorSystem.getInstance();
            String prjName = flow.getNameSpace();
            String flowName = flow.getName();
            SendSocketWarnZY.sendSocketWarnZY(execAct.getScopeId(), prjName, flowName, execAct.getFlowId(),
                execAct.getActName(), execAct.getRexecRequestId(), "", ms.ACT_TIMEOUT_STUDIO, ms.STATUS_ALERT, "", "",
                "");
        }
    }
}
