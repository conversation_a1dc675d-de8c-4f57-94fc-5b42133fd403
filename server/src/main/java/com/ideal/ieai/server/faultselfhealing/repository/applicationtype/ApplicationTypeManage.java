package com.ideal.ieai.server.faultselfhealing.repository.applicationtype;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;



@SuppressWarnings(value = { "all" })
public class ApplicationTypeManage
{
    private Logger log = Logger.getLogger(ApplicationTypeManage.class);

    /**
     * 查询 应用维护启动,当前选择 应用名称 次数
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年5月24日
     * @param conn
     * @param sysName : 应用名称
     * @return
     * @throws Exception
     * return int
     */
    public int queryApplicationTypeCount ( Connection conn, String sysName)
            throws RepositoryException
    {
        int applicationCount = 0 ;// 当前应用名称的次数
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "SELECT count(1) FROM IEAI_APP_STARTUP_INFO WHERE IINSTANCENAME=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                applicationCount = Integer.parseInt(rs.getString(1));
            }
        } catch (Exception e)
        {
            log.error("ApplicationTypeManage.queryApplicationTypeCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "queryApplicationTypeCount", log);
        }
        return applicationCount;
    }

    /**
     * @description 增加类型的判断，区分两种类型的同名工程
     * <AUTHOR>
     * @date  2022/5/16 14:26
     * @param: conn
     * @param: sysName
     * @param: apmType
     * @return int
    */
    public int queryApplicationTypeCount ( Connection conn, String sysName,int apmType)
            throws RepositoryException
    {
        int applicationCount = 0 ;// 当前应用名称的次数
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "SELECT count(1) FROM IEAI_APP_STARTUP_INFO WHERE IINSTANCENAME=? AND IAPMTYPE=?";
        try
        {
            ps = conn.prepareStatement(sql);
            ps.setString(1, sysName);
            ps.setLong(2, apmType);
            rs = ps.executeQuery();
            while (rs.next())
            {
                applicationCount = Integer.parseInt(rs.getString(1));
            }
        } catch (Exception e)
        {
            log.error("ApplicationTypeManage.queryApplicationTypeCount is error", e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        } finally
        {
            DBResource.closePSRS(rs, ps, "queryApplicationTypeCount", log);
        }
        return applicationCount;
    }
    /**
     * 添加  应用维护启动表 :IEAI_APP_STARTUP_INFO
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年5月24日
     * @param con
     * @param apmType
     * @param sysName
     * @param version
     * @param instName_input
     * @throws RepositoryException
     * return void
     */
    public void addApplicationType ( Connection con, int apmType, String sysName, String version,
            String instName_input ) throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
            long iid = IdGenerator.createId("IEAI_APP_STARTUP_INFO", con);
            String sql = "insert into IEAI_APP_STARTUP_INFO( IID, IAPMTYPE, IINSTANCENAME, IVERSIONALIAS, IRUNINSNAME ) values( ?, ?, ?, ?, ? )";
            ps = con.prepareStatement(sql);
            ps.setLong(1, iid);
            ps.setLong(2, apmType);
            ps.setString(3, sysName);
            ps.setString(4, version);
            ps.setString(5, instName_input);
            ps.executeUpdate();
            con.commit();
        } catch (Exception e)
        {
            log.error("ApplicationTypeManage.addApplicationType is error", e);
            throw new RepositoryException(ServerError.ERR_DB_INSERT);
        } finally
        {
            DBResource.closePSRS(rs, ps, "addApplicationType", log);
        }
    }
    /**
     * 更新 应用维护启动表 : IEAI_APP_STARTUP_INFO
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年5月24日
     * @param con
     * @param apmType
     * @param sysName
     * @param version
     * @param instName_input
     * return void
     */
    public void updateApplicationType ( Connection con, int apmType, String sysName, String version,
            String instName_input )throws RepositoryException
    {
        PreparedStatement ps = null;
        ResultSet rs = null;
        try
        {
//            String sqlUpdate = " UPDATE IEAI_APP_STARTUP_INFO SET  IAPMTYPE=?, IVERSIONALIAS=?, IRUNINSNAME=? WHERE IINSTANCENAME=? ";
            //更新的时候增加类型，区分同名
            String sqlUpdate = " UPDATE IEAI_APP_STARTUP_INFO SET  IVERSIONALIAS=?, IRUNINSNAME=? WHERE IINSTANCENAME=? AND IAPMTYPE=? ";
            ps = con.prepareStatement(sqlUpdate);
            ps.setString(1, version);
            ps.setString(2, instName_input);
            ps.setString(3, sysName);
            ps.setLong(4, apmType);
            ps.executeUpdate();
            con.commit();
        } catch (Exception e)
        {
            log.error("ApplicationTypeManage.updateApplicationType is error", e);
            throw new RepositoryException(ServerError.ERR_DB_UPDATE);
        } finally
        {
            DBResource.closePSRS(rs, ps, "updateApplicationType", log);
        }
    }
    /**
     * 查询 应用维护启动  {最后一次执行} 的相关信息 回填
     * <li>Description:</li>
     * <AUTHOR>
     * 2021年5月25日
     * @param sysName
     * @return
     * return Map<String,Object>
     */
    public Map<String, Object> getInformation ( String sysName ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        // 查询   操作类型 ,维护场景 , 应急预案
        String sql = "SELECT IAPMTYPE,IVERSIONALIAS,IRUNINSNAME FROM IEAI_APP_STARTUP_INFO WHERE IINSTANCENAME=?";
        // 查询 当前应用名称 应急预案的: IID 及  名称
        String sql_1 = "select T1.IID IID,T2.IPARAVALUE IPARAVALUE from IEAI_INSTANCE_VERSION T1,IEAI_SUS_BASIC_INFO T2 where T1.IID=T2.IINSTANCEID and T1.ISYSTYPE=16 and T1.IAPMTYPE=? and T1.IINSTANCENAME=? and T1.IVERSIONALIAS=? and T2.IPARAMETER=? and T2.IPARAVALUE=?"+" order by T1.ITIME desc";

        try
        {
            conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_APM);
            ps = conn.prepareStatement(sql);
            ps.setString(1, sysName);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("apmType", rs.getString("IAPMTYPE")); // 操作类型
                map.put("version", rs.getString("IVERSIONALIAS")); // 维护场景
                map.put("instName_input", rs.getString("IRUNINSNAME")); // 应急预案
            }
            if (rs != null)
            {
                rs.close();
            }
            if (ps != null)
            {
                ps.close();
            }
            if(!map.isEmpty()){//map有数据 才去查询
                //  查询 当前应用名称 应急预案的: IID 及  应急预案 名称
                ps = conn.prepareStatement(sql_1);
                int apmType =  Integer.parseInt(map.get("apmType").toString());
                ps.setInt(1, apmType);
                ps.setString(2, sysName);
                ps.setString(3, map.get("version").toString());
                if(apmType == 0)
                {
                    ps.setString(4, "应急预案名称");
                }else
                {
                    ps.setString(4, "数据获取场景名称");
                }
                ps.setString(5, map.get("instName_input").toString());
                rs = ps.executeQuery();
                while (rs.next())
                {
                    map.put("iid", rs.getString("IID")); // IID
                    map.put("instName_input", rs.getString("IPARAVALUE")); // 应急预案
                    map.put("timeOutApm",getTiemOutApm(rs.getString("IID")));//超时时间
                    break;
                }
            }
        } catch (Exception e)
        {
            log.error("method：" + Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);

        } finally
        {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return map;
    }

    /**
     * @description 查询 应用维护启动  {最后一次执行} 的相关信息 回填,增加操作类型，避免两种类型存在相同工程名称，最近一次显示错乱
     * <AUTHOR>
     * @date  2022/4/26 16:35
     * @param: sysName
     * @param: apmType
     * @return java.util.Map<java.lang.String,java.lang.Object>
    */
    public Map<String, Object> getInformation ( String sysName,Long apmType ) throws RepositoryException
    {
        Map<String, Object> map = new HashMap<String, Object>();
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        // 查询   操作类型 ,维护场景 , 应急预案
        String sql = "SELECT IAPMTYPE,IVERSIONALIAS,IRUNINSNAME FROM IEAI_APP_STARTUP_INFO WHERE IINSTANCENAME=? and IAPMTYPE = ? ";
        // 查询 当前应用名称 应急预案的: IID 及  名称
        String sql_1 = "select T1.IID IID,T2.IPARAVALUE IPARAVALUE from IEAI_INSTANCE_VERSION T1,IEAI_SUS_BASIC_INFO T2 where T1.IID=T2.IINSTANCEID and T1.ISYSTYPE=16 and T1.IAPMTYPE=? and T1.IINSTANCENAME=? and T1.IVERSIONALIAS=? and T2.IPARAMETER=? and T2.IPARAVALUE=?"+" order by T1.ITIME desc";

        try
        {
            conn = DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_APM);
            ps = conn.prepareStatement(sql);
            ps.setString(1, sysName);
            ps.setLong(2, apmType);
            rs = ps.executeQuery();
            while (rs.next())
            {
                map.put("apmType", rs.getString("IAPMTYPE")); // 操作类型
                map.put("version", rs.getString("IVERSIONALIAS")); // 维护场景
                map.put("instName_input", rs.getString("IRUNINSNAME")); // 应急预案
            }
            if (rs != null)
            {
                rs.close();
            }
            if (ps != null)
            {
                ps.close();
            }
            if(!map.isEmpty()){//map有数据 才去查询
                //  查询 当前应用名称 应急预案的: IID 及  应急预案 名称
                ps = conn.prepareStatement(sql_1);
//                int apmType =  Integer.parseInt(map.get("apmType").toString());
                ps.setLong(1, apmType);
                ps.setString(2, sysName);
                ps.setString(3, map.get("version").toString());
                if(apmType == 0)
                {
                    ps.setString(4, "应急预案名称");
                }else
                {
                    ps.setString(4, "数据获取场景名称");
                }
                ps.setString(5, map.get("instName_input").toString());
                rs = ps.executeQuery();
                while (rs.next())
                {
                    map.put("iid", rs.getString("IID")); // IID
                    map.put("instName_input", rs.getString("IPARAVALUE")); // 应急预案
                    map.put("timeOutApm",getTiemOutApm(rs.getString("IID")));//超时时间
                    break;
                }
            }
        } catch (Exception e)
        {
            log.error("method：" + Thread.currentThread().getStackTrace()[1].getMethodName(), e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);

        } finally
        {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return map;
    }

    /**
     * @description
     * <AUTHOR>
     * @date  2022/4/26 16:34
     * @param: iid
     * @return java.lang.String
    */
    public String getTiemOutApm(String iid){

        String timOutApm="0";
        if(null==iid||"0".equals(iid)){
            return timOutApm;
        }
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "select iparavalue from ieai_sus_basic_info where iinstanceid=? and iparameter=? order by iinstanceid desc ";
        try{
            conn=DBResource.getConnection(Thread.currentThread().getStackTrace()[1].getMethodName(), log, Constants.IEAI_APM);
            ps=conn.prepareStatement(sql);
            ps.setInt(1,Integer.valueOf(iid));
            ps.setString(2,"超时时间");
            rs = ps.executeQuery();
            while (rs.next()){
                timOutApm= rs.getString("iparavalue");
                break;
            }
        }catch (Exception e){
            log.error("ApplicationTypeManage.getTiemOutApm is error", e);
        }finally {
            DBResource.closeConn(conn,rs,ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return timOutApm;
    }
}
