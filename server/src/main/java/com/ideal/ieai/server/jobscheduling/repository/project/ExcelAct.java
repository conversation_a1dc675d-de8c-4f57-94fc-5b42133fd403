package com.ideal.ieai.server.jobscheduling.repository.project;

/**
 * <ul>
 * <li>Title: ExcelAct.java</li>
 * <li>Description:</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 *         2015年7月28日
 */
public class ExcelAct
{
    private long   aid;
    private long   iid;
    private String projectName;
    private String flowname;
    private String actName;
    private long   actId;
    private long   actsuccid;
    private String actDesc;
    private String actType;
    private String succConditions;   // 触发条件

    private String callPrjName;
    private String callFlowname;

    private int    iokfileindweek;   // 延时器 时间
    private String ishellhouse;       // 脚本外壳
    private String ishellabspath;    //
    private String iagentresoutgroup;
    private String ilastline;
    private int    iweights;
    private int    iprioity;
    private String aptgroupname;
    private String aptfilename;
    private String aptresoupname;
    private String isDB2;
    private String dDB2IP;
    private String checkagentgroup;  // 文件检查组
    private String okfileabspath;
    // gg增加变量，是否是组执行、延迟时间、分支条件、重试次数、日历名称
    private int    isAgentGroup;
    private String delayTime;
    private String branchCondition;
    private int    reTryCount;
    private String calendName;

    public int getReTryCount ()
    {
        return reTryCount;
    }

    public void setReTryCount ( int reTryCount )
    {
        this.reTryCount = reTryCount;
    }

    public String getCalendName ()
    {
        return calendName;
    }

    public void setCalendName ( String calendName )
    {
        this.calendName = calendName;
    }

    public String getBranchCondition ()
    {
        return branchCondition;
    }

    public void setBranchCondition ( String branchCondition )
    {
        this.branchCondition = branchCondition;
    }

    public int getIsAgentGroup ()
    {
        return isAgentGroup;
    }

    public void setIsAgentGroup ( int isAgentGroup )
    {
        this.isAgentGroup = isAgentGroup;
    }

    public String getDelayTime ()
    {
        return delayTime;
    }

    public void setDelayTime ( String delayTime )
    {
        this.delayTime = delayTime;
    }

    private long flowid;

    /* 活动触发数 */
    private int  sucNum;
    /* 活动依赖数 */
    private int  preNum;

    public String getActName ()
    {
        return actName;
    }

    public void setActName ( String actName )
    {
        this.actName = actName;
    }

    public long getActId ()
    {
        return actId;
    }

    public void setActId ( long actId )
    {
        this.actId = actId;
    }

    public long getActsuccid ()
    {
        return actsuccid;
    }

    public void setActsuccid ( int actsuccid )
    {
        this.actsuccid = actsuccid;
    }

    public String getActType ()
    {
        return actType;
    }

    public void setActType ( String actType )
    {
        this.actType = actType;
    }

    public String getActDesc ()
    {
        return actDesc;
    }

    public void setActDesc ( String actDesc )
    {
        this.actDesc = actDesc;
    }

    public String getSuccConditions ()
    {
        return succConditions;
    }

    public void setSuccConditions ( String succConditions )
    {
        this.succConditions = succConditions;
    }

    public String getCallPrjName ()
    {
        return callPrjName;
    }

    public void setCallPrjName ( String callPrjName )
    {
        this.callPrjName = callPrjName;
    }

    public String getCallFlowname ()
    {
        return callFlowname;
    }

    public void setCallFlowname ( String callFlowname )
    {
        this.callFlowname = callFlowname;
    }

    public String getProjectName ()
    {
        return projectName;
    }

    public void setProjectName ( String projectName )
    {
        this.projectName = projectName;
    }

    public int getIokfileindweek ()
    {
        return iokfileindweek;
    }

    public void setIokfileindweek ( int iokfileindweek )
    {
        this.iokfileindweek = iokfileindweek;
    }

    public String getIshellhouse ()
    {
        return ishellhouse;
    }

    public void setIshellhouse ( String ishellhouse )
    {
        this.ishellhouse = ishellhouse;
    }

    public String getIshellabspath ()
    {
        return ishellabspath;
    }

    public void setIshellabspath ( String ishellabspath )
    {
        this.ishellabspath = ishellabspath;
    }

    public String getIagentresourcegroup ()
    {
        return iagentresoutgroup;
    }

    public void setIagentResourceGroup ( String iagentresoutgroup )
    {
        this.iagentresoutgroup = iagentresoutgroup;
    }

    public String getIlastline ()
    {
        return ilastline;
    }

    public void setIlastline ( String ilastline )
    {
        this.ilastline = ilastline;
    }

    public int getIweights ()
    {
        return iweights;
    }

    public void setIweights ( int iweights )
    {
        this.iweights = iweights;
    }

    public int getIprioity ()
    {
        return iprioity;
    }

    public void setIprioity ( int iprioity )
    {
        this.iprioity = iprioity;
    }

    public String getAptgroupname ()
    {
        return aptgroupname;
    }

    public void setAptgroupname ( String aptgroupname )
    {
        this.aptgroupname = aptgroupname;
    }

    public String getAptfilename ()
    {
        return aptfilename;
    }

    public void setAptfilename ( String aptfilename )
    {
        this.aptfilename = aptfilename;
    }

    public String getAptresoupname ()
    {
        return aptresoupname;
    }

    public void setAptresoupname ( String aptresoupname )
    {
        this.aptresoupname = aptresoupname;
    }

    public String isDB2 ()
    {
        return isDB2;
    }

    public void setDB2 ( String isDB2 )
    {
        this.isDB2 = isDB2;
    }

    public String getDB2IP ()
    {
        return dDB2IP;
    }

    public void setDB2IP ( String dB2IP )
    {
        dDB2IP = dB2IP;
    }

    public String getFlowname ()
    {
        return flowname;
    }

    public void setFlowname ( String flowname )
    {
        this.flowname = flowname;
    }

    public String getIsDB2 ()
    {
        return isDB2;
    }


    public String getCheckagentgroup ()
    {
        return checkagentgroup;
    }

    public void setCheckagentgroup ( String checkagentgroup )
    {
        this.checkagentgroup = checkagentgroup;
    }

    public String getOkfileabspath ()
    {
        return okfileabspath;
    }

    public void setOkfileabspath ( String okfileabspath )
    {
        this.okfileabspath = okfileabspath;
    }

    public int getSucNum ()
    {
        return sucNum;
    }

    public void setSucNum ( int sucNum )
    {
        this.sucNum = sucNum;
    }

    public int getPreNum ()
    {
        return preNum;
    }

    public void setPreNum ( int preNum )
    {
        this.preNum = preNum;
    }

    public long getFlowid ()
    {
        return flowid;
    }

    public void setFlowid ( long flowid )
    {
        this.flowid = flowid;
    }

    public long getAid ()
    {
        return aid;
    }

    public void setAid ( long aid )
    {
        this.aid = aid;
    }

    public long getIid ()
    {
        return iid;
    }

    public void setIid ( long iid )
    {
        this.iid = iid;
    }


    public void setActsuccid ( long actsuccid )
    {
        this.actsuccid = actsuccid;
    }

}
