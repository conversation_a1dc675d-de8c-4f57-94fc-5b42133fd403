package com.ideal.service.user.ldap.mapper;

import javax.naming.NamingException;
import javax.naming.directory.Attributes;

import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.DistinguishedName;

import com.ideal.service.user.ldap.entity.Person;


public class PersonAttributeMapper implements AttributesMapper
{
    private boolean isOc;                           // 是否为外包

    public PersonAttributeMapper(boolean isOc)
    {
        super();
        this.isOc = isOc;
    }

    @Override
    public Person mapFromAttributes ( Attributes attr ) throws NamingException
    {
        Person person = new Person();

        person.setCn((String) attr.get("cn").get());

        if (attr.get("sn") != null)
        {
            person.setSn((String) attr.get("sn").get());
        }
        if (attr.get("name") != null)
        {
            person.setName((String) attr.get("name").get());
        }
        if (attr.get("givenName") != null)
        {
            person.setGivenName((String) attr.get("givenName").get());
        }
        if (attr.get("displayName") != null)
        {
            person.setDisplayName((String) attr.get("displayName").get());
        }
        if (attr.get("description") != null)
        {
            person.setDescription((String) attr.get("description").get());
        }
        if (attr.get("mobile") != null)
        {
            person.setMobile((String) attr.get("mobile").get());
        }
        if (attr.get("mail") != null)
        {
            person.setMail((String) attr.get("mail").get());
        }
        if (attr.get("sAMAccountName") != null)
        {
            person.setLoginName((String) attr.get("sAMAccountName").get());
        }
        if (attr.get("memberOf") != null)
        {
            person.setMemberOf((String) attr.get("memberOf").get());
        }
        if (attr.get("distinguishedName") != null)
        {
            person.setDistinguishedName((String) attr.get("distinguishedName").get());
            DistinguishedName path = new DistinguishedName((String) attr.get("distinguishedName").get());

            if (this.isOc)
            { // 外包
                person.setDepartmentName("外包部门");
                if (path.size() == 6)
                {
                    person.setGroupName(path.get(4).replace("ou=", ""));
                }
            } else
            {
                if (path.size() == 6)
                {
                    person.setDepartmentName(path.get(4).replace("ou=", ""));
                } else if (path.size() == 7)
                {
                    person.setDepartmentName(path.get(4).replace("ou=", ""));
                    person.setGroupName(path.get(5).replace("ou=", ""));
                }
            }
        }
        if (attr.get("userAccountControl") != null)
        {
            String control = Integer.toBinaryString(Integer.valueOf((String) attr.get("userAccountControl").get()));
            if ("1".equals(control.substring(control.length() - 2, control.length() - 1)))
            {
                person.setStatus(0);
            } else
            {
                person.setStatus(1);
            }
        }
        return person;
    }
}
