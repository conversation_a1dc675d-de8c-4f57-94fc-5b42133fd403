package com.ideal.ieai.server.platform.portal.model;

public class PortalTopModel
{
    private long iid;
    private long equipmentNum;
    private long onLineUserNum;
    private long processRelease;
    private long scriptNum;
    private long systemNum;
    private long userLoginNum;
    
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public long getEquipmentNum ()
    {
        return equipmentNum;
    }
    public void setEquipmentNum ( long equipmentNum )
    {
        this.equipmentNum = equipmentNum;
    }
    public long getOnLineUserNum ()
    {
        return onLineUserNum;
    }
    public void setOnLineUserNum ( long onLineUserNum )
    {
        this.onLineUserNum = onLineUserNum;
    }
    public long getProcessRelease ()
    {
        return processRelease;
    }
    public void setProcessRelease ( long processRelease )
    {
        this.processRelease = processRelease;
    }
    public long getScriptNum ()
    {
        return scriptNum;
    }
    public void setScriptNum ( long scriptNum )
    {
        this.scriptNum = scriptNum;
    }
    public long getSystemNum ()
    {
        return systemNum;
    }
    public void setSystemNum ( long systemNum )
    {
        this.systemNum = systemNum;
    }
    public long getUserLoginNum ()
    {
        return userLoginNum;
    }
    public void setUserLoginNum ( long userLoginNum )
    {
        this.userLoginNum = userLoginNum;
    }
    
}
