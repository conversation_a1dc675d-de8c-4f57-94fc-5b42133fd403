package com.ideal.controller.hcEnvironInfoReport;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.service.hcEnvironInfoReport.HcEnvironInfoReportService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
public class HcEnvironInfoReportController
{
    private static final Logger logger      = Logger.getLogger(HcEnvironInfoReportController.class);

    @RequestMapping("hcEnvrionInfoReport.do")
    public String mainPage ()
    {
        return "hcEnvironInfoReport/hcEnvironInfoReport";
    }

    @RequestMapping("hcEnvironViewSysName.do")
    public String hcEnvironViewSysName ()
    {
        return "hcEnvironInfoReport/environSysName";
    }


    @RequestMapping("hcOperViewInfo.do")
    public String hcOperViewInfo ()
    {
        return "hcEnvironInfoReport/operInfoPage";
    }


    @RequestMapping("hcBusiSysInfo.do")
    public String hcBusiSysInfo ()
    {
        return "hcEnvironInfoReport/busiSysInfoPage";
    }

    @RequestMapping("hcDepartSysInfo.do")
    public String hcDepartSysInfo ()
    {
        return "hcEnvironInfoReport/departSysInfoPage";
    }



    @RequestMapping("hcDataBaseComInfoInfo.do")
    public String hcDataBaseComInfoInfo ()
    {
        return "hcEnvironInfoReport/dataBaseComInfoPage";
    }

    @RequestMapping("getEnvironReportInfoList.do")
    @ResponseBody
    public Map<String, Object>  getEnvironReportInfoList (HttpServletRequest request) {

        String environInput = request.getParameter("environInput");
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getEnvironReportInfoList(environInput,startStr,limitStr,userId);
    }

    @RequestMapping("exportHcEnvironReportInfo.do")
    public void exportHcEnvironReportInfo ( HttpServletResponse response, HttpServletRequest request,Long[] envids )
    {
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        HcEnvironInfoReportService.getInstance().exportHcEnvironReportInfo(response,envids,userId);
    }


    @RequestMapping("exportOperateSysReportInfo.do")
    public void exportOperateSysReportInfo ( HttpServletResponse response, HttpServletRequest request,String[] osnames )
    {
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        HcEnvironInfoReportService.getInstance().exportOperateSysReportInfo(response,osnames,userId);
    }


    @RequestMapping("exportBusiSysReportInfo.do")
    public void exportBusiSysReportInfo ( HttpServletResponse response, HttpServletRequest request,String[] businames )
    {
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        HcEnvironInfoReportService.getInstance().exportBusiSysReportInfo(response,businames,userId);
    }


    @RequestMapping("exportDepartmentReportInfo.do")
    public void exportDepartmentReportInfo ( HttpServletResponse response, HttpServletRequest request,String[] departments )
    {
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        HcEnvironInfoReportService.getInstance().exportDepartmentReportInfo(response,departments,userId);
    }


    @RequestMapping("exportDataBaseReportInfo.do")
    public void exportDataBaseReportInfo ( HttpServletResponse response, HttpServletRequest request,String[] dataBaseNames )
    {
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        HcEnvironInfoReportService.getInstance().exportDataBaseReportInfo(response,dataBaseNames,userId);
    }

    @RequestMapping("getOperSysReportInfoList.do")
    @ResponseBody
    public Map<String, Object>  getOperSysReportInfoList (HttpServletRequest request) {

        String operSysInput = request.getParameter("operSysInput");
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        return HcEnvironInfoReportService.getInstance().getOperSysReportInfoList(operSysInput,startStr,limitStr);
    }

    @RequestMapping("getBusiSysReportInfoList.do")
    @ResponseBody
    public Map<String, Object>  getBusiSysReportInfoList (HttpServletRequest request) {

        String busiSysInput = request.getParameter("busiSysInput");
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getBusiSysReportInfoList(busiSysInput,startStr,limitStr,userId);
    }

    @RequestMapping("departmentList.do")
    @ResponseBody
    public Object getDepartmentList ( HttpServletRequest request )
    {
        List<Map> list = new ArrayList<Map>();
        try
        {
            list = HcEnvironInfoReportService.getInstance().getDepartmentList( Constants.IEAI_HEALTH_INSPECTION);
        } catch (RepositoryException e)
        {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName(), e);
        }
        return list;
    }

    @RequestMapping("getDepartReportInfoList.do")
    @ResponseBody
    public Map<String, Object>  getDepartReportInfoList (HttpServletRequest request) {

        String deapartInput = request.getParameter("deapartInput");
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getDepartReportInfoList(deapartInput,startStr,limitStr,userId);
    }


    @RequestMapping("getDataBaseInfoList.do")
    @ResponseBody
    public Map<String, Object>  getDataBaseInfoList (HttpServletRequest request) {

        String dataBaseInput = request.getParameter("dataBaseInput");
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        return HcEnvironInfoReportService.getInstance().getDataBaseInfoList(dataBaseInput,startStr,limitStr);
    }


    @RequestMapping("getEnvSysInfoList.do")
    @ResponseBody
    public Map<String, Object>  getEnvSysInfoList (HttpServletRequest request) {
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String envid = request.getParameter("envid");
        String sysNameVal = request.getParameter("sysNameVal");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getEnvSysInfoList(envid,sysNameVal,userId,startStr,limitStr);
    }


    @RequestMapping("getOperInfoList.do")
    @ResponseBody
    public Map<String, Object>  getOperInfoList (HttpServletRequest request) {
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String osName = request.getParameter("osName");
        String sysNameVal = request.getParameter("sysNameVal");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getOperInfoList(osName,sysNameVal,userId,startStr,limitStr);
    }

    @RequestMapping("getBusiEnvInfoList.do")
    @ResponseBody
    public Map<String, Object>  getBusiEnvInfoList (HttpServletRequest request) {
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String sysId = request.getParameter("sysId");
        String envNameVal = request.getParameter("envNameVal");
        return HcEnvironInfoReportService.getInstance().getBusiEnvInfoList(sysId,envNameVal,startStr,limitStr);
    }


    @RequestMapping("getDepartSysInfoList.do")
    @ResponseBody
    public Map<String, Object>  getDepartSysInfoList (HttpServletRequest request) {

        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String department = request.getParameter("department");
        String busiNameVal = request.getParameter("busiNameVal");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getDepartSysInfoList(department,busiNameVal,userId,startStr,limitStr);
    }


    @RequestMapping("getDataBaseComInfoList.do")
    @ResponseBody
    public Map<String, Object>  getDataBaseComInfoList (HttpServletRequest request) {
        String startStr =  request.getParameter("start");
        String limitStr =  request.getParameter("limit");
        String dataBaseName = request.getParameter("dataBaseName");
        String comIpVal = request.getParameter("comIpVal");
        String userId = SessionData.getSessionData(request).getUserInnerCode();
        return HcEnvironInfoReportService.getInstance().getDataBaseComInfoList(dataBaseName,comIpVal,userId,startStr,limitStr);
    }



}
