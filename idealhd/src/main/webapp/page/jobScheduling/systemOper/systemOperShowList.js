Ext
		.onReady(function() {
			$("body").unbind();
			destroyRubbish('showSysOper.do');
			var mainPheight = document.getElementById("systemOperList_area").style.height;
			var itemsPerPage = 20;
			// 所属系统下拉框数据源Model
			/** *********************Model********************* */
			Ext.define('sysOperList_systemModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'i_system_id',
					type : 'string'
				} ]
			});
			// 工程名下拉框数据源Model
			Ext.define('prjNameModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'i_prj_name',
					type : 'string'
				} ]
			});
			// 工作流名下拉框数据源Model
			Ext.define('flowNameModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'flowName',
					type : 'string'
				} ]
			});

			/** *********************Store********************* */
			// 所属系统下拉框数据源
			var systemOperStore_isystem_url = 'queryHomeInfo.do?action=jzQueryHomeInfo';
			var systemOperStore_isystem = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'sysOperList_systemModel',
				proxy : {
					type : 'ajax',
					timeout : 120000,
					url : systemOperStore_isystem_url,
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			// 工程名下拉框数据源
			var cstore_iprjName = Ext.create('Ext.data.Store', {
				autoLoad : true,
				autoDestroy : true,
				model : 'prjNameModel',
				proxy : {
					type : 'ajax',
					timeout : 120000,
					url : 'queryHomeInfo.do?action=queryPrjInfo',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			// 工作流名下拉框数据源
			var flowNameStore = Ext.create('Ext.data.Store', {
				autoLoad : false,
				autoDestroy : true,
				model : 'flowNameModel',
				proxy : {
					type : 'ajax',
					timeout : 120000,
					url : 'flowqueryTrial.do?action=getFlows',
					reader : {
						type : 'json',
						root : 'dataList'
					}
				}
			});
			flowNameStore.on('beforeload', function(store, options) {
				var new_params = {
					prjName : systemOperList_prj_Name.getValue()
				};
				Ext.apply(flowNameStore.proxy.extraParams, new_params);
			});
			/** *********************组件********************* */
			// 所属系统下拉框
			var systemOperList_sys_Name = Ext.create('Ext.form.field.ComboBox',
					{
						id : 'addcoreflow_sysNameComBox',
						width : '30%',
						labelWidth : 79,
						padding : '0 10 0 0',
						fieldLabel : '所属系统',
						displayField : 'i_system_id',
						valueField : 'i_system_id',
						editable : true,
						typeAhead : true,
						store : systemOperStore_isystem,
						queryMode : 'local',
						labelAlign : "right",
						listeners : {
							select : function() {
							},
							beforequery : function(e) {
								var combo = e.combo;
								if (!e.forceAll) {
									var value = e.query;
									combo.store.filterBy(function(record, id) {
										var text = record
												.get(combo.displayField);
										return (text.toLowerCase().indexOf(
												value.toLowerCase()) != -1);
									});
									combo.expand();
									return false;
								}
							}
						}
					});
			/*******************查询条件******************************/
			// 操作
			var operListOper_store = new Ext.data.ArrayStore({
				fields : [ 'id', 'name' ],
				data : [ [ '', '' ], [ '0', '暂停' ], [ '1', '继续' ], [ '2', '终止' ] ]

			});
			var operListOper_combox = Ext.create('Ext.form.field.ComboBox', {
				width : '20%',
				labelWidth : 90,
				padding : '0 10 0 0',
				fieldLabel : '操作名称',
				store : operListOper_store,
				displayField : 'name',
				valueField : 'id',
				triggerAction : 'all',
				editable : true,
				mode : 'local',
				labelAlign: "right"
			});
			// 状态
			var operListStatus_store = new Ext.data.ArrayStore({
				fields : [ 'id', 'name' ],
				data : [ [ '', '' ], [ '0', '运行中' ], [ '1', '操作完成' ] ]

			});
			var operListStatus_combox = Ext.create('Ext.form.field.ComboBox', {
				width : '20%',
				labelWidth : 90,
				padding : '0 10 0 0',
				fieldLabel : '运行状态',
				store : operListStatus_store,
				displayField : 'name',
				valueField : 'id',
				triggerAction : 'all',
				editable : true,
				mode : 'local',
				labelAlign: "right"
			});
			// 数据日期查询条件输入框
			var operListDatadate = Ext.create('Ext.form.field.Text', {
				width : '20%',
				labelWidth : 79,
				padding : '0 10 0 0',
				name : 'datadate',
				fieldLabel : '数据日期',
				labelAlign: "right"
			});
			// 按钮
			var search_btn = Ext.create('Ext.Button', {
				baseCls : 'Common_Btn',
				text : '查询',
				handler : function() {
					searchAdd();
				}
			});
			var reset_btn = Ext.create('Ext.Button', {
				baseCls : 'Common_Btn',
				text : '重置',
				handler : function() {
					resetAddcore();
				}
			});
			// 查询条件面板
			var sysOperList_queryForm = Ext.create('Ext.form.Panel', {
				border : false,
				bodyCls : 'x-docked-noborder-top',
				region : 'north',
				layout : 'anchor',
				collapsible : false,// 可收缩
				collapsed : false,// 默认收缩
				titleCollapse : true,
				buttonAlign : 'center',
				dockedItems : [ {
					xtype : 'toolbar',
					border : false,
					// margin : '5',
					dock : 'top',
					items : [ systemOperList_sys_Name,operListOper_combox,operListStatus_combox,operListDatadate, search_btn, reset_btn ]
				}, {
					xtype : 'toolbar',
					dock : 'top',
					border : false,
					// margin : '0',
					items : []
				} ]
			});
			var selModel = Ext.create('Ext.selection.CheckboxModel', {
				checkOnly : true,
				selType : 'cellmodel'
			});
			Ext.define('sysOperListModel', {
				extend : 'Ext.data.Model',
				fields : [ {
					name : 'systemName',
					type : 'string'
				}, {
					name : 'user',
					type : 'string'
				}, {
					name : 'startTimeStr',
					type : 'string'
				}, {
					name : 'endTimeStr',
					type : 'string'
				}, {
					name : 'statusStr',
					type : 'string'
				}, {
					name : 'operTypeStr',
					type : 'string'
				}, {
					name : 'dataDate',
					type : 'string'
				}, {
					name : 'sysFlowCount',
					type : 'string'
				} ]
			});
			// 查询列表数据源
			var sysOperList_store = Ext.create('Ext.data.Store', {
				autoLoad : true,
				remoteSort : true,
				model : 'sysOperListModel',
				pageSize : itemsPerPage,
				proxy : {
					type : 'ajax',
					timeout : 1800000,
					url : 'getSysOperList.do',
					actionMethods : {
						create : 'POST',
						read : 'POST',
						update : 'POST',
						destroy : 'POST'
					},
					reader : {
						type : 'json',
						root : 'dataList',
						totalProperty : 'total'
					}
				}
			});
			sysOperList_store.on('beforeload', function(s) {
				var params = s.getProxy().extraParams;
				var sysName = systemOperList_sys_Name.getValue();
				var status = operListStatus_combox.getValue();
				var oper = operListOper_combox.getValue();
				var datadate = operListDatadate.getValue();
				Ext.apply(params, {
					sysName : sysName,
					status : status,
					oper : oper,
					dataDate : datadate
				})
			});
			// 查询列表columns
			var coreflow_columns = [ {
				header : '序号',
				xtype : 'rownumberer',
				width : '5%',
				align : 'center',
				sortable : false
			}, {
				text : '所属系统',
				align : 'left',
				dataIndex : 'systemName',
				locked : false,
				width : '20%',
				sortable : false
			}, {
				text : '操作名称',
				align : 'center',
				width : '10%',
				dataIndex : 'operTypeStr',
				locked : false
			}, {
				text : '操作用户',
				align : 'left',
				width : '10%',
				flex : 1,
				dataIndex : 'user',
				locked : false
			}, {
				text : '开始时间',
				align : 'center',
				width : '10%',
				flex : 1,
				dataIndex : 'startTimeStr',
				locked : false
			}, {
				text : '结束时间',
				align : 'center',
				width : '10%',
				flex : 1,
				dataIndex : 'endTimeStr',
				locked : false
			}, {
				text : '运行状态',
				align : 'left',
				width : '10%',
				flex : 1,
				dataIndex : 'statusStr',
				locked : false
			}, {
				text : '数据日期',
				align : 'left',
				width : '10%',
				flex : 1,
				dataIndex : 'dataDate',
				locked : false
			}, {
				text : '操作工作流数量',
				align : 'center',
				width : '10%',
				flex : 1,
				dataIndex : 'sysFlowCount',
				locked : false
			} ];
			var sysOperList_pageBar = Ext.create('Ext.PagingToolbar', {
				id : 'sysOperList_pageBar',
				store : sysOperList_store,
				dock : 'bottom',
				displayInfo : true,
				border : false,
				baseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar'
			});
			// 查询列表grid
			var sysOperList_grid = Ext.create('Ext.grid.Panel', {
				store : sysOperList_store,
				columnLines : true,
				border : true,
				columns : coreflow_columns,
				// selModel : selModel,
				viewConfig : {
					selType : 'cellmodel',
					stripeRows : true,// 在表格中显示斑马线
					enableTextSelection : true,
					loadMask : false
				},
				renderTo : Ext.getBody(),
				bbar : sysOperList_pageBar
			});
			// 查询列表外panel
			var sysOperListPanel = Ext.create('Ext.Panel', {
				region : 'center',
				layout : 'fit',
				border : false,
				margins : grid_margin,
				witdh : '100%',
				height : '60%',
				split : true,
				items : [ sysOperList_grid ],
				listeners : {
					resize : function(a) {
						sysOperList_grid
								.setHeight(sysOperListPanel.getHeight() - 24);
					}
				}
			});
			// 主Panel
			var addaddMainPanel = Ext.create('Ext.panel.Panel', {
				renderTo : 'systemOperList_area',
				width : '100%',
				layout : 'border',
				bodyCls : 'service_platform_bodybg',
				header : false,
				border : false,
				height : mainPheight,
				items : [ sysOperList_queryForm, sysOperListPanel ]
			});

			var searchAdd = function() {
				sysOperList_pageBar.moveFirst();
			}
			var resetAddcore = function() {
				systemOperList_sys_Name.setValue('');
				operListStatus_combox.setValue('');
				operListOper_combox.setValue('');
				operListDatadate.setValue('');
			}
		});