package com.ideal.ieai.server.webservice;

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: Ideal Technologies,  Inc.</p>
 * <AUTHOR>
 * @version 3.0
 */

public class AdaptorInfo
{
    public AdaptorInfo()
    {
    }

    public static AdaptorInfo newInstance(com.ideal.ieai.commons.AdaptorInfo Info)
    {
        if (null == Info)
        {
            return null;
        }
        AdaptorInfo instance = new AdaptorInfo();
        instance.setIsFreeze(Info.isFreezed());
        instance.setUploadUser(WsUserInfo.newInstance(Info.getUploadUser()));
        instance.setId(Info.getId());
        instance.setUploadNum(Info.getUploadNum());
        instance.setUuid(Info.getUuid());
        
        instance.setFreezeUser(WsUserInfo.newInstance(Info.getFreezeUser()));
        instance.setAdpName(Info.getName());
        instance.setMajVer(Info.getMajorVersion());
        instance.setMinVer(Info.getMinorVersion());
        instance.setComment(Info.getComment());
        instance.setUploadTime(Info.getUploadTime());
        return instance;
    }

    /**
     * to override the clone method
     * to copy all the field to the clone object
     *     added by xu ming using in server manager
     * @return
     */
    public Object clone()
    {
        try
        {
            AdaptorInfo obj = (AdaptorInfo)super.clone();
            obj.adpName = this.adpName;
            obj.comment = this.comment;
            obj.isFreeze = this.isFreeze;
            obj.majVer = this.majVer;
            obj.minVer = this.minVer;
            obj.uploadUser = this.uploadUser;
            return obj;
        }
        catch (CloneNotSupportedException e)
        {
            return null;
        }

    }

    /**
     * added by xu ming
     * useing in sm
     *
     * to overide the equals method of object
     * @param obj
     * @return
     */
    public boolean equals(Object obj)
    {
        if (obj == this)
        {
            return true;
        }
        if (! (obj instanceof AdaptorInfo))
        {
            return false;
        }
        AdaptorInfo comObj = (AdaptorInfo) obj;

        //to compare all the attribute exception filedNames
        return comObj.id == id;

    }

    public String toString()
    {
        return "{" + adpName + ", " +
            majVer + ", " +
            minVer + ", " +
            isFreeze + ", " +
            uploadUser + ", " +
            comment + ", " +
            "}";
    }

    public String getAdpName()
    {
        return adpName;
    }

    public void setAdpName(String adpName)
    {
        this.adpName = adpName;
    }

    public String getComment()
    {
        return comment;
    }

    public void setComment(String comment)
    {
        this.comment = comment;
    }

    public boolean isIsFreeze()
    {
        return isFreeze;
    }

    public void setIsFreeze(boolean isFreeze)
    {
        this.isFreeze = isFreeze;
    }

    public int getMajVer()
    {
        return majVer;
    }

    public void setMajVer(int majVer)
    {
        this.majVer = majVer;
    }

    public int getMinVer()
    {
        return minVer;
    }

    public void setMinVer(int minVer)
    {
        this.minVer = minVer;
    }

    public WsUserInfo getUploadUser()
    {
        return uploadUser;
    }

    public void setUploadUser(WsUserInfo uploadUser)
    {
        this.uploadUser = uploadUser;
    }

    public long getId()
    {
        return id;
    }

    public void setId(long id)
    {
        this.id = id;
    }

    public int getUploadNum()
    {
        return uploadNum;
    }

    public void setUploadNum(int uploadNum)
    {
        this.uploadNum = uploadNum;
    }

    public java.util.Date getUploadTime()
    {
        return uploadTime;
    }

    public void setUploadTime(java.util.Date uploadTime)
    {
        this.uploadTime = uploadTime;
    }

    public WsUserInfo getFreezeUser()
    {
        return freezeUser;
    }

    public void setFreezeUser(WsUserInfo freezeUser)
    {
        this.freezeUser = freezeUser;
    }
	public String getUuid()
	{
		return uuid;
	}

	public void setUuid(String uuid)
	{
		this.uuid = uuid;
	}

    public boolean isFreeze;
    public WsUserInfo uploadUser;
    public long id;
    public int uploadNum;
    public String uuid;
    public WsUserInfo freezeUser;
    public String adpName;
    public int majVer;
    public int minVer;
    public String comment;
    public java.util.Date uploadTime;

}
