package com.ideal.ieai.studio.view.cmdui.actions;

import java.awt.event.ActionEvent;

import com.ideal.ieai.studio.view.cmdui.CmdIcons;

/**
 * <p>Title: iEAI Studio</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR>
 * @version 3.0
 */

public class EditComponentAction
    extends StudioAction
{
    public EditComponentAction()
    {
        super("", CmdIcons.imageEdit);
    }

    protected void execute(ActionEvent e)
    {
            /**@todo Implement this com.ideal.ieai.studio.view.cmdui.actions.StudioAction abstract method*/
    }

    protected void alterViews()
    {
            /**@todo Implement this com.ideal.ieai.studio.view.cmdui.actions.StudioAction abstract method*/
    }

}