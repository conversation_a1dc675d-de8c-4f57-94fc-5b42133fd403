package com.ideal.ieai.prv.provider.dbaas;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import org.apache.dubbo.config.annotation.DubboService;
import com.ideal.dubbo.interfaces.dbaas.reserveplan.IReservePlanService;
import com.ideal.dubbo.models.ReservePlanModel;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.prv.repository.hd.dubbo.dbaas.ReservePlanManager;
import com.ideal.ieai.server.repository.DBUtil;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.idgenerator.IdGenerator;
import com.ideal.ieai.server.util.BeanFormatter;
import com.ideal.ieai.server.util.Tools;

/**
 * 
 * @ClassName:  IReservePlanServiceImpl   
 * @Description:预案管理接口实现类
 * @author: baofeng_shi 
 * @date:   2020年2月18日 下午3:53:57   
 *     
 * @Copyright: 2020-2027 www.idealinfo.com Inc. All rights reserved. 
 *
 */
@DubboService
public class IReservePlanServiceImpl implements IReservePlanService
{

    private static final Logger           log      = Logger.getLogger(IReservePlanServiceImpl.class);
    private static IReservePlanServiceImpl intance = new IReservePlanServiceImpl();

    public static IReservePlanServiceImpl getInstance ()
    {
        if (intance == null)
        {
            intance = new IReservePlanServiceImpl();
        }
        return intance;
    }

    /**
     * 
     * @Title: findPlanTreeList   
     * @Description: 查询预案树形列表  
     * @param request
     * @return      
     * @author: baofeng_shi 
     * @throws RepositoryException 
     * @date:   2020年2月18日 下午3:28:20
     */
    @Override
    public List<Map<String, Object>> findPlanTreeList ( String name, String dbType, int sysType, int from )
            throws RepositoryException
    {
        return ReservePlanManager.getInstance().findPlanTreeList(0, name, dbType, sysType, from);
    }

    /**
     * 
     * @Title: checkReservePlanName   
     * @Description: 验证预案名称是否存在
     * @param id
     * @param name
     * @param sysType
     * @return
     * @throws Exception      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:43:29
     */
    @Override
    public boolean checkReservePlanName ( long id, String name, int sysType ) throws Exception
    {
        return ReservePlanManager.getInstance().checkReservePlanName(id, name, sysType);
    }

    /**
     * 
     * @Title: saveReservePlan   
     * @Description: 新增或者修改预案
     * @param model
     * @return
     * @throws Exception      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:41:31
     */
    @Override
    public Map<String, Object> saveReservePlan ( ReservePlanModel model ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> res = new HashMap<String, Object>();
        List<ReservePlanModel> list = new ArrayList<ReservePlanModel>();
        list.add(model);
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(Constants.STR_BASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(Constants.STR_DBCONNS);
            Map<String, Object> orgSql = organizeAddReservePlanSql(list, baseConn);
            if ((Boolean) orgSql.get(Constants.STR_SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(Constants.STR_EXESQLS),
                    (List<String>) orgSql.get(Constants.STR_ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, true);
                    res.put(Constants.STR_MESSAGE, " 数据保存成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, false);
                    res.put(Constants.STR_MESSAGE, " 执行信息变更sql时出现错误！");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, method, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, method, log);
                }
                res.put(Constants.STR_SUCCESS, false);
                res.put(Constants.STR_MESSAGE, " 组织信息sql时出现错误！");
                return res;
            }
        } else
        {
            res.put(Constants.STR_SUCCESS, false);
            res.put(Constants.STR_MESSAGE, " 没有基线源,  无法保存！");
            return res;
        }
    }

    /**
     * 
     * @Title: organizeAddReservePlanSql   
     * @Description: 组织预案插入或者修改sql   
     * @param list
     * @param baseConn
     * @param dbConns
     * @return      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:55:06
     */
    private Map<String, Object> organizeAddReservePlanSql ( List<ReservePlanModel> list, Connection baseConn )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        ReservePlanManager manage = ReservePlanManager.getInstance();
        String insertSqlTemplate = " INSERT INTO IEAI_SCRIPT_RESERVE_PLAN (iid,iname,icreateuser,iparentid,idbtype,iorder,icreatetime) VALUES({iId},'{iName}','{iCreateUser}',{iParentId},{iDbType},{iOrder},"
                + System.currentTimeMillis() + ") ";
        String deleteSqlTemplate = " delete from IEAI_SCRIPT_RESERVE_PLAN where IID=";
        String updateSqlTemplate = " update IEAI_SCRIPT_RESERVE_PLAN set iname='{iName}',iModifyUser='{iModifyUser}',iDbtype='{iDbType}',iOrder={iOrder},iModifyTime="
                + System.currentTimeMillis() + " where iid={iId}";
        try
        {
            for (ReservePlanModel model : list)
            {
                if (model.getiId() == 0)
                {
                    long id = IdGenerator.createId("IEAI_SCRIPT_RESERVE_PLAN", baseConn);
                    model.setiId(id);
                    BeanFormatter<ReservePlanModel> bf = new BeanFormatter<ReservePlanModel>(insertSqlTemplate);
                    String s = bf.format(model);
                    exeSqls.add(s);
                    rollbackSqls.add(deleteSqlTemplate + id);
                } else
                {
                    BeanFormatter<ReservePlanModel> bf = new BeanFormatter<ReservePlanModel>(updateSqlTemplate);
                    String s = bf.format(model);
                    exeSqls.add(s);
                    ReservePlanModel backModel = manage.getReservePlanOne(model.getiId(), baseConn);
                    if (null != backModel)
                    {
                        BeanFormatter<ReservePlanModel> rbf = new BeanFormatter<ReservePlanModel>(updateSqlTemplate);
                        String r = rbf.format(backModel);
                        rollbackSqls.add(r);
                        if(backModel.getiParentId()==0){
                            List<Long> iids=new ArrayList<Long>();
                            ReservePlanManager.getInstance().getAllChildNodeId(model.getiId(), iids, baseConn);
                            ReservePlanManager.getInstance().updateAllChilds(model.getiDbType(), iids, baseConn);
                        }
                        
                    }
                }
            }
        } catch (RepositoryException e)
        {
            isSuccess = false;
        } catch (Exception e)
        {
            isSuccess = false;
        }
        res.put(Constants.STR_SUCCESS, isSuccess);
        res.put(Constants.STR_EXESQLS, exeSqls);
        res.put(Constants.STR_ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    /**
     * 
     * @Title: getReservePlanSonId   
     * @Description: 通过父id查询子id集合
     * @param iid
     * @param sysType      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:45:24
     */
    @Override
    public List<Long> getReservePlanSonId ( long iid, int sysType )
    {
        return ReservePlanManager.getInstance().getReservePlanSonId(iid, sysType);
    }

    /**
     * 
     * @Title: deleteReservePlan   
     * @Description: 删除预案
     * @param request
     * @param ids
     * @return      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:44:31
     */
    @Override
    public Map<String, Object> deleteReservePlan ( String ids ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> res = new HashMap<String, Object>();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(Constants.STR_BASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(Constants.STR_DBCONNS);
            Map<String, Object> orgSql = organizeReservePlanSql(ids, baseConn);
            if ((Boolean) orgSql.get(Constants.STR_SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(Constants.STR_EXESQLS),
                    (List<String>) orgSql.get(Constants.STR_ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, true);
                    res.put(Constants.STR_MESSAGE, "数据删除成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, false);
                    res.put(Constants.STR_MESSAGE, "执行信息删除sql时出现错误！ ");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, method, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, method, log);
                }
                res.put(Constants.STR_SUCCESS, false);
                res.put(Constants.STR_MESSAGE, " 组织信息sql时出现错误！");
                return res;
            }
        } else
        {
            res.put(Constants.STR_SUCCESS, false);
            res.put(Constants.STR_MESSAGE, " 没有基线源, 无法删除！");
            return res;
        }
    }

    /**
     * 
     * @Title: organizeReservePlanSql   
     * @Description: 组织删除预案sql
     * @param ids
     * @param baseConn
     * @param dbConns
     * @return      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:55:45
     */
    private Map<String, Object> organizeReservePlanSql ( String ids, Connection baseConn )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        ReservePlanManager manage = ReservePlanManager.getInstance();
        String deleteSqlTemplate = " delete from IEAI_SCRIPT_RESERVE_PLAN where IID=";
        String insertSqlTemplate = " INSERT INTO IEAI_SCRIPT_RESERVE_PLAN (iid,iname,icreateuser,icreatetime,imodifyuser,imodifytime,iparentid,iserviceid,idbtype,iorder) VALUES({iId},'{iName}','{iCreateUser}','{iCreateTime}',{iModifyUser},{iModifyTime},{iParentId},{iServiceId},'{iDbType}',{iOrder}) ";
        try
        {
            String[] deleteId = ids.split(",");
            for (int i = 0; i < deleteId.length; i++)
            {
                exeSqls.add(deleteSqlTemplate + deleteId[i]);
                ReservePlanModel model = manage.getReservePlanOne(Long.valueOf(deleteId[i]), baseConn);
                if (null != model)
                {
                    BeanFormatter<ReservePlanModel> rbf = new BeanFormatter<ReservePlanModel>(insertSqlTemplate);
                    String s = rbf.format(model);
                    rollbackSqls.add(s);
                }
            }
        } catch (RepositoryException e)
        {
            isSuccess = false;
        } catch (Exception e)
        {
            isSuccess = false;
        }
        res.put(Constants.STR_SUCCESS, isSuccess);
        res.put(Constants.STR_EXESQLS, exeSqls);
        res.put(Constants.STR_ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    /**
     * 
     * @Title: findScriptInfoForReservePlan   
     * @Description: 查询未绑定过的脚本列表
     * @param serviceName
     * @param serviceType
     * @param start
     * @param limit
     * @param sysType
     * @return
     * @throws Exception      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:47:52
     */
    @Override
    public Map<String, Object> findScriptInfoForReservePlan ( String serviceName, String serviceType, String dbType,
            int start, int limit, int sysType ) throws Exception
    {
        return ReservePlanManager.getInstance().findScriptInfoForReservePlan(serviceName, serviceType, dbType, start,
            limit,
            sysType);
    }

    /**
     * 
     * @Title: relationReservePlanScript   
     * @Description: 预案绑定脚本
     * @param iServiceId
     * @param reservePlanId
     * @return
     * @throws Exception      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:50:34
     */
    @Override
    public Map<String, Object> relationReservePlanScript ( long iServiceId, long reservePlanId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> res = new HashMap<String, Object>();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(Constants.STR_BASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(Constants.STR_DBCONNS);
            Map<String, Object> orgSql = organizeRelationSql(iServiceId, reservePlanId);
            if ((Boolean) orgSql.get(Constants.STR_SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(Constants.STR_EXESQLS),
                    (List<String>) orgSql.get(Constants.STR_ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, true);
                    res.put(Constants.STR_MESSAGE, "绑定成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, false);
                    res.put(Constants.STR_MESSAGE, "执行信息sql时出现错误！ ");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, method, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, method, log);
                }
                res.put(Constants.STR_SUCCESS, false);
                res.put(Constants.STR_MESSAGE, "组织信息sql时出现错误！ ");
                return res;
            }
        } else
        {
            res.put(Constants.STR_SUCCESS, false);
            res.put(Constants.STR_MESSAGE, "没有基线源, 无法绑定！ ");
            return res;
        }
    }

    /**
     * 
     * @Title: organizeRelationSql   
     * @Description: 组织绑定脚本sql   
     * @param iServiceId
     * @param reservePlanId
     * @return      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:58:05
     */
    private Map<String, Object> organizeRelationSql ( long iServiceId, long reservePlanId )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        exeSqls.add("update IEAI_SCRIPT_RESERVE_PLAN set iServiceId=" + iServiceId + " where iid=" + reservePlanId);
        rollbackSqls.add("update IEAI_SCRIPT_RESERVE_PLAN set iServiceId=0 where iid=" + reservePlanId);
        res.put(Constants.STR_SUCCESS, isSuccess);
        res.put(Constants.STR_EXESQLS, exeSqls);
        res.put(Constants.STR_ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    /**
     * 
     * @Title: findScriptInfoById   
     * @Description: 查询预案绑定的脚本信息
     * @param serviceId
     * @param sysType
     * @return
     * @throws Exception      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:51:39
     */
    @Override
    public Map<String, Object> findScriptInfoById ( String serviceId, int sysType ) throws Exception
    {
        return ReservePlanManager.getInstance().findScriptInfoById(serviceId, sysType);
    }

    /**
     * 
     * @Title: deleteRelationReservePlanScript   
     * @Description: 解除预案与脚本绑定   
     * @param iServiceId
     * @param reservePlanId
     * @return
     * @throws Exception      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:52:30
     */
    @Override
    public Map<String, Object> deleteRelationReservePlanScript ( long iServiceId, long reservePlanId ) throws Exception
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> res = new HashMap<String, Object>();
        Map<String, Object> connInfo = Tools.getConnectionInfo();
        Connection baseConn = (Connection) connInfo.get(Constants.STR_BASECONN);
        if (null != baseConn)
        {
            List<Connection> dbConns = (List<Connection>) connInfo.get(Constants.STR_DBCONNS);
            Map<String, Object> orgSql = organizedeleteRelationSql(iServiceId, reservePlanId);
            if ((Boolean) orgSql.get(Constants.STR_SUCCESS))
            {
                if (DBUtil.hitDatabase(dbConns, (List<String>) orgSql.get(Constants.STR_EXESQLS),
                    (List<String>) orgSql.get(Constants.STR_ROLLBACKSQLS)))
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, true);
                    res.put(Constants.STR_MESSAGE, "解除绑定成功！");
                    return res;
                } else
                {
                    DBResource.closeConnection(baseConn, method, log);
                    res.put(Constants.STR_SUCCESS, false);
                    res.put(Constants.STR_MESSAGE, "执行信息sql时出现错误！");
                    return res;
                }
            } else
            {
                DBResource.closeConnection(baseConn, method, log);
                for (Connection conn : dbConns)
                {
                    DBResource.closeConnection(conn, method, log);
                }
                res.put(Constants.STR_SUCCESS, false);
                res.put(Constants.STR_MESSAGE, "组织信息sql时出现错误！");
                return res;
            }
        } else
        {
            res.put(Constants.STR_SUCCESS, false);
            res.put(Constants.STR_MESSAGE, "没有基线源, 无法解除绑定！");
            return res;
        }
    }

    /**
     * 
     * @Title: organizedeleteRelationSql   
     * @Description: 组织解除脚本绑定sql
     * @param iServiceId
     * @param reservePlanId
     * @return      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午3:58:28
     */
    private Map<String, Object> organizedeleteRelationSql ( long iServiceId, long reservePlanId )
    {
        Map<String, Object> res = new HashMap<String, Object>();
        boolean isSuccess = true;
        List<String> exeSqls = new ArrayList<String>();
        List<String> rollbackSqls = new ArrayList<String>();
        exeSqls.add("update IEAI_SCRIPT_RESERVE_PLAN set iServiceId=0 where iid=" + reservePlanId);
        rollbackSqls
                .add("update IEAI_SCRIPT_RESERVE_PLAN set iServiceId=" + iServiceId + " where iid=" + reservePlanId);
        res.put(Constants.STR_SUCCESS, isSuccess);
        res.put(Constants.STR_EXESQLS, exeSqls);
        res.put(Constants.STR_ROLLBACKSQLS, rollbackSqls);
        return res;
    }

    /**
     * 
     * @Title: findNamesByIds   
     * @Description: 通过id集合查询预案名称
     * @param ids
     * @param sysType
     * @return      
     * @author: baofeng_shi 
     * @date:   2020年2月18日 下午1:56:09
     */
    @Override
    public String findNamesByIds ( Long[] ids, int sysType )
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        StringBuilder names = new StringBuilder("删除预案 ");
        Connection conn = null;
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(sysType);
            for (long iId : ids)
            {
                ReservePlanModel model = ReservePlanManager.getInstance().getReservePlanOne(iId, conn);
                names.append("\"").append(model.getiName()).append("\" ");
            }
        } catch (Exception e)
        {
            log.error(e);
        } finally
        {
            DBResource.closeConnection(conn, method, log);
        }
        return names.toString();
    }


}
