package com.ideal.ieai.server.repository.hd.ic.hcstrategy.client.util;

import java.net.InetAddress;

public class InetAddressUtils
{
    public static String getLocalAddress()
    {
        try
        {
            InetAddress address = InetAddress.getLocalHost();
            return address.getHostAddress();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return "";
    }
}
