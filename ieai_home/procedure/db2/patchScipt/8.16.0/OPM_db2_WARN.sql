BEGIN
	DECLARE LS_SQL VARCHAR(2000);
	DECLARE  LI_EXISTS  NUMERIC(2);



	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=1;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (1,''执行超时'',''timetasktimeout'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=2;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (2,''定时任务执行Agent无法连接'',''timetaskagentnotconnect'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=44;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (44,''定时任务任务执行异常'',''timetaskexecexception'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=3;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (3,''脚本服务化执行Agent无法连接'',''scriptserviceagentnotconnect'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=4;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (4,''巡检Agent无法连接'',''hcagentnotconnect'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=5;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (5,''作业调度系统异常'',''IEAI_SYSTEMEXCEPTION'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=6;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (6,''作业调度业务异常'',''IEAI_BUSINESSEXCEPTION'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=7;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (7,''应用变更代理连接异常'',''SUS_AGENTREFUSE'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=8;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (8,''应用变更PubAgent连接异常'',''SUS_PUBAGENTREFUSE'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=9;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (9,''应用变更业务系统异常'',''SUS_BUSINESSEXCEPTIONS'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=10;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (10,''Agent连接异常'',''agentnotconnect'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_TYPE';
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_TYPE'',11)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =11 WHERE ICLASSNAME=''IEAI_BUSI_TYPE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=11;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (11,''巡检结果出现1级告警'',''ALARM-1'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=12;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (12,''巡检结果出现2级告警'',''ALARM-2'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=13;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (13,''巡检结果出现3级告警'',''ALARM-3'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=14;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (14,''巡检结果出现4级告警'',''ALARM-4'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=15;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (15,''应急操作执行异常'',''epexecerror'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=16;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (16,''灾备切换步骤异常'',''eswitch_BUSINESSEXCEPTIONS'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=17;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (17,''灾备切换Agent连不上'',''eswitch_AGENTREFUSE'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=18;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (18,''server宕机'',''serverexceptiondown'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=19;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (19,''数据库异常'',''DBConnection'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=20;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (20,''巡检结果出现5级告警'',''ALARM-5'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_TYPE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_TYPE'',21)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =21 WHERE ICLASSNAME=''IEAI_BUSI_TYPE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=21;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (21,''Agent连接恢复正常'',''agentreconnect'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	
	SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=22 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=21;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (21,''Agent连接恢复正常'',''agentreconnect'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	
	SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=22 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=22;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (22,''Agent连接超时'',''agentconnectouttime'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=23 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=23;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (23,''作业调度到时未结束预警'',''IEAI_ONTIMENOTENDEXCEPTION'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=24 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=24;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (24,''作业调度超时预警'',''IEAI_RUNTIMEEXCEEDSEXCEPTION'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=25 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=25;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (25,''作业调度到时未发起预警恢复'',''IEAI_ONTIMENOTSTARTRECOVER'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=26 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=26;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (26,''作业调度到时未结束预警恢复'',''IEAI_ONTIMENOTENDRECOVER'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=27 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=27;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (27,''作业调度超时预警恢复'',''IEAI_RUNTIMEEXCEEDSRECOVER'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=28 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=28;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (28,''作业业务异常恢复'',''IEAI_BUSINESSRECOVER'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=29 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=29;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (29,''作业系统异常恢复'',''IEAI_SYSTEMRECOVER'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=30 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=30;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (30,''作业调度到时未发起预警'',''IEAI_ONTIMENOTSTARTEXCEPTION'','''',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=31 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=31;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (31,''巡检1级告警恢复'',''HCWARNRESUME-1'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =32 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		 SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=32;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (32,''巡检2级告警恢复'',''HCWARNRESUME-2'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =33 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=33;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (33,''巡检3级告警恢复'',''HCWARNRESUME-3'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =34 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=34;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (34,''巡检4级告警恢复'',''HCWARNRESUME-4'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =35 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=35;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (35,''巡检5级告警恢复'',''HCWARNRESUME-5'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =36 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

			SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=36;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (36,''1级告警超时未处理'',''HCWARNRTIMEOUT-1'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =37 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		  SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=37;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (37,''2级告警超时未处理'',''HCWARNRTIMEOUT-2'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =38 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=38;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (38,''3级告警超时未处理'',''HCWARNRTIMEOUT-3'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =39 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		  SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=39;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (39,''4级告警超时未处理'',''HCWARNRTIMEOUT-4'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =40 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=40;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (40,''5级告警超时未处理'',''HCWARNRTIMEOUT-5'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =41 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

	 -- 新增第三方告警类型开始
	 
	 	 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=41;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (41,''3级第三方告警'',''THIRDPARTY-3'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =42 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=42;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (42,''4级第三方告警'',''THIRDPARTY-4'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =43 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_TYPE WHERE ITYPEID=43;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_BUSI_TYPE(ITYPEID,ITYPENAME,ITYPECODE,IREMARK,IDELETEFLAG) VALUES (43,''5级第三方告警'',''THIRDPARTY-5'','''',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =44 WHERE ICLASSNAME=''IEAI_BUSI_TYPE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
	 
	
	
	-- 新增第三方告警类型结束		






		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (1,''timetask'',''timetasktimeout'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (2,''timetask'',''timetaskagentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=44;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (44,''timetask'',''timetaskexecexception'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (3,''scriptservice'',''scriptserviceagentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (4,''hc'',''hcagentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (5,''IEAI'',''IEAI_SYSTEMEXCEPTION'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (6,''IEAI'',''IEAI_BUSINESSEXCEPTION'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (7,''SUS'',''SUS_AGENTREFUSE'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (8,''SUS'',''SUS_PUBAGENTREFUSE'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=9;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (9,''SUS'',''SUS_BUSINESSEXCEPTIONS'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=10;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (10,''platform'',''agentnotconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_SCENE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_SCENE'',11)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =11 WHERE ICLASSNAME=''IEAI_WARN_SCENE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=11;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (11,''hc'',''ALARM-1'',''1'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=12;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (12,''hc'',''ALARM-2'',''2'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=13;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (13,''hc'',''ALARM-3'',''3'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=14;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (14,''hc'',''ALARM-4'',''4'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=15;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (15,''ep'',''epexecerror'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=16;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (16,''eswitch'',''eswitch_BUSINESSEXCEPTIONS'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=17;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (17,''eswitch'',''eswitch_AGENTREFUSE'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=18;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (18,''platform'',''serverexceptiondown'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=19;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (19,''platform'',''DBConnection'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=20;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (20,''hc'',''ALARM-5'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_SCENE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_SCENE'',21)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =21 WHERE ICLASSNAME=''IEAI_WARN_SCENE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=21;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (21,''platform'',''agentreconnect'',''5'',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	
	SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=22 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=22;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (22,''platform'',''agentconnectouttime'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=23 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=23;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (23,''IEAI'',''IEAI_ONTIMENOTENDEXCEPTION'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=24 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=24;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (24,''IEAI'',''IEAI_RUNTIMEEXCEEDSEXCEPTION'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=25 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=25;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (25,''IEAI'',''IEAI_ONTIMENOTSTARTRECOVER'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=26 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=26;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (26,''IEAI'',''IEAI_ONTIMENOTENDRECOVER'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=27 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=27;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (27,''IEAI'',''IEAI_RUNTIMEEXCEEDSRECOVER'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=28 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=28;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (28,''IEAI'',''IEAI_BUSINESSRECOVER'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=29 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;


	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=29;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (29,''IEAI'',''IEAI_SYSTEMRECOVER'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=30 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=30;
	IF	LI_EXISTS = 0 THEN
		SET	LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (30,''IEAI'',''IEAI_ONTIMENOTSTARTEXCEPTION'',''5'',0)';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;

		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE=31 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		PREPARE	SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END	IF;

	 SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=31;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (31,''hc'',''HCWARNRESUME-1'',''1'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =32 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=32;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (32,''hc'',''HCWARNRESUME-2'',''2'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =33 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=33;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (33,''hc'',''HCWARNRESUME-3'',''3'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =34 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=34;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (34,''hc'',''HCWARNRESUME-4'',''4'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =35 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=35;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (35,''hc'',''HCWARNRESUME-5'',''5'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =36 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

		  SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=36;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (36,''hc'',''HCWARNRTIMEOUT-1'',''1'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =37 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(SIGN(COUNT(*))) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=37;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (37,''hc'',''HCWARNRTIMEOUT-2'',''2'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =38 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=38;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (38,''hc'',''HCWARNRTIMEOUT-3'',''3'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =39 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=39;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (39,''hc'',''HCWARNRTIMEOUT-4'',''4'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =40 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=40;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (40,''hc'',''HCWARNRTIMEOUT-5'',''5'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =41 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;

	 -- 新增第三方告警类型开始
	 
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=41;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (41,''hc'',''THIRDPARTY-3'',''3'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =42 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=42;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (42,''hc'',''THIRDPARTY-4'',''4'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =43 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
		 SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_SCENE WHERE ISCENEID=43;
		 IF  LI_EXISTS = 0 THEN
		 SET LS_SQL = 'INSERT INTO IEAI_WARN_SCENE(ISCENEID,IMODULECODE,ITYPECODE,ILEVELCODE,ISTATUS) VALUES (43,''hc'',''THIRDPARTY-5'',''5'',0)';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =44 WHERE ICLASSNAME=''IEAI_WARN_SCENE'' ';
		 PREPARE SQLA FROM LS_SQL;
		 EXECUTE SQLA;
		 END  IF;
	 
	
	 
	 -- 新增第三方告警类型开始
	 


		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(1,''模块名称'',''imodulename'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(2,''异常IP'',''iip'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(3,''告警时间'',''ihappentimeStr'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(4,''报警模块编码'',''imodulecode'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(5,''报警类型编码'',''itypecode'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(6,''用户责任人'',''userList'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(7,''报警信息'',''iwarnmsg'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=18;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(18,''业务系统名称'',''isystemname'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=84;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(84,''告警级别编码'',''ilevelcode'',0,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(8,''任务组名称'',''itaskGroupName'',1,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=9;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(9,''任务组名称'',''itaskGroupName'',2,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=246;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(246,''任务组名称'',''itaskGroupName'',44,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=10;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(10,''任务名称'',''itaskName'',1,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=11;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(11,''任务名称'',''itaskName'',2,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=247;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(247,''任务名称'',''itaskName'',44,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=248;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(248,''系统简称'',''sysabbv'',1,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=249;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(249,''系统简称'',''sysabbv'',2,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=250;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(250,''系统简称'',''sysabbv'',44,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=251;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(251,''告警类型'',''alertType'',1,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=252;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(252,''告警类型'',''alertType'',2,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=253;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(253,''告警类型'',''alertType'',44,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=12;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(12,''工程名称'',''iprojectName'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=13;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(13,''工作流名称'',''iworkFlowName'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=14;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(14,''报错步骤名称'',''iErrorStepName'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=15;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(15,''工程名称'',''iprojectName'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=16;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(16,''工作流名称'',''iworkFlowName'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=17;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(17,''报错步骤名称'',''iErrorStepName'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=27;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(27,''方案名称'',''iswitchName'',7,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=28;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(28,''方案名称'',''iswitchName'',8,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=29;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(29,''方案名称'',''iswitchName'',9,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=30;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(30,''子步骤名称'',''isonStepName'',7,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=31;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(31,''子步骤名称'',''isonStepName'',8,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=32;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(32,''子步骤名称'',''isonStepName'',9,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=33;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(33,''服务名称'',''iserviceName'',3,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=34;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(34,''任务名称'',''itaskName'',3,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=39;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(39,''子步骤名称'',''isonStepName'',16,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=40;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(40,''子步骤名称'',''isonStepName'',17,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=42;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(42,''应急预案名称'',''iswitchName'',15,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=43;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(43,''应急预案子步骤'',''isonStepName'',15,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',44)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =44 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=44;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(44,''巡检项名称'',''icheckItem'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=45;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(45,''巡检项名称'',''icheckItem'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=46;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(46,''巡检项名称'',''icheckItem'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=47;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(47,''巡检项名称'',''icheckItem'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=48;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(48,''巡检项名称'',''icheckItem'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=49;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(49,''巡检值'',''icheckValue'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=50;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(50,''巡检值'',''icheckValue'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=51;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(51,''巡检值'',''icheckValue'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=52;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(52,''巡检值'',''icheckValue'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=53;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(53,''巡检值'',''icheckValue'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=54;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(54,''基线值'',''iwarnValue'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=55;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(55,''基线值'',''iwarnValue'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=56;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(56,''基线值'',''iwarnValue'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=57;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(57,''基线值'',''iwarnValue'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=58;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(58,''基线值'',''iwarnValue'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=59;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(59,''巡检点名称'',''icheckObject'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=60;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(60,''巡检点名称'',''icheckObject'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=61;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(61,''巡检点名称'',''icheckObject'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=62;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(62,''巡检点名称'',''icheckObject'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=63;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(63,''巡检点名称'',''icheckObject'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=64;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(64,''设备主机名'',''ihostname'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=65;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(65,''设备主机名'',''ihostname'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=66;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(66,''设备主机名'',''ihostname'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=67;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(67,''设备主机名'',''ihostname'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=68;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(68,''设备主机名'',''ihostname'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=69;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(69,''比较规则'',''icomparerule'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=70;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(70,''比较规则'',''icomparerule'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=71;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(71,''比较规则'',''icomparerule'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=72;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(72,''比较规则'',''icomparerule'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=73;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(73,''比较规则'',''icomparerule'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=74;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(74,''检查点执行配置名称'',''cmdname'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=75;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(75,''检查点执行配置名称'',''cmdname'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=76;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(76,''检查点执行配置名称'',''cmdname'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=77;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(77,''检查点执行配置名称'',''cmdname'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=78;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(78,''检查点执行配置名称'',''cmdname'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=79;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(79,''辅助信息'',''amessage'',11,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=80;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(80,''辅助信息'',''amessage'',12,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=81;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(81,''辅助信息'',''amessage'',13,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=82;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(82,''辅助信息'',''amessage'',14,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=83;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(83,''辅助信息'',''amessage'',20,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;
	COMMIT;
	
	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',100)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =100 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=85;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(85,''系统编码'',''isystemcode'',30,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=86;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(86,''系统编码'',''isystemcode'',23,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=87;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(87,''系统编码'',''isystemcode'',24,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=88;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(88,''系统编码'',''isystemcode'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=89;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(89,''系统编码'',''isystemcode'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=90;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(90,''脚本输出内容'',''ishellLineContent'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=91;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(91,''系统编码'',''isystemcode'',25,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=92;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(92,''系统编码'',''isystemcode'',26,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=93;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(93,''系统编码'',''isystemcode'',27,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=94;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(94,''系统编码'',''isystemcode'',28,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=95;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(95,''系统编码'',''isystemcode'',29,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=96;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(96,''脚本输出内容'',''ishellLineContent'',28,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=97;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(97,''异常AgentIP'',''iagentip'',5,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=98;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(98,''异常AgentIP'',''iagentip'',6,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=99;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(99,''异常AgentIP'',''iagentip'',28,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=100;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(100,''异常AgentIP'',''iagentip'',29,0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',101)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =101 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=101;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(101,''巡检项名称'',''icheckItem'',31,0)';
			PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=102;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(102,''巡检项名称'',''icheckItem'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=103;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(103,''巡检项名称'',''icheckItem'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=104;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(104,''巡检项名称'',''icheckItem'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=105;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(105,''巡检项名称'',''icheckItem'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=106;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(106,''巡检值'',''icheckValue'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=107;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(107,''巡检值'',''icheckValue'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=108;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(108,''巡检值'',''icheckValue'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=109;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(109,''巡检值'',''icheckValue'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=110;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(110,''巡检值'',''icheckValue'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=111;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(111,''基线值'',''iwarnValue'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=112;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(112,''基线值'',''iwarnValue'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=113;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(113,''基线值'',''iwarnValue'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=114;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(114,''基线值'',''iwarnValue'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=115;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(115,''基线值'',''iwarnValue'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=116;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(116,''巡检点名称'',''icheckObject'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=117;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(117,''巡检点名称'',''icheckObject'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=118;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(118,''巡检点名称'',''icheckObject'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=119;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(119,''巡检点名称'',''icheckObject'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=120;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(120,''巡检点名称'',''icheckObject'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=121;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(121,''设备主机名'',''ihostname'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=122;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(122,''设备主机名'',''ihostname'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=123;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(123,''设备主机名'',''ihostname'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=124;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(124,''设备主机名'',''ihostname'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=125;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(125,''设备主机名'',''ihostname'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=126;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(126,''比较规则'',''icomparerule'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=127;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(127,''比较规则'',''icomparerule'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=128;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(128,''比较规则'',''icomparerule'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=129;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(129,''比较规则'',''icomparerule'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=130;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(130,''比较规则'',''icomparerule'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		 
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=131;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(131,''检查点执行配置名称'',''cmdname'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=132;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(132,''检查点执行配置名称'',''cmdname'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=133;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(133,''检查点执行配置名称'',''cmdname'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=134;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(134,''检查点执行配置名称'',''cmdname'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=135;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(135,''检查点执行配置名称'',''cmdname'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=136;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(136,''辅助信息'',''amessage'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=137;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(137,''辅助信息'',''amessage'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=138;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(138,''辅助信息'',''amessage'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=139;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(139,''辅助信息'',''amessage'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=140;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(140,''辅助信息'',''amessage'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		 
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=141;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(141,''巡检项名称'',''icheckItem'',36,0)';
			PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=142;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(142,''巡检项名称'',''icheckItem'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=143;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(143,''巡检项名称'',''icheckItem'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=144;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(144,''巡检项名称'',''icheckItem'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=145;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(145,''巡检项名称'',''icheckItem'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=146;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(146,''巡检值'',''icheckValue'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=107;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(147,''巡检值'',''icheckValue'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=148;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(148,''巡检值'',''icheckValue'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=149;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(149,''巡检值'',''icheckValue'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=150;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(150,''巡检值'',''icheckValue'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=151;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(151,''基线值'',''iwarnValue'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=152;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(152,''基线值'',''iwarnValue'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=153;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(153,''基线值'',''iwarnValue'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=154;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(154,''基线值'',''iwarnValue'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=155;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(155,''基线值'',''iwarnValue'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=116;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(156,''巡检点名称'',''icheckObject'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=157;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(157,''巡检点名称'',''icheckObject'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=158;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(158,''巡检点名称'',''icheckObject'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=159;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(159,''巡检点名称'',''icheckObject'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=160;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(160,''巡检点名称'',''icheckObject'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=161;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(161,''设备主机名'',''ihostname'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=162;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(162,''设备主机名'',''ihostname'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=163;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(163,''设备主机名'',''ihostname'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=164;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(164,''设备主机名'',''ihostname'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=165;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(165,''设备主机名'',''ihostname'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=166;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(166,''比较规则'',''icomparerule'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=167;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(167,''比较规则'',''icomparerule'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=168;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(168,''比较规则'',''icomparerule'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=169;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(169,''比较规则'',''icomparerule'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=170;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(170,''比较规则'',''icomparerule'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		 
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=171;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(171,''检查点执行配置名称'',''cmdname'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=172;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(172,''检查点执行配置名称'',''cmdname'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=173;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(173,''检查点执行配置名称'',''cmdname'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=174;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(174,''检查点执行配置名称'',''cmdname'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=175;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(175,''检查点执行配置名称'',''cmdname'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=176;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(176,''辅助信息'',''amessage'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=177;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(177,''辅助信息'',''amessage'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=178;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(178,''辅助信息'',''amessage'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=179;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(179,''辅助信息'',''amessage'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=180;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(180,''辅助信息'',''amessage'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',181)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		ELSE SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =181 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS'' ';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=181;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(181,''系统简称'',''sysabbv'',11,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=182;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(182,''系统简称'',''sysabbv'',12,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=183;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(183,''系统简称'',''sysabbv'',13,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=184;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(184,''系统简称'',''sysabbv'',14,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=185;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(185,''系统简称'',''sysabbv'',20,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=186;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(186,''扩展字段1'',''instanceobj'',11,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=187;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(187,''扩展字段1'',''instanceobj'',12,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=188;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(188,''扩展字段1'',''instanceobj'',13,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=189;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(189,''扩展字段1'',''instanceobj'',14,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=190;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(190,''扩展字段1'',''instanceobj'',20,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=191;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(191,''扩展字段2'',''alertType'',11,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=192;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(192,''扩展字段2'',''alertType'',12,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=193;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(193,''扩展字段2'',''alertType'',13,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=194;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(194,''扩展字段2'',''alertType'',14,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=195;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(195,''扩展字段2'',''alertType'',20,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
			
	-- 新增第三方告警类型开始
	
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=196;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(196,''平台'',''thirdpartplat'',41,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=197;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(197,''告警主机'',''ihostname'',41,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=198;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(198,''告警名称'',''alarmname'',41,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=199;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(199,''平台'',''thirdpartplat'',42,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=200;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(200,''告警主机'',''ihostname'',42,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=201;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(201,''告警名称'',''alarmname'',42,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=202;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(202,''平台'',''thirdpartplat'',43,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=203;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(203,''告警主机'',''ihostname'',43,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=204;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(204,''告警名称'',''alarmname'',43,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;

		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=205;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(205,''工作流ID'',''iflowid'',6,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=206;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(206,''工作流ID'',''iflowid'',5,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=207;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(207,''工作流ID'',''iflowid'',23,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=208;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(208,''工作流ID'',''iflowid'',26,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=209;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(209,''工作流ID'',''iflowid'',25,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=210;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(210,''工作流ID'',''iflowid'',24,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=211;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(211,''工作流ID'',''iflowid'',30,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=212;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(212,''工作流ID'',''iflowid'',29,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=213;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(213,''工作流ID'',''iflowid'',28,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=214;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(214,''工作流ID'',''iflowid'',27,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;


		-- 新增第三方告警类型结束

		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=215;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(215,''检查点ID'',''pointid'',11,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=216;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(216,''检查点ID'',''pointid'',12,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=217;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(217,''检查点ID'',''pointid'',13,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=218;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(218,''检查点ID'',''pointid'',14,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=219;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(219,''检查点ID'',''pointid'',20,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=220;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(220,''检查点ID'',''pointid'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=221;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(221,''检查点ID'',''pointid'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=222;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(222,''检查点ID'',''pointid'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=223;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(223,''检查点ID'',''pointid'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=224;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(224,''检查点ID'',''pointid'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=225;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(225,''检查点ID'',''pointid'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=226;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(226,''检查点ID'',''pointid'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=227;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(227,''检查点ID'',''pointid'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=228;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(228,''检查点ID'',''pointid'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=229;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(229,''检查点ID'',''pointid'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=230;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(230,''告警ID'',''hcWarnid'',11,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=231;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(231,''告警ID'',''hcWarnid'',12,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=232;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(232,''告警ID'',''hcWarnid'',13,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=233;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(233,''告警ID'',''hcWarnid'',14,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=234;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(234,''告警ID'',''hcWarnid'',20,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=235;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(235,''告警ID'',''hcWarnid'',31,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=236;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(236,''告警ID'',''hcWarnid'',32,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=237;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(237,''告警ID'',''hcWarnid'',33,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=238;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(238,''告警ID'',''hcWarnid'',34,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=239;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(239,''告警ID'',''hcWarnid'',35,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=240;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(240,''告警ID'',''hcWarnid'',36,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=241;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(241,''告警ID'',''hcWarnid'',37,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=242;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(242,''告警ID'',''hcWarnid'',38,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=243;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(243,''告警ID'',''hcWarnid'',39,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=244;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(244,''告警ID'',''hcWarnid'',40,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		SELECT  SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_WARN_COLUMNS WHERE IID=245;
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_WARN_COLUMNS(IID,ICOLUMNKEY,ICOLUMNVALUE,ISCENEID,ITYPE)VALUES(245,''告警ID'',''hcWarnid'',15,0)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;
		
		
		
		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_WARN_COLUMNS';
		IF  LI_EXISTS = 0 THEN
		SET LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_WARN_COLUMNS'',254)';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		ELSE
			SET LS_SQL = 'UPDATE IEAI_ID SET IVALUE =254 WHERE ICLASSNAME=''IEAI_WARN_COLUMNS'' ';
		PREPARE SQLA FROM LS_SQL;
		EXECUTE SQLA;
		END  IF;




		SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (1,''定时任务'',''timetask'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (2,''脚本服务化'',''scriptservice'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (3,''巡检'',''hc'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (4,''作业调度'',''IEAI'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (5,''应用变更'',''SUS'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (6,''平台管理'',''platform'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_MODULE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_MODULE'',6)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =6 WHERE ICLASSNAME=''IEAI_BUSI_MODULE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=7;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (7,''应急操作'',''ep'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_MODULE WHERE IMODULEID=8;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_MODULE(IMODULEID,IMODULENAME,IMODULECODE,IREMARK,IDELETEFLAG) VALUES (8,''灾备切换'',''eswitch'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_MODULE';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_MODULE'',9)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =9 WHERE ICLASSNAME=''IEAI_BUSI_MODULE''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;



	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=1;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (1,''五级'',''5'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_LEVEL';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_LEVEL'',2)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =2 WHERE ICLASSNAME=''IEAI_BUSI_LEVEL''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=2;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (2,''一级'',''1'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=3;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (3,''二级'',''2'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=4;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (4,''三级'',''3'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=5;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (5,''四级'',''4'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_LEVEL';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_LEVEL'',6)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =6 WHERE ICLASSNAME=''IEAI_BUSI_LEVEL''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_BUSI_LEVEL WHERE ILEVELID=6;
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_BUSI_LEVEL(ILEVELID,ILEVELNAME,ILEVELCODE,IREMARK,IDELETEFLAG) VALUES (6,''零级'',''0'','''',0)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;

	SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM IEAI_ID WHERE ICLASSNAME='IEAI_BUSI_LEVEL';
	IF	LI_EXISTS = 0 THEN
    SET	LS_SQL = 'INSERT INTO IEAI_ID(ICLASSNAME,IVALUE) VALUES (''IEAI_BUSI_LEVEL'',7)';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	ELSE
		SET	LS_SQL = 'UPDATE IEAI_ID SET IVALUE =7 WHERE ICLASSNAME=''IEAI_BUSI_LEVEL''';
    PREPARE	SQLA FROM LS_SQL;
    EXECUTE SQLA;
	END	IF;


COMMIT;
END
$

BEGIN
		DECLARE LS_SQL VARCHAR(4000);
		DECLARE LI_EXISTS  NUMERIC(2);

		SELECT SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE COLNAME='ISTATUS' AND TABNAME='IEAI_USER';
			IF LI_EXISTS = 0 THEN
				SET LS_SQL = 'ALTER TABLE ieai_user add  ISTATUS VARCHAR(255) DEFAULT 0';
		    	PREPARE	SQLA FROM LS_SQL;
				EXECUTE SQLA;
		END IF;

	END
	$