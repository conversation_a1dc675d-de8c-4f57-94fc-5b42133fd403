package com.ideal.ieai.server.jobscheduling.repository.configinfoview;

public class ConfigInfoViewBean
{
    private int dbType; // 系统类型 1-灾备 2-部署 3-巡检 4-作业调度
    private int prjType;   // 工程类型： 1-作业调度  2-巡检  8-一致性比对
    private String userId;  // 当前用户主键
    private String userName;

    private String iparamName;  // 参数名称
    private String iparamType;  // 参数类型
    private String iparamValue; // 参数值
    private String iparamDesc;  // 参数说明

    private String readOnly; // 是否只读

    private String ievaName;  // 环境变量名称
    private String ievaType;  // 环境变量类型
    private String ievaValue; // 环境变量值

    private String iparamJsonData;// 参数的json数据串
    private String ievaJsonData;  // 环境变量json数据串

    private String iprjName; // 工程名称
    private String iprjId;   // 工程ID
    private String iupperId; // 更新ID

    private String iworkFlowName; // 工作流名称
    private String iworkFlowId;   // 工作流ID
    private String instName;      // 工作流实例名称
    
    private String iactivityName;  // 活动名称
    private String iactivityId;  // 活动ID

    private String workFlowDesc;// 工作流说明

    private String flowAnnotation; // 工作流注释

    public int getPrjType ()
    {
        return prjType;
    }

    public void setPrjType ( int prjType )
    {
        this.prjType = prjType;
    }

    public String getReadOnly ()
    {
        return readOnly;
    }

    public void setReadOnly ( String readOnly )
    {
        this.readOnly = readOnly;
    }

    public String getUserName ()
    {
        return userName;
    }

    public void setUserName ( String userName )
    {
        this.userName = userName;
    }

    public int getDbType ()
    {
        return dbType;
    }

    public void setDbType ( int dbType )
    {
        this.dbType = dbType;
    }

    public String getUserId ()
    {
        return userId;
    }

    public void setUserId ( String userId )
    {
        this.userId = userId;
    }

    public String getIparamName ()
    {
        return iparamName;
    }

    public void setIparamName ( String iparamName )
    {
        this.iparamName = iparamName;
    }

    public String getIparamType ()
    {
        return iparamType;
    }

    public void setIparamType ( String iparamType )
    {
        this.iparamType = iparamType;
    }

    public String getIparamValue ()
    {
        return iparamValue;
    }

    public void setIparamValue ( String iparamValue )
    {
        this.iparamValue = iparamValue;
    }

    public String getIparamDesc ()
    {
        return iparamDesc;
    }

    public void setIparamDesc ( String iparamDesc )
    {
        this.iparamDesc = iparamDesc;
    }

    public String getIevaName ()
    {
        return ievaName;
    }

    public void setIevaName ( String ievaName )
    {
        this.ievaName = ievaName;
    }

    public String getIevaType ()
    {
        return ievaType;
    }

    public void setIevaType ( String ievaType )
    {
        this.ievaType = ievaType;
    }

    public String getIevaValue ()
    {
        return ievaValue;
    }

    public void setIevaValue ( String ievaValue )
    {
        this.ievaValue = ievaValue;
    }

    public String getIparamJsonData ()
    {
        return iparamJsonData;
    }

    public void setIparamJsonData ( String iparamJsonData )
    {
        this.iparamJsonData = iparamJsonData;
    }

    public String getIevaJsonData ()
    {
        return ievaJsonData;
    }

    public void setIevaJsonData ( String ievaJsonData )
    {
        this.ievaJsonData = ievaJsonData;
    }

    public String getIprjName ()
    {
        return iprjName;
    }

    public void setIprjName ( String iprjName )
    {
        this.iprjName = iprjName;
    }

    public String getIprjId ()
    {
        return iprjId;
    }

    public void setIprjId ( String iprjId )
    {
        this.iprjId = iprjId;
    }

    public String getIupperId ()
    {
        return iupperId;
    }

    public void setIupperId ( String iupperId )
    {
        this.iupperId = iupperId;
    }

    public String getIworkFlowName ()
    {
        return iworkFlowName;
    }

    public void setIworkFlowName ( String iworkFlowName )
    {
        this.iworkFlowName = iworkFlowName;
    }

    public String getIworkFlowId ()
    {
        return iworkFlowId;
    }

    public void setIworkFlowId ( String iworkFlowId )
    {
        this.iworkFlowId = iworkFlowId;
    }

    public String getInstName ()
    {
        return instName;
    }

    public void setInstName ( String instName )
    {
        this.instName = instName;
    }

    public String getWorkFlowDesc ()
    {
        return workFlowDesc;
    }

    public void setWorkFlowDesc ( String workFlowDesc )
    {
        this.workFlowDesc = workFlowDesc;
    }

    public String getFlowAnnotation ()
    {
        return flowAnnotation;
    }

    public void setFlowAnnotation ( String flowAnnotation )
    {
        this.flowAnnotation = flowAnnotation;
    }

    public String getIactivityName ()
    {
        return iactivityName;
    }

    public void setIactivityName ( String iactivityName )
    {
        this.iactivityName = iactivityName;
    }

    public String getIactivityId ()
    {
        return iactivityId;
    }

    public void setIactivityId ( String iactivityId )
    {
        this.iactivityId = iactivityId;
    }

}
