	-- 4.7.17 not exists
	-- 4.7.18 not exists		开始
	-- 4.7.19 not exists	
	-- 4.7.20 not exists	
	-- 4.7.21 not exists	
	-- 4.7.22 not exists	
	-- 4.7.23 
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDUR<PERSON> UPPRDE()
	BEGIN			
	    CREATE TABLE IF NOT EXISTS IEAI_FSH_ALARM_TYPE (IID NUMERIC(19) NOT NULL, INAME VARCHAR(255), IKEYWORD VARCHAR(255), IPLATFORM NUMERIC(4), ICREATETIME NUMERIC(19), ICREATEUSER VARCHAR(50),CONSTRAINT PK_IEAI_FSH_ALARM_TYPE PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_SCENE (IID NUMERIC(19) NOT NULL, INAME VARCHAR(255), <PERSON><PERSON><PERSON> NUMERIC(4), ITASK<PERSON> NUMERIC(19), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> NUMERIC(19), ICREATEUSER VARCHAR(50), ITASKNAME VARCHAR(255),CONSTRAINT PK_IEAI_FSH_SCENE PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_TASK (IID NUMERIC(19) NOT NULL, IALARMINFOID NUMERIC(19), ISCHEMEID NUMERIC(19), ICREATETIME NUMERIC(19), ISTARTTIME NUMERIC(19), IENDTIME NUMERIC(19), ISTATE NUMERIC(4), ITASK_EXECRESULT VARCHAR(255), ITASKTYPE NUMERIC(4), IAOMSTASKID NUMERIC(19), IALARMIP VARCHAR(20), ISUBMITUSER VARCHAR(255), IAUDITOR VARCHAR(255), ITIMEING VARCHAR(50), IEXAMINETYPE NUMERIC(4) ,CONSTRAINT PK_IEAI_FSH_TASK PRIMARY KEY(IID));		
		CREATE TABLE IF NOT EXISTS IEAI_FSH_TASK_HISTORY (IID NUMERIC(19) NOT NULL, IALARMINFOID NUMERIC(19), ISCHEMEID NUMERIC(19), ICREATETIME NUMERIC(19), ISTARTTIME NUMERIC(19), IENDTIME NUMERIC(19), ISTATE NUMERIC(4), ITASK_EXECRESULT VARCHAR(255), ITASKTYPE NUMERIC(4), IAOMSTASKID NUMERIC(19), IALARMIP VARCHAR(20), ISUBMITUSER VARCHAR(255), IAUDITOR VARCHAR(255), ITIMEING VARCHAR(50), IEXAMINETYPE NUMERIC(4) ,CONSTRAINT IEAI_FSH_TASK_HISTORY PRIMARY KEY(IID));		
		CREATE TABLE IF NOT EXISTS IEAI_FSH_ALARMINFO (IID NUMERIC(19) NOT NULL, IALARMID VARCHAR(100), IPLATFORM VARCHAR(100), IAPPNAME VARCHAR(255), IHOSTIP VARCHAR(25), IALARMCONTENT VARCHAR(500), IALARMCODE VARCHAR(255), IALARM_TIME NUMERIC(19), ICONTANT VARCHAR(500), IPHONE VARCHAR(500), IALARM_MESSAGE VARCHAR(2000), IIS_TRIGGER NUMERIC(4), ITASK_HANDLE_DETAIL VARCHAR(100), IALARMTYPEID VARCHAR(1000), ICREATETIME NUMERIC(19),CONSTRAINT IEAI_FSH_ALARMINFO PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_ALARMINFO_HISTORY (IID NUMERIC(19) NOT NULL, IALARMID VARCHAR(100), IPLATFORM VARCHAR(100), IAPPNAME VARCHAR(255), IHOSTIP VARCHAR(25), IALARMCONTENT VARCHAR(500), IALARMCODE VARCHAR(255), IALARM_TIME NUMERIC(19), ICONTANT VARCHAR(500), IPHONE VARCHAR(500), IALARM_MESSAGE VARCHAR(2000), IIS_TRIGGER NUMERIC(4), ITASK_HANDLE_DETAIL VARCHAR(100), IALARMTYPEID VARCHAR(1000), ICREATETIME NUMERIC(19),CONSTRAINT IEAI_FSH_ALARMINFO_HISTORY PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_ALARM_COMBINATION (IID NUMERIC(19) NOT NULL, INAME VARCHAR(255), IDES VARCHAR(255), ICREATETIME NUMERIC(19), ICREATEUSER VARCHAR(50),CONSTRAINT IEAI_FSH_ALARM_COMBINATION PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_ALARM_COM_TYPE (IID NUMERIC(19) NOT NULL, ICOMBINATIONID NUMERIC(19), IALARMTYPEID NUMERIC(19),CONSTRAINT IEAI_FSH_ALARM_COM_TYPE PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_SCHEME (IID NUMERIC(19) NOT NULL, INAME VARCHAR(255), ITYPE NUMERIC(4), IALARMCOMBINATIONID NUMERIC(19), ISCENE NUMERIC(19), IEXECMODEL NUMERIC(4), IFEEDBACK_TIME NUMERIC(4), ICOOLING_TIME NUMERIC(4), IOVERTIME_TIME NUMERIC(4), ISTATE NUMERIC(4), ICREATETIME NUMERIC(19), IONLINETIME NUMERIC(19), ICREATEUSER VARCHAR(50), IAPPLYUSER VARCHAR(50), IAUDITORUSER VARCHAR(50),CONSTRAINT IEAI_FSH_SCHEME PRIMARY KEY(IID));
		CREATE TABLE IF NOT EXISTS IEAI_FSH_SCHEME_EQU (IID NUMERIC(19) NOT NULL, ISCHEMEID NUMERIC(19), IEQUID NUMERIC(19), IEQUIP VARCHAR(20),CONSTRAINT IEAI_FSH_SCHEME_EQU PRIMARY KEY(IID));
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_PROJECT' AND COLUMN_NAME = 'ISSELFHEALING') THEN
			ALTER TABLE IEAI_PROJECT ADD ISSELFHEALING NUMERIC(1) DEFAULT 0;
		END IF;
		COMMIT;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_INSTANCE_VERSION' AND COLUMN_NAME = 'ISSELFHEALING') THEN
			ALTER TABLE IEAI_INSTANCE_VERSION ADD ISSELFHEALING INTEGER DEFAULT 0;
		END IF;
		COMMIT;
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_ALARMINFO' AND COLUMN_NAME = 'ISCHEMEID') THEN
			ALTER TABLE IEAI_FSH_ALARMINFO ADD ISCHEMEID NUMERIC(19);
		END IF;
		COMMIT;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME = 'ISCHEMEID') THEN
			ALTER TABLE IEAI_FSH_ALARMINFO_HISTORY ADD ISCHEMEID NUMERIC(19);
		END IF;
		COMMIT;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	-- 4.7.24 
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		CREATE TABLE IF NOT EXISTS IEAI_FSH_SCENE_TSAKCONFIG (IID NUMERIC(19) NOT NULL, ITASKID NUMERIC(19), ICOMBINATIONID NUMERIC(19), IALARMINFO VARCHAR(255), IPARAMNAME  VARCHAR(255), CONSTRAINT PK_IEAI_FSH_SCENE_TSAKCONFIG PRIMARY KEY (IID));
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_TASK' AND COLUMN_NAME = 'IAUDITOR') THEN
			ALTER TABLE IEAI_FSH_TASK MODIFY IAUDITOR VARCHAR(4000) ;
		END IF;
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME = 'IAUDITOR') THEN
			ALTER TABLE IEAI_FSH_TASK_HISTORY MODIFY IAUDITOR VARCHAR(4000) ;
		END IF;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- 4.7.25 
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN	
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_ALARMINFO' AND COLUMN_NAME = 'IALARMTYPEID') THEN
			ALTER TABLE IEAI_FSH_ALARMINFO MODIFY IALARMTYPEID VARCHAR(1000) ;
	    END IF;
		
		IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_ALARMINFO_HISTORY' AND COLUMN_NAME = 'IALARMTYPEID') THEN
			ALTER TABLE IEAI_FSH_ALARMINFO_HISTORY MODIFY IALARMTYPEID VARCHAR(1000) ;
	    END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCENE_TSAKCONFIG' AND COLUMN_NAME = 'ISCENEID') THEN
				ALTER TABLE IEAI_FSH_SCENE_TSAKCONFIG  ADD ISCENEID NUMERIC(19);
		END IF;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCENE_TSAKCONFIG' AND COLUMN_NAME = 'IALARMTYPEID') THEN
				ALTER TABLE IEAI_FSH_SCENE_TSAKCONFIG  ADD IALARMTYPEID NUMERIC(19);
		END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE(); 
	
	-- 4.7.26 not exists
	
	-- v8.18.1自愈sql
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		CREATE TABLE IF NOT EXISTS IEAI_FSH_SCENE_TASKCONFIG (IID NUMERIC(19) NOT NULL, ITASKID NUMERIC(19), ISCENEID NUMERIC(19), IPARAMNAME VARCHAR(255), IPARAMTYPE  VARCHAR(255), IPARAMVALUE VARCHAR(255),IPARAMDESC VARCHAR(255),IPARAMIID NUMERIC(19) , CONSTRAINT PK_IEAI_FSH_SCENE_TASKCONFIG PRIMARY KEY (IID)) ;
		COMMIT;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCHEME' AND COLUMN_NAME = 'SYSTEMCODE') THEN
			ALTER TABLE IEAI_FSH_SCHEME  ADD SYSTEMCODE VARCHAR(20);
		END IF;
		COMMIT;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCHEME' AND COLUMN_NAME = 'SYSSHORT') THEN
			ALTER TABLE IEAI_FSH_SCHEME  ADD SYSSHORT VARCHAR(20);
		END IF;
		COMMIT;
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCHEME' AND COLUMN_NAME = 'ISCENEIDIPMP') THEN
			ALTER TABLE IEAI_FSH_SCHEME  ADD ISCENEIDIPMP VARCHAR(30);
		END IF;
		COMMIT;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCHEME' AND COLUMN_NAME = 'ISCHEMEIP') THEN
			ALTER TABLE IEAI_FSH_SCHEME  ADD ISCHEMEIP VARCHAR(30);
		END IF;
		COMMIT;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_TASK' AND COLUMN_NAME = 'IALARMID') THEN
			ALTER TABLE IEAI_FSH_TASK  ADD IALARMID NUMERIC(19);
		END IF;
		COMMIT;
	
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_ALARM_COMBINATION' AND COLUMN_NAME = 'COMPOSITIONRELATIONSHIPS') THEN
			ALTER TABLE IEAI_FSH_ALARM_COMBINATION  ADD COMPOSITIONRELATIONSHIPS NUMERIC(19);
		END IF;
		COMMIT;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME = 'IALARMID') THEN
			ALTER TABLE IEAI_FSH_TASK_HISTORY  ADD IALARMID NUMERIC(19);
		END IF;
		COMMIT;
	
	END;;
	DELIMITER ;
	CALL UPPRDE();
	

	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		CREATE TABLE IF NOT EXISTS IEAI_FSH_AUDIT (IID NUMERIC(19), OPERATIONS VARCHAR(50), URL VARCHAR(50) NOT NULL, USERID NUMERIC(19) NOT NULL, USERFULLNAME  VARCHAR(100)  NOT NULL, OPERTIME NUMERIC(25),ISTATE  NUMERIC(19),IP VARCHAR(255), ITARGETDATA VARCHAR(1000), HOSTNAME VARCHAR(225),IDESC VARCHAR(200) , CONSTRAINT PK_IEAI_FSH_AUDIT PRIMARY KEY (IID)) ;
		COMMIT;

		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_TASK' AND COLUMN_NAME = 'IALARMID' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_FSH_TASK  ADD IALARMID_TMP VARCHAR(100);
			UPDATE IEAI_FSH_TASK SET IALARMID_TMP=IALARMID;
			UPDATE IEAI_FSH_TASK SET IALARMID=NULL;
			ALTER TABLE IEAI_FSH_TASK MODIFY IALARMID VARCHAR(100);	
			UPDATE IEAI_FSH_TASK SET IALARMID=IALARMID_TMP;
			ALTER TABLE IEAI_FSH_TASK DROP COLUMN IALARMID_TMP;
		END IF; 
		COMMIT;
		
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_TASK_HISTORY' AND COLUMN_NAME = 'IALARMID' AND DATA_TYPE='VARCHAR') THEN
			ALTER TABLE IEAI_FSH_TASK_HISTORY  ADD IALARMID_TMP VARCHAR(100);
			UPDATE IEAI_FSH_TASK_HISTORY SET IALARMID_TMP=IALARMID_TMP;
			UPDATE IEAI_FSH_TASK_HISTORY SET IALARMID=NULL;
			ALTER TABLE IEAI_FSH_TASK_HISTORY MODIFY IALARMID VARCHAR(100);	
			UPDATE IEAI_FSH_TASK_HISTORY SET IALARMID=IALARMID_TMP;
			ALTER TABLE IEAI_FSH_TASK_HISTORY DROP COLUMN IALARMID_TMP;
		END IF;
        COMMIT;

		IF  EXISTS (SELECT * FROM IEAI_PROPERTYCONFIG WHERE  IPROPERTYNAME='FSHProtectTime') THEN
			DELETE FROM IEAI_PROPERTYCONFIG WHERE IPROPERTYNAME='FSHProtectTime';
        END IF;
		COMMIT;
			  
        IF NOT EXISTS (SELECT * FROM IEAI_PROPERTYCONFIG WHERE  IPROPERTYNAME='FSHProtectTime') THEN
            INSERT INTO IEAI_PROPERTYCONFIG (IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) values (437,'FSHProtectTime',3,'故障自愈保护时间');
        END IF;
		COMMIT;

		IF EXISTS (SELECT * FROM IEAI_PROPERTYCONFIG WHERE  IPROPERTYNAME='FSHHISTORYCOUNT') THEN
			DELETE FROM IEAI_PROPERTYCONFIG WHERE IPROPERTYNAME='FSHHISTORYCOUNT';
		END IF;
		COMMIT;
			
        IF NOT EXISTS (SELECT * FROM IEAI_PROPERTYCONFIG WHERE  IPROPERTYNAME='FSHHistoryCount') THEN
            INSERT INTO IEAI_PROPERTYCONFIG (IID,IPROPERTYNAME,IPROPERTYVALUE,IPROPERTYDESC) values (1057,'FSHHistoryCount',3,'故障自愈历史查询条数');
        END IF;
        COMMIT;
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	-- v8.20自愈sql
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
		IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE  TABLE_NAME = 'IEAI_FSH_SCHEME' AND COLUMN_NAME = 'SYSNAME') THEN
				ALTER TABLE IEAI_FSH_SCHEME  ADD SYSNAME VARCHAR(255);
		END IF;
	END;;
	DELIMITER ;
	CALL UPPRDE();