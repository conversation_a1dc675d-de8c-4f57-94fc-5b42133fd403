var setsysname;
$(function() {
	var center = free;
	if (total == free) {
		center = 0;
	}
	$("#batch_total").html(total);
	$("#batch_finished").html(finished);
	$("#bigScreenConfig_bfb").html(per);
	$("#system_run_chart").html(dataPrj);
	var centerPie = echarts.init(document.getElementById('batch_center'));
	var batchRuntime = echarts.init(document.getElementById('batch_runtime'));
	loadData();
	function loadData() {
		$.get('showCommondExcelModelOverallProcess.do?queryDate=' + queryDate)
				.done(function(data) {
					var toal = data.total;
					var fre = data.free;
					var toal = data.total;
					var fin = data.finished;
					var percent = data.per;
					var cen = fre;
					// if(toal==fre){
					// cen=0;
					// }
					$("#batch_total").html(toal);
					$("#batch_finished").html(fin);
					$("#bigScreenConfig_bfb").html(percent);
					centerPie.clear();
					var option = {
						series : [ {
							type : 'pie',
							hoverAnimation : false,
							radius : [ '84%', '100%' ],
							center : [ '50%', '50%' ],
							avoidLabelOverlap : false,
							color : [ '#8accff', '#a175fb' ],
							label : {
								normal : {
									show : false,
									position : 'center'
								},
								emphasis : {
									show : false,
									textStyle : {
										fontSize : '30',
										fontWeight : 'bold'
									}
								}
							},
							labelLine : {
								normal : {
									show : false
								}
							},
							data : [ cen, fin ]
						} ]
					};
					centerPie.setOption(option);
				});
		$.get('showCommondExcelModelWarnInfo.do?queryDate=' + queryDate).done(
				function(data) {
					var trSubHTML = "";
					$.each(data.data, function(i, v) {
						trSubHTML = trSubHTML + "<tr>"
								+ "    <td><div class=\"alarm_td01\" title=\""
								+ v.proName_til + "\">" + v.proName
								+ "</div></td>"
								+ "    <td><div class=\"alarm_td02\" title=\""
								+ v.actName_til + "\">" + v.actName
								+ "</div></td>"
								+ "    <td><div class=\"alarm_td03\" title=\""
								+ v.warnTime_til + "\">" + v.warnTime
								+ "</div></td>"
								+ "   <td><div class=\"alarm_td04\"  title=\""
								+ v.actState + "\">" + v.actState
								+ "</div></td>" + "</tr>";
					})
					$("#alarm_tab_list").html(trSubHTML);
				});
		$.get('showCommondExcelModelSysRunInfo.do?queryDate=' + queryDate)
				.done(
						function(data) {
							$.each(data.data, function(i, v) {
								var char_id = v.flag;
								var char_text_id = "#_" + v.flag;
								var per = v.per;
								var total = v.total;
								var free = v.free;
								var finished = v.finished;
								var center = free;
								if (total == free) {
									center = 0;
								}
								var sysPie = echarts.init(document
										.getElementById(char_id));
								sysPie.clear();
								var option = {
									series : [ {
										type : 'pie',
										hoverAnimation : false,
										radius : [ '80%', '97%' ],
										center : [ '50%', '50%' ],
										avoidLabelOverlap : false,
										color : [ '#8accff', '#a175fb' ],
										label : {
											normal : {
												show : false,
												position : 'center'
											},
											emphasis : {
												show : false,
												textStyle : {
													fontSize : '30',
													fontWeight : 'bold'
												}
											}
										},
										labelLine : {
											normal : {
												show : false
											}
										},
										data : [ free, finished ]
									} ]
								};
								sysPie.setOption(option);
								$(char_text_id).html(per);
							});
						});
		$.get('showCommondExcelModelActInfo.do?queryDate=' + queryDate)
				.done(
						function(data) {
							var trSubHTML = "";
							$.each(data.data,
											function(i, v) {
												var state = v.actState;
												var status = '';
												var cls = '';
												if (state == '8') {
													status = '运行';
													cls = 'batch_sys_run';
												} else if (state == '4'
														|| state == '5'
														|| state == '6') {
													status = '异常';
													cls = 'batch_sys_abnormal';
												} else if (state == '10') {
													status = '未运行';
													cls = '';
												} else if (state == '20') {
													status = '完成';
													cls = 'batch_sys_complete';
												}
												trSubHTML = trSubHTML
														+ "<tr>"
														+ "<td><div class=\"batch_sys_td01\">"
														+ " <span class=\""
														+ cls
														+ "\">"
														+ status
														+ "</span></div></td>"
														+ "<td><div class=\"batch_sys_td02\" title=\""
														+ v.IPROJECTNAME
														+ "\">"
														+ v.IPROJECTNAME
														+ "</div></td>"
														+ "<td><div class=\"batch_sys_td03\" title=\""
														+ v.IACTNAME
														+ "\">"
														+ v.IACTNAME
														+ "</div></td>"
														+ "<td><div class=\"batch_sys_td04\" title=\""
														+ v.IREALBEGINTIME
														+ "\">"
														+ v.IREALBEGINTIME
														+ "</div></td>"
														+ "<td><div class=\"batch_sys_td04\" title=\""
														+ v.IREALENDTIME
														+ "\">"
														+ v.IREALENDTIME
														+ "</div></td>"
														+ "</tr>";
											});
							$("#batch_sys_list").html(trSubHTML);
						});
		$.get('showCommondExcelModelMile.do?queryDate=' + queryDate)
				.done(
						function(data) {
							var trSubHTML = "";
							trSubHTML = trSubHTML
									+ "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"100%\"><tr>";
							$.each(data.data,
								function(i, v) {
									// <tr class="milestone_dot"
									// id="milestone_dot_id">
									trSubHTML = trSubHTML
											+ "<td align=\"center\">"
											+ "<div class=\"ms_dot02 suspension_frame\">"
											+ "	<div class=\"frame_property frame_position_progress\">"
											+ "            <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\">"
											+ "                <tr>"
											+ "                    <td align=\"right\">系统名称</td>"
											+ "                    <td>"
											+ v.iprojectname
											+ "</td>"
											+ "               </tr>"
											+ "                <tr>"
											+ "                    <td align=\"right\">批量名称</td>"
											+ "                    <td>"
											+ v.iactname
											+ "</td>"
											+ "               </tr>"
											+ "                <tr>"
											+ "                    <td align=\"right\">开始时间</td>"
											+ "                    <td>"
											+ v.starttime
											+ "</td>"
											+ "               </tr>"
											+ "                <tr>"
											+ "                   <td align=\"right\">结束时间</td>"
											+ "                    <td>"
											+ v.endtime
											+ "</td>"
											+ "                </tr>"
											+ "            </table>"
											+ "        </div>"
											+ "</div>" + "</td>";
								});
							trSubHTML = trSubHTML + "</tr></table>";
							$("#milestone_dot_id").html(trSubHTML);
						});

		$.get('showCommondExcelModelSysRuntime.do?queryDate=' + queryDate)
				.done(function(data) {
					var xdata = [];
					var sdata1 = [];
					var sdata2 = [];
					$.each(data.data, function(i, v) {
						xdata.push(v.iprojectname);
						sdata1.push(v.hisavgTime);
						sdata2.push(v.avgTime);
					});
					batchRuntime.clear();
					var showCommondExcelModelSysRuntime = {
						tooltip : {
							show : true,
							trigger : 'axis',
							showDelay : 0,// 显示延时，添加显示延时可以避免频繁切换
							hideDelay : 50,// 隐藏延时
							transitionDuration : 0,// 动画变换时长
							backgroundColor : '#9d9ea0',// 背景颜色（此时为默认色）
							borderRadius : 8,// 边框圆角
							padding : 10
						},
						color : [ '#8accff', '#0b92fb' ],
						// legend: {
						// x: 'right',
						// data:['历史耗时','本次耗时'],
						// textStyle:{
						// color:"#9198b4"
						// }
						// },
						grid : {
							left : '2%',
							right : '2%',
							bottom : '2%',
							top : '2%',
							containLabel : true
						},
						calculable : true,
						xAxis : [ {
							type : 'category',
							splitLine : {
								show : false
							},
							axisLine : {
								lineStyle : {
									color : '#0b92fb'
								}
							},
							data : xdata,
							axisLabel : {
								show : true,
								nterval : 'auto',
								inside : false,
								rotate : 90,
								// margin: 8,
								textStyle : {
									color : "#9198b4"
								},
								formatter : function (value)
							     {
							         let valueTxt = '';
							         if (value.length > 8) {
							             valueTxt = value.substring(0,8) + '...';
							         }
							         else {
							             valueTxt = value;
							         }
							         return valueTxt ;
							     }
							}
						} ],
						yAxis : [

						{
							splitLine : {
								show : false
							},
							type : 'value',
							axisLine : {
								show : false
							},
							axisLabel : {
								show : true,
								textStyle : {
									color : "#9198b4",
									formatter : '{value}分'
								}
							}
						} ],
						// 第一个柱颜色#8accff,第二个柱颜色#0b92fb,背景线条颜色#0b92fb,字体颜色#9198b4,悬浮层字体颜色#9d9ea0
						series : [ {
							name : '历史耗时',
							type : 'bar',
							itemStyle : {
								normal : {
									color : "#8accff",
								}
							},
							barGap:'0%',/*多个并排柱子设置柱子之间的间距*/
				            //barCategoryGap:'50%',/*多个并排柱子设置柱子之间的间距*/
							data : sdata1
						}, {
							name : '本次耗时',
							type : 'bar',
							itemStyle : {
								normal : {
									color : "#0b92fb",
								}
							},
							data : sdata2
						} ]
					};
					batchRuntime.setOption(showCommondExcelModelSysRuntime);
					batchRuntime.on('click', function(params) {
						// 为三个柱子设置点击事件
						setsysname = params.name;
						$('#bm_win_title').html("系统“"+setsysname+"”特定耗时设置");
						$('#bm_window').show();
					});
				});
	}
	window.setInterval(function() {
		loadData();
	}, 5000);
});

function closering() {
	var aaa = confirm("确认后没有新告警将不会再响提示音，确认停止告警提示音？");
	if (aaa == true) {
		$.get('setAlarmRing.do?queryDate=' + queryDate).done();
		$('#turn_img').attr('src', '');
	}
}
function gotoBatchInSysDetail(prjname) {
	var url = "gotoBatchInSysDetail.do?pn=" + prjname + "&qd=" + queryDate;
	window.open(url); // 在另外新建窗口中打开窗口
}

function closeAvgTimeCfg() {
	$('#bm_window').hide();
}
function closeSetMethod() {
	$('#bm_window_alert').hide();
}
function saveAvgTime() {
	$('#bm_window').hide();
	$('#bm_window_alert').hide();
	var setdate = $('#demo').val();
	$.ajax({
		type : "POST",
		url : "setSysRuntimeShowDate.do",
		data : {
			sysname : setsysname,
			setdate : $('#demo').val(),
			queryDate:queryDate
		},
		success : function(data, response, status) {
			var succ=data.success;
			var mes=data.message;
			$('#bm_win_date_w').html(mes);
			$('#bm_window_alert').show();
		}
	});
}