package com.ideal.controller.platform.businessSystemQuery;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.ActBean;
import com.ideal.ieai.server.jobscheduling.repository.actmonitor.ActMonitorManager;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemBeanForQuery;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.BusinessSystemQueryBean;
import com.ideal.ieai.server.repository.permission.IPermissionManager;
import com.ideal.ieai.server.repository.permission.PermissionManager;
import com.ideal.ieai.server.repository.user.IUserManager;
import com.ideal.ieai.server.repository.user.UserManager;
import com.ideal.service.platform.businessSystemQuery.BusinessSystemQueryService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * 名称: BusinessSystemController.java<br>
 * 描述: 业务系统Controller<br>
 * 类型: JAVA<br>
 * 最近修改时间:2015年10月13日<br>
 * 
 * <AUTHOR>
 */
@Controller
public class BusinessSystemQueryController
{
    private static Logger       log            = Logger.getLogger(BusinessSystemQueryController.class);
    private static final String SUCCESS        = "success";
    private static final String MESSAGE        = "message";
    private static final String OPERASYSTYPE   = "opersystype";
    private static final String SYSNAME        = "sysName";
    private static final String URLTYPE        = "urlType";
    private static final String SYSCONFIGUSER  = "sysConfigUser";
    private static final String SYSCONFIGGUSER = "sysConfigGUser";


    IPermissionManager          _permMgr       = PermissionManager.getInstance();

    /**
     * User manager
     */
    IUserManager                _userMgr       = UserManager.getInstance();


    @RequestMapping("businessSystemQuery.do")
    public String businessSystemQuery ( HttpServletRequest request )
    {
        return "bussinessSystemQuery/businessSystemQuery";
    }


    @RequestMapping("initSystemDetailForIEAI.do")
    public String initProjectDetail ( HttpServletRequest request )
    {
        return "bussinessSystemQuery/projectSystemInfoForIEAI";
    }

    @RequestMapping("initExcelInfoForIEAI.do")
    public String initExcelInfoForIEAI ( HttpServletRequest request )
    {
        return "bussinessSystemQuery/businessSystemExcelInfo";
    }


    @RequestMapping("queryBusinessSystemInfo.do")
    @ResponseBody
    public Map queryBusinessSystemInfo ( HttpServletRequest request , BusinessSystemBeanForQuery businessSystemBeanForQuery ) throws RepositoryException
    {
        Map map = new HashMap();

        try{
            SessionData sessionData = SessionData.getSessionData(request);
            ActBean actBean=new ActBean();
            actBean.setDbType(Constants.IEAI_IEAI);
            actBean.setUserId(sessionData.getUserInnerCode());
            actBean.setUserName(sessionData.getUserName());
            actBean.setSysName("");
            Map proMap =  ActMonitorManager.getInstance().getPrjName(actBean);

            map= BusinessSystemQueryService.getInstance().queryBusinessSystemInfo(proMap,businessSystemBeanForQuery);

        }catch(RepositoryException e){

        }
        return map;
    }

    @RequestMapping("queryMainLineInfo.do")
    @ResponseBody
    public Map queryMainLineInfo ( HttpServletRequest request , BusinessSystemQueryBean businessSystemQueryBean ) throws RepositoryException
    {
        Map map = new HashMap();

        try{



            map= BusinessSystemQueryService.getInstance().queryMainLineInfo(businessSystemQueryBean);

        }catch(RepositoryException e){

        }
        return map;
    }


    @RequestMapping("queryExcelInfo.do")
    @ResponseBody
    public Map queryExcelInfo ( HttpServletRequest request , BusinessSystemQueryBean businessSystemQueryBean ) throws RepositoryException
    {
        Map map = new HashMap();

        try{



            map= BusinessSystemQueryService.getInstance().queryExcelInfo(businessSystemQueryBean);

        }catch(RepositoryException e){

        }
        return map;
    }





}