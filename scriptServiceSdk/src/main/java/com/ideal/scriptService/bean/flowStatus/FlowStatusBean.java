package com.ideal.scriptService.bean.flowStatus;

import lombok.Data;

/**
 * 脚本运行流程信息bean对象
 */
@Data
public class FlowStatusBean {
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务审核人
     */
    private String audiUser;
    /**
     * 任务开始时间
     */
    private String startTime;
    /**
     * 任务结束时间
     */
    private String endTime;
    /**
     * <pre>
     *     流程状态：
     *     10/11 ：运行
     *     20/5 : 完成
     *     30：异常
     *     40：异常完成
     *     50：异常运行
     *     60：已终止
     * </pre>
     */
    private int state;
}
