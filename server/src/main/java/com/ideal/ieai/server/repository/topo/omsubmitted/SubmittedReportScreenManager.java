package com.ideal.ieai.server.repository.topo.omsubmitted;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ProjectName: entegor_V8
 * @Package: com.ideal.ieai.server.repository.topo.omsubmitted
 */
public class SubmittedReportScreenManager {
    private static final Logger log = Logger.getLogger(SubmittedReportScreenManager.class);
    private static SubmittedReportScreenManager instance = null;
    private static final String                 STARTTIME         = "istarttime";
    private static final String                 ENDTIME           = "iendtime";
    private static final String                 PLANTIME          = "plantime";
    SimpleDateFormat formatdate = new SimpleDateFormat("HH:mm:ss");
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    private static final String  RUN_FIELDS ="IID, ISYSCLASSID, ISYSID, ISYSNAME, IRUNINSNAME, IINSDES, ISTARTTIME, IENDTIME, ISTATE, IISFAIL, ISTARTUSERID, ISTARTUSER, ISERVERIP, ISYSTYPE, IVERSIONNUM, IVERSION, IVERSIONDES, ISWITCHTO, IINSTANCETYPE, IMAXGRAPHID, IISMAININS, IBUSNES_SYS_IID, IDEPLOYIID, ISBACKINSTANCE, IENVIDS, ITASKID, MODELNAME, ISCONFIRM, IMXGRAPHID, IWORKITEMID, IAPMTYPE, IORDERNUM, ISCHECKFLOW, IINSTANCETYPE2, ITSMID, IBUTTERFLYVERSION, IAZTYPE, IPMPORDER, IXMLID, IIPMPCACHECODE, IBGTYPE, ICQTASKID, IEVENTID, ILCBP, ICHANGETYPE, IFATHER, ICICDTASKTYPE, IPROJECT_NUM, IREVIEWEDBY, ISTEPID";
    private static final String  RUNINFO_FIELDS = "IID, IRUNINSID, IFLOWID, IRUNINSNAME, ISERNER, ICONNER, IPRENER, IACTNAME, IACTDES, IACTTYPE, IREMINFO, IIP, IPORT, ISYSTYPE, ISHELLPATH, ITIMEOUT, ISWITCHSYSTYPE, IPARAMETER, IPARACHECK, IPARASWITCH, IPARASWITCHFORCE, ISTATE, IISFAIL, ISTARTTIME, IENDTIME, IEXECUSER, ISHELLSCRIPT, IRERUNFLAG, IEXPECEINFO, IISLOADENV, ICONNERNAME, ICENTER, IMODELTYPE, IRETNVALEXCEPION, IREDOABLE, IPKGNAME, ITYPE, ICHILDINSTANCEID, IMODELNAME, IMODELVERSION, ISCRIPTID, IMXGRAPHID, IPRESYSNAME, IPREACTNAME, IAPPFLAG, INOT_ALLOWED_EXEC, ISYSTEMTYPESTEP, IMODELTYPESTEP, SINGLEROLLBACK, IBRANCH, IDISABLE, ISPACEIID, IGOURPIID, IOWNERTYPE, IOWNER, IOLDIIP, IEXPBEGINTIME, IEXPENDTIME, IREMARK, IPROJECTNAME, IARRIVETIME, IISMANUAL, IREVIEWER, IBANKNAME, IREVIEWSTATE, ISELFHEALING, ISROLLBACK, IJUDGEISBACK, NETEQUIIP, ISRTO, IINFORTO, ICALLINSTANCENAME, ICALLPKGNAME, ICALLVERSIONDES, ICALLSTARTTIMES, ICALLSTARTENVNAMES, ICALLSTARTTIMESL, ICALLITASKID, ICALLIERRCLOBID, IROLLBACKSCHEME, IAZNAME, IPROBLEM, IISSEND, IPROXYIP, MAINDEPARTMENT, BUSINESSPERSONNEL, ARTISAN, KEYSTEPS, IEXECUTOR, IMESSAGEREMIND, PLANTIME, ICICDSTEPID, IHOSTNAME, ISONAME, ISTEPIUUID, ISCRIPTMODULETYPE, ISCRIPTMODULECHILDTYPE, ISTAGETYPE, IEXECFAILED, ISCRIPTTYPE, ICICDCONFIGFILE, IPRIORITY, ICICDEXECBATCHNUM, IEXECPERMISSION, ICICDINFOTYPE, IRUNMAININSTANCEID, IACTTIMEOUT, IMAININSTANCEID, ISTEPID";
    public static SubmittedReportScreenManager getInstance () {
        if (instance == null) {
            instance = new SubmittedReportScreenManager();
        }
        return instance;
    }

    /**
     *
     * @Title: showSubmittedReportScreen
     * @Description: 查询大屏数据
     * @param queryDate
     * @return
     */
    public Map<String, Object> showSubmittedReportScreen ( String queryDate)
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> datamap = new HashMap<String, Object>();
        Map<String, Object> map = new HashMap<String, Object>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
            String sql = "select  A.IID,A.ISYSID,A.ISYSNAME,A.IVERSIONNUM,A.IINSDES,A.ISTATE,A.ISTARTUSER,A.IPMPORDER,c.ICALLVERSIONDES as IENDTIME,A.IBGTYPE,A.IBUTTERFLYVERSION,A.ILCBP," +
                    "(SELECT min(ICALLPKGNAME) FROM (select * from (select "+RUNINFO_FIELDS+" from ieai_runinfo_instance_his) union all (select "+RUNINFO_FIELDS+" from ieai_runinfo_instance)) WHERE isystype = 52 and ICALLPKGNAME is not null and IRUNINSID = A .iid) AS ISTARTTIME,"+
                    "c.ICALLSTARTTIMES,c.ICALLSTARTENVNAMES,c.IROLLBACKSCHEME,c.ARTISAN,A.IISFAIL,c.IMODELTYPESTEP,c.PLANTIME,"+
                    "(SELECT COUNT (*) FROM (select "+RUNINFO_FIELDS+" from	(SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his where isystype = 52) UNION ALL (SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance where isystype = 52)) WHERE ("+
                    " iremark >0 OR ioldiip >0 OR iappflag >0 OR iproxyip >0 OR IISFAIL>0) AND IRUNINSID = A .iid) state," +
                    "(select count(*) from (SELECT "+RUNINFO_FIELDS+" FROM ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance ))  where isystype = 52 and IRUNINSID = A.iid and ICALLVERSIONDES is null) countnum from "+
                    "(SELECT A .iid AS iid,c.ICALLVERSIONDES,c.ICALLSTARTTIMES,c.ICALLSTARTENVNAMES,c.IROLLBACKSCHEME,c.ARTISAN,c.IMODELTYPESTEP,c.PLANTIME FROM ( SELECT "+RUN_FIELDS+" FROM ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his ) UNION ALL ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance)) A," +
                    "(SELECT MAX (ISERNER) AS ISERNER, iruninsid FROM (select "+RUNINFO_FIELDS+" from ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL (SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance )) GROUP BY iruninsid ) b, " +
                    "(select "+RUNINFO_FIELDS+" from (SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL (SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance)) c WHERE A .iid = b.iruninsid AND c.ISERNER = b.ISERNER AND A .iid = c.iruninsid ) c," +
                    " (SELECT "+RUN_FIELDS+" FROM ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his ) UNION ALL ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance ) ) A," +
                    "(SELECT MAX (iid) iid,isysid FROM (SELECT "+RUN_FIELDS+" FROM (SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his WHERE isystype = 52 AND (IINSDES = ? )) UNION ALL (SELECT "+RUN_FIELDS+" FROM ieai_run_instance WHERE isystype = 52 AND (IINSDES = ?))) GROUP BY isysid) f" +
                    " WHERE f .iid = c.iid AND A .iid = f.iid  ORDER BY IVERSIONNUM";

            ps = conn.prepareStatement(sql);
            ps.setString(1, queryDate);
            ps.setString(2, queryDate);
            rs = ps.executeQuery();
            while (rs.next())
            {
                datamap = new HashMap<String, Object>();
                datamap.put("iversionnum",  rs.getString("IVERSIONNUM"));//报送类型
                datamap.put("istarttime",  rs.getString(STARTTIME) == null || "null".equals(rs.getString(STARTTIME)) ? "" : rs.getString(STARTTIME));//运行开始时间
                datamap.put("iendtime",  rs.getString(ENDTIME) == null || "null".equals(rs.getString(ENDTIME)) ? "" : rs.getString(ENDTIME));//运行结束时间
                datamap.put("jinhong", rs.getString("ICALLSTARTTIMES") == null || "null".equals(rs.getString("ICALLSTARTTIMES")) ? "" : rs.getString("ICALLSTARTTIMES"));//上传金宏前置时间
                datamap.put("quzou", rs.getString("ICALLSTARTENVNAMES") == null || "null".equals(rs.getString("ICALLSTARTENVNAMES")) ? "" : rs.getString("ICALLSTARTENVNAMES"));//外管取走时间
                datamap.put("fankui", rs.getString("IROLLBACKSCHEME") == null || "null".equals(rs.getString("IROLLBACKSCHEME")) ? "" : rs.getString("IROLLBACKSCHEME"));//外管反馈时间
                String imodeltypestep = rs.getString("IMODELTYPESTEP");//文件下发时点标准值
                long str1 =  SubmittedHistoryManager.getInstance().getTodayDate(imodeltypestep);
                String endtime = rs.getString(ENDTIME)==null?"":rs.getString(ENDTIME).replace("\\","");
                String ibgtype = rs.getString("IBGTYPE")==null?"0":rs.getString("IBGTYPE");//历史是否重新计算
                if(ibgtype.equals("1")){//历史不需要重新计算
                    datamap.put("yanchi",  rs.getString("IBUTTERFLYVERSION")==null?"": rs.getString("IBUTTERFLYVERSION"));//延迟时长
                    datamap.put("yingxiang",  rs.getString("ILCBP"));//报送要求时间-下发实际时间
                    datamap.put("baosong",  rs.getString("IPMPORDER"));//报送要求时间
                }else{
                    long ipmporder =  SubmittedHistoryManager.getInstance().getTodayDate(rs.getString("IPMPORDER"));//报送要求时间
                    //延时时长：若未完成，为最后一步的上游累计延迟时长；若已完成，为最后一步的下发延迟时长(本步骤文件下发实际延时=本步骤文件下发实际时间-（文件下发标准时间+上游累计延迟时长）；)
                    if(rs.getString(ENDTIME)!=null){//运行结束时间不为空，已完成
                        datamap.put("yanchi",  formatDuring(simpDate(endtime, sdf) - str1 - rs.getLong(PLANTIME)));//延迟时长
                    }else{
                        datamap.put("yanchi",  rs.getLong(PLANTIME)==0?"":formatDuring(rs.getLong(PLANTIME)));//上游累计延迟时长
                    }
                    //是否影响报送 未完成，则用最后一步“统一监管报送文件下发标准时间+上游累计延迟时长”与“报送要求时间”比较，若大于等于0 影响报送，小于0 不影响；  已完成，则用最后一步“统一监管报送文件下发实际时间”和“报送要求时间”比较；
                    if(rs.getString(ENDTIME)!=null) {//运行结束时间不为空，已完成
                        datamap.put("yingxiang",  ipmporder-simpDate(endtime, sdf) >0?"否":"是");//报送要求时间-下发实际时间
                    }else{
                        long time = str1 + rs.getLong(PLANTIME);//标准值+上游累计延迟时长
                        datamap.put("yingxiang", time - ipmporder >= 0 ? "是" : "否");//（统一监管报送文件下发标准时间+上游累计延迟时长）-报送要求时间
                    }
                    datamap.put("baosong",  sdf.format(ipmporder));//报送要求时间
                }
                datamap.put("sysstate",  rs.getString("ARTISAN") == null || "null".equals(rs.getString("ARTISAN")) ? "" : rs.getString("ARTISAN"));//外管反馈状态
                long state =  rs.getLong("STATE");
                long istate =  rs.getLong("ISTATE");
                if(state>0){//下级存在“异常”状态的任务步骤
                    datamap.put("state", 3);//状态为异常
                }else{
                    if(rs.getString(STARTTIME)==null &&  istate!=2){//运行开始时间为空，且无异常，状态为未运行 非终止
                        datamap.put("state", 1);
                    }else if(rs.getLong("countnum")>0 &&  istate!=2){
                        datamap.put("state", 0);
                    }else if(rs.getString(STARTTIME)==null &&  istate==2){
                        datamap.put("state", 3);//状态为异常
                    }else if(rs.getLong("countnum")>0 &&  istate==2){
                        datamap.put("state", 3);//状态为异常
                    }else{
                        datamap.put("state", 2);//运行结束时间不为空，且无异常，状态为已完成
                    }
                }
                list.add(datamap);
            }
            map.put("data", list);
        } catch (Exception e)
        {
            log.error(method, e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, log);
        }
        return map;
    }

    public static String formatDuring(long mss) {
//        long hours = (mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
//        long minutes = (mss % (1000 * 60 * 60)) / (1000 * 60);
//        long seconds = (mss % (1000 * 60)) / 1000;
        long day = mss / (24 * 60 * 60 * 1000);
        long hours = (mss / (60 * 60 * 1000) - day * 24);
        long minutes = ((mss / (60 * 1000)) - day * 24 * 60 - hours * 60);
        long seconds = (mss / 1000 - day * 24 * 60 * 60 - hours * 60 * 60 - minutes * 60);
        if(Math.abs(day)>0){
            hours = Math.abs(day)*24 + Math.abs(hours);
        }
        String time = Math.abs(hours) + "小时" + Math.abs(minutes) + "分" + Math.abs(seconds) + "秒";
        if (mss < 0) {
            time = "-" + Math.abs(hours) + "小时" + Math.abs(minutes) + "分" + Math.abs(seconds) + "秒";
        }
        if(mss==0){
            time = "0秒";
        }
        if(time.equals("0小时0分0秒")){
            time = "0秒";
        }
        if(time.equals("-0小时0分0秒")){
            time = "0秒";
        }
        return time;


    }

    /**
     * 字符串转时间戳
     *
     * @param time
     * @return
     */
    private Long simpDate(String time, SimpleDateFormat sdf) {
        Long resultTime = 0L;
        try {
            if (com.ideal.ieai.server.jobscheduling.repository.home.StringUtils.isNotBlank(time)) {
                if (time.split(":").length == 2) {
                    time += ":00";
                }
                resultTime = sdf.parse(time).getTime();
            }
        } catch (ParseException pe) {
            log.error("SimpleDateFormat is (" + sdf.format(new Date()) + ") simpDate for error: " + pe.getMessage());
        }
        return resultTime;
    }

    /**
     *
     * @Title: getSubmittedReportScreen
     * @Description: 查询大屏数据
     * @param iruninsid
     * @return
     */
    public List<Map<String, Object>> getSubmittedReportScreen (String iruninsid)
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> datamap = new HashMap<String, Object>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "";
        Connection conn = null;
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
            sql = "select "+RUNINFO_FIELDS+" from ((select "+RUNINFO_FIELDS+" from ieai_runinfo_instance_his  where  isystype = 52  and  iruninsid = ?) union all (select "+RUNINFO_FIELDS+" from IEAI_RUNINFO_INSTANCE  where isystype = 52 and  iruninsid = ?) )d ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(iruninsid));
            ps.setLong(2, Long.valueOf(iruninsid));
            rs = ps.executeQuery();
            while (rs.next())
            {
                datamap = new HashMap<String, Object>();
                datamap.put("iid",  rs.getString("IID"));//报送类型
                datamap.put("type",  rs.getString("IRUNINSNAME"));//报送类型
                datamap.put("iserner",  rs.getString("ISERNER"));//步骤号
                datamap.put("iparameter",  rs.getString("IPARAMETER"));//上游步骤
                datamap.put("state", rs.getString("ISTATE"));//运行状态
                datamap.put("isfail", rs.getString("IISFAIL"));
                long isrto = rs.getLong("ISRTO");
                if(isrto==1) {//历史数据已重新计算
                    String isystemtypestep = rs.getString("ISYSTEMTYPESTEP");
                    String imodeltypestep = rs.getString("IMODELTYPESTEP");
                    if (isystemtypestep.split(":").length == 3) {
                        isystemtypestep = isystemtypestep.split(":")[0] + ":" + isystemtypestep.split(":")[1];
                    }
                    if (imodeltypestep.split(":").length == 3) {
                        imodeltypestep = imodeltypestep.split(":")[0] + ":" + imodeltypestep.split(":")[1];
                    }
                    datamap.put("istarttime",  isystemtypestep);//收齐时间
                    datamap.put("iendtime",  imodeltypestep);//下发时间
                }else{
                    Long collectSetL = SubmittedHistoryManager.getInstance().getTodayDate(rs.getString("ISYSTEMTYPESTEP"));//文件收齐设置时间
                    Long issuedSetL = SubmittedHistoryManager.getInstance().getTodayDate(rs.getString("IMODELTYPESTEP"));//文件下发设置时间
                    datamap.put("istarttime", sdfd.format(collectSetL));//文件收齐时点  标准值
                    datamap.put("iendtime",  sdfd.format(issuedSetL));//下发时间
                }
                list.add(datamap);
            }
        } catch (Exception e)
        {
            log.error(method, e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, log);
        }
        return list;
    }

    public List<Map<String, Object>> showSubmittedReportScreen1 ( String queryDate)
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> datamap = new HashMap<String, Object>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "";
        Connection conn = null;
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
            sql = "SELECT A .IID,A .IVERSIONNUM, A.IRUNINSNAME FROM (SELECT "+RUN_FIELDS+" FROM (SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his) UNION ALL( SELECT "+RUN_FIELDS+" FROM ieai_run_instance )) A,"+
                    "(SELECT MAX (iid) iid,isysid FROM( SELECT "+RUN_FIELDS+" FROM (SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his WHERE isystype = 52 AND (IINSDES = ?))"+
                    "UNION ALL (SELECT "+RUN_FIELDS+" FROM ieai_run_instance WHERE isystype = 52 AND (IINSDES = ?)) ) GROUP BY isysid ) f WHERE A .iid = f.iid ORDER BY IVERSIONNUM";
            ps = conn.prepareStatement(sql);
            ps.setString(1, queryDate);
            ps.setString(2, queryDate);
            rs = ps.executeQuery();
            while (rs.next())
            {
                datamap = new HashMap<String, Object>();
                datamap.put("iid",  rs.getString("iid"));
                datamap.put("type",  rs.getString("IRUNINSNAME"));//报送类型
                datamap.put("iversionnum", rs.getString("IVERSIONNUM"));
                list.add(datamap);
            }
        } catch (Exception e)
        {
            log.error(method, e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, log);
        }
        return list;
    }


    public Map<String, String> getTopoDataCellState (String queryDate)
    {
        PreparedStatement pstmt = null;
        PreparedStatement pstmts = null;
        ResultSet rs = null;
        ResultSet rss = null;
        Connection con = null;
        String runstate = "";
        LinkedHashMap<String, String> premap = new LinkedHashMap<String, String>();
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
        } catch (DBException e1)
        {
            log.error(e1);
        }
        String sql = "select  A.IID,A.ISYSID,A.ISYSNAME,A.IRUNINSNAME,A.IINSDES,A.ISTATE," +
                "(SELECT min(ICALLPKGNAME) FROM (select "+RUNINFO_FIELDS+" from (select "+RUNINFO_FIELDS+" from ieai_runinfo_instance_his) union all (select "+RUNINFO_FIELDS+" from ieai_runinfo_instance)) WHERE isystype = 52 and ICALLPKGNAME is not null and IRUNINSID = A .iid) AS ISTARTTIME,"+
                "(select count(*) from (SELECT "+RUNINFO_FIELDS+" FROM ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance )) where isystype = 52 and (iremark>1 or ioldiip>1 or iappflag>1 or iproxyip>1 OR IISFAIL>0) and IRUNINSID = A.iid) state," +
                "(select COUNT (*) from ((SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance ) UNION ALL ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ))WHERE isystype = 52 and (KEYSTEPS = 0 or KEYSTEPS is null) AND IRUNINSID = A .iid) num," +
                "(select count(*) from (SELECT "+RUNINFO_FIELDS+" FROM ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance ))  where isystype = 52 and IRUNINSID = A.iid and ICALLVERSIONDES is null) countnum from " +
                "(select A.ICALLPKGNAME,A.IRUNINSID FROM (select distinct IRUNINSID,ISERNER,ICALLPKGNAME from (SELECT "+RUNINFO_FIELDS+" FROM ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance )) WHERE isystype = 52)A ," +
                "(SELECT min(ISERNER)ISERNER,IRUNINSID FROM (SELECT "+RUNINFO_FIELDS+" FROM ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT "+RUNINFO_FIELDS+" FROM ieai_runinfo_instance )) WHERE isystype = 52 GROUP BY IRUNINSID)B WHERE B.ISERNER = A.ISERNER and A.IRUNINSID = B.IRUNINSID) b," +
                "(SELECT "+RUN_FIELDS+" FROM ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his ) UNION ALL ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance )) a," +
                "(select max(iid) iid,isysid from (SELECT "+RUN_FIELDS+" FROM ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his ) UNION ALL ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance ))  where (IINSDES = ?) AND isystype = 52 group by isysid) f where  b.IRUNINSID = A.iid  and A.iid =f.iid ";

//        String sql = "select  A.IID,A.ISYSID,A.ISYSNAME,A.IRUNINSNAME,A.IINSDES,A.ISTATE," +
//                "(SELECT min(ICALLPKGNAME) FROM (select * from (select * from ieai_runinfo_instance_his) union all (select * from ieai_runinfo_instance)) WHERE isystype = 52 and ICALLPKGNAME is not null and IRUNINSID = A .iid) AS ISTARTTIME,"+
//                "(select count(*) from (SELECT * FROM ( SELECT * FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT * FROM ieai_runinfo_instance )) where isystype = 52 and (iremark>0 or ioldiip>0 or iappflag>0 or iproxyip>0 OR IISFAIL>0) and IRUNINSID = A.iid) state," +
//                "(select COUNT (*) from ((SELECT * FROM ieai_runinfo_instance ) UNION ALL ( SELECT * FROM ieai_runinfo_instance_his ))WHERE isystype = 52 and (KEYSTEPS = 0 or KEYSTEPS is null) AND IRUNINSID = A .iid) num," +
//                "(select count(*) from (SELECT * FROM ( SELECT * FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT * FROM ieai_runinfo_instance ))  where isystype = 52 and IRUNINSID = A.iid and ICALLVERSIONDES is null) countnum from " +
//                "(select A.ICALLPKGNAME,A.IRUNINSID FROM (select distinct IRUNINSID,ISERNER,ICALLPKGNAME from (SELECT * FROM ( SELECT * FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT * FROM ieai_runinfo_instance )) WHERE isystype = 52)A ,(SELECT min(ISERNER)ISERNER,IRUNINSID FROM (SELECT * FROM ( SELECT * FROM ieai_runinfo_instance_his ) UNION ALL ( SELECT * FROM ieai_runinfo_instance )) WHERE isystype = 52 GROUP BY IRUNINSID)B WHERE B.ISERNER = A.ISERNER and A.IRUNINSID = B.IRUNINSID) b,(SELECT * FROM ( SELECT * FROM ieai_run_instance_his ) UNION ALL ( SELECT * FROM ieai_run_instance )) a," +
//                "(select max(iid) iid,isysid from (SELECT * FROM ( SELECT * FROM ieai_run_instance_his ) UNION ALL ( SELECT * FROM ieai_run_instance ))  where (IINSDES = ?) AND isystype = 52 group by isysid) f where  b.IRUNINSID = A.iid  and A.iid =f.iid ";

        try
        {
            pstmt = con.prepareStatement(sql);
            pstmt.setString(1, queryDate);
            rs = pstmt.executeQuery();
            while (rs.next())
            {
                String tCell = rs.getString(1);
                long state =  rs.getLong("STATE");
                long zt = rs.getLong("num");//是否终态
                long istate =  rs.getLong("ISTATE");
                String proUpState = "";
                /*
                 *（主实例）类型：0：运行中；1：已完成；2：终止
                 *（子步骤）步骤：0：运行中；1：未运行；2：已完成；3：异常
                 * 异常级别：异常>运行中>已完成>未开始
                 */
                //步骤异常 （三种异常:处理、收齐、下发）
                if(state>0){//下级存在“异常”状态的任务步骤
                    // 终态或者终止（终态或者终止算系统异常）
                    if(zt==0 || istate==2){//终态或者终止
                        proUpState = "3";//状态为异常
                    }else{ //不是终态且存在三种异常
                        proUpState = "4";//状态为运行中异常
                    }
                }else{
                    //报送类型（实例）终止状态 (只要终止了就算异常)
                    if(istate==2){
                        proUpState = "3";
                    }else{
                        //开始时间为空
                        if(rs.getString(STARTTIME)==null){
                            proUpState = "1";
                        }else if(rs.getLong("countnum")>0){ //步骤中文件下发实际时间有空值
                            proUpState = "0";
                        }else if(rs.getLong("countnum")==0){ //步骤中文件下发实际时间都有值
                            if(0==zt){ //步骤都为终态-
                                proUpState = "2";
                            }else{ //步骤不是终态
                                proUpState = "0";
                            }
                        }
                    }
                    //20231127注释原逻辑
//                    if(rs.getString(STARTTIME)==null &&  istate!=2){//运行开始时间为空，且无异常，状态为未运行
//                        proUpState = "1";
//                    }else if(rs.getLong("countnum")>0 && istate!=2){//下级存在运行中的数据，下级无异常步骤，不为终止数据，状态为运行中
//                        proUpState = "0";
//                    }else if(rs.getString(STARTTIME)==null &&  istate==2){
//                        proUpState = "3";//状态为异常
//                    }else if(rs.getLong("countnum")>0 &&  istate==2){//下级不存在运行中数据，下级无异常，终止数据，显示异常
//                        proUpState = "3";//状态为异常
//                    }else{
//                        proUpState = "2";//运行结束时间不为空，且无异常，状态为已完成
//                    }
                }
                if ("".equals(runstate))
                {
                    runstate = tCell + ":" + proUpState + ",";
                } else
                {
                    runstate = runstate + tCell  + ":" + proUpState + ",";
                }
            }
            premap.put("runstate", runstate);
        } catch (SQLException e)
        {
            log.error("SubmittedReportScreenManager.getTopoDataCellState" + e.getMessage());
        } finally
        {
            DBResource.closeConn(con, rs, pstmt, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePSRS(rss, pstmts, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return premap;
    }

    /**
     *
     * @Title: showSubmittedReportScreenDetail
     * @Description: 获取步骤执行明细弹框字段
     * @param iruninsid
     * @return
     */
    public Map<String, Object> showSubmittedReportScreenDetail ( String iruninsid)
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> datamap = new HashMap<String, Object>();
        Map<String, Object> map = new HashMap<String, Object>();
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rs = null;
        ResultSet rs1 = null;
        Connection conn = null;
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
//            String sql =  "select A.*,(select istate from ((SELECT * FROM ieai_run_instance WHERE isystype = 52 ) UNION ALL ( SELECT * FROM ieai_run_instance_his WHERE isystype = 52 ))WHERE iid = A .iruninsid) state from ((select * from IEAI_RUNINFO_INSTANCE_HIS where isystype = 52 AND IID =? ) union all (select * from IEAI_RUNINFO_INSTANCE where isystype = 52 AND IID =?)) A";
//            String sql1 = "select A.*,(select istate from ((SELECT * FROM ieai_run_instance WHERE isystype = 52 ) UNION ALL ( SELECT * FROM ieai_run_instance_his WHERE isystype = 52 ))WHERE iid = A .iruninsid) state from ((select * from IEAI_RUNINFO_INSTANCE_HIS where isystype = 52 AND IID =? ) union all (select * from IEAI_RUNINFO_INSTANCE where isystype = 52 AND IID =?)) A";
            String fields=" IRUNINSNAME,IAZNAME,PLANTIME,IREMARK,IOLDIIP,IAPPFLAG,IPROXYIP,IISFAIL,ICALLPKGNAME,ICALLVERSIONDES,ISYSTEMTYPESTEP,IMODELTYPESTEP," +
                    "SINGLEROLLBACK,ISRTO,IOWNER,IPARAMETER,KEYSTEPS,IMODELTYPE,IRETNVALEXCEPION,IREDOABLE,IBRANCH,IMESSAGEREMIND,IEXECUTOR,BUSINESSPERSONNEL,IRUNINSID," +
                    "IID,ICALLSTARTTIMES,IEXPECEINFO,ICALLINSTANCENAME,IIP,ISERNER,ISTATE ";
            String sql = "SELECT A.*,(select istate from ((SELECT * FROM ieai_run_instance WHERE isystype = 52 ) UNION ALL ( SELECT * FROM ieai_run_instance_his WHERE isystype = 52 ))WHERE iid = A .iruninsid) state FROM ((select " + fields + " from IEAI_RUNINFO_INSTANCE_HIS where isystype = 52 AND IID =? ) union all (select " + fields + " from IEAI_RUNINFO_INSTANCE where isystype = 52 AND IID =?) ) A ";
            String sql1 = "SELECT A.*,(select istate from ((SELECT * FROM ieai_run_instance WHERE isystype = 52 ) UNION ALL ( SELECT * FROM ieai_run_instance_his WHERE isystype = 52 ))WHERE iid = A .iruninsid) state FROM ((select " + fields + " from IEAI_RUNINFO_INSTANCE_HIS where isystype = 52 AND IID =? ) union all (select " + fields + " from IEAI_RUNINFO_INSTANCE where isystype = 52 AND IID =?) ) A ";

            ps = conn.prepareStatement(sql);
            ps1 = conn.prepareStatement(sql1);
            ps.setString(1, iruninsid);
            ps.setString(2, iruninsid);
            ps1.setString(1, iruninsid);
            ps1.setString(2, iruninsid);
            rs = ps.executeQuery();
            rs1 = ps1.executeQuery();
            JSONArray instanceList = new JSONArray();
            while (rs1.next()) {
                instanceList.add(selectInstance(rs1));
            }
            while (rs.next())
            {
                datamap = new HashMap<String, Object>();
                datamap.put("iname",  rs.getString("IRUNINSNAME"));//系统名称
                datamap.put("iazname",  rs.getString("IAZNAME") == null || "null".equals(rs.getString("IAZNAME")) ? "" : rs.getString("IAZNAME"));//系统编号
                datamap.put("sylj",   rs.getLong(PLANTIME)==0?"0":formatDuring(rs.getLong(PLANTIME)));//上游累计延迟时长（分钟）
                long state =  rs.getLong("STATE");
                if(rs.getLong("IREMARK")  > 0  || rs.getLong("IOLDIIP")  > 0 ||  rs.getLong("IAPPFLAG")  > 0 || rs.getLong("IPROXYIP") > 0 || rs.getLong("IISFAIL")>0) {//接收/处理/下发延时/异常推送过告警
                    datamap.put("state", 3);//状态为异常
                }else {
                    if(rs.getString("ICALLPKGNAME")==null &&  state!=2){//运行开始时间为空，且无异常，状态为未运行 非终止
                        datamap.put("state", 1);//状态为未运行
                    }else if(rs.getString("ICALLVERSIONDES")==null &&  state!=2){//运行开始时间不为空，运行结束时间为空，且下级无异常步骤，状态为运行中 非终止
                        datamap.put("state", 0);//状态为运行中
                    }else if(rs.getString("ICALLPKGNAME")==null &&  state==2){
                        datamap.put("state", 3);//状态为异常
                    }else if(rs.getString("ICALLVERSIONDES")==null &&  state==2){
                        datamap.put("state", 3);//状态为异常
                    }else{
                        datamap.put("state", 2);//状态为已完成  运行结束时间不为空，且无异常；
                    }
                }
                String collectTime = rs.getString("ISYSTEMTYPESTEP");
                String collectRealTime = rs.getString("ICALLPKGNAME");
                String issuedTime = rs.getString("IMODELTYPESTEP");
                String issuedRealTime = rs.getString("ICALLVERSIONDES");
                String dealTime = rs.getString("SINGLEROLLBACK");
                long planTime = rs.getLong(PLANTIME);
                datamap.put("icallpkgname", collectRealTime == null || "null".equals(collectRealTime) ? "" : collectRealTime);//文件收齐时点  实际值
                datamap.put("icallversiondes", issuedRealTime== null || "null".equals(issuedRealTime) ? "" : issuedRealTime);//文件下发时点  实际值
                datamap.put("wjsqsfwc", rs.getString("ICALLPKGNAME") == null || "null".equals(rs.getString("ICALLPKGNAME")) ? "否" : "是");//文件收齐是否完成  实际值是否存在，如存在则完成
                datamap.put("wjxfsfwc", rs.getString("ICALLVERSIONDES") == null || "null".equals(rs.getString("ICALLVERSIONDES"))? "否" : "是");//文件下发是否完成  实际值是否存在，如存在则完成
                long isrto = rs.getLong("ISRTO");
                long xflj = rs.getLong("IOWNER");
                //上游步骤号
                String upNum = rs.getString("IPARAMETER");
                String keysteps = rs.getString("KEYSTEPS");
                if(keysteps==null || "".equals(keysteps)){
                    keysteps = "-1";
                }
                datamap.put("keysteps",keysteps);
//                if(isrto==1){//历史数据已重新计算
                datamap.put("wjsqsfyc", rs.getLong("IMODELTYPE") > 0 ? "是" : "否");// 文件收齐 本系统是否延迟
                datamap.put("wjsqycsc", (rs.getString("ICALLPKGNAME") == null || "null".equals(rs.getString("ICALLPKGNAME"))) && rs.getLong("IMODELTYPE") <=0 ? "0秒" : formatDuring(rs.getLong("IMODELTYPE")));//文件收齐 本系统实际延时时长
                datamap.put("wjxfsfyc", rs.getLong("IRETNVALEXCEPION") > 0 ? "是" : "否");//文件下发 本系统是否延迟
                datamap.put("wjxfycsc", (rs.getString("ICALLVERSIONDES") == null || "null".equals(rs.getString("ICALLVERSIONDES"))) && rs.getLong("IRETNVALEXCEPION") <= 0 ? "0秒" : formatDuring(rs.getLong("IRETNVALEXCEPION")));//文件下发 本系统实际延时时长
                datamap.put("clscyc", rs.getLong("IREDOABLE") > 0 ? "是" : "否");//处理时长 本系统是否延迟
                datamap.put("clscsc",  (rs.getString("ICALLPKGNAME")==null && rs.getString("ICALLVERSIONDES")==null) && rs.getLong("IREDOABLE")<= 0 ? "0秒":formatDuring(rs.getLong("IREDOABLE")));//处理时长 本系统实际延时时长
                datamap.put("isystemtypestep", collectTime == null || "null".equals(collectTime) ? "" : collectTime);//文件收齐时点  标准值
                datamap.put("imodeltypestep", issuedTime == null || "null".equals(issuedTime) ? "" : issuedTime);//文件下发时点  标准值
                //xflj = xflj + rs.getLong("IRETNVALEXCEPION");//文件下发延迟时长+上有累计延迟时间
//                }else{//计算，正在运行中的数据
//                    if(StringUtils.isNotBlank(collectTime)){
//                        Long collectSetL = SubmittedHistoryManager.getInstance().getTodayDate(collectTime);//文件收齐设置时间
//                        Long collectRealL = simpDate(collectRealTime, sdf);//文件收齐实际时间
//                        collectRealL = collectRealL == 0 ? System.currentTimeMillis() : collectRealL;//收齐实际时间为空取当前时间
//                        Long realCollTime = collectRealL - collectSetL - planTime;//若未完成，则为当前时间-（文件收齐标准值+上游累计延迟时长）；若已完成，则为本步骤脚本返回文件收齐实际时间-（文件收齐标准值+上游累计延迟时长）；
//                        datamap.put("wjsqsfyc", realCollTime > 0 ? "是" : "否");// 文件收齐 本系统是否延迟
//                        datamap.put("wjsqycsc", (rs.getString("ICALLPKGNAME") == null || "null".equals(rs.getString("ICALLPKGNAME"))) && realCollTime<=0 ? "0秒" : formatDuring(realCollTime));//文件收齐 本系统实际延时时长
//                        datamap.put("isystemtypestep", sdf.format(collectSetL));//文件收齐时点  标准值
//                    }else{
//                        datamap.put("isystemtypestep", "");//文件收齐时点  标准值
//                    }
//                    if (StringUtils.isNotBlank(issuedTime)) {
//                        Long bhNum = 1000000000000000000L;
//                        Long issuedSetL = SubmittedHistoryManager.getInstance().getTodayDate(issuedTime);//文件下发设置时间
//                        Long issuedRealT = simpDate(issuedRealTime, sdf);//文件下发实际时间
//                        Long issuedRealL = issuedRealT == 0 ? System.currentTimeMillis() : issuedRealT;//文件下发实际时间为空取当前时间
//                        long realIssuedTime = issuedRealL - issuedSetL;
//                        for (int i = 0; i < instanceList.size(); i++) {
//                            JSONObject thisObj = instanceList.getJSONObject(i);
//                            String hUpNum = thisObj.getString("upNum");
//                            String hRunInsId = thisObj.getString("runInsId");
//                            if (iruninsid == hRunInsId && upNum == hUpNum) {
//                                //当前兄弟节点的设定标准时间
//                                Long hIssuedTime = SubmittedHistoryManager.getInstance().getTodayDate(thisObj.getString("issuedTime"));
//                                //得到当前步骤与兄弟节点设定标准时间最大值的所用时长（bOne大于0说明超时了）
//                                Long bOne = issuedRealL - (hIssuedTime + planTime);
//                                if (bOne > 0 && bhNum > bOne) {
//                                    bhNum = hIssuedTime;
//                                }
//                            }
//                        }
//                        if (bhNum != 1000000000000000000L && bhNum > 0) {
//                            realIssuedTime = issuedRealL - bhNum;
//                        }
//                        Long realIssuedTimeNow = realIssuedTime - rs.getLong(PLANTIME);
//                        datamap.put("wjxfsfyc", realIssuedTimeNow > 0 ? "是" : "否");//文件下发 本系统是否延迟
//                        datamap.put("wjxfycsc", (rs.getString("ICALLVERSIONDES") == null || "null".equals(rs.getString("ICALLVERSIONDES"))) && realIssuedTimeNow <=0 ? "0秒" : formatDuring(realIssuedTimeNow));//文件下发 本系统实际延时时长
//                        //xflj = xflj + realIssuedTimeNow;//文件下发延迟时长+上有累计延迟时间
//                        datamap.put("imodeltypestep", sdf.format(issuedSetL));//文件下发时点  标准值
//                    }else{
//                        datamap.put("imodeltypestep", "");//文件下发时点  标准值
//                    }
//                    //处理延迟
//                    if (StringUtils.isNotBlank(collectRealTime)) {//文件收齐实际时间为空不计算
//                        dealTime = StringUtils.isBlank(dealTime) ? "0" : dealTime;
//                        Long dealTimeSimp = Long.parseLong(dealTime) * 60 * 1000;
//                        Long irt = simpDate(issuedRealTime, sdf);//文件下发实际时间
//                        irt = irt == 0 ? System.currentTimeMillis() : irt;//下发实际时间为空取当前时间
//                        Long dealTmeL = irt - simpDate(collectRealTime, sdf) - dealTimeSimp;//若未完成，则为（当前时间-文件收齐实际时间）-（文件处理标准时间） 若已完成，则为（文件下发实际时间-文件收齐实际时间）-（文件处理标准时间）；
//                        datamap.put("clscyc", dealTmeL > 0 ? "是" : "否");//处理时长 本系统是否延迟
//                        datamap.put("clscsc", (rs.getString("ICALLPKGNAME")==null || rs.getString("ICALLVERSIONDES")==null) && dealTmeL <= 0 ? "0秒" : formatDuring(dealTmeL));//处理时长 本系统实际延时时长
//                    }else{
//                        datamap.put("clscyc", "否");//处理时长 本系统是否延迟
//                        datamap.put("clscsc", "0秒");//处理时长 本系统实际延时时长
//                    }
//
//                }
                if(StringUtils.isBlank(issuedRealTime)){//下发实际时间为空
                    xflj = 0;
                }
                datamap.put("xflj", xflj == 0 ? "--" : formatDuring(xflj));//下发累计延迟时长（分钟）
                datamap.put("wjsqyj", rs.getString("IBRANCH") == null || "null".equals(rs.getString("IBRANCH"))? "" : rs.getString("IBRANCH")+"分");//文件收齐 延迟时长预警阈值
                datamap.put("wjxfyj", rs.getString("IMESSAGEREMIND") == null || "null".equals(rs.getString("IMESSAGEREMIND"))? "" : rs.getString("IMESSAGEREMIND")+"分");//文件下发 延迟时长预警阈值
                datamap.put("wjsqgj", rs.getLong("IOLDIIP") > 0 ? "1" : "0" );//文件收齐 告警标识
                datamap.put("wjxfgj", rs.getLong("IAPPFLAG") > 0  ? "1" : "0");//文件下发 告警标识
                datamap.put("bzclsc", rs.getString("SINGLEROLLBACK")==null ?"":rs.getString("SINGLEROLLBACK")+"分");//处理时长 标准值
                if(rs.getString("ICALLVERSIONDES")!=null && rs.getString("ICALLPKGNAME")!=null){
                    datamap.put("sjclsc",  formatDuring(simpDate(rs.getString("ICALLVERSIONDES"),sdf) - simpDate(rs.getString("ICALLPKGNAME"),sdf)));//处理时长 实际值 文件下发实际时间-文件收齐实际时间
                }else{
                    datamap.put("sjclsc",  "");//处理时长 实际值
                }
                datamap.put("clscwc",  rs.getString("ICALLPKGNAME")!=null && rs.getString("ICALLVERSIONDES")!=null?"是":"否");//处理时长 是否完成  实际值不为空则已完成
                datamap.put("clscsyj",  rs.getString("IEXECUTOR")==null?"":rs.getString("IEXECUTOR")+"分");//处理时长 延迟时长预警阈值
                datamap.put("clscsgj",  rs.getLong("IREMARK") > 0 ? "1" : "0" );//处理时长 告警标识
                datamap.put("businesspersonnel",  rs.getString("BUSINESSPERSONNEL") == null || "null".equals(rs.getString("BUSINESSPERSONNEL")) ? "" : rs.getString("BUSINESSPERSONNEL"));//异常信息
                datamap.put("iisfail", rs.getString("IISFAIL"));
                datamap.put("isDealAlarm", rs.getLong("IREMARK"));
                datamap.put("isCollectAlarm", rs.getLong("IOLDIIP"));
                datamap.put("isIssuedAlarm", rs.getLong("IAPPFLAG"));
                datamap.put("isExpectAlarm", rs.getLong("IPROXYIP"));
                list.add(datamap);
            }
            map.put("data", list);
        } catch (Exception e)
        {
            log.error(method, e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, log);
            DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return map;
    }

    private JSONObject selectInstance(ResultSet rSet) throws RepositoryException {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        JSONObject result = new JSONObject();
        try {
            result.put("id", rSet.getLong("IID"));
            result.put("collectTime", rSet.getString("ISYSTEMTYPESTEP"));
            result.put("issuedTime", rSet.getString("IMODELTYPESTEP"));
            result.put("dealTime", rSet.getString("SINGLEROLLBACK"));
            result.put("collectAlarmTime", stringToLong(rSet.getString("IBRANCH")));
            result.put("issuedAlarmTime", stringToLong(rSet.getString("IMESSAGEREMIND")));
            result.put("dealAlarmTime", stringToLong(rSet.getString("IEXECUTOR")));
            result.put("collectRealTime", rSet.getString("ICALLPKGNAME"));
            result.put("issuedRealTime", rSet.getString("ICALLVERSIONDES"));
            result.put("upJhTime", rSet.getString("ICALLSTARTTIMES"));
            result.put("expectInfo", rSet.getString("IEXPECEINFO"));
            result.put("realInfo", rSet.getString("ICALLINSTANCENAME"));
            result.put("isCollectAlarm", rSet.getLong("IOLDIIP"));
            result.put("isIssuedAlarm", rSet.getLong("IAPPFLAG"));
            result.put("isDealAlarm", rSet.getLong("IREMARK"));
            result.put("isExpectAlarm", (rSet.getLong("IPROXYIP")));
            result.put("planTime", stringToLong(rSet.getString("PLANTIME")));
            result.put("ipAddress", rSet.getString("IIP"));
            result.put("stepNum", rSet.getString("ISERNER"));
            result.put("upNum", rSet.getString("IPARAMETER"));
            result.put("runInsId", rSet.getString("IRUNINSID"));
            result.put("state", rSet.getString("ISTATE"));
        } catch (Exception e) {
            log.error(method, e);
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }
        return result;
    }
    /**
     * 字符串转int
     *
     * @param num
     * @return
     */
    private Long stringToLong(String num) {
        Long ru = 0L;
        try {
            if (com.ideal.ieai.server.jobscheduling.repository.home.StringUtils.isNotBlank(num)) {
                ru = Long.parseLong(num);
            }
        } catch (NumberFormatException ne) {
            log.error("sendDelayAlarmInfo for string to conversion int is error: " + ne.getMessage());
        }
        return ru;
    }
    public int timeToStrings(String starttime,String endtime){

        int startzong = 0;
        int endzong = 0;
        if(starttime!=null && !starttime.equals("")){
            String[] my =starttime.split(":");

            int hour =Integer.parseInt(my[0]);

            int min =Integer.parseInt(my[1]);

            int sec =Integer.parseInt(my[2]);

            startzong =hour*3600+min*60+sec;//总秒
        }

        if(endtime!=null && !endtime.equals("")) {

            String[] endmy =endtime.split(":");

            int endhour =Integer.parseInt(endmy[0]);

            int endmin =Integer.parseInt(endmy[1]);

            int endsec =Integer.parseInt(endmy[2]);

            endzong =endhour*3600+endmin*60+endsec;//总秒
        }

        int zong  = endzong-startzong;

        return zong;
    }


    private String timeToString ( long time )
    {
        String str = "";
        if (time > 3600)
        {
            str = time / 3600 + "小时";
            if (time % 3600 > 60)
            {
                str = str + (time % 3600) / 60 + "分" + time % 60 + "秒";
            } else
            {
                str = str + time % 60 + "秒";
            }
        } else
        {
            if (time > 60)
            {
                str = time / 60 + "分" + time % 60 + "秒";
            } else
            {
                if(time<0){//负数转成时分秒
                    time = Math.abs(time);
                    str = "-" + timeToString(time);
                }else{
                    str = time + "秒";
                }
            }
        }
        return str;
    }

    public Map<String, String> getCellStateDetail (String iruninsid )
    {
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        Connection con = null;
        String runstate = "";
        LinkedHashMap<String, String> premap = new LinkedHashMap<String, String>();
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
        } catch (DBException e1)
        {
            log.error(e1);
        }
        String sql = "select iid,istate,IISFAIL,IREMARK,IOLDIIP,IAPPFLAG,IPROXYIP,ICALLPKGNAME,ICALLVERSIONDES,ICALLINSTANCENAME,IEXPECEINFO,IPKGNAME,IREDOABLE,IMODELTYPE,IRETNVALEXCEPION,KEYSTEPS,(select istate from ((SELECT "+RUN_FIELDS+" FROM ieai_run_instance WHERE isystype = 52 ) UNION ALL ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his WHERE isystype = 52 ))WHERE iid = D .iruninsid) state " +
                "from ((select iid,istate,IISFAIL,IREMARK,IOLDIIP,IAPPFLAG,IPROXYIP,ICALLPKGNAME,ICALLVERSIONDES,ICALLINSTANCENAME,IEXPECEINFO,IPKGNAME,IREDOABLE,IMODELTYPE,IRETNVALEXCEPION,KEYSTEPS,IRUNINSID from ieai_runinfo_instance  where  isystype = 52  and  IRUNINSID = ?) union all" +
                " (select iid,istate,IISFAIL,IREMARK,IOLDIIP,IAPPFLAG,IPROXYIP,ICALLPKGNAME,ICALLVERSIONDES,ICALLINSTANCENAME,IEXPECEINFO,IPKGNAME,IREDOABLE,IMODELTYPE,IRETNVALEXCEPION,KEYSTEPS,IRUNINSID from ieai_runinfo_instance_his  where isystype = 52 and  IRUNINSID = ?) )d ";

        try
        {
            pstmt = con.prepareStatement(sql);
            pstmt.setString(1,iruninsid);
            pstmt.setString(2,iruninsid);
            rs = pstmt.executeQuery();
            while (rs.next())
            {
                String tCell = rs.getString(1);
                long zt = rs.getLong("KEYSTEPS");//是否终态
                String keysteps = rs.getString("KEYSTEPS");
                if(keysteps==null || "".equals(keysteps)){
                    keysteps = "-1";
                }
                long state =  0;
                long istate = rs.getLong("state");
                if(rs.getLong("iremark")  >0 || rs.getLong("ioldiip")   >0  ||
                        rs.getLong("iappflag")  >0  || rs.getLong("iproxyip")  >0  || rs.getLong("iisfail")>0){//接收/处理/下发延时/异常推送过告警
                    if(zt>0 || istate==2){//终态并且未终止
                        state = 3;//状态为异常
                    }else{
                        state = 4;//状态为运行中异常
                    }
                }else{
                    if(rs.getString("ICALLPKGNAME")==null &&  istate!=2){//运行开始时间为空，且无异常，状态为未运行 非终止
                        state = 1;//状态为未运行
                    }else if(rs.getString("ICALLVERSIONDES")==null &&  istate!=2){//运行开始时间不为空，运行结束时间为空，且下级无异常步骤，状态为运行中 非终止
                        state = 0;//状态为运行中
                    }else if(rs.getString("ICALLPKGNAME")==null &&  istate==2){
                        state = 3;//状态为异常
                    }else if(rs.getString("ICALLVERSIONDES")==null &&  istate==2){
                        state = 3;//状态为异常
                    }else{
                        state = 2;//状态为已完成  运行结束时间不为空，且无异常；
                    }
                }

                String startTime = rs.getString("ICALLPKGNAME")==null?"":"1";
                String endTime = rs.getString("ICALLVERSIONDES")==null?"":"1";
                String iisfail = rs.getString("IISFAIL")==null?"":rs.getString("IISFAIL");
                long isDealAlarm = rs.getLong("IREMARK");
                long isCollectAlarm = rs.getLong("IOLDIIP");
                long isIssuedAlarm = rs.getLong("IAPPFLAG");
                long isExpectAlarm = rs.getLong("IPROXYIP");
                if ("".equals(runstate))
                {
                    runstate = tCell + ":" + state + ":"+keysteps+ ":" + startTime+ ":"+endTime+ ":" + iisfail + ":" + isDealAlarm + ":"+ isCollectAlarm + ":" + isIssuedAlarm + ":" + isExpectAlarm + ",";
                } else
                {
                    runstate = runstate + tCell  + ":" + state + ":" + keysteps + ":" + startTime+ ":" + endTime + ":" + iisfail + ":" + isDealAlarm + ":" + isCollectAlarm + ":" + isIssuedAlarm + ":" + isExpectAlarm + ",";
                }
            }
            premap.put("runstate", runstate);
        } catch (SQLException e)
        {
            log.error("SubmittedReportScreenManager.getCellStateDetail" + e.getMessage());
        } finally
        {
            DBResource.closeConn(con, rs, pstmt, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return premap;
    }

    public Map<String, Object> getNameById (String iid)
    {
        String method = Thread.currentThread().getStackTrace()[1].getMethodName();
        Map<String, Object> map = new HashMap<String, Object>();
        PreparedStatement ps = null;
        ResultSet rs = null;
        String sql = "";
        Connection conn = null;
        String str = "";
        String bm = "";
        try
        {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
            sql = "select "+RUN_FIELDS+" from ((select "+RUN_FIELDS+" from IEAI_RUN_INSTANCE_HIS  where  isystype = 52  and  iid = ?) union all (select "+RUN_FIELDS+" from IEAI_RUN_INSTANCE  where isystype = 52 and  iid = ?) )d ";
            ps = conn.prepareStatement(sql);
            ps.setLong(1, Long.valueOf(iid));
            ps.setLong(2, Long.valueOf(iid));
            rs = ps.executeQuery();
            while (rs.next())
            {
                str = rs.getString("IRUNINSNAME");//报送类型
                bm = rs.getString("IVERSIONNUM");//编码
            }
            map.put("iruninsname",str);
            map.put("iversionnum",bm);
        } catch (Exception e)
        {
            log.error(method, e);
        } finally
        {
            DBResource.closeConn(conn, rs, ps, method, log);
        }
        return map;
    }

    /**
     * 查询xml数据
     * @param iversionnum
     * @return
     */
    public JSONObject getSubmittedXml(String iversionnum,String queryDate,String queryTime){
        Connection conn = null;
        PreparedStatement ps = null;
        PreparedStatement ps1 = null;
        ResultSet rs = null;
        ResultSet rs1 = null;
        JSONObject result = new JSONObject();
        String iid = "";
        try {
            conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
            String sql1 = "select max(iid) iid from ((select "+RUN_FIELDS+" from IEAI_RUN_INSTANCE_HIS  where  isystype = 52  and  iversionnum=? and (iinsdes=? or iinsdes=?)) union all (select "+RUN_FIELDS+" from IEAI_RUN_INSTANCE  where isystype = 52 and  iversionnum=? and  (iinsdes=? or iinsdes=?)) )";
            String sql = "SELECT TASK_XML FROM IEAI_RUNXML_HIS WHERE IRUNINSID=?";

            ps1 = conn.prepareStatement(sql1);
            ps1.setString(1, iversionnum);
            ps1.setString(2, queryDate);
            ps1.setString(3, queryTime);
            ps1.setString(4, iversionnum);
            ps1.setString(5, queryDate);
            ps1.setString(6, queryTime);
            rs1 = ps1.executeQuery();
            while (rs1.next()) {
                iid = rs1.getString("iid");
            }
            if(StringUtils.isNotBlank(iid)){
                ps = conn.prepareStatement(sql);
                ps.setLong(1, Long.valueOf(iid));
                rs = ps.executeQuery();
                while (rs.next()) {
                    result.put("xml",rs.getString("TASK_XML"));
                }
            }else{
                result.put("xml","");
            }
            result.put("iid",iid);
        } catch (Exception e) {
            log.error("SubmittedReportScreenManager.class for getSubmittedXml is error:" + e.getMessage());
        } finally {
            DBResource.closeConn(conn, rs, ps, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
            DBResource.closePSRS(rs1, ps1, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return result;
    }

    /**
     * queryTopoCellData:取得cell系统信息. <br/>
     *
     * <AUTHOR> @return Map
     * @return
     * @since JDK 1.4
     */
    public Map<String, String> getReportTypeCellInfo ( String cellId )
    {
        // 鼠标放上查询节点纤细信息
        String sql = "  select  A.IID,A.ISYSID,A.ISYSNAME,A.IVERSIONNUM,A.IRUNINSNAME FROM ( SELECT "+RUN_FIELDS+" FROM ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance_his ) UNION ALL ( SELECT "+RUN_FIELDS+" FROM ieai_run_instance)) A WHERE A.IID = ?   AND isystype = 52";
        Map<String, String> atMap = new HashMap<String, String>();
        Connection con = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try
        {
            con = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMS);
        } catch (DBException e1)
        {
            log.error(e1);
        }
        try
        {

            pstmt = con.prepareStatement(sql);
            pstmt.setString(1, cellId);
            rs = pstmt.executeQuery();
            String info = "";
            while (rs.next())
            {
                String iversionnum = rs.getString("IVERSIONNUM");
                String iruninsname = rs.getString("IRUNINSNAME") == null ? "" : rs.getString("IRUNINSNAME");
                info += "类型编码：" + iversionnum + "\n";
                info += "报送类型：" + iruninsname + "\n";
            }
            atMap.put("cell", info);
        } catch (Exception e)
        {
            log.error("error when exec sql in getReportTypeCellInfo!" + e.getMessage());
        } finally
        {
            DBResource.closeConn(con, rs, pstmt, Thread.currentThread().getStackTrace()[1].getMethodName(), log);
        }
        return atMap;
    }


}
