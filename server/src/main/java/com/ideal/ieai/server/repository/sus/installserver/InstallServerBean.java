package com.ideal.ieai.server.repository.sus.installserver;

public class InstallServerBean
{
    private long iid =-1;
    private String ipAddress;
    private String userName;
    private String passWord;
    private String serverType;
    private String timeOut;
    private int installStatus;
    private String dhcp;    //执行jar包后的返回值
    

    private String checkini_rs_ip;
    private String checkini_rs_mac;
    private String iniFilePath=""; //该值，从数据库中查询出来
    public String getIpAddress ()
    {
        return ipAddress;
    }
    public void setIpAddress ( String ipAddress )
    {
        this.ipAddress = ipAddress;
    }
    public String getUserName ()
    {
        return userName;
    }
    public void setUserName ( String userName )
    {
        this.userName = userName;
    }
    public String getPassWord ()
    {
        return passWord;
    }
    public void setPassWord ( String passWord )
    {
        this.passWord = passWord;
    }
    public String getserverType ()
    {
        return serverType;
    }
    public void setserverType ( String serverType )
    {
        this.serverType = serverType;
    }
    public String getTimeOut ()
    {
        return timeOut;
    }
    public void setTimeOut ( String timeOut )
    {
        this.timeOut = timeOut;
    }
    public int getInstallStatus ()
    {
        return installStatus;
    }
    public void setInstallStatus ( int installStatus )
    {
        this.installStatus = installStatus;
    }
    public long getIid ()
    {
        return iid;
    }
    public void setIid ( long iid )
    {
        this.iid = iid;
    }
    public String getDhcp ()
    {
        return dhcp;
    }
    public void setDhcp ( String dhcp )
    {
        this.dhcp = dhcp;
    }
    
    public String getServerType ()
    {
        return serverType;
    }
    public void setServerType ( String serverType )
    {
        this.serverType = serverType;
    }
    public String getCheckini_rs_ip ()
    {
        return checkini_rs_ip;
    }
    public void setCheckini_rs_ip ( String checkini_rs_ip )
    {
        this.checkini_rs_ip = checkini_rs_ip;
    }
    public String getCheckini_rs_mac ()
    {
        return checkini_rs_mac;
    }
    public void setCheckini_rs_mac ( String checkini_rs_mac )
    {
        this.checkini_rs_mac = checkini_rs_mac;
    }
    public String getIniFilePath ()
    {
        return iniFilePath;
    }
    public void setIniFilePath ( String iniFilePath )
    {
        this.iniFilePath = iniFilePath;
    }
}
