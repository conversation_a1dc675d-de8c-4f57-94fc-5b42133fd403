\set SQLTERM /
CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE PROC_MV_RUN_INSTANCE(WORKID IN NUMBER,AN_OPT_ID OUT NUMBER) AS
 BEGIN
   DECLARE
        LV_SQL		VARCHAR2(4000);
   BEGIN
		
		INSERT INTO IEAI_RUNINFO_INSTANCE_HIS(IID,<PERSON><PERSON><PERSON>IN<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,IRUN<PERSON>SNAM<PERSON>,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,<PERSON><PERSON><PERSON><PERSON><PERSON>,IPA<PERSON>S<PERSON>TCH,<PERSON><PERSON><PERSON>WITCHFORCE,ISTATE,IISF<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,IENDTIME,IEXECUSER,ISH<PERSON>LSCRIPT,I<PERSON>R<PERSON><PERSON>AG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABL<PERSON>,<PERSON>K<PERSON>NAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPH<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>AME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ISRTO,IINFORTO,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME,IEXECUTOR,IMESSAGEREMIND,ICICDSTEPID,ISCRIPTTYPE,ISONAME,ISTEPIUUID,ISCRIPTMODULETYPE,ISCRIPTMODULECHILDTYPE,ISTAGETYPE,IEXECFAILED,IHOSTNAME,ICICDCONFIGFILE,IPRIORITY,ICICDEXECBATCHNUM,IEXECPERMISSION ) SELECT IID,IRUNINSID,IFLOWID,IRUNINSNAME,ISERNER,ICONNER,IPRENER,IACTNAME,IACTDES,IACTTYPE,IREMINFO,IIP,IPORT,ISYSTYPE,ISHELLPATH,ITIMEOUT,ISWITCHSYSTYPE,IPARAMETER,IPARACHECK,IPARASWITCH,IPARASWITCHFORCE,ISTATE,IISFAIL,ISTARTTIME,IENDTIME,IEXECUSER,ISHELLSCRIPT,IRERUNFLAG,IEXPECEINFO,IISLOADENV,ICONNERNAME,ICENTER, IMODELTYPE,IRETNVALEXCEPION, IREDOABLE,IPKGNAME,ITYPE,ICHILDINSTANCEID,IMODELNAME,IMODELVERSION,ISCRIPTID ,IMXGRAPHID,IPRESYSNAME,IPREACTNAME,ISYSTEMTYPESTEP,IMODELTYPESTEP,IAPPFLAG,INOT_ALLOWED_EXEC,SINGLEROLLBACK,IDISABLE,IBRANCH,ISPACEIID,IGOURPIID,IOWNERTYPE,IOWNER,IOLDIIP,IEXPBEGINTIME, IEXPENDTIME, IREMARK,IPROJECTNAME,IARRIVETIME,IISMANUAL,IREVIEWER,IBANKNAME,IREVIEWSTATE,ISELFHEALING,ISROLLBACK,IJUDGEISBACK,NETEQUIIP,ISRTO,IINFORTO,ICALLINSTANCENAME,ICALLPKGNAME,ICALLVERSIONDES,ICALLSTARTTIMES,ICALLSTARTENVNAMES,ICALLSTARTTIMESL,ICALLITASKID,ICALLIERRCLOBID,IROLLBACKSCHEME,IAZNAME,IPROXYIP,IPROBLEM,MAINDEPARTMENT,BUSINESSPERSONNEL,ARTISAN,KEYSTEPS,PLANTIME,IEXECUTOR,IMESSAGEREMIND ,ICICDSTEPID,ISCRIPTTYPE,ISONAME,ISTEPIUUID,ISCRIPTMODULETYPE,ISCRIPTMODULECHILDTYPE,ISTAGETYPE,IEXECFAILED,IHOSTNAME,ICICDCONFIGFILE,IPRIORITY,ICICDEXECBATCHNUM,IEXECPERMISSION  FROM IEAI_RUNINFO_INSTANCE WHERE IRUNINSID = WORKID;
		INSERT INTO IEAI_RUN_INSTANCE_HIS(IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID,ICHANGETYPE,IFATHER,IPROJECT_NUM,ICICDTASKTYPE,IREVIEWEDBY,IDISPLAYGROUPID ) SELECT IID,ISYSCLASSID, ISYSID,ISYSNAME,IRUNINSNAME, IINSDES,ISTARTTIME, IENDTIME,ISTATE,IISFAIL,ISTARTUSERID,ISTARTUSER,ISERVERIP,ISYSTYPE,IVERSIONNUM,IVERSION,IVERSIONDES,ISWITCHTO,IINSTANCETYPE,IMAXGRAPHID,IISMAININS,IBUSNES_SYS_IID,IDEPLOYIID,ISBACKINSTANCE,ITASKID,MODELNAME,ISCONFIRM,IMXGRAPHID,IENVIDS,IWORKITEMID,IAPMTYPE,IORDERNUM,ISCHECKFLOW,IINSTANCETYPE2,IBUTTERFLYVERSION,IAZTYPE,IPMPORDER,IXMLID,IBGTYPE,ICQTASKID,ICHANGETYPE,IFATHER,IPROJECT_NUM,ICICDTASKTYPE,IREVIEWEDBY,IDISPLAYGROUPID  FROM IEAI_RUN_INSTANCE WHERE IID = WORKID;
    -- 系统变更增加
    INSERT INTO IEAI_RUNINFO_INSTANCE_HIS_IPS(IID, IINSTANCEID, INSTANCEINFO_ID, IPS, PORTS, REMARK) SELECT IID, IINSTANCEID, INSTANCEINFO_ID, IPS, PORTS, REMARK FROM IEAI_RUNINFO_INSTANCE_IPS WHERE IINSTANCEID IN (SELECT ISYSID FROM IEAI_RUN_INSTANCE WHERE ISYSTYPE=51 AND IID=WORKID);
		DELETE FROM IEAI_RUNINFO_INSTANCE WHERE IRUNINSID = WORKID;
		DELETE FROM IEAI_RUN_INSTANCE WHERE IID = WORKID;
   END;
END PROC_MV_RUN_INSTANCE;