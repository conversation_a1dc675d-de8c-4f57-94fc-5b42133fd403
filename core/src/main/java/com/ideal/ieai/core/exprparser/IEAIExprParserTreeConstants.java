/* Generated By:JJTree: Do not edit this line. IEAIExprParserTreeConstants.java */

package com.ideal.ieai.core.exprparser;

public interface IEAIExprParserTreeConstants
{
  public int JJTVOID = 0;
  public int JJTARRAYINITIALIZER = 1;
  public int JJTTYPE = 2;
  public int JJTPRIMITIVETYPE = 3;
  public int JJTAMBIGUOUSNAME = 4;
  public int JJTTERNARYEXPRESSION = 5;
  public int JJTBINARYEXPRESSION = 6;
  public int JJTUNARYEXPRESSION = 7;
  public int JJTCASTEXPRESSION = 8;
  public int JJTPRIMARYEXPRESSION = 9;
  public int JJTMETHODINVOCATION = 10;
  public int JJTPRIMARYSUFFIX = 11;
  public int JJTLITERAL = 12;
  public int JJTARGUMENTS = 13;
  public int JJTALLOCATIONEXPRESSION = 14;
  public int JJTARRAYDIMENSIONS = 15;


  public String[] jjtNodeName = {
    "void",
    "ArrayInitializer",
    "Type",
    "PrimitiveType",
    "AmbiguousName",
    "TernaryExpression",
    "BinaryExpression",
    "UnaryExpression",
    "CastExpression",
    "PrimaryExpression",
    "MethodInvocation",
    "PrimarySuffix",
    "Literal",
    "Arguments",
    "AllocationExpression",
    "ArrayDimensions",
  };
}
