//alert(11);
/**
 * js说明
 * isOperDesc 为开启操作说明的开关
 */
var utHomeForm = null;
var operatePanel = null;
var operCode = -1;
var json = null;
var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
var explain = "";
Ext.onReady(function() {
	var etInfoVariable = {};
	
	destroyRubbish();
	Ext.tip.QuickTipManager.init();
	//reloadGroup();
	Ext.define ('utRecordModel',{
		extend : 'Ext.data.Model',
		fields : [ { name : 'taskId', 	type : 'long' },
		           { name : 'actId', 	type : 'long' },
		           { name : 'taskName', type : 'string' },
		           { name : 'flowId', type : 'string' },
		           { name : 'priority', type : 'string' },
		           { name : 'state', type : 'string' },
		           { name : 'taskOwner', type : 'string' },
		           { name : 'acquireTime', type : 'string' },
		           { name : 'readyTime', type : 'string' },
		           { name : 'description', type : 'string' },
		           { name : 'remark', type : 'string' },
		           { name : 'remarkLog', type : 'string' },
		           { name : 'agentIP', type : 'string' },
		           { name : 'agentPort', type : 'string' },
		           { name : 'shellName', type : 'string' },
		           { name : 'shellPath', type : 'string' }
		]
	});
	/** *********************Store********************* */
	/** 任务列表数据源* */
	var actId;
	var utRecordStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    groupField : 'istate',
	    model : 'utRecordModel',
	    pageSize : 6,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'etRecord.do',// utLogInfoRecord.do
	        extraParams:{sysType:sysType,state:state,errorTaskId:taskId}
	    }
	});
	
	/** 一体化运维******* 查询Shell历史信息的js代码 by yue_sun on 2018-03-16 start **/
	Ext.define('optHistory', {
		extend : 'Ext.data.Model',
		fields : [{
			name : 'time',
			type : 'string'
		}, {
			name : 'user',
			type : 'string'
		}, {
			name : 'operation',
			type : 'string'
		}, {
			name : 'description',
			type : 'string'
		} ]
	});
	var optHistoryStore = Ext.create('Ext.data.Store', {
		autoLoad : true,
		autoDestroy : true,
		model : 'optHistory',
		pageSize : 25,
		proxy : {
			type : 'ajax',
			url : 'etInfo_optHisory.do',
			reader : {
				type : 'json',
				root : 'dataList',
				totalProperty : 'total'
			},
			extraParams:{
				sysType : sysType,
				errorTaskId : taskId,
				state : state,
				flowId : 0,
				actId : 0,
				timeOut:timeOut
			}
		}
	});
	optHistoryStore.on('beforeload', function(store, options) {
		var newparams = {
				sysType : sysType,
				errorTaskId : taskId,
				state : state,
				flowId : 0,
				actId : 0,
				timeOut:timeOut
		};
		Ext.apply(store.proxy.extraParams, newparams);
	});
	var optHistoryPageBar = Ext.create('Ext.PagingToolbar', {
		store : optHistoryStore,
		dock : 'bottom',
		displayInfo : true
	});	 
	var  optHistoryPanel = Ext.create('Ext.grid.Panel', {
	    region : 'center',
	    split: true,
	    multiSelect: true,
	    emptyText: '没有操作历史',
	    columnLines : true,
	    width : contentPanel.getWidth() - 364,
		height : contentPanel.getHeight() - 165,
	    store: optHistoryStore,
	    columns: [
	      {text: '序号', xtype: 'rownumberer', width: 40},
	      {text: '时间', dataIndex: 'time', flex: 1, sortable: true},
	      {text: '操作人', dataIndex: 'user', flex: 1, sortable: true},
	      {text: '操作', dataIndex: 'operation', flex: 1, sortable: true},
	      {text: '详细说明', dataIndex: 'description', width:280, sortable: true} 
	    ]/*,
	    bbar: optHistoryPageBar*/
	  });
	
	var actStdError = Ext.create ('Ext.form.field.TextArea',
			{
				fieldLabel : '活动错误输出',
				width : 820,
				height : 180,
				name : 'actStdError' 
			});
	
	
	var shellInfoPanel = Ext.create('Ext.form.Panel', {
		    region: 'center',
    		border : false,
			buttonAlign : 'right',
			margin:'5',
			width : contentPanel.getWidth()*4/6,
			height : '100%',
//			width : '100%',
//			height : '100%',
	  	    layout: 'form',
	  	    dockedItems : [{
				xtype : 'toolbar',
    			border : false,
				dock : 'top',
	   	  		items:[{
					fieldLabel : '活动返回值',
					xtype : 'textfield',
					name : 'actRet',
					labelAlign : 'right',
		            labelWidth : 93,
		            width : '100%'
				}]
	  	    },{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
	  			items:[{
					fieldLabel : '活动末行输出',
					xtype : 'textfield',
					name : 'actLastline',
					labelAlign : 'right',
		            labelWidth : 93,
		            width : '100%'
				}]
	  	    },actStdError
//	  	     {
//				xtype : 'toolbar',
//				border : false,
//				dock : 'top',
//	  			items:[{
//				fieldLabel : '活动错误输出 ',
//				name : 'actStdError',
//				//xtype : 'textarea',
//				//height : 100,
//					height : 220,
//					labelAlign : 'right',
//		            labelWidth : 93,
//		            width : '100%',
//		            enableFont : false,  //隐藏或显示字体选项
//					enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
//					enableFormat : false,   //允许字体加粗
//					enableLinks : false,  //启用链接创建按钮。
//					enableSourceEdit : false,  //允许切换到源码编辑按钮。
//					enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
//					enableColors : false,  //启用回退/高亮颜色按钮
//					//enableSourceEdit : false, //启用切换到源编辑按钮。
//					enableLists : false, //启用bullet和有限数量的按钮列表。
//					readOnly : true,
//		            xtype: 'htmleditor'
//				}]
//	  	    } 
	  	    ,{
				xtype : 'toolbar',
				border : false,
				dock : 'top',
	  			items:[{
				fieldLabel : '活动详细输出',
				name : 'actStdOut',
				//xtype : 'textarea',
					labelAlign : 'right',
		            labelWidth : 93,
		            width : '100%',
		            height : contentPanel.getHeight()-485,
		            enableFont : false,  //隐藏或显示字体选项
					enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
					enableFormat : false,   //允许字体加粗
					enableLinks : false,  //启用链接创建按钮。
					enableSourceEdit : false,  //允许切换到源码编辑按钮。
					enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
					enableColors : false,  //启用回退/高亮颜色按钮
					//enableSourceEdit : false, //启用切换到源编辑按钮。
					enableLists : false, //启用bullet和有限数量的按钮列表。
					readOnly : true,
		            xtype: 'htmleditor'
			}]
	    }]
	});
	
	function getShellInfoData(flowId, actId) {
		Ext.Ajax.request({
			url : 'showActInfo.do',
			params : {
				sysType : sysType,
				flowId : flowId,
				id : actId
			},
			success : function(response, opts) {
				var msg = Ext.decode(response.responseText);
				 json = null;
				 json = {
				 actRet : msg.ret,
				 actLastline : msg.lastLine,
				 actStdOut : msg.stdout,
				 actStdError : msg.stderr
				 };
				 shellInfoPanel.getForm().findField("actRet").setValue(msg.ret);
				 shellInfoPanel.getForm().findField("actLastline").setValue(msg.lastLine);
				 shellInfoPanel.getForm().findField("actStdOut").setValue(msg.stdout);
				 shellInfoPanel.getForm().findField("actStdError").setValue(msg.stderr);
				 
			},
			failure : function(response, opts) {
			}

		});
	}
	//获取shellU或shellcmd执行信息
	getShellInfoData(flowid,actid);	
	
	/** 一体化运维******* 查询Shell历史信息的js代码 by yue_sun on 2018-03-16 end **/
	
	var retryBtn = Ext.widget('aomsBtn',{id : 'retryBtn',text:'重试',handler:flowDoubleCheckRetry});
	var skipBtn = Ext.widget('aomsBtn',{id : 'skipBtn',text:'略过', handler:flowDoubleCheckSkip});
	var waitTimeText = Ext.widget('textfield',{id : 'waitTimeText',labelAlign : 'right',fieldLabel:'继续等待时间',labelWidth:90});
	var continueBtn = Ext.widget('aomsBtn',{id : 'continueBtn',text:'继续执行',handler:continueFlow});
	var killflowBtn = false;
    if(killflowBtnSwitch=='true'){
    	killflowBtn = true;
    }
	var stopBtn = Ext.widget('aomsBtn',{id : 'stopBtn',text:'终止工作流',handler:stopFlow,hidden:killflowBtn});
	
	
	
	//var isOpera = userAgent.indexOf("Opera") > -1;
	var remarkHeight = null;
	if (userAgent.indexOf("Firefox") > -1) {
		if(pageType == '0'){  //|| state == 'Running' || state == 'Continue'
			if(isOperDesc == "true"){
				remarkHeight = contentPanel.getHeight() * 0.15;
			} else {
				remarkHeight = contentPanel.getHeight() * 0.22;
			}
		} else {
			if(state == 'Running') {
				remarkHeight = contentPanel.getHeight() * 0.11;
			} else {
				remarkHeight = contentPanel.getHeight() * 0.19;
			}
		}
	} /*else if(userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) {
		if(pageType == '0'){
			remarkHeight = contentPanel.getHeight() * 0.21;
		} else {
			if(state == 'Running') {
				remarkHeight = contentPanel.getHeight() * 0.15;
			} else {
				remarkHeight = contentPanel.getHeight() * 0.25;
			}
		}
	} */else {
		if(pageType == '0'){
			remarkHeight = contentPanel.getHeight() * 0.21;
		} else {
			if(state == 'Running') {
				remarkHeight = contentPanel.getHeight() * 0.15;
			} else {
				remarkHeight = contentPanel.getHeight() * 0.25;
			}
		}
	}
	var actOutPut = Ext.create ('Ext.form.field.TextArea',
			{
				fieldLabel : '异常日志',
				width : 820,
				height : 180,
				id:'remark',
				padding : '5 5 5 5',
				name : 'actStdOut'
			});
	console.log(pageType);
	console.log(timeOut);
	console.log(isOperDesc);
	//当pageType为0时，则证明为活动监控活动名称点开，才可进行操作
	if(pageType == '0'){
		if(timeOut=='true'){
			//operatePanel = Ext.widget('panel',{border : false,layout:{type:'hbox',align:'middle',pack:'end'},items:[waitTimeText,continueBtn,stopBtn]});
			if(isOperDesc == "true"){
				utHomeForm = Ext.create('Ext.form.Panel', {
				    region: 'north',
				    //renderTo : divid,
			    	border : false,
					buttonAlign : 'right',
					width : contentPanel.getWidth()*4/6,
					height : '100%',
			  	    layout: 'form',
			  	    dockedItems : [{
							xtype : 'toolbar',
				    	border : false,
							dock : 'top',
			   	  		items:[{
				    		id:"taskName",
				            fieldLabel: '异常活动名称',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '100%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:"taskId",
				            fieldLabel: '异常标识',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'priority',
				            fieldLabel: '优先级',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'state',
				            fieldLabel: '状态',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'readyTime',
				            fieldLabel: '异常产生时间',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
					        	id:'iFlowId',
				        	fieldLabel: '工作流ID',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'description',
				        	fieldLabel: '描述',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'explain',
				            fieldLabel: '操作说明',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 70,
				            width : '100%',
				            xtype: 'textarea'
					    }]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
							id : 'remarkLog',
							fieldLabel : '备注',
							labelAlign : 'right',
							labelWidth : 90,
							height : 70,
							//padding : '0 0 0 5',
							enableFont : false,  //隐藏或显示字体选项
							enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
							enableFormat : false,   //允许字体加粗
							enableLinks : false,  //启用链接创建按钮。
							enableSourceEdit : false,  //允许切换到源码编辑按钮。
							enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
							enableColors : false,  //启用回退/高亮颜色按钮
							//enableSourceEdit : false, //启用切换到源编辑按钮。
							enableLists : false, //启用bullet和有限数量的按钮列表。
							width : '100%',
							xtype : 'htmleditor'
						}]
						},actOutPut],
					buttons : [waitTimeText, continueBtn, skipBtn, stopBtn]
				});
			} else {
				utHomeForm = Ext.create('Ext.form.Panel', {
				    region: 'center',
				    //renderTo : divid,
			    	border : false,
					buttonAlign : 'right',
					width : contentPanel.getWidth()*4/6,
					height : '100%',
			  	    layout: 'form',
			  	    dockedItems : [{
							xtype : 'toolbar',
				    	border : false,
							dock : 'top',
			   	  		items:[{
				    		id:"taskName",
				            fieldLabel: '异常活动名称',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '100%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:"taskId",
				            fieldLabel: '异常标识',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'priority',
				            fieldLabel: '优先级',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'state',
				            fieldLabel: '状态',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'readyTime',
				            fieldLabel: '异常产生时间',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
					        	id:'iFlowId',
				        	fieldLabel: '工作流ID',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'description',
				        	fieldLabel: '描述',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'shellname',
				        	fieldLabel: 'shell名称',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'shellpath',
				        	fieldLabel: 'shell路径',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'agentip',
				        	fieldLabel: 'agentIP',
				        	hidden:jobSchedulingQuerySystemSwitch?true:false,
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'agentport',
				        	fieldLabel: 'agent端口',
				        	hidden:jobSchedulingQuerySystemSwitch?true:false,
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
			  	    },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
							id : 'remarkLog',
							fieldLabel : '备注',
							labelAlign : 'right',
							labelWidth : 90,
							height : 120,
							//padding : '0 0 0 5',
							enableFont : false,  //隐藏或显示字体选项
							enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
							enableFormat : false,   //允许字体加粗
							enableLinks : false,  //启用链接创建按钮。
							enableSourceEdit : false,  //允许切换到源码编辑按钮。
							enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
							enableColors : false,  //启用回退/高亮颜色按钮
							//enableSourceEdit : false, //启用切换到源编辑按钮。
							enableLists : false, //启用bullet和有限数量的按钮列表。
							width : '100%',
							xtype : 'htmleditor'
						}]
						},actOutPut],
				        buttons : [waitTimeText,continueBtn,skipBtn,stopBtn]
				});
			}
		}else{
			//operatePanel = Ext.widget('panel',{border : false,layout:{type:'hbox',align:'middle',pack:'end'},items:[retryBtn,skipBtn]});
			if(isOperDesc == "true"){
				utHomeForm = Ext.create('Ext.form.Panel', {
				    region: 'north',
				    //renderTo : divid,
			    	border : false,
					buttonAlign : 'right',
					width : contentPanel.getWidth()*4/6,
					height : '100%',
			  	    layout: 'form',
			  	    dockedItems : [{
							xtype : 'toolbar',
				    	border : false,
							dock : 'top',
			   	  		items:[{
				    		id:"taskName",
				            fieldLabel: '异常活动名称',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '100%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:"taskId",
				            fieldLabel: '异常标识',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'priority',
				            fieldLabel: '优先级',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'state',
				            fieldLabel: '状态',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'readyTime',
				            fieldLabel: '异常产生时间',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
					        	id:'iFlowId',
				        	fieldLabel: '工作流ID',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'description',
				        	fieldLabel: '描述',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'shellname',
				        	fieldLabel: 'shell名称',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'shellpath',
				        	fieldLabel: 'shell路径',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'agentip',
				        	fieldLabel: 'agentIP',
				        	hidden:jobSchedulingQuerySystemSwitch?true:false,
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'agentport',
				        	fieldLabel: 'agent端口',
				        	hidden:jobSchedulingQuerySystemSwitch?true:false,
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
			  	    },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
		   	         	id:'flowInstanceName',
			        	fieldLabel: '数据日期',
			        	labelAlign : 'right',
			        	hidden:!isJHSwitch,
			        	labelWidth : 90,
			        	height : 30,
			        	width : '50%',
			        	xtype: 'textfield'
			        }]
		  	    },{
					xtype : 'toolbar',
					border : false,
					dock : 'top',
	   	  			items:[{	
		   	  			
				        	id:'explain',
				            fieldLabel: '操作说明',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 70,
				            width : '100%',
				            xtype: 'textarea'
					    }]
					},{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
							id : 'remarkLog',
							fieldLabel : '备注',
							labelAlign : 'right',
							labelWidth : 90,
							height : 70,
							//padding : '0 0 0 5',
							enableFont : false,  //隐藏或显示字体选项
							enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
							enableFormat : false,   //允许字体加粗
							enableLinks : false,  //启用链接创建按钮。
							enableSourceEdit : false,  //允许切换到源码编辑按钮。
							enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
							enableColors : false,  //启用回退/高亮颜色按钮
							//enableSourceEdit : false, //启用切换到源编辑按钮。
							enableLists : false, //启用bullet和有限数量的按钮列表。
							width : '100%',
							xtype : 'htmleditor'
						}]
						},actOutPut],
			        buttons : [retryBtn,skipBtn]
				});
			} else {
				utHomeForm = Ext.create('Ext.form.Panel', {
				    region: 'center',
				    //renderTo : divid,
			    	border : false,
					buttonAlign : 'right',
					width : contentPanel.getWidth()*4/6,
					height : '100%',
			  	    layout: 'form',
			  	    dockedItems : [{
							xtype : 'toolbar',
				    	border : false,
							dock : 'top',
			   	  		items:[{
				    		id:"taskName",
				            fieldLabel: '异常活动名称',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '100%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:"taskId",
				            fieldLabel: '异常标识',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'priority',
				            fieldLabel: '优先级',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
				        	id:'state',
				            fieldLabel: '状态',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        },{
				        	id:'readyTime',
				            fieldLabel: '异常产生时间',
				            labelAlign : 'right',
				            labelWidth : 90,
				            height : 30,
				            width : '50%',
				            xtype: 'textfield'
				        }]
				        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
					        	id:'iFlowId',
				        	fieldLabel: '工作流ID',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        },{
				        	id:'description',
				        	fieldLabel: '描述',
				        	labelAlign : 'right',
				        	labelWidth : 90,
				        	height : 30,
				        	width : '50%',
				        	xtype: 'textfield'
				        }]
				        },actOutPut ],
				        buttons : [retryBtn,skipBtn]
				});
			}
		}
	} else {
		if(state == 'Running') {
			utHomeForm = Ext.create('Ext.form.Panel', {
			    region: 'north',
			    //renderTo : divid,
		    	border : false,
					buttonAlign : 'right',
					width : contentPanel.getWidth()*4/6,
					height : '100%',
			  	    layout: 'form',
			  	    dockedItems : [{
							xtype : 'toolbar',
			    	border : false,
							dock : 'top',
			   	  		items:[{
			    		id:"taskName",
			            fieldLabel: '异常活动名称',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '100%',
			            xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:"taskId",
			            fieldLabel: '异常标识',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '50%',
			            xtype: 'textfield'
			        },{
			        	id:'priority',
			            fieldLabel: '优先级',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '50%',
			            xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'state',
			            fieldLabel: '状态',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            //padding : '0 0 0 5',
				            width : '50%',
			            xtype: 'textfield'
			        },{
			        	id:'readyTime',
			            fieldLabel: '异常产生时间',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '50%',
			            xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'iFlowId',
			        	fieldLabel: '工作流ID',
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
			        },{
			        	id:'description',
			        	fieldLabel: '描述',
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'shellname',
			        	fieldLabel: 'shell名称',
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
			        },{
			        	id:'shellpath',
			        	fieldLabel: 'shell路径',
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'agentip',
			        	fieldLabel: 'agentIP',
			        	hidden:jobSchedulingQuerySystemSwitch?true:false,
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
			        },{
			        	id:'agentport',
			        	fieldLabel: 'agent端口',
			        	hidden:jobSchedulingQuerySystemSwitch?true:false,
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
				        }]
			  	    },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
						id : 'remarkLog',
						fieldLabel : '备注',
						labelAlign : 'right',
						labelWidth : 90,
						height : 90,
						//padding : '0 0 0 5',
						enableFont : false,  //隐藏或显示字体选项
						enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
						enableFormat : false,   //允许字体加粗
						enableLinks : false,  //启用链接创建按钮。
						enableSourceEdit : false,  //允许切换到源码编辑按钮。
						enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
						enableColors : false,  //启用回退/高亮颜色按钮
						//enableSourceEdit : false, //启用切换到源编辑按钮。
						enableLists : false, //启用bullet和有限数量的按钮列表。
						width : '100%',
						xtype : 'htmleditor'
						}]
					},actOutPut/*{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'remark',
			            fieldLabel: '异常日志',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : contentPanel.getHeight()-590,
			            width : '100%',
			            //xtype: 'textarea'
			            enableFont : false,  //隐藏或显示字体选项
						enableFontSize : false, //允许增大/缩小字号按钮(默认为 true)。
						enableFormat : false,   //允许字体加粗
						enableLinks : false,  //启用链接创建按钮。
						enableSourceEdit : false,  //允许切换到源码编辑按钮。
						enableAlignments : false, //启用左对齐、中间对齐、右对齐按钮
						enableColors : false,  //启用回退/高亮颜色按钮
						//enableSourceEdit : false, //启用切换到源编辑按钮。
						enableLists : false, //启用bullet和有限数量的按钮列表。
						readOnly : true,
			            xtype: 'htmleditor'
			        }]
			    }*/, {
					xtype : 'fieldcontainer',
					fieldLabel : '处理方式',
					labelAlign : 'right',
					items : [ {
						xtype : 'checkboxfield',
						boxLabel : '暂停',
						name : 'isPause',
						listeners:{
							change : function(ck, checked) {  
		                        if(checked){
		                        	operCode='4';
		                        }else{
		                        	operCode='-1';
		                        }
		                    }
				        }
					}]
				}],
				buttons : [ {
					text : '保存配置',
					handler : function() {
						var isPause = actPauseInfoForm.getForm().findField("isPause").getValue();
						//当操作类型都不选择时，提示选择操作类型
						if(operCode == '-1'){ 
							Ext.Msg.alert('提示', "请选择操作类型!");
						}
						
						Ext.Ajax.request({
							url : 'saveActInfo.do',
							params : {
								flowId : flowId,
								id : actId,
								isPause : isPause,
								operCode : operCode
							},
							success : function(response, opts) {
								var msg = Ext.decode(response.responseText);
								var json = {
									actName : msg.actName,
									actDesc : msg.actDesc,
									common : msg.actRemark,
									isPause : msg.isPause
								};
								Ext.getCmp('actPauseInfoForm').getForm().setValues(json);
								Ext.Msg.alert('提示', "保存成功!");
							},
							failure : function(response, opts) {
								Ext.Msg.alert('提示', "操作失败!");
							}
						});
					}
				} ]
			});
		} else {
			utHomeForm = Ext.create('Ext.form.Panel', {
				    region: 'center',
				    //renderTo : divid,
		    	border : false,
					buttonAlign : 'right',
					width : contentPanel.getWidth()*4/6,
					height : '100%',
			  	    layout: 'form',
			  	    dockedItems : [{
							xtype : 'toolbar',
			    	border : false,
							dock : 'top',
			   	  		items:[{
			    		id:"taskName",
			            fieldLabel: '异常活动名称',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '100%',
			            xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:"taskId",
			            fieldLabel: '异常标识',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '50%',
			            xtype: 'textfield'
			        },{
			        	id:'priority',
			            fieldLabel: '优先级',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '50%',
			            xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'state',
			            fieldLabel: '状态',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            //padding : '0 0 0 5',
				            width : '50%',
			            xtype: 'textfield'
			        },{
			        	id:'readyTime',
			            fieldLabel: '异常产生时间',
			            labelAlign : 'right',
			            labelWidth : 90,
			            height : 30,
				            width : '50%',
			            xtype: 'textfield'
				        }]
			        },{
						xtype : 'toolbar',
						border : false,
						dock : 'top',
		   	  			items:[{
			        	id:'iFlowId',
			        	fieldLabel: '工作流ID',
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
			        },{
			        	id:'description',
			        	fieldLabel: '描述',
			        	labelAlign : 'right',
			        	labelWidth : 90,
			        	height : 30,
				        	width : '50%',
			        	xtype: 'textfield'
				        }]
			        },actOutPut],
			        buttons : [retryBtn,skipBtn]
			});
		}
	}
	
	var tabPanel = Ext.create('Ext.tab.Panel', {
		items : [ {
			title : '任务信息',
			items : [ utHomeForm ]
		}, {
			title : '操作历史',
			items : [ /*testForm,*/optHistoryPanel ]
		}
		],
		  listeners:{
              'tabchange':function (t, n) {
                  if(t.activeTab.title == '活动详情'){
                	  actStdError.getEl().dom.innerHTML='&nbsp;&nbsp;&nbsp;活动错误输出:<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:0px; left:104px;bottom:0px;right:10px; width:87%;overflow:auto;white-space:pre-wrap;margin-top:10px;margin-left:3px;"><pre>'+actStdError.getValue()+'</pre></div>';
                  }
              }
          }
	});
	
	var workItemRecord_mainPanel = Ext.create ('Ext.panel.Panel',
			{
			    renderTo : "ut_grid",
			    width : '100%',
				height : '100%',
				autoScroll :true,
				layout : 'border',
			    border : false,
			    bodyPadding : 5,
			    bodyCls: 'service_platform_bodybg',
			    items : [tabPanel]
			});
	
	utRecordStore.on ('load', function (store, options, success)
	{
		myMask.hide ();
		var reader = store.getProxy().getReader();
		if (null!=reader.jsonData&&""!=reader.jsonData) {
			Ext.getCmp("taskId").setValue(reader.jsonData.taskId);
			Ext.getCmp("taskName").setValue(reader.jsonData.taskName);
			Ext.getCmp("state").setValue(reader.jsonData.state);
			Ext.getCmp("priority").setValue(reader.jsonData.priority);
			Ext.getCmp("remark").setValue(reader.jsonData.remark);
			
			Ext.getCmp("iFlowId").setValue(reader.jsonData.flowId);
			Ext.getCmp("readyTime").setValue(reader.jsonData.readyTime);
			Ext.getCmp("description").setValue(reader.jsonData.description);
		
			
			etInfoVariable.actDefName = reader.jsonData.actDefName;
			
			actId = reader.jsonData.actId;
			
			actOutPut.getEl().dom.innerHTML='&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp异常日志：<div style="position:absolute;border-style:solid; border-width:1px; border-color:#cccccc; top:5px; left:100px;bottom:0px;right:10px; width:88.5%;overflow:auto;white-space:pre-wrap;margin-top:5px;"><pre>'+actOutPut.getValue()+'</pre></div>';
		} else {
			Ext.Msg.alert('提示', "无权限查看该任务信息!");
		}
		
		if(timeOut=='true'){
			if(etInfoVariable.actDefName == 'ScriptCall'){
				skipBtn.show();
			}else{
				skipBtn.setVisible(false);
			}
		}
			
		
	});
	
	var myMask = new Ext.LoadMask(errorTaskWin, {msg:"读取中..."});
	contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
	{
		//myMask.show ();
		Ext.destroy (workItemRecord_mainPanel);
		if (Ext.isIE)
		{
			CollectGarbage ();
		}
	});
	
	Ext.getCmp("waitTimeText").setValue(etimeout);
	function oper(o)
	{
		var waitTime = '';
		
		var repeatUrl = 'jobSchedulingRepeat.do';
	
		Ext.Ajax.request({
			url : repeatUrl,// stepStop.do
			method : 'POST',
			params : {
				sysType : sysType,
				flowId : Ext.getCmp("iFlowId").getValue(),
				errorTaskId:Ext.getCmp("taskId").getValue(),
				execModel:o,
				actName:Ext.getCmp("taskName").getValue(),
				actId:actId,
                                iActId:actid,
				explain:explain,
				waitingSecond : waitTime
			},
			success : function(response, request) {
				//var success = Ext.decode(response.responseText).success;
				var message = Ext.decode(response.responseText).message;
				
				Ext.Msg.alert('提示', message, function() {
					if (Ext.isIE) {
						CollectGarbage();
					}
				//reloadGroup();
				if(errorTaskWin)
					errorTaskWin.close();
				});
				
			},
			failure : function(result, request) {
				secureFilterRs(result,"操作失败！");
			}
		});
	
	}
	
	//操作双人复核
	function flowDoubleCheckRetry(){
	
			retry();
		
	}
	function flowDoubleCheckSkip(){
		
			skip();
		
	}
	function flowDoubleCheck(type){
	    Ext.create("Ext.window.Window", {
	        id: 'flowDoubleCheckWin',
	        title: '操作复核',
	        width: contentPanel.getWidth() / 3,
	        height: contentPanel.getHeight() / 5,
	        layout: 'fit',
	        closeAction: 'destroy',
	        modal: true,
	        autoShow: false,
	        items: [{
	            xtype: 'form',
	            defaultType: 'textfield',
	            defaults: {
	                anchor: '100%',
	            },
	            fieldDefaults: {
	                labelWidth: 40,
	                labelAlign: 'left',
	                flex: 1,
	                margin: 5
	            },
	            items: [{
	                xtype: 'container',
	                layout: 'hbox',
	                items: [{
	                    xtype: 'textfield',
	                    name: 'checkuser',
	                    id: 'flowDoubleCheckUser',
	                    labelWidth: 90,
	                    width: '50%',
	                    fieldLabel: '审核人登录名',
	                    allowBlank: false,
	                    emptyText: '请输入审核人用户名'
	                },
	                {
	                    xtype: 'textfield',
	                    name: 'checkuserpass',
	                    id: 'flowDoubleCheckUserPass',
	                    fieldLabel: '密码',
	                    width: '50%',
	                    allowBlank: false,
	                    inputType: 'password',
	                    emptyText: '请输入审核人密码'
	                }]
	            }]
	        }],
	        buttons: [{
	            xtype: 'button',
	            text: '确定',
	            handler: function() {
	                var dUserName = Ext.getCmp('flowDoubleCheckUser').value;
	                var dUserPass = Ext.getCmp('flowDoubleCheckUserPass').value;
	                if (dUserName == null || dUserName == '' || dUserName == 'undefined') {
	                    Ext.MessageBox.alert("提示", "请填写审核用户名称！");
	                    Ext.getCmp('flowDoubleCheckUser').focus();
	                    return;
	                }
	                if (dUserPass == null || dUserPass == '' || dUserPass == 'undefined') {
	                    Ext.MessageBox.alert("提示", "请填写审核用户密码！");
	                    return;
	                }
	                Ext.Ajax.request({
	                    url: 'flowOrActDoubleCheck.do',
	                    method: 'POST',
	                    params: {
	                        dUserName : dUserName,
	                        dUserPass : dUserPass,
	                        flowid : Ext.getCmp("iFlowId").getValue()
	                    },
	                    success: function(response, opts) {
	                        var success = Ext.decode(response.responseText).success;
	                        var message = Ext.decode(response.responseText).message;
	                        if (success) {
	                            Ext.MessageBox.show({
	                                title: "提示",
	                                msg: message,
	                                buttonText: {
	                                    yes: '确定'
	                                },
	                                buttons: Ext.Msg.YES
	                            });
	                            Ext.getCmp('flowDoubleCheckWin').close();
	                            if (type == 1) {
	                            	retry();
	                            } else if (type == 2) {
	                            	skip();
	                            }
	                        } else {
	                            Ext.MessageBox.show({
	                                title: "提示",
	                                msg: message,
	                                buttonText: {
	                                    yes: '确定'
	                                },
	                                buttons: Ext.Msg.YES
	                            });
	                        }
	                    },
	                    failure: function(result, request) {
	                        secureFilterRs(result, "操作失败！");
	                    }
	                });
	            }
	        },
	        {
	            xtype: 'button',
	            text: '取消',
	            handler: function() {
	                this.up("window").close();
	            }
	        }]
	    }).show();
	}
	/**
	 * 1 代表重试 2 代表略过 0 代表继续执行 4 代表终止工作流
	 */
	function retry()
	{oper(1);}
	function skip()
	{
		// jobSchedulingRepeat.do 1_略过
		Ext.Msg.confirm ("请确认", "是否真的进行略过操作？", function (button, text)
		{
			if (button != "yes")
			{
				return;
			}else{
				aoms.log('skip');
				oper(2);
			}
		})
	}
	function continueFlow(){
		oper(0);
	}
	function stopFlow(){
		oper(4);
	}
});