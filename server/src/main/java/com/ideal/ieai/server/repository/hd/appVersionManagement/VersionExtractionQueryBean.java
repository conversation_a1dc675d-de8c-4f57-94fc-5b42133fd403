package com.ideal.ieai.server.repository.hd.appVersionManagement;

import java.io.Serializable;

/**
 * 
 * 名称: VersionExtractionQueryBean.java<br>
 * 描述: 版本提取查询参数类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2017年5月3日<br>
 */
public class VersionExtractionQueryBean implements Serializable
{
    private static final long serialVersionUID = 1L;

    private int               start            = 0; // 起始页
    private int               limit            = 50; // 每页数据
    private String            queryCriteria1;
    private Boolean           isShowFinish;         // 是否显示完成的任务
    private Boolean           isHistoryQuery;       // 是否查询历史信息
    private long              userId;
    private int               itype;
    private int               checkState;
    private int               intervalDay;//间隔天数

    
    public int getIntervalDay ()
    {
        return intervalDay;
    }

    public void setIntervalDay ( int intervalDay )
    {
        this.intervalDay = intervalDay;
    }

    public int getCheckState ()
    {
        return checkState;
    }

    public void setCheckState ( int checkState )
    {
        this.checkState = checkState;
    }

    public int getItype ()
    {
        return itype;
    }

    public void setItype ( int itype )
    {
        this.itype = itype;
    }

    public long getUserId ()
    {
        return userId;
    }

    public void setUserId ( long userId )
    {
        this.userId = userId;
    }

    public Boolean getIsShowFinish ()
    {
        return isShowFinish;
    }

    public void setIsShowFinish ( Boolean isShowFinish )
    {
        this.isShowFinish = isShowFinish;
    }

    public Boolean getIsHistoryQuery ()
    {
        return isHistoryQuery;
    }

    public void setIsHistoryQuery ( Boolean isHistoryQuery )
    {
        this.isHistoryQuery = isHistoryQuery;
    }
    public int getStart ()
    {
        return start;
    }

    public void setStart ( int start )
    {
        this.start = start;
    }

    public int getLimit ()
    {
        return limit;
    }

    public void setLimit ( int limit )
    {
        this.limit = limit;
    }

    public String getQueryCriteria1 ()
    {
        return queryCriteria1;
    }

    public void setQueryCriteria1 ( String queryCriteria1 )
    {
        this.queryCriteria1 = queryCriteria1;
    }



}
