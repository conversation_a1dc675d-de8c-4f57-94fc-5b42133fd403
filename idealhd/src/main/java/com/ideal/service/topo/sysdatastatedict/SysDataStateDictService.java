package com.ideal.service.topo.sysdatastatedict;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.json.JSONArray;

import com.ideal.ieai.server.repository.topo.sysdatastatedict.SysDataStateDictManager;

public class SysDataStateDictService
{

    private static final Logger _log = Logger.getLogger(SysDataStateDictService.class);

    public List querySysDataStateDict ( String systenName, Map map, String start, String limit )
    {
        if (null == systenName || "".equals(systenName) || "null".equals(systenName))
        {
            systenName = "";
        }
        return SysDataStateDictManager.getInstance().querySysDataStateDict(systenName, map, start, limit);
    }

    public void saveSysDataStateDict ( String jsonData ) throws Exception
    {
        List list = new ArrayList();
        try
        {
            if (null != jsonData && !"".equals(jsonData) && !"null".equals(jsonData))
            {
                JSONArray jsonArr;
                jsonArr = new JSONArray(jsonData);

                for (int i = 0; i < jsonArr.length(); i++)
                {
                    Map mo = new HashMap();
                    mo.put("sysName", jsonArr.getJSONObject(i).optString("sysName"));
                    mo.put("retpos", jsonArr.getJSONObject(i).optString("retpos"));
                    mo.put("retDes", jsonArr.getJSONObject(i).optString("retDes"));
                    mo.put("syspos", jsonArr.getJSONObject(i).optString("syspos"));
                    mo.put("iid", jsonArr.getJSONObject(i).optString("iid"));
                    list.add(mo);
                }
            }
            SysDataStateDictManager.getInstance().saveSysDataStateDict(list);
        } catch (Exception e)
        {
            _log.error(e);
            throw e;
        }
    }

    public void delSysDataStateDict ( String ids ) throws Exception
    {
        try
        {
            SysDataStateDictManager.getInstance().delSysDataStateDict(ids);
        } catch (Exception e)
        {
            _log.error(e);
            throw e;
        }
    }
}
