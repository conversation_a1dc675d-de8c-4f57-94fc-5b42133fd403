Ext.onReady(function() {
//清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init ();


    Ext.define("powerModel", {
        extend: "Ext.data.Model",
        fields: [{
            name: "powerid", type: "long"
        }, {
            name: "powername", type: "string"
        }]
    });

    var powerStore = Ext.create ('Ext.data.Store',
        {
            model : 'powerModel',
            autoLoad : true,
            proxy :
                {
                    type : 'ajax',
                    url : 'getpowerList.do',
                    reader :
                        {
                            type : 'json',
                            root : 'dataList'
                        }
                }
        });

    /** 查询条件-能力等级下拉 */
    var powerComboBox = Ext.create('Ext.form.field.ComboBox', {

        labelWidth : 90,
        labelAlign : 'right',
        fieldLabel : '能力等级',
        width : '25%',
        queryMode : 'local',
        emptyText : '全部',
        store : powerStore,
        displayField : 'powername',
        valueField : 'powerid',
        editable : true
    });

    Ext.define ('surveyModel',
        {
            extend : 'Ext.data.Model',
            fields : [
                {
                    name : 'surveyname',
                    type : 'string'
                },
                {
                    name : 'surveynid',
                    type : 'long'
                }
            ]
        });

    var surveyStore = Ext.create ('Ext.data.Store',
        {
            autoLoad : true,
            autoDestroy : true,
            model : 'surveyModel',
            proxy :
                {
                    type : 'ajax',
                    url : 'getsurveyList.do',
                    reader :
                        {
                            type : 'json',
                            root : 'dataList'
                        }
                }
        });

    /** 查询条件-问卷下拉 */
    var surveyComboBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
        //name : 'cpstatus',
        labelWidth : 90,
        queryMode : 'local',
        fieldLabel : ' 评估问卷',
        displayField : 'surveyname',
        valueField : 'surveynid',
        editable : true,
        emptyText : '全部',
        store : surveyStore,
        width : '25%',
        labelAlign : 'right'
    });




    /** 查询条件-计划名称输入框 */
    var assessPlanNameText = Ext.create('Ext.form.TextField', {
        fieldLabel : '计划名称',
        emptyText : '请输入',
        labelWidth : 90,
        labelAlign : 'right',
        width : '25%'
    });

    var queryStartTime = Ext.create('Go.form.field.DateTime',
        {
            fieldLabel: '计划开始时间',
            labelAlign: 'left',
            emptyText: '-请选择开始时间-',
            labelWidth: 90,
            width:'25%',
            format: 'Y-m-d H:i:s',
            margin:5,
            editable: false
        }
    )

    var queryEndTime = Ext.create('Go.form.field.DateTime',
        {
            fieldLabel: '计划结束时间',
            labelAlign: 'left',
            emptyText: '-请选择结束时间-',
            labelWidth: 90,
            width:'25%',
            format: 'Y-m-d H:i:s',
            margin:5,
            editable: false
        }
    )



    Ext.define('assessStatusModel', {
        extend : 'Ext.data.Model',
        fields: [{
            name: "asid", type: "long"
        }, {
            name: "asname", type: "string"
        }]
    });

    var assessStatusStore = Ext.create('Ext.data.Store', {
        model: "assessStatusModel",
        autoLoad : true,
        autoDestroy : true,
        data : [
            {"asid":-1, "asname":"全部"},
            {"asid":0, "asname":"已完成"},
            {"asid":1,"asname":"调研中"}


        ]
    });
    var assessStatusComboBox = Ext.create ('Ext.form.field.ComboBox',
        {
            labelWidth : 70,
            labelAlign : 'right',
            //labelWidth : 80,
            width : '25%',
            fieldLabel : '状态',
            emptyText : '--请选择--',
            queryMode : 'local',
            store : assessStatusStore,
            displayField : 'asname',
            valueField : 'asid',
            value:-1,
            anyMatch: true,
            editable : true
        });



//能力评审
    Ext.define('arModel', {
        extend : 'Ext.data.Model',
        fields : [
            {
                name : 'iid',
                type : 'long'
            },
            {
                name : 'iplanname',
                type : 'string'
            },
            {
                name : 'ipowerabilityid',
                type : 'long'
            },
            {
                name : 'ipowerability',
                type : 'string'
            },
            {
                name : 'surveyid',
                type : 'long'
            },
            {
                name : 'surveyname',
                type : 'string'
            },
            {
                name : 'istarttime',
                type : 'long'
            }, {
                name : 'iendtime',
                type : 'long'
            }, {
                name : 'imark',
                type : 'string'
            },
            {
                name : 'rreadystarttime',
                type : 'long'
            },
            {
                name : 'readyendtime',
                type : 'long'
            },
            {
                name : 'istate',
                type : 'string'
            },
            {
                name : 'abilityresult',
                type : 'string'
            }

        ]
    })

    var arStore = Ext.create('Ext.data.Store', {
        autoLoad : true,
        autoDestroy : true,
        model : 'arModel',
        proxy : {
            type : 'ajax',
            url : 'getAbilityReviewList.do',
            //url : 'index.do',
            actionMethods : 'post',
            reader : {
                type : 'json',
                root : 'dataList'
            }
        }
    })

    arStore.on ('beforeload', function (store, options)
    {
        var powerid = powerComboBox.getValue();
        var surveyid = surveyComboBox.getValue();
        var starttime = queryStartTime.getValue();
        var endtime = queryEndTime.getValue();
        var aptext = assessPlanNameText.getValue();
        var asid = assessStatusComboBox.getValue();




        var new_params = {
            powerid : powerid,
            surveyid : surveyid,
            starttime : starttime,
            endtime : endtime,
            aptext : aptext,
            asid : asid,


        };
        Ext.apply (arStore.proxy.extraParams, new_params);
    });


    var arColumns = [{
        text: '序号',
        xtype: 'rownumberer',
        width: 40
    }, {
        dataIndex: 'iid',
        text: 'IID',
        hidden: true
    }, {
        dataIndex: 'iplanname',
        text: '计划名称',
        flex: 1
    }, {
        text: '能力等级id',
        dataIndex: 'ipowerabilityid',
        hidden: true
    },
        {
            text: '能力等级',
            dataIndex: 'ipowerability',
            minWidth: 150,
            // hidden: true
        },
        {
            text: '评估问卷id',
            dataIndex: 'surveyid',
            minWidth: 100,
            flex: 1,
            hidden: true
        },
        {
            text: '评估问卷',
            dataIndex: 'surveyname',
            minWidth: 100,
            flex: 1,
            hidden: true
        },
        {
            dataIndex: 'istarttime',
            text: '计划开始时间',
            flex: 1
        }, {
            dataIndex: 'iendtime',
            text: '计划结束时间',
            flex: 1
        },
        {
            dataIndex: 'imark',
            text: '备注',
            flex: 1
        },{
            dataIndex: 'readystarttime',
            text: '实际开始时间',
            flex: 1
        }, {
            dataIndex: 'readyendtime',
            text: '实际结束时间',
            flex: 1
        },
        {
            dataIndex: 'istate',
            text: '状态',
            flex: 1,
            renderer:function(value){
                switch (value){
                    case 0:
                        return '全部'
                    case 1:
                        return '评审中'
                    case 2:
                        return '已完成'
                    default:
                        break;
                }
            }
        }, {
            dataIndex: 'abilityresult',
            text: '审批结果',
            flex: 1
        },
        {
            text : '操作',xtype : 'orderactiontextcolumn',width : 180,align : 'left',
            items : [
                {
                    text : '评审',
                    getClass : function(v, metadata, record) {
                    },
                    handler : function(grid, rowIndex) {
                        alert("评审");
                        var eventid = grid.getStore().data.items[rowIndex].data.iid;
                        var istate = grid.getStore().data.items[rowIndex].data.istate;
                        viewEventWinShow(1,eventid,istate);
                        operationArray.push('<a onclick="basicInfoWin(true, ' + value + ', 0, ' + state + ', 0, null, 1)">查看</a>');

                    }
                }
            ]
        }

    ]
    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });

    function turnDetailOne(value, metaData, record) {
        return "<a style='cursor: pointer;'>" +value + "</a>";
    }

    function barquery(){
        arGrid.ipage.moveFirst();
    }


    var  arGrid= Ext.create('Ext.ux.ideal.grid.Panel', {
        width : '100%',
        region:'center',
        store : arStore,
        layout : 'fit',
        ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        selModel : Ext.create('Ext.selection.CheckboxModel', {checkOnly: true}),
        plugins: [ cellEditing ],
        border : true,
        viewConfig:{
            enableTextSelection:true
        },
        columnLines : true,
        columns : arColumns,
        cls:'customize_panel_back',
        padding : grid_space,
        cellTip : true,
        animCollapse : false,
        dockedItems: [{
            xtype: 'toolbar',
            items: ['->'
            ]
        }]
    });



    var formMainPanel = Ext.create('Ext.ux.ideal.form.Panel', {
        region : 'north',
        layout : 'anchor',
        buttonAlign : 'center',
        bodyCls:'x-docked-noborder-top',
        // iqueryFun: barquery,
        border: false,
        dockedItems : [
            {
                xtype : 'toolbar',
                baseCls:'customize_gray_back',
                border : false,
                dock : 'top',
                items:[
                    assessPlanNameText, powerComboBox, surveyComboBox,assessStatusComboBox
                ]
            },
            {
                xtype : 'toolbar',
                baseCls:'customize_gray_back',
                border : false,
                dock : 'top',
                items:[
                    queryStartTime,queryEndTime
                ]
            },
            {
                xtype : 'toolbar',
                baseCls:'customize_gray_back',
                border : false,
                dock : 'top',
                items:['->',
                    {
                        text : '查询',
                        baseCls : 'Common_Btn',
                        handler : function(){
                            assessPlanStore.load();
                        }
                    },
                    {
                        text : '重置',
                        baseCls : 'Common_Btn',
                        handler : function() {
                            surveyComboBox.setValue("");
                            powerComboBox.setValue("");
                            assessPlanNameText.setValue("");
                            queryEndTime.setValue("");
                            queryStartTime.setValue("");
                            assessStatusComboBox.setValue("");



                        }
                    }

                ]
            }
        ]
    });

    var mainPanel = Ext.create ('Ext.panel.Panel',
        {
            renderTo : "ar_grid",
            layout : 'border',
            width : contentPanel.getWidth (),
            //layout : 'fit',
            bodyCls: 'service_platform_bodybg',
            height : contentPanel.getHeight ()-50,
            border : true,
            bodyPadding : grid_margin,
            items : [formMainPanel,arGrid]
        });

    /** 窗口尺寸调节* */
    contentPanel.on ('resize', function ()
    {
        mainPanel.setHeight (contentPanel.getHeight () -modelHeigth);
        mainPanel.setWidth ('100%');
    });

    contentPanel.getLoader ().on ("beforeload", function (obj, options, eOpts)
    {
        Ext.destroy (mainPanel);
        if (Ext.isIE)
        {
            CollectGarbage ();
        }
    });
    /** *********************方法********************* */
    /* 解决IE下trim问题 */
    String.prototype.trim = function ()
    {
        return this.replace (/(^\s*)|(\s*$)/g, "");
    };


});