-- 8.11.0 version RT patch is as follows	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM IEAI_SCRIPT_AUDITING  WHERE  IID=17) THEN
			INSERT INTO IEAI_SCRIPT_AUDITING (IID, INAME) VALUES (17, '例行任务用户发起操作系统权限');
			commit;
		END IF;
		
		IF NOT EXISTS (SELECT * FROM IEAI_SCRIPT_AUDITING  WHERE  IID=18) THEN
			INSERT INTO IEAI_SCRIPT_AUDITING (IID, INAME) VALUES (18, '例行任务用户审核操作系统权限');
			commit;
		END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM IEAI_GROUPMESSAGE WHERE GROUPID=47) THEN
		   INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (47, '例行任务', '例行任务模块组', 47, 'images/info110.png');
        END IF;
		
		IF NOT EXISTS (SELECT * FROM IEAI_DBSOURCE WHERE IDBSOURCEID=47) THEN
           INSERT INTO IEAI_DBSOURCE(IDBSOURCEID,IGROUPMESSGEID,IDBSOURCENAME,ISERVERIP,IDBURL,IDBUSER,IDBPASS,IISBASIC) VALUES (47,47,'例行任务源','','','','',0);	
        END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	
	DELIMITER ;;
	DROP PROCEDURE IF EXISTS UPPRDE;;
	create PROCEDURE UPPRDE()
	BEGIN
	
		IF NOT EXISTS (SELECT * FROM IEAI_PROJECT WHERE IID=-47) THEN
		   INSERT INTO IEAI_PROJECT(IID, IPKGCONTENTID, INAME, IMAJVER, IMINVER, IFREEZED, IUPLOADUSER, ICOMMENT, IUPLOADNUM, IUUID,  IFREEZEUSER, IFREEZEUSERID, IUPLOADUSERID, IGROUPID, PROTYPE, IUPPERID, ILATESTID) VALUES(-47, 0, '所有例行任务业务系统', 0, 0, 0, '', '', 0,  '', '', 0, 0, 47, -47, -47, -47);

        END IF;
		
	END;;
	DELIMITER ;
	CALL UPPRDE();
	