package com.ideal.controller.reportanalysis.taskinsquery;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.common.utils.IPTools;
import com.ideal.common.utils.SessionData;
import com.ideal.controller.reportanalysis.healthquery.HealthQueryController;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.server.quartz.TimetaskService;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.timetask.repository.TimetaskInfoBean;
import com.ideal.ieai.server.timetask.repository.manage.IpselectQueryCondition;
import com.ideal.ieai.server.util.PageSplitBean;
import com.ideal.service.reportanalysis.healthquery.HealthQueryService;
import com.ideal.service.reportanalysis.taskinsquery.TaskInsQueryService;
import com.ideal.service.timetask.manage.TTManageService;

@Controller
public class TaskInsQueryController
{
    private static final Logger logger      = Logger.getLogger(TaskInsQueryController.class);
    
    private static final String PROPERTY             = "property";
    private static final String DIRECTION            = "direction";
    
    @RequestMapping("taskinsquery.do")
    public String mainPage ()
    {
        return "reportanalysis/taskinsquery/taskinsquery";
    }
    
    @RequestMapping("/getTaskInsQueryrList.do")
    @ResponseBody
    public Map<String, Object> getTaskInsQueryrList ( HttpServletRequest request ) {
        Map<String, Object> map = new HashMap();
        String taskname = request.getParameter("taskname");
        String beginTime = request.getParameter("beginTime");
        String endTime = request.getParameter("endTime");
        PageSplitBean beans = new PageSplitBean(request, "limit", "start");
        TaskInsQueryService service = new TaskInsQueryService();
        try
        {
            map=service.getTaskInsQueryrList(taskname,beginTime,endTime,beans);
        } catch (RepositoryException e)
        {
            logger.error("TaskInsQueryController.getTaskInsQueryrList RepositoryException error",e);
        } catch (ParseException e)
        {
            logger.error("TaskInsQueryController.getTaskInsQueryrList ParseException error",e);
        }
        return map;
    }
    
    @RequestMapping("taskinsquery/getComputerListSelected.do")
    @ResponseBody
    public Map<String, Object> getComputerListSelected ( HttpServletRequest request ) {
        String errorMsg = "";
        Map map = new HashMap();
        int type = Constants.IEAI_TIMINGTASK;
        TaskInsQueryService service = new TaskInsQueryService();
        long ipcondition = 0L;
        try
        {  
            //接收参数
            String taskId = request.getParameter("taskId");
            String selcomids = request.getParameter("selcomids");
            String ipselstr = request.getParameter("ipselstr");
            String startStr = request.getParameter("start");
            String limitStr = request.getParameter("limit");
            String sort = request.getParameter("sort");
            Map sortMap = getSortMap2(sort);
            /*if(StringUtils.isNotBlank(ipselstr)){
                ipcondition = IPTools.getAlias(ipselstr).longValue();
            }*/
            //如果两个输入框都有值，则按ip段查询；否则只有一个输入框有值，则按ip模糊查询
            IpselectQueryCondition conditionBean = new IpselectQueryCondition();
          
            //处理分页信息
            int start = 0;
            int limit = 30;
            if(!(null==startStr || "".equals(startStr)))
            {
                start = Integer.parseInt(startStr);
            }
            if(!(null==limitStr || "".equals(limitStr)))
            {
                limit = Integer.parseInt(limitStr);
            }
            conditionBean.setStart(start);
            conditionBean.setLimit(limit);
            
            //String selCpIdStr = TTManageService.getInstance().getSelIpidStrByTaskId(Long.parseLong(taskIdStr), type);
            Object[] obj = service.getSelIpidStrByTaskId(selcomids,ipselstr,ipcondition,conditionBean,type);
            List resultList  = (List)obj[0];
            long total = (Long)obj[1];
            map.put("total", total);
            map.put("dataList", resultList);
            
            map.put("success", true);
        } catch (Exception e)
        {
            map.put("success", false);
            map.put("message", errorMsg);
            logger.error("TTManageController.getComputerListSelectedForSys Exception error:" ,e);
        }
        return map;
    }
    
    @RequestMapping("taskinsquery/getComputerListNoSelected.do")
    @ResponseBody
    public Map<String, List> getComputerListNoSelected ( HttpServletRequest request )
            throws RepositoryException
    {
        String errorMsg = "";
        Map map = new HashMap();
        int type = Constants.IEAI_TIMINGTASK;
        long ipcondition = 0L;
        TaskInsQueryService service = new TaskInsQueryService();
        try
        {  
            //接收参数
            String taskId = request.getParameter("taskId");
            String selcomids = request.getParameter("selcomids");
            String ipselstr = request.getParameter("ipunselstr");
            String startStr = request.getParameter("start");
            String limitStr = request.getParameter("limit");
            String sort = request.getParameter("sort");
            Map sortMap = getSortMap2(sort);
            /*if(StringUtils.isNotBlank(ipselstr)){
                ipcondition = IPTools.getAlias(ipselstr).longValue();
            }*/
            //处理分页信息
            int start = 0;
            int limit = 30;
            if(!(null==startStr || "".equals(startStr)))
            {
                start = Integer.parseInt(startStr);
            }
            if(!(null==limitStr || "".equals(limitStr)))
            {
                limit = Integer.parseInt(limitStr);
            }
            IpselectQueryCondition conditionBean = new IpselectQueryCondition();
            conditionBean.setStart(start);
            conditionBean.setLimit(limit);
            Object[] obj = service.getUnSelIpidStrByTaskId(selcomids,ipselstr,ipcondition,conditionBean,type);
            List resultList  = (List)obj[0];
            long total = (Long)obj[1];
            map.put("total", total);
            map.put("dataList", resultList);
            map.put("success", true);
        } catch (Exception e)
        {
            map.put("success", false);
            map.put("message", errorMsg);
            logger.error("TaskInsQueryController.getComputerListNoSelected Exception error:" ,e);
        }
        return map;
    }
    
    
    
    
    @RequestMapping("taskinsquery/createTaskId.do")
    @ResponseBody
    public Map<String, List> createTaskId ( HttpServletRequest request )
    {
        Map map = new HashMap();
        TaskInsQueryService service = new TaskInsQueryService();
        try
        {  
            long id = service.createTaskId();
            map.put("success", true);
            map.put("taskid", id);
        } catch (Exception e)
        {
            map.put("success", false);
            map.put("message", "创建任务ID失败！");
            logger.error("TTManageController.getComputerListNoSelectedForTT Exception error:" ,e);
        }
        return map;
    }
    
    public Map getSortMap2 ( String sort ) throws JSONException
    {
        Map sortMap = new HashMap();
        if (sort != null)
        {

            JSONArray jsonArr = new JSONArray(sort);
            if (jsonArr.length() > 0)
            {
                for (int i = 0; i < jsonArr.length(); i++)
                {
                    String sortName = jsonArr.getJSONObject(i).optString(PROPERTY);
                    String sortOrder = jsonArr.getJSONObject(i).optString(DIRECTION);

                    if (sortName.equals("ip"))
                    {
                        sortName = "IAGENT_IP";
                    } else if (sortName.equals("port"))
                    {
                        sortName = "IAGENT_PORT";
                    } else
                    {
                        sortName = "IAGENT_NAME";
                    }
                    sortMap.put("column", sortName);
                    sortMap.put("sortorder", sortOrder);
                }
            } else
            {
                sortMap.put("column", "IAGENT_NAME");
                sortMap.put("sortorder", "ASC");

            }
        }

        return sortMap;
    }
    
     @RequestMapping("taskinsquery/saveRows.do")
     @ResponseBody
     public Map<String, List> taskinsquerySaveRows ( HttpServletRequest request )
     {
         TaskInsQueryService service = new TaskInsQueryService();
         String userName = SessionData.getSessionData(request).getUserName();
         String errorMsg = "";
         Map map = new HashMap();
         int type = Constants.IEAI_TIMINGTASK;
         String selcomids = request.getParameter("selcomids");
         String taskname = request.getParameter("taskname");
         String tasktype = request.getParameter("tasktype");
         String taskscript = request.getParameter("taskscript");
         String excutestyle = request.getParameter("excutestyle");
         String cronexpression = request.getParameter("cronexpression");
         String path = request.getParameter("path");
         String executeUser = request.getParameter("executeUser");
         String scriptuuid = request.getParameter("scriptuuid");
         String serviceName = request.getParameter("serviceName");
         String scriptType = request.getParameter("scriptType");
         String scriptName = request.getParameter("scriptName");
         String version = request.getParameter("version");
         try {
             service.saveTask(taskname,tasktype,taskscript,excutestyle,cronexpression,type,userName,selcomids,path,executeUser,scriptuuid,serviceName,scriptType,scriptName,version);
             map.put("success", true);
             map.put("message",  "保存成功！");
         } catch(Exception e){
             map.put("success", false);
             map.put("message",  "保存失败！");
             logger.error("TaskInsQueryController.taskinsquerySaveRows Exception error:" , e);
         }
         return map;
     }
     
     @RequestMapping("taskinsquery/updateRows.do")
     @ResponseBody
     public Map<String, List> taskinsqueryUpdateRows ( HttpServletRequest request )
     {
         TaskInsQueryService service = new TaskInsQueryService();
         String userName = SessionData.getSessionData(request).getUserName();
         //long userId = Long.parseLong(userid);
         String errorMsg = "";
         Map map = new HashMap();
         int type = Constants.IEAI_TIMINGTASK;
         String iid = request.getParameter("iid");
         String selcomids = request.getParameter("selcomids");
         String taskname = request.getParameter("taskname");
         String tasktype = request.getParameter("tasktype");
         String taskscript = request.getParameter("taskscript");
         String excutestyle = request.getParameter("excutestyle");
         String cronexpression = request.getParameter("cronexpression");
         String path = request.getParameter("path");
         String executeUser = request.getParameter("executeUser");
         String scriptuuid = request.getParameter("scriptuuid");
         String serviceName = request.getParameter("serviceName");
         String scriptType = request.getParameter("scriptType");
         String scriptName = request.getParameter("scriptName");
         String version = request.getParameter("version");
         String scriptid = request.getParameter("scriptid");
         try {
             service.updateTask(iid,taskname,tasktype,taskscript,excutestyle,cronexpression,type,userName,selcomids,path,executeUser,scriptuuid,serviceName,scriptType,scriptName,version,scriptid);
             map.put("success", true);
             map.put("message",  "保存成功！");
         } catch(Exception e){
             map.put("success", false);
             map.put("message",  "保存失败！");
             logger.error("TaskInsQueryController.taskinsquerySaveRows Exception error:" , e);
         }
         return map;
     }
     
     @RequestMapping("taskinsquery/queryEditDatas.do")
     @ResponseBody
     public Map queryEditDatas ( HttpServletRequest request )
     {
         Map map = new HashMap();
         TaskInsQueryService service = new TaskInsQueryService();
         String taskid = request.getParameter("iid");
         try{
             List comlist = service.querySelComIds(taskid);
             map.put("comlist", comlist);
             service.queryTaskDatas(map,taskid);
         }catch(RepositoryException e){
             logger.error("TaskInsQueryController.queryEditDatas Exception error:" , e);
         }
         return map;
     }
     
     
     @RequestMapping("taskinsquery/starttask.do")
     @ResponseBody
     public Map starttask ( HttpServletRequest request )
     {
             String errorMsg = "";
             Map map = new HashMap();
             int type = Constants.IEAI_TIMINGTASK;
             TaskInsQueryService service = new TaskInsQueryService();
             try
             {
                 String userid = SessionData.getSessionData(request).getUserInnerCode();
                 //接收选中id的json数据
                 String idStr = request.getParameter("iid");
                 String taskid = service.getTaskid(idStr);
                 Object[] obj = TTManageService.getInstance().getTimetaskInfoByIdsStr(taskid, type);
                 List selectList = (List) obj[0];
                 
                 //判断是否有可用IP
                 boolean hasServerAvaliable = TimetaskService.getInstance().checkTTServerCnt();
                 if(hasServerAvaliable)
                 {
                     //启动任务
                     TimetaskService.getInstance().preStartTasks(selectList,userid);
                 }else
                 {
                     errorMsg = "没有可用的定时任务server！";
                 }
                 
                 map.put("success", true);
                 map.put("message", errorMsg);
             } catch (Exception e)
             {
                 map.put("success", false);
                 map.put("message", errorMsg);
                 logger.error("TTManageController.startSelects Exception error:" ,e);
             }
             return map;
     }
     
     
     @RequestMapping("taskinsquery/stopSelects.do")
     @ResponseBody
     public Map<String, List> stopSelects ( HttpServletRequest request )
             throws RepositoryException
     {
         
         //findAllThread();
         
         String errorMsg = "";
         Map map = new HashMap();
         int type = Constants.IEAI_TIMINGTASK;
         try
         {
             TaskInsQueryService service = new TaskInsQueryService();
             String idStr = request.getParameter("selectJSonData");
             String taskids = service.getTaskid(idStr);
             String stopType = request.getParameter("stopType");//是否强行终止
             String pause = request.getParameter("pause");// 是否暂停
             
             Object[] obj = TTManageService.getInstance().getTimetaskInfoByIdsStr(taskids, type);
             List selectList = (List) obj[0];
             
             //停止任务
             TimetaskService.getInstance().preStopTasks(selectList, pause);
            

             
             //判断是否需要终止正在运行的实例
             if("force".equals(stopType)){
                
                 
                 for (int i = 0; selectList != null && i < selectList.size(); i++)
                 {
                     TimetaskInfoBean bean = (TimetaskInfoBean) selectList.get(i);
                 }
                 //info表stopInsFlag=1
                 TimetaskService.getInstance().setSopInsFlagForTasks(selectList);
               
             }
             
             map.put("success", true);
         } catch (Exception e)
         {
             map.put("success", false);
             map.put("message", errorMsg);
             logger.error("TTManageController.stopSelects Exception error:" ,e);
         }
         return map;
     }
     
     
     @RequestMapping("taskinsquery/checkTasksIsRun.do")
     @ResponseBody
     public String checkTasksIsRun ( HttpServletRequest request )
             throws RepositoryException
     {
         
         String errorMsg = "";
         Map map = new HashMap();
         int type = Constants.IEAI_TIMINGTASK;
         String tasknames = "";
         try
         {
             TaskInsQueryService service = new TaskInsQueryService();
             String iidStr = request.getParameter("iidStr");
             tasknames = service.getRunningTasks(iidStr);
             map.put("success", true);
             map.put("tasknames", tasknames);
         } catch (Exception e)
         {
             map.put("success", false);
             map.put("message", errorMsg);
             logger.error("TaskInsQueryController.checkTasksIsRun Exception error:" ,e);
         }
         return tasknames;
     }
     
     @RequestMapping("taskinsquery/deleteRows.do")
     @ResponseBody
     public Map<String, List> deleteRows ( HttpServletRequest request )
             throws RepositoryException
     {
         String errorMsg = "";
         Map map = new HashMap();
         int type = Constants.IEAI_TIMINGTASK;
         TaskInsQueryService service = new TaskInsQueryService();
         try
         {
             String iidStr = request.getParameter("iidStr");
             service.delTaskinsDatas(iidStr, type);
             map.put("success", true);
         } catch (Exception e)
         {
             map.put("success", false);
             map.put("message", errorMsg);
             logger.error("TaskInsQueryController.ttManageDeleteRows Exception error:" ,e);
         }
         return map;
     }
     
}
