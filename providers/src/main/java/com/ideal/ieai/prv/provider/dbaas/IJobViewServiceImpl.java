package com.ideal.ieai.prv.provider.dbaas;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONObject;
import org.springframework.stereotype.Component;

import org.apache.dubbo.config.annotation.DubboService;
import com.ideal.dubbo.interfaces.dbaas.IJobViewService;
import com.ideal.dubbo.models.JobViewModel;
import com.ideal.ieai.prv.provider.utils.ParseJson;
import com.ideal.ieai.prv.repository.hd.dubbo.dbaas.JobViewManager;

@DubboService
public class IJobViewServiceImpl implements IJobViewService
{

    @Override
    public Map listDataSource ( String page, String limit, String status ) throws Exception
    {
        return JobViewManager.getInstance().listDataSource(page, limit, status);
    }

    @Override
    public Map<String, Object> saveDataSource ( String jsonData ) throws Exception
    {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("success", false);
        map.put("message", "保存失败!");

        List<JobViewModel> bsList = new ArrayList<JobViewModel>();
        List<Map<String, Object>> databaseList = ParseJson.JSON2List(jsonData);
        JobViewModel dbm;

        if (databaseList != null && !databaseList.isEmpty())
        {
            for (int i = 0; i < databaseList.size(); i++)
            {
                Map<String, Object> cmdMap = databaseList.get(i);
                dbm = convertToDataSourceModel(cmdMap);
                bsList.add(dbm);
            }
            if (JobViewManager.getInstance().saveDataSource(bsList))
            {
                map.put("success", true);
                map.put("message", "保存成功！");
            }

        }

        return map;
    }

    @Override
    public void updateDataSource ( Map map ) throws Exception
    {
        JobViewManager.getInstance().updateDataSource(map);
    }

    private JobViewModel convertToDataSourceModel ( Map<String, Object> rgMap )
    {
        JobViewModel res = new JobViewModel();
        Object iid = rgMap.get("IID");
        if (iid == JSONObject.NULL || "".equals(String.valueOf(iid)))
        {
            res.setIID(-1l);
        } else
        {
            res.setIID(Long.parseLong(String.valueOf(rgMap.get("IID"))));
        }
        res.setIDBID(String.valueOf(rgMap.get("IDBID")));
        res.setISERVICEID(Long.parseLong(String.valueOf(rgMap.get("ISERVICEID"))));
        res.setIRESID(Long.parseLong(String.valueOf(rgMap.get("IRESID"))));
        res.setITABLENAME(((String) rgMap.get("ITABLENAME")).trim());

        return res;
    }

    @Override
    public void deletejob ( String deleteIds ) throws Exception
    {
        JobViewManager.getInstance().deletejob(deleteIds);
    }

    public void canceljob ( String cancelIds ) throws Exception
    {
        JobViewManager.getInstance().canceljob(cancelIds);
    }
}
