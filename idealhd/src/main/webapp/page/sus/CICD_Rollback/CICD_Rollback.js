//启动任务总tab配置界面
var fal = true;
var pageUp = null;
var pageDown = null;
var confItemGrid = null;
var confItemConfStore = null;
var icSPDBCreateTaskWin = null;
var deployFromPanel = null;
var editorNameAll = "任务名称：";
var initJson = null;
var iframe = null;
var paramsProjNumBox = null;//项目编码
var paramsProjIncreBox = null;//项目增量
var paramsProjTypeBox = null;//项目类型
var consZpFileBox = null;//制品文件
var paramsModelTypeBox = null;//模块类型
var consCodeBox = null;//代码清单
var deployParamPeople = null;//审核人
var autoTestPeople = null;//是否发送自动化测试
var vm = null;//灰度部署比例
var hdStartflag = 1;//灰度部署状态 2启动状态 1 禁用
var zpfilenamestore = null;//制品文件
var deployGrid = null;//启动参数服务器
var startTime = null;//时间
var envInfoTf = null;//环境信息
var serviceGridPanelStore=null;//环境store
var modifyPriorityCICD=null;//优先级
var addServiceBut=null;//增加服务器
var addItem=null;//添加
var delItem=null;//删除
var disa='';//禁用属性
var loadflag=true;
var readOnly = false;
var paramsProjIncreStore=null;//项目增量store
var doZPStore=null;//待选制品store
var grayDeployScale=null;//灰度比例
var envinfo=[];//环境结合
var listCodeStore=null;//代码清单
var ds='';//审核人是否显示小红心
var infomationDescPanel=null;
var consParamsConfStore = null;
var tabDeployParam =null;//自定义参数
var agnetSet = null;
var consGridPanel = null;
Ext.onReady(function() {
    console.log(taskid);
    destroyRubbish();
    if(cicdflag == 'false'){
        ds='display:none';
    }
    let hdblvalue = 50;
    var hdblstate = '';
	if(jumpflag==4){
		if(hdblS == 2){
			hdblstate = 'checked';
		}
		hdblvalue = hdblV;
		disa='disabled';
		loadflag =false;
		readOnly = true;
	}else if(jumpflag==3){
		loadflag =false;
	}
    var navigate = function(panel, direction) {
        var layout = panel.getLayout();
        layout[direction]();
    };
    var checkModel=true;
    var checkModel2=true;
	if(jumpflag != 4){
		checkModel = Ext.create('Ext.selection.CheckboxModel', {checkOnly : true
		});
		checkModel2 = Ext.create('Ext.selection.CheckboxModel', {checkOnly : true
		});
	}
    //================001确认流水线步骤开始==========================
    var editorNameContent = Ext.create('Ext.panel.Panel', {
        border: false,
        margin: '0 0 0 20',
        autoScroll: true,
        region: 'center',
        html: '<div><h2 style="float: left;">'+editorNameAll+itaskName+'</h2><span style="color: red;position:relative;top:7px;margin-left:10px;">鼠标右键禁用步骤</span></div>'
    });

    var frameFlowTaskContent = Ext.create('Ext.panel.Panel', {
        border: false,
        autoScroll: false,
        region: 'center',
        html: '<div><iframe  id="iframeRollback" class="iframe" src="page/sus/CICD_FlowTask/CICD_FlowEditor.jsp" frameborder="0" scrolling="auto"></iframe></div>'
    });

    var flowConfigFromPanel = Ext.create('Ext.ux.ideal.form.Panel', {
        region: 'north',
        layout: 'anchor',
        buttonAlign: 'center',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [editorNameContent, '->', {
                xtype: 'button',
                text: '恢复',
                margin: '0 10 20 0',
                cls: 'Common_Btn',
                handler: function() {
                    initFlowJson(1);
                }
            }, {
                xtype: 'button',
                text: '查看',
                margin: '0 20 20 0',
                cls: 'Common_Btn',
                hidden : true,
                handler: function() {

                }
            }]
        }]
    });
    var sureFlowTaskStepPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        layout: 'border',
        items: [flowConfigFromPanel, frameFlowTaskContent]
    });

    //================001确认流水线步骤结束==========================

    //------------------配置全局参数 bengin-------------
    //-----------paramsProjTypeBox query bengi
    Ext.define('RollbackIhdstartflag', {
        extend: 'Ext.data.Model',
        fields: [
            { name: 'ihdstartflag', type: 'string' },
            { name: 'iprojectnum', type: 'string' },
            { name: 'iprojectincre', type: 'string' }
        ]
    });
    var ProjTypestore=null;
    var ProjTypestore = Ext.create('Ext.data.Store', {
        fields: ['id', 'name'],
        data: [
            { "id": 0, "name": "项目" },
            { "id": 1, "name": "日常" },
            {"id":2,"name":"后补"}
        ]
    });




    paramsProjTypeBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
        displayField: 'name',
        valueField: 'id',
        margin: '1px',
        labelWidth: 70,
        labelAlign: 'right',
        width: contentPanel.getWidth() / 5,
        fieldLabel: '项目类型<b  style="color:red;">*</b>',
        emptyText: "--请选择项目类型--",
        triggerAction: 'all',
        editable: true,
        multiSelect: true,
        queryMode: 'local',
        anyMatch: true,
        forceSelection:true,
        store: ProjTypestore,
        readOnly: true, // 不可编辑
        listeners: {
            change: function() {

                var idepName = this.getValue();
                if (idepName == 1) {
                    //paramsProjTypeBox.width='24.6%',
                    paramsProjNumBox.hide();
                    paramsProjIncreBox.hide();
					consZpFileBox.setValue('');
                    paramsProjNumBox.setValue('');
                    paramsProjIncreBox.setValue('');
					consCodeBox.setValue('');
                } else {
                    //paramsProjTypeBox.width='25%',
					consZpFileBox.setValue('');
                    paramsProjNumBox.setValue('');
                    paramsProjIncreBox.setValue('');
					consCodeBox.setValue('');
                    paramsProjNumBox.show();
                    paramsProjIncreBox.show();
					if((jumpflag == 3 || jumpflag == 4)&& idepName!=1){
                        //promiseResolve('项目类型初始化结束');
                    }
                }
            }, beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        if (null != text) {
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        }
                    });
                    combo.expand();
                    return false;
                }
            },collapse : function(e){
                loadCodeList();
            }
        }
    });
    Ext.define('paramsProjNumModel', {
        extend: 'Ext.data.Model',
        fields: [
            { name: 'projectName', type: 'string' },
            { name: 'projectNum', type: 'string' }
        ]
    });
    var paramsProjNumStore = Ext.create('Ext.data.Store', {
        autoLoad: loadflag,
        autoDestroy: true,
        pageSize: 50,
        model: 'paramsProjNumModel',
        proxy: {
            type: 'ajax',
            url: 'queryProjNumInfo.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    paramsProjNumStore.on('beforeload', function() {
        var new_params =
            {
                bussid: trim(bussid),
            };
        Ext.apply(paramsProjNumStore.proxy.extraParams, new_params);
    })

    paramsProjNumBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
        displayField: 'projectName',
        valueField: 'projectNum',
        store: paramsProjNumStore,
        margin: '1px 1px',
        labelWidth: 70,
        labelAlign: 'right',
        width: contentPanel.getWidth() / 5,
        fieldLabel: '项目编号<b  style="color:red;">*</b>',
        emptyText: '--请输入项目编号--',
        forceSelection: true,
        readOnly: true, // 不可编辑
        listeners: {
            change: function() {
                var projectNum = this.getValue();
                // paramsProjIncreBox.setValue('');
				consZpFileBox.setValue('');
				consCodeBox.setValue('');
                paramsProjIncreStore.getProxy().setExtraParam("projNo", projectNum);
                paramsProjIncreStore.load();
                var protype = paramsProjTypeBox.getValue();
				if((jumpflag == 3 || jumpflag == 4)&& protype!=1){
                    promiseResolve('项目编号初始化结束');
                }
            }, beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        if (null != text) {
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        }
                    });
                    combo.expand();
                    return false;
                }
            },collapse : function(e){
                loadCodeList();
            }
        }
    });
    Ext.define('paramsProjIncreModel', {
        extend: 'Ext.data.Model',
        fields: [
            { name: 'projectIncre', type: 'string' },
            { name: 'iname', type: 'string' }
        ]
    });
    paramsProjIncreStore = Ext.create('Ext.data.Store', {
        autoLoad: loadflag,
        autoDestroy: true,
        pageSize: 50,
        model: 'paramsProjIncreModel',
        proxy: {
            type: 'ajax',
            url: 'queryProjectIncreInfo.do',
            extraParams: {
                syscode: syscode,
            },
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    paramsProjIncreStore.on('beforeload', function(store, options) {
        var new_params =
            {
                proj_id: bussid,
            };
        Ext.apply(paramsProjIncreStore.proxy.extraParams, new_params);
    });
    paramsProjIncreStore.on('load', function (store, records, successful, operation) {
        if (successful && records.length > 0) {
            paramsProjIncreStore.insert(0, new paramsProjIncreModel());
        }
    });
    paramsProjIncreBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
        displayField: 'iname',
        valueField: 'projectIncre',
        store: paramsProjIncreStore,
        margin: '1 1 1 1',
        labelWidth: 70,
        width: contentPanel.getWidth() / 5,
        labelAlign: 'left',
        fieldLabel: '项目增量',
        emptyText: '--请选择增量--',
        forceSelection: true,
        readOnly: true, // 不可编辑
        listeners: {
            change: function() {
                var inc = this.getValue();
				consZpFileBox.setValue('');
				consCodeBox.setValue('');
                var protype = paramsProjTypeBox.getValue();
				if((jumpflag == 3 || jumpflag == 4)&& protype!=1){
                    promiseResolve('增量初始化结束');
                }
            }, beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        if (null != text) {
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        }
                    });
                    combo.expand();
                    return false;
                }
            },collapse : function(e){
                loadCodeList();
            }
        }
    });

    //-----------paramsModelTypeBox query bengi
    //-------模块类型 begin-----------
    Ext.define('modeltypeNamesModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'int',
            useNull: true
        }, {
            name: 'idepName',
            type: 'string'
        }]
    });
    var modeltypeNamestore = Ext.create('Ext.data.Store', {
        autoLoad: loadflag,
        autoDestroy: true,
        model: 'modeltypeNamesModel',
        proxy: {
            type: 'ajax',
            url: 'queryRollbackModeltype.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'totalCount'
            }
        }
    });
    modeltypeNamestore.on('beforeload', function(store, options) {
        var new_params =
            {
                iid: runhisIId,
            };
        Ext.apply(modeltypeNamestore.proxy.extraParams, new_params);
    });
    paramsModelTypeBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
        store: modeltypeNamestore,
        displayField: 'idepName',
        valueField: 'idepName',
        margin: '1px',
        labelWidth: 70,
        labelAlign: 'right',
        width: contentPanel.getWidth() / 5,
        readOnly: readOnly,
        fieldLabel: '模块类型<b  style="color:red;">*</b>:',
        emptyText: "--请输入模块类型--",
        triggerAction: 'all',
        editable: true,
        multiSelect: true,
        queryMode: 'local',
        anyMatch: true,
        forceSelection:true,
        listeners: {
            select: function() {
            }, beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        if (null != text) {
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        }
                    });
                    combo.expand();
                    return false;
                }
            },change: function() {
				if(jumpflag != 4 && jumpflag != 3){
					consZpFileBox.setValue('');
				}
            },collapse : function(e){
                var modeltype = this.getValue();
                confItemConfStore.getProxy().setExtraParam("modeltype", modeltype);
                loadconfItemConf();
            }
        }
    });
    //------------------配置全局参数 end-------------

    //------------------配置构建参数 begin---------------

	/*Ext.define('zpfilenameModel', {
		extend: 'Ext.data.Model',
		fields: [{
			name: 'iid',
			type: 'int',
			useNull: true
		}, {
			name: 'zpfilename',
			type: 'string'
		}]
	});
	zpfilenamestore = Ext.create('Ext.data.Store', {
		autoLoad: false,
		autoDestroy: true,
		model: 'zpfilenameModel',
		proxy: {
			type: 'ajax',
			url: 'ciQueryZpFile.do',
			reader: {
				type: 'json',
				root: 'dataList',
				totalProperty: 'totalCount'
			}
		}
	});
	zpfilenamestore.on('beforeload', function(store, options) {
		var projType = paramsProjTypeBox.getValue();//项目类型
		var projNum = paramsProjNumBox.getValue();//项目编码
		var projIncre = paramsProjIncreBox.getValue();//项目增量
		var new_params =
		{
			syscode: syscode,
			projType: projType,
			projNum: projNum,
			projIncre: projIncre,
		};
		Ext.apply(zpfilenamestore.proxy.extraParams, new_params);
	});

	consZpFileBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		store: zpfilenamestore,
		displayField: 'zpfilename',
		valueField: 'iid',
		margin: '5px',
		labelWidth: 70,
		labelAlign: 'right',
		width: contentPanel.getWidth() / 5,
		//fieldLabel: '制品文件<b  style="color:red;">*</b>',
		fieldLabel: '制品文件',
		emptyText: "--请选择制品文件--",
		triggerAction: 'all',
		editable: true,
		multiSelect: true,
		queryMode: 'local',
		anyMatch: true,
	});*/
	consZpFileBox = Ext.create('Ext.form.TextField', {
		name: 'zpfilename',
		labelWidth: 70,
		margin: '1px',
		width: contentPanel.getWidth() / 5,
		fieldLabel: '制品文件',
		readOnly:true,
        hidden:true,
		labelAlign: 'right',
		emptyText: "--请输入制品文件--",
		listeners: {
			render: function(p) {
				p.getEl().on('click', function(p) {
					if(jumpflag != 4){
						createZP();
					}
				});
				p.getEl().on('mouseover', function(p1) {
					updateTip(p);
				});
			}
		}
	});

    Ext.define('listCodeModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'long'
        }, {
            name: 'codelist',
            type: 'string'
        }]
    });


    listCodeStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 50,
        model: 'listCodeModel',
        proxy: {
            type: 'ajax',
            url: 'queryCICDList.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        },
        listeners: {
            load: function(store) {
                store.insert(0,{ iid: '-1', codelist: '请选择' });
            }
        }
    });
    listCodeStore.on('beforeload',function(store, records, successful, operation){
        let pt = paramsProjTypeBox.getValue();//项目类型
        let pn = paramsProjNumBox.getValue();//项目编码
        let pi = paramsProjIncreBox.getValue();//项目增量
        var new_params =
            {
                syscode: syscode,
                projType: pt,
                projNum: pn,
                projIncre: pi,
            };
        Ext.apply(listCodeStore.proxy.extraParams, new_params);
    });
    listCodeStore.on('load', function (store, records, successful, operation) {
    });
	consCodeBox = Ext.create('Ext.ux.ideal.form.ComboBox', {
		margin: '1 1 1 1',
		labelWidth: 70,
		labelAlign: 'left',
		width: contentPanel.getWidth() / 5,
		displayField: 'codelist',
		valueField: 'codelist',
		store: listCodeStore,
		readOnly: readOnly,
		fieldLabel: '代码清单',
        hidden: true,
		emptyText: "--请输入码清单--",
		listeners: {
			select: function(){
				let name = this.getValue();
				if(name=='请选择'){
					consCodeBox.setValue('');
				}
			},
			change: function() {
				var codelist = this.getValue();
				confItemConfStore.getProxy().setExtraParam("codelist", codelist);
				loadconfItemConf();
			}
		}
	});
    //------------------配置构建参数 end-----------------
    //------------------配置部署参数 begin-----------------

    Ext.define('serviceGridPanelModel',
        {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'iid',
                    type: 'long',
                    useNull: true
                },
                {
                    name : 'bussid',
                    type : 'long'
                },
                {
                    name : 'envid',
                    type : 'long'
                },
                {
                    name: 'envname',
                    type: 'string'
                },
                {
                    name : 'hostid',
                    type : 'long'
                },
                {
                    name: 'servername',
                    type: 'string'
                },
                {
                    name: 'ip',
                    type: 'string'
                },
                {
                    name: 'port',
                    type: 'int'
                },
                {
                    name: 'modeltype',
                    type: 'string'
                },
                {
                    name: 'appid',
                    type: 'string'
                },
                {
                    name : 'resid',
                    type : 'long'
                },
                {
                    name: 'resgroupname',
                    type: 'string'
                },
                {
                    name: 'priority',
                    type: 'string'
                },
                {
                    name: 'agent_id',
                    type: 'long'
                }
            ]
        });

    serviceGridPanelStore = Ext.create('Ext.data.Store',
        {
            autoLoad: false,
            autoDestroy: true,
            model: 'serviceGridPanelModel',
            proxy:{
                type: 'ajax',
                url: 'queryCICDDeployParamStore.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'totalCount'
                }
            }
        });

    serviceGridPanelStore.on('beforeload', function(store, options) {
        var paramName = envInfoTf.getValue();
        var new_params =
            {
                envname: paramName,
                bussid: bussid == '' ? 0 : bussid
            };
        Ext.apply(serviceGridPanelStore.proxy.extraParams, new_params);
    });

    serviceGridPanelStore.on('load', function(store, options) {
        //deployGrid.getSelectionModel().selectAll();//选中所有数据
        var scount=serviceGridPanelStore.getCount();
        var s = new Set();
        for(var i=0;i<serviceGridPanelStore.getCount();i++){
            var record = serviceGridPanelStore.getAt(i);
            s.add(record.data.ip);
        }
        document.getElementById("deployParamName").innerHTML="<p>已选择"+s.size+"台服务器，"+scount+"条节点记录</p>";

        loadconfItemConf();
    });

    Ext.define('envModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'envid',
            type: 'long'
        }, {
            name: 'envname',
            type: 'string'
        }]
    });
    var envRollbackStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        pageSize: 50,
        model: 'envModel',
        proxy: {
            type: 'ajax',
            url: 'queryRollbackEnvname.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        }
    });
    envRollbackStore.on('beforeload', function(store, options) {
        var new_params =
            {
                iid : runhisIId,
            };
        Ext.apply(envRollbackStore.proxy.extraParams, new_params);
    });
    envInfoTf = Ext.create('Ext.form.field.ComboBox', {
        displayField: 'envname',
        valueField: 'envname',
        store: envRollbackStore,
        fieldLabel: '环境信息<b  style="color:red;">*</b>',
        emptyText: '--请选择环境--',
        margin: '0 25px 0 0',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        readOnly: readOnly,
        editable: true,
        typeAhead: true,
        triggerAction: 'all',
        multiSelect: true,
        anyMatch: true,
        //forceSelection: true,
        listeners: {
            change: function() {

            }, beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        if (null != text) {
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        }
                    });
                    combo.expand();
                    return false;
                }
            },collapse : function(e){
                let envlist=this.getValue();
                confItemConfStore.getProxy().setExtraParam("envname", envlist);
                if(serviceGridPanelStore.getCount() == 0){
                    serviceGridPanelStore.load();
                    envinfo=[];//先清空后放入
                    envinfo = envinfo.concat(envlist);
                }else{
                    Ext.MessageBox.confirm("提示", "已经存在数据是否需要重新加载,加载后会改变数据,请确认?", function (btnId) {
                        if (btnId == "yes") {
                            serviceGridPanelStore.load();
                            envinfo=[];//先清空后放入
                            envinfo = envinfo.concat(envlist);
                        }else{
                            envInfoTf.setValue(envinfo);
                        }
                    });
                }
                document.getElementById("deployParamName").innerHTML="<p>已选择"+0+"台服务器，"+0+"条节点记录</p>";
            }
        }
    });
    var serverPanel = Ext.create('Ext.panel.Panel', {
        //width: '100%',
        border: false,
        autoScroll: false,
        region: 'center',
        html: '<p id="deployParamName">已选择0台服务器，0条节点记录</p>'
    });

    modifyPriorityCICD = Ext.create("Ext.Button",
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '修改优先级',
            hidden : true,
            handler: deployModifyPriority
        });

    addServiceBut = Ext.create('Ext.button.Button', {
        text: '添加',
        cls: 'Common_Btn',
        hidden : readOnly,
        handler: function() {
            addServiceRows();
        }
    });

    Ext.define('CICDPersonInChargeModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'id',
            type: 'long'
        }, {
            name: 'name',
            type: 'string'
        }, {
            name: 'logname',
            type: 'string'
        }]
    });
    var queryCICDPersonInChargeStore = Ext.create('Ext.data.Store', {
        autoLoad: loadflag,
        autoDestroy: true,
        pageSize: 50,
        model: 'CICDPersonInChargeModel',
        proxy: {
            type: 'ajax',
            url: 'queryCICDReviewerInfo.do',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        },
        listeners: {
            load: function(store) {
                store.insert(0,{ id: '-1', name: '请选择' });
            }
        }
    });
    queryCICDPersonInChargeStore.on('beforeload', function(store, options) {
        var new_params =
            {
                bussid: bussid,

            };
        Ext.apply(queryCICDPersonInChargeStore.proxy.extraParams, new_params);
    });
    deployParamPeople = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '审核人<b  style="color:red;'+ds+'">*</b>',
        emptyText: '--请输入审核人--',
        store: queryCICDPersonInChargeStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable: true,
        queryMode: 'local',
        mode: 'local',
        margin: '1px',
        labelWidth: 70,
        readOnly: readOnly,
        labelAlign: 'right',
        width: contentPanel.getWidth() / 5,
        forceSelection: true,
        listeners: {
            select: function() {
                let id = this.getValue();
                if(id==-1){
                    deployParamPeople.setValue('');
                }
            }, beforequery: function(e) {
                var combo = e.combo;
                if (!e.forceAll) {
                    var value = e.query;
                    combo.store.filterBy(function(record, id) {
                        var text = record.get(combo.displayField);
                        if (null != text) {
                            return (text.toLowerCase().indexOf(value.toLowerCase()) != -1);
                        }
                    });
                    combo.expand();
                    return false;
                }
            }
        }
    });

    var autoStore = Ext.create('Ext.data.Store',{
        fields :['id','name'],
        data:[{
            'id':'0',
            'name':'是'
        },{
            'id':'1',
            'name':'否'
        }]
    })

    autoTestPeople = Ext.create('Ext.form.field.ComboBox', {
        fieldLabel: '是否发送自动化测试',
        //emptyText: '--请输入审核人--',
        store: autoStore,
        displayField: 'name',
        valueField: 'id',
        triggerAction: 'all',
        editable: true,
        queryMode: 'local',
        mode: 'local',
        margin: '1px',
        labelWidth: 70,
        readOnly: readOnly,
        labelAlign: 'right',
        width: contentPanel.getWidth() / 5,
        forceSelection: true
    });

    var publishContent = Ext.create('Ext.panel.Panel', {
        margin: '1px',
        height: 33,
        border: false,
        autoScroll: true,
        region: 'center',
        labelWidth: 70,
        labelAlign: 'right',
        width: '25%',
        listeners: {
            afterrender: function() {
                var elemsInit = Array.prototype.slice.call(document.querySelectorAll('.js-switch-small2'));
                $(elemsInit).change(function(e) {
                    if (e.target.checked) {
                        hdStartflag = 2;
                        grayDeployScale.show();
                        deployGrid.columns[12].show();
                        modifyPriorityCICD.show();
                        infomationDescPanel.show();
                    } else {
                        hdStartflag = 1;
                        grayDeployScale.hide();
                        deployGrid.columns[12].hide();
                        modifyPriorityCICD.hide();
                        infomationDescPanel.hide();
                    }
                })
                setTimeout(function(){
                    for (var i = 0; i < elemsInit.length; i++) {
                        var switchery = new Switchery(elemsInit[i], { size: 'small' });
                    }
                },100)
            }
        },
        html: '<div ><span style="float: left;margin-top: 5px;font-size: 14px;">灰度发布:</span><span style="float: left;margin-top: 5px;margin-left:50px;" >否&nbsp&nbsp<input type="checkbox" '+disa+' class="js-switch-small2" '+hdblstate+' />&nbsp&nbsp是</span></div>'
    });

    grayDeployScale = Ext.create('Ext.panel.Panel', {
        height: 35,
        margin: '1px',
        labelWidth: 66,
        labelAlign: 'right',
        width: '25%',
        border: false,
        autoScroll: true,
        hidden: false,
        region: 'center',
        listeners: {
            afterrender: function() {
                const App = {
                    data() {
                        return {
                            value: 45,
                        };
                    },
                };
                const app = Vue.createApp(App);
                app.use(ElementPlus);
                vm = app.mount("#app");
                vm.value = hdblvalue;
            }
        },
        html: '<div id="app"><div class="slider-demo-block"><span style="float: left;margin-top: 4px;">灰度部署比例<b  style="color:red;">*</b>:</span><span><el-slider '+disa+' style="width:55%;margin-left:100px;" v-model="value" show-input /></span></div></div>'
    });

    infomationDescPanel = Ext.create('Ext.panel.Panel', {
        width: '100%',
        margin:'1px',
        border: false,
        autoScroll: true,
        region: 'center',
        hidden : true,
        html: '<span style="color:red;">选定服务器优先级相同时生效</span>'
    });

    var environmentalReminder = Ext.create('Ext.panel.Panel', {
        border: false,
        autoScroll: true,
        region: 'center',
        html: '<span style="color:red;">以下显示的服务器及节点都会被执行，可以通过删除操作，取消一个或多个服务器的执行。</span>'
    });
    deployGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'customize_panel_back',
        //ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        multiSelect: true,
        collapsible: false,
        plugins: Ext.create('Ext.grid.plugin.CellEditing', { clicksToEdit: 1 }),
        store: serviceGridPanelStore,
        viewConfig: {
            enableTextSelection: true
        },
        selModel : checkModel,
        padding: grid_space,
        columns: [{
            header: '序号',
            xtype: 'rownumberer',
            dataIndex: 'index',
            width: 70
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'bussid',
            hidden: true,
            text: '业务系统id'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'envid',
            hidden: true,
            text: '环境id'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'envname',
            flex: 1,
            text: '环境',
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'hostid',
            hidden: true,
            text: '服务器id'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'servername',
            flex: 1,
            text: '服务器名称'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'ip',
            flex: 1,
            text: 'IP'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'port',
            flex: 1,
            text: '端口'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'modeltype',
            flex: 1,
            text: '模块类型'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'appid',
            flex: 1,
            text: '应用标识'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'resid',
            hidden: true,
            text: '资源组id'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'resgroupname',
            flex: 1,
            text: '资源组'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'priority',
            flex: 1,
            hidden: true,
            text: '优先级',
            editor: new Ext.form.NumberField({
                allowDecimals: false, // 允许小数点？
                allowNegative: false, // 允许负数？
                maxValue: 100,
                minValue: 0
            })
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'agent_id',
            flex: 1,
            hidden: true,
            text: 'agent_id'
        },{
            text: '操作',
            dataIndex: 'opera',
            flex: 1,
            hidden : readOnly,
            renderer: function(value, metaData, record,index) {
                return "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"delService('" + index + "');\">" + "删除</a>";
            },
            sortable: false
        }
        ],
        dockedItems: [
            {
                items: [
                    {
                        xtype: 'toolbar',
                        border: false,
                        dock: 'top',
                        items: [envInfoTf, serverPanel, '->',addServiceBut, {
                            xtype: 'button',
                            cls: 'Common_Btn',
                            text: '删除',
                            hidden : readOnly,
                            handler: function() {
                                delService();
                            }
                        }, modifyPriorityCICD ]
                    },
                    {
                        xtype: 'toolbar',
                        border: false,
                        dock: 'top',
                        items: [environmentalReminder]
                    }
                ]
            }
        ]
    });
    startTime = Ext.create('Go.form.field.DateTime', {
        emptyText: '--请输入时间--',
        editable: false,
        readOnly: readOnly,
        format: 'Y-m-d H:i:s',
        name: 'startTime',
        hidden : true,
        width: '20%',
    });
    deployFromPanel = Ext.create('Ext.form.Panel', {
        region: 'north',
        border: false,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [paramsProjTypeBox, paramsProjNumBox, paramsProjIncreBox,autoTestPeople]
        }, {
            xtype: 'toolbar',
            dock: 'top',
			items: [paramsModelTypeBox, consZpFileBox,consCodeBox, deployParamPeople]
        }, {
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: 'radio',
                name: 'execstartname',
                id: 'execstart1',
                inputValue: '1',
                boxLabel: '触发执行',
                checked: true,
                hidden : true,
                margin: '5 0 5 5',
                labelWidth: 70,
                labelAlign: 'right',
                readOnly: readOnly,
                listeners: {
                    focus: function(field, e) {
                    }
                }
            }, {
                xtype: 'radio',
                name: 'execstartname',
                id: 'execstart2',
                inputValue: '2',
                labelWidth: 70,
                margin: '5 5 5 25',
                labelAlign: 'right',
                readOnly: readOnly,
                hidden : true,
                boxLabel: '定时执行',
                listeners: {
                    focus: function(field, e) {
                    }
                }
            }, startTime, {
                xtype: 'radio',
                name: 'execstartname',
                id: 'execstart3',
                inputValue: '3',
                labelWidth: 70,
                margin: '5 5 5 25',
                labelAlign: 'right',
                boxLabel: '周期执行',
                hidden : true,
                readOnly: readOnly,
                listeners: {
                    focus: function(field, e) {
                    }
                }
            }, {
                xtype: 'textfield',
                name: 'execcontent',
                labelAlign: 'right',
                width: '20%',
                readOnly: readOnly,
                hidden : true,
                emptyText: '--请输入周期内容--',
                listeners: {
                    render: function(p) {
                        p.getEl().on('click', function(p) {
                            createTask();
                        });
                    }
                }
            }]
        }, {
            xtype: 'textarea',
            fieldLabel: '变更说明',
            emptyText: '',
            name: 'citaskdesc',
            id : 'bgsm',
            margin: '1px',
            labelWidth: 70,
            labelAlign: 'right',
            readOnly: readOnly,
            height: contentPanel.getHeight() /22,
            maxLength: 2000
        }, publishContent, grayDeployScale,infomationDescPanel]
    });

    //字段模型begin
    Ext.define('cistartModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'iid',
                type: 'int',
                useNull: true
            },
            {
                name: 'iparamname',
                type: 'string'
            },
            {
                name: 'iparamvalue',
                type: 'string'
            },
            {
                name: 'iparamtype',
                type: 'string'
            },
            {
                name: 'iparamdesc',
                type: 'string'
            },
            {
                name: 'inewdata',
                type: 'string'
            },
            {
                name: 'iencryptype',
                type: 'string'
            }
        ]
    });

    //数据源begin
    consParamsConfStore = Ext.create('Ext.data.Store', {
        autoLoad: loadflag,
        autoDestroy: true,
        pageSize: 30,
        model: 'cistartModel',
        proxy: {
            type: 'ajax',
            url: 'selectParamByTaskId.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    consParamsConfStore.on ('beforeload', function (store, options)
    {
        var varibleByTaskId = taskid == null|| taskid =='' ?0:taskid;
        var new_params =
            {
                itaskid : varibleByTaskId
            };
        Ext.apply (consParamsConfStore.proxy.extraParams, new_params);
    });


    consParamsConfStore.on('load', function(store, options) {
        feedbackData();
    });
    var consEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit : 2
    });

     consGridPanel = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'customize_panel_back',
        plugins : [ consEditing ],
        multiSelect: true,
        collapsible: false,
        columns: [{
            header: '序号',
            xtype: 'rownumberer',
            width: 40
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'iid',
            hidden: true,
            text: 'id'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'iparamname',
            flex: 1,
            text: '参数名',

        }, {
            text: '参数类型',
            dataIndex: 'iparamtype',
            flex: 1,
            sortable: true,
            renderer: function(value, p, r) {
                if(value == '1'){
                    return 'String 加密';
                }else if(value == '0'){
                    return 'String';
                }
            },
        },
            {
                text: '参数值',
                dataIndex: 'iparamvalue',
                flex: 2,
                sortable: true,
                renderer: function(value, p, r) {
                    let ptyp = r.get('iparamtype');
                    let showValue = value;
                    if (ptyp == '1') {
                        let decodeVal = getSMEncode(value,0);
                        if(!isStrNull(decodeVal)){
                            value = decodeVal;
                            r.data.iparamvalue = decodeVal;
                        }
                        let xing = "";
                        let len = value.length;
                        for (let i = 0; i < len; i++) {
                            xing += "●";
                        }
                        showValue = xing;
                    }
                    console.log('sssss',showValue)
                    return showValue;
                },
                getEditor: function(record) {
                    if (record.get('iparamtype') != '1') { //String
                        return Ext.create('Ext.grid.CellEditor', {
                            field: Ext.create('Ext.form.field.Text', {
                                selectOnFocus: true
                            })
                        });
                    } else { //String 加密
                        return Ext.create('Ext.grid.CellEditor', {
                            field: Ext.create('Ext.form.field.Text',
                                {
                                    selectOnFocus: true,
                                    inputType: 'password'
                                })
                        });
                    }
                }
            }, {
                xtype: 'gridcolumn',
                dataIndex: 'iparamdesc',
                flex: 1,
                text: '描述',

            }],
        store: consParamsConfStore,
        viewConfig: {
            enableTextSelection: true
        },
        padding: grid_space,
    });

    var serviceAllPanel = Ext.create('Ext.panel.Panel', {
        region: 'center',
        width: '100%',
        height: 160,
        layout: 'fit',
        items: [consGridPanel]
    });

    var centerset = {
        xtype: 'fieldset',
        title: '自定义参数',
        layout: 'form',
        collapsible: true,
        items: [serviceAllPanel]
    };

    tabDeployParam = new Ext.FormPanel({
        autoScroll: false,
        region: 'south',
        labelAlign: 'top',
        frame: true,
        layout: 'form',
        cls:'cicd_set_panel',
        items: [centerset]
    });


    var deployParamPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        layout: 'border',
        //cls : 'form_style',
        items: [deployFromPanel, deployGrid,tabDeployParam]
    });

    function deployModifyPriority() {
        //校验end
        var bscs = deployGrid.getSelectionModel().getSelection();//部署参数
        if (bscs.length < 1) {
            Ext.Msg.alert('提示', '请选择需修改的优先级信息!');
            return;
        }


        var modifyNumberField = Ext.create('Ext.form.NumberField', {
            fieldLabel: '优先级:',
            width: contentPanel.getWidth() * 0.25,
            labelWidth: 60,
            emptyText: '--请输入优先级--',
            xtype: 'numberfield',
            allowDecimals: false, // 允许小数点？
            allowNegative: false, // 允许负数？
            margin: '5 5 5 5',
            maxValue: 100,
            minValue: 0
        })

        var modifyPanel = Ext.create('Ext.Panel', {
            region: 'center',
            padding: grid_space,
            dockedItems: [{
                layout: 'column',
                xtype: 'toolbar',
                dock: 'top',
                border: false,
                items: [modifyNumberField]
            }]
        });
        const modifyButtonWin = Ext.create('Ext.window.Window', {
            title: '请确认是否发布任务',
            width: contentPanel.getWidth() * 0.3,
            draggable: true,// 禁止拖动
            resizable: false,// 禁止缩放
            region: 'center',
            items: [modifyPanel],
            buttonAlign: 'center',
            modal: true,
            buttons: [{
                xtype: 'button',
                text: '确定',
                cls: 'Common_Btn',
                hidden: false,
                handler: function() {
                    let num = modifyNumberField.value;
                    if (num == null || num == '') {
                        Ext.Msg.alert('提示', '优先级不能为空!');
                        return;
                    } else if (num > 100) {
                        Ext.MessageBox.alert("提示", "优先级不能大于100！");
                        return;
                    }
                    for (var i = 0; i < bscs.length; i++) {
                        var recordtoedit = bscs[i];
                        recordtoedit.set('priority',num);
                    }
                    modifyButtonWin.close(this);
                }
            },{
                xtype: 'button',
                text: '取消',
                cls: 'Common_Btn',
                hidden: false,
                handler: function() {
                    modifyButtonWin.close(this);
                }
            }]
        }).show();
        modifyButtonWin.on('resize', function(a) {

        });
    }

    function addServiceRows() {
        var fwData = "";//部署参数
        for (var i = 0; i < serviceGridPanelStore.getCount(); i++){
            var record = serviceGridPanelStore.getAt(i);
            var fw = Ext.JSON.encode(record.data);
            if (i == 0) {
                fwData = fwData + fw;
            } else {
                fwData = fwData + "," + fw;
            }
        }
        addSelectInfo(serviceGridPanelStore,fwData);
    }

    //------------------002配置全部参数 end-----------------
    //------------------003选择配置项 begin-----------------
    var confItemIpAddrText = Ext.create('Ext.form.TextField', {
        fieldLabel: 'IP地址',
        emptyText: '--请输入IP地址--',
        name: 'ipAddr',
        editable: true,
        typeAhead: true,
        margin: '0 0 0 20',
        labelWidth: 50,
        width: '25%',
        xtype: 'textfield',
        listeners: {
            change: function() {
                var ipAddr = this.getValue();
                confItemConfStore.getProxy().setExtraParam("ip", ipAddr);
                confItemConfStore.clearFilter(true);
                //confItemConfStore.filter({property:'ipaddr',value:ipAddr})
                confItemConfStore.filterBy(function(record, id) {
                    if (record.data.ipaddr.indexOf(ipAddr) != -1) {
                        return true;
                    } else
                        return false;
                });
            }
        }
    });

    var confItemConfNameText = Ext.create('Ext.form.TextField', {
        fieldLabel: '配置项名称',
        emptyText: '--请输入配置项名称--',
        name: 'confname',
        editable: true,
        typeAhead: true,
        labelWidth: 80,
        margin: '0 0 0 20',
        width: '30%',
        xtype: 'textfield',
        listeners: {
            change: function() {
                var confname = this.getValue();
                confItemConfStore.getProxy().setExtraParam("confname", confname);
                confItemConfStore.clearFilter(true);
                //confItemConfStore.filter({property:'confname',value:confname})
                confItemConfStore.filterBy(function(record, id) {
                    if (record.data.confname.indexOf(confname) != -1) {
                        return true;
                    } else
                        return false;
                });
            }
        }
    });

    //按钮
    var confItemFromPanel = Ext.create('Ext.form.Panel', {
        region: 'north',
        border: false,
        hidden : readOnly,
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [confItemIpAddrText, confItemConfNameText, '->',{
                xtype: 'button',
                cls: 'Common_Btn',
                margin : '0 25px 0 0',
                textAlign: 'center',
                text: '重置',
                handler: function() {
                    confItemIpAddrText.setValue('');
                    confItemConfNameText.setValue('');
                }
            }]
        }]
    });

    //字段模型begin
    Ext.define('cServerModel', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'iid',
            type: 'string'
        }, {
            name: 'envname',
            type: 'string'
        }, {
            name: 'modeltype',
            type: 'string'
        }, {
            name: 'ipaddr',
            type: 'string'
        }, {
            name: 'furl',
            type: 'string'
        }, {
            name: 'confname',
            type: 'string'
        }, {
            name: 'ihost_id',
            type: 'string'
        }, {
            name: 'ihost_port',
            type: 'string'
        }]
    });

    //数据源begin
    confItemConfStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 30,
        model: 'cServerModel',
        proxy: {
            type: 'ajax',
            url: 'queryCiConfServerList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    confItemConfStore.on('beforeload', function(store, options) {
        var new_params =
            {
                bussid: bussid
            };
        Ext.apply(confItemConfStore.proxy.extraParams, new_params);
    });
    confItemConfStore.on('load', function(store, options) {
        //从新计算
        var scount=confItemConfStore.getCount();
        document.getElementById("serverconfig").innerHTML = "<p>已选择" + scount + "条配置文件</p>";
    });

    var confItemPanel = Ext.create('Ext.panel.Panel', {
        width: '100%',
        border: false,
        autoScroll: false,
        region: 'center',
        html: '<p id="serverconfig">已选择0条配置文件</p>'
    });

    addItem = Ext.create("Ext.Button",
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '添加',
            hidden : readOnly,
            handler: confItemSaveRows
        });
    delItem = Ext.create("Ext.Button",
        {
            xtype: 'button',
            cls: 'Common_Btn',
            text: '删除',
            hidden : readOnly,
            handler: function(){
                confItemDelRows();
            }
        });
    //数据模型grid begin
    confItemGrid = Ext.create('Ext.grid.Panel', {
        region: 'center',
        cls: 'customize_panel_back',
        //ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        multiSelect: true,
        collapsible: false,
        plugins: Ext.create('Ext.grid.plugin.CellEditing', { clicksToEdit: 1 }),
        store: confItemConfStore,
        viewConfig: {
            enableTextSelection: true
        },
        selModel: checkModel2,
        padding: grid_space,
        columns: [{
            header: '序号',
            xtype: 'rownumberer',
            width: 70
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'iid',
            hidden: true,
            text: 'id'
        },{
            xtype: 'gridcolumn',
            dataIndex: 'ihost_id',
            hidden: true,
            text: 'ihost_id'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'envname',
            flex: 1,
            text: '环境',
            renderer: function(value, metaData, record, colIndex, store, view) {
                metaData.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'modeltype',
            flex: 1,
            text: '模块',
            renderer: function(value, metaData, record, colIndex, store, view) {
                metaData.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'ipaddr',
            flex: 1,
            text: 'IP地址',
            renderer: function(value, metaData, record, colIndex, store, view) {
                metaData.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        },{
            xtype: 'gridcolumn',
            dataIndex: 'ihost_port',
            hidden: true,
            text: 'ihost_port'
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'furl',
            flex: 1,
            text: '分发路径',
            renderer: function(value, metaData, record, colIndex, store, view) {
                metaData.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        }, {
            xtype: 'gridcolumn',
            dataIndex: 'confname',
            flex: 1,
            text: '配置项名称',
            renderer: function(value, metaData, record, colIndex, store, view) {
                metaData.tdAttr = 'data-qtip="' + value + '"';
                return value;
            }
        }, {
            text: '操作',
            dataIndex: 'opera',
            width: 160,
            hidden : readOnly,
            renderer: function(value, metaData, record,index) {
                var iid = record.get('iid');
                var confname = record.get('confname');
                var a = "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"confItemDelRows('" + index + "');\">" + "删除 | </a>";
                var b = "<a href=\"#\" style=\"text-decoration:none;\" valign=\"middle\" onclick=\"shwoConfigFileContent('" + iid +"','"+ confname+ "');\">" + "查看 </a>";
                return a + b
            },
            sortable: false
        }],
        dockedItems: [{
            xtype: 'toolbar',
            dock: 'top',
            items: [{
                xtype: "label",
                html: '<b style="font-size:18px">请选择关联配置文件</b>'
            }, '->', addItem, delItem]
        }, confItemPanel]
    });
    var consItemPanel = Ext.create('Ext.panel.Panel', {
        border: false,
        layout: 'border',
        items: [confItemFromPanel, confItemGrid]
    });

    //保存行begin
    function confItemSaveRows() {
        var iidArr = [];//部署参数
        for(let i = 0;i<confItemConfStore.getCount();i++){
            var record = confItemConfStore.getAt (i);
            let itemIid =record.data.iid;
            iidArr[i]=itemIid;
        }
        agnetSet = new Set();
        agnetSet.clear();
        serviceGridPanelStore.data.items.forEach(record => {
            agnetSet.add(record.get('agent_id'));
        });
        selectAgent(confItemConfStore,iidArr);
    }
    //------------------选择配置项 end-----------------
    //------------------主panel begin-----------------
    let renderTo='cicdRollbackDeploy';

    var mainPanel = Ext.create('Ext.panel.Panel', {
        //title: 'Example Wizard',
        width: '100%',
        height: '100%',
        layout: 'card',
        bodyStyle: 'padding:15px',
        defaults: {
            border: false
        },
        items: [ sureFlowTaskStepPanel, deployParamPanel, consItemPanel],
        renderTo: renderTo
    });
    //------------------主panel end-----------------
    //------------------上一页 begin-----------------
    pageUp = function pageUp(btn) {
        navigate(mainPanel, "prev");
        if (btn == 2) {
            for (var i = 0; i < $("#ulstepid li").length; i++) {
                if ($("#ulstepid li").eq(i).attr('class') == 'on') {
                    countNum.push(parseInt($("#ulstepid li").eq(i - 1).children('span').text()))
                }
            }
        }
    }

    pageDown = function pageDown(btn) {
        navigate(mainPanel, "next");
        if (btn == 2) {
            for (var i = 0; i < $("#ulstepid li").length; i++) {
                if ($("#ulstepid li").eq(i).attr('class') == 'on') {
                    countNum.push(parseInt($("#ulstepid li").eq(i + 1).children('span').text()))
                }
            }
        }
    }
    //------------------上一页 end-----------------
    contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
        Ext.destroy(mainPanel);
        Ext.destroy(pageUp);
        Ext.destroy(pageDown);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    //查询图形化
    init();



});

//删除行begin
function confItemDelRows(iid) {
    if (iid != undefined) {
        Ext.Msg.confirm("提示", "确认删除?",
            function(btn) {
                if (btn == 'yes') {
                    var selectedRecord = confItemGrid.getStore().getAt(iid);
                    confItemGrid.getStore().remove(selectedRecord);
                    //从新计算
                    var scount=confItemConfStore.getCount();
                    document.getElementById("serverconfig").innerHTML = "<p>已选择" + scount + "条配置文件</p>";
                    //重新计算
                    confItemGrid.getView().refresh();
                } else {
                    return;
                }
            });
    } else {
        var records = confItemGrid.getSelectionModel().getSelection();
        if (records.length == 0) {
            Ext.Msg.show({
                title: '提示',
                msg: '请选择要删除的记录',
                buttons: Ext.Msg.OK,
                icon: Ext.Msg.INFO
            });
        } else {
            Ext.Msg.confirm("提示", "确认删除?",
                function(btn) {
                    if (btn == 'yes') {
                        Ext.each(records, function(record) {
                            confItemConfStore.remove(record);
                            //从新计算
                            var scount=confItemConfStore.getCount();
                            document.getElementById("serverconfig").innerHTML = "<p>已选择" + scount + "条配置文件</p>";
                            //重新计算
                            confItemGrid.getView().refresh();
                        });
                    } else {
                        return;
                    }
                });
        }
    }
}

//删除服务器 begin
function delService(iid) {
    if (iid != undefined) {
        Ext.Msg.confirm("提示", "确认删除?",
            function(btn) {
                if (btn == 'yes') {
                    //var records = deployGrid.getSelectionModel().getSelection();
                    //serviceGridPanelStore.remove(records);

                    var selectedRecord = deployGrid.getStore().getAt(iid);
                    deployGrid.getStore().remove(selectedRecord);

                    //从新计算
                    var scount=serviceGridPanelStore.getCount();
                    var s = new Set();
                    for(var i=0;i<serviceGridPanelStore.getCount();i++){
                        var record = serviceGridPanelStore.getAt(i);
                        s.add(record.data.ip);
                    }
                    document.getElementById("deployParamName").innerHTML="<p>已选择"+s.size+"台服务器，"+scount+"条节点记录</p>";
                    //重新计算
                    deployGrid.getView().refresh();
                    loadconfItemConf();
                } else {
                    return;
                }
            });
    } else {
        var records = deployGrid.getSelectionModel().getSelection();
        if (records.length == 0) {
            Ext.Msg.show({
                title: '提示',
                msg: '请选择要删除的记录',
                buttons: Ext.Msg.OK,
                icon: Ext.Msg.INFO
            });
        } else {
            Ext.Msg.confirm("提示", "确认删除?",
                function(btn) {
                    if (btn == 'yes') {
                        Ext.each(records, function(record) {
                            serviceGridPanelStore.remove(record);
                        });
                        //从新计算
                        var scount=serviceGridPanelStore.getCount();
                        var s = new Set();
                        for(var i=0;i<serviceGridPanelStore.getCount();i++){
                            var record = serviceGridPanelStore.getAt(i);
                            s.add(record.data.ip);
                        }
                        document.getElementById("deployParamName").innerHTML="<p>已选择"+s.size+"台服务器，"+scount+"条节点记录</p>";
                        //重新计算
                        deployGrid.getView().refresh();
                        loadconfItemConf();
                    } else {
                        return;
                    }
                });
        }
    };

}

//删除行end
function createTask() {
    var execcontent = deployFromPanel.getForm().findField('execcontent').getValue();
    if (icSPDBCreateTaskWin == undefined || !icSPDBCreateTaskWin.isVisible()) {
        icSPDBCreateTaskWin = Ext.create('Ext.window.Window', {
            title: '创建周期执行任务',
            modal: true,
            id: 'icSPDBCreateTaskWin',
            closeAction: 'destroy',
            constrain: true,
            autoScroll: true,
            upperWin: icSPDBCreateTaskWin,
            width: contentPanel.getWidth() * 0.8,
            height: contentPanel.getHeight() * 0.8,
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
            loader: {
                url: 'sus_CreateTaskPagePoc.do',
                params: {
                    execcontent: execcontent
                },
                autoLoad: true,
                autoDestroy: true,
                scripts: true
            }
        });
    }
    icSPDBCreateTaskWin.show();
}
//-----------------周期执行显示  end-------------------
function showCtrol(execRadioValue, value) {
    deployFromPanel.getForm().findField('execcontent').setValue(value);
    icSPDBCreateTaskWin.close();
}

//====================初始化begin=======================
function init() {
	if(jumpflag!=4){
		grayDeployScale.hide();
	}
	if(jumpflag==4){
        if(hdblS == 2){
            deployGrid.columns[12].show();
            grayDeployScale.show();
        }else{
            deployGrid.columns[12].hide();
            grayDeployScale.hide();
        }
	}

    var onlyTaskId = taskid == null || taskid == '' ? 0 : taskid;
    Ext.Ajax.request(
        {
            url: 'queryTaskFlowJson.do',
            timeout: 30000,
            params:
                {
                    iid: onlyTaskId,
                    flowtaskrunid: flowtaskrunid,
                },
            async: false,
            method: 'POST',
            success: function(response, opts) {
                var success = Ext.decode(response.responseText).success;
                var flowJsonString = Ext.decode(response.responseText).flowJson;
                if (success) {
                    if (flowJsonString != null && flowJsonString != '') {
                        initJson = JSON.parse(flowJsonString);
                        if(!isStrNull(initJson)){
                            initFlowRollbackExec(initJson);
                        }
                    }
                }
            },
            failure: function(response, ooptions) {
                Ext.Msg.alert('提示', '请求超时！');
            }
        });


}
function feedbackData(){
    Ext.Ajax.request(
        {
            url: 'queryRollbackIhdstartflag.do',
            timeout: 30000,
            params:
                {
                    iid : runhisIId
                },
            async: false,
            method: 'POST',
            success: function(response, opts) {
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    var iexec_policy = Ext.decode(response.responseText).iexec_policy;
                    var ihdstartflag = Ext.decode(response.responseText).ihdstartflag;
                    var itime_cycle = Ext.decode(response.responseText).itime_cycle;
                    var ichangedesc = Ext.decode(response.responseText).ichangedesc;
                    var iprojtype = Ext.decode(response.responseText).iprojtype;
                    var iprojectnum = Ext.decode(response.responseText).iprojectnum;
                    var iprojectincre = Ext.decode(response.responseText).iprojectincre;
                    var imodeltype = Ext.decode(response.responseText).imodeltype;
                    var izpfilename = Ext.decode(response.responseText).izpfilename;
                    var ireviewedby = Ext.decode(response.responseText).ireviewedby;
                    var ienvname = Ext.decode(response.responseText).ienvname;
                    var icodelist = Ext.decode(response.responseText).icodelist;
                    var ifullname = Ext.decode(response.responseText).ifullname;
                    var parsmslist = Ext.decode(response.responseText).parsmslist;
                    //自定义参数
                    if(!isStrNull(parsmslist)){
                        for(var i = 0 ;i < parsmslist.length; i++ ){
                            consGridPanel.getStore().insert(0, parsmslist[i]);
                        }
                    }

                    if(imodeltype !=undefined && imodeltype!=null &&imodeltype!=''){
                        let mtt = imodeltype.split(",");
                        paramsModelTypeBox.setValue(mtt);//模块类型
                    }
                    if(ienvname !=undefined && ienvname!=null &&ienvname!=''){
                        let ett = ienvname.split(",");
                        // envInfoTf.setValue(ett);//环境信息
                    }
                    //deployParamPeople.setValue(pasInt(ireviewedby));
                    // deployParamPeople.setRawValue(ifullname);

                    paramsProjTypeBox.setValue(pasInt(iprojtype));
                    Ext.getCmp('bgsm').setValue(ichangedesc);


                    if(iprojtype != 1){
                        listCodeStore.load();
                        asyncInitFuncPrjIncr(asyncInitFuncPrjIncr,iprojectnum,iprojectincre,izpfilename,icodelist).then(async result => {
                        })
                    }else{
                        listCodeStore.load();
                    };
                    //------------执行策略------------
                    if (iexec_policy == 1) {
                        Ext.getCmp("execstart1").setValue(true);
                    } else if (iexec_policy == 2) {
                        Ext.getCmp("execstart2").setValue(true);
                        startTime.setValue(itime_cycle);
                    } else if (iexec_policy == 3) {
                        Ext.getCmp("execstart3").setValue(true);
                        taskFromPanel.getForm().findField('execcontent').setValue(itime_cycle);
                    }
                }
            },
            failure: function(response, ooptions) {
                Ext.Msg.alert('提示', '请求超时！');
            }
        });
}
//====================初始化end=======================
//====================启动begin=======================
//判断字符串或对象是否为null
function isStrNull(str){
    if(str==null||str==''||str==undefined||str=='null'||str=='undefined'){
        return true;
    }else{
        return false;
    }
}
function startAssemblyLine(jsonFlow){

    var projType = paramsProjTypeBox.getValue();//项目类型
    var projNum = paramsProjNumBox.getValue();//项目编码
    var projIncre = paramsProjIncreBox.getValue();//项目增量
    var isAuto = autoTestPeople.getValue();//是否发送自动化测试
    var modelType = paramsModelTypeBox.getValue();//模块类型
    var citaskdesc = Ext.query('[name=citaskdesc]')[0].value;//变更说明
	var codeList = consCodeBox.getValue();//代码清单
    var envname = envInfoTf.getValue();//环境信息
    var paramPeople = deployParamPeople.getValue();//审核人
	var zpFile = consZpFileBox.getValue();//制品文件id
    var hdbl = vm.value;//灰度比例
    var execstart = 1;//执行侧率
    // var execcontent = '';//侧率内容
    if(hdStartflag == 2 && hdbl == 0){
        Ext.Msg.alert('提示', '灰度比例不能为0!');
        return;
    }
    // //执行策略
    // if (Ext.getCmp('execstart2').getValue()) {
    //     execstart = 2;
    //     execcontent = startTime.getValue();
    //     if (execcontent == null || execcontent == '') {
    //         Ext.Msg.alert('提示', '执行策略为定时执行时,执行时间不能为空!');
    //         return;
    //     } else {
    //         if (checkDate(execcontent)) {
    //             Ext.Msg.alert('提示', '启动时间必须大于当前时间!');
    //             return;
    //         } else {
    //             execcontent = dateFormat2(execcontent)
    //         }
    //     }
    // }
    // if (Ext.getCmp('execstart3').getValue()) {
    //     execstart = 3
    //     execcontent = deployFromPanel.getForm().findField('execcontent').getValue();
    //     if (execcontent == '' || execcontent == null) {
    //         Ext.Msg.alert('提示', '执行策略为周期执行时,执行内容不能为空!');
    //         return;
    //     }
    // }

    if (isStrNull(codeList)) {
        codeList = '';
    }

    var buscData = "[";//部署参数
    for (var i = 0; i < serviceGridPanelStore.getCount(); i++) {
        var record = serviceGridPanelStore.getAt(i);
        var bucs = Ext.JSON.encode(record.data);
        if (i == 0) {
            buscData = buscData + bucs;
        } else {
            buscData = buscData + "," + bucs;
        }
    }
    buscData += "]";



    var pzxData = "[";//配置项
    for (var i = 0; i < confItemConfStore.getCount(); i++) {
        var record = confItemConfStore.getAt(i);
        var pzx = Ext.JSON.encode(record.data);
        if (i == 0) {
            pzxData = pzxData + pzx;
        } else {
            pzxData = pzxData + "," + pzx;
        }
    }
    pzxData += "]";
    //制品文件
    var zpData = "";//制品参数
    if(zpFile != undefined && zpFile != null){
        var zpinfo = zpFile.split(",");
        for (var i = 0; i < zpinfo.length; i++) {
            var bucs = zpinfo[i];
            if (i == 0) {
                zpData = zpData + bucs;
            } else {
                zpData = zpData + "," + bucs;
            }
        }
    }
    //校验end
    var checkMask = new Ext.LoadMask(Ext.getBody(), {msg:"启动中请稍后..."});
    Ext.Msg.show({
        title: '提示',
        msg : '请确认是否启动该任务，如果有审核人，审核通过才能启动',
        buttons: Ext.Msg.YESNO,
        icon: Ext.Msg.QUESTION,
        buttonText: {
            yes: '启动',
            no: '取消'
        },
        fn: function(btn) {
            if (btn === 'yes') {

                let customData = "[";//自定义参数
                for (var i = 0; i < consParamsConfStore.getCount (); i++){

                    console.log("consParamsConfStore:",consParamsConfStore);

                    let record = consParamsConfStore.getAt (i);
                    let listdata = record.data;

                    console.log("record:",record);
                    console.log("listdata:",listdata)
                    if(listdata.iparamtype==1&&scriptEncryptSwitch&& !isStrNull(listdata.iparamvalue)){//加密类型进行加密传输
                        listdata.iparamvalue = getSMEncode(listdata.iparamvalue,1);
                    }
                    let zdycs = Ext.JSON.encode(listdata);
                    console.log("zdycs:",zdycs);
                    if (i == 0) {
                        customData = customData + zdycs;
                        console.log("customData:",customData);
                    }else {
                        customData = customData + "," + zdycs;
                        console.log("customData:",customData);
                    }
                }
                customData += "]";
                checkMask.show();
                Ext.Ajax.request({
                    url: 'cicdExecStartInfo.do',
                    method: 'POST',
                    timeout: 180000,
                    params: {
                        taskName:itaskName,
                        taskid: taskid,
                        syscode: syscode,
                        projType: projType,
                        projNum: projNum,
                        projIncre: projIncre,
                        modeltype: modelType,
                        citaskdesc: citaskdesc,
                        execstart: execstart,
                        // execcontent: execcontent,
						codelist: codeList,
                        paramPeople: paramPeople,
                        hdStartflag: hdStartflag,
                        hdbl: hdbl,
						zpData : zpData,
                        envname: envname,
                        zpfilename: zpFile,
                        // zpfileid: isStrNull(zpfileid)?'':zpfileid,
                        buscData: buscData,
                        jsonflow: jsonFlow,
                        customData: customData,
                        rollBackTaskId:taskRuniid,
                        isRollBack: fal,
                        isAuto:isAuto

                    },
                    success: function(response, opts) {
                        var message = Ext.decode(response.responseText).message;
                        var success = Ext.decode(response.responseText).success;
                        if (success) {
                            Ext.Msg.alert("提示", message);
                            if(paramPeople != undefined && paramPeople != null && paramPeople !=''){//审核人校验 cd端
                                depbackPage();
                            }else{
                                JumptoAssemblyLine();
                            }
                        } else {
                            Ext.Msg.alert("提示", message);
                        }
                        checkMask.hide();
                    },
                    failure: function(result, opts) {
                        checkMask.hide();
                        Ext.Msg.alert('提示', '保存失败!');
                    }
                });
            }
        }
    });
}
function submitDeployExecStart(jsonFlow) {

    if(!isStrNull(taskid)){
        let succ = isChildTaskDisable(taskid);
        if(!succ.success){
            Ext.Msg.alert('提示', '子任务名为：'+succ.taskName+'的状态为已禁用，不能启动');
            return;
        }
    }

    var projType = paramsProjTypeBox.getValue();//项目类型
    var projNum = paramsProjNumBox.getValue();//项目编码
    var projIncre = paramsProjIncreBox.getValue();//项目增量
    var modelType = paramsModelTypeBox.getValue();//模块类型
    var citaskdesc = Ext.query('[name=citaskdesc]')[0].value;//变更说明
	var codeList = consCodeBox.getValue();//代码清单
    var envname = envInfoTf.getValue();//环境信息
    var paramPeople = deployParamPeople.getValue();//审核人
	var zpFile = consZpFileBox.getValue();//制品文件id
    var hdbl = vm.value;//灰度比例
    var execstart = 1;//执行侧率
    var execcontent = '';//侧率内容
    if(hdStartflag == 2 && hdbl == 0){
        Ext.Msg.alert('提示', '灰度比例不能为0!');
        return;
    }
    // //执行策略
    // if (Ext.getCmp('execstart2').getValue()) {
    //     execstart = 2;
    //     execcontent = startTime.getValue();
    //     if (execcontent == null || execcontent == '') {
    //         Ext.Msg.alert('提示', '执行策略为定时执行时,执行时间不能为空!');
    //         return;
    //     } else {
    //         if (checkDate(execcontent)) {
    //             Ext.Msg.alert('提示', '启动时间必须大于当前时间!');
    //             return;
    //         } else {
    //             execcontent = dateFormat2(execcontent)
    //         }
    //     }
    // }
    // if (Ext.getCmp('execstart3').getValue()) {
    //     execstart = 3
    //     execcontent = deployFromPanel.getForm().findField('execcontent').getValue();
    //     if (execcontent == '' || execcontent == null) {
    //         Ext.Msg.alert('提示', '执行策略为周期执行时,执行内容不能为空!');
    //         return;
    //     }
    // }

    //校验 bengin
    if (projType == undefined || projType == null) {
        Ext.Msg.alert('提示', '项目类型不能为空!');
        return;
    }
    if (projType == 0 && (projNum == undefined || projNum == null || projNum == '')) {
        Ext.Msg.alert('提示', '项目编码不能为空!');
        return;
    }
    if (modelType == undefined || modelType == null || modelType == '') {
        Ext.Msg.alert('提示', '模块类型不能为空!');
        return;
    }
    if (envname == undefined || envname == null || envname =='') {
        Ext.Msg.alert('提示', '环境信息不能为空!');
        return;
    }

    if (serviceGridPanelStore.getCount() < 1) {
        Ext.Msg.alert('提示', '部署参数服务器信息为空,请选择!');
        return;
    }

    //校验项目增量
    if(projType == 0 && (projIncre == undefined || projIncre == null || projIncre == '')){
        //调用后台查询项目类型是否存在增量存在不填写报错
        if(paramsProjIncreStore !=null && paramsProjIncreStore.getCount()>0){
            Ext.Msg.alert('提示', '该项目编号下有项目增量请填写!');
            return;
        }
    }
    //校验end

    if(checkIsForbidden(jsonFlow)){
        Ext.MessageBox.buttonText.no = "取消";
        Ext.MessageBox.buttonText.yes = "确认";
        Ext.Msg.confirm('提示', '流水线存在禁用步骤，请确认', function(button, text) {
            if (button == "yes") {
                startAssemblyLine(jsonFlow);
            }
        });
    }else{
        startAssemblyLine(jsonFlow);
    }
}
//====================启动begin=======================
//--------------工具类 begin-----------------------------------------------
function dateFormat2(value) {
    if (null != value) {
        return Ext.Date.format(new Date(value), 'Y-m-d H:i:s');
    } else {
        return null;
    }
}
function checkDate(data) {
    var now = new Date().valueOf();
    var time = new Date(data).valueOf();
    if (now > time) {
        return true;
    } else {
        return false;
    }
}
//--------------工具类 end-----------------------------------------------
//--------------返回流水线页面
function depbackPage() {
    destroyRubbish();
    if (isTabSwitch){//多标签分支
        closeTabPanel();
    }else {
        let url = 'accessCICDFlowTask.do';
		if(jumpflag==3){
			url=dourl;
		}
        contentPanel.getLoader().load(
            {
                url: url,
                params:
                    {
                        /*iid : iid,*/
                        params : params,
                        contentPanelHeight: contentPanel.getHeight(),
                        windowScHeight: window.screen.height
                    },
                scripts: true
            });
        if (Ext.isIE) {
            CollectGarbage();
        }
    }
}
function JumptoAssemblyLine() {
    destroyRubbish();
    //depbackPage();
    let url = "accessConDelTaskMon.do";
    if(cicdflag == 'true'){
        url="accessConDelTaskMon_yw.do";
    }
    let params = {
        contentPanelHeight: contentPanel.getHeight(),
        windowScHeight: window.screen.height
    }
    if (isTabSwitch) {//多标签分支
        closeTabPanel();
        //获取菜单id
        Ext.Ajax.request({
            url: "cicd/getPublicMenuId.do",
            params: {
                url: url,
            },
            timeout: 180000,
            success: function (response, opts) {
                var message = Ext.decode(response.responseText).message;
                var success = Ext.decode(response.responseText).success;
                if (success) {
                    var groupid = Ext.decode(response.responseText).groupid;
                    var id = Ext.decode(response.responseText).id;
                    buttonOpenlabelClick(this,"任务监控",url,groupid,id,params);
                }else{
                    Ext.Msg.alert('提示', '页面打开失败,获取页面id异常！');
                    return;
                }
            }
        });
    }else{
        contentPanel.getLoader().load(
            {
                url: url,
                params:
                    {
                        /*iid : iid,*/
                        contentPanelHeight: contentPanel.getHeight(),
                        windowScHeight: window.screen.height
                    },
                scripts: true
            });
        if (Ext.isIE) {
            CollectGarbage();
        }
    }
}
function loadconfItemConf(){
    var envname = envInfoTf.getValue();//环境信息
	var codeList = consCodeBox.getValue();//代码清单
    var modelType = paramsModelTypeBox.getValue();//模块类型
	if ((envname != undefined && envname != null && envname !='') && (codeList != undefined && codeList != null && codeList !='') && (modelType != undefined && modelType != null && modelType !='')) {
		if(jumpflag != 4){
            confItemConfStore.loadData([],false);//清空数据
            //数据保存set中
            agnetSet = new Set();
            agnetSet.clear();
            serviceGridPanelStore.data.items.forEach(record => {
                agnetSet.add(record.get('agent_id'));
            });
            const array = Array.from(agnetSet);
            confItemConfStore.getProxy().setExtraParam("agnetSet", array);
            confItemConfStore.load();
		}
	}
    let scount=confItemConfStore.getCount();
    document.getElementById("serverconfig").innerHTML = "<p>已选择" + scount + "条配置文件</p>";
}
//通过json对象获取被禁用的UUID
function LbGetForbiddenIds(data) {
    const result = [];

    function search(data) {
        data.forEach(function(item) {
            if (item.isForbidden === true) {
                result.push(item.id);
            }
            if (item.children && item.children.length > 0) {
                search(item.children);
            }
        });
    }
    search(data);
    return result;
}

function createZP(){

    var pageBarBind = Ext.create('Ext.PagingToolbar', {
        store: '',
        dock: 'bottom',
        border : false,
        baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        displayInfo: true
    });
    var chosedcellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit : 2
    });

    //----------------待选-----------------------------
    Ext.define('doZPModel', {
        extend: 'Ext.data.Model',
        fields: [
            { name: 'iid', type: 'long'},
            { name: 'filename', type: 'string' },
            { name: 'size', type: 'int' },
            { name: 'version', type: 'string' },
            { name: 'pushdate', type: 'string' },
            { name: 'state', type: 'int' },
        ]
    });
    var doZPColumns = [
        {text: '序号', xtype:'rownumberer', width: 40},
        {xtype: 'gridcolumn', dataIndex: 'iid', hidden: true, text: 'id' },
        {xtype: 'gridcolumn', dataIndex: 'filename', flex: 1, text: '文件名称', },
        { xtype: 'gridcolumn', dataIndex: 'size', flex: 1, text: '大小', },
        { xtype: 'gridcolumn', dataIndex: 'version', flex: 1, text: '版本', },
        { xtype: 'gridcolumn', dataIndex: 'pushdate', flex: 1, text: '推送时间', },
        { xtype: 'gridcolumn', dataIndex: 'state', flex: 1, text: '状态',
            renderer: function(value, metaData, record) {
                var returnMess = "";
                if (value == 0) {
                    returnMess = '已生成';
                } else if (value == 1) {//已启动
                    returnMess = '已测试';
                } else if (value == 2) {
                    returnMess = '已验证';
                } else if (value == 3) {
                    returnMess = '已晋级';
                } else if (value == 4) {
                    returnMess = '已取消';
                } else if (value == 5) {
                    returnMess = '已投产';
                } else {
                    returnMess = '';
                }
                return returnMess;
            }}
    ];

    doZPStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 30,
        model: 'doZPModel',
        proxy: {
            type: 'ajax',
            url: 'ciQueryZpFile.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    doZPStore.on('beforeload', function(store, options) {
        let pt = paramsProjTypeBox.getValue();//项目类型
        let pn = paramsProjNumBox.getValue();//项目编码
        let pi = paramsProjIncreBox.getValue();//项目增量
		let pz = consZpFileBox.getValue();//获取选中制品
        let mt = paramsModelTypeBox.getValue();//模块类型
        var new_params =
            {
                syscode: syscode,
                projType: pt,
                projNum: pn,
                projIncre: pi,
                zpfilename : pz,
                zpfileid : isStrNull(zpfileid)?'':zpfileid,
                modeltype : mt,
            };
        Ext.apply(doZPStore.proxy.extraParams, new_params);
    });

    var selectZPColumns = [
        {text: '序号', xtype:'rownumberer', width: 40},
        {xtype: 'gridcolumn', dataIndex: 'iid', hidden: true, text: 'id' },
        {xtype: 'gridcolumn', dataIndex: 'filename', flex: 1, text: '文件名称', },
        { xtype: 'gridcolumn', dataIndex: 'size', flex: 1, text: '大小', },
        { xtype: 'gridcolumn', dataIndex: 'version', flex: 1, text: '版本', },
        { xtype: 'gridcolumn', dataIndex: 'pushdate', flex: 1, text: '推送时间', },
        { xtype: 'gridcolumn', dataIndex: 'state', flex: 1, text: '状态',
            renderer: function(value, metaData, record) {
                var returnMess = "";
                if (value == 0) {
                    returnMess = '已生成';
                } else if (value == 1) {//已启动
                    returnMess = '已测试';
                } else if (value == 2) {
                    returnMess = '已验证';
                } else if (value == 3) {
                    returnMess = '已晋级';
                } else if (value == 4) {
                    returnMess = '已取消';
                } else if (value == 5) {
                    returnMess = '已投产';
                } else {
                    returnMess = '';
                }
                return returnMess;
            }}
    ];
    var selectStore = Ext.create('Ext.data.Store', {
        autoLoad: false,
        autoDestroy: true,
        pageSize: 30,
        model: 'doZPModel',
        proxy: {
            type: 'ajax',
            url: 'ciQuerybdZpInfo.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    selectStore.on('beforeload', function(store, options) {
        let pt = paramsProjTypeBox.getValue();//项目类型
        let pn = paramsProjNumBox.getValue();//项目编码
        let pi = paramsProjIncreBox.getValue();//项目增量
		let pz = consZpFileBox.getValue();//获取选中制品
        var new_params =
            {
                syscode: syscode,
                projType: pt,
                projNum: pn,
                projIncre: pi,
                zpfilename : pz,
            };
        Ext.apply(selectStore.proxy.extraParams, new_params);
    });
    var pageBar = Ext.create('Ext.PagingToolbar', {
        store: doZPStore,
        dock: 'bottom',
        border : false,
        baseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
        displayInfo: true
    });

    var filenameField = Ext.create('Ext.form.TextField', {
        name: 'filename',
        labelWidth: 50,
        width: contentPanel.getWidth() / 7,
        fieldLabel: '文件名',
        labelAlign: 'right',
        emptyText: "--请选择文件名--",
        listeners: {
            change: function() {
                var filename = this.getValue();
                doZPStore.getProxy().setExtraParam("fileName", filename);
            }
        }
    });
    var versionField = Ext.create('Ext.form.TextField', {
        name: 'version',
        labelWidth: 50,
        width: contentPanel.getWidth() / 7,
        fieldLabel: '版本号',
        labelAlign: 'right',
        emptyText: "--请选择版本号--",
        listeners: {
            change: function() {
                var version = this.getValue();
                doZPStore.getProxy().setExtraParam("version", version);
                /*doZPStore.clearFilter(true);
                doZPStore.filter({property:'version',value:version})*/
            }
        }
    });
    var rightPanel = Ext.create('Ext.grid.Panel', {
        cls:' window_border  panel_space_right panel_space_bottom',
        region : 'center',
        title : '待选制品<font size=\'1px\'; color=\'#ff001c\'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 请选择行并拖动至左侧(按住CTRL键可多选)</font>',
        bbar: pageBar,
        multiSelect: true,
        split : true,
        store : doZPStore,
        plugins : [ chosedcellEditing ],
        columnLines : true,
        emptyText: '没有待选制品信息',
        columns : doZPColumns,
        viewConfig: {
            plugins: {
                ptype: 'gridviewdragdrop',
                dragGroup: 'secondGridDDGroup',
                dropGroup: 'firstGridDDGroup'
            },
            listeners: {
                drop: function(node, data, dropRec, dropPosition) {
                    var dropOn = dropRec ? ' ' + dropPosition + ' ' + dropRec.get('name') : ' on empty view';
                }
            }
        },
        dockedItems: [{
            xtype: 'toolbar',
            id:'unch_toolbar',
            items: [filenameField,versionField,{ xtype: 'tbfill' },{
                itemId : 'queryinfo',
                text : '查询',
                cls:'Common_Btn',
                handler : function(){
                    queryzp();
                }
            },{
                itemId : 'recover',
                text : '重置',
                cls:'Common_Btn',
                handler : function (){
                    filenameField.setValue('');
                    versionField.setValue('');
                }
            }]
        }],
    });

    var leftPanel = Ext.create('Ext.grid.Panel', {
        cls:'window_border  right_edge  panel_space_left  panel_space_right panel_space_bottom',
        region : 'west',
        title : '已选制品<font size=\'1px\'; color=\'#ff001c\'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 请选择行并拖动至右侧(按住CTRL键可多选)</font>',
        width : "50%",
        multiSelect: true,
        split : true,
        plugins : [ chosedcellEditing ],
        columnLines : true,
        emptyText: '没有已选制品信息',
        store : selectStore,
        columns : selectZPColumns,
        viewConfig: {
            plugins: {
                ptype: 'gridviewdragdrop',
                dragGroup: 'firstGridDDGroup',
                dropGroup: 'secondGridDDGroup'
            },
            listeners: {
                drop: function(node, data, dropRec, dropPosition) {
                    var dropOn = dropRec ? ' ' + dropPosition + ' ' + dropRec.get('name') : ' on empty view';
                }
            }
        },
        dockedItems: [{
            xtype: 'toolbar',
            items: [{ xtype: 'tbfill' },{
                text: '确定',
                cls:'Common_Btn',
                handler: function(){
                    addzpInfo();
                }
            }]
        }],
        listeners: {
            itemclick: function(dv, record, item, index, e) {
            }
        }

    });
    const gjstepwin = Ext.create('Ext.window.Window', {
        title: '制品文件选择',
        height: height,
        width: width * 0.9,
        layout: 'border',
        border: false,
        draggable: false,// 禁止拖动
        resizable: false,// 禁止缩放
        region: 'center',
        items: [leftPanel,rightPanel],
        buttonAlign: 'center',
        modal: true,
        buttons: [/*{
			xtype: 'button',
			text: '取消',
			cls: 'Common_Btn',
			handler: function() {
				gjstepwin.destroy();
			}
		}*/]
    }).show();

    queryzp();
    selectStore.load();
    gjstepwin.on('close', function() {

    });

    function addzpInfo(){
        let zpinfo="";
        var arr = [];
        for(let i=0; i< selectStore.getCount();i++){
            var record = selectStore.getAt(i);
            arr[i]=record.data.filename;
            if(i==0){
                zpinfo= zpinfo+record.data.filename+":"+record.data.version;
            }else{
                zpinfo= zpinfo+","+record.data.filename+":"+record.data.version;
            }
        }

        var sarr = arr.sort();
        for(let i=0; i<sarr.length; i++){
            if(sarr[i] == sarr[i+1]){
                Ext.Msg.alert("提示", "相同的制品文件，只允许选择一个版本,重复元素是：" + sarr[i]);
                return true;
            }
        }
		consZpFileBox.setValue(zpinfo);
        gjstepwin.destroy();

    };

    function queryzp(){
        pageBar.moveFirst();
    };
    function checkjsStep() {

    };
}
function loadCodeList(){
    listCodeStore.loadData([],false);
    var projType = paramsProjTypeBox.getValue();//项目类型
    var projNum = paramsProjNumBox.getValue();//项目编码
    if (projType != 0) {
        listCodeStore.load();
    }else if (projType == 0 && (projNum != undefined && projNum != null && projNum != '')){
        listCodeStore.load();
    }
}

function updateTip(field, t) {
    let value = field.getValue();
    if(value != undefined && value != null && value != ''){
        Ext.QuickTips.enable();
        Ext.QuickTips.init();
        Ext.QuickTips.register({
            target: field.el,
            text: field.getValue()
        })
    }else{
        Ext.QuickTips.disable();
    }

};
function rollbackbackPage() {
    destroyRubbish();
    if (isTabSwitch) {//多标签分支
        closeTabPanel();
    }else {
        contentPanel.getLoader().load(
            {
                url: 'accessHisCiCdMonitor.do',
                scripts: true
            });
        if (Ext.isIE) {
            CollectGarbage();
        }
    }
}

function pasInt(v) {
    if (v != 'null' && v != '') {
        return parseInt(v);
    } else {
        return '';
    }
}