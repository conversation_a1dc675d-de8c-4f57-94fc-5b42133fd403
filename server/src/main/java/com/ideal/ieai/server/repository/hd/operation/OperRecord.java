package com.ideal.ieai.server.repository.hd.operation;

import java.io.Serializable;

/**
 * 
 * 名称: OperRecord.java<br>
 * 描述: 操作记录实体类<br>
 * 类型: JAVA<br>
 * 最近修改时间:2015年10月19日<br>
 * 
 * <AUTHOR>
 */
public class OperRecord implements Serializable
{
    public OperRecord()
    {
    }

    private long   opId;         // 主键
    private String sysName;     // 业务系统名
    private String flowName;    // 进程类型
    private String agentIP;     // 服务器
    private String opcommand;   // 操作指令
    private String operator;    // 操作人
    private long   opTime;      // 操作时间
    private int    prjType;     // 工程类型 4(灾备切换) 5(健康巡检)
    private String other;       // 其他
    private String opTimeString; // 操作时间(格式化)

    public String getOpTimeString ()
    {
        return opTimeString;
    }

    public void setOpTimeString ( String opTimeString )
    {
        this.opTimeString = opTimeString;
    }

    public long getOpId ()
    {
        return opId;
    }

    public void setOpId ( long opId )
    {
        this.opId = opId;
    }

    public int getPrjType ()
    {
        return prjType;
    }

    public void setPrjType ( int prjType )
    {
        this.prjType = prjType;
    }

    public String getOther ()
    {
        return other;
    }

    public void setOther ( String other )
    {
        this.other = other;
    }

    public String getSysName ()
    {
        return sysName;
    }

    public void setSysName ( String sysName )
    {
        this.sysName = sysName;
    }

    public String getFlowName ()
    {
        return flowName;
    }

    public void setFlowName ( String flowName )
    {
        this.flowName = flowName;
    }

    public String getAgentIP ()
    {
        return agentIP;
    }

    public void setAgentIP ( String agentIP )
    {
        this.agentIP = agentIP;
    }

    public String getOpcommand ()
    {
        return opcommand;
    }

    public void setOpcommand ( String opcommand )
    {
        this.opcommand = opcommand;
    }

    public String getOperator ()
    {
        return operator;
    }

    public void setOperator ( String operator )
    {
        this.operator = operator;
    }

    public long getOpTime ()
    {
        return opTime;
    }

    public void setOpTime ( long opTime )
    {
        this.opTime = opTime;
    }

}
