/*******************************************************************************
 * 流程步骤列表
 ******************************************************************************/
var gridCheckbox;
var recordG;
Ext.onReady (function ()
{
	
	    gridCheckbox = Ext.create ('Ext.selection.CheckboxModel',
	    {
		    checkOnly : true
//		    ,
//		    listeners: {
//		    	selectionchange: function(model,selected){
//		    		var records = [];
//		    		alert(model.getCount());
////		    		for(var i=0;i<model.getCount();i++){
////		    			//var mm = model.getAt(i);
////		    			alert(model.isSelected(i));
////		    		}
//		    		//alert(5555);
//		    	}
//		    }
	    });
	// 主panel高度减掉的定值
	var staticHeight = 175;
	if (parent.showOnly == 2)
	{
		staticHeight = 265;
	}
	/** 树数据Model* */
	Ext.define ('flowModel',
	{
	    extend : 'Ext.data.Model',
	    fields : [
	            {
	                name : 'instanceName',
	                type : 'string'
	            },
	            {
	                name : 'iid',
	                type : 'long'
	            },
	            {
	                name : 'iserner',
	                type : 'long'
	            },
	            {
	                name : 'iprener',
	                type : 'string'
	            },{
	                name : 'ibranch',
	                type : 'string'
	            },
	            {
	                name : 'iactName',
	                type : 'string'
	            },
	            {
	                name : 'iscriptName',
	                type : 'string'
	            },
	            {
	                name : 'imodelName',
	                type : 'string'
	            },
	            {
	                name : 'imodelVersion',
	                type : 'long'
	            },
	            {
	                name : 'systemType',
	                type : 'string'
	            },
	            {
	                name : 'modelType',
	                type : 'string'
	            },
	            {
	                name : 'isSelect',
	                type : 'boolean'
	            }
	    ]
	});
	
	/** 树数据源* */
	var flowStore = Ext.create ('Ext.data.Store',
	{
	    autoLoad : true,
	    autoDestroy : true,
	    model : 'flowModel',
	    pageSize : 50,
	    proxy :
	    {
	        type : 'ajax',
	        url : 'getFlowSonList.do',
	        reader :
	        {
	            type : 'json',
	            root : 'dataList'
	        }
	    }
	});
	flowStore.on('load',function(store, records){
		if(null!=records){
			var model = flowGrid.getSelectionModel();
			for(var i=0;i<records.length;i++){
				var isSelect = records[i].get('isSelect');
				if(isSelect){
					model.select(i, true, false);
				}
			}
		}
		
	});
//	flowStore.addListener('load',function(){
//		
//		gridCheckbox.selectAll( true );
//		
//      }); 

	flowStore.on ('beforeload', function (store, options)
	{
		var workItemid=-1;
		if(null!=parent.iworkItemid){
			workItemid = parent.iworkItemid;
		}
		var new_params =
		{
			instanceId : parent.iid,
			iworkItemid : workItemid
		};
		Ext.apply (flowStore.proxy.extraParams, new_params);
	});
	parent.flowCustomizedListStore=flowStore;
		var paraCellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
		clicksToEdit : 1
	});
	/** 树列表columns* */
	var flowColumns = [
	        {
	            text : '序号',
	            // align : 'center',
	            width : 35,
	            xtype : 'rownumberer'
	        }, {
	            text : '作业名',
	            dataIndex : 'iactName',
	            flex : 1
	        },
	        {
	            text : '步骤标识',
	            dataIndex : 'iserner',
	            flex : 1
	        },
	        {
	            text : '依赖',
	            dataIndex : 'iprener',
	            flex : 1
	        },{
	            text : '分支条件',
	            dataIndex : 'ibranch',
	            flex : 1,
	            editor : {
					xtype : 'textfield',
					allowBlank : true,
					listeners : {
						'blur' : function() {
//							alert(editorSon);
							// 遍历所有节点
						    var root2 = parent.editorSon.graph.getModel ().getRoot ();
						    var count = parent.editorSon.graph.model.getChildCount (root2);
						    for (var i = 0; i < count; i++)
						    {
							    var cells = root2.getChildAt (i);
							    var counts = cells.getChildCount ();
							    for (var j = 0; j < counts; j++)
							    {
//							    	var record = flowGrid.getSelectionModel().getSelection()[0];
//							    	alert(record.data.iserner+"||"+cells.getChildAt (j).id+"||"+cells.getChildAt (j).value);
							    	if(recordG.data.iserner==cells.getChildAt (j).id)
							    		{
							    		cells.getChildAt (j).ibranch=this.value;
							    		//更新名后续刷新才能显示
							    		parent.editorSon.graph.view.refresh();
//							    		parent.editorSon.graph.refresh(cells.getChildAt (j));
							    		break;
							    		}
							    }
						    }

						}
					}
				}
	        },
	        {
	            text : '脚本名',
	            dataIndex : 'iscriptName',
	            flex : 1
	        },
	        {
	            text : '模板名',
	            dataIndex : 'imodelName',
	            flex : 1
	        },
	        {
	            text : '模板版本号',
	            dataIndex : 'imodelVersion',
	            flex : 1,
	            renderer : function (value, metaData, record)
	            {
		            if ('' == record.get ('imodelName'))
		            {
			            return '';
		            }
		            else
		            {
			            return value;
		            }
	            }
	        },
	        {
	            text : '模块类型',
	            dataIndex : 'modelType',
	            flex : 1
	        },
	        {
	            text : '应用标识',
	            dataIndex : 'systemType',
	            flex : 1
	        }
	];
	var bsPageBar = Ext.create ('Ext.PagingToolbar',
	{
	    store : flowStore,
	    dock : 'bottom',
	    displayInfo : true,
	    emptyMsg : "没有记录"
	});
	/** 树列表panel* */
	flowGrid = Ext.create ('Ext.ux.ideal.grid.Panel',
	{
		extend : 'Ext.grid.Panel',
	    alias : 'widget.ideapanel',
	    ipageBaseCls:Ext.baseCSSPrefix + 'toolbar customize_toolbar',
	    region : 'center',
	    store : flowStore,
	    columnLines : true,
	    columns : flowColumns,
	    selModel:gridCheckbox,
	    //bbar : bsPageBar,
	    plugins: [ paraCellEditing ],
	    listeners : {
	        cellclick: function( view, td, cellIndex, record, tr, rowIndex, e, eOpts ) {
	        	if(cellIndex==5){
	        		recordG=record;
	          }
	        }
	      },
	    collapsible : false
	});
	// 主Panel
	var MainPanel = Ext.create ('Ext.panel.Panel',
	{
	    renderTo : "flowCustomizedList_div",
	    width : '100%',
	    height : '100%',
	    layout : 'border',
	    border : false,
	    items : [
		    flowGrid
	    ]
	});
	contentPanel.on ('resize', function ()
	{
		MainPanel.setWidth ('100%');
		MainPanel.setHeight (contentPanel.getHeight () - staticHeight);
//		parent.MainPanel.setWidth ('100%');
//		parent.MainPanel.setHeight (contentPanel.getHeight () - parent.staticHeight);
	});
	
});

