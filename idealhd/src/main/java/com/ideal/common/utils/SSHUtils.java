package com.ideal.common.utils;

import com.ideal.ieai.core.Environment;
import com.jcraft.jsch.*;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.*;


public class SSHUtils
{
    /**
     * 默认端口
     */
    private final static int DEFAULT_PORT = 22;

    private final static String HOST = "host";

    private final static String PORT = "port";

    private final static String USER_NAME = "userName";

    private final static String PASSWORD = "password";

    /**
     * 服务端保存的文件名
     */
    private String remote;

    /**
     * 服务端保存的路径
     */
    private String remotePath;

    /**
     * 本地文件
     */
    private File local;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口
     */
    private int port = DEFAULT_PORT;

    /**
     * 登录名
     */
    private String userName;

    /**
     * 登录密码
     */
    private String password;

    private ChannelSftp sftp;
    private ChannelExec exec;
    private ChannelShell shell;
    private Session session;
    public SSHUtils(String host, int port, String userName, String password){
        this.init(host, port, userName, password);
    }
    
    
    
    public ChannelExec getExec ()
    {
        return exec;
    }



    public void setExec ( ChannelExec exec )
    {
        this.exec = exec;
    }



    /**
     * <li>Description: 初始化</li> 
     * <AUTHOR>
     * 2016年7月29日 
     * @param host
     * @param port
     * @param userName
     * @param password
     * return void
     */
    private void init(String host, int port, String userName, String password){
        this.host = host;
        this.port = port;
        this.userName = userName;
        this.password = password;
    }


    /**
     * <li>Description: 创建ssh连接</li> 
     * <AUTHOR>
     * 2016年7月29日 
     * @param chnel
     * @throws JSchException
     * return void
     */
    private void connect(String chnel) throws JSchException {
        JSch jsch = new JSch();
        session = jsch.getSession(userName, host, port);
        // 设置连接服务器密码
        session.setPassword(password);
        Properties sessionConfig = new Properties();
        // StrictHostKeyChecking
        // "如果设为"yes"，ssh将不会自动把计算机的密匙加入"$HOME/.ssh/known_hosts"文件，
        // 且一旦计算机的密匙发生了变化，就拒绝连接。
        sessionConfig.setProperty("StrictHostKeyChecking", "no");
        // 设置会话参数
        session.setConfig(sessionConfig);
        // 连接
        session.connect(60000);
        
        osType="linux";
        if(osType.equalsIgnoreCase("windows")){
            this.prompt = '>';
        }else{
            // 根据用户设置结束符
            this.prompt = userName.equals("root") ? '#' : '$'; 
        }
        
        
        Channel channel = session.openChannel(chnel);
//        channel.connect();
        if("sftp".equals(chnel)) {
            sftp = (ChannelSftp) channel;
        } else if("exec".equals(chnel)) {
            exec = (ChannelExec) channel;
        } else if("shell".equals(chnel)) {
            shell = (ChannelShell) channel;
        } 
    }

    /**
     * 
     * <li>Description:浙商ssh连接，密码为普通用户密码||root用户密码</li> 
     * <AUTHOR>
     * 2021年6月1日 
     * @param chnel
     * @param pdw
     * @throws JSchException
     * return void
     */
    private void connectCz(String chnel, String pdw) throws JSchException{
        JSch jsch = new JSch();
        session = jsch.getSession(userName, host, port);
        //设置连接服务器密码
        session.setPassword(pdw);
        Properties sessionConfig = new Properties();
        // StrictHostKeyChecking
        //"如果设为"yes",ssh将不会自动把计算机的密匙加入"$HOME/.ssh/known_hosts"文件,
        //且一旦计算机的密匙发生了变化，就拒绝连接。
        sessionConfig.setProperty("StrictHostKeyChecking", "no");
        sessionConfig.setProperty("PreferredAuthentications", "publickey,keyboard-interactive,password");
        //设置会话参数
        session.setConfig(sessionConfig);
        session.connect(60000);
        
        osType = "linux";
        if(osType.equalsIgnoreCase("windows")){
            this.prompt = '>';
        }else{
            //根据用户设置结束符
            this.prompt = userName.equals("root") ? '#' : '$';
        }
        Channel channel = session.openChannel(chnel);
        //channel.connect();
        if("sftp".equals(chnel)){
            sftp = (ChannelSftp) channel;
        }else if("exec".equals(chnel)){
            exec = (ChannelExec) channel;
        }else if("shell".equals(chnel)){
            shell = (ChannelShell) channel;
        }
    }
    
    /**
     * <li>Description: sftp上传文件</li> 
     * <AUTHOR>
     * 2016年7月29日 
     * return void
     * @throws Exception 
     */
    public void uploadFile()  throws Exception{
        FileInputStream inputStream = null;
        try {
            this.connect("sftp");
            sftp.connect();
            remote = local.getName();
            if(!StringUtils.isEmpty(remotePath)){
                sftp.cd(remotePath);
            }           
            inputStream = new FileInputStream(local);
            sftp.put(inputStream, remote);
            inputStream.close();
            _log.info("上载文件成功: "+ remote +" , 目标地址: "+this.host);
        } catch (JSchException e) {
            _log.error(e.getMessage());
            throw new Exception("SSH连接异常, IP: "+this.host+e.getMessage(),e.getCause());
        } catch (SftpException e)
        {
            _log.error(remotePath+" "+e.getMessage());
            throw new Exception(e.getMessage(),e.getCause());
        } catch (FileNotFoundException e)
        {
            _log.error(e.getMessage());
            throw new Exception(e.getMessage());
        }finally{
            if(null!=sftp && sftp.isConnected()){
                sftp.disconnect();
            }
        }
    }
    
    public void uploadFileForCzb(String ioperationpassword)  throws Exception{
        FileInputStream inputStream = null;
        String[] pdw = ioperationpassword.split("\\|\\|");
        try {
            this.connectCz("sftp",pdw[0]);
            sftp.connect();
            remote = local.getName();
            if(!StringUtils.isEmpty(remotePath)){
                sftp.cd(remotePath);
            }           
            inputStream = new FileInputStream(local);
            sftp.put(inputStream, remote);
            inputStream.close();
            _log.info("上载文件成功: "+ remote +" , 目标地址: "+this.host);
        } catch (JSchException e) {
            _log.error(e.getMessage());
            throw new Exception("SSH连接异常, IP: "+this.host+e.getMessage(),e.getCause());
        } catch (SftpException e)
        {
            _log.error(remotePath+" "+e.getMessage());
            throw new Exception(e.getMessage(),e.getCause());
        } catch (FileNotFoundException e)
        {
            _log.error(e.getMessage());
            throw new Exception(e.getMessage());
        }finally{
            if(null!=sftp && sftp.isConnected()){
                sftp.disconnect();
            }
        }
    }
    public void uploadFile(String remoteName)  throws Exception{
        FileInputStream inputStream = null;
        try {
            this.connect("sftp");
            sftp.connect();
            remote = remoteName;
            if(!StringUtils.isEmpty(remotePath)){
                sftp.cd(remotePath);
            }           
            inputStream = new FileInputStream(local);
            sftp.put(inputStream, remote);
            inputStream.close();
            _log.info("上载文件成功: "+ remote +" , 目标地址: "+this.host);
        } catch (JSchException e) {
            _log.error(e.getMessage());
            throw new Exception("SSH连接异常, IP: "+this.host+e.getMessage(),e.getCause());
        } catch (SftpException e)
        {
            _log.error(remotePath+" "+e.getMessage());
            throw new Exception(e.getMessage(),e.getCause());
        } catch (FileNotFoundException e)
        {
            _log.error(e.getMessage());
            throw new Exception(e.getMessage());
        }finally{
            if(null!=sftp && sftp.isConnected()){
                sftp.disconnect();
            }
        }
    }
    /**
     * <li>Description: sftp下载</li> 
     * <AUTHOR>
     * 2016年7月29日 
     * return void
     * @throws Exception 
     */
    public void download() throws Exception{
        FileOutputStream output = null;
        try {
            this.connect("sftp");
            sftp.connect();
            if(!StringUtils.isEmpty(remotePath)){
                sftp.cd(remotePath);
            }
            output = new FileOutputStream(local);
            sftp.get(remote, output);
            output.close();
        } catch (JSchException e) {
            _log.error(e.getMessage());
            throw new Exception(e);
        }finally{
            if(null!=sftp && sftp.isConnected()){
                sftp.disconnect();
            }
        }
    }
    
    public Map execCmd(String command) throws Exception {
        Map map = new HashMap();
        List<String> rs = new ArrayList<String>();
        BufferedReader reader = null;
        boolean retVal=false;
        try {
            this.connect("exec");
            exec.setPty(true);
            exec.setCommand(command);
            exec.setInputStream(System.in);
            exec.setErrStream(System.err);
            exec.connect();
            _log.info("command : "+command+" is executing");
            reader = new BufferedReader(new InputStreamReader(exec.getInputStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                _log.info(" line :"+line);
                if (StringUtils.isNotBlank(line)){
                    rs.add(line);
                }
            }
            while(true){
                if(exec.isClosed()){
                    retVal=exec.getExitStatus()==0?true:false;
                    _log.info(command+" exitCode :"+exec.getExitStatus());
                    break;
                }
            }
            map.put("execRet", retVal);
//            if(rs.size()<2){
//                map.put("errMsg", rs.get(rs.size()-1));
//            } else {
//                map.put("errMsg", rs.get(rs.size()-2));
//            }
            String str="";
            for (int i = 0; i < rs.size(); i++) {
                str+= rs.get(i) +" /n";
            }
            if (str.length()>511){
                str =str.substring(str.length()-511);
            }
            map.put("errMsg", str);
        } catch (JSchException e) {
            _log.error(e.getMessage());
            throw new Exception(e);
        }finally{
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(null!=exec && exec.isConnected()){
                exec.disconnect();
            }
        }
        return map;
    }
    
    /**
     * 浙商银行安装agent方法
     */
    public Map<String,Object> execCmdCz(String command) throws Exception{
        _log.info("cmd:"+command);
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("success", "false");
        map.put("message", "安装失败，详细请查看日志");
        loginCzb();
        write(command);
        StringBuffer sb = new StringBuffer();
        try{
            char ch;
            int code = -1;
            while((code = in.read()) != -1){
                ch = (char) code;
                sb.append(ch);
                //登录失败时返回结果
                if(sb.toString().contains("install sucessfully")){
                    map.put("success", true);
                    map.put("message", "安装成功");
                    _log.info(sb.toString());
                    return map;
                }
            }
        }catch(Exception e){
            _log.error("Agent安装execCmdCz is error",e);
            map.put("success", false);
            map.put("message", "安装失败，详情请查看日志");
        }  
        return map;
    }
    
    /**
     * 浙商 su-root 方法
     */
    private void loginCzb() throws Exception{
        String[] pdw = password.split("\\|\\|");
        try{
            this.connectCz("shell",pdw[0]);
            in = shell.getInputStream();
            out = new PrintStream(shell.getOutputStream());
            shell.connect();
            readUntil(prompt+"");
            write("export LANG=en_US.utf8");
            readUntil(prompt+"");
            write("su - root");
            readUntilTwo("Password:","password:","PASSWORD:");
            write(pdw[1]);
            String message = readUntil("#");
            if(message.indexOf("#") == -1){
                _log.info("安装失败" + message);
                throw new  Exception(message);
            }
            _log.info(host+"su 成功...");
            this.prompt = '#';
        }catch(Exception e){
            throw new  Exception(e.getMessage());
        }
    }
    
    public Map execNewCmd(String command) throws Exception {
        Map map = new HashMap();
        boolean retVal=false;
        try {
            this.connect("exec");
            exec.setPty(false);
            exec.setCommand(command);
            exec.setInputStream(System.in);
            exec.setErrStream(System.err);
            exec.connect();
            _log.info("command : "+command+" is executing");
            while(true){
                if(exec.isClosed() || exec.getExitStatus()==0){
                    retVal=exec.getExitStatus()==0?true:false;
                    _log.info(command+" exitCode :"+exec.getExitStatus());
                    break;
                }
            }
            map.put("execRet", retVal);
        } catch (Exception e) {
            _log.error(e.getMessage());
            throw new Exception(e);
        }finally{
            if(null!=exec && exec.isConnected()){
                exec.disconnect();
            }
        }
        return map;
    }

    public void setRemote(String remote) {
        this.remote = remote;
    }

    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath;
    }

    public void setLocal(File local) {
        this.local = local;
    }
    /**
     * 
     * <li>Description:关闭连接</li> 
     * <AUTHOR>
     * 2016年8月2日 
     * return void
     */
    public void closeSession(){
        if(null!=session && session.isConnected()){
            session.disconnect();
        }
    }
    
    /** 
     * 读取分析结果 
     *  
     * @param pattern   匹配到该字符串时返回结果 
     * @return 
     */  
    public String readUntil(String pattern) {  
        StringBuffer sb = new StringBuffer();  
        try {  
            char lastChar = (char)-1;  
            boolean flag = pattern!=null&&pattern.length()>0;  
            if(flag)  
                lastChar = pattern.charAt(pattern.length() - 1);  
            char ch;  
            int code = -1;  
            while ((code = in.read()) != -1) {  
                ch = (char)code;  
                sb.append(ch);  

                //匹配到结束标识时返回结果  
                if (flag) {  
                    if (ch == lastChar && sb.toString().endsWith(pattern)) {  
                        return sb.toString();  
                    }  
                }else{  
                    //如果没指定结束标识,匹配到默认结束标识字符时返回结果  
                    if(ch == prompt)  
                        return sb.toString();  
                }  
                //登录失败时返回结果  
                if(sb.toString().toLowerCase().contains("login failed")){  
                    return sb.toString();  
                }  
                if(sb.toString().toLowerCase().contains("failure")){  
                    return sb.toString();  
                }  
            }  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return sb.toString();  
    }  
    
    public String readUntilTwo(String pattern1, String pattern2, String pattern3) {  
        StringBuffer sb = new StringBuffer();  
        try {  
            char ch;  
            int code = -1;  
            while ((code = in.read()) != -1) {  
                ch = (char)code;  
                sb.append(ch);  
                if ((ch  == pattern2.charAt(pattern2.length() - 1) && sb.toString().endsWith(pattern2))
                    || (ch == pattern1.charAt(pattern1.length() - 1) && sb.toString().endsWith(pattern1))
                    || (ch == pattern3.charAt(pattern3.length() - 1) && sb.toString().endsWith(pattern3))) { 
                    _log.info("ssh"+sb.toString());    
                    return sb.toString();  
                }else if(ch == ftpPrompt){  
                        return sb.toString();  
                }  
                //登录失败时返回结果  
                if(sb.toString().contains("Connection reset by peer")){  
                    return sb.toString();  
                }  
           }  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return sb.toString();  
    }  
    
    public String readSftpUntil(String pattern1,String pattern2,String pattern3, String pattern4) {  
        StringBuffer sb = new StringBuffer(); 
        boolean isflg = false;
        try {  
            char ch;  
            int code = -1;  
            while ((code = in.read()) != -1) {  
                ch = (char)code;  
                sb.append(ch); 
                if((ch == pattern2.charAt(pattern2.length() - 1) && sb.toString().endsWith(pattern2))
                        || (ch == pattern3.charAt(pattern3.length() - 1) && sb.toString().endsWith(pattern3))
                        || (ch == pattern4.charAt(pattern4.length() - 1) && sb.toString().endsWith(pattern4))){
                    _log.info("ssh"+sb.toString());
                    return sb.toString();
                }else if((ch == pattern1.charAt(pattern1.length() - 1) && sb.toString().endsWith(pattern1))){
                    isflg = true;
                }else if(ch == ftpPrompt ) {
                    return sb.toString(); 
                }
                if(isflg && Character.isSpaceChar(ch) && (sb.toString().contains("yes")||sb.toString().contains("YES"))){
                    _log.info(sb.toString());
                    return sb.toString(); 
                }
                //登录失败时返回结果  
                if(sb.toString().contains("Connection reset by peer")){  
                    return sb.toString();  
                } 
            }  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return sb.toString();  
    }  
    /**
     * 写操作
     * 
     * @param value
     */
    public void write ( String value )
    {
        try
        {
            out.println(value);
            out.flush();
        } catch (Exception e)
        {
            e.printStackTrace();
            _log.error("ssh 操作在输入时 产生错误 ：" + e);
        }
    }
    /**
     * 
     * <li>Description:关闭正在执行的脚本类似于ctrl+c</li> 
     * <AUTHOR>
     * 2017年4月7日 
     * return void
     */
    public void closeExec(){
        try
        {
            OutputStream output = exec.getOutputStream();
            output.write(3);
            output.flush();
            output.close();
        } catch (IOException e)
        {
            e.printStackTrace();
        }
    }
    /**
     * 向目标发送命令字符串
     * 
     * @param command
     * @return
     */
    public String sendCommand ( String command )
    {
        String out = null;
        try
        {
            _log.info("excute cmd :"+command);
            write(command);
            out = readUntil(prompt + "");
        } catch (Exception e)
        {
            _log.error("sendconmmand error: " + e);
        }
        return out;
    }
    /**
     * 
     * <li>Description:登录FTP</li> 
     * <AUTHOR>
     * 2016年8月4日 
     * @param ip
     * @param user
     * @param password
     * return void
     * @throws Exception 
     */
    
    private void loginFtp (String ip,String user, String password ) throws Exception
    {
        
        try {
            this.connect("shell");
            in = shell.getInputStream();
            out = new PrintStream(shell.getOutputStream());
            shell.connect();
            write("export LANG=en_US.utf8");
            readUntil(prompt+"");
            sendCommand("cd /tmp");
            write("ftp "+ip);
            if(osType.equalsIgnoreCase("windows")){
              readUntil("User ("+ip+":(none)):");
            }else{
              readUntil("Name ("+ip+":"+this.userName+"):");
            }
            write(user);
            readUntilTwo("Password:","password:","PASSWORD:");
            write(password);
            String out = readUntil(ftpPrompt + " ");
            if(null!=out && out.contains("Login failed")){
                throw new Exception("FTP/SFTP登录失败  "+"ip:"+ip);
            }
            _log.info("FTP/SFTP登录"+ip+"成功...");
        } catch (Exception e) {
            throw new Exception(e.getMessage()+",FTP/SFTPip:"+ip);
        }
    }
    
    /**
     * 执行FTP命令获取文件
     * @param ip
     * @param user
     * @param passwd
     * @param filepath
     * @param filename
     * @return
     * @throws Exception 
     */
//    public String getFilebyFtp( String ip,String user,String passwd,String filepath,String filename ) throws Exception
//    {
//        String out = null;
//        loginFtp(ip,user,passwd);
//        try
//        {
//            write("bi");
//            readUntil(String.valueOf(ftpPrompt));
//            write("prompt off");
//            readUntil(String.valueOf(ftpPrompt));
//            write("cd "+filepath);
//            readUntil(String.valueOf(ftpPrompt));
//            write("get "+filename);
//            out = readUntil(String.valueOf(ftpPrompt));
//            write("by");
//            readUntil(prompt+"");
//            _log.info("FTP下载"+filename);
//        } catch (Exception e)
//        {
//            _log.error("FTP下载出错" + e.getMessage() + " ip：" + ip);
//        }finally{
//            if(null!=this.out){
//                this.out.close();
//            }
//            if(null!=this.in){
//                this.in.close();
//            }
//            if(null!=this.shell && this.shell.isConnected()){
//                this.shell.disconnect();
//            }
//        }
//        return out;
//    }
    
    public void getFilebyFtp(String ip,String user,String passwd,String filepath,String filename){
        try
        {
            this.connect("shell");
            shell.connect();
            in = shell.getInputStream();
            out = new PrintStream(shell.getOutputStream());
            sendCommand("echo '#!/bin/bash'>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'ftp -n<<!'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'open "+ip+"'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'user "+user+" "+passwd+"'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'binary'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'cd "+filepath+"'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'prompt'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'get "+filename+"'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'close'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo 'bye'>>/tmp/downloadAgentFile.sh");
            sendCommand("echo '!'>>/tmp/downloadAgentFile.sh");
            execCmd("cd /tmp;sh downloadAgentFile.sh");
        } catch (Exception e)
        {
            _log.error("FTP下载出错" + e.getMessage() + " ip：" + ip);
            e.printStackTrace();
        } finally{
          if(null!=this.out){
              this.out.close();
          }
          if(null!=this.in){
              try
            {
                this.in.close();
            } catch (IOException e)
            {
                e.printStackTrace();
            }
          }
          if(null!=this.shell && this.shell.isConnected()){
              this.shell.disconnect();
          }
        }
    }
    private void loginSftp ( String ip, String user, String password ) throws Exception
    {

        try
        {
            this.connect("shell");
            in = shell.getInputStream();
            out = new PrintStream(shell.getOutputStream());
            shell.connect();
            write("export LANG=en_US.utf8");
            readUntil(prompt + "");
            sendCommand("cd /tmp");
            write("sftp " + user + "@" + ip);
            String out = readSftpUntil("Are you sure you want to continue connecting", "Password:","password:","PASSWORD:");
            if (out.endsWith("Are you sure you want to continue connecting"))
            {
                write("yes");
                readUntilTwo("Password:","password:","PASSWORD:");
                write(password);
            } else if (out.trim().endsWith("Password:") || out.trim().endsWith("password:") || out.trim().endsWith("PASSWORD:"))
            {
                write(password);
            }
            if (null != out && out.contains("Connection reset by peer"))
            {
                throw new Exception("FTP/SFTP登录失败  " + "ip:" + ip);
            }
            out = readUntil(ftpPrompt + " ");
            _log.info("FTP/SFTP登录" + ip + "成功...");
        } catch (Exception e)
        {
            throw new Exception(e.getMessage());
        }
    }
    
    //浙商银行ssh连接 普通用户su -root
    private void loginSftpCzb(String ip,String user, String pw ) throws Exception
    {   String[] pdw = password.split("\\|\\|");    
        try{
            this.connectCz("shell", pdw[0]);
            in = shell.getInputStream();
            out = new PrintStream(shell.getOutputStream());
            shell.connect();
            readUntil(prompt+"");
            write("export LANG=en_US.utf8");
            readUntil("$");
            write("su - root");
            readUntilTwo("Password:","password:","PASSWORD:");
            write(pdw[1]);
            readUntil("#");
            _log.info(ip+"su 成功...");
            this.prompt = '#';
            sendCommand("cd /tmp");
            write("sftp "+user+"@"+ip );
            String out = readSftpUntil("Are you sure you want to continue connecting (yes/no)?","Password:","password:","PASSWORD:");
            _log.info(out);
            if(out.contains("Are you sure you want to continue connecting")){
                write("yes");
                readUntilTwo("Password:","password:","PASSWORD:");
                write(pw);
            }else if(out.trim().endsWith("Password:")||out.trim().endsWith("password:")||out.trim().endsWith("PASSWORD:")){
                write(pw);
            }
            if(null!=out && out.contains("Connection reset by peer")){
                throw new Exception("SFTP登录失败 "+"ip:"+ip);
            }
            out = readUntil(ftpPrompt +" ");
            _log.info("FTP登录"+ip+"成功...");
        }catch(Exception e){
            throw new Exception(e.getMessage());
        }
    }
    
    /**
     * 执行FTP命令获取文件
     * @param ip
     * @param user
     * @param passwd
     * @param filepath
     * @param filename
     * @return
     * @throws Exception 
     */
    public String getFilebySftp( String ip,String user,String passwd,String filepath,String filename ) throws Exception
    {
        String out = null;
        if(Environment.getInstance().getBankSwitchIsCzb()){
            loginSftpCzb(ip,user,passwd);
        }else{
            loginSftp(ip,user,passwd);
        }
        try
        {
            write("cd "+filepath);
            readUntil(String.valueOf(ftpPrompt));
            write("get "+filename);
            out = readUntil(String.valueOf(ftpPrompt));
            write("bye");
            readUntil(prompt+"");
            _log.info("FTP下载"+filename);
        } catch (Exception e)
        {
            _log.error("FTP下载出错" + e.getMessage() + " ip：" + ip);
        }finally{
            if(null!=this.out){
                this.out.close();
            }
            if(null!=this.in){
                this.in.close();
            }
            if(null!=this.shell && this.shell.isConnected()){
                this.shell.disconnect();
            }
        }
        return out;
    }
    
    private char ftpPrompt = '>';
    private char         prompt ;
    private String osType;
    private InputStream in;
    private PrintStream  out;
    static final private org.apache.log4j.Logger _log = org.apache.log4j.Logger
            .getLogger(SSHUtils.class);
}
