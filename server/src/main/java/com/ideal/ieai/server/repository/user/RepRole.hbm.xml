<?xml version="1.0"?>

<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 2.0//EN" 
    "http://hibernate.sourceforge.net/hibernate-mapping-2.0.dtd">

<hibernate-mapping>
    <class
        name="com.ideal.ieai.server.repository.user.RepRole"
        table="ieai_role"
        dynamic-update="false"
        dynamic-insert="false"
    >

        <id
            name="id"
            column="iid"
            type="java.lang.Long"
        >
            <generator class="com.ideal.ieai.server.repository.idgenerator.IdGenerator">
            </generator>
        </id>

        <property
            name="calendarId"
            type="long"
            update="true"
            insert="true"
            access="property"
            column="icalId"
        />

        <property
            name="description"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="idescrip"
        />

        <property
            name="name"
            type="java.lang.String"
            update="true"
            insert="true"
            access="property"
            column="iname"
        />

        <!--
            To add non XDoclet property mappings, create a file named
                hibernate-properties-RepRole.xml
            containing the additional properties and place it in your merge dir.
        -->

    </class>

</hibernate-mapping>
