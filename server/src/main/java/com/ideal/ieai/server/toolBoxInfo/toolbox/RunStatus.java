package com.ideal.ieai.server.toolBoxInfo.toolbox;

/**
 * 工具执行状态
 */
public enum RunStatus {
    //脚本服务化状态
    RUNNING("0", "运行中"),
    NOT_STARTED("1", "未开始"),
    COMPLETED("2", "完成"),
    EXCEPTION("3", "异常"),
    TERMINATED("4", "终止"),


    //日常操作状态
    ACT_STATE_FAIL("Fail","异常"),
    ACT_STATE_READY("Ready","运行中"),
//    ACT_STATE_MANUAL_RUNNING("ManualRunning","手工处理中"),
    ACT_STATE_SKIPPED("Skipped","终止"),
    ACT_STATE_RUNNING("Running","运行中"),
    ACT_STATE_FINISH("Finished","完成"),
    ACT_STATE_FAIL_SKIPPED ("Fail:Skipped","异常终止");

    private final String code;
    private final String name;


    RunStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RunStatus fromCode(String code) {
        for (RunStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null; // 如果没有匹配的代码，可以返回null或抛出异常，根据实际需求决定。
    }

    public static String getRunStatusName(String code)
    {
        switch(code)
        {
            case "Fail":
                return ACT_STATE_FAIL.getName();
            case "Ready":
                return ACT_STATE_READY.getName();
//            case "ManualRunning":
//                return ACT_STATE_MANUAL_RUNNING.getName();
            case "Skipped":
                return ACT_STATE_SKIPPED.getName();
            case "Running":
                return ACT_STATE_RUNNING.getName();
            case "Finished":
                return ACT_STATE_FINISH.getName();
            case "Fail:Skipped":
                return ACT_STATE_FAIL_SKIPPED.getName();
            default:
                return ACT_STATE_RUNNING.getName();
        }
    }

    public static String getRunStatusCode(String code)
    {
        switch(code)
        {
            case "Fail":
                return "3";
            case "Ready":
                return "0";
            case "Skipped":
                return "4";
            case "Running":
                return "0";
            case "Finished":
                return "2";
            default:
                return "0";
        }
    }


    public static String getRunWorkStatusName(String code) {
        switch(code)
        {
            case "2":
                return COMPLETED.getName();
            case "0":
                return RUNNING.getName();
            case "4":
                return TERMINATED.getName();
            case "55":
                return NOT_STARTED.getName();
            case "46":
                return EXCEPTION.getName();
            case "47":
                return EXCEPTION.getName();
            case "25":
                return TERMINATED.getName();
            default:
                return ACT_STATE_RUNNING.getName();
        }
    }

    public static String getRunWorkStatusNum(String code) {
        switch(code)
        {
            case "2":
                return COMPLETED.getCode();
            case "0":
                return RUNNING.getCode();
            case "4":
                return TERMINATED.getCode();
            case "55":
                return NOT_STARTED.getCode();
            case "46":
                return EXCEPTION.getCode();
            case "47":
                return EXCEPTION.getCode();
            case "25":
                return TERMINATED.getCode();
            default:
                return ACT_STATE_RUNNING.getCode();
        }
    }

    public static String getScriptStatusNum(String code) {
        switch(code)
        {
            case "10":
                return RUNNING.getCode();
            case "11":
                return RUNNING.getCode();
            case "20":
                return COMPLETED.getCode();
            case "30":
                return EXCEPTION.getCode();
            case "60":
                return TERMINATED.getCode();
            default:
                return RUNNING.getCode();
        }
    }
}
