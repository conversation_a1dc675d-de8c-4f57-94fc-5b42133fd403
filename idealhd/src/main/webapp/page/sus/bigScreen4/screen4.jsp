<%@page contentType="text/html; charset=utf-8" %>
<%@ page import="java.util.*" %>
<%@ page import="com.ideal.ieai.core.Environment" %>
<%@ page import="com.ideal.ieai.server.ieaikernel.ServerEnv" %>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>自动化部署监控</title>
    <link rel="stylesheet" href="css/screen_czbank.css"/>
    <script type="text/javascript" src="js/jquery-3.4.1.min.js"></script>
    <script type="text/javascript" src="js/jquery.nicescroll.js"></script>
    <script type="text/javascript" src="js/jquery.SuperSlide.2.1.3.js"></script>
    <script type="text/javascript" src="js/echarts.min.js"></script>
    <script type="text/javascript" src="js/echarts-liquidfill.min.js"></script>

</head>
<body>
<div class="deploy_bg">
    <div class="deploy_header">
        <div class="deply_logo"></div>
        <div class="header_text">自动化部署监控</div>
        <div class="deply_date">
            <span id="theClock1">09:35:26</span>
            <span class="deply_line"></span>
            <span id="theClock2">2020-5-12  星期二</span>
        </div>
    </div>
    <div class="deploy_left">
        <div class="deploy_sys deploy_text">
            <h1>当前正在部署系统</h1>
            <h2>Deployment system</h2>
            <div class="deploy_overall" id="bsxtDiv">
                <div class="dply_sys_part dply_sys_common deploy_red">
                    <h1 class="dply_title">B20支付</h1>
                    <div class="dply_status dply_status_common">异常</div>
                    <p class="dply_date">
                        <span>开始时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                    <p>
                        <span>结束时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                </div>
                <div class="dply_sys_part dply_sys_common deploy_blue">
                    <h1 class="dply_title">B20支付</h1>
                    <div class="dply_status dply_status_common">运行中</div>
                    <p class="dply_date">
                        <span>开始时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                    <p>
                        <span>结束时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                </div>
                <div class="dply_sys_part dply_sys_common deploy_green">
                    <h1 class="dply_title">B20支付</h1>
                    <div class="dply_status dply_status_common">正常</div>
                    <p class="dply_date">
                        <span>开始时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                    <p>
                        <span>结束时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                </div>
                <div class="dply_sys_part dply_sys_common deploy_green">
                    <h1 class="dply_title">B20支付</h1>
                    <div class="dply_status dply_status_common">正常</div>
                    <p class="dply_date">
                        <span>开始时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                    <p>
                        <span>结束时间</span>
                        <span>2020-05-13 16:00</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="deploy_sys deploy_text system_step">
            <h1>系统步骤</h1>
            <h2>System steps</h2>
         <%--   <div class="systep_overall picMarquee-left">
                <div class="bd">
                    <ul id='sysinfo'>
                        <li class="sys_step_slide">个人征信</li>
                        <li class="sys_step_slide">个人征信</li>
                        <li class="sys_step_slide">个人征信</li>
                        <li class="sys_step_slide">个人征信</li>
                        <li class="sys_step_slide">个人征信</li>
                        <li class="sys_step_slide">个人征信</li>
                    </ul>
                </div>
            </div>--%>
            <div class="step_part">
                <div class="step_part_img">
                    <div class="step_cn">
                        <p>步骤</p>
                        <p><span id="bigScreenConfig_bzm1"></span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--center-->
    <div class="deploy_center">
        <div class="dlpy_progress">
            <div class="total_number">
                <h1><span id="bigScreenConfig_total"></span></h1>
                <p>系统总数</p>
            </div>
            <div class="dep_progress_bg">
                <div id="container" class="dep_progress"></div>
                <div class="dep_percentage" id="bigScreenConfig_bfb">0%</div>
                <div class="dep_pro_text">部署进度</div>
            </div>
            <div class="finish_number">
                <h1><span id="bigScreenConfig_finish"></span></h1>
                <p>已完成系统总数</p>
            </div>
        </div>
        <div class="dlpy_cn">

            <div class="dlpy_cn_img">
                <h2>&nbsp;</h2>
                <h1><span id="bigScreenConfig_sjkssj1"></span></h1>
                <p>实际开始时间</p>
            </div>
            <div class="dlpy_cn_img dlpy_pos">
                <h2>&nbsp;</h2>
                <h1><span id="bigScreenConfig_yxsc"></span></h1>
                <p>部署时长</p>
            </div>
        </div>
    </div>
    <!--right-->
    <div class="deploy_sys_complete deploy_text">
        <h1>已完成部署系统</h1>
        <h2>Deployment system completed</h2>
        <div class="deploy_overall_complete" id="bsxtDivHis">
            <div class="dply_sys_part dply_sys_common deploy_green">
                <h1 class="dply_title">B20支付</h1>
                <div class="dply_status dply_status_common">完成</div>
                <p class="dply_date">
                    <span>开始时间</span>
                    <span>2020-05-13 16:00</span>
                </p>
                <p>
                    <span>结束时间</span>
                    <span>2020-05-13 16:00</span>
                </p>
            </div>

        </div>

    </div>
    <!--bottom-->
    <div class="histogram_bg deploy_text">
        <h1>系统部署进度</h1>
        <h2>System deployment progress</h2>
        <div style="width:100%;height: 100%;margin-top:-20px;" id="bigScreenConfig_zt">
        </div>
    </div>
    <script type="text/javascript">
        jQuery(".picMarquee-left").slide({
            mainCell: ".bd ul",
            autoPlay: true,
            effect: "leftMarquee",
            vis: 4,
            interTime: 25
        });
    </script>

    <script type="text/javascript">
        $(".deploy_overall").niceScroll({
            autohidemode: true,
            cursorcolor: "#01727e",
            cursorborder: "1px solid #01727e"
        });
        $(".deploy_overall_complete").niceScroll({
            autohidemode: true,
            cursorcolor: "#01727e",
            cursorborder: "1px solid #01727e"
        });
    </script>
    <script type="text/javascript">
        $(".output_text").niceScroll({
            autohidemode: true,
            cursorcolor: "#01727e",
            cursorborder: "1px solid #01727e"
        });
    </script>
    <script type="text/javascript">
        // 获取刷新时间
        var bigScreenConfig_refreshFrequency1 = <%=request.getAttribute("bigScreenConfig_refreshFrequency1")%>;// 大屏监控刷新频率
        var bigScreenConfig_refreshFrequency2 = <%=request.getAttribute("bigScreenConfig_refreshFrequency2")%>;// 柱图刷新频率
    </script>

    <script type="text/javascript">
        var instanceIdGoble = 0;
        var instanceIdArray = new Array();
        var bigScreenData_xtbz = new Array();
        var countNum3 = 0;//用于计算系统步骤滚动
        function setBuzhouxinxi() {
            if (countNum3 < bigScreenData_xtbz.length) {
                var stepName = bigScreenData_xtbz[countNum3].name2 == null ? "" : bigScreenData_xtbz[countNum3].name2;
                $('#bigScreenConfig_bzm1').html(stepName);
                window.setTimeout(setBuzhouxinxi,2000);
                countNum3++;
            }else{
                countNum3 =0;
            }
        }

        var countNum1 = 0;//用于计算部署系统滚动
        var countNum2 = 0;//用于计算部署完成系统滚动

        /**获取全部数据**/
        function getData1() {
            console.log("==============getData1");
            $.getJSON('getBigScreenData1.do', null, function (res) {
                var bigScreenData_bsxt1 = res.bigScreenData_bsxt1;
                var bigScreenData_bsxt2 = res.bigScreenData_bsxt2;
                bigScreenData_xtbz = res.bigScreenData_xtbz;

                var bigScreenConfig_yxsc = res.bigScreenConfig_yxsc;
                $('#bigScreenConfig_total').html(res.bigScreenConfig_total);
                $('#bigScreenConfig_sjkssj1').html(res.bigScreenConfig_sjkssj);
                $('#bigScreenConfig_yxsc').html(bigScreenConfig_yxsc);

                $('#bigScreenConfig_finish').html(res.bigScreenData_bsxt2.length);
                if (res.bfb > 100) {
                    $('#bigScreenConfig_bfb').html(100 + "%");
                } else {
                    $('#bigScreenConfig_bfb').html(res.bfb + "%");
                }


                var bsxtDivhtml = '';
                var countNum = 0;
                instanceIdArray = new Array();
                //滚动部署系统，一次4个
                var bigScreenData_bsxt1_final = new Array();
                $.each(bigScreenData_bsxt1, function (i, item) {
                    if (countNum1 <= i) {
                        bigScreenData_bsxt1_final.push(item);
                    }
                });
                if ((countNum1 + 4) < (bigScreenData_bsxt1.length)) {
                    countNum1 = countNum1 + 4;
                } else {
                    countNum1 = 0;
                }
                $.each(bigScreenData_bsxt1_final, function (i, item) {
                    //最多显示20条
                    if (countNum < 20) {

                        instanceIdArray.push(item.iid);
                        var name1 = item.name6 == null ? "" : item.name6;
                        var name2 = item.name2 == null ? "" : item.name2;
                        var name3 = item.name3 == null ? "" : item.name3;
                        var name4 = item.name4 == null ? "" : item.name4;
                        var classS = "deploy_blue";
                        if (name2 == '异常') {
                            classS = "deploy_red";
                        } else if (name2 == '完成') {
                            classS = "deploy_green";
                        }
                        if(name4 == ""){
                            bsxtDivhtml += '<div class="dply_sys_part2 dply_sys_common ' + classS + '"> <h1 class="dply_title">' + name1 + '</h1><div class="dply_status dply_status_common">' + name2 + '</div><p class="dply_date" ><span>开始时间</span><span>' + name3 + '</span></p></div>';

                        }else{
                        bsxtDivhtml += '<div class="dply_sys_part dply_sys_common ' + classS + '"> <h1 class="dply_title">' + name1 + '</h1><div class="dply_status dply_status_common">' + name2 + '</div><p class="dply_date"><span>开始时间</span><span>' + name3 + '</span></p><p><span>结束时间</span><span>' + name4 + '</span></p></div>';
                        }
                        }
                    countNum++;
                });
                $('#bsxtDiv').html(bsxtDivhtml);


                bsxtDivhtml = '';
                countNum = 0;
                //滚动部署完成系统，一次8个
                var bigScreenData_bsxt2_final = new Array();
                $.each(bigScreenData_bsxt2, function (i, item) {
                    if (countNum2 <= i) {
                        bigScreenData_bsxt2_final.push(item);
                    }
                });
                if ((countNum2 + 8) < (bigScreenData_bsxt2.length)) {
                    countNum2 = countNum2 + 8;
                } else {
                    countNum2 = 0;
                }
                $.each(bigScreenData_bsxt2_final, function (i, item) {
                    //最多显示20条
                    if (countNum < 20) {
                        var name1 = item.name6 == null ? "" : item.name6;
                        var name2 = item.name2 == null ? "" : item.name2;
                        var name3 = item.name3 == null ? "" : item.name3;
                        var name4 = item.name4 == null ? "" : item.name4;
                        var classS = "deploy_green";
                        if (name2 == '异常') {
                            classS = "deploy_red";
                        }
                        if(name4 == ""){
                            bsxtDivhtml += '<div class="dply_sys_part2 dply_sys_common ' + classS + '"> <h1 class="dply_title">' + name1 + '</h1><div class="dply_status dply_status_common">' + name2 + '</div><p class="dply_date" ><span>开始时间</span><span>' + name3 + '</span></p></div>';
                        }else {
                            bsxtDivhtml += '<div class="dply_sys_part dply_sys_common ' + classS + '"> <h1 class="dply_title">' + name1 + '</h1><div class="dply_status dply_status_common">' + name2 + '</div><p class="dply_date"><span>开始时间</span><span>' + name3 + '</span></p><p><span>结束时间</span><span>' + name4 + '</span></p></div>';
                        }
                        // bsxtDivhtml+='<tr><td><div class="dpart_name">'+name1+'</div></td><td><div class="dpart_status" ><span class="'+classS+'">'+name2+'</span></div></td><td><div class="dpart_hour01" >'+name3+'</div></td> <td><div class="dpart_hour01" >'+name4+'</div></td></tr>'
                    }
                    countNum++;
                });
                $('#bsxtDivHis').html(bsxtDivhtml);
                bsxtDivhtml = '';
                //系统步骤一次4个
                setBuzhouxinxi();


                countNum = 0;
                //滚动系统步骤，

                /*$.each(bigScreenData_xtbz, function (i, item) {
                    if (countNum3 <= i) {
                        bigScreenData_xtbz_final.push(item);
                    }
                });*/

                /*$.each(bigScreenData_xtbz_final, function (i, item) {
                    //最多显示20条
                    if (countNum < 20) {
                        var name1 = item.name6 == null ? "" : item.name6;
                        var name2 = item.name2 == null ? "" : item.name2;

                        if (countNum == 0) {
                            //默认显示第一个步骤的信息


                        }
                        //bsxtDivhtml += '<li class="sys_step_slide" title="' + name1 + '" onclick="setBuzhouxinxi(\'' + name2 + '\')">' + name1 + '</li>';
                    }
                    countNum++;
                });*/
                //$('#sysinfo').html(bsxtDivhtml);
            });

        }


        var countNum = 0;//用于计算柱图滚动
        var showNum = 15;//柱图显示柱数
        /**柱图数据**/
        function getData2() {
            console.log("==============getData2");
            $.getJSON('getBigScreenData2.do', null, function (res) {
                /**投产窗口显示**/
                var bigScreenData_yzhou_final = new Array();
                var bigScreenData_xzhou_final = new Array();
                var bigScreenData_yzhou = res.bigScreenData_yzhou;
                var bigScreenData_xzhou = res.bigScreenData_xzhou;
                //柱图要求滚动显示，如第一次显示 1-7位置的柱，第二次显示2-8位置的柱，依次滚动，遍历到最后，返回到开始从新计算
                $.each(bigScreenData_yzhou, function (i, item) {
                    if (countNum <= i) {
                        if (bigScreenData_yzhou_final.length < showNum) {
                            bigScreenData_yzhou_final.push(item);
                        }
                    }

                });
                $.each(bigScreenData_xzhou, function (i, item) {
                    if (countNum <= i) {
                        if (bigScreenData_xzhou_final.length < showNum) {
                            bigScreenData_xzhou_final.push(item);
                        }
                    }

                });
                if ((countNum + showNum) < (bigScreenData_yzhou.length)) {
                    countNum++;
                } else {
                    countNum = 0;
                }
                //为柱图填数
                initZt(bigScreenData_yzhou_final, bigScreenData_xzhou_final);
            });

        }

        var myChart2 = null;

        /**为柱图填数**/
        function initZt(bigScreenData_yzhou, bigScreenData_xzhou) {
            if (myChart2 != null) {
                myChart2.dispose();
            }
            myChart2 = echarts.init(document.getElementById('bigScreenConfig_zt'));

            option = {
                calculable: true,
                animation: false,
                xAxis: [
                    {
                        type: 'category',
                        axisLine: {  //这是x轴文字颜色
                            lineStyle: {
                                color: "#03fafc",
                            }
                        },
                        axisLabel: {

                            rotate: -25,
                            textStyle: {
                                color: "#03fafc"
                            }
                        },

                        data: bigScreenData_xzhou
                    }
                ],
                yAxis: [
                    {
                        splitLine: {show: false},
                        axisLine: {//这是y轴文字颜色
                            lineStyle: {
                                color: "#020d1b",
                            }
                        },
                        axisLabel: {
                            formatter: '{value} %',
                            color: "#03fafc"
                        },
                        type: 'value'
                    }
                ],
                series: [
                    {
                        name: '完成率',
                        itemStyle: {
                            normal: {
                                barBorderRadius: [10, 10, 10, 10],//设置柱图圆角
                                color: new echarts.graphic.LinearGradient(
                                    0, 0, 0, 1,
                                    [
                                        {offset: 0, color: '#03fafc'},                   //柱图渐变色
                                        {offset: 0.5, color: '#44C0C1'},                 //柱图渐变色
                                        {offset: 1, color: '#000000'},                   //柱图渐变色
                                    ]
                                )
                            },
                            emphasis: {
                                color: new echarts.graphic.LinearGradient(
                                    0, 0, 0, 1,
                                    [
                                        {offset: 0, color: '#71C8B1'},                  //柱图高亮渐变色
                                        {offset: 0.7, color: '#44C0C1'},                //柱图高亮渐变色
                                        {offset: 1, color: '#000000'}                   //柱图高亮渐变色
                                    ]
                                )
                            }
                        },
                        barWidth: 15,
                        type: 'bar',
                        data: bigScreenData_yzhou,
                    }
                ]
            };
            myChart2.setOption(option);
        }

        getData1();// 首次进入执行一次全量数据查询
        getData2();// 首次进入执行一次柱图数据查询
        window.setInterval(getData1, bigScreenConfig_refreshFrequency1 * 1000);// 定时查询全部数据
        window.setInterval(getData2, bigScreenConfig_refreshFrequency2 * 1000);//定时查询柱图

    </script>

    <script type="text/javascript">
        /** 增加时钟显示* */
        var days = new Array("日", "一", "二", "三", "四", "五", "六");

        function showDT() {
            var currentDT = new Date();
            var y, m, date, day, hs, ms, ss, theDateStr;
            y = currentDT.getFullYear(); // 四位整数表示的年份
            m = currentDT.getMonth() + 1; // 月 返回值是0-11,需要加一个1 才能达到1-12月
            date = currentDT.getDate(); // 日
            day = currentDT.getDay(); // 星期
            hs = currentDT.getHours(); // 时
            ms = currentDT.getMinutes(); // 分
            ss = currentDT.getSeconds(); // 秒
            //月份不足两位，用0占位
            if (m < 10) {
                m = '0' + m;
            }
            //日期不足两位，用0占位
            if (date < 10) {
                date = '0' + date;
            }
            //小时不足两位，用0占位
            if (hs < 10) {
                hs = '0' + hs;
            }
            //分钟不足两位，用0占位
            if (ms < 10) {
                ms = '0' + ms;
            }
            //秒数不足两位，用0占位
            if (ss < 10) {
                ss = '0' + ss;
            }
            //theDateStr = y + "年" + m + "月" + date + "日 星期" + days[day] + " " + hs + ":" + ms + ":" + ss;
            document.getElementById("theClock1").innerHTML = hs + ":" + ms + ":" + ss;
            document.getElementById("theClock2").innerHTML = y + "-" + m + "-" + date + " 星期" + days[day];

            // setTimeout 在执行时,是在载入后延迟指定时间后,去执行一次表达式,仅执行一次
            window.setTimeout(showDT, 1000);
        }

        showDT();
    </script>
</div>
</body>
</html>