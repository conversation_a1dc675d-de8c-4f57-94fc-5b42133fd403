--8.2.0
	    BEGIN
			DECLARE LS_SQL VARCHAR(2000);
			DECLARE  LI_EXISTS  NUMERIC(2);
			
			SELECT	SIGN(COUNT(*)) INTO LI_EXISTS FROM SYSCAT.COLUMNS WHERE TABNAME='IEAI_PROJECT_AGENTINFO' AND  COLNAME='IAGENTIP';
			IF	LI_EXISTS = 1 THEN
			SET	LS_SQL = 'ALTER TABLE IEAI_PROJECT_AGENTINFO alter column IAGENTIP set data type varchar(15)';
				PREPARE	SQLA FROM LS_SQL; 
				EXECUTE SQLA;
			END	IF;
			
		END
			 $