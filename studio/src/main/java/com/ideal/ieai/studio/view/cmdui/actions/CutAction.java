package com.ideal.ieai.studio.view.cmdui.actions;

import java.awt.event.ActionEvent;

import com.ideal.ieai.studio.util.IEdit;
import com.ideal.ieai.studio.view.cmdui.CmdIcons;
import com.ideal.ieai.studio.view.coordinator.StudioCoordinator;
import com.ideal.ieai.studio.view.editorui.EditorManager;


/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003 Ideal Technologies Inc.</p>
 * <p>Company: Ideal Technologies Inc.</p>
 * <AUTHOR> attributable
 * @version 1.0
 */

public class CutAction
    extends StudioAction
{
    public CutAction()
    {
        super("", CmdIcons.imageCut);
    }

    protected void execute(ActionEvent e)
    {
        if (null != EditorManager.getInstance().getCurSelectedPanel())
        {
            IEdit edit = EditorManager.getInstance().getCurSelectedPanel().getEditor().getIEdit();
            if (null != edit)
            {
                edit.cut(e);
                StudioCoordinator.getInstance().firePasteEnable(true);
            }
        }
    }

    protected void alterViews()
    {
    }
}