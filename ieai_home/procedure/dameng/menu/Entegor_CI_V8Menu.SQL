INSERT INTO IEAI_GROUPMESSAGE (GROUPID,GROUPNAME,GROUPDESCRIPTION,IORDER,IIMG) VALUES (31,'流水线-CI','流水线-CI',102,'images/info254.png');

INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33001,'个人工作台',31,'',1,'','images/info39.png',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33002,'项目概述',31,'',2,'','images/info39.png',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33003,'系统管理',31,'',3,'','images/info39.png',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33004,'持续交付',31,'',4,'','images/info39.png',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33005,'配置管理',31,'',5,'','images/info39.png',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33006,'插件管理',31,'',6,'','images/info39.png',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33007,'报表管理',31,'',7,'','images/info39.png',0,0);


INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33008,'全景概览',31,'switchCICD.do',4,'个人工作台','null',0,0);

INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33009,'项目信息',31,'accessProjectInForCiCd.do',2,'项目概述','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33010,'投产概述',31,'accessCI_SEQUENCE.do',1,'项目概述','null',0,0);

INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33011,'制品管理',31,'productmanage/initProduct.do',6,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33012,'历史监控',31,'accessHisCiCdMonitor.do',3,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33013,'任务监控',31,'accessConDelTaskMon.do',2,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33014,'流水线任务',31,'accessCICDFlowTask.do',1,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33015,'代码清单',31,'accessCICD_CodeList.do',8,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33016,'环境管理',31,'resourceGroup/framOfEvn.do',1,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33017,'资源组管理',31,'resourceGroup/framOfBer.do',3,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33018,'参数管理',31,'resourceGroup/frame_mgr_param.do',4,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33019,'模块管理',31,'modelTypePocInit.do',6,'持续交付','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33020,'构建服务器管理',31,'ci/server.do',5,'持续交付','null',0,0);

INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33021,'配置列表',31,'accessCI_CONFIGFILE.do',1,'配置管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33022,'历史版本',31,'accessCI_CONFIG_HISTORY.do',3,'配置管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33023,'生产IP管理',31,'produceAgentmgr.do',4,'配置管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33024,'我的脚本',31,'forwardScriptServiceRelease.do',1,'插件管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33025,'测试历史',31,'forwardscriptcoat.do',2,'插件管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33026,'脚本类别维护',31,'forwardBsManager.do',4,'插件管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33027,'共享脚本',31,'forwardScriptShare.do',5,'插件管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33028,'全部脚本',31,'viewAllScripts.do',6,'插件管理','null',0,0);

INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33029,'部署统计报表',31,'accessCICD_DeployReport.do',2,'报表管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33030,'流水线统计报表',31,'accessCICD_PipeLineReport.do',1,'报表管理','null',0,0);

INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33031,'机构管理',31,'initOrgManagement.do',1,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33032,'用户组管理',31,'getUserGroup.do',4,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33033,'业务系统管理',31,'initBusinessSystem.do?urlType=5',5,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33034,'操作日志',31,'audit.do',7,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33035,'服务信息配置',31,'ftpinfoindex.do',8,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33036,'清理策略',31,'accessCleanConfig.do',9,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33037,'Agent管理',31,'agentMaintainIndex.do',10,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33038,'在线用户',31,'onlineUsers.do',12,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33039,'在线开关管理',31,'onlineConfig.do',11,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33040,'临时-日志字典',31,'audit/dataDictionaryPage.do',13,'系统管理','null',0,0);
INSERT INTO  IEAI_MENU  ( IID , INAME , IGROUPMESSID , IURL , IORDER , IPARENTNAME , IIMG , AUDITENBLE , IISAPP ) VALUES (33041,'proxy代理管理',31,'proxyManager.do',14,'系统管理','null',0,0);
INSERT INTO IEAI_MENU(IID,INAME,IGROUPMESSID,IURL,IORDER,IPARENTNAME,IIMG,AUDITENBLE,IISAPP) VALUES (33042,'关键命令',31,'scriptDangerCmd.do',7,'脚本模版','null',0,0);
INSERT INTO IEAI_MENU(IID,INAME,IGROUPMESSID,IURL,IORDER,IPARENTNAME,IIMG,AUDITENBLE,IISAPP) VALUES (33043,'插件中心',31,'accessPulgInForCiCdController.do',2,'插件管理','null',0,0);

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33001, '添加', 'bangdinSystem.do', '项目信息-查看-业务系统-添加按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33001, 33009, 33001, '增添业务系统');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33002, '删除', 'deleteSystemAndInc.do', '项目信息-查看-业务系统-删除按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33002, 33009, 33002, '删除业务系统');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33003, '同步项目', 'manualSynchronization.do', '项目信息-同步项目');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33003, 33009, 33003, '同步项目');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33004, '项目信息查询', 'queryProjectInForCiCd.do', '项目信息查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33004, 33009,33004, '项目信息查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33005, '保存', 'saveConfigManage.do', '项目概述-投产概述-编辑按钮-配置管理-确认按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33005, 33010, 33005, '保存配置项信息');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33006, '保存', 'saveAutoTest.do', '项目概述-投产概述-编辑按钮-自动化-确认按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33006, 33010, 33006, '保存自动化信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33007, '导出', 'ExportSequence.do', '项目概述-投产概述-导出按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33007, 33010, 33007, '投产概述-导出');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33008, '同步', 'sequenceSynchronization.do', '项目概述-投产概述-同步按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33008, 33010, 33008, '同步自动化信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33009, '投产概述查询', 'queryCI_SEQUENCE.do', '投产概述查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33009, 33010,33009, '投产概述查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33010, '取消', 'riseCancel.do', '流水线任务-晋级日志-取消（超链接）');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33010, 33014,33010, '取消');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33011, '撤回', 'riseWithdraw.do', '流水线任务-晋级日志-撤回（超链接）');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33011, 33014,33011, '撤回');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33012, '晋级', 'checkCIPromotionInfo.do', '持续交付-流水线任务-晋级按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33012, 33014, 33012, '晋级按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33013, '发布', 'updateCICDFlowTask.do', '持续交付-流水线任务-创建任务-发布按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33013, 33014, 33013, '更改任务发布状态');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33014, '删除', 'deleteCICDFlowTask.do', '持续交付-流水线任务-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33014, 33014, 33014, '删除新创任务');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33015, '保存', 'saveCICDFlowTask.do', '持续交付-流水线任务-创建任务-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33015, 33014, 33015, '保存任务信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33016, '启用', 'updateFlowTaskStartUse.do', '持续交付-流水线任务-启用按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33016, 33014, 33016, '更改任务启用状态');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33017, '复制', 'copyCICDFlowTask.do', '持续交付-流水线任务-超链接复制');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33017, 33014,33017, '复制任务');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33018, '部署-执行', 'accessCICDDeployExecStart.do', '持续交付-流水线任务（部署）-超链接执行');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33018, 33014,33018, '执行');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33019, '常规-执行', 'accessCICDExecStart.do', '持续交付-流水线任务（常规）-超链接执行');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33019, 33014,33019, '执行');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33020, '构建-执行', 'accessCICDBuildExecStart.do', '持续交付-流水线任务（构建）-超链接执行');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33020, 33014,33020, '执行');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33021, '编辑', 'accessCICDTaskDetailCD.do', '持续交付-CD侧流水线任务-编辑权限');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33021, 33014,33021, '编辑');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33022, '流水线任务-导出', 'riseFlowTaskExport.do', '流水线任务-导出');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33022,33014,33022, '下流水线任务-导出');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33023, '流水线任务-导入', 'getZip.do', '流水线任务-导入');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33023,33014,33023, '下流水线任务-导入');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33024, '流水线任务查询', 'queryCICDFlowTaskById.do', '流水线任务查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33024, 33014,33024, '流水线任务查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33025, '保存', 'saveCICD_CodeList.do', '持续交付-代码清单-新增-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33025, 33015, 33025, '保存新增代码清单');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33026, '保存', 'editCICD_CodeList.do', '持续交付-代码清单-超链接编辑-保存按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33026, 33015, 33026, '代码清单编辑连接的保存');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33027, '复制', 'copyCodeList.do', '持续交付-代码清单-超链接复制');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33027, 33015, 33027, '代码清单的复制');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33028, '删除', 'deleteCICD_CodeOne.do', '持续交付-代码清单-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33028, 33015, 33028, '代码清单的删除');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33029, '代码清单-导出', 'ExportConfig.do', '代码清单-导出');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33029,33015,33029, '代码清单-导出');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33030, '代码清单查询', 'queryCICD_CodeList.do', '代码清单查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33030, 33015,33030, '代码清单查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33031, '保存', 'resourceGroup/envSave.do', '持续交付-流水线配置-环境管理-环境管理-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33031, 33016, 33031, '保存环境管理信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33032, '删除', 'resourceGroup/envDelete.do', '持续交付-流水线配置-环境管理-环境管理-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33032, 33016, 33032, '删除环境管理信息');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33033, '设置为上线发起环境', 'resourceGroup/setOnlineEnv.do', '持续交付-流水线配置-环境管理-环境管理-设置为上线发起环境按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33033, 33016, 33033, '设置为上线发起环境');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33034, '保存', 'resourceGroup/envSave_product.do', '持续交付-流水线配置-环境管理-生产环境-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33034, 33016, 33034, '保存生产环境信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33035, '删除', 'resourceGroup/envDelete_product.do', '持续交付-流水线配置-环境管理-生产环境-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33035, 33016,33035, '删除生产环境信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33036, '环境管理查询', 'resourceGroup/envQuery.do', '流水线配置-环境管理查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33036, 33016,33036, '环境管理查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33037, '生产环境中业务环境配置查询', 'resourceGroup/envQuery_product.do', '生产环境中业务环境配置查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33037, 33016,33037, '生产环境中业务环境配置查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33038, '保存', 'binder/IBusSysBindEnvUpdate.do', '持续交付-流水线配置-资源组-环境资源组-业务环境-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33038, 33017,33038, '业务环境保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33039, '保存', 'resourceGroup/saveGpAndBindEvn.do', '持续交付-流水线配置-资源组-环境资源组-环境资源组-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33039, 33017,33039, '环境资源组环境保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33040, '删除', 'resourceGroup/groupsDelete.do', '持续交付-流水线配置-资源组-环境资源组-环境资源组-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33040, 33017,33040, '环境资源组环境删除');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33041, '复制', 'resourceGroup/groupCopyAndBind.do', '持续交付-流水线配置-资源组-环境资源组-环境资源组-复制按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33041, 33017,33041, '环境资源组环境复制');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33042, '保存', 'resourceGroup/groupParaSave.do', '持续交付-流水线配置-资源组-环境资源组-资源组参数列表-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33042, 33017,33042, '资源组参数列表保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33043, '删除', 'resourceGroup/groupsParamsDelete.do', '持续交付-流水线配置-资源组-环境资源组-资源组参数列表-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33043, 33017,33043, '资源组参数列表删除');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33044, '资源组-业务系统查询', 'getBusinesssys.do', '资源组-业务系统查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33044, 33017,33044, '资源组-业务系统查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33045, '资源组-业务环境查询', 'getEnvBySysId.do', '资源组-业务环境查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33045, 33017,33045, '资源组-业务环境查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33046, '资源组-环境资源组查询', 'getBindBEG.do', '资源组-环境资源组查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33046, 33017,33046, '资源组-环境资源组查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33047, '资源组-资源组参数列表查询', 'resourceGroup/groupPara.do', '资源组-资源组参数列表查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33047, 33017,33047, '资源组-资源组参数列表查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33048, '保存', 'saveIpubvar.do', '持续交付-流水线配置-参数管理-公共参数-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33048, 33018,33048, '公共参数保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33049, '删除', 'deleteIpubvar.do', '持续交付-流水线配置-参数管理-公共参数-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33049, 33018,33049, '公共参数删除');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33050, '保存', 'saveStaticParam.do', '持续交付-流水线配置-参数管理-静态参数-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33050, 33018,33050, '静态资源保存');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33051, '删除', 'deleteStaticParam.do', '持续交付-流水线配置-参数管理-静态参数-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33051, 33018,33051, '静态资源删除');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33052, '公共参数查询', 'getIpubvar.do', '参数管理-公共参数查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33052, 33018,33052, '参数管理-公共参数查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33053, '静态参数查询', 'getStaticParam.do', '参数管理-静态参数查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33053, 33018,33053, '参数管理-静态参数查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33054, '保存', 'ci/saveServer.do', '持续交付-流水线配置-构建服务器-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33054, 33020,33054, '保存数据');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33055, '删除', 'ci/deleteServer.do', '持续交付-流水线配置-构建服务器-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33055, 33020,33055, '删除数据');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33056, '构建服务器管理查询', 'ci/getServerList.do', '构建服务器管理查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33056, 33020,33056, '构建服务器管理查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33057, '保存', 'saveIeaiVersionManagerMt.do', '持续交付-流水线配置-模块管理保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33057, 33019,33057, '保存数据');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33058, '删除', 'deleteIeaiVersionManagerMt.do', '持续交付-流水线配置-模块管理删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33058, 33019,33058, '保存数据');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33059, '模块管理查询', 'getModelTypePoc.do', '模块管理查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33059, 33019,33059, '模块管理查询');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33060, '保存', 'saveuser.do', '系统管理-用户管理-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33060, 14, 33060, '保存用户管理数据');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33061, '锁定', 'lockUser.do', '系统管理-用户管理-锁定按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33061, 14,33061, '修改用户的状态');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33062, '解锁', 'unlockUser.do', '系统管理-用户管理-解锁按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33062, 14, 33062, '修改用户的状态');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33063, '克隆', 'saveGroupRole.do', '系统管理-用户管理-克隆按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33063, 14,33063, '复制用户数据');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33064, '删除', 'deleteuser.do', '系统管理-用户管理-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33064, 14,33064, '删除用户数据');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33065, '保存', 'saveUserGroups.do', '系统管理-用户管理-查看用户组-超链接保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33065, 14,33065, '保存用户的用户组信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33066, '保存', 'saveroles.do', '系统管理-用户管理-查看角色-超链接保存按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33066, 14, 33066, '保存用户的角色信息');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33067, '统一门户同步', 'unifyCICDSyncUser.do', '用户管理-统一门户同步');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33067, 14,33067, '统一门户同步');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33068, '保存', 'saveRoles.do', '系统管理-角色管理-保存按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33068, 20,33068, '保存角色信息');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33069, '保存', 'saveUserToRole.do', '角色管理-配置用户超链接中的保存按钮');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33069, 20,33069, '保存');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33070, '配置权限', 'saveProjectPermissions.do', '系统管理-角色管理-配置权限管理按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33070, 20, 33070, '给用户配置权限');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33071, '用户自动赋权配置', 'saveUserIisextend.do', '系统管理-角色管理-用户自动赋权配置按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33071, 20,33071, '自动赋权');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33072, '删除', 'deleteRoles.do', '系统管理-角色管理-删除按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33072, 20,33072, '删除角色信息');
--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33167, '用户管理查询', 'userdisplay.do', '用户管理查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33167, 14,33167, '用户管理查询');

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33168, '角色管理查询', 'getRoleListByFilter.do', '角色管理查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33168, 20,33168, '角色管理查询');
 


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33073, '更新（生产）', 'editCI_CONFIGFILE.do?flag=0', '配置项列表-编辑-超链接更新按钮（生产）'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33073, 33021,33073, '配置项列表-编辑-超链接更新按钮（生产）');
 


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33074, '更新（非生产）', 'editCI_CONFIGFILE.do?flag=1', '配置项列表-编辑-超链接更新按钮（非生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33074, 33021,33074, '配置项列表-编辑-超链接更新按钮（非生产）');
 



INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33075, '复制', 'copyCI_CONFIGFILE.do', '配置项列表-复制-超链接开始复制按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33075, 33021,33075, '开始复制');
 	


INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33076, '删除（生产）', 'deleteCI_CONFIGFILE.do?flag=0', '配置项列表-超链接删除（生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33076, 33021,33076, '删除');
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33077, '删除（非生产）', 'deleteCI_CONFIGFILE.do?flag=1', '配置项列表-超链接删除（非生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33077, 33021,33077, '删除');
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33079, '配置对比', 'compareCI_CONFIGFILE1.do', '配置项列表-配置对比按钮');    
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33079, 33021,33079, '配置对比');
 


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33080, '配置对比（生产）', 'compareCI_CONFIGFILE_authControl.do?flag=0', '配置项列表-配置对比按钮（生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33080, 33021,33080, '配置对比');
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33081, '配置对比（非生产）', 'compareCI_CONFIGFILE_authControl.do?flag=1', '配置项列表-配置对比按钮（非生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33081, 33021,33081, '配置对比');
 



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33082, '推送', 'CI_pushItem.do', '配置项列表-推送按钮');    
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33082, 33021,33082, '推送');
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33083, '采集', 'CICD_pullItem.do', '配置项列表-采集按钮');    
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33083, 33021,33083, '采集');
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33084, '查看日志', 'auditCI_CONFIGFILE1.do', '配置项列表-查看日志按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33084, 33021,33084, '查看日志');	
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33085, '查看日志（生产）', 'auditCI_CONFIGFILE1_authControl.do?flag=0', '配置项列表-查看日志按钮（生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33085, 33021,33085, '查看日志');
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33086, '查看日志（非生产）', 'auditCI_CONFIGFILE1_authControl.do?flag=1', '配置项列表-查看日志按钮（非生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33086, 33021,33086, '查看日志');
 


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33087, '标记最新版', 'productmanage/updateGenericLastMvcc.do', '制品管理-制品仓库-版本数-标记最新版');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33087,33011,33087, '标记最新版');

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33088, '上传制品', 'productmanage/saveStore.do', '制品管理-上传制品按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33088,33011, 33088, '添加制品');	
 	

 
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33089, '关联后补工单', 'productmanage/afterSyncOrd.do', '制品管理-关联后补工单按钮');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33089,33011,33089, '关联后补工单');
  

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33090, '删除', 'productmanage/deleteStore.do', '制品管理-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33090,33011,33090, '删除');	
 	
 
  
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33091, '晋级', 'productmanage/upgradeStore.do', '制品管理-晋级');    
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33091,33011,33091, '晋级');	
 	




 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33092, '移动', 'productmanage/removeGeneric.do', '制品管理-制品仓库链接-超链接移动按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33092,33011,33092, '移动数据');	
 
 



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33093, '上传制品', 'productmanage/pushGeneric.do', '制品管理-制品仓库链接-超链接上传制品按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33093,33011,33093, '上传制品');
 	


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33094, '删除', 'productmanage/deleteGeneric.do', '制品管理-制品仓库链接-超链接删除按钮');   
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33094,33011,33094, '删除');	
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33095, '修改状态', 'productmanage/updateGenericStatus.do', '制品管理-制品仓库链接-超链接修改按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33095,33011,33095, '更改制品信息');	
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33096, '保存', 'saveScriptEdit.do', '脚本模块-我的脚本-创建-保存'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33096,33024,33096, '保存数据');
 	

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33322, '删除', 'deleteScriptParams.do', '脚本模块-编辑-我的脚本-删除'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33322,33024,33322, '删除');

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33097, '保存', 'updateScriptEdit.do', '脚本模块-我的脚本-编辑-保存'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33097,33024,33097, '保存数据');	
 	

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33098, '共享', 'scriptService/serviceRelease.do', '脚本模块-我的脚本-共享'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33098,33024,33098, '共享');	
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33099, '删除', 'scriptService/deleteScriptForTest.do', '脚本模块-我的脚本-删除按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33099,33024,33099, '删除');	
 	


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33100, '发布', 'scriptPublishAuditing.do', '脚本模块-我的脚本-发布按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33100,33024,33100, '发布');	
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33101, '转移', 'changeTransCreateUser.do', '脚本模块-我的脚本-创建者转移按钮'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33101,33024,33101, '转移');	
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33102, '导入', 'importBsName.do', '脚本类别维护-导入');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33102, 33026,33102, '导入');
 	


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33103, '保存', 'saveBsManager.do', '脚本类别维护-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33103, 33026,33103, '保存');
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33104, '删除', 'deleteBsModel.do', '脚本类别维护-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33104, 33026,33104, '删除');
 	


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33105, '恢复', 'updateBs.do', '脚本类别维护-恢复');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33105, 33026,33105, '恢复');
 


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33106, '同步', 'syncorgManagement.do', '系统管理-机构管理-同步'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33106, 33031,33106, '同步数据');
 

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33107, '保存', 'saveUserGroup.do', '系统管理-用户组管理-用户组-保存'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33107, 33032,33107, '保存数据');	
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33108, '删除', 'deleteUserGroup.do', '系统管理-用户组管理-用户组-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33108, 33032,33108, '删除数据');
 	



 
--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33109, '绑定', 'bindUserList.do', '系统管理-用户组管理-用户信息-绑定');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33109, 33032,33109, '绑定数据');
 	



 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33110, '删除', 'deleteUserGroupInfo.do', '系统管理-用户组管理-用户信息-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33110, 33032,33110, '删除数据');
 	


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33111, '保存', 'saveFtpInfo.do', '系统管理-服务信息配置-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33111, 33035, 33111, '保存数据');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33112, '删除', 'deleteFtpInfo.do', '系统管理-服务信息配置-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33112, 33035, 33112, '删除数据');	
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33113, '保存', 'saveBusinessSystem.do', '系统管理-业务系统管理-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33113, 33033,33113, '保存数据');
 	


 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33114, '删除', 'deleteBusinessSystem.do', '系统管理-业务系统管理-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33114, 33033, 33114, '删除数据');
 	





 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33115, '执行失败', 'ciCdMonFailArrange.do', '任务监控-执行失败');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33115, 33013,33115, '执行失败');
 	

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33116, '暂停', 'ciCdMonHalt.do', '任务监控-暂停');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33116, 33013,33116, '暂停');
 	

 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33117, '继续', 'ciCdMonGoOn.do', '任务监控-继续');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33117, 33013,33117, '继续');
 

 
 
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33118, '自定义命令', 'updateIcustomCmd.do', 'Agent管理-编辑Agent-自定义命令');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33118, 33037,33118, '自定义命令');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33119, '保存', 'saveAgentMaitainInfos.do', 'Agent管理-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33119, 33037,33119, '保存');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33120, '导入', 'uploadAgentCfg.do', 'Agent管理-Excel-导入');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33120, 33037,33120, '导入');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33121, '导入应用标识', 'importIsystemTypeExcel.do', 'Agent管理-Excel-导入应用标识');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33121, 33037,33121, '导入应用标识');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33122, '删除', 'deleteAgentMaintainInfos.do', 'Agent管理-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33122, 33037,33122, '删除');



INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33124, '获取信息', 'fetchAgentInfoQuery.do', 'Agent管理-获取信息');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33124, 33037,33124, '获取信息');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33125, '获取全部信息', 'fetchAgentInfo.do', 'Agent管理-获取全部信息');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33125, 33037,33125, '获取全部信息');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33126, '导入描述', 'importIsystemTypeExceldes.do', 'Agent管理-Excel-导入描述');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33126, 33037,33126, '导入描述');
 
 

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33127, '导入（生产）', 'impExcelData.do?flag=0', '配置项列表-导入配置文件(生产)');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33127, 33021,33127, '配置项列表-导入配置文件(生产)');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33128, '导入（非生产）', 'impExcelData.do?flag=1', '配置项列表-导入配置文件(非生产)');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33128, 33021,33128, '配置项列表-导入配置文件(非生产)');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33129, '下载导入模版', 'downloadExcelForSystem.do', '配置项列表-导入-下载导入模版（非生产）');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33129, 33021,33129, '下载导入模版');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33130, '下载导入模版', 'downloadExcel.do', '配置项列表-导入-下载导入模版（生产）');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33130, 33021,33130, '下载导入模版');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33131, '导出', 'exportAllConfigFile.do', '配置项列表-导出（非生产）');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33131, 33021,33131, '导出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33132, '导出', 'exportAllConfigFile1.do', '配置项列表-导出（生产）');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33132, 33021,33132, '导出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33133, '确定', 'execScriptServiceForSync.do', '脚本模块-我的脚本-自测'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33133,33024,33133, '确定');	

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33134, '确定', 'scriptStatus.do', '脚本模块-我的脚本-查看版本-版本回退'); 
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33134,33024,33134, '确定');	

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33135, '保存', 'saveUserGroupInfo.do', '用户组管理-用户信息保存按钮');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33135, 33032,33135, '保存');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33136, '同步', 'syncSystem.do', '业务系统管理-同步');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33136, 33033,33136, '同步');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33137, '确定', 'addGroupToSys.do', '系统管理-业务系统管理-用户组赋权-增加');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33137, 33033,33137, '确定');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33138, '确定', 'addUserToSys.do', '系统管理-业务系统管理-用户赋权-增加');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33138, 33033,33138, '确定');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33139, '删除', 'delGroupToSys.do', '系统管理-业务系统管理-用户组赋权-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33139, 33033,33139, '删除');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33140, '删除', 'delUserToSys.do', '系统管理-业务系统管理-用户赋权-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33140, 33033,33140, '删除');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33141, '踢出', 'kikoutUsers.do', '在线用户-踢出');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33141, 33038,33141, '踢出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33142, '在线用户导出', 'exportOnlineusers.do', '在线用户导出');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33142, 33038,33142, '在线用户导出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33143, '机构管理页面查询', 'getOrgManagementList.do', '机构管理页面查询');     
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33143,33031,33143, '机构管理页面查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33144, '清理策略查询', 'queryCleanConfig.do', '系统管理-清理策略查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33144,33036,33144, '清理策略查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33145, '保存', 'saveConfig.do', '系统管理-清理策略-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33145, 33036,33145, '保存数据');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33146, '导出', 'exportIPAll.do', 'Agent管理-Excel-导出');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33146,33037,33146, '导出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33147, '保存', 'saveButtons.do', '菜单管理-按钮维护-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33147,25,33147, '保存');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33148, '生产IP查询', 'produceAgentList.do', '生产IP管理超链接-查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33148,33023,33148, '生产IP查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33149, '生产IP保存', 'saveProduceAgent.do', '生产IP管理超链接-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33149,33023,33149, '生产IP保存');
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33150, '生产IP删除', 'delProduceAgent.do', '生产IP管理超链接-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33150,33023,33150, '生产IP删除');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33151, '生产IP导入', 'importProductionAgent.do', '生产IP管理超链接-导入');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33151,33023,33151, '生产IP导入');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33152, '生产IP导出', 'exportProductionAgent.do', '生产IP管理超链接-导出');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33152,33023,33152, '生产IP导出');
 

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33153, '任务监控查询', 'queryConMonitorStore.do', '任务监控查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33153, 33013,33153, '任务监控查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33154, '历史监控查询', 'queryHisCiCdMonitor.do', '历史监控查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33154, 33012,33154, '历史监控查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33155, '制品管理查询', 'productmanage/queryStoreListPage.do', '制品管理查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33155,33011,33155, '制品管理查询');
 


INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33156, '配置列表查询', 'queryCI_CONFIGFILE.do', '配置列表查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33156, 33021,33156, '配置列表查询');


INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33157, '配置项列表-生产查询', 'queryCI_CONFIGFILE.do?flag=0', '配置项列表-查询(生产)');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33157, 33021,33157, '配置项列表-查询(生产)');
 

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33158, '配置项列表-非生产查询', 'queryCI_CONFIGFILE.do?flag=1', '配置项列表-查询(非生产)');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33158, 33021,33158, '配置项列表-查询(非生产)');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33159, '配置项历史版本查询', 'queryCI_CONFIG_HISTORY.do', '配置项历史版本查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33159, 33022,33159, '配置项历史版本查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33160, '脚本模版-我的脚本查询', 'scriptService/queryServiceForMySelfWithoutContent.do', '脚本模版-我的脚本查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33160,33024,33160, '脚本模版-我的脚本查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33161, '脚本模版-测试历史查询', 'getScriptCoatList.do', '脚本模版-测试历史查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33161, 33025,33161, '脚本模版-测试历史查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33162, '脚本模版-脚本类别维护查询', 'bsManager/queryBsModel.do', '脚本模版-脚本类别维护查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33162, 33026,33162, '脚本模版-脚本类别维护查询');
 

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33163, '脚本模版-共享脚本查询', 'scriptService/queryServiceForShare.do', '脚本模版-共享脚本查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33163, 33027,33163, '脚本模版-共享脚本查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33164, '流水线统计报表查询', 'queryCICD_PipeLineReport.do', '流水线统计报表查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33164, 33030,33164, '流水线统计报表查询');
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33165, '部署统计报表查询', 'queryCICD_DeployReport.do', '部署统计报表查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33165, 33029,33165, '部署统计报表查询');
 

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33166, '菜单管理查询', 'queryMenuMessage.do', '菜单管理查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33166, 25,33166, '菜单管理查询');


--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33169, '业务系统管理查询', 'businessSystemList.do', '业务系统管理查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33169, 33033,33169, '业务系统管理查询');
 

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33170, '在线开关管理查询', 'getEntegorConfigList.do', '在线开关管理查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33170, 33039,33170, '在线开关管理查询');

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33171, 'Agent管理查询', 'getAgentMaintainList.do', 'Agent管理查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33171, 33037,33171, 'Agent管理查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33172, '操作日志查询', 'pagelist.do', '操作日志查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33172, 33034,33172, '操作日志查询');

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33173, '服务信息配置查询', 'getallFtpInfo.do', '服务信息配置查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33173, 33035,33173, '服务信息配置查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33174, '生产IP管理查询', 'queryCI_PRODUCEAGENTMGR.do', '生产IP管理查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33174, 33023,33174, '生产IP管理查询');

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33175, '在线用户查询', 'getOnlineUsers.do', '在线用户查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33175, 33038,33175, '在线用户查询');

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33176, '用户组查询', 'getUserGroupList.do', '用户组管理-用户组查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33176,33032,33176, '用户组查询');

--INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33177, '用户信息查询', 'getUserGroupInfoList.do', '用户组管理-用户信息查询');  
--INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33177,33032,33177, '用户信息查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33178, '操作日志导出', 'exportAudit.do', '操作日志导出');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33178,33034,33178, '操作日志导出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33179, '查询', 'getAuditDataDictionaryList.do', '临时日志字典-查询');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33179,33040,33179, '查询');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33180, '保存', 'addAuditDataDictionaryList.do', '临时日志字典-保存');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33180,33040,33180, '保存');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33181, '初始化', 'cshAuditDataDictionaryList.do', '临时日志字典-初始化');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33181,33040,33181, '初始化');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33182, '删除', 'delAuditDataDictionaryList.do', '临时日志字典-删除');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33182,33040,33182, '删除');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33183, '导出', 'outputAuditDataDictionaryExcel.do', '临时日志字典-导出');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33183,33040,33183, '导出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33184, '导入', 'importAuditDataDictionaryExcel.do', '临时日志字典-导入');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33184,33040,33184, '导入');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33185, '查询', 'queryConDelTaskMon.do', '任务监控-查询ci侧');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33185, 33013,33185, '查询ci侧');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33186, '检出', 'itemDetectionCheck.do', '配置项列表-检出（生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33186, 33021,33186, '配置项列表-检出');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33187, '作废', 'itemDetectionCheckdeclare.do', '配置项列表-作废（生产）');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33187, 33021,33187, '配置项列表-作废');
 
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33188, '保存', 'insertOrUpdateProxyData.do', 'proxy代理管理-保存按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33188,33041,33188, '保存按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33189, '删除', 'deleteProxyData.do', 'proxy代理管理-删除按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33189,33041,33189, '删除按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33190, '绑定', 'insertOrUpdateProxyBindData.do', 'proxy代理管理-绑定按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33190,33041, 33190, '绑定按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33191, '解绑', 'deleteProxyBindData.do', 'proxy代理管理-解绑按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33191,33041, 33191, '解绑按钮');

INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33192, '查询', 'scriptService/queryAllScripts.do', '全部脚本-查询按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33192, 33028, 33192, '查询按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33193, '导出', 'scriptService/exportAllScripts.do', '全部脚本-导出按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33193, 33028, 33193, '导出按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33194, '查询', 'selectProxyData.do', 'proxy代理管理-查询按钮');  
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33194,33041, 33194, '查询按钮');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33195, 'docker镜像查询', 'ci/getDockerList.do', 'docker镜像查询');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33195, 33020,33195, '构建服务器管理-docker镜像查询');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33196, '上传镜像', 'ci/uploadDockerImagPkg.do', '上传镜像');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33196, 33020,33196, '构建服务器管理-上传镜像');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33197, '删除镜像', 'ci/deleteDocker.do', '删除镜像');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33197, 33020,33197, '构建服务器管理-删除镜像');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33198, '上传镜像历史', 'ci/getRepositoryHisList.do', '上传镜像历史');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33198, 33020,33198, '构建服务器管理-docker镜像-上传镜像历史');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33199, '上传镜像历史详情', 'ci/getUploadImgDetailFinish.do', '上传镜像历史详情');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33199, 33020,33199, '构建服务器管理-docker镜像-上传镜像历史详情');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33200, '修改标签确认', 'ci/updateDockerTag.do', '修改标签确认');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33200, 33020,33200, '构建服务器管理-docker镜像-修改标签确认');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33201, '容器查询', 'ci/getContainerList.do', '容器查询');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33201, 33020,33201, '构建服务器管理-容器查询');
	
	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33206, '容器-启动', 'ci/reqStartContainer.do', '容器-启动');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33206, 33020,33206, '构建服务器管理-容器-启动');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33202, '容器-重启', 'ci/reqRestartContainer.do', '容器-重启');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33202, 33020,33202, '构建服务器管理-容器-重启');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33203, '容器-停止', 'ci/reqStopContainer.do', '容器-停止');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33203, 33020,33203, '构建服务器管理-容器-停止');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33204, '容器-强制停止', 'ci/reqKillContainer.do', '容器-强制停止');  
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33204, 33020,33204, '构建服务器管理-容器-强制停止');

	INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(33205, '容器-删除', 'ci/reqDelContainer.do', '容器-删除'); 
INSERT INTO IEAI_HIGHOPER(IBUTTONID,IBUTTONNAME,IBUTTONURL,IBUTTONDES) VALUES (33320,'删除','deleteDangerCmd.do','脚本模版—关键命令—删除按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID,IBUTTONNAME,IBUTTONURL,IBUTTONDES) VALUES (33321,'保存','saveDangerCmd.do','脚本模版—关键命令—保存按钮');	
INSERT INTO IEAI_HIGHOPER(IBUTTONID,IBUTTONNAME,IBUTTONURL,IBUTTONDES) VALUES (33323,'删除','deletePluginInfo.do','插件中心-删除按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID,IBUTTONNAME,IBUTTONURL,IBUTTONDES) VALUES (33324,'下载插件','exportPlugJson.do','插件中心-下载插件按钮');
INSERT INTO IEAI_HIGHOPER(IBUTTONID,IBUTTONNAME,IBUTTONURL,IBUTTONDES) VALUES (33325,'上传插件','savePlugJson.do','插件中心-上传插件按钮');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID,IMENUID,IBUTTONID,IDES) VALUES (33320,33042,33320,'脚本模版—关键命令—删除按钮');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID,IMENUID,IBUTTONID,IDES) VALUES (33321,33042,33321,'脚本模版—关键命令—保存按钮');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID,IMENUID,IBUTTONID,IDES) VALUES (33323,33043,33323,'插件中心-删除按钮');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID,IMENUID,IBUTTONID,IDES) VALUES (33324,33043,33324,'插件中心-下载插件按钮');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID,IMENUID,IBUTTONID,IDES) VALUES (33325,33043,33325,'插件中心-上传插件按钮');
	INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(33205, 33020,33205, '构建服务器管理-容器-删除');
