/** Excel上传监控显示Excel名称* */
Ext.onReady(function() {
	destroyRubbish();
	var store = Ext.create('Ext.data.Store',{
		fields : [ 'excelName', 'state'],
		autoLoad : true,
		proxy : {
			type : 'ajax',
			timeout :  1200000,
			url : 'getExcelList.do',
			reader : {
				type : 'json',
				root : 'dataList' 
			}
		}
	});
	store.on('beforeload', function(store, options) {
		var new_params = {
			zipName : zipName 
		};
		Ext.apply(store.proxy.extraParams, new_params);
	});

	var colModel = [ {
		text : 'Excel名称',
		dataIndex : 'excelName',
		width : 200
	}, {
		text : '状态',
		dataIndex : 'state',
		width : 100
	}];

	var gridPanel = Ext.create('Ext.grid.Panel', {
		forceFit : true,
		columnLines : false,
		store : store,
		width : '100%',
		height : 600,
		columns : colModel,
		autoScroll : true,
		selType : 'cellmodel',
		viewConfig :
		{
			selType : 'cellmodel',
			loadMask : false,
			stripeRows : true,// 在表格中显示斑马线
			enableTextSelection : true
		}
	});
			// 列表外panel
			var	middleePanel = Ext.create ('Ext.panel.Panel',
			{
				title : 'Excel名称列表',
				region : 'center',
				border : true,
				witdh : '100%',
				height : 600,
				split : true,
				items : [
				gridPanel
				]
			});
			//store.load({params:{limit:pagelimit,start:0}});
			var	activtySourseTopPanel = Ext.create('Ext.panel.Panel', {
				renderTo : "showExcelNameDiv",
				width : '100%',
				layout : 'border',
				header : false,
				border : false,
				height : 600,
				items : [ middleePanel]
			});
			/* 解决IE下trim问题 */
			String.prototype.trim = function() {
				return this.replace(/(^\s*)|(\s*$)/g, "");
			};

			contentPanel.getLoader().on("beforeload", function(obj, options, eOpts) {
				Ext.destroy(activtySourseTopPanel);
				if (Ext.isIE) {
					CollectGarbage();
				}
			});
		});
