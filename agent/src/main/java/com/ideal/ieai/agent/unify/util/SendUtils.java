package com.ideal.ieai.agent.unify.util;

import com.alibaba.fastjson.JSONObject;
import com.ideal.dubbo.interfaces.IScriptInstance;
import com.ideal.ieai.agent.AgentServiceManager;
import com.ideal.ieai.agent.IEAIRemoteAgent;
import com.ideal.ieai.agent.healthcheck.manager.TaskManager;
import com.ideal.ieai.agent.healthcheck.threads.ExecResultBean;
import com.ideal.ieai.agent.proxyapi.ProxyWSClient;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.core.activity.ActStateData;
import com.ideal.ieai.core.io.MarshallingException;
import com.ideal.ieai.proxy.ProxyModel;
import com.ideal.util.uuid.UUID;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.rmi.RemoteException;
import java.util.Hashtable;
import java.util.Map;

public class SendUtils
{
    private static final Logger log = LoggerFactory.getLogger(SendUtils.class);
    //发送proxy配置
    public static String SEND_PROXY ="true";
    public static String PROXY_TOPIC ="ideal-topic";
    private SendUtils() {

    }

    public static void sendToProxyOrServer(String reqid, Hashtable table,String instid) throws Exception {
        Map<String,String> sMap =  getConsoleServer(instid);
        String server = sMap.get("consoleServer");
        String proxy = sMap.get("iproxy");

        if(StringUtils.isNotBlank(proxy)) {
            sendToProxy(reqid, proxy, table);
        }else {
            sendToServer(reqid, server, table);
        }
    }
    public static boolean sendToKafka ( String msg,String topic,ExecResultBean bean )
    {
        boolean flag = true;
        if(StringUtils.isBlank(msg)) {
            return flag;
        }
        try
        {
            KafkaProducer producer = IEAIRemoteAgent.getInstance().getKafkaProducerMap().get(bean.getInstid());
            if(null!=producer) {
//                boolean sendProxy = Boolean.valueOf(KafkaConnectAPI.SEND_PROXY);
                boolean sendProxy = Boolean.valueOf(SEND_PROXY);
                if(sendProxy) {
//                    sendMessageToKafka( producer,msg, KafkaConnectAPI.PROXY_TOPIC,bean.getInstid());
                    sendMessageToKafka( producer,msg, PROXY_TOPIC,bean.getInstid());
                }else {
                    sendMessageToKafka( producer,msg, topic,bean.getInstid());
                }
            }
        } catch (Exception e)
        {
            flag = false;
            log.error("send msg to kafka is error", e);
        }
        return flag;
    }
    public static void sendToProxyOrServerNoCatch(String reqid, Hashtable table,String server,String type) throws Exception {
        if("1".equals(type)) {
            sendToProxyNoCatch(reqid, server, table);
        }else {
            sendToServerNoCatch(reqid, server, table);
        }
    }
    public static void sendToProxyOrServer(String reqid, Hashtable table,String server,String type) throws Exception {
        if("1".equals(type)) {
            sendToProxy(reqid, server, table);
        }else {
            sendToServer(reqid, server, table);
        }
    }
    // 执行发送
    private static void sendMessageToKafka(KafkaProducer producer,String record,String topic,long insId) {

        final ProducerRecord<Integer, String> producerRecord = new ProducerRecord<Integer, String>(topic, null,record);
        final long instid = insId;
        producer.send(producerRecord, new Callback()
        {
            @Override
            public void onCompletion ( RecordMetadata metadata, Exception exception )
            {

                if (exception != null)
                {
                    sendWarnToServer("【"+producerRecord.value() + " 】send failed",instid);
                    log.error(producerRecord.value() + " send failed!",exception);
                }else {
                    log.info(producerRecord.value() + " send success!");
                }
            }
        });
    }
    private static void sendWarnToServer ( String msg,long instid )
    {
        try
        {
            long time = System.currentTimeMillis();
            Hashtable table = new Hashtable();
            table.put("instid", String.valueOf(instid));
            table.put("time", String.valueOf(time));
            table.put("message", msg);
            SendUtils.sendToProxyOrServer("ieai-unify-agent-collectwarn",table,String.valueOf(instid));

        } catch (Exception e)
        {
            log.error("send server request is error", e);

        }
    }
    public static Map<String,String> getConsoleServer(String instid) throws  Exception {
        TaskManager manager = new TaskManager();
        return manager.getServerInfo(Long.valueOf(instid));
    }
    public static boolean sendToServer ( String reqid,String server, Hashtable table)
    {
        boolean send = false;
        ClassPathXmlApplicationContext context = null;
        try
        {
            context = IEAIRemoteAgent.getInstance().getContext();
            if (context.containsBean(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME))
            {
                IScriptInstance instance = (IScriptInstance) context
                        .getBean(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME);
                instance.updateResultService(reqid+UUID.create(), table, 2);
            }
            send = true;
        } catch (Exception e)
        {
            send = false;
            log.error("sendToServer error :" + e.getMessage(), e);
        }
        return send;
    }
    public static boolean sendToProxy ( String reqid,String proxy,Hashtable output)
    {
        boolean send = false;
        JSONObject jsonObject = JSONObject.parseObject(proxy);

        Map<String, Object> proxyMap = (Map<String, Object>)jsonObject;

        String serverHost = (String) proxyMap.get("serverHost");
        int serverPort =  Integer.valueOf(proxyMap.get("serverPort").toString());
        String proxyJson = proxyMap.get("proxyJson").toString();
        int level = Integer.valueOf(proxyMap.get("level").toString());

        ProxyWSClient _client = AgentServiceManager.getInstance().getProxyService(serverHost,serverPort);
        ProxyModel pm = new ProxyModel();
        pm.setProxyJsonData(proxyJson);
        if (level > 0)
        {
            pm.setLevel(--level);
        } else
        {
            pm.setLevel(level);
        }
        String newReq = reqid+UUID.create();

        pm.setReuqestUUID(newReq);
        pm.setStatus(2);
        pm.setOutput(output);
        pm.setExceptionSerialize(null);
        pm.setVersion(2);
        pm.setStateData(new ActStateData());
        pm.setServerIp(serverHost);
        pm.setServerPort(serverPort);
        output.put("removeAgentIp", serverHost);
        output.put("removeAgentPort", serverPort);
        try
        {
            _client.updateRequestStatus(pm, newReq, 2, output, null,
                    2, new ActStateData());
            send = true;
        } catch (Exception e)
        {
            send = false;
            log.error("sendToProxy request is error", e);
        }
        return send;
    }
    public static void sendToServerNoCatch ( String reqid,String server, Hashtable table)
    {
        ClassPathXmlApplicationContext context = null;
        context = IEAIRemoteAgent.getInstance().getContext();
        if (context.containsBean(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME))
        {
            IScriptInstance instance = (IScriptInstance) context
                    .getBean(Constants.SCRIPT_SERVICE_INSTANCE_OBJ_NAME);
            instance.updateResultService(reqid+UUID.create(), table, 2);
        }
    }
    public static void sendToProxyNoCatch ( String reqid,String proxy,Hashtable output) throws RemoteException, MarshallingException
    {
        JSONObject jsonObject = JSONObject.parseObject(proxy);

        Map<String, Object> proxyMap = (Map<String, Object>)jsonObject;

        String serverHost = (String) proxyMap.get("serverHost");
        int serverPort =  Integer.valueOf(proxyMap.get("serverPort").toString());
        String proxyJson = proxyMap.get("proxyJson").toString();
        int level = Integer.valueOf(proxyMap.get("level").toString());

        ProxyWSClient _client = AgentServiceManager.getInstance().getProxyService(serverHost,serverPort);
        ProxyModel pm = new ProxyModel();
        pm.setProxyJsonData(proxyJson);
        if (level > 0)
        {
            pm.setLevel(--level);
        } else
        {
            pm.setLevel(level);
        }
        String newReq = reqid+UUID.create();

        pm.setReuqestUUID(newReq);
        pm.setStatus(2);
        pm.setOutput(output);
        pm.setExceptionSerialize(null);
        pm.setVersion(2);
        pm.setStateData(new ActStateData());
        pm.setServerIp(serverHost);
        pm.setServerPort(serverPort);
        output.put("removeAgentIp", serverHost);
        output.put("removeAgentPort", serverPort);
        _client.updateRequestStatus(pm, newReq, 2, output, null,
                2, new ActStateData());
    }
}
