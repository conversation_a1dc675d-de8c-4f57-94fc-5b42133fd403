INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(100099, '历史记录保留天数', 1, 'activityHisSaveOutput.do', 43, '配置', 'images/info47.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100112, '耗时监控', '1', 'timeConsuming.do', 704, '', 'images/info44.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100113, '统计监控', '1', 'statisticalMonitorIndex.do', 793, '', 'images/info111.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100220, '日志路径配置', '1', 'logManagement.do', 452, '配置', 'images/info27.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(1188, '作业预暂停配置', 1, 'beforehandPause.do', 317, '配置', 'images/info276.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(100110, '提前起批配置', 1, 'earlyStart.do', 450, '配置','images/info27.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(100111, '延迟起批配置', 1, 'delayStart.do', 451, '配置','images/info27.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(21031, '延迟方式配置', 1, 'timeDelayWayConfig.do',316, '配置' ,'images/info38.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100221, '消息补录', '1', 'rocketMqInfo.do', 453, '配置', 'images/info27.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100023, '批量监控看板', '1', 'batchMonitoring.do', 480, '', 'images/info112.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100024, '系统运行监控', '1', 'sbtSystemPage.do', 480, '', 'images/info112.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100114, '数据日期维护', '1', 'dataTimeCheck.do', 705, '', 'images/info44.png');
INSERT INTO IEAI_HIGHOPER(IBUTTONID, IBUTTONNAME, IBUTTONURL, IBUTTONDES) VALUES(11200, '异常暂停', 'flowQuery/errorSkipFlow.do', '作业调度-ODS活动监控-活动名-异常暂停');
INSERT INTO IEAI_MENU_BUTTON(IMENUBUTTONID, IMENUID, IBUTTONID, IDES) VALUES(11200, 1074, 11200, '');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100115, '批量信息入库配置', '1', 'batchCheck.do', 705, '', 'images/info44.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(100116, '批量流程恢复确认', 1, 'batchFlowRecover.do', 2, '','images/info37.png');
INSERT INTO IEAI_MENU(IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG) VALUES(100117, '保存MQ历史天数', 1, 'mqHisSaveOutput.do', 2, '','images/info37.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 100025, '智能日启', '1', 'batchMonitoring.do', 424, '', 'images/info112.png');
INSERT INTO IEAI_MENU (IID, INAME, IGROUPMESSID, IURL, IORDER, IPARENTNAME, IIMG )VALUES( 50075, 'CMDB主机同步', '7', 'cmdbComSync.do', 3, '', 'images/info27.png');