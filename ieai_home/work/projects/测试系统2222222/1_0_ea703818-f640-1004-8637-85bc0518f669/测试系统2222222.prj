<?xml version="1.0" encoding="UTF-8"?>
<Project ID="0" Name="测试系统2222222" NameSpace="" imodelid="1" Validate="false"><Version Major="1" Minor="0" SubMinor="0" /><ProjectType ProType="1.0" /><ProjectFrom ProFrom="1" Isystemtypeuuid="" Dailytype="1" /><ImportPrjs /><EnvVariants><EnvVariant ID="3" Name="DZ" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant><EnvVariant ID="4" Name="GD" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant><EnvVariant ID="5" Name="QL" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant><EnvVariant ID="6" Name="RQ" NameSpace="" imodelid="0" IEAIType="String" IsExport="false"><Value><String><![CDATA[/opt/scripts/sleep10s.sh]]></String></Value><Remark><String><![CDATA[]]></String></Remark></EnvVariant></EnvVariants><Resources><Resource ID="1" Name="AgentResource" NameSpace="" imodelid="0"><ResourceInfo Name="AgentResource" AdaptorName="generalacts" ConfigClassName="" ConfigViewClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResourceView" Description="" IconPath="icon6.gif" ImpClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResource" Tip="Agent resource"><Version Major="1" Minor="0" SubMinor="0" /></ResourceInfo><DefaultConfig>
  <__flowId>0</__flowId>
  <__map>
    <entry>
      <string>isremotegroup</string>
      <boolean>false</boolean>
    </entry>
    <entry>
      <string>hostBakup</string>
      <string />
    </entry>
    <entry>
      <string>isremote</string>
      <boolean>true</boolean>
    </entry>
    <entry>
      <string>port</string>
      <string>15000</string>
    </entry>
    <entry>
      <string>connType</string>
      <int>0</int>
    </entry>
    <entry>
      <string>host</string>
      <string>*************</string>
    </entry>
    <entry>
      <string>portBakup</string>
      <string />
    </entry>
    <entry>
      <string>groupname</string>
      <null />
    </entry>
  </__map>
</DefaultConfig></Resource><Resource ID="93" Name="AgentResource_172" NameSpace="" imodelid="0"><ResourceInfo Name="AgentResource" AdaptorName="generalacts" ConfigClassName="" ConfigViewClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResourceView" Description="" IconPath="icon6.gif" ImpClassName="com.ideal.ieai.adaptors.commadaptors.agentresource.AgentResource" Tip="Agent resource"><Version Major="1" Minor="0" SubMinor="0" /></ResourceInfo><DefaultConfig>
  <__flowId>0</__flowId>
  <__map>
    <entry>
      <string>isremotegroup</string>
      <boolean>false</boolean>
    </entry>
    <entry>
      <string>hostBakup</string>
      <string />
    </entry>
    <entry>
      <string>isremote</string>
      <boolean>true</boolean>
    </entry>
    <entry>
      <string>port</string>
      <string>15000</string>
    </entry>
    <entry>
      <string>connType</string>
      <int>0</int>
    </entry>
    <entry>
      <string>host</string>
      <string>************</string>
    </entry>
    <entry>
      <string>portBakup</string>
      <string />
    </entry>
    <entry>
      <string>groupname</string>
      <null />
    </entry>
  </__map>
</DefaultConfig></Resource></Resources><Functions><InternalFunction ID="91" Name="getdate" NameSpace="测试系统2222222" imodelid="0" ReturnType="String"><SourceCode>var fm=new java.text.SimpleDateFormat("yyyyMMdd");
var systime=fm.format(new java.util.Date);
return systime;</SourceCode></InternalFunction></Functions><IEAISchemaDefs /><Workflows><Workflow ID="2" Name="子作业_A_DZ" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><Description><![CDATA[子作业_A_DZ]]></Description><InputParameterDefs><ParameterDef Name="string" ReadOnly="true" BatchParamter="false" necessary="false" IEAIType="String"><Description>111</Description></ParameterDef></InputParameterDefs><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /><FlowEdgeInfo StartID="1" TargetID="4" LineStyle="-1" /><FlowEdgeInfo StartID="2" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="5" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="5" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1" BorderColor="-ffff01"><Rectangle x="102.0" y="12.0" Width="48.0" Height="37.0" /><StructBound x="102.0" y="12.0" Width="358.0" Height="255.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="412.0" y="225.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="513.0" y="225.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="4"><Rectangle x="133.0" y="222.0" Width="72.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="5"><Rectangle x="230.0" y="19.0" Width="124.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADGElEQVR42u2Va0iTURjHXRPGyIH6pQjMG8amKGpQRPqlGJlkGKYjFDIvJIThJZDEFFMzmnmdWqZ5S2WCSzMZLVOZGhGWW1mpy5y36VTMe5/033tOKZlim/qxB368h3PO8/+f93nfc46Jyf/Yq/Dy8gKHw6GQ9p4Jh4aGgsA0N8Hj8bAr4V8CbPj6XkRISMQmA1NTU5ibm+/YhIrw+Sbw8DgCGxv2JgPrw9bwOedj/JvY29tTgVMnubgRFYDh3ky0PLuA5MQwpn/fBhNbWwFE/iII+ALDTQQCOpmKz45lY05XRFnQl6PjVQba5DfR8vwalM05CPbn4eDBQ3BycjbcgMViUYPi3NMY7U2jJvMTpZgblzDPR9SM9BHIeLDIAr9zDAsOh4WcNFe8bY2iNNQ/RXmlDDO6ekY0CyPaTtxL8UWzQoqu1nDKcTeu4QaRYQfQ330X00MFCL/iT/E7ux997+KwMNUEee15CD14CA9ypEz0p4DkGGxQJBZC25PE1LwKDRVu8BPFoSTbHYvTTdANd6O93hXRkQGIiorBmEbCLCYFJMdog/mJkvWaL+grsDhVC/2IEu2NQkb4/jpfVcnGGaTfvsyI1dOPuDzzAvOTddC8T8KUNgPLy7NoVVRiXJOOeX0Zs5B09HXFguQYtRf02lLGRErRacSMQSI0XxRoa1OiUZYFzQcJJgczMaBOQGfzHeN3c4c8hClLNYWUiPymatVL+ut+7pZQvo8WUJSK1J0dFwOqCFoiwpvSS3iSfAZ1t4SoSRBisP06RjS5qHt8bHcna1dHLgZUsVgYq0J1nh1FXu6IQXUW2mRWKM6/unODsvIa9PbIkRBpgZ7XQRj6JF6nqYqPPnUhHkqijTfw9vaGlZUV7OxsoRufwtA3NZobY1GY5kB5ID6Bvo8y6Cd/wN7Bmc4NDAzc3ohMcHFxgaeH54bTUiKRgMTKCpDn5IB8ZwGWlmZpX35+wYa55LYzMzP72+zolrfVn2B1lQqWuLlT1uJfeUIhs/nIZbF2124Fl8tFTHw8+qVSpFpaUkib9JGx7XLZbDZ+AgW7jJ48xbYHAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>2</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="4" Name="子作业_A_DZ" ParentEntryFlag="1" impor="false" ActID="子作业_A_DZ" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><Description><![CDATA[子作业_A_DZ]]></Description><SucceedingActivity><ID>5</ID><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2">"0".equals($子作业_A_DZ.lastLine)</Item><Item ID="5"></Item></BranchCondition><PreActivities><ID>1</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.command</path><formula><![CDATA[$env.DZ]]></formula></InputExpression></InputExpressions><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
</com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg></AbstractExceptionCfg><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2</__flowId>
  <__map>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityExeTimeConfig>
  <relFlag>0</relFlag>
  <year>-1</year>
  <month>-1</month>
  <day>-1</day>
  <hour>-1</hour>
  <minute>-1</minute>
  <actId>0</actId>
  <validateTimes />
</ActivityExeTimeConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string><RemoteExecution IsRemoteExecute="true" RemoteAgent="AgentResource" AgentConnType="0" /></ActivityElement><ActivityElement ID="5" Name="异常_子作业_A_DZ_UT" ParentEntryFlag="1" impor="false" ActID="异常_子作业_A_DZ_UT" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="4"><Description><![CDATA[异常_子作业_A_DZ_UT]]></Description><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><PreActivities><ID>4</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.properties.错误输出</path><formula><![CDATA[$子作业_A_DZ.stderr]]></formula></InputExpression><InputExpression><path>$ActivityElement.properties.标准输出</path><formula><![CDATA[$子作业_A_DZ.stdout]]></formula></InputExpression><InputExpression><path>$ActivityElement.properties.末行输出</path><formula><![CDATA[$子作业_A_DZ.lastLine]]></formula></InputExpression></InputExpressions><ActivityInfo Name="UserTask" AdaptorName="taskadaptor" IconPath="icon3.gif" ImpClassName="com.ideal.ieai.adaptors.taskadaptor.usertask.UserTaskAct" ConfigViewClassName="com.ideal.ieai.adaptors.taskadaptor.usertask.UserTaskActView" ConfigClassName="" Tip="UserTask act" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2</__flowId>
  <__map>
    <entry>
      <string>warnCodeTxtPane</string>
      <string />
    </entry>
    <entry>
      <string>Data</string>
      <TaskModel serialization="custom">
        <TaskModel>
          <default>
            <__isDefineDelegaterScope>true</__isDefineDelegaterScope>
            <__isDefineFowarderScope>true</__isDefineFowarderScope>
            <__permitFail>false</__permitFail>
            <__permitSkip>false</__permitSkip>
            <__priority>3</__priority>
            <__useJobPart>true</__useJobPart>
            <__userType>1</__userType>
            <__assignedGroup />
            <__assignees />
            <__itemList />
            <__operations>
              <TaskOperationInCore>
                <__id>3</__id>
                <__name>重试</__name>
                <__desc />
                <__returnStateId>complete</__returnStateId>
                <__threshold>1</__threshold>
                <__curPerformCount>0</__curPerformCount>
              </TaskOperationInCore>
              <TaskOperationInCore>
                <__id>4</__id>
                <__name>忽略</__name>
                <__desc />
                <__returnStateId>complete</__returnStateId>
                <__threshold>1</__threshold>
                <__curPerformCount>0</__curPerformCount>
              </TaskOperationInCore>
            </__operations>
            <paramList>
              <ParameterDef>
                <__name>标准输出</__name>
                <__type class="StringType" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
              <ParameterDef>
                <__name>末行输出</__name>
                <__type class="StringType" reference="../../ParameterDef/__type" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
              <ParameterDef>
                <__name>错误输出</__name>
                <__type class="StringType" reference="../../ParameterDef/__type" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
            </paramList>
            <timeoutRules />
          </default>
        </TaskModel>
      </TaskModel>
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>false</string>
    </entry>
    <entry>
      <string>disposeTimeTxtPane</string>
      <string />
    </entry>
    <entry>
      <string>warnInfoTxtPane</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="3" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>2</ID></PreActivities><InputExpressions /></EndActElement><LoopEntryActElement ID="1" Name="循环开始" ParentEntryFlag="-1" impor="false" ActID="循环开始" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" ExitFlag="2" SupportTransactionFlag="false"><SucceedingActivity><ID>4</ID></SucceedingActivity><BranchCondition><Item ID="4"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities></LoopEntryActElement><LoopEXITActElement ID="2" Name="循环结束" ParentEntryFlag="-1" impor="false" ActID="循环结束" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" EntryFlag="1"><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>4</ID><ID>5</ID></PreActivities><IEAISchemaDef ID="-1" Name="schema" NameSpace="ZHQZ_综合前置系统" imodelid="0" /><LoopConditions><LoopCondition>!"0".equals($子作业_A_DZ.lastLine)</LoopCondition><LoopCondition>"重试".equals($异常_子作业_A_DZ_UT.lastOperation)</LoopCondition></LoopConditions></LoopEXITActElement></ActElements></Workflow><Workflow ID="7" Name="子作业_B_GD" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><Description><![CDATA[子作业_B_GD]]></Description><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /><FlowEdgeInfo StartID="1" TargetID="4" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="5" LineStyle="-1" /><FlowEdgeInfo StartID="2" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="5" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="2" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1" BorderColor="-ffff01"><Rectangle x="102.0" y="12.0" Width="48.0" Height="37.0" /><StructBound x="102.0" y="12.0" Width="358.0" Height="255.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="412.0" y="225.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="513.0" y="225.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="4"><Rectangle x="133.0" y="222.0" Width="74.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="5"><Rectangle x="230.0" y="19.0" Width="126.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADGElEQVR42u2Va0iTURjHXRPGyIH6pQjMG8amKGpQRPqlGJlkGKYjFDIvJIThJZDEFFMzmnmdWqZ5S2WCSzMZLVOZGhGWW1mpy5y36VTMe5/033tOKZlim/qxB368h3PO8/+f93nfc46Jyf/Yq/Dy8gKHw6GQ9p4Jh4aGgsA0N8Hj8bAr4V8CbPj6XkRISMQmA1NTU5ibm+/YhIrw+Sbw8DgCGxv2JgPrw9bwOedj/JvY29tTgVMnubgRFYDh3ky0PLuA5MQwpn/fBhNbWwFE/iII+ALDTQQCOpmKz45lY05XRFnQl6PjVQba5DfR8vwalM05CPbn4eDBQ3BycjbcgMViUYPi3NMY7U2jJvMTpZgblzDPR9SM9BHIeLDIAr9zDAsOh4WcNFe8bY2iNNQ/RXmlDDO6ekY0CyPaTtxL8UWzQoqu1nDKcTeu4QaRYQfQ330X00MFCL/iT/E7ux997+KwMNUEee15CD14CA9ypEz0p4DkGGxQJBZC25PE1LwKDRVu8BPFoSTbHYvTTdANd6O93hXRkQGIiorBmEbCLCYFJMdog/mJkvWaL+grsDhVC/2IEu2NQkb4/jpfVcnGGaTfvsyI1dOPuDzzAvOTddC8T8KUNgPLy7NoVVRiXJOOeX0Zs5B09HXFguQYtRf02lLGRErRacSMQSI0XxRoa1OiUZYFzQcJJgczMaBOQGfzHeN3c4c8hClLNYWUiPymatVL+ut+7pZQvo8WUJSK1J0dFwOqCFoiwpvSS3iSfAZ1t4SoSRBisP06RjS5qHt8bHcna1dHLgZUsVgYq0J1nh1FXu6IQXUW2mRWKM6/unODsvIa9PbIkRBpgZ7XQRj6JF6nqYqPPnUhHkqijTfw9vaGlZUV7OxsoRufwtA3NZobY1GY5kB5ID6Bvo8y6Cd/wN7Bmc4NDAzc3ohMcHFxgaeH54bTUiKRgMTKCpDn5IB8ZwGWlmZpX35+wYa55LYzMzP72+zolrfVn2B1lQqWuLlT1uJfeUIhs/nIZbF2124Fl8tFTHw8+qVSpFpaUkib9JGx7XLZbDZ+AgW7jJ48xbYHAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>7</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="4" Name="子作业_B_GD" ParentEntryFlag="1" impor="false" ActID="子作业_B_GD" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><Description><![CDATA[子作业_B_GD]]></Description><SucceedingActivity><ID>5</ID><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2">"0".equals($子作业_B_GD.lastLine)</Item><Item ID="5"></Item></BranchCondition><PreActivities><ID>1</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.command</path><formula><![CDATA[$env.GD]]></formula></InputExpression></InputExpressions><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
</com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg></AbstractExceptionCfg><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2</__flowId>
  <__map>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityExeTimeConfig>
  <relFlag>0</relFlag>
  <year>-1</year>
  <month>-1</month>
  <day>-1</day>
  <hour>-1</hour>
  <minute>-1</minute>
  <actId>0</actId>
  <validateTimes />
</ActivityExeTimeConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string><RemoteExecution IsRemoteExecute="true" RemoteAgent="AgentResource" AgentConnType="0" /></ActivityElement><ActivityElement ID="5" Name="异常_子作业_B_GD_UT" ParentEntryFlag="1" impor="false" ActID="异常_子作业_B_GD_UT" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="4"><Description><![CDATA[异常_子作业_B_GD_UT]]></Description><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><PreActivities><ID>4</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.properties.错误输出</path><formula><![CDATA[$子作业_B_GD.stderr]]></formula></InputExpression><InputExpression><path>$ActivityElement.properties.标准输出</path><formula><![CDATA[$子作业_B_GD.stdout]]></formula></InputExpression><InputExpression><path>$ActivityElement.properties.末行输出</path><formula><![CDATA[$子作业_B_GD.lastLine]]></formula></InputExpression></InputExpressions><ActivityInfo Name="UserTask" AdaptorName="taskadaptor" IconPath="icon3.gif" ImpClassName="com.ideal.ieai.adaptors.taskadaptor.usertask.UserTaskAct" ConfigViewClassName="com.ideal.ieai.adaptors.taskadaptor.usertask.UserTaskActView" ConfigClassName="" Tip="UserTask act" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2</__flowId>
  <__map>
    <entry>
      <string>warnCodeTxtPane</string>
      <string />
    </entry>
    <entry>
      <string>Data</string>
      <TaskModel serialization="custom">
        <TaskModel>
          <default>
            <__isDefineDelegaterScope>true</__isDefineDelegaterScope>
            <__isDefineFowarderScope>true</__isDefineFowarderScope>
            <__permitFail>false</__permitFail>
            <__permitSkip>false</__permitSkip>
            <__priority>3</__priority>
            <__useJobPart>true</__useJobPart>
            <__userType>1</__userType>
            <__assignedGroup />
            <__assignees />
            <__itemList />
            <__operations>
              <TaskOperationInCore>
                <__id>3</__id>
                <__name>重试</__name>
                <__desc />
                <__returnStateId>complete</__returnStateId>
                <__threshold>1</__threshold>
                <__curPerformCount>0</__curPerformCount>
              </TaskOperationInCore>
              <TaskOperationInCore>
                <__id>4</__id>
                <__name>忽略</__name>
                <__desc />
                <__returnStateId>complete</__returnStateId>
                <__threshold>1</__threshold>
                <__curPerformCount>0</__curPerformCount>
              </TaskOperationInCore>
            </__operations>
            <paramList>
              <ParameterDef>
                <__name>标准输出</__name>
                <__type class="StringType" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
              <ParameterDef>
                <__name>末行输出</__name>
                <__type class="StringType" reference="../../ParameterDef/__type" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
              <ParameterDef>
                <__name>错误输出</__name>
                <__type class="StringType" reference="../../ParameterDef/__type" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
            </paramList>
            <timeoutRules />
          </default>
        </TaskModel>
      </TaskModel>
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>false</string>
    </entry>
    <entry>
      <string>disposeTimeTxtPane</string>
      <string />
    </entry>
    <entry>
      <string>warnInfoTxtPane</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="3" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>2</ID></PreActivities><InputExpressions /></EndActElement><LoopEntryActElement ID="1" Name="循环开始" ParentEntryFlag="-1" impor="false" ActID="循环开始" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" ExitFlag="2" SupportTransactionFlag="false"><SucceedingActivity><ID>4</ID></SucceedingActivity><BranchCondition><Item ID="4"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities></LoopEntryActElement><LoopEXITActElement ID="2" Name="循环结束" ParentEntryFlag="-1" impor="false" ActID="循环结束" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" EntryFlag="1"><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>4</ID><ID>5</ID></PreActivities><IEAISchemaDef ID="-1" Name="schema" NameSpace="ZHQZ_综合前置系统" imodelid="0" /><LoopConditions><LoopCondition>!"0".equals($子作业_B_GD.lastLine)</LoopCondition><LoopCondition>"重试".equals($异常_子作业_B_GD_UT.lastOperation)</LoopCondition></LoopConditions></LoopEXITActElement></ActElements></Workflow><Workflow ID="8" Name="子作业_C_QL" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><Description><![CDATA[子作业_C_QL]]></Description><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="0" TargetID="5" LineStyle="-1" /><FlowEdgeInfo StartID="5" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="3" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="1" LineStyle="-1" /><FlowEdgeInfo StartID="2" TargetID="4" LineStyle="-1" /><FlowEdgeInfo StartID="3" TargetID="4" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1"><Rectangle x="522.0" y="233.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="246.0" y="22.0" Width="125.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADGElEQVR42u2Va0iTURjHXRPGyIH6pQjMG8amKGpQRPqlGJlkGKYjFDIvJIThJZDEFFMzmnmdWqZ5S2WCSzMZLVOZGhGWW1mpy5y36VTMe5/033tOKZlim/qxB368h3PO8/+f93nfc46Jyf/Yq/Dy8gKHw6GQ9p4Jh4aGgsA0N8Hj8bAr4V8CbPj6XkRISMQmA1NTU5ibm+/YhIrw+Sbw8DgCGxv2JgPrw9bwOedj/JvY29tTgVMnubgRFYDh3ky0PLuA5MQwpn/fBhNbWwFE/iII+ALDTQQCOpmKz45lY05XRFnQl6PjVQba5DfR8vwalM05CPbn4eDBQ3BycjbcgMViUYPi3NMY7U2jJvMTpZgblzDPR9SM9BHIeLDIAr9zDAsOh4WcNFe8bY2iNNQ/RXmlDDO6ekY0CyPaTtxL8UWzQoqu1nDKcTeu4QaRYQfQ330X00MFCL/iT/E7ux997+KwMNUEee15CD14CA9ypEz0p4DkGGxQJBZC25PE1LwKDRVu8BPFoSTbHYvTTdANd6O93hXRkQGIiorBmEbCLCYFJMdog/mJkvWaL+grsDhVC/2IEu2NQkb4/jpfVcnGGaTfvsyI1dOPuDzzAvOTddC8T8KUNgPLy7NoVVRiXJOOeX0Zs5B09HXFguQYtRf02lLGRErRacSMQSI0XxRoa1OiUZYFzQcJJgczMaBOQGfzHeN3c4c8hClLNYWUiPymatVL+ut+7pZQvo8WUJSK1J0dFwOqCFoiwpvSS3iSfAZ1t4SoSRBisP06RjS5qHt8bHcna1dHLgZUsVgYq0J1nh1FXu6IQXUW2mRWKM6/unODsvIa9PbIkRBpgZ7XQRj6JF6nqYqPPnUhHkqijTfw9vaGlZUV7OxsoRufwtA3NZobY1GY5kB5ID6Bvo8y6Cd/wN7Bmc4NDAzc3ohMcHFxgaeH54bTUiKRgMTKCpDn5IB8ZwGWlmZpX35+wYa55LYzMzP72+zolrfVn2B1lQqWuLlT1uJfeUIhs/nIZbF2124Fl8tFTHw8+qVSpFpaUkib9JGx7XLZbDZ+AgW7jJ48xbYHAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="149.0" y="225.0" Width="73.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="4"><Rectangle x="428.0" y="228.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="5" BorderColor="-ffff01"><Rectangle x="118.0" y="15.0" Width="48.0" Height="37.0" /><StructBound x="118.0" y="15.0" Width="358.0" Height="255.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="2" Name="异常_子作业_C_QL_UT" ParentEntryFlag="5" impor="false" ActID="异常_子作业_C_QL_UT" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="4"><Description><![CDATA[异常_子作业_C_QL_UT]]></Description><SucceedingActivity><ID>4</ID></SucceedingActivity><BranchCondition><Item ID="4"></Item></BranchCondition><PreActivities><ID>3</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.properties.错误输出</path><formula><![CDATA[$子作业_C_QL.stderr]]></formula></InputExpression><InputExpression><path>$ActivityElement.properties.标准输出</path><formula><![CDATA[$子作业_C_QL.stdout]]></formula></InputExpression><InputExpression><path>$ActivityElement.properties.末行输出</path><formula><![CDATA[$子作业_C_QL.lastLine]]></formula></InputExpression></InputExpressions><ActivityInfo Name="UserTask" AdaptorName="taskadaptor" IconPath="icon3.gif" ImpClassName="com.ideal.ieai.adaptors.taskadaptor.usertask.UserTaskAct" ConfigViewClassName="com.ideal.ieai.adaptors.taskadaptor.usertask.UserTaskActView" ConfigClassName="" Tip="UserTask act" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>8</__flowId>
  <__map>
    <entry>
      <string>warnCodeTxtPane</string>
      <string />
    </entry>
    <entry>
      <string>Data</string>
      <TaskModel serialization="custom">
        <TaskModel>
          <default>
            <__isDefineDelegaterScope>true</__isDefineDelegaterScope>
            <__isDefineFowarderScope>true</__isDefineFowarderScope>
            <__permitFail>false</__permitFail>
            <__permitSkip>false</__permitSkip>
            <__priority>3</__priority>
            <__useJobPart>true</__useJobPart>
            <__userType>1</__userType>
            <__assignedGroup />
            <__assignees />
            <__itemList />
            <__operations>
              <TaskOperationInCore>
                <__id>3</__id>
                <__name>重试</__name>
                <__desc />
                <__returnStateId>complete</__returnStateId>
                <__threshold>1</__threshold>
                <__curPerformCount>0</__curPerformCount>
              </TaskOperationInCore>
              <TaskOperationInCore>
                <__id>4</__id>
                <__name>忽略</__name>
                <__desc />
                <__returnStateId>complete</__returnStateId>
                <__threshold>1</__threshold>
                <__curPerformCount>0</__curPerformCount>
              </TaskOperationInCore>
            </__operations>
            <paramList>
              <ParameterDef>
                <__name>标准输出</__name>
                <__type class="StringType" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
              <ParameterDef>
                <__name>末行输出</__name>
                <__type class="StringType" reference="../../ParameterDef/__type" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
              <ParameterDef>
                <__name>错误输出</__name>
                <__type class="StringType" reference="../../ParameterDef/__type" />
                <__readOnly>false</__readOnly>
                <__batchParameter>false</__batchParameter>
                <__description />
                <__required>false</__required>
                <__necessary>false</__necessary>
              </ParameterDef>
            </paramList>
            <timeoutRules />
          </default>
        </TaskModel>
      </TaskModel>
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>false</string>
    </entry>
    <entry>
      <string>disposeTimeTxtPane</string>
      <string />
    </entry>
    <entry>
      <string>warnInfoTxtPane</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="3" Name="子作业_C_QL" ParentEntryFlag="5" impor="false" ActID="子作业_C_QL" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><Description><![CDATA[子作业_C_QL]]></Description><SucceedingActivity><ID>4</ID><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="4">"0".equals($子作业_C_QL.lastLine)</Item><Item ID="2"></Item></BranchCondition><PreActivities><ID>5</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.command</path><formula><![CDATA[$env.QL]]></formula></InputExpression></InputExpressions><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
</com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg></AbstractExceptionCfg><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>8</__flowId>
  <__map>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityExeTimeConfig>
  <relFlag>0</relFlag>
  <year>-1</year>
  <month>-1</month>
  <day>-1</day>
  <hour>-1</hour>
  <minute>-1</minute>
  <actId>0</actId>
  <validateTimes />
</ActivityExeTimeConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string><RemoteExecution IsRemoteExecute="true" RemoteAgent="AgentResource" AgentConnType="0" /></ActivityElement><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>5</ID></SucceedingActivity><BranchCondition><Item ID="5"></Item></BranchCondition><DefaultConfig>
  <__flowId>8</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="1" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>4</ID></PreActivities><InputExpressions /></EndActElement><LoopEntryActElement ID="5" Name="循环开始" ParentEntryFlag="-1" impor="false" ActID="循环开始" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" ExitFlag="4" SupportTransactionFlag="false"><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities></LoopEntryActElement><LoopEXITActElement ID="4" Name="循环结束" ParentEntryFlag="-1" impor="false" ActID="循环结束" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" EntryFlag="5"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><PreActivities><ID>3</ID><ID>2</ID></PreActivities><IEAISchemaDef ID="-1" Name="schema" NameSpace="ZHQZ_综合前置系统" imodelid="0" /><LoopConditions><LoopCondition>!"0".equals($子作业_C_QL.lastLine)</LoopCondition><LoopCondition>"重试".equals($异常_子作业_C_QL_UT.lastOperation)</LoopCondition></LoopConditions></LoopEXITActElement></ActElements></Workflow><Workflow ID="10" Name="子作业_D_RQ" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><Description><![CDATA[子作业_D_RQ]]></Description><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="1" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1"><Rectangle x="169.0" y="82.0" Width="89.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="492.0" y="234.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>10</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="1" Name="DDD_D_RQ_SH" ParentEntryFlag="-1" impor="false" ActID="DDD_D_RQ_SH" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><Description><![CDATA[子作业_D_RQ]]></Description><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.command</path><formula><![CDATA[$env.RQ]]></formula></InputExpression></InputExpressions><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
</com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg></AbstractExceptionCfg><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>10</__flowId>
  <__map>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityExeTimeConfig>
  <relFlag>0</relFlag>
  <year>-1</year>
  <month>-1</month>
  <day>-1</day>
  <hour>-1</hour>
  <minute>-1</minute>
  <actId>0</actId>
  <validateTimes />
</ActivityExeTimeConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string><RemoteExecution IsRemoteExecute="true" RemoteAgent="AgentResource" AgentConnType="0" /></ActivityElement><EndActElement ID="3" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>1</ID></PreActivities><InputExpressions /></EndActElement></ActElements></Workflow><Workflow ID="2346" Name="F0010_水费对账" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><Description><![CDATA[F0010_水费对账]]></Description><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="2" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="1" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="46.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1"><Rectangle x="130.0" y="63.0" Width="59.0" Height="46.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACfUlEQVR42q2U20+ScRjH+TO6a+u2P6ILL1r/QF3X5trKtnJzWlYXXZXD0mYeqnkgnTUPxQxFGIgHzAOKIUMDMxWFFxApQE4Bn14wnUriC/lsn737/Z7f+3yf5/kdZLJTrL9aRgaZRCtofWYh2nNZ9n86HuDw+F/rTxeYKjlgf3xc4LDv8FrpVdjKj5AjkMcvXWSr+4AcgRN80oOHHUfIEcjjl3wqjiPVn9cYucBJAaQgSaBYCt6LfG2TnbVlN1Z/vrhMa3smKG/Vc/lhJ5cq2ih9OUhN7yQ5AnNXRa5JFxi3rFE/YEbeN8nEspPdZIrI7xRWp48m9RzP+qdQqRVo6y/u3drvNVkkt6qme5wB0wahRJIkaYYtKzi8ATKWFBl3eKhTmXn/+gb4NH8Zzn5PDV7bZ6RWzNwbjrPo2sEXinKnRUWTxkRIrCSYTmPe9NNmsFLdodurIOrMIqlFFe2jGGybhMVMt8W2+CJxyprVPB+YwStW5E+LiYrzFiHAk55pCn6WrzzqYtUfQYiDJwEuUeDmKxUvVLO4Yinc4nzG540mqGw3FH4sS6oULAlB9EtuZjYC2P1RylrUVCp0OHbi2HdizK7/5ItD4N4bbeECpQ1DaBadLLhCmEVsngifF9a5XqekcfgrNm8Eq2cX44qP+51jhQs8/WBErpwWW5PE4gmj/yawGQWNbYtbzSqU86tsJ1L0TNl5IG5yUbez+t0IumUBIZbGHohh246yFk5j/OHD5PSz5AnSqF5ALl7EogQ+GW3IB610jC1jcQewun8hRFOsB+N8NK3SqLHSa7T//7tT0aqjsmuCu2+13G4eokoxyuNOAw2q+bN/1M7C/gAtlxZ1rNXe3AAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="292.0" y="144.0" Width="90.0" Height="46.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="521.0" y="229.0" Width="48.0" Height="38.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>2346</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="1" Name="Act Monitor" ParentEntryFlag="-1" impor="false" ActID="Act_Monitor" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities><ActivityInfo Name="Act Monitor" AdaptorName="generalacts" IconPath="icon3.gif" ImpClassName="com.ideal.ieai.adaptors.commadaptors.actmonitor.MonitorAct" ConfigViewClassName="com.ideal.ieai.adaptors.commadaptors.actmonitor.MonitorActView" ConfigClassName="" Tip="Activity monitor" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2346</__flowId>
  <__map>
    <entry>
      <string>hour</string>
      <string>0</string>
    </entry>
    <entry>
      <string>monitorActs</string>
      <list>
        <MonitorActModel>
          <isActFromCur>false</isActFromCur>
          <prjName>测试系统111111</prjName>
          <flwName>F0010_综合对账</flwName>
          <actName>F0010_综合对账</actName>
          <actNameId>测试系统111111_F0010_综合对账_F0010_综合对账</actNameId>
          <actId>2</actId>
          <isTaskAct>true</isTaskAct>
          <flwInsType>0</flwInsType>
          <insSpeName />
          <isInstSepNameExpress>false</isInstSepNameExpress>
          <insSpeFlwId>0</insSpeFlwId>
          <state>Finished</state>
          <rules />
        </MonitorActModel>
      </list>
    </entry>
    <entry>
      <string>minute</string>
      <string>0</string>
    </entry>
    <entry>
      <string>second</string>
      <string>0</string>
    </entry>
  </__map>
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="2" Name="F0010_水费对账" ParentEntryFlag="-1" impor="false" ActID="F0010_水费对账" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>1</ID></PreActivities><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>2346</__flowId>
  <__map>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="3" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>2</ID></PreActivities><InputExpressions /></EndActElement></ActElements></Workflow><Workflow ID="11" Name="主流程_ZJYW_中间业务系统" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /><FlowEdgeInfo StartID="1" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="2" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="5" LineStyle="-1" /><FlowEdgeInfo StartID="3" TargetID="4" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1"><Rectangle x="85.0" y="41.0" Width="102.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA80lEQVR42mNgQALtBQ7/j87x+s+ABTzYGPwfJA/D2NSADfh2tRdsCEwhiA3CP1/s/f/7w9X/fz7e/P/r+QGshoAFv15u+f/jwXrC+OFaFEP+wwz4sNf1/6ejyQTx16udOAzY5/3/0/FUTE1HEv5/OhwPoY8m/v+w3xdiwAxTiGaYAR8PRhKN0Q0AByIxzofgVIQBIDzPT/l/T7nP/y9ni4nCn08XohoAdQkDyJDvt+f//35nIRwjRysIn10EtOh8LcQAkM3YDIEphomhgx2TPBCxgGwAzDswhdNMGHAaAgforsCFaW8IIYMYyAEUaYYZgE8eAESMrZAQlSCmAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="217.0" y="87.0" Width="104.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA80lEQVR42mNgQALtBQ7/j87x+s+ABTzYGPwfJA/D2NSADfh2tRdsCEwhiA3CP1/s/f/7w9X/fz7e/P/r+QGshoAFv15u+f/jwXrC+OFaFEP+wwz4sNf1/6ejyQTx16udOAzY5/3/0/FUTE1HEv5/OhwPoY8m/v+w3xdiwAxTiGaYAR8PRhKN0Q0AByIxzofgVIQBIDzPT/l/T7nP/y9ni4nCn08XohoAdQkDyJDvt+f//35nIRwjRysIn10EtOh8LcQAkM3YDIEphomhgx2TPBCxgGwAzDswhdNMGHAaAgforsCFaW8IIYMYyAEUaYYZgE8eAESMrZAQlSCmAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="356.0" y="132.0" Width="103.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA80lEQVR42mNgQALtBQ7/j87x+s+ABTzYGPwfJA/D2NSADfh2tRdsCEwhiA3CP1/s/f/7w9X/fz7e/P/r+QGshoAFv15u+f/jwXrC+OFaFEP+wwz4sNf1/6ejyQTx16udOAzY5/3/0/FUTE1HEv5/OhwPoY8m/v+w3xdiwAxTiGaYAR8PRhKN0Q0AByIxzofgVIQBIDzPT/l/T7nP/y9ni4nCn08XohoAdQkDyJDvt+f//35nIRwjRysIn10EtOh8LcQAkM3YDIEphomhgx2TPBCxgGwAzDswhdNMGHAaAgforsCFaW8IIYMYyAEUaYYZgE8eAESMrZAQlSCmAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="4"><Rectangle x="498.0" y="173.0" Width="105.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA80lEQVR42mNgQALtBQ7/j87x+s+ABTzYGPwfJA/D2NSADfh2tRdsCEwhiA3CP1/s/f/7w9X/fz7e/P/r+QGshoAFv15u+f/jwXrC+OFaFEP+wwz4sNf1/6ejyQTx16udOAzY5/3/0/FUTE1HEv5/OhwPoY8m/v+w3xdiwAxTiGaYAR8PRhKN0Q0AByIxzofgVIQBIDzPT/l/T7nP/y9ni4nCn08XohoAdQkDyJDvt+f//35nIRwjRysIn10EtOh8LcQAkM3YDIEphomhgx2TPBCxgGwAzDswhdNMGHAaAgforsCFaW8IIYMYyAEUaYYZgE8eAESMrZAQlSCmAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="5"><Rectangle x="648.0" y="205.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>11</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="5" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>4</ID></PreActivities><InputExpressions /></EndActElement><CallWorkflowActElement ID="1" Name="调用_子作业_A_DZ" ParentEntryFlag="-1" impor="false" ActID="调用_子作业_A_DZ" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" CallFlowInstanceName="调用_子作业_A_DZ" CallProjectName="测试系统2222222" CallWorkFlowName="子作业_A_DZ" CallIsSync="true" IsSafeFirst="true"><Description><![CDATA[调用_子作业_A_DZ]]></Description><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions></CallWorkflowActElement><CallWorkflowActElement ID="2" Name="调用_子作业_B_GD" ParentEntryFlag="-1" impor="false" ActID="调用_子作业_B_GD" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" CallFlowInstanceName="调用_子作业_B_GD" CallProjectName="测试系统2222222" CallWorkFlowName="子作业_B_GD" CallIsSync="true" IsSafeFirst="true"><Description><![CDATA[调用_子作业_B_GD]]></Description><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>1</ID></PreActivities><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions></CallWorkflowActElement><CallWorkflowActElement ID="3" Name="调用_子作业_C_QL" ParentEntryFlag="-1" impor="false" ActID="调用_子作业_C_QL" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" CallFlowInstanceName="调用_子作业_C_QL" CallProjectName="测试系统2222222" CallWorkFlowName="子作业_C_QL" CallIsSync="true" IsSafeFirst="true"><Description><![CDATA[调用_子作业_C_QL]]></Description><SucceedingActivity><ID>4</ID></SucceedingActivity><BranchCondition><Item ID="4"></Item></BranchCondition><PreActivities><ID>2</ID></PreActivities><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions></CallWorkflowActElement><CallWorkflowActElement ID="4" Name="调用_子作业_D_RQ" ParentEntryFlag="-1" impor="false" ActID="调用_子作业_D_RQ" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" CallFlowInstanceName="调用_子作业_D_RQ" CallProjectName="测试系统2222222" CallWorkFlowName="子作业_D_RQ" CallIsSync="true" IsSafeFirst="true"><Description><![CDATA[调用_子作业_D_RQ]]></Description><SucceedingActivity><ID>5</ID></SucceedingActivity><BranchCondition><Item ID="5"></Item></BranchCondition><PreActivities><ID>3</ID></PreActivities><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions></CallWorkflowActElement></ActElements></Workflow><Workflow ID="12" Name="日启动_ZJYW_中间业务系统" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="1" TargetID="3" LineStyle="-1" /><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /><FlowEdgeInfo StartID="2" TargetID="5" LineStyle="-1" /><FlowEdgeInfo StartID="4" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="3" TargetID="4" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="48.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1" BorderColor="-ffff01"><Rectangle x="132.0" y="31.0" Width="48.0" Height="37.0" /><StructBound x="132.0" y="31.0" Width="348.0" Height="198.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="432.0" y="192.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABIElEQVR42o2TzUoCURSAhxg3+QS+gm/QonkJH6IUg5ZtAjcTkiCtxAdwpwu3QhGziKCiojAiIsEgaqFgzUYNv7pncpzs3mYOfMzlnnO++8dYlibcDQsdVlyUCykpZOjBWwuea/BYgrs8XOckd7BtE10kbN7fWmU8egC/axRw7sBJNljkKL0QyMSnHwpmg+OFQLEkwMvoBdOPHpPhhQjUXKVoC5I3CebNUYHuwqQuicDYHHeEOXubKyR91kCw5mJkObR5NdiB9C5kXMhWwaljFFz2p7y8IwQ1GoGpuXM/SSZQO7CdSoiqad7MRNB9RYg9Qq4B+TaUDqF2igi8JzjrjYU/AvX9T9C6hfaVL/w+YvRGf8Ymgcql1svxf2XiZ/2OL3jb++YsN+DKAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="3"><Rectangle x="199.0" y="76.0" Width="86.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABfklEQVR42oWTL0/DUBTF69B8FtQSvgqKMFjAkGaCNDOkQZAJxEwJdhMk4IoDs6QIls5t7k0gKiYQE5WX97tN397oxpqc9L17zzn33fcnCP75wutQgB9brlYCmux2W8DBxbkkSSLx3UAuO5GCMTFyNW9tMhEJjh+cOO5Fkn0aKYpSypVFKTomRs6ZoEEbHCWSj2M5vOpolZ9lJVLhd+HGgBwcuGjQqgFOUTesqnqCfDrbmNergavV0Q57LQlPWpK+5bYCBBGzqBB2bTvj9ZwcHLho0AbZcyRt2xM91kRnYE/AN6gBVzVWG5hpKqdnofZGBR/1CsDTY6ogDhcNWj0IjsosyoZB/35g0VdhbVS1WOrxuivAOedfRvvzMZuLTCaV0I/DReMMXl5TGY7Sxo7vAlw0zsAsColuYuG/T+xzN27y+0dWbYxN7PrIwYG7/fF0qyNlebO50d3mOjMmRg7OVnE2zuzS7MO5jZX89zFpzObgwG0YUI1ksOeDA7ee/wLbj4/X5NVrUgAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="4"><Rectangle x="273.0" y="137.0" Width="182.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA80lEQVR42mNgQALtBQ7/j87x+s+ABTzYGPwfJA/D2NSADfh2tRdsCEwhiA3CP1/s/f/7w9X/fz7e/P/r+QGshoAFv15u+f/jwXrC+OFaFEP+wwz4sNf1/6ejyQTx16udOAzY5/3/0/FUTE1HEv5/OhwPoY8m/v+w3xdiwAxTiGaYAR8PRhKN0Q0AByIxzofgVIQBIDzPT/l/T7nP/y9ni4nCn08XohoAdQkDyJDvt+f//35nIRwjRysIn10EtOh8LcQAkM3YDIEphomhgx2TPBCxgGwAzDswhdNMGHAaAgforsCFaW8IIYMYyAEUaYYZgE8eAESMrZAQlSCmAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="5"><Rectangle x="528.0" y="194.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>12</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="3" Name="延迟到每天12点" ParentEntryFlag="1" impor="false" ActID="延迟到每天12点" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>4</ID></SucceedingActivity><BranchCondition><Item ID="4"></Item></BranchCondition><PreActivities><ID>1</ID></PreActivities><ActivityInfo Name="Delayer" AdaptorName="generalacts" IconPath="icon2.gif" ImpClassName="com.ideal.ieai.adaptors.commadaptors.delayer.DelayerAct" ConfigViewClassName="com.ideal.ieai.adaptors.commadaptors.delayer.DelayerConfigView" ConfigClassName="" Tip="Delayer" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>12</__flowId>
  <__map>
    <entry>
      <string>DelayType</string>
      <string>0</string>
    </entry>
    <entry>
      <string>uploadinview</string>
      <string>uploadinview2</string>
    </entry>
    <entry>
      <string>TimeStr</string>
      <string>*:*:*:12:0:0</string>
    </entry>
    <entry>
      <string>useDyn</string>
      <boolean>false</boolean>
    </entry>
  </__map>
</DefaultConfig><string>5</string><string>1</string></ActivityElement><EndActElement ID="5" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>2</ID></PreActivities><InputExpressions /></EndActElement><CallWorkflowActElement ID="4" Name="调用_主流程_ZJYW_中间业务系统" ParentEntryFlag="1" impor="false" ActID="调用_主流程_ZJYW_中间业务系统" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" CallFlowInstanceName="调用_主流程_ZJYW_中间业务系统" CallProjectName="测试系统2222222" CallWorkFlowName="主流程_ZJYW_中间业务系统" CallIsSync="true" IsSafeFirst="true"><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><PreActivities><ID>3</ID></PreActivities><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions><InputExpressions><InputExpression><path>$CallWorkflowActElement.instanceName</path><formula><![CDATA[getdate()]]></formula></InputExpression></InputExpressions></CallWorkflowActElement><LoopEntryActElement ID="1" Name="循环开始" ParentEntryFlag="-1" impor="false" ActID="循环开始" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" ExitFlag="2" SupportTransactionFlag="false"><SucceedingActivity><ID>3</ID></SucceedingActivity><BranchCondition><Item ID="3"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities></LoopEntryActElement><LoopEXITActElement ID="2" Name="循环结束" ParentEntryFlag="-1" impor="false" ActID="循环结束" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1" EntryFlag="1"><SucceedingActivity><ID>5</ID></SucceedingActivity><BranchCondition><Item ID="5"></Item></BranchCondition><PreActivities><ID>4</ID></PreActivities><LoopConditions /></LoopEXITActElement></ActElements></Workflow><Workflow ID="92" Name="Test_001" NameSpace="测试系统2222222" imodelid="0" Priority="3" StartType="1" UseCalendar="false" CalendarName="" IsCheckFlag="false" IsSafeFirst="true"><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
  <__isUseFlwDefineAssignee>false</__isUseFlwDefineAssignee>
  <__isDefineDelegaterScope>false</__isDefineDelegaterScope>
  <__isDefineFowarderScope>false</__isDefineFowarderScope>
  <__assignees />
  <__assignedGroup />
</com.ideal.ieai.core.exceptionhandler.ManualExceptionCfg></AbstractExceptionCfg><FlowChartInfo><EdgeInfos><FlowEdgeInfo StartID="1" TargetID="2" LineStyle="-1" /><FlowEdgeInfo StartID="0" TargetID="1" LineStyle="-1" /></EdgeInfos><VertexInfos><FlowVertexInfo ID="0"><Rectangle x="0.0" y="0.0" Width="58.0" Height="58.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEF0lEQVR42t2W+1NUZRjHnaFmmprph2b4A5phoixrJuwyk2MN42jFiBK3wwIVIJe4LJCIJVkRCigpI9WYISyVDijmdDG1KJc7xM2gFiIJWSi5CMiysOwCy6fnnGKCFjN/9cw885497/t8v9/n+5z3Pbtq1S19DQ8PU1pWxttvvk6qPoYdqfHk5b7DqfJSWpqbMPf3Y7PNcNPADoeDshMneWWbwhvJfpiM+Uz3nQFLI0xexHG1jbH+ZtqbjVQZK2hqbkHN+V/gdrudvHcPsvvVEIY7isDRBFaJ0W/gj5Mw8KmMx2HoM7h6lms95Rw9lEFmVg5Wq/W/Sebm5jh2vJRCSXCOGWGqWQA/xtmxk+mGKKZqQiV0WGvCsDVGsdCeAj2Z2Ey7SQ5/DL0+WSyzXZ+ks7OLL8uLYUKAx40smN5iulaHxRjAhDEQS2WwFhNaBMl9ENNCSoeQy7qgjR4UFxdzXWsa6qqZHm6FsQssiOpJAVDBJqtDMJ0O0calYakK4ZpRkQjG0RBK1+mtRIT6Mjo66kpiNpvpaKkS9fXw6x6mqiXxb3A1UuLD+L5IACtdScYrFbEuBLri2Lvdl6/OfO1KUFdbzWC3NHLAwGxTpFb+UiBFUVCC/NmXvlmUBriQ2OqEoDMJ0/ldGIoLXQmOHC7A0iVvTVeGpt5SpSwD8fHxwdvbGy8vLzase4CSzPUa8CLBjPQAUyoT7fnsz8l0JYiNDBZrsphvi/2rmVXLrVCBPT09cXd3V5O5847b0D17L52ntjApYuz10uzO16D/I1ISo1wJvNc9LI2Nw94QLo1VXBqqAru5uWngi6GSHEhdK/MKjsZw6M7SCEICnncl2PDM4/z2uZ/WrH+rV2MpsBqP3n8PJ3LWa3NqzkLrNug9xHjLHgK2blzBougIzhd4M/tDGFZJslbrViS4+67bifW/j255JRfn7A3i/8/pssOPUHcsDH1CjCtBScknpEU8IWXGM9/8IjP1umUkKviTa9w11f9YqMNWK+rbYmRH58PvucQHrsZgMLgSjIyMkLEzmcH6XUKyg4WWSG3zqOWrYBG+HvR84besKg1c1vFLtnh/kL5zCjr/TQwNDa28m7+t+I4P9qfh7H1fFO2F9kTmm14SIIUr516QtyVYqgqW40HRyBcuxouYXAHPZ7Ytmpe3PLSy+qXHdOFRA+/lJOLs+1A8PSwA2Zq/zh8TmW2Jw9mWIL/TRHUmXDoA5n3YWyOJC3jwxoedes3MzJCdm0dK1CbMxu1wRbwdKIDLMvYI4KU8GUX1ZSHuTafvbCARfo+QlKS/8XG9tJKi4hLNz4TgNdQYNjN0IVrbqfyUxFBFmPYsIWi1tqaoyIAq7Ka/bIODg0JkQJ8Yi+9zT/PUWg8t1Hv1mTqnrrm1/zj8Cef0+3LahnpoAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo><FlowVertexInfo ID="1"><Rectangle x="147.0" y="101.0" Width="55.0" Height="45.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACVklEQVR42q2W3WoaURSFhVIIhUKbCk3aYKLIhDFWjIhtTdrGapImtZBI/jAtFVq8CYWCFyHJTS+89AV8AF+pj9FX2HUdWWf2HB1rSoSFM+PM9+2zPTNnYrHhp9/vi+d5Mty8k8TjcQET7Fiv15NMNi+dTkcGg8FYcCKDc7vdrlxeXUu73ZbG6bns7H2U4qsNw0ik1+TRYkrmHs7LvftzIwl+xIWT4FECFENBpbYbEjxd8Y3kweMFjigWCdcCwJGr65tA0GjIxta2EST9dSuYX1o1mSrQlevqIbj48VOaX1pSPzwygkyhbATIQjov8WTOJFLgggnX7TlpfpXKhwMpvakawXKmJM+9QihW4FY7DY7qP7e+S71xZvqfLW3JSm40Ag1/5pfHBQTqsC2E2z93WP3ryr4VYAQM4CHBJCjBLhytQe8Bz5dr4hdHAoRgZHm9FggI5BxHACWYbTFwtGZYfWFzx1avs/TinUlIQJgGMqgacLSFcF29HgHhyeKeiRVgyjEEIthH1QfHzRAc1f8Lni4fBgLcMFFBv//8/jWxcoRgFx4SVKtVSb2sG5D+Ztx9RvcbwTHCV98eBwLc6rMKsM39KLj//tzECvCg4oU6rsA9BrDbFsLXtluBIOVlZhpBVKtceHb3m4kVLCZSt24Rj7HfCKpGcvttyX+6CAR4fs/SIldKuK6c8DHBbVuE7Wnw/xK4swhgd9RjAizQWN6eJHy7UPCBpec5Bd7mkQlniu65Dq43LxJ43mCBxiiwGiEa7k5DtmUaHNeBZ98sIMFI7uq1BZUT/hfRFUyvG00+QwAAAABJRU5ErkJggg==]]></icon></FlowVertexInfo><FlowVertexInfo ID="2"><Rectangle x="292.0" y="200.0" Width="48.0" Height="37.0" /><icon><![CDATA[iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAACT0lEQVR42pWT3UtTcRjHvcuLbqKLghndddMfEIQmXqQomJUv21y6HJXibrRI0RrBrBWRqYRSQTdlIeVFoiR0kfNlrrUXZy+6BmaoEUTg1Pm+7dNzzrARE6sDzznn9/s938/zPb/z/FJStrm6bMfYLlL+dm0lTow8gJ+DsOyG8DuYd7EwN7ozSFlwvGqDVZcI+ogF77Dhr2PDV8PmeB0EbfCtg+EXF5MhykTA3SMVnUQnbSyP6Am9OU1ooFiiRH0u2otZcZbDZGWyE2XA0luiH66owqWhEjZGJZxaIi4deAysOw3MD2gJ2bUyLksAlJfQjAOmWwiLUBEHek103a+js/0S965XcPlCNi9bcuIQuwDHjAT7q+IQ5RaZfkzEV6Xa3ZSqj5rNVNdYqDLXkpN3kl2793Mi86AIy1kY1BHzGNn8ZE0AmLrGqsMg9nREXXpuN2rJzS8kKysLjUajWCU/I02tvCiAiNsEs20JQOzjeSHrWRzSg7cMs+EIe/buIzU1VRUrUZB5QAUsDYmDMTN8f5gA/HhdRHhYAZSqSXnpab+FW5F7VKOurTqkSMDCeqA5AfA+LRCqkZWRUqKecuqNh5MAlYWHVHdR7znph3b83aY//wRTDcS8JtZGxcmglmc3MnhiTaezKR4zfaeIuY3w+SbMNCX3ga9fOm32LryvJaok+qVpxs7EY/yszMt3B29Jjo3e1uztu9H+vF42pwPmWiXZKpvbCBMWcSdVZ5vh61V62o7vfB5UN90VrPkbRGCFLxbW3NX4uor+/1T+y3H+Bf+VPJoWDb2rAAAAAElFTkSuQmCC]]></icon></FlowVertexInfo></VertexInfos></FlowChartInfo><ActElements><ActivityElement ID="0" Name="开始活动" ParentEntryFlag="-1" impor="false" ActID="开始活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>1</ID></SucceedingActivity><BranchCondition><Item ID="1"></Item></BranchCondition><DefaultConfig>
  <__flowId>92</__flowId>
  <__map />
</DefaultConfig><string>5</string><string>1</string></ActivityElement><ActivityElement ID="1" Name="ShellCmd" ParentEntryFlag="-1" impor="false" ActID="ShellCmd" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity><ID>2</ID></SucceedingActivity><BranchCondition><Item ID="2"></Item></BranchCondition><PreActivities><ID>0</ID></PreActivities><InputExpressions><InputExpression><path>$ActivityElement.command</path><formula><![CDATA[&quot;sh /root/sleep1.sh&quot;]]></formula></InputExpression></InputExpressions><AbstractExceptionCfg><com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg>
  <__noticeModel>
    <__isSyn>false</__isSyn>
    <__inputParamMap />
    <__isSafeFirst>false</__isSafeFirst>
  </__noticeModel>
  <__isUseFlwDefineNotice>false</__isUseFlwDefineNotice>
  <__isUseSuperNotice>false</__isUseSuperNotice>
</com.ideal.ieai.core.exceptionhandler.DefaultExceptionCfg></AbstractExceptionCfg><ActivityInfo Name="ShellCmd" AdaptorName="shellcmd" IconPath="icon5.gif" ImpClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdAct" ConfigViewClassName="com.ideal.ieai.shellcmdadaptor.shellcmd.ShellCmdActView" ConfigClassName="" Tip="Execute shell command" Description=""><Version Major="1" Minor="0" SubMinor="0" /></ActivityInfo><DefaultConfig>
  <__flowId>92</__flowId>
  <__map>
    <entry>
      <string>uploadinview</string>
      <string>uploadinview2</string>
    </entry>
    <entry>
      <string>errorExpectLastline</string>
      <string />
    </entry>
    <entry>
      <string>executeUsername</string>
      <string />
    </entry>
    <entry>
      <string>cbxYorNWarn</string>
      <string>true</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
    <entry>
      <string>startIn</string>
      <string />
    </entry>
    <entry>
      <string>expectLastline</string>
      <string />
    </entry>
    <entry>
      <string>expecttype</string>
      <string />
    </entry>
    <entry>
      <string>consoleInput</string>
      <list />
    </entry>
    <entry>
      <string>command</string>
      <string />
    </entry>
    <entry>
      <string>timeout</string>
      <string />
    </entry>
  </__map>
</DefaultConfig><ActivityExeTimeConfig>
  <relFlag>0</relFlag>
  <year>-1</year>
  <month>-1</month>
  <day>-1</day>
  <hour>-1</hour>
  <minute>-1</minute>
  <actId>0</actId>
  <validateTimes />
</ActivityExeTimeConfig><ActivityRelyTableConfig>
  <__map>
    <entry>
      <string>pri</string>
      <string>5</string>
    </entry>
    <entry>
      <string>params</string>
      <list />
    </entry>
  </__map>
</ActivityRelyTableConfig><string>5</string><string>1</string><RemoteExecution IsRemoteExecute="true" RemoteAgent="AgentResource_172" AgentConnType="0" /></ActivityElement><EndActElement ID="2" Name="结束活动" ParentEntryFlag="-1" impor="false" ActID="结束活动" Timeout="0" levelOfPRI="5" levelOfWeight="1" AsRecoveryPoint="true" RecoveryMethod="1"><SucceedingActivity /><BranchCondition /><PreActivities><ID>1</ID></PreActivities><InputExpressions /></EndActElement></ActElements></Workflow></Workflows></Project>
