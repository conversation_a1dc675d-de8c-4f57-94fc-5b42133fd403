/*******************************************************************************
 * 流程编排-关机维护
 ******************************************************************************/

Ext.onReady(function () {
    // 清理主面板的各种监听时间
    destroyRubbish();
    Ext.tip.QuickTipManager.init ();
    Ext.QuickTips.init();

    Ext.override(Ext.grid.GridPanel, {
        afterRender : Ext.Function.createSequence(Ext.grid.GridPanel.prototype.afterRender,
            function() {
                // 默认显示提示
                if (!this.cellTip) {
                    return;
                }

                var view = this.getView();

                this.tip = new Ext.ToolTip({
                    target: view.el,
                    delegate : '.x-grid-cell-inner',
                    trackMouse: true,
                    renderTo: document.body,
                    ancor : 'top',
                    style : 'background-color: #FFFFCC;',
                    listeners: {
                        beforeshow: function updateTipBody(tip) {
                            //取cell的值
                            //fireFox  tip.triggerElement.textContent
                            //IE  tip.triggerElement.innerText
                            var tipText = (tip.triggerElement.innerText || tip.triggerElement.textContent);
                            if (Ext.isEmpty(tipText) || Ext.isEmpty(tipText.trim()) ) {
                                return false;
                            }

                            tip.update(tipText);
                        }
                    }
                });
            })
    });


    var loginName = projectList.release_loginName;
    var userName = projectList.current_userName;
    var userid = projectList.userid;

    /** 模板名 下拉选**/
    Ext.define('projectModelName', {
        extend: 'Ext.data.Model',
        fields: [{
            name: 'IPROJECTNAME',
            type: 'string'
        }]
    });

    /** *********************Model********************* */
    /** 排程数据Model* */
    Ext.define('scheduleModel', {
        extend: 'Ext.data.Model',
        fields: [
            {
                name: 'scheduleId',
                type: 'long'
            }, {
                name: 'webStudioId',
                type: 'long'
            }, {
                name: 'flowName',
                type: 'string'
            }, {
                name: 'projectName',
                type: 'string'
            }, {
                name: 'projectId',
                type: 'long'
            }, {
                name: 'creationUser',
                type: 'string'
            }, {
                name: 'creationTime',
                type: 'string'
            }, {
                name: 'execWindow',
                type: 'string'
            },
            {
                name: 'id',
                type: 'string'
            },
            {
                name: 'year',
                type: 'string'
            },
            {
                name: 'state',
                type: 'string'
            },
            {
                name: 'appOfAff',
                type: 'string'
            },
            {
                name: 'serverRunState',
                type: 'string'
            },
            {
                name: 'appPutState',
                type: 'string'
            },
            {
                name: 'opeSysVer',
                type: 'string'
            },
            {
                name: 'hostname',
                type: 'string'
            },
            {
                name: 'ip',
                type: 'string'
            },
            {
                name: 'lastUpTime',
                type: 'string'
            },
            {
                name: 'ciPerson',
                type: 'string'
            },
            {
                name: 'appAdminA',
                type: 'string'
            },
            {
                name: 'groupOfAff',
                type: 'string'
            },
            {
                name: 'opeSysInfo',
                type: 'string'
            },
            {
                name: 'serverUsage',
                type: 'string'
            },
            {
                name: 'serverType',
                type: 'string'
            },
            {
                name: 'dutyGroup',
                type: 'string'
            },
            {
                name: 'dutyPerson',
                type: 'string'
            },
            {
                name: 'framework',
                type: 'string'
            },
            {
                name: 'shutdownAdvice',
                type: 'string'
            },
            {
                name: 'noShutdownCase',
                type: 'string'
            },
            {
                name: 'mastersNeedAtt',
                type: 'string'
            },
            {
                name: 'earlyMainIden',
                type: 'string'
            },
            {
                name: 'earlyYearMemo',
                type: 'string'
            },
            {
                name: 'earlyYearMes',
                type: 'string'
            },
            {
                name: 'earlyExecWindow',
                type: 'string'
            },
            {
                name: 'mainPlanAdjSign',
                type: 'string'
            },
            {
                name: 'adjMainIden',
                type: 'string'
            },
            {
                name: 'adjYearMemo',
                type: 'string'
            },
            {
                name: 'adjExecWindow',
                type: 'string'
            },
            {
                name: 'adjCause',
                type: 'string'
            },
            {
                name: 'windowExecMatter',
                type: 'string'
            },
            {
                name: 'planStartTime',
                type: 'string'
            },
            {
                name: 'planEndTime',
                type: 'string'
            },
            {
                name: 'carryOutSys',
                type: 'string'
            },
            {
                name: 'carryOutDba',
                type: 'string'
            },
            {
                name: 'carryOutApp',
                type: 'string'
            },
            {
                name: 'imOnServiceDuMain',
                type: 'string'
            },
            {
                name: 'genPrePre',
                type: 'string'
            },
            {
                name: 'motesMainExpSum',
                type: 'string'
            },
            {
                name: 'mainComIden',
                type: 'string'
            },
            {
                name: 'reallyStartTime',
                type: 'string'
            },
            {
                name: 'reallyEndTime',
                type: 'string'
            },
            {
                name: 'descOfOtherExce',
                type: 'string'
            },
            {
                name: 'exportFlag',
                type: 'string'
            },
            {
                name: 'syncState',
                type: 'string'
            },
            {
                name: 'scheduleState',
                type: 'long'
            },
            {
                name: 'hostid',
                type: 'string'
            },
            {
                name: 'iid',
                type: 'string'
            }]
    });


    /** 模板列表数据源* */
    var scheduleStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        remoteSort: true,
        model: 'scheduleModel',
        groupField: 'execWindow',
        pageSize: 10,
        proxy: {
            type: 'ajax',
            url: 'shutdown/getScheduleList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });
    scheduleStore.on('beforeload', function (store, options) {
        var new_params = {
            framework: frameworkManage.getValue(),
            app: appTextField.getValue(),
            hostName: hostnameTextField.getValue(),
            createBeginTime: createBeginTime.getValue(),
            createEndTime: createEndTime.getValue()
        };
        Ext.apply(scheduleStore.proxy.extraParams, new_params);
    });
    /** *********************组件********************* */


    /** 查询模板信息* */
    var queryButtonForPRJ = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '查询',
        handler: queryWhere
    });

    var resetBtn = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '清空',
        handler: reset
    });

    var deleteBtn = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '删除',
        handler: deleteInfo
    });

    var batchTaskBtn = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        text: '批量发起',
        handler: batchInitiatorTask
    });

    /** 架构类型下拉选--Store */
    var frameworkStore = Ext.create('Ext.data.Store', {
        autoLoad: true,
        autoDestroy: true,
        proxy: {
            type: 'ajax',
            url: 'getShutDownPlanFrameworkData.do',
            actionMethods: 'post',
            reader: {
                type: 'json',
                root: 'dataList'
            }
        },
        fields: ['framework', 'framework']
    });


    /** 架构类型下拉选--Combo */
    var frameworkManage = Ext.create('Ext.ux.ideal.form.ComboBox', {
        name: 'frameworkManage',
        fieldLabel: '架构类型',
        width: contentPanel.getWidth() / 7,
        labelAlign: 'right',
        labelWidth: 65,
        store: frameworkStore,
        displayField: 'framework',
        valueField: 'framework',
        triggerAction: 'all',
        emptyText: '--请选择架构类型--',
        allowBlank: true,
        enableKeyEvents: true,
        mode: 'local',
        listeners: {
            specialkey: function (field, e) {
                if (e.getKey() == Ext.EventObject.ENTER) {
                    grid_panel.ipage.moveFirst();
                }
            },
            select: function (combo, records, options) {
                grid_panel.ipage.moveFirst();
            },
        }

    });

    var appTextField = Ext.create('Ext.form.TextField', {
        fieldLabel: '所属应用',
        emptyText: '--请输入所属应用--',
        value: '',
        xtype: 'textfield',
        width: contentPanel.getWidth() / 7,
        labelAlign: 'right',
        labelWidth: 65
    });

    var hostnameTextField = Ext.create('Ext.form.TextField', {
        fieldLabel: '主机名',
        emptyText: '--请输入主机名--',
        value: '',
        width: contentPanel.getWidth() / 7,
        labelAlign: 'right',
        labelWidth: 65,
        xtype: 'textfield'
    });

    var createBeginTime = Ext.create('Go.form.field.DateTime',
        {
            fieldLabel: '创建时间',
            labelAlign: 'right',
            name: 'createBeginTime',
            format: 'Y-m-d H:i:s',
            width: contentPanel.getWidth() / 7,
            labelWidth: 65,
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == Ext.EventObject.ENTER) {
                        grid_panel.ipage.moveFirst();
                    }
                },
                select: function (combo, records, options) {
                    grid_panel.ipage.moveFirst();
                },
            }
        });
    var createEndTime = Ext.create('Go.form.field.DateTime',
        {
            name: 'createEndTime',
            emptyText: '',
            width: contentPanel.getWidth() / 8,
            labelAlign: 'right',
            labelWidth: 30,
            format: 'Y-m-d H:i:s',
            fieldLabel: '至:',
            labelSeparator: '',
            listeners: {
                specialkey: function (field, e) {
                    if (e.getKey() == Ext.EventObject.ENTER) {
                        grid_panel.ipage.moveFirst();
                    }
                },
                select: function (combo, records, options) {
                    grid_panel.ipage.moveFirst();
                },
            }
        });

    var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
        clicksToEdit: 2
    });
    //form 按钮的点击事件
    $(document).ready(function () {
        $("#itsmform").click(function () {
            Test.test();
        })
    });


    /** 报表列表Columns* */
    var gridColumns = [
        {
            text: '序号',
            width: 40,
            align: 'left',
            xtype: 'rownumberer'
        },
        {
            text: 'iid',
            dataIndex: 'iid',
            hidden: true,
            width: 120
        },
        {
            text: '主键',
            dataIndex: 'scheduleId',
            hidden: true,
            width: 120
        }, {
            text: '模板主键',
            dataIndex: 'webStudioId',
            hidden: true,
            width: 120
        }, {
            text: 'hostid',
            dataIndex: 'hostid',
            hidden: true,
            width: 120
        },
        {
            text: '年度',
            dataIndex: 'year',
            width: 50
        }, {
            text: '所属应用',
            width: 150,
            dataIndex: 'appOfAff',
        }, {
            text: '主机名',
            dataIndex: 'hostname',
            width: 120
        }, {
            text: '应用架构',
            dataIndex: 'framework',
            width: 100
        }, {
            text: '模板名称',
            dataIndex: 'projectName',
            width: 100
        }, {
            text: '流程名称',
            dataIndex: 'flowName',
            hidden: true,
            width: 100
        }, {
            text: '创建用户',
            dataIndex: 'creationUser',
            width: 120
        }, {
            text: '创建时间',
            dataIndex: 'creationTime',
            hidden: true,
            width: 170
        },
        {
            text: '主机id',
            dataIndex: 'id',
            hidden: true,
            width: 70
        }, {
            text: '备份ip',
            dataIndex: 'ip',
            hidden: true,
            width: 100
        }, {
            text: '状态',
            dataIndex: 'state',
            width: 60
        }, {
            text: '服务器CI责任人',
            dataIndex: 'ciPerson',
            hidden: true,
        }, {
            text: '应用管理员A角',
            dataIndex: 'appAdminA',
            hidden: true,
        }, {
            text: '实施人员(系统管理员)',
            dataIndex: 'carryOutSys',
            width: 120,
            editor: {
                allowBlank: false
            }
        }, {
            text: '实施人员(DBA)',
            width: 120,
            dataIndex: 'carryOutDba',
            editor: {
                allowBlank: false
            }
        }, {
            text: '实施人员(应用管理员)',
            dataIndex: 'carryOutApp',
            width: 120,
            editor: {
                allowBlank: false
            }
        }, {
            text: '责任组',
            dataIndex: 'dutyGroup',
        }, {
            text: '(调整后)维护标志',
            dataIndex: 'adjMainIden',
            width: 140
        }, {
            text: '操作',
            width: 80,
            xtype: 'actiontextcolumn',
            align: 'center',
            items: [{
                text: '发起',
                iconCls: 'execute',
                handler: function (grid, rowIndex) {
                    shutdownTaskA(grid, rowIndex);
                }
            },{
                text: '数据库详情',
                iconCls: 'monitor_search',
                handler: function (grid, rowIndex) {
                    shutdownDBInfo(grid, rowIndex);
                }
            }]
        },{
            text: '集群关系',
            width: 100,
            xtype: 'actiontextcolumn',
            align: 'center',
            items: [{
                text: '查看',
                iconCls: 'monitor_search',
                handler: function (grid, rowIndex) {
                    shutdownClusterInfo(grid, rowIndex);
                }
            }]
        },{
            text: '数据库详情',
            width: 100,
            xtype: 'actiontextcolumn',
            align: 'center',
            items: [{
                text: '查看',
                iconCls: 'monitor_search',
                handler: function (grid, rowIndex) {
                    shutdownDBInfo(grid, rowIndex);
                }
            }]
        }, {
            text: '责任人',
            dataIndex: 'dutyPerson',
            hidden: true,
            editor: {
                allowBlank: false
            }
        }, {
            text: '服务器运行状态',
            dataIndex: 'serverRunState',
            hidden: true
        }, {
            text: '应用投产状态',
            dataIndex: 'appPutState',
            hidden: true
        }, {
            text: '操作系统版本',
            dataIndex: 'opeSysVer',
            hidden: true
        }, {
            text: '最近一次开机时间',
            dataIndex: 'lastUpTime',
            hidden: true
        }, {
            text: '所属组别',
            dataIndex: 'groupOfAff',
            hidden: true
        }, {
            text: '操作系统版本详情',
            dataIndex: 'opeSysInfo',
            hidden: true
        }, {
            text: '服务器用途',
            dataIndex: 'serverUsage',
            hidden: true
        }, {
            text: '服务器类型',
            dataIndex: 'serverType',
            hidden: true
        }, {
            text: '关机维护建议',
            dataIndex: 'shutdownAdvice',
            hidden: true
        }, {
            text: '无需关机或实战切换的原因说明',
            dataIndex: 'noShutdownCase',
            hidden: true
        }, {
            text: '注意事项及维护经验总结',
            dataIndex: 'mastersNeedAtt',
            hidden: true
        }, {
            text: '（年初计划）维护标志',
            dataIndex: 'earlyMainIden',
            hidden: true
        }, {
            text: '（年初计划）年度其他工作计划备忘',
            dataIndex: 'earlyYearMemo',
            hidden: true
        }, {
            text: '（年初计划）年度其他工作计划备忘详情',
            dataIndex: 'earlyYearMes',
            hidden: true
        }, {
            text: '维护计划调整标志',
            dataIndex: 'mainPlanAdjSign',
            hidden: true
        }, {
            text: '（调整后）年度其他工作计划备忘',
            dataIndex: 'adjYearMemo',
            hidden: true
        }, {
            text: '调整原因说明',
            dataIndex: 'adjCause',
            hidden: true,
        }, {
            text: '窗口实施内容备忘',
            dataIndex: 'windowExecMatter',
            hidden: true
        }, {
            text: '计划实施起始时间',
            dataIndex: 'planStartTime',
            hidden: true
        }, {
            text: '计划实施结束时间',
            dataIndex: 'planEndTime',
            hidden: true,
            hidden: true
        }, {
            text: '维护期间的业务影响',
            dataIndex: 'imOnServiceDuMain',
            hidden: true
        }, {
            text: '通用准备工作注意事项',
            dataIndex: 'genPrePre',
            hidden: true
        }, {
            text: '注意事项及维护经验总结',
            dataIndex: 'motesMainExpSum',
            hidden: true
        }, {
            text: '维护完成标志',
            dataIndex: 'mainComIden',
            hidden: true
        }, {
            text: '实际维护起始时间',
            dataIndex: 'reallyStartTime',
            hidden: true
        }, {
            text: '实际维护结束时间',
            dataIndex: 'reallyEndTime',
            hidden: true
        }, {
            text: '其他异常情况说明',
            dataIndex: 'descOfOtherExce',
            hidden: true
        }];
    /** *********************Panel********************* */
    /** 模板信息列表panel* */
    var grid_panel = Ext.create('Ext.ux.ideal.grid.Panel',
        {
            store: scheduleStore,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
            iqueryFun: queryWhere,
            padding: grid_space,
            cls: 'customize_panel_back',
            selModel: Ext.create('Ext.selection.CheckboxModel'),
            region: 'center',
            border: true,
            cellTip : true,
            columnLines: true,
            columns: gridColumns,
            features: [
                Ext.create('Ext.grid.feature.Grouping',
                    {
                        showGroupsText: '实施窗口',
                        groupHeaderTpl: [
                            '<div>实施窗口: {name:this.formatName} ({rows.length})</div>',
                            {
                                formatName: function (name) {
                                    return name;
                                }
                            }
                        ],
                        startCollapsed: false
                    })
            ],
            listeners: {},
            collapsible: false,
        });

    function retWin(ret, rowIndex) {
        scheduleStore.getAt(rowIndex).set("isystemtypeuuid", ret.isystemtypeuuid);
        scheduleStore.getAt(rowIndex).set("ibusName", ret.ibusName);
        grid_panel.getView().refresh();
    }

    /** 主Panel* */
    var form = Ext.create("Ext.form.Panel", {

        buttonAlign: 'right', // 按钮对齐方式
        region: 'north',
        border: false,
        //baseCls:'customize_gray_back',
        dockedItems: [{
            baseCls: 'customize_gray_back',
            xtype: 'toolbar',
            border: false,
            dock: 'top',
            items: [frameworkManage, appTextField, hostnameTextField, createBeginTime, createEndTime, '->', queryButtonForPRJ, resetBtn, deleteBtn,batchTaskBtn]
        }]
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "shutdown_schedule_grid_area",
        layout: 'border',
        border: false,
        bodyCls: 'service_platform_bodybg customize_stbtn',
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        //bodyPadding : grid_space,
        items: [form, grid_panel]
    });

    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setHeight(contentPanel.getHeight() - 35);
        mainPanel.setWidth(contentPanel.getWidth());
    })
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });
    /** *********************方法********************* */
    /* 解决IE下trim问题 */
    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    /** 查询模板信息* */
    function queryWhere() {
        scheduleStore.reload();
    }

    function reset() {
        appTextField.setValue('');
        frameworkManage.setValue('');
        hostnameTextField.setValue('');
        createBeginTime.setValue('');
        createEndTime.setValue('');
        queryWhere();
        // 清理翻页缓存中的数据，避免在已经删除了记录后，还存在缓存数据，导致其他记录无法进行后续操作。
        grid_panel.getSelectionModel().deselectAll();
    }

    function deleteInfo() {
        var records = grid_panel.getSelectionModel().getSelection();
        var id;
        if (records.length == 0) {
            Ext.Msg.alert('提示', "请选择要删除的行");
            return;
        }

        for (var i = 0; i < records.length; i++) {
            if (i == 0) {
                id = records[i].get("scheduleId");
            } else {
                id = id + "," + records[i].get("scheduleId");
            }
        }

        Ext.Ajax.request({
            url: 'shutdown/deleteScheduleData.do',
            method: 'POST',
            params: {
                deleteIds: id
            },
            success: function (response, request) {
                var success = Ext.decode(response.responseText).success;
                var message = Ext.decode(response.responseText).message;
                if (success) {
                    Ext.Msg.alert('提示', '删除成功');
                    queryWhere();
                    // 清理翻页缓存中的数据，避免在已经删除了记录后，还存在缓存数据，导致其他记录无法进行后续操作。
                    grid_panel.getSelectionModel().deselectAll();
                } else {
                    Ext.Msg.alert('提示', message);
                }
            },
            failure: function (result, request) {
                secureFilterRs(result, '删除失败！');
            }
        });

    }
    
    function batchInitiatorTask() {
        var m = grid_panel.getSelectionModel().getSelection();
        if (m.length == 0) {
            Ext.Msg.alert('提示', "请选择要发起的行");
            return;
        }
        var jsonData = "[";
        for (var i = 0, len = m.length; i < len; i++) {
            var ss = Ext.JSON.encode(m[i].data);
            if (i == 0)
                jsonData = jsonData + ss;
            else
                jsonData = jsonData + "," + ss;
        }
        jsonData = jsonData + "]";


        Ext.Msg.confirm('确认发起', '是否确认发起选择的任务', function (id) {
            if (id == 'yes') {
                Ext.Ajax.request({
                    url: 'shutdown/creatInstanceTaskBatch.do',
                    method: 'POST',
                    params: {
                        jsonData: jsonData,
                    },
                    success: function (response, request) {
                        var success = Ext.decode(response.responseText).success;
                        var message = Ext.decode(response.responseText).message;
                        Ext.Msg.alert('提示', message);
                        scheduleStore.reload();
                    },
                    failure: function (result, request) {
                        secureFilterRs(result, '发起失败！');
                    }
                });
            }
        });
    }
    

    function winclose(me) {
        if (Ext.MessageBox.closable == false)
            Ext.MessageBox.close();
        if (me.winRet) {
            scheduleStore.reload();
        }
    }
    function shutdownTaskA(grid, rowIndex) {

        var iid = grid.getStore().data.items[rowIndex].data.iid;
        var hostname = grid.getStore().data.items[rowIndex].data.hostname;
        var projectId = grid.getStore().data.items[rowIndex].data.projectId;
        var projectName = grid.getStore().data.items[rowIndex].data.projectName;
        var earlyMainIden = grid.getStore().data.items[rowIndex].data.earlyMainIden;
        var adjYearMemo = grid.getStore().data.items[rowIndex].data.adjYearMemo;
        var flowName = grid.getStore().data.items[rowIndex].data.flowName;
        var webStudioId = grid.getStore().data.items[rowIndex].data.webStudioId;
        var framework = grid.getStore().data.items[rowIndex].data.framework;
        var carryOutApp = grid.getStore().data.items[rowIndex].data.carryOutApp;
        var carryOutDba = grid.getStore().data.items[rowIndex].data.carryOutDba;
        var carryOutSys = grid.getStore().data.items[rowIndex].data.carryOutSys;
        var ip = grid.getStore().data.items[rowIndex].data.ip;
        var os = grid.getStore().data.items[rowIndex].data.opeSysVer;
        var appName = grid.getStore().data.items[rowIndex].data.appOfAff;
        var hostid = grid.getStore().data.items[rowIndex].data.hostid;
        var scheduleId = grid.getStore().data.items[rowIndex].data.scheduleId;
        var adjMainIden =grid.getStore().data.items[rowIndex].data.adjMainIden;

        var taskHisParame = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '历史参数',
            handler: hisParamBtnHandler
        });

        var importExcel = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '导入',
            handler: importExcel
        });

        var exportTmp = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '导出模板',
            handler: exportParamBtnHandler
        });

        var taskSave = Ext.create("Ext.Button", {
            cls: 'Common_Btn',
            text: '排程发起',
            handler: initiatorTask
        });

        /** 步骤快照按钮  **/
        var snapshotBtn = Ext.create("Ext.Button",
            {
                cls: 'Common_Btn',
                text: "步骤快照",
                handler: StepSnapshot
            });


        // 定义复选框
        var selModel = Ext.create('Ext.selection.CheckboxModel', {
            checkOnly: true,
            listeners: {
                selectionchange: function (selModel, selections) {
                }
            }
        });
        // 定义可编辑grid组件
        var cellEditing = Ext.create('Ext.grid.plugin.CellEditing', {
            clicksToEdit: 2
        });

        /** 参数信息列表column* */
        var paramInfoColumns = [
            {
                text: '序号',
                width: 40,
                align: 'left',
                xtype: 'rownumberer'
            },
            {
                text: 'ID',
                dataIndex: 'iid',
                hidden: true
            },
            {
                text: '参数名称',
                dataIndex: 'iparamName',
                sortable: false,
                //width : 200
                flex: 2
            },
            {
                text: '参数类型',
                dataIndex: 'iparamType',
                sortable: false,
                renderer: function (value, cellmeta, record) {
                    if (record.get('iparamDesc').indexOf("#[sequence]s#") == 0) {
                        return 'String序列';
                    } else if (record.get('iparamType') == "cipher") {
                        return '密文';
                    }
                    return value;
                },
                flex: 2
            },
            {
                text: '参数值',
                dataIndex: 'iparamValue',
                sortable: false,
                flex: 4,
                //默认设置成textfield格式
                editor:
                    {
                        xtype: 'textfield',
                        allowBlank: false
                    },
                renderer: function (value, cellmeta, record) {
                    if (record.get('iparamType') == "cipher") {
                        var coun = "";
                        if (value.trim().length > 0) {
                            for (var i = 0; i < value.length; i++) {
                                coun = coun + "*";
                            }
                        }
                        if (value.trim() == "") {
                            coun = "";
                        }
                        return coun;
                    }
                    return value;
                },
            },
            {
                text: '参数说明',
                dataIndex: 'iparamDesc',
                sortable: false,
                renderer: function (value) {
                    var endnum = value.indexOf("#[sequence]e#");
                    var destemp = value.substring(endnum);
                    return destemp.replace("#[sequence]e#", "");
                },
                flex: 4
            }
        ];

        /** 参数信息列表model* */
        Ext.define('paramInfoModel',
            {
                extend: 'Ext.data.Model',
                fields: [
                    {
                        name: 'iid',
                        type: 'long'
                    },
                    {
                        name: 'iparamName',
                        type: 'string'
                    },
                    {
                        name: 'iparamType',
                        type: 'string'
                    },
                    {
                        name: 'iparamValue',
                        type: 'string'
                    },
                    {
                        name: 'iparamDesc',
                        type: 'string'
                    }
                ]
            });

        /** 参数信息列表store* */
        var paramInfoStore = Ext.create('Ext.data.Store',
            {
                autoLoad: false,
                autoDestroy: true,
                model: 'paramInfoModel',
                proxy:
                    {
                        type: 'ajax',
                        url: 'shutdown/getShutdownParamInfoList.do',
                        reader:
                            {
                                type: 'json',
                                root: 'dataList'
                            }
                    },
            });

        // 参数信息列表grid重新加载
        paramInfoStore.load(
            {
                params:
                    {
                        systemId: projectId,
                        sysName: projectName
                    }
            });

        paramInfoStore.on('load', function (store, options) {
            var paramName_splitStr = '@@';
            var paramNames = '';
            for (var i = 0; i < store.getCount(); i++) {
                var record = store.getAt(i);
                //参数校验
                if (store.getCount() == 0) {
                    paramNames += record.data.iparamName;
                } else {
                    paramNames += paramName_splitStr + record.data.iparamName;
                }
            }
        });

        var stringsStore = Ext.create('Ext.data.JsonStore', {
            fields: ['id', 'value']
        });

        /**参数信息列表gridpanel* */
        var param_info_grid = Ext.create('Ext.ux.ideal.grid.Panel',
            {
                columnLines: true,
                padding: grid_space,
                ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                cls: 'customize_panel_back',
                autoScroll: false,
                selModel: Ext.create('Ext.selection.CheckboxModel'),
                loadMask: {
                    msg: " 数据加载中，请稍等 "
                },
                border: true,
                store: paramInfoStore,
                columns: paramInfoColumns,
                listeners: {
                    //监听函数，在点击之前进行监听
                    beforeedit: function (editor, e, eOpts) {
                        //进行条件判断，符合条件则设置称combobox
                        var tempdes = e.record.get('iparamDesc');
                        if (tempdes.indexOf("#[sequence]s#") == 0) {
                            var endnum = tempdes.indexOf("#[sequence]e#");
                            var comboDatatemp = tempdes.substring(0, endnum);
                            var comboDataArray = comboDatatemp.replace("#[sequence]s#", "").split(",");
                            if (comboDataArray.length == 3 && comboDataArray[2] == '') {
                                var uuid = comboDataArray[0];
                                var iid = comboDataArray[1];
                                Ext.Ajax.request({
                                    url: 'getYushezhi.do',
                                    params: {
                                        uuid: uuid,
                                        iid: iid
                                    },
                                    success: function (resp, opts) {
                                        var respText = Ext.decode(resp.responseText);
                                        var data = respText.value.split(",");
                                        var comboDatatest = '[';
                                        for (var j = 0; j < data.length; j++) {
                                            if (j != 0) {
                                                comboDatatest += ",";
                                            }
                                            comboDatatest += "{'id':'" + data[j] + "','value':'" + data[j] + "'}";
                                        }
                                        comboDatatest += ']';
                                        stringsStore.loadData(Ext.JSON.decode(comboDatatest));
                                    }
                                });
                            } else {
                                var comboDatatest = '[';
                                for (var j = 0; j < comboDataArray.length; j++) {
                                    if (j != 0) {
                                        comboDatatest += ",";
                                    }
                                    comboDatatest += "{'id':'" + comboDataArray[j] + "','value':'" + comboDataArray[j] + "'}";
                                }
                                comboDatatest += ']';
                                stringsStore.loadData(Ext.JSON.decode(comboDatatest));

                            }
                            e.column.setEditor({
                                xtype: 'combobox',
                                store: stringsStore,
                                queryMode: 'local',
                                width: 100,
                                displayField: 'value',
                                valueField: 'id',
                                editable: true
                            });
                            $('#lbSty').remove();
                            $('body').append('<style id="lbSty1">#icspdbcreatetaskpage_grid_area div div:first-child div:nth-child(3) .x-boundlist,.x-css-shadow{height:auto!important;}</style>');
                        } else {
                            $(e.view.body.dom).parent().parent().siblings('.x-small-editor').find('tbody').find('.x-unselectable').children('div').css('background', '#fff');
                            $('#lbSty1').remove();
                            $('body').append('<style id="lbSty">#icspdbcreatetaskpage_grid_area div div:first-child div:nth-child(3) .x-boundlist,.x-css-shadow{height:0px!important;}</style>');
                            //不符合条件的设置称textfield
//                    e.column.setEditor({
//                        xtype: 'textfield',
//                        allowBlank:false
//                    });
                        }
                    }
                },
                plugins: [cellEditing],
                dockedItems: [
                    {
                        xtype: 'toolbar',
                        items: ['运行参数', '->', taskHisParame, importExcel, exportTmp]
                    }]
            });

        var handleTaskName =hostname+'_'+Ext.Date.format(new Date(), 'YmdHis');

        var planInfoformPanel = Ext.create('Ext.form.Panel', {
            region: 'north',
            layout: 'anchor',
            buttonAlign: 'center',
            collapsible: false,//可收缩
            collapsed: false,//默认收缩
            border: false,
            bodyBorder: false,
            dockedItems: [
                {
                    xtype: 'toolbar',
                    border: false,
                    dock: 'top',
                    items: [{
                        fieldLabel: '维护标志（运维需求）',
                        name: 'ifullname',
                        labelAlign: 'right',
                        width: contentPanel.getWidth() / 4,
                        labelWidth: 150,
                        readOnly: true,
                        xtype: 'textfield',
                        value: earlyMainIden,
                    },{
                        fieldLabel: '维护类',
                        name: 'iloginname',
                        labelAlign: 'right',
                        readOnly: true,
                        width: contentPanel.getWidth() / 5.5,
                        labelWidth: 65,
                        xtype: 'textfield',
                        value: adjYearMemo,
                    }]
                },            {
                    xtype: 'toolbar',
                    border: false,
                    dock: 'top',
                    items: [{
                        width: contentPanel.getWidth() / 4,
                        labelWidth: 65,
                        xtype: 'textfield',
                        labelAlign: 'right',
                        allowBlank: false,
                        maxLength: 255,
                        maxLengthText: '请输入255位长度的任务名称',
                        enforceMaxLength: true, // 是否强制限制字符串长度，超过最大长度将不能再被输入
                        name: 'handleTaskName',
                        fieldLabel: '任务名称',
                        value: handleTaskName
                    }, '->', snapshotBtn, taskSave]
                }]

        });

        var shutdownTaskPanl = Ext.create('Ext.panel.Panel', {
            region: 'center',
            layout: 'border',
            border: true,
            bodyPadding: grid_margin,
            bodyCls: 'customize_stbtn',
            cls: 'customize_panel_back panel_space_right_zb',
            items: [planInfoformPanel, param_info_grid]
        });

        var shutdownTaskWin = Ext.create('Ext.window.Window', {
            title: '主机：【' + hostname + '】排程发起',
            id: 'shutdownTaskWin',
            closeAction: 'destroy',
            modal: true,
            region: 'center',
            constrain: true,
            autoScroll: true,
            items: [shutdownTaskPanl],
            width: contentPanel.getWidth() - 350,
            height: contentPanel.getHeight() - 50,
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
        });
        shutdownTaskWin.show();

        // 快照按钮
        function StepSnapshot() {
            var xmlSnapshot = Ext.create('Ext.window.Window', {
                id: 'xmlSnapshot',
                title: '任务步骤图',
                width: contentPanel.getWidth(),
                height: contentPanel.getHeight(),
                maximizable: true,
                layout: 'border',
                modal: true,
                autoScroll: true,
                closeAction: 'destroy',
                loader: {
                    url: "page/shutdown/shutdownSchedule/show.jsp?prjID=" + projectId,
                    autoLoad: true,
                    params: {
                        projectname: projectName,
                        flowname: flowName
                    },
                    autoDestroy: true,
                    scripts: true
                }
            });
            xmlSnapshot.show();
        }

        // 导出模板方法
        function exportParamBtnHandler() {
            window.location.href = 'exportSpdbParams.do';
        }

        //导入Excel参数
        function importExcel() {

            var upldWin = null;
            var upLoadformPane = null;
            var paramNames = '';
            var ftpParamNames = '';
            var hcitemId = 0;
            var paramName_splitStr = '@@';
            var selectValue;
            var url = '';

            //校验是否选择了模板
            if (projectId == 0) {
                Ext.MessageBox.alert("提示", "请选择模板名称");
                return false;
            }
            //销毁win窗口
            if (!(null == upldWin || undefined == upldWin || '' == upldWin)) {
                upldWin.destroy();
                upldWin = null;
            }

            if (!(null == upLoadformPane || undefined == upLoadformPane || '' == upLoadformPane)) {
                upLoadformPane.destroy();
                upLoadformPane = null;
            }
            //导入文件Panel
            upLoadformPane = Ext.create('Ext.form.Panel', {
                //id:'upLoadpanel',
                width: '100%',
                height: '100%',
                frame: true,
                buttonAlign: 'left',
                items: [
                    {
                        //id:'fileFildId',
                        xtype: 'filefield',
                        name: 'file', // 设置该文件上传空间的name，也就是请求参数的名字
                        fieldLabel: '选择文件',
                        labelWidth: 80,
                        msgTarget: 'side',
                        anchor: '100%',
                        buttonText: '浏览...',
                        width: 370
                    },
                    {
                        //id:'paramNamesId',
                        xtype: 'hidden',
                        name: 'paramNames',
                        value: paramNames,
                        width: 360,
                        height: 130
                    }
                ],
                buttons: [
                    {
                        //id:'upldBtnId',
                        text: '批量导入',
                        margin: '0 0 0 5',
                        handler: function () {
                            var form = this.up('form').getForm();
                            var upfile = form.findField("file").getValue();

                            if (upfile == '') {
                                Ext.Msg.alert('提示', "请选择文件...");
                                return;
                            }

                            var hdtmpFilNam = form.findField("file").getValue();
                            if (!checkFile(hdtmpFilNam)) {
                                form.findField("file").setRawValue('');
                                return;
                            }

                            if (form.isValid()) {
                                Ext.MessageBox.wait("数据处理中...", "进度条");
                                form.submit({
                                    url: 'importParamExcel.do',
                                    success: function (form, action) {
                                        var msg = Ext.decode(action.response.responseText).message;
                                        var dataStr = Ext.decode(action.response.responseText).datastr;
                                        //分隔字符串
                                        var result = dataStr.split(";;;");
                                        var notFoundStr = result[0];//不存在的字符串
                                        var paramsStr = result[1];//匹配成功的参数
                                        var fmtErrStr = result[2];//存在但格式不正确的参数
                                        //处理不存在
                                        var msgStr = "";
                                        if (!(notFoundStr == null || notFoundStr == "" || notFoundStr == undefined)) {
                                            msgStr += "参数名【" + notFoundStr + "】在导入模板中不存在！<br>";
                                        }

                                        //处理匹配成功的参数
                                        var params = paramsStr.split(",,,");
                                        for (var mm = 0; mm < params.length; mm++) {
                                            var param = params[mm];
                                            if (!(param == null || param == "" || param == undefined)) {
                                                var val = param.split(":::");
                                                var name = val[0];
                                                var value = val[1];
                                                //给页面赋值
                                                var store = paramInfoStore;
                                                for (var i = 0; i < store.getCount(); i++) {
                                                    var record = store.getAt(i);
                                                    var iname = record.data.iparamName;
                                                    if (iname == name) {
                                                        record.set('iparamValue', value);
                                                    }
                                                }
                                            }
                                        }
                                        //处理存在但格式不正确的参数
                                        if (!(fmtErrStr == null || fmtErrStr == "" || fmtErrStr == undefined)) {
                                            msgStr += "<br>导入参数【" + fmtErrStr + "】格式不正确！<br>";
                                        }
                                        //提示信息
                                        if (!(msgStr == "" || msgStr == undefined)) {
                                            Ext.MessageBox.alert("提示", msgStr);
                                        } else {
                                            Ext.MessageBox.alert("提示", msg);
                                        }
                                        upldWin.close();
                                        return;
                                    },
                                    failure: function (form, action) {
                                        secureFilterRsFrom(form, action);
                                    }
                                });
                            }
                        }
                    }
                ]
            });
            //导入窗口
            upldWin = Ext.create('Ext.window.Window', {
                //id:'upldWinId',
                title: '参数信息批量导入',
                width: 400,
                height: 200,
                modal: true,
                resizable: false,
                closeAction: 'desdroy',
                items: [upLoadformPane]
            }).show();
            upldWin.on("beforeshow", function (self, eOpts) {
                var form = upLoadformPane.getForm();
                form.reset();
            });

            upldWin.on("destroy", function (self, eOpts) {
                upLoadformPane.destroy();
            });

            /** 判断文件类型是否为excel* */
            function checkFile(fileName) {
                var file_reg = /\.([xX][lL][sS]){1}$|\.([xX][lL][sS][xX]){1}$|\.([xX][lL][sS][mM]){1}$/;
                if (!file_reg.test(fileName)) {
                    Ext.Msg.alert('提示', '文件类型错误,请选择Excel文件');
                    return false;
                }
                return true;
            }

        }

        //历史参数按钮
        function hisParamBtnHandler() {
            /** 参数历史列表model* */
            Ext.define('paramHisModel',
                {
                    extend: 'Ext.data.Model',
                    fields: [
                        {
                            name: 'iid',
                            type: 'long'
                        },
                        {
                            name: 'iconfig',
                            type: 'long'
                        },
                        {
                            name: 'ivalues',
                            type: 'string'
                        }
                    ]
                });

            /** 参数历史列表store* */
            var paramHisStore = Ext.create('Ext.data.Store',
                {
                    autoLoad: false,
                    autoDestroy: true,
                    model: 'paramHisModel',
                    proxy:
                        {
                            type: 'ajax',
                            url: 'getParamHisList.do',
                            reader:
                                {
                                    type: 'json',
                                    root: 'dataList'
                                }
                        }
                });
            paramHisStore.on('beforeload', function (store, options) {
                var new_params =
                    {
                        systemId: projectId
                    };
                Ext.apply(store.proxy.extraParams, new_params);
            });

            /** 参数信息列表column* */
            var paramHisColumns = [
                {
                    text: '序号',
                    width: 40,
                    locked: true,
                    xtype: 'rownumberer'
                },
                {
                    text: 'iid',
                    dataIndex: 'iid',
                    hidden: true
                },
                {
                    text: '启动参数',
                    dataIndex: 'ivalues',
                    sortable: false,
                    flex: 1
                }
            ];

            /**参数历史列表gridpanel* */
            var param_history_grid = Ext.create('Ext.ux.ideal.grid.Panel',
                {
                    width: 610,
                    height: 265,
                    autoScroll: true,
                    loadMask: {
                        msg: " 数据加载中，请稍等 "
                    },
                    store: paramHisStore,
                    border: false,
                    columnLines: true,
                    columns: paramHisColumns
                });
            var param_his_win = Ext.create('Ext.window.Window', {
                //id:'param_his_win',
                width: 630,
                height: 340,
                resizable: false,
                plain: false,
                modal: true,
                draggable: false,
                closeAction: 'hide',
                items: [param_history_grid],
                buttons: [
                    {
                        text: '刷新',
                        handler: function () {
//					alert("刷新按钮。。。");
                            paramHisStore.reload();
                        }
                    },
                    {
                        text: '关闭',
                        handler: function () {
//					alert("关闭按钮。。。");
                            param_his_win.hide();
                        }
                    }
                ]
            });

            //校验是否选择了模板
            if (projectId == 0) {
                Ext.MessageBox.alert("提示", "请选择模板名称");
                return false;
            }
            //参数历史--store加载
            var grid = param_history_grid;
            grid.store.load();
            //参数历史--窗口属性设置
            //var param_his_win = Ext.getCmp("param_his_win");
            param_his_win.setTitle("历史参数--" + projectName);
            var bd = (document.body || document.documentElement);
            var left = bd.scrollLeft + Ext.getBody().getWidth() - 4 - param_his_win.width;
            var top = bd.scrollTop + Ext.getBody().getHeight() - 4 - param_his_win.height;
            param_his_win.setPosition(left, top);
            param_his_win.show();
        }


        function initiatorTask() {

            //处理参数
            var store = paramInfoStore;
            var jsonParamArr = [];
            for (var j = 0, lenn = store.getCount(); j < lenn; j++) {
                var record = store.getAt(j);
                jsonParamArr.push(record.data);
                //参数校验
                var iname = record.data.iparamName;
                var itype = record.data.iparamType;
                var ivalue = record.data.iparamValue;
                var reStr = validateParamRec(iname, itype, ivalue);
                if (!reStr[0]) {
                    Ext.MessageBox.alert('信息提示', reStr[1]);
                    return false;
                }
            }

            Ext.Msg.confirm('确认发起', '是否确认直接发起此任务', function (id) {
                if (id == 'yes') {
                    Ext.Ajax.request({
                        url: 'shutdown/creatInstanceTask.do',
                        method: 'POST',
                        params: {
                            scheduleId:scheduleId,
                            hostid:hostid,
                            jsonData: Ext.encode(jsonParamArr),
                            hostname: hostname,
                            OS:os,
                            projectId: projectId,
                            projectName: projectName,
                            maintainType: earlyMainIden,
                            maintainInfo: adjYearMemo,
                            flowName: flowName,
                            webStudioId: webStudioId,
                            framework: adjMainIden,
                            carryOutApp: carryOutApp,
                            carryOutDba: carryOutDba,
                            carryOutSys: carryOutSys,
                            taskName: handleTaskName,
                            ip: ip,
                            appName:appName,
                            iid:iid
                        },
                        success: function (response, request) {
                            var success = Ext.decode(response.responseText).success;
                            var message = Ext.decode(response.responseText).message;
                            Ext.Msg.alert('提示', message);
                            shutdownTaskWin.close();
                            scheduleStore.reload();
                        },
                        failure: function (result, request) {
                            secureFilterRs(result, '发起失败！');
                        }
                    });
                }
            });

            //校验参数方法
            function validateParamRec(iname, itype, ivalue) {
                var reStr = new Array();
                reStr[0] = true;
                reStr[1] = '';
                if (trim(ivalue).length > 255) {
                    reStr[0] = false;
                    reStr[1] = '参数值: ' + iname + ' 长度不能超过255个字符！';
                    return reStr;
                }
                if (itype == 0 || itype == "Integer") {
                    if (ivalue == "") {
                        reStr[0] = false;
                        reStr[1] = '参数 ' + iname + ' 请填写Integer类型数据(非负整数)！';
                        return reStr;
                    }
                    if (!isNotNegativeInteger(ivalue)) {
                        reStr[0] = false;
                        reStr[1] = '参数 ' + iname + ' 请填写Integer类型数据(非负整数)！';
                        return reStr;
                    }
                } else if (itype == 2 || itype == "String") {
                    var checkShellPath = ivalue;
                    if (iname != 'appName') {
                        if (!checkIsNotEmpty(checkShellPath)) {
                            reStr[0] = false;
                            reStr[1] = '参数 ' + iname + ' 请填写String类型数据(不能为空)！';
                            return reStr;
                        }
                        if (checkShellPath != "") {
                            var errorChar = "!@#$%^&*|";
                            for (var ii = 0; ii < errorChar.length; ii++) {
                                var check = errorChar.substring(ii, ii + 1);
                                if (checkShellPath.indexOf(check) >= 0) {
                                    reStr[0] = false;
                                    reStr[1] = '参数 ' + iname + ' 项检查包含非法字符串，请确认!';
                                    return reStr;
                                }
                            }
                        }
                    }
                }
                return reStr;
            }

        }
    }

    function  shutdownDBInfo (grid, rowIndex){

        var iid = grid.getStore().data.items[rowIndex].data.iid;
        var hostname = grid.getStore().data.items[rowIndex].data.hostname;
        var projectId = grid.getStore().data.items[rowIndex].data.projectId;
        var projectName = grid.getStore().data.items[rowIndex].data.projectName;
        var earlyMainIden = grid.getStore().data.items[rowIndex].data.earlyMainIden;
        var adjYearMemo = grid.getStore().data.items[rowIndex].data.adjYearMemo;
        var flowName = grid.getStore().data.items[rowIndex].data.flowName;
        var webStudioId = grid.getStore().data.items[rowIndex].data.webStudioId;
        var framework = grid.getStore().data.items[rowIndex].data.framework;
        var carryOutApp = grid.getStore().data.items[rowIndex].data.carryOutApp;
        var carryOutDba = grid.getStore().data.items[rowIndex].data.carryOutDba;
        var carryOutSys = grid.getStore().data.items[rowIndex].data.carryOutSys;
        var ip = grid.getStore().data.items[rowIndex].data.ip;
        var os = grid.getStore().data.items[rowIndex].data.opeSysVer;
        var appName = grid.getStore().data.items[rowIndex].data.appOfAff;
        var planId = grid.getStore().data.items[rowIndex].data.id;
        var scheduleId = grid.getStore().data.items[rowIndex].data.scheduleId;

        Ext.define('shutdownDBModel', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'hostName',
                    type: 'string'
                }, {
                    name: 'ip',
                    type: 'string'
                }, {
                    name: 'entry',
                    type: 'string'
                }, {
                    name: 'dbName',
                    type: 'string'
                }, {
                    name: 'state',
                    type: 'string'
                }, {
                    name: 'env',
                    type: 'string'
                }
            ]
        });

        /** 模板列表数据源* */
        var shutdownDBStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            remoteSort: true,
            model: 'shutdownDBModel',
            pageSize: 10,
            proxy: {
                type: 'ajax',
                url: 'shutdown/getShutdownDBInfoList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        shutdownDBStore.on('beforeload', function (store, options) {
            var new_params = {
                ip: ip
            };
            Ext.apply(shutdownDBStore.proxy.extraParams, new_params);
        });

        var DBgridColumns = [
            {
                text: '序号',
                width: 40,
                align: 'left',
                xtype: 'rownumberer'
            },{
                text: '主机名',
                dataIndex: 'hostName',
                flex: 1,
            },{
                text: 'IP',
                dataIndex: 'ip',
                flex: 1,
            },
            {
                text: '数据库类型',
                dataIndex: 'entry',
                flex: 1,
            }, {
                text: '数据库名称',
                flex: 1,
                dataIndex: 'dbName',
            }, {
                text: '状态',
                dataIndex: 'state',
                flex: 1,
            }, {
                text: '环境',
                dataIndex: 'env',
                flex: 1,
            }];

        var shutdownDBgrid = Ext.create('Ext.ux.ideal.grid.Panel',
            {
                store: shutdownDBStore,
                ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                iqueryFun:  shutdownDBStore.reload(),
                padding: grid_space,
                cls: 'customize_panel_back',
                selModel: Ext.create('Ext.selection.CheckboxModel'),
                region: 'center',
                border: true,
                cellTip: true,
                columnLines: true,
                columns: DBgridColumns,
                collapsible: false,
            });

        var shutdownDBPanl = Ext.create('Ext.panel.Panel', {
            region: 'center',
            layout: 'border',
            border: true,
            bodyPadding: grid_margin,
            bodyCls: 'customize_stbtn',
            cls: 'customize_panel_back panel_space_right_zb',
            items: [shutdownDBgrid]
        });

        var shutdownDBWin = Ext.create('Ext.window.Window', {
            title: '主机：【' + hostname + '】数据库详情',
            id: 'shutdownDBWin',
            closeAction: 'destroy',
            modal: true,
            region: 'center',
            constrain: true,
            autoScroll: true,
            items: [shutdownDBPanl],
            width: contentPanel.getWidth() - 350,
            height: contentPanel.getHeight() - 50,
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
        });
        shutdownDBWin.show();

    }

    function  shutdownClusterInfo (grid, rowIndex){

        var iid = grid.getStore().data.items[rowIndex].data.iid;
        var hostname = grid.getStore().data.items[rowIndex].data.hostname;
        var projectId = grid.getStore().data.items[rowIndex].data.projectId;
        var projectName = grid.getStore().data.items[rowIndex].data.projectName;
        var earlyMainIden = grid.getStore().data.items[rowIndex].data.earlyMainIden;
        var adjYearMemo = grid.getStore().data.items[rowIndex].data.adjYearMemo;
        var flowName = grid.getStore().data.items[rowIndex].data.flowName;
        var webStudioId = grid.getStore().data.items[rowIndex].data.webStudioId;
        var framework = grid.getStore().data.items[rowIndex].data.framework;
        var carryOutApp = grid.getStore().data.items[rowIndex].data.carryOutApp;
        var carryOutDba = grid.getStore().data.items[rowIndex].data.carryOutDba;
        var carryOutSys = grid.getStore().data.items[rowIndex].data.carryOutSys;
        var ip = grid.getStore().data.items[rowIndex].data.ip;
        var os = grid.getStore().data.items[rowIndex].data.opeSysVer;
        var appName = grid.getStore().data.items[rowIndex].data.appOfAff;
        var planId = grid.getStore().data.items[rowIndex].data.id;
        var scheduleId = grid.getStore().data.items[rowIndex].data.scheduleId;
        var adjMainIden =grid.getStore().data.items[rowIndex].data.adjMainIden;


        if(adjMainIden.indexOf("实战")==-1) {
            Ext.Msg.alert('提示', '单机没有集群关系！');
            return;
        }


        Ext.define('shutdownClusterModel', {
            extend: 'Ext.data.Model',
            fields: [
                {
                    name: 'hostName',
                    type: 'string'
                }, {
                    name: 'ip',
                    type: 'string'
                }, {
                    name: 'name',
                    type: 'string'
                }, {
                    name: 'instacename',
                    type: 'string'
                }, {
                    name: 'isprimthost',
                    type: 'string'
                }, {
                    name: 'isprimtoback',
                    type: 'string'
                }
            ]
        });

        /** 模板列表数据源* */
        var shutdownClusterStore = Ext.create('Ext.data.Store', {
            autoLoad: true,
            autoDestroy: true,
            remoteSort: true,
            model: 'shutdownClusterModel',
            pageSize: 10,
            proxy: {
                type: 'ajax',
                url: 'shutdown/getShutdownClusterInfoList.do',
                reader: {
                    type: 'json',
                    root: 'dataList',
                    totalProperty: 'total'
                }
            }
        });
        shutdownClusterStore.on('beforeload', function (store, options) {
            var new_params = {
                iid: iid
            };
            Ext.apply(shutdownClusterStore.proxy.extraParams, new_params);
        });

        var ClustergridColumns = [
            {
                text: '序号',
                width: 40,
                align: 'left',
                xtype: 'rownumberer'
            }, {
                text: '主机名',
                dataIndex: 'hostName',
                flex: 1,
            }, {
                text: 'IP',
                dataIndex: 'ip',
                flex: 1,
            },
            {
                text: '数据库名称',
                dataIndex: 'name',
                flex: 1,
            }, {
                text: '实例名称',
                flex: 1,
                dataIndex: 'instacename',
            }, {
                text: '是否是主机',
                dataIndex: 'isprimthost',
                flex: 1,
                renderer: function (value, p, record) {
                    if (value == 1) {
                        return "<span class='orange_color State_Color'>主机</span>";
                    } else if (value == 0) {
                        return "<span class='Green_color State_Color'>备机</span>";
                    } else {
                        return "<span class='yellow_color State_Color'>异常</span>";
                    }
                }
            }, {
                text: '是否从主机切换到当前备机',
                dataIndex: 'isprimtoback',
                flex: 1,
                renderer: function (value, p, record) {
                    if (value == 1) {
                        return "<span class='orange_color State_Color'>参与切换</span>";
                    } else if (value == 0) {
                        return "<span class='Green_color State_Color'>不参与切换</span>";
                    } else {
                        return "<span class='yellow_color State_Color'>异常</span>";
                    }
                }
            }];

        var shutdownClustergrid = Ext.create('Ext.ux.ideal.grid.Panel',
            {
                store: shutdownClusterStore,
                ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
                iqueryFun:  shutdownClusterStore.reload(),
                padding: grid_space,
                cls: 'customize_panel_back',
                selModel: Ext.create('Ext.selection.CheckboxModel'),
                region: 'center',
                border: true,
                cellTip: true,
                columnLines: true,
                columns: ClustergridColumns,
                collapsible: false,
            });

        var shutdownClusterPanl = Ext.create('Ext.panel.Panel', {
            region: 'center',
            layout: 'border',
            border: true,
            bodyPadding: grid_margin,
            bodyCls: 'customize_stbtn',
            cls: 'customize_panel_back panel_space_right_zb',
            items: [shutdownClustergrid]
        });

        var shutdownClusterWin = Ext.create('Ext.window.Window', {
            title: '主机：【' + hostname + '】集群关系详情',
            id: 'shutdownDBWin',
            closeAction: 'destroy',
            modal: true,
            region: 'center',
            constrain: true,
            autoScroll: true,
            items: [shutdownClusterPanl],
            width: contentPanel.getWidth() - 350,
            height: contentPanel.getHeight() - 50,
            draggable: false,// 禁止拖动
            resizable: false,// 禁止缩放
            layout: 'fit',
        });
        shutdownClusterWin.show();

    }
});


