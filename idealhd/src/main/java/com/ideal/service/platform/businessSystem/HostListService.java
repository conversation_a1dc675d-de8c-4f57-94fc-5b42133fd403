package com.ideal.service.platform.businessSystem;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.commons.UserInfo;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.engine.Engine;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.db.DBManager;
import com.ideal.ieai.server.repository.db.DBResource;
import com.ideal.ieai.server.repository.hd.dataCenterOperation.DataCenterOperationManage;
import com.ideal.ieai.server.repository.hd.datacenter.DataCenterFilter;
import com.ideal.ieai.server.repository.hd.operation.OperRecord;
import com.ideal.ieai.server.repository.hd.operation.OperationManager;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.Host;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.HostMger;
import com.ideal.ieai.server.repository.hd.platform.businessSystem.IfcSateContainer;

/**
 * <ul>
 * <li>Title: HostListService.java</li>
 * <li>Description:设备列表service</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2015-10-27
 */
public class HostListService {
	public HostListService() {
		super();
	} 
 
	/**
	 * <li>Description:获得“设备列表”，同时为每个设备设置 “流程”状态</li> 
	 * <AUTHOR>
	 * 2015-10-30 
	 * @param centerName
	 * @param sysId
	 * @param sysName
	 * @return
	 * @throws RepositoryException
	 * return List<Host>
	 */
	public List<Host> getHostList(String centerName,String sysId,String sysName) throws RepositoryException{
		String prjectName =sysName;
		Host tempHost =null;

		
		HostMger hostMgr = new HostMger();
		List<Host> hostList =hostMgr.getHostList(centerName, sysId);
		if(hostList!=null&&hostList.size()>0){
		    for (int i = 0; i < hostList.size(); i++) {
		        tempHost = hostList.get(i);
		        if(tempHost.getJobstate()==-1l){
		            tempHost.setStateWithFlow(IfcSateContainer.HOST_FLOW_STATE_ON_LINEING);//设置设备的流程状态为='上线中'
		        }else if(tempHost.getJobstate()==-2l){
		            tempHost.setStateWithFlow(IfcSateContainer.HOST_FLOW_STATE_OFF_LINEING);//设置设备的流程状态为='下线中'
		        }else if(tempHost.getJobstate()>0l){
		            tempHost.setStateWithFlow(IfcSateContainer.HOST_FLOW_STATE_HANDOVER);//设置设备的流程状态为='切换中'
		        }
		    }
		}
		return hostList;
	}
	
	
	/**
	 * 
	 * <li>Description:服务器上线</li> 
	 * <AUTHOR>
	 * 2015年10月20日 
	 * @param computerids
	 * @param sysname
	 * @param request
	 * @throws Exception
	 * return void
	 */
	public void serverOnline(String computerids,String sysname,long sysId, HttpServletRequest request,String flowNname) throws Exception{
	 for (int i = 0; i < 10; i++)
	    {
	        try
	        {
				Connection conn = null;
				List listOR = new ArrayList();
				try {
					HostMger hostMgr = new HostMger();
					try {
						conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMERGENCY_SWITCH);
					} catch (DBException e1) {
						throw new Exception("506");
					}
					SessionData sessionData = SessionData.getSessionData(request);
					UserInfo userInfo = new UserInfo();
					userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
					userInfo.setFullName(sessionData.getUserName());
					List list = hostMgr.getEquipmentById(computerids,conn);	
					if(list!=null&&list.size()>0){
						for(int n=0;n<list.size();n++){
							String servermessage = "";
							Map map = (Map)list.get(n);
							if(map!=null){
								servermessage = (String)map.get("agentip")+":"+(String)map.get("port");
								Map envVarValues = new HashMap();
								List parameters = new ArrayList();
								parameters.add(servermessage);
								parameters.add((String)map.get("ip"));
								parameters.add(sysname);
								List cpIdss = new ArrayList();
								cpIdss.add((Long)map.get("cpid"));
								DataCenterOperationManage.getInstance()
								.updateComputerJobState(
										cpIdss,
										sysId,
										Constants.JOB_ONLINE, conn);
					            Engine.getInstance().startFlow(userInfo, sysname, flowNname, parameters, envVarValues, null, (String)map.get("ip"), "",false, null, null,false,0,conn,Constants.IEAI_EMERGENCY_SWITCH,Constants.IEAI_EMERGENCY_SWITCH);
					            
					            OperRecord or = new OperRecord();
                                or.setSysName(sysname);
                                or.setFlowName(flowNname);
                                or.setAgentIP((String)map.get("ip"));
                                or.setOpTime(System.currentTimeMillis());
                                or.setPrjType(4);
                                or.setOpcommand(flowNname);
                                or.setOperator(sessionData.getUserName());
                                listOR.add(or);
							}
						}
					}
					conn.commit();
					OperationManager.getInstance().addOperRecords(listOR,Constants.IEAI_EMERGENCY_SWITCH);
					break;
				} catch (ServerException e) {
					conn.rollback();
					_log.info("DataCenterOperationService.sysOnline is error!",e);
					if(e.getErrCode()==506){
						throw new Exception("506");
					}else{
						throw new Exception(e);
					}
				}catch (Exception e) {
					conn.rollback();
					_log.info("DataCenterOperationService.sysOnline is error!",e);
					if("506".equals(e.getMessage())){
						throw new Exception("506");
					}else{
						throw new Exception(e);
					}
				}finally{
					if(conn!=null){
						conn.close();
					}
				}
            } catch (Exception ex)
            {
            	if("506".equals(ex.getMessage())){
            		DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            	}else{
            		throw new Exception(ex);
            	}
            }
	    }
	}
	
	/**
	 * 
	 * <li>Description:服务器下线</li> 
	 * <AUTHOR>
	 * 2015年10月20日 
	 * @param computerids
	 * @param sysname
	 * @param request
	 * @throws Exception
	 * return void
	 */
	public void serverOffline(String computerids,String sysname,long sysId,HttpServletRequest request,String flowNname) throws Exception{
	 for (int i = 0; i < 10; i++)
	    {
	        try
	        {
				Connection conn = null;
				List listOR = new ArrayList();
				try {
					HostMger hostMgr = new HostMger();
					try {
						conn = DBManager.getInstance().getJdbcConnection(Constants.IEAI_EMERGENCY_SWITCH);
					} catch (DBException e1) {
						throw new Exception("506");
					}
					SessionData sessionData = SessionData.getSessionData(request);
					UserInfo userInfo = new UserInfo();
					userInfo.setId(Long.valueOf(sessionData.getUserInnerCode()));
					userInfo.setFullName(sessionData.getUserName());
					List list = hostMgr.getEquipmentById(computerids,conn);	
					if(list!=null&&list.size()>0){
						for(int n=0;n<list.size();n++){
							String servermessage = "";
							Map map = (Map)list.get(n);
							if(map!=null){
								servermessage = (String)map.get("agentip")+":"+(String)map.get("port");
								Map envVarValues = new HashMap();
								List parameters = new ArrayList();
								parameters.add(servermessage);
								parameters.add((String)map.get("ip"));
								parameters.add(sysname);
								List cpIdss = new ArrayList();
								cpIdss.add((Long)map.get("cpid"));
								DataCenterOperationManage.getInstance()
								.updateComputerJobState(
										cpIdss,
										sysId,
										Constants.JOB_OFFLINE, conn);
								Engine.getInstance().startFlow(userInfo, sysname, flowNname, parameters, envVarValues, null, (String)map.get("ip"), "",false, null, null,false,0,conn,Constants.IEAI_EMERGENCY_SWITCH,Constants.IEAI_EMERGENCY_SWITCH);
								
								OperRecord or = new OperRecord();
                                or.setSysName(sysname);
                                or.setFlowName(flowNname);
                                or.setAgentIP((String)map.get("ip"));
                                or.setOpTime(System.currentTimeMillis());
                                or.setPrjType(4);
                                or.setOpcommand(flowNname);
                                or.setOperator(sessionData.getUserName());
                                listOR.add(or);
							}
						}
					}
					conn.commit();
					OperationManager.getInstance().addOperRecords(listOR,Constants.IEAI_EMERGENCY_SWITCH);
					break;
				} catch (ServerException e) {
					conn.rollback();
					_log.info("DataCenterOperationService.sysOnline is error!",e);
					if(e.getErrCode()==506){
						throw new Exception("506");
					}else{
						throw new Exception(e);
					}
				}catch (Exception e) {
					conn.rollback();
					_log.info("DataCenterOperationService.sysOnline is error!",e);
					if("506".equals(e.getMessage())){
						throw new Exception("506");
					}else{
						throw new Exception(e);
					}
				}finally{
					if(conn!=null){
						conn.close();
					}
				}
            } catch (Exception ex)
            {
            	if("506".equals(ex.getMessage())){
            		DBResource.throwRepositoryException(i, ServerError.ERR_DB_QUERY);
            	}else{
            		throw new Exception(ex);
            	}
            }
	    }
	}

	
	/**
	 * <li>Description: 根据工程名和流程类型，类获得全部上线流程 或 全部下线流程</li> 
	 * <AUTHOR>
	 * 2015-11-23 
	 * @param prjectName
	 * @param OnlineType
	 * @return
	 * @throws RepositoryException
	 * return String
	 */
	public List<DataCenterFilter> getFlowsByPrjName(String prjectName, String OnlineType)throws RepositoryException{
		HostMger hostMgr = new HostMger();
		return hostMgr.getFlowsByPrjName(prjectName, OnlineType);
	}
	private static final Logger          _log     = Logger.getLogger(HostListService.class);

}
