Ext.onReady(function () {
	Ext.define('userRolesModel', {
        extend : 'Ext.data.Model',
        fields : [ {
            name : 'iid',
            type : 'string'
        }, {
            name : 'fullName',
            type : 'string'
        }, {
            name : 'loginName',
            type : 'string'
        }, {
            name : 'locked',
            type : 'boolean'
        }]
    });
	
	var userRolesListStore = Ext.create('Ext.data.Store', {
        autoLoad : false,
        model : 'userRolesModel',
        proxy : {
            type : 'ajax',
            url : 'userdisplay.do',//需要写查询角色的集合
            reader : {
                type : 'json',
                root : 'dataList',
                totalProterty : 'total'
            }
        }
    });
	
	
	
	Ext.define ('classificationModel',{
		extend : 'Ext.data.Model',
		fields:[{
			name : 'iid',
            type : 'string'
		},{
			name : 'iclassname',
            type : 'string'
		},{
			name : 'iclassdesc',
            type : 'string'
		},{
			name : 'iparentid',
            type : 'string'
		}]
	});
	
	var classificationListstore = Ext.create ('Ext.data.TreeStore',{
		model : 'classificationModel',
		proxy : {
			type : 'ajax',
	        url : 'getcompareClassList.do'//查询分类的集合
		},
		root : {
            expanded : true,
            leaf : false
        },
        autoLoad:true
	});
	
	var userRolesGrid = Ext.create('Ext.ux.ideal.grid.Panel', {
      region : 'center',
      cls : 'customize_panel_back',
      columnLines : true,
      ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
//    selModel : Ext.create('Ext.selection.CheckboxModel', {}),
      columns : [ {
          xtype : 'rownumberer',
          width : 60,
          text : '序号'
      }, {
          xtype : 'gridcolumn',
          hidden : true,
          dataIndex : 'iid',
          text : 'iid'
      }, {
          xtype : 'gridcolumn',
          width : 120,
          flex : 2,
          dataIndex : 'fullName',
          text : '用户名',
          //renderer : function (value, p, record, rowIndex)
          //{
          //	if(value.indexOf("文件同步_")==0){
          //       value=value.substring("文件同步_".length,value.length+1).trim();
          //	}
          //   return value.trim();
          //}
      }, {
          xtype : 'gridcolumn',
          dataIndex : 'loginName',
          text : '登录ID',
          flex : 1
      }, {
          xtype : 'gridcolumn',
          width : 80,
          flex : 1,
          dataIndex : 'locked',
          text : '状态',
          renderer : function(value, p, record) {
				var backValue = "";
				if(value == false) {
					backValue = "有效";
				} else if (value == true) {
					backValue = "失效";
				}
				return backValue;
			}
      }],
      store : userRolesListStore,
      listeners : {
			itemclick : function(view, record, item, index, e, eOpts) {
				var iid = record.get('iid');
//				classificationListstore.clearFilter();
//				classificationListstore.load(
////                       uId : iid,
//				);
				Ext.Ajax.request({
                    url : 'selectIclassIds.do',
                    method : 'POST',
                    params : {
                        uId : iid,
                    },
                    success : function (response, request) {
                    	classificationGrid.getSelectionModel().deselectAll();
                        var ids=Ext.decode(response.responseText).IDS;
        				var idsArray=ids.split(",");
        				var rootnode = classificationListstore.getRootNode();
        				var child = rootnode.childNodes;
        				setSelectInfo(child,idsArray);
                    },
                    failure : function (result, request) {
                        secureFilterRs(result, '显示失败！');
                    }
                });
				
			}
		},
	});
	userRolesListStore.load();
	userRolesListStore.on ('beforeload', function (store, options){
		var qun =userRolesForm.getForm().findField("queryString").getValue();
		if('undefined'==typeof(qun)){
			qun="";
		}
		Ext.apply (userRolesListStore.proxy.extraParams, {queryCon:qun});
	});	
	var gridColumns = [{
		text : 'ID',
        dataIndex : 'iid',
        flex : 1,
        hidden : true
	},{
		xtype: 'treecolumn', 
        text : '比对分类名称',
        dataIndex : 'iclassname',
        flex : 1,
        editor :{
        	allowBlank : false
        }
	},{
		text : '比对分类描述',
        dataIndex : 'iclassdesc',
        flex : 1,
        editor :{
        	allowBlank : true
        }
	},{
		text : 'iparentid',
        dataIndex : 'iparentid',
        flex : 1,
        hidden : false        
	}];
	
	var userRolesForm = Ext.create('Ext.form.Panel', {
        region : 'north',
        border : false,
        dockedItems : [ {
            xtype : 'toolbar',
            dock : 'top',
            border : false,
            items : [ {
                xtype : 'textfield',
                fieldLabel : '',
                name : "queryString",
                width : 200,
                emptyText : '请输入用户角色名',
                listeners : {
                    specialkey : function (field, e) {
                        if (e.getKey() == Ext.EventObject.ENTER) {
                        	userRolesGrid.ipage.moveFirst();
                            userRolesListStore.load({
                                params : {
                                    queryString : userRolesForm.getForm().findField("queryString").getValue(),
                                }
                            });
                        }
                    }
                }
            }, {
                xtype : 'button',
                baseCls : 'Common_Btn',
                text : '查询',
                handler : function(){
                	userRolesListStore.load();
                
                }
            }, {
                xtype : 'button',
                baseCls : 'Common_Btn',
                text : '重置',
                handler : function () {
                	userRolesForm.getForm().findField("queryString").setValue('');
                }
            }]
        } ]
    });
	
	var classificationForm = Ext.create('Ext.form.Panel', {
        region : 'north',
        border : false,
        dockedItems : [ {
            xtype : 'toolbar',
            dock : 'top',
            border : false,
            items : [ {
                xtype : 'textfield',
                fieldLabel : '',
                name : "queryString",
                width : 200,
                emptyText : '请输入要查询的分类名称',
                listeners : {
                    specialkey : function (field, e) {
                        if (e.getKey() == Ext.EventObject.ENTER) {
                        	classificationGrid.ipage.moveFirst();
                        	classificationListstore.load({
                                params : {
                                    queryString : classificationForm.getForm().findField("queryString").getValue(),
                                }
                            });
                        }
                    }
                }
            }, {
                xtype : 'button',
                baseCls : 'Common_Btn',
                text : '查询',
                handler : function(){
                	classificationListstore.load();
                }
            }, {
                xtype : 'button',
                baseCls : 'Common_Btn',
                text : '重置',
                handler : function () {
                	classificationForm.getForm().findField("queryString").setValue('');
                }
            }, {
                xtype : 'button',
                baseCls : 'Common_Btn',
                text : '保存',
                handler : function () {
                	var userRecord=userRolesGrid.getSelectionModel().getSelection();
                	console.log(userRecord);
                	var classRecord=classificationGrid.getSelectionModel().getSelection();
			        if(userRecord.length==0){
			        	Ext.Msg.alert('提示', "请先点击您要操作的用户!");
			        	return;
			        }
			        if(classRecord.length==0){
			        	Ext.Msg.alert('提示', "请选择您要操作的分类!");
			        	return;
			        }
                	var uId=userRecord[0].get("iid");
                	var classIds="";
                	for(var i=0;i<classRecord.length;i++){
                		var classId=classRecord[i].get("iid");
                		if(i==0){
                			classIds+=classId;
                		}else{
                			classIds+=","+classId;
                		}
                	}
                	Ext.Ajax.request({
						url : 'saveRoleEmpowerment.do',
						params : {
							uId : uId,
							classIds : classIds
							},
							method : 'POST',
							success : function(response, request) {
								var success = Ext.decode(response.responseText).success;
								var message = Ext.decode(response.responseText).message;
								if (success) {
									Ext.Msg.alert('提示', message);
//									classificationListstore.load();
								} else {
									Ext.Msg.alert('提示', message);
								}
							},
								failure : function(response, ooptions) {
									Ext.MessageBox.hide();
									Ext.Msg.alert('提示','请求超时！');
								}
							});
                }
            }]
        } ]
    });
	
	var classificationGrid = Ext.create ('Ext.tree.Panel',
			{
			    store : classificationListstore,
			    region : 'center',
			    emptyText : '<table cellpadding="0" cellspacing="0" border="0" width="100%" height="100%"><tr><td align="center" height="100%" valign="middle"><div class="form_images"></div></td></tr></table>',
			    selModel : Ext.create ('Ext.selection.CheckboxModel',{
					checkOnly : true,
					listeners : {
						selectionchange : function(selModel, selections,opts) {
						}
					}
			    }),
		        useArrows: true,  
		        rootVisible: false,  
		        columns : gridColumns,
		        border : true,
//				title : '',
//				padding:grid_space,
//				margins:grid_margin,
		        columnLines : true,
		        cls:'customize_panel_back',
		        padding : grid_space,
//		        plugins: [Ext.create('Ext.grid.plugin.CellEditing', {clicksToEdit: 2})],
				listeners:{
//					'checkChange':function(node,checked){
//								var me = this;
//								node.set("cls", ""); 
//								setPnode(node, checked,me);
//								setCnode(node, checked);
//			    	},
					select:function(rowModel, record, index, eOpts ){
						var selectInfo=[];
						var parent = record.parentNode;
						if(parent&&parent.get("depth")>0){
							selectInfo.push(parent);
							classificationGrid.getSelectionModel().select(selectInfo,true,false);
						}
//						var record=classificationGrid.getSelectionModel().getSelection();
//							var iparentid=record[0].get("iparentid");
//							var iids=[];
//							var id="";
//							while(iparentid!='0'){
//								var node=classificationListstore.getNodeById(iparentid);
//								id=node.get("iid");
//								iids.push(id);
//							    iparentid=node.get("iparentid");
//							}
//							classificationGrid.getSelectionModel().select(iids);
							
							
////						var me = this;
////						var checked=true;
////						setPnode(record, checked,me);
////						setCnode(record, checked);
//						eachByIparentID(record.get("iparentid"));
					},
					deselect:function(rowModel, record, index, eOpts ){
//						var me = this;
//						var checked=false;
//						setPnode(record, checked,me);
//						setCnode(record, checked);
						var deselect=[];
						record.eachChild(function (nodeChild) {
							deselect.push(nodeChild);
						});
						classificationGrid.getSelectionModel().deselect(deselect);
					}
//					var record=classificationGrid.getSelectionModel().getSelection();
//					for(var i=0;i<record.length;i++){
//						var iparentid=record[i].get("iparentid");
//						if(iparentid!='0'){
//							eachByIparentID(iparentid);
//						}
//						
//					}
				}
			});
			
	classificationListstore.on("load",function(obj, node, records, successful, eOpts){
		if(records==''){
			var flag = true;
			var treeViewDiv = grid_panel.body.dom.childNodes[0].childNodes;
			for(var i=0;i<treeViewDiv.length;i++){
				if(treeViewDiv[i].className=='x-grid-empty'){
					flag = false;
				}
			}
			if(flag){
				var doc = document.createRange().createContextualFragment(grid_panel.getView().emptyText);
				grid_panel.body.dom.childNodes[0].appendChild(doc);
			}
		}
	});
	
	classificationListstore.on ('beforeload', function (store, options){
		var new_params ={
				iclassname : classificationForm.getForm().findField("queryString").getValue(),	
		}
		Ext.apply (store.proxy.extraParams, new_params);
	});
	
	var userRolesPanel = Ext.create('Ext.panel.Panel', {
        region : 'west',
        layout : 'border',
        border : true,
        cls:' window_border panel_space_right customize_panel_back',
        height : contentPanel.getHeight() - modelHeigth,
        width : 2*contentPanel.getWidth()/5,
        bodyPadding : grid_space,
        split : true,
        items : [ userRolesForm, userRolesGrid ]
    });
	
	var classificationPanel = Ext.create('Ext.panel.Panel', {
        region : 'center',
        layout : 'border',
        border : true,
        cls:'window_border customize_panel_back',
        height : contentPanel.getHeight() - modelHeigth,
        width : 3*contentPanel.getWidth()/5,
        bodyPadding : grid_space,
        items : [ classificationForm, classificationGrid ]
    });

	 var mainPanel = Ext.create('Ext.panel.Panel', {
	        height : contentPanel.getHeight() - modelHeigth,
	        width : contentPanel.getWidth(),
	        layout : 'border',
	        renderTo : 'classification_div',
	        border : true,
//	        padding : grid_space,
//	        bodyPadding : grid_space,
	        split : true,
	        bodyCls: 'service_platform_bodybg',
	        cls:'customize_panel_back',
	        items : [ userRolesPanel ,classificationPanel]
	    });
	    contentPanel.on('resize', function () {
	        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
	        mainPanel.setWidth(contentPanel.getWidth());
	    });
	    
	    
//	    function setPnode(node, ischecked, me) { 
//			var parent = node.parentNode; 
//			var partial = true;
//			var selectInfo = [];
//			if (parent != null) {
//				parent.eachChild(function (parentChild) {
//					// 确定半选状态 
//					// 情况一：子节点的checked与ischecked不一致 
//					// 情况二：子节点为半选状态 parentChild.get("cls") != "" 
//					if (parentChild.get("checked") != ischecked|| parentChild.get("cls") != "") { 
//						// 半选定状态为 : 选定 + 透明度50% 
//						parent.set("cls", "partial_select"); 
////						parent.set("checked", true); 
//						selectInfo.push(parent);
//						partial = false; 
//						return false;
//					}
//				});
//				if (partial) {
//					selectInfo.push(parent);
////					parent.set("checked", ischecked); 
//					parent.set("cls", ""); 
//				} 
////				setPnode(parent, ischecked, me); 
//			}
//			classificationGrid.getSelectionModel().select(selectInfo,true,false);
//		}
//		
//	   //子节点被选中
//	  function setCnode(node, checked) {
//		  if (node) {
//			   node.set("cls", "");
//			   var selectInfo = [];
//	           node.eachChild(function (child) {
////	               child.set('checked', checked);
//	        	   selectInfo.push(child);
////	               setCnode(child, checked);
//	           });
//	           classificationGrid.getSelectionModel().select(selectInfo,true,false);
//	       }
//	   }
	   function setSelectInfo(node,ids){
		    var selectInfo = [];
			for (var i = 0; i < node.length; i++) {
				var subNode = node[i];
				for (var j = 0; j < ids.length; j++) {
					var id = ids[j];
					if(id==subNode.get("iid")){
//						subNode.set("checked",true);
//						subNode.set("isper",);
						selectInfo.push(subNode);
						break;
					}
				}
				var child = subNode.childNodes;
				if(child&&child.length>0){
					setSelectInfo(child,ids);
				}
			}
			classificationGrid.getSelectionModel().select(selectInfo,true,false);
	   }
//	   function eachByIparentID(iparentid){
//		   
////		   var iids=[];
//		   var selectInfo=[];
//		   classificationListstore.each(function(r){
//			   var iid=r.get("iid");
////			   iids.push(iid);
//			   if(iid==iparentid){
//				   selectInfo.push(r);
//				   var pId=r.get("iparentid");
//				   if(pId!=0){
//					   eachByIparentID(pId);
////					   iids = iids.concat(eachByIparentID(pId));
//				   }
//			   }
//		   });
//		   classificationGrid.getSelectionModel().select(selectInfo,true,false);
////		   return iids;
//	   }
});