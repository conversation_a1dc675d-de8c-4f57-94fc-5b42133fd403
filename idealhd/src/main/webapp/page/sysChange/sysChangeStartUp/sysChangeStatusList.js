var SysRefreshObjNotRun;
var personUsertask_window;
var switchruninfoins_store;
Ext.onReady(function () {
//清理主面板的各种监听时间
    destroyRubbish();
    var switchruninfoins_grid;
    var alarmInfoDataR_TIME = 5;
    Ext.define('switchruninfoinsData', {
        extend: 'Ext.data.Model',
        fields: [
            {name: 'iid', type: 'string'},
            {name: 'iscriptid', type: 'string'},
            {name: 'istate', type: 'string'},
            {name: 'imodelversion', type: 'string'},
            {name: 'resnum', type: 'string'},
            {name: 'icallinstancename', type: 'string'},
            {name: 'iretnvalexcepion', type: 'string'},
            {name: 'iparacheck', type: 'string'},
            {name: 'iparaswitchforce', type: 'string'},
            {name: 'istarttime', type: 'string'},
            {name: 'iendtime', type: 'string'},//
            {name: 'imodelname', type: 'string'},
            {name: 'iconner', type: 'string'},//
            {name: 'iflowid', type: 'string'},// iparaswitch
            {name: 'iconnername', type: 'string'},
            {name: 'iparaswitch', type: 'string'}
        ]
    });

    switchruninfoins_store = Ext.create('Ext.data.Store', {
        autoLoad: true,
        pageSize: 30,
        model: 'switchruninfoinsData',
        proxy: {
            type: 'ajax',
            url: 'sysChangeStart/getsysChangeStatusList.do',
            reader: {
                type: 'json',
                root: 'dataList',
                totalProperty: 'total'
            }
        }
    });

    Ext.define ('auditorModel',{
        extend : 'Ext.data.Model',
        //idProperty: 'iid',
        fields : [
            {
                name : 'iid',
                type : 'long'
            },
            {
                name : 'iname',
                type : 'string'
            }]
    });

    var iupperidQStore = Ext.create ('Ext.data.Store',{
        autoLoad : true,
        model : 'auditorModel',
        proxy :
            {
                type : 'ajax',
                url : 'sysChange/sysChangeAuditorList.do?type=0',
                reader :
                    {
                        type : 'json',
                        root : 'dataList'
                    }
            }
    });
    var switchruninfoins_columns = [
        {text: '主键', dataIndex: 'iid', hidden: true},//iflowid
        {text: '流程ID', dataIndex: 'iflowid', hidden: true},
        {text: '执行顺序', dataIndex: 'iconner', width: 100},
        {
            text: '执行状态', dataIndex: 'istate', width: 100, renderer: function (value, p, record) {
                var backValue;
                if (value == '0') {
                    backValue = "运行中";
                } else if (value == '1') {
                    backValue = "待运行";
                } else if (value == '2') {
                    backValue = "已完成";
                } else if (value == '3') {
                    backValue = "取消";
                }else if(value=='4'){
                    backValue = "暂停";
                }else if(value=='5'){
                    backValue = "终止";
                }else {
                    backValue = "待运行";
                }
                return backValue;
            }
        },
        {text: '设备数量', dataIndex: 'iconnername', width: 100},
        {text: '是否忽略异常', dataIndex: 'imodelversion', width: 100,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 0) {
                    backValue = '是';
                } else {
                    backValue = '否';
                }
                return backValue;
            }
        },
        {
            text: '服务名称/模板名称', dataIndex: 'imodelname', width: 150,
            renderer: function (value, metaData, record, rowIndex, colIndex) {
                metaData.tdAttr = 'qclass="x-tip" data-qtitle="服务名称/模板名称：" data-qwidth="200" data-qtip="' + value + '"';
                return value;
            }
        },
        {
            text: '任务名称', dataIndex: 'icallinstancename', flex: 1, width: 150,
            renderer: function (value, metaData, record, rowIndex, colIndex) {
                metaData.tdAttr = 'qclass="x-tip" data-qtitle="任务名称：" data-qwidth="200" data-qtip="' + value + '"';
                return value;
            }
        },

        {
            text: '任务类型',
            dataIndex: 'iretnvalexcepion',
            width: 110,
            renderer: function (value, p, record) {
                var backValue = "";
                if (value == 'JBFWH') {
                    backValue = '脚本服务化';
                } else if (value == 'RCCZ') {
                    backValue = '日常操作';
                } else if (value == 'USERTASK') {
                    backValue = '人工断点';
                } else {
                    backValue = '';
                }
                return backValue;
            }
        },
        {text: '单号', dataIndex: 'iparacheck', flex: 1, width: 200},
        {text: '执行人', dataIndex: 'iparaswitchforce', flex: 1, width: 200},
        {text: '执行人', dataIndex: 'iparaswitch', flex: 1,hidden: true},
        {text: '执行开始时间', dataIndex: 'istarttime', flex: 1, width: 200},
        {text: '执行结束时间', dataIndex: 'iendtime', flex: 1, width: 200},
        {
            text: '操作',
            dataIndex: 'sysOperation',
            width: 160,
            renderer: function (value, p, record) {
                var iflowid = record.get('iflowid');
                var taskName = record.get('icallinstancename');
                var iparaswitch = record.get('iparaswitch');
                var cistate = record.get('istate');
                var iretnvalexcepion = record.get('iretnvalexcepion');
                var imess = '提醒任务信息';
                var iid =  record.get('iid')
                if(userCode==iparaswitch&&''!=iflowid){
                    var chtml = '';
                    if(iretnvalexcepion == 'USERTASK'){
                        if(cistate==0){
                            chtml = '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="openUTWindows(' + iflowid + ',\'' + iid +'\',\'' + imess +'\')">&nbsp;人工处理</a>' + '&nbsp;&nbsp;</span>';
                            chtml+='<span class="switch_span"><a href="javascript:void(0)" onclick="CellSysChange('+iflowid+','+cistate+')">&nbsp;终止</a>&nbsp;&nbsp;</span>';
                        }
                    }else{
                        chtml = '<span class="switch_span">' + '<a href="javascript:void(0)" onclick="dmShowTaskDetileRunning(' + iflowid + ',\'' + taskName +'\')">&nbsp;任务详情</a>' + '&nbsp;&nbsp;</span>';
                        if(cistate==0){
                            chtml+='<span class="switch_span"><a href="javascript:void(0)" onclick="CellSysChange('+iflowid+','+cistate+')">&nbsp;终止</a>&nbsp;&nbsp;</span>';
                        }else{
                            chtml += '<span class="switch_span"><a href="javascript:void(0)" style="color: #c0c0c0;">&nbsp;终止&nbsp;&nbsp;</a></span>';
                        }
                    }
                    return chtml;
                }else {
                    if(iretnvalexcepion == 'USERTASK'){
                        var chtml = '<span class="switch_span">' + '<a href="javascript:void(0)" style="color: #c0c0c0;">&nbsp;人工处理</a>' + '&nbsp;&nbsp;</span>';
                        chtml += '<span class="switch_span"><a href="javascript:void(0)" style="color: #c0c0c0;">&nbsp;终止&nbsp;&nbsp;</a></span>';
                        return chtml;
                    }else{
                        var chtml = '<span class="switch_span">' + '<a href="javascript:void(0)" style="color: #c0c0c0;">&nbsp;任务详情</a>' + '&nbsp;&nbsp;</span>';
                        chtml += '<span class="switch_span"><a href="javascript:void(0)" style="color: #c0c0c0;">&nbsp;终止&nbsp;&nbsp;</a></span>';
                        return chtml;
                    }

                }

            }
        }
    ];
    var isysIdCode = Ext.create('Ext.form.TextField', {
        fieldLabel: "单号",
        width: 260,
        typeAhead: true,
        triggerAction: 'all',
        labelWidth: 65,
        labelAlign: 'right',
        xtype: 'textfield',
        queryMode: 'local',
        padding: '0 10 0 -25',
        maxLength: 25,
        maxLengthText: '',
        enforceMaxLength: true// 是否强制限制字符串长度，超过最大长度将不能再被输入

    });
    var	SysFreshFormNotRun = Ext.create('Ext.form.Panel', {
        frame : true,
        border : false,
        bodyCls:'fm-spinner',
        layout : {
            type : 'hbox',
            align : 'middle'
        },
        defaults : {
            anchor : '100%'
        },
        items : [{
            fieldLabel : '自动刷新(秒)',
            xtype : 'checkbox',
            name : 'dmIsAutoRefreshNotRun',
            id : 'dmIsAutoRefreshNotRun',
            padding : '0 0 10 0',
            checked : false,
            labelWidth : 90,
            handler : function() {
                if (!SysFreshFormNotRun.getForm().isValid()) {
                    Ext.Msg.alert('提示', "自动刷新时间不符合要求!");
                    return;
                }
                if(SysRefreshObjNotRun){
                    clearInterval(SysRefreshObjNotRun);
                }
                if (this.value == true) {
                    var refreshTimeNotRun = SysFreshFormNotRun.getForm().findField("dmRefreshTimeNotRun").getValue();
                    SysRefreshObjNotRun= setInterval(refresh, parseInt(refreshTimeNotRun)*1000);
                    refresh();
                } else {
                    refresh();
                }
            }
        },{
                fieldLabel : '  ',
                hideLabel : true,
                labelWidth : 60,
                width : 50,
                name : 'dmRefreshTimeNotRun',
                padding : '0 20 0 0',
                id : 'dmRefreshTimeNotRun',
                value : '10',
                editable : true,
                xtype : 'numberfield',
                allowDecimals : true,
                step : 1,
                minValue : 5,
                maxValue : 1000
            }, {
                xtype:'button',
                cls: 'Common_Btn Monitor_Btn',
                textWidth:200,
                textAlign: 'center',
                text: '刷新',
                handler: resetWhere
            } ]
    });
    var iPreActName = Ext.create('Ext.form.TextField', {
        fieldLabel: "任务名称",
        width: 260,
        maxLength: 1000,
        typeAhead: true,
        triggerAction: 'all',
        labelWidth: 65,
        labelAlign: 'right',
        xtype: 'textfield',
        queryMode: 'local',
        padding: '0 10 0 5',
        maxLength: 25,
        maxLengthText: '',
        enforceMaxLength: true// 是否强制限制字符串长度，超过最大长度将不能再被输入
    });

    var iscenename = Ext.create ('Ext.form.ComboBox',{
        emptyText : '--请选择执行人--',
        labelWidth : 65,
        labelAlign : 'right',
        store : iupperidQStore,
        width : 260,
        fieldLabel: '执行人',
        displayField : 'iname',
        valueField : 'iid',
        name : 'iupperid',
        queryMode : 'local',
        padding : '5 10 0 5',
        anyMatch : true,
        forceSelection:true,
        listeners : {
            beforequery : function (e)
            {
                var combo = e.combo;
                if (!e.forceAll)
                {
                    var value = e.query;
                    combo.store.filterBy (function (record, id)
                    {
                        var text = record.get (combo.displayField);
                        return (text.toLowerCase ().indexOf (value.toLowerCase ()) != -1);
                    });
                    combo.expand ();
                    return false;
                }
            }
        }
    });

    switchruninfoins_store.on('beforeload', function (store, options) {
        var new_params = {
            iversionalias: isysIdCode.getValue(),
            iPreActName: iPreActName.getValue(),
            userName:iscenename.getValue()
        }
        Ext.apply(switchruninfoins_store.proxy.extraParams, new_params);
    });
    // 查询
    var queryButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        textAlign: 'center',
        text: '查询',
        handler: queryWhereList
    });
    // 终止
    var CellButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        textAlign: 'center',
        id:'CellButton',
        text: '终止',
        handler: CellSysChange
    });
    // 重置
    var resetButton = Ext.create("Ext.Button", {
        cls: 'Common_Btn',
        textAlign: 'center',
        text: '重置',
        handler: resetWhere
    });
    var selModel = Ext.create('Ext.selection.CheckboxModel', {
        checkOnly: true
    });
    // switchruninfoins_grid = Ext.create('Ext.ux.ideal.grid.Panel', {
    //     region: 'center',
    //     store: switchruninfoins_store,
    //     border: true,
    //     cls: 'customize_panel_back',
    //     columnLines: true,
    //     columns: switchruninfoins_columns,
    //     dockedItems: [{
    //         xtype: 'toolbar',
    //         items: [isysIdCode,iPreActName,iscenename,queryButton,resetButton]
    //     }],
    //     ipageBaseCls : Ext.baseCSSPrefix + 'toolbar customize_toolbar',
    //     ipageItems : [SysFreshFormNotRun],
    //     selModel: Ext.create('Ext.selection.CheckboxModel', {
    //         injectCheckbox: 1,
    //         //checkbox位于哪一列，默认值为0
    //         mode: "multi",
    //         //multi,simple,single；默认为多选multi
    //         checkOnly: false,
    //         //如果值为true，则只用点击checkbox列才能选中此条记录
    //         allowDeselect: true,
    //         //如果值true，并且mode值为单选（single）时，可以通过点击checkbox取消对其的选择
    //         enableKeyNav: true
    //     }),
    //     listeners:{
    //         'select':function (){
    //             var record=switchruninfoins_grid.getSelectionModel().getSelection();
    //             var isUser=true;
    //             for(var i=0;i<record.length;i++){
    //                 if(userCode!=record[i].data.iparaswitch){
    //                     isUser=false;
    //                 }
    //             }
    //             if(isUser){
    //                 Ext.getCmp('CellButton').enable();
    //             }else {
    //                 Ext.getCmp('CellButton').disable();
    //             }
    //         },
    //         'deselect':function (){
    //             Ext.getCmp('CellButton').disable();
    //         }
    //     }
    // });
    // 刷新时间输入框
	autoRefreshTime = Ext.create('Ext.form.NumberField',
		{
			fieldLabel: '刷新时间（秒）',
			margin: '5',
			labelWidth: 110,
			width: 170,
			value: '60',
			allowDecimals: false,
			minValue: 5,
			listeners:
			{
				blur: function() {
					refreshTime = this.getValue();
					refreshTime = (refreshTime == '' || refreshTime == null) ? alarmInfoDataR_TIME : refreshTime;
					try {
						refreshTime = refreshTime < alarmInfoDataR_TIME ? alarmInfoDataR_TIME : refreshTime;
						this.setValue(refreshTime);
					}
					catch (e) {
						refreshTime = alarmInfoDataR_TIME;
						this.setValue(refreshTime);
					}
					if (autoRefreshCheck.checked) {
						clearInterval(refreshObj);
						refreshObj = setInterval(queryWhereList, refreshTime * 1000);
					}
				}
			}
		});
	// 是否自动刷新复选框
	autoRefreshCheck = Ext.create('Ext.form.Checkbox',
		{
			fieldLabel: '自动刷新',
			margin: '5',
			labelWidth: 70,
			width: 90,
			checked: true,
			listeners:
			{
				change: function() {
					if (this.checked) {
						refreshTime = autoRefreshTime.getValue();
						refreshTime = refreshTime == '' ? alarmInfoDataR_TIME : refreshTime;
						try {
							refreshTime = refreshTime < alarmInfoDataR_TIME ? alarmInfoDataR_TIME : refreshTime;
							autoRefreshTime.setValue(refreshTime);
						}
						catch (e) {
							refreshTime = alarmInfoDataR_TIME;
							autoRefreshTime.setValue(refreshTime);
						}
						clearInterval(refreshObj);
						refreshObj = setInterval(queryWhereList, refreshTime * 1000);
					}
					else {
						clearInterval(refreshObj);
					}
				}
			}
		});
	//首次加载定时刷新
	if (autoRefreshCheck.checked == true) {
		refreshObj = setInterval(queryWhereList, autoRefreshTime.getValue() * 1000);
	}
	/** 刷新按钮* */
	refreshButton = Ext.create("Ext.Button",
		{
			text: '刷新',
			// width : 70,
			//height : 22,
			textAlign: 'center',
			cls: 'Common_Btn',
			listeners: {
				"click": function() {
					queryWhereList();
				}
			}
		});
    //  自动刷新区域
	var dmFreshFormNotRun = Ext.create('Ext.form.Panel', {
		frame: true,
		border: false,
		bodyCls: 'fm-spinner',
		layout: {
			type: 'hbox',
			align: 'middle'
		},
		defaults: {
			anchor: '100%'
		},
		items: [autoRefreshTime,refreshButton]
	});
	
    switchruninfoins_grid = Ext.create('Ext.ux.ideal.grid.Panel',
        {
            store: switchruninfoins_store,
            border: true,
            region: 'center',
            cls: 'customize_panel_back',
            forceFit: true,
            autoScroll: false,
            columnLines: true,
            cellTip: true,
            ipageSize: 30,
            ipageBaseCls: Ext.baseCSSPrefix + 'toolbar customize_toolbar',
            loadMask: {
                msg: ' 数据加载中，请稍等 ',
                removeMask: true
            },
            columns: switchruninfoins_columns,
            selModel: Ext.create('Ext.selection.CheckboxModel', {
                injectCheckbox: 1,
                //checkbox位于哪一列，默认值为0
                mode: "multi",
                //multi,simple,single；默认为多选multi
                checkOnly: false,
                //如果值为true，则只用点击checkbox列才能选中此条记录
                allowDeselect: true,
                //如果值true，并且mode值为单选（single）时，可以通过点击checkbox取消对其的选择
                enableKeyNav: true
            }),
            listeners:{
                'select':function (){
                    var record=switchruninfoins_grid.getSelectionModel().getSelection();
                    var isUser=true;
                    for(var i=0;i<record.length;i++){
                        if(userCode!=record[i].data.iparaswitch){
                            isUser=false;
                        }
                    }
                    if(isUser){
                        Ext.getCmp('CellButton').enable();
                    }else {
                        Ext.getCmp('CellButton').disable();
                    }
                },
                'deselect':function (){
                    Ext.getCmp('CellButton').disable();
                }
            },
            ipageItems: [dmFreshFormNotRun]
        });
    var compareClassFrom = Ext.create('Ext.form.FormPanel', {
        region: 'north',
        border: false,
        bodyCls: 'x-docked-noborder-top',
        baseCls: 'customize_gray_back',
        items: [{
            baseCls: 'customize_gray_back',
            xtype: 'toolbar',
            dock: 'top',
            items: [isysIdCode,iPreActName,iscenename,queryButton,resetButton]
        }
        ]
    });

    var mainPanel = Ext.create('Ext.panel.Panel', {
        renderTo: "syschangehistorydetail_area",
        width: contentPanel.getWidth(),
        height: contentPanel.getHeight() - modelHeigth,
        bodyPadding: grid_margin,
        bodyCls: 'service_platform_bodybg',
        border: true,
        layout: 'border',
//        bodyPadding : 5,
        items: [compareClassFrom,switchruninfoins_grid]
    });
    /** 窗口尺寸调节* */
    contentPanel.on('resize', function () {
        mainPanel.setWidth(contentPanel.getWidth());
        mainPanel.setHeight(contentPanel.getHeight() - modelHeigth);
    });
    // 当页面即将离开的时候清理掉自身页面生成的组建
    contentPanel.getLoader().on("beforeload", function (obj, options, eOpts) {
        Ext.destroy(mainPanel);
        if (Ext.isIE) {
            CollectGarbage();
        }
    });

    /* 解决IE下trim问题 */
    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    function queryWhereList(){
		if (Ext.isIE) {
			CollectGarbage();
		}
		switchruninfoins_grid.ipage.moveFirst();
    }
    
    function refresh(){
        switchruninfoins_store.load();
    }
    
    function resetWhere(){
        isysIdCode.setValue('');
        iPreActName.setValue('');
        iscenename.setValue('');
        switchruninfoins_store.load();
    }
});

function CellSysChange(iflowid,cistate){
    Ext.MessageBox.buttonText.yes = "确定";
	Ext.MessageBox.buttonText.no = "取消";
	Ext.Msg.confirm("确认终止", "确认终止任务", function(id) {
		if (id == 'yes'){
			//终止任务
		    if(cistate!=0){
		        Ext.Msg.alert('提示', "该状态任务不可终止");
		        return;
		    }
		    Ext.Ajax.request ({
		        url : 'sysChangeStart/killRunTask.do',
		        params :
		            {
		                ids: iflowid
		            },
		        success : function (response, opts)
		        {
		            var success = Ext.decode (response.responseText).success;
		            var message = Ext.decode (response.responseText).message;
		            if (success) {
		                Ext.Msg.alert('提示', '终止成功');
		                switchruninfoins_store.load();
		            } else {
		                Ext.Msg.alert('提示', message);
		            }
		        },
		        failure : function (result, request)
		        {
		            secureFilterRs(result,"请求返回失败！",request);
		        }
		    });
		}
	});
}
function dmShowTaskDetileRunning( itaskid,itaskName) {
    var	urlString = "sysChangeStart/showTaskDetileRunning.do";
    var	titleString = "任务详情-【" + itaskName + "】";
    var listAddRecord = new Array();
    listAddRecord.push(itaskName);
    Ext.encode(listAddRecord);
    if (messageWindow == undefined || !messageWindow.isVisible ())
    {
        messageWindow = Ext.create ('Ext.window.Window',
            {
                title :  titleString,
                modal : true,
                closeAction : 'destroy',
                constrain : true,
//		    autoScroll : true,
                autoScroll : false,
                width : contentPanel.getWidth (),
                height : contentPanel.getHeight ()+50 ,
                draggable : false,// 禁止拖动
                resizable : false,// 禁止缩放
                layout : 'fit',
                loader :
                    {
                        url : urlString,
                        autoLoad : true,
                        scripts : true,
                        params :
                            {
                                itaskid : itaskid,
                                itaskName : itaskName,
                                isendid:1,
                                jsonDate:listAddRecord,
                                contentPanelHeight : contentPanel.getHeight(),
                                windowScHeight : window.screen.height
                            }
                    },
                   listeners:{
                  		beforeclose:function(){
                      		contentPanel.getHeader().show();//设置contentPanel标题头隐藏
                 	 	}
            		}
            })
    }
    messageWindow.show ();
}
function  openUTWindows(iflowid,iruninfoinsid,ireminfo){
    personUsertask_window = Ext.create('Ext.window.Window', {
        title : '人工处理',
        autoScroll : true,
        modal : true,
        closeAction : 'destroy',
        buttonAlign : 'center',
        draggable : false,// 禁止拖动
        resizable : false,// 禁止缩放
        width : 600,
        height : 350,
        loader : {
            url : "sysChangeStart/switchUTForTL.do",
            // url : "switchUTForTopo.do",
            params : {
                iflowid : iflowid,
                iruninfoinsid : iruninfoinsid
            },
            autoLoad : true,
            autoDestroy : true,
            scripts : true
        },
        listeners:{
            'destroy':function(){
                var task = new Ext.util.DelayedTask(function(){
                    switchruninfoins_store.load({callback:mainLoadAfter});
                });
                task.delay(1000);
            }
        }
    }).show();
}
/** 提醒任务处理后刷新当前页面状态* */
function reloadGroup() {
    setTimeout(reload, 1500);
}
