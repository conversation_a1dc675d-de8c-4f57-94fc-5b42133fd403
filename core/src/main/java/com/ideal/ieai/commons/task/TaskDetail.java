package com.ideal.ieai.commons.task;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.ideal.ieai.commons.UserInfo;

public class TaskDetail implements Serializable {
	public int hashCode() {
		return new HashCodeBuilder().append(taskId).toHashCode();
	}

	public boolean equals(Object obj) {
		if (!(obj instanceof TaskDetail)) {
			return false;
		}
		TaskDetail that = (TaskDetail) obj;
		return new EqualsBuilder().append(taskId, that.getTaskId()).isEquals();
	}

	public String getAlertInterval() {
		return alertInterval;
	}

	public boolean isDelegateToParticipantOnly() {
		return delegateToParticipantOnly;
	}

	public boolean isTaskEnd() {
		return taskEnd;
	}

	public Date getLastAlertTime() {
		return lastAlertTime;
	}

	public List getHistory() {
		return history;
	}

	public Date getDelegateTime() {
		return delegateTime;
	}

	public String getState() {
		return state;
	}

	public String getDescription() {
		return description;
	}

	public List getParticipants() {
		return participants;
	}

	public Date getDeadline() {
		return deadline;
	}

	public Date getAcquireTime() {
		return acquireTime;
	}

	public Date getForwardTime() {
		return forwardTime;
	}

	public boolean isForwardToParticipantOnly() {
		return forwardToParticipantOnly;
	}

	public UserInfo getForwardUser() {
		return forwardUser;
	}

	public Long getFlowId() {
		return flowId;
	}

	public String getTaskName() {
		return taskName;
	}

	public long getAlertTime() {
		return alertTime;
	}

	public long getTaskId() {
		return taskId;
	}

	public List getParticipantGroups() {
		return participantGroups;
	}

	public boolean isUseFlowPart() {
		return useFlowPart;
	}

	public List getOperations() {
		return operations;
	}

	public long getPriority() {
		return priority;
	}

	public UserInfo getTaskOwner() {
		return taskOwner;
	}

	public boolean isAllowSkip() {
		return allowSkip;
	}

	public Date getCompleteTime() {
		return completeTime;
	}

	public UserInfo getDelegateUser() {
		return delegateUser;
	}

	public boolean isAcquired() {
		return acquired;
	}

	public int getAlertType() {
		return alertType;
	}

	public void setReadyTime(Date readyTime) {
		this.readyTime = readyTime;
	}

	public void setAlertInterval(String alertInterval) {
		this.alertInterval = alertInterval;
	}

	public void setDelegateToParticipantOnly(boolean delegateToParticipantOnly) {
		this.delegateToParticipantOnly = delegateToParticipantOnly;
	}

	public void setTaskEnd(boolean taskEnd) {
		this.taskEnd = taskEnd;
	}

	public void setLastAlertTime(Date lastAlertTime) {
		this.lastAlertTime = lastAlertTime;
	}

	public void setHistory(List history) {
		this.history = history;
	}

	public void setDelegateTime(Date delegateTime) {
		this.delegateTime = delegateTime;
	}

	public void setState(String state) {
		this.state = state;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public void setParticipants(List participants) {
		this.participants = participants;
	}

	public void setDeadline(Date deadline) {
		this.deadline = deadline;
	}

	public void setAcquireTime(Date acquireTime) {
		this.acquireTime = acquireTime;
	}

	public void setForwardTime(Date forwardTime) {
		this.forwardTime = forwardTime;
	}

	public void setForwardToParticipantOnly(boolean forwardToParticipantOnly) {
		this.forwardToParticipantOnly = forwardToParticipantOnly;
	}

	public void setForwardUser(UserInfo forwardUser) {
		this.forwardUser = forwardUser;
	}

	public void setFlowId(Long flowId) {
		this.flowId = flowId;
	}

	public void setTaskName(String taskName) {
		this.taskName = taskName;
	}

	public void setAlertTime(long alertTime) {
		this.alertTime = alertTime;
	}

	public void setTaskId(long taskId) {
		this.taskId = taskId;
	}

	public void setParticipantGroups(List participantGroups) {
		this.participantGroups = participantGroups;
	}

	public void setUseFlowPart(boolean useFlowPart) {
		this.useFlowPart = useFlowPart;
	}

	public void setOperations(List operations) {
		this.operations = operations;
	}

	public void setPriority(long priority) {
		this.priority = priority;
	}

	public void setTaskOwner(UserInfo taskOwner) {
		this.taskOwner = taskOwner;
	}

	public void setAllowSkip(boolean allowSkip) {
		this.allowSkip = allowSkip;
	}

	public void setCompleteTime(Date completeTime) {
		this.completeTime = completeTime;
	}

	public void setDelegateUser(UserInfo delegateUser) {
		this.delegateUser = delegateUser;
	}

	public void setAcquired(boolean acquired) {
		this.acquired = acquired;
	}

	public void setAlertType(int alertType) {
		this.alertType = alertType;
	}

	public void setTaskDelegateRecord(TaskDelegationRecord taskDelegateRecord) {
		this.taskDelegateRecord = taskDelegateRecord;
	}

	public Date getReadyTime() {
		return readyTime;
	}

	public TaskDelegationRecord getTaskDelegateRecord() {
		return taskDelegateRecord;
	}

	private void readObject(ObjectInputStream ois)
			throws ClassNotFoundException, IOException {
		ois.defaultReadObject();
	}

	private void writeObject(ObjectOutputStream oos) throws IOException {
		oos.defaultWriteObject();
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * @return Returns the isRecovered.
	 */
	public boolean isRecovered() {
		return isRecovered;
	}

	/**
	 * @param isRecovered
	 *            The isRecovered to set.
	 */
	public void setRecovered(boolean isRecovered) {
		this.isRecovered = isRecovered;
	}

	public TaskDetail() {
	}

	public TaskDetail(Long id, String name) {
		taskId = id.longValue();
		taskName = name;
	}

	private List operations;

	private List participants;

	private List participantGroups;

	/**
	 * Unique Id allocated for task. The Id is unique in iEAI server domain
	 */
	private long taskId;

	private String taskName;

	private String description;

	private String remark;

	/**
	 * priority 1..5, trival, low, normal, high, urgent
	 */
	private long priority;

	/**
	 * Time when task must be finished
	 */
	private java.util.Date deadline;

	/**
	 * Time when task was started
	 */
	private java.util.Date readyTime;
	/**
	 * Time when task was waiting
	 */
	private java.util.Date waitingTime;

	/**
	 * Time that task was completed( the last action was performed on current
	 * task )
	 */
	private java.util.Date completeTime;

	/**
	 * Time when task was forwarded to current user
	 */

	private java.util.Date forwardTime;

	/**
	 * Time when task was delegated to current user
	 */
	private java.util.Date delegateTime;

	/**
	 * User who owns the task. User owns a task by:
	 * <li>acquite it explicitly
	 * <li>be forwarded to by task's previous owner
	 * <li>be delegated to by task's previous owner
	 * <li>be assigned to by task creator or super user
	 */
	private UserInfo taskOwner;

	/**
	 * User who forwarded the task to current user
	 */
	private UserInfo forwardUser;

	/**
	 * User who delegate the task to current user
	 */
	private UserInfo delegateUser;

	/**
	 * Task can only be forwarded to uers in participant and part group list
	 */
	private boolean delegateToParticipantOnly;

	/**
	 * Task can only be forwarded to user in participant and part group list
	 */
	private boolean forwardToParticipantOnly;

	/**
	 * Task history information
	 */
	private List history;

	/**
	 * Work done by the delegated user
	 */
	private TaskDelegationRecord taskDelegateRecord;

	private java.util.Date acquireTime;

	/**
	 * How many seconds before reaching task's deadline, should an alert be
	 * reaised to corresponding participant(s). <br>
	 * This field stores value in seconds. <br>
	 * e.g.:
	 * <ul>
	 * <li>3600: Alert one hour before task reaches deadline
	 * <li>900: Alert 15 minuts ahead of deadline
	 * <li>86400(=24x3600):One daye before the deadline
	 * </ul>
	 * Default is 15 minutes before task is due
	 */
	private long alertTime = 15 * 60l;

	/**
	 * Record when the last alert was send. Null if no alert has been sent yet.
	 * System send alert by sending out email to correponding participant(s).
	 * <br>
	 * This fied stores absolute time when the last alert was sent. <br>
	 * Need to store this value because system might need to repeatedly alert
	 * user. For example, when system detected one task must be fulfilled in 1
	 * day. It might send one message every hour until the task is done.
	 */
	private java.util.Date lastAlertTime;

	/**
	 * Type of alert
	 */
	private int alertType;

	/**
	 * How many seconds will an alert be sent to user.
	 */
	private String alertInterval; // "":<month>:<day>:<hour>:<minute>:<second>

	private boolean allowSkip;

	private boolean taskEnd;

	private boolean acquired;

	private String state;

	private Long flowId;

	/**
	 * if use participant information defined in job
	 */
	private boolean useFlowPart;

	/**
	 * if the flow is recovered.
	 */
	private boolean isRecovered = false;

	private Date _failBeginTime;

	private Date _failEndTime;

	private List _properties;

	private List _items;

    private Map _validTimeoutRules;
    
    //changeFlow info
    private int _flowState;
    private long _delayTime;
    private int _ignore;
    private int _actChType;
    
    
	public long getDelayTime() {
		return _delayTime;
	}

	public void setDelayTime(long time) {
		_delayTime = time;
	}

	public int getFlowState() {
		return _flowState;
	}

	public void setFlowState(int state) {
		_flowState = state;
	}

	public int getIgnore() {
		return _ignore;
	}

	public void setIgnore(int _ignore) {
		this._ignore = _ignore;
	}

	public int getActChType() {
		return _actChType;
	}

	public void setActChType(int actChType) {
		this._actChType = actChType;
	}

	public List getItems() {
		return _items;
	}

	public void setItems(List items) {
		_items = items;
	}

	public List getProperties() {
		return _properties;
	}

	public void setProperties(List propertiess) {
		_properties = propertiess;
	}

	/**
	 * @return the failBeginTime
	 */
	public Date getFailBeginTime() {
		return _failBeginTime;
	}

	/**
	 * @param failBeginTime the failBeginTime to set
	 */
	public void setFailBeginTime(Date failBeginTime) {
		_failBeginTime = failBeginTime;
	}

	/**
	 * @return the failEndTime
	 */
	public Date getFailEndTime() {
		return _failEndTime;
	}

	/**
	 * @param failEndTime the failEndTime to set
	 */
	public void setFailEndTime(Date failEndTime) {
		_failEndTime = failEndTime;
	}

	public java.util.Date getWaitingTime() {
		return waitingTime;
	}

	public void setWaitingTime(java.util.Date waitingTime) {
		this.waitingTime = waitingTime;
	}

    public void setValidTimeoutRules ( Map validTimeoutRules )
    {
        this._validTimeoutRules=validTimeoutRules;
        
    }

    public Map getValidTimeoutRules ()
    {
        return _validTimeoutRules;
    }
    private java.lang.String IisIgnore;

	public java.lang.String getIisIgnore() {
		return IisIgnore;
	}

	public void setIisIgnore(java.lang.String iisIgnore) {
		IisIgnore = iisIgnore;
	}
}
