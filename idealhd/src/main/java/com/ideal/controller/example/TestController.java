package com.ideal.controller.example;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.ideal.ieai.server.repository.hd.platform.collect.CollectEnvironmentBean;
import com.ideal.util.ImportUtil;

@Controller
public class TestController {

	@RequestMapping("test.do")
	public String test(){
		return "example/test";
	}
	
	@RequestMapping("queryData.do")
	@ResponseBody
	public List<DataModel> queryData(){
		
		Map<String,List<DataModel>> map = new HashMap<String,List<DataModel>>();
		List<DataModel> list = new ArrayList<DataModel>();
		
		DataModel dataModel1 = new DataModel();
		dataModel1.setName("丁涛");
		dataModel1.setEmail("<EMAIL>");
		dataModel1.setPhone("18600000000");
		list.add(dataModel1);
		
		DataModel dataModel2 = new DataModel();
		dataModel2.setName("石一");
		dataModel2.setEmail("<EMAIL>");
		dataModel2.setPhone("18600000000");
		list.add(dataModel2);
		
		DataModel dataModel3 = new DataModel();
		dataModel3.setName("宫喜斌");
		dataModel3.setEmail("<EMAIL>");
		dataModel3.setPhone("18600000000");
		list.add(dataModel3);
		
		map.put("dataList", list);
		
		return list;
	}
	
	/**
     * 测试导入解析 单页excel 方法
     * @Title: ImportTest   
     * @Description: 
     * @return      
     * @author: gang_wang 
     * @date:   2019年12月16日 下午3:43:02
     */
    @RequestMapping("ImportTest.do")
    @ResponseBody
    public Map<String,Object> ImportTest(@RequestParam("fileName") CommonsMultipartFile file,HttpServletRequest request,HttpServletResponse response){
        String [][] ss = {{"iid","^-?\\d+$"},{"iname",""},{"ip",""}};
        InputStream fis;
        try
        {
            fis = file.getInputStream();
            Map map = ImportUtil.getInstance().importOnePageExcel(fis, file.getFileItem().getName(),ss, CollectEnvironmentBean.class, "");
            return map;
        } catch (IOException e)
        {
        }
        return null;
    }
	
}
