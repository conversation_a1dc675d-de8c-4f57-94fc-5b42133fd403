#!/bin/sh

#	########################################################################################
#	Description:
#					This program is aoms system script for collect OS config infomation
#					
#	Author:
#					zhaojh	
#	Date:
#					2018.11.12.14
#	########################################################################################
#

#	########################################################################################
#	funcion get_linux_os_version
#	########################################################################################
#
function get_linux_os_version {
  cat /proc/version|grep -i suse >/dev/null
  if [ $? -eq 0 ];then
    OS_type=SUSE
        OS_version=` cat /etc/issue|grep SUSE|awk '{print $3" "$4" "$5" "$6" "$7" "$8}'`
        if [ "${is_HA}" == "true" ];then
          duble_host=`crm_mon -1|grep Online|awk '{print $3";"$4}'`
          duble_host=${duble_host}\;
        fi
  else
    if [ `cat /etc/redhat-release|grep -ic centOS` -gt 0 ];then
          OS_type=CentOS
          OS_version=`cat /etc/redhat-release|awk '{print $1$4}'`
    else
        if [ `cat /etc/redhat-release|grep -ic "red hat"` -gt 0 ];then
            OS_type=RedHat
            OS_version=`cat /etc/redhat-release`
            if [ `cat /etc/system-release|grep -ic "Oracle Linux"` -gt 0 ];then
                OS_type=OracleLinux
                OS_version=`cat /etc/system-release`
  #                              else
  #          OS_type='get os type error'
  #              OS_version='get os version error'
            fi
        fi
    fi
  fi
}
#	########################################################################################
#	Main program
#	########################################################################################

# for base infomation
host_name=""
OS_type=""


osType=`uname | tr '[a-z]' '[A-Z]'`
case ${osType} in
	LINUX)
		  get_linux_os_version
		  
	     ;;
	HP-UX)
		  OS_type=HP-UX
		  
	     ;;
	AIX)
		  OS_type=AIX
		  
	     ;;		 
	*)
		echo "get os type failed!"
		exit
esac

echo os.type=${OS_type}
