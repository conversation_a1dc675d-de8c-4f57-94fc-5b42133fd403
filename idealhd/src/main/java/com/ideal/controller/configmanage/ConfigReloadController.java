package com.ideal.controller.configmanage;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.service.configmanage.ConfigReloadService;

@Controller
public class ConfigReloadController
{
    private static final Logger _log = Logger.getLogger(ConfigReloadController.class);

    @RequestMapping("configReloadView.do")
    public String ftpinfoindex ( HttpServletRequest request )
    {

        return "configmanage/configReloadView";
    }

    @RequestMapping("reloadConfig.do")
    @ResponseBody
    public Map<String,Object> reloadConfig ( HttpServletRequest request )
    {
        Map map = new HashMap();
        boolean flag = ConfigReloadService.getInstance().reloadConfig();

        if (flag)
        {
            map.put("success", true);
            map.put("message", "重新加载开关配置成功");
        } else
        {
            map.put("success", false);
            map.put("message", "重新加载开关配置失败");
        }

        return map;
    }
}
