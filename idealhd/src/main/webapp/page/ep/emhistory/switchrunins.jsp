<%@page contentType="text/html; charset=utf-8"%>
<%@ page import="com.ideal.ieai.core.Environment"%>
<%
	//单号显示开关
	boolean numShowSwitch = Environment.getInstance().getemAssociatedSwitch();
%>
<html>
<head>
	<script>
		var numShowSwitch=<%=numShowSwitch%>;

        var psbcOrderShow = <%= Environment.getInstance().getItsmOrderNumShowSwitch()%>;
        var bankSwitch='<%=Environment.getInstance().getBankSwitch()%>';
        var psbcOrderFlag=(bankSwitch==="PSBC"&&psbcOrderShow);
	</script>
	<script type="text/javascript"
			src="<%=request.getContextPath()%>/page/ep/emhistory/switchrunins.js"></script>
</head>
<body>
<div id="emhistory_switchrunins_div" style="width: 100%; height: 100%">
</div>
</body>
</html>