package com.ideal.controller.topo.monitorconfig;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ideal.common.utils.SessionData;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBException;
import com.ideal.ieai.server.repository.topo.monitorconfig.BusinessInsConfigBeanForTopo;
import com.ideal.service.topo.monitorconfig.MonitorConfigService;

@Controller
public class MonitorConfigController
{
    private static final Logger _log    = Logger.getLogger(MonitorConfigController.class);
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";

    @RequestMapping("initMonitorConfig.do")
    public String initMonitorConfig ()
    {
        return "topo/monitorconfig/monitorconfig";
    }

    MonitorConfigService monitorConfigService = new MonitorConfigService();

    /**
     * 
     * @Title: getJobList   
     * @Description: 监控项列表   
     * @param ip
     * @param page
     * @param limit
     * @param gobleScid
     * @param sysName
     * @return      
     * @author: wu_jian 
     * @date:   2018年8月29日 上午9:14:16
     */
    @RequestMapping("getMonitorConfigItemList.do")
    @ResponseBody
    public Map getMonitorConfigItemList ( String ip, String ipid, String start, String limit )
    {
        return monitorConfigService.getMonitorConfigItemList(start, limit, ip, ipid);
    }

    /**
     * 保存监控项
     * @param jsonData
     * @return
     * @throws DBException
     * @throws SQLException
     */
    @RequestMapping("saveMonitorConfigItem.do")
    @ResponseBody
    public Map saveMonitorConfigItem ( String jsonData )
    {
        return monitorConfigService.saveMonitorConfigItem(jsonData);
    }

    /**
     * 保存作业
     * @param jsonData
     * @return
     * @throws DBException
     * @throws SQLException
     */
    @RequestMapping("saveMonitorConfigJob.do")
    @ResponseBody
    public Map saveMonitorConfigJob ( String jsonData )
    {
        return monitorConfigService.saveMonitorConfigJob(jsonData);
    }

    /**
     * 
     * @Title: saveMonitorShell   
     * @Description: 修改作业
     * @param: @param jsonData
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: lichao_liu 
     * @date:   2017年9月5日 上午11:43:11
     */
    @RequestMapping("updMonitorConfigJob.do")
    @ResponseBody
    public Map updMonitorConfigJob ( String jsonStr )
    {
        return monitorConfigService.updMonitorConfigJob(jsonStr);
    }

    /**
     * 
     * @Title: saveMonitorShell   
     * @Description: 修改监控项
     * @param: @param jsonData
     * @param: @return      
     * @return: Map      
     * @throws   
     * @author: lichao_liu 
     * @date:   2017年9月5日 上午11:43:11
     */
    @RequestMapping("updMonitorConfigItem.do")
    @ResponseBody
    public Map updMonitorConfigItem ( String jsonStr )
    {
        return monitorConfigService.updMonitorConfigItem(jsonStr);
    }

    /**
     * 
     * @Title: getJobList   
     * @Description: 作业列表   
     * @param ip
     * @param page
     * @param limit
     * @param gobleScid
     * @param sysName
     * @return      
     * @author: wu_jian 
     * @date:   2018年8月29日 上午9:14:16
     */
    @RequestMapping("getJobList.do")
    @ResponseBody
    public Map getJobList ( String ip, String start, String limit, long gobleScid )
    {
        return monitorConfigService.getJobList(start, limit, ip, gobleScid);
    }

    /**
     * 删除监控项
     * @param deleteIds
     * @return
     * @throws DBException
     * @throws SQLException
     */
    @RequestMapping("deleteMonitorConfigItem.do")
    @ResponseBody
    public Map deleteMonitorConfigItem ( String iids )
    {
        return monitorConfigService.deleteMonitorConfigItem(iids);
    }

    /**
     * 删除作业
     * @param deleteIds
     * @return
     * @throws DBException
     * @throws SQLException
     */
    @RequestMapping("deleteMonitorConfigJob.do")
    @ResponseBody
    public Map deleteMonitorConfigJob ( String iids )
    {
        return monitorConfigService.deleteMonitorConfigJob(iids);
    }

    /**
     * 
     * @Title: getSysNames   
     * @Description:应用服务器列表   
     * @return
     * @throws RepositoryException      
     * @author: wu_jian 
     * @date:   2018年8月29日 上午9:00:09
     */
    @RequestMapping("getSysList.do")
    @ResponseBody
    public Map getSysList ()
    {
        return monitorConfigService.getSysnames();
    }

    /**
     * 
     * @Title: getSysServerList   
     * @Description:应用服务器列表下的  序号  ip   
     * @param sysIdForQuery
     * @return
     * @throws DBException
     * @throws SQLException      
     * @author: wu_jian 
     * @date:   2018年8月29日 上午9:07:37
     */
    @RequestMapping("getSysServerList.do")
    @ResponseBody
    public Map getSysServerList ( long sysIdForQuery )
    {
        return monitorConfigService.getSysServerList(sysIdForQuery);
    }

    /**
     * @Title: saveIntervalConfig
     * @Description: 添加监控时间
     * @param request
     * @param incdata
     * @return
     */
    @RequestMapping("saveIntervalConfig_cfg.do")
    @ResponseBody
    public Map saveIntervalConfig ( HttpServletRequest request, String incdata )
    {
        return monitorConfigService.saveintervalConfig(incdata);
    }

    /**
     * @Title: intervalConfigList
     * @Description: 监控时间列表
     * @param request
     * @param businessInsConfigBeanForQuery
     * @return
     */
    @RequestMapping("intervalConfigList_cfg.do")
    @ResponseBody
    public Object intervalConfigListCfg ( HttpServletRequest request,
            BusinessInsConfigBeanForTopo businessInsConfigBeanForTopo )
    {
        List list = null;
        try
        {
            list = monitorConfigService.intervalConfigList(businessInsConfigBeanForTopo);
        } catch (RepositoryException e)
        {
            _log.error(e);
        }
        return list;
    }

    /**
     * 
     * @Title: deleteIntervalConfig
     * @Description: 删除监控时间
     * @param request
     * @param deleteIds
     * @return
     */
    @RequestMapping("deleteIntervalConfig_cfg.do")
    @ResponseBody
    public Object deleteIntervalConfig ( HttpServletRequest request, Long[] deleteIds )
    {
        Map<String, Object> resp = new HashMap<String, Object>();
        SessionData sessionData = SessionData.getSessionData(request);
        try
        {
            boolean returnValue = monitorConfigService.deleteIntervalConfig(deleteIds, sessionData.getUserName());
            if (returnValue)
            {
                resp.put(SUCCESS, true);
                resp.put(MESSAGE, "操作执行成功！");
            } else
            {
                resp.put(SUCCESS, false);
                resp.put(MESSAGE, "执行操作失败！");
            }
        } catch (Exception e)
        {
            resp.put(SUCCESS, false);
            resp.put(MESSAGE, e.getMessage());
        }
        return resp;
    }

}
