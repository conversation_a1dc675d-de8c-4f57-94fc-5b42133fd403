var topoScreenDisplay_topoPanel;
var contentPanel="";
Ext.onReady(function() {
    if (groupName == 'null') {
        topoScreenDisplay_topoPanel = Ext.create('Ext.panel.Panel', {
            height : (document.documentElement.clientHeight)-160,
            border : false,
            loader : {
                url : 'topoScreenDisplayContentFir.do?refreshTime=' + refreshTime+'&queryFlag='+queryFlag+ "&tmp=" + new Date().getTime(),
                autoLoad : true,
                scripts : true
            }
        });
    } else {
        topoScreenDisplay_topoPanel = Ext.create('Ext.panel.Panel', {
            height : (document.documentElement.clientHeight)-160,
            border : false,
            loader : {
                url : 'topoScreenDisplayContentFir.do?groupName='
                        + encodeURI(groupName) + '&queryDate=' + queryDate
                        + '&refreshTime=' + refreshTime+'&queryFlag='+queryFlag+ "&tmp=" + new Date().getTime(),
                autoLoad : true,
                scripts : true
            }
        });
    }

    var mainPanel = Ext.create('Ext.panel.Panel', {
        border : false,
        width : document.documentElement.clientWidth,
        height : (document.documentElement.clientHeight)-160,
        items : [ topoScreenDisplay_topoPanel ],
        renderTo : "topoScreenDisplay_div"
    });
    contentPanel=topoScreenDisplay_topoPanel;
    Ext.EventManager.on(Ext.isIE ? document : window, "keydown",
            function(e, t) {
                if (e.getKey() == e.BACKSPACE) {
                    event.keyCode = 0;
                    event.cancelBubble = true;
                    return false;
                }
            });

});
