package com.ideal.ieai.server.repository.uploadrelyexcel;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ideal.ieai.commons.Constants;
import com.ideal.ieai.commons.ServerError;
import com.ideal.ieai.server.ServerException;
import com.ideal.ieai.server.repository.RepositoryException;
import com.ideal.ieai.server.repository.db.DBResource;
/**
 * 
 * <ul>
 * <li>Title: UpLoadWeightExcelManager.java</li>
 * <li>Description:迁移锦州银行userTasks作业优先级导入至主线作业调度</li>
 * <li>Copyright: Copyright 2003</li>
 * <li>Company: ideal</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 * 2018年12月13日
 */
public class UpLoadWeightExcelManager
{
    public static UpLoadWeightExcelManager getInstance ()
    {
        return _instGroup;
    }

    private UpLoadWeightExcelManager()
    {
    }

    private static final UpLoadWeightExcelManager   _instGroup  = 
            new UpLoadWeightExcelManager();
    
    private static final Logger _log = Logger.getLogger(UpLoadWeightExcelManager.class);

    /**
     * 
     * <li>Description:保存作业优先级</li> 
     * <AUTHOR>
     * 2014-2-9 
     * <AUTHOR> 2018年12月13日 迁移锦州银行userTasks作业优先级导入至主线作业调度
     * @param aibtmp
     * @return 
     * @throws ServerException
     * return int
     * @throws RepositoryException 
     */
    public String saveWeight( Map<String, ActWeightInfoBean> mapList,Connection conn ) throws ServerException, RepositoryException {
        String messages="";
        String method = "saveWeight";
        long countAct = -1;
        long countWeight = -1;
        long prjid = -1;
        long actid = -1;
        PreparedStatement ps1 = null;
        PreparedStatement ps2 = null;
        String sql1= "INSERT INTO IEAI_ACTINFO_PRIWEIGHT(IACTID,IPRI) VALUES(?, ?)";
        String sql2= "UPDATE IEAI_ACTINFO_PRIWEIGHT  SET IPRI=? WHERE IACTID = ? " ;
        try
        {
            ps1 = conn.prepareStatement(sql1);
            ps2 = conn.prepareStatement(sql2);
            if(mapList!=null)
            {
                for(ActWeightInfoBean afib : mapList.values())
                {
                    prjid = getPrjidByPrjName(afib);
                    actid = getActIdByPrjid(prjid, afib);
                    countAct = getCountExistsAct(prjid, afib);
                    countWeight = getCountExistsWeight( actid);
                    if(countAct<=0){
                        messages="导入失败,作业名称在系统中不存在,导入失败作业名称为： "+afib.getActName();
                        return messages;
                    }
                    _log.info("优先级表中存在："+countWeight+" 数据");
                    if(countWeight>0){
                        ps2.setLong(1, Long.valueOf(afib.getWeight()));
                        ps2.setLong(2, actid);
                        ps2.executeUpdate();
                    }else{
                        ps1.setLong(1, actid);
                        ps1.setLong(2, Long.valueOf(afib.getWeight()));
                        ps1.executeUpdate();
                    }
                }
                messages="导入成功";
           }
            
        }catch(SQLException e)
        {
            _log.error("saveWeight EXEC  IEAI_ACTINFO_PRI IS ERR"
                    + e.getMessage());
            messages="导入失败";
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }catch (NumberFormatException e)
        {
            _log.error("saveWeight EXEC  IEAI_ACTINFO_PRI IS ERR"
                    + e.getMessage());
            messages="优先级不能为空";
        }catch (RepositoryException e)
        {
            try
            {
                messages="导入失败";
                DBResource.throwRepositoryException(e, ServerError.ERR_DB_QUERY);
            } catch (RepositoryException e1)
            {     
                messages="导入失败";
                _log.error("saveWeight has error RepositoryException: "+e.getMessage());
                throw new ServerException(ServerError.ERR_DB_QUERY);
            }
        }finally
        {     
            DBResource.closePreparedStatement(ps1, method, _log);
            DBResource.closePreparedStatement(ps2, method, _log);
        }
        return messages;
    }
    
    /**
     * 
     * <li>Description:获取活动的数量</li> 
     * <AUTHOR>
     * 2019年1月6日 
     * @param conn
     * @param afib
     * @return
     * @throws RepositoryException
     * return long
     */
    public long getCountExistsAct(long prjid,ActWeightInfoBean afib) throws RepositoryException{
        String sql = "select count(IACTNAME) AS COUACTNAME from ieai_actinfo where iprjid=? and iactname = ? ";
        String method = "getCountExistsAct";
        long count = -1;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try{
            conn = DBResource.getConnection(method,_log,Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, prjid);
            ps.setString(2, afib.getActName());
            rs = ps.executeQuery(); 
            
            while(rs.next()){
                count = rs.getLong("COUACTNAME");
            }
            
        }catch (RepositoryException ex){
            DBResource.throwRepositoryException(ex, ServerError.ERR_DB_QUERY);
        }catch(Exception e){
            _log.error("getCountExistsAct is error ! "+e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, ps, method, _log);
            DBResource.closeConnection(conn, method, _log);
        }
        return count;
    }
    
    
    /**
     * 
     * <li>Description:获取优先级的数量</li> 
     * <AUTHOR>
     * 2019年1月6日 
     * @param conn
     * @param afib
     * @return
     * @throws RepositoryException
     * return long
     */
    public long getCountExistsWeight(long actid) throws RepositoryException{
        String sql = "select count(IACTID) AS COUACTID from IEAI_ACTINFO_PRIWEIGHT where IACTID=? ";
        String method = "getCountExistsWeight";
        long count = -1;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try{
            conn = DBResource.getConnection(method,_log,Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, actid);
            rs = ps.executeQuery(); 
            
            while(rs.next()){
                count = rs.getLong("COUACTID");
            }
            
        }catch (RepositoryException ex){
            DBResource.throwRepositoryException(ex, ServerError.ERR_DB_QUERY);
        }catch(Exception e){
            _log.error("getCountExistsWeight is error ! "+e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, ps, method, _log);
            DBResource.closeConnection(conn, method, _log);
        }
        return count;
    }
    
    
    /**
     * 
     * <li>Description:根据excel中工程名称获取prijd</li> 
     * <AUTHOR>
     * 2019年1月6日 
     * @param afib
     * @return
     * @throws RepositoryException
     * return long
     */
    public long getPrjidByPrjName(ActWeightInfoBean afib) throws RepositoryException{
        String sql = "select max(IID) AS MAXIID from ieai_project where iname = ? ";
        String method = "getPrjidByPrjName";
        long prjid = -1;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try{
            conn = DBResource.getConnection(method,_log,Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setString(1, afib.getPrjName());
            rs = ps.executeQuery(); 
            
            while(rs.next()){
                prjid = rs.getLong("MAXIID");
            }
            
        }catch (RepositoryException ex){
            DBResource.throwRepositoryException(ex, ServerError.ERR_DB_QUERY);
        }catch(Exception e){
            _log.error("getPrjidByPrjName is error ! "+e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, ps, method, _log);
            DBResource.closeConnection(conn, method, _log);
        }
        return prjid;
    }

    /**
     * 
     * <li>Description:根据excel中活动名、工作流名称和查询得到最新的prjid获取actid</li> 
     * <AUTHOR>
     * 2019年1月6日 
     * @param prjid
     * @param afib
     * @return
     * @throws RepositoryException
     * return long
     */
    public long getActIdByPrjid(long prjid,ActWeightInfoBean afib) throws RepositoryException{
        String sql = "select IID from ieai_actinfo where iprjid=? and iactname = ? and iflowname = ? ";
        String method = "getActIdByPrjid";
        long actId = -1;
        PreparedStatement ps = null;
        ResultSet rs = null;
        Connection conn = null;
        try{
            conn = DBResource.getConnection(method,_log,Constants.IEAI_SUS);
            ps = conn.prepareStatement(sql);
            ps.setLong(1, prjid);
            ps.setString(2, afib.getActName());
            ps.setString(3, afib.getWorkFlowName());
            rs = ps.executeQuery(); 
            
            while(rs.next()){
                actId = rs.getLong("IID");
            }
            
        }catch (RepositoryException ex){
            DBResource.throwRepositoryException(ex, ServerError.ERR_DB_QUERY);
        }catch(Exception e){
            _log.error("getActIdByPrjid is error ! "+e.getMessage());
            throw new RepositoryException(ServerError.ERR_DB_QUERY);
        }finally{
            DBResource.closePSRS(rs, ps, method, _log);
            DBResource.closeConnection(conn, method, _log);
        }
        return actId;
    }
    
}
